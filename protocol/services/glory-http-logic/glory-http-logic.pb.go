// Code generated by protoc-gen-go. DO NOT EDIT.
// source: glory-http-logic/glory-http-logic.proto

package glory_http_logic // import "golang.52tt.com/protocol/services/glory-http-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import glory_reward "golang.52tt.com/protocol/services/glory-reward"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type LiveStatus int32

const (
	LiveStatus_NotInLiving LiveStatus = 0
	LiveStatus_InLiving    LiveStatus = 1
	LiveStatus_InPk        LiveStatus = 2
	LiveStatus_WatchLiving LiveStatus = 3
)

var LiveStatus_name = map[int32]string{
	0: "NotInLiving",
	1: "InLiving",
	2: "InPk",
	3: "WatchLiving",
}
var LiveStatus_value = map[string]int32{
	"NotInLiving": 0,
	"InLiving":    1,
	"InPk":        2,
	"WatchLiving": 3,
}

func (x LiveStatus) String() string {
	return proto.EnumName(LiveStatus_name, int32(x))
}
func (LiveStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{0}
}

// 名流用户
type CelebrityUserProfile struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname"`
	Sex                  int32    `protobuf:"varint,4,opt,name=sex,proto3" json:"sex"`
	UkwAccount           string   `protobuf:"bytes,5,opt,name=ukw_account,json=ukwAccount,proto3" json:"ukw_account"`
	UkwNickname          string   `protobuf:"bytes,6,opt,name=ukw_nickname,json=ukwNickname,proto3" json:"ukw_nickname"`
	ChannelId            uint32   `protobuf:"varint,7,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	LiveStatus           uint32   `protobuf:"varint,8,opt,name=live_status,json=liveStatus,proto3" json:"live_status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CelebrityUserProfile) Reset()         { *m = CelebrityUserProfile{} }
func (m *CelebrityUserProfile) String() string { return proto.CompactTextString(m) }
func (*CelebrityUserProfile) ProtoMessage()    {}
func (*CelebrityUserProfile) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{0}
}
func (m *CelebrityUserProfile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CelebrityUserProfile.Unmarshal(m, b)
}
func (m *CelebrityUserProfile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CelebrityUserProfile.Marshal(b, m, deterministic)
}
func (dst *CelebrityUserProfile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CelebrityUserProfile.Merge(dst, src)
}
func (m *CelebrityUserProfile) XXX_Size() int {
	return xxx_messageInfo_CelebrityUserProfile.Size(m)
}
func (m *CelebrityUserProfile) XXX_DiscardUnknown() {
	xxx_messageInfo_CelebrityUserProfile.DiscardUnknown(m)
}

var xxx_messageInfo_CelebrityUserProfile proto.InternalMessageInfo

func (m *CelebrityUserProfile) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CelebrityUserProfile) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *CelebrityUserProfile) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *CelebrityUserProfile) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *CelebrityUserProfile) GetUkwAccount() string {
	if m != nil {
		return m.UkwAccount
	}
	return ""
}

func (m *CelebrityUserProfile) GetUkwNickname() string {
	if m != nil {
		return m.UkwNickname
	}
	return ""
}

func (m *CelebrityUserProfile) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CelebrityUserProfile) GetLiveStatus() uint32 {
	if m != nil {
		return m.LiveStatus
	}
	return 0
}

// 名流周榜用户信息
type CelebrityRankInfo struct {
	User                 *CelebrityUserProfile `protobuf:"bytes,1,opt,name=user,proto3" json:"user"`
	Score                uint32                `protobuf:"varint,2,opt,name=score,proto3" json:"score"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *CelebrityRankInfo) Reset()         { *m = CelebrityRankInfo{} }
func (m *CelebrityRankInfo) String() string { return proto.CompactTextString(m) }
func (*CelebrityRankInfo) ProtoMessage()    {}
func (*CelebrityRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{1}
}
func (m *CelebrityRankInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CelebrityRankInfo.Unmarshal(m, b)
}
func (m *CelebrityRankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CelebrityRankInfo.Marshal(b, m, deterministic)
}
func (dst *CelebrityRankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CelebrityRankInfo.Merge(dst, src)
}
func (m *CelebrityRankInfo) XXX_Size() int {
	return xxx_messageInfo_CelebrityRankInfo.Size(m)
}
func (m *CelebrityRankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CelebrityRankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CelebrityRankInfo proto.InternalMessageInfo

func (m *CelebrityRankInfo) GetUser() *CelebrityUserProfile {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *CelebrityRankInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

// 获取名流周榜信息-请求
type GetCelebrityWeekRankReq struct {
	IsCurWeek            bool     `protobuf:"varint,1,opt,name=is_cur_week,json=isCurWeek,proto3" json:"is_cur_week"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCelebrityWeekRankReq) Reset()         { *m = GetCelebrityWeekRankReq{} }
func (m *GetCelebrityWeekRankReq) String() string { return proto.CompactTextString(m) }
func (*GetCelebrityWeekRankReq) ProtoMessage()    {}
func (*GetCelebrityWeekRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{2}
}
func (m *GetCelebrityWeekRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCelebrityWeekRankReq.Unmarshal(m, b)
}
func (m *GetCelebrityWeekRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCelebrityWeekRankReq.Marshal(b, m, deterministic)
}
func (dst *GetCelebrityWeekRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCelebrityWeekRankReq.Merge(dst, src)
}
func (m *GetCelebrityWeekRankReq) XXX_Size() int {
	return xxx_messageInfo_GetCelebrityWeekRankReq.Size(m)
}
func (m *GetCelebrityWeekRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCelebrityWeekRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCelebrityWeekRankReq proto.InternalMessageInfo

func (m *GetCelebrityWeekRankReq) GetIsCurWeek() bool {
	if m != nil {
		return m.IsCurWeek
	}
	return false
}

type GetCelebrityWeekRankRsp struct {
	RankList             []*CelebrityRankInfo  `protobuf:"bytes,1,rep,name=rank_list,json=rankList,proto3" json:"rank_list"`
	MyScore              uint32                `protobuf:"varint,2,opt,name=my_score,json=myScore,proto3" json:"my_score"`
	MyInfo               *CelebrityUserProfile `protobuf:"bytes,3,opt,name=my_info,json=myInfo,proto3" json:"my_info"`
	IsFilter             bool                  `protobuf:"varint,4,opt,name=is_filter,json=isFilter,proto3" json:"is_filter"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetCelebrityWeekRankRsp) Reset()         { *m = GetCelebrityWeekRankRsp{} }
func (m *GetCelebrityWeekRankRsp) String() string { return proto.CompactTextString(m) }
func (*GetCelebrityWeekRankRsp) ProtoMessage()    {}
func (*GetCelebrityWeekRankRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{3}
}
func (m *GetCelebrityWeekRankRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCelebrityWeekRankRsp.Unmarshal(m, b)
}
func (m *GetCelebrityWeekRankRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCelebrityWeekRankRsp.Marshal(b, m, deterministic)
}
func (dst *GetCelebrityWeekRankRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCelebrityWeekRankRsp.Merge(dst, src)
}
func (m *GetCelebrityWeekRankRsp) XXX_Size() int {
	return xxx_messageInfo_GetCelebrityWeekRankRsp.Size(m)
}
func (m *GetCelebrityWeekRankRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCelebrityWeekRankRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCelebrityWeekRankRsp proto.InternalMessageInfo

func (m *GetCelebrityWeekRankRsp) GetRankList() []*CelebrityRankInfo {
	if m != nil {
		return m.RankList
	}
	return nil
}

func (m *GetCelebrityWeekRankRsp) GetMyScore() uint32 {
	if m != nil {
		return m.MyScore
	}
	return 0
}

func (m *GetCelebrityWeekRankRsp) GetMyInfo() *CelebrityUserProfile {
	if m != nil {
		return m.MyInfo
	}
	return nil
}

func (m *GetCelebrityWeekRankRsp) GetIsFilter() bool {
	if m != nil {
		return m.IsFilter
	}
	return false
}

// 名流殿堂-信息
type CelebrityPalaceInfo struct {
	User                 *CelebrityUserProfile `protobuf:"bytes,1,opt,name=user,proto3" json:"user"`
	CelebrityId          uint32                `protobuf:"varint,2,opt,name=celebrity_id,json=celebrityId,proto3" json:"celebrity_id"`
	Rank                 uint32                `protobuf:"varint,3,opt,name=rank,proto3" json:"rank"`
	Ts                   uint32                `protobuf:"varint,4,opt,name=ts,proto3" json:"ts"`
	Sum                  uint32                `protobuf:"varint,5,opt,name=sum,proto3" json:"sum"`
	LastTs               uint32                `protobuf:"varint,6,opt,name=last_ts,json=lastTs,proto3" json:"last_ts"`
	LastNum              uint32                `protobuf:"varint,7,opt,name=last_num,json=lastNum,proto3" json:"last_num"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *CelebrityPalaceInfo) Reset()         { *m = CelebrityPalaceInfo{} }
func (m *CelebrityPalaceInfo) String() string { return proto.CompactTextString(m) }
func (*CelebrityPalaceInfo) ProtoMessage()    {}
func (*CelebrityPalaceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{4}
}
func (m *CelebrityPalaceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CelebrityPalaceInfo.Unmarshal(m, b)
}
func (m *CelebrityPalaceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CelebrityPalaceInfo.Marshal(b, m, deterministic)
}
func (dst *CelebrityPalaceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CelebrityPalaceInfo.Merge(dst, src)
}
func (m *CelebrityPalaceInfo) XXX_Size() int {
	return xxx_messageInfo_CelebrityPalaceInfo.Size(m)
}
func (m *CelebrityPalaceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CelebrityPalaceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CelebrityPalaceInfo proto.InternalMessageInfo

func (m *CelebrityPalaceInfo) GetUser() *CelebrityUserProfile {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *CelebrityPalaceInfo) GetCelebrityId() uint32 {
	if m != nil {
		return m.CelebrityId
	}
	return 0
}

func (m *CelebrityPalaceInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *CelebrityPalaceInfo) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *CelebrityPalaceInfo) GetSum() uint32 {
	if m != nil {
		return m.Sum
	}
	return 0
}

func (m *CelebrityPalaceInfo) GetLastTs() uint32 {
	if m != nil {
		return m.LastTs
	}
	return 0
}

func (m *CelebrityPalaceInfo) GetLastNum() uint32 {
	if m != nil {
		return m.LastNum
	}
	return 0
}

type GiftInfo struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id"`
	GiftName             string   `protobuf:"bytes,2,opt,name=gift_name,json=giftName,proto3" json:"gift_name"`
	GiftIconUrl          string   `protobuf:"bytes,3,opt,name=gift_icon_url,json=giftIconUrl,proto3" json:"gift_icon_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiftInfo) Reset()         { *m = GiftInfo{} }
func (m *GiftInfo) String() string { return proto.CompactTextString(m) }
func (*GiftInfo) ProtoMessage()    {}
func (*GiftInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{5}
}
func (m *GiftInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiftInfo.Unmarshal(m, b)
}
func (m *GiftInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiftInfo.Marshal(b, m, deterministic)
}
func (dst *GiftInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiftInfo.Merge(dst, src)
}
func (m *GiftInfo) XXX_Size() int {
	return xxx_messageInfo_GiftInfo.Size(m)
}
func (m *GiftInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GiftInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GiftInfo proto.InternalMessageInfo

func (m *GiftInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *GiftInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *GiftInfo) GetGiftIconUrl() string {
	if m != nil {
		return m.GiftIconUrl
	}
	return ""
}

// 获取名流殿堂顶部信息-请求
type GetCelebrityPalaceTopInfoReq struct {
	IsCurPeriods         bool     `protobuf:"varint,1,opt,name=is_cur_periods,json=isCurPeriods,proto3" json:"is_cur_periods"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCelebrityPalaceTopInfoReq) Reset()         { *m = GetCelebrityPalaceTopInfoReq{} }
func (m *GetCelebrityPalaceTopInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetCelebrityPalaceTopInfoReq) ProtoMessage()    {}
func (*GetCelebrityPalaceTopInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{6}
}
func (m *GetCelebrityPalaceTopInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCelebrityPalaceTopInfoReq.Unmarshal(m, b)
}
func (m *GetCelebrityPalaceTopInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCelebrityPalaceTopInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetCelebrityPalaceTopInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCelebrityPalaceTopInfoReq.Merge(dst, src)
}
func (m *GetCelebrityPalaceTopInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetCelebrityPalaceTopInfoReq.Size(m)
}
func (m *GetCelebrityPalaceTopInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCelebrityPalaceTopInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCelebrityPalaceTopInfoReq proto.InternalMessageInfo

func (m *GetCelebrityPalaceTopInfoReq) GetIsCurPeriods() bool {
	if m != nil {
		return m.IsCurPeriods
	}
	return false
}

type GetCelebrityPalaceTopInfoRsp struct {
	NewInfo              *CelebrityPalaceInfo `protobuf:"bytes,1,opt,name=new_info,json=newInfo,proto3" json:"new_info"`
	BestInfo             *CelebrityPalaceInfo `protobuf:"bytes,2,opt,name=best_info,json=bestInfo,proto3" json:"best_info"`
	CurrGift             *GiftInfo            `protobuf:"bytes,3,opt,name=curr_gift,json=currGift,proto3" json:"curr_gift"`
	SelfInfo             *CelebrityPalaceInfo `protobuf:"bytes,4,opt,name=self_info,json=selfInfo,proto3" json:"self_info"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetCelebrityPalaceTopInfoRsp) Reset()         { *m = GetCelebrityPalaceTopInfoRsp{} }
func (m *GetCelebrityPalaceTopInfoRsp) String() string { return proto.CompactTextString(m) }
func (*GetCelebrityPalaceTopInfoRsp) ProtoMessage()    {}
func (*GetCelebrityPalaceTopInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{7}
}
func (m *GetCelebrityPalaceTopInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCelebrityPalaceTopInfoRsp.Unmarshal(m, b)
}
func (m *GetCelebrityPalaceTopInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCelebrityPalaceTopInfoRsp.Marshal(b, m, deterministic)
}
func (dst *GetCelebrityPalaceTopInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCelebrityPalaceTopInfoRsp.Merge(dst, src)
}
func (m *GetCelebrityPalaceTopInfoRsp) XXX_Size() int {
	return xxx_messageInfo_GetCelebrityPalaceTopInfoRsp.Size(m)
}
func (m *GetCelebrityPalaceTopInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCelebrityPalaceTopInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCelebrityPalaceTopInfoRsp proto.InternalMessageInfo

func (m *GetCelebrityPalaceTopInfoRsp) GetNewInfo() *CelebrityPalaceInfo {
	if m != nil {
		return m.NewInfo
	}
	return nil
}

func (m *GetCelebrityPalaceTopInfoRsp) GetBestInfo() *CelebrityPalaceInfo {
	if m != nil {
		return m.BestInfo
	}
	return nil
}

func (m *GetCelebrityPalaceTopInfoRsp) GetCurrGift() *GiftInfo {
	if m != nil {
		return m.CurrGift
	}
	return nil
}

func (m *GetCelebrityPalaceTopInfoRsp) GetSelfInfo() *CelebrityPalaceInfo {
	if m != nil {
		return m.SelfInfo
	}
	return nil
}

// //获取名流殿堂分页列表-请求
type GetCelebrityPalaceInfoListReq struct {
	IsCurPeriods         bool     `protobuf:"varint,1,opt,name=is_cur_periods,json=isCurPeriods,proto3" json:"is_cur_periods"`
	LastCelebrityId      uint32   `protobuf:"varint,2,opt,name=last_celebrity_id,json=lastCelebrityId,proto3" json:"last_celebrity_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCelebrityPalaceInfoListReq) Reset()         { *m = GetCelebrityPalaceInfoListReq{} }
func (m *GetCelebrityPalaceInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetCelebrityPalaceInfoListReq) ProtoMessage()    {}
func (*GetCelebrityPalaceInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{8}
}
func (m *GetCelebrityPalaceInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCelebrityPalaceInfoListReq.Unmarshal(m, b)
}
func (m *GetCelebrityPalaceInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCelebrityPalaceInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetCelebrityPalaceInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCelebrityPalaceInfoListReq.Merge(dst, src)
}
func (m *GetCelebrityPalaceInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetCelebrityPalaceInfoListReq.Size(m)
}
func (m *GetCelebrityPalaceInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCelebrityPalaceInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCelebrityPalaceInfoListReq proto.InternalMessageInfo

func (m *GetCelebrityPalaceInfoListReq) GetIsCurPeriods() bool {
	if m != nil {
		return m.IsCurPeriods
	}
	return false
}

func (m *GetCelebrityPalaceInfoListReq) GetLastCelebrityId() uint32 {
	if m != nil {
		return m.LastCelebrityId
	}
	return 0
}

type GetCelebrityPalaceInfoListRsp struct {
	InfoList             []*CelebrityPalaceInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list"`
	IsAllEnd             bool                   `protobuf:"varint,2,opt,name=is_all_end,json=isAllEnd,proto3" json:"is_all_end"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetCelebrityPalaceInfoListRsp) Reset()         { *m = GetCelebrityPalaceInfoListRsp{} }
func (m *GetCelebrityPalaceInfoListRsp) String() string { return proto.CompactTextString(m) }
func (*GetCelebrityPalaceInfoListRsp) ProtoMessage()    {}
func (*GetCelebrityPalaceInfoListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{9}
}
func (m *GetCelebrityPalaceInfoListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCelebrityPalaceInfoListRsp.Unmarshal(m, b)
}
func (m *GetCelebrityPalaceInfoListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCelebrityPalaceInfoListRsp.Marshal(b, m, deterministic)
}
func (dst *GetCelebrityPalaceInfoListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCelebrityPalaceInfoListRsp.Merge(dst, src)
}
func (m *GetCelebrityPalaceInfoListRsp) XXX_Size() int {
	return xxx_messageInfo_GetCelebrityPalaceInfoListRsp.Size(m)
}
func (m *GetCelebrityPalaceInfoListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCelebrityPalaceInfoListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCelebrityPalaceInfoListRsp proto.InternalMessageInfo

func (m *GetCelebrityPalaceInfoListRsp) GetInfoList() []*CelebrityPalaceInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetCelebrityPalaceInfoListRsp) GetIsAllEnd() bool {
	if m != nil {
		return m.IsAllEnd
	}
	return false
}

// 名流殿堂-回顾-请求
type ReplayCelebrityPalaceReq struct {
	CelebrityId          uint32   `protobuf:"varint,1,opt,name=celebrity_id,json=celebrityId,proto3" json:"celebrity_id"`
	IsCurPeriods         bool     `protobuf:"varint,2,opt,name=is_cur_periods,json=isCurPeriods,proto3" json:"is_cur_periods"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReplayCelebrityPalaceReq) Reset()         { *m = ReplayCelebrityPalaceReq{} }
func (m *ReplayCelebrityPalaceReq) String() string { return proto.CompactTextString(m) }
func (*ReplayCelebrityPalaceReq) ProtoMessage()    {}
func (*ReplayCelebrityPalaceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{10}
}
func (m *ReplayCelebrityPalaceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReplayCelebrityPalaceReq.Unmarshal(m, b)
}
func (m *ReplayCelebrityPalaceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReplayCelebrityPalaceReq.Marshal(b, m, deterministic)
}
func (dst *ReplayCelebrityPalaceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReplayCelebrityPalaceReq.Merge(dst, src)
}
func (m *ReplayCelebrityPalaceReq) XXX_Size() int {
	return xxx_messageInfo_ReplayCelebrityPalaceReq.Size(m)
}
func (m *ReplayCelebrityPalaceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReplayCelebrityPalaceReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReplayCelebrityPalaceReq proto.InternalMessageInfo

func (m *ReplayCelebrityPalaceReq) GetCelebrityId() uint32 {
	if m != nil {
		return m.CelebrityId
	}
	return 0
}

func (m *ReplayCelebrityPalaceReq) GetIsCurPeriods() bool {
	if m != nil {
		return m.IsCurPeriods
	}
	return false
}

type ReplayCelebrityPalaceRsp struct {
	SendUser             *CelebrityUserProfile `protobuf:"bytes,1,opt,name=send_user,json=sendUser,proto3" json:"send_user"`
	ToUser               *CelebrityUserProfile `protobuf:"bytes,2,opt,name=to_user,json=toUser,proto3" json:"to_user"`
	Gift                 *GiftInfo             `protobuf:"bytes,3,opt,name=gift,proto3" json:"gift"`
	Ts                   uint32                `protobuf:"varint,4,opt,name=ts,proto3" json:"ts"`
	GiftNum              uint32                `protobuf:"varint,5,opt,name=gift_num,json=giftNum,proto3" json:"gift_num"`
	ShowCid              string                `protobuf:"bytes,6,opt,name=show_cid,json=showCid,proto3" json:"show_cid"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ReplayCelebrityPalaceRsp) Reset()         { *m = ReplayCelebrityPalaceRsp{} }
func (m *ReplayCelebrityPalaceRsp) String() string { return proto.CompactTextString(m) }
func (*ReplayCelebrityPalaceRsp) ProtoMessage()    {}
func (*ReplayCelebrityPalaceRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{11}
}
func (m *ReplayCelebrityPalaceRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReplayCelebrityPalaceRsp.Unmarshal(m, b)
}
func (m *ReplayCelebrityPalaceRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReplayCelebrityPalaceRsp.Marshal(b, m, deterministic)
}
func (dst *ReplayCelebrityPalaceRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReplayCelebrityPalaceRsp.Merge(dst, src)
}
func (m *ReplayCelebrityPalaceRsp) XXX_Size() int {
	return xxx_messageInfo_ReplayCelebrityPalaceRsp.Size(m)
}
func (m *ReplayCelebrityPalaceRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReplayCelebrityPalaceRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ReplayCelebrityPalaceRsp proto.InternalMessageInfo

func (m *ReplayCelebrityPalaceRsp) GetSendUser() *CelebrityUserProfile {
	if m != nil {
		return m.SendUser
	}
	return nil
}

func (m *ReplayCelebrityPalaceRsp) GetToUser() *CelebrityUserProfile {
	if m != nil {
		return m.ToUser
	}
	return nil
}

func (m *ReplayCelebrityPalaceRsp) GetGift() *GiftInfo {
	if m != nil {
		return m.Gift
	}
	return nil
}

func (m *ReplayCelebrityPalaceRsp) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *ReplayCelebrityPalaceRsp) GetGiftNum() uint32 {
	if m != nil {
		return m.GiftNum
	}
	return 0
}

func (m *ReplayCelebrityPalaceRsp) GetShowCid() string {
	if m != nil {
		return m.ShowCid
	}
	return ""
}

// -------------------- 积分获取协议定义 start ----------------------------------
// IsNeedNoticeReq 是否需要红点提醒
type IsNeedNoticeReq struct {
	Type                 glory_reward.NoticeType `protobuf:"varint,1,opt,name=type,proto3,enum=glory_reward.NoticeType" json:"type"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *IsNeedNoticeReq) Reset()         { *m = IsNeedNoticeReq{} }
func (m *IsNeedNoticeReq) String() string { return proto.CompactTextString(m) }
func (*IsNeedNoticeReq) ProtoMessage()    {}
func (*IsNeedNoticeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{12}
}
func (m *IsNeedNoticeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsNeedNoticeReq.Unmarshal(m, b)
}
func (m *IsNeedNoticeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsNeedNoticeReq.Marshal(b, m, deterministic)
}
func (dst *IsNeedNoticeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsNeedNoticeReq.Merge(dst, src)
}
func (m *IsNeedNoticeReq) XXX_Size() int {
	return xxx_messageInfo_IsNeedNoticeReq.Size(m)
}
func (m *IsNeedNoticeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsNeedNoticeReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsNeedNoticeReq proto.InternalMessageInfo

func (m *IsNeedNoticeReq) GetType() glory_reward.NoticeType {
	if m != nil {
		return m.Type
	}
	return glory_reward.NoticeType_NoticeTypeInvalid
}

type IsNeedNoticeResp struct {
	IsNotice             bool     `protobuf:"varint,1,opt,name=is_notice,json=isNotice,proto3" json:"is_notice"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsNeedNoticeResp) Reset()         { *m = IsNeedNoticeResp{} }
func (m *IsNeedNoticeResp) String() string { return proto.CompactTextString(m) }
func (*IsNeedNoticeResp) ProtoMessage()    {}
func (*IsNeedNoticeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{13}
}
func (m *IsNeedNoticeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsNeedNoticeResp.Unmarshal(m, b)
}
func (m *IsNeedNoticeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsNeedNoticeResp.Marshal(b, m, deterministic)
}
func (dst *IsNeedNoticeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsNeedNoticeResp.Merge(dst, src)
}
func (m *IsNeedNoticeResp) XXX_Size() int {
	return xxx_messageInfo_IsNeedNoticeResp.Size(m)
}
func (m *IsNeedNoticeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsNeedNoticeResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsNeedNoticeResp proto.InternalMessageInfo

func (m *IsNeedNoticeResp) GetIsNotice() bool {
	if m != nil {
		return m.IsNotice
	}
	return false
}

// ReceiveRewardReq 领取任务奖励
type ReceiveRewardReq struct {
	TaskId               []uint32 `protobuf:"varint,1,rep,packed,name=task_id,json=taskId,proto3" json:"task_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReceiveRewardReq) Reset()         { *m = ReceiveRewardReq{} }
func (m *ReceiveRewardReq) String() string { return proto.CompactTextString(m) }
func (*ReceiveRewardReq) ProtoMessage()    {}
func (*ReceiveRewardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{14}
}
func (m *ReceiveRewardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveRewardReq.Unmarshal(m, b)
}
func (m *ReceiveRewardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveRewardReq.Marshal(b, m, deterministic)
}
func (dst *ReceiveRewardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveRewardReq.Merge(dst, src)
}
func (m *ReceiveRewardReq) XXX_Size() int {
	return xxx_messageInfo_ReceiveRewardReq.Size(m)
}
func (m *ReceiveRewardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveRewardReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveRewardReq proto.InternalMessageInfo

func (m *ReceiveRewardReq) GetTaskId() []uint32 {
	if m != nil {
		return m.TaskId
	}
	return nil
}

type ReceiveRewardResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReceiveRewardResp) Reset()         { *m = ReceiveRewardResp{} }
func (m *ReceiveRewardResp) String() string { return proto.CompactTextString(m) }
func (*ReceiveRewardResp) ProtoMessage()    {}
func (*ReceiveRewardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{15}
}
func (m *ReceiveRewardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveRewardResp.Unmarshal(m, b)
}
func (m *ReceiveRewardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveRewardResp.Marshal(b, m, deterministic)
}
func (dst *ReceiveRewardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveRewardResp.Merge(dst, src)
}
func (m *ReceiveRewardResp) XXX_Size() int {
	return xxx_messageInfo_ReceiveRewardResp.Size(m)
}
func (m *ReceiveRewardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveRewardResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveRewardResp proto.InternalMessageInfo

type GetRewardFragmentInfoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRewardFragmentInfoReq) Reset()         { *m = GetRewardFragmentInfoReq{} }
func (m *GetRewardFragmentInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetRewardFragmentInfoReq) ProtoMessage()    {}
func (*GetRewardFragmentInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{16}
}
func (m *GetRewardFragmentInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRewardFragmentInfoReq.Unmarshal(m, b)
}
func (m *GetRewardFragmentInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRewardFragmentInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetRewardFragmentInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRewardFragmentInfoReq.Merge(dst, src)
}
func (m *GetRewardFragmentInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetRewardFragmentInfoReq.Size(m)
}
func (m *GetRewardFragmentInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRewardFragmentInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRewardFragmentInfoReq proto.InternalMessageInfo

type GetRewardFragmentInfoResp struct {
	FragFame             uint32   `protobuf:"varint,1,opt,name=frag_fame,json=fragFame,proto3" json:"frag_fame"`
	FragGlory            uint32   `protobuf:"varint,2,opt,name=frag_glory,json=fragGlory,proto3" json:"frag_glory"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRewardFragmentInfoResp) Reset()         { *m = GetRewardFragmentInfoResp{} }
func (m *GetRewardFragmentInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetRewardFragmentInfoResp) ProtoMessage()    {}
func (*GetRewardFragmentInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{17}
}
func (m *GetRewardFragmentInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRewardFragmentInfoResp.Unmarshal(m, b)
}
func (m *GetRewardFragmentInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRewardFragmentInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetRewardFragmentInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRewardFragmentInfoResp.Merge(dst, src)
}
func (m *GetRewardFragmentInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetRewardFragmentInfoResp.Size(m)
}
func (m *GetRewardFragmentInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRewardFragmentInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRewardFragmentInfoResp proto.InternalMessageInfo

func (m *GetRewardFragmentInfoResp) GetFragFame() uint32 {
	if m != nil {
		return m.FragFame
	}
	return 0
}

func (m *GetRewardFragmentInfoResp) GetFragGlory() uint32 {
	if m != nil {
		return m.FragGlory
	}
	return 0
}

func (m *GetRewardFragmentInfoResp) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GetRewardFragmentInfoResp) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

// ChallengeTaskInfo 挑战任务信息
type ChallengeTaskInfo struct {
	TaskId               uint32                         `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	FragType             glory_reward.GloryFragmentType `protobuf:"varint,2,opt,name=frag_type,json=fragType,proto3,enum=glory_reward.GloryFragmentType" json:"frag_type"`
	RewardType           glory_reward.RewardType        `protobuf:"varint,3,opt,name=reward_type,json=rewardType,proto3,enum=glory_reward.RewardType" json:"reward_type"`
	TaskType             glory_reward.TaskType          `protobuf:"varint,4,opt,name=task_type,json=taskType,proto3,enum=glory_reward.TaskType" json:"task_type"`
	FinishNum            uint32                         `protobuf:"varint,5,opt,name=finish_num,json=finishNum,proto3" json:"finish_num"`
	NeedFinishNum        uint32                         `protobuf:"varint,6,opt,name=need_finish_num,json=needFinishNum,proto3" json:"need_finish_num"`
	IconUrl              string                         `protobuf:"bytes,7,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url"`
	GiftName             string                         `protobuf:"bytes,8,opt,name=gift_name,json=giftName,proto3" json:"gift_name"`
	GiftPrice            uint32                         `protobuf:"varint,9,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price"`
	RewardNum            uint32                         `protobuf:"varint,10,opt,name=reward_num,json=rewardNum,proto3" json:"reward_num"`
	IsThisWeekTask       bool                           `protobuf:"varint,11,opt,name=is_this_week_task,json=isThisWeekTask,proto3" json:"is_this_week_task"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *ChallengeTaskInfo) Reset()         { *m = ChallengeTaskInfo{} }
func (m *ChallengeTaskInfo) String() string { return proto.CompactTextString(m) }
func (*ChallengeTaskInfo) ProtoMessage()    {}
func (*ChallengeTaskInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{18}
}
func (m *ChallengeTaskInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChallengeTaskInfo.Unmarshal(m, b)
}
func (m *ChallengeTaskInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChallengeTaskInfo.Marshal(b, m, deterministic)
}
func (dst *ChallengeTaskInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChallengeTaskInfo.Merge(dst, src)
}
func (m *ChallengeTaskInfo) XXX_Size() int {
	return xxx_messageInfo_ChallengeTaskInfo.Size(m)
}
func (m *ChallengeTaskInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChallengeTaskInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChallengeTaskInfo proto.InternalMessageInfo

func (m *ChallengeTaskInfo) GetTaskId() uint32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *ChallengeTaskInfo) GetFragType() glory_reward.GloryFragmentType {
	if m != nil {
		return m.FragType
	}
	return glory_reward.GloryFragmentType_GloryFragmentTypeInvalid
}

func (m *ChallengeTaskInfo) GetRewardType() glory_reward.RewardType {
	if m != nil {
		return m.RewardType
	}
	return glory_reward.RewardType_RewardTypeInvalid
}

func (m *ChallengeTaskInfo) GetTaskType() glory_reward.TaskType {
	if m != nil {
		return m.TaskType
	}
	return glory_reward.TaskType_TaskTypeInvalid
}

func (m *ChallengeTaskInfo) GetFinishNum() uint32 {
	if m != nil {
		return m.FinishNum
	}
	return 0
}

func (m *ChallengeTaskInfo) GetNeedFinishNum() uint32 {
	if m != nil {
		return m.NeedFinishNum
	}
	return 0
}

func (m *ChallengeTaskInfo) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *ChallengeTaskInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *ChallengeTaskInfo) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *ChallengeTaskInfo) GetRewardNum() uint32 {
	if m != nil {
		return m.RewardNum
	}
	return 0
}

func (m *ChallengeTaskInfo) GetIsThisWeekTask() bool {
	if m != nil {
		return m.IsThisWeekTask
	}
	return false
}

// GetChallengeTaskDetailReq 获取所有挑战任务详情
type GetChallengeTaskDetailReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChallengeTaskDetailReq) Reset()         { *m = GetChallengeTaskDetailReq{} }
func (m *GetChallengeTaskDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetChallengeTaskDetailReq) ProtoMessage()    {}
func (*GetChallengeTaskDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{19}
}
func (m *GetChallengeTaskDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChallengeTaskDetailReq.Unmarshal(m, b)
}
func (m *GetChallengeTaskDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChallengeTaskDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetChallengeTaskDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChallengeTaskDetailReq.Merge(dst, src)
}
func (m *GetChallengeTaskDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetChallengeTaskDetailReq.Size(m)
}
func (m *GetChallengeTaskDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChallengeTaskDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChallengeTaskDetailReq proto.InternalMessageInfo

type GetChallengeTaskDetailResp struct {
	FinishTasks          []*ChallengeTaskInfo `protobuf:"bytes,1,rep,name=finish_tasks,json=finishTasks,proto3" json:"finish_tasks"`
	WeekTasks            []*ChallengeTaskInfo `protobuf:"bytes,2,rep,name=week_tasks,json=weekTasks,proto3" json:"week_tasks"`
	GiftTasks            []*ChallengeTaskInfo `protobuf:"bytes,3,rep,name=gift_tasks,json=giftTasks,proto3" json:"gift_tasks"`
	IsNotice             bool                 `protobuf:"varint,4,opt,name=is_notice,json=isNotice,proto3" json:"is_notice"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetChallengeTaskDetailResp) Reset()         { *m = GetChallengeTaskDetailResp{} }
func (m *GetChallengeTaskDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetChallengeTaskDetailResp) ProtoMessage()    {}
func (*GetChallengeTaskDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{20}
}
func (m *GetChallengeTaskDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChallengeTaskDetailResp.Unmarshal(m, b)
}
func (m *GetChallengeTaskDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChallengeTaskDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetChallengeTaskDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChallengeTaskDetailResp.Merge(dst, src)
}
func (m *GetChallengeTaskDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetChallengeTaskDetailResp.Size(m)
}
func (m *GetChallengeTaskDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChallengeTaskDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChallengeTaskDetailResp proto.InternalMessageInfo

func (m *GetChallengeTaskDetailResp) GetFinishTasks() []*ChallengeTaskInfo {
	if m != nil {
		return m.FinishTasks
	}
	return nil
}

func (m *GetChallengeTaskDetailResp) GetWeekTasks() []*ChallengeTaskInfo {
	if m != nil {
		return m.WeekTasks
	}
	return nil
}

func (m *GetChallengeTaskDetailResp) GetGiftTasks() []*ChallengeTaskInfo {
	if m != nil {
		return m.GiftTasks
	}
	return nil
}

func (m *GetChallengeTaskDetailResp) GetIsNotice() bool {
	if m != nil {
		return m.IsNotice
	}
	return false
}

// GetWeekTaskListReq 获取周任务信息
type GetWeekTaskListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeekTaskListReq) Reset()         { *m = GetWeekTaskListReq{} }
func (m *GetWeekTaskListReq) String() string { return proto.CompactTextString(m) }
func (*GetWeekTaskListReq) ProtoMessage()    {}
func (*GetWeekTaskListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{21}
}
func (m *GetWeekTaskListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeekTaskListReq.Unmarshal(m, b)
}
func (m *GetWeekTaskListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeekTaskListReq.Marshal(b, m, deterministic)
}
func (dst *GetWeekTaskListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeekTaskListReq.Merge(dst, src)
}
func (m *GetWeekTaskListReq) XXX_Size() int {
	return xxx_messageInfo_GetWeekTaskListReq.Size(m)
}
func (m *GetWeekTaskListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeekTaskListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeekTaskListReq proto.InternalMessageInfo

type GetWeekTaskListResp struct {
	TaskInfo             []*ChallengeTaskInfo `protobuf:"bytes,1,rep,name=task_info,json=taskInfo,proto3" json:"task_info"`
	GiftTasks            []*ChallengeTaskInfo `protobuf:"bytes,2,rep,name=gift_tasks,json=giftTasks,proto3" json:"gift_tasks"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetWeekTaskListResp) Reset()         { *m = GetWeekTaskListResp{} }
func (m *GetWeekTaskListResp) String() string { return proto.CompactTextString(m) }
func (*GetWeekTaskListResp) ProtoMessage()    {}
func (*GetWeekTaskListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{22}
}
func (m *GetWeekTaskListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeekTaskListResp.Unmarshal(m, b)
}
func (m *GetWeekTaskListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeekTaskListResp.Marshal(b, m, deterministic)
}
func (dst *GetWeekTaskListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeekTaskListResp.Merge(dst, src)
}
func (m *GetWeekTaskListResp) XXX_Size() int {
	return xxx_messageInfo_GetWeekTaskListResp.Size(m)
}
func (m *GetWeekTaskListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeekTaskListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeekTaskListResp proto.InternalMessageInfo

func (m *GetWeekTaskListResp) GetTaskInfo() []*ChallengeTaskInfo {
	if m != nil {
		return m.TaskInfo
	}
	return nil
}

func (m *GetWeekTaskListResp) GetGiftTasks() []*ChallengeTaskInfo {
	if m != nil {
		return m.GiftTasks
	}
	return nil
}

// TaskInfo 获取奖励的任务信息
type FragmentRewardInfo struct {
	FragType             glory_reward.GloryFragmentType `protobuf:"varint,1,opt,name=frag_type,json=fragType,proto3,enum=glory_reward.GloryFragmentType" json:"frag_type"`
	RewardType           glory_reward.RewardType        `protobuf:"varint,2,opt,name=reward_type,json=rewardType,proto3,enum=glory_reward.RewardType" json:"reward_type"`
	TaskType             glory_reward.TaskType          `protobuf:"varint,3,opt,name=task_type,json=taskType,proto3,enum=glory_reward.TaskType" json:"task_type"`
	Remark               string                         `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark"`
	MsgIconUrl           string                         `protobuf:"bytes,5,opt,name=msg_icon_url,json=msgIconUrl,proto3" json:"msg_icon_url"`
	GiftName             string                         `protobuf:"bytes,6,opt,name=gift_name,json=giftName,proto3" json:"gift_name"`
	Count                uint32                         `protobuf:"varint,7,opt,name=count,proto3" json:"count"`
	RewardTime           int64                          `protobuf:"varint,8,opt,name=reward_time,json=rewardTime,proto3" json:"reward_time"`
	ExpireTime           int64                          `protobuf:"varint,9,opt,name=expire_time,json=expireTime,proto3" json:"expire_time"`
	FragNum              uint32                         `protobuf:"varint,10,opt,name=frag_num,json=fragNum,proto3" json:"frag_num"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *FragmentRewardInfo) Reset()         { *m = FragmentRewardInfo{} }
func (m *FragmentRewardInfo) String() string { return proto.CompactTextString(m) }
func (*FragmentRewardInfo) ProtoMessage()    {}
func (*FragmentRewardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{23}
}
func (m *FragmentRewardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FragmentRewardInfo.Unmarshal(m, b)
}
func (m *FragmentRewardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FragmentRewardInfo.Marshal(b, m, deterministic)
}
func (dst *FragmentRewardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FragmentRewardInfo.Merge(dst, src)
}
func (m *FragmentRewardInfo) XXX_Size() int {
	return xxx_messageInfo_FragmentRewardInfo.Size(m)
}
func (m *FragmentRewardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FragmentRewardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FragmentRewardInfo proto.InternalMessageInfo

func (m *FragmentRewardInfo) GetFragType() glory_reward.GloryFragmentType {
	if m != nil {
		return m.FragType
	}
	return glory_reward.GloryFragmentType_GloryFragmentTypeInvalid
}

func (m *FragmentRewardInfo) GetRewardType() glory_reward.RewardType {
	if m != nil {
		return m.RewardType
	}
	return glory_reward.RewardType_RewardTypeInvalid
}

func (m *FragmentRewardInfo) GetTaskType() glory_reward.TaskType {
	if m != nil {
		return m.TaskType
	}
	return glory_reward.TaskType_TaskTypeInvalid
}

func (m *FragmentRewardInfo) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *FragmentRewardInfo) GetMsgIconUrl() string {
	if m != nil {
		return m.MsgIconUrl
	}
	return ""
}

func (m *FragmentRewardInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *FragmentRewardInfo) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *FragmentRewardInfo) GetRewardTime() int64 {
	if m != nil {
		return m.RewardTime
	}
	return 0
}

func (m *FragmentRewardInfo) GetExpireTime() int64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *FragmentRewardInfo) GetFragNum() uint32 {
	if m != nil {
		return m.FragNum
	}
	return 0
}

// GetRewardDetailReq 获取星钻获取明细信息
type GetRewardDetailReq struct {
	Offset               uint32                          `protobuf:"varint,1,opt,name=offset,proto3" json:"offset"`
	Limit                uint32                          `protobuf:"varint,2,opt,name=limit,proto3" json:"limit"`
	Type                 glory_reward.FragmentRewardType `protobuf:"varint,3,opt,name=type,proto3,enum=glory_reward.FragmentRewardType" json:"type"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetRewardDetailReq) Reset()         { *m = GetRewardDetailReq{} }
func (m *GetRewardDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetRewardDetailReq) ProtoMessage()    {}
func (*GetRewardDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{24}
}
func (m *GetRewardDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRewardDetailReq.Unmarshal(m, b)
}
func (m *GetRewardDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRewardDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetRewardDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRewardDetailReq.Merge(dst, src)
}
func (m *GetRewardDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetRewardDetailReq.Size(m)
}
func (m *GetRewardDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRewardDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRewardDetailReq proto.InternalMessageInfo

func (m *GetRewardDetailReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetRewardDetailReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetRewardDetailReq) GetType() glory_reward.FragmentRewardType {
	if m != nil {
		return m.Type
	}
	return glory_reward.FragmentRewardType_FragmentRewardTypeInvalid
}

type GetRewardDetailResp struct {
	Info                 []*FragmentRewardInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info"`
	Total                uint32                `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetRewardDetailResp) Reset()         { *m = GetRewardDetailResp{} }
func (m *GetRewardDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetRewardDetailResp) ProtoMessage()    {}
func (*GetRewardDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{25}
}
func (m *GetRewardDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRewardDetailResp.Unmarshal(m, b)
}
func (m *GetRewardDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRewardDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetRewardDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRewardDetailResp.Merge(dst, src)
}
func (m *GetRewardDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetRewardDetailResp.Size(m)
}
func (m *GetRewardDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRewardDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRewardDetailResp proto.InternalMessageInfo

func (m *GetRewardDetailResp) GetInfo() []*FragmentRewardInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetRewardDetailResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 获取入口状态
type GetEnterStatusReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEnterStatusReq) Reset()         { *m = GetEnterStatusReq{} }
func (m *GetEnterStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetEnterStatusReq) ProtoMessage()    {}
func (*GetEnterStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{26}
}
func (m *GetEnterStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEnterStatusReq.Unmarshal(m, b)
}
func (m *GetEnterStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEnterStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetEnterStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEnterStatusReq.Merge(dst, src)
}
func (m *GetEnterStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetEnterStatusReq.Size(m)
}
func (m *GetEnterStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEnterStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEnterStatusReq proto.InternalMessageInfo

type GetEnterStatusResp struct {
	IsOpen               bool     `protobuf:"varint,1,opt,name=is_open,json=isOpen,proto3" json:"is_open"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEnterStatusResp) Reset()         { *m = GetEnterStatusResp{} }
func (m *GetEnterStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetEnterStatusResp) ProtoMessage()    {}
func (*GetEnterStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_http_logic_f23a2fe907fa774d, []int{27}
}
func (m *GetEnterStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEnterStatusResp.Unmarshal(m, b)
}
func (m *GetEnterStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEnterStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetEnterStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEnterStatusResp.Merge(dst, src)
}
func (m *GetEnterStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetEnterStatusResp.Size(m)
}
func (m *GetEnterStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEnterStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEnterStatusResp proto.InternalMessageInfo

func (m *GetEnterStatusResp) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

func init() {
	proto.RegisterType((*CelebrityUserProfile)(nil), "glory_http_logic.CelebrityUserProfile")
	proto.RegisterType((*CelebrityRankInfo)(nil), "glory_http_logic.CelebrityRankInfo")
	proto.RegisterType((*GetCelebrityWeekRankReq)(nil), "glory_http_logic.GetCelebrityWeekRankReq")
	proto.RegisterType((*GetCelebrityWeekRankRsp)(nil), "glory_http_logic.GetCelebrityWeekRankRsp")
	proto.RegisterType((*CelebrityPalaceInfo)(nil), "glory_http_logic.CelebrityPalaceInfo")
	proto.RegisterType((*GiftInfo)(nil), "glory_http_logic.GiftInfo")
	proto.RegisterType((*GetCelebrityPalaceTopInfoReq)(nil), "glory_http_logic.GetCelebrityPalaceTopInfoReq")
	proto.RegisterType((*GetCelebrityPalaceTopInfoRsp)(nil), "glory_http_logic.GetCelebrityPalaceTopInfoRsp")
	proto.RegisterType((*GetCelebrityPalaceInfoListReq)(nil), "glory_http_logic.GetCelebrityPalaceInfoListReq")
	proto.RegisterType((*GetCelebrityPalaceInfoListRsp)(nil), "glory_http_logic.GetCelebrityPalaceInfoListRsp")
	proto.RegisterType((*ReplayCelebrityPalaceReq)(nil), "glory_http_logic.ReplayCelebrityPalaceReq")
	proto.RegisterType((*ReplayCelebrityPalaceRsp)(nil), "glory_http_logic.ReplayCelebrityPalaceRsp")
	proto.RegisterType((*IsNeedNoticeReq)(nil), "glory_http_logic.IsNeedNoticeReq")
	proto.RegisterType((*IsNeedNoticeResp)(nil), "glory_http_logic.IsNeedNoticeResp")
	proto.RegisterType((*ReceiveRewardReq)(nil), "glory_http_logic.ReceiveRewardReq")
	proto.RegisterType((*ReceiveRewardResp)(nil), "glory_http_logic.ReceiveRewardResp")
	proto.RegisterType((*GetRewardFragmentInfoReq)(nil), "glory_http_logic.GetRewardFragmentInfoReq")
	proto.RegisterType((*GetRewardFragmentInfoResp)(nil), "glory_http_logic.GetRewardFragmentInfoResp")
	proto.RegisterType((*ChallengeTaskInfo)(nil), "glory_http_logic.ChallengeTaskInfo")
	proto.RegisterType((*GetChallengeTaskDetailReq)(nil), "glory_http_logic.GetChallengeTaskDetailReq")
	proto.RegisterType((*GetChallengeTaskDetailResp)(nil), "glory_http_logic.GetChallengeTaskDetailResp")
	proto.RegisterType((*GetWeekTaskListReq)(nil), "glory_http_logic.GetWeekTaskListReq")
	proto.RegisterType((*GetWeekTaskListResp)(nil), "glory_http_logic.GetWeekTaskListResp")
	proto.RegisterType((*FragmentRewardInfo)(nil), "glory_http_logic.FragmentRewardInfo")
	proto.RegisterType((*GetRewardDetailReq)(nil), "glory_http_logic.GetRewardDetailReq")
	proto.RegisterType((*GetRewardDetailResp)(nil), "glory_http_logic.GetRewardDetailResp")
	proto.RegisterType((*GetEnterStatusReq)(nil), "glory_http_logic.GetEnterStatusReq")
	proto.RegisterType((*GetEnterStatusResp)(nil), "glory_http_logic.GetEnterStatusResp")
	proto.RegisterEnum("glory_http_logic.LiveStatus", LiveStatus_name, LiveStatus_value)
}

func init() {
	proto.RegisterFile("glory-http-logic/glory-http-logic.proto", fileDescriptor_glory_http_logic_f23a2fe907fa774d)
}

var fileDescriptor_glory_http_logic_f23a2fe907fa774d = []byte{
	// 1538 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x58, 0xcd, 0x6e, 0x1b, 0x47,
	0x12, 0xde, 0x21, 0x29, 0x72, 0x58, 0x14, 0xf5, 0xd3, 0x16, 0x2c, 0x4a, 0xb6, 0xd7, 0xf2, 0xac,
	0xd7, 0xab, 0xf5, 0xae, 0x25, 0x40, 0x76, 0x90, 0x38, 0x08, 0xe0, 0xd8, 0xb2, 0x25, 0x10, 0x30,
	0x14, 0x61, 0x2c, 0xc3, 0x40, 0x2e, 0x83, 0xf1, 0x4c, 0x93, 0x6c, 0x70, 0xfe, 0x3c, 0xdd, 0x14,
	0xcd, 0x63, 0x8e, 0x01, 0x92, 0x6b, 0x2e, 0x39, 0xe5, 0x55, 0xf2, 0x0c, 0x39, 0xe7, 0x29, 0xf2,
	0x00, 0x41, 0x55, 0x37, 0x47, 0xfc, 0x91, 0x04, 0xc9, 0xbe, 0x4d, 0x55, 0x57, 0x55, 0xd7, 0xcf,
	0x57, 0x55, 0x4d, 0xc2, 0x7f, 0xba, 0x51, 0x9a, 0x8f, 0x1e, 0xf5, 0x94, 0xca, 0x1e, 0x45, 0x69,
	0x57, 0x04, 0xbb, 0xb3, 0x8c, 0x9d, 0x2c, 0x4f, 0x55, 0xca, 0x56, 0x88, 0xef, 0x21, 0xdf, 0x23,
	0xfe, 0xe6, 0x5d, 0x2d, 0x99, 0xf3, 0xa1, 0x9f, 0x87, 0xbb, 0x93, 0x84, 0x56, 0x71, 0xfe, 0xb2,
	0x60, 0x6d, 0x9f, 0x47, 0xfc, 0x7d, 0x2e, 0xd4, 0xe8, 0xad, 0xe4, 0xf9, 0x71, 0x9e, 0x76, 0x44,
	0xc4, 0xd9, 0x0a, 0x94, 0x07, 0x22, 0x6c, 0x59, 0x5b, 0xd6, 0x76, 0xd3, 0xc5, 0x4f, 0xd6, 0x82,
	0x9a, 0x1f, 0x04, 0xe9, 0x20, 0x51, 0xad, 0xd2, 0x96, 0xb5, 0x5d, 0x77, 0xc7, 0x24, 0xdb, 0x04,
	0x3b, 0x11, 0x41, 0x3f, 0xf1, 0x63, 0xde, 0x2a, 0xd3, 0x51, 0x41, 0xa3, 0x1d, 0xc9, 0x3f, 0xb6,
	0x2a, 0x5b, 0xd6, 0xf6, 0x82, 0x8b, 0x9f, 0xec, 0x2e, 0x34, 0x06, 0xfd, 0xa1, 0x37, 0xb6, 0xb5,
	0x40, 0x0a, 0x30, 0xe8, 0x0f, 0x9f, 0x1b, 0x73, 0xf7, 0x60, 0x11, 0x05, 0x0a, 0x93, 0x55, 0x92,
	0x40, 0xa5, 0xa3, 0xb1, 0xd5, 0x3b, 0x00, 0x41, 0xcf, 0x4f, 0x12, 0x1e, 0x79, 0x22, 0x6c, 0xd5,
	0xc8, 0xc9, 0xba, 0xe1, 0xb4, 0x43, 0xbc, 0x22, 0x12, 0xa7, 0xdc, 0x93, 0xca, 0x57, 0x03, 0xd9,
	0xb2, 0xe9, 0x1c, 0x90, 0xf5, 0x86, 0x38, 0x0e, 0x87, 0xd5, 0x22, 0x6a, 0xd7, 0x4f, 0xfa, 0xed,
	0xa4, 0x93, 0xb2, 0xaf, 0xa1, 0x32, 0x90, 0x3c, 0xa7, 0x98, 0x1b, 0x7b, 0x0f, 0x76, 0x66, 0xb3,
	0xb9, 0x73, 0x5e, 0xa2, 0x5c, 0xd2, 0x61, 0x6b, 0xb0, 0x20, 0x83, 0x34, 0xe7, 0x94, 0x9a, 0xa6,
	0xab, 0x09, 0xe7, 0x29, 0xac, 0x1f, 0x72, 0x55, 0xa8, 0xbd, 0xe3, 0xbc, 0x8f, 0xb7, 0xb9, 0xfc,
	0x03, 0xfb, 0x27, 0x34, 0x84, 0xf4, 0x82, 0x41, 0xee, 0x0d, 0x39, 0xef, 0xd3, 0x9d, 0xb6, 0x5b,
	0x17, 0x72, 0x7f, 0x90, 0xa3, 0x98, 0xf3, 0x87, 0x75, 0x81, 0xae, 0xcc, 0xd8, 0xb7, 0x50, 0xcf,
	0xfd, 0xa4, 0xef, 0x45, 0x42, 0xaa, 0x96, 0xb5, 0x55, 0xde, 0x6e, 0xec, 0xfd, 0xeb, 0x12, 0x6f,
	0xc7, 0x01, 0xba, 0x36, 0x6a, 0xbd, 0x16, 0x52, 0xb1, 0x0d, 0xb0, 0xe3, 0x91, 0x37, 0xe9, 0x71,
	0x2d, 0x1e, 0xbd, 0x41, 0x92, 0x3d, 0x83, 0x5a, 0x3c, 0xf2, 0x44, 0xd2, 0x49, 0xa9, 0x96, 0x57,
	0x4f, 0x44, 0x35, 0x1e, 0x51, 0x1a, 0x6f, 0x41, 0x5d, 0x48, 0xaf, 0x23, 0x22, 0xc5, 0x73, 0xaa,
	0xbb, 0xed, 0xda, 0x42, 0x1e, 0x10, 0xed, 0xfc, 0x69, 0xc1, 0x8d, 0x42, 0xfb, 0xd8, 0x8f, 0xfc,
	0x80, 0x7f, 0x76, 0xee, 0xef, 0xc1, 0x62, 0x30, 0x3e, 0x45, 0x38, 0xe8, 0x80, 0x1a, 0x05, 0xaf,
	0x1d, 0x32, 0x06, 0x15, 0x8c, 0x9d, 0x22, 0x6a, 0xba, 0xf4, 0xcd, 0x96, 0xa0, 0xa4, 0x24, 0x39,
	0xd8, 0x74, 0x4b, 0x4a, 0x12, 0x52, 0x07, 0x31, 0xe1, 0xb1, 0xe9, 0xe2, 0x27, 0x5b, 0x87, 0x5a,
	0xe4, 0x4b, 0xe5, 0x29, 0x49, 0x18, 0x6c, 0xba, 0x55, 0x24, 0x4f, 0x24, 0xa6, 0x8f, 0x0e, 0x92,
	0x41, 0x6c, 0xc0, 0x47, 0x82, 0x47, 0x83, 0xd8, 0x09, 0xc1, 0x3e, 0x14, 0x1d, 0x45, 0x41, 0xad,
	0x43, 0xad, 0x2b, 0x3a, 0xca, 0x2b, 0xfa, 0xa8, 0x8a, 0x64, 0x3b, 0xc4, 0x14, 0xd1, 0x01, 0xc1,
	0x5b, 0x37, 0x93, 0x8d, 0x8c, 0x23, 0xc4, 0xb6, 0x03, 0x4d, 0xad, 0x15, 0xa4, 0x89, 0x37, 0xc8,
	0x23, 0xd3, 0x52, 0x0d, 0xd2, 0x0d, 0xd2, 0xe4, 0x6d, 0x1e, 0x39, 0x2f, 0xe1, 0xf6, 0x24, 0x38,
	0x74, 0x22, 0x4f, 0xd2, 0x8c, 0xca, 0xcc, 0x3f, 0xb0, 0xfb, 0xb0, 0x64, 0xd0, 0x95, 0xf1, 0x5c,
	0xa4, 0xa1, 0x34, 0x00, 0x5b, 0x24, 0x80, 0x1d, 0x6b, 0x9e, 0xf3, 0x5b, 0xe9, 0x32, 0x33, 0x04,
	0x34, 0x3b, 0xe1, 0x43, 0x0d, 0x06, 0x5d, 0x99, 0x7f, 0x5f, 0x52, 0x99, 0xb3, 0x72, 0xba, 0xb5,
	0x84, 0x0f, 0x29, 0x05, 0x2f, 0xa0, 0xfe, 0x9e, 0x4b, 0xa5, 0x4d, 0x94, 0xae, 0x63, 0xc2, 0x46,
	0x3d, 0xb2, 0xf1, 0x25, 0xd4, 0x83, 0x41, 0x9e, 0x7b, 0x98, 0x00, 0x83, 0xc9, 0xcd, 0x79, 0x1b,
	0xe3, 0xac, 0xbb, 0x36, 0x0a, 0x23, 0x85, 0x97, 0x4b, 0x1e, 0x75, 0xf4, 0xe5, 0x95, 0x6b, 0x5d,
	0x8e, 0x7a, 0xf8, 0xe5, 0x7c, 0x80, 0x3b, 0xf3, 0x29, 0xc2, 0x13, 0xec, 0xa3, 0x2b, 0xa7, 0x9a,
	0x3d, 0x84, 0x55, 0x42, 0xcc, 0x39, 0x40, 0x5d, 0xc6, 0x83, 0xfd, 0x33, 0xb0, 0x3a, 0x3f, 0x58,
	0x97, 0xde, 0x29, 0x33, 0x0c, 0x0c, 0x63, 0x9a, 0x1c, 0x00, 0x57, 0x0d, 0x4c, 0x18, 0x33, 0xec,
	0x36, 0x80, 0x90, 0x9e, 0x1f, 0x45, 0x1e, 0x4f, 0xb4, 0x2b, 0xd4, 0xa7, 0xcf, 0xa3, 0xe8, 0x55,
	0x12, 0x3a, 0x01, 0xb4, 0x5c, 0x9e, 0x45, 0xfe, 0x68, 0xc6, 0x08, 0x46, 0x3c, 0xdb, 0x6f, 0xd6,
	0x7c, 0xbf, 0xcd, 0x27, 0xa5, 0x74, 0x0e, 0xfe, 0x7e, 0x29, 0x5d, 0x74, 0x8b, 0xcc, 0xd8, 0x3e,
	0x16, 0x2f, 0x09, 0xbd, 0x4f, 0x18, 0x0b, 0x36, 0x2a, 0x22, 0x03, 0x87, 0x99, 0x4a, 0xb5, 0x89,
	0xd2, 0xf5, 0x86, 0x99, 0x4a, 0xc9, 0xc0, 0x0e, 0x54, 0xae, 0x08, 0x3b, 0x92, 0x9b, 0x1b, 0x2a,
	0x1b, 0x60, 0xeb, 0x4e, 0x2f, 0x26, 0x0b, 0x8d, 0x84, 0xa3, 0x41, 0x8c, 0x47, 0xb2, 0x97, 0x0e,
	0xbd, 0x40, 0x84, 0x66, 0xc5, 0xd5, 0x90, 0xde, 0x17, 0xa1, 0xf3, 0x0c, 0x96, 0xdb, 0xf2, 0x88,
	0xf3, 0xf0, 0x28, 0x55, 0x42, 0x27, 0xfd, 0xff, 0x50, 0x51, 0xa3, 0x8c, 0x53, 0x26, 0x96, 0xf6,
	0x5a, 0xc6, 0x11, 0xb3, 0xcb, 0xb5, 0xd8, 0xc9, 0x28, 0xe3, 0x2e, 0x49, 0x39, 0xbb, 0xb0, 0x32,
	0x6d, 0x40, 0x66, 0x66, 0x2e, 0x27, 0xc4, 0x30, 0x18, 0xb5, 0x85, 0xd4, 0x02, 0xce, 0xff, 0x60,
	0xc5, 0xe5, 0x01, 0x17, 0xa7, 0xdc, 0x25, 0x93, 0x78, 0xe5, 0x3a, 0xd4, 0x94, 0x2f, 0xfb, 0xba,
	0xc4, 0x65, 0x1c, 0x5f, 0x48, 0xb6, 0x43, 0xe7, 0x06, 0xac, 0xce, 0x08, 0xcb, 0xcc, 0xd9, 0x84,
	0xd6, 0x21, 0x57, 0x9a, 0x71, 0x90, 0xfb, 0xdd, 0x98, 0x27, 0xca, 0x8c, 0x23, 0xe7, 0x67, 0x0b,
	0x36, 0x2e, 0x38, 0xd4, 0x8e, 0x75, 0x72, 0xbf, 0xeb, 0x75, 0x70, 0x1a, 0x6a, 0x30, 0xd9, 0xc8,
	0x38, 0x30, 0x9b, 0x9e, 0x0e, 0x29, 0x5e, 0xd3, 0x31, 0x24, 0x7e, 0x88, 0x8c, 0xc9, 0x47, 0x49,
	0xf9, 0xe2, 0x47, 0x49, 0x65, 0xfa, 0x51, 0xe2, 0xfc, 0x5e, 0x86, 0xd5, 0xfd, 0x9e, 0x1f, 0x45,
	0x3c, 0xe9, 0xf2, 0x13, 0x0c, 0xca, 0x8c, 0xeb, 0xb3, 0x78, 0xad, 0xb3, 0x78, 0xd9, 0x37, 0xc6,
	0x41, 0x2a, 0x40, 0x89, 0x0a, 0x70, 0x77, 0xba, 0x00, 0xe4, 0xcc, 0x38, 0x30, 0xaa, 0x03, 0x45,
	0x80, 0x5f, 0xec, 0x29, 0x34, 0xb4, 0x94, 0xd6, 0x2f, 0x9f, 0x57, 0x40, 0x9d, 0x19, 0x52, 0x84,
	0xbc, 0xf8, 0x66, 0x8f, 0xa1, 0x4e, 0x1e, 0x91, 0x62, 0x85, 0x14, 0x6f, 0x4e, 0x2b, 0xa2, 0xf3,
	0xfa, 0x3e, 0x65, 0xbe, 0x28, 0x63, 0x22, 0x11, 0xb2, 0x37, 0x01, 0xba, 0xba, 0xe6, 0x20, 0xec,
	0x1e, 0xc0, 0x72, 0xc2, 0x79, 0xe8, 0x4d, 0xc8, 0xe8, 0xe5, 0xd6, 0x44, 0xf6, 0x41, 0x21, 0xb7,
	0x01, 0x76, 0xb1, 0x81, 0x6a, 0x3a, 0xb5, 0x42, 0x6f, 0x9f, 0xe9, 0xf5, 0x65, 0xcf, 0xac, 0xaf,
	0x3b, 0x00, 0x74, 0x98, 0xe5, 0x88, 0xb3, 0xba, 0xbe, 0x1e, 0x39, 0xc7, 0xc8, 0xc0, 0x63, 0x93,
	0x0d, 0xbc, 0x19, 0xf4, 0xb1, 0xe6, 0xe0, 0xad, 0xff, 0x85, 0x55, 0x21, 0x3d, 0xd5, 0x13, 0x92,
	0xde, 0x45, 0x1e, 0x46, 0xd5, 0x6a, 0x10, 0x58, 0x97, 0x84, 0x3c, 0xe9, 0x09, 0x89, 0x0f, 0x21,
	0x8c, 0xda, 0xb9, 0x45, 0x98, 0x9a, 0x2a, 0xe3, 0x4b, 0xae, 0x7c, 0x11, 0x21, 0xe2, 0x7e, 0x2c,
	0xc1, 0xe6, 0x45, 0xa7, 0x32, 0x63, 0x07, 0xb0, 0x68, 0xe2, 0xc7, 0x0b, 0xe4, 0x25, 0x8f, 0xa8,
	0x59, 0x94, 0xb8, 0x0d, 0xad, 0x88, 0xb4, 0x64, 0x2f, 0x00, 0x0a, 0x37, 0x71, 0xc6, 0x5d, 0xd9,
	0x4a, 0x7d, 0x68, 0xc2, 0x20, 0x1b, 0x94, 0x30, 0x6d, 0xa3, 0x7c, 0x0d, 0x1b, 0xa8, 0xa6, 0x6d,
	0x4c, 0xf5, 0x76, 0x65, 0xa6, 0xb7, 0xd7, 0x80, 0x1d, 0x72, 0x35, 0xce, 0x9b, 0xd9, 0x5b, 0xce,
	0xaf, 0x16, 0xdc, 0x98, 0x63, 0xeb, 0xc7, 0xa5, 0xee, 0x02, 0xbd, 0xf4, 0xaf, 0xec, 0x0d, 0x01,
	0xd0, 0xec, 0xfc, 0xc9, 0x80, 0x4a, 0x9f, 0x12, 0x90, 0xf3, 0x53, 0x19, 0xd8, 0xb8, 0x9f, 0x74,
	0x73, 0x90, 0xe9, 0xa9, 0x4e, 0xb4, 0x3e, 0xb3, 0x13, 0x4b, 0x9f, 0xda, 0x89, 0xe5, 0x2b, 0x76,
	0xe2, 0x4d, 0xa8, 0xe6, 0x3c, 0xf6, 0xf3, 0xbe, 0x19, 0x40, 0x86, 0x62, 0x5b, 0xb0, 0x18, 0xcb,
	0xee, 0xd9, 0x03, 0xcf, 0xfc, 0x04, 0x8a, 0x65, 0xb7, 0x7d, 0x5e, 0x87, 0x55, 0x67, 0x3a, 0x6c,
	0x0d, 0x16, 0xf4, 0xc4, 0xd3, 0x4f, 0x4f, 0x4d, 0xe0, 0x6f, 0x9e, 0x71, 0x70, 0xc2, 0xb4, 0x65,
	0xb9, 0x08, 0x41, 0xc4, 0x1c, 0x05, 0xf8, 0xc7, 0x4c, 0xe4, 0x5c, 0x0b, 0xd4, 0xb5, 0x80, 0x66,
	0x91, 0xc0, 0x06, 0x50, 0xaa, 0x26, 0x1a, 0xb3, 0x86, 0x34, 0xbe, 0x6a, 0x3f, 0x12, 0x84, 0x74,
	0x6e, 0x8a, 0x26, 0xc3, 0xf8, 0xd2, 0x4e, 0x47, 0x72, 0x35, 0x9e, 0x97, 0x9a, 0x42, 0x07, 0x23,
	0x11, 0x0b, 0x35, 0xfe, 0x31, 0x44, 0x04, 0x7b, 0x62, 0x36, 0x98, 0xce, 0xde, 0xd6, 0x74, 0xf6,
	0xa6, 0x6b, 0x3d, 0xb1, 0xc9, 0x38, 0xa1, 0x74, 0xfa, 0x66, 0x99, 0xb1, 0xaf, 0xa0, 0x32, 0x01,
	0xd0, 0xfb, 0xf3, 0xe8, 0x9a, 0x07, 0x8f, 0x4b, 0x1a, 0xe8, 0x9c, 0x4a, 0x95, 0x1f, 0x8d, 0x9d,
	0x23, 0x02, 0x57, 0xda, 0x21, 0x57, 0xaf, 0x12, 0xc5, 0x73, 0xfd, 0x13, 0x11, 0x5b, 0xe4, 0x11,
	0x45, 0x3d, 0xc5, 0x94, 0x19, 0xae, 0x09, 0x21, 0xbd, 0x34, 0xe3, 0x89, 0xd9, 0xa2, 0x55, 0x21,
	0xbf, 0xcb, 0x78, 0xf2, 0xf0, 0x00, 0xe0, 0x75, 0xf1, 0x13, 0x93, 0x2d, 0x43, 0xe3, 0x28, 0x55,
	0xed, 0xe4, 0xb5, 0x38, 0x15, 0x49, 0x77, 0xe5, 0x1f, 0x6c, 0x11, 0xec, 0x82, 0xb2, 0x98, 0x0d,
	0x95, 0x76, 0x72, 0xdc, 0x5f, 0x29, 0xa1, 0xe0, 0x3b, 0x5f, 0x05, 0x3d, 0x73, 0x54, 0x7e, 0xf1,
	0xe4, 0xfb, 0xbd, 0x6e, 0x1a, 0xf9, 0x49, 0x77, 0xe7, 0x8b, 0x3d, 0xa5, 0x76, 0x82, 0x34, 0xde,
	0xa5, 0x1f, 0xeb, 0x41, 0x1a, 0xed, 0x4a, 0x9e, 0x9f, 0x8a, 0x80, 0xcb, 0xb9, 0xbf, 0x00, 0xde,
	0x57, 0x49, 0xe6, 0xf1, 0xdf, 0x01, 0x00, 0x00, 0xff, 0xff, 0xb8, 0x05, 0xc8, 0x1f, 0x2e, 0x10,
	0x00, 0x00,
}
