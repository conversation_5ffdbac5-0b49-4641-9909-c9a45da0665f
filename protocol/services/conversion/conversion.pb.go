// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/conversion/conversion.proto

package conversion // import "golang.52tt.com/protocol/services/conversion"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type LogType int32

const (
	LogType_CONVERSION LogType = 0
	LogType_SEND       LogType = 1
	LogType_RECEIVE    LogType = 2
)

var LogType_name = map[int32]string{
	0: "CONVERSION",
	1: "SEND",
	2: "RECEIVE",
}
var LogType_value = map[string]int32{
	"CONVERSION": 0,
	"SEND":       1,
	"RECEIVE":    2,
}

func (x LogType) String() string {
	return proto.EnumName(LogType_name, int32(x))
}
func (LogType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{0}
}

type OrderStatus int32

const (
	OrderStatus_Prepare_Type  OrderStatus = 0
	OrderStatus_Commit_Type   OrderStatus = 1
	OrderStatus_RollBack_Type OrderStatus = 2
	OrderStatus_GetGift_Type  OrderStatus = 4
)

var OrderStatus_name = map[int32]string{
	0: "Prepare_Type",
	1: "Commit_Type",
	2: "RollBack_Type",
	4: "GetGift_Type",
}
var OrderStatus_value = map[string]int32{
	"Prepare_Type":  0,
	"Commit_Type":   1,
	"RollBack_Type": 2,
	"GetGift_Type":  4,
}

func (x OrderStatus) String() string {
	return proto.EnumName(OrderStatus_name, int32(x))
}
func (OrderStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{1}
}

type ConversionType int32

const (
	ConversionType_Gift_Type  ConversionType = 0
	ConversionType_Horse_Type ConversionType = 1
	ConversionType_Mic_Style  ConversionType = 2
	ConversionType_Dark_Gift  ConversionType = 3
)

var ConversionType_name = map[int32]string{
	0: "Gift_Type",
	1: "Horse_Type",
	2: "Mic_Style",
	3: "Dark_Gift",
}
var ConversionType_value = map[string]int32{
	"Gift_Type":  0,
	"Horse_Type": 1,
	"Mic_Style":  2,
	"Dark_Gift":  3,
}

func (x ConversionType) String() string {
	return proto.EnumName(ConversionType_name, int32(x))
}
func (ConversionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{2}
}

// =========================================== 运营后台黑暗礼物相关接口 start =================//
// 合成玩法类型
type ComposeType int32

const (
	ComposeType_Common      ComposeType = 0
	ComposeType_DarkCompose ComposeType = 1
)

var ComposeType_name = map[int32]string{
	0: "Common",
	1: "DarkCompose",
}
var ComposeType_value = map[string]int32{
	"Common":      0,
	"DarkCompose": 1,
}

func (x ComposeType) String() string {
	return proto.EnumName(ComposeType_name, int32(x))
}
func (ComposeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{3}
}

// 原材料类型
type MaterialType int32

const (
	MaterialType_GiftType     MaterialType = 0
	MaterialType_FragmentType MaterialType = 1
)

var MaterialType_name = map[int32]string{
	0: "GiftType",
	1: "FragmentType",
}
var MaterialType_value = map[string]int32{
	"GiftType":     0,
	"FragmentType": 1,
}

func (x MaterialType) String() string {
	return proto.EnumName(MaterialType_name, int32(x))
}
func (MaterialType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{4}
}

// 当前状态的枚举，分为未生效、上架中和已下架
type ConversionConfig_Status int32

const (
	ConversionConfig_Unknown   ConversionConfig_Status = 0
	ConversionConfig_NotActive ConversionConfig_Status = 1
	ConversionConfig_Active    ConversionConfig_Status = 2
	ConversionConfig_Expired   ConversionConfig_Status = 3
)

var ConversionConfig_Status_name = map[int32]string{
	0: "Unknown",
	1: "NotActive",
	2: "Active",
	3: "Expired",
}
var ConversionConfig_Status_value = map[string]int32{
	"Unknown":   0,
	"NotActive": 1,
	"Active":    2,
	"Expired":   3,
}

func (x ConversionConfig_Status) String() string {
	return proto.EnumName(ConversionConfig_Status_name, int32(x))
}
func (ConversionConfig_Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{7, 0}
}

type ConversionConfig_GiftType int32

const (
	ConversionConfig_Normal    ConversionConfig_GiftType = 0
	ConversionConfig_TimeLimit ConversionConfig_GiftType = 1
)

var ConversionConfig_GiftType_name = map[int32]string{
	0: "Normal",
	1: "TimeLimit",
}
var ConversionConfig_GiftType_value = map[string]int32{
	"Normal":    0,
	"TimeLimit": 1,
}

func (x ConversionConfig_GiftType) String() string {
	return proto.EnumName(ConversionConfig_GiftType_name, int32(x))
}
func (ConversionConfig_GiftType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{7, 1}
}

type ComposeGiftConf_GiftType int32

const (
	ComposeGiftConf_Common    ComposeGiftConf_GiftType = 0
	ComposeGiftConf_TimeLimit ComposeGiftConf_GiftType = 1
)

var ComposeGiftConf_GiftType_name = map[int32]string{
	0: "Common",
	1: "TimeLimit",
}
var ComposeGiftConf_GiftType_value = map[string]int32{
	"Common":    0,
	"TimeLimit": 1,
}

func (x ComposeGiftConf_GiftType) String() string {
	return proto.EnumName(ComposeGiftConf_GiftType_name, int32(x))
}
func (ComposeGiftConf_GiftType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{31, 0}
}

// 当前状态的枚举，分为未生效、上架中和已下架
type ComposeGiftConf_Status int32

const (
	ComposeGiftConf_Unknown   ComposeGiftConf_Status = 0
	ComposeGiftConf_NotActive ComposeGiftConf_Status = 1
	ComposeGiftConf_Active    ComposeGiftConf_Status = 2
	ComposeGiftConf_Expired   ComposeGiftConf_Status = 3
)

var ComposeGiftConf_Status_name = map[int32]string{
	0: "Unknown",
	1: "NotActive",
	2: "Active",
	3: "Expired",
}
var ComposeGiftConf_Status_value = map[string]int32{
	"Unknown":   0,
	"NotActive": 1,
	"Active":    2,
	"Expired":   3,
}

func (x ComposeGiftConf_Status) String() string {
	return proto.EnumName(ComposeGiftConf_Status_name, int32(x))
}
func (ComposeGiftConf_Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{31, 1}
}

type ComposeLog_Status int32

const (
	ComposeLog_Init    ComposeLog_Status = 0
	ComposeLog_Success ComposeLog_Status = 1
	ComposeLog_Fail    ComposeLog_Status = 2
)

var ComposeLog_Status_name = map[int32]string{
	0: "Init",
	1: "Success",
	2: "Fail",
}
var ComposeLog_Status_value = map[string]int32{
	"Init":    0,
	"Success": 1,
	"Fail":    2,
}

func (x ComposeLog_Status) String() string {
	return proto.EnumName(ComposeLog_Status_name, int32(x))
}
func (ComposeLog_Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{44, 0}
}

type DarkComposeGiftConf_GiftType int32

const (
	DarkComposeGiftConf_Common    DarkComposeGiftConf_GiftType = 0
	DarkComposeGiftConf_TimeLimit DarkComposeGiftConf_GiftType = 1
)

var DarkComposeGiftConf_GiftType_name = map[int32]string{
	0: "Common",
	1: "TimeLimit",
}
var DarkComposeGiftConf_GiftType_value = map[string]int32{
	"Common":    0,
	"TimeLimit": 1,
}

func (x DarkComposeGiftConf_GiftType) String() string {
	return proto.EnumName(DarkComposeGiftConf_GiftType_name, int32(x))
}
func (DarkComposeGiftConf_GiftType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{54, 0}
}

type ConversionLog struct {
	ConversionId         uint32   `protobuf:"varint,1,opt,name=conversion_id,json=conversionId,proto3" json:"conversion_id,omitempty"`
	ConversionTime       uint32   `protobuf:"varint,2,opt,name=conversion_time,json=conversionTime,proto3" json:"conversion_time,omitempty"`
	ConversionStatus     uint32   `protobuf:"varint,3,opt,name=conversion_status,json=conversionStatus,proto3" json:"conversion_status,omitempty"`
	ConversionLogType    uint32   `protobuf:"varint,4,opt,name=conversion_log_type,json=conversionLogType,proto3" json:"conversion_log_type,omitempty"`
	Count                uint32   `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConversionLog) Reset()         { *m = ConversionLog{} }
func (m *ConversionLog) String() string { return proto.CompactTextString(m) }
func (*ConversionLog) ProtoMessage()    {}
func (*ConversionLog) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{0}
}
func (m *ConversionLog) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConversionLog.Unmarshal(m, b)
}
func (m *ConversionLog) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConversionLog.Marshal(b, m, deterministic)
}
func (dst *ConversionLog) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConversionLog.Merge(dst, src)
}
func (m *ConversionLog) XXX_Size() int {
	return xxx_messageInfo_ConversionLog.Size(m)
}
func (m *ConversionLog) XXX_DiscardUnknown() {
	xxx_messageInfo_ConversionLog.DiscardUnknown(m)
}

var xxx_messageInfo_ConversionLog proto.InternalMessageInfo

func (m *ConversionLog) GetConversionId() uint32 {
	if m != nil {
		return m.ConversionId
	}
	return 0
}

func (m *ConversionLog) GetConversionTime() uint32 {
	if m != nil {
		return m.ConversionTime
	}
	return 0
}

func (m *ConversionLog) GetConversionStatus() uint32 {
	if m != nil {
		return m.ConversionStatus
	}
	return 0
}

func (m *ConversionLog) GetConversionLogType() uint32 {
	if m != nil {
		return m.ConversionLogType
	}
	return 0
}

func (m *ConversionLog) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetConversionLogsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageNum              uint32   `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetConversionLogsReq) Reset()         { *m = GetConversionLogsReq{} }
func (m *GetConversionLogsReq) String() string { return proto.CompactTextString(m) }
func (*GetConversionLogsReq) ProtoMessage()    {}
func (*GetConversionLogsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{1}
}
func (m *GetConversionLogsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConversionLogsReq.Unmarshal(m, b)
}
func (m *GetConversionLogsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConversionLogsReq.Marshal(b, m, deterministic)
}
func (dst *GetConversionLogsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConversionLogsReq.Merge(dst, src)
}
func (m *GetConversionLogsReq) XXX_Size() int {
	return xxx_messageInfo_GetConversionLogsReq.Size(m)
}
func (m *GetConversionLogsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConversionLogsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConversionLogsReq proto.InternalMessageInfo

func (m *GetConversionLogsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetConversionLogsReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetConversionLogsReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type GetConversionLogsResp struct {
	ConversionLogs       []*ConversionLog `protobuf:"bytes,1,rep,name=conversion_logs,json=conversionLogs,proto3" json:"conversion_logs,omitempty"`
	Total                uint32           `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetConversionLogsResp) Reset()         { *m = GetConversionLogsResp{} }
func (m *GetConversionLogsResp) String() string { return proto.CompactTextString(m) }
func (*GetConversionLogsResp) ProtoMessage()    {}
func (*GetConversionLogsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{2}
}
func (m *GetConversionLogsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConversionLogsResp.Unmarshal(m, b)
}
func (m *GetConversionLogsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConversionLogsResp.Marshal(b, m, deterministic)
}
func (dst *GetConversionLogsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConversionLogsResp.Merge(dst, src)
}
func (m *GetConversionLogsResp) XXX_Size() int {
	return xxx_messageInfo_GetConversionLogsResp.Size(m)
}
func (m *GetConversionLogsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConversionLogsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConversionLogsResp proto.InternalMessageInfo

func (m *GetConversionLogsResp) GetConversionLogs() []*ConversionLog {
	if m != nil {
		return m.ConversionLogs
	}
	return nil
}

func (m *GetConversionLogsResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type ConversionServerReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConversionServerReq) Reset()         { *m = ConversionServerReq{} }
func (m *ConversionServerReq) String() string { return proto.CompactTextString(m) }
func (*ConversionServerReq) ProtoMessage()    {}
func (*ConversionServerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{3}
}
func (m *ConversionServerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConversionServerReq.Unmarshal(m, b)
}
func (m *ConversionServerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConversionServerReq.Marshal(b, m, deterministic)
}
func (dst *ConversionServerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConversionServerReq.Merge(dst, src)
}
func (m *ConversionServerReq) XXX_Size() int {
	return xxx_messageInfo_ConversionServerReq.Size(m)
}
func (m *ConversionServerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConversionServerReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConversionServerReq proto.InternalMessageInfo

type ConversionServerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConversionServerResp) Reset()         { *m = ConversionServerResp{} }
func (m *ConversionServerResp) String() string { return proto.CompactTextString(m) }
func (*ConversionServerResp) ProtoMessage()    {}
func (*ConversionServerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{4}
}
func (m *ConversionServerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConversionServerResp.Unmarshal(m, b)
}
func (m *ConversionServerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConversionServerResp.Marshal(b, m, deterministic)
}
func (dst *ConversionServerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConversionServerResp.Merge(dst, src)
}
func (m *ConversionServerResp) XXX_Size() int {
	return xxx_messageInfo_ConversionServerResp.Size(m)
}
func (m *ConversionServerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConversionServerResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConversionServerResp proto.InternalMessageInfo

type UserItemInfo struct {
	ItemType             uint32   `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	UseItemId            uint32   `protobuf:"varint,2,opt,name=use_item_id,json=useItemId,proto3" json:"use_item_id,omitempty"`
	SourceId             uint32   `protobuf:"varint,3,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	UseCount             uint32   `protobuf:"varint,4,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserItemInfo) Reset()         { *m = UserItemInfo{} }
func (m *UserItemInfo) String() string { return proto.CompactTextString(m) }
func (*UserItemInfo) ProtoMessage()    {}
func (*UserItemInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{5}
}
func (m *UserItemInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserItemInfo.Unmarshal(m, b)
}
func (m *UserItemInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserItemInfo.Marshal(b, m, deterministic)
}
func (dst *UserItemInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserItemInfo.Merge(dst, src)
}
func (m *UserItemInfo) XXX_Size() int {
	return xxx_messageInfo_UserItemInfo.Size(m)
}
func (m *UserItemInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserItemInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserItemInfo proto.InternalMessageInfo

func (m *UserItemInfo) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *UserItemInfo) GetUseItemId() uint32 {
	if m != nil {
		return m.UseItemId
	}
	return 0
}

func (m *UserItemInfo) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *UserItemInfo) GetUseCount() uint32 {
	if m != nil {
		return m.UseCount
	}
	return 0
}

type ConversionDebris struct {
	ConversionId         uint32          `protobuf:"varint,1,opt,name=conversion_id,json=conversionId,proto3" json:"conversion_id,omitempty"`
	UserItemList         []*UserItemInfo `protobuf:"bytes,2,rep,name=user_item_list,json=userItemList,proto3" json:"user_item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ConversionDebris) Reset()         { *m = ConversionDebris{} }
func (m *ConversionDebris) String() string { return proto.CompactTextString(m) }
func (*ConversionDebris) ProtoMessage()    {}
func (*ConversionDebris) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{6}
}
func (m *ConversionDebris) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConversionDebris.Unmarshal(m, b)
}
func (m *ConversionDebris) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConversionDebris.Marshal(b, m, deterministic)
}
func (dst *ConversionDebris) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConversionDebris.Merge(dst, src)
}
func (m *ConversionDebris) XXX_Size() int {
	return xxx_messageInfo_ConversionDebris.Size(m)
}
func (m *ConversionDebris) XXX_DiscardUnknown() {
	xxx_messageInfo_ConversionDebris.DiscardUnknown(m)
}

var xxx_messageInfo_ConversionDebris proto.InternalMessageInfo

func (m *ConversionDebris) GetConversionId() uint32 {
	if m != nil {
		return m.ConversionId
	}
	return 0
}

func (m *ConversionDebris) GetUserItemList() []*UserItemInfo {
	if m != nil {
		return m.UserItemList
	}
	return nil
}

// 兑换关系结构
type ConversionConfig struct {
	ConversionId         uint32                    `protobuf:"varint,1,opt,name=conversion_id,json=conversionId,proto3" json:"conversion_id,omitempty"`
	Debrisid_2Num        map[uint32]uint32         `protobuf:"bytes,2,rep,name=debrisid_2_num,json=debrisid2Num,proto3" json:"debrisid_2_num,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	GiftId               string                    `protobuf:"bytes,3,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftNum              uint32                    `protobuf:"varint,4,opt,name=gift_num,json=giftNum,proto3" json:"gift_num,omitempty"`
	GiftName             string                    `protobuf:"bytes,5,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	ConversionType       uint32                    `protobuf:"varint,6,opt,name=conversion_type,json=conversionType,proto3" json:"conversion_type,omitempty"`
	ShowType             uint32                    `protobuf:"varint,7,opt,name=show_type,json=showType,proto3" json:"show_type,omitempty"`
	Index                uint32                    `protobuf:"varint,8,opt,name=index,proto3" json:"index,omitempty"`
	HoldDay              uint32                    `protobuf:"varint,9,opt,name=hold_day,json=holdDay,proto3" json:"hold_day,omitempty"`
	IsDelete             bool                      `protobuf:"varint,10,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
	UpTime               uint32                    `protobuf:"varint,11,opt,name=up_time,json=upTime,proto3" json:"up_time,omitempty"`
	DownTime             uint32                    `protobuf:"varint,12,opt,name=down_time,json=downTime,proto3" json:"down_time,omitempty"`
	Note                 string                    `protobuf:"bytes,13,opt,name=note,proto3" json:"note,omitempty"`
	Status               ConversionConfig_Status   `protobuf:"varint,14,opt,name=status,proto3,enum=conversion.ConversionConfig_Status" json:"status,omitempty"`
	GiftContent          string                    `protobuf:"bytes,15,opt,name=gift_content,json=giftContent,proto3" json:"gift_content,omitempty"`
	GiftType             ConversionConfig_GiftType `protobuf:"varint,16,opt,name=gift_type,json=giftType,proto3,enum=conversion.ConversionConfig_GiftType" json:"gift_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *ConversionConfig) Reset()         { *m = ConversionConfig{} }
func (m *ConversionConfig) String() string { return proto.CompactTextString(m) }
func (*ConversionConfig) ProtoMessage()    {}
func (*ConversionConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{7}
}
func (m *ConversionConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConversionConfig.Unmarshal(m, b)
}
func (m *ConversionConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConversionConfig.Marshal(b, m, deterministic)
}
func (dst *ConversionConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConversionConfig.Merge(dst, src)
}
func (m *ConversionConfig) XXX_Size() int {
	return xxx_messageInfo_ConversionConfig.Size(m)
}
func (m *ConversionConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_ConversionConfig.DiscardUnknown(m)
}

var xxx_messageInfo_ConversionConfig proto.InternalMessageInfo

func (m *ConversionConfig) GetConversionId() uint32 {
	if m != nil {
		return m.ConversionId
	}
	return 0
}

func (m *ConversionConfig) GetDebrisid_2Num() map[uint32]uint32 {
	if m != nil {
		return m.Debrisid_2Num
	}
	return nil
}

func (m *ConversionConfig) GetGiftId() string {
	if m != nil {
		return m.GiftId
	}
	return ""
}

func (m *ConversionConfig) GetGiftNum() uint32 {
	if m != nil {
		return m.GiftNum
	}
	return 0
}

func (m *ConversionConfig) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *ConversionConfig) GetConversionType() uint32 {
	if m != nil {
		return m.ConversionType
	}
	return 0
}

func (m *ConversionConfig) GetShowType() uint32 {
	if m != nil {
		return m.ShowType
	}
	return 0
}

func (m *ConversionConfig) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *ConversionConfig) GetHoldDay() uint32 {
	if m != nil {
		return m.HoldDay
	}
	return 0
}

func (m *ConversionConfig) GetIsDelete() bool {
	if m != nil {
		return m.IsDelete
	}
	return false
}

func (m *ConversionConfig) GetUpTime() uint32 {
	if m != nil {
		return m.UpTime
	}
	return 0
}

func (m *ConversionConfig) GetDownTime() uint32 {
	if m != nil {
		return m.DownTime
	}
	return 0
}

func (m *ConversionConfig) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *ConversionConfig) GetStatus() ConversionConfig_Status {
	if m != nil {
		return m.Status
	}
	return ConversionConfig_Unknown
}

func (m *ConversionConfig) GetGiftContent() string {
	if m != nil {
		return m.GiftContent
	}
	return ""
}

func (m *ConversionConfig) GetGiftType() ConversionConfig_GiftType {
	if m != nil {
		return m.GiftType
	}
	return ConversionConfig_Normal
}

// 兑换碎片
type ConversionDebrisReq struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Conversion           *ConversionDebris `protobuf:"bytes,2,opt,name=conversion,proto3" json:"conversion,omitempty"`
	Price                uint32            `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`
	TargetUid            uint32            `protobuf:"varint,4,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	Count                uint32            `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ConversionDebrisReq) Reset()         { *m = ConversionDebrisReq{} }
func (m *ConversionDebrisReq) String() string { return proto.CompactTextString(m) }
func (*ConversionDebrisReq) ProtoMessage()    {}
func (*ConversionDebrisReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{8}
}
func (m *ConversionDebrisReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConversionDebrisReq.Unmarshal(m, b)
}
func (m *ConversionDebrisReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConversionDebrisReq.Marshal(b, m, deterministic)
}
func (dst *ConversionDebrisReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConversionDebrisReq.Merge(dst, src)
}
func (m *ConversionDebrisReq) XXX_Size() int {
	return xxx_messageInfo_ConversionDebrisReq.Size(m)
}
func (m *ConversionDebrisReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConversionDebrisReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConversionDebrisReq proto.InternalMessageInfo

func (m *ConversionDebrisReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConversionDebrisReq) GetConversion() *ConversionDebris {
	if m != nil {
		return m.Conversion
	}
	return nil
}

func (m *ConversionDebrisReq) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *ConversionDebrisReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *ConversionDebrisReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type ConversionDebrisResp struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConversionDebrisResp) Reset()         { *m = ConversionDebrisResp{} }
func (m *ConversionDebrisResp) String() string { return proto.CompactTextString(m) }
func (*ConversionDebrisResp) ProtoMessage()    {}
func (*ConversionDebrisResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{9}
}
func (m *ConversionDebrisResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConversionDebrisResp.Unmarshal(m, b)
}
func (m *ConversionDebrisResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConversionDebrisResp.Marshal(b, m, deterministic)
}
func (dst *ConversionDebrisResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConversionDebrisResp.Merge(dst, src)
}
func (m *ConversionDebrisResp) XXX_Size() int {
	return xxx_messageInfo_ConversionDebrisResp.Size(m)
}
func (m *ConversionDebrisResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConversionDebrisResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConversionDebrisResp proto.InternalMessageInfo

func (m *ConversionDebrisResp) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

// 添加兑换配置
type AddConversionConfigReq struct {
	ConversionConfig     *ConversionConfig `protobuf:"bytes,1,opt,name=conversion_config,json=conversionConfig,proto3" json:"conversion_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AddConversionConfigReq) Reset()         { *m = AddConversionConfigReq{} }
func (m *AddConversionConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddConversionConfigReq) ProtoMessage()    {}
func (*AddConversionConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{10}
}
func (m *AddConversionConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddConversionConfigReq.Unmarshal(m, b)
}
func (m *AddConversionConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddConversionConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddConversionConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddConversionConfigReq.Merge(dst, src)
}
func (m *AddConversionConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddConversionConfigReq.Size(m)
}
func (m *AddConversionConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddConversionConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddConversionConfigReq proto.InternalMessageInfo

func (m *AddConversionConfigReq) GetConversionConfig() *ConversionConfig {
	if m != nil {
		return m.ConversionConfig
	}
	return nil
}

type AddConversionConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddConversionConfigResp) Reset()         { *m = AddConversionConfigResp{} }
func (m *AddConversionConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddConversionConfigResp) ProtoMessage()    {}
func (*AddConversionConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{11}
}
func (m *AddConversionConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddConversionConfigResp.Unmarshal(m, b)
}
func (m *AddConversionConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddConversionConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddConversionConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddConversionConfigResp.Merge(dst, src)
}
func (m *AddConversionConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddConversionConfigResp.Size(m)
}
func (m *AddConversionConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddConversionConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddConversionConfigResp proto.InternalMessageInfo

type SetConversionConfigsReq struct {
	Configs              []*ConversionConfig `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *SetConversionConfigsReq) Reset()         { *m = SetConversionConfigsReq{} }
func (m *SetConversionConfigsReq) String() string { return proto.CompactTextString(m) }
func (*SetConversionConfigsReq) ProtoMessage()    {}
func (*SetConversionConfigsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{12}
}
func (m *SetConversionConfigsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetConversionConfigsReq.Unmarshal(m, b)
}
func (m *SetConversionConfigsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetConversionConfigsReq.Marshal(b, m, deterministic)
}
func (dst *SetConversionConfigsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetConversionConfigsReq.Merge(dst, src)
}
func (m *SetConversionConfigsReq) XXX_Size() int {
	return xxx_messageInfo_SetConversionConfigsReq.Size(m)
}
func (m *SetConversionConfigsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetConversionConfigsReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetConversionConfigsReq proto.InternalMessageInfo

func (m *SetConversionConfigsReq) GetConfigs() []*ConversionConfig {
	if m != nil {
		return m.Configs
	}
	return nil
}

type SetConversionConfigsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetConversionConfigsResp) Reset()         { *m = SetConversionConfigsResp{} }
func (m *SetConversionConfigsResp) String() string { return proto.CompactTextString(m) }
func (*SetConversionConfigsResp) ProtoMessage()    {}
func (*SetConversionConfigsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{13}
}
func (m *SetConversionConfigsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetConversionConfigsResp.Unmarshal(m, b)
}
func (m *SetConversionConfigsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetConversionConfigsResp.Marshal(b, m, deterministic)
}
func (dst *SetConversionConfigsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetConversionConfigsResp.Merge(dst, src)
}
func (m *SetConversionConfigsResp) XXX_Size() int {
	return xxx_messageInfo_SetConversionConfigsResp.Size(m)
}
func (m *SetConversionConfigsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetConversionConfigsResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetConversionConfigsResp proto.InternalMessageInfo

// 删除兑换配置
type DelConversionConfigReq struct {
	ConversionId         uint32   `protobuf:"varint,1,opt,name=conversion_id,json=conversionId,proto3" json:"conversion_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelConversionConfigReq) Reset()         { *m = DelConversionConfigReq{} }
func (m *DelConversionConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelConversionConfigReq) ProtoMessage()    {}
func (*DelConversionConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{14}
}
func (m *DelConversionConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelConversionConfigReq.Unmarshal(m, b)
}
func (m *DelConversionConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelConversionConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelConversionConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelConversionConfigReq.Merge(dst, src)
}
func (m *DelConversionConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelConversionConfigReq.Size(m)
}
func (m *DelConversionConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelConversionConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelConversionConfigReq proto.InternalMessageInfo

func (m *DelConversionConfigReq) GetConversionId() uint32 {
	if m != nil {
		return m.ConversionId
	}
	return 0
}

type DelConversionConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelConversionConfigResp) Reset()         { *m = DelConversionConfigResp{} }
func (m *DelConversionConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelConversionConfigResp) ProtoMessage()    {}
func (*DelConversionConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{15}
}
func (m *DelConversionConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelConversionConfigResp.Unmarshal(m, b)
}
func (m *DelConversionConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelConversionConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelConversionConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelConversionConfigResp.Merge(dst, src)
}
func (m *DelConversionConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelConversionConfigResp.Size(m)
}
func (m *DelConversionConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelConversionConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelConversionConfigResp proto.InternalMessageInfo

type DelConversionConfByIdsReq struct {
	ConversionIdList     []uint32 `protobuf:"varint,1,rep,packed,name=conversion_id_list,json=conversionIdList,proto3" json:"conversion_id_list,omitempty"`
	EffectTime           uint32   `protobuf:"varint,2,opt,name=effect_time,json=effectTime,proto3" json:"effect_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelConversionConfByIdsReq) Reset()         { *m = DelConversionConfByIdsReq{} }
func (m *DelConversionConfByIdsReq) String() string { return proto.CompactTextString(m) }
func (*DelConversionConfByIdsReq) ProtoMessage()    {}
func (*DelConversionConfByIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{16}
}
func (m *DelConversionConfByIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelConversionConfByIdsReq.Unmarshal(m, b)
}
func (m *DelConversionConfByIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelConversionConfByIdsReq.Marshal(b, m, deterministic)
}
func (dst *DelConversionConfByIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelConversionConfByIdsReq.Merge(dst, src)
}
func (m *DelConversionConfByIdsReq) XXX_Size() int {
	return xxx_messageInfo_DelConversionConfByIdsReq.Size(m)
}
func (m *DelConversionConfByIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelConversionConfByIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelConversionConfByIdsReq proto.InternalMessageInfo

func (m *DelConversionConfByIdsReq) GetConversionIdList() []uint32 {
	if m != nil {
		return m.ConversionIdList
	}
	return nil
}

func (m *DelConversionConfByIdsReq) GetEffectTime() uint32 {
	if m != nil {
		return m.EffectTime
	}
	return 0
}

type DelConversionConfByIdsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelConversionConfByIdsResp) Reset()         { *m = DelConversionConfByIdsResp{} }
func (m *DelConversionConfByIdsResp) String() string { return proto.CompactTextString(m) }
func (*DelConversionConfByIdsResp) ProtoMessage()    {}
func (*DelConversionConfByIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{17}
}
func (m *DelConversionConfByIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelConversionConfByIdsResp.Unmarshal(m, b)
}
func (m *DelConversionConfByIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelConversionConfByIdsResp.Marshal(b, m, deterministic)
}
func (dst *DelConversionConfByIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelConversionConfByIdsResp.Merge(dst, src)
}
func (m *DelConversionConfByIdsResp) XXX_Size() int {
	return xxx_messageInfo_DelConversionConfByIdsResp.Size(m)
}
func (m *DelConversionConfByIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelConversionConfByIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelConversionConfByIdsResp proto.InternalMessageInfo

// 取全部兑换配置
type GetAllConversionConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllConversionConfigReq) Reset()         { *m = GetAllConversionConfigReq{} }
func (m *GetAllConversionConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetAllConversionConfigReq) ProtoMessage()    {}
func (*GetAllConversionConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{18}
}
func (m *GetAllConversionConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllConversionConfigReq.Unmarshal(m, b)
}
func (m *GetAllConversionConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllConversionConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetAllConversionConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllConversionConfigReq.Merge(dst, src)
}
func (m *GetAllConversionConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetAllConversionConfigReq.Size(m)
}
func (m *GetAllConversionConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllConversionConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllConversionConfigReq proto.InternalMessageInfo

type GetAllConversionConfigResp struct {
	ConversionConfigs    []*ConversionConfig `protobuf:"bytes,1,rep,name=conversion_configs,json=conversionConfigs,proto3" json:"conversion_configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetAllConversionConfigResp) Reset()         { *m = GetAllConversionConfigResp{} }
func (m *GetAllConversionConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetAllConversionConfigResp) ProtoMessage()    {}
func (*GetAllConversionConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{19}
}
func (m *GetAllConversionConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllConversionConfigResp.Unmarshal(m, b)
}
func (m *GetAllConversionConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllConversionConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetAllConversionConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllConversionConfigResp.Merge(dst, src)
}
func (m *GetAllConversionConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetAllConversionConfigResp.Size(m)
}
func (m *GetAllConversionConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllConversionConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllConversionConfigResp proto.InternalMessageInfo

func (m *GetAllConversionConfigResp) GetConversionConfigs() []*ConversionConfig {
	if m != nil {
		return m.ConversionConfigs
	}
	return nil
}

type GetFriendGiftReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFriendGiftReq) Reset()         { *m = GetFriendGiftReq{} }
func (m *GetFriendGiftReq) String() string { return proto.CompactTextString(m) }
func (*GetFriendGiftReq) ProtoMessage()    {}
func (*GetFriendGiftReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{20}
}
func (m *GetFriendGiftReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFriendGiftReq.Unmarshal(m, b)
}
func (m *GetFriendGiftReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFriendGiftReq.Marshal(b, m, deterministic)
}
func (dst *GetFriendGiftReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFriendGiftReq.Merge(dst, src)
}
func (m *GetFriendGiftReq) XXX_Size() int {
	return xxx_messageInfo_GetFriendGiftReq.Size(m)
}
func (m *GetFriendGiftReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFriendGiftReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFriendGiftReq proto.InternalMessageInfo

func (m *GetFriendGiftReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFriendGiftReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetFriendGiftResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFriendGiftResp) Reset()         { *m = GetFriendGiftResp{} }
func (m *GetFriendGiftResp) String() string { return proto.CompactTextString(m) }
func (*GetFriendGiftResp) ProtoMessage()    {}
func (*GetFriendGiftResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{21}
}
func (m *GetFriendGiftResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFriendGiftResp.Unmarshal(m, b)
}
func (m *GetFriendGiftResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFriendGiftResp.Marshal(b, m, deterministic)
}
func (dst *GetFriendGiftResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFriendGiftResp.Merge(dst, src)
}
func (m *GetFriendGiftResp) XXX_Size() int {
	return xxx_messageInfo_GetFriendGiftResp.Size(m)
}
func (m *GetFriendGiftResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFriendGiftResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFriendGiftResp proto.InternalMessageInfo

// 合成 原料配置
type ComposeMaterialConf struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	GiftId               uint32   `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	SortId               uint32   `protobuf:"varint,3,opt,name=sort_id,json=sortId,proto3" json:"sort_id,omitempty"`
	UpdateAt             uint32   `protobuf:"varint,4,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	BgId                 uint32   `protobuf:"varint,5,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	GiftName             string   `protobuf:"bytes,6,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GiftPrice            uint32   `protobuf:"varint,7,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ComposeMaterialConf) Reset()         { *m = ComposeMaterialConf{} }
func (m *ComposeMaterialConf) String() string { return proto.CompactTextString(m) }
func (*ComposeMaterialConf) ProtoMessage()    {}
func (*ComposeMaterialConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{22}
}
func (m *ComposeMaterialConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ComposeMaterialConf.Unmarshal(m, b)
}
func (m *ComposeMaterialConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ComposeMaterialConf.Marshal(b, m, deterministic)
}
func (dst *ComposeMaterialConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ComposeMaterialConf.Merge(dst, src)
}
func (m *ComposeMaterialConf) XXX_Size() int {
	return xxx_messageInfo_ComposeMaterialConf.Size(m)
}
func (m *ComposeMaterialConf) XXX_DiscardUnknown() {
	xxx_messageInfo_ComposeMaterialConf.DiscardUnknown(m)
}

var xxx_messageInfo_ComposeMaterialConf proto.InternalMessageInfo

func (m *ComposeMaterialConf) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ComposeMaterialConf) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *ComposeMaterialConf) GetSortId() uint32 {
	if m != nil {
		return m.SortId
	}
	return 0
}

func (m *ComposeMaterialConf) GetUpdateAt() uint32 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

func (m *ComposeMaterialConf) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *ComposeMaterialConf) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *ComposeMaterialConf) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

// 新增原料配置
type AddComposeMaterialConfReq struct {
	Conf                 *ComposeMaterialConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *AddComposeMaterialConfReq) Reset()         { *m = AddComposeMaterialConfReq{} }
func (m *AddComposeMaterialConfReq) String() string { return proto.CompactTextString(m) }
func (*AddComposeMaterialConfReq) ProtoMessage()    {}
func (*AddComposeMaterialConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{23}
}
func (m *AddComposeMaterialConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddComposeMaterialConfReq.Unmarshal(m, b)
}
func (m *AddComposeMaterialConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddComposeMaterialConfReq.Marshal(b, m, deterministic)
}
func (dst *AddComposeMaterialConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddComposeMaterialConfReq.Merge(dst, src)
}
func (m *AddComposeMaterialConfReq) XXX_Size() int {
	return xxx_messageInfo_AddComposeMaterialConfReq.Size(m)
}
func (m *AddComposeMaterialConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddComposeMaterialConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddComposeMaterialConfReq proto.InternalMessageInfo

func (m *AddComposeMaterialConfReq) GetConf() *ComposeMaterialConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type AddComposeMaterialConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddComposeMaterialConfResp) Reset()         { *m = AddComposeMaterialConfResp{} }
func (m *AddComposeMaterialConfResp) String() string { return proto.CompactTextString(m) }
func (*AddComposeMaterialConfResp) ProtoMessage()    {}
func (*AddComposeMaterialConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{24}
}
func (m *AddComposeMaterialConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddComposeMaterialConfResp.Unmarshal(m, b)
}
func (m *AddComposeMaterialConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddComposeMaterialConfResp.Marshal(b, m, deterministic)
}
func (dst *AddComposeMaterialConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddComposeMaterialConfResp.Merge(dst, src)
}
func (m *AddComposeMaterialConfResp) XXX_Size() int {
	return xxx_messageInfo_AddComposeMaterialConfResp.Size(m)
}
func (m *AddComposeMaterialConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddComposeMaterialConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddComposeMaterialConfResp proto.InternalMessageInfo

// 批量新增原料配置
type BatchAddComposeMaterialConfReq struct {
	ConfList             []*ComposeMaterialConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BatchAddComposeMaterialConfReq) Reset()         { *m = BatchAddComposeMaterialConfReq{} }
func (m *BatchAddComposeMaterialConfReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddComposeMaterialConfReq) ProtoMessage()    {}
func (*BatchAddComposeMaterialConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{25}
}
func (m *BatchAddComposeMaterialConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddComposeMaterialConfReq.Unmarshal(m, b)
}
func (m *BatchAddComposeMaterialConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddComposeMaterialConfReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddComposeMaterialConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddComposeMaterialConfReq.Merge(dst, src)
}
func (m *BatchAddComposeMaterialConfReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddComposeMaterialConfReq.Size(m)
}
func (m *BatchAddComposeMaterialConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddComposeMaterialConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddComposeMaterialConfReq proto.InternalMessageInfo

func (m *BatchAddComposeMaterialConfReq) GetConfList() []*ComposeMaterialConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type BatchAddComposeMaterialConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddComposeMaterialConfResp) Reset()         { *m = BatchAddComposeMaterialConfResp{} }
func (m *BatchAddComposeMaterialConfResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddComposeMaterialConfResp) ProtoMessage()    {}
func (*BatchAddComposeMaterialConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{26}
}
func (m *BatchAddComposeMaterialConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddComposeMaterialConfResp.Unmarshal(m, b)
}
func (m *BatchAddComposeMaterialConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddComposeMaterialConfResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddComposeMaterialConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddComposeMaterialConfResp.Merge(dst, src)
}
func (m *BatchAddComposeMaterialConfResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddComposeMaterialConfResp.Size(m)
}
func (m *BatchAddComposeMaterialConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddComposeMaterialConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddComposeMaterialConfResp proto.InternalMessageInfo

// 删除原料配置
type DelComposeMaterialConfReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelComposeMaterialConfReq) Reset()         { *m = DelComposeMaterialConfReq{} }
func (m *DelComposeMaterialConfReq) String() string { return proto.CompactTextString(m) }
func (*DelComposeMaterialConfReq) ProtoMessage()    {}
func (*DelComposeMaterialConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{27}
}
func (m *DelComposeMaterialConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelComposeMaterialConfReq.Unmarshal(m, b)
}
func (m *DelComposeMaterialConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelComposeMaterialConfReq.Marshal(b, m, deterministic)
}
func (dst *DelComposeMaterialConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelComposeMaterialConfReq.Merge(dst, src)
}
func (m *DelComposeMaterialConfReq) XXX_Size() int {
	return xxx_messageInfo_DelComposeMaterialConfReq.Size(m)
}
func (m *DelComposeMaterialConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelComposeMaterialConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelComposeMaterialConfReq proto.InternalMessageInfo

func (m *DelComposeMaterialConfReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelComposeMaterialConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelComposeMaterialConfResp) Reset()         { *m = DelComposeMaterialConfResp{} }
func (m *DelComposeMaterialConfResp) String() string { return proto.CompactTextString(m) }
func (*DelComposeMaterialConfResp) ProtoMessage()    {}
func (*DelComposeMaterialConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{28}
}
func (m *DelComposeMaterialConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelComposeMaterialConfResp.Unmarshal(m, b)
}
func (m *DelComposeMaterialConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelComposeMaterialConfResp.Marshal(b, m, deterministic)
}
func (dst *DelComposeMaterialConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelComposeMaterialConfResp.Merge(dst, src)
}
func (m *DelComposeMaterialConfResp) XXX_Size() int {
	return xxx_messageInfo_DelComposeMaterialConfResp.Size(m)
}
func (m *DelComposeMaterialConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelComposeMaterialConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelComposeMaterialConfResp proto.InternalMessageInfo

// 获取原料配置列表
type GetAllComposeMaterialConfReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllComposeMaterialConfReq) Reset()         { *m = GetAllComposeMaterialConfReq{} }
func (m *GetAllComposeMaterialConfReq) String() string { return proto.CompactTextString(m) }
func (*GetAllComposeMaterialConfReq) ProtoMessage()    {}
func (*GetAllComposeMaterialConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{29}
}
func (m *GetAllComposeMaterialConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllComposeMaterialConfReq.Unmarshal(m, b)
}
func (m *GetAllComposeMaterialConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllComposeMaterialConfReq.Marshal(b, m, deterministic)
}
func (dst *GetAllComposeMaterialConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllComposeMaterialConfReq.Merge(dst, src)
}
func (m *GetAllComposeMaterialConfReq) XXX_Size() int {
	return xxx_messageInfo_GetAllComposeMaterialConfReq.Size(m)
}
func (m *GetAllComposeMaterialConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllComposeMaterialConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllComposeMaterialConfReq proto.InternalMessageInfo

type GetAllComposeMaterialConfResp struct {
	ConfList             []*ComposeMaterialConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetAllComposeMaterialConfResp) Reset()         { *m = GetAllComposeMaterialConfResp{} }
func (m *GetAllComposeMaterialConfResp) String() string { return proto.CompactTextString(m) }
func (*GetAllComposeMaterialConfResp) ProtoMessage()    {}
func (*GetAllComposeMaterialConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{30}
}
func (m *GetAllComposeMaterialConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllComposeMaterialConfResp.Unmarshal(m, b)
}
func (m *GetAllComposeMaterialConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllComposeMaterialConfResp.Marshal(b, m, deterministic)
}
func (dst *GetAllComposeMaterialConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllComposeMaterialConfResp.Merge(dst, src)
}
func (m *GetAllComposeMaterialConfResp) XXX_Size() int {
	return xxx_messageInfo_GetAllComposeMaterialConfResp.Size(m)
}
func (m *GetAllComposeMaterialConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllComposeMaterialConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllComposeMaterialConfResp proto.InternalMessageInfo

func (m *GetAllComposeMaterialConfResp) GetConfList() []*ComposeMaterialConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

// 合成 礼物配置
type ComposeGiftConf struct {
	Id                   uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	BgId                 uint32                 `protobuf:"varint,2,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	GiftId               uint32                 `protobuf:"varint,3,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftType             uint32                 `protobuf:"varint,4,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	SortId               uint32                 `protobuf:"varint,5,opt,name=sort_id,json=sortId,proto3" json:"sort_id,omitempty"`
	UpTime               uint32                 `protobuf:"varint,6,opt,name=up_time,json=upTime,proto3" json:"up_time,omitempty"`
	DownTime             uint32                 `protobuf:"varint,7,opt,name=down_time,json=downTime,proto3" json:"down_time,omitempty"`
	UpdateAt             uint32                 `protobuf:"varint,8,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	GiftName             string                 `protobuf:"bytes,9,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	Note                 string                 `protobuf:"bytes,10,opt,name=note,proto3" json:"note,omitempty"`
	Status               ComposeGiftConf_Status `protobuf:"varint,11,opt,name=status,proto3,enum=conversion.ComposeGiftConf_Status" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ComposeGiftConf) Reset()         { *m = ComposeGiftConf{} }
func (m *ComposeGiftConf) String() string { return proto.CompactTextString(m) }
func (*ComposeGiftConf) ProtoMessage()    {}
func (*ComposeGiftConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{31}
}
func (m *ComposeGiftConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ComposeGiftConf.Unmarshal(m, b)
}
func (m *ComposeGiftConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ComposeGiftConf.Marshal(b, m, deterministic)
}
func (dst *ComposeGiftConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ComposeGiftConf.Merge(dst, src)
}
func (m *ComposeGiftConf) XXX_Size() int {
	return xxx_messageInfo_ComposeGiftConf.Size(m)
}
func (m *ComposeGiftConf) XXX_DiscardUnknown() {
	xxx_messageInfo_ComposeGiftConf.DiscardUnknown(m)
}

var xxx_messageInfo_ComposeGiftConf proto.InternalMessageInfo

func (m *ComposeGiftConf) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ComposeGiftConf) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *ComposeGiftConf) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *ComposeGiftConf) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *ComposeGiftConf) GetSortId() uint32 {
	if m != nil {
		return m.SortId
	}
	return 0
}

func (m *ComposeGiftConf) GetUpTime() uint32 {
	if m != nil {
		return m.UpTime
	}
	return 0
}

func (m *ComposeGiftConf) GetDownTime() uint32 {
	if m != nil {
		return m.DownTime
	}
	return 0
}

func (m *ComposeGiftConf) GetUpdateAt() uint32 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

func (m *ComposeGiftConf) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *ComposeGiftConf) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *ComposeGiftConf) GetStatus() ComposeGiftConf_Status {
	if m != nil {
		return m.Status
	}
	return ComposeGiftConf_Unknown
}

// 新增合成礼物配置
type AddComposeGiftConfReq struct {
	Conf                 *ComposeGiftConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *AddComposeGiftConfReq) Reset()         { *m = AddComposeGiftConfReq{} }
func (m *AddComposeGiftConfReq) String() string { return proto.CompactTextString(m) }
func (*AddComposeGiftConfReq) ProtoMessage()    {}
func (*AddComposeGiftConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{32}
}
func (m *AddComposeGiftConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddComposeGiftConfReq.Unmarshal(m, b)
}
func (m *AddComposeGiftConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddComposeGiftConfReq.Marshal(b, m, deterministic)
}
func (dst *AddComposeGiftConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddComposeGiftConfReq.Merge(dst, src)
}
func (m *AddComposeGiftConfReq) XXX_Size() int {
	return xxx_messageInfo_AddComposeGiftConfReq.Size(m)
}
func (m *AddComposeGiftConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddComposeGiftConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddComposeGiftConfReq proto.InternalMessageInfo

func (m *AddComposeGiftConfReq) GetConf() *ComposeGiftConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type AddComposeGiftConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddComposeGiftConfResp) Reset()         { *m = AddComposeGiftConfResp{} }
func (m *AddComposeGiftConfResp) String() string { return proto.CompactTextString(m) }
func (*AddComposeGiftConfResp) ProtoMessage()    {}
func (*AddComposeGiftConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{33}
}
func (m *AddComposeGiftConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddComposeGiftConfResp.Unmarshal(m, b)
}
func (m *AddComposeGiftConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddComposeGiftConfResp.Marshal(b, m, deterministic)
}
func (dst *AddComposeGiftConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddComposeGiftConfResp.Merge(dst, src)
}
func (m *AddComposeGiftConfResp) XXX_Size() int {
	return xxx_messageInfo_AddComposeGiftConfResp.Size(m)
}
func (m *AddComposeGiftConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddComposeGiftConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddComposeGiftConfResp proto.InternalMessageInfo

// 批量新增合成礼物配置
type BatchAddComposeGiftConfReq struct {
	ConfList             []*ComposeGiftConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchAddComposeGiftConfReq) Reset()         { *m = BatchAddComposeGiftConfReq{} }
func (m *BatchAddComposeGiftConfReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddComposeGiftConfReq) ProtoMessage()    {}
func (*BatchAddComposeGiftConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{34}
}
func (m *BatchAddComposeGiftConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddComposeGiftConfReq.Unmarshal(m, b)
}
func (m *BatchAddComposeGiftConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddComposeGiftConfReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddComposeGiftConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddComposeGiftConfReq.Merge(dst, src)
}
func (m *BatchAddComposeGiftConfReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddComposeGiftConfReq.Size(m)
}
func (m *BatchAddComposeGiftConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddComposeGiftConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddComposeGiftConfReq proto.InternalMessageInfo

func (m *BatchAddComposeGiftConfReq) GetConfList() []*ComposeGiftConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type BatchAddComposeGiftConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddComposeGiftConfResp) Reset()         { *m = BatchAddComposeGiftConfResp{} }
func (m *BatchAddComposeGiftConfResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddComposeGiftConfResp) ProtoMessage()    {}
func (*BatchAddComposeGiftConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{35}
}
func (m *BatchAddComposeGiftConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddComposeGiftConfResp.Unmarshal(m, b)
}
func (m *BatchAddComposeGiftConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddComposeGiftConfResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddComposeGiftConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddComposeGiftConfResp.Merge(dst, src)
}
func (m *BatchAddComposeGiftConfResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddComposeGiftConfResp.Size(m)
}
func (m *BatchAddComposeGiftConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddComposeGiftConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddComposeGiftConfResp proto.InternalMessageInfo

// 新增合成礼物配置
type UpdateComposeGiftConfReq struct {
	Conf                 *ComposeGiftConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UpdateComposeGiftConfReq) Reset()         { *m = UpdateComposeGiftConfReq{} }
func (m *UpdateComposeGiftConfReq) String() string { return proto.CompactTextString(m) }
func (*UpdateComposeGiftConfReq) ProtoMessage()    {}
func (*UpdateComposeGiftConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{36}
}
func (m *UpdateComposeGiftConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateComposeGiftConfReq.Unmarshal(m, b)
}
func (m *UpdateComposeGiftConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateComposeGiftConfReq.Marshal(b, m, deterministic)
}
func (dst *UpdateComposeGiftConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateComposeGiftConfReq.Merge(dst, src)
}
func (m *UpdateComposeGiftConfReq) XXX_Size() int {
	return xxx_messageInfo_UpdateComposeGiftConfReq.Size(m)
}
func (m *UpdateComposeGiftConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateComposeGiftConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateComposeGiftConfReq proto.InternalMessageInfo

func (m *UpdateComposeGiftConfReq) GetConf() *ComposeGiftConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type UpdateComposeGiftConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateComposeGiftConfResp) Reset()         { *m = UpdateComposeGiftConfResp{} }
func (m *UpdateComposeGiftConfResp) String() string { return proto.CompactTextString(m) }
func (*UpdateComposeGiftConfResp) ProtoMessage()    {}
func (*UpdateComposeGiftConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{37}
}
func (m *UpdateComposeGiftConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateComposeGiftConfResp.Unmarshal(m, b)
}
func (m *UpdateComposeGiftConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateComposeGiftConfResp.Marshal(b, m, deterministic)
}
func (dst *UpdateComposeGiftConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateComposeGiftConfResp.Merge(dst, src)
}
func (m *UpdateComposeGiftConfResp) XXX_Size() int {
	return xxx_messageInfo_UpdateComposeGiftConfResp.Size(m)
}
func (m *UpdateComposeGiftConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateComposeGiftConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateComposeGiftConfResp proto.InternalMessageInfo

// 删除合成礼物配置
type DelComposeGiftConfReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelComposeGiftConfReq) Reset()         { *m = DelComposeGiftConfReq{} }
func (m *DelComposeGiftConfReq) String() string { return proto.CompactTextString(m) }
func (*DelComposeGiftConfReq) ProtoMessage()    {}
func (*DelComposeGiftConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{38}
}
func (m *DelComposeGiftConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelComposeGiftConfReq.Unmarshal(m, b)
}
func (m *DelComposeGiftConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelComposeGiftConfReq.Marshal(b, m, deterministic)
}
func (dst *DelComposeGiftConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelComposeGiftConfReq.Merge(dst, src)
}
func (m *DelComposeGiftConfReq) XXX_Size() int {
	return xxx_messageInfo_DelComposeGiftConfReq.Size(m)
}
func (m *DelComposeGiftConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelComposeGiftConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelComposeGiftConfReq proto.InternalMessageInfo

func (m *DelComposeGiftConfReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelComposeGiftConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelComposeGiftConfResp) Reset()         { *m = DelComposeGiftConfResp{} }
func (m *DelComposeGiftConfResp) String() string { return proto.CompactTextString(m) }
func (*DelComposeGiftConfResp) ProtoMessage()    {}
func (*DelComposeGiftConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{39}
}
func (m *DelComposeGiftConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelComposeGiftConfResp.Unmarshal(m, b)
}
func (m *DelComposeGiftConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelComposeGiftConfResp.Marshal(b, m, deterministic)
}
func (dst *DelComposeGiftConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelComposeGiftConfResp.Merge(dst, src)
}
func (m *DelComposeGiftConfResp) XXX_Size() int {
	return xxx_messageInfo_DelComposeGiftConfResp.Size(m)
}
func (m *DelComposeGiftConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelComposeGiftConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelComposeGiftConfResp proto.InternalMessageInfo

// 获取合成礼物配置列表
type GetAllComposeGiftConfReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllComposeGiftConfReq) Reset()         { *m = GetAllComposeGiftConfReq{} }
func (m *GetAllComposeGiftConfReq) String() string { return proto.CompactTextString(m) }
func (*GetAllComposeGiftConfReq) ProtoMessage()    {}
func (*GetAllComposeGiftConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{40}
}
func (m *GetAllComposeGiftConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllComposeGiftConfReq.Unmarshal(m, b)
}
func (m *GetAllComposeGiftConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllComposeGiftConfReq.Marshal(b, m, deterministic)
}
func (dst *GetAllComposeGiftConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllComposeGiftConfReq.Merge(dst, src)
}
func (m *GetAllComposeGiftConfReq) XXX_Size() int {
	return xxx_messageInfo_GetAllComposeGiftConfReq.Size(m)
}
func (m *GetAllComposeGiftConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllComposeGiftConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllComposeGiftConfReq proto.InternalMessageInfo

func (m *GetAllComposeGiftConfReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAllComposeGiftConfResp struct {
	ConfList             []*ComposeGiftConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetAllComposeGiftConfResp) Reset()         { *m = GetAllComposeGiftConfResp{} }
func (m *GetAllComposeGiftConfResp) String() string { return proto.CompactTextString(m) }
func (*GetAllComposeGiftConfResp) ProtoMessage()    {}
func (*GetAllComposeGiftConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{41}
}
func (m *GetAllComposeGiftConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllComposeGiftConfResp.Unmarshal(m, b)
}
func (m *GetAllComposeGiftConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllComposeGiftConfResp.Marshal(b, m, deterministic)
}
func (dst *GetAllComposeGiftConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllComposeGiftConfResp.Merge(dst, src)
}
func (m *GetAllComposeGiftConfResp) XXX_Size() int {
	return xxx_messageInfo_GetAllComposeGiftConfResp.Size(m)
}
func (m *GetAllComposeGiftConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllComposeGiftConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllComposeGiftConfResp proto.InternalMessageInfo

func (m *GetAllComposeGiftConfResp) GetConfList() []*ComposeGiftConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

// 获取合成礼物配置
type GetComposeGiftConfByIdReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetComposeGiftConfByIdReq) Reset()         { *m = GetComposeGiftConfByIdReq{} }
func (m *GetComposeGiftConfByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetComposeGiftConfByIdReq) ProtoMessage()    {}
func (*GetComposeGiftConfByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{42}
}
func (m *GetComposeGiftConfByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetComposeGiftConfByIdReq.Unmarshal(m, b)
}
func (m *GetComposeGiftConfByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetComposeGiftConfByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetComposeGiftConfByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetComposeGiftConfByIdReq.Merge(dst, src)
}
func (m *GetComposeGiftConfByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetComposeGiftConfByIdReq.Size(m)
}
func (m *GetComposeGiftConfByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetComposeGiftConfByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetComposeGiftConfByIdReq proto.InternalMessageInfo

func (m *GetComposeGiftConfByIdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetComposeGiftConfByIdReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetComposeGiftConfByIdResp struct {
	Conf                 *ComposeGiftConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetComposeGiftConfByIdResp) Reset()         { *m = GetComposeGiftConfByIdResp{} }
func (m *GetComposeGiftConfByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetComposeGiftConfByIdResp) ProtoMessage()    {}
func (*GetComposeGiftConfByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{43}
}
func (m *GetComposeGiftConfByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetComposeGiftConfByIdResp.Unmarshal(m, b)
}
func (m *GetComposeGiftConfByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetComposeGiftConfByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetComposeGiftConfByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetComposeGiftConfByIdResp.Merge(dst, src)
}
func (m *GetComposeGiftConfByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetComposeGiftConfByIdResp.Size(m)
}
func (m *GetComposeGiftConfByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetComposeGiftConfByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetComposeGiftConfByIdResp proto.InternalMessageInfo

func (m *GetComposeGiftConfByIdResp) GetConf() *ComposeGiftConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

// 合成记录
type ComposeLog struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	BgId                 uint32   `protobuf:"varint,3,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	Num                  uint32   `protobuf:"varint,4,opt,name=num,proto3" json:"num,omitempty"`
	Status               uint32   `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	GiftDesc             string   `protobuf:"bytes,6,opt,name=gift_desc,json=giftDesc,proto3" json:"gift_desc,omitempty"`
	GiftPrice            uint32   `protobuf:"varint,7,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price,omitempty"`
	GiftIconUrl          string   `protobuf:"bytes,8,opt,name=gift_icon_url,json=giftIconUrl,proto3" json:"gift_icon_url,omitempty"`
	MaterialUsedDesc     string   `protobuf:"bytes,9,opt,name=material_used_desc,json=materialUsedDesc,proto3" json:"material_used_desc,omitempty"`
	MaterialTotalPrice   uint32   `protobuf:"varint,10,opt,name=material_total_price,json=materialTotalPrice,proto3" json:"material_total_price,omitempty"`
	CreateAt             uint32   `protobuf:"varint,11,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ComposeLog) Reset()         { *m = ComposeLog{} }
func (m *ComposeLog) String() string { return proto.CompactTextString(m) }
func (*ComposeLog) ProtoMessage()    {}
func (*ComposeLog) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{44}
}
func (m *ComposeLog) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ComposeLog.Unmarshal(m, b)
}
func (m *ComposeLog) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ComposeLog.Marshal(b, m, deterministic)
}
func (dst *ComposeLog) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ComposeLog.Merge(dst, src)
}
func (m *ComposeLog) XXX_Size() int {
	return xxx_messageInfo_ComposeLog.Size(m)
}
func (m *ComposeLog) XXX_DiscardUnknown() {
	xxx_messageInfo_ComposeLog.DiscardUnknown(m)
}

var xxx_messageInfo_ComposeLog proto.InternalMessageInfo

func (m *ComposeLog) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *ComposeLog) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ComposeLog) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *ComposeLog) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *ComposeLog) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ComposeLog) GetGiftDesc() string {
	if m != nil {
		return m.GiftDesc
	}
	return ""
}

func (m *ComposeLog) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *ComposeLog) GetGiftIconUrl() string {
	if m != nil {
		return m.GiftIconUrl
	}
	return ""
}

func (m *ComposeLog) GetMaterialUsedDesc() string {
	if m != nil {
		return m.MaterialUsedDesc
	}
	return ""
}

func (m *ComposeLog) GetMaterialTotalPrice() uint32 {
	if m != nil {
		return m.MaterialTotalPrice
	}
	return 0
}

func (m *ComposeLog) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

// 获取用户合成记录
type GetUserComposeMonthLogsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	QueryMonthTime       uint32   `protobuf:"varint,2,opt,name=query_month_time,json=queryMonthTime,proto3" json:"query_month_time,omitempty"`
	Status               uint32   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	Begin                uint32   `protobuf:"varint,4,opt,name=begin,proto3" json:"begin,omitempty"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserComposeMonthLogsReq) Reset()         { *m = GetUserComposeMonthLogsReq{} }
func (m *GetUserComposeMonthLogsReq) String() string { return proto.CompactTextString(m) }
func (*GetUserComposeMonthLogsReq) ProtoMessage()    {}
func (*GetUserComposeMonthLogsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{45}
}
func (m *GetUserComposeMonthLogsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserComposeMonthLogsReq.Unmarshal(m, b)
}
func (m *GetUserComposeMonthLogsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserComposeMonthLogsReq.Marshal(b, m, deterministic)
}
func (dst *GetUserComposeMonthLogsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserComposeMonthLogsReq.Merge(dst, src)
}
func (m *GetUserComposeMonthLogsReq) XXX_Size() int {
	return xxx_messageInfo_GetUserComposeMonthLogsReq.Size(m)
}
func (m *GetUserComposeMonthLogsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserComposeMonthLogsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserComposeMonthLogsReq proto.InternalMessageInfo

func (m *GetUserComposeMonthLogsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserComposeMonthLogsReq) GetQueryMonthTime() uint32 {
	if m != nil {
		return m.QueryMonthTime
	}
	return 0
}

func (m *GetUserComposeMonthLogsReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetUserComposeMonthLogsReq) GetBegin() uint32 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *GetUserComposeMonthLogsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetUserComposeMonthLogsResp struct {
	Logs                 []*ComposeLog `protobuf:"bytes,1,rep,name=logs,proto3" json:"logs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserComposeMonthLogsResp) Reset()         { *m = GetUserComposeMonthLogsResp{} }
func (m *GetUserComposeMonthLogsResp) String() string { return proto.CompactTextString(m) }
func (*GetUserComposeMonthLogsResp) ProtoMessage()    {}
func (*GetUserComposeMonthLogsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{46}
}
func (m *GetUserComposeMonthLogsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserComposeMonthLogsResp.Unmarshal(m, b)
}
func (m *GetUserComposeMonthLogsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserComposeMonthLogsResp.Marshal(b, m, deterministic)
}
func (dst *GetUserComposeMonthLogsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserComposeMonthLogsResp.Merge(dst, src)
}
func (m *GetUserComposeMonthLogsResp) XXX_Size() int {
	return xxx_messageInfo_GetUserComposeMonthLogsResp.Size(m)
}
func (m *GetUserComposeMonthLogsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserComposeMonthLogsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserComposeMonthLogsResp proto.InternalMessageInfo

func (m *GetUserComposeMonthLogsResp) GetLogs() []*ComposeLog {
	if m != nil {
		return m.Logs
	}
	return nil
}

type GetUserComposeMonthLogsCntReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	QueryMonthTime       uint32   `protobuf:"varint,2,opt,name=query_month_time,json=queryMonthTime,proto3" json:"query_month_time,omitempty"`
	Status               uint32   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserComposeMonthLogsCntReq) Reset()         { *m = GetUserComposeMonthLogsCntReq{} }
func (m *GetUserComposeMonthLogsCntReq) String() string { return proto.CompactTextString(m) }
func (*GetUserComposeMonthLogsCntReq) ProtoMessage()    {}
func (*GetUserComposeMonthLogsCntReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{47}
}
func (m *GetUserComposeMonthLogsCntReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserComposeMonthLogsCntReq.Unmarshal(m, b)
}
func (m *GetUserComposeMonthLogsCntReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserComposeMonthLogsCntReq.Marshal(b, m, deterministic)
}
func (dst *GetUserComposeMonthLogsCntReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserComposeMonthLogsCntReq.Merge(dst, src)
}
func (m *GetUserComposeMonthLogsCntReq) XXX_Size() int {
	return xxx_messageInfo_GetUserComposeMonthLogsCntReq.Size(m)
}
func (m *GetUserComposeMonthLogsCntReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserComposeMonthLogsCntReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserComposeMonthLogsCntReq proto.InternalMessageInfo

func (m *GetUserComposeMonthLogsCntReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserComposeMonthLogsCntReq) GetQueryMonthTime() uint32 {
	if m != nil {
		return m.QueryMonthTime
	}
	return 0
}

func (m *GetUserComposeMonthLogsCntReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type GetUserComposeMonthLogsCntResp struct {
	Cnt                  uint32   `protobuf:"varint,1,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserComposeMonthLogsCntResp) Reset()         { *m = GetUserComposeMonthLogsCntResp{} }
func (m *GetUserComposeMonthLogsCntResp) String() string { return proto.CompactTextString(m) }
func (*GetUserComposeMonthLogsCntResp) ProtoMessage()    {}
func (*GetUserComposeMonthLogsCntResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{48}
}
func (m *GetUserComposeMonthLogsCntResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserComposeMonthLogsCntResp.Unmarshal(m, b)
}
func (m *GetUserComposeMonthLogsCntResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserComposeMonthLogsCntResp.Marshal(b, m, deterministic)
}
func (dst *GetUserComposeMonthLogsCntResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserComposeMonthLogsCntResp.Merge(dst, src)
}
func (m *GetUserComposeMonthLogsCntResp) XXX_Size() int {
	return xxx_messageInfo_GetUserComposeMonthLogsCntResp.Size(m)
}
func (m *GetUserComposeMonthLogsCntResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserComposeMonthLogsCntResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserComposeMonthLogsCntResp proto.InternalMessageInfo

func (m *GetUserComposeMonthLogsCntResp) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type ComposeGiftInfo struct {
	BgId                 uint32   `protobuf:"varint,1,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	GiftId               uint32   `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftName             string   `protobuf:"bytes,3,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	Price                uint32   `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	GiftIconUrl          string   `protobuf:"bytes,5,opt,name=gift_icon_url,json=giftIconUrl,proto3" json:"gift_icon_url,omitempty"`
	Num                  uint32   `protobuf:"varint,6,opt,name=num,proto3" json:"num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ComposeGiftInfo) Reset()         { *m = ComposeGiftInfo{} }
func (m *ComposeGiftInfo) String() string { return proto.CompactTextString(m) }
func (*ComposeGiftInfo) ProtoMessage()    {}
func (*ComposeGiftInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{49}
}
func (m *ComposeGiftInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ComposeGiftInfo.Unmarshal(m, b)
}
func (m *ComposeGiftInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ComposeGiftInfo.Marshal(b, m, deterministic)
}
func (dst *ComposeGiftInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ComposeGiftInfo.Merge(dst, src)
}
func (m *ComposeGiftInfo) XXX_Size() int {
	return xxx_messageInfo_ComposeGiftInfo.Size(m)
}
func (m *ComposeGiftInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ComposeGiftInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ComposeGiftInfo proto.InternalMessageInfo

func (m *ComposeGiftInfo) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *ComposeGiftInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *ComposeGiftInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *ComposeGiftInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *ComposeGiftInfo) GetGiftIconUrl() string {
	if m != nil {
		return m.GiftIconUrl
	}
	return ""
}

func (m *ComposeGiftInfo) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

type MaterialUseInfo struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	Num                  uint32   `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
	GiftName             string   `protobuf:"bytes,3,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	Price                uint32   `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	UserItemId           uint32   `protobuf:"varint,5,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MaterialUseInfo) Reset()         { *m = MaterialUseInfo{} }
func (m *MaterialUseInfo) String() string { return proto.CompactTextString(m) }
func (*MaterialUseInfo) ProtoMessage()    {}
func (*MaterialUseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{50}
}
func (m *MaterialUseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaterialUseInfo.Unmarshal(m, b)
}
func (m *MaterialUseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaterialUseInfo.Marshal(b, m, deterministic)
}
func (dst *MaterialUseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaterialUseInfo.Merge(dst, src)
}
func (m *MaterialUseInfo) XXX_Size() int {
	return xxx_messageInfo_MaterialUseInfo.Size(m)
}
func (m *MaterialUseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MaterialUseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MaterialUseInfo proto.InternalMessageInfo

func (m *MaterialUseInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *MaterialUseInfo) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *MaterialUseInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *MaterialUseInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *MaterialUseInfo) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

// 礼物合成接口
type GiftComposeReq struct {
	Uid                  uint32             `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ComposeGiftInfo      *ComposeGiftInfo   `protobuf:"bytes,2,opt,name=compose_gift_info,json=composeGiftInfo,proto3" json:"compose_gift_info,omitempty"`
	MaterialList         []*MaterialUseInfo `protobuf:"bytes,3,rep,name=material_list,json=materialList,proto3" json:"material_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GiftComposeReq) Reset()         { *m = GiftComposeReq{} }
func (m *GiftComposeReq) String() string { return proto.CompactTextString(m) }
func (*GiftComposeReq) ProtoMessage()    {}
func (*GiftComposeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{51}
}
func (m *GiftComposeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiftComposeReq.Unmarshal(m, b)
}
func (m *GiftComposeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiftComposeReq.Marshal(b, m, deterministic)
}
func (dst *GiftComposeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiftComposeReq.Merge(dst, src)
}
func (m *GiftComposeReq) XXX_Size() int {
	return xxx_messageInfo_GiftComposeReq.Size(m)
}
func (m *GiftComposeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GiftComposeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GiftComposeReq proto.InternalMessageInfo

func (m *GiftComposeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GiftComposeReq) GetComposeGiftInfo() *ComposeGiftInfo {
	if m != nil {
		return m.ComposeGiftInfo
	}
	return nil
}

func (m *GiftComposeReq) GetMaterialList() []*MaterialUseInfo {
	if m != nil {
		return m.MaterialList
	}
	return nil
}

type GiftComposeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiftComposeResp) Reset()         { *m = GiftComposeResp{} }
func (m *GiftComposeResp) String() string { return proto.CompactTextString(m) }
func (*GiftComposeResp) ProtoMessage()    {}
func (*GiftComposeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{52}
}
func (m *GiftComposeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiftComposeResp.Unmarshal(m, b)
}
func (m *GiftComposeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiftComposeResp.Marshal(b, m, deterministic)
}
func (dst *GiftComposeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiftComposeResp.Merge(dst, src)
}
func (m *GiftComposeResp) XXX_Size() int {
	return xxx_messageInfo_GiftComposeResp.Size(m)
}
func (m *GiftComposeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GiftComposeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GiftComposeResp proto.InternalMessageInfo

// 合成 原料配置
type DarkComposeMaterialConf struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	GiftId               uint32   `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftName             string   `protobuf:"bytes,3,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	SortId               uint32   `protobuf:"varint,4,opt,name=sort_id,json=sortId,proto3" json:"sort_id,omitempty"`
	UpdateAt             uint32   `protobuf:"varint,5,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	GiftPrice            uint32   `protobuf:"varint,6,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price,omitempty"`
	ComposeType          uint32   `protobuf:"varint,7,opt,name=compose_type,json=composeType,proto3" json:"compose_type,omitempty"`
	ItemType             uint32   `protobuf:"varint,8,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DarkComposeMaterialConf) Reset()         { *m = DarkComposeMaterialConf{} }
func (m *DarkComposeMaterialConf) String() string { return proto.CompactTextString(m) }
func (*DarkComposeMaterialConf) ProtoMessage()    {}
func (*DarkComposeMaterialConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{53}
}
func (m *DarkComposeMaterialConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DarkComposeMaterialConf.Unmarshal(m, b)
}
func (m *DarkComposeMaterialConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DarkComposeMaterialConf.Marshal(b, m, deterministic)
}
func (dst *DarkComposeMaterialConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DarkComposeMaterialConf.Merge(dst, src)
}
func (m *DarkComposeMaterialConf) XXX_Size() int {
	return xxx_messageInfo_DarkComposeMaterialConf.Size(m)
}
func (m *DarkComposeMaterialConf) XXX_DiscardUnknown() {
	xxx_messageInfo_DarkComposeMaterialConf.DiscardUnknown(m)
}

var xxx_messageInfo_DarkComposeMaterialConf proto.InternalMessageInfo

func (m *DarkComposeMaterialConf) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DarkComposeMaterialConf) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *DarkComposeMaterialConf) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *DarkComposeMaterialConf) GetSortId() uint32 {
	if m != nil {
		return m.SortId
	}
	return 0
}

func (m *DarkComposeMaterialConf) GetUpdateAt() uint32 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

func (m *DarkComposeMaterialConf) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *DarkComposeMaterialConf) GetComposeType() uint32 {
	if m != nil {
		return m.ComposeType
	}
	return 0
}

func (m *DarkComposeMaterialConf) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

// 合成 礼物配置
type DarkComposeGiftConf struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	BgId                 uint32   `protobuf:"varint,2,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	GiftId               uint32   `protobuf:"varint,3,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftType             uint32   `protobuf:"varint,4,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	SortId               uint32   `protobuf:"varint,5,opt,name=sort_id,json=sortId,proto3" json:"sort_id,omitempty"`
	UpTime               uint32   `protobuf:"varint,6,opt,name=up_time,json=upTime,proto3" json:"up_time,omitempty"`
	DownTime             uint32   `protobuf:"varint,7,opt,name=down_time,json=downTime,proto3" json:"down_time,omitempty"`
	UpdateAt             uint32   `protobuf:"varint,8,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	ComposeType          uint32   `protobuf:"varint,9,opt,name=compose_type,json=composeType,proto3" json:"compose_type,omitempty"`
	MaterialList         []uint32 `protobuf:"varint,10,rep,packed,name=material_list,json=materialList,proto3" json:"material_list,omitempty"`
	IsGoddess            bool     `protobuf:"varint,11,opt,name=is_goddess,json=isGoddess,proto3" json:"is_goddess,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DarkComposeGiftConf) Reset()         { *m = DarkComposeGiftConf{} }
func (m *DarkComposeGiftConf) String() string { return proto.CompactTextString(m) }
func (*DarkComposeGiftConf) ProtoMessage()    {}
func (*DarkComposeGiftConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{54}
}
func (m *DarkComposeGiftConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DarkComposeGiftConf.Unmarshal(m, b)
}
func (m *DarkComposeGiftConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DarkComposeGiftConf.Marshal(b, m, deterministic)
}
func (dst *DarkComposeGiftConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DarkComposeGiftConf.Merge(dst, src)
}
func (m *DarkComposeGiftConf) XXX_Size() int {
	return xxx_messageInfo_DarkComposeGiftConf.Size(m)
}
func (m *DarkComposeGiftConf) XXX_DiscardUnknown() {
	xxx_messageInfo_DarkComposeGiftConf.DiscardUnknown(m)
}

var xxx_messageInfo_DarkComposeGiftConf proto.InternalMessageInfo

func (m *DarkComposeGiftConf) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DarkComposeGiftConf) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *DarkComposeGiftConf) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *DarkComposeGiftConf) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *DarkComposeGiftConf) GetSortId() uint32 {
	if m != nil {
		return m.SortId
	}
	return 0
}

func (m *DarkComposeGiftConf) GetUpTime() uint32 {
	if m != nil {
		return m.UpTime
	}
	return 0
}

func (m *DarkComposeGiftConf) GetDownTime() uint32 {
	if m != nil {
		return m.DownTime
	}
	return 0
}

func (m *DarkComposeGiftConf) GetUpdateAt() uint32 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

func (m *DarkComposeGiftConf) GetComposeType() uint32 {
	if m != nil {
		return m.ComposeType
	}
	return 0
}

func (m *DarkComposeGiftConf) GetMaterialList() []uint32 {
	if m != nil {
		return m.MaterialList
	}
	return nil
}

func (m *DarkComposeGiftConf) GetIsGoddess() bool {
	if m != nil {
		return m.IsGoddess
	}
	return false
}

// 新增合成礼物配置
type AddDarkComposeGiftConfReq struct {
	Conf                 *DarkComposeGiftConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	MaterialsList        []uint32             `protobuf:"varint,2,rep,packed,name=materials_list,json=materialsList,proto3" json:"materials_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *AddDarkComposeGiftConfReq) Reset()         { *m = AddDarkComposeGiftConfReq{} }
func (m *AddDarkComposeGiftConfReq) String() string { return proto.CompactTextString(m) }
func (*AddDarkComposeGiftConfReq) ProtoMessage()    {}
func (*AddDarkComposeGiftConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{55}
}
func (m *AddDarkComposeGiftConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddDarkComposeGiftConfReq.Unmarshal(m, b)
}
func (m *AddDarkComposeGiftConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddDarkComposeGiftConfReq.Marshal(b, m, deterministic)
}
func (dst *AddDarkComposeGiftConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddDarkComposeGiftConfReq.Merge(dst, src)
}
func (m *AddDarkComposeGiftConfReq) XXX_Size() int {
	return xxx_messageInfo_AddDarkComposeGiftConfReq.Size(m)
}
func (m *AddDarkComposeGiftConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddDarkComposeGiftConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddDarkComposeGiftConfReq proto.InternalMessageInfo

func (m *AddDarkComposeGiftConfReq) GetConf() *DarkComposeGiftConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

func (m *AddDarkComposeGiftConfReq) GetMaterialsList() []uint32 {
	if m != nil {
		return m.MaterialsList
	}
	return nil
}

type AddDarkComposeGiftConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddDarkComposeGiftConfResp) Reset()         { *m = AddDarkComposeGiftConfResp{} }
func (m *AddDarkComposeGiftConfResp) String() string { return proto.CompactTextString(m) }
func (*AddDarkComposeGiftConfResp) ProtoMessage()    {}
func (*AddDarkComposeGiftConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{56}
}
func (m *AddDarkComposeGiftConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddDarkComposeGiftConfResp.Unmarshal(m, b)
}
func (m *AddDarkComposeGiftConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddDarkComposeGiftConfResp.Marshal(b, m, deterministic)
}
func (dst *AddDarkComposeGiftConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddDarkComposeGiftConfResp.Merge(dst, src)
}
func (m *AddDarkComposeGiftConfResp) XXX_Size() int {
	return xxx_messageInfo_AddDarkComposeGiftConfResp.Size(m)
}
func (m *AddDarkComposeGiftConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddDarkComposeGiftConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddDarkComposeGiftConfResp proto.InternalMessageInfo

// 删除合成礼物配置
type DelDarkComposeGiftConfReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelDarkComposeGiftConfReq) Reset()         { *m = DelDarkComposeGiftConfReq{} }
func (m *DelDarkComposeGiftConfReq) String() string { return proto.CompactTextString(m) }
func (*DelDarkComposeGiftConfReq) ProtoMessage()    {}
func (*DelDarkComposeGiftConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{57}
}
func (m *DelDarkComposeGiftConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelDarkComposeGiftConfReq.Unmarshal(m, b)
}
func (m *DelDarkComposeGiftConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelDarkComposeGiftConfReq.Marshal(b, m, deterministic)
}
func (dst *DelDarkComposeGiftConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelDarkComposeGiftConfReq.Merge(dst, src)
}
func (m *DelDarkComposeGiftConfReq) XXX_Size() int {
	return xxx_messageInfo_DelDarkComposeGiftConfReq.Size(m)
}
func (m *DelDarkComposeGiftConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelDarkComposeGiftConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelDarkComposeGiftConfReq proto.InternalMessageInfo

func (m *DelDarkComposeGiftConfReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelDarkComposeGiftConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelDarkComposeGiftConfResp) Reset()         { *m = DelDarkComposeGiftConfResp{} }
func (m *DelDarkComposeGiftConfResp) String() string { return proto.CompactTextString(m) }
func (*DelDarkComposeGiftConfResp) ProtoMessage()    {}
func (*DelDarkComposeGiftConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{58}
}
func (m *DelDarkComposeGiftConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelDarkComposeGiftConfResp.Unmarshal(m, b)
}
func (m *DelDarkComposeGiftConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelDarkComposeGiftConfResp.Marshal(b, m, deterministic)
}
func (dst *DelDarkComposeGiftConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelDarkComposeGiftConfResp.Merge(dst, src)
}
func (m *DelDarkComposeGiftConfResp) XXX_Size() int {
	return xxx_messageInfo_DelDarkComposeGiftConfResp.Size(m)
}
func (m *DelDarkComposeGiftConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelDarkComposeGiftConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelDarkComposeGiftConfResp proto.InternalMessageInfo

// 获取合成礼物配置列表
type GetAllDarkComposeGiftConfReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllDarkComposeGiftConfReq) Reset()         { *m = GetAllDarkComposeGiftConfReq{} }
func (m *GetAllDarkComposeGiftConfReq) String() string { return proto.CompactTextString(m) }
func (*GetAllDarkComposeGiftConfReq) ProtoMessage()    {}
func (*GetAllDarkComposeGiftConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{59}
}
func (m *GetAllDarkComposeGiftConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllDarkComposeGiftConfReq.Unmarshal(m, b)
}
func (m *GetAllDarkComposeGiftConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllDarkComposeGiftConfReq.Marshal(b, m, deterministic)
}
func (dst *GetAllDarkComposeGiftConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllDarkComposeGiftConfReq.Merge(dst, src)
}
func (m *GetAllDarkComposeGiftConfReq) XXX_Size() int {
	return xxx_messageInfo_GetAllDarkComposeGiftConfReq.Size(m)
}
func (m *GetAllDarkComposeGiftConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllDarkComposeGiftConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllDarkComposeGiftConfReq proto.InternalMessageInfo

func (m *GetAllDarkComposeGiftConfReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAllDarkComposeGiftConfResp struct {
	ConfList             []*DarkComposeGiftConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetAllDarkComposeGiftConfResp) Reset()         { *m = GetAllDarkComposeGiftConfResp{} }
func (m *GetAllDarkComposeGiftConfResp) String() string { return proto.CompactTextString(m) }
func (*GetAllDarkComposeGiftConfResp) ProtoMessage()    {}
func (*GetAllDarkComposeGiftConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{60}
}
func (m *GetAllDarkComposeGiftConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllDarkComposeGiftConfResp.Unmarshal(m, b)
}
func (m *GetAllDarkComposeGiftConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllDarkComposeGiftConfResp.Marshal(b, m, deterministic)
}
func (dst *GetAllDarkComposeGiftConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllDarkComposeGiftConfResp.Merge(dst, src)
}
func (m *GetAllDarkComposeGiftConfResp) XXX_Size() int {
	return xxx_messageInfo_GetAllDarkComposeGiftConfResp.Size(m)
}
func (m *GetAllDarkComposeGiftConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllDarkComposeGiftConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllDarkComposeGiftConfResp proto.InternalMessageInfo

func (m *GetAllDarkComposeGiftConfResp) GetConfList() []*DarkComposeGiftConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

// 新增原料配置
type AddDarkMaterialConfReq struct {
	Conf                 *DarkComposeMaterialConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *AddDarkMaterialConfReq) Reset()         { *m = AddDarkMaterialConfReq{} }
func (m *AddDarkMaterialConfReq) String() string { return proto.CompactTextString(m) }
func (*AddDarkMaterialConfReq) ProtoMessage()    {}
func (*AddDarkMaterialConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{61}
}
func (m *AddDarkMaterialConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddDarkMaterialConfReq.Unmarshal(m, b)
}
func (m *AddDarkMaterialConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddDarkMaterialConfReq.Marshal(b, m, deterministic)
}
func (dst *AddDarkMaterialConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddDarkMaterialConfReq.Merge(dst, src)
}
func (m *AddDarkMaterialConfReq) XXX_Size() int {
	return xxx_messageInfo_AddDarkMaterialConfReq.Size(m)
}
func (m *AddDarkMaterialConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddDarkMaterialConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddDarkMaterialConfReq proto.InternalMessageInfo

func (m *AddDarkMaterialConfReq) GetConf() *DarkComposeMaterialConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type AddDarkMaterialConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddDarkMaterialConfResp) Reset()         { *m = AddDarkMaterialConfResp{} }
func (m *AddDarkMaterialConfResp) String() string { return proto.CompactTextString(m) }
func (*AddDarkMaterialConfResp) ProtoMessage()    {}
func (*AddDarkMaterialConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{62}
}
func (m *AddDarkMaterialConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddDarkMaterialConfResp.Unmarshal(m, b)
}
func (m *AddDarkMaterialConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddDarkMaterialConfResp.Marshal(b, m, deterministic)
}
func (dst *AddDarkMaterialConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddDarkMaterialConfResp.Merge(dst, src)
}
func (m *AddDarkMaterialConfResp) XXX_Size() int {
	return xxx_messageInfo_AddDarkMaterialConfResp.Size(m)
}
func (m *AddDarkMaterialConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddDarkMaterialConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddDarkMaterialConfResp proto.InternalMessageInfo

// 删除原料配置
type DelDarkMaterialConfReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelDarkMaterialConfReq) Reset()         { *m = DelDarkMaterialConfReq{} }
func (m *DelDarkMaterialConfReq) String() string { return proto.CompactTextString(m) }
func (*DelDarkMaterialConfReq) ProtoMessage()    {}
func (*DelDarkMaterialConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{63}
}
func (m *DelDarkMaterialConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelDarkMaterialConfReq.Unmarshal(m, b)
}
func (m *DelDarkMaterialConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelDarkMaterialConfReq.Marshal(b, m, deterministic)
}
func (dst *DelDarkMaterialConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelDarkMaterialConfReq.Merge(dst, src)
}
func (m *DelDarkMaterialConfReq) XXX_Size() int {
	return xxx_messageInfo_DelDarkMaterialConfReq.Size(m)
}
func (m *DelDarkMaterialConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelDarkMaterialConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelDarkMaterialConfReq proto.InternalMessageInfo

func (m *DelDarkMaterialConfReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelDarkMaterialConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelDarkMaterialConfResp) Reset()         { *m = DelDarkMaterialConfResp{} }
func (m *DelDarkMaterialConfResp) String() string { return proto.CompactTextString(m) }
func (*DelDarkMaterialConfResp) ProtoMessage()    {}
func (*DelDarkMaterialConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{64}
}
func (m *DelDarkMaterialConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelDarkMaterialConfResp.Unmarshal(m, b)
}
func (m *DelDarkMaterialConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelDarkMaterialConfResp.Marshal(b, m, deterministic)
}
func (dst *DelDarkMaterialConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelDarkMaterialConfResp.Merge(dst, src)
}
func (m *DelDarkMaterialConfResp) XXX_Size() int {
	return xxx_messageInfo_DelDarkMaterialConfResp.Size(m)
}
func (m *DelDarkMaterialConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelDarkMaterialConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelDarkMaterialConfResp proto.InternalMessageInfo

// 获取原料配置列表
type GetAllDarkMaterialConfReq struct {
	ComposeType          uint32   `protobuf:"varint,1,opt,name=compose_type,json=composeType,proto3" json:"compose_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllDarkMaterialConfReq) Reset()         { *m = GetAllDarkMaterialConfReq{} }
func (m *GetAllDarkMaterialConfReq) String() string { return proto.CompactTextString(m) }
func (*GetAllDarkMaterialConfReq) ProtoMessage()    {}
func (*GetAllDarkMaterialConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{65}
}
func (m *GetAllDarkMaterialConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllDarkMaterialConfReq.Unmarshal(m, b)
}
func (m *GetAllDarkMaterialConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllDarkMaterialConfReq.Marshal(b, m, deterministic)
}
func (dst *GetAllDarkMaterialConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllDarkMaterialConfReq.Merge(dst, src)
}
func (m *GetAllDarkMaterialConfReq) XXX_Size() int {
	return xxx_messageInfo_GetAllDarkMaterialConfReq.Size(m)
}
func (m *GetAllDarkMaterialConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllDarkMaterialConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllDarkMaterialConfReq proto.InternalMessageInfo

func (m *GetAllDarkMaterialConfReq) GetComposeType() uint32 {
	if m != nil {
		return m.ComposeType
	}
	return 0
}

type GetAllDarkMaterialConfResp struct {
	ConfList             []*DarkComposeMaterialConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetAllDarkMaterialConfResp) Reset()         { *m = GetAllDarkMaterialConfResp{} }
func (m *GetAllDarkMaterialConfResp) String() string { return proto.CompactTextString(m) }
func (*GetAllDarkMaterialConfResp) ProtoMessage()    {}
func (*GetAllDarkMaterialConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{66}
}
func (m *GetAllDarkMaterialConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllDarkMaterialConfResp.Unmarshal(m, b)
}
func (m *GetAllDarkMaterialConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllDarkMaterialConfResp.Marshal(b, m, deterministic)
}
func (dst *GetAllDarkMaterialConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllDarkMaterialConfResp.Merge(dst, src)
}
func (m *GetAllDarkMaterialConfResp) XXX_Size() int {
	return xxx_messageInfo_GetAllDarkMaterialConfResp.Size(m)
}
func (m *GetAllDarkMaterialConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllDarkMaterialConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllDarkMaterialConfResp proto.InternalMessageInfo

func (m *GetAllDarkMaterialConfResp) GetConfList() []*DarkComposeMaterialConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

// =============================================运营后台黑暗礼物相关接口 end =================//
// 获取合成礼物配置
type GetDarkComposeGiftConfByIdReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDarkComposeGiftConfByIdReq) Reset()         { *m = GetDarkComposeGiftConfByIdReq{} }
func (m *GetDarkComposeGiftConfByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetDarkComposeGiftConfByIdReq) ProtoMessage()    {}
func (*GetDarkComposeGiftConfByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{67}
}
func (m *GetDarkComposeGiftConfByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDarkComposeGiftConfByIdReq.Unmarshal(m, b)
}
func (m *GetDarkComposeGiftConfByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDarkComposeGiftConfByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetDarkComposeGiftConfByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDarkComposeGiftConfByIdReq.Merge(dst, src)
}
func (m *GetDarkComposeGiftConfByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetDarkComposeGiftConfByIdReq.Size(m)
}
func (m *GetDarkComposeGiftConfByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDarkComposeGiftConfByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDarkComposeGiftConfByIdReq proto.InternalMessageInfo

func (m *GetDarkComposeGiftConfByIdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetDarkComposeGiftConfByIdReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetDarkComposeGiftConfByIdResp struct {
	Conf                 *DarkComposeGiftConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetDarkComposeGiftConfByIdResp) Reset()         { *m = GetDarkComposeGiftConfByIdResp{} }
func (m *GetDarkComposeGiftConfByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetDarkComposeGiftConfByIdResp) ProtoMessage()    {}
func (*GetDarkComposeGiftConfByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{68}
}
func (m *GetDarkComposeGiftConfByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDarkComposeGiftConfByIdResp.Unmarshal(m, b)
}
func (m *GetDarkComposeGiftConfByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDarkComposeGiftConfByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetDarkComposeGiftConfByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDarkComposeGiftConfByIdResp.Merge(dst, src)
}
func (m *GetDarkComposeGiftConfByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetDarkComposeGiftConfByIdResp.Size(m)
}
func (m *GetDarkComposeGiftConfByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDarkComposeGiftConfByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDarkComposeGiftConfByIdResp proto.InternalMessageInfo

func (m *GetDarkComposeGiftConfByIdResp) GetConf() *DarkComposeGiftConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

// 获取指定合成礼物的原料配置列表
type GetDarkGift2MaterialsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ComposeId            uint32   `protobuf:"varint,2,opt,name=compose_id,json=composeId,proto3" json:"compose_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDarkGift2MaterialsReq) Reset()         { *m = GetDarkGift2MaterialsReq{} }
func (m *GetDarkGift2MaterialsReq) String() string { return proto.CompactTextString(m) }
func (*GetDarkGift2MaterialsReq) ProtoMessage()    {}
func (*GetDarkGift2MaterialsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{69}
}
func (m *GetDarkGift2MaterialsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDarkGift2MaterialsReq.Unmarshal(m, b)
}
func (m *GetDarkGift2MaterialsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDarkGift2MaterialsReq.Marshal(b, m, deterministic)
}
func (dst *GetDarkGift2MaterialsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDarkGift2MaterialsReq.Merge(dst, src)
}
func (m *GetDarkGift2MaterialsReq) XXX_Size() int {
	return xxx_messageInfo_GetDarkGift2MaterialsReq.Size(m)
}
func (m *GetDarkGift2MaterialsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDarkGift2MaterialsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDarkGift2MaterialsReq proto.InternalMessageInfo

func (m *GetDarkGift2MaterialsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetDarkGift2MaterialsReq) GetComposeId() uint32 {
	if m != nil {
		return m.ComposeId
	}
	return 0
}

type GetDarkGift2MaterialsResp struct {
	ConfList             []*DarkComposeMaterialConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetDarkGift2MaterialsResp) Reset()         { *m = GetDarkGift2MaterialsResp{} }
func (m *GetDarkGift2MaterialsResp) String() string { return proto.CompactTextString(m) }
func (*GetDarkGift2MaterialsResp) ProtoMessage()    {}
func (*GetDarkGift2MaterialsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{70}
}
func (m *GetDarkGift2MaterialsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDarkGift2MaterialsResp.Unmarshal(m, b)
}
func (m *GetDarkGift2MaterialsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDarkGift2MaterialsResp.Marshal(b, m, deterministic)
}
func (dst *GetDarkGift2MaterialsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDarkGift2MaterialsResp.Merge(dst, src)
}
func (m *GetDarkGift2MaterialsResp) XXX_Size() int {
	return xxx_messageInfo_GetDarkGift2MaterialsResp.Size(m)
}
func (m *GetDarkGift2MaterialsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDarkGift2MaterialsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDarkGift2MaterialsResp proto.InternalMessageInfo

func (m *GetDarkGift2MaterialsResp) GetConfList() []*DarkComposeMaterialConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type DarkComposeGiftInfo struct {
	ComposeId            uint32   `protobuf:"varint,1,opt,name=compose_id,json=composeId,proto3" json:"compose_id,omitempty"`
	BgId                 uint32   `protobuf:"varint,2,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	GiftId               uint32   `protobuf:"varint,3,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftName             string   `protobuf:"bytes,4,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	Price                uint32   `protobuf:"varint,5,opt,name=price,proto3" json:"price,omitempty"`
	GiftIconUrl          string   `protobuf:"bytes,6,opt,name=gift_icon_url,json=giftIconUrl,proto3" json:"gift_icon_url,omitempty"`
	Num                  uint32   `protobuf:"varint,7,opt,name=num,proto3" json:"num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DarkComposeGiftInfo) Reset()         { *m = DarkComposeGiftInfo{} }
func (m *DarkComposeGiftInfo) String() string { return proto.CompactTextString(m) }
func (*DarkComposeGiftInfo) ProtoMessage()    {}
func (*DarkComposeGiftInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{71}
}
func (m *DarkComposeGiftInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DarkComposeGiftInfo.Unmarshal(m, b)
}
func (m *DarkComposeGiftInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DarkComposeGiftInfo.Marshal(b, m, deterministic)
}
func (dst *DarkComposeGiftInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DarkComposeGiftInfo.Merge(dst, src)
}
func (m *DarkComposeGiftInfo) XXX_Size() int {
	return xxx_messageInfo_DarkComposeGiftInfo.Size(m)
}
func (m *DarkComposeGiftInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DarkComposeGiftInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DarkComposeGiftInfo proto.InternalMessageInfo

func (m *DarkComposeGiftInfo) GetComposeId() uint32 {
	if m != nil {
		return m.ComposeId
	}
	return 0
}

func (m *DarkComposeGiftInfo) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *DarkComposeGiftInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *DarkComposeGiftInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *DarkComposeGiftInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *DarkComposeGiftInfo) GetGiftIconUrl() string {
	if m != nil {
		return m.GiftIconUrl
	}
	return ""
}

func (m *DarkComposeGiftInfo) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

type DarkMaterialUseInfo struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	Num                  uint32   `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
	GiftName             string   `protobuf:"bytes,3,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	Price                uint32   `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	UserItemId           uint32   `protobuf:"varint,5,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
	GiftType             uint32   `protobuf:"varint,6,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DarkMaterialUseInfo) Reset()         { *m = DarkMaterialUseInfo{} }
func (m *DarkMaterialUseInfo) String() string { return proto.CompactTextString(m) }
func (*DarkMaterialUseInfo) ProtoMessage()    {}
func (*DarkMaterialUseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{72}
}
func (m *DarkMaterialUseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DarkMaterialUseInfo.Unmarshal(m, b)
}
func (m *DarkMaterialUseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DarkMaterialUseInfo.Marshal(b, m, deterministic)
}
func (dst *DarkMaterialUseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DarkMaterialUseInfo.Merge(dst, src)
}
func (m *DarkMaterialUseInfo) XXX_Size() int {
	return xxx_messageInfo_DarkMaterialUseInfo.Size(m)
}
func (m *DarkMaterialUseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DarkMaterialUseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DarkMaterialUseInfo proto.InternalMessageInfo

func (m *DarkMaterialUseInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *DarkMaterialUseInfo) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *DarkMaterialUseInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *DarkMaterialUseInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *DarkMaterialUseInfo) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *DarkMaterialUseInfo) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

type DarkGiftComposeReq struct {
	Uid                  uint32                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ComposeId            uint32                 `protobuf:"varint,2,opt,name=compose_id,json=composeId,proto3" json:"compose_id,omitempty"`
	ComposeGiftInfo      *DarkComposeGiftInfo   `protobuf:"bytes,3,opt,name=compose_gift_info,json=composeGiftInfo,proto3" json:"compose_gift_info,omitempty"`
	MaterialList         []*DarkMaterialUseInfo `protobuf:"bytes,4,rep,name=material_list,json=materialList,proto3" json:"material_list,omitempty"`
	MarketId             uint32                 `protobuf:"varint,5,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *DarkGiftComposeReq) Reset()         { *m = DarkGiftComposeReq{} }
func (m *DarkGiftComposeReq) String() string { return proto.CompactTextString(m) }
func (*DarkGiftComposeReq) ProtoMessage()    {}
func (*DarkGiftComposeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{73}
}
func (m *DarkGiftComposeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DarkGiftComposeReq.Unmarshal(m, b)
}
func (m *DarkGiftComposeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DarkGiftComposeReq.Marshal(b, m, deterministic)
}
func (dst *DarkGiftComposeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DarkGiftComposeReq.Merge(dst, src)
}
func (m *DarkGiftComposeReq) XXX_Size() int {
	return xxx_messageInfo_DarkGiftComposeReq.Size(m)
}
func (m *DarkGiftComposeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DarkGiftComposeReq.DiscardUnknown(m)
}

var xxx_messageInfo_DarkGiftComposeReq proto.InternalMessageInfo

func (m *DarkGiftComposeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DarkGiftComposeReq) GetComposeId() uint32 {
	if m != nil {
		return m.ComposeId
	}
	return 0
}

func (m *DarkGiftComposeReq) GetComposeGiftInfo() *DarkComposeGiftInfo {
	if m != nil {
		return m.ComposeGiftInfo
	}
	return nil
}

func (m *DarkGiftComposeReq) GetMaterialList() []*DarkMaterialUseInfo {
	if m != nil {
		return m.MaterialList
	}
	return nil
}

func (m *DarkGiftComposeReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type DarkGiftComposeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DarkGiftComposeResp) Reset()         { *m = DarkGiftComposeResp{} }
func (m *DarkGiftComposeResp) String() string { return proto.CompactTextString(m) }
func (*DarkGiftComposeResp) ProtoMessage()    {}
func (*DarkGiftComposeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{74}
}
func (m *DarkGiftComposeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DarkGiftComposeResp.Unmarshal(m, b)
}
func (m *DarkGiftComposeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DarkGiftComposeResp.Marshal(b, m, deterministic)
}
func (dst *DarkGiftComposeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DarkGiftComposeResp.Merge(dst, src)
}
func (m *DarkGiftComposeResp) XXX_Size() int {
	return xxx_messageInfo_DarkGiftComposeResp.Size(m)
}
func (m *DarkGiftComposeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DarkGiftComposeResp.DiscardUnknown(m)
}

var xxx_messageInfo_DarkGiftComposeResp proto.InternalMessageInfo

// 获取用户合成记录
type GetUserDarkComposeMonthLogsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	QueryMonthTime       uint32   `protobuf:"varint,2,opt,name=query_month_time,json=queryMonthTime,proto3" json:"query_month_time,omitempty"`
	Status               uint32   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	Begin                uint32   `protobuf:"varint,4,opt,name=begin,proto3" json:"begin,omitempty"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserDarkComposeMonthLogsReq) Reset()         { *m = GetUserDarkComposeMonthLogsReq{} }
func (m *GetUserDarkComposeMonthLogsReq) String() string { return proto.CompactTextString(m) }
func (*GetUserDarkComposeMonthLogsReq) ProtoMessage()    {}
func (*GetUserDarkComposeMonthLogsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{75}
}
func (m *GetUserDarkComposeMonthLogsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDarkComposeMonthLogsReq.Unmarshal(m, b)
}
func (m *GetUserDarkComposeMonthLogsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDarkComposeMonthLogsReq.Marshal(b, m, deterministic)
}
func (dst *GetUserDarkComposeMonthLogsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDarkComposeMonthLogsReq.Merge(dst, src)
}
func (m *GetUserDarkComposeMonthLogsReq) XXX_Size() int {
	return xxx_messageInfo_GetUserDarkComposeMonthLogsReq.Size(m)
}
func (m *GetUserDarkComposeMonthLogsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDarkComposeMonthLogsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDarkComposeMonthLogsReq proto.InternalMessageInfo

func (m *GetUserDarkComposeMonthLogsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserDarkComposeMonthLogsReq) GetQueryMonthTime() uint32 {
	if m != nil {
		return m.QueryMonthTime
	}
	return 0
}

func (m *GetUserDarkComposeMonthLogsReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetUserDarkComposeMonthLogsReq) GetBegin() uint32 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *GetUserDarkComposeMonthLogsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetUserDarkComposeMonthLogsResp struct {
	Logs                 []*ComposeLog `protobuf:"bytes,1,rep,name=logs,proto3" json:"logs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserDarkComposeMonthLogsResp) Reset()         { *m = GetUserDarkComposeMonthLogsResp{} }
func (m *GetUserDarkComposeMonthLogsResp) String() string { return proto.CompactTextString(m) }
func (*GetUserDarkComposeMonthLogsResp) ProtoMessage()    {}
func (*GetUserDarkComposeMonthLogsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{76}
}
func (m *GetUserDarkComposeMonthLogsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDarkComposeMonthLogsResp.Unmarshal(m, b)
}
func (m *GetUserDarkComposeMonthLogsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDarkComposeMonthLogsResp.Marshal(b, m, deterministic)
}
func (dst *GetUserDarkComposeMonthLogsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDarkComposeMonthLogsResp.Merge(dst, src)
}
func (m *GetUserDarkComposeMonthLogsResp) XXX_Size() int {
	return xxx_messageInfo_GetUserDarkComposeMonthLogsResp.Size(m)
}
func (m *GetUserDarkComposeMonthLogsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDarkComposeMonthLogsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDarkComposeMonthLogsResp proto.InternalMessageInfo

func (m *GetUserDarkComposeMonthLogsResp) GetLogs() []*ComposeLog {
	if m != nil {
		return m.Logs
	}
	return nil
}

type GetUserDarkComposeMonthLogsCntReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	QueryMonthTime       uint32   `protobuf:"varint,2,opt,name=query_month_time,json=queryMonthTime,proto3" json:"query_month_time,omitempty"`
	Status               uint32   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserDarkComposeMonthLogsCntReq) Reset()         { *m = GetUserDarkComposeMonthLogsCntReq{} }
func (m *GetUserDarkComposeMonthLogsCntReq) String() string { return proto.CompactTextString(m) }
func (*GetUserDarkComposeMonthLogsCntReq) ProtoMessage()    {}
func (*GetUserDarkComposeMonthLogsCntReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{77}
}
func (m *GetUserDarkComposeMonthLogsCntReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDarkComposeMonthLogsCntReq.Unmarshal(m, b)
}
func (m *GetUserDarkComposeMonthLogsCntReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDarkComposeMonthLogsCntReq.Marshal(b, m, deterministic)
}
func (dst *GetUserDarkComposeMonthLogsCntReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDarkComposeMonthLogsCntReq.Merge(dst, src)
}
func (m *GetUserDarkComposeMonthLogsCntReq) XXX_Size() int {
	return xxx_messageInfo_GetUserDarkComposeMonthLogsCntReq.Size(m)
}
func (m *GetUserDarkComposeMonthLogsCntReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDarkComposeMonthLogsCntReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDarkComposeMonthLogsCntReq proto.InternalMessageInfo

func (m *GetUserDarkComposeMonthLogsCntReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserDarkComposeMonthLogsCntReq) GetQueryMonthTime() uint32 {
	if m != nil {
		return m.QueryMonthTime
	}
	return 0
}

func (m *GetUserDarkComposeMonthLogsCntReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type GetUserDarkComposeMonthLogsCntResp struct {
	Cnt                  uint32   `protobuf:"varint,1,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserDarkComposeMonthLogsCntResp) Reset()         { *m = GetUserDarkComposeMonthLogsCntResp{} }
func (m *GetUserDarkComposeMonthLogsCntResp) String() string { return proto.CompactTextString(m) }
func (*GetUserDarkComposeMonthLogsCntResp) ProtoMessage()    {}
func (*GetUserDarkComposeMonthLogsCntResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{78}
}
func (m *GetUserDarkComposeMonthLogsCntResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDarkComposeMonthLogsCntResp.Unmarshal(m, b)
}
func (m *GetUserDarkComposeMonthLogsCntResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDarkComposeMonthLogsCntResp.Marshal(b, m, deterministic)
}
func (dst *GetUserDarkComposeMonthLogsCntResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDarkComposeMonthLogsCntResp.Merge(dst, src)
}
func (m *GetUserDarkComposeMonthLogsCntResp) XXX_Size() int {
	return xxx_messageInfo_GetUserDarkComposeMonthLogsCntResp.Size(m)
}
func (m *GetUserDarkComposeMonthLogsCntResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDarkComposeMonthLogsCntResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDarkComposeMonthLogsCntResp proto.InternalMessageInfo

func (m *GetUserDarkComposeMonthLogsCntResp) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

// 检查合成入口是否开启
type CheckGiftComposeEntryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckGiftComposeEntryReq) Reset()         { *m = CheckGiftComposeEntryReq{} }
func (m *CheckGiftComposeEntryReq) String() string { return proto.CompactTextString(m) }
func (*CheckGiftComposeEntryReq) ProtoMessage()    {}
func (*CheckGiftComposeEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{79}
}
func (m *CheckGiftComposeEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckGiftComposeEntryReq.Unmarshal(m, b)
}
func (m *CheckGiftComposeEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckGiftComposeEntryReq.Marshal(b, m, deterministic)
}
func (dst *CheckGiftComposeEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckGiftComposeEntryReq.Merge(dst, src)
}
func (m *CheckGiftComposeEntryReq) XXX_Size() int {
	return xxx_messageInfo_CheckGiftComposeEntryReq.Size(m)
}
func (m *CheckGiftComposeEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckGiftComposeEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckGiftComposeEntryReq proto.InternalMessageInfo

func (m *CheckGiftComposeEntryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckGiftComposeEntryResp struct {
	IsOpen               bool     `protobuf:"varint,1,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	MaterialsTolPrice    uint32   `protobuf:"varint,2,opt,name=materials_tol_price,json=materialsTolPrice,proto3" json:"materials_tol_price,omitempty"`
	PoolFullValue        uint32   `protobuf:"varint,3,opt,name=pool_full_value,json=poolFullValue,proto3" json:"pool_full_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckGiftComposeEntryResp) Reset()         { *m = CheckGiftComposeEntryResp{} }
func (m *CheckGiftComposeEntryResp) String() string { return proto.CompactTextString(m) }
func (*CheckGiftComposeEntryResp) ProtoMessage()    {}
func (*CheckGiftComposeEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_conversion_956ea4e0f4a993f4, []int{80}
}
func (m *CheckGiftComposeEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckGiftComposeEntryResp.Unmarshal(m, b)
}
func (m *CheckGiftComposeEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckGiftComposeEntryResp.Marshal(b, m, deterministic)
}
func (dst *CheckGiftComposeEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckGiftComposeEntryResp.Merge(dst, src)
}
func (m *CheckGiftComposeEntryResp) XXX_Size() int {
	return xxx_messageInfo_CheckGiftComposeEntryResp.Size(m)
}
func (m *CheckGiftComposeEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckGiftComposeEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckGiftComposeEntryResp proto.InternalMessageInfo

func (m *CheckGiftComposeEntryResp) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

func (m *CheckGiftComposeEntryResp) GetMaterialsTolPrice() uint32 {
	if m != nil {
		return m.MaterialsTolPrice
	}
	return 0
}

func (m *CheckGiftComposeEntryResp) GetPoolFullValue() uint32 {
	if m != nil {
		return m.PoolFullValue
	}
	return 0
}

func init() {
	proto.RegisterType((*ConversionLog)(nil), "conversion.ConversionLog")
	proto.RegisterType((*GetConversionLogsReq)(nil), "conversion.GetConversionLogsReq")
	proto.RegisterType((*GetConversionLogsResp)(nil), "conversion.GetConversionLogsResp")
	proto.RegisterType((*ConversionServerReq)(nil), "conversion.ConversionServerReq")
	proto.RegisterType((*ConversionServerResp)(nil), "conversion.ConversionServerResp")
	proto.RegisterType((*UserItemInfo)(nil), "conversion.UserItemInfo")
	proto.RegisterType((*ConversionDebris)(nil), "conversion.ConversionDebris")
	proto.RegisterType((*ConversionConfig)(nil), "conversion.ConversionConfig")
	proto.RegisterMapType((map[uint32]uint32)(nil), "conversion.ConversionConfig.Debrisid2NumEntry")
	proto.RegisterType((*ConversionDebrisReq)(nil), "conversion.ConversionDebrisReq")
	proto.RegisterType((*ConversionDebrisResp)(nil), "conversion.ConversionDebrisResp")
	proto.RegisterType((*AddConversionConfigReq)(nil), "conversion.AddConversionConfigReq")
	proto.RegisterType((*AddConversionConfigResp)(nil), "conversion.AddConversionConfigResp")
	proto.RegisterType((*SetConversionConfigsReq)(nil), "conversion.SetConversionConfigsReq")
	proto.RegisterType((*SetConversionConfigsResp)(nil), "conversion.SetConversionConfigsResp")
	proto.RegisterType((*DelConversionConfigReq)(nil), "conversion.DelConversionConfigReq")
	proto.RegisterType((*DelConversionConfigResp)(nil), "conversion.DelConversionConfigResp")
	proto.RegisterType((*DelConversionConfByIdsReq)(nil), "conversion.DelConversionConfByIdsReq")
	proto.RegisterType((*DelConversionConfByIdsResp)(nil), "conversion.DelConversionConfByIdsResp")
	proto.RegisterType((*GetAllConversionConfigReq)(nil), "conversion.GetAllConversionConfigReq")
	proto.RegisterType((*GetAllConversionConfigResp)(nil), "conversion.GetAllConversionConfigResp")
	proto.RegisterType((*GetFriendGiftReq)(nil), "conversion.GetFriendGiftReq")
	proto.RegisterType((*GetFriendGiftResp)(nil), "conversion.GetFriendGiftResp")
	proto.RegisterType((*ComposeMaterialConf)(nil), "conversion.ComposeMaterialConf")
	proto.RegisterType((*AddComposeMaterialConfReq)(nil), "conversion.AddComposeMaterialConfReq")
	proto.RegisterType((*AddComposeMaterialConfResp)(nil), "conversion.AddComposeMaterialConfResp")
	proto.RegisterType((*BatchAddComposeMaterialConfReq)(nil), "conversion.BatchAddComposeMaterialConfReq")
	proto.RegisterType((*BatchAddComposeMaterialConfResp)(nil), "conversion.BatchAddComposeMaterialConfResp")
	proto.RegisterType((*DelComposeMaterialConfReq)(nil), "conversion.DelComposeMaterialConfReq")
	proto.RegisterType((*DelComposeMaterialConfResp)(nil), "conversion.DelComposeMaterialConfResp")
	proto.RegisterType((*GetAllComposeMaterialConfReq)(nil), "conversion.GetAllComposeMaterialConfReq")
	proto.RegisterType((*GetAllComposeMaterialConfResp)(nil), "conversion.GetAllComposeMaterialConfResp")
	proto.RegisterType((*ComposeGiftConf)(nil), "conversion.ComposeGiftConf")
	proto.RegisterType((*AddComposeGiftConfReq)(nil), "conversion.AddComposeGiftConfReq")
	proto.RegisterType((*AddComposeGiftConfResp)(nil), "conversion.AddComposeGiftConfResp")
	proto.RegisterType((*BatchAddComposeGiftConfReq)(nil), "conversion.BatchAddComposeGiftConfReq")
	proto.RegisterType((*BatchAddComposeGiftConfResp)(nil), "conversion.BatchAddComposeGiftConfResp")
	proto.RegisterType((*UpdateComposeGiftConfReq)(nil), "conversion.UpdateComposeGiftConfReq")
	proto.RegisterType((*UpdateComposeGiftConfResp)(nil), "conversion.UpdateComposeGiftConfResp")
	proto.RegisterType((*DelComposeGiftConfReq)(nil), "conversion.DelComposeGiftConfReq")
	proto.RegisterType((*DelComposeGiftConfResp)(nil), "conversion.DelComposeGiftConfResp")
	proto.RegisterType((*GetAllComposeGiftConfReq)(nil), "conversion.GetAllComposeGiftConfReq")
	proto.RegisterType((*GetAllComposeGiftConfResp)(nil), "conversion.GetAllComposeGiftConfResp")
	proto.RegisterType((*GetComposeGiftConfByIdReq)(nil), "conversion.GetComposeGiftConfByIdReq")
	proto.RegisterType((*GetComposeGiftConfByIdResp)(nil), "conversion.GetComposeGiftConfByIdResp")
	proto.RegisterType((*ComposeLog)(nil), "conversion.ComposeLog")
	proto.RegisterType((*GetUserComposeMonthLogsReq)(nil), "conversion.GetUserComposeMonthLogsReq")
	proto.RegisterType((*GetUserComposeMonthLogsResp)(nil), "conversion.GetUserComposeMonthLogsResp")
	proto.RegisterType((*GetUserComposeMonthLogsCntReq)(nil), "conversion.GetUserComposeMonthLogsCntReq")
	proto.RegisterType((*GetUserComposeMonthLogsCntResp)(nil), "conversion.GetUserComposeMonthLogsCntResp")
	proto.RegisterType((*ComposeGiftInfo)(nil), "conversion.ComposeGiftInfo")
	proto.RegisterType((*MaterialUseInfo)(nil), "conversion.MaterialUseInfo")
	proto.RegisterType((*GiftComposeReq)(nil), "conversion.GiftComposeReq")
	proto.RegisterType((*GiftComposeResp)(nil), "conversion.GiftComposeResp")
	proto.RegisterType((*DarkComposeMaterialConf)(nil), "conversion.DarkComposeMaterialConf")
	proto.RegisterType((*DarkComposeGiftConf)(nil), "conversion.DarkComposeGiftConf")
	proto.RegisterType((*AddDarkComposeGiftConfReq)(nil), "conversion.AddDarkComposeGiftConfReq")
	proto.RegisterType((*AddDarkComposeGiftConfResp)(nil), "conversion.AddDarkComposeGiftConfResp")
	proto.RegisterType((*DelDarkComposeGiftConfReq)(nil), "conversion.DelDarkComposeGiftConfReq")
	proto.RegisterType((*DelDarkComposeGiftConfResp)(nil), "conversion.DelDarkComposeGiftConfResp")
	proto.RegisterType((*GetAllDarkComposeGiftConfReq)(nil), "conversion.GetAllDarkComposeGiftConfReq")
	proto.RegisterType((*GetAllDarkComposeGiftConfResp)(nil), "conversion.GetAllDarkComposeGiftConfResp")
	proto.RegisterType((*AddDarkMaterialConfReq)(nil), "conversion.AddDarkMaterialConfReq")
	proto.RegisterType((*AddDarkMaterialConfResp)(nil), "conversion.AddDarkMaterialConfResp")
	proto.RegisterType((*DelDarkMaterialConfReq)(nil), "conversion.DelDarkMaterialConfReq")
	proto.RegisterType((*DelDarkMaterialConfResp)(nil), "conversion.DelDarkMaterialConfResp")
	proto.RegisterType((*GetAllDarkMaterialConfReq)(nil), "conversion.GetAllDarkMaterialConfReq")
	proto.RegisterType((*GetAllDarkMaterialConfResp)(nil), "conversion.GetAllDarkMaterialConfResp")
	proto.RegisterType((*GetDarkComposeGiftConfByIdReq)(nil), "conversion.GetDarkComposeGiftConfByIdReq")
	proto.RegisterType((*GetDarkComposeGiftConfByIdResp)(nil), "conversion.GetDarkComposeGiftConfByIdResp")
	proto.RegisterType((*GetDarkGift2MaterialsReq)(nil), "conversion.GetDarkGift2MaterialsReq")
	proto.RegisterType((*GetDarkGift2MaterialsResp)(nil), "conversion.GetDarkGift2MaterialsResp")
	proto.RegisterType((*DarkComposeGiftInfo)(nil), "conversion.DarkComposeGiftInfo")
	proto.RegisterType((*DarkMaterialUseInfo)(nil), "conversion.DarkMaterialUseInfo")
	proto.RegisterType((*DarkGiftComposeReq)(nil), "conversion.DarkGiftComposeReq")
	proto.RegisterType((*DarkGiftComposeResp)(nil), "conversion.DarkGiftComposeResp")
	proto.RegisterType((*GetUserDarkComposeMonthLogsReq)(nil), "conversion.GetUserDarkComposeMonthLogsReq")
	proto.RegisterType((*GetUserDarkComposeMonthLogsResp)(nil), "conversion.GetUserDarkComposeMonthLogsResp")
	proto.RegisterType((*GetUserDarkComposeMonthLogsCntReq)(nil), "conversion.GetUserDarkComposeMonthLogsCntReq")
	proto.RegisterType((*GetUserDarkComposeMonthLogsCntResp)(nil), "conversion.GetUserDarkComposeMonthLogsCntResp")
	proto.RegisterType((*CheckGiftComposeEntryReq)(nil), "conversion.CheckGiftComposeEntryReq")
	proto.RegisterType((*CheckGiftComposeEntryResp)(nil), "conversion.CheckGiftComposeEntryResp")
	proto.RegisterEnum("conversion.LogType", LogType_name, LogType_value)
	proto.RegisterEnum("conversion.OrderStatus", OrderStatus_name, OrderStatus_value)
	proto.RegisterEnum("conversion.ConversionType", ConversionType_name, ConversionType_value)
	proto.RegisterEnum("conversion.ComposeType", ComposeType_name, ComposeType_value)
	proto.RegisterEnum("conversion.MaterialType", MaterialType_name, MaterialType_value)
	proto.RegisterEnum("conversion.ConversionConfig_Status", ConversionConfig_Status_name, ConversionConfig_Status_value)
	proto.RegisterEnum("conversion.ConversionConfig_GiftType", ConversionConfig_GiftType_name, ConversionConfig_GiftType_value)
	proto.RegisterEnum("conversion.ComposeGiftConf_GiftType", ComposeGiftConf_GiftType_name, ComposeGiftConf_GiftType_value)
	proto.RegisterEnum("conversion.ComposeGiftConf_Status", ComposeGiftConf_Status_name, ComposeGiftConf_Status_value)
	proto.RegisterEnum("conversion.ComposeLog_Status", ComposeLog_Status_name, ComposeLog_Status_value)
	proto.RegisterEnum("conversion.DarkComposeGiftConf_GiftType", DarkComposeGiftConf_GiftType_name, DarkComposeGiftConf_GiftType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ConversionServerClient is the client API for ConversionServer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ConversionServerClient interface {
	GetHello(ctx context.Context, in *ConversionServerReq, opts ...grpc.CallOption) (*ConversionServerResp, error)
	ConversionDebris(ctx context.Context, in *ConversionDebrisReq, opts ...grpc.CallOption) (*ConversionDebrisResp, error)
	AddConversionConfig(ctx context.Context, in *AddConversionConfigReq, opts ...grpc.CallOption) (*AddConversionConfigResp, error)
	SetConversionConfigs(ctx context.Context, in *SetConversionConfigsReq, opts ...grpc.CallOption) (*SetConversionConfigsResp, error)
	DelConversionConfig(ctx context.Context, in *DelConversionConfigReq, opts ...grpc.CallOption) (*DelConversionConfigResp, error)
	DelConversionConfByIds(ctx context.Context, in *DelConversionConfByIdsReq, opts ...grpc.CallOption) (*DelConversionConfByIdsResp, error)
	GetAllConversionConfig(ctx context.Context, in *GetAllConversionConfigReq, opts ...grpc.CallOption) (*GetAllConversionConfigResp, error)
	GetConversionLogs(ctx context.Context, in *GetConversionLogsReq, opts ...grpc.CallOption) (*GetConversionLogsResp, error)
	GetFriendGift(ctx context.Context, in *GetFriendGiftReq, opts ...grpc.CallOption) (*GetFriendGiftResp, error)
	AddComposeMaterialConf(ctx context.Context, in *AddComposeMaterialConfReq, opts ...grpc.CallOption) (*AddComposeMaterialConfResp, error)
	BatchAddComposeMaterialConf(ctx context.Context, in *BatchAddComposeMaterialConfReq, opts ...grpc.CallOption) (*BatchAddComposeMaterialConfResp, error)
	DelComposeMaterialConf(ctx context.Context, in *DelComposeMaterialConfReq, opts ...grpc.CallOption) (*DelComposeMaterialConfResp, error)
	GetAllComposeMaterialConf(ctx context.Context, in *GetAllComposeMaterialConfReq, opts ...grpc.CallOption) (*GetAllComposeMaterialConfResp, error)
	GetComposeGiftConfById(ctx context.Context, in *GetComposeGiftConfByIdReq, opts ...grpc.CallOption) (*GetComposeGiftConfByIdResp, error)
	AddComposeGiftConf(ctx context.Context, in *AddComposeGiftConfReq, opts ...grpc.CallOption) (*AddComposeGiftConfResp, error)
	BatchAddComposeGiftConf(ctx context.Context, in *BatchAddComposeGiftConfReq, opts ...grpc.CallOption) (*BatchAddComposeGiftConfResp, error)
	UpdateComposeGiftConf(ctx context.Context, in *UpdateComposeGiftConfReq, opts ...grpc.CallOption) (*UpdateComposeGiftConfResp, error)
	DelComposeGiftConf(ctx context.Context, in *DelComposeGiftConfReq, opts ...grpc.CallOption) (*DelComposeGiftConfResp, error)
	GetAllComposeGiftConf(ctx context.Context, in *GetAllComposeGiftConfReq, opts ...grpc.CallOption) (*GetAllComposeGiftConfResp, error)
	GetUserComposeMonthLogs(ctx context.Context, in *GetUserComposeMonthLogsReq, opts ...grpc.CallOption) (*GetUserComposeMonthLogsResp, error)
	GetUserComposeMonthLogsCnt(ctx context.Context, in *GetUserComposeMonthLogsCntReq, opts ...grpc.CallOption) (*GetUserComposeMonthLogsCntResp, error)
	GiftCompose(ctx context.Context, in *GiftComposeReq, opts ...grpc.CallOption) (*GiftComposeResp, error)
	AddDarkComposeGiftConf(ctx context.Context, in *AddDarkComposeGiftConfReq, opts ...grpc.CallOption) (*AddDarkComposeGiftConfResp, error)
	DelDarkComposeGiftConf(ctx context.Context, in *DelDarkComposeGiftConfReq, opts ...grpc.CallOption) (*DelDarkComposeGiftConfResp, error)
	GetAllDarkComposeGiftConf(ctx context.Context, in *GetAllDarkComposeGiftConfReq, opts ...grpc.CallOption) (*GetAllDarkComposeGiftConfResp, error)
	AddDarkMaterialConf(ctx context.Context, in *AddDarkMaterialConfReq, opts ...grpc.CallOption) (*AddDarkMaterialConfResp, error)
	DelDarkMaterialConf(ctx context.Context, in *DelDarkMaterialConfReq, opts ...grpc.CallOption) (*DelDarkMaterialConfResp, error)
	GetAllDarkMaterialConf(ctx context.Context, in *GetAllDarkMaterialConfReq, opts ...grpc.CallOption) (*GetAllDarkMaterialConfResp, error)
	GetDarkGift2Materials(ctx context.Context, in *GetDarkGift2MaterialsReq, opts ...grpc.CallOption) (*GetDarkGift2MaterialsResp, error)
	GetDarkComposeGiftConfById(ctx context.Context, in *GetDarkComposeGiftConfByIdReq, opts ...grpc.CallOption) (*GetDarkComposeGiftConfByIdResp, error)
	DarkGiftCompose(ctx context.Context, in *DarkGiftComposeReq, opts ...grpc.CallOption) (*DarkGiftComposeResp, error)
	GetUserDarkComposeMonthLogs(ctx context.Context, in *GetUserDarkComposeMonthLogsReq, opts ...grpc.CallOption) (*GetUserDarkComposeMonthLogsResp, error)
	GetUserDarkComposeMonthLogsCnt(ctx context.Context, in *GetUserDarkComposeMonthLogsCntReq, opts ...grpc.CallOption) (*GetUserDarkComposeMonthLogsCntResp, error)
	CheckGiftComposeEntry(ctx context.Context, in *CheckGiftComposeEntryReq, opts ...grpc.CallOption) (*CheckGiftComposeEntryResp, error)
	// 获取时间范围内的兑换订单数量和物品数量
	GetBackpackItemUseCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	// 获取时间范围内的兑换订单列表
	GetBackpackItemUseOrder(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
}

type conversionServerClient struct {
	cc *grpc.ClientConn
}

func NewConversionServerClient(cc *grpc.ClientConn) ConversionServerClient {
	return &conversionServerClient{cc}
}

func (c *conversionServerClient) GetHello(ctx context.Context, in *ConversionServerReq, opts ...grpc.CallOption) (*ConversionServerResp, error) {
	out := new(ConversionServerResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/GetHello", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) ConversionDebris(ctx context.Context, in *ConversionDebrisReq, opts ...grpc.CallOption) (*ConversionDebrisResp, error) {
	out := new(ConversionDebrisResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/ConversionDebris", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) AddConversionConfig(ctx context.Context, in *AddConversionConfigReq, opts ...grpc.CallOption) (*AddConversionConfigResp, error) {
	out := new(AddConversionConfigResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/AddConversionConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) SetConversionConfigs(ctx context.Context, in *SetConversionConfigsReq, opts ...grpc.CallOption) (*SetConversionConfigsResp, error) {
	out := new(SetConversionConfigsResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/SetConversionConfigs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) DelConversionConfig(ctx context.Context, in *DelConversionConfigReq, opts ...grpc.CallOption) (*DelConversionConfigResp, error) {
	out := new(DelConversionConfigResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/DelConversionConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) DelConversionConfByIds(ctx context.Context, in *DelConversionConfByIdsReq, opts ...grpc.CallOption) (*DelConversionConfByIdsResp, error) {
	out := new(DelConversionConfByIdsResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/DelConversionConfByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) GetAllConversionConfig(ctx context.Context, in *GetAllConversionConfigReq, opts ...grpc.CallOption) (*GetAllConversionConfigResp, error) {
	out := new(GetAllConversionConfigResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/GetAllConversionConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) GetConversionLogs(ctx context.Context, in *GetConversionLogsReq, opts ...grpc.CallOption) (*GetConversionLogsResp, error) {
	out := new(GetConversionLogsResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/GetConversionLogs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) GetFriendGift(ctx context.Context, in *GetFriendGiftReq, opts ...grpc.CallOption) (*GetFriendGiftResp, error) {
	out := new(GetFriendGiftResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/GetFriendGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) AddComposeMaterialConf(ctx context.Context, in *AddComposeMaterialConfReq, opts ...grpc.CallOption) (*AddComposeMaterialConfResp, error) {
	out := new(AddComposeMaterialConfResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/AddComposeMaterialConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) BatchAddComposeMaterialConf(ctx context.Context, in *BatchAddComposeMaterialConfReq, opts ...grpc.CallOption) (*BatchAddComposeMaterialConfResp, error) {
	out := new(BatchAddComposeMaterialConfResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/BatchAddComposeMaterialConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) DelComposeMaterialConf(ctx context.Context, in *DelComposeMaterialConfReq, opts ...grpc.CallOption) (*DelComposeMaterialConfResp, error) {
	out := new(DelComposeMaterialConfResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/DelComposeMaterialConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) GetAllComposeMaterialConf(ctx context.Context, in *GetAllComposeMaterialConfReq, opts ...grpc.CallOption) (*GetAllComposeMaterialConfResp, error) {
	out := new(GetAllComposeMaterialConfResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/GetAllComposeMaterialConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) GetComposeGiftConfById(ctx context.Context, in *GetComposeGiftConfByIdReq, opts ...grpc.CallOption) (*GetComposeGiftConfByIdResp, error) {
	out := new(GetComposeGiftConfByIdResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/GetComposeGiftConfById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) AddComposeGiftConf(ctx context.Context, in *AddComposeGiftConfReq, opts ...grpc.CallOption) (*AddComposeGiftConfResp, error) {
	out := new(AddComposeGiftConfResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/AddComposeGiftConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) BatchAddComposeGiftConf(ctx context.Context, in *BatchAddComposeGiftConfReq, opts ...grpc.CallOption) (*BatchAddComposeGiftConfResp, error) {
	out := new(BatchAddComposeGiftConfResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/BatchAddComposeGiftConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) UpdateComposeGiftConf(ctx context.Context, in *UpdateComposeGiftConfReq, opts ...grpc.CallOption) (*UpdateComposeGiftConfResp, error) {
	out := new(UpdateComposeGiftConfResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/UpdateComposeGiftConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) DelComposeGiftConf(ctx context.Context, in *DelComposeGiftConfReq, opts ...grpc.CallOption) (*DelComposeGiftConfResp, error) {
	out := new(DelComposeGiftConfResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/DelComposeGiftConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) GetAllComposeGiftConf(ctx context.Context, in *GetAllComposeGiftConfReq, opts ...grpc.CallOption) (*GetAllComposeGiftConfResp, error) {
	out := new(GetAllComposeGiftConfResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/GetAllComposeGiftConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) GetUserComposeMonthLogs(ctx context.Context, in *GetUserComposeMonthLogsReq, opts ...grpc.CallOption) (*GetUserComposeMonthLogsResp, error) {
	out := new(GetUserComposeMonthLogsResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/GetUserComposeMonthLogs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) GetUserComposeMonthLogsCnt(ctx context.Context, in *GetUserComposeMonthLogsCntReq, opts ...grpc.CallOption) (*GetUserComposeMonthLogsCntResp, error) {
	out := new(GetUserComposeMonthLogsCntResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/GetUserComposeMonthLogsCnt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) GiftCompose(ctx context.Context, in *GiftComposeReq, opts ...grpc.CallOption) (*GiftComposeResp, error) {
	out := new(GiftComposeResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/GiftCompose", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) AddDarkComposeGiftConf(ctx context.Context, in *AddDarkComposeGiftConfReq, opts ...grpc.CallOption) (*AddDarkComposeGiftConfResp, error) {
	out := new(AddDarkComposeGiftConfResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/AddDarkComposeGiftConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) DelDarkComposeGiftConf(ctx context.Context, in *DelDarkComposeGiftConfReq, opts ...grpc.CallOption) (*DelDarkComposeGiftConfResp, error) {
	out := new(DelDarkComposeGiftConfResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/DelDarkComposeGiftConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) GetAllDarkComposeGiftConf(ctx context.Context, in *GetAllDarkComposeGiftConfReq, opts ...grpc.CallOption) (*GetAllDarkComposeGiftConfResp, error) {
	out := new(GetAllDarkComposeGiftConfResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/GetAllDarkComposeGiftConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) AddDarkMaterialConf(ctx context.Context, in *AddDarkMaterialConfReq, opts ...grpc.CallOption) (*AddDarkMaterialConfResp, error) {
	out := new(AddDarkMaterialConfResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/AddDarkMaterialConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) DelDarkMaterialConf(ctx context.Context, in *DelDarkMaterialConfReq, opts ...grpc.CallOption) (*DelDarkMaterialConfResp, error) {
	out := new(DelDarkMaterialConfResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/DelDarkMaterialConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) GetAllDarkMaterialConf(ctx context.Context, in *GetAllDarkMaterialConfReq, opts ...grpc.CallOption) (*GetAllDarkMaterialConfResp, error) {
	out := new(GetAllDarkMaterialConfResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/GetAllDarkMaterialConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) GetDarkGift2Materials(ctx context.Context, in *GetDarkGift2MaterialsReq, opts ...grpc.CallOption) (*GetDarkGift2MaterialsResp, error) {
	out := new(GetDarkGift2MaterialsResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/GetDarkGift2Materials", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) GetDarkComposeGiftConfById(ctx context.Context, in *GetDarkComposeGiftConfByIdReq, opts ...grpc.CallOption) (*GetDarkComposeGiftConfByIdResp, error) {
	out := new(GetDarkComposeGiftConfByIdResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/GetDarkComposeGiftConfById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) DarkGiftCompose(ctx context.Context, in *DarkGiftComposeReq, opts ...grpc.CallOption) (*DarkGiftComposeResp, error) {
	out := new(DarkGiftComposeResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/DarkGiftCompose", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) GetUserDarkComposeMonthLogs(ctx context.Context, in *GetUserDarkComposeMonthLogsReq, opts ...grpc.CallOption) (*GetUserDarkComposeMonthLogsResp, error) {
	out := new(GetUserDarkComposeMonthLogsResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/GetUserDarkComposeMonthLogs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) GetUserDarkComposeMonthLogsCnt(ctx context.Context, in *GetUserDarkComposeMonthLogsCntReq, opts ...grpc.CallOption) (*GetUserDarkComposeMonthLogsCntResp, error) {
	out := new(GetUserDarkComposeMonthLogsCntResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/GetUserDarkComposeMonthLogsCnt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) CheckGiftComposeEntry(ctx context.Context, in *CheckGiftComposeEntryReq, opts ...grpc.CallOption) (*CheckGiftComposeEntryResp, error) {
	out := new(CheckGiftComposeEntryResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/CheckGiftComposeEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) GetBackpackItemUseCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/GetBackpackItemUseCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversionServerClient) GetBackpackItemUseOrder(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/conversion.ConversionServer/GetBackpackItemUseOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConversionServerServer is the server API for ConversionServer service.
type ConversionServerServer interface {
	GetHello(context.Context, *ConversionServerReq) (*ConversionServerResp, error)
	ConversionDebris(context.Context, *ConversionDebrisReq) (*ConversionDebrisResp, error)
	AddConversionConfig(context.Context, *AddConversionConfigReq) (*AddConversionConfigResp, error)
	SetConversionConfigs(context.Context, *SetConversionConfigsReq) (*SetConversionConfigsResp, error)
	DelConversionConfig(context.Context, *DelConversionConfigReq) (*DelConversionConfigResp, error)
	DelConversionConfByIds(context.Context, *DelConversionConfByIdsReq) (*DelConversionConfByIdsResp, error)
	GetAllConversionConfig(context.Context, *GetAllConversionConfigReq) (*GetAllConversionConfigResp, error)
	GetConversionLogs(context.Context, *GetConversionLogsReq) (*GetConversionLogsResp, error)
	GetFriendGift(context.Context, *GetFriendGiftReq) (*GetFriendGiftResp, error)
	AddComposeMaterialConf(context.Context, *AddComposeMaterialConfReq) (*AddComposeMaterialConfResp, error)
	BatchAddComposeMaterialConf(context.Context, *BatchAddComposeMaterialConfReq) (*BatchAddComposeMaterialConfResp, error)
	DelComposeMaterialConf(context.Context, *DelComposeMaterialConfReq) (*DelComposeMaterialConfResp, error)
	GetAllComposeMaterialConf(context.Context, *GetAllComposeMaterialConfReq) (*GetAllComposeMaterialConfResp, error)
	GetComposeGiftConfById(context.Context, *GetComposeGiftConfByIdReq) (*GetComposeGiftConfByIdResp, error)
	AddComposeGiftConf(context.Context, *AddComposeGiftConfReq) (*AddComposeGiftConfResp, error)
	BatchAddComposeGiftConf(context.Context, *BatchAddComposeGiftConfReq) (*BatchAddComposeGiftConfResp, error)
	UpdateComposeGiftConf(context.Context, *UpdateComposeGiftConfReq) (*UpdateComposeGiftConfResp, error)
	DelComposeGiftConf(context.Context, *DelComposeGiftConfReq) (*DelComposeGiftConfResp, error)
	GetAllComposeGiftConf(context.Context, *GetAllComposeGiftConfReq) (*GetAllComposeGiftConfResp, error)
	GetUserComposeMonthLogs(context.Context, *GetUserComposeMonthLogsReq) (*GetUserComposeMonthLogsResp, error)
	GetUserComposeMonthLogsCnt(context.Context, *GetUserComposeMonthLogsCntReq) (*GetUserComposeMonthLogsCntResp, error)
	GiftCompose(context.Context, *GiftComposeReq) (*GiftComposeResp, error)
	AddDarkComposeGiftConf(context.Context, *AddDarkComposeGiftConfReq) (*AddDarkComposeGiftConfResp, error)
	DelDarkComposeGiftConf(context.Context, *DelDarkComposeGiftConfReq) (*DelDarkComposeGiftConfResp, error)
	GetAllDarkComposeGiftConf(context.Context, *GetAllDarkComposeGiftConfReq) (*GetAllDarkComposeGiftConfResp, error)
	AddDarkMaterialConf(context.Context, *AddDarkMaterialConfReq) (*AddDarkMaterialConfResp, error)
	DelDarkMaterialConf(context.Context, *DelDarkMaterialConfReq) (*DelDarkMaterialConfResp, error)
	GetAllDarkMaterialConf(context.Context, *GetAllDarkMaterialConfReq) (*GetAllDarkMaterialConfResp, error)
	GetDarkGift2Materials(context.Context, *GetDarkGift2MaterialsReq) (*GetDarkGift2MaterialsResp, error)
	GetDarkComposeGiftConfById(context.Context, *GetDarkComposeGiftConfByIdReq) (*GetDarkComposeGiftConfByIdResp, error)
	DarkGiftCompose(context.Context, *DarkGiftComposeReq) (*DarkGiftComposeResp, error)
	GetUserDarkComposeMonthLogs(context.Context, *GetUserDarkComposeMonthLogsReq) (*GetUserDarkComposeMonthLogsResp, error)
	GetUserDarkComposeMonthLogsCnt(context.Context, *GetUserDarkComposeMonthLogsCntReq) (*GetUserDarkComposeMonthLogsCntResp, error)
	CheckGiftComposeEntry(context.Context, *CheckGiftComposeEntryReq) (*CheckGiftComposeEntryResp, error)
	// 获取时间范围内的兑换订单数量和物品数量
	GetBackpackItemUseCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	// 获取时间范围内的兑换订单列表
	GetBackpackItemUseOrder(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
}

func RegisterConversionServerServer(s *grpc.Server, srv ConversionServerServer) {
	s.RegisterService(&_ConversionServer_serviceDesc, srv)
}

func _ConversionServer_GetHello_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConversionServerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).GetHello(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/GetHello",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).GetHello(ctx, req.(*ConversionServerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_ConversionDebris_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConversionDebrisReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).ConversionDebris(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/ConversionDebris",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).ConversionDebris(ctx, req.(*ConversionDebrisReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_AddConversionConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddConversionConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).AddConversionConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/AddConversionConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).AddConversionConfig(ctx, req.(*AddConversionConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_SetConversionConfigs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetConversionConfigsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).SetConversionConfigs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/SetConversionConfigs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).SetConversionConfigs(ctx, req.(*SetConversionConfigsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_DelConversionConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelConversionConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).DelConversionConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/DelConversionConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).DelConversionConfig(ctx, req.(*DelConversionConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_DelConversionConfByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelConversionConfByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).DelConversionConfByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/DelConversionConfByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).DelConversionConfByIds(ctx, req.(*DelConversionConfByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_GetAllConversionConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllConversionConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).GetAllConversionConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/GetAllConversionConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).GetAllConversionConfig(ctx, req.(*GetAllConversionConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_GetConversionLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConversionLogsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).GetConversionLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/GetConversionLogs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).GetConversionLogs(ctx, req.(*GetConversionLogsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_GetFriendGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFriendGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).GetFriendGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/GetFriendGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).GetFriendGift(ctx, req.(*GetFriendGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_AddComposeMaterialConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddComposeMaterialConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).AddComposeMaterialConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/AddComposeMaterialConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).AddComposeMaterialConf(ctx, req.(*AddComposeMaterialConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_BatchAddComposeMaterialConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddComposeMaterialConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).BatchAddComposeMaterialConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/BatchAddComposeMaterialConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).BatchAddComposeMaterialConf(ctx, req.(*BatchAddComposeMaterialConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_DelComposeMaterialConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelComposeMaterialConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).DelComposeMaterialConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/DelComposeMaterialConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).DelComposeMaterialConf(ctx, req.(*DelComposeMaterialConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_GetAllComposeMaterialConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllComposeMaterialConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).GetAllComposeMaterialConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/GetAllComposeMaterialConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).GetAllComposeMaterialConf(ctx, req.(*GetAllComposeMaterialConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_GetComposeGiftConfById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetComposeGiftConfByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).GetComposeGiftConfById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/GetComposeGiftConfById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).GetComposeGiftConfById(ctx, req.(*GetComposeGiftConfByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_AddComposeGiftConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddComposeGiftConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).AddComposeGiftConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/AddComposeGiftConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).AddComposeGiftConf(ctx, req.(*AddComposeGiftConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_BatchAddComposeGiftConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddComposeGiftConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).BatchAddComposeGiftConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/BatchAddComposeGiftConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).BatchAddComposeGiftConf(ctx, req.(*BatchAddComposeGiftConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_UpdateComposeGiftConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateComposeGiftConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).UpdateComposeGiftConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/UpdateComposeGiftConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).UpdateComposeGiftConf(ctx, req.(*UpdateComposeGiftConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_DelComposeGiftConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelComposeGiftConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).DelComposeGiftConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/DelComposeGiftConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).DelComposeGiftConf(ctx, req.(*DelComposeGiftConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_GetAllComposeGiftConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllComposeGiftConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).GetAllComposeGiftConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/GetAllComposeGiftConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).GetAllComposeGiftConf(ctx, req.(*GetAllComposeGiftConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_GetUserComposeMonthLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserComposeMonthLogsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).GetUserComposeMonthLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/GetUserComposeMonthLogs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).GetUserComposeMonthLogs(ctx, req.(*GetUserComposeMonthLogsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_GetUserComposeMonthLogsCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserComposeMonthLogsCntReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).GetUserComposeMonthLogsCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/GetUserComposeMonthLogsCnt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).GetUserComposeMonthLogsCnt(ctx, req.(*GetUserComposeMonthLogsCntReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_GiftCompose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiftComposeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).GiftCompose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/GiftCompose",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).GiftCompose(ctx, req.(*GiftComposeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_AddDarkComposeGiftConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddDarkComposeGiftConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).AddDarkComposeGiftConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/AddDarkComposeGiftConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).AddDarkComposeGiftConf(ctx, req.(*AddDarkComposeGiftConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_DelDarkComposeGiftConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelDarkComposeGiftConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).DelDarkComposeGiftConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/DelDarkComposeGiftConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).DelDarkComposeGiftConf(ctx, req.(*DelDarkComposeGiftConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_GetAllDarkComposeGiftConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllDarkComposeGiftConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).GetAllDarkComposeGiftConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/GetAllDarkComposeGiftConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).GetAllDarkComposeGiftConf(ctx, req.(*GetAllDarkComposeGiftConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_AddDarkMaterialConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddDarkMaterialConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).AddDarkMaterialConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/AddDarkMaterialConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).AddDarkMaterialConf(ctx, req.(*AddDarkMaterialConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_DelDarkMaterialConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelDarkMaterialConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).DelDarkMaterialConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/DelDarkMaterialConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).DelDarkMaterialConf(ctx, req.(*DelDarkMaterialConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_GetAllDarkMaterialConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllDarkMaterialConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).GetAllDarkMaterialConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/GetAllDarkMaterialConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).GetAllDarkMaterialConf(ctx, req.(*GetAllDarkMaterialConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_GetDarkGift2Materials_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDarkGift2MaterialsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).GetDarkGift2Materials(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/GetDarkGift2Materials",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).GetDarkGift2Materials(ctx, req.(*GetDarkGift2MaterialsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_GetDarkComposeGiftConfById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDarkComposeGiftConfByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).GetDarkComposeGiftConfById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/GetDarkComposeGiftConfById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).GetDarkComposeGiftConfById(ctx, req.(*GetDarkComposeGiftConfByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_DarkGiftCompose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DarkGiftComposeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).DarkGiftCompose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/DarkGiftCompose",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).DarkGiftCompose(ctx, req.(*DarkGiftComposeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_GetUserDarkComposeMonthLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDarkComposeMonthLogsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).GetUserDarkComposeMonthLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/GetUserDarkComposeMonthLogs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).GetUserDarkComposeMonthLogs(ctx, req.(*GetUserDarkComposeMonthLogsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_GetUserDarkComposeMonthLogsCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDarkComposeMonthLogsCntReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).GetUserDarkComposeMonthLogsCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/GetUserDarkComposeMonthLogsCnt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).GetUserDarkComposeMonthLogsCnt(ctx, req.(*GetUserDarkComposeMonthLogsCntReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_CheckGiftComposeEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckGiftComposeEntryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).CheckGiftComposeEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/CheckGiftComposeEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).CheckGiftComposeEntry(ctx, req.(*CheckGiftComposeEntryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_GetBackpackItemUseCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).GetBackpackItemUseCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/GetBackpackItemUseCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).GetBackpackItemUseCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversionServer_GetBackpackItemUseOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversionServerServer).GetBackpackItemUseOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/conversion.ConversionServer/GetBackpackItemUseOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversionServerServer).GetBackpackItemUseOrder(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ConversionServer_serviceDesc = grpc.ServiceDesc{
	ServiceName: "conversion.ConversionServer",
	HandlerType: (*ConversionServerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetHello",
			Handler:    _ConversionServer_GetHello_Handler,
		},
		{
			MethodName: "ConversionDebris",
			Handler:    _ConversionServer_ConversionDebris_Handler,
		},
		{
			MethodName: "AddConversionConfig",
			Handler:    _ConversionServer_AddConversionConfig_Handler,
		},
		{
			MethodName: "SetConversionConfigs",
			Handler:    _ConversionServer_SetConversionConfigs_Handler,
		},
		{
			MethodName: "DelConversionConfig",
			Handler:    _ConversionServer_DelConversionConfig_Handler,
		},
		{
			MethodName: "DelConversionConfByIds",
			Handler:    _ConversionServer_DelConversionConfByIds_Handler,
		},
		{
			MethodName: "GetAllConversionConfig",
			Handler:    _ConversionServer_GetAllConversionConfig_Handler,
		},
		{
			MethodName: "GetConversionLogs",
			Handler:    _ConversionServer_GetConversionLogs_Handler,
		},
		{
			MethodName: "GetFriendGift",
			Handler:    _ConversionServer_GetFriendGift_Handler,
		},
		{
			MethodName: "AddComposeMaterialConf",
			Handler:    _ConversionServer_AddComposeMaterialConf_Handler,
		},
		{
			MethodName: "BatchAddComposeMaterialConf",
			Handler:    _ConversionServer_BatchAddComposeMaterialConf_Handler,
		},
		{
			MethodName: "DelComposeMaterialConf",
			Handler:    _ConversionServer_DelComposeMaterialConf_Handler,
		},
		{
			MethodName: "GetAllComposeMaterialConf",
			Handler:    _ConversionServer_GetAllComposeMaterialConf_Handler,
		},
		{
			MethodName: "GetComposeGiftConfById",
			Handler:    _ConversionServer_GetComposeGiftConfById_Handler,
		},
		{
			MethodName: "AddComposeGiftConf",
			Handler:    _ConversionServer_AddComposeGiftConf_Handler,
		},
		{
			MethodName: "BatchAddComposeGiftConf",
			Handler:    _ConversionServer_BatchAddComposeGiftConf_Handler,
		},
		{
			MethodName: "UpdateComposeGiftConf",
			Handler:    _ConversionServer_UpdateComposeGiftConf_Handler,
		},
		{
			MethodName: "DelComposeGiftConf",
			Handler:    _ConversionServer_DelComposeGiftConf_Handler,
		},
		{
			MethodName: "GetAllComposeGiftConf",
			Handler:    _ConversionServer_GetAllComposeGiftConf_Handler,
		},
		{
			MethodName: "GetUserComposeMonthLogs",
			Handler:    _ConversionServer_GetUserComposeMonthLogs_Handler,
		},
		{
			MethodName: "GetUserComposeMonthLogsCnt",
			Handler:    _ConversionServer_GetUserComposeMonthLogsCnt_Handler,
		},
		{
			MethodName: "GiftCompose",
			Handler:    _ConversionServer_GiftCompose_Handler,
		},
		{
			MethodName: "AddDarkComposeGiftConf",
			Handler:    _ConversionServer_AddDarkComposeGiftConf_Handler,
		},
		{
			MethodName: "DelDarkComposeGiftConf",
			Handler:    _ConversionServer_DelDarkComposeGiftConf_Handler,
		},
		{
			MethodName: "GetAllDarkComposeGiftConf",
			Handler:    _ConversionServer_GetAllDarkComposeGiftConf_Handler,
		},
		{
			MethodName: "AddDarkMaterialConf",
			Handler:    _ConversionServer_AddDarkMaterialConf_Handler,
		},
		{
			MethodName: "DelDarkMaterialConf",
			Handler:    _ConversionServer_DelDarkMaterialConf_Handler,
		},
		{
			MethodName: "GetAllDarkMaterialConf",
			Handler:    _ConversionServer_GetAllDarkMaterialConf_Handler,
		},
		{
			MethodName: "GetDarkGift2Materials",
			Handler:    _ConversionServer_GetDarkGift2Materials_Handler,
		},
		{
			MethodName: "GetDarkComposeGiftConfById",
			Handler:    _ConversionServer_GetDarkComposeGiftConfById_Handler,
		},
		{
			MethodName: "DarkGiftCompose",
			Handler:    _ConversionServer_DarkGiftCompose_Handler,
		},
		{
			MethodName: "GetUserDarkComposeMonthLogs",
			Handler:    _ConversionServer_GetUserDarkComposeMonthLogs_Handler,
		},
		{
			MethodName: "GetUserDarkComposeMonthLogsCnt",
			Handler:    _ConversionServer_GetUserDarkComposeMonthLogsCnt_Handler,
		},
		{
			MethodName: "CheckGiftComposeEntry",
			Handler:    _ConversionServer_CheckGiftComposeEntry_Handler,
		},
		{
			MethodName: "GetBackpackItemUseCount",
			Handler:    _ConversionServer_GetBackpackItemUseCount_Handler,
		},
		{
			MethodName: "GetBackpackItemUseOrder",
			Handler:    _ConversionServer_GetBackpackItemUseOrder_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/conversion/conversion.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/conversion/conversion.proto", fileDescriptor_conversion_956ea4e0f4a993f4)
}

var fileDescriptor_conversion_956ea4e0f4a993f4 = []byte{
	// 3148 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x3b, 0xcb, 0x6f, 0x1b, 0xc7,
	0xf9, 0x5c, 0x4a, 0xa2, 0xc8, 0x8f, 0xa2, 0x44, 0x8f, 0xfc, 0xa0, 0x28, 0xdb, 0x92, 0xc7, 0x3f,
	0x25, 0xb2, 0xe2, 0xd0, 0xf9, 0x29, 0x68, 0x1a, 0xa4, 0x79, 0xd9, 0x92, 0x2c, 0x13, 0xb1, 0xe5,
	0x84, 0x92, 0x1c, 0x20, 0x40, 0xc2, 0xd2, 0xbb, 0x23, 0x7a, 0xab, 0xe5, 0xee, 0x7a, 0x67, 0x29,
	0x45, 0xb7, 0xf6, 0x50, 0xf4, 0xd2, 0x4b, 0xef, 0x3d, 0xf4, 0x58, 0xa0, 0x28, 0x50, 0xf4, 0x7f,
	0xe8, 0xa9, 0x97, 0xfe, 0x1d, 0x3d, 0xb7, 0x40, 0x8f, 0xc5, 0xcc, 0xec, 0x6b, 0x76, 0x67, 0x97,
	0x74, 0x9c, 0x00, 0x41, 0x4f, 0xd6, 0xce, 0x7c, 0xf3, 0xbd, 0xe6, 0x9b, 0xef, 0x49, 0xc3, 0x96,
	0xef, 0xdf, 0x7b, 0x39, 0x36, 0xf5, 0x53, 0x6a, 0x5a, 0x67, 0xc4, 0xbb, 0xa7, 0x3b, 0xf6, 0x19,
	0xf1, 0xa8, 0xe9, 0xd8, 0x89, 0x3f, 0x3b, 0xae, 0xe7, 0xf8, 0x0e, 0x82, 0x78, 0xa5, 0xdd, 0x49,
	0x9d, 0xf3, 0x88, 0xee, 0xd8, 0xba, 0x69, 0x91, 0xb7, 0xcf, 0xb6, 0xa5, 0x0f, 0x71, 0x16, 0xff,
	0x43, 0x83, 0xc6, 0x4e, 0x74, 0xfc, 0xb1, 0x33, 0x44, 0xb7, 0xa1, 0x11, 0xe3, 0xeb, 0x9b, 0x46,
	0x4b, 0x5b, 0xd7, 0x36, 0x1b, 0xbd, 0x85, 0x78, 0xb1, 0x6b, 0xa0, 0x37, 0x61, 0x29, 0x01, 0xe4,
	0x9b, 0x23, 0xd2, 0x2a, 0x73, 0xb0, 0xc5, 0x78, 0xf9, 0xc8, 0x1c, 0x11, 0xf4, 0x16, 0x5c, 0x4a,
	0x00, 0x52, 0x7f, 0xe0, 0x8f, 0x69, 0x6b, 0x86, 0x83, 0x36, 0xe3, 0x8d, 0x43, 0xbe, 0x8e, 0x3a,
	0xb0, 0x9c, 0x00, 0xb6, 0x9c, 0x61, 0xdf, 0xbf, 0x70, 0x49, 0x6b, 0x96, 0x83, 0x27, 0xf0, 0x3c,
	0x76, 0x86, 0x47, 0x17, 0x2e, 0x41, 0x97, 0x61, 0x4e, 0x77, 0xc6, 0xb6, 0xdf, 0x9a, 0xe3, 0x10,
	0xe2, 0x03, 0x7f, 0x09, 0x97, 0xf7, 0x89, 0x2f, 0x09, 0x45, 0x7b, 0xe4, 0x25, 0x6a, 0xc2, 0xcc,
	0x38, 0x12, 0x87, 0xfd, 0x89, 0x10, 0xcc, 0xba, 0x83, 0x61, 0xc8, 0x3a, 0xff, 0x1b, 0xad, 0x40,
	0x95, 0xfd, 0xdb, 0xb7, 0xc7, 0xa3, 0x80, 0xcf, 0x79, 0xf6, 0x7d, 0x30, 0x1e, 0xe1, 0x97, 0x70,
	0x45, 0x81, 0x98, 0xba, 0xe8, 0x81, 0xa4, 0x0d, 0xcb, 0x19, 0xd2, 0x96, 0xb6, 0x3e, 0xb3, 0x59,
	0xdf, 0x5e, 0xe9, 0x24, 0x2e, 0x4b, 0x3a, 0x98, 0x54, 0x14, 0xc3, 0xc3, 0x64, 0xf1, 0x1d, 0x7f,
	0x60, 0x05, 0xcc, 0x88, 0x0f, 0x7c, 0x05, 0x96, 0xe3, 0x63, 0x87, 0xc4, 0x3b, 0x23, 0x5e, 0x8f,
	0xbc, 0xc4, 0x57, 0xe1, 0x72, 0x76, 0x99, 0xba, 0xf8, 0xd7, 0x1a, 0x2c, 0x1c, 0x53, 0xe2, 0x75,
	0x7d, 0x32, 0xea, 0xda, 0x27, 0x0e, 0x5a, 0x85, 0x9a, 0xe9, 0x93, 0x91, 0xd0, 0xa3, 0x90, 0xbc,
	0xca, 0x16, 0xb8, 0xfa, 0x6e, 0x42, 0x7d, 0x4c, 0x49, 0x9f, 0x03, 0x98, 0x46, 0x40, 0xb8, 0x36,
	0xa6, 0x84, 0x1f, 0x37, 0xd8, 0x61, 0xea, 0x8c, 0x3d, 0x9d, 0xb0, 0x5d, 0xa1, 0x8b, 0xaa, 0x58,
	0x10, 0x9b, 0xec, 0xb0, 0xd0, 0xbf, 0xb8, 0xa1, 0xea, 0x98, 0x92, 0x1d, 0x7e, 0x05, 0xe7, 0xd0,
	0x8c, 0xf9, 0xdb, 0x25, 0xcf, 0x3d, 0x93, 0x4e, 0x67, 0x57, 0x1f, 0xc3, 0xe2, 0x98, 0x12, 0x4f,
	0xf0, 0x64, 0x99, 0xd4, 0x6f, 0x95, 0xb9, 0x22, 0x5b, 0x49, 0x45, 0x26, 0x25, 0xec, 0x2d, 0x8c,
	0x83, 0xaf, 0xc7, 0x26, 0xf5, 0xf1, 0x6f, 0x2a, 0x49, 0xca, 0x3b, 0x8e, 0x7d, 0x62, 0x4e, 0x69,
	0xd1, 0x47, 0xb0, 0x68, 0x70, 0x46, 0x4d, 0xa3, 0xbf, 0xcd, 0x6f, 0x5f, 0x50, 0xee, 0xa8, 0xaf,
	0x50, 0xa0, 0xee, 0xec, 0x06, 0x47, 0xb6, 0x0f, 0xc6, 0xa3, 0x3d, 0xdb, 0xf7, 0x2e, 0x7a, 0x0b,
	0x46, 0x62, 0x09, 0x5d, 0x83, 0xf9, 0xa1, 0x79, 0xe2, 0x87, 0x0a, 0xac, 0xf5, 0x2a, 0xec, 0xb3,
	0x6b, 0x30, 0x33, 0xe3, 0x1b, 0x8c, 0x90, 0xd0, 0x1e, 0x07, 0x64, 0x67, 0x56, 0xa1, 0x26, 0xb6,
	0x06, 0x23, 0xc2, 0x2d, 0xbb, 0xd6, 0xe3, 0xb0, 0x07, 0x83, 0x11, 0x49, 0x3f, 0x3c, 0x76, 0xad,
	0x95, 0xcc, 0xc3, 0x63, 0x97, 0xcb, 0x2e, 0xef, 0x85, 0x73, 0x2e, 0x40, 0xe6, 0x83, 0xcb, 0x7b,
	0xe1, 0x9c, 0x87, 0x0f, 0xc7, 0xb4, 0x0d, 0xf2, 0x6d, 0xab, 0x2a, 0x8c, 0x8d, 0x7f, 0x30, 0x9e,
	0x5e, 0x38, 0x96, 0xd1, 0x37, 0x06, 0x17, 0xad, 0x9a, 0xe0, 0x89, 0x7d, 0xef, 0x0e, 0x2e, 0xb8,
	0x1d, 0xd1, 0xbe, 0x41, 0x2c, 0xe2, 0x93, 0x16, 0xac, 0x6b, 0x9b, 0xd5, 0x5e, 0xd5, 0xa4, 0xbb,
	0xfc, 0x9b, 0x09, 0x39, 0x76, 0x85, 0x13, 0xa8, 0xf3, 0x63, 0x95, 0xb1, 0xcb, 0x1f, 0xff, 0x2a,
	0xd4, 0x0c, 0xe7, 0x3c, 0xf0, 0x0f, 0x0b, 0x82, 0x07, 0xb6, 0xc0, 0x37, 0x11, 0xcc, 0xda, 0x8e,
	0x4f, 0x5a, 0x0d, 0x2e, 0x21, 0xff, 0x1b, 0xfd, 0x0c, 0x2a, 0x81, 0x8b, 0x58, 0x5c, 0xd7, 0x36,
	0x17, 0xb7, 0x6f, 0x17, 0x2a, 0x5f, 0x78, 0x8d, 0x5e, 0x70, 0x04, 0xdd, 0x82, 0x05, 0xae, 0x37,
	0xdd, 0xb1, 0x7d, 0x62, 0xfb, 0xad, 0x25, 0x8e, 0xb8, 0xce, 0xd6, 0x76, 0xc4, 0x12, 0x7a, 0x10,
	0xa8, 0x96, 0x2b, 0xa5, 0xc9, 0x49, 0x6c, 0x14, 0x92, 0xd8, 0x37, 0x4f, 0x7c, 0xa6, 0x31, 0x71,
	0x03, 0xec, 0xaf, 0xf6, 0x27, 0x70, 0x29, 0x73, 0xeb, 0xcc, 0xb7, 0x9c, 0x92, 0x8b, 0xd0, 0xb7,
	0x9c, 0x92, 0x0b, 0xa6, 0xe2, 0xb3, 0x81, 0x35, 0x0e, 0x9d, 0x8b, 0xf8, 0xf8, 0xa0, 0xfc, 0xbe,
	0x86, 0x3f, 0x82, 0x4a, 0xe0, 0xef, 0xea, 0x30, 0x7f, 0x6c, 0x9f, 0xda, 0xce, 0xb9, 0xdd, 0x2c,
	0xa1, 0x06, 0xd4, 0x0e, 0x1c, 0xff, 0xbe, 0xee, 0x9b, 0x67, 0xa4, 0xa9, 0x21, 0x80, 0x4a, 0xf0,
	0x77, 0x99, 0xc1, 0xed, 0x7d, 0xeb, 0x9a, 0x1e, 0x31, 0x9a, 0x33, 0x78, 0x03, 0xaa, 0x21, 0x57,
	0x0c, 0xe8, 0xc0, 0xf1, 0x46, 0x03, 0x4b, 0x9c, 0x67, 0x7a, 0x7d, 0x6c, 0x8e, 0x4c, 0xbf, 0xa9,
	0xe1, 0xbf, 0x68, 0x49, 0xd7, 0x21, 0x38, 0x56, 0x7b, 0xc1, 0x0f, 0x21, 0x11, 0x40, 0x38, 0xbb,
	0xf5, 0xed, 0xeb, 0x6a, 0xad, 0x04, 0x68, 0x12, 0xf0, 0x4c, 0x4e, 0xd7, 0x33, 0x75, 0x12, 0x38,
	0x08, 0xf1, 0x81, 0x6e, 0x00, 0xf8, 0x03, 0x6f, 0x48, 0xfc, 0x3e, 0x23, 0x26, 0x0c, 0xbc, 0x26,
	0x56, 0x8e, 0x4d, 0x23, 0xc7, 0x71, 0xff, 0x7f, 0xd2, 0xab, 0x85, 0x1c, 0x53, 0x97, 0xd9, 0xa5,
	0xe3, 0x19, 0xcc, 0x2b, 0x08, 0xbe, 0x6b, 0xbd, 0x79, 0xfe, 0xdd, 0x35, 0xb0, 0x0e, 0x57, 0xef,
	0x1b, 0x46, 0xfa, 0xda, 0x98, 0x9c, 0x5d, 0x29, 0xf0, 0xe8, 0x7c, 0x9d, 0x9f, 0xce, 0x15, 0x2e,
	0x38, 0x9b, 0x08, 0x4b, 0x62, 0x05, 0xaf, 0xc0, 0x35, 0x25, 0x11, 0xea, 0xe2, 0x2f, 0xe0, 0xda,
	0x61, 0x32, 0x24, 0x88, 0x2d, 0xae, 0xe8, 0xf7, 0x60, 0x5e, 0x50, 0x0d, 0x83, 0x41, 0x31, 0xd9,
	0x10, 0x18, 0xb7, 0xa1, 0xa5, 0x46, 0x49, 0x5d, 0xfc, 0x11, 0x5c, 0xdd, 0x25, 0x96, 0x4a, 0xdc,
	0x69, 0x7c, 0x1c, 0x13, 0x44, 0x79, 0x9c, 0xba, 0xf8, 0x17, 0xb0, 0x92, 0xd9, 0x7a, 0x70, 0xd1,
	0x35, 0xb8, 0x28, 0x77, 0x01, 0x49, 0xc8, 0x85, 0x67, 0x66, 0x52, 0x49, 0x51, 0xbc, 0x6b, 0x30,
	0x1f, 0x8c, 0xd6, 0xa0, 0x4e, 0x4e, 0x4e, 0x88, 0xee, 0x27, 0xf3, 0x02, 0x10, 0x4b, 0xcc, 0x42,
	0xf1, 0x75, 0x68, 0xe7, 0xd1, 0xa2, 0x2e, 0x5e, 0x85, 0x95, 0x7d, 0xe2, 0xdf, 0xb7, 0x54, 0x62,
	0x62, 0x13, 0xda, 0x79, 0x9b, 0xd4, 0x45, 0x9f, 0x49, 0x7c, 0xbe, 0x8a, 0xf6, 0x2f, 0xa5, 0x2f,
	0x9d, 0xe2, 0x4f, 0xa0, 0xb9, 0x4f, 0xfc, 0x87, 0x9e, 0x49, 0x6c, 0x83, 0x3d, 0x38, 0xf5, 0xe3,
	0x49, 0xda, 0x66, 0x59, 0xb6, 0xcd, 0x65, 0xb8, 0x94, 0x42, 0x40, 0x5d, 0xfc, 0x37, 0xfe, 0x2c,
	0x47, 0xae, 0x43, 0xc9, 0x93, 0x81, 0x4f, 0x3c, 0x73, 0xc0, 0x44, 0x39, 0x41, 0x8b, 0x50, 0x8e,
	0x10, 0x97, 0x4d, 0x23, 0x19, 0x38, 0x84, 0x02, 0xc3, 0xc0, 0x71, 0x0d, 0xe6, 0xa9, 0xe3, 0xf9,
	0x71, 0x48, 0xae, 0xb0, 0xcf, 0x20, 0x20, 0xbb, 0xc6, 0xc0, 0x27, 0xfd, 0x41, 0x1c, 0x90, 0xf9,
	0xc2, 0x7d, 0x1f, 0x2d, 0xc3, 0xdc, 0xf3, 0x21, 0x3b, 0x23, 0x1e, 0xdc, 0xec, 0xf3, 0xa1, 0x38,
	0x11, 0x07, 0x9a, 0x4a, 0x2a, 0xd0, 0xdc, 0x00, 0xe0, 0x9b, 0xe2, 0x71, 0x8b, 0x00, 0xc2, 0xc1,
	0x3f, 0x67, 0x0b, 0xf8, 0x73, 0x58, 0xe1, 0x6f, 0x22, 0x23, 0x09, 0x53, 0xd3, 0xbb, 0x30, 0xcb,
	0x94, 0x1f, 0x3c, 0xb7, 0x35, 0x59, 0xf3, 0xd9, 0x13, 0x1c, 0x98, 0x59, 0x45, 0x1e, 0x46, 0xea,
	0xe2, 0x6f, 0xe0, 0xe6, 0x83, 0x81, 0xaf, 0xbf, 0xc8, 0x27, 0xfa, 0x21, 0xd4, 0x18, 0x9e, 0xd8,
	0x36, 0xa7, 0xa0, 0x5c, 0x65, 0x27, 0x78, 0xe2, 0x70, 0x0b, 0xd6, 0x0a, 0xf1, 0x53, 0x17, 0xbf,
	0x15, 0x3c, 0x11, 0x25, 0xf5, 0xd4, 0xfd, 0x45, 0x36, 0xae, 0x46, 0x75, 0x13, 0xae, 0x87, 0x66,
	0xac, 0xc2, 0x86, 0xbf, 0x86, 0x1b, 0x05, 0xfb, 0xd4, 0x7d, 0x4d, 0x61, 0xff, 0x34, 0x03, 0x4b,
	0x01, 0xc4, 0xbe, 0x88, 0x8e, 0x59, 0x03, 0x8c, 0x2c, 0xa6, 0x9c, 0xb0, 0x98, 0x54, 0x3a, 0x13,
	0x5b, 0xe5, 0x6a, 0x32, 0xb0, 0x06, 0xc6, 0x17, 0x46, 0xcc, 0xa4, 0xc9, 0xce, 0x49, 0x26, 0x9b,
	0x48, 0x1c, 0x2a, 0xf9, 0x89, 0xc3, 0x7c, 0x2a, 0x71, 0x90, 0x0c, 0xbd, 0x9a, 0x32, 0x74, 0xc9,
	0xa6, 0x6b, 0x29, 0x9b, 0x0e, 0x53, 0x0e, 0x48, 0xa4, 0x1c, 0x1f, 0x44, 0x29, 0x47, 0x9d, 0xe7,
	0x03, 0x58, 0xa1, 0xc6, 0x50, 0x49, 0xa9, 0x8c, 0x23, 0x1d, 0x8a, 0x77, 0x9c, 0xd1, 0xc8, 0xb1,
	0xb3, 0xa1, 0xf8, 0x35, 0x03, 0xfe, 0x23, 0xb8, 0x12, 0x5b, 0x65, 0xc8, 0x0a, 0xb3, 0xb9, 0x7b,
	0xd2, 0x33, 0x5b, 0x2d, 0x60, 0x3c, 0x78, 0x62, 0xad, 0x20, 0x5a, 0xa6, 0x30, 0x51, 0x17, 0x3f,
	0x83, 0x76, 0xca, 0xfc, 0x93, 0x84, 0xde, 0xcf, 0x5a, 0x5b, 0x21, 0xb5, 0xd8, 0xd2, 0x6e, 0xc0,
	0x6a, 0x2e, 0x5e, 0xea, 0xe2, 0xcf, 0xa0, 0x75, 0xcc, 0x6f, 0xee, 0xfb, 0x90, 0x6e, 0x15, 0x56,
	0x72, 0x90, 0x51, 0x17, 0xbf, 0x09, 0x57, 0xe2, 0xf7, 0x98, 0x24, 0x93, 0x7e, 0xb8, 0xad, 0x20,
	0xc4, 0x66, 0x51, 0xdc, 0x85, 0x96, 0xf4, 0x28, 0x93, 0x58, 0x32, 0x81, 0x01, 0x1f, 0xc7, 0x61,
	0x2c, 0x83, 0xea, 0x35, 0x14, 0xfa, 0x11, 0x47, 0x9b, 0xda, 0x67, 0xc1, 0x53, 0x1d, 0x9e, 0x84,
	0x74, 0xe5, 0x48, 0xba, 0x27, 0x3c, 0x7e, 0x2a, 0x8f, 0x53, 0xf7, 0xd5, 0x55, 0xfe, 0xcb, 0x19,
	0x80, 0x60, 0xe7, 0xb1, 0x33, 0x2c, 0x48, 0xd4, 0x42, 0xd6, 0xca, 0x31, 0x6b, 0x91, 0x83, 0x99,
	0x49, 0x38, 0x98, 0x26, 0xcc, 0xc4, 0x15, 0x11, 0xfb, 0x13, 0x5d, 0x8d, 0xde, 0x67, 0xe8, 0x3b,
	0xc4, 0x53, 0x0a, 0x1f, 0xba, 0x41, 0xa8, 0x9e, 0x0c, 0x5e, 0xbb, 0x84, 0xea, 0x13, 0x82, 0x17,
	0xc2, 0xd0, 0x10, 0x6e, 0x4c, 0x77, 0xec, 0xfe, 0xd8, 0xb3, 0xb8, 0x17, 0x09, 0x4a, 0x85, 0xae,
	0xee, 0xd8, 0xc7, 0x9e, 0xc5, 0x72, 0x9e, 0x51, 0xe0, 0x3d, 0xfb, 0x63, 0x4a, 0x0c, 0x41, 0x48,
	0x78, 0x94, 0x66, 0xb8, 0x73, 0x4c, 0x89, 0xc1, 0x09, 0xbe, 0x03, 0x97, 0x23, 0x68, 0x5e, 0xb9,
	0x07, 0xa4, 0x81, 0x93, 0x8e, 0x30, 0x1d, 0xb1, 0x2d, 0xc1, 0xc3, 0x2a, 0xd4, 0x74, 0x8f, 0x04,
	0x5e, 0x4c, 0x94, 0x4d, 0x55, 0xb1, 0x70, 0xdf, 0xc7, 0x77, 0x22, 0x8f, 0x51, 0x85, 0xd9, 0xae,
	0x6d, 0xfa, 0xcd, 0x12, 0xf3, 0x09, 0x87, 0x63, 0x5d, 0x27, 0x94, 0x36, 0x35, 0xb6, 0xfc, 0x70,
	0x60, 0x5a, 0xcd, 0x32, 0xfe, 0xbd, 0xc6, 0xaf, 0x94, 0xd5, 0xc4, 0xa1, 0xd3, 0x77, 0x6c, 0xff,
	0x45, 0x7e, 0xd3, 0x63, 0x13, 0x9a, 0x2f, 0xc7, 0xc4, 0xbb, 0xe8, 0x8f, 0x18, 0x9c, 0xd4, 0xbb,
	0xe1, 0xeb, 0xfc, 0x38, 0x77, 0xb4, 0xb1, 0xea, 0x67, 0x24, 0xd5, 0x5f, 0x86, 0xb9, 0xe7, 0x64,
	0x68, 0xda, 0xc1, 0x35, 0x89, 0x0f, 0xb6, 0x6a, 0x31, 0x87, 0x17, 0xe6, 0xf4, 0xfc, 0x03, 0x77,
	0x61, 0x35, 0x97, 0x3b, 0xea, 0xa2, 0x2d, 0x98, 0x4d, 0xb4, 0x4b, 0xae, 0x2a, 0x2c, 0xee, 0xb1,
	0x33, 0xec, 0x71, 0x18, 0x4c, 0x79, 0x50, 0x54, 0xa1, 0xda, 0xb1, 0xfd, 0x1f, 0x48, 0x56, 0xbc,
	0x0d, 0x37, 0x8b, 0x88, 0x52, 0x97, 0x51, 0xd5, 0x6d, 0x3f, 0xa4, 0xaa, 0xdb, 0x3e, 0xfe, 0xa3,
	0x26, 0x85, 0x57, 0xde, 0x88, 0x89, 0xac, 0x5d, 0x53, 0x87, 0xd3, 0xb2, 0x32, 0x9c, 0xf2, 0x28,
	0x36, 0x93, 0x8a, 0x62, 0x51, 0xc5, 0x35, 0x9b, 0xac, 0xb8, 0x32, 0x36, 0x3d, 0x97, 0xb5, 0xe9,
	0xe0, 0x75, 0x55, 0xa2, 0xd7, 0x85, 0x7f, 0xa7, 0xc1, 0xd2, 0x93, 0xd8, 0x98, 0x39, 0xab, 0x09,
	0xae, 0x34, 0x89, 0xab, 0xe0, 0x78, 0x39, 0x7e, 0x9c, 0xdf, 0x81, 0xcf, 0x75, 0x58, 0x88, 0x3b,
	0x3c, 0x51, 0x46, 0x00, 0x61, 0x17, 0xa7, 0x6b, 0xe0, 0x3f, 0x6b, 0xb0, 0x28, 0xfc, 0x0c, 0x57,
	0xa1, 0xfa, 0x66, 0xf7, 0x59, 0x79, 0xc7, 0xf7, 0xfb, 0x82, 0x59, 0xfb, 0xc4, 0x09, 0x6a, 0xd7,
	0x3c, 0xbf, 0xc5, 0xdb, 0x45, 0x4b, 0x7a, 0xea, 0x62, 0x3e, 0x85, 0x46, 0xf4, 0x72, 0xb9, 0x3b,
	0x9e, 0xc9, 0xba, 0xe3, 0x94, 0x86, 0x7a, 0x0b, 0xe1, 0x09, 0xee, 0x92, 0x2f, 0xc1, 0x92, 0xc4,
	0x2e, 0x75, 0xf1, 0xbf, 0x34, 0xb8, 0xb6, 0x3b, 0xf0, 0x4e, 0x5f, 0x2b, 0xd3, 0x2f, 0x54, 0x6e,
	0x22, 0xa7, 0x9a, 0xcd, 0x2f, 0x03, 0xe6, 0x52, 0xd9, 0x91, 0xec, 0x17, 0x2b, 0x69, 0xbf, 0x78,
	0x0b, 0x16, 0x42, 0xa5, 0x26, 0xda, 0x46, 0xf5, 0x60, 0x2d, 0x6c, 0x2b, 0xc5, 0x0d, 0xc5, 0xaa,
	0xdc, 0x50, 0xc4, 0xff, 0x2c, 0xc3, 0x72, 0x42, 0xec, 0xff, 0xb5, 0xdc, 0x32, 0xad, 0x9e, 0x5a,
	0x56, 0x3d, 0xb7, 0xd3, 0xd6, 0x04, 0xbc, 0x48, 0x96, 0x0c, 0x86, 0xdd, 0x82, 0x49, 0xfb, 0x43,
	0xc7, 0x30, 0x08, 0x15, 0x69, 0x67, 0xb5, 0x57, 0x33, 0xe9, 0xbe, 0x58, 0x98, 0x36, 0xab, 0x3c,
	0xe7, 0x15, 0x98, 0x42, 0xdd, 0x13, 0x2a, 0x30, 0xd5, 0x09, 0x0e, 0x8c, 0x36, 0x60, 0x31, 0xe4,
	0x93, 0xc6, 0xcd, 0xd7, 0x46, 0x2f, 0x12, 0x89, 0x72, 0x7b, 0x17, 0x85, 0x9a, 0x92, 0x70, 0x54,
	0x25, 0xe5, 0xb0, 0xa5, 0xae, 0x92, 0xf2, 0x50, 0xbd, 0x13, 0x56, 0x49, 0x39, 0xd8, 0xb2, 0x49,
	0x57, 0x54, 0x37, 0xe5, 0xa0, 0x9c, 0x58, 0x37, 0xa9, 0xce, 0xc5, 0xc9, 0xd7, 0x17, 0x3c, 0x7f,
	0x66, 0x30, 0xe9, 0xf2, 0xef, 0xa7, 0x92, 0xbe, 0x6f, 0xe7, 0xa0, 0x54, 0x54, 0xbd, 0xa2, 0xb7,
	0x94, 0x45, 0x49, 0x5d, 0xbc, 0xc9, 0x33, 0x51, 0x15, 0xb5, 0xb4, 0x1a, 0x45, 0x5f, 0x47, 0x89,
	0xe4, 0xe3, 0x30, 0x0d, 0x55, 0xe1, 0x49, 0x1b, 0xb4, 0x96, 0x31, 0x68, 0xfc, 0x4d, 0xd8, 0x70,
	0x51, 0x61, 0x47, 0x9f, 0x66, 0xd5, 0x39, 0x95, 0xec, 0xb1, 0x4a, 0xef, 0xf3, 0x1b, 0x53, 0xa8,
	0x7d, 0xfa, 0x9c, 0xf6, 0x98, 0x87, 0xe8, 0x5c, 0x14, 0xd4, 0xfd, 0x4e, 0xaf, 0x81, 0xd5, 0x26,
	0x01, 0x5a, 0xb6, 0xb1, 0x1d, 0xf2, 0x9f, 0x93, 0x55, 0xdd, 0x00, 0x08, 0x55, 0x19, 0x8f, 0x52,
	0x82, 0x95, 0x2e, 0x33, 0xcc, 0x95, 0x1c, 0x64, 0xdf, 0x8b, 0x16, 0xff, 0xae, 0x65, 0x1c, 0x2f,
	0x0f, 0x6e, 0x32, 0x57, 0x5a, 0x8a, 0xab, 0xef, 0xe8, 0x87, 0x79, 0x3c, 0x9a, 0xcd, 0x0b, 0xf6,
	0x73, 0x85, 0x49, 0x49, 0x25, 0x37, 0x29, 0x99, 0x8f, 0x93, 0x92, 0xbf, 0x06, 0xd2, 0xfc, 0xc8,
	0x12, 0x13, 0x39, 0x10, 0x55, 0xe4, 0x40, 0x84, 0xff, 0xad, 0x01, 0x0a, 0xef, 0xb7, 0x30, 0x73,
	0x29, 0xb6, 0x14, 0xf4, 0x99, 0x2a, 0xb1, 0x99, 0x99, 0x68, 0xb8, 0xea, 0xe4, 0x66, 0x37, 0x1d,
	0x8e, 0x66, 0xd5, 0x2e, 0xaf, 0x30, 0xc1, 0x61, 0x72, 0x8f, 0x06, 0xde, 0x29, 0x49, 0x44, 0xd9,
	0xaa, 0x58, 0xe8, 0x1a, 0xf8, 0x8a, 0xb8, 0xab, 0x74, 0x06, 0xf4, 0x07, 0x2d, 0x4a, 0x9c, 0x93,
	0xe6, 0xfb, 0x63, 0x2a, 0x4d, 0x9e, 0xc0, 0x5a, 0x21, 0x87, 0xaf, 0x58, 0x9e, 0x9c, 0xc3, 0xad,
	0x02, 0x74, 0x3f, 0x60, 0x89, 0xf2, 0x1e, 0xe0, 0x49, 0x84, 0x95, 0x65, 0xca, 0x5d, 0x68, 0xed,
	0xbc, 0x20, 0x7a, 0xf2, 0xea, 0xc4, 0x08, 0x53, 0x19, 0x5a, 0x7f, 0xab, 0xc1, 0x4a, 0x0e, 0x38,
	0x75, 0xd9, 0xd3, 0x34, 0x69, 0xdf, 0x71, 0x89, 0xcd, 0xcf, 0x54, 0x7b, 0x15, 0x93, 0x3e, 0x75,
	0x89, 0x8d, 0x3a, 0xb0, 0x1c, 0xe7, 0x14, 0xbe, 0x13, 0xd6, 0xc5, 0x42, 0xc2, 0x4b, 0xd1, 0xd6,
	0x91, 0x13, 0x94, 0xc5, 0x6f, 0xc0, 0x92, 0xeb, 0x38, 0x56, 0xff, 0x64, 0x6c, 0x59, 0x7d, 0x31,
	0x40, 0x13, 0xd2, 0x36, 0xd8, 0xf2, 0xc3, 0xb1, 0x65, 0x3d, 0x63, 0x8b, 0x5b, 0xef, 0xc0, 0x7c,
	0xf8, 0x2b, 0x80, 0x45, 0x80, 0x9d, 0xa7, 0x07, 0xcf, 0xf6, 0x7a, 0x87, 0xdd, 0xa7, 0x07, 0xcd,
	0x12, 0xab, 0x8d, 0x0f, 0xf7, 0x0e, 0x76, 0x9b, 0x1a, 0x2b, 0x99, 0x7b, 0x7b, 0x3b, 0x7b, 0xdd,
	0x67, 0x7b, 0xcd, 0xf2, 0xd6, 0x31, 0xd4, 0x9f, 0x7a, 0x06, 0xf1, 0x82, 0xc2, 0xba, 0x09, 0x0b,
	0x9f, 0x7b, 0xc4, 0x1d, 0x78, 0xa4, 0xcf, 0xb0, 0x34, 0x4b, 0x68, 0x09, 0xea, 0x2c, 0xd7, 0x32,
	0x7d, 0xb1, 0xa0, 0xa1, 0x4b, 0xd0, 0xe8, 0x39, 0x96, 0xf5, 0x60, 0xa0, 0x9f, 0x8a, 0xa5, 0x32,
	0x3b, 0xb5, 0x4f, 0x7c, 0xa6, 0x02, 0xb1, 0x32, 0xbb, 0xf5, 0x04, 0x16, 0x77, 0xe4, 0xc9, 0x6b,
	0x03, 0x6a, 0x31, 0x40, 0x89, 0xb1, 0xf7, 0xc8, 0xf1, 0x28, 0x09, 0xb1, 0x36, 0xa0, 0xf6, 0xc4,
	0xd4, 0xfb, 0x87, 0xfe, 0x85, 0xc5, 0x30, 0x36, 0xa0, 0xc6, 0xae, 0xad, 0xcf, 0x8e, 0x34, 0x67,
	0xb6, 0xb6, 0x38, 0x13, 0x51, 0x3e, 0x99, 0xcc, 0xff, 0x96, 0xa0, 0x9e, 0xb8, 0xe0, 0xa6, 0xb6,
	0xd5, 0x81, 0x85, 0xf0, 0xe1, 0x72, 0xe0, 0x85, 0x38, 0x71, 0x6c, 0x96, 0x18, 0xab, 0x0f, 0xbd,
	0xc1, 0x70, 0x44, 0x6c, 0xb1, 0xa2, 0x6d, 0xff, 0xe7, 0x7a, 0x72, 0x38, 0x2e, 0x7e, 0x36, 0x80,
	0x9e, 0x42, 0x75, 0x9f, 0xf8, 0x8f, 0x88, 0x65, 0x39, 0x68, 0x4d, 0x3d, 0x23, 0x89, 0x7e, 0x77,
	0xd0, 0x5e, 0x2f, 0x06, 0xa0, 0x2e, 0x2e, 0xa1, 0x2f, 0x15, 0xb3, 0xff, 0xb5, 0xc2, 0x71, 0x62,
	0x3e, 0xe2, 0x78, 0x08, 0x88, 0x4b, 0xe8, 0xe7, 0xb0, 0xac, 0x18, 0xc3, 0x21, 0xa9, 0x61, 0xab,
	0x1e, 0x06, 0xb6, 0x6f, 0x4f, 0x84, 0xe1, 0x14, 0x74, 0xb8, 0xac, 0x1a, 0xbd, 0x21, 0xe9, 0x78,
	0xce, 0xbc, 0xaf, 0xfd, 0x7f, 0x93, 0x81, 0x42, 0x31, 0x14, 0x43, 0x38, 0x59, 0x0c, 0xf5, 0x90,
	0x4f, 0x16, 0x23, 0x6f, 0x92, 0x57, 0x42, 0xa6, 0x62, 0x4a, 0xc8, 0xe7, 0x6b, 0x68, 0xa3, 0x10,
	0x41, 0x38, 0xef, 0x6b, 0xbf, 0x31, 0x0d, 0x58, 0x48, 0x4a, 0x3d, 0x8f, 0x93, 0x49, 0xe5, 0x0e,
	0xf4, 0x64, 0x52, 0xf9, 0xa3, 0x3d, 0x5c, 0x42, 0x5f, 0xf1, 0x71, 0x9a, 0xfc, 0xeb, 0x1b, 0xb4,
	0x9e, 0x3a, 0x9e, 0xf9, 0xd5, 0x4f, 0xfb, 0xd6, 0x04, 0x08, 0x8e, 0xfb, 0x00, 0x1a, 0xd2, 0xa8,
	0x0e, 0x5d, 0x4f, 0x9d, 0x92, 0xc6, 0x80, 0xed, 0x1b, 0x05, 0xbb, 0xa1, 0x5a, 0xd4, 0x83, 0x24,
	0x59, 0x2d, 0xb9, 0xc3, 0x2c, 0x59, 0x2d, 0x05, 0x33, 0xa9, 0x12, 0xfa, 0x36, 0xd3, 0x61, 0x97,
	0xe8, 0x6d, 0x25, 0x11, 0x15, 0x4f, 0xd0, 0xda, 0x6f, 0x4d, 0x0d, 0x2b, 0x99, 0xd9, 0x04, 0x21,
	0x73, 0x67, 0x66, 0x0a, 0x33, 0xcb, 0x23, 0xe5, 0xa5, 0x9a, 0xe9, 0x12, 0xb5, 0x4d, 0x95, 0x09,
	0x29, 0x09, 0xde, 0x99, 0x12, 0x32, 0x61, 0xda, 0x8a, 0x92, 0x22, 0x63, 0xda, 0xea, 0xca, 0x25,
	0x63, 0xda, 0x39, 0xd5, 0x09, 0x2e, 0xa1, 0xaf, 0x01, 0x65, 0x07, 0x24, 0xe8, 0x96, 0xda, 0x06,
	0x12, 0x15, 0x70, 0x1b, 0x4f, 0x02, 0xe1, 0xe8, 0x2d, 0xb8, 0x96, 0x33, 0x84, 0x41, 0x6f, 0x14,
	0x5c, 0x79, 0x92, 0xd0, 0x9b, 0x53, 0xc1, 0x71, 0x6a, 0x27, 0x70, 0x45, 0x39, 0x86, 0x41, 0x92,
	0x83, 0xcc, 0x1b, 0xfb, 0xb4, 0x37, 0xa6, 0x80, 0x0a, 0x95, 0x96, 0x1d, 0xd4, 0xc8, 0x4a, 0x53,
	0x4e, 0x7c, 0xda, 0x78, 0x12, 0x48, 0x28, 0x86, 0x72, 0x7e, 0x23, 0x8b, 0x91, 0x37, 0x10, 0x6a,
	0x6f, 0x4c, 0x01, 0x15, 0x5e, 0x4e, 0x4e, 0x83, 0x19, 0xa5, 0x0d, 0x28, 0xa7, 0xc7, 0x2f, 0x5f,
	0x4e, 0x41, 0xb7, 0x1d, 0x97, 0xd0, 0x38, 0x77, 0x58, 0xb0, 0x63, 0xfb, 0xe8, 0xce, 0x14, 0x88,
	0x44, 0x22, 0xdb, 0xde, 0x9a, 0x16, 0x94, 0x93, 0x7d, 0x04, 0xf5, 0x44, 0xda, 0x88, 0xda, 0xd2,
	0x61, 0xa9, 0x60, 0x6a, 0xaf, 0xe6, 0xee, 0x25, 0x3c, 0xab, 0xaa, 0xc9, 0x98, 0xf6, 0xac, 0xea,
	0xa6, 0x51, 0xc6, 0xb3, 0xe6, 0x35, 0x9f, 0x42, 0xff, 0x36, 0x91, 0x54, 0x6e, 0xb7, 0x2b, 0xe3,
	0xdf, 0xf2, 0x49, 0x79, 0xc9, 0x2e, 0x4d, 0x9a, 0x9a, 0xc2, 0xbf, 0xe5, 0x10, 0xbc, 0x33, 0x25,
	0x64, 0x22, 0x9d, 0x4a, 0xb7, 0x75, 0x32, 0xe9, 0x94, 0xa2, 0x6f, 0x94, 0x49, 0xa7, 0x94, 0x9d,
	0xa7, 0x30, 0xd3, 0x29, 0xa6, 0xa0, 0xee, 0x70, 0x65, 0x32, 0x9d, 0x1c, 0x0a, 0x51, 0xfa, 0x91,
	0x21, 0xb2, 0xa1, 0x56, 0x45, 0x61, 0x08, 0xca, 0x6f, 0x74, 0x45, 0xfe, 0x20, 0xdb, 0xc1, 0xc9,
	0xf8, 0x03, 0x65, 0xc7, 0x28, 0xe3, 0x0f, 0xd4, 0xad, 0xa0, 0xe8, 0x85, 0xe6, 0x74, 0xb3, 0x32,
	0x2f, 0x34, 0xbf, 0x71, 0x96, 0x79, 0xa1, 0x05, 0x0d, 0x32, 0x5c, 0x42, 0x47, 0xb0, 0x94, 0x2a,
	0xe3, 0xd1, 0xcd, 0x74, 0x97, 0x20, 0xf5, 0x52, 0xd7, 0x0a, 0xf7, 0xc3, 0xe4, 0xa4, 0xa0, 0x34,
	0x45, 0x2a, 0x27, 0x92, 0xd3, 0x2d, 0x90, 0x93, 0x93, 0x09, 0x75, 0x3b, 0x2e, 0xa1, 0x5f, 0x15,
	0xf7, 0x1f, 0x98, 0xb7, 0x7b, 0x7b, 0x4a, 0x8c, 0x81, 0xc7, 0xeb, 0xbc, 0x0a, 0x78, 0x68, 0x32,
	0xca, 0x8a, 0x59, 0x36, 0x99, 0xbc, 0x1a, 0x5c, 0x36, 0x99, 0xdc, 0xd2, 0x9b, 0x67, 0xaf, 0x2c,
	0x84, 0xb0, 0x32, 0xd5, 0x1d, 0xe8, 0xa7, 0x5d, 0x9f, 0x8c, 0x8e, 0x83, 0x1f, 0x62, 0xa3, 0x95,
	0x4e, 0x2f, 0xfc, 0xcd, 0xff, 0xb3, 0xed, 0xce, 0x91, 0x39, 0x22, 0xbd, 0x81, 0x3d, 0xe4, 0xd7,
	0x77, 0x55, 0xda, 0xe2, 0xe0, 0x01, 0xbe, 0x2f, 0x54, 0xf8, 0x78, 0xed, 0x5c, 0x84, 0x4f, 0xde,
	0x7a, 0x2a, 0xe6, 0xfe, 0xc1, 0x75, 0x3c, 0xe8, 0x7c, 0x75, 0x77, 0xe8, 0x58, 0x03, 0x7b, 0xd8,
	0xf9, 0xc9, 0xb6, 0xef, 0x77, 0x74, 0x67, 0x74, 0x8f, 0xff, 0xff, 0x03, 0xdd, 0xb1, 0xee, 0x51,
	0xe2, 0x9d, 0x99, 0x3a, 0xa1, 0x89, 0xff, 0xd8, 0xf0, 0xbc, 0xc2, 0x77, 0xdf, 0xfd, 0x6f, 0x00,
	0x00, 0x00, 0xff, 0xff, 0x6b, 0x99, 0xa1, 0xdd, 0x07, 0x31, 0x00, 0x00,
}
