// Code generated by protoc-gen-gogo.
// source: starguildsvr/v3/starguildv3.proto
// DO NOT EDIT!

/*
	Package starguildv3 is a generated protocol buffer package.

	namespace

	It is generated from these files:
		starguildsvr/v3/starguildv3.proto

	It has these top-level messages:
		UserAddContributeReq
		UserAddContributeResp
		StarGuildInfo
		GetGuildStarInfosReq
		GetGuildStarInfosResp
		GetMemberContributionExtReq
		MemberContributionExt
		GetMemberContributionExtResp
		GetGuildMutableContributionReq
		GetGuildMutableContributionResp
		AddGuildMutableContributionReq
		AddGuildMutableContributionResp
		MemberQuitGuildReq
		GetGuildInfoByLevelReq
		GetGuildInfoByLevelObj
		GetGuildInfoByLevelResp
		CalcSingleGuildReq
		GetGuildLevelCountByDateReq
		LevelCount
		GuildLevelCount
		GetGuildLevelCountByDateResp
		GetGuildInfoByDateReq
		GuildInfoByDate
		GetGuildInfoByDateResp
		GetRankListByContributionReq
		RankListByContribution
		GetRankListByContributionResp
		ReportDismissGuildReq
		GetGuildLevelMemberHistoryReq
		GuildLevelMemberHistory
		GetGuildLevelMemberHistoryResp
		GuildMonthRank
		GetGuildMonthRankReq
		GetGuildMonthRankResp
		GetGuildEventCountReq
		GetGuildEventCountResp
*/
package starguildv3

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type UserConsumeType int32

const (
	UserConsumeType_UCT_GAME            UserConsumeType = 0
	UserConsumeType_UCT_LIVE            UserConsumeType = 1
	UserConsumeType_UCT_FUNNY_ROOM_GIFT UserConsumeType = 2
	UserConsumeType_UCT_MAX             UserConsumeType = 3
)

var UserConsumeType_name = map[int32]string{
	0: "UCT_GAME",
	1: "UCT_LIVE",
	2: "UCT_FUNNY_ROOM_GIFT",
	3: "UCT_MAX",
}
var UserConsumeType_value = map[string]int32{
	"UCT_GAME":            0,
	"UCT_LIVE":            1,
	"UCT_FUNNY_ROOM_GIFT": 2,
	"UCT_MAX":             3,
}

func (x UserConsumeType) Enum() *UserConsumeType {
	p := new(UserConsumeType)
	*p = x
	return p
}
func (x UserConsumeType) String() string {
	return proto.EnumName(UserConsumeType_name, int32(x))
}
func (x *UserConsumeType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(UserConsumeType_value, data, "UserConsumeType")
	if err != nil {
		return err
	}
	*x = UserConsumeType(value)
	return nil
}
func (UserConsumeType) EnumDescriptor() ([]byte, []int) { return fileDescriptorStarguildv3, []int{0} }

type UserOperateType int32

const (
	UserOperateType_CHECK_IN_DAILY    UserOperateType = 1
	UserOperateType_UNUSE_1           UserOperateType = 2
	UserOperateType_DONATION          UserOperateType = 3
	UserOperateType_CAPITAL_INJECTION UserOperateType = 4
	UserOperateType_CONSUME           UserOperateType = 5
)

var UserOperateType_name = map[int32]string{
	1: "CHECK_IN_DAILY",
	2: "UNUSE_1",
	3: "DONATION",
	4: "CAPITAL_INJECTION",
	5: "CONSUME",
}
var UserOperateType_value = map[string]int32{
	"CHECK_IN_DAILY":    1,
	"UNUSE_1":           2,
	"DONATION":          3,
	"CAPITAL_INJECTION": 4,
	"CONSUME":           5,
}

func (x UserOperateType) Enum() *UserOperateType {
	p := new(UserOperateType)
	*p = x
	return p
}
func (x UserOperateType) String() string {
	return proto.EnumName(UserOperateType_name, int32(x))
}
func (x *UserOperateType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(UserOperateType_value, data, "UserOperateType")
	if err != nil {
		return err
	}
	*x = UserOperateType(value)
	return nil
}
func (UserOperateType) EnumDescriptor() ([]byte, []int) { return fileDescriptorStarguildv3, []int{1} }

type GetMemberContributionExtReq_TIME_LIMIT int32

const (
	GetMemberContributionExtReq_TIME_LIMIT_ALL          GetMemberContributionExtReq_TIME_LIMIT = 1
	GetMemberContributionExtReq_TIME_LIMIT_RECENT_MONTH GetMemberContributionExtReq_TIME_LIMIT = 2
)

var GetMemberContributionExtReq_TIME_LIMIT_name = map[int32]string{
	1: "TIME_LIMIT_ALL",
	2: "TIME_LIMIT_RECENT_MONTH",
}
var GetMemberContributionExtReq_TIME_LIMIT_value = map[string]int32{
	"TIME_LIMIT_ALL":          1,
	"TIME_LIMIT_RECENT_MONTH": 2,
}

func (x GetMemberContributionExtReq_TIME_LIMIT) Enum() *GetMemberContributionExtReq_TIME_LIMIT {
	p := new(GetMemberContributionExtReq_TIME_LIMIT)
	*p = x
	return p
}
func (x GetMemberContributionExtReq_TIME_LIMIT) String() string {
	return proto.EnumName(GetMemberContributionExtReq_TIME_LIMIT_name, int32(x))
}
func (x *GetMemberContributionExtReq_TIME_LIMIT) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GetMemberContributionExtReq_TIME_LIMIT_value, data, "GetMemberContributionExtReq_TIME_LIMIT")
	if err != nil {
		return err
	}
	*x = GetMemberContributionExtReq_TIME_LIMIT(value)
	return nil
}
func (GetMemberContributionExtReq_TIME_LIMIT) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{5, 0}
}

type UserAddContributeReq struct {
	Uid              uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId          uint32 `protobuf:"varint,2,opt,name=guild_id,json=guildId" json:"guild_id"`
	EventType        uint32 `protobuf:"varint,3,req,name=event_type,json=eventType" json:"event_type"`
	ReddiamondAmount uint32 `protobuf:"varint,4,opt,name=reddiamond_amount,json=reddiamondAmount" json:"reddiamond_amount"`
	MoneyAmount      uint32 `protobuf:"varint,5,opt,name=money_amount,json=moneyAmount" json:"money_amount"`
	ConsumeType      uint32 `protobuf:"varint,6,opt,name=consume_type,json=consumeType" json:"consume_type"`
	Valid            bool   `protobuf:"varint,7,opt,name=valid" json:"valid"`
	OrderId          string `protobuf:"bytes,8,opt,name=order_id,json=orderId" json:"order_id"`
}

func (m *UserAddContributeReq) Reset()                    { *m = UserAddContributeReq{} }
func (m *UserAddContributeReq) String() string            { return proto.CompactTextString(m) }
func (*UserAddContributeReq) ProtoMessage()               {}
func (*UserAddContributeReq) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv3, []int{0} }

func (m *UserAddContributeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserAddContributeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UserAddContributeReq) GetEventType() uint32 {
	if m != nil {
		return m.EventType
	}
	return 0
}

func (m *UserAddContributeReq) GetReddiamondAmount() uint32 {
	if m != nil {
		return m.ReddiamondAmount
	}
	return 0
}

func (m *UserAddContributeReq) GetMoneyAmount() uint32 {
	if m != nil {
		return m.MoneyAmount
	}
	return 0
}

func (m *UserAddContributeReq) GetConsumeType() uint32 {
	if m != nil {
		return m.ConsumeType
	}
	return 0
}

func (m *UserAddContributeReq) GetValid() bool {
	if m != nil {
		return m.Valid
	}
	return false
}

func (m *UserAddContributeReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type UserAddContributeResp struct {
	ValueAdded uint32 `protobuf:"varint,1,req,name=value_added,json=valueAdded" json:"value_added"`
}

func (m *UserAddContributeResp) Reset()                    { *m = UserAddContributeResp{} }
func (m *UserAddContributeResp) String() string            { return proto.CompactTextString(m) }
func (*UserAddContributeResp) ProtoMessage()               {}
func (*UserAddContributeResp) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv3, []int{1} }

func (m *UserAddContributeResp) GetValueAdded() uint32 {
	if m != nil {
		return m.ValueAdded
	}
	return 0
}

type StarGuildInfo struct {
	GuildId                 uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	StarLevel               uint32 `protobuf:"varint,2,req,name=star_level,json=starLevel" json:"star_level"`
	ContributionBase        uint32 `protobuf:"varint,3,req,name=contribution_base,json=contributionBase" json:"contribution_base"`
	ContributionExt         uint32 `protobuf:"varint,4,req,name=contribution_ext,json=contributionExt" json:"contribution_ext"`
	ContributionExtPay      uint32 `protobuf:"varint,5,req,name=contribution_ext_pay,json=contributionExtPay" json:"contribution_ext_pay"`
	ContributionExtPercent  uint32 `protobuf:"varint,6,req,name=contribution_ext_percent,json=contributionExtPercent" json:"contribution_ext_percent"`
	ContributionNextPay     uint32 `protobuf:"varint,7,req,name=contribution_next_pay,json=contributionNextPay" json:"contribution_next_pay"`
	ContributionNextPercent uint32 `protobuf:"varint,8,req,name=contribution_next_percent,json=contributionNextPercent" json:"contribution_next_percent"`
	ContributionNextLevel   uint32 `protobuf:"varint,9,req,name=contribution_next_level,json=contributionNextLevel" json:"contribution_next_level"`
	ContributionCurLevel    uint32 `protobuf:"varint,10,opt,name=contribution_cur_level,json=contributionCurLevel" json:"contribution_cur_level"`
	JoinNumYesterday        uint32 `protobuf:"varint,11,req,name=join_num_yesterday,json=joinNumYesterday" json:"join_num_yesterday"`
	Contribution_30DaysAgo  uint32 `protobuf:"varint,12,opt,name=contribution_30_days_ago,json=contribution30DaysAgo" json:"contribution_30_days_ago"`
	ContributionToday       uint32 `protobuf:"varint,13,opt,name=contribution_today,json=contributionToday" json:"contribution_today"`
	LastCalcTime            uint32 `protobuf:"varint,14,opt,name=last_calc_time,json=lastCalcTime" json:"last_calc_time"`
}

func (m *StarGuildInfo) Reset()                    { *m = StarGuildInfo{} }
func (m *StarGuildInfo) String() string            { return proto.CompactTextString(m) }
func (*StarGuildInfo) ProtoMessage()               {}
func (*StarGuildInfo) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv3, []int{2} }

func (m *StarGuildInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *StarGuildInfo) GetStarLevel() uint32 {
	if m != nil {
		return m.StarLevel
	}
	return 0
}

func (m *StarGuildInfo) GetContributionBase() uint32 {
	if m != nil {
		return m.ContributionBase
	}
	return 0
}

func (m *StarGuildInfo) GetContributionExt() uint32 {
	if m != nil {
		return m.ContributionExt
	}
	return 0
}

func (m *StarGuildInfo) GetContributionExtPay() uint32 {
	if m != nil {
		return m.ContributionExtPay
	}
	return 0
}

func (m *StarGuildInfo) GetContributionExtPercent() uint32 {
	if m != nil {
		return m.ContributionExtPercent
	}
	return 0
}

func (m *StarGuildInfo) GetContributionNextPay() uint32 {
	if m != nil {
		return m.ContributionNextPay
	}
	return 0
}

func (m *StarGuildInfo) GetContributionNextPercent() uint32 {
	if m != nil {
		return m.ContributionNextPercent
	}
	return 0
}

func (m *StarGuildInfo) GetContributionNextLevel() uint32 {
	if m != nil {
		return m.ContributionNextLevel
	}
	return 0
}

func (m *StarGuildInfo) GetContributionCurLevel() uint32 {
	if m != nil {
		return m.ContributionCurLevel
	}
	return 0
}

func (m *StarGuildInfo) GetJoinNumYesterday() uint32 {
	if m != nil {
		return m.JoinNumYesterday
	}
	return 0
}

func (m *StarGuildInfo) GetContribution_30DaysAgo() uint32 {
	if m != nil {
		return m.Contribution_30DaysAgo
	}
	return 0
}

func (m *StarGuildInfo) GetContributionToday() uint32 {
	if m != nil {
		return m.ContributionToday
	}
	return 0
}

func (m *StarGuildInfo) GetLastCalcTime() uint32 {
	if m != nil {
		return m.LastCalcTime
	}
	return 0
}

type GetGuildStarInfosReq struct {
	GuildIdList []uint32 `protobuf:"varint,1,rep,name=guild_id_list,json=guildIdList" json:"guild_id_list,omitempty"`
	QueryToday  bool     `protobuf:"varint,2,opt,name=query_today,json=queryToday" json:"query_today"`
}

func (m *GetGuildStarInfosReq) Reset()                    { *m = GetGuildStarInfosReq{} }
func (m *GetGuildStarInfosReq) String() string            { return proto.CompactTextString(m) }
func (*GetGuildStarInfosReq) ProtoMessage()               {}
func (*GetGuildStarInfosReq) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv3, []int{3} }

func (m *GetGuildStarInfosReq) GetGuildIdList() []uint32 {
	if m != nil {
		return m.GuildIdList
	}
	return nil
}

func (m *GetGuildStarInfosReq) GetQueryToday() bool {
	if m != nil {
		return m.QueryToday
	}
	return false
}

type GetGuildStarInfosResp struct {
	StarInfoList []*StarGuildInfo `protobuf:"bytes,1,rep,name=star_info_list,json=starInfoList" json:"star_info_list,omitempty"`
}

func (m *GetGuildStarInfosResp) Reset()                    { *m = GetGuildStarInfosResp{} }
func (m *GetGuildStarInfosResp) String() string            { return proto.CompactTextString(m) }
func (*GetGuildStarInfosResp) ProtoMessage()               {}
func (*GetGuildStarInfosResp) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv3, []int{4} }

func (m *GetGuildStarInfosResp) GetStarInfoList() []*StarGuildInfo {
	if m != nil {
		return m.StarInfoList
	}
	return nil
}

type GetMemberContributionExtReq struct {
	GuildId   uint32   `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	Start     uint32   `protobuf:"varint,2,opt,name=start" json:"start"`
	Limit     uint32   `protobuf:"varint,3,opt,name=limit" json:"limit"`
	TimeLimit uint32   `protobuf:"varint,4,req,name=time_limit,json=timeLimit" json:"time_limit"`
	UidList   []uint32 `protobuf:"varint,5,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *GetMemberContributionExtReq) Reset()         { *m = GetMemberContributionExtReq{} }
func (m *GetMemberContributionExtReq) String() string { return proto.CompactTextString(m) }
func (*GetMemberContributionExtReq) ProtoMessage()    {}
func (*GetMemberContributionExtReq) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{5}
}

func (m *GetMemberContributionExtReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetMemberContributionExtReq) GetStart() uint32 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *GetMemberContributionExtReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetMemberContributionExtReq) GetTimeLimit() uint32 {
	if m != nil {
		return m.TimeLimit
	}
	return 0
}

func (m *GetMemberContributionExtReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type MemberContributionExt struct {
	Uid           uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Total         uint32 `protobuf:"varint,2,req,name=total" json:"total"`
	GameConsume   uint32 `protobuf:"varint,3,req,name=game_consume,json=gameConsume" json:"game_consume"`
	LiveConsume   uint32 `protobuf:"varint,4,req,name=live_consume,json=liveConsume" json:"live_consume"`
	FunnyRoomGift uint32 `protobuf:"varint,5,opt,name=funny_room_gift,json=funnyRoomGift" json:"funny_room_gift"`
}

func (m *MemberContributionExt) Reset()                    { *m = MemberContributionExt{} }
func (m *MemberContributionExt) String() string            { return proto.CompactTextString(m) }
func (*MemberContributionExt) ProtoMessage()               {}
func (*MemberContributionExt) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv3, []int{6} }

func (m *MemberContributionExt) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MemberContributionExt) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *MemberContributionExt) GetGameConsume() uint32 {
	if m != nil {
		return m.GameConsume
	}
	return 0
}

func (m *MemberContributionExt) GetLiveConsume() uint32 {
	if m != nil {
		return m.LiveConsume
	}
	return 0
}

func (m *MemberContributionExt) GetFunnyRoomGift() uint32 {
	if m != nil {
		return m.FunnyRoomGift
	}
	return 0
}

type GetMemberContributionExtResp struct {
	GuildId       uint32                   `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	ContriExtList []*MemberContributionExt `protobuf:"bytes,2,rep,name=contri_ext_list,json=contriExtList" json:"contri_ext_list,omitempty"`
	TotalCount    uint32                   `protobuf:"varint,3,opt,name=total_count,json=totalCount" json:"total_count"`
}

func (m *GetMemberContributionExtResp) Reset()         { *m = GetMemberContributionExtResp{} }
func (m *GetMemberContributionExtResp) String() string { return proto.CompactTextString(m) }
func (*GetMemberContributionExtResp) ProtoMessage()    {}
func (*GetMemberContributionExtResp) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{7}
}

func (m *GetMemberContributionExtResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetMemberContributionExtResp) GetContriExtList() []*MemberContributionExt {
	if m != nil {
		return m.ContriExtList
	}
	return nil
}

func (m *GetMemberContributionExtResp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type GetGuildMutableContributionReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *GetGuildMutableContributionReq) Reset()         { *m = GetGuildMutableContributionReq{} }
func (m *GetGuildMutableContributionReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildMutableContributionReq) ProtoMessage()    {}
func (*GetGuildMutableContributionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{8}
}

func (m *GetGuildMutableContributionReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildMutableContributionResp struct {
	MutableContribution uint32 `protobuf:"varint,1,req,name=mutable_contribution,json=mutableContribution" json:"mutable_contribution"`
}

func (m *GetGuildMutableContributionResp) Reset()         { *m = GetGuildMutableContributionResp{} }
func (m *GetGuildMutableContributionResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildMutableContributionResp) ProtoMessage()    {}
func (*GetGuildMutableContributionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{9}
}

func (m *GetGuildMutableContributionResp) GetMutableContribution() uint32 {
	if m != nil {
		return m.MutableContribution
	}
	return 0
}

type AddGuildMutableContributionReq struct {
	GuildId     uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	ChangeValue int32  `protobuf:"varint,2,req,name=change_value,json=changeValue" json:"change_value"`
	OrderId     string `protobuf:"bytes,3,req,name=order_id,json=orderId" json:"order_id"`
	OrderDesc   string `protobuf:"bytes,4,req,name=order_desc,json=orderDesc" json:"order_desc"`
	OpUid       uint32 `protobuf:"varint,5,req,name=op_uid,json=opUid" json:"op_uid"`
}

func (m *AddGuildMutableContributionReq) Reset()         { *m = AddGuildMutableContributionReq{} }
func (m *AddGuildMutableContributionReq) String() string { return proto.CompactTextString(m) }
func (*AddGuildMutableContributionReq) ProtoMessage()    {}
func (*AddGuildMutableContributionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{10}
}

func (m *AddGuildMutableContributionReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AddGuildMutableContributionReq) GetChangeValue() int32 {
	if m != nil {
		return m.ChangeValue
	}
	return 0
}

func (m *AddGuildMutableContributionReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AddGuildMutableContributionReq) GetOrderDesc() string {
	if m != nil {
		return m.OrderDesc
	}
	return ""
}

func (m *AddGuildMutableContributionReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

type AddGuildMutableContributionResp struct {
}

func (m *AddGuildMutableContributionResp) Reset()         { *m = AddGuildMutableContributionResp{} }
func (m *AddGuildMutableContributionResp) String() string { return proto.CompactTextString(m) }
func (*AddGuildMutableContributionResp) ProtoMessage()    {}
func (*AddGuildMutableContributionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{11}
}

type MemberQuitGuildReq struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *MemberQuitGuildReq) Reset()                    { *m = MemberQuitGuildReq{} }
func (m *MemberQuitGuildReq) String() string            { return proto.CompactTextString(m) }
func (*MemberQuitGuildReq) ProtoMessage()               {}
func (*MemberQuitGuildReq) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv3, []int{12} }

func (m *MemberQuitGuildReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MemberQuitGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildInfoByLevelReq struct {
	GuildIdList []uint32 `protobuf:"varint,1,rep,name=guild_id_list,json=guildIdList" json:"guild_id_list,omitempty"`
	LevelBegin  uint32   `protobuf:"varint,2,opt,name=level_begin,json=levelBegin" json:"level_begin"`
	LevelEnd    uint32   `protobuf:"varint,3,opt,name=level_end,json=levelEnd" json:"level_end"`
	Limit       uint32   `protobuf:"varint,4,req,name=limit" json:"limit"`
}

func (m *GetGuildInfoByLevelReq) Reset()         { *m = GetGuildInfoByLevelReq{} }
func (m *GetGuildInfoByLevelReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildInfoByLevelReq) ProtoMessage()    {}
func (*GetGuildInfoByLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{13}
}

func (m *GetGuildInfoByLevelReq) GetGuildIdList() []uint32 {
	if m != nil {
		return m.GuildIdList
	}
	return nil
}

func (m *GetGuildInfoByLevelReq) GetLevelBegin() uint32 {
	if m != nil {
		return m.LevelBegin
	}
	return 0
}

func (m *GetGuildInfoByLevelReq) GetLevelEnd() uint32 {
	if m != nil {
		return m.LevelEnd
	}
	return 0
}

func (m *GetGuildInfoByLevelReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetGuildInfoByLevelObj struct {
	GuildId             uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	StarLevel           uint32 `protobuf:"varint,2,req,name=star_level,json=starLevel" json:"star_level"`
	Contribution        uint32 `protobuf:"varint,3,req,name=contribution" json:"contribution"`
	ContributionPercent uint32 `protobuf:"varint,4,req,name=contribution_percent,json=contributionPercent" json:"contribution_percent"`
}

func (m *GetGuildInfoByLevelObj) Reset()         { *m = GetGuildInfoByLevelObj{} }
func (m *GetGuildInfoByLevelObj) String() string { return proto.CompactTextString(m) }
func (*GetGuildInfoByLevelObj) ProtoMessage()    {}
func (*GetGuildInfoByLevelObj) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{14}
}

func (m *GetGuildInfoByLevelObj) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildInfoByLevelObj) GetStarLevel() uint32 {
	if m != nil {
		return m.StarLevel
	}
	return 0
}

func (m *GetGuildInfoByLevelObj) GetContribution() uint32 {
	if m != nil {
		return m.Contribution
	}
	return 0
}

func (m *GetGuildInfoByLevelObj) GetContributionPercent() uint32 {
	if m != nil {
		return m.ContributionPercent
	}
	return 0
}

type GetGuildInfoByLevelResp struct {
	ObjList []*GetGuildInfoByLevelObj `protobuf:"bytes,1,rep,name=obj_list,json=objList" json:"obj_list,omitempty"`
}

func (m *GetGuildInfoByLevelResp) Reset()         { *m = GetGuildInfoByLevelResp{} }
func (m *GetGuildInfoByLevelResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildInfoByLevelResp) ProtoMessage()    {}
func (*GetGuildInfoByLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{15}
}

func (m *GetGuildInfoByLevelResp) GetObjList() []*GetGuildInfoByLevelObj {
	if m != nil {
		return m.ObjList
	}
	return nil
}

type CalcSingleGuildReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *CalcSingleGuildReq) Reset()                    { *m = CalcSingleGuildReq{} }
func (m *CalcSingleGuildReq) String() string            { return proto.CompactTextString(m) }
func (*CalcSingleGuildReq) ProtoMessage()               {}
func (*CalcSingleGuildReq) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv3, []int{16} }

func (m *CalcSingleGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

// 以下为统计查询接口
type GetGuildLevelCountByDateReq struct {
	DateFrom uint32 `protobuf:"varint,1,req,name=date_from,json=dateFrom" json:"date_from"`
	DateTo   uint32 `protobuf:"varint,2,req,name=date_to,json=dateTo" json:"date_to"`
}

func (m *GetGuildLevelCountByDateReq) Reset()         { *m = GetGuildLevelCountByDateReq{} }
func (m *GetGuildLevelCountByDateReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildLevelCountByDateReq) ProtoMessage()    {}
func (*GetGuildLevelCountByDateReq) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{17}
}

func (m *GetGuildLevelCountByDateReq) GetDateFrom() uint32 {
	if m != nil {
		return m.DateFrom
	}
	return 0
}

func (m *GetGuildLevelCountByDateReq) GetDateTo() uint32 {
	if m != nil {
		return m.DateTo
	}
	return 0
}

type LevelCount struct {
	StarLevel  uint32 `protobuf:"varint,1,req,name=star_level,json=starLevel" json:"star_level"`
	GuildCount uint32 `protobuf:"varint,2,req,name=guild_count,json=guildCount" json:"guild_count"`
}

func (m *LevelCount) Reset()                    { *m = LevelCount{} }
func (m *LevelCount) String() string            { return proto.CompactTextString(m) }
func (*LevelCount) ProtoMessage()               {}
func (*LevelCount) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv3, []int{18} }

func (m *LevelCount) GetStarLevel() uint32 {
	if m != nil {
		return m.StarLevel
	}
	return 0
}

func (m *LevelCount) GetGuildCount() uint32 {
	if m != nil {
		return m.GuildCount
	}
	return 0
}

type GuildLevelCount struct {
	Date           uint32        `protobuf:"varint,1,req,name=date" json:"date"`
	LevelCountList []*LevelCount `protobuf:"bytes,2,rep,name=level_count_list,json=levelCountList" json:"level_count_list,omitempty"`
}

func (m *GuildLevelCount) Reset()                    { *m = GuildLevelCount{} }
func (m *GuildLevelCount) String() string            { return proto.CompactTextString(m) }
func (*GuildLevelCount) ProtoMessage()               {}
func (*GuildLevelCount) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv3, []int{19} }

func (m *GuildLevelCount) GetDate() uint32 {
	if m != nil {
		return m.Date
	}
	return 0
}

func (m *GuildLevelCount) GetLevelCountList() []*LevelCount {
	if m != nil {
		return m.LevelCountList
	}
	return nil
}

type GetGuildLevelCountByDateResp struct {
	GuildLevelList []*GuildLevelCount `protobuf:"bytes,1,rep,name=guild_level_list,json=guildLevelList" json:"guild_level_list,omitempty"`
}

func (m *GetGuildLevelCountByDateResp) Reset()         { *m = GetGuildLevelCountByDateResp{} }
func (m *GetGuildLevelCountByDateResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildLevelCountByDateResp) ProtoMessage()    {}
func (*GetGuildLevelCountByDateResp) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{20}
}

func (m *GetGuildLevelCountByDateResp) GetGuildLevelList() []*GuildLevelCount {
	if m != nil {
		return m.GuildLevelList
	}
	return nil
}

type GetGuildInfoByDateReq struct {
	Date      uint32 `protobuf:"varint,1,req,name=date" json:"date"`
	StarLevel uint32 `protobuf:"varint,2,req,name=star_level,json=starLevel" json:"star_level"`
	Start     uint32 `protobuf:"varint,3,req,name=start" json:"start"`
	Limit     uint32 `protobuf:"varint,4,req,name=limit" json:"limit"`
}

func (m *GetGuildInfoByDateReq) Reset()         { *m = GetGuildInfoByDateReq{} }
func (m *GetGuildInfoByDateReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildInfoByDateReq) ProtoMessage()    {}
func (*GetGuildInfoByDateReq) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{21}
}

func (m *GetGuildInfoByDateReq) GetDate() uint32 {
	if m != nil {
		return m.Date
	}
	return 0
}

func (m *GetGuildInfoByDateReq) GetStarLevel() uint32 {
	if m != nil {
		return m.StarLevel
	}
	return 0
}

func (m *GetGuildInfoByDateReq) GetStart() uint32 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *GetGuildInfoByDateReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GuildInfoByDate struct {
	GuildId     uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	MemberCount uint32 `protobuf:"varint,2,req,name=member_count,json=memberCount" json:"member_count"`
}

func (m *GuildInfoByDate) Reset()                    { *m = GuildInfoByDate{} }
func (m *GuildInfoByDate) String() string            { return proto.CompactTextString(m) }
func (*GuildInfoByDate) ProtoMessage()               {}
func (*GuildInfoByDate) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv3, []int{22} }

func (m *GuildInfoByDate) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildInfoByDate) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

type GetGuildInfoByDateResp struct {
	GuildInfoList []*GuildInfoByDate `protobuf:"bytes,1,rep,name=guild_info_list,json=guildInfoList" json:"guild_info_list,omitempty"`
}

func (m *GetGuildInfoByDateResp) Reset()         { *m = GetGuildInfoByDateResp{} }
func (m *GetGuildInfoByDateResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildInfoByDateResp) ProtoMessage()    {}
func (*GetGuildInfoByDateResp) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{23}
}

func (m *GetGuildInfoByDateResp) GetGuildInfoList() []*GuildInfoByDate {
	if m != nil {
		return m.GuildInfoList
	}
	return nil
}

type GetRankListByContributionReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	Start   uint32 `protobuf:"varint,2,req,name=start" json:"start"`
	Limit   uint32 `protobuf:"varint,3,req,name=limit" json:"limit"`
}

func (m *GetRankListByContributionReq) Reset()         { *m = GetRankListByContributionReq{} }
func (m *GetRankListByContributionReq) String() string { return proto.CompactTextString(m) }
func (*GetRankListByContributionReq) ProtoMessage()    {}
func (*GetRankListByContributionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{24}
}

func (m *GetRankListByContributionReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetRankListByContributionReq) GetStart() uint32 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *GetRankListByContributionReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type RankListByContribution struct {
	GuildId      uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	Contribution uint32 `protobuf:"varint,2,req,name=contribution" json:"contribution"`
	Consume      uint32 `protobuf:"varint,3,opt,name=consume" json:"consume"`
}

func (m *RankListByContribution) Reset()         { *m = RankListByContribution{} }
func (m *RankListByContribution) String() string { return proto.CompactTextString(m) }
func (*RankListByContribution) ProtoMessage()    {}
func (*RankListByContribution) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{25}
}

func (m *RankListByContribution) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *RankListByContribution) GetContribution() uint32 {
	if m != nil {
		return m.Contribution
	}
	return 0
}

func (m *RankListByContribution) GetConsume() uint32 {
	if m != nil {
		return m.Consume
	}
	return 0
}

type GetRankListByContributionResp struct {
	RankList       []*RankListByContribution `protobuf:"bytes,1,rep,name=rank_list,json=rankList" json:"rank_list,omitempty"`
	MyRank         uint32                    `protobuf:"varint,2,req,name=my_rank,json=myRank" json:"my_rank"`
	MyContribution uint32                    `protobuf:"varint,3,req,name=my_contribution,json=myContribution" json:"my_contribution"`
}

func (m *GetRankListByContributionResp) Reset()         { *m = GetRankListByContributionResp{} }
func (m *GetRankListByContributionResp) String() string { return proto.CompactTextString(m) }
func (*GetRankListByContributionResp) ProtoMessage()    {}
func (*GetRankListByContributionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{26}
}

func (m *GetRankListByContributionResp) GetRankList() []*RankListByContribution {
	if m != nil {
		return m.RankList
	}
	return nil
}

func (m *GetRankListByContributionResp) GetMyRank() uint32 {
	if m != nil {
		return m.MyRank
	}
	return 0
}

func (m *GetRankListByContributionResp) GetMyContribution() uint32 {
	if m != nil {
		return m.MyContribution
	}
	return 0
}

type ReportDismissGuildReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *ReportDismissGuildReq) Reset()         { *m = ReportDismissGuildReq{} }
func (m *ReportDismissGuildReq) String() string { return proto.CompactTextString(m) }
func (*ReportDismissGuildReq) ProtoMessage()    {}
func (*ReportDismissGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{27}
}

func (m *ReportDismissGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildLevelMemberHistoryReq struct {
	Date        uint32   `protobuf:"varint,1,req,name=date" json:"date"`
	GuildIdList []uint32 `protobuf:"varint,2,rep,name=guild_id_list,json=guildIdList" json:"guild_id_list,omitempty"`
}

func (m *GetGuildLevelMemberHistoryReq) Reset()         { *m = GetGuildLevelMemberHistoryReq{} }
func (m *GetGuildLevelMemberHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildLevelMemberHistoryReq) ProtoMessage()    {}
func (*GetGuildLevelMemberHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{28}
}

func (m *GetGuildLevelMemberHistoryReq) GetDate() uint32 {
	if m != nil {
		return m.Date
	}
	return 0
}

func (m *GetGuildLevelMemberHistoryReq) GetGuildIdList() []uint32 {
	if m != nil {
		return m.GuildIdList
	}
	return nil
}

type GuildLevelMemberHistory struct {
	GuildId          uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	GuildLevel       uint32 `protobuf:"varint,2,req,name=guild_level,json=guildLevel" json:"guild_level"`
	GuildMemberCount uint32 `protobuf:"varint,3,req,name=guild_member_count,json=guildMemberCount" json:"guild_member_count"`
}

func (m *GuildLevelMemberHistory) Reset()         { *m = GuildLevelMemberHistory{} }
func (m *GuildLevelMemberHistory) String() string { return proto.CompactTextString(m) }
func (*GuildLevelMemberHistory) ProtoMessage()    {}
func (*GuildLevelMemberHistory) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{29}
}

func (m *GuildLevelMemberHistory) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildLevelMemberHistory) GetGuildLevel() uint32 {
	if m != nil {
		return m.GuildLevel
	}
	return 0
}

func (m *GuildLevelMemberHistory) GetGuildMemberCount() uint32 {
	if m != nil {
		return m.GuildMemberCount
	}
	return 0
}

type GetGuildLevelMemberHistoryResp struct {
	ResultList []*GuildLevelMemberHistory `protobuf:"bytes,1,rep,name=result_list,json=resultList" json:"result_list,omitempty"`
}

func (m *GetGuildLevelMemberHistoryResp) Reset()         { *m = GetGuildLevelMemberHistoryResp{} }
func (m *GetGuildLevelMemberHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildLevelMemberHistoryResp) ProtoMessage()    {}
func (*GetGuildLevelMemberHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{30}
}

func (m *GetGuildLevelMemberHistoryResp) GetResultList() []*GuildLevelMemberHistory {
	if m != nil {
		return m.ResultList
	}
	return nil
}

type GuildMonthRank struct {
	Uid   uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Value uint32 `protobuf:"varint,2,req,name=value" json:"value"`
}

func (m *GuildMonthRank) Reset()                    { *m = GuildMonthRank{} }
func (m *GuildMonthRank) String() string            { return proto.CompactTextString(m) }
func (*GuildMonthRank) ProtoMessage()               {}
func (*GuildMonthRank) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv3, []int{31} }

func (m *GuildMonthRank) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GuildMonthRank) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

type GetGuildMonthRankReq struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	ValueType uint32 `protobuf:"varint,2,req,name=value_type,json=valueType" json:"value_type"`
	Start     uint32 `protobuf:"varint,3,req,name=start" json:"start"`
	Limit     uint32 `protobuf:"varint,4,req,name=limit" json:"limit"`
}

func (m *GetGuildMonthRankReq) Reset()                    { *m = GetGuildMonthRankReq{} }
func (m *GetGuildMonthRankReq) String() string            { return proto.CompactTextString(m) }
func (*GetGuildMonthRankReq) ProtoMessage()               {}
func (*GetGuildMonthRankReq) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv3, []int{32} }

func (m *GetGuildMonthRankReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildMonthRankReq) GetValueType() uint32 {
	if m != nil {
		return m.ValueType
	}
	return 0
}

func (m *GetGuildMonthRankReq) GetStart() uint32 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *GetGuildMonthRankReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetGuildMonthRankResp struct {
	RankList []*GuildMonthRank `protobuf:"bytes,1,rep,name=rank_list,json=rankList" json:"rank_list,omitempty"`
}

func (m *GetGuildMonthRankResp) Reset()         { *m = GetGuildMonthRankResp{} }
func (m *GetGuildMonthRankResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildMonthRankResp) ProtoMessage()    {}
func (*GetGuildMonthRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{33}
}

func (m *GetGuildMonthRankResp) GetRankList() []*GuildMonthRank {
	if m != nil {
		return m.RankList
	}
	return nil
}

type GetGuildEventCountReq struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	EventType uint32 `protobuf:"varint,2,req,name=event_type,json=eventType" json:"event_type"`
	DtTime    uint32 `protobuf:"varint,3,req,name=dt_time,json=dtTime" json:"dt_time"`
}

func (m *GetGuildEventCountReq) Reset()         { *m = GetGuildEventCountReq{} }
func (m *GetGuildEventCountReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildEventCountReq) ProtoMessage()    {}
func (*GetGuildEventCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{34}
}

func (m *GetGuildEventCountReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildEventCountReq) GetEventType() uint32 {
	if m != nil {
		return m.EventType
	}
	return 0
}

func (m *GetGuildEventCountReq) GetDtTime() uint32 {
	if m != nil {
		return m.DtTime
	}
	return 0
}

type GetGuildEventCountResp struct {
	Count uint32 `protobuf:"varint,1,req,name=count" json:"count"`
}

func (m *GetGuildEventCountResp) Reset()         { *m = GetGuildEventCountResp{} }
func (m *GetGuildEventCountResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildEventCountResp) ProtoMessage()    {}
func (*GetGuildEventCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv3, []int{35}
}

func (m *GetGuildEventCountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func init() {
	proto.RegisterType((*UserAddContributeReq)(nil), "starguildv3.UserAddContributeReq")
	proto.RegisterType((*UserAddContributeResp)(nil), "starguildv3.UserAddContributeResp")
	proto.RegisterType((*StarGuildInfo)(nil), "starguildv3.StarGuildInfo")
	proto.RegisterType((*GetGuildStarInfosReq)(nil), "starguildv3.GetGuildStarInfosReq")
	proto.RegisterType((*GetGuildStarInfosResp)(nil), "starguildv3.GetGuildStarInfosResp")
	proto.RegisterType((*GetMemberContributionExtReq)(nil), "starguildv3.GetMemberContributionExtReq")
	proto.RegisterType((*MemberContributionExt)(nil), "starguildv3.MemberContributionExt")
	proto.RegisterType((*GetMemberContributionExtResp)(nil), "starguildv3.GetMemberContributionExtResp")
	proto.RegisterType((*GetGuildMutableContributionReq)(nil), "starguildv3.GetGuildMutableContributionReq")
	proto.RegisterType((*GetGuildMutableContributionResp)(nil), "starguildv3.GetGuildMutableContributionResp")
	proto.RegisterType((*AddGuildMutableContributionReq)(nil), "starguildv3.AddGuildMutableContributionReq")
	proto.RegisterType((*AddGuildMutableContributionResp)(nil), "starguildv3.AddGuildMutableContributionResp")
	proto.RegisterType((*MemberQuitGuildReq)(nil), "starguildv3.MemberQuitGuildReq")
	proto.RegisterType((*GetGuildInfoByLevelReq)(nil), "starguildv3.GetGuildInfoByLevelReq")
	proto.RegisterType((*GetGuildInfoByLevelObj)(nil), "starguildv3.GetGuildInfoByLevelObj")
	proto.RegisterType((*GetGuildInfoByLevelResp)(nil), "starguildv3.GetGuildInfoByLevelResp")
	proto.RegisterType((*CalcSingleGuildReq)(nil), "starguildv3.CalcSingleGuildReq")
	proto.RegisterType((*GetGuildLevelCountByDateReq)(nil), "starguildv3.GetGuildLevelCountByDateReq")
	proto.RegisterType((*LevelCount)(nil), "starguildv3.LevelCount")
	proto.RegisterType((*GuildLevelCount)(nil), "starguildv3.GuildLevelCount")
	proto.RegisterType((*GetGuildLevelCountByDateResp)(nil), "starguildv3.GetGuildLevelCountByDateResp")
	proto.RegisterType((*GetGuildInfoByDateReq)(nil), "starguildv3.GetGuildInfoByDateReq")
	proto.RegisterType((*GuildInfoByDate)(nil), "starguildv3.GuildInfoByDate")
	proto.RegisterType((*GetGuildInfoByDateResp)(nil), "starguildv3.GetGuildInfoByDateResp")
	proto.RegisterType((*GetRankListByContributionReq)(nil), "starguildv3.GetRankListByContributionReq")
	proto.RegisterType((*RankListByContribution)(nil), "starguildv3.RankListByContribution")
	proto.RegisterType((*GetRankListByContributionResp)(nil), "starguildv3.GetRankListByContributionResp")
	proto.RegisterType((*ReportDismissGuildReq)(nil), "starguildv3.ReportDismissGuildReq")
	proto.RegisterType((*GetGuildLevelMemberHistoryReq)(nil), "starguildv3.GetGuildLevelMemberHistoryReq")
	proto.RegisterType((*GuildLevelMemberHistory)(nil), "starguildv3.GuildLevelMemberHistory")
	proto.RegisterType((*GetGuildLevelMemberHistoryResp)(nil), "starguildv3.GetGuildLevelMemberHistoryResp")
	proto.RegisterType((*GuildMonthRank)(nil), "starguildv3.GuildMonthRank")
	proto.RegisterType((*GetGuildMonthRankReq)(nil), "starguildv3.GetGuildMonthRankReq")
	proto.RegisterType((*GetGuildMonthRankResp)(nil), "starguildv3.GetGuildMonthRankResp")
	proto.RegisterType((*GetGuildEventCountReq)(nil), "starguildv3.GetGuildEventCountReq")
	proto.RegisterType((*GetGuildEventCountResp)(nil), "starguildv3.GetGuildEventCountResp")
	proto.RegisterEnum("starguildv3.UserConsumeType", UserConsumeType_name, UserConsumeType_value)
	proto.RegisterEnum("starguildv3.UserOperateType", UserOperateType_name, UserOperateType_value)
	proto.RegisterEnum("starguildv3.GetMemberContributionExtReq_TIME_LIMIT", GetMemberContributionExtReq_TIME_LIMIT_name, GetMemberContributionExtReq_TIME_LIMIT_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for StarGuildV3 service

type StarGuildV3Client interface {
	UserAddContribute(ctx context.Context, in *UserAddContributeReq, opts ...grpc.CallOption) (*UserAddContributeResp, error)
	GetGuildStarInfos(ctx context.Context, in *GetGuildStarInfosReq, opts ...grpc.CallOption) (*GetGuildStarInfosResp, error)
	GetMemberContributionExt(ctx context.Context, in *GetMemberContributionExtReq, opts ...grpc.CallOption) (*GetMemberContributionExtResp, error)
	CalcYesterdayManually(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildMutableContribution(ctx context.Context, in *GetGuildMutableContributionReq, opts ...grpc.CallOption) (*GetGuildMutableContributionResp, error)
	AddGuildMutableContribution(ctx context.Context, in *AddGuildMutableContributionReq, opts ...grpc.CallOption) (*AddGuildMutableContributionResp, error)
	MemberQuitGuild(ctx context.Context, in *MemberQuitGuildReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildInfoByLevel(ctx context.Context, in *GetGuildInfoByLevelReq, opts ...grpc.CallOption) (*GetGuildInfoByLevelResp, error)
	CalcSingleGuild(ctx context.Context, in *CalcSingleGuildReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildLevelCountByDate(ctx context.Context, in *GetGuildLevelCountByDateReq, opts ...grpc.CallOption) (*GetGuildLevelCountByDateResp, error)
	GetGuildInfoByDate(ctx context.Context, in *GetGuildInfoByDateReq, opts ...grpc.CallOption) (*GetGuildInfoByDateResp, error)
	GetRankListByContribution(ctx context.Context, in *GetRankListByContributionReq, opts ...grpc.CallOption) (*GetRankListByContributionResp, error)
	ReportDismissGuild(ctx context.Context, in *ReportDismissGuildReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildLevelMemberHistory(ctx context.Context, in *GetGuildLevelMemberHistoryReq, opts ...grpc.CallOption) (*GetGuildLevelMemberHistoryResp, error)
	GetGuildMonthRank(ctx context.Context, in *GetGuildMonthRankReq, opts ...grpc.CallOption) (*GetGuildMonthRankResp, error)
	GetGuildEventCount(ctx context.Context, in *GetGuildEventCountReq, opts ...grpc.CallOption) (*GetGuildEventCountResp, error)
}

type starGuildV3Client struct {
	cc *grpc.ClientConn
}

func NewStarGuildV3Client(cc *grpc.ClientConn) StarGuildV3Client {
	return &starGuildV3Client{cc}
}

func (c *starGuildV3Client) UserAddContribute(ctx context.Context, in *UserAddContributeReq, opts ...grpc.CallOption) (*UserAddContributeResp, error) {
	out := new(UserAddContributeResp)
	err := grpc.Invoke(ctx, "/starguildv3.StarGuildV3/UserAddContribute", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starGuildV3Client) GetGuildStarInfos(ctx context.Context, in *GetGuildStarInfosReq, opts ...grpc.CallOption) (*GetGuildStarInfosResp, error) {
	out := new(GetGuildStarInfosResp)
	err := grpc.Invoke(ctx, "/starguildv3.StarGuildV3/GetGuildStarInfos", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starGuildV3Client) GetMemberContributionExt(ctx context.Context, in *GetMemberContributionExtReq, opts ...grpc.CallOption) (*GetMemberContributionExtResp, error) {
	out := new(GetMemberContributionExtResp)
	err := grpc.Invoke(ctx, "/starguildv3.StarGuildV3/GetMemberContributionExt", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starGuildV3Client) CalcYesterdayManually(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/starguildv3.StarGuildV3/CalcYesterdayManually", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starGuildV3Client) GetGuildMutableContribution(ctx context.Context, in *GetGuildMutableContributionReq, opts ...grpc.CallOption) (*GetGuildMutableContributionResp, error) {
	out := new(GetGuildMutableContributionResp)
	err := grpc.Invoke(ctx, "/starguildv3.StarGuildV3/GetGuildMutableContribution", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starGuildV3Client) AddGuildMutableContribution(ctx context.Context, in *AddGuildMutableContributionReq, opts ...grpc.CallOption) (*AddGuildMutableContributionResp, error) {
	out := new(AddGuildMutableContributionResp)
	err := grpc.Invoke(ctx, "/starguildv3.StarGuildV3/AddGuildMutableContribution", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starGuildV3Client) MemberQuitGuild(ctx context.Context, in *MemberQuitGuildReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/starguildv3.StarGuildV3/MemberQuitGuild", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starGuildV3Client) GetGuildInfoByLevel(ctx context.Context, in *GetGuildInfoByLevelReq, opts ...grpc.CallOption) (*GetGuildInfoByLevelResp, error) {
	out := new(GetGuildInfoByLevelResp)
	err := grpc.Invoke(ctx, "/starguildv3.StarGuildV3/GetGuildInfoByLevel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starGuildV3Client) CalcSingleGuild(ctx context.Context, in *CalcSingleGuildReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/starguildv3.StarGuildV3/CalcSingleGuild", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starGuildV3Client) GetGuildLevelCountByDate(ctx context.Context, in *GetGuildLevelCountByDateReq, opts ...grpc.CallOption) (*GetGuildLevelCountByDateResp, error) {
	out := new(GetGuildLevelCountByDateResp)
	err := grpc.Invoke(ctx, "/starguildv3.StarGuildV3/GetGuildLevelCountByDate", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starGuildV3Client) GetGuildInfoByDate(ctx context.Context, in *GetGuildInfoByDateReq, opts ...grpc.CallOption) (*GetGuildInfoByDateResp, error) {
	out := new(GetGuildInfoByDateResp)
	err := grpc.Invoke(ctx, "/starguildv3.StarGuildV3/GetGuildInfoByDate", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starGuildV3Client) GetRankListByContribution(ctx context.Context, in *GetRankListByContributionReq, opts ...grpc.CallOption) (*GetRankListByContributionResp, error) {
	out := new(GetRankListByContributionResp)
	err := grpc.Invoke(ctx, "/starguildv3.StarGuildV3/GetRankListByContribution", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starGuildV3Client) ReportDismissGuild(ctx context.Context, in *ReportDismissGuildReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/starguildv3.StarGuildV3/ReportDismissGuild", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starGuildV3Client) GetGuildLevelMemberHistory(ctx context.Context, in *GetGuildLevelMemberHistoryReq, opts ...grpc.CallOption) (*GetGuildLevelMemberHistoryResp, error) {
	out := new(GetGuildLevelMemberHistoryResp)
	err := grpc.Invoke(ctx, "/starguildv3.StarGuildV3/GetGuildLevelMemberHistory", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starGuildV3Client) GetGuildMonthRank(ctx context.Context, in *GetGuildMonthRankReq, opts ...grpc.CallOption) (*GetGuildMonthRankResp, error) {
	out := new(GetGuildMonthRankResp)
	err := grpc.Invoke(ctx, "/starguildv3.StarGuildV3/GetGuildMonthRank", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starGuildV3Client) GetGuildEventCount(ctx context.Context, in *GetGuildEventCountReq, opts ...grpc.CallOption) (*GetGuildEventCountResp, error) {
	out := new(GetGuildEventCountResp)
	err := grpc.Invoke(ctx, "/starguildv3.StarGuildV3/GetGuildEventCount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for StarGuildV3 service

type StarGuildV3Server interface {
	UserAddContribute(context.Context, *UserAddContributeReq) (*UserAddContributeResp, error)
	GetGuildStarInfos(context.Context, *GetGuildStarInfosReq) (*GetGuildStarInfosResp, error)
	GetMemberContributionExt(context.Context, *GetMemberContributionExtReq) (*GetMemberContributionExtResp, error)
	CalcYesterdayManually(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildMutableContribution(context.Context, *GetGuildMutableContributionReq) (*GetGuildMutableContributionResp, error)
	AddGuildMutableContribution(context.Context, *AddGuildMutableContributionReq) (*AddGuildMutableContributionResp, error)
	MemberQuitGuild(context.Context, *MemberQuitGuildReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildInfoByLevel(context.Context, *GetGuildInfoByLevelReq) (*GetGuildInfoByLevelResp, error)
	CalcSingleGuild(context.Context, *CalcSingleGuildReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildLevelCountByDate(context.Context, *GetGuildLevelCountByDateReq) (*GetGuildLevelCountByDateResp, error)
	GetGuildInfoByDate(context.Context, *GetGuildInfoByDateReq) (*GetGuildInfoByDateResp, error)
	GetRankListByContribution(context.Context, *GetRankListByContributionReq) (*GetRankListByContributionResp, error)
	ReportDismissGuild(context.Context, *ReportDismissGuildReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildLevelMemberHistory(context.Context, *GetGuildLevelMemberHistoryReq) (*GetGuildLevelMemberHistoryResp, error)
	GetGuildMonthRank(context.Context, *GetGuildMonthRankReq) (*GetGuildMonthRankResp, error)
	GetGuildEventCount(context.Context, *GetGuildEventCountReq) (*GetGuildEventCountResp, error)
}

func RegisterStarGuildV3Server(s *grpc.Server, srv StarGuildV3Server) {
	s.RegisterService(&_StarGuildV3_serviceDesc, srv)
}

func _StarGuildV3_UserAddContribute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserAddContributeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV3Server).UserAddContribute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv3.StarGuildV3/UserAddContribute",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV3Server).UserAddContribute(ctx, req.(*UserAddContributeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarGuildV3_GetGuildStarInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildStarInfosReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV3Server).GetGuildStarInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv3.StarGuildV3/GetGuildStarInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV3Server).GetGuildStarInfos(ctx, req.(*GetGuildStarInfosReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarGuildV3_GetMemberContributionExt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberContributionExtReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV3Server).GetMemberContributionExt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv3.StarGuildV3/GetMemberContributionExt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV3Server).GetMemberContributionExt(ctx, req.(*GetMemberContributionExtReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarGuildV3_CalcYesterdayManually_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV3Server).CalcYesterdayManually(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv3.StarGuildV3/CalcYesterdayManually",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV3Server).CalcYesterdayManually(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarGuildV3_GetGuildMutableContribution_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildMutableContributionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV3Server).GetGuildMutableContribution(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv3.StarGuildV3/GetGuildMutableContribution",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV3Server).GetGuildMutableContribution(ctx, req.(*GetGuildMutableContributionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarGuildV3_AddGuildMutableContribution_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddGuildMutableContributionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV3Server).AddGuildMutableContribution(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv3.StarGuildV3/AddGuildMutableContribution",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV3Server).AddGuildMutableContribution(ctx, req.(*AddGuildMutableContributionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarGuildV3_MemberQuitGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MemberQuitGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV3Server).MemberQuitGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv3.StarGuildV3/MemberQuitGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV3Server).MemberQuitGuild(ctx, req.(*MemberQuitGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarGuildV3_GetGuildInfoByLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildInfoByLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV3Server).GetGuildInfoByLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv3.StarGuildV3/GetGuildInfoByLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV3Server).GetGuildInfoByLevel(ctx, req.(*GetGuildInfoByLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarGuildV3_CalcSingleGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalcSingleGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV3Server).CalcSingleGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv3.StarGuildV3/CalcSingleGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV3Server).CalcSingleGuild(ctx, req.(*CalcSingleGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarGuildV3_GetGuildLevelCountByDate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildLevelCountByDateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV3Server).GetGuildLevelCountByDate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv3.StarGuildV3/GetGuildLevelCountByDate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV3Server).GetGuildLevelCountByDate(ctx, req.(*GetGuildLevelCountByDateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarGuildV3_GetGuildInfoByDate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildInfoByDateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV3Server).GetGuildInfoByDate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv3.StarGuildV3/GetGuildInfoByDate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV3Server).GetGuildInfoByDate(ctx, req.(*GetGuildInfoByDateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarGuildV3_GetRankListByContribution_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRankListByContributionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV3Server).GetRankListByContribution(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv3.StarGuildV3/GetRankListByContribution",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV3Server).GetRankListByContribution(ctx, req.(*GetRankListByContributionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarGuildV3_ReportDismissGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportDismissGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV3Server).ReportDismissGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv3.StarGuildV3/ReportDismissGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV3Server).ReportDismissGuild(ctx, req.(*ReportDismissGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarGuildV3_GetGuildLevelMemberHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildLevelMemberHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV3Server).GetGuildLevelMemberHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv3.StarGuildV3/GetGuildLevelMemberHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV3Server).GetGuildLevelMemberHistory(ctx, req.(*GetGuildLevelMemberHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarGuildV3_GetGuildMonthRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildMonthRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV3Server).GetGuildMonthRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv3.StarGuildV3/GetGuildMonthRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV3Server).GetGuildMonthRank(ctx, req.(*GetGuildMonthRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarGuildV3_GetGuildEventCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildEventCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV3Server).GetGuildEventCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv3.StarGuildV3/GetGuildEventCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV3Server).GetGuildEventCount(ctx, req.(*GetGuildEventCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _StarGuildV3_serviceDesc = grpc.ServiceDesc{
	ServiceName: "starguildv3.StarGuildV3",
	HandlerType: (*StarGuildV3Server)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UserAddContribute",
			Handler:    _StarGuildV3_UserAddContribute_Handler,
		},
		{
			MethodName: "GetGuildStarInfos",
			Handler:    _StarGuildV3_GetGuildStarInfos_Handler,
		},
		{
			MethodName: "GetMemberContributionExt",
			Handler:    _StarGuildV3_GetMemberContributionExt_Handler,
		},
		{
			MethodName: "CalcYesterdayManually",
			Handler:    _StarGuildV3_CalcYesterdayManually_Handler,
		},
		{
			MethodName: "GetGuildMutableContribution",
			Handler:    _StarGuildV3_GetGuildMutableContribution_Handler,
		},
		{
			MethodName: "AddGuildMutableContribution",
			Handler:    _StarGuildV3_AddGuildMutableContribution_Handler,
		},
		{
			MethodName: "MemberQuitGuild",
			Handler:    _StarGuildV3_MemberQuitGuild_Handler,
		},
		{
			MethodName: "GetGuildInfoByLevel",
			Handler:    _StarGuildV3_GetGuildInfoByLevel_Handler,
		},
		{
			MethodName: "CalcSingleGuild",
			Handler:    _StarGuildV3_CalcSingleGuild_Handler,
		},
		{
			MethodName: "GetGuildLevelCountByDate",
			Handler:    _StarGuildV3_GetGuildLevelCountByDate_Handler,
		},
		{
			MethodName: "GetGuildInfoByDate",
			Handler:    _StarGuildV3_GetGuildInfoByDate_Handler,
		},
		{
			MethodName: "GetRankListByContribution",
			Handler:    _StarGuildV3_GetRankListByContribution_Handler,
		},
		{
			MethodName: "ReportDismissGuild",
			Handler:    _StarGuildV3_ReportDismissGuild_Handler,
		},
		{
			MethodName: "GetGuildLevelMemberHistory",
			Handler:    _StarGuildV3_GetGuildLevelMemberHistory_Handler,
		},
		{
			MethodName: "GetGuildMonthRank",
			Handler:    _StarGuildV3_GetGuildMonthRank_Handler,
		},
		{
			MethodName: "GetGuildEventCount",
			Handler:    _StarGuildV3_GetGuildEventCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "starguildsvr/v3/starguildv3.proto",
}

func (m *UserAddContributeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserAddContributeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.EventType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.ReddiamondAmount))
	dAtA[i] = 0x28
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.MoneyAmount))
	dAtA[i] = 0x30
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.ConsumeType))
	dAtA[i] = 0x38
	i++
	if m.Valid {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x42
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	return i, nil
}

func (m *UserAddContributeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserAddContributeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.ValueAdded))
	return i, nil
}

func (m *StarGuildInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StarGuildInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.StarLevel))
	dAtA[i] = 0x18
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.ContributionBase))
	dAtA[i] = 0x20
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.ContributionExt))
	dAtA[i] = 0x28
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.ContributionExtPay))
	dAtA[i] = 0x30
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.ContributionExtPercent))
	dAtA[i] = 0x38
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.ContributionNextPay))
	dAtA[i] = 0x40
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.ContributionNextPercent))
	dAtA[i] = 0x48
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.ContributionNextLevel))
	dAtA[i] = 0x50
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.ContributionCurLevel))
	dAtA[i] = 0x58
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.JoinNumYesterday))
	dAtA[i] = 0x60
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Contribution_30DaysAgo))
	dAtA[i] = 0x68
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.ContributionToday))
	dAtA[i] = 0x70
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.LastCalcTime))
	return i, nil
}

func (m *GetGuildStarInfosReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildStarInfosReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GuildIdList) > 0 {
		for _, num := range m.GuildIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintStarguildv3(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	if m.QueryToday {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetGuildStarInfosResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildStarInfosResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.StarInfoList) > 0 {
		for _, msg := range m.StarInfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintStarguildv3(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetMemberContributionExtReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMemberContributionExtReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Start))
	dAtA[i] = 0x18
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Limit))
	dAtA[i] = 0x20
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.TimeLimit))
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x28
			i++
			i = encodeVarintStarguildv3(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *MemberContributionExt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MemberContributionExt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Total))
	dAtA[i] = 0x18
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GameConsume))
	dAtA[i] = 0x20
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.LiveConsume))
	dAtA[i] = 0x28
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.FunnyRoomGift))
	return i, nil
}

func (m *GetMemberContributionExtResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMemberContributionExtResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GuildId))
	if len(m.ContriExtList) > 0 {
		for _, msg := range m.ContriExtList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintStarguildv3(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.TotalCount))
	return i, nil
}

func (m *GetGuildMutableContributionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildMutableContributionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GetGuildMutableContributionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildMutableContributionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.MutableContribution))
	return i, nil
}

func (m *AddGuildMutableContributionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddGuildMutableContributionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.ChangeValue))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x22
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(len(m.OrderDesc)))
	i += copy(dAtA[i:], m.OrderDesc)
	dAtA[i] = 0x28
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.OpUid))
	return i, nil
}

func (m *AddGuildMutableContributionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddGuildMutableContributionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *MemberQuitGuildReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MemberQuitGuildReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GetGuildInfoByLevelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildInfoByLevelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GuildIdList) > 0 {
		for _, num := range m.GuildIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintStarguildv3(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.LevelBegin))
	dAtA[i] = 0x18
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.LevelEnd))
	dAtA[i] = 0x20
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GetGuildInfoByLevelObj) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildInfoByLevelObj) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.StarLevel))
	dAtA[i] = 0x18
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Contribution))
	dAtA[i] = 0x20
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.ContributionPercent))
	return i, nil
}

func (m *GetGuildInfoByLevelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildInfoByLevelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ObjList) > 0 {
		for _, msg := range m.ObjList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintStarguildv3(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CalcSingleGuildReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CalcSingleGuildReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GetGuildLevelCountByDateReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildLevelCountByDateReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.DateFrom))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.DateTo))
	return i, nil
}

func (m *LevelCount) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LevelCount) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.StarLevel))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GuildCount))
	return i, nil
}

func (m *GuildLevelCount) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildLevelCount) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Date))
	if len(m.LevelCountList) > 0 {
		for _, msg := range m.LevelCountList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintStarguildv3(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGuildLevelCountByDateResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildLevelCountByDateResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GuildLevelList) > 0 {
		for _, msg := range m.GuildLevelList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintStarguildv3(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGuildInfoByDateReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildInfoByDateReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Date))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.StarLevel))
	dAtA[i] = 0x18
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Start))
	dAtA[i] = 0x20
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GuildInfoByDate) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildInfoByDate) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.MemberCount))
	return i, nil
}

func (m *GetGuildInfoByDateResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildInfoByDateResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GuildInfoList) > 0 {
		for _, msg := range m.GuildInfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintStarguildv3(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetRankListByContributionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRankListByContributionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Start))
	dAtA[i] = 0x18
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *RankListByContribution) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RankListByContribution) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Contribution))
	dAtA[i] = 0x18
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Consume))
	return i, nil
}

func (m *GetRankListByContributionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRankListByContributionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RankList) > 0 {
		for _, msg := range m.RankList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintStarguildv3(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.MyRank))
	dAtA[i] = 0x18
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.MyContribution))
	return i, nil
}

func (m *ReportDismissGuildReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportDismissGuildReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GetGuildLevelMemberHistoryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildLevelMemberHistoryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Date))
	if len(m.GuildIdList) > 0 {
		for _, num := range m.GuildIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintStarguildv3(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GuildLevelMemberHistory) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildLevelMemberHistory) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GuildLevel))
	dAtA[i] = 0x18
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GuildMemberCount))
	return i, nil
}

func (m *GetGuildLevelMemberHistoryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildLevelMemberHistoryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ResultList) > 0 {
		for _, msg := range m.ResultList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintStarguildv3(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GuildMonthRank) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildMonthRank) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Value))
	return i, nil
}

func (m *GetGuildMonthRankReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildMonthRankReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.ValueType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Start))
	dAtA[i] = 0x20
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GetGuildMonthRankResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildMonthRankResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RankList) > 0 {
		for _, msg := range m.RankList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintStarguildv3(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGuildEventCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildEventCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.EventType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.DtTime))
	return i, nil
}

func (m *GetGuildEventCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildEventCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv3(dAtA, i, uint64(m.Count))
	return i, nil
}

func encodeFixed64Starguildv3(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Starguildv3(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintStarguildv3(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *UserAddContributeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.Uid))
	n += 1 + sovStarguildv3(uint64(m.GuildId))
	n += 1 + sovStarguildv3(uint64(m.EventType))
	n += 1 + sovStarguildv3(uint64(m.ReddiamondAmount))
	n += 1 + sovStarguildv3(uint64(m.MoneyAmount))
	n += 1 + sovStarguildv3(uint64(m.ConsumeType))
	n += 2
	l = len(m.OrderId)
	n += 1 + l + sovStarguildv3(uint64(l))
	return n
}

func (m *UserAddContributeResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.ValueAdded))
	return n
}

func (m *StarGuildInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.GuildId))
	n += 1 + sovStarguildv3(uint64(m.StarLevel))
	n += 1 + sovStarguildv3(uint64(m.ContributionBase))
	n += 1 + sovStarguildv3(uint64(m.ContributionExt))
	n += 1 + sovStarguildv3(uint64(m.ContributionExtPay))
	n += 1 + sovStarguildv3(uint64(m.ContributionExtPercent))
	n += 1 + sovStarguildv3(uint64(m.ContributionNextPay))
	n += 1 + sovStarguildv3(uint64(m.ContributionNextPercent))
	n += 1 + sovStarguildv3(uint64(m.ContributionNextLevel))
	n += 1 + sovStarguildv3(uint64(m.ContributionCurLevel))
	n += 1 + sovStarguildv3(uint64(m.JoinNumYesterday))
	n += 1 + sovStarguildv3(uint64(m.Contribution_30DaysAgo))
	n += 1 + sovStarguildv3(uint64(m.ContributionToday))
	n += 1 + sovStarguildv3(uint64(m.LastCalcTime))
	return n
}

func (m *GetGuildStarInfosReq) Size() (n int) {
	var l int
	_ = l
	if len(m.GuildIdList) > 0 {
		for _, e := range m.GuildIdList {
			n += 1 + sovStarguildv3(uint64(e))
		}
	}
	n += 2
	return n
}

func (m *GetGuildStarInfosResp) Size() (n int) {
	var l int
	_ = l
	if len(m.StarInfoList) > 0 {
		for _, e := range m.StarInfoList {
			l = e.Size()
			n += 1 + l + sovStarguildv3(uint64(l))
		}
	}
	return n
}

func (m *GetMemberContributionExtReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.GuildId))
	n += 1 + sovStarguildv3(uint64(m.Start))
	n += 1 + sovStarguildv3(uint64(m.Limit))
	n += 1 + sovStarguildv3(uint64(m.TimeLimit))
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovStarguildv3(uint64(e))
		}
	}
	return n
}

func (m *MemberContributionExt) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.Uid))
	n += 1 + sovStarguildv3(uint64(m.Total))
	n += 1 + sovStarguildv3(uint64(m.GameConsume))
	n += 1 + sovStarguildv3(uint64(m.LiveConsume))
	n += 1 + sovStarguildv3(uint64(m.FunnyRoomGift))
	return n
}

func (m *GetMemberContributionExtResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.GuildId))
	if len(m.ContriExtList) > 0 {
		for _, e := range m.ContriExtList {
			l = e.Size()
			n += 1 + l + sovStarguildv3(uint64(l))
		}
	}
	n += 1 + sovStarguildv3(uint64(m.TotalCount))
	return n
}

func (m *GetGuildMutableContributionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.GuildId))
	return n
}

func (m *GetGuildMutableContributionResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.MutableContribution))
	return n
}

func (m *AddGuildMutableContributionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.GuildId))
	n += 1 + sovStarguildv3(uint64(m.ChangeValue))
	l = len(m.OrderId)
	n += 1 + l + sovStarguildv3(uint64(l))
	l = len(m.OrderDesc)
	n += 1 + l + sovStarguildv3(uint64(l))
	n += 1 + sovStarguildv3(uint64(m.OpUid))
	return n
}

func (m *AddGuildMutableContributionResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *MemberQuitGuildReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.Uid))
	n += 1 + sovStarguildv3(uint64(m.GuildId))
	return n
}

func (m *GetGuildInfoByLevelReq) Size() (n int) {
	var l int
	_ = l
	if len(m.GuildIdList) > 0 {
		for _, e := range m.GuildIdList {
			n += 1 + sovStarguildv3(uint64(e))
		}
	}
	n += 1 + sovStarguildv3(uint64(m.LevelBegin))
	n += 1 + sovStarguildv3(uint64(m.LevelEnd))
	n += 1 + sovStarguildv3(uint64(m.Limit))
	return n
}

func (m *GetGuildInfoByLevelObj) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.GuildId))
	n += 1 + sovStarguildv3(uint64(m.StarLevel))
	n += 1 + sovStarguildv3(uint64(m.Contribution))
	n += 1 + sovStarguildv3(uint64(m.ContributionPercent))
	return n
}

func (m *GetGuildInfoByLevelResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ObjList) > 0 {
		for _, e := range m.ObjList {
			l = e.Size()
			n += 1 + l + sovStarguildv3(uint64(l))
		}
	}
	return n
}

func (m *CalcSingleGuildReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.GuildId))
	return n
}

func (m *GetGuildLevelCountByDateReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.DateFrom))
	n += 1 + sovStarguildv3(uint64(m.DateTo))
	return n
}

func (m *LevelCount) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.StarLevel))
	n += 1 + sovStarguildv3(uint64(m.GuildCount))
	return n
}

func (m *GuildLevelCount) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.Date))
	if len(m.LevelCountList) > 0 {
		for _, e := range m.LevelCountList {
			l = e.Size()
			n += 1 + l + sovStarguildv3(uint64(l))
		}
	}
	return n
}

func (m *GetGuildLevelCountByDateResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GuildLevelList) > 0 {
		for _, e := range m.GuildLevelList {
			l = e.Size()
			n += 1 + l + sovStarguildv3(uint64(l))
		}
	}
	return n
}

func (m *GetGuildInfoByDateReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.Date))
	n += 1 + sovStarguildv3(uint64(m.StarLevel))
	n += 1 + sovStarguildv3(uint64(m.Start))
	n += 1 + sovStarguildv3(uint64(m.Limit))
	return n
}

func (m *GuildInfoByDate) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.GuildId))
	n += 1 + sovStarguildv3(uint64(m.MemberCount))
	return n
}

func (m *GetGuildInfoByDateResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GuildInfoList) > 0 {
		for _, e := range m.GuildInfoList {
			l = e.Size()
			n += 1 + l + sovStarguildv3(uint64(l))
		}
	}
	return n
}

func (m *GetRankListByContributionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.GuildId))
	n += 1 + sovStarguildv3(uint64(m.Start))
	n += 1 + sovStarguildv3(uint64(m.Limit))
	return n
}

func (m *RankListByContribution) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.GuildId))
	n += 1 + sovStarguildv3(uint64(m.Contribution))
	n += 1 + sovStarguildv3(uint64(m.Consume))
	return n
}

func (m *GetRankListByContributionResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RankList) > 0 {
		for _, e := range m.RankList {
			l = e.Size()
			n += 1 + l + sovStarguildv3(uint64(l))
		}
	}
	n += 1 + sovStarguildv3(uint64(m.MyRank))
	n += 1 + sovStarguildv3(uint64(m.MyContribution))
	return n
}

func (m *ReportDismissGuildReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.GuildId))
	return n
}

func (m *GetGuildLevelMemberHistoryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.Date))
	if len(m.GuildIdList) > 0 {
		for _, e := range m.GuildIdList {
			n += 1 + sovStarguildv3(uint64(e))
		}
	}
	return n
}

func (m *GuildLevelMemberHistory) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.GuildId))
	n += 1 + sovStarguildv3(uint64(m.GuildLevel))
	n += 1 + sovStarguildv3(uint64(m.GuildMemberCount))
	return n
}

func (m *GetGuildLevelMemberHistoryResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ResultList) > 0 {
		for _, e := range m.ResultList {
			l = e.Size()
			n += 1 + l + sovStarguildv3(uint64(l))
		}
	}
	return n
}

func (m *GuildMonthRank) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.Uid))
	n += 1 + sovStarguildv3(uint64(m.Value))
	return n
}

func (m *GetGuildMonthRankReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.GuildId))
	n += 1 + sovStarguildv3(uint64(m.ValueType))
	n += 1 + sovStarguildv3(uint64(m.Start))
	n += 1 + sovStarguildv3(uint64(m.Limit))
	return n
}

func (m *GetGuildMonthRankResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RankList) > 0 {
		for _, e := range m.RankList {
			l = e.Size()
			n += 1 + l + sovStarguildv3(uint64(l))
		}
	}
	return n
}

func (m *GetGuildEventCountReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.GuildId))
	n += 1 + sovStarguildv3(uint64(m.EventType))
	n += 1 + sovStarguildv3(uint64(m.DtTime))
	return n
}

func (m *GetGuildEventCountResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv3(uint64(m.Count))
	return n
}

func sovStarguildv3(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozStarguildv3(x uint64) (n int) {
	return sovStarguildv3(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *UserAddContributeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserAddContributeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserAddContributeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EventType", wireType)
			}
			m.EventType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EventType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReddiamondAmount", wireType)
			}
			m.ReddiamondAmount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReddiamondAmount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MoneyAmount", wireType)
			}
			m.MoneyAmount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MoneyAmount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConsumeType", wireType)
			}
			m.ConsumeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConsumeType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Valid", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Valid = bool(v != 0)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthStarguildv3
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("event_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserAddContributeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserAddContributeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserAddContributeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValueAdded", wireType)
			}
			m.ValueAdded = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ValueAdded |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("value_added")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StarGuildInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StarGuildInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StarGuildInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StarLevel", wireType)
			}
			m.StarLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StarLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContributionBase", wireType)
			}
			m.ContributionBase = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContributionBase |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContributionExt", wireType)
			}
			m.ContributionExt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContributionExt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContributionExtPay", wireType)
			}
			m.ContributionExtPay = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContributionExtPay |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContributionExtPercent", wireType)
			}
			m.ContributionExtPercent = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContributionExtPercent |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContributionNextPay", wireType)
			}
			m.ContributionNextPay = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContributionNextPay |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContributionNextPercent", wireType)
			}
			m.ContributionNextPercent = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContributionNextPercent |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContributionNextLevel", wireType)
			}
			m.ContributionNextLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContributionNextLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContributionCurLevel", wireType)
			}
			m.ContributionCurLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContributionCurLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JoinNumYesterday", wireType)
			}
			m.JoinNumYesterday = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JoinNumYesterday |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Contribution_30DaysAgo", wireType)
			}
			m.Contribution_30DaysAgo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Contribution_30DaysAgo |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContributionToday", wireType)
			}
			m.ContributionToday = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContributionToday |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastCalcTime", wireType)
			}
			m.LastCalcTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastCalcTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("star_level")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("contribution_base")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("contribution_ext")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("contribution_ext_pay")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("contribution_ext_percent")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("contribution_next_pay")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("contribution_next_percent")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("contribution_next_level")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("join_num_yesterday")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildStarInfosReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildStarInfosReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildStarInfosReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStarguildv3
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GuildIdList = append(m.GuildIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStarguildv3
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthStarguildv3
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowStarguildv3
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GuildIdList = append(m.GuildIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildIdList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QueryToday", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.QueryToday = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildStarInfosResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildStarInfosResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildStarInfosResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StarInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStarguildv3
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StarInfoList = append(m.StarInfoList, &StarGuildInfo{})
			if err := m.StarInfoList[len(m.StarInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMemberContributionExtReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMemberContributionExtReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMemberContributionExtReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			m.Start = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Start |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimeLimit", wireType)
			}
			m.TimeLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeLimit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 5:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStarguildv3
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStarguildv3
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthStarguildv3
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowStarguildv3
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("time_limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MemberContributionExt) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MemberContributionExt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MemberContributionExt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameConsume", wireType)
			}
			m.GameConsume = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameConsume |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LiveConsume", wireType)
			}
			m.LiveConsume = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LiveConsume |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FunnyRoomGift", wireType)
			}
			m.FunnyRoomGift = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FunnyRoomGift |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("total")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("game_consume")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("live_consume")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMemberContributionExtResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMemberContributionExtResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMemberContributionExtResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContriExtList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStarguildv3
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ContriExtList = append(m.ContriExtList, &MemberContributionExt{})
			if err := m.ContriExtList[len(m.ContriExtList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalCount", wireType)
			}
			m.TotalCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildMutableContributionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildMutableContributionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildMutableContributionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildMutableContributionResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildMutableContributionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildMutableContributionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MutableContribution", wireType)
			}
			m.MutableContribution = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MutableContribution |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("mutable_contribution")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddGuildMutableContributionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddGuildMutableContributionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddGuildMutableContributionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChangeValue", wireType)
			}
			m.ChangeValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChangeValue |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthStarguildv3
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthStarguildv3
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("change_value")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("order_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("order_desc")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("op_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddGuildMutableContributionResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddGuildMutableContributionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddGuildMutableContributionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MemberQuitGuildReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MemberQuitGuildReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MemberQuitGuildReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildInfoByLevelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildInfoByLevelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildInfoByLevelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStarguildv3
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GuildIdList = append(m.GuildIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStarguildv3
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthStarguildv3
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowStarguildv3
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GuildIdList = append(m.GuildIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildIdList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelBegin", wireType)
			}
			m.LevelBegin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LevelBegin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelEnd", wireType)
			}
			m.LevelEnd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LevelEnd |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildInfoByLevelObj) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildInfoByLevelObj: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildInfoByLevelObj: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StarLevel", wireType)
			}
			m.StarLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StarLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Contribution", wireType)
			}
			m.Contribution = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Contribution |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContributionPercent", wireType)
			}
			m.ContributionPercent = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContributionPercent |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("star_level")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("contribution")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("contribution_percent")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildInfoByLevelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildInfoByLevelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildInfoByLevelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ObjList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStarguildv3
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ObjList = append(m.ObjList, &GetGuildInfoByLevelObj{})
			if err := m.ObjList[len(m.ObjList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CalcSingleGuildReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CalcSingleGuildReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CalcSingleGuildReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildLevelCountByDateReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildLevelCountByDateReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildLevelCountByDateReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DateFrom", wireType)
			}
			m.DateFrom = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DateFrom |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DateTo", wireType)
			}
			m.DateTo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DateTo |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("date_from")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("date_to")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LevelCount) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LevelCount: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LevelCount: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StarLevel", wireType)
			}
			m.StarLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StarLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildCount", wireType)
			}
			m.GuildCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("star_level")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildLevelCount) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildLevelCount: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildLevelCount: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Date", wireType)
			}
			m.Date = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Date |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelCountList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStarguildv3
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LevelCountList = append(m.LevelCountList, &LevelCount{})
			if err := m.LevelCountList[len(m.LevelCountList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("date")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildLevelCountByDateResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildLevelCountByDateResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildLevelCountByDateResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildLevelList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStarguildv3
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GuildLevelList = append(m.GuildLevelList, &GuildLevelCount{})
			if err := m.GuildLevelList[len(m.GuildLevelList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildInfoByDateReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildInfoByDateReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildInfoByDateReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Date", wireType)
			}
			m.Date = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Date |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StarLevel", wireType)
			}
			m.StarLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StarLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			m.Start = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Start |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("date")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("star_level")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("start")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildInfoByDate) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildInfoByDate: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildInfoByDate: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberCount", wireType)
			}
			m.MemberCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemberCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("member_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildInfoByDateResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildInfoByDateResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildInfoByDateResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStarguildv3
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GuildInfoList = append(m.GuildInfoList, &GuildInfoByDate{})
			if err := m.GuildInfoList[len(m.GuildInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRankListByContributionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRankListByContributionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRankListByContributionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			m.Start = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Start |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("start")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RankListByContribution) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RankListByContribution: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RankListByContribution: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Contribution", wireType)
			}
			m.Contribution = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Contribution |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Consume", wireType)
			}
			m.Consume = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Consume |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("contribution")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRankListByContributionResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRankListByContributionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRankListByContributionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStarguildv3
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RankList = append(m.RankList, &RankListByContribution{})
			if err := m.RankList[len(m.RankList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MyRank", wireType)
			}
			m.MyRank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MyRank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MyContribution", wireType)
			}
			m.MyContribution = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MyContribution |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("my_rank")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("my_contribution")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportDismissGuildReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportDismissGuildReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportDismissGuildReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildLevelMemberHistoryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildLevelMemberHistoryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildLevelMemberHistoryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Date", wireType)
			}
			m.Date = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Date |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStarguildv3
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GuildIdList = append(m.GuildIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStarguildv3
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthStarguildv3
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowStarguildv3
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GuildIdList = append(m.GuildIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("date")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildLevelMemberHistory) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildLevelMemberHistory: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildLevelMemberHistory: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildLevel", wireType)
			}
			m.GuildLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildMemberCount", wireType)
			}
			m.GuildMemberCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildMemberCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_level")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_member_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildLevelMemberHistoryResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildLevelMemberHistoryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildLevelMemberHistoryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStarguildv3
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultList = append(m.ResultList, &GuildLevelMemberHistory{})
			if err := m.ResultList[len(m.ResultList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildMonthRank) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildMonthRank: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildMonthRank: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("value")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildMonthRankReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildMonthRankReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildMonthRankReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValueType", wireType)
			}
			m.ValueType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ValueType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			m.Start = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Start |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("value_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("start")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildMonthRankResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildMonthRankResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildMonthRankResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStarguildv3
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RankList = append(m.RankList, &GuildMonthRank{})
			if err := m.RankList[len(m.RankList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildEventCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildEventCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildEventCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EventType", wireType)
			}
			m.EventType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EventType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DtTime", wireType)
			}
			m.DtTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DtTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("event_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("dt_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildEventCountResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildEventCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildEventCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv3(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv3
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipStarguildv3(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowStarguildv3
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowStarguildv3
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthStarguildv3
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowStarguildv3
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipStarguildv3(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthStarguildv3 = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowStarguildv3   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("starguildsvr/v3/starguildv3.proto", fileDescriptorStarguildv3) }

var fileDescriptorStarguildv3 = []byte{
	// 2564 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0xcd, 0x8f, 0x1b, 0x49,
	0x15, 0x4f, 0xdb, 0xf3, 0xe1, 0x79, 0x9e, 0x0f, 0xa7, 0x32, 0x93, 0x71, 0x9c, 0xdd, 0x89, 0xd3,
	0xd9, 0x65, 0x87, 0x6c, 0x3c, 0xd9, 0x64, 0x36, 0x10, 0x2c, 0xc7, 0x5a, 0x8f, 0xc7, 0x99, 0x78,
	0x77, 0xec, 0xc9, 0x7a, 0x3c, 0x61, 0xc3, 0x0a, 0x5a, 0x3d, 0xee, 0x1a, 0xa7, 0x13, 0xf7, 0xc7,
	0x76, 0x75, 0x0f, 0x31, 0xe2, 0x80, 0xc4, 0x85, 0x0f, 0x09, 0xa1, 0x15, 0x88, 0x0b, 0x08, 0x09,
	0xe5, 0xc2, 0x01, 0x21, 0x71, 0x80, 0x13, 0x07, 0x6e, 0x2b, 0x21, 0x24, 0xf8, 0x07, 0x10, 0x0a,
	0x97, 0xfc, 0x03, 0x9c, 0xb8, 0xa0, 0xaa, 0x6e, 0xbb, 0xab, 0xec, 0xee, 0xb1, 0x17, 0xb8, 0x75,
	0xbf, 0x57, 0xaf, 0xea, 0xd5, 0xfb, 0xf8, 0xbd, 0xf7, 0x0a, 0xae, 0x12, 0x57, 0x75, 0xba, 0x9e,
	0xde, 0xd3, 0xc8, 0xa9, 0x73, 0xf3, 0x74, 0xfb, 0xe6, 0xf0, 0xff, 0x74, 0x7b, 0xcb, 0x76, 0x2c,
	0xd7, 0x42, 0x69, 0x8e, 0x94, 0x7b, 0xa3, 0x63, 0x19, 0x86, 0x65, 0xde, 0x74, 0x7b, 0xa7, 0xb6,
	0xde, 0x79, 0xd6, 0xc3, 0x37, 0xc9, 0xb3, 0x63, 0x4f, 0xef, 0xb9, 0xba, 0xe9, 0xf6, 0x6d, 0xec,
	0x8b, 0xc8, 0xbf, 0x4f, 0xc0, 0xea, 0x11, 0xc1, 0x4e, 0x45, 0xd3, 0xaa, 0x96, 0xe9, 0x3a, 0xfa,
	0xb1, 0xe7, 0xe2, 0x16, 0xfe, 0x04, 0x5d, 0x84, 0xa4, 0xa7, 0x6b, 0x59, 0x29, 0x9f, 0xd8, 0x5c,
	0xda, 0x99, 0xf9, 0xec, 0xef, 0x57, 0xce, 0xb5, 0x28, 0x01, 0x5d, 0x81, 0x14, 0x3b, 0x41, 0xd1,
	0xb5, 0x6c, 0x22, 0x2f, 0x0d, 0x99, 0xf3, 0x8c, 0x5a, 0xd7, 0xd0, 0x35, 0x00, 0x7c, 0x8a, 0x4d,
	0x57, 0xa1, 0xa7, 0x64, 0x93, 0x9c, 0xfc, 0x02, 0xa3, 0xb7, 0xfb, 0x36, 0x46, 0xb7, 0xe0, 0xbc,
	0x83, 0x35, 0x4d, 0x57, 0x0d, 0xcb, 0xd4, 0x14, 0xd5, 0xb0, 0x3c, 0xd3, 0xcd, 0xce, 0x70, 0xdb,
	0x65, 0x42, 0x76, 0x85, 0x71, 0xd1, 0x5b, 0xb0, 0x68, 0x58, 0x26, 0xee, 0x0f, 0x56, 0xcf, 0x72,
	0xab, 0xd3, 0x8c, 0x13, 0x2e, 0xec, 0x58, 0x26, 0xf1, 0x0c, 0xec, 0xab, 0x30, 0xc7, 0x2f, 0x0c,
	0x38, 0x4c, 0x89, 0x1c, 0xcc, 0x9e, 0xaa, 0x3d, 0x5d, 0xcb, 0xce, 0xe7, 0xa5, 0xcd, 0x54, 0xb0,
	0xc2, 0x27, 0xd1, 0x6b, 0x5a, 0x8e, 0x86, 0x1d, 0x7a, 0xcd, 0x54, 0x5e, 0xda, 0x5c, 0x18, 0x5c,
	0x93, 0x51, 0xeb, 0x9a, 0x5c, 0x86, 0xb5, 0x08, 0xbb, 0x11, 0x1b, 0xbd, 0x09, 0xe9, 0x53, 0xb5,
	0xe7, 0x61, 0x45, 0xd5, 0x34, 0x2c, 0x1a, 0x10, 0x18, 0xa3, 0x42, 0xe9, 0xf2, 0xbf, 0x67, 0x61,
	0xe9, 0xd0, 0x55, 0x9d, 0x3d, 0x66, 0x36, 0xf3, 0xc4, 0x12, 0x2c, 0xcb, 0x4b, 0xf1, 0x96, 0xa5,
	0x0e, 0x56, 0x7a, 0xf8, 0x14, 0xf7, 0xb2, 0x09, 0xde, 0xb2, 0x94, 0xbe, 0x4f, 0xc9, 0xd4, 0xb2,
	0x9d, 0x81, 0x42, 0xba, 0x65, 0x2a, 0xc7, 0x2a, 0x11, 0xbd, 0x90, 0xe1, 0xd9, 0x3b, 0x2a, 0xc1,
	0xe8, 0x26, 0x08, 0x34, 0x05, 0x3f, 0xa7, 0xbe, 0x08, 0x25, 0x56, 0x78, 0x6e, 0xed, 0xb9, 0x8b,
	0xbe, 0x04, 0xab, 0xa3, 0x02, 0x8a, 0xad, 0xf6, 0xb3, 0xb3, 0x9c, 0x10, 0x1a, 0x11, 0x7a, 0xa8,
	0xf6, 0x51, 0x19, 0xb2, 0xe3, 0x72, 0xd8, 0xe9, 0x60, 0xd3, 0xcd, 0xce, 0x71, 0xb2, 0x17, 0x47,
	0x65, 0xfd, 0x35, 0xe8, 0x2e, 0xac, 0x09, 0xf2, 0xe6, 0xe0, 0xe0, 0x79, 0x4e, 0xf8, 0x02, 0xbf,
	0xa4, 0x89, 0xfd, 0x93, 0xdf, 0x83, 0x4b, 0x11, 0x92, 0xc1, 0xd1, 0x29, 0x4e, 0x7a, 0x7d, 0x4c,
	0x3a, 0x38, 0xbb, 0x04, 0xeb, 0xe3, 0x3b, 0xf8, 0x9e, 0x58, 0xe0, 0xe4, 0xd7, 0x46, 0xe5, 0x7d,
	0xaf, 0x14, 0x41, 0xb8, 0x93, 0xd2, 0xf1, 0x06, 0x6e, 0x04, 0x2e, 0x3a, 0x05, 0xab, 0x56, 0xbd,
	0xc0, 0xa3, 0xb7, 0x01, 0x3d, 0xb5, 0x74, 0x53, 0x31, 0x3d, 0x43, 0xe9, 0x63, 0xe2, 0x62, 0x47,
	0x53, 0xfb, 0xd9, 0x34, 0xef, 0x52, 0xca, 0x6f, 0x7a, 0xc6, 0xe3, 0x01, 0x17, 0xdd, 0x1b, 0xb1,
	0xf4, 0xf6, 0x3b, 0x8a, 0xa6, 0xf6, 0x89, 0xa2, 0x76, 0xad, 0xec, 0x22, 0x77, 0xa2, 0xa0, 0xee,
	0xf6, 0x3b, 0xbb, 0x6a, 0x9f, 0x54, 0xba, 0x16, 0xda, 0x06, 0xc1, 0x7d, 0x8a, 0x6b, 0xd1, 0x23,
	0x97, 0x38, 0x41, 0x21, 0xc8, 0xda, 0x94, 0x8d, 0xae, 0xc3, 0x72, 0x4f, 0x25, 0xae, 0xd2, 0x51,
	0x7b, 0x1d, 0xc5, 0xd5, 0x0d, 0x9c, 0x5d, 0xe6, 0x04, 0x16, 0x29, 0xaf, 0xaa, 0xf6, 0x3a, 0x6d,
	0xdd, 0xc0, 0xb2, 0x0a, 0xab, 0x7b, 0xd8, 0x65, 0xb1, 0x4f, 0x93, 0x80, 0xc6, 0x3f, 0xa1, 0xa8,
	0x23, 0xc3, 0xd2, 0x20, 0x07, 0x94, 0x9e, 0x4e, 0xdc, 0xac, 0x94, 0x4f, 0x6e, 0x2e, 0xb5, 0xd2,
	0x41, 0x0a, 0xec, 0xeb, 0xc4, 0xa5, 0x09, 0xf6, 0x89, 0x87, 0x9d, 0x7e, 0xa0, 0x55, 0x82, 0x4b,
	0x5e, 0x60, 0x0c, 0xa6, 0x8e, 0xfc, 0x18, 0xd6, 0x22, 0x8e, 0x20, 0x36, 0x7a, 0x0f, 0x96, 0x59,
	0x1a, 0xe9, 0xe6, 0x89, 0x15, 0x1e, 0x92, 0xbe, 0x9d, 0xdb, 0xe2, 0x11, 0x55, 0xc8, 0xcd, 0xd6,
	0x22, 0x09, 0xb6, 0xa0, 0x1a, 0xc8, 0xff, 0x92, 0xe0, 0xf2, 0x1e, 0x76, 0x1b, 0xd8, 0x38, 0xc6,
	0x4e, 0x55, 0x8c, 0x55, 0x7a, 0x8b, 0x89, 0x99, 0x9c, 0x83, 0x59, 0xba, 0xa1, 0x2b, 0x20, 0xa8,
	0x4f, 0xa2, 0xbc, 0x9e, 0x6e, 0xe8, 0x6e, 0x36, 0xc9, 0xf3, 0x18, 0x89, 0x22, 0x00, 0x35, 0xac,
	0xe2, 0x2f, 0xe0, 0x73, 0x74, 0x81, 0xd2, 0xf7, 0xd9, 0xa2, 0x4b, 0x90, 0xf2, 0x06, 0xe6, 0x9b,
	0x65, 0xe6, 0x9b, 0xf7, 0x74, 0x66, 0x3a, 0xf9, 0x1e, 0x40, 0xbb, 0xde, 0xa8, 0x29, 0xfb, 0xf5,
	0x46, 0xbd, 0x8d, 0x10, 0x2c, 0x87, 0x7f, 0x4a, 0x65, 0x7f, 0x3f, 0x23, 0xa1, 0xcb, 0xb0, 0xce,
	0xd1, 0x5a, 0xb5, 0x6a, 0xad, 0xd9, 0x56, 0x1a, 0x07, 0xcd, 0xf6, 0x83, 0x4c, 0x42, 0xfe, 0xb3,
	0x04, 0x6b, 0x91, 0x97, 0x8e, 0xad, 0x16, 0x39, 0x98, 0x75, 0x2d, 0x57, 0x15, 0xd1, 0xca, 0x27,
	0x51, 0x9c, 0xee, 0xaa, 0x06, 0x56, 0x02, 0x48, 0x16, 0x40, 0x2a, 0x4d, 0x39, 0x55, 0x9f, 0x41,
	0x17, 0xf6, 0xf4, 0xd3, 0x70, 0x21, 0x7f, 0xef, 0x34, 0xe5, 0x0c, 0x16, 0xde, 0x80, 0x95, 0x13,
	0xcf, 0x34, 0xfb, 0x8a, 0x63, 0x59, 0x86, 0xd2, 0xd5, 0x4f, 0xc4, 0x2a, 0xb1, 0xc4, 0x98, 0x2d,
	0xcb, 0x32, 0xf6, 0xf4, 0x13, 0x57, 0xfe, 0x9d, 0x04, 0xaf, 0xc5, 0x7b, 0x91, 0xd8, 0x93, 0xdd,
	0xf8, 0x3e, 0x04, 0xd0, 0xc8, 0x90, 0x8c, 0x19, 0x3c, 0xc1, 0x42, 0x49, 0x16, 0x42, 0x29, 0xfa,
	0x84, 0x25, 0x5f, 0xb4, 0xf6, 0xdc, 0x1d, 0x44, 0x35, 0x33, 0x8b, 0xd2, 0x61, 0xd5, 0x8d, 0x77,
	0x3e, 0x30, 0x46, 0x95, 0xd2, 0xe5, 0x0a, 0x6c, 0x0c, 0xa2, 0xba, 0xe1, 0xb9, 0xea, 0x71, 0x0f,
	0xf3, 0xfb, 0x4e, 0x13, 0x7c, 0xf2, 0xd7, 0xe0, 0xca, 0x99, 0x5b, 0x10, 0x1b, 0x7d, 0x19, 0x56,
	0x0d, 0x9f, 0xa5, 0xf0, 0x79, 0x2e, 0xec, 0x77, 0xc1, 0x18, 0x17, 0x96, 0xff, 0x22, 0xc1, 0x46,
	0x45, 0xd3, 0xfe, 0x17, 0xfd, 0x58, 0xfd, 0x7e, 0xa2, 0x9a, 0x5d, 0xac, 0xb0, 0x72, 0xc9, 0x42,
	0x67, 0x76, 0x58, 0xbf, 0x19, 0xe7, 0x11, 0x65, 0x08, 0x35, 0x9a, 0x06, 0xcf, 0x68, 0x8d, 0xa6,
	0xe9, 0xe2, 0x2f, 0xd0, 0x30, 0xe9, 0xb0, 0xb0, 0x19, 0x2c, 0x59, 0x60, 0xf4, 0x5d, 0x4c, 0x3a,
	0xe8, 0x32, 0xcc, 0x59, 0xb6, 0x42, 0xa3, 0x97, 0x2f, 0x5f, 0xb3, 0x96, 0x7d, 0xa4, 0x6b, 0xf2,
	0x55, 0xb8, 0x72, 0xe6, 0x75, 0x88, 0x2d, 0x37, 0x00, 0xf9, 0x0e, 0xfe, 0xd0, 0xd3, 0x7d, 0xab,
	0x4e, 0xdf, 0x3e, 0x45, 0x78, 0xe7, 0x57, 0x12, 0x5c, 0x1c, 0xb8, 0x87, 0x02, 0xce, 0x4e, 0x9f,
	0x55, 0x81, 0xcf, 0x01, 0x8e, 0xac, 0xae, 0x28, 0xc7, 0xb8, 0xab, 0x9b, 0x02, 0xbe, 0x00, 0x63,
	0xec, 0x50, 0x3a, 0xba, 0x0a, 0x0b, 0xfe, 0x32, 0x6c, 0x6a, 0x42, 0xac, 0xa5, 0x18, 0xb9, 0x66,
	0x6a, 0x21, 0x0e, 0xf1, 0xe9, 0xe6, 0x93, 0xe4, 0x3f, 0x45, 0x2b, 0x79, 0x70, 0xfc, 0xf4, 0xff,
	0xd4, 0xc5, 0x6c, 0xb2, 0x1e, 0x2e, 0x0c, 0x3c, 0x1e, 0x1b, 0x04, 0x0e, 0x0d, 0x55, 0xa1, 0x54,
	0x0d, 0x8a, 0xfa, 0x4c, 0x5c, 0x4b, 0x10, 0x14, 0x74, 0xf9, 0x31, 0xac, 0x47, 0xda, 0x99, 0xd8,
	0xa8, 0x0c, 0x29, 0xeb, 0xf8, 0x29, 0x5f, 0x1b, 0xae, 0x09, 0x09, 0x1d, 0x7d, 0xf5, 0xd6, 0xbc,
	0x75, 0xfc, 0x94, 0xc1, 0xec, 0x1d, 0x40, 0xb4, 0xd2, 0x1d, 0xea, 0x66, 0xb7, 0x87, 0x87, 0x21,
	0x31, 0x31, 0x31, 0x15, 0x56, 0x55, 0xd8, 0x7a, 0xb6, 0x27, 0xcb, 0xf8, 0x9d, 0xfe, 0xae, 0xea,
	0x77, 0xe4, 0x57, 0x61, 0x41, 0x53, 0x5d, 0xac, 0x9c, 0x38, 0x96, 0x21, 0x6c, 0x90, 0xa2, 0xe4,
	0xfb, 0x8e, 0x65, 0xa0, 0xd7, 0x61, 0x9e, 0x2d, 0x71, 0x2d, 0xc1, 0xb0, 0x73, 0x94, 0xd8, 0xb6,
	0xe4, 0x8f, 0x00, 0xc2, 0x8d, 0x47, 0x1c, 0x21, 0x45, 0x3b, 0xe2, 0x4d, 0xf0, 0xc3, 0x2b, 0x80,
	0x25, 0x7e, 0x57, 0x60, 0x0c, 0x1f, 0x96, 0x4c, 0x58, 0x19, 0xd1, 0x1b, 0x65, 0x61, 0x86, 0x1e,
	0x2b, 0x6c, 0xcc, 0x28, 0xa8, 0x02, 0x19, 0x3f, 0xf8, 0xd8, 0x9e, 0x3c, 0x6e, 0xae, 0x0b, 0x66,
	0x0e, 0x37, 0x6b, 0x2d, 0xf7, 0x86, 0xdf, 0xcc, 0xc2, 0x27, 0x0c, 0xba, 0x63, 0x4c, 0x45, 0x6c,
	0x74, 0x1f, 0x32, 0xbe, 0xda, 0xfe, 0x41, 0x9c, 0x27, 0x5f, 0x13, 0x3d, 0x29, 0xee, 0xd0, 0x5a,
	0xee, 0x0e, 0x09, 0xec, 0x9c, 0x1f, 0x49, 0x61, 0x17, 0xe1, 0x7b, 0x7b, 0xe0, 0x8d, 0xf8, 0xeb,
	0x4d, 0x15, 0xe0, 0xc3, 0x0e, 0x80, 0x8f, 0xec, 0xd1, 0x0e, 0x20, 0x22, 0xf3, 0x3e, 0x0e, 0x0c,
	0x1d, 0x2a, 0x33, 0x15, 0xa0, 0x1a, 0x0c, 0xa1, 0x22, 0x9c, 0x98, 0x36, 0x82, 0xe2, 0x44, 0xbd,
	0xf8, 0x8d, 0xd1, 0xac, 0x1e, 0xda, 0x73, 0x17, 0x56, 0x82, 0x33, 0x46, 0x9a, 0xa6, 0x08, 0x73,
	0x72, 0xa2, 0x3e, 0x5e, 0x0d, 0xfb, 0xa6, 0x6f, 0x32, 0xaf, 0xb5, 0x54, 0xf3, 0x19, 0xfd, 0xdd,
	0xe9, 0x7f, 0xee, 0xd2, 0xc0, 0xf5, 0x4d, 0x89, 0x33, 0xfa, 0xa6, 0x31, 0xab, 0x7d, 0x57, 0x82,
	0x8b, 0xd1, 0xc7, 0x4e, 0x3e, 0x73, 0x14, 0x8a, 0x12, 0xb1, 0x50, 0xb4, 0x01, 0xf3, 0x61, 0x2f,
	0xc3, 0x4d, 0xc6, 0x01, 0x51, 0xfe, 0xad, 0x04, 0xaf, 0x9f, 0x71, 0x7f, 0xd6, 0x9a, 0x2e, 0x38,
	0xaa, 0xf9, 0x2c, 0x1e, 0x79, 0x62, 0x64, 0x53, 0x4e, 0x40, 0xa7, 0x08, 0x60, 0xf4, 0x15, 0xfa,
	0x2b, 0x22, 0x80, 0xd1, 0xa7, 0xa2, 0xa8, 0x00, 0x2b, 0x46, 0x5f, 0x89, 0x85, 0xd6, 0x65, 0x43,
	0xd8, 0x57, 0xbe, 0x0b, 0x6b, 0x2d, 0x6c, 0x5b, 0x8e, 0xbb, 0xab, 0x13, 0x43, 0x27, 0x64, 0x7a,
	0x2c, 0xfb, 0x3a, 0xbb, 0x6a, 0x98, 0x5e, 0x7e, 0x89, 0x7c, 0xa0, 0x13, 0xd7, 0x72, 0xfa, 0x67,
	0xe7, 0xcf, 0x58, 0x99, 0x4b, 0x8c, 0x95, 0x39, 0xf9, 0xa7, 0x12, 0xac, 0xc7, 0x6c, 0x3e, 0xd9,
	0xa3, 0x43, 0x4c, 0x1b, 0xcf, 0x50, 0x08, 0xf3, 0x9f, 0xce, 0x5d, 0xfe, 0x32, 0x21, 0x79, 0x84,
	0x51, 0x9a, 0xf1, 0x1b, 0x5c, 0x06, 0x75, 0xc3, 0xf6, 0x2c, 0xea, 0xda, 0xc4, 0x46, 0x35, 0x48,
	0x3b, 0x98, 0x78, 0x3d, 0x97, 0x77, 0xf2, 0x1b, 0x31, 0xa0, 0x24, 0x8a, 0x83, 0x2f, 0xc8, 0x0c,
	0xb0, 0x0b, 0xcb, 0x7e, 0x57, 0x62, 0x99, 0xee, 0x13, 0xe6, 0xda, 0x33, 0x5a, 0xf0, 0xb0, 0x8f,
	0x5a, 0xe2, 0x5e, 0x39, 0x3c, 0x2c, 0xff, 0x44, 0x0a, 0xe7, 0xb0, 0xe1, 0x4e, 0x53, 0x65, 0xe2,
	0x35, 0xf0, 0x1f, 0x33, 0xfc, 0x27, 0x16, 0x01, 0xe4, 0x18, 0x7d, 0xf0, 0xc0, 0xf2, 0x5f, 0x81,
	0xdc, 0x87, 0x21, 0xe8, 0x72, 0x5a, 0x11, 0x1b, 0xdd, 0x1d, 0xcf, 0x8f, 0xcb, 0xe3, 0xa6, 0x0b,
	0x65, 0x86, 0x79, 0x21, 0x7f, 0x3b, 0xdc, 0xb2, 0x76, 0x8a, 0x4d, 0xd7, 0x87, 0xfb, 0x29, 0x6f,
	0xca, 0xbd, 0x67, 0x25, 0xa2, 0xdf, 0xb3, 0x68, 0xe1, 0x75, 0xfd, 0xa1, 0x37, 0x29, 0x14, 0x5e,
	0x97, 0x8d, 0xbb, 0xef, 0x86, 0xc0, 0xca, 0x9f, 0x4e, 0x6c, 0x6a, 0x06, 0x3f, 0xae, 0xf8, 0xb3,
	0x7d, 0xd2, 0xf5, 0x43, 0x58, 0x39, 0x22, 0x6c, 0x70, 0x18, 0x3e, 0x59, 0x2d, 0x42, 0xea, 0xa8,
	0xda, 0x56, 0xf6, 0x2a, 0x8d, 0x5a, 0xe6, 0xdc, 0xe0, 0x6f, 0xbf, 0xfe, 0xa8, 0x96, 0x91, 0xd0,
	0x3a, 0x5c, 0xa0, 0x7f, 0xf7, 0x8f, 0x9a, 0xcd, 0xc7, 0x4a, 0xeb, 0xe0, 0xa0, 0xa1, 0xec, 0xd5,
	0xef, 0xb7, 0x33, 0x09, 0x94, 0x86, 0x79, 0xca, 0x68, 0x54, 0x3e, 0xca, 0x24, 0xaf, 0x6b, 0xfe,
	0xa6, 0x07, 0x36, 0x76, 0x68, 0x53, 0x40, 0x37, 0x45, 0xb0, 0x5c, 0x7d, 0x50, 0xab, 0x7e, 0xa0,
	0xd4, 0x9b, 0xca, 0x6e, 0xa5, 0xbe, 0xff, 0x38, 0x23, 0x31, 0x99, 0xe6, 0xd1, 0x61, 0x4d, 0xb9,
	0x95, 0x49, 0xd0, 0x73, 0x76, 0x0f, 0x9a, 0x95, 0x76, 0xfd, 0xa0, 0x99, 0x49, 0xa2, 0x35, 0x38,
	0x5f, 0xad, 0x3c, 0xac, 0xb7, 0x2b, 0xfb, 0x4a, 0xbd, 0xf9, 0x7e, 0xad, 0xca, 0xc8, 0x33, 0x54,
	0xa2, 0x7a, 0xd0, 0x3c, 0x3c, 0x6a, 0xd4, 0x32, 0xb3, 0xb7, 0xbf, 0xbf, 0x0a, 0xe9, 0xe1, 0x04,
	0xfd, 0x68, 0x1b, 0xbd, 0x92, 0xe0, 0xfc, 0xd8, 0x73, 0x19, 0xba, 0x2a, 0xf8, 0x2e, 0xea, 0x19,
	0x32, 0x27, 0x4f, 0x5a, 0x42, 0x6c, 0xf9, 0x87, 0xd2, 0x77, 0x5e, 0xbc, 0x4a, 0x4a, 0x3f, 0x78,
	0xf1, 0x2a, 0xb9, 0xe8, 0x15, 0xbb, 0x45, 0x5c, 0x74, 0x8a, 0x46, 0xd1, 0x2d, 0x7e, 0xfa, 0xe2,
	0x55, 0xd2, 0x2e, 0x78, 0xf9, 0x92, 0xa7, 0x6b, 0xe5, 0x7c, 0xa1, 0x9b, 0x2f, 0x0d, 0x3c, 0x5d,
	0xce, 0x17, 0x70, 0xbe, 0xc4, 0xdc, 0x97, 0xbf, 0xb5, 0xd5, 0x79, 0x82, 0x3b, 0xcf, 0x74, 0x33,
	0xbf, 0xbd, 0xa5, 0x59, 0xa6, 0x4a, 0x81, 0x2f, 0xff, 0xee, 0x96, 0x6e, 0x3e, 0xc5, 0x1d, 0xb7,
	0x9c, 0x2f, 0x38, 0xa5, 0xf0, 0x29, 0xb2, 0x9c, 0x2f, 0x18, 0x25, 0xfe, 0x15, 0xb2, 0x9c, 0x2f,
	0xb8, 0x25, 0xfe, 0xb5, 0xb1, 0x8c, 0xbe, 0x05, 0xe7, 0xc7, 0xde, 0x1d, 0x46, 0x6e, 0x1a, 0xf5,
	0xf4, 0x31, 0x72, 0xd3, 0xc8, 0xa7, 0x0b, 0x79, 0x83, 0x5e, 0x34, 0x41, 0x2f, 0x9a, 0xe8, 0xb2,
	0xeb, 0x2d, 0x09, 0xb7, 0x42, 0x7f, 0x94, 0x20, 0x1b, 0x37, 0xd2, 0xa2, 0xcd, 0xd1, 0x03, 0xe2,
	0xde, 0x2f, 0x72, 0x5f, 0x9c, 0x72, 0x25, 0xb1, 0xe5, 0x7d, 0xaa, 0x51, 0x92, 0x6a, 0x94, 0xea,
	0x16, 0x49, 0xb1, 0x17, 0x98, 0xfd, 0x0e, 0xaf, 0x17, 0x4b, 0xd7, 0x72, 0xbe, 0x40, 0x4a, 0x2c,
	0xf5, 0xcb, 0xf9, 0x42, 0xaf, 0xc4, 0x12, 0x9d, 0x59, 0x2f, 0x7c, 0xd0, 0x28, 0x23, 0x0d, 0xd6,
	0x68, 0xe3, 0x3c, 0x7c, 0xc7, 0x6a, 0xa8, 0xa6, 0xa7, 0xf6, 0x7a, 0x7d, 0xf4, 0xda, 0xd6, 0xf0,
	0x19, 0x7b, 0xeb, 0xf0, 0x83, 0x1d, 0xff, 0x19, 0xbb, 0x66, 0xd8, 0x6e, 0x5f, 0x79, 0xb8, 0x93,
	0x3b, 0x93, 0x2b, 0xaf, 0x50, 0x15, 0x67, 0xa8, 0x8a, 0xe7, 0xa8, 0x6a, 0xe7, 0xd0, 0xcf, 0xa4,
	0xb0, 0xd1, 0x8e, 0x98, 0xea, 0xd0, 0xdb, 0x91, 0x9e, 0x88, 0x1e, 0x67, 0x73, 0x37, 0xa6, 0x5f,
	0x4c, 0x6c, 0xf9, 0x75, 0xaa, 0xcb, 0x2c, 0xe7, 0xc0, 0xc5, 0x42, 0x97, 0xf3, 0xdf, 0xdf, 0x24,
	0xb8, 0x7c, 0xc6, 0xbc, 0x39, 0xa2, 0xd9, 0xd9, 0x83, 0xf6, 0x88, 0x66, 0x93, 0xc6, 0xd8, 0xaf,
	0x52, 0xcd, 0xe6, 0xa8, 0x66, 0xd0, 0x2d, 0xaa, 0x45, 0xab, 0xa8, 0x15, 0x3d, 0xa6, 0x61, 0x99,
	0xd7, 0x30, 0x5f, 0x50, 0x4b, 0xc3, 0xc8, 0xb7, 0x4a, 0x83, 0xf1, 0xbb, 0x9c, 0x2f, 0x68, 0xa5,
	0x70, 0xd4, 0x2e, 0xe7, 0x0b, 0x5e, 0xc9, 0x1f, 0xaa, 0xcb, 0xa8, 0x0f, 0x2b, 0x23, 0xf3, 0x31,
	0xba, 0x12, 0xf1, 0x3c, 0xc2, 0x4f, 0xcf, 0x13, 0x1c, 0xfa, 0x16, 0x55, 0x75, 0x9e, 0xaa, 0x3a,
	0x43, 0xd3, 0x9d, 0x2a, 0xb9, 0x5a, 0xf0, 0x06, 0x59, 0xce, 0x99, 0xf3, 0xe7, 0x12, 0x5c, 0x88,
	0x98, 0xd5, 0xd0, 0xc4, 0x69, 0x8e, 0xea, 0xf0, 0xc6, 0xe4, 0x45, 0xc4, 0x96, 0x8b, 0x54, 0x97,
	0x14, 0xd5, 0x65, 0xae, 0x5b, 0x3c, 0x2e, 0x62, 0xa6, 0xcd, 0x9b, 0x43, 0x6d, 0x8e, 0x4b, 0xdc,
	0x34, 0x4e, 0x51, 0xa7, 0x34, 0x1c, 0xbb, 0xcb, 0xc8, 0x82, 0x95, 0x91, 0x31, 0x71, 0xc4, 0x32,
	0xe3, 0x43, 0xe4, 0x04, 0xcb, 0xb0, 0xf0, 0x5a, 0x88, 0x0d, 0xaf, 0x5f, 0xf8, 0xf0, 0x10, 0x39,
	0x36, 0x8d, 0xc3, 0x43, 0xdc, 0x20, 0x3a, 0x0e, 0x0f, 0xb1, 0x73, 0x98, 0x7c, 0x83, 0x2a, 0x04,
	0xcc, 0x55, 0x24, 0x30, 0xce, 0xa5, 0x02, 0x29, 0x0d, 0xe7, 0x58, 0x66, 0x92, 0x60, 0x64, 0x65,
	0xfe, 0x42, 0xe3, 0x03, 0x08, 0x92, 0xcf, 0xf0, 0xc4, 0x40, 0xa7, 0x6b, 0x13, 0xd7, 0x10, 0x5b,
	0xbe, 0x47, 0xb5, 0x49, 0x33, 0xb0, 0xd2, 0x8a, 0xbd, 0x22, 0x29, 0x9a, 0x4c, 0xa3, 0x2f, 0x14,
	0x34, 0xa6, 0x83, 0x0f, 0x4c, 0xf4, 0x1a, 0x02, 0x58, 0x99, 0x01, 0x58, 0xa1, 0x5f, 0x4b, 0x70,
	0x29, 0xb6, 0x7f, 0x47, 0x63, 0x56, 0x89, 0x9d, 0x73, 0x72, 0xd7, 0xa7, 0x5d, 0x4a, 0x6c, 0xf9,
	0x0e, 0xd5, 0x79, 0x31, 0x08, 0x30, 0x0a, 0xb0, 0x54, 0xe3, 0xbc, 0x98, 0x93, 0xe3, 0xc0, 0x8a,
	0x3c, 0x40, 0xe3, 0x9d, 0xfb, 0x88, 0x25, 0x23, 0x5b, 0xfb, 0x69, 0x22, 0x6c, 0x29, 0x36, 0xc2,
	0x7e, 0x29, 0x41, 0x2e, 0xbe, 0x01, 0x46, 0xd7, 0xe3, 0x23, 0x67, 0x74, 0x40, 0xc8, 0xbd, 0x3d,
	0xf5, 0x5a, 0x62, 0xcb, 0x9b, 0x54, 0xad, 0x65, 0x16, 0x67, 0xdd, 0xa2, 0xc6, 0x14, 0x5b, 0x13,
	0x6d, 0x14, 0xb8, 0x18, 0xfd, 0x41, 0x0a, 0xeb, 0x73, 0xd8, 0x3c, 0x47, 0xd7, 0x67, 0xbe, 0x25,
	0x8e, 0xa9, 0xcf, 0x42, 0x7f, 0x2a, 0x7f, 0x4c, 0xd5, 0x58, 0x09, 0xaa, 0xa1, 0x3b, 0x74, 0xd7,
	0xae, 0xa8, 0x8a, 0x5b, 0x0a, 0x5b, 0x67, 0xbe, 0xe9, 0xb8, 0xb3, 0x15, 0x74, 0x12, 0xd1, 0x2e,
	0xfd, 0x0d, 0x97, 0x1d, 0x61, 0x17, 0x19, 0x93, 0x1d, 0x42, 0x93, 0x1b, 0x93, 0x1d, 0x62, 0x2b,
	0x2a, 0x37, 0xa9, 0xf2, 0x99, 0x20, 0xd2, 0x70, 0x60, 0xc5, 0xaf, 0x88, 0xaa, 0xe3, 0xa0, 0x69,
	0x8a, 0xd1, 0x5a, 0x63, 0xd5, 0x9c, 0xb8, 0xaa, 0x61, 0x97, 0x73, 0x73, 0xdf, 0x7b, 0xf1, 0x2a,
	0xf9, 0xe9, 0xe9, 0x4e, 0xe6, 0xb3, 0x97, 0x1b, 0xd2, 0x5f, 0x5f, 0x6e, 0x48, 0xff, 0x78, 0xb9,
	0x21, 0xfd, 0xf8, 0x9f, 0x1b, 0xe7, 0xfe, 0x13, 0x00, 0x00, 0xff, 0xff, 0xdb, 0x49, 0xc3, 0xac,
	0xcb, 0x1e, 0x00, 0x00,
}
