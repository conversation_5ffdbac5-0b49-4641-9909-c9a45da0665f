// Code generated by protoc-gen-gogo.
// source: starguildsvr/v2/starguildv2.proto
// DO NOT EDIT!

/*
	Package starguildv2 is a generated protocol buffer package.

	namespace

	It is generated from these files:
		starguildsvr/v2/starguildv2.proto

	It has these top-level messages:
		UserAlivenessEvent
		UserReportAlivenessEventReq
		StarScore
		StarLevelRequirement
		StarGuildInfo
		GetGuildStarInfosReq
		GetGuildStarInfosResp
		GetGuildStarInfoDetailReq
		GetGuildStarInfoDetailResp
		ManullySettleUpReq
*/
package starguildv2

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type UserAlivenessEvent_Type int32

const (
	UserAlivenessEvent_OPEN_APP  UserAlivenessEvent_Type = 1
	UserAlivenessEvent_HEARTBEAT UserAlivenessEvent_Type = 2
)

var UserAlivenessEvent_Type_name = map[int32]string{
	1: "OPEN_APP",
	2: "HEARTBEAT",
}
var UserAlivenessEvent_Type_value = map[string]int32{
	"OPEN_APP":  1,
	"HEARTBEAT": 2,
}

func (x UserAlivenessEvent_Type) Enum() *UserAlivenessEvent_Type {
	p := new(UserAlivenessEvent_Type)
	*p = x
	return p
}
func (x UserAlivenessEvent_Type) String() string {
	return proto.EnumName(UserAlivenessEvent_Type_name, int32(x))
}
func (x *UserAlivenessEvent_Type) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(UserAlivenessEvent_Type_value, data, "UserAlivenessEvent_Type")
	if err != nil {
		return err
	}
	*x = UserAlivenessEvent_Type(value)
	return nil
}
func (UserAlivenessEvent_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorStarguildv2, []int{0, 0}
}

// 用户活跃事件
type UserAlivenessEvent struct {
	Type      uint32 `protobuf:"varint,1,req,name=type" json:"type"`
	Uid       uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	GuildId   uint32 `protobuf:"varint,3,opt,name=guild_id,json=guildId" json:"guild_id"`
	Timestamp uint32 `protobuf:"varint,4,opt,name=timestamp" json:"timestamp"`
}

func (m *UserAlivenessEvent) Reset()                    { *m = UserAlivenessEvent{} }
func (m *UserAlivenessEvent) String() string            { return proto.CompactTextString(m) }
func (*UserAlivenessEvent) ProtoMessage()               {}
func (*UserAlivenessEvent) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv2, []int{0} }

func (m *UserAlivenessEvent) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *UserAlivenessEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserAlivenessEvent) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UserAlivenessEvent) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

// 用户上报活跃事件
type UserReportAlivenessEventReq struct {
	UserAlivenessInfo *UserAlivenessEvent `protobuf:"bytes,1,req,name=user_aliveness_info,json=userAlivenessInfo" json:"user_aliveness_info,omitempty"`
}

func (m *UserReportAlivenessEventReq) Reset()         { *m = UserReportAlivenessEventReq{} }
func (m *UserReportAlivenessEventReq) String() string { return proto.CompactTextString(m) }
func (*UserReportAlivenessEventReq) ProtoMessage()    {}
func (*UserReportAlivenessEventReq) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv2, []int{1}
}

func (m *UserReportAlivenessEventReq) GetUserAlivenessInfo() *UserAlivenessEvent {
	if m != nil {
		return m.UserAlivenessInfo
	}
	return nil
}

type StarScore struct {
	Current   uint32 `protobuf:"varint,1,req,name=current" json:"current"`
	Today     uint32 `protobuf:"varint,2,req,name=today" json:"today"`
	Yesterday uint32 `protobuf:"varint,3,req,name=yesterday" json:"yesterday"`
}

func (m *StarScore) Reset()                    { *m = StarScore{} }
func (m *StarScore) String() string            { return proto.CompactTextString(m) }
func (*StarScore) ProtoMessage()               {}
func (*StarScore) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv2, []int{2} }

func (m *StarScore) GetCurrent() uint32 {
	if m != nil {
		return m.Current
	}
	return 0
}

func (m *StarScore) GetToday() uint32 {
	if m != nil {
		return m.Today
	}
	return 0
}

func (m *StarScore) GetYesterday() uint32 {
	if m != nil {
		return m.Yesterday
	}
	return 0
}

type StarLevelRequirement struct {
	PopularityRequired uint32 `protobuf:"varint,1,req,name=popularity_required,json=popularityRequired" json:"popularity_required"`
	AlivenessRequired  uint32 `protobuf:"varint,2,req,name=aliveness_required,json=alivenessRequired" json:"aliveness_required"`
}

func (m *StarLevelRequirement) Reset()                    { *m = StarLevelRequirement{} }
func (m *StarLevelRequirement) String() string            { return proto.CompactTextString(m) }
func (*StarLevelRequirement) ProtoMessage()               {}
func (*StarLevelRequirement) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv2, []int{3} }

func (m *StarLevelRequirement) GetPopularityRequired() uint32 {
	if m != nil {
		return m.PopularityRequired
	}
	return 0
}

func (m *StarLevelRequirement) GetAlivenessRequired() uint32 {
	if m != nil {
		return m.AlivenessRequired
	}
	return 0
}

type StarGuildInfo struct {
	GuildId         uint32     `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	StarLevel       uint32     `protobuf:"varint,2,req,name=star_level,json=starLevel" json:"star_level"`
	PopularityScore *StarScore `protobuf:"bytes,3,req,name=popularity_score,json=popularityScore" json:"popularity_score,omitempty"`
	AlivenessScore  *StarScore `protobuf:"bytes,4,req,name=aliveness_score,json=alivenessScore" json:"aliveness_score,omitempty"`
}

func (m *StarGuildInfo) Reset()                    { *m = StarGuildInfo{} }
func (m *StarGuildInfo) String() string            { return proto.CompactTextString(m) }
func (*StarGuildInfo) ProtoMessage()               {}
func (*StarGuildInfo) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv2, []int{4} }

func (m *StarGuildInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *StarGuildInfo) GetStarLevel() uint32 {
	if m != nil {
		return m.StarLevel
	}
	return 0
}

func (m *StarGuildInfo) GetPopularityScore() *StarScore {
	if m != nil {
		return m.PopularityScore
	}
	return nil
}

func (m *StarGuildInfo) GetAlivenessScore() *StarScore {
	if m != nil {
		return m.AlivenessScore
	}
	return nil
}

type GetGuildStarInfosReq struct {
	GuildIdList []uint32 `protobuf:"varint,1,rep,name=guild_id_list,json=guildIdList" json:"guild_id_list,omitempty"`
}

func (m *GetGuildStarInfosReq) Reset()                    { *m = GetGuildStarInfosReq{} }
func (m *GetGuildStarInfosReq) String() string            { return proto.CompactTextString(m) }
func (*GetGuildStarInfosReq) ProtoMessage()               {}
func (*GetGuildStarInfosReq) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv2, []int{5} }

func (m *GetGuildStarInfosReq) GetGuildIdList() []uint32 {
	if m != nil {
		return m.GuildIdList
	}
	return nil
}

type GetGuildStarInfosResp struct {
	// 不带next_level_requirement
	StarInfoList []*StarGuildInfo `protobuf:"bytes,1,rep,name=star_info_list,json=starInfoList" json:"star_info_list,omitempty"`
}

func (m *GetGuildStarInfosResp) Reset()                    { *m = GetGuildStarInfosResp{} }
func (m *GetGuildStarInfosResp) String() string            { return proto.CompactTextString(m) }
func (*GetGuildStarInfosResp) ProtoMessage()               {}
func (*GetGuildStarInfosResp) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv2, []int{6} }

func (m *GetGuildStarInfosResp) GetStarInfoList() []*StarGuildInfo {
	if m != nil {
		return m.StarInfoList
	}
	return nil
}

type GetGuildStarInfoDetailReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *GetGuildStarInfoDetailReq) Reset()         { *m = GetGuildStarInfoDetailReq{} }
func (m *GetGuildStarInfoDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildStarInfoDetailReq) ProtoMessage()    {}
func (*GetGuildStarInfoDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv2, []int{7}
}

func (m *GetGuildStarInfoDetailReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildStarInfoDetailResp struct {
	StarInfo             *StarGuildInfo        `protobuf:"bytes,1,req,name=star_info,json=starInfo" json:"star_info,omitempty"`
	NextLevelRequirement *StarLevelRequirement `protobuf:"bytes,10,req,name=next_level_requirement,json=nextLevelRequirement" json:"next_level_requirement,omitempty"`
}

func (m *GetGuildStarInfoDetailResp) Reset()         { *m = GetGuildStarInfoDetailResp{} }
func (m *GetGuildStarInfoDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildStarInfoDetailResp) ProtoMessage()    {}
func (*GetGuildStarInfoDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptorStarguildv2, []int{8}
}

func (m *GetGuildStarInfoDetailResp) GetStarInfo() *StarGuildInfo {
	if m != nil {
		return m.StarInfo
	}
	return nil
}

func (m *GetGuildStarInfoDetailResp) GetNextLevelRequirement() *StarLevelRequirement {
	if m != nil {
		return m.NextLevelRequirement
	}
	return nil
}

type ManullySettleUpReq struct {
	Year  uint32 `protobuf:"varint,1,req,name=year" json:"year"`
	Month uint32 `protobuf:"varint,2,req,name=month" json:"month"`
	Day   uint32 `protobuf:"varint,3,req,name=day" json:"day"`
}

func (m *ManullySettleUpReq) Reset()                    { *m = ManullySettleUpReq{} }
func (m *ManullySettleUpReq) String() string            { return proto.CompactTextString(m) }
func (*ManullySettleUpReq) ProtoMessage()               {}
func (*ManullySettleUpReq) Descriptor() ([]byte, []int) { return fileDescriptorStarguildv2, []int{9} }

func (m *ManullySettleUpReq) GetYear() uint32 {
	if m != nil {
		return m.Year
	}
	return 0
}

func (m *ManullySettleUpReq) GetMonth() uint32 {
	if m != nil {
		return m.Month
	}
	return 0
}

func (m *ManullySettleUpReq) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

func init() {
	proto.RegisterType((*UserAlivenessEvent)(nil), "starguildv2.UserAlivenessEvent")
	proto.RegisterType((*UserReportAlivenessEventReq)(nil), "starguildv2.UserReportAlivenessEventReq")
	proto.RegisterType((*StarScore)(nil), "starguildv2.StarScore")
	proto.RegisterType((*StarLevelRequirement)(nil), "starguildv2.StarLevelRequirement")
	proto.RegisterType((*StarGuildInfo)(nil), "starguildv2.StarGuildInfo")
	proto.RegisterType((*GetGuildStarInfosReq)(nil), "starguildv2.GetGuildStarInfosReq")
	proto.RegisterType((*GetGuildStarInfosResp)(nil), "starguildv2.GetGuildStarInfosResp")
	proto.RegisterType((*GetGuildStarInfoDetailReq)(nil), "starguildv2.GetGuildStarInfoDetailReq")
	proto.RegisterType((*GetGuildStarInfoDetailResp)(nil), "starguildv2.GetGuildStarInfoDetailResp")
	proto.RegisterType((*ManullySettleUpReq)(nil), "starguildv2.ManullySettleUpReq")
	proto.RegisterEnum("starguildv2.UserAlivenessEvent_Type", UserAlivenessEvent_Type_name, UserAlivenessEvent_Type_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for StarGuildV2 service

type StarGuildV2Client interface {
	UserReportAlivenessEvent(ctx context.Context, in *UserReportAlivenessEventReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildStarInfos(ctx context.Context, in *GetGuildStarInfosReq, opts ...grpc.CallOption) (*GetGuildStarInfosResp, error)
	GetGuildStarInfoDetail(ctx context.Context, in *GetGuildStarInfoDetailReq, opts ...grpc.CallOption) (*GetGuildStarInfoDetailResp, error)
	ManullySettleUp(ctx context.Context, in *ManullySettleUpReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DumpAllStarGuildInfos(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
}

type starGuildV2Client struct {
	cc *grpc.ClientConn
}

func NewStarGuildV2Client(cc *grpc.ClientConn) StarGuildV2Client {
	return &starGuildV2Client{cc}
}

func (c *starGuildV2Client) UserReportAlivenessEvent(ctx context.Context, in *UserReportAlivenessEventReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/starguildv2.StarGuildV2/UserReportAlivenessEvent", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starGuildV2Client) GetGuildStarInfos(ctx context.Context, in *GetGuildStarInfosReq, opts ...grpc.CallOption) (*GetGuildStarInfosResp, error) {
	out := new(GetGuildStarInfosResp)
	err := grpc.Invoke(ctx, "/starguildv2.StarGuildV2/GetGuildStarInfos", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starGuildV2Client) GetGuildStarInfoDetail(ctx context.Context, in *GetGuildStarInfoDetailReq, opts ...grpc.CallOption) (*GetGuildStarInfoDetailResp, error) {
	out := new(GetGuildStarInfoDetailResp)
	err := grpc.Invoke(ctx, "/starguildv2.StarGuildV2/GetGuildStarInfoDetail", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starGuildV2Client) ManullySettleUp(ctx context.Context, in *ManullySettleUpReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/starguildv2.StarGuildV2/ManullySettleUp", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starGuildV2Client) DumpAllStarGuildInfos(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/starguildv2.StarGuildV2/DumpAllStarGuildInfos", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for StarGuildV2 service

type StarGuildV2Server interface {
	UserReportAlivenessEvent(context.Context, *UserReportAlivenessEventReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildStarInfos(context.Context, *GetGuildStarInfosReq) (*GetGuildStarInfosResp, error)
	GetGuildStarInfoDetail(context.Context, *GetGuildStarInfoDetailReq) (*GetGuildStarInfoDetailResp, error)
	ManullySettleUp(context.Context, *ManullySettleUpReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DumpAllStarGuildInfos(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*tlvpickle.SKBuiltinEmpty_PB, error)
}

func RegisterStarGuildV2Server(s *grpc.Server, srv StarGuildV2Server) {
	s.RegisterService(&_StarGuildV2_serviceDesc, srv)
}

func _StarGuildV2_UserReportAlivenessEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserReportAlivenessEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV2Server).UserReportAlivenessEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv2.StarGuildV2/UserReportAlivenessEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV2Server).UserReportAlivenessEvent(ctx, req.(*UserReportAlivenessEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarGuildV2_GetGuildStarInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildStarInfosReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV2Server).GetGuildStarInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv2.StarGuildV2/GetGuildStarInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV2Server).GetGuildStarInfos(ctx, req.(*GetGuildStarInfosReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarGuildV2_GetGuildStarInfoDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildStarInfoDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV2Server).GetGuildStarInfoDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv2.StarGuildV2/GetGuildStarInfoDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV2Server).GetGuildStarInfoDetail(ctx, req.(*GetGuildStarInfoDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarGuildV2_ManullySettleUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ManullySettleUpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV2Server).ManullySettleUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv2.StarGuildV2/ManullySettleUp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV2Server).ManullySettleUp(ctx, req.(*ManullySettleUpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarGuildV2_DumpAllStarGuildInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarGuildV2Server).DumpAllStarGuildInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/starguildv2.StarGuildV2/DumpAllStarGuildInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarGuildV2Server).DumpAllStarGuildInfos(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

var _StarGuildV2_serviceDesc = grpc.ServiceDesc{
	ServiceName: "starguildv2.StarGuildV2",
	HandlerType: (*StarGuildV2Server)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UserReportAlivenessEvent",
			Handler:    _StarGuildV2_UserReportAlivenessEvent_Handler,
		},
		{
			MethodName: "GetGuildStarInfos",
			Handler:    _StarGuildV2_GetGuildStarInfos_Handler,
		},
		{
			MethodName: "GetGuildStarInfoDetail",
			Handler:    _StarGuildV2_GetGuildStarInfoDetail_Handler,
		},
		{
			MethodName: "ManullySettleUp",
			Handler:    _StarGuildV2_ManullySettleUp_Handler,
		},
		{
			MethodName: "DumpAllStarGuildInfos",
			Handler:    _StarGuildV2_DumpAllStarGuildInfos_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "starguildsvr/v2/starguildv2.proto",
}

func (m *UserAlivenessEvent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserAlivenessEvent) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv2(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv2(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintStarguildv2(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintStarguildv2(dAtA, i, uint64(m.Timestamp))
	return i, nil
}

func (m *UserReportAlivenessEventReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserReportAlivenessEventReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UserAlivenessInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("user_aliveness_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintStarguildv2(dAtA, i, uint64(m.UserAlivenessInfo.Size()))
		n1, err := m.UserAlivenessInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *StarScore) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StarScore) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv2(dAtA, i, uint64(m.Current))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv2(dAtA, i, uint64(m.Today))
	dAtA[i] = 0x18
	i++
	i = encodeVarintStarguildv2(dAtA, i, uint64(m.Yesterday))
	return i, nil
}

func (m *StarLevelRequirement) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StarLevelRequirement) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv2(dAtA, i, uint64(m.PopularityRequired))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv2(dAtA, i, uint64(m.AlivenessRequired))
	return i, nil
}

func (m *StarGuildInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StarGuildInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv2(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv2(dAtA, i, uint64(m.StarLevel))
	if m.PopularityScore == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("popularity_score")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintStarguildv2(dAtA, i, uint64(m.PopularityScore.Size()))
		n2, err := m.PopularityScore.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if m.AlivenessScore == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("aliveness_score")
	} else {
		dAtA[i] = 0x22
		i++
		i = encodeVarintStarguildv2(dAtA, i, uint64(m.AlivenessScore.Size()))
		n3, err := m.AlivenessScore.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *GetGuildStarInfosReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildStarInfosReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GuildIdList) > 0 {
		for _, num := range m.GuildIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintStarguildv2(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetGuildStarInfosResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildStarInfosResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.StarInfoList) > 0 {
		for _, msg := range m.StarInfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintStarguildv2(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGuildStarInfoDetailReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildStarInfoDetailReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv2(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GetGuildStarInfoDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildStarInfoDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.StarInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("star_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintStarguildv2(dAtA, i, uint64(m.StarInfo.Size()))
		n4, err := m.StarInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	if m.NextLevelRequirement == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("next_level_requirement")
	} else {
		dAtA[i] = 0x52
		i++
		i = encodeVarintStarguildv2(dAtA, i, uint64(m.NextLevelRequirement.Size()))
		n5, err := m.NextLevelRequirement.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *ManullySettleUpReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ManullySettleUpReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintStarguildv2(dAtA, i, uint64(m.Year))
	dAtA[i] = 0x10
	i++
	i = encodeVarintStarguildv2(dAtA, i, uint64(m.Month))
	dAtA[i] = 0x18
	i++
	i = encodeVarintStarguildv2(dAtA, i, uint64(m.Day))
	return i, nil
}

func encodeFixed64Starguildv2(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Starguildv2(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintStarguildv2(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *UserAlivenessEvent) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv2(uint64(m.Type))
	n += 1 + sovStarguildv2(uint64(m.Uid))
	n += 1 + sovStarguildv2(uint64(m.GuildId))
	n += 1 + sovStarguildv2(uint64(m.Timestamp))
	return n
}

func (m *UserReportAlivenessEventReq) Size() (n int) {
	var l int
	_ = l
	if m.UserAlivenessInfo != nil {
		l = m.UserAlivenessInfo.Size()
		n += 1 + l + sovStarguildv2(uint64(l))
	}
	return n
}

func (m *StarScore) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv2(uint64(m.Current))
	n += 1 + sovStarguildv2(uint64(m.Today))
	n += 1 + sovStarguildv2(uint64(m.Yesterday))
	return n
}

func (m *StarLevelRequirement) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv2(uint64(m.PopularityRequired))
	n += 1 + sovStarguildv2(uint64(m.AlivenessRequired))
	return n
}

func (m *StarGuildInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv2(uint64(m.GuildId))
	n += 1 + sovStarguildv2(uint64(m.StarLevel))
	if m.PopularityScore != nil {
		l = m.PopularityScore.Size()
		n += 1 + l + sovStarguildv2(uint64(l))
	}
	if m.AlivenessScore != nil {
		l = m.AlivenessScore.Size()
		n += 1 + l + sovStarguildv2(uint64(l))
	}
	return n
}

func (m *GetGuildStarInfosReq) Size() (n int) {
	var l int
	_ = l
	if len(m.GuildIdList) > 0 {
		for _, e := range m.GuildIdList {
			n += 1 + sovStarguildv2(uint64(e))
		}
	}
	return n
}

func (m *GetGuildStarInfosResp) Size() (n int) {
	var l int
	_ = l
	if len(m.StarInfoList) > 0 {
		for _, e := range m.StarInfoList {
			l = e.Size()
			n += 1 + l + sovStarguildv2(uint64(l))
		}
	}
	return n
}

func (m *GetGuildStarInfoDetailReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv2(uint64(m.GuildId))
	return n
}

func (m *GetGuildStarInfoDetailResp) Size() (n int) {
	var l int
	_ = l
	if m.StarInfo != nil {
		l = m.StarInfo.Size()
		n += 1 + l + sovStarguildv2(uint64(l))
	}
	if m.NextLevelRequirement != nil {
		l = m.NextLevelRequirement.Size()
		n += 1 + l + sovStarguildv2(uint64(l))
	}
	return n
}

func (m *ManullySettleUpReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovStarguildv2(uint64(m.Year))
	n += 1 + sovStarguildv2(uint64(m.Month))
	n += 1 + sovStarguildv2(uint64(m.Day))
	return n
}

func sovStarguildv2(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozStarguildv2(x uint64) (n int) {
	return sovStarguildv2(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *UserAlivenessEvent) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserAlivenessEvent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserAlivenessEvent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserReportAlivenessEventReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserReportAlivenessEventReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserReportAlivenessEventReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserAlivenessInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStarguildv2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserAlivenessInfo == nil {
				m.UserAlivenessInfo = &UserAlivenessEvent{}
			}
			if err := m.UserAlivenessInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_aliveness_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StarScore) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StarScore: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StarScore: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Current", wireType)
			}
			m.Current = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Current |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Today", wireType)
			}
			m.Today = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Today |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Yesterday", wireType)
			}
			m.Yesterday = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Yesterday |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("current")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("today")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("yesterday")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StarLevelRequirement) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StarLevelRequirement: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StarLevelRequirement: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PopularityRequired", wireType)
			}
			m.PopularityRequired = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PopularityRequired |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AlivenessRequired", wireType)
			}
			m.AlivenessRequired = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AlivenessRequired |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("popularity_required")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("aliveness_required")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StarGuildInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StarGuildInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StarGuildInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StarLevel", wireType)
			}
			m.StarLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StarLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PopularityScore", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStarguildv2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.PopularityScore == nil {
				m.PopularityScore = &StarScore{}
			}
			if err := m.PopularityScore.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AlivenessScore", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStarguildv2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.AlivenessScore == nil {
				m.AlivenessScore = &StarScore{}
			}
			if err := m.AlivenessScore.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("star_level")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("popularity_score")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("aliveness_score")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildStarInfosReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildStarInfosReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildStarInfosReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStarguildv2
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GuildIdList = append(m.GuildIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStarguildv2
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthStarguildv2
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowStarguildv2
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GuildIdList = append(m.GuildIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildStarInfosResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildStarInfosResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildStarInfosResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StarInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStarguildv2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StarInfoList = append(m.StarInfoList, &StarGuildInfo{})
			if err := m.StarInfoList[len(m.StarInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildStarInfoDetailReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildStarInfoDetailReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildStarInfoDetailReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildStarInfoDetailResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildStarInfoDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildStarInfoDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StarInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStarguildv2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.StarInfo == nil {
				m.StarInfo = &StarGuildInfo{}
			}
			if err := m.StarInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NextLevelRequirement", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStarguildv2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.NextLevelRequirement == nil {
				m.NextLevelRequirement = &StarLevelRequirement{}
			}
			if err := m.NextLevelRequirement.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("star_info")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("next_level_requirement")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ManullySettleUpReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStarguildv2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ManullySettleUpReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ManullySettleUpReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Year", wireType)
			}
			m.Year = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Year |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Month", wireType)
			}
			m.Month = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Month |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Day", wireType)
			}
			m.Day = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Day |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipStarguildv2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStarguildv2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("year")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("month")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("day")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipStarguildv2(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowStarguildv2
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowStarguildv2
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthStarguildv2
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowStarguildv2
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipStarguildv2(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthStarguildv2 = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowStarguildv2   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("starguildsvr/v2/starguildv2.proto", fileDescriptorStarguildv2) }

var fileDescriptorStarguildv2 = []byte{
	// 882 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x54, 0x4d, 0x6f, 0xe3, 0x54,
	0x14, 0xad, 0xe3, 0xcc, 0x4c, 0x73, 0x33, 0x99, 0xa6, 0x6f, 0x3a, 0x55, 0x08, 0x90, 0x7a, 0x5e,
	0xf9, 0xe8, 0x26, 0xad, 0x94, 0x11, 0x42, 0x8a, 0xa2, 0x40, 0xa2, 0x89, 0x86, 0x11, 0x03, 0x53,
	0xb9, 0x1d, 0x10, 0x2b, 0xcb, 0x53, 0xbf, 0x16, 0xab, 0xfe, 0xaa, 0xdf, 0xb3, 0x85, 0x57, 0x20,
	0x24, 0x24, 0x60, 0x05, 0xfc, 0x00, 0x56, 0x5d, 0x23, 0xc4, 0x9f, 0xa0, 0x4b, 0xb6, 0x6c, 0x10,
	0x2a, 0x9b, 0xfe, 0x0c, 0x74, 0x9f, 0x6b, 0xc7, 0x4e, 0xda, 0x66, 0x96, 0xbe, 0xef, 0x7e, 0x9c,
	0x73, 0xee, 0xf1, 0x85, 0x87, 0x5c, 0x98, 0xe1, 0x51, 0x64, 0x3b, 0x16, 0x8f, 0xc3, 0x9d, 0xb8,
	0xb7, 0x93, 0x7f, 0xc7, 0xbd, 0xed, 0x20, 0xf4, 0x85, 0x4f, 0xea, 0x85, 0x50, 0xfb, 0xad, 0x03,
	0xdf, 0x75, 0x7d, 0x6f, 0x47, 0x38, 0x71, 0x60, 0x1f, 0x1c, 0x3b, 0x6c, 0x87, 0x1f, 0xbf, 0x8c,
	0x6c, 0x47, 0xd8, 0x9e, 0x48, 0x02, 0x96, 0x96, 0xd0, 0xdf, 0x15, 0x20, 0x2f, 0x38, 0x0b, 0x47,
	0x8e, 0x1d, 0x33, 0x8f, 0x71, 0x3e, 0x89, 0x99, 0x27, 0x48, 0x0b, 0xaa, 0x98, 0xd4, 0x52, 0xb4,
	0xca, 0x56, 0x63, 0x5c, 0x3d, 0xfb, 0x67, 0x63, 0x49, 0x97, 0x11, 0xb2, 0x0e, 0x6a, 0x64, 0x5b,
	0xad, 0x4a, 0xe1, 0x01, 0x03, 0x64, 0x03, 0x96, 0xe5, 0x64, 0xc3, 0xb6, 0x5a, 0xaa, 0xa6, 0xe4,
	0x8f, 0x77, 0x64, 0xf4, 0xa9, 0x45, 0x28, 0xd4, 0x84, 0xed, 0x32, 0x2e, 0x4c, 0x37, 0x68, 0x55,
	0x0b, 0x19, 0xd3, 0x30, 0xdd, 0x84, 0xea, 0x3e, 0x0e, 0xb9, 0x0b, 0xcb, 0xcf, 0x77, 0x27, 0x9f,
	0x1a, 0xa3, 0xdd, 0xdd, 0xa6, 0x42, 0x1a, 0x50, 0xfb, 0x68, 0x32, 0xd2, 0xf7, 0xc7, 0x93, 0xd1,
	0x7e, 0xb3, 0x42, 0x3d, 0x78, 0x1d, 0x11, 0xeb, 0x2c, 0xf0, 0x43, 0x51, 0xc6, 0xad, 0xb3, 0x13,
	0xf2, 0x1c, 0xee, 0x47, 0x9c, 0x85, 0x86, 0x99, 0xbd, 0x18, 0xb6, 0x77, 0xe8, 0x4b, 0x26, 0xf5,
	0xde, 0xc6, 0x76, 0x51, 0xb5, 0x79, 0xe2, 0xfa, 0x6a, 0x54, 0x8c, 0x3d, 0xf5, 0x0e, 0x7d, 0x7a,
	0x0c, 0xb5, 0x3d, 0x61, 0x86, 0x7b, 0x07, 0x7e, 0xc8, 0x48, 0x07, 0xee, 0x1c, 0x44, 0x61, 0xc8,
	0x3c, 0x51, 0xd2, 0x26, 0x0b, 0x92, 0x36, 0xdc, 0x12, 0xbe, 0x65, 0x26, 0x25, 0x81, 0xd2, 0x10,
	0x2a, 0x90, 0x30, 0x2e, 0x58, 0x88, 0xef, 0x6a, 0xe1, 0x7d, 0x1a, 0xa6, 0xdf, 0x2a, 0xb0, 0x86,
	0xd3, 0x9e, 0xb1, 0x98, 0x39, 0x3a, 0x3b, 0x89, 0xec, 0x90, 0xb9, 0xd8, 0xf8, 0x3d, 0xb8, 0x1f,
	0xf8, 0x41, 0xe4, 0x98, 0xa1, 0x2d, 0x12, 0x23, 0x4c, 0x5f, 0xac, 0x12, 0x08, 0x32, 0x4d, 0xb8,
	0xac, 0xb4, 0xc8, 0x23, 0x20, 0x53, 0x21, 0xf2, 0xaa, 0x22, 0xb8, 0xd5, 0xfc, 0x3d, 0x2b, 0xa2,
	0x7f, 0x2b, 0xd0, 0x40, 0x10, 0x4f, 0xe4, 0xea, 0xbc, 0x43, 0xbf, 0xb4, 0xdd, 0x12, 0xef, 0x6c,
	0xbb, 0x9b, 0x00, 0xa8, 0xac, 0xe1, 0x20, 0xee, 0x52, 0xff, 0x1a, 0xcf, 0xe8, 0x90, 0x11, 0x34,
	0x0b, 0x1c, 0x38, 0x0a, 0x2a, 0x75, 0xa8, 0xf7, 0xd6, 0x4b, 0x7b, 0xc9, 0xe5, 0xd6, 0x57, 0xa6,
	0xf9, 0xa9, 0xfe, 0x1f, 0xc0, 0xca, 0x94, 0x4f, 0xda, 0xa1, 0x7a, 0x63, 0x87, 0x7b, 0x79, 0xba,
	0xfc, 0xa6, 0x7d, 0x58, 0x7b, 0xc2, 0x84, 0x64, 0x86, 0x49, 0xc8, 0x0e, 0x89, 0x13, 0x0a, 0x8d,
	0x8c, 0xa1, 0xe1, 0xd8, 0x1c, 0xd7, 0xab, 0x6e, 0x35, 0xf4, 0xfa, 0x25, 0xc1, 0x67, 0x36, 0x17,
	0xf4, 0x0b, 0x78, 0x70, 0x45, 0x2d, 0x0f, 0xc8, 0x87, 0x70, 0x4f, 0xb2, 0x47, 0xa7, 0x4d, 0xab,
	0xeb, 0xbd, 0xf6, 0x1c, 0xa8, 0x5c, 0x52, 0xfd, 0x2e, 0xbf, 0x6c, 0x21, 0x5b, 0x0f, 0xe0, 0xb5,
	0xd9, 0xd6, 0x8f, 0x99, 0x30, 0x6d, 0xf4, 0xc0, 0x42, 0xf5, 0xe9, 0x6f, 0x0a, 0xb4, 0xaf, 0x2b,
	0xe7, 0x01, 0x79, 0x1f, 0x6a, 0x39, 0xbc, 0xcb, 0x1f, 0xe1, 0x26, 0x64, 0xcb, 0x19, 0x32, 0xf2,
	0x39, 0xac, 0x7b, 0xec, 0x2b, 0x91, 0x6e, 0x35, 0xb3, 0x0f, 0xda, 0xb1, 0x05, 0xb2, 0xcb, 0xc3,
	0xb9, 0x2e, 0xb3, 0xbe, 0xd5, 0xd7, 0xb0, 0xc1, 0x6c, 0x94, 0xbe, 0x04, 0xf2, 0x89, 0xe9, 0x45,
	0x8e, 0x93, 0xec, 0x31, 0x21, 0x1c, 0xf6, 0x22, 0x40, 0x9e, 0x2d, 0xa8, 0x26, 0xcc, 0x0c, 0xcb,
	0x57, 0x07, 0x23, 0xf8, 0x5b, 0xb9, 0xbe, 0x27, 0xbe, 0x2c, 0xff, 0x56, 0x32, 0x84, 0x17, 0x69,
	0xf6, 0x87, 0xc2, 0x40, 0xef, 0x8f, 0x5b, 0x50, 0xcf, 0x89, 0x7d, 0xd6, 0x23, 0xbf, 0x2a, 0xd0,
	0xba, 0xee, 0x70, 0x90, 0xad, 0xb9, 0xc3, 0x70, 0xcd, 0x7d, 0x69, 0xbf, 0xb1, 0x9d, 0x5f, 0xd4,
	0xed, 0xbd, 0x8f, 0xc7, 0xe9, 0x45, 0x9d, 0xb8, 0x81, 0x48, 0x8c, 0xdd, 0x31, 0xed, 0x7f, 0x73,
	0x7a, 0xa1, 0x2a, 0x3f, 0x9e, 0x5e, 0xa8, 0xb7, 0xa3, 0xbe, 0xe8, 0x1f, 0xf5, 0x7f, 0x39, 0xbd,
	0x50, 0xdf, 0xee, 0x46, 0xda, 0x20, 0xb2, 0xad, 0xa1, 0xd6, 0x15, 0xda, 0x20, 0x3f, 0x77, 0x43,
	0xad, 0x7b, 0xa4, 0x0d, 0xb2, 0xcd, 0x0e, 0xc9, 0xd7, 0xb0, 0x3a, 0x67, 0x2f, 0x52, 0x96, 0xf8,
	0x2a, 0xeb, 0xb6, 0xe9, 0xa2, 0x14, 0x1e, 0xd0, 0x4d, 0xc4, 0x55, 0x41, 0x5c, 0x95, 0x14, 0x13,
	0x29, 0xce, 0x96, 0x9e, 0x1d, 0x92, 0x1f, 0x14, 0x58, 0xbf, 0xda, 0x46, 0xe4, 0x9d, 0x1b, 0x67,
	0xe4, 0x56, 0x6d, 0xbf, 0xfb, 0x4a, 0x79, 0x3c, 0xa0, 0x1d, 0x04, 0xa4, 0x16, 0x00, 0x35, 0xca,
	0x62, 0x7c, 0xa7, 0xc0, 0xca, 0x8c, 0x45, 0x48, 0xf9, 0x7a, 0xcf, 0x1b, 0x68, 0xc1, 0x6e, 0x1e,
	0xe1, 0xc8, 0x3f, 0xd3, 0xe5, 0x24, 0x7d, 0xb7, 0x6f, 0xc9, 0xb9, 0x6f, 0x76, 0x13, 0x6d, 0x80,
	0x26, 0x1b, 0x6a, 0x5d, 0x57, 0x1b, 0x48, 0x4f, 0x0d, 0xb5, 0xae, 0xa5, 0x0d, 0x2c, 0x33, 0x19,
	0x12, 0x06, 0x0f, 0x1e, 0x47, 0x6e, 0x30, 0x72, 0x9c, 0xd2, 0x4f, 0xc2, 0xc9, 0x8d, 0xb3, 0x16,
	0x20, 0x69, 0x22, 0x92, 0x33, 0x89, 0x64, 0x09, 0x31, 0x2c, 0xb5, 0x6f, 0x7f, 0x7f, 0x7a, 0xa1,
	0xfe, 0x1c, 0x8f, 0x9b, 0x67, 0xe7, 0x1d, 0xe5, 0xaf, 0xf3, 0x8e, 0xf2, 0xef, 0x79, 0x47, 0xf9,
	0xe9, 0xbf, 0xce, 0xd2, 0xff, 0x01, 0x00, 0x00, 0xff, 0xff, 0xe9, 0x6a, 0x9f, 0x8f, 0xf8, 0x07,
	0x00, 0x00,
}
