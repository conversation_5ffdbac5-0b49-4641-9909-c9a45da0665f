// Code generated by protoc-gen-go. DO NOT EDIT.
// source: highcontent/highcontent.proto

package highcontent // import "golang.52tt.com/protocol/services/highcontent"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PostType int32

const (
	PostType_Kol PostType = 0
	PostType_Tgl PostType = 1
)

var PostType_name = map[int32]string{
	0: "Kol",
	1: "Tgl",
}
var PostType_value = map[string]int32{
	"Kol": 0,
	"Tgl": 1,
}

func (x PostType) String() string {
	return proto.EnumName(PostType_name, int32(x))
}
func (PostType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{0}
}

// state
type GetStreamState int32

const (
	GetStreamState_GET_STREAM_STATE_NO_TAGS      GetStreamState = 0
	GetStreamState_GET_STREAM_STATE_TAGS         GetStreamState = 1
	GetStreamState_GET_STREAM_STATE_WEIGHT       GetStreamState = 2
	GetStreamState_GET_STREAM_STATE_NO_RECOMMEND GetStreamState = 3
)

var GetStreamState_name = map[int32]string{
	0: "GET_STREAM_STATE_NO_TAGS",
	1: "GET_STREAM_STATE_TAGS",
	2: "GET_STREAM_STATE_WEIGHT",
	3: "GET_STREAM_STATE_NO_RECOMMEND",
}
var GetStreamState_value = map[string]int32{
	"GET_STREAM_STATE_NO_TAGS":      0,
	"GET_STREAM_STATE_TAGS":         1,
	"GET_STREAM_STATE_WEIGHT":       2,
	"GET_STREAM_STATE_NO_RECOMMEND": 3,
}

func (x GetStreamState) String() string {
	return proto.EnumName(GetStreamState_name, int32(x))
}
func (GetStreamState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{1}
}

type RecommendType int32

const (
	RecommendType_DEFAULT_RECOMMEND RecommendType = 0
	RecommendType_NOT_RECOMMEND     RecommendType = 1
	RecommendType_WEIGHT_RECOMMEND  RecommendType = 2
)

var RecommendType_name = map[int32]string{
	0: "DEFAULT_RECOMMEND",
	1: "NOT_RECOMMEND",
	2: "WEIGHT_RECOMMEND",
}
var RecommendType_value = map[string]int32{
	"DEFAULT_RECOMMEND": 0,
	"NOT_RECOMMEND":     1,
	"WEIGHT_RECOMMEND":  2,
}

func (x RecommendType) String() string {
	return proto.EnumName(RecommendType_name, int32(x))
}
func (RecommendType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{2}
}

// kol...........................................................................
type KOLLevel int32

const (
	KOLLevel_KOL_LEVEL_NONE KOLLevel = 0
	KOLLevel_KOL_LEVEL_S1   KOLLevel = 1
	KOLLevel_KOL_LEVEL_S2   KOLLevel = 2
	KOLLevel_KOL_LEVEL_P1   KOLLevel = 3
	KOLLevel_KOL_LEVEL_P2   KOLLevel = 4
	KOLLevel_KOL_LEVEL_P3   KOLLevel = 5
)

var KOLLevel_name = map[int32]string{
	0: "KOL_LEVEL_NONE",
	1: "KOL_LEVEL_S1",
	2: "KOL_LEVEL_S2",
	3: "KOL_LEVEL_P1",
	4: "KOL_LEVEL_P2",
	5: "KOL_LEVEL_P3",
}
var KOLLevel_value = map[string]int32{
	"KOL_LEVEL_NONE": 0,
	"KOL_LEVEL_S1":   1,
	"KOL_LEVEL_S2":   2,
	"KOL_LEVEL_P1":   3,
	"KOL_LEVEL_P2":   4,
	"KOL_LEVEL_P3":   5,
}

func (x KOLLevel) String() string {
	return proto.EnumName(KOLLevel_name, int32(x))
}
func (KOLLevel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{3}
}

// ///////////////////////////////////////////////////////////////////////////////////////////
// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~tglrobot manager~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
type TglRobotStatusType int32

const (
	TglRobotStatusType_All       TglRobotStatusType = 0
	TglRobotStatusType_Normal    TglRobotStatusType = 1
	TglRobotStatusType_Blacklist TglRobotStatusType = 2
)

var TglRobotStatusType_name = map[int32]string{
	0: "All",
	1: "Normal",
	2: "Blacklist",
}
var TglRobotStatusType_value = map[string]int32{
	"All":       0,
	"Normal":    1,
	"Blacklist": 2,
}

func (x TglRobotStatusType) String() string {
	return proto.EnumName(TglRobotStatusType_name, int32(x))
}
func (TglRobotStatusType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{4}
}

type StreamItem struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Time                 uint32   `protobuf:"varint,2,opt,name=time,proto3" json:"time,omitempty"`
	Recommend            uint32   `protobuf:"varint,3,opt,name=recommend,proto3" json:"recommend,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	Tags                 []uint32 `protobuf:"varint,5,rep,packed,name=tags,proto3" json:"tags,omitempty"`
	Level                uint32   `protobuf:"varint,6,opt,name=level,proto3" json:"level,omitempty"`
	Status               uint32   `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`
	CoverUrl             string   `protobuf:"bytes,8,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"`
	Size                 string   `protobuf:"bytes,9,opt,name=size,proto3" json:"size,omitempty"`
	TglId                uint32   `protobuf:"varint,10,opt,name=tgl_id,json=tglId,proto3" json:"tgl_id,omitempty"`
	ValidBeginTime       int64    `protobuf:"varint,11,opt,name=valid_begin_time,json=validBeginTime,proto3" json:"valid_begin_time,omitempty"`
	ValidEndTime         int64    `protobuf:"varint,12,opt,name=valid_end_time,json=validEndTime,proto3" json:"valid_end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StreamItem) Reset()         { *m = StreamItem{} }
func (m *StreamItem) String() string { return proto.CompactTextString(m) }
func (*StreamItem) ProtoMessage()    {}
func (*StreamItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{0}
}
func (m *StreamItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StreamItem.Unmarshal(m, b)
}
func (m *StreamItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StreamItem.Marshal(b, m, deterministic)
}
func (dst *StreamItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StreamItem.Merge(dst, src)
}
func (m *StreamItem) XXX_Size() int {
	return xxx_messageInfo_StreamItem.Size(m)
}
func (m *StreamItem) XXX_DiscardUnknown() {
	xxx_messageInfo_StreamItem.DiscardUnknown(m)
}

var xxx_messageInfo_StreamItem proto.InternalMessageInfo

func (m *StreamItem) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *StreamItem) GetTime() uint32 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *StreamItem) GetRecommend() uint32 {
	if m != nil {
		return m.Recommend
	}
	return 0
}

func (m *StreamItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StreamItem) GetTags() []uint32 {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *StreamItem) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *StreamItem) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *StreamItem) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *StreamItem) GetSize() string {
	if m != nil {
		return m.Size
	}
	return ""
}

func (m *StreamItem) GetTglId() uint32 {
	if m != nil {
		return m.TglId
	}
	return 0
}

func (m *StreamItem) GetValidBeginTime() int64 {
	if m != nil {
		return m.ValidBeginTime
	}
	return 0
}

func (m *StreamItem) GetValidEndTime() int64 {
	if m != nil {
		return m.ValidEndTime
	}
	return 0
}

type AddStreamItemReq struct {
	PostType             PostType    `protobuf:"varint,1,opt,name=post_type,json=postType,proto3,enum=highcontent.PostType" json:"post_type,omitempty"`
	Info                 *StreamItem `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *AddStreamItemReq) Reset()         { *m = AddStreamItemReq{} }
func (m *AddStreamItemReq) String() string { return proto.CompactTextString(m) }
func (*AddStreamItemReq) ProtoMessage()    {}
func (*AddStreamItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{1}
}
func (m *AddStreamItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddStreamItemReq.Unmarshal(m, b)
}
func (m *AddStreamItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddStreamItemReq.Marshal(b, m, deterministic)
}
func (dst *AddStreamItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddStreamItemReq.Merge(dst, src)
}
func (m *AddStreamItemReq) XXX_Size() int {
	return xxx_messageInfo_AddStreamItemReq.Size(m)
}
func (m *AddStreamItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddStreamItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddStreamItemReq proto.InternalMessageInfo

func (m *AddStreamItemReq) GetPostType() PostType {
	if m != nil {
		return m.PostType
	}
	return PostType_Kol
}

func (m *AddStreamItemReq) GetInfo() *StreamItem {
	if m != nil {
		return m.Info
	}
	return nil
}

type AddStreamItemRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddStreamItemRsp) Reset()         { *m = AddStreamItemRsp{} }
func (m *AddStreamItemRsp) String() string { return proto.CompactTextString(m) }
func (*AddStreamItemRsp) ProtoMessage()    {}
func (*AddStreamItemRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{2}
}
func (m *AddStreamItemRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddStreamItemRsp.Unmarshal(m, b)
}
func (m *AddStreamItemRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddStreamItemRsp.Marshal(b, m, deterministic)
}
func (dst *AddStreamItemRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddStreamItemRsp.Merge(dst, src)
}
func (m *AddStreamItemRsp) XXX_Size() int {
	return xxx_messageInfo_AddStreamItemRsp.Size(m)
}
func (m *AddStreamItemRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddStreamItemRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AddStreamItemRsp proto.InternalMessageInfo

type UpdateStreamRecommendReq struct {
	PostType             PostType `protobuf:"varint,1,opt,name=post_type,json=postType,proto3,enum=highcontent.PostType" json:"post_type,omitempty"`
	Postids              []string `protobuf:"bytes,2,rep,name=postids,proto3" json:"postids,omitempty"`
	Recommend            uint32   `protobuf:"varint,3,opt,name=recommend,proto3" json:"recommend,omitempty"`
	BeginTime            int64    `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ValidBeginTime       int64    `protobuf:"varint,6,opt,name=valid_begin_time,json=validBeginTime,proto3" json:"valid_begin_time,omitempty"`
	ValidEndTime         int64    `protobuf:"varint,7,opt,name=valid_end_time,json=validEndTime,proto3" json:"valid_end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateStreamRecommendReq) Reset()         { *m = UpdateStreamRecommendReq{} }
func (m *UpdateStreamRecommendReq) String() string { return proto.CompactTextString(m) }
func (*UpdateStreamRecommendReq) ProtoMessage()    {}
func (*UpdateStreamRecommendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{3}
}
func (m *UpdateStreamRecommendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStreamRecommendReq.Unmarshal(m, b)
}
func (m *UpdateStreamRecommendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStreamRecommendReq.Marshal(b, m, deterministic)
}
func (dst *UpdateStreamRecommendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStreamRecommendReq.Merge(dst, src)
}
func (m *UpdateStreamRecommendReq) XXX_Size() int {
	return xxx_messageInfo_UpdateStreamRecommendReq.Size(m)
}
func (m *UpdateStreamRecommendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStreamRecommendReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStreamRecommendReq proto.InternalMessageInfo

func (m *UpdateStreamRecommendReq) GetPostType() PostType {
	if m != nil {
		return m.PostType
	}
	return PostType_Kol
}

func (m *UpdateStreamRecommendReq) GetPostids() []string {
	if m != nil {
		return m.Postids
	}
	return nil
}

func (m *UpdateStreamRecommendReq) GetRecommend() uint32 {
	if m != nil {
		return m.Recommend
	}
	return 0
}

func (m *UpdateStreamRecommendReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *UpdateStreamRecommendReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *UpdateStreamRecommendReq) GetValidBeginTime() int64 {
	if m != nil {
		return m.ValidBeginTime
	}
	return 0
}

func (m *UpdateStreamRecommendReq) GetValidEndTime() int64 {
	if m != nil {
		return m.ValidEndTime
	}
	return 0
}

type UpdateStreamRecommendRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateStreamRecommendRsp) Reset()         { *m = UpdateStreamRecommendRsp{} }
func (m *UpdateStreamRecommendRsp) String() string { return proto.CompactTextString(m) }
func (*UpdateStreamRecommendRsp) ProtoMessage()    {}
func (*UpdateStreamRecommendRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{4}
}
func (m *UpdateStreamRecommendRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStreamRecommendRsp.Unmarshal(m, b)
}
func (m *UpdateStreamRecommendRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStreamRecommendRsp.Marshal(b, m, deterministic)
}
func (dst *UpdateStreamRecommendRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStreamRecommendRsp.Merge(dst, src)
}
func (m *UpdateStreamRecommendRsp) XXX_Size() int {
	return xxx_messageInfo_UpdateStreamRecommendRsp.Size(m)
}
func (m *UpdateStreamRecommendRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStreamRecommendRsp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStreamRecommendRsp proto.InternalMessageInfo

type UpdateStreamStatusReq struct {
	PostType             PostType `protobuf:"varint,1,opt,name=post_type,json=postType,proto3,enum=highcontent.PostType" json:"post_type,omitempty"`
	Postid               string   `protobuf:"bytes,2,opt,name=postid,proto3" json:"postid,omitempty"`
	Status               uint32   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateStreamStatusReq) Reset()         { *m = UpdateStreamStatusReq{} }
func (m *UpdateStreamStatusReq) String() string { return proto.CompactTextString(m) }
func (*UpdateStreamStatusReq) ProtoMessage()    {}
func (*UpdateStreamStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{5}
}
func (m *UpdateStreamStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStreamStatusReq.Unmarshal(m, b)
}
func (m *UpdateStreamStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStreamStatusReq.Marshal(b, m, deterministic)
}
func (dst *UpdateStreamStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStreamStatusReq.Merge(dst, src)
}
func (m *UpdateStreamStatusReq) XXX_Size() int {
	return xxx_messageInfo_UpdateStreamStatusReq.Size(m)
}
func (m *UpdateStreamStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStreamStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStreamStatusReq proto.InternalMessageInfo

func (m *UpdateStreamStatusReq) GetPostType() PostType {
	if m != nil {
		return m.PostType
	}
	return PostType_Kol
}

func (m *UpdateStreamStatusReq) GetPostid() string {
	if m != nil {
		return m.Postid
	}
	return ""
}

func (m *UpdateStreamStatusReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type UpdateStreamStatusRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateStreamStatusRsp) Reset()         { *m = UpdateStreamStatusRsp{} }
func (m *UpdateStreamStatusRsp) String() string { return proto.CompactTextString(m) }
func (*UpdateStreamStatusRsp) ProtoMessage()    {}
func (*UpdateStreamStatusRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{6}
}
func (m *UpdateStreamStatusRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStreamStatusRsp.Unmarshal(m, b)
}
func (m *UpdateStreamStatusRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStreamStatusRsp.Marshal(b, m, deterministic)
}
func (dst *UpdateStreamStatusRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStreamStatusRsp.Merge(dst, src)
}
func (m *UpdateStreamStatusRsp) XXX_Size() int {
	return xxx_messageInfo_UpdateStreamStatusRsp.Size(m)
}
func (m *UpdateStreamStatusRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStreamStatusRsp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStreamStatusRsp proto.InternalMessageInfo

type DeleteStreamItemReq struct {
	PostType             PostType `protobuf:"varint,1,opt,name=post_type,json=postType,proto3,enum=highcontent.PostType" json:"post_type,omitempty"`
	Postid               string   `protobuf:"bytes,2,opt,name=postid,proto3" json:"postid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteStreamItemReq) Reset()         { *m = DeleteStreamItemReq{} }
func (m *DeleteStreamItemReq) String() string { return proto.CompactTextString(m) }
func (*DeleteStreamItemReq) ProtoMessage()    {}
func (*DeleteStreamItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{7}
}
func (m *DeleteStreamItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteStreamItemReq.Unmarshal(m, b)
}
func (m *DeleteStreamItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteStreamItemReq.Marshal(b, m, deterministic)
}
func (dst *DeleteStreamItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteStreamItemReq.Merge(dst, src)
}
func (m *DeleteStreamItemReq) XXX_Size() int {
	return xxx_messageInfo_DeleteStreamItemReq.Size(m)
}
func (m *DeleteStreamItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteStreamItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteStreamItemReq proto.InternalMessageInfo

func (m *DeleteStreamItemReq) GetPostType() PostType {
	if m != nil {
		return m.PostType
	}
	return PostType_Kol
}

func (m *DeleteStreamItemReq) GetPostid() string {
	if m != nil {
		return m.Postid
	}
	return ""
}

type DeleteStreamItemRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteStreamItemRsp) Reset()         { *m = DeleteStreamItemRsp{} }
func (m *DeleteStreamItemRsp) String() string { return proto.CompactTextString(m) }
func (*DeleteStreamItemRsp) ProtoMessage()    {}
func (*DeleteStreamItemRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{8}
}
func (m *DeleteStreamItemRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteStreamItemRsp.Unmarshal(m, b)
}
func (m *DeleteStreamItemRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteStreamItemRsp.Marshal(b, m, deterministic)
}
func (dst *DeleteStreamItemRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteStreamItemRsp.Merge(dst, src)
}
func (m *DeleteStreamItemRsp) XXX_Size() int {
	return xxx_messageInfo_DeleteStreamItemRsp.Size(m)
}
func (m *DeleteStreamItemRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteStreamItemRsp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteStreamItemRsp proto.InternalMessageInfo

type GetStreamItemReq struct {
	PostType             PostType `protobuf:"varint,1,opt,name=post_type,json=postType,proto3,enum=highcontent.PostType" json:"post_type,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	BeginTime            int64    `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	PostId               string   `protobuf:"bytes,6,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Tags                 []uint32 `protobuf:"varint,7,rep,packed,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStreamItemReq) Reset()         { *m = GetStreamItemReq{} }
func (m *GetStreamItemReq) String() string { return proto.CompactTextString(m) }
func (*GetStreamItemReq) ProtoMessage()    {}
func (*GetStreamItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{9}
}
func (m *GetStreamItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStreamItemReq.Unmarshal(m, b)
}
func (m *GetStreamItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStreamItemReq.Marshal(b, m, deterministic)
}
func (dst *GetStreamItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStreamItemReq.Merge(dst, src)
}
func (m *GetStreamItemReq) XXX_Size() int {
	return xxx_messageInfo_GetStreamItemReq.Size(m)
}
func (m *GetStreamItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStreamItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStreamItemReq proto.InternalMessageInfo

func (m *GetStreamItemReq) GetPostType() PostType {
	if m != nil {
		return m.PostType
	}
	return PostType_Kol
}

func (m *GetStreamItemReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetStreamItemReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetStreamItemReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetStreamItemReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetStreamItemReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetStreamItemReq) GetTags() []uint32 {
	if m != nil {
		return m.Tags
	}
	return nil
}

type GetStreamItemRsp struct {
	Info                 []*StreamItem `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	Cnt                  uint32        `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetStreamItemRsp) Reset()         { *m = GetStreamItemRsp{} }
func (m *GetStreamItemRsp) String() string { return proto.CompactTextString(m) }
func (*GetStreamItemRsp) ProtoMessage()    {}
func (*GetStreamItemRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{10}
}
func (m *GetStreamItemRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStreamItemRsp.Unmarshal(m, b)
}
func (m *GetStreamItemRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStreamItemRsp.Marshal(b, m, deterministic)
}
func (dst *GetStreamItemRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStreamItemRsp.Merge(dst, src)
}
func (m *GetStreamItemRsp) XXX_Size() int {
	return xxx_messageInfo_GetStreamItemRsp.Size(m)
}
func (m *GetStreamItemRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStreamItemRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStreamItemRsp proto.InternalMessageInfo

func (m *GetStreamItemRsp) GetInfo() []*StreamItem {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetStreamItemRsp) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type GetStreamItemByUidReq struct {
	PostType             PostType `protobuf:"varint,1,opt,name=post_type,json=postType,proto3,enum=highcontent.PostType" json:"post_type,omitempty"`
	BeginTime            int64    `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStreamItemByUidReq) Reset()         { *m = GetStreamItemByUidReq{} }
func (m *GetStreamItemByUidReq) String() string { return proto.CompactTextString(m) }
func (*GetStreamItemByUidReq) ProtoMessage()    {}
func (*GetStreamItemByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{11}
}
func (m *GetStreamItemByUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStreamItemByUidReq.Unmarshal(m, b)
}
func (m *GetStreamItemByUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStreamItemByUidReq.Marshal(b, m, deterministic)
}
func (dst *GetStreamItemByUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStreamItemByUidReq.Merge(dst, src)
}
func (m *GetStreamItemByUidReq) XXX_Size() int {
	return xxx_messageInfo_GetStreamItemByUidReq.Size(m)
}
func (m *GetStreamItemByUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStreamItemByUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStreamItemByUidReq proto.InternalMessageInfo

func (m *GetStreamItemByUidReq) GetPostType() PostType {
	if m != nil {
		return m.PostType
	}
	return PostType_Kol
}

func (m *GetStreamItemByUidReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetStreamItemByUidReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetStreamItemByUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetStreamItemByUidRsp struct {
	Info                 []*StreamItem `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	Cnt                  uint32        `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetStreamItemByUidRsp) Reset()         { *m = GetStreamItemByUidRsp{} }
func (m *GetStreamItemByUidRsp) String() string { return proto.CompactTextString(m) }
func (*GetStreamItemByUidRsp) ProtoMessage()    {}
func (*GetStreamItemByUidRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{12}
}
func (m *GetStreamItemByUidRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStreamItemByUidRsp.Unmarshal(m, b)
}
func (m *GetStreamItemByUidRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStreamItemByUidRsp.Marshal(b, m, deterministic)
}
func (dst *GetStreamItemByUidRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStreamItemByUidRsp.Merge(dst, src)
}
func (m *GetStreamItemByUidRsp) XXX_Size() int {
	return xxx_messageInfo_GetStreamItemByUidRsp.Size(m)
}
func (m *GetStreamItemByUidRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStreamItemByUidRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStreamItemByUidRsp proto.InternalMessageInfo

func (m *GetStreamItemByUidRsp) GetInfo() []*StreamItem {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetStreamItemByUidRsp) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type GetStreamItemByPostidsReq struct {
	PostType             PostType `protobuf:"varint,1,opt,name=post_type,json=postType,proto3,enum=highcontent.PostType" json:"post_type,omitempty"`
	Postids              []string `protobuf:"bytes,2,rep,name=postids,proto3" json:"postids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStreamItemByPostidsReq) Reset()         { *m = GetStreamItemByPostidsReq{} }
func (m *GetStreamItemByPostidsReq) String() string { return proto.CompactTextString(m) }
func (*GetStreamItemByPostidsReq) ProtoMessage()    {}
func (*GetStreamItemByPostidsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{13}
}
func (m *GetStreamItemByPostidsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStreamItemByPostidsReq.Unmarshal(m, b)
}
func (m *GetStreamItemByPostidsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStreamItemByPostidsReq.Marshal(b, m, deterministic)
}
func (dst *GetStreamItemByPostidsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStreamItemByPostidsReq.Merge(dst, src)
}
func (m *GetStreamItemByPostidsReq) XXX_Size() int {
	return xxx_messageInfo_GetStreamItemByPostidsReq.Size(m)
}
func (m *GetStreamItemByPostidsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStreamItemByPostidsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStreamItemByPostidsReq proto.InternalMessageInfo

func (m *GetStreamItemByPostidsReq) GetPostType() PostType {
	if m != nil {
		return m.PostType
	}
	return PostType_Kol
}

func (m *GetStreamItemByPostidsReq) GetPostids() []string {
	if m != nil {
		return m.Postids
	}
	return nil
}

type GetStreamItemByPostidsRsp struct {
	Info                 []*StreamItem `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetStreamItemByPostidsRsp) Reset()         { *m = GetStreamItemByPostidsRsp{} }
func (m *GetStreamItemByPostidsRsp) String() string { return proto.CompactTextString(m) }
func (*GetStreamItemByPostidsRsp) ProtoMessage()    {}
func (*GetStreamItemByPostidsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{14}
}
func (m *GetStreamItemByPostidsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStreamItemByPostidsRsp.Unmarshal(m, b)
}
func (m *GetStreamItemByPostidsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStreamItemByPostidsRsp.Marshal(b, m, deterministic)
}
func (dst *GetStreamItemByPostidsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStreamItemByPostidsRsp.Merge(dst, src)
}
func (m *GetStreamItemByPostidsRsp) XXX_Size() int {
	return xxx_messageInfo_GetStreamItemByPostidsRsp.Size(m)
}
func (m *GetStreamItemByPostidsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStreamItemByPostidsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStreamItemByPostidsRsp proto.InternalMessageInfo

func (m *GetStreamItemByPostidsRsp) GetInfo() []*StreamItem {
	if m != nil {
		return m.Info
	}
	return nil
}

// 运营后台
type GetStreamsReq struct {
	PostType             PostType `protobuf:"varint,1,opt,name=post_type,json=postType,proto3,enum=highcontent.PostType" json:"post_type,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	BeginTime            int64    `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	State                uint32   `protobuf:"varint,6,opt,name=state,proto3" json:"state,omitempty"`
	Uid                  uint32   `protobuf:"varint,7,opt,name=uid,proto3" json:"uid,omitempty"`
	KolLevel             uint32   `protobuf:"varint,8,opt,name=kol_level,json=kolLevel,proto3" json:"kol_level,omitempty"`
	Tags                 []uint32 `protobuf:"varint,9,rep,packed,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStreamsReq) Reset()         { *m = GetStreamsReq{} }
func (m *GetStreamsReq) String() string { return proto.CompactTextString(m) }
func (*GetStreamsReq) ProtoMessage()    {}
func (*GetStreamsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{15}
}
func (m *GetStreamsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStreamsReq.Unmarshal(m, b)
}
func (m *GetStreamsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStreamsReq.Marshal(b, m, deterministic)
}
func (dst *GetStreamsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStreamsReq.Merge(dst, src)
}
func (m *GetStreamsReq) XXX_Size() int {
	return xxx_messageInfo_GetStreamsReq.Size(m)
}
func (m *GetStreamsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStreamsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStreamsReq proto.InternalMessageInfo

func (m *GetStreamsReq) GetPostType() PostType {
	if m != nil {
		return m.PostType
	}
	return PostType_Kol
}

func (m *GetStreamsReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetStreamsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetStreamsReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetStreamsReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetStreamsReq) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *GetStreamsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetStreamsReq) GetKolLevel() uint32 {
	if m != nil {
		return m.KolLevel
	}
	return 0
}

func (m *GetStreamsReq) GetTags() []uint32 {
	if m != nil {
		return m.Tags
	}
	return nil
}

type GetStreamsResp struct {
	Info                 []*StreamItem `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	Cnt                  uint32        `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetStreamsResp) Reset()         { *m = GetStreamsResp{} }
func (m *GetStreamsResp) String() string { return proto.CompactTextString(m) }
func (*GetStreamsResp) ProtoMessage()    {}
func (*GetStreamsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{16}
}
func (m *GetStreamsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStreamsResp.Unmarshal(m, b)
}
func (m *GetStreamsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStreamsResp.Marshal(b, m, deterministic)
}
func (dst *GetStreamsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStreamsResp.Merge(dst, src)
}
func (m *GetStreamsResp) XXX_Size() int {
	return xxx_messageInfo_GetStreamsResp.Size(m)
}
func (m *GetStreamsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStreamsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStreamsResp proto.InternalMessageInfo

func (m *GetStreamsResp) GetInfo() []*StreamItem {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetStreamsResp) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type GetSecondLevelStreamItemReq struct {
	PostType             PostType `protobuf:"varint,1,opt,name=post_type,json=postType,proto3,enum=highcontent.PostType" json:"post_type,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Tags                 []uint32 `protobuf:"varint,4,rep,packed,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSecondLevelStreamItemReq) Reset()         { *m = GetSecondLevelStreamItemReq{} }
func (m *GetSecondLevelStreamItemReq) String() string { return proto.CompactTextString(m) }
func (*GetSecondLevelStreamItemReq) ProtoMessage()    {}
func (*GetSecondLevelStreamItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{17}
}
func (m *GetSecondLevelStreamItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSecondLevelStreamItemReq.Unmarshal(m, b)
}
func (m *GetSecondLevelStreamItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSecondLevelStreamItemReq.Marshal(b, m, deterministic)
}
func (dst *GetSecondLevelStreamItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSecondLevelStreamItemReq.Merge(dst, src)
}
func (m *GetSecondLevelStreamItemReq) XXX_Size() int {
	return xxx_messageInfo_GetSecondLevelStreamItemReq.Size(m)
}
func (m *GetSecondLevelStreamItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSecondLevelStreamItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSecondLevelStreamItemReq proto.InternalMessageInfo

func (m *GetSecondLevelStreamItemReq) GetPostType() PostType {
	if m != nil {
		return m.PostType
	}
	return PostType_Kol
}

func (m *GetSecondLevelStreamItemReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetSecondLevelStreamItemReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetSecondLevelStreamItemReq) GetTags() []uint32 {
	if m != nil {
		return m.Tags
	}
	return nil
}

type GetSecondLevelStreamItemRsp struct {
	Info                 []*StreamItem `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetSecondLevelStreamItemRsp) Reset()         { *m = GetSecondLevelStreamItemRsp{} }
func (m *GetSecondLevelStreamItemRsp) String() string { return proto.CompactTextString(m) }
func (*GetSecondLevelStreamItemRsp) ProtoMessage()    {}
func (*GetSecondLevelStreamItemRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{18}
}
func (m *GetSecondLevelStreamItemRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSecondLevelStreamItemRsp.Unmarshal(m, b)
}
func (m *GetSecondLevelStreamItemRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSecondLevelStreamItemRsp.Marshal(b, m, deterministic)
}
func (dst *GetSecondLevelStreamItemRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSecondLevelStreamItemRsp.Merge(dst, src)
}
func (m *GetSecondLevelStreamItemRsp) XXX_Size() int {
	return xxx_messageInfo_GetSecondLevelStreamItemRsp.Size(m)
}
func (m *GetSecondLevelStreamItemRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSecondLevelStreamItemRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSecondLevelStreamItemRsp proto.InternalMessageInfo

func (m *GetSecondLevelStreamItemRsp) GetInfo() []*StreamItem {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetPostUpdateExistReq struct {
	PostType             PostType `protobuf:"varint,1,opt,name=post_type,json=postType,proto3,enum=highcontent.PostType" json:"post_type,omitempty"`
	Uids                 []uint32 `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPostUpdateExistReq) Reset()         { *m = GetPostUpdateExistReq{} }
func (m *GetPostUpdateExistReq) String() string { return proto.CompactTextString(m) }
func (*GetPostUpdateExistReq) ProtoMessage()    {}
func (*GetPostUpdateExistReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{19}
}
func (m *GetPostUpdateExistReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostUpdateExistReq.Unmarshal(m, b)
}
func (m *GetPostUpdateExistReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostUpdateExistReq.Marshal(b, m, deterministic)
}
func (dst *GetPostUpdateExistReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostUpdateExistReq.Merge(dst, src)
}
func (m *GetPostUpdateExistReq) XXX_Size() int {
	return xxx_messageInfo_GetPostUpdateExistReq.Size(m)
}
func (m *GetPostUpdateExistReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostUpdateExistReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostUpdateExistReq proto.InternalMessageInfo

func (m *GetPostUpdateExistReq) GetPostType() PostType {
	if m != nil {
		return m.PostType
	}
	return PostType_Kol
}

func (m *GetPostUpdateExistReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type GetPostUpdateExistRsp struct {
	Info                 []*StreamItem `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetPostUpdateExistRsp) Reset()         { *m = GetPostUpdateExistRsp{} }
func (m *GetPostUpdateExistRsp) String() string { return proto.CompactTextString(m) }
func (*GetPostUpdateExistRsp) ProtoMessage()    {}
func (*GetPostUpdateExistRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{20}
}
func (m *GetPostUpdateExistRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostUpdateExistRsp.Unmarshal(m, b)
}
func (m *GetPostUpdateExistRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostUpdateExistRsp.Marshal(b, m, deterministic)
}
func (dst *GetPostUpdateExistRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostUpdateExistRsp.Merge(dst, src)
}
func (m *GetPostUpdateExistRsp) XXX_Size() int {
	return xxx_messageInfo_GetPostUpdateExistRsp.Size(m)
}
func (m *GetPostUpdateExistRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostUpdateExistRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostUpdateExistRsp proto.InternalMessageInfo

func (m *GetPostUpdateExistRsp) GetInfo() []*StreamItem {
	if m != nil {
		return m.Info
	}
	return nil
}

type KOLUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	Tag                  uint32   `protobuf:"varint,3,opt,name=tag,proto3" json:"tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KOLUserInfo) Reset()         { *m = KOLUserInfo{} }
func (m *KOLUserInfo) String() string { return proto.CompactTextString(m) }
func (*KOLUserInfo) ProtoMessage()    {}
func (*KOLUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{21}
}
func (m *KOLUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KOLUserInfo.Unmarshal(m, b)
}
func (m *KOLUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KOLUserInfo.Marshal(b, m, deterministic)
}
func (dst *KOLUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KOLUserInfo.Merge(dst, src)
}
func (m *KOLUserInfo) XXX_Size() int {
	return xxx_messageInfo_KOLUserInfo.Size(m)
}
func (m *KOLUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_KOLUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_KOLUserInfo proto.InternalMessageInfo

func (m *KOLUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *KOLUserInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *KOLUserInfo) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

type DelKOLUsersReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelKOLUsersReq) Reset()         { *m = DelKOLUsersReq{} }
func (m *DelKOLUsersReq) String() string { return proto.CompactTextString(m) }
func (*DelKOLUsersReq) ProtoMessage()    {}
func (*DelKOLUsersReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{22}
}
func (m *DelKOLUsersReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelKOLUsersReq.Unmarshal(m, b)
}
func (m *DelKOLUsersReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelKOLUsersReq.Marshal(b, m, deterministic)
}
func (dst *DelKOLUsersReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelKOLUsersReq.Merge(dst, src)
}
func (m *DelKOLUsersReq) XXX_Size() int {
	return xxx_messageInfo_DelKOLUsersReq.Size(m)
}
func (m *DelKOLUsersReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelKOLUsersReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelKOLUsersReq proto.InternalMessageInfo

func (m *DelKOLUsersReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type DelKOLUsersResp struct {
	SuccessList          []*KOLExistedMapInfo `protobuf:"bytes,1,rep,name=success_list,json=successList,proto3" json:"success_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *DelKOLUsersResp) Reset()         { *m = DelKOLUsersResp{} }
func (m *DelKOLUsersResp) String() string { return proto.CompactTextString(m) }
func (*DelKOLUsersResp) ProtoMessage()    {}
func (*DelKOLUsersResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{23}
}
func (m *DelKOLUsersResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelKOLUsersResp.Unmarshal(m, b)
}
func (m *DelKOLUsersResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelKOLUsersResp.Marshal(b, m, deterministic)
}
func (dst *DelKOLUsersResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelKOLUsersResp.Merge(dst, src)
}
func (m *DelKOLUsersResp) XXX_Size() int {
	return xxx_messageInfo_DelKOLUsersResp.Size(m)
}
func (m *DelKOLUsersResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelKOLUsersResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelKOLUsersResp proto.InternalMessageInfo

func (m *DelKOLUsersResp) GetSuccessList() []*KOLExistedMapInfo {
	if m != nil {
		return m.SuccessList
	}
	return nil
}

type GetKOLUsersReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetKOLUsersReq) Reset()         { *m = GetKOLUsersReq{} }
func (m *GetKOLUsersReq) String() string { return proto.CompactTextString(m) }
func (*GetKOLUsersReq) ProtoMessage()    {}
func (*GetKOLUsersReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{24}
}
func (m *GetKOLUsersReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKOLUsersReq.Unmarshal(m, b)
}
func (m *GetKOLUsersReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKOLUsersReq.Marshal(b, m, deterministic)
}
func (dst *GetKOLUsersReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKOLUsersReq.Merge(dst, src)
}
func (m *GetKOLUsersReq) XXX_Size() int {
	return xxx_messageInfo_GetKOLUsersReq.Size(m)
}
func (m *GetKOLUsersReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKOLUsersReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetKOLUsersReq proto.InternalMessageInfo

func (m *GetKOLUsersReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetKOLUsersReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GetKOLUsersReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetKOLUsersReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetKOLUsersResp struct {
	UserInfoList         []*KOLUserInfo `protobuf:"bytes,1,rep,name=user_info_list,json=userInfoList,proto3" json:"user_info_list,omitempty"`
	Cnt                  uint32         `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetKOLUsersResp) Reset()         { *m = GetKOLUsersResp{} }
func (m *GetKOLUsersResp) String() string { return proto.CompactTextString(m) }
func (*GetKOLUsersResp) ProtoMessage()    {}
func (*GetKOLUsersResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{25}
}
func (m *GetKOLUsersResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKOLUsersResp.Unmarshal(m, b)
}
func (m *GetKOLUsersResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKOLUsersResp.Marshal(b, m, deterministic)
}
func (dst *GetKOLUsersResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKOLUsersResp.Merge(dst, src)
}
func (m *GetKOLUsersResp) XXX_Size() int {
	return xxx_messageInfo_GetKOLUsersResp.Size(m)
}
func (m *GetKOLUsersResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKOLUsersResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetKOLUsersResp proto.InternalMessageInfo

func (m *GetKOLUsersResp) GetUserInfoList() []*KOLUserInfo {
	if m != nil {
		return m.UserInfoList
	}
	return nil
}

func (m *GetKOLUsersResp) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type KOLExistedReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KOLExistedReq) Reset()         { *m = KOLExistedReq{} }
func (m *KOLExistedReq) String() string { return proto.CompactTextString(m) }
func (*KOLExistedReq) ProtoMessage()    {}
func (*KOLExistedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{26}
}
func (m *KOLExistedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KOLExistedReq.Unmarshal(m, b)
}
func (m *KOLExistedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KOLExistedReq.Marshal(b, m, deterministic)
}
func (dst *KOLExistedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KOLExistedReq.Merge(dst, src)
}
func (m *KOLExistedReq) XXX_Size() int {
	return xxx_messageInfo_KOLExistedReq.Size(m)
}
func (m *KOLExistedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_KOLExistedReq.DiscardUnknown(m)
}

var xxx_messageInfo_KOLExistedReq proto.InternalMessageInfo

func (m *KOLExistedReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type KOLExistedResp struct {
	IsExisted            bool         `protobuf:"varint,1,opt,name=is_existed,json=isExisted,proto3" json:"is_existed,omitempty"`
	Info                 *KOLUserInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *KOLExistedResp) Reset()         { *m = KOLExistedResp{} }
func (m *KOLExistedResp) String() string { return proto.CompactTextString(m) }
func (*KOLExistedResp) ProtoMessage()    {}
func (*KOLExistedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{27}
}
func (m *KOLExistedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KOLExistedResp.Unmarshal(m, b)
}
func (m *KOLExistedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KOLExistedResp.Marshal(b, m, deterministic)
}
func (dst *KOLExistedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KOLExistedResp.Merge(dst, src)
}
func (m *KOLExistedResp) XXX_Size() int {
	return xxx_messageInfo_KOLExistedResp.Size(m)
}
func (m *KOLExistedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_KOLExistedResp.DiscardUnknown(m)
}

var xxx_messageInfo_KOLExistedResp proto.InternalMessageInfo

func (m *KOLExistedResp) GetIsExisted() bool {
	if m != nil {
		return m.IsExisted
	}
	return false
}

func (m *KOLExistedResp) GetInfo() *KOLUserInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type KOLExistedMapInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IsExisted            bool     `protobuf:"varint,2,opt,name=is_existed,json=isExisted,proto3" json:"is_existed,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KOLExistedMapInfo) Reset()         { *m = KOLExistedMapInfo{} }
func (m *KOLExistedMapInfo) String() string { return proto.CompactTextString(m) }
func (*KOLExistedMapInfo) ProtoMessage()    {}
func (*KOLExistedMapInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{28}
}
func (m *KOLExistedMapInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KOLExistedMapInfo.Unmarshal(m, b)
}
func (m *KOLExistedMapInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KOLExistedMapInfo.Marshal(b, m, deterministic)
}
func (dst *KOLExistedMapInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KOLExistedMapInfo.Merge(dst, src)
}
func (m *KOLExistedMapInfo) XXX_Size() int {
	return xxx_messageInfo_KOLExistedMapInfo.Size(m)
}
func (m *KOLExistedMapInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_KOLExistedMapInfo.DiscardUnknown(m)
}

var xxx_messageInfo_KOLExistedMapInfo proto.InternalMessageInfo

func (m *KOLExistedMapInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *KOLExistedMapInfo) GetIsExisted() bool {
	if m != nil {
		return m.IsExisted
	}
	return false
}

type BatchKOLExistedReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchKOLExistedReq) Reset()         { *m = BatchKOLExistedReq{} }
func (m *BatchKOLExistedReq) String() string { return proto.CompactTextString(m) }
func (*BatchKOLExistedReq) ProtoMessage()    {}
func (*BatchKOLExistedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{29}
}
func (m *BatchKOLExistedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchKOLExistedReq.Unmarshal(m, b)
}
func (m *BatchKOLExistedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchKOLExistedReq.Marshal(b, m, deterministic)
}
func (dst *BatchKOLExistedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchKOLExistedReq.Merge(dst, src)
}
func (m *BatchKOLExistedReq) XXX_Size() int {
	return xxx_messageInfo_BatchKOLExistedReq.Size(m)
}
func (m *BatchKOLExistedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchKOLExistedReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchKOLExistedReq proto.InternalMessageInfo

func (m *BatchKOLExistedReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchKOLExistedResp struct {
	IsExistedList        []*KOLExistedMapInfo `protobuf:"bytes,1,rep,name=is_existed_list,json=isExistedList,proto3" json:"is_existed_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatchKOLExistedResp) Reset()         { *m = BatchKOLExistedResp{} }
func (m *BatchKOLExistedResp) String() string { return proto.CompactTextString(m) }
func (*BatchKOLExistedResp) ProtoMessage()    {}
func (*BatchKOLExistedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{30}
}
func (m *BatchKOLExistedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchKOLExistedResp.Unmarshal(m, b)
}
func (m *BatchKOLExistedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchKOLExistedResp.Marshal(b, m, deterministic)
}
func (dst *BatchKOLExistedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchKOLExistedResp.Merge(dst, src)
}
func (m *BatchKOLExistedResp) XXX_Size() int {
	return xxx_messageInfo_BatchKOLExistedResp.Size(m)
}
func (m *BatchKOLExistedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchKOLExistedResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchKOLExistedResp proto.InternalMessageInfo

func (m *BatchKOLExistedResp) GetIsExistedList() []*KOLExistedMapInfo {
	if m != nil {
		return m.IsExistedList
	}
	return nil
}

type UpdateKOLUsersInfoReq struct {
	UserInfoList         []*KOLUserInfo `protobuf:"bytes,1,rep,name=user_info_list,json=userInfoList,proto3" json:"user_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UpdateKOLUsersInfoReq) Reset()         { *m = UpdateKOLUsersInfoReq{} }
func (m *UpdateKOLUsersInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateKOLUsersInfoReq) ProtoMessage()    {}
func (*UpdateKOLUsersInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{31}
}
func (m *UpdateKOLUsersInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateKOLUsersInfoReq.Unmarshal(m, b)
}
func (m *UpdateKOLUsersInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateKOLUsersInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpdateKOLUsersInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateKOLUsersInfoReq.Merge(dst, src)
}
func (m *UpdateKOLUsersInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpdateKOLUsersInfoReq.Size(m)
}
func (m *UpdateKOLUsersInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateKOLUsersInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateKOLUsersInfoReq proto.InternalMessageInfo

func (m *UpdateKOLUsersInfoReq) GetUserInfoList() []*KOLUserInfo {
	if m != nil {
		return m.UserInfoList
	}
	return nil
}

type UpdateKOLUsersInfoResp struct {
	SuccessList          []*KOLExistedMapInfo `protobuf:"bytes,1,rep,name=success_list,json=successList,proto3" json:"success_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UpdateKOLUsersInfoResp) Reset()         { *m = UpdateKOLUsersInfoResp{} }
func (m *UpdateKOLUsersInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpdateKOLUsersInfoResp) ProtoMessage()    {}
func (*UpdateKOLUsersInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{32}
}
func (m *UpdateKOLUsersInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateKOLUsersInfoResp.Unmarshal(m, b)
}
func (m *UpdateKOLUsersInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateKOLUsersInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpdateKOLUsersInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateKOLUsersInfoResp.Merge(dst, src)
}
func (m *UpdateKOLUsersInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpdateKOLUsersInfoResp.Size(m)
}
func (m *UpdateKOLUsersInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateKOLUsersInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateKOLUsersInfoResp proto.InternalMessageInfo

func (m *UpdateKOLUsersInfoResp) GetSuccessList() []*KOLExistedMapInfo {
	if m != nil {
		return m.SuccessList
	}
	return nil
}

type AddKOLUserReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddKOLUserReq) Reset()         { *m = AddKOLUserReq{} }
func (m *AddKOLUserReq) String() string { return proto.CompactTextString(m) }
func (*AddKOLUserReq) ProtoMessage()    {}
func (*AddKOLUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{33}
}
func (m *AddKOLUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddKOLUserReq.Unmarshal(m, b)
}
func (m *AddKOLUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddKOLUserReq.Marshal(b, m, deterministic)
}
func (dst *AddKOLUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddKOLUserReq.Merge(dst, src)
}
func (m *AddKOLUserReq) XXX_Size() int {
	return xxx_messageInfo_AddKOLUserReq.Size(m)
}
func (m *AddKOLUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddKOLUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddKOLUserReq proto.InternalMessageInfo

type AddKOLUserRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddKOLUserRsp) Reset()         { *m = AddKOLUserRsp{} }
func (m *AddKOLUserRsp) String() string { return proto.CompactTextString(m) }
func (*AddKOLUserRsp) ProtoMessage()    {}
func (*AddKOLUserRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{34}
}
func (m *AddKOLUserRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddKOLUserRsp.Unmarshal(m, b)
}
func (m *AddKOLUserRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddKOLUserRsp.Marshal(b, m, deterministic)
}
func (dst *AddKOLUserRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddKOLUserRsp.Merge(dst, src)
}
func (m *AddKOLUserRsp) XXX_Size() int {
	return xxx_messageInfo_AddKOLUserRsp.Size(m)
}
func (m *AddKOLUserRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddKOLUserRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AddKOLUserRsp proto.InternalMessageInfo

type BatchTGLExistedReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchTGLExistedReq) Reset()         { *m = BatchTGLExistedReq{} }
func (m *BatchTGLExistedReq) String() string { return proto.CompactTextString(m) }
func (*BatchTGLExistedReq) ProtoMessage()    {}
func (*BatchTGLExistedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{35}
}
func (m *BatchTGLExistedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchTGLExistedReq.Unmarshal(m, b)
}
func (m *BatchTGLExistedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchTGLExistedReq.Marshal(b, m, deterministic)
}
func (dst *BatchTGLExistedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchTGLExistedReq.Merge(dst, src)
}
func (m *BatchTGLExistedReq) XXX_Size() int {
	return xxx_messageInfo_BatchTGLExistedReq.Size(m)
}
func (m *BatchTGLExistedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchTGLExistedReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchTGLExistedReq proto.InternalMessageInfo

func (m *BatchTGLExistedReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchTGLExistedResp struct {
	IsExistedList        []*KOLExistedMapInfo `protobuf:"bytes,1,rep,name=is_existed_list,json=isExistedList,proto3" json:"is_existed_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatchTGLExistedResp) Reset()         { *m = BatchTGLExistedResp{} }
func (m *BatchTGLExistedResp) String() string { return proto.CompactTextString(m) }
func (*BatchTGLExistedResp) ProtoMessage()    {}
func (*BatchTGLExistedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{36}
}
func (m *BatchTGLExistedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchTGLExistedResp.Unmarshal(m, b)
}
func (m *BatchTGLExistedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchTGLExistedResp.Marshal(b, m, deterministic)
}
func (dst *BatchTGLExistedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchTGLExistedResp.Merge(dst, src)
}
func (m *BatchTGLExistedResp) XXX_Size() int {
	return xxx_messageInfo_BatchTGLExistedResp.Size(m)
}
func (m *BatchTGLExistedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchTGLExistedResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchTGLExistedResp proto.InternalMessageInfo

func (m *BatchTGLExistedResp) GetIsExistedList() []*KOLExistedMapInfo {
	if m != nil {
		return m.IsExistedList
	}
	return nil
}

type UpdatePostTagsReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Tags                 []uint32 `protobuf:"varint,2,rep,packed,name=tags,proto3" json:"tags,omitempty"`
	UserId               uint32   `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PostType             PostType `protobuf:"varint,4,opt,name=post_type,json=postType,proto3,enum=highcontent.PostType" json:"post_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePostTagsReq) Reset()         { *m = UpdatePostTagsReq{} }
func (m *UpdatePostTagsReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePostTagsReq) ProtoMessage()    {}
func (*UpdatePostTagsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{37}
}
func (m *UpdatePostTagsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePostTagsReq.Unmarshal(m, b)
}
func (m *UpdatePostTagsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePostTagsReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePostTagsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePostTagsReq.Merge(dst, src)
}
func (m *UpdatePostTagsReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePostTagsReq.Size(m)
}
func (m *UpdatePostTagsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePostTagsReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePostTagsReq proto.InternalMessageInfo

func (m *UpdatePostTagsReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpdatePostTagsReq) GetTags() []uint32 {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *UpdatePostTagsReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *UpdatePostTagsReq) GetPostType() PostType {
	if m != nil {
		return m.PostType
	}
	return PostType_Kol
}

type UpdatePostTagsRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePostTagsRsp) Reset()         { *m = UpdatePostTagsRsp{} }
func (m *UpdatePostTagsRsp) String() string { return proto.CompactTextString(m) }
func (*UpdatePostTagsRsp) ProtoMessage()    {}
func (*UpdatePostTagsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{38}
}
func (m *UpdatePostTagsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePostTagsRsp.Unmarshal(m, b)
}
func (m *UpdatePostTagsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePostTagsRsp.Marshal(b, m, deterministic)
}
func (dst *UpdatePostTagsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePostTagsRsp.Merge(dst, src)
}
func (m *UpdatePostTagsRsp) XXX_Size() int {
	return xxx_messageInfo_UpdatePostTagsRsp.Size(m)
}
func (m *UpdatePostTagsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePostTagsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePostTagsRsp proto.InternalMessageInfo

// force insert
type ForceInsertPostInfo struct {
	Index                uint32   `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	BeginTs              uint32   `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ForceInsertPostInfo) Reset()         { *m = ForceInsertPostInfo{} }
func (m *ForceInsertPostInfo) String() string { return proto.CompactTextString(m) }
func (*ForceInsertPostInfo) ProtoMessage()    {}
func (*ForceInsertPostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{39}
}
func (m *ForceInsertPostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ForceInsertPostInfo.Unmarshal(m, b)
}
func (m *ForceInsertPostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ForceInsertPostInfo.Marshal(b, m, deterministic)
}
func (dst *ForceInsertPostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ForceInsertPostInfo.Merge(dst, src)
}
func (m *ForceInsertPostInfo) XXX_Size() int {
	return xxx_messageInfo_ForceInsertPostInfo.Size(m)
}
func (m *ForceInsertPostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ForceInsertPostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ForceInsertPostInfo proto.InternalMessageInfo

func (m *ForceInsertPostInfo) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *ForceInsertPostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *ForceInsertPostInfo) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *ForceInsertPostInfo) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type ValidForceInsertPostInfo struct {
	Index                uint32   `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ValidForceInsertPostInfo) Reset()         { *m = ValidForceInsertPostInfo{} }
func (m *ValidForceInsertPostInfo) String() string { return proto.CompactTextString(m) }
func (*ValidForceInsertPostInfo) ProtoMessage()    {}
func (*ValidForceInsertPostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{40}
}
func (m *ValidForceInsertPostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ValidForceInsertPostInfo.Unmarshal(m, b)
}
func (m *ValidForceInsertPostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ValidForceInsertPostInfo.Marshal(b, m, deterministic)
}
func (dst *ValidForceInsertPostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidForceInsertPostInfo.Merge(dst, src)
}
func (m *ValidForceInsertPostInfo) XXX_Size() int {
	return xxx_messageInfo_ValidForceInsertPostInfo.Size(m)
}
func (m *ValidForceInsertPostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidForceInsertPostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ValidForceInsertPostInfo proto.InternalMessageInfo

func (m *ValidForceInsertPostInfo) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *ValidForceInsertPostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type SetForceInsertPostReq struct {
	Info                 *ForceInsertPostInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SetForceInsertPostReq) Reset()         { *m = SetForceInsertPostReq{} }
func (m *SetForceInsertPostReq) String() string { return proto.CompactTextString(m) }
func (*SetForceInsertPostReq) ProtoMessage()    {}
func (*SetForceInsertPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{41}
}
func (m *SetForceInsertPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetForceInsertPostReq.Unmarshal(m, b)
}
func (m *SetForceInsertPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetForceInsertPostReq.Marshal(b, m, deterministic)
}
func (dst *SetForceInsertPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetForceInsertPostReq.Merge(dst, src)
}
func (m *SetForceInsertPostReq) XXX_Size() int {
	return xxx_messageInfo_SetForceInsertPostReq.Size(m)
}
func (m *SetForceInsertPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetForceInsertPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetForceInsertPostReq proto.InternalMessageInfo

func (m *SetForceInsertPostReq) GetInfo() *ForceInsertPostInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SetForceInsertSendMsg2TrueReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetForceInsertSendMsg2TrueReq) Reset()         { *m = SetForceInsertSendMsg2TrueReq{} }
func (m *SetForceInsertSendMsg2TrueReq) String() string { return proto.CompactTextString(m) }
func (*SetForceInsertSendMsg2TrueReq) ProtoMessage()    {}
func (*SetForceInsertSendMsg2TrueReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{42}
}
func (m *SetForceInsertSendMsg2TrueReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetForceInsertSendMsg2TrueReq.Unmarshal(m, b)
}
func (m *SetForceInsertSendMsg2TrueReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetForceInsertSendMsg2TrueReq.Marshal(b, m, deterministic)
}
func (dst *SetForceInsertSendMsg2TrueReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetForceInsertSendMsg2TrueReq.Merge(dst, src)
}
func (m *SetForceInsertSendMsg2TrueReq) XXX_Size() int {
	return xxx_messageInfo_SetForceInsertSendMsg2TrueReq.Size(m)
}
func (m *SetForceInsertSendMsg2TrueReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetForceInsertSendMsg2TrueReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetForceInsertSendMsg2TrueReq proto.InternalMessageInfo

func (m *SetForceInsertSendMsg2TrueReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type SetForceInsertPostResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetForceInsertPostResp) Reset()         { *m = SetForceInsertPostResp{} }
func (m *SetForceInsertPostResp) String() string { return proto.CompactTextString(m) }
func (*SetForceInsertPostResp) ProtoMessage()    {}
func (*SetForceInsertPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{43}
}
func (m *SetForceInsertPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetForceInsertPostResp.Unmarshal(m, b)
}
func (m *SetForceInsertPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetForceInsertPostResp.Marshal(b, m, deterministic)
}
func (dst *SetForceInsertPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetForceInsertPostResp.Merge(dst, src)
}
func (m *SetForceInsertPostResp) XXX_Size() int {
	return xxx_messageInfo_SetForceInsertPostResp.Size(m)
}
func (m *SetForceInsertPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetForceInsertPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetForceInsertPostResp proto.InternalMessageInfo

type DelForceInsertPostReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelForceInsertPostReq) Reset()         { *m = DelForceInsertPostReq{} }
func (m *DelForceInsertPostReq) String() string { return proto.CompactTextString(m) }
func (*DelForceInsertPostReq) ProtoMessage()    {}
func (*DelForceInsertPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{44}
}
func (m *DelForceInsertPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelForceInsertPostReq.Unmarshal(m, b)
}
func (m *DelForceInsertPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelForceInsertPostReq.Marshal(b, m, deterministic)
}
func (dst *DelForceInsertPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelForceInsertPostReq.Merge(dst, src)
}
func (m *DelForceInsertPostReq) XXX_Size() int {
	return xxx_messageInfo_DelForceInsertPostReq.Size(m)
}
func (m *DelForceInsertPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelForceInsertPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelForceInsertPostReq proto.InternalMessageInfo

func (m *DelForceInsertPostReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type DelForceInsertPostResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelForceInsertPostResp) Reset()         { *m = DelForceInsertPostResp{} }
func (m *DelForceInsertPostResp) String() string { return proto.CompactTextString(m) }
func (*DelForceInsertPostResp) ProtoMessage()    {}
func (*DelForceInsertPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{45}
}
func (m *DelForceInsertPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelForceInsertPostResp.Unmarshal(m, b)
}
func (m *DelForceInsertPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelForceInsertPostResp.Marshal(b, m, deterministic)
}
func (dst *DelForceInsertPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelForceInsertPostResp.Merge(dst, src)
}
func (m *DelForceInsertPostResp) XXX_Size() int {
	return xxx_messageInfo_DelForceInsertPostResp.Size(m)
}
func (m *DelForceInsertPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelForceInsertPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelForceInsertPostResp proto.InternalMessageInfo

type GetForceInsertPostReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetForceInsertPostReq) Reset()         { *m = GetForceInsertPostReq{} }
func (m *GetForceInsertPostReq) String() string { return proto.CompactTextString(m) }
func (*GetForceInsertPostReq) ProtoMessage()    {}
func (*GetForceInsertPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{46}
}
func (m *GetForceInsertPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetForceInsertPostReq.Unmarshal(m, b)
}
func (m *GetForceInsertPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetForceInsertPostReq.Marshal(b, m, deterministic)
}
func (dst *GetForceInsertPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetForceInsertPostReq.Merge(dst, src)
}
func (m *GetForceInsertPostReq) XXX_Size() int {
	return xxx_messageInfo_GetForceInsertPostReq.Size(m)
}
func (m *GetForceInsertPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetForceInsertPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetForceInsertPostReq proto.InternalMessageInfo

func (m *GetForceInsertPostReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetForceInsertPostReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetForceInsertPostForSendReq struct {
	BeginTime            int64    `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetForceInsertPostForSendReq) Reset()         { *m = GetForceInsertPostForSendReq{} }
func (m *GetForceInsertPostForSendReq) String() string { return proto.CompactTextString(m) }
func (*GetForceInsertPostForSendReq) ProtoMessage()    {}
func (*GetForceInsertPostForSendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{47}
}
func (m *GetForceInsertPostForSendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetForceInsertPostForSendReq.Unmarshal(m, b)
}
func (m *GetForceInsertPostForSendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetForceInsertPostForSendReq.Marshal(b, m, deterministic)
}
func (dst *GetForceInsertPostForSendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetForceInsertPostForSendReq.Merge(dst, src)
}
func (m *GetForceInsertPostForSendReq) XXX_Size() int {
	return xxx_messageInfo_GetForceInsertPostForSendReq.Size(m)
}
func (m *GetForceInsertPostForSendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetForceInsertPostForSendReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetForceInsertPostForSendReq proto.InternalMessageInfo

func (m *GetForceInsertPostForSendReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetForceInsertPostForSendReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetForceInsertPostResp struct {
	InfoList             []*ForceInsertPostInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	Cnt                  uint32                 `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetForceInsertPostResp) Reset()         { *m = GetForceInsertPostResp{} }
func (m *GetForceInsertPostResp) String() string { return proto.CompactTextString(m) }
func (*GetForceInsertPostResp) ProtoMessage()    {}
func (*GetForceInsertPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{48}
}
func (m *GetForceInsertPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetForceInsertPostResp.Unmarshal(m, b)
}
func (m *GetForceInsertPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetForceInsertPostResp.Marshal(b, m, deterministic)
}
func (dst *GetForceInsertPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetForceInsertPostResp.Merge(dst, src)
}
func (m *GetForceInsertPostResp) XXX_Size() int {
	return xxx_messageInfo_GetForceInsertPostResp.Size(m)
}
func (m *GetForceInsertPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetForceInsertPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetForceInsertPostResp proto.InternalMessageInfo

func (m *GetForceInsertPostResp) GetInfoList() []*ForceInsertPostInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetForceInsertPostResp) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type GetValidForceInsertPostReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetValidForceInsertPostReq) Reset()         { *m = GetValidForceInsertPostReq{} }
func (m *GetValidForceInsertPostReq) String() string { return proto.CompactTextString(m) }
func (*GetValidForceInsertPostReq) ProtoMessage()    {}
func (*GetValidForceInsertPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{49}
}
func (m *GetValidForceInsertPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetValidForceInsertPostReq.Unmarshal(m, b)
}
func (m *GetValidForceInsertPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetValidForceInsertPostReq.Marshal(b, m, deterministic)
}
func (dst *GetValidForceInsertPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetValidForceInsertPostReq.Merge(dst, src)
}
func (m *GetValidForceInsertPostReq) XXX_Size() int {
	return xxx_messageInfo_GetValidForceInsertPostReq.Size(m)
}
func (m *GetValidForceInsertPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetValidForceInsertPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetValidForceInsertPostReq proto.InternalMessageInfo

func (m *GetValidForceInsertPostReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetValidForceInsertPostReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetValidForceInsertPostResp struct {
	InfoList             []*ValidForceInsertPostInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	Cnt                  uint32                      `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetValidForceInsertPostResp) Reset()         { *m = GetValidForceInsertPostResp{} }
func (m *GetValidForceInsertPostResp) String() string { return proto.CompactTextString(m) }
func (*GetValidForceInsertPostResp) ProtoMessage()    {}
func (*GetValidForceInsertPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{50}
}
func (m *GetValidForceInsertPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetValidForceInsertPostResp.Unmarshal(m, b)
}
func (m *GetValidForceInsertPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetValidForceInsertPostResp.Marshal(b, m, deterministic)
}
func (dst *GetValidForceInsertPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetValidForceInsertPostResp.Merge(dst, src)
}
func (m *GetValidForceInsertPostResp) XXX_Size() int {
	return xxx_messageInfo_GetValidForceInsertPostResp.Size(m)
}
func (m *GetValidForceInsertPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetValidForceInsertPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetValidForceInsertPostResp proto.InternalMessageInfo

func (m *GetValidForceInsertPostResp) GetInfoList() []*ValidForceInsertPostInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetValidForceInsertPostResp) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type PostExistedReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostExistedReq) Reset()         { *m = PostExistedReq{} }
func (m *PostExistedReq) String() string { return proto.CompactTextString(m) }
func (*PostExistedReq) ProtoMessage()    {}
func (*PostExistedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{51}
}
func (m *PostExistedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostExistedReq.Unmarshal(m, b)
}
func (m *PostExistedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostExistedReq.Marshal(b, m, deterministic)
}
func (dst *PostExistedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostExistedReq.Merge(dst, src)
}
func (m *PostExistedReq) XXX_Size() int {
	return xxx_messageInfo_PostExistedReq.Size(m)
}
func (m *PostExistedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PostExistedReq.DiscardUnknown(m)
}

var xxx_messageInfo_PostExistedReq proto.InternalMessageInfo

func (m *PostExistedReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type PostExistedResp struct {
	IsExisted            bool     `protobuf:"varint,1,opt,name=is_existed,json=isExisted,proto3" json:"is_existed,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostExistedResp) Reset()         { *m = PostExistedResp{} }
func (m *PostExistedResp) String() string { return proto.CompactTextString(m) }
func (*PostExistedResp) ProtoMessage()    {}
func (*PostExistedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{52}
}
func (m *PostExistedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostExistedResp.Unmarshal(m, b)
}
func (m *PostExistedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostExistedResp.Marshal(b, m, deterministic)
}
func (dst *PostExistedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostExistedResp.Merge(dst, src)
}
func (m *PostExistedResp) XXX_Size() int {
	return xxx_messageInfo_PostExistedResp.Size(m)
}
func (m *PostExistedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PostExistedResp.DiscardUnknown(m)
}

var xxx_messageInfo_PostExistedResp proto.InternalMessageInfo

func (m *PostExistedResp) GetIsExisted() bool {
	if m != nil {
		return m.IsExisted
	}
	return false
}

type GetTglUidReq struct {
	TglUid               string   `protobuf:"bytes,1,opt,name=tgl_uid,json=tglUid,proto3" json:"tgl_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTglUidReq) Reset()         { *m = GetTglUidReq{} }
func (m *GetTglUidReq) String() string { return proto.CompactTextString(m) }
func (*GetTglUidReq) ProtoMessage()    {}
func (*GetTglUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{53}
}
func (m *GetTglUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTglUidReq.Unmarshal(m, b)
}
func (m *GetTglUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTglUidReq.Marshal(b, m, deterministic)
}
func (dst *GetTglUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTglUidReq.Merge(dst, src)
}
func (m *GetTglUidReq) XXX_Size() int {
	return xxx_messageInfo_GetTglUidReq.Size(m)
}
func (m *GetTglUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTglUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTglUidReq proto.InternalMessageInfo

func (m *GetTglUidReq) GetTglUid() string {
	if m != nil {
		return m.TglUid
	}
	return ""
}

type GetTglUidRsp struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Exist                bool     `protobuf:"varint,3,opt,name=exist,proto3" json:"exist,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTglUidRsp) Reset()         { *m = GetTglUidRsp{} }
func (m *GetTglUidRsp) String() string { return proto.CompactTextString(m) }
func (*GetTglUidRsp) ProtoMessage()    {}
func (*GetTglUidRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{54}
}
func (m *GetTglUidRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTglUidRsp.Unmarshal(m, b)
}
func (m *GetTglUidRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTglUidRsp.Marshal(b, m, deterministic)
}
func (dst *GetTglUidRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTglUidRsp.Merge(dst, src)
}
func (m *GetTglUidRsp) XXX_Size() int {
	return xxx_messageInfo_GetTglUidRsp.Size(m)
}
func (m *GetTglUidRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTglUidRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTglUidRsp proto.InternalMessageInfo

func (m *GetTglUidRsp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetTglUidRsp) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GetTglUidRsp) GetExist() bool {
	if m != nil {
		return m.Exist
	}
	return false
}

type GetTglUidInfosReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTglUidInfosReq) Reset()         { *m = GetTglUidInfosReq{} }
func (m *GetTglUidInfosReq) String() string { return proto.CompactTextString(m) }
func (*GetTglUidInfosReq) ProtoMessage()    {}
func (*GetTglUidInfosReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{55}
}
func (m *GetTglUidInfosReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTglUidInfosReq.Unmarshal(m, b)
}
func (m *GetTglUidInfosReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTglUidInfosReq.Marshal(b, m, deterministic)
}
func (dst *GetTglUidInfosReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTglUidInfosReq.Merge(dst, src)
}
func (m *GetTglUidInfosReq) XXX_Size() int {
	return xxx_messageInfo_GetTglUidInfosReq.Size(m)
}
func (m *GetTglUidInfosReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTglUidInfosReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTglUidInfosReq proto.InternalMessageInfo

func (m *GetTglUidInfosReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetTglUidInfosReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type TglUidInfoT struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	TglUid               string   `protobuf:"bytes,3,opt,name=tgl_uid,json=tglUid,proto3" json:"tgl_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TglUidInfoT) Reset()         { *m = TglUidInfoT{} }
func (m *TglUidInfoT) String() string { return proto.CompactTextString(m) }
func (*TglUidInfoT) ProtoMessage()    {}
func (*TglUidInfoT) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{56}
}
func (m *TglUidInfoT) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TglUidInfoT.Unmarshal(m, b)
}
func (m *TglUidInfoT) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TglUidInfoT.Marshal(b, m, deterministic)
}
func (dst *TglUidInfoT) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TglUidInfoT.Merge(dst, src)
}
func (m *TglUidInfoT) XXX_Size() int {
	return xxx_messageInfo_TglUidInfoT.Size(m)
}
func (m *TglUidInfoT) XXX_DiscardUnknown() {
	xxx_messageInfo_TglUidInfoT.DiscardUnknown(m)
}

var xxx_messageInfo_TglUidInfoT proto.InternalMessageInfo

func (m *TglUidInfoT) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TglUidInfoT) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *TglUidInfoT) GetTglUid() string {
	if m != nil {
		return m.TglUid
	}
	return ""
}

type GetTglUidInfosRsp struct {
	TglUidInfo           []*TglUidInfoT `protobuf:"bytes,1,rep,name=tglUidInfo,proto3" json:"tglUidInfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetTglUidInfosRsp) Reset()         { *m = GetTglUidInfosRsp{} }
func (m *GetTglUidInfosRsp) String() string { return proto.CompactTextString(m) }
func (*GetTglUidInfosRsp) ProtoMessage()    {}
func (*GetTglUidInfosRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{57}
}
func (m *GetTglUidInfosRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTglUidInfosRsp.Unmarshal(m, b)
}
func (m *GetTglUidInfosRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTglUidInfosRsp.Marshal(b, m, deterministic)
}
func (dst *GetTglUidInfosRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTglUidInfosRsp.Merge(dst, src)
}
func (m *GetTglUidInfosRsp) XXX_Size() int {
	return xxx_messageInfo_GetTglUidInfosRsp.Size(m)
}
func (m *GetTglUidInfosRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTglUidInfosRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTglUidInfosRsp proto.InternalMessageInfo

func (m *GetTglUidInfosRsp) GetTglUidInfo() []*TglUidInfoT {
	if m != nil {
		return m.TglUidInfo
	}
	return nil
}

type AddTglUidReq struct {
	TglUid               string   `protobuf:"bytes,1,opt,name=tgl_uid,json=tglUid,proto3" json:"tgl_uid,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddTglUidReq) Reset()         { *m = AddTglUidReq{} }
func (m *AddTglUidReq) String() string { return proto.CompactTextString(m) }
func (*AddTglUidReq) ProtoMessage()    {}
func (*AddTglUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{58}
}
func (m *AddTglUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTglUidReq.Unmarshal(m, b)
}
func (m *AddTglUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTglUidReq.Marshal(b, m, deterministic)
}
func (dst *AddTglUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTglUidReq.Merge(dst, src)
}
func (m *AddTglUidReq) XXX_Size() int {
	return xxx_messageInfo_AddTglUidReq.Size(m)
}
func (m *AddTglUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTglUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddTglUidReq proto.InternalMessageInfo

func (m *AddTglUidReq) GetTglUid() string {
	if m != nil {
		return m.TglUid
	}
	return ""
}

func (m *AddTglUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddTglUidReq) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

type AddTglUidRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddTglUidRsp) Reset()         { *m = AddTglUidRsp{} }
func (m *AddTglUidRsp) String() string { return proto.CompactTextString(m) }
func (*AddTglUidRsp) ProtoMessage()    {}
func (*AddTglUidRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{59}
}
func (m *AddTglUidRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTglUidRsp.Unmarshal(m, b)
}
func (m *AddTglUidRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTglUidRsp.Marshal(b, m, deterministic)
}
func (dst *AddTglUidRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTglUidRsp.Merge(dst, src)
}
func (m *AddTglUidRsp) XXX_Size() int {
	return xxx_messageInfo_AddTglUidRsp.Size(m)
}
func (m *AddTglUidRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTglUidRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AddTglUidRsp proto.InternalMessageInfo

type GetPhoneIdReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPhoneIdReq) Reset()         { *m = GetPhoneIdReq{} }
func (m *GetPhoneIdReq) String() string { return proto.CompactTextString(m) }
func (*GetPhoneIdReq) ProtoMessage()    {}
func (*GetPhoneIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{60}
}
func (m *GetPhoneIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPhoneIdReq.Unmarshal(m, b)
}
func (m *GetPhoneIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPhoneIdReq.Marshal(b, m, deterministic)
}
func (dst *GetPhoneIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPhoneIdReq.Merge(dst, src)
}
func (m *GetPhoneIdReq) XXX_Size() int {
	return xxx_messageInfo_GetPhoneIdReq.Size(m)
}
func (m *GetPhoneIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPhoneIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPhoneIdReq proto.InternalMessageInfo

type GetPhoneIdRsp struct {
	Phoneid              string   `protobuf:"bytes,1,opt,name=phoneid,proto3" json:"phoneid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPhoneIdRsp) Reset()         { *m = GetPhoneIdRsp{} }
func (m *GetPhoneIdRsp) String() string { return proto.CompactTextString(m) }
func (*GetPhoneIdRsp) ProtoMessage()    {}
func (*GetPhoneIdRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{61}
}
func (m *GetPhoneIdRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPhoneIdRsp.Unmarshal(m, b)
}
func (m *GetPhoneIdRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPhoneIdRsp.Marshal(b, m, deterministic)
}
func (dst *GetPhoneIdRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPhoneIdRsp.Merge(dst, src)
}
func (m *GetPhoneIdRsp) XXX_Size() int {
	return xxx_messageInfo_GetPhoneIdRsp.Size(m)
}
func (m *GetPhoneIdRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPhoneIdRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPhoneIdRsp proto.InternalMessageInfo

func (m *GetPhoneIdRsp) GetPhoneid() string {
	if m != nil {
		return m.Phoneid
	}
	return ""
}

type GetConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetConfigReq) Reset()         { *m = GetConfigReq{} }
func (m *GetConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetConfigReq) ProtoMessage()    {}
func (*GetConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{62}
}
func (m *GetConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConfigReq.Unmarshal(m, b)
}
func (m *GetConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConfigReq.Merge(dst, src)
}
func (m *GetConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetConfigReq.Size(m)
}
func (m *GetConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConfigReq proto.InternalMessageInfo

type GetConfigRsp struct {
	Hide                 bool     `protobuf:"varint,1,opt,name=hide,proto3" json:"hide,omitempty"`
	RecommendType        uint32   `protobuf:"varint,2,opt,name=recommend_type,json=recommendType,proto3" json:"recommend_type,omitempty"`
	KolTabId             string   `protobuf:"bytes,3,opt,name=kolTabId,proto3" json:"kolTabId,omitempty"`
	NormalFoundTabId     string   `protobuf:"bytes,4,opt,name=normalFoundTabId,proto3" json:"normalFoundTabId,omitempty"`
	KolWhiteUids         []uint32 `protobuf:"varint,5,rep,packed,name=kol_white_uids,json=kolWhiteUids,proto3" json:"kol_white_uids,omitempty"`
	NormalWhiteUids      []uint32 `protobuf:"varint,6,rep,packed,name=normal_white_uids,json=normalWhiteUids,proto3" json:"normal_white_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetConfigRsp) Reset()         { *m = GetConfigRsp{} }
func (m *GetConfigRsp) String() string { return proto.CompactTextString(m) }
func (*GetConfigRsp) ProtoMessage()    {}
func (*GetConfigRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{63}
}
func (m *GetConfigRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConfigRsp.Unmarshal(m, b)
}
func (m *GetConfigRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConfigRsp.Marshal(b, m, deterministic)
}
func (dst *GetConfigRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConfigRsp.Merge(dst, src)
}
func (m *GetConfigRsp) XXX_Size() int {
	return xxx_messageInfo_GetConfigRsp.Size(m)
}
func (m *GetConfigRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConfigRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConfigRsp proto.InternalMessageInfo

func (m *GetConfigRsp) GetHide() bool {
	if m != nil {
		return m.Hide
	}
	return false
}

func (m *GetConfigRsp) GetRecommendType() uint32 {
	if m != nil {
		return m.RecommendType
	}
	return 0
}

func (m *GetConfigRsp) GetKolTabId() string {
	if m != nil {
		return m.KolTabId
	}
	return ""
}

func (m *GetConfigRsp) GetNormalFoundTabId() string {
	if m != nil {
		return m.NormalFoundTabId
	}
	return ""
}

func (m *GetConfigRsp) GetKolWhiteUids() []uint32 {
	if m != nil {
		return m.KolWhiteUids
	}
	return nil
}

func (m *GetConfigRsp) GetNormalWhiteUids() []uint32 {
	if m != nil {
		return m.NormalWhiteUids
	}
	return nil
}

// card
type GetRecommendCardReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendCardReq) Reset()         { *m = GetRecommendCardReq{} }
func (m *GetRecommendCardReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendCardReq) ProtoMessage()    {}
func (*GetRecommendCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{64}
}
func (m *GetRecommendCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendCardReq.Unmarshal(m, b)
}
func (m *GetRecommendCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendCardReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendCardReq.Merge(dst, src)
}
func (m *GetRecommendCardReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendCardReq.Size(m)
}
func (m *GetRecommendCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendCardReq proto.InternalMessageInfo

func (m *GetRecommendCardReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetRecommendCardReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetRecommendCardResp struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendCardResp) Reset()         { *m = GetRecommendCardResp{} }
func (m *GetRecommendCardResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendCardResp) ProtoMessage()    {}
func (*GetRecommendCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{65}
}
func (m *GetRecommendCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendCardResp.Unmarshal(m, b)
}
func (m *GetRecommendCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendCardResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendCardResp.Merge(dst, src)
}
func (m *GetRecommendCardResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendCardResp.Size(m)
}
func (m *GetRecommendCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendCardResp proto.InternalMessageInfo

func (m *GetRecommendCardResp) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type GetRecommendCardRandReq struct {
	Cnt                  uint32   `protobuf:"varint,1,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendCardRandReq) Reset()         { *m = GetRecommendCardRandReq{} }
func (m *GetRecommendCardRandReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendCardRandReq) ProtoMessage()    {}
func (*GetRecommendCardRandReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{66}
}
func (m *GetRecommendCardRandReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendCardRandReq.Unmarshal(m, b)
}
func (m *GetRecommendCardRandReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendCardRandReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendCardRandReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendCardRandReq.Merge(dst, src)
}
func (m *GetRecommendCardRandReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendCardRandReq.Size(m)
}
func (m *GetRecommendCardRandReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendCardRandReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendCardRandReq proto.InternalMessageInfo

func (m *GetRecommendCardRandReq) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type GetRecommendCardRandResp struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	Total                uint32   `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendCardRandResp) Reset()         { *m = GetRecommendCardRandResp{} }
func (m *GetRecommendCardRandResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendCardRandResp) ProtoMessage()    {}
func (*GetRecommendCardRandResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{67}
}
func (m *GetRecommendCardRandResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendCardRandResp.Unmarshal(m, b)
}
func (m *GetRecommendCardRandResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendCardRandResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendCardRandResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendCardRandResp.Merge(dst, src)
}
func (m *GetRecommendCardRandResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendCardRandResp.Size(m)
}
func (m *GetRecommendCardRandResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendCardRandResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendCardRandResp proto.InternalMessageInfo

func (m *GetRecommendCardRandResp) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *GetRecommendCardRandResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// aggregation
type GetRecommendAggregationReq struct {
	Category             uint32   `protobuf:"varint,1,opt,name=category,proto3" json:"category,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendAggregationReq) Reset()         { *m = GetRecommendAggregationReq{} }
func (m *GetRecommendAggregationReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendAggregationReq) ProtoMessage()    {}
func (*GetRecommendAggregationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{68}
}
func (m *GetRecommendAggregationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendAggregationReq.Unmarshal(m, b)
}
func (m *GetRecommendAggregationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendAggregationReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendAggregationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendAggregationReq.Merge(dst, src)
}
func (m *GetRecommendAggregationReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendAggregationReq.Size(m)
}
func (m *GetRecommendAggregationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendAggregationReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendAggregationReq proto.InternalMessageInfo

func (m *GetRecommendAggregationReq) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *GetRecommendAggregationReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetRecommendAggregationReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetRecommendAggregationResp struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendAggregationResp) Reset()         { *m = GetRecommendAggregationResp{} }
func (m *GetRecommendAggregationResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendAggregationResp) ProtoMessage()    {}
func (*GetRecommendAggregationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{69}
}
func (m *GetRecommendAggregationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendAggregationResp.Unmarshal(m, b)
}
func (m *GetRecommendAggregationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendAggregationResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendAggregationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendAggregationResp.Merge(dst, src)
}
func (m *GetRecommendAggregationResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendAggregationResp.Size(m)
}
func (m *GetRecommendAggregationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendAggregationResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendAggregationResp proto.InternalMessageInfo

func (m *GetRecommendAggregationResp) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type ExpertTagInfo struct {
	Category             uint32   `protobuf:"varint,1,opt,name=category,proto3" json:"category,omitempty"`
	TagName              string   `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExpertTagInfo) Reset()         { *m = ExpertTagInfo{} }
func (m *ExpertTagInfo) String() string { return proto.CompactTextString(m) }
func (*ExpertTagInfo) ProtoMessage()    {}
func (*ExpertTagInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{70}
}
func (m *ExpertTagInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpertTagInfo.Unmarshal(m, b)
}
func (m *ExpertTagInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpertTagInfo.Marshal(b, m, deterministic)
}
func (dst *ExpertTagInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpertTagInfo.Merge(dst, src)
}
func (m *ExpertTagInfo) XXX_Size() int {
	return xxx_messageInfo_ExpertTagInfo.Size(m)
}
func (m *ExpertTagInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpertTagInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ExpertTagInfo proto.InternalMessageInfo

func (m *ExpertTagInfo) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *ExpertTagInfo) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

// expert
type GetRecommendExpertReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendExpertReq) Reset()         { *m = GetRecommendExpertReq{} }
func (m *GetRecommendExpertReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendExpertReq) ProtoMessage()    {}
func (*GetRecommendExpertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{71}
}
func (m *GetRecommendExpertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendExpertReq.Unmarshal(m, b)
}
func (m *GetRecommendExpertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendExpertReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendExpertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendExpertReq.Merge(dst, src)
}
func (m *GetRecommendExpertReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendExpertReq.Size(m)
}
func (m *GetRecommendExpertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendExpertReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendExpertReq proto.InternalMessageInfo

type GetRecommendExpertResp struct {
	CategoryList         []*ExpertTagInfo `protobuf:"bytes,1,rep,name=category_list,json=categoryList,proto3" json:"category_list,omitempty"`
	Uids                 []uint32         `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetRecommendExpertResp) Reset()         { *m = GetRecommendExpertResp{} }
func (m *GetRecommendExpertResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendExpertResp) ProtoMessage()    {}
func (*GetRecommendExpertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{72}
}
func (m *GetRecommendExpertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendExpertResp.Unmarshal(m, b)
}
func (m *GetRecommendExpertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendExpertResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendExpertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendExpertResp.Merge(dst, src)
}
func (m *GetRecommendExpertResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendExpertResp.Size(m)
}
func (m *GetRecommendExpertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendExpertResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendExpertResp proto.InternalMessageInfo

func (m *GetRecommendExpertResp) GetCategoryList() []*ExpertTagInfo {
	if m != nil {
		return m.CategoryList
	}
	return nil
}

func (m *GetRecommendExpertResp) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

// recommend
type GetRecommendStreamsReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Tags                 []uint32 `protobuf:"varint,3,rep,packed,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendStreamsReq) Reset()         { *m = GetRecommendStreamsReq{} }
func (m *GetRecommendStreamsReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendStreamsReq) ProtoMessage()    {}
func (*GetRecommendStreamsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{73}
}
func (m *GetRecommendStreamsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendStreamsReq.Unmarshal(m, b)
}
func (m *GetRecommendStreamsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendStreamsReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendStreamsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendStreamsReq.Merge(dst, src)
}
func (m *GetRecommendStreamsReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendStreamsReq.Size(m)
}
func (m *GetRecommendStreamsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendStreamsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendStreamsReq proto.InternalMessageInfo

func (m *GetRecommendStreamsReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetRecommendStreamsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetRecommendStreamsReq) GetTags() []uint32 {
	if m != nil {
		return m.Tags
	}
	return nil
}

type GetRecommendStreamsRsp struct {
	Info                 []*StreamItem `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetRecommendStreamsRsp) Reset()         { *m = GetRecommendStreamsRsp{} }
func (m *GetRecommendStreamsRsp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendStreamsRsp) ProtoMessage()    {}
func (*GetRecommendStreamsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{74}
}
func (m *GetRecommendStreamsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendStreamsRsp.Unmarshal(m, b)
}
func (m *GetRecommendStreamsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendStreamsRsp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendStreamsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendStreamsRsp.Merge(dst, src)
}
func (m *GetRecommendStreamsRsp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendStreamsRsp.Size(m)
}
func (m *GetRecommendStreamsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendStreamsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendStreamsRsp proto.InternalMessageInfo

func (m *GetRecommendStreamsRsp) GetInfo() []*StreamItem {
	if m != nil {
		return m.Info
	}
	return nil
}

type StreamItemNotify struct {
	PostType             PostType    `protobuf:"varint,1,opt,name=post_type,json=postType,proto3,enum=highcontent.PostType" json:"post_type,omitempty"`
	Info                 *StreamItem `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *StreamItemNotify) Reset()         { *m = StreamItemNotify{} }
func (m *StreamItemNotify) String() string { return proto.CompactTextString(m) }
func (*StreamItemNotify) ProtoMessage()    {}
func (*StreamItemNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{75}
}
func (m *StreamItemNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StreamItemNotify.Unmarshal(m, b)
}
func (m *StreamItemNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StreamItemNotify.Marshal(b, m, deterministic)
}
func (dst *StreamItemNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StreamItemNotify.Merge(dst, src)
}
func (m *StreamItemNotify) XXX_Size() int {
	return xxx_messageInfo_StreamItemNotify.Size(m)
}
func (m *StreamItemNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_StreamItemNotify.DiscardUnknown(m)
}

var xxx_messageInfo_StreamItemNotify proto.InternalMessageInfo

func (m *StreamItemNotify) GetPostType() PostType {
	if m != nil {
		return m.PostType
	}
	return PostType_Kol
}

func (m *StreamItemNotify) GetInfo() *StreamItem {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetTglRobotListReq struct {
	// status 1为正常 2为黑名单
	Status               TglRobotStatusType `protobuf:"varint,1,opt,name=status,proto3,enum=highcontent.TglRobotStatusType" json:"status,omitempty"`
	Uid                  uint32             `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTime            int64              `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64              `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	PageNo               uint32             `protobuf:"varint,5,opt,name=page_no,json=pageNo,proto3" json:"page_no,omitempty"`
	PageSize             uint32             `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetTglRobotListReq) Reset()         { *m = GetTglRobotListReq{} }
func (m *GetTglRobotListReq) String() string { return proto.CompactTextString(m) }
func (*GetTglRobotListReq) ProtoMessage()    {}
func (*GetTglRobotListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{76}
}
func (m *GetTglRobotListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTglRobotListReq.Unmarshal(m, b)
}
func (m *GetTglRobotListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTglRobotListReq.Marshal(b, m, deterministic)
}
func (dst *GetTglRobotListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTglRobotListReq.Merge(dst, src)
}
func (m *GetTglRobotListReq) XXX_Size() int {
	return xxx_messageInfo_GetTglRobotListReq.Size(m)
}
func (m *GetTglRobotListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTglRobotListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTglRobotListReq proto.InternalMessageInfo

func (m *GetTglRobotListReq) GetStatus() TglRobotStatusType {
	if m != nil {
		return m.Status
	}
	return TglRobotStatusType_All
}

func (m *GetTglRobotListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetTglRobotListReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetTglRobotListReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetTglRobotListReq) GetPageNo() uint32 {
	if m != nil {
		return m.PageNo
	}
	return 0
}

func (m *GetTglRobotListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type TglRobotItem struct {
	Status TglRobotStatusType `protobuf:"varint,1,opt,name=status,proto3,enum=highcontent.TglRobotStatusType" json:"status,omitempty"`
	Uid    uint32             `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	// 昵称
	NickName string `protobuf:"bytes,5,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	// 创建时间
	CreateTime           int64    `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TglRobotItem) Reset()         { *m = TglRobotItem{} }
func (m *TglRobotItem) String() string { return proto.CompactTextString(m) }
func (*TglRobotItem) ProtoMessage()    {}
func (*TglRobotItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{77}
}
func (m *TglRobotItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TglRobotItem.Unmarshal(m, b)
}
func (m *TglRobotItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TglRobotItem.Marshal(b, m, deterministic)
}
func (dst *TglRobotItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TglRobotItem.Merge(dst, src)
}
func (m *TglRobotItem) XXX_Size() int {
	return xxx_messageInfo_TglRobotItem.Size(m)
}
func (m *TglRobotItem) XXX_DiscardUnknown() {
	xxx_messageInfo_TglRobotItem.DiscardUnknown(m)
}

var xxx_messageInfo_TglRobotItem proto.InternalMessageInfo

func (m *TglRobotItem) GetStatus() TglRobotStatusType {
	if m != nil {
		return m.Status
	}
	return TglRobotStatusType_All
}

func (m *TglRobotItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TglRobotItem) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *TglRobotItem) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type GetTglRobotListRsp struct {
	List                 []*TglRobotItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32          `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	PageNo               uint32          `protobuf:"varint,3,opt,name=page_no,json=pageNo,proto3" json:"page_no,omitempty"`
	PageSize             uint32          `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetTglRobotListRsp) Reset()         { *m = GetTglRobotListRsp{} }
func (m *GetTglRobotListRsp) String() string { return proto.CompactTextString(m) }
func (*GetTglRobotListRsp) ProtoMessage()    {}
func (*GetTglRobotListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{78}
}
func (m *GetTglRobotListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTglRobotListRsp.Unmarshal(m, b)
}
func (m *GetTglRobotListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTglRobotListRsp.Marshal(b, m, deterministic)
}
func (dst *GetTglRobotListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTglRobotListRsp.Merge(dst, src)
}
func (m *GetTglRobotListRsp) XXX_Size() int {
	return xxx_messageInfo_GetTglRobotListRsp.Size(m)
}
func (m *GetTglRobotListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTglRobotListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTglRobotListRsp proto.InternalMessageInfo

func (m *GetTglRobotListRsp) GetList() []*TglRobotItem {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetTglRobotListRsp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetTglRobotListRsp) GetPageNo() uint32 {
	if m != nil {
		return m.PageNo
	}
	return 0
}

func (m *GetTglRobotListRsp) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type UpdateTglRobotReq struct {
	// status 1为正常 2为黑名单
	Status               TglRobotStatusType `protobuf:"varint,1,opt,name=status,proto3,enum=highcontent.TglRobotStatusType" json:"status,omitempty"`
	Uid                  uint32             `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UpdateTglRobotReq) Reset()         { *m = UpdateTglRobotReq{} }
func (m *UpdateTglRobotReq) String() string { return proto.CompactTextString(m) }
func (*UpdateTglRobotReq) ProtoMessage()    {}
func (*UpdateTglRobotReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{79}
}
func (m *UpdateTglRobotReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTglRobotReq.Unmarshal(m, b)
}
func (m *UpdateTglRobotReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTglRobotReq.Marshal(b, m, deterministic)
}
func (dst *UpdateTglRobotReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTglRobotReq.Merge(dst, src)
}
func (m *UpdateTglRobotReq) XXX_Size() int {
	return xxx_messageInfo_UpdateTglRobotReq.Size(m)
}
func (m *UpdateTglRobotReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTglRobotReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTglRobotReq proto.InternalMessageInfo

func (m *UpdateTglRobotReq) GetStatus() TglRobotStatusType {
	if m != nil {
		return m.Status
	}
	return TglRobotStatusType_All
}

func (m *UpdateTglRobotReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type UpdateTglRobotRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTglRobotRsp) Reset()         { *m = UpdateTglRobotRsp{} }
func (m *UpdateTglRobotRsp) String() string { return proto.CompactTextString(m) }
func (*UpdateTglRobotRsp) ProtoMessage()    {}
func (*UpdateTglRobotRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_highcontent_352ca2e2e4179993, []int{80}
}
func (m *UpdateTglRobotRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTglRobotRsp.Unmarshal(m, b)
}
func (m *UpdateTglRobotRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTglRobotRsp.Marshal(b, m, deterministic)
}
func (dst *UpdateTglRobotRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTglRobotRsp.Merge(dst, src)
}
func (m *UpdateTglRobotRsp) XXX_Size() int {
	return xxx_messageInfo_UpdateTglRobotRsp.Size(m)
}
func (m *UpdateTglRobotRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTglRobotRsp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTglRobotRsp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*StreamItem)(nil), "highcontent.StreamItem")
	proto.RegisterType((*AddStreamItemReq)(nil), "highcontent.AddStreamItemReq")
	proto.RegisterType((*AddStreamItemRsp)(nil), "highcontent.AddStreamItemRsp")
	proto.RegisterType((*UpdateStreamRecommendReq)(nil), "highcontent.UpdateStreamRecommendReq")
	proto.RegisterType((*UpdateStreamRecommendRsp)(nil), "highcontent.UpdateStreamRecommendRsp")
	proto.RegisterType((*UpdateStreamStatusReq)(nil), "highcontent.UpdateStreamStatusReq")
	proto.RegisterType((*UpdateStreamStatusRsp)(nil), "highcontent.UpdateStreamStatusRsp")
	proto.RegisterType((*DeleteStreamItemReq)(nil), "highcontent.DeleteStreamItemReq")
	proto.RegisterType((*DeleteStreamItemRsp)(nil), "highcontent.DeleteStreamItemRsp")
	proto.RegisterType((*GetStreamItemReq)(nil), "highcontent.GetStreamItemReq")
	proto.RegisterType((*GetStreamItemRsp)(nil), "highcontent.GetStreamItemRsp")
	proto.RegisterType((*GetStreamItemByUidReq)(nil), "highcontent.GetStreamItemByUidReq")
	proto.RegisterType((*GetStreamItemByUidRsp)(nil), "highcontent.GetStreamItemByUidRsp")
	proto.RegisterType((*GetStreamItemByPostidsReq)(nil), "highcontent.GetStreamItemByPostidsReq")
	proto.RegisterType((*GetStreamItemByPostidsRsp)(nil), "highcontent.GetStreamItemByPostidsRsp")
	proto.RegisterType((*GetStreamsReq)(nil), "highcontent.GetStreamsReq")
	proto.RegisterType((*GetStreamsResp)(nil), "highcontent.GetStreamsResp")
	proto.RegisterType((*GetSecondLevelStreamItemReq)(nil), "highcontent.GetSecondLevelStreamItemReq")
	proto.RegisterType((*GetSecondLevelStreamItemRsp)(nil), "highcontent.GetSecondLevelStreamItemRsp")
	proto.RegisterType((*GetPostUpdateExistReq)(nil), "highcontent.GetPostUpdateExistReq")
	proto.RegisterType((*GetPostUpdateExistRsp)(nil), "highcontent.GetPostUpdateExistRsp")
	proto.RegisterType((*KOLUserInfo)(nil), "highcontent.KOLUserInfo")
	proto.RegisterType((*DelKOLUsersReq)(nil), "highcontent.DelKOLUsersReq")
	proto.RegisterType((*DelKOLUsersResp)(nil), "highcontent.DelKOLUsersResp")
	proto.RegisterType((*GetKOLUsersReq)(nil), "highcontent.GetKOLUsersReq")
	proto.RegisterType((*GetKOLUsersResp)(nil), "highcontent.GetKOLUsersResp")
	proto.RegisterType((*KOLExistedReq)(nil), "highcontent.KOLExistedReq")
	proto.RegisterType((*KOLExistedResp)(nil), "highcontent.KOLExistedResp")
	proto.RegisterType((*KOLExistedMapInfo)(nil), "highcontent.KOLExistedMapInfo")
	proto.RegisterType((*BatchKOLExistedReq)(nil), "highcontent.BatchKOLExistedReq")
	proto.RegisterType((*BatchKOLExistedResp)(nil), "highcontent.BatchKOLExistedResp")
	proto.RegisterType((*UpdateKOLUsersInfoReq)(nil), "highcontent.UpdateKOLUsersInfoReq")
	proto.RegisterType((*UpdateKOLUsersInfoResp)(nil), "highcontent.UpdateKOLUsersInfoResp")
	proto.RegisterType((*AddKOLUserReq)(nil), "highcontent.AddKOLUserReq")
	proto.RegisterType((*AddKOLUserRsp)(nil), "highcontent.AddKOLUserRsp")
	proto.RegisterType((*BatchTGLExistedReq)(nil), "highcontent.BatchTGLExistedReq")
	proto.RegisterType((*BatchTGLExistedResp)(nil), "highcontent.BatchTGLExistedResp")
	proto.RegisterType((*UpdatePostTagsReq)(nil), "highcontent.UpdatePostTagsReq")
	proto.RegisterType((*UpdatePostTagsRsp)(nil), "highcontent.UpdatePostTagsRsp")
	proto.RegisterType((*ForceInsertPostInfo)(nil), "highcontent.ForceInsertPostInfo")
	proto.RegisterType((*ValidForceInsertPostInfo)(nil), "highcontent.ValidForceInsertPostInfo")
	proto.RegisterType((*SetForceInsertPostReq)(nil), "highcontent.SetForceInsertPostReq")
	proto.RegisterType((*SetForceInsertSendMsg2TrueReq)(nil), "highcontent.SetForceInsertSendMsg2TrueReq")
	proto.RegisterType((*SetForceInsertPostResp)(nil), "highcontent.SetForceInsertPostResp")
	proto.RegisterType((*DelForceInsertPostReq)(nil), "highcontent.DelForceInsertPostReq")
	proto.RegisterType((*DelForceInsertPostResp)(nil), "highcontent.DelForceInsertPostResp")
	proto.RegisterType((*GetForceInsertPostReq)(nil), "highcontent.GetForceInsertPostReq")
	proto.RegisterType((*GetForceInsertPostForSendReq)(nil), "highcontent.GetForceInsertPostForSendReq")
	proto.RegisterType((*GetForceInsertPostResp)(nil), "highcontent.GetForceInsertPostResp")
	proto.RegisterType((*GetValidForceInsertPostReq)(nil), "highcontent.GetValidForceInsertPostReq")
	proto.RegisterType((*GetValidForceInsertPostResp)(nil), "highcontent.GetValidForceInsertPostResp")
	proto.RegisterType((*PostExistedReq)(nil), "highcontent.PostExistedReq")
	proto.RegisterType((*PostExistedResp)(nil), "highcontent.PostExistedResp")
	proto.RegisterType((*GetTglUidReq)(nil), "highcontent.GetTglUidReq")
	proto.RegisterType((*GetTglUidRsp)(nil), "highcontent.GetTglUidRsp")
	proto.RegisterType((*GetTglUidInfosReq)(nil), "highcontent.GetTglUidInfosReq")
	proto.RegisterType((*TglUidInfoT)(nil), "highcontent.TglUidInfoT")
	proto.RegisterType((*GetTglUidInfosRsp)(nil), "highcontent.GetTglUidInfosRsp")
	proto.RegisterType((*AddTglUidReq)(nil), "highcontent.AddTglUidReq")
	proto.RegisterType((*AddTglUidRsp)(nil), "highcontent.AddTglUidRsp")
	proto.RegisterType((*GetPhoneIdReq)(nil), "highcontent.GetPhoneIdReq")
	proto.RegisterType((*GetPhoneIdRsp)(nil), "highcontent.GetPhoneIdRsp")
	proto.RegisterType((*GetConfigReq)(nil), "highcontent.GetConfigReq")
	proto.RegisterType((*GetConfigRsp)(nil), "highcontent.GetConfigRsp")
	proto.RegisterType((*GetRecommendCardReq)(nil), "highcontent.GetRecommendCardReq")
	proto.RegisterType((*GetRecommendCardResp)(nil), "highcontent.GetRecommendCardResp")
	proto.RegisterType((*GetRecommendCardRandReq)(nil), "highcontent.GetRecommendCardRandReq")
	proto.RegisterType((*GetRecommendCardRandResp)(nil), "highcontent.GetRecommendCardRandResp")
	proto.RegisterType((*GetRecommendAggregationReq)(nil), "highcontent.GetRecommendAggregationReq")
	proto.RegisterType((*GetRecommendAggregationResp)(nil), "highcontent.GetRecommendAggregationResp")
	proto.RegisterType((*ExpertTagInfo)(nil), "highcontent.ExpertTagInfo")
	proto.RegisterType((*GetRecommendExpertReq)(nil), "highcontent.GetRecommendExpertReq")
	proto.RegisterType((*GetRecommendExpertResp)(nil), "highcontent.GetRecommendExpertResp")
	proto.RegisterType((*GetRecommendStreamsReq)(nil), "highcontent.GetRecommendStreamsReq")
	proto.RegisterType((*GetRecommendStreamsRsp)(nil), "highcontent.GetRecommendStreamsRsp")
	proto.RegisterType((*StreamItemNotify)(nil), "highcontent.StreamItemNotify")
	proto.RegisterType((*GetTglRobotListReq)(nil), "highcontent.GetTglRobotListReq")
	proto.RegisterType((*TglRobotItem)(nil), "highcontent.TglRobotItem")
	proto.RegisterType((*GetTglRobotListRsp)(nil), "highcontent.GetTglRobotListRsp")
	proto.RegisterType((*UpdateTglRobotReq)(nil), "highcontent.UpdateTglRobotReq")
	proto.RegisterType((*UpdateTglRobotRsp)(nil), "highcontent.UpdateTglRobotRsp")
	proto.RegisterEnum("highcontent.PostType", PostType_name, PostType_value)
	proto.RegisterEnum("highcontent.GetStreamState", GetStreamState_name, GetStreamState_value)
	proto.RegisterEnum("highcontent.RecommendType", RecommendType_name, RecommendType_value)
	proto.RegisterEnum("highcontent.KOLLevel", KOLLevel_name, KOLLevel_value)
	proto.RegisterEnum("highcontent.TglRobotStatusType", TglRobotStatusType_name, TglRobotStatusType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// HighContentClient is the client API for HighContent service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type HighContentClient interface {
	// get other system with StreamItem Object
	AddStreamItem(ctx context.Context, in *AddStreamItemReq, opts ...grpc.CallOption) (*AddStreamItemRsp, error)
	DeleteStreamItem(ctx context.Context, in *DeleteStreamItemReq, opts ...grpc.CallOption) (*DeleteStreamItemRsp, error)
	UpdateStreamRecommend(ctx context.Context, in *UpdateStreamRecommendReq, opts ...grpc.CallOption) (*UpdateStreamRecommendRsp, error)
	UpdatePostTags(ctx context.Context, in *UpdatePostTagsReq, opts ...grpc.CallOption) (*UpdatePostTagsRsp, error)
	UpdateStreamStatus(ctx context.Context, in *UpdateStreamStatusReq, opts ...grpc.CallOption) (*UpdateStreamStatusRsp, error)
	GetStreamItems(ctx context.Context, in *GetStreamItemReq, opts ...grpc.CallOption) (*GetStreamItemRsp, error)
	GetStreamItemsByUid(ctx context.Context, in *GetStreamItemByUidReq, opts ...grpc.CallOption) (*GetStreamItemByUidRsp, error)
	GetStreamItemByPostids(ctx context.Context, in *GetStreamItemByPostidsReq, opts ...grpc.CallOption) (*GetStreamItemByPostidsRsp, error)
	GetPostUpdateExist(ctx context.Context, in *GetPostUpdateExistReq, opts ...grpc.CallOption) (*GetPostUpdateExistRsp, error)
	GetSecondLevelStreamItem(ctx context.Context, in *GetSecondLevelStreamItemReq, opts ...grpc.CallOption) (*GetSecondLevelStreamItemRsp, error)
	GetStreams(ctx context.Context, in *GetStreamsReq, opts ...grpc.CallOption) (*GetStreamsResp, error)
	GetStreamsByPostID(ctx context.Context, in *GetStreamItemReq, opts ...grpc.CallOption) (*GetStreamItemRsp, error)
	// the impl of  KOL user
	KOLExisted(ctx context.Context, in *KOLExistedReq, opts ...grpc.CallOption) (*KOLExistedResp, error)
	GetKOLUsers(ctx context.Context, in *GetKOLUsersReq, opts ...grpc.CallOption) (*GetKOLUsersResp, error)
	DelKOLUsers(ctx context.Context, in *DelKOLUsersReq, opts ...grpc.CallOption) (*DelKOLUsersResp, error)
	BatchKOLExisted(ctx context.Context, in *BatchKOLExistedReq, opts ...grpc.CallOption) (*BatchKOLExistedResp, error)
	UpdateKOLUsersInfo(ctx context.Context, in *UpdateKOLUsersInfoReq, opts ...grpc.CallOption) (*UpdateKOLUsersInfoResp, error)
	BatchTGLExisted(ctx context.Context, in *BatchTGLExistedReq, opts ...grpc.CallOption) (*BatchTGLExistedResp, error)
	//
	SetForceInsertPost(ctx context.Context, in *SetForceInsertPostReq, opts ...grpc.CallOption) (*SetForceInsertPostResp, error)
	DelForceInsertPost(ctx context.Context, in *DelForceInsertPostReq, opts ...grpc.CallOption) (*DelForceInsertPostResp, error)
	GetForceInsertPost(ctx context.Context, in *GetForceInsertPostReq, opts ...grpc.CallOption) (*GetForceInsertPostResp, error)
	GetValidForceInsertPost(ctx context.Context, in *GetValidForceInsertPostReq, opts ...grpc.CallOption) (*GetValidForceInsertPostResp, error)
	GetForceInsertPostForSend(ctx context.Context, in *GetForceInsertPostForSendReq, opts ...grpc.CallOption) (*GetForceInsertPostResp, error)
	SetForceInsertSendMsg2True(ctx context.Context, in *SetForceInsertSendMsg2TrueReq, opts ...grpc.CallOption) (*SetForceInsertPostResp, error)
	// //the impl about TGL
	GetTglUid(ctx context.Context, in *GetTglUidReq, opts ...grpc.CallOption) (*GetTglUidRsp, error)
	AddTglUid(ctx context.Context, in *AddTglUidReq, opts ...grpc.CallOption) (*AddTglUidRsp, error)
	GetPhoneId(ctx context.Context, in *GetPhoneIdReq, opts ...grpc.CallOption) (*GetPhoneIdRsp, error)
	GetTglUidInfos(ctx context.Context, in *GetTglUidInfosReq, opts ...grpc.CallOption) (*GetTglUidInfosRsp, error)
	// the card info
	GetRecommendCard(ctx context.Context, in *GetRecommendCardReq, opts ...grpc.CallOption) (*GetRecommendCardResp, error)
	GetRecommendCardRand(ctx context.Context, in *GetRecommendCardRandReq, opts ...grpc.CallOption) (*GetRecommendCardRandResp, error)
	GetRecommendAggregation(ctx context.Context, in *GetRecommendAggregationReq, opts ...grpc.CallOption) (*GetRecommendAggregationResp, error)
	GetRecommendExpert(ctx context.Context, in *GetRecommendExpertReq, opts ...grpc.CallOption) (*GetRecommendExpertResp, error)
	// config set
	GetConfig(ctx context.Context, in *GetConfigReq, opts ...grpc.CallOption) (*GetConfigRsp, error)
	// //tgl 精选流TGL机器人管理
	GetTglRobotList(ctx context.Context, in *GetTglRobotListReq, opts ...grpc.CallOption) (*GetTglRobotListRsp, error)
	UpdateTglRobotStatus(ctx context.Context, in *UpdateTglRobotReq, opts ...grpc.CallOption) (*UpdateTglRobotRsp, error)
}

type highContentClient struct {
	cc *grpc.ClientConn
}

func NewHighContentClient(cc *grpc.ClientConn) HighContentClient {
	return &highContentClient{cc}
}

func (c *highContentClient) AddStreamItem(ctx context.Context, in *AddStreamItemReq, opts ...grpc.CallOption) (*AddStreamItemRsp, error) {
	out := new(AddStreamItemRsp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/AddStreamItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) DeleteStreamItem(ctx context.Context, in *DeleteStreamItemReq, opts ...grpc.CallOption) (*DeleteStreamItemRsp, error) {
	out := new(DeleteStreamItemRsp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/DeleteStreamItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) UpdateStreamRecommend(ctx context.Context, in *UpdateStreamRecommendReq, opts ...grpc.CallOption) (*UpdateStreamRecommendRsp, error) {
	out := new(UpdateStreamRecommendRsp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/UpdateStreamRecommend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) UpdatePostTags(ctx context.Context, in *UpdatePostTagsReq, opts ...grpc.CallOption) (*UpdatePostTagsRsp, error) {
	out := new(UpdatePostTagsRsp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/UpdatePostTags", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) UpdateStreamStatus(ctx context.Context, in *UpdateStreamStatusReq, opts ...grpc.CallOption) (*UpdateStreamStatusRsp, error) {
	out := new(UpdateStreamStatusRsp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/UpdateStreamStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetStreamItems(ctx context.Context, in *GetStreamItemReq, opts ...grpc.CallOption) (*GetStreamItemRsp, error) {
	out := new(GetStreamItemRsp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetStreamItems", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetStreamItemsByUid(ctx context.Context, in *GetStreamItemByUidReq, opts ...grpc.CallOption) (*GetStreamItemByUidRsp, error) {
	out := new(GetStreamItemByUidRsp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetStreamItemsByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetStreamItemByPostids(ctx context.Context, in *GetStreamItemByPostidsReq, opts ...grpc.CallOption) (*GetStreamItemByPostidsRsp, error) {
	out := new(GetStreamItemByPostidsRsp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetStreamItemByPostids", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetPostUpdateExist(ctx context.Context, in *GetPostUpdateExistReq, opts ...grpc.CallOption) (*GetPostUpdateExistRsp, error) {
	out := new(GetPostUpdateExistRsp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetPostUpdateExist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetSecondLevelStreamItem(ctx context.Context, in *GetSecondLevelStreamItemReq, opts ...grpc.CallOption) (*GetSecondLevelStreamItemRsp, error) {
	out := new(GetSecondLevelStreamItemRsp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetSecondLevelStreamItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetStreams(ctx context.Context, in *GetStreamsReq, opts ...grpc.CallOption) (*GetStreamsResp, error) {
	out := new(GetStreamsResp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetStreams", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetStreamsByPostID(ctx context.Context, in *GetStreamItemReq, opts ...grpc.CallOption) (*GetStreamItemRsp, error) {
	out := new(GetStreamItemRsp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetStreamsByPostID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) KOLExisted(ctx context.Context, in *KOLExistedReq, opts ...grpc.CallOption) (*KOLExistedResp, error) {
	out := new(KOLExistedResp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/KOLExisted", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetKOLUsers(ctx context.Context, in *GetKOLUsersReq, opts ...grpc.CallOption) (*GetKOLUsersResp, error) {
	out := new(GetKOLUsersResp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetKOLUsers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) DelKOLUsers(ctx context.Context, in *DelKOLUsersReq, opts ...grpc.CallOption) (*DelKOLUsersResp, error) {
	out := new(DelKOLUsersResp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/DelKOLUsers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) BatchKOLExisted(ctx context.Context, in *BatchKOLExistedReq, opts ...grpc.CallOption) (*BatchKOLExistedResp, error) {
	out := new(BatchKOLExistedResp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/BatchKOLExisted", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) UpdateKOLUsersInfo(ctx context.Context, in *UpdateKOLUsersInfoReq, opts ...grpc.CallOption) (*UpdateKOLUsersInfoResp, error) {
	out := new(UpdateKOLUsersInfoResp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/UpdateKOLUsersInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) BatchTGLExisted(ctx context.Context, in *BatchTGLExistedReq, opts ...grpc.CallOption) (*BatchTGLExistedResp, error) {
	out := new(BatchTGLExistedResp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/BatchTGLExisted", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) SetForceInsertPost(ctx context.Context, in *SetForceInsertPostReq, opts ...grpc.CallOption) (*SetForceInsertPostResp, error) {
	out := new(SetForceInsertPostResp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/SetForceInsertPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) DelForceInsertPost(ctx context.Context, in *DelForceInsertPostReq, opts ...grpc.CallOption) (*DelForceInsertPostResp, error) {
	out := new(DelForceInsertPostResp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/DelForceInsertPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetForceInsertPost(ctx context.Context, in *GetForceInsertPostReq, opts ...grpc.CallOption) (*GetForceInsertPostResp, error) {
	out := new(GetForceInsertPostResp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetForceInsertPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetValidForceInsertPost(ctx context.Context, in *GetValidForceInsertPostReq, opts ...grpc.CallOption) (*GetValidForceInsertPostResp, error) {
	out := new(GetValidForceInsertPostResp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetValidForceInsertPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetForceInsertPostForSend(ctx context.Context, in *GetForceInsertPostForSendReq, opts ...grpc.CallOption) (*GetForceInsertPostResp, error) {
	out := new(GetForceInsertPostResp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetForceInsertPostForSend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) SetForceInsertSendMsg2True(ctx context.Context, in *SetForceInsertSendMsg2TrueReq, opts ...grpc.CallOption) (*SetForceInsertPostResp, error) {
	out := new(SetForceInsertPostResp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/SetForceInsertSendMsg2True", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetTglUid(ctx context.Context, in *GetTglUidReq, opts ...grpc.CallOption) (*GetTglUidRsp, error) {
	out := new(GetTglUidRsp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetTglUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) AddTglUid(ctx context.Context, in *AddTglUidReq, opts ...grpc.CallOption) (*AddTglUidRsp, error) {
	out := new(AddTglUidRsp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/AddTglUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetPhoneId(ctx context.Context, in *GetPhoneIdReq, opts ...grpc.CallOption) (*GetPhoneIdRsp, error) {
	out := new(GetPhoneIdRsp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetPhoneId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetTglUidInfos(ctx context.Context, in *GetTglUidInfosReq, opts ...grpc.CallOption) (*GetTglUidInfosRsp, error) {
	out := new(GetTglUidInfosRsp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetTglUidInfos", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetRecommendCard(ctx context.Context, in *GetRecommendCardReq, opts ...grpc.CallOption) (*GetRecommendCardResp, error) {
	out := new(GetRecommendCardResp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetRecommendCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetRecommendCardRand(ctx context.Context, in *GetRecommendCardRandReq, opts ...grpc.CallOption) (*GetRecommendCardRandResp, error) {
	out := new(GetRecommendCardRandResp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetRecommendCardRand", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetRecommendAggregation(ctx context.Context, in *GetRecommendAggregationReq, opts ...grpc.CallOption) (*GetRecommendAggregationResp, error) {
	out := new(GetRecommendAggregationResp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetRecommendAggregation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetRecommendExpert(ctx context.Context, in *GetRecommendExpertReq, opts ...grpc.CallOption) (*GetRecommendExpertResp, error) {
	out := new(GetRecommendExpertResp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetRecommendExpert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetConfig(ctx context.Context, in *GetConfigReq, opts ...grpc.CallOption) (*GetConfigRsp, error) {
	out := new(GetConfigRsp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) GetTglRobotList(ctx context.Context, in *GetTglRobotListReq, opts ...grpc.CallOption) (*GetTglRobotListRsp, error) {
	out := new(GetTglRobotListRsp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/GetTglRobotList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *highContentClient) UpdateTglRobotStatus(ctx context.Context, in *UpdateTglRobotReq, opts ...grpc.CallOption) (*UpdateTglRobotRsp, error) {
	out := new(UpdateTglRobotRsp)
	err := c.cc.Invoke(ctx, "/highcontent.HighContent/UpdateTglRobotStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HighContentServer is the server API for HighContent service.
type HighContentServer interface {
	// get other system with StreamItem Object
	AddStreamItem(context.Context, *AddStreamItemReq) (*AddStreamItemRsp, error)
	DeleteStreamItem(context.Context, *DeleteStreamItemReq) (*DeleteStreamItemRsp, error)
	UpdateStreamRecommend(context.Context, *UpdateStreamRecommendReq) (*UpdateStreamRecommendRsp, error)
	UpdatePostTags(context.Context, *UpdatePostTagsReq) (*UpdatePostTagsRsp, error)
	UpdateStreamStatus(context.Context, *UpdateStreamStatusReq) (*UpdateStreamStatusRsp, error)
	GetStreamItems(context.Context, *GetStreamItemReq) (*GetStreamItemRsp, error)
	GetStreamItemsByUid(context.Context, *GetStreamItemByUidReq) (*GetStreamItemByUidRsp, error)
	GetStreamItemByPostids(context.Context, *GetStreamItemByPostidsReq) (*GetStreamItemByPostidsRsp, error)
	GetPostUpdateExist(context.Context, *GetPostUpdateExistReq) (*GetPostUpdateExistRsp, error)
	GetSecondLevelStreamItem(context.Context, *GetSecondLevelStreamItemReq) (*GetSecondLevelStreamItemRsp, error)
	GetStreams(context.Context, *GetStreamsReq) (*GetStreamsResp, error)
	GetStreamsByPostID(context.Context, *GetStreamItemReq) (*GetStreamItemRsp, error)
	// the impl of  KOL user
	KOLExisted(context.Context, *KOLExistedReq) (*KOLExistedResp, error)
	GetKOLUsers(context.Context, *GetKOLUsersReq) (*GetKOLUsersResp, error)
	DelKOLUsers(context.Context, *DelKOLUsersReq) (*DelKOLUsersResp, error)
	BatchKOLExisted(context.Context, *BatchKOLExistedReq) (*BatchKOLExistedResp, error)
	UpdateKOLUsersInfo(context.Context, *UpdateKOLUsersInfoReq) (*UpdateKOLUsersInfoResp, error)
	BatchTGLExisted(context.Context, *BatchTGLExistedReq) (*BatchTGLExistedResp, error)
	//
	SetForceInsertPost(context.Context, *SetForceInsertPostReq) (*SetForceInsertPostResp, error)
	DelForceInsertPost(context.Context, *DelForceInsertPostReq) (*DelForceInsertPostResp, error)
	GetForceInsertPost(context.Context, *GetForceInsertPostReq) (*GetForceInsertPostResp, error)
	GetValidForceInsertPost(context.Context, *GetValidForceInsertPostReq) (*GetValidForceInsertPostResp, error)
	GetForceInsertPostForSend(context.Context, *GetForceInsertPostForSendReq) (*GetForceInsertPostResp, error)
	SetForceInsertSendMsg2True(context.Context, *SetForceInsertSendMsg2TrueReq) (*SetForceInsertPostResp, error)
	// //the impl about TGL
	GetTglUid(context.Context, *GetTglUidReq) (*GetTglUidRsp, error)
	AddTglUid(context.Context, *AddTglUidReq) (*AddTglUidRsp, error)
	GetPhoneId(context.Context, *GetPhoneIdReq) (*GetPhoneIdRsp, error)
	GetTglUidInfos(context.Context, *GetTglUidInfosReq) (*GetTglUidInfosRsp, error)
	// the card info
	GetRecommendCard(context.Context, *GetRecommendCardReq) (*GetRecommendCardResp, error)
	GetRecommendCardRand(context.Context, *GetRecommendCardRandReq) (*GetRecommendCardRandResp, error)
	GetRecommendAggregation(context.Context, *GetRecommendAggregationReq) (*GetRecommendAggregationResp, error)
	GetRecommendExpert(context.Context, *GetRecommendExpertReq) (*GetRecommendExpertResp, error)
	// config set
	GetConfig(context.Context, *GetConfigReq) (*GetConfigRsp, error)
	// //tgl 精选流TGL机器人管理
	GetTglRobotList(context.Context, *GetTglRobotListReq) (*GetTglRobotListRsp, error)
	UpdateTglRobotStatus(context.Context, *UpdateTglRobotReq) (*UpdateTglRobotRsp, error)
}

func RegisterHighContentServer(s *grpc.Server, srv HighContentServer) {
	s.RegisterService(&_HighContent_serviceDesc, srv)
}

func _HighContent_AddStreamItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddStreamItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).AddStreamItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/AddStreamItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).AddStreamItem(ctx, req.(*AddStreamItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_DeleteStreamItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteStreamItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).DeleteStreamItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/DeleteStreamItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).DeleteStreamItem(ctx, req.(*DeleteStreamItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_UpdateStreamRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStreamRecommendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).UpdateStreamRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/UpdateStreamRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).UpdateStreamRecommend(ctx, req.(*UpdateStreamRecommendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_UpdatePostTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePostTagsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).UpdatePostTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/UpdatePostTags",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).UpdatePostTags(ctx, req.(*UpdatePostTagsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_UpdateStreamStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStreamStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).UpdateStreamStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/UpdateStreamStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).UpdateStreamStatus(ctx, req.(*UpdateStreamStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetStreamItems_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStreamItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetStreamItems(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetStreamItems",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetStreamItems(ctx, req.(*GetStreamItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetStreamItemsByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStreamItemByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetStreamItemsByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetStreamItemsByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetStreamItemsByUid(ctx, req.(*GetStreamItemByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetStreamItemByPostids_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStreamItemByPostidsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetStreamItemByPostids(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetStreamItemByPostids",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetStreamItemByPostids(ctx, req.(*GetStreamItemByPostidsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetPostUpdateExist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostUpdateExistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetPostUpdateExist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetPostUpdateExist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetPostUpdateExist(ctx, req.(*GetPostUpdateExistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetSecondLevelStreamItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSecondLevelStreamItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetSecondLevelStreamItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetSecondLevelStreamItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetSecondLevelStreamItem(ctx, req.(*GetSecondLevelStreamItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetStreams_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStreamsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetStreams(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetStreams",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetStreams(ctx, req.(*GetStreamsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetStreamsByPostID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStreamItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetStreamsByPostID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetStreamsByPostID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetStreamsByPostID(ctx, req.(*GetStreamItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_KOLExisted_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KOLExistedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).KOLExisted(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/KOLExisted",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).KOLExisted(ctx, req.(*KOLExistedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetKOLUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKOLUsersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetKOLUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetKOLUsers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetKOLUsers(ctx, req.(*GetKOLUsersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_DelKOLUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelKOLUsersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).DelKOLUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/DelKOLUsers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).DelKOLUsers(ctx, req.(*DelKOLUsersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_BatchKOLExisted_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchKOLExistedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).BatchKOLExisted(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/BatchKOLExisted",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).BatchKOLExisted(ctx, req.(*BatchKOLExistedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_UpdateKOLUsersInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateKOLUsersInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).UpdateKOLUsersInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/UpdateKOLUsersInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).UpdateKOLUsersInfo(ctx, req.(*UpdateKOLUsersInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_BatchTGLExisted_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchTGLExistedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).BatchTGLExisted(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/BatchTGLExisted",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).BatchTGLExisted(ctx, req.(*BatchTGLExistedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_SetForceInsertPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetForceInsertPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).SetForceInsertPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/SetForceInsertPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).SetForceInsertPost(ctx, req.(*SetForceInsertPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_DelForceInsertPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelForceInsertPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).DelForceInsertPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/DelForceInsertPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).DelForceInsertPost(ctx, req.(*DelForceInsertPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetForceInsertPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetForceInsertPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetForceInsertPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetForceInsertPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetForceInsertPost(ctx, req.(*GetForceInsertPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetValidForceInsertPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetValidForceInsertPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetValidForceInsertPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetValidForceInsertPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetValidForceInsertPost(ctx, req.(*GetValidForceInsertPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetForceInsertPostForSend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetForceInsertPostForSendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetForceInsertPostForSend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetForceInsertPostForSend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetForceInsertPostForSend(ctx, req.(*GetForceInsertPostForSendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_SetForceInsertSendMsg2True_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetForceInsertSendMsg2TrueReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).SetForceInsertSendMsg2True(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/SetForceInsertSendMsg2True",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).SetForceInsertSendMsg2True(ctx, req.(*SetForceInsertSendMsg2TrueReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetTglUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTglUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetTglUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetTglUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetTglUid(ctx, req.(*GetTglUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_AddTglUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTglUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).AddTglUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/AddTglUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).AddTglUid(ctx, req.(*AddTglUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetPhoneId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPhoneIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetPhoneId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetPhoneId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetPhoneId(ctx, req.(*GetPhoneIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetTglUidInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTglUidInfosReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetTglUidInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetTglUidInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetTglUidInfos(ctx, req.(*GetTglUidInfosReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetRecommendCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetRecommendCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetRecommendCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetRecommendCard(ctx, req.(*GetRecommendCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetRecommendCardRand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendCardRandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetRecommendCardRand(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetRecommendCardRand",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetRecommendCardRand(ctx, req.(*GetRecommendCardRandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetRecommendAggregation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendAggregationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetRecommendAggregation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetRecommendAggregation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetRecommendAggregation(ctx, req.(*GetRecommendAggregationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetRecommendExpert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendExpertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetRecommendExpert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetRecommendExpert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetRecommendExpert(ctx, req.(*GetRecommendExpertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetConfig(ctx, req.(*GetConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_GetTglRobotList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTglRobotListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).GetTglRobotList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/GetTglRobotList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).GetTglRobotList(ctx, req.(*GetTglRobotListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HighContent_UpdateTglRobotStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTglRobotReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HighContentServer).UpdateTglRobotStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/highcontent.HighContent/UpdateTglRobotStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HighContentServer).UpdateTglRobotStatus(ctx, req.(*UpdateTglRobotReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _HighContent_serviceDesc = grpc.ServiceDesc{
	ServiceName: "highcontent.HighContent",
	HandlerType: (*HighContentServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddStreamItem",
			Handler:    _HighContent_AddStreamItem_Handler,
		},
		{
			MethodName: "DeleteStreamItem",
			Handler:    _HighContent_DeleteStreamItem_Handler,
		},
		{
			MethodName: "UpdateStreamRecommend",
			Handler:    _HighContent_UpdateStreamRecommend_Handler,
		},
		{
			MethodName: "UpdatePostTags",
			Handler:    _HighContent_UpdatePostTags_Handler,
		},
		{
			MethodName: "UpdateStreamStatus",
			Handler:    _HighContent_UpdateStreamStatus_Handler,
		},
		{
			MethodName: "GetStreamItems",
			Handler:    _HighContent_GetStreamItems_Handler,
		},
		{
			MethodName: "GetStreamItemsByUid",
			Handler:    _HighContent_GetStreamItemsByUid_Handler,
		},
		{
			MethodName: "GetStreamItemByPostids",
			Handler:    _HighContent_GetStreamItemByPostids_Handler,
		},
		{
			MethodName: "GetPostUpdateExist",
			Handler:    _HighContent_GetPostUpdateExist_Handler,
		},
		{
			MethodName: "GetSecondLevelStreamItem",
			Handler:    _HighContent_GetSecondLevelStreamItem_Handler,
		},
		{
			MethodName: "GetStreams",
			Handler:    _HighContent_GetStreams_Handler,
		},
		{
			MethodName: "GetStreamsByPostID",
			Handler:    _HighContent_GetStreamsByPostID_Handler,
		},
		{
			MethodName: "KOLExisted",
			Handler:    _HighContent_KOLExisted_Handler,
		},
		{
			MethodName: "GetKOLUsers",
			Handler:    _HighContent_GetKOLUsers_Handler,
		},
		{
			MethodName: "DelKOLUsers",
			Handler:    _HighContent_DelKOLUsers_Handler,
		},
		{
			MethodName: "BatchKOLExisted",
			Handler:    _HighContent_BatchKOLExisted_Handler,
		},
		{
			MethodName: "UpdateKOLUsersInfo",
			Handler:    _HighContent_UpdateKOLUsersInfo_Handler,
		},
		{
			MethodName: "BatchTGLExisted",
			Handler:    _HighContent_BatchTGLExisted_Handler,
		},
		{
			MethodName: "SetForceInsertPost",
			Handler:    _HighContent_SetForceInsertPost_Handler,
		},
		{
			MethodName: "DelForceInsertPost",
			Handler:    _HighContent_DelForceInsertPost_Handler,
		},
		{
			MethodName: "GetForceInsertPost",
			Handler:    _HighContent_GetForceInsertPost_Handler,
		},
		{
			MethodName: "GetValidForceInsertPost",
			Handler:    _HighContent_GetValidForceInsertPost_Handler,
		},
		{
			MethodName: "GetForceInsertPostForSend",
			Handler:    _HighContent_GetForceInsertPostForSend_Handler,
		},
		{
			MethodName: "SetForceInsertSendMsg2True",
			Handler:    _HighContent_SetForceInsertSendMsg2True_Handler,
		},
		{
			MethodName: "GetTglUid",
			Handler:    _HighContent_GetTglUid_Handler,
		},
		{
			MethodName: "AddTglUid",
			Handler:    _HighContent_AddTglUid_Handler,
		},
		{
			MethodName: "GetPhoneId",
			Handler:    _HighContent_GetPhoneId_Handler,
		},
		{
			MethodName: "GetTglUidInfos",
			Handler:    _HighContent_GetTglUidInfos_Handler,
		},
		{
			MethodName: "GetRecommendCard",
			Handler:    _HighContent_GetRecommendCard_Handler,
		},
		{
			MethodName: "GetRecommendCardRand",
			Handler:    _HighContent_GetRecommendCardRand_Handler,
		},
		{
			MethodName: "GetRecommendAggregation",
			Handler:    _HighContent_GetRecommendAggregation_Handler,
		},
		{
			MethodName: "GetRecommendExpert",
			Handler:    _HighContent_GetRecommendExpert_Handler,
		},
		{
			MethodName: "GetConfig",
			Handler:    _HighContent_GetConfig_Handler,
		},
		{
			MethodName: "GetTglRobotList",
			Handler:    _HighContent_GetTglRobotList_Handler,
		},
		{
			MethodName: "UpdateTglRobotStatus",
			Handler:    _HighContent_UpdateTglRobotStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "highcontent/highcontent.proto",
}

func init() {
	proto.RegisterFile("highcontent/highcontent.proto", fileDescriptor_highcontent_352ca2e2e4179993)
}

var fileDescriptor_highcontent_352ca2e2e4179993 = []byte{
	// 2658 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x5a, 0x4b, 0x73, 0xdb, 0xc8,
	0x11, 0x16, 0x1f, 0xe2, 0xa3, 0x25, 0x52, 0xd4, 0x58, 0xb2, 0x28, 0xc8, 0xde, 0x95, 0x11, 0x6f,
	0x56, 0x96, 0xb3, 0xf6, 0x5a, 0x9b, 0x54, 0x7c, 0x49, 0x52, 0x7a, 0x50, 0xb4, 0x6c, 0x3d, 0x1c,
	0x88, 0xb2, 0x37, 0x9b, 0xda, 0x65, 0xc1, 0xc4, 0x08, 0x42, 0x4c, 0x01, 0x30, 0x07, 0x74, 0xec,
	0xcd, 0x35, 0x97, 0x1c, 0x52, 0x49, 0x55, 0x0e, 0xfb, 0x83, 0xf2, 0x03, 0xf2, 0x03, 0x72, 0xcc,
	0xaf, 0xc8, 0x2d, 0x35, 0x8d, 0x07, 0x07, 0xc0, 0x80, 0x14, 0x2d, 0x27, 0x95, 0x1b, 0xa6, 0xa7,
	0xa7, 0xbb, 0xa7, 0xa7, 0xa7, 0xa7, 0xe7, 0x1b, 0xc0, 0xed, 0x0b, 0xcb, 0xbc, 0xe8, 0x39, 0xb6,
	0x47, 0x6d, 0xef, 0xa1, 0xf0, 0xfd, 0xc0, 0x1d, 0x38, 0x9e, 0x43, 0xe6, 0x04, 0x92, 0xfa, 0xf7,
	0x3c, 0xc0, 0xa9, 0x37, 0xa0, 0xfa, 0xe5, 0x81, 0x47, 0x2f, 0xc9, 0x0a, 0x94, 0x5d, 0x87, 0x79,
	0x5d, 0xcb, 0x68, 0xe6, 0xd6, 0x73, 0x1b, 0x55, 0xad, 0xc4, 0x9b, 0x07, 0x06, 0x21, 0x50, 0xf4,
	0xac, 0x4b, 0xda, 0xcc, 0xaf, 0xe7, 0x36, 0x6a, 0x1a, 0x7e, 0x93, 0x5b, 0x50, 0x1d, 0xd0, 0x9e,
	0x73, 0x79, 0x49, 0x6d, 0xa3, 0x59, 0xc0, 0x8e, 0x11, 0x81, 0x34, 0xa0, 0x30, 0xb4, 0x8c, 0x66,
	0x11, 0xe9, 0xfc, 0x13, 0x65, 0xe8, 0x26, 0x6b, 0xce, 0xae, 0x17, 0x50, 0x86, 0x6e, 0x32, 0xb2,
	0x04, 0xb3, 0x7d, 0xfa, 0x96, 0xf6, 0x9b, 0x25, 0xe4, 0xf3, 0x1b, 0xe4, 0x26, 0x94, 0x98, 0xa7,
	0x7b, 0x43, 0xd6, 0x2c, 0x23, 0x39, 0x68, 0x91, 0x35, 0xa8, 0xf6, 0x9c, 0xb7, 0x74, 0xd0, 0x1d,
	0x0e, 0xfa, 0xcd, 0x0a, 0x1a, 0x58, 0x41, 0xc2, 0xd9, 0xa0, 0xcf, 0xc5, 0x33, 0xeb, 0x7b, 0xda,
	0xac, 0x22, 0x1d, 0xbf, 0xc9, 0x32, 0x94, 0x3c, 0xb3, 0xcf, 0xa7, 0x03, 0xbe, 0x7c, 0xcf, 0xec,
	0x1f, 0x18, 0x64, 0x03, 0x1a, 0x6f, 0xf5, 0xbe, 0x65, 0x74, 0x5f, 0x51, 0xd3, 0xb2, 0xbb, 0x38,
	0xb3, 0xb9, 0xf5, 0xdc, 0x46, 0x41, 0xab, 0x23, 0x7d, 0x87, 0x93, 0x3b, 0x7c, 0x8e, 0x77, 0xc1,
	0xa7, 0x74, 0xa9, 0x6d, 0xf8, 0x7c, 0xf3, 0xc8, 0x37, 0x8f, 0xd4, 0x96, 0x6d, 0x70, 0x2e, 0x95,
	0x41, 0x63, 0xdb, 0x30, 0x46, 0x7e, 0xd4, 0xe8, 0x1b, 0xb2, 0x05, 0x55, 0x74, 0xa5, 0xf7, 0xde,
	0xa5, 0xe8, 0xcc, 0xfa, 0xd6, 0xf2, 0x03, 0x71, 0x35, 0x9e, 0x3b, 0xcc, 0xeb, 0xbc, 0x77, 0xa9,
	0x56, 0x71, 0x83, 0x2f, 0x72, 0x1f, 0x8a, 0x96, 0x7d, 0xee, 0xa0, 0x97, 0xe7, 0xb6, 0x56, 0x62,
	0xec, 0x82, 0x74, 0x64, 0x52, 0x49, 0x52, 0x29, 0x73, 0xd5, 0xbf, 0xe6, 0xa1, 0x79, 0xe6, 0x1a,
	0xba, 0x47, 0x7d, 0xba, 0x16, 0x2e, 0xc7, 0x87, 0x5a, 0xd4, 0xf4, 0x03, 0xc2, 0x32, 0x58, 0x33,
	0xbf, 0x5e, 0xd8, 0xa8, 0x6a, 0x61, 0x73, 0xc2, 0xea, 0xdf, 0x06, 0x10, 0x7c, 0x5b, 0x44, 0x9f,
	0x55, 0x5f, 0x45, 0x6e, 0x5d, 0x85, 0x4a, 0xe4, 0xd0, 0x59, 0xec, 0x2c, 0x53, 0xdf, 0x97, 0xd2,
	0xb5, 0x29, 0x5d, 0x71, 0x6d, 0xca, 0x92, 0xb5, 0x51, 0xb2, 0x3c, 0xc2, 0x5c, 0xf5, 0x0f, 0xb0,
	0x2c, 0xf6, 0x9d, 0x62, 0x94, 0x7d, 0xa8, 0xab, 0x6e, 0x42, 0xc9, 0xf7, 0x0d, 0x2e, 0x5f, 0xb0,
	0x75, 0x2c, 0x43, 0x08, 0xe6, 0x82, 0x18, 0xcc, 0xea, 0x8a, 0x54, 0x39, 0x73, 0x55, 0x1d, 0x6e,
	0xec, 0xd1, 0x3e, 0x0d, 0x3b, 0xae, 0x13, 0x50, 0x19, 0x36, 0xa9, 0xcb, 0x12, 0x15, 0xcc, 0x55,
	0xff, 0x99, 0x83, 0x46, 0x9b, 0x7a, 0x1f, 0x45, 0xaf, 0x73, 0x7e, 0xce, 0xa8, 0x17, 0x24, 0x8c,
	0xa0, 0x85, 0xdb, 0xdd, 0xba, 0xb4, 0xbc, 0xc0, 0x15, 0x7e, 0xe3, 0x1a, 0xc1, 0x22, 0xe4, 0xab,
	0x52, 0x2a, 0x5f, 0xf1, 0x5c, 0x53, 0x1e, 0xe5, 0x1a, 0xf5, 0xd7, 0xc9, 0xc9, 0x31, 0x37, 0xda,
	0x71, 0xb9, 0xf5, 0xc2, 0xc4, 0x1d, 0xc7, 0x53, 0x5a, 0xcf, 0x0e, 0xa7, 0xc4, 0x3f, 0xd5, 0x1f,
	0x72, 0xb0, 0x1c, 0x93, 0xb9, 0xf3, 0xfe, 0xcc, 0xfa, 0xe0, 0xcd, 0x16, 0xf7, 0x43, 0x7e, 0x9c,
	0x1f, 0x0a, 0x71, 0x3f, 0xa4, 0x92, 0xad, 0xfa, 0x42, 0x6a, 0xd8, 0xf5, 0x67, 0x6c, 0xc1, 0x6a,
	0x42, 0xee, 0x73, 0x3f, 0x21, 0x7c, 0xf4, 0x0c, 0xa3, 0x3e, 0xc9, 0x54, 0x35, 0xe5, 0x34, 0xd4,
	0x3f, 0xe6, 0xa1, 0x16, 0x89, 0x62, 0xff, 0xef, 0x41, 0xbd, 0x04, 0xb3, 0x3c, 0x45, 0xd0, 0xf0,
	0x4c, 0xc4, 0x46, 0xb8, 0xc4, 0xe5, 0xd1, 0x79, 0xba, 0x06, 0xd5, 0xd7, 0x4e, 0xbf, 0xeb, 0x9f,
	0x9f, 0x15, 0xa4, 0x57, 0x5e, 0x3b, 0xfd, 0x43, 0x3c, 0x42, 0xc3, 0x0d, 0x50, 0x15, 0x36, 0xc0,
	0x09, 0xd4, 0x45, 0x2f, 0x5c, 0x3f, 0x18, 0xfe, 0x96, 0x83, 0x35, 0x2e, 0x91, 0xf6, 0x1c, 0xdb,
	0x40, 0xbd, 0xff, 0xeb, 0xd4, 0x11, 0x4e, 0xb3, 0x28, 0x4c, 0xf3, 0xe9, 0x18, 0xa3, 0xa6, 0x8d,
	0x9c, 0x2e, 0x6e, 0x23, 0x6e, 0xa6, 0x9f, 0xab, 0x5b, 0xef, 0x2c, 0xe6, 0x7d, 0xe8, 0xd4, 0x08,
	0x14, 0x87, 0x61, 0x9c, 0xd7, 0x34, 0xfc, 0x56, 0xf7, 0xa4, 0x0a, 0xa6, 0x35, 0xb3, 0x0d, 0x73,
	0xcf, 0x4e, 0x0e, 0xcf, 0x18, 0x1d, 0x1c, 0x04, 0x2b, 0x35, 0x0c, 0x4a, 0xb8, 0x20, 0x56, 0xa2,
	0x3a, 0x2b, 0x2f, 0xd6, 0x59, 0x0d, 0x28, 0x78, 0xba, 0x19, 0x78, 0x94, 0x7f, 0xaa, 0xf7, 0xa1,
	0xbe, 0x47, 0xfb, 0x81, 0x2c, 0xdc, 0x29, 0xab, 0x50, 0x19, 0x5a, 0x46, 0xb7, 0x6f, 0x31, 0x0f,
	0x6d, 0xa9, 0x69, 0xe5, 0xa1, 0x65, 0x1c, 0x5a, 0xcc, 0x53, 0x3b, 0xb0, 0x10, 0x63, 0x66, 0x2e,
	0xd9, 0x86, 0x79, 0x36, 0xec, 0xf5, 0x28, 0x63, 0xa3, 0x11, 0x73, 0x5b, 0x9f, 0xc4, 0xac, 0x7f,
	0x76, 0x72, 0x88, 0xb3, 0xa4, 0xc6, 0x91, 0xee, 0x72, 0x7b, 0xb5, 0xb9, 0x60, 0x0c, 0x4a, 0x3d,
	0xc7, 0x28, 0x15, 0x4d, 0xb8, 0xea, 0x74, 0x46, 0xa1, 0x53, 0x90, 0x87, 0x4e, 0x51, 0x08, 0x1d,
	0xb5, 0x07, 0x0b, 0x31, 0x3d, 0xcc, 0x25, 0xbf, 0x84, 0xfa, 0x90, 0xd1, 0x41, 0x97, 0xfb, 0x54,
	0xb4, 0xbf, 0x99, 0xb4, 0x3f, 0xf4, 0xb4, 0x36, 0x3f, 0x0c, 0xbe, 0xb8, 0xe9, 0x92, 0x1d, 0x72,
	0x07, 0x6a, 0xa3, 0xe9, 0x4a, 0xe7, 0xa2, 0x7e, 0x0b, 0x75, 0x91, 0x85, 0xb9, 0x3c, 0x75, 0x58,
	0xac, 0x4b, 0x7d, 0x0a, 0xb2, 0x56, 0xb4, 0xaa, 0xc5, 0x02, 0x16, 0xf2, 0x93, 0x58, 0x95, 0x98,
	0x6d, 0x9b, 0x1f, 0x1a, 0x7b, 0xb0, 0x98, 0x72, 0xb8, 0xc4, 0xa3, 0x71, 0x9d, 0xf9, 0x84, 0x4e,
	0xf5, 0x21, 0x90, 0x1d, 0xdd, 0xeb, 0x5d, 0xc4, 0x27, 0x33, 0x26, 0x36, 0xbe, 0x85, 0x1b, 0xa9,
	0x01, 0xcc, 0x25, 0xfb, 0xb0, 0x30, 0x52, 0x33, 0x4d, 0x88, 0xd4, 0x22, 0x5b, 0x50, 0xfc, 0xcb,
	0xb0, 0x78, 0x0a, 0xd7, 0x0f, 0x99, 0xe8, 0x9b, 0xeb, 0x2e, 0xa1, 0xfa, 0x5b, 0xb8, 0x29, 0x13,
	0xfc, 0x71, 0x42, 0x7b, 0x01, 0x6a, 0xdb, 0x86, 0x11, 0x48, 0xd6, 0xe8, 0x9b, 0x04, 0x81, 0xb9,
	0x91, 0x9f, 0x3b, 0xed, 0x29, 0xfd, 0x2c, 0x0e, 0xf8, 0x88, 0x7e, 0xfe, 0x73, 0x0e, 0x16, 0x7d,
	0x7f, 0x60, 0x3e, 0xd3, 0x4d, 0xdc, 0x90, 0x63, 0xaf, 0x89, 0x3c, 0x1d, 0xe7, 0x85, 0x2b, 0xde,
	0x0a, 0x94, 0xfd, 0x15, 0x09, 0xaf, 0x09, 0x25, 0x74, 0xb8, 0x11, 0x4f, 0xa1, 0xc5, 0x2b, 0xa5,
	0x50, 0xf5, 0x46, 0xca, 0x1c, 0xe6, 0xaa, 0x6f, 0xe1, 0xc6, 0xbe, 0x33, 0xe8, 0xd1, 0x03, 0x9b,
	0xd1, 0x01, 0xe6, 0x52, 0x0c, 0xf2, 0x25, 0x98, 0xb5, 0x6c, 0x83, 0xbe, 0x0b, 0xc2, 0xdc, 0x6f,
	0x88, 0xb6, 0xe7, 0x63, 0xb6, 0xaf, 0x42, 0x25, 0x38, 0xb0, 0xc3, 0x4a, 0xbd, 0xec, 0x1f, 0xd7,
	0x8c, 0x5f, 0x23, 0xf1, 0xb0, 0x66, 0x61, 0x06, 0xe1, 0x47, 0x35, 0x53, 0x0f, 0xa0, 0xf9, 0x82,
	0x5f, 0x35, 0xae, 0xaf, 0x5c, 0x3d, 0x82, 0xe5, 0x53, 0xea, 0x25, 0x04, 0x71, 0x57, 0xff, 0x34,
	0x3a, 0x06, 0xf8, 0x66, 0x5f, 0x8f, 0xf9, 0x47, 0xa2, 0x37, 0xd8, 0xf4, 0x8f, 0xe1, 0x76, 0x5c,
	0xdc, 0x29, 0xb5, 0x8d, 0x23, 0x66, 0x6e, 0x75, 0x06, 0x43, 0x3a, 0x6e, 0x05, 0xd5, 0x26, 0xdc,
	0x94, 0x19, 0xc2, 0x5c, 0xf5, 0x4b, 0x58, 0xde, 0xa3, 0x7d, 0x89, 0x89, 0xe3, 0x64, 0xc9, 0x46,
	0x30, 0x57, 0x6d, 0xe1, 0xa9, 0x27, 0x91, 0x35, 0x4a, 0xe1, 0x39, 0x79, 0x0a, 0xcf, 0x8b, 0x29,
	0xfc, 0x6b, 0xb8, 0x95, 0x16, 0xb3, 0xef, 0x0c, 0x4e, 0x83, 0x1b, 0x6f, 0xbc, 0x06, 0xcb, 0x8d,
	0xab, 0xc1, 0xf2, 0xb1, 0x1a, 0x4c, 0xb5, 0xe0, 0x66, 0x5b, 0xea, 0x06, 0xf2, 0x0b, 0xa8, 0x26,
	0x73, 0xcb, 0xe4, 0x55, 0xa9, 0x58, 0xd9, 0x47, 0xc4, 0x53, 0x50, 0xda, 0xd4, 0x93, 0x05, 0xd2,
	0xf4, 0x0e, 0x61, 0x58, 0xfa, 0xc8, 0x65, 0x31, 0x97, 0xec, 0xa4, 0x6d, 0xff, 0x2c, 0x66, 0x7b,
	0x56, 0x38, 0x8f, 0x9d, 0xc0, 0x3d, 0xa8, 0x73, 0x3e, 0x21, 0x5f, 0x65, 0x46, 0xc4, 0x97, 0xb0,
	0x10, 0x63, 0x9d, 0x78, 0xd8, 0xa9, 0x9f, 0xc3, 0x7c, 0x9b, 0x7a, 0x1d, 0xb3, 0x1f, 0xdc, 0xab,
	0x56, 0xa0, 0xec, 0x99, 0xfd, 0xee, 0x70, 0x24, 0xda, 0xc3, 0x3e, 0x55, 0x13, 0x19, 0x99, 0x2b,
	0x39, 0xe2, 0x14, 0xa8, 0xd8, 0x56, 0xef, 0xb5, 0xad, 0x07, 0xcb, 0x5d, 0xd5, 0xa2, 0x36, 0x77,
	0x27, 0x9a, 0x80, 0x3b, 0xbf, 0xa2, 0xf9, 0x0d, 0x75, 0x1b, 0x16, 0x23, 0x99, 0xdc, 0x0d, 0x6c,
	0xfa, 0x15, 0xe9, 0xc0, 0xdc, 0x68, 0x7c, 0x67, 0x4a, 0xab, 0x84, 0xc9, 0x16, 0x62, 0x93, 0x3d,
	0x4a, 0x19, 0xc6, 0x5c, 0xf2, 0x18, 0xc0, 0x8b, 0x28, 0xd2, 0x63, 0x4f, 0xb0, 0x44, 0x13, 0x78,
	0xd5, 0x33, 0x98, 0xdf, 0x36, 0x8c, 0xc9, 0x4e, 0x0e, 0xcd, 0xcf, 0xcb, 0xcd, 0x2f, 0xc4, 0xcd,
	0x57, 0xeb, 0xa2, 0x58, 0xe6, 0xf2, 0xd3, 0x8e, 0xd7, 0xba, 0x17, 0x8e, 0x4d, 0x0f, 0xb8, 0x1e,
	0xf5, 0x5e, 0x8c, 0xc0, 0x5c, 0xbc, 0x0c, 0xf2, 0x56, 0xa4, 0x38, 0x6c, 0x72, 0x59, 0x6d, 0xea,
	0xed, 0x3a, 0xf6, 0xb9, 0x65, 0xf2, 0xa1, 0xff, 0xca, 0x89, 0x04, 0xe6, 0xf2, 0xa3, 0xe7, 0xc2,
	0x32, 0x68, 0x10, 0x41, 0xf8, 0x4d, 0x3e, 0x83, 0x7a, 0x04, 0x49, 0xf9, 0xc7, 0x8c, 0x6f, 0x79,
	0x2d, 0xa2, 0x62, 0x5d, 0xae, 0x00, 0xbf, 0x37, 0x75, 0xf4, 0x57, 0x07, 0xa1, 0x9f, 0xa3, 0x36,
	0xd9, 0x84, 0x86, 0xed, 0x0c, 0x2e, 0xf5, 0xfe, 0xbe, 0x33, 0xb4, 0x0d, 0x9f, 0xa7, 0x88, 0x3c,
	0x29, 0x3a, 0xb9, 0x0b, 0x75, 0x7e, 0x21, 0xfb, 0xfd, 0x85, 0xe5, 0xd1, 0x2e, 0x56, 0xfa, 0x3e,
	0xd4, 0x39, 0xff, 0xda, 0xe9, 0xbf, 0xe4, 0xc4, 0x33, 0xcb, 0x60, 0x64, 0x13, 0x16, 0xfd, 0x91,
	0x22, 0x63, 0x09, 0x19, 0x17, 0xfc, 0x8e, 0x88, 0x57, 0xdd, 0x85, 0x1b, 0x6d, 0xea, 0x45, 0x98,
	0xd5, 0xae, 0x3e, 0x30, 0xa6, 0x0f, 0xc1, 0x4d, 0x58, 0x4a, 0x0b, 0xf1, 0x3d, 0x86, 0xba, 0x73,
	0xc2, 0x75, 0xe4, 0x3e, 0xac, 0xa4, 0x78, 0x75, 0x3b, 0xac, 0x5c, 0xf9, 0xc6, 0xcf, 0x8d, 0x36,
	0xfe, 0x1e, 0x34, 0xe5, 0xcc, 0x72, 0xe1, 0xdc, 0x3c, 0xcf, 0xf1, 0xf4, 0xa8, 0x6a, 0xc7, 0x86,
	0x7a, 0x8e, 0xf9, 0x2f, 0x92, 0xb2, 0x6d, 0x9a, 0x03, 0x6a, 0xea, 0x9e, 0xe5, 0xd8, 0x5c, 0xab,
	0x02, 0x95, 0x9e, 0xee, 0x51, 0xd3, 0x19, 0xbc, 0x0f, 0x54, 0x47, 0xed, 0xe9, 0xae, 0x8a, 0xea,
	0x23, 0xcc, 0x8d, 0x72, 0x3d, 0x19, 0xde, 0xd8, 0x87, 0x5a, 0xeb, 0x9d, 0x4b, 0x07, 0xbc, 0xd2,
	0xc0, 0x53, 0x7d, 0x9c, 0x35, 0xab, 0x50, 0xf1, 0x74, 0xb3, 0x2b, 0x6c, 0xe4, 0xb2, 0xa7, 0x9b,
	0xc7, 0x7c, 0x23, 0xac, 0xe0, 0x71, 0x17, 0xa9, 0xf6, 0x65, 0xf2, 0x28, 0xbe, 0xc4, 0x63, 0x26,
	0xd5, 0xc1, 0x5c, 0xf2, 0x2b, 0xa8, 0x85, 0x92, 0xc5, 0x74, 0xad, 0xc4, 0xf6, 0x73, 0xcc, 0x38,
	0x6d, 0x3e, 0x1c, 0x80, 0x79, 0x5a, 0x76, 0xd9, 0xfc, 0x26, 0xae, 0x4e, 0xc0, 0x43, 0xa6, 0x8a,
	0xa8, 0xa8, 0xcc, 0x2b, 0x08, 0xb7, 0xee, 0x96, 0x5c, 0xf6, 0xb4, 0x37, 0x59, 0x06, 0x8d, 0x11,
	0xed, 0xd8, 0xf1, 0xac, 0xf3, 0xf7, 0xff, 0x7d, 0x28, 0xfd, 0x1f, 0x39, 0x20, 0x7e, 0x3e, 0xd5,
	0x9c, 0x57, 0x8e, 0x77, 0x18, 0xdc, 0xf1, 0x7f, 0x1e, 0x21, 0xb7, 0xbe, 0xd2, 0x4f, 0x93, 0xc9,
	0x14, 0xb9, 0x7d, 0xe0, 0x16, 0xd5, 0x87, 0xef, 0x14, 0xe9, 0x34, 0x19, 0xaf, 0x44, 0x0a, 0xe3,
	0x2a, 0x91, 0x62, 0x1a, 0xe2, 0xd4, 0x4d, 0xda, 0xb5, 0x1d, 0xc4, 0x89, 0x6a, 0x5a, 0x89, 0x37,
	0x8f, 0x1d, 0xb2, 0x06, 0x55, 0xec, 0xc0, 0x47, 0x0f, 0x1f, 0x2a, 0xaa, 0x70, 0xc2, 0xa9, 0xf5,
	0x3d, 0x55, 0x7f, 0xc8, 0xc1, 0x7c, 0x68, 0x20, 0xbe, 0xec, 0x5c, 0x77, 0x2e, 0x85, 0x18, 0xee,
	0xc4, 0x53, 0xbc, 0x1f, 0xe9, 0xb3, 0xa3, 0x9c, 0xcf, 0x43, 0x9d, 0x7c, 0x0a, 0x73, 0xbd, 0x01,
	0xd5, 0x3d, 0x2a, 0x22, 0xf2, 0xe0, 0x93, 0xb0, 0xb2, 0xfa, 0x8b, 0xc4, 0xd7, 0xcc, 0x25, 0x5f,
	0x40, 0x51, 0x08, 0xf3, 0x55, 0xa9, 0x75, 0xfe, 0x8a, 0x71, 0x36, 0x79, 0x2a, 0x11, 0x7d, 0x55,
	0xc8, 0xf6, 0x55, 0x31, 0xe1, 0xab, 0xef, 0xc2, 0x3b, 0x45, 0xa8, 0xe7, 0xe3, 0xae, 0xfd, 0xe8,
	0xce, 0x12, 0xc9, 0x67, 0xee, 0xe6, 0x2d, 0xa8, 0x84, 0x51, 0x4b, 0xca, 0x50, 0x78, 0xe6, 0xf4,
	0x1b, 0x33, 0xfc, 0xa3, 0x63, 0xf6, 0x1b, 0xb9, 0xcd, 0x3f, 0xe5, 0x04, 0xa8, 0xee, 0x14, 0xf1,
	0xbf, 0x5b, 0xd0, 0x6c, 0xb7, 0x3a, 0xdd, 0xd3, 0x8e, 0xd6, 0xda, 0x3e, 0xea, 0x9e, 0x76, 0xb6,
	0x3b, 0xad, 0xee, 0xf1, 0x49, 0xb7, 0xb3, 0xdd, 0x3e, 0x6d, 0xcc, 0x90, 0x55, 0x58, 0x4e, 0xf5,
	0x62, 0x57, 0x8e, 0xac, 0xc1, 0x4a, 0xaa, 0xeb, 0x65, 0xeb, 0xa0, 0xfd, 0xa4, 0xd3, 0xc8, 0x93,
	0x3b, 0x70, 0x5b, 0x26, 0x55, 0x6b, 0xed, 0x9e, 0x1c, 0x1d, 0xb5, 0x8e, 0xf7, 0x1a, 0x85, 0xcd,
	0x13, 0xa8, 0x69, 0xb1, 0xe3, 0x72, 0x19, 0x16, 0xf7, 0x5a, 0xfb, 0xdb, 0x67, 0x87, 0x1d, 0x81,
	0x6f, 0x86, 0x2c, 0x42, 0xed, 0xf8, 0x44, 0x24, 0xe5, 0xc8, 0x12, 0x34, 0x7c, 0x4d, 0x02, 0x35,
	0xbf, 0xf9, 0x0e, 0x2a, 0xcf, 0x4e, 0x0e, 0x43, 0x98, 0xb2, 0xfe, 0xec, 0xe4, 0xb0, 0x7b, 0xd8,
	0x7a, 0xd1, 0x3a, 0xec, 0x1e, 0x9f, 0x1c, 0xb7, 0x1a, 0x33, 0xa4, 0x01, 0xf3, 0x23, 0xda, 0xe9,
	0xa3, 0x46, 0x2e, 0x41, 0xd9, 0x6a, 0xe4, 0xe3, 0x94, 0xe7, 0x8f, 0x1a, 0x85, 0x04, 0x65, 0xab,
	0x51, 0x4c, 0x50, 0xbe, 0x6a, 0xcc, 0x6e, 0x3e, 0x06, 0x92, 0x5e, 0x39, 0xee, 0xf5, 0xed, 0x3e,
	0x77, 0x3f, 0x40, 0xe9, 0x18, 0x0f, 0xe0, 0x46, 0x8e, 0xd4, 0xa0, 0xba, 0xd3, 0xd7, 0x7b, 0xaf,
	0x79, 0xb4, 0x35, 0xf2, 0x5b, 0xff, 0x6e, 0xc2, 0xdc, 0x13, 0xcb, 0xbc, 0xd8, 0xf5, 0x03, 0x80,
	0x9c, 0xe0, 0xc5, 0x5d, 0x78, 0x39, 0xbd, 0x1d, 0x8b, 0x8f, 0xe4, 0x6b, 0xa0, 0x32, 0xae, 0x9b,
	0xb9, 0xea, 0x0c, 0xf9, 0x1a, 0x1a, 0xc9, 0x17, 0x19, 0x12, 0xbf, 0x57, 0x48, 0xde, 0x84, 0x94,
	0x09, 0x1c, 0x28, 0xd9, 0x8c, 0xbf, 0x33, 0x45, 0x6b, 0x49, 0xe2, 0xa5, 0x7f, 0xd6, 0xb3, 0xa1,
	0x72, 0x15, 0x36, 0x54, 0xa4, 0x41, 0x3d, 0x7e, 0x37, 0x27, 0x9f, 0x48, 0x86, 0x0a, 0x38, 0x82,
	0x32, 0xb6, 0x1f, 0x65, 0x7e, 0x07, 0x24, 0xfd, 0x48, 0x46, 0xd4, 0x4c, 0x93, 0xa2, 0x27, 0x3c,
	0x65, 0x22, 0x0f, 0xca, 0x7f, 0x2e, 0xec, 0x33, 0xee, 0x32, 0x96, 0x58, 0xc8, 0xe4, 0x6b, 0x98,
	0x32, 0xae, 0x1b, 0x25, 0x76, 0xb1, 0x64, 0x13, 0x24, 0xe2, 0xcb, 0x4b, 0xc2, 0x64, 0xe9, 0x9b,
	0x91, 0x32, 0x91, 0x07, 0x15, 0xfc, 0x0e, 0x0f, 0x5a, 0xc9, 0xb3, 0x08, 0xf9, 0xf1, 0xb8, 0xf1,
	0xa3, 0x67, 0x1a, 0xe5, 0x4a, 0x7c, 0xa1, 0xfb, 0xd3, 0xe8, 0x74, 0x7a, 0x2e, 0x69, 0x7c, 0x5c,
	0x99, 0xc8, 0x83, 0xf2, 0x5d, 0xac, 0x20, 0xa5, 0x50, 0x3d, 0xd9, 0x48, 0x59, 0x99, 0xf1, 0xcc,
	0xa0, 0x5c, 0x91, 0x13, 0x35, 0xb6, 0x01, 0x46, 0x6f, 0x20, 0x44, 0x91, 0x7b, 0x02, 0xbd, 0xb4,
	0x96, 0xd9, 0x87, 0x82, 0x3a, 0xe8, 0x9a, 0x80, 0xe6, 0xbb, 0xed, 0x60, 0xef, 0xda, 0xd1, 0xd3,
	0x06, 0x18, 0x61, 0x72, 0x09, 0xf3, 0x62, 0xd8, 0x6b, 0xc2, 0xbc, 0x38, 0xcc, 0xaa, 0xce, 0x90,
	0xa7, 0x30, 0x27, 0xa0, 0xdb, 0x24, 0x35, 0x19, 0x01, 0x5f, 0x57, 0x6e, 0x65, 0x77, 0x86, 0xb2,
	0x04, 0x9c, 0x3f, 0x21, 0x2b, 0xfe, 0x5c, 0x90, 0x90, 0x95, 0x78, 0x1e, 0x50, 0x67, 0xc8, 0x0b,
	0x58, 0x48, 0xe0, 0xc2, 0x24, 0x7e, 0xb4, 0xa6, 0x61, 0xe6, 0x44, 0x96, 0x93, 0xc0, 0xca, 0xb8,
	0xed, 0x48, 0x1a, 0xb7, 0x95, 0x26, 0x8a, 0x04, 0x62, 0xac, 0xfc, 0x68, 0x22, 0x4f, 0xcc, 0xf0,
	0x11, 0xd0, 0x2a, 0x33, 0x3c, 0x86, 0xdb, 0xca, 0x0c, 0x8f, 0xe3, 0xb4, 0xbe, 0xe1, 0x69, 0xc0,
	0x2d, 0x61, 0xb8, 0x14, 0x1a, 0x4c, 0x18, 0x9e, 0x81, 0xda, 0xa1, 0x82, 0x34, 0x0a, 0x97, 0x50,
	0x20, 0x05, 0xf6, 0x12, 0x0a, 0x32, 0xa0, 0x3c, 0x54, 0xd0, 0x9e, 0x34, 0x83, 0xf6, 0x15, 0x66,
	0xd0, 0xce, 0x9a, 0x81, 0x8d, 0x97, 0x52, 0x19, 0x36, 0x45, 0x3e, 0x4f, 0x4a, 0xc8, 0xc0, 0xd1,
	0xd2, 0x39, 0x22, 0x0b, 0x24, 0x53, 0x67, 0xc8, 0x25, 0x3e, 0x3c, 0xcb, 0x61, 0x45, 0x72, 0x6f,
	0x82, 0xcd, 0x23, 0xf8, 0xf1, 0xaa, 0xd3, 0x73, 0x40, 0xc9, 0x06, 0x6b, 0xc9, 0xe6, 0x98, 0x55,
	0x4e, 0xa0, 0xba, 0x57, 0x8d, 0x88, 0x5d, 0xa8, 0x46, 0xe8, 0x11, 0x59, 0x4d, 0x1a, 0x19, 0xc1,
	0x40, 0x4a, 0x56, 0x57, 0x28, 0x24, 0x02, 0x77, 0x12, 0x42, 0x44, 0x2c, 0x49, 0xc9, 0xea, 0x42,
	0x21, 0xfb, 0x98, 0x8d, 0x03, 0x00, 0x28, 0x9d, 0x8d, 0x47, 0x50, 0x91, 0x92, 0xd9, 0x17, 0x96,
	0x1e, 0x71, 0x3c, 0x2c, 0x51, 0x7a, 0xa4, 0x50, 0x3c, 0x65, 0x6c, 0x3f, 0xca, 0xfc, 0x0d, 0xfe,
	0x2e, 0x12, 0x43, 0x37, 0x12, 0x15, 0x99, 0x04, 0x9a, 0x51, 0xee, 0x4c, 0xe0, 0x40, 0xd1, 0x54,
	0x82, 0xc8, 0xe8, 0xb6, 0x41, 0xee, 0x8e, 0x1f, 0xac, 0xcb, 0x0a, 0xb2, 0x2c, 0x04, 0x26, 0xda,
	0x37, 0x32, 0xc4, 0x23, 0xbd, 0x6f, 0x32, 0xf0, 0x97, 0xf4, 0xbe, 0xc9, 0x02, 0x50, 0xa2, 0x44,
	0x90, 0x40, 0x33, 0xd2, 0x89, 0x20, 0x8d, 0x83, 0xa4, 0x77, 0x8a, 0x04, 0x12, 0x89, 0x02, 0xd7,
	0xc7, 0xfc, 0xd2, 0x81, 0x1b, 0x81, 0x83, 0x4a, 0x56, 0x17, 0x0a, 0x39, 0xc3, 0x77, 0x5f, 0xf1,
	0xfe, 0x99, 0x48, 0xe4, 0x69, 0x24, 0x40, 0x19, 0xcf, 0x10, 0x14, 0xf0, 0x4b, 0xf1, 0x5b, 0x5e,
	0x50, 0xab, 0xca, 0x6a, 0x5c, 0xe1, 0xa2, 0xa9, 0x8c, 0xed, 0xe7, 0x92, 0x77, 0x1e, 0x7e, 0xf3,
	0x85, 0xe9, 0xf4, 0x75, 0xdb, 0x7c, 0xf0, 0xb3, 0x2d, 0xcf, 0x7b, 0xd0, 0x73, 0x2e, 0x1f, 0xe2,
	0x9f, 0x9c, 0x3d, 0xa7, 0xff, 0x90, 0xd1, 0xc1, 0x5b, 0xab, 0x47, 0x99, 0xf8, 0x9f, 0xe7, 0xab,
	0x12, 0x76, 0x7f, 0xf5, 0x9f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x27, 0xe4, 0x4d, 0xc3, 0x09, 0x2a,
	0x00, 0x00,
}
