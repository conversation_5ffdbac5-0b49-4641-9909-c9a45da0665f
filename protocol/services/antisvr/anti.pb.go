// Code generated by protoc-gen-gogo.
// source: src/antisvr/anti.proto
// DO NOT EDIT!

/*
	Package Anti is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/antisvr/anti.proto

	It has these top-level messages:
		UserLoginInfo
		RecordUserLoginReq
		RecordUserLoginResp
		RegUserInfo
		GetDeviceIdInfoReq
		GetDeviceIdInfoResp
		GetUserLastLoginInfoReq
		BatchGetUserLastLoginInfoReq
		BatchGetUserLastLoginInfoResp
		LoginDeviceInfo
		RecentLoginDeviceList
		LoginUserInfo
		RecentLoginUserList
		VerifyCAPTCHASuccessReq
		VerifyCAPTCHASuccessResp
		GetLastVerifySuccessInfoReq
		GetLastVerifySuccessInfoResp
		CheckUsualDeviceReq
		CheckUsualDeviceResp
		RecordUsualDeviceReq
		RecordUsualDeviceResp
		CheckUserIsValidReq
		CheckUserIsValidResp
		GetUserLoginHistoryReq
		GetUserLoginHistoryResp
		SetUserProfileReq
		SetUserProfileRsp
		GetUserProfileReq
		UserProfileDetail
		GetUserProfileRsp
		TrackUserLoginReq
		TrackUserLoginRsp
		GetUserLoginDeviceReq
		UserLoginDevice
		GetUserLoginDeviceRsp
		UserLoginHit
		GetUserLoginWithDeviceReq
		GetUserLoginWithDeviceRsp
		GetUserUsualDeviceReq
		GetUserUsualDeviceResp
		GetUserLoginIpReq
		UserLoginIp
		GetUserLoginIpRsp
		GetUserLoginWithIPReq
		GetUserLoginWithIPRsp
		GetUserLoginWithImeiReq
		GetUserLoginWithImeiRsp
		GetUserLoginImeiReq
		UserLoginImei
		GetUserLoginImeiRsp
		BatchSetUserProfileReq
		BatchSetUserProfileRsp
		BatchGetUserProfileReq
		BatchGetUserProfileRsp
		GetUserLoginByIdfaReq
		UserLoginWithIdfa
		GetUserLoginByIdfaResp
		RecordIdfaReq
		RecordIdfaResp
		GetUserRegInfoReq
		GetUserRegInfoRsp
		BatchGetUserLoginDeviceReq
		BatchUserLoginDevice
		BatchGetUserLoginDeviceRsp
		BatchGetUserLoginWithDeviceReq
		BatchGetUserLoginWithDeviceRsp
*/
package Anti

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type LOGIN_OP_TYPE int32

const (
	LOGIN_OP_TYPE_LOGIN_OP_Nil         LOGIN_OP_TYPE = 0
	LOGIN_OP_TYPE_LOGIN_OP_Reg         LOGIN_OP_TYPE = 1
	LOGIN_OP_TYPE_LOGIN_OP_Manual      LOGIN_OP_TYPE = 2
	LOGIN_OP_TYPE_LOGIN_OP_AutoLogin   LOGIN_OP_TYPE = 3
	LOGIN_OP_TYPE_LOGIN_OP_SdkActivate LOGIN_OP_TYPE = 4
	LOGIN_OP_TYPE_LOGIN_OP_IOSAvtivate LOGIN_OP_TYPE = 5
)

var LOGIN_OP_TYPE_name = map[int32]string{
	0: "LOGIN_OP_Nil",
	1: "LOGIN_OP_Reg",
	2: "LOGIN_OP_Manual",
	3: "LOGIN_OP_AutoLogin",
	4: "LOGIN_OP_SdkActivate",
	5: "LOGIN_OP_IOSAvtivate",
}
var LOGIN_OP_TYPE_value = map[string]int32{
	"LOGIN_OP_Nil":         0,
	"LOGIN_OP_Reg":         1,
	"LOGIN_OP_Manual":      2,
	"LOGIN_OP_AutoLogin":   3,
	"LOGIN_OP_SdkActivate": 4,
	"LOGIN_OP_IOSAvtivate": 5,
}

func (x LOGIN_OP_TYPE) Enum() *LOGIN_OP_TYPE {
	p := new(LOGIN_OP_TYPE)
	*p = x
	return p
}
func (x LOGIN_OP_TYPE) String() string {
	return proto.EnumName(LOGIN_OP_TYPE_name, int32(x))
}
func (x *LOGIN_OP_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(LOGIN_OP_TYPE_value, data, "LOGIN_OP_TYPE")
	if err != nil {
		return err
	}
	*x = LOGIN_OP_TYPE(value)
	return nil
}
func (LOGIN_OP_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorAnti, []int{0} }

type UnusualDeviceCheckType int32

const (
	UnusualDeviceCheckType_Unual_Device_Check_Captcha UnusualDeviceCheckType = 0
	UnusualDeviceCheckType_Unual_Device_Check_Sms     UnusualDeviceCheckType = 1
)

var UnusualDeviceCheckType_name = map[int32]string{
	0: "Unual_Device_Check_Captcha",
	1: "Unual_Device_Check_Sms",
}
var UnusualDeviceCheckType_value = map[string]int32{
	"Unual_Device_Check_Captcha": 0,
	"Unual_Device_Check_Sms":     1,
}

func (x UnusualDeviceCheckType) Enum() *UnusualDeviceCheckType {
	p := new(UnusualDeviceCheckType)
	*p = x
	return p
}
func (x UnusualDeviceCheckType) String() string {
	return proto.EnumName(UnusualDeviceCheckType_name, int32(x))
}
func (x *UnusualDeviceCheckType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(UnusualDeviceCheckType_value, data, "UnusualDeviceCheckType")
	if err != nil {
		return err
	}
	*x = UnusualDeviceCheckType(value)
	return nil
}
func (UnusualDeviceCheckType) EnumDescriptor() ([]byte, []int) { return fileDescriptorAnti, []int{1} }

type USER_PROFILE int32

const (
	USER_PROFILE_USER_PROFILE_NORMAL    USER_PROFILE = 0
	USER_PROFILE_USER_PROFILE_FAKE      USER_PROFILE = 1
	USER_PROFILE_USER_PROFILE_SUSPICOUS USER_PROFILE = 2
)

var USER_PROFILE_name = map[int32]string{
	0: "USER_PROFILE_NORMAL",
	1: "USER_PROFILE_FAKE",
	2: "USER_PROFILE_SUSPICOUS",
}
var USER_PROFILE_value = map[string]int32{
	"USER_PROFILE_NORMAL":    0,
	"USER_PROFILE_FAKE":      1,
	"USER_PROFILE_SUSPICOUS": 2,
}

func (x USER_PROFILE) Enum() *USER_PROFILE {
	p := new(USER_PROFILE)
	*p = x
	return p
}
func (x USER_PROFILE) String() string {
	return proto.EnumName(USER_PROFILE_name, int32(x))
}
func (x *USER_PROFILE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(USER_PROFILE_value, data, "USER_PROFILE")
	if err != nil {
		return err
	}
	*x = USER_PROFILE(value)
	return nil
}
func (USER_PROFILE) EnumDescriptor() ([]byte, []int) { return fileDescriptorAnti, []int{2} }

type UserLoginInfo_OP_TYPE int32

const (
	UserLoginInfo_REG          UserLoginInfo_OP_TYPE = 1
	UserLoginInfo_MANUAL       UserLoginInfo_OP_TYPE = 2
	UserLoginInfo_AUTO_LOGIN   UserLoginInfo_OP_TYPE = 3
	UserLoginInfo_SDK_ACTIVATE UserLoginInfo_OP_TYPE = 4
)

var UserLoginInfo_OP_TYPE_name = map[int32]string{
	1: "REG",
	2: "MANUAL",
	3: "AUTO_LOGIN",
	4: "SDK_ACTIVATE",
}
var UserLoginInfo_OP_TYPE_value = map[string]int32{
	"REG":          1,
	"MANUAL":       2,
	"AUTO_LOGIN":   3,
	"SDK_ACTIVATE": 4,
}

func (x UserLoginInfo_OP_TYPE) Enum() *UserLoginInfo_OP_TYPE {
	p := new(UserLoginInfo_OP_TYPE)
	*p = x
	return p
}
func (x UserLoginInfo_OP_TYPE) String() string {
	return proto.EnumName(UserLoginInfo_OP_TYPE_name, int32(x))
}
func (x *UserLoginInfo_OP_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(UserLoginInfo_OP_TYPE_value, data, "UserLoginInfo_OP_TYPE")
	if err != nil {
		return err
	}
	*x = UserLoginInfo_OP_TYPE(value)
	return nil
}
func (UserLoginInfo_OP_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorAnti, []int{0, 0} }

type UserLoginInfo struct {
	Uid                 uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	OpType              uint32 `protobuf:"varint,2,req,name=op_type,json=opType" json:"op_type"`
	Result              int32  `protobuf:"varint,3,req,name=result" json:"result"`
	Phone               string `protobuf:"bytes,4,req,name=phone" json:"phone"`
	ThirdPartyType      uint32 `protobuf:"varint,5,opt,name=third_party_type,json=thirdPartyType" json:"third_party_type"`
	Openid              string `protobuf:"bytes,6,opt,name=openid" json:"openid"`
	Imei                string `protobuf:"bytes,10,opt,name=imei" json:"imei"`
	OsVer               string `protobuf:"bytes,11,opt,name=os_ver,json=osVer" json:"os_ver"`
	OsType              string `protobuf:"bytes,12,opt,name=os_type,json=osType" json:"os_type"`
	DeviceModel         string `protobuf:"bytes,13,opt,name=device_model,json=deviceModel" json:"device_model"`
	Signature           string `protobuf:"bytes,14,opt,name=signature" json:"signature"`
	DeviceInfo          string `protobuf:"bytes,15,opt,name=device_info,json=deviceInfo" json:"device_info"`
	IsEmulator          uint32 `protobuf:"varint,16,opt,name=is_emulator,json=isEmulator" json:"is_emulator"`
	DeviceId            string `protobuf:"bytes,17,opt,name=device_id,json=deviceId" json:"device_id"`
	ClientVer           uint32 `protobuf:"varint,18,opt,name=client_ver,json=clientVer" json:"client_ver"`
	ClientIp            string `protobuf:"bytes,19,opt,name=clientIp" json:"clientIp"`
	ClientChannelId     string `protobuf:"bytes,20,opt,name=client_channel_id,json=clientChannelId" json:"client_channel_id"`
	ClientType          uint32 `protobuf:"varint,21,opt,name=client_type,json=clientType" json:"client_type"`
	TerminalType        uint32 `protobuf:"varint,22,opt,name=terminal_type,json=terminalType" json:"terminal_type"`
	IsQuickLoginFromSdk bool   `protobuf:"varint,23,opt,name=is_quick_login_from_sdk,json=isQuickLoginFromSdk" json:"is_quick_login_from_sdk"`
	LoginTime           string `protobuf:"bytes,24,opt,name=login_time,json=loginTime" json:"login_time"`
	ClientPort          int32  `protobuf:"varint,25,opt,name=client_port,json=clientPort" json:"client_port"`
	Idfa                string `protobuf:"bytes,26,opt,name=idfa" json:"idfa"`
	LoginAccount        string `protobuf:"bytes,27,opt,name=login_account,json=loginAccount" json:"login_account"`
	Username            string `protobuf:"bytes,28,opt,name=username" json:"username"`
	Alias               string `protobuf:"bytes,29,opt,name=alias" json:"alias"`
	Nickname            string `protobuf:"bytes,30,opt,name=nickname" json:"nickname"`
	UserSignature       string `protobuf:"bytes,31,opt,name=user_signature,json=userSignature" json:"user_signature"`
	ProxyIp             string `protobuf:"bytes,32,opt,name=proxy_ip,json=proxyIp" json:"proxy_ip"`
	ProxyPort           uint32 `protobuf:"varint,33,opt,name=proxy_port,json=proxyPort" json:"proxy_port"`
	ClientId            uint32 `protobuf:"varint,34,opt,name=client_id,json=clientId" json:"client_id"`
}

func (m *UserLoginInfo) Reset()                    { *m = UserLoginInfo{} }
func (m *UserLoginInfo) String() string            { return proto.CompactTextString(m) }
func (*UserLoginInfo) ProtoMessage()               {}
func (*UserLoginInfo) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{0} }

func (m *UserLoginInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserLoginInfo) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *UserLoginInfo) GetResult() int32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *UserLoginInfo) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *UserLoginInfo) GetThirdPartyType() uint32 {
	if m != nil {
		return m.ThirdPartyType
	}
	return 0
}

func (m *UserLoginInfo) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *UserLoginInfo) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *UserLoginInfo) GetOsVer() string {
	if m != nil {
		return m.OsVer
	}
	return ""
}

func (m *UserLoginInfo) GetOsType() string {
	if m != nil {
		return m.OsType
	}
	return ""
}

func (m *UserLoginInfo) GetDeviceModel() string {
	if m != nil {
		return m.DeviceModel
	}
	return ""
}

func (m *UserLoginInfo) GetSignature() string {
	if m != nil {
		return m.Signature
	}
	return ""
}

func (m *UserLoginInfo) GetDeviceInfo() string {
	if m != nil {
		return m.DeviceInfo
	}
	return ""
}

func (m *UserLoginInfo) GetIsEmulator() uint32 {
	if m != nil {
		return m.IsEmulator
	}
	return 0
}

func (m *UserLoginInfo) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *UserLoginInfo) GetClientVer() uint32 {
	if m != nil {
		return m.ClientVer
	}
	return 0
}

func (m *UserLoginInfo) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *UserLoginInfo) GetClientChannelId() string {
	if m != nil {
		return m.ClientChannelId
	}
	return ""
}

func (m *UserLoginInfo) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *UserLoginInfo) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *UserLoginInfo) GetIsQuickLoginFromSdk() bool {
	if m != nil {
		return m.IsQuickLoginFromSdk
	}
	return false
}

func (m *UserLoginInfo) GetLoginTime() string {
	if m != nil {
		return m.LoginTime
	}
	return ""
}

func (m *UserLoginInfo) GetClientPort() int32 {
	if m != nil {
		return m.ClientPort
	}
	return 0
}

func (m *UserLoginInfo) GetIdfa() string {
	if m != nil {
		return m.Idfa
	}
	return ""
}

func (m *UserLoginInfo) GetLoginAccount() string {
	if m != nil {
		return m.LoginAccount
	}
	return ""
}

func (m *UserLoginInfo) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *UserLoginInfo) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *UserLoginInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserLoginInfo) GetUserSignature() string {
	if m != nil {
		return m.UserSignature
	}
	return ""
}

func (m *UserLoginInfo) GetProxyIp() string {
	if m != nil {
		return m.ProxyIp
	}
	return ""
}

func (m *UserLoginInfo) GetProxyPort() uint32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

func (m *UserLoginInfo) GetClientId() uint32 {
	if m != nil {
		return m.ClientId
	}
	return 0
}

type RecordUserLoginReq struct {
	Info    *UserLoginInfo `protobuf:"bytes,1,req,name=info" json:"info,omitempty"`
	Invalid uint32         `protobuf:"varint,2,opt,name=invalid" json:"invalid"`
}

func (m *RecordUserLoginReq) Reset()                    { *m = RecordUserLoginReq{} }
func (m *RecordUserLoginReq) String() string            { return proto.CompactTextString(m) }
func (*RecordUserLoginReq) ProtoMessage()               {}
func (*RecordUserLoginReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{1} }

func (m *RecordUserLoginReq) GetInfo() *UserLoginInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *RecordUserLoginReq) GetInvalid() uint32 {
	if m != nil {
		return m.Invalid
	}
	return 0
}

type RecordUserLoginResp struct {
	IsNewUsualDevice bool `protobuf:"varint,1,opt,name=is_new_usual_device,json=isNewUsualDevice" json:"is_new_usual_device"`
}

func (m *RecordUserLoginResp) Reset()                    { *m = RecordUserLoginResp{} }
func (m *RecordUserLoginResp) String() string            { return proto.CompactTextString(m) }
func (*RecordUserLoginResp) ProtoMessage()               {}
func (*RecordUserLoginResp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{2} }

func (m *RecordUserLoginResp) GetIsNewUsualDevice() bool {
	if m != nil {
		return m.IsNewUsualDevice
	}
	return false
}

type RegUserInfo struct {
	Uid   uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Phone string `protobuf:"bytes,2,req,name=phone" json:"phone"`
	RegAt string `protobuf:"bytes,3,req,name=reg_at,json=regAt" json:"reg_at"`
}

func (m *RegUserInfo) Reset()                    { *m = RegUserInfo{} }
func (m *RegUserInfo) String() string            { return proto.CompactTextString(m) }
func (*RegUserInfo) ProtoMessage()               {}
func (*RegUserInfo) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{3} }

func (m *RegUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RegUserInfo) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *RegUserInfo) GetRegAt() string {
	if m != nil {
		return m.RegAt
	}
	return ""
}

type GetDeviceIdInfoReq struct {
	DeviceId string `protobuf:"bytes,1,req,name=device_id,json=deviceId" json:"device_id"`
}

func (m *GetDeviceIdInfoReq) Reset()                    { *m = GetDeviceIdInfoReq{} }
func (m *GetDeviceIdInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetDeviceIdInfoReq) ProtoMessage()               {}
func (*GetDeviceIdInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{4} }

func (m *GetDeviceIdInfoReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type GetDeviceIdInfoResp struct {
	RegUserCount uint32         `protobuf:"varint,1,req,name=reg_user_count,json=regUserCount" json:"reg_user_count"`
	RegInfoList  []*RegUserInfo `protobuf:"bytes,2,rep,name=reg_info_list,json=regInfoList" json:"reg_info_list,omitempty"`
}

func (m *GetDeviceIdInfoResp) Reset()                    { *m = GetDeviceIdInfoResp{} }
func (m *GetDeviceIdInfoResp) String() string            { return proto.CompactTextString(m) }
func (*GetDeviceIdInfoResp) ProtoMessage()               {}
func (*GetDeviceIdInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{5} }

func (m *GetDeviceIdInfoResp) GetRegUserCount() uint32 {
	if m != nil {
		return m.RegUserCount
	}
	return 0
}

func (m *GetDeviceIdInfoResp) GetRegInfoList() []*RegUserInfo {
	if m != nil {
		return m.RegInfoList
	}
	return nil
}

type GetUserLastLoginInfoReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserLastLoginInfoReq) Reset()                    { *m = GetUserLastLoginInfoReq{} }
func (m *GetUserLastLoginInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserLastLoginInfoReq) ProtoMessage()               {}
func (*GetUserLastLoginInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{6} }

func (m *GetUserLastLoginInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type BatchGetUserLastLoginInfoReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatchGetUserLastLoginInfoReq) Reset()                    { *m = BatchGetUserLastLoginInfoReq{} }
func (m *BatchGetUserLastLoginInfoReq) String() string            { return proto.CompactTextString(m) }
func (*BatchGetUserLastLoginInfoReq) ProtoMessage()               {}
func (*BatchGetUserLastLoginInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{7} }

func (m *BatchGetUserLastLoginInfoReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetUserLastLoginInfoResp struct {
	InfoList []*UserLoginInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList" json:"info_list,omitempty"`
}

func (m *BatchGetUserLastLoginInfoResp) Reset()         { *m = BatchGetUserLastLoginInfoResp{} }
func (m *BatchGetUserLastLoginInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserLastLoginInfoResp) ProtoMessage()    {}
func (*BatchGetUserLastLoginInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAnti, []int{8}
}

func (m *BatchGetUserLastLoginInfoResp) GetInfoList() []*UserLoginInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 登录设备信息
type LoginDeviceInfo struct {
	DeviceId      string `protobuf:"bytes,1,req,name=device_id,json=deviceId" json:"device_id"`
	LastLoginTime uint32 `protobuf:"varint,2,req,name=last_login_time,json=lastLoginTime" json:"last_login_time"`
}

func (m *LoginDeviceInfo) Reset()                    { *m = LoginDeviceInfo{} }
func (m *LoginDeviceInfo) String() string            { return proto.CompactTextString(m) }
func (*LoginDeviceInfo) ProtoMessage()               {}
func (*LoginDeviceInfo) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{9} }

func (m *LoginDeviceInfo) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *LoginDeviceInfo) GetLastLoginTime() uint32 {
	if m != nil {
		return m.LastLoginTime
	}
	return 0
}

type RecentLoginDeviceList struct {
	Uid        uint32             `protobuf:"varint,1,req,name=uid" json:"uid"`
	DeviceList []*LoginDeviceInfo `protobuf:"bytes,2,rep,name=device_list,json=deviceList" json:"device_list,omitempty"`
}

func (m *RecentLoginDeviceList) Reset()                    { *m = RecentLoginDeviceList{} }
func (m *RecentLoginDeviceList) String() string            { return proto.CompactTextString(m) }
func (*RecentLoginDeviceList) ProtoMessage()               {}
func (*RecentLoginDeviceList) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{10} }

func (m *RecentLoginDeviceList) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecentLoginDeviceList) GetDeviceList() []*LoginDeviceInfo {
	if m != nil {
		return m.DeviceList
	}
	return nil
}

// 登录用户信息
type LoginUserInfo struct {
	Uid           uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	LastLoginTime uint32 `protobuf:"varint,2,req,name=last_login_time,json=lastLoginTime" json:"last_login_time"`
}

func (m *LoginUserInfo) Reset()                    { *m = LoginUserInfo{} }
func (m *LoginUserInfo) String() string            { return proto.CompactTextString(m) }
func (*LoginUserInfo) ProtoMessage()               {}
func (*LoginUserInfo) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{11} }

func (m *LoginUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LoginUserInfo) GetLastLoginTime() uint32 {
	if m != nil {
		return m.LastLoginTime
	}
	return 0
}

type RecentLoginUserList struct {
	DeviceId string           `protobuf:"bytes,1,req,name=device_id,json=deviceId" json:"device_id"`
	UserList []*LoginUserInfo `protobuf:"bytes,2,rep,name=user_list,json=userList" json:"user_list,omitempty"`
}

func (m *RecentLoginUserList) Reset()                    { *m = RecentLoginUserList{} }
func (m *RecentLoginUserList) String() string            { return proto.CompactTextString(m) }
func (*RecentLoginUserList) ProtoMessage()               {}
func (*RecentLoginUserList) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{12} }

func (m *RecentLoginUserList) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *RecentLoginUserList) GetUserList() []*LoginUserInfo {
	if m != nil {
		return m.UserList
	}
	return nil
}

// 图片验证成功
type VerifyCAPTCHASuccessReq struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	VerifyReason uint32 `protobuf:"varint,2,req,name=verify_reason,json=verifyReason" json:"verify_reason"`
	DeviceId     string `protobuf:"bytes,3,req,name=device_id,json=deviceId" json:"device_id"`
}

func (m *VerifyCAPTCHASuccessReq) Reset()                    { *m = VerifyCAPTCHASuccessReq{} }
func (m *VerifyCAPTCHASuccessReq) String() string            { return proto.CompactTextString(m) }
func (*VerifyCAPTCHASuccessReq) ProtoMessage()               {}
func (*VerifyCAPTCHASuccessReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{13} }

func (m *VerifyCAPTCHASuccessReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *VerifyCAPTCHASuccessReq) GetVerifyReason() uint32 {
	if m != nil {
		return m.VerifyReason
	}
	return 0
}

func (m *VerifyCAPTCHASuccessReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type VerifyCAPTCHASuccessResp struct {
}

func (m *VerifyCAPTCHASuccessResp) Reset()                    { *m = VerifyCAPTCHASuccessResp{} }
func (m *VerifyCAPTCHASuccessResp) String() string            { return proto.CompactTextString(m) }
func (*VerifyCAPTCHASuccessResp) ProtoMessage()               {}
func (*VerifyCAPTCHASuccessResp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{14} }

// 获取上次验证成功的信息
type GetLastVerifySuccessInfoReq struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	VerifyReason uint32 `protobuf:"varint,2,req,name=verify_reason,json=verifyReason" json:"verify_reason"`
}

func (m *GetLastVerifySuccessInfoReq) Reset()                    { *m = GetLastVerifySuccessInfoReq{} }
func (m *GetLastVerifySuccessInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetLastVerifySuccessInfoReq) ProtoMessage()               {}
func (*GetLastVerifySuccessInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{15} }

func (m *GetLastVerifySuccessInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLastVerifySuccessInfoReq) GetVerifyReason() uint32 {
	if m != nil {
		return m.VerifyReason
	}
	return 0
}

type GetLastVerifySuccessInfoResp struct {
	DeviceId  string `protobuf:"bytes,1,req,name=device_id,json=deviceId" json:"device_id"`
	Timestamp uint32 `protobuf:"varint,2,req,name=timestamp" json:"timestamp"`
}

func (m *GetLastVerifySuccessInfoResp) Reset()         { *m = GetLastVerifySuccessInfoResp{} }
func (m *GetLastVerifySuccessInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetLastVerifySuccessInfoResp) ProtoMessage()    {}
func (*GetLastVerifySuccessInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAnti, []int{16}
}

func (m *GetLastVerifySuccessInfoResp) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GetLastVerifySuccessInfoResp) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

// 检验是否用户常用设备
type CheckUsualDeviceReq struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	DeviceId string `protobuf:"bytes,2,req,name=device_id,json=deviceId" json:"device_id"`
	Type     uint32 `protobuf:"varint,3,opt,name=type" json:"type"`
}

func (m *CheckUsualDeviceReq) Reset()                    { *m = CheckUsualDeviceReq{} }
func (m *CheckUsualDeviceReq) String() string            { return proto.CompactTextString(m) }
func (*CheckUsualDeviceReq) ProtoMessage()               {}
func (*CheckUsualDeviceReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{17} }

func (m *CheckUsualDeviceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckUsualDeviceReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *CheckUsualDeviceReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type CheckUsualDeviceResp struct {
	IsUsualDevice bool `protobuf:"varint,1,req,name=is_usual_device,json=isUsualDevice" json:"is_usual_device"`
}

func (m *CheckUsualDeviceResp) Reset()                    { *m = CheckUsualDeviceResp{} }
func (m *CheckUsualDeviceResp) String() string            { return proto.CompactTextString(m) }
func (*CheckUsualDeviceResp) ProtoMessage()               {}
func (*CheckUsualDeviceResp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{18} }

func (m *CheckUsualDeviceResp) GetIsUsualDevice() bool {
	if m != nil {
		return m.IsUsualDevice
	}
	return false
}

type RecordUsualDeviceReq struct {
	Uid      uint32 `protobuf:"varint,1,opt,name=uid" json:"uid"`
	DeviceId string `protobuf:"bytes,2,opt,name=device_id,json=deviceId" json:"device_id"`
}

func (m *RecordUsualDeviceReq) Reset()                    { *m = RecordUsualDeviceReq{} }
func (m *RecordUsualDeviceReq) String() string            { return proto.CompactTextString(m) }
func (*RecordUsualDeviceReq) ProtoMessage()               {}
func (*RecordUsualDeviceReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{19} }

func (m *RecordUsualDeviceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecordUsualDeviceReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type RecordUsualDeviceResp struct {
}

func (m *RecordUsualDeviceResp) Reset()                    { *m = RecordUsualDeviceResp{} }
func (m *RecordUsualDeviceResp) String() string            { return proto.CompactTextString(m) }
func (*RecordUsualDeviceResp) ProtoMessage()               {}
func (*RecordUsualDeviceResp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{20} }

// 检验用户是否是合法注册
type CheckUserIsValidReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *CheckUserIsValidReq) Reset()                    { *m = CheckUserIsValidReq{} }
func (m *CheckUserIsValidReq) String() string            { return proto.CompactTextString(m) }
func (*CheckUserIsValidReq) ProtoMessage()               {}
func (*CheckUserIsValidReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{21} }

func (m *CheckUserIsValidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckUserIsValidResp struct {
	IsValid bool `protobuf:"varint,1,req,name=is_valid,json=isValid" json:"is_valid"`
}

func (m *CheckUserIsValidResp) Reset()                    { *m = CheckUserIsValidResp{} }
func (m *CheckUserIsValidResp) String() string            { return proto.CompactTextString(m) }
func (*CheckUserIsValidResp) ProtoMessage()               {}
func (*CheckUserIsValidResp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{22} }

func (m *CheckUserIsValidResp) GetIsValid() bool {
	if m != nil {
		return m.IsValid
	}
	return false
}

type GetUserLoginHistoryReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	BeginTime int64  `protobuf:"varint,2,opt,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime   int64  `protobuf:"varint,3,opt,name=end_time,json=endTime" json:"end_time"`
}

func (m *GetUserLoginHistoryReq) Reset()                    { *m = GetUserLoginHistoryReq{} }
func (m *GetUserLoginHistoryReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserLoginHistoryReq) ProtoMessage()               {}
func (*GetUserLoginHistoryReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{23} }

func (m *GetUserLoginHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserLoginHistoryReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetUserLoginHistoryReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetUserLoginHistoryResp struct {
	LoginHistory []*UserLoginInfo `protobuf:"bytes,1,rep,name=login_history,json=loginHistory" json:"login_history,omitempty"`
}

func (m *GetUserLoginHistoryResp) Reset()                    { *m = GetUserLoginHistoryResp{} }
func (m *GetUserLoginHistoryResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserLoginHistoryResp) ProtoMessage()               {}
func (*GetUserLoginHistoryResp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{24} }

func (m *GetUserLoginHistoryResp) GetLoginHistory() []*UserLoginInfo {
	if m != nil {
		return m.LoginHistory
	}
	return nil
}

type SetUserProfileReq struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Profile    int32  `protobuf:"varint,2,req,name=profile" json:"profile"`
	ReasonCode uint32 `protobuf:"varint,3,req,name=reason_code,json=reasonCode" json:"reason_code"`
	Reason     string `protobuf:"bytes,4,req,name=reason" json:"reason"`
}

func (m *SetUserProfileReq) Reset()                    { *m = SetUserProfileReq{} }
func (m *SetUserProfileReq) String() string            { return proto.CompactTextString(m) }
func (*SetUserProfileReq) ProtoMessage()               {}
func (*SetUserProfileReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{25} }

func (m *SetUserProfileReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserProfileReq) GetProfile() int32 {
	if m != nil {
		return m.Profile
	}
	return 0
}

func (m *SetUserProfileReq) GetReasonCode() uint32 {
	if m != nil {
		return m.ReasonCode
	}
	return 0
}

func (m *SetUserProfileReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type SetUserProfileRsp struct {
}

func (m *SetUserProfileRsp) Reset()                    { *m = SetUserProfileRsp{} }
func (m *SetUserProfileRsp) String() string            { return proto.CompactTextString(m) }
func (*SetUserProfileRsp) ProtoMessage()               {}
func (*SetUserProfileRsp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{26} }

type GetUserProfileReq struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	WithFootmark bool   `protobuf:"varint,2,opt,name=with_footmark,json=withFootmark" json:"with_footmark"`
	WithDetail   bool   `protobuf:"varint,3,opt,name=with_detail,json=withDetail" json:"with_detail"`
}

func (m *GetUserProfileReq) Reset()                    { *m = GetUserProfileReq{} }
func (m *GetUserProfileReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserProfileReq) ProtoMessage()               {}
func (*GetUserProfileReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{27} }

func (m *GetUserProfileReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserProfileReq) GetWithFootmark() bool {
	if m != nil {
		return m.WithFootmark
	}
	return false
}

func (m *GetUserProfileReq) GetWithDetail() bool {
	if m != nil {
		return m.WithDetail
	}
	return false
}

type UserProfileDetail struct {
	Profile    int32  `protobuf:"varint,1,req,name=profile" json:"profile"`
	MarkTime   string `protobuf:"bytes,2,req,name=mark_time,json=markTime" json:"mark_time"`
	ReasonCode uint32 `protobuf:"varint,3,req,name=reason_code,json=reasonCode" json:"reason_code"`
	Reason     string `protobuf:"bytes,4,req,name=reason" json:"reason"`
	Status     uint32 `protobuf:"varint,5,req,name=status" json:"status"`
}

func (m *UserProfileDetail) Reset()                    { *m = UserProfileDetail{} }
func (m *UserProfileDetail) String() string            { return proto.CompactTextString(m) }
func (*UserProfileDetail) ProtoMessage()               {}
func (*UserProfileDetail) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{28} }

func (m *UserProfileDetail) GetProfile() int32 {
	if m != nil {
		return m.Profile
	}
	return 0
}

func (m *UserProfileDetail) GetMarkTime() string {
	if m != nil {
		return m.MarkTime
	}
	return ""
}

func (m *UserProfileDetail) GetReasonCode() uint32 {
	if m != nil {
		return m.ReasonCode
	}
	return 0
}

func (m *UserProfileDetail) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *UserProfileDetail) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type GetUserProfileRsp struct {
	Uid         uint32               `protobuf:"varint,1,opt,name=uid" json:"uid"`
	Profile     int32                `protobuf:"varint,2,opt,name=profile" json:"profile"`
	ProfileList []*UserProfileDetail `protobuf:"bytes,3,rep,name=profile_list,json=profileList" json:"profile_list,omitempty"`
	ReasonCode  uint32               `protobuf:"varint,4,opt,name=reason_code,json=reasonCode" json:"reason_code"`
	Reason      string               `protobuf:"bytes,5,opt,name=reason" json:"reason"`
}

func (m *GetUserProfileRsp) Reset()                    { *m = GetUserProfileRsp{} }
func (m *GetUserProfileRsp) String() string            { return proto.CompactTextString(m) }
func (*GetUserProfileRsp) ProtoMessage()               {}
func (*GetUserProfileRsp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{29} }

func (m *GetUserProfileRsp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserProfileRsp) GetProfile() int32 {
	if m != nil {
		return m.Profile
	}
	return 0
}

func (m *GetUserProfileRsp) GetProfileList() []*UserProfileDetail {
	if m != nil {
		return m.ProfileList
	}
	return nil
}

func (m *GetUserProfileRsp) GetReasonCode() uint32 {
	if m != nil {
		return m.ReasonCode
	}
	return 0
}

func (m *GetUserProfileRsp) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type TrackUserLoginReq struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	LoginAt  uint32 `protobuf:"varint,2,req,name=login_at,json=loginAt" json:"login_at"`
	DeviceId string `protobuf:"bytes,3,opt,name=device_id,json=deviceId" json:"device_id"`
	Imei     string `protobuf:"bytes,4,opt,name=imei" json:"imei"`
	Ip       string `protobuf:"bytes,5,opt,name=ip" json:"ip"`
}

func (m *TrackUserLoginReq) Reset()                    { *m = TrackUserLoginReq{} }
func (m *TrackUserLoginReq) String() string            { return proto.CompactTextString(m) }
func (*TrackUserLoginReq) ProtoMessage()               {}
func (*TrackUserLoginReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{30} }

func (m *TrackUserLoginReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TrackUserLoginReq) GetLoginAt() uint32 {
	if m != nil {
		return m.LoginAt
	}
	return 0
}

func (m *TrackUserLoginReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *TrackUserLoginReq) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *TrackUserLoginReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

type TrackUserLoginRsp struct {
}

func (m *TrackUserLoginRsp) Reset()                    { *m = TrackUserLoginRsp{} }
func (m *TrackUserLoginRsp) String() string            { return proto.CompactTextString(m) }
func (*TrackUserLoginRsp) ProtoMessage()               {}
func (*TrackUserLoginRsp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{31} }

// 用户登录设备
type GetUserLoginDeviceReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserLoginDeviceReq) Reset()                    { *m = GetUserLoginDeviceReq{} }
func (m *GetUserLoginDeviceReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserLoginDeviceReq) ProtoMessage()               {}
func (*GetUserLoginDeviceReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{32} }

func (m *GetUserLoginDeviceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type UserLoginDevice struct {
	DeviceId string `protobuf:"bytes,1,req,name=device_id,json=deviceId" json:"device_id"`
	LoginAt  uint32 `protobuf:"varint,2,req,name=login_at,json=loginAt" json:"login_at"`
}

func (m *UserLoginDevice) Reset()                    { *m = UserLoginDevice{} }
func (m *UserLoginDevice) String() string            { return proto.CompactTextString(m) }
func (*UserLoginDevice) ProtoMessage()               {}
func (*UserLoginDevice) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{33} }

func (m *UserLoginDevice) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *UserLoginDevice) GetLoginAt() uint32 {
	if m != nil {
		return m.LoginAt
	}
	return 0
}

type GetUserLoginDeviceRsp struct {
	Uid          uint32             `protobuf:"varint,1,opt,name=uid" json:"uid"`
	LoginDevices []*UserLoginDevice `protobuf:"bytes,2,rep,name=login_devices,json=loginDevices" json:"login_devices,omitempty"`
}

func (m *GetUserLoginDeviceRsp) Reset()                    { *m = GetUserLoginDeviceRsp{} }
func (m *GetUserLoginDeviceRsp) String() string            { return proto.CompactTextString(m) }
func (*GetUserLoginDeviceRsp) ProtoMessage()               {}
func (*GetUserLoginDeviceRsp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{34} }

func (m *GetUserLoginDeviceRsp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserLoginDeviceRsp) GetLoginDevices() []*UserLoginDevice {
	if m != nil {
		return m.LoginDevices
	}
	return nil
}

type UserLoginHit struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	LoginAt uint32 `protobuf:"varint,2,req,name=login_at,json=loginAt" json:"login_at"`
}

func (m *UserLoginHit) Reset()                    { *m = UserLoginHit{} }
func (m *UserLoginHit) String() string            { return proto.CompactTextString(m) }
func (*UserLoginHit) ProtoMessage()               {}
func (*UserLoginHit) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{35} }

func (m *UserLoginHit) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserLoginHit) GetLoginAt() uint32 {
	if m != nil {
		return m.LoginAt
	}
	return 0
}

type GetUserLoginWithDeviceReq struct {
	DeviceId string `protobuf:"bytes,1,req,name=device_id,json=deviceId" json:"device_id"`
}

func (m *GetUserLoginWithDeviceReq) Reset()                    { *m = GetUserLoginWithDeviceReq{} }
func (m *GetUserLoginWithDeviceReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserLoginWithDeviceReq) ProtoMessage()               {}
func (*GetUserLoginWithDeviceReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{36} }

func (m *GetUserLoginWithDeviceReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type GetUserLoginWithDeviceRsp struct {
	DeviceId   string          `protobuf:"bytes,1,opt,name=device_id,json=deviceId" json:"device_id"`
	LoginUsers []*UserLoginHit `protobuf:"bytes,2,rep,name=login_users,json=loginUsers" json:"login_users,omitempty"`
}

func (m *GetUserLoginWithDeviceRsp) Reset()                    { *m = GetUserLoginWithDeviceRsp{} }
func (m *GetUserLoginWithDeviceRsp) String() string            { return proto.CompactTextString(m) }
func (*GetUserLoginWithDeviceRsp) ProtoMessage()               {}
func (*GetUserLoginWithDeviceRsp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{37} }

func (m *GetUserLoginWithDeviceRsp) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GetUserLoginWithDeviceRsp) GetLoginUsers() []*UserLoginHit {
	if m != nil {
		return m.LoginUsers
	}
	return nil
}

//
type GetUserUsualDeviceReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	BeginTime uint32 `protobuf:"varint,2,opt,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime   uint32 `protobuf:"varint,3,opt,name=end_time,json=endTime" json:"end_time"`
	Limit     int32  `protobuf:"varint,4,opt,name=limit" json:"limit"`
}

func (m *GetUserUsualDeviceReq) Reset()                    { *m = GetUserUsualDeviceReq{} }
func (m *GetUserUsualDeviceReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserUsualDeviceReq) ProtoMessage()               {}
func (*GetUserUsualDeviceReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{38} }

func (m *GetUserUsualDeviceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserUsualDeviceReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetUserUsualDeviceReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetUserUsualDeviceReq) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetUserUsualDeviceResp struct {
	Uid          uint32             `protobuf:"varint,1,opt,name=uid" json:"uid"`
	LoginDevices []*UserLoginDevice `protobuf:"bytes,2,rep,name=login_devices,json=loginDevices" json:"login_devices,omitempty"`
}

func (m *GetUserUsualDeviceResp) Reset()                    { *m = GetUserUsualDeviceResp{} }
func (m *GetUserUsualDeviceResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserUsualDeviceResp) ProtoMessage()               {}
func (*GetUserUsualDeviceResp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{39} }

func (m *GetUserUsualDeviceResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserUsualDeviceResp) GetLoginDevices() []*UserLoginDevice {
	if m != nil {
		return m.LoginDevices
	}
	return nil
}

// 用户的登陆ip
type GetUserLoginIpReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserLoginIpReq) Reset()                    { *m = GetUserLoginIpReq{} }
func (m *GetUserLoginIpReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserLoginIpReq) ProtoMessage()               {}
func (*GetUserLoginIpReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{40} }

func (m *GetUserLoginIpReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type UserLoginIp struct {
	Ip      string `protobuf:"bytes,1,req,name=ip" json:"ip"`
	LoginAt uint32 `protobuf:"varint,2,req,name=login_at,json=loginAt" json:"login_at"`
}

func (m *UserLoginIp) Reset()                    { *m = UserLoginIp{} }
func (m *UserLoginIp) String() string            { return proto.CompactTextString(m) }
func (*UserLoginIp) ProtoMessage()               {}
func (*UserLoginIp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{41} }

func (m *UserLoginIp) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *UserLoginIp) GetLoginAt() uint32 {
	if m != nil {
		return m.LoginAt
	}
	return 0
}

type GetUserLoginIpRsp struct {
	Uid      uint32         `protobuf:"varint,1,opt,name=uid" json:"uid"`
	LoginIps []*UserLoginIp `protobuf:"bytes,2,rep,name=login_ips,json=loginIps" json:"login_ips,omitempty"`
}

func (m *GetUserLoginIpRsp) Reset()                    { *m = GetUserLoginIpRsp{} }
func (m *GetUserLoginIpRsp) String() string            { return proto.CompactTextString(m) }
func (*GetUserLoginIpRsp) ProtoMessage()               {}
func (*GetUserLoginIpRsp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{42} }

func (m *GetUserLoginIpRsp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserLoginIpRsp) GetLoginIps() []*UserLoginIp {
	if m != nil {
		return m.LoginIps
	}
	return nil
}

// 使用同IP登陆的用户
type GetUserLoginWithIPReq struct {
	Ip string `protobuf:"bytes,1,req,name=ip" json:"ip"`
}

func (m *GetUserLoginWithIPReq) Reset()                    { *m = GetUserLoginWithIPReq{} }
func (m *GetUserLoginWithIPReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserLoginWithIPReq) ProtoMessage()               {}
func (*GetUserLoginWithIPReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{43} }

func (m *GetUserLoginWithIPReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

type GetUserLoginWithIPRsp struct {
	Ip         string          `protobuf:"bytes,1,req,name=ip" json:"ip"`
	LoginUsers []*UserLoginHit `protobuf:"bytes,2,rep,name=login_users,json=loginUsers" json:"login_users,omitempty"`
}

func (m *GetUserLoginWithIPRsp) Reset()                    { *m = GetUserLoginWithIPRsp{} }
func (m *GetUserLoginWithIPRsp) String() string            { return proto.CompactTextString(m) }
func (*GetUserLoginWithIPRsp) ProtoMessage()               {}
func (*GetUserLoginWithIPRsp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{44} }

func (m *GetUserLoginWithIPRsp) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *GetUserLoginWithIPRsp) GetLoginUsers() []*UserLoginHit {
	if m != nil {
		return m.LoginUsers
	}
	return nil
}

// imei
type GetUserLoginWithImeiReq struct {
	Imei string `protobuf:"bytes,1,req,name=imei" json:"imei"`
}

func (m *GetUserLoginWithImeiReq) Reset()                    { *m = GetUserLoginWithImeiReq{} }
func (m *GetUserLoginWithImeiReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserLoginWithImeiReq) ProtoMessage()               {}
func (*GetUserLoginWithImeiReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{45} }

func (m *GetUserLoginWithImeiReq) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

type GetUserLoginWithImeiRsp struct {
	Imei       string          `protobuf:"bytes,1,req,name=imei" json:"imei"`
	LoginUsers []*UserLoginHit `protobuf:"bytes,2,rep,name=login_users,json=loginUsers" json:"login_users,omitempty"`
}

func (m *GetUserLoginWithImeiRsp) Reset()                    { *m = GetUserLoginWithImeiRsp{} }
func (m *GetUserLoginWithImeiRsp) String() string            { return proto.CompactTextString(m) }
func (*GetUserLoginWithImeiRsp) ProtoMessage()               {}
func (*GetUserLoginWithImeiRsp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{46} }

func (m *GetUserLoginWithImeiRsp) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *GetUserLoginWithImeiRsp) GetLoginUsers() []*UserLoginHit {
	if m != nil {
		return m.LoginUsers
	}
	return nil
}

type GetUserLoginImeiReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserLoginImeiReq) Reset()                    { *m = GetUserLoginImeiReq{} }
func (m *GetUserLoginImeiReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserLoginImeiReq) ProtoMessage()               {}
func (*GetUserLoginImeiReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{47} }

func (m *GetUserLoginImeiReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type UserLoginImei struct {
	Imei    string `protobuf:"bytes,1,req,name=imei" json:"imei"`
	LoginAt uint32 `protobuf:"varint,2,req,name=login_at,json=loginAt" json:"login_at"`
}

func (m *UserLoginImei) Reset()                    { *m = UserLoginImei{} }
func (m *UserLoginImei) String() string            { return proto.CompactTextString(m) }
func (*UserLoginImei) ProtoMessage()               {}
func (*UserLoginImei) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{48} }

func (m *UserLoginImei) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *UserLoginImei) GetLoginAt() uint32 {
	if m != nil {
		return m.LoginAt
	}
	return 0
}

type GetUserLoginImeiRsp struct {
	Uid        uint32           `protobuf:"varint,1,opt,name=uid" json:"uid"`
	LoginImeis []*UserLoginImei `protobuf:"bytes,2,rep,name=login_imeis,json=loginImeis" json:"login_imeis,omitempty"`
}

func (m *GetUserLoginImeiRsp) Reset()                    { *m = GetUserLoginImeiRsp{} }
func (m *GetUserLoginImeiRsp) String() string            { return proto.CompactTextString(m) }
func (*GetUserLoginImeiRsp) ProtoMessage()               {}
func (*GetUserLoginImeiRsp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{49} }

func (m *GetUserLoginImeiRsp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserLoginImeiRsp) GetLoginImeis() []*UserLoginImei {
	if m != nil {
		return m.LoginImeis
	}
	return nil
}

// 批量操作
type BatchSetUserProfileReq struct {
	UidList    []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
	Profile    int32    `protobuf:"varint,2,req,name=profile" json:"profile"`
	ReasonCode uint32   `protobuf:"varint,3,req,name=reason_code,json=reasonCode" json:"reason_code"`
	Reason     string   `protobuf:"bytes,4,req,name=reason" json:"reason"`
}

func (m *BatchSetUserProfileReq) Reset()                    { *m = BatchSetUserProfileReq{} }
func (m *BatchSetUserProfileReq) String() string            { return proto.CompactTextString(m) }
func (*BatchSetUserProfileReq) ProtoMessage()               {}
func (*BatchSetUserProfileReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{50} }

func (m *BatchSetUserProfileReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchSetUserProfileReq) GetProfile() int32 {
	if m != nil {
		return m.Profile
	}
	return 0
}

func (m *BatchSetUserProfileReq) GetReasonCode() uint32 {
	if m != nil {
		return m.ReasonCode
	}
	return 0
}

func (m *BatchSetUserProfileReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type BatchSetUserProfileRsp struct {
}

func (m *BatchSetUserProfileRsp) Reset()                    { *m = BatchSetUserProfileRsp{} }
func (m *BatchSetUserProfileRsp) String() string            { return proto.CompactTextString(m) }
func (*BatchSetUserProfileRsp) ProtoMessage()               {}
func (*BatchSetUserProfileRsp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{51} }

type BatchGetUserProfileReq struct {
	UidList      []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
	WithFootmark bool     `protobuf:"varint,2,opt,name=with_footmark,json=withFootmark" json:"with_footmark"`
}

func (m *BatchGetUserProfileReq) Reset()                    { *m = BatchGetUserProfileReq{} }
func (m *BatchGetUserProfileReq) String() string            { return proto.CompactTextString(m) }
func (*BatchGetUserProfileReq) ProtoMessage()               {}
func (*BatchGetUserProfileReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{52} }

func (m *BatchGetUserProfileReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetUserProfileReq) GetWithFootmark() bool {
	if m != nil {
		return m.WithFootmark
	}
	return false
}

type BatchGetUserProfileRsp struct {
	UserProfileList []*GetUserProfileRsp `protobuf:"bytes,3,rep,name=user_profile_list,json=userProfileList" json:"user_profile_list,omitempty"`
}

func (m *BatchGetUserProfileRsp) Reset()                    { *m = BatchGetUserProfileRsp{} }
func (m *BatchGetUserProfileRsp) String() string            { return proto.CompactTextString(m) }
func (*BatchGetUserProfileRsp) ProtoMessage()               {}
func (*BatchGetUserProfileRsp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{53} }

func (m *BatchGetUserProfileRsp) GetUserProfileList() []*GetUserProfileRsp {
	if m != nil {
		return m.UserProfileList
	}
	return nil
}

type GetUserLoginByIdfaReq struct {
	Idfa string `protobuf:"bytes,1,req,name=idfa" json:"idfa"`
}

func (m *GetUserLoginByIdfaReq) Reset()                    { *m = GetUserLoginByIdfaReq{} }
func (m *GetUserLoginByIdfaReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserLoginByIdfaReq) ProtoMessage()               {}
func (*GetUserLoginByIdfaReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{54} }

func (m *GetUserLoginByIdfaReq) GetIdfa() string {
	if m != nil {
		return m.Idfa
	}
	return ""
}

type UserLoginWithIdfa struct {
	Idfa   string `protobuf:"bytes,1,req,name=idfa" json:"idfa"`
	Uid    uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	At     uint32 `protobuf:"varint,3,req,name=at" json:"at"`
	OpType uint32 `protobuf:"varint,4,req,name=op_type,json=opType" json:"op_type"`
}

func (m *UserLoginWithIdfa) Reset()                    { *m = UserLoginWithIdfa{} }
func (m *UserLoginWithIdfa) String() string            { return proto.CompactTextString(m) }
func (*UserLoginWithIdfa) ProtoMessage()               {}
func (*UserLoginWithIdfa) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{55} }

func (m *UserLoginWithIdfa) GetIdfa() string {
	if m != nil {
		return m.Idfa
	}
	return ""
}

func (m *UserLoginWithIdfa) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserLoginWithIdfa) GetAt() uint32 {
	if m != nil {
		return m.At
	}
	return 0
}

func (m *UserLoginWithIdfa) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

type GetUserLoginByIdfaResp struct {
	Rec *UserLoginWithIdfa `protobuf:"bytes,1,opt,name=rec" json:"rec,omitempty"`
}

func (m *GetUserLoginByIdfaResp) Reset()                    { *m = GetUserLoginByIdfaResp{} }
func (m *GetUserLoginByIdfaResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserLoginByIdfaResp) ProtoMessage()               {}
func (*GetUserLoginByIdfaResp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{56} }

func (m *GetUserLoginByIdfaResp) GetRec() *UserLoginWithIdfa {
	if m != nil {
		return m.Rec
	}
	return nil
}

type RecordIdfaReq struct {
	Uid    uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	OpType uint32 `protobuf:"varint,2,req,name=op_type,json=opType" json:"op_type"`
	Idfa   string `protobuf:"bytes,3,req,name=idfa" json:"idfa"`
	At     uint32 `protobuf:"varint,4,opt,name=at" json:"at"`
}

func (m *RecordIdfaReq) Reset()                    { *m = RecordIdfaReq{} }
func (m *RecordIdfaReq) String() string            { return proto.CompactTextString(m) }
func (*RecordIdfaReq) ProtoMessage()               {}
func (*RecordIdfaReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{57} }

func (m *RecordIdfaReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecordIdfaReq) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *RecordIdfaReq) GetIdfa() string {
	if m != nil {
		return m.Idfa
	}
	return ""
}

func (m *RecordIdfaReq) GetAt() uint32 {
	if m != nil {
		return m.At
	}
	return 0
}

type RecordIdfaResp struct {
}

func (m *RecordIdfaResp) Reset()                    { *m = RecordIdfaResp{} }
func (m *RecordIdfaResp) String() string            { return proto.CompactTextString(m) }
func (*RecordIdfaResp) ProtoMessage()               {}
func (*RecordIdfaResp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{58} }

type GetUserRegInfoReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserRegInfoReq) Reset()                    { *m = GetUserRegInfoReq{} }
func (m *GetUserRegInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserRegInfoReq) ProtoMessage()               {}
func (*GetUserRegInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{59} }

func (m *GetUserRegInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserRegInfoRsp struct {
	Info *UserLoginInfo `protobuf:"bytes,1,opt,name=info" json:"info,omitempty"`
}

func (m *GetUserRegInfoRsp) Reset()                    { *m = GetUserRegInfoRsp{} }
func (m *GetUserRegInfoRsp) String() string            { return proto.CompactTextString(m) }
func (*GetUserRegInfoRsp) ProtoMessage()               {}
func (*GetUserRegInfoRsp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{60} }

func (m *GetUserRegInfoRsp) GetInfo() *UserLoginInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type BatchGetUserLoginDeviceReq struct {
	Uids []uint32 `protobuf:"varint,1,rep,name=uids" json:"uids,omitempty"`
}

func (m *BatchGetUserLoginDeviceReq) Reset()                    { *m = BatchGetUserLoginDeviceReq{} }
func (m *BatchGetUserLoginDeviceReq) String() string            { return proto.CompactTextString(m) }
func (*BatchGetUserLoginDeviceReq) ProtoMessage()               {}
func (*BatchGetUserLoginDeviceReq) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{61} }

func (m *BatchGetUserLoginDeviceReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type BatchUserLoginDevice struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	DeviceId string `protobuf:"bytes,2,req,name=device_id,json=deviceId" json:"device_id"`
	LoginAt  uint32 `protobuf:"varint,3,req,name=login_at,json=loginAt" json:"login_at"`
}

func (m *BatchUserLoginDevice) Reset()                    { *m = BatchUserLoginDevice{} }
func (m *BatchUserLoginDevice) String() string            { return proto.CompactTextString(m) }
func (*BatchUserLoginDevice) ProtoMessage()               {}
func (*BatchUserLoginDevice) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{62} }

func (m *BatchUserLoginDevice) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchUserLoginDevice) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *BatchUserLoginDevice) GetLoginAt() uint32 {
	if m != nil {
		return m.LoginAt
	}
	return 0
}

type BatchGetUserLoginDeviceRsp struct {
	LoginDevices []*BatchUserLoginDevice `protobuf:"bytes,2,rep,name=login_devices,json=loginDevices" json:"login_devices,omitempty"`
}

func (m *BatchGetUserLoginDeviceRsp) Reset()                    { *m = BatchGetUserLoginDeviceRsp{} }
func (m *BatchGetUserLoginDeviceRsp) String() string            { return proto.CompactTextString(m) }
func (*BatchGetUserLoginDeviceRsp) ProtoMessage()               {}
func (*BatchGetUserLoginDeviceRsp) Descriptor() ([]byte, []int) { return fileDescriptorAnti, []int{63} }

func (m *BatchGetUserLoginDeviceRsp) GetLoginDevices() []*BatchUserLoginDevice {
	if m != nil {
		return m.LoginDevices
	}
	return nil
}

type BatchGetUserLoginWithDeviceReq struct {
	DeviceIds []string `protobuf:"bytes,1,rep,name=device_ids,json=deviceIds" json:"device_ids,omitempty"`
}

func (m *BatchGetUserLoginWithDeviceReq) Reset()         { *m = BatchGetUserLoginWithDeviceReq{} }
func (m *BatchGetUserLoginWithDeviceReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserLoginWithDeviceReq) ProtoMessage()    {}
func (*BatchGetUserLoginWithDeviceReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAnti, []int{64}
}

func (m *BatchGetUserLoginWithDeviceReq) GetDeviceIds() []string {
	if m != nil {
		return m.DeviceIds
	}
	return nil
}

type BatchGetUserLoginWithDeviceRsp struct {
	LoginUsers []*BatchUserLoginDevice `protobuf:"bytes,1,rep,name=login_users,json=loginUsers" json:"login_users,omitempty"`
}

func (m *BatchGetUserLoginWithDeviceRsp) Reset()         { *m = BatchGetUserLoginWithDeviceRsp{} }
func (m *BatchGetUserLoginWithDeviceRsp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserLoginWithDeviceRsp) ProtoMessage()    {}
func (*BatchGetUserLoginWithDeviceRsp) Descriptor() ([]byte, []int) {
	return fileDescriptorAnti, []int{65}
}

func (m *BatchGetUserLoginWithDeviceRsp) GetLoginUsers() []*BatchUserLoginDevice {
	if m != nil {
		return m.LoginUsers
	}
	return nil
}

func init() {
	proto.RegisterType((*UserLoginInfo)(nil), "Anti.UserLoginInfo")
	proto.RegisterType((*RecordUserLoginReq)(nil), "Anti.RecordUserLoginReq")
	proto.RegisterType((*RecordUserLoginResp)(nil), "Anti.RecordUserLoginResp")
	proto.RegisterType((*RegUserInfo)(nil), "Anti.RegUserInfo")
	proto.RegisterType((*GetDeviceIdInfoReq)(nil), "Anti.GetDeviceIdInfoReq")
	proto.RegisterType((*GetDeviceIdInfoResp)(nil), "Anti.GetDeviceIdInfoResp")
	proto.RegisterType((*GetUserLastLoginInfoReq)(nil), "Anti.GetUserLastLoginInfoReq")
	proto.RegisterType((*BatchGetUserLastLoginInfoReq)(nil), "Anti.BatchGetUserLastLoginInfoReq")
	proto.RegisterType((*BatchGetUserLastLoginInfoResp)(nil), "Anti.BatchGetUserLastLoginInfoResp")
	proto.RegisterType((*LoginDeviceInfo)(nil), "Anti.LoginDeviceInfo")
	proto.RegisterType((*RecentLoginDeviceList)(nil), "Anti.RecentLoginDeviceList")
	proto.RegisterType((*LoginUserInfo)(nil), "Anti.LoginUserInfo")
	proto.RegisterType((*RecentLoginUserList)(nil), "Anti.RecentLoginUserList")
	proto.RegisterType((*VerifyCAPTCHASuccessReq)(nil), "Anti.VerifyCAPTCHASuccessReq")
	proto.RegisterType((*VerifyCAPTCHASuccessResp)(nil), "Anti.VerifyCAPTCHASuccessResp")
	proto.RegisterType((*GetLastVerifySuccessInfoReq)(nil), "Anti.GetLastVerifySuccessInfoReq")
	proto.RegisterType((*GetLastVerifySuccessInfoResp)(nil), "Anti.GetLastVerifySuccessInfoResp")
	proto.RegisterType((*CheckUsualDeviceReq)(nil), "Anti.CheckUsualDeviceReq")
	proto.RegisterType((*CheckUsualDeviceResp)(nil), "Anti.CheckUsualDeviceResp")
	proto.RegisterType((*RecordUsualDeviceReq)(nil), "Anti.RecordUsualDeviceReq")
	proto.RegisterType((*RecordUsualDeviceResp)(nil), "Anti.RecordUsualDeviceResp")
	proto.RegisterType((*CheckUserIsValidReq)(nil), "Anti.CheckUserIsValidReq")
	proto.RegisterType((*CheckUserIsValidResp)(nil), "Anti.CheckUserIsValidResp")
	proto.RegisterType((*GetUserLoginHistoryReq)(nil), "Anti.GetUserLoginHistoryReq")
	proto.RegisterType((*GetUserLoginHistoryResp)(nil), "Anti.GetUserLoginHistoryResp")
	proto.RegisterType((*SetUserProfileReq)(nil), "Anti.SetUserProfileReq")
	proto.RegisterType((*SetUserProfileRsp)(nil), "Anti.SetUserProfileRsp")
	proto.RegisterType((*GetUserProfileReq)(nil), "Anti.GetUserProfileReq")
	proto.RegisterType((*UserProfileDetail)(nil), "Anti.UserProfileDetail")
	proto.RegisterType((*GetUserProfileRsp)(nil), "Anti.GetUserProfileRsp")
	proto.RegisterType((*TrackUserLoginReq)(nil), "Anti.TrackUserLoginReq")
	proto.RegisterType((*TrackUserLoginRsp)(nil), "Anti.TrackUserLoginRsp")
	proto.RegisterType((*GetUserLoginDeviceReq)(nil), "Anti.GetUserLoginDeviceReq")
	proto.RegisterType((*UserLoginDevice)(nil), "Anti.UserLoginDevice")
	proto.RegisterType((*GetUserLoginDeviceRsp)(nil), "Anti.GetUserLoginDeviceRsp")
	proto.RegisterType((*UserLoginHit)(nil), "Anti.UserLoginHit")
	proto.RegisterType((*GetUserLoginWithDeviceReq)(nil), "Anti.GetUserLoginWithDeviceReq")
	proto.RegisterType((*GetUserLoginWithDeviceRsp)(nil), "Anti.GetUserLoginWithDeviceRsp")
	proto.RegisterType((*GetUserUsualDeviceReq)(nil), "Anti.GetUserUsualDeviceReq")
	proto.RegisterType((*GetUserUsualDeviceResp)(nil), "Anti.GetUserUsualDeviceResp")
	proto.RegisterType((*GetUserLoginIpReq)(nil), "Anti.GetUserLoginIpReq")
	proto.RegisterType((*UserLoginIp)(nil), "Anti.UserLoginIp")
	proto.RegisterType((*GetUserLoginIpRsp)(nil), "Anti.GetUserLoginIpRsp")
	proto.RegisterType((*GetUserLoginWithIPReq)(nil), "Anti.GetUserLoginWithIPReq")
	proto.RegisterType((*GetUserLoginWithIPRsp)(nil), "Anti.GetUserLoginWithIPRsp")
	proto.RegisterType((*GetUserLoginWithImeiReq)(nil), "Anti.GetUserLoginWithImeiReq")
	proto.RegisterType((*GetUserLoginWithImeiRsp)(nil), "Anti.GetUserLoginWithImeiRsp")
	proto.RegisterType((*GetUserLoginImeiReq)(nil), "Anti.GetUserLoginImeiReq")
	proto.RegisterType((*UserLoginImei)(nil), "Anti.UserLoginImei")
	proto.RegisterType((*GetUserLoginImeiRsp)(nil), "Anti.GetUserLoginImeiRsp")
	proto.RegisterType((*BatchSetUserProfileReq)(nil), "Anti.BatchSetUserProfileReq")
	proto.RegisterType((*BatchSetUserProfileRsp)(nil), "Anti.BatchSetUserProfileRsp")
	proto.RegisterType((*BatchGetUserProfileReq)(nil), "Anti.BatchGetUserProfileReq")
	proto.RegisterType((*BatchGetUserProfileRsp)(nil), "Anti.BatchGetUserProfileRsp")
	proto.RegisterType((*GetUserLoginByIdfaReq)(nil), "Anti.GetUserLoginByIdfaReq")
	proto.RegisterType((*UserLoginWithIdfa)(nil), "Anti.UserLoginWithIdfa")
	proto.RegisterType((*GetUserLoginByIdfaResp)(nil), "Anti.GetUserLoginByIdfaResp")
	proto.RegisterType((*RecordIdfaReq)(nil), "Anti.RecordIdfaReq")
	proto.RegisterType((*RecordIdfaResp)(nil), "Anti.RecordIdfaResp")
	proto.RegisterType((*GetUserRegInfoReq)(nil), "Anti.GetUserRegInfoReq")
	proto.RegisterType((*GetUserRegInfoRsp)(nil), "Anti.GetUserRegInfoRsp")
	proto.RegisterType((*BatchGetUserLoginDeviceReq)(nil), "Anti.BatchGetUserLoginDeviceReq")
	proto.RegisterType((*BatchUserLoginDevice)(nil), "Anti.BatchUserLoginDevice")
	proto.RegisterType((*BatchGetUserLoginDeviceRsp)(nil), "Anti.BatchGetUserLoginDeviceRsp")
	proto.RegisterType((*BatchGetUserLoginWithDeviceReq)(nil), "Anti.BatchGetUserLoginWithDeviceReq")
	proto.RegisterType((*BatchGetUserLoginWithDeviceRsp)(nil), "Anti.BatchGetUserLoginWithDeviceRsp")
	proto.RegisterEnum("Anti.LOGIN_OP_TYPE", LOGIN_OP_TYPE_name, LOGIN_OP_TYPE_value)
	proto.RegisterEnum("Anti.UnusualDeviceCheckType", UnusualDeviceCheckType_name, UnusualDeviceCheckType_value)
	proto.RegisterEnum("Anti.USER_PROFILE", USER_PROFILE_name, USER_PROFILE_value)
	proto.RegisterEnum("Anti.UserLoginInfo_OP_TYPE", UserLoginInfo_OP_TYPE_name, UserLoginInfo_OP_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Anti service

type AntiClient interface {
	RecordUserLogin(ctx context.Context, in *RecordUserLoginReq, opts ...grpc.CallOption) (*RecordUserLoginResp, error)
	GetDeviceIdInfo(ctx context.Context, in *GetDeviceIdInfoReq, opts ...grpc.CallOption) (*GetDeviceIdInfoResp, error)
	GetUserLastLoginInfo(ctx context.Context, in *GetUserLastLoginInfoReq, opts ...grpc.CallOption) (*UserLoginInfo, error)
	VerifyCAPTCHASuccess(ctx context.Context, in *VerifyCAPTCHASuccessReq, opts ...grpc.CallOption) (*VerifyCAPTCHASuccessResp, error)
	GetLastVerifySuccessInfo(ctx context.Context, in *GetLastVerifySuccessInfoReq, opts ...grpc.CallOption) (*GetLastVerifySuccessInfoResp, error)
	CheckUsualDevice(ctx context.Context, in *CheckUsualDeviceReq, opts ...grpc.CallOption) (*CheckUsualDeviceResp, error)
	CheckUserIsValid(ctx context.Context, in *CheckUserIsValidReq, opts ...grpc.CallOption) (*CheckUserIsValidResp, error)
	GetUserLoginHistory(ctx context.Context, in *GetUserLoginHistoryReq, opts ...grpc.CallOption) (*GetUserLoginHistoryResp, error)
	GetUserUsualDevice(ctx context.Context, in *GetUserUsualDeviceReq, opts ...grpc.CallOption) (*GetUserUsualDeviceResp, error)
	//
	// 2016/10, 2.9.0增加防刷机制
	//
	SetUserProfile(ctx context.Context, in *SetUserProfileReq, opts ...grpc.CallOption) (*SetUserProfileRsp, error)
	GetUserProfile(ctx context.Context, in *GetUserProfileReq, opts ...grpc.CallOption) (*GetUserProfileRsp, error)
	TrackUserLogin(ctx context.Context, in *TrackUserLoginReq, opts ...grpc.CallOption) (*TrackUserLoginRsp, error)
	GetUserLoginDevice(ctx context.Context, in *GetUserLoginDeviceReq, opts ...grpc.CallOption) (*GetUserLoginDeviceRsp, error)
	GetUserLoginWithDevice(ctx context.Context, in *GetUserLoginWithDeviceReq, opts ...grpc.CallOption) (*GetUserLoginWithDeviceRsp, error)
	GetUserLoginWithIP(ctx context.Context, in *GetUserLoginWithIPReq, opts ...grpc.CallOption) (*GetUserLoginWithIPRsp, error)
	GetUserLoginWithImei(ctx context.Context, in *GetUserLoginWithImeiReq, opts ...grpc.CallOption) (*GetUserLoginWithImeiRsp, error)
	GetUserLoginImei(ctx context.Context, in *GetUserLoginImeiReq, opts ...grpc.CallOption) (*GetUserLoginImeiRsp, error)
	BatchSetUserProfile(ctx context.Context, in *BatchSetUserProfileReq, opts ...grpc.CallOption) (*BatchSetUserProfileRsp, error)
	BatchGetUserProfile(ctx context.Context, in *BatchGetUserProfileReq, opts ...grpc.CallOption) (*BatchGetUserProfileRsp, error)
	GetUserLoginIp(ctx context.Context, in *GetUserLoginIpReq, opts ...grpc.CallOption) (*GetUserLoginIpRsp, error)
	GetUserLoginByIdfa(ctx context.Context, in *GetUserLoginByIdfaReq, opts ...grpc.CallOption) (*GetUserLoginByIdfaResp, error)
	RecordIdfa(ctx context.Context, in *RecordIdfaReq, opts ...grpc.CallOption) (*RecordIdfaResp, error)
	GetUserRegInfo(ctx context.Context, in *GetUserRegInfoReq, opts ...grpc.CallOption) (*GetUserRegInfoRsp, error)
	BatchGetUserLastLoginInfo(ctx context.Context, in *BatchGetUserLastLoginInfoReq, opts ...grpc.CallOption) (*BatchGetUserLastLoginInfoResp, error)
	BatchGetUserLoginDevice(ctx context.Context, in *BatchGetUserLoginDeviceReq, opts ...grpc.CallOption) (*BatchGetUserLoginDeviceRsp, error)
	BatchGetUserLoginWithDevice(ctx context.Context, in *BatchGetUserLoginWithDeviceReq, opts ...grpc.CallOption) (*BatchGetUserLoginWithDeviceRsp, error)
	RecordUsualDevice(ctx context.Context, in *RecordUsualDeviceReq, opts ...grpc.CallOption) (*RecordUsualDeviceResp, error)
}

type antiClient struct {
	cc *grpc.ClientConn
}

func NewAntiClient(cc *grpc.ClientConn) AntiClient {
	return &antiClient{cc}
}

func (c *antiClient) RecordUserLogin(ctx context.Context, in *RecordUserLoginReq, opts ...grpc.CallOption) (*RecordUserLoginResp, error) {
	out := new(RecordUserLoginResp)
	err := grpc.Invoke(ctx, "/Anti.Anti/RecordUserLogin", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) GetDeviceIdInfo(ctx context.Context, in *GetDeviceIdInfoReq, opts ...grpc.CallOption) (*GetDeviceIdInfoResp, error) {
	out := new(GetDeviceIdInfoResp)
	err := grpc.Invoke(ctx, "/Anti.Anti/GetDeviceIdInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) GetUserLastLoginInfo(ctx context.Context, in *GetUserLastLoginInfoReq, opts ...grpc.CallOption) (*UserLoginInfo, error) {
	out := new(UserLoginInfo)
	err := grpc.Invoke(ctx, "/Anti.Anti/GetUserLastLoginInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) VerifyCAPTCHASuccess(ctx context.Context, in *VerifyCAPTCHASuccessReq, opts ...grpc.CallOption) (*VerifyCAPTCHASuccessResp, error) {
	out := new(VerifyCAPTCHASuccessResp)
	err := grpc.Invoke(ctx, "/Anti.Anti/VerifyCAPTCHASuccess", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) GetLastVerifySuccessInfo(ctx context.Context, in *GetLastVerifySuccessInfoReq, opts ...grpc.CallOption) (*GetLastVerifySuccessInfoResp, error) {
	out := new(GetLastVerifySuccessInfoResp)
	err := grpc.Invoke(ctx, "/Anti.Anti/GetLastVerifySuccessInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) CheckUsualDevice(ctx context.Context, in *CheckUsualDeviceReq, opts ...grpc.CallOption) (*CheckUsualDeviceResp, error) {
	out := new(CheckUsualDeviceResp)
	err := grpc.Invoke(ctx, "/Anti.Anti/CheckUsualDevice", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) CheckUserIsValid(ctx context.Context, in *CheckUserIsValidReq, opts ...grpc.CallOption) (*CheckUserIsValidResp, error) {
	out := new(CheckUserIsValidResp)
	err := grpc.Invoke(ctx, "/Anti.Anti/CheckUserIsValid", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) GetUserLoginHistory(ctx context.Context, in *GetUserLoginHistoryReq, opts ...grpc.CallOption) (*GetUserLoginHistoryResp, error) {
	out := new(GetUserLoginHistoryResp)
	err := grpc.Invoke(ctx, "/Anti.Anti/GetUserLoginHistory", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) GetUserUsualDevice(ctx context.Context, in *GetUserUsualDeviceReq, opts ...grpc.CallOption) (*GetUserUsualDeviceResp, error) {
	out := new(GetUserUsualDeviceResp)
	err := grpc.Invoke(ctx, "/Anti.Anti/GetUserUsualDevice", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) SetUserProfile(ctx context.Context, in *SetUserProfileReq, opts ...grpc.CallOption) (*SetUserProfileRsp, error) {
	out := new(SetUserProfileRsp)
	err := grpc.Invoke(ctx, "/Anti.Anti/SetUserProfile", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) GetUserProfile(ctx context.Context, in *GetUserProfileReq, opts ...grpc.CallOption) (*GetUserProfileRsp, error) {
	out := new(GetUserProfileRsp)
	err := grpc.Invoke(ctx, "/Anti.Anti/GetUserProfile", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) TrackUserLogin(ctx context.Context, in *TrackUserLoginReq, opts ...grpc.CallOption) (*TrackUserLoginRsp, error) {
	out := new(TrackUserLoginRsp)
	err := grpc.Invoke(ctx, "/Anti.Anti/TrackUserLogin", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) GetUserLoginDevice(ctx context.Context, in *GetUserLoginDeviceReq, opts ...grpc.CallOption) (*GetUserLoginDeviceRsp, error) {
	out := new(GetUserLoginDeviceRsp)
	err := grpc.Invoke(ctx, "/Anti.Anti/GetUserLoginDevice", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) GetUserLoginWithDevice(ctx context.Context, in *GetUserLoginWithDeviceReq, opts ...grpc.CallOption) (*GetUserLoginWithDeviceRsp, error) {
	out := new(GetUserLoginWithDeviceRsp)
	err := grpc.Invoke(ctx, "/Anti.Anti/GetUserLoginWithDevice", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) GetUserLoginWithIP(ctx context.Context, in *GetUserLoginWithIPReq, opts ...grpc.CallOption) (*GetUserLoginWithIPRsp, error) {
	out := new(GetUserLoginWithIPRsp)
	err := grpc.Invoke(ctx, "/Anti.Anti/GetUserLoginWithIP", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) GetUserLoginWithImei(ctx context.Context, in *GetUserLoginWithImeiReq, opts ...grpc.CallOption) (*GetUserLoginWithImeiRsp, error) {
	out := new(GetUserLoginWithImeiRsp)
	err := grpc.Invoke(ctx, "/Anti.Anti/GetUserLoginWithImei", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) GetUserLoginImei(ctx context.Context, in *GetUserLoginImeiReq, opts ...grpc.CallOption) (*GetUserLoginImeiRsp, error) {
	out := new(GetUserLoginImeiRsp)
	err := grpc.Invoke(ctx, "/Anti.Anti/GetUserLoginImei", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) BatchSetUserProfile(ctx context.Context, in *BatchSetUserProfileReq, opts ...grpc.CallOption) (*BatchSetUserProfileRsp, error) {
	out := new(BatchSetUserProfileRsp)
	err := grpc.Invoke(ctx, "/Anti.Anti/BatchSetUserProfile", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) BatchGetUserProfile(ctx context.Context, in *BatchGetUserProfileReq, opts ...grpc.CallOption) (*BatchGetUserProfileRsp, error) {
	out := new(BatchGetUserProfileRsp)
	err := grpc.Invoke(ctx, "/Anti.Anti/BatchGetUserProfile", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) GetUserLoginIp(ctx context.Context, in *GetUserLoginIpReq, opts ...grpc.CallOption) (*GetUserLoginIpRsp, error) {
	out := new(GetUserLoginIpRsp)
	err := grpc.Invoke(ctx, "/Anti.Anti/GetUserLoginIp", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) GetUserLoginByIdfa(ctx context.Context, in *GetUserLoginByIdfaReq, opts ...grpc.CallOption) (*GetUserLoginByIdfaResp, error) {
	out := new(GetUserLoginByIdfaResp)
	err := grpc.Invoke(ctx, "/Anti.Anti/GetUserLoginByIdfa", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) RecordIdfa(ctx context.Context, in *RecordIdfaReq, opts ...grpc.CallOption) (*RecordIdfaResp, error) {
	out := new(RecordIdfaResp)
	err := grpc.Invoke(ctx, "/Anti.Anti/RecordIdfa", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) GetUserRegInfo(ctx context.Context, in *GetUserRegInfoReq, opts ...grpc.CallOption) (*GetUserRegInfoRsp, error) {
	out := new(GetUserRegInfoRsp)
	err := grpc.Invoke(ctx, "/Anti.Anti/GetUserRegInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) BatchGetUserLastLoginInfo(ctx context.Context, in *BatchGetUserLastLoginInfoReq, opts ...grpc.CallOption) (*BatchGetUserLastLoginInfoResp, error) {
	out := new(BatchGetUserLastLoginInfoResp)
	err := grpc.Invoke(ctx, "/Anti.Anti/BatchGetUserLastLoginInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) BatchGetUserLoginDevice(ctx context.Context, in *BatchGetUserLoginDeviceReq, opts ...grpc.CallOption) (*BatchGetUserLoginDeviceRsp, error) {
	out := new(BatchGetUserLoginDeviceRsp)
	err := grpc.Invoke(ctx, "/Anti.Anti/BatchGetUserLoginDevice", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) BatchGetUserLoginWithDevice(ctx context.Context, in *BatchGetUserLoginWithDeviceReq, opts ...grpc.CallOption) (*BatchGetUserLoginWithDeviceRsp, error) {
	out := new(BatchGetUserLoginWithDeviceRsp)
	err := grpc.Invoke(ctx, "/Anti.Anti/BatchGetUserLoginWithDevice", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antiClient) RecordUsualDevice(ctx context.Context, in *RecordUsualDeviceReq, opts ...grpc.CallOption) (*RecordUsualDeviceResp, error) {
	out := new(RecordUsualDeviceResp)
	err := grpc.Invoke(ctx, "/Anti.Anti/RecordUsualDevice", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Anti service

type AntiServer interface {
	RecordUserLogin(context.Context, *RecordUserLoginReq) (*RecordUserLoginResp, error)
	GetDeviceIdInfo(context.Context, *GetDeviceIdInfoReq) (*GetDeviceIdInfoResp, error)
	GetUserLastLoginInfo(context.Context, *GetUserLastLoginInfoReq) (*UserLoginInfo, error)
	VerifyCAPTCHASuccess(context.Context, *VerifyCAPTCHASuccessReq) (*VerifyCAPTCHASuccessResp, error)
	GetLastVerifySuccessInfo(context.Context, *GetLastVerifySuccessInfoReq) (*GetLastVerifySuccessInfoResp, error)
	CheckUsualDevice(context.Context, *CheckUsualDeviceReq) (*CheckUsualDeviceResp, error)
	CheckUserIsValid(context.Context, *CheckUserIsValidReq) (*CheckUserIsValidResp, error)
	GetUserLoginHistory(context.Context, *GetUserLoginHistoryReq) (*GetUserLoginHistoryResp, error)
	GetUserUsualDevice(context.Context, *GetUserUsualDeviceReq) (*GetUserUsualDeviceResp, error)
	//
	// 2016/10, 2.9.0增加防刷机制
	//
	SetUserProfile(context.Context, *SetUserProfileReq) (*SetUserProfileRsp, error)
	GetUserProfile(context.Context, *GetUserProfileReq) (*GetUserProfileRsp, error)
	TrackUserLogin(context.Context, *TrackUserLoginReq) (*TrackUserLoginRsp, error)
	GetUserLoginDevice(context.Context, *GetUserLoginDeviceReq) (*GetUserLoginDeviceRsp, error)
	GetUserLoginWithDevice(context.Context, *GetUserLoginWithDeviceReq) (*GetUserLoginWithDeviceRsp, error)
	GetUserLoginWithIP(context.Context, *GetUserLoginWithIPReq) (*GetUserLoginWithIPRsp, error)
	GetUserLoginWithImei(context.Context, *GetUserLoginWithImeiReq) (*GetUserLoginWithImeiRsp, error)
	GetUserLoginImei(context.Context, *GetUserLoginImeiReq) (*GetUserLoginImeiRsp, error)
	BatchSetUserProfile(context.Context, *BatchSetUserProfileReq) (*BatchSetUserProfileRsp, error)
	BatchGetUserProfile(context.Context, *BatchGetUserProfileReq) (*BatchGetUserProfileRsp, error)
	GetUserLoginIp(context.Context, *GetUserLoginIpReq) (*GetUserLoginIpRsp, error)
	GetUserLoginByIdfa(context.Context, *GetUserLoginByIdfaReq) (*GetUserLoginByIdfaResp, error)
	RecordIdfa(context.Context, *RecordIdfaReq) (*RecordIdfaResp, error)
	GetUserRegInfo(context.Context, *GetUserRegInfoReq) (*GetUserRegInfoRsp, error)
	BatchGetUserLastLoginInfo(context.Context, *BatchGetUserLastLoginInfoReq) (*BatchGetUserLastLoginInfoResp, error)
	BatchGetUserLoginDevice(context.Context, *BatchGetUserLoginDeviceReq) (*BatchGetUserLoginDeviceRsp, error)
	BatchGetUserLoginWithDevice(context.Context, *BatchGetUserLoginWithDeviceReq) (*BatchGetUserLoginWithDeviceRsp, error)
	RecordUsualDevice(context.Context, *RecordUsualDeviceReq) (*RecordUsualDeviceResp, error)
}

func RegisterAntiServer(s *grpc.Server, srv AntiServer) {
	s.RegisterService(&_Anti_serviceDesc, srv)
}

func _Anti_RecordUserLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordUserLoginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).RecordUserLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/RecordUserLogin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).RecordUserLogin(ctx, req.(*RecordUserLoginReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_GetDeviceIdInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeviceIdInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).GetDeviceIdInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/GetDeviceIdInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).GetDeviceIdInfo(ctx, req.(*GetDeviceIdInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_GetUserLastLoginInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLastLoginInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).GetUserLastLoginInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/GetUserLastLoginInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).GetUserLastLoginInfo(ctx, req.(*GetUserLastLoginInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_VerifyCAPTCHASuccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyCAPTCHASuccessReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).VerifyCAPTCHASuccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/VerifyCAPTCHASuccess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).VerifyCAPTCHASuccess(ctx, req.(*VerifyCAPTCHASuccessReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_GetLastVerifySuccessInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastVerifySuccessInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).GetLastVerifySuccessInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/GetLastVerifySuccessInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).GetLastVerifySuccessInfo(ctx, req.(*GetLastVerifySuccessInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_CheckUsualDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUsualDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).CheckUsualDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/CheckUsualDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).CheckUsualDevice(ctx, req.(*CheckUsualDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_CheckUserIsValid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserIsValidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).CheckUserIsValid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/CheckUserIsValid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).CheckUserIsValid(ctx, req.(*CheckUserIsValidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_GetUserLoginHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLoginHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).GetUserLoginHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/GetUserLoginHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).GetUserLoginHistory(ctx, req.(*GetUserLoginHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_GetUserUsualDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserUsualDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).GetUserUsualDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/GetUserUsualDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).GetUserUsualDevice(ctx, req.(*GetUserUsualDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_SetUserProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserProfileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).SetUserProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/SetUserProfile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).SetUserProfile(ctx, req.(*SetUserProfileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_GetUserProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserProfileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).GetUserProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/GetUserProfile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).GetUserProfile(ctx, req.(*GetUserProfileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_TrackUserLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TrackUserLoginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).TrackUserLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/TrackUserLogin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).TrackUserLogin(ctx, req.(*TrackUserLoginReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_GetUserLoginDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLoginDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).GetUserLoginDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/GetUserLoginDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).GetUserLoginDevice(ctx, req.(*GetUserLoginDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_GetUserLoginWithDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLoginWithDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).GetUserLoginWithDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/GetUserLoginWithDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).GetUserLoginWithDevice(ctx, req.(*GetUserLoginWithDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_GetUserLoginWithIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLoginWithIPReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).GetUserLoginWithIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/GetUserLoginWithIP",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).GetUserLoginWithIP(ctx, req.(*GetUserLoginWithIPReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_GetUserLoginWithImei_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLoginWithImeiReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).GetUserLoginWithImei(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/GetUserLoginWithImei",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).GetUserLoginWithImei(ctx, req.(*GetUserLoginWithImeiReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_GetUserLoginImei_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLoginImeiReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).GetUserLoginImei(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/GetUserLoginImei",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).GetUserLoginImei(ctx, req.(*GetUserLoginImeiReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_BatchSetUserProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetUserProfileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).BatchSetUserProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/BatchSetUserProfile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).BatchSetUserProfile(ctx, req.(*BatchSetUserProfileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_BatchGetUserProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserProfileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).BatchGetUserProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/BatchGetUserProfile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).BatchGetUserProfile(ctx, req.(*BatchGetUserProfileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_GetUserLoginIp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLoginIpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).GetUserLoginIp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/GetUserLoginIp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).GetUserLoginIp(ctx, req.(*GetUserLoginIpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_GetUserLoginByIdfa_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLoginByIdfaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).GetUserLoginByIdfa(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/GetUserLoginByIdfa",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).GetUserLoginByIdfa(ctx, req.(*GetUserLoginByIdfaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_RecordIdfa_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordIdfaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).RecordIdfa(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/RecordIdfa",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).RecordIdfa(ctx, req.(*RecordIdfaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_GetUserRegInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRegInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).GetUserRegInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/GetUserRegInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).GetUserRegInfo(ctx, req.(*GetUserRegInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_BatchGetUserLastLoginInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserLastLoginInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).BatchGetUserLastLoginInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/BatchGetUserLastLoginInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).BatchGetUserLastLoginInfo(ctx, req.(*BatchGetUserLastLoginInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_BatchGetUserLoginDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserLoginDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).BatchGetUserLoginDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/BatchGetUserLoginDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).BatchGetUserLoginDevice(ctx, req.(*BatchGetUserLoginDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_BatchGetUserLoginWithDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserLoginWithDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).BatchGetUserLoginWithDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/BatchGetUserLoginWithDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).BatchGetUserLoginWithDevice(ctx, req.(*BatchGetUserLoginWithDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Anti_RecordUsualDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordUsualDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntiServer).RecordUsualDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Anti.Anti/RecordUsualDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntiServer).RecordUsualDevice(ctx, req.(*RecordUsualDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Anti_serviceDesc = grpc.ServiceDesc{
	ServiceName: "Anti.Anti",
	HandlerType: (*AntiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RecordUserLogin",
			Handler:    _Anti_RecordUserLogin_Handler,
		},
		{
			MethodName: "GetDeviceIdInfo",
			Handler:    _Anti_GetDeviceIdInfo_Handler,
		},
		{
			MethodName: "GetUserLastLoginInfo",
			Handler:    _Anti_GetUserLastLoginInfo_Handler,
		},
		{
			MethodName: "VerifyCAPTCHASuccess",
			Handler:    _Anti_VerifyCAPTCHASuccess_Handler,
		},
		{
			MethodName: "GetLastVerifySuccessInfo",
			Handler:    _Anti_GetLastVerifySuccessInfo_Handler,
		},
		{
			MethodName: "CheckUsualDevice",
			Handler:    _Anti_CheckUsualDevice_Handler,
		},
		{
			MethodName: "CheckUserIsValid",
			Handler:    _Anti_CheckUserIsValid_Handler,
		},
		{
			MethodName: "GetUserLoginHistory",
			Handler:    _Anti_GetUserLoginHistory_Handler,
		},
		{
			MethodName: "GetUserUsualDevice",
			Handler:    _Anti_GetUserUsualDevice_Handler,
		},
		{
			MethodName: "SetUserProfile",
			Handler:    _Anti_SetUserProfile_Handler,
		},
		{
			MethodName: "GetUserProfile",
			Handler:    _Anti_GetUserProfile_Handler,
		},
		{
			MethodName: "TrackUserLogin",
			Handler:    _Anti_TrackUserLogin_Handler,
		},
		{
			MethodName: "GetUserLoginDevice",
			Handler:    _Anti_GetUserLoginDevice_Handler,
		},
		{
			MethodName: "GetUserLoginWithDevice",
			Handler:    _Anti_GetUserLoginWithDevice_Handler,
		},
		{
			MethodName: "GetUserLoginWithIP",
			Handler:    _Anti_GetUserLoginWithIP_Handler,
		},
		{
			MethodName: "GetUserLoginWithImei",
			Handler:    _Anti_GetUserLoginWithImei_Handler,
		},
		{
			MethodName: "GetUserLoginImei",
			Handler:    _Anti_GetUserLoginImei_Handler,
		},
		{
			MethodName: "BatchSetUserProfile",
			Handler:    _Anti_BatchSetUserProfile_Handler,
		},
		{
			MethodName: "BatchGetUserProfile",
			Handler:    _Anti_BatchGetUserProfile_Handler,
		},
		{
			MethodName: "GetUserLoginIp",
			Handler:    _Anti_GetUserLoginIp_Handler,
		},
		{
			MethodName: "GetUserLoginByIdfa",
			Handler:    _Anti_GetUserLoginByIdfa_Handler,
		},
		{
			MethodName: "RecordIdfa",
			Handler:    _Anti_RecordIdfa_Handler,
		},
		{
			MethodName: "GetUserRegInfo",
			Handler:    _Anti_GetUserRegInfo_Handler,
		},
		{
			MethodName: "BatchGetUserLastLoginInfo",
			Handler:    _Anti_BatchGetUserLastLoginInfo_Handler,
		},
		{
			MethodName: "BatchGetUserLoginDevice",
			Handler:    _Anti_BatchGetUserLoginDevice_Handler,
		},
		{
			MethodName: "BatchGetUserLoginWithDevice",
			Handler:    _Anti_BatchGetUserLoginWithDevice_Handler,
		},
		{
			MethodName: "RecordUsualDevice",
			Handler:    _Anti_RecordUsualDevice_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/antisvr/anti.proto",
}

func (m *UserLoginInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserLoginInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.OpType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Result))
	dAtA[i] = 0x22
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x28
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.ThirdPartyType))
	dAtA[i] = 0x32
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Openid)))
	i += copy(dAtA[i:], m.Openid)
	dAtA[i] = 0x52
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Imei)))
	i += copy(dAtA[i:], m.Imei)
	dAtA[i] = 0x5a
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.OsVer)))
	i += copy(dAtA[i:], m.OsVer)
	dAtA[i] = 0x62
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.OsType)))
	i += copy(dAtA[i:], m.OsType)
	dAtA[i] = 0x6a
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.DeviceModel)))
	i += copy(dAtA[i:], m.DeviceModel)
	dAtA[i] = 0x72
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Signature)))
	i += copy(dAtA[i:], m.Signature)
	dAtA[i] = 0x7a
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.DeviceInfo)))
	i += copy(dAtA[i:], m.DeviceInfo)
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.IsEmulator))
	dAtA[i] = 0x8a
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x90
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.ClientVer))
	dAtA[i] = 0x9a
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.ClientIp)))
	i += copy(dAtA[i:], m.ClientIp)
	dAtA[i] = 0xa2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.ClientChannelId)))
	i += copy(dAtA[i:], m.ClientChannelId)
	dAtA[i] = 0xa8
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.ClientType))
	dAtA[i] = 0xb0
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.TerminalType))
	dAtA[i] = 0xb8
	i++
	dAtA[i] = 0x1
	i++
	if m.IsQuickLoginFromSdk {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0xc2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.LoginTime)))
	i += copy(dAtA[i:], m.LoginTime)
	dAtA[i] = 0xc8
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.ClientPort))
	dAtA[i] = 0xd2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Idfa)))
	i += copy(dAtA[i:], m.Idfa)
	dAtA[i] = 0xda
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.LoginAccount)))
	i += copy(dAtA[i:], m.LoginAccount)
	dAtA[i] = 0xe2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Username)))
	i += copy(dAtA[i:], m.Username)
	dAtA[i] = 0xea
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Alias)))
	i += copy(dAtA[i:], m.Alias)
	dAtA[i] = 0xf2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0xfa
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.UserSignature)))
	i += copy(dAtA[i:], m.UserSignature)
	dAtA[i] = 0x82
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.ProxyIp)))
	i += copy(dAtA[i:], m.ProxyIp)
	dAtA[i] = 0x88
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.ProxyPort))
	dAtA[i] = 0x90
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.ClientId))
	return i, nil
}

func (m *RecordUserLoginReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordUserLoginReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Info == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAnti(dAtA, i, uint64(m.Info.Size()))
		n1, err := m.Info.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Invalid))
	return i, nil
}

func (m *RecordUserLoginResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordUserLoginResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsNewUsualDevice {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *RegUserInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegUserInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.RegAt)))
	i += copy(dAtA[i:], m.RegAt)
	return i, nil
}

func (m *GetDeviceIdInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDeviceIdInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	return i, nil
}

func (m *GetDeviceIdInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDeviceIdInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.RegUserCount))
	if len(m.RegInfoList) > 0 {
		for _, msg := range m.RegInfoList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintAnti(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserLastLoginInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLastLoginInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *BatchGetUserLastLoginInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserLastLoginInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintAnti(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchGetUserLastLoginInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserLastLoginInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, msg := range m.InfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAnti(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *LoginDeviceInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LoginDeviceInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x10
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.LastLoginTime))
	return i, nil
}

func (m *RecentLoginDeviceList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecentLoginDeviceList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	if len(m.DeviceList) > 0 {
		for _, msg := range m.DeviceList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintAnti(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *LoginUserInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LoginUserInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.LastLoginTime))
	return i, nil
}

func (m *RecentLoginUserList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecentLoginUserList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	if len(m.UserList) > 0 {
		for _, msg := range m.UserList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintAnti(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *VerifyCAPTCHASuccessReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyCAPTCHASuccessReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.VerifyReason))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	return i, nil
}

func (m *VerifyCAPTCHASuccessResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyCAPTCHASuccessResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetLastVerifySuccessInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLastVerifySuccessInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.VerifyReason))
	return i, nil
}

func (m *GetLastVerifySuccessInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLastVerifySuccessInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x10
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Timestamp))
	return i, nil
}

func (m *CheckUsualDeviceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUsualDeviceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x18
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Type))
	return i, nil
}

func (m *CheckUsualDeviceResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUsualDeviceResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsUsualDevice {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *RecordUsualDeviceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordUsualDeviceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	return i, nil
}

func (m *RecordUsualDeviceResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordUsualDeviceResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *CheckUserIsValidReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserIsValidReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *CheckUserIsValidResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserIsValidResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsValid {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetUserLoginHistoryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLoginHistoryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.EndTime))
	return i, nil
}

func (m *GetUserLoginHistoryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLoginHistoryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LoginHistory) > 0 {
		for _, msg := range m.LoginHistory {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAnti(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetUserProfileReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserProfileReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Profile))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.ReasonCode))
	dAtA[i] = 0x22
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Reason)))
	i += copy(dAtA[i:], m.Reason)
	return i, nil
}

func (m *SetUserProfileRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserProfileRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetUserProfileReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserProfileReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	if m.WithFootmark {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	if m.WithDetail {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *UserProfileDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserProfileDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Profile))
	dAtA[i] = 0x12
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.MarkTime)))
	i += copy(dAtA[i:], m.MarkTime)
	dAtA[i] = 0x18
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.ReasonCode))
	dAtA[i] = 0x22
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Reason)))
	i += copy(dAtA[i:], m.Reason)
	dAtA[i] = 0x28
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *GetUserProfileRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserProfileRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Profile))
	if len(m.ProfileList) > 0 {
		for _, msg := range m.ProfileList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintAnti(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.ReasonCode))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Reason)))
	i += copy(dAtA[i:], m.Reason)
	return i, nil
}

func (m *TrackUserLoginReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TrackUserLoginReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.LoginAt))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x22
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Imei)))
	i += copy(dAtA[i:], m.Imei)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	return i, nil
}

func (m *TrackUserLoginRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TrackUserLoginRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetUserLoginDeviceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLoginDeviceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *UserLoginDevice) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserLoginDevice) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x10
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.LoginAt))
	return i, nil
}

func (m *GetUserLoginDeviceRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLoginDeviceRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	if len(m.LoginDevices) > 0 {
		for _, msg := range m.LoginDevices {
			dAtA[i] = 0x12
			i++
			i = encodeVarintAnti(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UserLoginHit) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserLoginHit) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.LoginAt))
	return i, nil
}

func (m *GetUserLoginWithDeviceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLoginWithDeviceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	return i, nil
}

func (m *GetUserLoginWithDeviceRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLoginWithDeviceRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	if len(m.LoginUsers) > 0 {
		for _, msg := range m.LoginUsers {
			dAtA[i] = 0x12
			i++
			i = encodeVarintAnti(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserUsualDeviceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserUsualDeviceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GetUserUsualDeviceResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserUsualDeviceResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	if len(m.LoginDevices) > 0 {
		for _, msg := range m.LoginDevices {
			dAtA[i] = 0x12
			i++
			i = encodeVarintAnti(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserLoginIpReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLoginIpReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *UserLoginIp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserLoginIp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	dAtA[i] = 0x10
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.LoginAt))
	return i, nil
}

func (m *GetUserLoginIpRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLoginIpRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	if len(m.LoginIps) > 0 {
		for _, msg := range m.LoginIps {
			dAtA[i] = 0x12
			i++
			i = encodeVarintAnti(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserLoginWithIPReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLoginWithIPReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	return i, nil
}

func (m *GetUserLoginWithIPRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLoginWithIPRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	if len(m.LoginUsers) > 0 {
		for _, msg := range m.LoginUsers {
			dAtA[i] = 0x12
			i++
			i = encodeVarintAnti(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserLoginWithImeiReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLoginWithImeiReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Imei)))
	i += copy(dAtA[i:], m.Imei)
	return i, nil
}

func (m *GetUserLoginWithImeiRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLoginWithImeiRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Imei)))
	i += copy(dAtA[i:], m.Imei)
	if len(m.LoginUsers) > 0 {
		for _, msg := range m.LoginUsers {
			dAtA[i] = 0x12
			i++
			i = encodeVarintAnti(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserLoginImeiReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLoginImeiReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *UserLoginImei) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserLoginImei) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Imei)))
	i += copy(dAtA[i:], m.Imei)
	dAtA[i] = 0x10
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.LoginAt))
	return i, nil
}

func (m *GetUserLoginImeiRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLoginImeiRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	if len(m.LoginImeis) > 0 {
		for _, msg := range m.LoginImeis {
			dAtA[i] = 0x12
			i++
			i = encodeVarintAnti(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchSetUserProfileReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchSetUserProfileReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintAnti(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Profile))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.ReasonCode))
	dAtA[i] = 0x22
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Reason)))
	i += copy(dAtA[i:], m.Reason)
	return i, nil
}

func (m *BatchSetUserProfileRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchSetUserProfileRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *BatchGetUserProfileReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserProfileReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintAnti(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	if m.WithFootmark {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *BatchGetUserProfileRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserProfileRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UserProfileList) > 0 {
		for _, msg := range m.UserProfileList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintAnti(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserLoginByIdfaReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLoginByIdfaReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Idfa)))
	i += copy(dAtA[i:], m.Idfa)
	return i, nil
}

func (m *UserLoginWithIdfa) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserLoginWithIdfa) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Idfa)))
	i += copy(dAtA[i:], m.Idfa)
	dAtA[i] = 0x10
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.At))
	dAtA[i] = 0x20
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.OpType))
	return i, nil
}

func (m *GetUserLoginByIdfaResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLoginByIdfaResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Rec != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAnti(dAtA, i, uint64(m.Rec.Size()))
		n2, err := m.Rec.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *RecordIdfaReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordIdfaReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.OpType))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.Idfa)))
	i += copy(dAtA[i:], m.Idfa)
	dAtA[i] = 0x20
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.At))
	return i, nil
}

func (m *RecordIdfaResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordIdfaResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetUserRegInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserRegInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetUserRegInfoRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserRegInfoRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Info != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAnti(dAtA, i, uint64(m.Info.Size()))
		n3, err := m.Info.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *BatchGetUserLoginDeviceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserLoginDeviceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Uids) > 0 {
		for _, num := range m.Uids {
			dAtA[i] = 0x8
			i++
			i = encodeVarintAnti(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchUserLoginDevice) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchUserLoginDevice) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintAnti(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x18
	i++
	i = encodeVarintAnti(dAtA, i, uint64(m.LoginAt))
	return i, nil
}

func (m *BatchGetUserLoginDeviceRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserLoginDeviceRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LoginDevices) > 0 {
		for _, msg := range m.LoginDevices {
			dAtA[i] = 0x12
			i++
			i = encodeVarintAnti(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchGetUserLoginWithDeviceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserLoginWithDeviceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.DeviceIds) > 0 {
		for _, s := range m.DeviceIds {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *BatchGetUserLoginWithDeviceRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserLoginWithDeviceRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LoginUsers) > 0 {
		for _, msg := range m.LoginUsers {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAnti(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func encodeFixed64Anti(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Anti(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintAnti(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *UserLoginInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	n += 1 + sovAnti(uint64(m.OpType))
	n += 1 + sovAnti(uint64(m.Result))
	l = len(m.Phone)
	n += 1 + l + sovAnti(uint64(l))
	n += 1 + sovAnti(uint64(m.ThirdPartyType))
	l = len(m.Openid)
	n += 1 + l + sovAnti(uint64(l))
	l = len(m.Imei)
	n += 1 + l + sovAnti(uint64(l))
	l = len(m.OsVer)
	n += 1 + l + sovAnti(uint64(l))
	l = len(m.OsType)
	n += 1 + l + sovAnti(uint64(l))
	l = len(m.DeviceModel)
	n += 1 + l + sovAnti(uint64(l))
	l = len(m.Signature)
	n += 1 + l + sovAnti(uint64(l))
	l = len(m.DeviceInfo)
	n += 1 + l + sovAnti(uint64(l))
	n += 2 + sovAnti(uint64(m.IsEmulator))
	l = len(m.DeviceId)
	n += 2 + l + sovAnti(uint64(l))
	n += 2 + sovAnti(uint64(m.ClientVer))
	l = len(m.ClientIp)
	n += 2 + l + sovAnti(uint64(l))
	l = len(m.ClientChannelId)
	n += 2 + l + sovAnti(uint64(l))
	n += 2 + sovAnti(uint64(m.ClientType))
	n += 2 + sovAnti(uint64(m.TerminalType))
	n += 3
	l = len(m.LoginTime)
	n += 2 + l + sovAnti(uint64(l))
	n += 2 + sovAnti(uint64(m.ClientPort))
	l = len(m.Idfa)
	n += 2 + l + sovAnti(uint64(l))
	l = len(m.LoginAccount)
	n += 2 + l + sovAnti(uint64(l))
	l = len(m.Username)
	n += 2 + l + sovAnti(uint64(l))
	l = len(m.Alias)
	n += 2 + l + sovAnti(uint64(l))
	l = len(m.Nickname)
	n += 2 + l + sovAnti(uint64(l))
	l = len(m.UserSignature)
	n += 2 + l + sovAnti(uint64(l))
	l = len(m.ProxyIp)
	n += 2 + l + sovAnti(uint64(l))
	n += 2 + sovAnti(uint64(m.ProxyPort))
	n += 2 + sovAnti(uint64(m.ClientId))
	return n
}

func (m *RecordUserLoginReq) Size() (n int) {
	var l int
	_ = l
	if m.Info != nil {
		l = m.Info.Size()
		n += 1 + l + sovAnti(uint64(l))
	}
	n += 1 + sovAnti(uint64(m.Invalid))
	return n
}

func (m *RecordUserLoginResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *RegUserInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	l = len(m.Phone)
	n += 1 + l + sovAnti(uint64(l))
	l = len(m.RegAt)
	n += 1 + l + sovAnti(uint64(l))
	return n
}

func (m *GetDeviceIdInfoReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.DeviceId)
	n += 1 + l + sovAnti(uint64(l))
	return n
}

func (m *GetDeviceIdInfoResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.RegUserCount))
	if len(m.RegInfoList) > 0 {
		for _, e := range m.RegInfoList {
			l = e.Size()
			n += 1 + l + sovAnti(uint64(l))
		}
	}
	return n
}

func (m *GetUserLastLoginInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	return n
}

func (m *BatchGetUserLastLoginInfoReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovAnti(uint64(e))
		}
	}
	return n
}

func (m *BatchGetUserLastLoginInfoResp) Size() (n int) {
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, e := range m.InfoList {
			l = e.Size()
			n += 1 + l + sovAnti(uint64(l))
		}
	}
	return n
}

func (m *LoginDeviceInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.DeviceId)
	n += 1 + l + sovAnti(uint64(l))
	n += 1 + sovAnti(uint64(m.LastLoginTime))
	return n
}

func (m *RecentLoginDeviceList) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	if len(m.DeviceList) > 0 {
		for _, e := range m.DeviceList {
			l = e.Size()
			n += 1 + l + sovAnti(uint64(l))
		}
	}
	return n
}

func (m *LoginUserInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	n += 1 + sovAnti(uint64(m.LastLoginTime))
	return n
}

func (m *RecentLoginUserList) Size() (n int) {
	var l int
	_ = l
	l = len(m.DeviceId)
	n += 1 + l + sovAnti(uint64(l))
	if len(m.UserList) > 0 {
		for _, e := range m.UserList {
			l = e.Size()
			n += 1 + l + sovAnti(uint64(l))
		}
	}
	return n
}

func (m *VerifyCAPTCHASuccessReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	n += 1 + sovAnti(uint64(m.VerifyReason))
	l = len(m.DeviceId)
	n += 1 + l + sovAnti(uint64(l))
	return n
}

func (m *VerifyCAPTCHASuccessResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetLastVerifySuccessInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	n += 1 + sovAnti(uint64(m.VerifyReason))
	return n
}

func (m *GetLastVerifySuccessInfoResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.DeviceId)
	n += 1 + l + sovAnti(uint64(l))
	n += 1 + sovAnti(uint64(m.Timestamp))
	return n
}

func (m *CheckUsualDeviceReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	l = len(m.DeviceId)
	n += 1 + l + sovAnti(uint64(l))
	n += 1 + sovAnti(uint64(m.Type))
	return n
}

func (m *CheckUsualDeviceResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *RecordUsualDeviceReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	l = len(m.DeviceId)
	n += 1 + l + sovAnti(uint64(l))
	return n
}

func (m *RecordUsualDeviceResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *CheckUserIsValidReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	return n
}

func (m *CheckUserIsValidResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *GetUserLoginHistoryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	n += 1 + sovAnti(uint64(m.BeginTime))
	n += 1 + sovAnti(uint64(m.EndTime))
	return n
}

func (m *GetUserLoginHistoryResp) Size() (n int) {
	var l int
	_ = l
	if len(m.LoginHistory) > 0 {
		for _, e := range m.LoginHistory {
			l = e.Size()
			n += 1 + l + sovAnti(uint64(l))
		}
	}
	return n
}

func (m *SetUserProfileReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	n += 1 + sovAnti(uint64(m.Profile))
	n += 1 + sovAnti(uint64(m.ReasonCode))
	l = len(m.Reason)
	n += 1 + l + sovAnti(uint64(l))
	return n
}

func (m *SetUserProfileRsp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetUserProfileReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	n += 2
	n += 2
	return n
}

func (m *UserProfileDetail) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Profile))
	l = len(m.MarkTime)
	n += 1 + l + sovAnti(uint64(l))
	n += 1 + sovAnti(uint64(m.ReasonCode))
	l = len(m.Reason)
	n += 1 + l + sovAnti(uint64(l))
	n += 1 + sovAnti(uint64(m.Status))
	return n
}

func (m *GetUserProfileRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	n += 1 + sovAnti(uint64(m.Profile))
	if len(m.ProfileList) > 0 {
		for _, e := range m.ProfileList {
			l = e.Size()
			n += 1 + l + sovAnti(uint64(l))
		}
	}
	n += 1 + sovAnti(uint64(m.ReasonCode))
	l = len(m.Reason)
	n += 1 + l + sovAnti(uint64(l))
	return n
}

func (m *TrackUserLoginReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	n += 1 + sovAnti(uint64(m.LoginAt))
	l = len(m.DeviceId)
	n += 1 + l + sovAnti(uint64(l))
	l = len(m.Imei)
	n += 1 + l + sovAnti(uint64(l))
	l = len(m.Ip)
	n += 1 + l + sovAnti(uint64(l))
	return n
}

func (m *TrackUserLoginRsp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetUserLoginDeviceReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	return n
}

func (m *UserLoginDevice) Size() (n int) {
	var l int
	_ = l
	l = len(m.DeviceId)
	n += 1 + l + sovAnti(uint64(l))
	n += 1 + sovAnti(uint64(m.LoginAt))
	return n
}

func (m *GetUserLoginDeviceRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	if len(m.LoginDevices) > 0 {
		for _, e := range m.LoginDevices {
			l = e.Size()
			n += 1 + l + sovAnti(uint64(l))
		}
	}
	return n
}

func (m *UserLoginHit) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	n += 1 + sovAnti(uint64(m.LoginAt))
	return n
}

func (m *GetUserLoginWithDeviceReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.DeviceId)
	n += 1 + l + sovAnti(uint64(l))
	return n
}

func (m *GetUserLoginWithDeviceRsp) Size() (n int) {
	var l int
	_ = l
	l = len(m.DeviceId)
	n += 1 + l + sovAnti(uint64(l))
	if len(m.LoginUsers) > 0 {
		for _, e := range m.LoginUsers {
			l = e.Size()
			n += 1 + l + sovAnti(uint64(l))
		}
	}
	return n
}

func (m *GetUserUsualDeviceReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	n += 1 + sovAnti(uint64(m.BeginTime))
	n += 1 + sovAnti(uint64(m.EndTime))
	n += 1 + sovAnti(uint64(m.Limit))
	return n
}

func (m *GetUserUsualDeviceResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	if len(m.LoginDevices) > 0 {
		for _, e := range m.LoginDevices {
			l = e.Size()
			n += 1 + l + sovAnti(uint64(l))
		}
	}
	return n
}

func (m *GetUserLoginIpReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	return n
}

func (m *UserLoginIp) Size() (n int) {
	var l int
	_ = l
	l = len(m.Ip)
	n += 1 + l + sovAnti(uint64(l))
	n += 1 + sovAnti(uint64(m.LoginAt))
	return n
}

func (m *GetUserLoginIpRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	if len(m.LoginIps) > 0 {
		for _, e := range m.LoginIps {
			l = e.Size()
			n += 1 + l + sovAnti(uint64(l))
		}
	}
	return n
}

func (m *GetUserLoginWithIPReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Ip)
	n += 1 + l + sovAnti(uint64(l))
	return n
}

func (m *GetUserLoginWithIPRsp) Size() (n int) {
	var l int
	_ = l
	l = len(m.Ip)
	n += 1 + l + sovAnti(uint64(l))
	if len(m.LoginUsers) > 0 {
		for _, e := range m.LoginUsers {
			l = e.Size()
			n += 1 + l + sovAnti(uint64(l))
		}
	}
	return n
}

func (m *GetUserLoginWithImeiReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Imei)
	n += 1 + l + sovAnti(uint64(l))
	return n
}

func (m *GetUserLoginWithImeiRsp) Size() (n int) {
	var l int
	_ = l
	l = len(m.Imei)
	n += 1 + l + sovAnti(uint64(l))
	if len(m.LoginUsers) > 0 {
		for _, e := range m.LoginUsers {
			l = e.Size()
			n += 1 + l + sovAnti(uint64(l))
		}
	}
	return n
}

func (m *GetUserLoginImeiReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	return n
}

func (m *UserLoginImei) Size() (n int) {
	var l int
	_ = l
	l = len(m.Imei)
	n += 1 + l + sovAnti(uint64(l))
	n += 1 + sovAnti(uint64(m.LoginAt))
	return n
}

func (m *GetUserLoginImeiRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	if len(m.LoginImeis) > 0 {
		for _, e := range m.LoginImeis {
			l = e.Size()
			n += 1 + l + sovAnti(uint64(l))
		}
	}
	return n
}

func (m *BatchSetUserProfileReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovAnti(uint64(e))
		}
	}
	n += 1 + sovAnti(uint64(m.Profile))
	n += 1 + sovAnti(uint64(m.ReasonCode))
	l = len(m.Reason)
	n += 1 + l + sovAnti(uint64(l))
	return n
}

func (m *BatchSetUserProfileRsp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *BatchGetUserProfileReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovAnti(uint64(e))
		}
	}
	n += 2
	return n
}

func (m *BatchGetUserProfileRsp) Size() (n int) {
	var l int
	_ = l
	if len(m.UserProfileList) > 0 {
		for _, e := range m.UserProfileList {
			l = e.Size()
			n += 1 + l + sovAnti(uint64(l))
		}
	}
	return n
}

func (m *GetUserLoginByIdfaReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Idfa)
	n += 1 + l + sovAnti(uint64(l))
	return n
}

func (m *UserLoginWithIdfa) Size() (n int) {
	var l int
	_ = l
	l = len(m.Idfa)
	n += 1 + l + sovAnti(uint64(l))
	n += 1 + sovAnti(uint64(m.Uid))
	n += 1 + sovAnti(uint64(m.At))
	n += 1 + sovAnti(uint64(m.OpType))
	return n
}

func (m *GetUserLoginByIdfaResp) Size() (n int) {
	var l int
	_ = l
	if m.Rec != nil {
		l = m.Rec.Size()
		n += 1 + l + sovAnti(uint64(l))
	}
	return n
}

func (m *RecordIdfaReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	n += 1 + sovAnti(uint64(m.OpType))
	l = len(m.Idfa)
	n += 1 + l + sovAnti(uint64(l))
	n += 1 + sovAnti(uint64(m.At))
	return n
}

func (m *RecordIdfaResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetUserRegInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	return n
}

func (m *GetUserRegInfoRsp) Size() (n int) {
	var l int
	_ = l
	if m.Info != nil {
		l = m.Info.Size()
		n += 1 + l + sovAnti(uint64(l))
	}
	return n
}

func (m *BatchGetUserLoginDeviceReq) Size() (n int) {
	var l int
	_ = l
	if len(m.Uids) > 0 {
		for _, e := range m.Uids {
			n += 1 + sovAnti(uint64(e))
		}
	}
	return n
}

func (m *BatchUserLoginDevice) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAnti(uint64(m.Uid))
	l = len(m.DeviceId)
	n += 1 + l + sovAnti(uint64(l))
	n += 1 + sovAnti(uint64(m.LoginAt))
	return n
}

func (m *BatchGetUserLoginDeviceRsp) Size() (n int) {
	var l int
	_ = l
	if len(m.LoginDevices) > 0 {
		for _, e := range m.LoginDevices {
			l = e.Size()
			n += 1 + l + sovAnti(uint64(l))
		}
	}
	return n
}

func (m *BatchGetUserLoginWithDeviceReq) Size() (n int) {
	var l int
	_ = l
	if len(m.DeviceIds) > 0 {
		for _, s := range m.DeviceIds {
			l = len(s)
			n += 1 + l + sovAnti(uint64(l))
		}
	}
	return n
}

func (m *BatchGetUserLoginWithDeviceRsp) Size() (n int) {
	var l int
	_ = l
	if len(m.LoginUsers) > 0 {
		for _, e := range m.LoginUsers {
			l = e.Size()
			n += 1 + l + sovAnti(uint64(l))
		}
	}
	return n
}

func sovAnti(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozAnti(x uint64) (n int) {
	return sovAnti(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *UserLoginInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserLoginInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserLoginInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpType", wireType)
			}
			m.OpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ThirdPartyType", wireType)
			}
			m.ThirdPartyType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ThirdPartyType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Openid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Openid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Imei", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Imei = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OsVer", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OsVer = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OsType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OsType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceModel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceModel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Signature", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Signature = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 16:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsEmulator", wireType)
			}
			m.IsEmulator = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IsEmulator |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 17:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 18:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientVer", wireType)
			}
			m.ClientVer = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientVer |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 19:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 20:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientChannelId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientChannelId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 21:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientType", wireType)
			}
			m.ClientType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 22:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TerminalType", wireType)
			}
			m.TerminalType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TerminalType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 23:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsQuickLoginFromSdk", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsQuickLoginFromSdk = bool(v != 0)
		case 24:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoginTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LoginTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 25:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientPort", wireType)
			}
			m.ClientPort = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientPort |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 26:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Idfa", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Idfa = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 27:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoginAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LoginAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 28:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Username", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Username = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 29:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Alias", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Alias = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 30:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 31:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserSignature", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserSignature = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 32:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProxyIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProxyIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 33:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProxyPort", wireType)
			}
			m.ProxyPort = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProxyPort |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 34:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientId", wireType)
			}
			m.ClientId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("result")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("phone")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordUserLoginReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordUserLoginReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordUserLoginReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Info", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Info == nil {
				m.Info = &UserLoginInfo{}
			}
			if err := m.Info.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Invalid", wireType)
			}
			m.Invalid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Invalid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordUserLoginResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordUserLoginResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordUserLoginResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsNewUsualDevice", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNewUsualDevice = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegUserInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RegUserInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RegUserInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RegAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RegAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("phone")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("reg_at")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDeviceIdInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetDeviceIdInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetDeviceIdInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDeviceIdInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetDeviceIdInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetDeviceIdInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RegUserCount", wireType)
			}
			m.RegUserCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegUserCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RegInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RegInfoList = append(m.RegInfoList, &RegUserInfo{})
			if err := m.RegInfoList[len(m.RegInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("reg_user_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLastLoginInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLastLoginInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLastLoginInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserLastLoginInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserLastLoginInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserLastLoginInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAnti
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAnti
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAnti
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowAnti
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserLastLoginInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserLastLoginInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserLastLoginInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.InfoList = append(m.InfoList, &UserLoginInfo{})
			if err := m.InfoList[len(m.InfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LoginDeviceInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LoginDeviceInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LoginDeviceInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastLoginTime", wireType)
			}
			m.LastLoginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastLoginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_login_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecentLoginDeviceList) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecentLoginDeviceList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecentLoginDeviceList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceList = append(m.DeviceList, &LoginDeviceInfo{})
			if err := m.DeviceList[len(m.DeviceList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LoginUserInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LoginUserInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LoginUserInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastLoginTime", wireType)
			}
			m.LastLoginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastLoginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_login_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecentLoginUserList) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecentLoginUserList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecentLoginUserList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserList = append(m.UserList, &LoginUserInfo{})
			if err := m.UserList[len(m.UserList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyCAPTCHASuccessReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyCAPTCHASuccessReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyCAPTCHASuccessReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyReason", wireType)
			}
			m.VerifyReason = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VerifyReason |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("verify_reason")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyCAPTCHASuccessResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyCAPTCHASuccessResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyCAPTCHASuccessResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLastVerifySuccessInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLastVerifySuccessInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLastVerifySuccessInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyReason", wireType)
			}
			m.VerifyReason = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VerifyReason |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("verify_reason")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLastVerifySuccessInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLastVerifySuccessInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLastVerifySuccessInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("timestamp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUsualDeviceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUsualDeviceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUsualDeviceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUsualDeviceResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUsualDeviceResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUsualDeviceResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsUsualDevice", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsUsualDevice = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_usual_device")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordUsualDeviceReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordUsualDeviceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordUsualDeviceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordUsualDeviceResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordUsualDeviceResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordUsualDeviceResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserIsValidReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserIsValidReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserIsValidReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserIsValidResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserIsValidResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserIsValidResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsValid", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsValid = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_valid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLoginHistoryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLoginHistoryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLoginHistoryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLoginHistoryResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLoginHistoryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLoginHistoryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoginHistory", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LoginHistory = append(m.LoginHistory, &UserLoginInfo{})
			if err := m.LoginHistory[len(m.LoginHistory)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserProfileReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserProfileReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserProfileReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Profile", wireType)
			}
			m.Profile = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Profile |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReasonCode", wireType)
			}
			m.ReasonCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReasonCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("profile")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("reason_code")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("reason")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserProfileRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserProfileRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserProfileRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserProfileReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserProfileReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserProfileReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WithFootmark", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithFootmark = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WithDetail", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithDetail = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserProfileDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserProfileDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserProfileDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Profile", wireType)
			}
			m.Profile = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Profile |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarkTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MarkTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReasonCode", wireType)
			}
			m.ReasonCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReasonCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("profile")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mark_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("reason_code")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("reason")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserProfileRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserProfileRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserProfileRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Profile", wireType)
			}
			m.Profile = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Profile |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProfileList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProfileList = append(m.ProfileList, &UserProfileDetail{})
			if err := m.ProfileList[len(m.ProfileList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReasonCode", wireType)
			}
			m.ReasonCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReasonCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TrackUserLoginReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TrackUserLoginReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TrackUserLoginReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoginAt", wireType)
			}
			m.LoginAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LoginAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Imei", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Imei = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("login_at")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TrackUserLoginRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TrackUserLoginRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TrackUserLoginRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLoginDeviceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLoginDeviceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLoginDeviceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserLoginDevice) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserLoginDevice: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserLoginDevice: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoginAt", wireType)
			}
			m.LoginAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LoginAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("login_at")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLoginDeviceRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLoginDeviceRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLoginDeviceRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoginDevices", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LoginDevices = append(m.LoginDevices, &UserLoginDevice{})
			if err := m.LoginDevices[len(m.LoginDevices)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserLoginHit) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserLoginHit: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserLoginHit: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoginAt", wireType)
			}
			m.LoginAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LoginAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("login_at")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLoginWithDeviceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLoginWithDeviceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLoginWithDeviceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLoginWithDeviceRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLoginWithDeviceRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLoginWithDeviceRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoginUsers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LoginUsers = append(m.LoginUsers, &UserLoginHit{})
			if err := m.LoginUsers[len(m.LoginUsers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserUsualDeviceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserUsualDeviceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserUsualDeviceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserUsualDeviceResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserUsualDeviceResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserUsualDeviceResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoginDevices", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LoginDevices = append(m.LoginDevices, &UserLoginDevice{})
			if err := m.LoginDevices[len(m.LoginDevices)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLoginIpReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLoginIpReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLoginIpReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserLoginIp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserLoginIp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserLoginIp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoginAt", wireType)
			}
			m.LoginAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LoginAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ip")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("login_at")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLoginIpRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLoginIpRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLoginIpRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoginIps", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LoginIps = append(m.LoginIps, &UserLoginIp{})
			if err := m.LoginIps[len(m.LoginIps)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLoginWithIPReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLoginWithIPReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLoginWithIPReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ip")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLoginWithIPRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLoginWithIPRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLoginWithIPRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoginUsers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LoginUsers = append(m.LoginUsers, &UserLoginHit{})
			if err := m.LoginUsers[len(m.LoginUsers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ip")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLoginWithImeiReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLoginWithImeiReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLoginWithImeiReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Imei", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Imei = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("imei")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLoginWithImeiRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLoginWithImeiRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLoginWithImeiRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Imei", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Imei = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoginUsers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LoginUsers = append(m.LoginUsers, &UserLoginHit{})
			if err := m.LoginUsers[len(m.LoginUsers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("imei")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLoginImeiReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLoginImeiReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLoginImeiReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserLoginImei) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserLoginImei: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserLoginImei: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Imei", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Imei = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoginAt", wireType)
			}
			m.LoginAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LoginAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("imei")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("login_at")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLoginImeiRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLoginImeiRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLoginImeiRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoginImeis", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LoginImeis = append(m.LoginImeis, &UserLoginImei{})
			if err := m.LoginImeis[len(m.LoginImeis)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchSetUserProfileReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchSetUserProfileReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchSetUserProfileReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAnti
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAnti
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAnti
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowAnti
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Profile", wireType)
			}
			m.Profile = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Profile |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReasonCode", wireType)
			}
			m.ReasonCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReasonCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("profile")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("reason_code")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("reason")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchSetUserProfileRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchSetUserProfileRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchSetUserProfileRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserProfileReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserProfileReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserProfileReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAnti
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAnti
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAnti
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowAnti
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WithFootmark", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithFootmark = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserProfileRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserProfileRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserProfileRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserProfileList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserProfileList = append(m.UserProfileList, &GetUserProfileRsp{})
			if err := m.UserProfileList[len(m.UserProfileList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLoginByIdfaReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLoginByIdfaReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLoginByIdfaReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Idfa", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Idfa = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("idfa")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserLoginWithIdfa) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserLoginWithIdfa: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserLoginWithIdfa: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Idfa", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Idfa = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpType", wireType)
			}
			m.OpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("idfa")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("at")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLoginByIdfaResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLoginByIdfaResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLoginByIdfaResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rec", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Rec == nil {
				m.Rec = &UserLoginWithIdfa{}
			}
			if err := m.Rec.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordIdfaReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordIdfaReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordIdfaReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpType", wireType)
			}
			m.OpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Idfa", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Idfa = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("idfa")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordIdfaResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordIdfaResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordIdfaResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserRegInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserRegInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserRegInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserRegInfoRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserRegInfoRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserRegInfoRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Info", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Info == nil {
				m.Info = &UserLoginInfo{}
			}
			if err := m.Info.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserLoginDeviceReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserLoginDeviceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserLoginDeviceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAnti
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Uids = append(m.Uids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAnti
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAnti
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowAnti
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Uids = append(m.Uids, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uids", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchUserLoginDevice) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchUserLoginDevice: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchUserLoginDevice: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoginAt", wireType)
			}
			m.LoginAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LoginAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("login_at")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserLoginDeviceRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserLoginDeviceRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserLoginDeviceRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoginDevices", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LoginDevices = append(m.LoginDevices, &BatchUserLoginDevice{})
			if err := m.LoginDevices[len(m.LoginDevices)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserLoginWithDeviceReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserLoginWithDeviceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserLoginWithDeviceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceIds", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceIds = append(m.DeviceIds, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserLoginWithDeviceRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserLoginWithDeviceRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserLoginWithDeviceRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoginUsers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAnti
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LoginUsers = append(m.LoginUsers, &BatchUserLoginDevice{})
			if err := m.LoginUsers[len(m.LoginUsers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAnti(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAnti
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipAnti(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowAnti
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAnti
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthAnti
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowAnti
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipAnti(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthAnti = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowAnti   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/antisvr/anti.proto", fileDescriptorAnti) }

var fileDescriptorAnti = []byte{
	// 3256 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x5a, 0xcd, 0x6f, 0x1c, 0xc7,
	0x95, 0x57, 0xcf, 0xf0, 0xf3, 0x91, 0x43, 0x0e, 0x8b, 0x14, 0xd9, 0x1c, 0x7e, 0xa8, 0x55, 0xb6,
	0x61, 0x4a, 0xe6, 0x50, 0x5f, 0xf6, 0x7a, 0x97, 0xcb, 0xa5, 0x77, 0x44, 0x51, 0x34, 0x6d, 0x7d,
	0x50, 0x43, 0x52, 0xc6, 0xae, 0x96, 0xee, 0x6d, 0x4d, 0x37, 0xc9, 0x0a, 0x67, 0xa6, 0x4b, 0x5d,
	0x3d, 0xb4, 0x94, 0x20, 0x70, 0x8e, 0x81, 0x91, 0x00, 0x76, 0x12, 0x04, 0xb9, 0x39, 0x07, 0x1d,
	0x03, 0x04, 0xc8, 0x2d, 0xff, 0x81, 0x0f, 0x39, 0xe4, 0x92, 0x6b, 0x10, 0x38, 0x17, 0xfd, 0x19,
	0x41, 0x55, 0x75, 0xf7, 0x54, 0x7f, 0x0d, 0x69, 0x19, 0xc9, 0xc5, 0x66, 0xbf, 0xf7, 0xaa, 0xde,
	0x47, 0xfd, 0xea, 0xd5, 0x7b, 0x6f, 0x04, 0xd3, 0xcc, 0x6b, 0x5c, 0xb3, 0xda, 0x3e, 0x61, 0xa7,
	0x9e, 0xf8, 0xff, 0x0a, 0xf5, 0x5c, 0xdf, 0x45, 0x7d, 0xb5, 0xb6, 0x4f, 0x2a, 0x6f, 0x36, 0xdc,
	0x56, 0xcb, 0x6d, 0x5f, 0xf3, 0x9b, 0xa7, 0x94, 0x34, 0x4e, 0x9a, 0xce, 0x35, 0x76, 0xf2, 0xb4,
	0x43, 0x9a, 0x3e, 0x69, 0xfb, 0x2f, 0xa8, 0x23, 0x65, 0xf1, 0x1f, 0x86, 0xa1, 0xb4, 0xcf, 0x1c,
	0xef, 0x9e, 0x7b, 0x44, 0xda, 0xdb, 0xed, 0x43, 0x17, 0x4d, 0x43, 0xb1, 0x43, 0x6c, 0x5d, 0x33,
	0x0a, 0x4b, 0xa5, 0xdb, 0x7d, 0xdf, 0xfc, 0xf5, 0xd2, 0x85, 0x3a, 0x27, 0xa0, 0x05, 0x18, 0x74,
	0xa9, 0xc9, 0x97, 0xea, 0x05, 0x85, 0x37, 0xe0, 0xd2, 0xbd, 0x17, 0xd4, 0x41, 0xf3, 0x30, 0xe0,
	0x39, 0xac, 0xd3, 0xf4, 0xf5, 0xa2, 0x51, 0x58, 0xea, 0x0f, 0xb9, 0x92, 0x86, 0x2a, 0xd0, 0x4f,
	0x8f, 0xdd, 0xb6, 0xa3, 0xf7, 0x19, 0x85, 0xa5, 0xe1, 0x80, 0x29, 0x49, 0x68, 0x05, 0xca, 0xfe,
	0x31, 0xf1, 0x6c, 0x93, 0x5a, 0x9e, 0xff, 0x42, 0x6a, 0xe8, 0x37, 0xb4, 0x48, 0xc3, 0x98, 0xe0,
	0xee, 0x70, 0x66, 0xa8, 0xc9, 0xa5, 0x4e, 0x9b, 0xd8, 0xfa, 0x80, 0xa1, 0x45, 0x9b, 0x05, 0x34,
	0xa4, 0x43, 0x1f, 0x69, 0x39, 0x44, 0x07, 0x85, 0x27, 0x28, 0x68, 0x0e, 0x06, 0x5c, 0x66, 0x9e,
	0x3a, 0x9e, 0x3e, 0xa2, 0xf0, 0xfa, 0x5d, 0xf6, 0xd8, 0xf1, 0x84, 0x77, 0x4c, 0xea, 0x1e, 0x8d,
	0xed, 0xca, 0x84, 0xce, 0xb7, 0x61, 0xd4, 0x76, 0x4e, 0x49, 0xc3, 0x31, 0x5b, 0xae, 0xed, 0x34,
	0xf5, 0x92, 0x22, 0x33, 0x22, 0x39, 0xf7, 0x39, 0x03, 0x61, 0x18, 0x66, 0xe4, 0xa8, 0x6d, 0xf9,
	0x1d, 0xcf, 0xd1, 0xc7, 0x14, 0xa9, 0x2e, 0x19, 0xbd, 0x05, 0xc1, 0x12, 0x93, 0xb4, 0x0f, 0x5d,
	0x7d, 0x5c, 0x91, 0x02, 0xc9, 0x10, 0x07, 0xf1, 0x16, 0x8c, 0x10, 0x66, 0x3a, 0xad, 0x4e, 0xd3,
	0xf2, 0x5d, 0x4f, 0x2f, 0x2b, 0x21, 0x01, 0xc2, 0x36, 0x03, 0x3a, 0xba, 0x0c, 0xc3, 0xe1, 0x6e,
	0xb6, 0x3e, 0xa1, 0xec, 0x35, 0x14, 0xec, 0x65, 0xa3, 0x37, 0x00, 0x1a, 0x4d, 0xe2, 0xb4, 0x7d,
	0xe1, 0x3d, 0x52, 0x36, 0x1a, 0x96, 0x74, 0x1e, 0x01, 0x03, 0x86, 0xe4, 0xc7, 0x36, 0xd5, 0x27,
	0xd5, 0x6d, 0x42, 0x2a, 0xba, 0x0e, 0x13, 0xc1, 0x36, 0x8d, 0x63, 0xab, 0xdd, 0x76, 0x9a, 0x5c,
	0xe3, 0x94, 0x22, 0x3a, 0x2e, 0xd9, 0x1b, 0x92, 0xbb, 0x6d, 0x73, 0x17, 0x82, 0x15, 0x22, 0xb2,
	0x17, 0x55, 0x17, 0x24, 0x43, 0x44, 0xf7, 0x0a, 0x94, 0x7c, 0xc7, 0x6b, 0x91, 0xb6, 0xd5, 0x94,
	0x82, 0xd3, 0x8a, 0xe0, 0x68, 0xc8, 0x12, 0xa2, 0xab, 0x30, 0x43, 0x98, 0xf9, 0xac, 0x43, 0x1a,
	0x27, 0x66, 0x93, 0x63, 0xd6, 0x3c, 0xf4, 0xdc, 0x96, 0xc9, 0xec, 0x13, 0x7d, 0xc6, 0xd0, 0x96,
	0x86, 0x82, 0x45, 0x93, 0x84, 0x3d, 0xe2, 0x32, 0x02, 0xd6, 0x77, 0x3d, 0xb7, 0xb5, 0x6b, 0x9f,
	0xf0, 0x30, 0xc8, 0x25, 0x3e, 0x69, 0x39, 0xba, 0xae, 0x1e, 0x8e, 0xa0, 0xef, 0x91, 0x96, 0xa3,
	0x98, 0x4c, 0x5d, 0xcf, 0xd7, 0x67, 0x0d, 0x2d, 0x02, 0x73, 0x60, 0xf2, 0x8e, 0xeb, 0xf9, 0x02,
	0x66, 0xf6, 0xa1, 0xa5, 0x57, 0x62, 0x30, 0xb3, 0x0f, 0x2d, 0xee, 0x8c, 0xd4, 0x62, 0x35, 0x1a,
	0x6e, 0xa7, 0xed, 0xeb, 0x73, 0x8a, 0xc8, 0xa8, 0x60, 0xd5, 0x24, 0x87, 0x87, 0xbc, 0xc3, 0x1c,
	0xaf, 0x6d, 0xb5, 0x1c, 0x7d, 0x5e, 0x0d, 0x79, 0x48, 0xe5, 0xf7, 0xc6, 0x6a, 0x12, 0x8b, 0xe9,
	0x0b, 0x2a, 0x64, 0x05, 0x89, 0xaf, 0x6e, 0x93, 0xc6, 0x89, 0x58, 0xbd, 0xa8, 0xae, 0x0e, 0xa9,
	0xe8, 0x1d, 0x18, 0xe3, 0x3b, 0x99, 0x5d, 0x44, 0x5e, 0x52, 0xe4, 0x4a, 0x9c, 0xb7, 0x1b, 0xa1,
	0xf2, 0x12, 0x0c, 0x51, 0xcf, 0x7d, 0xfe, 0xc2, 0x24, 0x54, 0x37, 0x14, 0xb1, 0x41, 0x41, 0xdd,
	0xa6, 0x3c, 0x7c, 0x52, 0x40, 0x04, 0xe6, 0xb2, 0x8a, 0x22, 0x41, 0x17, 0x71, 0xb9, 0x0c, 0x01,
	0xa4, 0x38, 0x36, 0xb0, 0x22, 0x13, 0xc2, 0xc8, 0xc6, 0xff, 0x0d, 0x83, 0x0f, 0x77, 0xcc, 0xbd,
	0xff, 0xd9, 0xd9, 0x44, 0x83, 0x50, 0xac, 0x6f, 0x6e, 0x95, 0x35, 0x04, 0x30, 0x70, 0xbf, 0xf6,
	0x60, 0xbf, 0x76, 0xaf, 0x5c, 0x40, 0x63, 0x00, 0xb5, 0xfd, 0xbd, 0x87, 0xe6, 0xbd, 0x87, 0x5b,
	0xdb, 0x0f, 0xca, 0x45, 0x54, 0x86, 0xd1, 0xdd, 0x3b, 0x1f, 0x9b, 0xb5, 0x8d, 0xbd, 0xed, 0xc7,
	0xb5, 0xbd, 0xcd, 0x72, 0x1f, 0x3e, 0x00, 0x54, 0x77, 0x1a, 0xae, 0x67, 0x47, 0x99, 0xab, 0xee,
	0x3c, 0x43, 0x6f, 0x43, 0x9f, 0xb8, 0x4f, 0x3c, 0x73, 0x8d, 0xdc, 0x9c, 0x5c, 0xe1, 0x59, 0x70,
	0x25, 0x96, 0xdb, 0xea, 0x42, 0x00, 0x2d, 0xc2, 0x20, 0x69, 0x9f, 0x5a, 0x4d, 0x62, 0xeb, 0x05,
	0xc5, 0xc2, 0x90, 0x88, 0x3f, 0x82, 0xc9, 0xd4, 0xf6, 0x8c, 0xa2, 0x5b, 0x30, 0x49, 0x98, 0xd9,
	0x76, 0x3e, 0x33, 0x3b, 0xac, 0x63, 0x35, 0x4d, 0x79, 0xbd, 0x74, 0x4d, 0x81, 0x5d, 0x99, 0xb0,
	0x07, 0xce, 0x67, 0xfb, 0x9c, 0x7d, 0x47, 0x70, 0xf1, 0xa7, 0x30, 0x52, 0x77, 0x8e, 0xf8, 0x46,
	0x3d, 0x93, 0x6b, 0x94, 0x1f, 0x0b, 0xe9, 0xfc, 0x38, 0xc7, 0x33, 0xeb, 0x91, 0x69, 0xc9, 0xcc,
	0x1a, 0x31, 0x3d, 0xe7, 0xa8, 0xe6, 0xe3, 0xf7, 0x01, 0x6d, 0x39, 0xfe, 0x9d, 0xe0, 0xa6, 0x0b,
	0x27, 0x9d, 0x67, 0xf1, 0x9c, 0xa0, 0x29, 0xab, 0xa2, 0x9c, 0x80, 0x9f, 0xc3, 0x64, 0x6a, 0x21,
	0xa3, 0xe8, 0x2a, 0x8c, 0x71, 0x65, 0x02, 0x36, 0x12, 0xbe, 0xaa, 0xad, 0xa3, 0x9e, 0xf4, 0x65,
	0x43, 0xc0, 0xf7, 0x3d, 0x28, 0x71, 0x59, 0x1e, 0x53, 0xb3, 0x49, 0x98, 0xaf, 0x17, 0x8c, 0xe2,
	0xd2, 0xc8, 0xcd, 0x09, 0x19, 0x79, 0xc5, 0xed, 0xfa, 0x88, 0xe7, 0x1c, 0xf1, 0x3f, 0xee, 0x11,
	0xe6, 0xe3, 0x1b, 0x30, 0xb3, 0xe5, 0xf8, 0x22, 0xb6, 0x16, 0xf3, 0xbb, 0x87, 0xe3, 0x3c, 0xcb,
	0x0b, 0x0f, 0xfe, 0x0f, 0x98, 0xbf, 0x6d, 0xf9, 0x8d, 0xe3, 0xbc, 0x75, 0xb3, 0x30, 0xd4, 0x21,
	0xb6, 0x34, 0x42, 0x33, 0x8a, 0x4b, 0xa5, 0xfa, 0x60, 0x87, 0xd8, 0x42, 0xdb, 0x23, 0x58, 0xe8,
	0xb1, 0x94, 0xf1, 0xac, 0x36, 0xdc, 0xf5, 0x40, 0x13, 0x1e, 0x64, 0x62, 0x67, 0x88, 0x84, 0x0e,
	0x3c, 0x85, 0x71, 0x41, 0xbe, 0xd3, 0xcd, 0xd5, 0x67, 0x07, 0x1c, 0x2d, 0xc3, 0x78, 0xd3, 0x62,
	0xbe, 0xa9, 0xa4, 0x20, 0xf5, 0x1d, 0x2d, 0x35, 0x43, 0xcb, 0x78, 0x1a, 0xc2, 0x47, 0x70, 0xb1,
	0xee, 0x34, 0x9c, 0xb6, 0xaf, 0x68, 0xe2, 0xca, 0x73, 0x11, 0xf4, 0x6f, 0xd1, 0xa3, 0xa2, 0x1c,
	0xc5, 0x45, 0xe9, 0x48, 0xc2, 0xda, 0xf0, 0x95, 0x11, 0xce, 0xec, 0x43, 0x49, 0xb0, 0xcf, 0x84,
	0xe8, 0x77, 0xb3, 0xff, 0x07, 0xe2, 0x0e, 0x85, 0xf6, 0x8b, 0x48, 0x72, 0xeb, 0xcf, 0x11, 0xa7,
	0xeb, 0x30, 0x2c, 0xd0, 0xa7, 0xb8, 0x31, 0xa9, 0xb8, 0x11, 0x61, 0x4a, 0x24, 0x49, 0xe1, 0xc2,
	0xe7, 0x30, 0xf3, 0xd8, 0xf1, 0xc8, 0xe1, 0x8b, 0x8d, 0xda, 0xce, 0xde, 0xc6, 0x87, 0xb5, 0xdd,
	0x4e, 0xa3, 0xe1, 0x30, 0xd6, 0x03, 0x50, 0x3c, 0x49, 0x9f, 0x8a, 0x25, 0xa6, 0xe7, 0x58, 0xcc,
	0x6d, 0xc7, 0x5c, 0x19, 0x95, 0xac, 0xba, 0xe0, 0xc4, 0x4d, 0x2e, 0x66, 0xde, 0xa5, 0x0a, 0xe8,
	0xd9, 0x06, 0x30, 0x8a, 0xff, 0x1f, 0xe6, 0xb6, 0x1c, 0x9f, 0xc3, 0x4e, 0x8a, 0x04, 0xbc, 0x33,
	0x10, 0xff, 0x1d, 0x0c, 0xc4, 0x0e, 0xcc, 0xe7, 0x6b, 0x60, 0xf4, 0x3c, 0x31, 0xc7, 0x30, 0xcc,
	0x0f, 0x94, 0xf9, 0x56, 0x8b, 0xc6, 0x34, 0x75, 0xc9, 0xfc, 0x44, 0x37, 0x8e, 0x9d, 0xc6, 0x89,
	0x92, 0xdd, 0x7a, 0x39, 0x10, 0xd3, 0x5a, 0xc8, 0xd4, 0xaa, 0x43, 0x9f, 0x78, 0xed, 0x8b, 0x4a,
	0x12, 0x16, 0x14, 0x7c, 0x07, 0xa6, 0xd2, 0xba, 0x18, 0xe5, 0x18, 0x24, 0x2c, 0x99, 0x7e, 0x0b,
	0x51, 0xfa, 0x2d, 0x11, 0xa6, 0xe6, 0xde, 0x47, 0x30, 0x15, 0xe6, 0xf1, 0x6c, 0x93, 0xb5, 0x9e,
	0x26, 0x67, 0x54, 0x52, 0x78, 0x46, 0x5c, 0xcb, 0xe4, 0x96, 0x8c, 0xe2, 0x6a, 0x14, 0x1d, 0xc7,
	0xdb, 0x66, 0x8f, 0xf9, 0x3b, 0xd2, 0x2b, 0xa1, 0xbd, 0x1f, 0x39, 0xa8, 0x88, 0x33, 0xca, 0x1f,
	0x61, 0xc2, 0x4c, 0xf9, 0x36, 0xa9, 0x9e, 0x0d, 0x12, 0x29, 0x84, 0x4f, 0x61, 0x3a, 0xcc, 0x64,
	0xfc, 0x36, 0x7c, 0x48, 0x98, 0xef, 0x7a, 0x2f, 0x7a, 0x1d, 0xc4, 0x1b, 0x00, 0x4f, 0x1d, 0xe5,
	0xca, 0x6a, 0x4b, 0xc5, 0xf0, 0x70, 0x05, 0x5d, 0x54, 0x3d, 0x97, 0x60, 0xc8, 0x69, 0xdb, 0x52,
	0xa4, 0xa8, 0x88, 0x0c, 0x3a, 0x6d, 0x5b, 0xdc, 0xe7, 0xdd, 0x6e, 0xd2, 0x8e, 0xe9, 0x65, 0x14,
	0xfd, 0x7b, 0x58, 0xf0, 0x1c, 0x4b, 0x62, 0xaf, 0x24, 0x2a, 0xeb, 0x9f, 0x60, 0x35, 0xfe, 0x52,
	0x83, 0x89, 0x5d, 0xb9, 0xeb, 0x8e, 0xe7, 0x1e, 0x92, 0x66, 0x4f, 0x44, 0x2d, 0x02, 0x2f, 0x45,
	0xb8, 0x94, 0xc0, 0x53, 0xbf, 0x52, 0x9f, 0x70, 0x22, 0xaf, 0xdc, 0xe4, 0x5d, 0x31, 0x1b, 0xae,
	0xed, 0x88, 0xab, 0x1a, 0x15, 0x9b, 0x92, 0xb1, 0xe1, 0xda, 0x41, 0xa3, 0x22, 0xae, 0x94, 0xda,
	0x8b, 0x04, 0x34, 0x3c, 0x99, 0xb2, 0x88, 0x51, 0xfc, 0x63, 0x98, 0xd8, 0x3a, 0xb7, 0x99, 0x57,
	0xa0, 0xf4, 0x19, 0xf1, 0x8f, 0xcd, 0x43, 0xd7, 0xf5, 0x5b, 0x96, 0x77, 0x22, 0x42, 0x1e, 0x9e,
	0xe3, 0x28, 0x67, 0xdd, 0x0d, 0x38, 0xdc, 0x62, 0x21, 0x6a, 0x3b, 0xbe, 0x45, 0x9a, 0x22, 0xf0,
	0xa1, 0x20, 0x70, 0xc6, 0x1d, 0x41, 0xc7, 0x7f, 0xd4, 0x60, 0x42, 0x51, 0x2e, 0xa9, 0x6a, 0x38,
	0xb4, 0xac, 0x70, 0x5c, 0x86, 0x61, 0xae, 0xa4, 0x9b, 0xa9, 0x23, 0x34, 0x73, 0x72, 0x58, 0xeb,
	0x7e, 0xef, 0x88, 0x71, 0x2e, 0xf3, 0x2d, 0xbf, 0xc3, 0xf4, 0x7e, 0xb5, 0x2d, 0x94, 0x34, 0xfc,
	0x27, 0x2d, 0x15, 0x3b, 0x46, 0x73, 0x6f, 0x60, 0xec, 0x88, 0xb5, 0xb4, 0x4f, 0xab, 0x30, 0x1a,
	0xfc, 0x29, 0x9f, 0x87, 0xa2, 0x40, 0xda, 0x4c, 0x17, 0x69, 0xb1, 0x10, 0xd5, 0x47, 0x02, 0x61,
	0xf1, 0xf4, 0x24, 0x9c, 0xed, 0x53, 0x7b, 0x91, 0x4c, 0x67, 0xfb, 0xd5, 0x3e, 0x30, 0x80, 0xc7,
	0xd7, 0x1a, 0x4c, 0xec, 0x79, 0x96, 0xbc, 0xb8, 0x51, 0xe5, 0x99, 0x07, 0x85, 0x4b, 0x30, 0x14,
	0xb4, 0x02, 0x7e, 0x2c, 0xab, 0x0e, 0xca, 0x2e, 0xc0, 0x4f, 0xbe, 0x2d, 0x5a, 0x76, 0x92, 0x14,
	0xfd, 0x6c, 0x5f, 0xaa, 0x9f, 0x9d, 0x82, 0x02, 0xa1, 0x31, 0x2b, 0x0b, 0x84, 0x72, 0x00, 0x27,
	0x0c, 0x64, 0x14, 0x5f, 0x83, 0x8b, 0xea, 0xed, 0x3d, 0x33, 0x7b, 0xe3, 0x7d, 0x18, 0x4f, 0x48,
	0x9f, 0xe7, 0x19, 0x39, 0xcb, 0x5f, 0x7c, 0x92, 0x69, 0x47, 0x0f, 0x40, 0xac, 0x86, 0xb9, 0x45,
	0xea, 0x60, 0xf1, 0xba, 0x26, 0xb9, 0x91, 0xcc, 0x2e, 0xf2, 0x83, 0xe1, 0x2d, 0x18, 0x55, 0xf2,
	0x95, 0xff, 0xda, 0xa7, 0x84, 0xd7, 0x61, 0x56, 0xb5, 0xfa, 0x13, 0x71, 0x33, 0xc3, 0x08, 0x9e,
	0xa3, 0xd4, 0x66, 0xb9, 0xeb, 0xd3, 0xaf, 0x73, 0x16, 0x04, 0x6e, 0xc1, 0x88, 0x34, 0x90, 0x57,
	0x3c, 0x61, 0x08, 0x50, 0x22, 0x04, 0x1f, 0x12, 0xbf, 0x2e, 0xdb, 0x5b, 0x4e, 0x62, 0xf8, 0x2b,
	0x2d, 0x8a, 0xf5, 0x39, 0x5f, 0xec, 0xf4, 0x43, 0x51, 0x3a, 0xfb, 0xa1, 0x28, 0x25, 0x1e, 0x0a,
	0xde, 0xc9, 0x34, 0x49, 0x8b, 0xf8, 0x02, 0xb0, 0xe1, 0x05, 0x96, 0x24, 0xdc, 0x8c, 0x1e, 0xaf,
	0xe4, 0xc3, 0xfe, 0xcf, 0x38, 0xff, 0x77, 0xa2, 0xcc, 0x23, 0xdf, 0x1f, 0xda, 0x0b, 0xf0, 0x77,
	0x60, 0x44, 0x91, 0x0c, 0xee, 0x96, 0x7a, 0x9c, 0x05, 0x42, 0xcf, 0x46, 0xca, 0x93, 0x94, 0xca,
	0x1e, 0xbe, 0xad, 0x80, 0x1c, 0x3b, 0x98, 0x84, 0xb2, 0x78, 0xeb, 0xa4, 0x6e, 0x20, 0x35, 0x6e,
	0x53, 0x86, 0xab, 0xf1, 0xcb, 0xc3, 0x61, 0xb4, 0xbd, 0xc3, 0x7d, 0xca, 0x34, 0x16, 0x3f, 0xcd,
	0x14, 0x67, 0x79, 0xbe, 0xbd, 0x16, 0xc8, 0x6e, 0xc5, 0xab, 0x02, 0xa1, 0xa3, 0xe5, 0x10, 0x6e,
	0x54, 0x98, 0xb7, 0x54, 0x3d, 0x82, 0x82, 0x8f, 0x73, 0x16, 0x31, 0x9a, 0xbf, 0xe8, 0xf5, 0xcc,
	0xab, 0x8a, 0x1e, 0xb7, 0x1b, 0xcd, 0xc0, 0xb4, 0x3c, 0x0c, 0x7c, 0xa4, 0x8e, 0x42, 0xb9, 0xd2,
	0x7c, 0x73, 0xce, 0x44, 0x42, 0x23, 0x43, 0x75, 0x0f, 0x2c, 0xbc, 0x1b, 0xba, 0xc7, 0x77, 0x67,
	0xf1, 0xb6, 0x27, 0xbe, 0x89, 0xf4, 0x8f, 0xff, 0xc9, 0xf0, 0x6f, 0x34, 0x98, 0x16, 0xcd, 0x6d,
	0xba, 0x88, 0xca, 0xef, 0x88, 0xff, 0x35, 0x75, 0x94, 0x9e, 0x6d, 0x19, 0xa3, 0xf8, 0xd3, 0x80,
	0xb3, 0xf5, 0x5d, 0x6c, 0x3e, 0x7f, 0x51, 0x85, 0x0f, 0xb2, 0xf7, 0x67, 0x14, 0x6d, 0xc0, 0x84,
	0xe8, 0x2c, 0xf3, 0x4b, 0x88, 0xd4, 0x9a, 0xfa, 0x78, 0xa7, 0xfb, 0x1d, 0x4c, 0x2f, 0x62, 0xd7,
	0xea, 0xf6, 0x8b, 0x6d, 0xfb, 0xd0, 0x0a, 0x01, 0x6f, 0x1f, 0x5a, 0x09, 0xb0, 0xd8, 0x87, 0x16,
	0xfe, 0xa1, 0x2c, 0xdf, 0xba, 0x68, 0xb7, 0x0f, 0xad, 0x7c, 0xf1, 0x10, 0x23, 0x85, 0x64, 0x7e,
	0x9e, 0x82, 0x42, 0x30, 0x03, 0x0a, 0xc9, 0x05, 0xcb, 0x57, 0xc7, 0xf2, 0x7d, 0xe9, 0xb1, 0x3c,
	0xde, 0x88, 0xf7, 0x0b, 0xa1, 0xb9, 0x8c, 0xa2, 0x2b, 0x50, 0xf4, 0x9c, 0x86, 0x80, 0x62, 0xac,
	0x84, 0x8a, 0x99, 0x59, 0xe7, 0x32, 0xf8, 0x14, 0x4a, 0xb2, 0xeb, 0x09, 0x7d, 0x7d, 0xcd, 0xdf,
	0x08, 0x42, 0x9f, 0x8b, 0x29, 0x9f, 0xa5, 0x6f, 0x6a, 0x4d, 0x56, 0xb0, 0x7c, 0x5c, 0x86, 0x31,
	0x55, 0x2f, 0xa3, 0x4a, 0x4e, 0xaf, 0xcb, 0x89, 0x52, 0xaf, 0xfb, 0xbc, 0x96, 0x12, 0x66, 0x54,
	0x99, 0x12, 0x6a, 0x3d, 0xa7, 0x84, 0xf8, 0x3a, 0x54, 0x62, 0x83, 0xa3, 0x78, 0xe1, 0x84, 0xa0,
	0xaf, 0x43, 0x6c, 0x16, 0xe0, 0x54, 0xfc, 0x8d, 0x3d, 0x98, 0x12, 0x2b, 0x92, 0x95, 0xd3, 0xf7,
	0x68, 0x91, 0xd5, 0x3c, 0x53, 0xcc, 0xca, 0x33, 0x07, 0xf9, 0x56, 0x32, 0x8a, 0x3e, 0xc8, 0x7e,
	0x3e, 0x2b, 0xd2, 0xeb, 0x2c, 0x63, 0x13, 0x6f, 0xe8, 0x07, 0xb0, 0x98, 0xda, 0x3e, 0x5e, 0xff,
	0x2c, 0x00, 0x44, 0x4e, 0xc8, 0x70, 0x0c, 0xd7, 0x87, 0x43, 0xfb, 0x19, 0x3e, 0xe8, 0xbd, 0x01,
	0xa3, 0xe8, 0x3f, 0xe3, 0x99, 0x5d, 0x3b, 0xd3, 0x42, 0x25, 0xc3, 0x5f, 0xfd, 0x95, 0x06, 0x25,
	0x31, 0x27, 0x36, 0xc3, 0x91, 0x72, 0x19, 0x46, 0x23, 0xc2, 0x03, 0xd2, 0x2c, 0x5f, 0x88, 0x51,
	0xea, 0xce, 0x51, 0x59, 0x43, 0x93, 0x30, 0x1e, 0x51, 0xee, 0x5b, 0xed, 0x8e, 0xd5, 0x2c, 0x17,
	0xd0, 0x34, 0xa0, 0x88, 0x58, 0xeb, 0xf8, 0xae, 0x50, 0x59, 0x2e, 0x22, 0x1d, 0xa6, 0x22, 0xfa,
	0xae, 0x7d, 0x52, 0x6b, 0xf8, 0xe4, 0xd4, 0xf2, 0x9d, 0x72, 0x5f, 0x8c, 0xb3, 0xfd, 0x70, 0xb7,
	0x76, 0x1a, 0x70, 0xfa, 0xaf, 0xee, 0xc1, 0xf4, 0x7e, 0xbb, 0xd3, 0xad, 0x71, 0x44, 0xaf, 0x2f,
	0xae, 0xc0, 0x22, 0x54, 0xf6, 0xb9, 0x42, 0x53, 0x32, 0x4c, 0xc1, 0x31, 0x37, 0x2c, 0xea, 0x37,
	0x8e, 0xad, 0xf2, 0x05, 0x54, 0x11, 0x2b, 0x93, 0xfc, 0xdd, 0x16, 0x2b, 0x6b, 0x57, 0xff, 0x17,
	0x46, 0xf7, 0x77, 0x37, 0xeb, 0xe6, 0x4e, 0xfd, 0xe1, 0xdd, 0xed, 0x7b, 0x9b, 0x68, 0x06, 0x26,
	0xd5, 0x6f, 0xf3, 0xc1, 0xc3, 0xfa, 0xfd, 0xda, 0xbd, 0xf2, 0x05, 0x74, 0x11, 0x26, 0x62, 0x8c,
	0xbb, 0xb5, 0x8f, 0x37, 0xcb, 0x9a, 0xd8, 0x5b, 0x25, 0xef, 0xee, 0xef, 0xee, 0x6c, 0x6f, 0x3c,
	0xdc, 0xdf, 0x2d, 0x17, 0x6e, 0xfe, 0x7c, 0x0e, 0xc4, 0xcf, 0x86, 0xe8, 0x19, 0x8c, 0x27, 0x86,
	0xdf, 0x48, 0x0f, 0x07, 0xba, 0xc9, 0x91, 0x7b, 0x65, 0x36, 0x87, 0xc3, 0x28, 0xbe, 0xf2, 0x93,
	0x97, 0xaf, 0x8a, 0xda, 0x17, 0x2f, 0x5f, 0x15, 0xfb, 0x3a, 0xab, 0xf6, 0xea, 0x2f, 0x5e, 0xbe,
	0x2a, 0x4e, 0x57, 0x3b, 0xc6, 0x5a, 0x87, 0xd8, 0xeb, 0x46, 0xd5, 0x5e, 0x8b, 0x40, 0xb3, 0x8e,
	0x3e, 0x81, 0xf1, 0xc4, 0x28, 0x3a, 0x54, 0x99, 0x1e, 0x6d, 0x87, 0x2a, 0x33, 0x66, 0xd7, 0x78,
	0x9c, 0xab, 0x2c, 0x70, 0x95, 0x17, 0xb8, 0xba, 0x0b, 0xc8, 0x81, 0xa9, 0xac, 0xb1, 0x2f, 0x5a,
	0x88, 0x65, 0xfb, 0xe4, 0x34, 0xb9, 0x92, 0x95, 0x14, 0xf0, 0x2c, 0xdf, 0xbc, 0xc8, 0x37, 0x2f,
	0x74, 0x84, 0x37, 0x43, 0xa1, 0x37, 0xe8, 0xd7, 0x1a, 0x4c, 0x65, 0xcd, 0xff, 0x42, 0x3d, 0x39,
	0xc3, 0xc9, 0xca, 0x62, 0x2f, 0x36, 0xa3, 0xf8, 0x03, 0xae, 0xb2, 0x8f, 0xab, 0x1c, 0xe8, 0xac,
	0x7a, 0x41, 0x10, 0xaf, 0x76, 0x83, 0xe8, 0x19, 0x6b, 0xb1, 0x19, 0x21, 0x8f, 0xab, 0xa1, 0x06,
	0xf6, 0x2b, 0x0d, 0xf4, 0xbc, 0xd1, 0x20, 0xba, 0x1c, 0x05, 0x21, 0x6f, 0x38, 0x59, 0xc1, 0x67,
	0x89, 0x30, 0x8a, 0x57, 0xb8, 0x91, 0xfd, 0xc1, 0x39, 0x7b, 0xc2, 0xc4, 0xb9, 0x1e, 0x26, 0xa2,
	0x9f, 0x69, 0x50, 0x4e, 0xce, 0xf6, 0x50, 0x70, 0xa8, 0x19, 0xf3, 0xc5, 0x4a, 0x25, 0x8f, 0xc5,
	0x28, 0xae, 0x71, 0xdd, 0x03, 0x41, 0x80, 0xec, 0x55, 0x5f, 0x68, 0x5f, 0x56, 0x50, 0xa6, 0x46,
	0xc3, 0xa8, 0xfa, 0xc6, 0xda, 0xf5, 0x95, 0x86, 0xbc, 0x6a, 0xc6, 0x8d, 0x15, 0xd6, 0x62, 0xeb,
	0xc8, 0x89, 0xac, 0x89, 0x06, 0x71, 0x09, 0x6b, 0xd4, 0x79, 0x5e, 0xc2, 0x9a, 0xd8, 0xec, 0x0e,
	0x57, 0xb8, 0x35, 0x83, 0x0a, 0x42, 0x86, 0x23, 0x4b, 0xd0, 0x2f, 0xb5, 0x78, 0x3d, 0x18, 0x4c,
	0xc0, 0xd0, 0x7c, 0x1c, 0x89, 0xf1, 0x91, 0x5e, 0x65, 0xa1, 0x07, 0x37, 0xc4, 0xc7, 0x50, 0xe0,
	0xfe, 0xd3, 0x55, 0x27, 0x81, 0x8f, 0x27, 0xd5, 0xa7, 0xc6, 0x9a, 0xe8, 0xdc, 0x0c, 0xde, 0xae,
	0xad, 0x1f, 0x18, 0x4f, 0xaa, 0x8e, 0xb1, 0xe6, 0xb4, 0xed, 0xe0, 0x1b, 0x7d, 0xad, 0x89, 0x5f,
	0x8f, 0x12, 0x0d, 0x19, 0x9a, 0x8b, 0xa9, 0x4d, 0x9c, 0xc7, 0x7c, 0x3e, 0x93, 0x51, 0xfc, 0x80,
	0x9b, 0x34, 0xcc, 0x4d, 0x1a, 0x92, 0x26, 0x35, 0x85, 0x51, 0xef, 0x9f, 0xdf, 0x28, 0xe3, 0x49,
	0xb5, 0x69, 0xac, 0x89, 0x7e, 0x71, 0xfd, 0x00, 0xfd, 0x4e, 0x83, 0xb1, 0x78, 0x09, 0x89, 0x82,
	0x52, 0x25, 0x55, 0xf2, 0x56, 0xb2, 0x19, 0x8c, 0xe2, 0x63, 0x6e, 0xd4, 0x48, 0x60, 0x14, 0x5d,
	0xf5, 0x56, 0x99, 0x30, 0xea, 0x51, 0x17, 0x28, 0xd4, 0x58, 0x0b, 0x8a, 0x42, 0x6e, 0xa1, 0x67,
	0xac, 0x29, 0x05, 0xf0, 0xb2, 0x61, 0x3b, 0x87, 0x56, 0xa7, 0xe9, 0xff, 0xd7, 0xcd, 0x75, 0xa3,
	0xca, 0x22, 0x1e, 0xf3, 0xbd, 0xe5, 0x90, 0x63, 0xd9, 0x2d, 0xd2, 0x5e, 0x3f, 0x40, 0xcf, 0x60,
	0x6c, 0x2b, 0xd3, 0xda, 0xad, 0x3c, 0x6b, 0x53, 0x15, 0x27, 0xae, 0x72, 0x6b, 0x47, 0xb9, 0xb5,
	0xc5, 0xce, 0xaa, 0x1d, 0xbf, 0x4f, 0x4f, 0xaa, 0xb6, 0xb1, 0x6c, 0xf0, 0x72, 0xd7, 0x90, 0x33,
	0xc3, 0x03, 0xf4, 0x5b, 0x0d, 0xc6, 0xe2, 0x03, 0x9f, 0x50, 0x67, 0x6a, 0x4e, 0x55, 0xc9, 0x66,
	0x30, 0x8a, 0xff, 0x8f, 0xeb, 0x2c, 0x71, 0x9d, 0xc0, 0x2f, 0xd2, 0xf1, 0x2a, 0x09, 0x2e, 0xd3,
	0x46, 0xc6, 0x65, 0x32, 0xc4, 0xe7, 0xb1, 0xb1, 0x26, 0x7f, 0xd1, 0x35, 0x08, 0x5d, 0x37, 0xaa,
	0xc4, 0x58, 0xe3, 0xad, 0x8b, 0xbc, 0x65, 0x61, 0xc9, 0x72, 0xcd, 0xb6, 0x7c, 0x67, 0x1d, 0x91,
	0x08, 0x65, 0x6a, 0x55, 0x34, 0x97, 0x06, 0x77, 0x17, 0x65, 0xf9, 0x4c, 0x46, 0x65, 0x2a, 0x1e,
	0xcb, 0x4c, 0xc5, 0x3f, 0x8a, 0x97, 0xbb, 0xdd, 0x4a, 0x03, 0x5d, 0x4a, 0xef, 0x18, 0x2b, 0x64,
	0x2a, 0xbd, 0x05, 0x18, 0xc5, 0x97, 0xb8, 0xda, 0x71, 0xa1, 0x56, 0xa6, 0xe2, 0xb1, 0x44, 0xba,
	0x3d, 0x8e, 0xfb, 0x29, 0x3b, 0xee, 0x2c, 0x3f, 0xa3, 0xd6, 0xbd, 0x92, 0xcf, 0x64, 0x14, 0xeb,
	0x5c, 0x61, 0x59, 0x28, 0x3c, 0x16, 0x0a, 0x07, 0x79, 0xd4, 0x09, 0x5d, 0x47, 0xb4, 0xfb, 0xb0,
	0xa9, 0x2d, 0x34, 0x5a, 0xc8, 0xd9, 0x4e, 0x36, 0xbe, 0x95, 0x5e, 0xec, 0x30, 0x81, 0x4d, 0x08,
	0x7d, 0x24, 0x48, 0x60, 0xe1, 0xb1, 0xa2, 0x06, 0x94, 0x93, 0xfd, 0x2c, 0x9a, 0x4d, 0x6f, 0x17,
	0x6a, 0xca, 0x63, 0x85, 0xa7, 0x87, 0x32, 0x4f, 0xef, 0xf7, 0x1a, 0x4c, 0x66, 0x74, 0x8d, 0x61,
	0x96, 0xcc, 0x6e, 0x75, 0x2b, 0x3d, 0xb8, 0x21, 0xb4, 0x27, 0x53, 0x97, 0x7f, 0x33, 0x50, 0x7a,
	0x63, 0xb9, 0x43, 0xec, 0x9b, 0xfc, 0x3f, 0xb7, 0x96, 0x57, 0x56, 0x56, 0x12, 0xb9, 0x20, 0x91,
	0x0a, 0x92, 0xf7, 0x9f, 0xe3, 0x6d, 0x32, 0xa3, 0xd9, 0x8c, 0x19, 0xbc, 0xd5, 0xd3, 0xe0, 0xf4,
	0xfd, 0x5f, 0xe2, 0x06, 0x4f, 0x29, 0xf1, 0x99, 0xc9, 0x31, 0x15, 0x1d, 0x44, 0xd9, 0x26, 0x1c,
	0x5b, 0xcd, 0x64, 0x84, 0x9d, 0xa6, 0xb3, 0x4d, 0x77, 0x38, 0x25, 0x4f, 0x63, 0x3a, 0xf3, 0x34,
	0x4e, 0xe2, 0x70, 0x96, 0xad, 0x63, 0x16, 0x9c, 0xa3, 0x1e, 0xb8, 0x32, 0x9f, 0xcf, 0x0c, 0xf1,
	0x35, 0x93, 0xc2, 0x97, 0x7d, 0x68, 0xad, 0xa3, 0x97, 0x1a, 0x40, 0xb7, 0xd7, 0x43, 0x93, 0x6a,
	0x61, 0x19, 0xee, 0x3e, 0x95, 0x26, 0x32, 0x8a, 0x4f, 0xf8, 0xae, 0x7a, 0xf0, 0x0a, 0xfa, 0xab,
	0x72, 0xe7, 0xc7, 0xdd, 0xbc, 0xe5, 0x1b, 0x6b, 0x41, 0x4f, 0xba, 0x7a, 0xa3, 0xea, 0x39, 0x47,
	0xcb, 0x37, 0xab, 0x2d, 0x51, 0xf3, 0x2f, 0xdf, 0xaa, 0x5a, 0x1d, 0xdf, 0x15, 0xc9, 0x6a, 0xf9,
	0xdd, 0x2a, 0xb3, 0x4f, 0xac, 0xa0, 0xb6, 0x5f, 0x7e, 0xaf, 0x4a, 0x5c, 0x16, 0x7e, 0x05, 0xd9,
	0x4d, 0x98, 0xd9, 0x0d, 0x79, 0xd0, 0x52, 0x26, 0x42, 0xde, 0xed, 0x4a, 0x2b, 0xd9, 0x8c, 0x30,
	0xe4, 0xb3, 0x99, 0x21, 0xff, 0x1c, 0x66, 0x73, 0xff, 0xb1, 0x02, 0xc2, 0x69, 0xd8, 0xa4, 0x4a,
	0xd7, 0x37, 0xce, 0x94, 0x09, 0x0d, 0xa8, 0x64, 0x1a, 0xf0, 0x1c, 0x66, 0x72, 0xda, 0x49, 0x64,
	0x64, 0x6c, 0x1d, 0x4f, 0xda, 0x67, 0x48, 0x84, 0x9a, 0xe7, 0x32, 0x35, 0x7f, 0xa1, 0xc1, 0x5c,
	0x8f, 0x4e, 0x11, 0xbd, 0x99, 0xb3, 0x79, 0x3c, 0x89, 0x9f, 0x43, 0x2a, 0xcc, 0xe4, 0xf3, 0x3d,
	0x32, 0xf9, 0x73, 0x98, 0x48, 0xfd, 0xcc, 0x8b, 0x2a, 0xf1, 0x66, 0x27, 0x56, 0x15, 0xcd, 0xe5,
	0xf2, 0x18, 0xc5, 0x57, 0xb9, 0xba, 0x85, 0x58, 0x2b, 0x34, 0x93, 0x53, 0xa4, 0x56, 0x06, 0x7e,
	0xfa, 0xf2, 0x55, 0xf1, 0x2f, 0x9d, 0xdb, 0xe5, 0x6f, 0xbe, 0x5d, 0xd4, 0xfe, 0xfc, 0xed, 0xa2,
	0xf6, 0xb7, 0x6f, 0x17, 0xb5, 0x2f, 0xff, 0xbe, 0x78, 0xe1, 0x1f, 0x01, 0x00, 0x00, 0xff, 0xff,
	0x39, 0x14, 0x80, 0x67, 0xee, 0x29, 0x00, 0x00,
}
