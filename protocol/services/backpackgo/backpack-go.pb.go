// Code generated by protoc-gen-go. DO NOT EDIT.
// source: backpack-go.proto

package backpackgo // import "golang.52tt.com/protocol/services/backpackgo"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type FreezeStatus int32

const (
	FreezeStatus_ENUM_INIT   FreezeStatus = 0
	FreezeStatus_ENUM_COMMIT FreezeStatus = 1
)

var FreezeStatus_name = map[int32]string{
	0: "ENUM_INIT",
	1: "ENUM_COMMIT",
}
var FreezeStatus_value = map[string]int32{
	"ENUM_INIT":   0,
	"ENUM_COMMIT": 1,
}

func (x FreezeStatus) String() string {
	return proto.EnumName(FreezeStatus_name, int32(x))
}
func (FreezeStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_backpack_go_1a4b25dc6acf3bab, []int{0}
}

type Callback_Type int32

const (
	Callback_UNDEFINED Callback_Type = 0
	Callback_GRPC      Callback_Type = 1
)

var Callback_Type_name = map[int32]string{
	0: "UNDEFINED",
	1: "GRPC",
}
var Callback_Type_value = map[string]int32{
	"UNDEFINED": 0,
	"GRPC":      1,
}

func (x Callback_Type) String() string {
	return proto.EnumName(Callback_Type_name, int32(x))
}
func (Callback_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_backpack_go_1a4b25dc6acf3bab, []int{1, 0}
}

type ItemInfo struct {
	ItemId               uint32   `protobuf:"varint,4,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemType             uint32   `protobuf:"varint,5,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemCount            uint32   `protobuf:"varint,6,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ItemInfo) Reset()         { *m = ItemInfo{} }
func (m *ItemInfo) String() string { return proto.CompactTextString(m) }
func (*ItemInfo) ProtoMessage()    {}
func (*ItemInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_go_1a4b25dc6acf3bab, []int{0}
}
func (m *ItemInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ItemInfo.Unmarshal(m, b)
}
func (m *ItemInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ItemInfo.Marshal(b, m, deterministic)
}
func (dst *ItemInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ItemInfo.Merge(dst, src)
}
func (m *ItemInfo) XXX_Size() int {
	return xxx_messageInfo_ItemInfo.Size(m)
}
func (m *ItemInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ItemInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ItemInfo proto.InternalMessageInfo

func (m *ItemInfo) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *ItemInfo) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *ItemInfo) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

type Callback struct {
	// Type, only GRPC is supported by now.
	Type Callback_Type `protobuf:"varint,8,opt,name=type,proto3,enum=backpackgo.Callback_Type" json:"type,omitempty"`
	// A url which should be registered to name server
	// e.g.: pay_callback.userpresent.52tt.local
	Url string `protobuf:"bytes,9,opt,name=url,proto3" json:"url,omitempty"`
	// Duration after pay order is created (in seconds).
	// default: 5 minutes
	// max: 1 hour
	FirstTime            uint32   `protobuf:"varint,10,opt,name=first_time,json=firstTime,proto3" json:"first_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Callback) Reset()         { *m = Callback{} }
func (m *Callback) String() string { return proto.CompactTextString(m) }
func (*Callback) ProtoMessage()    {}
func (*Callback) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_go_1a4b25dc6acf3bab, []int{1}
}
func (m *Callback) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Callback.Unmarshal(m, b)
}
func (m *Callback) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Callback.Marshal(b, m, deterministic)
}
func (dst *Callback) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Callback.Merge(dst, src)
}
func (m *Callback) XXX_Size() int {
	return xxx_messageInfo_Callback.Size(m)
}
func (m *Callback) XXX_DiscardUnknown() {
	xxx_messageInfo_Callback.DiscardUnknown(m)
}

var xxx_messageInfo_Callback proto.InternalMessageInfo

func (m *Callback) GetType() Callback_Type {
	if m != nil {
		return m.Type
	}
	return Callback_UNDEFINED
}

func (m *Callback) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *Callback) GetFirstTime() uint32 {
	if m != nil {
		return m.FirstTime
	}
	return 0
}

type FreezeOrderResultReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreezeOrderResultReq) Reset()         { *m = FreezeOrderResultReq{} }
func (m *FreezeOrderResultReq) String() string { return proto.CompactTextString(m) }
func (*FreezeOrderResultReq) ProtoMessage()    {}
func (*FreezeOrderResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_go_1a4b25dc6acf3bab, []int{2}
}
func (m *FreezeOrderResultReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeOrderResultReq.Unmarshal(m, b)
}
func (m *FreezeOrderResultReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeOrderResultReq.Marshal(b, m, deterministic)
}
func (dst *FreezeOrderResultReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeOrderResultReq.Merge(dst, src)
}
func (m *FreezeOrderResultReq) XXX_Size() int {
	return xxx_messageInfo_FreezeOrderResultReq.Size(m)
}
func (m *FreezeOrderResultReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeOrderResultReq.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeOrderResultReq proto.InternalMessageInfo

func (m *FreezeOrderResultReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type FreezeOrderResultResp struct {
	Confirm              bool     `protobuf:"varint,1,opt,name=confirm,proto3" json:"confirm,omitempty"`
	CommitCount          uint32   `protobuf:"varint,2,opt,name=commit_count,json=commitCount,proto3" json:"commit_count,omitempty"`
	CommitTime           uint32   `protobuf:"varint,3,opt,name=commit_time,json=commitTime,proto3" json:"commit_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreezeOrderResultResp) Reset()         { *m = FreezeOrderResultResp{} }
func (m *FreezeOrderResultResp) String() string { return proto.CompactTextString(m) }
func (*FreezeOrderResultResp) ProtoMessage()    {}
func (*FreezeOrderResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_go_1a4b25dc6acf3bab, []int{3}
}
func (m *FreezeOrderResultResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeOrderResultResp.Unmarshal(m, b)
}
func (m *FreezeOrderResultResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeOrderResultResp.Marshal(b, m, deterministic)
}
func (dst *FreezeOrderResultResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeOrderResultResp.Merge(dst, src)
}
func (m *FreezeOrderResultResp) XXX_Size() int {
	return xxx_messageInfo_FreezeOrderResultResp.Size(m)
}
func (m *FreezeOrderResultResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeOrderResultResp.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeOrderResultResp proto.InternalMessageInfo

func (m *FreezeOrderResultResp) GetConfirm() bool {
	if m != nil {
		return m.Confirm
	}
	return false
}

func (m *FreezeOrderResultResp) GetCommitCount() uint32 {
	if m != nil {
		return m.CommitCount
	}
	return 0
}

func (m *FreezeOrderResultResp) GetCommitTime() uint32 {
	if m != nil {
		return m.CommitTime
	}
	return 0
}

// 减道具数量，记录在冻结表；不产生扣除记录
type FreezeItemReq struct {
	OrderId              string    `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Uid                  uint32    `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	FreezeTime           uint32    `protobuf:"varint,3,opt,name=freeze_time,json=freezeTime,proto3" json:"freeze_time,omitempty"`
	ItemInfo             *ItemInfo `protobuf:"bytes,4,opt,name=item_info,json=itemInfo,proto3" json:"item_info,omitempty"`
	SourceId             uint32    `protobuf:"varint,5,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	OperType             uint32    `protobuf:"varint,6,opt,name=oper_type,json=operType,proto3" json:"oper_type,omitempty"`
	CallBack             *Callback `protobuf:"bytes,7,opt,name=call_back,json=callBack,proto3" json:"call_back,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *FreezeItemReq) Reset()         { *m = FreezeItemReq{} }
func (m *FreezeItemReq) String() string { return proto.CompactTextString(m) }
func (*FreezeItemReq) ProtoMessage()    {}
func (*FreezeItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_go_1a4b25dc6acf3bab, []int{4}
}
func (m *FreezeItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeItemReq.Unmarshal(m, b)
}
func (m *FreezeItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeItemReq.Marshal(b, m, deterministic)
}
func (dst *FreezeItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeItemReq.Merge(dst, src)
}
func (m *FreezeItemReq) XXX_Size() int {
	return xxx_messageInfo_FreezeItemReq.Size(m)
}
func (m *FreezeItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeItemReq proto.InternalMessageInfo

func (m *FreezeItemReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *FreezeItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FreezeItemReq) GetFreezeTime() uint32 {
	if m != nil {
		return m.FreezeTime
	}
	return 0
}

func (m *FreezeItemReq) GetItemInfo() *ItemInfo {
	if m != nil {
		return m.ItemInfo
	}
	return nil
}

func (m *FreezeItemReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *FreezeItemReq) GetOperType() uint32 {
	if m != nil {
		return m.OperType
	}
	return 0
}

func (m *FreezeItemReq) GetCallBack() *Callback {
	if m != nil {
		return m.CallBack
	}
	return nil
}

type FreezeItemResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreezeItemResp) Reset()         { *m = FreezeItemResp{} }
func (m *FreezeItemResp) String() string { return proto.CompactTextString(m) }
func (*FreezeItemResp) ProtoMessage()    {}
func (*FreezeItemResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_go_1a4b25dc6acf3bab, []int{5}
}
func (m *FreezeItemResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeItemResp.Unmarshal(m, b)
}
func (m *FreezeItemResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeItemResp.Marshal(b, m, deterministic)
}
func (dst *FreezeItemResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeItemResp.Merge(dst, src)
}
func (m *FreezeItemResp) XXX_Size() int {
	return xxx_messageInfo_FreezeItemResp.Size(m)
}
func (m *FreezeItemResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeItemResp.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeItemResp proto.InternalMessageInfo

type CommitItemReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	FreezeTime           uint32   `protobuf:"varint,3,opt,name=freeze_time,json=freezeTime,proto3" json:"freeze_time,omitempty"`
	CommitTime           uint32   `protobuf:"varint,4,opt,name=commit_time,json=commitTime,proto3" json:"commit_time,omitempty"`
	CommitCount          uint32   `protobuf:"varint,5,opt,name=commit_count,json=commitCount,proto3" json:"commit_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommitItemReq) Reset()         { *m = CommitItemReq{} }
func (m *CommitItemReq) String() string { return proto.CompactTextString(m) }
func (*CommitItemReq) ProtoMessage()    {}
func (*CommitItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_go_1a4b25dc6acf3bab, []int{6}
}
func (m *CommitItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommitItemReq.Unmarshal(m, b)
}
func (m *CommitItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommitItemReq.Marshal(b, m, deterministic)
}
func (dst *CommitItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommitItemReq.Merge(dst, src)
}
func (m *CommitItemReq) XXX_Size() int {
	return xxx_messageInfo_CommitItemReq.Size(m)
}
func (m *CommitItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommitItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommitItemReq proto.InternalMessageInfo

func (m *CommitItemReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *CommitItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CommitItemReq) GetFreezeTime() uint32 {
	if m != nil {
		return m.FreezeTime
	}
	return 0
}

func (m *CommitItemReq) GetCommitTime() uint32 {
	if m != nil {
		return m.CommitTime
	}
	return 0
}

func (m *CommitItemReq) GetCommitCount() uint32 {
	if m != nil {
		return m.CommitCount
	}
	return 0
}

type CommitItemResp struct {
	DealToken            string   `protobuf:"bytes,1,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommitItemResp) Reset()         { *m = CommitItemResp{} }
func (m *CommitItemResp) String() string { return proto.CompactTextString(m) }
func (*CommitItemResp) ProtoMessage()    {}
func (*CommitItemResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_go_1a4b25dc6acf3bab, []int{7}
}
func (m *CommitItemResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommitItemResp.Unmarshal(m, b)
}
func (m *CommitItemResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommitItemResp.Marshal(b, m, deterministic)
}
func (dst *CommitItemResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommitItemResp.Merge(dst, src)
}
func (m *CommitItemResp) XXX_Size() int {
	return xxx_messageInfo_CommitItemResp.Size(m)
}
func (m *CommitItemResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CommitItemResp.DiscardUnknown(m)
}

var xxx_messageInfo_CommitItemResp proto.InternalMessageInfo

func (m *CommitItemResp) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

// 对账接口
// 获得订单数据
type TimeRangeReq struct {
	BeginTime            int64    `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Params               string   `protobuf:"bytes,3,opt,name=params,proto3" json:"params,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TimeRangeReq) Reset()         { *m = TimeRangeReq{} }
func (m *TimeRangeReq) String() string { return proto.CompactTextString(m) }
func (*TimeRangeReq) ProtoMessage()    {}
func (*TimeRangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_go_1a4b25dc6acf3bab, []int{8}
}
func (m *TimeRangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeRangeReq.Unmarshal(m, b)
}
func (m *TimeRangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeRangeReq.Marshal(b, m, deterministic)
}
func (dst *TimeRangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeRangeReq.Merge(dst, src)
}
func (m *TimeRangeReq) XXX_Size() int {
	return xxx_messageInfo_TimeRangeReq.Size(m)
}
func (m *TimeRangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeRangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_TimeRangeReq proto.InternalMessageInfo

func (m *TimeRangeReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *TimeRangeReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *TimeRangeReq) GetParams() string {
	if m != nil {
		return m.Params
	}
	return ""
}

// 响应order_id个数
type CountResp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Value                uint32   `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CountResp) Reset()         { *m = CountResp{} }
func (m *CountResp) String() string { return proto.CompactTextString(m) }
func (*CountResp) ProtoMessage()    {}
func (*CountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_go_1a4b25dc6acf3bab, []int{9}
}
func (m *CountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CountResp.Unmarshal(m, b)
}
func (m *CountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CountResp.Marshal(b, m, deterministic)
}
func (dst *CountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CountResp.Merge(dst, src)
}
func (m *CountResp) XXX_Size() int {
	return xxx_messageInfo_CountResp.Size(m)
}
func (m *CountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CountResp.DiscardUnknown(m)
}

var xxx_messageInfo_CountResp proto.InternalMessageInfo

func (m *CountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *CountResp) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func init() {
	proto.RegisterType((*ItemInfo)(nil), "backpackgo.ItemInfo")
	proto.RegisterType((*Callback)(nil), "backpackgo.Callback")
	proto.RegisterType((*FreezeOrderResultReq)(nil), "backpackgo.FreezeOrderResultReq")
	proto.RegisterType((*FreezeOrderResultResp)(nil), "backpackgo.FreezeOrderResultResp")
	proto.RegisterType((*FreezeItemReq)(nil), "backpackgo.FreezeItemReq")
	proto.RegisterType((*FreezeItemResp)(nil), "backpackgo.FreezeItemResp")
	proto.RegisterType((*CommitItemReq)(nil), "backpackgo.CommitItemReq")
	proto.RegisterType((*CommitItemResp)(nil), "backpackgo.CommitItemResp")
	proto.RegisterType((*TimeRangeReq)(nil), "backpackgo.TimeRangeReq")
	proto.RegisterType((*CountResp)(nil), "backpackgo.CountResp")
	proto.RegisterEnum("backpackgo.FreezeStatus", FreezeStatus_name, FreezeStatus_value)
	proto.RegisterEnum("backpackgo.Callback_Type", Callback_Type_name, Callback_Type_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// BackpackGoServerClient is the client API for BackpackGoServer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type BackpackGoServerClient interface {
	FreezeItem(ctx context.Context, in *FreezeItemReq, opts ...grpc.CallOption) (*FreezeItemResp, error)
	CommitItem(ctx context.Context, in *CommitItemReq, opts ...grpc.CallOption) (*CommitItemResp, error)
	GainTimeRange(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*CountResp, error)
}

type backpackGoServerClient struct {
	cc *grpc.ClientConn
}

func NewBackpackGoServerClient(cc *grpc.ClientConn) BackpackGoServerClient {
	return &backpackGoServerClient{cc}
}

func (c *backpackGoServerClient) FreezeItem(ctx context.Context, in *FreezeItemReq, opts ...grpc.CallOption) (*FreezeItemResp, error) {
	out := new(FreezeItemResp)
	err := c.cc.Invoke(ctx, "/backpackgo.BackpackGoServer/FreezeItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackGoServerClient) CommitItem(ctx context.Context, in *CommitItemReq, opts ...grpc.CallOption) (*CommitItemResp, error) {
	out := new(CommitItemResp)
	err := c.cc.Invoke(ctx, "/backpackgo.BackpackGoServer/CommitItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackGoServerClient) GainTimeRange(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*CountResp, error) {
	out := new(CountResp)
	err := c.cc.Invoke(ctx, "/backpackgo.BackpackGoServer/GainTimeRange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BackpackGoServerServer is the server API for BackpackGoServer service.
type BackpackGoServerServer interface {
	FreezeItem(context.Context, *FreezeItemReq) (*FreezeItemResp, error)
	CommitItem(context.Context, *CommitItemReq) (*CommitItemResp, error)
	GainTimeRange(context.Context, *TimeRangeReq) (*CountResp, error)
}

func RegisterBackpackGoServerServer(s *grpc.Server, srv BackpackGoServerServer) {
	s.RegisterService(&_BackpackGoServer_serviceDesc, srv)
}

func _BackpackGoServer_FreezeItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreezeItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackGoServerServer).FreezeItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpackgo.BackpackGoServer/FreezeItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackGoServerServer).FreezeItem(ctx, req.(*FreezeItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackGoServer_CommitItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommitItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackGoServerServer).CommitItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpackgo.BackpackGoServer/CommitItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackGoServerServer).CommitItem(ctx, req.(*CommitItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackGoServer_GainTimeRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackGoServerServer).GainTimeRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpackgo.BackpackGoServer/GainTimeRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackGoServerServer).GainTimeRange(ctx, req.(*TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _BackpackGoServer_serviceDesc = grpc.ServiceDesc{
	ServiceName: "backpackgo.BackpackGoServer",
	HandlerType: (*BackpackGoServerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "FreezeItem",
			Handler:    _BackpackGoServer_FreezeItem_Handler,
		},
		{
			MethodName: "CommitItem",
			Handler:    _BackpackGoServer_CommitItem_Handler,
		},
		{
			MethodName: "GainTimeRange",
			Handler:    _BackpackGoServer_GainTimeRange_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backpack-go.proto",
}

func init() { proto.RegisterFile("backpack-go.proto", fileDescriptor_backpack_go_1a4b25dc6acf3bab) }

var fileDescriptor_backpack_go_1a4b25dc6acf3bab = []byte{
	// 676 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x54, 0xc1, 0x4e, 0xdb, 0x40,
	0x10, 0x8d, 0x21, 0x24, 0xf6, 0x40, 0xa8, 0xbb, 0x82, 0xd6, 0x80, 0x10, 0xd4, 0x27, 0x54, 0x15,
	0x47, 0xa4, 0xaa, 0x7a, 0x4f, 0x08, 0xc8, 0x07, 0x42, 0x65, 0xc2, 0xa5, 0x97, 0xd4, 0xd8, 0x9b,
	0xc8, 0x8a, 0xed, 0x75, 0xed, 0x35, 0x12, 0xfd, 0x85, 0x7e, 0x45, 0xff, 0xad, 0xbf, 0x51, 0xa9,
	0x9a, 0x59, 0x47, 0x89, 0x49, 0xdb, 0x5b, 0x6f, 0x99, 0x37, 0xb3, 0x6f, 0xde, 0xcc, 0x1b, 0x07,
	0x5e, 0x3e, 0xf8, 0xc1, 0x3c, 0xf3, 0x83, 0xf9, 0xf9, 0x4c, 0x38, 0x59, 0x2e, 0xa4, 0x60, 0xb0,
	0x80, 0x66, 0xc2, 0x9e, 0x80, 0xee, 0x4a, 0x9e, 0xb8, 0xe9, 0x54, 0xb0, 0xd7, 0xd0, 0x8e, 0x24,
	0x4f, 0x26, 0x51, 0x68, 0x35, 0x4f, 0xb5, 0xb3, 0x8e, 0xd7, 0xc2, 0xd0, 0x0d, 0xd9, 0x11, 0x18,
	0x94, 0x90, 0x4f, 0x19, 0xb7, 0xb6, 0x28, 0xa5, 0x23, 0x30, 0x7e, 0xca, 0x38, 0x3b, 0x06, 0xa0,
	0x64, 0x20, 0xca, 0x54, 0x5a, 0x2d, 0xca, 0x52, 0xf9, 0x00, 0x01, 0xfb, 0xbb, 0x06, 0xfa, 0xc0,
	0x8f, 0x63, 0xec, 0xc9, 0xce, 0xa1, 0x49, 0x1c, 0xfa, 0xa9, 0x76, 0xb6, 0xdb, 0x3b, 0x70, 0x96,
	0x42, 0x9c, 0x45, 0x8d, 0x83, 0xa4, 0x1e, 0x95, 0x31, 0x13, 0x36, 0xcb, 0x3c, 0xb6, 0x8c, 0x53,
	0xed, 0xcc, 0xf0, 0xf0, 0x27, 0x36, 0x9b, 0x46, 0x79, 0x21, 0x27, 0x32, 0x4a, 0xb8, 0x05, 0xaa,
	0x19, 0x21, 0xe3, 0x28, 0xe1, 0xf6, 0x09, 0x34, 0x49, 0x53, 0x07, 0x8c, 0xfb, 0xd1, 0xe5, 0xf0,
	0xca, 0x1d, 0x0d, 0x2f, 0xcd, 0x06, 0xd3, 0xa1, 0x79, 0xed, 0x7d, 0x1a, 0x98, 0x9a, 0x7d, 0x01,
	0x7b, 0x57, 0x39, 0xe7, 0xdf, 0xf8, 0x6d, 0x1e, 0xf2, 0xdc, 0xe3, 0x45, 0x19, 0x4b, 0x8f, 0x7f,
	0x65, 0x07, 0xa0, 0x0b, 0x44, 0x70, 0x76, 0x8d, 0xda, 0xb5, 0x29, 0x76, 0x43, 0xbb, 0x84, 0xfd,
	0x3f, 0x3c, 0x29, 0x32, 0x66, 0x41, 0x3b, 0x10, 0xe9, 0x34, 0xca, 0x13, 0x7a, 0xa2, 0x7b, 0x8b,
	0x90, 0xbd, 0x81, 0x9d, 0x40, 0x24, 0x49, 0x24, 0xab, 0xa5, 0x6c, 0x90, 0xce, 0x6d, 0x85, 0xd1,
	0x5a, 0xd8, 0x09, 0x54, 0xa1, 0x9a, 0x64, 0x93, 0x2a, 0x40, 0x41, 0x34, 0xca, 0x2f, 0x0d, 0x3a,
	0xaa, 0x2f, 0xfa, 0xf3, 0x6f, 0x8d, 0xb4, 0xa8, 0x28, 0xac, 0xfa, 0xe0, 0x4f, 0xe4, 0x9f, 0xd2,
	0xeb, 0x1a, 0xbf, 0x82, 0x90, 0x9f, 0x5d, 0x54, 0x9e, 0x46, 0xe9, 0x54, 0x90, 0xdd, 0xdb, 0xbd,
	0xbd, 0x55, 0x3f, 0x16, 0x57, 0xa1, 0x9c, 0xa6, 0xfb, 0x38, 0x02, 0xa3, 0x10, 0x65, 0x1e, 0x70,
	0x54, 0x50, 0x9d, 0x81, 0x02, 0xd4, 0x8d, 0x88, 0x8c, 0xe7, 0xea, 0x46, 0xd4, 0x15, 0xe8, 0x08,
	0x90, 0x1f, 0x17, 0x60, 0x04, 0x7e, 0x1c, 0x4f, 0x90, 0xdf, 0x6a, 0xaf, 0x37, 0x5b, 0x98, 0xef,
	0xe9, 0x58, 0xd6, 0xf7, 0x83, 0xb9, 0x6d, 0xc2, 0xee, 0xea, 0xf8, 0x45, 0x66, 0xff, 0xd0, 0xa0,
	0x33, 0xa0, 0x05, 0xfd, 0x9f, 0x8d, 0x3c, 0xb3, 0xa4, 0xf9, 0xdc, 0x92, 0x35, 0x5b, 0xb7, 0xd6,
	0x6c, 0xb5, 0xbb, 0xb0, 0xbb, 0x2a, 0xb1, 0xc8, 0xf0, 0x62, 0x43, 0xee, 0xc7, 0x13, 0x29, 0xe6,
	0x3c, 0xad, 0x54, 0x1a, 0x88, 0x8c, 0x11, 0xb0, 0xbf, 0xc0, 0x0e, 0x72, 0x7b, 0x7e, 0x3a, 0xe3,
	0x38, 0xd2, 0x31, 0xc0, 0x03, 0x9f, 0x45, 0xa9, 0xd2, 0x80, 0xe5, 0x9b, 0x9e, 0x41, 0x08, 0x49,
	0x38, 0x00, 0x9d, 0xa7, 0xa1, 0x4a, 0x6e, 0x50, 0xb2, 0xcd, 0xd3, 0x90, 0x52, 0xaf, 0xa0, 0x95,
	0xf9, 0xb9, 0x9f, 0x14, 0x34, 0x9a, 0xe1, 0x55, 0x91, 0xfd, 0x11, 0x0c, 0xd2, 0x46, 0x6a, 0xf6,
	0x60, 0x4b, 0x69, 0xd7, 0x48, 0xbb, 0x0a, 0x10, 0x7d, 0xf4, 0xe3, 0x92, 0x57, 0xeb, 0x52, 0xc1,
	0x5b, 0x07, 0x76, 0x94, 0x03, 0x77, 0xd2, 0x97, 0x65, 0x81, 0x1f, 0xd5, 0x70, 0x74, 0x7f, 0x33,
	0x71, 0x47, 0xee, 0xd8, 0x6c, 0xb0, 0x17, 0xb0, 0x4d, 0xe1, 0xe0, 0xf6, 0xe6, 0xc6, 0x1d, 0x9b,
	0x5a, 0xef, 0xa7, 0x06, 0x66, 0xbf, 0xf2, 0xf4, 0x5a, 0xdc, 0xf1, 0xfc, 0x91, 0xe7, 0x6c, 0x08,
	0xb0, 0xb4, 0x91, 0xd5, 0xbe, 0xf8, 0xda, 0x75, 0x1f, 0x1e, 0xfe, 0x2d, 0x55, 0x64, 0x76, 0x03,
	0x69, 0x96, 0x7b, 0xad, 0xd3, 0xd4, 0x4e, 0xa2, 0x4e, 0x53, 0xb7, 0xc2, 0x6e, 0xb0, 0x3e, 0x74,
	0xae, 0x7d, 0xb5, 0x4a, 0xda, 0x38, 0xb3, 0x56, 0xcb, 0x57, 0x8d, 0x38, 0xdc, 0xaf, 0x13, 0x55,
	0x0b, 0xb4, 0x1b, 0x7d, 0xe7, 0xf3, 0xbb, 0x99, 0x88, 0xfd, 0x74, 0xe6, 0x7c, 0xe8, 0x49, 0xe9,
	0x04, 0x22, 0xe9, 0xd2, 0xdf, 0x6a, 0x20, 0xe2, 0x6e, 0xc1, 0xf3, 0xc7, 0x28, 0xe0, 0x45, 0x77,
	0xf9, 0xf6, 0xa1, 0x45, 0xd9, 0xf7, 0xbf, 0x03, 0x00, 0x00, 0xff, 0xff, 0xfb, 0x2d, 0x67, 0x6e,
	0x89, 0x05, 0x00, 0x00,
}
