// Code generated by protoc-gen-gogo.
// source: src/channelrobotsvr/channelrobot.proto
// DO NOT EDIT!

/*
	Package ChannelRobot is a generated protocol buffer package.

	It is generated from these files:
		src/channelrobotsvr/channelrobot.proto

	It has these top-level messages:
		AddChannelRobotAccountReq
		JoinChannelNotifyReq
		LeaveChannelNotifyReq
		ControlRobotInfo
		CreateRobotUserReq
		AddSpecifyChannelReq
		DelSpecifyChannelReq
		GetSpecifyChannelResp
		GetChannelRobotListReq
		GetChannelRobotListResp
		GetChannelRobotSizeReq
		GetChannelRobotSizeResp
		BatGetChannelRobotSizeReq
		BatGetChannelRobotSizeResp
		RobotCntInfo
		GetAllChannelRobotCntReq
		GetAllChannelRobotCntResp
*/
package ChannelRobot

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type ChannelStatus int32

const (
	ChannelStatus_EnterChannel ChannelStatus = 1
	ChannelStatus_LeaveChannel ChannelStatus = 2
)

var ChannelStatus_name = map[int32]string{
	1: "EnterChannel",
	2: "LeaveChannel",
}
var ChannelStatus_value = map[string]int32{
	"EnterChannel": 1,
	"LeaveChannel": 2,
}

func (x ChannelStatus) Enum() *ChannelStatus {
	p := new(ChannelStatus)
	*p = x
	return p
}
func (x ChannelStatus) String() string {
	return proto.EnumName(ChannelStatus_name, int32(x))
}
func (x *ChannelStatus) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ChannelStatus_value, data, "ChannelStatus")
	if err != nil {
		return err
	}
	*x = ChannelStatus(value)
	return nil
}
func (ChannelStatus) EnumDescriptor() ([]byte, []int) { return fileDescriptorChannelrobot, []int{0} }

type AddChannelRobotAccountReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *AddChannelRobotAccountReq) Reset()         { *m = AddChannelRobotAccountReq{} }
func (m *AddChannelRobotAccountReq) String() string { return proto.CompactTextString(m) }
func (*AddChannelRobotAccountReq) ProtoMessage()    {}
func (*AddChannelRobotAccountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrobot, []int{0}
}

func (m *AddChannelRobotAccountReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type JoinChannelNotifyReq struct {
	ChannelId     uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	JoinAmount    uint32 `protobuf:"varint,2,req,name=join_amount,json=joinAmount" json:"join_amount"`
	RemainUserCnt uint32 `protobuf:"varint,3,opt,name=remain_user_cnt,json=remainUserCnt" json:"remain_user_cnt"`
}

func (m *JoinChannelNotifyReq) Reset()                    { *m = JoinChannelNotifyReq{} }
func (m *JoinChannelNotifyReq) String() string            { return proto.CompactTextString(m) }
func (*JoinChannelNotifyReq) ProtoMessage()               {}
func (*JoinChannelNotifyReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelrobot, []int{1} }

func (m *JoinChannelNotifyReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *JoinChannelNotifyReq) GetJoinAmount() uint32 {
	if m != nil {
		return m.JoinAmount
	}
	return 0
}

func (m *JoinChannelNotifyReq) GetRemainUserCnt() uint32 {
	if m != nil {
		return m.RemainUserCnt
	}
	return 0
}

type LeaveChannelNotifyReq struct {
	ChannelId     uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	LeaveAmount   uint32 `protobuf:"varint,2,req,name=leave_amount,json=leaveAmount" json:"leave_amount"`
	RemainUserCnt uint32 `protobuf:"varint,3,opt,name=remain_user_cnt,json=remainUserCnt" json:"remain_user_cnt"`
	IsRobotLeave  bool   `protobuf:"varint,4,opt,name=is_robot_leave,json=isRobotLeave" json:"is_robot_leave"`
}

func (m *LeaveChannelNotifyReq) Reset()         { *m = LeaveChannelNotifyReq{} }
func (m *LeaveChannelNotifyReq) String() string { return proto.CompactTextString(m) }
func (*LeaveChannelNotifyReq) ProtoMessage()    {}
func (*LeaveChannelNotifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrobot, []int{2}
}

func (m *LeaveChannelNotifyReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *LeaveChannelNotifyReq) GetLeaveAmount() uint32 {
	if m != nil {
		return m.LeaveAmount
	}
	return 0
}

func (m *LeaveChannelNotifyReq) GetRemainUserCnt() uint32 {
	if m != nil {
		return m.RemainUserCnt
	}
	return 0
}

func (m *LeaveChannelNotifyReq) GetIsRobotLeave() bool {
	if m != nil {
		return m.IsRobotLeave
	}
	return false
}

type ControlRobotInfo struct {
	BaseNum         uint32 `protobuf:"varint,1,req,name=base_num,json=baseNum" json:"base_num"`
	IntoChannelTime uint32 `protobuf:"varint,2,req,name=into_channel_time,json=intoChannelTime" json:"into_channel_time"`
	Left            uint32 `protobuf:"varint,3,req,name=left" json:"left"`
	UserCount       uint32 `protobuf:"varint,4,req,name=user_count,json=userCount" json:"user_count"`
}

func (m *ControlRobotInfo) Reset()                    { *m = ControlRobotInfo{} }
func (m *ControlRobotInfo) String() string            { return proto.CompactTextString(m) }
func (*ControlRobotInfo) ProtoMessage()               {}
func (*ControlRobotInfo) Descriptor() ([]byte, []int) { return fileDescriptorChannelrobot, []int{3} }

func (m *ControlRobotInfo) GetBaseNum() uint32 {
	if m != nil {
		return m.BaseNum
	}
	return 0
}

func (m *ControlRobotInfo) GetIntoChannelTime() uint32 {
	if m != nil {
		return m.IntoChannelTime
	}
	return 0
}

func (m *ControlRobotInfo) GetLeft() uint32 {
	if m != nil {
		return m.Left
	}
	return 0
}

func (m *ControlRobotInfo) GetUserCount() uint32 {
	if m != nil {
		return m.UserCount
	}
	return 0
}

type CreateRobotUserReq struct {
	Num uint32 `protobuf:"varint,1,req,name=num" json:"num"`
	Sex uint32 `protobuf:"varint,2,opt,name=sex" json:"sex"`
}

func (m *CreateRobotUserReq) Reset()                    { *m = CreateRobotUserReq{} }
func (m *CreateRobotUserReq) String() string            { return proto.CompactTextString(m) }
func (*CreateRobotUserReq) ProtoMessage()               {}
func (*CreateRobotUserReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelrobot, []int{4} }

func (m *CreateRobotUserReq) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *CreateRobotUserReq) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type AddSpecifyChannelReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *AddSpecifyChannelReq) Reset()                    { *m = AddSpecifyChannelReq{} }
func (m *AddSpecifyChannelReq) String() string            { return proto.CompactTextString(m) }
func (*AddSpecifyChannelReq) ProtoMessage()               {}
func (*AddSpecifyChannelReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelrobot, []int{5} }

func (m *AddSpecifyChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type DelSpecifyChannelReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *DelSpecifyChannelReq) Reset()                    { *m = DelSpecifyChannelReq{} }
func (m *DelSpecifyChannelReq) String() string            { return proto.CompactTextString(m) }
func (*DelSpecifyChannelReq) ProtoMessage()               {}
func (*DelSpecifyChannelReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelrobot, []int{6} }

func (m *DelSpecifyChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetSpecifyChannelResp struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *GetSpecifyChannelResp) Reset()         { *m = GetSpecifyChannelResp{} }
func (m *GetSpecifyChannelResp) String() string { return proto.CompactTextString(m) }
func (*GetSpecifyChannelResp) ProtoMessage()    {}
func (*GetSpecifyChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrobot, []int{7}
}

func (m *GetSpecifyChannelResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type GetChannelRobotListReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetChannelRobotListReq) Reset()         { *m = GetChannelRobotListReq{} }
func (m *GetChannelRobotListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelRobotListReq) ProtoMessage()    {}
func (*GetChannelRobotListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrobot, []int{8}
}

func (m *GetChannelRobotListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelRobotListResp struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *GetChannelRobotListResp) Reset()         { *m = GetChannelRobotListResp{} }
func (m *GetChannelRobotListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelRobotListResp) ProtoMessage()    {}
func (*GetChannelRobotListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrobot, []int{9}
}

func (m *GetChannelRobotListResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type GetChannelRobotSizeReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetChannelRobotSizeReq) Reset()         { *m = GetChannelRobotSizeReq{} }
func (m *GetChannelRobotSizeReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelRobotSizeReq) ProtoMessage()    {}
func (*GetChannelRobotSizeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrobot, []int{10}
}

func (m *GetChannelRobotSizeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelRobotSizeResp struct {
	RobotSize uint32 `protobuf:"varint,1,req,name=robot_size,json=robotSize" json:"robot_size"`
}

func (m *GetChannelRobotSizeResp) Reset()         { *m = GetChannelRobotSizeResp{} }
func (m *GetChannelRobotSizeResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelRobotSizeResp) ProtoMessage()    {}
func (*GetChannelRobotSizeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrobot, []int{11}
}

func (m *GetChannelRobotSizeResp) GetRobotSize() uint32 {
	if m != nil {
		return m.RobotSize
	}
	return 0
}

type BatGetChannelRobotSizeReq struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *BatGetChannelRobotSizeReq) Reset()         { *m = BatGetChannelRobotSizeReq{} }
func (m *BatGetChannelRobotSizeReq) String() string { return proto.CompactTextString(m) }
func (*BatGetChannelRobotSizeReq) ProtoMessage()    {}
func (*BatGetChannelRobotSizeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrobot, []int{12}
}

func (m *BatGetChannelRobotSizeReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type BatGetChannelRobotSizeResp struct {
	RobotSizeList []uint32 `protobuf:"varint,1,rep,name=robot_size_list,json=robotSizeList" json:"robot_size_list,omitempty"`
}

func (m *BatGetChannelRobotSizeResp) Reset()         { *m = BatGetChannelRobotSizeResp{} }
func (m *BatGetChannelRobotSizeResp) String() string { return proto.CompactTextString(m) }
func (*BatGetChannelRobotSizeResp) ProtoMessage()    {}
func (*BatGetChannelRobotSizeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrobot, []int{13}
}

func (m *BatGetChannelRobotSizeResp) GetRobotSizeList() []uint32 {
	if m != nil {
		return m.RobotSizeList
	}
	return nil
}

type RobotCntInfo struct {
	Cid      string `protobuf:"bytes,1,req,name=cid" json:"cid"`
	RobotCnt int32  `protobuf:"varint,2,req,name=robot_cnt,json=robotCnt" json:"robot_cnt"`
}

func (m *RobotCntInfo) Reset()                    { *m = RobotCntInfo{} }
func (m *RobotCntInfo) String() string            { return proto.CompactTextString(m) }
func (*RobotCntInfo) ProtoMessage()               {}
func (*RobotCntInfo) Descriptor() ([]byte, []int) { return fileDescriptorChannelrobot, []int{14} }

func (m *RobotCntInfo) GetCid() string {
	if m != nil {
		return m.Cid
	}
	return ""
}

func (m *RobotCntInfo) GetRobotCnt() int32 {
	if m != nil {
		return m.RobotCnt
	}
	return 0
}

// 获取所有房间的机器人数量
type GetAllChannelRobotCntReq struct {
}

func (m *GetAllChannelRobotCntReq) Reset()         { *m = GetAllChannelRobotCntReq{} }
func (m *GetAllChannelRobotCntReq) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelRobotCntReq) ProtoMessage()    {}
func (*GetAllChannelRobotCntReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrobot, []int{15}
}

type GetAllChannelRobotCntResp struct {
	CntInfo []*RobotCntInfo `protobuf:"bytes,1,rep,name=cnt_info,json=cntInfo" json:"cnt_info,omitempty"`
}

func (m *GetAllChannelRobotCntResp) Reset()         { *m = GetAllChannelRobotCntResp{} }
func (m *GetAllChannelRobotCntResp) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelRobotCntResp) ProtoMessage()    {}
func (*GetAllChannelRobotCntResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrobot, []int{16}
}

func (m *GetAllChannelRobotCntResp) GetCntInfo() []*RobotCntInfo {
	if m != nil {
		return m.CntInfo
	}
	return nil
}

func init() {
	proto.RegisterType((*AddChannelRobotAccountReq)(nil), "ChannelRobot.AddChannelRobotAccountReq")
	proto.RegisterType((*JoinChannelNotifyReq)(nil), "ChannelRobot.JoinChannelNotifyReq")
	proto.RegisterType((*LeaveChannelNotifyReq)(nil), "ChannelRobot.LeaveChannelNotifyReq")
	proto.RegisterType((*ControlRobotInfo)(nil), "ChannelRobot.ControlRobotInfo")
	proto.RegisterType((*CreateRobotUserReq)(nil), "ChannelRobot.CreateRobotUserReq")
	proto.RegisterType((*AddSpecifyChannelReq)(nil), "ChannelRobot.AddSpecifyChannelReq")
	proto.RegisterType((*DelSpecifyChannelReq)(nil), "ChannelRobot.DelSpecifyChannelReq")
	proto.RegisterType((*GetSpecifyChannelResp)(nil), "ChannelRobot.GetSpecifyChannelResp")
	proto.RegisterType((*GetChannelRobotListReq)(nil), "ChannelRobot.GetChannelRobotListReq")
	proto.RegisterType((*GetChannelRobotListResp)(nil), "ChannelRobot.GetChannelRobotListResp")
	proto.RegisterType((*GetChannelRobotSizeReq)(nil), "ChannelRobot.GetChannelRobotSizeReq")
	proto.RegisterType((*GetChannelRobotSizeResp)(nil), "ChannelRobot.GetChannelRobotSizeResp")
	proto.RegisterType((*BatGetChannelRobotSizeReq)(nil), "ChannelRobot.BatGetChannelRobotSizeReq")
	proto.RegisterType((*BatGetChannelRobotSizeResp)(nil), "ChannelRobot.BatGetChannelRobotSizeResp")
	proto.RegisterType((*RobotCntInfo)(nil), "ChannelRobot.RobotCntInfo")
	proto.RegisterType((*GetAllChannelRobotCntReq)(nil), "ChannelRobot.GetAllChannelRobotCntReq")
	proto.RegisterType((*GetAllChannelRobotCntResp)(nil), "ChannelRobot.GetAllChannelRobotCntResp")
	proto.RegisterEnum("ChannelRobot.ChannelStatus", ChannelStatus_name, ChannelStatus_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for ChannelRobot service

type ChannelRobotClient interface {
	AddChannelRobotAccount(ctx context.Context, in *AddChannelRobotAccountReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	JoinChannelNotify(ctx context.Context, in *JoinChannelNotifyReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	LeaveChannelNotify(ctx context.Context, in *LeaveChannelNotifyReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	CreateRobotUser(ctx context.Context, in *CreateRobotUserReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	AddSpecifyChannel(ctx context.Context, in *AddSpecifyChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelSpecifyChannel(ctx context.Context, in *DelSpecifyChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetSpecifyChannel(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetSpecifyChannelResp, error)
	GetChannelRobotList(ctx context.Context, in *GetChannelRobotListReq, opts ...grpc.CallOption) (*GetChannelRobotListResp, error)
	GetChannelRobotSize(ctx context.Context, in *GetChannelRobotSizeReq, opts ...grpc.CallOption) (*GetChannelRobotSizeResp, error)
	BatGetChannelRobotSize(ctx context.Context, in *BatGetChannelRobotSizeReq, opts ...grpc.CallOption) (*BatGetChannelRobotSizeResp, error)
	GetAllChannelRobotCnt(ctx context.Context, in *GetAllChannelRobotCntReq, opts ...grpc.CallOption) (*GetAllChannelRobotCntResp, error)
}

type channelRobotClient struct {
	cc *grpc.ClientConn
}

func NewChannelRobotClient(cc *grpc.ClientConn) ChannelRobotClient {
	return &channelRobotClient{cc}
}

func (c *channelRobotClient) AddChannelRobotAccount(ctx context.Context, in *AddChannelRobotAccountReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/ChannelRobot.ChannelRobot/AddChannelRobotAccount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRobotClient) JoinChannelNotify(ctx context.Context, in *JoinChannelNotifyReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/ChannelRobot.ChannelRobot/JoinChannelNotify", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRobotClient) LeaveChannelNotify(ctx context.Context, in *LeaveChannelNotifyReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/ChannelRobot.ChannelRobot/LeaveChannelNotify", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRobotClient) CreateRobotUser(ctx context.Context, in *CreateRobotUserReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/ChannelRobot.ChannelRobot/CreateRobotUser", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRobotClient) AddSpecifyChannel(ctx context.Context, in *AddSpecifyChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/ChannelRobot.ChannelRobot/AddSpecifyChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRobotClient) DelSpecifyChannel(ctx context.Context, in *DelSpecifyChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/ChannelRobot.ChannelRobot/DelSpecifyChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRobotClient) GetSpecifyChannel(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetSpecifyChannelResp, error) {
	out := new(GetSpecifyChannelResp)
	err := grpc.Invoke(ctx, "/ChannelRobot.ChannelRobot/GetSpecifyChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRobotClient) GetChannelRobotList(ctx context.Context, in *GetChannelRobotListReq, opts ...grpc.CallOption) (*GetChannelRobotListResp, error) {
	out := new(GetChannelRobotListResp)
	err := grpc.Invoke(ctx, "/ChannelRobot.ChannelRobot/GetChannelRobotList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRobotClient) GetChannelRobotSize(ctx context.Context, in *GetChannelRobotSizeReq, opts ...grpc.CallOption) (*GetChannelRobotSizeResp, error) {
	out := new(GetChannelRobotSizeResp)
	err := grpc.Invoke(ctx, "/ChannelRobot.ChannelRobot/GetChannelRobotSize", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRobotClient) BatGetChannelRobotSize(ctx context.Context, in *BatGetChannelRobotSizeReq, opts ...grpc.CallOption) (*BatGetChannelRobotSizeResp, error) {
	out := new(BatGetChannelRobotSizeResp)
	err := grpc.Invoke(ctx, "/ChannelRobot.ChannelRobot/BatGetChannelRobotSize", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRobotClient) GetAllChannelRobotCnt(ctx context.Context, in *GetAllChannelRobotCntReq, opts ...grpc.CallOption) (*GetAllChannelRobotCntResp, error) {
	out := new(GetAllChannelRobotCntResp)
	err := grpc.Invoke(ctx, "/ChannelRobot.ChannelRobot/GetAllChannelRobotCnt", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for ChannelRobot service

type ChannelRobotServer interface {
	AddChannelRobotAccount(context.Context, *AddChannelRobotAccountReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	JoinChannelNotify(context.Context, *JoinChannelNotifyReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	LeaveChannelNotify(context.Context, *LeaveChannelNotifyReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	CreateRobotUser(context.Context, *CreateRobotUserReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	AddSpecifyChannel(context.Context, *AddSpecifyChannelReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelSpecifyChannel(context.Context, *DelSpecifyChannelReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetSpecifyChannel(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*GetSpecifyChannelResp, error)
	GetChannelRobotList(context.Context, *GetChannelRobotListReq) (*GetChannelRobotListResp, error)
	GetChannelRobotSize(context.Context, *GetChannelRobotSizeReq) (*GetChannelRobotSizeResp, error)
	BatGetChannelRobotSize(context.Context, *BatGetChannelRobotSizeReq) (*BatGetChannelRobotSizeResp, error)
	GetAllChannelRobotCnt(context.Context, *GetAllChannelRobotCntReq) (*GetAllChannelRobotCntResp, error)
}

func RegisterChannelRobotServer(s *grpc.Server, srv ChannelRobotServer) {
	s.RegisterService(&_ChannelRobot_serviceDesc, srv)
}

func _ChannelRobot_AddChannelRobotAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelRobotAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRobotServer).AddChannelRobotAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelRobot.ChannelRobot/AddChannelRobotAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRobotServer).AddChannelRobotAccount(ctx, req.(*AddChannelRobotAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRobot_JoinChannelNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinChannelNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRobotServer).JoinChannelNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelRobot.ChannelRobot/JoinChannelNotify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRobotServer).JoinChannelNotify(ctx, req.(*JoinChannelNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRobot_LeaveChannelNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LeaveChannelNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRobotServer).LeaveChannelNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelRobot.ChannelRobot/LeaveChannelNotify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRobotServer).LeaveChannelNotify(ctx, req.(*LeaveChannelNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRobot_CreateRobotUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRobotUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRobotServer).CreateRobotUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelRobot.ChannelRobot/CreateRobotUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRobotServer).CreateRobotUser(ctx, req.(*CreateRobotUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRobot_AddSpecifyChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSpecifyChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRobotServer).AddSpecifyChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelRobot.ChannelRobot/AddSpecifyChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRobotServer).AddSpecifyChannel(ctx, req.(*AddSpecifyChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRobot_DelSpecifyChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelSpecifyChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRobotServer).DelSpecifyChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelRobot.ChannelRobot/DelSpecifyChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRobotServer).DelSpecifyChannel(ctx, req.(*DelSpecifyChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRobot_GetSpecifyChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRobotServer).GetSpecifyChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelRobot.ChannelRobot/GetSpecifyChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRobotServer).GetSpecifyChannel(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRobot_GetChannelRobotList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelRobotListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRobotServer).GetChannelRobotList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelRobot.ChannelRobot/GetChannelRobotList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRobotServer).GetChannelRobotList(ctx, req.(*GetChannelRobotListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRobot_GetChannelRobotSize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelRobotSizeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRobotServer).GetChannelRobotSize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelRobot.ChannelRobot/GetChannelRobotSize",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRobotServer).GetChannelRobotSize(ctx, req.(*GetChannelRobotSizeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRobot_BatGetChannelRobotSize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetChannelRobotSizeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRobotServer).BatGetChannelRobotSize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelRobot.ChannelRobot/BatGetChannelRobotSize",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRobotServer).BatGetChannelRobotSize(ctx, req.(*BatGetChannelRobotSizeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRobot_GetAllChannelRobotCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllChannelRobotCntReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRobotServer).GetAllChannelRobotCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelRobot.ChannelRobot/GetAllChannelRobotCnt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRobotServer).GetAllChannelRobotCnt(ctx, req.(*GetAllChannelRobotCntReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelRobot_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ChannelRobot.ChannelRobot",
	HandlerType: (*ChannelRobotServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddChannelRobotAccount",
			Handler:    _ChannelRobot_AddChannelRobotAccount_Handler,
		},
		{
			MethodName: "JoinChannelNotify",
			Handler:    _ChannelRobot_JoinChannelNotify_Handler,
		},
		{
			MethodName: "LeaveChannelNotify",
			Handler:    _ChannelRobot_LeaveChannelNotify_Handler,
		},
		{
			MethodName: "CreateRobotUser",
			Handler:    _ChannelRobot_CreateRobotUser_Handler,
		},
		{
			MethodName: "AddSpecifyChannel",
			Handler:    _ChannelRobot_AddSpecifyChannel_Handler,
		},
		{
			MethodName: "DelSpecifyChannel",
			Handler:    _ChannelRobot_DelSpecifyChannel_Handler,
		},
		{
			MethodName: "GetSpecifyChannel",
			Handler:    _ChannelRobot_GetSpecifyChannel_Handler,
		},
		{
			MethodName: "GetChannelRobotList",
			Handler:    _ChannelRobot_GetChannelRobotList_Handler,
		},
		{
			MethodName: "GetChannelRobotSize",
			Handler:    _ChannelRobot_GetChannelRobotSize_Handler,
		},
		{
			MethodName: "BatGetChannelRobotSize",
			Handler:    _ChannelRobot_BatGetChannelRobotSize_Handler,
		},
		{
			MethodName: "GetAllChannelRobotCnt",
			Handler:    _ChannelRobot_GetAllChannelRobotCnt_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/channelrobotsvr/channelrobot.proto",
}

func (m *AddChannelRobotAccountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddChannelRobotAccountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrobot(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *JoinChannelNotifyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *JoinChannelNotifyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrobot(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrobot(dAtA, i, uint64(m.JoinAmount))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelrobot(dAtA, i, uint64(m.RemainUserCnt))
	return i, nil
}

func (m *LeaveChannelNotifyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LeaveChannelNotifyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrobot(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrobot(dAtA, i, uint64(m.LeaveAmount))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelrobot(dAtA, i, uint64(m.RemainUserCnt))
	dAtA[i] = 0x20
	i++
	if m.IsRobotLeave {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ControlRobotInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ControlRobotInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrobot(dAtA, i, uint64(m.BaseNum))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrobot(dAtA, i, uint64(m.IntoChannelTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelrobot(dAtA, i, uint64(m.Left))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelrobot(dAtA, i, uint64(m.UserCount))
	return i, nil
}

func (m *CreateRobotUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateRobotUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrobot(dAtA, i, uint64(m.Num))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrobot(dAtA, i, uint64(m.Sex))
	return i, nil
}

func (m *AddSpecifyChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddSpecifyChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrobot(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *DelSpecifyChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelSpecifyChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrobot(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetSpecifyChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSpecifyChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrobot(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetChannelRobotListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelRobotListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrobot(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetChannelRobotListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelRobotListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrobot(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetChannelRobotSizeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelRobotSizeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrobot(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetChannelRobotSizeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelRobotSizeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrobot(dAtA, i, uint64(m.RobotSize))
	return i, nil
}

func (m *BatGetChannelRobotSizeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetChannelRobotSizeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrobot(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatGetChannelRobotSizeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetChannelRobotSizeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RobotSizeList) > 0 {
		for _, num := range m.RobotSizeList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrobot(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *RobotCntInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RobotCntInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintChannelrobot(dAtA, i, uint64(len(m.Cid)))
	i += copy(dAtA[i:], m.Cid)
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrobot(dAtA, i, uint64(m.RobotCnt))
	return i, nil
}

func (m *GetAllChannelRobotCntReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllChannelRobotCntReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetAllChannelRobotCntResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllChannelRobotCntResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CntInfo) > 0 {
		for _, msg := range m.CntInfo {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelrobot(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func encodeFixed64Channelrobot(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Channelrobot(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintChannelrobot(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *AddChannelRobotAccountReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovChannelrobot(uint64(e))
		}
	}
	return n
}

func (m *JoinChannelNotifyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrobot(uint64(m.ChannelId))
	n += 1 + sovChannelrobot(uint64(m.JoinAmount))
	n += 1 + sovChannelrobot(uint64(m.RemainUserCnt))
	return n
}

func (m *LeaveChannelNotifyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrobot(uint64(m.ChannelId))
	n += 1 + sovChannelrobot(uint64(m.LeaveAmount))
	n += 1 + sovChannelrobot(uint64(m.RemainUserCnt))
	n += 2
	return n
}

func (m *ControlRobotInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrobot(uint64(m.BaseNum))
	n += 1 + sovChannelrobot(uint64(m.IntoChannelTime))
	n += 1 + sovChannelrobot(uint64(m.Left))
	n += 1 + sovChannelrobot(uint64(m.UserCount))
	return n
}

func (m *CreateRobotUserReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrobot(uint64(m.Num))
	n += 1 + sovChannelrobot(uint64(m.Sex))
	return n
}

func (m *AddSpecifyChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrobot(uint64(m.ChannelId))
	return n
}

func (m *DelSpecifyChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrobot(uint64(m.ChannelId))
	return n
}

func (m *GetSpecifyChannelResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrobot(uint64(e))
		}
	}
	return n
}

func (m *GetChannelRobotListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrobot(uint64(m.ChannelId))
	return n
}

func (m *GetChannelRobotListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovChannelrobot(uint64(e))
		}
	}
	return n
}

func (m *GetChannelRobotSizeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrobot(uint64(m.ChannelId))
	return n
}

func (m *GetChannelRobotSizeResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrobot(uint64(m.RobotSize))
	return n
}

func (m *BatGetChannelRobotSizeReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrobot(uint64(e))
		}
	}
	return n
}

func (m *BatGetChannelRobotSizeResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RobotSizeList) > 0 {
		for _, e := range m.RobotSizeList {
			n += 1 + sovChannelrobot(uint64(e))
		}
	}
	return n
}

func (m *RobotCntInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.Cid)
	n += 1 + l + sovChannelrobot(uint64(l))
	n += 1 + sovChannelrobot(uint64(m.RobotCnt))
	return n
}

func (m *GetAllChannelRobotCntReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetAllChannelRobotCntResp) Size() (n int) {
	var l int
	_ = l
	if len(m.CntInfo) > 0 {
		for _, e := range m.CntInfo {
			l = e.Size()
			n += 1 + l + sovChannelrobot(uint64(l))
		}
	}
	return n
}

func sovChannelrobot(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozChannelrobot(x uint64) (n int) {
	return sovChannelrobot(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *AddChannelRobotAccountReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrobot
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddChannelRobotAccountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddChannelRobotAccountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrobot
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrobot
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrobot
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrobot
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrobot(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrobot
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *JoinChannelNotifyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrobot
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: JoinChannelNotifyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: JoinChannelNotifyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JoinAmount", wireType)
			}
			m.JoinAmount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JoinAmount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RemainUserCnt", wireType)
			}
			m.RemainUserCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainUserCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrobot(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrobot
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("join_amount")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LeaveChannelNotifyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrobot
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LeaveChannelNotifyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LeaveChannelNotifyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeaveAmount", wireType)
			}
			m.LeaveAmount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeaveAmount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RemainUserCnt", wireType)
			}
			m.RemainUserCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainUserCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsRobotLeave", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsRobotLeave = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrobot(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrobot
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("leave_amount")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ControlRobotInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrobot
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ControlRobotInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ControlRobotInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseNum", wireType)
			}
			m.BaseNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BaseNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IntoChannelTime", wireType)
			}
			m.IntoChannelTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IntoChannelTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Left", wireType)
			}
			m.Left = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Left |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserCount", wireType)
			}
			m.UserCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrobot(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrobot
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("base_num")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("into_channel_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("left")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("user_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateRobotUserReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrobot
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateRobotUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateRobotUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Num", wireType)
			}
			m.Num = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Num |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrobot(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrobot
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("num")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddSpecifyChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrobot
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddSpecifyChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddSpecifyChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrobot(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrobot
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelSpecifyChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrobot
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelSpecifyChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelSpecifyChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrobot(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrobot
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSpecifyChannelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrobot
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSpecifyChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSpecifyChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrobot
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrobot
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrobot
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrobot
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrobot(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrobot
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelRobotListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrobot
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelRobotListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelRobotListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrobot(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrobot
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelRobotListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrobot
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelRobotListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelRobotListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrobot
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrobot
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrobot
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrobot
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrobot(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrobot
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelRobotSizeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrobot
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelRobotSizeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelRobotSizeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrobot(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrobot
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelRobotSizeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrobot
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelRobotSizeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelRobotSizeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RobotSize", wireType)
			}
			m.RobotSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RobotSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrobot(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrobot
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("robot_size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetChannelRobotSizeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrobot
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetChannelRobotSizeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetChannelRobotSizeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrobot
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrobot
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrobot
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrobot
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrobot(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrobot
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetChannelRobotSizeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrobot
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetChannelRobotSizeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetChannelRobotSizeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrobot
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.RobotSizeList = append(m.RobotSizeList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrobot
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrobot
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrobot
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.RobotSizeList = append(m.RobotSizeList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field RobotSizeList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrobot(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrobot
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RobotCntInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrobot
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RobotCntInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RobotCntInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrobot
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Cid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RobotCnt", wireType)
			}
			m.RobotCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RobotCnt |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrobot(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrobot
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("cid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("robot_cnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllChannelRobotCntReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrobot
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllChannelRobotCntReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllChannelRobotCntReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrobot(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrobot
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllChannelRobotCntResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrobot
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllChannelRobotCntResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllChannelRobotCntResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CntInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrobot
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CntInfo = append(m.CntInfo, &RobotCntInfo{})
			if err := m.CntInfo[len(m.CntInfo)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrobot(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrobot
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipChannelrobot(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowChannelrobot
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelrobot
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthChannelrobot
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowChannelrobot
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipChannelrobot(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthChannelrobot = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowChannelrobot   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/channelrobotsvr/channelrobot.proto", fileDescriptorChannelrobot) }

var fileDescriptorChannelrobot = []byte{
	// 932 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x55, 0x4f, 0x73, 0xdb, 0x44,
	0x1c, 0x8d, 0x6c, 0xb7, 0x71, 0x7e, 0xb1, 0xb1, 0xb3, 0xb4, 0xc6, 0x11, 0x4c, 0x2a, 0xd4, 0x36,
	0xc9, 0x74, 0xa8, 0xc3, 0x94, 0x3f, 0x07, 0x28, 0x65, 0x6c, 0xa5, 0x93, 0x09, 0x74, 0x3a, 0x8c,
	0x03, 0x67, 0x8d, 0x22, 0xad, 0xe9, 0x52, 0x69, 0x25, 0xb4, 0xab, 0x10, 0x67, 0x38, 0x70, 0xe0,
	0xc0, 0x70, 0x60, 0x3a, 0x7c, 0x86, 0x1c, 0xf9, 0x0e, 0x5c, 0x7b, 0xe4, 0x13, 0x30, 0x4c, 0xb8,
	0xe4, 0x63, 0x74, 0x76, 0x25, 0xc7, 0x92, 0x25, 0xdb, 0x9a, 0x5c, 0x32, 0xf1, 0xdb, 0xdf, 0xdb,
	0xf7, 0xf6, 0xf7, 0xdb, 0x7d, 0x82, 0x6d, 0x16, 0xda, 0x7b, 0xf6, 0x0b, 0x8b, 0x52, 0xec, 0x86,
	0xfe, 0xb1, 0xcf, 0xd9, 0x49, 0x98, 0xf9, 0xdd, 0x0b, 0x42, 0x9f, 0xfb, 0xa8, 0x61, 0xc4, 0xd8,
	0x50, 0x60, 0xea, 0x3d, 0xdb, 0xf7, 0x3c, 0x9f, 0xee, 0x71, 0xf7, 0x24, 0x20, 0xf6, 0x4b, 0x17,
	0xef, 0xb1, 0x97, 0xc7, 0x11, 0x71, 0x39, 0xa1, 0x7c, 0x1c, 0xe0, 0x98, 0xa3, 0x7f, 0x0a, 0x9b,
	0x7d, 0xc7, 0x49, 0x13, 0xfb, 0xb6, 0xed, 0x47, 0x94, 0x0f, 0xf1, 0x8f, 0x68, 0x13, 0xea, 0x11,
	0x71, 0x4c, 0x97, 0x30, 0xde, 0x55, 0xb4, 0xea, 0x6e, 0x73, 0xb8, 0x1a, 0x11, 0xe7, 0x19, 0x61,
	0x5c, 0x7f, 0xa5, 0xc0, 0xad, 0xaf, 0x7c, 0x42, 0x13, 0xe6, 0x73, 0x9f, 0x93, 0xd1, 0x58, 0x70,
	0xee, 0x02, 0x24, 0xd6, 0x4c, 0xe2, 0x74, 0x15, 0xad, 0xb2, 0xdb, 0x1c, 0xd4, 0x5e, 0xff, 0x7b,
	0x67, 0x65, 0xb8, 0x96, 0xe0, 0x87, 0x0e, 0xba, 0x0f, 0xeb, 0x3f, 0xf8, 0x84, 0x9a, 0x96, 0x27,
	0xa4, 0xba, 0x95, 0x54, 0x15, 0x88, 0x85, 0xbe, 0xc4, 0xd1, 0x07, 0xd0, 0x0a, 0xb1, 0x67, 0x11,
	0x6a, 0x46, 0x0c, 0x87, 0xa6, 0x4d, 0x79, 0xb7, 0xaa, 0x29, 0x57, 0xa5, 0xcd, 0x78, 0xf1, 0x3b,
	0x86, 0x43, 0x83, 0x72, 0xfd, 0x6f, 0x05, 0x6e, 0x3f, 0xc3, 0xd6, 0x09, 0xbe, 0x9e, 0xa7, 0x1d,
	0x68, 0xb8, 0x82, 0x5d, 0x64, 0x6a, 0x5d, 0xae, 0x5c, 0xc7, 0x15, 0x7a, 0x00, 0x6f, 0x11, 0x66,
	0xca, 0x31, 0x99, 0x72, 0x97, 0x6e, 0x4d, 0x53, 0x76, 0xeb, 0x49, 0x71, 0x83, 0x30, 0xd9, 0x74,
	0xe9, 0x5b, 0x3f, 0x57, 0xa0, 0x6d, 0xf8, 0x94, 0x87, 0x7e, 0x3c, 0x8a, 0x43, 0x3a, 0xf2, 0xd1,
	0x1d, 0xa8, 0x1f, 0x5b, 0x0c, 0x9b, 0x34, 0xf2, 0x32, 0xd6, 0x57, 0x05, 0xfa, 0x3c, 0xf2, 0xd0,
	0x87, 0xb0, 0x41, 0x28, 0xf7, 0xcd, 0xc9, 0x11, 0x39, 0xf1, 0x70, 0xc6, 0x7d, 0x4b, 0x2c, 0x27,
	0x4d, 0xf9, 0x96, 0x78, 0x18, 0x75, 0xa1, 0xe6, 0xe2, 0x91, 0xb0, 0x3d, 0x2d, 0x92, 0x88, 0xe8,
	0x54, 0x7c, 0x28, 0xd9, 0x82, 0x5a, 0xba, 0x53, 0x02, 0x37, 0x04, 0xac, 0xef, 0x03, 0x32, 0x42,
	0x6c, 0x71, 0x2c, 0x4d, 0x8a, 0x83, 0x8a, 0x26, 0x77, 0xa0, 0x3a, 0x6b, 0x51, 0x00, 0x02, 0x67,
	0xf8, 0xb4, 0x5b, 0x49, 0xb5, 0x48, 0x00, 0xfa, 0xe7, 0x70, 0xab, 0xef, 0x38, 0x47, 0x01, 0xb6,
	0xc9, 0x68, 0x3c, 0xb9, 0x80, 0x25, 0x87, 0x25, 0xc8, 0xfb, 0xd8, 0xbd, 0x26, 0xf9, 0x4b, 0xb8,
	0x7d, 0x80, 0xf9, 0x2c, 0x99, 0x05, 0x68, 0x1b, 0x5a, 0x53, 0x76, 0xfa, 0xda, 0x37, 0xaf, 0xc8,
	0xf2, 0xf2, 0x7f, 0x01, 0x9d, 0x03, 0xcc, 0xd3, 0x8f, 0x46, 0xc0, 0xa5, 0xf5, 0x3f, 0x86, 0x77,
	0x0a, 0xe9, 0x2c, 0x58, 0xf4, 0xe2, 0xf2, 0xa2, 0x47, 0xe4, 0x0c, 0x97, 0x16, 0x7d, 0x92, 0x13,
	0x8d, 0xe9, 0x2c, 0x10, 0xfc, 0xf8, 0x7e, 0x32, 0x72, 0x86, 0xb3, 0xfc, 0x70, 0x52, 0xa8, 0x1b,
	0xb0, 0x39, 0xb0, 0xf8, 0x1c, 0x07, 0x65, 0x1b, 0xb7, 0x0f, 0xea, 0xbc, 0x4d, 0xe2, 0xf6, 0x4f,
	0x7d, 0x64, 0x76, 0xb9, 0xb2, 0x21, 0x77, 0x39, 0x84, 0x86, 0x24, 0x1a, 0x34, 0x7e, 0x21, 0x1d,
	0xa8, 0xda, 0xc9, 0xc1, 0xd7, 0x26, 0x37, 0xcc, 0x26, 0x0e, 0x7a, 0x1f, 0x62, 0xff, 0xf2, 0x89,
	0x8a, 0x07, 0x71, 0x23, 0x59, 0xad, 0x87, 0x09, 0x5d, 0x57, 0xa1, 0x7b, 0x80, 0x79, 0xdf, 0x75,
	0xd3, 0x86, 0x0c, 0x99, 0x7e, 0xfa, 0x10, 0x36, 0xe7, 0xac, 0xb1, 0x00, 0x7d, 0x02, 0x75, 0x9b,
	0x72, 0x93, 0xd0, 0x91, 0x2f, 0x4d, 0xae, 0x3f, 0x52, 0x7b, 0xe9, 0xba, 0x5e, 0xda, 0xe1, 0x70,
	0xd5, 0x8e, 0xff, 0x79, 0xf0, 0x11, 0x34, 0x93, 0xaa, 0x23, 0x6e, 0xf1, 0x88, 0xa1, 0x36, 0x34,
	0x9e, 0x52, 0x8e, 0xc3, 0x04, 0x6d, 0x2b, 0x02, 0x49, 0xa7, 0x58, 0xbb, 0xf2, 0xe8, 0x2f, 0x80,
	0x4c, 0xb4, 0xa3, 0x00, 0x3a, 0xc5, 0xa1, 0x8d, 0x76, 0xb2, 0x26, 0xe6, 0x46, 0xbb, 0xfa, 0x5e,
	0xef, 0xea, 0xbb, 0xd0, 0x3b, 0xfa, 0x7a, 0x10, 0x7f, 0x17, 0x9e, 0x7a, 0x01, 0x1f, 0x9b, 0xdf,
	0x0c, 0xf4, 0xd6, 0x2f, 0xe7, 0x97, 0x55, 0xe5, 0xf7, 0xf3, 0xcb, 0xea, 0xca, 0x9f, 0xe2, 0x0f,
	0x1a, 0xc1, 0x46, 0x2e, 0xed, 0x91, 0x9e, 0x15, 0x2b, 0xfa, 0x1c, 0x94, 0xd1, 0xa9, 0xa4, 0x74,
	0x5e, 0x00, 0xca, 0x47, 0x38, 0xba, 0x9b, 0x15, 0x2a, 0x0c, 0xf9, 0x32, 0x4a, 0xd5, 0x94, 0x12,
	0x85, 0xd6, 0x4c, 0x88, 0x21, 0x2d, 0x2b, 0x93, 0xcf, 0xb8, 0x25, 0x1a, 0xef, 0x0a, 0x8d, 0x9a,
	0xd0, 0xa8, 0xd0, 0xcf, 0x84, 0x0a, 0x3c, 0xa4, 0xda, 0x63, 0xd9, 0xf2, 0x27, 0xe8, 0x27, 0xd8,
	0xc8, 0xc5, 0xdd, 0x6c, 0x07, 0x8b, 0xf2, 0x70, 0x89, 0xa6, 0x26, 0x34, 0x6f, 0x48, 0xcd, 0x53,
	0xa9, 0xd9, 0x7a, 0x78, 0xaa, 0x3d, 0x9e, 0x3e, 0x46, 0x29, 0x9c, 0x8b, 0xca, 0x59, 0xe1, 0xa2,
	0x2c, 0x2d, 0x23, 0x7c, 0x73, 0x91, 0xf0, 0xf7, 0xb0, 0x91, 0x8b, 0x59, 0xb4, 0x70, 0x53, 0x75,
	0x66, 0xd0, 0x85, 0x29, 0x1d, 0x8f, 0x72, 0x35, 0x35, 0xca, 0x5f, 0x15, 0x78, 0xbb, 0x20, 0x50,
	0xd1, 0xbd, 0xdc, 0x6e, 0x05, 0x91, 0xad, 0xde, 0x2f, 0x51, 0xc5, 0x82, 0xf8, 0xbc, 0xf5, 0x45,
	0xe7, 0x2d, 0xb0, 0x21, 0x22, 0x6b, 0x89, 0x8d, 0x24, 0x42, 0x97, 0xd8, 0x98, 0x64, 0x64, 0x6c,
	0x63, 0x6d, 0x91, 0x8d, 0x9f, 0xa1, 0x53, 0x9c, 0xb1, 0xb3, 0xe1, 0x30, 0x37, 0xce, 0xd5, 0xdd,
	0x72, 0x85, 0x93, 0x59, 0x40, 0x6a, 0x16, 0x63, 0xf9, 0x6d, 0xcd, 0x87, 0x26, 0xda, 0xce, 0x9d,
	0xaf, 0x30, 0x75, 0xd5, 0x9d, 0x52, 0x75, 0x13, 0xe9, 0xf5, 0xa9, 0xb4, 0x7a, 0xf3, 0xb7, 0xf3,
	0xcb, 0xea, 0x1f, 0x67, 0x83, 0xf6, 0xeb, 0x8b, 0x2d, 0xe5, 0x9f, 0x8b, 0x2d, 0xe5, 0xbf, 0x8b,
	0x2d, 0xe5, 0xd5, 0xff, 0x5b, 0x2b, 0x6f, 0x02, 0x00, 0x00, 0xff, 0xff, 0x79, 0xdc, 0x75, 0x36,
	0x41, 0x0b, 0x00, 0x00,
}
