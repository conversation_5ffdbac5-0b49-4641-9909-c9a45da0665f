// Code generated by protoc-gen-gogo.
// source: services/mall/mall.proto
// DO NOT EDIT!

/*
	Package Mall is a generated protocol buffer package.

	It is generated from these files:
		services/mall/mall.proto

	It has these top-level messages:
		Category
		Product
		ProductItem
		UserProductItem
		ProductIdList
		ProductList
		CategoryList
		GetProductReg
		ProductItem_AccountPassword
		ProductItem_Medal
		ProductItem_TTStaff
		ActivityProductConfig
		Activity
		ActivityProductDetail
		CurrentActivityDetail
		ActivityProductResult
		ActivityPurchaseRecord
		ActivitySnapshot
		CreateProductReq
		CreateProductResp
		UpdateProductReq
		GetProductsByCategoryReq
		AddProductItemReq_AccountPassword
		AddProductItemResp_AccountPassword
		RemainInfo
		GetProductsRemainReq
		GetProductsRemainResp
		GetProductRemainsByCategoryReq
		GetProductRemainsByCategoryResp
		GetProductItemReq
		GetProductItemResp
		DeleteProductItemReq
		CreateActivityReq
		CreateActivityResp
		UpdateActivityReq
		UpdateActivityResp
		DeleteActivityReq
		CloseActivityReq
		GetActivityListReq
		GetActivityListResp
		AddProductItemsReq
		AddProductItemsResp
		PurchaseActivityProductItemReq
		PurchaseActivityProductItemResp
		GetUserProductsReq
		GetUserProductsResp
		SetCurrentActivityReq
		ActivityCategoryReq
		FeedbackDiamondReq
		FeedbackDiamondResp
		GetUserProductItemByOrderReq
		GetProductTotalPurchaseTimesReq
		ProductTotalPurchaseTimes
		GetProductTotalPurchaseTimesResp
		GetActivityWinningReq
		ActivityWinningDetail
		GetActivityWinningDetailResp
		ActivityWinningCount
		GetActivityWinningCountResp
*/
package Mall

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type CurrencyBizMinType int32

const (
	CurrencyBizMinType_Biz_Purchase CurrencyBizMinType = 11
	CurrencyBizMinType_Biz_Feedback CurrencyBizMinType = 12
	CurrencyBizMinType_Biz_Share    CurrencyBizMinType = 13
)

var CurrencyBizMinType_name = map[int32]string{
	11: "Biz_Purchase",
	12: "Biz_Feedback",
	13: "Biz_Share",
}
var CurrencyBizMinType_value = map[string]int32{
	"Biz_Purchase": 11,
	"Biz_Feedback": 12,
	"Biz_Share":    13,
}

func (x CurrencyBizMinType) Enum() *CurrencyBizMinType {
	p := new(CurrencyBizMinType)
	*p = x
	return p
}
func (x CurrencyBizMinType) String() string {
	return proto.EnumName(CurrencyBizMinType_name, int32(x))
}
func (x *CurrencyBizMinType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CurrencyBizMinType_value, data, "CurrencyBizMinType")
	if err != nil {
		return err
	}
	*x = CurrencyBizMinType(value)
	return nil
}
func (CurrencyBizMinType) EnumDescriptor() ([]byte, []int) { return fileDescriptorMall, []int{0} }

type Product_ProductStatus int32

const (
	Product_Normal  Product_ProductStatus = 0
	Product_Deleted Product_ProductStatus = 1
)

var Product_ProductStatus_name = map[int32]string{
	0: "Normal",
	1: "Deleted",
}
var Product_ProductStatus_value = map[string]int32{
	"Normal":  0,
	"Deleted": 1,
}

func (x Product_ProductStatus) Enum() *Product_ProductStatus {
	p := new(Product_ProductStatus)
	*p = x
	return p
}
func (x Product_ProductStatus) String() string {
	return proto.EnumName(Product_ProductStatus_name, int32(x))
}
func (x *Product_ProductStatus) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Product_ProductStatus_value, data, "Product_ProductStatus")
	if err != nil {
		return err
	}
	*x = Product_ProductStatus(value)
	return nil
}
func (Product_ProductStatus) EnumDescriptor() ([]byte, []int) { return fileDescriptorMall, []int{1, 0} }

type Product_ProductItemType int32

const (
	Product_AccountAndPassword Product_ProductItemType = 1
	Product_Medal              Product_ProductItemType = 2
	Product_TTStaff            Product_ProductItemType = 3
)

var Product_ProductItemType_name = map[int32]string{
	1: "AccountAndPassword",
	2: "Medal",
	3: "TTStaff",
}
var Product_ProductItemType_value = map[string]int32{
	"AccountAndPassword": 1,
	"Medal":              2,
	"TTStaff":            3,
}

func (x Product_ProductItemType) Enum() *Product_ProductItemType {
	p := new(Product_ProductItemType)
	*p = x
	return p
}
func (x Product_ProductItemType) String() string {
	return proto.EnumName(Product_ProductItemType_name, int32(x))
}
func (x *Product_ProductItemType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Product_ProductItemType_value, data, "Product_ProductItemType")
	if err != nil {
		return err
	}
	*x = Product_ProductItemType(value)
	return nil
}
func (Product_ProductItemType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorMall, []int{1, 1}
}

type Activity_ActivityStatus int32

const (
	Activity_Normal  Activity_ActivityStatus = 0
	Activity_Locking Activity_ActivityStatus = 1
	Activity_Opening Activity_ActivityStatus = 2
	Activity_Closed  Activity_ActivityStatus = 3
	Activity_Deleted Activity_ActivityStatus = 99
)

var Activity_ActivityStatus_name = map[int32]string{
	0:  "Normal",
	1:  "Locking",
	2:  "Opening",
	3:  "Closed",
	99: "Deleted",
}
var Activity_ActivityStatus_value = map[string]int32{
	"Normal":  0,
	"Locking": 1,
	"Opening": 2,
	"Closed":  3,
	"Deleted": 99,
}

func (x Activity_ActivityStatus) Enum() *Activity_ActivityStatus {
	p := new(Activity_ActivityStatus)
	*p = x
	return p
}
func (x Activity_ActivityStatus) String() string {
	return proto.EnumName(Activity_ActivityStatus_name, int32(x))
}
func (x *Activity_ActivityStatus) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Activity_ActivityStatus_value, data, "Activity_ActivityStatus")
	if err != nil {
		return err
	}
	*x = Activity_ActivityStatus(value)
	return nil
}
func (Activity_ActivityStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorMall, []int{12, 0}
}

type GetActivityListReq_Options int32

const (
	GetActivityListReq_NotBegin GetActivityListReq_Options = 1
	GetActivityListReq_Opening  GetActivityListReq_Options = 2
	GetActivityListReq_Closed   GetActivityListReq_Options = 4
)

var GetActivityListReq_Options_name = map[int32]string{
	1: "NotBegin",
	2: "Opening",
	4: "Closed",
}
var GetActivityListReq_Options_value = map[string]int32{
	"NotBegin": 1,
	"Opening":  2,
	"Closed":   4,
}

func (x GetActivityListReq_Options) Enum() *GetActivityListReq_Options {
	p := new(GetActivityListReq_Options)
	*p = x
	return p
}
func (x GetActivityListReq_Options) String() string {
	return proto.EnumName(GetActivityListReq_Options_name, int32(x))
}
func (x *GetActivityListReq_Options) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GetActivityListReq_Options_value, data, "GetActivityListReq_Options")
	if err != nil {
		return err
	}
	*x = GetActivityListReq_Options(value)
	return nil
}
func (GetActivityListReq_Options) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorMall, []int{38, 0}
}

// 商品类别
type Category struct {
	CategoryId   uint32 `protobuf:"varint,1,req,name=category_id,json=categoryId" json:"category_id"`
	CategoryName string `protobuf:"bytes,2,req,name=category_name,json=categoryName" json:"category_name"`
}

func (m *Category) Reset()                    { *m = Category{} }
func (m *Category) String() string            { return proto.CompactTextString(m) }
func (*Category) ProtoMessage()               {}
func (*Category) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{0} }

func (m *Category) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *Category) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

// 商品
type Product struct {
	ProductId     uint32 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Name          string `protobuf:"bytes,2,req,name=name" json:"name"`
	CategoryId    uint32 `protobuf:"varint,3,req,name=category_id,json=categoryId" json:"category_id"`
	Description   string `protobuf:"bytes,4,req,name=description" json:"description"`
	Status        uint32 `protobuf:"varint,5,req,name=status" json:"status"`
	Price         uint32 `protobuf:"varint,6,req,name=price" json:"price"`
	LevelRequired uint32 `protobuf:"varint,7,req,name=level_required,json=levelRequired" json:"level_required"`
	GameId        uint32 `protobuf:"varint,8,req,name=game_id,json=gameId" json:"game_id"`
	IconUrl       string `protobuf:"bytes,9,req,name=icon_url,json=iconUrl" json:"icon_url"`
	ItemType      uint32 `protobuf:"varint,10,req,name=item_type,json=itemType" json:"item_type"`
}

func (m *Product) Reset()                    { *m = Product{} }
func (m *Product) String() string            { return proto.CompactTextString(m) }
func (*Product) ProtoMessage()               {}
func (*Product) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{1} }

func (m *Product) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *Product) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Product) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *Product) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *Product) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *Product) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *Product) GetLevelRequired() uint32 {
	if m != nil {
		return m.LevelRequired
	}
	return 0
}

func (m *Product) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *Product) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *Product) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

// 物品
type ProductItem struct {
	ItemHash   string `protobuf:"bytes,1,req,name=item_hash,json=itemHash" json:"item_hash"`
	ItemBinary []byte `protobuf:"bytes,2,req,name=item_binary,json=itemBinary" json:"item_binary"`
	ProductId  uint32 `protobuf:"varint,3,req,name=product_id,json=productId" json:"product_id"`
}

func (m *ProductItem) Reset()                    { *m = ProductItem{} }
func (m *ProductItem) String() string            { return proto.CompactTextString(m) }
func (*ProductItem) ProtoMessage()               {}
func (*ProductItem) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{2} }

func (m *ProductItem) GetItemHash() string {
	if m != nil {
		return m.ItemHash
	}
	return ""
}

func (m *ProductItem) GetItemBinary() []byte {
	if m != nil {
		return m.ItemBinary
	}
	return nil
}

func (m *ProductItem) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

// 用户的物品
type UserProductItem struct {
	Uid             uint32       `protobuf:"varint,1,req,name=uid" json:"uid"`
	Timestamp       uint64       `protobuf:"varint,2,req,name=timestamp" json:"timestamp"`
	ProductSnapshot *Product     `protobuf:"bytes,3,req,name=product_snapshot,json=productSnapshot" json:"product_snapshot,omitempty"`
	ItemSnapshot    *ProductItem `protobuf:"bytes,4,req,name=item_snapshot,json=itemSnapshot" json:"item_snapshot,omitempty"`
	OrderId         string       `protobuf:"bytes,5,req,name=order_id,json=orderId" json:"order_id"`
}

func (m *UserProductItem) Reset()                    { *m = UserProductItem{} }
func (m *UserProductItem) String() string            { return proto.CompactTextString(m) }
func (*UserProductItem) ProtoMessage()               {}
func (*UserProductItem) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{3} }

func (m *UserProductItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserProductItem) GetTimestamp() uint64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *UserProductItem) GetProductSnapshot() *Product {
	if m != nil {
		return m.ProductSnapshot
	}
	return nil
}

func (m *UserProductItem) GetItemSnapshot() *ProductItem {
	if m != nil {
		return m.ItemSnapshot
	}
	return nil
}

func (m *UserProductItem) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type ProductIdList struct {
	ProductIdList []uint32 `protobuf:"varint,1,rep,name=product_id_list,json=productIdList" json:"product_id_list,omitempty"`
}

func (m *ProductIdList) Reset()                    { *m = ProductIdList{} }
func (m *ProductIdList) String() string            { return proto.CompactTextString(m) }
func (*ProductIdList) ProtoMessage()               {}
func (*ProductIdList) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{4} }

func (m *ProductIdList) GetProductIdList() []uint32 {
	if m != nil {
		return m.ProductIdList
	}
	return nil
}

type ProductList struct {
	ProductList []*Product `protobuf:"bytes,1,rep,name=product_list,json=productList" json:"product_list,omitempty"`
}

func (m *ProductList) Reset()                    { *m = ProductList{} }
func (m *ProductList) String() string            { return proto.CompactTextString(m) }
func (*ProductList) ProtoMessage()               {}
func (*ProductList) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{5} }

func (m *ProductList) GetProductList() []*Product {
	if m != nil {
		return m.ProductList
	}
	return nil
}

type CategoryList struct {
	CategoryList []*Category `protobuf:"bytes,1,rep,name=category_list,json=categoryList" json:"category_list,omitempty"`
}

func (m *CategoryList) Reset()                    { *m = CategoryList{} }
func (m *CategoryList) String() string            { return proto.CompactTextString(m) }
func (*CategoryList) ProtoMessage()               {}
func (*CategoryList) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{6} }

func (m *CategoryList) GetCategoryList() []*Category {
	if m != nil {
		return m.CategoryList
	}
	return nil
}

type GetProductReg struct {
	Uid   uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Index uint32 `protobuf:"varint,2,req,name=index" json:"index"`
	Count uint32 `protobuf:"varint,3,req,name=count" json:"count"`
}

func (m *GetProductReg) Reset()                    { *m = GetProductReg{} }
func (m *GetProductReg) String() string            { return proto.CompactTextString(m) }
func (*GetProductReg) ProtoMessage()               {}
func (*GetProductReg) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{7} }

func (m *GetProductReg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetProductReg) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *GetProductReg) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type ProductItem_AccountPassword struct {
	Account  string `protobuf:"bytes,1,req,name=account" json:"account"`
	Password string `protobuf:"bytes,2,req,name=password" json:"password"`
}

func (m *ProductItem_AccountPassword) Reset()                    { *m = ProductItem_AccountPassword{} }
func (m *ProductItem_AccountPassword) String() string            { return proto.CompactTextString(m) }
func (*ProductItem_AccountPassword) ProtoMessage()               {}
func (*ProductItem_AccountPassword) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{8} }

func (m *ProductItem_AccountPassword) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ProductItem_AccountPassword) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

type ProductItem_Medal struct {
	MedalId   uint32 `protobuf:"varint,1,req,name=medal_id,json=medalId" json:"medal_id"`
	MedalName string `protobuf:"bytes,2,req,name=medal_name,json=medalName" json:"medal_name"`
}

func (m *ProductItem_Medal) Reset()                    { *m = ProductItem_Medal{} }
func (m *ProductItem_Medal) String() string            { return proto.CompactTextString(m) }
func (*ProductItem_Medal) ProtoMessage()               {}
func (*ProductItem_Medal) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{9} }

func (m *ProductItem_Medal) GetMedalId() uint32 {
	if m != nil {
		return m.MedalId
	}
	return 0
}

func (m *ProductItem_Medal) GetMedalName() string {
	if m != nil {
		return m.MedalName
	}
	return ""
}

type ProductItem_TTStaff struct {
	StaffId   uint32 `protobuf:"varint,1,req,name=staff_id,json=staffId" json:"staff_id"`
	StaffName string `protobuf:"bytes,2,req,name=staff_name,json=staffName" json:"staff_name"`
}

func (m *ProductItem_TTStaff) Reset()                    { *m = ProductItem_TTStaff{} }
func (m *ProductItem_TTStaff) String() string            { return proto.CompactTextString(m) }
func (*ProductItem_TTStaff) ProtoMessage()               {}
func (*ProductItem_TTStaff) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{10} }

func (m *ProductItem_TTStaff) GetStaffId() uint32 {
	if m != nil {
		return m.StaffId
	}
	return 0
}

func (m *ProductItem_TTStaff) GetStaffName() string {
	if m != nil {
		return m.StaffName
	}
	return ""
}

type ActivityProductConfig struct {
	ProductId uint32 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Count     uint32 `protobuf:"varint,2,req,name=count" json:"count"`
	Coeff     uint32 `protobuf:"varint,3,opt,name=coeff" json:"coeff"`
}

func (m *ActivityProductConfig) Reset()                    { *m = ActivityProductConfig{} }
func (m *ActivityProductConfig) String() string            { return proto.CompactTextString(m) }
func (*ActivityProductConfig) ProtoMessage()               {}
func (*ActivityProductConfig) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{11} }

func (m *ActivityProductConfig) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *ActivityProductConfig) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ActivityProductConfig) GetCoeff() uint32 {
	if m != nil {
		return m.Coeff
	}
	return 0
}

// 活动
type Activity struct {
	ActivityId                uint32                   `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
	Name                      string                   `protobuf:"bytes,2,req,name=name" json:"name"`
	BeginTime                 uint64                   `protobuf:"varint,3,req,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime                   uint64                   `protobuf:"varint,4,req,name=end_time,json=endTime" json:"end_time"`
	Status                    uint32                   `protobuf:"varint,5,req,name=status" json:"status"`
	ActivityProductConfigList []*ActivityProductConfig `protobuf:"bytes,6,rep,name=activity_product_config_list,json=activityProductConfigList" json:"activity_product_config_list,omitempty"`
}

func (m *Activity) Reset()                    { *m = Activity{} }
func (m *Activity) String() string            { return proto.CompactTextString(m) }
func (*Activity) ProtoMessage()               {}
func (*Activity) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{12} }

func (m *Activity) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *Activity) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Activity) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *Activity) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *Activity) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *Activity) GetActivityProductConfigList() []*ActivityProductConfig {
	if m != nil {
		return m.ActivityProductConfigList
	}
	return nil
}

type ActivityProductDetail struct {
	Product        *Product `protobuf:"bytes,1,req,name=product" json:"product,omitempty"`
	Total          uint32   `protobuf:"varint,2,req,name=total" json:"total"`
	Remain         uint32   `protobuf:"varint,3,req,name=remain" json:"remain"`
	SoldOut        uint32   `protobuf:"varint,4,opt,name=sold_out,json=soldOut" json:"sold_out"`
	TotalPurchased uint32   `protobuf:"varint,5,opt,name=total_purchased,json=totalPurchased" json:"total_purchased"`
}

func (m *ActivityProductDetail) Reset()                    { *m = ActivityProductDetail{} }
func (m *ActivityProductDetail) String() string            { return proto.CompactTextString(m) }
func (*ActivityProductDetail) ProtoMessage()               {}
func (*ActivityProductDetail) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{13} }

func (m *ActivityProductDetail) GetProduct() *Product {
	if m != nil {
		return m.Product
	}
	return nil
}

func (m *ActivityProductDetail) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ActivityProductDetail) GetRemain() uint32 {
	if m != nil {
		return m.Remain
	}
	return 0
}

func (m *ActivityProductDetail) GetSoldOut() uint32 {
	if m != nil {
		return m.SoldOut
	}
	return 0
}

func (m *ActivityProductDetail) GetTotalPurchased() uint32 {
	if m != nil {
		return m.TotalPurchased
	}
	return 0
}

// 当前活动详情
type CurrentActivityDetail struct {
	Activity    *Activity                `protobuf:"bytes,1,req,name=activity" json:"activity,omitempty"`
	ProductList []*ActivityProductDetail `protobuf:"bytes,2,rep,name=product_list,json=productList" json:"product_list,omitempty"`
}

func (m *CurrentActivityDetail) Reset()                    { *m = CurrentActivityDetail{} }
func (m *CurrentActivityDetail) String() string            { return proto.CompactTextString(m) }
func (*CurrentActivityDetail) ProtoMessage()               {}
func (*CurrentActivityDetail) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{14} }

func (m *CurrentActivityDetail) GetActivity() *Activity {
	if m != nil {
		return m.Activity
	}
	return nil
}

func (m *CurrentActivityDetail) GetProductList() []*ActivityProductDetail {
	if m != nil {
		return m.ProductList
	}
	return nil
}

type ActivityProductResult struct {
	ProductId uint32 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Total     uint32 `protobuf:"varint,2,req,name=total" json:"total"`
	Purchased uint32 `protobuf:"varint,3,req,name=purchased" json:"purchased"`
	SoldOut   uint32 `protobuf:"varint,4,opt,name=sold_out,json=soldOut" json:"sold_out"`
}

func (m *ActivityProductResult) Reset()                    { *m = ActivityProductResult{} }
func (m *ActivityProductResult) String() string            { return proto.CompactTextString(m) }
func (*ActivityProductResult) ProtoMessage()               {}
func (*ActivityProductResult) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{15} }

func (m *ActivityProductResult) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *ActivityProductResult) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ActivityProductResult) GetPurchased() uint32 {
	if m != nil {
		return m.Purchased
	}
	return 0
}

func (m *ActivityProductResult) GetSoldOut() uint32 {
	if m != nil {
		return m.SoldOut
	}
	return 0
}

type ActivityPurchaseRecord struct {
	ProductId   uint32 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	PurchaseUid uint32 `protobuf:"varint,2,req,name=purchase_uid,json=purchaseUid" json:"purchase_uid"`
	Timestamp   uint64 `protobuf:"varint,3,req,name=timestamp" json:"timestamp"`
}

func (m *ActivityPurchaseRecord) Reset()                    { *m = ActivityPurchaseRecord{} }
func (m *ActivityPurchaseRecord) String() string            { return proto.CompactTextString(m) }
func (*ActivityPurchaseRecord) ProtoMessage()               {}
func (*ActivityPurchaseRecord) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{16} }

func (m *ActivityPurchaseRecord) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *ActivityPurchaseRecord) GetPurchaseUid() uint32 {
	if m != nil {
		return m.PurchaseUid
	}
	return 0
}

func (m *ActivityPurchaseRecord) GetTimestamp() uint64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

// 活动快照
type ActivitySnapshot struct {
	Activity                  *Activity                 `protobuf:"bytes,1,req,name=activity" json:"activity,omitempty"`
	ProductSnapshotList       []*Product                `protobuf:"bytes,2,rep,name=product_snapshot_list,json=productSnapshotList" json:"product_snapshot_list,omitempty"`
	ActivityProductResultList []*ActivityProductResult  `protobuf:"bytes,3,rep,name=activity_product_result_list,json=activityProductResultList" json:"activity_product_result_list,omitempty"`
	PurchaseTimeLine          []*ActivityPurchaseRecord `protobuf:"bytes,4,rep,name=purchase_time_line,json=purchaseTimeLine" json:"purchase_time_line,omitempty"`
}

func (m *ActivitySnapshot) Reset()                    { *m = ActivitySnapshot{} }
func (m *ActivitySnapshot) String() string            { return proto.CompactTextString(m) }
func (*ActivitySnapshot) ProtoMessage()               {}
func (*ActivitySnapshot) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{17} }

func (m *ActivitySnapshot) GetActivity() *Activity {
	if m != nil {
		return m.Activity
	}
	return nil
}

func (m *ActivitySnapshot) GetProductSnapshotList() []*Product {
	if m != nil {
		return m.ProductSnapshotList
	}
	return nil
}

func (m *ActivitySnapshot) GetActivityProductResultList() []*ActivityProductResult {
	if m != nil {
		return m.ActivityProductResultList
	}
	return nil
}

func (m *ActivitySnapshot) GetPurchaseTimeLine() []*ActivityPurchaseRecord {
	if m != nil {
		return m.PurchaseTimeLine
	}
	return nil
}

type CreateProductReq struct {
	Name          string `protobuf:"bytes,1,req,name=name" json:"name"`
	CategoryId    uint32 `protobuf:"varint,2,req,name=category_id,json=categoryId" json:"category_id"`
	Description   string `protobuf:"bytes,3,req,name=description" json:"description"`
	Price         uint32 `protobuf:"varint,4,req,name=price" json:"price"`
	LevelRequired uint32 `protobuf:"varint,5,req,name=level_required,json=levelRequired" json:"level_required"`
	GameId        uint32 `protobuf:"varint,6,req,name=game_id,json=gameId" json:"game_id"`
	IconUrl       string `protobuf:"bytes,7,req,name=icon_url,json=iconUrl" json:"icon_url"`
}

func (m *CreateProductReq) Reset()                    { *m = CreateProductReq{} }
func (m *CreateProductReq) String() string            { return proto.CompactTextString(m) }
func (*CreateProductReq) ProtoMessage()               {}
func (*CreateProductReq) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{18} }

func (m *CreateProductReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreateProductReq) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *CreateProductReq) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *CreateProductReq) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *CreateProductReq) GetLevelRequired() uint32 {
	if m != nil {
		return m.LevelRequired
	}
	return 0
}

func (m *CreateProductReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CreateProductReq) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

type CreateProductResp struct {
	ProductId uint32 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
}

func (m *CreateProductResp) Reset()                    { *m = CreateProductResp{} }
func (m *CreateProductResp) String() string            { return proto.CompactTextString(m) }
func (*CreateProductResp) ProtoMessage()               {}
func (*CreateProductResp) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{19} }

func (m *CreateProductResp) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

type UpdateProductReq struct {
	ProductId     uint32 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Name          string `protobuf:"bytes,2,opt,name=name" json:"name"`
	Description   string `protobuf:"bytes,3,opt,name=description" json:"description"`
	Price         uint32 `protobuf:"varint,4,opt,name=price" json:"price"`
	LevelRequired uint32 `protobuf:"varint,5,opt,name=level_required,json=levelRequired" json:"level_required"`
	GameId        uint32 `protobuf:"varint,6,opt,name=game_id,json=gameId" json:"game_id"`
	IconUrl       string `protobuf:"bytes,7,opt,name=icon_url,json=iconUrl" json:"icon_url"`
}

func (m *UpdateProductReq) Reset()                    { *m = UpdateProductReq{} }
func (m *UpdateProductReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateProductReq) ProtoMessage()               {}
func (*UpdateProductReq) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{20} }

func (m *UpdateProductReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *UpdateProductReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UpdateProductReq) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *UpdateProductReq) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *UpdateProductReq) GetLevelRequired() uint32 {
	if m != nil {
		return m.LevelRequired
	}
	return 0
}

func (m *UpdateProductReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UpdateProductReq) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

type GetProductsByCategoryReq struct {
	CategoryId uint32 `protobuf:"varint,1,req,name=category_id,json=categoryId" json:"category_id"`
	FromIndex  uint32 `protobuf:"varint,2,req,name=from_index,json=fromIndex" json:"from_index"`
	Count      uint32 `protobuf:"varint,3,req,name=count" json:"count"`
}

func (m *GetProductsByCategoryReq) Reset()                    { *m = GetProductsByCategoryReq{} }
func (m *GetProductsByCategoryReq) String() string            { return proto.CompactTextString(m) }
func (*GetProductsByCategoryReq) ProtoMessage()               {}
func (*GetProductsByCategoryReq) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{21} }

func (m *GetProductsByCategoryReq) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *GetProductsByCategoryReq) GetFromIndex() uint32 {
	if m != nil {
		return m.FromIndex
	}
	return 0
}

func (m *GetProductsByCategoryReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type AddProductItemReq_AccountPassword struct {
	ProductId           uint32                         `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	AccountPasswordList []*ProductItem_AccountPassword `protobuf:"bytes,2,rep,name=account_password_list,json=accountPasswordList" json:"account_password_list,omitempty"`
}

func (m *AddProductItemReq_AccountPassword) Reset()         { *m = AddProductItemReq_AccountPassword{} }
func (m *AddProductItemReq_AccountPassword) String() string { return proto.CompactTextString(m) }
func (*AddProductItemReq_AccountPassword) ProtoMessage()    {}
func (*AddProductItemReq_AccountPassword) Descriptor() ([]byte, []int) {
	return fileDescriptorMall, []int{22}
}

func (m *AddProductItemReq_AccountPassword) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *AddProductItemReq_AccountPassword) GetAccountPasswordList() []*ProductItem_AccountPassword {
	if m != nil {
		return m.AccountPasswordList
	}
	return nil
}

type AddProductItemResp_AccountPassword struct {
	SuccessAccountPasswordList []*ProductItem_AccountPassword `protobuf:"bytes,1,rep,name=success_account_password_list,json=successAccountPasswordList" json:"success_account_password_list,omitempty"`
	FailedAccountPasswordList  []*ProductItem_AccountPassword `protobuf:"bytes,2,rep,name=failed_account_password_list,json=failedAccountPasswordList" json:"failed_account_password_list,omitempty"`
}

func (m *AddProductItemResp_AccountPassword) Reset()         { *m = AddProductItemResp_AccountPassword{} }
func (m *AddProductItemResp_AccountPassword) String() string { return proto.CompactTextString(m) }
func (*AddProductItemResp_AccountPassword) ProtoMessage()    {}
func (*AddProductItemResp_AccountPassword) Descriptor() ([]byte, []int) {
	return fileDescriptorMall, []int{23}
}

func (m *AddProductItemResp_AccountPassword) GetSuccessAccountPasswordList() []*ProductItem_AccountPassword {
	if m != nil {
		return m.SuccessAccountPasswordList
	}
	return nil
}

func (m *AddProductItemResp_AccountPassword) GetFailedAccountPasswordList() []*ProductItem_AccountPassword {
	if m != nil {
		return m.FailedAccountPasswordList
	}
	return nil
}

type RemainInfo struct {
	ProductId uint32 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	ItemNum   uint32 `protobuf:"varint,2,req,name=item_num,json=itemNum" json:"item_num"`
}

func (m *RemainInfo) Reset()                    { *m = RemainInfo{} }
func (m *RemainInfo) String() string            { return proto.CompactTextString(m) }
func (*RemainInfo) ProtoMessage()               {}
func (*RemainInfo) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{24} }

func (m *RemainInfo) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *RemainInfo) GetItemNum() uint32 {
	if m != nil {
		return m.ItemNum
	}
	return 0
}

type GetProductsRemainReq struct {
	ProductIdList    []uint32 `protobuf:"varint,1,rep,name=product_id_list,json=productIdList" json:"product_id_list,omitempty"`
	QueryForActivity uint32   `protobuf:"varint,2,opt,name=query_for_activity,json=queryForActivity" json:"query_for_activity"`
}

func (m *GetProductsRemainReq) Reset()                    { *m = GetProductsRemainReq{} }
func (m *GetProductsRemainReq) String() string            { return proto.CompactTextString(m) }
func (*GetProductsRemainReq) ProtoMessage()               {}
func (*GetProductsRemainReq) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{25} }

func (m *GetProductsRemainReq) GetProductIdList() []uint32 {
	if m != nil {
		return m.ProductIdList
	}
	return nil
}

func (m *GetProductsRemainReq) GetQueryForActivity() uint32 {
	if m != nil {
		return m.QueryForActivity
	}
	return 0
}

type GetProductsRemainResp struct {
	RemainInfoList []*RemainInfo `protobuf:"bytes,1,rep,name=remain_info_list,json=remainInfoList" json:"remain_info_list,omitempty"`
}

func (m *GetProductsRemainResp) Reset()                    { *m = GetProductsRemainResp{} }
func (m *GetProductsRemainResp) String() string            { return proto.CompactTextString(m) }
func (*GetProductsRemainResp) ProtoMessage()               {}
func (*GetProductsRemainResp) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{26} }

func (m *GetProductsRemainResp) GetRemainInfoList() []*RemainInfo {
	if m != nil {
		return m.RemainInfoList
	}
	return nil
}

type GetProductRemainsByCategoryReq struct {
	CategoryId uint32 `protobuf:"varint,1,req,name=category_id,json=categoryId" json:"category_id"`
}

func (m *GetProductRemainsByCategoryReq) Reset()         { *m = GetProductRemainsByCategoryReq{} }
func (m *GetProductRemainsByCategoryReq) String() string { return proto.CompactTextString(m) }
func (*GetProductRemainsByCategoryReq) ProtoMessage()    {}
func (*GetProductRemainsByCategoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMall, []int{27}
}

func (m *GetProductRemainsByCategoryReq) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

type GetProductRemainsByCategoryResp struct {
	Remain uint32 `protobuf:"varint,1,req,name=remain" json:"remain"`
}

func (m *GetProductRemainsByCategoryResp) Reset()         { *m = GetProductRemainsByCategoryResp{} }
func (m *GetProductRemainsByCategoryResp) String() string { return proto.CompactTextString(m) }
func (*GetProductRemainsByCategoryResp) ProtoMessage()    {}
func (*GetProductRemainsByCategoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMall, []int{28}
}

func (m *GetProductRemainsByCategoryResp) GetRemain() uint32 {
	if m != nil {
		return m.Remain
	}
	return 0
}

type GetProductItemReq struct {
	ProductId uint32 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
}

func (m *GetProductItemReq) Reset()                    { *m = GetProductItemReq{} }
func (m *GetProductItemReq) String() string            { return proto.CompactTextString(m) }
func (*GetProductItemReq) ProtoMessage()               {}
func (*GetProductItemReq) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{29} }

func (m *GetProductItemReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

type GetProductItemResp struct {
	ProductItemList []*ProductItem `protobuf:"bytes,1,rep,name=product_item_list,json=productItemList" json:"product_item_list,omitempty"`
}

func (m *GetProductItemResp) Reset()                    { *m = GetProductItemResp{} }
func (m *GetProductItemResp) String() string            { return proto.CompactTextString(m) }
func (*GetProductItemResp) ProtoMessage()               {}
func (*GetProductItemResp) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{30} }

func (m *GetProductItemResp) GetProductItemList() []*ProductItem {
	if m != nil {
		return m.ProductItemList
	}
	return nil
}

type DeleteProductItemReq struct {
	ProductId uint32   `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	HashList  []string `protobuf:"bytes,2,rep,name=hash_list,json=hashList" json:"hash_list,omitempty"`
}

func (m *DeleteProductItemReq) Reset()                    { *m = DeleteProductItemReq{} }
func (m *DeleteProductItemReq) String() string            { return proto.CompactTextString(m) }
func (*DeleteProductItemReq) ProtoMessage()               {}
func (*DeleteProductItemReq) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{31} }

func (m *DeleteProductItemReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *DeleteProductItemReq) GetHashList() []string {
	if m != nil {
		return m.HashList
	}
	return nil
}

type CreateActivityReq struct {
	Name                      string                   `protobuf:"bytes,1,req,name=name" json:"name"`
	BeginTime                 uint64                   `protobuf:"varint,2,req,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime                   uint64                   `protobuf:"varint,3,req,name=end_time,json=endTime" json:"end_time"`
	ActivityProductConfigList []*ActivityProductConfig `protobuf:"bytes,4,rep,name=activity_product_config_list,json=activityProductConfigList" json:"activity_product_config_list,omitempty"`
}

func (m *CreateActivityReq) Reset()                    { *m = CreateActivityReq{} }
func (m *CreateActivityReq) String() string            { return proto.CompactTextString(m) }
func (*CreateActivityReq) ProtoMessage()               {}
func (*CreateActivityReq) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{32} }

func (m *CreateActivityReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreateActivityReq) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *CreateActivityReq) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *CreateActivityReq) GetActivityProductConfigList() []*ActivityProductConfig {
	if m != nil {
		return m.ActivityProductConfigList
	}
	return nil
}

type CreateActivityResp struct {
	ActivityId   uint32 `protobuf:"varint,1,opt,name=activity_id,json=activityId" json:"activity_id"`
	ErrorMessage string `protobuf:"bytes,2,opt,name=error_message,json=errorMessage" json:"error_message"`
}

func (m *CreateActivityResp) Reset()                    { *m = CreateActivityResp{} }
func (m *CreateActivityResp) String() string            { return proto.CompactTextString(m) }
func (*CreateActivityResp) ProtoMessage()               {}
func (*CreateActivityResp) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{33} }

func (m *CreateActivityResp) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *CreateActivityResp) GetErrorMessage() string {
	if m != nil {
		return m.ErrorMessage
	}
	return ""
}

type UpdateActivityReq struct {
	ActivityId                uint32                   `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
	Name                      string                   `protobuf:"bytes,2,req,name=name" json:"name"`
	BeginTime                 uint64                   `protobuf:"varint,3,req,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime                   uint64                   `protobuf:"varint,4,req,name=end_time,json=endTime" json:"end_time"`
	ActivityProductConfigList []*ActivityProductConfig `protobuf:"bytes,5,rep,name=activity_product_config_list,json=activityProductConfigList" json:"activity_product_config_list,omitempty"`
}

func (m *UpdateActivityReq) Reset()                    { *m = UpdateActivityReq{} }
func (m *UpdateActivityReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateActivityReq) ProtoMessage()               {}
func (*UpdateActivityReq) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{34} }

func (m *UpdateActivityReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *UpdateActivityReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UpdateActivityReq) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *UpdateActivityReq) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *UpdateActivityReq) GetActivityProductConfigList() []*ActivityProductConfig {
	if m != nil {
		return m.ActivityProductConfigList
	}
	return nil
}

type UpdateActivityResp struct {
	ErrorMessage string `protobuf:"bytes,1,opt,name=error_message,json=errorMessage" json:"error_message"`
}

func (m *UpdateActivityResp) Reset()                    { *m = UpdateActivityResp{} }
func (m *UpdateActivityResp) String() string            { return proto.CompactTextString(m) }
func (*UpdateActivityResp) ProtoMessage()               {}
func (*UpdateActivityResp) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{35} }

func (m *UpdateActivityResp) GetErrorMessage() string {
	if m != nil {
		return m.ErrorMessage
	}
	return ""
}

type DeleteActivityReq struct {
	ActivityId uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
}

func (m *DeleteActivityReq) Reset()                    { *m = DeleteActivityReq{} }
func (m *DeleteActivityReq) String() string            { return proto.CompactTextString(m) }
func (*DeleteActivityReq) ProtoMessage()               {}
func (*DeleteActivityReq) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{36} }

func (m *DeleteActivityReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

type CloseActivityReq struct {
	ActivityId uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
}

func (m *CloseActivityReq) Reset()                    { *m = CloseActivityReq{} }
func (m *CloseActivityReq) String() string            { return proto.CompactTextString(m) }
func (*CloseActivityReq) ProtoMessage()               {}
func (*CloseActivityReq) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{37} }

func (m *CloseActivityReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

type GetActivityListReq struct {
	Options   uint32 `protobuf:"varint,1,req,name=options" json:"options"`
	FromIndex uint32 `protobuf:"varint,2,req,name=from_index,json=fromIndex" json:"from_index"`
	Count     uint32 `protobuf:"varint,3,req,name=count" json:"count"`
	Reverse   bool   `protobuf:"varint,4,req,name=reverse" json:"reverse"`
}

func (m *GetActivityListReq) Reset()                    { *m = GetActivityListReq{} }
func (m *GetActivityListReq) String() string            { return proto.CompactTextString(m) }
func (*GetActivityListReq) ProtoMessage()               {}
func (*GetActivityListReq) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{38} }

func (m *GetActivityListReq) GetOptions() uint32 {
	if m != nil {
		return m.Options
	}
	return 0
}

func (m *GetActivityListReq) GetFromIndex() uint32 {
	if m != nil {
		return m.FromIndex
	}
	return 0
}

func (m *GetActivityListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetActivityListReq) GetReverse() bool {
	if m != nil {
		return m.Reverse
	}
	return false
}

type GetActivityListResp struct {
	ActivityList []*Activity `protobuf:"bytes,1,rep,name=activity_list,json=activityList" json:"activity_list,omitempty"`
}

func (m *GetActivityListResp) Reset()                    { *m = GetActivityListResp{} }
func (m *GetActivityListResp) String() string            { return proto.CompactTextString(m) }
func (*GetActivityListResp) ProtoMessage()               {}
func (*GetActivityListResp) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{39} }

func (m *GetActivityListResp) GetActivityList() []*Activity {
	if m != nil {
		return m.ActivityList
	}
	return nil
}

type AddProductItemsReq struct {
	ProductItemList []*ProductItem `protobuf:"bytes,1,rep,name=product_item_list,json=productItemList" json:"product_item_list,omitempty"`
}

func (m *AddProductItemsReq) Reset()                    { *m = AddProductItemsReq{} }
func (m *AddProductItemsReq) String() string            { return proto.CompactTextString(m) }
func (*AddProductItemsReq) ProtoMessage()               {}
func (*AddProductItemsReq) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{40} }

func (m *AddProductItemsReq) GetProductItemList() []*ProductItem {
	if m != nil {
		return m.ProductItemList
	}
	return nil
}

type AddProductItemsResp struct {
	SuccessProductItemList []*ProductItem `protobuf:"bytes,1,rep,name=success_product_item_list,json=successProductItemList" json:"success_product_item_list,omitempty"`
	FailedProductItemList  []*ProductItem `protobuf:"bytes,2,rep,name=failed_product_item_list,json=failedProductItemList" json:"failed_product_item_list,omitempty"`
}

func (m *AddProductItemsResp) Reset()                    { *m = AddProductItemsResp{} }
func (m *AddProductItemsResp) String() string            { return proto.CompactTextString(m) }
func (*AddProductItemsResp) ProtoMessage()               {}
func (*AddProductItemsResp) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{41} }

func (m *AddProductItemsResp) GetSuccessProductItemList() []*ProductItem {
	if m != nil {
		return m.SuccessProductItemList
	}
	return nil
}

func (m *AddProductItemsResp) GetFailedProductItemList() []*ProductItem {
	if m != nil {
		return m.FailedProductItemList
	}
	return nil
}

type PurchaseActivityProductItemReq struct {
	Uid              uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ActivityId       uint32 `protobuf:"varint,2,req,name=activity_id,json=activityId" json:"activity_id"`
	ProductId        uint32 `protobuf:"varint,3,req,name=product_id,json=productId" json:"product_id"`
	ActivityCategory uint32 `protobuf:"varint,4,opt,name=activity_category,json=activityCategory" json:"activity_category"`
}

func (m *PurchaseActivityProductItemReq) Reset()         { *m = PurchaseActivityProductItemReq{} }
func (m *PurchaseActivityProductItemReq) String() string { return proto.CompactTextString(m) }
func (*PurchaseActivityProductItemReq) ProtoMessage()    {}
func (*PurchaseActivityProductItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMall, []int{42}
}

func (m *PurchaseActivityProductItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PurchaseActivityProductItemReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *PurchaseActivityProductItemReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *PurchaseActivityProductItemReq) GetActivityCategory() uint32 {
	if m != nil {
		return m.ActivityCategory
	}
	return 0
}

type PurchaseActivityProductItemResp struct {
	PurchasedItem           *UserProductItem       `protobuf:"bytes,1,opt,name=purchased_item,json=purchasedItem" json:"purchased_item,omitempty"`
	PurchasingProductDetail *ActivityProductDetail `protobuf:"bytes,2,opt,name=purchasing_product_detail,json=purchasingProductDetail" json:"purchasing_product_detail,omitempty"`
	CurrentActivity         *Activity              `protobuf:"bytes,3,opt,name=current_activity,json=currentActivity" json:"current_activity,omitempty"`
}

func (m *PurchaseActivityProductItemResp) Reset()         { *m = PurchaseActivityProductItemResp{} }
func (m *PurchaseActivityProductItemResp) String() string { return proto.CompactTextString(m) }
func (*PurchaseActivityProductItemResp) ProtoMessage()    {}
func (*PurchaseActivityProductItemResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMall, []int{43}
}

func (m *PurchaseActivityProductItemResp) GetPurchasedItem() *UserProductItem {
	if m != nil {
		return m.PurchasedItem
	}
	return nil
}

func (m *PurchaseActivityProductItemResp) GetPurchasingProductDetail() *ActivityProductDetail {
	if m != nil {
		return m.PurchasingProductDetail
	}
	return nil
}

func (m *PurchaseActivityProductItemResp) GetCurrentActivity() *Activity {
	if m != nil {
		return m.CurrentActivity
	}
	return nil
}

type GetUserProductsReq struct {
	Uid   uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Index uint32 `protobuf:"varint,2,req,name=index" json:"index"`
	Count uint32 `protobuf:"varint,3,req,name=count" json:"count"`
}

func (m *GetUserProductsReq) Reset()                    { *m = GetUserProductsReq{} }
func (m *GetUserProductsReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserProductsReq) ProtoMessage()               {}
func (*GetUserProductsReq) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{44} }

func (m *GetUserProductsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserProductsReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *GetUserProductsReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetUserProductsResp struct {
	UserItemList []*UserProductItem `protobuf:"bytes,1,rep,name=user_item_list,json=userItemList" json:"user_item_list,omitempty"`
}

func (m *GetUserProductsResp) Reset()                    { *m = GetUserProductsResp{} }
func (m *GetUserProductsResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserProductsResp) ProtoMessage()               {}
func (*GetUserProductsResp) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{45} }

func (m *GetUserProductsResp) GetUserItemList() []*UserProductItem {
	if m != nil {
		return m.UserItemList
	}
	return nil
}

type SetCurrentActivityReq struct {
	ActivityId       uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
	ActivityCategory uint32 `protobuf:"varint,2,opt,name=activity_category,json=activityCategory" json:"activity_category"`
}

func (m *SetCurrentActivityReq) Reset()                    { *m = SetCurrentActivityReq{} }
func (m *SetCurrentActivityReq) String() string            { return proto.CompactTextString(m) }
func (*SetCurrentActivityReq) ProtoMessage()               {}
func (*SetCurrentActivityReq) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{46} }

func (m *SetCurrentActivityReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *SetCurrentActivityReq) GetActivityCategory() uint32 {
	if m != nil {
		return m.ActivityCategory
	}
	return 0
}

type ActivityCategoryReq struct {
	ActivityCategory uint32 `protobuf:"varint,1,opt,name=activity_category,json=activityCategory" json:"activity_category"`
}

func (m *ActivityCategoryReq) Reset()                    { *m = ActivityCategoryReq{} }
func (m *ActivityCategoryReq) String() string            { return proto.CompactTextString(m) }
func (*ActivityCategoryReq) ProtoMessage()               {}
func (*ActivityCategoryReq) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{47} }

func (m *ActivityCategoryReq) GetActivityCategory() uint32 {
	if m != nil {
		return m.ActivityCategory
	}
	return 0
}

type FeedbackDiamondReq struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	OrderId string `protobuf:"bytes,2,req,name=order_id,json=orderId" json:"order_id"`
}

func (m *FeedbackDiamondReq) Reset()                    { *m = FeedbackDiamondReq{} }
func (m *FeedbackDiamondReq) String() string            { return proto.CompactTextString(m) }
func (*FeedbackDiamondReq) ProtoMessage()               {}
func (*FeedbackDiamondReq) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{48} }

func (m *FeedbackDiamondReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FeedbackDiamondReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type FeedbackDiamondResp struct {
	GetDiamond  uint32 `protobuf:"varint,1,req,name=get_diamond,json=getDiamond" json:"get_diamond"`
	UserDiamond uint32 `protobuf:"varint,2,req,name=user_diamond,json=userDiamond" json:"user_diamond"`
}

func (m *FeedbackDiamondResp) Reset()                    { *m = FeedbackDiamondResp{} }
func (m *FeedbackDiamondResp) String() string            { return proto.CompactTextString(m) }
func (*FeedbackDiamondResp) ProtoMessage()               {}
func (*FeedbackDiamondResp) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{49} }

func (m *FeedbackDiamondResp) GetGetDiamond() uint32 {
	if m != nil {
		return m.GetDiamond
	}
	return 0
}

func (m *FeedbackDiamondResp) GetUserDiamond() uint32 {
	if m != nil {
		return m.UserDiamond
	}
	return 0
}

type GetUserProductItemByOrderReq struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	OrderId string `protobuf:"bytes,2,req,name=order_id,json=orderId" json:"order_id"`
}

func (m *GetUserProductItemByOrderReq) Reset()         { *m = GetUserProductItemByOrderReq{} }
func (m *GetUserProductItemByOrderReq) String() string { return proto.CompactTextString(m) }
func (*GetUserProductItemByOrderReq) ProtoMessage()    {}
func (*GetUserProductItemByOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMall, []int{50}
}

func (m *GetUserProductItemByOrderReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserProductItemByOrderReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetProductTotalPurchaseTimesReq struct {
	ProductIdList []uint32 `protobuf:"varint,1,rep,name=product_id_list,json=productIdList" json:"product_id_list,omitempty"`
}

func (m *GetProductTotalPurchaseTimesReq) Reset()         { *m = GetProductTotalPurchaseTimesReq{} }
func (m *GetProductTotalPurchaseTimesReq) String() string { return proto.CompactTextString(m) }
func (*GetProductTotalPurchaseTimesReq) ProtoMessage()    {}
func (*GetProductTotalPurchaseTimesReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMall, []int{51}
}

func (m *GetProductTotalPurchaseTimesReq) GetProductIdList() []uint32 {
	if m != nil {
		return m.ProductIdList
	}
	return nil
}

type ProductTotalPurchaseTimes struct {
	ProductId           uint32 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	TotalPurchasedTimes uint32 `protobuf:"varint,2,req,name=total_purchased_times,json=totalPurchasedTimes" json:"total_purchased_times"`
}

func (m *ProductTotalPurchaseTimes) Reset()                    { *m = ProductTotalPurchaseTimes{} }
func (m *ProductTotalPurchaseTimes) String() string            { return proto.CompactTextString(m) }
func (*ProductTotalPurchaseTimes) ProtoMessage()               {}
func (*ProductTotalPurchaseTimes) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{52} }

func (m *ProductTotalPurchaseTimes) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *ProductTotalPurchaseTimes) GetTotalPurchasedTimes() uint32 {
	if m != nil {
		return m.TotalPurchasedTimes
	}
	return 0
}

type GetProductTotalPurchaseTimesResp struct {
	ProductPurchaseTimeList []*ProductTotalPurchaseTimes `protobuf:"bytes,1,rep,name=product_purchase_time_list,json=productPurchaseTimeList" json:"product_purchase_time_list,omitempty"`
}

func (m *GetProductTotalPurchaseTimesResp) Reset()         { *m = GetProductTotalPurchaseTimesResp{} }
func (m *GetProductTotalPurchaseTimesResp) String() string { return proto.CompactTextString(m) }
func (*GetProductTotalPurchaseTimesResp) ProtoMessage()    {}
func (*GetProductTotalPurchaseTimesResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMall, []int{53}
}

func (m *GetProductTotalPurchaseTimesResp) GetProductPurchaseTimeList() []*ProductTotalPurchaseTimes {
	if m != nil {
		return m.ProductPurchaseTimeList
	}
	return nil
}

type GetActivityWinningReq struct {
	ActivityId uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
}

func (m *GetActivityWinningReq) Reset()                    { *m = GetActivityWinningReq{} }
func (m *GetActivityWinningReq) String() string            { return proto.CompactTextString(m) }
func (*GetActivityWinningReq) ProtoMessage()               {}
func (*GetActivityWinningReq) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{54} }

func (m *GetActivityWinningReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

type ActivityWinningDetail struct {
	ProductName string `protobuf:"bytes,1,req,name=product_name,json=productName" json:"product_name"`
	Uid         uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	Account     string `protobuf:"bytes,3,req,name=account" json:"account"`
	Pwd         string `protobuf:"bytes,4,req,name=pwd" json:"pwd"`
}

func (m *ActivityWinningDetail) Reset()                    { *m = ActivityWinningDetail{} }
func (m *ActivityWinningDetail) String() string            { return proto.CompactTextString(m) }
func (*ActivityWinningDetail) ProtoMessage()               {}
func (*ActivityWinningDetail) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{55} }

func (m *ActivityWinningDetail) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *ActivityWinningDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ActivityWinningDetail) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ActivityWinningDetail) GetPwd() string {
	if m != nil {
		return m.Pwd
	}
	return ""
}

type GetActivityWinningDetailResp struct {
	WinningList []*ActivityWinningDetail `protobuf:"bytes,1,rep,name=winning_list,json=winningList" json:"winning_list,omitempty"`
}

func (m *GetActivityWinningDetailResp) Reset()         { *m = GetActivityWinningDetailResp{} }
func (m *GetActivityWinningDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetActivityWinningDetailResp) ProtoMessage()    {}
func (*GetActivityWinningDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMall, []int{56}
}

func (m *GetActivityWinningDetailResp) GetWinningList() []*ActivityWinningDetail {
	if m != nil {
		return m.WinningList
	}
	return nil
}

type ActivityWinningCount struct {
	ProductName string `protobuf:"bytes,1,req,name=product_name,json=productName" json:"product_name"`
	Count       uint32 `protobuf:"varint,2,req,name=count" json:"count"`
}

func (m *ActivityWinningCount) Reset()                    { *m = ActivityWinningCount{} }
func (m *ActivityWinningCount) String() string            { return proto.CompactTextString(m) }
func (*ActivityWinningCount) ProtoMessage()               {}
func (*ActivityWinningCount) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{57} }

func (m *ActivityWinningCount) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *ActivityWinningCount) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetActivityWinningCountResp struct {
	CountList []*ActivityWinningCount `protobuf:"bytes,1,rep,name=count_list,json=countList" json:"count_list,omitempty"`
}

func (m *GetActivityWinningCountResp) Reset()                    { *m = GetActivityWinningCountResp{} }
func (m *GetActivityWinningCountResp) String() string            { return proto.CompactTextString(m) }
func (*GetActivityWinningCountResp) ProtoMessage()               {}
func (*GetActivityWinningCountResp) Descriptor() ([]byte, []int) { return fileDescriptorMall, []int{58} }

func (m *GetActivityWinningCountResp) GetCountList() []*ActivityWinningCount {
	if m != nil {
		return m.CountList
	}
	return nil
}

func init() {
	proto.RegisterType((*Category)(nil), "Mall.Category")
	proto.RegisterType((*Product)(nil), "Mall.Product")
	proto.RegisterType((*ProductItem)(nil), "Mall.ProductItem")
	proto.RegisterType((*UserProductItem)(nil), "Mall.UserProductItem")
	proto.RegisterType((*ProductIdList)(nil), "Mall.ProductIdList")
	proto.RegisterType((*ProductList)(nil), "Mall.ProductList")
	proto.RegisterType((*CategoryList)(nil), "Mall.CategoryList")
	proto.RegisterType((*GetProductReg)(nil), "Mall.GetProductReg")
	proto.RegisterType((*ProductItem_AccountPassword)(nil), "Mall.ProductItem_AccountPassword")
	proto.RegisterType((*ProductItem_Medal)(nil), "Mall.ProductItem_Medal")
	proto.RegisterType((*ProductItem_TTStaff)(nil), "Mall.ProductItem_TTStaff")
	proto.RegisterType((*ActivityProductConfig)(nil), "Mall.ActivityProductConfig")
	proto.RegisterType((*Activity)(nil), "Mall.Activity")
	proto.RegisterType((*ActivityProductDetail)(nil), "Mall.ActivityProductDetail")
	proto.RegisterType((*CurrentActivityDetail)(nil), "Mall.CurrentActivityDetail")
	proto.RegisterType((*ActivityProductResult)(nil), "Mall.ActivityProductResult")
	proto.RegisterType((*ActivityPurchaseRecord)(nil), "Mall.ActivityPurchaseRecord")
	proto.RegisterType((*ActivitySnapshot)(nil), "Mall.ActivitySnapshot")
	proto.RegisterType((*CreateProductReq)(nil), "Mall.CreateProductReq")
	proto.RegisterType((*CreateProductResp)(nil), "Mall.CreateProductResp")
	proto.RegisterType((*UpdateProductReq)(nil), "Mall.UpdateProductReq")
	proto.RegisterType((*GetProductsByCategoryReq)(nil), "Mall.GetProductsByCategoryReq")
	proto.RegisterType((*AddProductItemReq_AccountPassword)(nil), "Mall.AddProductItemReq_AccountPassword")
	proto.RegisterType((*AddProductItemResp_AccountPassword)(nil), "Mall.AddProductItemResp_AccountPassword")
	proto.RegisterType((*RemainInfo)(nil), "Mall.RemainInfo")
	proto.RegisterType((*GetProductsRemainReq)(nil), "Mall.GetProductsRemainReq")
	proto.RegisterType((*GetProductsRemainResp)(nil), "Mall.GetProductsRemainResp")
	proto.RegisterType((*GetProductRemainsByCategoryReq)(nil), "Mall.GetProductRemainsByCategoryReq")
	proto.RegisterType((*GetProductRemainsByCategoryResp)(nil), "Mall.GetProductRemainsByCategoryResp")
	proto.RegisterType((*GetProductItemReq)(nil), "Mall.GetProductItemReq")
	proto.RegisterType((*GetProductItemResp)(nil), "Mall.GetProductItemResp")
	proto.RegisterType((*DeleteProductItemReq)(nil), "Mall.DeleteProductItemReq")
	proto.RegisterType((*CreateActivityReq)(nil), "Mall.CreateActivityReq")
	proto.RegisterType((*CreateActivityResp)(nil), "Mall.CreateActivityResp")
	proto.RegisterType((*UpdateActivityReq)(nil), "Mall.UpdateActivityReq")
	proto.RegisterType((*UpdateActivityResp)(nil), "Mall.UpdateActivityResp")
	proto.RegisterType((*DeleteActivityReq)(nil), "Mall.DeleteActivityReq")
	proto.RegisterType((*CloseActivityReq)(nil), "Mall.CloseActivityReq")
	proto.RegisterType((*GetActivityListReq)(nil), "Mall.GetActivityListReq")
	proto.RegisterType((*GetActivityListResp)(nil), "Mall.GetActivityListResp")
	proto.RegisterType((*AddProductItemsReq)(nil), "Mall.AddProductItemsReq")
	proto.RegisterType((*AddProductItemsResp)(nil), "Mall.AddProductItemsResp")
	proto.RegisterType((*PurchaseActivityProductItemReq)(nil), "Mall.PurchaseActivityProductItemReq")
	proto.RegisterType((*PurchaseActivityProductItemResp)(nil), "Mall.PurchaseActivityProductItemResp")
	proto.RegisterType((*GetUserProductsReq)(nil), "Mall.GetUserProductsReq")
	proto.RegisterType((*GetUserProductsResp)(nil), "Mall.GetUserProductsResp")
	proto.RegisterType((*SetCurrentActivityReq)(nil), "Mall.SetCurrentActivityReq")
	proto.RegisterType((*ActivityCategoryReq)(nil), "Mall.ActivityCategoryReq")
	proto.RegisterType((*FeedbackDiamondReq)(nil), "Mall.FeedbackDiamondReq")
	proto.RegisterType((*FeedbackDiamondResp)(nil), "Mall.FeedbackDiamondResp")
	proto.RegisterType((*GetUserProductItemByOrderReq)(nil), "Mall.GetUserProductItemByOrderReq")
	proto.RegisterType((*GetProductTotalPurchaseTimesReq)(nil), "Mall.GetProductTotalPurchaseTimesReq")
	proto.RegisterType((*ProductTotalPurchaseTimes)(nil), "Mall.ProductTotalPurchaseTimes")
	proto.RegisterType((*GetProductTotalPurchaseTimesResp)(nil), "Mall.GetProductTotalPurchaseTimesResp")
	proto.RegisterType((*GetActivityWinningReq)(nil), "Mall.GetActivityWinningReq")
	proto.RegisterType((*ActivityWinningDetail)(nil), "Mall.ActivityWinningDetail")
	proto.RegisterType((*GetActivityWinningDetailResp)(nil), "Mall.GetActivityWinningDetailResp")
	proto.RegisterType((*ActivityWinningCount)(nil), "Mall.ActivityWinningCount")
	proto.RegisterType((*GetActivityWinningCountResp)(nil), "Mall.GetActivityWinningCountResp")
	proto.RegisterEnum("Mall.CurrencyBizMinType", CurrencyBizMinType_name, CurrencyBizMinType_value)
	proto.RegisterEnum("Mall.Product_ProductStatus", Product_ProductStatus_name, Product_ProductStatus_value)
	proto.RegisterEnum("Mall.Product_ProductItemType", Product_ProductItemType_name, Product_ProductItemType_value)
	proto.RegisterEnum("Mall.Activity_ActivityStatus", Activity_ActivityStatus_name, Activity_ActivityStatus_value)
	proto.RegisterEnum("Mall.GetActivityListReq_Options", GetActivityListReq_Options_name, GetActivityListReq_Options_value)
}
func (m *Category) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Category) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.CategoryId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.CategoryName)))
	i += copy(dAtA[i:], m.CategoryName)
	return i, nil
}

func (m *Product) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Product) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x18
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.CategoryId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.Description)))
	i += copy(dAtA[i:], m.Description)
	dAtA[i] = 0x28
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x30
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Price))
	dAtA[i] = 0x38
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.LevelRequired))
	dAtA[i] = 0x40
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x4a
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.IconUrl)))
	i += copy(dAtA[i:], m.IconUrl)
	dAtA[i] = 0x50
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ItemType))
	return i, nil
}

func (m *ProductItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ProductItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.ItemHash)))
	i += copy(dAtA[i:], m.ItemHash)
	if m.ItemBinary != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintMall(dAtA, i, uint64(len(m.ItemBinary)))
		i += copy(dAtA[i:], m.ItemBinary)
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ProductId))
	return i, nil
}

func (m *UserProductItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserProductItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Timestamp))
	if m.ProductSnapshot == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("product_snapshot")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintMall(dAtA, i, uint64(m.ProductSnapshot.Size()))
		n1, err := m.ProductSnapshot.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if m.ItemSnapshot == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("item_snapshot")
	} else {
		dAtA[i] = 0x22
		i++
		i = encodeVarintMall(dAtA, i, uint64(m.ItemSnapshot.Size()))
		n2, err := m.ItemSnapshot.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x2a
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	return i, nil
}

func (m *ProductIdList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ProductIdList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ProductIdList) > 0 {
		for _, num := range m.ProductIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintMall(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *ProductList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ProductList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ProductList) > 0 {
		for _, msg := range m.ProductList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CategoryList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CategoryList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CategoryList) > 0 {
		for _, msg := range m.CategoryList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetProductReg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetProductReg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Index))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *ProductItem_AccountPassword) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ProductItem_AccountPassword) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x12
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.Password)))
	i += copy(dAtA[i:], m.Password)
	return i, nil
}

func (m *ProductItem_Medal) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ProductItem_Medal) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.MedalId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.MedalName)))
	i += copy(dAtA[i:], m.MedalName)
	return i, nil
}

func (m *ProductItem_TTStaff) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ProductItem_TTStaff) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.StaffId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.StaffName)))
	i += copy(dAtA[i:], m.StaffName)
	return i, nil
}

func (m *ActivityProductConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActivityProductConfig) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Coeff))
	return i, nil
}

func (m *Activity) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Activity) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x18
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x28
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Status))
	if len(m.ActivityProductConfigList) > 0 {
		for _, msg := range m.ActivityProductConfigList {
			dAtA[i] = 0x32
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ActivityProductDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActivityProductDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Product == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("product")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMall(dAtA, i, uint64(m.Product.Size()))
		n3, err := m.Product.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Total))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Remain))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.SoldOut))
	dAtA[i] = 0x28
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.TotalPurchased))
	return i, nil
}

func (m *CurrentActivityDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CurrentActivityDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Activity == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("activity")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMall(dAtA, i, uint64(m.Activity.Size()))
		n4, err := m.Activity.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	if len(m.ProductList) > 0 {
		for _, msg := range m.ProductList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ActivityProductResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActivityProductResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Total))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Purchased))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.SoldOut))
	return i, nil
}

func (m *ActivityPurchaseRecord) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActivityPurchaseRecord) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.PurchaseUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Timestamp))
	return i, nil
}

func (m *ActivitySnapshot) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActivitySnapshot) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Activity == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("activity")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMall(dAtA, i, uint64(m.Activity.Size()))
		n5, err := m.Activity.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if len(m.ProductSnapshotList) > 0 {
		for _, msg := range m.ProductSnapshotList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.ActivityProductResultList) > 0 {
		for _, msg := range m.ActivityProductResultList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.PurchaseTimeLine) > 0 {
		for _, msg := range m.PurchaseTimeLine {
			dAtA[i] = 0x22
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CreateProductReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateProductReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x10
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.CategoryId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.Description)))
	i += copy(dAtA[i:], m.Description)
	dAtA[i] = 0x20
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Price))
	dAtA[i] = 0x28
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.LevelRequired))
	dAtA[i] = 0x30
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.IconUrl)))
	i += copy(dAtA[i:], m.IconUrl)
	return i, nil
}

func (m *CreateProductResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateProductResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ProductId))
	return i, nil
}

func (m *UpdateProductReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateProductReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.Description)))
	i += copy(dAtA[i:], m.Description)
	dAtA[i] = 0x20
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Price))
	dAtA[i] = 0x28
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.LevelRequired))
	dAtA[i] = 0x30
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.IconUrl)))
	i += copy(dAtA[i:], m.IconUrl)
	return i, nil
}

func (m *GetProductsByCategoryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetProductsByCategoryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.CategoryId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.FromIndex))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *AddProductItemReq_AccountPassword) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddProductItemReq_AccountPassword) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ProductId))
	if len(m.AccountPasswordList) > 0 {
		for _, msg := range m.AccountPasswordList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddProductItemResp_AccountPassword) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddProductItemResp_AccountPassword) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.SuccessAccountPasswordList) > 0 {
		for _, msg := range m.SuccessAccountPasswordList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.FailedAccountPasswordList) > 0 {
		for _, msg := range m.FailedAccountPasswordList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *RemainInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemainInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ItemNum))
	return i, nil
}

func (m *GetProductsRemainReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetProductsRemainReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ProductIdList) > 0 {
		for _, num := range m.ProductIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintMall(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.QueryForActivity))
	return i, nil
}

func (m *GetProductsRemainResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetProductsRemainResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RemainInfoList) > 0 {
		for _, msg := range m.RemainInfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetProductRemainsByCategoryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetProductRemainsByCategoryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.CategoryId))
	return i, nil
}

func (m *GetProductRemainsByCategoryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetProductRemainsByCategoryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Remain))
	return i, nil
}

func (m *GetProductItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetProductItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ProductId))
	return i, nil
}

func (m *GetProductItemResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetProductItemResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ProductItemList) > 0 {
		for _, msg := range m.ProductItemList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *DeleteProductItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteProductItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ProductId))
	if len(m.HashList) > 0 {
		for _, s := range m.HashList {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *CreateActivityReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateActivityReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x10
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.EndTime))
	if len(m.ActivityProductConfigList) > 0 {
		for _, msg := range m.ActivityProductConfigList {
			dAtA[i] = 0x22
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CreateActivityResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateActivityResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.ErrorMessage)))
	i += copy(dAtA[i:], m.ErrorMessage)
	return i, nil
}

func (m *UpdateActivityReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateActivityReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x18
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.EndTime))
	if len(m.ActivityProductConfigList) > 0 {
		for _, msg := range m.ActivityProductConfigList {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UpdateActivityResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateActivityResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.ErrorMessage)))
	i += copy(dAtA[i:], m.ErrorMessage)
	return i, nil
}

func (m *DeleteActivityReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteActivityReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ActivityId))
	return i, nil
}

func (m *CloseActivityReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CloseActivityReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ActivityId))
	return i, nil
}

func (m *GetActivityListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActivityListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Options))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.FromIndex))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x20
	i++
	if m.Reverse {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetActivityListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActivityListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ActivityList) > 0 {
		for _, msg := range m.ActivityList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddProductItemsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddProductItemsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ProductItemList) > 0 {
		for _, msg := range m.ProductItemList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddProductItemsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddProductItemsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.SuccessProductItemList) > 0 {
		for _, msg := range m.SuccessProductItemList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.FailedProductItemList) > 0 {
		for _, msg := range m.FailedProductItemList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *PurchaseActivityProductItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PurchaseActivityProductItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ActivityCategory))
	return i, nil
}

func (m *PurchaseActivityProductItemResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PurchaseActivityProductItemResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.PurchasedItem != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMall(dAtA, i, uint64(m.PurchasedItem.Size()))
		n6, err := m.PurchasedItem.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if m.PurchasingProductDetail != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintMall(dAtA, i, uint64(m.PurchasingProductDetail.Size()))
		n7, err := m.PurchasingProductDetail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if m.CurrentActivity != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintMall(dAtA, i, uint64(m.CurrentActivity.Size()))
		n8, err := m.CurrentActivity.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *GetUserProductsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserProductsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Index))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *GetUserProductsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserProductsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UserItemList) > 0 {
		for _, msg := range m.UserItemList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetCurrentActivityReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetCurrentActivityReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ActivityCategory))
	return i, nil
}

func (m *ActivityCategoryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActivityCategoryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ActivityCategory))
	return i, nil
}

func (m *FeedbackDiamondReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FeedbackDiamondReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	return i, nil
}

func (m *FeedbackDiamondResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FeedbackDiamondResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.GetDiamond))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.UserDiamond))
	return i, nil
}

func (m *GetUserProductItemByOrderReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserProductItemByOrderReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	return i, nil
}

func (m *GetProductTotalPurchaseTimesReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetProductTotalPurchaseTimesReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ProductIdList) > 0 {
		for _, num := range m.ProductIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintMall(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *ProductTotalPurchaseTimes) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ProductTotalPurchaseTimes) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.TotalPurchasedTimes))
	return i, nil
}

func (m *GetProductTotalPurchaseTimesResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetProductTotalPurchaseTimesResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ProductPurchaseTimeList) > 0 {
		for _, msg := range m.ProductPurchaseTimeList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetActivityWinningReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActivityWinningReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.ActivityId))
	return i, nil
}

func (m *ActivityWinningDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActivityWinningDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.ProductName)))
	i += copy(dAtA[i:], m.ProductName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x22
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.Pwd)))
	i += copy(dAtA[i:], m.Pwd)
	return i, nil
}

func (m *GetActivityWinningDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActivityWinningDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.WinningList) > 0 {
		for _, msg := range m.WinningList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ActivityWinningCount) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActivityWinningCount) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintMall(dAtA, i, uint64(len(m.ProductName)))
	i += copy(dAtA[i:], m.ProductName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintMall(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *GetActivityWinningCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActivityWinningCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CountList) > 0 {
		for _, msg := range m.CountList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMall(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func encodeFixed64Mall(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Mall(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintMall(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *Category) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.CategoryId))
	l = len(m.CategoryName)
	n += 1 + l + sovMall(uint64(l))
	return n
}

func (m *Product) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.ProductId))
	l = len(m.Name)
	n += 1 + l + sovMall(uint64(l))
	n += 1 + sovMall(uint64(m.CategoryId))
	l = len(m.Description)
	n += 1 + l + sovMall(uint64(l))
	n += 1 + sovMall(uint64(m.Status))
	n += 1 + sovMall(uint64(m.Price))
	n += 1 + sovMall(uint64(m.LevelRequired))
	n += 1 + sovMall(uint64(m.GameId))
	l = len(m.IconUrl)
	n += 1 + l + sovMall(uint64(l))
	n += 1 + sovMall(uint64(m.ItemType))
	return n
}

func (m *ProductItem) Size() (n int) {
	var l int
	_ = l
	l = len(m.ItemHash)
	n += 1 + l + sovMall(uint64(l))
	if m.ItemBinary != nil {
		l = len(m.ItemBinary)
		n += 1 + l + sovMall(uint64(l))
	}
	n += 1 + sovMall(uint64(m.ProductId))
	return n
}

func (m *UserProductItem) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.Uid))
	n += 1 + sovMall(uint64(m.Timestamp))
	if m.ProductSnapshot != nil {
		l = m.ProductSnapshot.Size()
		n += 1 + l + sovMall(uint64(l))
	}
	if m.ItemSnapshot != nil {
		l = m.ItemSnapshot.Size()
		n += 1 + l + sovMall(uint64(l))
	}
	l = len(m.OrderId)
	n += 1 + l + sovMall(uint64(l))
	return n
}

func (m *ProductIdList) Size() (n int) {
	var l int
	_ = l
	if len(m.ProductIdList) > 0 {
		for _, e := range m.ProductIdList {
			n += 1 + sovMall(uint64(e))
		}
	}
	return n
}

func (m *ProductList) Size() (n int) {
	var l int
	_ = l
	if len(m.ProductList) > 0 {
		for _, e := range m.ProductList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	return n
}

func (m *CategoryList) Size() (n int) {
	var l int
	_ = l
	if len(m.CategoryList) > 0 {
		for _, e := range m.CategoryList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	return n
}

func (m *GetProductReg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.Uid))
	n += 1 + sovMall(uint64(m.Index))
	n += 1 + sovMall(uint64(m.Count))
	return n
}

func (m *ProductItem_AccountPassword) Size() (n int) {
	var l int
	_ = l
	l = len(m.Account)
	n += 1 + l + sovMall(uint64(l))
	l = len(m.Password)
	n += 1 + l + sovMall(uint64(l))
	return n
}

func (m *ProductItem_Medal) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.MedalId))
	l = len(m.MedalName)
	n += 1 + l + sovMall(uint64(l))
	return n
}

func (m *ProductItem_TTStaff) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.StaffId))
	l = len(m.StaffName)
	n += 1 + l + sovMall(uint64(l))
	return n
}

func (m *ActivityProductConfig) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.ProductId))
	n += 1 + sovMall(uint64(m.Count))
	n += 1 + sovMall(uint64(m.Coeff))
	return n
}

func (m *Activity) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.ActivityId))
	l = len(m.Name)
	n += 1 + l + sovMall(uint64(l))
	n += 1 + sovMall(uint64(m.BeginTime))
	n += 1 + sovMall(uint64(m.EndTime))
	n += 1 + sovMall(uint64(m.Status))
	if len(m.ActivityProductConfigList) > 0 {
		for _, e := range m.ActivityProductConfigList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	return n
}

func (m *ActivityProductDetail) Size() (n int) {
	var l int
	_ = l
	if m.Product != nil {
		l = m.Product.Size()
		n += 1 + l + sovMall(uint64(l))
	}
	n += 1 + sovMall(uint64(m.Total))
	n += 1 + sovMall(uint64(m.Remain))
	n += 1 + sovMall(uint64(m.SoldOut))
	n += 1 + sovMall(uint64(m.TotalPurchased))
	return n
}

func (m *CurrentActivityDetail) Size() (n int) {
	var l int
	_ = l
	if m.Activity != nil {
		l = m.Activity.Size()
		n += 1 + l + sovMall(uint64(l))
	}
	if len(m.ProductList) > 0 {
		for _, e := range m.ProductList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	return n
}

func (m *ActivityProductResult) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.ProductId))
	n += 1 + sovMall(uint64(m.Total))
	n += 1 + sovMall(uint64(m.Purchased))
	n += 1 + sovMall(uint64(m.SoldOut))
	return n
}

func (m *ActivityPurchaseRecord) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.ProductId))
	n += 1 + sovMall(uint64(m.PurchaseUid))
	n += 1 + sovMall(uint64(m.Timestamp))
	return n
}

func (m *ActivitySnapshot) Size() (n int) {
	var l int
	_ = l
	if m.Activity != nil {
		l = m.Activity.Size()
		n += 1 + l + sovMall(uint64(l))
	}
	if len(m.ProductSnapshotList) > 0 {
		for _, e := range m.ProductSnapshotList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	if len(m.ActivityProductResultList) > 0 {
		for _, e := range m.ActivityProductResultList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	if len(m.PurchaseTimeLine) > 0 {
		for _, e := range m.PurchaseTimeLine {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	return n
}

func (m *CreateProductReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Name)
	n += 1 + l + sovMall(uint64(l))
	n += 1 + sovMall(uint64(m.CategoryId))
	l = len(m.Description)
	n += 1 + l + sovMall(uint64(l))
	n += 1 + sovMall(uint64(m.Price))
	n += 1 + sovMall(uint64(m.LevelRequired))
	n += 1 + sovMall(uint64(m.GameId))
	l = len(m.IconUrl)
	n += 1 + l + sovMall(uint64(l))
	return n
}

func (m *CreateProductResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.ProductId))
	return n
}

func (m *UpdateProductReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.ProductId))
	l = len(m.Name)
	n += 1 + l + sovMall(uint64(l))
	l = len(m.Description)
	n += 1 + l + sovMall(uint64(l))
	n += 1 + sovMall(uint64(m.Price))
	n += 1 + sovMall(uint64(m.LevelRequired))
	n += 1 + sovMall(uint64(m.GameId))
	l = len(m.IconUrl)
	n += 1 + l + sovMall(uint64(l))
	return n
}

func (m *GetProductsByCategoryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.CategoryId))
	n += 1 + sovMall(uint64(m.FromIndex))
	n += 1 + sovMall(uint64(m.Count))
	return n
}

func (m *AddProductItemReq_AccountPassword) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.ProductId))
	if len(m.AccountPasswordList) > 0 {
		for _, e := range m.AccountPasswordList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	return n
}

func (m *AddProductItemResp_AccountPassword) Size() (n int) {
	var l int
	_ = l
	if len(m.SuccessAccountPasswordList) > 0 {
		for _, e := range m.SuccessAccountPasswordList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	if len(m.FailedAccountPasswordList) > 0 {
		for _, e := range m.FailedAccountPasswordList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	return n
}

func (m *RemainInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.ProductId))
	n += 1 + sovMall(uint64(m.ItemNum))
	return n
}

func (m *GetProductsRemainReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ProductIdList) > 0 {
		for _, e := range m.ProductIdList {
			n += 1 + sovMall(uint64(e))
		}
	}
	n += 1 + sovMall(uint64(m.QueryForActivity))
	return n
}

func (m *GetProductsRemainResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RemainInfoList) > 0 {
		for _, e := range m.RemainInfoList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	return n
}

func (m *GetProductRemainsByCategoryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.CategoryId))
	return n
}

func (m *GetProductRemainsByCategoryResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.Remain))
	return n
}

func (m *GetProductItemReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.ProductId))
	return n
}

func (m *GetProductItemResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ProductItemList) > 0 {
		for _, e := range m.ProductItemList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	return n
}

func (m *DeleteProductItemReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.ProductId))
	if len(m.HashList) > 0 {
		for _, s := range m.HashList {
			l = len(s)
			n += 1 + l + sovMall(uint64(l))
		}
	}
	return n
}

func (m *CreateActivityReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Name)
	n += 1 + l + sovMall(uint64(l))
	n += 1 + sovMall(uint64(m.BeginTime))
	n += 1 + sovMall(uint64(m.EndTime))
	if len(m.ActivityProductConfigList) > 0 {
		for _, e := range m.ActivityProductConfigList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	return n
}

func (m *CreateActivityResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.ActivityId))
	l = len(m.ErrorMessage)
	n += 1 + l + sovMall(uint64(l))
	return n
}

func (m *UpdateActivityReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.ActivityId))
	l = len(m.Name)
	n += 1 + l + sovMall(uint64(l))
	n += 1 + sovMall(uint64(m.BeginTime))
	n += 1 + sovMall(uint64(m.EndTime))
	if len(m.ActivityProductConfigList) > 0 {
		for _, e := range m.ActivityProductConfigList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	return n
}

func (m *UpdateActivityResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.ErrorMessage)
	n += 1 + l + sovMall(uint64(l))
	return n
}

func (m *DeleteActivityReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.ActivityId))
	return n
}

func (m *CloseActivityReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.ActivityId))
	return n
}

func (m *GetActivityListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.Options))
	n += 1 + sovMall(uint64(m.FromIndex))
	n += 1 + sovMall(uint64(m.Count))
	n += 2
	return n
}

func (m *GetActivityListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ActivityList) > 0 {
		for _, e := range m.ActivityList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	return n
}

func (m *AddProductItemsReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ProductItemList) > 0 {
		for _, e := range m.ProductItemList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	return n
}

func (m *AddProductItemsResp) Size() (n int) {
	var l int
	_ = l
	if len(m.SuccessProductItemList) > 0 {
		for _, e := range m.SuccessProductItemList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	if len(m.FailedProductItemList) > 0 {
		for _, e := range m.FailedProductItemList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	return n
}

func (m *PurchaseActivityProductItemReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.Uid))
	n += 1 + sovMall(uint64(m.ActivityId))
	n += 1 + sovMall(uint64(m.ProductId))
	n += 1 + sovMall(uint64(m.ActivityCategory))
	return n
}

func (m *PurchaseActivityProductItemResp) Size() (n int) {
	var l int
	_ = l
	if m.PurchasedItem != nil {
		l = m.PurchasedItem.Size()
		n += 1 + l + sovMall(uint64(l))
	}
	if m.PurchasingProductDetail != nil {
		l = m.PurchasingProductDetail.Size()
		n += 1 + l + sovMall(uint64(l))
	}
	if m.CurrentActivity != nil {
		l = m.CurrentActivity.Size()
		n += 1 + l + sovMall(uint64(l))
	}
	return n
}

func (m *GetUserProductsReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.Uid))
	n += 1 + sovMall(uint64(m.Index))
	n += 1 + sovMall(uint64(m.Count))
	return n
}

func (m *GetUserProductsResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UserItemList) > 0 {
		for _, e := range m.UserItemList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	return n
}

func (m *SetCurrentActivityReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.ActivityId))
	n += 1 + sovMall(uint64(m.ActivityCategory))
	return n
}

func (m *ActivityCategoryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.ActivityCategory))
	return n
}

func (m *FeedbackDiamondReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.Uid))
	l = len(m.OrderId)
	n += 1 + l + sovMall(uint64(l))
	return n
}

func (m *FeedbackDiamondResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.GetDiamond))
	n += 1 + sovMall(uint64(m.UserDiamond))
	return n
}

func (m *GetUserProductItemByOrderReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.Uid))
	l = len(m.OrderId)
	n += 1 + l + sovMall(uint64(l))
	return n
}

func (m *GetProductTotalPurchaseTimesReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ProductIdList) > 0 {
		for _, e := range m.ProductIdList {
			n += 1 + sovMall(uint64(e))
		}
	}
	return n
}

func (m *ProductTotalPurchaseTimes) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.ProductId))
	n += 1 + sovMall(uint64(m.TotalPurchasedTimes))
	return n
}

func (m *GetProductTotalPurchaseTimesResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ProductPurchaseTimeList) > 0 {
		for _, e := range m.ProductPurchaseTimeList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	return n
}

func (m *GetActivityWinningReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMall(uint64(m.ActivityId))
	return n
}

func (m *ActivityWinningDetail) Size() (n int) {
	var l int
	_ = l
	l = len(m.ProductName)
	n += 1 + l + sovMall(uint64(l))
	n += 1 + sovMall(uint64(m.Uid))
	l = len(m.Account)
	n += 1 + l + sovMall(uint64(l))
	l = len(m.Pwd)
	n += 1 + l + sovMall(uint64(l))
	return n
}

func (m *GetActivityWinningDetailResp) Size() (n int) {
	var l int
	_ = l
	if len(m.WinningList) > 0 {
		for _, e := range m.WinningList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	return n
}

func (m *ActivityWinningCount) Size() (n int) {
	var l int
	_ = l
	l = len(m.ProductName)
	n += 1 + l + sovMall(uint64(l))
	n += 1 + sovMall(uint64(m.Count))
	return n
}

func (m *GetActivityWinningCountResp) Size() (n int) {
	var l int
	_ = l
	if len(m.CountList) > 0 {
		for _, e := range m.CountList {
			l = e.Size()
			n += 1 + l + sovMall(uint64(l))
		}
	}
	return n
}

func sovMall(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozMall(x uint64) (n int) {
	return sovMall(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *Category) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Category: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Category: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CategoryId", wireType)
			}
			m.CategoryId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CategoryId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CategoryName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CategoryName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("category_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("category_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Product) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Product: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Product: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CategoryId", wireType)
			}
			m.CategoryId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CategoryId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelRequired", wireType)
			}
			m.LevelRequired = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LevelRequired |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("category_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("description")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("price")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level_required")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("icon_url")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ProductItem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ProductItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ProductItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemHash", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemHash = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemBinary", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemBinary = append(m.ItemBinary[:0], dAtA[iNdEx:postIndex]...)
			if m.ItemBinary == nil {
				m.ItemBinary = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_hash")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_binary")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserProductItem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserProductItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserProductItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductSnapshot", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ProductSnapshot == nil {
				m.ProductSnapshot = &Product{}
			}
			if err := m.ProductSnapshot.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemSnapshot", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ItemSnapshot == nil {
				m.ItemSnapshot = &ProductItem{}
			}
			if err := m.ItemSnapshot.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("timestamp")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_snapshot")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_snapshot")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ProductIdList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ProductIdList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ProductIdList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMall
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ProductIdList = append(m.ProductIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMall
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMall
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMall
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ProductIdList = append(m.ProductIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ProductList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ProductList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ProductList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProductList = append(m.ProductList, &Product{})
			if err := m.ProductList[len(m.ProductList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CategoryList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CategoryList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CategoryList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CategoryList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CategoryList = append(m.CategoryList, &Category{})
			if err := m.CategoryList[len(m.CategoryList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetProductReg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetProductReg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetProductReg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("index")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ProductItem_AccountPassword) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ProductItem_AccountPassword: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ProductItem_AccountPassword: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Password", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Password = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("password")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ProductItem_Medal) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ProductItem_Medal: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ProductItem_Medal: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MedalId", wireType)
			}
			m.MedalId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MedalId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MedalName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MedalName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("medal_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("medal_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ProductItem_TTStaff) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ProductItem_TTStaff: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ProductItem_TTStaff: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StaffId", wireType)
			}
			m.StaffId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StaffId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StaffName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StaffName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("staff_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("staff_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActivityProductConfig) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActivityProductConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActivityProductConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Coeff", wireType)
			}
			m.Coeff = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Coeff |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Activity) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Activity: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Activity: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityProductConfigList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActivityProductConfigList = append(m.ActivityProductConfigList, &ActivityProductConfig{})
			if err := m.ActivityProductConfigList[len(m.ActivityProductConfigList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActivityProductDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActivityProductDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActivityProductDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Product", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Product == nil {
				m.Product = &Product{}
			}
			if err := m.Product.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Remain", wireType)
			}
			m.Remain = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Remain |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SoldOut", wireType)
			}
			m.SoldOut = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SoldOut |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalPurchased", wireType)
			}
			m.TotalPurchased = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalPurchased |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("remain")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CurrentActivityDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CurrentActivityDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CurrentActivityDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Activity", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Activity == nil {
				m.Activity = &Activity{}
			}
			if err := m.Activity.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProductList = append(m.ProductList, &ActivityProductDetail{})
			if err := m.ProductList[len(m.ProductList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActivityProductResult) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActivityProductResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActivityProductResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Purchased", wireType)
			}
			m.Purchased = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Purchased |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SoldOut", wireType)
			}
			m.SoldOut = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SoldOut |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("purchased")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActivityPurchaseRecord) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActivityPurchaseRecord: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActivityPurchaseRecord: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PurchaseUid", wireType)
			}
			m.PurchaseUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PurchaseUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("purchase_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("timestamp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActivitySnapshot) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActivitySnapshot: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActivitySnapshot: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Activity", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Activity == nil {
				m.Activity = &Activity{}
			}
			if err := m.Activity.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductSnapshotList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProductSnapshotList = append(m.ProductSnapshotList, &Product{})
			if err := m.ProductSnapshotList[len(m.ProductSnapshotList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityProductResultList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActivityProductResultList = append(m.ActivityProductResultList, &ActivityProductResult{})
			if err := m.ActivityProductResultList[len(m.ActivityProductResultList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PurchaseTimeLine", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PurchaseTimeLine = append(m.PurchaseTimeLine, &ActivityPurchaseRecord{})
			if err := m.PurchaseTimeLine[len(m.PurchaseTimeLine)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateProductReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateProductReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateProductReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CategoryId", wireType)
			}
			m.CategoryId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CategoryId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelRequired", wireType)
			}
			m.LevelRequired = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LevelRequired |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("category_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("description")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("price")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level_required")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("icon_url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateProductResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateProductResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateProductResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateProductReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateProductReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateProductReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelRequired", wireType)
			}
			m.LevelRequired = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LevelRequired |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetProductsByCategoryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetProductsByCategoryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetProductsByCategoryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CategoryId", wireType)
			}
			m.CategoryId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CategoryId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromIndex", wireType)
			}
			m.FromIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("category_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("from_index")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddProductItemReq_AccountPassword) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddProductItemReq_AccountPassword: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddProductItemReq_AccountPassword: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccountPasswordList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AccountPasswordList = append(m.AccountPasswordList, &ProductItem_AccountPassword{})
			if err := m.AccountPasswordList[len(m.AccountPasswordList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddProductItemResp_AccountPassword) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddProductItemResp_AccountPassword: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddProductItemResp_AccountPassword: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SuccessAccountPasswordList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SuccessAccountPasswordList = append(m.SuccessAccountPasswordList, &ProductItem_AccountPassword{})
			if err := m.SuccessAccountPasswordList[len(m.SuccessAccountPasswordList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FailedAccountPasswordList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FailedAccountPasswordList = append(m.FailedAccountPasswordList, &ProductItem_AccountPassword{})
			if err := m.FailedAccountPasswordList[len(m.FailedAccountPasswordList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemainInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemainInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemainInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemNum", wireType)
			}
			m.ItemNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_num")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetProductsRemainReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetProductsRemainReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetProductsRemainReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMall
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ProductIdList = append(m.ProductIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMall
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMall
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMall
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ProductIdList = append(m.ProductIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductIdList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QueryForActivity", wireType)
			}
			m.QueryForActivity = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QueryForActivity |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetProductsRemainResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetProductsRemainResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetProductsRemainResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RemainInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RemainInfoList = append(m.RemainInfoList, &RemainInfo{})
			if err := m.RemainInfoList[len(m.RemainInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetProductRemainsByCategoryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetProductRemainsByCategoryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetProductRemainsByCategoryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CategoryId", wireType)
			}
			m.CategoryId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CategoryId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("category_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetProductRemainsByCategoryResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetProductRemainsByCategoryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetProductRemainsByCategoryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Remain", wireType)
			}
			m.Remain = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Remain |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("remain")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetProductItemReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetProductItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetProductItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetProductItemResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetProductItemResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetProductItemResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProductItemList = append(m.ProductItemList, &ProductItem{})
			if err := m.ProductItemList[len(m.ProductItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteProductItemReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteProductItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteProductItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HashList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HashList = append(m.HashList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateActivityReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateActivityReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateActivityReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityProductConfigList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActivityProductConfigList = append(m.ActivityProductConfigList, &ActivityProductConfig{})
			if err := m.ActivityProductConfigList[len(m.ActivityProductConfigList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateActivityResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateActivityResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateActivityResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ErrorMessage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ErrorMessage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateActivityReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateActivityReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateActivityReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityProductConfigList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActivityProductConfigList = append(m.ActivityProductConfigList, &ActivityProductConfig{})
			if err := m.ActivityProductConfigList[len(m.ActivityProductConfigList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateActivityResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateActivityResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateActivityResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ErrorMessage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ErrorMessage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteActivityReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteActivityReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteActivityReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CloseActivityReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CloseActivityReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CloseActivityReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActivityListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActivityListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActivityListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Options", wireType)
			}
			m.Options = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Options |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromIndex", wireType)
			}
			m.FromIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Reverse", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Reverse = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("options")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("from_index")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("reverse")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActivityListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActivityListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActivityListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActivityList = append(m.ActivityList, &Activity{})
			if err := m.ActivityList[len(m.ActivityList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddProductItemsReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddProductItemsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddProductItemsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProductItemList = append(m.ProductItemList, &ProductItem{})
			if err := m.ProductItemList[len(m.ProductItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddProductItemsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddProductItemsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddProductItemsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SuccessProductItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SuccessProductItemList = append(m.SuccessProductItemList, &ProductItem{})
			if err := m.SuccessProductItemList[len(m.SuccessProductItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FailedProductItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FailedProductItemList = append(m.FailedProductItemList, &ProductItem{})
			if err := m.FailedProductItemList[len(m.FailedProductItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PurchaseActivityProductItemReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PurchaseActivityProductItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PurchaseActivityProductItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityCategory", wireType)
			}
			m.ActivityCategory = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityCategory |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PurchaseActivityProductItemResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PurchaseActivityProductItemResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PurchaseActivityProductItemResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PurchasedItem", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.PurchasedItem == nil {
				m.PurchasedItem = &UserProductItem{}
			}
			if err := m.PurchasedItem.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PurchasingProductDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.PurchasingProductDetail == nil {
				m.PurchasingProductDetail = &ActivityProductDetail{}
			}
			if err := m.PurchasingProductDetail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrentActivity", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CurrentActivity == nil {
				m.CurrentActivity = &Activity{}
			}
			if err := m.CurrentActivity.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserProductsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserProductsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserProductsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("index")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserProductsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserProductsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserProductsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserItemList = append(m.UserItemList, &UserProductItem{})
			if err := m.UserItemList[len(m.UserItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetCurrentActivityReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetCurrentActivityReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetCurrentActivityReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityCategory", wireType)
			}
			m.ActivityCategory = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityCategory |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActivityCategoryReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActivityCategoryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActivityCategoryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityCategory", wireType)
			}
			m.ActivityCategory = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityCategory |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FeedbackDiamondReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FeedbackDiamondReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FeedbackDiamondReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FeedbackDiamondResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FeedbackDiamondResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FeedbackDiamondResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GetDiamond", wireType)
			}
			m.GetDiamond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GetDiamond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserDiamond", wireType)
			}
			m.UserDiamond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserDiamond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("get_diamond")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_diamond")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserProductItemByOrderReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserProductItemByOrderReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserProductItemByOrderReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetProductTotalPurchaseTimesReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetProductTotalPurchaseTimesReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetProductTotalPurchaseTimesReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMall
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ProductIdList = append(m.ProductIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMall
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMall
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMall
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ProductIdList = append(m.ProductIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ProductTotalPurchaseTimes) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ProductTotalPurchaseTimes: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ProductTotalPurchaseTimes: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalPurchasedTimes", wireType)
			}
			m.TotalPurchasedTimes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalPurchasedTimes |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_purchased_times")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetProductTotalPurchaseTimesResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetProductTotalPurchaseTimesResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetProductTotalPurchaseTimesResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductPurchaseTimeList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProductPurchaseTimeList = append(m.ProductPurchaseTimeList, &ProductTotalPurchaseTimes{})
			if err := m.ProductPurchaseTimeList[len(m.ProductPurchaseTimeList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActivityWinningReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActivityWinningReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActivityWinningReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActivityWinningDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActivityWinningDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActivityWinningDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProductName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Pwd", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Pwd = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pwd")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActivityWinningDetailResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActivityWinningDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActivityWinningDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WinningList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.WinningList = append(m.WinningList, &ActivityWinningDetail{})
			if err := m.WinningList[len(m.WinningList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActivityWinningCount) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActivityWinningCount: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActivityWinningCount: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProductName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActivityWinningCountResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMall
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActivityWinningCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActivityWinningCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CountList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMall
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMall
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CountList = append(m.CountList, &ActivityWinningCount{})
			if err := m.CountList[len(m.CountList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMall(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMall
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipMall(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowMall
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMall
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMall
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthMall
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowMall
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipMall(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthMall = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowMall   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("services/mall/mall.proto", fileDescriptorMall) }

var fileDescriptorMall = []byte{
	// 3232 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x59, 0x4b, 0x6c, 0x23, 0xc7,
	0xd1, 0xde, 0x21, 0xf5, 0x62, 0x89, 0x94, 0xa8, 0xd6, 0x6a, 0x97, 0xa2, 0x64, 0xed, 0xec, 0xf8,
	0xb1, 0x5a, 0xff, 0x96, 0xec, 0x5d, 0xff, 0xf8, 0x7f, 0x9b, 0x3f, 0x4d, 0x43, 0x92, 0x7f, 0xdb,
	0xb2, 0xf7, 0x21, 0x53, 0x5a, 0xac, 0x1d, 0x3b, 0x21, 0x66, 0x39, 0x4d, 0x6a, 0x2c, 0x72, 0x66,
	0x34, 0x3d, 0x94, 0x23, 0xc3, 0x07, 0xc7, 0xc9, 0x21, 0x31, 0x10, 0xc0, 0x48, 0xce, 0x41, 0x2e,
	0x3e, 0x25, 0x08, 0x72, 0x4b, 0x4e, 0xb9, 0x26, 0x8e, 0xf3, 0x40, 0x72, 0xc9, 0xd1, 0x30, 0x9c,
	0x8b, 0x4f, 0xb9, 0xe4, 0x92, 0x63, 0xd0, 0xdd, 0xd3, 0xc3, 0xee, 0x99, 0xe1, 0x43, 0xab, 0x4b,
	0x2e, 0x02, 0x55, 0xdd, 0x5d, 0x55, 0x5d, 0x55, 0x5d, 0x8f, 0x6f, 0xa0, 0x44, 0xb0, 0x7f, 0x62,
	0x37, 0x31, 0x79, 0xba, 0x6b, 0x76, 0x3a, 0xec, 0xcf, 0xa6, 0xe7, 0xbb, 0x81, 0x8b, 0x26, 0x6e,
	0x9b, 0x9d, 0x4e, 0xf9, 0xb1, 0xa6, 0xdb, 0xed, 0xba, 0xce, 0xd3, 0x41, 0xe7, 0xc4, 0xb3, 0x9b,
	0x47, 0x1d, 0xfc, 0x34, 0x39, 0x7a, 0xd0, 0xb3, 0x3b, 0x81, 0xed, 0x04, 0xa7, 0x1e, 0xe6, 0x7b,
	0x8d, 0x77, 0x60, 0x66, 0xc7, 0x0c, 0x70, 0xdb, 0xf5, 0x4f, 0xd1, 0xe3, 0x30, 0xdb, 0x0c, 0x7f,
	0x37, 0x6c, 0xab, 0xa4, 0xe9, 0x99, 0xf5, 0xc2, 0xf6, 0xc4, 0x67, 0x5f, 0x5c, 0xb9, 0x50, 0x07,
	0xb1, 0xb0, 0x6b, 0xa1, 0xeb, 0x50, 0x88, 0xb6, 0x39, 0x66, 0x17, 0x97, 0x32, 0x7a, 0x66, 0x3d,
	0x17, 0x6e, 0xcc, 0x8b, 0xa5, 0x3b, 0x66, 0x17, 0x1b, 0x7f, 0xce, 0xc2, 0xf4, 0x9e, 0xef, 0x5a,
	0xbd, 0x66, 0x80, 0x1e, 0x05, 0xf0, 0xf8, 0xcf, 0x38, 0xf3, 0x5c, 0x48, 0xdf, 0xb5, 0x50, 0x09,
	0x26, 0x12, 0x2c, 0x19, 0x25, 0xae, 0x5c, 0x76, 0x80, 0x72, 0x4f, 0xc0, 0xac, 0x85, 0x49, 0xd3,
	0xb7, 0xbd, 0xc0, 0x76, 0x9d, 0xd2, 0x84, 0xc4, 0x47, 0x5e, 0x40, 0xab, 0x30, 0x45, 0x02, 0x33,
	0xe8, 0x91, 0xd2, 0xa4, 0xc4, 0x29, 0xa4, 0xa1, 0x32, 0x4c, 0x7a, 0xbe, 0xdd, 0xc4, 0xa5, 0x29,
	0x69, 0x91, 0x93, 0xd0, 0x7f, 0xc1, 0x5c, 0x07, 0x9f, 0xe0, 0x4e, 0xc3, 0xc7, 0xc7, 0x3d, 0xdb,
	0xc7, 0x56, 0x69, 0x5a, 0xda, 0x54, 0x60, 0x6b, 0xf5, 0x70, 0x09, 0x3d, 0x02, 0xd3, 0x6d, 0xb3,
	0x8b, 0xa9, 0xc6, 0x33, 0xb2, 0x1c, 0x4a, 0xdc, 0xb5, 0xd0, 0x15, 0x98, 0xb1, 0x9b, 0xae, 0xd3,
	0xe8, 0xf9, 0x9d, 0x52, 0x4e, 0x52, 0x75, 0x9a, 0x52, 0xef, 0xf9, 0x1d, 0x74, 0x15, 0x72, 0x76,
	0x80, 0xbb, 0x0d, 0xea, 0xb1, 0x12, 0x48, 0x1c, 0x66, 0x28, 0xf9, 0xe0, 0xd4, 0xc3, 0xc6, 0x3a,
	0x14, 0x42, 0x13, 0xef, 0x73, 0xe5, 0x01, 0xa6, 0xee, 0xb8, 0x7e, 0xd7, 0xec, 0x14, 0x2f, 0xa0,
	0x59, 0x98, 0x7e, 0x09, 0x77, 0x70, 0x80, 0xad, 0xa2, 0x66, 0x6c, 0xc1, 0x7c, 0xb8, 0x73, 0x37,
	0x3c, 0x8c, 0x2e, 0x01, 0xda, 0x6a, 0x36, 0xdd, 0x9e, 0x13, 0x6c, 0x39, 0xd6, 0x9e, 0x49, 0xc8,
	0x7b, 0xae, 0x6f, 0x15, 0x35, 0x94, 0x83, 0xc9, 0xdb, 0xd8, 0x32, 0x3b, 0xc5, 0x0c, 0x65, 0x71,
	0x70, 0xb0, 0x1f, 0x98, 0xad, 0x56, 0x31, 0x6b, 0x7c, 0x00, 0xb3, 0x12, 0x8b, 0x48, 0xbd, 0x43,
	0x93, 0x1c, 0x32, 0x97, 0xe6, 0x64, 0xf5, 0x5e, 0x35, 0xc9, 0x21, 0xf5, 0x1b, 0xdb, 0xf2, 0xc0,
	0x76, 0x4c, 0xff, 0x94, 0x39, 0x36, 0x2f, 0xfc, 0x46, 0x17, 0xb6, 0x19, 0x3d, 0x16, 0x1d, 0xd9,
	0xd4, 0xe8, 0x30, 0xbe, 0xd4, 0x60, 0xfe, 0x1e, 0xc1, 0xbe, 0xac, 0xc2, 0x25, 0xc8, 0xf6, 0x62,
	0xf1, 0x44, 0x09, 0xc8, 0x80, 0x5c, 0x60, 0x77, 0x31, 0x09, 0xcc, 0xae, 0xc7, 0xa4, 0x4e, 0x08,
	0x7e, 0x11, 0x19, 0x3d, 0x07, 0x45, 0x21, 0x94, 0x38, 0xa6, 0x47, 0x0e, 0xdd, 0x80, 0x89, 0x9e,
	0xbd, 0x59, 0xd8, 0xa4, 0x6f, 0x68, 0x33, 0x14, 0x54, 0x9f, 0x0f, 0xb7, 0xed, 0x87, 0xbb, 0xd0,
	0xff, 0x40, 0x81, 0xdd, 0x2a, 0x3a, 0x36, 0xc1, 0x8e, 0x2d, 0x28, 0xc7, 0xa8, 0x7e, 0xf5, 0x3c,
	0xdd, 0x17, 0x9d, 0xbb, 0x02, 0x33, 0xae, 0x6f, 0x61, 0x9f, 0x5e, 0x72, 0x52, 0x76, 0x38, 0xa3,
	0xee, 0x5a, 0xc6, 0xff, 0x46, 0xde, 0xdc, 0xb5, 0x6e, 0xd9, 0x24, 0x40, 0x4f, 0xc0, 0x7c, 0xdf,
	0x30, 0x8d, 0x8e, 0x4d, 0x82, 0x92, 0xa6, 0x67, 0xd7, 0x0b, 0xf5, 0x82, 0x27, 0xef, 0x33, 0x5e,
	0x8c, 0x3c, 0xc3, 0x8e, 0x3d, 0x03, 0x79, 0x71, 0x2c, 0x3a, 0x93, 0xb8, 0xd6, 0xac, 0xd7, 0x3f,
	0x61, 0xec, 0x40, 0x5e, 0x64, 0x02, 0xc6, 0xe1, 0x59, 0xe9, 0x99, 0x4b, 0x2c, 0xe6, 0x38, 0x0b,
	0xb1, 0xb5, 0xff, 0xe0, 0x19, 0x93, 0x06, 0x14, 0x5e, 0xc1, 0x81, 0xe0, 0x8f, 0xdb, 0x03, 0xdd,
	0x53, 0x86, 0x49, 0xdb, 0xb1, 0xf0, 0xb7, 0x99, 0x6b, 0xa2, 0x17, 0xc6, 0x48, 0x74, 0x8d, 0x85,
	0xa4, 0x12, 0x06, 0x9c, 0x64, 0x34, 0x60, 0x45, 0xb2, 0x6e, 0x23, 0x0c, 0x5e, 0x11, 0xb9, 0x68,
	0x0d, 0xa6, 0x4d, 0x4e, 0x52, 0xc2, 0x51, 0x10, 0x91, 0x0e, 0x33, 0x5e, 0xb8, 0x57, 0xc9, 0x31,
	0x11, 0xd5, 0x78, 0x0b, 0x16, 0x64, 0x01, 0xec, 0x15, 0x50, 0xb7, 0x75, 0xe9, 0x8f, 0x78, 0xe6,
	0x9a, 0x66, 0xd4, 0x5d, 0x8b, 0x86, 0x2f, 0xdf, 0x90, 0xc8, 0x5e, 0x39, 0x46, 0x67, 0xd9, 0xf0,
	0x6d, 0x58, 0x94, 0x59, 0x87, 0xaf, 0x8a, 0x32, 0x27, 0xf4, 0x47, 0x82, 0x39, 0xa3, 0x72, 0xe6,
	0x7c, 0x43, 0x92, 0x39, 0xa3, 0x33, 0xe6, 0x01, 0x2c, 0x6d, 0x35, 0x03, 0xfb, 0xc4, 0x0e, 0x4e,
	0x43, 0x21, 0x3b, 0xae, 0xd3, 0xb2, 0xdb, 0xe3, 0xe5, 0xdd, 0xc8, 0xe4, 0x99, 0x84, 0xc9, 0xf9,
	0x1a, 0x6e, 0xb5, 0x4a, 0x59, 0x5d, 0x93, 0xd7, 0x70, 0xab, 0x65, 0x7c, 0x91, 0x81, 0x19, 0x21,
	0x96, 0x3e, 0x75, 0x33, 0xfc, 0x9d, 0xa8, 0x1f, 0x62, 0x61, 0x68, 0x8e, 0x7f, 0x14, 0xe0, 0x01,
	0x6e, 0xdb, 0x4e, 0x83, 0x3e, 0x51, 0xe6, 0xfd, 0xe8, 0xd1, 0x32, 0xfa, 0x81, 0xdd, 0xc5, 0xd4,
	0x5c, 0xd8, 0xb1, 0xf8, 0x96, 0x09, 0x69, 0xcb, 0x34, 0x76, 0x2c, 0xb6, 0x61, 0x78, 0x6a, 0x7f,
	0x07, 0x56, 0x23, 0x25, 0x85, 0x5d, 0x9a, 0xcc, 0x52, 0x3c, 0xca, 0xa7, 0x58, 0x94, 0xaf, 0xf0,
	0x28, 0x4f, 0xb5, 0x68, 0x7d, 0xd9, 0x4c, 0x23, 0xb3, 0xf8, 0xbf, 0x0b, 0x73, 0xe2, 0x4c, 0x7a,
	0x36, 0xbe, 0xe5, 0x36, 0x8f, 0x6c, 0xa7, 0x5d, 0xd4, 0xe8, 0x3f, 0x77, 0x3d, 0xec, 0xd0, 0x7f,
	0x32, 0x74, 0xd7, 0x4e, 0xc7, 0x25, 0xd8, 0x2a, 0x66, 0xe5, 0x9c, 0xdd, 0x34, 0x3e, 0xd7, 0x12,
	0x7e, 0x7d, 0x09, 0x07, 0xa6, 0xdd, 0x41, 0xd7, 0x60, 0x3a, 0xd4, 0x9f, 0x59, 0x3a, 0xf1, 0xb8,
	0xc5, 0x2a, 0xf5, 0x5f, 0xe0, 0x06, 0x66, 0x47, 0xf5, 0x2d, 0x23, 0x51, 0x5b, 0xf9, 0xb8, 0x6b,
	0xda, 0x8e, 0xf2, 0xd6, 0x42, 0x1a, 0x8b, 0x4c, 0xb7, 0x63, 0x35, 0xdc, 0x1e, 0x4d, 0x70, 0x9a,
	0x14, 0x99, 0x6e, 0xc7, 0xba, 0xdb, 0x0b, 0xd0, 0x06, 0xcc, 0x33, 0x3e, 0x0d, 0xaf, 0xe7, 0x37,
	0x0f, 0x4d, 0x82, 0x69, 0x56, 0xeb, 0xef, 0x9b, 0x63, 0x8b, 0x7b, 0x62, 0xcd, 0xf8, 0xae, 0x06,
	0x4b, 0x3b, 0x3d, 0xdf, 0xc7, 0x4e, 0x20, 0xee, 0x14, 0x5e, 0xe6, 0x49, 0x98, 0x11, 0x46, 0x0d,
	0x6f, 0x33, 0xa7, 0x7a, 0xa0, 0x1e, 0xad, 0xa3, 0x5a, 0x2c, 0xb5, 0x65, 0x86, 0x78, 0x8c, 0xb3,
	0x57, 0x13, 0xdd, 0x4f, 0x92, 0x26, 0xad, 0x63, 0xd2, 0xeb, 0x04, 0x63, 0x3f, 0x95, 0x81, 0xe6,
	0x34, 0x20, 0xd7, 0xb7, 0x84, 0x5a, 0xc4, 0x04, 0x79, 0xa4, 0x51, 0x8d, 0x8f, 0x35, 0xb8, 0x14,
	0xe9, 0x17, 0x1e, 0xab, 0xe3, 0x26, 0x4d, 0x6f, 0x63, 0x29, 0x78, 0x0d, 0xf2, 0x42, 0x5a, 0x83,
	0xe6, 0x5e, 0x59, 0xcf, 0x59, 0xb1, 0x72, 0x2f, 0x5e, 0x22, 0xb3, 0xa9, 0x25, 0xd2, 0xf8, 0x79,
	0x06, 0x8a, 0x51, 0x44, 0x8b, 0x2a, 0x76, 0x16, 0x6f, 0x6d, 0xc1, 0x52, 0xbc, 0xc6, 0xca, 0x6e,
	0x8b, 0x05, 0xed, 0x62, 0xac, 0xd0, 0xb2, 0x4a, 0x94, 0xf6, 0x64, 0x7d, 0xe6, 0x31, 0xce, 0x29,
	0x3b, 0x24, 0x00, 0xb8, 0x67, 0x13, 0x4f, 0x96, 0x93, 0x19, 0xf7, 0xd7, 0x00, 0x45, 0xe6, 0xa2,
	0xf7, 0x6e, 0x74, 0x6c, 0x87, 0x66, 0x16, 0xca, 0x73, 0x35, 0xc6, 0x53, 0xf1, 0x46, 0xbd, 0x28,
	0xce, 0xd1, 0xb4, 0x73, 0xcb, 0x76, 0xb0, 0xf1, 0xbd, 0x0c, 0x14, 0x77, 0x7c, 0x6c, 0x06, 0x38,
	0x92, 0x73, 0x1c, 0xe5, 0x3b, 0x6d, 0x54, 0x4f, 0x9b, 0x19, 0xaf, 0xa7, 0xcd, 0x0e, 0xea, 0x69,
	0xa3, 0xae, 0x75, 0x62, 0x9c, 0xae, 0x75, 0x72, 0xac, 0xae, 0x75, 0x6a, 0x44, 0xd7, 0x3a, 0x9d,
	0xd2, 0xb5, 0x1a, 0xcf, 0xc1, 0x42, 0xcc, 0x0a, 0xc4, 0x1b, 0x2b, 0x76, 0x8d, 0x8f, 0x32, 0x50,
	0xbc, 0xe7, 0x59, 0xaa, 0x01, 0xcf, 0x38, 0x39, 0x68, 0x31, 0x2b, 0x27, 0xcc, 0xa7, 0x8d, 0x34,
	0x9f, 0x36, 0x8e, 0xf9, 0xb4, 0xb1, 0xcc, 0xa7, 0x8d, 0x30, 0x9f, 0x96, 0x34, 0xdf, 0x47, 0x1a,
	0x94, 0xfa, 0x5d, 0x14, 0xd9, 0x3e, 0x8d, 0x9a, 0x2d, 0x7c, 0x3c, 0xee, 0x90, 0xf6, 0x28, 0x40,
	0xcb, 0x77, 0xbb, 0x8d, 0x64, 0x93, 0x95, 0xa3, 0xf4, 0xdd, 0x91, 0x8d, 0xd6, 0x4f, 0x35, 0xb8,
	0xba, 0x65, 0x59, 0x72, 0x2b, 0x8b, 0x8f, 0x13, 0xfd, 0xd6, 0x58, 0xae, 0xb9, 0x07, 0x4b, 0x61,
	0xff, 0xd5, 0x10, 0x6d, 0x96, 0x9c, 0x02, 0xae, 0x26, 0x9a, 0xe6, 0xb8, 0x98, 0xfa, 0xa2, 0xa9,
	0x12, 0x58, 0x1e, 0xff, 0x87, 0x06, 0x46, 0x5c, 0x43, 0xe2, 0x25, 0x54, 0xb4, 0xe0, 0x11, 0xd2,
	0x6b, 0x36, 0x31, 0x21, 0x8d, 0x74, 0x2d, 0xb4, 0x71, 0xb5, 0x28, 0x87, 0x7c, 0xb6, 0x92, 0xca,
	0xa0, 0x07, 0xb0, 0xda, 0x32, 0xed, 0x0e, 0xb6, 0x1a, 0xe7, 0xbc, 0xea, 0x32, 0x67, 0x93, 0x22,
	0xc3, 0xa8, 0x03, 0xd4, 0x59, 0x61, 0xde, 0x75, 0x5a, 0xee, 0x78, 0xa6, 0xa7, 0xb1, 0x46, 0xa5,
	0x38, 0xbd, 0xae, 0x12, 0x04, 0xd3, 0x94, 0x7a, 0xa7, 0xd7, 0x35, 0x7c, 0xb8, 0x28, 0x85, 0x1a,
	0x67, 0x4f, 0xc3, 0x6c, 0xcc, 0xb1, 0x03, 0xdd, 0x04, 0x74, 0xdc, 0xc3, 0xfe, 0x69, 0xa3, 0xe5,
	0xfa, 0x8d, 0xa8, 0x28, 0x64, 0xa4, 0xb0, 0x2f, 0xb2, 0xf5, 0x97, 0x5d, 0x5f, 0x64, 0x51, 0x63,
	0x1f, 0x96, 0x52, 0x64, 0x12, 0x0f, 0x55, 0xa0, 0xc8, 0x3b, 0x8f, 0x86, 0xed, 0xb4, 0x5c, 0xd9,
	0x3b, 0x45, 0x6e, 0xb8, 0xfe, 0xf5, 0xeb, 0x73, 0x7e, 0xf4, 0x9b, 0x19, 0xe7, 0x15, 0x58, 0x93,
	0x27, 0x0f, 0xba, 0xf6, 0x50, 0x2f, 0xc7, 0x78, 0x11, 0xae, 0x0c, 0x65, 0x44, 0x3c, 0xa9, 0x6b,
	0xd2, 0x92, 0x5d, 0x13, 0xcd, 0x7e, 0x7d, 0x06, 0xe1, 0xc3, 0x19, 0x2f, 0xfb, 0xed, 0x03, 0x8a,
	0x9f, 0x24, 0x1e, 0x7a, 0x01, 0x16, 0xa2, 0xa3, 0xd4, 0x97, 0x92, 0x59, 0x52, 0xe6, 0x4d, 0xe1,
	0x36, 0xfa, 0x0f, 0x33, 0xcc, 0x9b, 0x70, 0x91, 0xb7, 0x93, 0x0f, 0xa1, 0x11, 0x5a, 0x81, 0x1c,
	0x9d, 0xed, 0xfb, 0x31, 0x9c, 0xab, 0xcf, 0x50, 0x02, 0xe3, 0xfc, 0x57, 0x4d, 0xe4, 0xf9, 0xa8,
	0xee, 0x0f, 0x2d, 0x77, 0x6a, 0x7b, 0x9f, 0x19, 0xdd, 0xde, 0x67, 0xd3, 0xda, 0xfb, 0x51, 0x0d,
	0xfc, 0xc4, 0xb9, 0x1a, 0xf8, 0x16, 0xa0, 0xf8, 0x95, 0x88, 0x97, 0x9c, 0x6c, 0xb4, 0xd4, 0xc9,
	0xe6, 0x3a, 0x14, 0xb0, 0xef, 0xbb, 0x7e, 0xa3, 0x8b, 0x09, 0x31, 0xdb, 0x6a, 0x31, 0xca, 0xb3,
	0xa5, 0xdb, 0x7c, 0xc5, 0xf8, 0x97, 0x06, 0x0b, 0xbc, 0xd0, 0xc9, 0xb6, 0xfb, 0x0f, 0x99, 0xa0,
	0x46, 0x99, 0x78, 0xf2, 0x5c, 0x26, 0x7e, 0x11, 0x50, 0xfc, 0xe6, 0xc4, 0x4b, 0xda, 0x4e, 0x1b,
	0x68, 0xbb, 0x0a, 0x2c, 0xf0, 0x88, 0x3e, 0xbb, 0xe9, 0x8c, 0xe7, 0xa1, 0xc8, 0x06, 0xad, 0x87,
	0x38, 0xfa, 0x3b, 0x8d, 0x3d, 0x4f, 0x71, 0x92, 0xde, 0x85, 0x9e, 0x5e, 0x83, 0x69, 0x97, 0x35,
	0x10, 0x44, 0x9d, 0xde, 0x43, 0xe2, 0xb9, 0x2b, 0x31, 0x15, 0xe0, 0xe3, 0x13, 0xec, 0x13, 0xee,
	0xad, 0x19, 0x21, 0x20, 0x24, 0x1a, 0xcf, 0xd0, 0x41, 0x92, 0xcb, 0xca, 0xc3, 0xcc, 0x1d, 0x37,
	0xd8, 0xa6, 0x9e, 0x1e, 0x3c, 0x61, 0x4e, 0x18, 0xaf, 0xc1, 0x62, 0xe2, 0x22, 0xc4, 0x43, 0xcf,
	0x42, 0x21, 0xb2, 0x43, 0x12, 0xf1, 0x89, 0x2c, 0x96, 0x37, 0xa5, 0x83, 0x34, 0x67, 0xa9, 0x45,
	0x98, 0x50, 0xa3, 0x9c, 0x33, 0x67, 0xfd, 0x52, 0x83, 0xc5, 0x04, 0x57, 0xe2, 0xa1, 0x5b, 0xb0,
	0x2c, 0x6a, 0xf9, 0x19, 0xd8, 0x5f, 0x0a, 0xcf, 0xec, 0xa9, 0x52, 0xd0, 0x6b, 0x50, 0x0a, 0x6b,
	0x76, 0x92, 0x59, 0x66, 0x10, 0xb3, 0x25, 0x7e, 0x24, 0xc6, 0xcb, 0xf8, 0xb5, 0x06, 0x6b, 0x62,
	0x3c, 0x88, 0xbd, 0x08, 0x91, 0x70, 0x07, 0x41, 0x61, 0xb1, 0xf0, 0xcb, 0x0c, 0x78, 0xf4, 0xe3,
	0x20, 0xa4, 0xe8, 0x06, 0x2c, 0x44, 0xbc, 0x44, 0x4d, 0x53, 0xfa, 0xd9, 0xa2, 0x58, 0x16, 0x05,
	0xcd, 0xf8, 0xa7, 0x06, 0x57, 0x86, 0x6a, 0x4e, 0x3c, 0x54, 0x85, 0xb9, 0x68, 0x80, 0x65, 0x46,
	0x62, 0xaf, 0x73, 0xf6, 0xe6, 0x12, 0xb7, 0x4f, 0x0c, 0x93, 0xad, 0x17, 0xa2, 0xcd, 0x0c, 0xa2,
	0xbd, 0x0f, 0xcb, 0x21, 0xc1, 0x76, 0xda, 0x91, 0xad, 0x2d, 0x36, 0x9a, 0xb3, 0x14, 0x39, 0x62,
	0x7a, 0xbf, 0xdc, 0x3f, 0xad, 0x42, 0x20, 0xcf, 0x43, 0xb1, 0xc9, 0xe1, 0x84, 0x7e, 0xeb, 0x91,
	0x65, 0xfc, 0xe2, 0x31, 0x3b, 0xdf, 0x54, 0x61, 0x07, 0xc3, 0x62, 0x6f, 0x59, 0x52, 0x9c, 0x0c,
	0x73, 0xd1, 0xc3, 0xa2, 0x95, 0x75, 0xf6, 0xd0, 0x54, 0x29, 0xc4, 0x43, 0xff, 0x07, 0x73, 0x3d,
	0x82, 0xfd, 0x44, 0xec, 0x0e, 0x30, 0x67, 0x9e, 0x6e, 0x8e, 0x22, 0xed, 0x18, 0x96, 0xf6, 0x71,
	0x10, 0x83, 0x51, 0xce, 0x50, 0x3c, 0x52, 0x43, 0x24, 0x33, 0x34, 0x44, 0x5e, 0x85, 0xc5, 0xad,
	0x18, 0x8d, 0x0a, 0x4c, 0xe5, 0xa4, 0x0d, 0xe5, 0x74, 0x1b, 0xd0, 0xcb, 0x18, 0x5b, 0x0f, 0xcc,
	0xe6, 0xd1, 0x4b, 0xb6, 0xd9, 0x75, 0x1d, 0x6b, 0x98, 0xd9, 0x65, 0xb4, 0x3c, 0x93, 0x86, 0x96,
	0x63, 0x58, 0x4c, 0xb0, 0xe3, 0xe5, 0xba, 0x8d, 0x83, 0x86, 0xc5, 0x49, 0xaa, 0x25, 0xda, 0x38,
	0x08, 0xb7, 0xa2, 0x6b, 0xc0, 0x2c, 0x1b, 0xed, 0x53, 0x80, 0x12, 0xba, 0x12, 0x6e, 0x34, 0xee,
	0xc3, 0xaa, 0xea, 0x46, 0xea, 0x8c, 0xed, 0xd3, 0xbb, 0x54, 0x8b, 0x73, 0xe9, 0xbf, 0x2b, 0xf7,
	0x9a, 0x07, 0x32, 0x58, 0x46, 0xeb, 0x30, 0x39, 0x43, 0x23, 0x6e, 0xbc, 0x0f, 0xcb, 0x03, 0xf9,
	0x8c, 0xd7, 0xeb, 0x3d, 0x07, 0x4b, 0x31, 0x30, 0x8f, 0xb5, 0x08, 0x44, 0xb1, 0xcb, 0xa2, 0x0a,
	0xe9, 0x31, 0xf6, 0xc6, 0x87, 0x1a, 0xe8, 0xc3, 0xef, 0x41, 0x3c, 0xf4, 0x0e, 0x94, 0x85, 0x0e,
	0x71, 0xbc, 0x25, 0x7a, 0x00, 0x57, 0x94, 0x7c, 0x9b, 0xc2, 0xe8, 0x72, 0xc8, 0x62, 0x4f, 0x41,
	0x5e, 0x48, 0x60, 0xd4, 0xd8, 0x4c, 0x21, 0xa2, 0xf4, 0xbe, 0xed, 0xd0, 0xb2, 0x77, 0x86, 0xe2,
	0xfe, 0x89, 0x04, 0x0a, 0x86, 0xa7, 0x23, 0x9c, 0x35, 0x82, 0x1b, 0x13, 0x7d, 0xad, 0xc0, 0x15,
	0xef, 0xd0, 0xde, 0x2b, 0x8c, 0x82, 0x4c, 0x3c, 0x0a, 0xa4, 0x6f, 0x12, 0xd9, 0xb4, 0x6f, 0x12,
	0x97, 0x20, 0xeb, 0xbd, 0x67, 0x29, 0x9f, 0x2a, 0x29, 0xc1, 0xf8, 0x16, 0x8b, 0xba, 0x54, 0xa5,
	0x98, 0x41, 0x6b, 0x90, 0x7f, 0x8f, 0x13, 0x65, 0x13, 0xc6, 0x32, 0xa9, 0x7a, 0x6c, 0x36, 0x3c,
	0xc0, 0x4c, 0xf6, 0x36, 0x5c, 0x8c, 0xed, 0xda, 0x61, 0xfa, 0x8c, 0x7d, 0xe1, 0x21, 0x1f, 0x0d,
	0x8c, 0x37, 0x61, 0x25, 0xa9, 0x3c, 0xe3, 0xcf, 0x74, 0x7f, 0x1e, 0x80, 0x4f, 0xc9, 0x92, 0xe6,
	0xe5, 0x54, 0xcd, 0xf9, 0x99, 0x1c, 0xdb, 0x4d, 0xd5, 0x7e, 0xf2, 0x15, 0x40, 0x3c, 0xf9, 0x35,
	0x4f, 0xb7, 0xed, 0xf7, 0x6f, 0xdb, 0x0e, 0xfb, 0x90, 0x59, 0x84, 0xfc, 0xb6, 0xfd, 0x7e, 0x43,
	0xc4, 0x45, 0x71, 0x56, 0x50, 0x44, 0x7e, 0x28, 0xe6, 0x51, 0x01, 0x72, 0x94, 0xb2, 0x7f, 0x68,
	0xfa, 0xb8, 0x58, 0xb8, 0xf9, 0x8b, 0x55, 0x60, 0x5f, 0xca, 0x51, 0x9d, 0x7d, 0xb4, 0x0a, 0x73,
	0x94, 0x8d, 0x09, 0x5a, 0xdd, 0x8c, 0x3e, 0x9a, 0x6f, 0xee, 0xbf, 0xbe, 0xcd, 0x3f, 0x9a, 0xff,
	0x7f, 0xd7, 0x0b, 0x4e, 0x1b, 0x7b, 0xdb, 0x65, 0xa4, 0x7e, 0x01, 0x63, 0xb6, 0x9c, 0xff, 0xf0,
	0xd3, 0xaf, 0xb3, 0xda, 0xc7, 0x9f, 0x7e, 0x9d, 0xbd, 0xf0, 0x23, 0xfa, 0x07, 0xfd, 0x4a, 0x83,
	0x82, 0x82, 0x81, 0xa1, 0x4b, 0xe1, 0xb1, 0x18, 0x3c, 0x58, 0xbe, 0x9c, 0x4a, 0x27, 0x9e, 0x11,
	0x50, 0x9e, 0x19, 0xca, 0x73, 0xce, 0xa9, 0x1c, 0x55, 0xac, 0x8a, 0x57, 0xe9, 0x54, 0xda, 0x15,
	0xb7, 0x42, 0x25, 0xdc, 0xdf, 0x70, 0xf4, 0x2a, 0x75, 0x4f, 0x4d, 0xdf, 0x38, 0xd2, 0xab, 0x22,
	0xfd, 0xd6, 0xf4, 0x0d, 0x4b, 0xaf, 0x5a, 0x98, 0x34, 0x6b, 0xfa, 0x86, 0xa7, 0x57, 0x19, 0x7a,
	0x55, 0xd3, 0x37, 0x3a, 0x7a, 0x55, 0xc5, 0xaf, 0x6a, 0xfa, 0x46, 0x5b, 0xaf, 0x86, 0x30, 0x55,
	0x4d, 0xdf, 0x70, 0xf5, 0xaa, 0xdd, 0x74, 0x9d, 0x1a, 0xfa, 0x8d, 0x06, 0x05, 0x05, 0x81, 0x13,
	0x8a, 0xc7, 0x61, 0xb9, 0xf2, 0x50, 0x2b, 0x19, 0x27, 0x54, 0xfb, 0x2c, 0xd3, 0xde, 0xae, 0x38,
	0x31, 0xed, 0xdf, 0xda, 0xb0, 0xa9, 0x6e, 0x22, 0x23, 0xd5, 0x74, 0xe9, 0x36, 0xe7, 0xd4, 0xff,
	0x5d, 0x98, 0x53, 0xc6, 0x5d, 0x82, 0x16, 0xd5, 0x26, 0x8e, 0xa5, 0xcb, 0x11, 0xca, 0x3f, 0x4e,
	0x95, 0x9f, 0xa0, 0xca, 0x67, 0x6c, 0xa6, 0xf0, 0x45, 0x55, 0x61, 0x16, 0xb6, 0x35, 0x84, 0xe5,
	0x49, 0x9f, 0x6c, 0x9f, 0xee, 0x99, 0x6d, 0x2c, 0xc4, 0x29, 0x9f, 0x41, 0xcb, 0x6a, 0x23, 0xc9,
	0x42, 0x66, 0x9d, 0xca, 0x98, 0xa4, 0x32, 0x26, 0xa8, 0x81, 0xa8, 0x94, 0x25, 0x2a, 0x85, 0x75,
	0x15, 0xdc, 0x22, 0x2c, 0xe8, 0x6b, 0xe8, 0x07, 0x9a, 0x02, 0x98, 0xf4, 0xc1, 0x08, 0xb4, 0x16,
	0x97, 0xa5, 0x42, 0x1e, 0x69, 0x62, 0x5f, 0xa0, 0x62, 0xa7, 0xa8, 0xd8, 0xa9, 0xa3, 0x8a, 0x1f,
	0x0a, 0x5e, 0x8f, 0xc5, 0x90, 0xaf, 0x57, 0xfb, 0x33, 0x8b, 0xa2, 0xcb, 0x6f, 0x35, 0x58, 0x53,
	0x3b, 0xf3, 0x04, 0xe0, 0x76, 0x2d, 0x7c, 0xc7, 0xa3, 0xc0, 0xc3, 0xf2, 0x7a, 0xfa, 0xc6, 0x24,
	0x86, 0x67, 0xbc, 0x41, 0x95, 0x9e, 0x66, 0xb6, 0xf2, 0x2a, 0xdc, 0x23, 0x35, 0x1e, 0x1e, 0x52,
	0x08, 0xd9, 0x7a, 0x35, 0x4c, 0xa9, 0x37, 0x2a, 0x02, 0x7c, 0xbb, 0xf1, 0x54, 0x48, 0xba, 0x19,
	0x91, 0x6e, 0x3e, 0xb5, 0xb9, 0xb9, 0x59, 0x43, 0xdf, 0x54, 0x7c, 0xc7, 0x71, 0x1e, 0x54, 0x4e,
	0xd8, 0x33, 0x42, 0xc4, 0xca, 0x2b, 0x03, 0xd7, 0x88, 0xc7, 0xdf, 0xff, 0x8c, 0xf4, 0xfe, 0xbf,
	0xc1, 0x3a, 0xbf, 0xbe, 0x57, 0x42, 0xb8, 0x2c, 0x35, 0x16, 0x53, 0xbc, 0xb4, 0x42, 0xf9, 0xe5,
	0xa4, 0x00, 0x04, 0x16, 0x1a, 0xd6, 0x53, 0xb6, 0x55, 0x43, 0x3f, 0xd4, 0x58, 0x72, 0x1d, 0x04,
	0x51, 0xa1, 0xc7, 0x92, 0x11, 0x98, 0x84, 0xc3, 0xca, 0x8f, 0x8f, 0xb1, 0x8b, 0x78, 0xc6, 0x55,
	0xaa, 0x09, 0x48, 0x9a, 0x14, 0xa9, 0x26, 0x12, 0x90, 0x56, 0x43, 0x76, 0x1c, 0xb6, 0x62, 0x57,
	0xbd, 0x1c, 0xe7, 0x1f, 0x86, 0x41, 0xb9, 0x94, 0xbe, 0x40, 0x3c, 0x43, 0xa7, 0xb2, 0x66, 0x25,
	0x59, 0xf3, 0xb1, 0x3c, 0x81, 0x3c, 0x40, 0x09, 0x30, 0x8b, 0x08, 0xb7, 0xa5, 0xc1, 0x5c, 0x23,
	0x1e, 0x3a, 0x93, 0x98, 0x1f, 0x26, 0xf1, 0x3e, 0xcc, 0xc7, 0x26, 0x51, 0x54, 0x4a, 0x8b, 0x5b,
	0xda, 0xac, 0x95, 0x97, 0x07, 0xac, 0x88, 0x08, 0x29, 0x48, 0x11, 0xf2, 0x86, 0x6c, 0xb5, 0x7d,
	0x1c, 0xce, 0x4a, 0x03, 0xad, 0x96, 0x1c, 0x45, 0x39, 0xcb, 0x39, 0x89, 0xe5, 0x27, 0x1a, 0x6b,
	0x19, 0x06, 0xf7, 0x81, 0x09, 0x9f, 0xa7, 0xf6, 0x9c, 0xe5, 0x27, 0xc6, 0xd9, 0x26, 0x62, 0x63,
	0x9e, 0x2a, 0x90, 0xdd, 0x18, 0x60, 0xbe, 0x03, 0x98, 0x53, 0xf1, 0x34, 0xa4, 0xd4, 0x3b, 0x69,
	0x7e, 0x11, 0x71, 0x91, 0x84, 0xdf, 0xf8, 0x45, 0x2d, 0xe9, 0xa2, 0x07, 0x30, 0xa7, 0x42, 0x48,
	0x82, 0x6b, 0x02, 0x52, 0x13, 0x5c, 0x93, 0x88, 0x13, 0xe7, 0x8a, 0x25, 0xae, 0x51, 0xe9, 0x88,
	0x73, 0x4d, 0xa0, 0x4d, 0x23, 0xa2, 0x8a, 0xd9, 0xa5, 0xc5, 0xa2, 0xca, 0x0c, 0xdf, 0x8c, 0x49,
	0x93, 0x53, 0xd4, 0x86, 0xd6, 0xd0, 0x8f, 0x35, 0x98, 0x8f, 0x61, 0x30, 0xa8, 0xff, 0x30, 0x62,
	0x18, 0x93, 0x88, 0xab, 0x14, 0xd0, 0xc6, 0x78, 0x9d, 0xca, 0x6a, 0x53, 0x59, 0x33, 0x6e, 0x85,
	0x54, 0x9c, 0x8a, 0xcf, 0x24, 0xfe, 0x37, 0xad, 0x88, 0x21, 0x04, 0x55, 0xd3, 0x37, 0x88, 0x5e,
	0x25, 0x81, 0xe9, 0x07, 0x0d, 0x9a, 0xd6, 0xe5, 0x84, 0xce, 0x72, 0x7d, 0x88, 0x24, 0xd5, 0x50,
	0x8b, 0x7d, 0x78, 0x4a, 0xff, 0x44, 0x3f, 0xbc, 0x29, 0x0a, 0xb3, 0x63, 0xea, 0x51, 0x6e, 0xe9,
	0x43, 0xc9, 0xd2, 0x3f, 0xd3, 0x60, 0x65, 0x08, 0xe6, 0x20, 0x32, 0xd8, 0x70, 0x40, 0x45, 0x64,
	0xb0, 0x11, 0xe0, 0x85, 0x51, 0xa3, 0xd2, 0x6d, 0x56, 0xf1, 0x7a, 0x15, 0xb3, 0xe2, 0x31, 0xfb,
	0x5c, 0xdf, 0xe8, 0xe9, 0xd5, 0x1e, 0xab, 0x1b, 0x31, 0xd7, 0xe8, 0xb1, 0xc2, 0x82, 0x3e, 0xd2,
	0x00, 0x25, 0x27, 0x6e, 0x14, 0xde, 0x38, 0x75, 0x16, 0x1f, 0x11, 0x1f, 0x37, 0xa8, 0x46, 0xef,
	0xb2, 0x72, 0x66, 0x56, 0x8e, 0x98, 0x3e, 0x6b, 0x49, 0x35, 0xe4, 0x92, 0x8c, 0xf8, 0xa7, 0x81,
	0xb8, 0x0e, 0xc3, 0x7d, 0x12, 0x03, 0x41, 0xb8, 0x1b, 0x8e, 0x24, 0x37, 0x1c, 0x42, 0x41, 0x01,
	0x43, 0xa3, 0x1e, 0x35, 0x86, 0x90, 0x8e, 0x13, 0xee, 0x9d, 0xa1, 0xe1, 0xfe, 0x01, 0x18, 0x83,
	0x02, 0x4b, 0x2a, 0x5c, 0xcb, 0xaa, 0xc2, 0x72, 0xb5, 0x1a, 0x1a, 0x5f, 0x6b, 0x54, 0x81, 0x2e,
	0x53, 0x80, 0x5b, 0xb3, 0xa0, 0x1a, 0xcf, 0x66, 0x6f, 0x4d, 0x86, 0x61, 0xa4, 0xb7, 0x16, 0xc3,
	0x80, 0xa4, 0xb7, 0x16, 0xc7, 0x6d, 0xf8, 0x45, 0x7f, 0xaf, 0xc5, 0xcb, 0x45, 0xff, 0xe3, 0x51,
	0x0d, 0x1d, 0xc3, 0x7c, 0x0c, 0x91, 0x10, 0xa2, 0x92, 0xb8, 0x87, 0x10, 0x95, 0x02, 0x61, 0x18,
	0xd7, 0xa9, 0xa8, 0xcf, 0x99, 0xa8, 0x89, 0x5e, 0xd8, 0x35, 0x2f, 0xf5, 0x63, 0xd6, 0xad, 0x0a,
	0x60, 0xa1, 0x86, 0xbe, 0xa3, 0xc1, 0xf2, 0x40, 0x78, 0x02, 0x19, 0x69, 0xd7, 0x51, 0xf1, 0x8b,
	0x72, 0x3a, 0xee, 0xc4, 0x75, 0xf8, 0xc3, 0x58, 0x3a, 0x74, 0x59, 0xe2, 0x48, 0x1f, 0xa0, 0x57,
	0x12, 0xc9, 0xab, 0x3f, 0x9e, 0x97, 0x8d, 0x41, 0x8b, 0xfd, 0x41, 0xd7, 0x28, 0x52, 0x3d, 0xfe,
	0x28, 0x4f, 0x57, 0x47, 0x70, 0x79, 0xc0, 0x74, 0x39, 0x5c, 0xda, 0xd5, 0x41, 0x8b, 0xd1, 0x64,
	0xca, 0x85, 0xfd, 0x49, 0x12, 0x56, 0x9e, 0xfa, 0xfe, 0xa7, 0x5f, 0x67, 0xff, 0x76, 0xb2, 0x5d,
	0xfc, 0xec, 0xab, 0x35, 0xed, 0x2f, 0x5f, 0xad, 0x69, 0x5f, 0x7e, 0xb5, 0xa6, 0x7d, 0xf2, 0xf7,
	0xb5, 0x0b, 0xff, 0x0e, 0x00, 0x00, 0xff, 0xff, 0x29, 0x6b, 0x29, 0xa1, 0x83, 0x2d, 0x00, 0x00,
}
