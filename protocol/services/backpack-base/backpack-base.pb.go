// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/backpack-base/backpack-base.proto

package backpack_base // import "golang.52tt.com/protocol/services/backpack-base"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type LogType int32

const (
	LogType_LOG_TYPE_INVALID                     LogType = 0
	LogType_LOG_TYPE_USE                         LogType = 1
	LogType_LOG_TYPE_EXPIRE                      LogType = 2
	LogType_LOG_TYPE_FRAGMENT_EXCHANGE           LogType = 3
	LogType_LOG_TYPE_FRAGMENT_ROLLBACK           LogType = 4
	LogType_LOG_TYPE_ITEM_CONVERSION             LogType = 5
	LogType_LOG_TYPE_ROLLBACK                    LogType = 6
	LogType_LOG_TYPE_OPRATE_DEDUCT               LogType = 7
	LogType_LOG_TYPE_BUSINESS_DEDUCT             LogType = 8
	LogType_LOG_TYPE_ENERGY_ITEM_CONVERSION      LogType = 9
	LogType_LOG_TYPE_ENERGY_ITEM_STAR_TREK_USE   LogType = 10
	LogType_LOG_TYPE_CHANNEL_LOTTERY             LogType = 11
	LogType_LOG_TYPE_ENERGY_ITEM_CAT_CANTEEN_USE LogType = 12
	LogType_LOG_TYPE_GLORY_WORLD_USE             LogType = 13
	LogType_LOG_TYPE_GLORY_WORLD_LOTTERY_USE     LogType = 14
	LogType_LOG_TYPE_KNIGHT_CARD_USE             LogType = 15
	LogType_LOG_TYPE_GLORY_WORLD_MAGIC_USE       LogType = 16
	LogType_LOG_TYPE_DRIFT_BOTTLE_USE            LogType = 17
)

var LogType_name = map[int32]string{
	0:  "LOG_TYPE_INVALID",
	1:  "LOG_TYPE_USE",
	2:  "LOG_TYPE_EXPIRE",
	3:  "LOG_TYPE_FRAGMENT_EXCHANGE",
	4:  "LOG_TYPE_FRAGMENT_ROLLBACK",
	5:  "LOG_TYPE_ITEM_CONVERSION",
	6:  "LOG_TYPE_ROLLBACK",
	7:  "LOG_TYPE_OPRATE_DEDUCT",
	8:  "LOG_TYPE_BUSINESS_DEDUCT",
	9:  "LOG_TYPE_ENERGY_ITEM_CONVERSION",
	10: "LOG_TYPE_ENERGY_ITEM_STAR_TREK_USE",
	11: "LOG_TYPE_CHANNEL_LOTTERY",
	12: "LOG_TYPE_ENERGY_ITEM_CAT_CANTEEN_USE",
	13: "LOG_TYPE_GLORY_WORLD_USE",
	14: "LOG_TYPE_GLORY_WORLD_LOTTERY_USE",
	15: "LOG_TYPE_KNIGHT_CARD_USE",
	16: "LOG_TYPE_GLORY_WORLD_MAGIC_USE",
	17: "LOG_TYPE_DRIFT_BOTTLE_USE",
}
var LogType_value = map[string]int32{
	"LOG_TYPE_INVALID":                     0,
	"LOG_TYPE_USE":                         1,
	"LOG_TYPE_EXPIRE":                      2,
	"LOG_TYPE_FRAGMENT_EXCHANGE":           3,
	"LOG_TYPE_FRAGMENT_ROLLBACK":           4,
	"LOG_TYPE_ITEM_CONVERSION":             5,
	"LOG_TYPE_ROLLBACK":                    6,
	"LOG_TYPE_OPRATE_DEDUCT":               7,
	"LOG_TYPE_BUSINESS_DEDUCT":             8,
	"LOG_TYPE_ENERGY_ITEM_CONVERSION":      9,
	"LOG_TYPE_ENERGY_ITEM_STAR_TREK_USE":   10,
	"LOG_TYPE_CHANNEL_LOTTERY":             11,
	"LOG_TYPE_ENERGY_ITEM_CAT_CANTEEN_USE": 12,
	"LOG_TYPE_GLORY_WORLD_USE":             13,
	"LOG_TYPE_GLORY_WORLD_LOTTERY_USE":     14,
	"LOG_TYPE_KNIGHT_CARD_USE":             15,
	"LOG_TYPE_GLORY_WORLD_MAGIC_USE":       16,
	"LOG_TYPE_DRIFT_BOTTLE_USE":            17,
}

func (x LogType) String() string {
	return proto.EnumName(LogType_name, int32(x))
}
func (LogType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{0}
}

type PackageItemType int32

const (
	PackageItemType_UNKNOW_ITEM_TYPE                PackageItemType = 0
	PackageItemType_BACKPACK_PRESENT                PackageItemType = 1
	PackageItemType_BACKPACK_CARD_RICH_ACCELERATOR  PackageItemType = 2
	PackageItemType_BACKPACK_CARD_CHARM_ACCELERATOR PackageItemType = 3
	PackageItemType_BACKPACK_LOTTERY_FRAGMENT       PackageItemType = 4
	PackageItemType_BACKPACK_CARD_RICH_INCR         PackageItemType = 5
	PackageItemType_BACKPACK_CARD_KNIGHT            PackageItemType = 6
)

var PackageItemType_name = map[int32]string{
	0: "UNKNOW_ITEM_TYPE",
	1: "BACKPACK_PRESENT",
	2: "BACKPACK_CARD_RICH_ACCELERATOR",
	3: "BACKPACK_CARD_CHARM_ACCELERATOR",
	4: "BACKPACK_LOTTERY_FRAGMENT",
	5: "BACKPACK_CARD_RICH_INCR",
	6: "BACKPACK_CARD_KNIGHT",
}
var PackageItemType_value = map[string]int32{
	"UNKNOW_ITEM_TYPE":                0,
	"BACKPACK_PRESENT":                1,
	"BACKPACK_CARD_RICH_ACCELERATOR":  2,
	"BACKPACK_CARD_CHARM_ACCELERATOR": 3,
	"BACKPACK_LOTTERY_FRAGMENT":       4,
	"BACKPACK_CARD_RICH_INCR":         5,
	"BACKPACK_CARD_KNIGHT":            6,
}

func (x PackageItemType) String() string {
	return proto.EnumName(PackageItemType_name, int32(x))
}
func (PackageItemType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{1}
}

// 包裹来源
type PackageSourceType int32

const (
	PackageSourceType_UNKNOW_PACKAGE_SOURCE                       PackageSourceType = 0
	PackageSourceType_PACKAGE_SOURCE_ACTIVITY_PRESENT             PackageSourceType = 1
	PackageSourceType_PACKAGE_SOURCE_DAILY_CHECKIN                PackageSourceType = 2
	PackageSourceType_PACKAGE_SOURCE_FIRST_RECHARGE               PackageSourceType = 3
	PackageSourceType_PACKAGE_SOURCE_OFFICIAL                     PackageSourceType = 4
	PackageSourceType_PACKAGE_SOURCE_SMASHEGG                     PackageSourceType = 5
	PackageSourceType_PACKAGE_SOURCE_CONVERSION                   PackageSourceType = 6
	PackageSourceType_PACKAGE_SOURCE_AWARD_CENTER                 PackageSourceType = 7
	PackageSourceType_PACKAGE_SOURCE_AWARD_YUYIN_LIVE_MISSION     PackageSourceType = 8
	PackageSourceType_PACKAGE_SOURCE_INTERACTION_INTIMACY_MISSION PackageSourceType = 9
	PackageSourceType_PACKAGE_SOURCE_ITEM_CONVERSION              PackageSourceType = 10
	PackageSourceType_PACKAGE_SOURCE_NOBILITY                     PackageSourceType = 11
	PackageSourceType_PACKAGE_SOURCE_TBEAN_BUY                    PackageSourceType = 12
	PackageSourceType_PACKAGE_SOURCE_CHANNEL_RED_PACKET           PackageSourceType = 13
	PackageSourceType_PACKAGE_SOURCE_SMASH_GOLD_EGG               PackageSourceType = 14
	PackageSourceType_PACKAGE_SOURCE_VIP_MILLION                  PackageSourceType = 15
	PackageSourceType_PACKAGE_SOURCE_ENERGY_STONE_CONVERSION      PackageSourceType = 16
	PackageSourceType_PACKAGE_SOURCE_DARK_GIFT_BONUS              PackageSourceType = 17
	PackageSourceType_PACKAGE_SOURCE_ONE_PIECE                    PackageSourceType = 18
	PackageSourceType_PACKAGE_SOURCE_STAR_TREK                    PackageSourceType = 19
	PackageSourceType_PACKAGE_SOURCE_YOU_KNOW_WHO                 PackageSourceType = 20
	PackageSourceType_PACKAGE_SOURCE_GAME_TICKET_RECHARGE         PackageSourceType = 21
	PackageSourceType_PACKAGE_SOURCE_CHANNEL_LOTTERY              PackageSourceType = 23
	PackageSourceType_PACKAGE_SOURCE_CAT_CANTEEN                  PackageSourceType = 24
	PackageSourceType_PACKAGE_SOURCE_GLORY_WORLD                  PackageSourceType = 25
	PackageSourceType_PACKAGE_SOURCE_GOLRY_STORE                  PackageSourceType = 26
	PackageSourceType_PACKAGE_SOURCE_GOLRY_MISSION                PackageSourceType = 27
	PackageSourceType_PACKAGE_SOURCE_USER_CALL                    PackageSourceType = 28
	PackageSourceType_PACKAGE_SOURCE_GLORY_WORLD_MAGIC            PackageSourceType = 29
	PackageSourceType_PACKAGE_SOURCE_PRESENT_WEEK_CARD            PackageSourceType = 30
	PackageSourceType_PACKAGE_SOURCE_PEARL_MILK_TEA               PackageSourceType = 31
	PackageSourceType_PACKAGE_SOURCE_DRIFT_BOTTLE                 PackageSourceType = 32
	PackageSourceType_PACKAGE_SOURCE_DRIFT_BOTTLE_CONVERSION      PackageSourceType = 33
	PackageSourceType_PACKAGE_SOURCE_OFFICIAL_GAME_WELFARE        PackageSourceType = 34
	PackageSourceType_PACKAGE_SOURCE_STAR_TRAIN                   PackageSourceType = 35
	PackageSourceType_PACKAGE_SOURCE_GAME_HELPER                  PackageSourceType = 36
	PackageSourceType_PACKAGE_SOURCE_PRESENT_SET                  PackageSourceType = 37
	PackageSourceType_PACKAGE_SOURCE_FELLOW_LEVEL_AWARD           PackageSourceType = 38
	PackageSourceType_PACKAGE_SOURCE_INVITE_USER_FROM_CHANNEL     PackageSourceType = 39
	PackageSourceType_PACKAGE_SOURCE_NEW_FIRST_RECHARGE           PackageSourceType = 40
	PackageSourceType_PACKAGE_SOURCE_EXT_GAME_ZDXX                PackageSourceType = 41
	PackageSourceType_PACKAGE_SOURCE_GRAB_CHAIR_GAME              PackageSourceType = 42
	PackageSourceType_PACKAGE_SOURCE_CHANNEL_WEDDING              PackageSourceType = 43
	PackageSourceType_PACKAGE_SOURCE_ACTIVITY_PRESENT_MONTH_CARD  PackageSourceType = 44
	PackageSourceType_PACKAGE_SOURCE_ADVENTURE_ACTIVITY           PackageSourceType = 45
)

var PackageSourceType_name = map[int32]string{
	0:  "UNKNOW_PACKAGE_SOURCE",
	1:  "PACKAGE_SOURCE_ACTIVITY_PRESENT",
	2:  "PACKAGE_SOURCE_DAILY_CHECKIN",
	3:  "PACKAGE_SOURCE_FIRST_RECHARGE",
	4:  "PACKAGE_SOURCE_OFFICIAL",
	5:  "PACKAGE_SOURCE_SMASHEGG",
	6:  "PACKAGE_SOURCE_CONVERSION",
	7:  "PACKAGE_SOURCE_AWARD_CENTER",
	8:  "PACKAGE_SOURCE_AWARD_YUYIN_LIVE_MISSION",
	9:  "PACKAGE_SOURCE_INTERACTION_INTIMACY_MISSION",
	10: "PACKAGE_SOURCE_ITEM_CONVERSION",
	11: "PACKAGE_SOURCE_NOBILITY",
	12: "PACKAGE_SOURCE_TBEAN_BUY",
	13: "PACKAGE_SOURCE_CHANNEL_RED_PACKET",
	14: "PACKAGE_SOURCE_SMASH_GOLD_EGG",
	15: "PACKAGE_SOURCE_VIP_MILLION",
	16: "PACKAGE_SOURCE_ENERGY_STONE_CONVERSION",
	17: "PACKAGE_SOURCE_DARK_GIFT_BONUS",
	18: "PACKAGE_SOURCE_ONE_PIECE",
	19: "PACKAGE_SOURCE_STAR_TREK",
	20: "PACKAGE_SOURCE_YOU_KNOW_WHO",
	21: "PACKAGE_SOURCE_GAME_TICKET_RECHARGE",
	23: "PACKAGE_SOURCE_CHANNEL_LOTTERY",
	24: "PACKAGE_SOURCE_CAT_CANTEEN",
	25: "PACKAGE_SOURCE_GLORY_WORLD",
	26: "PACKAGE_SOURCE_GOLRY_STORE",
	27: "PACKAGE_SOURCE_GOLRY_MISSION",
	28: "PACKAGE_SOURCE_USER_CALL",
	29: "PACKAGE_SOURCE_GLORY_WORLD_MAGIC",
	30: "PACKAGE_SOURCE_PRESENT_WEEK_CARD",
	31: "PACKAGE_SOURCE_PEARL_MILK_TEA",
	32: "PACKAGE_SOURCE_DRIFT_BOTTLE",
	33: "PACKAGE_SOURCE_DRIFT_BOTTLE_CONVERSION",
	34: "PACKAGE_SOURCE_OFFICIAL_GAME_WELFARE",
	35: "PACKAGE_SOURCE_STAR_TRAIN",
	36: "PACKAGE_SOURCE_GAME_HELPER",
	37: "PACKAGE_SOURCE_PRESENT_SET",
	38: "PACKAGE_SOURCE_FELLOW_LEVEL_AWARD",
	39: "PACKAGE_SOURCE_INVITE_USER_FROM_CHANNEL",
	40: "PACKAGE_SOURCE_NEW_FIRST_RECHARGE",
	41: "PACKAGE_SOURCE_EXT_GAME_ZDXX",
	42: "PACKAGE_SOURCE_GRAB_CHAIR_GAME",
	43: "PACKAGE_SOURCE_CHANNEL_WEDDING",
	44: "PACKAGE_SOURCE_ACTIVITY_PRESENT_MONTH_CARD",
	45: "PACKAGE_SOURCE_ADVENTURE_ACTIVITY",
}
var PackageSourceType_value = map[string]int32{
	"UNKNOW_PACKAGE_SOURCE":                       0,
	"PACKAGE_SOURCE_ACTIVITY_PRESENT":             1,
	"PACKAGE_SOURCE_DAILY_CHECKIN":                2,
	"PACKAGE_SOURCE_FIRST_RECHARGE":               3,
	"PACKAGE_SOURCE_OFFICIAL":                     4,
	"PACKAGE_SOURCE_SMASHEGG":                     5,
	"PACKAGE_SOURCE_CONVERSION":                   6,
	"PACKAGE_SOURCE_AWARD_CENTER":                 7,
	"PACKAGE_SOURCE_AWARD_YUYIN_LIVE_MISSION":     8,
	"PACKAGE_SOURCE_INTERACTION_INTIMACY_MISSION": 9,
	"PACKAGE_SOURCE_ITEM_CONVERSION":              10,
	"PACKAGE_SOURCE_NOBILITY":                     11,
	"PACKAGE_SOURCE_TBEAN_BUY":                    12,
	"PACKAGE_SOURCE_CHANNEL_RED_PACKET":           13,
	"PACKAGE_SOURCE_SMASH_GOLD_EGG":               14,
	"PACKAGE_SOURCE_VIP_MILLION":                  15,
	"PACKAGE_SOURCE_ENERGY_STONE_CONVERSION":      16,
	"PACKAGE_SOURCE_DARK_GIFT_BONUS":              17,
	"PACKAGE_SOURCE_ONE_PIECE":                    18,
	"PACKAGE_SOURCE_STAR_TREK":                    19,
	"PACKAGE_SOURCE_YOU_KNOW_WHO":                 20,
	"PACKAGE_SOURCE_GAME_TICKET_RECHARGE":         21,
	"PACKAGE_SOURCE_CHANNEL_LOTTERY":              23,
	"PACKAGE_SOURCE_CAT_CANTEEN":                  24,
	"PACKAGE_SOURCE_GLORY_WORLD":                  25,
	"PACKAGE_SOURCE_GOLRY_STORE":                  26,
	"PACKAGE_SOURCE_GOLRY_MISSION":                27,
	"PACKAGE_SOURCE_USER_CALL":                    28,
	"PACKAGE_SOURCE_GLORY_WORLD_MAGIC":            29,
	"PACKAGE_SOURCE_PRESENT_WEEK_CARD":            30,
	"PACKAGE_SOURCE_PEARL_MILK_TEA":               31,
	"PACKAGE_SOURCE_DRIFT_BOTTLE":                 32,
	"PACKAGE_SOURCE_DRIFT_BOTTLE_CONVERSION":      33,
	"PACKAGE_SOURCE_OFFICIAL_GAME_WELFARE":        34,
	"PACKAGE_SOURCE_STAR_TRAIN":                   35,
	"PACKAGE_SOURCE_GAME_HELPER":                  36,
	"PACKAGE_SOURCE_PRESENT_SET":                  37,
	"PACKAGE_SOURCE_FELLOW_LEVEL_AWARD":           38,
	"PACKAGE_SOURCE_INVITE_USER_FROM_CHANNEL":     39,
	"PACKAGE_SOURCE_NEW_FIRST_RECHARGE":           40,
	"PACKAGE_SOURCE_EXT_GAME_ZDXX":                41,
	"PACKAGE_SOURCE_GRAB_CHAIR_GAME":              42,
	"PACKAGE_SOURCE_CHANNEL_WEDDING":              43,
	"PACKAGE_SOURCE_ACTIVITY_PRESENT_MONTH_CARD":  44,
	"PACKAGE_SOURCE_ADVENTURE_ACTIVITY":           45,
}

func (x PackageSourceType) String() string {
	return proto.EnumName(PackageSourceType_name, int32(x))
}
func (PackageSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{2}
}

// T豆包裹类型
type PresentBusinessType int32

const (
	PresentBusinessType_E_PRESENT_BUSINESS_TYPE_DEFAULT          PresentBusinessType = 0
	PresentBusinessType_E_PRESENT_BUSINESS_TYPE_INTIMATE_PRESENT PresentBusinessType = 1
)

var PresentBusinessType_name = map[int32]string{
	0: "E_PRESENT_BUSINESS_TYPE_DEFAULT",
	1: "E_PRESENT_BUSINESS_TYPE_INTIMATE_PRESENT",
}
var PresentBusinessType_value = map[string]int32{
	"E_PRESENT_BUSINESS_TYPE_DEFAULT":          0,
	"E_PRESENT_BUSINESS_TYPE_INTIMATE_PRESENT": 1,
}

func (x PresentBusinessType) String() string {
	return proto.EnumName(PresentBusinessType_name, int32(x))
}
func (PresentBusinessType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{3}
}

type FREEZETYPE int32

const (
	FREEZETYPE_FREEZETYPE_UNVALID  FREEZETYPE = 0
	FREEZETYPE_FREEZETYPE_PREPARE  FREEZETYPE = 1
	FREEZETYPE_FREEZETYPE_COMMIT   FREEZETYPE = 2
	FREEZETYPE_FREEZETYPE_ROLLBACK FREEZETYPE = 3
)

var FREEZETYPE_name = map[int32]string{
	0: "FREEZETYPE_UNVALID",
	1: "FREEZETYPE_PREPARE",
	2: "FREEZETYPE_COMMIT",
	3: "FREEZETYPE_ROLLBACK",
}
var FREEZETYPE_value = map[string]int32{
	"FREEZETYPE_UNVALID":  0,
	"FREEZETYPE_PREPARE":  1,
	"FREEZETYPE_COMMIT":   2,
	"FREEZETYPE_ROLLBACK": 3,
}

func (x FREEZETYPE) String() string {
	return proto.EnumName(FREEZETYPE_name, int32(x))
}
func (FREEZETYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{4}
}

type DeductType int32

const (
	DeductType_UNKNOW_DEDUCT_TYPE DeductType = 0
	DeductType_OPRATE_DEDUCT      DeductType = 1
	DeductType_BUSINESS_DEDUCT    DeductType = 2
)

var DeductType_name = map[int32]string{
	0: "UNKNOW_DEDUCT_TYPE",
	1: "OPRATE_DEDUCT",
	2: "BUSINESS_DEDUCT",
}
var DeductType_value = map[string]int32{
	"UNKNOW_DEDUCT_TYPE": 0,
	"OPRATE_DEDUCT":      1,
	"BUSINESS_DEDUCT":    2,
}

func (x DeductType) String() string {
	return proto.EnumName(DeductType_name, int32(x))
}
func (DeductType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{5}
}

type DeductFailType int32

const (
	DeductFailType_DEDUCTFAILTYPE_UNVALID          DeductFailType = 0
	DeductFailType_DEDUCTFAILTYPE_UID_NOT_EXIST    DeductFailType = 1
	DeductFailType_DEDUCTFAILTYPE_ITEM_NOT_ENOUGH  DeductFailType = 2
	DeductFailType_DEDUCTFAILTYPE_DEDUCT_ITEM_FAIL DeductFailType = 3
)

var DeductFailType_name = map[int32]string{
	0: "DEDUCTFAILTYPE_UNVALID",
	1: "DEDUCTFAILTYPE_UID_NOT_EXIST",
	2: "DEDUCTFAILTYPE_ITEM_NOT_ENOUGH",
	3: "DEDUCTFAILTYPE_DEDUCT_ITEM_FAIL",
}
var DeductFailType_value = map[string]int32{
	"DEDUCTFAILTYPE_UNVALID":          0,
	"DEDUCTFAILTYPE_UID_NOT_EXIST":    1,
	"DEDUCTFAILTYPE_ITEM_NOT_ENOUGH":  2,
	"DEDUCTFAILTYPE_DEDUCT_ITEM_FAIL": 3,
}

func (x DeductFailType) String() string {
	return proto.EnumName(DeductFailType_name, int32(x))
}
func (DeductFailType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{6}
}

// 包裹配置
type PackageCfg struct {
	BgId                 uint32   `protobuf:"varint,1,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	IsDel                bool     `protobuf:"varint,4,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PackageCfg) Reset()         { *m = PackageCfg{} }
func (m *PackageCfg) String() string { return proto.CompactTextString(m) }
func (*PackageCfg) ProtoMessage()    {}
func (*PackageCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{0}
}
func (m *PackageCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PackageCfg.Unmarshal(m, b)
}
func (m *PackageCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PackageCfg.Marshal(b, m, deterministic)
}
func (dst *PackageCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PackageCfg.Merge(dst, src)
}
func (m *PackageCfg) XXX_Size() int {
	return xxx_messageInfo_PackageCfg.Size(m)
}
func (m *PackageCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_PackageCfg.DiscardUnknown(m)
}

var xxx_messageInfo_PackageCfg proto.InternalMessageInfo

func (m *PackageCfg) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *PackageCfg) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PackageCfg) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *PackageCfg) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

type PackageItemCfg struct {
	BgItemId             uint32   `protobuf:"varint,1,opt,name=bg_item_id,json=bgItemId,proto3" json:"bg_item_id,omitempty"`
	BgId                 uint32   `protobuf:"varint,2,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	ItemType             uint32   `protobuf:"varint,3,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	SourceId             uint32   `protobuf:"varint,4,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	ItemCount            uint32   `protobuf:"varint,5,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	FinTime              uint32   `protobuf:"varint,6,opt,name=fin_time,json=finTime,proto3" json:"fin_time,omitempty"`
	IsDel                bool     `protobuf:"varint,7,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	Weight               uint32   `protobuf:"varint,8,opt,name=weight,proto3" json:"weight,omitempty"`
	DynamicFinTime       uint32   `protobuf:"varint,9,opt,name=dynamic_fin_time,json=dynamicFinTime,proto3" json:"dynamic_fin_time,omitempty"`
	Months               uint32   `protobuf:"varint,10,opt,name=months,proto3" json:"months,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PackageItemCfg) Reset()         { *m = PackageItemCfg{} }
func (m *PackageItemCfg) String() string { return proto.CompactTextString(m) }
func (*PackageItemCfg) ProtoMessage()    {}
func (*PackageItemCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{1}
}
func (m *PackageItemCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PackageItemCfg.Unmarshal(m, b)
}
func (m *PackageItemCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PackageItemCfg.Marshal(b, m, deterministic)
}
func (dst *PackageItemCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PackageItemCfg.Merge(dst, src)
}
func (m *PackageItemCfg) XXX_Size() int {
	return xxx_messageInfo_PackageItemCfg.Size(m)
}
func (m *PackageItemCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_PackageItemCfg.DiscardUnknown(m)
}

var xxx_messageInfo_PackageItemCfg proto.InternalMessageInfo

func (m *PackageItemCfg) GetBgItemId() uint32 {
	if m != nil {
		return m.BgItemId
	}
	return 0
}

func (m *PackageItemCfg) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *PackageItemCfg) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *PackageItemCfg) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *PackageItemCfg) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *PackageItemCfg) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

func (m *PackageItemCfg) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

func (m *PackageItemCfg) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *PackageItemCfg) GetDynamicFinTime() uint32 {
	if m != nil {
		return m.DynamicFinTime
	}
	return 0
}

func (m *PackageItemCfg) GetMonths() uint32 {
	if m != nil {
		return m.Months
	}
	return 0
}

// 砸蛋得的抽奖碎片
type LotteryFragmentCfg struct {
	FragmentId           uint32   `protobuf:"varint,1,opt,name=fragment_id,json=fragmentId,proto3" json:"fragment_id,omitempty"`
	FragmentType         uint32   `protobuf:"varint,2,opt,name=fragment_type,json=fragmentType,proto3" json:"fragment_type,omitempty"`
	FragmentName         string   `protobuf:"bytes,3,opt,name=fragment_name,json=fragmentName,proto3" json:"fragment_name,omitempty"`
	FragmentDesc         string   `protobuf:"bytes,4,opt,name=fragment_desc,json=fragmentDesc,proto3" json:"fragment_desc,omitempty"`
	FragmentUrl          string   `protobuf:"bytes,5,opt,name=fragment_url,json=fragmentUrl,proto3" json:"fragment_url,omitempty"`
	IsDel                uint32   `protobuf:"varint,6,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	FragmentPrice        uint32   `protobuf:"varint,7,opt,name=fragment_price,json=fragmentPrice,proto3" json:"fragment_price,omitempty"`
	IsShowExpireHint     uint32   `protobuf:"varint,8,opt,name=is_show_expire_hint,json=isShowExpireHint,proto3" json:"is_show_expire_hint,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LotteryFragmentCfg) Reset()         { *m = LotteryFragmentCfg{} }
func (m *LotteryFragmentCfg) String() string { return proto.CompactTextString(m) }
func (*LotteryFragmentCfg) ProtoMessage()    {}
func (*LotteryFragmentCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{2}
}
func (m *LotteryFragmentCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryFragmentCfg.Unmarshal(m, b)
}
func (m *LotteryFragmentCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryFragmentCfg.Marshal(b, m, deterministic)
}
func (dst *LotteryFragmentCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryFragmentCfg.Merge(dst, src)
}
func (m *LotteryFragmentCfg) XXX_Size() int {
	return xxx_messageInfo_LotteryFragmentCfg.Size(m)
}
func (m *LotteryFragmentCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryFragmentCfg.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryFragmentCfg proto.InternalMessageInfo

func (m *LotteryFragmentCfg) GetFragmentId() uint32 {
	if m != nil {
		return m.FragmentId
	}
	return 0
}

func (m *LotteryFragmentCfg) GetFragmentType() uint32 {
	if m != nil {
		return m.FragmentType
	}
	return 0
}

func (m *LotteryFragmentCfg) GetFragmentName() string {
	if m != nil {
		return m.FragmentName
	}
	return ""
}

func (m *LotteryFragmentCfg) GetFragmentDesc() string {
	if m != nil {
		return m.FragmentDesc
	}
	return ""
}

func (m *LotteryFragmentCfg) GetFragmentUrl() string {
	if m != nil {
		return m.FragmentUrl
	}
	return ""
}

func (m *LotteryFragmentCfg) GetIsDel() uint32 {
	if m != nil {
		return m.IsDel
	}
	return 0
}

func (m *LotteryFragmentCfg) GetFragmentPrice() uint32 {
	if m != nil {
		return m.FragmentPrice
	}
	return 0
}

func (m *LotteryFragmentCfg) GetIsShowExpireHint() uint32 {
	if m != nil {
		return m.IsShowExpireHint
	}
	return 0
}

type UserBackpackItem struct {
	ItemType             uint32   `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	UserItemId           uint32   `protobuf:"varint,2,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
	ItemCount            uint32   `protobuf:"varint,3,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	FinTime              uint32   `protobuf:"varint,4,opt,name=fin_time,json=finTime,proto3" json:"fin_time,omitempty"`
	SourceId             uint32   `protobuf:"varint,5,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	Weight               uint32   `protobuf:"varint,6,opt,name=weight,proto3" json:"weight,omitempty"`
	ObtainTime           uint32   `protobuf:"varint,7,opt,name=obtain_time,json=obtainTime,proto3" json:"obtain_time,omitempty"`
	SourceType           uint32   `protobuf:"varint,8,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	FinalItemCount       uint32   `protobuf:"varint,9,opt,name=final_item_count,json=finalItemCount,proto3" json:"final_item_count,omitempty"`
	BusinessType         uint32   `protobuf:"varint,10,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserBackpackItem) Reset()         { *m = UserBackpackItem{} }
func (m *UserBackpackItem) String() string { return proto.CompactTextString(m) }
func (*UserBackpackItem) ProtoMessage()    {}
func (*UserBackpackItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{3}
}
func (m *UserBackpackItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBackpackItem.Unmarshal(m, b)
}
func (m *UserBackpackItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBackpackItem.Marshal(b, m, deterministic)
}
func (dst *UserBackpackItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBackpackItem.Merge(dst, src)
}
func (m *UserBackpackItem) XXX_Size() int {
	return xxx_messageInfo_UserBackpackItem.Size(m)
}
func (m *UserBackpackItem) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBackpackItem.DiscardUnknown(m)
}

var xxx_messageInfo_UserBackpackItem proto.InternalMessageInfo

func (m *UserBackpackItem) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *UserBackpackItem) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *UserBackpackItem) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *UserBackpackItem) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

func (m *UserBackpackItem) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *UserBackpackItem) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *UserBackpackItem) GetObtainTime() uint32 {
	if m != nil {
		return m.ObtainTime
	}
	return 0
}

func (m *UserBackpackItem) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *UserBackpackItem) GetFinalItemCount() uint32 {
	if m != nil {
		return m.FinalItemCount
	}
	return 0
}

func (m *UserBackpackItem) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

type UserBackpackLog struct {
	ItemType             uint32   `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemCount            uint32   `protobuf:"varint,2,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	SourceId             uint32   `protobuf:"varint,3,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	LogType              uint32   `protobuf:"varint,4,opt,name=log_type,json=logType,proto3" json:"log_type,omitempty"`
	LogTime              uint32   `protobuf:"varint,5,opt,name=log_time,json=logTime,proto3" json:"log_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserBackpackLog) Reset()         { *m = UserBackpackLog{} }
func (m *UserBackpackLog) String() string { return proto.CompactTextString(m) }
func (*UserBackpackLog) ProtoMessage()    {}
func (*UserBackpackLog) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{4}
}
func (m *UserBackpackLog) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBackpackLog.Unmarshal(m, b)
}
func (m *UserBackpackLog) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBackpackLog.Marshal(b, m, deterministic)
}
func (dst *UserBackpackLog) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBackpackLog.Merge(dst, src)
}
func (m *UserBackpackLog) XXX_Size() int {
	return xxx_messageInfo_UserBackpackLog.Size(m)
}
func (m *UserBackpackLog) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBackpackLog.DiscardUnknown(m)
}

var xxx_messageInfo_UserBackpackLog proto.InternalMessageInfo

func (m *UserBackpackLog) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *UserBackpackLog) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *UserBackpackLog) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *UserBackpackLog) GetLogType() uint32 {
	if m != nil {
		return m.LogType
	}
	return 0
}

func (m *UserBackpackLog) GetLogTime() uint32 {
	if m != nil {
		return m.LogTime
	}
	return 0
}

type AddPackageCfgReq struct {
	Cfg                  *PackageCfg `protobuf:"bytes,1,opt,name=cfg,proto3" json:"cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *AddPackageCfgReq) Reset()         { *m = AddPackageCfgReq{} }
func (m *AddPackageCfgReq) String() string { return proto.CompactTextString(m) }
func (*AddPackageCfgReq) ProtoMessage()    {}
func (*AddPackageCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{5}
}
func (m *AddPackageCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPackageCfgReq.Unmarshal(m, b)
}
func (m *AddPackageCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPackageCfgReq.Marshal(b, m, deterministic)
}
func (dst *AddPackageCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPackageCfgReq.Merge(dst, src)
}
func (m *AddPackageCfgReq) XXX_Size() int {
	return xxx_messageInfo_AddPackageCfgReq.Size(m)
}
func (m *AddPackageCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPackageCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPackageCfgReq proto.InternalMessageInfo

func (m *AddPackageCfgReq) GetCfg() *PackageCfg {
	if m != nil {
		return m.Cfg
	}
	return nil
}

type AddPackageCfgResp struct {
	Cfg                  *PackageCfg `protobuf:"bytes,1,opt,name=cfg,proto3" json:"cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *AddPackageCfgResp) Reset()         { *m = AddPackageCfgResp{} }
func (m *AddPackageCfgResp) String() string { return proto.CompactTextString(m) }
func (*AddPackageCfgResp) ProtoMessage()    {}
func (*AddPackageCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{6}
}
func (m *AddPackageCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPackageCfgResp.Unmarshal(m, b)
}
func (m *AddPackageCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPackageCfgResp.Marshal(b, m, deterministic)
}
func (dst *AddPackageCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPackageCfgResp.Merge(dst, src)
}
func (m *AddPackageCfgResp) XXX_Size() int {
	return xxx_messageInfo_AddPackageCfgResp.Size(m)
}
func (m *AddPackageCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPackageCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPackageCfgResp proto.InternalMessageInfo

func (m *AddPackageCfgResp) GetCfg() *PackageCfg {
	if m != nil {
		return m.Cfg
	}
	return nil
}

type AddPackageAndItemCfgReq struct {
	Cfg                  *PackageCfg       `protobuf:"bytes,1,opt,name=cfg,proto3" json:"cfg,omitempty"`
	ItemCfgList          []*PackageItemCfg `protobuf:"bytes,2,rep,name=item_cfg_list,json=itemCfgList,proto3" json:"item_cfg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AddPackageAndItemCfgReq) Reset()         { *m = AddPackageAndItemCfgReq{} }
func (m *AddPackageAndItemCfgReq) String() string { return proto.CompactTextString(m) }
func (*AddPackageAndItemCfgReq) ProtoMessage()    {}
func (*AddPackageAndItemCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{7}
}
func (m *AddPackageAndItemCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPackageAndItemCfgReq.Unmarshal(m, b)
}
func (m *AddPackageAndItemCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPackageAndItemCfgReq.Marshal(b, m, deterministic)
}
func (dst *AddPackageAndItemCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPackageAndItemCfgReq.Merge(dst, src)
}
func (m *AddPackageAndItemCfgReq) XXX_Size() int {
	return xxx_messageInfo_AddPackageAndItemCfgReq.Size(m)
}
func (m *AddPackageAndItemCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPackageAndItemCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPackageAndItemCfgReq proto.InternalMessageInfo

func (m *AddPackageAndItemCfgReq) GetCfg() *PackageCfg {
	if m != nil {
		return m.Cfg
	}
	return nil
}

func (m *AddPackageAndItemCfgReq) GetItemCfgList() []*PackageItemCfg {
	if m != nil {
		return m.ItemCfgList
	}
	return nil
}

type AddPackageAndItemCfgResp struct {
	Cfg                  *PackageCfg       `protobuf:"bytes,1,opt,name=cfg,proto3" json:"cfg,omitempty"`
	ItemCfgList          []*PackageItemCfg `protobuf:"bytes,2,rep,name=item_cfg_list,json=itemCfgList,proto3" json:"item_cfg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AddPackageAndItemCfgResp) Reset()         { *m = AddPackageAndItemCfgResp{} }
func (m *AddPackageAndItemCfgResp) String() string { return proto.CompactTextString(m) }
func (*AddPackageAndItemCfgResp) ProtoMessage()    {}
func (*AddPackageAndItemCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{8}
}
func (m *AddPackageAndItemCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPackageAndItemCfgResp.Unmarshal(m, b)
}
func (m *AddPackageAndItemCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPackageAndItemCfgResp.Marshal(b, m, deterministic)
}
func (dst *AddPackageAndItemCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPackageAndItemCfgResp.Merge(dst, src)
}
func (m *AddPackageAndItemCfgResp) XXX_Size() int {
	return xxx_messageInfo_AddPackageAndItemCfgResp.Size(m)
}
func (m *AddPackageAndItemCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPackageAndItemCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPackageAndItemCfgResp proto.InternalMessageInfo

func (m *AddPackageAndItemCfgResp) GetCfg() *PackageCfg {
	if m != nil {
		return m.Cfg
	}
	return nil
}

func (m *AddPackageAndItemCfgResp) GetItemCfgList() []*PackageItemCfg {
	if m != nil {
		return m.ItemCfgList
	}
	return nil
}

type DelPackageCfgReq struct {
	BgId                 uint32   `protobuf:"varint,1,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPackageCfgReq) Reset()         { *m = DelPackageCfgReq{} }
func (m *DelPackageCfgReq) String() string { return proto.CompactTextString(m) }
func (*DelPackageCfgReq) ProtoMessage()    {}
func (*DelPackageCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{9}
}
func (m *DelPackageCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPackageCfgReq.Unmarshal(m, b)
}
func (m *DelPackageCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPackageCfgReq.Marshal(b, m, deterministic)
}
func (dst *DelPackageCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPackageCfgReq.Merge(dst, src)
}
func (m *DelPackageCfgReq) XXX_Size() int {
	return xxx_messageInfo_DelPackageCfgReq.Size(m)
}
func (m *DelPackageCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPackageCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPackageCfgReq proto.InternalMessageInfo

func (m *DelPackageCfgReq) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

type DelPackageCfgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPackageCfgResp) Reset()         { *m = DelPackageCfgResp{} }
func (m *DelPackageCfgResp) String() string { return proto.CompactTextString(m) }
func (*DelPackageCfgResp) ProtoMessage()    {}
func (*DelPackageCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{10}
}
func (m *DelPackageCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPackageCfgResp.Unmarshal(m, b)
}
func (m *DelPackageCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPackageCfgResp.Marshal(b, m, deterministic)
}
func (dst *DelPackageCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPackageCfgResp.Merge(dst, src)
}
func (m *DelPackageCfgResp) XXX_Size() int {
	return xxx_messageInfo_DelPackageCfgResp.Size(m)
}
func (m *DelPackageCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPackageCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPackageCfgResp proto.InternalMessageInfo

type GetPackageCfgReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPackageCfgReq) Reset()         { *m = GetPackageCfgReq{} }
func (m *GetPackageCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetPackageCfgReq) ProtoMessage()    {}
func (*GetPackageCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{11}
}
func (m *GetPackageCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPackageCfgReq.Unmarshal(m, b)
}
func (m *GetPackageCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPackageCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetPackageCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPackageCfgReq.Merge(dst, src)
}
func (m *GetPackageCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetPackageCfgReq.Size(m)
}
func (m *GetPackageCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPackageCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPackageCfgReq proto.InternalMessageInfo

func (m *GetPackageCfgReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetPackageCfgReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetPackageCfgResp struct {
	CfgList              []*PackageCfg `protobuf:"bytes,1,rep,name=cfg_list,json=cfgList,proto3" json:"cfg_list,omitempty"`
	TotalCount           uint32        `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetPackageCfgResp) Reset()         { *m = GetPackageCfgResp{} }
func (m *GetPackageCfgResp) String() string { return proto.CompactTextString(m) }
func (*GetPackageCfgResp) ProtoMessage()    {}
func (*GetPackageCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{12}
}
func (m *GetPackageCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPackageCfgResp.Unmarshal(m, b)
}
func (m *GetPackageCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPackageCfgResp.Marshal(b, m, deterministic)
}
func (dst *GetPackageCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPackageCfgResp.Merge(dst, src)
}
func (m *GetPackageCfgResp) XXX_Size() int {
	return xxx_messageInfo_GetPackageCfgResp.Size(m)
}
func (m *GetPackageCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPackageCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPackageCfgResp proto.InternalMessageInfo

func (m *GetPackageCfgResp) GetCfgList() []*PackageCfg {
	if m != nil {
		return m.CfgList
	}
	return nil
}

func (m *GetPackageCfgResp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type AddPackageItemCfgReq struct {
	ItemCfg              *PackageItemCfg `protobuf:"bytes,1,opt,name=item_cfg,json=itemCfg,proto3" json:"item_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AddPackageItemCfgReq) Reset()         { *m = AddPackageItemCfgReq{} }
func (m *AddPackageItemCfgReq) String() string { return proto.CompactTextString(m) }
func (*AddPackageItemCfgReq) ProtoMessage()    {}
func (*AddPackageItemCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{13}
}
func (m *AddPackageItemCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPackageItemCfgReq.Unmarshal(m, b)
}
func (m *AddPackageItemCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPackageItemCfgReq.Marshal(b, m, deterministic)
}
func (dst *AddPackageItemCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPackageItemCfgReq.Merge(dst, src)
}
func (m *AddPackageItemCfgReq) XXX_Size() int {
	return xxx_messageInfo_AddPackageItemCfgReq.Size(m)
}
func (m *AddPackageItemCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPackageItemCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPackageItemCfgReq proto.InternalMessageInfo

func (m *AddPackageItemCfgReq) GetItemCfg() *PackageItemCfg {
	if m != nil {
		return m.ItemCfg
	}
	return nil
}

type AddPackageItemCfgResp struct {
	ItemCfg              *PackageItemCfg `protobuf:"bytes,1,opt,name=item_cfg,json=itemCfg,proto3" json:"item_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AddPackageItemCfgResp) Reset()         { *m = AddPackageItemCfgResp{} }
func (m *AddPackageItemCfgResp) String() string { return proto.CompactTextString(m) }
func (*AddPackageItemCfgResp) ProtoMessage()    {}
func (*AddPackageItemCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{14}
}
func (m *AddPackageItemCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPackageItemCfgResp.Unmarshal(m, b)
}
func (m *AddPackageItemCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPackageItemCfgResp.Marshal(b, m, deterministic)
}
func (dst *AddPackageItemCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPackageItemCfgResp.Merge(dst, src)
}
func (m *AddPackageItemCfgResp) XXX_Size() int {
	return xxx_messageInfo_AddPackageItemCfgResp.Size(m)
}
func (m *AddPackageItemCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPackageItemCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPackageItemCfgResp proto.InternalMessageInfo

func (m *AddPackageItemCfgResp) GetItemCfg() *PackageItemCfg {
	if m != nil {
		return m.ItemCfg
	}
	return nil
}

type ModPackageItemCfgReq struct {
	ItemCfg              *PackageItemCfg `protobuf:"bytes,1,opt,name=item_cfg,json=itemCfg,proto3" json:"item_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ModPackageItemCfgReq) Reset()         { *m = ModPackageItemCfgReq{} }
func (m *ModPackageItemCfgReq) String() string { return proto.CompactTextString(m) }
func (*ModPackageItemCfgReq) ProtoMessage()    {}
func (*ModPackageItemCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{15}
}
func (m *ModPackageItemCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModPackageItemCfgReq.Unmarshal(m, b)
}
func (m *ModPackageItemCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModPackageItemCfgReq.Marshal(b, m, deterministic)
}
func (dst *ModPackageItemCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModPackageItemCfgReq.Merge(dst, src)
}
func (m *ModPackageItemCfgReq) XXX_Size() int {
	return xxx_messageInfo_ModPackageItemCfgReq.Size(m)
}
func (m *ModPackageItemCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModPackageItemCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModPackageItemCfgReq proto.InternalMessageInfo

func (m *ModPackageItemCfgReq) GetItemCfg() *PackageItemCfg {
	if m != nil {
		return m.ItemCfg
	}
	return nil
}

type ModPackageItemCfgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModPackageItemCfgResp) Reset()         { *m = ModPackageItemCfgResp{} }
func (m *ModPackageItemCfgResp) String() string { return proto.CompactTextString(m) }
func (*ModPackageItemCfgResp) ProtoMessage()    {}
func (*ModPackageItemCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{16}
}
func (m *ModPackageItemCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModPackageItemCfgResp.Unmarshal(m, b)
}
func (m *ModPackageItemCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModPackageItemCfgResp.Marshal(b, m, deterministic)
}
func (dst *ModPackageItemCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModPackageItemCfgResp.Merge(dst, src)
}
func (m *ModPackageItemCfgResp) XXX_Size() int {
	return xxx_messageInfo_ModPackageItemCfgResp.Size(m)
}
func (m *ModPackageItemCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModPackageItemCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModPackageItemCfgResp proto.InternalMessageInfo

type DelPackageItemCfgReq struct {
	BgId                 uint32   `protobuf:"varint,1,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	BgItemId             uint32   `protobuf:"varint,2,opt,name=bg_item_id,json=bgItemId,proto3" json:"bg_item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPackageItemCfgReq) Reset()         { *m = DelPackageItemCfgReq{} }
func (m *DelPackageItemCfgReq) String() string { return proto.CompactTextString(m) }
func (*DelPackageItemCfgReq) ProtoMessage()    {}
func (*DelPackageItemCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{17}
}
func (m *DelPackageItemCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPackageItemCfgReq.Unmarshal(m, b)
}
func (m *DelPackageItemCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPackageItemCfgReq.Marshal(b, m, deterministic)
}
func (dst *DelPackageItemCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPackageItemCfgReq.Merge(dst, src)
}
func (m *DelPackageItemCfgReq) XXX_Size() int {
	return xxx_messageInfo_DelPackageItemCfgReq.Size(m)
}
func (m *DelPackageItemCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPackageItemCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPackageItemCfgReq proto.InternalMessageInfo

func (m *DelPackageItemCfgReq) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *DelPackageItemCfgReq) GetBgItemId() uint32 {
	if m != nil {
		return m.BgItemId
	}
	return 0
}

type DelPackageItemCfgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPackageItemCfgResp) Reset()         { *m = DelPackageItemCfgResp{} }
func (m *DelPackageItemCfgResp) String() string { return proto.CompactTextString(m) }
func (*DelPackageItemCfgResp) ProtoMessage()    {}
func (*DelPackageItemCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{18}
}
func (m *DelPackageItemCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPackageItemCfgResp.Unmarshal(m, b)
}
func (m *DelPackageItemCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPackageItemCfgResp.Marshal(b, m, deterministic)
}
func (dst *DelPackageItemCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPackageItemCfgResp.Merge(dst, src)
}
func (m *DelPackageItemCfgResp) XXX_Size() int {
	return xxx_messageInfo_DelPackageItemCfgResp.Size(m)
}
func (m *DelPackageItemCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPackageItemCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPackageItemCfgResp proto.InternalMessageInfo

type GetPackageItemCfgReq struct {
	BgId                 uint32   `protobuf:"varint,1,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	BgIdList             []uint32 `protobuf:"varint,2,rep,packed,name=bg_id_list,json=bgIdList,proto3" json:"bg_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPackageItemCfgReq) Reset()         { *m = GetPackageItemCfgReq{} }
func (m *GetPackageItemCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetPackageItemCfgReq) ProtoMessage()    {}
func (*GetPackageItemCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{19}
}
func (m *GetPackageItemCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPackageItemCfgReq.Unmarshal(m, b)
}
func (m *GetPackageItemCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPackageItemCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetPackageItemCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPackageItemCfgReq.Merge(dst, src)
}
func (m *GetPackageItemCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetPackageItemCfgReq.Size(m)
}
func (m *GetPackageItemCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPackageItemCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPackageItemCfgReq proto.InternalMessageInfo

func (m *GetPackageItemCfgReq) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *GetPackageItemCfgReq) GetBgIdList() []uint32 {
	if m != nil {
		return m.BgIdList
	}
	return nil
}

type PackageItemCfgList struct {
	ItemCfgList          []*PackageItemCfg `protobuf:"bytes,1,rep,name=item_cfg_list,json=itemCfgList,proto3" json:"item_cfg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PackageItemCfgList) Reset()         { *m = PackageItemCfgList{} }
func (m *PackageItemCfgList) String() string { return proto.CompactTextString(m) }
func (*PackageItemCfgList) ProtoMessage()    {}
func (*PackageItemCfgList) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{20}
}
func (m *PackageItemCfgList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PackageItemCfgList.Unmarshal(m, b)
}
func (m *PackageItemCfgList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PackageItemCfgList.Marshal(b, m, deterministic)
}
func (dst *PackageItemCfgList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PackageItemCfgList.Merge(dst, src)
}
func (m *PackageItemCfgList) XXX_Size() int {
	return xxx_messageInfo_PackageItemCfgList.Size(m)
}
func (m *PackageItemCfgList) XXX_DiscardUnknown() {
	xxx_messageInfo_PackageItemCfgList.DiscardUnknown(m)
}

var xxx_messageInfo_PackageItemCfgList proto.InternalMessageInfo

func (m *PackageItemCfgList) GetItemCfgList() []*PackageItemCfg {
	if m != nil {
		return m.ItemCfgList
	}
	return nil
}

type GetPackageItemCfgResp struct {
	ItemCfgList          []*PackageItemCfg     `protobuf:"bytes,1,rep,name=item_cfg_list,json=itemCfgList,proto3" json:"item_cfg_list,omitempty"`
	PackageItemCfgList   []*PackageItemCfgList `protobuf:"bytes,2,rep,name=package_item_cfg_list,json=packageItemCfgList,proto3" json:"package_item_cfg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetPackageItemCfgResp) Reset()         { *m = GetPackageItemCfgResp{} }
func (m *GetPackageItemCfgResp) String() string { return proto.CompactTextString(m) }
func (*GetPackageItemCfgResp) ProtoMessage()    {}
func (*GetPackageItemCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{21}
}
func (m *GetPackageItemCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPackageItemCfgResp.Unmarshal(m, b)
}
func (m *GetPackageItemCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPackageItemCfgResp.Marshal(b, m, deterministic)
}
func (dst *GetPackageItemCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPackageItemCfgResp.Merge(dst, src)
}
func (m *GetPackageItemCfgResp) XXX_Size() int {
	return xxx_messageInfo_GetPackageItemCfgResp.Size(m)
}
func (m *GetPackageItemCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPackageItemCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPackageItemCfgResp proto.InternalMessageInfo

func (m *GetPackageItemCfgResp) GetItemCfgList() []*PackageItemCfg {
	if m != nil {
		return m.ItemCfgList
	}
	return nil
}

func (m *GetPackageItemCfgResp) GetPackageItemCfgList() []*PackageItemCfgList {
	if m != nil {
		return m.PackageItemCfgList
	}
	return nil
}

// 获取背包物品展示权重配置
type ItemWeightCfg struct {
	ItemType             uint32   `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemId               uint32   `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Weight               uint32   `protobuf:"varint,3,opt,name=weight,proto3" json:"weight,omitempty"`
	IsDel                bool     `protobuf:"varint,4,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ItemWeightCfg) Reset()         { *m = ItemWeightCfg{} }
func (m *ItemWeightCfg) String() string { return proto.CompactTextString(m) }
func (*ItemWeightCfg) ProtoMessage()    {}
func (*ItemWeightCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{22}
}
func (m *ItemWeightCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ItemWeightCfg.Unmarshal(m, b)
}
func (m *ItemWeightCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ItemWeightCfg.Marshal(b, m, deterministic)
}
func (dst *ItemWeightCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ItemWeightCfg.Merge(dst, src)
}
func (m *ItemWeightCfg) XXX_Size() int {
	return xxx_messageInfo_ItemWeightCfg.Size(m)
}
func (m *ItemWeightCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_ItemWeightCfg.DiscardUnknown(m)
}

var xxx_messageInfo_ItemWeightCfg proto.InternalMessageInfo

func (m *ItemWeightCfg) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *ItemWeightCfg) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *ItemWeightCfg) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *ItemWeightCfg) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

type GetItemWeightCfgReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetItemWeightCfgReq) Reset()         { *m = GetItemWeightCfgReq{} }
func (m *GetItemWeightCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetItemWeightCfgReq) ProtoMessage()    {}
func (*GetItemWeightCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{23}
}
func (m *GetItemWeightCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetItemWeightCfgReq.Unmarshal(m, b)
}
func (m *GetItemWeightCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetItemWeightCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetItemWeightCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetItemWeightCfgReq.Merge(dst, src)
}
func (m *GetItemWeightCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetItemWeightCfgReq.Size(m)
}
func (m *GetItemWeightCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetItemWeightCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetItemWeightCfgReq proto.InternalMessageInfo

type GetItemWeightCfgRsp struct {
	CfgList              []*ItemWeightCfg `protobuf:"bytes,1,rep,name=cfg_list,json=cfgList,proto3" json:"cfg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetItemWeightCfgRsp) Reset()         { *m = GetItemWeightCfgRsp{} }
func (m *GetItemWeightCfgRsp) String() string { return proto.CompactTextString(m) }
func (*GetItemWeightCfgRsp) ProtoMessage()    {}
func (*GetItemWeightCfgRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{24}
}
func (m *GetItemWeightCfgRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetItemWeightCfgRsp.Unmarshal(m, b)
}
func (m *GetItemWeightCfgRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetItemWeightCfgRsp.Marshal(b, m, deterministic)
}
func (dst *GetItemWeightCfgRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetItemWeightCfgRsp.Merge(dst, src)
}
func (m *GetItemWeightCfgRsp) XXX_Size() int {
	return xxx_messageInfo_GetItemWeightCfgRsp.Size(m)
}
func (m *GetItemWeightCfgRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetItemWeightCfgRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetItemWeightCfgRsp proto.InternalMessageInfo

func (m *GetItemWeightCfgRsp) GetCfgList() []*ItemWeightCfg {
	if m != nil {
		return m.CfgList
	}
	return nil
}

type AddItemWeightCfgReq struct {
	Cfg                  *ItemWeightCfg `protobuf:"bytes,1,opt,name=cfg,proto3" json:"cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *AddItemWeightCfgReq) Reset()         { *m = AddItemWeightCfgReq{} }
func (m *AddItemWeightCfgReq) String() string { return proto.CompactTextString(m) }
func (*AddItemWeightCfgReq) ProtoMessage()    {}
func (*AddItemWeightCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{25}
}
func (m *AddItemWeightCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddItemWeightCfgReq.Unmarshal(m, b)
}
func (m *AddItemWeightCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddItemWeightCfgReq.Marshal(b, m, deterministic)
}
func (dst *AddItemWeightCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddItemWeightCfgReq.Merge(dst, src)
}
func (m *AddItemWeightCfgReq) XXX_Size() int {
	return xxx_messageInfo_AddItemWeightCfgReq.Size(m)
}
func (m *AddItemWeightCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddItemWeightCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddItemWeightCfgReq proto.InternalMessageInfo

func (m *AddItemWeightCfgReq) GetCfg() *ItemWeightCfg {
	if m != nil {
		return m.Cfg
	}
	return nil
}

type AddItemWeightCfgRsp struct {
	CfgId                uint32   `protobuf:"varint,1,opt,name=cfg_id,json=cfgId,proto3" json:"cfg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddItemWeightCfgRsp) Reset()         { *m = AddItemWeightCfgRsp{} }
func (m *AddItemWeightCfgRsp) String() string { return proto.CompactTextString(m) }
func (*AddItemWeightCfgRsp) ProtoMessage()    {}
func (*AddItemWeightCfgRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{26}
}
func (m *AddItemWeightCfgRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddItemWeightCfgRsp.Unmarshal(m, b)
}
func (m *AddItemWeightCfgRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddItemWeightCfgRsp.Marshal(b, m, deterministic)
}
func (dst *AddItemWeightCfgRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddItemWeightCfgRsp.Merge(dst, src)
}
func (m *AddItemWeightCfgRsp) XXX_Size() int {
	return xxx_messageInfo_AddItemWeightCfgRsp.Size(m)
}
func (m *AddItemWeightCfgRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddItemWeightCfgRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AddItemWeightCfgRsp proto.InternalMessageInfo

func (m *AddItemWeightCfgRsp) GetCfgId() uint32 {
	if m != nil {
		return m.CfgId
	}
	return 0
}

// 包裹发放协议
type GiveUserPackageReq struct {
	Uid            uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BgId           uint32 `protobuf:"varint,2,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	Num            uint32 `protobuf:"varint,3,opt,name=num,proto3" json:"num,omitempty"`
	Source         uint32 `protobuf:"varint,4,opt,name=source,proto3" json:"source,omitempty"`
	OrderId        string `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ExpireDuration uint32 `protobuf:"varint,6,opt,name=expire_duration,json=expireDuration,proto3" json:"expire_duration,omitempty"`
	// example:expire_duration=1,当前时间为 2019-10-28 10:42:00 这该物品过期时间为 2019-10-29 00:00:00
	// note :expire_duration>=1 则会忽略 包裹配置的 dynamic_fin_time, months
	SourceAppId          string   `protobuf:"bytes,7,opt,name=source_app_id,json=sourceAppId,proto3" json:"source_app_id,omitempty"`
	TotalPrice           uint32   `protobuf:"varint,8,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	OutsideTime          uint32   `protobuf:"varint,9,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
	Sign                 string   `protobuf:"bytes,10,opt,name=sign,proto3" json:"sign,omitempty"`
	BusinessType         uint32   `protobuf:"varint,11,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiveUserPackageReq) Reset()         { *m = GiveUserPackageReq{} }
func (m *GiveUserPackageReq) String() string { return proto.CompactTextString(m) }
func (*GiveUserPackageReq) ProtoMessage()    {}
func (*GiveUserPackageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{27}
}
func (m *GiveUserPackageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiveUserPackageReq.Unmarshal(m, b)
}
func (m *GiveUserPackageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiveUserPackageReq.Marshal(b, m, deterministic)
}
func (dst *GiveUserPackageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiveUserPackageReq.Merge(dst, src)
}
func (m *GiveUserPackageReq) XXX_Size() int {
	return xxx_messageInfo_GiveUserPackageReq.Size(m)
}
func (m *GiveUserPackageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GiveUserPackageReq.DiscardUnknown(m)
}

var xxx_messageInfo_GiveUserPackageReq proto.InternalMessageInfo

func (m *GiveUserPackageReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GiveUserPackageReq) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *GiveUserPackageReq) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *GiveUserPackageReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *GiveUserPackageReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *GiveUserPackageReq) GetExpireDuration() uint32 {
	if m != nil {
		return m.ExpireDuration
	}
	return 0
}

func (m *GiveUserPackageReq) GetSourceAppId() string {
	if m != nil {
		return m.SourceAppId
	}
	return ""
}

func (m *GiveUserPackageReq) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *GiveUserPackageReq) GetOutsideTime() uint32 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

func (m *GiveUserPackageReq) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

func (m *GiveUserPackageReq) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

type GiveUserPackageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiveUserPackageResp) Reset()         { *m = GiveUserPackageResp{} }
func (m *GiveUserPackageResp) String() string { return proto.CompactTextString(m) }
func (*GiveUserPackageResp) ProtoMessage()    {}
func (*GiveUserPackageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{28}
}
func (m *GiveUserPackageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiveUserPackageResp.Unmarshal(m, b)
}
func (m *GiveUserPackageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiveUserPackageResp.Marshal(b, m, deterministic)
}
func (dst *GiveUserPackageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiveUserPackageResp.Merge(dst, src)
}
func (m *GiveUserPackageResp) XXX_Size() int {
	return xxx_messageInfo_GiveUserPackageResp.Size(m)
}
func (m *GiveUserPackageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GiveUserPackageResp.DiscardUnknown(m)
}

var xxx_messageInfo_GiveUserPackageResp proto.InternalMessageInfo

type UseBackpackExtraInfo struct {
	TargetUid            uint32   `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UseCount             uint32   `protobuf:"varint,3,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	DealToken            string   `protobuf:"bytes,4,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UseBackpackExtraInfo) Reset()         { *m = UseBackpackExtraInfo{} }
func (m *UseBackpackExtraInfo) String() string { return proto.CompactTextString(m) }
func (*UseBackpackExtraInfo) ProtoMessage()    {}
func (*UseBackpackExtraInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{29}
}
func (m *UseBackpackExtraInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UseBackpackExtraInfo.Unmarshal(m, b)
}
func (m *UseBackpackExtraInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UseBackpackExtraInfo.Marshal(b, m, deterministic)
}
func (dst *UseBackpackExtraInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UseBackpackExtraInfo.Merge(dst, src)
}
func (m *UseBackpackExtraInfo) XXX_Size() int {
	return xxx_messageInfo_UseBackpackExtraInfo.Size(m)
}
func (m *UseBackpackExtraInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UseBackpackExtraInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UseBackpackExtraInfo proto.InternalMessageInfo

func (m *UseBackpackExtraInfo) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *UseBackpackExtraInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UseBackpackExtraInfo) GetUseCount() uint32 {
	if m != nil {
		return m.UseCount
	}
	return 0
}

func (m *UseBackpackExtraInfo) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

// 背包物品使用协议
type UseBackpackItemReq struct {
	Uid                  uint32                `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemType             uint32                `protobuf:"varint,2,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	SourceId             uint32                `protobuf:"varint,4,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	OrderId              string                `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	UseCount             uint32                `protobuf:"varint,6,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	OutsideTime          uint32                `protobuf:"varint,7,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
	OrderIdList          []string              `protobuf:"bytes,8,rep,name=order_id_list,json=orderIdList,proto3" json:"order_id_list,omitempty"`
	ItemPrice            uint32                `protobuf:"varint,9,opt,name=item_price,json=itemPrice,proto3" json:"item_price,omitempty"`
	PriceType            uint32                `protobuf:"varint,10,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	TargetUidList        []uint32              `protobuf:"varint,11,rep,packed,name=target_uid_list,json=targetUidList,proto3" json:"target_uid_list,omitempty"`
	ExtraInfo            *UseBackpackExtraInfo `protobuf:"bytes,12,opt,name=extra_info,json=extraInfo,proto3" json:"extra_info,omitempty"`
	UseReasionType       uint32                `protobuf:"varint,13,opt,name=use_reasion_type,json=useReasionType,proto3" json:"use_reasion_type,omitempty"`
	BusinessType         uint32                `protobuf:"varint,14,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *UseBackpackItemReq) Reset()         { *m = UseBackpackItemReq{} }
func (m *UseBackpackItemReq) String() string { return proto.CompactTextString(m) }
func (*UseBackpackItemReq) ProtoMessage()    {}
func (*UseBackpackItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{30}
}
func (m *UseBackpackItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UseBackpackItemReq.Unmarshal(m, b)
}
func (m *UseBackpackItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UseBackpackItemReq.Marshal(b, m, deterministic)
}
func (dst *UseBackpackItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UseBackpackItemReq.Merge(dst, src)
}
func (m *UseBackpackItemReq) XXX_Size() int {
	return xxx_messageInfo_UseBackpackItemReq.Size(m)
}
func (m *UseBackpackItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UseBackpackItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_UseBackpackItemReq proto.InternalMessageInfo

func (m *UseBackpackItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UseBackpackItemReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *UseBackpackItemReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *UseBackpackItemReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *UseBackpackItemReq) GetUseCount() uint32 {
	if m != nil {
		return m.UseCount
	}
	return 0
}

func (m *UseBackpackItemReq) GetOutsideTime() uint32 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

func (m *UseBackpackItemReq) GetOrderIdList() []string {
	if m != nil {
		return m.OrderIdList
	}
	return nil
}

func (m *UseBackpackItemReq) GetItemPrice() uint32 {
	if m != nil {
		return m.ItemPrice
	}
	return 0
}

func (m *UseBackpackItemReq) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *UseBackpackItemReq) GetTargetUidList() []uint32 {
	if m != nil {
		return m.TargetUidList
	}
	return nil
}

func (m *UseBackpackItemReq) GetExtraInfo() *UseBackpackExtraInfo {
	if m != nil {
		return m.ExtraInfo
	}
	return nil
}

func (m *UseBackpackItemReq) GetUseReasionType() uint32 {
	if m != nil {
		return m.UseReasionType
	}
	return 0
}

func (m *UseBackpackItemReq) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

type UseBackpackItemResp struct {
	Remain               uint32   `protobuf:"varint,1,opt,name=remain,proto3" json:"remain,omitempty"`
	DealToken            string   `protobuf:"bytes,2,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	FinTime              uint32   `protobuf:"varint,3,opt,name=fin_time,json=finTime,proto3" json:"fin_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UseBackpackItemResp) Reset()         { *m = UseBackpackItemResp{} }
func (m *UseBackpackItemResp) String() string { return proto.CompactTextString(m) }
func (*UseBackpackItemResp) ProtoMessage()    {}
func (*UseBackpackItemResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{31}
}
func (m *UseBackpackItemResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UseBackpackItemResp.Unmarshal(m, b)
}
func (m *UseBackpackItemResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UseBackpackItemResp.Marshal(b, m, deterministic)
}
func (dst *UseBackpackItemResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UseBackpackItemResp.Merge(dst, src)
}
func (m *UseBackpackItemResp) XXX_Size() int {
	return xxx_messageInfo_UseBackpackItemResp.Size(m)
}
func (m *UseBackpackItemResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UseBackpackItemResp.DiscardUnknown(m)
}

var xxx_messageInfo_UseBackpackItemResp proto.InternalMessageInfo

func (m *UseBackpackItemResp) GetRemain() uint32 {
	if m != nil {
		return m.Remain
	}
	return 0
}

func (m *UseBackpackItemResp) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

func (m *UseBackpackItemResp) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

type BaseItemInfo struct {
	ItemType             uint32   `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	SourceId             uint32   `protobuf:"varint,2,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	Num                  uint32   `protobuf:"varint,3,opt,name=num,proto3" json:"num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseItemInfo) Reset()         { *m = BaseItemInfo{} }
func (m *BaseItemInfo) String() string { return proto.CompactTextString(m) }
func (*BaseItemInfo) ProtoMessage()    {}
func (*BaseItemInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{32}
}
func (m *BaseItemInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseItemInfo.Unmarshal(m, b)
}
func (m *BaseItemInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseItemInfo.Marshal(b, m, deterministic)
}
func (dst *BaseItemInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseItemInfo.Merge(dst, src)
}
func (m *BaseItemInfo) XXX_Size() int {
	return xxx_messageInfo_BaseItemInfo.Size(m)
}
func (m *BaseItemInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseItemInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BaseItemInfo proto.InternalMessageInfo

func (m *BaseItemInfo) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *BaseItemInfo) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *BaseItemInfo) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

// 批量背包物品使用协议
type BatchUseBackpackItemReq struct {
	Uid                  uint32          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string          `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	OutsideTime          uint32          `protobuf:"varint,3,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
	UseReasonType        uint32          `protobuf:"varint,4,opt,name=use_reason_type,json=useReasonType,proto3" json:"use_reason_type,omitempty"`
	UseItemInfos         []*BaseItemInfo `protobuf:"bytes,5,rep,name=use_item_infos,json=useItemInfos,proto3" json:"use_item_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchUseBackpackItemReq) Reset()         { *m = BatchUseBackpackItemReq{} }
func (m *BatchUseBackpackItemReq) String() string { return proto.CompactTextString(m) }
func (*BatchUseBackpackItemReq) ProtoMessage()    {}
func (*BatchUseBackpackItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{33}
}
func (m *BatchUseBackpackItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUseBackpackItemReq.Unmarshal(m, b)
}
func (m *BatchUseBackpackItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUseBackpackItemReq.Marshal(b, m, deterministic)
}
func (dst *BatchUseBackpackItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUseBackpackItemReq.Merge(dst, src)
}
func (m *BatchUseBackpackItemReq) XXX_Size() int {
	return xxx_messageInfo_BatchUseBackpackItemReq.Size(m)
}
func (m *BatchUseBackpackItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUseBackpackItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUseBackpackItemReq proto.InternalMessageInfo

func (m *BatchUseBackpackItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchUseBackpackItemReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *BatchUseBackpackItemReq) GetOutsideTime() uint32 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

func (m *BatchUseBackpackItemReq) GetUseReasonType() uint32 {
	if m != nil {
		return m.UseReasonType
	}
	return 0
}

func (m *BatchUseBackpackItemReq) GetUseItemInfos() []*BaseItemInfo {
	if m != nil {
		return m.UseItemInfos
	}
	return nil
}

type BatchUseBackpackItemResp struct {
	DealToken            string   `protobuf:"bytes,1,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUseBackpackItemResp) Reset()         { *m = BatchUseBackpackItemResp{} }
func (m *BatchUseBackpackItemResp) String() string { return proto.CompactTextString(m) }
func (*BatchUseBackpackItemResp) ProtoMessage()    {}
func (*BatchUseBackpackItemResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{34}
}
func (m *BatchUseBackpackItemResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUseBackpackItemResp.Unmarshal(m, b)
}
func (m *BatchUseBackpackItemResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUseBackpackItemResp.Marshal(b, m, deterministic)
}
func (dst *BatchUseBackpackItemResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUseBackpackItemResp.Merge(dst, src)
}
func (m *BatchUseBackpackItemResp) XXX_Size() int {
	return xxx_messageInfo_BatchUseBackpackItemResp.Size(m)
}
func (m *BatchUseBackpackItemResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUseBackpackItemResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUseBackpackItemResp proto.InternalMessageInfo

func (m *BatchUseBackpackItemResp) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

// 检查某个物品使用是否成功
type CheckUseItemSuccByMainOrderReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	OutsideTime          uint32   `protobuf:"varint,2,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUseItemSuccByMainOrderReq) Reset()         { *m = CheckUseItemSuccByMainOrderReq{} }
func (m *CheckUseItemSuccByMainOrderReq) String() string { return proto.CompactTextString(m) }
func (*CheckUseItemSuccByMainOrderReq) ProtoMessage()    {}
func (*CheckUseItemSuccByMainOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{35}
}
func (m *CheckUseItemSuccByMainOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUseItemSuccByMainOrderReq.Unmarshal(m, b)
}
func (m *CheckUseItemSuccByMainOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUseItemSuccByMainOrderReq.Marshal(b, m, deterministic)
}
func (dst *CheckUseItemSuccByMainOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUseItemSuccByMainOrderReq.Merge(dst, src)
}
func (m *CheckUseItemSuccByMainOrderReq) XXX_Size() int {
	return xxx_messageInfo_CheckUseItemSuccByMainOrderReq.Size(m)
}
func (m *CheckUseItemSuccByMainOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUseItemSuccByMainOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUseItemSuccByMainOrderReq proto.InternalMessageInfo

func (m *CheckUseItemSuccByMainOrderReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *CheckUseItemSuccByMainOrderReq) GetOutsideTime() uint32 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

type CheckUseItemSuccByMainOrderResp struct {
	Success              bool     `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUseItemSuccByMainOrderResp) Reset()         { *m = CheckUseItemSuccByMainOrderResp{} }
func (m *CheckUseItemSuccByMainOrderResp) String() string { return proto.CompactTextString(m) }
func (*CheckUseItemSuccByMainOrderResp) ProtoMessage()    {}
func (*CheckUseItemSuccByMainOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{36}
}
func (m *CheckUseItemSuccByMainOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUseItemSuccByMainOrderResp.Unmarshal(m, b)
}
func (m *CheckUseItemSuccByMainOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUseItemSuccByMainOrderResp.Marshal(b, m, deterministic)
}
func (dst *CheckUseItemSuccByMainOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUseItemSuccByMainOrderResp.Merge(dst, src)
}
func (m *CheckUseItemSuccByMainOrderResp) XXX_Size() int {
	return xxx_messageInfo_CheckUseItemSuccByMainOrderResp.Size(m)
}
func (m *CheckUseItemSuccByMainOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUseItemSuccByMainOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUseItemSuccByMainOrderResp proto.InternalMessageInfo

func (m *CheckUseItemSuccByMainOrderResp) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

type GetUseItemOrderInfoReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUseItemOrderInfoReq) Reset()         { *m = GetUseItemOrderInfoReq{} }
func (m *GetUseItemOrderInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUseItemOrderInfoReq) ProtoMessage()    {}
func (*GetUseItemOrderInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{37}
}
func (m *GetUseItemOrderInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUseItemOrderInfoReq.Unmarshal(m, b)
}
func (m *GetUseItemOrderInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUseItemOrderInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUseItemOrderInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUseItemOrderInfoReq.Merge(dst, src)
}
func (m *GetUseItemOrderInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUseItemOrderInfoReq.Size(m)
}
func (m *GetUseItemOrderInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUseItemOrderInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUseItemOrderInfoReq proto.InternalMessageInfo

func (m *GetUseItemOrderInfoReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetUseItemOrderInfoResp struct {
	UseOrderDetail       *UseOrderDetail       `protobuf:"bytes,1,opt,name=use_order_detail,json=useOrderDetail,proto3" json:"use_order_detail,omitempty"`
	UseOrderExtraInfo    *UseBackpackExtraInfo `protobuf:"bytes,2,opt,name=use_order_extra_info,json=useOrderExtraInfo,proto3" json:"use_order_extra_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetUseItemOrderInfoResp) Reset()         { *m = GetUseItemOrderInfoResp{} }
func (m *GetUseItemOrderInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUseItemOrderInfoResp) ProtoMessage()    {}
func (*GetUseItemOrderInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{38}
}
func (m *GetUseItemOrderInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUseItemOrderInfoResp.Unmarshal(m, b)
}
func (m *GetUseItemOrderInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUseItemOrderInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUseItemOrderInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUseItemOrderInfoResp.Merge(dst, src)
}
func (m *GetUseItemOrderInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUseItemOrderInfoResp.Size(m)
}
func (m *GetUseItemOrderInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUseItemOrderInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUseItemOrderInfoResp proto.InternalMessageInfo

func (m *GetUseItemOrderInfoResp) GetUseOrderDetail() *UseOrderDetail {
	if m != nil {
		return m.UseOrderDetail
	}
	return nil
}

func (m *GetUseItemOrderInfoResp) GetUseOrderExtraInfo() *UseBackpackExtraInfo {
	if m != nil {
		return m.UseOrderExtraInfo
	}
	return nil
}

type ProcBackpackItemTimeoutReq struct {
	UserItemId           uint32   `protobuf:"varint,1,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemType             uint32   `protobuf:"varint,3,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	SourceId             uint32   `protobuf:"varint,4,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	ItemCount            uint32   `protobuf:"varint,5,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	FinTime              uint32   `protobuf:"varint,6,opt,name=fin_time,json=finTime,proto3" json:"fin_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProcBackpackItemTimeoutReq) Reset()         { *m = ProcBackpackItemTimeoutReq{} }
func (m *ProcBackpackItemTimeoutReq) String() string { return proto.CompactTextString(m) }
func (*ProcBackpackItemTimeoutReq) ProtoMessage()    {}
func (*ProcBackpackItemTimeoutReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{39}
}
func (m *ProcBackpackItemTimeoutReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProcBackpackItemTimeoutReq.Unmarshal(m, b)
}
func (m *ProcBackpackItemTimeoutReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProcBackpackItemTimeoutReq.Marshal(b, m, deterministic)
}
func (dst *ProcBackpackItemTimeoutReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProcBackpackItemTimeoutReq.Merge(dst, src)
}
func (m *ProcBackpackItemTimeoutReq) XXX_Size() int {
	return xxx_messageInfo_ProcBackpackItemTimeoutReq.Size(m)
}
func (m *ProcBackpackItemTimeoutReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ProcBackpackItemTimeoutReq.DiscardUnknown(m)
}

var xxx_messageInfo_ProcBackpackItemTimeoutReq proto.InternalMessageInfo

func (m *ProcBackpackItemTimeoutReq) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *ProcBackpackItemTimeoutReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ProcBackpackItemTimeoutReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *ProcBackpackItemTimeoutReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *ProcBackpackItemTimeoutReq) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *ProcBackpackItemTimeoutReq) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

type ProcBackpackItemTimeoutResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProcBackpackItemTimeoutResp) Reset()         { *m = ProcBackpackItemTimeoutResp{} }
func (m *ProcBackpackItemTimeoutResp) String() string { return proto.CompactTextString(m) }
func (*ProcBackpackItemTimeoutResp) ProtoMessage()    {}
func (*ProcBackpackItemTimeoutResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{40}
}
func (m *ProcBackpackItemTimeoutResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProcBackpackItemTimeoutResp.Unmarshal(m, b)
}
func (m *ProcBackpackItemTimeoutResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProcBackpackItemTimeoutResp.Marshal(b, m, deterministic)
}
func (dst *ProcBackpackItemTimeoutResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProcBackpackItemTimeoutResp.Merge(dst, src)
}
func (m *ProcBackpackItemTimeoutResp) XXX_Size() int {
	return xxx_messageInfo_ProcBackpackItemTimeoutResp.Size(m)
}
func (m *ProcBackpackItemTimeoutResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ProcBackpackItemTimeoutResp.DiscardUnknown(m)
}

var xxx_messageInfo_ProcBackpackItemTimeoutResp proto.InternalMessageInfo

type GetUserBackpackReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserBackpackReq) Reset()         { *m = GetUserBackpackReq{} }
func (m *GetUserBackpackReq) String() string { return proto.CompactTextString(m) }
func (*GetUserBackpackReq) ProtoMessage()    {}
func (*GetUserBackpackReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{41}
}
func (m *GetUserBackpackReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBackpackReq.Unmarshal(m, b)
}
func (m *GetUserBackpackReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBackpackReq.Marshal(b, m, deterministic)
}
func (dst *GetUserBackpackReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBackpackReq.Merge(dst, src)
}
func (m *GetUserBackpackReq) XXX_Size() int {
	return xxx_messageInfo_GetUserBackpackReq.Size(m)
}
func (m *GetUserBackpackReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBackpackReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBackpackReq proto.InternalMessageInfo

func (m *GetUserBackpackReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserBackpackResp struct {
	UserItemList         []*UserBackpackItem `protobuf:"bytes,1,rep,name=user_item_list,json=userItemList,proto3" json:"user_item_list,omitempty"`
	LastObtainTs         int64               `protobuf:"varint,2,opt,name=last_obtain_ts,json=lastObtainTs,proto3" json:"last_obtain_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetUserBackpackResp) Reset()         { *m = GetUserBackpackResp{} }
func (m *GetUserBackpackResp) String() string { return proto.CompactTextString(m) }
func (*GetUserBackpackResp) ProtoMessage()    {}
func (*GetUserBackpackResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{42}
}
func (m *GetUserBackpackResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBackpackResp.Unmarshal(m, b)
}
func (m *GetUserBackpackResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBackpackResp.Marshal(b, m, deterministic)
}
func (dst *GetUserBackpackResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBackpackResp.Merge(dst, src)
}
func (m *GetUserBackpackResp) XXX_Size() int {
	return xxx_messageInfo_GetUserBackpackResp.Size(m)
}
func (m *GetUserBackpackResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBackpackResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBackpackResp proto.InternalMessageInfo

func (m *GetUserBackpackResp) GetUserItemList() []*UserBackpackItem {
	if m != nil {
		return m.UserItemList
	}
	return nil
}

func (m *GetUserBackpackResp) GetLastObtainTs() int64 {
	if m != nil {
		return m.LastObtainTs
	}
	return 0
}

// 按物品查询用户物品
type GetUserBackpackByItemReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemType             uint32   `protobuf:"varint,2,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	SourceId             uint32   `protobuf:"varint,3,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	IsNeedObtainTs       bool     `protobuf:"varint,4,opt,name=is_need_obtain_ts,json=isNeedObtainTs,proto3" json:"is_need_obtain_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserBackpackByItemReq) Reset()         { *m = GetUserBackpackByItemReq{} }
func (m *GetUserBackpackByItemReq) String() string { return proto.CompactTextString(m) }
func (*GetUserBackpackByItemReq) ProtoMessage()    {}
func (*GetUserBackpackByItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{43}
}
func (m *GetUserBackpackByItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBackpackByItemReq.Unmarshal(m, b)
}
func (m *GetUserBackpackByItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBackpackByItemReq.Marshal(b, m, deterministic)
}
func (dst *GetUserBackpackByItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBackpackByItemReq.Merge(dst, src)
}
func (m *GetUserBackpackByItemReq) XXX_Size() int {
	return xxx_messageInfo_GetUserBackpackByItemReq.Size(m)
}
func (m *GetUserBackpackByItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBackpackByItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBackpackByItemReq proto.InternalMessageInfo

func (m *GetUserBackpackByItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserBackpackByItemReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *GetUserBackpackByItemReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *GetUserBackpackByItemReq) GetIsNeedObtainTs() bool {
	if m != nil {
		return m.IsNeedObtainTs
	}
	return false
}

type GetUserBackpackByItemResp struct {
	UserItemList         []*UserBackpackItem `protobuf:"bytes,1,rep,name=user_item_list,json=userItemList,proto3" json:"user_item_list,omitempty"`
	LastObtainTs         int64               `protobuf:"varint,2,opt,name=last_obtain_ts,json=lastObtainTs,proto3" json:"last_obtain_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetUserBackpackByItemResp) Reset()         { *m = GetUserBackpackByItemResp{} }
func (m *GetUserBackpackByItemResp) String() string { return proto.CompactTextString(m) }
func (*GetUserBackpackByItemResp) ProtoMessage()    {}
func (*GetUserBackpackByItemResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{44}
}
func (m *GetUserBackpackByItemResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBackpackByItemResp.Unmarshal(m, b)
}
func (m *GetUserBackpackByItemResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBackpackByItemResp.Marshal(b, m, deterministic)
}
func (dst *GetUserBackpackByItemResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBackpackByItemResp.Merge(dst, src)
}
func (m *GetUserBackpackByItemResp) XXX_Size() int {
	return xxx_messageInfo_GetUserBackpackByItemResp.Size(m)
}
func (m *GetUserBackpackByItemResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBackpackByItemResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBackpackByItemResp proto.InternalMessageInfo

func (m *GetUserBackpackByItemResp) GetUserItemList() []*UserBackpackItem {
	if m != nil {
		return m.UserItemList
	}
	return nil
}

func (m *GetUserBackpackByItemResp) GetLastObtainTs() int64 {
	if m != nil {
		return m.LastObtainTs
	}
	return 0
}

type GetUserBackpackLogReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTime            uint32   `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ItemType             uint32   `protobuf:"varint,4,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	SourceId             uint32   `protobuf:"varint,5,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	LogType              uint32   `protobuf:"varint,6,opt,name=log_type,json=logType,proto3" json:"log_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserBackpackLogReq) Reset()         { *m = GetUserBackpackLogReq{} }
func (m *GetUserBackpackLogReq) String() string { return proto.CompactTextString(m) }
func (*GetUserBackpackLogReq) ProtoMessage()    {}
func (*GetUserBackpackLogReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{45}
}
func (m *GetUserBackpackLogReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBackpackLogReq.Unmarshal(m, b)
}
func (m *GetUserBackpackLogReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBackpackLogReq.Marshal(b, m, deterministic)
}
func (dst *GetUserBackpackLogReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBackpackLogReq.Merge(dst, src)
}
func (m *GetUserBackpackLogReq) XXX_Size() int {
	return xxx_messageInfo_GetUserBackpackLogReq.Size(m)
}
func (m *GetUserBackpackLogReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBackpackLogReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBackpackLogReq proto.InternalMessageInfo

func (m *GetUserBackpackLogReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserBackpackLogReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetUserBackpackLogReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetUserBackpackLogReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *GetUserBackpackLogReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *GetUserBackpackLogReq) GetLogType() uint32 {
	if m != nil {
		return m.LogType
	}
	return 0
}

type GetUserBackpackLogResp struct {
	LogList              []*UserBackpackLog `protobuf:"bytes,1,rep,name=log_list,json=logList,proto3" json:"log_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetUserBackpackLogResp) Reset()         { *m = GetUserBackpackLogResp{} }
func (m *GetUserBackpackLogResp) String() string { return proto.CompactTextString(m) }
func (*GetUserBackpackLogResp) ProtoMessage()    {}
func (*GetUserBackpackLogResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{46}
}
func (m *GetUserBackpackLogResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBackpackLogResp.Unmarshal(m, b)
}
func (m *GetUserBackpackLogResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBackpackLogResp.Marshal(b, m, deterministic)
}
func (dst *GetUserBackpackLogResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBackpackLogResp.Merge(dst, src)
}
func (m *GetUserBackpackLogResp) XXX_Size() int {
	return xxx_messageInfo_GetUserBackpackLogResp.Size(m)
}
func (m *GetUserBackpackLogResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBackpackLogResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBackpackLogResp proto.InternalMessageInfo

func (m *GetUserBackpackLogResp) GetLogList() []*UserBackpackLog {
	if m != nil {
		return m.LogList
	}
	return nil
}

// 此接口暂时不支持添加卡片
type AddItemCfgReq struct {
	ItemType             uint32   `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemCfg              []byte   `protobuf:"bytes,2,opt,name=item_cfg,json=itemCfg,proto3" json:"item_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddItemCfgReq) Reset()         { *m = AddItemCfgReq{} }
func (m *AddItemCfgReq) String() string { return proto.CompactTextString(m) }
func (*AddItemCfgReq) ProtoMessage()    {}
func (*AddItemCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{47}
}
func (m *AddItemCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddItemCfgReq.Unmarshal(m, b)
}
func (m *AddItemCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddItemCfgReq.Marshal(b, m, deterministic)
}
func (dst *AddItemCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddItemCfgReq.Merge(dst, src)
}
func (m *AddItemCfgReq) XXX_Size() int {
	return xxx_messageInfo_AddItemCfgReq.Size(m)
}
func (m *AddItemCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddItemCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddItemCfgReq proto.InternalMessageInfo

func (m *AddItemCfgReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *AddItemCfgReq) GetItemCfg() []byte {
	if m != nil {
		return m.ItemCfg
	}
	return nil
}

type AddItemCfgResp struct {
	ItemType             uint32   `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemCfg              []byte   `protobuf:"bytes,2,opt,name=item_cfg,json=itemCfg,proto3" json:"item_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddItemCfgResp) Reset()         { *m = AddItemCfgResp{} }
func (m *AddItemCfgResp) String() string { return proto.CompactTextString(m) }
func (*AddItemCfgResp) ProtoMessage()    {}
func (*AddItemCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{48}
}
func (m *AddItemCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddItemCfgResp.Unmarshal(m, b)
}
func (m *AddItemCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddItemCfgResp.Marshal(b, m, deterministic)
}
func (dst *AddItemCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddItemCfgResp.Merge(dst, src)
}
func (m *AddItemCfgResp) XXX_Size() int {
	return xxx_messageInfo_AddItemCfgResp.Size(m)
}
func (m *AddItemCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddItemCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddItemCfgResp proto.InternalMessageInfo

func (m *AddItemCfgResp) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *AddItemCfgResp) GetItemCfg() []byte {
	if m != nil {
		return m.ItemCfg
	}
	return nil
}

type DelItemCfgReq struct {
	ItemType uint32 `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	// uint32 item_source_type = 3;
	ItemCfg              []byte   `protobuf:"bytes,2,opt,name=item_cfg,json=itemCfg,proto3" json:"item_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelItemCfgReq) Reset()         { *m = DelItemCfgReq{} }
func (m *DelItemCfgReq) String() string { return proto.CompactTextString(m) }
func (*DelItemCfgReq) ProtoMessage()    {}
func (*DelItemCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{49}
}
func (m *DelItemCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelItemCfgReq.Unmarshal(m, b)
}
func (m *DelItemCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelItemCfgReq.Marshal(b, m, deterministic)
}
func (dst *DelItemCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelItemCfgReq.Merge(dst, src)
}
func (m *DelItemCfgReq) XXX_Size() int {
	return xxx_messageInfo_DelItemCfgReq.Size(m)
}
func (m *DelItemCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelItemCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelItemCfgReq proto.InternalMessageInfo

func (m *DelItemCfgReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *DelItemCfgReq) GetItemCfg() []byte {
	if m != nil {
		return m.ItemCfg
	}
	return nil
}

type DelItemCfgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelItemCfgResp) Reset()         { *m = DelItemCfgResp{} }
func (m *DelItemCfgResp) String() string { return proto.CompactTextString(m) }
func (*DelItemCfgResp) ProtoMessage()    {}
func (*DelItemCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{50}
}
func (m *DelItemCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelItemCfgResp.Unmarshal(m, b)
}
func (m *DelItemCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelItemCfgResp.Marshal(b, m, deterministic)
}
func (dst *DelItemCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelItemCfgResp.Merge(dst, src)
}
func (m *DelItemCfgResp) XXX_Size() int {
	return xxx_messageInfo_DelItemCfgResp.Size(m)
}
func (m *DelItemCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelItemCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelItemCfgResp proto.InternalMessageInfo

type GetItemCfgReq struct {
	ItemType             uint32   `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemSourceIdList     []uint32 `protobuf:"varint,2,rep,packed,name=item_source_id_list,json=itemSourceIdList,proto3" json:"item_source_id_list,omitempty"`
	GetAll               bool     `protobuf:"varint,3,opt,name=get_all,json=getAll,proto3" json:"get_all,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetItemCfgReq) Reset()         { *m = GetItemCfgReq{} }
func (m *GetItemCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetItemCfgReq) ProtoMessage()    {}
func (*GetItemCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{51}
}
func (m *GetItemCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetItemCfgReq.Unmarshal(m, b)
}
func (m *GetItemCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetItemCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetItemCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetItemCfgReq.Merge(dst, src)
}
func (m *GetItemCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetItemCfgReq.Size(m)
}
func (m *GetItemCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetItemCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetItemCfgReq proto.InternalMessageInfo

func (m *GetItemCfgReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *GetItemCfgReq) GetItemSourceIdList() []uint32 {
	if m != nil {
		return m.ItemSourceIdList
	}
	return nil
}

func (m *GetItemCfgReq) GetGetAll() bool {
	if m != nil {
		return m.GetAll
	}
	return false
}

type GetItemCfgResp struct {
	ItemCfgList          [][]byte `protobuf:"bytes,1,rep,name=item_cfg_list,json=itemCfgList,proto3" json:"item_cfg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetItemCfgResp) Reset()         { *m = GetItemCfgResp{} }
func (m *GetItemCfgResp) String() string { return proto.CompactTextString(m) }
func (*GetItemCfgResp) ProtoMessage()    {}
func (*GetItemCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{52}
}
func (m *GetItemCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetItemCfgResp.Unmarshal(m, b)
}
func (m *GetItemCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetItemCfgResp.Marshal(b, m, deterministic)
}
func (dst *GetItemCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetItemCfgResp.Merge(dst, src)
}
func (m *GetItemCfgResp) XXX_Size() int {
	return xxx_messageInfo_GetItemCfgResp.Size(m)
}
func (m *GetItemCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetItemCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetItemCfgResp proto.InternalMessageInfo

func (m *GetItemCfgResp) GetItemCfgList() [][]byte {
	if m != nil {
		return m.ItemCfgList
	}
	return nil
}

type UseItemInfo struct {
	ItemType             uint32   `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	UserItemId           uint32   `protobuf:"varint,2,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
	SourceId             uint32   `protobuf:"varint,3,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	UseCount             uint32   `protobuf:"varint,4,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	TotalPrice           uint32   `protobuf:"varint,5,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UseItemInfo) Reset()         { *m = UseItemInfo{} }
func (m *UseItemInfo) String() string { return proto.CompactTextString(m) }
func (*UseItemInfo) ProtoMessage()    {}
func (*UseItemInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{53}
}
func (m *UseItemInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UseItemInfo.Unmarshal(m, b)
}
func (m *UseItemInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UseItemInfo.Marshal(b, m, deterministic)
}
func (dst *UseItemInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UseItemInfo.Merge(dst, src)
}
func (m *UseItemInfo) XXX_Size() int {
	return xxx_messageInfo_UseItemInfo.Size(m)
}
func (m *UseItemInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UseItemInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UseItemInfo proto.InternalMessageInfo

func (m *UseItemInfo) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *UseItemInfo) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *UseItemInfo) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *UseItemInfo) GetUseCount() uint32 {
	if m != nil {
		return m.UseCount
	}
	return 0
}

func (m *UseItemInfo) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

type TransactionInfo struct {
	FreezeType           uint32   `protobuf:"varint,1,opt,name=freeze_type,json=freezeType,proto3" json:"freeze_type,omitempty"`
	OperTime             uint32   `protobuf:"varint,2,opt,name=oper_time,json=operTime,proto3" json:"oper_time,omitempty"`
	OrderId              string   `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,4,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	Reason               uint32   `protobuf:"varint,5,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TransactionInfo) Reset()         { *m = TransactionInfo{} }
func (m *TransactionInfo) String() string { return proto.CompactTextString(m) }
func (*TransactionInfo) ProtoMessage()    {}
func (*TransactionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{54}
}
func (m *TransactionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TransactionInfo.Unmarshal(m, b)
}
func (m *TransactionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TransactionInfo.Marshal(b, m, deterministic)
}
func (dst *TransactionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransactionInfo.Merge(dst, src)
}
func (m *TransactionInfo) XXX_Size() int {
	return xxx_messageInfo_TransactionInfo.Size(m)
}
func (m *TransactionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TransactionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TransactionInfo proto.InternalMessageInfo

func (m *TransactionInfo) GetFreezeType() uint32 {
	if m != nil {
		return m.FreezeType
	}
	return 0
}

func (m *TransactionInfo) GetOperTime() uint32 {
	if m != nil {
		return m.OperTime
	}
	return 0
}

func (m *TransactionInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *TransactionInfo) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *TransactionInfo) GetReason() uint32 {
	if m != nil {
		return m.Reason
	}
	return 0
}

// 仅支付碎片类型： BACKPACK_LOTTERY_FRAGMENT
type FreeZeItemReq struct {
	TansacationInfo      *TransactionInfo `protobuf:"bytes,1,opt,name=tansacation_info,json=tansacationInfo,proto3" json:"tansacation_info,omitempty"`
	ItemInfoList         []*UseItemInfo   `protobuf:"bytes,2,rep,name=item_info_list,json=itemInfoList,proto3" json:"item_info_list,omitempty"`
	Uid                  uint32           `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *FreeZeItemReq) Reset()         { *m = FreeZeItemReq{} }
func (m *FreeZeItemReq) String() string { return proto.CompactTextString(m) }
func (*FreeZeItemReq) ProtoMessage()    {}
func (*FreeZeItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{55}
}
func (m *FreeZeItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreeZeItemReq.Unmarshal(m, b)
}
func (m *FreeZeItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreeZeItemReq.Marshal(b, m, deterministic)
}
func (dst *FreeZeItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreeZeItemReq.Merge(dst, src)
}
func (m *FreeZeItemReq) XXX_Size() int {
	return xxx_messageInfo_FreeZeItemReq.Size(m)
}
func (m *FreeZeItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FreeZeItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_FreeZeItemReq proto.InternalMessageInfo

func (m *FreeZeItemReq) GetTansacationInfo() *TransactionInfo {
	if m != nil {
		return m.TansacationInfo
	}
	return nil
}

func (m *FreeZeItemReq) GetItemInfoList() []*UseItemInfo {
	if m != nil {
		return m.ItemInfoList
	}
	return nil
}

func (m *FreeZeItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type FreeZeItemResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreeZeItemResp) Reset()         { *m = FreeZeItemResp{} }
func (m *FreeZeItemResp) String() string { return proto.CompactTextString(m) }
func (*FreeZeItemResp) ProtoMessage()    {}
func (*FreeZeItemResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{56}
}
func (m *FreeZeItemResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreeZeItemResp.Unmarshal(m, b)
}
func (m *FreeZeItemResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreeZeItemResp.Marshal(b, m, deterministic)
}
func (dst *FreeZeItemResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreeZeItemResp.Merge(dst, src)
}
func (m *FreeZeItemResp) XXX_Size() int {
	return xxx_messageInfo_FreeZeItemResp.Size(m)
}
func (m *FreeZeItemResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FreeZeItemResp.DiscardUnknown(m)
}

var xxx_messageInfo_FreeZeItemResp proto.InternalMessageInfo

// 新版碎片扣除，可回滚. 不用传具体物品项， 按过期时间优先使用
type FreeZeItemV2Req struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	OutsideTime          uint32   `protobuf:"varint,3,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
	FreezeType           uint32   `protobuf:"varint,4,opt,name=freeze_type,json=freezeType,proto3" json:"freeze_type,omitempty"`
	ItemType             uint32   `protobuf:"varint,5,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	SourceId             uint32   `protobuf:"varint,6,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	UseCount             uint32   `protobuf:"varint,7,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	Reason               uint32   `protobuf:"varint,8,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreeZeItemV2Req) Reset()         { *m = FreeZeItemV2Req{} }
func (m *FreeZeItemV2Req) String() string { return proto.CompactTextString(m) }
func (*FreeZeItemV2Req) ProtoMessage()    {}
func (*FreeZeItemV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{57}
}
func (m *FreeZeItemV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreeZeItemV2Req.Unmarshal(m, b)
}
func (m *FreeZeItemV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreeZeItemV2Req.Marshal(b, m, deterministic)
}
func (dst *FreeZeItemV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreeZeItemV2Req.Merge(dst, src)
}
func (m *FreeZeItemV2Req) XXX_Size() int {
	return xxx_messageInfo_FreeZeItemV2Req.Size(m)
}
func (m *FreeZeItemV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_FreeZeItemV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_FreeZeItemV2Req proto.InternalMessageInfo

func (m *FreeZeItemV2Req) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FreeZeItemV2Req) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *FreeZeItemV2Req) GetOutsideTime() uint32 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

func (m *FreeZeItemV2Req) GetFreezeType() uint32 {
	if m != nil {
		return m.FreezeType
	}
	return 0
}

func (m *FreeZeItemV2Req) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *FreeZeItemV2Req) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *FreeZeItemV2Req) GetUseCount() uint32 {
	if m != nil {
		return m.UseCount
	}
	return 0
}

func (m *FreeZeItemV2Req) GetReason() uint32 {
	if m != nil {
		return m.Reason
	}
	return 0
}

type FreeZeItemV2Resp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreeZeItemV2Resp) Reset()         { *m = FreeZeItemV2Resp{} }
func (m *FreeZeItemV2Resp) String() string { return proto.CompactTextString(m) }
func (*FreeZeItemV2Resp) ProtoMessage()    {}
func (*FreeZeItemV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{58}
}
func (m *FreeZeItemV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreeZeItemV2Resp.Unmarshal(m, b)
}
func (m *FreeZeItemV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreeZeItemV2Resp.Marshal(b, m, deterministic)
}
func (dst *FreeZeItemV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreeZeItemV2Resp.Merge(dst, src)
}
func (m *FreeZeItemV2Resp) XXX_Size() int {
	return xxx_messageInfo_FreeZeItemV2Resp.Size(m)
}
func (m *FreeZeItemV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_FreeZeItemV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_FreeZeItemV2Resp proto.InternalMessageInfo

type UserPackageSum struct {
	ItemType             uint32   `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemId               uint32   `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemCount            uint32   `protobuf:"varint,3,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPackageSum) Reset()         { *m = UserPackageSum{} }
func (m *UserPackageSum) String() string { return proto.CompactTextString(m) }
func (*UserPackageSum) ProtoMessage()    {}
func (*UserPackageSum) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{59}
}
func (m *UserPackageSum) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPackageSum.Unmarshal(m, b)
}
func (m *UserPackageSum) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPackageSum.Marshal(b, m, deterministic)
}
func (dst *UserPackageSum) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPackageSum.Merge(dst, src)
}
func (m *UserPackageSum) XXX_Size() int {
	return xxx_messageInfo_UserPackageSum.Size(m)
}
func (m *UserPackageSum) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPackageSum.DiscardUnknown(m)
}

var xxx_messageInfo_UserPackageSum proto.InternalMessageInfo

func (m *UserPackageSum) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *UserPackageSum) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *UserPackageSum) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

type GetUserPackageSumReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemType             uint32   `protobuf:"varint,2,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemId               uint32   `protobuf:"varint,3,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPackageSumReq) Reset()         { *m = GetUserPackageSumReq{} }
func (m *GetUserPackageSumReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPackageSumReq) ProtoMessage()    {}
func (*GetUserPackageSumReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{60}
}
func (m *GetUserPackageSumReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPackageSumReq.Unmarshal(m, b)
}
func (m *GetUserPackageSumReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPackageSumReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPackageSumReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPackageSumReq.Merge(dst, src)
}
func (m *GetUserPackageSumReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPackageSumReq.Size(m)
}
func (m *GetUserPackageSumReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPackageSumReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPackageSumReq proto.InternalMessageInfo

func (m *GetUserPackageSumReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserPackageSumReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *GetUserPackageSumReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type GetUserPackageSumResp struct {
	ItemSum              *UserPackageSum `protobuf:"bytes,1,opt,name=item_sum,json=itemSum,proto3" json:"item_sum,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetUserPackageSumResp) Reset()         { *m = GetUserPackageSumResp{} }
func (m *GetUserPackageSumResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPackageSumResp) ProtoMessage()    {}
func (*GetUserPackageSumResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{61}
}
func (m *GetUserPackageSumResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPackageSumResp.Unmarshal(m, b)
}
func (m *GetUserPackageSumResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPackageSumResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPackageSumResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPackageSumResp.Merge(dst, src)
}
func (m *GetUserPackageSumResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPackageSumResp.Size(m)
}
func (m *GetUserPackageSumResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPackageSumResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPackageSumResp proto.InternalMessageInfo

func (m *GetUserPackageSumResp) GetItemSum() *UserPackageSum {
	if m != nil {
		return m.ItemSum
	}
	return nil
}

type GetOrderCountByTimeRangeReq struct {
	BeginTime            uint32   `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	LogType              uint32   `protobuf:"varint,3,opt,name=log_type,json=logType,proto3" json:"log_type,omitempty"`
	Paras                string   `protobuf:"bytes,4,opt,name=paras,proto3" json:"paras,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderCountByTimeRangeReq) Reset()         { *m = GetOrderCountByTimeRangeReq{} }
func (m *GetOrderCountByTimeRangeReq) String() string { return proto.CompactTextString(m) }
func (*GetOrderCountByTimeRangeReq) ProtoMessage()    {}
func (*GetOrderCountByTimeRangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{62}
}
func (m *GetOrderCountByTimeRangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderCountByTimeRangeReq.Unmarshal(m, b)
}
func (m *GetOrderCountByTimeRangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderCountByTimeRangeReq.Marshal(b, m, deterministic)
}
func (dst *GetOrderCountByTimeRangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderCountByTimeRangeReq.Merge(dst, src)
}
func (m *GetOrderCountByTimeRangeReq) XXX_Size() int {
	return xxx_messageInfo_GetOrderCountByTimeRangeReq.Size(m)
}
func (m *GetOrderCountByTimeRangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderCountByTimeRangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderCountByTimeRangeReq proto.InternalMessageInfo

func (m *GetOrderCountByTimeRangeReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetOrderCountByTimeRangeReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetOrderCountByTimeRangeReq) GetLogType() uint32 {
	if m != nil {
		return m.LogType
	}
	return 0
}

func (m *GetOrderCountByTimeRangeReq) GetParas() string {
	if m != nil {
		return m.Paras
	}
	return ""
}

type GetOrderCountByTimeRangeResp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	UseCount             uint32   `protobuf:"varint,2,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	Value                uint32   `protobuf:"varint,3,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderCountByTimeRangeResp) Reset()         { *m = GetOrderCountByTimeRangeResp{} }
func (m *GetOrderCountByTimeRangeResp) String() string { return proto.CompactTextString(m) }
func (*GetOrderCountByTimeRangeResp) ProtoMessage()    {}
func (*GetOrderCountByTimeRangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{63}
}
func (m *GetOrderCountByTimeRangeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderCountByTimeRangeResp.Unmarshal(m, b)
}
func (m *GetOrderCountByTimeRangeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderCountByTimeRangeResp.Marshal(b, m, deterministic)
}
func (dst *GetOrderCountByTimeRangeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderCountByTimeRangeResp.Merge(dst, src)
}
func (m *GetOrderCountByTimeRangeResp) XXX_Size() int {
	return xxx_messageInfo_GetOrderCountByTimeRangeResp.Size(m)
}
func (m *GetOrderCountByTimeRangeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderCountByTimeRangeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderCountByTimeRangeResp proto.InternalMessageInfo

func (m *GetOrderCountByTimeRangeResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetOrderCountByTimeRangeResp) GetUseCount() uint32 {
	if m != nil {
		return m.UseCount
	}
	return 0
}

func (m *GetOrderCountByTimeRangeResp) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

type GetOrderListByTimeRangeReq struct {
	BeginTime            uint32   `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	LogType              uint32   `protobuf:"varint,3,opt,name=log_type,json=logType,proto3" json:"log_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderListByTimeRangeReq) Reset()         { *m = GetOrderListByTimeRangeReq{} }
func (m *GetOrderListByTimeRangeReq) String() string { return proto.CompactTextString(m) }
func (*GetOrderListByTimeRangeReq) ProtoMessage()    {}
func (*GetOrderListByTimeRangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{64}
}
func (m *GetOrderListByTimeRangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderListByTimeRangeReq.Unmarshal(m, b)
}
func (m *GetOrderListByTimeRangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderListByTimeRangeReq.Marshal(b, m, deterministic)
}
func (dst *GetOrderListByTimeRangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderListByTimeRangeReq.Merge(dst, src)
}
func (m *GetOrderListByTimeRangeReq) XXX_Size() int {
	return xxx_messageInfo_GetOrderListByTimeRangeReq.Size(m)
}
func (m *GetOrderListByTimeRangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderListByTimeRangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderListByTimeRangeReq proto.InternalMessageInfo

func (m *GetOrderListByTimeRangeReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetOrderListByTimeRangeReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetOrderListByTimeRangeReq) GetLogType() uint32 {
	if m != nil {
		return m.LogType
	}
	return 0
}

type GetOrderListByTimeRangeResp struct {
	OrderList            []string `protobuf:"bytes,1,rep,name=order_list,json=orderList,proto3" json:"order_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderListByTimeRangeResp) Reset()         { *m = GetOrderListByTimeRangeResp{} }
func (m *GetOrderListByTimeRangeResp) String() string { return proto.CompactTextString(m) }
func (*GetOrderListByTimeRangeResp) ProtoMessage()    {}
func (*GetOrderListByTimeRangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{65}
}
func (m *GetOrderListByTimeRangeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderListByTimeRangeResp.Unmarshal(m, b)
}
func (m *GetOrderListByTimeRangeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderListByTimeRangeResp.Marshal(b, m, deterministic)
}
func (dst *GetOrderListByTimeRangeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderListByTimeRangeResp.Merge(dst, src)
}
func (m *GetOrderListByTimeRangeResp) XXX_Size() int {
	return xxx_messageInfo_GetOrderListByTimeRangeResp.Size(m)
}
func (m *GetOrderListByTimeRangeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderListByTimeRangeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderListByTimeRangeResp proto.InternalMessageInfo

func (m *GetOrderListByTimeRangeResp) GetOrderList() []string {
	if m != nil {
		return m.OrderList
	}
	return nil
}

type ConversionItemReq struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OutsideTime          uint32         `protobuf:"varint,2,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
	BgId                 uint32         `protobuf:"varint,3,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	BgNum                uint32         `protobuf:"varint,4,opt,name=bg_num,json=bgNum,proto3" json:"bg_num,omitempty"`
	Source               uint32         `protobuf:"varint,5,opt,name=source,proto3" json:"source,omitempty"`
	MaterialPrice        uint32         `protobuf:"varint,6,opt,name=material_price,json=materialPrice,proto3" json:"material_price,omitempty"`
	ConversionPrice      uint32         `protobuf:"varint,7,opt,name=conversion_price,json=conversionPrice,proto3" json:"conversion_price,omitempty"`
	OrderId              string         `protobuf:"bytes,8,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	MaterialItemList     []*UseItemInfo `protobuf:"bytes,9,rep,name=material_item_list,json=materialItemList,proto3" json:"material_item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ConversionItemReq) Reset()         { *m = ConversionItemReq{} }
func (m *ConversionItemReq) String() string { return proto.CompactTextString(m) }
func (*ConversionItemReq) ProtoMessage()    {}
func (*ConversionItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{66}
}
func (m *ConversionItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConversionItemReq.Unmarshal(m, b)
}
func (m *ConversionItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConversionItemReq.Marshal(b, m, deterministic)
}
func (dst *ConversionItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConversionItemReq.Merge(dst, src)
}
func (m *ConversionItemReq) XXX_Size() int {
	return xxx_messageInfo_ConversionItemReq.Size(m)
}
func (m *ConversionItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConversionItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConversionItemReq proto.InternalMessageInfo

func (m *ConversionItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConversionItemReq) GetOutsideTime() uint32 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

func (m *ConversionItemReq) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *ConversionItemReq) GetBgNum() uint32 {
	if m != nil {
		return m.BgNum
	}
	return 0
}

func (m *ConversionItemReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *ConversionItemReq) GetMaterialPrice() uint32 {
	if m != nil {
		return m.MaterialPrice
	}
	return 0
}

func (m *ConversionItemReq) GetConversionPrice() uint32 {
	if m != nil {
		return m.ConversionPrice
	}
	return 0
}

func (m *ConversionItemReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *ConversionItemReq) GetMaterialItemList() []*UseItemInfo {
	if m != nil {
		return m.MaterialItemList
	}
	return nil
}

type ConversionItemResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConversionItemResp) Reset()         { *m = ConversionItemResp{} }
func (m *ConversionItemResp) String() string { return proto.CompactTextString(m) }
func (*ConversionItemResp) ProtoMessage()    {}
func (*ConversionItemResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{67}
}
func (m *ConversionItemResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConversionItemResp.Unmarshal(m, b)
}
func (m *ConversionItemResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConversionItemResp.Marshal(b, m, deterministic)
}
func (dst *ConversionItemResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConversionItemResp.Merge(dst, src)
}
func (m *ConversionItemResp) XXX_Size() int {
	return xxx_messageInfo_ConversionItemResp.Size(m)
}
func (m *ConversionItemResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConversionItemResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConversionItemResp proto.InternalMessageInfo

type RollBackUserItemReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CreateTime           uint32   `protobuf:"varint,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	OriginOrderId        string   `protobuf:"bytes,3,opt,name=origin_order_id,json=originOrderId,proto3" json:"origin_order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RollBackUserItemReq) Reset()         { *m = RollBackUserItemReq{} }
func (m *RollBackUserItemReq) String() string { return proto.CompactTextString(m) }
func (*RollBackUserItemReq) ProtoMessage()    {}
func (*RollBackUserItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{68}
}
func (m *RollBackUserItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RollBackUserItemReq.Unmarshal(m, b)
}
func (m *RollBackUserItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RollBackUserItemReq.Marshal(b, m, deterministic)
}
func (dst *RollBackUserItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RollBackUserItemReq.Merge(dst, src)
}
func (m *RollBackUserItemReq) XXX_Size() int {
	return xxx_messageInfo_RollBackUserItemReq.Size(m)
}
func (m *RollBackUserItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RollBackUserItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_RollBackUserItemReq proto.InternalMessageInfo

func (m *RollBackUserItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RollBackUserItemReq) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *RollBackUserItemReq) GetOriginOrderId() string {
	if m != nil {
		return m.OriginOrderId
	}
	return ""
}

type RollBackUserItemResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RollBackUserItemResp) Reset()         { *m = RollBackUserItemResp{} }
func (m *RollBackUserItemResp) String() string { return proto.CompactTextString(m) }
func (*RollBackUserItemResp) ProtoMessage()    {}
func (*RollBackUserItemResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{69}
}
func (m *RollBackUserItemResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RollBackUserItemResp.Unmarshal(m, b)
}
func (m *RollBackUserItemResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RollBackUserItemResp.Marshal(b, m, deterministic)
}
func (dst *RollBackUserItemResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RollBackUserItemResp.Merge(dst, src)
}
func (m *RollBackUserItemResp) XXX_Size() int {
	return xxx_messageInfo_RollBackUserItemResp.Size(m)
}
func (m *RollBackUserItemResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RollBackUserItemResp.DiscardUnknown(m)
}

var xxx_messageInfo_RollBackUserItemResp proto.InternalMessageInfo

type DeductItem struct {
	ItemType             uint32   `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	SourceId             uint32   `protobuf:"varint,2,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeductItem) Reset()         { *m = DeductItem{} }
func (m *DeductItem) String() string { return proto.CompactTextString(m) }
func (*DeductItem) ProtoMessage()    {}
func (*DeductItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{70}
}
func (m *DeductItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeductItem.Unmarshal(m, b)
}
func (m *DeductItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeductItem.Marshal(b, m, deterministic)
}
func (dst *DeductItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeductItem.Merge(dst, src)
}
func (m *DeductItem) XXX_Size() int {
	return xxx_messageInfo_DeductItem.Size(m)
}
func (m *DeductItem) XXX_DiscardUnknown() {
	xxx_messageInfo_DeductItem.DiscardUnknown(m)
}

var xxx_messageInfo_DeductItem proto.InternalMessageInfo

func (m *DeductItem) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *DeductItem) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *DeductItem) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type DeductDetail struct {
	Uid                  uint32        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemList             []*DeductItem `protobuf:"bytes,2,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	Count                uint32        `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	SourceType           uint32        `protobuf:"varint,4,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	IsAllSource          bool          `protobuf:"varint,5,opt,name=is_all_source,json=isAllSource,proto3" json:"is_all_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DeductDetail) Reset()         { *m = DeductDetail{} }
func (m *DeductDetail) String() string { return proto.CompactTextString(m) }
func (*DeductDetail) ProtoMessage()    {}
func (*DeductDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{71}
}
func (m *DeductDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeductDetail.Unmarshal(m, b)
}
func (m *DeductDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeductDetail.Marshal(b, m, deterministic)
}
func (dst *DeductDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeductDetail.Merge(dst, src)
}
func (m *DeductDetail) XXX_Size() int {
	return xxx_messageInfo_DeductDetail.Size(m)
}
func (m *DeductDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DeductDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DeductDetail proto.InternalMessageInfo

func (m *DeductDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DeductDetail) GetItemList() []*DeductItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *DeductDetail) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *DeductDetail) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *DeductDetail) GetIsAllSource() bool {
	if m != nil {
		return m.IsAllSource
	}
	return false
}

type BatchDeductUserItemReq struct {
	DeductList           []*DeductDetail `protobuf:"bytes,1,rep,name=deduct_list,json=deductList,proto3" json:"deduct_list,omitempty"`
	Oper                 string          `protobuf:"bytes,2,opt,name=oper,proto3" json:"oper,omitempty"`
	OrderId              string          `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	DeductType           uint32          `protobuf:"varint,4,opt,name=deduct_type,json=deductType,proto3" json:"deduct_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchDeductUserItemReq) Reset()         { *m = BatchDeductUserItemReq{} }
func (m *BatchDeductUserItemReq) String() string { return proto.CompactTextString(m) }
func (*BatchDeductUserItemReq) ProtoMessage()    {}
func (*BatchDeductUserItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{72}
}
func (m *BatchDeductUserItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDeductUserItemReq.Unmarshal(m, b)
}
func (m *BatchDeductUserItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDeductUserItemReq.Marshal(b, m, deterministic)
}
func (dst *BatchDeductUserItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDeductUserItemReq.Merge(dst, src)
}
func (m *BatchDeductUserItemReq) XXX_Size() int {
	return xxx_messageInfo_BatchDeductUserItemReq.Size(m)
}
func (m *BatchDeductUserItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDeductUserItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDeductUserItemReq proto.InternalMessageInfo

func (m *BatchDeductUserItemReq) GetDeductList() []*DeductDetail {
	if m != nil {
		return m.DeductList
	}
	return nil
}

func (m *BatchDeductUserItemReq) GetOper() string {
	if m != nil {
		return m.Oper
	}
	return ""
}

func (m *BatchDeductUserItemReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *BatchDeductUserItemReq) GetDeductType() uint32 {
	if m != nil {
		return m.DeductType
	}
	return 0
}

type DeductResult struct {
	Uid                  uint32        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SuccessItemList      []*DeductItem `protobuf:"bytes,2,rep,name=success_item_list,json=successItemList,proto3" json:"success_item_list,omitempty"`
	FailItemList         []*DeductItem `protobuf:"bytes,3,rep,name=fail_item_list,json=failItemList,proto3" json:"fail_item_list,omitempty"`
	FailType             uint32        `protobuf:"varint,4,opt,name=fail_type,json=failType,proto3" json:"fail_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DeductResult) Reset()         { *m = DeductResult{} }
func (m *DeductResult) String() string { return proto.CompactTextString(m) }
func (*DeductResult) ProtoMessage()    {}
func (*DeductResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{73}
}
func (m *DeductResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeductResult.Unmarshal(m, b)
}
func (m *DeductResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeductResult.Marshal(b, m, deterministic)
}
func (dst *DeductResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeductResult.Merge(dst, src)
}
func (m *DeductResult) XXX_Size() int {
	return xxx_messageInfo_DeductResult.Size(m)
}
func (m *DeductResult) XXX_DiscardUnknown() {
	xxx_messageInfo_DeductResult.DiscardUnknown(m)
}

var xxx_messageInfo_DeductResult proto.InternalMessageInfo

func (m *DeductResult) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DeductResult) GetSuccessItemList() []*DeductItem {
	if m != nil {
		return m.SuccessItemList
	}
	return nil
}

func (m *DeductResult) GetFailItemList() []*DeductItem {
	if m != nil {
		return m.FailItemList
	}
	return nil
}

func (m *DeductResult) GetFailType() uint32 {
	if m != nil {
		return m.FailType
	}
	return 0
}

type BatchDeductUserItemResp struct {
	DeductList           []*DeductResult `protobuf:"bytes,1,rep,name=deduct_list,json=deductList,proto3" json:"deduct_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchDeductUserItemResp) Reset()         { *m = BatchDeductUserItemResp{} }
func (m *BatchDeductUserItemResp) String() string { return proto.CompactTextString(m) }
func (*BatchDeductUserItemResp) ProtoMessage()    {}
func (*BatchDeductUserItemResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{74}
}
func (m *BatchDeductUserItemResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDeductUserItemResp.Unmarshal(m, b)
}
func (m *BatchDeductUserItemResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDeductUserItemResp.Marshal(b, m, deterministic)
}
func (dst *BatchDeductUserItemResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDeductUserItemResp.Merge(dst, src)
}
func (m *BatchDeductUserItemResp) XXX_Size() int {
	return xxx_messageInfo_BatchDeductUserItemResp.Size(m)
}
func (m *BatchDeductUserItemResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDeductUserItemResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDeductUserItemResp proto.InternalMessageInfo

func (m *BatchDeductUserItemResp) GetDeductList() []*DeductResult {
	if m != nil {
		return m.DeductList
	}
	return nil
}

type UseOrderDetail struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	SourceId             uint32   `protobuf:"varint,3,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	ItemType             uint32   `protobuf:"varint,4,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	UseCount             uint32   `protobuf:"varint,5,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	PriceType            uint32   `protobuf:"varint,6,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	CreateTime           uint32   `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UserItemId           uint32   `protobuf:"varint,8,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UseOrderDetail) Reset()         { *m = UseOrderDetail{} }
func (m *UseOrderDetail) String() string { return proto.CompactTextString(m) }
func (*UseOrderDetail) ProtoMessage()    {}
func (*UseOrderDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{75}
}
func (m *UseOrderDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UseOrderDetail.Unmarshal(m, b)
}
func (m *UseOrderDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UseOrderDetail.Marshal(b, m, deterministic)
}
func (dst *UseOrderDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UseOrderDetail.Merge(dst, src)
}
func (m *UseOrderDetail) XXX_Size() int {
	return xxx_messageInfo_UseOrderDetail.Size(m)
}
func (m *UseOrderDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_UseOrderDetail.DiscardUnknown(m)
}

var xxx_messageInfo_UseOrderDetail proto.InternalMessageInfo

func (m *UseOrderDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UseOrderDetail) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *UseOrderDetail) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *UseOrderDetail) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *UseOrderDetail) GetUseCount() uint32 {
	if m != nil {
		return m.UseCount
	}
	return 0
}

func (m *UseOrderDetail) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *UseOrderDetail) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *UseOrderDetail) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

// 获得订单数据
type TimeRangeReq struct {
	BeginTime            int64    `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Params               string   `protobuf:"bytes,3,opt,name=params,proto3" json:"params,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TimeRangeReq) Reset()         { *m = TimeRangeReq{} }
func (m *TimeRangeReq) String() string { return proto.CompactTextString(m) }
func (*TimeRangeReq) ProtoMessage()    {}
func (*TimeRangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{76}
}
func (m *TimeRangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeRangeReq.Unmarshal(m, b)
}
func (m *TimeRangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeRangeReq.Marshal(b, m, deterministic)
}
func (dst *TimeRangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeRangeReq.Merge(dst, src)
}
func (m *TimeRangeReq) XXX_Size() int {
	return xxx_messageInfo_TimeRangeReq.Size(m)
}
func (m *TimeRangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeRangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_TimeRangeReq proto.InternalMessageInfo

func (m *TimeRangeReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *TimeRangeReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *TimeRangeReq) GetParams() string {
	if m != nil {
		return m.Params
	}
	return ""
}

// 响应order_id个数
type CountResp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Value                uint32   `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CountResp) Reset()         { *m = CountResp{} }
func (m *CountResp) String() string { return proto.CompactTextString(m) }
func (*CountResp) ProtoMessage()    {}
func (*CountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{77}
}
func (m *CountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CountResp.Unmarshal(m, b)
}
func (m *CountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CountResp.Marshal(b, m, deterministic)
}
func (dst *CountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CountResp.Merge(dst, src)
}
func (m *CountResp) XXX_Size() int {
	return xxx_messageInfo_CountResp.Size(m)
}
func (m *CountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CountResp.DiscardUnknown(m)
}

var xxx_messageInfo_CountResp proto.InternalMessageInfo

func (m *CountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *CountResp) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

// 响应orderId详情
type OrderIdsResp struct {
	OrderIds             []string `protobuf:"bytes,1,rep,name=order_ids,json=orderIds,proto3" json:"order_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderIdsResp) Reset()         { *m = OrderIdsResp{} }
func (m *OrderIdsResp) String() string { return proto.CompactTextString(m) }
func (*OrderIdsResp) ProtoMessage()    {}
func (*OrderIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{78}
}
func (m *OrderIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderIdsResp.Unmarshal(m, b)
}
func (m *OrderIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderIdsResp.Marshal(b, m, deterministic)
}
func (dst *OrderIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderIdsResp.Merge(dst, src)
}
func (m *OrderIdsResp) XXX_Size() int {
	return xxx_messageInfo_OrderIdsResp.Size(m)
}
func (m *OrderIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_OrderIdsResp proto.InternalMessageInfo

func (m *OrderIdsResp) GetOrderIds() []string {
	if m != nil {
		return m.OrderIds
	}
	return nil
}

type CleanUserBackpackCacheReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CleanUserBackpackCacheReq) Reset()         { *m = CleanUserBackpackCacheReq{} }
func (m *CleanUserBackpackCacheReq) String() string { return proto.CompactTextString(m) }
func (*CleanUserBackpackCacheReq) ProtoMessage()    {}
func (*CleanUserBackpackCacheReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{79}
}
func (m *CleanUserBackpackCacheReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CleanUserBackpackCacheReq.Unmarshal(m, b)
}
func (m *CleanUserBackpackCacheReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CleanUserBackpackCacheReq.Marshal(b, m, deterministic)
}
func (dst *CleanUserBackpackCacheReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CleanUserBackpackCacheReq.Merge(dst, src)
}
func (m *CleanUserBackpackCacheReq) XXX_Size() int {
	return xxx_messageInfo_CleanUserBackpackCacheReq.Size(m)
}
func (m *CleanUserBackpackCacheReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CleanUserBackpackCacheReq.DiscardUnknown(m)
}

var xxx_messageInfo_CleanUserBackpackCacheReq proto.InternalMessageInfo

func (m *CleanUserBackpackCacheReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CleanUserBackpackCacheRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CleanUserBackpackCacheRsp) Reset()         { *m = CleanUserBackpackCacheRsp{} }
func (m *CleanUserBackpackCacheRsp) String() string { return proto.CompactTextString(m) }
func (*CleanUserBackpackCacheRsp) ProtoMessage()    {}
func (*CleanUserBackpackCacheRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_backpack_base_70ca9199dce9ad38, []int{80}
}
func (m *CleanUserBackpackCacheRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CleanUserBackpackCacheRsp.Unmarshal(m, b)
}
func (m *CleanUserBackpackCacheRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CleanUserBackpackCacheRsp.Marshal(b, m, deterministic)
}
func (dst *CleanUserBackpackCacheRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CleanUserBackpackCacheRsp.Merge(dst, src)
}
func (m *CleanUserBackpackCacheRsp) XXX_Size() int {
	return xxx_messageInfo_CleanUserBackpackCacheRsp.Size(m)
}
func (m *CleanUserBackpackCacheRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_CleanUserBackpackCacheRsp.DiscardUnknown(m)
}

var xxx_messageInfo_CleanUserBackpackCacheRsp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*PackageCfg)(nil), "backpack_base.PackageCfg")
	proto.RegisterType((*PackageItemCfg)(nil), "backpack_base.PackageItemCfg")
	proto.RegisterType((*LotteryFragmentCfg)(nil), "backpack_base.LotteryFragmentCfg")
	proto.RegisterType((*UserBackpackItem)(nil), "backpack_base.UserBackpackItem")
	proto.RegisterType((*UserBackpackLog)(nil), "backpack_base.UserBackpackLog")
	proto.RegisterType((*AddPackageCfgReq)(nil), "backpack_base.AddPackageCfgReq")
	proto.RegisterType((*AddPackageCfgResp)(nil), "backpack_base.AddPackageCfgResp")
	proto.RegisterType((*AddPackageAndItemCfgReq)(nil), "backpack_base.AddPackageAndItemCfgReq")
	proto.RegisterType((*AddPackageAndItemCfgResp)(nil), "backpack_base.AddPackageAndItemCfgResp")
	proto.RegisterType((*DelPackageCfgReq)(nil), "backpack_base.DelPackageCfgReq")
	proto.RegisterType((*DelPackageCfgResp)(nil), "backpack_base.DelPackageCfgResp")
	proto.RegisterType((*GetPackageCfgReq)(nil), "backpack_base.GetPackageCfgReq")
	proto.RegisterType((*GetPackageCfgResp)(nil), "backpack_base.GetPackageCfgResp")
	proto.RegisterType((*AddPackageItemCfgReq)(nil), "backpack_base.AddPackageItemCfgReq")
	proto.RegisterType((*AddPackageItemCfgResp)(nil), "backpack_base.AddPackageItemCfgResp")
	proto.RegisterType((*ModPackageItemCfgReq)(nil), "backpack_base.ModPackageItemCfgReq")
	proto.RegisterType((*ModPackageItemCfgResp)(nil), "backpack_base.ModPackageItemCfgResp")
	proto.RegisterType((*DelPackageItemCfgReq)(nil), "backpack_base.DelPackageItemCfgReq")
	proto.RegisterType((*DelPackageItemCfgResp)(nil), "backpack_base.DelPackageItemCfgResp")
	proto.RegisterType((*GetPackageItemCfgReq)(nil), "backpack_base.GetPackageItemCfgReq")
	proto.RegisterType((*PackageItemCfgList)(nil), "backpack_base.PackageItemCfgList")
	proto.RegisterType((*GetPackageItemCfgResp)(nil), "backpack_base.GetPackageItemCfgResp")
	proto.RegisterType((*ItemWeightCfg)(nil), "backpack_base.ItemWeightCfg")
	proto.RegisterType((*GetItemWeightCfgReq)(nil), "backpack_base.GetItemWeightCfgReq")
	proto.RegisterType((*GetItemWeightCfgRsp)(nil), "backpack_base.GetItemWeightCfgRsp")
	proto.RegisterType((*AddItemWeightCfgReq)(nil), "backpack_base.AddItemWeightCfgReq")
	proto.RegisterType((*AddItemWeightCfgRsp)(nil), "backpack_base.AddItemWeightCfgRsp")
	proto.RegisterType((*GiveUserPackageReq)(nil), "backpack_base.GiveUserPackageReq")
	proto.RegisterType((*GiveUserPackageResp)(nil), "backpack_base.GiveUserPackageResp")
	proto.RegisterType((*UseBackpackExtraInfo)(nil), "backpack_base.UseBackpackExtraInfo")
	proto.RegisterType((*UseBackpackItemReq)(nil), "backpack_base.UseBackpackItemReq")
	proto.RegisterType((*UseBackpackItemResp)(nil), "backpack_base.UseBackpackItemResp")
	proto.RegisterType((*BaseItemInfo)(nil), "backpack_base.BaseItemInfo")
	proto.RegisterType((*BatchUseBackpackItemReq)(nil), "backpack_base.BatchUseBackpackItemReq")
	proto.RegisterType((*BatchUseBackpackItemResp)(nil), "backpack_base.BatchUseBackpackItemResp")
	proto.RegisterType((*CheckUseItemSuccByMainOrderReq)(nil), "backpack_base.CheckUseItemSuccByMainOrderReq")
	proto.RegisterType((*CheckUseItemSuccByMainOrderResp)(nil), "backpack_base.CheckUseItemSuccByMainOrderResp")
	proto.RegisterType((*GetUseItemOrderInfoReq)(nil), "backpack_base.GetUseItemOrderInfoReq")
	proto.RegisterType((*GetUseItemOrderInfoResp)(nil), "backpack_base.GetUseItemOrderInfoResp")
	proto.RegisterType((*ProcBackpackItemTimeoutReq)(nil), "backpack_base.ProcBackpackItemTimeoutReq")
	proto.RegisterType((*ProcBackpackItemTimeoutResp)(nil), "backpack_base.ProcBackpackItemTimeoutResp")
	proto.RegisterType((*GetUserBackpackReq)(nil), "backpack_base.GetUserBackpackReq")
	proto.RegisterType((*GetUserBackpackResp)(nil), "backpack_base.GetUserBackpackResp")
	proto.RegisterType((*GetUserBackpackByItemReq)(nil), "backpack_base.GetUserBackpackByItemReq")
	proto.RegisterType((*GetUserBackpackByItemResp)(nil), "backpack_base.GetUserBackpackByItemResp")
	proto.RegisterType((*GetUserBackpackLogReq)(nil), "backpack_base.GetUserBackpackLogReq")
	proto.RegisterType((*GetUserBackpackLogResp)(nil), "backpack_base.GetUserBackpackLogResp")
	proto.RegisterType((*AddItemCfgReq)(nil), "backpack_base.AddItemCfgReq")
	proto.RegisterType((*AddItemCfgResp)(nil), "backpack_base.AddItemCfgResp")
	proto.RegisterType((*DelItemCfgReq)(nil), "backpack_base.DelItemCfgReq")
	proto.RegisterType((*DelItemCfgResp)(nil), "backpack_base.DelItemCfgResp")
	proto.RegisterType((*GetItemCfgReq)(nil), "backpack_base.GetItemCfgReq")
	proto.RegisterType((*GetItemCfgResp)(nil), "backpack_base.GetItemCfgResp")
	proto.RegisterType((*UseItemInfo)(nil), "backpack_base.UseItemInfo")
	proto.RegisterType((*TransactionInfo)(nil), "backpack_base.TransactionInfo")
	proto.RegisterType((*FreeZeItemReq)(nil), "backpack_base.FreeZeItemReq")
	proto.RegisterType((*FreeZeItemResp)(nil), "backpack_base.FreeZeItemResp")
	proto.RegisterType((*FreeZeItemV2Req)(nil), "backpack_base.FreeZeItemV2Req")
	proto.RegisterType((*FreeZeItemV2Resp)(nil), "backpack_base.FreeZeItemV2Resp")
	proto.RegisterType((*UserPackageSum)(nil), "backpack_base.UserPackageSum")
	proto.RegisterType((*GetUserPackageSumReq)(nil), "backpack_base.GetUserPackageSumReq")
	proto.RegisterType((*GetUserPackageSumResp)(nil), "backpack_base.GetUserPackageSumResp")
	proto.RegisterType((*GetOrderCountByTimeRangeReq)(nil), "backpack_base.GetOrderCountByTimeRangeReq")
	proto.RegisterType((*GetOrderCountByTimeRangeResp)(nil), "backpack_base.GetOrderCountByTimeRangeResp")
	proto.RegisterType((*GetOrderListByTimeRangeReq)(nil), "backpack_base.GetOrderListByTimeRangeReq")
	proto.RegisterType((*GetOrderListByTimeRangeResp)(nil), "backpack_base.GetOrderListByTimeRangeResp")
	proto.RegisterType((*ConversionItemReq)(nil), "backpack_base.ConversionItemReq")
	proto.RegisterType((*ConversionItemResp)(nil), "backpack_base.ConversionItemResp")
	proto.RegisterType((*RollBackUserItemReq)(nil), "backpack_base.RollBackUserItemReq")
	proto.RegisterType((*RollBackUserItemResp)(nil), "backpack_base.RollBackUserItemResp")
	proto.RegisterType((*DeductItem)(nil), "backpack_base.DeductItem")
	proto.RegisterType((*DeductDetail)(nil), "backpack_base.DeductDetail")
	proto.RegisterType((*BatchDeductUserItemReq)(nil), "backpack_base.BatchDeductUserItemReq")
	proto.RegisterType((*DeductResult)(nil), "backpack_base.DeductResult")
	proto.RegisterType((*BatchDeductUserItemResp)(nil), "backpack_base.BatchDeductUserItemResp")
	proto.RegisterType((*UseOrderDetail)(nil), "backpack_base.UseOrderDetail")
	proto.RegisterType((*TimeRangeReq)(nil), "backpack_base.TimeRangeReq")
	proto.RegisterType((*CountResp)(nil), "backpack_base.CountResp")
	proto.RegisterType((*OrderIdsResp)(nil), "backpack_base.OrderIdsResp")
	proto.RegisterType((*CleanUserBackpackCacheReq)(nil), "backpack_base.CleanUserBackpackCacheReq")
	proto.RegisterType((*CleanUserBackpackCacheRsp)(nil), "backpack_base.CleanUserBackpackCacheRsp")
	proto.RegisterEnum("backpack_base.LogType", LogType_name, LogType_value)
	proto.RegisterEnum("backpack_base.PackageItemType", PackageItemType_name, PackageItemType_value)
	proto.RegisterEnum("backpack_base.PackageSourceType", PackageSourceType_name, PackageSourceType_value)
	proto.RegisterEnum("backpack_base.PresentBusinessType", PresentBusinessType_name, PresentBusinessType_value)
	proto.RegisterEnum("backpack_base.FREEZETYPE", FREEZETYPE_name, FREEZETYPE_value)
	proto.RegisterEnum("backpack_base.DeductType", DeductType_name, DeductType_value)
	proto.RegisterEnum("backpack_base.DeductFailType", DeductFailType_name, DeductFailType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// BackpackBaseServiceClient is the client API for BackpackBaseService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type BackpackBaseServiceClient interface {
	// 包裹、包裹物品配置的增、删、改、查
	AddPackageCfg(ctx context.Context, in *AddPackageCfgReq, opts ...grpc.CallOption) (*AddPackageCfgResp, error)
	GetPackageCfg(ctx context.Context, in *GetPackageCfgReq, opts ...grpc.CallOption) (*GetPackageCfgResp, error)
	DelPackageCfg(ctx context.Context, in *DelPackageCfgReq, opts ...grpc.CallOption) (*DelPackageCfgResp, error)
	AddPackageItemCfg(ctx context.Context, in *AddPackageItemCfgReq, opts ...grpc.CallOption) (*AddPackageItemCfgResp, error)
	AddPackageAndItemCfg(ctx context.Context, in *AddPackageAndItemCfgReq, opts ...grpc.CallOption) (*AddPackageAndItemCfgResp, error)
	GetPackageItemCfg(ctx context.Context, in *GetPackageItemCfgReq, opts ...grpc.CallOption) (*GetPackageItemCfgResp, error)
	ModPackageItemCfg(ctx context.Context, in *ModPackageItemCfgReq, opts ...grpc.CallOption) (*ModPackageItemCfgResp, error)
	DelPackageItemCfg(ctx context.Context, in *DelPackageItemCfgReq, opts ...grpc.CallOption) (*DelPackageItemCfgResp, error)
	// 获取、添加背包物品展示权重配置
	GetItemWeightCfg(ctx context.Context, in *GetItemWeightCfgReq, opts ...grpc.CallOption) (*GetItemWeightCfgRsp, error)
	AddItemWeightCfg(ctx context.Context, in *AddItemWeightCfgReq, opts ...grpc.CallOption) (*AddItemWeightCfgRsp, error)
	// 碎片配置
	AddItemCfg(ctx context.Context, in *AddItemCfgReq, opts ...grpc.CallOption) (*AddItemCfgResp, error)
	DelItemCfg(ctx context.Context, in *DelItemCfgReq, opts ...grpc.CallOption) (*DelItemCfgResp, error)
	GetItemCfg(ctx context.Context, in *GetItemCfgReq, opts ...grpc.CallOption) (*GetItemCfgResp, error)
	// 包裹发放协议
	GiveUserPackage(ctx context.Context, in *GiveUserPackageReq, opts ...grpc.CallOption) (*GiveUserPackageResp, error)
	// 背包物品使用
	UseBackpackItem(ctx context.Context, in *UseBackpackItemReq, opts ...grpc.CallOption) (*UseBackpackItemResp, error)
	// 批量物品使用
	BatchUseBackpackItem(ctx context.Context, in *BatchUseBackpackItemReq, opts ...grpc.CallOption) (*BatchUseBackpackItemResp, error)
	// 检查确认某个物品使用订单是否成功
	CheckUseItemSuccByMainOrder(ctx context.Context, in *CheckUseItemSuccByMainOrderReq, opts ...grpc.CallOption) (*CheckUseItemSuccByMainOrderResp, error)
	// 查询用户所有背包物品列表
	GetUserBackpack(ctx context.Context, in *GetUserBackpackReq, opts ...grpc.CallOption) (*GetUserBackpackResp, error)
	// 按物品类型和ID查询用户背包
	GetUserBackpackByItem(ctx context.Context, in *GetUserBackpackByItemReq, opts ...grpc.CallOption) (*GetUserBackpackByItemResp, error)
	ProcBackpackItemTimeout(ctx context.Context, in *ProcBackpackItemTimeoutReq, opts ...grpc.CallOption) (*ProcBackpackItemTimeoutResp, error)
	// 二阶段消耗碎片
	FreeZeItem(ctx context.Context, in *FreeZeItemReq, opts ...grpc.CallOption) (*FreeZeItemResp, error)
	FreeZeItemV2(ctx context.Context, in *FreeZeItemV2Req, opts ...grpc.CallOption) (*FreeZeItemV2Resp, error)
	GetUseItemOrderInfo(ctx context.Context, in *GetUseItemOrderInfoReq, opts ...grpc.CallOption) (*GetUseItemOrderInfoResp, error)
	GetUserBackpackLog(ctx context.Context, in *GetUserBackpackLogReq, opts ...grpc.CallOption) (*GetUserBackpackLogResp, error)
	GetUserPackageSum(ctx context.Context, in *GetUserPackageSumReq, opts ...grpc.CallOption) (*GetUserPackageSumResp, error)
	GetOrderCountByTimeRange(ctx context.Context, in *GetOrderCountByTimeRangeReq, opts ...grpc.CallOption) (*GetOrderCountByTimeRangeResp, error)
	GetOrderListByTimeRange(ctx context.Context, in *GetOrderListByTimeRangeReq, opts ...grpc.CallOption) (*GetOrderListByTimeRangeResp, error)
	// 合成物品
	ConversionItem(ctx context.Context, in *ConversionItemReq, opts ...grpc.CallOption) (*ConversionItemResp, error)
	// 回滚物品
	RollBackUserItem(ctx context.Context, in *RollBackUserItemReq, opts ...grpc.CallOption) (*RollBackUserItemResp, error)
	// 批量扣减
	BatchDeductUserItem(ctx context.Context, in *BatchDeductUserItemReq, opts ...grpc.CallOption) (*BatchDeductUserItemResp, error)
	GetTimeRangeOrderData(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*CountResp, error)
	GetTimeRangeUseOrderData(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*CountResp, error)
	GetTimeRangeOrderList(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*OrderIdsResp, error)
	CleanUserBackpackCache(ctx context.Context, in *CleanUserBackpackCacheReq, opts ...grpc.CallOption) (*CleanUserBackpackCacheRsp, error)
}

type backpackBaseServiceClient struct {
	cc *grpc.ClientConn
}

func NewBackpackBaseServiceClient(cc *grpc.ClientConn) BackpackBaseServiceClient {
	return &backpackBaseServiceClient{cc}
}

func (c *backpackBaseServiceClient) AddPackageCfg(ctx context.Context, in *AddPackageCfgReq, opts ...grpc.CallOption) (*AddPackageCfgResp, error) {
	out := new(AddPackageCfgResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/AddPackageCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) GetPackageCfg(ctx context.Context, in *GetPackageCfgReq, opts ...grpc.CallOption) (*GetPackageCfgResp, error) {
	out := new(GetPackageCfgResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/GetPackageCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) DelPackageCfg(ctx context.Context, in *DelPackageCfgReq, opts ...grpc.CallOption) (*DelPackageCfgResp, error) {
	out := new(DelPackageCfgResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/DelPackageCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) AddPackageItemCfg(ctx context.Context, in *AddPackageItemCfgReq, opts ...grpc.CallOption) (*AddPackageItemCfgResp, error) {
	out := new(AddPackageItemCfgResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/AddPackageItemCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) AddPackageAndItemCfg(ctx context.Context, in *AddPackageAndItemCfgReq, opts ...grpc.CallOption) (*AddPackageAndItemCfgResp, error) {
	out := new(AddPackageAndItemCfgResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/AddPackageAndItemCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) GetPackageItemCfg(ctx context.Context, in *GetPackageItemCfgReq, opts ...grpc.CallOption) (*GetPackageItemCfgResp, error) {
	out := new(GetPackageItemCfgResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/GetPackageItemCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) ModPackageItemCfg(ctx context.Context, in *ModPackageItemCfgReq, opts ...grpc.CallOption) (*ModPackageItemCfgResp, error) {
	out := new(ModPackageItemCfgResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/ModPackageItemCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) DelPackageItemCfg(ctx context.Context, in *DelPackageItemCfgReq, opts ...grpc.CallOption) (*DelPackageItemCfgResp, error) {
	out := new(DelPackageItemCfgResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/DelPackageItemCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) GetItemWeightCfg(ctx context.Context, in *GetItemWeightCfgReq, opts ...grpc.CallOption) (*GetItemWeightCfgRsp, error) {
	out := new(GetItemWeightCfgRsp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/GetItemWeightCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) AddItemWeightCfg(ctx context.Context, in *AddItemWeightCfgReq, opts ...grpc.CallOption) (*AddItemWeightCfgRsp, error) {
	out := new(AddItemWeightCfgRsp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/AddItemWeightCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) AddItemCfg(ctx context.Context, in *AddItemCfgReq, opts ...grpc.CallOption) (*AddItemCfgResp, error) {
	out := new(AddItemCfgResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/AddItemCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) DelItemCfg(ctx context.Context, in *DelItemCfgReq, opts ...grpc.CallOption) (*DelItemCfgResp, error) {
	out := new(DelItemCfgResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/DelItemCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) GetItemCfg(ctx context.Context, in *GetItemCfgReq, opts ...grpc.CallOption) (*GetItemCfgResp, error) {
	out := new(GetItemCfgResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/GetItemCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) GiveUserPackage(ctx context.Context, in *GiveUserPackageReq, opts ...grpc.CallOption) (*GiveUserPackageResp, error) {
	out := new(GiveUserPackageResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/GiveUserPackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) UseBackpackItem(ctx context.Context, in *UseBackpackItemReq, opts ...grpc.CallOption) (*UseBackpackItemResp, error) {
	out := new(UseBackpackItemResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/UseBackpackItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) BatchUseBackpackItem(ctx context.Context, in *BatchUseBackpackItemReq, opts ...grpc.CallOption) (*BatchUseBackpackItemResp, error) {
	out := new(BatchUseBackpackItemResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/BatchUseBackpackItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) CheckUseItemSuccByMainOrder(ctx context.Context, in *CheckUseItemSuccByMainOrderReq, opts ...grpc.CallOption) (*CheckUseItemSuccByMainOrderResp, error) {
	out := new(CheckUseItemSuccByMainOrderResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/CheckUseItemSuccByMainOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) GetUserBackpack(ctx context.Context, in *GetUserBackpackReq, opts ...grpc.CallOption) (*GetUserBackpackResp, error) {
	out := new(GetUserBackpackResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/GetUserBackpack", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) GetUserBackpackByItem(ctx context.Context, in *GetUserBackpackByItemReq, opts ...grpc.CallOption) (*GetUserBackpackByItemResp, error) {
	out := new(GetUserBackpackByItemResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/GetUserBackpackByItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) ProcBackpackItemTimeout(ctx context.Context, in *ProcBackpackItemTimeoutReq, opts ...grpc.CallOption) (*ProcBackpackItemTimeoutResp, error) {
	out := new(ProcBackpackItemTimeoutResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/ProcBackpackItemTimeout", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) FreeZeItem(ctx context.Context, in *FreeZeItemReq, opts ...grpc.CallOption) (*FreeZeItemResp, error) {
	out := new(FreeZeItemResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/FreeZeItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) FreeZeItemV2(ctx context.Context, in *FreeZeItemV2Req, opts ...grpc.CallOption) (*FreeZeItemV2Resp, error) {
	out := new(FreeZeItemV2Resp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/FreeZeItemV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) GetUseItemOrderInfo(ctx context.Context, in *GetUseItemOrderInfoReq, opts ...grpc.CallOption) (*GetUseItemOrderInfoResp, error) {
	out := new(GetUseItemOrderInfoResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/GetUseItemOrderInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) GetUserBackpackLog(ctx context.Context, in *GetUserBackpackLogReq, opts ...grpc.CallOption) (*GetUserBackpackLogResp, error) {
	out := new(GetUserBackpackLogResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/GetUserBackpackLog", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) GetUserPackageSum(ctx context.Context, in *GetUserPackageSumReq, opts ...grpc.CallOption) (*GetUserPackageSumResp, error) {
	out := new(GetUserPackageSumResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/GetUserPackageSum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) GetOrderCountByTimeRange(ctx context.Context, in *GetOrderCountByTimeRangeReq, opts ...grpc.CallOption) (*GetOrderCountByTimeRangeResp, error) {
	out := new(GetOrderCountByTimeRangeResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/GetOrderCountByTimeRange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) GetOrderListByTimeRange(ctx context.Context, in *GetOrderListByTimeRangeReq, opts ...grpc.CallOption) (*GetOrderListByTimeRangeResp, error) {
	out := new(GetOrderListByTimeRangeResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/GetOrderListByTimeRange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) ConversionItem(ctx context.Context, in *ConversionItemReq, opts ...grpc.CallOption) (*ConversionItemResp, error) {
	out := new(ConversionItemResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/ConversionItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) RollBackUserItem(ctx context.Context, in *RollBackUserItemReq, opts ...grpc.CallOption) (*RollBackUserItemResp, error) {
	out := new(RollBackUserItemResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/RollBackUserItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) BatchDeductUserItem(ctx context.Context, in *BatchDeductUserItemReq, opts ...grpc.CallOption) (*BatchDeductUserItemResp, error) {
	out := new(BatchDeductUserItemResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/BatchDeductUserItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) GetTimeRangeOrderData(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*CountResp, error) {
	out := new(CountResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/GetTimeRangeOrderData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) GetTimeRangeUseOrderData(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*CountResp, error) {
	out := new(CountResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/GetTimeRangeUseOrderData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) GetTimeRangeOrderList(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*OrderIdsResp, error) {
	out := new(OrderIdsResp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/GetTimeRangeOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackBaseServiceClient) CleanUserBackpackCache(ctx context.Context, in *CleanUserBackpackCacheReq, opts ...grpc.CallOption) (*CleanUserBackpackCacheRsp, error) {
	out := new(CleanUserBackpackCacheRsp)
	err := c.cc.Invoke(ctx, "/backpack_base.BackpackBaseService/CleanUserBackpackCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BackpackBaseServiceServer is the server API for BackpackBaseService service.
type BackpackBaseServiceServer interface {
	// 包裹、包裹物品配置的增、删、改、查
	AddPackageCfg(context.Context, *AddPackageCfgReq) (*AddPackageCfgResp, error)
	GetPackageCfg(context.Context, *GetPackageCfgReq) (*GetPackageCfgResp, error)
	DelPackageCfg(context.Context, *DelPackageCfgReq) (*DelPackageCfgResp, error)
	AddPackageItemCfg(context.Context, *AddPackageItemCfgReq) (*AddPackageItemCfgResp, error)
	AddPackageAndItemCfg(context.Context, *AddPackageAndItemCfgReq) (*AddPackageAndItemCfgResp, error)
	GetPackageItemCfg(context.Context, *GetPackageItemCfgReq) (*GetPackageItemCfgResp, error)
	ModPackageItemCfg(context.Context, *ModPackageItemCfgReq) (*ModPackageItemCfgResp, error)
	DelPackageItemCfg(context.Context, *DelPackageItemCfgReq) (*DelPackageItemCfgResp, error)
	// 获取、添加背包物品展示权重配置
	GetItemWeightCfg(context.Context, *GetItemWeightCfgReq) (*GetItemWeightCfgRsp, error)
	AddItemWeightCfg(context.Context, *AddItemWeightCfgReq) (*AddItemWeightCfgRsp, error)
	// 碎片配置
	AddItemCfg(context.Context, *AddItemCfgReq) (*AddItemCfgResp, error)
	DelItemCfg(context.Context, *DelItemCfgReq) (*DelItemCfgResp, error)
	GetItemCfg(context.Context, *GetItemCfgReq) (*GetItemCfgResp, error)
	// 包裹发放协议
	GiveUserPackage(context.Context, *GiveUserPackageReq) (*GiveUserPackageResp, error)
	// 背包物品使用
	UseBackpackItem(context.Context, *UseBackpackItemReq) (*UseBackpackItemResp, error)
	// 批量物品使用
	BatchUseBackpackItem(context.Context, *BatchUseBackpackItemReq) (*BatchUseBackpackItemResp, error)
	// 检查确认某个物品使用订单是否成功
	CheckUseItemSuccByMainOrder(context.Context, *CheckUseItemSuccByMainOrderReq) (*CheckUseItemSuccByMainOrderResp, error)
	// 查询用户所有背包物品列表
	GetUserBackpack(context.Context, *GetUserBackpackReq) (*GetUserBackpackResp, error)
	// 按物品类型和ID查询用户背包
	GetUserBackpackByItem(context.Context, *GetUserBackpackByItemReq) (*GetUserBackpackByItemResp, error)
	ProcBackpackItemTimeout(context.Context, *ProcBackpackItemTimeoutReq) (*ProcBackpackItemTimeoutResp, error)
	// 二阶段消耗碎片
	FreeZeItem(context.Context, *FreeZeItemReq) (*FreeZeItemResp, error)
	FreeZeItemV2(context.Context, *FreeZeItemV2Req) (*FreeZeItemV2Resp, error)
	GetUseItemOrderInfo(context.Context, *GetUseItemOrderInfoReq) (*GetUseItemOrderInfoResp, error)
	GetUserBackpackLog(context.Context, *GetUserBackpackLogReq) (*GetUserBackpackLogResp, error)
	GetUserPackageSum(context.Context, *GetUserPackageSumReq) (*GetUserPackageSumResp, error)
	GetOrderCountByTimeRange(context.Context, *GetOrderCountByTimeRangeReq) (*GetOrderCountByTimeRangeResp, error)
	GetOrderListByTimeRange(context.Context, *GetOrderListByTimeRangeReq) (*GetOrderListByTimeRangeResp, error)
	// 合成物品
	ConversionItem(context.Context, *ConversionItemReq) (*ConversionItemResp, error)
	// 回滚物品
	RollBackUserItem(context.Context, *RollBackUserItemReq) (*RollBackUserItemResp, error)
	// 批量扣减
	BatchDeductUserItem(context.Context, *BatchDeductUserItemReq) (*BatchDeductUserItemResp, error)
	GetTimeRangeOrderData(context.Context, *TimeRangeReq) (*CountResp, error)
	GetTimeRangeUseOrderData(context.Context, *TimeRangeReq) (*CountResp, error)
	GetTimeRangeOrderList(context.Context, *TimeRangeReq) (*OrderIdsResp, error)
	CleanUserBackpackCache(context.Context, *CleanUserBackpackCacheReq) (*CleanUserBackpackCacheRsp, error)
}

func RegisterBackpackBaseServiceServer(s *grpc.Server, srv BackpackBaseServiceServer) {
	s.RegisterService(&_BackpackBaseService_serviceDesc, srv)
}

func _BackpackBaseService_AddPackageCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPackageCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).AddPackageCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/AddPackageCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).AddPackageCfg(ctx, req.(*AddPackageCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_GetPackageCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPackageCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).GetPackageCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/GetPackageCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).GetPackageCfg(ctx, req.(*GetPackageCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_DelPackageCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPackageCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).DelPackageCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/DelPackageCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).DelPackageCfg(ctx, req.(*DelPackageCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_AddPackageItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPackageItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).AddPackageItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/AddPackageItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).AddPackageItemCfg(ctx, req.(*AddPackageItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_AddPackageAndItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPackageAndItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).AddPackageAndItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/AddPackageAndItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).AddPackageAndItemCfg(ctx, req.(*AddPackageAndItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_GetPackageItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPackageItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).GetPackageItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/GetPackageItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).GetPackageItemCfg(ctx, req.(*GetPackageItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_ModPackageItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModPackageItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).ModPackageItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/ModPackageItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).ModPackageItemCfg(ctx, req.(*ModPackageItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_DelPackageItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPackageItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).DelPackageItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/DelPackageItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).DelPackageItemCfg(ctx, req.(*DelPackageItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_GetItemWeightCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetItemWeightCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).GetItemWeightCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/GetItemWeightCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).GetItemWeightCfg(ctx, req.(*GetItemWeightCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_AddItemWeightCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddItemWeightCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).AddItemWeightCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/AddItemWeightCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).AddItemWeightCfg(ctx, req.(*AddItemWeightCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_AddItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).AddItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/AddItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).AddItemCfg(ctx, req.(*AddItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_DelItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).DelItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/DelItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).DelItemCfg(ctx, req.(*DelItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_GetItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).GetItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/GetItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).GetItemCfg(ctx, req.(*GetItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_GiveUserPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiveUserPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).GiveUserPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/GiveUserPackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).GiveUserPackage(ctx, req.(*GiveUserPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_UseBackpackItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UseBackpackItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).UseBackpackItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/UseBackpackItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).UseBackpackItem(ctx, req.(*UseBackpackItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_BatchUseBackpackItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUseBackpackItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).BatchUseBackpackItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/BatchUseBackpackItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).BatchUseBackpackItem(ctx, req.(*BatchUseBackpackItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_CheckUseItemSuccByMainOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUseItemSuccByMainOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).CheckUseItemSuccByMainOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/CheckUseItemSuccByMainOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).CheckUseItemSuccByMainOrder(ctx, req.(*CheckUseItemSuccByMainOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_GetUserBackpack_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBackpackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).GetUserBackpack(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/GetUserBackpack",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).GetUserBackpack(ctx, req.(*GetUserBackpackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_GetUserBackpackByItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBackpackByItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).GetUserBackpackByItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/GetUserBackpackByItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).GetUserBackpackByItem(ctx, req.(*GetUserBackpackByItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_ProcBackpackItemTimeout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcBackpackItemTimeoutReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).ProcBackpackItemTimeout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/ProcBackpackItemTimeout",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).ProcBackpackItemTimeout(ctx, req.(*ProcBackpackItemTimeoutReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_FreeZeItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreeZeItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).FreeZeItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/FreeZeItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).FreeZeItem(ctx, req.(*FreeZeItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_FreeZeItemV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreeZeItemV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).FreeZeItemV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/FreeZeItemV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).FreeZeItemV2(ctx, req.(*FreeZeItemV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_GetUseItemOrderInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUseItemOrderInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).GetUseItemOrderInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/GetUseItemOrderInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).GetUseItemOrderInfo(ctx, req.(*GetUseItemOrderInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_GetUserBackpackLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBackpackLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).GetUserBackpackLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/GetUserBackpackLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).GetUserBackpackLog(ctx, req.(*GetUserBackpackLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_GetUserPackageSum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPackageSumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).GetUserPackageSum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/GetUserPackageSum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).GetUserPackageSum(ctx, req.(*GetUserPackageSumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_GetOrderCountByTimeRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderCountByTimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).GetOrderCountByTimeRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/GetOrderCountByTimeRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).GetOrderCountByTimeRange(ctx, req.(*GetOrderCountByTimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_GetOrderListByTimeRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderListByTimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).GetOrderListByTimeRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/GetOrderListByTimeRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).GetOrderListByTimeRange(ctx, req.(*GetOrderListByTimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_ConversionItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConversionItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).ConversionItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/ConversionItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).ConversionItem(ctx, req.(*ConversionItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_RollBackUserItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RollBackUserItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).RollBackUserItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/RollBackUserItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).RollBackUserItem(ctx, req.(*RollBackUserItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_BatchDeductUserItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeductUserItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).BatchDeductUserItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/BatchDeductUserItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).BatchDeductUserItem(ctx, req.(*BatchDeductUserItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_GetTimeRangeOrderData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).GetTimeRangeOrderData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/GetTimeRangeOrderData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).GetTimeRangeOrderData(ctx, req.(*TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_GetTimeRangeUseOrderData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).GetTimeRangeUseOrderData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/GetTimeRangeUseOrderData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).GetTimeRangeUseOrderData(ctx, req.(*TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_GetTimeRangeOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).GetTimeRangeOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/GetTimeRangeOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).GetTimeRangeOrderList(ctx, req.(*TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackpackBaseService_CleanUserBackpackCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanUserBackpackCacheReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackBaseServiceServer).CleanUserBackpackCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack_base.BackpackBaseService/CleanUserBackpackCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackBaseServiceServer).CleanUserBackpackCache(ctx, req.(*CleanUserBackpackCacheReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _BackpackBaseService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "backpack_base.BackpackBaseService",
	HandlerType: (*BackpackBaseServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddPackageCfg",
			Handler:    _BackpackBaseService_AddPackageCfg_Handler,
		},
		{
			MethodName: "GetPackageCfg",
			Handler:    _BackpackBaseService_GetPackageCfg_Handler,
		},
		{
			MethodName: "DelPackageCfg",
			Handler:    _BackpackBaseService_DelPackageCfg_Handler,
		},
		{
			MethodName: "AddPackageItemCfg",
			Handler:    _BackpackBaseService_AddPackageItemCfg_Handler,
		},
		{
			MethodName: "AddPackageAndItemCfg",
			Handler:    _BackpackBaseService_AddPackageAndItemCfg_Handler,
		},
		{
			MethodName: "GetPackageItemCfg",
			Handler:    _BackpackBaseService_GetPackageItemCfg_Handler,
		},
		{
			MethodName: "ModPackageItemCfg",
			Handler:    _BackpackBaseService_ModPackageItemCfg_Handler,
		},
		{
			MethodName: "DelPackageItemCfg",
			Handler:    _BackpackBaseService_DelPackageItemCfg_Handler,
		},
		{
			MethodName: "GetItemWeightCfg",
			Handler:    _BackpackBaseService_GetItemWeightCfg_Handler,
		},
		{
			MethodName: "AddItemWeightCfg",
			Handler:    _BackpackBaseService_AddItemWeightCfg_Handler,
		},
		{
			MethodName: "AddItemCfg",
			Handler:    _BackpackBaseService_AddItemCfg_Handler,
		},
		{
			MethodName: "DelItemCfg",
			Handler:    _BackpackBaseService_DelItemCfg_Handler,
		},
		{
			MethodName: "GetItemCfg",
			Handler:    _BackpackBaseService_GetItemCfg_Handler,
		},
		{
			MethodName: "GiveUserPackage",
			Handler:    _BackpackBaseService_GiveUserPackage_Handler,
		},
		{
			MethodName: "UseBackpackItem",
			Handler:    _BackpackBaseService_UseBackpackItem_Handler,
		},
		{
			MethodName: "BatchUseBackpackItem",
			Handler:    _BackpackBaseService_BatchUseBackpackItem_Handler,
		},
		{
			MethodName: "CheckUseItemSuccByMainOrder",
			Handler:    _BackpackBaseService_CheckUseItemSuccByMainOrder_Handler,
		},
		{
			MethodName: "GetUserBackpack",
			Handler:    _BackpackBaseService_GetUserBackpack_Handler,
		},
		{
			MethodName: "GetUserBackpackByItem",
			Handler:    _BackpackBaseService_GetUserBackpackByItem_Handler,
		},
		{
			MethodName: "ProcBackpackItemTimeout",
			Handler:    _BackpackBaseService_ProcBackpackItemTimeout_Handler,
		},
		{
			MethodName: "FreeZeItem",
			Handler:    _BackpackBaseService_FreeZeItem_Handler,
		},
		{
			MethodName: "FreeZeItemV2",
			Handler:    _BackpackBaseService_FreeZeItemV2_Handler,
		},
		{
			MethodName: "GetUseItemOrderInfo",
			Handler:    _BackpackBaseService_GetUseItemOrderInfo_Handler,
		},
		{
			MethodName: "GetUserBackpackLog",
			Handler:    _BackpackBaseService_GetUserBackpackLog_Handler,
		},
		{
			MethodName: "GetUserPackageSum",
			Handler:    _BackpackBaseService_GetUserPackageSum_Handler,
		},
		{
			MethodName: "GetOrderCountByTimeRange",
			Handler:    _BackpackBaseService_GetOrderCountByTimeRange_Handler,
		},
		{
			MethodName: "GetOrderListByTimeRange",
			Handler:    _BackpackBaseService_GetOrderListByTimeRange_Handler,
		},
		{
			MethodName: "ConversionItem",
			Handler:    _BackpackBaseService_ConversionItem_Handler,
		},
		{
			MethodName: "RollBackUserItem",
			Handler:    _BackpackBaseService_RollBackUserItem_Handler,
		},
		{
			MethodName: "BatchDeductUserItem",
			Handler:    _BackpackBaseService_BatchDeductUserItem_Handler,
		},
		{
			MethodName: "GetTimeRangeOrderData",
			Handler:    _BackpackBaseService_GetTimeRangeOrderData_Handler,
		},
		{
			MethodName: "GetTimeRangeUseOrderData",
			Handler:    _BackpackBaseService_GetTimeRangeUseOrderData_Handler,
		},
		{
			MethodName: "GetTimeRangeOrderList",
			Handler:    _BackpackBaseService_GetTimeRangeOrderList_Handler,
		},
		{
			MethodName: "CleanUserBackpackCache",
			Handler:    _BackpackBaseService_CleanUserBackpackCache_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/backpack-base/backpack-base.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/backpack-base/backpack-base.proto", fileDescriptor_backpack_base_70ca9199dce9ad38)
}

var fileDescriptor_backpack_base_70ca9199dce9ad38 = []byte{
	// 4431 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x5b, 0xcd, 0x73, 0x23, 0x49,
	0x56, 0x1f, 0x49, 0xfe, 0x90, 0x9f, 0x2d, 0xb9, 0x9c, 0xfe, 0x92, 0xe5, 0x76, 0xdb, 0x5d, 0xee,
	0x0f, 0x8f, 0x7b, 0xba, 0x1b, 0x7a, 0x17, 0x96, 0x0d, 0x36, 0x62, 0xa7, 0x2c, 0x95, 0xe5, 0x8a,
	0x96, 0x25, 0x4f, 0xa9, 0xd4, 0x6e, 0x37, 0xc4, 0xd4, 0x96, 0xa5, 0xb4, 0x5c, 0xdb, 0xb2, 0x54,
	0xa3, 0x2c, 0xf5, 0x6c, 0x13, 0x9c, 0x38, 0x01, 0xa7, 0xe5, 0xc4, 0x95, 0x08, 0x22, 0x80, 0x13,
	0x10, 0x01, 0x87, 0xbd, 0x03, 0x17, 0x6e, 0x44, 0xec, 0x81, 0x03, 0xff, 0x04, 0x17, 0xce, 0x44,
	0x66, 0xd6, 0x67, 0x56, 0x49, 0x76, 0xcf, 0xf6, 0xce, 0x4d, 0xf5, 0xf2, 0xe5, 0xcb, 0x7c, 0xbf,
	0x7c, 0x2f, 0xdf, 0xcb, 0xcc, 0x27, 0xf8, 0x1d, 0xd7, 0x7d, 0xf1, 0xcd, 0xd8, 0xee, 0xbc, 0x23,
	0x76, 0xff, 0x3d, 0x1e, 0xbd, 0xb8, 0xb4, 0x3a, 0xef, 0x1c, 0xab, 0xf3, 0xee, 0xd9, 0xa5, 0x45,
	0x70, 0xfc, 0xeb, 0xb9, 0x33, 0x1a, 0xba, 0x43, 0x54, 0xf0, 0x89, 0x26, 0x25, 0xca, 0x3f, 0x03,
	0x38, 0xb3, 0x3a, 0xef, 0xac, 0x1e, 0xae, 0x5c, 0xf5, 0xd0, 0x2a, 0xcc, 0x5e, 0xf6, 0x4c, 0xbb,
	0x5b, 0xca, 0xec, 0x65, 0x0e, 0x0a, 0xfa, 0xcc, 0x65, 0x4f, 0xeb, 0x22, 0x04, 0x33, 0x03, 0xeb,
	0x06, 0x97, 0xb2, 0x7b, 0x99, 0x83, 0x05, 0x9d, 0xfd, 0xa6, 0xb4, 0x2e, 0x26, 0x9d, 0x52, 0x8e,
	0xd3, 0xe8, 0x6f, 0xb4, 0x0e, 0x73, 0x36, 0x31, 0xbb, 0xb8, 0x5f, 0x9a, 0xd9, 0xcb, 0x1c, 0xe4,
	0xf5, 0x59, 0x9b, 0x54, 0x71, 0x5f, 0xfe, 0x87, 0x2c, 0x14, 0xbd, 0x21, 0x34, 0x17, 0xdf, 0xd0,
	0x61, 0xee, 0x01, 0xd0, 0x61, 0x5c, 0x7c, 0x13, 0x8e, 0x95, 0xbf, 0xec, 0xd1, 0x66, 0xad, 0x1b,
	0x4e, 0x22, 0x1b, 0x99, 0xc4, 0x36, 0x2c, 0x30, 0x7e, 0xf7, 0x83, 0x83, 0xd9, 0xa8, 0x05, 0x3d,
	0x4f, 0x09, 0xc6, 0x07, 0x07, 0xd3, 0x46, 0x32, 0x1c, 0x8f, 0x3a, 0x98, 0xf6, 0x9a, 0xe1, 0x8d,
	0x9c, 0xa0, 0x75, 0xd1, 0x0e, 0x00, 0xeb, 0xd9, 0x19, 0x8e, 0x07, 0x6e, 0x69, 0x96, 0xb5, 0x32,
	0x59, 0x15, 0x4a, 0x40, 0x5b, 0x90, 0xbf, 0xb2, 0x07, 0xa6, 0x6b, 0xdf, 0xe0, 0xd2, 0x1c, 0x6b,
	0x9c, 0xbf, 0xb2, 0x07, 0x86, 0x7d, 0x83, 0x23, 0x0a, 0xcd, 0x47, 0x14, 0x42, 0x1b, 0x30, 0xf7,
	0x2d, 0xb6, 0x7b, 0xd7, 0x6e, 0x29, 0xcf, 0xf8, 0xbd, 0x2f, 0x74, 0x00, 0x52, 0xf7, 0xc3, 0xc0,
	0xba, 0xb1, 0x3b, 0x66, 0x20, 0x71, 0x81, 0x71, 0x14, 0x3d, 0xfa, 0xb1, 0x27, 0x78, 0x03, 0xe6,
	0x6e, 0x86, 0x03, 0xf7, 0x9a, 0x94, 0x80, 0x4b, 0xe0, 0x5f, 0xf2, 0xbf, 0x66, 0x01, 0xd5, 0x87,
	0xae, 0x8b, 0x47, 0x1f, 0x8e, 0x47, 0x56, 0xef, 0x06, 0x0f, 0x5c, 0x0a, 0xd7, 0x2e, 0x2c, 0x5e,
	0x79, 0x9f, 0x21, 0x5e, 0xe0, 0x93, 0xb4, 0x2e, 0xda, 0x87, 0x42, 0xc0, 0xc0, 0x00, 0xe2, 0xc8,
	0x2d, 0xf9, 0x44, 0x06, 0x52, 0x94, 0x89, 0xad, 0x27, 0x5f, 0xbb, 0x80, 0xa9, 0x41, 0xd7, 0x35,
	0xca, 0xc4, 0x16, 0x78, 0x26, 0xce, 0x54, 0xa5, 0x0b, 0xfd, 0x00, 0x82, 0x6f, 0x73, 0x3c, 0xea,
	0x33, 0x4c, 0x17, 0xf4, 0x60, 0x8e, 0xed, 0x51, 0x3f, 0x02, 0x1d, 0xc7, 0xd4, 0x83, 0xee, 0x11,
	0x14, 0x83, 0x9e, 0xce, 0xc8, 0xee, 0x60, 0x86, 0x6c, 0x41, 0x0f, 0x06, 0x3d, 0xa3, 0x44, 0xf4,
	0x0c, 0x56, 0x6d, 0x62, 0x92, 0xeb, 0xe1, 0xb7, 0x26, 0xfe, 0x85, 0x63, 0x8f, 0xb0, 0x79, 0x6d,
	0x0f, 0x7c, 0xb8, 0x25, 0x9b, 0xb4, 0xae, 0x87, 0xdf, 0xaa, 0xac, 0xe1, 0xc4, 0x1e, 0xb8, 0xf2,
	0x7f, 0x65, 0x41, 0x6a, 0x13, 0x3c, 0x3a, 0xf2, 0x2c, 0x9b, 0xda, 0x51, 0xdc, 0x60, 0x32, 0x82,
	0xc1, 0xec, 0xc1, 0xd2, 0x98, 0xe0, 0x51, 0x60, 0x82, 0x1c, 0x2f, 0xa0, 0x34, 0xcf, 0x08, 0xe3,
	0x56, 0x93, 0x9b, 0x66, 0x35, 0x33, 0x71, 0xab, 0x89, 0x19, 0xe3, 0xac, 0x60, 0x8c, 0xa1, 0xed,
	0xcc, 0xc5, 0x6c, 0x67, 0x17, 0x16, 0x87, 0x97, 0xae, 0xe5, 0x8b, 0xe4, 0xa8, 0x00, 0x27, 0x31,
	0xa9, 0xbb, 0xb0, 0xe8, 0x49, 0x65, 0x0a, 0x71, 0x28, 0x80, 0x93, 0x98, 0x4a, 0x07, 0x20, 0x5d,
	0xd9, 0x03, 0xab, 0x6f, 0x46, 0xa6, 0xed, 0x59, 0x1f, 0xa3, 0x6b, 0xc1, 0xdc, 0xf7, 0xa1, 0x70,
	0x39, 0x26, 0xf6, 0x00, 0x13, 0xc2, 0x85, 0x71, 0x23, 0x5c, 0xf2, 0x89, 0x54, 0x9c, 0xfc, 0x37,
	0x19, 0x58, 0x8e, 0x62, 0x5a, 0x1f, 0xf6, 0xa6, 0x43, 0x1a, 0x07, 0x2c, 0x2b, 0x02, 0x16, 0x43,
	0x25, 0x27, 0xa0, 0xb2, 0x05, 0xf9, 0xfe, 0xb0, 0xc7, 0xe5, 0x7a, 0x68, 0xf6, 0x87, 0x3d, 0x26,
	0xd6, 0x6f, 0xa2, 0xa8, 0xcc, 0x86, 0x4d, 0xf6, 0x0d, 0x96, 0x7f, 0x0a, 0x92, 0xd2, 0xed, 0x86,
	0xbb, 0x97, 0x8e, 0xbf, 0x41, 0x4f, 0x21, 0xd7, 0xb9, 0xea, 0xb1, 0xc9, 0x2d, 0xbe, 0xdc, 0x7a,
	0x1e, 0xdb, 0xeb, 0x9e, 0x47, 0x58, 0x29, 0x97, 0xfc, 0x25, 0xac, 0x08, 0x02, 0x88, 0xf3, 0x71,
	0x12, 0xfe, 0x22, 0x03, 0x9b, 0xa1, 0x08, 0x65, 0xd0, 0xf5, 0x76, 0xb8, 0x8f, 0x9d, 0x0a, 0x52,
	0xa0, 0xc0, 0xd1, 0xbb, 0xea, 0x99, 0x7d, 0x9b, 0x50, 0x00, 0x73, 0x07, 0x8b, 0x2f, 0x77, 0xd2,
	0xbb, 0xf9, 0xa3, 0x2c, 0xda, 0xfc, 0x47, 0xdd, 0x26, 0xae, 0xfc, 0x97, 0x19, 0x28, 0xa5, 0xcf,
	0xe5, 0x23, 0xb5, 0xfa, 0x14, 0x93, 0x79, 0x02, 0x52, 0x15, 0xf7, 0xe3, 0x6b, 0x93, 0x16, 0x5c,
	0xe4, 0x55, 0x58, 0x11, 0x18, 0x89, 0x23, 0x7f, 0x09, 0x52, 0x0d, 0xbb, 0xf1, 0xde, 0x1b, 0x30,
	0x37, 0xbc, 0xba, 0x22, 0xd8, 0xf5, 0xba, 0x7b, 0x5f, 0x68, 0x0d, 0x66, 0xfb, 0xf6, 0x8d, 0xed,
	0x9b, 0x1c, 0xff, 0x90, 0x7f, 0x0e, 0x2b, 0x82, 0x04, 0xe2, 0xa0, 0x1f, 0x42, 0x3e, 0x50, 0x29,
	0xc3, 0x54, 0x9a, 0x82, 0xc4, 0x7c, 0x87, 0xab, 0x42, 0x3d, 0xcf, 0x1d, 0xba, 0x56, 0x3f, 0x66,
	0xd9, 0xc0, 0x48, 0xcc, 0xb4, 0xe5, 0x33, 0x58, 0x0b, 0x71, 0x8f, 0x18, 0xc0, 0x1f, 0x40, 0xde,
	0x87, 0xd1, 0x03, 0xfe, 0x16, 0x04, 0xe7, 0x3d, 0x04, 0xe5, 0xaf, 0x60, 0x3d, 0x45, 0x22, 0x71,
	0x7e, 0x03, 0x91, 0x67, 0xb0, 0x76, 0x3a, 0xfc, 0xa4, 0x93, 0xdc, 0x84, 0xf5, 0x14, 0x89, 0xc4,
	0x91, 0x35, 0x58, 0x0b, 0x97, 0x34, 0x32, 0x54, 0x6a, 0x72, 0x11, 0x4f, 0x05, 0xb2, 0xf1, 0x54,
	0x80, 0x8e, 0x91, 0x22, 0x8a, 0x8f, 0x11, 0xae, 0xef, 0x1d, 0xc7, 0xe8, 0x86, 0xc6, 0xcc, 0xc7,
	0xe8, 0x32, 0x53, 0x3d, 0x07, 0x14, 0x97, 0xc3, 0x56, 0x3d, 0xe1, 0x03, 0x99, 0x8f, 0xf6, 0x81,
	0x7f, 0xce, 0xc0, 0x7a, 0xca, 0x24, 0x89, 0xf3, 0x09, 0x84, 0x23, 0x03, 0xd6, 0x1d, 0xde, 0x6c,
	0xa6, 0xf9, 0xea, 0x83, 0xa9, 0xa2, 0xa8, 0x04, 0x1d, 0x39, 0x09, 0x9a, 0xec, 0x42, 0x81, 0x7e,
	0x9e, 0xb3, 0xa0, 0x44, 0x53, 0x8f, 0xa9, 0x5b, 0xfe, 0x26, 0xcc, 0xc7, 0x17, 0x6e, 0xce, 0xe6,
	0xc1, 0x33, 0x8c, 0x72, 0xb9, 0x58, 0x94, 0x9b, 0x90, 0x21, 0xae, 0xc3, 0x6a, 0x0d, 0xbb, 0xb1,
	0x81, 0x75, 0xfc, 0x8d, 0xdc, 0x48, 0x21, 0x13, 0x07, 0xfd, 0x28, 0xe1, 0xc5, 0xf7, 0x04, 0x65,
	0xe3, 0x5d, 0x7c, 0x47, 0x96, 0x55, 0x58, 0x55, 0xba, 0x5d, 0x71, 0x18, 0xf4, 0x3c, 0xba, 0x35,
	0x4e, 0x17, 0xc5, 0xf6, 0xfc, 0x2f, 0x52, 0xc4, 0x10, 0x87, 0xea, 0x46, 0xa7, 0x15, 0x98, 0xde,
	0x6c, 0xe7, 0x8a, 0xee, 0x6f, 0xff, 0x99, 0x05, 0x54, 0xb3, 0xdf, 0x63, 0x1a, 0x4b, 0xbd, 0x45,
	0xa0, 0x83, 0x4a, 0x90, 0x1b, 0x07, 0xac, 0xf4, 0x67, 0x7a, 0xd6, 0x2b, 0x41, 0x6e, 0x30, 0xbe,
	0xf1, 0x50, 0xa4, 0x3f, 0x29, 0xb4, 0x3c, 0x6c, 0x7a, 0x81, 0xd2, 0xfb, 0xa2, 0x71, 0x72, 0x38,
	0xea, 0xd2, 0x94, 0xa6, 0xeb, 0xe5, 0x63, 0xf3, 0xec, 0x5b, 0xeb, 0xa2, 0x27, 0xb0, 0xec, 0x65,
	0x51, 0xdd, 0xf1, 0xc8, 0x72, 0xed, 0xe1, 0xc0, 0x4b, 0x3e, 0x8a, 0x9c, 0x5c, 0xf5, 0xa8, 0x48,
	0x86, 0x82, 0x17, 0xa3, 0x2d, 0xc7, 0xa1, 0x82, 0xe6, 0x79, 0x62, 0xc7, 0x89, 0x8a, 0xe3, 0x68,
	0xdd, 0x70, 0x37, 0xe4, 0xe9, 0x5b, 0x3e, 0xb2, 0x1b, 0xf2, 0xdc, 0xed, 0x01, 0x2c, 0x0d, 0xc7,
	0x2e, 0xb1, 0xbb, 0x38, 0x9a, 0x01, 0x2f, 0x7a, 0x34, 0x96, 0xcb, 0x20, 0x98, 0x21, 0x76, 0x6f,
	0xc0, 0xf2, 0x8e, 0x05, 0x9d, 0xfd, 0x4e, 0x26, 0x25, 0x8b, 0x29, 0x49, 0x09, 0x35, 0x14, 0x11,
	0x4b, 0xe2, 0xc8, 0xbf, 0xcc, 0xc0, 0x5a, 0x9b, 0x60, 0x3f, 0x55, 0x51, 0x7f, 0xe1, 0x8e, 0x2c,
	0x6d, 0x70, 0x35, 0xa4, 0x39, 0x89, 0x6b, 0x8d, 0x7a, 0xd8, 0x35, 0x43, 0xb0, 0x17, 0x38, 0xa5,
	0x6d, 0xb3, 0x1c, 0xaf, 0x73, 0x6d, 0x0d, 0x06, 0xb8, 0x1f, 0xe2, 0xbe, 0xe0, 0x51, 0xf8, 0x91,
	0x63, 0x4c, 0x70, 0x2c, 0x03, 0xcc, 0x8f, 0x09, 0xe6, 0xf9, 0xcc, 0x0e, 0x40, 0x17, 0x5b, 0x7d,
	0xd3, 0x1d, 0xbe, 0xc3, 0x03, 0x2f, 0x4b, 0x5e, 0xa0, 0x14, 0x83, 0x12, 0xe4, 0xff, 0xce, 0x01,
	0x8a, 0x4c, 0x89, 0x5a, 0x4b, 0xfa, 0xb2, 0xc7, 0x1c, 0x2c, 0xfb, 0x31, 0xe7, 0x9a, 0x29, 0x2b,
	0x1e, 0x9b, 0xf9, 0x9c, 0x30, 0x73, 0x71, 0x81, 0xe6, 0x93, 0x0b, 0x24, 0x43, 0xc1, 0x17, 0xcd,
	0xfd, 0x2c, 0xbf, 0x97, 0xa3, 0x86, 0xe0, 0xc9, 0x67, 0x1b, 0x90, 0x9f, 0xef, 0x71, 0x3b, 0x58,
	0x08, 0xf3, 0x3d, 0x6e, 0x06, 0x3b, 0x00, 0xac, 0x25, 0x9a, 0x61, 0x2e, 0x30, 0x0a, 0xd3, 0xec,
	0x31, 0x2c, 0x87, 0x2b, 0xc3, 0xc7, 0x58, 0x64, 0xfb, 0x72, 0x21, 0x58, 0x1e, 0x36, 0xca, 0x11,
	0x00, 0xa6, 0xcb, 0x69, 0xda, 0x83, 0xab, 0x61, 0x69, 0x89, 0xf9, 0xe8, 0xbe, 0xe0, 0xa3, 0x69,
	0x4b, 0xaf, 0x2f, 0xe0, 0xc0, 0x0a, 0x0e, 0x40, 0xa2, 0x68, 0x8c, 0xb0, 0x45, 0xec, 0xe1, 0x80,
	0x4f, 0xa8, 0xc0, 0x1d, 0x60, 0x4c, 0xb0, 0xce, 0xc9, 0xfe, 0x11, 0x29, 0x6e, 0x84, 0xc5, 0x14,
	0x23, 0xec, 0xc1, 0x6a, 0x62, 0x65, 0x89, 0x43, 0x1d, 0x73, 0x84, 0x6f, 0x2c, 0x7b, 0xe0, 0xe7,
	0x27, 0xfc, 0x4b, 0x30, 0x94, 0xac, 0x60, 0x28, 0xb1, 0x83, 0x44, 0x2e, 0x76, 0x90, 0x90, 0xdf,
	0xc2, 0xd2, 0x91, 0x45, 0xd8, 0xfe, 0xcc, 0xf4, 0x98, 0xba, 0x17, 0xc7, 0x4c, 0x25, 0x2b, 0x98,
	0x4a, 0x62, 0x1b, 0x91, 0x7f, 0x9d, 0x81, 0xcd, 0x23, 0xcb, 0xed, 0x5c, 0xdf, 0xc9, 0x48, 0xa3,
	0xa6, 0x96, 0x8d, 0x9b, 0x9a, 0x68, 0x4d, 0xb9, 0xa4, 0x35, 0x3d, 0x86, 0x65, 0x1f, 0x7f, 0x1f,
	0x7e, 0x6e, 0xcb, 0x05, 0x0f, 0x7e, 0x0f, 0x7d, 0x05, 0xe8, 0x7a, 0x78, 0xb9, 0xc0, 0xe0, 0x6a,
	0x48, 0x4a, 0xb3, 0x6c, 0x7b, 0xdf, 0x16, 0xd6, 0x3b, 0x0a, 0x8a, 0x4e, 0xcf, 0x71, 0xfe, 0x07,
	0x91, 0x7f, 0x0c, 0xa5, 0x74, 0xad, 0x88, 0x23, 0x2c, 0x44, 0x46, 0xf4, 0xd8, 0xaf, 0xe1, 0x7e,
	0xe5, 0x1a, 0x77, 0xde, 0xb5, 0xb9, 0xbc, 0xd6, 0xb8, 0xd3, 0x39, 0xfa, 0x70, 0x6a, 0xd9, 0x83,
	0x26, 0xd5, 0x94, 0xe2, 0x12, 0x45, 0x21, 0x33, 0x1d, 0x85, 0x6c, 0x02, 0x05, 0xf9, 0x0f, 0x61,
	0x77, 0xaa, 0x7c, 0xe2, 0xa0, 0x12, 0xcc, 0x93, 0x71, 0xa7, 0x83, 0x09, 0x61, 0xf2, 0xf3, 0xba,
	0xff, 0x29, 0xff, 0x00, 0x36, 0x6a, 0xd8, 0xf5, 0xba, 0xb2, 0x0e, 0x4c, 0xf9, 0xa9, 0x93, 0x92,
	0x7f, 0x95, 0x81, 0xcd, 0xd4, 0x5e, 0xc4, 0x41, 0x35, 0xee, 0x13, 0xbc, 0x6b, 0x17, 0xbb, 0x96,
	0xdd, 0x9f, 0x90, 0xfe, 0xb5, 0x09, 0x66, 0x5d, 0xab, 0x8c, 0x89, 0xb9, 0x4c, 0xe4, 0x1b, 0x19,
	0xb0, 0x16, 0x0a, 0x8a, 0xb8, 0x6a, 0xf6, 0xee, 0xae, 0xba, 0xe2, 0x8b, 0x0c, 0x48, 0xf2, 0xbf,
	0x67, 0xa0, 0x7c, 0x36, 0x1a, 0x76, 0xa2, 0x8b, 0x48, 0x51, 0x1c, 0x8e, 0x5d, 0xaa, 0xb4, 0x78,
	0x7c, 0xcf, 0x24, 0x8e, 0xef, 0x9e, 0x0d, 0x67, 0x27, 0x6c, 0xb4, 0xdf, 0xcf, 0x05, 0x92, 0xbc,
	0x03, 0xdb, 0x13, 0xb5, 0x20, 0x8e, 0xfc, 0x18, 0x10, 0x5f, 0x9f, 0xe0, 0x94, 0x9d, 0xea, 0x7e,
	0xf2, 0x9f, 0x65, 0x58, 0x26, 0x14, 0x67, 0x24, 0x0e, 0x52, 0x99, 0xc3, 0x78, 0x30, 0x44, 0xf2,
	0xa1, 0xdd, 0x24, 0xea, 0xb1, 0xbb, 0x11, 0xe6, 0x34, 0x0c, 0x29, 0xb6, 0xc7, 0x3e, 0x84, 0x62,
	0xdf, 0x22, 0xae, 0xe9, 0x5f, 0x40, 0x10, 0x06, 0x5b, 0x4e, 0x5f, 0xa2, 0xd4, 0x26, 0xbf, 0x82,
	0x20, 0xf2, 0x5f, 0x65, 0xa0, 0x24, 0x4c, 0xe2, 0xe8, 0xc3, 0xa7, 0x88, 0x6b, 0xe2, 0x65, 0xc0,
	0xe7, 0xb0, 0x62, 0x13, 0x73, 0x80, 0x71, 0x37, 0x32, 0x23, 0x9e, 0x2f, 0x16, 0x6d, 0xd2, 0xc0,
	0xb8, 0x1b, 0xcc, 0xe9, 0xcf, 0x33, 0xb0, 0x35, 0x61, 0x4e, 0xdf, 0x37, 0x3c, 0xbf, 0xe2, 0xc9,
	0xbe, 0x70, 0x65, 0x92, 0x8e, 0xcd, 0x0e, 0xc0, 0x25, 0xee, 0xf9, 0x36, 0xe3, 0xe5, 0x1d, 0x8c,
	0xc2, 0xf6, 0xcb, 0x2d, 0xc8, 0xe3, 0x41, 0x37, 0x16, 0x12, 0xf0, 0xa0, 0xeb, 0xdf, 0x2d, 0x85,
	0xa8, 0xce, 0x4c, 0x43, 0x75, 0x76, 0xca, 0x15, 0xcb, 0x5c, 0xec, 0x8a, 0x45, 0x6e, 0xf9, 0x9b,
	0x4b, 0x7c, 0xe6, 0xc4, 0x41, 0x3f, 0xe6, 0x9d, 0x22, 0xd8, 0xdd, 0x9f, 0x82, 0x1d, 0xed, 0x45,
	0x85, 0xb2, 0x64, 0xbb, 0x06, 0x05, 0x2f, 0x4b, 0xf6, 0xd2, 0xec, 0xa9, 0xd1, 0x6b, 0x2b, 0x72,
	0x0a, 0xa5, 0x78, 0x2c, 0x85, 0xc7, 0xcc, 0x13, 0x28, 0x46, 0x05, 0x11, 0xe7, 0x3b, 0x4b, 0xaa,
	0x41, 0xa1, 0x8a, 0xfb, 0x9f, 0x60, 0x4a, 0x12, 0x14, 0xa3, 0x82, 0x88, 0x43, 0xcf, 0x4d, 0xde,
	0x51, 0xe5, 0x2e, 0xa2, 0x9f, 0xc1, 0x2a, 0x6b, 0x0c, 0x56, 0x2b, 0x7a, 0x30, 0x95, 0x68, 0x53,
	0xcb, 0x5b, 0x36, 0x66, 0x80, 0x9b, 0x30, 0x4f, 0x13, 0x25, 0xab, 0xdf, 0x67, 0xe6, 0x90, 0xd7,
	0xe7, 0x7a, 0xd8, 0x55, 0xfa, 0x7d, 0xf9, 0x87, 0x50, 0x8c, 0x8e, 0x4a, 0x1c, 0x9a, 0xb8, 0x25,
	0x0f, 0x96, 0x4b, 0xf1, 0x63, 0xe9, 0xdf, 0x67, 0x60, 0xb1, 0x7d, 0xd7, 0xb4, 0xe2, 0xf6, 0x8b,
	0xd2, 0xa9, 0xbe, 0x1c, 0x4b, 0x44, 0x67, 0x84, 0x44, 0x54, 0x38, 0x4a, 0xcc, 0x8a, 0x47, 0x09,
	0xf9, 0x6f, 0x33, 0xb0, 0x6c, 0x8c, 0xac, 0x01, 0xb1, 0x3a, 0xf4, 0x7c, 0xc2, 0x66, 0xcb, 0xee,
	0xc2, 0x31, 0xfe, 0x13, 0x1c, 0x9d, 0x2f, 0x70, 0x92, 0xef, 0x05, 0x43, 0x07, 0x8f, 0xa2, 0xbe,
	0x95, 0xa7, 0x04, 0xdf, 0xb5, 0x82, 0x68, 0x99, 0x8b, 0x87, 0xf0, 0x5d, 0x58, 0xf4, 0x4e, 0x49,
	0x91, 0x4b, 0x5d, 0xe0, 0x24, 0xff, 0xd2, 0x9e, 0xa7, 0x30, 0xde, 0x4c, 0xbd, 0x2f, 0xf9, 0x1f,
	0x33, 0x50, 0x38, 0x1e, 0x61, 0xfc, 0x16, 0xfb, 0xbb, 0xa1, 0x06, 0x92, 0xcb, 0x66, 0xcd, 0x8e,
	0x55, 0x3c, 0x1e, 0xf2, 0xe0, 0x2a, 0xba, 0x8f, 0xa0, 0x9d, 0xbe, 0x1c, 0xe9, 0xc7, 0xd4, 0xfd,
	0x12, 0x8a, 0x41, 0x3e, 0x14, 0x3d, 0xdf, 0x97, 0x93, 0x7e, 0x18, 0xa6, 0x44, 0xb6, 0xf7, 0x8b,
	0x59, 0x8f, 0xb7, 0xfd, 0xe4, 0xc2, 0x70, 0x22, 0x41, 0x31, 0x3a, 0x5f, 0xe2, 0xc8, 0xff, 0x9b,
	0x81, 0xe5, 0x90, 0xf4, 0xfa, 0xe5, 0x6f, 0x23, 0x0b, 0x14, 0x16, 0x6e, 0x26, 0x6d, 0xe1, 0x42,
	0x3b, 0x9c, 0x9d, 0xb6, 0xb7, 0xcd, 0x4d, 0xb3, 0xb2, 0x79, 0xc1, 0xca, 0xc2, 0x65, 0xcb, 0xc7,
	0x96, 0x0d, 0x81, 0x14, 0x57, 0x99, 0x38, 0x32, 0x86, 0x62, 0xe4, 0x6c, 0xd9, 0x1a, 0xdf, 0x7c,
	0xc7, 0xfb, 0x8f, 0xe9, 0x8f, 0x07, 0xf2, 0xd7, 0xec, 0xf2, 0x2a, 0x3e, 0xd2, 0x77, 0x88, 0xa2,
	0x91, 0xe1, 0x73, 0xd1, 0xe1, 0xe5, 0xaf, 0x82, 0x50, 0x14, 0x95, 0x1f, 0xb9, 0x3e, 0x24, 0xe3,
	0x9b, 0xc9, 0xd9, 0x5e, 0xb4, 0x13, 0x1b, 0xa0, 0x35, 0xbe, 0xa1, 0x91, 0x76, 0xbb, 0x86, 0x5d,
	0x96, 0xa6, 0x31, 0x25, 0x8e, 0x3e, 0xd0, 0x55, 0xd5, 0xad, 0x01, 0xbf, 0xcf, 0x88, 0x87, 0xb4,
	0xcc, 0xb4, 0x90, 0x96, 0x8d, 0x87, 0xb4, 0x68, 0x60, 0xca, 0xc5, 0xef, 0xfe, 0xd7, 0x60, 0xd6,
	0xb1, 0x46, 0x16, 0xf1, 0x8e, 0xd7, 0xfc, 0x43, 0xee, 0xc1, 0xbd, 0xc9, 0x33, 0x21, 0x0e, 0xed,
	0xc5, 0x71, 0xf7, 0xef, 0x61, 0xfc, 0xf7, 0x87, 0xd0, 0x46, 0xb2, 0x82, 0x8d, 0xac, 0xc1, 0xec,
	0x7b, 0xab, 0x3f, 0xf6, 0x27, 0xc0, 0x3f, 0xe4, 0x6f, 0xa0, 0xec, 0x0f, 0x44, 0x3d, 0xe9, 0x7b,
	0xd0, 0x58, 0xfe, 0x49, 0x88, 0x72, 0x62, 0x48, 0x7e, 0x84, 0xe1, 0x1e, 0x18, 0xec, 0xed, 0x0b,
	0xfa, 0xc2, 0xd0, 0xe7, 0x96, 0xff, 0x2d, 0x0b, 0x2b, 0x95, 0xe1, 0xe0, 0x3d, 0x1e, 0xd1, 0x13,
	0xed, 0xe4, 0xdc, 0xec, 0xf6, 0xd3, 0x4a, 0x78, 0x1b, 0x95, 0x8b, 0xdc, 0x46, 0xad, 0xc3, 0xdc,
	0x65, 0xcf, 0xa4, 0x27, 0x49, 0xee, 0xbd, 0xb3, 0x97, 0xbd, 0x46, 0xec, 0x4a, 0x6a, 0x36, 0x76,
	0x25, 0xf5, 0x08, 0x8a, 0x37, 0x96, 0x8b, 0x47, 0x76, 0xb0, 0xc5, 0x73, 0xc7, 0x2d, 0xf8, 0x54,
	0x7e, 0x53, 0xf0, 0x39, 0x48, 0x9d, 0x60, 0xd2, 0xb1, 0x57, 0xc1, 0xe5, 0x90, 0xce, 0x59, 0xa3,
	0x3b, 0x50, 0x3e, 0xbe, 0x03, 0x9d, 0x00, 0x0a, 0x06, 0x0b, 0x13, 0xbe, 0x85, 0x5b, 0x37, 0x4b,
	0xc9, 0xef, 0xe5, 0xe7, 0x7b, 0xf2, 0x1a, 0x20, 0x11, 0x44, 0xe2, 0xc8, 0x0e, 0xac, 0xea, 0xc3,
	0x7e, 0x9f, 0xe6, 0x3a, 0x6d, 0x2f, 0xf8, 0xa5, 0x83, 0xbb, 0x0b, 0x8b, 0x9d, 0x11, 0xb6, 0xdc,
	0x18, 0xb6, 0xc0, 0x49, 0xfe, 0x71, 0x78, 0x38, 0xb2, 0xa9, 0xe5, 0x08, 0xa1, 0xa8, 0xc0, 0xc9,
	0x4d, 0xef, 0xf8, 0xb6, 0x01, 0x6b, 0xc9, 0x11, 0x89, 0x23, 0xff, 0x31, 0x40, 0x15, 0x77, 0xc7,
	0x1d, 0xf7, 0xf6, 0x67, 0xce, 0xa9, 0x97, 0x02, 0x81, 0x9f, 0xe4, 0x22, 0x7e, 0x22, 0xff, 0x4b,
	0x06, 0x96, 0xb8, 0x78, 0xef, 0x80, 0x97, 0xd4, 0xf0, 0xf7, 0xbd, 0x21, 0x23, 0xe1, 0x48, 0x7c,
	0x47, 0x09, 0x27, 0xc8, 0x67, 0xc3, 0x22, 0x51, 0xea, 0x80, 0xe2, 0xc3, 0xe6, 0x4c, 0xe2, 0x61,
	0x93, 0xe6, 0x34, 0x84, 0x66, 0x3f, 0x66, 0xc4, 0xca, 0xf2, 0xfa, 0xa2, 0x4d, 0x94, 0x7e, 0x9f,
	0x27, 0x4a, 0xf2, 0xdf, 0x65, 0x60, 0x83, 0x1d, 0xfc, 0xf9, 0xc0, 0xd1, 0x15, 0xfa, 0x09, 0x2c,
	0x76, 0x19, 0x31, 0x9a, 0xc6, 0x6e, 0xa7, 0xce, 0xd7, 0x3b, 0xe2, 0x02, 0xe7, 0x67, 0x73, 0x46,
	0x30, 0x43, 0x93, 0x07, 0xbf, 0xf6, 0x81, 0xfe, 0xbe, 0x25, 0x89, 0xf0, 0x06, 0x8b, 0x2a, 0xc3,
	0x49, 0xcc, 0xc1, 0xff, 0x23, 0x80, 0x57, 0xc7, 0x64, 0xdc, 0x77, 0x53, 0xe0, 0x55, 0x61, 0xc5,
	0x3b, 0xf6, 0x9b, 0x1f, 0x01, 0xf3, 0xb2, 0xd7, 0x27, 0x38, 0xb6, 0xfc, 0x14, 0x8a, 0x57, 0x96,
	0x1d, 0x75, 0x86, 0xdc, 0x6d, 0x32, 0x96, 0x68, 0x87, 0x40, 0xc0, 0x36, 0x2c, 0x30, 0x01, 0xd1,
	0xb3, 0x06, 0x25, 0x30, 0x3d, 0xce, 0xbd, 0xeb, 0x23, 0x11, 0x6f, 0xe2, 0x7c, 0x0c, 0xe0, 0x1c,
	0x83, 0x28, 0xe0, 0xf2, 0xff, 0x65, 0x58, 0x0c, 0x8e, 0x5e, 0x31, 0x7c, 0x54, 0x26, 0x72, 0x5b,
	0x3a, 0x3a, 0xf5, 0xf8, 0x14, 0x46, 0x88, 0xd9, 0xe4, 0x75, 0x6f, 0xe4, 0x3a, 0x73, 0x4e, 0xbc,
	0xce, 0x14, 0x9c, 0x7e, 0x3e, 0xe1, 0xf4, 0x62, 0x1e, 0x9d, 0x17, 0xf3, 0x68, 0xf9, 0x67, 0xb0,
	0x74, 0x4b, 0x7c, 0xc9, 0x4d, 0x8b, 0x2f, 0xb9, 0x30, 0xbe, 0x6c, 0xc0, 0x1c, 0x8d, 0x94, 0x37,
	0xc4, 0xb3, 0x4e, 0xef, 0x4b, 0xfe, 0x11, 0x2c, 0x30, 0x65, 0xa6, 0x44, 0xc9, 0x20, 0x10, 0x66,
	0xa3, 0x81, 0xf0, 0x29, 0x2c, 0x79, 0x9b, 0x12, 0xf1, 0x0f, 0x60, 0x3e, 0xfc, 0xc4, 0x8b, 0x42,
	0x79, 0x0f, 0x7f, 0x22, 0x3f, 0x83, 0xad, 0x4a, 0x1f, 0x5b, 0x83, 0xe8, 0xc9, 0xb0, 0x62, 0x75,
	0xae, 0xd3, 0x9f, 0x3d, 0xe4, 0xed, 0x89, 0xec, 0xc4, 0x39, 0xfc, 0xa7, 0x19, 0x98, 0xaf, 0x07,
	0xc9, 0x80, 0x54, 0x6f, 0xd6, 0x4c, 0xe3, 0xe2, 0x4c, 0x35, 0xb5, 0xc6, 0x6b, 0xa5, 0xae, 0x55,
	0xa5, 0xcf, 0x90, 0x04, 0x4b, 0x01, 0xb5, 0xdd, 0x52, 0xa5, 0x0c, 0x5a, 0x85, 0xe5, 0x80, 0xa2,
	0xbe, 0x39, 0xd3, 0x74, 0x55, 0xca, 0xa2, 0xfb, 0x50, 0x0e, 0x88, 0xc7, 0xba, 0x52, 0x3b, 0x55,
	0x1b, 0x86, 0xa9, 0xbe, 0xa9, 0x9c, 0x28, 0x8d, 0x9a, 0x2a, 0xe5, 0xd2, 0xdb, 0xf5, 0x66, 0xbd,
	0x7e, 0xa4, 0x54, 0x5e, 0x49, 0x33, 0xe8, 0x1e, 0x94, 0xc2, 0xc1, 0x0d, 0xf5, 0xd4, 0xac, 0x34,
	0x1b, 0xaf, 0x55, 0xbd, 0xa5, 0x35, 0x1b, 0xd2, 0x2c, 0x5a, 0x87, 0x95, 0xa0, 0x35, 0xe8, 0x34,
	0x87, 0xca, 0xb0, 0x11, 0x90, 0x9b, 0x67, 0xba, 0x62, 0xa8, 0x66, 0x55, 0xad, 0xb6, 0x2b, 0x86,
	0x34, 0x1f, 0x13, 0x78, 0xd4, 0x6e, 0x69, 0x0d, 0xb5, 0xd5, 0xf2, 0x5b, 0xf3, 0x68, 0x1f, 0x76,
	0x43, 0x1d, 0x1a, 0xaa, 0x5e, 0xbb, 0x48, 0x8c, 0xba, 0x80, 0x1e, 0x83, 0x9c, 0xca, 0xd4, 0x32,
	0x14, 0xdd, 0x34, 0x74, 0xf5, 0x15, 0x03, 0x04, 0x62, 0x43, 0x51, 0x85, 0x1b, 0x6a, 0xdd, 0xac,
	0x37, 0x0d, 0x43, 0xd5, 0x2f, 0xa4, 0x45, 0x74, 0x00, 0x0f, 0xd3, 0x87, 0x52, 0x0c, 0xb3, 0xa2,
	0x34, 0x0c, 0x55, 0x6d, 0x30, 0x39, 0x4b, 0x31, 0x39, 0xb5, 0x7a, 0x53, 0xbf, 0x30, 0xcf, 0x9b,
	0x7a, 0xbd, 0xca, 0x5a, 0x0b, 0xe8, 0x21, 0xec, 0xa5, 0xb6, 0x7a, 0x23, 0x31, 0xae, 0x62, 0x4c,
	0xc6, 0xab, 0x86, 0x56, 0x3b, 0xa1, 0x83, 0xe8, 0x5c, 0xc6, 0x32, 0x92, 0xe1, 0x7e, 0xaa, 0x8c,
	0x53, 0xa5, 0xa6, 0x55, 0x18, 0x8f, 0x84, 0x76, 0x60, 0x2b, 0xe0, 0xa9, 0xea, 0xda, 0xb1, 0x61,
	0x1e, 0x35, 0x0d, 0xa3, 0xce, 0x57, 0x7f, 0xe5, 0xf0, 0x7f, 0x32, 0xb0, 0x1c, 0x79, 0xeb, 0xf4,
	0x2d, 0xa7, 0xdd, 0x78, 0xd5, 0x68, 0x9e, 0x73, 0xcd, 0x68, 0x57, 0xe9, 0x33, 0x4a, 0xa5, 0xeb,
	0x74, 0xa6, 0x54, 0x5e, 0x99, 0x67, 0xba, 0xda, 0x52, 0x1b, 0x86, 0x94, 0xa1, 0x53, 0x08, 0xa8,
	0x6c, 0x66, 0xba, 0x56, 0x39, 0x31, 0x95, 0x4a, 0x45, 0xad, 0xab, 0xba, 0x62, 0x34, 0x75, 0x29,
	0x4b, 0x57, 0x27, 0xce, 0x53, 0x39, 0x51, 0xf4, 0xd3, 0x18, 0x53, 0x8e, 0xce, 0x33, 0x60, 0xf2,
	0x31, 0xf0, 0x2d, 0x4b, 0x9a, 0x41, 0xdb, 0xb0, 0x99, 0x32, 0x8e, 0xd6, 0xa8, 0xe8, 0xd2, 0x2c,
	0x2a, 0xc1, 0x5a, 0xbc, 0x91, 0x43, 0x25, 0xcd, 0x1d, 0xfe, 0x7a, 0x09, 0x56, 0xfc, 0xf4, 0x3c,
	0x8c, 0x90, 0x5b, 0xb0, 0xee, 0x29, 0x48, 0xbb, 0x28, 0x35, 0xd5, 0x6c, 0x35, 0xdb, 0x7a, 0x85,
	0x6a, 0xb9, 0x0f, 0xbb, 0x71, 0x9a, 0xa9, 0x54, 0x0c, 0xed, 0xb5, 0x66, 0x5c, 0x44, 0x94, 0xde,
	0x83, 0x7b, 0x02, 0x53, 0x55, 0xd1, 0xea, 0x17, 0x66, 0xe5, 0x44, 0xad, 0xbc, 0xd2, 0x1a, 0x52,
	0x16, 0x3d, 0x80, 0x1d, 0x81, 0xe3, 0x58, 0xd3, 0x5b, 0x86, 0xa9, 0xab, 0x54, 0x77, 0xe6, 0x42,
	0xdb, 0xb0, 0x29, 0xb0, 0x34, 0x8f, 0x8f, 0xb5, 0x8a, 0xa6, 0xd4, 0xb9, 0xba, 0x42, 0x63, 0xeb,
	0x54, 0x69, 0x9d, 0xa8, 0xb5, 0x9a, 0x34, 0x4b, 0xa1, 0x12, 0x1a, 0x23, 0x76, 0x3e, 0x87, 0x76,
	0x61, 0x5b, 0x54, 0xe1, 0x9c, 0xa1, 0xae, 0x36, 0x0c, 0x55, 0x97, 0xe6, 0xd1, 0x53, 0x78, 0x92,
	0xca, 0x70, 0xd1, 0xbe, 0xd0, 0x1a, 0x66, 0x5d, 0x7b, 0xad, 0x9a, 0xa7, 0x5a, 0x8b, 0x49, 0xcb,
	0xa3, 0x17, 0xf0, 0x54, 0x60, 0xd6, 0xa8, 0x18, 0x8a, 0x4a, 0xb3, 0x41, 0x7f, 0x6b, 0xa7, 0x4a,
	0xe5, 0x22, 0xe8, 0xb0, 0x40, 0x2d, 0x42, 0xec, 0x20, 0xb8, 0x22, 0xa4, 0xa8, 0xd7, 0x68, 0x1e,
	0x69, 0x75, 0xcd, 0xa0, 0x1e, 0x76, 0x0f, 0x4a, 0x42, 0xa3, 0x71, 0xa4, 0x2a, 0x0d, 0xf3, 0xa8,
	0x7d, 0x21, 0x2d, 0xa1, 0x47, 0xf0, 0x40, 0x54, 0xde, 0xf3, 0x51, 0x5d, 0xad, 0xb2, 0xf5, 0x54,
	0x0d, 0xa9, 0x90, 0xb2, 0x00, 0x0c, 0x40, 0xb3, 0xd6, 0xac, 0x57, 0x4d, 0x0a, 0x63, 0x91, 0xee,
	0x61, 0x02, 0xcb, 0x6b, 0xed, 0xcc, 0x3c, 0xd5, 0xea, 0x75, 0x3a, 0xc9, 0x65, 0x74, 0x08, 0x8f,
	0x85, 0x76, 0xcf, 0xdf, 0x5b, 0x46, 0xb3, 0x11, 0xc3, 0x5c, 0x4a, 0x51, 0xba, 0xaa, 0xe8, 0xaf,
	0xcc, 0x1a, 0xf7, 0xb7, 0x46, 0xbb, 0x25, 0xad, 0xa4, 0xe8, 0x45, 0xc5, 0x9c, 0x69, 0x6a, 0x45,
	0x95, 0x50, 0x4a, 0x6b, 0xb0, 0x2f, 0x49, 0xab, 0x29, 0x6b, 0x7a, 0xd1, 0x6c, 0x9b, 0xcc, 0x84,
	0xcf, 0x4f, 0x9a, 0xd2, 0x1a, 0x7a, 0x02, 0xfb, 0x02, 0x43, 0x4d, 0x39, 0x55, 0x4d, 0x43, 0xa3,
	0x78, 0x84, 0x66, 0xb7, 0x9e, 0x32, 0x53, 0x71, 0x8f, 0xdb, 0x4c, 0x41, 0x26, 0xb2, 0xbb, 0x49,
	0xa5, 0x94, 0xf6, 0xc8, 0xee, 0x23, 0x6d, 0xa5, 0xb5, 0x37, 0xeb, 0x3a, 0x03, 0x4e, 0x57, 0xa5,
	0x72, 0x8a, 0xff, 0xf0, 0x76, 0xdf, 0x88, 0xb6, 0x53, 0xd0, 0x68, 0xb7, 0x54, 0xdd, 0xac, 0x28,
	0xf5, 0xba, 0x74, 0x8f, 0xee, 0x9d, 0x93, 0xc7, 0xe7, 0xbb, 0x9f, 0xb4, 0x93, 0xc2, 0xe5, 0x79,
	0xb0, 0x79, 0xae, 0xaa, 0x7c, 0xa3, 0x90, 0xee, 0xa7, 0x18, 0xca, 0x99, 0xaa, 0xe8, 0x75, 0x6a,
	0x07, 0xaf, 0x4c, 0x43, 0x55, 0xa4, 0xdd, 0x14, 0xf0, 0xa3, 0x1b, 0xa9, 0xb4, 0x97, 0x62, 0x29,
	0xb1, 0x9d, 0x36, 0x62, 0x29, 0x0f, 0x68, 0xfc, 0x98, 0xe0, 0xf6, 0x7c, 0xc5, 0xce, 0xd5, 0xfa,
	0xb1, 0xa2, 0xab, 0x92, 0x9c, 0xe2, 0xe6, 0x9e, 0x45, 0x28, 0x5a, 0x43, 0xda, 0x4f, 0x03, 0x99,
	0xf6, 0x3f, 0x51, 0xeb, 0x67, 0xaa, 0x2e, 0x3d, 0x4c, 0x69, 0xf7, 0xd5, 0x6f, 0xa9, 0x86, 0xf4,
	0x28, 0xc5, 0x91, 0x8e, 0xd5, 0x7a, 0xbd, 0x79, 0x6e, 0xd6, 0xd5, 0xd7, 0x6a, 0x9d, 0x6f, 0x09,
	0xd2, 0xe3, 0x94, 0xcd, 0x42, 0x6b, 0xbc, 0xd6, 0x0c, 0x6f, 0x41, 0x8e, 0xf5, 0xe6, 0xa9, 0x6f,
	0x40, 0xd2, 0x93, 0x14, 0x99, 0x0d, 0xf5, 0x5c, 0xdc, 0xfa, 0x0e, 0x52, 0xd6, 0x5f, 0x7d, 0x63,
	0xf0, 0xe9, 0xbf, 0xad, 0xbe, 0x79, 0x23, 0x7d, 0x9e, 0x62, 0xa5, 0x35, 0x5d, 0x39, 0xa2, 0x23,
	0x69, 0x3a, 0x63, 0x94, 0x0e, 0xa7, 0x58, 0xf2, 0xb9, 0x5a, 0xad, 0x6a, 0x8d, 0x9a, 0xf4, 0x14,
	0x3d, 0x87, 0xc3, 0x5b, 0xb6, 0x73, 0xf3, 0xb4, 0xd9, 0x30, 0x4e, 0xb8, 0x35, 0x7c, 0x91, 0xa2,
	0x80, 0x52, 0x7d, 0xad, 0x36, 0x8c, 0xb6, 0x1e, 0xf6, 0x94, 0x9e, 0x1d, 0x5e, 0xc3, 0xea, 0xd9,
	0x08, 0x13, 0x3c, 0x70, 0x8f, 0x22, 0x2f, 0xdd, 0x34, 0x78, 0x84, 0x28, 0x07, 0x59, 0x0a, 0x0f,
	0xbd, 0xea, 0xb1, 0xd2, 0xae, 0x1b, 0xd2, 0x67, 0xe8, 0x0b, 0x38, 0x98, 0xc4, 0xc4, 0x77, 0x53,
	0x43, 0x0d, 0x43, 0xcd, 0x61, 0x1f, 0xe0, 0x58, 0x57, 0xd5, 0xb7, 0x2a, 0x65, 0x40, 0x1b, 0x80,
	0xc2, 0x2f, 0xb3, 0x1d, 0x64, 0x75, 0x71, 0xfa, 0x99, 0xae, 0x9e, 0x51, 0x13, 0xca, 0xd0, 0x44,
	0x2b, 0x42, 0xaf, 0x34, 0x4f, 0x4f, 0x35, 0x43, 0xca, 0xa2, 0x4d, 0x58, 0x8d, 0x90, 0x83, 0x0c,
	0x2c, 0x77, 0x58, 0xf7, 0x8f, 0xca, 0x4c, 0x9d, 0x0d, 0x40, 0x5e, 0x98, 0xe4, 0x89, 0x96, 0x9f,
	0x09, 0xac, 0x40, 0x21, 0x9e, 0x9e, 0xb1, 0x24, 0x52, 0xcc, 0xca, 0xb2, 0x87, 0x7f, 0x9d, 0x81,
	0x22, 0x17, 0x77, 0xec, 0x1d, 0x83, 0x68, 0x8a, 0xc7, 0x9b, 0x8f, 0x15, 0xad, 0x2e, 0x28, 0xb1,
	0x07, 0xf7, 0xc4, 0x36, 0xad, 0x6a, 0x36, 0x9a, 0x34, 0xf1, 0xd4, 0x5a, 0x5e, 0xb2, 0x21, 0x70,
	0xb0, 0xd0, 0xc2, 0x58, 0x1a, 0xcd, 0x76, 0xed, 0x84, 0x27, 0x1b, 0x02, 0x8f, 0x37, 0x79, 0xc6,
	0x4a, 0x89, 0x52, 0xee, 0xe5, 0x2f, 0xb7, 0x60, 0x35, 0x78, 0x00, 0xb3, 0x08, 0x6e, 0xe1, 0xd1,
	0x7b, 0xbb, 0x83, 0x91, 0xce, 0x1e, 0x61, 0x22, 0xf5, 0xfd, 0xe2, 0xd3, 0x97, 0x58, 0x3f, 0x5b,
	0xde, 0x9b, 0xce, 0x40, 0x1c, 0x2a, 0x33, 0x56, 0x59, 0x99, 0x90, 0x29, 0x56, 0x6e, 0x26, 0x64,
	0x26, 0x0b, 0x33, 0x75, 0xf6, 0x32, 0x33, 0x45, 0xa6, 0x58, 0x4b, 0x9a, 0x90, 0x99, 0xa8, 0x21,
	0x45, 0x5f, 0x47, 0x8b, 0x7b, 0xfd, 0x3f, 0x1e, 0xec, 0x4f, 0x54, 0x2f, 0x7c, 0xbb, 0x29, 0x3f,
	0xbc, 0x9d, 0x89, 0x38, 0xa8, 0x17, 0xad, 0xfa, 0x0c, 0xab, 0x6d, 0xd1, 0xe3, 0x89, 0xbd, 0x63,
	0xe5, 0xc1, 0xe5, 0x27, 0x77, 0xe2, 0xe3, 0x8a, 0x24, 0xaa, 0x08, 0x13, 0x8a, 0xa4, 0x15, 0x43,
	0x26, 0x14, 0x49, 0x2f, 0x46, 0xfc, 0x1a, 0x56, 0x12, 0x75, 0x9c, 0x09, 0xf9, 0x69, 0xb5, 0xa3,
	0x09, 0xf9, 0xa9, 0xe5, 0xa0, 0x54, 0x7e, 0xa2, 0x86, 0x33, 0x21, 0x3f, 0xad, 0x60, 0x34, 0x21,
	0x3f, 0xb5, 0x14, 0x14, 0xbd, 0x65, 0xc5, 0xc2, 0xf1, 0xb2, 0x45, 0x39, 0xa9, 0xb9, 0x58, 0xf7,
	0x57, 0xbe, 0x95, 0x87, 0xcb, 0x16, 0x6b, 0xfd, 0x12, 0xb2, 0x53, 0x6a, 0x0a, 0xcb, 0xb7, 0xf2,
	0x10, 0x07, 0x69, 0x00, 0xe1, 0xc3, 0x26, 0xba, 0x97, 0xde, 0xc3, 0x93, 0xb7, 0x33, 0xa5, 0x95,
	0x8b, 0x0a, 0x1f, 0x24, 0x13, 0xa2, 0x62, 0x8f, 0x9e, 0x09, 0x51, 0xf1, 0x97, 0x4c, 0x2a, 0x2a,
	0x7c, 0x53, 0x4c, 0x88, 0x8a, 0x3d, 0x72, 0x26, 0x44, 0x09, 0x8f, 0x91, 0x6f, 0x60, 0x59, 0xa8,
	0xd6, 0x43, 0x62, 0x59, 0x6a, 0xb2, 0x32, 0x32, 0xb9, 0x2c, 0xc9, 0x82, 0x3f, 0x2a, 0x59, 0xa8,
	0xf0, 0x49, 0x48, 0x4e, 0xd6, 0x35, 0x25, 0x24, 0xa7, 0x15, 0x09, 0xf5, 0x60, 0x2d, 0xad, 0x80,
	0x28, 0xe1, 0xd5, 0x13, 0x6a, 0xa7, 0x12, 0x5e, 0x3d, 0xb1, 0x1a, 0xe9, 0x4f, 0x61, 0x7b, 0x4a,
	0x39, 0x10, 0x7a, 0x26, 0xc8, 0x99, 0x5e, 0x9a, 0x54, 0x7e, 0xfe, 0x31, 0xec, 0xde, 0xd2, 0xc4,
	0x9f, 0xfc, 0x93, 0x4b, 0x93, 0xa8, 0x4c, 0x49, 0xf3, 0x98, 0x44, 0x4d, 0xca, 0xcf, 0x13, 0x65,
	0x10, 0xbc, 0x22, 0x03, 0x3d, 0x99, 0xde, 0x39, 0xa8, 0x25, 0x29, 0x1f, 0xdc, 0x8d, 0x91, 0x38,
	0xc8, 0x81, 0xcd, 0x09, 0xe5, 0x35, 0xe8, 0x73, 0xb1, 0xfe, 0x79, 0x62, 0x31, 0x51, 0xf9, 0xf0,
	0xae, 0xac, 0xdc, 0x3b, 0xc2, 0x47, 0xc3, 0x84, 0x77, 0xc4, 0x5e, 0x81, 0x13, 0xde, 0x11, 0x7f,
	0x73, 0x45, 0x4d, 0x58, 0x8a, 0xbe, 0x3f, 0xa2, 0xfb, 0x13, 0xd9, 0xd9, 0x7b, 0x6c, 0x79, 0x77,
	0x6a, 0x3b, 0x71, 0x50, 0xd7, 0x2f, 0x12, 0x8a, 0x55, 0x7b, 0xa1, 0x47, 0xa9, 0x70, 0x8a, 0x75,
	0x64, 0xe5, 0xc7, 0x77, 0x61, 0x23, 0x0e, 0xb2, 0x12, 0x35, 0x4b, 0xf5, 0x61, 0x0f, 0x3d, 0x9c,
	0xbe, 0x66, 0xbc, 0x12, 0xa6, 0xfc, 0xe8, 0x0e, 0x5c, 0x41, 0xc0, 0x13, 0x1e, 0x62, 0xf7, 0xd3,
	0xfb, 0xc6, 0x1e, 0x50, 0xcb, 0x0f, 0x6f, 0x67, 0x22, 0x0e, 0x22, 0xac, 0x90, 0x29, 0xf5, 0x01,
	0x11, 0x1d, 0x26, 0x25, 0x4c, 0x7a, 0xf3, 0x2c, 0x3f, 0xbd, 0x33, 0x2f, 0xb7, 0xd5, 0x09, 0x2f,
	0x7b, 0x09, 0x5b, 0x9d, 0xfc, 0xe8, 0x58, 0x3e, 0xbc, 0x2b, 0x2b, 0x71, 0x50, 0x1b, 0x8a, 0xf1,
	0x77, 0x2c, 0x24, 0x26, 0x4d, 0x89, 0xb7, 0xc2, 0xf2, 0x83, 0x5b, 0x38, 0x88, 0x83, 0xfe, 0x08,
	0x24, 0xf1, 0x59, 0x2a, 0x11, 0x12, 0x53, 0x5e, 0xca, 0xca, 0xfb, 0xb7, 0xf2, 0x70, 0x1b, 0x4e,
	0x79, 0x56, 0x48, 0xd8, 0x70, 0xfa, 0x53, 0x4f, 0xf9, 0xf1, 0x5d, 0xd8, 0x88, 0x83, 0x1a, 0x6c,
	0x8f, 0x0a, 0xd0, 0xe2, 0x6f, 0x0d, 0x96, 0x6b, 0x21, 0xf1, 0x95, 0x22, 0x86, 0x7d, 0x29, 0x81,
	0x8d, 0x7f, 0x97, 0xfe, 0x15, 0x33, 0xa8, 0x80, 0x39, 0x78, 0xbe, 0xf8, 0x8d, 0x44, 0x26, 0xa7,
	0xc8, 0x5f, 0x65, 0xa6, 0xc9, 0x13, 0x1b, 0x63, 0xb7, 0xf6, 0x7d, 0xd8, 0x48, 0xbf, 0x69, 0x47,
	0xe2, 0x8e, 0x3b, 0xf1, 0xfe, 0xbe, 0x7c, 0x47, 0x4e, 0xe2, 0x1c, 0xfd, 0xee, 0xdb, 0x17, 0xbd,
	0x61, 0xdf, 0x1a, 0xf4, 0x9e, 0xff, 0xde, 0x4b, 0xd7, 0x7d, 0xde, 0x19, 0xde, 0xbc, 0x60, 0xff,
	0x3f, 0xee, 0x0c, 0xfb, 0x2f, 0x08, 0x3f, 0x9e, 0x90, 0xf8, 0xff, 0x93, 0x2f, 0xe7, 0x18, 0xc3,
	0x0f, 0xfe, 0x3f, 0x00, 0x00, 0xff, 0xff, 0xb9, 0x4b, 0x09, 0x62, 0xd4, 0x3c, 0x00, 0x00,
}
