// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/present-limit/present-limit.proto

package present_limit // import "golang.52tt.com/protocol/services/present-limit"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 开闭类型
type LimitValueType int32

const (
	LimitValueType_LIMIT_VALUE_TYPE_UNSPECIFIED LimitValueType = 0
	LimitValueType_LIMIT_VALUE_TYPE_LEFT_OPEN   LimitValueType = 1
	LimitValueType_LIMIT_VALUE_TYPE_RIGHT_OPEN  LimitValueType = 2
)

var LimitValueType_name = map[int32]string{
	0: "LIMIT_VALUE_TYPE_UNSPECIFIED",
	1: "LIMIT_VALUE_TYPE_LEFT_OPEN",
	2: "LIMIT_VALUE_TYPE_RIGHT_OPEN",
}
var LimitValueType_value = map[string]int32{
	"LIMIT_VALUE_TYPE_UNSPECIFIED": 0,
	"LIMIT_VALUE_TYPE_LEFT_OPEN":   1,
	"LIMIT_VALUE_TYPE_RIGHT_OPEN":  2,
}

func (x LimitValueType) String() string {
	return proto.EnumName(LimitValueType_name, int32(x))
}
func (LimitValueType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{0}
}

type ChangeScopeType int32

const (
	ChangeScopeType_CHANGE_SCOPE_TYPE_UNSPECIFIED          ChangeScopeType = 0
	ChangeScopeType_CHANGE_SCOPE_TYPE_PRESENT_LIMIT_SCOPE  ChangeScopeType = 1
	ChangeScopeType_CHANGE_SCOPE_TYPE_PRESENT_SCENE_LIMIT  ChangeScopeType = 2
	ChangeScopeType_CHANGE_SCOPE_TYPE_PRESENT_TARGET_LIMIT ChangeScopeType = 3
	ChangeScopeType_CHANGE_SCOPE_TYPE_BACKPACK_PRICE_LIMIT ChangeScopeType = 4
)

var ChangeScopeType_name = map[int32]string{
	0: "CHANGE_SCOPE_TYPE_UNSPECIFIED",
	1: "CHANGE_SCOPE_TYPE_PRESENT_LIMIT_SCOPE",
	2: "CHANGE_SCOPE_TYPE_PRESENT_SCENE_LIMIT",
	3: "CHANGE_SCOPE_TYPE_PRESENT_TARGET_LIMIT",
	4: "CHANGE_SCOPE_TYPE_BACKPACK_PRICE_LIMIT",
}
var ChangeScopeType_value = map[string]int32{
	"CHANGE_SCOPE_TYPE_UNSPECIFIED":          0,
	"CHANGE_SCOPE_TYPE_PRESENT_LIMIT_SCOPE":  1,
	"CHANGE_SCOPE_TYPE_PRESENT_SCENE_LIMIT":  2,
	"CHANGE_SCOPE_TYPE_PRESENT_TARGET_LIMIT": 3,
	"CHANGE_SCOPE_TYPE_BACKPACK_PRICE_LIMIT": 4,
}

func (x ChangeScopeType) String() string {
	return proto.EnumName(ChangeScopeType_name, int32(x))
}
func (ChangeScopeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{1}
}

type ErrorCode int32

const (
	ErrorCode_ERROR_CODE_UNSPECIFIED                ErrorCode = 0
	ErrorCode_ERROR_CODE_CHANNEL_LOCKED             ErrorCode = 1
	ErrorCode_ERROR_CODE_PGC_ROOM                   ErrorCode = 2
	ErrorCode_ERROR_CODE_FELLOW_VALUE_LIMIT         ErrorCode = 3
	ErrorCode_ERROR_CODE_FELLOW_LV_LIMIT            ErrorCode = 4
	ErrorCode_ERROR_CODE_DAT_LIMIT                  ErrorCode = 5
	ErrorCode_ERROR_CODE_BACKPACK_PRICE_LIMIT       ErrorCode = 6
	ErrorCode_ERROR_CODE_NOT_FELLOW                 ErrorCode = 7
	ErrorCode_ERROR_CODE_CHANNEL_IM                 ErrorCode = 8
	ErrorCode_ERROR_CODE_CHANNEL_LOCKED_NO_PGC_ROOM ErrorCode = 9
	ErrorCode_ERROR_CODE_CHANNEL_ALL_MIC            ErrorCode = 10
)

var ErrorCode_name = map[int32]string{
	0:  "ERROR_CODE_UNSPECIFIED",
	1:  "ERROR_CODE_CHANNEL_LOCKED",
	2:  "ERROR_CODE_PGC_ROOM",
	3:  "ERROR_CODE_FELLOW_VALUE_LIMIT",
	4:  "ERROR_CODE_FELLOW_LV_LIMIT",
	5:  "ERROR_CODE_DAT_LIMIT",
	6:  "ERROR_CODE_BACKPACK_PRICE_LIMIT",
	7:  "ERROR_CODE_NOT_FELLOW",
	8:  "ERROR_CODE_CHANNEL_IM",
	9:  "ERROR_CODE_CHANNEL_LOCKED_NO_PGC_ROOM",
	10: "ERROR_CODE_CHANNEL_ALL_MIC",
}
var ErrorCode_value = map[string]int32{
	"ERROR_CODE_UNSPECIFIED":                0,
	"ERROR_CODE_CHANNEL_LOCKED":             1,
	"ERROR_CODE_PGC_ROOM":                   2,
	"ERROR_CODE_FELLOW_VALUE_LIMIT":         3,
	"ERROR_CODE_FELLOW_LV_LIMIT":            4,
	"ERROR_CODE_DAT_LIMIT":                  5,
	"ERROR_CODE_BACKPACK_PRICE_LIMIT":       6,
	"ERROR_CODE_NOT_FELLOW":                 7,
	"ERROR_CODE_CHANNEL_IM":                 8,
	"ERROR_CODE_CHANNEL_LOCKED_NO_PGC_ROOM": 9,
	"ERROR_CODE_CHANNEL_ALL_MIC":            10,
}

func (x ErrorCode) String() string {
	return proto.EnumName(ErrorCode_name, int32(x))
}
func (ErrorCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{2}
}

type BackpackPriceLimit_PriceLimitType int32

const (
	BackpackPriceLimit_PRICE_LIMIT_TYPE_UNSPECIFIED  BackpackPriceLimit_PriceLimitType = 0
	BackpackPriceLimit_PRICE_LIMIT_TYPE_FELLOW_VALUE BackpackPriceLimit_PriceLimitType = 1
	BackpackPriceLimit_PRICE_LIMIT_TYPE_FELLOW_LV    BackpackPriceLimit_PriceLimitType = 2
)

var BackpackPriceLimit_PriceLimitType_name = map[int32]string{
	0: "PRICE_LIMIT_TYPE_UNSPECIFIED",
	1: "PRICE_LIMIT_TYPE_FELLOW_VALUE",
	2: "PRICE_LIMIT_TYPE_FELLOW_LV",
}
var BackpackPriceLimit_PriceLimitType_value = map[string]int32{
	"PRICE_LIMIT_TYPE_UNSPECIFIED":  0,
	"PRICE_LIMIT_TYPE_FELLOW_VALUE": 1,
	"PRICE_LIMIT_TYPE_FELLOW_LV":    2,
}

func (x BackpackPriceLimit_PriceLimitType) String() string {
	return proto.EnumName(BackpackPriceLimit_PriceLimitType_name, int32(x))
}
func (BackpackPriceLimit_PriceLimitType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{4, 0}
}

// RuleSet 完整对象，对应数据库中的 rule_sets 表及关联表
type RuleSet struct {
	// 受限礼物范围
	PresentLimitScope *PresentLimitScope `protobuf:"bytes,1,opt,name=present_limit_scope,json=presentLimitScope,proto3" json:"present_limit_scope,omitempty"`
	// 礼物场景限制
	PresentSceneLimit *PresentSceneLimit `protobuf:"bytes,2,opt,name=present_scene_limit,json=presentSceneLimit,proto3" json:"present_scene_limit,omitempty"`
	// 送礼对象限制
	PresentTargetLimit []*PresentTargetLimit `protobuf:"bytes,3,rep,name=present_target_limit,json=presentTargetLimit,proto3" json:"present_target_limit,omitempty"`
	// 背包送礼金额上限
	BackpackPriceLimit   *BackpackPriceLimit `protobuf:"bytes,4,opt,name=backpack_price_limit,json=backpackPriceLimit,proto3" json:"backpack_price_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *RuleSet) Reset()         { *m = RuleSet{} }
func (m *RuleSet) String() string { return proto.CompactTextString(m) }
func (*RuleSet) ProtoMessage()    {}
func (*RuleSet) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{0}
}
func (m *RuleSet) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RuleSet.Unmarshal(m, b)
}
func (m *RuleSet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RuleSet.Marshal(b, m, deterministic)
}
func (dst *RuleSet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RuleSet.Merge(dst, src)
}
func (m *RuleSet) XXX_Size() int {
	return xxx_messageInfo_RuleSet.Size(m)
}
func (m *RuleSet) XXX_DiscardUnknown() {
	xxx_messageInfo_RuleSet.DiscardUnknown(m)
}

var xxx_messageInfo_RuleSet proto.InternalMessageInfo

func (m *RuleSet) GetPresentLimitScope() *PresentLimitScope {
	if m != nil {
		return m.PresentLimitScope
	}
	return nil
}

func (m *RuleSet) GetPresentSceneLimit() *PresentSceneLimit {
	if m != nil {
		return m.PresentSceneLimit
	}
	return nil
}

func (m *RuleSet) GetPresentTargetLimit() []*PresentTargetLimit {
	if m != nil {
		return m.PresentTargetLimit
	}
	return nil
}

func (m *RuleSet) GetBackpackPriceLimit() *BackpackPriceLimit {
	if m != nil {
		return m.BackpackPriceLimit
	}
	return nil
}

type PresentLimitScope struct {
	IsAllBackpackGifts    bool     `protobuf:"varint,1,opt,name=is_all_backpack_gifts,json=isAllBackpackGifts,proto3" json:"is_all_backpack_gifts,omitempty"`
	IsAllIntimateGifts    bool     `protobuf:"varint,2,opt,name=is_all_intimate_gifts,json=isAllIntimateGifts,proto3" json:"is_all_intimate_gifts,omitempty"`
	RiskControlBusinessId []uint32 `protobuf:"varint,3,rep,packed,name=risk_control_business_id,json=riskControlBusinessId,proto3" json:"risk_control_business_id,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *PresentLimitScope) Reset()         { *m = PresentLimitScope{} }
func (m *PresentLimitScope) String() string { return proto.CompactTextString(m) }
func (*PresentLimitScope) ProtoMessage()    {}
func (*PresentLimitScope) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{1}
}
func (m *PresentLimitScope) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentLimitScope.Unmarshal(m, b)
}
func (m *PresentLimitScope) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentLimitScope.Marshal(b, m, deterministic)
}
func (dst *PresentLimitScope) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentLimitScope.Merge(dst, src)
}
func (m *PresentLimitScope) XXX_Size() int {
	return xxx_messageInfo_PresentLimitScope.Size(m)
}
func (m *PresentLimitScope) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentLimitScope.DiscardUnknown(m)
}

var xxx_messageInfo_PresentLimitScope proto.InternalMessageInfo

func (m *PresentLimitScope) GetIsAllBackpackGifts() bool {
	if m != nil {
		return m.IsAllBackpackGifts
	}
	return false
}

func (m *PresentLimitScope) GetIsAllIntimateGifts() bool {
	if m != nil {
		return m.IsAllIntimateGifts
	}
	return false
}

func (m *PresentLimitScope) GetRiskControlBusinessId() []uint32 {
	if m != nil {
		return m.RiskControlBusinessId
	}
	return nil
}

type PresentTargetLimit struct {
	BeginPrice           uint32         `protobuf:"varint,1,opt,name=begin_price,json=beginPrice,proto3" json:"begin_price,omitempty"`
	EndPrice             uint32         `protobuf:"varint,2,opt,name=end_price,json=endPrice,proto3" json:"end_price,omitempty"`
	LimitValueType       LimitValueType `protobuf:"varint,3,opt,name=limit_value_type,json=limitValueType,proto3,enum=present_limit.LimitValueType" json:"limit_value_type,omitempty"`
	FellowValueLimit     uint32         `protobuf:"varint,4,opt,name=fellow_value_limit,json=fellowValueLimit,proto3" json:"fellow_value_limit,omitempty"`
	FellowLvLimit        uint32         `protobuf:"varint,5,opt,name=fellow_lv_limit,json=fellowLvLimit,proto3" json:"fellow_lv_limit,omitempty"`
	DatLimit             uint32         `protobuf:"varint,6,opt,name=dat_limit,json=datLimit,proto3" json:"dat_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *PresentTargetLimit) Reset()         { *m = PresentTargetLimit{} }
func (m *PresentTargetLimit) String() string { return proto.CompactTextString(m) }
func (*PresentTargetLimit) ProtoMessage()    {}
func (*PresentTargetLimit) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{2}
}
func (m *PresentTargetLimit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentTargetLimit.Unmarshal(m, b)
}
func (m *PresentTargetLimit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentTargetLimit.Marshal(b, m, deterministic)
}
func (dst *PresentTargetLimit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentTargetLimit.Merge(dst, src)
}
func (m *PresentTargetLimit) XXX_Size() int {
	return xxx_messageInfo_PresentTargetLimit.Size(m)
}
func (m *PresentTargetLimit) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentTargetLimit.DiscardUnknown(m)
}

var xxx_messageInfo_PresentTargetLimit proto.InternalMessageInfo

func (m *PresentTargetLimit) GetBeginPrice() uint32 {
	if m != nil {
		return m.BeginPrice
	}
	return 0
}

func (m *PresentTargetLimit) GetEndPrice() uint32 {
	if m != nil {
		return m.EndPrice
	}
	return 0
}

func (m *PresentTargetLimit) GetLimitValueType() LimitValueType {
	if m != nil {
		return m.LimitValueType
	}
	return LimitValueType_LIMIT_VALUE_TYPE_UNSPECIFIED
}

func (m *PresentTargetLimit) GetFellowValueLimit() uint32 {
	if m != nil {
		return m.FellowValueLimit
	}
	return 0
}

func (m *PresentTargetLimit) GetFellowLvLimit() uint32 {
	if m != nil {
		return m.FellowLvLimit
	}
	return 0
}

func (m *PresentTargetLimit) GetDatLimit() uint32 {
	if m != nil {
		return m.DatLimit
	}
	return 0
}

type PresentSceneLimit struct {
	IsLockedChannel      bool     `protobuf:"varint,1,opt,name=is_locked_channel,json=isLockedChannel,proto3" json:"is_locked_channel,omitempty"`
	IsNotPgc             bool     `protobuf:"varint,2,opt,name=is_not_pgc,json=isNotPgc,proto3" json:"is_not_pgc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentSceneLimit) Reset()         { *m = PresentSceneLimit{} }
func (m *PresentSceneLimit) String() string { return proto.CompactTextString(m) }
func (*PresentSceneLimit) ProtoMessage()    {}
func (*PresentSceneLimit) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{3}
}
func (m *PresentSceneLimit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentSceneLimit.Unmarshal(m, b)
}
func (m *PresentSceneLimit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentSceneLimit.Marshal(b, m, deterministic)
}
func (dst *PresentSceneLimit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentSceneLimit.Merge(dst, src)
}
func (m *PresentSceneLimit) XXX_Size() int {
	return xxx_messageInfo_PresentSceneLimit.Size(m)
}
func (m *PresentSceneLimit) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentSceneLimit.DiscardUnknown(m)
}

var xxx_messageInfo_PresentSceneLimit proto.InternalMessageInfo

func (m *PresentSceneLimit) GetIsLockedChannel() bool {
	if m != nil {
		return m.IsLockedChannel
	}
	return false
}

func (m *PresentSceneLimit) GetIsNotPgc() bool {
	if m != nil {
		return m.IsNotPgc
	}
	return false
}

type BackpackPriceLimit struct {
	PriceLimitType       BackpackPriceLimit_PriceLimitType `protobuf:"varint,1,opt,name=price_limit_type,json=priceLimitType,proto3,enum=present_limit.BackpackPriceLimit_PriceLimitType" json:"price_limit_type,omitempty"`
	LimitScopes          []*BackpackLimitScope             `protobuf:"bytes,2,rep,name=limit_scopes,json=limitScopes,proto3" json:"limit_scopes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *BackpackPriceLimit) Reset()         { *m = BackpackPriceLimit{} }
func (m *BackpackPriceLimit) String() string { return proto.CompactTextString(m) }
func (*BackpackPriceLimit) ProtoMessage()    {}
func (*BackpackPriceLimit) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{4}
}
func (m *BackpackPriceLimit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BackpackPriceLimit.Unmarshal(m, b)
}
func (m *BackpackPriceLimit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BackpackPriceLimit.Marshal(b, m, deterministic)
}
func (dst *BackpackPriceLimit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BackpackPriceLimit.Merge(dst, src)
}
func (m *BackpackPriceLimit) XXX_Size() int {
	return xxx_messageInfo_BackpackPriceLimit.Size(m)
}
func (m *BackpackPriceLimit) XXX_DiscardUnknown() {
	xxx_messageInfo_BackpackPriceLimit.DiscardUnknown(m)
}

var xxx_messageInfo_BackpackPriceLimit proto.InternalMessageInfo

func (m *BackpackPriceLimit) GetPriceLimitType() BackpackPriceLimit_PriceLimitType {
	if m != nil {
		return m.PriceLimitType
	}
	return BackpackPriceLimit_PRICE_LIMIT_TYPE_UNSPECIFIED
}

func (m *BackpackPriceLimit) GetLimitScopes() []*BackpackLimitScope {
	if m != nil {
		return m.LimitScopes
	}
	return nil
}

type BackpackLimitScope struct {
	BeginValue           uint32         `protobuf:"varint,1,opt,name=begin_value,json=beginValue,proto3" json:"begin_value,omitempty"`
	EndValue             uint32         `protobuf:"varint,2,opt,name=end_value,json=endValue,proto3" json:"end_value,omitempty"`
	LimitValueType       LimitValueType `protobuf:"varint,3,opt,name=limit_value_type,json=limitValueType,proto3,enum=present_limit.LimitValueType" json:"limit_value_type,omitempty"`
	LimitTbeanValue      uint32         `protobuf:"varint,4,opt,name=limit_tbean_value,json=limitTbeanValue,proto3" json:"limit_tbean_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BackpackLimitScope) Reset()         { *m = BackpackLimitScope{} }
func (m *BackpackLimitScope) String() string { return proto.CompactTextString(m) }
func (*BackpackLimitScope) ProtoMessage()    {}
func (*BackpackLimitScope) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{5}
}
func (m *BackpackLimitScope) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BackpackLimitScope.Unmarshal(m, b)
}
func (m *BackpackLimitScope) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BackpackLimitScope.Marshal(b, m, deterministic)
}
func (dst *BackpackLimitScope) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BackpackLimitScope.Merge(dst, src)
}
func (m *BackpackLimitScope) XXX_Size() int {
	return xxx_messageInfo_BackpackLimitScope.Size(m)
}
func (m *BackpackLimitScope) XXX_DiscardUnknown() {
	xxx_messageInfo_BackpackLimitScope.DiscardUnknown(m)
}

var xxx_messageInfo_BackpackLimitScope proto.InternalMessageInfo

func (m *BackpackLimitScope) GetBeginValue() uint32 {
	if m != nil {
		return m.BeginValue
	}
	return 0
}

func (m *BackpackLimitScope) GetEndValue() uint32 {
	if m != nil {
		return m.EndValue
	}
	return 0
}

func (m *BackpackLimitScope) GetLimitValueType() LimitValueType {
	if m != nil {
		return m.LimitValueType
	}
	return LimitValueType_LIMIT_VALUE_TYPE_UNSPECIFIED
}

func (m *BackpackLimitScope) GetLimitTbeanValue() uint32 {
	if m != nil {
		return m.LimitTbeanValue
	}
	return 0
}

type GetRuleSetReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRuleSetReq) Reset()         { *m = GetRuleSetReq{} }
func (m *GetRuleSetReq) String() string { return proto.CompactTextString(m) }
func (*GetRuleSetReq) ProtoMessage()    {}
func (*GetRuleSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{6}
}
func (m *GetRuleSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRuleSetReq.Unmarshal(m, b)
}
func (m *GetRuleSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRuleSetReq.Marshal(b, m, deterministic)
}
func (dst *GetRuleSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRuleSetReq.Merge(dst, src)
}
func (m *GetRuleSetReq) XXX_Size() int {
	return xxx_messageInfo_GetRuleSetReq.Size(m)
}
func (m *GetRuleSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRuleSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRuleSetReq proto.InternalMessageInfo

type GetRuleSetResp struct {
	SetInfo              *RuleSet `protobuf:"bytes,1,opt,name=set_info,json=setInfo,proto3" json:"set_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRuleSetResp) Reset()         { *m = GetRuleSetResp{} }
func (m *GetRuleSetResp) String() string { return proto.CompactTextString(m) }
func (*GetRuleSetResp) ProtoMessage()    {}
func (*GetRuleSetResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{7}
}
func (m *GetRuleSetResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRuleSetResp.Unmarshal(m, b)
}
func (m *GetRuleSetResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRuleSetResp.Marshal(b, m, deterministic)
}
func (dst *GetRuleSetResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRuleSetResp.Merge(dst, src)
}
func (m *GetRuleSetResp) XXX_Size() int {
	return xxx_messageInfo_GetRuleSetResp.Size(m)
}
func (m *GetRuleSetResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRuleSetResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRuleSetResp proto.InternalMessageInfo

func (m *GetRuleSetResp) GetSetInfo() *RuleSet {
	if m != nil {
		return m.SetInfo
	}
	return nil
}

type UpdateRuleSetReq struct {
	SetInfo              *RuleSet        `protobuf:"bytes,1,opt,name=set_info,json=setInfo,proto3" json:"set_info,omitempty"`
	ChangeScopeType      ChangeScopeType `protobuf:"varint,2,opt,name=change_scope_type,json=changeScopeType,proto3,enum=present_limit.ChangeScopeType" json:"change_scope_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UpdateRuleSetReq) Reset()         { *m = UpdateRuleSetReq{} }
func (m *UpdateRuleSetReq) String() string { return proto.CompactTextString(m) }
func (*UpdateRuleSetReq) ProtoMessage()    {}
func (*UpdateRuleSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{8}
}
func (m *UpdateRuleSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRuleSetReq.Unmarshal(m, b)
}
func (m *UpdateRuleSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRuleSetReq.Marshal(b, m, deterministic)
}
func (dst *UpdateRuleSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRuleSetReq.Merge(dst, src)
}
func (m *UpdateRuleSetReq) XXX_Size() int {
	return xxx_messageInfo_UpdateRuleSetReq.Size(m)
}
func (m *UpdateRuleSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRuleSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRuleSetReq proto.InternalMessageInfo

func (m *UpdateRuleSetReq) GetSetInfo() *RuleSet {
	if m != nil {
		return m.SetInfo
	}
	return nil
}

func (m *UpdateRuleSetReq) GetChangeScopeType() ChangeScopeType {
	if m != nil {
		return m.ChangeScopeType
	}
	return ChangeScopeType_CHANGE_SCOPE_TYPE_UNSPECIFIED
}

type UpdateRuleSetResp struct {
	SetInfo              *RuleSet `protobuf:"bytes,1,opt,name=set_info,json=setInfo,proto3" json:"set_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateRuleSetResp) Reset()         { *m = UpdateRuleSetResp{} }
func (m *UpdateRuleSetResp) String() string { return proto.CompactTextString(m) }
func (*UpdateRuleSetResp) ProtoMessage()    {}
func (*UpdateRuleSetResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{9}
}
func (m *UpdateRuleSetResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRuleSetResp.Unmarshal(m, b)
}
func (m *UpdateRuleSetResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRuleSetResp.Marshal(b, m, deterministic)
}
func (dst *UpdateRuleSetResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRuleSetResp.Merge(dst, src)
}
func (m *UpdateRuleSetResp) XXX_Size() int {
	return xxx_messageInfo_UpdateRuleSetResp.Size(m)
}
func (m *UpdateRuleSetResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRuleSetResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRuleSetResp proto.InternalMessageInfo

func (m *UpdateRuleSetResp) GetSetInfo() *RuleSet {
	if m != nil {
		return m.SetInfo
	}
	return nil
}

type GetAllRulePresentReq struct {
	IsAllIntimateGifts    bool     `protobuf:"varint,1,opt,name=is_all_intimate_gifts,json=isAllIntimateGifts,proto3" json:"is_all_intimate_gifts,omitempty"`
	RiskControlBusinessId []uint32 `protobuf:"varint,2,rep,packed,name=risk_control_business_id,json=riskControlBusinessId,proto3" json:"risk_control_business_id,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *GetAllRulePresentReq) Reset()         { *m = GetAllRulePresentReq{} }
func (m *GetAllRulePresentReq) String() string { return proto.CompactTextString(m) }
func (*GetAllRulePresentReq) ProtoMessage()    {}
func (*GetAllRulePresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{10}
}
func (m *GetAllRulePresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllRulePresentReq.Unmarshal(m, b)
}
func (m *GetAllRulePresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllRulePresentReq.Marshal(b, m, deterministic)
}
func (dst *GetAllRulePresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllRulePresentReq.Merge(dst, src)
}
func (m *GetAllRulePresentReq) XXX_Size() int {
	return xxx_messageInfo_GetAllRulePresentReq.Size(m)
}
func (m *GetAllRulePresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllRulePresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllRulePresentReq proto.InternalMessageInfo

func (m *GetAllRulePresentReq) GetIsAllIntimateGifts() bool {
	if m != nil {
		return m.IsAllIntimateGifts
	}
	return false
}

func (m *GetAllRulePresentReq) GetRiskControlBusinessId() []uint32 {
	if m != nil {
		return m.RiskControlBusinessId
	}
	return nil
}

type GetAllRulePresentResp struct {
	PresentConfigs       []*SimplePresentConfig `protobuf:"bytes,1,rep,name=present_configs,json=presentConfigs,proto3" json:"present_configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetAllRulePresentResp) Reset()         { *m = GetAllRulePresentResp{} }
func (m *GetAllRulePresentResp) String() string { return proto.CompactTextString(m) }
func (*GetAllRulePresentResp) ProtoMessage()    {}
func (*GetAllRulePresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{11}
}
func (m *GetAllRulePresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllRulePresentResp.Unmarshal(m, b)
}
func (m *GetAllRulePresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllRulePresentResp.Marshal(b, m, deterministic)
}
func (dst *GetAllRulePresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllRulePresentResp.Merge(dst, src)
}
func (m *GetAllRulePresentResp) XXX_Size() int {
	return xxx_messageInfo_GetAllRulePresentResp.Size(m)
}
func (m *GetAllRulePresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllRulePresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllRulePresentResp proto.InternalMessageInfo

func (m *GetAllRulePresentResp) GetPresentConfigs() []*SimplePresentConfig {
	if m != nil {
		return m.PresentConfigs
	}
	return nil
}

type SimplePresentConfig struct {
	PresentId            uint32   `protobuf:"varint,1,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	PresentName          string   `protobuf:"bytes,2,opt,name=present_name,json=presentName,proto3" json:"present_name,omitempty"`
	Price                uint32   `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`
	IconUrl              string   `protobuf:"bytes,4,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SimplePresentConfig) Reset()         { *m = SimplePresentConfig{} }
func (m *SimplePresentConfig) String() string { return proto.CompactTextString(m) }
func (*SimplePresentConfig) ProtoMessage()    {}
func (*SimplePresentConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{12}
}
func (m *SimplePresentConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimplePresentConfig.Unmarshal(m, b)
}
func (m *SimplePresentConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimplePresentConfig.Marshal(b, m, deterministic)
}
func (dst *SimplePresentConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimplePresentConfig.Merge(dst, src)
}
func (m *SimplePresentConfig) XXX_Size() int {
	return xxx_messageInfo_SimplePresentConfig.Size(m)
}
func (m *SimplePresentConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_SimplePresentConfig.DiscardUnknown(m)
}

var xxx_messageInfo_SimplePresentConfig proto.InternalMessageInfo

func (m *SimplePresentConfig) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *SimplePresentConfig) GetPresentName() string {
	if m != nil {
		return m.PresentName
	}
	return ""
}

func (m *SimplePresentConfig) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *SimplePresentConfig) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

type CheckPresentLimitReq struct {
	SendUid              uint32   `protobuf:"varint,1,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	ReceiveUid           []uint32 `protobuf:"varint,2,rep,packed,name=receive_uid,json=receiveUid,proto3" json:"receive_uid,omitempty"`
	PresentId            uint32   `protobuf:"varint,3,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Count                uint32   `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckPresentLimitReq) Reset()         { *m = CheckPresentLimitReq{} }
func (m *CheckPresentLimitReq) String() string { return proto.CompactTextString(m) }
func (*CheckPresentLimitReq) ProtoMessage()    {}
func (*CheckPresentLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{13}
}
func (m *CheckPresentLimitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckPresentLimitReq.Unmarshal(m, b)
}
func (m *CheckPresentLimitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckPresentLimitReq.Marshal(b, m, deterministic)
}
func (dst *CheckPresentLimitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckPresentLimitReq.Merge(dst, src)
}
func (m *CheckPresentLimitReq) XXX_Size() int {
	return xxx_messageInfo_CheckPresentLimitReq.Size(m)
}
func (m *CheckPresentLimitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckPresentLimitReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckPresentLimitReq proto.InternalMessageInfo

func (m *CheckPresentLimitReq) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *CheckPresentLimitReq) GetReceiveUid() []uint32 {
	if m != nil {
		return m.ReceiveUid
	}
	return nil
}

func (m *CheckPresentLimitReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *CheckPresentLimitReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CheckPresentLimitReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type CheckPresentLimitResp struct {
	IsAllowed            bool        `protobuf:"varint,1,opt,name=is_allowed,json=isAllowed,proto3" json:"is_allowed,omitempty"`
	Reason               *ErrContent `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *CheckPresentLimitResp) Reset()         { *m = CheckPresentLimitResp{} }
func (m *CheckPresentLimitResp) String() string { return proto.CompactTextString(m) }
func (*CheckPresentLimitResp) ProtoMessage()    {}
func (*CheckPresentLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{14}
}
func (m *CheckPresentLimitResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckPresentLimitResp.Unmarshal(m, b)
}
func (m *CheckPresentLimitResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckPresentLimitResp.Marshal(b, m, deterministic)
}
func (dst *CheckPresentLimitResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckPresentLimitResp.Merge(dst, src)
}
func (m *CheckPresentLimitResp) XXX_Size() int {
	return xxx_messageInfo_CheckPresentLimitResp.Size(m)
}
func (m *CheckPresentLimitResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckPresentLimitResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckPresentLimitResp proto.InternalMessageInfo

func (m *CheckPresentLimitResp) GetIsAllowed() bool {
	if m != nil {
		return m.IsAllowed
	}
	return false
}

func (m *CheckPresentLimitResp) GetReason() *ErrContent {
	if m != nil {
		return m.Reason
	}
	return nil
}

type ErrContent struct {
	SubErrCode           ErrorCode `protobuf:"varint,1,opt,name=sub_err_code,json=subErrCode,proto3,enum=present_limit.ErrorCode" json:"sub_err_code,omitempty"`
	PopupContent         string    `protobuf:"bytes,2,opt,name=popup_content,json=popupContent,proto3" json:"popup_content,omitempty"`
	PopupButtonAText     string    `protobuf:"bytes,3,opt,name=popup_button_a_text,json=popupButtonAText,proto3" json:"popup_button_a_text,omitempty"`
	PopupButtonAUrl      string    `protobuf:"bytes,4,opt,name=popup_button_a_url,json=popupButtonAUrl,proto3" json:"popup_button_a_url,omitempty"`
	PopupButtonBText     string    `protobuf:"bytes,5,opt,name=popup_button_b_text,json=popupButtonBText,proto3" json:"popup_button_b_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ErrContent) Reset()         { *m = ErrContent{} }
func (m *ErrContent) String() string { return proto.CompactTextString(m) }
func (*ErrContent) ProtoMessage()    {}
func (*ErrContent) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{15}
}
func (m *ErrContent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ErrContent.Unmarshal(m, b)
}
func (m *ErrContent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ErrContent.Marshal(b, m, deterministic)
}
func (dst *ErrContent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ErrContent.Merge(dst, src)
}
func (m *ErrContent) XXX_Size() int {
	return xxx_messageInfo_ErrContent.Size(m)
}
func (m *ErrContent) XXX_DiscardUnknown() {
	xxx_messageInfo_ErrContent.DiscardUnknown(m)
}

var xxx_messageInfo_ErrContent proto.InternalMessageInfo

func (m *ErrContent) GetSubErrCode() ErrorCode {
	if m != nil {
		return m.SubErrCode
	}
	return ErrorCode_ERROR_CODE_UNSPECIFIED
}

func (m *ErrContent) GetPopupContent() string {
	if m != nil {
		return m.PopupContent
	}
	return ""
}

func (m *ErrContent) GetPopupButtonAText() string {
	if m != nil {
		return m.PopupButtonAText
	}
	return ""
}

func (m *ErrContent) GetPopupButtonAUrl() string {
	if m != nil {
		return m.PopupButtonAUrl
	}
	return ""
}

func (m *ErrContent) GetPopupButtonBText() string {
	if m != nil {
		return m.PopupButtonBText
	}
	return ""
}

type AddBackpackPresentLimitReq struct {
	SendUid              uint32   `protobuf:"varint,1,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	ReceiveUid           []uint32 `protobuf:"varint,2,rep,packed,name=receive_uid,json=receiveUid,proto3" json:"receive_uid,omitempty"`
	PresentId            uint32   `protobuf:"varint,3,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Count                uint32   `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddBackpackPresentLimitReq) Reset()         { *m = AddBackpackPresentLimitReq{} }
func (m *AddBackpackPresentLimitReq) String() string { return proto.CompactTextString(m) }
func (*AddBackpackPresentLimitReq) ProtoMessage()    {}
func (*AddBackpackPresentLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{16}
}
func (m *AddBackpackPresentLimitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBackpackPresentLimitReq.Unmarshal(m, b)
}
func (m *AddBackpackPresentLimitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBackpackPresentLimitReq.Marshal(b, m, deterministic)
}
func (dst *AddBackpackPresentLimitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBackpackPresentLimitReq.Merge(dst, src)
}
func (m *AddBackpackPresentLimitReq) XXX_Size() int {
	return xxx_messageInfo_AddBackpackPresentLimitReq.Size(m)
}
func (m *AddBackpackPresentLimitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBackpackPresentLimitReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddBackpackPresentLimitReq proto.InternalMessageInfo

func (m *AddBackpackPresentLimitReq) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *AddBackpackPresentLimitReq) GetReceiveUid() []uint32 {
	if m != nil {
		return m.ReceiveUid
	}
	return nil
}

func (m *AddBackpackPresentLimitReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *AddBackpackPresentLimitReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddBackpackPresentLimitReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type AddBackpackPresentLimitResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddBackpackPresentLimitResp) Reset()         { *m = AddBackpackPresentLimitResp{} }
func (m *AddBackpackPresentLimitResp) String() string { return proto.CompactTextString(m) }
func (*AddBackpackPresentLimitResp) ProtoMessage()    {}
func (*AddBackpackPresentLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_limit_c746ad8a98b54421, []int{17}
}
func (m *AddBackpackPresentLimitResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBackpackPresentLimitResp.Unmarshal(m, b)
}
func (m *AddBackpackPresentLimitResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBackpackPresentLimitResp.Marshal(b, m, deterministic)
}
func (dst *AddBackpackPresentLimitResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBackpackPresentLimitResp.Merge(dst, src)
}
func (m *AddBackpackPresentLimitResp) XXX_Size() int {
	return xxx_messageInfo_AddBackpackPresentLimitResp.Size(m)
}
func (m *AddBackpackPresentLimitResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBackpackPresentLimitResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddBackpackPresentLimitResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*RuleSet)(nil), "present_limit.RuleSet")
	proto.RegisterType((*PresentLimitScope)(nil), "present_limit.PresentLimitScope")
	proto.RegisterType((*PresentTargetLimit)(nil), "present_limit.PresentTargetLimit")
	proto.RegisterType((*PresentSceneLimit)(nil), "present_limit.PresentSceneLimit")
	proto.RegisterType((*BackpackPriceLimit)(nil), "present_limit.BackpackPriceLimit")
	proto.RegisterType((*BackpackLimitScope)(nil), "present_limit.BackpackLimitScope")
	proto.RegisterType((*GetRuleSetReq)(nil), "present_limit.GetRuleSetReq")
	proto.RegisterType((*GetRuleSetResp)(nil), "present_limit.GetRuleSetResp")
	proto.RegisterType((*UpdateRuleSetReq)(nil), "present_limit.UpdateRuleSetReq")
	proto.RegisterType((*UpdateRuleSetResp)(nil), "present_limit.UpdateRuleSetResp")
	proto.RegisterType((*GetAllRulePresentReq)(nil), "present_limit.GetAllRulePresentReq")
	proto.RegisterType((*GetAllRulePresentResp)(nil), "present_limit.GetAllRulePresentResp")
	proto.RegisterType((*SimplePresentConfig)(nil), "present_limit.SimplePresentConfig")
	proto.RegisterType((*CheckPresentLimitReq)(nil), "present_limit.CheckPresentLimitReq")
	proto.RegisterType((*CheckPresentLimitResp)(nil), "present_limit.CheckPresentLimitResp")
	proto.RegisterType((*ErrContent)(nil), "present_limit.ErrContent")
	proto.RegisterType((*AddBackpackPresentLimitReq)(nil), "present_limit.AddBackpackPresentLimitReq")
	proto.RegisterType((*AddBackpackPresentLimitResp)(nil), "present_limit.AddBackpackPresentLimitResp")
	proto.RegisterEnum("present_limit.LimitValueType", LimitValueType_name, LimitValueType_value)
	proto.RegisterEnum("present_limit.ChangeScopeType", ChangeScopeType_name, ChangeScopeType_value)
	proto.RegisterEnum("present_limit.ErrorCode", ErrorCode_name, ErrorCode_value)
	proto.RegisterEnum("present_limit.BackpackPriceLimit_PriceLimitType", BackpackPriceLimit_PriceLimitType_name, BackpackPriceLimit_PriceLimitType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PresentLimitClient is the client API for PresentLimit service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PresentLimitClient interface {
	// 更新规则集
	UpdateRuleSet(ctx context.Context, in *UpdateRuleSetReq, opts ...grpc.CallOption) (*UpdateRuleSetResp, error)
	// 获取规则集的详细信息
	GetRuleSet(ctx context.Context, in *GetRuleSetReq, opts ...grpc.CallOption) (*GetRuleSetResp, error)
	// 获取所有符合规则的礼物配置
	GetAllRulePresent(ctx context.Context, in *GetAllRulePresentReq, opts ...grpc.CallOption) (*GetAllRulePresentResp, error)
	// 检查礼物是否能够送出
	CheckPresentLimit(ctx context.Context, in *CheckPresentLimitReq, opts ...grpc.CallOption) (*CheckPresentLimitResp, error)
	// 受限的背包礼物送出后加每日累计值
	AddBackpackPresentLimit(ctx context.Context, in *AddBackpackPresentLimitReq, opts ...grpc.CallOption) (*AddBackpackPresentLimitResp, error)
}

type presentLimitClient struct {
	cc *grpc.ClientConn
}

func NewPresentLimitClient(cc *grpc.ClientConn) PresentLimitClient {
	return &presentLimitClient{cc}
}

func (c *presentLimitClient) UpdateRuleSet(ctx context.Context, in *UpdateRuleSetReq, opts ...grpc.CallOption) (*UpdateRuleSetResp, error) {
	out := new(UpdateRuleSetResp)
	err := c.cc.Invoke(ctx, "/present_limit.PresentLimit/UpdateRuleSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentLimitClient) GetRuleSet(ctx context.Context, in *GetRuleSetReq, opts ...grpc.CallOption) (*GetRuleSetResp, error) {
	out := new(GetRuleSetResp)
	err := c.cc.Invoke(ctx, "/present_limit.PresentLimit/GetRuleSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentLimitClient) GetAllRulePresent(ctx context.Context, in *GetAllRulePresentReq, opts ...grpc.CallOption) (*GetAllRulePresentResp, error) {
	out := new(GetAllRulePresentResp)
	err := c.cc.Invoke(ctx, "/present_limit.PresentLimit/GetAllRulePresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentLimitClient) CheckPresentLimit(ctx context.Context, in *CheckPresentLimitReq, opts ...grpc.CallOption) (*CheckPresentLimitResp, error) {
	out := new(CheckPresentLimitResp)
	err := c.cc.Invoke(ctx, "/present_limit.PresentLimit/CheckPresentLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentLimitClient) AddBackpackPresentLimit(ctx context.Context, in *AddBackpackPresentLimitReq, opts ...grpc.CallOption) (*AddBackpackPresentLimitResp, error) {
	out := new(AddBackpackPresentLimitResp)
	err := c.cc.Invoke(ctx, "/present_limit.PresentLimit/AddBackpackPresentLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PresentLimitServer is the server API for PresentLimit service.
type PresentLimitServer interface {
	// 更新规则集
	UpdateRuleSet(context.Context, *UpdateRuleSetReq) (*UpdateRuleSetResp, error)
	// 获取规则集的详细信息
	GetRuleSet(context.Context, *GetRuleSetReq) (*GetRuleSetResp, error)
	// 获取所有符合规则的礼物配置
	GetAllRulePresent(context.Context, *GetAllRulePresentReq) (*GetAllRulePresentResp, error)
	// 检查礼物是否能够送出
	CheckPresentLimit(context.Context, *CheckPresentLimitReq) (*CheckPresentLimitResp, error)
	// 受限的背包礼物送出后加每日累计值
	AddBackpackPresentLimit(context.Context, *AddBackpackPresentLimitReq) (*AddBackpackPresentLimitResp, error)
}

func RegisterPresentLimitServer(s *grpc.Server, srv PresentLimitServer) {
	s.RegisterService(&_PresentLimit_serviceDesc, srv)
}

func _PresentLimit_UpdateRuleSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRuleSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentLimitServer).UpdateRuleSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_limit.PresentLimit/UpdateRuleSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentLimitServer).UpdateRuleSet(ctx, req.(*UpdateRuleSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentLimit_GetRuleSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRuleSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentLimitServer).GetRuleSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_limit.PresentLimit/GetRuleSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentLimitServer).GetRuleSet(ctx, req.(*GetRuleSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentLimit_GetAllRulePresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllRulePresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentLimitServer).GetAllRulePresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_limit.PresentLimit/GetAllRulePresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentLimitServer).GetAllRulePresent(ctx, req.(*GetAllRulePresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentLimit_CheckPresentLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPresentLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentLimitServer).CheckPresentLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_limit.PresentLimit/CheckPresentLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentLimitServer).CheckPresentLimit(ctx, req.(*CheckPresentLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentLimit_AddBackpackPresentLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBackpackPresentLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentLimitServer).AddBackpackPresentLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_limit.PresentLimit/AddBackpackPresentLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentLimitServer).AddBackpackPresentLimit(ctx, req.(*AddBackpackPresentLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PresentLimit_serviceDesc = grpc.ServiceDesc{
	ServiceName: "present_limit.PresentLimit",
	HandlerType: (*PresentLimitServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateRuleSet",
			Handler:    _PresentLimit_UpdateRuleSet_Handler,
		},
		{
			MethodName: "GetRuleSet",
			Handler:    _PresentLimit_GetRuleSet_Handler,
		},
		{
			MethodName: "GetAllRulePresent",
			Handler:    _PresentLimit_GetAllRulePresent_Handler,
		},
		{
			MethodName: "CheckPresentLimit",
			Handler:    _PresentLimit_CheckPresentLimit_Handler,
		},
		{
			MethodName: "AddBackpackPresentLimit",
			Handler:    _PresentLimit_AddBackpackPresentLimit_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/present-limit/present-limit.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/present-limit/present-limit.proto", fileDescriptor_present_limit_c746ad8a98b54421)
}

var fileDescriptor_present_limit_c746ad8a98b54421 = []byte{
	// 1467 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x57, 0xcd, 0x6e, 0xdb, 0x48,
	0x12, 0x5e, 0x4a, 0xf1, 0x8f, 0xca, 0x96, 0x45, 0xb7, 0xed, 0xc4, 0x96, 0xe3, 0x9f, 0x30, 0xbb,
	0x41, 0xe2, 0xdd, 0xd8, 0xb1, 0x17, 0x8b, 0x05, 0xf6, 0x26, 0xd3, 0xb4, 0xa2, 0xb5, 0x2c, 0x09,
	0x94, 0xec, 0xc5, 0x06, 0xd8, 0x6d, 0x50, 0x64, 0x5b, 0x21, 0x4c, 0x93, 0x0c, 0x9b, 0x72, 0x92,
	0xeb, 0x9e, 0xf6, 0x34, 0xc0, 0xbc, 0xc4, 0x9c, 0x66, 0x2e, 0xf3, 0x12, 0xf3, 0x08, 0x73, 0x1c,
	0x20, 0x4f, 0x31, 0xc7, 0x41, 0xff, 0x50, 0xa2, 0x48, 0xd9, 0x19, 0x04, 0x73, 0x98, 0x9b, 0xba,
	0xbe, 0xaf, 0xab, 0xab, 0xaa, 0xbf, 0xae, 0xa2, 0xe0, 0x55, 0x1c, 0x1f, 0xbc, 0x1b, 0xba, 0xf6,
	0x35, 0x75, 0xbd, 0x5b, 0x12, 0x1d, 0x84, 0x11, 0xa1, 0xc4, 0x8f, 0x5f, 0x7a, 0xee, 0x8d, 0x1b,
	0x4f, 0xae, 0xf6, 0xc3, 0x28, 0x88, 0x03, 0x54, 0x96, 0x46, 0xcc, 0x8d, 0xda, 0x8f, 0x05, 0x98,
	0x33, 0x87, 0x1e, 0xe9, 0x92, 0x18, 0x75, 0x60, 0x65, 0x02, 0xc4, 0xd4, 0x0e, 0x42, 0xb2, 0xae,
	0xec, 0x2a, 0xcf, 0x17, 0x8e, 0x76, 0xf7, 0x27, 0xb0, 0xfd, 0x8e, 0x58, 0x35, 0xd9, 0xa2, 0xcb,
	0x78, 0xe6, 0x72, 0x98, 0x35, 0xa5, 0x3d, 0x52, 0x9b, 0xf8, 0x44, 0xec, 0x5d, 0x2f, 0xdc, 0xe7,
	0xb1, 0xcb, 0x88, 0xdc, 0xc7, 0xc8, 0xe3, 0xd8, 0x84, 0xba, 0xb0, 0x9a, 0xec, 0x8a, 0xad, 0x68,
	0x40, 0xe4, 0xe6, 0xf5, 0xe2, 0x6e, 0xf1, 0xf9, 0xc2, 0xd1, 0x93, 0xe9, 0x2e, 0x7b, 0x9c, 0x29,
	0x7c, 0xa2, 0x30, 0x67, 0x63, 0x4e, 0xfb, 0x96, 0x7d, 0x1d, 0x5a, 0xf6, 0x35, 0x0e, 0x23, 0xd7,
	0x4e, 0xe2, 0x7c, 0xc0, 0xe3, 0xcc, 0x3a, 0x3d, 0x96, 0xd4, 0x0e, 0x63, 0x4a, 0xa7, 0xfd, 0x9c,
	0x4d, 0xfb, 0x5e, 0x81, 0xe5, 0x5c, 0x91, 0xd0, 0x21, 0xac, 0xb9, 0x14, 0x5b, 0x9e, 0x87, 0x47,
	0x27, 0x0e, 0xdc, 0xab, 0x98, 0xf2, 0x2a, 0xcf, 0x9b, 0xc8, 0xa5, 0x35, 0xcf, 0x4b, 0x4e, 0xa8,
	0x33, 0x24, 0xb5, 0xc5, 0xf5, 0x63, 0xf7, 0xc6, 0x8a, 0x89, 0xdc, 0x52, 0x48, 0x6d, 0x69, 0x48,
	0x48, 0x6c, 0xf9, 0x3b, 0xac, 0x47, 0x2e, 0xbd, 0xc6, 0x76, 0xe0, 0xc7, 0x51, 0xe0, 0xe1, 0xfe,
	0x90, 0xba, 0x3e, 0xa1, 0x14, 0xbb, 0x0e, 0xaf, 0x54, 0xd9, 0x5c, 0x63, 0xb8, 0x2e, 0xe0, 0x63,
	0x89, 0x36, 0x1c, 0xed, 0xab, 0x02, 0xa0, 0x7c, 0xd1, 0xd0, 0x0e, 0x2c, 0xf4, 0xc9, 0xc0, 0xf5,
	0x45, 0x75, 0x78, 0xac, 0x65, 0x13, 0xb8, 0x89, 0x67, 0x8c, 0x36, 0xa1, 0x44, 0x7c, 0x47, 0xc2,
	0x05, 0x0e, 0xcf, 0x13, 0xdf, 0x11, 0x60, 0x1d, 0x54, 0xa1, 0xa7, 0x5b, 0xcb, 0x1b, 0x12, 0x1c,
	0x7f, 0x0c, 0xc9, 0x7a, 0x71, 0x57, 0x79, 0xbe, 0x74, 0xb4, 0x95, 0x29, 0x2d, 0x3f, 0xed, 0x92,
	0xb1, 0x7a, 0x1f, 0x43, 0x62, 0x2e, 0x79, 0x13, 0x6b, 0xf4, 0x17, 0x40, 0x57, 0xc4, 0xf3, 0x82,
	0xf7, 0xd2, 0xd3, 0xf8, 0x96, 0xca, 0xa6, 0x2a, 0x10, 0x4e, 0x16, 0x41, 0x3f, 0x83, 0x8a, 0x64,
	0x7b, 0xb7, 0x92, 0x3a, 0xc3, 0xa9, 0x65, 0x61, 0x6e, 0xde, 0x0a, 0xde, 0x26, 0x94, 0x1c, 0x2b,
	0xd1, 0xd1, 0xac, 0x88, 0xdd, 0xb1, 0x44, 0xe6, 0xda, 0x7f, 0x46, 0x97, 0x98, 0x12, 0xe1, 0x1e,
	0x2c, 0xbb, 0x14, 0x7b, 0x81, 0x7d, 0x4d, 0x1c, 0x6c, 0xbf, 0xb5, 0x7c, 0x9f, 0x78, 0xf2, 0x02,
	0x2b, 0x2e, 0x6d, 0x72, 0xbb, 0x2e, 0xcc, 0xe8, 0x31, 0x80, 0x4b, 0xb1, 0x1f, 0xc4, 0x38, 0x1c,
	0xd8, 0xf2, 0xca, 0xe6, 0x5d, 0xda, 0x0a, 0xe2, 0xce, 0xc0, 0xd6, 0xbe, 0x2b, 0x00, 0xca, 0xeb,
	0x09, 0xbd, 0x01, 0x35, 0xa5, 0x43, 0x51, 0x31, 0x85, 0x57, 0xec, 0xd5, 0x67, 0xc5, 0xb8, 0x3f,
	0xfe, 0x29, 0x8a, 0x18, 0x4e, 0xac, 0xd1, 0x09, 0x2c, 0xa6, 0x5e, 0x37, 0x53, 0x51, 0xf1, 0x1e,
	0x91, 0xa7, 0xde, 0xf7, 0x82, 0x37, 0xfa, 0x4d, 0xb5, 0x21, 0x2c, 0x4d, 0x9e, 0x83, 0x76, 0xe1,
	0x71, 0xc7, 0x6c, 0xe8, 0x06, 0x6e, 0x36, 0xce, 0x1b, 0x3d, 0xdc, 0xfb, 0x77, 0xc7, 0xc0, 0x17,
	0xad, 0x6e, 0xc7, 0xd0, 0x1b, 0xa7, 0x0d, 0xe3, 0x44, 0xfd, 0x03, 0x7a, 0x02, 0x5b, 0x39, 0xc6,
	0xa9, 0xd1, 0x6c, 0xb6, 0xff, 0x85, 0x2f, 0x6b, 0xcd, 0x0b, 0x43, 0x55, 0xd0, 0x36, 0x54, 0xef,
	0xa2, 0x34, 0x2f, 0xd5, 0x82, 0xf6, 0x83, 0x32, 0xae, 0x57, 0xea, 0x55, 0x8d, 0xf4, 0xc9, 0x75,
	0x31, 0xa1, 0x4f, 0x2e, 0x88, 0x44, 0x9f, 0x02, 0x1e, 0xeb, 0x53, 0x80, 0xbf, 0x99, 0x3e, 0xf7,
	0x60, 0x59, 0x5e, 0x58, 0x9f, 0x58, 0x49, 0x30, 0x42, 0x9e, 0x15, 0x0e, 0xf4, 0x98, 0x9d, 0xf3,
	0xb5, 0x0a, 0x94, 0xeb, 0x24, 0x96, 0xad, 0xd7, 0x24, 0xef, 0x34, 0x1d, 0x96, 0xd2, 0x06, 0x1a,
	0xa2, 0x43, 0x98, 0xa7, 0x24, 0xc6, 0xae, 0x7f, 0x15, 0xc8, 0x26, 0xfc, 0x30, 0x13, 0x4f, 0xc2,
	0x9e, 0xa3, 0x24, 0x6e, 0xf8, 0x57, 0x81, 0xf6, 0xb5, 0x02, 0xea, 0x45, 0xe8, 0x58, 0x31, 0x19,
	0x7b, 0xfe, 0x02, 0x3f, 0xe8, 0x9f, 0xb0, 0xcc, 0x74, 0x3d, 0x20, 0x42, 0x25, 0xa2, 0x26, 0x05,
	0x5e, 0x93, 0xed, 0xcc, 0x5e, 0x9d, 0xf3, 0xf8, 0x3d, 0xf0, 0xa2, 0x54, 0xec, 0x49, 0x83, 0x76,
	0x0a, 0xcb, 0x99, 0x90, 0xbe, 0x2c, 0xb7, 0xff, 0x29, 0xb0, 0x5a, 0x27, 0x71, 0xcd, 0xf3, 0x18,
	0x24, 0x5f, 0xa5, 0xc8, 0xef, 0x8e, 0x06, 0xa9, 0x7c, 0x51, 0x83, 0x2c, 0xdc, 0xd7, 0x20, 0x1d,
	0x58, 0x9b, 0x12, 0x03, 0x0d, 0xd1, 0x19, 0x54, 0x92, 0xf8, 0xed, 0xc0, 0xbf, 0x72, 0x07, 0xec,
	0x78, 0xf6, 0xb2, 0xb4, 0x4c, 0x5e, 0x5d, 0xf7, 0x26, 0x1c, 0x6d, 0xd5, 0x39, 0x95, 0xbd, 0xd1,
	0xd4, 0x92, 0x6a, 0xff, 0x57, 0x60, 0x65, 0x0a, 0x0f, 0x6d, 0x01, 0x24, 0xce, 0x5c, 0x47, 0xca,
	0xbc, 0x24, 0x2d, 0x0d, 0x07, 0x3d, 0x81, 0xc5, 0x04, 0xf6, 0xad, 0x1b, 0x71, 0x61, 0x25, 0x73,
	0x41, 0xda, 0x5a, 0xd6, 0x0d, 0x41, 0xab, 0x30, 0x23, 0x9a, 0x74, 0x91, 0x6f, 0x16, 0x0b, 0xb4,
	0x01, 0xf3, 0xae, 0x1d, 0xf8, 0x78, 0x18, 0x79, 0x5c, 0xaf, 0x25, 0x73, 0x8e, 0xad, 0x2f, 0x22,
	0x4f, 0xfb, 0x46, 0x81, 0x55, 0xfd, 0x2d, 0x61, 0x1d, 0x66, 0x3c, 0xcb, 0x58, 0xd5, 0x37, 0xd8,
	0x0d, 0xfa, 0x0e, 0x1e, 0x8e, 0x22, 0x99, 0x63, 0xeb, 0x0b, 0xd7, 0x61, 0xcf, 0x31, 0x22, 0x36,
	0x71, 0x6f, 0x09, 0x47, 0x45, 0x41, 0x41, 0x9a, 0x18, 0x61, 0x32, 0x8f, 0x62, 0x36, 0x8f, 0x2d,
	0x00, 0xd9, 0x55, 0x19, 0x2c, 0x1e, 0x50, 0x49, 0x5a, 0x1a, 0x0e, 0xcb, 0xc1, 0x0e, 0x86, 0x7e,
	0xd2, 0xce, 0xc5, 0x42, 0x73, 0x61, 0x6d, 0x4a, 0x9c, 0x34, 0x64, 0xde, 0x84, 0x3c, 0x82, 0xf7,
	0xc4, 0x91, 0x9a, 0x28, 0x71, 0x4d, 0x30, 0x03, 0x3a, 0x84, 0xd9, 0x88, 0x58, 0x34, 0xf0, 0xe5,
	0x67, 0xc9, 0x46, 0xe6, 0xbe, 0x8c, 0x28, 0x62, 0x32, 0x60, 0xf7, 0x2c, 0x89, 0xda, 0xcf, 0x0a,
	0xc0, 0xd8, 0x8c, 0xfe, 0x01, 0x8b, 0x74, 0xd8, 0xc7, 0x24, 0x8a, 0xb0, 0x1d, 0x38, 0x49, 0xa7,
	0x5e, 0xcf, 0xfb, 0x09, 0x22, 0x3d, 0x70, 0x88, 0x09, 0x74, 0xd8, 0xe7, 0xdb, 0x1d, 0x82, 0x9e,
	0x42, 0x39, 0x0c, 0xc2, 0x61, 0xc8, 0x95, 0x48, 0xfc, 0x58, 0xde, 0xd9, 0x22, 0x37, 0x26, 0x07,
	0xbc, 0x84, 0x15, 0x41, 0xea, 0x0f, 0xe3, 0x38, 0xf0, 0xb1, 0x85, 0x63, 0xf2, 0x21, 0xe6, 0x75,
	0x2b, 0x99, 0x2a, 0x87, 0x8e, 0x39, 0x52, 0xeb, 0x91, 0x0f, 0x31, 0xfa, 0x33, 0xa0, 0x0c, 0x7d,
	0x7c, 0xaf, 0x95, 0x34, 0xfb, 0x22, 0xf2, 0x72, 0xbe, 0xfb, 0xc2, 0xf7, 0x4c, 0xce, 0xf7, 0x31,
	0xf3, 0xad, 0x7d, 0xab, 0x40, 0xb5, 0xe6, 0x38, 0xe3, 0xb1, 0xf3, 0xfb, 0x16, 0xc5, 0x16, 0x6c,
	0xde, 0x19, 0x2d, 0x0d, 0xf7, 0x28, 0x2c, 0x4d, 0xb6, 0x74, 0x36, 0xc5, 0xc4, 0xe8, 0xe1, 0x13,
	0x69, 0xda, 0x14, 0xdb, 0x86, 0x6a, 0x8e, 0xd1, 0x34, 0x4e, 0x7b, 0xb8, 0xdd, 0x31, 0x5a, 0xaa,
	0x82, 0x76, 0x60, 0x33, 0x87, 0x9b, 0x8d, 0xfa, 0x6b, 0x49, 0x28, 0xec, 0x7d, 0x52, 0xa0, 0x92,
	0x69, 0x9a, 0x6c, 0x34, 0xea, 0xaf, 0x6b, 0xad, 0xba, 0x81, 0xbb, 0x7a, 0xbb, 0x33, 0xf5, 0xdc,
	0x17, 0xf0, 0xa7, 0x3c, 0xa5, 0x63, 0x1a, 0x5d, 0xa3, 0xd5, 0x93, 0xe3, 0x92, 0x03, 0xaa, 0x72,
	0x3f, 0xb5, 0xab, 0x1b, 0x2d, 0x39, 0x5f, 0xd5, 0x02, 0xda, 0x83, 0x67, 0x77, 0x53, 0x7b, 0x35,
	0xb3, 0x6e, 0x48, 0xe7, 0x6a, 0x71, 0x3a, 0xf7, 0xb8, 0xa6, 0x9f, 0x75, 0x6a, 0xfa, 0x19, 0x4e,
	0xcd, 0x6d, 0xf5, 0xc1, 0xde, 0xa7, 0x02, 0x94, 0x46, 0x8a, 0x47, 0x55, 0x78, 0x68, 0x98, 0x66,
	0xdb, 0xc4, 0x7a, 0xfb, 0x24, 0x9b, 0xd7, 0x16, 0x6c, 0xa4, 0x30, 0x76, 0x40, 0xcb, 0x68, 0xe2,
	0x66, 0x5b, 0x3f, 0x33, 0x4e, 0x54, 0x05, 0x3d, 0x82, 0x95, 0x14, 0xdc, 0xa9, 0xeb, 0xd8, 0x6c,
	0xb7, 0xcf, 0xd5, 0x02, 0x2b, 0x59, 0x0a, 0x48, 0x7f, 0x47, 0x8c, 0x02, 0xde, 0x86, 0x6a, 0x9e,
	0xd2, 0xbc, 0x4c, 0x82, 0x44, 0xeb, 0xb0, 0x9a, 0xc2, 0x4f, 0x6a, 0x49, 0xaa, 0x33, 0xe8, 0x29,
	0xec, 0xa4, 0x90, 0xa9, 0x39, 0xce, 0xa2, 0x0d, 0x58, 0x4b, 0x91, 0x5a, 0xed, 0x9e, 0x3c, 0x42,
	0x9d, 0xcb, 0x40, 0x49, 0x52, 0x8d, 0x73, 0x75, 0x9e, 0x5d, 0xce, 0x9d, 0xf9, 0xe2, 0x56, 0x7b,
	0x9c, 0x62, 0x29, 0x13, 0x7f, 0x42, 0xad, 0x35, 0x9b, 0xf8, 0xbc, 0xa1, 0xab, 0x70, 0xf4, 0x53,
	0x11, 0x16, 0xd3, 0x9a, 0x46, 0x26, 0x94, 0x27, 0x46, 0x2d, 0xda, 0xc9, 0x34, 0xa1, 0xec, 0xb7,
	0x41, 0x75, 0xf7, 0x7e, 0x02, 0x0d, 0x51, 0x03, 0x60, 0xfc, 0x5d, 0x82, 0x1e, 0x67, 0xf8, 0x13,
	0xdf, 0x30, 0xd5, 0xad, 0x7b, 0x50, 0x1a, 0xa2, 0xff, 0xc2, 0x72, 0x6e, 0x78, 0xa2, 0xa7, 0xf9,
	0x3d, 0xb9, 0x11, 0x5f, 0xfd, 0xe3, 0xe7, 0x49, 0xc2, 0x7f, 0x6e, 0x04, 0xe4, 0xfc, 0x4f, 0x1b,
	0x66, 0x39, 0xff, 0xd3, 0x27, 0x49, 0x08, 0x8f, 0xee, 0xe8, 0x26, 0xe8, 0x45, 0xc6, 0xc1, 0xdd,
	0x3d, 0xb2, 0xba, 0xf7, 0x6b, 0xa9, 0x34, 0x3c, 0x3e, 0x7c, 0x73, 0x30, 0x08, 0x3c, 0xcb, 0x1f,
	0xec, 0xff, 0xed, 0x28, 0x8e, 0xf7, 0xed, 0xe0, 0xe6, 0x80, 0xff, 0x8d, 0xb7, 0x03, 0xef, 0x80,
	0x92, 0xe8, 0xd6, 0xb5, 0x09, 0x9d, 0xfc, 0x9b, 0xdf, 0x9f, 0xe5, 0x84, 0xbf, 0xfe, 0x12, 0x00,
	0x00, 0xff, 0xff, 0xaa, 0x00, 0x3b, 0x1d, 0x1b, 0x10, 0x00, 0x00,
}
