// Code generated by protoc-gen-go. DO NOT EDIT.
// source: user-auth/user-auth.proto

package user_auth // import "golang.52tt.com/protocol/services/user-auth"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 授权类型
type AuthType int32

const (
	AuthType_AUTH_TYPE_UNKNOWN AuthType = 0
	AuthType_AUTH_TYPE_COMMON  AuthType = 1
)

var AuthType_name = map[int32]string{
	0: "AUTH_TYPE_UNKNOWN",
	1: "AUTH_TYPE_COMMON",
}
var AuthType_value = map[string]int32{
	"AUTH_TYPE_UNKNOWN": 0,
	"AUTH_TYPE_COMMON":  1,
}

func (x AuthType) String() string {
	return proto.EnumName(AuthType_name, int32(x))
}
func (AuthType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_c09bcb8e76df3376, []int{0}
}

// 申请状态
type ApplyState int32

const (
	ApplyState_APPLY_STATE_UNKNOWN ApplyState = 0
	ApplyState_APPLY_STATE_NORMAL  ApplyState = 1
)

var ApplyState_name = map[int32]string{
	0: "APPLY_STATE_UNKNOWN",
	1: "APPLY_STATE_NORMAL",
}
var ApplyState_value = map[string]int32{
	"APPLY_STATE_UNKNOWN": 0,
	"APPLY_STATE_NORMAL":  1,
}

func (x ApplyState) String() string {
	return proto.EnumName(ApplyState_name, int32(x))
}
func (ApplyState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_c09bcb8e76df3376, []int{1}
}

// 授权状态
type AuthState int32

const (
	AuthState_AUTH_STATE_UNKNOWN AuthState = 0
	AuthState_AUTH_STATE_PASS    AuthState = 1
	AuthState_AUTH_STATE_REJECT  AuthState = 2
)

var AuthState_name = map[int32]string{
	0: "AUTH_STATE_UNKNOWN",
	1: "AUTH_STATE_PASS",
	2: "AUTH_STATE_REJECT",
}
var AuthState_value = map[string]int32{
	"AUTH_STATE_UNKNOWN": 0,
	"AUTH_STATE_PASS":    1,
	"AUTH_STATE_REJECT":  2,
}

func (x AuthState) String() string {
	return proto.EnumName(AuthState_name, int32(x))
}
func (AuthState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_c09bcb8e76df3376, []int{2}
}

type CreateAuthApplyReq struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Scene                string            `protobuf:"bytes,2,opt,name=scene,proto3" json:"scene,omitempty"`
	Ttl                  int64             `protobuf:"varint,3,opt,name=ttl,proto3" json:"ttl,omitempty"`
	ExtendInfo           []byte            `protobuf:"bytes,4,opt,name=extend_info,json=extendInfo,proto3" json:"extend_info,omitempty"`
	ExtendParams         map[string]string `protobuf:"bytes,5,rep,name=extend_params,json=extendParams,proto3" json:"extend_params,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CreateAuthApplyReq) Reset()         { *m = CreateAuthApplyReq{} }
func (m *CreateAuthApplyReq) String() string { return proto.CompactTextString(m) }
func (*CreateAuthApplyReq) ProtoMessage()    {}
func (*CreateAuthApplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_c09bcb8e76df3376, []int{0}
}
func (m *CreateAuthApplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAuthApplyReq.Unmarshal(m, b)
}
func (m *CreateAuthApplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAuthApplyReq.Marshal(b, m, deterministic)
}
func (dst *CreateAuthApplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAuthApplyReq.Merge(dst, src)
}
func (m *CreateAuthApplyReq) XXX_Size() int {
	return xxx_messageInfo_CreateAuthApplyReq.Size(m)
}
func (m *CreateAuthApplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAuthApplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAuthApplyReq proto.InternalMessageInfo

func (m *CreateAuthApplyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CreateAuthApplyReq) GetScene() string {
	if m != nil {
		return m.Scene
	}
	return ""
}

func (m *CreateAuthApplyReq) GetTtl() int64 {
	if m != nil {
		return m.Ttl
	}
	return 0
}

func (m *CreateAuthApplyReq) GetExtendInfo() []byte {
	if m != nil {
		return m.ExtendInfo
	}
	return nil
}

func (m *CreateAuthApplyReq) GetExtendParams() map[string]string {
	if m != nil {
		return m.ExtendParams
	}
	return nil
}

type CreateAuthApplyResp struct {
	ApplyKey             string   `protobuf:"bytes,1,opt,name=apply_key,json=applyKey,proto3" json:"apply_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateAuthApplyResp) Reset()         { *m = CreateAuthApplyResp{} }
func (m *CreateAuthApplyResp) String() string { return proto.CompactTextString(m) }
func (*CreateAuthApplyResp) ProtoMessage()    {}
func (*CreateAuthApplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_c09bcb8e76df3376, []int{1}
}
func (m *CreateAuthApplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAuthApplyResp.Unmarshal(m, b)
}
func (m *CreateAuthApplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAuthApplyResp.Marshal(b, m, deterministic)
}
func (dst *CreateAuthApplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAuthApplyResp.Merge(dst, src)
}
func (m *CreateAuthApplyResp) XXX_Size() int {
	return xxx_messageInfo_CreateAuthApplyResp.Size(m)
}
func (m *CreateAuthApplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAuthApplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAuthApplyResp proto.InternalMessageInfo

func (m *CreateAuthApplyResp) GetApplyKey() string {
	if m != nil {
		return m.ApplyKey
	}
	return ""
}

type AuthInfo struct {
	AuthType             AuthType          `protobuf:"varint,1,opt,name=auth_type,json=authType,proto3,enum=user_auth.AuthType" json:"auth_type,omitempty"`
	AuthState            AuthState         `protobuf:"varint,2,opt,name=auth_state,json=authState,proto3,enum=user_auth.AuthState" json:"auth_state,omitempty"`
	Ttl                  int64             `protobuf:"varint,3,opt,name=ttl,proto3" json:"ttl,omitempty"`
	ExtendInfo           []byte            `protobuf:"bytes,4,opt,name=extend_info,json=extendInfo,proto3" json:"extend_info,omitempty"`
	ExtendParams         map[string]string `protobuf:"bytes,5,rep,name=extend_params,json=extendParams,proto3" json:"extend_params,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AuthInfo) Reset()         { *m = AuthInfo{} }
func (m *AuthInfo) String() string { return proto.CompactTextString(m) }
func (*AuthInfo) ProtoMessage()    {}
func (*AuthInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_c09bcb8e76df3376, []int{2}
}
func (m *AuthInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuthInfo.Unmarshal(m, b)
}
func (m *AuthInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuthInfo.Marshal(b, m, deterministic)
}
func (dst *AuthInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuthInfo.Merge(dst, src)
}
func (m *AuthInfo) XXX_Size() int {
	return xxx_messageInfo_AuthInfo.Size(m)
}
func (m *AuthInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AuthInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AuthInfo proto.InternalMessageInfo

func (m *AuthInfo) GetAuthType() AuthType {
	if m != nil {
		return m.AuthType
	}
	return AuthType_AUTH_TYPE_UNKNOWN
}

func (m *AuthInfo) GetAuthState() AuthState {
	if m != nil {
		return m.AuthState
	}
	return AuthState_AUTH_STATE_UNKNOWN
}

func (m *AuthInfo) GetTtl() int64 {
	if m != nil {
		return m.Ttl
	}
	return 0
}

func (m *AuthInfo) GetExtendInfo() []byte {
	if m != nil {
		return m.ExtendInfo
	}
	return nil
}

func (m *AuthInfo) GetExtendParams() map[string]string {
	if m != nil {
		return m.ExtendParams
	}
	return nil
}

type ProcAuthApplyReq struct {
	ApplyKey             string    `protobuf:"bytes,1,opt,name=apply_key,json=applyKey,proto3" json:"apply_key,omitempty"`
	AuthInfo             *AuthInfo `protobuf:"bytes,2,opt,name=auth_info,json=authInfo,proto3" json:"auth_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ProcAuthApplyReq) Reset()         { *m = ProcAuthApplyReq{} }
func (m *ProcAuthApplyReq) String() string { return proto.CompactTextString(m) }
func (*ProcAuthApplyReq) ProtoMessage()    {}
func (*ProcAuthApplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_c09bcb8e76df3376, []int{3}
}
func (m *ProcAuthApplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProcAuthApplyReq.Unmarshal(m, b)
}
func (m *ProcAuthApplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProcAuthApplyReq.Marshal(b, m, deterministic)
}
func (dst *ProcAuthApplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProcAuthApplyReq.Merge(dst, src)
}
func (m *ProcAuthApplyReq) XXX_Size() int {
	return xxx_messageInfo_ProcAuthApplyReq.Size(m)
}
func (m *ProcAuthApplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ProcAuthApplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_ProcAuthApplyReq proto.InternalMessageInfo

func (m *ProcAuthApplyReq) GetApplyKey() string {
	if m != nil {
		return m.ApplyKey
	}
	return ""
}

func (m *ProcAuthApplyReq) GetAuthInfo() *AuthInfo {
	if m != nil {
		return m.AuthInfo
	}
	return nil
}

type ProcAuthApplyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProcAuthApplyResp) Reset()         { *m = ProcAuthApplyResp{} }
func (m *ProcAuthApplyResp) String() string { return proto.CompactTextString(m) }
func (*ProcAuthApplyResp) ProtoMessage()    {}
func (*ProcAuthApplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_c09bcb8e76df3376, []int{4}
}
func (m *ProcAuthApplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProcAuthApplyResp.Unmarshal(m, b)
}
func (m *ProcAuthApplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProcAuthApplyResp.Marshal(b, m, deterministic)
}
func (dst *ProcAuthApplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProcAuthApplyResp.Merge(dst, src)
}
func (m *ProcAuthApplyResp) XXX_Size() int {
	return xxx_messageInfo_ProcAuthApplyResp.Size(m)
}
func (m *ProcAuthApplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ProcAuthApplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_ProcAuthApplyResp proto.InternalMessageInfo

type GetAuthApplyKeyReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Scene                string   `protobuf:"bytes,2,opt,name=scene,proto3" json:"scene,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAuthApplyKeyReq) Reset()         { *m = GetAuthApplyKeyReq{} }
func (m *GetAuthApplyKeyReq) String() string { return proto.CompactTextString(m) }
func (*GetAuthApplyKeyReq) ProtoMessage()    {}
func (*GetAuthApplyKeyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_c09bcb8e76df3376, []int{5}
}
func (m *GetAuthApplyKeyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAuthApplyKeyReq.Unmarshal(m, b)
}
func (m *GetAuthApplyKeyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAuthApplyKeyReq.Marshal(b, m, deterministic)
}
func (dst *GetAuthApplyKeyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAuthApplyKeyReq.Merge(dst, src)
}
func (m *GetAuthApplyKeyReq) XXX_Size() int {
	return xxx_messageInfo_GetAuthApplyKeyReq.Size(m)
}
func (m *GetAuthApplyKeyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAuthApplyKeyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAuthApplyKeyReq proto.InternalMessageInfo

func (m *GetAuthApplyKeyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAuthApplyKeyReq) GetScene() string {
	if m != nil {
		return m.Scene
	}
	return ""
}

type GetAuthApplyKeyResp struct {
	ApplyKey             string   `protobuf:"bytes,1,opt,name=apply_key,json=applyKey,proto3" json:"apply_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAuthApplyKeyResp) Reset()         { *m = GetAuthApplyKeyResp{} }
func (m *GetAuthApplyKeyResp) String() string { return proto.CompactTextString(m) }
func (*GetAuthApplyKeyResp) ProtoMessage()    {}
func (*GetAuthApplyKeyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_c09bcb8e76df3376, []int{6}
}
func (m *GetAuthApplyKeyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAuthApplyKeyResp.Unmarshal(m, b)
}
func (m *GetAuthApplyKeyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAuthApplyKeyResp.Marshal(b, m, deterministic)
}
func (dst *GetAuthApplyKeyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAuthApplyKeyResp.Merge(dst, src)
}
func (m *GetAuthApplyKeyResp) XXX_Size() int {
	return xxx_messageInfo_GetAuthApplyKeyResp.Size(m)
}
func (m *GetAuthApplyKeyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAuthApplyKeyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAuthApplyKeyResp proto.InternalMessageInfo

func (m *GetAuthApplyKeyResp) GetApplyKey() string {
	if m != nil {
		return m.ApplyKey
	}
	return ""
}

type GetAuthApplyInfoReq struct {
	ApplyKey             string     `protobuf:"bytes,1,opt,name=apply_key,json=applyKey,proto3" json:"apply_key,omitempty"`
	AuthTypeList         []AuthType `protobuf:"varint,2,rep,packed,name=auth_type_list,json=authTypeList,proto3,enum=user_auth.AuthType" json:"auth_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetAuthApplyInfoReq) Reset()         { *m = GetAuthApplyInfoReq{} }
func (m *GetAuthApplyInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetAuthApplyInfoReq) ProtoMessage()    {}
func (*GetAuthApplyInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_c09bcb8e76df3376, []int{7}
}
func (m *GetAuthApplyInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAuthApplyInfoReq.Unmarshal(m, b)
}
func (m *GetAuthApplyInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAuthApplyInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetAuthApplyInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAuthApplyInfoReq.Merge(dst, src)
}
func (m *GetAuthApplyInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetAuthApplyInfoReq.Size(m)
}
func (m *GetAuthApplyInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAuthApplyInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAuthApplyInfoReq proto.InternalMessageInfo

func (m *GetAuthApplyInfoReq) GetApplyKey() string {
	if m != nil {
		return m.ApplyKey
	}
	return ""
}

func (m *GetAuthApplyInfoReq) GetAuthTypeList() []AuthType {
	if m != nil {
		return m.AuthTypeList
	}
	return nil
}

type ApplyInfo struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Scene                string            `protobuf:"bytes,2,opt,name=scene,proto3" json:"scene,omitempty"`
	ApplyKey             string            `protobuf:"bytes,3,opt,name=apply_key,json=applyKey,proto3" json:"apply_key,omitempty"`
	ApplyState           ApplyState        `protobuf:"varint,4,opt,name=apply_state,json=applyState,proto3,enum=user_auth.ApplyState" json:"apply_state,omitempty"`
	ExtendInfo           []byte            `protobuf:"bytes,5,opt,name=extend_info,json=extendInfo,proto3" json:"extend_info,omitempty"`
	ExtendParams         map[string]string `protobuf:"bytes,6,rep,name=extend_params,json=extendParams,proto3" json:"extend_params,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	AuthInfoList         []*AuthInfo       `protobuf:"bytes,7,rep,name=auth_info_list,json=authInfoList,proto3" json:"auth_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ApplyInfo) Reset()         { *m = ApplyInfo{} }
func (m *ApplyInfo) String() string { return proto.CompactTextString(m) }
func (*ApplyInfo) ProtoMessage()    {}
func (*ApplyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_c09bcb8e76df3376, []int{8}
}
func (m *ApplyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyInfo.Unmarshal(m, b)
}
func (m *ApplyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyInfo.Marshal(b, m, deterministic)
}
func (dst *ApplyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyInfo.Merge(dst, src)
}
func (m *ApplyInfo) XXX_Size() int {
	return xxx_messageInfo_ApplyInfo.Size(m)
}
func (m *ApplyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyInfo proto.InternalMessageInfo

func (m *ApplyInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApplyInfo) GetScene() string {
	if m != nil {
		return m.Scene
	}
	return ""
}

func (m *ApplyInfo) GetApplyKey() string {
	if m != nil {
		return m.ApplyKey
	}
	return ""
}

func (m *ApplyInfo) GetApplyState() ApplyState {
	if m != nil {
		return m.ApplyState
	}
	return ApplyState_APPLY_STATE_UNKNOWN
}

func (m *ApplyInfo) GetExtendInfo() []byte {
	if m != nil {
		return m.ExtendInfo
	}
	return nil
}

func (m *ApplyInfo) GetExtendParams() map[string]string {
	if m != nil {
		return m.ExtendParams
	}
	return nil
}

func (m *ApplyInfo) GetAuthInfoList() []*AuthInfo {
	if m != nil {
		return m.AuthInfoList
	}
	return nil
}

type GetAuthApplyInfoResp struct {
	ApplyInfo            *ApplyInfo `protobuf:"bytes,1,opt,name=apply_info,json=applyInfo,proto3" json:"apply_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetAuthApplyInfoResp) Reset()         { *m = GetAuthApplyInfoResp{} }
func (m *GetAuthApplyInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetAuthApplyInfoResp) ProtoMessage()    {}
func (*GetAuthApplyInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_c09bcb8e76df3376, []int{9}
}
func (m *GetAuthApplyInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAuthApplyInfoResp.Unmarshal(m, b)
}
func (m *GetAuthApplyInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAuthApplyInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetAuthApplyInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAuthApplyInfoResp.Merge(dst, src)
}
func (m *GetAuthApplyInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetAuthApplyInfoResp.Size(m)
}
func (m *GetAuthApplyInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAuthApplyInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAuthApplyInfoResp proto.InternalMessageInfo

func (m *GetAuthApplyInfoResp) GetApplyInfo() *ApplyInfo {
	if m != nil {
		return m.ApplyInfo
	}
	return nil
}

type GetAuthApplyInfoByUidReq struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Scene                string     `protobuf:"bytes,2,opt,name=scene,proto3" json:"scene,omitempty"`
	AuthTypeList         []AuthType `protobuf:"varint,3,rep,packed,name=auth_type_list,json=authTypeList,proto3,enum=user_auth.AuthType" json:"auth_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetAuthApplyInfoByUidReq) Reset()         { *m = GetAuthApplyInfoByUidReq{} }
func (m *GetAuthApplyInfoByUidReq) String() string { return proto.CompactTextString(m) }
func (*GetAuthApplyInfoByUidReq) ProtoMessage()    {}
func (*GetAuthApplyInfoByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_c09bcb8e76df3376, []int{10}
}
func (m *GetAuthApplyInfoByUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAuthApplyInfoByUidReq.Unmarshal(m, b)
}
func (m *GetAuthApplyInfoByUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAuthApplyInfoByUidReq.Marshal(b, m, deterministic)
}
func (dst *GetAuthApplyInfoByUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAuthApplyInfoByUidReq.Merge(dst, src)
}
func (m *GetAuthApplyInfoByUidReq) XXX_Size() int {
	return xxx_messageInfo_GetAuthApplyInfoByUidReq.Size(m)
}
func (m *GetAuthApplyInfoByUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAuthApplyInfoByUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAuthApplyInfoByUidReq proto.InternalMessageInfo

func (m *GetAuthApplyInfoByUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAuthApplyInfoByUidReq) GetScene() string {
	if m != nil {
		return m.Scene
	}
	return ""
}

func (m *GetAuthApplyInfoByUidReq) GetAuthTypeList() []AuthType {
	if m != nil {
		return m.AuthTypeList
	}
	return nil
}

type GetAuthApplyInfoByUidResp struct {
	ApplyInfo            *ApplyInfo `protobuf:"bytes,1,opt,name=apply_info,json=applyInfo,proto3" json:"apply_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetAuthApplyInfoByUidResp) Reset()         { *m = GetAuthApplyInfoByUidResp{} }
func (m *GetAuthApplyInfoByUidResp) String() string { return proto.CompactTextString(m) }
func (*GetAuthApplyInfoByUidResp) ProtoMessage()    {}
func (*GetAuthApplyInfoByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_c09bcb8e76df3376, []int{11}
}
func (m *GetAuthApplyInfoByUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAuthApplyInfoByUidResp.Unmarshal(m, b)
}
func (m *GetAuthApplyInfoByUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAuthApplyInfoByUidResp.Marshal(b, m, deterministic)
}
func (dst *GetAuthApplyInfoByUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAuthApplyInfoByUidResp.Merge(dst, src)
}
func (m *GetAuthApplyInfoByUidResp) XXX_Size() int {
	return xxx_messageInfo_GetAuthApplyInfoByUidResp.Size(m)
}
func (m *GetAuthApplyInfoByUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAuthApplyInfoByUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAuthApplyInfoByUidResp proto.InternalMessageInfo

func (m *GetAuthApplyInfoByUidResp) GetApplyInfo() *ApplyInfo {
	if m != nil {
		return m.ApplyInfo
	}
	return nil
}

func init() {
	proto.RegisterType((*CreateAuthApplyReq)(nil), "user_auth.CreateAuthApplyReq")
	proto.RegisterMapType((map[string]string)(nil), "user_auth.CreateAuthApplyReq.ExtendParamsEntry")
	proto.RegisterType((*CreateAuthApplyResp)(nil), "user_auth.CreateAuthApplyResp")
	proto.RegisterType((*AuthInfo)(nil), "user_auth.AuthInfo")
	proto.RegisterMapType((map[string]string)(nil), "user_auth.AuthInfo.ExtendParamsEntry")
	proto.RegisterType((*ProcAuthApplyReq)(nil), "user_auth.ProcAuthApplyReq")
	proto.RegisterType((*ProcAuthApplyResp)(nil), "user_auth.ProcAuthApplyResp")
	proto.RegisterType((*GetAuthApplyKeyReq)(nil), "user_auth.GetAuthApplyKeyReq")
	proto.RegisterType((*GetAuthApplyKeyResp)(nil), "user_auth.GetAuthApplyKeyResp")
	proto.RegisterType((*GetAuthApplyInfoReq)(nil), "user_auth.GetAuthApplyInfoReq")
	proto.RegisterType((*ApplyInfo)(nil), "user_auth.ApplyInfo")
	proto.RegisterMapType((map[string]string)(nil), "user_auth.ApplyInfo.ExtendParamsEntry")
	proto.RegisterType((*GetAuthApplyInfoResp)(nil), "user_auth.GetAuthApplyInfoResp")
	proto.RegisterType((*GetAuthApplyInfoByUidReq)(nil), "user_auth.GetAuthApplyInfoByUidReq")
	proto.RegisterType((*GetAuthApplyInfoByUidResp)(nil), "user_auth.GetAuthApplyInfoByUidResp")
	proto.RegisterEnum("user_auth.AuthType", AuthType_name, AuthType_value)
	proto.RegisterEnum("user_auth.ApplyState", ApplyState_name, ApplyState_value)
	proto.RegisterEnum("user_auth.AuthState", AuthState_name, AuthState_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserAuthClient is the client API for UserAuth service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserAuthClient interface {
	// 创建授权申请
	CreateAuthApply(ctx context.Context, in *CreateAuthApplyReq, opts ...grpc.CallOption) (*CreateAuthApplyResp, error)
	// 处理授权申请
	ProcAuthApply(ctx context.Context, in *ProcAuthApplyReq, opts ...grpc.CallOption) (*ProcAuthApplyResp, error)
	// 获取授权申请信息
	GetAuthApplyKey(ctx context.Context, in *GetAuthApplyKeyReq, opts ...grpc.CallOption) (*GetAuthApplyKeyResp, error)
	GetAuthApplyInfo(ctx context.Context, in *GetAuthApplyInfoReq, opts ...grpc.CallOption) (*GetAuthApplyInfoResp, error)
	GetAuthApplyInfoByUid(ctx context.Context, in *GetAuthApplyInfoByUidReq, opts ...grpc.CallOption) (*GetAuthApplyInfoByUidResp, error)
}

type userAuthClient struct {
	cc *grpc.ClientConn
}

func NewUserAuthClient(cc *grpc.ClientConn) UserAuthClient {
	return &userAuthClient{cc}
}

func (c *userAuthClient) CreateAuthApply(ctx context.Context, in *CreateAuthApplyReq, opts ...grpc.CallOption) (*CreateAuthApplyResp, error) {
	out := new(CreateAuthApplyResp)
	err := c.cc.Invoke(ctx, "/user_auth.UserAuth/CreateAuthApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAuthClient) ProcAuthApply(ctx context.Context, in *ProcAuthApplyReq, opts ...grpc.CallOption) (*ProcAuthApplyResp, error) {
	out := new(ProcAuthApplyResp)
	err := c.cc.Invoke(ctx, "/user_auth.UserAuth/ProcAuthApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAuthClient) GetAuthApplyKey(ctx context.Context, in *GetAuthApplyKeyReq, opts ...grpc.CallOption) (*GetAuthApplyKeyResp, error) {
	out := new(GetAuthApplyKeyResp)
	err := c.cc.Invoke(ctx, "/user_auth.UserAuth/GetAuthApplyKey", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAuthClient) GetAuthApplyInfo(ctx context.Context, in *GetAuthApplyInfoReq, opts ...grpc.CallOption) (*GetAuthApplyInfoResp, error) {
	out := new(GetAuthApplyInfoResp)
	err := c.cc.Invoke(ctx, "/user_auth.UserAuth/GetAuthApplyInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAuthClient) GetAuthApplyInfoByUid(ctx context.Context, in *GetAuthApplyInfoByUidReq, opts ...grpc.CallOption) (*GetAuthApplyInfoByUidResp, error) {
	out := new(GetAuthApplyInfoByUidResp)
	err := c.cc.Invoke(ctx, "/user_auth.UserAuth/GetAuthApplyInfoByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserAuthServer is the server API for UserAuth service.
type UserAuthServer interface {
	// 创建授权申请
	CreateAuthApply(context.Context, *CreateAuthApplyReq) (*CreateAuthApplyResp, error)
	// 处理授权申请
	ProcAuthApply(context.Context, *ProcAuthApplyReq) (*ProcAuthApplyResp, error)
	// 获取授权申请信息
	GetAuthApplyKey(context.Context, *GetAuthApplyKeyReq) (*GetAuthApplyKeyResp, error)
	GetAuthApplyInfo(context.Context, *GetAuthApplyInfoReq) (*GetAuthApplyInfoResp, error)
	GetAuthApplyInfoByUid(context.Context, *GetAuthApplyInfoByUidReq) (*GetAuthApplyInfoByUidResp, error)
}

func RegisterUserAuthServer(s *grpc.Server, srv UserAuthServer) {
	s.RegisterService(&_UserAuth_serviceDesc, srv)
}

func _UserAuth_CreateAuthApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAuthApplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAuthServer).CreateAuthApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_auth.UserAuth/CreateAuthApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAuthServer).CreateAuthApply(ctx, req.(*CreateAuthApplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAuth_ProcAuthApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcAuthApplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAuthServer).ProcAuthApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_auth.UserAuth/ProcAuthApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAuthServer).ProcAuthApply(ctx, req.(*ProcAuthApplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAuth_GetAuthApplyKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthApplyKeyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAuthServer).GetAuthApplyKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_auth.UserAuth/GetAuthApplyKey",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAuthServer).GetAuthApplyKey(ctx, req.(*GetAuthApplyKeyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAuth_GetAuthApplyInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthApplyInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAuthServer).GetAuthApplyInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_auth.UserAuth/GetAuthApplyInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAuthServer).GetAuthApplyInfo(ctx, req.(*GetAuthApplyInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAuth_GetAuthApplyInfoByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthApplyInfoByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAuthServer).GetAuthApplyInfoByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_auth.UserAuth/GetAuthApplyInfoByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAuthServer).GetAuthApplyInfoByUid(ctx, req.(*GetAuthApplyInfoByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserAuth_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user_auth.UserAuth",
	HandlerType: (*UserAuthServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAuthApply",
			Handler:    _UserAuth_CreateAuthApply_Handler,
		},
		{
			MethodName: "ProcAuthApply",
			Handler:    _UserAuth_ProcAuthApply_Handler,
		},
		{
			MethodName: "GetAuthApplyKey",
			Handler:    _UserAuth_GetAuthApplyKey_Handler,
		},
		{
			MethodName: "GetAuthApplyInfo",
			Handler:    _UserAuth_GetAuthApplyInfo_Handler,
		},
		{
			MethodName: "GetAuthApplyInfoByUid",
			Handler:    _UserAuth_GetAuthApplyInfoByUid_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "user-auth/user-auth.proto",
}

func init() {
	proto.RegisterFile("user-auth/user-auth.proto", fileDescriptor_user_auth_c09bcb8e76df3376)
}

var fileDescriptor_user_auth_c09bcb8e76df3376 = []byte{
	// 763 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x56, 0x6d, 0x6f, 0xd2, 0x50,
	0x14, 0x5e, 0xe9, 0x36, 0xe1, 0xc0, 0xb6, 0xee, 0xc2, 0xb4, 0x63, 0xea, 0x48, 0x7d, 0x09, 0x99,
	0x19, 0x18, 0x16, 0x5f, 0xa3, 0x31, 0xdd, 0x42, 0xd4, 0xc1, 0xa0, 0xb9, 0x94, 0x98, 0xf9, 0xa5,
	0xa9, 0x70, 0xb7, 0x11, 0x19, 0xad, 0xed, 0x65, 0xb1, 0x1f, 0xfc, 0xea, 0xff, 0xf1, 0x07, 0xf8,
	0xa7, 0xfc, 0x05, 0xe6, 0xde, 0x6e, 0xa5, 0xb4, 0xc0, 0x5e, 0x8c, 0xdf, 0x4e, 0xcf, 0xeb, 0x73,
	0x1e, 0x9e, 0x1e, 0x0a, 0xeb, 0x43, 0x97, 0x38, 0xdb, 0xe6, 0x90, 0x9e, 0x94, 0x03, 0xab, 0x64,
	0x3b, 0x16, 0xb5, 0x50, 0x8a, 0x39, 0x0c, 0xe6, 0x50, 0x7e, 0x26, 0x00, 0xed, 0x39, 0xc4, 0xa4,
	0x44, 0x1d, 0xd2, 0x13, 0xd5, 0xb6, 0xfb, 0x1e, 0x26, 0xdf, 0x90, 0x04, 0xe2, 0xb0, 0xd7, 0x95,
	0x85, 0x82, 0x50, 0x5c, 0xc2, 0xcc, 0x44, 0x39, 0x58, 0x70, 0x3b, 0x64, 0x40, 0xe4, 0x44, 0x41,
	0x28, 0xa6, 0xb0, 0xff, 0xc0, 0xf2, 0x28, 0xed, 0xcb, 0x62, 0x41, 0x28, 0x8a, 0x98, 0x99, 0x68,
	0x13, 0xd2, 0xe4, 0x3b, 0x25, 0x83, 0xae, 0xd1, 0x1b, 0x1c, 0x59, 0xf2, 0x7c, 0x41, 0x28, 0x66,
	0x30, 0xf8, 0xae, 0x8f, 0x83, 0x23, 0x0b, 0xe9, 0xb0, 0x74, 0x9e, 0x60, 0x9b, 0x8e, 0x79, 0xea,
	0xca, 0x0b, 0x05, 0xb1, 0x98, 0xae, 0x94, 0x4b, 0x01, 0xa8, 0x52, 0x1c, 0x50, 0xa9, 0xca, 0x4b,
	0x34, 0x5e, 0x51, 0x1d, 0x50, 0xc7, 0xc3, 0x19, 0x12, 0x72, 0xe5, 0xdf, 0xc1, 0x6a, 0x2c, 0x85,
	0xa1, 0xfb, 0x4a, 0x3c, 0xbe, 0x45, 0x0a, 0x33, 0x93, 0x6d, 0x71, 0x66, 0xf6, 0x87, 0xc1, 0x16,
	0xfc, 0xe1, 0x75, 0xe2, 0xa5, 0xa0, 0x54, 0x20, 0x1b, 0x1b, 0xeb, 0xda, 0x68, 0x03, 0x52, 0x26,
	0x7b, 0x30, 0x46, 0x8d, 0x92, 0xdc, 0x51, 0x23, 0x9e, 0xf2, 0x2b, 0x01, 0x49, 0x96, 0xce, 0xf7,
	0x7a, 0x0a, 0x29, 0x06, 0xde, 0xa0, 0x9e, 0x4d, 0x78, 0xe6, 0x72, 0x25, 0x1b, 0xda, 0x89, 0xe5,
	0xe9, 0x9e, 0x4d, 0x70, 0xd2, 0x3c, 0xb7, 0xd0, 0x0e, 0x00, 0xaf, 0x70, 0xa9, 0x49, 0x7d, 0x44,
	0xcb, 0x95, 0x5c, 0xa4, 0xa4, 0xc5, 0x62, 0x98, 0x77, 0xe6, 0xe6, 0x4d, 0x18, 0xdf, 0x9f, 0xcc,
	0xf8, 0xa3, 0xc8, 0x28, 0x96, 0xfb, 0xff, 0x79, 0x36, 0x41, 0xd2, 0x1c, 0xab, 0x33, 0xa6, 0xb6,
	0x59, 0x24, 0x07, 0xbc, 0xf2, 0xe5, 0x58, 0xbb, 0x74, 0x8c, 0x57, 0x86, 0xdc, 0xe7, 0x95, 0x59,
	0x4a, 0x16, 0x56, 0x23, 0x23, 0x5c, 0x5b, 0x79, 0x03, 0xe8, 0x3d, 0xa1, 0x81, 0xaf, 0x46, 0xae,
	0xa3, 0x73, 0xa6, 0x8e, 0x58, 0xf5, 0x65, 0xea, 0x38, 0x1d, 0xaf, 0xe1, 0x20, 0x2f, 0x5b, 0xf6,
	0x15, 0x2c, 0x07, 0x22, 0x32, 0xfa, 0x3d, 0x97, 0xca, 0x89, 0x82, 0x38, 0x4d, 0x49, 0x99, 0x0b,
	0x25, 0xd5, 0x7b, 0x2e, 0x55, 0xfe, 0x24, 0x20, 0x15, 0x0c, 0xba, 0xf2, 0x0b, 0x3c, 0x86, 0x46,
	0x8c, 0xa0, 0x79, 0x0e, 0x69, 0x3f, 0xe8, 0x2b, 0x74, 0x9e, 0x2b, 0x74, 0x2d, 0x0c, 0x85, 0x45,
	0x7d, 0x89, 0x82, 0x19, 0xd8, 0x51, 0x45, 0x2e, 0xc4, 0x14, 0x59, 0x8b, 0x2a, 0x72, 0x91, 0x2b,
	0xf2, 0x71, 0xb4, 0xf5, 0x55, 0x24, 0x19, 0x70, 0xc6, 0x66, 0xf9, 0x9c, 0xdd, 0xe2, 0xdd, 0x26,
	0xaa, 0x24, 0x73, 0xa1, 0x12, 0xc6, 0xd9, 0xbf, 0xab, 0xb9, 0x06, 0xb9, 0xf8, 0x6f, 0xec, 0xda,
	0xfc, 0xd5, 0xe6, 0xcc, 0x71, 0x02, 0x04, 0xae, 0xda, 0xdc, 0xa4, 0xed, 0xb0, 0x4f, 0x3f, 0xd7,
	0xed, 0x0f, 0x90, 0xa3, 0xcd, 0x76, 0xbd, 0x76, 0xaf, 0x7b, 0x9d, 0x83, 0x1c, 0x17, 0x90, 0x78,
	0x55, 0x01, 0x69, 0xb0, 0x3e, 0x65, 0xfc, 0x0d, 0x17, 0xda, 0x7a, 0xe1, 0x9f, 0x47, 0x7e, 0xec,
	0xd6, 0x60, 0x55, 0x6d, 0xeb, 0x1f, 0x0c, 0xfd, 0x50, 0xab, 0x1a, 0xed, 0x46, 0xad, 0xd1, 0xfc,
	0xd4, 0x90, 0xe6, 0x50, 0x0e, 0xa4, 0x91, 0x7b, 0xaf, 0x79, 0x70, 0xd0, 0x6c, 0x48, 0xc2, 0xd6,
	0x5b, 0x80, 0x91, 0xb4, 0xd0, 0x1d, 0xc8, 0xaa, 0x9a, 0x56, 0x3f, 0x34, 0x5a, 0xba, 0xaa, 0x87,
	0x8b, 0x6f, 0x03, 0x0a, 0x07, 0x1a, 0x4d, 0x7c, 0xa0, 0xd6, 0x25, 0x61, 0xab, 0x09, 0xa9, 0xe0,
	0x76, 0xf2, 0x24, 0x36, 0x21, 0x5a, 0x9c, 0x85, 0x95, 0x90, 0x5f, 0x53, 0x5b, 0x2d, 0x49, 0x08,
	0x50, 0xfa, 0x4e, 0x5c, 0xdd, 0xaf, 0xee, 0xe9, 0x52, 0xa2, 0xf2, 0x5b, 0x84, 0x64, 0xdb, 0x25,
	0x0e, 0xeb, 0x8a, 0x30, 0xac, 0x44, 0xfe, 0x29, 0xd0, 0xbd, 0x99, 0x7f, 0x5e, 0xf9, 0xfb, 0xb3,
	0xc2, 0xae, 0xad, 0xcc, 0xa1, 0x3a, 0x2c, 0x8d, 0x9d, 0x2c, 0xb4, 0x11, 0x2a, 0x89, 0xde, 0xcb,
	0xfc, 0xdd, 0xe9, 0x41, 0xde, 0x0d, 0xc3, 0x4a, 0xe4, 0x5a, 0x8d, 0x21, 0x8c, 0xdf, 0xc1, 0x31,
	0x84, 0x13, 0x0e, 0x9d, 0x32, 0x87, 0xda, 0x20, 0x45, 0xd5, 0x81, 0xa6, 0x55, 0x9d, 0x9f, 0xba,
	0xfc, 0xe6, 0xcc, 0x38, 0x6f, 0xdb, 0x85, 0xb5, 0x89, 0xa2, 0x43, 0x0f, 0x66, 0xd4, 0x5e, 0xbc,
	0x15, 0xf9, 0x87, 0x97, 0x27, 0xb1, 0x29, 0xbb, 0xdb, 0x9f, 0x9f, 0x1c, 0x5b, 0x7d, 0x73, 0x70,
	0x5c, 0x7a, 0x56, 0xa1, 0xb4, 0xd4, 0xb1, 0x4e, 0xcb, 0xfc, 0x4b, 0xa8, 0x63, 0xf5, 0xcb, 0x2e,
	0x71, 0xce, 0x7a, 0x1d, 0xe2, 0x8e, 0xbe, 0x92, 0xbe, 0x2c, 0xf2, 0xe0, 0xce, 0xdf, 0x00, 0x00,
	0x00, 0xff, 0xff, 0xb7, 0xca, 0x9a, 0x92, 0x43, 0x09, 0x00, 0x00,
}
