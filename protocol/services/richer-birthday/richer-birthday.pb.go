// Code generated by protoc-gen-go. DO NOT EDIT.
// source: richer-birthday/richer-birthday.proto

package richer_birthday // import "golang.52tt.com/protocol/services/richer-birthday"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RicherBirthdayGiftType int32

const (
	RicherBirthdayGiftType_RICHER_BIRTHDAY_GIFT_TYPE_UNSPECIFIED RicherBirthdayGiftType = 0
	RicherBirthdayGiftType_RICHER_BIRTHDAY_GIFT_TYPE_BLESS       RicherBirthdayGiftType = 1
	RicherBirthdayGiftType_RICHER_BIRTHDAY_GIFT_TYPE_RICHER      RicherBirthdayGiftType = 2
	RicherBirthdayGiftType_RICHER_BIRTHDAY_GIFT_TYPE_IM          RicherBirthdayGiftType = 3
	RicherBirthdayGiftType_RICHER_BIRTHDAY_GIFT_TYPE_NORMAL      RicherBirthdayGiftType = 4
)

var RicherBirthdayGiftType_name = map[int32]string{
	0: "RICHER_BIRTHDAY_GIFT_TYPE_UNSPECIFIED",
	1: "RICHER_BIRTHDAY_GIFT_TYPE_BLESS",
	2: "RICHER_BIRTHDAY_GIFT_TYPE_RICHER",
	3: "RICHER_BIRTHDAY_GIFT_TYPE_IM",
	4: "RICHER_BIRTHDAY_GIFT_TYPE_NORMAL",
}
var RicherBirthdayGiftType_value = map[string]int32{
	"RICHER_BIRTHDAY_GIFT_TYPE_UNSPECIFIED": 0,
	"RICHER_BIRTHDAY_GIFT_TYPE_BLESS":       1,
	"RICHER_BIRTHDAY_GIFT_TYPE_RICHER":      2,
	"RICHER_BIRTHDAY_GIFT_TYPE_IM":          3,
	"RICHER_BIRTHDAY_GIFT_TYPE_NORMAL":      4,
}

func (x RicherBirthdayGiftType) String() string {
	return proto.EnumName(RicherBirthdayGiftType_name, int32(x))
}
func (RicherBirthdayGiftType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{0}
}

// 获取生日信息
type GetRicherBirthdayRegisterStatusRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRicherBirthdayRegisterStatusRequest) Reset() {
	*m = GetRicherBirthdayRegisterStatusRequest{}
}
func (m *GetRicherBirthdayRegisterStatusRequest) String() string { return proto.CompactTextString(m) }
func (*GetRicherBirthdayRegisterStatusRequest) ProtoMessage()    {}
func (*GetRicherBirthdayRegisterStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{0}
}
func (m *GetRicherBirthdayRegisterStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRicherBirthdayRegisterStatusRequest.Unmarshal(m, b)
}
func (m *GetRicherBirthdayRegisterStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRicherBirthdayRegisterStatusRequest.Marshal(b, m, deterministic)
}
func (dst *GetRicherBirthdayRegisterStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRicherBirthdayRegisterStatusRequest.Merge(dst, src)
}
func (m *GetRicherBirthdayRegisterStatusRequest) XXX_Size() int {
	return xxx_messageInfo_GetRicherBirthdayRegisterStatusRequest.Size(m)
}
func (m *GetRicherBirthdayRegisterStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRicherBirthdayRegisterStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRicherBirthdayRegisterStatusRequest proto.InternalMessageInfo

func (m *GetRicherBirthdayRegisterStatusRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 查看生日登记状态
type GetRicherBirthdayRegisterStatusResp struct {
	IsRicher             bool     `protobuf:"varint,1,opt,name=is_richer,json=isRicher,proto3" json:"is_richer,omitempty"`
	Birthday             int64    `protobuf:"varint,2,opt,name=birthday,proto3" json:"birthday,omitempty"`
	CouldModify          bool     `protobuf:"varint,3,opt,name=could_modify,json=couldModify,proto3" json:"could_modify,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRicherBirthdayRegisterStatusResp) Reset()         { *m = GetRicherBirthdayRegisterStatusResp{} }
func (m *GetRicherBirthdayRegisterStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetRicherBirthdayRegisterStatusResp) ProtoMessage()    {}
func (*GetRicherBirthdayRegisterStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{1}
}
func (m *GetRicherBirthdayRegisterStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRicherBirthdayRegisterStatusResp.Unmarshal(m, b)
}
func (m *GetRicherBirthdayRegisterStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRicherBirthdayRegisterStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetRicherBirthdayRegisterStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRicherBirthdayRegisterStatusResp.Merge(dst, src)
}
func (m *GetRicherBirthdayRegisterStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetRicherBirthdayRegisterStatusResp.Size(m)
}
func (m *GetRicherBirthdayRegisterStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRicherBirthdayRegisterStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRicherBirthdayRegisterStatusResp proto.InternalMessageInfo

func (m *GetRicherBirthdayRegisterStatusResp) GetIsRicher() bool {
	if m != nil {
		return m.IsRicher
	}
	return false
}

func (m *GetRicherBirthdayRegisterStatusResp) GetBirthday() int64 {
	if m != nil {
		return m.Birthday
	}
	return 0
}

func (m *GetRicherBirthdayRegisterStatusResp) GetCouldModify() bool {
	if m != nil {
		return m.CouldModify
	}
	return false
}

// 生日登记（设置或者修改）
type RegisterRicherBirthdayReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Birthday             int64    `protobuf:"varint,2,opt,name=birthday,proto3" json:"birthday,omitempty"`
	IsUpdate             bool     `protobuf:"varint,3,opt,name=is_update,json=isUpdate,proto3" json:"is_update,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RegisterRicherBirthdayReq) Reset()         { *m = RegisterRicherBirthdayReq{} }
func (m *RegisterRicherBirthdayReq) String() string { return proto.CompactTextString(m) }
func (*RegisterRicherBirthdayReq) ProtoMessage()    {}
func (*RegisterRicherBirthdayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{2}
}
func (m *RegisterRicherBirthdayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegisterRicherBirthdayReq.Unmarshal(m, b)
}
func (m *RegisterRicherBirthdayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegisterRicherBirthdayReq.Marshal(b, m, deterministic)
}
func (dst *RegisterRicherBirthdayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegisterRicherBirthdayReq.Merge(dst, src)
}
func (m *RegisterRicherBirthdayReq) XXX_Size() int {
	return xxx_messageInfo_RegisterRicherBirthdayReq.Size(m)
}
func (m *RegisterRicherBirthdayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RegisterRicherBirthdayReq.DiscardUnknown(m)
}

var xxx_messageInfo_RegisterRicherBirthdayReq proto.InternalMessageInfo

func (m *RegisterRicherBirthdayReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RegisterRicherBirthdayReq) GetBirthday() int64 {
	if m != nil {
		return m.Birthday
	}
	return 0
}

func (m *RegisterRicherBirthdayReq) GetIsUpdate() bool {
	if m != nil {
		return m.IsUpdate
	}
	return false
}

type RegisterRicherBirthdayResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RegisterRicherBirthdayResp) Reset()         { *m = RegisterRicherBirthdayResp{} }
func (m *RegisterRicherBirthdayResp) String() string { return proto.CompactTextString(m) }
func (*RegisterRicherBirthdayResp) ProtoMessage()    {}
func (*RegisterRicherBirthdayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{3}
}
func (m *RegisterRicherBirthdayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegisterRicherBirthdayResp.Unmarshal(m, b)
}
func (m *RegisterRicherBirthdayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegisterRicherBirthdayResp.Marshal(b, m, deterministic)
}
func (dst *RegisterRicherBirthdayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegisterRicherBirthdayResp.Merge(dst, src)
}
func (m *RegisterRicherBirthdayResp) XXX_Size() int {
	return xxx_messageInfo_RegisterRicherBirthdayResp.Size(m)
}
func (m *RegisterRicherBirthdayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RegisterRicherBirthdayResp.DiscardUnknown(m)
}

var xxx_messageInfo_RegisterRicherBirthdayResp proto.InternalMessageInfo

// 获取生日奖励列表
type GetRicherBirthdayRewardListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRicherBirthdayRewardListReq) Reset()         { *m = GetRicherBirthdayRewardListReq{} }
func (m *GetRicherBirthdayRewardListReq) String() string { return proto.CompactTextString(m) }
func (*GetRicherBirthdayRewardListReq) ProtoMessage()    {}
func (*GetRicherBirthdayRewardListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{4}
}
func (m *GetRicherBirthdayRewardListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRicherBirthdayRewardListReq.Unmarshal(m, b)
}
func (m *GetRicherBirthdayRewardListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRicherBirthdayRewardListReq.Marshal(b, m, deterministic)
}
func (dst *GetRicherBirthdayRewardListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRicherBirthdayRewardListReq.Merge(dst, src)
}
func (m *GetRicherBirthdayRewardListReq) XXX_Size() int {
	return xxx_messageInfo_GetRicherBirthdayRewardListReq.Size(m)
}
func (m *GetRicherBirthdayRewardListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRicherBirthdayRewardListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRicherBirthdayRewardListReq proto.InternalMessageInfo

type RicherBirthdayReward struct {
	RewardId             string   `protobuf:"bytes,1,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`
	RewardName           string   `protobuf:"bytes,2,opt,name=reward_name,json=rewardName,proto3" json:"reward_name,omitempty"`
	RewardType           uint32   `protobuf:"varint,3,opt,name=reward_type,json=rewardType,proto3" json:"reward_type,omitempty"`
	RewardNum            uint32   `protobuf:"varint,4,opt,name=reward_num,json=rewardNum,proto3" json:"reward_num,omitempty"`
	RewardIcon           string   `protobuf:"bytes,5,opt,name=reward_icon,json=rewardIcon,proto3" json:"reward_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RicherBirthdayReward) Reset()         { *m = RicherBirthdayReward{} }
func (m *RicherBirthdayReward) String() string { return proto.CompactTextString(m) }
func (*RicherBirthdayReward) ProtoMessage()    {}
func (*RicherBirthdayReward) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{5}
}
func (m *RicherBirthdayReward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RicherBirthdayReward.Unmarshal(m, b)
}
func (m *RicherBirthdayReward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RicherBirthdayReward.Marshal(b, m, deterministic)
}
func (dst *RicherBirthdayReward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RicherBirthdayReward.Merge(dst, src)
}
func (m *RicherBirthdayReward) XXX_Size() int {
	return xxx_messageInfo_RicherBirthdayReward.Size(m)
}
func (m *RicherBirthdayReward) XXX_DiscardUnknown() {
	xxx_messageInfo_RicherBirthdayReward.DiscardUnknown(m)
}

var xxx_messageInfo_RicherBirthdayReward proto.InternalMessageInfo

func (m *RicherBirthdayReward) GetRewardId() string {
	if m != nil {
		return m.RewardId
	}
	return ""
}

func (m *RicherBirthdayReward) GetRewardName() string {
	if m != nil {
		return m.RewardName
	}
	return ""
}

func (m *RicherBirthdayReward) GetRewardType() uint32 {
	if m != nil {
		return m.RewardType
	}
	return 0
}

func (m *RicherBirthdayReward) GetRewardNum() uint32 {
	if m != nil {
		return m.RewardNum
	}
	return 0
}

func (m *RicherBirthdayReward) GetRewardIcon() string {
	if m != nil {
		return m.RewardIcon
	}
	return ""
}

type GetRicherBirthdayRewardListResp struct {
	RicherBirthdayRewardList []*RicherBirthdayReward `protobuf:"bytes,1,rep,name=richer_birthday_reward_list,json=richerBirthdayRewardList,proto3" json:"richer_birthday_reward_list,omitempty"`
	XXX_NoUnkeyedLiteral     struct{}                `json:"-"`
	XXX_unrecognized         []byte                  `json:"-"`
	XXX_sizecache            int32                   `json:"-"`
}

func (m *GetRicherBirthdayRewardListResp) Reset()         { *m = GetRicherBirthdayRewardListResp{} }
func (m *GetRicherBirthdayRewardListResp) String() string { return proto.CompactTextString(m) }
func (*GetRicherBirthdayRewardListResp) ProtoMessage()    {}
func (*GetRicherBirthdayRewardListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{6}
}
func (m *GetRicherBirthdayRewardListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRicherBirthdayRewardListResp.Unmarshal(m, b)
}
func (m *GetRicherBirthdayRewardListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRicherBirthdayRewardListResp.Marshal(b, m, deterministic)
}
func (dst *GetRicherBirthdayRewardListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRicherBirthdayRewardListResp.Merge(dst, src)
}
func (m *GetRicherBirthdayRewardListResp) XXX_Size() int {
	return xxx_messageInfo_GetRicherBirthdayRewardListResp.Size(m)
}
func (m *GetRicherBirthdayRewardListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRicherBirthdayRewardListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRicherBirthdayRewardListResp proto.InternalMessageInfo

func (m *GetRicherBirthdayRewardListResp) GetRicherBirthdayRewardList() []*RicherBirthdayReward {
	if m != nil {
		return m.RicherBirthdayRewardList
	}
	return nil
}

type BirthdayBlessingCfg struct {
	GiftIdList           []uint32 `protobuf:"varint,1,rep,packed,name=gift_id_list,json=giftIdList,proto3" json:"gift_id_list,omitempty"`
	BlessingText         []string `protobuf:"bytes,2,rep,name=blessing_text,json=blessingText,proto3" json:"blessing_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BirthdayBlessingCfg) Reset()         { *m = BirthdayBlessingCfg{} }
func (m *BirthdayBlessingCfg) String() string { return proto.CompactTextString(m) }
func (*BirthdayBlessingCfg) ProtoMessage()    {}
func (*BirthdayBlessingCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{7}
}
func (m *BirthdayBlessingCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BirthdayBlessingCfg.Unmarshal(m, b)
}
func (m *BirthdayBlessingCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BirthdayBlessingCfg.Marshal(b, m, deterministic)
}
func (dst *BirthdayBlessingCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BirthdayBlessingCfg.Merge(dst, src)
}
func (m *BirthdayBlessingCfg) XXX_Size() int {
	return xxx_messageInfo_BirthdayBlessingCfg.Size(m)
}
func (m *BirthdayBlessingCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_BirthdayBlessingCfg.DiscardUnknown(m)
}

var xxx_messageInfo_BirthdayBlessingCfg proto.InternalMessageInfo

func (m *BirthdayBlessingCfg) GetGiftIdList() []uint32 {
	if m != nil {
		return m.GiftIdList
	}
	return nil
}

func (m *BirthdayBlessingCfg) GetBlessingText() []string {
	if m != nil {
		return m.BlessingText
	}
	return nil
}

type GetRicherBirthdayInfoRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRicherBirthdayInfoRequest) Reset()         { *m = GetRicherBirthdayInfoRequest{} }
func (m *GetRicherBirthdayInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetRicherBirthdayInfoRequest) ProtoMessage()    {}
func (*GetRicherBirthdayInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{8}
}
func (m *GetRicherBirthdayInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRicherBirthdayInfoRequest.Unmarshal(m, b)
}
func (m *GetRicherBirthdayInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRicherBirthdayInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetRicherBirthdayInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRicherBirthdayInfoRequest.Merge(dst, src)
}
func (m *GetRicherBirthdayInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetRicherBirthdayInfoRequest.Size(m)
}
func (m *GetRicherBirthdayInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRicherBirthdayInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRicherBirthdayInfoRequest proto.InternalMessageInfo

func (m *GetRicherBirthdayInfoRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetRicherBirthdayInfoResponse struct {
	BirthdayTimestampMap map[uint32]int64     `protobuf:"bytes,1,rep,name=birthday_timestamp_map,json=birthdayTimestampMap,proto3" json:"birthday_timestamp_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	BlessingCfg          *BirthdayBlessingCfg `protobuf:"bytes,2,opt,name=blessing_cfg,json=blessingCfg,proto3" json:"blessing_cfg,omitempty"`
	IsRicher             bool                 `protobuf:"varint,3,opt,name=is_richer,json=isRicher,proto3" json:"is_richer,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetRicherBirthdayInfoResponse) Reset()         { *m = GetRicherBirthdayInfoResponse{} }
func (m *GetRicherBirthdayInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetRicherBirthdayInfoResponse) ProtoMessage()    {}
func (*GetRicherBirthdayInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{9}
}
func (m *GetRicherBirthdayInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRicherBirthdayInfoResponse.Unmarshal(m, b)
}
func (m *GetRicherBirthdayInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRicherBirthdayInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetRicherBirthdayInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRicherBirthdayInfoResponse.Merge(dst, src)
}
func (m *GetRicherBirthdayInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetRicherBirthdayInfoResponse.Size(m)
}
func (m *GetRicherBirthdayInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRicherBirthdayInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRicherBirthdayInfoResponse proto.InternalMessageInfo

func (m *GetRicherBirthdayInfoResponse) GetBirthdayTimestampMap() map[uint32]int64 {
	if m != nil {
		return m.BirthdayTimestampMap
	}
	return nil
}

func (m *GetRicherBirthdayInfoResponse) GetBlessingCfg() *BirthdayBlessingCfg {
	if m != nil {
		return m.BlessingCfg
	}
	return nil
}

func (m *GetRicherBirthdayInfoResponse) GetIsRicher() bool {
	if m != nil {
		return m.IsRicher
	}
	return false
}

type HideRicherBirthdaySwitchRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IsHide               bool     `protobuf:"varint,2,opt,name=is_hide,json=isHide,proto3" json:"is_hide,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HideRicherBirthdaySwitchRequest) Reset()         { *m = HideRicherBirthdaySwitchRequest{} }
func (m *HideRicherBirthdaySwitchRequest) String() string { return proto.CompactTextString(m) }
func (*HideRicherBirthdaySwitchRequest) ProtoMessage()    {}
func (*HideRicherBirthdaySwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{10}
}
func (m *HideRicherBirthdaySwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HideRicherBirthdaySwitchRequest.Unmarshal(m, b)
}
func (m *HideRicherBirthdaySwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HideRicherBirthdaySwitchRequest.Marshal(b, m, deterministic)
}
func (dst *HideRicherBirthdaySwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HideRicherBirthdaySwitchRequest.Merge(dst, src)
}
func (m *HideRicherBirthdaySwitchRequest) XXX_Size() int {
	return xxx_messageInfo_HideRicherBirthdaySwitchRequest.Size(m)
}
func (m *HideRicherBirthdaySwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HideRicherBirthdaySwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HideRicherBirthdaySwitchRequest proto.InternalMessageInfo

func (m *HideRicherBirthdaySwitchRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HideRicherBirthdaySwitchRequest) GetIsHide() bool {
	if m != nil {
		return m.IsHide
	}
	return false
}

type HideRicherBirthdaySwitchResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HideRicherBirthdaySwitchResponse) Reset()         { *m = HideRicherBirthdaySwitchResponse{} }
func (m *HideRicherBirthdaySwitchResponse) String() string { return proto.CompactTextString(m) }
func (*HideRicherBirthdaySwitchResponse) ProtoMessage()    {}
func (*HideRicherBirthdaySwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{11}
}
func (m *HideRicherBirthdaySwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HideRicherBirthdaySwitchResponse.Unmarshal(m, b)
}
func (m *HideRicherBirthdaySwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HideRicherBirthdaySwitchResponse.Marshal(b, m, deterministic)
}
func (dst *HideRicherBirthdaySwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HideRicherBirthdaySwitchResponse.Merge(dst, src)
}
func (m *HideRicherBirthdaySwitchResponse) XXX_Size() int {
	return xxx_messageInfo_HideRicherBirthdaySwitchResponse.Size(m)
}
func (m *HideRicherBirthdaySwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HideRicherBirthdaySwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HideRicherBirthdaySwitchResponse proto.InternalMessageInfo

type GetHideRicherBirthdaySwitchRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHideRicherBirthdaySwitchRequest) Reset()         { *m = GetHideRicherBirthdaySwitchRequest{} }
func (m *GetHideRicherBirthdaySwitchRequest) String() string { return proto.CompactTextString(m) }
func (*GetHideRicherBirthdaySwitchRequest) ProtoMessage()    {}
func (*GetHideRicherBirthdaySwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{12}
}
func (m *GetHideRicherBirthdaySwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHideRicherBirthdaySwitchRequest.Unmarshal(m, b)
}
func (m *GetHideRicherBirthdaySwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHideRicherBirthdaySwitchRequest.Marshal(b, m, deterministic)
}
func (dst *GetHideRicherBirthdaySwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHideRicherBirthdaySwitchRequest.Merge(dst, src)
}
func (m *GetHideRicherBirthdaySwitchRequest) XXX_Size() int {
	return xxx_messageInfo_GetHideRicherBirthdaySwitchRequest.Size(m)
}
func (m *GetHideRicherBirthdaySwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHideRicherBirthdaySwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetHideRicherBirthdaySwitchRequest proto.InternalMessageInfo

func (m *GetHideRicherBirthdaySwitchRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetHideRicherBirthdaySwitchResponse struct {
	IsHide               bool     `protobuf:"varint,1,opt,name=is_hide,json=isHide,proto3" json:"is_hide,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHideRicherBirthdaySwitchResponse) Reset()         { *m = GetHideRicherBirthdaySwitchResponse{} }
func (m *GetHideRicherBirthdaySwitchResponse) String() string { return proto.CompactTextString(m) }
func (*GetHideRicherBirthdaySwitchResponse) ProtoMessage()    {}
func (*GetHideRicherBirthdaySwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{13}
}
func (m *GetHideRicherBirthdaySwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHideRicherBirthdaySwitchResponse.Unmarshal(m, b)
}
func (m *GetHideRicherBirthdaySwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHideRicherBirthdaySwitchResponse.Marshal(b, m, deterministic)
}
func (dst *GetHideRicherBirthdaySwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHideRicherBirthdaySwitchResponse.Merge(dst, src)
}
func (m *GetHideRicherBirthdaySwitchResponse) XXX_Size() int {
	return xxx_messageInfo_GetHideRicherBirthdaySwitchResponse.Size(m)
}
func (m *GetHideRicherBirthdaySwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHideRicherBirthdaySwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetHideRicherBirthdaySwitchResponse proto.InternalMessageInfo

func (m *GetHideRicherBirthdaySwitchResponse) GetIsHide() bool {
	if m != nil {
		return m.IsHide
	}
	return false
}

type GetRicherBirthdayGiftCfgInRoomRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRicherBirthdayGiftCfgInRoomRequest) Reset()         { *m = GetRicherBirthdayGiftCfgInRoomRequest{} }
func (m *GetRicherBirthdayGiftCfgInRoomRequest) String() string { return proto.CompactTextString(m) }
func (*GetRicherBirthdayGiftCfgInRoomRequest) ProtoMessage()    {}
func (*GetRicherBirthdayGiftCfgInRoomRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{14}
}
func (m *GetRicherBirthdayGiftCfgInRoomRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRicherBirthdayGiftCfgInRoomRequest.Unmarshal(m, b)
}
func (m *GetRicherBirthdayGiftCfgInRoomRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRicherBirthdayGiftCfgInRoomRequest.Marshal(b, m, deterministic)
}
func (dst *GetRicherBirthdayGiftCfgInRoomRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRicherBirthdayGiftCfgInRoomRequest.Merge(dst, src)
}
func (m *GetRicherBirthdayGiftCfgInRoomRequest) XXX_Size() int {
	return xxx_messageInfo_GetRicherBirthdayGiftCfgInRoomRequest.Size(m)
}
func (m *GetRicherBirthdayGiftCfgInRoomRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRicherBirthdayGiftCfgInRoomRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRicherBirthdayGiftCfgInRoomRequest proto.InternalMessageInfo

func (m *GetRicherBirthdayGiftCfgInRoomRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRicherBirthdayGiftCfgInRoomRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetRicherBirthdayGiftCfgInRoomResponse struct {
	GiftIdList           []uint32 `protobuf:"varint,1,rep,packed,name=gift_id_list,json=giftIdList,proto3" json:"gift_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRicherBirthdayGiftCfgInRoomResponse) Reset() {
	*m = GetRicherBirthdayGiftCfgInRoomResponse{}
}
func (m *GetRicherBirthdayGiftCfgInRoomResponse) String() string { return proto.CompactTextString(m) }
func (*GetRicherBirthdayGiftCfgInRoomResponse) ProtoMessage()    {}
func (*GetRicherBirthdayGiftCfgInRoomResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{15}
}
func (m *GetRicherBirthdayGiftCfgInRoomResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRicherBirthdayGiftCfgInRoomResponse.Unmarshal(m, b)
}
func (m *GetRicherBirthdayGiftCfgInRoomResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRicherBirthdayGiftCfgInRoomResponse.Marshal(b, m, deterministic)
}
func (dst *GetRicherBirthdayGiftCfgInRoomResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRicherBirthdayGiftCfgInRoomResponse.Merge(dst, src)
}
func (m *GetRicherBirthdayGiftCfgInRoomResponse) XXX_Size() int {
	return xxx_messageInfo_GetRicherBirthdayGiftCfgInRoomResponse.Size(m)
}
func (m *GetRicherBirthdayGiftCfgInRoomResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRicherBirthdayGiftCfgInRoomResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRicherBirthdayGiftCfgInRoomResponse proto.InternalMessageInfo

func (m *GetRicherBirthdayGiftCfgInRoomResponse) GetGiftIdList() []uint32 {
	if m != nil {
		return m.GiftIdList
	}
	return nil
}

// 领取生日礼包
type ReceiveRicherBirthdayGiftRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReceiveRicherBirthdayGiftRequest) Reset()         { *m = ReceiveRicherBirthdayGiftRequest{} }
func (m *ReceiveRicherBirthdayGiftRequest) String() string { return proto.CompactTextString(m) }
func (*ReceiveRicherBirthdayGiftRequest) ProtoMessage()    {}
func (*ReceiveRicherBirthdayGiftRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{16}
}
func (m *ReceiveRicherBirthdayGiftRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveRicherBirthdayGiftRequest.Unmarshal(m, b)
}
func (m *ReceiveRicherBirthdayGiftRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveRicherBirthdayGiftRequest.Marshal(b, m, deterministic)
}
func (dst *ReceiveRicherBirthdayGiftRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveRicherBirthdayGiftRequest.Merge(dst, src)
}
func (m *ReceiveRicherBirthdayGiftRequest) XXX_Size() int {
	return xxx_messageInfo_ReceiveRicherBirthdayGiftRequest.Size(m)
}
func (m *ReceiveRicherBirthdayGiftRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveRicherBirthdayGiftRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveRicherBirthdayGiftRequest proto.InternalMessageInfo

func (m *ReceiveRicherBirthdayGiftRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ReceiveRicherBirthdayGiftResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReceiveRicherBirthdayGiftResponse) Reset()         { *m = ReceiveRicherBirthdayGiftResponse{} }
func (m *ReceiveRicherBirthdayGiftResponse) String() string { return proto.CompactTextString(m) }
func (*ReceiveRicherBirthdayGiftResponse) ProtoMessage()    {}
func (*ReceiveRicherBirthdayGiftResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{17}
}
func (m *ReceiveRicherBirthdayGiftResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveRicherBirthdayGiftResponse.Unmarshal(m, b)
}
func (m *ReceiveRicherBirthdayGiftResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveRicherBirthdayGiftResponse.Marshal(b, m, deterministic)
}
func (dst *ReceiveRicherBirthdayGiftResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveRicherBirthdayGiftResponse.Merge(dst, src)
}
func (m *ReceiveRicherBirthdayGiftResponse) XXX_Size() int {
	return xxx_messageInfo_ReceiveRicherBirthdayGiftResponse.Size(m)
}
func (m *ReceiveRicherBirthdayGiftResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveRicherBirthdayGiftResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveRicherBirthdayGiftResponse proto.InternalMessageInfo

// 检查是否拉取了生日礼包
type CheckIfReceivedRicherBirthdayGiftRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Birthday             int64    `protobuf:"varint,2,opt,name=birthday,proto3" json:"birthday,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIfReceivedRicherBirthdayGiftRequest) Reset() {
	*m = CheckIfReceivedRicherBirthdayGiftRequest{}
}
func (m *CheckIfReceivedRicherBirthdayGiftRequest) String() string { return proto.CompactTextString(m) }
func (*CheckIfReceivedRicherBirthdayGiftRequest) ProtoMessage()    {}
func (*CheckIfReceivedRicherBirthdayGiftRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{18}
}
func (m *CheckIfReceivedRicherBirthdayGiftRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIfReceivedRicherBirthdayGiftRequest.Unmarshal(m, b)
}
func (m *CheckIfReceivedRicherBirthdayGiftRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIfReceivedRicherBirthdayGiftRequest.Marshal(b, m, deterministic)
}
func (dst *CheckIfReceivedRicherBirthdayGiftRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIfReceivedRicherBirthdayGiftRequest.Merge(dst, src)
}
func (m *CheckIfReceivedRicherBirthdayGiftRequest) XXX_Size() int {
	return xxx_messageInfo_CheckIfReceivedRicherBirthdayGiftRequest.Size(m)
}
func (m *CheckIfReceivedRicherBirthdayGiftRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIfReceivedRicherBirthdayGiftRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIfReceivedRicherBirthdayGiftRequest proto.InternalMessageInfo

func (m *CheckIfReceivedRicherBirthdayGiftRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckIfReceivedRicherBirthdayGiftRequest) GetBirthday() int64 {
	if m != nil {
		return m.Birthday
	}
	return 0
}

type CheckIfReceivedRicherBirthdayGiftResponse struct {
	Received             bool     `protobuf:"varint,1,opt,name=received,proto3" json:"received,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIfReceivedRicherBirthdayGiftResponse) Reset() {
	*m = CheckIfReceivedRicherBirthdayGiftResponse{}
}
func (m *CheckIfReceivedRicherBirthdayGiftResponse) String() string { return proto.CompactTextString(m) }
func (*CheckIfReceivedRicherBirthdayGiftResponse) ProtoMessage()    {}
func (*CheckIfReceivedRicherBirthdayGiftResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{19}
}
func (m *CheckIfReceivedRicherBirthdayGiftResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIfReceivedRicherBirthdayGiftResponse.Unmarshal(m, b)
}
func (m *CheckIfReceivedRicherBirthdayGiftResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIfReceivedRicherBirthdayGiftResponse.Marshal(b, m, deterministic)
}
func (dst *CheckIfReceivedRicherBirthdayGiftResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIfReceivedRicherBirthdayGiftResponse.Merge(dst, src)
}
func (m *CheckIfReceivedRicherBirthdayGiftResponse) XXX_Size() int {
	return xxx_messageInfo_CheckIfReceivedRicherBirthdayGiftResponse.Size(m)
}
func (m *CheckIfReceivedRicherBirthdayGiftResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIfReceivedRicherBirthdayGiftResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIfReceivedRicherBirthdayGiftResponse proto.InternalMessageInfo

func (m *CheckIfReceivedRicherBirthdayGiftResponse) GetReceived() bool {
	if m != nil {
		return m.Received
	}
	return false
}

type RankInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Val                  uint32   `protobuf:"varint,2,opt,name=val,proto3" json:"val,omitempty"`
	Rank                 uint32   `protobuf:"varint,3,opt,name=rank,proto3" json:"rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RankInfo) Reset()         { *m = RankInfo{} }
func (m *RankInfo) String() string { return proto.CompactTextString(m) }
func (*RankInfo) ProtoMessage()    {}
func (*RankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{20}
}
func (m *RankInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RankInfo.Unmarshal(m, b)
}
func (m *RankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RankInfo.Marshal(b, m, deterministic)
}
func (dst *RankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RankInfo.Merge(dst, src)
}
func (m *RankInfo) XXX_Size() int {
	return xxx_messageInfo_RankInfo.Size(m)
}
func (m *RankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RankInfo proto.InternalMessageInfo

func (m *RankInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RankInfo) GetVal() uint32 {
	if m != nil {
		return m.Val
	}
	return 0
}

func (m *RankInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

// 获取榜单列表
type GetRicherBirthdayRankListRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRicherBirthdayRankListRequest) Reset()         { *m = GetRicherBirthdayRankListRequest{} }
func (m *GetRicherBirthdayRankListRequest) String() string { return proto.CompactTextString(m) }
func (*GetRicherBirthdayRankListRequest) ProtoMessage()    {}
func (*GetRicherBirthdayRankListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{21}
}
func (m *GetRicherBirthdayRankListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRicherBirthdayRankListRequest.Unmarshal(m, b)
}
func (m *GetRicherBirthdayRankListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRicherBirthdayRankListRequest.Marshal(b, m, deterministic)
}
func (dst *GetRicherBirthdayRankListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRicherBirthdayRankListRequest.Merge(dst, src)
}
func (m *GetRicherBirthdayRankListRequest) XXX_Size() int {
	return xxx_messageInfo_GetRicherBirthdayRankListRequest.Size(m)
}
func (m *GetRicherBirthdayRankListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRicherBirthdayRankListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRicherBirthdayRankListRequest proto.InternalMessageInfo

func (m *GetRicherBirthdayRankListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRicherBirthdayRankListRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetRicherBirthdayRankListResponse struct {
	RankList             []*RankInfo `protobuf:"bytes,1,rep,name=rank_list,json=rankList,proto3" json:"rank_list,omitempty"`
	MyRank               *RankInfo   `protobuf:"bytes,2,opt,name=my_rank,json=myRank,proto3" json:"my_rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetRicherBirthdayRankListResponse) Reset()         { *m = GetRicherBirthdayRankListResponse{} }
func (m *GetRicherBirthdayRankListResponse) String() string { return proto.CompactTextString(m) }
func (*GetRicherBirthdayRankListResponse) ProtoMessage()    {}
func (*GetRicherBirthdayRankListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{22}
}
func (m *GetRicherBirthdayRankListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRicherBirthdayRankListResponse.Unmarshal(m, b)
}
func (m *GetRicherBirthdayRankListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRicherBirthdayRankListResponse.Marshal(b, m, deterministic)
}
func (dst *GetRicherBirthdayRankListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRicherBirthdayRankListResponse.Merge(dst, src)
}
func (m *GetRicherBirthdayRankListResponse) XXX_Size() int {
	return xxx_messageInfo_GetRicherBirthdayRankListResponse.Size(m)
}
func (m *GetRicherBirthdayRankListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRicherBirthdayRankListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRicherBirthdayRankListResponse proto.InternalMessageInfo

func (m *GetRicherBirthdayRankListResponse) GetRankList() []*RankInfo {
	if m != nil {
		return m.RankList
	}
	return nil
}

func (m *GetRicherBirthdayRankListResponse) GetMyRank() *RankInfo {
	if m != nil {
		return m.MyRank
	}
	return nil
}

// 获取用户榜单信息
type GetUserRicherBirthdayRankRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRicherBirthdayRankRequest) Reset()         { *m = GetUserRicherBirthdayRankRequest{} }
func (m *GetUserRicherBirthdayRankRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserRicherBirthdayRankRequest) ProtoMessage()    {}
func (*GetUserRicherBirthdayRankRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{23}
}
func (m *GetUserRicherBirthdayRankRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRicherBirthdayRankRequest.Unmarshal(m, b)
}
func (m *GetUserRicherBirthdayRankRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRicherBirthdayRankRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserRicherBirthdayRankRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRicherBirthdayRankRequest.Merge(dst, src)
}
func (m *GetUserRicherBirthdayRankRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserRicherBirthdayRankRequest.Size(m)
}
func (m *GetUserRicherBirthdayRankRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRicherBirthdayRankRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRicherBirthdayRankRequest proto.InternalMessageInfo

func (m *GetUserRicherBirthdayRankRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserRicherBirthdayRankResponse struct {
	Rank                 *RankInfo `protobuf:"bytes,1,opt,name=rank,proto3" json:"rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetUserRicherBirthdayRankResponse) Reset()         { *m = GetUserRicherBirthdayRankResponse{} }
func (m *GetUserRicherBirthdayRankResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserRicherBirthdayRankResponse) ProtoMessage()    {}
func (*GetUserRicherBirthdayRankResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{24}
}
func (m *GetUserRicherBirthdayRankResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRicherBirthdayRankResponse.Unmarshal(m, b)
}
func (m *GetUserRicherBirthdayRankResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRicherBirthdayRankResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserRicherBirthdayRankResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRicherBirthdayRankResponse.Merge(dst, src)
}
func (m *GetUserRicherBirthdayRankResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserRicherBirthdayRankResponse.Size(m)
}
func (m *GetUserRicherBirthdayRankResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRicherBirthdayRankResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRicherBirthdayRankResponse proto.InternalMessageInfo

func (m *GetUserRicherBirthdayRankResponse) GetRank() *RankInfo {
	if m != nil {
		return m.Rank
	}
	return nil
}

type BlessInfo struct {
	FromUid              uint32   `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	GiftId               uint32   `protobuf:"varint,3,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	BlessContent         string   `protobuf:"bytes,4,opt,name=bless_content,json=blessContent,proto3" json:"bless_content,omitempty"`
	BlessTime            int64    `protobuf:"varint,5,opt,name=bless_time,json=blessTime,proto3" json:"bless_time,omitempty"`
	Num                  uint32   `protobuf:"varint,6,opt,name=num,proto3" json:"num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlessInfo) Reset()         { *m = BlessInfo{} }
func (m *BlessInfo) String() string { return proto.CompactTextString(m) }
func (*BlessInfo) ProtoMessage()    {}
func (*BlessInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{25}
}
func (m *BlessInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlessInfo.Unmarshal(m, b)
}
func (m *BlessInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlessInfo.Marshal(b, m, deterministic)
}
func (dst *BlessInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlessInfo.Merge(dst, src)
}
func (m *BlessInfo) XXX_Size() int {
	return xxx_messageInfo_BlessInfo.Size(m)
}
func (m *BlessInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BlessInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BlessInfo proto.InternalMessageInfo

func (m *BlessInfo) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *BlessInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *BlessInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *BlessInfo) GetBlessContent() string {
	if m != nil {
		return m.BlessContent
	}
	return ""
}

func (m *BlessInfo) GetBlessTime() int64 {
	if m != nil {
		return m.BlessTime
	}
	return 0
}

func (m *BlessInfo) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

// 获取用户的祝福列表
type GetRicherBirthdayBlessListRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRicherBirthdayBlessListRequest) Reset()         { *m = GetRicherBirthdayBlessListRequest{} }
func (m *GetRicherBirthdayBlessListRequest) String() string { return proto.CompactTextString(m) }
func (*GetRicherBirthdayBlessListRequest) ProtoMessage()    {}
func (*GetRicherBirthdayBlessListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{26}
}
func (m *GetRicherBirthdayBlessListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRicherBirthdayBlessListRequest.Unmarshal(m, b)
}
func (m *GetRicherBirthdayBlessListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRicherBirthdayBlessListRequest.Marshal(b, m, deterministic)
}
func (dst *GetRicherBirthdayBlessListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRicherBirthdayBlessListRequest.Merge(dst, src)
}
func (m *GetRicherBirthdayBlessListRequest) XXX_Size() int {
	return xxx_messageInfo_GetRicherBirthdayBlessListRequest.Size(m)
}
func (m *GetRicherBirthdayBlessListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRicherBirthdayBlessListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRicherBirthdayBlessListRequest proto.InternalMessageInfo

func (m *GetRicherBirthdayBlessListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRicherBirthdayBlessListRequest) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetRicherBirthdayBlessListRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetRicherBirthdayBlessListResponse struct {
	BlessList            []*BlessInfo `protobuf:"bytes,1,rep,name=bless_list,json=blessList,proto3" json:"bless_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRicherBirthdayBlessListResponse) Reset()         { *m = GetRicherBirthdayBlessListResponse{} }
func (m *GetRicherBirthdayBlessListResponse) String() string { return proto.CompactTextString(m) }
func (*GetRicherBirthdayBlessListResponse) ProtoMessage()    {}
func (*GetRicherBirthdayBlessListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{27}
}
func (m *GetRicherBirthdayBlessListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRicherBirthdayBlessListResponse.Unmarshal(m, b)
}
func (m *GetRicherBirthdayBlessListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRicherBirthdayBlessListResponse.Marshal(b, m, deterministic)
}
func (dst *GetRicherBirthdayBlessListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRicherBirthdayBlessListResponse.Merge(dst, src)
}
func (m *GetRicherBirthdayBlessListResponse) XXX_Size() int {
	return xxx_messageInfo_GetRicherBirthdayBlessListResponse.Size(m)
}
func (m *GetRicherBirthdayBlessListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRicherBirthdayBlessListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRicherBirthdayBlessListResponse proto.InternalMessageInfo

func (m *GetRicherBirthdayBlessListResponse) GetBlessList() []*BlessInfo {
	if m != nil {
		return m.BlessList
	}
	return nil
}

// 上报订单祝福语
type ReportRicherBirthdayBlessRequest struct {
	FromUid              uint32   `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	BlessContent         string   `protobuf:"bytes,3,opt,name=bless_content,json=blessContent,proto3" json:"bless_content,omitempty"`
	BlessTime            int64    `protobuf:"varint,4,opt,name=bless_time,json=blessTime,proto3" json:"bless_time,omitempty"`
	OrderId              string   `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	GiftId               uint32   `protobuf:"varint,6,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	Num                  uint32   `protobuf:"varint,7,opt,name=num,proto3" json:"num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportRicherBirthdayBlessRequest) Reset()         { *m = ReportRicherBirthdayBlessRequest{} }
func (m *ReportRicherBirthdayBlessRequest) String() string { return proto.CompactTextString(m) }
func (*ReportRicherBirthdayBlessRequest) ProtoMessage()    {}
func (*ReportRicherBirthdayBlessRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{28}
}
func (m *ReportRicherBirthdayBlessRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportRicherBirthdayBlessRequest.Unmarshal(m, b)
}
func (m *ReportRicherBirthdayBlessRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportRicherBirthdayBlessRequest.Marshal(b, m, deterministic)
}
func (dst *ReportRicherBirthdayBlessRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportRicherBirthdayBlessRequest.Merge(dst, src)
}
func (m *ReportRicherBirthdayBlessRequest) XXX_Size() int {
	return xxx_messageInfo_ReportRicherBirthdayBlessRequest.Size(m)
}
func (m *ReportRicherBirthdayBlessRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportRicherBirthdayBlessRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReportRicherBirthdayBlessRequest proto.InternalMessageInfo

func (m *ReportRicherBirthdayBlessRequest) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *ReportRicherBirthdayBlessRequest) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *ReportRicherBirthdayBlessRequest) GetBlessContent() string {
	if m != nil {
		return m.BlessContent
	}
	return ""
}

func (m *ReportRicherBirthdayBlessRequest) GetBlessTime() int64 {
	if m != nil {
		return m.BlessTime
	}
	return 0
}

func (m *ReportRicherBirthdayBlessRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *ReportRicherBirthdayBlessRequest) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *ReportRicherBirthdayBlessRequest) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

type ReportRicherBirthdayBlessResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportRicherBirthdayBlessResponse) Reset()         { *m = ReportRicherBirthdayBlessResponse{} }
func (m *ReportRicherBirthdayBlessResponse) String() string { return proto.CompactTextString(m) }
func (*ReportRicherBirthdayBlessResponse) ProtoMessage()    {}
func (*ReportRicherBirthdayBlessResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{29}
}
func (m *ReportRicherBirthdayBlessResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportRicherBirthdayBlessResponse.Unmarshal(m, b)
}
func (m *ReportRicherBirthdayBlessResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportRicherBirthdayBlessResponse.Marshal(b, m, deterministic)
}
func (dst *ReportRicherBirthdayBlessResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportRicherBirthdayBlessResponse.Merge(dst, src)
}
func (m *ReportRicherBirthdayBlessResponse) XXX_Size() int {
	return xxx_messageInfo_ReportRicherBirthdayBlessResponse.Size(m)
}
func (m *ReportRicherBirthdayBlessResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportRicherBirthdayBlessResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReportRicherBirthdayBlessResponse proto.InternalMessageInfo

// 检查是否能发送生日礼物
type CheckIfCouldSendRicherBirthdayGiftRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	GiftId               uint32   `protobuf:"varint,3,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SendSource           uint32   `protobuf:"varint,5,opt,name=send_source,json=sendSource,proto3" json:"send_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIfCouldSendRicherBirthdayGiftRequest) Reset() {
	*m = CheckIfCouldSendRicherBirthdayGiftRequest{}
}
func (m *CheckIfCouldSendRicherBirthdayGiftRequest) String() string { return proto.CompactTextString(m) }
func (*CheckIfCouldSendRicherBirthdayGiftRequest) ProtoMessage()    {}
func (*CheckIfCouldSendRicherBirthdayGiftRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{30}
}
func (m *CheckIfCouldSendRicherBirthdayGiftRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIfCouldSendRicherBirthdayGiftRequest.Unmarshal(m, b)
}
func (m *CheckIfCouldSendRicherBirthdayGiftRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIfCouldSendRicherBirthdayGiftRequest.Marshal(b, m, deterministic)
}
func (dst *CheckIfCouldSendRicherBirthdayGiftRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIfCouldSendRicherBirthdayGiftRequest.Merge(dst, src)
}
func (m *CheckIfCouldSendRicherBirthdayGiftRequest) XXX_Size() int {
	return xxx_messageInfo_CheckIfCouldSendRicherBirthdayGiftRequest.Size(m)
}
func (m *CheckIfCouldSendRicherBirthdayGiftRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIfCouldSendRicherBirthdayGiftRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIfCouldSendRicherBirthdayGiftRequest proto.InternalMessageInfo

func (m *CheckIfCouldSendRicherBirthdayGiftRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckIfCouldSendRicherBirthdayGiftRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *CheckIfCouldSendRicherBirthdayGiftRequest) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *CheckIfCouldSendRicherBirthdayGiftRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CheckIfCouldSendRicherBirthdayGiftRequest) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

// 检查是否能发送生日礼物返回
type CheckIfCouldSendRicherBirthdayGiftResponse struct {
	GiftType             RicherBirthdayGiftType `protobuf:"varint,1,opt,name=gift_type,json=giftType,proto3,enum=richer_birthday.RicherBirthdayGiftType" json:"gift_type,omitempty"`
	CouldSend            bool                   `protobuf:"varint,2,opt,name=could_send,json=couldSend,proto3" json:"could_send,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *CheckIfCouldSendRicherBirthdayGiftResponse) Reset() {
	*m = CheckIfCouldSendRicherBirthdayGiftResponse{}
}
func (m *CheckIfCouldSendRicherBirthdayGiftResponse) String() string {
	return proto.CompactTextString(m)
}
func (*CheckIfCouldSendRicherBirthdayGiftResponse) ProtoMessage() {}
func (*CheckIfCouldSendRicherBirthdayGiftResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{31}
}
func (m *CheckIfCouldSendRicherBirthdayGiftResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIfCouldSendRicherBirthdayGiftResponse.Unmarshal(m, b)
}
func (m *CheckIfCouldSendRicherBirthdayGiftResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIfCouldSendRicherBirthdayGiftResponse.Marshal(b, m, deterministic)
}
func (dst *CheckIfCouldSendRicherBirthdayGiftResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIfCouldSendRicherBirthdayGiftResponse.Merge(dst, src)
}
func (m *CheckIfCouldSendRicherBirthdayGiftResponse) XXX_Size() int {
	return xxx_messageInfo_CheckIfCouldSendRicherBirthdayGiftResponse.Size(m)
}
func (m *CheckIfCouldSendRicherBirthdayGiftResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIfCouldSendRicherBirthdayGiftResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIfCouldSendRicherBirthdayGiftResponse proto.InternalMessageInfo

func (m *CheckIfCouldSendRicherBirthdayGiftResponse) GetGiftType() RicherBirthdayGiftType {
	if m != nil {
		return m.GiftType
	}
	return RicherBirthdayGiftType_RICHER_BIRTHDAY_GIFT_TYPE_UNSPECIFIED
}

func (m *CheckIfCouldSendRicherBirthdayGiftResponse) GetCouldSend() bool {
	if m != nil {
		return m.CouldSend
	}
	return false
}

// 批量获取用户生日信息
type BathGetRicherBirthdayInfoRequest struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BathGetRicherBirthdayInfoRequest) Reset()         { *m = BathGetRicherBirthdayInfoRequest{} }
func (m *BathGetRicherBirthdayInfoRequest) String() string { return proto.CompactTextString(m) }
func (*BathGetRicherBirthdayInfoRequest) ProtoMessage()    {}
func (*BathGetRicherBirthdayInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{32}
}
func (m *BathGetRicherBirthdayInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BathGetRicherBirthdayInfoRequest.Unmarshal(m, b)
}
func (m *BathGetRicherBirthdayInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BathGetRicherBirthdayInfoRequest.Marshal(b, m, deterministic)
}
func (dst *BathGetRicherBirthdayInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BathGetRicherBirthdayInfoRequest.Merge(dst, src)
}
func (m *BathGetRicherBirthdayInfoRequest) XXX_Size() int {
	return xxx_messageInfo_BathGetRicherBirthdayInfoRequest.Size(m)
}
func (m *BathGetRicherBirthdayInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BathGetRicherBirthdayInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BathGetRicherBirthdayInfoRequest proto.InternalMessageInfo

func (m *BathGetRicherBirthdayInfoRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BathGetRicherBirthdayInfoResponse struct {
	BirthdayTimestampMap map[uint32]int64 `protobuf:"bytes,1,rep,name=birthday_timestamp_map,json=birthdayTimestampMap,proto3" json:"birthday_timestamp_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BathGetRicherBirthdayInfoResponse) Reset()         { *m = BathGetRicherBirthdayInfoResponse{} }
func (m *BathGetRicherBirthdayInfoResponse) String() string { return proto.CompactTextString(m) }
func (*BathGetRicherBirthdayInfoResponse) ProtoMessage()    {}
func (*BathGetRicherBirthdayInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{33}
}
func (m *BathGetRicherBirthdayInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BathGetRicherBirthdayInfoResponse.Unmarshal(m, b)
}
func (m *BathGetRicherBirthdayInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BathGetRicherBirthdayInfoResponse.Marshal(b, m, deterministic)
}
func (dst *BathGetRicherBirthdayInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BathGetRicherBirthdayInfoResponse.Merge(dst, src)
}
func (m *BathGetRicherBirthdayInfoResponse) XXX_Size() int {
	return xxx_messageInfo_BathGetRicherBirthdayInfoResponse.Size(m)
}
func (m *BathGetRicherBirthdayInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BathGetRicherBirthdayInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BathGetRicherBirthdayInfoResponse proto.InternalMessageInfo

func (m *BathGetRicherBirthdayInfoResponse) GetBirthdayTimestampMap() map[uint32]int64 {
	if m != nil {
		return m.BirthdayTimestampMap
	}
	return nil
}

type SendRicherBirthdaySystemMessageRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendRicherBirthdaySystemMessageRequest) Reset() {
	*m = SendRicherBirthdaySystemMessageRequest{}
}
func (m *SendRicherBirthdaySystemMessageRequest) String() string { return proto.CompactTextString(m) }
func (*SendRicherBirthdaySystemMessageRequest) ProtoMessage()    {}
func (*SendRicherBirthdaySystemMessageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{34}
}
func (m *SendRicherBirthdaySystemMessageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendRicherBirthdaySystemMessageRequest.Unmarshal(m, b)
}
func (m *SendRicherBirthdaySystemMessageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendRicherBirthdaySystemMessageRequest.Marshal(b, m, deterministic)
}
func (dst *SendRicherBirthdaySystemMessageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendRicherBirthdaySystemMessageRequest.Merge(dst, src)
}
func (m *SendRicherBirthdaySystemMessageRequest) XXX_Size() int {
	return xxx_messageInfo_SendRicherBirthdaySystemMessageRequest.Size(m)
}
func (m *SendRicherBirthdaySystemMessageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendRicherBirthdaySystemMessageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendRicherBirthdaySystemMessageRequest proto.InternalMessageInfo

func (m *SendRicherBirthdaySystemMessageRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendRicherBirthdaySystemMessageRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type SendRicherBirthdaySystemMessageResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendRicherBirthdaySystemMessageResponse) Reset() {
	*m = SendRicherBirthdaySystemMessageResponse{}
}
func (m *SendRicherBirthdaySystemMessageResponse) String() string { return proto.CompactTextString(m) }
func (*SendRicherBirthdaySystemMessageResponse) ProtoMessage()    {}
func (*SendRicherBirthdaySystemMessageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{35}
}
func (m *SendRicherBirthdaySystemMessageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendRicherBirthdaySystemMessageResponse.Unmarshal(m, b)
}
func (m *SendRicherBirthdaySystemMessageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendRicherBirthdaySystemMessageResponse.Marshal(b, m, deterministic)
}
func (dst *SendRicherBirthdaySystemMessageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendRicherBirthdaySystemMessageResponse.Merge(dst, src)
}
func (m *SendRicherBirthdaySystemMessageResponse) XXX_Size() int {
	return xxx_messageInfo_SendRicherBirthdaySystemMessageResponse.Size(m)
}
func (m *SendRicherBirthdaySystemMessageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SendRicherBirthdaySystemMessageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SendRicherBirthdaySystemMessageResponse proto.InternalMessageInfo

type TestSetUserBirthdayRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Birthday             int64    `protobuf:"varint,2,opt,name=birthday,proto3" json:"birthday,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestSetUserBirthdayRequest) Reset()         { *m = TestSetUserBirthdayRequest{} }
func (m *TestSetUserBirthdayRequest) String() string { return proto.CompactTextString(m) }
func (*TestSetUserBirthdayRequest) ProtoMessage()    {}
func (*TestSetUserBirthdayRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{36}
}
func (m *TestSetUserBirthdayRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestSetUserBirthdayRequest.Unmarshal(m, b)
}
func (m *TestSetUserBirthdayRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestSetUserBirthdayRequest.Marshal(b, m, deterministic)
}
func (dst *TestSetUserBirthdayRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestSetUserBirthdayRequest.Merge(dst, src)
}
func (m *TestSetUserBirthdayRequest) XXX_Size() int {
	return xxx_messageInfo_TestSetUserBirthdayRequest.Size(m)
}
func (m *TestSetUserBirthdayRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TestSetUserBirthdayRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TestSetUserBirthdayRequest proto.InternalMessageInfo

func (m *TestSetUserBirthdayRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TestSetUserBirthdayRequest) GetBirthday() int64 {
	if m != nil {
		return m.Birthday
	}
	return 0
}

type TestSetUserBirthdayResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestSetUserBirthdayResponse) Reset()         { *m = TestSetUserBirthdayResponse{} }
func (m *TestSetUserBirthdayResponse) String() string { return proto.CompactTextString(m) }
func (*TestSetUserBirthdayResponse) ProtoMessage()    {}
func (*TestSetUserBirthdayResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_richer_birthday_58a99c1e8e7992bb, []int{37}
}
func (m *TestSetUserBirthdayResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestSetUserBirthdayResponse.Unmarshal(m, b)
}
func (m *TestSetUserBirthdayResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestSetUserBirthdayResponse.Marshal(b, m, deterministic)
}
func (dst *TestSetUserBirthdayResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestSetUserBirthdayResponse.Merge(dst, src)
}
func (m *TestSetUserBirthdayResponse) XXX_Size() int {
	return xxx_messageInfo_TestSetUserBirthdayResponse.Size(m)
}
func (m *TestSetUserBirthdayResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TestSetUserBirthdayResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TestSetUserBirthdayResponse proto.InternalMessageInfo

func init() {
	proto.RegisterType((*GetRicherBirthdayRegisterStatusRequest)(nil), "richer_birthday.GetRicherBirthdayRegisterStatusRequest")
	proto.RegisterType((*GetRicherBirthdayRegisterStatusResp)(nil), "richer_birthday.GetRicherBirthdayRegisterStatusResp")
	proto.RegisterType((*RegisterRicherBirthdayReq)(nil), "richer_birthday.RegisterRicherBirthdayReq")
	proto.RegisterType((*RegisterRicherBirthdayResp)(nil), "richer_birthday.RegisterRicherBirthdayResp")
	proto.RegisterType((*GetRicherBirthdayRewardListReq)(nil), "richer_birthday.GetRicherBirthdayRewardListReq")
	proto.RegisterType((*RicherBirthdayReward)(nil), "richer_birthday.RicherBirthdayReward")
	proto.RegisterType((*GetRicherBirthdayRewardListResp)(nil), "richer_birthday.GetRicherBirthdayRewardListResp")
	proto.RegisterType((*BirthdayBlessingCfg)(nil), "richer_birthday.BirthdayBlessingCfg")
	proto.RegisterType((*GetRicherBirthdayInfoRequest)(nil), "richer_birthday.GetRicherBirthdayInfoRequest")
	proto.RegisterType((*GetRicherBirthdayInfoResponse)(nil), "richer_birthday.GetRicherBirthdayInfoResponse")
	proto.RegisterMapType((map[uint32]int64)(nil), "richer_birthday.GetRicherBirthdayInfoResponse.BirthdayTimestampMapEntry")
	proto.RegisterType((*HideRicherBirthdaySwitchRequest)(nil), "richer_birthday.HideRicherBirthdaySwitchRequest")
	proto.RegisterType((*HideRicherBirthdaySwitchResponse)(nil), "richer_birthday.HideRicherBirthdaySwitchResponse")
	proto.RegisterType((*GetHideRicherBirthdaySwitchRequest)(nil), "richer_birthday.GetHideRicherBirthdaySwitchRequest")
	proto.RegisterType((*GetHideRicherBirthdaySwitchResponse)(nil), "richer_birthday.GetHideRicherBirthdaySwitchResponse")
	proto.RegisterType((*GetRicherBirthdayGiftCfgInRoomRequest)(nil), "richer_birthday.GetRicherBirthdayGiftCfgInRoomRequest")
	proto.RegisterType((*GetRicherBirthdayGiftCfgInRoomResponse)(nil), "richer_birthday.GetRicherBirthdayGiftCfgInRoomResponse")
	proto.RegisterType((*ReceiveRicherBirthdayGiftRequest)(nil), "richer_birthday.ReceiveRicherBirthdayGiftRequest")
	proto.RegisterType((*ReceiveRicherBirthdayGiftResponse)(nil), "richer_birthday.ReceiveRicherBirthdayGiftResponse")
	proto.RegisterType((*CheckIfReceivedRicherBirthdayGiftRequest)(nil), "richer_birthday.CheckIfReceivedRicherBirthdayGiftRequest")
	proto.RegisterType((*CheckIfReceivedRicherBirthdayGiftResponse)(nil), "richer_birthday.CheckIfReceivedRicherBirthdayGiftResponse")
	proto.RegisterType((*RankInfo)(nil), "richer_birthday.RankInfo")
	proto.RegisterType((*GetRicherBirthdayRankListRequest)(nil), "richer_birthday.GetRicherBirthdayRankListRequest")
	proto.RegisterType((*GetRicherBirthdayRankListResponse)(nil), "richer_birthday.GetRicherBirthdayRankListResponse")
	proto.RegisterType((*GetUserRicherBirthdayRankRequest)(nil), "richer_birthday.GetUserRicherBirthdayRankRequest")
	proto.RegisterType((*GetUserRicherBirthdayRankResponse)(nil), "richer_birthday.GetUserRicherBirthdayRankResponse")
	proto.RegisterType((*BlessInfo)(nil), "richer_birthday.BlessInfo")
	proto.RegisterType((*GetRicherBirthdayBlessListRequest)(nil), "richer_birthday.GetRicherBirthdayBlessListRequest")
	proto.RegisterType((*GetRicherBirthdayBlessListResponse)(nil), "richer_birthday.GetRicherBirthdayBlessListResponse")
	proto.RegisterType((*ReportRicherBirthdayBlessRequest)(nil), "richer_birthday.ReportRicherBirthdayBlessRequest")
	proto.RegisterType((*ReportRicherBirthdayBlessResponse)(nil), "richer_birthday.ReportRicherBirthdayBlessResponse")
	proto.RegisterType((*CheckIfCouldSendRicherBirthdayGiftRequest)(nil), "richer_birthday.CheckIfCouldSendRicherBirthdayGiftRequest")
	proto.RegisterType((*CheckIfCouldSendRicherBirthdayGiftResponse)(nil), "richer_birthday.CheckIfCouldSendRicherBirthdayGiftResponse")
	proto.RegisterType((*BathGetRicherBirthdayInfoRequest)(nil), "richer_birthday.BathGetRicherBirthdayInfoRequest")
	proto.RegisterType((*BathGetRicherBirthdayInfoResponse)(nil), "richer_birthday.BathGetRicherBirthdayInfoResponse")
	proto.RegisterMapType((map[uint32]int64)(nil), "richer_birthday.BathGetRicherBirthdayInfoResponse.BirthdayTimestampMapEntry")
	proto.RegisterType((*SendRicherBirthdaySystemMessageRequest)(nil), "richer_birthday.SendRicherBirthdaySystemMessageRequest")
	proto.RegisterType((*SendRicherBirthdaySystemMessageResponse)(nil), "richer_birthday.SendRicherBirthdaySystemMessageResponse")
	proto.RegisterType((*TestSetUserBirthdayRequest)(nil), "richer_birthday.TestSetUserBirthdayRequest")
	proto.RegisterType((*TestSetUserBirthdayResponse)(nil), "richer_birthday.TestSetUserBirthdayResponse")
	proto.RegisterEnum("richer_birthday.RicherBirthdayGiftType", RicherBirthdayGiftType_name, RicherBirthdayGiftType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RicherBirthdayServiceClient is the client API for RicherBirthdayService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RicherBirthdayServiceClient interface {
	GetRicherBirthdayRegisterStatus(ctx context.Context, in *GetRicherBirthdayRegisterStatusRequest, opts ...grpc.CallOption) (*GetRicherBirthdayRegisterStatusResp, error)
	RegisterRicherBirthday(ctx context.Context, in *RegisterRicherBirthdayReq, opts ...grpc.CallOption) (*RegisterRicherBirthdayResp, error)
	GetRicherBirthdayRewardList(ctx context.Context, in *GetRicherBirthdayRewardListReq, opts ...grpc.CallOption) (*GetRicherBirthdayRewardListResp, error)
	GetRicherBirthdayInfo(ctx context.Context, in *GetRicherBirthdayInfoRequest, opts ...grpc.CallOption) (*GetRicherBirthdayInfoResponse, error)
	HideRicherBirthdaySwitch(ctx context.Context, in *HideRicherBirthdaySwitchRequest, opts ...grpc.CallOption) (*HideRicherBirthdaySwitchResponse, error)
	GetHideRicherBirthdaySwitch(ctx context.Context, in *GetHideRicherBirthdaySwitchRequest, opts ...grpc.CallOption) (*GetHideRicherBirthdaySwitchResponse, error)
	GetRicherBirthdayGiftCfgInRoom(ctx context.Context, in *GetRicherBirthdayGiftCfgInRoomRequest, opts ...grpc.CallOption) (*GetRicherBirthdayGiftCfgInRoomResponse, error)
	ReceiveRicherBirthdayGift(ctx context.Context, in *ReceiveRicherBirthdayGiftRequest, opts ...grpc.CallOption) (*ReceiveRicherBirthdayGiftResponse, error)
	CheckIfReceivedRicherBirthdayGift(ctx context.Context, in *CheckIfReceivedRicherBirthdayGiftRequest, opts ...grpc.CallOption) (*CheckIfReceivedRicherBirthdayGiftResponse, error)
	GetRicherBirthdayRankList(ctx context.Context, in *GetRicherBirthdayRankListRequest, opts ...grpc.CallOption) (*GetRicherBirthdayRankListResponse, error)
	GetUserRicherBirthdayRank(ctx context.Context, in *GetUserRicherBirthdayRankRequest, opts ...grpc.CallOption) (*GetUserRicherBirthdayRankResponse, error)
	GetRicherBirthdayBlessList(ctx context.Context, in *GetRicherBirthdayBlessListRequest, opts ...grpc.CallOption) (*GetRicherBirthdayBlessListResponse, error)
	ReportRicherBirthdayBless(ctx context.Context, in *ReportRicherBirthdayBlessRequest, opts ...grpc.CallOption) (*ReportRicherBirthdayBlessResponse, error)
	CheckIfCouldSendRicherBirthdayGift(ctx context.Context, in *CheckIfCouldSendRicherBirthdayGiftRequest, opts ...grpc.CallOption) (*CheckIfCouldSendRicherBirthdayGiftResponse, error)
	BathGetRicherBirthdayInfo(ctx context.Context, in *BathGetRicherBirthdayInfoRequest, opts ...grpc.CallOption) (*BathGetRicherBirthdayInfoResponse, error)
	SendRicherBirthdaySystemMessage(ctx context.Context, in *SendRicherBirthdaySystemMessageRequest, opts ...grpc.CallOption) (*SendRicherBirthdaySystemMessageResponse, error)
	TestSetUserBirthday(ctx context.Context, in *TestSetUserBirthdayRequest, opts ...grpc.CallOption) (*TestSetUserBirthdayResponse, error)
}

type richerBirthdayServiceClient struct {
	cc *grpc.ClientConn
}

func NewRicherBirthdayServiceClient(cc *grpc.ClientConn) RicherBirthdayServiceClient {
	return &richerBirthdayServiceClient{cc}
}

func (c *richerBirthdayServiceClient) GetRicherBirthdayRegisterStatus(ctx context.Context, in *GetRicherBirthdayRegisterStatusRequest, opts ...grpc.CallOption) (*GetRicherBirthdayRegisterStatusResp, error) {
	out := new(GetRicherBirthdayRegisterStatusResp)
	err := c.cc.Invoke(ctx, "/richer_birthday.RicherBirthdayService/GetRicherBirthdayRegisterStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayServiceClient) RegisterRicherBirthday(ctx context.Context, in *RegisterRicherBirthdayReq, opts ...grpc.CallOption) (*RegisterRicherBirthdayResp, error) {
	out := new(RegisterRicherBirthdayResp)
	err := c.cc.Invoke(ctx, "/richer_birthday.RicherBirthdayService/RegisterRicherBirthday", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayServiceClient) GetRicherBirthdayRewardList(ctx context.Context, in *GetRicherBirthdayRewardListReq, opts ...grpc.CallOption) (*GetRicherBirthdayRewardListResp, error) {
	out := new(GetRicherBirthdayRewardListResp)
	err := c.cc.Invoke(ctx, "/richer_birthday.RicherBirthdayService/GetRicherBirthdayRewardList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayServiceClient) GetRicherBirthdayInfo(ctx context.Context, in *GetRicherBirthdayInfoRequest, opts ...grpc.CallOption) (*GetRicherBirthdayInfoResponse, error) {
	out := new(GetRicherBirthdayInfoResponse)
	err := c.cc.Invoke(ctx, "/richer_birthday.RicherBirthdayService/GetRicherBirthdayInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayServiceClient) HideRicherBirthdaySwitch(ctx context.Context, in *HideRicherBirthdaySwitchRequest, opts ...grpc.CallOption) (*HideRicherBirthdaySwitchResponse, error) {
	out := new(HideRicherBirthdaySwitchResponse)
	err := c.cc.Invoke(ctx, "/richer_birthday.RicherBirthdayService/HideRicherBirthdaySwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayServiceClient) GetHideRicherBirthdaySwitch(ctx context.Context, in *GetHideRicherBirthdaySwitchRequest, opts ...grpc.CallOption) (*GetHideRicherBirthdaySwitchResponse, error) {
	out := new(GetHideRicherBirthdaySwitchResponse)
	err := c.cc.Invoke(ctx, "/richer_birthday.RicherBirthdayService/GetHideRicherBirthdaySwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayServiceClient) GetRicherBirthdayGiftCfgInRoom(ctx context.Context, in *GetRicherBirthdayGiftCfgInRoomRequest, opts ...grpc.CallOption) (*GetRicherBirthdayGiftCfgInRoomResponse, error) {
	out := new(GetRicherBirthdayGiftCfgInRoomResponse)
	err := c.cc.Invoke(ctx, "/richer_birthday.RicherBirthdayService/GetRicherBirthdayGiftCfgInRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayServiceClient) ReceiveRicherBirthdayGift(ctx context.Context, in *ReceiveRicherBirthdayGiftRequest, opts ...grpc.CallOption) (*ReceiveRicherBirthdayGiftResponse, error) {
	out := new(ReceiveRicherBirthdayGiftResponse)
	err := c.cc.Invoke(ctx, "/richer_birthday.RicherBirthdayService/ReceiveRicherBirthdayGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayServiceClient) CheckIfReceivedRicherBirthdayGift(ctx context.Context, in *CheckIfReceivedRicherBirthdayGiftRequest, opts ...grpc.CallOption) (*CheckIfReceivedRicherBirthdayGiftResponse, error) {
	out := new(CheckIfReceivedRicherBirthdayGiftResponse)
	err := c.cc.Invoke(ctx, "/richer_birthday.RicherBirthdayService/CheckIfReceivedRicherBirthdayGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayServiceClient) GetRicherBirthdayRankList(ctx context.Context, in *GetRicherBirthdayRankListRequest, opts ...grpc.CallOption) (*GetRicherBirthdayRankListResponse, error) {
	out := new(GetRicherBirthdayRankListResponse)
	err := c.cc.Invoke(ctx, "/richer_birthday.RicherBirthdayService/GetRicherBirthdayRankList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayServiceClient) GetUserRicherBirthdayRank(ctx context.Context, in *GetUserRicherBirthdayRankRequest, opts ...grpc.CallOption) (*GetUserRicherBirthdayRankResponse, error) {
	out := new(GetUserRicherBirthdayRankResponse)
	err := c.cc.Invoke(ctx, "/richer_birthday.RicherBirthdayService/GetUserRicherBirthdayRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayServiceClient) GetRicherBirthdayBlessList(ctx context.Context, in *GetRicherBirthdayBlessListRequest, opts ...grpc.CallOption) (*GetRicherBirthdayBlessListResponse, error) {
	out := new(GetRicherBirthdayBlessListResponse)
	err := c.cc.Invoke(ctx, "/richer_birthday.RicherBirthdayService/GetRicherBirthdayBlessList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayServiceClient) ReportRicherBirthdayBless(ctx context.Context, in *ReportRicherBirthdayBlessRequest, opts ...grpc.CallOption) (*ReportRicherBirthdayBlessResponse, error) {
	out := new(ReportRicherBirthdayBlessResponse)
	err := c.cc.Invoke(ctx, "/richer_birthday.RicherBirthdayService/ReportRicherBirthdayBless", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayServiceClient) CheckIfCouldSendRicherBirthdayGift(ctx context.Context, in *CheckIfCouldSendRicherBirthdayGiftRequest, opts ...grpc.CallOption) (*CheckIfCouldSendRicherBirthdayGiftResponse, error) {
	out := new(CheckIfCouldSendRicherBirthdayGiftResponse)
	err := c.cc.Invoke(ctx, "/richer_birthday.RicherBirthdayService/CheckIfCouldSendRicherBirthdayGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayServiceClient) BathGetRicherBirthdayInfo(ctx context.Context, in *BathGetRicherBirthdayInfoRequest, opts ...grpc.CallOption) (*BathGetRicherBirthdayInfoResponse, error) {
	out := new(BathGetRicherBirthdayInfoResponse)
	err := c.cc.Invoke(ctx, "/richer_birthday.RicherBirthdayService/BathGetRicherBirthdayInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayServiceClient) SendRicherBirthdaySystemMessage(ctx context.Context, in *SendRicherBirthdaySystemMessageRequest, opts ...grpc.CallOption) (*SendRicherBirthdaySystemMessageResponse, error) {
	out := new(SendRicherBirthdaySystemMessageResponse)
	err := c.cc.Invoke(ctx, "/richer_birthday.RicherBirthdayService/SendRicherBirthdaySystemMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayServiceClient) TestSetUserBirthday(ctx context.Context, in *TestSetUserBirthdayRequest, opts ...grpc.CallOption) (*TestSetUserBirthdayResponse, error) {
	out := new(TestSetUserBirthdayResponse)
	err := c.cc.Invoke(ctx, "/richer_birthday.RicherBirthdayService/TestSetUserBirthday", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RicherBirthdayServiceServer is the server API for RicherBirthdayService service.
type RicherBirthdayServiceServer interface {
	GetRicherBirthdayRegisterStatus(context.Context, *GetRicherBirthdayRegisterStatusRequest) (*GetRicherBirthdayRegisterStatusResp, error)
	RegisterRicherBirthday(context.Context, *RegisterRicherBirthdayReq) (*RegisterRicherBirthdayResp, error)
	GetRicherBirthdayRewardList(context.Context, *GetRicherBirthdayRewardListReq) (*GetRicherBirthdayRewardListResp, error)
	GetRicherBirthdayInfo(context.Context, *GetRicherBirthdayInfoRequest) (*GetRicherBirthdayInfoResponse, error)
	HideRicherBirthdaySwitch(context.Context, *HideRicherBirthdaySwitchRequest) (*HideRicherBirthdaySwitchResponse, error)
	GetHideRicherBirthdaySwitch(context.Context, *GetHideRicherBirthdaySwitchRequest) (*GetHideRicherBirthdaySwitchResponse, error)
	GetRicherBirthdayGiftCfgInRoom(context.Context, *GetRicherBirthdayGiftCfgInRoomRequest) (*GetRicherBirthdayGiftCfgInRoomResponse, error)
	ReceiveRicherBirthdayGift(context.Context, *ReceiveRicherBirthdayGiftRequest) (*ReceiveRicherBirthdayGiftResponse, error)
	CheckIfReceivedRicherBirthdayGift(context.Context, *CheckIfReceivedRicherBirthdayGiftRequest) (*CheckIfReceivedRicherBirthdayGiftResponse, error)
	GetRicherBirthdayRankList(context.Context, *GetRicherBirthdayRankListRequest) (*GetRicherBirthdayRankListResponse, error)
	GetUserRicherBirthdayRank(context.Context, *GetUserRicherBirthdayRankRequest) (*GetUserRicherBirthdayRankResponse, error)
	GetRicherBirthdayBlessList(context.Context, *GetRicherBirthdayBlessListRequest) (*GetRicherBirthdayBlessListResponse, error)
	ReportRicherBirthdayBless(context.Context, *ReportRicherBirthdayBlessRequest) (*ReportRicherBirthdayBlessResponse, error)
	CheckIfCouldSendRicherBirthdayGift(context.Context, *CheckIfCouldSendRicherBirthdayGiftRequest) (*CheckIfCouldSendRicherBirthdayGiftResponse, error)
	BathGetRicherBirthdayInfo(context.Context, *BathGetRicherBirthdayInfoRequest) (*BathGetRicherBirthdayInfoResponse, error)
	SendRicherBirthdaySystemMessage(context.Context, *SendRicherBirthdaySystemMessageRequest) (*SendRicherBirthdaySystemMessageResponse, error)
	TestSetUserBirthday(context.Context, *TestSetUserBirthdayRequest) (*TestSetUserBirthdayResponse, error)
}

func RegisterRicherBirthdayServiceServer(s *grpc.Server, srv RicherBirthdayServiceServer) {
	s.RegisterService(&_RicherBirthdayService_serviceDesc, srv)
}

func _RicherBirthdayService_GetRicherBirthdayRegisterStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRicherBirthdayRegisterStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayServiceServer).GetRicherBirthdayRegisterStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/richer_birthday.RicherBirthdayService/GetRicherBirthdayRegisterStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayServiceServer).GetRicherBirthdayRegisterStatus(ctx, req.(*GetRicherBirthdayRegisterStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayService_RegisterRicherBirthday_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterRicherBirthdayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayServiceServer).RegisterRicherBirthday(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/richer_birthday.RicherBirthdayService/RegisterRicherBirthday",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayServiceServer).RegisterRicherBirthday(ctx, req.(*RegisterRicherBirthdayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayService_GetRicherBirthdayRewardList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRicherBirthdayRewardListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayServiceServer).GetRicherBirthdayRewardList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/richer_birthday.RicherBirthdayService/GetRicherBirthdayRewardList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayServiceServer).GetRicherBirthdayRewardList(ctx, req.(*GetRicherBirthdayRewardListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayService_GetRicherBirthdayInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRicherBirthdayInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayServiceServer).GetRicherBirthdayInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/richer_birthday.RicherBirthdayService/GetRicherBirthdayInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayServiceServer).GetRicherBirthdayInfo(ctx, req.(*GetRicherBirthdayInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayService_HideRicherBirthdaySwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HideRicherBirthdaySwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayServiceServer).HideRicherBirthdaySwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/richer_birthday.RicherBirthdayService/HideRicherBirthdaySwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayServiceServer).HideRicherBirthdaySwitch(ctx, req.(*HideRicherBirthdaySwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayService_GetHideRicherBirthdaySwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHideRicherBirthdaySwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayServiceServer).GetHideRicherBirthdaySwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/richer_birthday.RicherBirthdayService/GetHideRicherBirthdaySwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayServiceServer).GetHideRicherBirthdaySwitch(ctx, req.(*GetHideRicherBirthdaySwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayService_GetRicherBirthdayGiftCfgInRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRicherBirthdayGiftCfgInRoomRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayServiceServer).GetRicherBirthdayGiftCfgInRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/richer_birthday.RicherBirthdayService/GetRicherBirthdayGiftCfgInRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayServiceServer).GetRicherBirthdayGiftCfgInRoom(ctx, req.(*GetRicherBirthdayGiftCfgInRoomRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayService_ReceiveRicherBirthdayGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceiveRicherBirthdayGiftRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayServiceServer).ReceiveRicherBirthdayGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/richer_birthday.RicherBirthdayService/ReceiveRicherBirthdayGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayServiceServer).ReceiveRicherBirthdayGift(ctx, req.(*ReceiveRicherBirthdayGiftRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayService_CheckIfReceivedRicherBirthdayGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckIfReceivedRicherBirthdayGiftRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayServiceServer).CheckIfReceivedRicherBirthdayGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/richer_birthday.RicherBirthdayService/CheckIfReceivedRicherBirthdayGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayServiceServer).CheckIfReceivedRicherBirthdayGift(ctx, req.(*CheckIfReceivedRicherBirthdayGiftRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayService_GetRicherBirthdayRankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRicherBirthdayRankListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayServiceServer).GetRicherBirthdayRankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/richer_birthday.RicherBirthdayService/GetRicherBirthdayRankList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayServiceServer).GetRicherBirthdayRankList(ctx, req.(*GetRicherBirthdayRankListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayService_GetUserRicherBirthdayRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRicherBirthdayRankRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayServiceServer).GetUserRicherBirthdayRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/richer_birthday.RicherBirthdayService/GetUserRicherBirthdayRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayServiceServer).GetUserRicherBirthdayRank(ctx, req.(*GetUserRicherBirthdayRankRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayService_GetRicherBirthdayBlessList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRicherBirthdayBlessListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayServiceServer).GetRicherBirthdayBlessList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/richer_birthday.RicherBirthdayService/GetRicherBirthdayBlessList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayServiceServer).GetRicherBirthdayBlessList(ctx, req.(*GetRicherBirthdayBlessListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayService_ReportRicherBirthdayBless_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportRicherBirthdayBlessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayServiceServer).ReportRicherBirthdayBless(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/richer_birthday.RicherBirthdayService/ReportRicherBirthdayBless",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayServiceServer).ReportRicherBirthdayBless(ctx, req.(*ReportRicherBirthdayBlessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayService_CheckIfCouldSendRicherBirthdayGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckIfCouldSendRicherBirthdayGiftRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayServiceServer).CheckIfCouldSendRicherBirthdayGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/richer_birthday.RicherBirthdayService/CheckIfCouldSendRicherBirthdayGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayServiceServer).CheckIfCouldSendRicherBirthdayGift(ctx, req.(*CheckIfCouldSendRicherBirthdayGiftRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayService_BathGetRicherBirthdayInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BathGetRicherBirthdayInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayServiceServer).BathGetRicherBirthdayInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/richer_birthday.RicherBirthdayService/BathGetRicherBirthdayInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayServiceServer).BathGetRicherBirthdayInfo(ctx, req.(*BathGetRicherBirthdayInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayService_SendRicherBirthdaySystemMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendRicherBirthdaySystemMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayServiceServer).SendRicherBirthdaySystemMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/richer_birthday.RicherBirthdayService/SendRicherBirthdaySystemMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayServiceServer).SendRicherBirthdaySystemMessage(ctx, req.(*SendRicherBirthdaySystemMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayService_TestSetUserBirthday_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestSetUserBirthdayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayServiceServer).TestSetUserBirthday(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/richer_birthday.RicherBirthdayService/TestSetUserBirthday",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayServiceServer).TestSetUserBirthday(ctx, req.(*TestSetUserBirthdayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _RicherBirthdayService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "richer_birthday.RicherBirthdayService",
	HandlerType: (*RicherBirthdayServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRicherBirthdayRegisterStatus",
			Handler:    _RicherBirthdayService_GetRicherBirthdayRegisterStatus_Handler,
		},
		{
			MethodName: "RegisterRicherBirthday",
			Handler:    _RicherBirthdayService_RegisterRicherBirthday_Handler,
		},
		{
			MethodName: "GetRicherBirthdayRewardList",
			Handler:    _RicherBirthdayService_GetRicherBirthdayRewardList_Handler,
		},
		{
			MethodName: "GetRicherBirthdayInfo",
			Handler:    _RicherBirthdayService_GetRicherBirthdayInfo_Handler,
		},
		{
			MethodName: "HideRicherBirthdaySwitch",
			Handler:    _RicherBirthdayService_HideRicherBirthdaySwitch_Handler,
		},
		{
			MethodName: "GetHideRicherBirthdaySwitch",
			Handler:    _RicherBirthdayService_GetHideRicherBirthdaySwitch_Handler,
		},
		{
			MethodName: "GetRicherBirthdayGiftCfgInRoom",
			Handler:    _RicherBirthdayService_GetRicherBirthdayGiftCfgInRoom_Handler,
		},
		{
			MethodName: "ReceiveRicherBirthdayGift",
			Handler:    _RicherBirthdayService_ReceiveRicherBirthdayGift_Handler,
		},
		{
			MethodName: "CheckIfReceivedRicherBirthdayGift",
			Handler:    _RicherBirthdayService_CheckIfReceivedRicherBirthdayGift_Handler,
		},
		{
			MethodName: "GetRicherBirthdayRankList",
			Handler:    _RicherBirthdayService_GetRicherBirthdayRankList_Handler,
		},
		{
			MethodName: "GetUserRicherBirthdayRank",
			Handler:    _RicherBirthdayService_GetUserRicherBirthdayRank_Handler,
		},
		{
			MethodName: "GetRicherBirthdayBlessList",
			Handler:    _RicherBirthdayService_GetRicherBirthdayBlessList_Handler,
		},
		{
			MethodName: "ReportRicherBirthdayBless",
			Handler:    _RicherBirthdayService_ReportRicherBirthdayBless_Handler,
		},
		{
			MethodName: "CheckIfCouldSendRicherBirthdayGift",
			Handler:    _RicherBirthdayService_CheckIfCouldSendRicherBirthdayGift_Handler,
		},
		{
			MethodName: "BathGetRicherBirthdayInfo",
			Handler:    _RicherBirthdayService_BathGetRicherBirthdayInfo_Handler,
		},
		{
			MethodName: "SendRicherBirthdaySystemMessage",
			Handler:    _RicherBirthdayService_SendRicherBirthdaySystemMessage_Handler,
		},
		{
			MethodName: "TestSetUserBirthday",
			Handler:    _RicherBirthdayService_TestSetUserBirthday_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "richer-birthday/richer-birthday.proto",
}

func init() {
	proto.RegisterFile("richer-birthday/richer-birthday.proto", fileDescriptor_richer_birthday_58a99c1e8e7992bb)
}

var fileDescriptor_richer_birthday_58a99c1e8e7992bb = []byte{
	// 1653 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x59, 0xdf, 0x4e, 0x1b, 0xc7,
	0x1a, 0x67, 0x31, 0x18, 0xfb, 0x23, 0x9c, 0x83, 0x26, 0x84, 0x63, 0x96, 0x70, 0x30, 0x43, 0x48,
	0x48, 0xce, 0x09, 0x24, 0x90, 0xe6, 0x5f, 0xd5, 0x4a, 0x81, 0x10, 0xe3, 0x08, 0x68, 0xb4, 0x06,
	0x29, 0xa9, 0x2a, 0xad, 0x96, 0xf5, 0xd8, 0xac, 0xf0, 0xee, 0x3a, 0x3b, 0x63, 0x12, 0xdf, 0x54,
	0x4a, 0x54, 0x29, 0x52, 0x6f, 0xaa, 0xb6, 0x52, 0xaf, 0x2a, 0xf5, 0x15, 0x7a, 0x93, 0x17, 0xe8,
	0x03, 0xf4, 0xb2, 0x97, 0x7d, 0x87, 0xbe, 0x41, 0xb5, 0xb3, 0xb3, 0xc6, 0xf6, 0xce, 0xee, 0xda,
	0xa8, 0x77, 0x3b, 0xdf, 0xcc, 0xf7, 0xef, 0x37, 0xdf, 0x7c, 0x7f, 0x6c, 0x58, 0xf1, 0x2c, 0xf3,
	0x84, 0x78, 0xb7, 0x8f, 0x2d, 0x8f, 0x9d, 0x54, 0x8d, 0xf6, 0x7a, 0xdf, 0x7a, 0xad, 0xe9, 0xb9,
	0xcc, 0x45, 0xff, 0x0e, 0xc8, 0x7a, 0x48, 0xc6, 0x8f, 0xe1, 0x7a, 0x89, 0x30, 0x8d, 0x53, 0xb7,
	0x04, 0x51, 0x23, 0x75, 0x8b, 0x32, 0xe2, 0x55, 0x98, 0xc1, 0x5a, 0x54, 0x23, 0xaf, 0x5b, 0x84,
	0x32, 0x34, 0x0d, 0x99, 0x96, 0x55, 0x2d, 0x28, 0x45, 0x65, 0x75, 0x4a, 0xf3, 0x3f, 0xf1, 0x3b,
	0x05, 0x96, 0x53, 0x99, 0x69, 0x13, 0xcd, 0x43, 0xde, 0xa2, 0x7a, 0xa0, 0x99, 0xf3, 0xe7, 0xb4,
	0x9c, 0x45, 0x03, 0x36, 0xa4, 0x42, 0x2e, 0x34, 0xa6, 0x30, 0x5a, 0x54, 0x56, 0x33, 0x5a, 0x67,
	0x8d, 0x96, 0xe0, 0x92, 0xe9, 0xb6, 0x1a, 0x55, 0xdd, 0x76, 0xab, 0x56, 0xad, 0x5d, 0xc8, 0x70,
	0xde, 0x49, 0x4e, 0xdb, 0xe7, 0x24, 0x5c, 0x83, 0xb9, 0x50, 0x63, 0xbf, 0x1d, 0xaf, 0xa3, 0x26,
	0x27, 0x6a, 0x0b, 0xcc, 0x6c, 0x35, 0xab, 0x06, 0x23, 0x42, 0x55, 0xce, 0xa2, 0x47, 0x7c, 0x8d,
	0xaf, 0x82, 0x1a, 0xa7, 0x87, 0x36, 0x71, 0x11, 0xfe, 0x2b, 0x01, 0xe2, 0x8d, 0xe1, 0x55, 0xf7,
	0x2c, 0xca, 0x34, 0xf2, 0x1a, 0x7f, 0x54, 0x60, 0x46, 0xb6, 0xef, 0x6b, 0xf5, 0xf8, 0x97, 0x2e,
	0x2c, 0xcd, 0x6b, 0xb9, 0x80, 0x50, 0xae, 0xa2, 0x45, 0x98, 0x14, 0x9b, 0x8e, 0x61, 0x13, 0x6e,
	0x71, 0x5e, 0x83, 0x80, 0x74, 0x60, 0xd8, 0xa4, 0xeb, 0x00, 0x6b, 0x37, 0x03, 0xab, 0xa7, 0xc2,
	0x03, 0x87, 0xed, 0x26, 0x41, 0x0b, 0x00, 0xa1, 0x84, 0x96, 0x5d, 0x18, 0xe3, 0xfb, 0x42, 0xe1,
	0x41, 0xcb, 0xee, 0xe2, 0xb7, 0x4c, 0xd7, 0x29, 0x8c, 0x77, 0x2b, 0x28, 0x9b, 0xae, 0x83, 0x3f,
	0x28, 0xb0, 0x98, 0xe8, 0x1a, 0x6d, 0xa2, 0x2a, 0xcc, 0xf7, 0x85, 0x95, 0x2e, 0x84, 0x36, 0x2c,
	0xca, 0x0a, 0x4a, 0x31, 0xb3, 0x3a, 0xb9, 0xb1, 0xb2, 0xd6, 0x77, 0x66, 0x4d, 0x26, 0x53, 0x2b,
	0x78, 0x31, 0x9a, 0xf0, 0x57, 0x70, 0x39, 0xa4, 0x6e, 0x35, 0x08, 0xa5, 0x96, 0x53, 0xdf, 0xae,
	0xd5, 0x51, 0x11, 0x2e, 0xd5, 0xad, 0x1a, 0xd3, 0xad, 0x2e, 0x6d, 0x53, 0x1a, 0xf8, 0xb4, 0x32,
	0x67, 0x44, 0xcb, 0x30, 0x75, 0x2c, 0x18, 0x74, 0x46, 0xde, 0xb2, 0xc2, 0x68, 0x31, 0xb3, 0x9a,
	0xd7, 0x2e, 0x85, 0xc4, 0x43, 0xf2, 0x96, 0xe1, 0x3b, 0x70, 0x35, 0xe2, 0x66, 0xd9, 0xa9, 0xb9,
	0xf1, 0xd1, 0xff, 0xfb, 0x28, 0x2c, 0xc4, 0xb0, 0xd0, 0xa6, 0xeb, 0x50, 0x82, 0xbe, 0x86, 0xd9,
	0x0e, 0x20, 0xcc, 0xb2, 0x09, 0x65, 0x86, 0xdd, 0xd4, 0x6d, 0xa3, 0x29, 0x20, 0xd9, 0x8d, 0x40,
	0x92, 0x28, 0x6f, 0x2d, 0x24, 0x1e, 0x86, 0xb2, 0xf6, 0x8d, 0xe6, 0x8e, 0xc3, 0xbc, 0xb6, 0x36,
	0x73, 0x2c, 0xd9, 0x42, 0x25, 0xe8, 0xf8, 0xa8, 0x9b, 0xb5, 0x3a, 0x0f, 0x9f, 0xc9, 0x8d, 0x6b,
	0x11, 0xad, 0x12, 0x58, 0xb5, 0xc9, 0xe3, 0x2e, 0x8c, 0x7b, 0x1e, 0x70, 0xa6, 0xf7, 0x01, 0xab,
	0x25, 0x98, 0x8b, 0x35, 0xcc, 0x87, 0xed, 0x94, 0xb4, 0x43, 0xd8, 0x4e, 0x49, 0x1b, 0xcd, 0xc0,
	0xf8, 0x99, 0xd1, 0x68, 0x11, 0xf1, 0xfc, 0x82, 0xc5, 0xe3, 0xd1, 0x87, 0x0a, 0xde, 0x83, 0xc5,
	0x5d, 0xab, 0x4a, 0x7a, 0x01, 0xa8, 0xbc, 0xb1, 0x98, 0x79, 0x12, 0x7b, 0x0b, 0xe8, 0x3f, 0x30,
	0x61, 0x51, 0xfd, 0xc4, 0xaa, 0x06, 0x02, 0x73, 0x5a, 0xd6, 0xa2, 0xbe, 0x14, 0x8c, 0xa1, 0x18,
	0x2f, 0x2d, 0x00, 0x14, 0xdf, 0x07, 0x5c, 0x22, 0x6c, 0x68, 0xa5, 0xf8, 0x73, 0x9e, 0xf7, 0xd2,
	0xc4, 0x77, 0xdb, 0xa6, 0xf4, 0xd8, 0xf6, 0x12, 0x56, 0x22, 0x37, 0x5d, 0xb2, 0x6a, 0x6c, 0xbb,
	0x56, 0x2f, 0x3b, 0x9a, 0xeb, 0xda, 0xf1, 0xfe, 0x2e, 0x00, 0x98, 0x27, 0x86, 0xe3, 0x90, 0x86,
	0x9f, 0x2f, 0x46, 0x83, 0xf7, 0x2c, 0x28, 0xe5, 0x2a, 0x7e, 0x2e, 0x49, 0xe7, 0x7d, 0x92, 0x85,
	0x71, 0xa9, 0xef, 0x06, 0xdf, 0x83, 0xa2, 0x46, 0x4c, 0x62, 0x9d, 0x91, 0xa8, 0xbc, 0x78, 0x6c,
	0x96, 0x61, 0x29, 0x81, 0x4b, 0x00, 0xff, 0x12, 0x56, 0xb7, 0x4f, 0x88, 0x79, 0x5a, 0xae, 0x89,
	0xb3, 0xd5, 0x21, 0x54, 0x24, 0x25, 0x71, 0x5c, 0x82, 0x9b, 0x03, 0x48, 0x16, 0x18, 0xa8, 0x90,
	0xf3, 0xc4, 0xa9, 0xb0, 0x2e, 0x85, 0x6b, 0xbc, 0x05, 0x39, 0xcd, 0x70, 0x4e, 0xfd, 0x07, 0x28,
	0x31, 0x61, 0x1a, 0x32, 0x67, 0x46, 0x43, 0xe0, 0xef, 0x7f, 0x22, 0x04, 0x63, 0x9e, 0xe1, 0x9c,
	0x8a, 0x14, 0xcc, 0xbf, 0xf1, 0x73, 0x28, 0x46, 0x73, 0xa7, 0xe1, 0x9c, 0x8a, 0xa2, 0x10, 0xe3,
	0xde, 0x0c, 0x8c, 0x37, 0x2c, 0xdb, 0x62, 0x42, 0x7a, 0xb0, 0xc0, 0xdf, 0x29, 0xb0, 0x94, 0x20,
	0x4c, 0x78, 0x74, 0x1f, 0xf2, 0xbe, 0xe6, 0xee, 0xc4, 0x3b, 0x17, 0x4d, 0xbc, 0xc2, 0x2f, 0x2d,
	0xe7, 0x09, 0x7e, 0xb4, 0x01, 0x13, 0x76, 0x5b, 0xe7, 0x0e, 0x04, 0x59, 0x22, 0x81, 0x2b, 0x6b,
	0x73, 0xbd, 0x7e, 0x7c, 0x94, 0x08, 0x3b, 0xa2, 0x91, 0x8a, 0x68, 0x38, 0xa7, 0xf1, 0xf1, 0xa1,
	0x71, 0x37, 0xe2, 0xb8, 0x84, 0x1b, 0xb7, 0x05, 0x98, 0x4a, 0x9a, 0x2d, 0x01, 0xce, 0xbf, 0x2a,
	0x90, 0xe7, 0xc9, 0x8b, 0xdf, 0xd6, 0x1c, 0xe4, 0x6a, 0x9e, 0x6b, 0xeb, 0xe7, 0x8a, 0x27, 0xfc,
	0xf5, 0x91, 0x55, 0xf5, 0xb7, 0x5c, 0xaf, 0x4a, 0xbc, 0xf0, 0xed, 0xe4, 0xb5, 0x09, 0xbe, 0x2e,
	0xf3, 0x44, 0x22, 0xde, 0x83, 0xb8, 0xc2, 0x6c, 0xf0, 0x14, 0x3a, 0xe5, 0x43, 0x37, 0x5d, 0x87,
	0x11, 0x87, 0xf1, 0x22, 0x1a, 0x96, 0x8f, 0xed, 0x80, 0xe6, 0x3f, 0xcb, 0xe0, 0x90, 0x9f, 0xe7,
	0x79, 0x19, 0xcd, 0x68, 0x79, 0x4e, 0xf1, 0x73, 0xa2, 0x0f, 0x83, 0x5f, 0x7e, 0xb3, 0x01, 0x0c,
	0x4e, 0xcb, 0xc6, 0xa6, 0xe4, 0x36, 0xb9, 0x0b, 0xc9, 0xb1, 0x31, 0x0b, 0x59, 0xb7, 0x56, 0xa3,
	0x24, 0x0c, 0x0e, 0xb1, 0x3a, 0x8f, 0x99, 0x4c, 0x77, 0xcc, 0xe8, 0x3c, 0xbf, 0xc5, 0x2a, 0x11,
	0x60, 0x3f, 0x0a, 0x6d, 0xef, 0x0a, 0x1a, 0x35, 0x5a, 0x24, 0x42, 0x7c, 0x85, 0x5f, 0x3c, 0x45,
	0xfc, 0xa9, 0xf8, 0x39, 0xa2, 0xe9, 0x7a, 0x32, 0x25, 0xa1, 0x17, 0x09, 0xf7, 0x71, 0x05, 0xb2,
	0xcc, 0xe5, 0x1b, 0x22, 0xd6, 0x99, 0xeb, 0x93, 0x23, 0x90, 0x67, 0x52, 0x21, 0x1f, 0xeb, 0x87,
	0xbc, 0xfb, 0xaa, 0xc7, 0x63, 0xaf, 0x3a, 0xdb, 0x73, 0xd5, 0xe2, 0x9a, 0x26, 0xce, 0xaf, 0x89,
	0x67, 0xb3, 0x58, 0xff, 0x44, 0x36, 0xfb, 0xa8, 0x74, 0x92, 0xce, 0xb6, 0xdf, 0x9a, 0x56, 0x88,
	0x33, 0x54, 0x3e, 0x5b, 0x00, 0x60, 0x86, 0x57, 0x27, 0xac, 0x0b, 0x89, 0x7c, 0x40, 0x39, 0xb2,
	0x12, 0x22, 0xb3, 0xb7, 0x16, 0x8c, 0xf5, 0xd5, 0x02, 0xbf, 0xb7, 0xa3, 0xc4, 0xa9, 0xea, 0xd4,
	0x6d, 0x79, 0x66, 0x10, 0x94, 0x53, 0x1a, 0xf8, 0xa4, 0x0a, 0xa7, 0xe0, 0xef, 0x15, 0xb8, 0x35,
	0x88, 0xdd, 0x22, 0x4e, 0x9e, 0x42, 0x9e, 0xdb, 0xc1, 0x3b, 0x4d, 0xdf, 0xfc, 0x7f, 0x6d, 0xdc,
	0x48, 0x69, 0xea, 0x7c, 0x7e, 0xbf, 0x0d, 0xd5, 0x72, 0x75, 0xf1, 0xc5, 0x8d, 0xe6, 0x3d, 0xbd,
	0x6f, 0x88, 0xa8, 0xd9, 0x79, 0x33, 0x54, 0x8f, 0x3f, 0x83, 0xe2, 0x96, 0xc1, 0x4e, 0x12, 0x7b,
	0xb1, 0x39, 0xc8, 0xb5, 0x7a, 0xcb, 0xd6, 0x44, 0xcb, 0x0a, 0x6a, 0xd6, 0x5f, 0x0a, 0x2c, 0x25,
	0xf0, 0x0b, 0x4f, 0xde, 0x2b, 0x29, 0x9d, 0xd9, 0x5e, 0x34, 0xfc, 0xd3, 0x84, 0x0e, 0xdb, 0x9d,
	0xfd, 0x73, 0x7d, 0xd3, 0x2b, 0xb8, 0x1e, 0xbd, 0xb9, 0x4a, 0x9b, 0x32, 0x62, 0xef, 0x13, 0x4a,
	0x8d, 0x3a, 0xb9, 0x68, 0xe8, 0xe1, 0x9b, 0x70, 0x23, 0x55, 0xb4, 0x78, 0x04, 0xcf, 0x41, 0x3d,
	0x24, 0x94, 0x55, 0x82, 0xdc, 0xde, 0x35, 0x85, 0x5d, 0xa0, 0x88, 0x2f, 0xc0, 0xbc, 0x54, 0x56,
	0xa0, 0xea, 0xd6, 0x1f, 0x0a, 0xcc, 0xca, 0xe3, 0x0c, 0xdd, 0x84, 0x15, 0xad, 0xbc, 0xbd, 0xbb,
	0xa3, 0xe9, 0x5b, 0x65, 0xed, 0x70, 0xf7, 0xe9, 0x93, 0x57, 0x7a, 0xa9, 0xfc, 0xec, 0x50, 0x3f,
	0x7c, 0xf5, 0x62, 0x47, 0x3f, 0x3a, 0xa8, 0xbc, 0xd8, 0xd9, 0x2e, 0x3f, 0x2b, 0xef, 0x3c, 0x9d,
	0x1e, 0x41, 0xcb, 0xb0, 0x18, 0x7f, 0x74, 0x6b, 0x6f, 0xa7, 0x52, 0x99, 0x56, 0xd0, 0x35, 0x28,
	0xc6, 0x1f, 0x0a, 0x76, 0xa6, 0x47, 0x51, 0x11, 0xae, 0xc6, 0x9f, 0x2a, 0xef, 0x4f, 0x67, 0x92,
	0xe5, 0x1c, 0x7c, 0xa1, 0xed, 0x3f, 0xd9, 0x9b, 0x1e, 0xdb, 0xf8, 0x0d, 0xc1, 0x95, 0x3e, 0xac,
	0x89, 0x77, 0x66, 0x99, 0x04, 0xfd, 0x20, 0x1f, 0xc3, 0xba, 0x47, 0x6d, 0xf4, 0x20, 0x7d, 0x9c,
	0x90, 0x4e, 0xf6, 0xea, 0xbd, 0xe1, 0x19, 0x69, 0x13, 0x8f, 0x20, 0x0a, 0xb3, 0xf2, 0x99, 0x18,
	0xdd, 0x8a, 0xe6, 0x85, 0xb8, 0x21, 0x5d, 0xfd, 0xdf, 0xc0, 0x67, 0xb9, 0xd2, 0xf7, 0x0a, 0xcc,
	0x27, 0x0c, 0xa4, 0x68, 0x7d, 0x10, 0x67, 0xba, 0x26, 0x73, 0xf5, 0xce, 0x70, 0x0c, 0xdc, 0x88,
	0xb7, 0x70, 0x45, 0x9a, 0x0c, 0xd0, 0xed, 0x41, 0x47, 0xba, 0x00, 0xf9, 0xb5, 0xe1, 0x26, 0x40,
	0x3c, 0x82, 0xde, 0x29, 0x50, 0x88, 0x1b, 0x3c, 0x50, 0xd4, 0x95, 0x94, 0xd9, 0x46, 0xbd, 0x3b,
	0x04, 0x47, 0xc7, 0x86, 0x6f, 0x83, 0x2b, 0x88, 0x35, 0x63, 0x53, 0xe6, 0x55, 0x9a, 0x25, 0xf7,
	0x86, 0x63, 0xea, 0x18, 0xf3, 0xa3, 0x22, 0xf9, 0xed, 0xa5, 0x67, 0xe4, 0x41, 0xf7, 0xd3, 0x51,
	0x96, 0x4d, 0x5f, 0xea, 0x83, 0xa1, 0xf9, 0x3a, 0x56, 0x7d, 0xa3, 0xc0, 0x5c, 0xec, 0x18, 0x84,
	0xee, 0x4a, 0x42, 0x3e, 0x79, 0xd0, 0x52, 0x37, 0x86, 0x61, 0xe9, 0x98, 0xf1, 0xb3, 0x02, 0x4b,
	0xa9, 0xe3, 0x10, 0x7a, 0x14, 0x91, 0x3d, 0xe8, 0x70, 0xa6, 0x3e, 0xbe, 0x08, 0x6b, 0x0f, 0x4a,
	0xb1, 0x33, 0x8d, 0x04, 0xa5, 0xb4, 0x61, 0x4a, 0x82, 0x52, 0xea, 0xc8, 0x74, 0x6e, 0x86, 0x7c,
	0x26, 0x91, 0x9b, 0x91, 0x38, 0xf5, 0xc8, 0xcd, 0x48, 0x1e, 0x79, 0xf0, 0x08, 0xfa, 0xa0, 0x80,
	0x1a, 0xdf, 0xae, 0xa3, 0x01, 0x7c, 0xeb, 0x1f, 0x20, 0xd4, 0xcd, 0xa1, 0x78, 0xfa, 0xa2, 0x37,
	0xa6, 0xed, 0x95, 0x46, 0x6f, 0xf2, 0x08, 0x20, 0x8d, 0xde, 0xb4, 0xae, 0x7a, 0x04, 0xfd, 0xa2,
	0x00, 0x4e, 0xef, 0x4f, 0x51, 0x6c, 0x0c, 0xa6, 0x37, 0xe3, 0xea, 0xa7, 0x17, 0xe2, 0xed, 0x01,
	0x2a, 0xb6, 0x33, 0x94, 0x00, 0x95, 0xd6, 0xda, 0x4a, 0x80, 0x4a, 0x6d, 0x3c, 0xf1, 0x08, 0xfa,
	0x49, 0x81, 0xc5, 0x94, 0x3e, 0x4d, 0xd2, 0x1d, 0x0c, 0xd6, 0x34, 0xaa, 0x0f, 0x87, 0x67, 0xec,
	0x18, 0xe6, 0xc1, 0x65, 0x49, 0x23, 0x87, 0xa2, 0x25, 0x3f, 0xbe, 0x75, 0x54, 0xff, 0x3f, 0xd8,
	0xe1, 0x50, 0xe7, 0xd6, 0xe6, 0x97, 0x77, 0xeb, 0x6e, 0xc3, 0x70, 0xea, 0x6b, 0x9f, 0x6c, 0x30,
	0xb6, 0x66, 0xba, 0xf6, 0x3a, 0xff, 0xef, 0xc3, 0x74, 0x1b, 0xeb, 0x34, 0x68, 0xa7, 0x68, 0xff,
	0xbf, 0x23, 0xc7, 0x59, 0x7e, 0x64, 0xf3, 0xef, 0x00, 0x00, 0x00, 0xff, 0xff, 0x7b, 0x02, 0xeb,
	0x67, 0x47, 0x19, 0x00, 0x00,
}
