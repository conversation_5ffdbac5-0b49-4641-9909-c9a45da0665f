// Code generated by protoc-gen-gogo.
// source: src/numericsvr/numericsvr.proto
// DO NOT EDIT!

/*
	Package numericsvr is a generated protocol buffer package.

	It is generated from these files:
		src/numericsvr/numericsvr.proto

	It has these top-level messages:
		GetGuildNumericListReq
		GuildMemerNumeric
		GetGuildNumericListResp
		GetPersonalNumericReq
		GetPersonalNumericResp
		BatchGetPersonalNumericReq
		PersonalNumeric
		BatchGetPersonalNumericResp
		RecordSendGiftEventReq
		RecordSendGiftEventResp
		UserGiftEventInfo
		BatchRecordSendGiftEventReq
		BatchRecordSendGiftEventResp
		RecordConsumeEventReq
		RecordConsumeEventResp
		NotifyGuildChangeReq
		GetGuildGiftTotalValueReq
		GetGuildGiftTotalValueResp
		GetPersonalRankingReq
		GetPersonalRankingResp
		GetRankListReq
		GetRankListResp
		AddUserNumericReq
		AddUserNumericResp
*/
package numericsvr

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 记录 用户消费行为
// 导致更新Numeric 只可能引起公会土豪魅力值变化
type CONSUME_TYPE int32

const (
	CONSUME_TYPE_ENUM_CONSUME_TT_GAME   CONSUME_TYPE = 1
	CONSUME_TYPE_ENUM_CONSUME_HAPPYCITY CONSUME_TYPE = 2
)

var CONSUME_TYPE_name = map[int32]string{
	1: "ENUM_CONSUME_TT_GAME",
	2: "ENUM_CONSUME_HAPPYCITY",
}
var CONSUME_TYPE_value = map[string]int32{
	"ENUM_CONSUME_TT_GAME":   1,
	"ENUM_CONSUME_HAPPYCITY": 2,
}

func (x CONSUME_TYPE) Enum() *CONSUME_TYPE {
	p := new(CONSUME_TYPE)
	*p = x
	return p
}
func (x CONSUME_TYPE) String() string {
	return proto.EnumName(CONSUME_TYPE_name, int32(x))
}
func (x *CONSUME_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CONSUME_TYPE_value, data, "CONSUME_TYPE")
	if err != nil {
		return err
	}
	*x = CONSUME_TYPE(value)
	return nil
}
func (CONSUME_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorNumericsvr, []int{0} }

type AddUserNumericType int32

const (
	AddUserNumericType_ENUM_ADD_TYPE_INVALID    AddUserNumericType = 0
	AddUserNumericType_ENUM_ADD_TYPE_LIVE_TO_TT AddUserNumericType = 1
)

var AddUserNumericType_name = map[int32]string{
	0: "ENUM_ADD_TYPE_INVALID",
	1: "ENUM_ADD_TYPE_LIVE_TO_TT",
}
var AddUserNumericType_value = map[string]int32{
	"ENUM_ADD_TYPE_INVALID":    0,
	"ENUM_ADD_TYPE_LIVE_TO_TT": 1,
}

func (x AddUserNumericType) Enum() *AddUserNumericType {
	p := new(AddUserNumericType)
	*p = x
	return p
}
func (x AddUserNumericType) String() string {
	return proto.EnumName(AddUserNumericType_name, int32(x))
}
func (x *AddUserNumericType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(AddUserNumericType_value, data, "AddUserNumericType")
	if err != nil {
		return err
	}
	*x = AddUserNumericType(value)
	return nil
}
func (AddUserNumericType) EnumDescriptor() ([]byte, []int) { return fileDescriptorNumericsvr, []int{1} }

type GetRankListReq_RANK_TYPE int32

const (
	GetRankListReq_WEEK_RICH   GetRankListReq_RANK_TYPE = 1
	GetRankListReq_DAY_RICH    GetRankListReq_RANK_TYPE = 2
	GetRankListReq_WEEK_CHARM  GetRankListReq_RANK_TYPE = 3
	GetRankListReq_DAY_CHARM   GetRankListReq_RANK_TYPE = 4
	GetRankListReq_MONTH_RICH  GetRankListReq_RANK_TYPE = 5
	GetRankListReq_MONTH_CHARM GetRankListReq_RANK_TYPE = 6
)

var GetRankListReq_RANK_TYPE_name = map[int32]string{
	1: "WEEK_RICH",
	2: "DAY_RICH",
	3: "WEEK_CHARM",
	4: "DAY_CHARM",
	5: "MONTH_RICH",
	6: "MONTH_CHARM",
}
var GetRankListReq_RANK_TYPE_value = map[string]int32{
	"WEEK_RICH":   1,
	"DAY_RICH":    2,
	"WEEK_CHARM":  3,
	"DAY_CHARM":   4,
	"MONTH_RICH":  5,
	"MONTH_CHARM": 6,
}

func (x GetRankListReq_RANK_TYPE) Enum() *GetRankListReq_RANK_TYPE {
	p := new(GetRankListReq_RANK_TYPE)
	*p = x
	return p
}
func (x GetRankListReq_RANK_TYPE) String() string {
	return proto.EnumName(GetRankListReq_RANK_TYPE_name, int32(x))
}
func (x *GetRankListReq_RANK_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GetRankListReq_RANK_TYPE_value, data, "GetRankListReq_RANK_TYPE")
	if err != nil {
		return err
	}
	*x = GetRankListReq_RANK_TYPE(value)
	return nil
}
func (GetRankListReq_RANK_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorNumericsvr, []int{20, 0}
}

type GetGuildNumericListReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	Index   uint32 `protobuf:"varint,2,req,name=index" json:"index"`
	Count   uint32 `protobuf:"varint,3,req,name=count" json:"count"`
}

func (m *GetGuildNumericListReq) Reset()                    { *m = GetGuildNumericListReq{} }
func (m *GetGuildNumericListReq) String() string            { return proto.CompactTextString(m) }
func (*GetGuildNumericListReq) ProtoMessage()               {}
func (*GetGuildNumericListReq) Descriptor() ([]byte, []int) { return fileDescriptorNumericsvr, []int{0} }

func (m *GetGuildNumericListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildNumericListReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *GetGuildNumericListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GuildMemerNumeric struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Numeric   uint32 `protobuf:"varint,2,req,name=numeric" json:"numeric"`
	Numeric64 uint64 `protobuf:"varint,3,opt,name=numeric64" json:"numeric64"`
}

func (m *GuildMemerNumeric) Reset()                    { *m = GuildMemerNumeric{} }
func (m *GuildMemerNumeric) String() string            { return proto.CompactTextString(m) }
func (*GuildMemerNumeric) ProtoMessage()               {}
func (*GuildMemerNumeric) Descriptor() ([]byte, []int) { return fileDescriptorNumericsvr, []int{1} }

func (m *GuildMemerNumeric) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GuildMemerNumeric) GetNumeric() uint32 {
	if m != nil {
		return m.Numeric
	}
	return 0
}

func (m *GuildMemerNumeric) GetNumeric64() uint64 {
	if m != nil {
		return m.Numeric64
	}
	return 0
}

type GetGuildNumericListResp struct {
	NumericList []*GuildMemerNumeric `protobuf:"bytes,1,rep,name=numeric_list,json=numericList" json:"numeric_list,omitempty"`
}

func (m *GetGuildNumericListResp) Reset()         { *m = GetGuildNumericListResp{} }
func (m *GetGuildNumericListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildNumericListResp) ProtoMessage()    {}
func (*GetGuildNumericListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNumericsvr, []int{2}
}

func (m *GetGuildNumericListResp) GetNumericList() []*GuildMemerNumeric {
	if m != nil {
		return m.NumericList
	}
	return nil
}

type GetPersonalNumericReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetPersonalNumericReq) Reset()                    { *m = GetPersonalNumericReq{} }
func (m *GetPersonalNumericReq) String() string            { return proto.CompactTextString(m) }
func (*GetPersonalNumericReq) ProtoMessage()               {}
func (*GetPersonalNumericReq) Descriptor() ([]byte, []int) { return fileDescriptorNumericsvr, []int{3} }

func (m *GetPersonalNumericReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPersonalNumericResp struct {
	ConsumeNumeric   uint32 `protobuf:"varint,1,req,name=consume_numeric,json=consumeNumeric" json:"consume_numeric"`
	CharmNumeric     uint32 `protobuf:"varint,2,req,name=charm_numeric,json=charmNumeric" json:"charm_numeric"`
	ConsumeNumeric64 uint64 `protobuf:"varint,3,opt,name=consume_numeric64,json=consumeNumeric64" json:"consume_numeric64"`
	CharmNumeric64   uint64 `protobuf:"varint,4,opt,name=charm_numeric64,json=charmNumeric64" json:"charm_numeric64"`
}

func (m *GetPersonalNumericResp) Reset()                    { *m = GetPersonalNumericResp{} }
func (m *GetPersonalNumericResp) String() string            { return proto.CompactTextString(m) }
func (*GetPersonalNumericResp) ProtoMessage()               {}
func (*GetPersonalNumericResp) Descriptor() ([]byte, []int) { return fileDescriptorNumericsvr, []int{4} }

func (m *GetPersonalNumericResp) GetConsumeNumeric() uint32 {
	if m != nil {
		return m.ConsumeNumeric
	}
	return 0
}

func (m *GetPersonalNumericResp) GetCharmNumeric() uint32 {
	if m != nil {
		return m.CharmNumeric
	}
	return 0
}

func (m *GetPersonalNumericResp) GetConsumeNumeric64() uint64 {
	if m != nil {
		return m.ConsumeNumeric64
	}
	return 0
}

func (m *GetPersonalNumericResp) GetCharmNumeric64() uint64 {
	if m != nil {
		return m.CharmNumeric64
	}
	return 0
}

type BatchGetPersonalNumericReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatchGetPersonalNumericReq) Reset()         { *m = BatchGetPersonalNumericReq{} }
func (m *BatchGetPersonalNumericReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetPersonalNumericReq) ProtoMessage()    {}
func (*BatchGetPersonalNumericReq) Descriptor() ([]byte, []int) {
	return fileDescriptorNumericsvr, []int{5}
}

func (m *BatchGetPersonalNumericReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type PersonalNumeric struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Charm   uint32 `protobuf:"varint,2,req,name=charm" json:"charm"`
	Rich    uint32 `protobuf:"varint,3,req,name=rich" json:"rich"`
	Charm64 uint64 `protobuf:"varint,4,opt,name=charm64" json:"charm64"`
	Rich64  uint64 `protobuf:"varint,5,opt,name=rich64" json:"rich64"`
}

func (m *PersonalNumeric) Reset()                    { *m = PersonalNumeric{} }
func (m *PersonalNumeric) String() string            { return proto.CompactTextString(m) }
func (*PersonalNumeric) ProtoMessage()               {}
func (*PersonalNumeric) Descriptor() ([]byte, []int) { return fileDescriptorNumericsvr, []int{6} }

func (m *PersonalNumeric) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PersonalNumeric) GetCharm() uint32 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *PersonalNumeric) GetRich() uint32 {
	if m != nil {
		return m.Rich
	}
	return 0
}

func (m *PersonalNumeric) GetCharm64() uint64 {
	if m != nil {
		return m.Charm64
	}
	return 0
}

func (m *PersonalNumeric) GetRich64() uint64 {
	if m != nil {
		return m.Rich64
	}
	return 0
}

type BatchGetPersonalNumericResp struct {
	NumericList []*PersonalNumeric `protobuf:"bytes,1,rep,name=numeric_list,json=numericList" json:"numeric_list,omitempty"`
}

func (m *BatchGetPersonalNumericResp) Reset()         { *m = BatchGetPersonalNumericResp{} }
func (m *BatchGetPersonalNumericResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetPersonalNumericResp) ProtoMessage()    {}
func (*BatchGetPersonalNumericResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNumericsvr, []int{7}
}

func (m *BatchGetPersonalNumericResp) GetNumericList() []*PersonalNumeric {
	if m != nil {
		return m.NumericList
	}
	return nil
}

// 记录 用户送礼物的行为
// 导致更新Numeric 可能引起公会土豪魅力值变化 也可能引起用户的土豪魅力值变化
type RecordSendGiftEventReq struct {
	GiverUid      uint32 `protobuf:"varint,1,req,name=giver_uid,json=giverUid" json:"giver_uid"`
	ReceiverUid   uint32 `protobuf:"varint,2,req,name=receiver_uid,json=receiverUid" json:"receiver_uid"`
	RichValue     uint32 `protobuf:"varint,3,req,name=rich_value,json=richValue" json:"rich_value"`
	GiverGuild    uint32 `protobuf:"varint,4,req,name=giver_guild,json=giverGuild" json:"giver_guild"`
	ReceiverGuild uint32 `protobuf:"varint,5,req,name=receiver_guild,json=receiverGuild" json:"receiver_guild"`
	CharmValue    uint32 `protobuf:"varint,6,req,name=charm_value,json=charmValue" json:"charm_value"`
	OrderId       string `protobuf:"bytes,7,req,name=order_id,json=orderId" json:"order_id"`
	ChannelId     uint32 `protobuf:"varint,8,opt,name=channel_id,json=channelId" json:"channel_id"`
	ChannelGuild  uint32 `protobuf:"varint,9,opt,name=channel_guild,json=channelGuild" json:"channel_guild"`
	PriceType     uint32 `protobuf:"varint,10,opt,name=price_type,json=priceType" json:"price_type"`
	GiftId        uint32 `protobuf:"varint,11,opt,name=gift_id,json=giftId" json:"gift_id"`
}

func (m *RecordSendGiftEventReq) Reset()                    { *m = RecordSendGiftEventReq{} }
func (m *RecordSendGiftEventReq) String() string            { return proto.CompactTextString(m) }
func (*RecordSendGiftEventReq) ProtoMessage()               {}
func (*RecordSendGiftEventReq) Descriptor() ([]byte, []int) { return fileDescriptorNumericsvr, []int{8} }

func (m *RecordSendGiftEventReq) GetGiverUid() uint32 {
	if m != nil {
		return m.GiverUid
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetReceiverUid() uint32 {
	if m != nil {
		return m.ReceiverUid
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetRichValue() uint32 {
	if m != nil {
		return m.RichValue
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetGiverGuild() uint32 {
	if m != nil {
		return m.GiverGuild
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetReceiverGuild() uint32 {
	if m != nil {
		return m.ReceiverGuild
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetCharmValue() uint32 {
	if m != nil {
		return m.CharmValue
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *RecordSendGiftEventReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetChannelGuild() uint32 {
	if m != nil {
		return m.ChannelGuild
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type RecordSendGiftEventResp struct {
	LevelChanageUidList    []uint32 `protobuf:"varint,1,rep,name=level_chanage_uid_list,json=levelChanageUidList" json:"level_chanage_uid_list,omitempty"`
	RealCharm              uint32   `protobuf:"varint,2,opt,name=real_charm,json=realCharm" json:"real_charm"`
	GiverCurrAllRich       uint32   `protobuf:"varint,3,opt,name=giver_curr_all_rich,json=giverCurrAllRich" json:"giver_curr_all_rich"`
	ReceiverCurrAllCharm   uint32   `protobuf:"varint,4,opt,name=receiver_curr_all_charm,json=receiverCurrAllCharm" json:"receiver_curr_all_charm"`
	RealRich               uint32   `protobuf:"varint,5,opt,name=real_rich,json=realRich" json:"real_rich"`
	BeforeRich             uint32   `protobuf:"varint,6,opt,name=before_rich,json=beforeRich" json:"before_rich"`
	BeforeRich64           uint64   `protobuf:"varint,7,opt,name=before_rich64,json=beforeRich64" json:"before_rich64"`
	GiverCurrAllRich64     uint64   `protobuf:"varint,8,opt,name=giver_curr_all_rich64,json=giverCurrAllRich64" json:"giver_curr_all_rich64"`
	ReceiverCurrAllCharm64 uint64   `protobuf:"varint,9,opt,name=receiver_curr_all_charm64,json=receiverCurrAllCharm64" json:"receiver_curr_all_charm64"`
}

func (m *RecordSendGiftEventResp) Reset()         { *m = RecordSendGiftEventResp{} }
func (m *RecordSendGiftEventResp) String() string { return proto.CompactTextString(m) }
func (*RecordSendGiftEventResp) ProtoMessage()    {}
func (*RecordSendGiftEventResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNumericsvr, []int{9}
}

func (m *RecordSendGiftEventResp) GetLevelChanageUidList() []uint32 {
	if m != nil {
		return m.LevelChanageUidList
	}
	return nil
}

func (m *RecordSendGiftEventResp) GetRealCharm() uint32 {
	if m != nil {
		return m.RealCharm
	}
	return 0
}

func (m *RecordSendGiftEventResp) GetGiverCurrAllRich() uint32 {
	if m != nil {
		return m.GiverCurrAllRich
	}
	return 0
}

func (m *RecordSendGiftEventResp) GetReceiverCurrAllCharm() uint32 {
	if m != nil {
		return m.ReceiverCurrAllCharm
	}
	return 0
}

func (m *RecordSendGiftEventResp) GetRealRich() uint32 {
	if m != nil {
		return m.RealRich
	}
	return 0
}

func (m *RecordSendGiftEventResp) GetBeforeRich() uint32 {
	if m != nil {
		return m.BeforeRich
	}
	return 0
}

func (m *RecordSendGiftEventResp) GetBeforeRich64() uint64 {
	if m != nil {
		return m.BeforeRich64
	}
	return 0
}

func (m *RecordSendGiftEventResp) GetGiverCurrAllRich64() uint64 {
	if m != nil {
		return m.GiverCurrAllRich64
	}
	return 0
}

func (m *RecordSendGiftEventResp) GetReceiverCurrAllCharm64() uint64 {
	if m != nil {
		return m.ReceiverCurrAllCharm64
	}
	return 0
}

type UserGiftEventInfo struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId      uint32 `protobuf:"varint,2,opt,name=guild_id,json=guildId" json:"guild_id"`
	AddValue     uint32 `protobuf:"varint,3,opt,name=add_value,json=addValue" json:"add_value"`
	FinalValue   uint32 `protobuf:"varint,4,opt,name=final_value,json=finalValue" json:"final_value"`
	LevelChange  bool   `protobuf:"varint,5,opt,name=level_change,json=levelChange" json:"level_change"`
	FinalValue64 uint64 `protobuf:"varint,6,opt,name=final_value64,json=finalValue64" json:"final_value64"`
}

func (m *UserGiftEventInfo) Reset()                    { *m = UserGiftEventInfo{} }
func (m *UserGiftEventInfo) String() string            { return proto.CompactTextString(m) }
func (*UserGiftEventInfo) ProtoMessage()               {}
func (*UserGiftEventInfo) Descriptor() ([]byte, []int) { return fileDescriptorNumericsvr, []int{10} }

func (m *UserGiftEventInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserGiftEventInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UserGiftEventInfo) GetAddValue() uint32 {
	if m != nil {
		return m.AddValue
	}
	return 0
}

func (m *UserGiftEventInfo) GetFinalValue() uint32 {
	if m != nil {
		return m.FinalValue
	}
	return 0
}

func (m *UserGiftEventInfo) GetLevelChange() bool {
	if m != nil {
		return m.LevelChange
	}
	return false
}

func (m *UserGiftEventInfo) GetFinalValue64() uint64 {
	if m != nil {
		return m.FinalValue64
	}
	return 0
}

// 记录 用户批量送礼物的行为
// 导致更新Numeric 可能引起公会土豪魅力值变化 也可能引起用户的土豪魅力值变化
type BatchRecordSendGiftEventReq struct {
	GiverUserInfo        *UserGiftEventInfo   `protobuf:"bytes,1,req,name=giver_user_info,json=giverUserInfo" json:"giver_user_info,omitempty"`
	ReceiverUserInfoList []*UserGiftEventInfo `protobuf:"bytes,2,rep,name=receiver_user_info_list,json=receiverUserInfoList" json:"receiver_user_info_list,omitempty"`
	OrderId              string               `protobuf:"bytes,3,req,name=order_id,json=orderId" json:"order_id"`
	ChannelId            uint32               `protobuf:"varint,4,opt,name=channel_id,json=channelId" json:"channel_id"`
	ChannelGuild         uint32               `protobuf:"varint,5,opt,name=channel_guild,json=channelGuild" json:"channel_guild"`
	PriceType            uint32               `protobuf:"varint,6,opt,name=price_type,json=priceType" json:"price_type"`
	GiftId               uint32               `protobuf:"varint,7,opt,name=gift_id,json=giftId" json:"gift_id"`
}

func (m *BatchRecordSendGiftEventReq) Reset()         { *m = BatchRecordSendGiftEventReq{} }
func (m *BatchRecordSendGiftEventReq) String() string { return proto.CompactTextString(m) }
func (*BatchRecordSendGiftEventReq) ProtoMessage()    {}
func (*BatchRecordSendGiftEventReq) Descriptor() ([]byte, []int) {
	return fileDescriptorNumericsvr, []int{11}
}

func (m *BatchRecordSendGiftEventReq) GetGiverUserInfo() *UserGiftEventInfo {
	if m != nil {
		return m.GiverUserInfo
	}
	return nil
}

func (m *BatchRecordSendGiftEventReq) GetReceiverUserInfoList() []*UserGiftEventInfo {
	if m != nil {
		return m.ReceiverUserInfoList
	}
	return nil
}

func (m *BatchRecordSendGiftEventReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *BatchRecordSendGiftEventReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BatchRecordSendGiftEventReq) GetChannelGuild() uint32 {
	if m != nil {
		return m.ChannelGuild
	}
	return 0
}

func (m *BatchRecordSendGiftEventReq) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *BatchRecordSendGiftEventReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type BatchRecordSendGiftEventResp struct {
	GiverUserInfo        *UserGiftEventInfo   `protobuf:"bytes,1,opt,name=giver_user_info,json=giverUserInfo" json:"giver_user_info,omitempty"`
	ReceiverUserInfoList []*UserGiftEventInfo `protobuf:"bytes,2,rep,name=receiver_user_info_list,json=receiverUserInfoList" json:"receiver_user_info_list,omitempty"`
}

func (m *BatchRecordSendGiftEventResp) Reset()         { *m = BatchRecordSendGiftEventResp{} }
func (m *BatchRecordSendGiftEventResp) String() string { return proto.CompactTextString(m) }
func (*BatchRecordSendGiftEventResp) ProtoMessage()    {}
func (*BatchRecordSendGiftEventResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNumericsvr, []int{12}
}

func (m *BatchRecordSendGiftEventResp) GetGiverUserInfo() *UserGiftEventInfo {
	if m != nil {
		return m.GiverUserInfo
	}
	return nil
}

func (m *BatchRecordSendGiftEventResp) GetReceiverUserInfoList() []*UserGiftEventInfo {
	if m != nil {
		return m.ReceiverUserInfoList
	}
	return nil
}

type RecordConsumeEventReq struct {
	Uid         uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId     uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	RichValue   uint32 `protobuf:"varint,3,req,name=rich_value,json=richValue" json:"rich_value"`
	ConsumeType uint32 `protobuf:"varint,4,req,name=consume_type,json=consumeType" json:"consume_type"`
	OrderInfo   string `protobuf:"bytes,5,req,name=order_info,json=orderInfo" json:"order_info"`
}

func (m *RecordConsumeEventReq) Reset()                    { *m = RecordConsumeEventReq{} }
func (m *RecordConsumeEventReq) String() string            { return proto.CompactTextString(m) }
func (*RecordConsumeEventReq) ProtoMessage()               {}
func (*RecordConsumeEventReq) Descriptor() ([]byte, []int) { return fileDescriptorNumericsvr, []int{13} }

func (m *RecordConsumeEventReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecordConsumeEventReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *RecordConsumeEventReq) GetRichValue() uint32 {
	if m != nil {
		return m.RichValue
	}
	return 0
}

func (m *RecordConsumeEventReq) GetConsumeType() uint32 {
	if m != nil {
		return m.ConsumeType
	}
	return 0
}

func (m *RecordConsumeEventReq) GetOrderInfo() string {
	if m != nil {
		return m.OrderInfo
	}
	return ""
}

type RecordConsumeEventResp struct {
}

func (m *RecordConsumeEventResp) Reset()         { *m = RecordConsumeEventResp{} }
func (m *RecordConsumeEventResp) String() string { return proto.CompactTextString(m) }
func (*RecordConsumeEventResp) ProtoMessage()    {}
func (*RecordConsumeEventResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNumericsvr, []int{14}
}

type NotifyGuildChangeReq struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *NotifyGuildChangeReq) Reset()                    { *m = NotifyGuildChangeReq{} }
func (m *NotifyGuildChangeReq) String() string            { return proto.CompactTextString(m) }
func (*NotifyGuildChangeReq) ProtoMessage()               {}
func (*NotifyGuildChangeReq) Descriptor() ([]byte, []int) { return fileDescriptorNumericsvr, []int{15} }

func (m *NotifyGuildChangeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NotifyGuildChangeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildGiftTotalValueReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *GetGuildGiftTotalValueReq) Reset()         { *m = GetGuildGiftTotalValueReq{} }
func (m *GetGuildGiftTotalValueReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildGiftTotalValueReq) ProtoMessage()    {}
func (*GetGuildGiftTotalValueReq) Descriptor() ([]byte, []int) {
	return fileDescriptorNumericsvr, []int{16}
}

func (m *GetGuildGiftTotalValueReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildGiftTotalValueResp struct {
	GiftValue   uint32 `protobuf:"varint,1,req,name=gift_value,json=giftValue" json:"gift_value"`
	RichValue64 int64  `protobuf:"varint,2,opt,name=rich_value64,json=richValue64" json:"rich_value64"`
	GiftValue64 int64  `protobuf:"varint,3,opt,name=gift_value64,json=giftValue64" json:"gift_value64"`
}

func (m *GetGuildGiftTotalValueResp) Reset()         { *m = GetGuildGiftTotalValueResp{} }
func (m *GetGuildGiftTotalValueResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildGiftTotalValueResp) ProtoMessage()    {}
func (*GetGuildGiftTotalValueResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNumericsvr, []int{17}
}

func (m *GetGuildGiftTotalValueResp) GetGiftValue() uint32 {
	if m != nil {
		return m.GiftValue
	}
	return 0
}

func (m *GetGuildGiftTotalValueResp) GetRichValue64() int64 {
	if m != nil {
		return m.RichValue64
	}
	return 0
}

func (m *GetGuildGiftTotalValueResp) GetGiftValue64() int64 {
	if m != nil {
		return m.GiftValue64
	}
	return 0
}

type GetPersonalRankingReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetPersonalRankingReq) Reset()                    { *m = GetPersonalRankingReq{} }
func (m *GetPersonalRankingReq) String() string            { return proto.CompactTextString(m) }
func (*GetPersonalRankingReq) ProtoMessage()               {}
func (*GetPersonalRankingReq) Descriptor() ([]byte, []int) { return fileDescriptorNumericsvr, []int{18} }

func (m *GetPersonalRankingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPersonalRankingResp struct {
	WeekCharmRanking  uint32 `protobuf:"varint,1,req,name=week_charm_ranking,json=weekCharmRanking" json:"week_charm_ranking"`
	DayCharmRanking   uint32 `protobuf:"varint,2,req,name=day_charm_ranking,json=dayCharmRanking" json:"day_charm_ranking"`
	WeekRichRanking   uint32 `protobuf:"varint,3,req,name=week_rich_ranking,json=weekRichRanking" json:"week_rich_ranking"`
	DayRichRanking    uint32 `protobuf:"varint,4,req,name=day_rich_ranking,json=dayRichRanking" json:"day_rich_ranking"`
	MonthCharmRanking uint32 `protobuf:"varint,5,opt,name=month_charm_ranking,json=monthCharmRanking" json:"month_charm_ranking"`
	MonthRichRanking  uint32 `protobuf:"varint,6,opt,name=month_rich_ranking,json=monthRichRanking" json:"month_rich_ranking"`
}

func (m *GetPersonalRankingResp) Reset()         { *m = GetPersonalRankingResp{} }
func (m *GetPersonalRankingResp) String() string { return proto.CompactTextString(m) }
func (*GetPersonalRankingResp) ProtoMessage()    {}
func (*GetPersonalRankingResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNumericsvr, []int{19}
}

func (m *GetPersonalRankingResp) GetWeekCharmRanking() uint32 {
	if m != nil {
		return m.WeekCharmRanking
	}
	return 0
}

func (m *GetPersonalRankingResp) GetDayCharmRanking() uint32 {
	if m != nil {
		return m.DayCharmRanking
	}
	return 0
}

func (m *GetPersonalRankingResp) GetWeekRichRanking() uint32 {
	if m != nil {
		return m.WeekRichRanking
	}
	return 0
}

func (m *GetPersonalRankingResp) GetDayRichRanking() uint32 {
	if m != nil {
		return m.DayRichRanking
	}
	return 0
}

func (m *GetPersonalRankingResp) GetMonthCharmRanking() uint32 {
	if m != nil {
		return m.MonthCharmRanking
	}
	return 0
}

func (m *GetPersonalRankingResp) GetMonthRichRanking() uint32 {
	if m != nil {
		return m.MonthRichRanking
	}
	return 0
}

type GetRankListReq struct {
	Index    uint32 `protobuf:"varint,1,req,name=index" json:"index"`
	Count    uint32 `protobuf:"varint,2,req,name=count" json:"count"`
	RankType uint32 `protobuf:"varint,3,req,name=rank_type,json=rankType" json:"rank_type"`
	Uid      uint32 `protobuf:"varint,4,opt,name=uid" json:"uid"`
}

func (m *GetRankListReq) Reset()                    { *m = GetRankListReq{} }
func (m *GetRankListReq) String() string            { return proto.CompactTextString(m) }
func (*GetRankListReq) ProtoMessage()               {}
func (*GetRankListReq) Descriptor() ([]byte, []int) { return fileDescriptorNumericsvr, []int{20} }

func (m *GetRankListReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *GetRankListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetRankListReq) GetRankType() uint32 {
	if m != nil {
		return m.RankType
	}
	return 0
}

func (m *GetRankListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetRankListResp struct {
	RankList      []uint32 `protobuf:"varint,1,rep,name=rank_list,json=rankList" json:"rank_list,omitempty"`
	RankLast      uint32   `protobuf:"varint,2,opt,name=rank_last,json=rankLast" json:"rank_last"`
	RankNow       uint32   `protobuf:"varint,3,opt,name=rank_now,json=rankNow" json:"rank_now"`
	GapToRiseRank uint32   `protobuf:"varint,4,opt,name=gap_to_rise_rank,json=gapToRiseRank" json:"gap_to_rise_rank"`
}

func (m *GetRankListResp) Reset()                    { *m = GetRankListResp{} }
func (m *GetRankListResp) String() string            { return proto.CompactTextString(m) }
func (*GetRankListResp) ProtoMessage()               {}
func (*GetRankListResp) Descriptor() ([]byte, []int) { return fileDescriptorNumericsvr, []int{21} }

func (m *GetRankListResp) GetRankList() []uint32 {
	if m != nil {
		return m.RankList
	}
	return nil
}

func (m *GetRankListResp) GetRankLast() uint32 {
	if m != nil {
		return m.RankLast
	}
	return 0
}

func (m *GetRankListResp) GetRankNow() uint32 {
	if m != nil {
		return m.RankNow
	}
	return 0
}

func (m *GetRankListResp) GetGapToRiseRank() uint32 {
	if m != nil {
		return m.GapToRiseRank
	}
	return 0
}

type AddUserNumericReq struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	AddType    uint32 `protobuf:"varint,2,req,name=add_type,json=addType" json:"add_type"`
	RichValue  uint64 `protobuf:"varint,3,opt,name=rich_value,json=richValue" json:"rich_value"`
	CharmValue uint64 `protobuf:"varint,4,opt,name=charm_value,json=charmValue" json:"charm_value"`
}

func (m *AddUserNumericReq) Reset()                    { *m = AddUserNumericReq{} }
func (m *AddUserNumericReq) String() string            { return proto.CompactTextString(m) }
func (*AddUserNumericReq) ProtoMessage()               {}
func (*AddUserNumericReq) Descriptor() ([]byte, []int) { return fileDescriptorNumericsvr, []int{22} }

func (m *AddUserNumericReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserNumericReq) GetAddType() uint32 {
	if m != nil {
		return m.AddType
	}
	return 0
}

func (m *AddUserNumericReq) GetRichValue() uint64 {
	if m != nil {
		return m.RichValue
	}
	return 0
}

func (m *AddUserNumericReq) GetCharmValue() uint64 {
	if m != nil {
		return m.CharmValue
	}
	return 0
}

type AddUserNumericResp struct {
	FinalRichValue  uint64 `protobuf:"varint,1,req,name=final_rich_value,json=finalRichValue" json:"final_rich_value"`
	FinalCharmValue uint64 `protobuf:"varint,2,req,name=final_charm_value,json=finalCharmValue" json:"final_charm_value"`
}

func (m *AddUserNumericResp) Reset()                    { *m = AddUserNumericResp{} }
func (m *AddUserNumericResp) String() string            { return proto.CompactTextString(m) }
func (*AddUserNumericResp) ProtoMessage()               {}
func (*AddUserNumericResp) Descriptor() ([]byte, []int) { return fileDescriptorNumericsvr, []int{23} }

func (m *AddUserNumericResp) GetFinalRichValue() uint64 {
	if m != nil {
		return m.FinalRichValue
	}
	return 0
}

func (m *AddUserNumericResp) GetFinalCharmValue() uint64 {
	if m != nil {
		return m.FinalCharmValue
	}
	return 0
}

func init() {
	proto.RegisterType((*GetGuildNumericListReq)(nil), "numericsvr.GetGuildNumericListReq")
	proto.RegisterType((*GuildMemerNumeric)(nil), "numericsvr.GuildMemerNumeric")
	proto.RegisterType((*GetGuildNumericListResp)(nil), "numericsvr.GetGuildNumericListResp")
	proto.RegisterType((*GetPersonalNumericReq)(nil), "numericsvr.GetPersonalNumericReq")
	proto.RegisterType((*GetPersonalNumericResp)(nil), "numericsvr.GetPersonalNumericResp")
	proto.RegisterType((*BatchGetPersonalNumericReq)(nil), "numericsvr.BatchGetPersonalNumericReq")
	proto.RegisterType((*PersonalNumeric)(nil), "numericsvr.PersonalNumeric")
	proto.RegisterType((*BatchGetPersonalNumericResp)(nil), "numericsvr.BatchGetPersonalNumericResp")
	proto.RegisterType((*RecordSendGiftEventReq)(nil), "numericsvr.RecordSendGiftEventReq")
	proto.RegisterType((*RecordSendGiftEventResp)(nil), "numericsvr.RecordSendGiftEventResp")
	proto.RegisterType((*UserGiftEventInfo)(nil), "numericsvr.UserGiftEventInfo")
	proto.RegisterType((*BatchRecordSendGiftEventReq)(nil), "numericsvr.BatchRecordSendGiftEventReq")
	proto.RegisterType((*BatchRecordSendGiftEventResp)(nil), "numericsvr.BatchRecordSendGiftEventResp")
	proto.RegisterType((*RecordConsumeEventReq)(nil), "numericsvr.RecordConsumeEventReq")
	proto.RegisterType((*RecordConsumeEventResp)(nil), "numericsvr.RecordConsumeEventResp")
	proto.RegisterType((*NotifyGuildChangeReq)(nil), "numericsvr.NotifyGuildChangeReq")
	proto.RegisterType((*GetGuildGiftTotalValueReq)(nil), "numericsvr.GetGuildGiftTotalValueReq")
	proto.RegisterType((*GetGuildGiftTotalValueResp)(nil), "numericsvr.GetGuildGiftTotalValueResp")
	proto.RegisterType((*GetPersonalRankingReq)(nil), "numericsvr.GetPersonalRankingReq")
	proto.RegisterType((*GetPersonalRankingResp)(nil), "numericsvr.GetPersonalRankingResp")
	proto.RegisterType((*GetRankListReq)(nil), "numericsvr.GetRankListReq")
	proto.RegisterType((*GetRankListResp)(nil), "numericsvr.GetRankListResp")
	proto.RegisterType((*AddUserNumericReq)(nil), "numericsvr.AddUserNumericReq")
	proto.RegisterType((*AddUserNumericResp)(nil), "numericsvr.AddUserNumericResp")
	proto.RegisterEnum("numericsvr.CONSUME_TYPE", CONSUME_TYPE_name, CONSUME_TYPE_value)
	proto.RegisterEnum("numericsvr.AddUserNumericType", AddUserNumericType_name, AddUserNumericType_value)
	proto.RegisterEnum("numericsvr.GetRankListReq_RANK_TYPE", GetRankListReq_RANK_TYPE_name, GetRankListReq_RANK_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for NumericSvr service

type NumericSvrClient interface {
	GetGuildConsumeList(ctx context.Context, in *GetGuildNumericListReq, opts ...grpc.CallOption) (*GetGuildNumericListResp, error)
	GetGuildCharmList(ctx context.Context, in *GetGuildNumericListReq, opts ...grpc.CallOption) (*GetGuildNumericListResp, error)
	GetPersonalNumeric(ctx context.Context, in *GetPersonalNumericReq, opts ...grpc.CallOption) (*GetPersonalNumericResp, error)
	// 由送礼物 导致更新Numeric
	RecordSendGiftEvent(ctx context.Context, in *RecordSendGiftEventReq, opts ...grpc.CallOption) (*RecordSendGiftEventResp, error)
	NotifyGuildQuit(ctx context.Context, in *NotifyGuildChangeReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	NotifyGuildJoin(ctx context.Context, in *NotifyGuildChangeReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildGiftTotalValue(ctx context.Context, in *GetGuildGiftTotalValueReq, opts ...grpc.CallOption) (*GetGuildGiftTotalValueResp, error)
	// 由现金消费 导致更新Numeric
	RecordConsumeEvent(ctx context.Context, in *RecordConsumeEventReq, opts ...grpc.CallOption) (*RecordConsumeEventResp, error)
	GetPersonalRanking(ctx context.Context, in *GetPersonalRankingReq, opts ...grpc.CallOption) (*GetPersonalRankingResp, error)
	GetRankList(ctx context.Context, in *GetRankListReq, opts ...grpc.CallOption) (*GetRankListResp, error)
	BatchGetPersonalNumeric(ctx context.Context, in *BatchGetPersonalNumericReq, opts ...grpc.CallOption) (*BatchGetPersonalNumericResp, error)
	// 由批量送礼物 导致更新Numeric
	BatchRecordSendGiftEvent(ctx context.Context, in *BatchRecordSendGiftEventReq, opts ...grpc.CallOption) (*BatchRecordSendGiftEventResp, error)
	AddUserNumeric(ctx context.Context, in *AddUserNumericReq, opts ...grpc.CallOption) (*AddUserNumericResp, error)
	GetPersonalRankingFromLocalCache(ctx context.Context, in *GetPersonalRankingReq, opts ...grpc.CallOption) (*GetPersonalRankingResp, error)
}

type numericSvrClient struct {
	cc *grpc.ClientConn
}

func NewNumericSvrClient(cc *grpc.ClientConn) NumericSvrClient {
	return &numericSvrClient{cc}
}

func (c *numericSvrClient) GetGuildConsumeList(ctx context.Context, in *GetGuildNumericListReq, opts ...grpc.CallOption) (*GetGuildNumericListResp, error) {
	out := new(GetGuildNumericListResp)
	err := grpc.Invoke(ctx, "/numericsvr.NumericSvr/GetGuildConsumeList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericSvrClient) GetGuildCharmList(ctx context.Context, in *GetGuildNumericListReq, opts ...grpc.CallOption) (*GetGuildNumericListResp, error) {
	out := new(GetGuildNumericListResp)
	err := grpc.Invoke(ctx, "/numericsvr.NumericSvr/GetGuildCharmList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericSvrClient) GetPersonalNumeric(ctx context.Context, in *GetPersonalNumericReq, opts ...grpc.CallOption) (*GetPersonalNumericResp, error) {
	out := new(GetPersonalNumericResp)
	err := grpc.Invoke(ctx, "/numericsvr.NumericSvr/GetPersonalNumeric", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericSvrClient) RecordSendGiftEvent(ctx context.Context, in *RecordSendGiftEventReq, opts ...grpc.CallOption) (*RecordSendGiftEventResp, error) {
	out := new(RecordSendGiftEventResp)
	err := grpc.Invoke(ctx, "/numericsvr.NumericSvr/RecordSendGiftEvent", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericSvrClient) NotifyGuildQuit(ctx context.Context, in *NotifyGuildChangeReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/numericsvr.NumericSvr/NotifyGuildQuit", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericSvrClient) NotifyGuildJoin(ctx context.Context, in *NotifyGuildChangeReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/numericsvr.NumericSvr/NotifyGuildJoin", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericSvrClient) GetGuildGiftTotalValue(ctx context.Context, in *GetGuildGiftTotalValueReq, opts ...grpc.CallOption) (*GetGuildGiftTotalValueResp, error) {
	out := new(GetGuildGiftTotalValueResp)
	err := grpc.Invoke(ctx, "/numericsvr.NumericSvr/GetGuildGiftTotalValue", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericSvrClient) RecordConsumeEvent(ctx context.Context, in *RecordConsumeEventReq, opts ...grpc.CallOption) (*RecordConsumeEventResp, error) {
	out := new(RecordConsumeEventResp)
	err := grpc.Invoke(ctx, "/numericsvr.NumericSvr/RecordConsumeEvent", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericSvrClient) GetPersonalRanking(ctx context.Context, in *GetPersonalRankingReq, opts ...grpc.CallOption) (*GetPersonalRankingResp, error) {
	out := new(GetPersonalRankingResp)
	err := grpc.Invoke(ctx, "/numericsvr.NumericSvr/GetPersonalRanking", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericSvrClient) GetRankList(ctx context.Context, in *GetRankListReq, opts ...grpc.CallOption) (*GetRankListResp, error) {
	out := new(GetRankListResp)
	err := grpc.Invoke(ctx, "/numericsvr.NumericSvr/GetRankList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericSvrClient) BatchGetPersonalNumeric(ctx context.Context, in *BatchGetPersonalNumericReq, opts ...grpc.CallOption) (*BatchGetPersonalNumericResp, error) {
	out := new(BatchGetPersonalNumericResp)
	err := grpc.Invoke(ctx, "/numericsvr.NumericSvr/BatchGetPersonalNumeric", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericSvrClient) BatchRecordSendGiftEvent(ctx context.Context, in *BatchRecordSendGiftEventReq, opts ...grpc.CallOption) (*BatchRecordSendGiftEventResp, error) {
	out := new(BatchRecordSendGiftEventResp)
	err := grpc.Invoke(ctx, "/numericsvr.NumericSvr/BatchRecordSendGiftEvent", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericSvrClient) AddUserNumeric(ctx context.Context, in *AddUserNumericReq, opts ...grpc.CallOption) (*AddUserNumericResp, error) {
	out := new(AddUserNumericResp)
	err := grpc.Invoke(ctx, "/numericsvr.NumericSvr/AddUserNumeric", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericSvrClient) GetPersonalRankingFromLocalCache(ctx context.Context, in *GetPersonalRankingReq, opts ...grpc.CallOption) (*GetPersonalRankingResp, error) {
	out := new(GetPersonalRankingResp)
	err := grpc.Invoke(ctx, "/numericsvr.NumericSvr/GetPersonalRankingFromLocalCache", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for NumericSvr service

type NumericSvrServer interface {
	GetGuildConsumeList(context.Context, *GetGuildNumericListReq) (*GetGuildNumericListResp, error)
	GetGuildCharmList(context.Context, *GetGuildNumericListReq) (*GetGuildNumericListResp, error)
	GetPersonalNumeric(context.Context, *GetPersonalNumericReq) (*GetPersonalNumericResp, error)
	// 由送礼物 导致更新Numeric
	RecordSendGiftEvent(context.Context, *RecordSendGiftEventReq) (*RecordSendGiftEventResp, error)
	NotifyGuildQuit(context.Context, *NotifyGuildChangeReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	NotifyGuildJoin(context.Context, *NotifyGuildChangeReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildGiftTotalValue(context.Context, *GetGuildGiftTotalValueReq) (*GetGuildGiftTotalValueResp, error)
	// 由现金消费 导致更新Numeric
	RecordConsumeEvent(context.Context, *RecordConsumeEventReq) (*RecordConsumeEventResp, error)
	GetPersonalRanking(context.Context, *GetPersonalRankingReq) (*GetPersonalRankingResp, error)
	GetRankList(context.Context, *GetRankListReq) (*GetRankListResp, error)
	BatchGetPersonalNumeric(context.Context, *BatchGetPersonalNumericReq) (*BatchGetPersonalNumericResp, error)
	// 由批量送礼物 导致更新Numeric
	BatchRecordSendGiftEvent(context.Context, *BatchRecordSendGiftEventReq) (*BatchRecordSendGiftEventResp, error)
	AddUserNumeric(context.Context, *AddUserNumericReq) (*AddUserNumericResp, error)
	GetPersonalRankingFromLocalCache(context.Context, *GetPersonalRankingReq) (*GetPersonalRankingResp, error)
}

func RegisterNumericSvrServer(s *grpc.Server, srv NumericSvrServer) {
	s.RegisterService(&_NumericSvr_serviceDesc, srv)
}

func _NumericSvr_GetGuildConsumeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildNumericListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericSvrServer).GetGuildConsumeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numericsvr.NumericSvr/GetGuildConsumeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericSvrServer).GetGuildConsumeList(ctx, req.(*GetGuildNumericListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericSvr_GetGuildCharmList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildNumericListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericSvrServer).GetGuildCharmList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numericsvr.NumericSvr/GetGuildCharmList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericSvrServer).GetGuildCharmList(ctx, req.(*GetGuildNumericListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericSvr_GetPersonalNumeric_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPersonalNumericReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericSvrServer).GetPersonalNumeric(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numericsvr.NumericSvr/GetPersonalNumeric",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericSvrServer).GetPersonalNumeric(ctx, req.(*GetPersonalNumericReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericSvr_RecordSendGiftEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordSendGiftEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericSvrServer).RecordSendGiftEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numericsvr.NumericSvr/RecordSendGiftEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericSvrServer).RecordSendGiftEvent(ctx, req.(*RecordSendGiftEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericSvr_NotifyGuildQuit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyGuildChangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericSvrServer).NotifyGuildQuit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numericsvr.NumericSvr/NotifyGuildQuit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericSvrServer).NotifyGuildQuit(ctx, req.(*NotifyGuildChangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericSvr_NotifyGuildJoin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyGuildChangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericSvrServer).NotifyGuildJoin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numericsvr.NumericSvr/NotifyGuildJoin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericSvrServer).NotifyGuildJoin(ctx, req.(*NotifyGuildChangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericSvr_GetGuildGiftTotalValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildGiftTotalValueReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericSvrServer).GetGuildGiftTotalValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numericsvr.NumericSvr/GetGuildGiftTotalValue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericSvrServer).GetGuildGiftTotalValue(ctx, req.(*GetGuildGiftTotalValueReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericSvr_RecordConsumeEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordConsumeEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericSvrServer).RecordConsumeEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numericsvr.NumericSvr/RecordConsumeEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericSvrServer).RecordConsumeEvent(ctx, req.(*RecordConsumeEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericSvr_GetPersonalRanking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPersonalRankingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericSvrServer).GetPersonalRanking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numericsvr.NumericSvr/GetPersonalRanking",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericSvrServer).GetPersonalRanking(ctx, req.(*GetPersonalRankingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericSvr_GetRankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericSvrServer).GetRankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numericsvr.NumericSvr/GetRankList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericSvrServer).GetRankList(ctx, req.(*GetRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericSvr_BatchGetPersonalNumeric_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetPersonalNumericReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericSvrServer).BatchGetPersonalNumeric(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numericsvr.NumericSvr/BatchGetPersonalNumeric",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericSvrServer).BatchGetPersonalNumeric(ctx, req.(*BatchGetPersonalNumericReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericSvr_BatchRecordSendGiftEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchRecordSendGiftEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericSvrServer).BatchRecordSendGiftEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numericsvr.NumericSvr/BatchRecordSendGiftEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericSvrServer).BatchRecordSendGiftEvent(ctx, req.(*BatchRecordSendGiftEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericSvr_AddUserNumeric_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserNumericReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericSvrServer).AddUserNumeric(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numericsvr.NumericSvr/AddUserNumeric",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericSvrServer).AddUserNumeric(ctx, req.(*AddUserNumericReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericSvr_GetPersonalRankingFromLocalCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPersonalRankingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericSvrServer).GetPersonalRankingFromLocalCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numericsvr.NumericSvr/GetPersonalRankingFromLocalCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericSvrServer).GetPersonalRankingFromLocalCache(ctx, req.(*GetPersonalRankingReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _NumericSvr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "numericsvr.NumericSvr",
	HandlerType: (*NumericSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGuildConsumeList",
			Handler:    _NumericSvr_GetGuildConsumeList_Handler,
		},
		{
			MethodName: "GetGuildCharmList",
			Handler:    _NumericSvr_GetGuildCharmList_Handler,
		},
		{
			MethodName: "GetPersonalNumeric",
			Handler:    _NumericSvr_GetPersonalNumeric_Handler,
		},
		{
			MethodName: "RecordSendGiftEvent",
			Handler:    _NumericSvr_RecordSendGiftEvent_Handler,
		},
		{
			MethodName: "NotifyGuildQuit",
			Handler:    _NumericSvr_NotifyGuildQuit_Handler,
		},
		{
			MethodName: "NotifyGuildJoin",
			Handler:    _NumericSvr_NotifyGuildJoin_Handler,
		},
		{
			MethodName: "GetGuildGiftTotalValue",
			Handler:    _NumericSvr_GetGuildGiftTotalValue_Handler,
		},
		{
			MethodName: "RecordConsumeEvent",
			Handler:    _NumericSvr_RecordConsumeEvent_Handler,
		},
		{
			MethodName: "GetPersonalRanking",
			Handler:    _NumericSvr_GetPersonalRanking_Handler,
		},
		{
			MethodName: "GetRankList",
			Handler:    _NumericSvr_GetRankList_Handler,
		},
		{
			MethodName: "BatchGetPersonalNumeric",
			Handler:    _NumericSvr_BatchGetPersonalNumeric_Handler,
		},
		{
			MethodName: "BatchRecordSendGiftEvent",
			Handler:    _NumericSvr_BatchRecordSendGiftEvent_Handler,
		},
		{
			MethodName: "AddUserNumeric",
			Handler:    _NumericSvr_AddUserNumeric_Handler,
		},
		{
			MethodName: "GetPersonalRankingFromLocalCache",
			Handler:    _NumericSvr_GetPersonalRankingFromLocalCache_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/numericsvr/numericsvr.proto",
}

func (m *GetGuildNumericListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildNumericListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.Index))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *GuildMemerNumeric) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildMemerNumeric) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.Numeric))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.Numeric64))
	return i, nil
}

func (m *GetGuildNumericListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildNumericListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.NumericList) > 0 {
		for _, msg := range m.NumericList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintNumericsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetPersonalNumericReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPersonalNumericReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetPersonalNumericResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPersonalNumericResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.ConsumeNumeric))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.CharmNumeric))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.ConsumeNumeric64))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.CharmNumeric64))
	return i, nil
}

func (m *BatchGetPersonalNumericReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetPersonalNumericReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintNumericsvr(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *PersonalNumeric) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PersonalNumeric) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.Charm))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.Rich))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.Charm64))
	dAtA[i] = 0x28
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.Rich64))
	return i, nil
}

func (m *BatchGetPersonalNumericResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetPersonalNumericResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.NumericList) > 0 {
		for _, msg := range m.NumericList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintNumericsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *RecordSendGiftEventReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordSendGiftEventReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.GiverUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.ReceiverUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.RichValue))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.GiverGuild))
	dAtA[i] = 0x28
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.ReceiverGuild))
	dAtA[i] = 0x30
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.CharmValue))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x40
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x48
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.ChannelGuild))
	dAtA[i] = 0x50
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.PriceType))
	dAtA[i] = 0x58
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.GiftId))
	return i, nil
}

func (m *RecordSendGiftEventResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordSendGiftEventResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LevelChanageUidList) > 0 {
		for _, num := range m.LevelChanageUidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintNumericsvr(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.RealCharm))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.GiverCurrAllRich))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.ReceiverCurrAllCharm))
	dAtA[i] = 0x28
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.RealRich))
	dAtA[i] = 0x30
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.BeforeRich))
	dAtA[i] = 0x38
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.BeforeRich64))
	dAtA[i] = 0x40
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.GiverCurrAllRich64))
	dAtA[i] = 0x48
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.ReceiverCurrAllCharm64))
	return i, nil
}

func (m *UserGiftEventInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserGiftEventInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.AddValue))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.FinalValue))
	dAtA[i] = 0x28
	i++
	if m.LevelChange {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x30
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.FinalValue64))
	return i, nil
}

func (m *BatchRecordSendGiftEventReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchRecordSendGiftEventReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GiverUserInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("giver_user_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintNumericsvr(dAtA, i, uint64(m.GiverUserInfo.Size()))
		n1, err := m.GiverUserInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if len(m.ReceiverUserInfoList) > 0 {
		for _, msg := range m.ReceiverUserInfoList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintNumericsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.ChannelGuild))
	dAtA[i] = 0x30
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.PriceType))
	dAtA[i] = 0x38
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.GiftId))
	return i, nil
}

func (m *BatchRecordSendGiftEventResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchRecordSendGiftEventResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GiverUserInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintNumericsvr(dAtA, i, uint64(m.GiverUserInfo.Size()))
		n2, err := m.GiverUserInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if len(m.ReceiverUserInfoList) > 0 {
		for _, msg := range m.ReceiverUserInfoList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintNumericsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *RecordConsumeEventReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordConsumeEventReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.RichValue))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.ConsumeType))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(len(m.OrderInfo)))
	i += copy(dAtA[i:], m.OrderInfo)
	return i, nil
}

func (m *RecordConsumeEventResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordConsumeEventResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *NotifyGuildChangeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyGuildChangeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GetGuildGiftTotalValueReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildGiftTotalValueReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GetGuildGiftTotalValueResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildGiftTotalValueResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.GiftValue))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.RichValue64))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.GiftValue64))
	return i, nil
}

func (m *GetPersonalRankingReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPersonalRankingReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetPersonalRankingResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPersonalRankingResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.WeekCharmRanking))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.DayCharmRanking))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.WeekRichRanking))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.DayRichRanking))
	dAtA[i] = 0x28
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.MonthCharmRanking))
	dAtA[i] = 0x30
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.MonthRichRanking))
	return i, nil
}

func (m *GetRankListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRankListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.Index))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.RankType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetRankListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRankListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RankList) > 0 {
		for _, num := range m.RankList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintNumericsvr(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.RankLast))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.RankNow))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.GapToRiseRank))
	return i, nil
}

func (m *AddUserNumericReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserNumericReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.AddType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.RichValue))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.CharmValue))
	return i, nil
}

func (m *AddUserNumericResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserNumericResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.FinalRichValue))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericsvr(dAtA, i, uint64(m.FinalCharmValue))
	return i, nil
}

func encodeFixed64Numericsvr(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Numericsvr(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintNumericsvr(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GetGuildNumericListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericsvr(uint64(m.GuildId))
	n += 1 + sovNumericsvr(uint64(m.Index))
	n += 1 + sovNumericsvr(uint64(m.Count))
	return n
}

func (m *GuildMemerNumeric) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericsvr(uint64(m.Uid))
	n += 1 + sovNumericsvr(uint64(m.Numeric))
	n += 1 + sovNumericsvr(uint64(m.Numeric64))
	return n
}

func (m *GetGuildNumericListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.NumericList) > 0 {
		for _, e := range m.NumericList {
			l = e.Size()
			n += 1 + l + sovNumericsvr(uint64(l))
		}
	}
	return n
}

func (m *GetPersonalNumericReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericsvr(uint64(m.Uid))
	return n
}

func (m *GetPersonalNumericResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericsvr(uint64(m.ConsumeNumeric))
	n += 1 + sovNumericsvr(uint64(m.CharmNumeric))
	n += 1 + sovNumericsvr(uint64(m.ConsumeNumeric64))
	n += 1 + sovNumericsvr(uint64(m.CharmNumeric64))
	return n
}

func (m *BatchGetPersonalNumericReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovNumericsvr(uint64(e))
		}
	}
	return n
}

func (m *PersonalNumeric) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericsvr(uint64(m.Uid))
	n += 1 + sovNumericsvr(uint64(m.Charm))
	n += 1 + sovNumericsvr(uint64(m.Rich))
	n += 1 + sovNumericsvr(uint64(m.Charm64))
	n += 1 + sovNumericsvr(uint64(m.Rich64))
	return n
}

func (m *BatchGetPersonalNumericResp) Size() (n int) {
	var l int
	_ = l
	if len(m.NumericList) > 0 {
		for _, e := range m.NumericList {
			l = e.Size()
			n += 1 + l + sovNumericsvr(uint64(l))
		}
	}
	return n
}

func (m *RecordSendGiftEventReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericsvr(uint64(m.GiverUid))
	n += 1 + sovNumericsvr(uint64(m.ReceiverUid))
	n += 1 + sovNumericsvr(uint64(m.RichValue))
	n += 1 + sovNumericsvr(uint64(m.GiverGuild))
	n += 1 + sovNumericsvr(uint64(m.ReceiverGuild))
	n += 1 + sovNumericsvr(uint64(m.CharmValue))
	l = len(m.OrderId)
	n += 1 + l + sovNumericsvr(uint64(l))
	n += 1 + sovNumericsvr(uint64(m.ChannelId))
	n += 1 + sovNumericsvr(uint64(m.ChannelGuild))
	n += 1 + sovNumericsvr(uint64(m.PriceType))
	n += 1 + sovNumericsvr(uint64(m.GiftId))
	return n
}

func (m *RecordSendGiftEventResp) Size() (n int) {
	var l int
	_ = l
	if len(m.LevelChanageUidList) > 0 {
		for _, e := range m.LevelChanageUidList {
			n += 1 + sovNumericsvr(uint64(e))
		}
	}
	n += 1 + sovNumericsvr(uint64(m.RealCharm))
	n += 1 + sovNumericsvr(uint64(m.GiverCurrAllRich))
	n += 1 + sovNumericsvr(uint64(m.ReceiverCurrAllCharm))
	n += 1 + sovNumericsvr(uint64(m.RealRich))
	n += 1 + sovNumericsvr(uint64(m.BeforeRich))
	n += 1 + sovNumericsvr(uint64(m.BeforeRich64))
	n += 1 + sovNumericsvr(uint64(m.GiverCurrAllRich64))
	n += 1 + sovNumericsvr(uint64(m.ReceiverCurrAllCharm64))
	return n
}

func (m *UserGiftEventInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericsvr(uint64(m.Uid))
	n += 1 + sovNumericsvr(uint64(m.GuildId))
	n += 1 + sovNumericsvr(uint64(m.AddValue))
	n += 1 + sovNumericsvr(uint64(m.FinalValue))
	n += 2
	n += 1 + sovNumericsvr(uint64(m.FinalValue64))
	return n
}

func (m *BatchRecordSendGiftEventReq) Size() (n int) {
	var l int
	_ = l
	if m.GiverUserInfo != nil {
		l = m.GiverUserInfo.Size()
		n += 1 + l + sovNumericsvr(uint64(l))
	}
	if len(m.ReceiverUserInfoList) > 0 {
		for _, e := range m.ReceiverUserInfoList {
			l = e.Size()
			n += 1 + l + sovNumericsvr(uint64(l))
		}
	}
	l = len(m.OrderId)
	n += 1 + l + sovNumericsvr(uint64(l))
	n += 1 + sovNumericsvr(uint64(m.ChannelId))
	n += 1 + sovNumericsvr(uint64(m.ChannelGuild))
	n += 1 + sovNumericsvr(uint64(m.PriceType))
	n += 1 + sovNumericsvr(uint64(m.GiftId))
	return n
}

func (m *BatchRecordSendGiftEventResp) Size() (n int) {
	var l int
	_ = l
	if m.GiverUserInfo != nil {
		l = m.GiverUserInfo.Size()
		n += 1 + l + sovNumericsvr(uint64(l))
	}
	if len(m.ReceiverUserInfoList) > 0 {
		for _, e := range m.ReceiverUserInfoList {
			l = e.Size()
			n += 1 + l + sovNumericsvr(uint64(l))
		}
	}
	return n
}

func (m *RecordConsumeEventReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericsvr(uint64(m.Uid))
	n += 1 + sovNumericsvr(uint64(m.GuildId))
	n += 1 + sovNumericsvr(uint64(m.RichValue))
	n += 1 + sovNumericsvr(uint64(m.ConsumeType))
	l = len(m.OrderInfo)
	n += 1 + l + sovNumericsvr(uint64(l))
	return n
}

func (m *RecordConsumeEventResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *NotifyGuildChangeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericsvr(uint64(m.Uid))
	n += 1 + sovNumericsvr(uint64(m.GuildId))
	return n
}

func (m *GetGuildGiftTotalValueReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericsvr(uint64(m.GuildId))
	return n
}

func (m *GetGuildGiftTotalValueResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericsvr(uint64(m.GiftValue))
	n += 1 + sovNumericsvr(uint64(m.RichValue64))
	n += 1 + sovNumericsvr(uint64(m.GiftValue64))
	return n
}

func (m *GetPersonalRankingReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericsvr(uint64(m.Uid))
	return n
}

func (m *GetPersonalRankingResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericsvr(uint64(m.WeekCharmRanking))
	n += 1 + sovNumericsvr(uint64(m.DayCharmRanking))
	n += 1 + sovNumericsvr(uint64(m.WeekRichRanking))
	n += 1 + sovNumericsvr(uint64(m.DayRichRanking))
	n += 1 + sovNumericsvr(uint64(m.MonthCharmRanking))
	n += 1 + sovNumericsvr(uint64(m.MonthRichRanking))
	return n
}

func (m *GetRankListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericsvr(uint64(m.Index))
	n += 1 + sovNumericsvr(uint64(m.Count))
	n += 1 + sovNumericsvr(uint64(m.RankType))
	n += 1 + sovNumericsvr(uint64(m.Uid))
	return n
}

func (m *GetRankListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RankList) > 0 {
		for _, e := range m.RankList {
			n += 1 + sovNumericsvr(uint64(e))
		}
	}
	n += 1 + sovNumericsvr(uint64(m.RankLast))
	n += 1 + sovNumericsvr(uint64(m.RankNow))
	n += 1 + sovNumericsvr(uint64(m.GapToRiseRank))
	return n
}

func (m *AddUserNumericReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericsvr(uint64(m.Uid))
	n += 1 + sovNumericsvr(uint64(m.AddType))
	n += 1 + sovNumericsvr(uint64(m.RichValue))
	n += 1 + sovNumericsvr(uint64(m.CharmValue))
	return n
}

func (m *AddUserNumericResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericsvr(uint64(m.FinalRichValue))
	n += 1 + sovNumericsvr(uint64(m.FinalCharmValue))
	return n
}

func sovNumericsvr(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozNumericsvr(x uint64) (n int) {
	return sovNumericsvr(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *GetGuildNumericListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildNumericListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildNumericListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("index")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildMemerNumeric) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildMemerNumeric: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildMemerNumeric: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Numeric", wireType)
			}
			m.Numeric = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Numeric |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Numeric64", wireType)
			}
			m.Numeric64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Numeric64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("numeric")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildNumericListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildNumericListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildNumericListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NumericList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNumericsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NumericList = append(m.NumericList, &GuildMemerNumeric{})
			if err := m.NumericList[len(m.NumericList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPersonalNumericReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPersonalNumericReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPersonalNumericReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPersonalNumericResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPersonalNumericResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPersonalNumericResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConsumeNumeric", wireType)
			}
			m.ConsumeNumeric = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConsumeNumeric |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CharmNumeric", wireType)
			}
			m.CharmNumeric = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CharmNumeric |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConsumeNumeric64", wireType)
			}
			m.ConsumeNumeric64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConsumeNumeric64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CharmNumeric64", wireType)
			}
			m.CharmNumeric64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CharmNumeric64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("consume_numeric")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("charm_numeric")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetPersonalNumericReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetPersonalNumericReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetPersonalNumericReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNumericsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNumericsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthNumericsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowNumericsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PersonalNumeric) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PersonalNumeric: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PersonalNumeric: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Charm", wireType)
			}
			m.Charm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Charm |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rich", wireType)
			}
			m.Rich = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rich |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Charm64", wireType)
			}
			m.Charm64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Charm64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rich64", wireType)
			}
			m.Rich64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rich64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("charm")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rich")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetPersonalNumericResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetPersonalNumericResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetPersonalNumericResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NumericList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNumericsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NumericList = append(m.NumericList, &PersonalNumeric{})
			if err := m.NumericList[len(m.NumericList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordSendGiftEventReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordSendGiftEventReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordSendGiftEventReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiverUid", wireType)
			}
			m.GiverUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiverUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReceiverUid", wireType)
			}
			m.ReceiverUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReceiverUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RichValue", wireType)
			}
			m.RichValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RichValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiverGuild", wireType)
			}
			m.GiverGuild = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiverGuild |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReceiverGuild", wireType)
			}
			m.ReceiverGuild = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReceiverGuild |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CharmValue", wireType)
			}
			m.CharmValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CharmValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNumericsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelGuild", wireType)
			}
			m.ChannelGuild = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelGuild |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PriceType", wireType)
			}
			m.PriceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PriceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftId", wireType)
			}
			m.GiftId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("giver_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("receiver_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rich_value")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("giver_guild")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("receiver_guild")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("charm_value")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordSendGiftEventResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordSendGiftEventResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordSendGiftEventResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNumericsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.LevelChanageUidList = append(m.LevelChanageUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNumericsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthNumericsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowNumericsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.LevelChanageUidList = append(m.LevelChanageUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelChanageUidList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RealCharm", wireType)
			}
			m.RealCharm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RealCharm |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiverCurrAllRich", wireType)
			}
			m.GiverCurrAllRich = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiverCurrAllRich |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReceiverCurrAllCharm", wireType)
			}
			m.ReceiverCurrAllCharm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReceiverCurrAllCharm |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RealRich", wireType)
			}
			m.RealRich = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RealRich |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeforeRich", wireType)
			}
			m.BeforeRich = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeforeRich |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeforeRich64", wireType)
			}
			m.BeforeRich64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeforeRich64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiverCurrAllRich64", wireType)
			}
			m.GiverCurrAllRich64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiverCurrAllRich64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReceiverCurrAllCharm64", wireType)
			}
			m.ReceiverCurrAllCharm64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReceiverCurrAllCharm64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserGiftEventInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserGiftEventInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserGiftEventInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AddValue", wireType)
			}
			m.AddValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AddValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinalValue", wireType)
			}
			m.FinalValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinalValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelChange", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.LevelChange = bool(v != 0)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinalValue64", wireType)
			}
			m.FinalValue64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinalValue64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchRecordSendGiftEventReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchRecordSendGiftEventReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchRecordSendGiftEventReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiverUserInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNumericsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GiverUserInfo == nil {
				m.GiverUserInfo = &UserGiftEventInfo{}
			}
			if err := m.GiverUserInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReceiverUserInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNumericsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReceiverUserInfoList = append(m.ReceiverUserInfoList, &UserGiftEventInfo{})
			if err := m.ReceiverUserInfoList[len(m.ReceiverUserInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNumericsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelGuild", wireType)
			}
			m.ChannelGuild = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelGuild |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PriceType", wireType)
			}
			m.PriceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PriceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftId", wireType)
			}
			m.GiftId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("giver_user_info")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchRecordSendGiftEventResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchRecordSendGiftEventResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchRecordSendGiftEventResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiverUserInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNumericsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GiverUserInfo == nil {
				m.GiverUserInfo = &UserGiftEventInfo{}
			}
			if err := m.GiverUserInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReceiverUserInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNumericsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReceiverUserInfoList = append(m.ReceiverUserInfoList, &UserGiftEventInfo{})
			if err := m.ReceiverUserInfoList[len(m.ReceiverUserInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordConsumeEventReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordConsumeEventReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordConsumeEventReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RichValue", wireType)
			}
			m.RichValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RichValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConsumeType", wireType)
			}
			m.ConsumeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConsumeType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNumericsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rich_value")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("consume_type")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordConsumeEventResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordConsumeEventResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordConsumeEventResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyGuildChangeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyGuildChangeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyGuildChangeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildGiftTotalValueReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildGiftTotalValueReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildGiftTotalValueReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildGiftTotalValueResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildGiftTotalValueResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildGiftTotalValueResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftValue", wireType)
			}
			m.GiftValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RichValue64", wireType)
			}
			m.RichValue64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RichValue64 |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftValue64", wireType)
			}
			m.GiftValue64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftValue64 |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_value")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPersonalRankingReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPersonalRankingReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPersonalRankingReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPersonalRankingResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPersonalRankingResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPersonalRankingResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WeekCharmRanking", wireType)
			}
			m.WeekCharmRanking = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.WeekCharmRanking |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DayCharmRanking", wireType)
			}
			m.DayCharmRanking = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DayCharmRanking |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WeekRichRanking", wireType)
			}
			m.WeekRichRanking = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.WeekRichRanking |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DayRichRanking", wireType)
			}
			m.DayRichRanking = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DayRichRanking |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MonthCharmRanking", wireType)
			}
			m.MonthCharmRanking = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MonthCharmRanking |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MonthRichRanking", wireType)
			}
			m.MonthRichRanking = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MonthRichRanking |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("week_charm_ranking")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("day_charm_ranking")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("week_rich_ranking")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("day_rich_ranking")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRankListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRankListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRankListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankType", wireType)
			}
			m.RankType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RankType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("index")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rank_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRankListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRankListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRankListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNumericsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.RankList = append(m.RankList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNumericsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthNumericsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowNumericsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.RankList = append(m.RankList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankLast", wireType)
			}
			m.RankLast = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RankLast |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankNow", wireType)
			}
			m.RankNow = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RankNow |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GapToRiseRank", wireType)
			}
			m.GapToRiseRank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GapToRiseRank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserNumericReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserNumericReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserNumericReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AddType", wireType)
			}
			m.AddType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AddType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RichValue", wireType)
			}
			m.RichValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RichValue |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CharmValue", wireType)
			}
			m.CharmValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CharmValue |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("add_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserNumericResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserNumericResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserNumericResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinalRichValue", wireType)
			}
			m.FinalRichValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinalRichValue |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinalCharmValue", wireType)
			}
			m.FinalCharmValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinalCharmValue |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipNumericsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("final_rich_value")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("final_charm_value")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipNumericsvr(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowNumericsvr
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowNumericsvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthNumericsvr
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowNumericsvr
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipNumericsvr(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthNumericsvr = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowNumericsvr   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/numericsvr/numericsvr.proto", fileDescriptorNumericsvr) }

var fileDescriptorNumericsvr = []byte{
	// 2138 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x58, 0xcd, 0x6f, 0x1b, 0x5b,
	0x15, 0xef, 0xd8, 0x4e, 0x62, 0x1f, 0xdb, 0xb1, 0x73, 0xdb, 0xa6, 0x8e, 0xdb, 0x97, 0x4e, 0xa6,
	0x94, 0xa6, 0x0f, 0x39, 0x7d, 0xe4, 0x59, 0x7e, 0x92, 0x9f, 0x65, 0x48, 0x1c, 0x93, 0x9a, 0x26,
	0x69, 0x70, 0x9d, 0xa2, 0x82, 0xd0, 0x68, 0x9e, 0xe7, 0xc6, 0x19, 0x62, 0xcf, 0x4c, 0x67, 0xc6,
	0x6e, 0x23, 0xa1, 0x27, 0x16, 0x48, 0x7c, 0x2c, 0x10, 0x82, 0xcd, 0xdb, 0xb0, 0x00, 0xf5, 0x6f,
	0x40, 0x42, 0x2c, 0xd9, 0x3c, 0xb1, 0x42, 0xe8, 0x49, 0xec, 0x10, 0x2a, 0x9b, 0x0a, 0x09, 0xb1,
	0x66, 0x87, 0xee, 0xbd, 0x33, 0xe3, 0x3b, 0xe3, 0x99, 0xc6, 0x12, 0xef, 0x49, 0x2c, 0x22, 0xc5,
	0xe7, 0xfc, 0xce, 0xe7, 0x3d, 0xe7, 0xdc, 0x73, 0x07, 0x6e, 0xdb, 0x56, 0xff, 0x81, 0x3e, 0x1e,
	0x61, 0x4b, 0xeb, 0xdb, 0x13, 0x8b, 0xfb, 0x77, 0xcb, 0xb4, 0x0c, 0xc7, 0x40, 0x30, 0xa5, 0x94,
	0xbf, 0xd4, 0x37, 0x46, 0x23, 0x43, 0x7f, 0xe0, 0x0c, 0x27, 0xa6, 0xd6, 0x3f, 0x1f, 0xe2, 0x07,
	0xf6, 0xf9, 0x47, 0x63, 0x6d, 0xe8, 0x68, 0xba, 0x73, 0x61, 0x62, 0x26, 0x21, 0x3d, 0x87, 0xd5,
	0x7d, 0xec, 0xec, 0x8f, 0xb5, 0xa1, 0x7a, 0xc4, 0x64, 0x0f, 0x34, 0xdb, 0xe9, 0xe2, 0xe7, 0xe8,
	0x36, 0xa4, 0x07, 0x84, 0x2c, 0x6b, 0x6a, 0x49, 0x10, 0x13, 0x9b, 0xf9, 0xdd, 0xd4, 0xa7, 0x7f,
	0xbb, 0x7d, 0xa5, 0xbb, 0x44, 0xa9, 0x1d, 0x15, 0x95, 0x61, 0x41, 0xd3, 0x55, 0xfc, 0xb2, 0x94,
	0xe0, 0xb8, 0x8c, 0x44, 0x78, 0x7d, 0x63, 0xac, 0x3b, 0xa5, 0x24, 0xcf, 0xa3, 0x24, 0xc9, 0x80,
	0x15, 0x6a, 0xef, 0x10, 0x8f, 0xb0, 0xe5, 0x1a, 0x45, 0xab, 0x90, 0x1c, 0x87, 0x0c, 0x11, 0x02,
	0x5a, 0x87, 0x25, 0x37, 0xa6, 0x80, 0x19, 0x8f, 0x88, 0x24, 0xc8, 0xb8, 0xff, 0xd6, 0xaa, 0xa5,
	0xa4, 0x28, 0x6c, 0xa6, 0x5c, 0xc4, 0x94, 0x2c, 0x7d, 0x17, 0x6e, 0x44, 0xc6, 0x68, 0x9b, 0xe8,
	0xeb, 0x90, 0x73, 0x71, 0xf2, 0x50, 0xb3, 0x9d, 0x92, 0x20, 0x26, 0x37, 0xb3, 0xdb, 0xef, 0x6c,
	0x71, 0x99, 0x9d, 0xf1, 0xb5, 0x9b, 0xd5, 0xa7, 0x5a, 0xa4, 0x07, 0x70, 0x7d, 0x1f, 0x3b, 0xc7,
	0xd8, 0xb2, 0x0d, 0x5d, 0x19, 0x7a, 0x10, 0xfc, 0x3c, 0x2e, 0x22, 0xe9, 0x33, 0x81, 0xa6, 0x7c,
	0x46, 0xc2, 0x36, 0x51, 0x05, 0x0a, 0x7d, 0x43, 0xb7, 0xc7, 0x23, 0x2c, 0x7b, 0x41, 0xf3, 0xe2,
	0xcb, 0x2e, 0xd3, 0xcb, 0xd9, 0x7d, 0xc8, 0xf7, 0xcf, 0x14, 0x6b, 0x24, 0x47, 0x65, 0x28, 0x47,
	0x59, 0x1e, 0xf4, 0xab, 0xb0, 0x12, 0xd2, 0x1c, 0x4a, 0x57, 0x31, 0xa8, 0xbb, 0x56, 0xa5, 0xce,
	0xf0, 0xda, 0x6b, 0xd5, 0x52, 0x8a, 0x13, 0x58, 0xe6, 0xf5, 0xd7, 0xaa, 0xd2, 0x07, 0x50, 0xde,
	0x55, 0x9c, 0xfe, 0x59, 0x74, 0x32, 0xd6, 0x20, 0x3d, 0xd6, 0xd4, 0x69, 0x8e, 0xf3, 0xdd, 0xa5,
	0xb1, 0xa6, 0xd2, 0x04, 0xfe, 0x5a, 0x80, 0x42, 0x48, 0x22, 0xb6, 0x1a, 0x48, 0x59, 0x11, 0xb3,
	0xc1, 0x92, 0xa3, 0x24, 0x54, 0x82, 0x94, 0xa5, 0xf5, 0xcf, 0x02, 0x15, 0x47, 0x29, 0xa4, 0x86,
	0x28, 0x24, 0x14, 0x81, 0x47, 0x44, 0xb7, 0x60, 0x91, 0xe0, 0x6a, 0xd5, 0xd2, 0x02, 0xc7, 0x76,
	0x69, 0xd2, 0xf7, 0xe0, 0x66, 0x6c, 0x60, 0xb6, 0x89, 0x9a, 0x91, 0x15, 0x74, 0x93, 0xaf, 0xa0,
	0xb0, 0x58, 0xa0, 0x7e, 0x7e, 0x9f, 0x84, 0xd5, 0x2e, 0xee, 0x1b, 0x96, 0xfa, 0x04, 0xeb, 0xea,
	0xbe, 0x76, 0xea, 0xb4, 0x27, 0x58, 0xa7, 0x1d, 0xb8, 0x01, 0x99, 0x81, 0x36, 0xc1, 0x96, 0x1c,
	0xce, 0x45, 0x9a, 0x92, 0x4f, 0x34, 0x15, 0xdd, 0x83, 0x9c, 0x85, 0xfb, 0xd8, 0x47, 0xf1, 0x79,
	0xc9, 0x7a, 0x1c, 0x02, 0xbc, 0x03, 0x40, 0xe2, 0x91, 0x27, 0xca, 0x70, 0x8c, 0x03, 0x39, 0xca,
	0x10, 0xfa, 0x53, 0x42, 0x46, 0x77, 0x21, 0xcb, 0x0c, 0xd2, 0x16, 0x2f, 0xa5, 0x38, 0x14, 0x50,
	0x06, 0xed, 0x05, 0xf4, 0x15, 0x58, 0xf6, 0x8d, 0x32, 0xe4, 0x02, 0x87, 0xcc, 0x7b, 0x3c, 0x06,
	0xbe, 0x0b, 0x59, 0x56, 0x46, 0xcc, 0xf2, 0x22, 0xaf, 0x93, 0x32, 0x98, 0xe9, 0xdb, 0x90, 0x36,
	0x2c, 0x15, 0x5b, 0x64, 0xda, 0x2c, 0x89, 0x89, 0xcd, 0x8c, 0x77, 0x48, 0x94, 0xda, 0xa1, 0x01,
	0xf4, 0xcf, 0x14, 0x5d, 0xc7, 0x43, 0x02, 0x49, 0x8b, 0xc2, 0x34, 0x00, 0x97, 0xde, 0x51, 0xdd,
	0x8e, 0xa0, 0x20, 0xe6, 0x58, 0x86, 0xc3, 0xe5, 0x5c, 0x16, 0xf3, 0xeb, 0x0e, 0x80, 0x69, 0x69,
	0x7d, 0x2c, 0x93, 0x61, 0x58, 0x02, 0x5e, 0x1f, 0xa5, 0xf7, 0x2e, 0x4c, 0x8c, 0xde, 0x81, 0xa5,
	0x81, 0x76, 0xea, 0x10, 0x8b, 0x59, 0x0e, 0xb1, 0x48, 0x88, 0x1d, 0x55, 0xfa, 0x2c, 0x09, 0x37,
	0x22, 0xcf, 0xce, 0x36, 0xd1, 0xfb, 0xb0, 0x3a, 0xc4, 0x13, 0x3c, 0x94, 0x89, 0x55, 0x65, 0x80,
	0xe5, 0x50, 0xfd, 0x5f, 0xa5, 0xdc, 0x16, 0x63, 0x9e, 0xb0, 0x5e, 0xa0, 0xa7, 0x84, 0x15, 0x2a,
	0x43, 0x8b, 0x9c, 0x73, 0x8a, 0xd0, 0x5b, 0xb4, 0xd0, 0xdf, 0x87, 0xab, 0xec, 0x94, 0xfa, 0x63,
	0xcb, 0x92, 0x95, 0xe1, 0x50, 0x76, 0xeb, 0x7e, 0x8a, 0x2e, 0x52, 0x40, 0x6b, 0x6c, 0x59, 0x3b,
	0xc3, 0x61, 0x97, 0xf4, 0xc0, 0x87, 0x70, 0xc3, 0x3f, 0x33, 0x5f, 0x8e, 0x99, 0x49, 0x71, 0x82,
	0xd7, 0x3c, 0x90, 0x2b, 0xcb, 0x2c, 0x6e, 0x00, 0x35, 0xcf, 0xec, 0x2c, 0x70, 0xf0, 0x34, 0x21,
	0x53, 0xfd, 0x77, 0x21, 0xfb, 0x11, 0x3e, 0x35, 0x2c, 0xcc, 0x40, 0x8b, 0x1c, 0x08, 0x18, 0x83,
	0xc2, 0xee, 0x43, 0x9e, 0x83, 0xd5, 0xaa, 0xa5, 0x25, 0xae, 0xe3, 0x72, 0x53, 0x60, 0xad, 0x8a,
	0x3e, 0x80, 0xeb, 0x11, 0x61, 0xd6, 0xaa, 0xf4, 0xec, 0x3d, 0x11, 0x14, 0x0e, 0xb4, 0x56, 0x45,
	0x5f, 0x83, 0xb5, 0x98, 0x50, 0x6b, 0x55, 0x5a, 0x10, 0x9e, 0xf0, 0x6a, 0x54, 0xb0, 0xb5, 0xaa,
	0xf4, 0x4f, 0x01, 0x56, 0x4e, 0x6c, 0x6c, 0xf9, 0x07, 0xda, 0xd1, 0x4f, 0x8d, 0xd8, 0x99, 0xc4,
	0xdf, 0x93, 0xfc, 0x89, 0xf9, 0xf7, 0xe4, 0x06, 0x64, 0x14, 0x55, 0xf5, 0x3b, 0x8f, 0xcb, 0x9e,
	0xa2, 0xaa, 0x7e, 0xe3, 0x9d, 0x6a, 0xba, 0x32, 0x74, 0x41, 0xfc, 0x89, 0x00, 0x65, 0x30, 0xd8,
	0x3d, 0xc8, 0x4d, 0x6b, 0x6a, 0x80, 0xe9, 0x51, 0xa4, 0xbd, 0x6e, 0xf7, 0xeb, 0x69, 0x80, 0x49,
	0x9a, 0x39, 0x7d, 0xb5, 0x2a, 0x3d, 0x0f, 0x3f, 0xcd, 0x53, 0x8d, 0xb5, 0xaa, 0xf4, 0xef, 0x84,
	0x3b, 0xdf, 0x62, 0x86, 0x50, 0x1b, 0x0a, 0xee, 0x10, 0xb2, 0x49, 0x77, 0xea, 0xa7, 0x06, 0x4d,
	0x41, 0xe8, 0x92, 0x9c, 0x49, 0x57, 0x37, 0xcf, 0x66, 0x94, 0x8d, 0x2d, 0x9a, 0xbd, 0x1e, 0x57,
	0x7f, 0xbe, 0x26, 0xd6, 0x0f, 0x89, 0xd9, 0x3b, 0x77, 0x56, 0x9d, 0x5f, 0x98, 0x9e, 0x46, 0xda,
	0x2f, 0xfc, 0xd4, 0x48, 0x5e, 0x3e, 0x35, 0x52, 0x73, 0x4e, 0x8d, 0x85, 0x39, 0xa7, 0xc6, 0xe2,
	0xa5, 0x53, 0x63, 0x29, 0x62, 0x6a, 0xfc, 0x41, 0x80, 0x5b, 0xf1, 0x19, 0xb7, 0xcd, 0xe8, 0x94,
	0x0b, 0xff, 0x1f, 0x29, 0x97, 0xfe, 0x28, 0xc0, 0x75, 0xe6, 0x78, 0x8b, 0x6d, 0x0c, 0x7e, 0xa5,
	0xcc, 0xd7, 0x20, 0x11, 0x8b, 0xe4, 0x5c, 0x77, 0xd3, 0x3d, 0xc8, 0x79, 0x1b, 0x0c, 0xcd, 0x3d,
	0x7f, 0x39, 0x65, 0x5d, 0x0e, 0xcd, 0xfe, 0x1d, 0x00, 0xb7, 0x26, 0x48, 0xe2, 0x16, 0xb8, 0xaa,
	0xc8, 0xb0, 0xaa, 0xd0, 0x4f, 0x0d, 0xa9, 0xe4, 0x5d, 0xba, 0xc1, 0x20, 0x6c, 0x53, 0x7a, 0x0c,
	0xd7, 0x8e, 0x0c, 0x47, 0x3b, 0xbd, 0xa0, 0x07, 0xce, 0xfa, 0xe9, 0x7f, 0x89, 0x4e, 0x6a, 0xc0,
	0x9a, 0xb7, 0x7d, 0x92, 0xfc, 0xf6, 0x0c, 0xc7, 0x6d, 0xbe, 0x79, 0x96, 0x6c, 0xe9, 0x57, 0x02,
	0x94, 0xe3, 0xc4, 0x6d, 0x93, 0x04, 0x4b, 0x4b, 0x8d, 0xa5, 0x8e, 0xd7, 0x90, 0x21, 0x74, 0x3f,
	0x75, 0xd3, 0xfc, 0xd6, 0xaa, 0x74, 0x4a, 0x25, 0xfd, 0x25, 0xc1, 0xcb, 0x70, 0xad, 0x4a, 0x80,
	0x53, 0x6d, 0xee, 0x82, 0xe8, 0x03, 0x7d, 0x7d, 0xb5, 0x6a, 0x68, 0xe9, 0xed, 0x2a, 0xfa, 0xb9,
	0xa6, 0x0f, 0xde, 0xb6, 0xf4, 0xfe, 0x29, 0x11, 0x58, 0x7a, 0x7d, 0x09, 0xdb, 0x44, 0xdb, 0x80,
	0x5e, 0x60, 0x7c, 0xce, 0x26, 0xb4, 0x6c, 0x31, 0x4e, 0x40, 0x43, 0x91, 0xf0, 0xe9, 0x70, 0x76,
	0xe5, 0xd0, 0x7b, 0xb0, 0xa2, 0x2a, 0x17, 0x21, 0x11, 0x3e, 0xfb, 0x05, 0x55, 0xb9, 0x08, 0x4b,
	0x50, 0x2b, 0x34, 0x11, 0x9e, 0x04, 0x5f, 0x6a, 0x05, 0xc2, 0x26, 0x37, 0x88, 0x27, 0xb1, 0x05,
	0x45, 0x62, 0x23, 0x20, 0xc0, 0x17, 0xdd, 0xb2, 0xaa, 0x5c, 0xf0, 0xf8, 0x2a, 0x5c, 0x1d, 0x19,
	0xba, 0x73, 0x16, 0xf2, 0x8a, 0x9f, 0x25, 0x2b, 0x14, 0x10, 0xf0, 0x6b, 0x1b, 0x10, 0x93, 0x0a,
	0xd8, 0xe1, 0x07, 0x4b, 0x91, 0xf2, 0x39, 0x4b, 0xd2, 0xbf, 0x04, 0x58, 0xde, 0xc7, 0x0e, 0xf9,
	0xe9, 0x3d, 0xd6, 0xfc, 0xb7, 0x98, 0xf0, 0x96, 0xb7, 0x58, 0x62, 0xe6, 0x2d, 0x46, 0x6f, 0x76,
	0x45, 0x3f, 0x67, 0x2d, 0xc5, 0xa7, 0x23, 0x4d, 0xc8, 0xb4, 0x9f, 0xdc, 0x23, 0xe5, 0x67, 0x27,
	0x3d, 0xd2, 0x33, 0xc8, 0x74, 0x77, 0x8e, 0x1e, 0xc9, 0xbd, 0x67, 0xc7, 0x6d, 0x94, 0x87, 0xcc,
	0xb7, 0xdb, 0xed, 0x47, 0x72, 0xb7, 0xd3, 0x7a, 0x58, 0x14, 0x50, 0x0e, 0xd2, 0x7b, 0x3b, 0xcf,
	0xd8, 0xaf, 0x04, 0x5a, 0x06, 0xa0, 0xcc, 0xd6, 0xc3, 0x9d, 0xee, 0x61, 0x31, 0x49, 0xc0, 0x84,
	0xcb, 0x7e, 0xa6, 0x08, 0xfb, 0xf0, 0xf1, 0x51, 0xef, 0x21, 0x83, 0x2f, 0xa0, 0x02, 0x64, 0xd9,
	0x6f, 0x06, 0x58, 0x94, 0x7e, 0x23, 0x40, 0x21, 0x10, 0xaf, 0x6d, 0xa2, 0x9b, 0xae, 0xe3, 0xdc,
	0x46, 0x45, 0x5d, 0xa6, 0xd7, 0x82, 0x17, 0xd5, 0x50, 0xa1, 0xb3, 0x4e, 0x08, 0x46, 0x75, 0xa0,
	0xb0, 0x9b, 0x83, 0x42, 0x74, 0xe3, 0x45, 0xe0, 0x4e, 0x5e, 0x22, 0xd4, 0x23, 0xe3, 0x05, 0xaa,
	0x40, 0x71, 0xa0, 0x98, 0xb2, 0x63, 0xc8, 0x96, 0x66, 0x63, 0x7a, 0x32, 0x81, 0x1c, 0xe4, 0x07,
	0x8a, 0xd9, 0x33, 0xba, 0x9a, 0x8d, 0x89, 0x5f, 0xd2, 0x27, 0x02, 0xac, 0xec, 0xa8, 0x2a, 0x19,
	0x95, 0x97, 0xbf, 0x01, 0x89, 0x75, 0xb2, 0x12, 0xd0, 0xac, 0x07, 0x86, 0x86, 0xa2, 0xaa, 0xde,
	0x10, 0x0b, 0x8c, 0x44, 0xee, 0x5d, 0x1b, 0x58, 0xd7, 0xf9, 0xd5, 0x9a, 0x7f, 0xdb, 0x70, 0xab,
	0xb5, 0x34, 0x01, 0x14, 0xf6, 0xcc, 0x36, 0x49, 0x79, 0xb3, 0x15, 0x81, 0xb3, 0x43, 0xfc, 0xf4,
	0xdf, 0x77, 0x94, 0xdb, 0xf5, 0x8d, 0xbd, 0x07, 0x2b, 0x0c, 0xcf, 0x9b, 0x4c, 0x70, 0x02, 0x05,
	0xca, 0x6e, 0xf9, 0x76, 0xdf, 0xdd, 0x83, 0x5c, 0xeb, 0xf1, 0xd1, 0x93, 0x93, 0xc3, 0x36, 0xab,
	0x91, 0x12, 0x5c, 0x6b, 0x1f, 0x9d, 0x1c, 0xca, 0x3e, 0xb1, 0x27, 0xef, 0xef, 0x1c, 0xb6, 0x8b,
	0x02, 0x2a, 0xc3, 0x6a, 0x80, 0xf3, 0x70, 0xe7, 0xf8, 0xf8, 0x59, 0xab, 0xd3, 0x7b, 0x56, 0x4c,
	0xbc, 0x7b, 0x18, 0xf6, 0x9e, 0xe6, 0x67, 0x0d, 0xae, 0x53, 0x89, 0x9d, 0xbd, 0x3d, 0xaa, 0x5c,
	0xee, 0x1c, 0x3d, 0xdd, 0x39, 0xe8, 0xec, 0x15, 0xaf, 0xa0, 0x5b, 0x50, 0x0a, 0xb2, 0x0e, 0x3a,
	0x4f, 0xdb, 0x72, 0xef, 0xb1, 0xdc, 0xeb, 0x15, 0x85, 0xed, 0xdf, 0x15, 0x01, 0x5c, 0x45, 0x4f,
	0x26, 0x16, 0xfa, 0x18, 0xae, 0x7a, 0xd3, 0xd5, 0xbd, 0x09, 0x68, 0x01, 0x49, 0x81, 0x0f, 0x00,
	0x91, 0xdf, 0x47, 0xca, 0x77, 0x2e, 0xc5, 0xd8, 0xa6, 0xb4, 0xfe, 0xc3, 0x57, 0x6f, 0x92, 0xc2,
	0xcf, 0x5e, 0xbd, 0x49, 0x26, 0x06, 0xf5, 0x5f, 0xbe, 0x7a, 0x93, 0xcc, 0x57, 0x06, 0x62, 0xc3,
	0x1b, 0xf9, 0x4d, 0xf4, 0x03, 0x58, 0xf1, 0xed, 0x93, 0xcc, 0x7d, 0x01, 0xd6, 0x13, 0xf1, 0xd6,
	0x27, 0x80, 0x66, 0x5f, 0xb5, 0x68, 0x23, 0xa4, 0x7a, 0xf6, 0x39, 0x5f, 0x96, 0x2e, 0x83, 0xd8,
	0xa6, 0xb4, 0x46, 0x8c, 0x27, 0xa9, 0xf1, 0x31, 0x35, 0x9e, 0xae, 0x8c, 0xc5, 0xc6, 0x98, 0xd8,
	0xfd, 0xab, 0x00, 0x57, 0x23, 0x96, 0x9f, 0x60, 0xe0, 0xd1, 0xfb, 0x68, 0x30, 0xf0, 0x98, 0x0d,
	0x4a, 0x7a, 0x41, 0x6c, 0xa7, 0x88, 0xed, 0xe5, 0x71, 0x7d, 0x50, 0xb7, 0xea, 0x4e, 0x7d, 0x54,
	0x57, 0xea, 0x06, 0xf5, 0xe3, 0x3b, 0x9e, 0x1f, 0x62, 0x20, 0x1b, 0x62, 0xc5, 0x12, 0x1b, 0xa4,
	0x19, 0x9a, 0x62, 0xc5, 0x11, 0x1b, 0x8e, 0x62, 0x0d, 0xb0, 0xc3, 0x18, 0x23, 0xff, 0x27, 0x45,
	0x37, 0xc5, 0x8a, 0x22, 0x36, 0x68, 0x1f, 0x34, 0xc5, 0x8a, 0x21, 0x36, 0xbc, 0x1d, 0x94, 0x9c,
	0x67, 0x81, 0xdb, 0x1e, 0xbe, 0x35, 0xd6, 0x1c, 0x24, 0xf2, 0x0e, 0x47, 0xad, 0x16, 0xe5, 0x5b,
	0x5b, 0xfe, 0x37, 0xba, 0xad, 0x27, 0x8f, 0x76, 0xd9, 0x37, 0xba, 0xf6, 0xc8, 0x74, 0x2e, 0xe4,
	0xe3, 0x5d, 0xe9, 0x3e, 0x89, 0x65, 0x91, 0xc4, 0x92, 0x1a, 0xd4, 0x59, 0x26, 0x57, 0x43, 0x8e,
	0x7b, 0x79, 0x0d, 0x5a, 0xff, 0xa6, 0xa1, 0xe9, 0x9f, 0x8f, 0xf5, 0xa5, 0xb9, 0xac, 0xff, 0x58,
	0x98, 0x7e, 0x4b, 0x0c, 0xae, 0x2a, 0xe8, 0x6e, 0x54, 0xb5, 0xce, 0x6c, 0x43, 0xe5, 0x2f, 0xcf,
	0x03, 0xf3, 0xea, 0x3a, 0x1d, 0x5f, 0xd7, 0xbf, 0x15, 0x00, 0xcd, 0xae, 0x77, 0xc1, 0xc2, 0x8e,
	0xdc, 0x61, 0xcb, 0xd2, 0x65, 0x10, 0xdb, 0x94, 0xda, 0xc4, 0x7a, 0x86, 0x58, 0x4f, 0xb3, 0xe2,
	0x62, 0x65, 0xb5, 0x75, 0x79, 0x59, 0x05, 0x4a, 0x25, 0xd8, 0x7c, 0xde, 0x3e, 0x10, 0xd7, 0x7c,
	0xd3, 0x1d, 0x2b, 0xb6, 0xf9, 0xb8, 0xa5, 0x8a, 0x35, 0x1f, 0x44, 0x36, 0xdf, 0xc7, 0x90, 0xe5,
	0x2e, 0x53, 0x54, 0x0e, 0x69, 0xe3, 0xb6, 0x8a, 0xf2, 0xcd, 0x58, 0x9e, 0x6d, 0x4a, 0x35, 0x62,
	0x22, 0x4b, 0x4c, 0x2c, 0x8e, 0xeb, 0x7a, 0xdd, 0xa1, 0x66, 0x36, 0xa6, 0x49, 0xd0, 0xc5, 0x86,
	0x63, 0x98, 0x47, 0xac, 0x9b, 0xfc, 0x25, 0xa3, 0x89, 0x7e, 0x2e, 0xc0, 0x8d, 0x98, 0x0f, 0x6a,
	0x28, 0x50, 0x00, 0xf1, 0x9f, 0x13, 0xcb, 0xf7, 0xe6, 0xc2, 0xd9, 0xa6, 0x24, 0x11, 0x27, 0x73,
	0x5c, 0x1e, 0x56, 0x5c, 0x07, 0x3f, 0xf4, 0xfe, 0x9a, 0xe8, 0x3f, 0x02, 0x94, 0xe2, 0xde, 0x63,
	0x68, 0xd6, 0x52, 0xcc, 0x5c, 0xda, 0x9c, 0x0f, 0x68, 0x9b, 0xd2, 0x4f, 0x05, 0xe2, 0x54, 0x9e,
	0x38, 0x55, 0x0c, 0x4e, 0x27, 0x93, 0xba, 0xf8, 0xfd, 0x2f, 0x6e, 0x3e, 0x89, 0x15, 0x53, 0x6c,
	0x4c, 0x1f, 0xb0, 0x4d, 0xf4, 0x89, 0x00, 0xcb, 0xc1, 0xeb, 0x15, 0x05, 0x5e, 0x85, 0x33, 0x2b,
	0x4d, 0x79, 0xfd, 0x6d, 0x6c, 0xdb, 0x94, 0xf6, 0x49, 0x70, 0xcb, 0x6e, 0x59, 0x58, 0xf5, 0x97,
	0x34, 0xa4, 0xed, 0x69, 0x48, 0x96, 0xd8, 0x50, 0x54, 0x55, 0x9c, 0x6e, 0x1d, 0x4d, 0xb1, 0xf2,
	0x92, 0xd1, 0xb8, 0xcd, 0xa2, 0x29, 0xa2, 0x1f, 0x09, 0x20, 0xce, 0x56, 0xf7, 0x37, 0x2c, 0x63,
	0x74, 0x60, 0xf4, 0x95, 0x61, 0x4b, 0xe9, 0x9f, 0xe1, 0xcf, 0xb5, 0x5d, 0x0a, 0x51, 0xed, 0x52,
	0x5e, 0xfc, 0xc9, 0xab, 0x37, 0xc9, 0xbf, 0x5c, 0xec, 0x16, 0x3f, 0x7d, 0xbd, 0x2e, 0xfc, 0xf9,
	0xf5, 0xba, 0xf0, 0xf7, 0xd7, 0xeb, 0xc2, 0x2f, 0xfe, 0xb1, 0x7e, 0xe5, 0xbf, 0x01, 0x00, 0x00,
	0xff, 0xff, 0x66, 0x54, 0x96, 0xa8, 0x8e, 0x19, 0x00, 0x00,
}
