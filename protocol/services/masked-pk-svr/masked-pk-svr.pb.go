// Code generated by protoc-gen-go. DO NOT EDIT.
// source: masked-pk-svr/masked-pk-svr.proto

package masked_pk_svr // import "golang.52tt.com/protocol/services/masked-pk-svr"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type DivideType int32

const (
	DivideType_DIVIDE_TYPE_DEFAULT DivideType = 0
	DivideType_DIVIDE_TYPE_HALF    DivideType = 1
	DivideType_DIVIDE_TYPE_PERCENT DivideType = 2
)

var DivideType_name = map[int32]string{
	0: "DIVIDE_TYPE_DEFAULT",
	1: "DIVIDE_TYPE_HALF",
	2: "DIVIDE_TYPE_PERCENT",
}
var DivideType_value = map[string]int32{
	"DIVIDE_TYPE_DEFAULT": 0,
	"DIVIDE_TYPE_HALF":    1,
	"DIVIDE_TYPE_PERCENT": 2,
}

func (x DivideType) String() string {
	return proto.EnumName(DivideType_name, int32(x))
}
func (DivideType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{0}
}

type ChannelMaskedPKConf_ChipRole int32

const (
	ChannelMaskedPKConf_NoChip   ChannelMaskedPKConf_ChipRole = 0
	ChannelMaskedPKConf_HaveChip ChannelMaskedPKConf_ChipRole = 1
)

var ChannelMaskedPKConf_ChipRole_name = map[int32]string{
	0: "NoChip",
	1: "HaveChip",
}
var ChannelMaskedPKConf_ChipRole_value = map[string]int32{
	"NoChip":   0,
	"HaveChip": 1,
}

func (x ChannelMaskedPKConf_ChipRole) String() string {
	return proto.EnumName(ChannelMaskedPKConf_ChipRole_name, int32(x))
}
func (ChannelMaskedPKConf_ChipRole) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{2, 0}
}

type ChannelMaskedPKStatus_Status int32

const (
	ChannelMaskedPKStatus_NotParticipating ChannelMaskedPKStatus_Status = 0
	ChannelMaskedPKStatus_NotMatching      ChannelMaskedPKStatus_Status = 1
	ChannelMaskedPKStatus_PreMatching      ChannelMaskedPKStatus_Status = 2
	ChannelMaskedPKStatus_AutoInMatching   ChannelMaskedPKStatus_Status = 3
	ChannelMaskedPKStatus_ActiveInMatching ChannelMaskedPKStatus_Status = 4
	ChannelMaskedPKStatus_InPKing          ChannelMaskedPKStatus_Status = 5
	ChannelMaskedPKStatus_InReviving       ChannelMaskedPKStatus_Status = 6
	ChannelMaskedPKStatus_IsOut            ChannelMaskedPKStatus_Status = 7
)

var ChannelMaskedPKStatus_Status_name = map[int32]string{
	0: "NotParticipating",
	1: "NotMatching",
	2: "PreMatching",
	3: "AutoInMatching",
	4: "ActiveInMatching",
	5: "InPKing",
	6: "InReviving",
	7: "IsOut",
}
var ChannelMaskedPKStatus_Status_value = map[string]int32{
	"NotParticipating": 0,
	"NotMatching":      1,
	"PreMatching":      2,
	"AutoInMatching":   3,
	"ActiveInMatching": 4,
	"InPKing":          5,
	"InReviving":       6,
	"IsOut":            7,
}

func (x ChannelMaskedPKStatus_Status) String() string {
	return proto.EnumName(ChannelMaskedPKStatus_Status_name, int32(x))
}
func (ChannelMaskedPKStatus_Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{3, 0}
}

type ChannelMaskedPKBattle_SubPhrase int32

const (
	ChannelMaskedPKBattle_Common    ChannelMaskedPKBattle_SubPhrase = 0
	ChannelMaskedPKBattle_QuickKill ChannelMaskedPKBattle_SubPhrase = 1
	ChannelMaskedPKBattle_PeakPk    ChannelMaskedPKBattle_SubPhrase = 2
)

var ChannelMaskedPKBattle_SubPhrase_name = map[int32]string{
	0: "Common",
	1: "QuickKill",
	2: "PeakPk",
}
var ChannelMaskedPKBattle_SubPhrase_value = map[string]int32{
	"Common":    0,
	"QuickKill": 1,
	"PeakPk":    2,
}

func (x ChannelMaskedPKBattle_SubPhrase) String() string {
	return proto.EnumName(ChannelMaskedPKBattle_SubPhrase_name, int32(x))
}
func (ChannelMaskedPKBattle_SubPhrase) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{8, 0}
}

type TestPushQuickKillChangeReq_EventType int32

const (
	TestPushQuickKillChangeReq_Unknown          TestPushQuickKillChangeReq_EventType = 0
	TestPushQuickKillChangeReq_TriggerQuickKill TestPushQuickKillChangeReq_EventType = 1
	TestPushQuickKillChangeReq_StopQuickKill    TestPushQuickKillChangeReq_EventType = 2
)

var TestPushQuickKillChangeReq_EventType_name = map[int32]string{
	0: "Unknown",
	1: "TriggerQuickKill",
	2: "StopQuickKill",
}
var TestPushQuickKillChangeReq_EventType_value = map[string]int32{
	"Unknown":          0,
	"TriggerQuickKill": 1,
	"StopQuickKill":    2,
}

func (x TestPushQuickKillChangeReq_EventType) String() string {
	return proto.EnumName(TestPushQuickKillChangeReq_EventType_name, int32(x))
}
func (TestPushQuickKillChangeReq_EventType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{68, 0}
}

type UserPrivilege struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Type                 uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	Options              []byte   `protobuf:"bytes,4,opt,name=options,proto3" json:"options,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPrivilege) Reset()         { *m = UserPrivilege{} }
func (m *UserPrivilege) String() string { return proto.CompactTextString(m) }
func (*UserPrivilege) ProtoMessage()    {}
func (*UserPrivilege) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{0}
}
func (m *UserPrivilege) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPrivilege.Unmarshal(m, b)
}
func (m *UserPrivilege) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPrivilege.Marshal(b, m, deterministic)
}
func (dst *UserPrivilege) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPrivilege.Merge(dst, src)
}
func (m *UserPrivilege) XXX_Size() int {
	return xxx_messageInfo_UserPrivilege.Size(m)
}
func (m *UserPrivilege) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPrivilege.DiscardUnknown(m)
}

var xxx_messageInfo_UserPrivilege proto.InternalMessageInfo

func (m *UserPrivilege) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserPrivilege) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserPrivilege) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *UserPrivilege) GetOptions() []byte {
	if m != nil {
		return m.Options
	}
	return nil
}

type UserProfile struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string         `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string         `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	AccountAlias         string         `protobuf:"bytes,4,opt,name=account_alias,json=accountAlias,proto3" json:"account_alias,omitempty"`
	Sex                  uint32         `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	Privilege            *UserPrivilege `protobuf:"bytes,6,opt,name=privilege,proto3" json:"privilege,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserProfile) Reset()         { *m = UserProfile{} }
func (m *UserProfile) String() string { return proto.CompactTextString(m) }
func (*UserProfile) ProtoMessage()    {}
func (*UserProfile) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{1}
}
func (m *UserProfile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserProfile.Unmarshal(m, b)
}
func (m *UserProfile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserProfile.Marshal(b, m, deterministic)
}
func (dst *UserProfile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserProfile.Merge(dst, src)
}
func (m *UserProfile) XXX_Size() int {
	return xxx_messageInfo_UserProfile.Size(m)
}
func (m *UserProfile) XXX_DiscardUnknown() {
	xxx_messageInfo_UserProfile.DiscardUnknown(m)
}

var xxx_messageInfo_UserProfile proto.InternalMessageInfo

func (m *UserProfile) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserProfile) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserProfile) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserProfile) GetAccountAlias() string {
	if m != nil {
		return m.AccountAlias
	}
	return ""
}

func (m *UserProfile) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserProfile) GetPrivilege() *UserPrivilege {
	if m != nil {
		return m.Privilege
	}
	return nil
}

// 蒙面PK该场次配置信息
type ChannelMaskedPKConf struct {
	BeginTs              uint32   `protobuf:"varint,1,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,2,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	ChipRole             uint32   `protobuf:"varint,3,opt,name=chip_role,json=chipRole,proto3" json:"chip_role,omitempty"`
	Chip                 uint32   `protobuf:"varint,4,opt,name=chip,proto3" json:"chip,omitempty"`
	ContinueMatch        uint32   `protobuf:"varint,5,opt,name=continue_match,json=continueMatch,proto3" json:"continue_match,omitempty"`
	AutoMatchingCnt      uint32   `protobuf:"varint,6,opt,name=auto_matching_cnt,json=autoMatchingCnt,proto3" json:"auto_matching_cnt,omitempty"`
	JumpUrl              string   `protobuf:"bytes,7,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	ConfId               uint32   `protobuf:"varint,8,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	ServerNs             int64    `protobuf:"varint,9,opt,name=server_ns,json=serverNs,proto3" json:"server_ns,omitempty"`
	DivideType           uint32   `protobuf:"varint,10,opt,name=divide_type,json=divideType,proto3" json:"divide_type,omitempty"`
	UseBackpack          uint32   `protobuf:"varint,11,opt,name=use_backpack,json=useBackpack,proto3" json:"use_backpack,omitempty"`
	ChipReceiveEndTs     uint32   `protobuf:"varint,12,opt,name=chip_receive_end_ts,json=chipReceiveEndTs,proto3" json:"chip_receive_end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelMaskedPKConf) Reset()         { *m = ChannelMaskedPKConf{} }
func (m *ChannelMaskedPKConf) String() string { return proto.CompactTextString(m) }
func (*ChannelMaskedPKConf) ProtoMessage()    {}
func (*ChannelMaskedPKConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{2}
}
func (m *ChannelMaskedPKConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMaskedPKConf.Unmarshal(m, b)
}
func (m *ChannelMaskedPKConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMaskedPKConf.Marshal(b, m, deterministic)
}
func (dst *ChannelMaskedPKConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMaskedPKConf.Merge(dst, src)
}
func (m *ChannelMaskedPKConf) XXX_Size() int {
	return xxx_messageInfo_ChannelMaskedPKConf.Size(m)
}
func (m *ChannelMaskedPKConf) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMaskedPKConf.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMaskedPKConf proto.InternalMessageInfo

func (m *ChannelMaskedPKConf) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetChipRole() uint32 {
	if m != nil {
		return m.ChipRole
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetChip() uint32 {
	if m != nil {
		return m.Chip
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetContinueMatch() uint32 {
	if m != nil {
		return m.ContinueMatch
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetAutoMatchingCnt() uint32 {
	if m != nil {
		return m.AutoMatchingCnt
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *ChannelMaskedPKConf) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetServerNs() int64 {
	if m != nil {
		return m.ServerNs
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetDivideType() uint32 {
	if m != nil {
		return m.DivideType
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetUseBackpack() uint32 {
	if m != nil {
		return m.UseBackpack
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetChipReceiveEndTs() uint32 {
	if m != nil {
		return m.ChipReceiveEndTs
	}
	return 0
}

// 蒙面PK状态信息
type ChannelMaskedPKStatus struct {
	GameId               uint32               `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	ChannelId            uint32               `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	CurrChip             uint32               `protobuf:"varint,3,opt,name=curr_chip,json=currChip,proto3" json:"curr_chip,omitempty"`
	WinCnt               uint32               `protobuf:"varint,4,opt,name=win_cnt,json=winCnt,proto3" json:"win_cnt,omitempty"`
	LossCnt              uint32               `protobuf:"varint,5,opt,name=loss_cnt,json=lossCnt,proto3" json:"loss_cnt,omitempty"`
	Status               uint32               `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	LossChip             uint32               `protobuf:"varint,7,opt,name=loss_chip,json=lossChip,proto3" json:"loss_chip,omitempty"`
	StatusEndTs          uint32               `protobuf:"varint,8,opt,name=status_end_ts,json=statusEndTs,proto3" json:"status_end_ts,omitempty"`
	StatusDesc           string               `protobuf:"bytes,9,opt,name=status_desc,json=statusDesc,proto3" json:"status_desc,omitempty"`
	RestCancelCnt        uint32               `protobuf:"varint,10,opt,name=rest_cancel_cnt,json=restCancelCnt,proto3" json:"rest_cancel_cnt,omitempty"`
	RestReviveCnt        uint32               `protobuf:"varint,11,opt,name=rest_revive_cnt,json=restReviveCnt,proto3" json:"rest_revive_cnt,omitempty"`
	RevivedCnt           uint32               `protobuf:"varint,12,opt,name=revived_cnt,json=revivedCnt,proto3" json:"revived_cnt,omitempty"`
	PkConf               *ChannelMaskedPKConf `protobuf:"bytes,13,opt,name=pk_conf,json=pkConf,proto3" json:"pk_conf,omitempty"`
	ServerNs             int64                `protobuf:"varint,14,opt,name=server_ns,json=serverNs,proto3" json:"server_ns,omitempty"`
	LossDesc             string               `protobuf:"bytes,15,opt,name=loss_desc,json=lossDesc,proto3" json:"loss_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ChannelMaskedPKStatus) Reset()         { *m = ChannelMaskedPKStatus{} }
func (m *ChannelMaskedPKStatus) String() string { return proto.CompactTextString(m) }
func (*ChannelMaskedPKStatus) ProtoMessage()    {}
func (*ChannelMaskedPKStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{3}
}
func (m *ChannelMaskedPKStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMaskedPKStatus.Unmarshal(m, b)
}
func (m *ChannelMaskedPKStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMaskedPKStatus.Marshal(b, m, deterministic)
}
func (dst *ChannelMaskedPKStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMaskedPKStatus.Merge(dst, src)
}
func (m *ChannelMaskedPKStatus) XXX_Size() int {
	return xxx_messageInfo_ChannelMaskedPKStatus.Size(m)
}
func (m *ChannelMaskedPKStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMaskedPKStatus.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMaskedPKStatus proto.InternalMessageInfo

func (m *ChannelMaskedPKStatus) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetCurrChip() uint32 {
	if m != nil {
		return m.CurrChip
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetWinCnt() uint32 {
	if m != nil {
		return m.WinCnt
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetLossCnt() uint32 {
	if m != nil {
		return m.LossCnt
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetLossChip() uint32 {
	if m != nil {
		return m.LossChip
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetStatusEndTs() uint32 {
	if m != nil {
		return m.StatusEndTs
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetStatusDesc() string {
	if m != nil {
		return m.StatusDesc
	}
	return ""
}

func (m *ChannelMaskedPKStatus) GetRestCancelCnt() uint32 {
	if m != nil {
		return m.RestCancelCnt
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetRestReviveCnt() uint32 {
	if m != nil {
		return m.RestReviveCnt
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetRevivedCnt() uint32 {
	if m != nil {
		return m.RevivedCnt
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetPkConf() *ChannelMaskedPKConf {
	if m != nil {
		return m.PkConf
	}
	return nil
}

func (m *ChannelMaskedPKStatus) GetServerNs() int64 {
	if m != nil {
		return m.ServerNs
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetLossDesc() string {
	if m != nil {
		return m.LossDesc
	}
	return ""
}

// 蒙面PK榜单成员简略信息
type ChannelMaskedPKRankMem struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Score                uint32       `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	Profile              *UserProfile `protobuf:"bytes,3,opt,name=profile,proto3" json:"profile,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChannelMaskedPKRankMem) Reset()         { *m = ChannelMaskedPKRankMem{} }
func (m *ChannelMaskedPKRankMem) String() string { return proto.CompactTextString(m) }
func (*ChannelMaskedPKRankMem) ProtoMessage()    {}
func (*ChannelMaskedPKRankMem) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{4}
}
func (m *ChannelMaskedPKRankMem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMaskedPKRankMem.Unmarshal(m, b)
}
func (m *ChannelMaskedPKRankMem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMaskedPKRankMem.Marshal(b, m, deterministic)
}
func (dst *ChannelMaskedPKRankMem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMaskedPKRankMem.Merge(dst, src)
}
func (m *ChannelMaskedPKRankMem) XXX_Size() int {
	return xxx_messageInfo_ChannelMaskedPKRankMem.Size(m)
}
func (m *ChannelMaskedPKRankMem) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMaskedPKRankMem.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMaskedPKRankMem proto.InternalMessageInfo

func (m *ChannelMaskedPKRankMem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelMaskedPKRankMem) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ChannelMaskedPKRankMem) GetProfile() *UserProfile {
	if m != nil {
		return m.Profile
	}
	return nil
}

// 蒙面PK进行PK中的信息
type ChannelMaskedPKInfo struct {
	ChannelId            uint32                    `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	CurrChip             uint32                    `protobuf:"varint,2,opt,name=curr_chip,json=currChip,proto3" json:"curr_chip,omitempty"`
	WinCnt               uint32                    `protobuf:"varint,3,opt,name=win_cnt,json=winCnt,proto3" json:"win_cnt,omitempty"`
	ReviveCnt            uint32                    `protobuf:"varint,4,opt,name=revive_cnt,json=reviveCnt,proto3" json:"revive_cnt,omitempty"`
	Score                uint32                    `protobuf:"varint,5,opt,name=score,proto3" json:"score,omitempty"`
	LossDesc             string                    `protobuf:"bytes,6,opt,name=loss_desc,json=lossDesc,proto3" json:"loss_desc,omitempty"`
	TopAnchorList        []*ChannelMaskedPKRankMem `protobuf:"bytes,7,rep,name=top_anchor_list,json=topAnchorList,proto3" json:"top_anchor_list,omitempty"`
	PrefixLossDesc       string                    `protobuf:"bytes,8,opt,name=prefix_loss_desc,json=prefixLossDesc,proto3" json:"prefix_loss_desc,omitempty"`
	LossChip             uint32                    `protobuf:"varint,9,opt,name=loss_chip,json=lossChip,proto3" json:"loss_chip,omitempty"`
	QuickKillInfo        *QuickKillInfo            `protobuf:"bytes,10,opt,name=quick_kill_info,json=quickKillInfo,proto3" json:"quick_kill_info,omitempty"`
	PeakPkInfo           *PeakPkInfo               `protobuf:"bytes,11,opt,name=peak_pk_info,json=peakPkInfo,proto3" json:"peak_pk_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *ChannelMaskedPKInfo) Reset()         { *m = ChannelMaskedPKInfo{} }
func (m *ChannelMaskedPKInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelMaskedPKInfo) ProtoMessage()    {}
func (*ChannelMaskedPKInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{5}
}
func (m *ChannelMaskedPKInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMaskedPKInfo.Unmarshal(m, b)
}
func (m *ChannelMaskedPKInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMaskedPKInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelMaskedPKInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMaskedPKInfo.Merge(dst, src)
}
func (m *ChannelMaskedPKInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelMaskedPKInfo.Size(m)
}
func (m *ChannelMaskedPKInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMaskedPKInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMaskedPKInfo proto.InternalMessageInfo

func (m *ChannelMaskedPKInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelMaskedPKInfo) GetCurrChip() uint32 {
	if m != nil {
		return m.CurrChip
	}
	return 0
}

func (m *ChannelMaskedPKInfo) GetWinCnt() uint32 {
	if m != nil {
		return m.WinCnt
	}
	return 0
}

func (m *ChannelMaskedPKInfo) GetReviveCnt() uint32 {
	if m != nil {
		return m.ReviveCnt
	}
	return 0
}

func (m *ChannelMaskedPKInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ChannelMaskedPKInfo) GetLossDesc() string {
	if m != nil {
		return m.LossDesc
	}
	return ""
}

func (m *ChannelMaskedPKInfo) GetTopAnchorList() []*ChannelMaskedPKRankMem {
	if m != nil {
		return m.TopAnchorList
	}
	return nil
}

func (m *ChannelMaskedPKInfo) GetPrefixLossDesc() string {
	if m != nil {
		return m.PrefixLossDesc
	}
	return ""
}

func (m *ChannelMaskedPKInfo) GetLossChip() uint32 {
	if m != nil {
		return m.LossChip
	}
	return 0
}

func (m *ChannelMaskedPKInfo) GetQuickKillInfo() *QuickKillInfo {
	if m != nil {
		return m.QuickKillInfo
	}
	return nil
}

func (m *ChannelMaskedPKInfo) GetPeakPkInfo() *PeakPkInfo {
	if m != nil {
		return m.PeakPkInfo
	}
	return nil
}

// 斩杀信息
type QuickKillInfo struct {
	QuickKillDescPrefix  string   `protobuf:"bytes,1,opt,name=quick_kill_desc_prefix,json=quickKillDescPrefix,proto3" json:"quick_kill_desc_prefix,omitempty"`
	QuickKillDesc        string   `protobuf:"bytes,2,opt,name=quick_kill_desc,json=quickKillDesc,proto3" json:"quick_kill_desc,omitempty"`
	QuickKillEndTs       uint32   `protobuf:"varint,3,opt,name=quick_kill_end_ts,json=quickKillEndTs,proto3" json:"quick_kill_end_ts,omitempty"`
	EnableMinPkSec       uint32   `protobuf:"varint,4,opt,name=enable_min_pk_sec,json=enableMinPkSec,proto3" json:"enable_min_pk_sec,omitempty"`
	EnableMaxPkSec       uint32   `protobuf:"varint,5,opt,name=enable_max_pk_sec,json=enableMaxPkSec,proto3" json:"enable_max_pk_sec,omitempty"`
	ConditionValue       uint32   `protobuf:"varint,6,opt,name=condition_value,json=conditionValue,proto3" json:"condition_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QuickKillInfo) Reset()         { *m = QuickKillInfo{} }
func (m *QuickKillInfo) String() string { return proto.CompactTextString(m) }
func (*QuickKillInfo) ProtoMessage()    {}
func (*QuickKillInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{6}
}
func (m *QuickKillInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuickKillInfo.Unmarshal(m, b)
}
func (m *QuickKillInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuickKillInfo.Marshal(b, m, deterministic)
}
func (dst *QuickKillInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuickKillInfo.Merge(dst, src)
}
func (m *QuickKillInfo) XXX_Size() int {
	return xxx_messageInfo_QuickKillInfo.Size(m)
}
func (m *QuickKillInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_QuickKillInfo.DiscardUnknown(m)
}

var xxx_messageInfo_QuickKillInfo proto.InternalMessageInfo

func (m *QuickKillInfo) GetQuickKillDescPrefix() string {
	if m != nil {
		return m.QuickKillDescPrefix
	}
	return ""
}

func (m *QuickKillInfo) GetQuickKillDesc() string {
	if m != nil {
		return m.QuickKillDesc
	}
	return ""
}

func (m *QuickKillInfo) GetQuickKillEndTs() uint32 {
	if m != nil {
		return m.QuickKillEndTs
	}
	return 0
}

func (m *QuickKillInfo) GetEnableMinPkSec() uint32 {
	if m != nil {
		return m.EnableMinPkSec
	}
	return 0
}

func (m *QuickKillInfo) GetEnableMaxPkSec() uint32 {
	if m != nil {
		return m.EnableMaxPkSec
	}
	return 0
}

func (m *QuickKillInfo) GetConditionValue() uint32 {
	if m != nil {
		return m.ConditionValue
	}
	return 0
}

// 巅峰对决信息
type PeakPkInfo struct {
	PeakDesc             string   `protobuf:"bytes,1,opt,name=peak_desc,json=peakDesc,proto3" json:"peak_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PeakPkInfo) Reset()         { *m = PeakPkInfo{} }
func (m *PeakPkInfo) String() string { return proto.CompactTextString(m) }
func (*PeakPkInfo) ProtoMessage()    {}
func (*PeakPkInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{7}
}
func (m *PeakPkInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PeakPkInfo.Unmarshal(m, b)
}
func (m *PeakPkInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PeakPkInfo.Marshal(b, m, deterministic)
}
func (dst *PeakPkInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PeakPkInfo.Merge(dst, src)
}
func (m *PeakPkInfo) XXX_Size() int {
	return xxx_messageInfo_PeakPkInfo.Size(m)
}
func (m *PeakPkInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PeakPkInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PeakPkInfo proto.InternalMessageInfo

func (m *PeakPkInfo) GetPeakDesc() string {
	if m != nil {
		return m.PeakDesc
	}
	return ""
}

// 蒙面PK实时战况
type ChannelMaskedPKBattle struct {
	MemList              []*ChannelMaskedPKInfo `protobuf:"bytes,1,rep,name=mem_list,json=memList,proto3" json:"mem_list,omitempty"`
	PkEndTs              uint32                 `protobuf:"varint,2,opt,name=pk_end_ts,json=pkEndTs,proto3" json:"pk_end_ts,omitempty"`
	ChipRole             uint32                 `protobuf:"varint,3,opt,name=chip_role,json=chipRole,proto3" json:"chip_role,omitempty"`
	ServerNs             int64                  `protobuf:"varint,4,opt,name=server_ns,json=serverNs,proto3" json:"server_ns,omitempty"`
	ValidPkDesc          string                 `protobuf:"bytes,5,opt,name=valid_pk_desc,json=validPkDesc,proto3" json:"valid_pk_desc,omitempty"`
	ValidPk              bool                   `protobuf:"varint,6,opt,name=valid_pk,json=validPk,proto3" json:"valid_pk,omitempty"`
	PkId                 uint32                 `protobuf:"varint,7,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	SubPhrase            uint32                 `protobuf:"varint,8,opt,name=sub_phrase,json=subPhrase,proto3" json:"sub_phrase,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ChannelMaskedPKBattle) Reset()         { *m = ChannelMaskedPKBattle{} }
func (m *ChannelMaskedPKBattle) String() string { return proto.CompactTextString(m) }
func (*ChannelMaskedPKBattle) ProtoMessage()    {}
func (*ChannelMaskedPKBattle) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{8}
}
func (m *ChannelMaskedPKBattle) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMaskedPKBattle.Unmarshal(m, b)
}
func (m *ChannelMaskedPKBattle) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMaskedPKBattle.Marshal(b, m, deterministic)
}
func (dst *ChannelMaskedPKBattle) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMaskedPKBattle.Merge(dst, src)
}
func (m *ChannelMaskedPKBattle) XXX_Size() int {
	return xxx_messageInfo_ChannelMaskedPKBattle.Size(m)
}
func (m *ChannelMaskedPKBattle) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMaskedPKBattle.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMaskedPKBattle proto.InternalMessageInfo

func (m *ChannelMaskedPKBattle) GetMemList() []*ChannelMaskedPKInfo {
	if m != nil {
		return m.MemList
	}
	return nil
}

func (m *ChannelMaskedPKBattle) GetPkEndTs() uint32 {
	if m != nil {
		return m.PkEndTs
	}
	return 0
}

func (m *ChannelMaskedPKBattle) GetChipRole() uint32 {
	if m != nil {
		return m.ChipRole
	}
	return 0
}

func (m *ChannelMaskedPKBattle) GetServerNs() int64 {
	if m != nil {
		return m.ServerNs
	}
	return 0
}

func (m *ChannelMaskedPKBattle) GetValidPkDesc() string {
	if m != nil {
		return m.ValidPkDesc
	}
	return ""
}

func (m *ChannelMaskedPKBattle) GetValidPk() bool {
	if m != nil {
		return m.ValidPk
	}
	return false
}

func (m *ChannelMaskedPKBattle) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

func (m *ChannelMaskedPKBattle) GetSubPhrase() uint32 {
	if m != nil {
		return m.SubPhrase
	}
	return 0
}

// 蒙面PK复活进度
type ChannelMaskedPKRevive struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Goal                 uint32   `protobuf:"varint,2,opt,name=goal,proto3" json:"goal,omitempty"`
	Curr                 uint32   `protobuf:"varint,3,opt,name=curr,proto3" json:"curr,omitempty"`
	EndTs                uint32   `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	ServerNs             int64    `protobuf:"varint,5,opt,name=server_ns,json=serverNs,proto3" json:"server_ns,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelMaskedPKRevive) Reset()         { *m = ChannelMaskedPKRevive{} }
func (m *ChannelMaskedPKRevive) String() string { return proto.CompactTextString(m) }
func (*ChannelMaskedPKRevive) ProtoMessage()    {}
func (*ChannelMaskedPKRevive) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{9}
}
func (m *ChannelMaskedPKRevive) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMaskedPKRevive.Unmarshal(m, b)
}
func (m *ChannelMaskedPKRevive) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMaskedPKRevive.Marshal(b, m, deterministic)
}
func (dst *ChannelMaskedPKRevive) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMaskedPKRevive.Merge(dst, src)
}
func (m *ChannelMaskedPKRevive) XXX_Size() int {
	return xxx_messageInfo_ChannelMaskedPKRevive.Size(m)
}
func (m *ChannelMaskedPKRevive) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMaskedPKRevive.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMaskedPKRevive proto.InternalMessageInfo

func (m *ChannelMaskedPKRevive) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelMaskedPKRevive) GetGoal() uint32 {
	if m != nil {
		return m.Goal
	}
	return 0
}

func (m *ChannelMaskedPKRevive) GetCurr() uint32 {
	if m != nil {
		return m.Curr
	}
	return 0
}

func (m *ChannelMaskedPKRevive) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *ChannelMaskedPKRevive) GetServerNs() int64 {
	if m != nil {
		return m.ServerNs
	}
	return 0
}

// 获取房间蒙面PK信息
type GetChannelMaskedPKInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelMaskedPKInfoReq) Reset()         { *m = GetChannelMaskedPKInfoReq{} }
func (m *GetChannelMaskedPKInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelMaskedPKInfoReq) ProtoMessage()    {}
func (*GetChannelMaskedPKInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{10}
}
func (m *GetChannelMaskedPKInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMaskedPKInfoReq.Unmarshal(m, b)
}
func (m *GetChannelMaskedPKInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMaskedPKInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelMaskedPKInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMaskedPKInfoReq.Merge(dst, src)
}
func (m *GetChannelMaskedPKInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelMaskedPKInfoReq.Size(m)
}
func (m *GetChannelMaskedPKInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMaskedPKInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMaskedPKInfoReq proto.InternalMessageInfo

func (m *GetChannelMaskedPKInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelMaskedPKInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelMaskedPKInfoResp struct {
	StatusInfo           *ChannelMaskedPKStatus `protobuf:"bytes,1,opt,name=status_info,json=statusInfo,proto3" json:"status_info,omitempty"`
	PkBattleInfo         *ChannelMaskedPKBattle `protobuf:"bytes,2,opt,name=pk_battle_info,json=pkBattleInfo,proto3" json:"pk_battle_info,omitempty"`
	ReviveInfo           *ChannelMaskedPKRevive `protobuf:"bytes,3,opt,name=revive_info,json=reviveInfo,proto3" json:"revive_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetChannelMaskedPKInfoResp) Reset()         { *m = GetChannelMaskedPKInfoResp{} }
func (m *GetChannelMaskedPKInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelMaskedPKInfoResp) ProtoMessage()    {}
func (*GetChannelMaskedPKInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{11}
}
func (m *GetChannelMaskedPKInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMaskedPKInfoResp.Unmarshal(m, b)
}
func (m *GetChannelMaskedPKInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMaskedPKInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelMaskedPKInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMaskedPKInfoResp.Merge(dst, src)
}
func (m *GetChannelMaskedPKInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelMaskedPKInfoResp.Size(m)
}
func (m *GetChannelMaskedPKInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMaskedPKInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMaskedPKInfoResp proto.InternalMessageInfo

func (m *GetChannelMaskedPKInfoResp) GetStatusInfo() *ChannelMaskedPKStatus {
	if m != nil {
		return m.StatusInfo
	}
	return nil
}

func (m *GetChannelMaskedPKInfoResp) GetPkBattleInfo() *ChannelMaskedPKBattle {
	if m != nil {
		return m.PkBattleInfo
	}
	return nil
}

func (m *GetChannelMaskedPKInfoResp) GetReviveInfo() *ChannelMaskedPKRevive {
	if m != nil {
		return m.ReviveInfo
	}
	return nil
}

// 开始蒙面PK匹配
type StartChannelMaskedPKReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartChannelMaskedPKReq) Reset()         { *m = StartChannelMaskedPKReq{} }
func (m *StartChannelMaskedPKReq) String() string { return proto.CompactTextString(m) }
func (*StartChannelMaskedPKReq) ProtoMessage()    {}
func (*StartChannelMaskedPKReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{12}
}
func (m *StartChannelMaskedPKReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartChannelMaskedPKReq.Unmarshal(m, b)
}
func (m *StartChannelMaskedPKReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartChannelMaskedPKReq.Marshal(b, m, deterministic)
}
func (dst *StartChannelMaskedPKReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartChannelMaskedPKReq.Merge(dst, src)
}
func (m *StartChannelMaskedPKReq) XXX_Size() int {
	return xxx_messageInfo_StartChannelMaskedPKReq.Size(m)
}
func (m *StartChannelMaskedPKReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartChannelMaskedPKReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartChannelMaskedPKReq proto.InternalMessageInfo

func (m *StartChannelMaskedPKReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StartChannelMaskedPKReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type StartChannelMaskedPKResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartChannelMaskedPKResp) Reset()         { *m = StartChannelMaskedPKResp{} }
func (m *StartChannelMaskedPKResp) String() string { return proto.CompactTextString(m) }
func (*StartChannelMaskedPKResp) ProtoMessage()    {}
func (*StartChannelMaskedPKResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{13}
}
func (m *StartChannelMaskedPKResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartChannelMaskedPKResp.Unmarshal(m, b)
}
func (m *StartChannelMaskedPKResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartChannelMaskedPKResp.Marshal(b, m, deterministic)
}
func (dst *StartChannelMaskedPKResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartChannelMaskedPKResp.Merge(dst, src)
}
func (m *StartChannelMaskedPKResp) XXX_Size() int {
	return xxx_messageInfo_StartChannelMaskedPKResp.Size(m)
}
func (m *StartChannelMaskedPKResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartChannelMaskedPKResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartChannelMaskedPKResp proto.InternalMessageInfo

// 放弃参加本次比赛
type GiveUpChannelMaskedPKReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ConfId               uint32   `protobuf:"varint,3,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiveUpChannelMaskedPKReq) Reset()         { *m = GiveUpChannelMaskedPKReq{} }
func (m *GiveUpChannelMaskedPKReq) String() string { return proto.CompactTextString(m) }
func (*GiveUpChannelMaskedPKReq) ProtoMessage()    {}
func (*GiveUpChannelMaskedPKReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{14}
}
func (m *GiveUpChannelMaskedPKReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiveUpChannelMaskedPKReq.Unmarshal(m, b)
}
func (m *GiveUpChannelMaskedPKReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiveUpChannelMaskedPKReq.Marshal(b, m, deterministic)
}
func (dst *GiveUpChannelMaskedPKReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiveUpChannelMaskedPKReq.Merge(dst, src)
}
func (m *GiveUpChannelMaskedPKReq) XXX_Size() int {
	return xxx_messageInfo_GiveUpChannelMaskedPKReq.Size(m)
}
func (m *GiveUpChannelMaskedPKReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GiveUpChannelMaskedPKReq.DiscardUnknown(m)
}

var xxx_messageInfo_GiveUpChannelMaskedPKReq proto.InternalMessageInfo

func (m *GiveUpChannelMaskedPKReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GiveUpChannelMaskedPKReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GiveUpChannelMaskedPKReq) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

type GiveUpChannelMaskedPKResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiveUpChannelMaskedPKResp) Reset()         { *m = GiveUpChannelMaskedPKResp{} }
func (m *GiveUpChannelMaskedPKResp) String() string { return proto.CompactTextString(m) }
func (*GiveUpChannelMaskedPKResp) ProtoMessage()    {}
func (*GiveUpChannelMaskedPKResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{15}
}
func (m *GiveUpChannelMaskedPKResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiveUpChannelMaskedPKResp.Unmarshal(m, b)
}
func (m *GiveUpChannelMaskedPKResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiveUpChannelMaskedPKResp.Marshal(b, m, deterministic)
}
func (dst *GiveUpChannelMaskedPKResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiveUpChannelMaskedPKResp.Merge(dst, src)
}
func (m *GiveUpChannelMaskedPKResp) XXX_Size() int {
	return xxx_messageInfo_GiveUpChannelMaskedPKResp.Size(m)
}
func (m *GiveUpChannelMaskedPKResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GiveUpChannelMaskedPKResp.DiscardUnknown(m)
}

var xxx_messageInfo_GiveUpChannelMaskedPKResp proto.InternalMessageInfo

// 取消蒙面PK匹配
type CancelChannelMaskedPKReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelChannelMaskedPKReq) Reset()         { *m = CancelChannelMaskedPKReq{} }
func (m *CancelChannelMaskedPKReq) String() string { return proto.CompactTextString(m) }
func (*CancelChannelMaskedPKReq) ProtoMessage()    {}
func (*CancelChannelMaskedPKReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{16}
}
func (m *CancelChannelMaskedPKReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelChannelMaskedPKReq.Unmarshal(m, b)
}
func (m *CancelChannelMaskedPKReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelChannelMaskedPKReq.Marshal(b, m, deterministic)
}
func (dst *CancelChannelMaskedPKReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelChannelMaskedPKReq.Merge(dst, src)
}
func (m *CancelChannelMaskedPKReq) XXX_Size() int {
	return xxx_messageInfo_CancelChannelMaskedPKReq.Size(m)
}
func (m *CancelChannelMaskedPKReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelChannelMaskedPKReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelChannelMaskedPKReq proto.InternalMessageInfo

func (m *CancelChannelMaskedPKReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CancelChannelMaskedPKReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CancelChannelMaskedPKResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelChannelMaskedPKResp) Reset()         { *m = CancelChannelMaskedPKResp{} }
func (m *CancelChannelMaskedPKResp) String() string { return proto.CompactTextString(m) }
func (*CancelChannelMaskedPKResp) ProtoMessage()    {}
func (*CancelChannelMaskedPKResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{17}
}
func (m *CancelChannelMaskedPKResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelChannelMaskedPKResp.Unmarshal(m, b)
}
func (m *CancelChannelMaskedPKResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelChannelMaskedPKResp.Marshal(b, m, deterministic)
}
func (dst *CancelChannelMaskedPKResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelChannelMaskedPKResp.Merge(dst, src)
}
func (m *CancelChannelMaskedPKResp) XXX_Size() int {
	return xxx_messageInfo_CancelChannelMaskedPKResp.Size(m)
}
func (m *CancelChannelMaskedPKResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelChannelMaskedPKResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelChannelMaskedPKResp proto.InternalMessageInfo

type GetChannelMaskedPKStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelMaskedPKStatusReq) Reset()         { *m = GetChannelMaskedPKStatusReq{} }
func (m *GetChannelMaskedPKStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelMaskedPKStatusReq) ProtoMessage()    {}
func (*GetChannelMaskedPKStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{18}
}
func (m *GetChannelMaskedPKStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMaskedPKStatusReq.Unmarshal(m, b)
}
func (m *GetChannelMaskedPKStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMaskedPKStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelMaskedPKStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMaskedPKStatusReq.Merge(dst, src)
}
func (m *GetChannelMaskedPKStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelMaskedPKStatusReq.Size(m)
}
func (m *GetChannelMaskedPKStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMaskedPKStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMaskedPKStatusReq proto.InternalMessageInfo

func (m *GetChannelMaskedPKStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelMaskedPKStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelMaskedPKStatusResp struct {
	Status               uint32   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelMaskedPKStatusResp) Reset()         { *m = GetChannelMaskedPKStatusResp{} }
func (m *GetChannelMaskedPKStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelMaskedPKStatusResp) ProtoMessage()    {}
func (*GetChannelMaskedPKStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{19}
}
func (m *GetChannelMaskedPKStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMaskedPKStatusResp.Unmarshal(m, b)
}
func (m *GetChannelMaskedPKStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMaskedPKStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelMaskedPKStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMaskedPKStatusResp.Merge(dst, src)
}
func (m *GetChannelMaskedPKStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelMaskedPKStatusResp.Size(m)
}
func (m *GetChannelMaskedPKStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMaskedPKStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMaskedPKStatusResp proto.InternalMessageInfo

func (m *GetChannelMaskedPKStatusResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type BatchGetChannelMaskedPKStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	ChannelIdList        []uint32 `protobuf:"varint,3,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelMaskedPKStatusReq) Reset()         { *m = BatchGetChannelMaskedPKStatusReq{} }
func (m *BatchGetChannelMaskedPKStatusReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelMaskedPKStatusReq) ProtoMessage()    {}
func (*BatchGetChannelMaskedPKStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{20}
}
func (m *BatchGetChannelMaskedPKStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelMaskedPKStatusReq.Unmarshal(m, b)
}
func (m *BatchGetChannelMaskedPKStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelMaskedPKStatusReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelMaskedPKStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelMaskedPKStatusReq.Merge(dst, src)
}
func (m *BatchGetChannelMaskedPKStatusReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelMaskedPKStatusReq.Size(m)
}
func (m *BatchGetChannelMaskedPKStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelMaskedPKStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelMaskedPKStatusReq proto.InternalMessageInfo

func (m *BatchGetChannelMaskedPKStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchGetChannelMaskedPKStatusReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *BatchGetChannelMaskedPKStatusReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type BatchGetChannelMaskedPKStatusResp struct {
	ChannelStatusMap     map[uint32]uint32 `protobuf:"bytes,1,rep,name=channel_status_map,json=channelStatusMap,proto3" json:"channel_status_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetChannelMaskedPKStatusResp) Reset()         { *m = BatchGetChannelMaskedPKStatusResp{} }
func (m *BatchGetChannelMaskedPKStatusResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelMaskedPKStatusResp) ProtoMessage()    {}
func (*BatchGetChannelMaskedPKStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{21}
}
func (m *BatchGetChannelMaskedPKStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelMaskedPKStatusResp.Unmarshal(m, b)
}
func (m *BatchGetChannelMaskedPKStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelMaskedPKStatusResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelMaskedPKStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelMaskedPKStatusResp.Merge(dst, src)
}
func (m *BatchGetChannelMaskedPKStatusResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelMaskedPKStatusResp.Size(m)
}
func (m *BatchGetChannelMaskedPKStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelMaskedPKStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelMaskedPKStatusResp proto.InternalMessageInfo

func (m *BatchGetChannelMaskedPKStatusResp) GetChannelStatusMap() map[uint32]uint32 {
	if m != nil {
		return m.ChannelStatusMap
	}
	return nil
}

// 获取当前蒙面PK配置信息(仅是符合资格的人能获取到)
type GetChannelMaskedPKCurrConfWithUserReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelMaskedPKCurrConfWithUserReq) Reset()         { *m = GetChannelMaskedPKCurrConfWithUserReq{} }
func (m *GetChannelMaskedPKCurrConfWithUserReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelMaskedPKCurrConfWithUserReq) ProtoMessage()    {}
func (*GetChannelMaskedPKCurrConfWithUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{22}
}
func (m *GetChannelMaskedPKCurrConfWithUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMaskedPKCurrConfWithUserReq.Unmarshal(m, b)
}
func (m *GetChannelMaskedPKCurrConfWithUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMaskedPKCurrConfWithUserReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelMaskedPKCurrConfWithUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMaskedPKCurrConfWithUserReq.Merge(dst, src)
}
func (m *GetChannelMaskedPKCurrConfWithUserReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelMaskedPKCurrConfWithUserReq.Size(m)
}
func (m *GetChannelMaskedPKCurrConfWithUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMaskedPKCurrConfWithUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMaskedPKCurrConfWithUserReq proto.InternalMessageInfo

func (m *GetChannelMaskedPKCurrConfWithUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelMaskedPKCurrConfWithUserReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelMaskedPKCurrConfWithUserResp struct {
	Conf                 *ChannelMaskedPKConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	IsGiveUp             bool                 `protobuf:"varint,2,opt,name=is_give_up,json=isGiveUp,proto3" json:"is_give_up,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetChannelMaskedPKCurrConfWithUserResp) Reset() {
	*m = GetChannelMaskedPKCurrConfWithUserResp{}
}
func (m *GetChannelMaskedPKCurrConfWithUserResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelMaskedPKCurrConfWithUserResp) ProtoMessage()    {}
func (*GetChannelMaskedPKCurrConfWithUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{23}
}
func (m *GetChannelMaskedPKCurrConfWithUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMaskedPKCurrConfWithUserResp.Unmarshal(m, b)
}
func (m *GetChannelMaskedPKCurrConfWithUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMaskedPKCurrConfWithUserResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelMaskedPKCurrConfWithUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMaskedPKCurrConfWithUserResp.Merge(dst, src)
}
func (m *GetChannelMaskedPKCurrConfWithUserResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelMaskedPKCurrConfWithUserResp.Size(m)
}
func (m *GetChannelMaskedPKCurrConfWithUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMaskedPKCurrConfWithUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMaskedPKCurrConfWithUserResp proto.InternalMessageInfo

func (m *GetChannelMaskedPKCurrConfWithUserResp) GetConf() *ChannelMaskedPKConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

func (m *GetChannelMaskedPKCurrConfWithUserResp) GetIsGiveUp() bool {
	if m != nil {
		return m.IsGiveUp
	}
	return false
}

// 获取当前蒙面PK配置信息(任何人都能获取到)
type GetChannelMaskedPKCurrConfReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelMaskedPKCurrConfReq) Reset()         { *m = GetChannelMaskedPKCurrConfReq{} }
func (m *GetChannelMaskedPKCurrConfReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelMaskedPKCurrConfReq) ProtoMessage()    {}
func (*GetChannelMaskedPKCurrConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{24}
}
func (m *GetChannelMaskedPKCurrConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMaskedPKCurrConfReq.Unmarshal(m, b)
}
func (m *GetChannelMaskedPKCurrConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMaskedPKCurrConfReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelMaskedPKCurrConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMaskedPKCurrConfReq.Merge(dst, src)
}
func (m *GetChannelMaskedPKCurrConfReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelMaskedPKCurrConfReq.Size(m)
}
func (m *GetChannelMaskedPKCurrConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMaskedPKCurrConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMaskedPKCurrConfReq proto.InternalMessageInfo

type GetChannelMaskedPKCurrConfResp struct {
	Conf                 *ChannelMaskedPKConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetChannelMaskedPKCurrConfResp) Reset()         { *m = GetChannelMaskedPKCurrConfResp{} }
func (m *GetChannelMaskedPKCurrConfResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelMaskedPKCurrConfResp) ProtoMessage()    {}
func (*GetChannelMaskedPKCurrConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{25}
}
func (m *GetChannelMaskedPKCurrConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMaskedPKCurrConfResp.Unmarshal(m, b)
}
func (m *GetChannelMaskedPKCurrConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMaskedPKCurrConfResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelMaskedPKCurrConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMaskedPKCurrConfResp.Merge(dst, src)
}
func (m *GetChannelMaskedPKCurrConfResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelMaskedPKCurrConfResp.Size(m)
}
func (m *GetChannelMaskedPKCurrConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMaskedPKCurrConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMaskedPKCurrConfResp proto.InternalMessageInfo

func (m *GetChannelMaskedPKCurrConfResp) GetConf() *ChannelMaskedPKConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type ChannelMaskedPKAnchorRankMem struct {
	Uid                  uint32                    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Score                uint32                    `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	Bonus                uint32                    `protobuf:"varint,3,opt,name=bonus,proto3" json:"bonus,omitempty"`
	Ratio                float32                   `protobuf:"fixed32,4,opt,name=ratio,proto3" json:"ratio,omitempty"`
	TopAudienceList      []*ChannelMaskedPKRankMem `protobuf:"bytes,5,rep,name=top_audience_list,json=topAudienceList,proto3" json:"top_audience_list,omitempty"`
	Profile              *UserProfile              `protobuf:"bytes,6,opt,name=profile,proto3" json:"profile,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *ChannelMaskedPKAnchorRankMem) Reset()         { *m = ChannelMaskedPKAnchorRankMem{} }
func (m *ChannelMaskedPKAnchorRankMem) String() string { return proto.CompactTextString(m) }
func (*ChannelMaskedPKAnchorRankMem) ProtoMessage()    {}
func (*ChannelMaskedPKAnchorRankMem) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{26}
}
func (m *ChannelMaskedPKAnchorRankMem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMaskedPKAnchorRankMem.Unmarshal(m, b)
}
func (m *ChannelMaskedPKAnchorRankMem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMaskedPKAnchorRankMem.Marshal(b, m, deterministic)
}
func (dst *ChannelMaskedPKAnchorRankMem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMaskedPKAnchorRankMem.Merge(dst, src)
}
func (m *ChannelMaskedPKAnchorRankMem) XXX_Size() int {
	return xxx_messageInfo_ChannelMaskedPKAnchorRankMem.Size(m)
}
func (m *ChannelMaskedPKAnchorRankMem) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMaskedPKAnchorRankMem.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMaskedPKAnchorRankMem proto.InternalMessageInfo

func (m *ChannelMaskedPKAnchorRankMem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelMaskedPKAnchorRankMem) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ChannelMaskedPKAnchorRankMem) GetBonus() uint32 {
	if m != nil {
		return m.Bonus
	}
	return 0
}

func (m *ChannelMaskedPKAnchorRankMem) GetRatio() float32 {
	if m != nil {
		return m.Ratio
	}
	return 0
}

func (m *ChannelMaskedPKAnchorRankMem) GetTopAudienceList() []*ChannelMaskedPKRankMem {
	if m != nil {
		return m.TopAudienceList
	}
	return nil
}

func (m *ChannelMaskedPKAnchorRankMem) GetProfile() *UserProfile {
	if m != nil {
		return m.Profile
	}
	return nil
}

// 获取本轮PK战力榜
type GetChannelMaskedPKAnchorRankReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Begin                uint32   `protobuf:"varint,3,opt,name=begin,proto3" json:"begin,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelMaskedPKAnchorRankReq) Reset()         { *m = GetChannelMaskedPKAnchorRankReq{} }
func (m *GetChannelMaskedPKAnchorRankReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelMaskedPKAnchorRankReq) ProtoMessage()    {}
func (*GetChannelMaskedPKAnchorRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{27}
}
func (m *GetChannelMaskedPKAnchorRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMaskedPKAnchorRankReq.Unmarshal(m, b)
}
func (m *GetChannelMaskedPKAnchorRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMaskedPKAnchorRankReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelMaskedPKAnchorRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMaskedPKAnchorRankReq.Merge(dst, src)
}
func (m *GetChannelMaskedPKAnchorRankReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelMaskedPKAnchorRankReq.Size(m)
}
func (m *GetChannelMaskedPKAnchorRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMaskedPKAnchorRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMaskedPKAnchorRankReq proto.InternalMessageInfo

func (m *GetChannelMaskedPKAnchorRankReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelMaskedPKAnchorRankReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelMaskedPKAnchorRankReq) GetBegin() uint32 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *GetChannelMaskedPKAnchorRankReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetChannelMaskedPKAnchorRankResp struct {
	RankList             []*ChannelMaskedPKAnchorRankMem `protobuf:"bytes,2,rep,name=rank_list,json=rankList,proto3" json:"rank_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetChannelMaskedPKAnchorRankResp) Reset()         { *m = GetChannelMaskedPKAnchorRankResp{} }
func (m *GetChannelMaskedPKAnchorRankResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelMaskedPKAnchorRankResp) ProtoMessage()    {}
func (*GetChannelMaskedPKAnchorRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{28}
}
func (m *GetChannelMaskedPKAnchorRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMaskedPKAnchorRankResp.Unmarshal(m, b)
}
func (m *GetChannelMaskedPKAnchorRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMaskedPKAnchorRankResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelMaskedPKAnchorRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMaskedPKAnchorRankResp.Merge(dst, src)
}
func (m *GetChannelMaskedPKAnchorRankResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelMaskedPKAnchorRankResp.Size(m)
}
func (m *GetChannelMaskedPKAnchorRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMaskedPKAnchorRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMaskedPKAnchorRankResp proto.InternalMessageInfo

func (m *GetChannelMaskedPKAnchorRankResp) GetRankList() []*ChannelMaskedPKAnchorRankMem {
	if m != nil {
		return m.RankList
	}
	return nil
}

// 获取单场PK战力榜
type GetChannelSinglePKAnchorRankReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Begin                uint32   `protobuf:"varint,3,opt,name=begin,proto3" json:"begin,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelSinglePKAnchorRankReq) Reset()         { *m = GetChannelSinglePKAnchorRankReq{} }
func (m *GetChannelSinglePKAnchorRankReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelSinglePKAnchorRankReq) ProtoMessage()    {}
func (*GetChannelSinglePKAnchorRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{29}
}
func (m *GetChannelSinglePKAnchorRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelSinglePKAnchorRankReq.Unmarshal(m, b)
}
func (m *GetChannelSinglePKAnchorRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelSinglePKAnchorRankReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelSinglePKAnchorRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelSinglePKAnchorRankReq.Merge(dst, src)
}
func (m *GetChannelSinglePKAnchorRankReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelSinglePKAnchorRankReq.Size(m)
}
func (m *GetChannelSinglePKAnchorRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelSinglePKAnchorRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelSinglePKAnchorRankReq proto.InternalMessageInfo

func (m *GetChannelSinglePKAnchorRankReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelSinglePKAnchorRankReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelSinglePKAnchorRankReq) GetBegin() uint32 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *GetChannelSinglePKAnchorRankReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetChannelSinglePKAnchorRankResp struct {
	RankList             []*ChannelMaskedPKAnchorRankMem `protobuf:"bytes,2,rep,name=rank_list,json=rankList,proto3" json:"rank_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetChannelSinglePKAnchorRankResp) Reset()         { *m = GetChannelSinglePKAnchorRankResp{} }
func (m *GetChannelSinglePKAnchorRankResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelSinglePKAnchorRankResp) ProtoMessage()    {}
func (*GetChannelSinglePKAnchorRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{30}
}
func (m *GetChannelSinglePKAnchorRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelSinglePKAnchorRankResp.Unmarshal(m, b)
}
func (m *GetChannelSinglePKAnchorRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelSinglePKAnchorRankResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelSinglePKAnchorRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelSinglePKAnchorRankResp.Merge(dst, src)
}
func (m *GetChannelSinglePKAnchorRankResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelSinglePKAnchorRankResp.Size(m)
}
func (m *GetChannelSinglePKAnchorRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelSinglePKAnchorRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelSinglePKAnchorRankResp proto.InternalMessageInfo

func (m *GetChannelSinglePKAnchorRankResp) GetRankList() []*ChannelMaskedPKAnchorRankMem {
	if m != nil {
		return m.RankList
	}
	return nil
}

// 只填channel_id和uid 其他留空就好了
type BatchAddUserToEntertainmentQualificationReq struct {
	Qualifications       []*Qualification `protobuf:"bytes,1,rep,name=qualifications,proto3" json:"qualifications,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatchAddUserToEntertainmentQualificationReq) Reset() {
	*m = BatchAddUserToEntertainmentQualificationReq{}
}
func (m *BatchAddUserToEntertainmentQualificationReq) String() string {
	return proto.CompactTextString(m)
}
func (*BatchAddUserToEntertainmentQualificationReq) ProtoMessage() {}
func (*BatchAddUserToEntertainmentQualificationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{31}
}
func (m *BatchAddUserToEntertainmentQualificationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddUserToEntertainmentQualificationReq.Unmarshal(m, b)
}
func (m *BatchAddUserToEntertainmentQualificationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddUserToEntertainmentQualificationReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddUserToEntertainmentQualificationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddUserToEntertainmentQualificationReq.Merge(dst, src)
}
func (m *BatchAddUserToEntertainmentQualificationReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddUserToEntertainmentQualificationReq.Size(m)
}
func (m *BatchAddUserToEntertainmentQualificationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddUserToEntertainmentQualificationReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddUserToEntertainmentQualificationReq proto.InternalMessageInfo

func (m *BatchAddUserToEntertainmentQualificationReq) GetQualifications() []*Qualification {
	if m != nil {
		return m.Qualifications
	}
	return nil
}

type Qualification struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	UserNickname         string   `protobuf:"bytes,3,opt,name=user_nickname,json=userNickname,proto3" json:"user_nickname,omitempty"`
	UserAge              uint32   `protobuf:"varint,4,opt,name=user_age,json=userAge,proto3" json:"user_age,omitempty"`
	UserSex              string   `protobuf:"bytes,5,opt,name=user_sex,json=userSex,proto3" json:"user_sex,omitempty"`
	ChannelName          string   `protobuf:"bytes,6,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	GuildName            string   `protobuf:"bytes,7,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Ttid                 string   `protobuf:"bytes,9,opt,name=ttid,proto3" json:"ttid,omitempty"`
	DisplayId            uint32   `protobuf:"varint,10,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	GuildId              uint32   `protobuf:"varint,11,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildShortId         uint32   `protobuf:"varint,12,opt,name=guild_short_id,json=guildShortId,proto3" json:"guild_short_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Qualification) Reset()         { *m = Qualification{} }
func (m *Qualification) String() string { return proto.CompactTextString(m) }
func (*Qualification) ProtoMessage()    {}
func (*Qualification) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{32}
}
func (m *Qualification) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Qualification.Unmarshal(m, b)
}
func (m *Qualification) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Qualification.Marshal(b, m, deterministic)
}
func (dst *Qualification) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Qualification.Merge(dst, src)
}
func (m *Qualification) XXX_Size() int {
	return xxx_messageInfo_Qualification.Size(m)
}
func (m *Qualification) XXX_DiscardUnknown() {
	xxx_messageInfo_Qualification.DiscardUnknown(m)
}

var xxx_messageInfo_Qualification proto.InternalMessageInfo

func (m *Qualification) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *Qualification) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Qualification) GetUserNickname() string {
	if m != nil {
		return m.UserNickname
	}
	return ""
}

func (m *Qualification) GetUserAge() uint32 {
	if m != nil {
		return m.UserAge
	}
	return 0
}

func (m *Qualification) GetUserSex() string {
	if m != nil {
		return m.UserSex
	}
	return ""
}

func (m *Qualification) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *Qualification) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *Qualification) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *Qualification) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *Qualification) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *Qualification) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *Qualification) GetGuildShortId() uint32 {
	if m != nil {
		return m.GuildShortId
	}
	return 0
}

type BatchAddUserToEntertainmentQualificationResp struct {
	FailQualifications   []*Qualification `protobuf:"bytes,1,rep,name=fail_qualifications,json=failQualifications,proto3" json:"fail_qualifications,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatchAddUserToEntertainmentQualificationResp) Reset() {
	*m = BatchAddUserToEntertainmentQualificationResp{}
}
func (m *BatchAddUserToEntertainmentQualificationResp) String() string {
	return proto.CompactTextString(m)
}
func (*BatchAddUserToEntertainmentQualificationResp) ProtoMessage() {}
func (*BatchAddUserToEntertainmentQualificationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{33}
}
func (m *BatchAddUserToEntertainmentQualificationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddUserToEntertainmentQualificationResp.Unmarshal(m, b)
}
func (m *BatchAddUserToEntertainmentQualificationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddUserToEntertainmentQualificationResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddUserToEntertainmentQualificationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddUserToEntertainmentQualificationResp.Merge(dst, src)
}
func (m *BatchAddUserToEntertainmentQualificationResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddUserToEntertainmentQualificationResp.Size(m)
}
func (m *BatchAddUserToEntertainmentQualificationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddUserToEntertainmentQualificationResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddUserToEntertainmentQualificationResp proto.InternalMessageInfo

func (m *BatchAddUserToEntertainmentQualificationResp) GetFailQualifications() []*Qualification {
	if m != nil {
		return m.FailQualifications
	}
	return nil
}

type IsUserHasEntertainmentQualificationReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsUserHasEntertainmentQualificationReq) Reset() {
	*m = IsUserHasEntertainmentQualificationReq{}
}
func (m *IsUserHasEntertainmentQualificationReq) String() string { return proto.CompactTextString(m) }
func (*IsUserHasEntertainmentQualificationReq) ProtoMessage()    {}
func (*IsUserHasEntertainmentQualificationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{34}
}
func (m *IsUserHasEntertainmentQualificationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsUserHasEntertainmentQualificationReq.Unmarshal(m, b)
}
func (m *IsUserHasEntertainmentQualificationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsUserHasEntertainmentQualificationReq.Marshal(b, m, deterministic)
}
func (dst *IsUserHasEntertainmentQualificationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsUserHasEntertainmentQualificationReq.Merge(dst, src)
}
func (m *IsUserHasEntertainmentQualificationReq) XXX_Size() int {
	return xxx_messageInfo_IsUserHasEntertainmentQualificationReq.Size(m)
}
func (m *IsUserHasEntertainmentQualificationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsUserHasEntertainmentQualificationReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsUserHasEntertainmentQualificationReq proto.InternalMessageInfo

func (m *IsUserHasEntertainmentQualificationReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *IsUserHasEntertainmentQualificationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type IsUserHasEntertainmentQualificationResp struct {
	Exist                bool     `protobuf:"varint,1,opt,name=exist,proto3" json:"exist,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsUserHasEntertainmentQualificationResp) Reset() {
	*m = IsUserHasEntertainmentQualificationResp{}
}
func (m *IsUserHasEntertainmentQualificationResp) String() string { return proto.CompactTextString(m) }
func (*IsUserHasEntertainmentQualificationResp) ProtoMessage()    {}
func (*IsUserHasEntertainmentQualificationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{35}
}
func (m *IsUserHasEntertainmentQualificationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsUserHasEntertainmentQualificationResp.Unmarshal(m, b)
}
func (m *IsUserHasEntertainmentQualificationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsUserHasEntertainmentQualificationResp.Marshal(b, m, deterministic)
}
func (dst *IsUserHasEntertainmentQualificationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsUserHasEntertainmentQualificationResp.Merge(dst, src)
}
func (m *IsUserHasEntertainmentQualificationResp) XXX_Size() int {
	return xxx_messageInfo_IsUserHasEntertainmentQualificationResp.Size(m)
}
func (m *IsUserHasEntertainmentQualificationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsUserHasEntertainmentQualificationResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsUserHasEntertainmentQualificationResp proto.InternalMessageInfo

func (m *IsUserHasEntertainmentQualificationResp) GetExist() bool {
	if m != nil {
		return m.Exist
	}
	return false
}

type AddMaskedGameConfigReq struct {
	Config               *MaskedGameConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AddMaskedGameConfigReq) Reset()         { *m = AddMaskedGameConfigReq{} }
func (m *AddMaskedGameConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddMaskedGameConfigReq) ProtoMessage()    {}
func (*AddMaskedGameConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{36}
}
func (m *AddMaskedGameConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMaskedGameConfigReq.Unmarshal(m, b)
}
func (m *AddMaskedGameConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMaskedGameConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddMaskedGameConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMaskedGameConfigReq.Merge(dst, src)
}
func (m *AddMaskedGameConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddMaskedGameConfigReq.Size(m)
}
func (m *AddMaskedGameConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMaskedGameConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddMaskedGameConfigReq proto.InternalMessageInfo

func (m *AddMaskedGameConfigReq) GetConfig() *MaskedGameConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type AddMaskedGameConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddMaskedGameConfigResp) Reset()         { *m = AddMaskedGameConfigResp{} }
func (m *AddMaskedGameConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddMaskedGameConfigResp) ProtoMessage()    {}
func (*AddMaskedGameConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{37}
}
func (m *AddMaskedGameConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMaskedGameConfigResp.Unmarshal(m, b)
}
func (m *AddMaskedGameConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMaskedGameConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddMaskedGameConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMaskedGameConfigResp.Merge(dst, src)
}
func (m *AddMaskedGameConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddMaskedGameConfigResp.Size(m)
}
func (m *AddMaskedGameConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMaskedGameConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddMaskedGameConfigResp proto.InternalMessageInfo

type MaskedGameConfig struct {
	BeginTime            uint32     `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32     `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ChipAvg              uint32     `protobuf:"varint,3,opt,name=chip_avg,json=chipAvg,proto3" json:"chip_avg,omitempty"`
	ChipCount            uint32     `protobuf:"varint,4,opt,name=chip_count,json=chipCount,proto3" json:"chip_count,omitempty"`
	WithDraw             uint32     `protobuf:"varint,5,opt,name=with_draw,json=withDraw,proto3" json:"with_draw,omitempty"`
	ActivityUrl          string     `protobuf:"bytes,6,opt,name=activity_url,json=activityUrl,proto3" json:"activity_url,omitempty"`
	DivideType           DivideType `protobuf:"varint,7,opt,name=divide_type,json=divideType,proto3,enum=masked_pk_svr.DivideType" json:"divide_type,omitempty"`
	DivideCount          uint32     `protobuf:"varint,8,opt,name=divide_count,json=divideCount,proto3" json:"divide_count,omitempty"`
	ReviveChip           uint32     `protobuf:"varint,9,opt,name=revive_chip,json=reviveChip,proto3" json:"revive_chip,omitempty"`
	ProvideChip          uint32     `protobuf:"varint,10,opt,name=provide_chip,json=provideChip,proto3" json:"provide_chip,omitempty"`
	ContinueMatch        uint32     `protobuf:"varint,11,opt,name=continue_match,json=continueMatch,proto3" json:"continue_match,omitempty"`
	GameId               uint32     `protobuf:"varint,12,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	DieOutCount          uint32     `protobuf:"varint,13,opt,name=die_out_count,json=dieOutCount,proto3" json:"die_out_count,omitempty"`
	AwardDivideUser      uint32     `protobuf:"varint,14,opt,name=award_divide_user,json=awardDivideUser,proto3" json:"award_divide_user,omitempty"`
	ReviveTbean          uint32     `protobuf:"varint,15,opt,name=revive_tbean,json=reviveTbean,proto3" json:"revive_tbean,omitempty"`
	CanBeModify          bool       `protobuf:"varint,16,opt,name=can_be_modify,json=canBeModify,proto3" json:"can_be_modify,omitempty"`
	UseBackpack          uint32     `protobuf:"varint,17,opt,name=use_backpack,json=useBackpack,proto3" json:"use_backpack,omitempty"`
	QuickKill            uint32     `protobuf:"varint,18,opt,name=quick_kill,json=quickKill,proto3" json:"quick_kill,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *MaskedGameConfig) Reset()         { *m = MaskedGameConfig{} }
func (m *MaskedGameConfig) String() string { return proto.CompactTextString(m) }
func (*MaskedGameConfig) ProtoMessage()    {}
func (*MaskedGameConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{38}
}
func (m *MaskedGameConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaskedGameConfig.Unmarshal(m, b)
}
func (m *MaskedGameConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaskedGameConfig.Marshal(b, m, deterministic)
}
func (dst *MaskedGameConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaskedGameConfig.Merge(dst, src)
}
func (m *MaskedGameConfig) XXX_Size() int {
	return xxx_messageInfo_MaskedGameConfig.Size(m)
}
func (m *MaskedGameConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_MaskedGameConfig.DiscardUnknown(m)
}

var xxx_messageInfo_MaskedGameConfig proto.InternalMessageInfo

func (m *MaskedGameConfig) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *MaskedGameConfig) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *MaskedGameConfig) GetChipAvg() uint32 {
	if m != nil {
		return m.ChipAvg
	}
	return 0
}

func (m *MaskedGameConfig) GetChipCount() uint32 {
	if m != nil {
		return m.ChipCount
	}
	return 0
}

func (m *MaskedGameConfig) GetWithDraw() uint32 {
	if m != nil {
		return m.WithDraw
	}
	return 0
}

func (m *MaskedGameConfig) GetActivityUrl() string {
	if m != nil {
		return m.ActivityUrl
	}
	return ""
}

func (m *MaskedGameConfig) GetDivideType() DivideType {
	if m != nil {
		return m.DivideType
	}
	return DivideType_DIVIDE_TYPE_DEFAULT
}

func (m *MaskedGameConfig) GetDivideCount() uint32 {
	if m != nil {
		return m.DivideCount
	}
	return 0
}

func (m *MaskedGameConfig) GetReviveChip() uint32 {
	if m != nil {
		return m.ReviveChip
	}
	return 0
}

func (m *MaskedGameConfig) GetProvideChip() uint32 {
	if m != nil {
		return m.ProvideChip
	}
	return 0
}

func (m *MaskedGameConfig) GetContinueMatch() uint32 {
	if m != nil {
		return m.ContinueMatch
	}
	return 0
}

func (m *MaskedGameConfig) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *MaskedGameConfig) GetDieOutCount() uint32 {
	if m != nil {
		return m.DieOutCount
	}
	return 0
}

func (m *MaskedGameConfig) GetAwardDivideUser() uint32 {
	if m != nil {
		return m.AwardDivideUser
	}
	return 0
}

func (m *MaskedGameConfig) GetReviveTbean() uint32 {
	if m != nil {
		return m.ReviveTbean
	}
	return 0
}

func (m *MaskedGameConfig) GetCanBeModify() bool {
	if m != nil {
		return m.CanBeModify
	}
	return false
}

func (m *MaskedGameConfig) GetUseBackpack() uint32 {
	if m != nil {
		return m.UseBackpack
	}
	return 0
}

func (m *MaskedGameConfig) GetQuickKill() uint32 {
	if m != nil {
		return m.QuickKill
	}
	return 0
}

type DelMaskedGameConfigReq struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelMaskedGameConfigReq) Reset()         { *m = DelMaskedGameConfigReq{} }
func (m *DelMaskedGameConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelMaskedGameConfigReq) ProtoMessage()    {}
func (*DelMaskedGameConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{39}
}
func (m *DelMaskedGameConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelMaskedGameConfigReq.Unmarshal(m, b)
}
func (m *DelMaskedGameConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelMaskedGameConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelMaskedGameConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelMaskedGameConfigReq.Merge(dst, src)
}
func (m *DelMaskedGameConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelMaskedGameConfigReq.Size(m)
}
func (m *DelMaskedGameConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelMaskedGameConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelMaskedGameConfigReq proto.InternalMessageInfo

func (m *DelMaskedGameConfigReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type DelMaskedGameConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelMaskedGameConfigResp) Reset()         { *m = DelMaskedGameConfigResp{} }
func (m *DelMaskedGameConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelMaskedGameConfigResp) ProtoMessage()    {}
func (*DelMaskedGameConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{40}
}
func (m *DelMaskedGameConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelMaskedGameConfigResp.Unmarshal(m, b)
}
func (m *DelMaskedGameConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelMaskedGameConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelMaskedGameConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelMaskedGameConfigResp.Merge(dst, src)
}
func (m *DelMaskedGameConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelMaskedGameConfigResp.Size(m)
}
func (m *DelMaskedGameConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelMaskedGameConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelMaskedGameConfigResp proto.InternalMessageInfo

type BatchAddChannelToWhiteListReq struct {
	Channel              []uint32 `protobuf:"varint,1,rep,packed,name=channel,proto3" json:"channel,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddChannelToWhiteListReq) Reset()         { *m = BatchAddChannelToWhiteListReq{} }
func (m *BatchAddChannelToWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddChannelToWhiteListReq) ProtoMessage()    {}
func (*BatchAddChannelToWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{41}
}
func (m *BatchAddChannelToWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddChannelToWhiteListReq.Unmarshal(m, b)
}
func (m *BatchAddChannelToWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddChannelToWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddChannelToWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddChannelToWhiteListReq.Merge(dst, src)
}
func (m *BatchAddChannelToWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddChannelToWhiteListReq.Size(m)
}
func (m *BatchAddChannelToWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddChannelToWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddChannelToWhiteListReq proto.InternalMessageInfo

func (m *BatchAddChannelToWhiteListReq) GetChannel() []uint32 {
	if m != nil {
		return m.Channel
	}
	return nil
}

type BatchAddChannelToWhiteListResp struct {
	FailDisplay          []uint32 `protobuf:"varint,1,rep,packed,name=fail_display,json=failDisplay,proto3" json:"fail_display,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddChannelToWhiteListResp) Reset()         { *m = BatchAddChannelToWhiteListResp{} }
func (m *BatchAddChannelToWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddChannelToWhiteListResp) ProtoMessage()    {}
func (*BatchAddChannelToWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{42}
}
func (m *BatchAddChannelToWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddChannelToWhiteListResp.Unmarshal(m, b)
}
func (m *BatchAddChannelToWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddChannelToWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddChannelToWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddChannelToWhiteListResp.Merge(dst, src)
}
func (m *BatchAddChannelToWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddChannelToWhiteListResp.Size(m)
}
func (m *BatchAddChannelToWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddChannelToWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddChannelToWhiteListResp proto.InternalMessageInfo

func (m *BatchAddChannelToWhiteListResp) GetFailDisplay() []uint32 {
	if m != nil {
		return m.FailDisplay
	}
	return nil
}

type BatchDelChannelToWhiteListReq struct {
	Channel              []uint32 `protobuf:"varint,1,rep,packed,name=channel,proto3" json:"channel,omitempty"`
	DeleteAll            uint32   `protobuf:"varint,2,opt,name=delete_all,json=deleteAll,proto3" json:"delete_all,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelChannelToWhiteListReq) Reset()         { *m = BatchDelChannelToWhiteListReq{} }
func (m *BatchDelChannelToWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*BatchDelChannelToWhiteListReq) ProtoMessage()    {}
func (*BatchDelChannelToWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{43}
}
func (m *BatchDelChannelToWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelChannelToWhiteListReq.Unmarshal(m, b)
}
func (m *BatchDelChannelToWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelChannelToWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *BatchDelChannelToWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelChannelToWhiteListReq.Merge(dst, src)
}
func (m *BatchDelChannelToWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_BatchDelChannelToWhiteListReq.Size(m)
}
func (m *BatchDelChannelToWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelChannelToWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelChannelToWhiteListReq proto.InternalMessageInfo

func (m *BatchDelChannelToWhiteListReq) GetChannel() []uint32 {
	if m != nil {
		return m.Channel
	}
	return nil
}

func (m *BatchDelChannelToWhiteListReq) GetDeleteAll() uint32 {
	if m != nil {
		return m.DeleteAll
	}
	return 0
}

type BatchDelChannelToWhiteListResp struct {
	FailDisplay          []uint32 `protobuf:"varint,1,rep,packed,name=fail_display,json=failDisplay,proto3" json:"fail_display,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelChannelToWhiteListResp) Reset()         { *m = BatchDelChannelToWhiteListResp{} }
func (m *BatchDelChannelToWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*BatchDelChannelToWhiteListResp) ProtoMessage()    {}
func (*BatchDelChannelToWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{44}
}
func (m *BatchDelChannelToWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelChannelToWhiteListResp.Unmarshal(m, b)
}
func (m *BatchDelChannelToWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelChannelToWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *BatchDelChannelToWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelChannelToWhiteListResp.Merge(dst, src)
}
func (m *BatchDelChannelToWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_BatchDelChannelToWhiteListResp.Size(m)
}
func (m *BatchDelChannelToWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelChannelToWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelChannelToWhiteListResp proto.InternalMessageInfo

func (m *BatchDelChannelToWhiteListResp) GetFailDisplay() []uint32 {
	if m != nil {
		return m.FailDisplay
	}
	return nil
}

type GetAllWhiteListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllWhiteListReq) Reset()         { *m = GetAllWhiteListReq{} }
func (m *GetAllWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*GetAllWhiteListReq) ProtoMessage()    {}
func (*GetAllWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{45}
}
func (m *GetAllWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllWhiteListReq.Unmarshal(m, b)
}
func (m *GetAllWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *GetAllWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllWhiteListReq.Merge(dst, src)
}
func (m *GetAllWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_GetAllWhiteListReq.Size(m)
}
func (m *GetAllWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllWhiteListReq proto.InternalMessageInfo

type GetAllWhiteListResp struct {
	List                 []*WhiteListConfig `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetAllWhiteListResp) Reset()         { *m = GetAllWhiteListResp{} }
func (m *GetAllWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*GetAllWhiteListResp) ProtoMessage()    {}
func (*GetAllWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{46}
}
func (m *GetAllWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllWhiteListResp.Unmarshal(m, b)
}
func (m *GetAllWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *GetAllWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllWhiteListResp.Merge(dst, src)
}
func (m *GetAllWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_GetAllWhiteListResp.Size(m)
}
func (m *GetAllWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllWhiteListResp proto.InternalMessageInfo

func (m *GetAllWhiteListResp) GetList() []*WhiteListConfig {
	if m != nil {
		return m.List
	}
	return nil
}

type WhiteListConfig struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName          string   `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	GuildName            string   `protobuf:"bytes,3,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,4,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	DisplayId            uint32   `protobuf:"varint,5,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WhiteListConfig) Reset()         { *m = WhiteListConfig{} }
func (m *WhiteListConfig) String() string { return proto.CompactTextString(m) }
func (*WhiteListConfig) ProtoMessage()    {}
func (*WhiteListConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{47}
}
func (m *WhiteListConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WhiteListConfig.Unmarshal(m, b)
}
func (m *WhiteListConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WhiteListConfig.Marshal(b, m, deterministic)
}
func (dst *WhiteListConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WhiteListConfig.Merge(dst, src)
}
func (m *WhiteListConfig) XXX_Size() int {
	return xxx_messageInfo_WhiteListConfig.Size(m)
}
func (m *WhiteListConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_WhiteListConfig.DiscardUnknown(m)
}

var xxx_messageInfo_WhiteListConfig proto.InternalMessageInfo

func (m *WhiteListConfig) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *WhiteListConfig) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *WhiteListConfig) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *WhiteListConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *WhiteListConfig) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

type BatchDelUserFromEntertainmentQualificationReq struct {
	Qualifications       []*Qualification `protobuf:"bytes,1,rep,name=qualifications,proto3" json:"qualifications,omitempty"`
	DeleteAll            uint32           `protobuf:"varint,2,opt,name=delete_all,json=deleteAll,proto3" json:"delete_all,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatchDelUserFromEntertainmentQualificationReq) Reset() {
	*m = BatchDelUserFromEntertainmentQualificationReq{}
}
func (m *BatchDelUserFromEntertainmentQualificationReq) String() string {
	return proto.CompactTextString(m)
}
func (*BatchDelUserFromEntertainmentQualificationReq) ProtoMessage() {}
func (*BatchDelUserFromEntertainmentQualificationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{48}
}
func (m *BatchDelUserFromEntertainmentQualificationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelUserFromEntertainmentQualificationReq.Unmarshal(m, b)
}
func (m *BatchDelUserFromEntertainmentQualificationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelUserFromEntertainmentQualificationReq.Marshal(b, m, deterministic)
}
func (dst *BatchDelUserFromEntertainmentQualificationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelUserFromEntertainmentQualificationReq.Merge(dst, src)
}
func (m *BatchDelUserFromEntertainmentQualificationReq) XXX_Size() int {
	return xxx_messageInfo_BatchDelUserFromEntertainmentQualificationReq.Size(m)
}
func (m *BatchDelUserFromEntertainmentQualificationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelUserFromEntertainmentQualificationReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelUserFromEntertainmentQualificationReq proto.InternalMessageInfo

func (m *BatchDelUserFromEntertainmentQualificationReq) GetQualifications() []*Qualification {
	if m != nil {
		return m.Qualifications
	}
	return nil
}

func (m *BatchDelUserFromEntertainmentQualificationReq) GetDeleteAll() uint32 {
	if m != nil {
		return m.DeleteAll
	}
	return 0
}

type BatchDelUserFromEntertainmentQualificationResp struct {
	FailQualifications   []*Qualification `protobuf:"bytes,1,rep,name=fail_qualifications,json=failQualifications,proto3" json:"fail_qualifications,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatchDelUserFromEntertainmentQualificationResp) Reset() {
	*m = BatchDelUserFromEntertainmentQualificationResp{}
}
func (m *BatchDelUserFromEntertainmentQualificationResp) String() string {
	return proto.CompactTextString(m)
}
func (*BatchDelUserFromEntertainmentQualificationResp) ProtoMessage() {}
func (*BatchDelUserFromEntertainmentQualificationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{49}
}
func (m *BatchDelUserFromEntertainmentQualificationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelUserFromEntertainmentQualificationResp.Unmarshal(m, b)
}
func (m *BatchDelUserFromEntertainmentQualificationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelUserFromEntertainmentQualificationResp.Marshal(b, m, deterministic)
}
func (dst *BatchDelUserFromEntertainmentQualificationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelUserFromEntertainmentQualificationResp.Merge(dst, src)
}
func (m *BatchDelUserFromEntertainmentQualificationResp) XXX_Size() int {
	return xxx_messageInfo_BatchDelUserFromEntertainmentQualificationResp.Size(m)
}
func (m *BatchDelUserFromEntertainmentQualificationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelUserFromEntertainmentQualificationResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelUserFromEntertainmentQualificationResp proto.InternalMessageInfo

func (m *BatchDelUserFromEntertainmentQualificationResp) GetFailQualifications() []*Qualification {
	if m != nil {
		return m.FailQualifications
	}
	return nil
}

type GetUserFromEntertainmentQualificationReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserFromEntertainmentQualificationReq) Reset() {
	*m = GetUserFromEntertainmentQualificationReq{}
}
func (m *GetUserFromEntertainmentQualificationReq) String() string { return proto.CompactTextString(m) }
func (*GetUserFromEntertainmentQualificationReq) ProtoMessage()    {}
func (*GetUserFromEntertainmentQualificationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{50}
}
func (m *GetUserFromEntertainmentQualificationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFromEntertainmentQualificationReq.Unmarshal(m, b)
}
func (m *GetUserFromEntertainmentQualificationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFromEntertainmentQualificationReq.Marshal(b, m, deterministic)
}
func (dst *GetUserFromEntertainmentQualificationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFromEntertainmentQualificationReq.Merge(dst, src)
}
func (m *GetUserFromEntertainmentQualificationReq) XXX_Size() int {
	return xxx_messageInfo_GetUserFromEntertainmentQualificationReq.Size(m)
}
func (m *GetUserFromEntertainmentQualificationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFromEntertainmentQualificationReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFromEntertainmentQualificationReq proto.InternalMessageInfo

func (m *GetUserFromEntertainmentQualificationReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetUserFromEntertainmentQualificationReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetUserFromEntertainmentQualificationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserFromEntertainmentQualificationResp struct {
	Qualifications       []*Qualification `protobuf:"bytes,1,rep,name=qualifications,proto3" json:"qualifications,omitempty"`
	SumCount             uint32           `protobuf:"varint,2,opt,name=sum_count,json=sumCount,proto3" json:"sum_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserFromEntertainmentQualificationResp) Reset() {
	*m = GetUserFromEntertainmentQualificationResp{}
}
func (m *GetUserFromEntertainmentQualificationResp) String() string { return proto.CompactTextString(m) }
func (*GetUserFromEntertainmentQualificationResp) ProtoMessage()    {}
func (*GetUserFromEntertainmentQualificationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{51}
}
func (m *GetUserFromEntertainmentQualificationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFromEntertainmentQualificationResp.Unmarshal(m, b)
}
func (m *GetUserFromEntertainmentQualificationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFromEntertainmentQualificationResp.Marshal(b, m, deterministic)
}
func (dst *GetUserFromEntertainmentQualificationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFromEntertainmentQualificationResp.Merge(dst, src)
}
func (m *GetUserFromEntertainmentQualificationResp) XXX_Size() int {
	return xxx_messageInfo_GetUserFromEntertainmentQualificationResp.Size(m)
}
func (m *GetUserFromEntertainmentQualificationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFromEntertainmentQualificationResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFromEntertainmentQualificationResp proto.InternalMessageInfo

func (m *GetUserFromEntertainmentQualificationResp) GetQualifications() []*Qualification {
	if m != nil {
		return m.Qualifications
	}
	return nil
}

func (m *GetUserFromEntertainmentQualificationResp) GetSumCount() uint32 {
	if m != nil {
		return m.SumCount
	}
	return 0
}

type GetChannelFromEntertainmentQualificationReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelFromEntertainmentQualificationReq) Reset() {
	*m = GetChannelFromEntertainmentQualificationReq{}
}
func (m *GetChannelFromEntertainmentQualificationReq) String() string {
	return proto.CompactTextString(m)
}
func (*GetChannelFromEntertainmentQualificationReq) ProtoMessage() {}
func (*GetChannelFromEntertainmentQualificationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{52}
}
func (m *GetChannelFromEntertainmentQualificationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelFromEntertainmentQualificationReq.Unmarshal(m, b)
}
func (m *GetChannelFromEntertainmentQualificationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelFromEntertainmentQualificationReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelFromEntertainmentQualificationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelFromEntertainmentQualificationReq.Merge(dst, src)
}
func (m *GetChannelFromEntertainmentQualificationReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelFromEntertainmentQualificationReq.Size(m)
}
func (m *GetChannelFromEntertainmentQualificationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelFromEntertainmentQualificationReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelFromEntertainmentQualificationReq proto.InternalMessageInfo

func (m *GetChannelFromEntertainmentQualificationReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetChannelFromEntertainmentQualificationReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetChannelFromEntertainmentQualificationReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelFromEntertainmentQualificationResp struct {
	Qualifications       []*Qualification `protobuf:"bytes,1,rep,name=qualifications,proto3" json:"qualifications,omitempty"`
	SumCount             uint32           `protobuf:"varint,2,opt,name=sum_count,json=sumCount,proto3" json:"sum_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetChannelFromEntertainmentQualificationResp) Reset() {
	*m = GetChannelFromEntertainmentQualificationResp{}
}
func (m *GetChannelFromEntertainmentQualificationResp) String() string {
	return proto.CompactTextString(m)
}
func (*GetChannelFromEntertainmentQualificationResp) ProtoMessage() {}
func (*GetChannelFromEntertainmentQualificationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{53}
}
func (m *GetChannelFromEntertainmentQualificationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelFromEntertainmentQualificationResp.Unmarshal(m, b)
}
func (m *GetChannelFromEntertainmentQualificationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelFromEntertainmentQualificationResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelFromEntertainmentQualificationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelFromEntertainmentQualificationResp.Merge(dst, src)
}
func (m *GetChannelFromEntertainmentQualificationResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelFromEntertainmentQualificationResp.Size(m)
}
func (m *GetChannelFromEntertainmentQualificationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelFromEntertainmentQualificationResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelFromEntertainmentQualificationResp proto.InternalMessageInfo

func (m *GetChannelFromEntertainmentQualificationResp) GetQualifications() []*Qualification {
	if m != nil {
		return m.Qualifications
	}
	return nil
}

func (m *GetChannelFromEntertainmentQualificationResp) GetSumCount() uint32 {
	if m != nil {
		return m.SumCount
	}
	return 0
}

type UpdateWhiteListConfigReq struct {
	UseWhiteList            uint32   `protobuf:"varint,1,opt,name=use_white_list,json=useWhiteList,proto3" json:"use_white_list,omitempty"`
	AllowMatchWithSameGuild uint32   `protobuf:"varint,2,opt,name=allow_match_with_same_guild,json=allowMatchWithSameGuild,proto3" json:"allow_match_with_same_guild,omitempty"`
	UsePrior                uint32   `protobuf:"varint,3,opt,name=use_prior,json=usePrior,proto3" json:"use_prior,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *UpdateWhiteListConfigReq) Reset()         { *m = UpdateWhiteListConfigReq{} }
func (m *UpdateWhiteListConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdateWhiteListConfigReq) ProtoMessage()    {}
func (*UpdateWhiteListConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{54}
}
func (m *UpdateWhiteListConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateWhiteListConfigReq.Unmarshal(m, b)
}
func (m *UpdateWhiteListConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateWhiteListConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdateWhiteListConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateWhiteListConfigReq.Merge(dst, src)
}
func (m *UpdateWhiteListConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdateWhiteListConfigReq.Size(m)
}
func (m *UpdateWhiteListConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateWhiteListConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateWhiteListConfigReq proto.InternalMessageInfo

func (m *UpdateWhiteListConfigReq) GetUseWhiteList() uint32 {
	if m != nil {
		return m.UseWhiteList
	}
	return 0
}

func (m *UpdateWhiteListConfigReq) GetAllowMatchWithSameGuild() uint32 {
	if m != nil {
		return m.AllowMatchWithSameGuild
	}
	return 0
}

func (m *UpdateWhiteListConfigReq) GetUsePrior() uint32 {
	if m != nil {
		return m.UsePrior
	}
	return 0
}

type UpdateWhiteListConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateWhiteListConfigResp) Reset()         { *m = UpdateWhiteListConfigResp{} }
func (m *UpdateWhiteListConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdateWhiteListConfigResp) ProtoMessage()    {}
func (*UpdateWhiteListConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{55}
}
func (m *UpdateWhiteListConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateWhiteListConfigResp.Unmarshal(m, b)
}
func (m *UpdateWhiteListConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateWhiteListConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdateWhiteListConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateWhiteListConfigResp.Merge(dst, src)
}
func (m *UpdateWhiteListConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdateWhiteListConfigResp.Size(m)
}
func (m *UpdateWhiteListConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateWhiteListConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateWhiteListConfigResp proto.InternalMessageInfo

type GetWhiteListConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWhiteListConfigReq) Reset()         { *m = GetWhiteListConfigReq{} }
func (m *GetWhiteListConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetWhiteListConfigReq) ProtoMessage()    {}
func (*GetWhiteListConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{56}
}
func (m *GetWhiteListConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWhiteListConfigReq.Unmarshal(m, b)
}
func (m *GetWhiteListConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWhiteListConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetWhiteListConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWhiteListConfigReq.Merge(dst, src)
}
func (m *GetWhiteListConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetWhiteListConfigReq.Size(m)
}
func (m *GetWhiteListConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWhiteListConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWhiteListConfigReq proto.InternalMessageInfo

type GetWhiteListConfigResp struct {
	UseWhiteList            uint32   `protobuf:"varint,1,opt,name=use_white_list,json=useWhiteList,proto3" json:"use_white_list,omitempty"`
	AllowMatchWithSameGuild uint32   `protobuf:"varint,2,opt,name=allow_match_with_same_guild,json=allowMatchWithSameGuild,proto3" json:"allow_match_with_same_guild,omitempty"`
	UsePrior                uint32   `protobuf:"varint,3,opt,name=use_prior,json=usePrior,proto3" json:"use_prior,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *GetWhiteListConfigResp) Reset()         { *m = GetWhiteListConfigResp{} }
func (m *GetWhiteListConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetWhiteListConfigResp) ProtoMessage()    {}
func (*GetWhiteListConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{57}
}
func (m *GetWhiteListConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWhiteListConfigResp.Unmarshal(m, b)
}
func (m *GetWhiteListConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWhiteListConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetWhiteListConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWhiteListConfigResp.Merge(dst, src)
}
func (m *GetWhiteListConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetWhiteListConfigResp.Size(m)
}
func (m *GetWhiteListConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWhiteListConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWhiteListConfigResp proto.InternalMessageInfo

func (m *GetWhiteListConfigResp) GetUseWhiteList() uint32 {
	if m != nil {
		return m.UseWhiteList
	}
	return 0
}

func (m *GetWhiteListConfigResp) GetAllowMatchWithSameGuild() uint32 {
	if m != nil {
		return m.AllowMatchWithSameGuild
	}
	return 0
}

func (m *GetWhiteListConfigResp) GetUsePrior() uint32 {
	if m != nil {
		return m.UsePrior
	}
	return 0
}

type SetMonthGiftValueReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Value                uint32   `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMonthGiftValueReq) Reset()         { *m = SetMonthGiftValueReq{} }
func (m *SetMonthGiftValueReq) String() string { return proto.CompactTextString(m) }
func (*SetMonthGiftValueReq) ProtoMessage()    {}
func (*SetMonthGiftValueReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{58}
}
func (m *SetMonthGiftValueReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMonthGiftValueReq.Unmarshal(m, b)
}
func (m *SetMonthGiftValueReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMonthGiftValueReq.Marshal(b, m, deterministic)
}
func (dst *SetMonthGiftValueReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMonthGiftValueReq.Merge(dst, src)
}
func (m *SetMonthGiftValueReq) XXX_Size() int {
	return xxx_messageInfo_SetMonthGiftValueReq.Size(m)
}
func (m *SetMonthGiftValueReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMonthGiftValueReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetMonthGiftValueReq proto.InternalMessageInfo

func (m *SetMonthGiftValueReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetMonthGiftValueReq) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

type SetMonthGiftValueResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMonthGiftValueResp) Reset()         { *m = SetMonthGiftValueResp{} }
func (m *SetMonthGiftValueResp) String() string { return proto.CompactTextString(m) }
func (*SetMonthGiftValueResp) ProtoMessage()    {}
func (*SetMonthGiftValueResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{59}
}
func (m *SetMonthGiftValueResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMonthGiftValueResp.Unmarshal(m, b)
}
func (m *SetMonthGiftValueResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMonthGiftValueResp.Marshal(b, m, deterministic)
}
func (dst *SetMonthGiftValueResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMonthGiftValueResp.Merge(dst, src)
}
func (m *SetMonthGiftValueResp) XXX_Size() int {
	return xxx_messageInfo_SetMonthGiftValueResp.Size(m)
}
func (m *SetMonthGiftValueResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMonthGiftValueResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetMonthGiftValueResp proto.InternalMessageInfo

type UpdateMaskedGameConfigReq struct {
	Config               *MaskedGameConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UpdateMaskedGameConfigReq) Reset()         { *m = UpdateMaskedGameConfigReq{} }
func (m *UpdateMaskedGameConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdateMaskedGameConfigReq) ProtoMessage()    {}
func (*UpdateMaskedGameConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{60}
}
func (m *UpdateMaskedGameConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateMaskedGameConfigReq.Unmarshal(m, b)
}
func (m *UpdateMaskedGameConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateMaskedGameConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdateMaskedGameConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateMaskedGameConfigReq.Merge(dst, src)
}
func (m *UpdateMaskedGameConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdateMaskedGameConfigReq.Size(m)
}
func (m *UpdateMaskedGameConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateMaskedGameConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateMaskedGameConfigReq proto.InternalMessageInfo

func (m *UpdateMaskedGameConfigReq) GetConfig() *MaskedGameConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type UpdateMaskedGameConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateMaskedGameConfigResp) Reset()         { *m = UpdateMaskedGameConfigResp{} }
func (m *UpdateMaskedGameConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdateMaskedGameConfigResp) ProtoMessage()    {}
func (*UpdateMaskedGameConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{61}
}
func (m *UpdateMaskedGameConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateMaskedGameConfigResp.Unmarshal(m, b)
}
func (m *UpdateMaskedGameConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateMaskedGameConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdateMaskedGameConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateMaskedGameConfigResp.Merge(dst, src)
}
func (m *UpdateMaskedGameConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdateMaskedGameConfigResp.Size(m)
}
func (m *UpdateMaskedGameConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateMaskedGameConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateMaskedGameConfigResp proto.InternalMessageInfo

type GetAllMaskedGameConfigReq struct {
	GetExpire            bool     `protobuf:"varint,1,opt,name=get_expire,json=getExpire,proto3" json:"get_expire,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllMaskedGameConfigReq) Reset()         { *m = GetAllMaskedGameConfigReq{} }
func (m *GetAllMaskedGameConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetAllMaskedGameConfigReq) ProtoMessage()    {}
func (*GetAllMaskedGameConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{62}
}
func (m *GetAllMaskedGameConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllMaskedGameConfigReq.Unmarshal(m, b)
}
func (m *GetAllMaskedGameConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllMaskedGameConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetAllMaskedGameConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllMaskedGameConfigReq.Merge(dst, src)
}
func (m *GetAllMaskedGameConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetAllMaskedGameConfigReq.Size(m)
}
func (m *GetAllMaskedGameConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllMaskedGameConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllMaskedGameConfigReq proto.InternalMessageInfo

func (m *GetAllMaskedGameConfigReq) GetGetExpire() bool {
	if m != nil {
		return m.GetExpire
	}
	return false
}

type GetAllMaskedGameConfigResp struct {
	Config               []*MaskedGameConfig `protobuf:"bytes,1,rep,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetAllMaskedGameConfigResp) Reset()         { *m = GetAllMaskedGameConfigResp{} }
func (m *GetAllMaskedGameConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetAllMaskedGameConfigResp) ProtoMessage()    {}
func (*GetAllMaskedGameConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{63}
}
func (m *GetAllMaskedGameConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllMaskedGameConfigResp.Unmarshal(m, b)
}
func (m *GetAllMaskedGameConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllMaskedGameConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetAllMaskedGameConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllMaskedGameConfigResp.Merge(dst, src)
}
func (m *GetAllMaskedGameConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetAllMaskedGameConfigResp.Size(m)
}
func (m *GetAllMaskedGameConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllMaskedGameConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllMaskedGameConfigResp proto.InternalMessageInfo

func (m *GetAllMaskedGameConfigResp) GetConfig() []*MaskedGameConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type GetChannelQualifiedAnchorListReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelQualifiedAnchorListReq) Reset()         { *m = GetChannelQualifiedAnchorListReq{} }
func (m *GetChannelQualifiedAnchorListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelQualifiedAnchorListReq) ProtoMessage()    {}
func (*GetChannelQualifiedAnchorListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{64}
}
func (m *GetChannelQualifiedAnchorListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelQualifiedAnchorListReq.Unmarshal(m, b)
}
func (m *GetChannelQualifiedAnchorListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelQualifiedAnchorListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelQualifiedAnchorListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelQualifiedAnchorListReq.Merge(dst, src)
}
func (m *GetChannelQualifiedAnchorListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelQualifiedAnchorListReq.Size(m)
}
func (m *GetChannelQualifiedAnchorListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelQualifiedAnchorListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelQualifiedAnchorListReq proto.InternalMessageInfo

func (m *GetChannelQualifiedAnchorListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelQualifiedAnchorListResp struct {
	AnchorUidList        []uint32 `protobuf:"varint,1,rep,packed,name=anchor_uid_list,json=anchorUidList,proto3" json:"anchor_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelQualifiedAnchorListResp) Reset()         { *m = GetChannelQualifiedAnchorListResp{} }
func (m *GetChannelQualifiedAnchorListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelQualifiedAnchorListResp) ProtoMessage()    {}
func (*GetChannelQualifiedAnchorListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{65}
}
func (m *GetChannelQualifiedAnchorListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelQualifiedAnchorListResp.Unmarshal(m, b)
}
func (m *GetChannelQualifiedAnchorListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelQualifiedAnchorListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelQualifiedAnchorListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelQualifiedAnchorListResp.Merge(dst, src)
}
func (m *GetChannelQualifiedAnchorListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelQualifiedAnchorListResp.Size(m)
}
func (m *GetChannelQualifiedAnchorListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelQualifiedAnchorListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelQualifiedAnchorListResp proto.InternalMessageInfo

func (m *GetChannelQualifiedAnchorListResp) GetAnchorUidList() []uint32 {
	if m != nil {
		return m.AnchorUidList
	}
	return nil
}

type GetLastMaskedGameConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLastMaskedGameConfigReq) Reset()         { *m = GetLastMaskedGameConfigReq{} }
func (m *GetLastMaskedGameConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetLastMaskedGameConfigReq) ProtoMessage()    {}
func (*GetLastMaskedGameConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{66}
}
func (m *GetLastMaskedGameConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastMaskedGameConfigReq.Unmarshal(m, b)
}
func (m *GetLastMaskedGameConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastMaskedGameConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetLastMaskedGameConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastMaskedGameConfigReq.Merge(dst, src)
}
func (m *GetLastMaskedGameConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetLastMaskedGameConfigReq.Size(m)
}
func (m *GetLastMaskedGameConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastMaskedGameConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastMaskedGameConfigReq proto.InternalMessageInfo

type GetLastMaskedGameConfigResp struct {
	Config               *MaskedGameConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetLastMaskedGameConfigResp) Reset()         { *m = GetLastMaskedGameConfigResp{} }
func (m *GetLastMaskedGameConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetLastMaskedGameConfigResp) ProtoMessage()    {}
func (*GetLastMaskedGameConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{67}
}
func (m *GetLastMaskedGameConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastMaskedGameConfigResp.Unmarshal(m, b)
}
func (m *GetLastMaskedGameConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastMaskedGameConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetLastMaskedGameConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastMaskedGameConfigResp.Merge(dst, src)
}
func (m *GetLastMaskedGameConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetLastMaskedGameConfigResp.Size(m)
}
func (m *GetLastMaskedGameConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastMaskedGameConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastMaskedGameConfigResp proto.InternalMessageInfo

func (m *GetLastMaskedGameConfigResp) GetConfig() *MaskedGameConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type TestPushQuickKillChangeReq struct {
	EventType            uint32   `protobuf:"varint,1,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"`
	TriggerChannelId     uint32   `protobuf:"varint,2,opt,name=trigger_channel_id,json=triggerChannelId,proto3" json:"trigger_channel_id,omitempty"`
	KillChannelId        uint32   `protobuf:"varint,3,opt,name=kill_channel_id,json=killChannelId,proto3" json:"kill_channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestPushQuickKillChangeReq) Reset()         { *m = TestPushQuickKillChangeReq{} }
func (m *TestPushQuickKillChangeReq) String() string { return proto.CompactTextString(m) }
func (*TestPushQuickKillChangeReq) ProtoMessage()    {}
func (*TestPushQuickKillChangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{68}
}
func (m *TestPushQuickKillChangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestPushQuickKillChangeReq.Unmarshal(m, b)
}
func (m *TestPushQuickKillChangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestPushQuickKillChangeReq.Marshal(b, m, deterministic)
}
func (dst *TestPushQuickKillChangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestPushQuickKillChangeReq.Merge(dst, src)
}
func (m *TestPushQuickKillChangeReq) XXX_Size() int {
	return xxx_messageInfo_TestPushQuickKillChangeReq.Size(m)
}
func (m *TestPushQuickKillChangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestPushQuickKillChangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestPushQuickKillChangeReq proto.InternalMessageInfo

func (m *TestPushQuickKillChangeReq) GetEventType() uint32 {
	if m != nil {
		return m.EventType
	}
	return 0
}

func (m *TestPushQuickKillChangeReq) GetTriggerChannelId() uint32 {
	if m != nil {
		return m.TriggerChannelId
	}
	return 0
}

func (m *TestPushQuickKillChangeReq) GetKillChannelId() uint32 {
	if m != nil {
		return m.KillChannelId
	}
	return 0
}

type TestPushQuickKillChangeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestPushQuickKillChangeResp) Reset()         { *m = TestPushQuickKillChangeResp{} }
func (m *TestPushQuickKillChangeResp) String() string { return proto.CompactTextString(m) }
func (*TestPushQuickKillChangeResp) ProtoMessage()    {}
func (*TestPushQuickKillChangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{69}
}
func (m *TestPushQuickKillChangeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestPushQuickKillChangeResp.Unmarshal(m, b)
}
func (m *TestPushQuickKillChangeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestPushQuickKillChangeResp.Marshal(b, m, deterministic)
}
func (dst *TestPushQuickKillChangeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestPushQuickKillChangeResp.Merge(dst, src)
}
func (m *TestPushQuickKillChangeResp) XXX_Size() int {
	return xxx_messageInfo_TestPushQuickKillChangeResp.Size(m)
}
func (m *TestPushQuickKillChangeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestPushQuickKillChangeResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestPushQuickKillChangeResp proto.InternalMessageInfo

type GodLikeTopUsers struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Rank                 uint32   `protobuf:"varint,2,opt,name=rank,proto3" json:"rank,omitempty"`
	Contribution         uint32   `protobuf:"varint,3,opt,name=contribution,proto3" json:"contribution,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GodLikeTopUsers) Reset()         { *m = GodLikeTopUsers{} }
func (m *GodLikeTopUsers) String() string { return proto.CompactTextString(m) }
func (*GodLikeTopUsers) ProtoMessage()    {}
func (*GodLikeTopUsers) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{70}
}
func (m *GodLikeTopUsers) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GodLikeTopUsers.Unmarshal(m, b)
}
func (m *GodLikeTopUsers) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GodLikeTopUsers.Marshal(b, m, deterministic)
}
func (dst *GodLikeTopUsers) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GodLikeTopUsers.Merge(dst, src)
}
func (m *GodLikeTopUsers) XXX_Size() int {
	return xxx_messageInfo_GodLikeTopUsers.Size(m)
}
func (m *GodLikeTopUsers) XXX_DiscardUnknown() {
	xxx_messageInfo_GodLikeTopUsers.DiscardUnknown(m)
}

var xxx_messageInfo_GodLikeTopUsers proto.InternalMessageInfo

func (m *GodLikeTopUsers) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GodLikeTopUsers) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *GodLikeTopUsers) GetContribution() uint32 {
	if m != nil {
		return m.Contribution
	}
	return 0
}

func (m *GodLikeTopUsers) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type NotifyGodLikeTopUsersReq struct {
	GameId               uint32             `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Users                []*GodLikeTopUsers `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
	ExpiredTime          uint32             `protobuf:"varint,3,opt,name=expired_time,json=expiredTime,proto3" json:"expired_time,omitempty"`
	ActivityEndTime      uint32             `protobuf:"varint,4,opt,name=activity_end_time,json=activityEndTime,proto3" json:"activity_end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *NotifyGodLikeTopUsersReq) Reset()         { *m = NotifyGodLikeTopUsersReq{} }
func (m *NotifyGodLikeTopUsersReq) String() string { return proto.CompactTextString(m) }
func (*NotifyGodLikeTopUsersReq) ProtoMessage()    {}
func (*NotifyGodLikeTopUsersReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{71}
}
func (m *NotifyGodLikeTopUsersReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyGodLikeTopUsersReq.Unmarshal(m, b)
}
func (m *NotifyGodLikeTopUsersReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyGodLikeTopUsersReq.Marshal(b, m, deterministic)
}
func (dst *NotifyGodLikeTopUsersReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyGodLikeTopUsersReq.Merge(dst, src)
}
func (m *NotifyGodLikeTopUsersReq) XXX_Size() int {
	return xxx_messageInfo_NotifyGodLikeTopUsersReq.Size(m)
}
func (m *NotifyGodLikeTopUsersReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyGodLikeTopUsersReq.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyGodLikeTopUsersReq proto.InternalMessageInfo

func (m *NotifyGodLikeTopUsersReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *NotifyGodLikeTopUsersReq) GetUsers() []*GodLikeTopUsers {
	if m != nil {
		return m.Users
	}
	return nil
}

func (m *NotifyGodLikeTopUsersReq) GetExpiredTime() uint32 {
	if m != nil {
		return m.ExpiredTime
	}
	return 0
}

func (m *NotifyGodLikeTopUsersReq) GetActivityEndTime() uint32 {
	if m != nil {
		return m.ActivityEndTime
	}
	return 0
}

type NotifyGodLikeTopUsersResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyGodLikeTopUsersResp) Reset()         { *m = NotifyGodLikeTopUsersResp{} }
func (m *NotifyGodLikeTopUsersResp) String() string { return proto.CompactTextString(m) }
func (*NotifyGodLikeTopUsersResp) ProtoMessage()    {}
func (*NotifyGodLikeTopUsersResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{72}
}
func (m *NotifyGodLikeTopUsersResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyGodLikeTopUsersResp.Unmarshal(m, b)
}
func (m *NotifyGodLikeTopUsersResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyGodLikeTopUsersResp.Marshal(b, m, deterministic)
}
func (dst *NotifyGodLikeTopUsersResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyGodLikeTopUsersResp.Merge(dst, src)
}
func (m *NotifyGodLikeTopUsersResp) XXX_Size() int {
	return xxx_messageInfo_NotifyGodLikeTopUsersResp.Size(m)
}
func (m *NotifyGodLikeTopUsersResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyGodLikeTopUsersResp.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyGodLikeTopUsersResp proto.InternalMessageInfo

// 蒙面pk榜单入口
type CheckMaskedPkRankEntryReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckMaskedPkRankEntryReq) Reset()         { *m = CheckMaskedPkRankEntryReq{} }
func (m *CheckMaskedPkRankEntryReq) String() string { return proto.CompactTextString(m) }
func (*CheckMaskedPkRankEntryReq) ProtoMessage()    {}
func (*CheckMaskedPkRankEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{73}
}
func (m *CheckMaskedPkRankEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckMaskedPkRankEntryReq.Unmarshal(m, b)
}
func (m *CheckMaskedPkRankEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckMaskedPkRankEntryReq.Marshal(b, m, deterministic)
}
func (dst *CheckMaskedPkRankEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckMaskedPkRankEntryReq.Merge(dst, src)
}
func (m *CheckMaskedPkRankEntryReq) XXX_Size() int {
	return xxx_messageInfo_CheckMaskedPkRankEntryReq.Size(m)
}
func (m *CheckMaskedPkRankEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckMaskedPkRankEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckMaskedPkRankEntryReq proto.InternalMessageInfo

type CheckMaskedPkRankEntryResp struct {
	EntryEnable          bool     `protobuf:"varint,1,opt,name=entry_enable,json=entryEnable,proto3" json:"entry_enable,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckMaskedPkRankEntryResp) Reset()         { *m = CheckMaskedPkRankEntryResp{} }
func (m *CheckMaskedPkRankEntryResp) String() string { return proto.CompactTextString(m) }
func (*CheckMaskedPkRankEntryResp) ProtoMessage()    {}
func (*CheckMaskedPkRankEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{74}
}
func (m *CheckMaskedPkRankEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckMaskedPkRankEntryResp.Unmarshal(m, b)
}
func (m *CheckMaskedPkRankEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckMaskedPkRankEntryResp.Marshal(b, m, deterministic)
}
func (dst *CheckMaskedPkRankEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckMaskedPkRankEntryResp.Merge(dst, src)
}
func (m *CheckMaskedPkRankEntryResp) XXX_Size() int {
	return xxx_messageInfo_CheckMaskedPkRankEntryResp.Size(m)
}
func (m *CheckMaskedPkRankEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckMaskedPkRankEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckMaskedPkRankEntryResp proto.InternalMessageInfo

func (m *CheckMaskedPkRankEntryResp) GetEntryEnable() bool {
	if m != nil {
		return m.EntryEnable
	}
	return false
}

type UserTaillightInfo struct {
	// 尾灯业务id
	BizId uint32 `protobuf:"varint,1,opt,name=biz_id,json=bizId,proto3" json:"biz_id,omitempty"`
	// 尾灯数量
	Num                  uint32   `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserTaillightInfo) Reset()         { *m = UserTaillightInfo{} }
func (m *UserTaillightInfo) String() string { return proto.CompactTextString(m) }
func (*UserTaillightInfo) ProtoMessage()    {}
func (*UserTaillightInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{75}
}
func (m *UserTaillightInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserTaillightInfo.Unmarshal(m, b)
}
func (m *UserTaillightInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserTaillightInfo.Marshal(b, m, deterministic)
}
func (dst *UserTaillightInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserTaillightInfo.Merge(dst, src)
}
func (m *UserTaillightInfo) XXX_Size() int {
	return xxx_messageInfo_UserTaillightInfo.Size(m)
}
func (m *UserTaillightInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserTaillightInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserTaillightInfo proto.InternalMessageInfo

func (m *UserTaillightInfo) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *UserTaillightInfo) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

type MaskedPKConsumeInfo struct {
	Uid                  uint32             `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string             `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Score                uint32             `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`
	Rich                 uint32             `protobuf:"varint,4,opt,name=rich,proto3" json:"rich,omitempty"`
	Charm                uint32             `protobuf:"varint,5,opt,name=charm,proto3" json:"charm,omitempty"`
	Nickname             string             `protobuf:"bytes,6,opt,name=nickname,proto3" json:"nickname,omitempty"`
	NobilityLevel        uint32             `protobuf:"varint,7,opt,name=nobility_level,json=nobilityLevel,proto3" json:"nobility_level,omitempty"`
	GroupFansLevel       uint32             `protobuf:"varint,8,opt,name=group_fans_level,json=groupFansLevel,proto3" json:"group_fans_level,omitempty"`
	ChannelMemLevel      uint32             `protobuf:"varint,9,opt,name=channel_mem_level,json=channelMemLevel,proto3" json:"channel_mem_level,omitempty"`
	GroupName            string             `protobuf:"bytes,10,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	Sex                  uint32             `protobuf:"varint,12,opt,name=sex,proto3" json:"sex,omitempty"`
	Rank                 uint32             `protobuf:"varint,13,opt,name=rank,proto3" json:"rank,omitempty"`
	DValue               uint32             `protobuf:"varint,14,opt,name=d_value,json=dValue,proto3" json:"d_value,omitempty"`
	PlatformLight        *UserTaillightInfo `protobuf:"bytes,15,opt,name=platform_light,json=platformLight,proto3" json:"platform_light,omitempty"`
	ChannelLight         *UserTaillightInfo `protobuf:"bytes,16,opt,name=channel_light,json=channelLight,proto3" json:"channel_light,omitempty"`
	SuperPlayerLevel     uint32             `protobuf:"varint,17,opt,name=super_player_level,json=superPlayerLevel,proto3" json:"super_player_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MaskedPKConsumeInfo) Reset()         { *m = MaskedPKConsumeInfo{} }
func (m *MaskedPKConsumeInfo) String() string { return proto.CompactTextString(m) }
func (*MaskedPKConsumeInfo) ProtoMessage()    {}
func (*MaskedPKConsumeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{76}
}
func (m *MaskedPKConsumeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaskedPKConsumeInfo.Unmarshal(m, b)
}
func (m *MaskedPKConsumeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaskedPKConsumeInfo.Marshal(b, m, deterministic)
}
func (dst *MaskedPKConsumeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaskedPKConsumeInfo.Merge(dst, src)
}
func (m *MaskedPKConsumeInfo) XXX_Size() int {
	return xxx_messageInfo_MaskedPKConsumeInfo.Size(m)
}
func (m *MaskedPKConsumeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MaskedPKConsumeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MaskedPKConsumeInfo proto.InternalMessageInfo

func (m *MaskedPKConsumeInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *MaskedPKConsumeInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetRich() uint32 {
	if m != nil {
		return m.Rich
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetCharm() uint32 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *MaskedPKConsumeInfo) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetGroupFansLevel() uint32 {
	if m != nil {
		return m.GroupFansLevel
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetChannelMemLevel() uint32 {
	if m != nil {
		return m.ChannelMemLevel
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *MaskedPKConsumeInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetDValue() uint32 {
	if m != nil {
		return m.DValue
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetPlatformLight() *UserTaillightInfo {
	if m != nil {
		return m.PlatformLight
	}
	return nil
}

func (m *MaskedPKConsumeInfo) GetChannelLight() *UserTaillightInfo {
	if m != nil {
		return m.ChannelLight
	}
	return nil
}

func (m *MaskedPKConsumeInfo) GetSuperPlayerLevel() uint32 {
	if m != nil {
		return m.SuperPlayerLevel
	}
	return 0
}

// 蒙面PK消费榜
type MaskedPkGetConsumeTopNReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BeginId              uint32   `protobuf:"varint,2,opt,name=begin_id,json=beginId,proto3" json:"begin_id,omitempty"`
	ReqCnt               uint32   `protobuf:"varint,3,opt,name=req_cnt,json=reqCnt,proto3" json:"req_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MaskedPkGetConsumeTopNReq) Reset()         { *m = MaskedPkGetConsumeTopNReq{} }
func (m *MaskedPkGetConsumeTopNReq) String() string { return proto.CompactTextString(m) }
func (*MaskedPkGetConsumeTopNReq) ProtoMessage()    {}
func (*MaskedPkGetConsumeTopNReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{77}
}
func (m *MaskedPkGetConsumeTopNReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaskedPkGetConsumeTopNReq.Unmarshal(m, b)
}
func (m *MaskedPkGetConsumeTopNReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaskedPkGetConsumeTopNReq.Marshal(b, m, deterministic)
}
func (dst *MaskedPkGetConsumeTopNReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaskedPkGetConsumeTopNReq.Merge(dst, src)
}
func (m *MaskedPkGetConsumeTopNReq) XXX_Size() int {
	return xxx_messageInfo_MaskedPkGetConsumeTopNReq.Size(m)
}
func (m *MaskedPkGetConsumeTopNReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MaskedPkGetConsumeTopNReq.DiscardUnknown(m)
}

var xxx_messageInfo_MaskedPkGetConsumeTopNReq proto.InternalMessageInfo

func (m *MaskedPkGetConsumeTopNReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MaskedPkGetConsumeTopNReq) GetBeginId() uint32 {
	if m != nil {
		return m.BeginId
	}
	return 0
}

func (m *MaskedPkGetConsumeTopNReq) GetReqCnt() uint32 {
	if m != nil {
		return m.ReqCnt
	}
	return 0
}

type MaskedPkGetConsumeTopNResp struct {
	ChannelId            uint32                 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BeginId              uint32                 `protobuf:"varint,2,opt,name=begin_id,json=beginId,proto3" json:"begin_id,omitempty"`
	MemberList           []*MaskedPKConsumeInfo `protobuf:"bytes,3,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	MyInfo               *MaskedPKConsumeInfo   `protobuf:"bytes,4,opt,name=my_info,json=myInfo,proto3" json:"my_info,omitempty"`
	ViewCnt              uint32                 `protobuf:"varint,5,opt,name=view_cnt,json=viewCnt,proto3" json:"view_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *MaskedPkGetConsumeTopNResp) Reset()         { *m = MaskedPkGetConsumeTopNResp{} }
func (m *MaskedPkGetConsumeTopNResp) String() string { return proto.CompactTextString(m) }
func (*MaskedPkGetConsumeTopNResp) ProtoMessage()    {}
func (*MaskedPkGetConsumeTopNResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{78}
}
func (m *MaskedPkGetConsumeTopNResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaskedPkGetConsumeTopNResp.Unmarshal(m, b)
}
func (m *MaskedPkGetConsumeTopNResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaskedPkGetConsumeTopNResp.Marshal(b, m, deterministic)
}
func (dst *MaskedPkGetConsumeTopNResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaskedPkGetConsumeTopNResp.Merge(dst, src)
}
func (m *MaskedPkGetConsumeTopNResp) XXX_Size() int {
	return xxx_messageInfo_MaskedPkGetConsumeTopNResp.Size(m)
}
func (m *MaskedPkGetConsumeTopNResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MaskedPkGetConsumeTopNResp.DiscardUnknown(m)
}

var xxx_messageInfo_MaskedPkGetConsumeTopNResp proto.InternalMessageInfo

func (m *MaskedPkGetConsumeTopNResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MaskedPkGetConsumeTopNResp) GetBeginId() uint32 {
	if m != nil {
		return m.BeginId
	}
	return 0
}

func (m *MaskedPkGetConsumeTopNResp) GetMemberList() []*MaskedPKConsumeInfo {
	if m != nil {
		return m.MemberList
	}
	return nil
}

func (m *MaskedPkGetConsumeTopNResp) GetMyInfo() *MaskedPKConsumeInfo {
	if m != nil {
		return m.MyInfo
	}
	return nil
}

func (m *MaskedPkGetConsumeTopNResp) GetViewCnt() uint32 {
	if m != nil {
		return m.ViewCnt
	}
	return 0
}

type PushGameBeginConfReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushGameBeginConfReq) Reset()         { *m = PushGameBeginConfReq{} }
func (m *PushGameBeginConfReq) String() string { return proto.CompactTextString(m) }
func (*PushGameBeginConfReq) ProtoMessage()    {}
func (*PushGameBeginConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{79}
}
func (m *PushGameBeginConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushGameBeginConfReq.Unmarshal(m, b)
}
func (m *PushGameBeginConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushGameBeginConfReq.Marshal(b, m, deterministic)
}
func (dst *PushGameBeginConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushGameBeginConfReq.Merge(dst, src)
}
func (m *PushGameBeginConfReq) XXX_Size() int {
	return xxx_messageInfo_PushGameBeginConfReq.Size(m)
}
func (m *PushGameBeginConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushGameBeginConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushGameBeginConfReq proto.InternalMessageInfo

func (m *PushGameBeginConfReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type PushGameBeginConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushGameBeginConfResp) Reset()         { *m = PushGameBeginConfResp{} }
func (m *PushGameBeginConfResp) String() string { return proto.CompactTextString(m) }
func (*PushGameBeginConfResp) ProtoMessage()    {}
func (*PushGameBeginConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_svr_7f96e23def430b21, []int{80}
}
func (m *PushGameBeginConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushGameBeginConfResp.Unmarshal(m, b)
}
func (m *PushGameBeginConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushGameBeginConfResp.Marshal(b, m, deterministic)
}
func (dst *PushGameBeginConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushGameBeginConfResp.Merge(dst, src)
}
func (m *PushGameBeginConfResp) XXX_Size() int {
	return xxx_messageInfo_PushGameBeginConfResp.Size(m)
}
func (m *PushGameBeginConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushGameBeginConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_PushGameBeginConfResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*UserPrivilege)(nil), "masked_pk_svr.UserPrivilege")
	proto.RegisterType((*UserProfile)(nil), "masked_pk_svr.UserProfile")
	proto.RegisterType((*ChannelMaskedPKConf)(nil), "masked_pk_svr.ChannelMaskedPKConf")
	proto.RegisterType((*ChannelMaskedPKStatus)(nil), "masked_pk_svr.ChannelMaskedPKStatus")
	proto.RegisterType((*ChannelMaskedPKRankMem)(nil), "masked_pk_svr.ChannelMaskedPKRankMem")
	proto.RegisterType((*ChannelMaskedPKInfo)(nil), "masked_pk_svr.ChannelMaskedPKInfo")
	proto.RegisterType((*QuickKillInfo)(nil), "masked_pk_svr.QuickKillInfo")
	proto.RegisterType((*PeakPkInfo)(nil), "masked_pk_svr.PeakPkInfo")
	proto.RegisterType((*ChannelMaskedPKBattle)(nil), "masked_pk_svr.ChannelMaskedPKBattle")
	proto.RegisterType((*ChannelMaskedPKRevive)(nil), "masked_pk_svr.ChannelMaskedPKRevive")
	proto.RegisterType((*GetChannelMaskedPKInfoReq)(nil), "masked_pk_svr.GetChannelMaskedPKInfoReq")
	proto.RegisterType((*GetChannelMaskedPKInfoResp)(nil), "masked_pk_svr.GetChannelMaskedPKInfoResp")
	proto.RegisterType((*StartChannelMaskedPKReq)(nil), "masked_pk_svr.StartChannelMaskedPKReq")
	proto.RegisterType((*StartChannelMaskedPKResp)(nil), "masked_pk_svr.StartChannelMaskedPKResp")
	proto.RegisterType((*GiveUpChannelMaskedPKReq)(nil), "masked_pk_svr.GiveUpChannelMaskedPKReq")
	proto.RegisterType((*GiveUpChannelMaskedPKResp)(nil), "masked_pk_svr.GiveUpChannelMaskedPKResp")
	proto.RegisterType((*CancelChannelMaskedPKReq)(nil), "masked_pk_svr.CancelChannelMaskedPKReq")
	proto.RegisterType((*CancelChannelMaskedPKResp)(nil), "masked_pk_svr.CancelChannelMaskedPKResp")
	proto.RegisterType((*GetChannelMaskedPKStatusReq)(nil), "masked_pk_svr.GetChannelMaskedPKStatusReq")
	proto.RegisterType((*GetChannelMaskedPKStatusResp)(nil), "masked_pk_svr.GetChannelMaskedPKStatusResp")
	proto.RegisterType((*BatchGetChannelMaskedPKStatusReq)(nil), "masked_pk_svr.BatchGetChannelMaskedPKStatusReq")
	proto.RegisterType((*BatchGetChannelMaskedPKStatusResp)(nil), "masked_pk_svr.BatchGetChannelMaskedPKStatusResp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "masked_pk_svr.BatchGetChannelMaskedPKStatusResp.ChannelStatusMapEntry")
	proto.RegisterType((*GetChannelMaskedPKCurrConfWithUserReq)(nil), "masked_pk_svr.GetChannelMaskedPKCurrConfWithUserReq")
	proto.RegisterType((*GetChannelMaskedPKCurrConfWithUserResp)(nil), "masked_pk_svr.GetChannelMaskedPKCurrConfWithUserResp")
	proto.RegisterType((*GetChannelMaskedPKCurrConfReq)(nil), "masked_pk_svr.GetChannelMaskedPKCurrConfReq")
	proto.RegisterType((*GetChannelMaskedPKCurrConfResp)(nil), "masked_pk_svr.GetChannelMaskedPKCurrConfResp")
	proto.RegisterType((*ChannelMaskedPKAnchorRankMem)(nil), "masked_pk_svr.ChannelMaskedPKAnchorRankMem")
	proto.RegisterType((*GetChannelMaskedPKAnchorRankReq)(nil), "masked_pk_svr.GetChannelMaskedPKAnchorRankReq")
	proto.RegisterType((*GetChannelMaskedPKAnchorRankResp)(nil), "masked_pk_svr.GetChannelMaskedPKAnchorRankResp")
	proto.RegisterType((*GetChannelSinglePKAnchorRankReq)(nil), "masked_pk_svr.GetChannelSinglePKAnchorRankReq")
	proto.RegisterType((*GetChannelSinglePKAnchorRankResp)(nil), "masked_pk_svr.GetChannelSinglePKAnchorRankResp")
	proto.RegisterType((*BatchAddUserToEntertainmentQualificationReq)(nil), "masked_pk_svr.BatchAddUserToEntertainmentQualificationReq")
	proto.RegisterType((*Qualification)(nil), "masked_pk_svr.Qualification")
	proto.RegisterType((*BatchAddUserToEntertainmentQualificationResp)(nil), "masked_pk_svr.BatchAddUserToEntertainmentQualificationResp")
	proto.RegisterType((*IsUserHasEntertainmentQualificationReq)(nil), "masked_pk_svr.IsUserHasEntertainmentQualificationReq")
	proto.RegisterType((*IsUserHasEntertainmentQualificationResp)(nil), "masked_pk_svr.IsUserHasEntertainmentQualificationResp")
	proto.RegisterType((*AddMaskedGameConfigReq)(nil), "masked_pk_svr.AddMaskedGameConfigReq")
	proto.RegisterType((*AddMaskedGameConfigResp)(nil), "masked_pk_svr.AddMaskedGameConfigResp")
	proto.RegisterType((*MaskedGameConfig)(nil), "masked_pk_svr.MaskedGameConfig")
	proto.RegisterType((*DelMaskedGameConfigReq)(nil), "masked_pk_svr.DelMaskedGameConfigReq")
	proto.RegisterType((*DelMaskedGameConfigResp)(nil), "masked_pk_svr.DelMaskedGameConfigResp")
	proto.RegisterType((*BatchAddChannelToWhiteListReq)(nil), "masked_pk_svr.BatchAddChannelToWhiteListReq")
	proto.RegisterType((*BatchAddChannelToWhiteListResp)(nil), "masked_pk_svr.BatchAddChannelToWhiteListResp")
	proto.RegisterType((*BatchDelChannelToWhiteListReq)(nil), "masked_pk_svr.BatchDelChannelToWhiteListReq")
	proto.RegisterType((*BatchDelChannelToWhiteListResp)(nil), "masked_pk_svr.BatchDelChannelToWhiteListResp")
	proto.RegisterType((*GetAllWhiteListReq)(nil), "masked_pk_svr.GetAllWhiteListReq")
	proto.RegisterType((*GetAllWhiteListResp)(nil), "masked_pk_svr.GetAllWhiteListResp")
	proto.RegisterType((*WhiteListConfig)(nil), "masked_pk_svr.WhiteListConfig")
	proto.RegisterType((*BatchDelUserFromEntertainmentQualificationReq)(nil), "masked_pk_svr.BatchDelUserFromEntertainmentQualificationReq")
	proto.RegisterType((*BatchDelUserFromEntertainmentQualificationResp)(nil), "masked_pk_svr.BatchDelUserFromEntertainmentQualificationResp")
	proto.RegisterType((*GetUserFromEntertainmentQualificationReq)(nil), "masked_pk_svr.GetUserFromEntertainmentQualificationReq")
	proto.RegisterType((*GetUserFromEntertainmentQualificationResp)(nil), "masked_pk_svr.GetUserFromEntertainmentQualificationResp")
	proto.RegisterType((*GetChannelFromEntertainmentQualificationReq)(nil), "masked_pk_svr.GetChannelFromEntertainmentQualificationReq")
	proto.RegisterType((*GetChannelFromEntertainmentQualificationResp)(nil), "masked_pk_svr.GetChannelFromEntertainmentQualificationResp")
	proto.RegisterType((*UpdateWhiteListConfigReq)(nil), "masked_pk_svr.UpdateWhiteListConfigReq")
	proto.RegisterType((*UpdateWhiteListConfigResp)(nil), "masked_pk_svr.UpdateWhiteListConfigResp")
	proto.RegisterType((*GetWhiteListConfigReq)(nil), "masked_pk_svr.GetWhiteListConfigReq")
	proto.RegisterType((*GetWhiteListConfigResp)(nil), "masked_pk_svr.GetWhiteListConfigResp")
	proto.RegisterType((*SetMonthGiftValueReq)(nil), "masked_pk_svr.SetMonthGiftValueReq")
	proto.RegisterType((*SetMonthGiftValueResp)(nil), "masked_pk_svr.SetMonthGiftValueResp")
	proto.RegisterType((*UpdateMaskedGameConfigReq)(nil), "masked_pk_svr.UpdateMaskedGameConfigReq")
	proto.RegisterType((*UpdateMaskedGameConfigResp)(nil), "masked_pk_svr.UpdateMaskedGameConfigResp")
	proto.RegisterType((*GetAllMaskedGameConfigReq)(nil), "masked_pk_svr.GetAllMaskedGameConfigReq")
	proto.RegisterType((*GetAllMaskedGameConfigResp)(nil), "masked_pk_svr.GetAllMaskedGameConfigResp")
	proto.RegisterType((*GetChannelQualifiedAnchorListReq)(nil), "masked_pk_svr.GetChannelQualifiedAnchorListReq")
	proto.RegisterType((*GetChannelQualifiedAnchorListResp)(nil), "masked_pk_svr.GetChannelQualifiedAnchorListResp")
	proto.RegisterType((*GetLastMaskedGameConfigReq)(nil), "masked_pk_svr.GetLastMaskedGameConfigReq")
	proto.RegisterType((*GetLastMaskedGameConfigResp)(nil), "masked_pk_svr.GetLastMaskedGameConfigResp")
	proto.RegisterType((*TestPushQuickKillChangeReq)(nil), "masked_pk_svr.TestPushQuickKillChangeReq")
	proto.RegisterType((*TestPushQuickKillChangeResp)(nil), "masked_pk_svr.TestPushQuickKillChangeResp")
	proto.RegisterType((*GodLikeTopUsers)(nil), "masked_pk_svr.GodLikeTopUsers")
	proto.RegisterType((*NotifyGodLikeTopUsersReq)(nil), "masked_pk_svr.NotifyGodLikeTopUsersReq")
	proto.RegisterType((*NotifyGodLikeTopUsersResp)(nil), "masked_pk_svr.NotifyGodLikeTopUsersResp")
	proto.RegisterType((*CheckMaskedPkRankEntryReq)(nil), "masked_pk_svr.CheckMaskedPkRankEntryReq")
	proto.RegisterType((*CheckMaskedPkRankEntryResp)(nil), "masked_pk_svr.CheckMaskedPkRankEntryResp")
	proto.RegisterType((*UserTaillightInfo)(nil), "masked_pk_svr.UserTaillightInfo")
	proto.RegisterType((*MaskedPKConsumeInfo)(nil), "masked_pk_svr.MaskedPKConsumeInfo")
	proto.RegisterType((*MaskedPkGetConsumeTopNReq)(nil), "masked_pk_svr.MaskedPkGetConsumeTopNReq")
	proto.RegisterType((*MaskedPkGetConsumeTopNResp)(nil), "masked_pk_svr.MaskedPkGetConsumeTopNResp")
	proto.RegisterType((*PushGameBeginConfReq)(nil), "masked_pk_svr.PushGameBeginConfReq")
	proto.RegisterType((*PushGameBeginConfResp)(nil), "masked_pk_svr.PushGameBeginConfResp")
	proto.RegisterEnum("masked_pk_svr.DivideType", DivideType_name, DivideType_value)
	proto.RegisterEnum("masked_pk_svr.ChannelMaskedPKConf_ChipRole", ChannelMaskedPKConf_ChipRole_name, ChannelMaskedPKConf_ChipRole_value)
	proto.RegisterEnum("masked_pk_svr.ChannelMaskedPKStatus_Status", ChannelMaskedPKStatus_Status_name, ChannelMaskedPKStatus_Status_value)
	proto.RegisterEnum("masked_pk_svr.ChannelMaskedPKBattle_SubPhrase", ChannelMaskedPKBattle_SubPhrase_name, ChannelMaskedPKBattle_SubPhrase_value)
	proto.RegisterEnum("masked_pk_svr.TestPushQuickKillChangeReq_EventType", TestPushQuickKillChangeReq_EventType_name, TestPushQuickKillChangeReq_EventType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MaskedPKSvrClient is the client API for MaskedPKSvr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MaskedPKSvrClient interface {
	StartChannelMaskedPK(ctx context.Context, in *StartChannelMaskedPKReq, opts ...grpc.CallOption) (*StartChannelMaskedPKResp, error)
	GiveUpChannelMaskedPK(ctx context.Context, in *GiveUpChannelMaskedPKReq, opts ...grpc.CallOption) (*GiveUpChannelMaskedPKResp, error)
	CancelChannelMaskedPK(ctx context.Context, in *CancelChannelMaskedPKReq, opts ...grpc.CallOption) (*CancelChannelMaskedPKResp, error)
	GetChannelMaskedPKCurrConfWithUser(ctx context.Context, in *GetChannelMaskedPKCurrConfWithUserReq, opts ...grpc.CallOption) (*GetChannelMaskedPKCurrConfWithUserResp, error)
	GetChannelMaskedPKCurrConf(ctx context.Context, in *GetChannelMaskedPKCurrConfReq, opts ...grpc.CallOption) (*GetChannelMaskedPKCurrConfResp, error)
	GetChannelMaskedPKInfo(ctx context.Context, in *GetChannelMaskedPKInfoReq, opts ...grpc.CallOption) (*GetChannelMaskedPKInfoResp, error)
	GetChannelMaskedPKAnchorRank(ctx context.Context, in *GetChannelMaskedPKAnchorRankReq, opts ...grpc.CallOption) (*GetChannelMaskedPKAnchorRankResp, error)
	GetChannelSinglePKAnchorRank(ctx context.Context, in *GetChannelSinglePKAnchorRankReq, opts ...grpc.CallOption) (*GetChannelSinglePKAnchorRankResp, error)
	GetChannelMaskedPKStatus(ctx context.Context, in *GetChannelMaskedPKStatusReq, opts ...grpc.CallOption) (*GetChannelMaskedPKStatusResp, error)
	BatchGetChannelMaskedPKStatus(ctx context.Context, in *BatchGetChannelMaskedPKStatusReq, opts ...grpc.CallOption) (*BatchGetChannelMaskedPKStatusResp, error)
	// 为（房间 - 用户）添加参赛资格，房间和用户都填，需要查询并确认guildId
	BatchAddUserToEntertainmentQualification(ctx context.Context, in *BatchAddUserToEntertainmentQualificationReq, opts ...grpc.CallOption) (*BatchAddUserToEntertainmentQualificationResp, error)
	// 删除（房间 - 用户）的参赛资格
	BatchDelUserFromEntertainmentQualification(ctx context.Context, in *BatchDelUserFromEntertainmentQualificationReq, opts ...grpc.CallOption) (*BatchDelUserFromEntertainmentQualificationResp, error)
	// 获取参赛资格名单
	GetUserFromEntertainmentQualification(ctx context.Context, in *GetUserFromEntertainmentQualificationReq, opts ...grpc.CallOption) (*GetUserFromEntertainmentQualificationResp, error)
	// 获取有参赛资格的房间列表
	GetChannelFromEntertainmentQualification(ctx context.Context, in *GetChannelFromEntertainmentQualificationReq, opts ...grpc.CallOption) (*GetChannelFromEntertainmentQualificationResp, error)
	// （房间 - 用户）是否具有参赛资格
	IsUserHasEntertainmentQualification(ctx context.Context, in *IsUserHasEntertainmentQualificationReq, opts ...grpc.CallOption) (*IsUserHasEntertainmentQualificationResp, error)
	// 添加蒙面pk场次配置
	AddMaskedGameConfig(ctx context.Context, in *AddMaskedGameConfigReq, opts ...grpc.CallOption) (*AddMaskedGameConfigResp, error)
	// 删除蒙面pk场次配置
	DelMaskedGameConfig(ctx context.Context, in *DelMaskedGameConfigReq, opts ...grpc.CallOption) (*DelMaskedGameConfigResp, error)
	// 更新对应gameId的蒙面pk场次配置
	UpdateMaskedGameConfig(ctx context.Context, in *UpdateMaskedGameConfigReq, opts ...grpc.CallOption) (*UpdateMaskedGameConfigResp, error)
	// 获得所有蒙面pk场次配置
	GetAllMaskedGameConfig(ctx context.Context, in *GetAllMaskedGameConfigReq, opts ...grpc.CallOption) (*GetAllMaskedGameConfigResp, error)
	// 获取上一场PK的场次配置
	GetLastMaskedGameConfig(ctx context.Context, in *GetLastMaskedGameConfigReq, opts ...grpc.CallOption) (*GetLastMaskedGameConfigResp, error)
	// 添加房间到白名单
	BatchAddChannelToWhiteList(ctx context.Context, in *BatchAddChannelToWhiteListReq, opts ...grpc.CallOption) (*BatchAddChannelToWhiteListResp, error)
	// 删除房间白名单
	BatchDelChannelToWhiteList(ctx context.Context, in *BatchDelChannelToWhiteListReq, opts ...grpc.CallOption) (*BatchDelChannelToWhiteListResp, error)
	// 获得所有白名单内房间
	GetAllWhiteList(ctx context.Context, in *GetAllWhiteListReq, opts ...grpc.CallOption) (*GetAllWhiteListResp, error)
	// 更新白名单配置（是否允许同工会匹配，是否使用白名单），全局配置所以只有更新接口
	UpdateWhiteListConfig(ctx context.Context, in *UpdateWhiteListConfigReq, opts ...grpc.CallOption) (*UpdateWhiteListConfigResp, error)
	// 获取白名单配置（是否允许同工会匹配，是否使用白名单）
	GetWhiteListConfig(ctx context.Context, in *GetWhiteListConfigReq, opts ...grpc.CallOption) (*GetWhiteListConfigResp, error)
	// 测试用接口
	SetMonthGiftValue(ctx context.Context, in *SetMonthGiftValueReq, opts ...grpc.CallOption) (*SetMonthGiftValueResp, error)
	// 获取房间有资格的主播列表
	GetChannelQualifiedAnchorList(ctx context.Context, in *GetChannelQualifiedAnchorListReq, opts ...grpc.CallOption) (*GetChannelQualifiedAnchorListResp, error)
	// only for test
	TestPushQuickKillChange(ctx context.Context, in *TestPushQuickKillChangeReq, opts ...grpc.CallOption) (*TestPushQuickKillChangeResp, error)
	// 通知神仙局前三用户
	NotifyGodLikeTopUsers(ctx context.Context, in *NotifyGodLikeTopUsersReq, opts ...grpc.CallOption) (*NotifyGodLikeTopUsersResp, error)
	// 蒙面PK房间消费榜入口是否开放
	CheckMaskedPkRankEntry(ctx context.Context, in *CheckMaskedPkRankEntryReq, opts ...grpc.CallOption) (*CheckMaskedPkRankEntryResp, error)
	// 蒙面PK房间消费榜单
	MaskedPkGetConsumeTopN(ctx context.Context, in *MaskedPkGetConsumeTopNReq, opts ...grpc.CallOption) (*MaskedPkGetConsumeTopNResp, error)
	// 主动推送蒙面PK配置
	PushGameBeginConf(ctx context.Context, in *PushGameBeginConfReq, opts ...grpc.CallOption) (*PushGameBeginConfResp, error)
}

type maskedPKSvrClient struct {
	cc *grpc.ClientConn
}

func NewMaskedPKSvrClient(cc *grpc.ClientConn) MaskedPKSvrClient {
	return &maskedPKSvrClient{cc}
}

func (c *maskedPKSvrClient) StartChannelMaskedPK(ctx context.Context, in *StartChannelMaskedPKReq, opts ...grpc.CallOption) (*StartChannelMaskedPKResp, error) {
	out := new(StartChannelMaskedPKResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/StartChannelMaskedPK", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) GiveUpChannelMaskedPK(ctx context.Context, in *GiveUpChannelMaskedPKReq, opts ...grpc.CallOption) (*GiveUpChannelMaskedPKResp, error) {
	out := new(GiveUpChannelMaskedPKResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/GiveUpChannelMaskedPK", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) CancelChannelMaskedPK(ctx context.Context, in *CancelChannelMaskedPKReq, opts ...grpc.CallOption) (*CancelChannelMaskedPKResp, error) {
	out := new(CancelChannelMaskedPKResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/CancelChannelMaskedPK", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) GetChannelMaskedPKCurrConfWithUser(ctx context.Context, in *GetChannelMaskedPKCurrConfWithUserReq, opts ...grpc.CallOption) (*GetChannelMaskedPKCurrConfWithUserResp, error) {
	out := new(GetChannelMaskedPKCurrConfWithUserResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/GetChannelMaskedPKCurrConfWithUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) GetChannelMaskedPKCurrConf(ctx context.Context, in *GetChannelMaskedPKCurrConfReq, opts ...grpc.CallOption) (*GetChannelMaskedPKCurrConfResp, error) {
	out := new(GetChannelMaskedPKCurrConfResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/GetChannelMaskedPKCurrConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) GetChannelMaskedPKInfo(ctx context.Context, in *GetChannelMaskedPKInfoReq, opts ...grpc.CallOption) (*GetChannelMaskedPKInfoResp, error) {
	out := new(GetChannelMaskedPKInfoResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/GetChannelMaskedPKInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) GetChannelMaskedPKAnchorRank(ctx context.Context, in *GetChannelMaskedPKAnchorRankReq, opts ...grpc.CallOption) (*GetChannelMaskedPKAnchorRankResp, error) {
	out := new(GetChannelMaskedPKAnchorRankResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/GetChannelMaskedPKAnchorRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) GetChannelSinglePKAnchorRank(ctx context.Context, in *GetChannelSinglePKAnchorRankReq, opts ...grpc.CallOption) (*GetChannelSinglePKAnchorRankResp, error) {
	out := new(GetChannelSinglePKAnchorRankResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/GetChannelSinglePKAnchorRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) GetChannelMaskedPKStatus(ctx context.Context, in *GetChannelMaskedPKStatusReq, opts ...grpc.CallOption) (*GetChannelMaskedPKStatusResp, error) {
	out := new(GetChannelMaskedPKStatusResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/GetChannelMaskedPKStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) BatchGetChannelMaskedPKStatus(ctx context.Context, in *BatchGetChannelMaskedPKStatusReq, opts ...grpc.CallOption) (*BatchGetChannelMaskedPKStatusResp, error) {
	out := new(BatchGetChannelMaskedPKStatusResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/BatchGetChannelMaskedPKStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) BatchAddUserToEntertainmentQualification(ctx context.Context, in *BatchAddUserToEntertainmentQualificationReq, opts ...grpc.CallOption) (*BatchAddUserToEntertainmentQualificationResp, error) {
	out := new(BatchAddUserToEntertainmentQualificationResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/BatchAddUserToEntertainmentQualification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) BatchDelUserFromEntertainmentQualification(ctx context.Context, in *BatchDelUserFromEntertainmentQualificationReq, opts ...grpc.CallOption) (*BatchDelUserFromEntertainmentQualificationResp, error) {
	out := new(BatchDelUserFromEntertainmentQualificationResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/BatchDelUserFromEntertainmentQualification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) GetUserFromEntertainmentQualification(ctx context.Context, in *GetUserFromEntertainmentQualificationReq, opts ...grpc.CallOption) (*GetUserFromEntertainmentQualificationResp, error) {
	out := new(GetUserFromEntertainmentQualificationResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/GetUserFromEntertainmentQualification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) GetChannelFromEntertainmentQualification(ctx context.Context, in *GetChannelFromEntertainmentQualificationReq, opts ...grpc.CallOption) (*GetChannelFromEntertainmentQualificationResp, error) {
	out := new(GetChannelFromEntertainmentQualificationResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/GetChannelFromEntertainmentQualification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) IsUserHasEntertainmentQualification(ctx context.Context, in *IsUserHasEntertainmentQualificationReq, opts ...grpc.CallOption) (*IsUserHasEntertainmentQualificationResp, error) {
	out := new(IsUserHasEntertainmentQualificationResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/IsUserHasEntertainmentQualification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) AddMaskedGameConfig(ctx context.Context, in *AddMaskedGameConfigReq, opts ...grpc.CallOption) (*AddMaskedGameConfigResp, error) {
	out := new(AddMaskedGameConfigResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/AddMaskedGameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) DelMaskedGameConfig(ctx context.Context, in *DelMaskedGameConfigReq, opts ...grpc.CallOption) (*DelMaskedGameConfigResp, error) {
	out := new(DelMaskedGameConfigResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/DelMaskedGameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) UpdateMaskedGameConfig(ctx context.Context, in *UpdateMaskedGameConfigReq, opts ...grpc.CallOption) (*UpdateMaskedGameConfigResp, error) {
	out := new(UpdateMaskedGameConfigResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/UpdateMaskedGameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) GetAllMaskedGameConfig(ctx context.Context, in *GetAllMaskedGameConfigReq, opts ...grpc.CallOption) (*GetAllMaskedGameConfigResp, error) {
	out := new(GetAllMaskedGameConfigResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/GetAllMaskedGameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) GetLastMaskedGameConfig(ctx context.Context, in *GetLastMaskedGameConfigReq, opts ...grpc.CallOption) (*GetLastMaskedGameConfigResp, error) {
	out := new(GetLastMaskedGameConfigResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/GetLastMaskedGameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) BatchAddChannelToWhiteList(ctx context.Context, in *BatchAddChannelToWhiteListReq, opts ...grpc.CallOption) (*BatchAddChannelToWhiteListResp, error) {
	out := new(BatchAddChannelToWhiteListResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/BatchAddChannelToWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) BatchDelChannelToWhiteList(ctx context.Context, in *BatchDelChannelToWhiteListReq, opts ...grpc.CallOption) (*BatchDelChannelToWhiteListResp, error) {
	out := new(BatchDelChannelToWhiteListResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/BatchDelChannelToWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) GetAllWhiteList(ctx context.Context, in *GetAllWhiteListReq, opts ...grpc.CallOption) (*GetAllWhiteListResp, error) {
	out := new(GetAllWhiteListResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/GetAllWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) UpdateWhiteListConfig(ctx context.Context, in *UpdateWhiteListConfigReq, opts ...grpc.CallOption) (*UpdateWhiteListConfigResp, error) {
	out := new(UpdateWhiteListConfigResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/UpdateWhiteListConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) GetWhiteListConfig(ctx context.Context, in *GetWhiteListConfigReq, opts ...grpc.CallOption) (*GetWhiteListConfigResp, error) {
	out := new(GetWhiteListConfigResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/GetWhiteListConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) SetMonthGiftValue(ctx context.Context, in *SetMonthGiftValueReq, opts ...grpc.CallOption) (*SetMonthGiftValueResp, error) {
	out := new(SetMonthGiftValueResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/SetMonthGiftValue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) GetChannelQualifiedAnchorList(ctx context.Context, in *GetChannelQualifiedAnchorListReq, opts ...grpc.CallOption) (*GetChannelQualifiedAnchorListResp, error) {
	out := new(GetChannelQualifiedAnchorListResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/GetChannelQualifiedAnchorList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) TestPushQuickKillChange(ctx context.Context, in *TestPushQuickKillChangeReq, opts ...grpc.CallOption) (*TestPushQuickKillChangeResp, error) {
	out := new(TestPushQuickKillChangeResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/TestPushQuickKillChange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) NotifyGodLikeTopUsers(ctx context.Context, in *NotifyGodLikeTopUsersReq, opts ...grpc.CallOption) (*NotifyGodLikeTopUsersResp, error) {
	out := new(NotifyGodLikeTopUsersResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/NotifyGodLikeTopUsers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) CheckMaskedPkRankEntry(ctx context.Context, in *CheckMaskedPkRankEntryReq, opts ...grpc.CallOption) (*CheckMaskedPkRankEntryResp, error) {
	out := new(CheckMaskedPkRankEntryResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/CheckMaskedPkRankEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) MaskedPkGetConsumeTopN(ctx context.Context, in *MaskedPkGetConsumeTopNReq, opts ...grpc.CallOption) (*MaskedPkGetConsumeTopNResp, error) {
	out := new(MaskedPkGetConsumeTopNResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/MaskedPkGetConsumeTopN", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKSvrClient) PushGameBeginConf(ctx context.Context, in *PushGameBeginConfReq, opts ...grpc.CallOption) (*PushGameBeginConfResp, error) {
	out := new(PushGameBeginConfResp)
	err := c.cc.Invoke(ctx, "/masked_pk_svr.MaskedPKSvr/PushGameBeginConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MaskedPKSvrServer is the server API for MaskedPKSvr service.
type MaskedPKSvrServer interface {
	StartChannelMaskedPK(context.Context, *StartChannelMaskedPKReq) (*StartChannelMaskedPKResp, error)
	GiveUpChannelMaskedPK(context.Context, *GiveUpChannelMaskedPKReq) (*GiveUpChannelMaskedPKResp, error)
	CancelChannelMaskedPK(context.Context, *CancelChannelMaskedPKReq) (*CancelChannelMaskedPKResp, error)
	GetChannelMaskedPKCurrConfWithUser(context.Context, *GetChannelMaskedPKCurrConfWithUserReq) (*GetChannelMaskedPKCurrConfWithUserResp, error)
	GetChannelMaskedPKCurrConf(context.Context, *GetChannelMaskedPKCurrConfReq) (*GetChannelMaskedPKCurrConfResp, error)
	GetChannelMaskedPKInfo(context.Context, *GetChannelMaskedPKInfoReq) (*GetChannelMaskedPKInfoResp, error)
	GetChannelMaskedPKAnchorRank(context.Context, *GetChannelMaskedPKAnchorRankReq) (*GetChannelMaskedPKAnchorRankResp, error)
	GetChannelSinglePKAnchorRank(context.Context, *GetChannelSinglePKAnchorRankReq) (*GetChannelSinglePKAnchorRankResp, error)
	GetChannelMaskedPKStatus(context.Context, *GetChannelMaskedPKStatusReq) (*GetChannelMaskedPKStatusResp, error)
	BatchGetChannelMaskedPKStatus(context.Context, *BatchGetChannelMaskedPKStatusReq) (*BatchGetChannelMaskedPKStatusResp, error)
	// 为（房间 - 用户）添加参赛资格，房间和用户都填，需要查询并确认guildId
	BatchAddUserToEntertainmentQualification(context.Context, *BatchAddUserToEntertainmentQualificationReq) (*BatchAddUserToEntertainmentQualificationResp, error)
	// 删除（房间 - 用户）的参赛资格
	BatchDelUserFromEntertainmentQualification(context.Context, *BatchDelUserFromEntertainmentQualificationReq) (*BatchDelUserFromEntertainmentQualificationResp, error)
	// 获取参赛资格名单
	GetUserFromEntertainmentQualification(context.Context, *GetUserFromEntertainmentQualificationReq) (*GetUserFromEntertainmentQualificationResp, error)
	// 获取有参赛资格的房间列表
	GetChannelFromEntertainmentQualification(context.Context, *GetChannelFromEntertainmentQualificationReq) (*GetChannelFromEntertainmentQualificationResp, error)
	// （房间 - 用户）是否具有参赛资格
	IsUserHasEntertainmentQualification(context.Context, *IsUserHasEntertainmentQualificationReq) (*IsUserHasEntertainmentQualificationResp, error)
	// 添加蒙面pk场次配置
	AddMaskedGameConfig(context.Context, *AddMaskedGameConfigReq) (*AddMaskedGameConfigResp, error)
	// 删除蒙面pk场次配置
	DelMaskedGameConfig(context.Context, *DelMaskedGameConfigReq) (*DelMaskedGameConfigResp, error)
	// 更新对应gameId的蒙面pk场次配置
	UpdateMaskedGameConfig(context.Context, *UpdateMaskedGameConfigReq) (*UpdateMaskedGameConfigResp, error)
	// 获得所有蒙面pk场次配置
	GetAllMaskedGameConfig(context.Context, *GetAllMaskedGameConfigReq) (*GetAllMaskedGameConfigResp, error)
	// 获取上一场PK的场次配置
	GetLastMaskedGameConfig(context.Context, *GetLastMaskedGameConfigReq) (*GetLastMaskedGameConfigResp, error)
	// 添加房间到白名单
	BatchAddChannelToWhiteList(context.Context, *BatchAddChannelToWhiteListReq) (*BatchAddChannelToWhiteListResp, error)
	// 删除房间白名单
	BatchDelChannelToWhiteList(context.Context, *BatchDelChannelToWhiteListReq) (*BatchDelChannelToWhiteListResp, error)
	// 获得所有白名单内房间
	GetAllWhiteList(context.Context, *GetAllWhiteListReq) (*GetAllWhiteListResp, error)
	// 更新白名单配置（是否允许同工会匹配，是否使用白名单），全局配置所以只有更新接口
	UpdateWhiteListConfig(context.Context, *UpdateWhiteListConfigReq) (*UpdateWhiteListConfigResp, error)
	// 获取白名单配置（是否允许同工会匹配，是否使用白名单）
	GetWhiteListConfig(context.Context, *GetWhiteListConfigReq) (*GetWhiteListConfigResp, error)
	// 测试用接口
	SetMonthGiftValue(context.Context, *SetMonthGiftValueReq) (*SetMonthGiftValueResp, error)
	// 获取房间有资格的主播列表
	GetChannelQualifiedAnchorList(context.Context, *GetChannelQualifiedAnchorListReq) (*GetChannelQualifiedAnchorListResp, error)
	// only for test
	TestPushQuickKillChange(context.Context, *TestPushQuickKillChangeReq) (*TestPushQuickKillChangeResp, error)
	// 通知神仙局前三用户
	NotifyGodLikeTopUsers(context.Context, *NotifyGodLikeTopUsersReq) (*NotifyGodLikeTopUsersResp, error)
	// 蒙面PK房间消费榜入口是否开放
	CheckMaskedPkRankEntry(context.Context, *CheckMaskedPkRankEntryReq) (*CheckMaskedPkRankEntryResp, error)
	// 蒙面PK房间消费榜单
	MaskedPkGetConsumeTopN(context.Context, *MaskedPkGetConsumeTopNReq) (*MaskedPkGetConsumeTopNResp, error)
	// 主动推送蒙面PK配置
	PushGameBeginConf(context.Context, *PushGameBeginConfReq) (*PushGameBeginConfResp, error)
}

func RegisterMaskedPKSvrServer(s *grpc.Server, srv MaskedPKSvrServer) {
	s.RegisterService(&_MaskedPKSvr_serviceDesc, srv)
}

func _MaskedPKSvr_StartChannelMaskedPK_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartChannelMaskedPKReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).StartChannelMaskedPK(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/StartChannelMaskedPK",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).StartChannelMaskedPK(ctx, req.(*StartChannelMaskedPKReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_GiveUpChannelMaskedPK_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiveUpChannelMaskedPKReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).GiveUpChannelMaskedPK(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/GiveUpChannelMaskedPK",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).GiveUpChannelMaskedPK(ctx, req.(*GiveUpChannelMaskedPKReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_CancelChannelMaskedPK_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelChannelMaskedPKReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).CancelChannelMaskedPK(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/CancelChannelMaskedPK",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).CancelChannelMaskedPK(ctx, req.(*CancelChannelMaskedPKReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_GetChannelMaskedPKCurrConfWithUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMaskedPKCurrConfWithUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).GetChannelMaskedPKCurrConfWithUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/GetChannelMaskedPKCurrConfWithUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).GetChannelMaskedPKCurrConfWithUser(ctx, req.(*GetChannelMaskedPKCurrConfWithUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_GetChannelMaskedPKCurrConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMaskedPKCurrConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).GetChannelMaskedPKCurrConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/GetChannelMaskedPKCurrConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).GetChannelMaskedPKCurrConf(ctx, req.(*GetChannelMaskedPKCurrConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_GetChannelMaskedPKInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMaskedPKInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).GetChannelMaskedPKInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/GetChannelMaskedPKInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).GetChannelMaskedPKInfo(ctx, req.(*GetChannelMaskedPKInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_GetChannelMaskedPKAnchorRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMaskedPKAnchorRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).GetChannelMaskedPKAnchorRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/GetChannelMaskedPKAnchorRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).GetChannelMaskedPKAnchorRank(ctx, req.(*GetChannelMaskedPKAnchorRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_GetChannelSinglePKAnchorRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelSinglePKAnchorRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).GetChannelSinglePKAnchorRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/GetChannelSinglePKAnchorRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).GetChannelSinglePKAnchorRank(ctx, req.(*GetChannelSinglePKAnchorRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_GetChannelMaskedPKStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMaskedPKStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).GetChannelMaskedPKStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/GetChannelMaskedPKStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).GetChannelMaskedPKStatus(ctx, req.(*GetChannelMaskedPKStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_BatchGetChannelMaskedPKStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelMaskedPKStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).BatchGetChannelMaskedPKStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/BatchGetChannelMaskedPKStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).BatchGetChannelMaskedPKStatus(ctx, req.(*BatchGetChannelMaskedPKStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_BatchAddUserToEntertainmentQualification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddUserToEntertainmentQualificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).BatchAddUserToEntertainmentQualification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/BatchAddUserToEntertainmentQualification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).BatchAddUserToEntertainmentQualification(ctx, req.(*BatchAddUserToEntertainmentQualificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_BatchDelUserFromEntertainmentQualification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelUserFromEntertainmentQualificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).BatchDelUserFromEntertainmentQualification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/BatchDelUserFromEntertainmentQualification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).BatchDelUserFromEntertainmentQualification(ctx, req.(*BatchDelUserFromEntertainmentQualificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_GetUserFromEntertainmentQualification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserFromEntertainmentQualificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).GetUserFromEntertainmentQualification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/GetUserFromEntertainmentQualification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).GetUserFromEntertainmentQualification(ctx, req.(*GetUserFromEntertainmentQualificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_GetChannelFromEntertainmentQualification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelFromEntertainmentQualificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).GetChannelFromEntertainmentQualification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/GetChannelFromEntertainmentQualification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).GetChannelFromEntertainmentQualification(ctx, req.(*GetChannelFromEntertainmentQualificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_IsUserHasEntertainmentQualification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsUserHasEntertainmentQualificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).IsUserHasEntertainmentQualification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/IsUserHasEntertainmentQualification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).IsUserHasEntertainmentQualification(ctx, req.(*IsUserHasEntertainmentQualificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_AddMaskedGameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMaskedGameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).AddMaskedGameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/AddMaskedGameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).AddMaskedGameConfig(ctx, req.(*AddMaskedGameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_DelMaskedGameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelMaskedGameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).DelMaskedGameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/DelMaskedGameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).DelMaskedGameConfig(ctx, req.(*DelMaskedGameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_UpdateMaskedGameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMaskedGameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).UpdateMaskedGameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/UpdateMaskedGameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).UpdateMaskedGameConfig(ctx, req.(*UpdateMaskedGameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_GetAllMaskedGameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllMaskedGameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).GetAllMaskedGameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/GetAllMaskedGameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).GetAllMaskedGameConfig(ctx, req.(*GetAllMaskedGameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_GetLastMaskedGameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastMaskedGameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).GetLastMaskedGameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/GetLastMaskedGameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).GetLastMaskedGameConfig(ctx, req.(*GetLastMaskedGameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_BatchAddChannelToWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddChannelToWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).BatchAddChannelToWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/BatchAddChannelToWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).BatchAddChannelToWhiteList(ctx, req.(*BatchAddChannelToWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_BatchDelChannelToWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelChannelToWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).BatchDelChannelToWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/BatchDelChannelToWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).BatchDelChannelToWhiteList(ctx, req.(*BatchDelChannelToWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_GetAllWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).GetAllWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/GetAllWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).GetAllWhiteList(ctx, req.(*GetAllWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_UpdateWhiteListConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWhiteListConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).UpdateWhiteListConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/UpdateWhiteListConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).UpdateWhiteListConfig(ctx, req.(*UpdateWhiteListConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_GetWhiteListConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWhiteListConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).GetWhiteListConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/GetWhiteListConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).GetWhiteListConfig(ctx, req.(*GetWhiteListConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_SetMonthGiftValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMonthGiftValueReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).SetMonthGiftValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/SetMonthGiftValue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).SetMonthGiftValue(ctx, req.(*SetMonthGiftValueReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_GetChannelQualifiedAnchorList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelQualifiedAnchorListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).GetChannelQualifiedAnchorList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/GetChannelQualifiedAnchorList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).GetChannelQualifiedAnchorList(ctx, req.(*GetChannelQualifiedAnchorListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_TestPushQuickKillChange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestPushQuickKillChangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).TestPushQuickKillChange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/TestPushQuickKillChange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).TestPushQuickKillChange(ctx, req.(*TestPushQuickKillChangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_NotifyGodLikeTopUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyGodLikeTopUsersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).NotifyGodLikeTopUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/NotifyGodLikeTopUsers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).NotifyGodLikeTopUsers(ctx, req.(*NotifyGodLikeTopUsersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_CheckMaskedPkRankEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckMaskedPkRankEntryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).CheckMaskedPkRankEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/CheckMaskedPkRankEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).CheckMaskedPkRankEntry(ctx, req.(*CheckMaskedPkRankEntryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_MaskedPkGetConsumeTopN_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MaskedPkGetConsumeTopNReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).MaskedPkGetConsumeTopN(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/MaskedPkGetConsumeTopN",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).MaskedPkGetConsumeTopN(ctx, req.(*MaskedPkGetConsumeTopNReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKSvr_PushGameBeginConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushGameBeginConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKSvrServer).PushGameBeginConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_svr.MaskedPKSvr/PushGameBeginConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKSvrServer).PushGameBeginConf(ctx, req.(*PushGameBeginConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MaskedPKSvr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "masked_pk_svr.MaskedPKSvr",
	HandlerType: (*MaskedPKSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "StartChannelMaskedPK",
			Handler:    _MaskedPKSvr_StartChannelMaskedPK_Handler,
		},
		{
			MethodName: "GiveUpChannelMaskedPK",
			Handler:    _MaskedPKSvr_GiveUpChannelMaskedPK_Handler,
		},
		{
			MethodName: "CancelChannelMaskedPK",
			Handler:    _MaskedPKSvr_CancelChannelMaskedPK_Handler,
		},
		{
			MethodName: "GetChannelMaskedPKCurrConfWithUser",
			Handler:    _MaskedPKSvr_GetChannelMaskedPKCurrConfWithUser_Handler,
		},
		{
			MethodName: "GetChannelMaskedPKCurrConf",
			Handler:    _MaskedPKSvr_GetChannelMaskedPKCurrConf_Handler,
		},
		{
			MethodName: "GetChannelMaskedPKInfo",
			Handler:    _MaskedPKSvr_GetChannelMaskedPKInfo_Handler,
		},
		{
			MethodName: "GetChannelMaskedPKAnchorRank",
			Handler:    _MaskedPKSvr_GetChannelMaskedPKAnchorRank_Handler,
		},
		{
			MethodName: "GetChannelSinglePKAnchorRank",
			Handler:    _MaskedPKSvr_GetChannelSinglePKAnchorRank_Handler,
		},
		{
			MethodName: "GetChannelMaskedPKStatus",
			Handler:    _MaskedPKSvr_GetChannelMaskedPKStatus_Handler,
		},
		{
			MethodName: "BatchGetChannelMaskedPKStatus",
			Handler:    _MaskedPKSvr_BatchGetChannelMaskedPKStatus_Handler,
		},
		{
			MethodName: "BatchAddUserToEntertainmentQualification",
			Handler:    _MaskedPKSvr_BatchAddUserToEntertainmentQualification_Handler,
		},
		{
			MethodName: "BatchDelUserFromEntertainmentQualification",
			Handler:    _MaskedPKSvr_BatchDelUserFromEntertainmentQualification_Handler,
		},
		{
			MethodName: "GetUserFromEntertainmentQualification",
			Handler:    _MaskedPKSvr_GetUserFromEntertainmentQualification_Handler,
		},
		{
			MethodName: "GetChannelFromEntertainmentQualification",
			Handler:    _MaskedPKSvr_GetChannelFromEntertainmentQualification_Handler,
		},
		{
			MethodName: "IsUserHasEntertainmentQualification",
			Handler:    _MaskedPKSvr_IsUserHasEntertainmentQualification_Handler,
		},
		{
			MethodName: "AddMaskedGameConfig",
			Handler:    _MaskedPKSvr_AddMaskedGameConfig_Handler,
		},
		{
			MethodName: "DelMaskedGameConfig",
			Handler:    _MaskedPKSvr_DelMaskedGameConfig_Handler,
		},
		{
			MethodName: "UpdateMaskedGameConfig",
			Handler:    _MaskedPKSvr_UpdateMaskedGameConfig_Handler,
		},
		{
			MethodName: "GetAllMaskedGameConfig",
			Handler:    _MaskedPKSvr_GetAllMaskedGameConfig_Handler,
		},
		{
			MethodName: "GetLastMaskedGameConfig",
			Handler:    _MaskedPKSvr_GetLastMaskedGameConfig_Handler,
		},
		{
			MethodName: "BatchAddChannelToWhiteList",
			Handler:    _MaskedPKSvr_BatchAddChannelToWhiteList_Handler,
		},
		{
			MethodName: "BatchDelChannelToWhiteList",
			Handler:    _MaskedPKSvr_BatchDelChannelToWhiteList_Handler,
		},
		{
			MethodName: "GetAllWhiteList",
			Handler:    _MaskedPKSvr_GetAllWhiteList_Handler,
		},
		{
			MethodName: "UpdateWhiteListConfig",
			Handler:    _MaskedPKSvr_UpdateWhiteListConfig_Handler,
		},
		{
			MethodName: "GetWhiteListConfig",
			Handler:    _MaskedPKSvr_GetWhiteListConfig_Handler,
		},
		{
			MethodName: "SetMonthGiftValue",
			Handler:    _MaskedPKSvr_SetMonthGiftValue_Handler,
		},
		{
			MethodName: "GetChannelQualifiedAnchorList",
			Handler:    _MaskedPKSvr_GetChannelQualifiedAnchorList_Handler,
		},
		{
			MethodName: "TestPushQuickKillChange",
			Handler:    _MaskedPKSvr_TestPushQuickKillChange_Handler,
		},
		{
			MethodName: "NotifyGodLikeTopUsers",
			Handler:    _MaskedPKSvr_NotifyGodLikeTopUsers_Handler,
		},
		{
			MethodName: "CheckMaskedPkRankEntry",
			Handler:    _MaskedPKSvr_CheckMaskedPkRankEntry_Handler,
		},
		{
			MethodName: "MaskedPkGetConsumeTopN",
			Handler:    _MaskedPKSvr_MaskedPkGetConsumeTopN_Handler,
		},
		{
			MethodName: "PushGameBeginConf",
			Handler:    _MaskedPKSvr_PushGameBeginConf_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "masked-pk-svr/masked-pk-svr.proto",
}

func init() {
	proto.RegisterFile("masked-pk-svr/masked-pk-svr.proto", fileDescriptor_masked_pk_svr_7f96e23def430b21)
}

var fileDescriptor_masked_pk_svr_7f96e23def430b21 = []byte{
	// 4142 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x3b, 0xcb, 0x72, 0x1b, 0x49,
	0x72, 0x6c, 0xbe, 0x40, 0x24, 0x08, 0x12, 0x2c, 0xbd, 0x40, 0x48, 0x1a, 0x51, 0x3d, 0x92, 0x86,
	0xd2, 0xe8, 0xe1, 0xd5, 0xce, 0xcc, 0xae, 0x35, 0xb3, 0xb1, 0x41, 0x91, 0x14, 0x87, 0x2b, 0x91,
	0x4b, 0x81, 0xd4, 0xec, 0xec, 0x5e, 0xda, 0x4d, 0xa0, 0x08, 0x96, 0xd1, 0xe8, 0x6e, 0x76, 0x75,
	0x83, 0xe2, 0x3a, 0xbc, 0x0e, 0x3b, 0x7c, 0x76, 0x84, 0x1f, 0xb1, 0x0e, 0x87, 0x7d, 0xf5, 0xc9,
	0x37, 0x47, 0xd8, 0xe1, 0x4f, 0xf0, 0xdd, 0xf6, 0xd5, 0x1f, 0xb1, 0x8e, 0xf0, 0xc5, 0x17, 0x47,
	0x66, 0x55, 0xe3, 0xd1, 0x0f, 0x02, 0xe4, 0x2a, 0x76, 0x6e, 0xa8, 0xac, 0xac, 0xcc, 0xac, 0xac,
	0xac, 0xcc, 0xac, 0xcc, 0x06, 0xdc, 0xed, 0xd8, 0xb2, 0xcd, 0x9b, 0x4f, 0xfc, 0xf6, 0x13, 0xd9,
	0x0d, 0x9e, 0x0d, 0x8d, 0x9e, 0xfa, 0x81, 0x17, 0x7a, 0xac, 0xac, 0x80, 0x96, 0xdf, 0xb6, 0x64,
	0x37, 0x30, 0x25, 0x94, 0xdf, 0x49, 0x1e, 0xec, 0x05, 0xa2, 0x2b, 0x1c, 0xde, 0xe2, 0xac, 0x0a,
	0x05, 0xbb, 0xd1, 0xf0, 0x22, 0x37, 0xac, 0x1a, 0x2b, 0xc6, 0x6a, 0xb1, 0x1e, 0x0f, 0x59, 0x0d,
	0xe6, 0x5c, 0xd1, 0x68, 0xbb, 0x76, 0x87, 0x57, 0x27, 0x69, 0xaa, 0x37, 0x66, 0x0c, 0xa6, 0xc3,
	0x33, 0x9f, 0x57, 0xa7, 0x56, 0x8c, 0xd5, 0x72, 0x9d, 0x7e, 0x23, 0x25, 0xcf, 0x0f, 0x85, 0xe7,
	0xca, 0xea, 0xf4, 0x8a, 0xb1, 0x3a, 0x5f, 0x8f, 0x87, 0xe6, 0xbf, 0x1b, 0x50, 0x52, 0x5c, 0xbd,
	0x23, 0xe1, 0x70, 0x56, 0x81, 0xa9, 0x48, 0x34, 0x89, 0x5f, 0xb9, 0x8e, 0x3f, 0x07, 0xa5, 0x98,
	0xcc, 0x97, 0x62, 0x2a, 0x21, 0xc5, 0xc7, 0x50, 0xd6, 0x68, 0x96, 0xed, 0x08, 0x5b, 0xf1, 0x2d,
	0xd6, 0xe7, 0x35, 0x70, 0x0d, 0x61, 0xc8, 0x4c, 0xf2, 0xf7, 0xd5, 0x19, 0xc5, 0x4c, 0xf2, 0xf7,
	0xec, 0x05, 0x14, 0xfd, 0x78, 0xff, 0xd5, 0xd9, 0x15, 0x63, 0xb5, 0xf4, 0xfc, 0xd6, 0xd3, 0x21,
	0x35, 0x3d, 0x1d, 0xd2, 0x51, 0xbd, 0x8f, 0x6e, 0xfe, 0xcb, 0x14, 0x5c, 0x59, 0x3f, 0xb6, 0x5d,
	0x97, 0x3b, 0x3b, 0xb4, 0x62, 0xef, 0xf5, 0xba, 0xe7, 0x1e, 0xb1, 0x65, 0x98, 0x3b, 0xe4, 0x2d,
	0xe1, 0x5a, 0xa1, 0xd4, 0xfb, 0x2a, 0xd0, 0xf8, 0x40, 0xb2, 0x6b, 0x30, 0xcb, 0xdd, 0x26, 0x4e,
	0x4c, 0xd2, 0xc4, 0x0c, 0x77, 0x9b, 0x07, 0x92, 0xdd, 0x84, 0x62, 0xe3, 0x58, 0xf8, 0x56, 0xe0,
	0x39, 0xb1, 0x1e, 0xe7, 0x10, 0x50, 0xf7, 0x1c, 0xd2, 0x2f, 0xfe, 0xa6, 0x0d, 0x95, 0xeb, 0xf4,
	0x9b, 0xdd, 0x87, 0x85, 0x86, 0xe7, 0x86, 0xc2, 0x8d, 0xb8, 0xd5, 0xb1, 0xc3, 0xc6, 0xb1, 0xde,
	0x53, 0x39, 0x86, 0xee, 0x20, 0x90, 0x3d, 0x82, 0x25, 0x3b, 0x0a, 0x3d, 0x85, 0x22, 0xdc, 0x96,
	0xd5, 0x70, 0x43, 0xda, 0x65, 0xb9, 0xbe, 0x88, 0x13, 0x3b, 0x1a, 0xbe, 0xee, 0x86, 0x28, 0xf5,
	0x1f, 0x46, 0x1d, 0xdf, 0x8a, 0x02, 0xa7, 0x5a, 0x50, 0x7a, 0xc7, 0xf1, 0xbb, 0xc0, 0x61, 0x37,
	0xa0, 0xd0, 0xf0, 0xdc, 0x23, 0x4b, 0x34, 0xab, 0x73, 0xb4, 0x78, 0x16, 0x87, 0xdb, 0x4d, 0x94,
	0x5b, 0xf2, 0xa0, 0xcb, 0x03, 0xcb, 0x95, 0xd5, 0xe2, 0x8a, 0xb1, 0x3a, 0x55, 0x9f, 0x53, 0x80,
	0x5d, 0xc9, 0xee, 0x40, 0xa9, 0x29, 0xba, 0xa2, 0xc9, 0x2d, 0x32, 0x0f, 0xa0, 0x95, 0xa0, 0x40,
	0x07, 0x68, 0x24, 0x77, 0x61, 0x3e, 0x92, 0xdc, 0x3a, 0xb4, 0x1b, 0x6d, 0xdf, 0x6e, 0xb4, 0xab,
	0x25, 0xc2, 0x28, 0x45, 0x92, 0xbf, 0xd4, 0x20, 0xf6, 0x04, 0xae, 0x28, 0xc5, 0xf0, 0x06, 0x17,
	0x5d, 0x6e, 0x69, 0xe5, 0xcd, 0x13, 0x66, 0x85, 0x54, 0xa4, 0x66, 0x36, 0x51, 0x8f, 0xe6, 0x3d,
	0x98, 0x5b, 0x8f, 0xd5, 0x06, 0x30, 0xbb, 0xeb, 0xe1, 0xa8, 0x32, 0xc1, 0xe6, 0x61, 0xee, 0x6b,
	0xbb, 0xcb, 0x69, 0x64, 0x98, 0x7f, 0x3b, 0x03, 0xd7, 0x12, 0xe7, 0xb6, 0x1f, 0xda, 0x61, 0x24,
	0x71, 0xa3, 0x2d, 0xbb, 0xc3, 0xad, 0x9e, 0x41, 0xce, 0xe2, 0x70, 0xbb, 0xc9, 0x6e, 0x03, 0x34,
	0xd4, 0x0a, 0x9c, 0x53, 0x67, 0x57, 0xd4, 0x10, 0xa5, 0x87, 0x46, 0x14, 0x04, 0x16, 0x9d, 0x53,
	0x7c, 0x7e, 0x51, 0x10, 0x20, 0x43, 0x24, 0x7a, 0x2a, 0x5c, 0x52, 0xbd, 0x3a, 0xc2, 0xd9, 0x53,
	0xe1, 0x6a, 0x8d, 0x3b, 0x9e, 0x94, 0x34, 0xa3, 0x8e, 0xaf, 0x80, 0x63, 0x9c, 0xba, 0x0e, 0xb3,
	0x92, 0x44, 0xd2, 0xa7, 0xa5, 0x47, 0xc8, 0x48, 0x2d, 0x41, 0x46, 0x05, 0xc5, 0x88, 0xd6, 0x20,
	0x23, 0x13, 0xca, 0x0a, 0x2d, 0x56, 0x93, 0x3a, 0xac, 0x92, 0x02, 0x92, 0x86, 0xf0, 0x50, 0x34,
	0x4e, 0x93, 0xcb, 0x06, 0x9d, 0x59, 0xb1, 0x0e, 0x0a, 0xb4, 0xc1, 0x65, 0x83, 0x3d, 0x80, 0xc5,
	0x80, 0xcb, 0xd0, 0x6a, 0xd8, 0x6e, 0x83, 0x3b, 0x24, 0x9b, 0x3a, 0xb9, 0x32, 0x82, 0xd7, 0x09,
	0x8a, 0x12, 0xc6, 0x78, 0x01, 0xef, 0xe2, 0xc1, 0x20, 0x5e, 0xa9, 0x8f, 0x57, 0x27, 0x28, 0xe2,
	0xdd, 0x81, 0x92, 0x42, 0x69, 0x12, 0x8e, 0x3a, 0x39, 0xd0, 0x20, 0x44, 0xf8, 0x12, 0x0a, 0x7e,
	0xdb, 0x42, 0x83, 0xaa, 0x96, 0xe9, 0xfe, 0x99, 0x89, 0xfb, 0x97, 0x71, 0xc5, 0xea, 0xb3, 0x7e,
	0x9b, 0xae, 0xda, 0x90, 0x01, 0x2e, 0x24, 0x0c, 0x30, 0x56, 0x16, 0xed, 0x74, 0x51, 0xf9, 0x0b,
	0x04, 0xe0, 0x3e, 0xcd, 0xbf, 0x32, 0x60, 0x56, 0x9f, 0xfa, 0x55, 0xa8, 0xec, 0x7a, 0xe1, 0x9e,
	0x1d, 0x84, 0xa2, 0x21, 0x7c, 0x3b, 0x14, 0x6e, 0xab, 0x32, 0xc1, 0x16, 0xa1, 0xb4, 0xeb, 0x85,
	0xf1, 0x0d, 0xa9, 0x18, 0x08, 0xd8, 0x0b, 0x78, 0x0f, 0x30, 0xc9, 0x18, 0x2c, 0xac, 0x45, 0xa1,
	0xb7, 0xed, 0xf6, 0x60, 0x53, 0x48, 0x6b, 0xad, 0x11, 0x8a, 0x2e, 0x1f, 0x80, 0x4e, 0xb3, 0x12,
	0x14, 0xb6, 0xdd, 0xbd, 0xd7, 0x38, 0x98, 0x61, 0x0b, 0x00, 0xdb, 0x2e, 0x29, 0x08, 0xc7, 0xb3,
	0xac, 0x08, 0x33, 0xdb, 0xf2, 0xa7, 0x51, 0x58, 0x29, 0x98, 0x5d, 0xb8, 0x9e, 0xd8, 0x6d, 0xdd,
	0x76, 0xdb, 0x3b, 0xbc, 0x93, 0xe1, 0x26, 0xaf, 0xc2, 0x8c, 0x6c, 0x78, 0x01, 0x8f, 0x3d, 0x09,
	0x0d, 0xd8, 0x67, 0x50, 0xf0, 0x95, 0x67, 0x25, 0x3b, 0x2c, 0x3d, 0xaf, 0x65, 0x7a, 0x33, 0xc2,
	0xa8, 0xc7, 0xa8, 0xe6, 0x7f, 0xa5, 0x3d, 0xd9, 0xb6, 0x7b, 0xe4, 0x25, 0xcc, 0xde, 0x38, 0xd7,
	0xec, 0x27, 0xf3, 0xcd, 0x7e, 0x6a, 0xc8, 0xec, 0x6f, 0x03, 0x0c, 0x18, 0x8d, 0xba, 0x12, 0xc5,
	0xa0, 0x67, 0x30, 0xbd, 0x7d, 0xcd, 0x0c, 0xee, 0x6b, 0xe8, 0x2c, 0x67, 0x87, 0xcf, 0x92, 0xed,
	0xc0, 0x62, 0xe8, 0xf9, 0x96, 0xed, 0x36, 0x8e, 0xbd, 0xc0, 0x72, 0x84, 0x0c, 0xab, 0x85, 0x95,
	0xa9, 0xd5, 0xd2, 0xf3, 0xfb, 0xe7, 0x9b, 0x92, 0x56, 0x6e, 0xbd, 0x1c, 0x7a, 0xfe, 0x1a, 0x2d,
	0x7e, 0x23, 0x64, 0xc8, 0x56, 0xa1, 0xe2, 0x07, 0xfc, 0x48, 0xbc, 0xb7, 0xfa, 0x2c, 0xe7, 0x88,
	0xe5, 0x82, 0x82, 0xbf, 0x89, 0x19, 0x0f, 0x5d, 0xc7, 0x62, 0xe2, 0x3a, 0x6e, 0xc0, 0xe2, 0x49,
	0x24, 0x1a, 0x6d, 0xab, 0x2d, 0x1c, 0xc7, 0x12, 0xee, 0x91, 0x47, 0x37, 0x29, 0x1d, 0x60, 0xde,
	0x22, 0xd6, 0x6b, 0xe1, 0x38, 0xa8, 0xf3, 0x7a, 0xf9, 0x64, 0x70, 0xc8, 0xbe, 0x84, 0x79, 0x9f,
	0xdb, 0x6d, 0xc4, 0x25, 0x12, 0x25, 0x22, 0xb1, 0x9c, 0x20, 0xb1, 0xc7, 0xed, 0xf6, 0x5e, 0x9b,
	0xd6, 0x83, 0xdf, 0xfb, 0x6d, 0xfe, 0x7a, 0x12, 0xca, 0x43, 0xd4, 0xd9, 0xf7, 0xe1, 0xfa, 0x80,
	0x50, 0xb8, 0x35, 0x4b, 0xed, 0x49, 0x47, 0xfc, 0x2b, 0x3d, 0xee, 0xb8, 0xc1, 0x3d, 0x9a, 0xc2,
	0xbb, 0x9e, 0x58, 0xa4, 0x23, 0x73, 0x79, 0x08, 0x9b, 0x3d, 0x84, 0xa5, 0x01, 0x3c, 0xed, 0x84,
	0xd4, 0xe1, 0x2f, 0xf4, 0x30, 0x95, 0x1f, 0x7a, 0x08, 0x4b, 0xdc, 0xb5, 0x0f, 0x1d, 0x6e, 0x75,
	0x84, 0x4b, 0xbb, 0xe0, 0x0d, 0x6d, 0x0b, 0x0b, 0x6a, 0x62, 0x47, 0xb8, 0x7b, 0xed, 0x7d, 0xde,
	0x18, 0x44, 0xb5, 0xdf, 0xc7, 0xa8, 0x33, 0x43, 0xa8, 0xf6, 0x7b, 0x85, 0xfa, 0x09, 0x2c, 0x36,
	0x3c, 0xb7, 0x29, 0x30, 0xd5, 0xb0, 0xba, 0xb6, 0x13, 0x71, 0xed, 0x3f, 0x17, 0x7a, 0xe0, 0x6f,
	0x10, 0x6a, 0x3e, 0x04, 0xe8, 0xab, 0x0c, 0x8f, 0x91, 0x74, 0x4c, 0x3b, 0x53, 0x7a, 0x98, 0x43,
	0x00, 0x39, 0x8a, 0xff, 0x98, 0x4c, 0x45, 0x8b, 0x97, 0x76, 0x18, 0x3a, 0x9c, 0xfd, 0x08, 0xe6,
	0x3a, 0xbc, 0xa3, 0xec, 0xcd, 0x20, 0x7b, 0x1b, 0xe1, 0xba, 0xe8, 0x7c, 0x0a, 0x1d, 0xde, 0x21,
	0x33, 0xab, 0x41, 0xd1, 0x6f, 0x5b, 0x43, 0xe9, 0x40, 0xc1, 0x6f, 0x6f, 0x8e, 0x4e, 0x08, 0x86,
	0x9c, 0xde, 0x74, 0xc2, 0xe9, 0x99, 0x50, 0xee, 0xda, 0x8e, 0x20, 0x11, 0x68, 0x3f, 0x33, 0xb4,
	0x9f, 0x12, 0x01, 0xf7, 0x68, 0x4b, 0x18, 0x78, 0x62, 0x1c, 0xd2, 0xcf, 0x5c, 0xbd, 0xa0, 0xa7,
	0xd9, 0x15, 0x98, 0x41, 0x4b, 0x6b, 0xea, 0xe0, 0x32, 0xed, 0xb7, 0x55, 0xf4, 0x93, 0xd1, 0xa1,
	0xe5, 0x1f, 0x07, 0xb6, 0xe4, 0x3a, 0xaa, 0x14, 0x65, 0x74, 0xb8, 0x47, 0x00, 0xf3, 0x39, 0x14,
	0xf7, 0xe3, 0x01, 0x86, 0xdd, 0x75, 0xaf, 0xd3, 0xf1, 0xdc, 0xca, 0x04, 0x2b, 0x43, 0xb1, 0x67,
	0x7d, 0x15, 0x03, 0xa7, 0x94, 0xd2, 0x2b, 0x93, 0xe8, 0x7e, 0x93, 0x5a, 0x55, 0x31, 0x63, 0x94,
	0xcf, 0x61, 0x30, 0xdd, 0xf2, 0x6c, 0x47, 0x2b, 0x8c, 0x7e, 0x53, 0x86, 0x14, 0x05, 0x41, 0x9c,
	0x81, 0xe2, 0xef, 0x81, 0x4c, 0x6b, 0x3a, 0x91, 0x69, 0xf5, 0x75, 0x37, 0x33, 0xac, 0x3b, 0xf3,
	0x0d, 0x2c, 0x6f, 0xf1, 0x30, 0xeb, 0xd0, 0xf8, 0x49, 0x86, 0x07, 0x3e, 0x3f, 0x29, 0x30, 0xff,
	0xd7, 0x80, 0x5a, 0x1e, 0x39, 0xe9, 0xb3, 0xcd, 0x5e, 0x24, 0xa6, 0x7b, 0x6d, 0xd0, 0xbd, 0xbe,
	0x77, 0xbe, 0x01, 0xa9, 0x80, 0x15, 0xc7, 0x6b, 0xb2, 0xdd, 0x9f, 0xc0, 0x82, 0xdf, 0xb6, 0x0e,
	0xc9, 0x22, 0x15, 0xa5, 0xc9, 0x71, 0x28, 0x29, 0x13, 0xae, 0xcf, 0xfb, 0x6d, 0xf5, 0x8b, 0x68,
	0x6d, 0xc6, 0xb1, 0x5a, 0x11, 0x9a, 0x1a, 0x87, 0x90, 0x3a, 0xb5, 0x38, 0xa2, 0x93, 0xd7, 0xf9,
	0x09, 0xdc, 0xd8, 0x0f, 0xed, 0x20, 0x4c, 0x61, 0x5e, 0x42, 0x89, 0x35, 0xa8, 0x66, 0xd3, 0x92,
	0xbe, 0xd9, 0x84, 0xea, 0x96, 0xe8, 0xf2, 0x77, 0xfe, 0x07, 0x60, 0x34, 0x98, 0xe3, 0x4e, 0x0d,
	0xe6, 0xb8, 0xe6, 0x4d, 0x58, 0xce, 0xe1, 0x22, 0x7d, 0xf3, 0x35, 0x54, 0x75, 0x4a, 0xf4, 0x01,
	0xf6, 0x7a, 0x13, 0x96, 0x73, 0x88, 0x49, 0xdf, 0xdc, 0x85, 0x9b, 0x69, 0x63, 0xd2, 0xf6, 0x70,
	0x19, 0x66, 0x5f, 0xc0, 0xad, 0x7c, 0x7a, 0xd2, 0x1f, 0xc8, 0x40, 0x8d, 0xc1, 0x0c, 0xd4, 0x8c,
	0x60, 0xe5, 0x25, 0x26, 0x36, 0x17, 0x13, 0x66, 0x20, 0xb1, 0x9e, 0x1c, 0x4a, 0xac, 0x1f, 0xc0,
	0x62, 0x5f, 0x4a, 0xe5, 0x4a, 0xa7, 0x56, 0xa6, 0xe8, 0x25, 0x13, 0x8b, 0x8a, 0xce, 0xd2, 0xfc,
	0x6f, 0x03, 0xee, 0x8e, 0xe0, 0x2b, 0x7d, 0x16, 0x02, 0x8b, 0xa9, 0xe9, 0xbb, 0xd5, 0xb1, 0x7d,
	0xed, 0x9b, 0x5f, 0x25, 0xec, 0x78, 0x24, 0xb5, 0xd8, 0xd2, 0x15, 0x64, 0xc7, 0xf6, 0x37, 0xdd,
	0x30, 0x38, 0xc3, 0x57, 0xc7, 0x30, 0xb8, 0xb6, 0xde, 0x73, 0x65, 0xc3, 0xa8, 0xa8, 0x87, 0x36,
	0x3f, 0x8b, 0xf5, 0xd0, 0xe6, 0x67, 0x98, 0xdc, 0xa8, 0xb0, 0xa4, 0x93, 0x36, 0x1a, 0xbc, 0x98,
	0xfc, 0xa1, 0x61, 0x7e, 0x0b, 0xf7, 0xd3, 0xc2, 0xac, 0x63, 0x32, 0xe5, 0xb9, 0x47, 0x3f, 0x13,
	0xe1, 0x31, 0x26, 0x6e, 0x97, 0x3a, 0xe9, 0x5f, 0xc1, 0x83, 0x71, 0x28, 0x4b, 0x9f, 0x7d, 0x01,
	0xd3, 0x94, 0x87, 0x1b, 0x63, 0xe7, 0xe1, 0x84, 0xcf, 0x6e, 0x01, 0x08, 0x69, 0xb5, 0xd0, 0x71,
	0x44, 0x2a, 0x11, 0x9c, 0xab, 0xcf, 0x09, 0xa9, 0xae, 0x8d, 0x79, 0x07, 0x6e, 0xe7, 0xf3, 0xaf,
	0xf3, 0x13, 0xf3, 0x5b, 0xf8, 0xe8, 0x3c, 0x84, 0xcb, 0x0b, 0x66, 0xfe, 0x9f, 0x01, 0xb7, 0x12,
	0xb3, 0x2a, 0xcf, 0xbb, 0x68, 0x5a, 0x7d, 0x15, 0x66, 0x0e, 0x3d, 0x37, 0x8a, 0xb3, 0x19, 0x35,
	0x40, 0x68, 0x60, 0x87, 0xc2, 0xa3, 0x10, 0x33, 0x59, 0x57, 0x03, 0xf6, 0x16, 0x96, 0x28, 0x1b,
	0x8d, 0x9a, 0x82, 0xbb, 0x0d, 0xae, 0x8c, 0x7a, 0xe6, 0x22, 0xf9, 0x28, 0x66, 0xb3, 0x6b, 0x7a,
	0x39, 0xa5, 0x0a, 0x03, 0x59, 0xfd, 0xec, 0xf8, 0x59, 0xfd, 0xaf, 0xe0, 0x4e, 0x5a, 0xaf, 0xfd,
	0xfd, 0x5f, 0xca, 0x4d, 0xa2, 0x22, 0x78, 0x4b, 0xb8, 0x3d, 0x45, 0xe0, 0x00, 0xa1, 0x8e, 0xe8,
	0x88, 0x38, 0x9b, 0x57, 0x03, 0xd3, 0x81, 0x95, 0xf3, 0xf9, 0x4b, 0x9f, 0x7d, 0x0d, 0xc5, 0xc0,
	0x76, 0xdb, 0x4a, 0x49, 0x93, 0xa4, 0xa4, 0x4f, 0xcf, 0x57, 0xd2, 0xd0, 0x01, 0xd6, 0xe7, 0x70,
	0x35, 0x79, 0x88, 0xa1, 0xdd, 0xee, 0x0b, 0xb7, 0xe5, 0xf0, 0xef, 0x6e, 0xb7, 0x59, 0xfc, 0x3f,
	0xe8, 0x6e, 0x25, 0x7c, 0x4a, 0x0e, 0x6c, 0xad, 0xd9, 0xc4, 0xb3, 0x3f, 0xf0, 0x36, 0xdd, 0x90,
	0x07, 0xa1, 0x2d, 0xdc, 0x0e, 0x77, 0xc3, 0xb7, 0x91, 0xed, 0x88, 0x23, 0xd1, 0x40, 0x7b, 0x74,
	0x71, 0xe7, 0x1b, 0xb0, 0x70, 0x32, 0x08, 0x93, 0xda, 0x29, 0xa6, 0x9f, 0x22, 0x83, 0x0b, 0x13,
	0x6b, 0xcc, 0xdf, 0xd0, 0x73, 0x62, 0x00, 0x34, 0x2a, 0x59, 0xd3, 0x0a, 0x9f, 0xec, 0x2b, 0xfc,
	0x63, 0x28, 0x47, 0x12, 0xb3, 0xaf, 0xe1, 0x3a, 0xde, 0x3c, 0x02, 0x77, 0xe3, 0x5a, 0xde, 0x32,
	0xcc, 0x11, 0x92, 0xdd, 0xe2, 0x5a, 0xc7, 0x05, 0x1c, 0xaf, 0xb5, 0xfa, 0x53, 0x71, 0x19, 0xaf,
	0xa8, 0xa6, 0xf6, 0xf9, 0x7b, 0x76, 0x17, 0xe6, 0x63, 0x59, 0x88, 0xb2, 0x7a, 0x25, 0x96, 0x34,
	0x6c, 0x17, 0x09, 0xdf, 0x06, 0x68, 0x45, 0xc2, 0x69, 0x2a, 0x04, 0x55, 0xe5, 0x2a, 0x12, 0x84,
	0xa6, 0xef, 0x40, 0x29, 0xf2, 0x9b, 0x76, 0xc8, 0xad, 0x50, 0x74, 0xe2, 0x44, 0x17, 0x14, 0xe8,
	0x40, 0xe8, 0x52, 0x67, 0x28, 0x9a, 0xba, 0x6c, 0x42, 0xbf, 0x91, 0x66, 0x53, 0x48, 0xdf, 0xb1,
	0xcf, 0x50, 0x05, 0xaa, 0x56, 0x52, 0xd4, 0x90, 0xed, 0x26, 0x0a, 0xac, 0x58, 0x8a, 0xa6, 0x2e,
	0x90, 0x14, 0x68, 0xbc, 0xdd, 0x64, 0xf7, 0x60, 0x41, 0x4d, 0xc9, 0x63, 0x2f, 0x08, 0x11, 0x41,
	0x55, 0x47, 0xe6, 0x09, 0xba, 0x8f, 0xc0, 0xed, 0xa6, 0xf9, 0xc7, 0xf0, 0x78, 0xfc, 0x93, 0x96,
	0x3e, 0xdb, 0x81, 0x2b, 0x47, 0xb6, 0x70, 0xac, 0x4b, 0x9c, 0x37, 0xc3, 0x85, 0x6f, 0x87, 0xcf,
	0xfc, 0xe7, 0xf0, 0x60, 0x5b, 0x22, 0xe3, 0xaf, 0x6d, 0x79, 0xbe, 0x8d, 0x5d, 0xd4, 0x16, 0xcc,
	0x1f, 0xc3, 0x27, 0x63, 0x91, 0x96, 0x3e, 0x5e, 0x39, 0xfe, 0x5e, 0xbd, 0xb3, 0x30, 0xb8, 0xa8,
	0x81, 0xf9, 0x16, 0xae, 0xaf, 0x35, 0x9b, 0xea, 0xaa, 0x6c, 0xd9, 0x1d, 0x8e, 0x9e, 0x5f, 0xb4,
	0x50, 0x96, 0x1f, 0x00, 0xa5, 0x6f, 0xa2, 0xa5, 0x43, 0xc6, 0x9d, 0xc4, 0xbe, 0x53, 0x6b, 0x34,
	0xba, 0xb9, 0x0c, 0x37, 0x32, 0x49, 0x4a, 0xdf, 0xfc, 0xb3, 0x19, 0xa8, 0x24, 0x27, 0x70, 0xd3,
	0xba, 0xd6, 0x8b, 0x16, 0xa3, 0x37, 0xad, 0xaa, 0xbd, 0x42, 0x59, 0x32, 0xbd, 0x42, 0x44, 0x27,
	0x0e, 0x28, 0x05, 0x7c, 0x87, 0xe8, 0x29, 0x7a, 0xe2, 0xd9, 0xdd, 0x96, 0x76, 0x2f, 0x05, 0x1c,
	0xaf, 0x75, 0x5b, 0x4a, 0x93, 0xc2, 0xb7, 0x54, 0x11, 0x7c, 0x3a, 0xd6, 0xa4, 0xf0, 0xd7, 0xa9,
	0x0c, 0x7e, 0x13, 0x8a, 0xa7, 0x22, 0x3c, 0xb6, 0x9a, 0x81, 0x7d, 0xaa, 0x1f, 0xc2, 0x73, 0x08,
	0xd8, 0x08, 0xec, 0x53, 0xbc, 0x05, 0x76, 0x23, 0x14, 0x5d, 0x11, 0x9e, 0x51, 0x29, 0x57, 0xdf,
	0x82, 0x18, 0xf6, 0x2e, 0x70, 0xd8, 0x8b, 0xe1, 0xc2, 0x2c, 0x5e, 0x83, 0x85, 0x54, 0x45, 0x61,
	0xa3, 0x57, 0xa7, 0x4d, 0xd6, 0x6c, 0xf5, 0x5a, 0x25, 0x9c, 0x2e, 0x31, 0x2a, 0x98, 0x12, 0xaf,
	0x57, 0xf1, 0x1b, 0x2c, 0x8b, 0xe8, 0xf7, 0x01, 0x15, 0x46, 0xee, 0xc2, 0xbc, 0x1f, 0x78, 0x8a,
	0x08, 0x62, 0xa8, 0x3b, 0x53, 0xd2, 0xb0, 0xf5, 0xec, 0xfa, 0x76, 0x29, 0xab, 0xbe, 0x3d, 0x90,
	0x56, 0xce, 0x0f, 0xa5, 0x95, 0x26, 0x94, 0x9b, 0x82, 0x5b, 0x5e, 0x14, 0x6a, 0x39, 0xcb, 0xb1,
	0x9c, 0xfc, 0xa7, 0x51, 0xa8, 0xe4, 0x7c, 0x04, 0x4b, 0xf6, 0xa9, 0x1d, 0x34, 0x2d, 0xbd, 0x21,
	0xf4, 0x23, 0x54, 0x43, 0x2c, 0xd7, 0x17, 0x69, 0x42, 0xa9, 0x00, 0x0d, 0x14, 0x45, 0xd6, 0x7b,
	0x0a, 0x0f, 0xb9, 0xed, 0x52, 0x35, 0xb1, 0x5c, 0xd7, 0xfb, 0x3c, 0x40, 0x10, 0xb2, 0x6c, 0xd8,
	0xae, 0x75, 0xc8, 0xad, 0x8e, 0xd7, 0x14, 0x47, 0x67, 0xd5, 0x0a, 0x99, 0x6a, 0xa9, 0x61, 0xbb,
	0x2f, 0xf9, 0x0e, 0x81, 0x52, 0x15, 0xef, 0xa5, 0x74, 0xc5, 0xfb, 0x36, 0x40, 0xbf, 0x86, 0x52,
	0x65, 0xea, 0xec, 0x7b, 0xc5, 0x13, 0xf3, 0x7b, 0x70, 0x7d, 0x23, 0x8e, 0x0e, 0xc3, 0x26, 0x9f,
	0x57, 0xbb, 0x46, 0x93, 0xce, 0x5c, 0x22, 0x7d, 0xf3, 0xf7, 0xe1, 0x76, 0xec, 0x5b, 0x74, 0xdc,
	0x39, 0xf0, 0x7e, 0x76, 0x2c, 0x42, 0xca, 0x3a, 0x90, 0x68, 0x15, 0x0a, 0xfa, 0x06, 0x93, 0x03,
	0x21, 0x1b, 0xa5, 0xa1, 0xb9, 0x0e, 0x1f, 0x9d, 0xb7, 0x54, 0xd2, 0x31, 0x93, 0x23, 0xd2, 0xbe,
	0x50, 0x13, 0x28, 0x21, 0x6c, 0x43, 0x81, 0xcc, 0x6f, 0x35, 0xff, 0x8d, 0xde, 0x9b, 0x67, 0x5c,
	0xfe, 0xe4, 0x76, 0xb9, 0xc3, 0x43, 0x6e, 0xd9, 0x4e, 0x5c, 0x0d, 0x28, 0x2a, 0xc8, 0x9a, 0xd3,
	0x17, 0x2f, 0x87, 0xf2, 0x78, 0xe2, 0x5d, 0x05, 0xb6, 0xc5, 0xc3, 0x35, 0xc7, 0x19, 0x94, 0xc9,
	0xdc, 0x86, 0x2b, 0x29, 0xa8, 0xf4, 0xd9, 0x73, 0x98, 0x1e, 0xa8, 0x04, 0x7d, 0x94, 0xb8, 0x4e,
	0x3d, 0x5c, 0xad, 0x7d, 0xc2, 0x35, 0xff, 0xd9, 0x80, 0xc5, 0xc4, 0xcc, 0x28, 0x37, 0x9a, 0x8c,
	0x72, 0x93, 0xa3, 0xa2, 0xdc, 0xd4, 0x88, 0x28, 0x37, 0x9d, 0x8a, 0x72, 0xc3, 0x11, 0x6d, 0x26,
	0x11, 0xd1, 0xcc, 0xbf, 0x31, 0xe0, 0x49, 0xac, 0x5b, 0xbc, 0x1c, 0xaf, 0x02, 0xaf, 0xf3, 0x3b,
	0xc8, 0x3e, 0x46, 0x9d, 0xf8, 0x9f, 0xc0, 0xd3, 0x8b, 0x48, 0xf5, 0xe1, 0x23, 0xe5, 0x11, 0xac,
	0x6e, 0xf1, 0x70, 0x3c, 0x8d, 0x30, 0x98, 0xf6, 0x31, 0xbb, 0x31, 0x74, 0x95, 0xcd, 0x6e, 0xd1,
	0x1b, 0xa3, 0xdf, 0xf5, 0x2c, 0xd7, 0xd5, 0x20, 0x0e, 0x9b, 0x53, 0xfd, 0xb0, 0xf9, 0x17, 0x06,
	0x3c, 0x1c, 0x93, 0x91, 0xf4, 0x3f, 0x90, 0xee, 0x6f, 0x42, 0x51, 0x46, 0x1d, 0x6b, 0x50, 0xbe,
	0x39, 0x19, 0x75, 0xc8, 0x91, 0x9a, 0x5d, 0xf8, 0xb4, 0x9f, 0xf9, 0x7e, 0xc8, 0xbd, 0x0f, 0x5f,
	0x85, 0xa9, 0xe4, 0xc3, 0xf6, 0x2f, 0x0d, 0x78, 0x3c, 0x3e, 0xe3, 0xdf, 0x8d, 0x2e, 0xfe, 0xde,
	0x80, 0xea, 0x3b, 0xba, 0x4a, 0xc9, 0x1b, 0xcf, 0x4f, 0x30, 0xe1, 0x43, 0xf7, 0x7f, 0x8a, 0x33,
	0x71, 0xd9, 0x98, 0x12, 0xbe, 0x48, 0xf6, 0xd1, 0xd9, 0x57, 0x70, 0xd3, 0x76, 0x1c, 0xef, 0x54,
	0x05, 0x3e, 0x8b, 0x42, 0xbd, 0x44, 0xcf, 0x4e, 0x57, 0x58, 0x73, 0xbc, 0x41, 0x28, 0x14, 0x05,
	0xf1, 0x09, 0xbf, 0x6f, 0x77, 0xf8, 0x16, 0x4e, 0xa3, 0x74, 0xc8, 0xc3, 0x0f, 0x84, 0x17, 0x17,
	0x44, 0x31, 0x63, 0xde, 0xc3, 0xb1, 0x79, 0x13, 0x96, 0x73, 0x84, 0x93, 0xbe, 0x79, 0x03, 0xae,
	0x6d, 0xf1, 0x30, 0x2d, 0xb6, 0xf9, 0x77, 0x06, 0x5c, 0xcf, 0x9a, 0x91, 0xfe, 0x77, 0xbf, 0xa3,
	0xd7, 0x70, 0x75, 0x9f, 0x87, 0x3b, 0x9e, 0x1b, 0x1e, 0x6f, 0x89, 0xa3, 0x90, 0xaa, 0xfb, 0x63,
	0x24, 0xa3, 0x99, 0x75, 0x18, 0xd4, 0x40, 0x06, 0x31, 0xe9, 0x9b, 0x07, 0xb1, 0xde, 0x3e, 0x68,
	0xae, 0x79, 0x0b, 0x6a, 0x79, 0x54, 0xa5, 0x6f, 0xbe, 0xa0, 0x62, 0xf4, 0x9a, 0x93, 0x19, 0xec,
	0xd1, 0xc5, 0xf3, 0xd0, 0xe2, 0xef, 0x7d, 0x11, 0x70, 0x9d, 0x14, 0x17, 0x5b, 0x3c, 0xdc, 0x24,
	0x80, 0xf9, 0x8e, 0x2a, 0xcf, 0x99, 0x6b, 0xa5, 0x3f, 0x24, 0xf0, 0xd4, 0x45, 0x04, 0x5e, 0x1b,
	0x7c, 0xe2, 0xea, 0x4b, 0xc2, 0x9b, 0xfd, 0xce, 0xd9, 0x68, 0xc5, 0x9b, 0xaf, 0xe1, 0xee, 0x08,
	0x12, 0xd2, 0x67, 0x0f, 0x60, 0x51, 0xf7, 0xf2, 0xa2, 0xb8, 0x28, 0xa8, 0xa2, 0x73, 0x59, 0x81,
	0xdf, 0x09, 0x55, 0x14, 0xbc, 0x45, 0xdb, 0x7c, 0x63, 0xcb, 0x30, 0x43, 0x47, 0xe6, 0x37, 0x54,
	0x31, 0xcd, 0x9e, 0x4d, 0x68, 0xe1, 0x42, 0xc7, 0xf6, 0x9f, 0x06, 0xd4, 0x0e, 0xb8, 0x0c, 0xf7,
	0x22, 0x79, 0xdc, 0x6b, 0x6f, 0xe0, 0x8e, 0x5a, 0xb1, 0xe5, 0xf1, 0x2e, 0x77, 0x43, 0x95, 0x5c,
	0x6b, 0x05, 0x10, 0x84, 0x12, 0xe8, 0xc7, 0xc0, 0xc2, 0x40, 0xb4, 0x5a, 0x3c, 0xb0, 0x52, 0x95,
	0x87, 0x8a, 0x9e, 0x59, 0xef, 0xd9, 0xe9, 0x03, 0x58, 0xa4, 0x5e, 0x5a, 0xca, 0x0d, 0x96, 0xdb,
	0x9a, 0xa9, 0x52, 0xeb, 0x1a, 0x14, 0x37, 0x7b, 0x2c, 0x4a, 0x50, 0x78, 0xe7, 0xb6, 0x5d, 0xef,
	0xd4, 0xad, 0x4c, 0xb0, 0xab, 0x50, 0x39, 0x50, 0x54, 0x07, 0x5b, 0x31, 0x4b, 0x50, 0xde, 0x0f,
	0x3d, 0xbf, 0x0f, 0x9a, 0x34, 0x6f, 0xc3, 0xcd, 0xdc, 0x5d, 0x49, 0xdf, 0x3c, 0x81, 0xc5, 0x2d,
	0xaf, 0xf9, 0x46, 0xb4, 0xf9, 0x81, 0xe7, 0x63, 0xec, 0x91, 0x19, 0xe5, 0x14, 0x06, 0xd3, 0x81,
	0xed, 0xb6, 0xe3, 0xe6, 0x0c, 0xfe, 0x66, 0x26, 0xcc, 0x63, 0xd2, 0x1e, 0x88, 0xc3, 0x08, 0xfd,
	0xa7, 0x96, 0x7f, 0x08, 0xd6, 0x77, 0xff, 0xd3, 0x03, 0xee, 0xdf, 0xfc, 0x37, 0x03, 0xaa, 0xbb,
	0x5e, 0x28, 0x8e, 0xce, 0x12, 0x9c, 0xcf, 0x4b, 0x77, 0xd9, 0x67, 0x30, 0x83, 0x99, 0xbc, 0xd4,
	0xf5, 0x95, 0x64, 0x22, 0x96, 0x24, 0xa5, 0x90, 0x31, 0xad, 0x52, 0x97, 0x49, 0x3f, 0xd6, 0x94,
	0x94, 0x25, 0x0d, 0xa3, 0xb4, 0x08, 0xdf, 0x0b, 0xf1, 0xcb, 0xaa, 0xf7, 0xa8, 0x9b, 0xd6, 0xef,
	0x05, 0x3d, 0xb1, 0xa9, 0x1e, 0x77, 0xe8, 0x68, 0x73, 0x24, 0x97, 0x3e, 0xd5, 0xf9, 0x8f, 0x79,
	0xa3, 0xad, 0x6b, 0x3c, 0xed, 0xba, 0xed, 0xb6, 0x55, 0x6d, 0x99, 0x9f, 0x98, 0x3f, 0x86, 0x5a,
	0xde, 0xa4, 0x4a, 0x5a, 0x39, 0x0e, 0x2c, 0xd5, 0xf8, 0xd4, 0x37, 0xbf, 0x44, 0xb0, 0x4d, 0x02,
	0x99, 0x5f, 0xc1, 0x12, 0xd5, 0x09, 0x6c, 0xe1, 0x38, 0xa2, 0x75, 0x1c, 0x52, 0x67, 0xe7, 0x1a,
	0xcc, 0x1e, 0x8a, 0x5f, 0xf6, 0x95, 0x35, 0x73, 0x28, 0x7e, 0xa9, 0xde, 0xe4, 0x6e, 0xd4, 0x89,
	0xdf, 0xe4, 0x6e, 0xd4, 0x31, 0xff, 0x75, 0x1a, 0xae, 0x0c, 0x14, 0x52, 0x65, 0xd4, 0x51, 0xad,
	0xa1, 0x8b, 0x7c, 0xa6, 0xd5, 0x2b, 0xa1, 0x4e, 0x0d, 0x96, 0x50, 0xd1, 0x36, 0x44, 0xe3, 0x38,
	0xfe, 0x8c, 0x09, 0x7f, 0xd3, 0xb9, 0x1f, 0xdb, 0x41, 0x27, 0xee, 0xf5, 0xd3, 0x60, 0xe8, 0x33,
	0xaf, 0xd9, 0xc4, 0x67, 0x5e, 0xf7, 0x61, 0xc1, 0xf5, 0x0e, 0x85, 0x83, 0x87, 0xe0, 0xf0, 0x2e,
	0x77, 0x74, 0xa3, 0xb2, 0x1c, 0x43, 0xdf, 0x20, 0x90, 0xad, 0x42, 0xa5, 0x15, 0x78, 0x91, 0x6f,
	0x1d, 0xd9, 0xae, 0xd4, 0x88, 0xea, 0xa9, 0xba, 0x40, 0xf0, 0x57, 0xb6, 0x2b, 0x15, 0xe6, 0x23,
	0x58, 0x8a, 0x2f, 0x17, 0x35, 0x73, 0x09, 0x55, 0xbd, 0x59, 0xe3, 0xce, 0xc4, 0x0e, 0xef, 0x28,
	0x5c, 0xf4, 0xba, 0x44, 0x95, 0x44, 0x03, 0x9d, 0x58, 0x23, 0x84, 0x12, 0x6b, 0xfd, 0x75, 0xd9,
	0x7c, 0xff, 0xeb, 0xb2, 0xf8, 0x3e, 0x94, 0x07, 0xee, 0xc3, 0x0d, 0x28, 0x34, 0x75, 0x6f, 0x5a,
	0x3d, 0x36, 0x67, 0x9b, 0x14, 0x68, 0xd8, 0x16, 0x2c, 0xf8, 0x8e, 0x1d, 0x1e, 0x79, 0x41, 0xc7,
	0xa2, 0x93, 0xa3, 0x57, 0x66, 0xe9, 0xf9, 0x4a, 0x46, 0xad, 0x77, 0xe8, 0x74, 0xeb, 0xe5, 0x78,
	0xdd, 0x1b, 0x04, 0xb1, 0x4d, 0x88, 0x9b, 0x27, 0x9a, 0x4e, 0x65, 0x4c, 0x3a, 0xf1, 0xcb, 0x42,
	0x91, 0x79, 0x0c, 0x4c, 0x46, 0x3e, 0x0f, 0x2c, 0xcc, 0xfb, 0x79, 0xa0, 0x55, 0xa3, 0x9e, 0xac,
	0x15, 0x9a, 0xd9, 0xa3, 0x09, 0xd2, 0x8d, 0xe9, 0xc2, 0x72, 0x6c, 0xb2, 0xe8, 0xe0, 0x95, 0xe9,
	0x1c, 0x78, 0xfe, 0xee, 0x18, 0xd1, 0xb8, 0xf7, 0xc1, 0x5c, 0xcf, 0x13, 0xaa, 0x0f, 0xe6, 0x54,
	0x5b, 0x2e, 0xe0, 0x27, 0x83, 0x5f, 0x91, 0x04, 0xfc, 0x64, 0xdd, 0x0d, 0xcd, 0xff, 0x31, 0xa0,
	0x96, 0xc7, 0x50, 0xfa, 0xbf, 0x05, 0xc7, 0x75, 0x28, 0x75, 0x78, 0xe7, 0x90, 0x07, 0xfd, 0x6e,
	0x54, 0xba, 0xe5, 0x90, 0x71, 0x45, 0xea, 0xa0, 0x96, 0x51, 0xc6, 0xf3, 0x25, 0x14, 0x3a, 0x67,
	0xaa, 0x8b, 0x3a, 0x9d, 0xd9, 0xb3, 0xc8, 0x22, 0x30, 0xdb, 0x39, 0xa3, 0xbb, 0xb6, 0x0c, 0x73,
	0x5d, 0xc1, 0x4f, 0x07, 0xbf, 0x0b, 0xc3, 0x31, 0xee, 0xfa, 0x73, 0xb8, 0x8a, 0x0e, 0x1a, 0xa3,
	0xd2, 0x4b, 0x94, 0x57, 0xb7, 0x50, 0x46, 0x45, 0xdd, 0x1b, 0x70, 0x2d, 0x63, 0x99, 0xf4, 0x1f,
	0x1d, 0x00, 0xf4, 0x0b, 0x3d, 0xec, 0x06, 0x5c, 0xd9, 0xd8, 0xfe, 0x66, 0x7b, 0x63, 0xd3, 0x3a,
	0xf8, 0xf9, 0xde, 0xa6, 0xb5, 0xb1, 0xf9, 0x6a, 0xed, 0xdd, 0x9b, 0x03, 0x15, 0x44, 0x06, 0x27,
	0xbe, 0x5e, 0x7b, 0xf3, 0xaa, 0x62, 0x24, 0xd1, 0xf7, 0x36, 0xeb, 0xeb, 0x9b, 0xbb, 0x07, 0x95,
	0xc9, 0xe7, 0xbf, 0xf9, 0x08, 0x4a, 0xbd, 0x7e, 0x5a, 0x37, 0x60, 0x02, 0xae, 0x66, 0x35, 0x71,
	0xd9, 0x83, 0x84, 0x52, 0x72, 0xba, 0xc6, 0xb5, 0x4f, 0xc6, 0xc2, 0x93, 0xbe, 0x39, 0xc1, 0x1c,
	0xb8, 0x96, 0xd9, 0xad, 0x65, 0x49, 0x1a, 0x79, 0x9d, 0xe3, 0xda, 0xea, 0x78, 0x88, 0x31, 0xb7,
	0xcc, 0x8e, 0x6d, 0x8a, 0x5b, 0x5e, 0x93, 0x38, 0xc5, 0x2d, 0xbf, 0x01, 0x3c, 0xc1, 0xfe, 0xda,
	0x00, 0x73, 0x74, 0x27, 0x8f, 0x7d, 0x96, 0xdc, 0xc0, 0x38, 0x6d, 0xc5, 0xda, 0xe7, 0x97, 0x58,
	0x45, 0x52, 0xfd, 0x51, 0xd6, 0x57, 0x0e, 0x31, 0x2e, 0x7b, 0x3c, 0x36, 0x59, 0x14, 0xe2, 0xc9,
	0x05, 0xb0, 0x89, 0xb9, 0x47, 0x2f, 0x93, 0xac, 0x4f, 0xd7, 0x56, 0x47, 0x92, 0xd2, 0x1f, 0x76,
	0xd4, 0x1e, 0x8e, 0x89, 0x49, 0x0c, 0xff, 0xd4, 0xc8, 0xea, 0x9b, 0xf7, 0xbb, 0x34, 0xec, 0xe9,
	0x48, 0x6a, 0x43, 0x3d, 0xa9, 0xda, 0xb3, 0x0b, 0xe1, 0x67, 0xc8, 0x90, 0x6e, 0x35, 0x9d, 0x23,
	0x43, 0x66, 0x5f, 0xec, 0x1c, 0x19, 0xb2, 0xfb, 0x58, 0xe6, 0x04, 0x8b, 0xa0, 0x9a, 0xd7, 0x3b,
	0x67, 0x8f, 0x46, 0x6e, 0xa9, 0xf7, 0xa9, 0x40, 0xed, 0xd3, 0xb1, 0x71, 0x89, 0xed, 0x9f, 0x1b,
	0xba, 0x62, 0x98, 0xcb, 0xfc, 0xd9, 0xc5, 0xda, 0xfc, 0x27, 0xb5, 0xdf, 0xbb, 0xe8, 0x77, 0x01,
	0xe6, 0x04, 0xfb, 0x47, 0x03, 0x56, 0xc7, 0x6d, 0xca, 0xb0, 0x17, 0x59, 0x0c, 0xc6, 0xeb, 0xdb,
	0xd5, 0xbe, 0xbc, 0xf4, 0x5a, 0x92, 0xf3, 0x9f, 0x0c, 0x78, 0x34, 0x7e, 0x51, 0x8c, 0x7d, 0x95,
	0xc5, 0x6d, 0xdc, 0x2a, 0x5f, 0xed, 0x47, 0xbf, 0xc5, 0x6a, 0x92, 0xf6, 0x1f, 0x0c, 0xfa, 0x06,
	0x62, 0x0c, 0x41, 0x7f, 0x90, 0xb6, 0x9a, 0xf1, 0x64, 0xfc, 0xe1, 0xe5, 0x16, 0xf6, 0x0e, 0x7d,
	0xdc, 0x72, 0x53, 0xea, 0xd0, 0x2f, 0x50, 0x20, 0x4b, 0x1d, 0xfa, 0x45, 0x6a, 0x5c, 0xe6, 0x04,
	0xfb, 0xb5, 0x01, 0x1f, 0x8f, 0xd1, 0x57, 0x63, 0x49, 0x8f, 0x3f, 0x5e, 0x9b, 0xaf, 0xf6, 0xc5,
	0x65, 0x96, 0x91, 0x60, 0x47, 0x70, 0x25, 0xa3, 0xb7, 0xc6, 0x92, 0x1f, 0x45, 0x64, 0xb7, 0xf4,
	0x6a, 0x0f, 0xc6, 0x41, 0x8b, 0xf9, 0x64, 0x34, 0x3c, 0x52, 0x7c, 0xb2, 0xfb, 0x28, 0x29, 0x3e,
	0x79, 0xbd, 0x13, 0x0a, 0x3e, 0xd9, 0xf5, 0x9b, 0x54, 0xf0, 0xc9, 0x2d, 0x1e, 0xa5, 0x82, 0xcf,
	0x39, 0x05, 0xa1, 0x38, 0xda, 0x65, 0x94, 0x75, 0xb2, 0xa2, 0x5d, 0x76, 0xe5, 0x28, 0x2b, 0xda,
	0xe5, 0xd4, 0x89, 0xcc, 0x09, 0x16, 0xc0, 0x8d, 0x9c, 0x12, 0x0a, 0xcb, 0xa0, 0x93, 0x53, 0x88,
	0xa9, 0x3d, 0x1a, 0x17, 0x35, 0xce, 0x27, 0xf2, 0x1b, 0x4b, 0xa9, 0x7c, 0xe2, 0xdc, 0xf6, 0x55,
	0x2a, 0x9f, 0x38, 0xbf, 0x63, 0x35, 0xc0, 0x3c, 0xb3, 0x6d, 0x94, 0xcd, 0x3c, 0xaf, 0x77, 0x95,
	0xcd, 0x3c, 0xb7, 0x1f, 0x65, 0x4e, 0xb0, 0x5f, 0xc0, 0x62, 0xa2, 0xb1, 0xc4, 0xee, 0x66, 0x9e,
	0xd6, 0x10, 0x1b, 0x73, 0x14, 0x4a, 0x9c, 0xa9, 0x66, 0x56, 0x7e, 0x53, 0x99, 0x6a, 0x5e, 0xf1,
	0xba, 0xb6, 0x3a, 0x1e, 0x22, 0x71, 0x6b, 0x50, 0xe3, 0x2c, 0xc9, 0xea, 0x5e, 0x5a, 0xd2, 0x0c,
	0x3e, 0xf7, 0xc7, 0xc0, 0x22, 0x26, 0x7f, 0x00, 0x4b, 0xa9, 0x6a, 0x2d, 0xfb, 0x38, 0xf9, 0x54,
	0xc8, 0x28, 0x0e, 0xd7, 0xee, 0x8d, 0x46, 0xea, 0x65, 0x1b, 0xe7, 0x56, 0x2b, 0x59, 0x7e, 0xe6,
	0x94, 0x5d, 0x1e, 0x4d, 0x65, 0x1b, 0x23, 0x8b, 0xa1, 0xea, 0x16, 0xe6, 0x54, 0xe6, 0x52, 0xb7,
	0x30, 0xbf, 0x2e, 0x99, 0xba, 0x85, 0xe7, 0x15, 0xfb, 0xc8, 0x5e, 0x32, 0x0b, 0x58, 0x29, 0x7b,
	0xc9, 0x2b, 0xd0, 0xa5, 0xec, 0x25, 0xbf, 0x1e, 0x46, 0x8e, 0x2d, 0xbb, 0xe8, 0x95, 0x72, 0x6c,
	0xb9, 0x85, 0xb3, 0x94, 0x63, 0xcb, 0xaf, 0xa2, 0x29, 0x86, 0xd9, 0xc5, 0x83, 0x14, 0xc3, 0xdc,
	0xa2, 0x46, 0x8a, 0x61, 0x7e, 0x35, 0x42, 0x19, 0x6b, 0xea, 0x05, 0x9e, 0x32, 0xd6, 0xac, 0xa7,
	0x7d, 0xca, 0x58, 0x33, 0x1f, 0xf2, 0xe6, 0xc4, 0xcb, 0xef, 0xfd, 0xe2, 0x59, 0xcb, 0x73, 0x6c,
	0xb7, 0xf5, 0xf4, 0xf3, 0xe7, 0x61, 0xf8, 0xb4, 0xe1, 0x75, 0x9e, 0xd1, 0xbf, 0x3e, 0x1b, 0x9e,
	0xf3, 0x4c, 0xf2, 0xa0, 0x2b, 0x1a, 0x5c, 0x0e, 0xff, 0x2b, 0xf4, 0x70, 0x96, 0x10, 0xbe, 0xff,
	0xff, 0x01, 0x00, 0x00, 0xff, 0xff, 0xac, 0x08, 0x16, 0xba, 0x3b, 0x3a, 0x00, 0x00,
}
