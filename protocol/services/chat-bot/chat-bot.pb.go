// Code generated by protoc-gen-go. DO NOT EDIT.
// source: chat-bot/chat-bot.proto

package chat_bot // import "golang.52tt.com/protocol/services/chat-bot"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// IM 消息类型
type ImMsgType int32

const (
	ImMsgType_ImMsgTypeUnknown ImMsgType = 0
	// 文本
	ImMsgType_ImMsgTypeText ImMsgType = 1
	// CUE
	ImMsgType_ImMsgTypeCue ImMsgType = 2
	// 表情
	ImMsgType_ImMsgTypeEmoticon ImMsgType = 3
	// 沉默(AI伴侣由活跃切为沉默)
	ImMsgType_ImMsgTypeSilence ImMsgType = 4
	// 传送门
	ImMsgType_ImMsgTypeAirTicket ImMsgType = 5
	// AI伴侣统一消息通道
	ImMsgType_ImMsgAIPartner ImMsgType = 6
)

var ImMsgType_name = map[int32]string{
	0: "ImMsgTypeUnknown",
	1: "ImMsgTypeText",
	2: "ImMsgTypeCue",
	3: "ImMsgTypeEmoticon",
	4: "ImMsgTypeSilence",
	5: "ImMsgTypeAirTicket",
	6: "ImMsgAIPartner",
}
var ImMsgType_value = map[string]int32{
	"ImMsgTypeUnknown":   0,
	"ImMsgTypeText":      1,
	"ImMsgTypeCue":       2,
	"ImMsgTypeEmoticon":  3,
	"ImMsgTypeSilence":   4,
	"ImMsgTypeAirTicket": 5,
	"ImMsgAIPartner":     6,
}

func (x ImMsgType) String() string {
	return proto.EnumName(ImMsgType_name, int32(x))
}
func (ImMsgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{0}
}

// 送审结果类型
type AuditResult int32

const (
	// 机审无法识别
	AuditResult_AuditResultReview AuditResult = 0
	// 通过
	AuditResult_AuditResultPass AuditResult = 1
	// 不通过
	AuditResult_AuditResultReject AuditResult = 2
)

var AuditResult_name = map[int32]string{
	0: "AuditResultReview",
	1: "AuditResultPass",
	2: "AuditResultReject",
}
var AuditResult_value = map[string]int32{
	"AuditResultReview": 0,
	"AuditResultPass":   1,
	"AuditResultReject": 2,
}

func (x AuditResult) String() string {
	return proto.EnumName(AuditResult_name, int32(x))
}
func (AuditResult) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{1}
}

// AI形象类型
type AIRoleType int32

const (
	// 树洞形象
	AIRoleType_AIRoleTypePartner AIRoleType = 0
	// 游戏形象
	AIRoleType_AIRoleTypeGame AIRoleType = 1
)

var AIRoleType_name = map[int32]string{
	0: "AIRoleTypePartner",
	1: "AIRoleTypeGame",
}
var AIRoleType_value = map[string]int32{
	"AIRoleTypePartner": 0,
	"AIRoleTypeGame":    1,
}

func (x AIRoleType) String() string {
	return proto.EnumName(AIRoleType_name, int32(x))
}
func (AIRoleType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{2}
}

type AIPartner_Relationship int32

const (
	AIPartner_RelationshipUnknown AIPartner_Relationship = 0
	// 朋友
	AIPartner_RelationshipFriend AIPartner_Relationship = 1
	// 恋人
	AIPartner_RelationshipLover AIPartner_Relationship = 2
)

var AIPartner_Relationship_name = map[int32]string{
	0: "RelationshipUnknown",
	1: "RelationshipFriend",
	2: "RelationshipLover",
}
var AIPartner_Relationship_value = map[string]int32{
	"RelationshipUnknown": 0,
	"RelationshipFriend":  1,
	"RelationshipLover":   2,
}

func (x AIPartner_Relationship) String() string {
	return proto.EnumName(AIPartner_Relationship_name, int32(x))
}
func (AIPartner_Relationship) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{6, 0}
}

type AIPartner_Source int32

const (
	// 用户创建
	AIPartner_SourceUser AIPartner_Source = 0
	// 去形象化创建
	AIPartner_SourceDeRole AIPartner_Source = 1
	// 游戏形象
	AIPartner_SourceGame AIPartner_Source = 2
)

var AIPartner_Source_name = map[int32]string{
	0: "SourceUser",
	1: "SourceDeRole",
	2: "SourceGame",
}
var AIPartner_Source_value = map[string]int32{
	"SourceUser":   0,
	"SourceDeRole": 1,
	"SourceGame":   2,
}

func (x AIPartner_Source) String() string {
	return proto.EnumName(AIPartner_Source_name, int32(x))
}
func (AIPartner_Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{6, 1}
}

// used by ImMsgTypeSilent
type ImMsgExtSilence struct {
	// 点击后变成活跃
	ActiveText string `protobuf:"bytes,1,opt,name=active_text,json=activeText,proto3" json:"active_text,omitempty"`
	// 点击后变成沉默
	SilentText           string   `protobuf:"bytes,2,opt,name=silent_text,json=silentText,proto3" json:"silent_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImMsgExtSilence) Reset()         { *m = ImMsgExtSilence{} }
func (m *ImMsgExtSilence) String() string { return proto.CompactTextString(m) }
func (*ImMsgExtSilence) ProtoMessage()    {}
func (*ImMsgExtSilence) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{0}
}
func (m *ImMsgExtSilence) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImMsgExtSilence.Unmarshal(m, b)
}
func (m *ImMsgExtSilence) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImMsgExtSilence.Marshal(b, m, deterministic)
}
func (dst *ImMsgExtSilence) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImMsgExtSilence.Merge(dst, src)
}
func (m *ImMsgExtSilence) XXX_Size() int {
	return xxx_messageInfo_ImMsgExtSilence.Size(m)
}
func (m *ImMsgExtSilence) XXX_DiscardUnknown() {
	xxx_messageInfo_ImMsgExtSilence.DiscardUnknown(m)
}

var xxx_messageInfo_ImMsgExtSilence proto.InternalMessageInfo

func (m *ImMsgExtSilence) GetActiveText() string {
	if m != nil {
		return m.ActiveText
	}
	return ""
}

func (m *ImMsgExtSilence) GetSilentText() string {
	if m != nil {
		return m.SilentText
	}
	return ""
}

// used by ImMsgTypeAirTicket
type ImMsgExtAirTicket struct {
	ChannelId uint32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	// 房间麦位模式 see ga.EChannelMicMode
	ChannelMode uint32 `protobuf:"varint,2,opt,name=channel_mode,json=channelMode,proto3" json:"channel_mode,omitempty"`
	// 房间类型 see ga.ChannelType
	ChannelType uint32 `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	// 房间外显id
	ChannelViewId string `protobuf:"bytes,4,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	// 房主account
	ChannelCreatorAccount string `protobuf:"bytes,5,opt,name=channel_creator_account,json=channelCreatorAccount,proto3" json:"channel_creator_account,omitempty"`
	// 房间名称
	ChannelName string `protobuf:"bytes,6,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	// 玩法id
	TabId uint32 `protobuf:"varint,7,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	// 玩法名称(用于拼接im消息外显文案)
	TabName string `protobuf:"bytes,8,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	// 传送门标题
	Title string `protobuf:"bytes,9,opt,name=title,proto3" json:"title,omitempty"`
	// 传送门内容
	Content              string   `protobuf:"bytes,10,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImMsgExtAirTicket) Reset()         { *m = ImMsgExtAirTicket{} }
func (m *ImMsgExtAirTicket) String() string { return proto.CompactTextString(m) }
func (*ImMsgExtAirTicket) ProtoMessage()    {}
func (*ImMsgExtAirTicket) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{1}
}
func (m *ImMsgExtAirTicket) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImMsgExtAirTicket.Unmarshal(m, b)
}
func (m *ImMsgExtAirTicket) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImMsgExtAirTicket.Marshal(b, m, deterministic)
}
func (dst *ImMsgExtAirTicket) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImMsgExtAirTicket.Merge(dst, src)
}
func (m *ImMsgExtAirTicket) XXX_Size() int {
	return xxx_messageInfo_ImMsgExtAirTicket.Size(m)
}
func (m *ImMsgExtAirTicket) XXX_DiscardUnknown() {
	xxx_messageInfo_ImMsgExtAirTicket.DiscardUnknown(m)
}

var xxx_messageInfo_ImMsgExtAirTicket proto.InternalMessageInfo

func (m *ImMsgExtAirTicket) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ImMsgExtAirTicket) GetChannelMode() uint32 {
	if m != nil {
		return m.ChannelMode
	}
	return 0
}

func (m *ImMsgExtAirTicket) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *ImMsgExtAirTicket) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

func (m *ImMsgExtAirTicket) GetChannelCreatorAccount() string {
	if m != nil {
		return m.ChannelCreatorAccount
	}
	return ""
}

func (m *ImMsgExtAirTicket) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *ImMsgExtAirTicket) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ImMsgExtAirTicket) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *ImMsgExtAirTicket) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ImMsgExtAirTicket) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type ImMsg struct {
	// 消息类型
	Type ImMsgType `protobuf:"varint,1,opt,name=type,proto3,enum=chat_bot.ImMsgType" json:"type,omitempty"`
	// 消息内容
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	// 扩展消息
	Ext []byte `protobuf:"bytes,3,opt,name=ext,proto3" json:"ext,omitempty"`
	// 消息发送时间(毫秒)
	SentAt int64 `protobuf:"varint,4,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	// 消息唯一标识
	MsgId string `protobuf:"bytes,5,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	// 消息来源类型 see ga.MsgSourceType
	MsgSourceType        uint32   `protobuf:"varint,6,opt,name=msg_source_type,json=msgSourceType,proto3" json:"msg_source_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImMsg) Reset()         { *m = ImMsg{} }
func (m *ImMsg) String() string { return proto.CompactTextString(m) }
func (*ImMsg) ProtoMessage()    {}
func (*ImMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{2}
}
func (m *ImMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImMsg.Unmarshal(m, b)
}
func (m *ImMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImMsg.Marshal(b, m, deterministic)
}
func (dst *ImMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImMsg.Merge(dst, src)
}
func (m *ImMsg) XXX_Size() int {
	return xxx_messageInfo_ImMsg.Size(m)
}
func (m *ImMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ImMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ImMsg proto.InternalMessageInfo

func (m *ImMsg) GetType() ImMsgType {
	if m != nil {
		return m.Type
	}
	return ImMsgType_ImMsgTypeUnknown
}

func (m *ImMsg) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ImMsg) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *ImMsg) GetSentAt() int64 {
	if m != nil {
		return m.SentAt
	}
	return 0
}

func (m *ImMsg) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *ImMsg) GetMsgSourceType() uint32 {
	if m != nil {
		return m.MsgSourceType
	}
	return 0
}

// AI形象卡信息
type AIRole struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// AI头像
	Avatar string `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// AI风格
	Style string `protobuf:"bytes,3,opt,name=style,proto3" json:"style,omitempty"`
	// AI性别 0:女 1:男
	Sex int32 `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	// AI头像大图
	Image string `protobuf:"bytes,5,opt,name=image,proto3" json:"image,omitempty"`
	// 介绍文案
	Intro string `protobuf:"bytes,6,opt,name=intro,proto3" json:"intro,omitempty"`
	// AI形象类型
	Type AIRoleType `protobuf:"varint,7,opt,name=type,proto3,enum=chat_bot.AIRoleType" json:"type,omitempty"`
	// 对话框颜色
	DialogColor          string   `protobuf:"bytes,8,opt,name=dialog_color,json=dialogColor,proto3" json:"dialog_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIRole) Reset()         { *m = AIRole{} }
func (m *AIRole) String() string { return proto.CompactTextString(m) }
func (*AIRole) ProtoMessage()    {}
func (*AIRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{3}
}
func (m *AIRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRole.Unmarshal(m, b)
}
func (m *AIRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRole.Marshal(b, m, deterministic)
}
func (dst *AIRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRole.Merge(dst, src)
}
func (m *AIRole) XXX_Size() int {
	return xxx_messageInfo_AIRole.Size(m)
}
func (m *AIRole) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRole.DiscardUnknown(m)
}

var xxx_messageInfo_AIRole proto.InternalMessageInfo

func (m *AIRole) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AIRole) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *AIRole) GetStyle() string {
	if m != nil {
		return m.Style
	}
	return ""
}

func (m *AIRole) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *AIRole) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *AIRole) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *AIRole) GetType() AIRoleType {
	if m != nil {
		return m.Type
	}
	return AIRoleType_AIRoleTypePartner
}

func (m *AIRole) GetDialogColor() string {
	if m != nil {
		return m.DialogColor
	}
	return ""
}

// AI形象立绘
type AIPhotograph struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Avatar               string   `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Image                string   `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIPhotograph) Reset()         { *m = AIPhotograph{} }
func (m *AIPhotograph) String() string { return proto.CompactTextString(m) }
func (*AIPhotograph) ProtoMessage()    {}
func (*AIPhotograph) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{4}
}
func (m *AIPhotograph) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIPhotograph.Unmarshal(m, b)
}
func (m *AIPhotograph) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIPhotograph.Marshal(b, m, deterministic)
}
func (dst *AIPhotograph) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIPhotograph.Merge(dst, src)
}
func (m *AIPhotograph) XXX_Size() int {
	return xxx_messageInfo_AIPhotograph.Size(m)
}
func (m *AIPhotograph) XXX_DiscardUnknown() {
	xxx_messageInfo_AIPhotograph.DiscardUnknown(m)
}

var xxx_messageInfo_AIPhotograph proto.InternalMessageInfo

func (m *AIPhotograph) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AIPhotograph) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *AIPhotograph) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

// AI关系
type AIRelationship struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIRelationship) Reset()         { *m = AIRelationship{} }
func (m *AIRelationship) String() string { return proto.CompactTextString(m) }
func (*AIRelationship) ProtoMessage()    {}
func (*AIRelationship) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{5}
}
func (m *AIRelationship) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRelationship.Unmarshal(m, b)
}
func (m *AIRelationship) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRelationship.Marshal(b, m, deterministic)
}
func (dst *AIRelationship) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRelationship.Merge(dst, src)
}
func (m *AIRelationship) XXX_Size() int {
	return xxx_messageInfo_AIRelationship.Size(m)
}
func (m *AIRelationship) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRelationship.DiscardUnknown(m)
}

var xxx_messageInfo_AIRelationship proto.InternalMessageInfo

func (m *AIRelationship) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AIRelationship) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// AI伴侣信息
type AIPartner struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// ta的名字
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 你希望ta怎么称呼你
	CallName string `protobuf:"bytes,3,opt,name=call_name,json=callName,proto3" json:"call_name,omitempty"`
	// AI伴侣形象卡
	Role *AIRole `protobuf:"bytes,4,opt,name=role,proto3" json:"role,omitempty"`
	// 你们的关系(废弃, 改成relation, 从中台获取)
	Relationship AIPartner_Relationship `protobuf:"varint,5,opt,name=relationship,proto3,enum=chat_bot.AIPartner_Relationship" json:"relationship,omitempty"`
	// [不再接收ta的消息]开关 true:打开 false:关闭
	Silent bool `protobuf:"varint,6,opt,name=silent,proto3" json:"silent,omitempty"`
	// AI伴侣所属用户id
	Uid uint32 `protobuf:"varint,7,opt,name=uid,proto3" json:"uid,omitempty"`
	// AI伴侣修改形象次数
	ChangeRoleCnt uint32 `protobuf:"varint,8,opt,name=change_role_cnt,json=changeRoleCnt,proto3" json:"change_role_cnt,omitempty"`
	// AI伴侣形象立绘
	Photograph *AIPhotograph `protobuf:"bytes,9,opt,name=photograph,proto3" json:"photograph,omitempty"`
	// AI伴侣关系
	Relation *AIRelationship `protobuf:"bytes,10,opt,name=relation,proto3" json:"relation,omitempty"`
	// AI伴侣来源
	Source               AIPartner_Source `protobuf:"varint,11,opt,name=source,proto3,enum=chat_bot.AIPartner_Source" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *AIPartner) Reset()         { *m = AIPartner{} }
func (m *AIPartner) String() string { return proto.CompactTextString(m) }
func (*AIPartner) ProtoMessage()    {}
func (*AIPartner) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{6}
}
func (m *AIPartner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIPartner.Unmarshal(m, b)
}
func (m *AIPartner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIPartner.Marshal(b, m, deterministic)
}
func (dst *AIPartner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIPartner.Merge(dst, src)
}
func (m *AIPartner) XXX_Size() int {
	return xxx_messageInfo_AIPartner.Size(m)
}
func (m *AIPartner) XXX_DiscardUnknown() {
	xxx_messageInfo_AIPartner.DiscardUnknown(m)
}

var xxx_messageInfo_AIPartner proto.InternalMessageInfo

func (m *AIPartner) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AIPartner) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AIPartner) GetCallName() string {
	if m != nil {
		return m.CallName
	}
	return ""
}

func (m *AIPartner) GetRole() *AIRole {
	if m != nil {
		return m.Role
	}
	return nil
}

func (m *AIPartner) GetRelationship() AIPartner_Relationship {
	if m != nil {
		return m.Relationship
	}
	return AIPartner_RelationshipUnknown
}

func (m *AIPartner) GetSilent() bool {
	if m != nil {
		return m.Silent
	}
	return false
}

func (m *AIPartner) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AIPartner) GetChangeRoleCnt() uint32 {
	if m != nil {
		return m.ChangeRoleCnt
	}
	return 0
}

func (m *AIPartner) GetPhotograph() *AIPhotograph {
	if m != nil {
		return m.Photograph
	}
	return nil
}

func (m *AIPartner) GetRelation() *AIRelationship {
	if m != nil {
		return m.Relation
	}
	return nil
}

func (m *AIPartner) GetSource() AIPartner_Source {
	if m != nil {
		return m.Source
	}
	return AIPartner_SourceUser
}

type AIPartnerPushMsg struct {
	// 推送类型(中台与WEB对接)
	Type uint32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	// 推送内容
	Data                 []byte   `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIPartnerPushMsg) Reset()         { *m = AIPartnerPushMsg{} }
func (m *AIPartnerPushMsg) String() string { return proto.CompactTextString(m) }
func (*AIPartnerPushMsg) ProtoMessage()    {}
func (*AIPartnerPushMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{7}
}
func (m *AIPartnerPushMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIPartnerPushMsg.Unmarshal(m, b)
}
func (m *AIPartnerPushMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIPartnerPushMsg.Marshal(b, m, deterministic)
}
func (dst *AIPartnerPushMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIPartnerPushMsg.Merge(dst, src)
}
func (m *AIPartnerPushMsg) XXX_Size() int {
	return xxx_messageInfo_AIPartnerPushMsg.Size(m)
}
func (m *AIPartnerPushMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_AIPartnerPushMsg.DiscardUnknown(m)
}

var xxx_messageInfo_AIPartnerPushMsg proto.InternalMessageInfo

func (m *AIPartnerPushMsg) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AIPartnerPushMsg) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type SendOption struct {
	// 是否发送离线推送
	WithOfflinePush bool `protobuf:"varint,1,opt,name=with_offline_push,json=withOfflinePush,proto3" json:"with_offline_push,omitempty"`
	// 离线推送内容
	OfflinePushContent   string   `protobuf:"bytes,2,opt,name=offline_push_content,json=offlinePushContent,proto3" json:"offline_push_content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendOption) Reset()         { *m = SendOption{} }
func (m *SendOption) String() string { return proto.CompactTextString(m) }
func (*SendOption) ProtoMessage()    {}
func (*SendOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{8}
}
func (m *SendOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendOption.Unmarshal(m, b)
}
func (m *SendOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendOption.Marshal(b, m, deterministic)
}
func (dst *SendOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendOption.Merge(dst, src)
}
func (m *SendOption) XXX_Size() int {
	return xxx_messageInfo_SendOption.Size(m)
}
func (m *SendOption) XXX_DiscardUnknown() {
	xxx_messageInfo_SendOption.DiscardUnknown(m)
}

var xxx_messageInfo_SendOption proto.InternalMessageInfo

func (m *SendOption) GetWithOfflinePush() bool {
	if m != nil {
		return m.WithOfflinePush
	}
	return false
}

func (m *SendOption) GetOfflinePushContent() string {
	if m != nil {
		return m.OfflinePushContent
	}
	return ""
}

type SendImMsgToUserReq struct {
	// 发送者 uid
	SenderUid uint32 `protobuf:"varint,1,opt,name=sender_uid,json=senderUid,proto3" json:"sender_uid,omitempty"`
	// 接收者 uid
	ReceiverUid uint32 `protobuf:"varint,2,opt,name=receiver_uid,json=receiverUid,proto3" json:"receiver_uid,omitempty"`
	// im 消息内容
	Msg                  *ImMsg   `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendImMsgToUserReq) Reset()         { *m = SendImMsgToUserReq{} }
func (m *SendImMsgToUserReq) String() string { return proto.CompactTextString(m) }
func (*SendImMsgToUserReq) ProtoMessage()    {}
func (*SendImMsgToUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{9}
}
func (m *SendImMsgToUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendImMsgToUserReq.Unmarshal(m, b)
}
func (m *SendImMsgToUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendImMsgToUserReq.Marshal(b, m, deterministic)
}
func (dst *SendImMsgToUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendImMsgToUserReq.Merge(dst, src)
}
func (m *SendImMsgToUserReq) XXX_Size() int {
	return xxx_messageInfo_SendImMsgToUserReq.Size(m)
}
func (m *SendImMsgToUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendImMsgToUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendImMsgToUserReq proto.InternalMessageInfo

func (m *SendImMsgToUserReq) GetSenderUid() uint32 {
	if m != nil {
		return m.SenderUid
	}
	return 0
}

func (m *SendImMsgToUserReq) GetReceiverUid() uint32 {
	if m != nil {
		return m.ReceiverUid
	}
	return 0
}

func (m *SendImMsgToUserReq) GetMsg() *ImMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

type SendImMsgToUserResp struct {
	// 发送内容审核结果
	AuditResult AuditResult `protobuf:"varint,1,opt,name=audit_result,json=auditResult,proto3,enum=chat_bot.AuditResult" json:"audit_result,omitempty"`
	// 接收者消息 id
	ReceiverMsgId uint64 `protobuf:"varint,2,opt,name=receiver_msg_id,json=receiverMsgId,proto3" json:"receiver_msg_id,omitempty"`
	// 发送者消息 id
	SenderMsgId          uint64   `protobuf:"varint,3,opt,name=sender_msg_id,json=senderMsgId,proto3" json:"sender_msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendImMsgToUserResp) Reset()         { *m = SendImMsgToUserResp{} }
func (m *SendImMsgToUserResp) String() string { return proto.CompactTextString(m) }
func (*SendImMsgToUserResp) ProtoMessage()    {}
func (*SendImMsgToUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{10}
}
func (m *SendImMsgToUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendImMsgToUserResp.Unmarshal(m, b)
}
func (m *SendImMsgToUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendImMsgToUserResp.Marshal(b, m, deterministic)
}
func (dst *SendImMsgToUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendImMsgToUserResp.Merge(dst, src)
}
func (m *SendImMsgToUserResp) XXX_Size() int {
	return xxx_messageInfo_SendImMsgToUserResp.Size(m)
}
func (m *SendImMsgToUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendImMsgToUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendImMsgToUserResp proto.InternalMessageInfo

func (m *SendImMsgToUserResp) GetAuditResult() AuditResult {
	if m != nil {
		return m.AuditResult
	}
	return AuditResult_AuditResultReview
}

func (m *SendImMsgToUserResp) GetReceiverMsgId() uint64 {
	if m != nil {
		return m.ReceiverMsgId
	}
	return 0
}

func (m *SendImMsgToUserResp) GetSenderMsgId() uint64 {
	if m != nil {
		return m.SenderMsgId
	}
	return 0
}

type Bot struct {
	Uid      uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Username string `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	// 克隆用户信息的uid
	CloneUid uint32 `protobuf:"varint,3,opt,name=clone_uid,json=cloneUid,proto3" json:"clone_uid,omitempty"`
	// 用户昵称
	Nickname string `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	// 用户性别
	Sex                  int32    `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Bot) Reset()         { *m = Bot{} }
func (m *Bot) String() string { return proto.CompactTextString(m) }
func (*Bot) ProtoMessage()    {}
func (*Bot) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{11}
}
func (m *Bot) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Bot.Unmarshal(m, b)
}
func (m *Bot) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Bot.Marshal(b, m, deterministic)
}
func (dst *Bot) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Bot.Merge(dst, src)
}
func (m *Bot) XXX_Size() int {
	return xxx_messageInfo_Bot.Size(m)
}
func (m *Bot) XXX_DiscardUnknown() {
	xxx_messageInfo_Bot.DiscardUnknown(m)
}

var xxx_messageInfo_Bot proto.InternalMessageInfo

func (m *Bot) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Bot) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *Bot) GetCloneUid() uint32 {
	if m != nil {
		return m.CloneUid
	}
	return 0
}

func (m *Bot) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *Bot) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type CreateChatBotReq struct {
	Bot                  *Bot     `protobuf:"bytes,1,opt,name=bot,proto3" json:"bot,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateChatBotReq) Reset()         { *m = CreateChatBotReq{} }
func (m *CreateChatBotReq) String() string { return proto.CompactTextString(m) }
func (*CreateChatBotReq) ProtoMessage()    {}
func (*CreateChatBotReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{12}
}
func (m *CreateChatBotReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateChatBotReq.Unmarshal(m, b)
}
func (m *CreateChatBotReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateChatBotReq.Marshal(b, m, deterministic)
}
func (dst *CreateChatBotReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateChatBotReq.Merge(dst, src)
}
func (m *CreateChatBotReq) XXX_Size() int {
	return xxx_messageInfo_CreateChatBotReq.Size(m)
}
func (m *CreateChatBotReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateChatBotReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateChatBotReq proto.InternalMessageInfo

func (m *CreateChatBotReq) GetBot() *Bot {
	if m != nil {
		return m.Bot
	}
	return nil
}

type CreateChatBotResp struct {
	Bot                  *Bot     `protobuf:"bytes,1,opt,name=bot,proto3" json:"bot,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateChatBotResp) Reset()         { *m = CreateChatBotResp{} }
func (m *CreateChatBotResp) String() string { return proto.CompactTextString(m) }
func (*CreateChatBotResp) ProtoMessage()    {}
func (*CreateChatBotResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{13}
}
func (m *CreateChatBotResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateChatBotResp.Unmarshal(m, b)
}
func (m *CreateChatBotResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateChatBotResp.Marshal(b, m, deterministic)
}
func (dst *CreateChatBotResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateChatBotResp.Merge(dst, src)
}
func (m *CreateChatBotResp) XXX_Size() int {
	return xxx_messageInfo_CreateChatBotResp.Size(m)
}
func (m *CreateChatBotResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateChatBotResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateChatBotResp proto.InternalMessageInfo

func (m *CreateChatBotResp) GetBot() *Bot {
	if m != nil {
		return m.Bot
	}
	return nil
}

type MarkImMsgReadReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MsgId                uint32   `protobuf:"varint,2,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	PeerUid              uint32   `protobuf:"varint,3,opt,name=peer_uid,json=peerUid,proto3" json:"peer_uid,omitempty"`
	PeerMsgId            uint32   `protobuf:"varint,4,opt,name=peer_msg_id,json=peerMsgId,proto3" json:"peer_msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkImMsgReadReq) Reset()         { *m = MarkImMsgReadReq{} }
func (m *MarkImMsgReadReq) String() string { return proto.CompactTextString(m) }
func (*MarkImMsgReadReq) ProtoMessage()    {}
func (*MarkImMsgReadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{14}
}
func (m *MarkImMsgReadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkImMsgReadReq.Unmarshal(m, b)
}
func (m *MarkImMsgReadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkImMsgReadReq.Marshal(b, m, deterministic)
}
func (dst *MarkImMsgReadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkImMsgReadReq.Merge(dst, src)
}
func (m *MarkImMsgReadReq) XXX_Size() int {
	return xxx_messageInfo_MarkImMsgReadReq.Size(m)
}
func (m *MarkImMsgReadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkImMsgReadReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkImMsgReadReq proto.InternalMessageInfo

func (m *MarkImMsgReadReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MarkImMsgReadReq) GetMsgId() uint32 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *MarkImMsgReadReq) GetPeerUid() uint32 {
	if m != nil {
		return m.PeerUid
	}
	return 0
}

func (m *MarkImMsgReadReq) GetPeerMsgId() uint32 {
	if m != nil {
		return m.PeerMsgId
	}
	return 0
}

type MarkImMsgReadResp struct {
	PeerReadMsgId        uint32   `protobuf:"varint,1,opt,name=peer_read_msg_id,json=peerReadMsgId,proto3" json:"peer_read_msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkImMsgReadResp) Reset()         { *m = MarkImMsgReadResp{} }
func (m *MarkImMsgReadResp) String() string { return proto.CompactTextString(m) }
func (*MarkImMsgReadResp) ProtoMessage()    {}
func (*MarkImMsgReadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{15}
}
func (m *MarkImMsgReadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkImMsgReadResp.Unmarshal(m, b)
}
func (m *MarkImMsgReadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkImMsgReadResp.Marshal(b, m, deterministic)
}
func (dst *MarkImMsgReadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkImMsgReadResp.Merge(dst, src)
}
func (m *MarkImMsgReadResp) XXX_Size() int {
	return xxx_messageInfo_MarkImMsgReadResp.Size(m)
}
func (m *MarkImMsgReadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkImMsgReadResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkImMsgReadResp proto.InternalMessageInfo

func (m *MarkImMsgReadResp) GetPeerReadMsgId() uint32 {
	if m != nil {
		return m.PeerReadMsgId
	}
	return 0
}

type GetAIRoleListReq struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Type                 AIRoleType `protobuf:"varint,2,opt,name=type,proto3,enum=chat_bot.AIRoleType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetAIRoleListReq) Reset()         { *m = GetAIRoleListReq{} }
func (m *GetAIRoleListReq) String() string { return proto.CompactTextString(m) }
func (*GetAIRoleListReq) ProtoMessage()    {}
func (*GetAIRoleListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{16}
}
func (m *GetAIRoleListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIRoleListReq.Unmarshal(m, b)
}
func (m *GetAIRoleListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIRoleListReq.Marshal(b, m, deterministic)
}
func (dst *GetAIRoleListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIRoleListReq.Merge(dst, src)
}
func (m *GetAIRoleListReq) XXX_Size() int {
	return xxx_messageInfo_GetAIRoleListReq.Size(m)
}
func (m *GetAIRoleListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIRoleListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIRoleListReq proto.InternalMessageInfo

func (m *GetAIRoleListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAIRoleListReq) GetType() AIRoleType {
	if m != nil {
		return m.Type
	}
	return AIRoleType_AIRoleTypePartner
}

type GetAIRoleListResp struct {
	Roles                []*AIRole `protobuf:"bytes,1,rep,name=roles,proto3" json:"roles,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetAIRoleListResp) Reset()         { *m = GetAIRoleListResp{} }
func (m *GetAIRoleListResp) String() string { return proto.CompactTextString(m) }
func (*GetAIRoleListResp) ProtoMessage()    {}
func (*GetAIRoleListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{17}
}
func (m *GetAIRoleListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIRoleListResp.Unmarshal(m, b)
}
func (m *GetAIRoleListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIRoleListResp.Marshal(b, m, deterministic)
}
func (dst *GetAIRoleListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIRoleListResp.Merge(dst, src)
}
func (m *GetAIRoleListResp) XXX_Size() int {
	return xxx_messageInfo_GetAIRoleListResp.Size(m)
}
func (m *GetAIRoleListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIRoleListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIRoleListResp proto.InternalMessageInfo

func (m *GetAIRoleListResp) GetRoles() []*AIRole {
	if m != nil {
		return m.Roles
	}
	return nil
}

type UpsertAIPartnerReq struct {
	Partner              *AIPartner `protobuf:"bytes,2,opt,name=partner,proto3" json:"partner,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UpsertAIPartnerReq) Reset()         { *m = UpsertAIPartnerReq{} }
func (m *UpsertAIPartnerReq) String() string { return proto.CompactTextString(m) }
func (*UpsertAIPartnerReq) ProtoMessage()    {}
func (*UpsertAIPartnerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{18}
}
func (m *UpsertAIPartnerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertAIPartnerReq.Unmarshal(m, b)
}
func (m *UpsertAIPartnerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertAIPartnerReq.Marshal(b, m, deterministic)
}
func (dst *UpsertAIPartnerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertAIPartnerReq.Merge(dst, src)
}
func (m *UpsertAIPartnerReq) XXX_Size() int {
	return xxx_messageInfo_UpsertAIPartnerReq.Size(m)
}
func (m *UpsertAIPartnerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertAIPartnerReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertAIPartnerReq proto.InternalMessageInfo

func (m *UpsertAIPartnerReq) GetPartner() *AIPartner {
	if m != nil {
		return m.Partner
	}
	return nil
}

type UpsertAIPartnerResp struct {
	Partner              *AIPartner  `protobuf:"bytes,1,opt,name=partner,proto3" json:"partner,omitempty"`
	NameResult           AuditResult `protobuf:"varint,2,opt,name=name_result,json=nameResult,proto3,enum=chat_bot.AuditResult" json:"name_result,omitempty"`
	CallNameResult       AuditResult `protobuf:"varint,3,opt,name=call_name_result,json=callNameResult,proto3,enum=chat_bot.AuditResult" json:"call_name_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UpsertAIPartnerResp) Reset()         { *m = UpsertAIPartnerResp{} }
func (m *UpsertAIPartnerResp) String() string { return proto.CompactTextString(m) }
func (*UpsertAIPartnerResp) ProtoMessage()    {}
func (*UpsertAIPartnerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{19}
}
func (m *UpsertAIPartnerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertAIPartnerResp.Unmarshal(m, b)
}
func (m *UpsertAIPartnerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertAIPartnerResp.Marshal(b, m, deterministic)
}
func (dst *UpsertAIPartnerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertAIPartnerResp.Merge(dst, src)
}
func (m *UpsertAIPartnerResp) XXX_Size() int {
	return xxx_messageInfo_UpsertAIPartnerResp.Size(m)
}
func (m *UpsertAIPartnerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertAIPartnerResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertAIPartnerResp proto.InternalMessageInfo

func (m *UpsertAIPartnerResp) GetPartner() *AIPartner {
	if m != nil {
		return m.Partner
	}
	return nil
}

func (m *UpsertAIPartnerResp) GetNameResult() AuditResult {
	if m != nil {
		return m.NameResult
	}
	return AuditResult_AuditResultReview
}

func (m *UpsertAIPartnerResp) GetCallNameResult() AuditResult {
	if m != nil {
		return m.CallNameResult
	}
	return AuditResult_AuditResultReview
}

type GetOption struct {
	WithRcmdInfo         bool     `protobuf:"varint,1,opt,name=with_rcmd_info,json=withRcmdInfo,proto3" json:"with_rcmd_info,omitempty"`
	WithDefaultName      bool     `protobuf:"varint,2,opt,name=with_default_name,json=withDefaultName,proto3" json:"with_default_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOption) Reset()         { *m = GetOption{} }
func (m *GetOption) String() string { return proto.CompactTextString(m) }
func (*GetOption) ProtoMessage()    {}
func (*GetOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{20}
}
func (m *GetOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOption.Unmarshal(m, b)
}
func (m *GetOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOption.Marshal(b, m, deterministic)
}
func (dst *GetOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOption.Merge(dst, src)
}
func (m *GetOption) XXX_Size() int {
	return xxx_messageInfo_GetOption.Size(m)
}
func (m *GetOption) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOption.DiscardUnknown(m)
}

var xxx_messageInfo_GetOption proto.InternalMessageInfo

func (m *GetOption) GetWithRcmdInfo() bool {
	if m != nil {
		return m.WithRcmdInfo
	}
	return false
}

func (m *GetOption) GetWithDefaultName() bool {
	if m != nil {
		return m.WithDefaultName
	}
	return false
}

type GetUserAIPartnerReq struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleType             AIRoleType `protobuf:"varint,2,opt,name=role_type,json=roleType,proto3,enum=chat_bot.AIRoleType" json:"role_type,omitempty"`
	RoleId               uint32     `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	Opt                  *GetOption `protobuf:"bytes,4,opt,name=opt,proto3" json:"opt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetUserAIPartnerReq) Reset()         { *m = GetUserAIPartnerReq{} }
func (m *GetUserAIPartnerReq) String() string { return proto.CompactTextString(m) }
func (*GetUserAIPartnerReq) ProtoMessage()    {}
func (*GetUserAIPartnerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{21}
}
func (m *GetUserAIPartnerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAIPartnerReq.Unmarshal(m, b)
}
func (m *GetUserAIPartnerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAIPartnerReq.Marshal(b, m, deterministic)
}
func (dst *GetUserAIPartnerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAIPartnerReq.Merge(dst, src)
}
func (m *GetUserAIPartnerReq) XXX_Size() int {
	return xxx_messageInfo_GetUserAIPartnerReq.Size(m)
}
func (m *GetUserAIPartnerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAIPartnerReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAIPartnerReq proto.InternalMessageInfo

func (m *GetUserAIPartnerReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserAIPartnerReq) GetRoleType() AIRoleType {
	if m != nil {
		return m.RoleType
	}
	return AIRoleType_AIRoleTypePartner
}

func (m *GetUserAIPartnerReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetUserAIPartnerReq) GetOpt() *GetOption {
	if m != nil {
		return m.Opt
	}
	return nil
}

type GetUserAIPartnerResp struct {
	Partner              *AIPartner `protobuf:"bytes,1,opt,name=partner,proto3" json:"partner,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetUserAIPartnerResp) Reset()         { *m = GetUserAIPartnerResp{} }
func (m *GetUserAIPartnerResp) String() string { return proto.CompactTextString(m) }
func (*GetUserAIPartnerResp) ProtoMessage()    {}
func (*GetUserAIPartnerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{22}
}
func (m *GetUserAIPartnerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAIPartnerResp.Unmarshal(m, b)
}
func (m *GetUserAIPartnerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAIPartnerResp.Marshal(b, m, deterministic)
}
func (dst *GetUserAIPartnerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAIPartnerResp.Merge(dst, src)
}
func (m *GetUserAIPartnerResp) XXX_Size() int {
	return xxx_messageInfo_GetUserAIPartnerResp.Size(m)
}
func (m *GetUserAIPartnerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAIPartnerResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAIPartnerResp proto.InternalMessageInfo

func (m *GetUserAIPartnerResp) GetPartner() *AIPartner {
	if m != nil {
		return m.Partner
	}
	return nil
}

type UpdateAIPartnerChatStateReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// AI伴侣id
	Id uint32 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// true:免打扰 false:可打扰
	Silent               bool     `protobuf:"varint,3,opt,name=silent,proto3" json:"silent,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAIPartnerChatStateReq) Reset()         { *m = UpdateAIPartnerChatStateReq{} }
func (m *UpdateAIPartnerChatStateReq) String() string { return proto.CompactTextString(m) }
func (*UpdateAIPartnerChatStateReq) ProtoMessage()    {}
func (*UpdateAIPartnerChatStateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{23}
}
func (m *UpdateAIPartnerChatStateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIPartnerChatStateReq.Unmarshal(m, b)
}
func (m *UpdateAIPartnerChatStateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIPartnerChatStateReq.Marshal(b, m, deterministic)
}
func (dst *UpdateAIPartnerChatStateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIPartnerChatStateReq.Merge(dst, src)
}
func (m *UpdateAIPartnerChatStateReq) XXX_Size() int {
	return xxx_messageInfo_UpdateAIPartnerChatStateReq.Size(m)
}
func (m *UpdateAIPartnerChatStateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIPartnerChatStateReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIPartnerChatStateReq proto.InternalMessageInfo

func (m *UpdateAIPartnerChatStateReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateAIPartnerChatStateReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateAIPartnerChatStateReq) GetSilent() bool {
	if m != nil {
		return m.Silent
	}
	return false
}

type UpdateAIPartnerChatStateResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAIPartnerChatStateResp) Reset()         { *m = UpdateAIPartnerChatStateResp{} }
func (m *UpdateAIPartnerChatStateResp) String() string { return proto.CompactTextString(m) }
func (*UpdateAIPartnerChatStateResp) ProtoMessage()    {}
func (*UpdateAIPartnerChatStateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{24}
}
func (m *UpdateAIPartnerChatStateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIPartnerChatStateResp.Unmarshal(m, b)
}
func (m *UpdateAIPartnerChatStateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIPartnerChatStateResp.Marshal(b, m, deterministic)
}
func (dst *UpdateAIPartnerChatStateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIPartnerChatStateResp.Merge(dst, src)
}
func (m *UpdateAIPartnerChatStateResp) XXX_Size() int {
	return xxx_messageInfo_UpdateAIPartnerChatStateResp.Size(m)
}
func (m *UpdateAIPartnerChatStateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIPartnerChatStateResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIPartnerChatStateResp proto.InternalMessageInfo

type ChangeUserAIPartnerRoleReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeUserAIPartnerRoleReq) Reset()         { *m = ChangeUserAIPartnerRoleReq{} }
func (m *ChangeUserAIPartnerRoleReq) String() string { return proto.CompactTextString(m) }
func (*ChangeUserAIPartnerRoleReq) ProtoMessage()    {}
func (*ChangeUserAIPartnerRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{25}
}
func (m *ChangeUserAIPartnerRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeUserAIPartnerRoleReq.Unmarshal(m, b)
}
func (m *ChangeUserAIPartnerRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeUserAIPartnerRoleReq.Marshal(b, m, deterministic)
}
func (dst *ChangeUserAIPartnerRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeUserAIPartnerRoleReq.Merge(dst, src)
}
func (m *ChangeUserAIPartnerRoleReq) XXX_Size() int {
	return xxx_messageInfo_ChangeUserAIPartnerRoleReq.Size(m)
}
func (m *ChangeUserAIPartnerRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeUserAIPartnerRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeUserAIPartnerRoleReq proto.InternalMessageInfo

func (m *ChangeUserAIPartnerRoleReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChangeUserAIPartnerRoleReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ChangeUserAIPartnerRoleReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type ChangeUserAIPartnerRoleResp struct {
	Partner              *AIPartner `protobuf:"bytes,1,opt,name=partner,proto3" json:"partner,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ChangeUserAIPartnerRoleResp) Reset()         { *m = ChangeUserAIPartnerRoleResp{} }
func (m *ChangeUserAIPartnerRoleResp) String() string { return proto.CompactTextString(m) }
func (*ChangeUserAIPartnerRoleResp) ProtoMessage()    {}
func (*ChangeUserAIPartnerRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{26}
}
func (m *ChangeUserAIPartnerRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeUserAIPartnerRoleResp.Unmarshal(m, b)
}
func (m *ChangeUserAIPartnerRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeUserAIPartnerRoleResp.Marshal(b, m, deterministic)
}
func (dst *ChangeUserAIPartnerRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeUserAIPartnerRoleResp.Merge(dst, src)
}
func (m *ChangeUserAIPartnerRoleResp) XXX_Size() int {
	return xxx_messageInfo_ChangeUserAIPartnerRoleResp.Size(m)
}
func (m *ChangeUserAIPartnerRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeUserAIPartnerRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeUserAIPartnerRoleResp proto.InternalMessageInfo

func (m *ChangeUserAIPartnerRoleResp) GetPartner() *AIPartner {
	if m != nil {
		return m.Partner
	}
	return nil
}

type GetAIPartnerReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAIPartnerReq) Reset()         { *m = GetAIPartnerReq{} }
func (m *GetAIPartnerReq) String() string { return proto.CompactTextString(m) }
func (*GetAIPartnerReq) ProtoMessage()    {}
func (*GetAIPartnerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{27}
}
func (m *GetAIPartnerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIPartnerReq.Unmarshal(m, b)
}
func (m *GetAIPartnerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIPartnerReq.Marshal(b, m, deterministic)
}
func (dst *GetAIPartnerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIPartnerReq.Merge(dst, src)
}
func (m *GetAIPartnerReq) XXX_Size() int {
	return xxx_messageInfo_GetAIPartnerReq.Size(m)
}
func (m *GetAIPartnerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIPartnerReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIPartnerReq proto.InternalMessageInfo

func (m *GetAIPartnerReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetAIPartnerResp struct {
	Partner              *AIPartner `protobuf:"bytes,1,opt,name=partner,proto3" json:"partner,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetAIPartnerResp) Reset()         { *m = GetAIPartnerResp{} }
func (m *GetAIPartnerResp) String() string { return proto.CompactTextString(m) }
func (*GetAIPartnerResp) ProtoMessage()    {}
func (*GetAIPartnerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{28}
}
func (m *GetAIPartnerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIPartnerResp.Unmarshal(m, b)
}
func (m *GetAIPartnerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIPartnerResp.Marshal(b, m, deterministic)
}
func (dst *GetAIPartnerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIPartnerResp.Merge(dst, src)
}
func (m *GetAIPartnerResp) XXX_Size() int {
	return xxx_messageInfo_GetAIPartnerResp.Size(m)
}
func (m *GetAIPartnerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIPartnerResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIPartnerResp proto.InternalMessageInfo

func (m *GetAIPartnerResp) GetPartner() *AIPartner {
	if m != nil {
		return m.Partner
	}
	return nil
}

type SendImMsgUserToAIReq struct {
	// 用户id
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// AI伴侣id
	PartnerId uint32 `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	// 消息内容
	Msg                  *ImMsg   `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendImMsgUserToAIReq) Reset()         { *m = SendImMsgUserToAIReq{} }
func (m *SendImMsgUserToAIReq) String() string { return proto.CompactTextString(m) }
func (*SendImMsgUserToAIReq) ProtoMessage()    {}
func (*SendImMsgUserToAIReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{29}
}
func (m *SendImMsgUserToAIReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendImMsgUserToAIReq.Unmarshal(m, b)
}
func (m *SendImMsgUserToAIReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendImMsgUserToAIReq.Marshal(b, m, deterministic)
}
func (dst *SendImMsgUserToAIReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendImMsgUserToAIReq.Merge(dst, src)
}
func (m *SendImMsgUserToAIReq) XXX_Size() int {
	return xxx_messageInfo_SendImMsgUserToAIReq.Size(m)
}
func (m *SendImMsgUserToAIReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendImMsgUserToAIReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendImMsgUserToAIReq proto.InternalMessageInfo

func (m *SendImMsgUserToAIReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendImMsgUserToAIReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *SendImMsgUserToAIReq) GetMsg() *ImMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

type SendImMsgUserToAIResp struct {
	// 消息发送时间(毫秒)
	SentAt int64 `protobuf:"varint,1,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	// 消息唯一标识
	MsgId                string   `protobuf:"bytes,2,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendImMsgUserToAIResp) Reset()         { *m = SendImMsgUserToAIResp{} }
func (m *SendImMsgUserToAIResp) String() string { return proto.CompactTextString(m) }
func (*SendImMsgUserToAIResp) ProtoMessage()    {}
func (*SendImMsgUserToAIResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{30}
}
func (m *SendImMsgUserToAIResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendImMsgUserToAIResp.Unmarshal(m, b)
}
func (m *SendImMsgUserToAIResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendImMsgUserToAIResp.Marshal(b, m, deterministic)
}
func (dst *SendImMsgUserToAIResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendImMsgUserToAIResp.Merge(dst, src)
}
func (m *SendImMsgUserToAIResp) XXX_Size() int {
	return xxx_messageInfo_SendImMsgUserToAIResp.Size(m)
}
func (m *SendImMsgUserToAIResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendImMsgUserToAIResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendImMsgUserToAIResp proto.InternalMessageInfo

func (m *SendImMsgUserToAIResp) GetSentAt() int64 {
	if m != nil {
		return m.SentAt
	}
	return 0
}

func (m *SendImMsgUserToAIResp) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

type SendImMsgAIToUserReq struct {
	// AI伴侣id
	PartnerId uint32 `protobuf:"varint,1,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	// 用户id
	Uid uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Msg *ImMsg `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg,omitempty"`
	// 发送消息可选参数
	Opt                  *SendOption `protobuf:"bytes,4,opt,name=opt,proto3" json:"opt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SendImMsgAIToUserReq) Reset()         { *m = SendImMsgAIToUserReq{} }
func (m *SendImMsgAIToUserReq) String() string { return proto.CompactTextString(m) }
func (*SendImMsgAIToUserReq) ProtoMessage()    {}
func (*SendImMsgAIToUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{31}
}
func (m *SendImMsgAIToUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendImMsgAIToUserReq.Unmarshal(m, b)
}
func (m *SendImMsgAIToUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendImMsgAIToUserReq.Marshal(b, m, deterministic)
}
func (dst *SendImMsgAIToUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendImMsgAIToUserReq.Merge(dst, src)
}
func (m *SendImMsgAIToUserReq) XXX_Size() int {
	return xxx_messageInfo_SendImMsgAIToUserReq.Size(m)
}
func (m *SendImMsgAIToUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendImMsgAIToUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendImMsgAIToUserReq proto.InternalMessageInfo

func (m *SendImMsgAIToUserReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *SendImMsgAIToUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendImMsgAIToUserReq) GetMsg() *ImMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *SendImMsgAIToUserReq) GetOpt() *SendOption {
	if m != nil {
		return m.Opt
	}
	return nil
}

type SendImMsgAIToUserResp struct {
	Result AuditResult `protobuf:"varint,1,opt,name=result,proto3,enum=chat_bot.AuditResult" json:"result,omitempty"`
	// 消息唯一标识
	MsgId                string   `protobuf:"bytes,3,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendImMsgAIToUserResp) Reset()         { *m = SendImMsgAIToUserResp{} }
func (m *SendImMsgAIToUserResp) String() string { return proto.CompactTextString(m) }
func (*SendImMsgAIToUserResp) ProtoMessage()    {}
func (*SendImMsgAIToUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{32}
}
func (m *SendImMsgAIToUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendImMsgAIToUserResp.Unmarshal(m, b)
}
func (m *SendImMsgAIToUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendImMsgAIToUserResp.Marshal(b, m, deterministic)
}
func (dst *SendImMsgAIToUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendImMsgAIToUserResp.Merge(dst, src)
}
func (m *SendImMsgAIToUserResp) XXX_Size() int {
	return xxx_messageInfo_SendImMsgAIToUserResp.Size(m)
}
func (m *SendImMsgAIToUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendImMsgAIToUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendImMsgAIToUserResp proto.InternalMessageInfo

func (m *SendImMsgAIToUserResp) GetResult() AuditResult {
	if m != nil {
		return m.Result
	}
	return AuditResult_AuditResultReview
}

func (m *SendImMsgAIToUserResp) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

type ReportUserEnterAIPartnerChatReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportUserEnterAIPartnerChatReq) Reset()         { *m = ReportUserEnterAIPartnerChatReq{} }
func (m *ReportUserEnterAIPartnerChatReq) String() string { return proto.CompactTextString(m) }
func (*ReportUserEnterAIPartnerChatReq) ProtoMessage()    {}
func (*ReportUserEnterAIPartnerChatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{33}
}
func (m *ReportUserEnterAIPartnerChatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportUserEnterAIPartnerChatReq.Unmarshal(m, b)
}
func (m *ReportUserEnterAIPartnerChatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportUserEnterAIPartnerChatReq.Marshal(b, m, deterministic)
}
func (dst *ReportUserEnterAIPartnerChatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportUserEnterAIPartnerChatReq.Merge(dst, src)
}
func (m *ReportUserEnterAIPartnerChatReq) XXX_Size() int {
	return xxx_messageInfo_ReportUserEnterAIPartnerChatReq.Size(m)
}
func (m *ReportUserEnterAIPartnerChatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportUserEnterAIPartnerChatReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportUserEnterAIPartnerChatReq proto.InternalMessageInfo

func (m *ReportUserEnterAIPartnerChatReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportUserEnterAIPartnerChatReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

type ReportUserEnterAIPartnerChatResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportUserEnterAIPartnerChatResp) Reset()         { *m = ReportUserEnterAIPartnerChatResp{} }
func (m *ReportUserEnterAIPartnerChatResp) String() string { return proto.CompactTextString(m) }
func (*ReportUserEnterAIPartnerChatResp) ProtoMessage()    {}
func (*ReportUserEnterAIPartnerChatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{34}
}
func (m *ReportUserEnterAIPartnerChatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportUserEnterAIPartnerChatResp.Unmarshal(m, b)
}
func (m *ReportUserEnterAIPartnerChatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportUserEnterAIPartnerChatResp.Marshal(b, m, deterministic)
}
func (dst *ReportUserEnterAIPartnerChatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportUserEnterAIPartnerChatResp.Merge(dst, src)
}
func (m *ReportUserEnterAIPartnerChatResp) XXX_Size() int {
	return xxx_messageInfo_ReportUserEnterAIPartnerChatResp.Size(m)
}
func (m *ReportUserEnterAIPartnerChatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportUserEnterAIPartnerChatResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportUserEnterAIPartnerChatResp proto.InternalMessageInfo

type GetAIPartnerMsgListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAIPartnerMsgListReq) Reset()         { *m = GetAIPartnerMsgListReq{} }
func (m *GetAIPartnerMsgListReq) String() string { return proto.CompactTextString(m) }
func (*GetAIPartnerMsgListReq) ProtoMessage()    {}
func (*GetAIPartnerMsgListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{35}
}
func (m *GetAIPartnerMsgListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIPartnerMsgListReq.Unmarshal(m, b)
}
func (m *GetAIPartnerMsgListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIPartnerMsgListReq.Marshal(b, m, deterministic)
}
func (dst *GetAIPartnerMsgListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIPartnerMsgListReq.Merge(dst, src)
}
func (m *GetAIPartnerMsgListReq) XXX_Size() int {
	return xxx_messageInfo_GetAIPartnerMsgListReq.Size(m)
}
func (m *GetAIPartnerMsgListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIPartnerMsgListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIPartnerMsgListReq proto.InternalMessageInfo

func (m *GetAIPartnerMsgListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAIPartnerMsgListReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

type GetAIPartnerMsgListResp struct {
	Msgs                 []*ImMsg `protobuf:"bytes,1,rep,name=msgs,proto3" json:"msgs,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAIPartnerMsgListResp) Reset()         { *m = GetAIPartnerMsgListResp{} }
func (m *GetAIPartnerMsgListResp) String() string { return proto.CompactTextString(m) }
func (*GetAIPartnerMsgListResp) ProtoMessage()    {}
func (*GetAIPartnerMsgListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{36}
}
func (m *GetAIPartnerMsgListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIPartnerMsgListResp.Unmarshal(m, b)
}
func (m *GetAIPartnerMsgListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIPartnerMsgListResp.Marshal(b, m, deterministic)
}
func (dst *GetAIPartnerMsgListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIPartnerMsgListResp.Merge(dst, src)
}
func (m *GetAIPartnerMsgListResp) XXX_Size() int {
	return xxx_messageInfo_GetAIPartnerMsgListResp.Size(m)
}
func (m *GetAIPartnerMsgListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIPartnerMsgListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIPartnerMsgListResp proto.InternalMessageInfo

func (m *GetAIPartnerMsgListResp) GetMsgs() []*ImMsg {
	if m != nil {
		return m.Msgs
	}
	return nil
}

type ReadAIPartnerMsgReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReadAIPartnerMsgReq) Reset()         { *m = ReadAIPartnerMsgReq{} }
func (m *ReadAIPartnerMsgReq) String() string { return proto.CompactTextString(m) }
func (*ReadAIPartnerMsgReq) ProtoMessage()    {}
func (*ReadAIPartnerMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{37}
}
func (m *ReadAIPartnerMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadAIPartnerMsgReq.Unmarshal(m, b)
}
func (m *ReadAIPartnerMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadAIPartnerMsgReq.Marshal(b, m, deterministic)
}
func (dst *ReadAIPartnerMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadAIPartnerMsgReq.Merge(dst, src)
}
func (m *ReadAIPartnerMsgReq) XXX_Size() int {
	return xxx_messageInfo_ReadAIPartnerMsgReq.Size(m)
}
func (m *ReadAIPartnerMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadAIPartnerMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReadAIPartnerMsgReq proto.InternalMessageInfo

func (m *ReadAIPartnerMsgReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReadAIPartnerMsgReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

type ReadAIPartnerMsgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReadAIPartnerMsgResp) Reset()         { *m = ReadAIPartnerMsgResp{} }
func (m *ReadAIPartnerMsgResp) String() string { return proto.CompactTextString(m) }
func (*ReadAIPartnerMsgResp) ProtoMessage()    {}
func (*ReadAIPartnerMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{38}
}
func (m *ReadAIPartnerMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadAIPartnerMsgResp.Unmarshal(m, b)
}
func (m *ReadAIPartnerMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadAIPartnerMsgResp.Marshal(b, m, deterministic)
}
func (dst *ReadAIPartnerMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadAIPartnerMsgResp.Merge(dst, src)
}
func (m *ReadAIPartnerMsgResp) XXX_Size() int {
	return xxx_messageInfo_ReadAIPartnerMsgResp.Size(m)
}
func (m *ReadAIPartnerMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadAIPartnerMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReadAIPartnerMsgResp proto.InternalMessageInfo

type SendAIPartnerPushReq struct {
	Msg *AIPartnerPushMsg `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	// 接收推送的用户ID
	Uid uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// AI伴侣ID
	PartnerId            uint32   `protobuf:"varint,3,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendAIPartnerPushReq) Reset()         { *m = SendAIPartnerPushReq{} }
func (m *SendAIPartnerPushReq) String() string { return proto.CompactTextString(m) }
func (*SendAIPartnerPushReq) ProtoMessage()    {}
func (*SendAIPartnerPushReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{39}
}
func (m *SendAIPartnerPushReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendAIPartnerPushReq.Unmarshal(m, b)
}
func (m *SendAIPartnerPushReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendAIPartnerPushReq.Marshal(b, m, deterministic)
}
func (dst *SendAIPartnerPushReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendAIPartnerPushReq.Merge(dst, src)
}
func (m *SendAIPartnerPushReq) XXX_Size() int {
	return xxx_messageInfo_SendAIPartnerPushReq.Size(m)
}
func (m *SendAIPartnerPushReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendAIPartnerPushReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendAIPartnerPushReq proto.InternalMessageInfo

func (m *SendAIPartnerPushReq) GetMsg() *AIPartnerPushMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *SendAIPartnerPushReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendAIPartnerPushReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

type SendAIPartnerPushResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendAIPartnerPushResp) Reset()         { *m = SendAIPartnerPushResp{} }
func (m *SendAIPartnerPushResp) String() string { return proto.CompactTextString(m) }
func (*SendAIPartnerPushResp) ProtoMessage()    {}
func (*SendAIPartnerPushResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{40}
}
func (m *SendAIPartnerPushResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendAIPartnerPushResp.Unmarshal(m, b)
}
func (m *SendAIPartnerPushResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendAIPartnerPushResp.Marshal(b, m, deterministic)
}
func (dst *SendAIPartnerPushResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendAIPartnerPushResp.Merge(dst, src)
}
func (m *SendAIPartnerPushResp) XXX_Size() int {
	return xxx_messageInfo_SendAIPartnerPushResp.Size(m)
}
func (m *SendAIPartnerPushResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendAIPartnerPushResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendAIPartnerPushResp proto.InternalMessageInfo

type CreateGamePartnerReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGamePartnerReq) Reset()         { *m = CreateGamePartnerReq{} }
func (m *CreateGamePartnerReq) String() string { return proto.CompactTextString(m) }
func (*CreateGamePartnerReq) ProtoMessage()    {}
func (*CreateGamePartnerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{41}
}
func (m *CreateGamePartnerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGamePartnerReq.Unmarshal(m, b)
}
func (m *CreateGamePartnerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGamePartnerReq.Marshal(b, m, deterministic)
}
func (dst *CreateGamePartnerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGamePartnerReq.Merge(dst, src)
}
func (m *CreateGamePartnerReq) XXX_Size() int {
	return xxx_messageInfo_CreateGamePartnerReq.Size(m)
}
func (m *CreateGamePartnerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGamePartnerReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGamePartnerReq proto.InternalMessageInfo

func (m *CreateGamePartnerReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CreateGamePartnerReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type CreateGamePartnerResp struct {
	Partner              *AIPartner `protobuf:"bytes,1,opt,name=partner,proto3" json:"partner,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CreateGamePartnerResp) Reset()         { *m = CreateGamePartnerResp{} }
func (m *CreateGamePartnerResp) String() string { return proto.CompactTextString(m) }
func (*CreateGamePartnerResp) ProtoMessage()    {}
func (*CreateGamePartnerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_6676a9c84e7ccb77, []int{42}
}
func (m *CreateGamePartnerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGamePartnerResp.Unmarshal(m, b)
}
func (m *CreateGamePartnerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGamePartnerResp.Marshal(b, m, deterministic)
}
func (dst *CreateGamePartnerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGamePartnerResp.Merge(dst, src)
}
func (m *CreateGamePartnerResp) XXX_Size() int {
	return xxx_messageInfo_CreateGamePartnerResp.Size(m)
}
func (m *CreateGamePartnerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGamePartnerResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGamePartnerResp proto.InternalMessageInfo

func (m *CreateGamePartnerResp) GetPartner() *AIPartner {
	if m != nil {
		return m.Partner
	}
	return nil
}

func init() {
	proto.RegisterType((*ImMsgExtSilence)(nil), "chat_bot.ImMsgExtSilence")
	proto.RegisterType((*ImMsgExtAirTicket)(nil), "chat_bot.ImMsgExtAirTicket")
	proto.RegisterType((*ImMsg)(nil), "chat_bot.ImMsg")
	proto.RegisterType((*AIRole)(nil), "chat_bot.AIRole")
	proto.RegisterType((*AIPhotograph)(nil), "chat_bot.AIPhotograph")
	proto.RegisterType((*AIRelationship)(nil), "chat_bot.AIRelationship")
	proto.RegisterType((*AIPartner)(nil), "chat_bot.AIPartner")
	proto.RegisterType((*AIPartnerPushMsg)(nil), "chat_bot.AIPartnerPushMsg")
	proto.RegisterType((*SendOption)(nil), "chat_bot.SendOption")
	proto.RegisterType((*SendImMsgToUserReq)(nil), "chat_bot.SendImMsgToUserReq")
	proto.RegisterType((*SendImMsgToUserResp)(nil), "chat_bot.SendImMsgToUserResp")
	proto.RegisterType((*Bot)(nil), "chat_bot.Bot")
	proto.RegisterType((*CreateChatBotReq)(nil), "chat_bot.CreateChatBotReq")
	proto.RegisterType((*CreateChatBotResp)(nil), "chat_bot.CreateChatBotResp")
	proto.RegisterType((*MarkImMsgReadReq)(nil), "chat_bot.MarkImMsgReadReq")
	proto.RegisterType((*MarkImMsgReadResp)(nil), "chat_bot.MarkImMsgReadResp")
	proto.RegisterType((*GetAIRoleListReq)(nil), "chat_bot.GetAIRoleListReq")
	proto.RegisterType((*GetAIRoleListResp)(nil), "chat_bot.GetAIRoleListResp")
	proto.RegisterType((*UpsertAIPartnerReq)(nil), "chat_bot.UpsertAIPartnerReq")
	proto.RegisterType((*UpsertAIPartnerResp)(nil), "chat_bot.UpsertAIPartnerResp")
	proto.RegisterType((*GetOption)(nil), "chat_bot.GetOption")
	proto.RegisterType((*GetUserAIPartnerReq)(nil), "chat_bot.GetUserAIPartnerReq")
	proto.RegisterType((*GetUserAIPartnerResp)(nil), "chat_bot.GetUserAIPartnerResp")
	proto.RegisterType((*UpdateAIPartnerChatStateReq)(nil), "chat_bot.UpdateAIPartnerChatStateReq")
	proto.RegisterType((*UpdateAIPartnerChatStateResp)(nil), "chat_bot.UpdateAIPartnerChatStateResp")
	proto.RegisterType((*ChangeUserAIPartnerRoleReq)(nil), "chat_bot.ChangeUserAIPartnerRoleReq")
	proto.RegisterType((*ChangeUserAIPartnerRoleResp)(nil), "chat_bot.ChangeUserAIPartnerRoleResp")
	proto.RegisterType((*GetAIPartnerReq)(nil), "chat_bot.GetAIPartnerReq")
	proto.RegisterType((*GetAIPartnerResp)(nil), "chat_bot.GetAIPartnerResp")
	proto.RegisterType((*SendImMsgUserToAIReq)(nil), "chat_bot.SendImMsgUserToAIReq")
	proto.RegisterType((*SendImMsgUserToAIResp)(nil), "chat_bot.SendImMsgUserToAIResp")
	proto.RegisterType((*SendImMsgAIToUserReq)(nil), "chat_bot.SendImMsgAIToUserReq")
	proto.RegisterType((*SendImMsgAIToUserResp)(nil), "chat_bot.SendImMsgAIToUserResp")
	proto.RegisterType((*ReportUserEnterAIPartnerChatReq)(nil), "chat_bot.ReportUserEnterAIPartnerChatReq")
	proto.RegisterType((*ReportUserEnterAIPartnerChatResp)(nil), "chat_bot.ReportUserEnterAIPartnerChatResp")
	proto.RegisterType((*GetAIPartnerMsgListReq)(nil), "chat_bot.GetAIPartnerMsgListReq")
	proto.RegisterType((*GetAIPartnerMsgListResp)(nil), "chat_bot.GetAIPartnerMsgListResp")
	proto.RegisterType((*ReadAIPartnerMsgReq)(nil), "chat_bot.ReadAIPartnerMsgReq")
	proto.RegisterType((*ReadAIPartnerMsgResp)(nil), "chat_bot.ReadAIPartnerMsgResp")
	proto.RegisterType((*SendAIPartnerPushReq)(nil), "chat_bot.SendAIPartnerPushReq")
	proto.RegisterType((*SendAIPartnerPushResp)(nil), "chat_bot.SendAIPartnerPushResp")
	proto.RegisterType((*CreateGamePartnerReq)(nil), "chat_bot.CreateGamePartnerReq")
	proto.RegisterType((*CreateGamePartnerResp)(nil), "chat_bot.CreateGamePartnerResp")
	proto.RegisterEnum("chat_bot.ImMsgType", ImMsgType_name, ImMsgType_value)
	proto.RegisterEnum("chat_bot.AuditResult", AuditResult_name, AuditResult_value)
	proto.RegisterEnum("chat_bot.AIRoleType", AIRoleType_name, AIRoleType_value)
	proto.RegisterEnum("chat_bot.AIPartner_Relationship", AIPartner_Relationship_name, AIPartner_Relationship_value)
	proto.RegisterEnum("chat_bot.AIPartner_Source", AIPartner_Source_name, AIPartner_Source_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChatBotClient is the client API for ChatBot service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChatBotClient interface {
	// 发送 IM 消息给指定用户
	SendImMsgToUser(ctx context.Context, in *SendImMsgToUserReq, opts ...grpc.CallOption) (*SendImMsgToUserResp, error)
	// 创建聊天机器人
	CreateChatBot(ctx context.Context, in *CreateChatBotReq, opts ...grpc.CallOption) (*CreateChatBotResp, error)
	// 标记 IM 消息已读
	MarkImMsgRead(ctx context.Context, in *MarkImMsgReadReq, opts ...grpc.CallOption) (*MarkImMsgReadResp, error)
	// 获取可选的AI角色卡
	GetAIRoleList(ctx context.Context, in *GetAIRoleListReq, opts ...grpc.CallOption) (*GetAIRoleListResp, error)
	// 创建/修改AI伴侣信息
	UpsertAIPartner(ctx context.Context, in *UpsertAIPartnerReq, opts ...grpc.CallOption) (*UpsertAIPartnerResp, error)
	// 获取用户的AI伴侣
	GetUserAIPartner(ctx context.Context, in *GetUserAIPartnerReq, opts ...grpc.CallOption) (*GetUserAIPartnerResp, error)
	// 更新AI伴侣[不再接收ta的消息]开关
	UpdateAIPartnerChatState(ctx context.Context, in *UpdateAIPartnerChatStateReq, opts ...grpc.CallOption) (*UpdateAIPartnerChatStateResp, error)
	// 修改用户AI伴侣形象
	ChangeUserAIPartnerRole(ctx context.Context, in *ChangeUserAIPartnerRoleReq, opts ...grpc.CallOption) (*ChangeUserAIPartnerRoleResp, error)
	// 获取AI伴侣信息
	GetAIPartner(ctx context.Context, in *GetAIPartnerReq, opts ...grpc.CallOption) (*GetAIPartnerResp, error)
	// 用户发消息给AI伴侣
	SendImMsgUserToAI(ctx context.Context, in *SendImMsgUserToAIReq, opts ...grpc.CallOption) (*SendImMsgUserToAIResp, error)
	// AI伴侣发消息给用户
	SendImMsgAIToUser(ctx context.Context, in *SendImMsgAIToUserReq, opts ...grpc.CallOption) (*SendImMsgAIToUserResp, error)
	// 拉取AI伴侣消息列表
	GetAIPartnerMsgList(ctx context.Context, in *GetAIPartnerMsgListReq, opts ...grpc.CallOption) (*GetAIPartnerMsgListResp, error)
	// 标记AI伴侣消息已读
	ReadAIPartnerMsg(ctx context.Context, in *ReadAIPartnerMsgReq, opts ...grpc.CallOption) (*ReadAIPartnerMsgResp, error)
	// 上报用户进入AI伴侣聊天页面
	ReportUserEnterAIPartnerChat(ctx context.Context, in *ReportUserEnterAIPartnerChatReq, opts ...grpc.CallOption) (*ReportUserEnterAIPartnerChatResp, error)
	// AI伴侣统一推送(中台->WEB)
	SendAIPartnerPush(ctx context.Context, in *SendAIPartnerPushReq, opts ...grpc.CallOption) (*SendAIPartnerPushResp, error)
	// 创建游戏伴侣
	CreateGamePartner(ctx context.Context, in *CreateGamePartnerReq, opts ...grpc.CallOption) (*CreateGamePartnerResp, error)
}

type chatBotClient struct {
	cc *grpc.ClientConn
}

func NewChatBotClient(cc *grpc.ClientConn) ChatBotClient {
	return &chatBotClient{cc}
}

func (c *chatBotClient) SendImMsgToUser(ctx context.Context, in *SendImMsgToUserReq, opts ...grpc.CallOption) (*SendImMsgToUserResp, error) {
	out := new(SendImMsgToUserResp)
	err := c.cc.Invoke(ctx, "/chat_bot.ChatBot/SendImMsgToUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotClient) CreateChatBot(ctx context.Context, in *CreateChatBotReq, opts ...grpc.CallOption) (*CreateChatBotResp, error) {
	out := new(CreateChatBotResp)
	err := c.cc.Invoke(ctx, "/chat_bot.ChatBot/CreateChatBot", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotClient) MarkImMsgRead(ctx context.Context, in *MarkImMsgReadReq, opts ...grpc.CallOption) (*MarkImMsgReadResp, error) {
	out := new(MarkImMsgReadResp)
	err := c.cc.Invoke(ctx, "/chat_bot.ChatBot/MarkImMsgRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotClient) GetAIRoleList(ctx context.Context, in *GetAIRoleListReq, opts ...grpc.CallOption) (*GetAIRoleListResp, error) {
	out := new(GetAIRoleListResp)
	err := c.cc.Invoke(ctx, "/chat_bot.ChatBot/GetAIRoleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotClient) UpsertAIPartner(ctx context.Context, in *UpsertAIPartnerReq, opts ...grpc.CallOption) (*UpsertAIPartnerResp, error) {
	out := new(UpsertAIPartnerResp)
	err := c.cc.Invoke(ctx, "/chat_bot.ChatBot/UpsertAIPartner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotClient) GetUserAIPartner(ctx context.Context, in *GetUserAIPartnerReq, opts ...grpc.CallOption) (*GetUserAIPartnerResp, error) {
	out := new(GetUserAIPartnerResp)
	err := c.cc.Invoke(ctx, "/chat_bot.ChatBot/GetUserAIPartner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotClient) UpdateAIPartnerChatState(ctx context.Context, in *UpdateAIPartnerChatStateReq, opts ...grpc.CallOption) (*UpdateAIPartnerChatStateResp, error) {
	out := new(UpdateAIPartnerChatStateResp)
	err := c.cc.Invoke(ctx, "/chat_bot.ChatBot/UpdateAIPartnerChatState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotClient) ChangeUserAIPartnerRole(ctx context.Context, in *ChangeUserAIPartnerRoleReq, opts ...grpc.CallOption) (*ChangeUserAIPartnerRoleResp, error) {
	out := new(ChangeUserAIPartnerRoleResp)
	err := c.cc.Invoke(ctx, "/chat_bot.ChatBot/ChangeUserAIPartnerRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotClient) GetAIPartner(ctx context.Context, in *GetAIPartnerReq, opts ...grpc.CallOption) (*GetAIPartnerResp, error) {
	out := new(GetAIPartnerResp)
	err := c.cc.Invoke(ctx, "/chat_bot.ChatBot/GetAIPartner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotClient) SendImMsgUserToAI(ctx context.Context, in *SendImMsgUserToAIReq, opts ...grpc.CallOption) (*SendImMsgUserToAIResp, error) {
	out := new(SendImMsgUserToAIResp)
	err := c.cc.Invoke(ctx, "/chat_bot.ChatBot/SendImMsgUserToAI", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotClient) SendImMsgAIToUser(ctx context.Context, in *SendImMsgAIToUserReq, opts ...grpc.CallOption) (*SendImMsgAIToUserResp, error) {
	out := new(SendImMsgAIToUserResp)
	err := c.cc.Invoke(ctx, "/chat_bot.ChatBot/SendImMsgAIToUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotClient) GetAIPartnerMsgList(ctx context.Context, in *GetAIPartnerMsgListReq, opts ...grpc.CallOption) (*GetAIPartnerMsgListResp, error) {
	out := new(GetAIPartnerMsgListResp)
	err := c.cc.Invoke(ctx, "/chat_bot.ChatBot/GetAIPartnerMsgList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotClient) ReadAIPartnerMsg(ctx context.Context, in *ReadAIPartnerMsgReq, opts ...grpc.CallOption) (*ReadAIPartnerMsgResp, error) {
	out := new(ReadAIPartnerMsgResp)
	err := c.cc.Invoke(ctx, "/chat_bot.ChatBot/ReadAIPartnerMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotClient) ReportUserEnterAIPartnerChat(ctx context.Context, in *ReportUserEnterAIPartnerChatReq, opts ...grpc.CallOption) (*ReportUserEnterAIPartnerChatResp, error) {
	out := new(ReportUserEnterAIPartnerChatResp)
	err := c.cc.Invoke(ctx, "/chat_bot.ChatBot/ReportUserEnterAIPartnerChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotClient) SendAIPartnerPush(ctx context.Context, in *SendAIPartnerPushReq, opts ...grpc.CallOption) (*SendAIPartnerPushResp, error) {
	out := new(SendAIPartnerPushResp)
	err := c.cc.Invoke(ctx, "/chat_bot.ChatBot/SendAIPartnerPush", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotClient) CreateGamePartner(ctx context.Context, in *CreateGamePartnerReq, opts ...grpc.CallOption) (*CreateGamePartnerResp, error) {
	out := new(CreateGamePartnerResp)
	err := c.cc.Invoke(ctx, "/chat_bot.ChatBot/CreateGamePartner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChatBotServer is the server API for ChatBot service.
type ChatBotServer interface {
	// 发送 IM 消息给指定用户
	SendImMsgToUser(context.Context, *SendImMsgToUserReq) (*SendImMsgToUserResp, error)
	// 创建聊天机器人
	CreateChatBot(context.Context, *CreateChatBotReq) (*CreateChatBotResp, error)
	// 标记 IM 消息已读
	MarkImMsgRead(context.Context, *MarkImMsgReadReq) (*MarkImMsgReadResp, error)
	// 获取可选的AI角色卡
	GetAIRoleList(context.Context, *GetAIRoleListReq) (*GetAIRoleListResp, error)
	// 创建/修改AI伴侣信息
	UpsertAIPartner(context.Context, *UpsertAIPartnerReq) (*UpsertAIPartnerResp, error)
	// 获取用户的AI伴侣
	GetUserAIPartner(context.Context, *GetUserAIPartnerReq) (*GetUserAIPartnerResp, error)
	// 更新AI伴侣[不再接收ta的消息]开关
	UpdateAIPartnerChatState(context.Context, *UpdateAIPartnerChatStateReq) (*UpdateAIPartnerChatStateResp, error)
	// 修改用户AI伴侣形象
	ChangeUserAIPartnerRole(context.Context, *ChangeUserAIPartnerRoleReq) (*ChangeUserAIPartnerRoleResp, error)
	// 获取AI伴侣信息
	GetAIPartner(context.Context, *GetAIPartnerReq) (*GetAIPartnerResp, error)
	// 用户发消息给AI伴侣
	SendImMsgUserToAI(context.Context, *SendImMsgUserToAIReq) (*SendImMsgUserToAIResp, error)
	// AI伴侣发消息给用户
	SendImMsgAIToUser(context.Context, *SendImMsgAIToUserReq) (*SendImMsgAIToUserResp, error)
	// 拉取AI伴侣消息列表
	GetAIPartnerMsgList(context.Context, *GetAIPartnerMsgListReq) (*GetAIPartnerMsgListResp, error)
	// 标记AI伴侣消息已读
	ReadAIPartnerMsg(context.Context, *ReadAIPartnerMsgReq) (*ReadAIPartnerMsgResp, error)
	// 上报用户进入AI伴侣聊天页面
	ReportUserEnterAIPartnerChat(context.Context, *ReportUserEnterAIPartnerChatReq) (*ReportUserEnterAIPartnerChatResp, error)
	// AI伴侣统一推送(中台->WEB)
	SendAIPartnerPush(context.Context, *SendAIPartnerPushReq) (*SendAIPartnerPushResp, error)
	// 创建游戏伴侣
	CreateGamePartner(context.Context, *CreateGamePartnerReq) (*CreateGamePartnerResp, error)
}

func RegisterChatBotServer(s *grpc.Server, srv ChatBotServer) {
	s.RegisterService(&_ChatBot_serviceDesc, srv)
}

func _ChatBot_SendImMsgToUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendImMsgToUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotServer).SendImMsgToUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chat_bot.ChatBot/SendImMsgToUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotServer).SendImMsgToUser(ctx, req.(*SendImMsgToUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBot_CreateChatBot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateChatBotReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotServer).CreateChatBot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chat_bot.ChatBot/CreateChatBot",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotServer).CreateChatBot(ctx, req.(*CreateChatBotReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBot_MarkImMsgRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkImMsgReadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotServer).MarkImMsgRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chat_bot.ChatBot/MarkImMsgRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotServer).MarkImMsgRead(ctx, req.(*MarkImMsgReadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBot_GetAIRoleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAIRoleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotServer).GetAIRoleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chat_bot.ChatBot/GetAIRoleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotServer).GetAIRoleList(ctx, req.(*GetAIRoleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBot_UpsertAIPartner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertAIPartnerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotServer).UpsertAIPartner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chat_bot.ChatBot/UpsertAIPartner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotServer).UpsertAIPartner(ctx, req.(*UpsertAIPartnerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBot_GetUserAIPartner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAIPartnerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotServer).GetUserAIPartner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chat_bot.ChatBot/GetUserAIPartner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotServer).GetUserAIPartner(ctx, req.(*GetUserAIPartnerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBot_UpdateAIPartnerChatState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAIPartnerChatStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotServer).UpdateAIPartnerChatState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chat_bot.ChatBot/UpdateAIPartnerChatState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotServer).UpdateAIPartnerChatState(ctx, req.(*UpdateAIPartnerChatStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBot_ChangeUserAIPartnerRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeUserAIPartnerRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotServer).ChangeUserAIPartnerRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chat_bot.ChatBot/ChangeUserAIPartnerRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotServer).ChangeUserAIPartnerRole(ctx, req.(*ChangeUserAIPartnerRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBot_GetAIPartner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAIPartnerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotServer).GetAIPartner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chat_bot.ChatBot/GetAIPartner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotServer).GetAIPartner(ctx, req.(*GetAIPartnerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBot_SendImMsgUserToAI_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendImMsgUserToAIReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotServer).SendImMsgUserToAI(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chat_bot.ChatBot/SendImMsgUserToAI",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotServer).SendImMsgUserToAI(ctx, req.(*SendImMsgUserToAIReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBot_SendImMsgAIToUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendImMsgAIToUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotServer).SendImMsgAIToUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chat_bot.ChatBot/SendImMsgAIToUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotServer).SendImMsgAIToUser(ctx, req.(*SendImMsgAIToUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBot_GetAIPartnerMsgList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAIPartnerMsgListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotServer).GetAIPartnerMsgList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chat_bot.ChatBot/GetAIPartnerMsgList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotServer).GetAIPartnerMsgList(ctx, req.(*GetAIPartnerMsgListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBot_ReadAIPartnerMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadAIPartnerMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotServer).ReadAIPartnerMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chat_bot.ChatBot/ReadAIPartnerMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotServer).ReadAIPartnerMsg(ctx, req.(*ReadAIPartnerMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBot_ReportUserEnterAIPartnerChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportUserEnterAIPartnerChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotServer).ReportUserEnterAIPartnerChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chat_bot.ChatBot/ReportUserEnterAIPartnerChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotServer).ReportUserEnterAIPartnerChat(ctx, req.(*ReportUserEnterAIPartnerChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBot_SendAIPartnerPush_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendAIPartnerPushReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotServer).SendAIPartnerPush(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chat_bot.ChatBot/SendAIPartnerPush",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotServer).SendAIPartnerPush(ctx, req.(*SendAIPartnerPushReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBot_CreateGamePartner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGamePartnerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotServer).CreateGamePartner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chat_bot.ChatBot/CreateGamePartner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotServer).CreateGamePartner(ctx, req.(*CreateGamePartnerReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChatBot_serviceDesc = grpc.ServiceDesc{
	ServiceName: "chat_bot.ChatBot",
	HandlerType: (*ChatBotServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendImMsgToUser",
			Handler:    _ChatBot_SendImMsgToUser_Handler,
		},
		{
			MethodName: "CreateChatBot",
			Handler:    _ChatBot_CreateChatBot_Handler,
		},
		{
			MethodName: "MarkImMsgRead",
			Handler:    _ChatBot_MarkImMsgRead_Handler,
		},
		{
			MethodName: "GetAIRoleList",
			Handler:    _ChatBot_GetAIRoleList_Handler,
		},
		{
			MethodName: "UpsertAIPartner",
			Handler:    _ChatBot_UpsertAIPartner_Handler,
		},
		{
			MethodName: "GetUserAIPartner",
			Handler:    _ChatBot_GetUserAIPartner_Handler,
		},
		{
			MethodName: "UpdateAIPartnerChatState",
			Handler:    _ChatBot_UpdateAIPartnerChatState_Handler,
		},
		{
			MethodName: "ChangeUserAIPartnerRole",
			Handler:    _ChatBot_ChangeUserAIPartnerRole_Handler,
		},
		{
			MethodName: "GetAIPartner",
			Handler:    _ChatBot_GetAIPartner_Handler,
		},
		{
			MethodName: "SendImMsgUserToAI",
			Handler:    _ChatBot_SendImMsgUserToAI_Handler,
		},
		{
			MethodName: "SendImMsgAIToUser",
			Handler:    _ChatBot_SendImMsgAIToUser_Handler,
		},
		{
			MethodName: "GetAIPartnerMsgList",
			Handler:    _ChatBot_GetAIPartnerMsgList_Handler,
		},
		{
			MethodName: "ReadAIPartnerMsg",
			Handler:    _ChatBot_ReadAIPartnerMsg_Handler,
		},
		{
			MethodName: "ReportUserEnterAIPartnerChat",
			Handler:    _ChatBot_ReportUserEnterAIPartnerChat_Handler,
		},
		{
			MethodName: "SendAIPartnerPush",
			Handler:    _ChatBot_SendAIPartnerPush_Handler,
		},
		{
			MethodName: "CreateGamePartner",
			Handler:    _ChatBot_CreateGamePartner_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "chat-bot/chat-bot.proto",
}

func init() { proto.RegisterFile("chat-bot/chat-bot.proto", fileDescriptor_chat_bot_6676a9c84e7ccb77) }

var fileDescriptor_chat_bot_6676a9c84e7ccb77 = []byte{
	// 2083 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x59, 0xdd, 0x6e, 0xdb, 0xc8,
	0xf5, 0xd7, 0x97, 0x65, 0xe9, 0x48, 0xb2, 0xe9, 0xf1, 0x97, 0xa2, 0x7c, 0x39, 0xf3, 0x4f, 0xb2,
	0xfb, 0x37, 0x76, 0x9d, 0x56, 0x9b, 0xa6, 0x45, 0x5b, 0xb4, 0x50, 0x9c, 0xc4, 0x50, 0x11, 0xef,
	0x1a, 0xb4, 0x9d, 0x05, 0x8a, 0x2e, 0x04, 0x9a, 0x1c, 0x4b, 0x8c, 0x25, 0x0e, 0xcb, 0x19, 0xd9,
	0xce, 0x4d, 0x1f, 0x62, 0x2f, 0xdb, 0x8b, 0xbe, 0x44, 0x6f, 0x0b, 0xf4, 0x15, 0xfa, 0x00, 0x7d,
	0x97, 0x62, 0x3e, 0x48, 0x0d, 0x29, 0xca, 0x76, 0xb6, 0x77, 0x9a, 0x73, 0xce, 0x9c, 0x39, 0xdf,
	0xbf, 0x43, 0x1b, 0xb6, 0xdd, 0x91, 0xc3, 0xbf, 0x3e, 0xa3, 0xfc, 0x45, 0xfc, 0x63, 0x2f, 0x8c,
	0x28, 0xa7, 0xa8, 0x26, 0xce, 0x83, 0x33, 0xca, 0xf1, 0x31, 0xac, 0xf6, 0x27, 0x87, 0x6c, 0xf8,
	0xf6, 0x9a, 0x1f, 0xfb, 0x63, 0x12, 0xb8, 0x04, 0x3d, 0x86, 0x86, 0xe3, 0x72, 0xff, 0x92, 0x0c,
	0x38, 0xb9, 0xe6, 0xed, 0xe2, 0x4e, 0xf1, 0xcb, 0xba, 0x0d, 0x8a, 0x74, 0x42, 0xae, 0xb9, 0x10,
	0x60, 0x42, 0x96, 0x2b, 0x81, 0x92, 0x12, 0x50, 0x24, 0x21, 0x80, 0xff, 0x53, 0x82, 0xb5, 0x58,
	0x6b, 0xcf, 0x8f, 0x4e, 0x7c, 0xf7, 0x82, 0x70, 0xf4, 0x10, 0xc0, 0x1d, 0x39, 0x41, 0x40, 0xc6,
	0x03, 0xdf, 0x93, 0x6a, 0x5b, 0x76, 0x5d, 0x53, 0xfa, 0x1e, 0x7a, 0x02, 0xcd, 0x98, 0x3d, 0xa1,
	0x1e, 0x91, 0x6a, 0x5b, 0x76, 0x43, 0xd3, 0x0e, 0xa9, 0x47, 0x4c, 0x11, 0xfe, 0x29, 0x24, 0xed,
	0x72, 0x4a, 0xe4, 0xe4, 0x53, 0x48, 0xd0, 0x73, 0x58, 0x8d, 0x45, 0x2e, 0x7d, 0x72, 0x25, 0x5e,
	0xaa, 0x48, 0xfb, 0x5a, 0x9a, 0xfc, 0xc1, 0x27, 0x57, 0x7d, 0x0f, 0xbd, 0x92, 0xc1, 0x91, 0x72,
	0x6e, 0x44, 0x1c, 0x4e, 0xa3, 0x81, 0xe3, 0xba, 0x74, 0x1a, 0xf0, 0xf6, 0x92, 0x94, 0xdf, 0xd4,
	0xec, 0x7d, 0xc5, 0xed, 0x29, 0xa6, 0x69, 0x42, 0xe0, 0x4c, 0x48, 0xbb, 0x2a, 0x85, 0x63, 0x13,
	0xbe, 0x75, 0x26, 0x04, 0x6d, 0x42, 0x95, 0x3b, 0x67, 0xe2, 0xe5, 0x65, 0x69, 0xdf, 0x12, 0x77,
	0xce, 0xfa, 0x1e, 0xba, 0x07, 0x35, 0x41, 0x96, 0xb7, 0x6a, 0xf2, 0xd6, 0x32, 0x77, 0xce, 0xe4,
	0x8d, 0x0d, 0x58, 0xe2, 0x3e, 0x1f, 0x93, 0x76, 0x5d, 0xd2, 0xd5, 0x01, 0xb5, 0x61, 0xd9, 0xa5,
	0x01, 0x27, 0x01, 0x6f, 0x83, 0x92, 0xd7, 0x47, 0xfc, 0x8f, 0x22, 0x2c, 0xc9, 0xf8, 0xa2, 0x2f,
	0xa0, 0x22, 0x23, 0x21, 0xa2, 0xb9, 0xd2, 0x5d, 0xdf, 0x8b, 0xf3, 0xba, 0x27, 0xd9, 0x22, 0x22,
	0xb6, 0x14, 0x30, 0x95, 0x95, 0x52, 0xca, 0x90, 0x05, 0x65, 0x91, 0x45, 0x11, 0xcb, 0xa6, 0x2d,
	0x7e, 0xa2, 0x6d, 0x58, 0x66, 0x22, 0xbb, 0x0e, 0x97, 0xb1, 0x2b, 0xdb, 0x55, 0x71, 0xec, 0x71,
	0xe1, 0xd9, 0x84, 0x0d, 0x85, 0x67, 0x2a, 0x46, 0x4b, 0x13, 0x36, 0xec, 0x7b, 0x22, 0xe6, 0x82,
	0xcc, 0xe8, 0x34, 0x72, 0x89, 0xca, 0x4c, 0x55, 0x7a, 0xde, 0x9a, 0xb0, 0xe1, 0xb1, 0xa4, 0x0a,
	0x4b, 0xf0, 0xbf, 0x8b, 0x50, 0xed, 0xf5, 0x6d, 0x3a, 0x26, 0x68, 0x05, 0x4a, 0x49, 0x0d, 0x94,
	0x7c, 0x0f, 0x6d, 0x41, 0xd5, 0xb9, 0x74, 0xb8, 0x13, 0x69, 0xeb, 0xf4, 0x49, 0x44, 0x86, 0xf1,
	0x4f, 0x63, 0x95, 0xea, 0xba, 0xad, 0x0e, 0xc2, 0x64, 0x46, 0xae, 0xa5, 0x71, 0x4b, 0xb6, 0xf8,
	0x29, 0xe4, 0xfc, 0x89, 0x33, 0x24, 0xb1, 0x61, 0xf2, 0x20, 0xa9, 0x01, 0x8f, 0xa8, 0xce, 0x92,
	0x3a, 0xa0, 0x2f, 0x75, 0xcc, 0x96, 0x65, 0xcc, 0x36, 0x66, 0x31, 0x53, 0xb6, 0x19, 0x41, 0x7b,
	0x02, 0x4d, 0xcf, 0x77, 0xc6, 0x74, 0x38, 0x70, 0xe9, 0x98, 0x46, 0x3a, 0x6d, 0x0d, 0x45, 0xdb,
	0x17, 0x24, 0xfc, 0x1e, 0x9a, 0xbd, 0xfe, 0xd1, 0x88, 0x72, 0x3a, 0x8c, 0x9c, 0x70, 0xf4, 0x39,
	0x8e, 0x29, 0x83, 0xcb, 0x86, 0xc1, 0xf8, 0x25, 0xac, 0xf4, 0xfa, 0x36, 0x19, 0x3b, 0xdc, 0xa7,
	0x01, 0x1b, 0xf9, 0xe1, 0x9c, 0x3e, 0x04, 0x15, 0x59, 0x41, 0x4a, 0x9b, 0xfc, 0x8d, 0xff, 0x55,
	0x81, 0x7a, 0xaf, 0x7f, 0xe4, 0x44, 0x3c, 0x20, 0xd1, 0x5d, 0x6e, 0xa0, 0xfb, 0x50, 0x77, 0x9d,
	0xb1, 0x2e, 0x61, 0x65, 0x41, 0x4d, 0x10, 0x64, 0x35, 0x3e, 0x85, 0x4a, 0x44, 0xc7, 0x44, 0x86,
	0xb7, 0xd1, 0xb5, 0xb2, 0xf1, 0xb1, 0x25, 0x17, 0xbd, 0x81, 0x66, 0x64, 0x18, 0x2a, 0x03, 0xbf,
	0xd2, 0xdd, 0x31, 0xa5, 0xb5, 0x45, 0x7b, 0xa6, 0x43, 0x76, 0xea, 0x96, 0x08, 0x8f, 0x9a, 0x1b,
	0x32, 0x45, 0x35, 0x5b, 0x9f, 0x44, 0x86, 0xa7, 0x49, 0x03, 0x89, 0x9f, 0x71, 0x63, 0x0f, 0xc9,
	0x40, 0x3c, 0x3f, 0x70, 0x03, 0x2e, 0xd3, 0xd1, 0x52, 0x8d, 0x3d, 0x24, 0xc2, 0xb4, 0xfd, 0x80,
	0xa3, 0x57, 0x00, 0x61, 0x92, 0x0e, 0xd9, 0x50, 0x8d, 0xee, 0x56, 0xca, 0xaa, 0x84, 0x6b, 0x1b,
	0x92, 0xe8, 0x25, 0xd4, 0x62, 0xcb, 0x64, 0xbb, 0x35, 0xba, 0xed, 0x94, 0xe7, 0xa6, 0x0f, 0x89,
	0x24, 0xea, 0x42, 0x55, 0x95, 0x7d, 0xbb, 0x21, 0xfd, 0xef, 0xe4, 0xf9, 0xaf, 0x5a, 0xc0, 0xd6,
	0x92, 0xf8, 0x03, 0x34, 0x53, 0x29, 0xde, 0x86, 0x75, 0xf3, 0x7c, 0x1a, 0x5c, 0x04, 0xf4, 0x2a,
	0xb0, 0x0a, 0x68, 0x0b, 0x90, 0xc9, 0x78, 0x17, 0xf9, 0x24, 0xf0, 0xac, 0x22, 0xda, 0x84, 0x35,
	0x93, 0xfe, 0x9e, 0x5e, 0x92, 0xc8, 0x2a, 0xe1, 0x5f, 0x43, 0x55, 0xbd, 0x84, 0x56, 0x00, 0xd4,
	0xaf, 0x53, 0x46, 0x22, 0xab, 0x80, 0x2c, 0x68, 0xaa, 0xf3, 0x1b, 0x19, 0x26, 0xab, 0x38, 0x93,
	0x38, 0x70, 0x26, 0x44, 0xde, 0xb5, 0x12, 0x7b, 0x8f, 0xa6, 0x6c, 0x24, 0x66, 0x0b, 0x32, 0x66,
	0x4b, 0x4b, 0x77, 0x04, 0x82, 0x8a, 0xe7, 0x70, 0x47, 0x16, 0x53, 0xd3, 0x96, 0xbf, 0xf1, 0x47,
	0x80, 0x63, 0x12, 0x78, 0xdf, 0x85, 0x32, 0x22, 0xbb, 0xb0, 0x76, 0xe5, 0xf3, 0xd1, 0x80, 0x9e,
	0x9f, 0x8f, 0xfd, 0x80, 0x0c, 0xc2, 0x29, 0x1b, 0x49, 0x15, 0x35, 0x7b, 0x55, 0x30, 0xbe, 0x53,
	0x74, 0xf1, 0x08, 0xfa, 0x19, 0x6c, 0x98, 0x62, 0x83, 0xf4, 0x84, 0x42, 0x74, 0x26, 0xba, 0xaf,
	0x27, 0xdf, 0x27, 0x40, 0xe2, 0x2d, 0x35, 0xdd, 0xa8, 0x70, 0xcf, 0x26, 0x7f, 0x16, 0xc8, 0xc2,
	0x48, 0xe0, 0x91, 0x68, 0x30, 0x9d, 0x21, 0x8b, 0xa2, 0x9c, 0xfa, 0x12, 0x59, 0x22, 0xe2, 0x12,
	0xff, 0x52, 0x0b, 0x68, 0x64, 0x89, 0x69, 0x4a, 0xa4, 0x3c, 0x61, 0x43, 0xd9, 0x0a, 0x8d, 0xee,
	0x6a, 0x66, 0x8c, 0xda, 0x82, 0x87, 0xff, 0x56, 0x84, 0xf5, 0xb9, 0xb7, 0x59, 0x88, 0x7e, 0x05,
	0x4d, 0x67, 0xea, 0xf9, 0x7c, 0x10, 0x11, 0x36, 0x1d, 0x73, 0x3d, 0x8a, 0x37, 0x8d, 0x42, 0x10,
	0x5c, 0x5b, 0x32, 0xed, 0x86, 0x33, 0x3b, 0x88, 0x92, 0x4e, 0xec, 0xd2, 0x73, 0x55, 0x98, 0x56,
	0xb1, 0x5b, 0x31, 0xf9, 0x50, 0xce, 0x57, 0x0c, 0x2d, 0xed, 0x9e, 0x96, 0x2a, 0x4b, 0xa9, 0x86,
	0x22, 0x4a, 0x19, 0xfc, 0x17, 0x28, 0xbf, 0xa6, 0x49, 0xdf, 0x14, 0x67, 0x7d, 0xd3, 0x81, 0xda,
	0x94, 0x91, 0xc8, 0x18, 0x01, 0xc9, 0x59, 0x8e, 0x81, 0x31, 0x0d, 0x88, 0x8c, 0x8a, 0x02, 0xd3,
	0x9a, 0x24, 0x9c, 0xaa, 0x8b, 0x81, 0xef, 0x5e, 0xc8, 0x8b, 0x0a, 0x42, 0x93, 0x73, 0x3c, 0x80,
	0x97, 0x92, 0x01, 0x8c, 0xbf, 0x01, 0x4b, 0x22, 0x25, 0xd9, 0x1f, 0x39, 0xfc, 0x35, 0xe5, 0x22,
	0x2d, 0x8f, 0xa1, 0x7c, 0x46, 0x55, 0x40, 0x1a, 0xdd, 0xd6, 0x2c, 0x20, 0x82, 0x2d, 0x38, 0xf8,
	0x25, 0xac, 0x65, 0x2e, 0xb1, 0xf0, 0xf6, 0x5b, 0x97, 0x60, 0x1d, 0x3a, 0xd1, 0x85, 0x4a, 0x0d,
	0x71, 0x3c, 0xf1, 0xd4, 0xbc, 0xdf, 0x33, 0xac, 0x52, 0xe9, 0xd6, 0x58, 0x75, 0x0f, 0x6a, 0x21,
	0xd1, 0x75, 0xa0, 0x3c, 0x5e, 0x16, 0x67, 0xe1, 0xf0, 0x23, 0x68, 0x48, 0x96, 0xbe, 0x56, 0x51,
	0x65, 0x24, 0x48, 0x2a, 0xc4, 0xbf, 0x85, 0xb5, 0xcc, 0xbb, 0x2c, 0x44, 0x5f, 0x80, 0x25, 0x2f,
	0x45, 0xc4, 0xf1, 0xe2, 0x9b, 0xca, 0x8a, 0x96, 0xa0, 0x0b, 0x39, 0x75, 0xfb, 0x5b, 0xb0, 0x0e,
	0x08, 0x57, 0x23, 0xf4, 0xbd, 0xcf, 0x78, 0xbe, 0xd5, 0x31, 0x36, 0x95, 0x6e, 0xc3, 0x26, 0xfc,
	0x1b, 0x58, 0xcb, 0xe8, 0x63, 0x21, 0x7a, 0x0e, 0x4b, 0x62, 0x3a, 0xb2, 0x76, 0x71, 0xa7, 0x9c,
	0x3b, 0xbb, 0x15, 0x1b, 0xef, 0x03, 0x3a, 0x0d, 0x19, 0x89, 0x78, 0xd2, 0xf4, 0xc2, 0x9c, 0xaf,
	0x61, 0x39, 0x54, 0x27, 0xf9, 0x7e, 0xc3, 0xdc, 0x27, 0x66, 0x82, 0xb1, 0x0c, 0xfe, 0x67, 0x11,
	0xd6, 0xe7, 0xb4, 0xb0, 0xd0, 0x54, 0x53, 0xbc, 0x5d, 0x0d, 0x7a, 0x05, 0x0d, 0x51, 0x53, 0x71,
	0xfb, 0x94, 0x6e, 0x6a, 0x1f, 0x10, 0x92, 0xba, 0x7b, 0x7e, 0x0f, 0x56, 0x82, 0x61, 0xf1, 0xe5,
	0xf2, 0x4d, 0x97, 0x57, 0x62, 0x84, 0x53, 0x67, 0xfc, 0x03, 0xd4, 0x0f, 0x08, 0xd7, 0x63, 0xeb,
	0x29, 0xac, 0xc8, 0xb1, 0x15, 0xb9, 0x13, 0x6f, 0xe0, 0x07, 0xe7, 0x54, 0xcf, 0xac, 0xa6, 0xa0,
	0xda, 0xee, 0xc4, 0xeb, 0x07, 0xe7, 0x34, 0x19, 0x6e, 0x1e, 0x39, 0x77, 0xa6, 0x63, 0x3e, 0x48,
	0xba, 0x4a, 0x0f, 0xb7, 0x37, 0x8a, 0x2e, 0x1e, 0xc1, 0x7f, 0x2d, 0xc2, 0xfa, 0x01, 0xe1, 0x62,
	0x4e, 0xa4, 0xa2, 0x3c, 0x9f, 0xf4, 0x9f, 0x43, 0x5d, 0x62, 0xda, 0xad, 0x99, 0xaf, 0x45, 0xfa,
	0x97, 0x58, 0xd1, 0xe4, 0x95, 0xa4, 0x8a, 0xab, 0xe2, 0xd8, 0xf7, 0xd0, 0x33, 0x28, 0xd3, 0x90,
	0x6b, 0xec, 0x36, 0x02, 0x9f, 0x78, 0x6a, 0x0b, 0x3e, 0x7e, 0x0b, 0x1b, 0xf3, 0xb6, 0x7d, 0x76,
	0xee, 0xf0, 0xf7, 0x70, 0xff, 0x34, 0xf4, 0x1c, 0x4e, 0x12, 0x9e, 0xe8, 0xe4, 0x63, 0xee, 0x70,
	0x92, 0xef, 0xaa, 0x5a, 0x4e, 0x4a, 0xe6, 0x7a, 0xa4, 0xf1, 0xbf, 0x6c, 0xe2, 0x3f, 0x7e, 0x04,
	0x0f, 0x16, 0x2b, 0x66, 0x21, 0xfe, 0x1e, 0x3a, 0xfb, 0x12, 0xf6, 0xd3, 0x2e, 0x88, 0x12, 0xbf,
	0xd3, 0xbb, 0x8b, 0xe2, 0x87, 0xdf, 0xc3, 0xfd, 0x85, 0x8a, 0x3f, 0x3f, 0x3e, 0x4f, 0x60, 0x55,
	0x36, 0xa9, 0x91, 0xfe, 0xcc, 0x7a, 0x86, 0x7b, 0x7a, 0x2e, 0xfc, 0x0f, 0x59, 0xf8, 0x08, 0x1b,
	0x09, 0x30, 0x09, 0xb3, 0x4f, 0xa8, 0xd8, 0x57, 0xf2, 0xc2, 0xf0, 0x10, 0x40, 0x5f, 0x9a, 0x0d,
	0xc6, 0xba, 0xa6, 0xf4, 0xef, 0x84, 0x82, 0x07, 0xb0, 0x99, 0xf3, 0x16, 0x0b, 0xcd, 0x8f, 0x86,
	0xe2, 0x82, 0x8f, 0x86, 0x92, 0xf1, 0xd1, 0x80, 0x7f, 0x2c, 0x1a, 0x56, 0xf7, 0xfa, 0x29, 0x30,
	0x37, 0x6c, 0x2c, 0x66, 0x6d, 0xd4, 0x4e, 0x95, 0x66, 0x4e, 0xdd, 0x6e, 0x35, 0x7a, 0x6e, 0x76,
	0x85, 0xd1, 0x5b, 0xb3, 0xbd, 0x45, 0xb5, 0xc5, 0x0f, 0x86, 0x77, 0x33, 0x9b, 0x64, 0x46, 0xaa,
	0x77, 0x81, 0x77, 0x2d, 0x64, 0xf8, 0x5c, 0x36, 0x7d, 0xb6, 0xe1, 0xb1, 0x4d, 0x42, 0x1a, 0xc9,
	0xc6, 0x7b, 0x1b, 0x70, 0xa3, 0xc2, 0x44, 0x79, 0xff, 0x94, 0x9c, 0x61, 0x0c, 0x3b, 0x37, 0xeb,
	0x64, 0x21, 0xee, 0xc3, 0x96, 0x59, 0x63, 0x87, 0x6c, 0xb8, 0x18, 0x81, 0x6e, 0x79, 0xee, 0x77,
	0xb0, 0x9d, 0xab, 0x8a, 0x85, 0xe8, 0xff, 0xa0, 0x32, 0x61, 0xc3, 0x18, 0x7b, 0xe6, 0x12, 0x21,
	0x99, 0xf8, 0x9d, 0x58, 0x76, 0x1d, 0xcf, 0x54, 0xf0, 0x93, 0xec, 0xd8, 0x82, 0x8d, 0x79, 0x3d,
	0x2c, 0xc4, 0x53, 0x55, 0x55, 0xa9, 0x65, 0x56, 0x3c, 0xf0, 0x95, 0x2a, 0x12, 0xd5, 0x4e, 0x79,
	0x5b, 0xba, 0xde, 0x7a, 0x55, 0xbd, 0xcc, 0x17, 0x59, 0xda, 0x9c, 0x72, 0xd6, 0x9c, 0x6d, 0x55,
	0x38, 0x99, 0x67, 0x59, 0x88, 0x7b, 0xb0, 0xa1, 0x56, 0x1c, 0xb1, 0x68, 0xdf, 0x88, 0x02, 0xc6,
	0x48, 0x2a, 0xa5, 0x46, 0xd2, 0x3b, 0xd8, 0xcc, 0x51, 0xf1, 0xd9, 0x63, 0x62, 0xf7, 0xef, 0x45,
	0xa8, 0x27, 0x7f, 0x16, 0x40, 0x1b, 0x60, 0x25, 0x87, 0xd9, 0x27, 0xc7, 0x1a, 0xb4, 0x12, 0xea,
	0x09, 0xb9, 0xe6, 0x56, 0x51, 0x7c, 0x3c, 0x24, 0xa4, 0xfd, 0x29, 0xb1, 0x4a, 0xe2, 0xfb, 0x23,
	0xa1, 0xbc, 0x9d, 0x50, 0xee, 0xbb, 0x34, 0xb0, 0xca, 0x29, 0x8d, 0xfa, 0x6f, 0x49, 0x56, 0x45,
	0x7c, 0xc4, 0x24, 0xd4, 0xe4, 0x6f, 0x41, 0xd6, 0x12, 0x42, 0xb0, 0xa2, 0xdb, 0x4c, 0x1b, 0x6a,
	0x55, 0x77, 0x8f, 0xa0, 0x61, 0x74, 0x93, 0x78, 0xc7, 0x6c, 0x2e, 0x72, 0xe9, 0x93, 0x2b, 0xab,
	0x80, 0xd6, 0x61, 0xd5, 0x20, 0x1f, 0x39, 0x8c, 0xa9, 0x6f, 0xa2, 0x94, 0xec, 0x47, 0xe2, 0x72,
	0xab, 0xb4, 0xfb, 0x4b, 0x80, 0x19, 0x7e, 0x4a, 0xa1, 0xe4, 0x14, 0x3f, 0x5b, 0x10, 0xa6, 0xcc,
	0xc8, 0xf2, 0x83, 0xa8, 0xd8, 0xfd, 0xb1, 0x01, 0xcb, 0x7a, 0x2b, 0x45, 0x47, 0xb0, 0x9a, 0x59,
	0xfc, 0xd1, 0x83, 0xf4, 0x0c, 0x49, 0x7f, 0x8f, 0x74, 0x1e, 0xde, 0xc0, 0x65, 0x21, 0x2e, 0xa0,
	0x3f, 0x40, 0x2b, 0xb5, 0xf8, 0x22, 0xa3, 0x22, 0xb3, 0x6b, 0x74, 0xe7, 0xfe, 0x42, 0x5e, 0xac,
	0x2b, 0xb5, 0x96, 0x9a, 0xba, 0xb2, 0x7b, 0xb2, 0xa9, 0x6b, 0x6e, 0x97, 0x55, 0xba, 0x52, 0x4b,
	0xa5, 0xa9, 0x2b, 0xbb, 0xbd, 0x9a, 0xba, 0xe6, 0x36, 0x51, 0x5c, 0x10, 0x51, 0xcb, 0x6c, 0x87,
	0x66, 0xd4, 0xe6, 0xd7, 0x4f, 0x33, 0x6a, 0x39, 0x6b, 0x25, 0x2e, 0xa0, 0x63, 0x09, 0x95, 0x29,
	0x60, 0x46, 0x0f, 0x53, 0x46, 0x64, 0x97, 0xad, 0xce, 0xa3, 0x9b, 0xd8, 0x52, 0xe9, 0x05, 0xb4,
	0x17, 0x6d, 0x1a, 0xe8, 0x99, 0x69, 0xd1, 0xc2, 0x35, 0xa7, 0xf3, 0xfc, 0x2e, 0x62, 0xf2, 0xb1,
	0x11, 0x6c, 0x2f, 0xd8, 0x2e, 0xd0, 0x53, 0x23, 0xcb, 0x0b, 0x37, 0x9b, 0xce, 0xb3, 0x3b, 0x48,
	0xc9, 0x97, 0x0e, 0xa0, 0x69, 0xce, 0x69, 0x74, 0x2f, 0x93, 0x2c, 0x23, 0x46, 0x9d, 0x45, 0x2c,
	0xa9, 0xe8, 0x03, 0xac, 0xcd, 0x01, 0x3e, 0x7a, 0x94, 0x53, 0xe0, 0xc6, 0xe6, 0xd1, 0x79, 0x7c,
	0x23, 0x7f, 0x4e, 0x6f, 0x0c, 0xb5, 0xb9, 0x7a, 0x8d, 0xdd, 0x20, 0x57, 0xaf, 0x89, 0xd3, 0xb8,
	0x80, 0xfe, 0x24, 0xb7, 0xee, 0x2c, 0x40, 0xa1, 0x9d, 0x7c, 0x27, 0x67, 0x50, 0xd8, 0x79, 0x72,
	0x8b, 0x44, 0x5c, 0x82, 0x59, 0xd8, 0x31, 0x4b, 0x30, 0x07, 0xda, 0xcc, 0x12, 0xcc, 0x45, 0xac,
	0x02, 0xba, 0x82, 0x07, 0x37, 0x41, 0x38, 0xfa, 0x7f, 0x53, 0xc3, 0x8d, 0xeb, 0x43, 0x67, 0xf7,
	0xae, 0xa2, 0x66, 0x0e, 0x52, 0xa8, 0x95, 0xcd, 0x41, 0x16, 0x49, 0xb3, 0x39, 0x98, 0x87, 0x3c,
	0xa9, 0x77, 0x0e, 0xb1, 0x4c, 0xbd, 0x79, 0x88, 0x68, 0xea, 0xcd, 0x85, 0x3b, 0x5c, 0x78, 0xfd,
	0xd5, 0x1f, 0x77, 0x87, 0x74, 0xec, 0x04, 0xc3, 0xbd, 0x5f, 0x74, 0x39, 0xdf, 0x73, 0xe9, 0xe4,
	0x85, 0xfc, 0x7f, 0x86, 0x4b, 0xc7, 0x2f, 0x18, 0x89, 0x2e, 0x7d, 0x97, 0xb0, 0xe4, 0x5f, 0x1d,
	0x67, 0x55, 0xc9, 0xfb, 0xe6, 0xbf, 0x01, 0x00, 0x00, 0xff, 0xff, 0x94, 0x0a, 0x1b, 0x76, 0x06,
	0x19, 0x00, 0x00,
}
