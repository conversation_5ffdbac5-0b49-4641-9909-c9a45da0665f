// Code generated by protoc-gen-gogo.
// source: src/exchangeLiveBroEarnings/exchangelivebro.proto
// DO NOT EDIT!

/*
	Package exchangelivebro is a generated protocol buffer package.

	It is generated from these files:
		src/exchangeLiveBroEarnings/exchangelivebro.proto

	It has these top-level messages:
		LiveBroEarnInfo
		PresentPackage
		ExchangeScheme
		GetLiveUserExchangeInfoReq
		GetLiveUserExchangeInfoResp
		ExchangeLiveEarnReq
		ExchangeLiveEarnResp
		GetLiveUserExchangeLogReq
		GetLiveUserExchangeLogResp
		AddLiveUserReq
		AddLiveUserResp
*/
package exchangelivebro

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type ExchangeType int32

const (
	ExchangeType_ENUM_EXCHANGE_TBEAN      ExchangeType = 0
	ExchangeType_ENUM_EXCHANGE_USER_EXP   ExchangeType = 1
	ExchangeType_ENUM_EXCHANGE_ANCHOR_EXP ExchangeType = 2
)

var ExchangeType_name = map[int32]string{
	0: "ENUM_EXCHANGE_TBEAN",
	1: "ENUM_EXCHANGE_USER_EXP",
	2: "ENUM_EXCHANGE_ANCHOR_EXP",
}
var ExchangeType_value = map[string]int32{
	"ENUM_EXCHANGE_TBEAN":      0,
	"ENUM_EXCHANGE_USER_EXP":   1,
	"ENUM_EXCHANGE_ANCHOR_EXP": 2,
}

func (x ExchangeType) String() string {
	return proto.EnumName(ExchangeType_name, int32(x))
}
func (ExchangeType) EnumDescriptor() ([]byte, []int) { return fileDescriptorExchangelivebro, []int{0} }

// 操作类型
type OperType int32

const (
	OperType_ENUM_USER_EXCHANGE      OperType = 0
	OperType_ENUM_ERROR_EXCHANGE_ADD OperType = 1
)

var OperType_name = map[int32]string{
	0: "ENUM_USER_EXCHANGE",
	1: "ENUM_ERROR_EXCHANGE_ADD",
}
var OperType_value = map[string]int32{
	"ENUM_USER_EXCHANGE":      0,
	"ENUM_ERROR_EXCHANGE_ADD": 1,
}

func (x OperType) String() string {
	return proto.EnumName(OperType_name, int32(x))
}
func (OperType) EnumDescriptor() ([]byte, []int) { return fileDescriptorExchangelivebro, []int{1} }

type ExChangeFlag int32

const (
	ExChangeFlag_ENUM_FLAG_NO_EXCHANGE      ExChangeFlag = 0
	ExChangeFlag_ENUM_FLAG_ALREADY_EXCHANGE ExChangeFlag = 1
)

var ExChangeFlag_name = map[int32]string{
	0: "ENUM_FLAG_NO_EXCHANGE",
	1: "ENUM_FLAG_ALREADY_EXCHANGE",
}
var ExChangeFlag_value = map[string]int32{
	"ENUM_FLAG_NO_EXCHANGE":      0,
	"ENUM_FLAG_ALREADY_EXCHANGE": 1,
}

func (x ExChangeFlag) String() string {
	return proto.EnumName(ExChangeFlag_name, int32(x))
}
func (ExChangeFlag) EnumDescriptor() ([]byte, []int) { return fileDescriptorExchangelivebro, []int{2} }

type LiveBroEarnInfo struct {
	Tbean        uint64 `protobuf:"varint,1,opt,name=tbean,proto3" json:"tbean,omitempty"`
	UserExp      uint64 `protobuf:"varint,2,opt,name=user_exp,json=userExp,proto3" json:"user_exp,omitempty"`
	AnchorExp    uint64 `protobuf:"varint,3,opt,name=anchor_exp,json=anchorExp,proto3" json:"anchor_exp,omitempty"`
	ExchangeFlag uint32 `protobuf:"varint,4,opt,name=exchange_flag,json=exchangeFlag,proto3" json:"exchange_flag,omitempty"`
}

func (m *LiveBroEarnInfo) Reset()                    { *m = LiveBroEarnInfo{} }
func (m *LiveBroEarnInfo) String() string            { return proto.CompactTextString(m) }
func (*LiveBroEarnInfo) ProtoMessage()               {}
func (*LiveBroEarnInfo) Descriptor() ([]byte, []int) { return fileDescriptorExchangelivebro, []int{0} }

func (m *LiveBroEarnInfo) GetTbean() uint64 {
	if m != nil {
		return m.Tbean
	}
	return 0
}

func (m *LiveBroEarnInfo) GetUserExp() uint64 {
	if m != nil {
		return m.UserExp
	}
	return 0
}

func (m *LiveBroEarnInfo) GetAnchorExp() uint64 {
	if m != nil {
		return m.AnchorExp
	}
	return 0
}

func (m *LiveBroEarnInfo) GetExchangeFlag() uint32 {
	if m != nil {
		return m.ExchangeFlag
	}
	return 0
}

type PresentPackage struct {
	ItemId    uint32 `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemName  string `protobuf:"bytes,2,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	ItemPrice uint32 `protobuf:"varint,3,opt,name=item_price,json=itemPrice,proto3" json:"item_price,omitempty"`
	ItemNum   uint32 `protobuf:"varint,4,opt,name=item_num,json=itemNum,proto3" json:"item_num,omitempty"`
	ItemUrl   string `protobuf:"bytes,5,opt,name=item_url,json=itemUrl,proto3" json:"item_url,omitempty"`
	PackageId uint32 `protobuf:"varint,6,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
}

func (m *PresentPackage) Reset()                    { *m = PresentPackage{} }
func (m *PresentPackage) String() string            { return proto.CompactTextString(m) }
func (*PresentPackage) ProtoMessage()               {}
func (*PresentPackage) Descriptor() ([]byte, []int) { return fileDescriptorExchangelivebro, []int{1} }

func (m *PresentPackage) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentPackage) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *PresentPackage) GetItemPrice() uint32 {
	if m != nil {
		return m.ItemPrice
	}
	return 0
}

func (m *PresentPackage) GetItemNum() uint32 {
	if m != nil {
		return m.ItemNum
	}
	return 0
}

func (m *PresentPackage) GetItemUrl() string {
	if m != nil {
		return m.ItemUrl
	}
	return ""
}

func (m *PresentPackage) GetPackageId() uint32 {
	if m != nil {
		return m.PackageId
	}
	return 0
}

type ExchangeScheme struct {
	Packages []*PresentPackage `protobuf:"bytes,1,rep,name=packages" json:"packages,omitempty"`
	RichVal  uint64            `protobuf:"varint,2,opt,name=rich_val,json=richVal,proto3" json:"rich_val,omitempty"`
	CharmVal uint64            `protobuf:"varint,3,opt,name=charm_val,json=charmVal,proto3" json:"charm_val,omitempty"`
}

func (m *ExchangeScheme) Reset()                    { *m = ExchangeScheme{} }
func (m *ExchangeScheme) String() string            { return proto.CompactTextString(m) }
func (*ExchangeScheme) ProtoMessage()               {}
func (*ExchangeScheme) Descriptor() ([]byte, []int) { return fileDescriptorExchangelivebro, []int{2} }

func (m *ExchangeScheme) GetPackages() []*PresentPackage {
	if m != nil {
		return m.Packages
	}
	return nil
}

func (m *ExchangeScheme) GetRichVal() uint64 {
	if m != nil {
		return m.RichVal
	}
	return 0
}

func (m *ExchangeScheme) GetCharmVal() uint64 {
	if m != nil {
		return m.CharmVal
	}
	return 0
}

type GetLiveUserExchangeInfoReq struct {
}

func (m *GetLiveUserExchangeInfoReq) Reset()         { *m = GetLiveUserExchangeInfoReq{} }
func (m *GetLiveUserExchangeInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveUserExchangeInfoReq) ProtoMessage()    {}
func (*GetLiveUserExchangeInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorExchangelivebro, []int{3}
}

type GetLiveUserExchangeInfoResp struct {
	LiveInfo       *LiveBroEarnInfo `protobuf:"bytes,1,opt,name=live_info,json=liveInfo" json:"live_info,omitempty"`
	ExchangeScheme *ExchangeScheme  `protobuf:"bytes,2,opt,name=exchange_scheme,json=exchangeScheme" json:"exchange_scheme,omitempty"`
}

func (m *GetLiveUserExchangeInfoResp) Reset()         { *m = GetLiveUserExchangeInfoResp{} }
func (m *GetLiveUserExchangeInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetLiveUserExchangeInfoResp) ProtoMessage()    {}
func (*GetLiveUserExchangeInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorExchangelivebro, []int{4}
}

func (m *GetLiveUserExchangeInfoResp) GetLiveInfo() *LiveBroEarnInfo {
	if m != nil {
		return m.LiveInfo
	}
	return nil
}

func (m *GetLiveUserExchangeInfoResp) GetExchangeScheme() *ExchangeScheme {
	if m != nil {
		return m.ExchangeScheme
	}
	return nil
}

type ExchangeLiveEarnReq struct {
}

func (m *ExchangeLiveEarnReq) Reset()         { *m = ExchangeLiveEarnReq{} }
func (m *ExchangeLiveEarnReq) String() string { return proto.CompactTextString(m) }
func (*ExchangeLiveEarnReq) ProtoMessage()    {}
func (*ExchangeLiveEarnReq) Descriptor() ([]byte, []int) {
	return fileDescriptorExchangelivebro, []int{5}
}

type ExchangeLiveEarnResp struct {
	ExchangedTbean     uint64 `protobuf:"varint,1,opt,name=exchanged_tbean,json=exchangedTbean,proto3" json:"exchanged_tbean,omitempty"`
	ExchangedUserExp   uint64 `protobuf:"varint,2,opt,name=exchanged_user_exp,json=exchangedUserExp,proto3" json:"exchanged_user_exp,omitempty"`
	ExchangedAnchorExp uint64 `protobuf:"varint,3,opt,name=exchanged_anchor_exp,json=exchangedAnchorExp,proto3" json:"exchanged_anchor_exp,omitempty"`
}

func (m *ExchangeLiveEarnResp) Reset()         { *m = ExchangeLiveEarnResp{} }
func (m *ExchangeLiveEarnResp) String() string { return proto.CompactTextString(m) }
func (*ExchangeLiveEarnResp) ProtoMessage()    {}
func (*ExchangeLiveEarnResp) Descriptor() ([]byte, []int) {
	return fileDescriptorExchangelivebro, []int{6}
}

func (m *ExchangeLiveEarnResp) GetExchangedTbean() uint64 {
	if m != nil {
		return m.ExchangedTbean
	}
	return 0
}

func (m *ExchangeLiveEarnResp) GetExchangedUserExp() uint64 {
	if m != nil {
		return m.ExchangedUserExp
	}
	return 0
}

func (m *ExchangeLiveEarnResp) GetExchangedAnchorExp() uint64 {
	if m != nil {
		return m.ExchangedAnchorExp
	}
	return 0
}

type GetLiveUserExchangeLogReq struct {
}

func (m *GetLiveUserExchangeLogReq) Reset()         { *m = GetLiveUserExchangeLogReq{} }
func (m *GetLiveUserExchangeLogReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveUserExchangeLogReq) ProtoMessage()    {}
func (*GetLiveUserExchangeLogReq) Descriptor() ([]byte, []int) {
	return fileDescriptorExchangelivebro, []int{7}
}

type GetLiveUserExchangeLogResp struct {
	HaveLog    bool   `protobuf:"varint,1,opt,name=have_log,json=haveLog,proto3" json:"have_log,omitempty"`
	Tbean      uint64 `protobuf:"varint,2,opt,name=tbean,proto3" json:"tbean,omitempty"`
	UserExp    uint64 `protobuf:"varint,3,opt,name=user_exp,json=userExp,proto3" json:"user_exp,omitempty"`
	AnchorExp  uint64 `protobuf:"varint,4,opt,name=anchor_exp,json=anchorExp,proto3" json:"anchor_exp,omitempty"`
	UpdateTime uint32 `protobuf:"varint,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (m *GetLiveUserExchangeLogResp) Reset()         { *m = GetLiveUserExchangeLogResp{} }
func (m *GetLiveUserExchangeLogResp) String() string { return proto.CompactTextString(m) }
func (*GetLiveUserExchangeLogResp) ProtoMessage()    {}
func (*GetLiveUserExchangeLogResp) Descriptor() ([]byte, []int) {
	return fileDescriptorExchangelivebro, []int{8}
}

func (m *GetLiveUserExchangeLogResp) GetHaveLog() bool {
	if m != nil {
		return m.HaveLog
	}
	return false
}

func (m *GetLiveUserExchangeLogResp) GetTbean() uint64 {
	if m != nil {
		return m.Tbean
	}
	return 0
}

func (m *GetLiveUserExchangeLogResp) GetUserExp() uint64 {
	if m != nil {
		return m.UserExp
	}
	return 0
}

func (m *GetLiveUserExchangeLogResp) GetAnchorExp() uint64 {
	if m != nil {
		return m.AnchorExp
	}
	return 0
}

func (m *GetLiveUserExchangeLogResp) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type AddLiveUserReq struct {
	Tbean     uint64 `protobuf:"varint,1,opt,name=tbean,proto3" json:"tbean,omitempty"`
	UserExp   uint64 `protobuf:"varint,2,opt,name=user_exp,json=userExp,proto3" json:"user_exp,omitempty"`
	AnchorExp uint64 `protobuf:"varint,3,opt,name=anchor_exp,json=anchorExp,proto3" json:"anchor_exp,omitempty"`
}

func (m *AddLiveUserReq) Reset()                    { *m = AddLiveUserReq{} }
func (m *AddLiveUserReq) String() string            { return proto.CompactTextString(m) }
func (*AddLiveUserReq) ProtoMessage()               {}
func (*AddLiveUserReq) Descriptor() ([]byte, []int) { return fileDescriptorExchangelivebro, []int{9} }

func (m *AddLiveUserReq) GetTbean() uint64 {
	if m != nil {
		return m.Tbean
	}
	return 0
}

func (m *AddLiveUserReq) GetUserExp() uint64 {
	if m != nil {
		return m.UserExp
	}
	return 0
}

func (m *AddLiveUserReq) GetAnchorExp() uint64 {
	if m != nil {
		return m.AnchorExp
	}
	return 0
}

type AddLiveUserResp struct {
}

func (m *AddLiveUserResp) Reset()                    { *m = AddLiveUserResp{} }
func (m *AddLiveUserResp) String() string            { return proto.CompactTextString(m) }
func (*AddLiveUserResp) ProtoMessage()               {}
func (*AddLiveUserResp) Descriptor() ([]byte, []int) { return fileDescriptorExchangelivebro, []int{10} }

func init() {
	proto.RegisterType((*LiveBroEarnInfo)(nil), "exchangelivebro.LiveBroEarnInfo")
	proto.RegisterType((*PresentPackage)(nil), "exchangelivebro.PresentPackage")
	proto.RegisterType((*ExchangeScheme)(nil), "exchangelivebro.ExchangeScheme")
	proto.RegisterType((*GetLiveUserExchangeInfoReq)(nil), "exchangelivebro.GetLiveUserExchangeInfoReq")
	proto.RegisterType((*GetLiveUserExchangeInfoResp)(nil), "exchangelivebro.GetLiveUserExchangeInfoResp")
	proto.RegisterType((*ExchangeLiveEarnReq)(nil), "exchangelivebro.ExchangeLiveEarnReq")
	proto.RegisterType((*ExchangeLiveEarnResp)(nil), "exchangelivebro.ExchangeLiveEarnResp")
	proto.RegisterType((*GetLiveUserExchangeLogReq)(nil), "exchangelivebro.GetLiveUserExchangeLogReq")
	proto.RegisterType((*GetLiveUserExchangeLogResp)(nil), "exchangelivebro.GetLiveUserExchangeLogResp")
	proto.RegisterType((*AddLiveUserReq)(nil), "exchangelivebro.AddLiveUserReq")
	proto.RegisterType((*AddLiveUserResp)(nil), "exchangelivebro.AddLiveUserResp")
	proto.RegisterEnum("exchangelivebro.ExchangeType", ExchangeType_name, ExchangeType_value)
	proto.RegisterEnum("exchangelivebro.OperType", OperType_name, OperType_value)
	proto.RegisterEnum("exchangelivebro.ExChangeFlag", ExChangeFlag_name, ExChangeFlag_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Exchangelivebro service

type ExchangelivebroClient interface {
	GetLiveUserExchangeInfo(ctx context.Context, in *GetLiveUserExchangeInfoReq, opts ...grpc.CallOption) (*GetLiveUserExchangeInfoResp, error)
	ExchangeLiveEarn(ctx context.Context, in *ExchangeLiveEarnReq, opts ...grpc.CallOption) (*ExchangeLiveEarnResp, error)
	GetLiveUserExchangeLog(ctx context.Context, in *GetLiveUserExchangeLogReq, opts ...grpc.CallOption) (*GetLiveUserExchangeLogResp, error)
	AddLiveUser(ctx context.Context, in *AddLiveUserReq, opts ...grpc.CallOption) (*AddLiveUserResp, error)
}

type exchangelivebroClient struct {
	cc *grpc.ClientConn
}

func NewExchangelivebroClient(cc *grpc.ClientConn) ExchangelivebroClient {
	return &exchangelivebroClient{cc}
}

func (c *exchangelivebroClient) GetLiveUserExchangeInfo(ctx context.Context, in *GetLiveUserExchangeInfoReq, opts ...grpc.CallOption) (*GetLiveUserExchangeInfoResp, error) {
	out := new(GetLiveUserExchangeInfoResp)
	err := grpc.Invoke(ctx, "/exchangelivebro.exchangelivebro/GetLiveUserExchangeInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangelivebroClient) ExchangeLiveEarn(ctx context.Context, in *ExchangeLiveEarnReq, opts ...grpc.CallOption) (*ExchangeLiveEarnResp, error) {
	out := new(ExchangeLiveEarnResp)
	err := grpc.Invoke(ctx, "/exchangelivebro.exchangelivebro/ExchangeLiveEarn", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangelivebroClient) GetLiveUserExchangeLog(ctx context.Context, in *GetLiveUserExchangeLogReq, opts ...grpc.CallOption) (*GetLiveUserExchangeLogResp, error) {
	out := new(GetLiveUserExchangeLogResp)
	err := grpc.Invoke(ctx, "/exchangelivebro.exchangelivebro/GetLiveUserExchangeLog", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangelivebroClient) AddLiveUser(ctx context.Context, in *AddLiveUserReq, opts ...grpc.CallOption) (*AddLiveUserResp, error) {
	out := new(AddLiveUserResp)
	err := grpc.Invoke(ctx, "/exchangelivebro.exchangelivebro/AddLiveUser", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Exchangelivebro service

type ExchangelivebroServer interface {
	GetLiveUserExchangeInfo(context.Context, *GetLiveUserExchangeInfoReq) (*GetLiveUserExchangeInfoResp, error)
	ExchangeLiveEarn(context.Context, *ExchangeLiveEarnReq) (*ExchangeLiveEarnResp, error)
	GetLiveUserExchangeLog(context.Context, *GetLiveUserExchangeLogReq) (*GetLiveUserExchangeLogResp, error)
	AddLiveUser(context.Context, *AddLiveUserReq) (*AddLiveUserResp, error)
}

func RegisterExchangelivebroServer(s *grpc.Server, srv ExchangelivebroServer) {
	s.RegisterService(&_Exchangelivebro_serviceDesc, srv)
}

func _Exchangelivebro_GetLiveUserExchangeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveUserExchangeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangelivebroServer).GetLiveUserExchangeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchangelivebro.exchangelivebro/GetLiveUserExchangeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangelivebroServer).GetLiveUserExchangeInfo(ctx, req.(*GetLiveUserExchangeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Exchangelivebro_ExchangeLiveEarn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExchangeLiveEarnReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangelivebroServer).ExchangeLiveEarn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchangelivebro.exchangelivebro/ExchangeLiveEarn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangelivebroServer).ExchangeLiveEarn(ctx, req.(*ExchangeLiveEarnReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Exchangelivebro_GetLiveUserExchangeLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveUserExchangeLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangelivebroServer).GetLiveUserExchangeLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchangelivebro.exchangelivebro/GetLiveUserExchangeLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangelivebroServer).GetLiveUserExchangeLog(ctx, req.(*GetLiveUserExchangeLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Exchangelivebro_AddLiveUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddLiveUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangelivebroServer).AddLiveUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/exchangelivebro.exchangelivebro/AddLiveUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangelivebroServer).AddLiveUser(ctx, req.(*AddLiveUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Exchangelivebro_serviceDesc = grpc.ServiceDesc{
	ServiceName: "exchangelivebro.exchangelivebro",
	HandlerType: (*ExchangelivebroServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLiveUserExchangeInfo",
			Handler:    _Exchangelivebro_GetLiveUserExchangeInfo_Handler,
		},
		{
			MethodName: "ExchangeLiveEarn",
			Handler:    _Exchangelivebro_ExchangeLiveEarn_Handler,
		},
		{
			MethodName: "GetLiveUserExchangeLog",
			Handler:    _Exchangelivebro_GetLiveUserExchangeLog_Handler,
		},
		{
			MethodName: "AddLiveUser",
			Handler:    _Exchangelivebro_AddLiveUser_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/exchangeLiveBroEarnings/exchangelivebro.proto",
}

func (m *LiveBroEarnInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LiveBroEarnInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Tbean != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.Tbean))
	}
	if m.UserExp != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.UserExp))
	}
	if m.AnchorExp != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.AnchorExp))
	}
	if m.ExchangeFlag != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.ExchangeFlag))
	}
	return i, nil
}

func (m *PresentPackage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PresentPackage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.ItemId))
	}
	if len(m.ItemName) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(len(m.ItemName)))
		i += copy(dAtA[i:], m.ItemName)
	}
	if m.ItemPrice != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.ItemPrice))
	}
	if m.ItemNum != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.ItemNum))
	}
	if len(m.ItemUrl) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(len(m.ItemUrl)))
		i += copy(dAtA[i:], m.ItemUrl)
	}
	if m.PackageId != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.PackageId))
	}
	return i, nil
}

func (m *ExchangeScheme) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExchangeScheme) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Packages) > 0 {
		for _, msg := range m.Packages {
			dAtA[i] = 0xa
			i++
			i = encodeVarintExchangelivebro(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.RichVal != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.RichVal))
	}
	if m.CharmVal != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.CharmVal))
	}
	return i, nil
}

func (m *GetLiveUserExchangeInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLiveUserExchangeInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetLiveUserExchangeInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLiveUserExchangeInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.LiveInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.LiveInfo.Size()))
		n1, err := m.LiveInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if m.ExchangeScheme != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.ExchangeScheme.Size()))
		n2, err := m.ExchangeScheme.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *ExchangeLiveEarnReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExchangeLiveEarnReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *ExchangeLiveEarnResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExchangeLiveEarnResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ExchangedTbean != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.ExchangedTbean))
	}
	if m.ExchangedUserExp != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.ExchangedUserExp))
	}
	if m.ExchangedAnchorExp != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.ExchangedAnchorExp))
	}
	return i, nil
}

func (m *GetLiveUserExchangeLogReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLiveUserExchangeLogReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetLiveUserExchangeLogResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLiveUserExchangeLogResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.HaveLog {
		dAtA[i] = 0x8
		i++
		if m.HaveLog {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.Tbean != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.Tbean))
	}
	if m.UserExp != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.UserExp))
	}
	if m.AnchorExp != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.AnchorExp))
	}
	if m.UpdateTime != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.UpdateTime))
	}
	return i, nil
}

func (m *AddLiveUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddLiveUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Tbean != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.Tbean))
	}
	if m.UserExp != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.UserExp))
	}
	if m.AnchorExp != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintExchangelivebro(dAtA, i, uint64(m.AnchorExp))
	}
	return i, nil
}

func (m *AddLiveUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddLiveUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Exchangelivebro(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Exchangelivebro(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintExchangelivebro(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *LiveBroEarnInfo) Size() (n int) {
	var l int
	_ = l
	if m.Tbean != 0 {
		n += 1 + sovExchangelivebro(uint64(m.Tbean))
	}
	if m.UserExp != 0 {
		n += 1 + sovExchangelivebro(uint64(m.UserExp))
	}
	if m.AnchorExp != 0 {
		n += 1 + sovExchangelivebro(uint64(m.AnchorExp))
	}
	if m.ExchangeFlag != 0 {
		n += 1 + sovExchangelivebro(uint64(m.ExchangeFlag))
	}
	return n
}

func (m *PresentPackage) Size() (n int) {
	var l int
	_ = l
	if m.ItemId != 0 {
		n += 1 + sovExchangelivebro(uint64(m.ItemId))
	}
	l = len(m.ItemName)
	if l > 0 {
		n += 1 + l + sovExchangelivebro(uint64(l))
	}
	if m.ItemPrice != 0 {
		n += 1 + sovExchangelivebro(uint64(m.ItemPrice))
	}
	if m.ItemNum != 0 {
		n += 1 + sovExchangelivebro(uint64(m.ItemNum))
	}
	l = len(m.ItemUrl)
	if l > 0 {
		n += 1 + l + sovExchangelivebro(uint64(l))
	}
	if m.PackageId != 0 {
		n += 1 + sovExchangelivebro(uint64(m.PackageId))
	}
	return n
}

func (m *ExchangeScheme) Size() (n int) {
	var l int
	_ = l
	if len(m.Packages) > 0 {
		for _, e := range m.Packages {
			l = e.Size()
			n += 1 + l + sovExchangelivebro(uint64(l))
		}
	}
	if m.RichVal != 0 {
		n += 1 + sovExchangelivebro(uint64(m.RichVal))
	}
	if m.CharmVal != 0 {
		n += 1 + sovExchangelivebro(uint64(m.CharmVal))
	}
	return n
}

func (m *GetLiveUserExchangeInfoReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetLiveUserExchangeInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.LiveInfo != nil {
		l = m.LiveInfo.Size()
		n += 1 + l + sovExchangelivebro(uint64(l))
	}
	if m.ExchangeScheme != nil {
		l = m.ExchangeScheme.Size()
		n += 1 + l + sovExchangelivebro(uint64(l))
	}
	return n
}

func (m *ExchangeLiveEarnReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *ExchangeLiveEarnResp) Size() (n int) {
	var l int
	_ = l
	if m.ExchangedTbean != 0 {
		n += 1 + sovExchangelivebro(uint64(m.ExchangedTbean))
	}
	if m.ExchangedUserExp != 0 {
		n += 1 + sovExchangelivebro(uint64(m.ExchangedUserExp))
	}
	if m.ExchangedAnchorExp != 0 {
		n += 1 + sovExchangelivebro(uint64(m.ExchangedAnchorExp))
	}
	return n
}

func (m *GetLiveUserExchangeLogReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetLiveUserExchangeLogResp) Size() (n int) {
	var l int
	_ = l
	if m.HaveLog {
		n += 2
	}
	if m.Tbean != 0 {
		n += 1 + sovExchangelivebro(uint64(m.Tbean))
	}
	if m.UserExp != 0 {
		n += 1 + sovExchangelivebro(uint64(m.UserExp))
	}
	if m.AnchorExp != 0 {
		n += 1 + sovExchangelivebro(uint64(m.AnchorExp))
	}
	if m.UpdateTime != 0 {
		n += 1 + sovExchangelivebro(uint64(m.UpdateTime))
	}
	return n
}

func (m *AddLiveUserReq) Size() (n int) {
	var l int
	_ = l
	if m.Tbean != 0 {
		n += 1 + sovExchangelivebro(uint64(m.Tbean))
	}
	if m.UserExp != 0 {
		n += 1 + sovExchangelivebro(uint64(m.UserExp))
	}
	if m.AnchorExp != 0 {
		n += 1 + sovExchangelivebro(uint64(m.AnchorExp))
	}
	return n
}

func (m *AddLiveUserResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovExchangelivebro(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozExchangelivebro(x uint64) (n int) {
	return sovExchangelivebro(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *LiveBroEarnInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowExchangelivebro
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LiveBroEarnInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LiveBroEarnInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Tbean", wireType)
			}
			m.Tbean = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Tbean |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserExp", wireType)
			}
			m.UserExp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserExp |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AnchorExp", wireType)
			}
			m.AnchorExp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AnchorExp |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeFlag", wireType)
			}
			m.ExchangeFlag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExchangeFlag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipExchangelivebro(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthExchangelivebro
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PresentPackage) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowExchangelivebro
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PresentPackage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PresentPackage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthExchangelivebro
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemPrice", wireType)
			}
			m.ItemPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemNum", wireType)
			}
			m.ItemNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthExchangelivebro
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PackageId", wireType)
			}
			m.PackageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PackageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipExchangelivebro(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthExchangelivebro
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExchangeScheme) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowExchangelivebro
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ExchangeScheme: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ExchangeScheme: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Packages", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthExchangelivebro
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Packages = append(m.Packages, &PresentPackage{})
			if err := m.Packages[len(m.Packages)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RichVal", wireType)
			}
			m.RichVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RichVal |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CharmVal", wireType)
			}
			m.CharmVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CharmVal |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipExchangelivebro(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthExchangelivebro
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLiveUserExchangeInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowExchangelivebro
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLiveUserExchangeInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLiveUserExchangeInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipExchangelivebro(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthExchangelivebro
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLiveUserExchangeInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowExchangelivebro
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLiveUserExchangeInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLiveUserExchangeInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LiveInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthExchangelivebro
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.LiveInfo == nil {
				m.LiveInfo = &LiveBroEarnInfo{}
			}
			if err := m.LiveInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeScheme", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthExchangelivebro
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ExchangeScheme == nil {
				m.ExchangeScheme = &ExchangeScheme{}
			}
			if err := m.ExchangeScheme.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipExchangelivebro(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthExchangelivebro
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExchangeLiveEarnReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowExchangelivebro
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ExchangeLiveEarnReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ExchangeLiveEarnReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipExchangelivebro(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthExchangelivebro
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExchangeLiveEarnResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowExchangelivebro
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ExchangeLiveEarnResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ExchangeLiveEarnResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangedTbean", wireType)
			}
			m.ExchangedTbean = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExchangedTbean |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangedUserExp", wireType)
			}
			m.ExchangedUserExp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExchangedUserExp |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangedAnchorExp", wireType)
			}
			m.ExchangedAnchorExp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExchangedAnchorExp |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipExchangelivebro(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthExchangelivebro
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLiveUserExchangeLogReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowExchangelivebro
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLiveUserExchangeLogReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLiveUserExchangeLogReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipExchangelivebro(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthExchangelivebro
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLiveUserExchangeLogResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowExchangelivebro
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLiveUserExchangeLogResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLiveUserExchangeLogResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HaveLog", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HaveLog = bool(v != 0)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Tbean", wireType)
			}
			m.Tbean = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Tbean |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserExp", wireType)
			}
			m.UserExp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserExp |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AnchorExp", wireType)
			}
			m.AnchorExp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AnchorExp |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipExchangelivebro(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthExchangelivebro
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddLiveUserReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowExchangelivebro
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddLiveUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddLiveUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Tbean", wireType)
			}
			m.Tbean = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Tbean |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserExp", wireType)
			}
			m.UserExp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserExp |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AnchorExp", wireType)
			}
			m.AnchorExp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AnchorExp |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipExchangelivebro(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthExchangelivebro
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddLiveUserResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowExchangelivebro
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddLiveUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddLiveUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipExchangelivebro(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthExchangelivebro
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipExchangelivebro(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowExchangelivebro
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowExchangelivebro
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthExchangelivebro
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowExchangelivebro
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipExchangelivebro(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthExchangelivebro = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowExchangelivebro   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/exchangeLiveBroEarnings/exchangelivebro.proto", fileDescriptorExchangelivebro)
}

var fileDescriptorExchangelivebro = []byte{
	// 927 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x55, 0xcd, 0x6e, 0x1b, 0x55,
	0x14, 0xce, 0xb5, 0xdd, 0xc4, 0x39, 0xa9, 0x13, 0x73, 0x9b, 0x26, 0xce, 0xa4, 0xb8, 0xa3, 0xa1,
	0x88, 0x28, 0xad, 0x1b, 0x08, 0x3b, 0x63, 0x8c, 0x9c, 0x64, 0x9a, 0x44, 0x32, 0x4e, 0x74, 0x1b,
	0x97, 0xb2, 0x1a, 0x6e, 0x66, 0xae, 0xed, 0x51, 0xe6, 0xaf, 0xf3, 0x63, 0xa5, 0x5d, 0xb1, 0x01,
	0x21, 0x84, 0x04, 0xe2, 0x05, 0xd8, 0x44, 0x08, 0x96, 0x6c, 0x79, 0x02, 0x96, 0x3c, 0x02, 0x0a,
	0x9b, 0x3c, 0x06, 0xba, 0x77, 0x3c, 0xfe, 0x77, 0x95, 0x05, 0xcb, 0x7b, 0xbe, 0x33, 0xe7, 0x7c,
	0xf7, 0x9c, 0xef, 0xbb, 0x03, 0x1f, 0x05, 0xbe, 0xbe, 0xc3, 0x2e, 0xf5, 0x0e, 0x75, 0xda, 0xac,
	0x6e, 0x76, 0xd9, 0x9e, 0xef, 0xaa, 0xd4, 0x77, 0x4c, 0xa7, 0x1d, 0xf4, 0xe3, 0x96, 0xd9, 0x65,
	0xe7, 0xbe, 0xfb, 0xd4, 0xf3, 0xdd, 0xd0, 0xc5, 0x2b, 0x63, 0x61, 0xe9, 0x91, 0xee, 0xda, 0xb6,
	0xeb, 0xec, 0x84, 0x56, 0xd7, 0x33, 0xf5, 0x0b, 0x8b, 0xed, 0x04, 0x17, 0xe7, 0x91, 0x69, 0x85,
	0xa6, 0x13, 0xbe, 0xf6, 0x58, 0xfc, 0x99, 0xf2, 0x2d, 0x82, 0x95, 0xa1, 0x06, 0xc7, 0x4e, 0xcb,
	0xc5, 0xab, 0x70, 0x27, 0x3c, 0x67, 0xd4, 0x29, 0x20, 0x19, 0x6d, 0x65, 0x48, 0x7c, 0xc0, 0x1b,
	0x90, 0x8d, 0x02, 0xe6, 0x6b, 0xec, 0xd2, 0x2b, 0xa4, 0x04, 0xb0, 0xc0, 0xcf, 0xea, 0xa5, 0x87,
	0xdf, 0x05, 0xa0, 0x8e, 0xde, 0x71, 0x63, 0x30, 0x2d, 0xc0, 0xc5, 0x38, 0xc2, 0xe1, 0xf7, 0x20,
	0x97, 0x90, 0xd3, 0x5a, 0x16, 0x6d, 0x17, 0x32, 0x32, 0xda, 0xca, 0x91, 0xbb, 0x49, 0xf0, 0x99,
	0x45, 0xdb, 0xca, 0x9f, 0x08, 0x96, 0x4f, 0x7d, 0x16, 0x30, 0x27, 0x3c, 0xa5, 0xfa, 0x05, 0x6d,
	0x33, 0xbc, 0x0e, 0x0b, 0x66, 0xc8, 0x6c, 0xcd, 0x34, 0x04, 0x93, 0x1c, 0x99, 0xe7, 0xc7, 0x63,
	0x03, 0x6f, 0xc2, 0xa2, 0x00, 0x1c, 0x6a, 0x33, 0xc1, 0x65, 0x91, 0x64, 0x79, 0xa0, 0x41, 0x6d,
	0xc6, 0xc9, 0x08, 0xd0, 0xf3, 0x4d, 0x9d, 0x09, 0x32, 0x39, 0x22, 0xd2, 0x4f, 0x79, 0x80, 0x5f,
	0x23, 0xfe, 0x36, 0xb2, 0x7b, 0x3c, 0x44, 0x93, 0x46, 0x64, 0xf7, 0xa1, 0xc8, 0xb7, 0x0a, 0x77,
	0x44, 0x55, 0x01, 0x35, 0x7d, 0x8b, 0x17, 0xf5, 0x62, 0x56, 0x9c, 0xcd, 0x7c, 0x5c, 0xb4, 0x17,
	0x39, 0x36, 0x94, 0x6f, 0x10, 0x2c, 0xab, 0xbd, 0xdb, 0x3c, 0xd7, 0x3b, 0xcc, 0x66, 0xf8, 0x13,
	0xc8, 0xf6, 0xf0, 0xa0, 0x80, 0xe4, 0xf4, 0xd6, 0xd2, 0xee, 0xc3, 0xa7, 0xe3, 0x9b, 0x1b, 0xbd,
	0x2f, 0xe9, 0x7f, 0xc0, 0x99, 0xf8, 0xa6, 0xde, 0xd1, 0xba, 0xd4, 0x4a, 0x66, 0xcd, 0xcf, 0x2f,
	0xa8, 0xc5, 0xef, 0xae, 0x77, 0xa8, 0x6f, 0x0b, 0x2c, 0x1e, 0x75, 0x56, 0x04, 0x5e, 0x50, 0x4b,
	0x79, 0x00, 0xd2, 0x21, 0x0b, 0xf9, 0x3e, 0x9b, 0x62, 0x35, 0x71, 0x3b, 0xbe, 0x54, 0xc2, 0x5e,
	0x29, 0xbf, 0x22, 0xd8, 0x9c, 0x09, 0x07, 0x1e, 0xfe, 0x14, 0x16, 0x39, 0x33, 0xcd, 0x74, 0x5a,
	0xae, 0x98, 0xf8, 0xd2, 0xae, 0x3c, 0xc1, 0x79, 0x4c, 0x2c, 0x24, 0xcb, 0x01, 0x21, 0x9b, 0x23,
	0xe8, 0x6b, 0x50, 0x0b, 0xc4, 0x10, 0x04, 0xf7, 0x69, 0x17, 0x1f, 0x9d, 0x15, 0x59, 0x66, 0x23,
	0x67, 0xe5, 0x3e, 0xdc, 0x53, 0x87, 0xc4, 0xcf, 0x7b, 0x71, 0xfe, 0xbf, 0x20, 0x58, 0x9d, 0x8c,
	0x07, 0x1e, 0xfe, 0x60, 0xd0, 0xd9, 0xd0, 0x86, 0xa5, 0xdb, 0x2f, 0x6c, 0x9c, 0x09, 0x0d, 0x3f,
	0x01, 0x3c, 0x48, 0x1c, 0x53, 0x73, 0xbe, 0x8f, 0x34, 0x7b, 0xb2, 0xfe, 0x10, 0x56, 0x07, 0xd9,
	0x13, 0x02, 0x1f, 0x54, 0xaa, 0x25, 0x4a, 0x57, 0x36, 0x61, 0x63, 0xca, 0x80, 0xeb, 0x6e, 0x9b,
	0xd3, 0xff, 0x0d, 0x4d, 0xdd, 0x8e, 0x40, 0x03, 0x8f, 0xef, 0xbc, 0x43, 0xbb, 0x4c, 0xb3, 0xdc,
	0xb6, 0x60, 0x9f, 0x25, 0x0b, 0xfc, 0x5c, 0x77, 0xdb, 0x03, 0x43, 0xa6, 0x66, 0x19, 0x32, 0xfd,
	0x36, 0x43, 0x66, 0xc6, 0x0d, 0xf9, 0x10, 0x96, 0x22, 0xcf, 0xa0, 0x21, 0xd3, 0x42, 0xd3, 0x66,
	0x42, 0xeb, 0x39, 0x02, 0x71, 0xe8, 0xcc, 0xb4, 0x99, 0xf2, 0x15, 0x2c, 0xd7, 0x0c, 0x23, 0x61,
	0x4a, 0xd8, 0xab, 0xff, 0xfb, 0x4d, 0x50, 0xde, 0x81, 0x95, 0x91, 0x0e, 0x81, 0xb7, 0x4d, 0xe1,
	0x6e, 0x32, 0x93, 0xb3, 0xd7, 0x1e, 0xb7, 0xff, 0x3d, 0xb5, 0xd1, 0xfc, 0x5c, 0x53, 0x5f, 0xee,
	0x1f, 0xd5, 0x1a, 0x87, 0xaa, 0x76, 0xb6, 0xa7, 0xd6, 0x1a, 0xf9, 0x39, 0x2c, 0xc1, 0xda, 0x28,
	0xd0, 0x7c, 0xae, 0x12, 0x4d, 0x7d, 0x79, 0x9a, 0x47, 0xf8, 0x01, 0x14, 0x46, 0xb1, 0x5a, 0x63,
	0xff, 0xe8, 0x24, 0x46, 0x53, 0xdb, 0x9f, 0x41, 0xf6, 0xc4, 0x63, 0xbe, 0x28, 0xbf, 0x06, 0x58,
	0x64, 0xf6, 0x3e, 0x8e, 0xd3, 0xf3, 0x73, 0x78, 0x13, 0xd6, 0xe3, 0x0a, 0x84, 0x9c, 0x90, 0xa1,
	0x3a, 0x07, 0x07, 0x79, 0xb4, 0x7d, 0xcc, 0x39, 0xee, 0xf7, 0x5f, 0x2d, 0xbc, 0x01, 0xf7, 0x45,
	0xf2, 0xb3, 0x7a, 0xed, 0x50, 0x6b, 0x9c, 0x0c, 0xd7, 0x29, 0x82, 0x34, 0x80, 0x6a, 0x75, 0xa2,
	0xd6, 0x0e, 0xbe, 0x1c, 0xe0, 0x68, 0xf7, 0x3a, 0x03, 0xe3, 0x6f, 0x36, 0xfe, 0x11, 0xc1, 0xfa,
	0x0c, 0x87, 0xe2, 0xc7, 0x13, 0x2e, 0x9a, 0x6d, 0x75, 0xe9, 0xc9, 0xed, 0x93, 0x03, 0x4f, 0xd9,
	0xf8, 0xfa, 0xea, 0x26, 0x8d, 0xbe, 0xbf, 0xba, 0x49, 0xa7, 0xa2, 0xf2, 0xcf, 0x57, 0x37, 0xe9,
	0x6c, 0x29, 0x92, 0x2b, 0x91, 0x69, 0x54, 0xf1, 0x1b, 0xc8, 0x8f, 0x5b, 0x0e, 0x3f, 0x9a, 0xe9,
	0xe7, 0x21, 0xb7, 0x4a, 0xef, 0xdf, 0x22, 0x2b, 0xe9, 0x9d, 0x9a, 0xda, 0xfb, 0x07, 0x04, 0x6b,
	0xd3, 0x0d, 0x83, 0xb7, 0x6f, 0x73, 0xbf, 0xd8, 0x77, 0xd2, 0xe3, 0x5b, 0xe7, 0x26, 0x74, 0xd2,
	0x53, 0xe9, 0xfc, 0x8e, 0x60, 0x69, 0x48, 0xb3, 0x78, 0xf2, 0x59, 0x1b, 0xf5, 0x8c, 0x24, 0xbf,
	0x3d, 0x21, 0xf0, 0x94, 0x2f, 0x78, 0xb7, 0x0c, 0xef, 0x06, 0x51, 0x39, 0x2c, 0xb3, 0x32, 0x2d,
	0xb7, 0x44, 0xd7, 0x6a, 0xd2, 0x55, 0x2e, 0x85, 0x72, 0x45, 0x3c, 0x5e, 0x55, 0xb9, 0xc4, 0xe4,
	0x4a, 0x62, 0xb7, 0xaa, 0x5c, 0xa2, 0x95, 0xd8, 0x4e, 0x55, 0xb9, 0xd4, 0xaa, 0x8c, 0xfc, 0x60,
	0xab, 0xd2, 0xfc, 0x77, 0x57, 0x37, 0xe9, 0x3f, 0xde, 0xec, 0xe5, 0xff, 0xba, 0x2e, 0xa2, 0xbf,
	0xaf, 0x8b, 0xe8, 0x9f, 0xeb, 0x22, 0xfa, 0xe9, 0xdf, 0xe2, 0xdc, 0xf9, 0xbc, 0xf8, 0xef, 0x7f,
	0xfc, 0x5f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x3b, 0x1a, 0x10, 0x61, 0x63, 0x08, 0x00, 0x00,
}
