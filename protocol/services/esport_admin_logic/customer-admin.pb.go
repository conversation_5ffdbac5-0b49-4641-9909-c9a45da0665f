// Code generated by protoc-gen-go. DO NOT EDIT.
// source: esport-admin-logic/customer-admin.proto

package esport_admin_logic // import "golang.52tt.com/protocol/services/esport_admin_logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type CustomerAccountStatus int32

const (
	CustomerAccountStatus_CUSTOMER_ACCOUNT_STATUS_UNSPECIFIED CustomerAccountStatus = 0
	CustomerAccountStatus_CUSTOMER_ACCOUNT_STATUS_NORMAL      CustomerAccountStatus = 1
	CustomerAccountStatus_CUSTOMER_ACCOUNT_STATUS_BANNED      CustomerAccountStatus = 2
	CustomerAccountStatus_CUSTOMER_ACCOUNT_STATUS_UNREG       CustomerAccountStatus = 3
)

var CustomerAccountStatus_name = map[int32]string{
	0: "CUSTOMER_ACCOUNT_STATUS_UNSPECIFIED",
	1: "CUSTOMER_ACCOUNT_STATUS_NORMAL",
	2: "CUSTOMER_ACCOUNT_STATUS_BANNED",
	3: "CUSTOMER_ACCOUNT_STATUS_UNREG",
}
var CustomerAccountStatus_value = map[string]int32{
	"CUSTOMER_ACCOUNT_STATUS_UNSPECIFIED": 0,
	"CUSTOMER_ACCOUNT_STATUS_NORMAL":      1,
	"CUSTOMER_ACCOUNT_STATUS_BANNED":      2,
	"CUSTOMER_ACCOUNT_STATUS_UNREG":       3,
}

func (x CustomerAccountStatus) String() string {
	return proto.EnumName(CustomerAccountStatus_name, int32(x))
}
func (CustomerAccountStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_customer_admin_bc041484aca9ae83, []int{0}
}

type ManagedGodStatus int32

const (
	ManagedGodStatus_MANAGED_GOD_STATUS_UNSPECIFIED ManagedGodStatus = 0
	ManagedGodStatus_MANAGED_GOD_STATUS_MANAGED     ManagedGodStatus = 1
	ManagedGodStatus_MANAGED_GOD_STATUS_UNMANAGED   ManagedGodStatus = 2
)

var ManagedGodStatus_name = map[int32]string{
	0: "MANAGED_GOD_STATUS_UNSPECIFIED",
	1: "MANAGED_GOD_STATUS_MANAGED",
	2: "MANAGED_GOD_STATUS_UNMANAGED",
}
var ManagedGodStatus_value = map[string]int32{
	"MANAGED_GOD_STATUS_UNSPECIFIED": 0,
	"MANAGED_GOD_STATUS_MANAGED":     1,
	"MANAGED_GOD_STATUS_UNMANAGED":   2,
}

func (x ManagedGodStatus) String() string {
	return proto.EnumName(ManagedGodStatus_name, int32(x))
}
func (ManagedGodStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_customer_admin_bc041484aca9ae83, []int{1}
}

type GetCustomerAccountsRequest struct {
	Page                 int32    `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size                 int32    `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	GuildId              uint32   `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	CustomerUid          []uint32 `protobuf:"varint,4,rep,packed,name=customer_uid,json=customerUid,proto3" json:"customer_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCustomerAccountsRequest) Reset()         { *m = GetCustomerAccountsRequest{} }
func (m *GetCustomerAccountsRequest) String() string { return proto.CompactTextString(m) }
func (*GetCustomerAccountsRequest) ProtoMessage()    {}
func (*GetCustomerAccountsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_customer_admin_bc041484aca9ae83, []int{0}
}
func (m *GetCustomerAccountsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCustomerAccountsRequest.Unmarshal(m, b)
}
func (m *GetCustomerAccountsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCustomerAccountsRequest.Marshal(b, m, deterministic)
}
func (dst *GetCustomerAccountsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCustomerAccountsRequest.Merge(dst, src)
}
func (m *GetCustomerAccountsRequest) XXX_Size() int {
	return xxx_messageInfo_GetCustomerAccountsRequest.Size(m)
}
func (m *GetCustomerAccountsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCustomerAccountsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCustomerAccountsRequest proto.InternalMessageInfo

func (m *GetCustomerAccountsRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetCustomerAccountsRequest) GetSize() int32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetCustomerAccountsRequest) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetCustomerAccountsRequest) GetCustomerUid() []uint32 {
	if m != nil {
		return m.CustomerUid
	}
	return nil
}

type CustomerAccount struct {
	CustomerTtid         string                `protobuf:"bytes,1,opt,name=customer_ttid,json=customerTtid,proto3" json:"customer_ttid,omitempty"`
	CustomerUid          uint32                `protobuf:"varint,2,opt,name=customer_uid,json=customerUid,proto3" json:"customer_uid,omitempty"`
	CustomerNickname     string                `protobuf:"bytes,3,opt,name=customer_nickname,json=customerNickname,proto3" json:"customer_nickname,omitempty"`
	GuildId              uint32                `protobuf:"varint,4,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ShortGuildId         uint32                `protobuf:"varint,5,opt,name=short_guild_id,json=shortGuildId,proto3" json:"short_guild_id,omitempty"`
	LongGuildId          uint32                `protobuf:"varint,6,opt,name=long_guild_id,json=longGuildId,proto3" json:"long_guild_id,omitempty"`
	GuildName            string                `protobuf:"bytes,7,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	GuildAdminTtid       string                `protobuf:"bytes,8,opt,name=guild_admin_ttid,json=guildAdminTtid,proto3" json:"guild_admin_ttid,omitempty"`
	Creator              string                `protobuf:"bytes,9,opt,name=creator,proto3" json:"creator,omitempty"`
	CreateTime           int64                 `protobuf:"varint,10,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Password             string                `protobuf:"bytes,11,opt,name=password,proto3" json:"password,omitempty"`
	Status               CustomerAccountStatus `protobuf:"varint,12,opt,name=status,proto3,enum=esport_admin_logic.CustomerAccountStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *CustomerAccount) Reset()         { *m = CustomerAccount{} }
func (m *CustomerAccount) String() string { return proto.CompactTextString(m) }
func (*CustomerAccount) ProtoMessage()    {}
func (*CustomerAccount) Descriptor() ([]byte, []int) {
	return fileDescriptor_customer_admin_bc041484aca9ae83, []int{1}
}
func (m *CustomerAccount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomerAccount.Unmarshal(m, b)
}
func (m *CustomerAccount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomerAccount.Marshal(b, m, deterministic)
}
func (dst *CustomerAccount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomerAccount.Merge(dst, src)
}
func (m *CustomerAccount) XXX_Size() int {
	return xxx_messageInfo_CustomerAccount.Size(m)
}
func (m *CustomerAccount) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomerAccount.DiscardUnknown(m)
}

var xxx_messageInfo_CustomerAccount proto.InternalMessageInfo

func (m *CustomerAccount) GetCustomerTtid() string {
	if m != nil {
		return m.CustomerTtid
	}
	return ""
}

func (m *CustomerAccount) GetCustomerUid() uint32 {
	if m != nil {
		return m.CustomerUid
	}
	return 0
}

func (m *CustomerAccount) GetCustomerNickname() string {
	if m != nil {
		return m.CustomerNickname
	}
	return ""
}

func (m *CustomerAccount) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CustomerAccount) GetShortGuildId() uint32 {
	if m != nil {
		return m.ShortGuildId
	}
	return 0
}

func (m *CustomerAccount) GetLongGuildId() uint32 {
	if m != nil {
		return m.LongGuildId
	}
	return 0
}

func (m *CustomerAccount) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *CustomerAccount) GetGuildAdminTtid() string {
	if m != nil {
		return m.GuildAdminTtid
	}
	return ""
}

func (m *CustomerAccount) GetCreator() string {
	if m != nil {
		return m.Creator
	}
	return ""
}

func (m *CustomerAccount) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *CustomerAccount) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

func (m *CustomerAccount) GetStatus() CustomerAccountStatus {
	if m != nil {
		return m.Status
	}
	return CustomerAccountStatus_CUSTOMER_ACCOUNT_STATUS_UNSPECIFIED
}

type GetCustomerAccountsResponse struct {
	CustomerAccounts     []*CustomerAccount `protobuf:"bytes,1,rep,name=customer_accounts,json=customerAccounts,proto3" json:"customer_accounts,omitempty"`
	Total                int32              `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetCustomerAccountsResponse) Reset()         { *m = GetCustomerAccountsResponse{} }
func (m *GetCustomerAccountsResponse) String() string { return proto.CompactTextString(m) }
func (*GetCustomerAccountsResponse) ProtoMessage()    {}
func (*GetCustomerAccountsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_customer_admin_bc041484aca9ae83, []int{2}
}
func (m *GetCustomerAccountsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCustomerAccountsResponse.Unmarshal(m, b)
}
func (m *GetCustomerAccountsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCustomerAccountsResponse.Marshal(b, m, deterministic)
}
func (dst *GetCustomerAccountsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCustomerAccountsResponse.Merge(dst, src)
}
func (m *GetCustomerAccountsResponse) XXX_Size() int {
	return xxx_messageInfo_GetCustomerAccountsResponse.Size(m)
}
func (m *GetCustomerAccountsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCustomerAccountsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCustomerAccountsResponse proto.InternalMessageInfo

func (m *GetCustomerAccountsResponse) GetCustomerAccounts() []*CustomerAccount {
	if m != nil {
		return m.CustomerAccounts
	}
	return nil
}

func (m *GetCustomerAccountsResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type CreateCustomerAccountRequest struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Password             string   `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	Creator              string   `protobuf:"bytes,3,opt,name=creator,proto3" json:"creator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateCustomerAccountRequest) Reset()         { *m = CreateCustomerAccountRequest{} }
func (m *CreateCustomerAccountRequest) String() string { return proto.CompactTextString(m) }
func (*CreateCustomerAccountRequest) ProtoMessage()    {}
func (*CreateCustomerAccountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_customer_admin_bc041484aca9ae83, []int{3}
}
func (m *CreateCustomerAccountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateCustomerAccountRequest.Unmarshal(m, b)
}
func (m *CreateCustomerAccountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateCustomerAccountRequest.Marshal(b, m, deterministic)
}
func (dst *CreateCustomerAccountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateCustomerAccountRequest.Merge(dst, src)
}
func (m *CreateCustomerAccountRequest) XXX_Size() int {
	return xxx_messageInfo_CreateCustomerAccountRequest.Size(m)
}
func (m *CreateCustomerAccountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateCustomerAccountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateCustomerAccountRequest proto.InternalMessageInfo

func (m *CreateCustomerAccountRequest) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CreateCustomerAccountRequest) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

func (m *CreateCustomerAccountRequest) GetCreator() string {
	if m != nil {
		return m.Creator
	}
	return ""
}

type CreateCustomerAccountResponse struct {
	CustomerAccount      *CustomerAccount `protobuf:"bytes,1,opt,name=customer_account,json=customerAccount,proto3" json:"customer_account,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *CreateCustomerAccountResponse) Reset()         { *m = CreateCustomerAccountResponse{} }
func (m *CreateCustomerAccountResponse) String() string { return proto.CompactTextString(m) }
func (*CreateCustomerAccountResponse) ProtoMessage()    {}
func (*CreateCustomerAccountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_customer_admin_bc041484aca9ae83, []int{4}
}
func (m *CreateCustomerAccountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateCustomerAccountResponse.Unmarshal(m, b)
}
func (m *CreateCustomerAccountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateCustomerAccountResponse.Marshal(b, m, deterministic)
}
func (dst *CreateCustomerAccountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateCustomerAccountResponse.Merge(dst, src)
}
func (m *CreateCustomerAccountResponse) XXX_Size() int {
	return xxx_messageInfo_CreateCustomerAccountResponse.Size(m)
}
func (m *CreateCustomerAccountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateCustomerAccountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateCustomerAccountResponse proto.InternalMessageInfo

func (m *CreateCustomerAccountResponse) GetCustomerAccount() *CustomerAccount {
	if m != nil {
		return m.CustomerAccount
	}
	return nil
}

type GetManagedGodsRequest struct {
	Page                 int32    `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size                 int32    `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	GuildId              uint32   `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	CoachUid             uint32   `protobuf:"varint,4,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetManagedGodsRequest) Reset()         { *m = GetManagedGodsRequest{} }
func (m *GetManagedGodsRequest) String() string { return proto.CompactTextString(m) }
func (*GetManagedGodsRequest) ProtoMessage()    {}
func (*GetManagedGodsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_customer_admin_bc041484aca9ae83, []int{5}
}
func (m *GetManagedGodsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetManagedGodsRequest.Unmarshal(m, b)
}
func (m *GetManagedGodsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetManagedGodsRequest.Marshal(b, m, deterministic)
}
func (dst *GetManagedGodsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetManagedGodsRequest.Merge(dst, src)
}
func (m *GetManagedGodsRequest) XXX_Size() int {
	return xxx_messageInfo_GetManagedGodsRequest.Size(m)
}
func (m *GetManagedGodsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetManagedGodsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetManagedGodsRequest proto.InternalMessageInfo

func (m *GetManagedGodsRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetManagedGodsRequest) GetSize() int32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetManagedGodsRequest) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetManagedGodsRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

type ManagedGod struct {
	Ttid                 string           `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Uid                  uint32           `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string           `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Status               ManagedGodStatus `protobuf:"varint,4,opt,name=status,proto3,enum=esport_admin_logic.ManagedGodStatus" json:"status,omitempty"`
	CustomerUid          uint32           `protobuf:"varint,5,opt,name=customer_uid,json=customerUid,proto3" json:"customer_uid,omitempty"`
	CustomerNickname     string           `protobuf:"bytes,6,opt,name=customer_nickname,json=customerNickname,proto3" json:"customer_nickname,omitempty"`
	CustomerTtid         string           `protobuf:"bytes,7,opt,name=customer_ttid,json=customerTtid,proto3" json:"customer_ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ManagedGod) Reset()         { *m = ManagedGod{} }
func (m *ManagedGod) String() string { return proto.CompactTextString(m) }
func (*ManagedGod) ProtoMessage()    {}
func (*ManagedGod) Descriptor() ([]byte, []int) {
	return fileDescriptor_customer_admin_bc041484aca9ae83, []int{6}
}
func (m *ManagedGod) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManagedGod.Unmarshal(m, b)
}
func (m *ManagedGod) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManagedGod.Marshal(b, m, deterministic)
}
func (dst *ManagedGod) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManagedGod.Merge(dst, src)
}
func (m *ManagedGod) XXX_Size() int {
	return xxx_messageInfo_ManagedGod.Size(m)
}
func (m *ManagedGod) XXX_DiscardUnknown() {
	xxx_messageInfo_ManagedGod.DiscardUnknown(m)
}

var xxx_messageInfo_ManagedGod proto.InternalMessageInfo

func (m *ManagedGod) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *ManagedGod) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ManagedGod) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ManagedGod) GetStatus() ManagedGodStatus {
	if m != nil {
		return m.Status
	}
	return ManagedGodStatus_MANAGED_GOD_STATUS_UNSPECIFIED
}

func (m *ManagedGod) GetCustomerUid() uint32 {
	if m != nil {
		return m.CustomerUid
	}
	return 0
}

func (m *ManagedGod) GetCustomerNickname() string {
	if m != nil {
		return m.CustomerNickname
	}
	return ""
}

func (m *ManagedGod) GetCustomerTtid() string {
	if m != nil {
		return m.CustomerTtid
	}
	return ""
}

type GetManagedGodsResponse struct {
	ManagedGods          []*ManagedGod `protobuf:"bytes,1,rep,name=managed_gods,json=managedGods,proto3" json:"managed_gods,omitempty"`
	Total                int32         `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetManagedGodsResponse) Reset()         { *m = GetManagedGodsResponse{} }
func (m *GetManagedGodsResponse) String() string { return proto.CompactTextString(m) }
func (*GetManagedGodsResponse) ProtoMessage()    {}
func (*GetManagedGodsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_customer_admin_bc041484aca9ae83, []int{7}
}
func (m *GetManagedGodsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetManagedGodsResponse.Unmarshal(m, b)
}
func (m *GetManagedGodsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetManagedGodsResponse.Marshal(b, m, deterministic)
}
func (dst *GetManagedGodsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetManagedGodsResponse.Merge(dst, src)
}
func (m *GetManagedGodsResponse) XXX_Size() int {
	return xxx_messageInfo_GetManagedGodsResponse.Size(m)
}
func (m *GetManagedGodsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetManagedGodsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetManagedGodsResponse proto.InternalMessageInfo

func (m *GetManagedGodsResponse) GetManagedGods() []*ManagedGod {
	if m != nil {
		return m.ManagedGods
	}
	return nil
}

func (m *GetManagedGodsResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type AddManagedGodRequest struct {
	CustomerUid          uint32   `protobuf:"varint,1,opt,name=customer_uid,json=customerUid,proto3" json:"customer_uid,omitempty"`
	CoachUid             []uint32 `protobuf:"varint,2,rep,packed,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddManagedGodRequest) Reset()         { *m = AddManagedGodRequest{} }
func (m *AddManagedGodRequest) String() string { return proto.CompactTextString(m) }
func (*AddManagedGodRequest) ProtoMessage()    {}
func (*AddManagedGodRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_customer_admin_bc041484aca9ae83, []int{8}
}
func (m *AddManagedGodRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddManagedGodRequest.Unmarshal(m, b)
}
func (m *AddManagedGodRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddManagedGodRequest.Marshal(b, m, deterministic)
}
func (dst *AddManagedGodRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddManagedGodRequest.Merge(dst, src)
}
func (m *AddManagedGodRequest) XXX_Size() int {
	return xxx_messageInfo_AddManagedGodRequest.Size(m)
}
func (m *AddManagedGodRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddManagedGodRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddManagedGodRequest proto.InternalMessageInfo

func (m *AddManagedGodRequest) GetCustomerUid() uint32 {
	if m != nil {
		return m.CustomerUid
	}
	return 0
}

func (m *AddManagedGodRequest) GetCoachUid() []uint32 {
	if m != nil {
		return m.CoachUid
	}
	return nil
}

type AddManagedGodResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddManagedGodResponse) Reset()         { *m = AddManagedGodResponse{} }
func (m *AddManagedGodResponse) String() string { return proto.CompactTextString(m) }
func (*AddManagedGodResponse) ProtoMessage()    {}
func (*AddManagedGodResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_customer_admin_bc041484aca9ae83, []int{9}
}
func (m *AddManagedGodResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddManagedGodResponse.Unmarshal(m, b)
}
func (m *AddManagedGodResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddManagedGodResponse.Marshal(b, m, deterministic)
}
func (dst *AddManagedGodResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddManagedGodResponse.Merge(dst, src)
}
func (m *AddManagedGodResponse) XXX_Size() int {
	return xxx_messageInfo_AddManagedGodResponse.Size(m)
}
func (m *AddManagedGodResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddManagedGodResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddManagedGodResponse proto.InternalMessageInfo

type RemoveManagedGodRequest struct {
	CustomerUid          uint32   `protobuf:"varint,1,opt,name=customer_uid,json=customerUid,proto3" json:"customer_uid,omitempty"`
	CoachUid             []uint32 `protobuf:"varint,2,rep,packed,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveManagedGodRequest) Reset()         { *m = RemoveManagedGodRequest{} }
func (m *RemoveManagedGodRequest) String() string { return proto.CompactTextString(m) }
func (*RemoveManagedGodRequest) ProtoMessage()    {}
func (*RemoveManagedGodRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_customer_admin_bc041484aca9ae83, []int{10}
}
func (m *RemoveManagedGodRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveManagedGodRequest.Unmarshal(m, b)
}
func (m *RemoveManagedGodRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveManagedGodRequest.Marshal(b, m, deterministic)
}
func (dst *RemoveManagedGodRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveManagedGodRequest.Merge(dst, src)
}
func (m *RemoveManagedGodRequest) XXX_Size() int {
	return xxx_messageInfo_RemoveManagedGodRequest.Size(m)
}
func (m *RemoveManagedGodRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveManagedGodRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveManagedGodRequest proto.InternalMessageInfo

func (m *RemoveManagedGodRequest) GetCustomerUid() uint32 {
	if m != nil {
		return m.CustomerUid
	}
	return 0
}

func (m *RemoveManagedGodRequest) GetCoachUid() []uint32 {
	if m != nil {
		return m.CoachUid
	}
	return nil
}

type RemoveManagedGodResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveManagedGodResponse) Reset()         { *m = RemoveManagedGodResponse{} }
func (m *RemoveManagedGodResponse) String() string { return proto.CompactTextString(m) }
func (*RemoveManagedGodResponse) ProtoMessage()    {}
func (*RemoveManagedGodResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_customer_admin_bc041484aca9ae83, []int{11}
}
func (m *RemoveManagedGodResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveManagedGodResponse.Unmarshal(m, b)
}
func (m *RemoveManagedGodResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveManagedGodResponse.Marshal(b, m, deterministic)
}
func (dst *RemoveManagedGodResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveManagedGodResponse.Merge(dst, src)
}
func (m *RemoveManagedGodResponse) XXX_Size() int {
	return xxx_messageInfo_RemoveManagedGodResponse.Size(m)
}
func (m *RemoveManagedGodResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveManagedGodResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveManagedGodResponse proto.InternalMessageInfo

func init() {
	proto.RegisterType((*GetCustomerAccountsRequest)(nil), "esport_admin_logic.GetCustomerAccountsRequest")
	proto.RegisterType((*CustomerAccount)(nil), "esport_admin_logic.CustomerAccount")
	proto.RegisterType((*GetCustomerAccountsResponse)(nil), "esport_admin_logic.GetCustomerAccountsResponse")
	proto.RegisterType((*CreateCustomerAccountRequest)(nil), "esport_admin_logic.CreateCustomerAccountRequest")
	proto.RegisterType((*CreateCustomerAccountResponse)(nil), "esport_admin_logic.CreateCustomerAccountResponse")
	proto.RegisterType((*GetManagedGodsRequest)(nil), "esport_admin_logic.GetManagedGodsRequest")
	proto.RegisterType((*ManagedGod)(nil), "esport_admin_logic.ManagedGod")
	proto.RegisterType((*GetManagedGodsResponse)(nil), "esport_admin_logic.GetManagedGodsResponse")
	proto.RegisterType((*AddManagedGodRequest)(nil), "esport_admin_logic.AddManagedGodRequest")
	proto.RegisterType((*AddManagedGodResponse)(nil), "esport_admin_logic.AddManagedGodResponse")
	proto.RegisterType((*RemoveManagedGodRequest)(nil), "esport_admin_logic.RemoveManagedGodRequest")
	proto.RegisterType((*RemoveManagedGodResponse)(nil), "esport_admin_logic.RemoveManagedGodResponse")
	proto.RegisterEnum("esport_admin_logic.CustomerAccountStatus", CustomerAccountStatus_name, CustomerAccountStatus_value)
	proto.RegisterEnum("esport_admin_logic.ManagedGodStatus", ManagedGodStatus_name, ManagedGodStatus_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// CustomerServiceAdminClient is the client API for CustomerServiceAdmin service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type CustomerServiceAdminClient interface {
	// 获取已创建的客服号信息
	GetCustomerAccounts(ctx context.Context, in *GetCustomerAccountsRequest, opts ...grpc.CallOption) (*GetCustomerAccountsResponse, error)
	// 创建客服账号
	CreateCustomerAccount(ctx context.Context, in *CreateCustomerAccountRequest, opts ...grpc.CallOption) (*CreateCustomerAccountResponse, error)
	// 获取托管大神配置
	GetManagedGods(ctx context.Context, in *GetManagedGodsRequest, opts ...grpc.CallOption) (*GetManagedGodsResponse, error)
	// 添加托管大神
	AddManagedGod(ctx context.Context, in *AddManagedGodRequest, opts ...grpc.CallOption) (*AddManagedGodResponse, error)
	// 移除托管大神
	RemoveManagedGod(ctx context.Context, in *RemoveManagedGodRequest, opts ...grpc.CallOption) (*RemoveManagedGodResponse, error)
}

type customerServiceAdminClient struct {
	cc *grpc.ClientConn
}

func NewCustomerServiceAdminClient(cc *grpc.ClientConn) CustomerServiceAdminClient {
	return &customerServiceAdminClient{cc}
}

func (c *customerServiceAdminClient) GetCustomerAccounts(ctx context.Context, in *GetCustomerAccountsRequest, opts ...grpc.CallOption) (*GetCustomerAccountsResponse, error) {
	out := new(GetCustomerAccountsResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.CustomerServiceAdmin/GetCustomerAccounts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceAdminClient) CreateCustomerAccount(ctx context.Context, in *CreateCustomerAccountRequest, opts ...grpc.CallOption) (*CreateCustomerAccountResponse, error) {
	out := new(CreateCustomerAccountResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.CustomerServiceAdmin/CreateCustomerAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceAdminClient) GetManagedGods(ctx context.Context, in *GetManagedGodsRequest, opts ...grpc.CallOption) (*GetManagedGodsResponse, error) {
	out := new(GetManagedGodsResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.CustomerServiceAdmin/GetManagedGods", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceAdminClient) AddManagedGod(ctx context.Context, in *AddManagedGodRequest, opts ...grpc.CallOption) (*AddManagedGodResponse, error) {
	out := new(AddManagedGodResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.CustomerServiceAdmin/AddManagedGod", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceAdminClient) RemoveManagedGod(ctx context.Context, in *RemoveManagedGodRequest, opts ...grpc.CallOption) (*RemoveManagedGodResponse, error) {
	out := new(RemoveManagedGodResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.CustomerServiceAdmin/RemoveManagedGod", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CustomerServiceAdminServer is the server API for CustomerServiceAdmin service.
type CustomerServiceAdminServer interface {
	// 获取已创建的客服号信息
	GetCustomerAccounts(context.Context, *GetCustomerAccountsRequest) (*GetCustomerAccountsResponse, error)
	// 创建客服账号
	CreateCustomerAccount(context.Context, *CreateCustomerAccountRequest) (*CreateCustomerAccountResponse, error)
	// 获取托管大神配置
	GetManagedGods(context.Context, *GetManagedGodsRequest) (*GetManagedGodsResponse, error)
	// 添加托管大神
	AddManagedGod(context.Context, *AddManagedGodRequest) (*AddManagedGodResponse, error)
	// 移除托管大神
	RemoveManagedGod(context.Context, *RemoveManagedGodRequest) (*RemoveManagedGodResponse, error)
}

func RegisterCustomerServiceAdminServer(s *grpc.Server, srv CustomerServiceAdminServer) {
	s.RegisterService(&_CustomerServiceAdmin_serviceDesc, srv)
}

func _CustomerServiceAdmin_GetCustomerAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceAdminServer).GetCustomerAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.CustomerServiceAdmin/GetCustomerAccounts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceAdminServer).GetCustomerAccounts(ctx, req.(*GetCustomerAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerServiceAdmin_CreateCustomerAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCustomerAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceAdminServer).CreateCustomerAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.CustomerServiceAdmin/CreateCustomerAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceAdminServer).CreateCustomerAccount(ctx, req.(*CreateCustomerAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerServiceAdmin_GetManagedGods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetManagedGodsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceAdminServer).GetManagedGods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.CustomerServiceAdmin/GetManagedGods",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceAdminServer).GetManagedGods(ctx, req.(*GetManagedGodsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerServiceAdmin_AddManagedGod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddManagedGodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceAdminServer).AddManagedGod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.CustomerServiceAdmin/AddManagedGod",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceAdminServer).AddManagedGod(ctx, req.(*AddManagedGodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerServiceAdmin_RemoveManagedGod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveManagedGodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceAdminServer).RemoveManagedGod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.CustomerServiceAdmin/RemoveManagedGod",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceAdminServer).RemoveManagedGod(ctx, req.(*RemoveManagedGodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _CustomerServiceAdmin_serviceDesc = grpc.ServiceDesc{
	ServiceName: "esport_admin_logic.CustomerServiceAdmin",
	HandlerType: (*CustomerServiceAdminServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCustomerAccounts",
			Handler:    _CustomerServiceAdmin_GetCustomerAccounts_Handler,
		},
		{
			MethodName: "CreateCustomerAccount",
			Handler:    _CustomerServiceAdmin_CreateCustomerAccount_Handler,
		},
		{
			MethodName: "GetManagedGods",
			Handler:    _CustomerServiceAdmin_GetManagedGods_Handler,
		},
		{
			MethodName: "AddManagedGod",
			Handler:    _CustomerServiceAdmin_AddManagedGod_Handler,
		},
		{
			MethodName: "RemoveManagedGod",
			Handler:    _CustomerServiceAdmin_RemoveManagedGod_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "esport-admin-logic/customer-admin.proto",
}

func init() {
	proto.RegisterFile("esport-admin-logic/customer-admin.proto", fileDescriptor_customer_admin_bc041484aca9ae83)
}

var fileDescriptor_customer_admin_bc041484aca9ae83 = []byte{
	// 887 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x56, 0xdb, 0x8e, 0xdb, 0x44,
	0x18, 0xc6, 0x71, 0xf6, 0x90, 0x3f, 0x9b, 0xad, 0x19, 0x76, 0xa9, 0x71, 0xbb, 0x4b, 0xea, 0xad,
	0x54, 0xef, 0x96, 0x26, 0x10, 0x0e, 0x57, 0xdc, 0xb8, 0x49, 0x88, 0x56, 0x22, 0x4e, 0xe5, 0x24,
	0x48, 0x70, 0x63, 0x19, 0x7b, 0xe4, 0x5a, 0xc4, 0x9e, 0x34, 0x9e, 0x6c, 0x11, 0xe2, 0x06, 0x24,
	0x9e, 0x86, 0x37, 0xe1, 0x82, 0x67, 0x42, 0x1e, 0x8f, 0x93, 0xf8, 0xb4, 0x0a, 0xd2, 0xde, 0xcd,
	0x7c, 0xf3, 0xfd, 0xe7, 0x83, 0x0d, 0x2f, 0x70, 0xb4, 0x24, 0x2b, 0xfa, 0xca, 0x76, 0x03, 0x3f,
	0x7c, 0xb5, 0x20, 0x9e, 0xef, 0x74, 0x9d, 0x75, 0x44, 0x49, 0x80, 0x57, 0x09, 0xd8, 0x59, 0xae,
	0x08, 0x25, 0x08, 0x25, 0x44, 0x8b, 0x61, 0x16, 0x23, 0xaa, 0x7f, 0x0a, 0xa0, 0x8c, 0x30, 0xed,
	0x73, 0xbe, 0xee, 0x38, 0x64, 0x1d, 0xd2, 0xc8, 0xc4, 0xef, 0xd6, 0x38, 0xa2, 0x08, 0x41, 0x7d,
	0x69, 0x7b, 0x58, 0x16, 0xda, 0x82, 0x76, 0x60, 0xb2, 0x73, 0x8c, 0x45, 0xfe, 0x6f, 0x58, 0xae,
	0x25, 0x58, 0x7c, 0x46, 0x9f, 0xc0, 0xb1, 0xb7, 0xf6, 0x17, 0xae, 0xe5, 0xbb, 0xb2, 0xd8, 0x16,
	0xb4, 0x96, 0x79, 0xc4, 0xee, 0xb7, 0x2e, 0x7a, 0x06, 0x27, 0xa9, 0x37, 0xd6, 0xda, 0x77, 0xe5,
	0x7a, 0x5b, 0xd4, 0x5a, 0x66, 0x33, 0xc5, 0xe6, 0xbe, 0xab, 0xfe, 0x2b, 0xc2, 0xa3, 0x9c, 0x07,
	0xe8, 0x0a, 0x5a, 0x1b, 0x31, 0x4a, 0x7d, 0x97, 0xb9, 0xd0, 0x30, 0x37, 0xba, 0x66, 0xd4, 0x2f,
	0xea, 0xae, 0x31, 0xd3, 0xbb, 0xba, 0xd1, 0x4b, 0xf8, 0x70, 0x43, 0x09, 0x7d, 0xe7, 0x97, 0xd0,
	0x0e, 0x30, 0x73, 0xb1, 0x61, 0x4a, 0xe9, 0x83, 0xc1, 0xf1, 0x4c, 0x18, 0xf5, 0x6c, 0x18, 0xcf,
	0xe1, 0x34, 0x7a, 0x1b, 0x67, 0x6f, 0x43, 0x38, 0x60, 0x84, 0x13, 0x86, 0x8e, 0x38, 0x4b, 0x85,
	0xd6, 0x82, 0x84, 0xde, 0x96, 0x74, 0x98, 0x78, 0x14, 0x83, 0x29, 0xe7, 0x02, 0x20, 0x79, 0x66,
	0xae, 0x1c, 0x31, 0x57, 0x1a, 0x0c, 0x31, 0x62, 0x1f, 0x34, 0x90, 0x92, 0xe7, 0xa4, 0x4c, 0x2c,
	0xf6, 0x63, 0x46, 0x3a, 0x65, 0xb8, 0x1e, 0xc3, 0x2c, 0x7a, 0x19, 0x8e, 0x9c, 0x15, 0xb6, 0x29,
	0x59, 0xc9, 0x0d, 0x46, 0x48, 0xaf, 0xe8, 0x53, 0x68, 0xb2, 0x23, 0xb6, 0xa8, 0x1f, 0x60, 0x19,
	0xda, 0x82, 0x26, 0x9a, 0x90, 0x40, 0x33, 0x3f, 0xc0, 0x48, 0x81, 0xe3, 0xa5, 0x1d, 0x45, 0xef,
	0xc9, 0xca, 0x95, 0x9b, 0x4c, 0x76, 0x73, 0x47, 0x3a, 0x1c, 0x46, 0xd4, 0xa6, 0xeb, 0x48, 0x3e,
	0x69, 0x0b, 0xda, 0x69, 0xef, 0xba, 0x53, 0xec, 0x9b, 0x4e, 0xae, 0x5c, 0x53, 0x26, 0x60, 0x72,
	0x41, 0xf5, 0x2f, 0x01, 0x9e, 0x94, 0x76, 0x55, 0xb4, 0x24, 0x61, 0x84, 0xd1, 0x9b, 0x9d, 0xa2,
	0xd8, 0xfc, 0x51, 0x16, 0xda, 0xa2, 0xd6, 0xec, 0x5d, 0xed, 0x61, 0x6d, 0x5b, 0xb9, 0x54, 0x33,
	0x3a, 0x83, 0x03, 0x4a, 0xa8, 0xbd, 0xe0, 0x5d, 0x99, 0x5c, 0x54, 0x02, 0x4f, 0xfb, 0x2c, 0xe8,
	0xbc, 0x02, 0xde, 0xde, 0xbb, 0xf5, 0x16, 0xb2, 0xf5, 0xde, 0xcd, 0x50, 0x2d, 0x97, 0xa1, 0x9d,
	0xc4, 0x8b, 0x99, 0xc4, 0xab, 0x04, 0x2e, 0x2a, 0x0c, 0xf2, 0xc8, 0x0d, 0x90, 0xf2, 0x91, 0x33,
	0xcb, 0x7b, 0x06, 0xfe, 0x28, 0x17, 0xb8, 0xfa, 0x1e, 0xce, 0x47, 0x98, 0x8e, 0xed, 0xd0, 0xf6,
	0xb0, 0x3b, 0x22, 0xee, 0x43, 0x4e, 0xee, 0x13, 0x68, 0x38, 0xc4, 0x76, 0xde, 0xf2, 0xb1, 0x8d,
	0xdf, 0x8e, 0x19, 0x10, 0xcf, 0xec, 0x1f, 0x35, 0x80, 0xad, 0xd9, 0x58, 0xf5, 0xce, 0x94, 0xb2,
	0x33, 0x92, 0x40, 0xdc, 0x0e, 0x65, 0x7c, 0x8c, 0x93, 0x9a, 0x9b, 0xc1, 0xcd, 0x1d, 0x7d, 0xbb,
	0x69, 0xbb, 0x3a, 0x6b, 0xbb, 0xe7, 0x65, 0xf9, 0xd8, 0x5a, 0xcc, 0x76, 0x5c, 0x61, 0x13, 0x1c,
	0xec, 0xb9, 0x09, 0x0e, 0x2b, 0x36, 0x41, 0x61, 0xfd, 0x1c, 0x15, 0xd7, 0x8f, 0xfa, 0x0e, 0x3e,
	0xce, 0x27, 0x9f, 0x97, 0x59, 0x87, 0x93, 0x20, 0x81, 0x2d, 0x8f, 0xb8, 0x69, 0x6f, 0x5f, 0xde,
	0x1f, 0x92, 0xd9, 0x0c, 0xb6, 0xaa, 0x2a, 0x3a, 0xfa, 0x07, 0x38, 0xd3, 0x5d, 0x77, 0x47, 0x86,
	0x97, 0x3b, 0x1f, 0xbf, 0x50, 0x8c, 0x3f, 0x53, 0xce, 0x1a, 0xdb, 0xc2, 0xdb, 0x72, 0x3e, 0x86,
	0xf3, 0x9c, 0xde, 0x24, 0x12, 0xf5, 0x47, 0x78, 0x6c, 0xe2, 0x80, 0xdc, 0xe1, 0x87, 0xb7, 0xa9,
	0x80, 0x5c, 0x54, 0x9d, 0x98, 0xbd, 0xf9, 0x5b, 0x80, 0xf3, 0xd2, 0x1d, 0x83, 0x5e, 0xc0, 0x55,
	0x7f, 0x3e, 0x9d, 0x4d, 0xc6, 0x43, 0xd3, 0xd2, 0xfb, 0xfd, 0xc9, 0xdc, 0x98, 0x59, 0xd3, 0x99,
	0x3e, 0x9b, 0x4f, 0xad, 0xb9, 0x31, 0x7d, 0x33, 0xec, 0xdf, 0x7e, 0x77, 0x3b, 0x1c, 0x48, 0x1f,
	0x20, 0x15, 0x2e, 0xab, 0x88, 0xc6, 0xc4, 0x1c, 0xeb, 0xdf, 0x4b, 0xc2, 0x7d, 0x9c, 0xd7, 0xba,
	0x61, 0x0c, 0x07, 0x52, 0x0d, 0x3d, 0x83, 0x8b, 0x6a, 0x83, 0xe6, 0x70, 0x24, 0x89, 0x37, 0xbf,
	0x82, 0x94, 0xef, 0xcc, 0x58, 0xf5, 0x58, 0x37, 0xf4, 0xd1, 0x70, 0x60, 0x8d, 0x26, 0x83, 0x72,
	0x17, 0x2f, 0x41, 0x29, 0xe1, 0x70, 0x48, 0x12, 0x50, 0x1b, 0x9e, 0x96, 0xea, 0x48, 0x19, 0xb5,
	0xde, 0x3f, 0x75, 0x38, 0x4b, 0xf3, 0x34, 0xc5, 0xab, 0x3b, 0xdf, 0xc1, 0xec, 0x03, 0x81, 0xee,
	0xe0, 0xa3, 0x92, 0x0d, 0x8c, 0x3a, 0x65, 0x2d, 0x58, 0xfd, 0x03, 0xa0, 0x74, 0xf7, 0xe6, 0xf3,
	0xce, 0xff, 0x1d, 0xce, 0x4b, 0x37, 0x20, 0xfa, 0xbc, 0x74, 0xbf, 0xdd, 0xb3, 0x9d, 0x95, 0x2f,
	0xfe, 0x87, 0x04, 0xb7, 0xee, 0xc1, 0x69, 0x76, 0x22, 0xd1, 0x75, 0x45, 0x00, 0xc5, 0x95, 0xa9,
	0xdc, 0xec, 0x43, 0xe5, 0x86, 0x5c, 0x68, 0x65, 0xe6, 0x05, 0x69, 0x65, 0xc2, 0x65, 0xa3, 0xaa,
	0x5c, 0xef, 0xc1, 0xe4, 0x56, 0x02, 0x90, 0xf2, 0x13, 0x82, 0x5e, 0x96, 0x89, 0x57, 0x8c, 0xa8,
	0xf2, 0xd9, 0x7e, 0xe4, 0xc4, 0xdc, 0xeb, 0x6f, 0x7e, 0xfa, 0xca, 0x23, 0x0b, 0x3b, 0xf4, 0x3a,
	0x5f, 0xf7, 0x28, 0xed, 0x38, 0x24, 0xe8, 0xb2, 0x3f, 0x47, 0x87, 0x2c, 0xba, 0x51, 0xd2, 0x5c,
	0x51, 0xb7, 0xa8, 0xf0, 0xe7, 0x43, 0xc6, 0xfa, 0xf2, 0xbf, 0x00, 0x00, 0x00, 0xff, 0xff, 0x70,
	0x11, 0x10, 0x08, 0x8a, 0x0a, 0x00, 0x00,
}
