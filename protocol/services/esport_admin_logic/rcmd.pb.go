// Code generated by protoc-gen-go. DO NOT EDIT.
// source: esport-admin-logic/rcmd.proto

package esport_admin_logic // import "golang.52tt.com/protocol/services/esport_admin_logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 策略状态
type StrategyStatus int32

const (
	StrategyStatus_STRATEGY_STATUS_UNSPECIFIED StrategyStatus = 0
	StrategyStatus_STRATEGY_STATUS_RUNNING     StrategyStatus = 1
	StrategyStatus_STRATEGY_STATUS_PAUSED      StrategyStatus = 2
	StrategyStatus_STRATEGY_STATUS_DRAFT       StrategyStatus = 3
)

var StrategyStatus_name = map[int32]string{
	0: "STRATEGY_STATUS_UNSPECIFIED",
	1: "STRATEGY_STATUS_RUNNING",
	2: "STRATEGY_STATUS_PAUSED",
	3: "STRATEGY_STATUS_DRAFT",
}
var StrategyStatus_value = map[string]int32{
	"STRATEGY_STATUS_UNSPECIFIED": 0,
	"STRATEGY_STATUS_RUNNING":     1,
	"STRATEGY_STATUS_PAUSED":      2,
	"STRATEGY_STATUS_DRAFT":       3,
}

func (x StrategyStatus) String() string {
	return proto.EnumName(StrategyStatus_name, int32(x))
}
func (StrategyStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{0}
}

type DelimitType int32

const (
	DelimitType_DELIMIT_TYPE_UNSPECIFIED DelimitType = 0
	DelimitType_DELIMIT_TYPE_AB_TEST     DelimitType = 1
	DelimitType_DELIMIT_TYPE_USER_GROUP  DelimitType = 2
)

var DelimitType_name = map[int32]string{
	0: "DELIMIT_TYPE_UNSPECIFIED",
	1: "DELIMIT_TYPE_AB_TEST",
	2: "DELIMIT_TYPE_USER_GROUP",
}
var DelimitType_value = map[string]int32{
	"DELIMIT_TYPE_UNSPECIFIED": 0,
	"DELIMIT_TYPE_AB_TEST":     1,
	"DELIMIT_TYPE_USER_GROUP":  2,
}

func (x DelimitType) String() string {
	return proto.EnumName(DelimitType_name, int32(x))
}
func (DelimitType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{1}
}

type RecallSource_SortParameterType int32

const (
	RecallSource_SORT_PARAMETER_TYPE_UNKNOWN  RecallSource_SortParameterType = 0
	RecallSource_SORT_PARAMETER_TYPE_CUSTOMER RecallSource_SortParameterType = 1
	RecallSource_SORT_PARAMETER_TYPE_RANDOM   RecallSource_SortParameterType = 2
)

var RecallSource_SortParameterType_name = map[int32]string{
	0: "SORT_PARAMETER_TYPE_UNKNOWN",
	1: "SORT_PARAMETER_TYPE_CUSTOMER",
	2: "SORT_PARAMETER_TYPE_RANDOM",
}
var RecallSource_SortParameterType_value = map[string]int32{
	"SORT_PARAMETER_TYPE_UNKNOWN":  0,
	"SORT_PARAMETER_TYPE_CUSTOMER": 1,
	"SORT_PARAMETER_TYPE_RANDOM":   2,
}

func (x RecallSource_SortParameterType) String() string {
	return proto.EnumName(RecallSource_SortParameterType_name, int32(x))
}
func (RecallSource_SortParameterType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{3, 0}
}

// 策略信息
type Strategy struct {
	Id                    uint32                     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SceneType             uint32                     `protobuf:"varint,2,opt,name=scene_type,json=sceneType,proto3" json:"scene_type,omitempty"`
	Name                  string                     `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Priority              uint32                     `protobuf:"varint,4,opt,name=priority,proto3" json:"priority,omitempty"`
	Status                StrategyStatus             `protobuf:"varint,5,opt,name=status,proto3,enum=esport_admin_logic.StrategyStatus" json:"status,omitempty"`
	LastOperator          string                     `protobuf:"bytes,6,opt,name=last_operator,json=lastOperator,proto3" json:"last_operator,omitempty"`
	LastOperationTime     int64                      `protobuf:"varint,7,opt,name=last_operation_time,json=lastOperationTime,proto3" json:"last_operation_time,omitempty"`
	DelimitType           DelimitType                `protobuf:"varint,8,opt,name=delimit_type,json=delimitType,proto3,enum=esport_admin_logic.DelimitType" json:"delimit_type,omitempty"`
	AbTestSetting         *Strategy_AbTestSetting    `protobuf:"bytes,9,opt,name=ab_test_setting,json=abTestSetting,proto3" json:"ab_test_setting,omitempty"`
	UserGroupSetting      *Strategy_UserGroupSetting `protobuf:"bytes,10,opt,name=user_group_setting,json=userGroupSetting,proto3" json:"user_group_setting,omitempty"`
	TestListIds           []int64                    `protobuf:"varint,20,rep,packed,name=test_list_ids,json=testListIds,proto3" json:"test_list_ids,omitempty"`
	RecallSources         []*Strategy_RecallSource   `protobuf:"bytes,21,rep,name=recall_sources,json=recallSources,proto3" json:"recall_sources,omitempty"`
	MinimumRecallQuantity int32                      `protobuf:"varint,22,opt,name=minimum_recall_quantity,json=minimumRecallQuantity,proto3" json:"minimum_recall_quantity,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                   `json:"-"`
	XXX_unrecognized      []byte                     `json:"-"`
	XXX_sizecache         int32                      `json:"-"`
}

func (m *Strategy) Reset()         { *m = Strategy{} }
func (m *Strategy) String() string { return proto.CompactTextString(m) }
func (*Strategy) ProtoMessage()    {}
func (*Strategy) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{0}
}
func (m *Strategy) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Strategy.Unmarshal(m, b)
}
func (m *Strategy) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Strategy.Marshal(b, m, deterministic)
}
func (dst *Strategy) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Strategy.Merge(dst, src)
}
func (m *Strategy) XXX_Size() int {
	return xxx_messageInfo_Strategy.Size(m)
}
func (m *Strategy) XXX_DiscardUnknown() {
	xxx_messageInfo_Strategy.DiscardUnknown(m)
}

var xxx_messageInfo_Strategy proto.InternalMessageInfo

func (m *Strategy) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Strategy) GetSceneType() uint32 {
	if m != nil {
		return m.SceneType
	}
	return 0
}

func (m *Strategy) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Strategy) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func (m *Strategy) GetStatus() StrategyStatus {
	if m != nil {
		return m.Status
	}
	return StrategyStatus_STRATEGY_STATUS_UNSPECIFIED
}

func (m *Strategy) GetLastOperator() string {
	if m != nil {
		return m.LastOperator
	}
	return ""
}

func (m *Strategy) GetLastOperationTime() int64 {
	if m != nil {
		return m.LastOperationTime
	}
	return 0
}

func (m *Strategy) GetDelimitType() DelimitType {
	if m != nil {
		return m.DelimitType
	}
	return DelimitType_DELIMIT_TYPE_UNSPECIFIED
}

func (m *Strategy) GetAbTestSetting() *Strategy_AbTestSetting {
	if m != nil {
		return m.AbTestSetting
	}
	return nil
}

func (m *Strategy) GetUserGroupSetting() *Strategy_UserGroupSetting {
	if m != nil {
		return m.UserGroupSetting
	}
	return nil
}

func (m *Strategy) GetTestListIds() []int64 {
	if m != nil {
		return m.TestListIds
	}
	return nil
}

func (m *Strategy) GetRecallSources() []*Strategy_RecallSource {
	if m != nil {
		return m.RecallSources
	}
	return nil
}

func (m *Strategy) GetMinimumRecallQuantity() int32 {
	if m != nil {
		return m.MinimumRecallQuantity
	}
	return 0
}

// 召回源信息
type Strategy_RecallSource struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TotalQuantity        int32    `protobuf:"varint,2,opt,name=total_quantity,json=totalQuantity,proto3" json:"total_quantity,omitempty"`
	MinimumQuantity      int32    `protobuf:"varint,3,opt,name=minimum_quantity,json=minimumQuantity,proto3" json:"minimum_quantity,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Strategy_RecallSource) Reset()         { *m = Strategy_RecallSource{} }
func (m *Strategy_RecallSource) String() string { return proto.CompactTextString(m) }
func (*Strategy_RecallSource) ProtoMessage()    {}
func (*Strategy_RecallSource) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{0, 0}
}
func (m *Strategy_RecallSource) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Strategy_RecallSource.Unmarshal(m, b)
}
func (m *Strategy_RecallSource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Strategy_RecallSource.Marshal(b, m, deterministic)
}
func (dst *Strategy_RecallSource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Strategy_RecallSource.Merge(dst, src)
}
func (m *Strategy_RecallSource) XXX_Size() int {
	return xxx_messageInfo_Strategy_RecallSource.Size(m)
}
func (m *Strategy_RecallSource) XXX_DiscardUnknown() {
	xxx_messageInfo_Strategy_RecallSource.DiscardUnknown(m)
}

var xxx_messageInfo_Strategy_RecallSource proto.InternalMessageInfo

func (m *Strategy_RecallSource) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Strategy_RecallSource) GetTotalQuantity() int32 {
	if m != nil {
		return m.TotalQuantity
	}
	return 0
}

func (m *Strategy_RecallSource) GetMinimumQuantity() int32 {
	if m != nil {
		return m.MinimumQuantity
	}
	return 0
}

type Strategy_AbTestSetting struct {
	ExperimentTag        string   `protobuf:"bytes,1,opt,name=experiment_tag,json=experimentTag,proto3" json:"experiment_tag,omitempty"`
	ExperimentArgs       []string `protobuf:"bytes,2,rep,name=experiment_args,json=experimentArgs,proto3" json:"experiment_args,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Strategy_AbTestSetting) Reset()         { *m = Strategy_AbTestSetting{} }
func (m *Strategy_AbTestSetting) String() string { return proto.CompactTextString(m) }
func (*Strategy_AbTestSetting) ProtoMessage()    {}
func (*Strategy_AbTestSetting) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{0, 1}
}
func (m *Strategy_AbTestSetting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Strategy_AbTestSetting.Unmarshal(m, b)
}
func (m *Strategy_AbTestSetting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Strategy_AbTestSetting.Marshal(b, m, deterministic)
}
func (dst *Strategy_AbTestSetting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Strategy_AbTestSetting.Merge(dst, src)
}
func (m *Strategy_AbTestSetting) XXX_Size() int {
	return xxx_messageInfo_Strategy_AbTestSetting.Size(m)
}
func (m *Strategy_AbTestSetting) XXX_DiscardUnknown() {
	xxx_messageInfo_Strategy_AbTestSetting.DiscardUnknown(m)
}

var xxx_messageInfo_Strategy_AbTestSetting proto.InternalMessageInfo

func (m *Strategy_AbTestSetting) GetExperimentTag() string {
	if m != nil {
		return m.ExperimentTag
	}
	return ""
}

func (m *Strategy_AbTestSetting) GetExperimentArgs() []string {
	if m != nil {
		return m.ExperimentArgs
	}
	return nil
}

type Strategy_UserGroupSetting struct {
	UserGroupIds         []int64  `protobuf:"varint,1,rep,packed,name=user_group_ids,json=userGroupIds,proto3" json:"user_group_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Strategy_UserGroupSetting) Reset()         { *m = Strategy_UserGroupSetting{} }
func (m *Strategy_UserGroupSetting) String() string { return proto.CompactTextString(m) }
func (*Strategy_UserGroupSetting) ProtoMessage()    {}
func (*Strategy_UserGroupSetting) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{0, 2}
}
func (m *Strategy_UserGroupSetting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Strategy_UserGroupSetting.Unmarshal(m, b)
}
func (m *Strategy_UserGroupSetting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Strategy_UserGroupSetting.Marshal(b, m, deterministic)
}
func (dst *Strategy_UserGroupSetting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Strategy_UserGroupSetting.Merge(dst, src)
}
func (m *Strategy_UserGroupSetting) XXX_Size() int {
	return xxx_messageInfo_Strategy_UserGroupSetting.Size(m)
}
func (m *Strategy_UserGroupSetting) XXX_DiscardUnknown() {
	xxx_messageInfo_Strategy_UserGroupSetting.DiscardUnknown(m)
}

var xxx_messageInfo_Strategy_UserGroupSetting proto.InternalMessageInfo

func (m *Strategy_UserGroupSetting) GetUserGroupIds() []int64 {
	if m != nil {
		return m.UserGroupIds
	}
	return nil
}

// 过滤规则信息（游离类型）
type FilterRule struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FilterRule) Reset()         { *m = FilterRule{} }
func (m *FilterRule) String() string { return proto.CompactTextString(m) }
func (*FilterRule) ProtoMessage()    {}
func (*FilterRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{1}
}
func (m *FilterRule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FilterRule.Unmarshal(m, b)
}
func (m *FilterRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FilterRule.Marshal(b, m, deterministic)
}
func (dst *FilterRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FilterRule.Merge(dst, src)
}
func (m *FilterRule) XXX_Size() int {
	return xxx_messageInfo_FilterRule.Size(m)
}
func (m *FilterRule) XXX_DiscardUnknown() {
	xxx_messageInfo_FilterRule.DiscardUnknown(m)
}

var xxx_messageInfo_FilterRule proto.InternalMessageInfo

func (m *FilterRule) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *FilterRule) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// 场景信息（游离类型）
type Scene struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Children             []*Scene `protobuf:"bytes,3,rep,name=children,proto3" json:"children,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Scene) Reset()         { *m = Scene{} }
func (m *Scene) String() string { return proto.CompactTextString(m) }
func (*Scene) ProtoMessage()    {}
func (*Scene) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{2}
}
func (m *Scene) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Scene.Unmarshal(m, b)
}
func (m *Scene) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Scene.Marshal(b, m, deterministic)
}
func (dst *Scene) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Scene.Merge(dst, src)
}
func (m *Scene) XXX_Size() int {
	return xxx_messageInfo_Scene.Size(m)
}
func (m *Scene) XXX_DiscardUnknown() {
	xxx_messageInfo_Scene.DiscardUnknown(m)
}

var xxx_messageInfo_Scene proto.InternalMessageInfo

func (m *Scene) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *Scene) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Scene) GetChildren() []*Scene {
	if m != nil {
		return m.Children
	}
	return nil
}

type RecallSource struct {
	Id                   uint32                          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	LastOperator         string                          `protobuf:"bytes,3,opt,name=last_operator,json=lastOperator,proto3" json:"last_operator,omitempty"`
	LastOperationTime    int64                           `protobuf:"varint,4,opt,name=last_operation_time,json=lastOperationTime,proto3" json:"last_operation_time,omitempty"`
	RuleSetting          *RecallSourceRuleSetting        `protobuf:"bytes,5,opt,name=rule_setting,json=ruleSetting,proto3" json:"rule_setting,omitempty"`
	SortParameterType    uint32                          `protobuf:"varint,6,opt,name=sort_parameter_type,json=sortParameterType,proto3" json:"sort_parameter_type,omitempty"`
	Customer             *SortParameterTable             `protobuf:"bytes,7,opt,name=customer,proto3" json:"customer,omitempty"`
	QueueRuleTypeList    []uint32                        `protobuf:"varint,10,rep,packed,name=queue_rule_type_list,json=queueRuleTypeList,proto3" json:"queue_rule_type_list,omitempty"`
	FilterRuleValues     []*RecallSource_FilterRuleValue `protobuf:"bytes,11,rep,name=filter_rule_values,json=filterRuleValues,proto3" json:"filter_rule_values,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *RecallSource) Reset()         { *m = RecallSource{} }
func (m *RecallSource) String() string { return proto.CompactTextString(m) }
func (*RecallSource) ProtoMessage()    {}
func (*RecallSource) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{3}
}
func (m *RecallSource) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallSource.Unmarshal(m, b)
}
func (m *RecallSource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallSource.Marshal(b, m, deterministic)
}
func (dst *RecallSource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallSource.Merge(dst, src)
}
func (m *RecallSource) XXX_Size() int {
	return xxx_messageInfo_RecallSource.Size(m)
}
func (m *RecallSource) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallSource.DiscardUnknown(m)
}

var xxx_messageInfo_RecallSource proto.InternalMessageInfo

func (m *RecallSource) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RecallSource) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RecallSource) GetLastOperator() string {
	if m != nil {
		return m.LastOperator
	}
	return ""
}

func (m *RecallSource) GetLastOperationTime() int64 {
	if m != nil {
		return m.LastOperationTime
	}
	return 0
}

func (m *RecallSource) GetRuleSetting() *RecallSourceRuleSetting {
	if m != nil {
		return m.RuleSetting
	}
	return nil
}

func (m *RecallSource) GetSortParameterType() uint32 {
	if m != nil {
		return m.SortParameterType
	}
	return 0
}

func (m *RecallSource) GetCustomer() *SortParameterTable {
	if m != nil {
		return m.Customer
	}
	return nil
}

func (m *RecallSource) GetQueueRuleTypeList() []uint32 {
	if m != nil {
		return m.QueueRuleTypeList
	}
	return nil
}

func (m *RecallSource) GetFilterRuleValues() []*RecallSource_FilterRuleValue {
	if m != nil {
		return m.FilterRuleValues
	}
	return nil
}

// 过滤规则信息
type RecallSource_FilterRuleValue struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Quantity             int32    `protobuf:"varint,2,opt,name=quantity,proto3" json:"quantity,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecallSource_FilterRuleValue) Reset()         { *m = RecallSource_FilterRuleValue{} }
func (m *RecallSource_FilterRuleValue) String() string { return proto.CompactTextString(m) }
func (*RecallSource_FilterRuleValue) ProtoMessage()    {}
func (*RecallSource_FilterRuleValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{3, 0}
}
func (m *RecallSource_FilterRuleValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallSource_FilterRuleValue.Unmarshal(m, b)
}
func (m *RecallSource_FilterRuleValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallSource_FilterRuleValue.Marshal(b, m, deterministic)
}
func (dst *RecallSource_FilterRuleValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallSource_FilterRuleValue.Merge(dst, src)
}
func (m *RecallSource_FilterRuleValue) XXX_Size() int {
	return xxx_messageInfo_RecallSource_FilterRuleValue.Size(m)
}
func (m *RecallSource_FilterRuleValue) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallSource_FilterRuleValue.DiscardUnknown(m)
}

var xxx_messageInfo_RecallSource_FilterRuleValue proto.InternalMessageInfo

func (m *RecallSource_FilterRuleValue) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RecallSource_FilterRuleValue) GetQuantity() int32 {
	if m != nil {
		return m.Quantity
	}
	return 0
}

type RecallSourceRuleSetting struct {
	RuleSet              []*RecallSourceRuleSetting_Set `protobuf:"bytes,1,rep,name=rule_set,json=ruleSet,proto3" json:"rule_set,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *RecallSourceRuleSetting) Reset()         { *m = RecallSourceRuleSetting{} }
func (m *RecallSourceRuleSetting) String() string { return proto.CompactTextString(m) }
func (*RecallSourceRuleSetting) ProtoMessage()    {}
func (*RecallSourceRuleSetting) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{4}
}
func (m *RecallSourceRuleSetting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallSourceRuleSetting.Unmarshal(m, b)
}
func (m *RecallSourceRuleSetting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallSourceRuleSetting.Marshal(b, m, deterministic)
}
func (dst *RecallSourceRuleSetting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallSourceRuleSetting.Merge(dst, src)
}
func (m *RecallSourceRuleSetting) XXX_Size() int {
	return xxx_messageInfo_RecallSourceRuleSetting.Size(m)
}
func (m *RecallSourceRuleSetting) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallSourceRuleSetting.DiscardUnknown(m)
}

var xxx_messageInfo_RecallSourceRuleSetting proto.InternalMessageInfo

func (m *RecallSourceRuleSetting) GetRuleSet() []*RecallSourceRuleSetting_Set {
	if m != nil {
		return m.RuleSet
	}
	return nil
}

type RecallSourceRuleSetting_Condition struct {
	RuleType             uint32   `protobuf:"varint,1,opt,name=rule_type,json=ruleType,proto3" json:"rule_type,omitempty"`
	Value                string   `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	OperatorType         uint32   `protobuf:"varint,3,opt,name=operator_type,json=operatorType,proto3" json:"operator_type,omitempty"`
	SelectedPath         []uint32 `protobuf:"varint,4,rep,packed,name=selected_path,json=selectedPath,proto3" json:"selected_path,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecallSourceRuleSetting_Condition) Reset()         { *m = RecallSourceRuleSetting_Condition{} }
func (m *RecallSourceRuleSetting_Condition) String() string { return proto.CompactTextString(m) }
func (*RecallSourceRuleSetting_Condition) ProtoMessage()    {}
func (*RecallSourceRuleSetting_Condition) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{4, 0}
}
func (m *RecallSourceRuleSetting_Condition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallSourceRuleSetting_Condition.Unmarshal(m, b)
}
func (m *RecallSourceRuleSetting_Condition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallSourceRuleSetting_Condition.Marshal(b, m, deterministic)
}
func (dst *RecallSourceRuleSetting_Condition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallSourceRuleSetting_Condition.Merge(dst, src)
}
func (m *RecallSourceRuleSetting_Condition) XXX_Size() int {
	return xxx_messageInfo_RecallSourceRuleSetting_Condition.Size(m)
}
func (m *RecallSourceRuleSetting_Condition) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallSourceRuleSetting_Condition.DiscardUnknown(m)
}

var xxx_messageInfo_RecallSourceRuleSetting_Condition proto.InternalMessageInfo

func (m *RecallSourceRuleSetting_Condition) GetRuleType() uint32 {
	if m != nil {
		return m.RuleType
	}
	return 0
}

func (m *RecallSourceRuleSetting_Condition) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

func (m *RecallSourceRuleSetting_Condition) GetOperatorType() uint32 {
	if m != nil {
		return m.OperatorType
	}
	return 0
}

func (m *RecallSourceRuleSetting_Condition) GetSelectedPath() []uint32 {
	if m != nil {
		return m.SelectedPath
	}
	return nil
}

type RecallSourceRuleSetting_Set struct {
	Conditions           []*RecallSourceRuleSetting_Condition `protobuf:"bytes,1,rep,name=conditions,proto3" json:"conditions,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *RecallSourceRuleSetting_Set) Reset()         { *m = RecallSourceRuleSetting_Set{} }
func (m *RecallSourceRuleSetting_Set) String() string { return proto.CompactTextString(m) }
func (*RecallSourceRuleSetting_Set) ProtoMessage()    {}
func (*RecallSourceRuleSetting_Set) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{4, 1}
}
func (m *RecallSourceRuleSetting_Set) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallSourceRuleSetting_Set.Unmarshal(m, b)
}
func (m *RecallSourceRuleSetting_Set) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallSourceRuleSetting_Set.Marshal(b, m, deterministic)
}
func (dst *RecallSourceRuleSetting_Set) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallSourceRuleSetting_Set.Merge(dst, src)
}
func (m *RecallSourceRuleSetting_Set) XXX_Size() int {
	return xxx_messageInfo_RecallSourceRuleSetting_Set.Size(m)
}
func (m *RecallSourceRuleSetting_Set) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallSourceRuleSetting_Set.DiscardUnknown(m)
}

var xxx_messageInfo_RecallSourceRuleSetting_Set proto.InternalMessageInfo

func (m *RecallSourceRuleSetting_Set) GetConditions() []*RecallSourceRuleSetting_Condition {
	if m != nil {
		return m.Conditions
	}
	return nil
}

// 召回源规则（游离类型）
type RecallSourceRule struct {
	Type                 uint32                       `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Name                 string                       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Operator             []*RecallSourceRule_Operator `protobuf:"bytes,3,rep,name=operator,proto3" json:"operator,omitempty"`
	Children             []*RecallSourceRule          `protobuf:"bytes,4,rep,name=children,proto3" json:"children,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *RecallSourceRule) Reset()         { *m = RecallSourceRule{} }
func (m *RecallSourceRule) String() string { return proto.CompactTextString(m) }
func (*RecallSourceRule) ProtoMessage()    {}
func (*RecallSourceRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{5}
}
func (m *RecallSourceRule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallSourceRule.Unmarshal(m, b)
}
func (m *RecallSourceRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallSourceRule.Marshal(b, m, deterministic)
}
func (dst *RecallSourceRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallSourceRule.Merge(dst, src)
}
func (m *RecallSourceRule) XXX_Size() int {
	return xxx_messageInfo_RecallSourceRule.Size(m)
}
func (m *RecallSourceRule) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallSourceRule.DiscardUnknown(m)
}

var xxx_messageInfo_RecallSourceRule proto.InternalMessageInfo

func (m *RecallSourceRule) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *RecallSourceRule) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RecallSourceRule) GetOperator() []*RecallSourceRule_Operator {
	if m != nil {
		return m.Operator
	}
	return nil
}

func (m *RecallSourceRule) GetChildren() []*RecallSourceRule {
	if m != nil {
		return m.Children
	}
	return nil
}

type RecallSourceRule_Operator struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Values               []string `protobuf:"bytes,3,rep,name=values,proto3" json:"values,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecallSourceRule_Operator) Reset()         { *m = RecallSourceRule_Operator{} }
func (m *RecallSourceRule_Operator) String() string { return proto.CompactTextString(m) }
func (*RecallSourceRule_Operator) ProtoMessage()    {}
func (*RecallSourceRule_Operator) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{5, 0}
}
func (m *RecallSourceRule_Operator) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallSourceRule_Operator.Unmarshal(m, b)
}
func (m *RecallSourceRule_Operator) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallSourceRule_Operator.Marshal(b, m, deterministic)
}
func (dst *RecallSourceRule_Operator) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallSourceRule_Operator.Merge(dst, src)
}
func (m *RecallSourceRule_Operator) XXX_Size() int {
	return xxx_messageInfo_RecallSourceRule_Operator.Size(m)
}
func (m *RecallSourceRule_Operator) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallSourceRule_Operator.DiscardUnknown(m)
}

var xxx_messageInfo_RecallSourceRule_Operator proto.InternalMessageInfo

func (m *RecallSourceRule_Operator) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *RecallSourceRule_Operator) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RecallSourceRule_Operator) GetValues() []string {
	if m != nil {
		return m.Values
	}
	return nil
}

// 排序参数表（游离类型）
type SortParameterTable struct {
	Parameters           []*SortParameterTable_SortParameter `protobuf:"bytes,1,rep,name=parameters,proto3" json:"parameters,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *SortParameterTable) Reset()         { *m = SortParameterTable{} }
func (m *SortParameterTable) String() string { return proto.CompactTextString(m) }
func (*SortParameterTable) ProtoMessage()    {}
func (*SortParameterTable) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{6}
}
func (m *SortParameterTable) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SortParameterTable.Unmarshal(m, b)
}
func (m *SortParameterTable) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SortParameterTable.Marshal(b, m, deterministic)
}
func (dst *SortParameterTable) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SortParameterTable.Merge(dst, src)
}
func (m *SortParameterTable) XXX_Size() int {
	return xxx_messageInfo_SortParameterTable.Size(m)
}
func (m *SortParameterTable) XXX_DiscardUnknown() {
	xxx_messageInfo_SortParameterTable.DiscardUnknown(m)
}

var xxx_messageInfo_SortParameterTable proto.InternalMessageInfo

func (m *SortParameterTable) GetParameters() []*SortParameterTable_SortParameter {
	if m != nil {
		return m.Parameters
	}
	return nil
}

type SortParameterTable_Item struct {
	Value                string   `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	Score                int32    `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SortParameterTable_Item) Reset()         { *m = SortParameterTable_Item{} }
func (m *SortParameterTable_Item) String() string { return proto.CompactTextString(m) }
func (*SortParameterTable_Item) ProtoMessage()    {}
func (*SortParameterTable_Item) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{6, 0}
}
func (m *SortParameterTable_Item) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SortParameterTable_Item.Unmarshal(m, b)
}
func (m *SortParameterTable_Item) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SortParameterTable_Item.Marshal(b, m, deterministic)
}
func (dst *SortParameterTable_Item) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SortParameterTable_Item.Merge(dst, src)
}
func (m *SortParameterTable_Item) XXX_Size() int {
	return xxx_messageInfo_SortParameterTable_Item.Size(m)
}
func (m *SortParameterTable_Item) XXX_DiscardUnknown() {
	xxx_messageInfo_SortParameterTable_Item.DiscardUnknown(m)
}

var xxx_messageInfo_SortParameterTable_Item proto.InternalMessageInfo

func (m *SortParameterTable_Item) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

func (m *SortParameterTable_Item) GetScore() int32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type SortParameterTable_SortParameter struct {
	Type                 uint32                              `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Name                 string                              `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Items                []*SortParameterTable_Item          `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	Children             []*SortParameterTable_SortParameter `protobuf:"bytes,4,rep,name=children,proto3" json:"children,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *SortParameterTable_SortParameter) Reset()         { *m = SortParameterTable_SortParameter{} }
func (m *SortParameterTable_SortParameter) String() string { return proto.CompactTextString(m) }
func (*SortParameterTable_SortParameter) ProtoMessage()    {}
func (*SortParameterTable_SortParameter) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{6, 1}
}
func (m *SortParameterTable_SortParameter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SortParameterTable_SortParameter.Unmarshal(m, b)
}
func (m *SortParameterTable_SortParameter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SortParameterTable_SortParameter.Marshal(b, m, deterministic)
}
func (dst *SortParameterTable_SortParameter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SortParameterTable_SortParameter.Merge(dst, src)
}
func (m *SortParameterTable_SortParameter) XXX_Size() int {
	return xxx_messageInfo_SortParameterTable_SortParameter.Size(m)
}
func (m *SortParameterTable_SortParameter) XXX_DiscardUnknown() {
	xxx_messageInfo_SortParameterTable_SortParameter.DiscardUnknown(m)
}

var xxx_messageInfo_SortParameterTable_SortParameter proto.InternalMessageInfo

func (m *SortParameterTable_SortParameter) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *SortParameterTable_SortParameter) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SortParameterTable_SortParameter) GetItems() []*SortParameterTable_Item {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *SortParameterTable_SortParameter) GetChildren() []*SortParameterTable_SortParameter {
	if m != nil {
		return m.Children
	}
	return nil
}

// 多级排序规则（游离类型）
type RecallSourceQueueRule struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecallSourceQueueRule) Reset()         { *m = RecallSourceQueueRule{} }
func (m *RecallSourceQueueRule) String() string { return proto.CompactTextString(m) }
func (*RecallSourceQueueRule) ProtoMessage()    {}
func (*RecallSourceQueueRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{7}
}
func (m *RecallSourceQueueRule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallSourceQueueRule.Unmarshal(m, b)
}
func (m *RecallSourceQueueRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallSourceQueueRule.Marshal(b, m, deterministic)
}
func (dst *RecallSourceQueueRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallSourceQueueRule.Merge(dst, src)
}
func (m *RecallSourceQueueRule) XXX_Size() int {
	return xxx_messageInfo_RecallSourceQueueRule.Size(m)
}
func (m *RecallSourceQueueRule) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallSourceQueueRule.DiscardUnknown(m)
}

var xxx_messageInfo_RecallSourceQueueRule proto.InternalMessageInfo

func (m *RecallSourceQueueRule) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *RecallSourceQueueRule) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type GetRecallSourceQueueRuleRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecallSourceQueueRuleRequest) Reset()         { *m = GetRecallSourceQueueRuleRequest{} }
func (m *GetRecallSourceQueueRuleRequest) String() string { return proto.CompactTextString(m) }
func (*GetRecallSourceQueueRuleRequest) ProtoMessage()    {}
func (*GetRecallSourceQueueRuleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{8}
}
func (m *GetRecallSourceQueueRuleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallSourceQueueRuleRequest.Unmarshal(m, b)
}
func (m *GetRecallSourceQueueRuleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallSourceQueueRuleRequest.Marshal(b, m, deterministic)
}
func (dst *GetRecallSourceQueueRuleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallSourceQueueRuleRequest.Merge(dst, src)
}
func (m *GetRecallSourceQueueRuleRequest) XXX_Size() int {
	return xxx_messageInfo_GetRecallSourceQueueRuleRequest.Size(m)
}
func (m *GetRecallSourceQueueRuleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallSourceQueueRuleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallSourceQueueRuleRequest proto.InternalMessageInfo

type GetRecallSourceQueueRuleResponse struct {
	List                 []*RecallSourceQueueRule `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetRecallSourceQueueRuleResponse) Reset()         { *m = GetRecallSourceQueueRuleResponse{} }
func (m *GetRecallSourceQueueRuleResponse) String() string { return proto.CompactTextString(m) }
func (*GetRecallSourceQueueRuleResponse) ProtoMessage()    {}
func (*GetRecallSourceQueueRuleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{9}
}
func (m *GetRecallSourceQueueRuleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallSourceQueueRuleResponse.Unmarshal(m, b)
}
func (m *GetRecallSourceQueueRuleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallSourceQueueRuleResponse.Marshal(b, m, deterministic)
}
func (dst *GetRecallSourceQueueRuleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallSourceQueueRuleResponse.Merge(dst, src)
}
func (m *GetRecallSourceQueueRuleResponse) XXX_Size() int {
	return xxx_messageInfo_GetRecallSourceQueueRuleResponse.Size(m)
}
func (m *GetRecallSourceQueueRuleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallSourceQueueRuleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallSourceQueueRuleResponse proto.InternalMessageInfo

func (m *GetRecallSourceQueueRuleResponse) GetList() []*RecallSourceQueueRule {
	if m != nil {
		return m.List
	}
	return nil
}

// 请求策略列表
type ListStrategiesRequest struct {
	SceneType            uint32   `protobuf:"varint,1,opt,name=scene_type,json=sceneType,proto3" json:"scene_type,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	PageNumber           uint32   `protobuf:"varint,3,opt,name=page_number,json=pageNumber,proto3" json:"page_number,omitempty"`
	PageSize             uint32   `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListStrategiesRequest) Reset()         { *m = ListStrategiesRequest{} }
func (m *ListStrategiesRequest) String() string { return proto.CompactTextString(m) }
func (*ListStrategiesRequest) ProtoMessage()    {}
func (*ListStrategiesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{10}
}
func (m *ListStrategiesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListStrategiesRequest.Unmarshal(m, b)
}
func (m *ListStrategiesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListStrategiesRequest.Marshal(b, m, deterministic)
}
func (dst *ListStrategiesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListStrategiesRequest.Merge(dst, src)
}
func (m *ListStrategiesRequest) XXX_Size() int {
	return xxx_messageInfo_ListStrategiesRequest.Size(m)
}
func (m *ListStrategiesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListStrategiesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListStrategiesRequest proto.InternalMessageInfo

func (m *ListStrategiesRequest) GetSceneType() uint32 {
	if m != nil {
		return m.SceneType
	}
	return 0
}

func (m *ListStrategiesRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ListStrategiesRequest) GetPageNumber() uint32 {
	if m != nil {
		return m.PageNumber
	}
	return 0
}

func (m *ListStrategiesRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 响应策略列表
type ListStrategiesResponse struct {
	Strategies           []*Strategy `protobuf:"bytes,1,rep,name=strategies,proto3" json:"strategies,omitempty"`
	Total                uint32      `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *ListStrategiesResponse) Reset()         { *m = ListStrategiesResponse{} }
func (m *ListStrategiesResponse) String() string { return proto.CompactTextString(m) }
func (*ListStrategiesResponse) ProtoMessage()    {}
func (*ListStrategiesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{11}
}
func (m *ListStrategiesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListStrategiesResponse.Unmarshal(m, b)
}
func (m *ListStrategiesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListStrategiesResponse.Marshal(b, m, deterministic)
}
func (dst *ListStrategiesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListStrategiesResponse.Merge(dst, src)
}
func (m *ListStrategiesResponse) XXX_Size() int {
	return xxx_messageInfo_ListStrategiesResponse.Size(m)
}
func (m *ListStrategiesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListStrategiesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListStrategiesResponse proto.InternalMessageInfo

func (m *ListStrategiesResponse) GetStrategies() []*Strategy {
	if m != nil {
		return m.Strategies
	}
	return nil
}

func (m *ListStrategiesResponse) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 获取策略详情请求
type GetStrategyRequest struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStrategyRequest) Reset()         { *m = GetStrategyRequest{} }
func (m *GetStrategyRequest) String() string { return proto.CompactTextString(m) }
func (*GetStrategyRequest) ProtoMessage()    {}
func (*GetStrategyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{12}
}
func (m *GetStrategyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStrategyRequest.Unmarshal(m, b)
}
func (m *GetStrategyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStrategyRequest.Marshal(b, m, deterministic)
}
func (dst *GetStrategyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStrategyRequest.Merge(dst, src)
}
func (m *GetStrategyRequest) XXX_Size() int {
	return xxx_messageInfo_GetStrategyRequest.Size(m)
}
func (m *GetStrategyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStrategyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetStrategyRequest proto.InternalMessageInfo

func (m *GetStrategyRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 获取策略详情响应
type GetStrategyResponse struct {
	Strategy             *Strategy `protobuf:"bytes,1,opt,name=strategy,proto3" json:"strategy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetStrategyResponse) Reset()         { *m = GetStrategyResponse{} }
func (m *GetStrategyResponse) String() string { return proto.CompactTextString(m) }
func (*GetStrategyResponse) ProtoMessage()    {}
func (*GetStrategyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{13}
}
func (m *GetStrategyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStrategyResponse.Unmarshal(m, b)
}
func (m *GetStrategyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStrategyResponse.Marshal(b, m, deterministic)
}
func (dst *GetStrategyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStrategyResponse.Merge(dst, src)
}
func (m *GetStrategyResponse) XXX_Size() int {
	return xxx_messageInfo_GetStrategyResponse.Size(m)
}
func (m *GetStrategyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStrategyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetStrategyResponse proto.InternalMessageInfo

func (m *GetStrategyResponse) GetStrategy() *Strategy {
	if m != nil {
		return m.Strategy
	}
	return nil
}

// 创建策略请求
type CreateStrategyRequest struct {
	Strategy             *Strategy `protobuf:"bytes,1,opt,name=strategy,proto3" json:"strategy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *CreateStrategyRequest) Reset()         { *m = CreateStrategyRequest{} }
func (m *CreateStrategyRequest) String() string { return proto.CompactTextString(m) }
func (*CreateStrategyRequest) ProtoMessage()    {}
func (*CreateStrategyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{14}
}
func (m *CreateStrategyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateStrategyRequest.Unmarshal(m, b)
}
func (m *CreateStrategyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateStrategyRequest.Marshal(b, m, deterministic)
}
func (dst *CreateStrategyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateStrategyRequest.Merge(dst, src)
}
func (m *CreateStrategyRequest) XXX_Size() int {
	return xxx_messageInfo_CreateStrategyRequest.Size(m)
}
func (m *CreateStrategyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateStrategyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateStrategyRequest proto.InternalMessageInfo

func (m *CreateStrategyRequest) GetStrategy() *Strategy {
	if m != nil {
		return m.Strategy
	}
	return nil
}

// 创建策略响应
type CreateStrategyResponse struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateStrategyResponse) Reset()         { *m = CreateStrategyResponse{} }
func (m *CreateStrategyResponse) String() string { return proto.CompactTextString(m) }
func (*CreateStrategyResponse) ProtoMessage()    {}
func (*CreateStrategyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{15}
}
func (m *CreateStrategyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateStrategyResponse.Unmarshal(m, b)
}
func (m *CreateStrategyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateStrategyResponse.Marshal(b, m, deterministic)
}
func (dst *CreateStrategyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateStrategyResponse.Merge(dst, src)
}
func (m *CreateStrategyResponse) XXX_Size() int {
	return xxx_messageInfo_CreateStrategyResponse.Size(m)
}
func (m *CreateStrategyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateStrategyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateStrategyResponse proto.InternalMessageInfo

func (m *CreateStrategyResponse) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 更新策略请求
type UpdateStrategyRequest struct {
	Strategy             *Strategy `protobuf:"bytes,1,opt,name=strategy,proto3" json:"strategy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *UpdateStrategyRequest) Reset()         { *m = UpdateStrategyRequest{} }
func (m *UpdateStrategyRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateStrategyRequest) ProtoMessage()    {}
func (*UpdateStrategyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{16}
}
func (m *UpdateStrategyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStrategyRequest.Unmarshal(m, b)
}
func (m *UpdateStrategyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStrategyRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateStrategyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStrategyRequest.Merge(dst, src)
}
func (m *UpdateStrategyRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateStrategyRequest.Size(m)
}
func (m *UpdateStrategyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStrategyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStrategyRequest proto.InternalMessageInfo

func (m *UpdateStrategyRequest) GetStrategy() *Strategy {
	if m != nil {
		return m.Strategy
	}
	return nil
}

// 更新策略响应
type UpdateStrategyResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateStrategyResponse) Reset()         { *m = UpdateStrategyResponse{} }
func (m *UpdateStrategyResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateStrategyResponse) ProtoMessage()    {}
func (*UpdateStrategyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{17}
}
func (m *UpdateStrategyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStrategyResponse.Unmarshal(m, b)
}
func (m *UpdateStrategyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStrategyResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateStrategyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStrategyResponse.Merge(dst, src)
}
func (m *UpdateStrategyResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateStrategyResponse.Size(m)
}
func (m *UpdateStrategyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStrategyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStrategyResponse proto.InternalMessageInfo

// 暂停策略请求
type PauseStrategyRequest struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PauseStrategyRequest) Reset()         { *m = PauseStrategyRequest{} }
func (m *PauseStrategyRequest) String() string { return proto.CompactTextString(m) }
func (*PauseStrategyRequest) ProtoMessage()    {}
func (*PauseStrategyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{18}
}
func (m *PauseStrategyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PauseStrategyRequest.Unmarshal(m, b)
}
func (m *PauseStrategyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PauseStrategyRequest.Marshal(b, m, deterministic)
}
func (dst *PauseStrategyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PauseStrategyRequest.Merge(dst, src)
}
func (m *PauseStrategyRequest) XXX_Size() int {
	return xxx_messageInfo_PauseStrategyRequest.Size(m)
}
func (m *PauseStrategyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PauseStrategyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PauseStrategyRequest proto.InternalMessageInfo

func (m *PauseStrategyRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 暂停策略响应
type PauseStrategyResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PauseStrategyResponse) Reset()         { *m = PauseStrategyResponse{} }
func (m *PauseStrategyResponse) String() string { return proto.CompactTextString(m) }
func (*PauseStrategyResponse) ProtoMessage()    {}
func (*PauseStrategyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{19}
}
func (m *PauseStrategyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PauseStrategyResponse.Unmarshal(m, b)
}
func (m *PauseStrategyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PauseStrategyResponse.Marshal(b, m, deterministic)
}
func (dst *PauseStrategyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PauseStrategyResponse.Merge(dst, src)
}
func (m *PauseStrategyResponse) XXX_Size() int {
	return xxx_messageInfo_PauseStrategyResponse.Size(m)
}
func (m *PauseStrategyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PauseStrategyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PauseStrategyResponse proto.InternalMessageInfo

// 启动策略请求
type StartStrategyRequest struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartStrategyRequest) Reset()         { *m = StartStrategyRequest{} }
func (m *StartStrategyRequest) String() string { return proto.CompactTextString(m) }
func (*StartStrategyRequest) ProtoMessage()    {}
func (*StartStrategyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{20}
}
func (m *StartStrategyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartStrategyRequest.Unmarshal(m, b)
}
func (m *StartStrategyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartStrategyRequest.Marshal(b, m, deterministic)
}
func (dst *StartStrategyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartStrategyRequest.Merge(dst, src)
}
func (m *StartStrategyRequest) XXX_Size() int {
	return xxx_messageInfo_StartStrategyRequest.Size(m)
}
func (m *StartStrategyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StartStrategyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StartStrategyRequest proto.InternalMessageInfo

func (m *StartStrategyRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 启动策略响应
type StartStrategyResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartStrategyResponse) Reset()         { *m = StartStrategyResponse{} }
func (m *StartStrategyResponse) String() string { return proto.CompactTextString(m) }
func (*StartStrategyResponse) ProtoMessage()    {}
func (*StartStrategyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{21}
}
func (m *StartStrategyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartStrategyResponse.Unmarshal(m, b)
}
func (m *StartStrategyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartStrategyResponse.Marshal(b, m, deterministic)
}
func (dst *StartStrategyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartStrategyResponse.Merge(dst, src)
}
func (m *StartStrategyResponse) XXX_Size() int {
	return xxx_messageInfo_StartStrategyResponse.Size(m)
}
func (m *StartStrategyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StartStrategyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StartStrategyResponse proto.InternalMessageInfo

// 删除策略请求
type DeleteStrategyRequest struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteStrategyRequest) Reset()         { *m = DeleteStrategyRequest{} }
func (m *DeleteStrategyRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteStrategyRequest) ProtoMessage()    {}
func (*DeleteStrategyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{22}
}
func (m *DeleteStrategyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteStrategyRequest.Unmarshal(m, b)
}
func (m *DeleteStrategyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteStrategyRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteStrategyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteStrategyRequest.Merge(dst, src)
}
func (m *DeleteStrategyRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteStrategyRequest.Size(m)
}
func (m *DeleteStrategyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteStrategyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteStrategyRequest proto.InternalMessageInfo

func (m *DeleteStrategyRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 删除策略响应
type DeleteStrategyResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteStrategyResponse) Reset()         { *m = DeleteStrategyResponse{} }
func (m *DeleteStrategyResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteStrategyResponse) ProtoMessage()    {}
func (*DeleteStrategyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{23}
}
func (m *DeleteStrategyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteStrategyResponse.Unmarshal(m, b)
}
func (m *DeleteStrategyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteStrategyResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteStrategyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteStrategyResponse.Merge(dst, src)
}
func (m *DeleteStrategyResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteStrategyResponse.Size(m)
}
func (m *DeleteStrategyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteStrategyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteStrategyResponse proto.InternalMessageInfo

// 获取过滤规则请求
type GetFilterRulesRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFilterRulesRequest) Reset()         { *m = GetFilterRulesRequest{} }
func (m *GetFilterRulesRequest) String() string { return proto.CompactTextString(m) }
func (*GetFilterRulesRequest) ProtoMessage()    {}
func (*GetFilterRulesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{24}
}
func (m *GetFilterRulesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterRulesRequest.Unmarshal(m, b)
}
func (m *GetFilterRulesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterRulesRequest.Marshal(b, m, deterministic)
}
func (dst *GetFilterRulesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterRulesRequest.Merge(dst, src)
}
func (m *GetFilterRulesRequest) XXX_Size() int {
	return xxx_messageInfo_GetFilterRulesRequest.Size(m)
}
func (m *GetFilterRulesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterRulesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterRulesRequest proto.InternalMessageInfo

// 获取过滤规则响应
type GetFilterRulesResponse struct {
	FilterRules          []*FilterRule `protobuf:"bytes,1,rep,name=filter_rules,json=filterRules,proto3" json:"filter_rules,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetFilterRulesResponse) Reset()         { *m = GetFilterRulesResponse{} }
func (m *GetFilterRulesResponse) String() string { return proto.CompactTextString(m) }
func (*GetFilterRulesResponse) ProtoMessage()    {}
func (*GetFilterRulesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{25}
}
func (m *GetFilterRulesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterRulesResponse.Unmarshal(m, b)
}
func (m *GetFilterRulesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterRulesResponse.Marshal(b, m, deterministic)
}
func (dst *GetFilterRulesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterRulesResponse.Merge(dst, src)
}
func (m *GetFilterRulesResponse) XXX_Size() int {
	return xxx_messageInfo_GetFilterRulesResponse.Size(m)
}
func (m *GetFilterRulesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterRulesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterRulesResponse proto.InternalMessageInfo

func (m *GetFilterRulesResponse) GetFilterRules() []*FilterRule {
	if m != nil {
		return m.FilterRules
	}
	return nil
}

// 获取场景列表请求
type GetScenesRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScenesRequest) Reset()         { *m = GetScenesRequest{} }
func (m *GetScenesRequest) String() string { return proto.CompactTextString(m) }
func (*GetScenesRequest) ProtoMessage()    {}
func (*GetScenesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{26}
}
func (m *GetScenesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenesRequest.Unmarshal(m, b)
}
func (m *GetScenesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenesRequest.Marshal(b, m, deterministic)
}
func (dst *GetScenesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenesRequest.Merge(dst, src)
}
func (m *GetScenesRequest) XXX_Size() int {
	return xxx_messageInfo_GetScenesRequest.Size(m)
}
func (m *GetScenesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenesRequest proto.InternalMessageInfo

// 获取场景列表响应
type GetScenesResponse struct {
	List                 []*Scene `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScenesResponse) Reset()         { *m = GetScenesResponse{} }
func (m *GetScenesResponse) String() string { return proto.CompactTextString(m) }
func (*GetScenesResponse) ProtoMessage()    {}
func (*GetScenesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{27}
}
func (m *GetScenesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenesResponse.Unmarshal(m, b)
}
func (m *GetScenesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenesResponse.Marshal(b, m, deterministic)
}
func (dst *GetScenesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenesResponse.Merge(dst, src)
}
func (m *GetScenesResponse) XXX_Size() int {
	return xxx_messageInfo_GetScenesResponse.Size(m)
}
func (m *GetScenesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenesResponse proto.InternalMessageInfo

func (m *GetScenesResponse) GetList() []*Scene {
	if m != nil {
		return m.List
	}
	return nil
}

type ListRecallSourcesRequest struct {
	PageNumber           uint32   `protobuf:"varint,1,opt,name=page_number,json=pageNumber,proto3" json:"page_number,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Id                   uint32   `protobuf:"varint,4,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListRecallSourcesRequest) Reset()         { *m = ListRecallSourcesRequest{} }
func (m *ListRecallSourcesRequest) String() string { return proto.CompactTextString(m) }
func (*ListRecallSourcesRequest) ProtoMessage()    {}
func (*ListRecallSourcesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{28}
}
func (m *ListRecallSourcesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListRecallSourcesRequest.Unmarshal(m, b)
}
func (m *ListRecallSourcesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListRecallSourcesRequest.Marshal(b, m, deterministic)
}
func (dst *ListRecallSourcesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListRecallSourcesRequest.Merge(dst, src)
}
func (m *ListRecallSourcesRequest) XXX_Size() int {
	return xxx_messageInfo_ListRecallSourcesRequest.Size(m)
}
func (m *ListRecallSourcesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListRecallSourcesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListRecallSourcesRequest proto.InternalMessageInfo

func (m *ListRecallSourcesRequest) GetPageNumber() uint32 {
	if m != nil {
		return m.PageNumber
	}
	return 0
}

func (m *ListRecallSourcesRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *ListRecallSourcesRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ListRecallSourcesRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type ListRecallSourcesResponse struct {
	RecallSources        []*RecallSource `protobuf:"bytes,1,rep,name=recall_sources,json=recallSources,proto3" json:"recall_sources,omitempty"`
	Total                uint32          `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListRecallSourcesResponse) Reset()         { *m = ListRecallSourcesResponse{} }
func (m *ListRecallSourcesResponse) String() string { return proto.CompactTextString(m) }
func (*ListRecallSourcesResponse) ProtoMessage()    {}
func (*ListRecallSourcesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{29}
}
func (m *ListRecallSourcesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListRecallSourcesResponse.Unmarshal(m, b)
}
func (m *ListRecallSourcesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListRecallSourcesResponse.Marshal(b, m, deterministic)
}
func (dst *ListRecallSourcesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListRecallSourcesResponse.Merge(dst, src)
}
func (m *ListRecallSourcesResponse) XXX_Size() int {
	return xxx_messageInfo_ListRecallSourcesResponse.Size(m)
}
func (m *ListRecallSourcesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListRecallSourcesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListRecallSourcesResponse proto.InternalMessageInfo

func (m *ListRecallSourcesResponse) GetRecallSources() []*RecallSource {
	if m != nil {
		return m.RecallSources
	}
	return nil
}

func (m *ListRecallSourcesResponse) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetRecallSourceDetailsRequest struct {
	RecallSourceId       uint32   `protobuf:"varint,1,opt,name=recall_source_id,json=recallSourceId,proto3" json:"recall_source_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecallSourceDetailsRequest) Reset()         { *m = GetRecallSourceDetailsRequest{} }
func (m *GetRecallSourceDetailsRequest) String() string { return proto.CompactTextString(m) }
func (*GetRecallSourceDetailsRequest) ProtoMessage()    {}
func (*GetRecallSourceDetailsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{30}
}
func (m *GetRecallSourceDetailsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallSourceDetailsRequest.Unmarshal(m, b)
}
func (m *GetRecallSourceDetailsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallSourceDetailsRequest.Marshal(b, m, deterministic)
}
func (dst *GetRecallSourceDetailsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallSourceDetailsRequest.Merge(dst, src)
}
func (m *GetRecallSourceDetailsRequest) XXX_Size() int {
	return xxx_messageInfo_GetRecallSourceDetailsRequest.Size(m)
}
func (m *GetRecallSourceDetailsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallSourceDetailsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallSourceDetailsRequest proto.InternalMessageInfo

func (m *GetRecallSourceDetailsRequest) GetRecallSourceId() uint32 {
	if m != nil {
		return m.RecallSourceId
	}
	return 0
}

type GetRecallSourceDetailsResponse struct {
	RecallSource         *RecallSource `protobuf:"bytes,1,opt,name=recall_source,json=recallSource,proto3" json:"recall_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetRecallSourceDetailsResponse) Reset()         { *m = GetRecallSourceDetailsResponse{} }
func (m *GetRecallSourceDetailsResponse) String() string { return proto.CompactTextString(m) }
func (*GetRecallSourceDetailsResponse) ProtoMessage()    {}
func (*GetRecallSourceDetailsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{31}
}
func (m *GetRecallSourceDetailsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallSourceDetailsResponse.Unmarshal(m, b)
}
func (m *GetRecallSourceDetailsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallSourceDetailsResponse.Marshal(b, m, deterministic)
}
func (dst *GetRecallSourceDetailsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallSourceDetailsResponse.Merge(dst, src)
}
func (m *GetRecallSourceDetailsResponse) XXX_Size() int {
	return xxx_messageInfo_GetRecallSourceDetailsResponse.Size(m)
}
func (m *GetRecallSourceDetailsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallSourceDetailsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallSourceDetailsResponse proto.InternalMessageInfo

func (m *GetRecallSourceDetailsResponse) GetRecallSource() *RecallSource {
	if m != nil {
		return m.RecallSource
	}
	return nil
}

type CreateRecallSourceRequest struct {
	RecallSource         *RecallSource `protobuf:"bytes,1,opt,name=recall_source,json=recallSource,proto3" json:"recall_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CreateRecallSourceRequest) Reset()         { *m = CreateRecallSourceRequest{} }
func (m *CreateRecallSourceRequest) String() string { return proto.CompactTextString(m) }
func (*CreateRecallSourceRequest) ProtoMessage()    {}
func (*CreateRecallSourceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{32}
}
func (m *CreateRecallSourceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateRecallSourceRequest.Unmarshal(m, b)
}
func (m *CreateRecallSourceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateRecallSourceRequest.Marshal(b, m, deterministic)
}
func (dst *CreateRecallSourceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateRecallSourceRequest.Merge(dst, src)
}
func (m *CreateRecallSourceRequest) XXX_Size() int {
	return xxx_messageInfo_CreateRecallSourceRequest.Size(m)
}
func (m *CreateRecallSourceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateRecallSourceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateRecallSourceRequest proto.InternalMessageInfo

func (m *CreateRecallSourceRequest) GetRecallSource() *RecallSource {
	if m != nil {
		return m.RecallSource
	}
	return nil
}

type CreateRecallSourceResponse struct {
	RecallSourceId       uint32   `protobuf:"varint,1,opt,name=recall_source_id,json=recallSourceId,proto3" json:"recall_source_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateRecallSourceResponse) Reset()         { *m = CreateRecallSourceResponse{} }
func (m *CreateRecallSourceResponse) String() string { return proto.CompactTextString(m) }
func (*CreateRecallSourceResponse) ProtoMessage()    {}
func (*CreateRecallSourceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{33}
}
func (m *CreateRecallSourceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateRecallSourceResponse.Unmarshal(m, b)
}
func (m *CreateRecallSourceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateRecallSourceResponse.Marshal(b, m, deterministic)
}
func (dst *CreateRecallSourceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateRecallSourceResponse.Merge(dst, src)
}
func (m *CreateRecallSourceResponse) XXX_Size() int {
	return xxx_messageInfo_CreateRecallSourceResponse.Size(m)
}
func (m *CreateRecallSourceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateRecallSourceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateRecallSourceResponse proto.InternalMessageInfo

func (m *CreateRecallSourceResponse) GetRecallSourceId() uint32 {
	if m != nil {
		return m.RecallSourceId
	}
	return 0
}

type UpdateRecallSourceRequest struct {
	RecallSource         *RecallSource `protobuf:"bytes,1,opt,name=recall_source,json=recallSource,proto3" json:"recall_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdateRecallSourceRequest) Reset()         { *m = UpdateRecallSourceRequest{} }
func (m *UpdateRecallSourceRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateRecallSourceRequest) ProtoMessage()    {}
func (*UpdateRecallSourceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{34}
}
func (m *UpdateRecallSourceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRecallSourceRequest.Unmarshal(m, b)
}
func (m *UpdateRecallSourceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRecallSourceRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateRecallSourceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRecallSourceRequest.Merge(dst, src)
}
func (m *UpdateRecallSourceRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateRecallSourceRequest.Size(m)
}
func (m *UpdateRecallSourceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRecallSourceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRecallSourceRequest proto.InternalMessageInfo

func (m *UpdateRecallSourceRequest) GetRecallSource() *RecallSource {
	if m != nil {
		return m.RecallSource
	}
	return nil
}

type UpdateRecallSourceResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateRecallSourceResponse) Reset()         { *m = UpdateRecallSourceResponse{} }
func (m *UpdateRecallSourceResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateRecallSourceResponse) ProtoMessage()    {}
func (*UpdateRecallSourceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{35}
}
func (m *UpdateRecallSourceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRecallSourceResponse.Unmarshal(m, b)
}
func (m *UpdateRecallSourceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRecallSourceResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateRecallSourceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRecallSourceResponse.Merge(dst, src)
}
func (m *UpdateRecallSourceResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateRecallSourceResponse.Size(m)
}
func (m *UpdateRecallSourceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRecallSourceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRecallSourceResponse proto.InternalMessageInfo

type DeleteRecallSourceRequest struct {
	RecallSourceId       uint32   `protobuf:"varint,1,opt,name=recall_source_id,json=recallSourceId,proto3" json:"recall_source_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteRecallSourceRequest) Reset()         { *m = DeleteRecallSourceRequest{} }
func (m *DeleteRecallSourceRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteRecallSourceRequest) ProtoMessage()    {}
func (*DeleteRecallSourceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{36}
}
func (m *DeleteRecallSourceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteRecallSourceRequest.Unmarshal(m, b)
}
func (m *DeleteRecallSourceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteRecallSourceRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteRecallSourceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteRecallSourceRequest.Merge(dst, src)
}
func (m *DeleteRecallSourceRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteRecallSourceRequest.Size(m)
}
func (m *DeleteRecallSourceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteRecallSourceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteRecallSourceRequest proto.InternalMessageInfo

func (m *DeleteRecallSourceRequest) GetRecallSourceId() uint32 {
	if m != nil {
		return m.RecallSourceId
	}
	return 0
}

type DeleteRecallSourceResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteRecallSourceResponse) Reset()         { *m = DeleteRecallSourceResponse{} }
func (m *DeleteRecallSourceResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteRecallSourceResponse) ProtoMessage()    {}
func (*DeleteRecallSourceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{37}
}
func (m *DeleteRecallSourceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteRecallSourceResponse.Unmarshal(m, b)
}
func (m *DeleteRecallSourceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteRecallSourceResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteRecallSourceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteRecallSourceResponse.Merge(dst, src)
}
func (m *DeleteRecallSourceResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteRecallSourceResponse.Size(m)
}
func (m *DeleteRecallSourceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteRecallSourceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteRecallSourceResponse proto.InternalMessageInfo

type GetRecallSourceRuleListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecallSourceRuleListRequest) Reset()         { *m = GetRecallSourceRuleListRequest{} }
func (m *GetRecallSourceRuleListRequest) String() string { return proto.CompactTextString(m) }
func (*GetRecallSourceRuleListRequest) ProtoMessage()    {}
func (*GetRecallSourceRuleListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{38}
}
func (m *GetRecallSourceRuleListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallSourceRuleListRequest.Unmarshal(m, b)
}
func (m *GetRecallSourceRuleListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallSourceRuleListRequest.Marshal(b, m, deterministic)
}
func (dst *GetRecallSourceRuleListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallSourceRuleListRequest.Merge(dst, src)
}
func (m *GetRecallSourceRuleListRequest) XXX_Size() int {
	return xxx_messageInfo_GetRecallSourceRuleListRequest.Size(m)
}
func (m *GetRecallSourceRuleListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallSourceRuleListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallSourceRuleListRequest proto.InternalMessageInfo

type GetRecallSourceRuleListResponse struct {
	Rules                []*RecallSourceRule `protobuf:"bytes,1,rep,name=rules,proto3" json:"rules,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetRecallSourceRuleListResponse) Reset()         { *m = GetRecallSourceRuleListResponse{} }
func (m *GetRecallSourceRuleListResponse) String() string { return proto.CompactTextString(m) }
func (*GetRecallSourceRuleListResponse) ProtoMessage()    {}
func (*GetRecallSourceRuleListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{39}
}
func (m *GetRecallSourceRuleListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallSourceRuleListResponse.Unmarshal(m, b)
}
func (m *GetRecallSourceRuleListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallSourceRuleListResponse.Marshal(b, m, deterministic)
}
func (dst *GetRecallSourceRuleListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallSourceRuleListResponse.Merge(dst, src)
}
func (m *GetRecallSourceRuleListResponse) XXX_Size() int {
	return xxx_messageInfo_GetRecallSourceRuleListResponse.Size(m)
}
func (m *GetRecallSourceRuleListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallSourceRuleListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallSourceRuleListResponse proto.InternalMessageInfo

func (m *GetRecallSourceRuleListResponse) GetRules() []*RecallSourceRule {
	if m != nil {
		return m.Rules
	}
	return nil
}

type GetDefaultSortParameterTableRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDefaultSortParameterTableRequest) Reset()         { *m = GetDefaultSortParameterTableRequest{} }
func (m *GetDefaultSortParameterTableRequest) String() string { return proto.CompactTextString(m) }
func (*GetDefaultSortParameterTableRequest) ProtoMessage()    {}
func (*GetDefaultSortParameterTableRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{40}
}
func (m *GetDefaultSortParameterTableRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDefaultSortParameterTableRequest.Unmarshal(m, b)
}
func (m *GetDefaultSortParameterTableRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDefaultSortParameterTableRequest.Marshal(b, m, deterministic)
}
func (dst *GetDefaultSortParameterTableRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDefaultSortParameterTableRequest.Merge(dst, src)
}
func (m *GetDefaultSortParameterTableRequest) XXX_Size() int {
	return xxx_messageInfo_GetDefaultSortParameterTableRequest.Size(m)
}
func (m *GetDefaultSortParameterTableRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDefaultSortParameterTableRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetDefaultSortParameterTableRequest proto.InternalMessageInfo

type GetDefaultSortParameterTableResponse struct {
	SortParameterTable   *SortParameterTable `protobuf:"bytes,1,opt,name=sort_parameter_table,json=sortParameterTable,proto3" json:"sort_parameter_table,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetDefaultSortParameterTableResponse) Reset()         { *m = GetDefaultSortParameterTableResponse{} }
func (m *GetDefaultSortParameterTableResponse) String() string { return proto.CompactTextString(m) }
func (*GetDefaultSortParameterTableResponse) ProtoMessage()    {}
func (*GetDefaultSortParameterTableResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{41}
}
func (m *GetDefaultSortParameterTableResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDefaultSortParameterTableResponse.Unmarshal(m, b)
}
func (m *GetDefaultSortParameterTableResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDefaultSortParameterTableResponse.Marshal(b, m, deterministic)
}
func (dst *GetDefaultSortParameterTableResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDefaultSortParameterTableResponse.Merge(dst, src)
}
func (m *GetDefaultSortParameterTableResponse) XXX_Size() int {
	return xxx_messageInfo_GetDefaultSortParameterTableResponse.Size(m)
}
func (m *GetDefaultSortParameterTableResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDefaultSortParameterTableResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetDefaultSortParameterTableResponse proto.InternalMessageInfo

func (m *GetDefaultSortParameterTableResponse) GetSortParameterTable() *SortParameterTable {
	if m != nil {
		return m.SortParameterTable
	}
	return nil
}

type UpdateDefaultSortParameterTableRequest struct {
	SortParameterTable   *SortParameterTable `protobuf:"bytes,1,opt,name=sort_parameter_table,json=sortParameterTable,proto3" json:"sort_parameter_table,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UpdateDefaultSortParameterTableRequest) Reset() {
	*m = UpdateDefaultSortParameterTableRequest{}
}
func (m *UpdateDefaultSortParameterTableRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateDefaultSortParameterTableRequest) ProtoMessage()    {}
func (*UpdateDefaultSortParameterTableRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{42}
}
func (m *UpdateDefaultSortParameterTableRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateDefaultSortParameterTableRequest.Unmarshal(m, b)
}
func (m *UpdateDefaultSortParameterTableRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateDefaultSortParameterTableRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateDefaultSortParameterTableRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateDefaultSortParameterTableRequest.Merge(dst, src)
}
func (m *UpdateDefaultSortParameterTableRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateDefaultSortParameterTableRequest.Size(m)
}
func (m *UpdateDefaultSortParameterTableRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateDefaultSortParameterTableRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateDefaultSortParameterTableRequest proto.InternalMessageInfo

func (m *UpdateDefaultSortParameterTableRequest) GetSortParameterTable() *SortParameterTable {
	if m != nil {
		return m.SortParameterTable
	}
	return nil
}

type UpdateDefaultSortParameterTableResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateDefaultSortParameterTableResponse) Reset() {
	*m = UpdateDefaultSortParameterTableResponse{}
}
func (m *UpdateDefaultSortParameterTableResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateDefaultSortParameterTableResponse) ProtoMessage()    {}
func (*UpdateDefaultSortParameterTableResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_878064ef3ea88777, []int{43}
}
func (m *UpdateDefaultSortParameterTableResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateDefaultSortParameterTableResponse.Unmarshal(m, b)
}
func (m *UpdateDefaultSortParameterTableResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateDefaultSortParameterTableResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateDefaultSortParameterTableResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateDefaultSortParameterTableResponse.Merge(dst, src)
}
func (m *UpdateDefaultSortParameterTableResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateDefaultSortParameterTableResponse.Size(m)
}
func (m *UpdateDefaultSortParameterTableResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateDefaultSortParameterTableResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateDefaultSortParameterTableResponse proto.InternalMessageInfo

func init() {
	proto.RegisterType((*Strategy)(nil), "esport_admin_logic.Strategy")
	proto.RegisterType((*Strategy_RecallSource)(nil), "esport_admin_logic.Strategy.RecallSource")
	proto.RegisterType((*Strategy_AbTestSetting)(nil), "esport_admin_logic.Strategy.AbTestSetting")
	proto.RegisterType((*Strategy_UserGroupSetting)(nil), "esport_admin_logic.Strategy.UserGroupSetting")
	proto.RegisterType((*FilterRule)(nil), "esport_admin_logic.FilterRule")
	proto.RegisterType((*Scene)(nil), "esport_admin_logic.Scene")
	proto.RegisterType((*RecallSource)(nil), "esport_admin_logic.RecallSource")
	proto.RegisterType((*RecallSource_FilterRuleValue)(nil), "esport_admin_logic.RecallSource.FilterRuleValue")
	proto.RegisterType((*RecallSourceRuleSetting)(nil), "esport_admin_logic.RecallSourceRuleSetting")
	proto.RegisterType((*RecallSourceRuleSetting_Condition)(nil), "esport_admin_logic.RecallSourceRuleSetting.Condition")
	proto.RegisterType((*RecallSourceRuleSetting_Set)(nil), "esport_admin_logic.RecallSourceRuleSetting.Set")
	proto.RegisterType((*RecallSourceRule)(nil), "esport_admin_logic.RecallSourceRule")
	proto.RegisterType((*RecallSourceRule_Operator)(nil), "esport_admin_logic.RecallSourceRule.Operator")
	proto.RegisterType((*SortParameterTable)(nil), "esport_admin_logic.SortParameterTable")
	proto.RegisterType((*SortParameterTable_Item)(nil), "esport_admin_logic.SortParameterTable.Item")
	proto.RegisterType((*SortParameterTable_SortParameter)(nil), "esport_admin_logic.SortParameterTable.SortParameter")
	proto.RegisterType((*RecallSourceQueueRule)(nil), "esport_admin_logic.RecallSourceQueueRule")
	proto.RegisterType((*GetRecallSourceQueueRuleRequest)(nil), "esport_admin_logic.GetRecallSourceQueueRuleRequest")
	proto.RegisterType((*GetRecallSourceQueueRuleResponse)(nil), "esport_admin_logic.GetRecallSourceQueueRuleResponse")
	proto.RegisterType((*ListStrategiesRequest)(nil), "esport_admin_logic.ListStrategiesRequest")
	proto.RegisterType((*ListStrategiesResponse)(nil), "esport_admin_logic.ListStrategiesResponse")
	proto.RegisterType((*GetStrategyRequest)(nil), "esport_admin_logic.GetStrategyRequest")
	proto.RegisterType((*GetStrategyResponse)(nil), "esport_admin_logic.GetStrategyResponse")
	proto.RegisterType((*CreateStrategyRequest)(nil), "esport_admin_logic.CreateStrategyRequest")
	proto.RegisterType((*CreateStrategyResponse)(nil), "esport_admin_logic.CreateStrategyResponse")
	proto.RegisterType((*UpdateStrategyRequest)(nil), "esport_admin_logic.UpdateStrategyRequest")
	proto.RegisterType((*UpdateStrategyResponse)(nil), "esport_admin_logic.UpdateStrategyResponse")
	proto.RegisterType((*PauseStrategyRequest)(nil), "esport_admin_logic.PauseStrategyRequest")
	proto.RegisterType((*PauseStrategyResponse)(nil), "esport_admin_logic.PauseStrategyResponse")
	proto.RegisterType((*StartStrategyRequest)(nil), "esport_admin_logic.StartStrategyRequest")
	proto.RegisterType((*StartStrategyResponse)(nil), "esport_admin_logic.StartStrategyResponse")
	proto.RegisterType((*DeleteStrategyRequest)(nil), "esport_admin_logic.DeleteStrategyRequest")
	proto.RegisterType((*DeleteStrategyResponse)(nil), "esport_admin_logic.DeleteStrategyResponse")
	proto.RegisterType((*GetFilterRulesRequest)(nil), "esport_admin_logic.GetFilterRulesRequest")
	proto.RegisterType((*GetFilterRulesResponse)(nil), "esport_admin_logic.GetFilterRulesResponse")
	proto.RegisterType((*GetScenesRequest)(nil), "esport_admin_logic.GetScenesRequest")
	proto.RegisterType((*GetScenesResponse)(nil), "esport_admin_logic.GetScenesResponse")
	proto.RegisterType((*ListRecallSourcesRequest)(nil), "esport_admin_logic.ListRecallSourcesRequest")
	proto.RegisterType((*ListRecallSourcesResponse)(nil), "esport_admin_logic.ListRecallSourcesResponse")
	proto.RegisterType((*GetRecallSourceDetailsRequest)(nil), "esport_admin_logic.GetRecallSourceDetailsRequest")
	proto.RegisterType((*GetRecallSourceDetailsResponse)(nil), "esport_admin_logic.GetRecallSourceDetailsResponse")
	proto.RegisterType((*CreateRecallSourceRequest)(nil), "esport_admin_logic.CreateRecallSourceRequest")
	proto.RegisterType((*CreateRecallSourceResponse)(nil), "esport_admin_logic.CreateRecallSourceResponse")
	proto.RegisterType((*UpdateRecallSourceRequest)(nil), "esport_admin_logic.UpdateRecallSourceRequest")
	proto.RegisterType((*UpdateRecallSourceResponse)(nil), "esport_admin_logic.UpdateRecallSourceResponse")
	proto.RegisterType((*DeleteRecallSourceRequest)(nil), "esport_admin_logic.DeleteRecallSourceRequest")
	proto.RegisterType((*DeleteRecallSourceResponse)(nil), "esport_admin_logic.DeleteRecallSourceResponse")
	proto.RegisterType((*GetRecallSourceRuleListRequest)(nil), "esport_admin_logic.GetRecallSourceRuleListRequest")
	proto.RegisterType((*GetRecallSourceRuleListResponse)(nil), "esport_admin_logic.GetRecallSourceRuleListResponse")
	proto.RegisterType((*GetDefaultSortParameterTableRequest)(nil), "esport_admin_logic.GetDefaultSortParameterTableRequest")
	proto.RegisterType((*GetDefaultSortParameterTableResponse)(nil), "esport_admin_logic.GetDefaultSortParameterTableResponse")
	proto.RegisterType((*UpdateDefaultSortParameterTableRequest)(nil), "esport_admin_logic.UpdateDefaultSortParameterTableRequest")
	proto.RegisterType((*UpdateDefaultSortParameterTableResponse)(nil), "esport_admin_logic.UpdateDefaultSortParameterTableResponse")
	proto.RegisterEnum("esport_admin_logic.StrategyStatus", StrategyStatus_name, StrategyStatus_value)
	proto.RegisterEnum("esport_admin_logic.DelimitType", DelimitType_name, DelimitType_value)
	proto.RegisterEnum("esport_admin_logic.RecallSource_SortParameterType", RecallSource_SortParameterType_name, RecallSource_SortParameterType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// EsportRcmdAdminServiceClient is the client API for EsportRcmdAdminService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type EsportRcmdAdminServiceClient interface {
	// 列出策略列表
	ListStrategies(ctx context.Context, in *ListStrategiesRequest, opts ...grpc.CallOption) (*ListStrategiesResponse, error)
	// 获取特定策略的详细信息
	GetStrategy(ctx context.Context, in *GetStrategyRequest, opts ...grpc.CallOption) (*GetStrategyResponse, error)
	// 创建新策略
	CreateStrategy(ctx context.Context, in *CreateStrategyRequest, opts ...grpc.CallOption) (*CreateStrategyResponse, error)
	// 更新现有策略
	UpdateStrategy(ctx context.Context, in *UpdateStrategyRequest, opts ...grpc.CallOption) (*UpdateStrategyResponse, error)
	// 暂停策略
	PauseStrategy(ctx context.Context, in *PauseStrategyRequest, opts ...grpc.CallOption) (*PauseStrategyResponse, error)
	// 启动策略
	StartStrategy(ctx context.Context, in *StartStrategyRequest, opts ...grpc.CallOption) (*StartStrategyResponse, error)
	// 删除策略
	DeleteStrategy(ctx context.Context, in *DeleteStrategyRequest, opts ...grpc.CallOption) (*DeleteStrategyResponse, error)
	// 获取过滤规则
	GetFilterRules(ctx context.Context, in *GetFilterRulesRequest, opts ...grpc.CallOption) (*GetFilterRulesResponse, error)
	// 获取场景列表
	GetScenes(ctx context.Context, in *GetScenesRequest, opts ...grpc.CallOption) (*GetScenesResponse, error)
	// 列出所有召回源
	ListRecallSources(ctx context.Context, in *ListRecallSourcesRequest, opts ...grpc.CallOption) (*ListRecallSourcesResponse, error)
	// 获取某个召回源的详细信息
	GetRecallSourceDetails(ctx context.Context, in *GetRecallSourceDetailsRequest, opts ...grpc.CallOption) (*GetRecallSourceDetailsResponse, error)
	// 新建召回源
	CreateRecallSource(ctx context.Context, in *CreateRecallSourceRequest, opts ...grpc.CallOption) (*CreateRecallSourceResponse, error)
	// 编辑召回源
	UpdateRecallSource(ctx context.Context, in *UpdateRecallSourceRequest, opts ...grpc.CallOption) (*UpdateRecallSourceResponse, error)
	// 删除召回源
	DeleteRecallSource(ctx context.Context, in *DeleteRecallSourceRequest, opts ...grpc.CallOption) (*DeleteRecallSourceResponse, error)
	// 获取召回源规则列表
	GetRecallSourceRuleList(ctx context.Context, in *GetRecallSourceRuleListRequest, opts ...grpc.CallOption) (*GetRecallSourceRuleListResponse, error)
	// 获取默认的排序参数表
	GetDefaultSortParameterTable(ctx context.Context, in *GetDefaultSortParameterTableRequest, opts ...grpc.CallOption) (*GetDefaultSortParameterTableResponse, error)
	// 更新默认的排序参数表
	UpdateDefaultSortParameterTable(ctx context.Context, in *UpdateDefaultSortParameterTableRequest, opts ...grpc.CallOption) (*UpdateDefaultSortParameterTableResponse, error)
	// 获取多级排序的规则列表
	GetRecallSourceQueueRule(ctx context.Context, in *GetRecallSourceQueueRuleRequest, opts ...grpc.CallOption) (*GetRecallSourceQueueRuleResponse, error)
}

type esportRcmdAdminServiceClient struct {
	cc *grpc.ClientConn
}

func NewEsportRcmdAdminServiceClient(cc *grpc.ClientConn) EsportRcmdAdminServiceClient {
	return &esportRcmdAdminServiceClient{cc}
}

func (c *esportRcmdAdminServiceClient) ListStrategies(ctx context.Context, in *ListStrategiesRequest, opts ...grpc.CallOption) (*ListStrategiesResponse, error) {
	out := new(ListStrategiesResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.EsportRcmdAdminService/ListStrategies", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdAdminServiceClient) GetStrategy(ctx context.Context, in *GetStrategyRequest, opts ...grpc.CallOption) (*GetStrategyResponse, error) {
	out := new(GetStrategyResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.EsportRcmdAdminService/GetStrategy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdAdminServiceClient) CreateStrategy(ctx context.Context, in *CreateStrategyRequest, opts ...grpc.CallOption) (*CreateStrategyResponse, error) {
	out := new(CreateStrategyResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.EsportRcmdAdminService/CreateStrategy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdAdminServiceClient) UpdateStrategy(ctx context.Context, in *UpdateStrategyRequest, opts ...grpc.CallOption) (*UpdateStrategyResponse, error) {
	out := new(UpdateStrategyResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.EsportRcmdAdminService/UpdateStrategy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdAdminServiceClient) PauseStrategy(ctx context.Context, in *PauseStrategyRequest, opts ...grpc.CallOption) (*PauseStrategyResponse, error) {
	out := new(PauseStrategyResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.EsportRcmdAdminService/PauseStrategy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdAdminServiceClient) StartStrategy(ctx context.Context, in *StartStrategyRequest, opts ...grpc.CallOption) (*StartStrategyResponse, error) {
	out := new(StartStrategyResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.EsportRcmdAdminService/StartStrategy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdAdminServiceClient) DeleteStrategy(ctx context.Context, in *DeleteStrategyRequest, opts ...grpc.CallOption) (*DeleteStrategyResponse, error) {
	out := new(DeleteStrategyResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.EsportRcmdAdminService/DeleteStrategy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdAdminServiceClient) GetFilterRules(ctx context.Context, in *GetFilterRulesRequest, opts ...grpc.CallOption) (*GetFilterRulesResponse, error) {
	out := new(GetFilterRulesResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.EsportRcmdAdminService/GetFilterRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdAdminServiceClient) GetScenes(ctx context.Context, in *GetScenesRequest, opts ...grpc.CallOption) (*GetScenesResponse, error) {
	out := new(GetScenesResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.EsportRcmdAdminService/GetScenes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdAdminServiceClient) ListRecallSources(ctx context.Context, in *ListRecallSourcesRequest, opts ...grpc.CallOption) (*ListRecallSourcesResponse, error) {
	out := new(ListRecallSourcesResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.EsportRcmdAdminService/ListRecallSources", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdAdminServiceClient) GetRecallSourceDetails(ctx context.Context, in *GetRecallSourceDetailsRequest, opts ...grpc.CallOption) (*GetRecallSourceDetailsResponse, error) {
	out := new(GetRecallSourceDetailsResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.EsportRcmdAdminService/GetRecallSourceDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdAdminServiceClient) CreateRecallSource(ctx context.Context, in *CreateRecallSourceRequest, opts ...grpc.CallOption) (*CreateRecallSourceResponse, error) {
	out := new(CreateRecallSourceResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.EsportRcmdAdminService/CreateRecallSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdAdminServiceClient) UpdateRecallSource(ctx context.Context, in *UpdateRecallSourceRequest, opts ...grpc.CallOption) (*UpdateRecallSourceResponse, error) {
	out := new(UpdateRecallSourceResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.EsportRcmdAdminService/UpdateRecallSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdAdminServiceClient) DeleteRecallSource(ctx context.Context, in *DeleteRecallSourceRequest, opts ...grpc.CallOption) (*DeleteRecallSourceResponse, error) {
	out := new(DeleteRecallSourceResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.EsportRcmdAdminService/DeleteRecallSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdAdminServiceClient) GetRecallSourceRuleList(ctx context.Context, in *GetRecallSourceRuleListRequest, opts ...grpc.CallOption) (*GetRecallSourceRuleListResponse, error) {
	out := new(GetRecallSourceRuleListResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.EsportRcmdAdminService/GetRecallSourceRuleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdAdminServiceClient) GetDefaultSortParameterTable(ctx context.Context, in *GetDefaultSortParameterTableRequest, opts ...grpc.CallOption) (*GetDefaultSortParameterTableResponse, error) {
	out := new(GetDefaultSortParameterTableResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.EsportRcmdAdminService/GetDefaultSortParameterTable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdAdminServiceClient) UpdateDefaultSortParameterTable(ctx context.Context, in *UpdateDefaultSortParameterTableRequest, opts ...grpc.CallOption) (*UpdateDefaultSortParameterTableResponse, error) {
	out := new(UpdateDefaultSortParameterTableResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.EsportRcmdAdminService/UpdateDefaultSortParameterTable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdAdminServiceClient) GetRecallSourceQueueRule(ctx context.Context, in *GetRecallSourceQueueRuleRequest, opts ...grpc.CallOption) (*GetRecallSourceQueueRuleResponse, error) {
	out := new(GetRecallSourceQueueRuleResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.EsportRcmdAdminService/GetRecallSourceQueueRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EsportRcmdAdminServiceServer is the server API for EsportRcmdAdminService service.
type EsportRcmdAdminServiceServer interface {
	// 列出策略列表
	ListStrategies(context.Context, *ListStrategiesRequest) (*ListStrategiesResponse, error)
	// 获取特定策略的详细信息
	GetStrategy(context.Context, *GetStrategyRequest) (*GetStrategyResponse, error)
	// 创建新策略
	CreateStrategy(context.Context, *CreateStrategyRequest) (*CreateStrategyResponse, error)
	// 更新现有策略
	UpdateStrategy(context.Context, *UpdateStrategyRequest) (*UpdateStrategyResponse, error)
	// 暂停策略
	PauseStrategy(context.Context, *PauseStrategyRequest) (*PauseStrategyResponse, error)
	// 启动策略
	StartStrategy(context.Context, *StartStrategyRequest) (*StartStrategyResponse, error)
	// 删除策略
	DeleteStrategy(context.Context, *DeleteStrategyRequest) (*DeleteStrategyResponse, error)
	// 获取过滤规则
	GetFilterRules(context.Context, *GetFilterRulesRequest) (*GetFilterRulesResponse, error)
	// 获取场景列表
	GetScenes(context.Context, *GetScenesRequest) (*GetScenesResponse, error)
	// 列出所有召回源
	ListRecallSources(context.Context, *ListRecallSourcesRequest) (*ListRecallSourcesResponse, error)
	// 获取某个召回源的详细信息
	GetRecallSourceDetails(context.Context, *GetRecallSourceDetailsRequest) (*GetRecallSourceDetailsResponse, error)
	// 新建召回源
	CreateRecallSource(context.Context, *CreateRecallSourceRequest) (*CreateRecallSourceResponse, error)
	// 编辑召回源
	UpdateRecallSource(context.Context, *UpdateRecallSourceRequest) (*UpdateRecallSourceResponse, error)
	// 删除召回源
	DeleteRecallSource(context.Context, *DeleteRecallSourceRequest) (*DeleteRecallSourceResponse, error)
	// 获取召回源规则列表
	GetRecallSourceRuleList(context.Context, *GetRecallSourceRuleListRequest) (*GetRecallSourceRuleListResponse, error)
	// 获取默认的排序参数表
	GetDefaultSortParameterTable(context.Context, *GetDefaultSortParameterTableRequest) (*GetDefaultSortParameterTableResponse, error)
	// 更新默认的排序参数表
	UpdateDefaultSortParameterTable(context.Context, *UpdateDefaultSortParameterTableRequest) (*UpdateDefaultSortParameterTableResponse, error)
	// 获取多级排序的规则列表
	GetRecallSourceQueueRule(context.Context, *GetRecallSourceQueueRuleRequest) (*GetRecallSourceQueueRuleResponse, error)
}

func RegisterEsportRcmdAdminServiceServer(s *grpc.Server, srv EsportRcmdAdminServiceServer) {
	s.RegisterService(&_EsportRcmdAdminService_serviceDesc, srv)
}

func _EsportRcmdAdminService_ListStrategies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListStrategiesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdAdminServiceServer).ListStrategies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.EsportRcmdAdminService/ListStrategies",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdAdminServiceServer).ListStrategies(ctx, req.(*ListStrategiesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdAdminService_GetStrategy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStrategyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdAdminServiceServer).GetStrategy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.EsportRcmdAdminService/GetStrategy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdAdminServiceServer).GetStrategy(ctx, req.(*GetStrategyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdAdminService_CreateStrategy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateStrategyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdAdminServiceServer).CreateStrategy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.EsportRcmdAdminService/CreateStrategy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdAdminServiceServer).CreateStrategy(ctx, req.(*CreateStrategyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdAdminService_UpdateStrategy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStrategyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdAdminServiceServer).UpdateStrategy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.EsportRcmdAdminService/UpdateStrategy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdAdminServiceServer).UpdateStrategy(ctx, req.(*UpdateStrategyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdAdminService_PauseStrategy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PauseStrategyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdAdminServiceServer).PauseStrategy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.EsportRcmdAdminService/PauseStrategy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdAdminServiceServer).PauseStrategy(ctx, req.(*PauseStrategyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdAdminService_StartStrategy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartStrategyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdAdminServiceServer).StartStrategy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.EsportRcmdAdminService/StartStrategy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdAdminServiceServer).StartStrategy(ctx, req.(*StartStrategyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdAdminService_DeleteStrategy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteStrategyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdAdminServiceServer).DeleteStrategy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.EsportRcmdAdminService/DeleteStrategy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdAdminServiceServer).DeleteStrategy(ctx, req.(*DeleteStrategyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdAdminService_GetFilterRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFilterRulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdAdminServiceServer).GetFilterRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.EsportRcmdAdminService/GetFilterRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdAdminServiceServer).GetFilterRules(ctx, req.(*GetFilterRulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdAdminService_GetScenes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScenesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdAdminServiceServer).GetScenes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.EsportRcmdAdminService/GetScenes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdAdminServiceServer).GetScenes(ctx, req.(*GetScenesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdAdminService_ListRecallSources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRecallSourcesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdAdminServiceServer).ListRecallSources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.EsportRcmdAdminService/ListRecallSources",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdAdminServiceServer).ListRecallSources(ctx, req.(*ListRecallSourcesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdAdminService_GetRecallSourceDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecallSourceDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdAdminServiceServer).GetRecallSourceDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.EsportRcmdAdminService/GetRecallSourceDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdAdminServiceServer).GetRecallSourceDetails(ctx, req.(*GetRecallSourceDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdAdminService_CreateRecallSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRecallSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdAdminServiceServer).CreateRecallSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.EsportRcmdAdminService/CreateRecallSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdAdminServiceServer).CreateRecallSource(ctx, req.(*CreateRecallSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdAdminService_UpdateRecallSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRecallSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdAdminServiceServer).UpdateRecallSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.EsportRcmdAdminService/UpdateRecallSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdAdminServiceServer).UpdateRecallSource(ctx, req.(*UpdateRecallSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdAdminService_DeleteRecallSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRecallSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdAdminServiceServer).DeleteRecallSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.EsportRcmdAdminService/DeleteRecallSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdAdminServiceServer).DeleteRecallSource(ctx, req.(*DeleteRecallSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdAdminService_GetRecallSourceRuleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecallSourceRuleListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdAdminServiceServer).GetRecallSourceRuleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.EsportRcmdAdminService/GetRecallSourceRuleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdAdminServiceServer).GetRecallSourceRuleList(ctx, req.(*GetRecallSourceRuleListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdAdminService_GetDefaultSortParameterTable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDefaultSortParameterTableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdAdminServiceServer).GetDefaultSortParameterTable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.EsportRcmdAdminService/GetDefaultSortParameterTable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdAdminServiceServer).GetDefaultSortParameterTable(ctx, req.(*GetDefaultSortParameterTableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdAdminService_UpdateDefaultSortParameterTable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDefaultSortParameterTableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdAdminServiceServer).UpdateDefaultSortParameterTable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.EsportRcmdAdminService/UpdateDefaultSortParameterTable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdAdminServiceServer).UpdateDefaultSortParameterTable(ctx, req.(*UpdateDefaultSortParameterTableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdAdminService_GetRecallSourceQueueRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecallSourceQueueRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdAdminServiceServer).GetRecallSourceQueueRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.EsportRcmdAdminService/GetRecallSourceQueueRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdAdminServiceServer).GetRecallSourceQueueRule(ctx, req.(*GetRecallSourceQueueRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _EsportRcmdAdminService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "esport_admin_logic.EsportRcmdAdminService",
	HandlerType: (*EsportRcmdAdminServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListStrategies",
			Handler:    _EsportRcmdAdminService_ListStrategies_Handler,
		},
		{
			MethodName: "GetStrategy",
			Handler:    _EsportRcmdAdminService_GetStrategy_Handler,
		},
		{
			MethodName: "CreateStrategy",
			Handler:    _EsportRcmdAdminService_CreateStrategy_Handler,
		},
		{
			MethodName: "UpdateStrategy",
			Handler:    _EsportRcmdAdminService_UpdateStrategy_Handler,
		},
		{
			MethodName: "PauseStrategy",
			Handler:    _EsportRcmdAdminService_PauseStrategy_Handler,
		},
		{
			MethodName: "StartStrategy",
			Handler:    _EsportRcmdAdminService_StartStrategy_Handler,
		},
		{
			MethodName: "DeleteStrategy",
			Handler:    _EsportRcmdAdminService_DeleteStrategy_Handler,
		},
		{
			MethodName: "GetFilterRules",
			Handler:    _EsportRcmdAdminService_GetFilterRules_Handler,
		},
		{
			MethodName: "GetScenes",
			Handler:    _EsportRcmdAdminService_GetScenes_Handler,
		},
		{
			MethodName: "ListRecallSources",
			Handler:    _EsportRcmdAdminService_ListRecallSources_Handler,
		},
		{
			MethodName: "GetRecallSourceDetails",
			Handler:    _EsportRcmdAdminService_GetRecallSourceDetails_Handler,
		},
		{
			MethodName: "CreateRecallSource",
			Handler:    _EsportRcmdAdminService_CreateRecallSource_Handler,
		},
		{
			MethodName: "UpdateRecallSource",
			Handler:    _EsportRcmdAdminService_UpdateRecallSource_Handler,
		},
		{
			MethodName: "DeleteRecallSource",
			Handler:    _EsportRcmdAdminService_DeleteRecallSource_Handler,
		},
		{
			MethodName: "GetRecallSourceRuleList",
			Handler:    _EsportRcmdAdminService_GetRecallSourceRuleList_Handler,
		},
		{
			MethodName: "GetDefaultSortParameterTable",
			Handler:    _EsportRcmdAdminService_GetDefaultSortParameterTable_Handler,
		},
		{
			MethodName: "UpdateDefaultSortParameterTable",
			Handler:    _EsportRcmdAdminService_UpdateDefaultSortParameterTable_Handler,
		},
		{
			MethodName: "GetRecallSourceQueueRule",
			Handler:    _EsportRcmdAdminService_GetRecallSourceQueueRule_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "esport-admin-logic/rcmd.proto",
}

func init() {
	proto.RegisterFile("esport-admin-logic/rcmd.proto", fileDescriptor_rcmd_878064ef3ea88777)
}

var fileDescriptor_rcmd_878064ef3ea88777 = []byte{
	// 2082 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x59, 0x71, 0x6f, 0xdb, 0xc6,
	0x15, 0x2f, 0x25, 0xdb, 0x91, 0x9f, 0x2c, 0x45, 0xbe, 0x5a, 0x36, 0xcd, 0x38, 0xb1, 0xc6, 0x38,
	0x89, 0x9c, 0xce, 0x72, 0xe7, 0x26, 0x5d, 0x90, 0xad, 0xd8, 0xe4, 0x58, 0x11, 0xd4, 0x35, 0xb2,
	0x42, 0x4a, 0x5b, 0xbb, 0x6e, 0x25, 0x68, 0xe9, 0xa2, 0x10, 0xa0, 0x44, 0x85, 0x77, 0x0a, 0xe6,
	0x60, 0xc0, 0x86, 0x02, 0x05, 0xfa, 0xef, 0xd0, 0xff, 0xf6, 0x11, 0xf6, 0x15, 0xf6, 0x15, 0x86,
	0x7d, 0x93, 0x7d, 0x87, 0x81, 0xc7, 0x13, 0x45, 0x52, 0x47, 0x8b, 0x0e, 0xda, 0xff, 0xcc, 0xbb,
	0xf7, 0xde, 0xef, 0xbd, 0x7b, 0xef, 0x77, 0xfc, 0x51, 0x86, 0xdb, 0x98, 0x4c, 0x1c, 0x97, 0x1e,
	0x99, 0x83, 0x91, 0x35, 0x3e, 0xb2, 0x9d, 0xa1, 0xd5, 0x3f, 0x76, 0xfb, 0xa3, 0x41, 0x6d, 0xe2,
	0x3a, 0xd4, 0x41, 0xc8, 0xdf, 0x36, 0xd8, 0xb6, 0xc1, 0xb6, 0xd5, 0x7f, 0xdd, 0x80, 0x9c, 0x4e,
	0x5d, 0x93, 0xe2, 0xe1, 0x25, 0x2a, 0x42, 0xc6, 0x1a, 0xc8, 0x52, 0x45, 0xaa, 0x16, 0xb4, 0x8c,
	0x35, 0x40, 0xb7, 0x01, 0x48, 0x1f, 0x8f, 0xb1, 0x41, 0x2f, 0x27, 0x58, 0xce, 0xb0, 0xf5, 0x75,
	0xb6, 0xd2, 0xbd, 0x9c, 0x60, 0x84, 0x60, 0x65, 0x6c, 0x8e, 0xb0, 0x9c, 0xad, 0x48, 0xd5, 0x75,
	0x8d, 0xfd, 0x8d, 0x14, 0xc8, 0x4d, 0x5c, 0xcb, 0x71, 0x2d, 0x7a, 0x29, 0xaf, 0x30, 0x87, 0xe0,
	0x19, 0x3d, 0x85, 0x35, 0x42, 0x4d, 0x3a, 0x25, 0xf2, 0x6a, 0x45, 0xaa, 0x16, 0x4f, 0xd4, 0xda,
	0x62, 0x42, 0xb5, 0x59, 0x32, 0x3a, 0xb3, 0xd4, 0xb8, 0x07, 0xba, 0x0b, 0x05, 0xdb, 0x24, 0xd4,
	0x70, 0x26, 0xd8, 0x35, 0xa9, 0xe3, 0xca, 0x6b, 0x0c, 0x74, 0xc3, 0x5b, 0x3c, 0xe7, 0x6b, 0xa8,
	0x06, 0x1f, 0x86, 0x8c, 0x2c, 0x67, 0x6c, 0x50, 0x6b, 0x84, 0xe5, 0x1b, 0x15, 0xa9, 0x9a, 0xd5,
	0x36, 0xe7, 0xa6, 0x96, 0x33, 0xee, 0x5a, 0x23, 0x8c, 0x4e, 0x61, 0x63, 0x80, 0x6d, 0x6b, 0x64,
	0x51, 0xbf, 0xc2, 0x1c, 0x4b, 0x6b, 0x5f, 0x94, 0xd6, 0x99, 0x6f, 0xe7, 0xd5, 0xad, 0xe5, 0x07,
	0xf3, 0x07, 0xa4, 0xc1, 0x4d, 0xf3, 0xc2, 0xa0, 0x98, 0x50, 0x83, 0x60, 0x4a, 0xad, 0xf1, 0x50,
	0x5e, 0xaf, 0x48, 0xd5, 0xfc, 0xc9, 0xc3, 0xab, 0xaa, 0xab, 0xd5, 0x2f, 0xba, 0x98, 0x50, 0xdd,
	0xf7, 0xd0, 0x0a, 0x66, 0xf8, 0x11, 0x7d, 0x0d, 0x68, 0x4a, 0xb0, 0x6b, 0x0c, 0x5d, 0x67, 0x3a,
	0x09, 0xc2, 0x02, 0x0b, 0x7b, 0x74, 0x65, 0xd8, 0x1e, 0xc1, 0x6e, 0xd3, 0xf3, 0x9a, 0x45, 0x2e,
	0x4d, 0x63, 0x2b, 0x48, 0x85, 0x02, 0xcb, 0xd6, 0xb6, 0x08, 0x35, 0xac, 0x01, 0x91, 0xb7, 0x2a,
	0xd9, 0x6a, 0x56, 0xcb, 0x7b, 0x8b, 0x5f, 0x58, 0x84, 0xb6, 0x06, 0x04, 0x75, 0xa0, 0xe8, 0xe2,
	0xbe, 0x69, 0xdb, 0x06, 0x71, 0xa6, 0x6e, 0x1f, 0x13, 0xb9, 0x5c, 0xc9, 0x56, 0xf3, 0x27, 0x87,
	0x57, 0x82, 0x6b, 0xcc, 0x45, 0x67, 0x1e, 0x5a, 0xc1, 0x0d, 0x3d, 0x11, 0xf4, 0x29, 0xec, 0x8c,
	0xac, 0xb1, 0x35, 0x9a, 0x8e, 0x0c, 0x1e, 0xf9, 0xcd, 0xd4, 0x1c, 0x53, 0x6f, 0x4c, 0xb6, 0x2b,
	0x52, 0x75, 0x55, 0x2b, 0xf3, 0x6d, 0x3f, 0xc8, 0x4b, 0xbe, 0xa9, 0x4c, 0x60, 0x23, 0x1c, 0x36,
	0x34, 0xa2, 0x59, 0x36, 0xa2, 0xf7, 0xa0, 0x48, 0x1d, 0x6a, 0x86, 0xc2, 0x65, 0x58, 0xb8, 0x02,
	0x5b, 0x9d, 0x85, 0x41, 0x87, 0x50, 0x9a, 0xc1, 0x07, 0x86, 0x59, 0x66, 0x78, 0x93, 0xaf, 0x07,
	0x88, 0x06, 0x14, 0x22, 0xcd, 0xf1, 0x20, 0xf0, 0x5f, 0x26, 0xd8, 0xb5, 0x46, 0x78, 0x4c, 0x0d,
	0x6a, 0x0e, 0x19, 0xfc, 0xba, 0x56, 0x98, 0xaf, 0x76, 0xcd, 0x21, 0x7a, 0x00, 0x37, 0x43, 0x66,
	0xa6, 0x3b, 0x24, 0x72, 0xa6, 0x92, 0xad, 0xae, 0x6b, 0x21, 0xef, 0xba, 0x3b, 0x24, 0xca, 0x13,
	0x28, 0xc5, 0xdb, 0x84, 0x0e, 0xa0, 0x18, 0xea, 0xb8, 0xd7, 0x15, 0x89, 0x75, 0x65, 0x23, 0x68,
	0x5f, 0x6b, 0x40, 0xd4, 0x8f, 0x01, 0x9e, 0x5b, 0x36, 0xc5, 0xae, 0x36, 0xb5, 0xf1, 0x02, 0x5b,
	0x67, 0x74, 0xcc, 0xcc, 0xe9, 0xa8, 0xbe, 0x82, 0x55, 0xdd, 0xe3, 0xab, 0xb7, 0xc9, 0x46, 0xdc,
	0x37, 0x67, 0x7f, 0x8b, 0x1c, 0xd0, 0x63, 0xc8, 0xf5, 0x5f, 0x5b, 0xf6, 0xc0, 0xc5, 0x63, 0x39,
	0xcb, 0x7a, 0xbe, 0x2b, 0xec, 0xb9, 0x17, 0x54, 0x0b, 0x4c, 0xd5, 0x1f, 0x56, 0x13, 0xfb, 0x94,
	0x98, 0xdc, 0x22, 0xa7, 0xb3, 0xe9, 0x39, 0xbd, 0x92, 0xc4, 0xe9, 0x36, 0x6c, 0xb8, 0x53, 0x1b,
	0x07, 0xac, 0x59, 0x65, 0xac, 0xf9, 0x48, 0x54, 0x44, 0x64, 0x5e, 0xa7, 0x36, 0x9e, 0x71, 0x26,
	0xef, 0xce, 0x1f, 0x3c, 0x7c, 0xe2, 0x39, 0x4e, 0x4c, 0xd7, 0x1c, 0x61, 0x8a, 0x5d, 0xff, 0xaa,
	0x58, 0x63, 0x95, 0x6d, 0x7a, 0x5b, 0x9d, 0xd9, 0x0e, 0xbb, 0x0f, 0x4e, 0x21, 0xd7, 0x9f, 0x12,
	0xea, 0x8c, 0xb0, 0xcb, 0x2e, 0x9e, 0xfc, 0xc9, 0x7d, 0xe1, 0x01, 0x46, 0x1c, 0xcd, 0x0b, 0xdb,
	0x3b, 0x4d, 0xee, 0x87, 0x8e, 0x61, 0xeb, 0xcd, 0x14, 0x4f, 0xb1, 0xc1, 0x2a, 0xf1, 0xf0, 0x18,
	0x5b, 0x65, 0xa8, 0x64, 0x3d, 0x50, 0xb6, 0xe7, 0x25, 0xec, 0x01, 0x7a, 0x94, 0x45, 0xdf, 0x00,
	0x7a, 0xc5, 0x06, 0xc3, 0xf7, 0x78, 0x6b, 0xda, 0x53, 0x4c, 0xe4, 0x3c, 0xeb, 0xdf, 0xc7, 0xcb,
	0x4a, 0xaf, 0xcd, 0x67, 0xea, 0xf7, 0x9e, 0xa3, 0x56, 0x7a, 0x15, 0x5d, 0x20, 0xca, 0x67, 0x70,
	0x33, 0x66, 0xb4, 0xd0, 0x60, 0x05, 0x72, 0x31, 0x0a, 0x06, 0xcf, 0xea, 0x5b, 0xd8, 0xd4, 0x17,
	0x0e, 0x6a, 0x1f, 0x6e, 0xe9, 0xe7, 0x5a, 0xd7, 0xe8, 0xd4, 0xb5, 0xfa, 0x8b, 0x46, 0xb7, 0xa1,
	0x19, 0xdd, 0xaf, 0x3a, 0x0d, 0xa3, 0xd7, 0xfe, 0x5d, 0xfb, 0xfc, 0x0f, 0xed, 0xd2, 0x07, 0xa8,
	0x02, 0x7b, 0x22, 0x83, 0x67, 0x3d, 0xbd, 0x7b, 0xfe, 0xa2, 0xa1, 0x95, 0x24, 0x74, 0x07, 0x14,
	0x91, 0x85, 0x56, 0x6f, 0x9f, 0x9d, 0xbf, 0x28, 0x65, 0xd4, 0xff, 0x66, 0x60, 0x27, 0xa1, 0xc9,
	0xe8, 0x73, 0xc8, 0xcd, 0xe6, 0x84, 0x71, 0x2d, 0x7f, 0x72, 0x7c, 0x8d, 0x19, 0xa9, 0xe9, 0x98,
	0x6a, 0x37, 0xf8, 0x9c, 0x28, 0xdf, 0x4b, 0xb0, 0xfe, 0xcc, 0x19, 0x0f, 0x2c, 0x6f, 0x0a, 0xd1,
	0x2d, 0x58, 0x0f, 0xfa, 0xc6, 0x0f, 0x88, 0x41, 0xb1, 0xaa, 0xb7, 0x60, 0x95, 0x75, 0x87, 0x13,
	0xc1, 0x7f, 0xf0, 0x98, 0x30, 0x23, 0x81, 0xef, 0x96, 0x65, 0x6e, 0x1b, 0xb3, 0x45, 0xe6, 0x7a,
	0x17, 0x0a, 0x04, 0xdb, 0xb8, 0x4f, 0xf1, 0xc0, 0x98, 0x98, 0xf4, 0xb5, 0xbc, 0xc2, 0xc6, 0x61,
	0x63, 0xb6, 0xd8, 0x31, 0xe9, 0x6b, 0xe5, 0x4f, 0x90, 0xd5, 0x31, 0x45, 0x3d, 0x80, 0xfe, 0x2c,
	0x21, 0xc2, 0xeb, 0x7b, 0x7c, 0x9d, 0xfa, 0x82, 0x72, 0xb4, 0x50, 0x20, 0xf5, 0x87, 0x0c, 0x94,
	0xe2, 0x1e, 0xa9, 0xaf, 0x96, 0x16, 0xe4, 0x42, 0x4c, 0xcf, 0x26, 0xbd, 0xcb, 0xe2, 0xf1, 0x6b,
	0xb3, 0xab, 0x40, 0x0b, 0xdc, 0xd1, 0x6f, 0x43, 0xb7, 0xd4, 0x0a, 0x0b, 0x75, 0x90, 0x26, 0xd4,
	0xfc, 0xc2, 0x52, 0x3e, 0x87, 0x5c, 0x70, 0xc5, 0xa4, 0x2d, 0x60, 0x1b, 0xd6, 0x38, 0xb3, 0xb2,
	0xec, 0x62, 0xe7, 0x4f, 0xea, 0xff, 0x32, 0x80, 0x16, 0xf9, 0x8c, 0xba, 0x00, 0xc1, 0xa5, 0x31,
	0xeb, 0xc1, 0xa3, 0x74, 0x77, 0x41, 0x74, 0x49, 0x0b, 0xc5, 0x51, 0x4e, 0x60, 0xa5, 0x45, 0xf1,
	0x68, 0x3e, 0x48, 0x52, 0x78, 0x90, 0xb6, 0x60, 0x95, 0xf4, 0x1d, 0x17, 0x73, 0x0a, 0xfa, 0x0f,
	0xca, 0x7f, 0x24, 0x28, 0x44, 0x22, 0xa6, 0x2e, 0xb9, 0x0e, 0xab, 0x16, 0xc5, 0x23, 0xc2, 0x1b,
	0xf6, 0x51, 0xca, 0xf4, 0xbd, 0x0c, 0x35, 0xdf, 0x13, 0x75, 0x16, 0x7a, 0xf5, 0x7e, 0x87, 0x30,
	0x7f, 0xd9, 0xfc, 0x06, 0xca, 0xe1, 0xce, 0xbe, 0x9c, 0x5d, 0x87, 0x69, 0xab, 0x52, 0x7f, 0x06,
	0xfb, 0x4d, 0x4c, 0x85, 0x31, 0x34, 0xfc, 0x66, 0x8a, 0x09, 0x55, 0x4d, 0xa8, 0x24, 0x9b, 0x90,
	0x89, 0x33, 0x26, 0x18, 0x7d, 0x06, 0x2b, 0xec, 0x5a, 0x96, 0x92, 0xb5, 0x91, 0x38, 0x00, 0x73,
	0x53, 0xbf, 0x97, 0xa0, 0xec, 0xdd, 0xde, 0x5c, 0x3f, 0x59, 0x98, 0x70, 0xf0, 0x98, 0xee, 0x96,
	0x92, 0x74, 0x77, 0xb8, 0x51, 0xfb, 0x90, 0x9f, 0x98, 0x43, 0x6c, 0x8c, 0xa7, 0xa3, 0x0b, 0xec,
	0xf2, 0xfb, 0x03, 0xbc, 0xa5, 0x36, 0x5b, 0xf1, 0x6e, 0x25, 0x66, 0x40, 0xac, 0x77, 0x38, 0x50,
	0xe6, 0xe6, 0x10, 0xeb, 0xd6, 0x3b, 0xac, 0xda, 0xb0, 0x1d, 0xcf, 0x84, 0xd7, 0xf8, 0x6b, 0x00,
	0x12, 0xac, 0xf2, 0x4a, 0xf7, 0xae, 0x52, 0x81, 0x5a, 0xc8, 0xde, 0x1b, 0x47, 0xa6, 0xc3, 0xf8,
	0xb7, 0x83, 0xff, 0xa0, 0x1e, 0x00, 0x6a, 0x62, 0x1a, 0x38, 0xf0, 0xa2, 0x63, 0x2f, 0x14, 0xf5,
	0x1c, 0x3e, 0x8c, 0x58, 0xf1, 0x84, 0x9e, 0x40, 0x8e, 0x03, 0x5c, 0x32, 0xe3, 0x65, 0xe9, 0x04,
	0xd6, 0xea, 0x4b, 0x28, 0x3f, 0x73, 0xb1, 0x49, 0x71, 0x1c, 0xf9, 0xfd, 0x43, 0x56, 0x61, 0x3b,
	0x1e, 0x92, 0xa7, 0x19, 0xaf, 0xe6, 0x25, 0x94, 0x7b, 0x93, 0xc1, 0x8f, 0x0a, 0x2e, 0xc3, 0x76,
	0x3c, 0xa4, 0x0f, 0xae, 0xde, 0x87, 0xad, 0x8e, 0x39, 0x25, 0x78, 0xd9, 0x11, 0xef, 0x40, 0x39,
	0x66, 0x37, 0x0f, 0xa0, 0x53, 0xd3, 0xa5, 0x29, 0x02, 0xc4, 0xec, 0x78, 0x80, 0x07, 0x50, 0x3e,
	0xc3, 0x36, 0xa6, 0x4b, 0x53, 0x90, 0x61, 0x3b, 0x6e, 0xc8, 0x43, 0xec, 0x40, 0xb9, 0x89, 0xe9,
	0x5c, 0x76, 0xcc, 0xd8, 0xa1, 0x7e, 0x0d, 0xdb, 0xf1, 0x0d, 0x7e, 0xe8, 0x75, 0xd8, 0x08, 0xc9,
	0xa0, 0xd9, 0xb8, 0xde, 0x11, 0x9d, 0xe7, 0xdc, 0x5d, 0xcb, 0xcf, 0xe5, 0x0e, 0x51, 0x11, 0x94,
	0xbc, 0xa9, 0xf3, 0xb8, 0x16, 0x00, 0x9e, 0xc2, 0x66, 0x68, 0x8d, 0x63, 0x1d, 0x45, 0xc8, 0x7f,
	0x85, 0x48, 0xf6, 0xc9, 0xfe, 0x57, 0x90, 0x3d, 0x86, 0x85, 0xef, 0x83, 0x80, 0xee, 0x31, 0xee,
	0x4a, 0x57, 0x73, 0x37, 0x13, 0xe5, 0xae, 0xf0, 0x2b, 0xdc, 0x3f, 0xe5, 0x95, 0xe0, 0x94, 0xdf,
	0xc1, 0xae, 0x00, 0x9d, 0x57, 0xd2, 0x5c, 0xf8, 0xd8, 0xf3, 0x6b, 0xaa, 0x2c, 0x7d, 0xa5, 0xc6,
	0xbe, 0xf1, 0xc4, 0x6c, 0x6f, 0xc1, 0xed, 0xd8, 0x4d, 0x7a, 0x86, 0xa9, 0x69, 0xd9, 0x41, 0xf9,
	0x55, 0x28, 0x45, 0xf0, 0x8d, 0x60, 0x40, 0x8a, 0xe1, 0xf8, 0xad, 0x81, 0x3a, 0x84, 0x3b, 0x49,
	0xa1, 0x78, 0x2d, 0x0d, 0x28, 0x44, 0x62, 0x71, 0x4a, 0x2d, 0x2f, 0x65, 0x23, 0x0c, 0xa5, 0x5e,
	0xc0, 0xae, 0xcf, 0xeb, 0x88, 0x0d, 0xcf, 0xf7, 0x47, 0xc2, 0x78, 0x0e, 0x8a, 0x08, 0x83, 0x17,
	0x92, 0xfe, 0x50, 0x2e, 0x60, 0xd7, 0xbf, 0x06, 0x7e, 0xc2, 0x5c, 0xf7, 0x40, 0x11, 0x61, 0x70,
	0xa6, 0x36, 0x60, 0xd7, 0xe7, 0xb0, 0x28, 0x83, 0xf4, 0x85, 0xec, 0x81, 0x22, 0x0a, 0xc3, 0x41,
	0x2a, 0x0b, 0xbd, 0xf7, 0x08, 0xeb, 0x4f, 0xb5, 0x4f, 0xd3, 0x3f, 0x2f, 0xbc, 0xd5, 0xe7, 0x16,
	0xfc, 0x54, 0x9f, 0xc2, 0x6a, 0xf8, 0x66, 0x48, 0x27, 0x1a, 0x7d, 0x17, 0xf5, 0x1e, 0xdc, 0x6d,
	0x62, 0x7a, 0x86, 0x5f, 0x99, 0x53, 0x9b, 0x0a, 0x3e, 0xdf, 0x78, 0x16, 0x7f, 0x97, 0xe0, 0xe0,
	0x6a, 0x3b, 0x9e, 0xcb, 0x97, 0xb0, 0x15, 0xff, 0xb0, 0xf4, 0xf6, 0x79, 0x87, 0xd2, 0x7e, 0x34,
	0x22, 0xb2, 0xb0, 0xa6, 0x7e, 0x2b, 0xc1, 0x7d, 0xbf, 0x5d, 0xcb, 0xb2, 0xfd, 0x09, 0x93, 0x38,
	0x84, 0x07, 0x4b, 0x73, 0xf0, 0x4f, 0xe2, 0xe1, 0x77, 0x12, 0x14, 0xa3, 0x3f, 0xfb, 0xb1, 0x8f,
	0xc3, 0xae, 0x56, 0xef, 0x36, 0x9a, 0x5f, 0x19, 0x7a, 0xb7, 0xde, 0xed, 0xe9, 0x46, 0xaf, 0xad,
	0x77, 0x1a, 0xcf, 0x5a, 0xcf, 0x5b, 0x8d, 0xb3, 0xd2, 0x07, 0xe8, 0x16, 0xec, 0xc4, 0x0d, 0xb4,
	0x5e, 0xbb, 0xdd, 0x6a, 0x37, 0x4b, 0x12, 0x52, 0x60, 0x3b, 0xbe, 0xd9, 0xa9, 0xf7, 0xf4, 0xc6,
	0x59, 0x29, 0x83, 0x76, 0xa1, 0x1c, 0xdf, 0x3b, 0xd3, 0xea, 0xcf, 0xbb, 0xa5, 0xec, 0xc3, 0x0b,
	0xc8, 0x87, 0x7e, 0xe6, 0x43, 0x7b, 0x20, 0x9f, 0x35, 0xbe, 0x68, 0xbd, 0x68, 0x75, 0x67, 0x5f,
	0xa6, 0xe1, 0x04, 0x64, 0xd8, 0x8a, 0xec, 0xd6, 0x4f, 0x8d, 0x6e, 0x43, 0xef, 0x96, 0x24, 0x2f,
	0xb5, 0xa8, 0x9f, 0xde, 0xd0, 0x8c, 0xa6, 0x76, 0xde, 0xeb, 0x94, 0x32, 0x27, 0xff, 0x2e, 0xc1,
	0x76, 0x83, 0x1d, 0xaa, 0xd6, 0x1f, 0x0d, 0xea, 0xde, 0xb9, 0xea, 0xd8, 0x7d, 0x6b, 0xf5, 0x31,
	0x1a, 0x42, 0x31, 0x2a, 0xc2, 0x90, 0x50, 0x52, 0x0a, 0x25, 0xa3, 0xf2, 0x30, 0x8d, 0x29, 0x9f,
	0xbc, 0x6f, 0x20, 0x1f, 0x52, 0x56, 0x48, 0xd8, 0xe5, 0x45, 0x81, 0xa6, 0x3c, 0x58, 0x6a, 0xc7,
	0xe3, 0x0f, 0xa1, 0x18, 0x55, 0x45, 0xe2, 0x42, 0x84, 0x62, 0x4c, 0x5c, 0x48, 0x82, 0xc8, 0x1a,
	0x42, 0x31, 0xaa, 0x80, 0xc4, 0x40, 0x42, 0xe1, 0x25, 0x06, 0x12, 0x0b, 0x2a, 0x34, 0x80, 0x42,
	0x44, 0x28, 0xa1, 0xaa, 0xc8, 0x59, 0xa4, 0xb9, 0x94, 0xc3, 0x14, 0x96, 0x73, 0x94, 0x88, 0x9a,
	0x12, 0xa3, 0x88, 0x84, 0x99, 0x72, 0x98, 0xc2, 0x72, 0x7e, 0x68, 0x51, 0xc5, 0x25, 0x3e, 0x34,
	0xa1, 0x7c, 0x13, 0x1f, 0x9a, 0x58, 0xc0, 0x79, 0x40, 0x51, 0x9d, 0x26, 0x06, 0x12, 0x8a, 0x3c,
	0x31, 0x50, 0x82, 0xec, 0xfb, 0x12, 0xd6, 0x03, 0x7d, 0x86, 0x0e, 0x92, 0xa6, 0x34, 0x2c, 0xe9,
	0x94, 0x7b, 0x4b, 0xac, 0x78, 0xe4, 0x09, 0x6c, 0x2e, 0xe8, 0x26, 0xf4, 0xf3, 0x24, 0xaa, 0x89,
	0xc4, 0x9d, 0x72, 0x94, 0xd2, 0x9a, 0x23, 0xfe, 0x8d, 0x89, 0x5b, 0x81, 0xc4, 0x41, 0xbf, 0x48,
	0x48, 0x39, 0x59, 0x59, 0x29, 0x27, 0xd7, 0x71, 0xe1, 0x09, 0x10, 0x40, 0x8b, 0xb2, 0x04, 0x1d,
	0x25, 0xb3, 0x52, 0xf0, 0xd2, 0x57, 0x6a, 0x69, 0xcd, 0xe7, 0xa0, 0x8b, 0xfa, 0x42, 0x0c, 0x9a,
	0xa8, 0x75, 0xc4, 0xa0, 0xc9, 0xb2, 0xc5, 0x03, 0x5d, 0xd4, 0x1b, 0x62, 0xd0, 0x44, 0x79, 0x23,
	0x06, 0x4d, 0x96, 0x31, 0xe8, 0x5b, 0x09, 0x76, 0x12, 0x54, 0x0a, 0x4a, 0xd3, 0xae, 0x98, 0xe8,
	0x51, 0x3e, 0xb9, 0x96, 0x0f, 0x4f, 0xe2, 0x1f, 0x12, 0xec, 0x5d, 0xa5, 0x51, 0xd0, 0x2f, 0x13,
	0xa2, 0x2e, 0xd3, 0x13, 0xca, 0x93, 0xeb, 0x3b, 0xf2, 0x9c, 0xfe, 0x29, 0xc1, 0xfe, 0x12, 0xc1,
	0x80, 0x9e, 0x26, 0x77, 0x78, 0x69, 0x66, 0xbf, 0x7a, 0x2f, 0x5f, 0x9e, 0xdc, 0x77, 0x12, 0xc8,
	0x49, 0x3f, 0x07, 0xa1, 0x34, 0x2d, 0x88, 0xff, 0xbe, 0xa4, 0x3c, 0xba, 0x9e, 0x93, 0x9f, 0xc7,
	0xe9, 0xa7, 0x7f, 0x7c, 0x34, 0x74, 0x6c, 0x73, 0x3c, 0xac, 0x3d, 0x3e, 0xa1, 0xb4, 0xd6, 0x77,
	0x46, 0xc7, 0xec, 0x5f, 0xbb, 0x7d, 0xc7, 0x3e, 0x26, 0xbe, 0x8c, 0x20, 0xc7, 0x8b, 0x81, 0x2f,
	0xd6, 0x98, 0xd5, 0x27, 0xff, 0x0f, 0x00, 0x00, 0xff, 0xff, 0x8f, 0x0c, 0xd5, 0x8a, 0x21, 0x1e,
	0x00, 0x00,
}
