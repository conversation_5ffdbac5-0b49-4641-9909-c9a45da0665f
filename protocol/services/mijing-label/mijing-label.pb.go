// Code generated by protoc-gen-go. DO NOT EDIT.
// source: mijing-label/mijing-label.proto

package mijing_label // import "golang.52tt.com/protocol/services/mijing-label"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type EscapeUserLabelStatus int32

const (
	EscapeUserLabelStatus_EscapeUserLabelStatusNil        EscapeUserLabelStatus = 0
	EscapeUserLabelStatus_EscapeUserLabelStatusUnfinished EscapeUserLabelStatus = 100
	EscapeUserLabelStatus_EscapeUserLabelStatusFinished   EscapeUserLabelStatus = 1001
)

var EscapeUserLabelStatus_name = map[int32]string{
	0:    "EscapeUserLabelStatusNil",
	100:  "EscapeUserLabelStatusUnfinished",
	1001: "EscapeUserLabelStatusFinished",
}
var EscapeUserLabelStatus_value = map[string]int32{
	"EscapeUserLabelStatusNil":        0,
	"EscapeUserLabelStatusUnfinished": 100,
	"EscapeUserLabelStatusFinished":   1001,
}

func (x EscapeUserLabelStatus) String() string {
	return proto.EnumName(EscapeUserLabelStatus_name, int32(x))
}
func (EscapeUserLabelStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{0}
}

type EscapeUserLabel_SexOption int32

const (
	EscapeUserLabel_SexOptionNil     EscapeUserLabel_SexOption = 0
	EscapeUserLabel_SexOptionMale    EscapeUserLabel_SexOption = 1
	EscapeUserLabel_SexOptionFemale  EscapeUserLabel_SexOption = 2
	EscapeUserLabel_SexOptionUnknown EscapeUserLabel_SexOption = 3
)

var EscapeUserLabel_SexOption_name = map[int32]string{
	0: "SexOptionNil",
	1: "SexOptionMale",
	2: "SexOptionFemale",
	3: "SexOptionUnknown",
}
var EscapeUserLabel_SexOption_value = map[string]int32{
	"SexOptionNil":     0,
	"SexOptionMale":    1,
	"SexOptionFemale":  2,
	"SexOptionUnknown": 3,
}

func (x EscapeUserLabel_SexOption) String() string {
	return proto.EnumName(EscapeUserLabel_SexOption_name, int32(x))
}
func (EscapeUserLabel_SexOption) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{0, 0}
}

// 该选项是否选择
type EscapeUserLabelSurveyOption_OptionIsSelected int32

const (
	EscapeUserLabelSurveyOption_OptionIsSelectedNil   EscapeUserLabelSurveyOption_OptionIsSelected = 0
	EscapeUserLabelSurveyOption_OptionIsSelectedFalse EscapeUserLabelSurveyOption_OptionIsSelected = 1
	EscapeUserLabelSurveyOption_OptionIsSelectedTrue  EscapeUserLabelSurveyOption_OptionIsSelected = 2
)

var EscapeUserLabelSurveyOption_OptionIsSelected_name = map[int32]string{
	0: "OptionIsSelectedNil",
	1: "OptionIsSelectedFalse",
	2: "OptionIsSelectedTrue",
}
var EscapeUserLabelSurveyOption_OptionIsSelected_value = map[string]int32{
	"OptionIsSelectedNil":   0,
	"OptionIsSelectedFalse": 1,
	"OptionIsSelectedTrue":  2,
}

func (x EscapeUserLabelSurveyOption_OptionIsSelected) String() string {
	return proto.EnumName(EscapeUserLabelSurveyOption_OptionIsSelected_name, int32(x))
}
func (EscapeUserLabelSurveyOption_OptionIsSelected) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{9, 0}
}

// 性别选项（只有在 SpecialQuestion != SpecialQuestionNil 和 SpecialQuestionNornal 时才会用到）
type EscapeUserLabelSurveyOption_SexOption int32

const (
	EscapeUserLabelSurveyOption_SexOptionNil     EscapeUserLabelSurveyOption_SexOption = 0
	EscapeUserLabelSurveyOption_SexOptionMale    EscapeUserLabelSurveyOption_SexOption = 1
	EscapeUserLabelSurveyOption_SexOptionFemale  EscapeUserLabelSurveyOption_SexOption = 2
	EscapeUserLabelSurveyOption_SexOptionUnknown EscapeUserLabelSurveyOption_SexOption = 3
)

var EscapeUserLabelSurveyOption_SexOption_name = map[int32]string{
	0: "SexOptionNil",
	1: "SexOptionMale",
	2: "SexOptionFemale",
	3: "SexOptionUnknown",
}
var EscapeUserLabelSurveyOption_SexOption_value = map[string]int32{
	"SexOptionNil":     0,
	"SexOptionMale":    1,
	"SexOptionFemale":  2,
	"SexOptionUnknown": 3,
}

func (x EscapeUserLabelSurveyOption_SexOption) String() string {
	return proto.EnumName(EscapeUserLabelSurveyOption_SexOption_name, int32(x))
}
func (EscapeUserLabelSurveyOption_SexOption) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{9, 1}
}

// 题目类型
type EscapeUserLabelSurveyQuestion_QuestionType int32

const (
	EscapeUserLabelSurveyQuestion_QuestionTypeNil            EscapeUserLabelSurveyQuestion_QuestionType = 0
	EscapeUserLabelSurveyQuestion_QuestionTypeSingleChoice   EscapeUserLabelSurveyQuestion_QuestionType = 1
	EscapeUserLabelSurveyQuestion_QuestionTypeMultipleChoice EscapeUserLabelSurveyQuestion_QuestionType = 2
)

var EscapeUserLabelSurveyQuestion_QuestionType_name = map[int32]string{
	0: "QuestionTypeNil",
	1: "QuestionTypeSingleChoice",
	2: "QuestionTypeMultipleChoice",
}
var EscapeUserLabelSurveyQuestion_QuestionType_value = map[string]int32{
	"QuestionTypeNil":            0,
	"QuestionTypeSingleChoice":   1,
	"QuestionTypeMultipleChoice": 2,
}

func (x EscapeUserLabelSurveyQuestion_QuestionType) String() string {
	return proto.EnumName(EscapeUserLabelSurveyQuestion_QuestionType_name, int32(x))
}
func (EscapeUserLabelSurveyQuestion_QuestionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{10, 0}
}

// 特殊题目
type EscapeUserLabelSurveyQuestion_SpecialQuestion int32

const (
	EscapeUserLabelSurveyQuestion_SpecialQuestionNil       EscapeUserLabelSurveyQuestion_SpecialQuestion = 0
	EscapeUserLabelSurveyQuestion_SpecialQuestionNormal    EscapeUserLabelSurveyQuestion_SpecialQuestion = 1
	EscapeUserLabelSurveyQuestion_SpecialQuestionUserSex   EscapeUserLabelSurveyQuestion_SpecialQuestion = 2
	EscapeUserLabelSurveyQuestion_SpecialQuestionFriendSex EscapeUserLabelSurveyQuestion_SpecialQuestion = 3
)

var EscapeUserLabelSurveyQuestion_SpecialQuestion_name = map[int32]string{
	0: "SpecialQuestionNil",
	1: "SpecialQuestionNormal",
	2: "SpecialQuestionUserSex",
	3: "SpecialQuestionFriendSex",
}
var EscapeUserLabelSurveyQuestion_SpecialQuestion_value = map[string]int32{
	"SpecialQuestionNil":       0,
	"SpecialQuestionNormal":    1,
	"SpecialQuestionUserSex":   2,
	"SpecialQuestionFriendSex": 3,
}

func (x EscapeUserLabelSurveyQuestion_SpecialQuestion) String() string {
	return proto.EnumName(EscapeUserLabelSurveyQuestion_SpecialQuestion_name, int32(x))
}
func (EscapeUserLabelSurveyQuestion_SpecialQuestion) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{10, 1}
}

type GetEscapeUserLabelConfigResp_Switch int32

const (
	GetEscapeUserLabelConfigResp_SwitchNil GetEscapeUserLabelConfigResp_Switch = 0
	GetEscapeUserLabelConfigResp_SwitchOn  GetEscapeUserLabelConfigResp_Switch = 1
	GetEscapeUserLabelConfigResp_SwitchOff GetEscapeUserLabelConfigResp_Switch = 2
)

var GetEscapeUserLabelConfigResp_Switch_name = map[int32]string{
	0: "SwitchNil",
	1: "SwitchOn",
	2: "SwitchOff",
}
var GetEscapeUserLabelConfigResp_Switch_value = map[string]int32{
	"SwitchNil": 0,
	"SwitchOn":  1,
	"SwitchOff": 2,
}

func (x GetEscapeUserLabelConfigResp_Switch) String() string {
	return proto.EnumName(GetEscapeUserLabelConfigResp_Switch_name, int32(x))
}
func (GetEscapeUserLabelConfigResp_Switch) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{17, 0}
}

// 密逃用户标签
type EscapeUserLabel struct {
	Uid                  uint32                           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DimensionInfoList    []*EscapeUserLabel_DimensionInfo `protobuf:"bytes,2,rep,name=dimension_info_list,json=dimensionInfoList,proto3" json:"dimension_info_list,omitempty"`
	CharacterResult      string                           `protobuf:"bytes,3,opt,name=character_result,json=characterResult,proto3" json:"character_result,omitempty"`
	CharacterUserDesc    string                           `protobuf:"bytes,4,opt,name=character_user_desc,json=characterUserDesc,proto3" json:"character_user_desc,omitempty"`
	CharacterFriendDesc  string                           `protobuf:"bytes,5,opt,name=character_friend_desc,json=characterFriendDesc,proto3" json:"character_friend_desc,omitempty"`
	ScenarioInfoList     []*EscapeUserLabel_ScenarioInfo  `protobuf:"bytes,6,rep,name=scenario_info_list,json=scenarioInfoList,proto3" json:"scenario_info_list,omitempty"`
	LabelNameList        []string                         `protobuf:"bytes,7,rep,name=label_name_list,json=labelNameList,proto3" json:"label_name_list,omitempty"`
	AvatarInfo           *EscapeUserLabel_AvatarInfo      `protobuf:"bytes,8,opt,name=avatar_info,json=avatarInfo,proto3" json:"avatar_info,omitempty"`
	UserSexOption        EscapeUserLabel_SexOption        `protobuf:"varint,9,opt,name=user_sex_option,json=userSexOption,proto3,enum=mijing_label.EscapeUserLabel_SexOption" json:"user_sex_option,omitempty"`
	FriendSexOption      EscapeUserLabel_SexOption        `protobuf:"varint,10,opt,name=friend_sex_option,json=friendSexOption,proto3,enum=mijing_label.EscapeUserLabel_SexOption" json:"friend_sex_option,omitempty"`
	Status               EscapeUserLabelStatus            `protobuf:"varint,11,opt,name=status,proto3,enum=mijing_label.EscapeUserLabelStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *EscapeUserLabel) Reset()         { *m = EscapeUserLabel{} }
func (m *EscapeUserLabel) String() string { return proto.CompactTextString(m) }
func (*EscapeUserLabel) ProtoMessage()    {}
func (*EscapeUserLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{0}
}
func (m *EscapeUserLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EscapeUserLabel.Unmarshal(m, b)
}
func (m *EscapeUserLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EscapeUserLabel.Marshal(b, m, deterministic)
}
func (dst *EscapeUserLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EscapeUserLabel.Merge(dst, src)
}
func (m *EscapeUserLabel) XXX_Size() int {
	return xxx_messageInfo_EscapeUserLabel.Size(m)
}
func (m *EscapeUserLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_EscapeUserLabel.DiscardUnknown(m)
}

var xxx_messageInfo_EscapeUserLabel proto.InternalMessageInfo

func (m *EscapeUserLabel) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *EscapeUserLabel) GetDimensionInfoList() []*EscapeUserLabel_DimensionInfo {
	if m != nil {
		return m.DimensionInfoList
	}
	return nil
}

func (m *EscapeUserLabel) GetCharacterResult() string {
	if m != nil {
		return m.CharacterResult
	}
	return ""
}

func (m *EscapeUserLabel) GetCharacterUserDesc() string {
	if m != nil {
		return m.CharacterUserDesc
	}
	return ""
}

func (m *EscapeUserLabel) GetCharacterFriendDesc() string {
	if m != nil {
		return m.CharacterFriendDesc
	}
	return ""
}

func (m *EscapeUserLabel) GetScenarioInfoList() []*EscapeUserLabel_ScenarioInfo {
	if m != nil {
		return m.ScenarioInfoList
	}
	return nil
}

func (m *EscapeUserLabel) GetLabelNameList() []string {
	if m != nil {
		return m.LabelNameList
	}
	return nil
}

func (m *EscapeUserLabel) GetAvatarInfo() *EscapeUserLabel_AvatarInfo {
	if m != nil {
		return m.AvatarInfo
	}
	return nil
}

func (m *EscapeUserLabel) GetUserSexOption() EscapeUserLabel_SexOption {
	if m != nil {
		return m.UserSexOption
	}
	return EscapeUserLabel_SexOptionNil
}

func (m *EscapeUserLabel) GetFriendSexOption() EscapeUserLabel_SexOption {
	if m != nil {
		return m.FriendSexOption
	}
	return EscapeUserLabel_SexOptionNil
}

func (m *EscapeUserLabel) GetStatus() EscapeUserLabelStatus {
	if m != nil {
		return m.Status
	}
	return EscapeUserLabelStatus_EscapeUserLabelStatusNil
}

type EscapeUserLabel_DimensionInfo struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Score                uint32   `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EscapeUserLabel_DimensionInfo) Reset()         { *m = EscapeUserLabel_DimensionInfo{} }
func (m *EscapeUserLabel_DimensionInfo) String() string { return proto.CompactTextString(m) }
func (*EscapeUserLabel_DimensionInfo) ProtoMessage()    {}
func (*EscapeUserLabel_DimensionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{0, 0}
}
func (m *EscapeUserLabel_DimensionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EscapeUserLabel_DimensionInfo.Unmarshal(m, b)
}
func (m *EscapeUserLabel_DimensionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EscapeUserLabel_DimensionInfo.Marshal(b, m, deterministic)
}
func (dst *EscapeUserLabel_DimensionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EscapeUserLabel_DimensionInfo.Merge(dst, src)
}
func (m *EscapeUserLabel_DimensionInfo) XXX_Size() int {
	return xxx_messageInfo_EscapeUserLabel_DimensionInfo.Size(m)
}
func (m *EscapeUserLabel_DimensionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EscapeUserLabel_DimensionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EscapeUserLabel_DimensionInfo proto.InternalMessageInfo

func (m *EscapeUserLabel_DimensionInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *EscapeUserLabel_DimensionInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type EscapeUserLabel_ScenarioInfo struct {
	ScenarioId           uint32   `protobuf:"varint,1,opt,name=scenario_id,json=scenarioId,proto3" json:"scenario_id,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EscapeUserLabel_ScenarioInfo) Reset()         { *m = EscapeUserLabel_ScenarioInfo{} }
func (m *EscapeUserLabel_ScenarioInfo) String() string { return proto.CompactTextString(m) }
func (*EscapeUserLabel_ScenarioInfo) ProtoMessage()    {}
func (*EscapeUserLabel_ScenarioInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{0, 1}
}
func (m *EscapeUserLabel_ScenarioInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EscapeUserLabel_ScenarioInfo.Unmarshal(m, b)
}
func (m *EscapeUserLabel_ScenarioInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EscapeUserLabel_ScenarioInfo.Marshal(b, m, deterministic)
}
func (dst *EscapeUserLabel_ScenarioInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EscapeUserLabel_ScenarioInfo.Merge(dst, src)
}
func (m *EscapeUserLabel_ScenarioInfo) XXX_Size() int {
	return xxx_messageInfo_EscapeUserLabel_ScenarioInfo.Size(m)
}
func (m *EscapeUserLabel_ScenarioInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EscapeUserLabel_ScenarioInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EscapeUserLabel_ScenarioInfo proto.InternalMessageInfo

func (m *EscapeUserLabel_ScenarioInfo) GetScenarioId() uint32 {
	if m != nil {
		return m.ScenarioId
	}
	return 0
}

func (m *EscapeUserLabel_ScenarioInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type EscapeUserLabel_AvatarInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	StyleId              uint32   `protobuf:"varint,2,opt,name=style_id,json=styleId,proto3" json:"style_id,omitempty"`
	AvatarUrl            string   `protobuf:"bytes,3,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EscapeUserLabel_AvatarInfo) Reset()         { *m = EscapeUserLabel_AvatarInfo{} }
func (m *EscapeUserLabel_AvatarInfo) String() string { return proto.CompactTextString(m) }
func (*EscapeUserLabel_AvatarInfo) ProtoMessage()    {}
func (*EscapeUserLabel_AvatarInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{0, 2}
}
func (m *EscapeUserLabel_AvatarInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EscapeUserLabel_AvatarInfo.Unmarshal(m, b)
}
func (m *EscapeUserLabel_AvatarInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EscapeUserLabel_AvatarInfo.Marshal(b, m, deterministic)
}
func (dst *EscapeUserLabel_AvatarInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EscapeUserLabel_AvatarInfo.Merge(dst, src)
}
func (m *EscapeUserLabel_AvatarInfo) XXX_Size() int {
	return xxx_messageInfo_EscapeUserLabel_AvatarInfo.Size(m)
}
func (m *EscapeUserLabel_AvatarInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EscapeUserLabel_AvatarInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EscapeUserLabel_AvatarInfo proto.InternalMessageInfo

func (m *EscapeUserLabel_AvatarInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *EscapeUserLabel_AvatarInfo) GetStyleId() uint32 {
	if m != nil {
		return m.StyleId
	}
	return 0
}

func (m *EscapeUserLabel_AvatarInfo) GetAvatarUrl() string {
	if m != nil {
		return m.AvatarUrl
	}
	return ""
}

// 新增密逃用户标签
type AddEscapeUserLabelReq struct {
	UserLabelInfo        *EscapeUserLabel `protobuf:"bytes,1,opt,name=user_label_info,json=userLabelInfo,proto3" json:"user_label_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *AddEscapeUserLabelReq) Reset()         { *m = AddEscapeUserLabelReq{} }
func (m *AddEscapeUserLabelReq) String() string { return proto.CompactTextString(m) }
func (*AddEscapeUserLabelReq) ProtoMessage()    {}
func (*AddEscapeUserLabelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{1}
}
func (m *AddEscapeUserLabelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddEscapeUserLabelReq.Unmarshal(m, b)
}
func (m *AddEscapeUserLabelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddEscapeUserLabelReq.Marshal(b, m, deterministic)
}
func (dst *AddEscapeUserLabelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddEscapeUserLabelReq.Merge(dst, src)
}
func (m *AddEscapeUserLabelReq) XXX_Size() int {
	return xxx_messageInfo_AddEscapeUserLabelReq.Size(m)
}
func (m *AddEscapeUserLabelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddEscapeUserLabelReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddEscapeUserLabelReq proto.InternalMessageInfo

func (m *AddEscapeUserLabelReq) GetUserLabelInfo() *EscapeUserLabel {
	if m != nil {
		return m.UserLabelInfo
	}
	return nil
}

type AddEscapeUserLabelResp struct {
	UserLabelInfo        *EscapeUserLabel `protobuf:"bytes,1,opt,name=user_label_info,json=userLabelInfo,proto3" json:"user_label_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *AddEscapeUserLabelResp) Reset()         { *m = AddEscapeUserLabelResp{} }
func (m *AddEscapeUserLabelResp) String() string { return proto.CompactTextString(m) }
func (*AddEscapeUserLabelResp) ProtoMessage()    {}
func (*AddEscapeUserLabelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{2}
}
func (m *AddEscapeUserLabelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddEscapeUserLabelResp.Unmarshal(m, b)
}
func (m *AddEscapeUserLabelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddEscapeUserLabelResp.Marshal(b, m, deterministic)
}
func (dst *AddEscapeUserLabelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddEscapeUserLabelResp.Merge(dst, src)
}
func (m *AddEscapeUserLabelResp) XXX_Size() int {
	return xxx_messageInfo_AddEscapeUserLabelResp.Size(m)
}
func (m *AddEscapeUserLabelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddEscapeUserLabelResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddEscapeUserLabelResp proto.InternalMessageInfo

func (m *AddEscapeUserLabelResp) GetUserLabelInfo() *EscapeUserLabel {
	if m != nil {
		return m.UserLabelInfo
	}
	return nil
}

// 获取密逃用户标签列表
type GetEscapeUserLabelListReq struct {
	UidList              []uint32                `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	StatusList           []EscapeUserLabelStatus `protobuf:"varint,2,rep,packed,name=status_list,json=statusList,proto3,enum=mijing_label.EscapeUserLabelStatus" json:"status_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetEscapeUserLabelListReq) Reset()         { *m = GetEscapeUserLabelListReq{} }
func (m *GetEscapeUserLabelListReq) String() string { return proto.CompactTextString(m) }
func (*GetEscapeUserLabelListReq) ProtoMessage()    {}
func (*GetEscapeUserLabelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{3}
}
func (m *GetEscapeUserLabelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEscapeUserLabelListReq.Unmarshal(m, b)
}
func (m *GetEscapeUserLabelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEscapeUserLabelListReq.Marshal(b, m, deterministic)
}
func (dst *GetEscapeUserLabelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEscapeUserLabelListReq.Merge(dst, src)
}
func (m *GetEscapeUserLabelListReq) XXX_Size() int {
	return xxx_messageInfo_GetEscapeUserLabelListReq.Size(m)
}
func (m *GetEscapeUserLabelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEscapeUserLabelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEscapeUserLabelListReq proto.InternalMessageInfo

func (m *GetEscapeUserLabelListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetEscapeUserLabelListReq) GetStatusList() []EscapeUserLabelStatus {
	if m != nil {
		return m.StatusList
	}
	return nil
}

type GetEscapeUserLabelListResp struct {
	UserLabelInfoList    []*EscapeUserLabel `protobuf:"bytes,1,rep,name=user_label_info_list,json=userLabelInfoList,proto3" json:"user_label_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetEscapeUserLabelListResp) Reset()         { *m = GetEscapeUserLabelListResp{} }
func (m *GetEscapeUserLabelListResp) String() string { return proto.CompactTextString(m) }
func (*GetEscapeUserLabelListResp) ProtoMessage()    {}
func (*GetEscapeUserLabelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{4}
}
func (m *GetEscapeUserLabelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEscapeUserLabelListResp.Unmarshal(m, b)
}
func (m *GetEscapeUserLabelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEscapeUserLabelListResp.Marshal(b, m, deterministic)
}
func (dst *GetEscapeUserLabelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEscapeUserLabelListResp.Merge(dst, src)
}
func (m *GetEscapeUserLabelListResp) XXX_Size() int {
	return xxx_messageInfo_GetEscapeUserLabelListResp.Size(m)
}
func (m *GetEscapeUserLabelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEscapeUserLabelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEscapeUserLabelListResp proto.InternalMessageInfo

func (m *GetEscapeUserLabelListResp) GetUserLabelInfoList() []*EscapeUserLabel {
	if m != nil {
		return m.UserLabelInfoList
	}
	return nil
}

// 统计密逃用户标签每日被邀请数
type CountEscapeUserLabelDailyInviteeReq struct {
	InviteUid            uint32   `protobuf:"varint,1,opt,name=invite_uid,json=inviteUid,proto3" json:"invite_uid,omitempty"`
	InviteeUid           uint32   `protobuf:"varint,2,opt,name=invitee_uid,json=inviteeUid,proto3" json:"invitee_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CountEscapeUserLabelDailyInviteeReq) Reset()         { *m = CountEscapeUserLabelDailyInviteeReq{} }
func (m *CountEscapeUserLabelDailyInviteeReq) String() string { return proto.CompactTextString(m) }
func (*CountEscapeUserLabelDailyInviteeReq) ProtoMessage()    {}
func (*CountEscapeUserLabelDailyInviteeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{5}
}
func (m *CountEscapeUserLabelDailyInviteeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CountEscapeUserLabelDailyInviteeReq.Unmarshal(m, b)
}
func (m *CountEscapeUserLabelDailyInviteeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CountEscapeUserLabelDailyInviteeReq.Marshal(b, m, deterministic)
}
func (dst *CountEscapeUserLabelDailyInviteeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CountEscapeUserLabelDailyInviteeReq.Merge(dst, src)
}
func (m *CountEscapeUserLabelDailyInviteeReq) XXX_Size() int {
	return xxx_messageInfo_CountEscapeUserLabelDailyInviteeReq.Size(m)
}
func (m *CountEscapeUserLabelDailyInviteeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CountEscapeUserLabelDailyInviteeReq.DiscardUnknown(m)
}

var xxx_messageInfo_CountEscapeUserLabelDailyInviteeReq proto.InternalMessageInfo

func (m *CountEscapeUserLabelDailyInviteeReq) GetInviteUid() uint32 {
	if m != nil {
		return m.InviteUid
	}
	return 0
}

func (m *CountEscapeUserLabelDailyInviteeReq) GetInviteeUid() uint32 {
	if m != nil {
		return m.InviteeUid
	}
	return 0
}

type CountEscapeUserLabelDailyInviteeResp struct {
	InviteeTimes         uint32   `protobuf:"varint,1,opt,name=invitee_times,json=inviteeTimes,proto3" json:"invitee_times,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CountEscapeUserLabelDailyInviteeResp) Reset()         { *m = CountEscapeUserLabelDailyInviteeResp{} }
func (m *CountEscapeUserLabelDailyInviteeResp) String() string { return proto.CompactTextString(m) }
func (*CountEscapeUserLabelDailyInviteeResp) ProtoMessage()    {}
func (*CountEscapeUserLabelDailyInviteeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{6}
}
func (m *CountEscapeUserLabelDailyInviteeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CountEscapeUserLabelDailyInviteeResp.Unmarshal(m, b)
}
func (m *CountEscapeUserLabelDailyInviteeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CountEscapeUserLabelDailyInviteeResp.Marshal(b, m, deterministic)
}
func (dst *CountEscapeUserLabelDailyInviteeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CountEscapeUserLabelDailyInviteeResp.Merge(dst, src)
}
func (m *CountEscapeUserLabelDailyInviteeResp) XXX_Size() int {
	return xxx_messageInfo_CountEscapeUserLabelDailyInviteeResp.Size(m)
}
func (m *CountEscapeUserLabelDailyInviteeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CountEscapeUserLabelDailyInviteeResp.DiscardUnknown(m)
}

var xxx_messageInfo_CountEscapeUserLabelDailyInviteeResp proto.InternalMessageInfo

func (m *CountEscapeUserLabelDailyInviteeResp) GetInviteeTimes() uint32 {
	if m != nil {
		return m.InviteeTimes
	}
	return 0
}

// 获取密逃用户标签每日被邀请数
type GetEscapeUserLabelDailyInviteeTimesReq struct {
	InviteeUidList       []uint32 `protobuf:"varint,1,rep,packed,name=invitee_uid_list,json=inviteeUidList,proto3" json:"invitee_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEscapeUserLabelDailyInviteeTimesReq) Reset() {
	*m = GetEscapeUserLabelDailyInviteeTimesReq{}
}
func (m *GetEscapeUserLabelDailyInviteeTimesReq) String() string { return proto.CompactTextString(m) }
func (*GetEscapeUserLabelDailyInviteeTimesReq) ProtoMessage()    {}
func (*GetEscapeUserLabelDailyInviteeTimesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{7}
}
func (m *GetEscapeUserLabelDailyInviteeTimesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEscapeUserLabelDailyInviteeTimesReq.Unmarshal(m, b)
}
func (m *GetEscapeUserLabelDailyInviteeTimesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEscapeUserLabelDailyInviteeTimesReq.Marshal(b, m, deterministic)
}
func (dst *GetEscapeUserLabelDailyInviteeTimesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEscapeUserLabelDailyInviteeTimesReq.Merge(dst, src)
}
func (m *GetEscapeUserLabelDailyInviteeTimesReq) XXX_Size() int {
	return xxx_messageInfo_GetEscapeUserLabelDailyInviteeTimesReq.Size(m)
}
func (m *GetEscapeUserLabelDailyInviteeTimesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEscapeUserLabelDailyInviteeTimesReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEscapeUserLabelDailyInviteeTimesReq proto.InternalMessageInfo

func (m *GetEscapeUserLabelDailyInviteeTimesReq) GetInviteeUidList() []uint32 {
	if m != nil {
		return m.InviteeUidList
	}
	return nil
}

type GetEscapeUserLabelDailyInviteeTimesResp struct {
	InfoList             []*GetEscapeUserLabelDailyInviteeTimesResp_Info `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                        `json:"-"`
	XXX_unrecognized     []byte                                          `json:"-"`
	XXX_sizecache        int32                                           `json:"-"`
}

func (m *GetEscapeUserLabelDailyInviteeTimesResp) Reset() {
	*m = GetEscapeUserLabelDailyInviteeTimesResp{}
}
func (m *GetEscapeUserLabelDailyInviteeTimesResp) String() string { return proto.CompactTextString(m) }
func (*GetEscapeUserLabelDailyInviteeTimesResp) ProtoMessage()    {}
func (*GetEscapeUserLabelDailyInviteeTimesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{8}
}
func (m *GetEscapeUserLabelDailyInviteeTimesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEscapeUserLabelDailyInviteeTimesResp.Unmarshal(m, b)
}
func (m *GetEscapeUserLabelDailyInviteeTimesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEscapeUserLabelDailyInviteeTimesResp.Marshal(b, m, deterministic)
}
func (dst *GetEscapeUserLabelDailyInviteeTimesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEscapeUserLabelDailyInviteeTimesResp.Merge(dst, src)
}
func (m *GetEscapeUserLabelDailyInviteeTimesResp) XXX_Size() int {
	return xxx_messageInfo_GetEscapeUserLabelDailyInviteeTimesResp.Size(m)
}
func (m *GetEscapeUserLabelDailyInviteeTimesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEscapeUserLabelDailyInviteeTimesResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEscapeUserLabelDailyInviteeTimesResp proto.InternalMessageInfo

func (m *GetEscapeUserLabelDailyInviteeTimesResp) GetInfoList() []*GetEscapeUserLabelDailyInviteeTimesResp_Info {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type GetEscapeUserLabelDailyInviteeTimesResp_Info struct {
	InviteeUid           uint32   `protobuf:"varint,1,opt,name=invitee_uid,json=inviteeUid,proto3" json:"invitee_uid,omitempty"`
	InviteeTimes         uint32   `protobuf:"varint,2,opt,name=invitee_times,json=inviteeTimes,proto3" json:"invitee_times,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEscapeUserLabelDailyInviteeTimesResp_Info) Reset() {
	*m = GetEscapeUserLabelDailyInviteeTimesResp_Info{}
}
func (m *GetEscapeUserLabelDailyInviteeTimesResp_Info) String() string {
	return proto.CompactTextString(m)
}
func (*GetEscapeUserLabelDailyInviteeTimesResp_Info) ProtoMessage() {}
func (*GetEscapeUserLabelDailyInviteeTimesResp_Info) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{8, 0}
}
func (m *GetEscapeUserLabelDailyInviteeTimesResp_Info) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEscapeUserLabelDailyInviteeTimesResp_Info.Unmarshal(m, b)
}
func (m *GetEscapeUserLabelDailyInviteeTimesResp_Info) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEscapeUserLabelDailyInviteeTimesResp_Info.Marshal(b, m, deterministic)
}
func (dst *GetEscapeUserLabelDailyInviteeTimesResp_Info) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEscapeUserLabelDailyInviteeTimesResp_Info.Merge(dst, src)
}
func (m *GetEscapeUserLabelDailyInviteeTimesResp_Info) XXX_Size() int {
	return xxx_messageInfo_GetEscapeUserLabelDailyInviteeTimesResp_Info.Size(m)
}
func (m *GetEscapeUserLabelDailyInviteeTimesResp_Info) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEscapeUserLabelDailyInviteeTimesResp_Info.DiscardUnknown(m)
}

var xxx_messageInfo_GetEscapeUserLabelDailyInviteeTimesResp_Info proto.InternalMessageInfo

func (m *GetEscapeUserLabelDailyInviteeTimesResp_Info) GetInviteeUid() uint32 {
	if m != nil {
		return m.InviteeUid
	}
	return 0
}

func (m *GetEscapeUserLabelDailyInviteeTimesResp_Info) GetInviteeTimes() uint32 {
	if m != nil {
		return m.InviteeTimes
	}
	return 0
}

// 用户标签题目选项
type EscapeUserLabelSurveyOption struct {
	SexOption            EscapeUserLabelSurveyOption_SexOption        `protobuf:"varint,1,opt,name=sex_option,json=sexOption,proto3,enum=mijing_label.EscapeUserLabelSurveyOption_SexOption" json:"sex_option,omitempty"`
	OptionContent        string                                       `protobuf:"bytes,2,opt,name=option_content,json=optionContent,proto3" json:"option_content,omitempty"`
	UserLabelContent     string                                       `protobuf:"bytes,3,opt,name=user_label_content,json=userLabelContent,proto3" json:"user_label_content,omitempty"`
	IsSelected           EscapeUserLabelSurveyOption_OptionIsSelected `protobuf:"varint,4,opt,name=is_selected,json=isSelected,proto3,enum=mijing_label.EscapeUserLabelSurveyOption_OptionIsSelected" json:"is_selected,omitempty"`
	DimensionInfoList    []*EscapeUserLabelSurveyOption_DimensionInfo `protobuf:"bytes,5,rep,name=dimension_info_list,json=dimensionInfoList,proto3" json:"dimension_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *EscapeUserLabelSurveyOption) Reset()         { *m = EscapeUserLabelSurveyOption{} }
func (m *EscapeUserLabelSurveyOption) String() string { return proto.CompactTextString(m) }
func (*EscapeUserLabelSurveyOption) ProtoMessage()    {}
func (*EscapeUserLabelSurveyOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{9}
}
func (m *EscapeUserLabelSurveyOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EscapeUserLabelSurveyOption.Unmarshal(m, b)
}
func (m *EscapeUserLabelSurveyOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EscapeUserLabelSurveyOption.Marshal(b, m, deterministic)
}
func (dst *EscapeUserLabelSurveyOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EscapeUserLabelSurveyOption.Merge(dst, src)
}
func (m *EscapeUserLabelSurveyOption) XXX_Size() int {
	return xxx_messageInfo_EscapeUserLabelSurveyOption.Size(m)
}
func (m *EscapeUserLabelSurveyOption) XXX_DiscardUnknown() {
	xxx_messageInfo_EscapeUserLabelSurveyOption.DiscardUnknown(m)
}

var xxx_messageInfo_EscapeUserLabelSurveyOption proto.InternalMessageInfo

func (m *EscapeUserLabelSurveyOption) GetSexOption() EscapeUserLabelSurveyOption_SexOption {
	if m != nil {
		return m.SexOption
	}
	return EscapeUserLabelSurveyOption_SexOptionNil
}

func (m *EscapeUserLabelSurveyOption) GetOptionContent() string {
	if m != nil {
		return m.OptionContent
	}
	return ""
}

func (m *EscapeUserLabelSurveyOption) GetUserLabelContent() string {
	if m != nil {
		return m.UserLabelContent
	}
	return ""
}

func (m *EscapeUserLabelSurveyOption) GetIsSelected() EscapeUserLabelSurveyOption_OptionIsSelected {
	if m != nil {
		return m.IsSelected
	}
	return EscapeUserLabelSurveyOption_OptionIsSelectedNil
}

func (m *EscapeUserLabelSurveyOption) GetDimensionInfoList() []*EscapeUserLabelSurveyOption_DimensionInfo {
	if m != nil {
		return m.DimensionInfoList
	}
	return nil
}

// 添加维度分数值信息
type EscapeUserLabelSurveyOption_DimensionInfo struct {
	DimensionId          uint32   `protobuf:"varint,1,opt,name=dimension_id,json=dimensionId,proto3" json:"dimension_id,omitempty"`
	Score                uint32   `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EscapeUserLabelSurveyOption_DimensionInfo) Reset() {
	*m = EscapeUserLabelSurveyOption_DimensionInfo{}
}
func (m *EscapeUserLabelSurveyOption_DimensionInfo) String() string {
	return proto.CompactTextString(m)
}
func (*EscapeUserLabelSurveyOption_DimensionInfo) ProtoMessage() {}
func (*EscapeUserLabelSurveyOption_DimensionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{9, 0}
}
func (m *EscapeUserLabelSurveyOption_DimensionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EscapeUserLabelSurveyOption_DimensionInfo.Unmarshal(m, b)
}
func (m *EscapeUserLabelSurveyOption_DimensionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EscapeUserLabelSurveyOption_DimensionInfo.Marshal(b, m, deterministic)
}
func (dst *EscapeUserLabelSurveyOption_DimensionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EscapeUserLabelSurveyOption_DimensionInfo.Merge(dst, src)
}
func (m *EscapeUserLabelSurveyOption_DimensionInfo) XXX_Size() int {
	return xxx_messageInfo_EscapeUserLabelSurveyOption_DimensionInfo.Size(m)
}
func (m *EscapeUserLabelSurveyOption_DimensionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EscapeUserLabelSurveyOption_DimensionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EscapeUserLabelSurveyOption_DimensionInfo proto.InternalMessageInfo

func (m *EscapeUserLabelSurveyOption_DimensionInfo) GetDimensionId() uint32 {
	if m != nil {
		return m.DimensionId
	}
	return 0
}

func (m *EscapeUserLabelSurveyOption_DimensionInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

// 密逃用户标签题目
type EscapeUserLabelSurveyQuestion struct {
	QuestionContent      string                                        `protobuf:"bytes,1,opt,name=question_content,json=questionContent,proto3" json:"question_content,omitempty"`
	QuestionType         EscapeUserLabelSurveyQuestion_QuestionType    `protobuf:"varint,2,opt,name=question_type,json=questionType,proto3,enum=mijing_label.EscapeUserLabelSurveyQuestion_QuestionType" json:"question_type,omitempty"`
	SpecialQuestion      EscapeUserLabelSurveyQuestion_SpecialQuestion `protobuf:"varint,3,opt,name=special_question,json=specialQuestion,proto3,enum=mijing_label.EscapeUserLabelSurveyQuestion_SpecialQuestion" json:"special_question,omitempty"`
	OptionList           []*EscapeUserLabelSurveyOption                `protobuf:"bytes,4,rep,name=option_list,json=optionList,proto3" json:"option_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *EscapeUserLabelSurveyQuestion) Reset()         { *m = EscapeUserLabelSurveyQuestion{} }
func (m *EscapeUserLabelSurveyQuestion) String() string { return proto.CompactTextString(m) }
func (*EscapeUserLabelSurveyQuestion) ProtoMessage()    {}
func (*EscapeUserLabelSurveyQuestion) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{10}
}
func (m *EscapeUserLabelSurveyQuestion) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EscapeUserLabelSurveyQuestion.Unmarshal(m, b)
}
func (m *EscapeUserLabelSurveyQuestion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EscapeUserLabelSurveyQuestion.Marshal(b, m, deterministic)
}
func (dst *EscapeUserLabelSurveyQuestion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EscapeUserLabelSurveyQuestion.Merge(dst, src)
}
func (m *EscapeUserLabelSurveyQuestion) XXX_Size() int {
	return xxx_messageInfo_EscapeUserLabelSurveyQuestion.Size(m)
}
func (m *EscapeUserLabelSurveyQuestion) XXX_DiscardUnknown() {
	xxx_messageInfo_EscapeUserLabelSurveyQuestion.DiscardUnknown(m)
}

var xxx_messageInfo_EscapeUserLabelSurveyQuestion proto.InternalMessageInfo

func (m *EscapeUserLabelSurveyQuestion) GetQuestionContent() string {
	if m != nil {
		return m.QuestionContent
	}
	return ""
}

func (m *EscapeUserLabelSurveyQuestion) GetQuestionType() EscapeUserLabelSurveyQuestion_QuestionType {
	if m != nil {
		return m.QuestionType
	}
	return EscapeUserLabelSurveyQuestion_QuestionTypeNil
}

func (m *EscapeUserLabelSurveyQuestion) GetSpecialQuestion() EscapeUserLabelSurveyQuestion_SpecialQuestion {
	if m != nil {
		return m.SpecialQuestion
	}
	return EscapeUserLabelSurveyQuestion_SpecialQuestionNil
}

func (m *EscapeUserLabelSurveyQuestion) GetOptionList() []*EscapeUserLabelSurveyOption {
	if m != nil {
		return m.OptionList
	}
	return nil
}

// 密逃用户标签维度信息
type EscapeUserLabelDimensionInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EscapeUserLabelDimensionInfo) Reset()         { *m = EscapeUserLabelDimensionInfo{} }
func (m *EscapeUserLabelDimensionInfo) String() string { return proto.CompactTextString(m) }
func (*EscapeUserLabelDimensionInfo) ProtoMessage()    {}
func (*EscapeUserLabelDimensionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{11}
}
func (m *EscapeUserLabelDimensionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EscapeUserLabelDimensionInfo.Unmarshal(m, b)
}
func (m *EscapeUserLabelDimensionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EscapeUserLabelDimensionInfo.Marshal(b, m, deterministic)
}
func (dst *EscapeUserLabelDimensionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EscapeUserLabelDimensionInfo.Merge(dst, src)
}
func (m *EscapeUserLabelDimensionInfo) XXX_Size() int {
	return xxx_messageInfo_EscapeUserLabelDimensionInfo.Size(m)
}
func (m *EscapeUserLabelDimensionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EscapeUserLabelDimensionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EscapeUserLabelDimensionInfo proto.InternalMessageInfo

func (m *EscapeUserLabelDimensionInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *EscapeUserLabelDimensionInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// 密逃用户标签测试结果
type EscapeUserLabelResult struct {
	FirstDimensionId     uint32                                `protobuf:"varint,1,opt,name=first_dimension_id,json=firstDimensionId,proto3" json:"first_dimension_id,omitempty"`
	SecondDimensionId    uint32                                `protobuf:"varint,2,opt,name=second_dimension_id,json=secondDimensionId,proto3" json:"second_dimension_id,omitempty"`
	CharacterResult      string                                `protobuf:"bytes,3,opt,name=character_result,json=characterResult,proto3" json:"character_result,omitempty"`
	CharacterUserDesc    string                                `protobuf:"bytes,4,opt,name=character_user_desc,json=characterUserDesc,proto3" json:"character_user_desc,omitempty"`
	CharacterFriendDesc  string                                `protobuf:"bytes,5,opt,name=character_friend_desc,json=characterFriendDesc,proto3" json:"character_friend_desc,omitempty"`
	ScenarioInfoList     []*EscapeUserLabelResult_ScenarioInfo `protobuf:"bytes,6,rep,name=scenario_info_list,json=scenarioInfoList,proto3" json:"scenario_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *EscapeUserLabelResult) Reset()         { *m = EscapeUserLabelResult{} }
func (m *EscapeUserLabelResult) String() string { return proto.CompactTextString(m) }
func (*EscapeUserLabelResult) ProtoMessage()    {}
func (*EscapeUserLabelResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{12}
}
func (m *EscapeUserLabelResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EscapeUserLabelResult.Unmarshal(m, b)
}
func (m *EscapeUserLabelResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EscapeUserLabelResult.Marshal(b, m, deterministic)
}
func (dst *EscapeUserLabelResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EscapeUserLabelResult.Merge(dst, src)
}
func (m *EscapeUserLabelResult) XXX_Size() int {
	return xxx_messageInfo_EscapeUserLabelResult.Size(m)
}
func (m *EscapeUserLabelResult) XXX_DiscardUnknown() {
	xxx_messageInfo_EscapeUserLabelResult.DiscardUnknown(m)
}

var xxx_messageInfo_EscapeUserLabelResult proto.InternalMessageInfo

func (m *EscapeUserLabelResult) GetFirstDimensionId() uint32 {
	if m != nil {
		return m.FirstDimensionId
	}
	return 0
}

func (m *EscapeUserLabelResult) GetSecondDimensionId() uint32 {
	if m != nil {
		return m.SecondDimensionId
	}
	return 0
}

func (m *EscapeUserLabelResult) GetCharacterResult() string {
	if m != nil {
		return m.CharacterResult
	}
	return ""
}

func (m *EscapeUserLabelResult) GetCharacterUserDesc() string {
	if m != nil {
		return m.CharacterUserDesc
	}
	return ""
}

func (m *EscapeUserLabelResult) GetCharacterFriendDesc() string {
	if m != nil {
		return m.CharacterFriendDesc
	}
	return ""
}

func (m *EscapeUserLabelResult) GetScenarioInfoList() []*EscapeUserLabelResult_ScenarioInfo {
	if m != nil {
		return m.ScenarioInfoList
	}
	return nil
}

type EscapeUserLabelResult_ScenarioInfo struct {
	ScenarioId           uint32   `protobuf:"varint,1,opt,name=scenario_id,json=scenarioId,proto3" json:"scenario_id,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EscapeUserLabelResult_ScenarioInfo) Reset()         { *m = EscapeUserLabelResult_ScenarioInfo{} }
func (m *EscapeUserLabelResult_ScenarioInfo) String() string { return proto.CompactTextString(m) }
func (*EscapeUserLabelResult_ScenarioInfo) ProtoMessage()    {}
func (*EscapeUserLabelResult_ScenarioInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{12, 0}
}
func (m *EscapeUserLabelResult_ScenarioInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EscapeUserLabelResult_ScenarioInfo.Unmarshal(m, b)
}
func (m *EscapeUserLabelResult_ScenarioInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EscapeUserLabelResult_ScenarioInfo.Marshal(b, m, deterministic)
}
func (dst *EscapeUserLabelResult_ScenarioInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EscapeUserLabelResult_ScenarioInfo.Merge(dst, src)
}
func (m *EscapeUserLabelResult_ScenarioInfo) XXX_Size() int {
	return xxx_messageInfo_EscapeUserLabelResult_ScenarioInfo.Size(m)
}
func (m *EscapeUserLabelResult_ScenarioInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EscapeUserLabelResult_ScenarioInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EscapeUserLabelResult_ScenarioInfo proto.InternalMessageInfo

func (m *EscapeUserLabelResult_ScenarioInfo) GetScenarioId() uint32 {
	if m != nil {
		return m.ScenarioId
	}
	return 0
}

func (m *EscapeUserLabelResult_ScenarioInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

// 密逃用户标签问卷
type EscapeUserLabelSurvey struct {
	Version              uint32                           `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	ResultList           []*EscapeUserLabelResult         `protobuf:"bytes,2,rep,name=result_list,json=resultList,proto3" json:"result_list,omitempty"`
	DimensionList        []*EscapeUserLabelDimensionInfo  `protobuf:"bytes,3,rep,name=dimension_list,json=dimensionList,proto3" json:"dimension_list,omitempty"`
	QuestionList         []*EscapeUserLabelSurveyQuestion `protobuf:"bytes,4,rep,name=question_list,json=questionList,proto3" json:"question_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *EscapeUserLabelSurvey) Reset()         { *m = EscapeUserLabelSurvey{} }
func (m *EscapeUserLabelSurvey) String() string { return proto.CompactTextString(m) }
func (*EscapeUserLabelSurvey) ProtoMessage()    {}
func (*EscapeUserLabelSurvey) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{13}
}
func (m *EscapeUserLabelSurvey) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EscapeUserLabelSurvey.Unmarshal(m, b)
}
func (m *EscapeUserLabelSurvey) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EscapeUserLabelSurvey.Marshal(b, m, deterministic)
}
func (dst *EscapeUserLabelSurvey) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EscapeUserLabelSurvey.Merge(dst, src)
}
func (m *EscapeUserLabelSurvey) XXX_Size() int {
	return xxx_messageInfo_EscapeUserLabelSurvey.Size(m)
}
func (m *EscapeUserLabelSurvey) XXX_DiscardUnknown() {
	xxx_messageInfo_EscapeUserLabelSurvey.DiscardUnknown(m)
}

var xxx_messageInfo_EscapeUserLabelSurvey proto.InternalMessageInfo

func (m *EscapeUserLabelSurvey) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *EscapeUserLabelSurvey) GetResultList() []*EscapeUserLabelResult {
	if m != nil {
		return m.ResultList
	}
	return nil
}

func (m *EscapeUserLabelSurvey) GetDimensionList() []*EscapeUserLabelDimensionInfo {
	if m != nil {
		return m.DimensionList
	}
	return nil
}

func (m *EscapeUserLabelSurvey) GetQuestionList() []*EscapeUserLabelSurveyQuestion {
	if m != nil {
		return m.QuestionList
	}
	return nil
}

// 获取用户标签问卷信息
type GetEscapeUserLabelSurveyReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEscapeUserLabelSurveyReq) Reset()         { *m = GetEscapeUserLabelSurveyReq{} }
func (m *GetEscapeUserLabelSurveyReq) String() string { return proto.CompactTextString(m) }
func (*GetEscapeUserLabelSurveyReq) ProtoMessage()    {}
func (*GetEscapeUserLabelSurveyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{14}
}
func (m *GetEscapeUserLabelSurveyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEscapeUserLabelSurveyReq.Unmarshal(m, b)
}
func (m *GetEscapeUserLabelSurveyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEscapeUserLabelSurveyReq.Marshal(b, m, deterministic)
}
func (dst *GetEscapeUserLabelSurveyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEscapeUserLabelSurveyReq.Merge(dst, src)
}
func (m *GetEscapeUserLabelSurveyReq) XXX_Size() int {
	return xxx_messageInfo_GetEscapeUserLabelSurveyReq.Size(m)
}
func (m *GetEscapeUserLabelSurveyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEscapeUserLabelSurveyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEscapeUserLabelSurveyReq proto.InternalMessageInfo

type GetEscapeUserLabelSurveyResp struct {
	Survey               *EscapeUserLabelSurvey `protobuf:"bytes,1,opt,name=survey,proto3" json:"survey,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetEscapeUserLabelSurveyResp) Reset()         { *m = GetEscapeUserLabelSurveyResp{} }
func (m *GetEscapeUserLabelSurveyResp) String() string { return proto.CompactTextString(m) }
func (*GetEscapeUserLabelSurveyResp) ProtoMessage()    {}
func (*GetEscapeUserLabelSurveyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{15}
}
func (m *GetEscapeUserLabelSurveyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEscapeUserLabelSurveyResp.Unmarshal(m, b)
}
func (m *GetEscapeUserLabelSurveyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEscapeUserLabelSurveyResp.Marshal(b, m, deterministic)
}
func (dst *GetEscapeUserLabelSurveyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEscapeUserLabelSurveyResp.Merge(dst, src)
}
func (m *GetEscapeUserLabelSurveyResp) XXX_Size() int {
	return xxx_messageInfo_GetEscapeUserLabelSurveyResp.Size(m)
}
func (m *GetEscapeUserLabelSurveyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEscapeUserLabelSurveyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEscapeUserLabelSurveyResp proto.InternalMessageInfo

func (m *GetEscapeUserLabelSurveyResp) GetSurvey() *EscapeUserLabelSurvey {
	if m != nil {
		return m.Survey
	}
	return nil
}

// 获取逃离用户标签配置
type GetEscapeUserLabelConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEscapeUserLabelConfigReq) Reset()         { *m = GetEscapeUserLabelConfigReq{} }
func (m *GetEscapeUserLabelConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetEscapeUserLabelConfigReq) ProtoMessage()    {}
func (*GetEscapeUserLabelConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{16}
}
func (m *GetEscapeUserLabelConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEscapeUserLabelConfigReq.Unmarshal(m, b)
}
func (m *GetEscapeUserLabelConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEscapeUserLabelConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetEscapeUserLabelConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEscapeUserLabelConfigReq.Merge(dst, src)
}
func (m *GetEscapeUserLabelConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetEscapeUserLabelConfigReq.Size(m)
}
func (m *GetEscapeUserLabelConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEscapeUserLabelConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEscapeUserLabelConfigReq proto.InternalMessageInfo

type GetEscapeUserLabelConfigResp struct {
	Switch               GetEscapeUserLabelConfigResp_Switch `protobuf:"varint,1,opt,name=switch,proto3,enum=mijing_label.GetEscapeUserLabelConfigResp_Switch" json:"switch,omitempty"`
	Url                  string                              `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	DailyInviteeTimes    uint32                              `protobuf:"varint,3,opt,name=daily_invitee_times,json=dailyInviteeTimes,proto3" json:"daily_invitee_times,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *GetEscapeUserLabelConfigResp) Reset()         { *m = GetEscapeUserLabelConfigResp{} }
func (m *GetEscapeUserLabelConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetEscapeUserLabelConfigResp) ProtoMessage()    {}
func (*GetEscapeUserLabelConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_label_2f4ef90e0bb75e63, []int{17}
}
func (m *GetEscapeUserLabelConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEscapeUserLabelConfigResp.Unmarshal(m, b)
}
func (m *GetEscapeUserLabelConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEscapeUserLabelConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetEscapeUserLabelConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEscapeUserLabelConfigResp.Merge(dst, src)
}
func (m *GetEscapeUserLabelConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetEscapeUserLabelConfigResp.Size(m)
}
func (m *GetEscapeUserLabelConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEscapeUserLabelConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEscapeUserLabelConfigResp proto.InternalMessageInfo

func (m *GetEscapeUserLabelConfigResp) GetSwitch() GetEscapeUserLabelConfigResp_Switch {
	if m != nil {
		return m.Switch
	}
	return GetEscapeUserLabelConfigResp_SwitchNil
}

func (m *GetEscapeUserLabelConfigResp) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *GetEscapeUserLabelConfigResp) GetDailyInviteeTimes() uint32 {
	if m != nil {
		return m.DailyInviteeTimes
	}
	return 0
}

func init() {
	proto.RegisterType((*EscapeUserLabel)(nil), "mijing_label.EscapeUserLabel")
	proto.RegisterType((*EscapeUserLabel_DimensionInfo)(nil), "mijing_label.EscapeUserLabel.DimensionInfo")
	proto.RegisterType((*EscapeUserLabel_ScenarioInfo)(nil), "mijing_label.EscapeUserLabel.ScenarioInfo")
	proto.RegisterType((*EscapeUserLabel_AvatarInfo)(nil), "mijing_label.EscapeUserLabel.AvatarInfo")
	proto.RegisterType((*AddEscapeUserLabelReq)(nil), "mijing_label.AddEscapeUserLabelReq")
	proto.RegisterType((*AddEscapeUserLabelResp)(nil), "mijing_label.AddEscapeUserLabelResp")
	proto.RegisterType((*GetEscapeUserLabelListReq)(nil), "mijing_label.GetEscapeUserLabelListReq")
	proto.RegisterType((*GetEscapeUserLabelListResp)(nil), "mijing_label.GetEscapeUserLabelListResp")
	proto.RegisterType((*CountEscapeUserLabelDailyInviteeReq)(nil), "mijing_label.CountEscapeUserLabelDailyInviteeReq")
	proto.RegisterType((*CountEscapeUserLabelDailyInviteeResp)(nil), "mijing_label.CountEscapeUserLabelDailyInviteeResp")
	proto.RegisterType((*GetEscapeUserLabelDailyInviteeTimesReq)(nil), "mijing_label.GetEscapeUserLabelDailyInviteeTimesReq")
	proto.RegisterType((*GetEscapeUserLabelDailyInviteeTimesResp)(nil), "mijing_label.GetEscapeUserLabelDailyInviteeTimesResp")
	proto.RegisterType((*GetEscapeUserLabelDailyInviteeTimesResp_Info)(nil), "mijing_label.GetEscapeUserLabelDailyInviteeTimesResp.Info")
	proto.RegisterType((*EscapeUserLabelSurveyOption)(nil), "mijing_label.EscapeUserLabelSurveyOption")
	proto.RegisterType((*EscapeUserLabelSurveyOption_DimensionInfo)(nil), "mijing_label.EscapeUserLabelSurveyOption.DimensionInfo")
	proto.RegisterType((*EscapeUserLabelSurveyQuestion)(nil), "mijing_label.EscapeUserLabelSurveyQuestion")
	proto.RegisterType((*EscapeUserLabelDimensionInfo)(nil), "mijing_label.EscapeUserLabelDimensionInfo")
	proto.RegisterType((*EscapeUserLabelResult)(nil), "mijing_label.EscapeUserLabelResult")
	proto.RegisterType((*EscapeUserLabelResult_ScenarioInfo)(nil), "mijing_label.EscapeUserLabelResult.ScenarioInfo")
	proto.RegisterType((*EscapeUserLabelSurvey)(nil), "mijing_label.EscapeUserLabelSurvey")
	proto.RegisterType((*GetEscapeUserLabelSurveyReq)(nil), "mijing_label.GetEscapeUserLabelSurveyReq")
	proto.RegisterType((*GetEscapeUserLabelSurveyResp)(nil), "mijing_label.GetEscapeUserLabelSurveyResp")
	proto.RegisterType((*GetEscapeUserLabelConfigReq)(nil), "mijing_label.GetEscapeUserLabelConfigReq")
	proto.RegisterType((*GetEscapeUserLabelConfigResp)(nil), "mijing_label.GetEscapeUserLabelConfigResp")
	proto.RegisterEnum("mijing_label.EscapeUserLabelStatus", EscapeUserLabelStatus_name, EscapeUserLabelStatus_value)
	proto.RegisterEnum("mijing_label.EscapeUserLabel_SexOption", EscapeUserLabel_SexOption_name, EscapeUserLabel_SexOption_value)
	proto.RegisterEnum("mijing_label.EscapeUserLabelSurveyOption_OptionIsSelected", EscapeUserLabelSurveyOption_OptionIsSelected_name, EscapeUserLabelSurveyOption_OptionIsSelected_value)
	proto.RegisterEnum("mijing_label.EscapeUserLabelSurveyOption_SexOption", EscapeUserLabelSurveyOption_SexOption_name, EscapeUserLabelSurveyOption_SexOption_value)
	proto.RegisterEnum("mijing_label.EscapeUserLabelSurveyQuestion_QuestionType", EscapeUserLabelSurveyQuestion_QuestionType_name, EscapeUserLabelSurveyQuestion_QuestionType_value)
	proto.RegisterEnum("mijing_label.EscapeUserLabelSurveyQuestion_SpecialQuestion", EscapeUserLabelSurveyQuestion_SpecialQuestion_name, EscapeUserLabelSurveyQuestion_SpecialQuestion_value)
	proto.RegisterEnum("mijing_label.GetEscapeUserLabelConfigResp_Switch", GetEscapeUserLabelConfigResp_Switch_name, GetEscapeUserLabelConfigResp_Switch_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MijingLabelClient is the client API for MijingLabel service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MijingLabelClient interface {
	// 新增密逃用户标签
	AddEscapeUserLabel(ctx context.Context, in *AddEscapeUserLabelReq, opts ...grpc.CallOption) (*AddEscapeUserLabelResp, error)
	// 获取密逃用户标签列表
	GetEscapeUserLabelList(ctx context.Context, in *GetEscapeUserLabelListReq, opts ...grpc.CallOption) (*GetEscapeUserLabelListResp, error)
	// 统计密逃用户标签每日被邀请数
	CountEscapeUserLabelDailyInvitee(ctx context.Context, in *CountEscapeUserLabelDailyInviteeReq, opts ...grpc.CallOption) (*CountEscapeUserLabelDailyInviteeResp, error)
	// 获取密逃用户标签每日被邀请数
	GetEscapeUserLabelDailyInviteeTimes(ctx context.Context, in *GetEscapeUserLabelDailyInviteeTimesReq, opts ...grpc.CallOption) (*GetEscapeUserLabelDailyInviteeTimesResp, error)
	// 获取用户标签问卷信息
	GetEscapeUserLabelSurvey(ctx context.Context, in *GetEscapeUserLabelSurveyReq, opts ...grpc.CallOption) (*GetEscapeUserLabelSurveyResp, error)
	// 获取逃离用户标签配置
	GetEscapeUserLabelConfig(ctx context.Context, in *GetEscapeUserLabelConfigReq, opts ...grpc.CallOption) (*GetEscapeUserLabelConfigResp, error)
}

type mijingLabelClient struct {
	cc *grpc.ClientConn
}

func NewMijingLabelClient(cc *grpc.ClientConn) MijingLabelClient {
	return &mijingLabelClient{cc}
}

func (c *mijingLabelClient) AddEscapeUserLabel(ctx context.Context, in *AddEscapeUserLabelReq, opts ...grpc.CallOption) (*AddEscapeUserLabelResp, error) {
	out := new(AddEscapeUserLabelResp)
	err := c.cc.Invoke(ctx, "/mijing_label.MijingLabel/AddEscapeUserLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingLabelClient) GetEscapeUserLabelList(ctx context.Context, in *GetEscapeUserLabelListReq, opts ...grpc.CallOption) (*GetEscapeUserLabelListResp, error) {
	out := new(GetEscapeUserLabelListResp)
	err := c.cc.Invoke(ctx, "/mijing_label.MijingLabel/GetEscapeUserLabelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingLabelClient) CountEscapeUserLabelDailyInvitee(ctx context.Context, in *CountEscapeUserLabelDailyInviteeReq, opts ...grpc.CallOption) (*CountEscapeUserLabelDailyInviteeResp, error) {
	out := new(CountEscapeUserLabelDailyInviteeResp)
	err := c.cc.Invoke(ctx, "/mijing_label.MijingLabel/CountEscapeUserLabelDailyInvitee", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingLabelClient) GetEscapeUserLabelDailyInviteeTimes(ctx context.Context, in *GetEscapeUserLabelDailyInviteeTimesReq, opts ...grpc.CallOption) (*GetEscapeUserLabelDailyInviteeTimesResp, error) {
	out := new(GetEscapeUserLabelDailyInviteeTimesResp)
	err := c.cc.Invoke(ctx, "/mijing_label.MijingLabel/GetEscapeUserLabelDailyInviteeTimes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingLabelClient) GetEscapeUserLabelSurvey(ctx context.Context, in *GetEscapeUserLabelSurveyReq, opts ...grpc.CallOption) (*GetEscapeUserLabelSurveyResp, error) {
	out := new(GetEscapeUserLabelSurveyResp)
	err := c.cc.Invoke(ctx, "/mijing_label.MijingLabel/GetEscapeUserLabelSurvey", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingLabelClient) GetEscapeUserLabelConfig(ctx context.Context, in *GetEscapeUserLabelConfigReq, opts ...grpc.CallOption) (*GetEscapeUserLabelConfigResp, error) {
	out := new(GetEscapeUserLabelConfigResp)
	err := c.cc.Invoke(ctx, "/mijing_label.MijingLabel/GetEscapeUserLabelConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MijingLabelServer is the server API for MijingLabel service.
type MijingLabelServer interface {
	// 新增密逃用户标签
	AddEscapeUserLabel(context.Context, *AddEscapeUserLabelReq) (*AddEscapeUserLabelResp, error)
	// 获取密逃用户标签列表
	GetEscapeUserLabelList(context.Context, *GetEscapeUserLabelListReq) (*GetEscapeUserLabelListResp, error)
	// 统计密逃用户标签每日被邀请数
	CountEscapeUserLabelDailyInvitee(context.Context, *CountEscapeUserLabelDailyInviteeReq) (*CountEscapeUserLabelDailyInviteeResp, error)
	// 获取密逃用户标签每日被邀请数
	GetEscapeUserLabelDailyInviteeTimes(context.Context, *GetEscapeUserLabelDailyInviteeTimesReq) (*GetEscapeUserLabelDailyInviteeTimesResp, error)
	// 获取用户标签问卷信息
	GetEscapeUserLabelSurvey(context.Context, *GetEscapeUserLabelSurveyReq) (*GetEscapeUserLabelSurveyResp, error)
	// 获取逃离用户标签配置
	GetEscapeUserLabelConfig(context.Context, *GetEscapeUserLabelConfigReq) (*GetEscapeUserLabelConfigResp, error)
}

func RegisterMijingLabelServer(s *grpc.Server, srv MijingLabelServer) {
	s.RegisterService(&_MijingLabel_serviceDesc, srv)
}

func _MijingLabel_AddEscapeUserLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddEscapeUserLabelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingLabelServer).AddEscapeUserLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mijing_label.MijingLabel/AddEscapeUserLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingLabelServer).AddEscapeUserLabel(ctx, req.(*AddEscapeUserLabelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingLabel_GetEscapeUserLabelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEscapeUserLabelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingLabelServer).GetEscapeUserLabelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mijing_label.MijingLabel/GetEscapeUserLabelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingLabelServer).GetEscapeUserLabelList(ctx, req.(*GetEscapeUserLabelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingLabel_CountEscapeUserLabelDailyInvitee_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountEscapeUserLabelDailyInviteeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingLabelServer).CountEscapeUserLabelDailyInvitee(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mijing_label.MijingLabel/CountEscapeUserLabelDailyInvitee",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingLabelServer).CountEscapeUserLabelDailyInvitee(ctx, req.(*CountEscapeUserLabelDailyInviteeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingLabel_GetEscapeUserLabelDailyInviteeTimes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEscapeUserLabelDailyInviteeTimesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingLabelServer).GetEscapeUserLabelDailyInviteeTimes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mijing_label.MijingLabel/GetEscapeUserLabelDailyInviteeTimes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingLabelServer).GetEscapeUserLabelDailyInviteeTimes(ctx, req.(*GetEscapeUserLabelDailyInviteeTimesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingLabel_GetEscapeUserLabelSurvey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEscapeUserLabelSurveyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingLabelServer).GetEscapeUserLabelSurvey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mijing_label.MijingLabel/GetEscapeUserLabelSurvey",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingLabelServer).GetEscapeUserLabelSurvey(ctx, req.(*GetEscapeUserLabelSurveyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingLabel_GetEscapeUserLabelConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEscapeUserLabelConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingLabelServer).GetEscapeUserLabelConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mijing_label.MijingLabel/GetEscapeUserLabelConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingLabelServer).GetEscapeUserLabelConfig(ctx, req.(*GetEscapeUserLabelConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MijingLabel_serviceDesc = grpc.ServiceDesc{
	ServiceName: "mijing_label.MijingLabel",
	HandlerType: (*MijingLabelServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddEscapeUserLabel",
			Handler:    _MijingLabel_AddEscapeUserLabel_Handler,
		},
		{
			MethodName: "GetEscapeUserLabelList",
			Handler:    _MijingLabel_GetEscapeUserLabelList_Handler,
		},
		{
			MethodName: "CountEscapeUserLabelDailyInvitee",
			Handler:    _MijingLabel_CountEscapeUserLabelDailyInvitee_Handler,
		},
		{
			MethodName: "GetEscapeUserLabelDailyInviteeTimes",
			Handler:    _MijingLabel_GetEscapeUserLabelDailyInviteeTimes_Handler,
		},
		{
			MethodName: "GetEscapeUserLabelSurvey",
			Handler:    _MijingLabel_GetEscapeUserLabelSurvey_Handler,
		},
		{
			MethodName: "GetEscapeUserLabelConfig",
			Handler:    _MijingLabel_GetEscapeUserLabelConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "mijing-label/mijing-label.proto",
}

func init() {
	proto.RegisterFile("mijing-label/mijing-label.proto", fileDescriptor_mijing_label_2f4ef90e0bb75e63)
}

var fileDescriptor_mijing_label_2f4ef90e0bb75e63 = []byte{
	// 1567 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x18, 0xdb, 0x72, 0xdb, 0xd4,
	0xf6, 0xc8, 0xce, 0xcd, 0xcb, 0x37, 0x65, 0xe7, 0x72, 0x1c, 0xb7, 0x39, 0xf5, 0x51, 0x4a, 0xeb,
	0xb4, 0xc5, 0x2d, 0x6e, 0x3b, 0x5c, 0xfa, 0xd4, 0x26, 0x0d, 0x04, 0xda, 0x86, 0x2a, 0x0d, 0x30,
	0x64, 0xa8, 0x50, 0xa4, 0xed, 0x64, 0x53, 0x59, 0x52, 0xb4, 0xe5, 0xb4, 0x99, 0x61, 0xfa, 0xce,
	0x2b, 0x33, 0xbc, 0xf1, 0x09, 0x7c, 0x04, 0x9f, 0xc0, 0x2f, 0xc0, 0x1b, 0x5f, 0xc0, 0x2b, 0xa3,
	0xb5, 0x75, 0xb3, 0xec, 0xd8, 0x4e, 0xa7, 0xcc, 0xf0, 0xe4, 0xbd, 0xd7, 0x75, 0xaf, 0xab, 0xd6,
	0x32, 0x5c, 0xea, 0xb2, 0xef, 0x98, 0x7d, 0xf8, 0xae, 0xa5, 0x1f, 0x50, 0xeb, 0x66, 0xfa, 0xd2,
	0x72, 0x3d, 0xc7, 0x77, 0x48, 0x49, 0xc0, 0x34, 0x84, 0x29, 0x7f, 0xcd, 0x42, 0xf5, 0x21, 0x37,
	0x74, 0x97, 0xee, 0x71, 0xea, 0x3d, 0x0a, 0x60, 0x44, 0x86, 0x7c, 0x8f, 0x99, 0x35, 0xa9, 0x21,
	0x35, 0xcb, 0x6a, 0x70, 0x24, 0xfb, 0xb0, 0x60, 0xb2, 0x2e, 0xb5, 0x39, 0x73, 0x6c, 0x8d, 0xd9,
	0x1d, 0x47, 0xb3, 0x18, 0xf7, 0x6b, 0xb9, 0x46, 0xbe, 0x59, 0x6c, 0x5f, 0x6f, 0xa5, 0x25, 0xb6,
	0x32, 0xd2, 0x5a, 0x9b, 0x11, 0xe3, 0xb6, 0xdd, 0x71, 0xd4, 0x79, 0x33, 0x7d, 0x7d, 0xc4, 0xb8,
	0x4f, 0xd6, 0x41, 0x36, 0x8e, 0x74, 0x4f, 0x37, 0x7c, 0xea, 0x69, 0x1e, 0xe5, 0x3d, 0xcb, 0xaf,
	0xe5, 0x1b, 0x52, 0xb3, 0xa0, 0x56, 0x63, 0xb8, 0x8a, 0x60, 0xd2, 0x82, 0x85, 0x84, 0xb4, 0xc7,
	0xa9, 0xa7, 0x99, 0x94, 0x1b, 0xb5, 0x29, 0xa4, 0x9e, 0x8f, 0x51, 0x81, 0xf2, 0x4d, 0xca, 0x0d,
	0xd2, 0x86, 0xa5, 0x84, 0xbe, 0xe3, 0x31, 0x6a, 0x9b, 0x82, 0x63, 0x1a, 0x39, 0x12, 0x61, 0x5b,
	0x88, 0x43, 0x9e, 0xaf, 0x80, 0x70, 0x83, 0xda, 0xba, 0xc7, 0x9c, 0x94, 0xa9, 0x33, 0x68, 0xea,
	0xb5, 0xd1, 0xa6, 0xee, 0x86, 0x7c, 0x68, 0xa9, 0xcc, 0x53, 0x37, 0x34, 0xf4, 0x0a, 0x54, 0x91,
	0x4f, 0xb3, 0xf5, 0x2e, 0x15, 0x62, 0x67, 0x1b, 0xf9, 0x66, 0x41, 0x2d, 0x23, 0xf8, 0x89, 0xde,
	0xa5, 0x48, 0xb7, 0x0d, 0x45, 0xfd, 0x44, 0xf7, 0x75, 0x0f, 0xf5, 0xd7, 0xe6, 0x1a, 0x52, 0xb3,
	0xd8, 0x6e, 0x8e, 0x56, 0x7d, 0x1f, 0x19, 0x50, 0x31, 0xe8, 0xf1, 0x99, 0xec, 0x40, 0x15, 0xdd,
	0xc4, 0xe9, 0x2b, 0xcd, 0x71, 0x7d, 0xe6, 0xd8, 0xb5, 0x42, 0x43, 0x6a, 0x56, 0xda, 0x57, 0xc7,
	0x58, 0x42, 0x5f, 0xed, 0x20, 0xb9, 0x5a, 0x0e, 0xf8, 0xe3, 0x2b, 0xd9, 0x85, 0xf9, 0xd0, 0x8f,
	0x29, 0x91, 0x70, 0x3e, 0x91, 0x55, 0x21, 0x21, 0x11, 0x7a, 0x0f, 0x66, 0xb8, 0xaf, 0xfb, 0x3d,
	0x5e, 0x2b, 0xa2, 0xa4, 0xb5, 0x91, 0x92, 0x76, 0x91, 0x54, 0x0d, 0x59, 0xea, 0x1f, 0x42, 0xb9,
	0x2f, 0xc5, 0x08, 0x81, 0xa9, 0xc0, 0xc1, 0x98, 0xbf, 0x05, 0x15, 0xcf, 0x64, 0x11, 0xa6, 0xb9,
	0xe1, 0x78, 0xb4, 0x96, 0xc3, 0xa4, 0x16, 0x97, 0xfa, 0x16, 0x94, 0xd2, 0x21, 0x23, 0x97, 0xa0,
	0x98, 0x84, 0x3e, 0x2a, 0x00, 0x88, 0xe3, 0x68, 0x92, 0x25, 0x98, 0xf1, 0xf5, 0x83, 0x00, 0x17,
	0xca, 0xf1, 0xf5, 0x83, 0x6d, 0xb3, 0xfe, 0x05, 0x40, 0xe2, 0x7f, 0x52, 0x81, 0x5c, 0xc8, 0x5c,
	0x50, 0x73, 0xcc, 0x24, 0x2b, 0x30, 0xc7, 0xfd, 0x53, 0x8b, 0x26, 0x6c, 0xb3, 0x78, 0xdf, 0x36,
	0xc9, 0x2a, 0x84, 0xc1, 0xd2, 0x7a, 0x9e, 0x15, 0x26, 0x7d, 0x41, 0x40, 0xf6, 0x3c, 0x4b, 0xd9,
	0x87, 0x42, 0xe2, 0x24, 0x19, 0x4a, 0xf1, 0xe5, 0x09, 0xb3, 0xe4, 0xff, 0x90, 0x79, 0x28, 0xc7,
	0x90, 0xc7, 0xba, 0x45, 0x65, 0x89, 0x2c, 0x40, 0x35, 0x06, 0x6d, 0xd1, 0x6e, 0x00, 0xcc, 0x91,
	0x45, 0x90, 0x63, 0xe0, 0x9e, 0xfd, 0xc2, 0x76, 0x5e, 0xda, 0x72, 0x5e, 0x79, 0x0e, 0x4b, 0xf7,
	0x4d, 0x33, 0xe3, 0x5b, 0x95, 0x1e, 0x93, 0x87, 0x61, 0xce, 0x88, 0x5c, 0xc5, 0x14, 0x94, 0x30,
	0x05, 0x57, 0x47, 0x86, 0x45, 0x64, 0x0a, 0x1e, 0x03, 0x37, 0x28, 0x1a, 0x2c, 0x0f, 0x93, 0xcf,
	0xdd, 0xb7, 0xa5, 0xe0, 0x7b, 0x58, 0xf9, 0x98, 0xfa, 0x19, 0xa2, 0xa0, 0x80, 0x02, 0x23, 0x56,
	0x60, 0xae, 0xc7, 0x4c, 0x51, 0x64, 0x52, 0x23, 0x1f, 0x38, 0xbd, 0xc7, 0x4c, 0x2c, 0xaf, 0x4d,
	0x28, 0x8a, 0xd4, 0x49, 0x9a, 0xd8, 0x84, 0x29, 0x07, 0x82, 0x2f, 0x90, 0xa2, 0x58, 0x50, 0x3f,
	0x4b, 0x3b, 0x77, 0xc9, 0x13, 0x58, 0xcc, 0x98, 0x98, 0x3c, 0x65, 0xac, 0x9d, 0xf3, 0x7d, 0x76,
	0xa2, 0x36, 0x0a, 0x6b, 0x1b, 0x4e, 0xcf, 0xce, 0xea, 0xdb, 0xd4, 0x99, 0x75, 0xba, 0x6d, 0x9f,
	0x30, 0x9f, 0xd2, 0xc0, 0xea, 0x55, 0x00, 0x86, 0x37, 0x2d, 0x69, 0xe0, 0x05, 0x01, 0xd9, 0x63,
	0x66, 0x90, 0xdf, 0xe2, 0x22, 0xf0, 0x22, 0x19, 0x43, 0x8e, 0x80, 0x40, 0xf9, 0x0c, 0x2e, 0x8f,
	0x57, 0xc3, 0x5d, 0xb2, 0x06, 0xe5, 0x48, 0x90, 0xcf, 0xba, 0x94, 0x87, 0xaa, 0x4a, 0x21, 0xf0,
	0x59, 0x00, 0x53, 0x54, 0xb8, 0x32, 0xe8, 0xa1, 0xb4, 0x28, 0x24, 0x0b, 0x9e, 0xdd, 0x04, 0x39,
	0xf5, 0xae, 0x74, 0xd0, 0x2a, 0xc9, 0xe3, 0xd0, 0x0f, 0xbf, 0x49, 0x70, 0x75, 0x22, 0xa1, 0xdc,
	0x25, 0x5f, 0x42, 0x21, 0xeb, 0xf8, 0x8f, 0xfa, 0x1d, 0x3f, 0xa1, 0xa4, 0x16, 0xb6, 0xd5, 0x39,
	0x16, 0x06, 0xa3, 0xfe, 0x08, 0xa6, 0xa2, 0x76, 0x91, 0x76, 0xa7, 0x94, 0x75, 0xe7, 0xa0, 0x9b,
	0x72, 0x43, 0xdc, 0xf4, 0xd3, 0x34, 0x5c, 0xc8, 0xa6, 0x5b, 0xcf, 0x3b, 0xa1, 0xa7, 0x61, 0xdd,
	0xab, 0x00, 0xa9, 0x56, 0x2b, 0x61, 0x83, 0xbc, 0x3d, 0x3a, 0x5b, 0x53, 0xec, 0xa9, 0xb6, 0x5b,
	0xe0, 0x71, 0x2f, 0x79, 0x07, 0x2a, 0x42, 0x9e, 0x66, 0x38, 0xb6, 0x4f, 0x6d, 0x1f, 0x5f, 0x56,
	0x50, 0xcb, 0x02, 0xba, 0x21, 0x80, 0xe4, 0x06, 0x90, 0x54, 0x16, 0x47, 0xa4, 0xa2, 0x4d, 0xc9,
	0x71, 0x92, 0x46, 0xd4, 0xfb, 0x50, 0x64, 0x5c, 0xe3, 0xd4, 0xa2, 0x86, 0x4f, 0x4d, 0xfc, 0x28,
	0x57, 0xb2, 0x1e, 0x1f, 0xf5, 0x52, 0xf1, 0xb3, 0xcd, 0x77, 0x43, 0x09, 0x2a, 0xb0, 0xf8, 0x4c,
	0x0e, 0x87, 0x4f, 0x20, 0xd3, 0x18, 0xd6, 0xf7, 0x27, 0x57, 0x32, 0x6e, 0x1a, 0xa9, 0x7f, 0x92,
	0xfd, 0x9c, 0xfc, 0x1f, 0x4a, 0x29, 0xcd, 0x51, 0x98, 0x8b, 0x09, 0xa7, 0x39, 0xfc, 0xeb, 0xa2,
	0x7c, 0x0b, 0x72, 0xd6, 0x24, 0xf2, 0x5f, 0x58, 0xc8, 0xc2, 0x44, 0x2f, 0x5f, 0x81, 0xa5, 0x2c,
	0x62, 0x4b, 0xb7, 0x78, 0xd0, 0xd3, 0x6b, 0xb0, 0x98, 0x45, 0x3d, 0xf3, 0x7a, 0x54, 0xce, 0xfd,
	0xb3, 0xdf, 0x87, 0x5f, 0xa7, 0x60, 0x75, 0xa8, 0x27, 0x9f, 0xf6, 0x28, 0x47, 0x8d, 0xeb, 0x20,
	0x1f, 0x87, 0xe7, 0x38, 0x39, 0xc4, 0x67, 0xaf, 0x1a, 0xc1, 0xa3, 0xdc, 0xf8, 0x06, 0xca, 0x31,
	0xa9, 0x7f, 0xea, 0x0a, 0x4f, 0x55, 0xda, 0x1f, 0x4c, 0x10, 0xb8, 0x48, 0x5d, 0x2b, 0x3a, 0x3c,
	0x3b, 0x75, 0xa9, 0x5a, 0x3a, 0x4e, 0xdd, 0x48, 0x07, 0x64, 0xee, 0x52, 0x83, 0xe9, 0x96, 0x16,
	0xc1, 0x31, 0x4d, 0x2b, 0xed, 0x7b, 0xe7, 0xd1, 0xb0, 0x2b, 0x64, 0x44, 0x77, 0xb5, 0xca, 0xfb,
	0x01, 0xe4, 0x53, 0x28, 0x86, 0x75, 0x83, 0xd9, 0x37, 0x85, 0xd9, 0xb7, 0x3e, 0x71, 0xf6, 0xa9,
	0x20, 0xb8, 0xb1, 0x95, 0xe9, 0x50, 0x4a, 0x5b, 0x14, 0x84, 0x26, 0x7d, 0x17, 0x21, 0xbc, 0x08,
	0xb5, 0x34, 0x70, 0x97, 0xd9, 0x87, 0x16, 0xdd, 0x38, 0x72, 0x98, 0x11, 0x44, 0xf3, 0x7f, 0x50,
	0x4f, 0x63, 0x1f, 0xf7, 0x2c, 0x9f, 0xb9, 0x31, 0x3e, 0xa7, 0xbc, 0x86, 0x6a, 0xc6, 0x24, 0xb2,
	0x0c, 0x24, 0x03, 0x8a, 0xf3, 0x2f, 0x0b, 0x77, 0xbc, 0xae, 0x6e, 0xc9, 0x12, 0xa9, 0xc3, 0x72,
	0x06, 0xb5, 0x27, 0x46, 0x42, 0x39, 0x17, 0xbc, 0x2f, 0x83, 0xdb, 0x8a, 0x66, 0x3b, 0x39, 0xaf,
	0x3c, 0x80, 0x8b, 0xd9, 0xfe, 0xda, 0x57, 0x5a, 0xc9, 0xa4, 0x54, 0xc6, 0x49, 0x29, 0x9a, 0xdc,
	0x72, 0xc9, 0xe4, 0xa6, 0xfc, 0x92, 0x87, 0xa5, 0xc1, 0x21, 0x22, 0x58, 0x06, 0x6e, 0x00, 0xe9,
	0x30, 0x8f, 0xfb, 0xda, 0x90, 0xf2, 0x94, 0x11, 0xb3, 0x99, 0xaa, 0xd1, 0x16, 0x2c, 0x70, 0x6a,
	0x38, 0xc1, 0x02, 0x90, 0x26, 0x17, 0x15, 0x3b, 0x2f, 0x50, 0x69, 0xfa, 0x7f, 0xd9, 0x56, 0xf2,
	0x7c, 0xc4, 0x56, 0x72, 0x6b, 0xf4, 0x38, 0x81, 0x8f, 0x1c, 0xb3, 0x9b, 0xbc, 0xad, 0x51, 0x58,
	0xf9, 0x39, 0x37, 0x10, 0x2e, 0x51, 0x01, 0xa4, 0x06, 0xb3, 0x27, 0xd4, 0xe3, 0xd1, 0x47, 0xac,
	0xac, 0x46, 0xd7, 0x60, 0x20, 0x13, 0x0e, 0x4e, 0x6f, 0x95, 0x6b, 0x13, 0x18, 0xa5, 0x82, 0xe0,
	0xc3, 0xb1, 0xee, 0x29, 0x54, 0x92, 0xc8, 0xa2, 0xa0, 0xfc, 0x04, 0x3b, 0x5b, 0xff, 0xf7, 0xa0,
	0x1c, 0x4b, 0x40, 0x91, 0x9f, 0xa7, 0xba, 0x56, 0xaa, 0xe0, 0xaf, 0x9f, 0xa3, 0xa7, 0x24, 0x8d,
	0x0a, 0x8b, 0x7e, 0x15, 0x2e, 0x0c, 0x0e, 0x1d, 0x82, 0x43, 0xa5, 0xc7, 0xca, 0x3e, 0x5c, 0x3c,
	0x1b, 0xcd, 0x5d, 0x5c, 0x94, 0xf0, 0x16, 0x0e, 0xcc, 0x6b, 0x13, 0xbc, 0x44, 0x0d, 0x59, 0x86,
	0xeb, 0xde, 0x70, 0xec, 0x0e, 0x3b, 0x0c, 0x74, 0xff, 0x2e, 0x0d, 0x53, 0x1e, 0xe1, 0xb9, 0x4b,
	0xb6, 0x61, 0x86, 0xbf, 0x64, 0xbe, 0x71, 0x14, 0x0e, 0x21, 0xef, 0x8d, 0x1b, 0xa6, 0x12, 0xde,
	0xd6, 0x2e, 0x32, 0xaa, 0xa1, 0x00, 0xfc, 0x87, 0xc1, 0xb3, 0xc2, 0x3a, 0x0f, 0x8e, 0x41, 0x0d,
	0x99, 0xc1, 0xec, 0xa5, 0xf5, 0x0f, 0x4c, 0x79, 0x51, 0x9e, 0x66, 0x76, 0x2c, 0x53, 0xee, 0xc0,
	0x8c, 0x90, 0x49, 0xca, 0x50, 0x10, 0x27, 0xd1, 0xc8, 0x4a, 0x30, 0x27, 0xae, 0x3b, 0xb6, 0x2c,
	0x25, 0xc8, 0x9d, 0x4e, 0x47, 0xce, 0x5d, 0x7b, 0x3d, 0x98, 0x9c, 0x38, 0xd1, 0x07, 0x7d, 0x6c,
	0x28, 0x42, 0xc8, 0x5c, 0x83, 0x4b, 0x43, 0xb1, 0x7b, 0x76, 0x87, 0xd9, 0x8c, 0x1f, 0x51, 0x53,
	0x36, 0x89, 0x32, 0xf8, 0xb9, 0x44, 0xa2, 0xad, 0x88, 0xe4, 0xcf, 0xd9, 0xf6, 0x1f, 0xd3, 0x50,
	0x7c, 0x8c, 0x4e, 0x13, 0xff, 0xb4, 0x68, 0x40, 0x06, 0x77, 0x24, 0x92, 0x89, 0xea, 0xd0, 0x2d,
	0xad, 0x7e, 0x79, 0x3c, 0x11, 0x77, 0xc9, 0x0b, 0x58, 0x1e, 0xbe, 0xa5, 0x90, 0xab, 0xe3, 0xa2,
	0x17, 0x6e, 0x52, 0xf5, 0xe6, 0x64, 0x84, 0xdc, 0x25, 0x3f, 0x48, 0xd0, 0x18, 0xb7, 0x3e, 0x90,
	0x4c, 0xd6, 0x4c, 0xb0, 0xd5, 0xd4, 0xdb, 0xe7, 0x65, 0xe1, 0x2e, 0xf9, 0x51, 0x82, 0xb5, 0x09,
	0xc6, 0x7b, 0x72, 0xe7, 0x0d, 0x36, 0x82, 0xe3, 0xfa, 0xdd, 0x37, 0xda, 0x23, 0xc8, 0x31, 0xd4,
	0xce, 0x2a, 0x6f, 0xb2, 0x3e, 0x4e, 0x64, 0xdc, 0x25, 0xea, 0xd7, 0x26, 0x25, 0x3d, 0x4b, 0xa5,
	0x28, 0xcc, 0xf1, 0x2a, 0xe3, 0xe6, 0x30, 0x5e, 0x65, 0x52, 0xeb, 0x0f, 0x6e, 0x7d, 0xdd, 0x3a,
	0x74, 0x2c, 0xdd, 0x3e, 0x6c, 0xdd, 0x6d, 0xfb, 0x7e, 0xcb, 0x70, 0xba, 0x37, 0xf1, 0x9f, 0x47,
	0xc3, 0xb1, 0x6e, 0x72, 0xea, 0x9d, 0x30, 0x83, 0xf2, 0xbe, 0x3f, 0x26, 0x0f, 0x66, 0x10, 0x7f,
	0xfb, 0xef, 0x00, 0x00, 0x00, 0xff, 0xff, 0xf4, 0xf3, 0x5b, 0x81, 0xbc, 0x14, 0x00, 0x00,
}
