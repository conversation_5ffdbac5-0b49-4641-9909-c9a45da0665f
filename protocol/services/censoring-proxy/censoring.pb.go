// Code generated by protoc-gen-go. DO NOT EDIT.
// source: censoring-proxy/censoring.proto

package censoring_proxy // import "golang.52tt.com/protocol/services/censoring-proxy"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 音视频图文混合接口
type CensoringReq struct {
	Context              *TaskContext `protobuf:"bytes,1,opt,name=context,proto3" json:"context,omitempty"`
	TextData             []*TextData  `protobuf:"bytes,2,rep,name=text_data,json=textData,proto3" json:"text_data,omitempty"`
	ImageDatas           []*ImageData `protobuf:"bytes,3,rep,name=image_datas,json=imageDatas,proto3" json:"image_datas,omitempty"`
	AudioData            *AudioData   `protobuf:"bytes,4,opt,name=audio_data,json=audioData,proto3" json:"audio_data,omitempty"`
	VideoData            *VideoData   `protobuf:"bytes,5,opt,name=video_data,json=videoData,proto3" json:"video_data,omitempty"`
	Callback             *Callback    `protobuf:"bytes,6,opt,name=Callback,proto3" json:"Callback,omitempty"`
	DataType             MixDataType  `protobuf:"varint,7,opt,name=data_type,json=dataType,proto3,enum=censoring_proxy.MixDataType" json:"data_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CensoringReq) Reset()         { *m = CensoringReq{} }
func (m *CensoringReq) String() string { return proto.CompactTextString(m) }
func (*CensoringReq) ProtoMessage()    {}
func (*CensoringReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_censoring_8cddbdf3b3a1c653, []int{0}
}
func (m *CensoringReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CensoringReq.Unmarshal(m, b)
}
func (m *CensoringReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CensoringReq.Marshal(b, m, deterministic)
}
func (dst *CensoringReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CensoringReq.Merge(dst, src)
}
func (m *CensoringReq) XXX_Size() int {
	return xxx_messageInfo_CensoringReq.Size(m)
}
func (m *CensoringReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CensoringReq.DiscardUnknown(m)
}

var xxx_messageInfo_CensoringReq proto.InternalMessageInfo

func (m *CensoringReq) GetContext() *TaskContext {
	if m != nil {
		return m.Context
	}
	return nil
}

func (m *CensoringReq) GetTextData() []*TextData {
	if m != nil {
		return m.TextData
	}
	return nil
}

func (m *CensoringReq) GetImageDatas() []*ImageData {
	if m != nil {
		return m.ImageDatas
	}
	return nil
}

func (m *CensoringReq) GetAudioData() *AudioData {
	if m != nil {
		return m.AudioData
	}
	return nil
}

func (m *CensoringReq) GetVideoData() *VideoData {
	if m != nil {
		return m.VideoData
	}
	return nil
}

func (m *CensoringReq) GetCallback() *Callback {
	if m != nil {
		return m.Callback
	}
	return nil
}

func (m *CensoringReq) GetDataType() MixDataType {
	if m != nil {
		return m.DataType
	}
	return MixDataType_DATA_TYPE_UNSPECIFIC
}

type CensoringResp struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CensoringResp) Reset()         { *m = CensoringResp{} }
func (m *CensoringResp) String() string { return proto.CompactTextString(m) }
func (*CensoringResp) ProtoMessage()    {}
func (*CensoringResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_censoring_8cddbdf3b3a1c653, []int{1}
}
func (m *CensoringResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CensoringResp.Unmarshal(m, b)
}
func (m *CensoringResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CensoringResp.Marshal(b, m, deterministic)
}
func (dst *CensoringResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CensoringResp.Merge(dst, src)
}
func (m *CensoringResp) XXX_Size() int {
	return xxx_messageInfo_CensoringResp.Size(m)
}
func (m *CensoringResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CensoringResp.DiscardUnknown(m)
}

var xxx_messageInfo_CensoringResp proto.InternalMessageInfo

func (m *CensoringResp) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func init() {
	proto.RegisterType((*CensoringReq)(nil), "censoring_proxy.CensoringReq")
	proto.RegisterType((*CensoringResp)(nil), "censoring_proxy.CensoringResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// CensoringClient is the client API for Censoring service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type CensoringClient interface {
	AsyncScanMix(ctx context.Context, in *CensoringReq, opts ...grpc.CallOption) (*CensoringResp, error)
}

type censoringClient struct {
	cc *grpc.ClientConn
}

func NewCensoringClient(cc *grpc.ClientConn) CensoringClient {
	return &censoringClient{cc}
}

func (c *censoringClient) AsyncScanMix(ctx context.Context, in *CensoringReq, opts ...grpc.CallOption) (*CensoringResp, error) {
	out := new(CensoringResp)
	err := c.cc.Invoke(ctx, "/censoring_proxy.Censoring/AsyncScanMix", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CensoringServer is the server API for Censoring service.
type CensoringServer interface {
	AsyncScanMix(context.Context, *CensoringReq) (*CensoringResp, error)
}

func RegisterCensoringServer(s *grpc.Server, srv CensoringServer) {
	s.RegisterService(&_Censoring_serviceDesc, srv)
}

func _Censoring_AsyncScanMix_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CensoringReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CensoringServer).AsyncScanMix(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/censoring_proxy.Censoring/AsyncScanMix",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CensoringServer).AsyncScanMix(ctx, req.(*CensoringReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Censoring_serviceDesc = grpc.ServiceDesc{
	ServiceName: "censoring_proxy.Censoring",
	HandlerType: (*CensoringServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AsyncScanMix",
			Handler:    _Censoring_AsyncScanMix_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "censoring-proxy/censoring.proto",
}

func init() {
	proto.RegisterFile("censoring-proxy/censoring.proto", fileDescriptor_censoring_8cddbdf3b3a1c653)
}

var fileDescriptor_censoring_8cddbdf3b3a1c653 = []byte{
	// 390 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x7c, 0x92, 0xbf, 0x6e, 0xea, 0x30,
	0x14, 0x87, 0xc5, 0xe5, 0x5e, 0x20, 0x86, 0xdb, 0x4a, 0x5e, 0x9a, 0xa6, 0x94, 0x22, 0xa6, 0x2c,
	0x4d, 0xd4, 0x20, 0x90, 0x50, 0x27, 0x4a, 0x17, 0x06, 0x96, 0x14, 0x75, 0x60, 0x89, 0x8c, 0x63,
	0x45, 0x16, 0x21, 0x4e, 0x63, 0x17, 0x25, 0xcf, 0xd3, 0x17, 0xad, 0xec, 0xfc, 0x11, 0x4a, 0x4a,
	0x27, 0x8e, 0xf9, 0x7d, 0x5f, 0x8e, 0xed, 0x63, 0xf0, 0x80, 0x49, 0xc4, 0x59, 0x42, 0xa3, 0xe0,
	0x31, 0x4e, 0x58, 0x9a, 0xd9, 0xd5, 0xda, 0x8a, 0x13, 0x26, 0x18, 0xbc, 0xae, 0xfe, 0xf0, 0x14,
	0x60, 0x0c, 0xeb, 0x46, 0x10, 0xb2, 0x3d, 0x0a, 0x73, 0xdc, 0x18, 0x35, 0xbe, 0x87, 0xc2, 0x70,
	0x8f, 0xf0, 0xa1, 0xc8, 0x8d, 0x7a, 0x2e, 0x48, 0x2a, 0x8a, 0xec, 0xae, 0x9e, 0xa1, 0x4f, 0x9f,
	0xb2, 0x4b, 0xe1, 0x89, 0xfa, 0xe4, 0x62, 0x48, 0x8f, 0x28, 0x20, 0x79, 0x38, 0xf9, 0x6a, 0x83,
	0xc1, 0xaa, 0xcc, 0x5d, 0xf2, 0x01, 0xe7, 0xa0, 0x8b, 0x59, 0x24, 0x1b, 0xeb, 0xad, 0x71, 0xcb,
	0xec, 0x3b, 0x43, 0xab, 0x76, 0x48, 0x6b, 0x8b, 0xf8, 0x61, 0x95, 0x33, 0x6e, 0x09, 0xc3, 0x39,
	0xd0, 0xe4, 0xaf, 0xe7, 0x23, 0x81, 0xf4, 0x3f, 0xe3, 0xb6, 0xd9, 0x77, 0x6e, 0x9b, 0x26, 0x49,
	0xc5, 0x2b, 0x12, 0xc8, 0xed, 0x89, 0xa2, 0x82, 0xcf, 0xa0, 0xaf, 0xf6, 0xa3, 0x44, 0xae, 0xb7,
	0x95, 0x69, 0x34, 0xcc, 0xb5, 0x64, 0x94, 0x0a, 0x68, 0x59, 0x72, 0xb8, 0x00, 0x40, 0x5d, 0x43,
	0xde, 0xf5, 0xaf, 0xda, 0x6f, 0xd3, 0x5d, 0x4a, 0x44, 0xb9, 0x1a, 0x2a, 0x4b, 0xa9, 0xaa, 0x4b,
	0xca, 0xd5, 0x7f, 0x17, 0xd4, 0x77, 0x89, 0xe4, 0xea, 0xa9, 0x2c, 0xe1, 0x0c, 0xf4, 0x56, 0xc5,
	0xe0, 0xf4, 0x8e, 0x12, 0x9b, 0x27, 0x2d, 0x01, 0xb7, 0x42, 0xe1, 0x02, 0x68, 0xb2, 0x97, 0x27,
	0xb2, 0x98, 0xe8, 0xdd, 0x71, 0xcb, 0xbc, 0xfa, 0xe1, 0x6e, 0x37, 0x34, 0x95, 0x3d, 0xb6, 0x59,
	0x4c, 0xdc, 0x9e, 0x5f, 0x54, 0x13, 0x13, 0xfc, 0x3f, 0x1b, 0x12, 0x8f, 0xe1, 0x0d, 0xe8, 0x0a,
	0xc4, 0x0f, 0x1e, 0xf5, 0xd5, 0x94, 0x34, 0xb7, 0x23, 0x97, 0x6b, 0xdf, 0xd9, 0x01, 0xad, 0x22,
	0xe1, 0x06, 0x0c, 0x96, 0x3c, 0x8b, 0xf0, 0x1b, 0x46, 0xd1, 0x86, 0xa6, 0xf0, 0xbe, 0xb9, 0xcd,
	0xb3, 0xd1, 0x1b, 0xa3, 0xdf, 0x62, 0x1e, 0xbf, 0x4c, 0x77, 0x4f, 0x01, 0x0b, 0x51, 0x14, 0x58,
	0x33, 0x47, 0x08, 0x0b, 0xb3, 0xa3, 0xad, 0x1e, 0x11, 0x66, 0xa1, 0xcd, 0x49, 0x72, 0xa2, 0x98,
	0x70, 0xbb, 0xf6, 0xd8, 0xf6, 0x1d, 0x85, 0x4c, 0xbf, 0x03, 0x00, 0x00, 0xff, 0xff, 0x64, 0x11,
	0x34, 0x7a, 0x4c, 0x03, 0x00, 0x00,
}
