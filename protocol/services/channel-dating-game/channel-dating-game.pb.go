// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-dating-game/channel-dating-game.proto

package channel_dating_game // import "golang.52tt.com/protocol/services/channel-dating-game"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SortByType int32

const (
	SortByType_SORT_TYPE_UNSPECIFIED SortByType = 0
	SortByType_SORT_TYPE_TBEAN       SortByType = 1
)

var SortByType_name = map[int32]string{
	0: "SORT_TYPE_UNSPECIFIED",
	1: "SORT_TYPE_TBEAN",
}
var SortByType_value = map[string]int32{
	"SORT_TYPE_UNSPECIFIED": 0,
	"SORT_TYPE_TBEAN":       1,
}

func (x SortByType) String() string {
	return proto.EnumName(SortByType_name, int32(x))
}
func (SortByType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{0}
}

type TimeType int32

const (
	TimeType_TIME_LIMIT_TYPE_UNSPECIFIED TimeType = 0
	TimeType_TIME_LIMIT_TYPE_LONG_TERM   TimeType = 1
	TimeType_TIME_LIMIT_TYPE_TIME_LIMIT  TimeType = 2
)

var TimeType_name = map[int32]string{
	0: "TIME_LIMIT_TYPE_UNSPECIFIED",
	1: "TIME_LIMIT_TYPE_LONG_TERM",
	2: "TIME_LIMIT_TYPE_TIME_LIMIT",
}
var TimeType_value = map[string]int32{
	"TIME_LIMIT_TYPE_UNSPECIFIED": 0,
	"TIME_LIMIT_TYPE_LONG_TERM":   1,
	"TIME_LIMIT_TYPE_TIME_LIMIT":  2,
}

func (x TimeType) String() string {
	return proto.EnumName(TimeType_name, int32(x))
}
func (TimeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{1}
}

type DrawState int32

const (
	DrawState_DRAW_STATE_UNSPECIFIED DrawState = 0
	DrawState_DRAW_STATE_IN_USE      DrawState = 1
	DrawState_DRAW_STATE_PENDING     DrawState = 2
	DrawState_DRAW_STATE_OFF         DrawState = 3
)

var DrawState_name = map[int32]string{
	0: "DRAW_STATE_UNSPECIFIED",
	1: "DRAW_STATE_IN_USE",
	2: "DRAW_STATE_PENDING",
	3: "DRAW_STATE_OFF",
}
var DrawState_value = map[string]int32{
	"DRAW_STATE_UNSPECIFIED": 0,
	"DRAW_STATE_IN_USE":      1,
	"DRAW_STATE_PENDING":     2,
	"DRAW_STATE_OFF":         3,
}

func (x DrawState) String() string {
	return proto.EnumName(DrawState_name, int32(x))
}
func (DrawState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{2}
}

type HatUser struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	HatCfg               *DatingGameHatCfg `protobuf:"bytes,2,opt,name=hat_cfg,json=hatCfg,proto3" json:"hat_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *HatUser) Reset()         { *m = HatUser{} }
func (m *HatUser) String() string { return proto.CompactTextString(m) }
func (*HatUser) ProtoMessage()    {}
func (*HatUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{0}
}
func (m *HatUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HatUser.Unmarshal(m, b)
}
func (m *HatUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HatUser.Marshal(b, m, deterministic)
}
func (dst *HatUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HatUser.Merge(dst, src)
}
func (m *HatUser) XXX_Size() int {
	return xxx_messageInfo_HatUser.Size(m)
}
func (m *HatUser) XXX_DiscardUnknown() {
	xxx_messageInfo_HatUser.DiscardUnknown(m)
}

var xxx_messageInfo_HatUser proto.InternalMessageInfo

func (m *HatUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HatUser) GetHatCfg() *DatingGameHatCfg {
	if m != nil {
		return m.HatCfg
	}
	return nil
}

// 获取相亲房信息
// 用于进房的时候拉取初始信息（比如麦上用户的心动值 / 当前帽子用户 / 当前土豪位用户 / 当前阶段 ）
type GetDatingGameCurInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDatingGameCurInfoReq) Reset()         { *m = GetDatingGameCurInfoReq{} }
func (m *GetDatingGameCurInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetDatingGameCurInfoReq) ProtoMessage()    {}
func (*GetDatingGameCurInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{1}
}
func (m *GetDatingGameCurInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDatingGameCurInfoReq.Unmarshal(m, b)
}
func (m *GetDatingGameCurInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDatingGameCurInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetDatingGameCurInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDatingGameCurInfoReq.Merge(dst, src)
}
func (m *GetDatingGameCurInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetDatingGameCurInfoReq.Size(m)
}
func (m *GetDatingGameCurInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDatingGameCurInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDatingGameCurInfoReq proto.InternalMessageInfo

func (m *GetDatingGameCurInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetDatingGameCurInfoResp struct {
	Phase                uint32              `protobuf:"varint,1,opt,name=phase,proto3" json:"phase,omitempty"`
	VipUid               uint32              `protobuf:"varint,2,opt,name=vip_uid,json=vipUid,proto3" json:"vip_uid,omitempty"`
	HatUserList          []*HatUser          `protobuf:"bytes,3,rep,name=hat_user_list,json=hatUserList,proto3" json:"hat_user_list,omitempty"`
	LikeBeatList         []*UserLikeBeatInfo `protobuf:"bytes,4,rep,name=like_beat_list,json=likeBeatList,proto3" json:"like_beat_list,omitempty"`
	OpenLikeUserList     []*OpenLikeUserInfo `protobuf:"bytes,5,rep,name=open_like_user_list,json=openLikeUserList,proto3" json:"open_like_user_list,omitempty"`
	ApplyMicLen          uint32              `protobuf:"varint,6,opt,name=apply_mic_len,json=applyMicLen,proto3" json:"apply_mic_len,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetDatingGameCurInfoResp) Reset()         { *m = GetDatingGameCurInfoResp{} }
func (m *GetDatingGameCurInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetDatingGameCurInfoResp) ProtoMessage()    {}
func (*GetDatingGameCurInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{2}
}
func (m *GetDatingGameCurInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDatingGameCurInfoResp.Unmarshal(m, b)
}
func (m *GetDatingGameCurInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDatingGameCurInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetDatingGameCurInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDatingGameCurInfoResp.Merge(dst, src)
}
func (m *GetDatingGameCurInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetDatingGameCurInfoResp.Size(m)
}
func (m *GetDatingGameCurInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDatingGameCurInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDatingGameCurInfoResp proto.InternalMessageInfo

func (m *GetDatingGameCurInfoResp) GetPhase() uint32 {
	if m != nil {
		return m.Phase
	}
	return 0
}

func (m *GetDatingGameCurInfoResp) GetVipUid() uint32 {
	if m != nil {
		return m.VipUid
	}
	return 0
}

func (m *GetDatingGameCurInfoResp) GetHatUserList() []*HatUser {
	if m != nil {
		return m.HatUserList
	}
	return nil
}

func (m *GetDatingGameCurInfoResp) GetLikeBeatList() []*UserLikeBeatInfo {
	if m != nil {
		return m.LikeBeatList
	}
	return nil
}

func (m *GetDatingGameCurInfoResp) GetOpenLikeUserList() []*OpenLikeUserInfo {
	if m != nil {
		return m.OpenLikeUserList
	}
	return nil
}

func (m *GetDatingGameCurInfoResp) GetApplyMicLen() uint32 {
	if m != nil {
		return m.ApplyMicLen
	}
	return 0
}

// 开启相亲游戏入口
type OpenDatingGameEntryReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OpenDatingGameEntryReq) Reset()         { *m = OpenDatingGameEntryReq{} }
func (m *OpenDatingGameEntryReq) String() string { return proto.CompactTextString(m) }
func (*OpenDatingGameEntryReq) ProtoMessage()    {}
func (*OpenDatingGameEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{3}
}
func (m *OpenDatingGameEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenDatingGameEntryReq.Unmarshal(m, b)
}
func (m *OpenDatingGameEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenDatingGameEntryReq.Marshal(b, m, deterministic)
}
func (dst *OpenDatingGameEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenDatingGameEntryReq.Merge(dst, src)
}
func (m *OpenDatingGameEntryReq) XXX_Size() int {
	return xxx_messageInfo_OpenDatingGameEntryReq.Size(m)
}
func (m *OpenDatingGameEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenDatingGameEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_OpenDatingGameEntryReq proto.InternalMessageInfo

func (m *OpenDatingGameEntryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OpenDatingGameEntryReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type OpenDatingGameEntryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OpenDatingGameEntryResp) Reset()         { *m = OpenDatingGameEntryResp{} }
func (m *OpenDatingGameEntryResp) String() string { return proto.CompactTextString(m) }
func (*OpenDatingGameEntryResp) ProtoMessage()    {}
func (*OpenDatingGameEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{4}
}
func (m *OpenDatingGameEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenDatingGameEntryResp.Unmarshal(m, b)
}
func (m *OpenDatingGameEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenDatingGameEntryResp.Marshal(b, m, deterministic)
}
func (dst *OpenDatingGameEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenDatingGameEntryResp.Merge(dst, src)
}
func (m *OpenDatingGameEntryResp) XXX_Size() int {
	return xxx_messageInfo_OpenDatingGameEntryResp.Size(m)
}
func (m *OpenDatingGameEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenDatingGameEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_OpenDatingGameEntryResp proto.InternalMessageInfo

type CloseDatingGameEntryReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CloseDatingGameEntryReq) Reset()         { *m = CloseDatingGameEntryReq{} }
func (m *CloseDatingGameEntryReq) String() string { return proto.CompactTextString(m) }
func (*CloseDatingGameEntryReq) ProtoMessage()    {}
func (*CloseDatingGameEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{5}
}
func (m *CloseDatingGameEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CloseDatingGameEntryReq.Unmarshal(m, b)
}
func (m *CloseDatingGameEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CloseDatingGameEntryReq.Marshal(b, m, deterministic)
}
func (dst *CloseDatingGameEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CloseDatingGameEntryReq.Merge(dst, src)
}
func (m *CloseDatingGameEntryReq) XXX_Size() int {
	return xxx_messageInfo_CloseDatingGameEntryReq.Size(m)
}
func (m *CloseDatingGameEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CloseDatingGameEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_CloseDatingGameEntryReq proto.InternalMessageInfo

func (m *CloseDatingGameEntryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CloseDatingGameEntryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CloseDatingGameEntryResp) Reset()         { *m = CloseDatingGameEntryResp{} }
func (m *CloseDatingGameEntryResp) String() string { return proto.CompactTextString(m) }
func (*CloseDatingGameEntryResp) ProtoMessage()    {}
func (*CloseDatingGameEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{6}
}
func (m *CloseDatingGameEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CloseDatingGameEntryResp.Unmarshal(m, b)
}
func (m *CloseDatingGameEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CloseDatingGameEntryResp.Marshal(b, m, deterministic)
}
func (dst *CloseDatingGameEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CloseDatingGameEntryResp.Merge(dst, src)
}
func (m *CloseDatingGameEntryResp) XXX_Size() int {
	return xxx_messageInfo_CloseDatingGameEntryResp.Size(m)
}
func (m *CloseDatingGameEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CloseDatingGameEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_CloseDatingGameEntryResp proto.InternalMessageInfo

type CheckDatingGameEntryReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckDatingGameEntryReq) Reset()         { *m = CheckDatingGameEntryReq{} }
func (m *CheckDatingGameEntryReq) String() string { return proto.CompactTextString(m) }
func (*CheckDatingGameEntryReq) ProtoMessage()    {}
func (*CheckDatingGameEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{7}
}
func (m *CheckDatingGameEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckDatingGameEntryReq.Unmarshal(m, b)
}
func (m *CheckDatingGameEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckDatingGameEntryReq.Marshal(b, m, deterministic)
}
func (dst *CheckDatingGameEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckDatingGameEntryReq.Merge(dst, src)
}
func (m *CheckDatingGameEntryReq) XXX_Size() int {
	return xxx_messageInfo_CheckDatingGameEntryReq.Size(m)
}
func (m *CheckDatingGameEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckDatingGameEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckDatingGameEntryReq proto.InternalMessageInfo

func (m *CheckDatingGameEntryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CheckDatingGameEntryResp struct {
	IsOpen               bool     `protobuf:"varint,1,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckDatingGameEntryResp) Reset()         { *m = CheckDatingGameEntryResp{} }
func (m *CheckDatingGameEntryResp) String() string { return proto.CompactTextString(m) }
func (*CheckDatingGameEntryResp) ProtoMessage()    {}
func (*CheckDatingGameEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{8}
}
func (m *CheckDatingGameEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckDatingGameEntryResp.Unmarshal(m, b)
}
func (m *CheckDatingGameEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckDatingGameEntryResp.Marshal(b, m, deterministic)
}
func (dst *CheckDatingGameEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckDatingGameEntryResp.Merge(dst, src)
}
func (m *CheckDatingGameEntryResp) XXX_Size() int {
	return xxx_messageInfo_CheckDatingGameEntryResp.Size(m)
}
func (m *CheckDatingGameEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckDatingGameEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckDatingGameEntryResp proto.InternalMessageInfo

func (m *CheckDatingGameEntryResp) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

func (m *CheckDatingGameEntryResp) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

// 设置当前阶段
type SetGamePhaseReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TargetPhase          uint32   `protobuf:"varint,2,opt,name=target_phase,json=targetPhase,proto3" json:"target_phase,omitempty"`
	OpUid                uint32   `protobuf:"varint,3,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGamePhaseReq) Reset()         { *m = SetGamePhaseReq{} }
func (m *SetGamePhaseReq) String() string { return proto.CompactTextString(m) }
func (*SetGamePhaseReq) ProtoMessage()    {}
func (*SetGamePhaseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{9}
}
func (m *SetGamePhaseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGamePhaseReq.Unmarshal(m, b)
}
func (m *SetGamePhaseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGamePhaseReq.Marshal(b, m, deterministic)
}
func (dst *SetGamePhaseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGamePhaseReq.Merge(dst, src)
}
func (m *SetGamePhaseReq) XXX_Size() int {
	return xxx_messageInfo_SetGamePhaseReq.Size(m)
}
func (m *SetGamePhaseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGamePhaseReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGamePhaseReq proto.InternalMessageInfo

func (m *SetGamePhaseReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetGamePhaseReq) GetTargetPhase() uint32 {
	if m != nil {
		return m.TargetPhase
	}
	return 0
}

func (m *SetGamePhaseReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

type SetGamePhaseResp struct {
	FromPhase            uint32   `protobuf:"varint,1,opt,name=from_phase,json=fromPhase,proto3" json:"from_phase,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGamePhaseResp) Reset()         { *m = SetGamePhaseResp{} }
func (m *SetGamePhaseResp) String() string { return proto.CompactTextString(m) }
func (*SetGamePhaseResp) ProtoMessage()    {}
func (*SetGamePhaseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{10}
}
func (m *SetGamePhaseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGamePhaseResp.Unmarshal(m, b)
}
func (m *SetGamePhaseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGamePhaseResp.Marshal(b, m, deterministic)
}
func (dst *SetGamePhaseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGamePhaseResp.Merge(dst, src)
}
func (m *SetGamePhaseResp) XXX_Size() int {
	return xxx_messageInfo_SetGamePhaseResp.Size(m)
}
func (m *SetGamePhaseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGamePhaseResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGamePhaseResp proto.InternalMessageInfo

func (m *SetGamePhaseResp) GetFromPhase() uint32 {
	if m != nil {
		return m.FromPhase
	}
	return 0
}

type GetGamePhaseReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGamePhaseReq) Reset()         { *m = GetGamePhaseReq{} }
func (m *GetGamePhaseReq) String() string { return proto.CompactTextString(m) }
func (*GetGamePhaseReq) ProtoMessage()    {}
func (*GetGamePhaseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{11}
}
func (m *GetGamePhaseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGamePhaseReq.Unmarshal(m, b)
}
func (m *GetGamePhaseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGamePhaseReq.Marshal(b, m, deterministic)
}
func (dst *GetGamePhaseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGamePhaseReq.Merge(dst, src)
}
func (m *GetGamePhaseReq) XXX_Size() int {
	return xxx_messageInfo_GetGamePhaseReq.Size(m)
}
func (m *GetGamePhaseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGamePhaseReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGamePhaseReq proto.InternalMessageInfo

func (m *GetGamePhaseReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetGamePhaseResp struct {
	CurrPhase            uint32   `protobuf:"varint,1,opt,name=curr_phase,json=currPhase,proto3" json:"curr_phase,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGamePhaseResp) Reset()         { *m = GetGamePhaseResp{} }
func (m *GetGamePhaseResp) String() string { return proto.CompactTextString(m) }
func (*GetGamePhaseResp) ProtoMessage()    {}
func (*GetGamePhaseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{12}
}
func (m *GetGamePhaseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGamePhaseResp.Unmarshal(m, b)
}
func (m *GetGamePhaseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGamePhaseResp.Marshal(b, m, deterministic)
}
func (dst *GetGamePhaseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGamePhaseResp.Merge(dst, src)
}
func (m *GetGamePhaseResp) XXX_Size() int {
	return xxx_messageInfo_GetGamePhaseResp.Size(m)
}
func (m *GetGamePhaseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGamePhaseResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGamePhaseResp proto.InternalMessageInfo

func (m *GetGamePhaseResp) GetCurrPhase() uint32 {
	if m != nil {
		return m.CurrPhase
	}
	return 0
}

// 初始化相亲游戏的成员(用于初始化 游戏开始时 已经在麦上的)
type DatingMember struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MicId                uint32   `protobuf:"varint,2,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DatingMember) Reset()         { *m = DatingMember{} }
func (m *DatingMember) String() string { return proto.CompactTextString(m) }
func (*DatingMember) ProtoMessage()    {}
func (*DatingMember) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{13}
}
func (m *DatingMember) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DatingMember.Unmarshal(m, b)
}
func (m *DatingMember) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DatingMember.Marshal(b, m, deterministic)
}
func (dst *DatingMember) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DatingMember.Merge(dst, src)
}
func (m *DatingMember) XXX_Size() int {
	return xxx_messageInfo_DatingMember.Size(m)
}
func (m *DatingMember) XXX_DiscardUnknown() {
	xxx_messageInfo_DatingMember.DiscardUnknown(m)
}

var xxx_messageInfo_DatingMember proto.InternalMessageInfo

func (m *DatingMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DatingMember) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *DatingMember) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type InitDatingMemberReq struct {
	ChannelId            uint32          `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MemberList           []*DatingMember `protobuf:"bytes,2,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *InitDatingMemberReq) Reset()         { *m = InitDatingMemberReq{} }
func (m *InitDatingMemberReq) String() string { return proto.CompactTextString(m) }
func (*InitDatingMemberReq) ProtoMessage()    {}
func (*InitDatingMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{14}
}
func (m *InitDatingMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InitDatingMemberReq.Unmarshal(m, b)
}
func (m *InitDatingMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InitDatingMemberReq.Marshal(b, m, deterministic)
}
func (dst *InitDatingMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InitDatingMemberReq.Merge(dst, src)
}
func (m *InitDatingMemberReq) XXX_Size() int {
	return xxx_messageInfo_InitDatingMemberReq.Size(m)
}
func (m *InitDatingMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InitDatingMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_InitDatingMemberReq proto.InternalMessageInfo

func (m *InitDatingMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *InitDatingMemberReq) GetMemberList() []*DatingMember {
	if m != nil {
		return m.MemberList
	}
	return nil
}

type InitDatingMemberResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InitDatingMemberResp) Reset()         { *m = InitDatingMemberResp{} }
func (m *InitDatingMemberResp) String() string { return proto.CompactTextString(m) }
func (*InitDatingMemberResp) ProtoMessage()    {}
func (*InitDatingMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{15}
}
func (m *InitDatingMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InitDatingMemberResp.Unmarshal(m, b)
}
func (m *InitDatingMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InitDatingMemberResp.Marshal(b, m, deterministic)
}
func (dst *InitDatingMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InitDatingMemberResp.Merge(dst, src)
}
func (m *InitDatingMemberResp) XXX_Size() int {
	return xxx_messageInfo_InitDatingMemberResp.Size(m)
}
func (m *InitDatingMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_InitDatingMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_InitDatingMemberResp proto.InternalMessageInfo

// 获取心动值
type GetUserLikeBeatValReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserLikeBeatValReq) Reset()         { *m = GetUserLikeBeatValReq{} }
func (m *GetUserLikeBeatValReq) String() string { return proto.CompactTextString(m) }
func (*GetUserLikeBeatValReq) ProtoMessage()    {}
func (*GetUserLikeBeatValReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{16}
}
func (m *GetUserLikeBeatValReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLikeBeatValReq.Unmarshal(m, b)
}
func (m *GetUserLikeBeatValReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLikeBeatValReq.Marshal(b, m, deterministic)
}
func (dst *GetUserLikeBeatValReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLikeBeatValReq.Merge(dst, src)
}
func (m *GetUserLikeBeatValReq) XXX_Size() int {
	return xxx_messageInfo_GetUserLikeBeatValReq.Size(m)
}
func (m *GetUserLikeBeatValReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLikeBeatValReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLikeBeatValReq proto.InternalMessageInfo

func (m *GetUserLikeBeatValReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetUserLikeBeatValReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type GetUserLikeBeatValResp struct {
	LikeBeatInfoList     []*UserLikeBeatInfo `protobuf:"bytes,2,rep,name=like_beat_info_list,json=likeBeatInfoList,proto3" json:"like_beat_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetUserLikeBeatValResp) Reset()         { *m = GetUserLikeBeatValResp{} }
func (m *GetUserLikeBeatValResp) String() string { return proto.CompactTextString(m) }
func (*GetUserLikeBeatValResp) ProtoMessage()    {}
func (*GetUserLikeBeatValResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{17}
}
func (m *GetUserLikeBeatValResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLikeBeatValResp.Unmarshal(m, b)
}
func (m *GetUserLikeBeatValResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLikeBeatValResp.Marshal(b, m, deterministic)
}
func (dst *GetUserLikeBeatValResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLikeBeatValResp.Merge(dst, src)
}
func (m *GetUserLikeBeatValResp) XXX_Size() int {
	return xxx_messageInfo_GetUserLikeBeatValResp.Size(m)
}
func (m *GetUserLikeBeatValResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLikeBeatValResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLikeBeatValResp proto.InternalMessageInfo

func (m *GetUserLikeBeatValResp) GetLikeBeatInfoList() []*UserLikeBeatInfo {
	if m != nil {
		return m.LikeBeatInfoList
	}
	return nil
}

type GetUserRankLikeBeatValReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRankLikeBeatValReq) Reset()         { *m = GetUserRankLikeBeatValReq{} }
func (m *GetUserRankLikeBeatValReq) String() string { return proto.CompactTextString(m) }
func (*GetUserRankLikeBeatValReq) ProtoMessage()    {}
func (*GetUserRankLikeBeatValReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{18}
}
func (m *GetUserRankLikeBeatValReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRankLikeBeatValReq.Unmarshal(m, b)
}
func (m *GetUserRankLikeBeatValReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRankLikeBeatValReq.Marshal(b, m, deterministic)
}
func (dst *GetUserRankLikeBeatValReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRankLikeBeatValReq.Merge(dst, src)
}
func (m *GetUserRankLikeBeatValReq) XXX_Size() int {
	return xxx_messageInfo_GetUserRankLikeBeatValReq.Size(m)
}
func (m *GetUserRankLikeBeatValReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRankLikeBeatValReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRankLikeBeatValReq proto.InternalMessageInfo

func (m *GetUserRankLikeBeatValReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetUserRankLikeBeatValResp struct {
	LikeBeatInfoList     []*UserLikeBeatInfo `protobuf:"bytes,2,rep,name=like_beat_info_list,json=likeBeatInfoList,proto3" json:"like_beat_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetUserRankLikeBeatValResp) Reset()         { *m = GetUserRankLikeBeatValResp{} }
func (m *GetUserRankLikeBeatValResp) String() string { return proto.CompactTextString(m) }
func (*GetUserRankLikeBeatValResp) ProtoMessage()    {}
func (*GetUserRankLikeBeatValResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{19}
}
func (m *GetUserRankLikeBeatValResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRankLikeBeatValResp.Unmarshal(m, b)
}
func (m *GetUserRankLikeBeatValResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRankLikeBeatValResp.Marshal(b, m, deterministic)
}
func (dst *GetUserRankLikeBeatValResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRankLikeBeatValResp.Merge(dst, src)
}
func (m *GetUserRankLikeBeatValResp) XXX_Size() int {
	return xxx_messageInfo_GetUserRankLikeBeatValResp.Size(m)
}
func (m *GetUserRankLikeBeatValResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRankLikeBeatValResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRankLikeBeatValResp proto.InternalMessageInfo

func (m *GetUserRankLikeBeatValResp) GetLikeBeatInfoList() []*UserLikeBeatInfo {
	if m != nil {
		return m.LikeBeatInfoList
	}
	return nil
}

type UserLikeBeatInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	LikeBeatVal          uint32   `protobuf:"varint,2,opt,name=like_beat_val,json=likeBeatVal,proto3" json:"like_beat_val,omitempty"`
	SelectStatus         bool     `protobuf:"varint,3,opt,name=select_status,json=selectStatus,proto3" json:"select_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserLikeBeatInfo) Reset()         { *m = UserLikeBeatInfo{} }
func (m *UserLikeBeatInfo) String() string { return proto.CompactTextString(m) }
func (*UserLikeBeatInfo) ProtoMessage()    {}
func (*UserLikeBeatInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{20}
}
func (m *UserLikeBeatInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLikeBeatInfo.Unmarshal(m, b)
}
func (m *UserLikeBeatInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLikeBeatInfo.Marshal(b, m, deterministic)
}
func (dst *UserLikeBeatInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLikeBeatInfo.Merge(dst, src)
}
func (m *UserLikeBeatInfo) XXX_Size() int {
	return xxx_messageInfo_UserLikeBeatInfo.Size(m)
}
func (m *UserLikeBeatInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLikeBeatInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserLikeBeatInfo proto.InternalMessageInfo

func (m *UserLikeBeatInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserLikeBeatInfo) GetLikeBeatVal() uint32 {
	if m != nil {
		return m.LikeBeatVal
	}
	return 0
}

func (m *UserLikeBeatInfo) GetSelectStatus() bool {
	if m != nil {
		return m.SelectStatus
	}
	return false
}

// 设置心动对象
type SetUserLikeBeatObjReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SelectUid            uint32   `protobuf:"varint,2,opt,name=select_uid,json=selectUid,proto3" json:"select_uid,omitempty"`
	LikeUid              uint32   `protobuf:"varint,3,opt,name=like_uid,json=likeUid,proto3" json:"like_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserLikeBeatObjReq) Reset()         { *m = SetUserLikeBeatObjReq{} }
func (m *SetUserLikeBeatObjReq) String() string { return proto.CompactTextString(m) }
func (*SetUserLikeBeatObjReq) ProtoMessage()    {}
func (*SetUserLikeBeatObjReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{21}
}
func (m *SetUserLikeBeatObjReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserLikeBeatObjReq.Unmarshal(m, b)
}
func (m *SetUserLikeBeatObjReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserLikeBeatObjReq.Marshal(b, m, deterministic)
}
func (dst *SetUserLikeBeatObjReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserLikeBeatObjReq.Merge(dst, src)
}
func (m *SetUserLikeBeatObjReq) XXX_Size() int {
	return xxx_messageInfo_SetUserLikeBeatObjReq.Size(m)
}
func (m *SetUserLikeBeatObjReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserLikeBeatObjReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserLikeBeatObjReq proto.InternalMessageInfo

func (m *SetUserLikeBeatObjReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetUserLikeBeatObjReq) GetSelectUid() uint32 {
	if m != nil {
		return m.SelectUid
	}
	return 0
}

func (m *SetUserLikeBeatObjReq) GetLikeUid() uint32 {
	if m != nil {
		return m.LikeUid
	}
	return 0
}

type SetUserLikeBeatObjResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserLikeBeatObjResp) Reset()         { *m = SetUserLikeBeatObjResp{} }
func (m *SetUserLikeBeatObjResp) String() string { return proto.CompactTextString(m) }
func (*SetUserLikeBeatObjResp) ProtoMessage()    {}
func (*SetUserLikeBeatObjResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{22}
}
func (m *SetUserLikeBeatObjResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserLikeBeatObjResp.Unmarshal(m, b)
}
func (m *SetUserLikeBeatObjResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserLikeBeatObjResp.Marshal(b, m, deterministic)
}
func (dst *SetUserLikeBeatObjResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserLikeBeatObjResp.Merge(dst, src)
}
func (m *SetUserLikeBeatObjResp) XXX_Size() int {
	return xxx_messageInfo_SetUserLikeBeatObjResp.Size(m)
}
func (m *SetUserLikeBeatObjResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserLikeBeatObjResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserLikeBeatObjResp proto.InternalMessageInfo

// 获取心动对象
type GetUserLikeBeatObjReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OpenUid              uint32   `protobuf:"varint,2,opt,name=open_uid,json=openUid,proto3" json:"open_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserLikeBeatObjReq) Reset()         { *m = GetUserLikeBeatObjReq{} }
func (m *GetUserLikeBeatObjReq) String() string { return proto.CompactTextString(m) }
func (*GetUserLikeBeatObjReq) ProtoMessage()    {}
func (*GetUserLikeBeatObjReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{23}
}
func (m *GetUserLikeBeatObjReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLikeBeatObjReq.Unmarshal(m, b)
}
func (m *GetUserLikeBeatObjReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLikeBeatObjReq.Marshal(b, m, deterministic)
}
func (dst *GetUserLikeBeatObjReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLikeBeatObjReq.Merge(dst, src)
}
func (m *GetUserLikeBeatObjReq) XXX_Size() int {
	return xxx_messageInfo_GetUserLikeBeatObjReq.Size(m)
}
func (m *GetUserLikeBeatObjReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLikeBeatObjReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLikeBeatObjReq proto.InternalMessageInfo

func (m *GetUserLikeBeatObjReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetUserLikeBeatObjReq) GetOpenUid() uint32 {
	if m != nil {
		return m.OpenUid
	}
	return 0
}

type GetUserLikeBeatObjResp struct {
	LikeUid              uint32   `protobuf:"varint,1,opt,name=like_uid,json=likeUid,proto3" json:"like_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserLikeBeatObjResp) Reset()         { *m = GetUserLikeBeatObjResp{} }
func (m *GetUserLikeBeatObjResp) String() string { return proto.CompactTextString(m) }
func (*GetUserLikeBeatObjResp) ProtoMessage()    {}
func (*GetUserLikeBeatObjResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{24}
}
func (m *GetUserLikeBeatObjResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLikeBeatObjResp.Unmarshal(m, b)
}
func (m *GetUserLikeBeatObjResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLikeBeatObjResp.Marshal(b, m, deterministic)
}
func (dst *GetUserLikeBeatObjResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLikeBeatObjResp.Merge(dst, src)
}
func (m *GetUserLikeBeatObjResp) XXX_Size() int {
	return xxx_messageInfo_GetUserLikeBeatObjResp.Size(m)
}
func (m *GetUserLikeBeatObjResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLikeBeatObjResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLikeBeatObjResp proto.InternalMessageInfo

func (m *GetUserLikeBeatObjResp) GetLikeUid() uint32 {
	if m != nil {
		return m.LikeUid
	}
	return 0
}

type GetSelectLikeBeatObjUserReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSelectLikeBeatObjUserReq) Reset()         { *m = GetSelectLikeBeatObjUserReq{} }
func (m *GetSelectLikeBeatObjUserReq) String() string { return proto.CompactTextString(m) }
func (*GetSelectLikeBeatObjUserReq) ProtoMessage()    {}
func (*GetSelectLikeBeatObjUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{25}
}
func (m *GetSelectLikeBeatObjUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSelectLikeBeatObjUserReq.Unmarshal(m, b)
}
func (m *GetSelectLikeBeatObjUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSelectLikeBeatObjUserReq.Marshal(b, m, deterministic)
}
func (dst *GetSelectLikeBeatObjUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSelectLikeBeatObjUserReq.Merge(dst, src)
}
func (m *GetSelectLikeBeatObjUserReq) XXX_Size() int {
	return xxx_messageInfo_GetSelectLikeBeatObjUserReq.Size(m)
}
func (m *GetSelectLikeBeatObjUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSelectLikeBeatObjUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSelectLikeBeatObjUserReq proto.InternalMessageInfo

func (m *GetSelectLikeBeatObjUserReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetSelectLikeBeatObjUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetSelectLikeBeatObjUserResp struct {
	SelectUidList        []uint32 `protobuf:"varint,1,rep,packed,name=select_uid_list,json=selectUidList,proto3" json:"select_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSelectLikeBeatObjUserResp) Reset()         { *m = GetSelectLikeBeatObjUserResp{} }
func (m *GetSelectLikeBeatObjUserResp) String() string { return proto.CompactTextString(m) }
func (*GetSelectLikeBeatObjUserResp) ProtoMessage()    {}
func (*GetSelectLikeBeatObjUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{26}
}
func (m *GetSelectLikeBeatObjUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSelectLikeBeatObjUserResp.Unmarshal(m, b)
}
func (m *GetSelectLikeBeatObjUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSelectLikeBeatObjUserResp.Marshal(b, m, deterministic)
}
func (dst *GetSelectLikeBeatObjUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSelectLikeBeatObjUserResp.Merge(dst, src)
}
func (m *GetSelectLikeBeatObjUserResp) XXX_Size() int {
	return xxx_messageInfo_GetSelectLikeBeatObjUserResp.Size(m)
}
func (m *GetSelectLikeBeatObjUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSelectLikeBeatObjUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSelectLikeBeatObjUserResp proto.InternalMessageInfo

func (m *GetSelectLikeBeatObjUserResp) GetSelectUidList() []uint32 {
	if m != nil {
		return m.SelectUidList
	}
	return nil
}

// 相互心动信息
type MatchLikeBeatInfo struct {
	UidA                 uint32   `protobuf:"varint,1,opt,name=uid_a,json=uidA,proto3" json:"uid_a,omitempty"`
	UidB                 uint32   `protobuf:"varint,2,opt,name=uid_b,json=uidB,proto3" json:"uid_b,omitempty"`
	LikeBeatValA         uint32   `protobuf:"varint,3,opt,name=like_beat_val_a,json=likeBeatValA,proto3" json:"like_beat_val_a,omitempty"`
	LikeBeatValB         uint32   `protobuf:"varint,4,opt,name=like_beat_val_b,json=likeBeatValB,proto3" json:"like_beat_val_b,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MatchLikeBeatInfo) Reset()         { *m = MatchLikeBeatInfo{} }
func (m *MatchLikeBeatInfo) String() string { return proto.CompactTextString(m) }
func (*MatchLikeBeatInfo) ProtoMessage()    {}
func (*MatchLikeBeatInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{27}
}
func (m *MatchLikeBeatInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MatchLikeBeatInfo.Unmarshal(m, b)
}
func (m *MatchLikeBeatInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MatchLikeBeatInfo.Marshal(b, m, deterministic)
}
func (dst *MatchLikeBeatInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MatchLikeBeatInfo.Merge(dst, src)
}
func (m *MatchLikeBeatInfo) XXX_Size() int {
	return xxx_messageInfo_MatchLikeBeatInfo.Size(m)
}
func (m *MatchLikeBeatInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MatchLikeBeatInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MatchLikeBeatInfo proto.InternalMessageInfo

func (m *MatchLikeBeatInfo) GetUidA() uint32 {
	if m != nil {
		return m.UidA
	}
	return 0
}

func (m *MatchLikeBeatInfo) GetUidB() uint32 {
	if m != nil {
		return m.UidB
	}
	return 0
}

func (m *MatchLikeBeatInfo) GetLikeBeatValA() uint32 {
	if m != nil {
		return m.LikeBeatValA
	}
	return 0
}

func (m *MatchLikeBeatInfo) GetLikeBeatValB() uint32 {
	if m != nil {
		return m.LikeBeatValB
	}
	return 0
}

// 获取已公布的用户
type GetOpenLikeUserListReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOpenLikeUserListReq) Reset()         { *m = GetOpenLikeUserListReq{} }
func (m *GetOpenLikeUserListReq) String() string { return proto.CompactTextString(m) }
func (*GetOpenLikeUserListReq) ProtoMessage()    {}
func (*GetOpenLikeUserListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{28}
}
func (m *GetOpenLikeUserListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOpenLikeUserListReq.Unmarshal(m, b)
}
func (m *GetOpenLikeUserListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOpenLikeUserListReq.Marshal(b, m, deterministic)
}
func (dst *GetOpenLikeUserListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOpenLikeUserListReq.Merge(dst, src)
}
func (m *GetOpenLikeUserListReq) XXX_Size() int {
	return xxx_messageInfo_GetOpenLikeUserListReq.Size(m)
}
func (m *GetOpenLikeUserListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOpenLikeUserListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOpenLikeUserListReq proto.InternalMessageInfo

func (m *GetOpenLikeUserListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetOpenLikeUserListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetOpenLikeUserListResp struct {
	OpenInfoList         []*OpenLikeUserInfo `protobuf:"bytes,1,rep,name=open_info_list,json=openInfoList,proto3" json:"open_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetOpenLikeUserListResp) Reset()         { *m = GetOpenLikeUserListResp{} }
func (m *GetOpenLikeUserListResp) String() string { return proto.CompactTextString(m) }
func (*GetOpenLikeUserListResp) ProtoMessage()    {}
func (*GetOpenLikeUserListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{29}
}
func (m *GetOpenLikeUserListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOpenLikeUserListResp.Unmarshal(m, b)
}
func (m *GetOpenLikeUserListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOpenLikeUserListResp.Marshal(b, m, deterministic)
}
func (dst *GetOpenLikeUserListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOpenLikeUserListResp.Merge(dst, src)
}
func (m *GetOpenLikeUserListResp) XXX_Size() int {
	return xxx_messageInfo_GetOpenLikeUserListResp.Size(m)
}
func (m *GetOpenLikeUserListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOpenLikeUserListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOpenLikeUserListResp proto.InternalMessageInfo

func (m *GetOpenLikeUserListResp) GetOpenInfoList() []*OpenLikeUserInfo {
	if m != nil {
		return m.OpenInfoList
	}
	return nil
}

type OpenLikeUserInfo struct {
	OpenUid              uint32   `protobuf:"varint,1,opt,name=open_uid,json=openUid,proto3" json:"open_uid,omitempty"`
	LikeUid              uint32   `protobuf:"varint,2,opt,name=like_uid,json=likeUid,proto3" json:"like_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OpenLikeUserInfo) Reset()         { *m = OpenLikeUserInfo{} }
func (m *OpenLikeUserInfo) String() string { return proto.CompactTextString(m) }
func (*OpenLikeUserInfo) ProtoMessage()    {}
func (*OpenLikeUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{30}
}
func (m *OpenLikeUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenLikeUserInfo.Unmarshal(m, b)
}
func (m *OpenLikeUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenLikeUserInfo.Marshal(b, m, deterministic)
}
func (dst *OpenLikeUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenLikeUserInfo.Merge(dst, src)
}
func (m *OpenLikeUserInfo) XXX_Size() int {
	return xxx_messageInfo_OpenLikeUserInfo.Size(m)
}
func (m *OpenLikeUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenLikeUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OpenLikeUserInfo proto.InternalMessageInfo

func (m *OpenLikeUserInfo) GetOpenUid() uint32 {
	if m != nil {
		return m.OpenUid
	}
	return 0
}

func (m *OpenLikeUserInfo) GetLikeUid() uint32 {
	if m != nil {
		return m.LikeUid
	}
	return 0
}

// 排麦
type UserApplyMicReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	IsCancel             bool     `protobuf:"varint,3,opt,name=is_cancel,json=isCancel,proto3" json:"is_cancel,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserApplyMicReq) Reset()         { *m = UserApplyMicReq{} }
func (m *UserApplyMicReq) String() string { return proto.CompactTextString(m) }
func (*UserApplyMicReq) ProtoMessage()    {}
func (*UserApplyMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{31}
}
func (m *UserApplyMicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserApplyMicReq.Unmarshal(m, b)
}
func (m *UserApplyMicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserApplyMicReq.Marshal(b, m, deterministic)
}
func (dst *UserApplyMicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserApplyMicReq.Merge(dst, src)
}
func (m *UserApplyMicReq) XXX_Size() int {
	return xxx_messageInfo_UserApplyMicReq.Size(m)
}
func (m *UserApplyMicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserApplyMicReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserApplyMicReq proto.InternalMessageInfo

func (m *UserApplyMicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserApplyMicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserApplyMicReq) GetIsCancel() bool {
	if m != nil {
		return m.IsCancel
	}
	return false
}

type UserApplyMicResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserApplyMicResp) Reset()         { *m = UserApplyMicResp{} }
func (m *UserApplyMicResp) String() string { return proto.CompactTextString(m) }
func (*UserApplyMicResp) ProtoMessage()    {}
func (*UserApplyMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{32}
}
func (m *UserApplyMicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserApplyMicResp.Unmarshal(m, b)
}
func (m *UserApplyMicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserApplyMicResp.Marshal(b, m, deterministic)
}
func (dst *UserApplyMicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserApplyMicResp.Merge(dst, src)
}
func (m *UserApplyMicResp) XXX_Size() int {
	return xxx_messageInfo_UserApplyMicResp.Size(m)
}
func (m *UserApplyMicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserApplyMicResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserApplyMicResp proto.InternalMessageInfo

// 获取排麦用户列表
type GetApplyMicUserListReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetApplyMicUserListReq) Reset()         { *m = GetApplyMicUserListReq{} }
func (m *GetApplyMicUserListReq) String() string { return proto.CompactTextString(m) }
func (*GetApplyMicUserListReq) ProtoMessage()    {}
func (*GetApplyMicUserListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{33}
}
func (m *GetApplyMicUserListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplyMicUserListReq.Unmarshal(m, b)
}
func (m *GetApplyMicUserListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplyMicUserListReq.Marshal(b, m, deterministic)
}
func (dst *GetApplyMicUserListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplyMicUserListReq.Merge(dst, src)
}
func (m *GetApplyMicUserListReq) XXX_Size() int {
	return xxx_messageInfo_GetApplyMicUserListReq.Size(m)
}
func (m *GetApplyMicUserListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplyMicUserListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplyMicUserListReq proto.InternalMessageInfo

func (m *GetApplyMicUserListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetApplyMicUserListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetApplyMicUserListResp struct {
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetApplyMicUserListResp) Reset()         { *m = GetApplyMicUserListResp{} }
func (m *GetApplyMicUserListResp) String() string { return proto.CompactTextString(m) }
func (*GetApplyMicUserListResp) ProtoMessage()    {}
func (*GetApplyMicUserListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{34}
}
func (m *GetApplyMicUserListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplyMicUserListResp.Unmarshal(m, b)
}
func (m *GetApplyMicUserListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplyMicUserListResp.Marshal(b, m, deterministic)
}
func (dst *GetApplyMicUserListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplyMicUserListResp.Merge(dst, src)
}
func (m *GetApplyMicUserListResp) XXX_Size() int {
	return xxx_messageInfo_GetApplyMicUserListResp.Size(m)
}
func (m *GetApplyMicUserListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplyMicUserListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplyMicUserListResp proto.InternalMessageInfo

func (m *GetApplyMicUserListResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

// 帽子信息
type DatingGameHatCfg struct {
	HatId                uint32   `protobuf:"varint,1,opt,name=hat_id,json=hatId,proto3" json:"hat_id,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Md5                  string   `protobuf:"bytes,3,opt,name=md5,proto3" json:"md5,omitempty"`
	TbeanLimit           uint32   `protobuf:"varint,4,opt,name=tbean_limit,json=tbeanLimit,proto3" json:"tbean_limit,omitempty"`
	IsMale               bool     `protobuf:"varint,5,opt,name=is_male,json=isMale,proto3" json:"is_male,omitempty"`
	Level                uint32   `protobuf:"varint,6,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DatingGameHatCfg) Reset()         { *m = DatingGameHatCfg{} }
func (m *DatingGameHatCfg) String() string { return proto.CompactTextString(m) }
func (*DatingGameHatCfg) ProtoMessage()    {}
func (*DatingGameHatCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{35}
}
func (m *DatingGameHatCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DatingGameHatCfg.Unmarshal(m, b)
}
func (m *DatingGameHatCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DatingGameHatCfg.Marshal(b, m, deterministic)
}
func (dst *DatingGameHatCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DatingGameHatCfg.Merge(dst, src)
}
func (m *DatingGameHatCfg) XXX_Size() int {
	return xxx_messageInfo_DatingGameHatCfg.Size(m)
}
func (m *DatingGameHatCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_DatingGameHatCfg.DiscardUnknown(m)
}

var xxx_messageInfo_DatingGameHatCfg proto.InternalMessageInfo

func (m *DatingGameHatCfg) GetHatId() uint32 {
	if m != nil {
		return m.HatId
	}
	return 0
}

func (m *DatingGameHatCfg) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *DatingGameHatCfg) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *DatingGameHatCfg) GetTbeanLimit() uint32 {
	if m != nil {
		return m.TbeanLimit
	}
	return 0
}

func (m *DatingGameHatCfg) GetIsMale() bool {
	if m != nil {
		return m.IsMale
	}
	return false
}

func (m *DatingGameHatCfg) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

// 获取vip用户
type GetVipMicUserReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVipMicUserReq) Reset()         { *m = GetVipMicUserReq{} }
func (m *GetVipMicUserReq) String() string { return proto.CompactTextString(m) }
func (*GetVipMicUserReq) ProtoMessage()    {}
func (*GetVipMicUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{36}
}
func (m *GetVipMicUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVipMicUserReq.Unmarshal(m, b)
}
func (m *GetVipMicUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVipMicUserReq.Marshal(b, m, deterministic)
}
func (dst *GetVipMicUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVipMicUserReq.Merge(dst, src)
}
func (m *GetVipMicUserReq) XXX_Size() int {
	return xxx_messageInfo_GetVipMicUserReq.Size(m)
}
func (m *GetVipMicUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVipMicUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetVipMicUserReq proto.InternalMessageInfo

func (m *GetVipMicUserReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetVipMicUserResp struct {
	VipUid               uint32   `protobuf:"varint,1,opt,name=vip_uid,json=vipUid,proto3" json:"vip_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVipMicUserResp) Reset()         { *m = GetVipMicUserResp{} }
func (m *GetVipMicUserResp) String() string { return proto.CompactTextString(m) }
func (*GetVipMicUserResp) ProtoMessage()    {}
func (*GetVipMicUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{37}
}
func (m *GetVipMicUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVipMicUserResp.Unmarshal(m, b)
}
func (m *GetVipMicUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVipMicUserResp.Marshal(b, m, deterministic)
}
func (dst *GetVipMicUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVipMicUserResp.Merge(dst, src)
}
func (m *GetVipMicUserResp) XXX_Size() int {
	return xxx_messageInfo_GetVipMicUserResp.Size(m)
}
func (m *GetVipMicUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVipMicUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetVipMicUserResp proto.InternalMessageInfo

func (m *GetVipMicUserResp) GetVipUid() uint32 {
	if m != nil {
		return m.VipUid
	}
	return 0
}

// vip用户上麦确认
type ConfirmVipHoldMicReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmVipHoldMicReq) Reset()         { *m = ConfirmVipHoldMicReq{} }
func (m *ConfirmVipHoldMicReq) String() string { return proto.CompactTextString(m) }
func (*ConfirmVipHoldMicReq) ProtoMessage()    {}
func (*ConfirmVipHoldMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{38}
}
func (m *ConfirmVipHoldMicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmVipHoldMicReq.Unmarshal(m, b)
}
func (m *ConfirmVipHoldMicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmVipHoldMicReq.Marshal(b, m, deterministic)
}
func (dst *ConfirmVipHoldMicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmVipHoldMicReq.Merge(dst, src)
}
func (m *ConfirmVipHoldMicReq) XXX_Size() int {
	return xxx_messageInfo_ConfirmVipHoldMicReq.Size(m)
}
func (m *ConfirmVipHoldMicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmVipHoldMicReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmVipHoldMicReq proto.InternalMessageInfo

func (m *ConfirmVipHoldMicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConfirmVipHoldMicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ConfirmVipHoldMicResp struct {
	PreVipUid            uint32   `protobuf:"varint,1,opt,name=pre_vip_uid,json=preVipUid,proto3" json:"pre_vip_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmVipHoldMicResp) Reset()         { *m = ConfirmVipHoldMicResp{} }
func (m *ConfirmVipHoldMicResp) String() string { return proto.CompactTextString(m) }
func (*ConfirmVipHoldMicResp) ProtoMessage()    {}
func (*ConfirmVipHoldMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{39}
}
func (m *ConfirmVipHoldMicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmVipHoldMicResp.Unmarshal(m, b)
}
func (m *ConfirmVipHoldMicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmVipHoldMicResp.Marshal(b, m, deterministic)
}
func (dst *ConfirmVipHoldMicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmVipHoldMicResp.Merge(dst, src)
}
func (m *ConfirmVipHoldMicResp) XXX_Size() int {
	return xxx_messageInfo_ConfirmVipHoldMicResp.Size(m)
}
func (m *ConfirmVipHoldMicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmVipHoldMicResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmVipHoldMicResp proto.InternalMessageInfo

func (m *ConfirmVipHoldMicResp) GetPreVipUid() uint32 {
	if m != nil {
		return m.PreVipUid
	}
	return 0
}

type TestDrawImageReq struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	UidA                 uint32   `protobuf:"varint,2,opt,name=uid_a,json=uidA,proto3" json:"uid_a,omitempty"`
	UidB                 uint32   `protobuf:"varint,3,opt,name=uid_b,json=uidB,proto3" json:"uid_b,omitempty"`
	VA                   uint32   `protobuf:"varint,4,opt,name=v_a,json=vA,proto3" json:"v_a,omitempty"`
	VB                   uint32   `protobuf:"varint,5,opt,name=v_b,json=vB,proto3" json:"v_b,omitempty"`
	PushChannel          bool     `protobuf:"varint,6,opt,name=push_channel,json=pushChannel,proto3" json:"push_channel,omitempty"`
	ScenceId             uint32   `protobuf:"varint,7,opt,name=scence_id,json=scenceId,proto3" json:"scence_id,omitempty"`
	UseStoreConf         bool     `protobuf:"varint,8,opt,name=use_store_conf,json=useStoreConf,proto3" json:"use_store_conf,omitempty"`
	TriggerTs            int64    `protobuf:"varint,9,opt,name=trigger_ts,json=triggerTs,proto3" json:"trigger_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestDrawImageReq) Reset()         { *m = TestDrawImageReq{} }
func (m *TestDrawImageReq) String() string { return proto.CompactTextString(m) }
func (*TestDrawImageReq) ProtoMessage()    {}
func (*TestDrawImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{40}
}
func (m *TestDrawImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestDrawImageReq.Unmarshal(m, b)
}
func (m *TestDrawImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestDrawImageReq.Marshal(b, m, deterministic)
}
func (dst *TestDrawImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestDrawImageReq.Merge(dst, src)
}
func (m *TestDrawImageReq) XXX_Size() int {
	return xxx_messageInfo_TestDrawImageReq.Size(m)
}
func (m *TestDrawImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestDrawImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestDrawImageReq proto.InternalMessageInfo

func (m *TestDrawImageReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *TestDrawImageReq) GetUidA() uint32 {
	if m != nil {
		return m.UidA
	}
	return 0
}

func (m *TestDrawImageReq) GetUidB() uint32 {
	if m != nil {
		return m.UidB
	}
	return 0
}

func (m *TestDrawImageReq) GetVA() uint32 {
	if m != nil {
		return m.VA
	}
	return 0
}

func (m *TestDrawImageReq) GetVB() uint32 {
	if m != nil {
		return m.VB
	}
	return 0
}

func (m *TestDrawImageReq) GetPushChannel() bool {
	if m != nil {
		return m.PushChannel
	}
	return false
}

func (m *TestDrawImageReq) GetScenceId() uint32 {
	if m != nil {
		return m.ScenceId
	}
	return 0
}

func (m *TestDrawImageReq) GetUseStoreConf() bool {
	if m != nil {
		return m.UseStoreConf
	}
	return false
}

func (m *TestDrawImageReq) GetTriggerTs() int64 {
	if m != nil {
		return m.TriggerTs
	}
	return 0
}

type TestDrawImageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestDrawImageResp) Reset()         { *m = TestDrawImageResp{} }
func (m *TestDrawImageResp) String() string { return proto.CompactTextString(m) }
func (*TestDrawImageResp) ProtoMessage()    {}
func (*TestDrawImageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{41}
}
func (m *TestDrawImageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestDrawImageResp.Unmarshal(m, b)
}
func (m *TestDrawImageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestDrawImageResp.Marshal(b, m, deterministic)
}
func (dst *TestDrawImageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestDrawImageResp.Merge(dst, src)
}
func (m *TestDrawImageResp) XXX_Size() int {
	return xxx_messageInfo_TestDrawImageResp.Size(m)
}
func (m *TestDrawImageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestDrawImageResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestDrawImageResp proto.InternalMessageInfo

// =================== 相亲资源配置后台接口 begin =======================
type DatingGameDrawCfg struct {
	SceneId             uint32 `protobuf:"varint,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	Name                string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	TbeanLimit          uint32 `protobuf:"varint,3,opt,name=tbean_limit,json=tbeanLimit,proto3" json:"tbean_limit,omitempty"`
	BackgroundPic       string `protobuf:"bytes,4,opt,name=background_pic,json=backgroundPic,proto3" json:"background_pic,omitempty"`
	FellowNameplate     string `protobuf:"bytes,5,opt,name=fellow_nameplate,json=fellowNameplate,proto3" json:"fellow_nameplate,omitempty"`
	FellowNameplateUniq string `protobuf:"bytes,6,opt,name=fellow_nameplate_uniq,json=fellowNameplateUniq,proto3" json:"fellow_nameplate_uniq,omitempty"`
	ChBackgroundPic     string `protobuf:"bytes,7,opt,name=ch_background_pic,json=chBackgroundPic,proto3" json:"ch_background_pic,omitempty"`
	ImBackgroundPic     string `protobuf:"bytes,8,opt,name=im_background_pic,json=imBackgroundPic,proto3" json:"im_background_pic,omitempty"`
	EffectUrl           string `protobuf:"bytes,9,opt,name=effect_url,json=effectUrl,proto3" json:"effect_url,omitempty"`
	EffectUrlMd5        string `protobuf:"bytes,10,opt,name=effect_url_md5,json=effectUrlMd5,proto3" json:"effect_url_md5,omitempty"`
	BreaknewsId         uint32 `protobuf:"varint,11,opt,name=breaknews_id,json=breaknewsId,proto3" json:"breaknews_id,omitempty"`
	HwIdFemale          uint32 `protobuf:"varint,12,opt,name=hw_id_female,json=hwIdFemale,proto3" json:"hw_id_female,omitempty"`
	HwIdMale            uint32 `protobuf:"varint,13,opt,name=hw_id_male,json=hwIdMale,proto3" json:"hw_id_male,omitempty"`
	BackgroudParamJson  string `protobuf:"bytes,14,opt,name=backgroud_param_json,json=backgroudParamJson,proto3" json:"backgroud_param_json,omitempty"`
	Remark              string `protobuf:"bytes,15,opt,name=remark,proto3" json:"remark,omitempty"`
	// 额外字段
	BeginTs              int64    `protobuf:"varint,16,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                int64    `protobuf:"varint,17,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DatingGameDrawCfg) Reset()         { *m = DatingGameDrawCfg{} }
func (m *DatingGameDrawCfg) String() string { return proto.CompactTextString(m) }
func (*DatingGameDrawCfg) ProtoMessage()    {}
func (*DatingGameDrawCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{42}
}
func (m *DatingGameDrawCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DatingGameDrawCfg.Unmarshal(m, b)
}
func (m *DatingGameDrawCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DatingGameDrawCfg.Marshal(b, m, deterministic)
}
func (dst *DatingGameDrawCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DatingGameDrawCfg.Merge(dst, src)
}
func (m *DatingGameDrawCfg) XXX_Size() int {
	return xxx_messageInfo_DatingGameDrawCfg.Size(m)
}
func (m *DatingGameDrawCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_DatingGameDrawCfg.DiscardUnknown(m)
}

var xxx_messageInfo_DatingGameDrawCfg proto.InternalMessageInfo

func (m *DatingGameDrawCfg) GetSceneId() uint32 {
	if m != nil {
		return m.SceneId
	}
	return 0
}

func (m *DatingGameDrawCfg) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DatingGameDrawCfg) GetTbeanLimit() uint32 {
	if m != nil {
		return m.TbeanLimit
	}
	return 0
}

func (m *DatingGameDrawCfg) GetBackgroundPic() string {
	if m != nil {
		return m.BackgroundPic
	}
	return ""
}

func (m *DatingGameDrawCfg) GetFellowNameplate() string {
	if m != nil {
		return m.FellowNameplate
	}
	return ""
}

func (m *DatingGameDrawCfg) GetFellowNameplateUniq() string {
	if m != nil {
		return m.FellowNameplateUniq
	}
	return ""
}

func (m *DatingGameDrawCfg) GetChBackgroundPic() string {
	if m != nil {
		return m.ChBackgroundPic
	}
	return ""
}

func (m *DatingGameDrawCfg) GetImBackgroundPic() string {
	if m != nil {
		return m.ImBackgroundPic
	}
	return ""
}

func (m *DatingGameDrawCfg) GetEffectUrl() string {
	if m != nil {
		return m.EffectUrl
	}
	return ""
}

func (m *DatingGameDrawCfg) GetEffectUrlMd5() string {
	if m != nil {
		return m.EffectUrlMd5
	}
	return ""
}

func (m *DatingGameDrawCfg) GetBreaknewsId() uint32 {
	if m != nil {
		return m.BreaknewsId
	}
	return 0
}

func (m *DatingGameDrawCfg) GetHwIdFemale() uint32 {
	if m != nil {
		return m.HwIdFemale
	}
	return 0
}

func (m *DatingGameDrawCfg) GetHwIdMale() uint32 {
	if m != nil {
		return m.HwIdMale
	}
	return 0
}

func (m *DatingGameDrawCfg) GetBackgroudParamJson() string {
	if m != nil {
		return m.BackgroudParamJson
	}
	return ""
}

func (m *DatingGameDrawCfg) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *DatingGameDrawCfg) GetBeginTs() int64 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *DatingGameDrawCfg) GetEndTs() int64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

// 新增相亲厅牵手资源配置
type AddDatingDrawCfgReq struct {
	Cfg                  *DatingGameDrawCfg `protobuf:"bytes,1,opt,name=cfg,proto3" json:"cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AddDatingDrawCfgReq) Reset()         { *m = AddDatingDrawCfgReq{} }
func (m *AddDatingDrawCfgReq) String() string { return proto.CompactTextString(m) }
func (*AddDatingDrawCfgReq) ProtoMessage()    {}
func (*AddDatingDrawCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{43}
}
func (m *AddDatingDrawCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddDatingDrawCfgReq.Unmarshal(m, b)
}
func (m *AddDatingDrawCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddDatingDrawCfgReq.Marshal(b, m, deterministic)
}
func (dst *AddDatingDrawCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddDatingDrawCfgReq.Merge(dst, src)
}
func (m *AddDatingDrawCfgReq) XXX_Size() int {
	return xxx_messageInfo_AddDatingDrawCfgReq.Size(m)
}
func (m *AddDatingDrawCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddDatingDrawCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddDatingDrawCfgReq proto.InternalMessageInfo

func (m *AddDatingDrawCfgReq) GetCfg() *DatingGameDrawCfg {
	if m != nil {
		return m.Cfg
	}
	return nil
}

type AddDatingDrawCfgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddDatingDrawCfgResp) Reset()         { *m = AddDatingDrawCfgResp{} }
func (m *AddDatingDrawCfgResp) String() string { return proto.CompactTextString(m) }
func (*AddDatingDrawCfgResp) ProtoMessage()    {}
func (*AddDatingDrawCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{44}
}
func (m *AddDatingDrawCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddDatingDrawCfgResp.Unmarshal(m, b)
}
func (m *AddDatingDrawCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddDatingDrawCfgResp.Marshal(b, m, deterministic)
}
func (dst *AddDatingDrawCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddDatingDrawCfgResp.Merge(dst, src)
}
func (m *AddDatingDrawCfgResp) XXX_Size() int {
	return xxx_messageInfo_AddDatingDrawCfgResp.Size(m)
}
func (m *AddDatingDrawCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddDatingDrawCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddDatingDrawCfgResp proto.InternalMessageInfo

// 更新相亲厅牵手资源配置
type UpdateDatingDrawCfgReq struct {
	Cfg                  *DatingGameDrawCfg `protobuf:"bytes,1,opt,name=cfg,proto3" json:"cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UpdateDatingDrawCfgReq) Reset()         { *m = UpdateDatingDrawCfgReq{} }
func (m *UpdateDatingDrawCfgReq) String() string { return proto.CompactTextString(m) }
func (*UpdateDatingDrawCfgReq) ProtoMessage()    {}
func (*UpdateDatingDrawCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{45}
}
func (m *UpdateDatingDrawCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateDatingDrawCfgReq.Unmarshal(m, b)
}
func (m *UpdateDatingDrawCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateDatingDrawCfgReq.Marshal(b, m, deterministic)
}
func (dst *UpdateDatingDrawCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateDatingDrawCfgReq.Merge(dst, src)
}
func (m *UpdateDatingDrawCfgReq) XXX_Size() int {
	return xxx_messageInfo_UpdateDatingDrawCfgReq.Size(m)
}
func (m *UpdateDatingDrawCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateDatingDrawCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateDatingDrawCfgReq proto.InternalMessageInfo

func (m *UpdateDatingDrawCfgReq) GetCfg() *DatingGameDrawCfg {
	if m != nil {
		return m.Cfg
	}
	return nil
}

type UpdateDatingDrawCfgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateDatingDrawCfgResp) Reset()         { *m = UpdateDatingDrawCfgResp{} }
func (m *UpdateDatingDrawCfgResp) String() string { return proto.CompactTextString(m) }
func (*UpdateDatingDrawCfgResp) ProtoMessage()    {}
func (*UpdateDatingDrawCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{46}
}
func (m *UpdateDatingDrawCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateDatingDrawCfgResp.Unmarshal(m, b)
}
func (m *UpdateDatingDrawCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateDatingDrawCfgResp.Marshal(b, m, deterministic)
}
func (dst *UpdateDatingDrawCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateDatingDrawCfgResp.Merge(dst, src)
}
func (m *UpdateDatingDrawCfgResp) XXX_Size() int {
	return xxx_messageInfo_UpdateDatingDrawCfgResp.Size(m)
}
func (m *UpdateDatingDrawCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateDatingDrawCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateDatingDrawCfgResp proto.InternalMessageInfo

// 分页获取相亲厅牵手资源配置
type BatGetDatingDrawCfgReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	SceneId              uint32   `protobuf:"varint,3,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	Name                 string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	SortBy               uint32   `protobuf:"varint,5,opt,name=sort_by,json=sortBy,proto3" json:"sort_by,omitempty"`
	IsAsc                bool     `protobuf:"varint,6,opt,name=is_asc,json=isAsc,proto3" json:"is_asc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetDatingDrawCfgReq) Reset()         { *m = BatGetDatingDrawCfgReq{} }
func (m *BatGetDatingDrawCfgReq) String() string { return proto.CompactTextString(m) }
func (*BatGetDatingDrawCfgReq) ProtoMessage()    {}
func (*BatGetDatingDrawCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{47}
}
func (m *BatGetDatingDrawCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetDatingDrawCfgReq.Unmarshal(m, b)
}
func (m *BatGetDatingDrawCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetDatingDrawCfgReq.Marshal(b, m, deterministic)
}
func (dst *BatGetDatingDrawCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetDatingDrawCfgReq.Merge(dst, src)
}
func (m *BatGetDatingDrawCfgReq) XXX_Size() int {
	return xxx_messageInfo_BatGetDatingDrawCfgReq.Size(m)
}
func (m *BatGetDatingDrawCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetDatingDrawCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetDatingDrawCfgReq proto.InternalMessageInfo

func (m *BatGetDatingDrawCfgReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *BatGetDatingDrawCfgReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *BatGetDatingDrawCfgReq) GetSceneId() uint32 {
	if m != nil {
		return m.SceneId
	}
	return 0
}

func (m *BatGetDatingDrawCfgReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *BatGetDatingDrawCfgReq) GetSortBy() uint32 {
	if m != nil {
		return m.SortBy
	}
	return 0
}

func (m *BatGetDatingDrawCfgReq) GetIsAsc() bool {
	if m != nil {
		return m.IsAsc
	}
	return false
}

type BatGetDatingDrawCfgResp struct {
	DrawList             []*DatingGameDrawCfg `protobuf:"bytes,1,rep,name=draw_list,json=drawList,proto3" json:"draw_list,omitempty"`
	Total                uint32               `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatGetDatingDrawCfgResp) Reset()         { *m = BatGetDatingDrawCfgResp{} }
func (m *BatGetDatingDrawCfgResp) String() string { return proto.CompactTextString(m) }
func (*BatGetDatingDrawCfgResp) ProtoMessage()    {}
func (*BatGetDatingDrawCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{48}
}
func (m *BatGetDatingDrawCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetDatingDrawCfgResp.Unmarshal(m, b)
}
func (m *BatGetDatingDrawCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetDatingDrawCfgResp.Marshal(b, m, deterministic)
}
func (dst *BatGetDatingDrawCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetDatingDrawCfgResp.Merge(dst, src)
}
func (m *BatGetDatingDrawCfgResp) XXX_Size() int {
	return xxx_messageInfo_BatGetDatingDrawCfgResp.Size(m)
}
func (m *BatGetDatingDrawCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetDatingDrawCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetDatingDrawCfgResp proto.InternalMessageInfo

func (m *BatGetDatingDrawCfgResp) GetDrawList() []*DatingGameDrawCfg {
	if m != nil {
		return m.DrawList
	}
	return nil
}

func (m *BatGetDatingDrawCfgResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 通过场景id获取相亲厅牵手资源配置
type BatGetDrawCfgBySceneIdReq struct {
	SceneIdList          []uint32 `protobuf:"varint,1,rep,packed,name=scene_id_list,json=sceneIdList,proto3" json:"scene_id_list,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetDrawCfgBySceneIdReq) Reset()         { *m = BatGetDrawCfgBySceneIdReq{} }
func (m *BatGetDrawCfgBySceneIdReq) String() string { return proto.CompactTextString(m) }
func (*BatGetDrawCfgBySceneIdReq) ProtoMessage()    {}
func (*BatGetDrawCfgBySceneIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{49}
}
func (m *BatGetDrawCfgBySceneIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetDrawCfgBySceneIdReq.Unmarshal(m, b)
}
func (m *BatGetDrawCfgBySceneIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetDrawCfgBySceneIdReq.Marshal(b, m, deterministic)
}
func (dst *BatGetDrawCfgBySceneIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetDrawCfgBySceneIdReq.Merge(dst, src)
}
func (m *BatGetDrawCfgBySceneIdReq) XXX_Size() int {
	return xxx_messageInfo_BatGetDrawCfgBySceneIdReq.Size(m)
}
func (m *BatGetDrawCfgBySceneIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetDrawCfgBySceneIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetDrawCfgBySceneIdReq proto.InternalMessageInfo

func (m *BatGetDrawCfgBySceneIdReq) GetSceneIdList() []uint32 {
	if m != nil {
		return m.SceneIdList
	}
	return nil
}

func (m *BatGetDrawCfgBySceneIdReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type BatGetDrawCfgBySceneIdResp struct {
	Cfg                  []*DatingGameDrawCfg `protobuf:"bytes,1,rep,name=cfg,proto3" json:"cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatGetDrawCfgBySceneIdResp) Reset()         { *m = BatGetDrawCfgBySceneIdResp{} }
func (m *BatGetDrawCfgBySceneIdResp) String() string { return proto.CompactTextString(m) }
func (*BatGetDrawCfgBySceneIdResp) ProtoMessage()    {}
func (*BatGetDrawCfgBySceneIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{50}
}
func (m *BatGetDrawCfgBySceneIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetDrawCfgBySceneIdResp.Unmarshal(m, b)
}
func (m *BatGetDrawCfgBySceneIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetDrawCfgBySceneIdResp.Marshal(b, m, deterministic)
}
func (dst *BatGetDrawCfgBySceneIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetDrawCfgBySceneIdResp.Merge(dst, src)
}
func (m *BatGetDrawCfgBySceneIdResp) XXX_Size() int {
	return xxx_messageInfo_BatGetDrawCfgBySceneIdResp.Size(m)
}
func (m *BatGetDrawCfgBySceneIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetDrawCfgBySceneIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetDrawCfgBySceneIdResp proto.InternalMessageInfo

func (m *BatGetDrawCfgBySceneIdResp) GetCfg() []*DatingGameDrawCfg {
	if m != nil {
		return m.Cfg
	}
	return nil
}

// 上架相亲厅牵手资源配置
type ApplyDatingDrawCfgReq struct {
	SceneId              []uint32 `protobuf:"varint,1,rep,packed,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	TimeLimitType        TimeType `protobuf:"varint,2,opt,name=time_limit_type,json=timeLimitType,proto3,enum=channel_dating_game.TimeType" json:"time_limit_type,omitempty"`
	BeginTs              int64    `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                int64    `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	UseInAllLv           bool     `protobuf:"varint,5,opt,name=use_in_all_lv,json=useInAllLv,proto3" json:"use_in_all_lv,omitempty"`
	Remark               string   `protobuf:"bytes,6,opt,name=remark,proto3" json:"remark,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyDatingDrawCfgReq) Reset()         { *m = ApplyDatingDrawCfgReq{} }
func (m *ApplyDatingDrawCfgReq) String() string { return proto.CompactTextString(m) }
func (*ApplyDatingDrawCfgReq) ProtoMessage()    {}
func (*ApplyDatingDrawCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{51}
}
func (m *ApplyDatingDrawCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyDatingDrawCfgReq.Unmarshal(m, b)
}
func (m *ApplyDatingDrawCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyDatingDrawCfgReq.Marshal(b, m, deterministic)
}
func (dst *ApplyDatingDrawCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyDatingDrawCfgReq.Merge(dst, src)
}
func (m *ApplyDatingDrawCfgReq) XXX_Size() int {
	return xxx_messageInfo_ApplyDatingDrawCfgReq.Size(m)
}
func (m *ApplyDatingDrawCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyDatingDrawCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyDatingDrawCfgReq proto.InternalMessageInfo

func (m *ApplyDatingDrawCfgReq) GetSceneId() []uint32 {
	if m != nil {
		return m.SceneId
	}
	return nil
}

func (m *ApplyDatingDrawCfgReq) GetTimeLimitType() TimeType {
	if m != nil {
		return m.TimeLimitType
	}
	return TimeType_TIME_LIMIT_TYPE_UNSPECIFIED
}

func (m *ApplyDatingDrawCfgReq) GetBeginTs() int64 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *ApplyDatingDrawCfgReq) GetEndTs() int64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *ApplyDatingDrawCfgReq) GetUseInAllLv() bool {
	if m != nil {
		return m.UseInAllLv
	}
	return false
}

func (m *ApplyDatingDrawCfgReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

type ApplyDatingDrawCfgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyDatingDrawCfgResp) Reset()         { *m = ApplyDatingDrawCfgResp{} }
func (m *ApplyDatingDrawCfgResp) String() string { return proto.CompactTextString(m) }
func (*ApplyDatingDrawCfgResp) ProtoMessage()    {}
func (*ApplyDatingDrawCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{52}
}
func (m *ApplyDatingDrawCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyDatingDrawCfgResp.Unmarshal(m, b)
}
func (m *ApplyDatingDrawCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyDatingDrawCfgResp.Marshal(b, m, deterministic)
}
func (dst *ApplyDatingDrawCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyDatingDrawCfgResp.Merge(dst, src)
}
func (m *ApplyDatingDrawCfgResp) XXX_Size() int {
	return xxx_messageInfo_ApplyDatingDrawCfgResp.Size(m)
}
func (m *ApplyDatingDrawCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyDatingDrawCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyDatingDrawCfgResp proto.InternalMessageInfo

type PreCheckApplyDrawCfgReq struct {
	SceneId              []uint32 `protobuf:"varint,1,rep,packed,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	TimeLimitType        TimeType `protobuf:"varint,2,opt,name=time_limit_type,json=timeLimitType,proto3,enum=channel_dating_game.TimeType" json:"time_limit_type,omitempty"`
	BeginTs              int64    `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                int64    `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	UseInAllLv           bool     `protobuf:"varint,5,opt,name=use_in_all_lv,json=useInAllLv,proto3" json:"use_in_all_lv,omitempty"`
	Remark               string   `protobuf:"bytes,6,opt,name=remark,proto3" json:"remark,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PreCheckApplyDrawCfgReq) Reset()         { *m = PreCheckApplyDrawCfgReq{} }
func (m *PreCheckApplyDrawCfgReq) String() string { return proto.CompactTextString(m) }
func (*PreCheckApplyDrawCfgReq) ProtoMessage()    {}
func (*PreCheckApplyDrawCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{53}
}
func (m *PreCheckApplyDrawCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreCheckApplyDrawCfgReq.Unmarshal(m, b)
}
func (m *PreCheckApplyDrawCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreCheckApplyDrawCfgReq.Marshal(b, m, deterministic)
}
func (dst *PreCheckApplyDrawCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreCheckApplyDrawCfgReq.Merge(dst, src)
}
func (m *PreCheckApplyDrawCfgReq) XXX_Size() int {
	return xxx_messageInfo_PreCheckApplyDrawCfgReq.Size(m)
}
func (m *PreCheckApplyDrawCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PreCheckApplyDrawCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_PreCheckApplyDrawCfgReq proto.InternalMessageInfo

func (m *PreCheckApplyDrawCfgReq) GetSceneId() []uint32 {
	if m != nil {
		return m.SceneId
	}
	return nil
}

func (m *PreCheckApplyDrawCfgReq) GetTimeLimitType() TimeType {
	if m != nil {
		return m.TimeLimitType
	}
	return TimeType_TIME_LIMIT_TYPE_UNSPECIFIED
}

func (m *PreCheckApplyDrawCfgReq) GetBeginTs() int64 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *PreCheckApplyDrawCfgReq) GetEndTs() int64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *PreCheckApplyDrawCfgReq) GetUseInAllLv() bool {
	if m != nil {
		return m.UseInAllLv
	}
	return false
}

func (m *PreCheckApplyDrawCfgReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

type PreCheckApplyDrawCfgResp struct {
	Notify               string   `protobuf:"bytes,1,opt,name=notify,proto3" json:"notify,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PreCheckApplyDrawCfgResp) Reset()         { *m = PreCheckApplyDrawCfgResp{} }
func (m *PreCheckApplyDrawCfgResp) String() string { return proto.CompactTextString(m) }
func (*PreCheckApplyDrawCfgResp) ProtoMessage()    {}
func (*PreCheckApplyDrawCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{54}
}
func (m *PreCheckApplyDrawCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreCheckApplyDrawCfgResp.Unmarshal(m, b)
}
func (m *PreCheckApplyDrawCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreCheckApplyDrawCfgResp.Marshal(b, m, deterministic)
}
func (dst *PreCheckApplyDrawCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreCheckApplyDrawCfgResp.Merge(dst, src)
}
func (m *PreCheckApplyDrawCfgResp) XXX_Size() int {
	return xxx_messageInfo_PreCheckApplyDrawCfgResp.Size(m)
}
func (m *PreCheckApplyDrawCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PreCheckApplyDrawCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_PreCheckApplyDrawCfgResp proto.InternalMessageInfo

func (m *PreCheckApplyDrawCfgResp) GetNotify() string {
	if m != nil {
		return m.Notify
	}
	return ""
}

// 下架相亲厅牵手资源配置
type CancelApplyDrawCfgReq struct {
	ApplyId              []uint32 `protobuf:"varint,1,rep,packed,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	Remark               string   `protobuf:"bytes,2,opt,name=remark,proto3" json:"remark,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelApplyDrawCfgReq) Reset()         { *m = CancelApplyDrawCfgReq{} }
func (m *CancelApplyDrawCfgReq) String() string { return proto.CompactTextString(m) }
func (*CancelApplyDrawCfgReq) ProtoMessage()    {}
func (*CancelApplyDrawCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{55}
}
func (m *CancelApplyDrawCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelApplyDrawCfgReq.Unmarshal(m, b)
}
func (m *CancelApplyDrawCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelApplyDrawCfgReq.Marshal(b, m, deterministic)
}
func (dst *CancelApplyDrawCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelApplyDrawCfgReq.Merge(dst, src)
}
func (m *CancelApplyDrawCfgReq) XXX_Size() int {
	return xxx_messageInfo_CancelApplyDrawCfgReq.Size(m)
}
func (m *CancelApplyDrawCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelApplyDrawCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelApplyDrawCfgReq proto.InternalMessageInfo

func (m *CancelApplyDrawCfgReq) GetApplyId() []uint32 {
	if m != nil {
		return m.ApplyId
	}
	return nil
}

func (m *CancelApplyDrawCfgReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

type CancelApplyDrawCfgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelApplyDrawCfgResp) Reset()         { *m = CancelApplyDrawCfgResp{} }
func (m *CancelApplyDrawCfgResp) String() string { return proto.CompactTextString(m) }
func (*CancelApplyDrawCfgResp) ProtoMessage()    {}
func (*CancelApplyDrawCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{56}
}
func (m *CancelApplyDrawCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelApplyDrawCfgResp.Unmarshal(m, b)
}
func (m *CancelApplyDrawCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelApplyDrawCfgResp.Marshal(b, m, deterministic)
}
func (dst *CancelApplyDrawCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelApplyDrawCfgResp.Merge(dst, src)
}
func (m *CancelApplyDrawCfgResp) XXX_Size() int {
	return xxx_messageInfo_CancelApplyDrawCfgResp.Size(m)
}
func (m *CancelApplyDrawCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelApplyDrawCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelApplyDrawCfgResp proto.InternalMessageInfo

type DrawApplyInfo struct {
	ApplyId              uint32   `protobuf:"varint,1,opt,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	SceneId              uint32   `protobuf:"varint,2,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	BeginTs              int64    `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                int64    `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	UseInAllLv           bool     `protobuf:"varint,5,opt,name=use_in_all_lv,json=useInAllLv,proto3" json:"use_in_all_lv,omitempty"`
	UpdateTs             int64    `protobuf:"varint,6,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	Remarks              string   `protobuf:"bytes,7,opt,name=remarks,proto3" json:"remarks,omitempty"`
	SceneName            string   `protobuf:"bytes,8,opt,name=scene_name,json=sceneName,proto3" json:"scene_name,omitempty"`
	BackgroundPic        string   `protobuf:"bytes,9,opt,name=background_pic,json=backgroundPic,proto3" json:"background_pic,omitempty"`
	TimeLimitType        TimeType `protobuf:"varint,10,opt,name=time_limit_type,json=timeLimitType,proto3,enum=channel_dating_game.TimeType" json:"time_limit_type,omitempty"`
	Tbean                uint32   `protobuf:"varint,11,opt,name=tbean,proto3" json:"tbean,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DrawApplyInfo) Reset()         { *m = DrawApplyInfo{} }
func (m *DrawApplyInfo) String() string { return proto.CompactTextString(m) }
func (*DrawApplyInfo) ProtoMessage()    {}
func (*DrawApplyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{57}
}
func (m *DrawApplyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DrawApplyInfo.Unmarshal(m, b)
}
func (m *DrawApplyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DrawApplyInfo.Marshal(b, m, deterministic)
}
func (dst *DrawApplyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DrawApplyInfo.Merge(dst, src)
}
func (m *DrawApplyInfo) XXX_Size() int {
	return xxx_messageInfo_DrawApplyInfo.Size(m)
}
func (m *DrawApplyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DrawApplyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DrawApplyInfo proto.InternalMessageInfo

func (m *DrawApplyInfo) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

func (m *DrawApplyInfo) GetSceneId() uint32 {
	if m != nil {
		return m.SceneId
	}
	return 0
}

func (m *DrawApplyInfo) GetBeginTs() int64 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *DrawApplyInfo) GetEndTs() int64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *DrawApplyInfo) GetUseInAllLv() bool {
	if m != nil {
		return m.UseInAllLv
	}
	return false
}

func (m *DrawApplyInfo) GetUpdateTs() int64 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *DrawApplyInfo) GetRemarks() string {
	if m != nil {
		return m.Remarks
	}
	return ""
}

func (m *DrawApplyInfo) GetSceneName() string {
	if m != nil {
		return m.SceneName
	}
	return ""
}

func (m *DrawApplyInfo) GetBackgroundPic() string {
	if m != nil {
		return m.BackgroundPic
	}
	return ""
}

func (m *DrawApplyInfo) GetTimeLimitType() TimeType {
	if m != nil {
		return m.TimeLimitType
	}
	return TimeType_TIME_LIMIT_TYPE_UNSPECIFIED
}

func (m *DrawApplyInfo) GetTbean() uint32 {
	if m != nil {
		return m.Tbean
	}
	return 0
}

// 获取相亲厅牵手资源上下架状态信息（全量返回）
type GetApplyDrawCfgStateReq struct {
	GetState             DrawState `protobuf:"varint,1,opt,name=get_state,json=getState,proto3,enum=channel_dating_game.DrawState" json:"get_state,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetApplyDrawCfgStateReq) Reset()         { *m = GetApplyDrawCfgStateReq{} }
func (m *GetApplyDrawCfgStateReq) String() string { return proto.CompactTextString(m) }
func (*GetApplyDrawCfgStateReq) ProtoMessage()    {}
func (*GetApplyDrawCfgStateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{58}
}
func (m *GetApplyDrawCfgStateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplyDrawCfgStateReq.Unmarshal(m, b)
}
func (m *GetApplyDrawCfgStateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplyDrawCfgStateReq.Marshal(b, m, deterministic)
}
func (dst *GetApplyDrawCfgStateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplyDrawCfgStateReq.Merge(dst, src)
}
func (m *GetApplyDrawCfgStateReq) XXX_Size() int {
	return xxx_messageInfo_GetApplyDrawCfgStateReq.Size(m)
}
func (m *GetApplyDrawCfgStateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplyDrawCfgStateReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplyDrawCfgStateReq proto.InternalMessageInfo

func (m *GetApplyDrawCfgStateReq) GetGetState() DrawState {
	if m != nil {
		return m.GetState
	}
	return DrawState_DRAW_STATE_UNSPECIFIED
}

type GetApplyDrawCfgStateResp struct {
	ApplyList            []*DrawApplyInfo `protobuf:"bytes,1,rep,name=apply_list,json=applyList,proto3" json:"apply_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetApplyDrawCfgStateResp) Reset()         { *m = GetApplyDrawCfgStateResp{} }
func (m *GetApplyDrawCfgStateResp) String() string { return proto.CompactTextString(m) }
func (*GetApplyDrawCfgStateResp) ProtoMessage()    {}
func (*GetApplyDrawCfgStateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{59}
}
func (m *GetApplyDrawCfgStateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplyDrawCfgStateResp.Unmarshal(m, b)
}
func (m *GetApplyDrawCfgStateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplyDrawCfgStateResp.Marshal(b, m, deterministic)
}
func (dst *GetApplyDrawCfgStateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplyDrawCfgStateResp.Merge(dst, src)
}
func (m *GetApplyDrawCfgStateResp) XXX_Size() int {
	return xxx_messageInfo_GetApplyDrawCfgStateResp.Size(m)
}
func (m *GetApplyDrawCfgStateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplyDrawCfgStateResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplyDrawCfgStateResp proto.InternalMessageInfo

func (m *GetApplyDrawCfgStateResp) GetApplyList() []*DrawApplyInfo {
	if m != nil {
		return m.ApplyList
	}
	return nil
}

// 编辑已上架配置
type EditApplyDrawCfgStateReq struct {
	Cfg                  *DrawApplyInfo `protobuf:"bytes,1,opt,name=cfg,proto3" json:"cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *EditApplyDrawCfgStateReq) Reset()         { *m = EditApplyDrawCfgStateReq{} }
func (m *EditApplyDrawCfgStateReq) String() string { return proto.CompactTextString(m) }
func (*EditApplyDrawCfgStateReq) ProtoMessage()    {}
func (*EditApplyDrawCfgStateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{60}
}
func (m *EditApplyDrawCfgStateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditApplyDrawCfgStateReq.Unmarshal(m, b)
}
func (m *EditApplyDrawCfgStateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditApplyDrawCfgStateReq.Marshal(b, m, deterministic)
}
func (dst *EditApplyDrawCfgStateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditApplyDrawCfgStateReq.Merge(dst, src)
}
func (m *EditApplyDrawCfgStateReq) XXX_Size() int {
	return xxx_messageInfo_EditApplyDrawCfgStateReq.Size(m)
}
func (m *EditApplyDrawCfgStateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EditApplyDrawCfgStateReq.DiscardUnknown(m)
}

var xxx_messageInfo_EditApplyDrawCfgStateReq proto.InternalMessageInfo

func (m *EditApplyDrawCfgStateReq) GetCfg() *DrawApplyInfo {
	if m != nil {
		return m.Cfg
	}
	return nil
}

type EditApplyDrawCfgStateResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EditApplyDrawCfgStateResp) Reset()         { *m = EditApplyDrawCfgStateResp{} }
func (m *EditApplyDrawCfgStateResp) String() string { return proto.CompactTextString(m) }
func (*EditApplyDrawCfgStateResp) ProtoMessage()    {}
func (*EditApplyDrawCfgStateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{61}
}
func (m *EditApplyDrawCfgStateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditApplyDrawCfgStateResp.Unmarshal(m, b)
}
func (m *EditApplyDrawCfgStateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditApplyDrawCfgStateResp.Marshal(b, m, deterministic)
}
func (dst *EditApplyDrawCfgStateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditApplyDrawCfgStateResp.Merge(dst, src)
}
func (m *EditApplyDrawCfgStateResp) XXX_Size() int {
	return xxx_messageInfo_EditApplyDrawCfgStateResp.Size(m)
}
func (m *EditApplyDrawCfgStateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EditApplyDrawCfgStateResp.DiscardUnknown(m)
}

var xxx_messageInfo_EditApplyDrawCfgStateResp proto.InternalMessageInfo

type SceneFellowInfo struct {
	SceneId              uint32   `protobuf:"varint,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	SceneName            string   `protobuf:"bytes,2,opt,name=scene_name,json=sceneName,proto3" json:"scene_name,omitempty"`
	Nameplate            string   `protobuf:"bytes,3,opt,name=nameplate,proto3" json:"nameplate,omitempty"`
	NameplateUnique      string   `protobuf:"bytes,4,opt,name=nameplate_unique,json=nameplateUnique,proto3" json:"nameplate_unique,omitempty"`
	Background           string   `protobuf:"bytes,5,opt,name=background,proto3" json:"background,omitempty"`
	SceneScore           uint32   `protobuf:"varint,6,opt,name=scene_score,json=sceneScore,proto3" json:"scene_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SceneFellowInfo) Reset()         { *m = SceneFellowInfo{} }
func (m *SceneFellowInfo) String() string { return proto.CompactTextString(m) }
func (*SceneFellowInfo) ProtoMessage()    {}
func (*SceneFellowInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{62}
}
func (m *SceneFellowInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SceneFellowInfo.Unmarshal(m, b)
}
func (m *SceneFellowInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SceneFellowInfo.Marshal(b, m, deterministic)
}
func (dst *SceneFellowInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SceneFellowInfo.Merge(dst, src)
}
func (m *SceneFellowInfo) XXX_Size() int {
	return xxx_messageInfo_SceneFellowInfo.Size(m)
}
func (m *SceneFellowInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SceneFellowInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SceneFellowInfo proto.InternalMessageInfo

func (m *SceneFellowInfo) GetSceneId() uint32 {
	if m != nil {
		return m.SceneId
	}
	return 0
}

func (m *SceneFellowInfo) GetSceneName() string {
	if m != nil {
		return m.SceneName
	}
	return ""
}

func (m *SceneFellowInfo) GetNameplate() string {
	if m != nil {
		return m.Nameplate
	}
	return ""
}

func (m *SceneFellowInfo) GetNameplateUnique() string {
	if m != nil {
		return m.NameplateUnique
	}
	return ""
}

func (m *SceneFellowInfo) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

func (m *SceneFellowInfo) GetSceneScore() uint32 {
	if m != nil {
		return m.SceneScore
	}
	return 0
}

// 全量获取挚友相关场景信息
type GetAllSceneFellowInfoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllSceneFellowInfoReq) Reset()         { *m = GetAllSceneFellowInfoReq{} }
func (m *GetAllSceneFellowInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetAllSceneFellowInfoReq) ProtoMessage()    {}
func (*GetAllSceneFellowInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{63}
}
func (m *GetAllSceneFellowInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllSceneFellowInfoReq.Unmarshal(m, b)
}
func (m *GetAllSceneFellowInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllSceneFellowInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetAllSceneFellowInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllSceneFellowInfoReq.Merge(dst, src)
}
func (m *GetAllSceneFellowInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetAllSceneFellowInfoReq.Size(m)
}
func (m *GetAllSceneFellowInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllSceneFellowInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllSceneFellowInfoReq proto.InternalMessageInfo

type GetAllSceneFellowInfoResp struct {
	SceneList            []*SceneFellowInfo `protobuf:"bytes,1,rep,name=scene_list,json=sceneList,proto3" json:"scene_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetAllSceneFellowInfoResp) Reset()         { *m = GetAllSceneFellowInfoResp{} }
func (m *GetAllSceneFellowInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetAllSceneFellowInfoResp) ProtoMessage()    {}
func (*GetAllSceneFellowInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{64}
}
func (m *GetAllSceneFellowInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllSceneFellowInfoResp.Unmarshal(m, b)
}
func (m *GetAllSceneFellowInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllSceneFellowInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetAllSceneFellowInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllSceneFellowInfoResp.Merge(dst, src)
}
func (m *GetAllSceneFellowInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetAllSceneFellowInfoResp.Size(m)
}
func (m *GetAllSceneFellowInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllSceneFellowInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllSceneFellowInfoResp proto.InternalMessageInfo

func (m *GetAllSceneFellowInfoResp) GetSceneList() []*SceneFellowInfo {
	if m != nil {
		return m.SceneList
	}
	return nil
}

type GetLocalApplyDrawCfgReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLocalApplyDrawCfgReq) Reset()         { *m = GetLocalApplyDrawCfgReq{} }
func (m *GetLocalApplyDrawCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetLocalApplyDrawCfgReq) ProtoMessage()    {}
func (*GetLocalApplyDrawCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{65}
}
func (m *GetLocalApplyDrawCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLocalApplyDrawCfgReq.Unmarshal(m, b)
}
func (m *GetLocalApplyDrawCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLocalApplyDrawCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetLocalApplyDrawCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLocalApplyDrawCfgReq.Merge(dst, src)
}
func (m *GetLocalApplyDrawCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetLocalApplyDrawCfgReq.Size(m)
}
func (m *GetLocalApplyDrawCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLocalApplyDrawCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLocalApplyDrawCfgReq proto.InternalMessageInfo

type GetLocalApplyDrawCfgResp struct {
	ApplyList            []*DatingGameDrawCfg `protobuf:"bytes,1,rep,name=apply_list,json=applyList,proto3" json:"apply_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetLocalApplyDrawCfgResp) Reset()         { *m = GetLocalApplyDrawCfgResp{} }
func (m *GetLocalApplyDrawCfgResp) String() string { return proto.CompactTextString(m) }
func (*GetLocalApplyDrawCfgResp) ProtoMessage()    {}
func (*GetLocalApplyDrawCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{66}
}
func (m *GetLocalApplyDrawCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLocalApplyDrawCfgResp.Unmarshal(m, b)
}
func (m *GetLocalApplyDrawCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLocalApplyDrawCfgResp.Marshal(b, m, deterministic)
}
func (dst *GetLocalApplyDrawCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLocalApplyDrawCfgResp.Merge(dst, src)
}
func (m *GetLocalApplyDrawCfgResp) XXX_Size() int {
	return xxx_messageInfo_GetLocalApplyDrawCfgResp.Size(m)
}
func (m *GetLocalApplyDrawCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLocalApplyDrawCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLocalApplyDrawCfgResp proto.InternalMessageInfo

func (m *GetLocalApplyDrawCfgResp) GetApplyList() []*DatingGameDrawCfg {
	if m != nil {
		return m.ApplyList
	}
	return nil
}

type BatDelDrawCfgBySceneIdReq struct {
	SceneIdList          []uint32 `protobuf:"varint,1,rep,packed,name=scene_id_list,json=sceneIdList,proto3" json:"scene_id_list,omitempty"`
	SecretKey            string   `protobuf:"bytes,2,opt,name=secret_key,json=secretKey,proto3" json:"secret_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatDelDrawCfgBySceneIdReq) Reset()         { *m = BatDelDrawCfgBySceneIdReq{} }
func (m *BatDelDrawCfgBySceneIdReq) String() string { return proto.CompactTextString(m) }
func (*BatDelDrawCfgBySceneIdReq) ProtoMessage()    {}
func (*BatDelDrawCfgBySceneIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{67}
}
func (m *BatDelDrawCfgBySceneIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatDelDrawCfgBySceneIdReq.Unmarshal(m, b)
}
func (m *BatDelDrawCfgBySceneIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatDelDrawCfgBySceneIdReq.Marshal(b, m, deterministic)
}
func (dst *BatDelDrawCfgBySceneIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatDelDrawCfgBySceneIdReq.Merge(dst, src)
}
func (m *BatDelDrawCfgBySceneIdReq) XXX_Size() int {
	return xxx_messageInfo_BatDelDrawCfgBySceneIdReq.Size(m)
}
func (m *BatDelDrawCfgBySceneIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatDelDrawCfgBySceneIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatDelDrawCfgBySceneIdReq proto.InternalMessageInfo

func (m *BatDelDrawCfgBySceneIdReq) GetSceneIdList() []uint32 {
	if m != nil {
		return m.SceneIdList
	}
	return nil
}

func (m *BatDelDrawCfgBySceneIdReq) GetSecretKey() string {
	if m != nil {
		return m.SecretKey
	}
	return ""
}

type BatDelDrawCfgBySceneIdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatDelDrawCfgBySceneIdResp) Reset()         { *m = BatDelDrawCfgBySceneIdResp{} }
func (m *BatDelDrawCfgBySceneIdResp) String() string { return proto.CompactTextString(m) }
func (*BatDelDrawCfgBySceneIdResp) ProtoMessage()    {}
func (*BatDelDrawCfgBySceneIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{68}
}
func (m *BatDelDrawCfgBySceneIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatDelDrawCfgBySceneIdResp.Unmarshal(m, b)
}
func (m *BatDelDrawCfgBySceneIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatDelDrawCfgBySceneIdResp.Marshal(b, m, deterministic)
}
func (dst *BatDelDrawCfgBySceneIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatDelDrawCfgBySceneIdResp.Merge(dst, src)
}
func (m *BatDelDrawCfgBySceneIdResp) XXX_Size() int {
	return xxx_messageInfo_BatDelDrawCfgBySceneIdResp.Size(m)
}
func (m *BatDelDrawCfgBySceneIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatDelDrawCfgBySceneIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatDelDrawCfgBySceneIdResp proto.InternalMessageInfo

type BatchGetChannelDatingGamePhaseReq struct {
	ChannelId            []uint32 `protobuf:"varint,1,rep,packed,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelDatingGamePhaseReq) Reset()         { *m = BatchGetChannelDatingGamePhaseReq{} }
func (m *BatchGetChannelDatingGamePhaseReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelDatingGamePhaseReq) ProtoMessage()    {}
func (*BatchGetChannelDatingGamePhaseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{69}
}
func (m *BatchGetChannelDatingGamePhaseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelDatingGamePhaseReq.Unmarshal(m, b)
}
func (m *BatchGetChannelDatingGamePhaseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelDatingGamePhaseReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelDatingGamePhaseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelDatingGamePhaseReq.Merge(dst, src)
}
func (m *BatchGetChannelDatingGamePhaseReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelDatingGamePhaseReq.Size(m)
}
func (m *BatchGetChannelDatingGamePhaseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelDatingGamePhaseReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelDatingGamePhaseReq proto.InternalMessageInfo

func (m *BatchGetChannelDatingGamePhaseReq) GetChannelId() []uint32 {
	if m != nil {
		return m.ChannelId
	}
	return nil
}

type BatchGetChannelDatingGamePhaseResp struct {
	PhaseMap             map[uint32]uint32 `protobuf:"bytes,1,rep,name=phase_map,json=phaseMap,proto3" json:"phase_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetChannelDatingGamePhaseResp) Reset()         { *m = BatchGetChannelDatingGamePhaseResp{} }
func (m *BatchGetChannelDatingGamePhaseResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelDatingGamePhaseResp) ProtoMessage()    {}
func (*BatchGetChannelDatingGamePhaseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_dating_game_5fb1af7a1a7baa64, []int{70}
}
func (m *BatchGetChannelDatingGamePhaseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelDatingGamePhaseResp.Unmarshal(m, b)
}
func (m *BatchGetChannelDatingGamePhaseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelDatingGamePhaseResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelDatingGamePhaseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelDatingGamePhaseResp.Merge(dst, src)
}
func (m *BatchGetChannelDatingGamePhaseResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelDatingGamePhaseResp.Size(m)
}
func (m *BatchGetChannelDatingGamePhaseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelDatingGamePhaseResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelDatingGamePhaseResp proto.InternalMessageInfo

func (m *BatchGetChannelDatingGamePhaseResp) GetPhaseMap() map[uint32]uint32 {
	if m != nil {
		return m.PhaseMap
	}
	return nil
}

func init() {
	proto.RegisterType((*HatUser)(nil), "channel_dating_game.HatUser")
	proto.RegisterType((*GetDatingGameCurInfoReq)(nil), "channel_dating_game.GetDatingGameCurInfoReq")
	proto.RegisterType((*GetDatingGameCurInfoResp)(nil), "channel_dating_game.GetDatingGameCurInfoResp")
	proto.RegisterType((*OpenDatingGameEntryReq)(nil), "channel_dating_game.OpenDatingGameEntryReq")
	proto.RegisterType((*OpenDatingGameEntryResp)(nil), "channel_dating_game.OpenDatingGameEntryResp")
	proto.RegisterType((*CloseDatingGameEntryReq)(nil), "channel_dating_game.CloseDatingGameEntryReq")
	proto.RegisterType((*CloseDatingGameEntryResp)(nil), "channel_dating_game.CloseDatingGameEntryResp")
	proto.RegisterType((*CheckDatingGameEntryReq)(nil), "channel_dating_game.CheckDatingGameEntryReq")
	proto.RegisterType((*CheckDatingGameEntryResp)(nil), "channel_dating_game.CheckDatingGameEntryResp")
	proto.RegisterType((*SetGamePhaseReq)(nil), "channel_dating_game.SetGamePhaseReq")
	proto.RegisterType((*SetGamePhaseResp)(nil), "channel_dating_game.SetGamePhaseResp")
	proto.RegisterType((*GetGamePhaseReq)(nil), "channel_dating_game.GetGamePhaseReq")
	proto.RegisterType((*GetGamePhaseResp)(nil), "channel_dating_game.GetGamePhaseResp")
	proto.RegisterType((*DatingMember)(nil), "channel_dating_game.DatingMember")
	proto.RegisterType((*InitDatingMemberReq)(nil), "channel_dating_game.InitDatingMemberReq")
	proto.RegisterType((*InitDatingMemberResp)(nil), "channel_dating_game.InitDatingMemberResp")
	proto.RegisterType((*GetUserLikeBeatValReq)(nil), "channel_dating_game.GetUserLikeBeatValReq")
	proto.RegisterType((*GetUserLikeBeatValResp)(nil), "channel_dating_game.GetUserLikeBeatValResp")
	proto.RegisterType((*GetUserRankLikeBeatValReq)(nil), "channel_dating_game.GetUserRankLikeBeatValReq")
	proto.RegisterType((*GetUserRankLikeBeatValResp)(nil), "channel_dating_game.GetUserRankLikeBeatValResp")
	proto.RegisterType((*UserLikeBeatInfo)(nil), "channel_dating_game.UserLikeBeatInfo")
	proto.RegisterType((*SetUserLikeBeatObjReq)(nil), "channel_dating_game.SetUserLikeBeatObjReq")
	proto.RegisterType((*SetUserLikeBeatObjResp)(nil), "channel_dating_game.SetUserLikeBeatObjResp")
	proto.RegisterType((*GetUserLikeBeatObjReq)(nil), "channel_dating_game.GetUserLikeBeatObjReq")
	proto.RegisterType((*GetUserLikeBeatObjResp)(nil), "channel_dating_game.GetUserLikeBeatObjResp")
	proto.RegisterType((*GetSelectLikeBeatObjUserReq)(nil), "channel_dating_game.GetSelectLikeBeatObjUserReq")
	proto.RegisterType((*GetSelectLikeBeatObjUserResp)(nil), "channel_dating_game.GetSelectLikeBeatObjUserResp")
	proto.RegisterType((*MatchLikeBeatInfo)(nil), "channel_dating_game.MatchLikeBeatInfo")
	proto.RegisterType((*GetOpenLikeUserListReq)(nil), "channel_dating_game.GetOpenLikeUserListReq")
	proto.RegisterType((*GetOpenLikeUserListResp)(nil), "channel_dating_game.GetOpenLikeUserListResp")
	proto.RegisterType((*OpenLikeUserInfo)(nil), "channel_dating_game.OpenLikeUserInfo")
	proto.RegisterType((*UserApplyMicReq)(nil), "channel_dating_game.UserApplyMicReq")
	proto.RegisterType((*UserApplyMicResp)(nil), "channel_dating_game.UserApplyMicResp")
	proto.RegisterType((*GetApplyMicUserListReq)(nil), "channel_dating_game.GetApplyMicUserListReq")
	proto.RegisterType((*GetApplyMicUserListResp)(nil), "channel_dating_game.GetApplyMicUserListResp")
	proto.RegisterType((*DatingGameHatCfg)(nil), "channel_dating_game.DatingGameHatCfg")
	proto.RegisterType((*GetVipMicUserReq)(nil), "channel_dating_game.GetVipMicUserReq")
	proto.RegisterType((*GetVipMicUserResp)(nil), "channel_dating_game.GetVipMicUserResp")
	proto.RegisterType((*ConfirmVipHoldMicReq)(nil), "channel_dating_game.ConfirmVipHoldMicReq")
	proto.RegisterType((*ConfirmVipHoldMicResp)(nil), "channel_dating_game.ConfirmVipHoldMicResp")
	proto.RegisterType((*TestDrawImageReq)(nil), "channel_dating_game.TestDrawImageReq")
	proto.RegisterType((*TestDrawImageResp)(nil), "channel_dating_game.TestDrawImageResp")
	proto.RegisterType((*DatingGameDrawCfg)(nil), "channel_dating_game.DatingGameDrawCfg")
	proto.RegisterType((*AddDatingDrawCfgReq)(nil), "channel_dating_game.AddDatingDrawCfgReq")
	proto.RegisterType((*AddDatingDrawCfgResp)(nil), "channel_dating_game.AddDatingDrawCfgResp")
	proto.RegisterType((*UpdateDatingDrawCfgReq)(nil), "channel_dating_game.UpdateDatingDrawCfgReq")
	proto.RegisterType((*UpdateDatingDrawCfgResp)(nil), "channel_dating_game.UpdateDatingDrawCfgResp")
	proto.RegisterType((*BatGetDatingDrawCfgReq)(nil), "channel_dating_game.BatGetDatingDrawCfgReq")
	proto.RegisterType((*BatGetDatingDrawCfgResp)(nil), "channel_dating_game.BatGetDatingDrawCfgResp")
	proto.RegisterType((*BatGetDrawCfgBySceneIdReq)(nil), "channel_dating_game.BatGetDrawCfgBySceneIdReq")
	proto.RegisterType((*BatGetDrawCfgBySceneIdResp)(nil), "channel_dating_game.BatGetDrawCfgBySceneIdResp")
	proto.RegisterType((*ApplyDatingDrawCfgReq)(nil), "channel_dating_game.ApplyDatingDrawCfgReq")
	proto.RegisterType((*ApplyDatingDrawCfgResp)(nil), "channel_dating_game.ApplyDatingDrawCfgResp")
	proto.RegisterType((*PreCheckApplyDrawCfgReq)(nil), "channel_dating_game.PreCheckApplyDrawCfgReq")
	proto.RegisterType((*PreCheckApplyDrawCfgResp)(nil), "channel_dating_game.PreCheckApplyDrawCfgResp")
	proto.RegisterType((*CancelApplyDrawCfgReq)(nil), "channel_dating_game.CancelApplyDrawCfgReq")
	proto.RegisterType((*CancelApplyDrawCfgResp)(nil), "channel_dating_game.CancelApplyDrawCfgResp")
	proto.RegisterType((*DrawApplyInfo)(nil), "channel_dating_game.DrawApplyInfo")
	proto.RegisterType((*GetApplyDrawCfgStateReq)(nil), "channel_dating_game.GetApplyDrawCfgStateReq")
	proto.RegisterType((*GetApplyDrawCfgStateResp)(nil), "channel_dating_game.GetApplyDrawCfgStateResp")
	proto.RegisterType((*EditApplyDrawCfgStateReq)(nil), "channel_dating_game.EditApplyDrawCfgStateReq")
	proto.RegisterType((*EditApplyDrawCfgStateResp)(nil), "channel_dating_game.EditApplyDrawCfgStateResp")
	proto.RegisterType((*SceneFellowInfo)(nil), "channel_dating_game.SceneFellowInfo")
	proto.RegisterType((*GetAllSceneFellowInfoReq)(nil), "channel_dating_game.GetAllSceneFellowInfoReq")
	proto.RegisterType((*GetAllSceneFellowInfoResp)(nil), "channel_dating_game.GetAllSceneFellowInfoResp")
	proto.RegisterType((*GetLocalApplyDrawCfgReq)(nil), "channel_dating_game.GetLocalApplyDrawCfgReq")
	proto.RegisterType((*GetLocalApplyDrawCfgResp)(nil), "channel_dating_game.GetLocalApplyDrawCfgResp")
	proto.RegisterType((*BatDelDrawCfgBySceneIdReq)(nil), "channel_dating_game.BatDelDrawCfgBySceneIdReq")
	proto.RegisterType((*BatDelDrawCfgBySceneIdResp)(nil), "channel_dating_game.BatDelDrawCfgBySceneIdResp")
	proto.RegisterType((*BatchGetChannelDatingGamePhaseReq)(nil), "channel_dating_game.BatchGetChannelDatingGamePhaseReq")
	proto.RegisterType((*BatchGetChannelDatingGamePhaseResp)(nil), "channel_dating_game.BatchGetChannelDatingGamePhaseResp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "channel_dating_game.BatchGetChannelDatingGamePhaseResp.PhaseMapEntry")
	proto.RegisterEnum("channel_dating_game.SortByType", SortByType_name, SortByType_value)
	proto.RegisterEnum("channel_dating_game.TimeType", TimeType_name, TimeType_value)
	proto.RegisterEnum("channel_dating_game.DrawState", DrawState_name, DrawState_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelDatingGameClient is the client API for ChannelDatingGame service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelDatingGameClient interface {
	OpenDatingGameEntry(ctx context.Context, in *OpenDatingGameEntryReq, opts ...grpc.CallOption) (*OpenDatingGameEntryResp, error)
	CheckDatingGameEntry(ctx context.Context, in *CheckDatingGameEntryReq, opts ...grpc.CallOption) (*CheckDatingGameEntryResp, error)
	SetGamePhase(ctx context.Context, in *SetGamePhaseReq, opts ...grpc.CallOption) (*SetGamePhaseResp, error)
	GetGamePhase(ctx context.Context, in *GetGamePhaseReq, opts ...grpc.CallOption) (*GetGamePhaseResp, error)
	GetUserLikeBeatVal(ctx context.Context, in *GetUserLikeBeatValReq, opts ...grpc.CallOption) (*GetUserLikeBeatValResp, error)
	GetUserRankLikeBeatVal(ctx context.Context, in *GetUserRankLikeBeatValReq, opts ...grpc.CallOption) (*GetUserRankLikeBeatValResp, error)
	SetUserLikeBeatObj(ctx context.Context, in *SetUserLikeBeatObjReq, opts ...grpc.CallOption) (*SetUserLikeBeatObjResp, error)
	GetUserLikeBeatObj(ctx context.Context, in *GetUserLikeBeatObjReq, opts ...grpc.CallOption) (*GetUserLikeBeatObjResp, error)
	UserApplyMic(ctx context.Context, in *UserApplyMicReq, opts ...grpc.CallOption) (*UserApplyMicResp, error)
	GetApplyMicUserList(ctx context.Context, in *GetApplyMicUserListReq, opts ...grpc.CallOption) (*GetApplyMicUserListResp, error)
	InitDatingMember(ctx context.Context, in *InitDatingMemberReq, opts ...grpc.CallOption) (*InitDatingMemberResp, error)
	GetVipMicUser(ctx context.Context, in *GetVipMicUserReq, opts ...grpc.CallOption) (*GetVipMicUserResp, error)
	GetSelectLikeBeatObjUser(ctx context.Context, in *GetSelectLikeBeatObjUserReq, opts ...grpc.CallOption) (*GetSelectLikeBeatObjUserResp, error)
	ConfirmVipHoldMic(ctx context.Context, in *ConfirmVipHoldMicReq, opts ...grpc.CallOption) (*ConfirmVipHoldMicResp, error)
	GetDatingGameCurInfo(ctx context.Context, in *GetDatingGameCurInfoReq, opts ...grpc.CallOption) (*GetDatingGameCurInfoResp, error)
	GetOpenLikeUserList(ctx context.Context, in *GetOpenLikeUserListReq, opts ...grpc.CallOption) (*GetOpenLikeUserListResp, error)
	CloseDatingGameEntry(ctx context.Context, in *CloseDatingGameEntryReq, opts ...grpc.CallOption) (*CloseDatingGameEntryResp, error)
	TestDrawImage(ctx context.Context, in *TestDrawImageReq, opts ...grpc.CallOption) (*TestDrawImageResp, error)
	// 获取礼物kafka消费单数
	CntPresentEvLogTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	// 获取礼物kafka消费订单
	GetPresentEvLogOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 礼物Kafka消费补单
	FixPresentEvLogOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	// ================== 相亲厅牵手资源配置后台 ==================
	// 新增相亲厅牵手资源配置
	AddDatingDrawCfg(ctx context.Context, in *AddDatingDrawCfgReq, opts ...grpc.CallOption) (*AddDatingDrawCfgResp, error)
	// 更新相亲厅牵手资源配置
	UpdateDatingDrawCfg(ctx context.Context, in *UpdateDatingDrawCfgReq, opts ...grpc.CallOption) (*UpdateDatingDrawCfgResp, error)
	// 分页获取相亲厅牵手资源配置
	BatGetDatingDrawCfg(ctx context.Context, in *BatGetDatingDrawCfgReq, opts ...grpc.CallOption) (*BatGetDatingDrawCfgResp, error)
	// 通过场景id获取相亲厅牵手资源配置
	BatGetDrawCfgBySceneId(ctx context.Context, in *BatGetDrawCfgBySceneIdReq, opts ...grpc.CallOption) (*BatGetDrawCfgBySceneIdResp, error)
	// 删除牵手资源配置
	BatDelDrawCfgBySceneId(ctx context.Context, in *BatDelDrawCfgBySceneIdReq, opts ...grpc.CallOption) (*BatDelDrawCfgBySceneIdResp, error)
	// 上架相亲厅牵手资源配置
	ApplyDatingDrawCfg(ctx context.Context, in *ApplyDatingDrawCfgReq, opts ...grpc.CallOption) (*ApplyDatingDrawCfgResp, error)
	// 下架相亲厅牵手资源配置
	CancelApplyDrawCfg(ctx context.Context, in *CancelApplyDrawCfgReq, opts ...grpc.CallOption) (*CancelApplyDrawCfgResp, error)
	// 获取相亲厅牵手资源上下架状态信息
	GetApplyDrawCfgState(ctx context.Context, in *GetApplyDrawCfgStateReq, opts ...grpc.CallOption) (*GetApplyDrawCfgStateResp, error)
	// 编辑上下架配置
	EditApplyDrawCfgState(ctx context.Context, in *EditApplyDrawCfgStateReq, opts ...grpc.CallOption) (*EditApplyDrawCfgStateResp, error)
	// 上架长期牵手场景前置检查
	PreCheckApplyDrawCfg(ctx context.Context, in *PreCheckApplyDrawCfgReq, opts ...grpc.CallOption) (*PreCheckApplyDrawCfgResp, error)
	// 全量获取挚友相关场景信息
	GetAllSceneFellowInfo(ctx context.Context, in *GetAllSceneFellowInfoReq, opts ...grpc.CallOption) (*GetAllSceneFellowInfoResp, error)
	// 获取本地生效中的相亲厅牵手资源配置
	GetLocalApplyDrawCfg(ctx context.Context, in *GetLocalApplyDrawCfgReq, opts ...grpc.CallOption) (*GetLocalApplyDrawCfgResp, error)
	// 批量获取房间相亲当前阶段
	BatchGetChannelDatingGamePhase(ctx context.Context, in *BatchGetChannelDatingGamePhaseReq, opts ...grpc.CallOption) (*BatchGetChannelDatingGamePhaseResp, error)
}

type channelDatingGameClient struct {
	cc *grpc.ClientConn
}

func NewChannelDatingGameClient(cc *grpc.ClientConn) ChannelDatingGameClient {
	return &channelDatingGameClient{cc}
}

func (c *channelDatingGameClient) OpenDatingGameEntry(ctx context.Context, in *OpenDatingGameEntryReq, opts ...grpc.CallOption) (*OpenDatingGameEntryResp, error) {
	out := new(OpenDatingGameEntryResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/OpenDatingGameEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) CheckDatingGameEntry(ctx context.Context, in *CheckDatingGameEntryReq, opts ...grpc.CallOption) (*CheckDatingGameEntryResp, error) {
	out := new(CheckDatingGameEntryResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/CheckDatingGameEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) SetGamePhase(ctx context.Context, in *SetGamePhaseReq, opts ...grpc.CallOption) (*SetGamePhaseResp, error) {
	out := new(SetGamePhaseResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/SetGamePhase", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetGamePhase(ctx context.Context, in *GetGamePhaseReq, opts ...grpc.CallOption) (*GetGamePhaseResp, error) {
	out := new(GetGamePhaseResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/GetGamePhase", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetUserLikeBeatVal(ctx context.Context, in *GetUserLikeBeatValReq, opts ...grpc.CallOption) (*GetUserLikeBeatValResp, error) {
	out := new(GetUserLikeBeatValResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/GetUserLikeBeatVal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetUserRankLikeBeatVal(ctx context.Context, in *GetUserRankLikeBeatValReq, opts ...grpc.CallOption) (*GetUserRankLikeBeatValResp, error) {
	out := new(GetUserRankLikeBeatValResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/GetUserRankLikeBeatVal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) SetUserLikeBeatObj(ctx context.Context, in *SetUserLikeBeatObjReq, opts ...grpc.CallOption) (*SetUserLikeBeatObjResp, error) {
	out := new(SetUserLikeBeatObjResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/SetUserLikeBeatObj", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetUserLikeBeatObj(ctx context.Context, in *GetUserLikeBeatObjReq, opts ...grpc.CallOption) (*GetUserLikeBeatObjResp, error) {
	out := new(GetUserLikeBeatObjResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/GetUserLikeBeatObj", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) UserApplyMic(ctx context.Context, in *UserApplyMicReq, opts ...grpc.CallOption) (*UserApplyMicResp, error) {
	out := new(UserApplyMicResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/UserApplyMic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetApplyMicUserList(ctx context.Context, in *GetApplyMicUserListReq, opts ...grpc.CallOption) (*GetApplyMicUserListResp, error) {
	out := new(GetApplyMicUserListResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/GetApplyMicUserList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) InitDatingMember(ctx context.Context, in *InitDatingMemberReq, opts ...grpc.CallOption) (*InitDatingMemberResp, error) {
	out := new(InitDatingMemberResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/InitDatingMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetVipMicUser(ctx context.Context, in *GetVipMicUserReq, opts ...grpc.CallOption) (*GetVipMicUserResp, error) {
	out := new(GetVipMicUserResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/GetVipMicUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetSelectLikeBeatObjUser(ctx context.Context, in *GetSelectLikeBeatObjUserReq, opts ...grpc.CallOption) (*GetSelectLikeBeatObjUserResp, error) {
	out := new(GetSelectLikeBeatObjUserResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/GetSelectLikeBeatObjUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) ConfirmVipHoldMic(ctx context.Context, in *ConfirmVipHoldMicReq, opts ...grpc.CallOption) (*ConfirmVipHoldMicResp, error) {
	out := new(ConfirmVipHoldMicResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/ConfirmVipHoldMic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetDatingGameCurInfo(ctx context.Context, in *GetDatingGameCurInfoReq, opts ...grpc.CallOption) (*GetDatingGameCurInfoResp, error) {
	out := new(GetDatingGameCurInfoResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/GetDatingGameCurInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetOpenLikeUserList(ctx context.Context, in *GetOpenLikeUserListReq, opts ...grpc.CallOption) (*GetOpenLikeUserListResp, error) {
	out := new(GetOpenLikeUserListResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/GetOpenLikeUserList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) CloseDatingGameEntry(ctx context.Context, in *CloseDatingGameEntryReq, opts ...grpc.CallOption) (*CloseDatingGameEntryResp, error) {
	out := new(CloseDatingGameEntryResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/CloseDatingGameEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) TestDrawImage(ctx context.Context, in *TestDrawImageReq, opts ...grpc.CallOption) (*TestDrawImageResp, error) {
	out := new(TestDrawImageResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/TestDrawImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) CntPresentEvLogTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/CntPresentEvLogTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetPresentEvLogOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/GetPresentEvLogOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) FixPresentEvLogOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/FixPresentEvLogOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) AddDatingDrawCfg(ctx context.Context, in *AddDatingDrawCfgReq, opts ...grpc.CallOption) (*AddDatingDrawCfgResp, error) {
	out := new(AddDatingDrawCfgResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/AddDatingDrawCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) UpdateDatingDrawCfg(ctx context.Context, in *UpdateDatingDrawCfgReq, opts ...grpc.CallOption) (*UpdateDatingDrawCfgResp, error) {
	out := new(UpdateDatingDrawCfgResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/UpdateDatingDrawCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) BatGetDatingDrawCfg(ctx context.Context, in *BatGetDatingDrawCfgReq, opts ...grpc.CallOption) (*BatGetDatingDrawCfgResp, error) {
	out := new(BatGetDatingDrawCfgResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/BatGetDatingDrawCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) BatGetDrawCfgBySceneId(ctx context.Context, in *BatGetDrawCfgBySceneIdReq, opts ...grpc.CallOption) (*BatGetDrawCfgBySceneIdResp, error) {
	out := new(BatGetDrawCfgBySceneIdResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/BatGetDrawCfgBySceneId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) BatDelDrawCfgBySceneId(ctx context.Context, in *BatDelDrawCfgBySceneIdReq, opts ...grpc.CallOption) (*BatDelDrawCfgBySceneIdResp, error) {
	out := new(BatDelDrawCfgBySceneIdResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/BatDelDrawCfgBySceneId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) ApplyDatingDrawCfg(ctx context.Context, in *ApplyDatingDrawCfgReq, opts ...grpc.CallOption) (*ApplyDatingDrawCfgResp, error) {
	out := new(ApplyDatingDrawCfgResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/ApplyDatingDrawCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) CancelApplyDrawCfg(ctx context.Context, in *CancelApplyDrawCfgReq, opts ...grpc.CallOption) (*CancelApplyDrawCfgResp, error) {
	out := new(CancelApplyDrawCfgResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/CancelApplyDrawCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetApplyDrawCfgState(ctx context.Context, in *GetApplyDrawCfgStateReq, opts ...grpc.CallOption) (*GetApplyDrawCfgStateResp, error) {
	out := new(GetApplyDrawCfgStateResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/GetApplyDrawCfgState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) EditApplyDrawCfgState(ctx context.Context, in *EditApplyDrawCfgStateReq, opts ...grpc.CallOption) (*EditApplyDrawCfgStateResp, error) {
	out := new(EditApplyDrawCfgStateResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/EditApplyDrawCfgState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) PreCheckApplyDrawCfg(ctx context.Context, in *PreCheckApplyDrawCfgReq, opts ...grpc.CallOption) (*PreCheckApplyDrawCfgResp, error) {
	out := new(PreCheckApplyDrawCfgResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/PreCheckApplyDrawCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetAllSceneFellowInfo(ctx context.Context, in *GetAllSceneFellowInfoReq, opts ...grpc.CallOption) (*GetAllSceneFellowInfoResp, error) {
	out := new(GetAllSceneFellowInfoResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/GetAllSceneFellowInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetLocalApplyDrawCfg(ctx context.Context, in *GetLocalApplyDrawCfgReq, opts ...grpc.CallOption) (*GetLocalApplyDrawCfgResp, error) {
	out := new(GetLocalApplyDrawCfgResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/GetLocalApplyDrawCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) BatchGetChannelDatingGamePhase(ctx context.Context, in *BatchGetChannelDatingGamePhaseReq, opts ...grpc.CallOption) (*BatchGetChannelDatingGamePhaseResp, error) {
	out := new(BatchGetChannelDatingGamePhaseResp)
	err := c.cc.Invoke(ctx, "/channel_dating_game.ChannelDatingGame/BatchGetChannelDatingGamePhase", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelDatingGameServer is the server API for ChannelDatingGame service.
type ChannelDatingGameServer interface {
	OpenDatingGameEntry(context.Context, *OpenDatingGameEntryReq) (*OpenDatingGameEntryResp, error)
	CheckDatingGameEntry(context.Context, *CheckDatingGameEntryReq) (*CheckDatingGameEntryResp, error)
	SetGamePhase(context.Context, *SetGamePhaseReq) (*SetGamePhaseResp, error)
	GetGamePhase(context.Context, *GetGamePhaseReq) (*GetGamePhaseResp, error)
	GetUserLikeBeatVal(context.Context, *GetUserLikeBeatValReq) (*GetUserLikeBeatValResp, error)
	GetUserRankLikeBeatVal(context.Context, *GetUserRankLikeBeatValReq) (*GetUserRankLikeBeatValResp, error)
	SetUserLikeBeatObj(context.Context, *SetUserLikeBeatObjReq) (*SetUserLikeBeatObjResp, error)
	GetUserLikeBeatObj(context.Context, *GetUserLikeBeatObjReq) (*GetUserLikeBeatObjResp, error)
	UserApplyMic(context.Context, *UserApplyMicReq) (*UserApplyMicResp, error)
	GetApplyMicUserList(context.Context, *GetApplyMicUserListReq) (*GetApplyMicUserListResp, error)
	InitDatingMember(context.Context, *InitDatingMemberReq) (*InitDatingMemberResp, error)
	GetVipMicUser(context.Context, *GetVipMicUserReq) (*GetVipMicUserResp, error)
	GetSelectLikeBeatObjUser(context.Context, *GetSelectLikeBeatObjUserReq) (*GetSelectLikeBeatObjUserResp, error)
	ConfirmVipHoldMic(context.Context, *ConfirmVipHoldMicReq) (*ConfirmVipHoldMicResp, error)
	GetDatingGameCurInfo(context.Context, *GetDatingGameCurInfoReq) (*GetDatingGameCurInfoResp, error)
	GetOpenLikeUserList(context.Context, *GetOpenLikeUserListReq) (*GetOpenLikeUserListResp, error)
	CloseDatingGameEntry(context.Context, *CloseDatingGameEntryReq) (*CloseDatingGameEntryResp, error)
	TestDrawImage(context.Context, *TestDrawImageReq) (*TestDrawImageResp, error)
	// 获取礼物kafka消费单数
	CntPresentEvLogTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	// 获取礼物kafka消费订单
	GetPresentEvLogOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 礼物Kafka消费补单
	FixPresentEvLogOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	// ================== 相亲厅牵手资源配置后台 ==================
	// 新增相亲厅牵手资源配置
	AddDatingDrawCfg(context.Context, *AddDatingDrawCfgReq) (*AddDatingDrawCfgResp, error)
	// 更新相亲厅牵手资源配置
	UpdateDatingDrawCfg(context.Context, *UpdateDatingDrawCfgReq) (*UpdateDatingDrawCfgResp, error)
	// 分页获取相亲厅牵手资源配置
	BatGetDatingDrawCfg(context.Context, *BatGetDatingDrawCfgReq) (*BatGetDatingDrawCfgResp, error)
	// 通过场景id获取相亲厅牵手资源配置
	BatGetDrawCfgBySceneId(context.Context, *BatGetDrawCfgBySceneIdReq) (*BatGetDrawCfgBySceneIdResp, error)
	// 删除牵手资源配置
	BatDelDrawCfgBySceneId(context.Context, *BatDelDrawCfgBySceneIdReq) (*BatDelDrawCfgBySceneIdResp, error)
	// 上架相亲厅牵手资源配置
	ApplyDatingDrawCfg(context.Context, *ApplyDatingDrawCfgReq) (*ApplyDatingDrawCfgResp, error)
	// 下架相亲厅牵手资源配置
	CancelApplyDrawCfg(context.Context, *CancelApplyDrawCfgReq) (*CancelApplyDrawCfgResp, error)
	// 获取相亲厅牵手资源上下架状态信息
	GetApplyDrawCfgState(context.Context, *GetApplyDrawCfgStateReq) (*GetApplyDrawCfgStateResp, error)
	// 编辑上下架配置
	EditApplyDrawCfgState(context.Context, *EditApplyDrawCfgStateReq) (*EditApplyDrawCfgStateResp, error)
	// 上架长期牵手场景前置检查
	PreCheckApplyDrawCfg(context.Context, *PreCheckApplyDrawCfgReq) (*PreCheckApplyDrawCfgResp, error)
	// 全量获取挚友相关场景信息
	GetAllSceneFellowInfo(context.Context, *GetAllSceneFellowInfoReq) (*GetAllSceneFellowInfoResp, error)
	// 获取本地生效中的相亲厅牵手资源配置
	GetLocalApplyDrawCfg(context.Context, *GetLocalApplyDrawCfgReq) (*GetLocalApplyDrawCfgResp, error)
	// 批量获取房间相亲当前阶段
	BatchGetChannelDatingGamePhase(context.Context, *BatchGetChannelDatingGamePhaseReq) (*BatchGetChannelDatingGamePhaseResp, error)
}

func RegisterChannelDatingGameServer(s *grpc.Server, srv ChannelDatingGameServer) {
	s.RegisterService(&_ChannelDatingGame_serviceDesc, srv)
}

func _ChannelDatingGame_OpenDatingGameEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OpenDatingGameEntryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).OpenDatingGameEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/OpenDatingGameEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).OpenDatingGameEntry(ctx, req.(*OpenDatingGameEntryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_CheckDatingGameEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckDatingGameEntryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).CheckDatingGameEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/CheckDatingGameEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).CheckDatingGameEntry(ctx, req.(*CheckDatingGameEntryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_SetGamePhase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGamePhaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).SetGamePhase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/SetGamePhase",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).SetGamePhase(ctx, req.(*SetGamePhaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetGamePhase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGamePhaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetGamePhase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/GetGamePhase",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetGamePhase(ctx, req.(*GetGamePhaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetUserLikeBeatVal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLikeBeatValReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetUserLikeBeatVal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/GetUserLikeBeatVal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetUserLikeBeatVal(ctx, req.(*GetUserLikeBeatValReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetUserRankLikeBeatVal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRankLikeBeatValReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetUserRankLikeBeatVal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/GetUserRankLikeBeatVal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetUserRankLikeBeatVal(ctx, req.(*GetUserRankLikeBeatValReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_SetUserLikeBeatObj_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserLikeBeatObjReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).SetUserLikeBeatObj(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/SetUserLikeBeatObj",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).SetUserLikeBeatObj(ctx, req.(*SetUserLikeBeatObjReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetUserLikeBeatObj_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLikeBeatObjReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetUserLikeBeatObj(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/GetUserLikeBeatObj",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetUserLikeBeatObj(ctx, req.(*GetUserLikeBeatObjReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_UserApplyMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserApplyMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).UserApplyMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/UserApplyMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).UserApplyMic(ctx, req.(*UserApplyMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetApplyMicUserList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplyMicUserListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetApplyMicUserList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/GetApplyMicUserList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetApplyMicUserList(ctx, req.(*GetApplyMicUserListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_InitDatingMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitDatingMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).InitDatingMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/InitDatingMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).InitDatingMember(ctx, req.(*InitDatingMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetVipMicUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVipMicUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetVipMicUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/GetVipMicUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetVipMicUser(ctx, req.(*GetVipMicUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetSelectLikeBeatObjUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSelectLikeBeatObjUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetSelectLikeBeatObjUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/GetSelectLikeBeatObjUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetSelectLikeBeatObjUser(ctx, req.(*GetSelectLikeBeatObjUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_ConfirmVipHoldMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmVipHoldMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).ConfirmVipHoldMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/ConfirmVipHoldMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).ConfirmVipHoldMic(ctx, req.(*ConfirmVipHoldMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetDatingGameCurInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDatingGameCurInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetDatingGameCurInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/GetDatingGameCurInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetDatingGameCurInfo(ctx, req.(*GetDatingGameCurInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetOpenLikeUserList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOpenLikeUserListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetOpenLikeUserList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/GetOpenLikeUserList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetOpenLikeUserList(ctx, req.(*GetOpenLikeUserListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_CloseDatingGameEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloseDatingGameEntryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).CloseDatingGameEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/CloseDatingGameEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).CloseDatingGameEntry(ctx, req.(*CloseDatingGameEntryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_TestDrawImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestDrawImageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).TestDrawImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/TestDrawImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).TestDrawImage(ctx, req.(*TestDrawImageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_CntPresentEvLogTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).CntPresentEvLogTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/CntPresentEvLogTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).CntPresentEvLogTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetPresentEvLogOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetPresentEvLogOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/GetPresentEvLogOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetPresentEvLogOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_FixPresentEvLogOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).FixPresentEvLogOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/FixPresentEvLogOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).FixPresentEvLogOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_AddDatingDrawCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddDatingDrawCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).AddDatingDrawCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/AddDatingDrawCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).AddDatingDrawCfg(ctx, req.(*AddDatingDrawCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_UpdateDatingDrawCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDatingDrawCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).UpdateDatingDrawCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/UpdateDatingDrawCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).UpdateDatingDrawCfg(ctx, req.(*UpdateDatingDrawCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_BatGetDatingDrawCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetDatingDrawCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).BatGetDatingDrawCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/BatGetDatingDrawCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).BatGetDatingDrawCfg(ctx, req.(*BatGetDatingDrawCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_BatGetDrawCfgBySceneId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetDrawCfgBySceneIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).BatGetDrawCfgBySceneId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/BatGetDrawCfgBySceneId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).BatGetDrawCfgBySceneId(ctx, req.(*BatGetDrawCfgBySceneIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_BatDelDrawCfgBySceneId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatDelDrawCfgBySceneIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).BatDelDrawCfgBySceneId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/BatDelDrawCfgBySceneId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).BatDelDrawCfgBySceneId(ctx, req.(*BatDelDrawCfgBySceneIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_ApplyDatingDrawCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyDatingDrawCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).ApplyDatingDrawCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/ApplyDatingDrawCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).ApplyDatingDrawCfg(ctx, req.(*ApplyDatingDrawCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_CancelApplyDrawCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelApplyDrawCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).CancelApplyDrawCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/CancelApplyDrawCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).CancelApplyDrawCfg(ctx, req.(*CancelApplyDrawCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetApplyDrawCfgState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplyDrawCfgStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetApplyDrawCfgState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/GetApplyDrawCfgState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetApplyDrawCfgState(ctx, req.(*GetApplyDrawCfgStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_EditApplyDrawCfgState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditApplyDrawCfgStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).EditApplyDrawCfgState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/EditApplyDrawCfgState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).EditApplyDrawCfgState(ctx, req.(*EditApplyDrawCfgStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_PreCheckApplyDrawCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreCheckApplyDrawCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).PreCheckApplyDrawCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/PreCheckApplyDrawCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).PreCheckApplyDrawCfg(ctx, req.(*PreCheckApplyDrawCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetAllSceneFellowInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllSceneFellowInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetAllSceneFellowInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/GetAllSceneFellowInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetAllSceneFellowInfo(ctx, req.(*GetAllSceneFellowInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetLocalApplyDrawCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLocalApplyDrawCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetLocalApplyDrawCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/GetLocalApplyDrawCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetLocalApplyDrawCfg(ctx, req.(*GetLocalApplyDrawCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_BatchGetChannelDatingGamePhase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelDatingGamePhaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).BatchGetChannelDatingGamePhase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_dating_game.ChannelDatingGame/BatchGetChannelDatingGamePhase",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).BatchGetChannelDatingGamePhase(ctx, req.(*BatchGetChannelDatingGamePhaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelDatingGame_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_dating_game.ChannelDatingGame",
	HandlerType: (*ChannelDatingGameServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "OpenDatingGameEntry",
			Handler:    _ChannelDatingGame_OpenDatingGameEntry_Handler,
		},
		{
			MethodName: "CheckDatingGameEntry",
			Handler:    _ChannelDatingGame_CheckDatingGameEntry_Handler,
		},
		{
			MethodName: "SetGamePhase",
			Handler:    _ChannelDatingGame_SetGamePhase_Handler,
		},
		{
			MethodName: "GetGamePhase",
			Handler:    _ChannelDatingGame_GetGamePhase_Handler,
		},
		{
			MethodName: "GetUserLikeBeatVal",
			Handler:    _ChannelDatingGame_GetUserLikeBeatVal_Handler,
		},
		{
			MethodName: "GetUserRankLikeBeatVal",
			Handler:    _ChannelDatingGame_GetUserRankLikeBeatVal_Handler,
		},
		{
			MethodName: "SetUserLikeBeatObj",
			Handler:    _ChannelDatingGame_SetUserLikeBeatObj_Handler,
		},
		{
			MethodName: "GetUserLikeBeatObj",
			Handler:    _ChannelDatingGame_GetUserLikeBeatObj_Handler,
		},
		{
			MethodName: "UserApplyMic",
			Handler:    _ChannelDatingGame_UserApplyMic_Handler,
		},
		{
			MethodName: "GetApplyMicUserList",
			Handler:    _ChannelDatingGame_GetApplyMicUserList_Handler,
		},
		{
			MethodName: "InitDatingMember",
			Handler:    _ChannelDatingGame_InitDatingMember_Handler,
		},
		{
			MethodName: "GetVipMicUser",
			Handler:    _ChannelDatingGame_GetVipMicUser_Handler,
		},
		{
			MethodName: "GetSelectLikeBeatObjUser",
			Handler:    _ChannelDatingGame_GetSelectLikeBeatObjUser_Handler,
		},
		{
			MethodName: "ConfirmVipHoldMic",
			Handler:    _ChannelDatingGame_ConfirmVipHoldMic_Handler,
		},
		{
			MethodName: "GetDatingGameCurInfo",
			Handler:    _ChannelDatingGame_GetDatingGameCurInfo_Handler,
		},
		{
			MethodName: "GetOpenLikeUserList",
			Handler:    _ChannelDatingGame_GetOpenLikeUserList_Handler,
		},
		{
			MethodName: "CloseDatingGameEntry",
			Handler:    _ChannelDatingGame_CloseDatingGameEntry_Handler,
		},
		{
			MethodName: "TestDrawImage",
			Handler:    _ChannelDatingGame_TestDrawImage_Handler,
		},
		{
			MethodName: "CntPresentEvLogTotalCount",
			Handler:    _ChannelDatingGame_CntPresentEvLogTotalCount_Handler,
		},
		{
			MethodName: "GetPresentEvLogOrderIds",
			Handler:    _ChannelDatingGame_GetPresentEvLogOrderIds_Handler,
		},
		{
			MethodName: "FixPresentEvLogOrder",
			Handler:    _ChannelDatingGame_FixPresentEvLogOrder_Handler,
		},
		{
			MethodName: "AddDatingDrawCfg",
			Handler:    _ChannelDatingGame_AddDatingDrawCfg_Handler,
		},
		{
			MethodName: "UpdateDatingDrawCfg",
			Handler:    _ChannelDatingGame_UpdateDatingDrawCfg_Handler,
		},
		{
			MethodName: "BatGetDatingDrawCfg",
			Handler:    _ChannelDatingGame_BatGetDatingDrawCfg_Handler,
		},
		{
			MethodName: "BatGetDrawCfgBySceneId",
			Handler:    _ChannelDatingGame_BatGetDrawCfgBySceneId_Handler,
		},
		{
			MethodName: "BatDelDrawCfgBySceneId",
			Handler:    _ChannelDatingGame_BatDelDrawCfgBySceneId_Handler,
		},
		{
			MethodName: "ApplyDatingDrawCfg",
			Handler:    _ChannelDatingGame_ApplyDatingDrawCfg_Handler,
		},
		{
			MethodName: "CancelApplyDrawCfg",
			Handler:    _ChannelDatingGame_CancelApplyDrawCfg_Handler,
		},
		{
			MethodName: "GetApplyDrawCfgState",
			Handler:    _ChannelDatingGame_GetApplyDrawCfgState_Handler,
		},
		{
			MethodName: "EditApplyDrawCfgState",
			Handler:    _ChannelDatingGame_EditApplyDrawCfgState_Handler,
		},
		{
			MethodName: "PreCheckApplyDrawCfg",
			Handler:    _ChannelDatingGame_PreCheckApplyDrawCfg_Handler,
		},
		{
			MethodName: "GetAllSceneFellowInfo",
			Handler:    _ChannelDatingGame_GetAllSceneFellowInfo_Handler,
		},
		{
			MethodName: "GetLocalApplyDrawCfg",
			Handler:    _ChannelDatingGame_GetLocalApplyDrawCfg_Handler,
		},
		{
			MethodName: "BatchGetChannelDatingGamePhase",
			Handler:    _ChannelDatingGame_BatchGetChannelDatingGamePhase_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-dating-game/channel-dating-game.proto",
}

func init() {
	proto.RegisterFile("channel-dating-game/channel-dating-game.proto", fileDescriptor_channel_dating_game_5fb1af7a1a7baa64)
}

var fileDescriptor_channel_dating_game_5fb1af7a1a7baa64 = []byte{
	// 3100 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x5a, 0x4b, 0x73, 0xe3, 0xc6,
	0x11, 0x16, 0x49, 0x3d, 0xc8, 0x96, 0x28, 0x51, 0xa3, 0x17, 0xc5, 0x7d, 0x23, 0x5e, 0xd7, 0x5a,
	0xf6, 0x6a, 0x6d, 0xd9, 0x1b, 0xbb, 0xec, 0x54, 0x2a, 0x92, 0x96, 0xe2, 0xd2, 0xd6, 0xcb, 0x20,
	0xa5, 0x54, 0x92, 0x72, 0x60, 0x10, 0x18, 0x92, 0x58, 0x81, 0x00, 0x16, 0x03, 0x4a, 0x2b, 0xe7,
	0x94, 0x73, 0x4e, 0xa9, 0x9c, 0x73, 0xcf, 0x3f, 0xc8, 0x2f, 0xc8, 0x2d, 0xa7, 0x54, 0xe5, 0x27,
	0x24, 0x95, 0x1f, 0x90, 0x5c, 0x72, 0x4a, 0xcd, 0x0c, 0x40, 0x3c, 0x38, 0xb3, 0x4b, 0x6d, 0xe2,
	0x4b, 0x4e, 0xe4, 0xf4, 0xf4, 0x74, 0xcf, 0xf4, 0x74, 0xf7, 0xf4, 0x7c, 0x03, 0x78, 0x6c, 0xf4,
	0x75, 0xc7, 0xc1, 0xf6, 0x63, 0x53, 0x0f, 0x2c, 0xa7, 0xf7, 0xb8, 0xa7, 0x0f, 0xf0, 0x13, 0x01,
	0x6d, 0xdb, 0xf3, 0xdd, 0xc0, 0x45, 0x2b, 0x61, 0x97, 0xc6, 0xbb, 0x34, 0xda, 0x55, 0xbb, 0x87,
	0x5f, 0x05, 0xd8, 0x21, 0x96, 0xeb, 0x3c, 0x71, 0xbd, 0xc0, 0x72, 0x1d, 0x12, 0xfd, 0xf2, 0x51,
	0xb5, 0x7b, 0x3e, 0x36, 0x5c, 0xc7, 0xb0, 0x6c, 0xfc, 0xf8, 0x72, 0xe7, 0x49, 0xb2, 0xc1, 0x19,
	0x94, 0x5f, 0xc0, 0xdc, 0x73, 0x3d, 0x38, 0x23, 0xd8, 0x47, 0x15, 0x28, 0x0c, 0x2d, 0xb3, 0x9a,
	0xbb, 0x9f, 0x7b, 0x54, 0x56, 0xe9, 0x5f, 0xf4, 0x63, 0x98, 0xeb, 0xeb, 0x81, 0x66, 0x74, 0x7b,
	0xd5, 0xfc, 0xfd, 0xdc, 0xa3, 0xf9, 0x9d, 0x87, 0xdb, 0x82, 0x59, 0x6c, 0x3f, 0x63, 0xff, 0x1b,
	0xfa, 0x00, 0x3f, 0xd7, 0x83, 0xfd, 0x6e, 0x4f, 0x9d, 0xed, 0xb3, 0x5f, 0xe5, 0x33, 0xd8, 0x68,
	0xe0, 0x20, 0xee, 0xde, 0x1f, 0xfa, 0x4d, 0xa7, 0xeb, 0xaa, 0xf8, 0x25, 0xba, 0x03, 0x10, 0x89,
	0x1a, 0xe9, 0x2c, 0x85, 0x94, 0xa6, 0xa9, 0xfc, 0x25, 0x0f, 0x55, 0xf1, 0x50, 0xe2, 0xa1, 0x55,
	0x98, 0xf1, 0xfa, 0x3a, 0xc1, 0xe1, 0x30, 0xde, 0x40, 0x1b, 0x30, 0x77, 0x69, 0x79, 0x1a, 0x5d,
	0x42, 0x9e, 0xd1, 0x67, 0x2f, 0x2d, 0xef, 0xcc, 0x32, 0xd1, 0x4f, 0xa0, 0x4c, 0x57, 0x31, 0x24,
	0xd8, 0xd7, 0x6c, 0x8b, 0x04, 0xd5, 0xc2, 0xfd, 0xc2, 0xa3, 0xf9, 0x9d, 0xdb, 0xc2, 0xb5, 0x84,
	0xc6, 0x50, 0xe7, 0xfb, 0xfc, 0xcf, 0xa1, 0x45, 0x02, 0xf4, 0x15, 0x2c, 0xda, 0xd6, 0x05, 0xd6,
	0x3a, 0x58, 0x0f, 0xb8, 0x88, 0x69, 0x26, 0x42, 0x6c, 0x0e, 0x3e, 0xec, 0x02, 0xef, 0x61, 0x3d,
	0x60, 0x73, 0x5e, 0xb0, 0xc3, 0x16, 0x13, 0xd6, 0x86, 0x15, 0xd7, 0xc3, 0x8e, 0xc6, 0x24, 0xc6,
	0x93, 0x9a, 0x79, 0x8d, 0xc4, 0x13, 0x0f, 0x3b, 0x54, 0x22, 0x95, 0xcc, 0x24, 0x56, 0xdc, 0x04,
	0x85, 0x49, 0x55, 0xa0, 0xac, 0x7b, 0x9e, 0x7d, 0xad, 0x0d, 0x2c, 0x43, 0xb3, 0xb1, 0x53, 0x9d,
	0x65, 0x36, 0x98, 0x67, 0xc4, 0x23, 0xcb, 0x38, 0xc4, 0x8e, 0x72, 0x04, 0xeb, 0x54, 0x52, 0x6c,
	0xd4, 0xba, 0x13, 0xf8, 0xd7, 0x6f, 0xde, 0x0d, 0x6a, 0x70, 0x1b, 0x5f, 0x62, 0x3b, 0x34, 0x2c,
	0x6f, 0x28, 0x9b, 0xb0, 0x21, 0x14, 0x47, 0x3c, 0xba, 0xf1, 0xfb, 0xb6, 0x4b, 0xf0, 0x8d, 0x55,
	0x29, 0x35, 0xa8, 0x8a, 0x47, 0x86, 0x52, 0xfb, 0xd8, 0xb8, 0xb8, 0xb9, 0xd4, 0x26, 0x54, 0xc5,
	0x23, 0x89, 0x47, 0xfd, 0xc6, 0x22, 0x1a, 0x35, 0x28, 0x1b, 0x57, 0x54, 0x67, 0x2d, 0x42, 0xd7,
	0x25, 0x59, 0xf5, 0x0b, 0x58, 0x6a, 0xe1, 0x80, 0x8a, 0x38, 0xa5, 0x6e, 0x37, 0x81, 0xf5, 0x1e,
	0xc0, 0x42, 0xa0, 0xfb, 0x3d, 0x1c, 0x68, 0xdc, 0x6b, 0xb9, 0xb8, 0x79, 0x4e, 0x63, 0x42, 0xd0,
	0x1a, 0xcc, 0xba, 0xdc, 0x75, 0x0b, 0x5c, 0x97, 0x4b, 0x3d, 0x57, 0xf9, 0x08, 0x2a, 0x69, 0x5d,
	0xc4, 0xa3, 0xca, 0xba, 0xbe, 0x3b, 0xd0, 0x92, 0x11, 0x50, 0xa2, 0x14, 0xc6, 0xa2, 0x7c, 0x08,
	0x4b, 0x8d, 0x1b, 0x4d, 0x8f, 0x2a, 0x69, 0x08, 0x94, 0x18, 0x43, 0xdf, 0x4f, 0x2b, 0xa1, 0x14,
	0xae, 0xa4, 0x09, 0x0b, 0xdc, 0x92, 0x47, 0x78, 0xd0, 0x11, 0x66, 0x8e, 0x35, 0x98, 0xa5, 0x8e,
	0x38, 0x8a, 0xc5, 0x99, 0x81, 0x65, 0x34, 0x4d, 0xca, 0x48, 0xf0, 0xab, 0x70, 0x91, 0xf4, 0xaf,
	0xf2, 0x0a, 0x56, 0x9a, 0x8e, 0x15, 0x24, 0xc5, 0x4d, 0x60, 0xd2, 0x3d, 0x98, 0x1f, 0x30, 0x5e,
	0x1e, 0x3b, 0x79, 0x16, 0x3b, 0x0f, 0x5e, 0x93, 0x9c, 0x42, 0xc9, 0xc0, 0x47, 0xd1, 0x88, 0x51,
	0xd6, 0x61, 0x75, 0x5c, 0x33, 0xf1, 0x94, 0xaf, 0x61, 0xad, 0x81, 0x83, 0x64, 0x10, 0x9f, 0xeb,
	0xf6, 0x04, 0x73, 0xda, 0x84, 0xe2, 0xd0, 0x32, 0xe3, 0x09, 0x95, 0xd5, 0xb9, 0xa1, 0x65, 0x32,
	0x55, 0x0e, 0xac, 0x8b, 0x44, 0x12, 0x8f, 0x26, 0x83, 0x38, 0xb3, 0x58, 0x4e, 0xd7, 0x4d, 0x2e,
	0x68, 0xc2, 0xf4, 0x52, 0xb1, 0x13, 0x2d, 0xa6, 0xef, 0x73, 0xd8, 0x0c, 0xf5, 0xa9, 0xba, 0x73,
	0x71, 0xa3, 0x65, 0x28, 0x3e, 0xd4, 0x64, 0x63, 0xbf, 0xb7, 0xf9, 0x0e, 0xa0, 0x92, 0xe5, 0x12,
	0xf8, 0x94, 0x02, 0xe5, 0x58, 0xf7, 0xa5, 0x1e, 0xc5, 0xe5, 0xbc, 0x1d, 0xcf, 0x11, 0xfd, 0x00,
	0xca, 0x04, 0xdb, 0xd8, 0x08, 0x34, 0x12, 0xe8, 0xc1, 0x90, 0x30, 0x57, 0x2b, 0xaa, 0x0b, 0x9c,
	0xd8, 0x62, 0x34, 0xc5, 0x83, 0xb5, 0x56, 0x7a, 0x3b, 0x4e, 0x3a, 0x2f, 0x26, 0xd8, 0xe1, 0x3b,
	0x00, 0xa1, 0xf0, 0xf8, 0x90, 0x29, 0x71, 0x0a, 0x3d, 0x67, 0x36, 0xa1, 0xc8, 0x73, 0xfa, 0x28,
	0x8c, 0xe7, 0x68, 0x9b, 0x06, 0x72, 0x15, 0xd6, 0x45, 0x1a, 0x85, 0xde, 0x36, 0xd9, 0x5c, 0x36,
	0xa1, 0xc8, 0x4e, 0x91, 0x78, 0x26, 0x73, 0xb4, 0x4d, 0x95, 0x7d, 0x3c, 0xe6, 0x6d, 0xa1, 0xb2,
	0xd4, 0x0c, 0x73, 0xe9, 0x19, 0x1e, 0xc3, 0xad, 0x06, 0x0e, 0x5a, 0x6c, 0x31, 0x89, 0x61, 0xcc,
	0x0f, 0xde, 0x3c, 0x9b, 0x70, 0xb3, 0x0a, 0xa3, 0xcd, 0x52, 0x0e, 0xe0, 0xb6, 0x5c, 0x1e, 0xf1,
	0xd0, 0xbb, 0xb0, 0x14, 0xdb, 0x92, 0x3b, 0x51, 0x8e, 0x05, 0x4d, 0x79, 0x64, 0x50, 0xe6, 0x1a,
	0xbf, 0xc9, 0xc1, 0xf2, 0x91, 0x1e, 0x18, 0xfd, 0x94, 0x73, 0xac, 0xc0, 0x0c, 0x1d, 0xa6, 0x87,
	0x33, 0x99, 0x1e, 0x5a, 0xe6, 0x6e, 0x44, 0xec, 0x84, 0xf6, 0xa0, 0xc4, 0x3d, 0xf4, 0x10, 0x96,
	0x52, 0x4e, 0xa3, 0xe9, 0xe1, 0x2c, 0x17, 0x12, 0x6e, 0xb3, 0x3b, 0xce, 0xd6, 0xa9, 0x4e, 0x8f,
	0xb1, 0xed, 0x29, 0x4d, 0x66, 0xda, 0x93, 0xcc, 0xe1, 0x3b, 0xb9, 0x81, 0xf2, 0xb1, 0x81, 0xba,
	0xac, 0x36, 0x1a, 0x17, 0x45, 0x3c, 0x5a, 0x6e, 0xb0, 0xbd, 0x8d, 0xe3, 0x2b, 0x77, 0x93, 0xe2,
	0x60, 0x81, 0x0e, 0x1e, 0xc5, 0xd6, 0x73, 0xa8, 0x64, 0x39, 0x52, 0xce, 0x93, 0x4b, 0x39, 0x4f,
	0xca, 0x45, 0xf2, 0x69, 0x17, 0xd1, 0x60, 0x89, 0x4a, 0xd8, 0x0d, 0x2b, 0x8a, 0xb7, 0x59, 0x35,
	0xba, 0x05, 0x25, 0x8b, 0x68, 0x86, 0xee, 0x18, 0xd8, 0x0e, 0x63, 0xb3, 0x68, 0x91, 0x7d, 0xd6,
	0x56, 0x10, 0x4f, 0x03, 0xb1, 0x02, 0xe2, 0x85, 0x16, 0x8f, 0x48, 0xff, 0x95, 0xc5, 0x3f, 0x61,
	0x16, 0x1f, 0x17, 0xc5, 0x03, 0x43, 0x96, 0xbb, 0x7f, 0x9f, 0x83, 0x4a, 0xb6, 0xc0, 0xa5, 0xc7,
	0x1b, 0x2d, 0x29, 0x47, 0x7a, 0x67, 0xfa, 0x7a, 0x10, 0xea, 0xf4, 0x79, 0x5e, 0x2a, 0xa9, 0xf4,
	0x2f, 0xa5, 0x0c, 0xcc, 0xa7, 0x6c, 0xa5, 0x25, 0x95, 0xfe, 0x45, 0xf7, 0x60, 0x3e, 0xe8, 0x60,
	0x9d, 0xd6, 0x7f, 0x03, 0x2b, 0x08, 0xbd, 0x0c, 0x18, 0xe9, 0x90, 0x52, 0xc2, 0x7a, 0x64, 0xa0,
	0xdb, 0xb8, 0x3a, 0x13, 0xd5, 0x23, 0x47, 0xba, 0x8d, 0xe3, 0x7a, 0x64, 0x36, 0x59, 0x8f, 0xf0,
	0xe3, 0xfb, 0xdc, 0xf2, 0xc2, 0x35, 0x4d, 0x90, 0xe2, 0x3f, 0x80, 0xe5, 0xcc, 0x10, 0x5e, 0x06,
	0x45, 0xe5, 0x73, 0x2e, 0x59, 0x3e, 0x2b, 0x0d, 0x58, 0xdd, 0x77, 0x9d, 0xae, 0xe5, 0x0f, 0xce,
	0x2d, 0xef, 0xb9, 0x6b, 0x9b, 0xe1, 0xde, 0x8f, 0x27, 0xe8, 0xb4, 0xda, 0x7c, 0x56, 0xed, 0xa7,
	0xb0, 0x26, 0x10, 0x44, 0x3c, 0x74, 0x17, 0xe6, 0x3d, 0x1f, 0x6b, 0x69, 0xf5, 0x25, 0xcf, 0xc7,
	0xe7, 0x7c, 0x06, 0xff, 0xca, 0x41, 0xa5, 0x8d, 0x49, 0xf0, 0xcc, 0xd7, 0xaf, 0x9a, 0x03, 0xbd,
	0x87, 0x43, 0xf5, 0x46, 0xac, 0xde, 0xb0, 0xcc, 0x38, 0x29, 0xe4, 0x45, 0x49, 0xa1, 0x90, 0x48,
	0x0a, 0x4b, 0x50, 0xb8, 0xd4, 0xf4, 0xd0, 0xf6, 0xf9, 0xcb, 0x5d, 0x4e, 0xe8, 0x30, 0x7b, 0x53,
	0xc2, 0x1e, 0xad, 0xd9, 0xbc, 0x21, 0xe9, 0x6b, 0xe1, 0xec, 0x99, 0xc9, 0x8b, 0xea, 0x3c, 0xa5,
	0xed, 0x73, 0x12, 0x75, 0x65, 0x62, 0x60, 0xc7, 0xc0, 0x74, 0xb1, 0x73, 0x6c, 0x64, 0x91, 0x13,
	0x9a, 0x26, 0x7a, 0x07, 0x16, 0x87, 0x04, 0x6b, 0x24, 0x70, 0x7d, 0xac, 0x19, 0xae, 0xd3, 0xad,
	0x16, 0xf9, 0x41, 0x34, 0x24, 0xb8, 0x45, 0x89, 0xd4, 0x12, 0xd4, 0x60, 0x81, 0x6f, 0xf5, 0x7a,
	0xd8, 0xd7, 0x02, 0x52, 0x2d, 0xdd, 0xcf, 0x3d, 0x2a, 0xa8, 0xa5, 0x90, 0xd2, 0x26, 0xca, 0x0a,
	0x2c, 0x67, 0x96, 0x4d, 0x3c, 0xe5, 0x6f, 0xd3, 0xb0, 0x1c, 0xfb, 0x23, 0xed, 0xa3, 0x0e, 0xb9,
	0x09, 0x4c, 0x37, 0x8e, 0xf7, 0x7b, 0x8e, 0xb5, 0x9b, 0x26, 0x42, 0x30, 0xed, 0xe8, 0x03, 0x1c,
	0x7a, 0x25, 0xfb, 0x9f, 0x75, 0xc2, 0xc2, 0x98, 0x13, 0x3e, 0x84, 0xc5, 0x8e, 0x6e, 0x5c, 0xf4,
	0x7c, 0x77, 0xe8, 0x98, 0x9a, 0x67, 0x19, 0xcc, 0x58, 0x25, 0xb5, 0x1c, 0x53, 0x4f, 0x2d, 0x03,
	0xbd, 0x07, 0x95, 0x2e, 0xb6, 0x6d, 0xf7, 0x4a, 0xa3, 0x62, 0x3d, 0x5b, 0x0f, 0xb8, 0xd3, 0x96,
	0xd4, 0x25, 0x4e, 0x3f, 0x8e, 0xc8, 0x68, 0x07, 0xd6, 0xb2, 0xac, 0xda, 0xd0, 0xb1, 0x5e, 0x32,
	0xd3, 0x96, 0xd4, 0x95, 0x0c, 0xff, 0x99, 0x63, 0xbd, 0x44, 0x5b, 0xb0, 0x6c, 0xf4, 0xb5, 0xcc,
	0x44, 0xe6, 0xb8, 0x7c, 0xa3, 0xbf, 0x97, 0x9a, 0xca, 0x16, 0x2c, 0x5b, 0x83, 0x2c, 0x6f, 0x91,
	0xf3, 0x5a, 0x83, 0x34, 0xef, 0x1d, 0x00, 0xdc, 0xed, 0xb2, 0xc3, 0xc7, 0xb7, 0x99, 0xdd, 0x4b,
	0x6a, 0x89, 0x53, 0xce, 0x7c, 0x9b, 0x6e, 0x5e, 0xdc, 0xad, 0xd1, 0xf8, 0x05, 0xc6, 0xb2, 0x30,
	0x62, 0x39, 0x32, 0x9f, 0x52, 0x17, 0xe9, 0xf8, 0x58, 0xbf, 0x70, 0xf0, 0x15, 0xa1, 0x66, 0x9f,
	0xe7, 0xd5, 0xc8, 0x88, 0xd6, 0x34, 0xd1, 0x7d, 0x58, 0xe8, 0x5f, 0x69, 0x96, 0xa9, 0x75, 0x31,
	0x8b, 0xe7, 0x05, 0x6e, 0xe7, 0xfe, 0x55, 0xd3, 0x3c, 0x60, 0x14, 0x74, 0x1b, 0x80, 0x73, 0xb0,
	0xfe, 0x32, 0xf7, 0x22, 0xda, 0xcf, 0x22, 0xfe, 0x43, 0x58, 0x8d, 0x16, 0x64, 0x6a, 0x9e, 0xee,
	0xeb, 0x03, 0xed, 0x05, 0x71, 0x9d, 0xea, 0x22, 0x9b, 0x0e, 0x1a, 0xf5, 0x9d, 0xd2, 0xae, 0x2f,
	0x89, 0xeb, 0xa0, 0x75, 0x98, 0xf5, 0xf1, 0x40, 0xf7, 0x2f, 0xaa, 0x4b, 0x8c, 0x27, 0x6c, 0x51,
	0xff, 0xe8, 0xe0, 0x9e, 0xe5, 0x50, 0x3f, 0xab, 0x30, 0x3f, 0x9b, 0x63, 0xed, 0x36, 0xa1, 0xb9,
	0x0c, 0x3b, 0x26, 0xed, 0x58, 0x66, 0x1d, 0x33, 0xd8, 0x31, 0xdb, 0x44, 0x39, 0x81, 0x95, 0x5d,
	0xd3, 0xe4, 0x9e, 0x16, 0x7a, 0x19, 0x0d, 0xbb, 0xcf, 0xa0, 0x60, 0x74, 0x7b, 0xcc, 0xc7, 0xe6,
	0x77, 0xde, 0x7d, 0x03, 0x1c, 0x10, 0x8d, 0xa3, 0x43, 0x68, 0xbd, 0x3d, 0x2e, 0x90, 0x78, 0x8a,
	0x0a, 0xeb, 0x67, 0x9e, 0xa9, 0x07, 0xf8, 0x7f, 0xa8, 0x6b, 0x13, 0x36, 0x84, 0x32, 0x89, 0xa7,
	0xfc, 0x21, 0x07, 0xeb, 0x7b, 0x7a, 0x30, 0x02, 0x17, 0x12, 0xfa, 0x10, 0x4c, 0x7b, 0x7a, 0x2f,
	0xba, 0xef, 0xb0, 0xff, 0x34, 0xca, 0xe9, 0xaf, 0x46, 0xac, 0xef, 0xa2, 0x9b, 0x5b, 0x91, 0x12,
	0x5a, 0xd6, 0x77, 0x38, 0x15, 0x75, 0x05, 0x71, 0xd4, 0x4d, 0x27, 0xa2, 0x6e, 0x03, 0xe6, 0x88,
	0xeb, 0x07, 0x5a, 0xe7, 0x3a, 0xcc, 0x34, 0xb3, 0xb4, 0xb9, 0x77, 0x4d, 0xb7, 0xc0, 0x22, 0x9a,
	0x4e, 0x8c, 0x30, 0xcf, 0xcc, 0x58, 0x64, 0x97, 0x18, 0x4a, 0x00, 0x1b, 0xc2, 0x99, 0x12, 0x0f,
	0xed, 0x43, 0xc9, 0xf4, 0xf5, 0xab, 0x64, 0x75, 0x30, 0xa9, 0x81, 0x8a, 0x74, 0x20, 0xc3, 0x0c,
	0x56, 0x61, 0x26, 0x70, 0x83, 0x51, 0x21, 0xcd, 0x1b, 0x4a, 0x0b, 0x36, 0x43, 0xad, 0x7c, 0xc0,
	0xde, 0x75, 0x8b, 0xaf, 0x89, 0x9a, 0x48, 0x81, 0x72, 0xb4, 0xe2, 0x64, 0xd1, 0x36, 0x1f, 0x2e,
	0x9b, 0x89, 0x15, 0x24, 0x1c, 0xe5, 0x1c, 0x6a, 0x32, 0xa1, 0xc4, 0x8b, 0x37, 0xba, 0x70, 0xd3,
	0x8d, 0xfe, 0x7b, 0x0e, 0xd6, 0xd8, 0x89, 0x3e, 0xb6, 0x99, 0xe9, 0x8c, 0x58, 0x48, 0xee, 0x4d,
	0x1d, 0x96, 0x02, 0x6b, 0x80, 0x79, 0xf2, 0xd3, 0x82, 0x6b, 0x8f, 0xcf, 0x75, 0x71, 0xe7, 0x8e,
	0x50, 0x75, 0xdb, 0x1a, 0xe0, 0xf6, 0xb5, 0x87, 0xd5, 0x32, 0x1d, 0xc5, 0xf2, 0x23, 0x6d, 0xa6,
	0x62, 0xaa, 0x20, 0x8b, 0xa9, 0xe9, 0x44, 0x4c, 0xa1, 0x07, 0x50, 0xa6, 0xa7, 0x82, 0xe5, 0x68,
	0xba, 0x6d, 0x6b, 0xf6, 0x65, 0x78, 0xc0, 0xc3, 0x90, 0xe0, 0xa6, 0xb3, 0x6b, 0xdb, 0x87, 0x97,
	0x89, 0x00, 0x9e, 0x4d, 0x06, 0x30, 0xbd, 0x41, 0x88, 0xd6, 0x49, 0x3c, 0xe5, 0x1f, 0x39, 0xd8,
	0x38, 0xf5, 0x31, 0xc3, 0x37, 0x38, 0xcb, 0xff, 0xad, 0x11, 0x76, 0xa0, 0x2a, 0x5e, 0x29, 0xf1,
	0xe8, 0x18, 0xc7, 0x0d, 0xac, 0xee, 0x35, 0x0b, 0xdf, 0x92, 0x1a, 0xb6, 0x94, 0x2f, 0x61, 0x8d,
	0x97, 0x97, 0x02, 0xdb, 0x70, 0xc4, 0x2c, 0xb6, 0x0d, 0x6b, 0x37, 0xcd, 0x84, 0xfe, 0x7c, 0x76,
	0x13, 0x44, 0xb2, 0x88, 0xa7, 0xfc, 0x33, 0x0f, 0x65, 0xda, 0x66, 0x1d, 0x51, 0x8d, 0x9d, 0x10,
	0x9f, 0x4b, 0x8a, 0x4f, 0xee, 0x4a, 0x3e, 0x9d, 0x36, 0xbe, 0x17, 0x73, 0xde, 0x82, 0xd2, 0x90,
	0x65, 0x43, 0x3a, 0x78, 0x96, 0x0d, 0x2e, 0x72, 0x42, 0x9b, 0xa0, 0x2a, 0xcc, 0xf1, 0xd5, 0x91,
	0xf0, 0x64, 0x8d, 0x9a, 0xec, 0xba, 0xcb, 0xa6, 0xc9, 0xa2, 0x99, 0x1f, 0xa5, 0xac, 0xe4, 0xc1,
	0xf4, 0x94, 0x16, 0x94, 0x08, 0x25, 0x51, 0x89, 0x20, 0xf0, 0x33, 0x78, 0x0b, 0x3f, 0xa3, 0xb9,
	0x8a, 0x96, 0x27, 0xe1, 0x31, 0xcb, 0x1b, 0xca, 0x79, 0x5c, 0xd2, 0x87, 0xbb, 0x41, 0xaf, 0xf8,
	0xac, 0x3e, 0xfc, 0x02, 0x4a, 0x3d, 0xcc, 0x61, 0x00, 0x9e, 0xd1, 0x17, 0x77, 0xee, 0x8a, 0x33,
	0x8b, 0xaf, 0x5f, 0xf1, 0x51, 0xc5, 0x1e, 0x66, 0x10, 0x01, 0x56, 0xbe, 0x61, 0xe8, 0xb3, 0x40,
	0x2e, 0xf1, 0xd0, 0x2e, 0x00, 0xdf, 0xd8, 0x44, 0xee, 0x55, 0xa4, 0x92, 0x47, 0x0e, 0xa1, 0x96,
	0xd8, 0x28, 0x76, 0xa7, 0x38, 0x85, 0x6a, 0xdd, 0xb4, 0xc4, 0xf3, 0xfe, 0x24, 0x79, 0xe8, 0x4d,
	0x22, 0x97, 0xe5, 0xc1, 0x5b, 0xb0, 0x29, 0x91, 0x48, 0x3c, 0xe5, 0xaf, 0x39, 0x58, 0x62, 0xe9,
	0xf6, 0x80, 0xd5, 0x58, 0x91, 0x7b, 0xca, 0x0a, 0xc6, 0xf4, 0xbe, 0xe7, 0xb3, 0xfb, 0x7e, 0x1b,
	0x4a, 0x71, 0xb1, 0xc7, 0x2f, 0x36, 0x31, 0x81, 0x56, 0x84, 0xe9, 0xfa, 0x6e, 0x18, 0x9d, 0x81,
	0x4b, 0x4e, 0xb2, 0xb6, 0x1b, 0x62, 0x74, 0x17, 0x20, 0x76, 0x95, 0xb0, 0x6c, 0x4c, 0x50, 0x68,
	0x91, 0xca, 0xe7, 0x41, 0x0c, 0xd7, 0xc7, 0xe1, 0xad, 0x87, 0x4f, 0xad, 0x45, 0x29, 0x4a, 0x8d,
	0xef, 0x92, 0x6d, 0x67, 0x16, 0xa7, 0xe2, 0x97, 0xca, 0xb7, 0x0c, 0x02, 0x13, 0xf5, 0xb1, 0xd3,
	0x33, 0x5c, 0x61, 0x62, 0x0b, 0xdf, 0x11, 0x9a, 0x3a, 0x3b, 0x9a, 0xdb, 0x81, 0x6d, 0xe2, 0x26,
	0xf3, 0xbd, 0x43, 0xd7, 0xd0, 0xb3, 0xa9, 0x45, 0xd1, 0xd9, 0xc4, 0x04, 0x5d, 0xc4, 0x43, 0x75,
	0x81, 0xfb, 0x4c, 0x7a, 0xe4, 0x25, 0x5c, 0xe8, 0x97, 0xec, 0x94, 0x7e, 0x86, 0xed, 0xb7, 0x3d,
	0xa5, 0x19, 0x98, 0x65, 0xf8, 0x38, 0xd0, 0x2e, 0xf0, 0xf5, 0x68, 0x97, 0x19, 0xe5, 0x2b, 0x7c,
	0xad, 0xdc, 0x66, 0x07, 0xb6, 0x50, 0x3e, 0xf1, 0x94, 0x3d, 0x78, 0xb0, 0xa7, 0x07, 0x46, 0xbf,
	0x81, 0x83, 0xf0, 0x3a, 0x14, 0x4f, 0x56, 0x8a, 0x3b, 0x17, 0xd2, 0xd7, 0xc1, 0x3f, 0xe5, 0x40,
	0x79, 0x93, 0x10, 0xe2, 0xa1, 0x0e, 0x94, 0x18, 0x0a, 0xad, 0x0d, 0x74, 0x2f, 0x34, 0x57, 0x5d,
	0x68, 0xae, 0x37, 0xcb, 0xda, 0x66, 0xff, 0x8e, 0x74, 0x8f, 0x83, 0xff, 0x45, 0x2f, 0x6c, 0xd6,
	0xbe, 0x80, 0x72, 0xaa, 0x8b, 0x5e, 0x2e, 0xa9, 0x55, 0xc2, 0xcb, 0xe5, 0x05, 0xbe, 0xa6, 0xf9,
	0xe7, 0x52, 0xb7, 0x87, 0x51, 0x0d, 0xc8, 0x1b, 0x9f, 0xe7, 0x3f, 0xcb, 0x6d, 0xfd, 0x08, 0xa0,
	0xc5, 0xca, 0xb8, 0xf0, 0x3c, 0x5c, 0x6b, 0x9d, 0xa8, 0x6d, 0xad, 0xfd, 0xb3, 0xd3, 0xba, 0x76,
	0x76, 0xdc, 0x3a, 0xad, 0xef, 0x37, 0x0f, 0x9a, 0xf5, 0x67, 0x95, 0x29, 0xb4, 0x02, 0x4b, 0x71,
	0x57, 0x7b, 0xaf, 0xbe, 0x7b, 0x5c, 0xc9, 0x6d, 0xbd, 0x80, 0x62, 0x94, 0xf2, 0xd0, 0x3d, 0xb8,
	0xd5, 0x6e, 0x1e, 0xd5, 0xb5, 0xc3, 0xe6, 0x51, 0x53, 0x28, 0xe1, 0x0e, 0x6c, 0x66, 0x19, 0x0e,
	0x4f, 0x8e, 0x1b, 0x5a, 0xbb, 0xae, 0x1e, 0x55, 0x72, 0xe8, 0x2e, 0xd4, 0xb2, 0xdd, 0x71, 0xbb,
	0x92, 0xdf, 0x7a, 0x01, 0xa5, 0x51, 0xb2, 0x43, 0x35, 0x58, 0x7f, 0xa6, 0xee, 0xfe, 0x54, 0x6b,
	0xb5, 0x77, 0xdb, 0x59, 0x3d, 0x6b, 0xb0, 0x9c, 0xe8, 0x6b, 0x1e, 0x6b, 0x67, 0xad, 0x7a, 0x25,
	0x87, 0xd6, 0x01, 0x25, 0xc8, 0xa7, 0xf5, 0xe3, 0x67, 0xcd, 0xe3, 0x46, 0x25, 0x8f, 0x10, 0x2c,
	0x26, 0xe8, 0x27, 0x07, 0x07, 0x95, 0xc2, 0xce, 0x6f, 0xef, 0xc2, 0xf2, 0xd8, 0x4e, 0x20, 0x1f,
	0x56, 0x04, 0x4f, 0x46, 0xe8, 0x7d, 0x29, 0xb0, 0x35, 0xfe, 0xd4, 0x53, 0xfb, 0x60, 0x72, 0x66,
	0xe2, 0x29, 0x53, 0x68, 0x08, 0xab, 0xa2, 0xb7, 0x1f, 0x24, 0x96, 0x23, 0x79, 0x60, 0xaa, 0x3d,
	0xbe, 0x01, 0x37, 0x53, 0xfb, 0x0d, 0x2c, 0x24, 0xdf, 0x6e, 0x90, 0x24, 0xbf, 0xa4, 0xdf, 0x6a,
	0x6a, 0x0f, 0x27, 0xe0, 0x8a, 0xc4, 0x37, 0xde, 0x2c, 0xbe, 0x31, 0x91, 0xf8, 0xc6, 0xb8, 0x78,
	0x17, 0xd0, 0xf8, 0x8b, 0x05, 0xda, 0x92, 0x0d, 0x1f, 0x7f, 0x2d, 0xa9, 0xbd, 0x3f, 0x31, 0x2f,
	0x53, 0xf8, 0xab, 0x11, 0x68, 0x9d, 0x79, 0x76, 0x40, 0xdb, 0xaf, 0x13, 0x34, 0xfe, 0xbe, 0x51,
	0x7b, 0x72, 0x23, 0xfe, 0x68, 0xb5, 0xe3, 0xf0, 0xbc, 0x64, 0xb5, 0xc2, 0x97, 0x03, 0xc9, 0x6a,
	0x25, 0x98, 0xbf, 0xc8, 0xbc, 0x72, 0x85, 0x8d, 0x1b, 0x28, 0x6c, 0xc8, 0x14, 0x7e, 0x03, 0x0b,
	0x49, 0x68, 0x55, 0xe2, 0x2e, 0x19, 0x78, 0xb7, 0xf6, 0x70, 0x02, 0x2e, 0x26, 0xde, 0x87, 0x15,
	0x01, 0xb4, 0x8a, 0xa4, 0x93, 0x14, 0xe0, 0xb9, 0x92, 0xb8, 0x96, 0x20, 0xb6, 0xca, 0x14, 0xb2,
	0xa0, 0x92, 0x7d, 0xbf, 0x43, 0x8f, 0x84, 0x32, 0x04, 0x0f, 0x8c, 0xb5, 0xf7, 0x26, 0xe4, 0x64,
	0xaa, 0xbe, 0x85, 0x72, 0x0a, 0x30, 0x45, 0xd2, 0x38, 0x4a, 0xe1, 0xb0, 0xb5, 0x77, 0x27, 0x61,
	0x63, 0x1a, 0x7e, 0x9d, 0x63, 0x25, 0x83, 0xf0, 0xbd, 0x04, 0x7d, 0x28, 0x13, 0x23, 0x7b, 0xae,
	0xa9, 0x7d, 0x74, 0xc3, 0x11, 0x6c, 0x0e, 0x36, 0x2c, 0x8f, 0xe1, 0xb3, 0x48, 0x6c, 0x27, 0x11,
	0x20, 0x5c, 0xdb, 0x9a, 0x94, 0x35, 0x4a, 0xcb, 0xa2, 0x0f, 0x3c, 0x90, 0xd4, 0x0d, 0x44, 0x9f,
	0x91, 0x48, 0xd2, 0xb2, 0xec, 0xcb, 0x91, 0x91, 0xa7, 0x66, 0x9f, 0x5d, 0xe4, 0x9e, 0x2a, 0x78,
	0xeb, 0x91, 0x7b, 0xaa, 0xe8, 0x35, 0x27, 0x3c, 0x81, 0x04, 0xdf, 0x34, 0xc8, 0x4e, 0x20, 0xf1,
	0x87, 0x13, 0xb2, 0x13, 0x48, 0xf6, 0xb1, 0x04, 0xf3, 0xda, 0x14, 0x7c, 0x2c, 0xf1, 0xda, 0x2c,
	0xb2, 0x2e, 0xf1, 0xda, 0x71, 0x24, 0x7a, 0x0a, 0x9d, 0xc2, 0xe6, 0xbe, 0x13, 0x9c, 0xfa, 0x98,
	0x60, 0x27, 0xa8, 0x5f, 0x1e, 0xba, 0xbd, 0xb6, 0x1b, 0xe8, 0xf6, 0xbe, 0x3b, 0x74, 0x02, 0xb4,
	0xb9, 0xad, 0x46, 0x9f, 0x1b, 0x9d, 0xef, 0xb0, 0x7b, 0x9d, 0xaa, 0x3b, 0x5c, 0xc3, 0x7a, 0xaa,
	0x8b, 0xb1, 0x87, 0x12, 0xbf, 0x66, 0x45, 0x75, 0x52, 0xe2, 0x89, 0x6f, 0x62, 0xbf, 0x69, 0x92,
	0xd7, 0xc9, 0x4b, 0x77, 0x45, 0x23, 0x42, 0x91, 0xc7, 0xb0, 0x7a, 0x60, 0xbd, 0x1a, 0x13, 0x89,
	0x6e, 0xa7, 0x06, 0xa9, 0xf4, 0x6a, 0x62, 0x60, 0xd6, 0x35, 0x3e, 0xc5, 0xfa, 0xc0, 0x0b, 0xae,
	0xe3, 0xbc, 0x93, 0xc5, 0x31, 0x25, 0x79, 0x47, 0x80, 0x9f, 0x4a, 0xf2, 0x8e, 0x10, 0x18, 0x65,
	0xce, 0x2a, 0x80, 0x31, 0x25, 0xce, 0x2a, 0x06, 0x51, 0x25, 0xce, 0x2a, 0x43, 0x47, 0x99, 0x4e,
	0x01, 0xe8, 0x28, 0xd1, 0x29, 0x06, 0x52, 0x25, 0x3a, 0x25, 0x58, 0x26, 0x3f, 0xfc, 0xc5, 0xe8,
	0xa0, 0xe4, 0xf0, 0x97, 0xe2, 0x93, 0x92, 0xc3, 0x5f, 0x0e, 0x3d, 0x8e, 0x94, 0x0b, 0x6e, 0x3a,
	0x72, 0xe5, 0xe2, 0x6b, 0x97, 0x5c, 0xb9, 0xec, 0x1a, 0xc5, 0x0a, 0x81, 0x71, 0x58, 0x4f, 0x52,
	0x08, 0x08, 0x71, 0x4e, 0x49, 0x21, 0x20, 0xc1, 0x0a, 0x99, 0xc2, 0x71, 0x08, 0x4b, 0xa2, 0x50,
	0x88, 0x9b, 0x49, 0x14, 0x4a, 0x70, 0xb1, 0x28, 0xcf, 0x8f, 0x01, 0x13, 0xe8, 0xf5, 0xc7, 0x7d,
	0x06, 0x15, 0x91, 0xe7, 0x79, 0x31, 0xe2, 0x31, 0x85, 0x5e, 0xc1, 0x9a, 0x10, 0x10, 0x41, 0x62,
	0x49, 0x32, 0x38, 0xa6, 0xb6, 0x7d, 0x13, 0xf6, 0x68, 0xc1, 0x22, 0x90, 0x52, 0xb2, 0x60, 0x09,
	0x72, 0x2b, 0x59, 0xb0, 0x0c, 0xfd, 0xe4, 0x0b, 0x16, 0x02, 0x1e, 0x48, 0x6e, 0x3a, 0x11, 0x70,
	0x52, 0xdb, 0xbe, 0x09, 0x7b, 0x62, 0x87, 0xc7, 0xd0, 0x0e, 0xf9, 0x0e, 0x8b, 0x30, 0x13, 0xf9,
	0x0e, 0x0b, 0x61, 0x14, 0x65, 0x0a, 0xfd, 0x2e, 0x07, 0x77, 0x5f, 0x7f, 0xe7, 0x47, 0x3f, 0x7c,
	0x2b, 0xa0, 0xe0, 0x65, 0xed, 0xd3, 0xb7, 0x04, 0x18, 0x94, 0xa9, 0x5a, 0xed, 0xdf, 0x7f, 0xfc,
	0x73, 0x7b, 0x0d, 0x56, 0x04, 0x1f, 0xf2, 0xee, 0x7d, 0xfa, 0xf3, 0xa7, 0x3d, 0xd7, 0xd6, 0x9d,
	0xde, 0xf6, 0xd3, 0x9d, 0x20, 0xd8, 0x36, 0xdc, 0xc1, 0x13, 0xf6, 0x11, 0xae, 0xe1, 0xda, 0x4f,
	0x08, 0xf6, 0x2f, 0x2d, 0x03, 0x13, 0xd1, 0x17, 0xc0, 0x9d, 0x59, 0xc6, 0xf6, 0xf1, 0x7f, 0x02,
	0x00, 0x00, 0xff, 0xff, 0xfa, 0x55, 0x43, 0xee, 0x33, 0x2c, 0x00, 0x00,
}
