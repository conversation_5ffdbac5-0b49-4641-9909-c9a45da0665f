// Code generated by protoc-gen-gogo.
// source: src/appconfig/appconfigsvr.proto
// DO NOT EDIT!

/*
	Package appconfig is a generated protocol buffer package.

	It is generated from these files:
		src/appconfig/appconfigsvr.proto

	It has these top-level messages:
		GamePageAdvert
		SetGamePageTopLableReq
		GetGamePageTopLableResp
		SetGamePageEnterReq
		GetGamePageEnterResp
		SetGamePageAdvertReq
		GetGamePageAdvertResp
		SetGamePageGrayUidsReq
		GetGamePageGrayUidsReq
		GetGamePageGrayUidsResp
		GetUpdateTimeReq
		GetUpdateTimeResp
		RaidRichesConfig
		CustomEntryInfo
		CustomEntryList
		GameTabAdEntry
		GameTabAdList
		ChannelBackground
		ChannelBackgroundList
		RushInfo
		FloatLayerEntry
		FloatLayerList
		MsgPageAdvertEntry
		MsgPageAdvertList
		GetAppConfigReq
		GetAppConfigDetailResp
		SetAppConfigDetailReq
		CommonEffectSource
		CommonEffectSourceList
		ClientAuditInfo
		SetIosAuditInfoReq
		GetIosAuditInfoResp
		ConfigKV
		ConfigData
		MainPageTagConfig
		ConfigInfo
		ConfigRangeInfoList
		GetSyncConfigV2AllVersionReq
		GetSyncConfigV2AllVersionResp
		GetSyncConfigV2InfoByKeyListReq
		GetSyncConfigV2InfoByKeyListResp
		StrReq
		CodeResp
		GetRankBlackUidListReq
		GetRankBlackUidListResp
*/
package appconfig

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type AdvertType int32

const (
	AdvertType_Unknow               AdvertType = 0
	AdvertType_Top_Lable            AdvertType = 1
	AdvertType_Advert               AdvertType = 2
	AdvertType_Fun_Enter            AdvertType = 3
	AdvertType_Raid_Riches          AdvertType = 4
	AdvertType_Custom_Entry         AdvertType = 5
	AdvertType_Game_Tab_Ad          AdvertType = 6
	AdvertType_Float_Layer          AdvertType = 7
	AdvertType_Msg_Page_Advert      AdvertType = 8
	AdvertType_Common_Effect_Source AdvertType = 9
	AdvertType_Channel_Background   AdvertType = 10
)

var AdvertType_name = map[int32]string{
	0:  "Unknow",
	1:  "Top_Lable",
	2:  "Advert",
	3:  "Fun_Enter",
	4:  "Raid_Riches",
	5:  "Custom_Entry",
	6:  "Game_Tab_Ad",
	7:  "Float_Layer",
	8:  "Msg_Page_Advert",
	9:  "Common_Effect_Source",
	10: "Channel_Background",
}
var AdvertType_value = map[string]int32{
	"Unknow":               0,
	"Top_Lable":            1,
	"Advert":               2,
	"Fun_Enter":            3,
	"Raid_Riches":          4,
	"Custom_Entry":         5,
	"Game_Tab_Ad":          6,
	"Float_Layer":          7,
	"Msg_Page_Advert":      8,
	"Common_Effect_Source": 9,
	"Channel_Background":   10,
}

func (x AdvertType) Enum() *AdvertType {
	p := new(AdvertType)
	*p = x
	return p
}
func (x AdvertType) String() string {
	return proto.EnumName(AdvertType_name, int32(x))
}
func (x *AdvertType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(AdvertType_value, data, "AdvertType")
	if err != nil {
		return err
	}
	*x = AdvertType(value)
	return nil
}
func (AdvertType) EnumDescriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{0} }

type FloatLayerType int32

const (
	FloatLayerType_CHANNEL                 FloatLayerType = 1
	FloatLayerType_CHANNEL_HUNT_MONSTER    FloatLayerType = 2
	FloatLayerType_CHANNEL_HUNT_MONSTER_V2 FloatLayerType = 3
)

var FloatLayerType_name = map[int32]string{
	1: "CHANNEL",
	2: "CHANNEL_HUNT_MONSTER",
	3: "CHANNEL_HUNT_MONSTER_V2",
}
var FloatLayerType_value = map[string]int32{
	"CHANNEL":                 1,
	"CHANNEL_HUNT_MONSTER":    2,
	"CHANNEL_HUNT_MONSTER_V2": 3,
}

func (x FloatLayerType) Enum() *FloatLayerType {
	p := new(FloatLayerType)
	*p = x
	return p
}
func (x FloatLayerType) String() string {
	return proto.EnumName(FloatLayerType_name, int32(x))
}
func (x *FloatLayerType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(FloatLayerType_value, data, "FloatLayerType")
	if err != nil {
		return err
	}
	*x = FloatLayerType(value)
	return nil
}
func (FloatLayerType) EnumDescriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{1} }

type CUSTOM_ENTRY_TYPE int32

const (
	CUSTOM_ENTRY_TYPE_ME_TAB CUSTOM_ENTRY_TYPE = 1
)

var CUSTOM_ENTRY_TYPE_name = map[int32]string{
	1: "ME_TAB",
}
var CUSTOM_ENTRY_TYPE_value = map[string]int32{
	"ME_TAB": 1,
}

func (x CUSTOM_ENTRY_TYPE) Enum() *CUSTOM_ENTRY_TYPE {
	p := new(CUSTOM_ENTRY_TYPE)
	*p = x
	return p
}
func (x CUSTOM_ENTRY_TYPE) String() string {
	return proto.EnumName(CUSTOM_ENTRY_TYPE_name, int32(x))
}
func (x *CUSTOM_ENTRY_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CUSTOM_ENTRY_TYPE_value, data, "CUSTOM_ENTRY_TYPE")
	if err != nil {
		return err
	}
	*x = CUSTOM_ENTRY_TYPE(value)
	return nil
}
func (CUSTOM_ENTRY_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{2} }

type ClientAuditType int32

const (
	ClientAuditType_Pass     ClientAuditType = 0
	ClientAuditType_Auditing ClientAuditType = 1
)

var ClientAuditType_name = map[int32]string{
	0: "Pass",
	1: "Auditing",
}
var ClientAuditType_value = map[string]int32{
	"Pass":     0,
	"Auditing": 1,
}

func (x ClientAuditType) Enum() *ClientAuditType {
	p := new(ClientAuditType)
	*p = x
	return p
}
func (x ClientAuditType) String() string {
	return proto.EnumName(ClientAuditType_name, int32(x))
}
func (x *ClientAuditType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ClientAuditType_value, data, "ClientAuditType")
	if err != nil {
		return err
	}
	*x = ClientAuditType(value)
	return nil
}
func (ClientAuditType) EnumDescriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{3} }

type GamePageAdvert struct {
	Name string `protobuf:"bytes,1,opt,name=name" json:"name"`
	Url  string `protobuf:"bytes,2,opt,name=url" json:"url"`
	Icon string `protobuf:"bytes,3,opt,name=icon" json:"icon"`
}

func (m *GamePageAdvert) Reset()                    { *m = GamePageAdvert{} }
func (m *GamePageAdvert) String() string            { return proto.CompactTextString(m) }
func (*GamePageAdvert) ProtoMessage()               {}
func (*GamePageAdvert) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{0} }

func (m *GamePageAdvert) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GamePageAdvert) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *GamePageAdvert) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

type SetGamePageTopLableReq struct {
	TopLableList []*GamePageAdvert `protobuf:"bytes,1,rep,name=top_lable_list,json=topLableList" json:"top_lable_list,omitempty"`
}

func (m *SetGamePageTopLableReq) Reset()         { *m = SetGamePageTopLableReq{} }
func (m *SetGamePageTopLableReq) String() string { return proto.CompactTextString(m) }
func (*SetGamePageTopLableReq) ProtoMessage()    {}
func (*SetGamePageTopLableReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAppconfigsvr, []int{1}
}

func (m *SetGamePageTopLableReq) GetTopLableList() []*GamePageAdvert {
	if m != nil {
		return m.TopLableList
	}
	return nil
}

type GetGamePageTopLableResp struct {
	TopLableList []*GamePageAdvert `protobuf:"bytes,1,rep,name=top_lable_list,json=topLableList" json:"top_lable_list,omitempty"`
}

func (m *GetGamePageTopLableResp) Reset()         { *m = GetGamePageTopLableResp{} }
func (m *GetGamePageTopLableResp) String() string { return proto.CompactTextString(m) }
func (*GetGamePageTopLableResp) ProtoMessage()    {}
func (*GetGamePageTopLableResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAppconfigsvr, []int{2}
}

func (m *GetGamePageTopLableResp) GetTopLableList() []*GamePageAdvert {
	if m != nil {
		return m.TopLableList
	}
	return nil
}

type SetGamePageEnterReq struct {
	EnterList []*GamePageAdvert `protobuf:"bytes,1,rep,name=enter_list,json=enterList" json:"enter_list,omitempty"`
}

func (m *SetGamePageEnterReq) Reset()                    { *m = SetGamePageEnterReq{} }
func (m *SetGamePageEnterReq) String() string            { return proto.CompactTextString(m) }
func (*SetGamePageEnterReq) ProtoMessage()               {}
func (*SetGamePageEnterReq) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{3} }

func (m *SetGamePageEnterReq) GetEnterList() []*GamePageAdvert {
	if m != nil {
		return m.EnterList
	}
	return nil
}

type GetGamePageEnterResp struct {
	EnterList []*GamePageAdvert `protobuf:"bytes,1,rep,name=enter_list,json=enterList" json:"enter_list,omitempty"`
}

func (m *GetGamePageEnterResp) Reset()                    { *m = GetGamePageEnterResp{} }
func (m *GetGamePageEnterResp) String() string            { return proto.CompactTextString(m) }
func (*GetGamePageEnterResp) ProtoMessage()               {}
func (*GetGamePageEnterResp) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{4} }

func (m *GetGamePageEnterResp) GetEnterList() []*GamePageAdvert {
	if m != nil {
		return m.EnterList
	}
	return nil
}

type SetGamePageAdvertReq struct {
	MainAdvList []*GamePageAdvert `protobuf:"bytes,1,rep,name=main_adv_list,json=mainAdvList" json:"main_adv_list,omitempty"`
	ViceAdvList []*GamePageAdvert `protobuf:"bytes,2,rep,name=vice_adv_list,json=viceAdvList" json:"vice_adv_list,omitempty"`
}

func (m *SetGamePageAdvertReq) Reset()                    { *m = SetGamePageAdvertReq{} }
func (m *SetGamePageAdvertReq) String() string            { return proto.CompactTextString(m) }
func (*SetGamePageAdvertReq) ProtoMessage()               {}
func (*SetGamePageAdvertReq) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{5} }

func (m *SetGamePageAdvertReq) GetMainAdvList() []*GamePageAdvert {
	if m != nil {
		return m.MainAdvList
	}
	return nil
}

func (m *SetGamePageAdvertReq) GetViceAdvList() []*GamePageAdvert {
	if m != nil {
		return m.ViceAdvList
	}
	return nil
}

type GetGamePageAdvertResp struct {
	MainAdvList []*GamePageAdvert `protobuf:"bytes,1,rep,name=main_adv_list,json=mainAdvList" json:"main_adv_list,omitempty"`
	ViceAdvList []*GamePageAdvert `protobuf:"bytes,2,rep,name=vice_adv_list,json=viceAdvList" json:"vice_adv_list,omitempty"`
}

func (m *GetGamePageAdvertResp) Reset()         { *m = GetGamePageAdvertResp{} }
func (m *GetGamePageAdvertResp) String() string { return proto.CompactTextString(m) }
func (*GetGamePageAdvertResp) ProtoMessage()    {}
func (*GetGamePageAdvertResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAppconfigsvr, []int{6}
}

func (m *GetGamePageAdvertResp) GetMainAdvList() []*GamePageAdvert {
	if m != nil {
		return m.MainAdvList
	}
	return nil
}

func (m *GetGamePageAdvertResp) GetViceAdvList() []*GamePageAdvert {
	if m != nil {
		return m.ViceAdvList
	}
	return nil
}

type SetGamePageGrayUidsReq struct {
	Uids  string     `protobuf:"bytes,1,req,name=uids" json:"uids"`
	GType AdvertType `protobuf:"varint,2,req,name=g_type,json=gType,enum=appconfig.AdvertType" json:"g_type"`
}

func (m *SetGamePageGrayUidsReq) Reset()         { *m = SetGamePageGrayUidsReq{} }
func (m *SetGamePageGrayUidsReq) String() string { return proto.CompactTextString(m) }
func (*SetGamePageGrayUidsReq) ProtoMessage()    {}
func (*SetGamePageGrayUidsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAppconfigsvr, []int{7}
}

func (m *SetGamePageGrayUidsReq) GetUids() string {
	if m != nil {
		return m.Uids
	}
	return ""
}

func (m *SetGamePageGrayUidsReq) GetGType() AdvertType {
	if m != nil {
		return m.GType
	}
	return AdvertType_Unknow
}

type GetGamePageGrayUidsReq struct {
	GType AdvertType `protobuf:"varint,1,req,name=g_type,json=gType,enum=appconfig.AdvertType" json:"g_type"`
}

func (m *GetGamePageGrayUidsReq) Reset()         { *m = GetGamePageGrayUidsReq{} }
func (m *GetGamePageGrayUidsReq) String() string { return proto.CompactTextString(m) }
func (*GetGamePageGrayUidsReq) ProtoMessage()    {}
func (*GetGamePageGrayUidsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAppconfigsvr, []int{8}
}

func (m *GetGamePageGrayUidsReq) GetGType() AdvertType {
	if m != nil {
		return m.GType
	}
	return AdvertType_Unknow
}

type GetGamePageGrayUidsResp struct {
	Uids string `protobuf:"bytes,1,req,name=uids" json:"uids"`
}

func (m *GetGamePageGrayUidsResp) Reset()         { *m = GetGamePageGrayUidsResp{} }
func (m *GetGamePageGrayUidsResp) String() string { return proto.CompactTextString(m) }
func (*GetGamePageGrayUidsResp) ProtoMessage()    {}
func (*GetGamePageGrayUidsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAppconfigsvr, []int{9}
}

func (m *GetGamePageGrayUidsResp) GetUids() string {
	if m != nil {
		return m.Uids
	}
	return ""
}

type GetUpdateTimeReq struct {
	AdvertType AdvertType `protobuf:"varint,1,req,name=advert_type,json=advertType,enum=appconfig.AdvertType" json:"advert_type"`
}

func (m *GetUpdateTimeReq) Reset()                    { *m = GetUpdateTimeReq{} }
func (m *GetUpdateTimeReq) String() string            { return proto.CompactTextString(m) }
func (*GetUpdateTimeReq) ProtoMessage()               {}
func (*GetUpdateTimeReq) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{10} }

func (m *GetUpdateTimeReq) GetAdvertType() AdvertType {
	if m != nil {
		return m.AdvertType
	}
	return AdvertType_Unknow
}

type GetUpdateTimeResp struct {
	UpdateTime uint32 `protobuf:"varint,1,req,name=update_time,json=updateTime" json:"update_time"`
}

func (m *GetUpdateTimeResp) Reset()                    { *m = GetUpdateTimeResp{} }
func (m *GetUpdateTimeResp) String() string            { return proto.CompactTextString(m) }
func (*GetUpdateTimeResp) ProtoMessage()               {}
func (*GetUpdateTimeResp) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{11} }

func (m *GetUpdateTimeResp) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type RaidRichesConfig struct {
	Url       string   `protobuf:"bytes,1,req,name=url" json:"url"`
	Title     string   `protobuf:"bytes,2,req,name=title" json:"title"`
	SubTitle  string   `protobuf:"bytes,3,opt,name=sub_title,json=subTitle" json:"sub_title"`
	BeginTime uint64   `protobuf:"varint,4,req,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime   uint64   `protobuf:"varint,5,req,name=end_time,json=endTime" json:"end_time"`
	UidSuffix []uint32 `protobuf:"varint,6,rep,name=uid_suffix,json=uidSuffix" json:"uid_suffix,omitempty"`
}

func (m *RaidRichesConfig) Reset()                    { *m = RaidRichesConfig{} }
func (m *RaidRichesConfig) String() string            { return proto.CompactTextString(m) }
func (*RaidRichesConfig) ProtoMessage()               {}
func (*RaidRichesConfig) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{12} }

func (m *RaidRichesConfig) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *RaidRichesConfig) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *RaidRichesConfig) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *RaidRichesConfig) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *RaidRichesConfig) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *RaidRichesConfig) GetUidSuffix() []uint32 {
	if m != nil {
		return m.UidSuffix
	}
	return nil
}

type CustomEntryInfo struct {
	Url            string   `protobuf:"bytes,1,req,name=url" json:"url"`
	Title          string   `protobuf:"bytes,2,req,name=title" json:"title"`
	SubTitle       string   `protobuf:"bytes,3,opt,name=sub_title,json=subTitle" json:"sub_title"`
	BeginTime      uint64   `protobuf:"varint,4,req,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime        uint64   `protobuf:"varint,5,req,name=end_time,json=endTime" json:"end_time"`
	UidSuffix      []uint32 `protobuf:"varint,6,rep,name=uid_suffix,json=uidSuffix" json:"uid_suffix,omitempty"`
	Icon           string   `protobuf:"bytes,7,opt,name=icon" json:"icon"`
	ClientType     uint32   `protobuf:"varint,8,opt,name=client_type,json=clientType" json:"client_type"`
	EntryType      uint32   `protobuf:"varint,9,opt,name=entry_type,json=entryType" json:"entry_type"`
	AndroidVersion string   `protobuf:"bytes,10,opt,name=android_version,json=androidVersion" json:"android_version"`
	IosVersion     string   `protobuf:"bytes,11,opt,name=ios_version,json=iosVersion" json:"ios_version"`
}

func (m *CustomEntryInfo) Reset()                    { *m = CustomEntryInfo{} }
func (m *CustomEntryInfo) String() string            { return proto.CompactTextString(m) }
func (*CustomEntryInfo) ProtoMessage()               {}
func (*CustomEntryInfo) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{13} }

func (m *CustomEntryInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *CustomEntryInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *CustomEntryInfo) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *CustomEntryInfo) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *CustomEntryInfo) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *CustomEntryInfo) GetUidSuffix() []uint32 {
	if m != nil {
		return m.UidSuffix
	}
	return nil
}

func (m *CustomEntryInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *CustomEntryInfo) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *CustomEntryInfo) GetEntryType() uint32 {
	if m != nil {
		return m.EntryType
	}
	return 0
}

func (m *CustomEntryInfo) GetAndroidVersion() string {
	if m != nil {
		return m.AndroidVersion
	}
	return ""
}

func (m *CustomEntryInfo) GetIosVersion() string {
	if m != nil {
		return m.IosVersion
	}
	return ""
}

type CustomEntryList struct {
	EntryList []*CustomEntryInfo `protobuf:"bytes,1,rep,name=entry_list,json=entryList" json:"entry_list,omitempty"`
}

func (m *CustomEntryList) Reset()                    { *m = CustomEntryList{} }
func (m *CustomEntryList) String() string            { return proto.CompactTextString(m) }
func (*CustomEntryList) ProtoMessage()               {}
func (*CustomEntryList) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{14} }

func (m *CustomEntryList) GetEntryList() []*CustomEntryInfo {
	if m != nil {
		return m.EntryList
	}
	return nil
}

type GameTabAdEntry struct {
	ActId          uint32 `protobuf:"varint,1,req,name=act_id,json=actId" json:"act_id"`
	ActUrl         string `protobuf:"bytes,2,req,name=act_url,json=actUrl" json:"act_url"`
	GameTabImg     string `protobuf:"bytes,3,req,name=game_tab_img,json=gameTabImg" json:"game_tab_img"`
	GuildMgroupImg string `protobuf:"bytes,4,req,name=guild_mgroup_img,json=guildMgroupImg" json:"guild_mgroup_img"`
	BeginTime      uint64 `protobuf:"varint,5,req,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime        uint64 `protobuf:"varint,6,req,name=end_time,json=endTime" json:"end_time"`
	IsValid        bool   `protobuf:"varint,7,req,name=is_valid,json=isValid" json:"is_valid"`
}

func (m *GameTabAdEntry) Reset()                    { *m = GameTabAdEntry{} }
func (m *GameTabAdEntry) String() string            { return proto.CompactTextString(m) }
func (*GameTabAdEntry) ProtoMessage()               {}
func (*GameTabAdEntry) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{15} }

func (m *GameTabAdEntry) GetActId() uint32 {
	if m != nil {
		return m.ActId
	}
	return 0
}

func (m *GameTabAdEntry) GetActUrl() string {
	if m != nil {
		return m.ActUrl
	}
	return ""
}

func (m *GameTabAdEntry) GetGameTabImg() string {
	if m != nil {
		return m.GameTabImg
	}
	return ""
}

func (m *GameTabAdEntry) GetGuildMgroupImg() string {
	if m != nil {
		return m.GuildMgroupImg
	}
	return ""
}

func (m *GameTabAdEntry) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GameTabAdEntry) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GameTabAdEntry) GetIsValid() bool {
	if m != nil {
		return m.IsValid
	}
	return false
}

type GameTabAdList struct {
	EntryList []*GameTabAdEntry `protobuf:"bytes,1,rep,name=entry_list,json=entryList" json:"entry_list,omitempty"`
}

func (m *GameTabAdList) Reset()                    { *m = GameTabAdList{} }
func (m *GameTabAdList) String() string            { return proto.CompactTextString(m) }
func (*GameTabAdList) ProtoMessage()               {}
func (*GameTabAdList) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{16} }

func (m *GameTabAdList) GetEntryList() []*GameTabAdEntry {
	if m != nil {
		return m.EntryList
	}
	return nil
}

type ChannelBackground struct {
	BackgroundUrl string `protobuf:"bytes,1,req,name=background_url,json=backgroundUrl" json:"background_url"`
	BeginTime     uint64 `protobuf:"varint,2,req,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime       uint64 `protobuf:"varint,3,req,name=end_time,json=endTime" json:"end_time"`
}

func (m *ChannelBackground) Reset()                    { *m = ChannelBackground{} }
func (m *ChannelBackground) String() string            { return proto.CompactTextString(m) }
func (*ChannelBackground) ProtoMessage()               {}
func (*ChannelBackground) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{17} }

func (m *ChannelBackground) GetBackgroundUrl() string {
	if m != nil {
		return m.BackgroundUrl
	}
	return ""
}

func (m *ChannelBackground) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ChannelBackground) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type ChannelBackgroundList struct {
	BackgroundList []*ChannelBackground `protobuf:"bytes,1,rep,name=background_list,json=backgroundList" json:"background_list,omitempty"`
}

func (m *ChannelBackgroundList) Reset()         { *m = ChannelBackgroundList{} }
func (m *ChannelBackgroundList) String() string { return proto.CompactTextString(m) }
func (*ChannelBackgroundList) ProtoMessage()    {}
func (*ChannelBackgroundList) Descriptor() ([]byte, []int) {
	return fileDescriptorAppconfigsvr, []int{18}
}

func (m *ChannelBackgroundList) GetBackgroundList() []*ChannelBackground {
	if m != nil {
		return m.BackgroundList
	}
	return nil
}

// rush限流机制相关参数，有些活动需要配上
type RushInfo struct {
	Type          uint32 `protobuf:"varint,1,opt,name=type" json:"type"`
	RushMaxRandTs uint32 `protobuf:"varint,2,opt,name=rush_max_rand_ts,json=rushMaxRandTs" json:"rush_max_rand_ts"`
	RushWaitTs    uint32 `protobuf:"varint,3,opt,name=rush_wait_ts,json=rushWaitTs" json:"rush_wait_ts"`
}

func (m *RushInfo) Reset()                    { *m = RushInfo{} }
func (m *RushInfo) String() string            { return proto.CompactTextString(m) }
func (*RushInfo) ProtoMessage()               {}
func (*RushInfo) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{19} }

func (m *RushInfo) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *RushInfo) GetRushMaxRandTs() uint32 {
	if m != nil {
		return m.RushMaxRandTs
	}
	return 0
}

func (m *RushInfo) GetRushWaitTs() uint32 {
	if m != nil {
		return m.RushWaitTs
	}
	return 0
}

type FloatLayerEntry struct {
	Img                string    `protobuf:"bytes,1,req,name=img" json:"img"`
	Url                string    `protobuf:"bytes,2,req,name=url" json:"url"`
	Type               uint32    `protobuf:"varint,3,req,name=type" json:"type"`
	BeginTime          uint64    `protobuf:"varint,4,req,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime            uint64    `protobuf:"varint,5,req,name=end_time,json=endTime" json:"end_time"`
	DotType            uint32    `protobuf:"varint,6,opt,name=dot_type,json=dotType" json:"dot_type"`
	DotValue           uint32    `protobuf:"varint,7,opt,name=dot_value,json=dotValue" json:"dot_value"`
	TopImg             string    `protobuf:"bytes,8,opt,name=top_img,json=topImg" json:"top_img"`
	AppId              uint32    `protobuf:"varint,9,opt,name=app_id,json=appId" json:"app_id"`
	MarketId           uint32    `protobuf:"varint,10,opt,name=market_id,json=marketId" json:"market_id"`
	Platform           uint32    `protobuf:"varint,11,opt,name=platform" json:"platform"`
	DisplayChannelType uint32    `protobuf:"varint,12,opt,name=display_channel_type,json=displayChannelType" json:"display_channel_type"`
	RushInfo           *RushInfo `protobuf:"bytes,13,opt,name=rush_info,json=rushInfo" json:"rush_info,omitempty"`
}

func (m *FloatLayerEntry) Reset()                    { *m = FloatLayerEntry{} }
func (m *FloatLayerEntry) String() string            { return proto.CompactTextString(m) }
func (*FloatLayerEntry) ProtoMessage()               {}
func (*FloatLayerEntry) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{20} }

func (m *FloatLayerEntry) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *FloatLayerEntry) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *FloatLayerEntry) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *FloatLayerEntry) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *FloatLayerEntry) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *FloatLayerEntry) GetDotType() uint32 {
	if m != nil {
		return m.DotType
	}
	return 0
}

func (m *FloatLayerEntry) GetDotValue() uint32 {
	if m != nil {
		return m.DotValue
	}
	return 0
}

func (m *FloatLayerEntry) GetTopImg() string {
	if m != nil {
		return m.TopImg
	}
	return ""
}

func (m *FloatLayerEntry) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *FloatLayerEntry) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *FloatLayerEntry) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *FloatLayerEntry) GetDisplayChannelType() uint32 {
	if m != nil {
		return m.DisplayChannelType
	}
	return 0
}

func (m *FloatLayerEntry) GetRushInfo() *RushInfo {
	if m != nil {
		return m.RushInfo
	}
	return nil
}

type FloatLayerList struct {
	EntryList []*FloatLayerEntry `protobuf:"bytes,1,rep,name=entry_list,json=entryList" json:"entry_list,omitempty"`
}

func (m *FloatLayerList) Reset()                    { *m = FloatLayerList{} }
func (m *FloatLayerList) String() string            { return proto.CompactTextString(m) }
func (*FloatLayerList) ProtoMessage()               {}
func (*FloatLayerList) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{21} }

func (m *FloatLayerList) GetEntryList() []*FloatLayerEntry {
	if m != nil {
		return m.EntryList
	}
	return nil
}

type MsgPageAdvertEntry struct {
	Id             uint32   `protobuf:"varint,1,req,name=id" json:"id"`
	Img            string   `protobuf:"bytes,2,req,name=img" json:"img"`
	Url            string   `protobuf:"bytes,3,req,name=url" json:"url"`
	Text           string   `protobuf:"bytes,4,req,name=text" json:"text"`
	BeginTime      uint64   `protobuf:"varint,5,req,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime        uint64   `protobuf:"varint,6,req,name=end_time,json=endTime" json:"end_time"`
	Platform       uint32   `protobuf:"varint,7,opt,name=platform" json:"platform"`
	IncludeChannel []string `protobuf:"bytes,8,rep,name=include_channel,json=includeChannel" json:"include_channel,omitempty"`
	ExcludeChannel []string `protobuf:"bytes,9,rep,name=exclude_channel,json=excludeChannel" json:"exclude_channel,omitempty"`
	IsNewbieEntry  bool     `protobuf:"varint,10,opt,name=is_newbie_entry,json=isNewbieEntry" json:"is_newbie_entry"`
	AppId          uint32   `protobuf:"varint,11,opt,name=app_id,json=appId" json:"app_id"`
	MarketId       uint32   `protobuf:"varint,12,opt,name=market_id,json=marketId" json:"market_id"`
}

func (m *MsgPageAdvertEntry) Reset()                    { *m = MsgPageAdvertEntry{} }
func (m *MsgPageAdvertEntry) String() string            { return proto.CompactTextString(m) }
func (*MsgPageAdvertEntry) ProtoMessage()               {}
func (*MsgPageAdvertEntry) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{22} }

func (m *MsgPageAdvertEntry) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MsgPageAdvertEntry) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *MsgPageAdvertEntry) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *MsgPageAdvertEntry) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *MsgPageAdvertEntry) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *MsgPageAdvertEntry) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *MsgPageAdvertEntry) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *MsgPageAdvertEntry) GetIncludeChannel() []string {
	if m != nil {
		return m.IncludeChannel
	}
	return nil
}

func (m *MsgPageAdvertEntry) GetExcludeChannel() []string {
	if m != nil {
		return m.ExcludeChannel
	}
	return nil
}

func (m *MsgPageAdvertEntry) GetIsNewbieEntry() bool {
	if m != nil {
		return m.IsNewbieEntry
	}
	return false
}

func (m *MsgPageAdvertEntry) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *MsgPageAdvertEntry) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type MsgPageAdvertList struct {
	EntryList []*MsgPageAdvertEntry `protobuf:"bytes,1,rep,name=entry_list,json=entryList" json:"entry_list,omitempty"`
}

func (m *MsgPageAdvertList) Reset()                    { *m = MsgPageAdvertList{} }
func (m *MsgPageAdvertList) String() string            { return proto.CompactTextString(m) }
func (*MsgPageAdvertList) ProtoMessage()               {}
func (*MsgPageAdvertList) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{23} }

func (m *MsgPageAdvertList) GetEntryList() []*MsgPageAdvertEntry {
	if m != nil {
		return m.EntryList
	}
	return nil
}

type GetAppConfigReq struct {
	AdvertType AdvertType `protobuf:"varint,1,req,name=advert_type,json=advertType,enum=appconfig.AdvertType" json:"advert_type"`
}

func (m *GetAppConfigReq) Reset()                    { *m = GetAppConfigReq{} }
func (m *GetAppConfigReq) String() string            { return proto.CompactTextString(m) }
func (*GetAppConfigReq) ProtoMessage()               {}
func (*GetAppConfigReq) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{24} }

func (m *GetAppConfigReq) GetAdvertType() AdvertType {
	if m != nil {
		return m.AdvertType
	}
	return AdvertType_Unknow
}

type GetAppConfigDetailResp struct {
	Detail []byte `protobuf:"bytes,1,req,name=detail" json:"detail"`
}

func (m *GetAppConfigDetailResp) Reset()         { *m = GetAppConfigDetailResp{} }
func (m *GetAppConfigDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetAppConfigDetailResp) ProtoMessage()    {}
func (*GetAppConfigDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAppconfigsvr, []int{25}
}

func (m *GetAppConfigDetailResp) GetDetail() []byte {
	if m != nil {
		return m.Detail
	}
	return nil
}

type SetAppConfigDetailReq struct {
	AdvertType AdvertType `protobuf:"varint,1,req,name=advert_type,json=advertType,enum=appconfig.AdvertType" json:"advert_type"`
	Detail     []byte     `protobuf:"bytes,2,req,name=detail" json:"detail"`
}

func (m *SetAppConfigDetailReq) Reset()         { *m = SetAppConfigDetailReq{} }
func (m *SetAppConfigDetailReq) String() string { return proto.CompactTextString(m) }
func (*SetAppConfigDetailReq) ProtoMessage()    {}
func (*SetAppConfigDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAppconfigsvr, []int{26}
}

func (m *SetAppConfigDetailReq) GetAdvertType() AdvertType {
	if m != nil {
		return m.AdvertType
	}
	return AdvertType_Unknow
}

func (m *SetAppConfigDetailReq) GetDetail() []byte {
	if m != nil {
		return m.Detail
	}
	return nil
}

type CommonEffectSource struct {
	Type uint32 `protobuf:"varint,1,req,name=type" json:"type"`
	Url  string `protobuf:"bytes,2,req,name=url" json:"url"`
	Md5  string `protobuf:"bytes,3,req,name=md5" json:"md5"`
	Id   uint32 `protobuf:"varint,4,opt,name=id" json:"id"`
}

func (m *CommonEffectSource) Reset()                    { *m = CommonEffectSource{} }
func (m *CommonEffectSource) String() string            { return proto.CompactTextString(m) }
func (*CommonEffectSource) ProtoMessage()               {}
func (*CommonEffectSource) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{27} }

func (m *CommonEffectSource) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *CommonEffectSource) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *CommonEffectSource) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *CommonEffectSource) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type CommonEffectSourceList struct {
	EntryList []*CommonEffectSource `protobuf:"bytes,1,rep,name=entry_list,json=entryList" json:"entry_list,omitempty"`
}

func (m *CommonEffectSourceList) Reset()         { *m = CommonEffectSourceList{} }
func (m *CommonEffectSourceList) String() string { return proto.CompactTextString(m) }
func (*CommonEffectSourceList) ProtoMessage()    {}
func (*CommonEffectSourceList) Descriptor() ([]byte, []int) {
	return fileDescriptorAppconfigsvr, []int{28}
}

func (m *CommonEffectSourceList) GetEntryList() []*CommonEffectSource {
	if m != nil {
		return m.EntryList
	}
	return nil
}

type ClientAuditInfo struct {
	ClientType    uint32 `protobuf:"varint,1,req,name=client_type,json=clientType" json:"client_type"`
	ClientVersion uint32 `protobuf:"varint,2,req,name=client_version,json=clientVersion" json:"client_version"`
	AuditStatus   uint32 `protobuf:"varint,3,req,name=audit_status,json=auditStatus" json:"audit_status"`
}

func (m *ClientAuditInfo) Reset()                    { *m = ClientAuditInfo{} }
func (m *ClientAuditInfo) String() string            { return proto.CompactTextString(m) }
func (*ClientAuditInfo) ProtoMessage()               {}
func (*ClientAuditInfo) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{29} }

func (m *ClientAuditInfo) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *ClientAuditInfo) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *ClientAuditInfo) GetAuditStatus() uint32 {
	if m != nil {
		return m.AuditStatus
	}
	return 0
}

// ios审核信息
type SetIosAuditInfoReq struct {
	ClientVersion uint32 `protobuf:"varint,1,req,name=client_version,json=clientVersion" json:"client_version"`
	AuditStatus   uint32 `protobuf:"varint,2,req,name=audit_status,json=auditStatus" json:"audit_status"`
}

func (m *SetIosAuditInfoReq) Reset()                    { *m = SetIosAuditInfoReq{} }
func (m *SetIosAuditInfoReq) String() string            { return proto.CompactTextString(m) }
func (*SetIosAuditInfoReq) ProtoMessage()               {}
func (*SetIosAuditInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{30} }

func (m *SetIosAuditInfoReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *SetIosAuditInfoReq) GetAuditStatus() uint32 {
	if m != nil {
		return m.AuditStatus
	}
	return 0
}

type GetIosAuditInfoResp struct {
	AuditInfo *ClientAuditInfo `protobuf:"bytes,1,req,name=audit_info,json=auditInfo" json:"audit_info,omitempty"`
}

func (m *GetIosAuditInfoResp) Reset()                    { *m = GetIosAuditInfoResp{} }
func (m *GetIosAuditInfoResp) String() string            { return proto.CompactTextString(m) }
func (*GetIosAuditInfoResp) ProtoMessage()               {}
func (*GetIosAuditInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{31} }

func (m *GetIosAuditInfoResp) GetAuditInfo() *ClientAuditInfo {
	if m != nil {
		return m.AuditInfo
	}
	return nil
}

type ConfigKV struct {
	Key   string `protobuf:"bytes,1,req,name=key" json:"key"`
	Value string `protobuf:"bytes,2,req,name=value" json:"value"`
}

func (m *ConfigKV) Reset()                    { *m = ConfigKV{} }
func (m *ConfigKV) String() string            { return proto.CompactTextString(m) }
func (*ConfigKV) ProtoMessage()               {}
func (*ConfigKV) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{32} }

func (m *ConfigKV) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *ConfigKV) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

type ConfigData struct {
	ConfigKvList []*ConfigKV `protobuf:"bytes,1,rep,name=config_kv_list,json=configKvList" json:"config_kv_list,omitempty"`
}

func (m *ConfigData) Reset()                    { *m = ConfigData{} }
func (m *ConfigData) String() string            { return proto.CompactTextString(m) }
func (*ConfigData) ProtoMessage()               {}
func (*ConfigData) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{33} }

func (m *ConfigData) GetConfigKvList() []*ConfigKV {
	if m != nil {
		return m.ConfigKvList
	}
	return nil
}

type MainPageTagConfig struct {
	ConfigKvList   []*ConfigKV `protobuf:"bytes,1,rep,name=config_kv_list,json=configKvList" json:"config_kv_list,omitempty"`
	ExpirationTime uint32      `protobuf:"varint,2,req,name=expiration_time,json=expirationTime" json:"expiration_time"`
	BeginTime      uint32      `protobuf:"varint,3,req,name=begin_time,json=beginTime" json:"begin_time"`
}

func (m *MainPageTagConfig) Reset()                    { *m = MainPageTagConfig{} }
func (m *MainPageTagConfig) String() string            { return proto.CompactTextString(m) }
func (*MainPageTagConfig) ProtoMessage()               {}
func (*MainPageTagConfig) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{34} }

func (m *MainPageTagConfig) GetConfigKvList() []*ConfigKV {
	if m != nil {
		return m.ConfigKvList
	}
	return nil
}

func (m *MainPageTagConfig) GetExpirationTime() uint32 {
	if m != nil {
		return m.ExpirationTime
	}
	return 0
}

func (m *MainPageTagConfig) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

type ConfigInfo struct {
	ConfigType     uint32 `protobuf:"varint,1,req,name=config_type,json=configType" json:"config_type"`
	ConfigVersion  uint32 `protobuf:"varint,2,req,name=config_version,json=configVersion" json:"config_version"`
	ConfigData     []byte `protobuf:"bytes,3,req,name=config_data,json=configData" json:"config_data"`
	BeginTime      uint32 `protobuf:"varint,4,req,name=begin_time,json=beginTime" json:"begin_time"`
	ExpirationTime uint32 `protobuf:"varint,5,req,name=expiration_time,json=expirationTime" json:"expiration_time"`
}

func (m *ConfigInfo) Reset()                    { *m = ConfigInfo{} }
func (m *ConfigInfo) String() string            { return proto.CompactTextString(m) }
func (*ConfigInfo) ProtoMessage()               {}
func (*ConfigInfo) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{35} }

func (m *ConfigInfo) GetConfigType() uint32 {
	if m != nil {
		return m.ConfigType
	}
	return 0
}

func (m *ConfigInfo) GetConfigVersion() uint32 {
	if m != nil {
		return m.ConfigVersion
	}
	return 0
}

func (m *ConfigInfo) GetConfigData() []byte {
	if m != nil {
		return m.ConfigData
	}
	return nil
}

func (m *ConfigInfo) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ConfigInfo) GetExpirationTime() uint32 {
	if m != nil {
		return m.ExpirationTime
	}
	return 0
}

type ConfigRangeInfoList struct {
	ConfigType     uint32        `protobuf:"varint,1,req,name=config_type,json=configType" json:"config_type"`
	ConfigVersion  uint32        `protobuf:"varint,2,req,name=config_version,json=configVersion" json:"config_version"`
	ConfigInfoList []*ConfigInfo `protobuf:"bytes,3,rep,name=config_info_list,json=configInfoList" json:"config_info_list,omitempty"`
}

func (m *ConfigRangeInfoList) Reset()                    { *m = ConfigRangeInfoList{} }
func (m *ConfigRangeInfoList) String() string            { return proto.CompactTextString(m) }
func (*ConfigRangeInfoList) ProtoMessage()               {}
func (*ConfigRangeInfoList) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{36} }

func (m *ConfigRangeInfoList) GetConfigType() uint32 {
	if m != nil {
		return m.ConfigType
	}
	return 0
}

func (m *ConfigRangeInfoList) GetConfigVersion() uint32 {
	if m != nil {
		return m.ConfigVersion
	}
	return 0
}

func (m *ConfigRangeInfoList) GetConfigInfoList() []*ConfigInfo {
	if m != nil {
		return m.ConfigInfoList
	}
	return nil
}

type GetSyncConfigV2AllVersionReq struct {
}

func (m *GetSyncConfigV2AllVersionReq) Reset()         { *m = GetSyncConfigV2AllVersionReq{} }
func (m *GetSyncConfigV2AllVersionReq) String() string { return proto.CompactTextString(m) }
func (*GetSyncConfigV2AllVersionReq) ProtoMessage()    {}
func (*GetSyncConfigV2AllVersionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAppconfigsvr, []int{37}
}

type GetSyncConfigV2AllVersionResp struct {
	VersionList []*ConfigKV `protobuf:"bytes,1,rep,name=version_list,json=versionList" json:"version_list,omitempty"`
}

func (m *GetSyncConfigV2AllVersionResp) Reset()         { *m = GetSyncConfigV2AllVersionResp{} }
func (m *GetSyncConfigV2AllVersionResp) String() string { return proto.CompactTextString(m) }
func (*GetSyncConfigV2AllVersionResp) ProtoMessage()    {}
func (*GetSyncConfigV2AllVersionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAppconfigsvr, []int{38}
}

func (m *GetSyncConfigV2AllVersionResp) GetVersionList() []*ConfigKV {
	if m != nil {
		return m.VersionList
	}
	return nil
}

type GetSyncConfigV2InfoByKeyListReq struct {
	ConfigTypeList []uint32 `protobuf:"varint,1,rep,name=config_type_list,json=configTypeList" json:"config_type_list,omitempty"`
}

func (m *GetSyncConfigV2InfoByKeyListReq) Reset()         { *m = GetSyncConfigV2InfoByKeyListReq{} }
func (m *GetSyncConfigV2InfoByKeyListReq) String() string { return proto.CompactTextString(m) }
func (*GetSyncConfigV2InfoByKeyListReq) ProtoMessage()    {}
func (*GetSyncConfigV2InfoByKeyListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAppconfigsvr, []int{39}
}

func (m *GetSyncConfigV2InfoByKeyListReq) GetConfigTypeList() []uint32 {
	if m != nil {
		return m.ConfigTypeList
	}
	return nil
}

type GetSyncConfigV2InfoByKeyListResp struct {
	ConfigInfoList []*ConfigInfo `protobuf:"bytes,1,rep,name=config_info_list,json=configInfoList" json:"config_info_list,omitempty"`
}

func (m *GetSyncConfigV2InfoByKeyListResp) Reset()         { *m = GetSyncConfigV2InfoByKeyListResp{} }
func (m *GetSyncConfigV2InfoByKeyListResp) String() string { return proto.CompactTextString(m) }
func (*GetSyncConfigV2InfoByKeyListResp) ProtoMessage()    {}
func (*GetSyncConfigV2InfoByKeyListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAppconfigsvr, []int{40}
}

func (m *GetSyncConfigV2InfoByKeyListResp) GetConfigInfoList() []*ConfigInfo {
	if m != nil {
		return m.ConfigInfoList
	}
	return nil
}

type StrReq struct {
	ConfigType uint32 `protobuf:"varint,1,req,name=config_type,json=configType" json:"config_type"`
}

func (m *StrReq) Reset()                    { *m = StrReq{} }
func (m *StrReq) String() string            { return proto.CompactTextString(m) }
func (*StrReq) ProtoMessage()               {}
func (*StrReq) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{41} }

func (m *StrReq) GetConfigType() uint32 {
	if m != nil {
		return m.ConfigType
	}
	return 0
}

type CodeResp struct {
	Code uint32 `protobuf:"varint,1,req,name=code" json:"code"`
}

func (m *CodeResp) Reset()                    { *m = CodeResp{} }
func (m *CodeResp) String() string            { return proto.CompactTextString(m) }
func (*CodeResp) ProtoMessage()               {}
func (*CodeResp) Descriptor() ([]byte, []int) { return fileDescriptorAppconfigsvr, []int{42} }

func (m *CodeResp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

// 获取当前 各种送礼/收礼活动通用的 排行榜黑名单用户列表
// 该接口数据是从配置中心SDK获取，因此该接口只提供给WEB服务来获取数据
// 其他服务 可以直接使用通用配置中心SDK来获取黑名单数据 (src/commConfigCenterSDK/rankblacklist/RankBlackUidListWatcher.h)
type GetRankBlackUidListReq struct {
}

func (m *GetRankBlackUidListReq) Reset()         { *m = GetRankBlackUidListReq{} }
func (m *GetRankBlackUidListReq) String() string { return proto.CompactTextString(m) }
func (*GetRankBlackUidListReq) ProtoMessage()    {}
func (*GetRankBlackUidListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAppconfigsvr, []int{43}
}

type GetRankBlackUidListResp struct {
	RecvRankUidList []uint32 `protobuf:"varint,1,rep,name=recv_rank_uid_list,json=recvRankUidList" json:"recv_rank_uid_list,omitempty"`
	SendRankUidList []uint32 `protobuf:"varint,2,rep,name=send_rank_uid_list,json=sendRankUidList" json:"send_rank_uid_list,omitempty"`
}

func (m *GetRankBlackUidListResp) Reset()         { *m = GetRankBlackUidListResp{} }
func (m *GetRankBlackUidListResp) String() string { return proto.CompactTextString(m) }
func (*GetRankBlackUidListResp) ProtoMessage()    {}
func (*GetRankBlackUidListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAppconfigsvr, []int{44}
}

func (m *GetRankBlackUidListResp) GetRecvRankUidList() []uint32 {
	if m != nil {
		return m.RecvRankUidList
	}
	return nil
}

func (m *GetRankBlackUidListResp) GetSendRankUidList() []uint32 {
	if m != nil {
		return m.SendRankUidList
	}
	return nil
}

func init() {
	proto.RegisterType((*GamePageAdvert)(nil), "appconfig.GamePageAdvert")
	proto.RegisterType((*SetGamePageTopLableReq)(nil), "appconfig.SetGamePageTopLableReq")
	proto.RegisterType((*GetGamePageTopLableResp)(nil), "appconfig.GetGamePageTopLableResp")
	proto.RegisterType((*SetGamePageEnterReq)(nil), "appconfig.SetGamePageEnterReq")
	proto.RegisterType((*GetGamePageEnterResp)(nil), "appconfig.GetGamePageEnterResp")
	proto.RegisterType((*SetGamePageAdvertReq)(nil), "appconfig.SetGamePageAdvertReq")
	proto.RegisterType((*GetGamePageAdvertResp)(nil), "appconfig.GetGamePageAdvertResp")
	proto.RegisterType((*SetGamePageGrayUidsReq)(nil), "appconfig.SetGamePageGrayUidsReq")
	proto.RegisterType((*GetGamePageGrayUidsReq)(nil), "appconfig.GetGamePageGrayUidsReq")
	proto.RegisterType((*GetGamePageGrayUidsResp)(nil), "appconfig.GetGamePageGrayUidsResp")
	proto.RegisterType((*GetUpdateTimeReq)(nil), "appconfig.GetUpdateTimeReq")
	proto.RegisterType((*GetUpdateTimeResp)(nil), "appconfig.GetUpdateTimeResp")
	proto.RegisterType((*RaidRichesConfig)(nil), "appconfig.RaidRichesConfig")
	proto.RegisterType((*CustomEntryInfo)(nil), "appconfig.CustomEntryInfo")
	proto.RegisterType((*CustomEntryList)(nil), "appconfig.CustomEntryList")
	proto.RegisterType((*GameTabAdEntry)(nil), "appconfig.GameTabAdEntry")
	proto.RegisterType((*GameTabAdList)(nil), "appconfig.GameTabAdList")
	proto.RegisterType((*ChannelBackground)(nil), "appconfig.ChannelBackground")
	proto.RegisterType((*ChannelBackgroundList)(nil), "appconfig.ChannelBackgroundList")
	proto.RegisterType((*RushInfo)(nil), "appconfig.RushInfo")
	proto.RegisterType((*FloatLayerEntry)(nil), "appconfig.FloatLayerEntry")
	proto.RegisterType((*FloatLayerList)(nil), "appconfig.FloatLayerList")
	proto.RegisterType((*MsgPageAdvertEntry)(nil), "appconfig.MsgPageAdvertEntry")
	proto.RegisterType((*MsgPageAdvertList)(nil), "appconfig.MsgPageAdvertList")
	proto.RegisterType((*GetAppConfigReq)(nil), "appconfig.GetAppConfigReq")
	proto.RegisterType((*GetAppConfigDetailResp)(nil), "appconfig.GetAppConfigDetailResp")
	proto.RegisterType((*SetAppConfigDetailReq)(nil), "appconfig.SetAppConfigDetailReq")
	proto.RegisterType((*CommonEffectSource)(nil), "appconfig.CommonEffectSource")
	proto.RegisterType((*CommonEffectSourceList)(nil), "appconfig.CommonEffectSourceList")
	proto.RegisterType((*ClientAuditInfo)(nil), "appconfig.ClientAuditInfo")
	proto.RegisterType((*SetIosAuditInfoReq)(nil), "appconfig.SetIosAuditInfoReq")
	proto.RegisterType((*GetIosAuditInfoResp)(nil), "appconfig.GetIosAuditInfoResp")
	proto.RegisterType((*ConfigKV)(nil), "appconfig.ConfigKV")
	proto.RegisterType((*ConfigData)(nil), "appconfig.ConfigData")
	proto.RegisterType((*MainPageTagConfig)(nil), "appconfig.MainPageTagConfig")
	proto.RegisterType((*ConfigInfo)(nil), "appconfig.ConfigInfo")
	proto.RegisterType((*ConfigRangeInfoList)(nil), "appconfig.ConfigRangeInfoList")
	proto.RegisterType((*GetSyncConfigV2AllVersionReq)(nil), "appconfig.GetSyncConfigV2AllVersionReq")
	proto.RegisterType((*GetSyncConfigV2AllVersionResp)(nil), "appconfig.GetSyncConfigV2AllVersionResp")
	proto.RegisterType((*GetSyncConfigV2InfoByKeyListReq)(nil), "appconfig.GetSyncConfigV2InfoByKeyListReq")
	proto.RegisterType((*GetSyncConfigV2InfoByKeyListResp)(nil), "appconfig.GetSyncConfigV2InfoByKeyListResp")
	proto.RegisterType((*StrReq)(nil), "appconfig.StrReq")
	proto.RegisterType((*CodeResp)(nil), "appconfig.CodeResp")
	proto.RegisterType((*GetRankBlackUidListReq)(nil), "appconfig.GetRankBlackUidListReq")
	proto.RegisterType((*GetRankBlackUidListResp)(nil), "appconfig.GetRankBlackUidListResp")
	proto.RegisterEnum("appconfig.AdvertType", AdvertType_name, AdvertType_value)
	proto.RegisterEnum("appconfig.FloatLayerType", FloatLayerType_name, FloatLayerType_value)
	proto.RegisterEnum("appconfig.CUSTOM_ENTRY_TYPE", CUSTOM_ENTRY_TYPE_name, CUSTOM_ENTRY_TYPE_value)
	proto.RegisterEnum("appconfig.ClientAuditType", ClientAuditType_name, ClientAuditType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for AppConfigSvr service

type AppConfigSvrClient interface {
	SetGamePageTopLable(ctx context.Context, in *SetGamePageTopLableReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGamePageTopLable(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetGamePageTopLableResp, error)
	SetGamePageEnter(ctx context.Context, in *SetGamePageEnterReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGamePageEnter(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetGamePageEnterResp, error)
	SetGamePageAdvert(ctx context.Context, in *SetGamePageAdvertReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGamePageAdvert(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetGamePageAdvertResp, error)
	SetGamePageGrayUids(ctx context.Context, in *SetGamePageGrayUidsReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGamePageGrayUids(ctx context.Context, in *GetGamePageGrayUidsReq, opts ...grpc.CallOption) (*GetGamePageGrayUidsResp, error)
	GetUpdateTime(ctx context.Context, in *GetUpdateTimeReq, opts ...grpc.CallOption) (*GetUpdateTimeResp, error)
	GetAppConfigDetail(ctx context.Context, in *GetAppConfigReq, opts ...grpc.CallOption) (*GetAppConfigDetailResp, error)
	SetAppConfigDetail(ctx context.Context, in *SetAppConfigDetailReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	AddCustomEntry(ctx context.Context, in *CustomEntryInfo, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetCustomEntry(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*CustomEntryList, error)
	RemoveCustomEntry(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	AddGameTabAd(ctx context.Context, in *GameTabAdEntry, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGameTabAd(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GameTabAdEntry, error)
	RemoveGameTabAd(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	AddFloatLayer(ctx context.Context, in *FloatLayerEntry, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetFloatLayer(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*FloatLayerList, error)
	DelFloatLayer(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	AddMsgPageAdvert(ctx context.Context, in *MsgPageAdvertEntry, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetMsgPageAdvert(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*MsgPageAdvertList, error)
	DelMsgPageAdvert(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	SetIosAuditInfo(ctx context.Context, in *SetIosAuditInfoReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetIosAuditInfo(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetIosAuditInfoResp, error)
	MoveCustomEntry(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	AddCommonEffectSource(ctx context.Context, in *CommonEffectSource, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetCommonEffectSource(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*CommonEffectSourceList, error)
	DelCommonEffectSource(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取所有的 ConfigKEY 对应的 Config版本ID
	GetSyncConfigV2AllVersion(ctx context.Context, in *GetSyncConfigV2AllVersionReq, opts ...grpc.CallOption) (*GetSyncConfigV2AllVersionResp, error)
	// 根据 ConfigKEY 获取configV2的数据
	GetSyncConfigV2InfoByKeyList(ctx context.Context, in *GetSyncConfigV2InfoByKeyListReq, opts ...grpc.CallOption) (*GetSyncConfigV2InfoByKeyListResp, error)
	SetSyncConfigObj(ctx context.Context, in *ConfigRangeInfoList, opts ...grpc.CallOption) (*CodeResp, error)
	DelSyncConfigObj(ctx context.Context, in *StrReq, opts ...grpc.CallOption) (*CodeResp, error)
	GetSyncConfigObj(ctx context.Context, in *StrReq, opts ...grpc.CallOption) (*ConfigInfo, error)
	AddChannelBackground(ctx context.Context, in *ChannelBackground, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetChannelBackground(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*ChannelBackgroundList, error)
	RemoveChannelBackground(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取当前 各种送礼/收礼活动通用的 排行榜黑名单用户列表
	GetRankBlackUidList(ctx context.Context, in *GetRankBlackUidListReq, opts ...grpc.CallOption) (*GetRankBlackUidListResp, error)
}

type appConfigSvrClient struct {
	cc *grpc.ClientConn
}

func NewAppConfigSvrClient(cc *grpc.ClientConn) AppConfigSvrClient {
	return &appConfigSvrClient{cc}
}

func (c *appConfigSvrClient) SetGamePageTopLable(ctx context.Context, in *SetGamePageTopLableReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/SetGamePageTopLable", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) GetGamePageTopLable(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetGamePageTopLableResp, error) {
	out := new(GetGamePageTopLableResp)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/GetGamePageTopLable", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) SetGamePageEnter(ctx context.Context, in *SetGamePageEnterReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/SetGamePageEnter", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) GetGamePageEnter(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetGamePageEnterResp, error) {
	out := new(GetGamePageEnterResp)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/GetGamePageEnter", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) SetGamePageAdvert(ctx context.Context, in *SetGamePageAdvertReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/SetGamePageAdvert", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) GetGamePageAdvert(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetGamePageAdvertResp, error) {
	out := new(GetGamePageAdvertResp)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/GetGamePageAdvert", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) SetGamePageGrayUids(ctx context.Context, in *SetGamePageGrayUidsReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/SetGamePageGrayUids", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) GetGamePageGrayUids(ctx context.Context, in *GetGamePageGrayUidsReq, opts ...grpc.CallOption) (*GetGamePageGrayUidsResp, error) {
	out := new(GetGamePageGrayUidsResp)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/GetGamePageGrayUids", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) GetUpdateTime(ctx context.Context, in *GetUpdateTimeReq, opts ...grpc.CallOption) (*GetUpdateTimeResp, error) {
	out := new(GetUpdateTimeResp)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/GetUpdateTime", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) GetAppConfigDetail(ctx context.Context, in *GetAppConfigReq, opts ...grpc.CallOption) (*GetAppConfigDetailResp, error) {
	out := new(GetAppConfigDetailResp)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/GetAppConfigDetail", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) SetAppConfigDetail(ctx context.Context, in *SetAppConfigDetailReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/SetAppConfigDetail", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) AddCustomEntry(ctx context.Context, in *CustomEntryInfo, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/AddCustomEntry", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) GetCustomEntry(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*CustomEntryList, error) {
	out := new(CustomEntryList)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/GetCustomEntry", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) RemoveCustomEntry(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/RemoveCustomEntry", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) AddGameTabAd(ctx context.Context, in *GameTabAdEntry, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/AddGameTabAd", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) GetGameTabAd(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GameTabAdEntry, error) {
	out := new(GameTabAdEntry)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/GetGameTabAd", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) RemoveGameTabAd(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/RemoveGameTabAd", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) AddFloatLayer(ctx context.Context, in *FloatLayerEntry, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/AddFloatLayer", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) GetFloatLayer(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*FloatLayerList, error) {
	out := new(FloatLayerList)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/GetFloatLayer", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) DelFloatLayer(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/DelFloatLayer", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) AddMsgPageAdvert(ctx context.Context, in *MsgPageAdvertEntry, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/AddMsgPageAdvert", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) GetMsgPageAdvert(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*MsgPageAdvertList, error) {
	out := new(MsgPageAdvertList)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/GetMsgPageAdvert", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) DelMsgPageAdvert(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/DelMsgPageAdvert", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) SetIosAuditInfo(ctx context.Context, in *SetIosAuditInfoReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/SetIosAuditInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) GetIosAuditInfo(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetIosAuditInfoResp, error) {
	out := new(GetIosAuditInfoResp)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/GetIosAuditInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) MoveCustomEntry(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/MoveCustomEntry", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) AddCommonEffectSource(ctx context.Context, in *CommonEffectSource, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/AddCommonEffectSource", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) GetCommonEffectSource(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*CommonEffectSourceList, error) {
	out := new(CommonEffectSourceList)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/GetCommonEffectSource", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) DelCommonEffectSource(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/DelCommonEffectSource", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) GetSyncConfigV2AllVersion(ctx context.Context, in *GetSyncConfigV2AllVersionReq, opts ...grpc.CallOption) (*GetSyncConfigV2AllVersionResp, error) {
	out := new(GetSyncConfigV2AllVersionResp)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/GetSyncConfigV2AllVersion", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) GetSyncConfigV2InfoByKeyList(ctx context.Context, in *GetSyncConfigV2InfoByKeyListReq, opts ...grpc.CallOption) (*GetSyncConfigV2InfoByKeyListResp, error) {
	out := new(GetSyncConfigV2InfoByKeyListResp)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/GetSyncConfigV2InfoByKeyList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) SetSyncConfigObj(ctx context.Context, in *ConfigRangeInfoList, opts ...grpc.CallOption) (*CodeResp, error) {
	out := new(CodeResp)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/SetSyncConfigObj", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) DelSyncConfigObj(ctx context.Context, in *StrReq, opts ...grpc.CallOption) (*CodeResp, error) {
	out := new(CodeResp)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/DelSyncConfigObj", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) GetSyncConfigObj(ctx context.Context, in *StrReq, opts ...grpc.CallOption) (*ConfigInfo, error) {
	out := new(ConfigInfo)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/GetSyncConfigObj", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) AddChannelBackground(ctx context.Context, in *ChannelBackground, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/AddChannelBackground", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) GetChannelBackground(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*ChannelBackgroundList, error) {
	out := new(ChannelBackgroundList)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/GetChannelBackground", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) RemoveChannelBackground(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/RemoveChannelBackground", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appConfigSvrClient) GetRankBlackUidList(ctx context.Context, in *GetRankBlackUidListReq, opts ...grpc.CallOption) (*GetRankBlackUidListResp, error) {
	out := new(GetRankBlackUidListResp)
	err := grpc.Invoke(ctx, "/appconfig.AppConfigSvr/GetRankBlackUidList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for AppConfigSvr service

type AppConfigSvrServer interface {
	SetGamePageTopLable(context.Context, *SetGamePageTopLableReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGamePageTopLable(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*GetGamePageTopLableResp, error)
	SetGamePageEnter(context.Context, *SetGamePageEnterReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGamePageEnter(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*GetGamePageEnterResp, error)
	SetGamePageAdvert(context.Context, *SetGamePageAdvertReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGamePageAdvert(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*GetGamePageAdvertResp, error)
	SetGamePageGrayUids(context.Context, *SetGamePageGrayUidsReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGamePageGrayUids(context.Context, *GetGamePageGrayUidsReq) (*GetGamePageGrayUidsResp, error)
	GetUpdateTime(context.Context, *GetUpdateTimeReq) (*GetUpdateTimeResp, error)
	GetAppConfigDetail(context.Context, *GetAppConfigReq) (*GetAppConfigDetailResp, error)
	SetAppConfigDetail(context.Context, *SetAppConfigDetailReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	AddCustomEntry(context.Context, *CustomEntryInfo) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetCustomEntry(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*CustomEntryList, error)
	RemoveCustomEntry(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*tlvpickle.SKBuiltinEmpty_PB, error)
	AddGameTabAd(context.Context, *GameTabAdEntry) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGameTabAd(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*GameTabAdEntry, error)
	RemoveGameTabAd(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*tlvpickle.SKBuiltinEmpty_PB, error)
	AddFloatLayer(context.Context, *FloatLayerEntry) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetFloatLayer(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*FloatLayerList, error)
	DelFloatLayer(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*tlvpickle.SKBuiltinEmpty_PB, error)
	AddMsgPageAdvert(context.Context, *MsgPageAdvertEntry) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetMsgPageAdvert(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*MsgPageAdvertList, error)
	DelMsgPageAdvert(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*tlvpickle.SKBuiltinEmpty_PB, error)
	SetIosAuditInfo(context.Context, *SetIosAuditInfoReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetIosAuditInfo(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*GetIosAuditInfoResp, error)
	MoveCustomEntry(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*tlvpickle.SKBuiltinEmpty_PB, error)
	AddCommonEffectSource(context.Context, *CommonEffectSource) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetCommonEffectSource(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*CommonEffectSourceList, error)
	DelCommonEffectSource(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取所有的 ConfigKEY 对应的 Config版本ID
	GetSyncConfigV2AllVersion(context.Context, *GetSyncConfigV2AllVersionReq) (*GetSyncConfigV2AllVersionResp, error)
	// 根据 ConfigKEY 获取configV2的数据
	GetSyncConfigV2InfoByKeyList(context.Context, *GetSyncConfigV2InfoByKeyListReq) (*GetSyncConfigV2InfoByKeyListResp, error)
	SetSyncConfigObj(context.Context, *ConfigRangeInfoList) (*CodeResp, error)
	DelSyncConfigObj(context.Context, *StrReq) (*CodeResp, error)
	GetSyncConfigObj(context.Context, *StrReq) (*ConfigInfo, error)
	AddChannelBackground(context.Context, *ChannelBackground) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetChannelBackground(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*ChannelBackgroundList, error)
	RemoveChannelBackground(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取当前 各种送礼/收礼活动通用的 排行榜黑名单用户列表
	GetRankBlackUidList(context.Context, *GetRankBlackUidListReq) (*GetRankBlackUidListResp, error)
}

func RegisterAppConfigSvrServer(s *grpc.Server, srv AppConfigSvrServer) {
	s.RegisterService(&_AppConfigSvr_serviceDesc, srv)
}

func _AppConfigSvr_SetGamePageTopLable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGamePageTopLableReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).SetGamePageTopLable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/SetGamePageTopLable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).SetGamePageTopLable(ctx, req.(*SetGamePageTopLableReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_GetGamePageTopLable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).GetGamePageTopLable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/GetGamePageTopLable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).GetGamePageTopLable(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_SetGamePageEnter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGamePageEnterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).SetGamePageEnter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/SetGamePageEnter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).SetGamePageEnter(ctx, req.(*SetGamePageEnterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_GetGamePageEnter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).GetGamePageEnter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/GetGamePageEnter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).GetGamePageEnter(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_SetGamePageAdvert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGamePageAdvertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).SetGamePageAdvert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/SetGamePageAdvert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).SetGamePageAdvert(ctx, req.(*SetGamePageAdvertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_GetGamePageAdvert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).GetGamePageAdvert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/GetGamePageAdvert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).GetGamePageAdvert(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_SetGamePageGrayUids_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGamePageGrayUidsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).SetGamePageGrayUids(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/SetGamePageGrayUids",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).SetGamePageGrayUids(ctx, req.(*SetGamePageGrayUidsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_GetGamePageGrayUids_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGamePageGrayUidsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).GetGamePageGrayUids(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/GetGamePageGrayUids",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).GetGamePageGrayUids(ctx, req.(*GetGamePageGrayUidsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_GetUpdateTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUpdateTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).GetUpdateTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/GetUpdateTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).GetUpdateTime(ctx, req.(*GetUpdateTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_GetAppConfigDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).GetAppConfigDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/GetAppConfigDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).GetAppConfigDetail(ctx, req.(*GetAppConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_SetAppConfigDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAppConfigDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).SetAppConfigDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/SetAppConfigDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).SetAppConfigDetail(ctx, req.(*SetAppConfigDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_AddCustomEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CustomEntryInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).AddCustomEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/AddCustomEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).AddCustomEntry(ctx, req.(*CustomEntryInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_GetCustomEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).GetCustomEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/GetCustomEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).GetCustomEntry(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_RemoveCustomEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).RemoveCustomEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/RemoveCustomEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).RemoveCustomEntry(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_AddGameTabAd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameTabAdEntry)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).AddGameTabAd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/AddGameTabAd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).AddGameTabAd(ctx, req.(*GameTabAdEntry))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_GetGameTabAd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).GetGameTabAd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/GetGameTabAd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).GetGameTabAd(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_RemoveGameTabAd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).RemoveGameTabAd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/RemoveGameTabAd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).RemoveGameTabAd(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_AddFloatLayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FloatLayerEntry)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).AddFloatLayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/AddFloatLayer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).AddFloatLayer(ctx, req.(*FloatLayerEntry))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_GetFloatLayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).GetFloatLayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/GetFloatLayer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).GetFloatLayer(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_DelFloatLayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).DelFloatLayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/DelFloatLayer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).DelFloatLayer(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_AddMsgPageAdvert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MsgPageAdvertEntry)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).AddMsgPageAdvert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/AddMsgPageAdvert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).AddMsgPageAdvert(ctx, req.(*MsgPageAdvertEntry))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_GetMsgPageAdvert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).GetMsgPageAdvert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/GetMsgPageAdvert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).GetMsgPageAdvert(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_DelMsgPageAdvert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).DelMsgPageAdvert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/DelMsgPageAdvert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).DelMsgPageAdvert(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_SetIosAuditInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetIosAuditInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).SetIosAuditInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/SetIosAuditInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).SetIosAuditInfo(ctx, req.(*SetIosAuditInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_GetIosAuditInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).GetIosAuditInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/GetIosAuditInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).GetIosAuditInfo(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_MoveCustomEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).MoveCustomEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/MoveCustomEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).MoveCustomEntry(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_AddCommonEffectSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonEffectSource)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).AddCommonEffectSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/AddCommonEffectSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).AddCommonEffectSource(ctx, req.(*CommonEffectSource))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_GetCommonEffectSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).GetCommonEffectSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/GetCommonEffectSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).GetCommonEffectSource(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_DelCommonEffectSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).DelCommonEffectSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/DelCommonEffectSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).DelCommonEffectSource(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_GetSyncConfigV2AllVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSyncConfigV2AllVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).GetSyncConfigV2AllVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/GetSyncConfigV2AllVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).GetSyncConfigV2AllVersion(ctx, req.(*GetSyncConfigV2AllVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_GetSyncConfigV2InfoByKeyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSyncConfigV2InfoByKeyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).GetSyncConfigV2InfoByKeyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/GetSyncConfigV2InfoByKeyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).GetSyncConfigV2InfoByKeyList(ctx, req.(*GetSyncConfigV2InfoByKeyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_SetSyncConfigObj_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfigRangeInfoList)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).SetSyncConfigObj(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/SetSyncConfigObj",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).SetSyncConfigObj(ctx, req.(*ConfigRangeInfoList))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_DelSyncConfigObj_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StrReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).DelSyncConfigObj(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/DelSyncConfigObj",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).DelSyncConfigObj(ctx, req.(*StrReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_GetSyncConfigObj_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StrReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).GetSyncConfigObj(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/GetSyncConfigObj",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).GetSyncConfigObj(ctx, req.(*StrReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_AddChannelBackground_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelBackground)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).AddChannelBackground(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/AddChannelBackground",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).AddChannelBackground(ctx, req.(*ChannelBackground))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_GetChannelBackground_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).GetChannelBackground(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/GetChannelBackground",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).GetChannelBackground(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_RemoveChannelBackground_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).RemoveChannelBackground(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/RemoveChannelBackground",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).RemoveChannelBackground(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppConfigSvr_GetRankBlackUidList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRankBlackUidListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigSvrServer).GetRankBlackUidList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/appconfig.AppConfigSvr/GetRankBlackUidList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigSvrServer).GetRankBlackUidList(ctx, req.(*GetRankBlackUidListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AppConfigSvr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "appconfig.AppConfigSvr",
	HandlerType: (*AppConfigSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetGamePageTopLable",
			Handler:    _AppConfigSvr_SetGamePageTopLable_Handler,
		},
		{
			MethodName: "GetGamePageTopLable",
			Handler:    _AppConfigSvr_GetGamePageTopLable_Handler,
		},
		{
			MethodName: "SetGamePageEnter",
			Handler:    _AppConfigSvr_SetGamePageEnter_Handler,
		},
		{
			MethodName: "GetGamePageEnter",
			Handler:    _AppConfigSvr_GetGamePageEnter_Handler,
		},
		{
			MethodName: "SetGamePageAdvert",
			Handler:    _AppConfigSvr_SetGamePageAdvert_Handler,
		},
		{
			MethodName: "GetGamePageAdvert",
			Handler:    _AppConfigSvr_GetGamePageAdvert_Handler,
		},
		{
			MethodName: "SetGamePageGrayUids",
			Handler:    _AppConfigSvr_SetGamePageGrayUids_Handler,
		},
		{
			MethodName: "GetGamePageGrayUids",
			Handler:    _AppConfigSvr_GetGamePageGrayUids_Handler,
		},
		{
			MethodName: "GetUpdateTime",
			Handler:    _AppConfigSvr_GetUpdateTime_Handler,
		},
		{
			MethodName: "GetAppConfigDetail",
			Handler:    _AppConfigSvr_GetAppConfigDetail_Handler,
		},
		{
			MethodName: "SetAppConfigDetail",
			Handler:    _AppConfigSvr_SetAppConfigDetail_Handler,
		},
		{
			MethodName: "AddCustomEntry",
			Handler:    _AppConfigSvr_AddCustomEntry_Handler,
		},
		{
			MethodName: "GetCustomEntry",
			Handler:    _AppConfigSvr_GetCustomEntry_Handler,
		},
		{
			MethodName: "RemoveCustomEntry",
			Handler:    _AppConfigSvr_RemoveCustomEntry_Handler,
		},
		{
			MethodName: "AddGameTabAd",
			Handler:    _AppConfigSvr_AddGameTabAd_Handler,
		},
		{
			MethodName: "GetGameTabAd",
			Handler:    _AppConfigSvr_GetGameTabAd_Handler,
		},
		{
			MethodName: "RemoveGameTabAd",
			Handler:    _AppConfigSvr_RemoveGameTabAd_Handler,
		},
		{
			MethodName: "AddFloatLayer",
			Handler:    _AppConfigSvr_AddFloatLayer_Handler,
		},
		{
			MethodName: "GetFloatLayer",
			Handler:    _AppConfigSvr_GetFloatLayer_Handler,
		},
		{
			MethodName: "DelFloatLayer",
			Handler:    _AppConfigSvr_DelFloatLayer_Handler,
		},
		{
			MethodName: "AddMsgPageAdvert",
			Handler:    _AppConfigSvr_AddMsgPageAdvert_Handler,
		},
		{
			MethodName: "GetMsgPageAdvert",
			Handler:    _AppConfigSvr_GetMsgPageAdvert_Handler,
		},
		{
			MethodName: "DelMsgPageAdvert",
			Handler:    _AppConfigSvr_DelMsgPageAdvert_Handler,
		},
		{
			MethodName: "SetIosAuditInfo",
			Handler:    _AppConfigSvr_SetIosAuditInfo_Handler,
		},
		{
			MethodName: "GetIosAuditInfo",
			Handler:    _AppConfigSvr_GetIosAuditInfo_Handler,
		},
		{
			MethodName: "MoveCustomEntry",
			Handler:    _AppConfigSvr_MoveCustomEntry_Handler,
		},
		{
			MethodName: "AddCommonEffectSource",
			Handler:    _AppConfigSvr_AddCommonEffectSource_Handler,
		},
		{
			MethodName: "GetCommonEffectSource",
			Handler:    _AppConfigSvr_GetCommonEffectSource_Handler,
		},
		{
			MethodName: "DelCommonEffectSource",
			Handler:    _AppConfigSvr_DelCommonEffectSource_Handler,
		},
		{
			MethodName: "GetSyncConfigV2AllVersion",
			Handler:    _AppConfigSvr_GetSyncConfigV2AllVersion_Handler,
		},
		{
			MethodName: "GetSyncConfigV2InfoByKeyList",
			Handler:    _AppConfigSvr_GetSyncConfigV2InfoByKeyList_Handler,
		},
		{
			MethodName: "SetSyncConfigObj",
			Handler:    _AppConfigSvr_SetSyncConfigObj_Handler,
		},
		{
			MethodName: "DelSyncConfigObj",
			Handler:    _AppConfigSvr_DelSyncConfigObj_Handler,
		},
		{
			MethodName: "GetSyncConfigObj",
			Handler:    _AppConfigSvr_GetSyncConfigObj_Handler,
		},
		{
			MethodName: "AddChannelBackground",
			Handler:    _AppConfigSvr_AddChannelBackground_Handler,
		},
		{
			MethodName: "GetChannelBackground",
			Handler:    _AppConfigSvr_GetChannelBackground_Handler,
		},
		{
			MethodName: "RemoveChannelBackground",
			Handler:    _AppConfigSvr_RemoveChannelBackground_Handler,
		},
		{
			MethodName: "GetRankBlackUidList",
			Handler:    _AppConfigSvr_GetRankBlackUidList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/appconfig/appconfigsvr.proto",
}

func (m *GamePageAdvert) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GamePageAdvert) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x12
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Icon)))
	i += copy(dAtA[i:], m.Icon)
	return i, nil
}

func (m *SetGamePageTopLableReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetGamePageTopLableReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TopLableList) > 0 {
		for _, msg := range m.TopLableList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGamePageTopLableResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGamePageTopLableResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TopLableList) > 0 {
		for _, msg := range m.TopLableList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetGamePageEnterReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetGamePageEnterReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.EnterList) > 0 {
		for _, msg := range m.EnterList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGamePageEnterResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGamePageEnterResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.EnterList) > 0 {
		for _, msg := range m.EnterList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetGamePageAdvertReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetGamePageAdvertReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MainAdvList) > 0 {
		for _, msg := range m.MainAdvList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.ViceAdvList) > 0 {
		for _, msg := range m.ViceAdvList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGamePageAdvertResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGamePageAdvertResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MainAdvList) > 0 {
		for _, msg := range m.MainAdvList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.ViceAdvList) > 0 {
		for _, msg := range m.ViceAdvList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetGamePageGrayUidsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetGamePageGrayUidsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Uids)))
	i += copy(dAtA[i:], m.Uids)
	dAtA[i] = 0x10
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.GType))
	return i, nil
}

func (m *GetGamePageGrayUidsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGamePageGrayUidsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.GType))
	return i, nil
}

func (m *GetGamePageGrayUidsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGamePageGrayUidsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Uids)))
	i += copy(dAtA[i:], m.Uids)
	return i, nil
}

func (m *GetUpdateTimeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUpdateTimeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.AdvertType))
	return i, nil
}

func (m *GetUpdateTimeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUpdateTimeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.UpdateTime))
	return i, nil
}

func (m *RaidRichesConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaidRichesConfig) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x12
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.SubTitle)))
	i += copy(dAtA[i:], m.SubTitle)
	dAtA[i] = 0x20
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x28
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.EndTime))
	if len(m.UidSuffix) > 0 {
		for _, num := range m.UidSuffix {
			dAtA[i] = 0x30
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *CustomEntryInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CustomEntryInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x12
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.SubTitle)))
	i += copy(dAtA[i:], m.SubTitle)
	dAtA[i] = 0x20
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x28
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.EndTime))
	if len(m.UidSuffix) > 0 {
		for _, num := range m.UidSuffix {
			dAtA[i] = 0x30
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x3a
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Icon)))
	i += copy(dAtA[i:], m.Icon)
	dAtA[i] = 0x40
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.ClientType))
	dAtA[i] = 0x48
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.EntryType))
	dAtA[i] = 0x52
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.AndroidVersion)))
	i += copy(dAtA[i:], m.AndroidVersion)
	dAtA[i] = 0x5a
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.IosVersion)))
	i += copy(dAtA[i:], m.IosVersion)
	return i, nil
}

func (m *CustomEntryList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CustomEntryList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.EntryList) > 0 {
		for _, msg := range m.EntryList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GameTabAdEntry) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameTabAdEntry) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.ActId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.ActUrl)))
	i += copy(dAtA[i:], m.ActUrl)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.GameTabImg)))
	i += copy(dAtA[i:], m.GameTabImg)
	dAtA[i] = 0x22
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.GuildMgroupImg)))
	i += copy(dAtA[i:], m.GuildMgroupImg)
	dAtA[i] = 0x28
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x30
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x38
	i++
	if m.IsValid {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GameTabAdList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameTabAdList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.EntryList) > 0 {
		for _, msg := range m.EntryList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ChannelBackground) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelBackground) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.BackgroundUrl)))
	i += copy(dAtA[i:], m.BackgroundUrl)
	dAtA[i] = 0x10
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.EndTime))
	return i, nil
}

func (m *ChannelBackgroundList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelBackgroundList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.BackgroundList) > 0 {
		for _, msg := range m.BackgroundList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *RushInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RushInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.RushMaxRandTs))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.RushWaitTs))
	return i, nil
}

func (m *FloatLayerEntry) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FloatLayerEntry) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Img)))
	i += copy(dAtA[i:], m.Img)
	dAtA[i] = 0x12
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x18
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x20
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x28
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x30
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.DotType))
	dAtA[i] = 0x38
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.DotValue))
	dAtA[i] = 0x42
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.TopImg)))
	i += copy(dAtA[i:], m.TopImg)
	dAtA[i] = 0x48
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x50
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.MarketId))
	dAtA[i] = 0x58
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.Platform))
	dAtA[i] = 0x60
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.DisplayChannelType))
	if m.RushInfo != nil {
		dAtA[i] = 0x6a
		i++
		i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.RushInfo.Size()))
		n1, err := m.RushInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *FloatLayerList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FloatLayerList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.EntryList) > 0 {
		for _, msg := range m.EntryList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *MsgPageAdvertEntry) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MsgPageAdvertEntry) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Img)))
	i += copy(dAtA[i:], m.Img)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x22
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Text)))
	i += copy(dAtA[i:], m.Text)
	dAtA[i] = 0x28
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x30
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x38
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.Platform))
	if len(m.IncludeChannel) > 0 {
		for _, s := range m.IncludeChannel {
			dAtA[i] = 0x42
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.ExcludeChannel) > 0 {
		for _, s := range m.ExcludeChannel {
			dAtA[i] = 0x4a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x50
	i++
	if m.IsNewbieEntry {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x58
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x60
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.MarketId))
	return i, nil
}

func (m *MsgPageAdvertList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MsgPageAdvertList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.EntryList) > 0 {
		for _, msg := range m.EntryList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetAppConfigReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAppConfigReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.AdvertType))
	return i, nil
}

func (m *GetAppConfigDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAppConfigDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Detail != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Detail)))
		i += copy(dAtA[i:], m.Detail)
	}
	return i, nil
}

func (m *SetAppConfigDetailReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetAppConfigDetailReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.AdvertType))
	if m.Detail != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Detail)))
		i += copy(dAtA[i:], m.Detail)
	}
	return i, nil
}

func (m *CommonEffectSource) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommonEffectSource) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x12
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Md5)))
	i += copy(dAtA[i:], m.Md5)
	dAtA[i] = 0x20
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.Id))
	return i, nil
}

func (m *CommonEffectSourceList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommonEffectSourceList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.EntryList) > 0 {
		for _, msg := range m.EntryList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ClientAuditInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClientAuditInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.ClientType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.ClientVersion))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.AuditStatus))
	return i, nil
}

func (m *SetIosAuditInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetIosAuditInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.ClientVersion))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.AuditStatus))
	return i, nil
}

func (m *GetIosAuditInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIosAuditInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.AuditInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("audit_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.AuditInfo.Size()))
		n2, err := m.AuditInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *ConfigKV) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConfigKV) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	dAtA[i] = 0x12
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.Value)))
	i += copy(dAtA[i:], m.Value)
	return i, nil
}

func (m *ConfigData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConfigData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ConfigKvList) > 0 {
		for _, msg := range m.ConfigKvList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *MainPageTagConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MainPageTagConfig) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ConfigKvList) > 0 {
		for _, msg := range m.ConfigKvList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.ExpirationTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.BeginTime))
	return i, nil
}

func (m *ConfigInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConfigInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.ConfigType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.ConfigVersion))
	if m.ConfigData != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintAppconfigsvr(dAtA, i, uint64(len(m.ConfigData)))
		i += copy(dAtA[i:], m.ConfigData)
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x28
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.ExpirationTime))
	return i, nil
}

func (m *ConfigRangeInfoList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConfigRangeInfoList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.ConfigType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.ConfigVersion))
	if len(m.ConfigInfoList) > 0 {
		for _, msg := range m.ConfigInfoList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetSyncConfigV2AllVersionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSyncConfigV2AllVersionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetSyncConfigV2AllVersionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSyncConfigV2AllVersionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.VersionList) > 0 {
		for _, msg := range m.VersionList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetSyncConfigV2InfoByKeyListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSyncConfigV2InfoByKeyListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ConfigTypeList) > 0 {
		for _, num := range m.ConfigTypeList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetSyncConfigV2InfoByKeyListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSyncConfigV2InfoByKeyListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ConfigInfoList) > 0 {
		for _, msg := range m.ConfigInfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *StrReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StrReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.ConfigType))
	return i, nil
}

func (m *CodeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CodeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAppconfigsvr(dAtA, i, uint64(m.Code))
	return i, nil
}

func (m *GetRankBlackUidListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRankBlackUidListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetRankBlackUidListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRankBlackUidListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RecvRankUidList) > 0 {
		for _, num := range m.RecvRankUidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(num))
		}
	}
	if len(m.SendRankUidList) > 0 {
		for _, num := range m.SendRankUidList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintAppconfigsvr(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func encodeFixed64Appconfigsvr(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Appconfigsvr(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintAppconfigsvr(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GamePageAdvert) Size() (n int) {
	var l int
	_ = l
	l = len(m.Name)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	l = len(m.Url)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	l = len(m.Icon)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	return n
}

func (m *SetGamePageTopLableReq) Size() (n int) {
	var l int
	_ = l
	if len(m.TopLableList) > 0 {
		for _, e := range m.TopLableList {
			l = e.Size()
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	return n
}

func (m *GetGamePageTopLableResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TopLableList) > 0 {
		for _, e := range m.TopLableList {
			l = e.Size()
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	return n
}

func (m *SetGamePageEnterReq) Size() (n int) {
	var l int
	_ = l
	if len(m.EnterList) > 0 {
		for _, e := range m.EnterList {
			l = e.Size()
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	return n
}

func (m *GetGamePageEnterResp) Size() (n int) {
	var l int
	_ = l
	if len(m.EnterList) > 0 {
		for _, e := range m.EnterList {
			l = e.Size()
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	return n
}

func (m *SetGamePageAdvertReq) Size() (n int) {
	var l int
	_ = l
	if len(m.MainAdvList) > 0 {
		for _, e := range m.MainAdvList {
			l = e.Size()
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	if len(m.ViceAdvList) > 0 {
		for _, e := range m.ViceAdvList {
			l = e.Size()
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	return n
}

func (m *GetGamePageAdvertResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MainAdvList) > 0 {
		for _, e := range m.MainAdvList {
			l = e.Size()
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	if len(m.ViceAdvList) > 0 {
		for _, e := range m.ViceAdvList {
			l = e.Size()
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	return n
}

func (m *SetGamePageGrayUidsReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Uids)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	n += 1 + sovAppconfigsvr(uint64(m.GType))
	return n
}

func (m *GetGamePageGrayUidsReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAppconfigsvr(uint64(m.GType))
	return n
}

func (m *GetGamePageGrayUidsResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.Uids)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	return n
}

func (m *GetUpdateTimeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAppconfigsvr(uint64(m.AdvertType))
	return n
}

func (m *GetUpdateTimeResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAppconfigsvr(uint64(m.UpdateTime))
	return n
}

func (m *RaidRichesConfig) Size() (n int) {
	var l int
	_ = l
	l = len(m.Url)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	l = len(m.Title)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	l = len(m.SubTitle)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	n += 1 + sovAppconfigsvr(uint64(m.BeginTime))
	n += 1 + sovAppconfigsvr(uint64(m.EndTime))
	if len(m.UidSuffix) > 0 {
		for _, e := range m.UidSuffix {
			n += 1 + sovAppconfigsvr(uint64(e))
		}
	}
	return n
}

func (m *CustomEntryInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.Url)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	l = len(m.Title)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	l = len(m.SubTitle)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	n += 1 + sovAppconfigsvr(uint64(m.BeginTime))
	n += 1 + sovAppconfigsvr(uint64(m.EndTime))
	if len(m.UidSuffix) > 0 {
		for _, e := range m.UidSuffix {
			n += 1 + sovAppconfigsvr(uint64(e))
		}
	}
	l = len(m.Icon)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	n += 1 + sovAppconfigsvr(uint64(m.ClientType))
	n += 1 + sovAppconfigsvr(uint64(m.EntryType))
	l = len(m.AndroidVersion)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	l = len(m.IosVersion)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	return n
}

func (m *CustomEntryList) Size() (n int) {
	var l int
	_ = l
	if len(m.EntryList) > 0 {
		for _, e := range m.EntryList {
			l = e.Size()
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	return n
}

func (m *GameTabAdEntry) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAppconfigsvr(uint64(m.ActId))
	l = len(m.ActUrl)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	l = len(m.GameTabImg)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	l = len(m.GuildMgroupImg)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	n += 1 + sovAppconfigsvr(uint64(m.BeginTime))
	n += 1 + sovAppconfigsvr(uint64(m.EndTime))
	n += 2
	return n
}

func (m *GameTabAdList) Size() (n int) {
	var l int
	_ = l
	if len(m.EntryList) > 0 {
		for _, e := range m.EntryList {
			l = e.Size()
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	return n
}

func (m *ChannelBackground) Size() (n int) {
	var l int
	_ = l
	l = len(m.BackgroundUrl)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	n += 1 + sovAppconfigsvr(uint64(m.BeginTime))
	n += 1 + sovAppconfigsvr(uint64(m.EndTime))
	return n
}

func (m *ChannelBackgroundList) Size() (n int) {
	var l int
	_ = l
	if len(m.BackgroundList) > 0 {
		for _, e := range m.BackgroundList {
			l = e.Size()
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	return n
}

func (m *RushInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAppconfigsvr(uint64(m.Type))
	n += 1 + sovAppconfigsvr(uint64(m.RushMaxRandTs))
	n += 1 + sovAppconfigsvr(uint64(m.RushWaitTs))
	return n
}

func (m *FloatLayerEntry) Size() (n int) {
	var l int
	_ = l
	l = len(m.Img)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	l = len(m.Url)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	n += 1 + sovAppconfigsvr(uint64(m.Type))
	n += 1 + sovAppconfigsvr(uint64(m.BeginTime))
	n += 1 + sovAppconfigsvr(uint64(m.EndTime))
	n += 1 + sovAppconfigsvr(uint64(m.DotType))
	n += 1 + sovAppconfigsvr(uint64(m.DotValue))
	l = len(m.TopImg)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	n += 1 + sovAppconfigsvr(uint64(m.AppId))
	n += 1 + sovAppconfigsvr(uint64(m.MarketId))
	n += 1 + sovAppconfigsvr(uint64(m.Platform))
	n += 1 + sovAppconfigsvr(uint64(m.DisplayChannelType))
	if m.RushInfo != nil {
		l = m.RushInfo.Size()
		n += 1 + l + sovAppconfigsvr(uint64(l))
	}
	return n
}

func (m *FloatLayerList) Size() (n int) {
	var l int
	_ = l
	if len(m.EntryList) > 0 {
		for _, e := range m.EntryList {
			l = e.Size()
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	return n
}

func (m *MsgPageAdvertEntry) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAppconfigsvr(uint64(m.Id))
	l = len(m.Img)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	l = len(m.Url)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	l = len(m.Text)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	n += 1 + sovAppconfigsvr(uint64(m.BeginTime))
	n += 1 + sovAppconfigsvr(uint64(m.EndTime))
	n += 1 + sovAppconfigsvr(uint64(m.Platform))
	if len(m.IncludeChannel) > 0 {
		for _, s := range m.IncludeChannel {
			l = len(s)
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	if len(m.ExcludeChannel) > 0 {
		for _, s := range m.ExcludeChannel {
			l = len(s)
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	n += 2
	n += 1 + sovAppconfigsvr(uint64(m.AppId))
	n += 1 + sovAppconfigsvr(uint64(m.MarketId))
	return n
}

func (m *MsgPageAdvertList) Size() (n int) {
	var l int
	_ = l
	if len(m.EntryList) > 0 {
		for _, e := range m.EntryList {
			l = e.Size()
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	return n
}

func (m *GetAppConfigReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAppconfigsvr(uint64(m.AdvertType))
	return n
}

func (m *GetAppConfigDetailResp) Size() (n int) {
	var l int
	_ = l
	if m.Detail != nil {
		l = len(m.Detail)
		n += 1 + l + sovAppconfigsvr(uint64(l))
	}
	return n
}

func (m *SetAppConfigDetailReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAppconfigsvr(uint64(m.AdvertType))
	if m.Detail != nil {
		l = len(m.Detail)
		n += 1 + l + sovAppconfigsvr(uint64(l))
	}
	return n
}

func (m *CommonEffectSource) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAppconfigsvr(uint64(m.Type))
	l = len(m.Url)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	l = len(m.Md5)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	n += 1 + sovAppconfigsvr(uint64(m.Id))
	return n
}

func (m *CommonEffectSourceList) Size() (n int) {
	var l int
	_ = l
	if len(m.EntryList) > 0 {
		for _, e := range m.EntryList {
			l = e.Size()
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	return n
}

func (m *ClientAuditInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAppconfigsvr(uint64(m.ClientType))
	n += 1 + sovAppconfigsvr(uint64(m.ClientVersion))
	n += 1 + sovAppconfigsvr(uint64(m.AuditStatus))
	return n
}

func (m *SetIosAuditInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAppconfigsvr(uint64(m.ClientVersion))
	n += 1 + sovAppconfigsvr(uint64(m.AuditStatus))
	return n
}

func (m *GetIosAuditInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.AuditInfo != nil {
		l = m.AuditInfo.Size()
		n += 1 + l + sovAppconfigsvr(uint64(l))
	}
	return n
}

func (m *ConfigKV) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	l = len(m.Value)
	n += 1 + l + sovAppconfigsvr(uint64(l))
	return n
}

func (m *ConfigData) Size() (n int) {
	var l int
	_ = l
	if len(m.ConfigKvList) > 0 {
		for _, e := range m.ConfigKvList {
			l = e.Size()
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	return n
}

func (m *MainPageTagConfig) Size() (n int) {
	var l int
	_ = l
	if len(m.ConfigKvList) > 0 {
		for _, e := range m.ConfigKvList {
			l = e.Size()
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	n += 1 + sovAppconfigsvr(uint64(m.ExpirationTime))
	n += 1 + sovAppconfigsvr(uint64(m.BeginTime))
	return n
}

func (m *ConfigInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAppconfigsvr(uint64(m.ConfigType))
	n += 1 + sovAppconfigsvr(uint64(m.ConfigVersion))
	if m.ConfigData != nil {
		l = len(m.ConfigData)
		n += 1 + l + sovAppconfigsvr(uint64(l))
	}
	n += 1 + sovAppconfigsvr(uint64(m.BeginTime))
	n += 1 + sovAppconfigsvr(uint64(m.ExpirationTime))
	return n
}

func (m *ConfigRangeInfoList) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAppconfigsvr(uint64(m.ConfigType))
	n += 1 + sovAppconfigsvr(uint64(m.ConfigVersion))
	if len(m.ConfigInfoList) > 0 {
		for _, e := range m.ConfigInfoList {
			l = e.Size()
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	return n
}

func (m *GetSyncConfigV2AllVersionReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetSyncConfigV2AllVersionResp) Size() (n int) {
	var l int
	_ = l
	if len(m.VersionList) > 0 {
		for _, e := range m.VersionList {
			l = e.Size()
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	return n
}

func (m *GetSyncConfigV2InfoByKeyListReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ConfigTypeList) > 0 {
		for _, e := range m.ConfigTypeList {
			n += 1 + sovAppconfigsvr(uint64(e))
		}
	}
	return n
}

func (m *GetSyncConfigV2InfoByKeyListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ConfigInfoList) > 0 {
		for _, e := range m.ConfigInfoList {
			l = e.Size()
			n += 1 + l + sovAppconfigsvr(uint64(l))
		}
	}
	return n
}

func (m *StrReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAppconfigsvr(uint64(m.ConfigType))
	return n
}

func (m *CodeResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAppconfigsvr(uint64(m.Code))
	return n
}

func (m *GetRankBlackUidListReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetRankBlackUidListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RecvRankUidList) > 0 {
		for _, e := range m.RecvRankUidList {
			n += 1 + sovAppconfigsvr(uint64(e))
		}
	}
	if len(m.SendRankUidList) > 0 {
		for _, e := range m.SendRankUidList {
			n += 1 + sovAppconfigsvr(uint64(e))
		}
	}
	return n
}

func sovAppconfigsvr(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozAppconfigsvr(x uint64) (n int) {
	return sovAppconfigsvr(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *GamePageAdvert) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GamePageAdvert: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GamePageAdvert: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Icon", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Icon = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetGamePageTopLableReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetGamePageTopLableReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetGamePageTopLableReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopLableList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopLableList = append(m.TopLableList, &GamePageAdvert{})
			if err := m.TopLableList[len(m.TopLableList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGamePageTopLableResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGamePageTopLableResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGamePageTopLableResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopLableList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopLableList = append(m.TopLableList, &GamePageAdvert{})
			if err := m.TopLableList[len(m.TopLableList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetGamePageEnterReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetGamePageEnterReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetGamePageEnterReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EnterList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EnterList = append(m.EnterList, &GamePageAdvert{})
			if err := m.EnterList[len(m.EnterList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGamePageEnterResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGamePageEnterResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGamePageEnterResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EnterList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EnterList = append(m.EnterList, &GamePageAdvert{})
			if err := m.EnterList[len(m.EnterList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetGamePageAdvertReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetGamePageAdvertReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetGamePageAdvertReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MainAdvList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MainAdvList = append(m.MainAdvList, &GamePageAdvert{})
			if err := m.MainAdvList[len(m.MainAdvList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ViceAdvList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ViceAdvList = append(m.ViceAdvList, &GamePageAdvert{})
			if err := m.ViceAdvList[len(m.ViceAdvList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGamePageAdvertResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGamePageAdvertResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGamePageAdvertResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MainAdvList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MainAdvList = append(m.MainAdvList, &GamePageAdvert{})
			if err := m.MainAdvList[len(m.MainAdvList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ViceAdvList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ViceAdvList = append(m.ViceAdvList, &GamePageAdvert{})
			if err := m.ViceAdvList[len(m.ViceAdvList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetGamePageGrayUidsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetGamePageGrayUidsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetGamePageGrayUidsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uids", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Uids = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GType", wireType)
			}
			m.GType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GType |= (AdvertType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uids")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("g_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGamePageGrayUidsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGamePageGrayUidsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGamePageGrayUidsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GType", wireType)
			}
			m.GType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GType |= (AdvertType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("g_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGamePageGrayUidsResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGamePageGrayUidsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGamePageGrayUidsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uids", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Uids = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uids")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUpdateTimeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUpdateTimeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUpdateTimeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdvertType", wireType)
			}
			m.AdvertType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AdvertType |= (AdvertType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("advert_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUpdateTimeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUpdateTimeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUpdateTimeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("update_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaidRichesConfig) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RaidRichesConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RaidRichesConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubTitle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SubTitle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAppconfigsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidSuffix = append(m.UidSuffix, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAppconfigsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAppconfigsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowAppconfigsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidSuffix = append(m.UidSuffix, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidSuffix", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CustomEntryInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CustomEntryInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CustomEntryInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubTitle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SubTitle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAppconfigsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidSuffix = append(m.UidSuffix, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAppconfigsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAppconfigsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowAppconfigsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidSuffix = append(m.UidSuffix, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidSuffix", wireType)
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Icon", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Icon = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientType", wireType)
			}
			m.ClientType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryType", wireType)
			}
			m.EntryType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EntryType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AndroidVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AndroidVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IosVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IosVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CustomEntryList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CustomEntryList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CustomEntryList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryList = append(m.EntryList, &CustomEntryInfo{})
			if err := m.EntryList[len(m.EntryList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameTabAdEntry) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameTabAdEntry: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameTabAdEntry: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActId", wireType)
			}
			m.ActId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameTabImg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameTabImg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildMgroupImg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GuildMgroupImg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsValid", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsValid = bool(v != 0)
			hasFields[0] |= uint64(0x00000040)
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("act_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("act_url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_tab_img")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_mgroup_img")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin_time")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_valid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameTabAdList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameTabAdList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameTabAdList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryList = append(m.EntryList, &GameTabAdEntry{})
			if err := m.EntryList[len(m.EntryList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelBackground) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelBackground: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelBackground: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BackgroundUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BackgroundUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("background_url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelBackgroundList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelBackgroundList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelBackgroundList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BackgroundList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BackgroundList = append(m.BackgroundList, &ChannelBackground{})
			if err := m.BackgroundList[len(m.BackgroundList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RushInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RushInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RushInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RushMaxRandTs", wireType)
			}
			m.RushMaxRandTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RushMaxRandTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RushWaitTs", wireType)
			}
			m.RushWaitTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RushWaitTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FloatLayerEntry) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FloatLayerEntry: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FloatLayerEntry: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Img", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Img = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DotType", wireType)
			}
			m.DotType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DotType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DotValue", wireType)
			}
			m.DotValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DotValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopImg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopImg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DisplayChannelType", wireType)
			}
			m.DisplayChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DisplayChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RushInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RushInfo == nil {
				m.RushInfo = &RushInfo{}
			}
			if err := m.RushInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("img")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin_time")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FloatLayerList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FloatLayerList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FloatLayerList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryList = append(m.EntryList, &FloatLayerEntry{})
			if err := m.EntryList[len(m.EntryList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MsgPageAdvertEntry) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MsgPageAdvertEntry: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MsgPageAdvertEntry: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Img", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Img = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Text", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Text = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IncludeChannel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IncludeChannel = append(m.IncludeChannel, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExcludeChannel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExcludeChannel = append(m.ExcludeChannel, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsNewbieEntry", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNewbieEntry = bool(v != 0)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("img")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("url")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("text")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin_time")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MsgPageAdvertList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MsgPageAdvertList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MsgPageAdvertList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryList = append(m.EntryList, &MsgPageAdvertEntry{})
			if err := m.EntryList[len(m.EntryList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAppConfigReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAppConfigReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAppConfigReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdvertType", wireType)
			}
			m.AdvertType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AdvertType |= (AdvertType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("advert_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAppConfigDetailResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAppConfigDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAppConfigDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Detail", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Detail = append(m.Detail[:0], dAtA[iNdEx:postIndex]...)
			if m.Detail == nil {
				m.Detail = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("detail")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetAppConfigDetailReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetAppConfigDetailReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetAppConfigDetailReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdvertType", wireType)
			}
			m.AdvertType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AdvertType |= (AdvertType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Detail", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Detail = append(m.Detail[:0], dAtA[iNdEx:postIndex]...)
			if m.Detail == nil {
				m.Detail = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("advert_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("detail")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommonEffectSource) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CommonEffectSource: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CommonEffectSource: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Md5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Md5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("md5")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommonEffectSourceList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CommonEffectSourceList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CommonEffectSourceList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryList = append(m.EntryList, &CommonEffectSource{})
			if err := m.EntryList[len(m.EntryList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClientAuditInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ClientAuditInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ClientAuditInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientType", wireType)
			}
			m.ClientType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientVersion", wireType)
			}
			m.ClientVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuditStatus", wireType)
			}
			m.AuditStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AuditStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("client_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("client_version")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("audit_status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetIosAuditInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetIosAuditInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetIosAuditInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientVersion", wireType)
			}
			m.ClientVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuditStatus", wireType)
			}
			m.AuditStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AuditStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("client_version")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("audit_status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetIosAuditInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetIosAuditInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetIosAuditInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuditInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.AuditInfo == nil {
				m.AuditInfo = &ClientAuditInfo{}
			}
			if err := m.AuditInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("audit_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConfigKV) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConfigKV: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConfigKV: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Value = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("key")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("value")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConfigData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConfigData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConfigData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConfigKvList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ConfigKvList = append(m.ConfigKvList, &ConfigKV{})
			if err := m.ConfigKvList[len(m.ConfigKvList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MainPageTagConfig) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MainPageTagConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MainPageTagConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConfigKvList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ConfigKvList = append(m.ConfigKvList, &ConfigKV{})
			if err := m.ConfigKvList[len(m.ConfigKvList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpirationTime", wireType)
			}
			m.ExpirationTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpirationTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("expiration_time")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConfigInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConfigInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConfigInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConfigType", wireType)
			}
			m.ConfigType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConfigType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConfigVersion", wireType)
			}
			m.ConfigVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConfigVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConfigData", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ConfigData = append(m.ConfigData[:0], dAtA[iNdEx:postIndex]...)
			if m.ConfigData == nil {
				m.ConfigData = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpirationTime", wireType)
			}
			m.ExpirationTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpirationTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("config_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("config_version")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("config_data")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin_time")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("expiration_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConfigRangeInfoList) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConfigRangeInfoList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConfigRangeInfoList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConfigType", wireType)
			}
			m.ConfigType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConfigType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConfigVersion", wireType)
			}
			m.ConfigVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConfigVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConfigInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ConfigInfoList = append(m.ConfigInfoList, &ConfigInfo{})
			if err := m.ConfigInfoList[len(m.ConfigInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("config_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("config_version")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSyncConfigV2AllVersionReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSyncConfigV2AllVersionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSyncConfigV2AllVersionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSyncConfigV2AllVersionResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSyncConfigV2AllVersionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSyncConfigV2AllVersionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VersionList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VersionList = append(m.VersionList, &ConfigKV{})
			if err := m.VersionList[len(m.VersionList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSyncConfigV2InfoByKeyListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSyncConfigV2InfoByKeyListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSyncConfigV2InfoByKeyListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAppconfigsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ConfigTypeList = append(m.ConfigTypeList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAppconfigsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAppconfigsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowAppconfigsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ConfigTypeList = append(m.ConfigTypeList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConfigTypeList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSyncConfigV2InfoByKeyListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSyncConfigV2InfoByKeyListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSyncConfigV2InfoByKeyListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConfigInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ConfigInfoList = append(m.ConfigInfoList, &ConfigInfo{})
			if err := m.ConfigInfoList[len(m.ConfigInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StrReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StrReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StrReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConfigType", wireType)
			}
			m.ConfigType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConfigType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("config_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CodeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CodeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CodeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("code")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRankBlackUidListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRankBlackUidListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRankBlackUidListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRankBlackUidListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRankBlackUidListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRankBlackUidListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAppconfigsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.RecvRankUidList = append(m.RecvRankUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAppconfigsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAppconfigsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowAppconfigsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.RecvRankUidList = append(m.RecvRankUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecvRankUidList", wireType)
			}
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAppconfigsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.SendRankUidList = append(m.SendRankUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAppconfigsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAppconfigsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowAppconfigsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.SendRankUidList = append(m.SendRankUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendRankUidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAppconfigsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAppconfigsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipAppconfigsvr(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowAppconfigsvr
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAppconfigsvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthAppconfigsvr
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowAppconfigsvr
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipAppconfigsvr(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthAppconfigsvr = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowAppconfigsvr   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/appconfig/appconfigsvr.proto", fileDescriptorAppconfigsvr) }

var fileDescriptorAppconfigsvr = []byte{
	// 3324 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x5a, 0xdd, 0x6f, 0x1b, 0xc7,
	0xb5, 0xf7, 0x92, 0xfa, 0x20, 0x8f, 0x28, 0x92, 0x5a, 0x4b, 0x32, 0x2d, 0xdb, 0xd2, 0x7a, 0xe3,
	0x24, 0x8a, 0x6d, 0xca, 0xb6, 0x0c, 0xfb, 0xc6, 0x04, 0x2f, 0x73, 0x25, 0x5b, 0x51, 0x04, 0x5b,
	0xb6, 0x2f, 0x45, 0x29, 0xd7, 0x37, 0x37, 0x77, 0x33, 0xe2, 0x8e, 0xe8, 0x09, 0xf7, 0xcb, 0xdc,
	0x25, 0x2d, 0xa2, 0x0f, 0x29, 0x50, 0xa0, 0x48, 0x5b, 0xb4, 0x28, 0x9a, 0xb6, 0x40, 0x8b, 0x02,
	0x45, 0x8b, 0x3c, 0x15, 0xe8, 0x73, 0x81, 0xbe, 0xf5, 0x2d, 0x4f, 0x41, 0x5e, 0xfa, 0x54, 0xa0,
	0x28, 0xd2, 0x97, 0xfc, 0x05, 0x7d, 0x2e, 0x66, 0x66, 0x97, 0x3b, 0x4b, 0x2e, 0x29, 0xba, 0x4e,
	0x51, 0xf4, 0xc5, 0xd0, 0xce, 0x9c, 0x39, 0xbf, 0xf3, 0x3d, 0x67, 0x0e, 0x0d, 0x8a, 0xdb, 0xaa,
	0x5f, 0x43, 0x8e, 0x53, 0xb7, 0xad, 0x23, 0xd2, 0x08, 0xff, 0x72, 0x3b, 0xad, 0x35, 0xa7, 0x65,
	0x7b, 0xb6, 0x9c, 0xee, 0xad, 0x2d, 0x5d, 0xaa, 0xdb, 0xa6, 0x69, 0x5b, 0xd7, 0x3c, 0xa3, 0xe3,
	0x90, 0x7a, 0xd3, 0xc0, 0xd7, 0xdc, 0xe6, 0x61, 0x9b, 0x18, 0x1e, 0xb1, 0xbc, 0xae, 0x83, 0xf9,
	0x01, 0xf5, 0xff, 0x20, 0xbb, 0x8d, 0x4c, 0xfc, 0x18, 0x35, 0xf0, 0x86, 0xde, 0xc1, 0x2d, 0x4f,
	0x2e, 0xc0, 0x84, 0x85, 0x4c, 0x5c, 0x90, 0x14, 0x69, 0x35, 0xbd, 0x39, 0xf1, 0xd9, 0x9f, 0x57,
	0x4e, 0x55, 0xd9, 0x8a, 0xbc, 0x08, 0xc9, 0x76, 0xcb, 0x28, 0x24, 0x84, 0x0d, 0xba, 0x40, 0x4f,
	0x90, 0xba, 0x6d, 0x15, 0x92, 0xe2, 0x09, 0xba, 0xa2, 0x3e, 0x81, 0xc5, 0x3d, 0xec, 0x05, 0x00,
	0x35, 0xdb, 0x79, 0x80, 0x0e, 0x0d, 0x5c, 0xc5, 0xcf, 0xe4, 0xb7, 0x20, 0xeb, 0xd9, 0x8e, 0x66,
	0xd0, 0x6f, 0xcd, 0x20, 0xae, 0x57, 0x90, 0x94, 0xe4, 0xea, 0xcc, 0xfa, 0xd9, 0xb5, 0x9e, 0x06,
	0x6b, 0x51, 0xc1, 0xaa, 0x19, 0xcf, 0x3f, 0xff, 0x80, 0xb8, 0x9e, 0xfa, 0xbf, 0x70, 0x66, 0x3b,
	0x8e, 0xb5, 0xeb, 0xbc, 0x3c, 0xef, 0x47, 0x70, 0x5a, 0x10, 0x7b, 0xcb, 0xf2, 0x70, 0x8b, 0xca,
	0xfc, 0x26, 0x00, 0xa6, 0x7f, 0x8f, 0xc9, 0x33, 0xcd, 0x88, 0x19, 0xc3, 0xc7, 0x30, 0xbf, 0x3d,
	0xc0, 0xd0, 0x75, 0x5e, 0x82, 0xe3, 0x8f, 0x25, 0x98, 0x17, 0x64, 0xf4, 0x09, 0xf0, 0x33, 0xf9,
	0x3f, 0x61, 0xd6, 0x44, 0xc4, 0xd2, 0x90, 0xde, 0x19, 0x93, 0xeb, 0x0c, 0xa5, 0xdf, 0xd0, 0x3b,
	0x94, 0x2f, 0x3d, 0xde, 0x21, 0x75, 0x1c, 0x1e, 0x4f, 0x9c, 0x78, 0x9c, 0xd2, 0xfb, 0xc7, 0xd5,
	0x9f, 0x48, 0xb0, 0xb0, 0x3d, 0x28, 0x96, 0xeb, 0xfc, 0x8b, 0xe5, 0x3a, 0x8a, 0x04, 0xe2, 0x76,
	0x0b, 0x75, 0xf7, 0x89, 0xee, 0x52, 0x7b, 0x15, 0x60, 0xa2, 0x4d, 0x74, 0xb7, 0x20, 0x29, 0x89,
	0x30, 0x78, 0xe9, 0x8a, 0xbc, 0x0e, 0x53, 0x0d, 0x8d, 0xa6, 0x4a, 0x21, 0xa1, 0x24, 0x56, 0xb3,
	0xeb, 0x0b, 0x02, 0x16, 0xc7, 0xa8, 0x75, 0x1d, 0xec, 0x1f, 0x99, 0x6c, 0xd0, 0x0f, 0xf5, 0x01,
	0x2c, 0x6e, 0xc7, 0xe3, 0x84, 0xdc, 0xa4, 0xb1, 0xb9, 0xdd, 0x8c, 0xc4, 0x78, 0xc8, 0xcd, 0x75,
	0x86, 0x8b, 0xad, 0x3e, 0x86, 0xfc, 0x36, 0xf6, 0xf6, 0x1d, 0x1d, 0x79, 0xb8, 0x46, 0x4c, 0x96,
	0x6d, 0x65, 0x98, 0x41, 0x0c, 0x63, 0x6c, 0x09, 0x00, 0xf5, 0x56, 0xd4, 0x12, 0xcc, 0xf5, 0x71,
	0x74, 0x1d, 0xf9, 0x55, 0x98, 0x69, 0xb3, 0x15, 0xcd, 0x23, 0x26, 0x67, 0x39, 0x1b, 0x9c, 0x6d,
	0xf7, 0x48, 0xd5, 0xcf, 0x25, 0xc8, 0x57, 0x11, 0xd1, 0xab, 0xa4, 0xfe, 0x14, 0xbb, 0x77, 0x19,
	0x5a, 0x50, 0x48, 0x44, 0xd9, 0x59, 0x21, 0x59, 0x82, 0x49, 0x8f, 0x78, 0x06, 0x37, 0x78, 0xb0,
	0xc3, 0x97, 0xe4, 0x8b, 0x90, 0x76, 0xdb, 0x87, 0x1a, 0xdf, 0x17, 0x2b, 0x4d, 0xca, 0x6d, 0x1f,
	0xd6, 0x18, 0xc9, 0x2b, 0x00, 0x87, 0xb8, 0x41, 0x2c, 0x2e, 0xd1, 0x84, 0x92, 0x58, 0x9d, 0xf0,
	0x69, 0xd2, 0x6c, 0x9d, 0x0a, 0x24, 0xaf, 0x40, 0x0a, 0x5b, 0x3a, 0x27, 0x99, 0x14, 0x48, 0xa6,
	0xb1, 0xa5, 0x33, 0x82, 0x0b, 0x00, 0x6d, 0xa2, 0x6b, 0x6e, 0xfb, 0xe8, 0x88, 0x1c, 0x17, 0xa6,
	0x94, 0xe4, 0xea, 0x6c, 0x35, 0xdd, 0x26, 0xfa, 0x1e, 0x5b, 0x50, 0xbf, 0x93, 0x84, 0xdc, 0xdd,
	0xb6, 0xeb, 0xd9, 0xe6, 0x96, 0xe5, 0xb5, 0xba, 0x3b, 0xd6, 0x91, 0xfd, 0xef, 0xac, 0x4f, 0xaf,
	0x78, 0x4f, 0xf7, 0x17, 0x6f, 0xea, 0xe1, 0xba, 0x41, 0xb0, 0xe5, 0x07, 0x4d, 0x4a, 0x91, 0x42,
	0x0f, 0xf3, 0x0d, 0x1a, 0x1d, 0x54, 0x4a, 0x4c, 0x2d, 0xc1, 0xa9, 0xd2, 0x02, 0x55, 0x9a, 0xad,
	0x33, 0xa2, 0x22, 0xe4, 0x90, 0xa5, 0xb7, 0x6c, 0xa2, 0x6b, 0x1d, 0xdc, 0x72, 0x89, 0x6d, 0x15,
	0x40, 0x00, 0xcc, 0xfa, 0x9b, 0x07, 0x7c, 0x8f, 0x42, 0x13, 0xdb, 0xed, 0x91, 0xce, 0x08, 0xa4,
	0x40, 0x6c, 0xd7, 0x27, 0x53, 0x1f, 0x44, 0x5c, 0xc1, 0xea, 0xc4, 0x9d, 0x40, 0x1a, 0xa1, 0xc6,
	0x2c, 0x09, 0x81, 0xde, 0xe7, 0x3a, 0x5f, 0x46, 0x56, 0x23, 0xbe, 0x97, 0xe0, 0x77, 0x61, 0x0d,
	0x1d, 0x6e, 0xe8, 0x8c, 0x42, 0x3e, 0x07, 0x53, 0xa8, 0xee, 0x69, 0x44, 0x8f, 0xc4, 0xf7, 0x24,
	0xaa, 0x7b, 0x3b, 0xba, 0x7c, 0x01, 0xa6, 0xe9, 0x26, 0xbf, 0x12, 0x43, 0xff, 0xd2, 0x13, 0xfb,
	0x2d, 0x43, 0x7e, 0x0d, 0x32, 0x0d, 0x64, 0x62, 0xcd, 0x43, 0x87, 0x1a, 0x31, 0x1b, 0x85, 0xa4,
	0x40, 0x03, 0x0d, 0x8e, 0xb3, 0x63, 0x36, 0xe4, 0x35, 0xc8, 0x37, 0xda, 0xc4, 0xd0, 0x35, 0xb3,
	0xd1, 0xb2, 0xdb, 0x0e, 0xa3, 0x9d, 0x10, 0x68, 0xb3, 0x6c, 0x77, 0x97, 0x6d, 0x52, 0xfa, 0x68,
	0x54, 0x4c, 0x9e, 0x1c, 0x15, 0x53, 0x71, 0x51, 0xb1, 0x02, 0x29, 0xe2, 0x6a, 0x1d, 0x64, 0x10,
	0xbd, 0x30, 0xad, 0x24, 0x56, 0x53, 0x01, 0x01, 0x71, 0x0f, 0xe8, 0xa2, 0xba, 0x03, 0xb3, 0x3d,
	0x63, 0x30, 0xcb, 0xbe, 0x19, 0x63, 0xd9, 0xfe, 0xf2, 0x1b, 0x9a, 0x4e, 0x34, 0xec, 0xb7, 0x25,
	0x98, 0xbb, 0xfb, 0x14, 0x59, 0x16, 0x36, 0x36, 0x51, 0xbd, 0x49, 0x35, 0xb1, 0x74, 0xf9, 0x0a,
	0x64, 0x0f, 0x7b, 0x5f, 0x5a, 0x7f, 0xfe, 0xcc, 0x86, 0x7b, 0xd4, 0x98, 0x51, 0xa5, 0x13, 0x27,
	0x2b, 0x9d, 0x8c, 0x51, 0x5a, 0xfd, 0x7f, 0x58, 0x18, 0x90, 0x83, 0xe9, 0xb6, 0x05, 0x39, 0x41,
	0x16, 0x41, 0xc1, 0xf3, 0x62, 0xe8, 0xf4, 0x1f, 0xad, 0x0a, 0x0a, 0x30, 0x45, 0xbf, 0x01, 0xa9,
	0x6a, 0xdb, 0x7d, 0xca, 0x6a, 0x42, 0x01, 0x26, 0xfc, 0x5a, 0x1b, 0x26, 0x04, 0x5b, 0x91, 0x8b,
	0x90, 0x6f, 0xb5, 0xdd, 0xa7, 0x9a, 0x89, 0x8e, 0xb5, 0x16, 0xa2, 0x02, 0xbb, 0xac, 0xa7, 0x0a,
	0xa8, 0x66, 0xe9, 0xee, 0x2e, 0x3a, 0xae, 0x22, 0x4b, 0xaf, 0xb9, 0x34, 0x8e, 0x18, 0xf9, 0x73,
	0x44, 0x3c, 0x4a, 0x9a, 0x14, 0xf3, 0x90, 0xee, 0xbc, 0x8b, 0x88, 0x57, 0x73, 0xd5, 0x2f, 0x92,
	0x90, 0x7b, 0xdb, 0xb0, 0x91, 0xf7, 0x00, 0x75, 0x71, 0x8b, 0xc7, 0xef, 0x22, 0x24, 0x69, 0x38,
	0x45, 0x0a, 0x13, 0x31, 0x1b, 0x61, 0x27, 0x97, 0x18, 0xe8, 0xe4, 0x98, 0xd0, 0x49, 0x21, 0xda,
	0xb9, 0xd0, 0x5f, 0x4f, 0x2d, 0x5a, 0x81, 0x94, 0x6e, 0xfb, 0xf5, 0x64, 0x4a, 0xd0, 0x63, 0x5a,
	0xb7, 0x79, 0x31, 0xb9, 0x08, 0x69, 0x4a, 0xd0, 0x41, 0x46, 0x1b, 0xb3, 0x92, 0x14, 0x50, 0xd0,
	0x73, 0x07, 0x74, 0x95, 0xa6, 0x1d, 0xed, 0xee, 0xa8, 0x5e, 0x29, 0xa1, 0x2e, 0x4c, 0x79, 0x36,
	0x4b, 0x0f, 0x9a, 0xb2, 0x8e, 0x43, 0x53, 0x56, 0x2c, 0x45, 0x93, 0xc8, 0x71, 0x76, 0x74, 0xca,
	0xde, 0x44, 0xad, 0x26, 0x66, 0x29, 0x0d, 0x22, 0x7b, 0xbe, 0xbc, 0xa3, 0xcb, 0x0a, 0xa4, 0x1c,
	0x03, 0x79, 0x47, 0x76, 0xcb, 0x64, 0x75, 0xa7, 0x47, 0x11, 0xac, 0xca, 0xb7, 0x61, 0x5e, 0x27,
	0xae, 0x63, 0xa0, 0xae, 0x56, 0xe7, 0x21, 0xc1, 0x15, 0xca, 0x08, 0xd4, 0xb2, 0x4f, 0xe1, 0xc7,
	0x0c, 0xd3, 0xed, 0x3a, 0xa4, 0x99, 0x23, 0x89, 0x75, 0x64, 0x17, 0x66, 0x15, 0x69, 0x75, 0x66,
	0xfd, 0xb4, 0x10, 0x5e, 0x41, 0xe4, 0x54, 0x53, 0x2d, 0xff, 0x2f, 0xf5, 0x3e, 0x64, 0x43, 0x8f,
	0x8e, 0x55, 0xde, 0xfa, 0x02, 0x40, 0xcc, 0xc2, 0x5f, 0x24, 0x41, 0xde, 0x75, 0x1b, 0x61, 0x87,
	0xc4, 0x43, 0x64, 0x1e, 0x12, 0x7d, 0xe5, 0x2d, 0x41, 0xf4, 0x20, 0x70, 0x12, 0x43, 0x02, 0x27,
	0x19, 0x17, 0x38, 0xf8, 0xd8, 0x8b, 0x14, 0x2e, 0xb6, 0xf2, 0x35, 0x95, 0x2b, 0xd1, 0x2b, 0xd3,
	0xb1, 0x5e, 0x79, 0x1d, 0x72, 0xc4, 0xaa, 0x1b, 0x6d, 0x1d, 0x07, 0x5e, 0x29, 0xa4, 0x94, 0xe4,
	0x6a, 0xba, 0x9a, 0xf5, 0x97, 0x7d, 0x57, 0x50, 0x42, 0x7c, 0x1c, 0x25, 0x4c, 0x73, 0x42, 0x7f,
	0x39, 0x20, 0xbc, 0x0a, 0x39, 0xe2, 0x6a, 0x16, 0x7e, 0x7e, 0x48, 0xb0, 0xc6, 0xec, 0xc8, 0x42,
	0x26, 0xa8, 0x94, 0xb3, 0xc4, 0x7d, 0xc8, 0xf6, 0xc2, 0xab, 0x82, 0xc7, 0xdd, 0xcc, 0x09, 0x71,
	0x97, 0x89, 0x8b, 0x3b, 0xf5, 0xbf, 0x61, 0x2e, 0xe2, 0x1d, 0xe6, 0xee, 0x72, 0x8c, 0xbb, 0x2f,
	0x08, 0xee, 0x1e, 0xf4, 0xa7, 0xe8, 0xf1, 0x47, 0x90, 0xdb, 0xc6, 0xde, 0x86, 0xe3, 0xf0, 0xb6,
	0xeb, 0xe5, 0x1b, 0xc1, 0xdb, 0xac, 0xbb, 0xed, 0x31, 0xbc, 0x87, 0x3d, 0x44, 0x0c, 0xd6, 0x0d,
	0x9e, 0x87, 0x29, 0x9d, 0x7d, 0x31, 0x96, 0x99, 0x20, 0x27, 0xf9, 0x9a, 0xea, 0xc2, 0xc2, 0x5e,
	0xcc, 0xb9, 0x97, 0x14, 0x47, 0x00, 0x4d, 0xc4, 0x80, 0x7a, 0x20, 0xdf, 0x65, 0x2f, 0xe0, 0xad,
	0xa3, 0x23, 0x5c, 0xf7, 0xf6, 0xec, 0x76, 0xab, 0x8e, 0x85, 0xb2, 0xdc, 0x5f, 0xe1, 0x86, 0xd5,
	0xc4, 0x45, 0x48, 0x9a, 0xfa, 0xad, 0x68, 0xc8, 0x9b, 0xfa, 0x2d, 0x3f, 0x71, 0x26, 0x04, 0x67,
	0x26, 0x88, 0xae, 0x1e, 0xc0, 0xe2, 0x20, 0xea, 0x58, 0xbe, 0x1c, 0x3c, 0x26, 0xfa, 0xf2, 0x07,
	0x12, 0xe4, 0xee, 0xb2, 0xa6, 0x6b, 0xa3, 0xad, 0x13, 0x8f, 0x5d, 0x31, 0x7d, 0x0d, 0x5a, 0xa4,
	0x05, 0x17, 0x1a, 0xb4, 0x2b, 0x90, 0xf5, 0xc9, 0x82, 0x7e, 0x2a, 0x21, 0x50, 0xce, 0xf2, 0xbd,
	0xa0, 0xf3, 0x7a, 0x1d, 0x32, 0x88, 0x02, 0x68, 0xae, 0x87, 0xbc, 0xb6, 0x1b, 0xb9, 0x09, 0x66,
	0xd8, 0xce, 0x1e, 0xdb, 0x50, 0x3f, 0x04, 0x79, 0x0f, 0x7b, 0x3b, 0xb6, 0xdb, 0x93, 0x87, 0x3a,
	0x74, 0x10, 0x4b, 0x1a, 0x1f, 0x2b, 0x31, 0x0c, 0xeb, 0x31, 0x9c, 0xde, 0xee, 0xc7, 0x72, 0x1d,
	0x5a, 0x0c, 0xf9, 0x79, 0x56, 0x51, 0x29, 0x50, 0x5f, 0xaf, 0x17, 0xb5, 0x57, 0x35, 0x8d, 0x82,
	0x3f, 0xd5, 0x0a, 0xa4, 0x78, 0x2c, 0xde, 0x3f, 0xa0, 0x0e, 0x6e, 0xe2, 0x6e, 0xf4, 0x92, 0x6c,
	0xe2, 0x2e, 0xed, 0xde, 0xf9, 0x3d, 0x14, 0xe9, 0xde, 0xd9, 0x92, 0xba, 0x0d, 0xe0, 0xc7, 0x32,
	0xf2, 0x90, 0x7c, 0x07, 0xb2, 0x1c, 0x52, 0x6b, 0x46, 0x1e, 0xb7, 0xa7, 0x23, 0xee, 0xe5, 0x70,
	0xd5, 0x0c, 0x5f, 0xb8, 0xcf, 0x1f, 0xa6, 0xbf, 0x96, 0x60, 0x6e, 0x17, 0x11, 0x8b, 0x0d, 0x31,
	0x50, 0xc3, 0x7f, 0x20, 0xfd, 0xe3, 0x0c, 0x69, 0xa7, 0x8d, 0x8f, 0x1d, 0xd2, 0x42, 0x1e, 0xb1,
	0x85, 0x76, 0x29, 0xb0, 0x6b, 0x36, 0xdc, 0x64, 0x85, 0x35, 0x5a, 0x9e, 0x45, 0x6f, 0x87, 0xe5,
	0x59, 0xfd, 0x93, 0x14, 0xa8, 0xdb, 0x8b, 0x3b, 0x2e, 0x5d, 0x4c, 0xdc, 0xb1, 0x8d, 0x5e, 0xdc,
	0x71, 0xb2, 0xf8, 0xb8, 0x63, 0x7b, 0x42, 0xc7, 0xef, 0x13, 0xeb, 0xc8, 0x43, 0x4c, 0x90, 0x4c,
	0x94, 0x27, 0xb3, 0xf4, 0x60, 0x1b, 0x32, 0x28, 0x6e, 0x9c, 0x09, 0x26, 0x87, 0x9b, 0x40, 0xfd,
	0x8d, 0x04, 0xa7, 0xfd, 0x0a, 0x89, 0xac, 0x06, 0xa6, 0x2a, 0x32, 0x4b, 0xfe, 0x33, 0xd4, 0x7c,
	0x0b, 0xf2, 0x3e, 0x31, 0x8d, 0x59, 0xee, 0xda, 0x24, 0x73, 0xed, 0xc2, 0x80, 0x6b, 0x59, 0xcc,
	0xfa, 0xbc, 0x03, 0xa1, 0xd4, 0x65, 0x38, 0xbf, 0x8d, 0xbd, 0xbd, 0xae, 0x55, 0xe7, 0x44, 0x07,
	0xeb, 0x1b, 0x86, 0xe1, 0x73, 0xaf, 0xe2, 0x67, 0xea, 0xbb, 0x70, 0x61, 0xc4, 0xbe, 0xeb, 0xc8,
	0xb7, 0x21, 0xe3, 0xcb, 0x79, 0x62, 0x60, 0xcd, 0xf8, 0x84, 0x0c, 0xf8, 0x3e, 0xac, 0xf4, 0x31,
	0xa6, 0x32, 0x6d, 0x76, 0xef, 0x63, 0x56, 0xa0, 0x68, 0xf2, 0xaf, 0xf6, 0x94, 0xa3, 0x06, 0x0b,
	0xd9, 0xcf, 0x06, 0x5a, 0x50, 0x7b, 0x31, 0x66, 0x75, 0x50, 0x46, 0x33, 0x63, 0x53, 0xbc, 0x41,
	0x53, 0x49, 0x2f, 0x62, 0xaa, 0x6b, 0x30, 0xb5, 0xe7, 0xb1, 0xc1, 0xdd, 0x78, 0x9e, 0x54, 0x2f,
	0xd1, 0xa2, 0xa0, 0xe3, 0x60, 0xbe, 0x52, 0xb7, 0xf5, 0xbe, 0x7b, 0x82, 0xae, 0xa8, 0x05, 0x76,
	0x09, 0x56, 0x91, 0xd5, 0xdc, 0x34, 0x50, 0xbd, 0xb9, 0x4f, 0x74, 0x5f, 0x7f, 0xd5, 0x65, 0xe3,
	0x9a, 0xc1, 0x1d, 0xd7, 0x91, 0xaf, 0x80, 0xdc, 0xc2, 0xf5, 0x0e, 0xed, 0xf7, 0x9b, 0x1a, 0x7d,
	0x8e, 0x0b, 0xc6, 0xc9, 0xd1, 0x1d, 0x7a, 0xca, 0x3f, 0x40, 0x89, 0x5d, 0xda, 0x0e, 0x45, 0x89,
	0x13, 0x9c, 0x98, 0xee, 0x08, 0xc4, 0x97, 0x3f, 0x97, 0x00, 0xc2, 0x5b, 0x52, 0x06, 0x98, 0xda,
	0xb7, 0x9a, 0x96, 0xfd, 0x3c, 0x7f, 0x4a, 0x9e, 0x85, 0x74, 0xcd, 0x76, 0x34, 0x36, 0xd7, 0xcc,
	0x4b, 0x74, 0x8b, 0x13, 0xe6, 0x13, 0x74, 0xeb, 0xed, 0xb6, 0xa5, 0xb1, 0x49, 0x64, 0x3e, 0x29,
	0xe7, 0x60, 0xa6, 0x8a, 0x88, 0xae, 0xf1, 0x29, 0x4d, 0x7e, 0x42, 0xce, 0x43, 0x86, 0xbf, 0x94,
	0x35, 0xd6, 0x55, 0xe4, 0x27, 0x29, 0x09, 0x7d, 0xe1, 0x69, 0x35, 0x74, 0xa8, 0x6d, 0xe8, 0xf9,
	0x29, 0xba, 0xc0, 0xba, 0x4d, 0x8d, 0xb5, 0x9b, 0xf9, 0x69, 0xf9, 0x34, 0xe4, 0x76, 0xdd, 0x86,
	0x46, 0x2b, 0x99, 0xe6, 0x03, 0xa5, 0xe4, 0x02, 0xcc, 0xf3, 0x8b, 0x4d, 0xe3, 0x37, 0x9b, 0xc6,
	0xaf, 0xb6, 0x7c, 0x5a, 0x5e, 0x04, 0xd9, 0xef, 0xb4, 0xb4, 0xf0, 0x49, 0x95, 0x87, 0xcb, 0x35,
	0xb1, 0xe9, 0x65, 0x3a, 0xcd, 0xc0, 0xf4, 0xdd, 0x77, 0x36, 0x1e, 0x3e, 0xdc, 0x7a, 0x90, 0x97,
	0x18, 0x43, 0xfe, 0xa1, 0xbd, 0xb3, 0xff, 0xb0, 0xa6, 0xed, 0x3e, 0x7a, 0xb8, 0x57, 0xdb, 0xaa,
	0xe6, 0x13, 0xf2, 0x39, 0x38, 0x13, 0xb7, 0xa3, 0x1d, 0xac, 0xe7, 0x93, 0x97, 0x57, 0x60, 0xee,
	0xee, 0xfe, 0x5e, 0xed, 0xd1, 0xae, 0xb6, 0xf5, 0xb0, 0x56, 0x7d, 0xa2, 0xd5, 0x9e, 0x3c, 0xde,
	0xa2, 0x16, 0xd9, 0xdd, 0xd2, 0x6a, 0x1b, 0x9b, 0x79, 0xe9, 0xf2, 0x1b, 0x91, 0xfb, 0x95, 0xe1,
	0xa6, 0x60, 0xe2, 0x31, 0x72, 0xdd, 0xfc, 0x29, 0x39, 0x03, 0x29, 0xb6, 0x4c, 0xac, 0x46, 0x5e,
	0x5a, 0xff, 0xfd, 0xab, 0x90, 0xe9, 0x35, 0x33, 0x7b, 0x9d, 0x96, 0xdc, 0x88, 0xcc, 0x8b, 0x83,
	0x59, 0xb4, 0x7c, 0x51, 0x88, 0xd3, 0xf8, 0x31, 0xf8, 0xd2, 0xf9, 0xb5, 0xde, 0x78, 0x7e, 0x6d,
	0xef, 0xfe, 0x26, 0x1f, 0xcf, 0x6f, 0x99, 0x8e, 0xd7, 0xd5, 0x1e, 0x6f, 0xaa, 0xb9, 0x6f, 0x7e,
	0xfa, 0x55, 0x52, 0xfa, 0xee, 0xa7, 0x5f, 0x25, 0x4f, 0xfd, 0x88, 0xfe, 0x23, 0x3f, 0x65, 0x17,
	0xe1, 0x00, 0xd0, 0x48, 0x2e, 0x4b, 0xaa, 0xf8, 0x48, 0x8f, 0x1f, 0x99, 0x73, 0xa4, 0x84, 0x80,
	0x84, 0x20, 0xdf, 0x3f, 0x02, 0x97, 0x97, 0xe3, 0xf5, 0x09, 0xe6, 0xe3, 0xe3, 0x28, 0x93, 0x14,
	0x20, 0x0e, 0xd9, 0xa0, 0x32, 0x0a, 0x31, 0x5a, 0x93, 0x95, 0x78, 0x4d, 0x7a, 0xf3, 0x74, 0x8e,
	0x31, 0x21, 0x60, 0xd4, 0x61, 0x6e, 0x60, 0x4a, 0x2e, 0xaf, 0xc4, 0xeb, 0xd1, 0x9b, 0xa1, 0x8f,
	0xa3, 0xc8, 0xa4, 0x00, 0xa2, 0xb3, 0xf9, 0x68, 0x1f, 0xc8, 0x68, 0x4d, 0x94, 0x78, 0x4d, 0xc2,
	0x79, 0x39, 0x47, 0x99, 0x12, 0x50, 0xa2, 0x41, 0x16, 0x0c, 0x83, 0x87, 0x05, 0x99, 0x30, 0x7a,
	0x1e, 0x47, 0x9d, 0x69, 0x01, 0xc8, 0x8c, 0x04, 0x59, 0x2c, 0x50, 0xfc, 0x8c, 0x7b, 0x58, 0xa4,
	0x89, 0x83, 0x6b, 0x0e, 0x97, 0x12, 0xe0, 0xde, 0x83, 0xd9, 0xc8, 0x74, 0x59, 0x3e, 0x17, 0xe5,
	0x12, 0x99, 0x64, 0x2f, 0x9d, 0x1f, 0xbe, 0x19, 0x30, 0x4f, 0x0b, 0xcc, 0x2d, 0x90, 0x07, 0x5f,
	0x2c, 0xf2, 0x52, 0x94, 0x89, 0xf8, 0x42, 0x5a, 0xba, 0x38, 0x64, 0x2f, 0x7c, 0xec, 0xa8, 0x67,
	0x29, 0x0a, 0x50, 0x94, 0x84, 0x57, 0xa2, 0x38, 0xa9, 0xa2, 0x57, 0xa6, 0x37, 0x4b, 0x45, 0xfe,
	0xa3, 0xc4, 0xda, 0xe2, 0x7e, 0x40, 0x25, 0xea, 0xa4, 0xc1, 0x97, 0xd0, 0x09, 0x3e, 0xfa, 0x88,
	0x22, 0xce, 0x50, 0xc4, 0xac, 0x57, 0x6a, 0x97, 0x8c, 0x12, 0x29, 0xb9, 0x25, 0x5c, 0x3a, 0x66,
	0xe8, 0x87, 0x01, 0xba, 0x52, 0x6c, 0x97, 0xdb, 0x2d, 0xa3, 0xa2, 0x14, 0x8d, 0x32, 0x9b, 0x2b,
	0x57, 0x94, 0x22, 0x29, 0xf7, 0xa6, 0xcc, 0x15, 0xa5, 0xe8, 0x96, 0x5d, 0x0f, 0xd1, 0xd7, 0x16,
	0x31, 0xe9, 0x27, 0x2e, 0x07, 0xaf, 0xee, 0x8a, 0x52, 0x3c, 0x2e, 0x87, 0x63, 0xe2, 0x55, 0xbc,
	0xd6, 0x58, 0x53, 0x6e, 0xad, 0xdf, 0xb8, 0x7a, 0xfb, 0x3f, 0xae, 0xde, 0x79, 0xa3, 0x22, 0xff,
	0x4a, 0x82, 0xec, 0x86, 0xae, 0x0b, 0xd3, 0x53, 0x79, 0xc4, 0x54, 0xf5, 0x04, 0x6d, 0xf6, 0xa9,
	0x36, 0x19, 0xaa, 0x0d, 0x84, 0xba, 0x50, 0x4d, 0xca, 0x2f, 0xa3, 0x80, 0xfc, 0x1e, 0x64, 0xb7,
	0xb1, 0x27, 0x8a, 0x38, 0x3a, 0x07, 0x87, 0x28, 0xc0, 0x7a, 0x07, 0x16, 0x48, 0xb3, 0x42, 0x20,
	0x7d, 0x00, 0x73, 0x55, 0x6c, 0xda, 0x1d, 0x3c, 0x3e, 0xff, 0x31, 0xd2, 0x2e, 0x2b, 0x20, 0xfc,
	0x56, 0x82, 0xcc, 0x86, 0xae, 0xf7, 0xc6, 0xa8, 0xf2, 0xf0, 0xe1, 0xea, 0x09, 0xac, 0x75, 0xca,
	0x3a, 0x47, 0x59, 0x67, 0x50, 0xa9, 0x5d, 0x32, 0x4b, 0x56, 0xcf, 0xc2, 0x3b, 0x45, 0x54, 0xe6,
	0x13, 0x6d, 0x21, 0x5a, 0xcc, 0xf2, 0x21, 0xed, 0xb2, 0xcc, 0x46, 0x45, 0x29, 0x5a, 0x65, 0xd7,
	0x44, 0x86, 0xe1, 0x7f, 0x8d, 0x32, 0xf7, 0xff, 0x40, 0xc6, 0xcf, 0x71, 0x2e, 0xee, 0x68, 0x63,
	0x0c, 0x57, 0x86, 0x5b, 0x22, 0x2f, 0x58, 0xe2, 0x43, 0xc8, 0x71, 0x5b, 0x8f, 0xcb, 0x7c, 0xb4,
	0x39, 0xce, 0x51, 0xfe, 0x73, 0x2c, 0x5d, 0x11, 0x33, 0x02, 0x84, 0x46, 0x90, 0xbf, 0x95, 0x80,
	0xd9, 0x0d, 0x5d, 0x0f, 0x3b, 0x0e, 0x79, 0xc4, 0x38, 0xed, 0x04, 0xa0, 0xdf, 0x49, 0x14, 0x49,
	0xa6, 0x48, 0xf3, 0xa4, 0xd4, 0x2e, 0x79, 0xcc, 0xec, 0x7a, 0xc9, 0x2a, 0x39, 0x25, 0xb3, 0xc4,
	0xb1, 0x3f, 0x91, 0x8a, 0xa4, 0xcc, 0x8d, 0x1b, 0x98, 0xbf, 0x97, 0xbd, 0xa3, 0xb2, 0x13, 0x95,
	0x91, 0xe3, 0xec, 0xdc, 0xab, 0x28, 0x66, 0xd9, 0x1f, 0x19, 0xdd, 0xab, 0x28, 0xef, 0x15, 0x75,
	0xa5, 0x1c, 0x0c, 0x54, 0x95, 0xeb, 0x6b, 0x96, 0x6d, 0x61, 0xe5, 0xc6, 0x9a, 0x6d, 0xd5, 0xb1,
	0xb2, 0xbe, 0x86, 0x3b, 0xb8, 0xd5, 0xd5, 0x2c, 0x4d, 0x47, 0x5d, 0x97, 0x7a, 0x96, 0x93, 0xb2,
	0xf7, 0x6b, 0x45, 0x29, 0x3a, 0x4a, 0xd9, 0x1f, 0xa3, 0x56, 0xde, 0x97, 0x9f, 0xb0, 0x1a, 0x2c,
	0x18, 0x61, 0x7c, 0x67, 0x46, 0x07, 0x94, 0xdc, 0x99, 0xa7, 0x05, 0x67, 0x1e, 0xc1, 0xec, 0x3d,
	0x6c, 0x8c, 0xcd, 0x7a, 0xb4, 0x85, 0x59, 0xe5, 0x9d, 0x8f, 0xad, 0xbc, 0x9f, 0x24, 0x20, 0xbf,
	0xa1, 0xeb, 0x91, 0x89, 0x98, 0x3c, 0x7a, 0x56, 0x76, 0x02, 0xd8, 0x1f, 0x98, 0x3b, 0x17, 0x28,
	0x5a, 0x81, 0x94, 0x9c, 0x9e, 0x43, 0xed, 0xd2, 0xd3, 0x92, 0x55, 0x3a, 0x2e, 0xa1, 0x92, 0xc9,
	0x64, 0xf8, 0x19, 0x73, 0x29, 0x4d, 0x28, 0x67, 0xd0, 0xb3, 0xf8, 0xd8, 0x0b, 0x3d, 0x5b, 0x13,
	0x1c, 0xeb, 0xff, 0x6d, 0x97, 0xaf, 0x2b, 0xc8, 0x30, 0xae, 0x2a, 0x37, 0x14, 0xff, 0x37, 0xb0,
	0xab, 0xca, 0xba, 0x42, 0x6c, 0xea, 0xb1, 0xa7, 0xe5, 0x70, 0xa2, 0xc9, 0x52, 0x33, 0x9c, 0x5b,
	0xb2, 0x7a, 0x1d, 0x0c, 0x20, 0x69, 0x78, 0x28, 0x34, 0x3e, 0x98, 0x10, 0xa6, 0x12, 0x44, 0x88,
	0x5e, 0x91, 0x35, 0xd6, 0x63, 0x45, 0x8d, 0x72, 0x92, 0x03, 0x86, 0x98, 0x2c, 0x74, 0xef, 0x62,
	0xa4, 0x2b, 0xc9, 0xdf, 0xc3, 0xc6, 0x8b, 0x01, 0x8c, 0x32, 0xfa, 0x19, 0x0a, 0x70, 0x86, 0x79,
	0x98, 0x30, 0xeb, 0x4e, 0x71, 0xe3, 0xca, 0x1f, 0x41, 0xae, 0x6f, 0xde, 0x14, 0xf1, 0xee, 0xe0,
	0x2c, 0xea, 0x04, 0xa0, 0x6b, 0x14, 0xa8, 0x40, 0x81, 0x26, 0x5c, 0x3f, 0x37, 0xcf, 0x17, 0xdd,
	0xb2, 0xf0, 0xb3, 0x23, 0xcb, 0x3a, 0xe2, 0x6a, 0x6c, 0x6a, 0x54, 0x91, 0x3f, 0x60, 0xd3, 0xd4,
	0x88, 0x00, 0xa3, 0x15, 0x5d, 0x8e, 0x76, 0x12, 0xfd, 0xe3, 0x2b, 0x6e, 0xcb, 0xb3, 0x82, 0x2d,
	0x3f, 0x82, 0xdc, 0xee, 0xd7, 0x78, 0xc3, 0x5c, 0xa7, 0xfc, 0x97, 0x98, 0x86, 0x76, 0xc9, 0x61,
	0x1a, 0x5e, 0x28, 0xda, 0x65, 0xbb, 0x45, 0x1a, 0x96, 0x42, 0x2c, 0x1d, 0x1f, 0xb3, 0x90, 0xb5,
	0xf0, 0x73, 0xff, 0x4b, 0xfe, 0xa9, 0x04, 0x0b, 0xf4, 0x96, 0x1f, 0x1c, 0x9b, 0x8e, 0x1e, 0x54,
	0x9e, 0x20, 0xc8, 0x7f, 0x51, 0x41, 0xce, 0x51, 0x41, 0x52, 0x1e, 0xbb, 0x8f, 0x78, 0xdf, 0xf2,
	0x86, 0x9f, 0xbb, 0xca, 0xcd, 0xb5, 0x96, 0x5d, 0x6f, 0x62, 0x9a, 0x28, 0xc7, 0x4a, 0xb9, 0xef,
	0x6a, 0x32, 0xf5, 0x5b, 0x15, 0x99, 0xb0, 0xff, 0x55, 0x12, 0x23, 0xd7, 0x68, 0xfb, 0x5c, 0x1c,
	0x29, 0x75, 0x18, 0xd0, 0xe7, 0x23, 0x1d, 0xe3, 0xc2, 0x3d, 0x6c, 0xbc, 0x30, 0xd4, 0x18, 0x75,
	0xeb, 0x42, 0x6c, 0xdd, 0xfa, 0x58, 0x82, 0xb3, 0x43, 0x27, 0x36, 0xf2, 0xeb, 0xd1, 0x18, 0x1a,
	0x3a, 0xf7, 0x59, 0x5a, 0x1d, 0x8f, 0xd0, 0x75, 0xd4, 0x02, 0x95, 0x65, 0x99, 0xc9, 0xd2, 0x66,
	0xb2, 0x4c, 0x53, 0x6b, 0xd3, 0x14, 0xfb, 0xb9, 0x34, 0x30, 0x5c, 0x8a, 0x8c, 0x65, 0xe4, 0xcb,
	0xc3, 0x41, 0xfa, 0x87, 0x41, 0x4b, 0x57, 0xc6, 0xa6, 0x75, 0x1d, 0xf5, 0x22, 0x95, 0x69, 0x85,
	0x85, 0x6a, 0xbb, 0xd4, 0x64, 0x52, 0x65, 0xa9, 0x54, 0x15, 0xa5, 0xd8, 0x2c, 0x37, 0x71, 0xd7,
	0xad, 0xc8, 0xbf, 0x94, 0xd8, 0x8b, 0x34, 0xe4, 0xf3, 0xe8, 0xf0, 0xc3, 0xc8, 0x8b, 0x34, 0x66,
	0x84, 0xb7, 0x14, 0x1d, 0x6b, 0xf1, 0xd1, 0x8e, 0x7a, 0x40, 0xc1, 0x14, 0xd6, 0x4c, 0x37, 0x4b,
	0xcf, 0x4a, 0xcf, 0x4b, 0xb8, 0xd4, 0x2a, 0x79, 0xa5, 0x2e, 0x83, 0xbd, 0x53, 0x6c, 0x96, 0x85,
	0x59, 0x51, 0x45, 0x29, 0x3e, 0x2b, 0x37, 0x6f, 0x54, 0x94, 0xe2, 0xf3, 0x72, 0xe7, 0x06, 0xab,
	0xdd, 0xcd, 0xf5, 0x8a, 0x52, 0x6c, 0x95, 0x3b, 0xeb, 0xac, 0xc0, 0x37, 0x6f, 0x56, 0x94, 0x62,
	0xb7, 0xdc, 0xb9, 0x59, 0x91, 0xdf, 0x67, 0xa5, 0x30, 0x2a, 0xe0, 0x9c, 0x58, 0xa2, 0xd8, 0x30,
	0x2a, 0x5e, 0x26, 0x66, 0x80, 0x8b, 0x23, 0x0d, 0xc0, 0x4b, 0xf9, 0x89, 0xec, 0xe3, 0x87, 0x63,
	0x1c, 0x40, 0x1d, 0x09, 0xf0, 0x7d, 0x09, 0xe6, 0x69, 0xf6, 0x0f, 0xfc, 0x52, 0x3f, 0xf2, 0x47,
	0xf0, 0x13, 0x22, 0xff, 0x0e, 0xc5, 0x7d, 0x85, 0xe2, 0x4e, 0xb5, 0x7b, 0x5d, 0xe8, 0xa5, 0x5e,
	0x7e, 0x8f, 0x6a, 0x30, 0x1b, 0xec, 0x3f, 0xcd, 0xc5, 0x89, 0x33, 0xee, 0xcb, 0x3a, 0xf6, 0xc7,
	0x7e, 0x9e, 0xf2, 0x97, 0x84, 0x94, 0xf7, 0xe0, 0x8c, 0xdf, 0xdb, 0xbf, 0x20, 0xd6, 0x68, 0xd5,
	0x2f, 0x50, 0x9c, 0x57, 0x59, 0xa2, 0x71, 0xb5, 0x33, 0xa2, 0x7e, 0xfe, 0x33, 0xbb, 0x7f, 0x5a,
	0xd8, 0xff, 0xcc, 0x8e, 0x99, 0x33, 0xf6, 0x3f, 0xb3, 0xe3, 0x06, 0x8e, 0x5c, 0xc9, 0xd7, 0x42,
	0x25, 0x97, 0xa6, 0x3e, 0xfe, 0xf4, 0xab, 0xe4, 0xdf, 0x9e, 0x6f, 0xe6, 0x3f, 0xfb, 0x72, 0x59,
	0xfa, 0xe2, 0xcb, 0x65, 0xe9, 0x2f, 0x5f, 0x2e, 0x4b, 0x3f, 0xfc, 0xeb, 0xf2, 0xa9, 0xbf, 0x07,
	0x00, 0x00, 0xff, 0xff, 0x57, 0x7a, 0xc8, 0x19, 0x56, 0x2a, 0x00, 0x00,
}
