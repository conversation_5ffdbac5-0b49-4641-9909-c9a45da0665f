// Code generated by protoc-gen-go. DO NOT EDIT.
// source: player-found/player-found.proto

package playerfound // import "golang.52tt.com/protocol/services/playerfound"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type Settings int32

const (
	Settings_PLAYER_FOUND Settings = 0
	Settings_CITY_TAG     Settings = 1
)

var Settings_name = map[int32]string{
	0: "PLAYER_FOUND",
	1: "CITY_TAG",
}
var Settings_value = map[string]int32{
	"PLAYER_FOUND": 0,
	"CITY_TAG":     1,
}

func (x Settings) String() string {
	return proto.EnumName(Settings_name, int32(x))
}
func (Settings) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_player_found_5cd6d9d2287a4776, []int{0}
}

type BatchGetUserPostsReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserPostsReq) Reset()         { *m = BatchGetUserPostsReq{} }
func (m *BatchGetUserPostsReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserPostsReq) ProtoMessage()    {}
func (*BatchGetUserPostsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_player_found_5cd6d9d2287a4776, []int{0}
}
func (m *BatchGetUserPostsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserPostsReq.Unmarshal(m, b)
}
func (m *BatchGetUserPostsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserPostsReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserPostsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserPostsReq.Merge(dst, src)
}
func (m *BatchGetUserPostsReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserPostsReq.Size(m)
}
func (m *BatchGetUserPostsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserPostsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserPostsReq proto.InternalMessageInfo

func (m *BatchGetUserPostsReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type BatchGetUserPostsResp struct {
	UserPosts            []*PlaymateUserPosts `protobuf:"bytes,1,rep,name=user_posts,json=userPosts,proto3" json:"user_posts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatchGetUserPostsResp) Reset()         { *m = BatchGetUserPostsResp{} }
func (m *BatchGetUserPostsResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserPostsResp) ProtoMessage()    {}
func (*BatchGetUserPostsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_player_found_5cd6d9d2287a4776, []int{1}
}
func (m *BatchGetUserPostsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserPostsResp.Unmarshal(m, b)
}
func (m *BatchGetUserPostsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserPostsResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserPostsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserPostsResp.Merge(dst, src)
}
func (m *BatchGetUserPostsResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserPostsResp.Size(m)
}
func (m *BatchGetUserPostsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserPostsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserPostsResp proto.InternalMessageInfo

func (m *BatchGetUserPostsResp) GetUserPosts() []*PlaymateUserPosts {
	if m != nil {
		return m.UserPosts
	}
	return nil
}

type BatchSetUserPostsReq struct {
	UserPosts            []*PlaymateUserPosts `protobuf:"bytes,1,rep,name=user_posts,json=userPosts,proto3" json:"user_posts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatchSetUserPostsReq) Reset()         { *m = BatchSetUserPostsReq{} }
func (m *BatchSetUserPostsReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetUserPostsReq) ProtoMessage()    {}
func (*BatchSetUserPostsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_player_found_5cd6d9d2287a4776, []int{2}
}
func (m *BatchSetUserPostsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetUserPostsReq.Unmarshal(m, b)
}
func (m *BatchSetUserPostsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetUserPostsReq.Marshal(b, m, deterministic)
}
func (dst *BatchSetUserPostsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetUserPostsReq.Merge(dst, src)
}
func (m *BatchSetUserPostsReq) XXX_Size() int {
	return xxx_messageInfo_BatchSetUserPostsReq.Size(m)
}
func (m *BatchSetUserPostsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetUserPostsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetUserPostsReq proto.InternalMessageInfo

func (m *BatchSetUserPostsReq) GetUserPosts() []*PlaymateUserPosts {
	if m != nil {
		return m.UserPosts
	}
	return nil
}

type BatchSetUserPostsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSetUserPostsResp) Reset()         { *m = BatchSetUserPostsResp{} }
func (m *BatchSetUserPostsResp) String() string { return proto.CompactTextString(m) }
func (*BatchSetUserPostsResp) ProtoMessage()    {}
func (*BatchSetUserPostsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_player_found_5cd6d9d2287a4776, []int{3}
}
func (m *BatchSetUserPostsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetUserPostsResp.Unmarshal(m, b)
}
func (m *BatchSetUserPostsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetUserPostsResp.Marshal(b, m, deterministic)
}
func (dst *BatchSetUserPostsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetUserPostsResp.Merge(dst, src)
}
func (m *BatchSetUserPostsResp) XXX_Size() int {
	return xxx_messageInfo_BatchSetUserPostsResp.Size(m)
}
func (m *BatchSetUserPostsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetUserPostsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetUserPostsResp proto.InternalMessageInfo

type PlaymateUserPosts struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PostIds              []string `protobuf:"bytes,2,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaymateUserPosts) Reset()         { *m = PlaymateUserPosts{} }
func (m *PlaymateUserPosts) String() string { return proto.CompactTextString(m) }
func (*PlaymateUserPosts) ProtoMessage()    {}
func (*PlaymateUserPosts) Descriptor() ([]byte, []int) {
	return fileDescriptor_player_found_5cd6d9d2287a4776, []int{4}
}
func (m *PlaymateUserPosts) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaymateUserPosts.Unmarshal(m, b)
}
func (m *PlaymateUserPosts) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaymateUserPosts.Marshal(b, m, deterministic)
}
func (dst *PlaymateUserPosts) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaymateUserPosts.Merge(dst, src)
}
func (m *PlaymateUserPosts) XXX_Size() int {
	return xxx_messageInfo_PlaymateUserPosts.Size(m)
}
func (m *PlaymateUserPosts) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaymateUserPosts.DiscardUnknown(m)
}

var xxx_messageInfo_PlaymateUserPosts proto.InternalMessageInfo

func (m *PlaymateUserPosts) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PlaymateUserPosts) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

// 找人玩、城市标签开关
type GetPlayerFoundSettingReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	Type                 Settings `protobuf:"varint,2,opt,name=type,proto3,enum=playerfound.Settings" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlayerFoundSettingReq) Reset()         { *m = GetPlayerFoundSettingReq{} }
func (m *GetPlayerFoundSettingReq) String() string { return proto.CompactTextString(m) }
func (*GetPlayerFoundSettingReq) ProtoMessage()    {}
func (*GetPlayerFoundSettingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_player_found_5cd6d9d2287a4776, []int{5}
}
func (m *GetPlayerFoundSettingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayerFoundSettingReq.Unmarshal(m, b)
}
func (m *GetPlayerFoundSettingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayerFoundSettingReq.Marshal(b, m, deterministic)
}
func (dst *GetPlayerFoundSettingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayerFoundSettingReq.Merge(dst, src)
}
func (m *GetPlayerFoundSettingReq) XXX_Size() int {
	return xxx_messageInfo_GetPlayerFoundSettingReq.Size(m)
}
func (m *GetPlayerFoundSettingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayerFoundSettingReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayerFoundSettingReq proto.InternalMessageInfo

func (m *GetPlayerFoundSettingReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *GetPlayerFoundSettingReq) GetType() Settings {
	if m != nil {
		return m.Type
	}
	return Settings_PLAYER_FOUND
}

type UserPlayerFoundSetting struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	On                   bool     `protobuf:"varint,2,opt,name=on,proto3" json:"on,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPlayerFoundSetting) Reset()         { *m = UserPlayerFoundSetting{} }
func (m *UserPlayerFoundSetting) String() string { return proto.CompactTextString(m) }
func (*UserPlayerFoundSetting) ProtoMessage()    {}
func (*UserPlayerFoundSetting) Descriptor() ([]byte, []int) {
	return fileDescriptor_player_found_5cd6d9d2287a4776, []int{6}
}
func (m *UserPlayerFoundSetting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPlayerFoundSetting.Unmarshal(m, b)
}
func (m *UserPlayerFoundSetting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPlayerFoundSetting.Marshal(b, m, deterministic)
}
func (dst *UserPlayerFoundSetting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPlayerFoundSetting.Merge(dst, src)
}
func (m *UserPlayerFoundSetting) XXX_Size() int {
	return xxx_messageInfo_UserPlayerFoundSetting.Size(m)
}
func (m *UserPlayerFoundSetting) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPlayerFoundSetting.DiscardUnknown(m)
}

var xxx_messageInfo_UserPlayerFoundSetting proto.InternalMessageInfo

func (m *UserPlayerFoundSetting) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserPlayerFoundSetting) GetOn() bool {
	if m != nil {
		return m.On
	}
	return false
}

type GetPlayerFoundSettingResp struct {
	Settings             []*UserPlayerFoundSetting `protobuf:"bytes,1,rep,name=settings,proto3" json:"settings,omitempty"`
	Type                 Settings                  `protobuf:"varint,2,opt,name=type,proto3,enum=playerfound.Settings" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetPlayerFoundSettingResp) Reset()         { *m = GetPlayerFoundSettingResp{} }
func (m *GetPlayerFoundSettingResp) String() string { return proto.CompactTextString(m) }
func (*GetPlayerFoundSettingResp) ProtoMessage()    {}
func (*GetPlayerFoundSettingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_player_found_5cd6d9d2287a4776, []int{7}
}
func (m *GetPlayerFoundSettingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayerFoundSettingResp.Unmarshal(m, b)
}
func (m *GetPlayerFoundSettingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayerFoundSettingResp.Marshal(b, m, deterministic)
}
func (dst *GetPlayerFoundSettingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayerFoundSettingResp.Merge(dst, src)
}
func (m *GetPlayerFoundSettingResp) XXX_Size() int {
	return xxx_messageInfo_GetPlayerFoundSettingResp.Size(m)
}
func (m *GetPlayerFoundSettingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayerFoundSettingResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayerFoundSettingResp proto.InternalMessageInfo

func (m *GetPlayerFoundSettingResp) GetSettings() []*UserPlayerFoundSetting {
	if m != nil {
		return m.Settings
	}
	return nil
}

func (m *GetPlayerFoundSettingResp) GetType() Settings {
	if m != nil {
		return m.Type
	}
	return Settings_PLAYER_FOUND
}

type UpdatePlayerFoundSettingReq struct {
	Setting              *UserPlayerFoundSetting `protobuf:"bytes,1,opt,name=setting,proto3" json:"setting,omitempty"`
	Type                 Settings                `protobuf:"varint,2,opt,name=type,proto3,enum=playerfound.Settings" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *UpdatePlayerFoundSettingReq) Reset()         { *m = UpdatePlayerFoundSettingReq{} }
func (m *UpdatePlayerFoundSettingReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePlayerFoundSettingReq) ProtoMessage()    {}
func (*UpdatePlayerFoundSettingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_player_found_5cd6d9d2287a4776, []int{8}
}
func (m *UpdatePlayerFoundSettingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePlayerFoundSettingReq.Unmarshal(m, b)
}
func (m *UpdatePlayerFoundSettingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePlayerFoundSettingReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePlayerFoundSettingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePlayerFoundSettingReq.Merge(dst, src)
}
func (m *UpdatePlayerFoundSettingReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePlayerFoundSettingReq.Size(m)
}
func (m *UpdatePlayerFoundSettingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePlayerFoundSettingReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePlayerFoundSettingReq proto.InternalMessageInfo

func (m *UpdatePlayerFoundSettingReq) GetSetting() *UserPlayerFoundSetting {
	if m != nil {
		return m.Setting
	}
	return nil
}

func (m *UpdatePlayerFoundSettingReq) GetType() Settings {
	if m != nil {
		return m.Type
	}
	return Settings_PLAYER_FOUND
}

type UpdatePlayerFoundSettingResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePlayerFoundSettingResp) Reset()         { *m = UpdatePlayerFoundSettingResp{} }
func (m *UpdatePlayerFoundSettingResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePlayerFoundSettingResp) ProtoMessage()    {}
func (*UpdatePlayerFoundSettingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_player_found_5cd6d9d2287a4776, []int{9}
}
func (m *UpdatePlayerFoundSettingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePlayerFoundSettingResp.Unmarshal(m, b)
}
func (m *UpdatePlayerFoundSettingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePlayerFoundSettingResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePlayerFoundSettingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePlayerFoundSettingResp.Merge(dst, src)
}
func (m *UpdatePlayerFoundSettingResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePlayerFoundSettingResp.Size(m)
}
func (m *UpdatePlayerFoundSettingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePlayerFoundSettingResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePlayerFoundSettingResp proto.InternalMessageInfo

type DelUserPostsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserPostsReq) Reset()         { *m = DelUserPostsReq{} }
func (m *DelUserPostsReq) String() string { return proto.CompactTextString(m) }
func (*DelUserPostsReq) ProtoMessage()    {}
func (*DelUserPostsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_player_found_5cd6d9d2287a4776, []int{10}
}
func (m *DelUserPostsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserPostsReq.Unmarshal(m, b)
}
func (m *DelUserPostsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserPostsReq.Marshal(b, m, deterministic)
}
func (dst *DelUserPostsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserPostsReq.Merge(dst, src)
}
func (m *DelUserPostsReq) XXX_Size() int {
	return xxx_messageInfo_DelUserPostsReq.Size(m)
}
func (m *DelUserPostsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserPostsReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserPostsReq proto.InternalMessageInfo

func (m *DelUserPostsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type DelUserPostsRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserPostsRsp) Reset()         { *m = DelUserPostsRsp{} }
func (m *DelUserPostsRsp) String() string { return proto.CompactTextString(m) }
func (*DelUserPostsRsp) ProtoMessage()    {}
func (*DelUserPostsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_player_found_5cd6d9d2287a4776, []int{11}
}
func (m *DelUserPostsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserPostsRsp.Unmarshal(m, b)
}
func (m *DelUserPostsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserPostsRsp.Marshal(b, m, deterministic)
}
func (dst *DelUserPostsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserPostsRsp.Merge(dst, src)
}
func (m *DelUserPostsRsp) XXX_Size() int {
	return xxx_messageInfo_DelUserPostsRsp.Size(m)
}
func (m *DelUserPostsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserPostsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserPostsRsp proto.InternalMessageInfo

type GetUserPostsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPostsReq) Reset()         { *m = GetUserPostsReq{} }
func (m *GetUserPostsReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPostsReq) ProtoMessage()    {}
func (*GetUserPostsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_player_found_5cd6d9d2287a4776, []int{12}
}
func (m *GetUserPostsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPostsReq.Unmarshal(m, b)
}
func (m *GetUserPostsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPostsReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPostsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPostsReq.Merge(dst, src)
}
func (m *GetUserPostsReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPostsReq.Size(m)
}
func (m *GetUserPostsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPostsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPostsReq proto.InternalMessageInfo

func (m *GetUserPostsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserPostsRsp struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PostIds              []string `protobuf:"bytes,2,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPostsRsp) Reset()         { *m = GetUserPostsRsp{} }
func (m *GetUserPostsRsp) String() string { return proto.CompactTextString(m) }
func (*GetUserPostsRsp) ProtoMessage()    {}
func (*GetUserPostsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_player_found_5cd6d9d2287a4776, []int{13}
}
func (m *GetUserPostsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPostsRsp.Unmarshal(m, b)
}
func (m *GetUserPostsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPostsRsp.Marshal(b, m, deterministic)
}
func (dst *GetUserPostsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPostsRsp.Merge(dst, src)
}
func (m *GetUserPostsRsp) XXX_Size() int {
	return xxx_messageInfo_GetUserPostsRsp.Size(m)
}
func (m *GetUserPostsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPostsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPostsRsp proto.InternalMessageInfo

func (m *GetUserPostsRsp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserPostsRsp) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

func init() {
	proto.RegisterType((*BatchGetUserPostsReq)(nil), "playerfound.BatchGetUserPostsReq")
	proto.RegisterType((*BatchGetUserPostsResp)(nil), "playerfound.BatchGetUserPostsResp")
	proto.RegisterType((*BatchSetUserPostsReq)(nil), "playerfound.BatchSetUserPostsReq")
	proto.RegisterType((*BatchSetUserPostsResp)(nil), "playerfound.BatchSetUserPostsResp")
	proto.RegisterType((*PlaymateUserPosts)(nil), "playerfound.PlaymateUserPosts")
	proto.RegisterType((*GetPlayerFoundSettingReq)(nil), "playerfound.GetPlayerFoundSettingReq")
	proto.RegisterType((*UserPlayerFoundSetting)(nil), "playerfound.UserPlayerFoundSetting")
	proto.RegisterType((*GetPlayerFoundSettingResp)(nil), "playerfound.GetPlayerFoundSettingResp")
	proto.RegisterType((*UpdatePlayerFoundSettingReq)(nil), "playerfound.UpdatePlayerFoundSettingReq")
	proto.RegisterType((*UpdatePlayerFoundSettingResp)(nil), "playerfound.UpdatePlayerFoundSettingResp")
	proto.RegisterType((*DelUserPostsReq)(nil), "playerfound.DelUserPostsReq")
	proto.RegisterType((*DelUserPostsRsp)(nil), "playerfound.DelUserPostsRsp")
	proto.RegisterType((*GetUserPostsReq)(nil), "playerfound.GetUserPostsReq")
	proto.RegisterType((*GetUserPostsRsp)(nil), "playerfound.GetUserPostsRsp")
	proto.RegisterEnum("playerfound.Settings", Settings_name, Settings_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PlayerFoundClient is the client API for PlayerFound service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PlayerFoundClient interface {
	GetPlayerFoundSetting(ctx context.Context, in *GetPlayerFoundSettingReq, opts ...grpc.CallOption) (*GetPlayerFoundSettingResp, error)
	UpdatePlayerFoundSetting(ctx context.Context, in *UpdatePlayerFoundSettingReq, opts ...grpc.CallOption) (*UpdatePlayerFoundSettingResp, error)
	DelUserPosts(ctx context.Context, in *DelUserPostsReq, opts ...grpc.CallOption) (*DelUserPostsRsp, error)
	BatchGetUserPosts(ctx context.Context, in *BatchGetUserPostsReq, opts ...grpc.CallOption) (*BatchGetUserPostsResp, error)
	BatchSetUserPosts(ctx context.Context, in *BatchSetUserPostsReq, opts ...grpc.CallOption) (*BatchSetUserPostsResp, error)
	GetUserPosts(ctx context.Context, in *GetUserPostsReq, opts ...grpc.CallOption) (*GetUserPostsRsp, error)
}

type playerFoundClient struct {
	cc *grpc.ClientConn
}

func NewPlayerFoundClient(cc *grpc.ClientConn) PlayerFoundClient {
	return &playerFoundClient{cc}
}

func (c *playerFoundClient) GetPlayerFoundSetting(ctx context.Context, in *GetPlayerFoundSettingReq, opts ...grpc.CallOption) (*GetPlayerFoundSettingResp, error) {
	out := new(GetPlayerFoundSettingResp)
	err := c.cc.Invoke(ctx, "/playerfound.PlayerFound/GetPlayerFoundSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *playerFoundClient) UpdatePlayerFoundSetting(ctx context.Context, in *UpdatePlayerFoundSettingReq, opts ...grpc.CallOption) (*UpdatePlayerFoundSettingResp, error) {
	out := new(UpdatePlayerFoundSettingResp)
	err := c.cc.Invoke(ctx, "/playerfound.PlayerFound/UpdatePlayerFoundSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *playerFoundClient) DelUserPosts(ctx context.Context, in *DelUserPostsReq, opts ...grpc.CallOption) (*DelUserPostsRsp, error) {
	out := new(DelUserPostsRsp)
	err := c.cc.Invoke(ctx, "/playerfound.PlayerFound/DelUserPosts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *playerFoundClient) BatchGetUserPosts(ctx context.Context, in *BatchGetUserPostsReq, opts ...grpc.CallOption) (*BatchGetUserPostsResp, error) {
	out := new(BatchGetUserPostsResp)
	err := c.cc.Invoke(ctx, "/playerfound.PlayerFound/BatchGetUserPosts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *playerFoundClient) BatchSetUserPosts(ctx context.Context, in *BatchSetUserPostsReq, opts ...grpc.CallOption) (*BatchSetUserPostsResp, error) {
	out := new(BatchSetUserPostsResp)
	err := c.cc.Invoke(ctx, "/playerfound.PlayerFound/BatchSetUserPosts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *playerFoundClient) GetUserPosts(ctx context.Context, in *GetUserPostsReq, opts ...grpc.CallOption) (*GetUserPostsRsp, error) {
	out := new(GetUserPostsRsp)
	err := c.cc.Invoke(ctx, "/playerfound.PlayerFound/GetUserPosts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PlayerFoundServer is the server API for PlayerFound service.
type PlayerFoundServer interface {
	GetPlayerFoundSetting(context.Context, *GetPlayerFoundSettingReq) (*GetPlayerFoundSettingResp, error)
	UpdatePlayerFoundSetting(context.Context, *UpdatePlayerFoundSettingReq) (*UpdatePlayerFoundSettingResp, error)
	DelUserPosts(context.Context, *DelUserPostsReq) (*DelUserPostsRsp, error)
	BatchGetUserPosts(context.Context, *BatchGetUserPostsReq) (*BatchGetUserPostsResp, error)
	BatchSetUserPosts(context.Context, *BatchSetUserPostsReq) (*BatchSetUserPostsResp, error)
	GetUserPosts(context.Context, *GetUserPostsReq) (*GetUserPostsRsp, error)
}

func RegisterPlayerFoundServer(s *grpc.Server, srv PlayerFoundServer) {
	s.RegisterService(&_PlayerFound_serviceDesc, srv)
}

func _PlayerFound_GetPlayerFoundSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlayerFoundSettingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlayerFoundServer).GetPlayerFoundSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/playerfound.PlayerFound/GetPlayerFoundSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlayerFoundServer).GetPlayerFoundSetting(ctx, req.(*GetPlayerFoundSettingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlayerFound_UpdatePlayerFoundSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePlayerFoundSettingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlayerFoundServer).UpdatePlayerFoundSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/playerfound.PlayerFound/UpdatePlayerFoundSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlayerFoundServer).UpdatePlayerFoundSetting(ctx, req.(*UpdatePlayerFoundSettingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlayerFound_DelUserPosts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelUserPostsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlayerFoundServer).DelUserPosts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/playerfound.PlayerFound/DelUserPosts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlayerFoundServer).DelUserPosts(ctx, req.(*DelUserPostsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlayerFound_BatchGetUserPosts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserPostsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlayerFoundServer).BatchGetUserPosts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/playerfound.PlayerFound/BatchGetUserPosts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlayerFoundServer).BatchGetUserPosts(ctx, req.(*BatchGetUserPostsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlayerFound_BatchSetUserPosts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetUserPostsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlayerFoundServer).BatchSetUserPosts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/playerfound.PlayerFound/BatchSetUserPosts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlayerFoundServer).BatchSetUserPosts(ctx, req.(*BatchSetUserPostsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlayerFound_GetUserPosts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPostsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlayerFoundServer).GetUserPosts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/playerfound.PlayerFound/GetUserPosts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlayerFoundServer).GetUserPosts(ctx, req.(*GetUserPostsReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PlayerFound_serviceDesc = grpc.ServiceDesc{
	ServiceName: "playerfound.PlayerFound",
	HandlerType: (*PlayerFoundServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPlayerFoundSetting",
			Handler:    _PlayerFound_GetPlayerFoundSetting_Handler,
		},
		{
			MethodName: "UpdatePlayerFoundSetting",
			Handler:    _PlayerFound_UpdatePlayerFoundSetting_Handler,
		},
		{
			MethodName: "DelUserPosts",
			Handler:    _PlayerFound_DelUserPosts_Handler,
		},
		{
			MethodName: "BatchGetUserPosts",
			Handler:    _PlayerFound_BatchGetUserPosts_Handler,
		},
		{
			MethodName: "BatchSetUserPosts",
			Handler:    _PlayerFound_BatchSetUserPosts_Handler,
		},
		{
			MethodName: "GetUserPosts",
			Handler:    _PlayerFound_GetUserPosts_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "player-found/player-found.proto",
}

func init() {
	proto.RegisterFile("player-found/player-found.proto", fileDescriptor_player_found_5cd6d9d2287a4776)
}

var fileDescriptor_player_found_5cd6d9d2287a4776 = []byte{
	// 534 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x55, 0x7f, 0x6f, 0xd2, 0x50,
	0x14, 0xa5, 0x40, 0x1c, 0xbb, 0xb0, 0x09, 0x2f, 0xa2, 0x1d, 0x92, 0x89, 0x6f, 0xd1, 0x74, 0x24,
	0x6b, 0x13, 0x8c, 0xff, 0x98, 0x4c, 0xdd, 0x9c, 0x23, 0x4b, 0x88, 0x92, 0x76, 0x98, 0x60, 0x4c,
	0x48, 0xa5, 0x4f, 0x46, 0xd2, 0xb5, 0x6f, 0x7d, 0xaf, 0x26, 0x7c, 0x82, 0x7d, 0x2f, 0x3f, 0x99,
	0xe9, 0x5b, 0xd1, 0xfe, 0x1c, 0x90, 0xfd, 0xf7, 0x7e, 0xdc, 0x73, 0xee, 0xb9, 0xe7, 0xde, 0xbe,
	0xc2, 0x0b, 0x6a, 0x9b, 0x0b, 0xe2, 0x1d, 0xfd, 0x72, 0x7d, 0xc7, 0xd2, 0xa2, 0x1b, 0x95, 0x7a,
	0x2e, 0x77, 0x51, 0xf5, 0xee, 0x4c, 0x1c, 0xe1, 0x2e, 0x3c, 0x39, 0x35, 0xf9, 0xf4, 0xaa, 0x4f,
	0xf8, 0x88, 0x11, 0x6f, 0xe8, 0x32, 0xce, 0x74, 0x72, 0x83, 0x10, 0x94, 0xfd, 0xb9, 0xc5, 0x64,
	0xa9, 0x53, 0x52, 0x76, 0x74, 0xb1, 0xc6, 0xdf, 0xa0, 0x99, 0x11, 0xcb, 0x28, 0x3a, 0x06, 0xf0,
	0x19, 0xf1, 0x26, 0x34, 0x38, 0x11, 0x90, 0x6a, 0x6f, 0x5f, 0x8d, 0xa4, 0x51, 0x87, 0xb6, 0xb9,
	0xb8, 0x36, 0x39, 0xf9, 0x8f, 0xdb, 0xf6, 0x97, 0x4b, 0x3c, 0x0a, 0x35, 0x18, 0x09, 0x0d, 0x0f,
	0xa4, 0x7d, 0x16, 0xca, 0x35, 0x12, 0x72, 0xf1, 0x47, 0x68, 0xa4, 0x80, 0xa8, 0x0e, 0x25, 0x7f,
	0x6e, 0xc9, 0x52, 0x47, 0x52, 0x76, 0xf4, 0x60, 0x89, 0xf6, 0xa0, 0x12, 0x64, 0x9e, 0x04, 0x36,
	0x14, 0x3b, 0x25, 0x65, 0x5b, 0xdf, 0x0a, 0xf6, 0x17, 0x16, 0xc3, 0x63, 0x90, 0xfb, 0x84, 0x0f,
	0x85, 0x92, 0xf3, 0x40, 0x89, 0x41, 0x38, 0x9f, 0x3b, 0xb3, 0x1c, 0xe7, 0xd0, 0x21, 0x94, 0xf9,
	0x82, 0x12, 0xb9, 0xd8, 0x91, 0x94, 0xdd, 0x5e, 0x33, 0x56, 0x43, 0x08, 0x65, 0xba, 0x08, 0xc1,
	0xef, 0xe0, 0xa9, 0x10, 0x95, 0xe2, 0xce, 0x50, 0xb8, 0x0b, 0x45, 0xd7, 0x11, 0xa4, 0x15, 0xbd,
	0xe8, 0x3a, 0xf8, 0x56, 0x82, 0xbd, 0x1c, 0x5d, 0x8c, 0xa2, 0x0f, 0x50, 0x61, 0x61, 0xae, 0xd0,
	0xcc, 0x83, 0x98, 0x90, 0xec, 0xb4, 0xfa, 0x3f, 0xd0, 0x26, 0x55, 0xdc, 0x4a, 0xf0, 0x7c, 0x44,
	0x2d, 0x93, 0x93, 0x6c, 0x93, 0x8e, 0x61, 0x2b, 0xa4, 0x15, 0xf5, 0xac, 0x29, 0x65, 0x89, 0xd9,
	0x44, 0xc9, 0x3e, 0xb4, 0xf3, 0x85, 0x30, 0x8a, 0x0f, 0xe0, 0xf1, 0x19, 0xb1, 0x63, 0x73, 0x97,
	0x32, 0x1a, 0x37, 0x12, 0x41, 0x77, 0xb8, 0xe4, 0x37, 0x93, 0xc6, 0xbd, 0x4f, 0x04, 0x31, 0xba,
	0xd1, 0x9c, 0x75, 0xbb, 0x50, 0x59, 0x96, 0x83, 0xea, 0x50, 0x1b, 0x0e, 0x4e, 0xc6, 0x9f, 0xf5,
	0xc9, 0xf9, 0xd7, 0xd1, 0x97, 0xb3, 0x7a, 0x01, 0xd5, 0xa0, 0xf2, 0xe9, 0xe2, 0x72, 0x3c, 0xb9,
	0x3c, 0xe9, 0xd7, 0xa5, 0xde, 0x9f, 0x32, 0x54, 0x23, 0x35, 0xa2, 0x2b, 0x68, 0x66, 0xce, 0x02,
	0x7a, 0x15, 0xb3, 0x2b, 0x6f, 0x8e, 0x5b, 0xaf, 0xd7, 0x09, 0x63, 0x14, 0x17, 0xd0, 0x0d, 0xc8,
	0x79, 0x16, 0x23, 0x25, 0xde, 0xd7, 0xfc, 0x91, 0x68, 0x1d, 0xae, 0x19, 0x29, 0x52, 0x0e, 0xa0,
	0x16, 0x6d, 0x08, 0x6a, 0xc7, 0xc0, 0x89, 0x86, 0xb6, 0xee, 0xb9, 0x15, 0x6c, 0x3f, 0xa0, 0x91,
	0x7a, 0xd8, 0xd0, 0xcb, 0x18, 0x28, 0xeb, 0x91, 0x6c, 0xe1, 0x55, 0x21, 0x31, 0x76, 0x63, 0x05,
	0xbb, 0xb1, 0x9a, 0xdd, 0x48, 0xb3, 0x0f, 0xa0, 0x16, 0x93, 0xdd, 0x4e, 0xb6, 0xed, 0x1e, 0x27,
	0x12, 0xb3, 0x89, 0x0b, 0xa7, 0xda, 0xf7, 0xa3, 0x99, 0x6b, 0x9b, 0xce, 0x4c, 0x7d, 0xdb, 0xe3,
	0x5c, 0x9d, 0xba, 0xd7, 0x9a, 0xf8, 0x69, 0x4c, 0x5d, 0x5b, 0x63, 0xc4, 0xfb, 0x3d, 0x9f, 0x12,
	0xa6, 0x45, 0x28, 0x7e, 0x3e, 0x12, 0xd7, 0x6f, 0xfe, 0x06, 0x00, 0x00, 0xff, 0xff, 0x29, 0x2e,
	0xd0, 0x19, 0x76, 0x06, 0x00, 0x00,
}
