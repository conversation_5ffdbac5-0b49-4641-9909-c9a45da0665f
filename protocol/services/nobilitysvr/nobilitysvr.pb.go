// Code generated by protoc-gen-gogo.
// source: src/nobilitysvr/nobilitysvr.proto
// DO NOT EDIT!

/*
	Package NobilitySvr is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/nobilitysvr/nobilitysvr.proto

	It has these top-level messages:
		NobilityRecored
		NoilityInvestInfo
		NobilityInfo
		GetNobilityInfoReq
		GetNobilityInfoResp
		BatchGetNobilityInfoReq
		BatchGetNobilityInfoResp
		SetInvisibleStatusReq
		SetInvisibleStatusResp
		GetInvisibleStatusReq
		GetInvisibleStatusResp
		BatchGetInvisibleStatusReq
		BatchGetInvisibleStatusResp
		GetTrumpetLeftCntReq
		GetTrumpetLeftCntResp
		ReduceTrumpetLeftCntReq
		ReduceTrumpetLeftCntResp
		NobilityNotifyMsg
		AddRechargeReq
		AddRechargeResp
		GetRechargeResultInfoReq
		GetRechargeResultInfoResp
		AddUserNobilityValueReq
		AddUserNobilityValueResp
		GetMemberNobilityInfoReq
		GetMemberNobilityInfoResp
		PushNobilityLevelUpMsgReq
		PushNobilityLevelUpMsgResp
		AddRechargeTbeanReq
		AddRechargeTbeanResp
		TempNobilityInfo
		TempNobilityAwardInfo
		AddTempNobilityReq
		AddTempNobilityResp
		GetTempNobilityListReq
		GetTempNobilityListResp
		GetConsumeOrderCountReq
		GetConsumeOrderCountResp
		GetConsumeOrderListReq
		GetConsumeOrderListResp
		FixConsumeOrderReq
		FixConsumeOrderResp
*/
package NobilitySvr

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import math3 "math"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import math4 "math"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type KfkWorkerType int32

const (
	KfkWorkerType_PresentWorker KfkWorkerType = 1
	KfkWorkerType_CatFishWoker  KfkWorkerType = 2
)

var KfkWorkerType_name = map[int32]string{
	1: "PresentWorker",
	2: "CatFishWoker",
}
var KfkWorkerType_value = map[string]int32{
	"PresentWorker": 1,
	"CatFishWoker":  2,
}

func (x KfkWorkerType) Enum() *KfkWorkerType {
	p := new(KfkWorkerType)
	*p = x
	return p
}
func (x KfkWorkerType) String() string {
	return proto.EnumName(KfkWorkerType_name, int32(x))
}
func (x *KfkWorkerType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(KfkWorkerType_value, data, "KfkWorkerType")
	if err != nil {
		return err
	}
	*x = KfkWorkerType(value)
	return nil
}
func (KfkWorkerType) EnumDescriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{0} }

type InvisibleType int32

const (
	InvisibleType_ENUM_NotInvisible InvisibleType = 0
	InvisibleType_ENUM_Invisible    InvisibleType = 1
)

var InvisibleType_name = map[int32]string{
	0: "ENUM_NotInvisible",
	1: "ENUM_Invisible",
}
var InvisibleType_value = map[string]int32{
	"ENUM_NotInvisible": 0,
	"ENUM_Invisible":    1,
}

func (x InvisibleType) Enum() *InvisibleType {
	p := new(InvisibleType)
	*p = x
	return p
}
func (x InvisibleType) String() string {
	return proto.EnumName(InvisibleType_name, int32(x))
}
func (x *InvisibleType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(InvisibleType_value, data, "InvisibleType")
	if err != nil {
		return err
	}
	*x = InvisibleType(value)
	return nil
}
func (InvisibleType) EnumDescriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{1} }

type NobilityLevelType int32

const (
	NobilityLevelType_Nobility_Level_Invalid           NobilityLevelType = 0
	NobilityLevelType_Nobility_Level_Baron             NobilityLevelType = 1
	NobilityLevelType_Nobility_Level_Viscount          NobilityLevelType = 2
	NobilityLevelType_Nobility_Level_Earl              NobilityLevelType = 3
	NobilityLevelType_Nobility_Level_Marquis           NobilityLevelType = 4
	NobilityLevelType_Nobility_Level_Duke              NobilityLevelType = 5
	NobilityLevelType_Nobility_Level_Commandery_Prince NobilityLevelType = 6
	NobilityLevelType_Nobility_Level_Prince            NobilityLevelType = 7
	NobilityLevelType_Nobility_Level_King              NobilityLevelType = 8
	NobilityLevelType_Nobility_Level_God               NobilityLevelType = 9
	NobilityLevelType_Nobility_Level_Young             NobilityLevelType = 10
)

var NobilityLevelType_name = map[int32]string{
	0:  "Nobility_Level_Invalid",
	1:  "Nobility_Level_Baron",
	2:  "Nobility_Level_Viscount",
	3:  "Nobility_Level_Earl",
	4:  "Nobility_Level_Marquis",
	5:  "Nobility_Level_Duke",
	6:  "Nobility_Level_Commandery_Prince",
	7:  "Nobility_Level_Prince",
	8:  "Nobility_Level_King",
	9:  "Nobility_Level_God",
	10: "Nobility_Level_Young",
}
var NobilityLevelType_value = map[string]int32{
	"Nobility_Level_Invalid":           0,
	"Nobility_Level_Baron":             1,
	"Nobility_Level_Viscount":          2,
	"Nobility_Level_Earl":              3,
	"Nobility_Level_Marquis":           4,
	"Nobility_Level_Duke":              5,
	"Nobility_Level_Commandery_Prince": 6,
	"Nobility_Level_Prince":            7,
	"Nobility_Level_King":              8,
	"Nobility_Level_God":               9,
	"Nobility_Level_Young":             10,
}

func (x NobilityLevelType) Enum() *NobilityLevelType {
	p := new(NobilityLevelType)
	*p = x
	return p
}
func (x NobilityLevelType) String() string {
	return proto.EnumName(NobilityLevelType_name, int32(x))
}
func (x *NobilityLevelType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(NobilityLevelType_value, data, "NobilityLevelType")
	if err != nil {
		return err
	}
	*x = NobilityLevelType(value)
	return nil
}
func (NobilityLevelType) EnumDescriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{2} }

type NobilityRecored struct {
	Ts    uint32 `protobuf:"varint,1,req,name=ts" json:"ts"`
	Level uint32 `protobuf:"varint,2,req,name=level" json:"level"`
}

func (m *NobilityRecored) Reset()                    { *m = NobilityRecored{} }
func (m *NobilityRecored) String() string            { return proto.CompactTextString(m) }
func (*NobilityRecored) ProtoMessage()               {}
func (*NobilityRecored) Descriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{0} }

func (m *NobilityRecored) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *NobilityRecored) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

// 贵族待充值差额
type NoilityInvestInfo struct {
	Level    uint32 `protobuf:"varint,1,opt,name=level" json:"level"`
	GapValue int64  `protobuf:"varint,2,opt,name=gap_value,json=gapValue" json:"gap_value"`
}

func (m *NoilityInvestInfo) Reset()                    { *m = NoilityInvestInfo{} }
func (m *NoilityInvestInfo) String() string            { return proto.CompactTextString(m) }
func (*NoilityInvestInfo) ProtoMessage()               {}
func (*NoilityInvestInfo) Descriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{1} }

func (m *NoilityInvestInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *NoilityInvestInfo) GetGapValue() int64 {
	if m != nil {
		return m.GapValue
	}
	return 0
}

type NobilityInfo struct {
	Value                uint64               `protobuf:"varint,1,req,name=value" json:"value"`
	KeepValue            uint64               `protobuf:"varint,2,req,name=keep_value,json=keepValue" json:"keep_value"`
	Level                uint32               `protobuf:"varint,3,req,name=level" json:"level"`
	CycleTs              uint32               `protobuf:"varint,4,req,name=cycle_ts,json=cycleTs" json:"cycle_ts"`
	Uid                  uint32               `protobuf:"varint,5,req,name=uid" json:"uid"`
	Invisible            bool                 `protobuf:"varint,6,opt,name=invisible" json:"invisible"`
	WaitCostValue        uint32               `protobuf:"varint,7,opt,name=wait_cost_value,json=waitCostValue" json:"wait_cost_value"`
	TotalWaitCostValue   uint32               `protobuf:"varint,8,opt,name=total_wait_cost_value,json=totalWaitCostValue" json:"total_wait_cost_value"`
	LevelName            string               `protobuf:"bytes,9,opt,name=level_name,json=levelName" json:"level_name"`
	InvestInfoList       []*NoilityInvestInfo `protobuf:"bytes,10,rep,name=invest_info_list,json=investInfoList" json:"invest_info_list,omitempty"`
	TempNobilityRemainTs uint32               `protobuf:"varint,11,opt,name=temp_nobility_remain_ts,json=tempNobilityRemainTs" json:"temp_nobility_remain_ts"`
	RealLevel            uint32               `protobuf:"varint,12,opt,name=real_level,json=realLevel" json:"real_level"`
	ExtentCnt            uint32               `protobuf:"varint,13,opt,name=extent_cnt,json=extentCnt" json:"extent_cnt"`
	LackExtentVal        uint32               `protobuf:"varint,14,opt,name=lack_extent_val,json=lackExtentVal" json:"lack_extent_val"`
	FLevel               float32              `protobuf:"fixed32,15,opt,name=f_level,json=fLevel" json:"f_level"`
	LevelValue           uint64               `protobuf:"varint,16,opt,name=level_value,json=levelValue" json:"level_value"`
}

func (m *NobilityInfo) Reset()                    { *m = NobilityInfo{} }
func (m *NobilityInfo) String() string            { return proto.CompactTextString(m) }
func (*NobilityInfo) ProtoMessage()               {}
func (*NobilityInfo) Descriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{2} }

func (m *NobilityInfo) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *NobilityInfo) GetKeepValue() uint64 {
	if m != nil {
		return m.KeepValue
	}
	return 0
}

func (m *NobilityInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *NobilityInfo) GetCycleTs() uint32 {
	if m != nil {
		return m.CycleTs
	}
	return 0
}

func (m *NobilityInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NobilityInfo) GetInvisible() bool {
	if m != nil {
		return m.Invisible
	}
	return false
}

func (m *NobilityInfo) GetWaitCostValue() uint32 {
	if m != nil {
		return m.WaitCostValue
	}
	return 0
}

func (m *NobilityInfo) GetTotalWaitCostValue() uint32 {
	if m != nil {
		return m.TotalWaitCostValue
	}
	return 0
}

func (m *NobilityInfo) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *NobilityInfo) GetInvestInfoList() []*NoilityInvestInfo {
	if m != nil {
		return m.InvestInfoList
	}
	return nil
}

func (m *NobilityInfo) GetTempNobilityRemainTs() uint32 {
	if m != nil {
		return m.TempNobilityRemainTs
	}
	return 0
}

func (m *NobilityInfo) GetRealLevel() uint32 {
	if m != nil {
		return m.RealLevel
	}
	return 0
}

func (m *NobilityInfo) GetExtentCnt() uint32 {
	if m != nil {
		return m.ExtentCnt
	}
	return 0
}

func (m *NobilityInfo) GetLackExtentVal() uint32 {
	if m != nil {
		return m.LackExtentVal
	}
	return 0
}

func (m *NobilityInfo) GetFLevel() float32 {
	if m != nil {
		return m.FLevel
	}
	return 0
}

func (m *NobilityInfo) GetLevelValue() uint64 {
	if m != nil {
		return m.LevelValue
	}
	return 0
}

type GetNobilityInfoReq struct {
	NeedRecoreds bool   `protobuf:"varint,1,opt,name=need_recoreds,json=needRecoreds" json:"need_recoreds"`
	Source       string `protobuf:"bytes,2,opt,name=source" json:"source"`
	Uid          uint32 `protobuf:"varint,3,opt,name=uid" json:"uid"`
}

func (m *GetNobilityInfoReq) Reset()                    { *m = GetNobilityInfoReq{} }
func (m *GetNobilityInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetNobilityInfoReq) ProtoMessage()               {}
func (*GetNobilityInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{3} }

func (m *GetNobilityInfoReq) GetNeedRecoreds() bool {
	if m != nil {
		return m.NeedRecoreds
	}
	return false
}

func (m *GetNobilityInfoReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *GetNobilityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetNobilityInfoResp struct {
	Info    *NobilityInfo      `protobuf:"bytes,1,req,name=info" json:"info,omitempty"`
	Records []*NobilityRecored `protobuf:"bytes,2,rep,name=records" json:"records,omitempty"`
}

func (m *GetNobilityInfoResp) Reset()                    { *m = GetNobilityInfoResp{} }
func (m *GetNobilityInfoResp) String() string            { return proto.CompactTextString(m) }
func (*GetNobilityInfoResp) ProtoMessage()               {}
func (*GetNobilityInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{4} }

func (m *GetNobilityInfoResp) GetInfo() *NobilityInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetNobilityInfoResp) GetRecords() []*NobilityRecored {
	if m != nil {
		return m.Records
	}
	return nil
}

type BatchGetNobilityInfoReq struct {
	Uids []uint32 `protobuf:"varint,1,rep,name=uids" json:"uids,omitempty"`
}

func (m *BatchGetNobilityInfoReq) Reset()         { *m = BatchGetNobilityInfoReq{} }
func (m *BatchGetNobilityInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetNobilityInfoReq) ProtoMessage()    {}
func (*BatchGetNobilityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{5}
}

func (m *BatchGetNobilityInfoReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type BatchGetNobilityInfoResp struct {
	NobilityInfos []*NobilityInfo `protobuf:"bytes,1,rep,name=nobility_infos,json=nobilityInfos" json:"nobility_infos,omitempty"`
}

func (m *BatchGetNobilityInfoResp) Reset()         { *m = BatchGetNobilityInfoResp{} }
func (m *BatchGetNobilityInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetNobilityInfoResp) ProtoMessage()    {}
func (*BatchGetNobilityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{6}
}

func (m *BatchGetNobilityInfoResp) GetNobilityInfos() []*NobilityInfo {
	if m != nil {
		return m.NobilityInfos
	}
	return nil
}

type SetInvisibleStatusReq struct {
	Status     int32  `protobuf:"varint,1,req,name=status" json:"status"`
	StatusType uint32 `protobuf:"varint,2,opt,name=status_type,json=statusType" json:"status_type"`
}

func (m *SetInvisibleStatusReq) Reset()                    { *m = SetInvisibleStatusReq{} }
func (m *SetInvisibleStatusReq) String() string            { return proto.CompactTextString(m) }
func (*SetInvisibleStatusReq) ProtoMessage()               {}
func (*SetInvisibleStatusReq) Descriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{7} }

func (m *SetInvisibleStatusReq) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *SetInvisibleStatusReq) GetStatusType() uint32 {
	if m != nil {
		return m.StatusType
	}
	return 0
}

type SetInvisibleStatusResp struct {
	Status int32 `protobuf:"varint,1,req,name=status" json:"status"`
}

func (m *SetInvisibleStatusResp) Reset()         { *m = SetInvisibleStatusResp{} }
func (m *SetInvisibleStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetInvisibleStatusResp) ProtoMessage()    {}
func (*SetInvisibleStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{8}
}

func (m *SetInvisibleStatusResp) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type GetInvisibleStatusReq struct {
}

func (m *GetInvisibleStatusReq) Reset()                    { *m = GetInvisibleStatusReq{} }
func (m *GetInvisibleStatusReq) String() string            { return proto.CompactTextString(m) }
func (*GetInvisibleStatusReq) ProtoMessage()               {}
func (*GetInvisibleStatusReq) Descriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{9} }

type GetInvisibleStatusResp struct {
	Status int32 `protobuf:"varint,1,req,name=status" json:"status"`
}

func (m *GetInvisibleStatusResp) Reset()         { *m = GetInvisibleStatusResp{} }
func (m *GetInvisibleStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetInvisibleStatusResp) ProtoMessage()    {}
func (*GetInvisibleStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{10}
}

func (m *GetInvisibleStatusResp) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type BatchGetInvisibleStatusReq struct {
	Uids []uint32 `protobuf:"varint,1,rep,name=uids" json:"uids,omitempty"`
}

func (m *BatchGetInvisibleStatusReq) Reset()         { *m = BatchGetInvisibleStatusReq{} }
func (m *BatchGetInvisibleStatusReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetInvisibleStatusReq) ProtoMessage()    {}
func (*BatchGetInvisibleStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{11}
}

func (m *BatchGetInvisibleStatusReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type BatchGetInvisibleStatusResp struct {
	VisibleStatusList []bool `protobuf:"varint,1,rep,name=visible_status_list,json=visibleStatusList" json:"visible_status_list,omitempty"`
}

func (m *BatchGetInvisibleStatusResp) Reset()         { *m = BatchGetInvisibleStatusResp{} }
func (m *BatchGetInvisibleStatusResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetInvisibleStatusResp) ProtoMessage()    {}
func (*BatchGetInvisibleStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{12}
}

func (m *BatchGetInvisibleStatusResp) GetVisibleStatusList() []bool {
	if m != nil {
		return m.VisibleStatusList
	}
	return nil
}

type GetTrumpetLeftCntReq struct {
	PrivilegeId uint32 `protobuf:"varint,1,req,name=privilege_id,json=privilegeId" json:"privilege_id"`
	CycleTs     uint32 `protobuf:"varint,2,req,name=cycle_ts,json=cycleTs" json:"cycle_ts"`
}

func (m *GetTrumpetLeftCntReq) Reset()                    { *m = GetTrumpetLeftCntReq{} }
func (m *GetTrumpetLeftCntReq) String() string            { return proto.CompactTextString(m) }
func (*GetTrumpetLeftCntReq) ProtoMessage()               {}
func (*GetTrumpetLeftCntReq) Descriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{13} }

func (m *GetTrumpetLeftCntReq) GetPrivilegeId() uint32 {
	if m != nil {
		return m.PrivilegeId
	}
	return 0
}

func (m *GetTrumpetLeftCntReq) GetCycleTs() uint32 {
	if m != nil {
		return m.CycleTs
	}
	return 0
}

type GetTrumpetLeftCntResp struct {
	LeftCnt uint32 `protobuf:"varint,1,opt,name=left_cnt,json=leftCnt" json:"left_cnt"`
}

func (m *GetTrumpetLeftCntResp) Reset()         { *m = GetTrumpetLeftCntResp{} }
func (m *GetTrumpetLeftCntResp) String() string { return proto.CompactTextString(m) }
func (*GetTrumpetLeftCntResp) ProtoMessage()    {}
func (*GetTrumpetLeftCntResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{14}
}

func (m *GetTrumpetLeftCntResp) GetLeftCnt() uint32 {
	if m != nil {
		return m.LeftCnt
	}
	return 0
}

type ReduceTrumpetLeftCntReq struct {
	PrivilegeId uint32 `protobuf:"varint,1,req,name=privilege_id,json=privilegeId" json:"privilege_id"`
	CycleTs     uint32 `protobuf:"varint,2,req,name=cycle_ts,json=cycleTs" json:"cycle_ts"`
}

func (m *ReduceTrumpetLeftCntReq) Reset()         { *m = ReduceTrumpetLeftCntReq{} }
func (m *ReduceTrumpetLeftCntReq) String() string { return proto.CompactTextString(m) }
func (*ReduceTrumpetLeftCntReq) ProtoMessage()    {}
func (*ReduceTrumpetLeftCntReq) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{15}
}

func (m *ReduceTrumpetLeftCntReq) GetPrivilegeId() uint32 {
	if m != nil {
		return m.PrivilegeId
	}
	return 0
}

func (m *ReduceTrumpetLeftCntReq) GetCycleTs() uint32 {
	if m != nil {
		return m.CycleTs
	}
	return 0
}

type ReduceTrumpetLeftCntResp struct {
	LeftCnt uint32 `protobuf:"varint,1,opt,name=left_cnt,json=leftCnt" json:"left_cnt"`
}

func (m *ReduceTrumpetLeftCntResp) Reset()         { *m = ReduceTrumpetLeftCntResp{} }
func (m *ReduceTrumpetLeftCntResp) String() string { return proto.CompactTextString(m) }
func (*ReduceTrumpetLeftCntResp) ProtoMessage()    {}
func (*ReduceTrumpetLeftCntResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{16}
}

func (m *ReduceTrumpetLeftCntResp) GetLeftCnt() uint32 {
	if m != nil {
		return m.LeftCnt
	}
	return 0
}

type NobilityNotifyMsg struct {
	Ty    uint32 `protobuf:"varint,1,req,name=ty" json:"ty"`
	Uid   uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	Level uint32 `protobuf:"varint,3,req,name=level" json:"level"`
	Day   uint32 `protobuf:"varint,4,opt,name=day" json:"day"`
	Left  uint32 `protobuf:"varint,5,opt,name=left" json:"left"`
}

func (m *NobilityNotifyMsg) Reset()                    { *m = NobilityNotifyMsg{} }
func (m *NobilityNotifyMsg) String() string            { return proto.CompactTextString(m) }
func (*NobilityNotifyMsg) ProtoMessage()               {}
func (*NobilityNotifyMsg) Descriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{17} }

func (m *NobilityNotifyMsg) GetTy() uint32 {
	if m != nil {
		return m.Ty
	}
	return 0
}

func (m *NobilityNotifyMsg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NobilityNotifyMsg) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *NobilityNotifyMsg) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

func (m *NobilityNotifyMsg) GetLeft() uint32 {
	if m != nil {
		return m.Left
	}
	return 0
}

type AddRechargeReq struct {
	AddValue  uint32 `protobuf:"varint,1,req,name=add_value,json=addValue" json:"add_value"`
	GoalLevel uint32 `protobuf:"varint,2,req,name=goal_level,json=goalLevel" json:"goal_level"`
	Uid       uint32 `protobuf:"varint,3,opt,name=uid" json:"uid"`
}

func (m *AddRechargeReq) Reset()                    { *m = AddRechargeReq{} }
func (m *AddRechargeReq) String() string            { return proto.CompactTextString(m) }
func (*AddRechargeReq) ProtoMessage()               {}
func (*AddRechargeReq) Descriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{18} }

func (m *AddRechargeReq) GetAddValue() uint32 {
	if m != nil {
		return m.AddValue
	}
	return 0
}

func (m *AddRechargeReq) GetGoalLevel() uint32 {
	if m != nil {
		return m.GoalLevel
	}
	return 0
}

func (m *AddRechargeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type AddRechargeResp struct {
}

func (m *AddRechargeResp) Reset()                    { *m = AddRechargeResp{} }
func (m *AddRechargeResp) String() string            { return proto.CompactTextString(m) }
func (*AddRechargeResp) ProtoMessage()               {}
func (*AddRechargeResp) Descriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{19} }

type GetRechargeResultInfoReq struct {
	AddValue  uint32 `protobuf:"varint,1,req,name=add_value,json=addValue" json:"add_value"`
	GoalLevel uint32 `protobuf:"varint,2,req,name=goal_level,json=goalLevel" json:"goal_level"`
	Uid       uint32 `protobuf:"varint,3,opt,name=uid" json:"uid"`
}

func (m *GetRechargeResultInfoReq) Reset()         { *m = GetRechargeResultInfoReq{} }
func (m *GetRechargeResultInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetRechargeResultInfoReq) ProtoMessage()    {}
func (*GetRechargeResultInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{20}
}

func (m *GetRechargeResultInfoReq) GetAddValue() uint32 {
	if m != nil {
		return m.AddValue
	}
	return 0
}

func (m *GetRechargeResultInfoReq) GetGoalLevel() uint32 {
	if m != nil {
		return m.GoalLevel
	}
	return 0
}

func (m *GetRechargeResultInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetRechargeResultInfoResp struct {
	CurrInfo   *NobilityInfo `protobuf:"bytes,1,req,name=curr_info,json=currInfo" json:"curr_info,omitempty"`
	ResultInfo *NobilityInfo `protobuf:"bytes,2,req,name=result_info,json=resultInfo" json:"result_info,omitempty"`
}

func (m *GetRechargeResultInfoResp) Reset()         { *m = GetRechargeResultInfoResp{} }
func (m *GetRechargeResultInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetRechargeResultInfoResp) ProtoMessage()    {}
func (*GetRechargeResultInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{21}
}

func (m *GetRechargeResultInfoResp) GetCurrInfo() *NobilityInfo {
	if m != nil {
		return m.CurrInfo
	}
	return nil
}

func (m *GetRechargeResultInfoResp) GetResultInfo() *NobilityInfo {
	if m != nil {
		return m.ResultInfo
	}
	return nil
}

type AddUserNobilityValueReq struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Value   uint64 `protobuf:"varint,2,req,name=value" json:"value"`
	OrderId string `protobuf:"bytes,3,req,name=order_id,json=orderId" json:"order_id"`
	Source  string `protobuf:"bytes,4,req,name=source" json:"source"`
}

func (m *AddUserNobilityValueReq) Reset()         { *m = AddUserNobilityValueReq{} }
func (m *AddUserNobilityValueReq) String() string { return proto.CompactTextString(m) }
func (*AddUserNobilityValueReq) ProtoMessage()    {}
func (*AddUserNobilityValueReq) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{22}
}

func (m *AddUserNobilityValueReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserNobilityValueReq) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *AddUserNobilityValueReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AddUserNobilityValueReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

type AddUserNobilityValueResp struct {
}

func (m *AddUserNobilityValueResp) Reset()         { *m = AddUserNobilityValueResp{} }
func (m *AddUserNobilityValueResp) String() string { return proto.CompactTextString(m) }
func (*AddUserNobilityValueResp) ProtoMessage()    {}
func (*AddUserNobilityValueResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{23}
}

type GetMemberNobilityInfoReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetMemberNobilityInfoReq) Reset()         { *m = GetMemberNobilityInfoReq{} }
func (m *GetMemberNobilityInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetMemberNobilityInfoReq) ProtoMessage()    {}
func (*GetMemberNobilityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{24}
}

func (m *GetMemberNobilityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMemberNobilityInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetMemberNobilityInfoResp struct {
	NobilityLevel uint32 `protobuf:"varint,1,req,name=nobility_level,json=nobilityLevel" json:"nobility_level"`
	Invisible     bool   `protobuf:"varint,2,req,name=invisible" json:"invisible"`
}

func (m *GetMemberNobilityInfoResp) Reset()         { *m = GetMemberNobilityInfoResp{} }
func (m *GetMemberNobilityInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetMemberNobilityInfoResp) ProtoMessage()    {}
func (*GetMemberNobilityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{25}
}

func (m *GetMemberNobilityInfoResp) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *GetMemberNobilityInfoResp) GetInvisible() bool {
	if m != nil {
		return m.Invisible
	}
	return false
}

type PushNobilityLevelUpMsgReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	LevelName string `protobuf:"bytes,2,req,name=level_name,json=levelName" json:"level_name"`
	ChannelId uint32 `protobuf:"varint,3,req,name=channel_id,json=channelId" json:"channel_id"`
	Level     uint32 `protobuf:"varint,4,req,name=level" json:"level"`
	Trigger   uint32 `protobuf:"varint,5,opt,name=trigger" json:"trigger"`
}

func (m *PushNobilityLevelUpMsgReq) Reset()         { *m = PushNobilityLevelUpMsgReq{} }
func (m *PushNobilityLevelUpMsgReq) String() string { return proto.CompactTextString(m) }
func (*PushNobilityLevelUpMsgReq) ProtoMessage()    {}
func (*PushNobilityLevelUpMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{26}
}

func (m *PushNobilityLevelUpMsgReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PushNobilityLevelUpMsgReq) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *PushNobilityLevelUpMsgReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PushNobilityLevelUpMsgReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *PushNobilityLevelUpMsgReq) GetTrigger() uint32 {
	if m != nil {
		return m.Trigger
	}
	return 0
}

type PushNobilityLevelUpMsgResp struct {
}

func (m *PushNobilityLevelUpMsgResp) Reset()         { *m = PushNobilityLevelUpMsgResp{} }
func (m *PushNobilityLevelUpMsgResp) String() string { return proto.CompactTextString(m) }
func (*PushNobilityLevelUpMsgResp) ProtoMessage()    {}
func (*PushNobilityLevelUpMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{27}
}

type AddRechargeTbeanReq struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Num        int64  `protobuf:"varint,2,req,name=num" json:"num"`
	OrderId    string `protobuf:"bytes,3,req,name=orderId" json:"orderId"`
	ChargeType string `protobuf:"bytes,4,req,name=chargeType" json:"chargeType"`
	PayChannel string `protobuf:"bytes,5,req,name=payChannel" json:"payChannel"`
	ChargeTime string `protobuf:"bytes,6,req,name=chargeTime" json:"chargeTime"`
}

func (m *AddRechargeTbeanReq) Reset()                    { *m = AddRechargeTbeanReq{} }
func (m *AddRechargeTbeanReq) String() string            { return proto.CompactTextString(m) }
func (*AddRechargeTbeanReq) ProtoMessage()               {}
func (*AddRechargeTbeanReq) Descriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{28} }

func (m *AddRechargeTbeanReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddRechargeTbeanReq) GetNum() int64 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *AddRechargeTbeanReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AddRechargeTbeanReq) GetChargeType() string {
	if m != nil {
		return m.ChargeType
	}
	return ""
}

func (m *AddRechargeTbeanReq) GetPayChannel() string {
	if m != nil {
		return m.PayChannel
	}
	return ""
}

func (m *AddRechargeTbeanReq) GetChargeTime() string {
	if m != nil {
		return m.ChargeTime
	}
	return ""
}

type AddRechargeTbeanResp struct {
}

func (m *AddRechargeTbeanResp) Reset()                    { *m = AddRechargeTbeanResp{} }
func (m *AddRechargeTbeanResp) String() string            { return proto.CompactTextString(m) }
func (*AddRechargeTbeanResp) ProtoMessage()               {}
func (*AddRechargeTbeanResp) Descriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{29} }

// 限时体验贵族信息
type TempNobilityInfo struct {
	Uid      uint32 `protobuf:"varint,1,opt,name=uid" json:"uid"`
	BeginTs  uint32 `protobuf:"varint,2,opt,name=begin_ts,json=beginTs" json:"begin_ts"`
	EndTs    uint32 `protobuf:"varint,3,opt,name=end_ts,json=endTs" json:"end_ts"`
	UpdateTs uint32 `protobuf:"varint,4,opt,name=update_ts,json=updateTs" json:"update_ts"`
	Level    uint32 `protobuf:"varint,5,opt,name=level" json:"level"`
	Id       uint32 `protobuf:"varint,6,opt,name=id" json:"id"`
}

func (m *TempNobilityInfo) Reset()                    { *m = TempNobilityInfo{} }
func (m *TempNobilityInfo) String() string            { return proto.CompactTextString(m) }
func (*TempNobilityInfo) ProtoMessage()               {}
func (*TempNobilityInfo) Descriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{30} }

func (m *TempNobilityInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TempNobilityInfo) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *TempNobilityInfo) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *TempNobilityInfo) GetUpdateTs() uint32 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *TempNobilityInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *TempNobilityInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 奖励配置
type TempNobilityAwardInfo struct {
	AwardImgUrl string `protobuf:"bytes,1,opt,name=award_img_url,json=awardImgUrl" json:"award_img_url"`
	AwardName   string `protobuf:"bytes,2,opt,name=award_name,json=awardName" json:"award_name"`
	Cnt         uint32 `protobuf:"varint,3,opt,name=cnt" json:"cnt"`
}

func (m *TempNobilityAwardInfo) Reset()         { *m = TempNobilityAwardInfo{} }
func (m *TempNobilityAwardInfo) String() string { return proto.CompactTextString(m) }
func (*TempNobilityAwardInfo) ProtoMessage()    {}
func (*TempNobilityAwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{31}
}

func (m *TempNobilityAwardInfo) GetAwardImgUrl() string {
	if m != nil {
		return m.AwardImgUrl
	}
	return ""
}

func (m *TempNobilityAwardInfo) GetAwardName() string {
	if m != nil {
		return m.AwardName
	}
	return ""
}

func (m *TempNobilityAwardInfo) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

// 开通限时体验贵族
type AddTempNobilityReq struct {
	Uid     uint32 `protobuf:"varint,1,opt,name=uid" json:"uid"`
	BeginTs uint32 `protobuf:"varint,2,opt,name=begin_ts,json=beginTs" json:"begin_ts"`
	Level   uint32 `protobuf:"varint,3,opt,name=level" json:"level"`
}

func (m *AddTempNobilityReq) Reset()                    { *m = AddTempNobilityReq{} }
func (m *AddTempNobilityReq) String() string            { return proto.CompactTextString(m) }
func (*AddTempNobilityReq) ProtoMessage()               {}
func (*AddTempNobilityReq) Descriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{32} }

func (m *AddTempNobilityReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddTempNobilityReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *AddTempNobilityReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type AddTempNobilityResp struct {
}

func (m *AddTempNobilityResp) Reset()                    { *m = AddTempNobilityResp{} }
func (m *AddTempNobilityResp) String() string            { return proto.CompactTextString(m) }
func (*AddTempNobilityResp) ProtoMessage()               {}
func (*AddTempNobilityResp) Descriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{33} }

// 获取限时体验贵族列表
type GetTempNobilityListReq struct {
	IsValid bool `protobuf:"varint,1,opt,name=is_valid,json=isValid" json:"is_valid"`
}

func (m *GetTempNobilityListReq) Reset()         { *m = GetTempNobilityListReq{} }
func (m *GetTempNobilityListReq) String() string { return proto.CompactTextString(m) }
func (*GetTempNobilityListReq) ProtoMessage()    {}
func (*GetTempNobilityListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{34}
}

func (m *GetTempNobilityListReq) GetIsValid() bool {
	if m != nil {
		return m.IsValid
	}
	return false
}

type GetTempNobilityListResp struct {
	InfoList      []*TempNobilityInfo      `protobuf:"bytes,1,rep,name=info_list,json=infoList" json:"info_list,omitempty"`
	AwardInfoList []*TempNobilityAwardInfo `protobuf:"bytes,2,rep,name=award_info_list,json=awardInfoList" json:"award_info_list,omitempty"`
}

func (m *GetTempNobilityListResp) Reset()         { *m = GetTempNobilityListResp{} }
func (m *GetTempNobilityListResp) String() string { return proto.CompactTextString(m) }
func (*GetTempNobilityListResp) ProtoMessage()    {}
func (*GetTempNobilityListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{35}
}

func (m *GetTempNobilityListResp) GetInfoList() []*TempNobilityInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetTempNobilityListResp) GetAwardInfoList() []*TempNobilityAwardInfo {
	if m != nil {
		return m.AwardInfoList
	}
	return nil
}

type GetConsumeOrderCountReq struct {
	BeginTs uint32 `protobuf:"varint,1,opt,name=begin_ts,json=beginTs" json:"begin_ts"`
	EndTs   uint32 `protobuf:"varint,2,opt,name=end_ts,json=endTs" json:"end_ts"`
}

func (m *GetConsumeOrderCountReq) Reset()         { *m = GetConsumeOrderCountReq{} }
func (m *GetConsumeOrderCountReq) String() string { return proto.CompactTextString(m) }
func (*GetConsumeOrderCountReq) ProtoMessage()    {}
func (*GetConsumeOrderCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{36}
}

func (m *GetConsumeOrderCountReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetConsumeOrderCountReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type GetConsumeOrderCountResp struct {
	Count uint32 `protobuf:"varint,1,opt,name=count" json:"count"`
	Value uint32 `protobuf:"varint,2,opt,name=value" json:"value"`
}

func (m *GetConsumeOrderCountResp) Reset()         { *m = GetConsumeOrderCountResp{} }
func (m *GetConsumeOrderCountResp) String() string { return proto.CompactTextString(m) }
func (*GetConsumeOrderCountResp) ProtoMessage()    {}
func (*GetConsumeOrderCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{37}
}

func (m *GetConsumeOrderCountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetConsumeOrderCountResp) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

type GetConsumeOrderListReq struct {
	BeginTs uint32 `protobuf:"varint,1,opt,name=begin_ts,json=beginTs" json:"begin_ts"`
	EndTs   uint32 `protobuf:"varint,2,opt,name=end_ts,json=endTs" json:"end_ts"`
}

func (m *GetConsumeOrderListReq) Reset()         { *m = GetConsumeOrderListReq{} }
func (m *GetConsumeOrderListReq) String() string { return proto.CompactTextString(m) }
func (*GetConsumeOrderListReq) ProtoMessage()    {}
func (*GetConsumeOrderListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{38}
}

func (m *GetConsumeOrderListReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetConsumeOrderListReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type GetConsumeOrderListResp struct {
	OrderList []string `protobuf:"bytes,1,rep,name=order_list,json=orderList" json:"order_list,omitempty"`
}

func (m *GetConsumeOrderListResp) Reset()         { *m = GetConsumeOrderListResp{} }
func (m *GetConsumeOrderListResp) String() string { return proto.CompactTextString(m) }
func (*GetConsumeOrderListResp) ProtoMessage()    {}
func (*GetConsumeOrderListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNobilitysvr, []int{39}
}

func (m *GetConsumeOrderListResp) GetOrderList() []string {
	if m != nil {
		return m.OrderList
	}
	return nil
}

type FixConsumeOrderReq struct {
	OrderId   string `protobuf:"bytes,1,opt,name=order_id,json=orderId" json:"order_id"`
	Uid       uint32 `protobuf:"varint,2,opt,name=uid" json:"uid"`
	IncrValue uint32 `protobuf:"varint,3,opt,name=incr_value,json=incrValue" json:"incr_value"`
	ConsumeTs uint32 `protobuf:"varint,4,opt,name=consume_ts,json=consumeTs" json:"consume_ts"`
}

func (m *FixConsumeOrderReq) Reset()                    { *m = FixConsumeOrderReq{} }
func (m *FixConsumeOrderReq) String() string            { return proto.CompactTextString(m) }
func (*FixConsumeOrderReq) ProtoMessage()               {}
func (*FixConsumeOrderReq) Descriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{40} }

func (m *FixConsumeOrderReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *FixConsumeOrderReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FixConsumeOrderReq) GetIncrValue() uint32 {
	if m != nil {
		return m.IncrValue
	}
	return 0
}

func (m *FixConsumeOrderReq) GetConsumeTs() uint32 {
	if m != nil {
		return m.ConsumeTs
	}
	return 0
}

type FixConsumeOrderResp struct {
}

func (m *FixConsumeOrderResp) Reset()                    { *m = FixConsumeOrderResp{} }
func (m *FixConsumeOrderResp) String() string            { return proto.CompactTextString(m) }
func (*FixConsumeOrderResp) ProtoMessage()               {}
func (*FixConsumeOrderResp) Descriptor() ([]byte, []int) { return fileDescriptorNobilitysvr, []int{41} }

func init() {
	proto.RegisterType((*NobilityRecored)(nil), "NobilitySvr.NobilityRecored")
	proto.RegisterType((*NoilityInvestInfo)(nil), "NobilitySvr.NoilityInvestInfo")
	proto.RegisterType((*NobilityInfo)(nil), "NobilitySvr.NobilityInfo")
	proto.RegisterType((*GetNobilityInfoReq)(nil), "NobilitySvr.GetNobilityInfoReq")
	proto.RegisterType((*GetNobilityInfoResp)(nil), "NobilitySvr.GetNobilityInfoResp")
	proto.RegisterType((*BatchGetNobilityInfoReq)(nil), "NobilitySvr.BatchGetNobilityInfoReq")
	proto.RegisterType((*BatchGetNobilityInfoResp)(nil), "NobilitySvr.BatchGetNobilityInfoResp")
	proto.RegisterType((*SetInvisibleStatusReq)(nil), "NobilitySvr.SetInvisibleStatusReq")
	proto.RegisterType((*SetInvisibleStatusResp)(nil), "NobilitySvr.SetInvisibleStatusResp")
	proto.RegisterType((*GetInvisibleStatusReq)(nil), "NobilitySvr.GetInvisibleStatusReq")
	proto.RegisterType((*GetInvisibleStatusResp)(nil), "NobilitySvr.GetInvisibleStatusResp")
	proto.RegisterType((*BatchGetInvisibleStatusReq)(nil), "NobilitySvr.BatchGetInvisibleStatusReq")
	proto.RegisterType((*BatchGetInvisibleStatusResp)(nil), "NobilitySvr.BatchGetInvisibleStatusResp")
	proto.RegisterType((*GetTrumpetLeftCntReq)(nil), "NobilitySvr.GetTrumpetLeftCntReq")
	proto.RegisterType((*GetTrumpetLeftCntResp)(nil), "NobilitySvr.GetTrumpetLeftCntResp")
	proto.RegisterType((*ReduceTrumpetLeftCntReq)(nil), "NobilitySvr.ReduceTrumpetLeftCntReq")
	proto.RegisterType((*ReduceTrumpetLeftCntResp)(nil), "NobilitySvr.ReduceTrumpetLeftCntResp")
	proto.RegisterType((*NobilityNotifyMsg)(nil), "NobilitySvr.NobilityNotifyMsg")
	proto.RegisterType((*AddRechargeReq)(nil), "NobilitySvr.AddRechargeReq")
	proto.RegisterType((*AddRechargeResp)(nil), "NobilitySvr.AddRechargeResp")
	proto.RegisterType((*GetRechargeResultInfoReq)(nil), "NobilitySvr.GetRechargeResultInfoReq")
	proto.RegisterType((*GetRechargeResultInfoResp)(nil), "NobilitySvr.GetRechargeResultInfoResp")
	proto.RegisterType((*AddUserNobilityValueReq)(nil), "NobilitySvr.AddUserNobilityValueReq")
	proto.RegisterType((*AddUserNobilityValueResp)(nil), "NobilitySvr.AddUserNobilityValueResp")
	proto.RegisterType((*GetMemberNobilityInfoReq)(nil), "NobilitySvr.GetMemberNobilityInfoReq")
	proto.RegisterType((*GetMemberNobilityInfoResp)(nil), "NobilitySvr.GetMemberNobilityInfoResp")
	proto.RegisterType((*PushNobilityLevelUpMsgReq)(nil), "NobilitySvr.PushNobilityLevelUpMsgReq")
	proto.RegisterType((*PushNobilityLevelUpMsgResp)(nil), "NobilitySvr.PushNobilityLevelUpMsgResp")
	proto.RegisterType((*AddRechargeTbeanReq)(nil), "NobilitySvr.AddRechargeTbeanReq")
	proto.RegisterType((*AddRechargeTbeanResp)(nil), "NobilitySvr.AddRechargeTbeanResp")
	proto.RegisterType((*TempNobilityInfo)(nil), "NobilitySvr.TempNobilityInfo")
	proto.RegisterType((*TempNobilityAwardInfo)(nil), "NobilitySvr.TempNobilityAwardInfo")
	proto.RegisterType((*AddTempNobilityReq)(nil), "NobilitySvr.AddTempNobilityReq")
	proto.RegisterType((*AddTempNobilityResp)(nil), "NobilitySvr.AddTempNobilityResp")
	proto.RegisterType((*GetTempNobilityListReq)(nil), "NobilitySvr.GetTempNobilityListReq")
	proto.RegisterType((*GetTempNobilityListResp)(nil), "NobilitySvr.GetTempNobilityListResp")
	proto.RegisterType((*GetConsumeOrderCountReq)(nil), "NobilitySvr.GetConsumeOrderCountReq")
	proto.RegisterType((*GetConsumeOrderCountResp)(nil), "NobilitySvr.GetConsumeOrderCountResp")
	proto.RegisterType((*GetConsumeOrderListReq)(nil), "NobilitySvr.GetConsumeOrderListReq")
	proto.RegisterType((*GetConsumeOrderListResp)(nil), "NobilitySvr.GetConsumeOrderListResp")
	proto.RegisterType((*FixConsumeOrderReq)(nil), "NobilitySvr.FixConsumeOrderReq")
	proto.RegisterType((*FixConsumeOrderResp)(nil), "NobilitySvr.FixConsumeOrderResp")
	proto.RegisterEnum("NobilitySvr.KfkWorkerType", KfkWorkerType_name, KfkWorkerType_value)
	proto.RegisterEnum("NobilitySvr.InvisibleType", InvisibleType_name, InvisibleType_value)
	proto.RegisterEnum("NobilitySvr.NobilityLevelType", NobilityLevelType_name, NobilityLevelType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for NobilitySvr service

type NobilitySvrClient interface {
	GetNobilityInfo(ctx context.Context, in *GetNobilityInfoReq, opts ...grpc.CallOption) (*GetNobilityInfoResp, error)
	BatchGetNobilityInfo(ctx context.Context, in *BatchGetNobilityInfoReq, opts ...grpc.CallOption) (*BatchGetNobilityInfoResp, error)
	SetInvisibleStatus(ctx context.Context, in *SetInvisibleStatusReq, opts ...grpc.CallOption) (*SetInvisibleStatusResp, error)
	GetInvisibleStatus(ctx context.Context, in *GetInvisibleStatusReq, opts ...grpc.CallOption) (*GetInvisibleStatusResp, error)
	BatchGetInvisibleStatus(ctx context.Context, in *BatchGetInvisibleStatusReq, opts ...grpc.CallOption) (*BatchGetInvisibleStatusResp, error)
	GetTrumpetLeftCnt(ctx context.Context, in *GetTrumpetLeftCntReq, opts ...grpc.CallOption) (*GetTrumpetLeftCntResp, error)
	ReduceTrumpetLeftCnt(ctx context.Context, in *ReduceTrumpetLeftCntReq, opts ...grpc.CallOption) (*ReduceTrumpetLeftCntResp, error)
	AddRecharge(ctx context.Context, in *AddRechargeReq, opts ...grpc.CallOption) (*AddRechargeResp, error)
	GetRechargeResultInfo(ctx context.Context, in *GetRechargeResultInfoReq, opts ...grpc.CallOption) (*GetRechargeResultInfoResp, error)
	AddUserNobilityValue(ctx context.Context, in *AddUserNobilityValueReq, opts ...grpc.CallOption) (*AddUserNobilityValueResp, error)
	// 设置房间临时隐身状态
	GetMemberNobilityInfo(ctx context.Context, in *GetMemberNobilityInfoReq, opts ...grpc.CallOption) (*GetMemberNobilityInfoResp, error)
	PushNobilityLevelUpMsg(ctx context.Context, in *PushNobilityLevelUpMsgReq, opts ...grpc.CallOption) (*PushNobilityLevelUpMsgResp, error)
	AddRechargeTbean(ctx context.Context, in *AddRechargeTbeanReq, opts ...grpc.CallOption) (*AddRechargeTbeanResp, error)
	AddTempNobility(ctx context.Context, in *AddTempNobilityReq, opts ...grpc.CallOption) (*AddTempNobilityResp, error)
	GetTempNobilityList(ctx context.Context, in *GetTempNobilityListReq, opts ...grpc.CallOption) (*GetTempNobilityListResp, error)
	GetConsumeOrderCount(ctx context.Context, in *GetConsumeOrderCountReq, opts ...grpc.CallOption) (*GetConsumeOrderCountResp, error)
	GetConsumeOrderList(ctx context.Context, in *GetConsumeOrderListReq, opts ...grpc.CallOption) (*GetConsumeOrderListResp, error)
	FixConsumeOrder(ctx context.Context, in *FixConsumeOrderReq, opts ...grpc.CallOption) (*FixConsumeOrderResp, error)
}

type nobilitySvrClient struct {
	cc *grpc.ClientConn
}

func NewNobilitySvrClient(cc *grpc.ClientConn) NobilitySvrClient {
	return &nobilitySvrClient{cc}
}

func (c *nobilitySvrClient) GetNobilityInfo(ctx context.Context, in *GetNobilityInfoReq, opts ...grpc.CallOption) (*GetNobilityInfoResp, error) {
	out := new(GetNobilityInfoResp)
	err := grpc.Invoke(ctx, "/NobilitySvr.NobilitySvr/GetNobilityInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nobilitySvrClient) BatchGetNobilityInfo(ctx context.Context, in *BatchGetNobilityInfoReq, opts ...grpc.CallOption) (*BatchGetNobilityInfoResp, error) {
	out := new(BatchGetNobilityInfoResp)
	err := grpc.Invoke(ctx, "/NobilitySvr.NobilitySvr/BatchGetNobilityInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nobilitySvrClient) SetInvisibleStatus(ctx context.Context, in *SetInvisibleStatusReq, opts ...grpc.CallOption) (*SetInvisibleStatusResp, error) {
	out := new(SetInvisibleStatusResp)
	err := grpc.Invoke(ctx, "/NobilitySvr.NobilitySvr/SetInvisibleStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nobilitySvrClient) GetInvisibleStatus(ctx context.Context, in *GetInvisibleStatusReq, opts ...grpc.CallOption) (*GetInvisibleStatusResp, error) {
	out := new(GetInvisibleStatusResp)
	err := grpc.Invoke(ctx, "/NobilitySvr.NobilitySvr/GetInvisibleStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nobilitySvrClient) BatchGetInvisibleStatus(ctx context.Context, in *BatchGetInvisibleStatusReq, opts ...grpc.CallOption) (*BatchGetInvisibleStatusResp, error) {
	out := new(BatchGetInvisibleStatusResp)
	err := grpc.Invoke(ctx, "/NobilitySvr.NobilitySvr/BatchGetInvisibleStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nobilitySvrClient) GetTrumpetLeftCnt(ctx context.Context, in *GetTrumpetLeftCntReq, opts ...grpc.CallOption) (*GetTrumpetLeftCntResp, error) {
	out := new(GetTrumpetLeftCntResp)
	err := grpc.Invoke(ctx, "/NobilitySvr.NobilitySvr/GetTrumpetLeftCnt", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nobilitySvrClient) ReduceTrumpetLeftCnt(ctx context.Context, in *ReduceTrumpetLeftCntReq, opts ...grpc.CallOption) (*ReduceTrumpetLeftCntResp, error) {
	out := new(ReduceTrumpetLeftCntResp)
	err := grpc.Invoke(ctx, "/NobilitySvr.NobilitySvr/ReduceTrumpetLeftCnt", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nobilitySvrClient) AddRecharge(ctx context.Context, in *AddRechargeReq, opts ...grpc.CallOption) (*AddRechargeResp, error) {
	out := new(AddRechargeResp)
	err := grpc.Invoke(ctx, "/NobilitySvr.NobilitySvr/AddRecharge", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nobilitySvrClient) GetRechargeResultInfo(ctx context.Context, in *GetRechargeResultInfoReq, opts ...grpc.CallOption) (*GetRechargeResultInfoResp, error) {
	out := new(GetRechargeResultInfoResp)
	err := grpc.Invoke(ctx, "/NobilitySvr.NobilitySvr/GetRechargeResultInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nobilitySvrClient) AddUserNobilityValue(ctx context.Context, in *AddUserNobilityValueReq, opts ...grpc.CallOption) (*AddUserNobilityValueResp, error) {
	out := new(AddUserNobilityValueResp)
	err := grpc.Invoke(ctx, "/NobilitySvr.NobilitySvr/AddUserNobilityValue", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nobilitySvrClient) GetMemberNobilityInfo(ctx context.Context, in *GetMemberNobilityInfoReq, opts ...grpc.CallOption) (*GetMemberNobilityInfoResp, error) {
	out := new(GetMemberNobilityInfoResp)
	err := grpc.Invoke(ctx, "/NobilitySvr.NobilitySvr/GetMemberNobilityInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nobilitySvrClient) PushNobilityLevelUpMsg(ctx context.Context, in *PushNobilityLevelUpMsgReq, opts ...grpc.CallOption) (*PushNobilityLevelUpMsgResp, error) {
	out := new(PushNobilityLevelUpMsgResp)
	err := grpc.Invoke(ctx, "/NobilitySvr.NobilitySvr/PushNobilityLevelUpMsg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nobilitySvrClient) AddRechargeTbean(ctx context.Context, in *AddRechargeTbeanReq, opts ...grpc.CallOption) (*AddRechargeTbeanResp, error) {
	out := new(AddRechargeTbeanResp)
	err := grpc.Invoke(ctx, "/NobilitySvr.NobilitySvr/AddRechargeTbean", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nobilitySvrClient) AddTempNobility(ctx context.Context, in *AddTempNobilityReq, opts ...grpc.CallOption) (*AddTempNobilityResp, error) {
	out := new(AddTempNobilityResp)
	err := grpc.Invoke(ctx, "/NobilitySvr.NobilitySvr/AddTempNobility", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nobilitySvrClient) GetTempNobilityList(ctx context.Context, in *GetTempNobilityListReq, opts ...grpc.CallOption) (*GetTempNobilityListResp, error) {
	out := new(GetTempNobilityListResp)
	err := grpc.Invoke(ctx, "/NobilitySvr.NobilitySvr/GetTempNobilityList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nobilitySvrClient) GetConsumeOrderCount(ctx context.Context, in *GetConsumeOrderCountReq, opts ...grpc.CallOption) (*GetConsumeOrderCountResp, error) {
	out := new(GetConsumeOrderCountResp)
	err := grpc.Invoke(ctx, "/NobilitySvr.NobilitySvr/GetConsumeOrderCount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nobilitySvrClient) GetConsumeOrderList(ctx context.Context, in *GetConsumeOrderListReq, opts ...grpc.CallOption) (*GetConsumeOrderListResp, error) {
	out := new(GetConsumeOrderListResp)
	err := grpc.Invoke(ctx, "/NobilitySvr.NobilitySvr/GetConsumeOrderList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nobilitySvrClient) FixConsumeOrder(ctx context.Context, in *FixConsumeOrderReq, opts ...grpc.CallOption) (*FixConsumeOrderResp, error) {
	out := new(FixConsumeOrderResp)
	err := grpc.Invoke(ctx, "/NobilitySvr.NobilitySvr/FixConsumeOrder", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for NobilitySvr service

type NobilitySvrServer interface {
	GetNobilityInfo(context.Context, *GetNobilityInfoReq) (*GetNobilityInfoResp, error)
	BatchGetNobilityInfo(context.Context, *BatchGetNobilityInfoReq) (*BatchGetNobilityInfoResp, error)
	SetInvisibleStatus(context.Context, *SetInvisibleStatusReq) (*SetInvisibleStatusResp, error)
	GetInvisibleStatus(context.Context, *GetInvisibleStatusReq) (*GetInvisibleStatusResp, error)
	BatchGetInvisibleStatus(context.Context, *BatchGetInvisibleStatusReq) (*BatchGetInvisibleStatusResp, error)
	GetTrumpetLeftCnt(context.Context, *GetTrumpetLeftCntReq) (*GetTrumpetLeftCntResp, error)
	ReduceTrumpetLeftCnt(context.Context, *ReduceTrumpetLeftCntReq) (*ReduceTrumpetLeftCntResp, error)
	AddRecharge(context.Context, *AddRechargeReq) (*AddRechargeResp, error)
	GetRechargeResultInfo(context.Context, *GetRechargeResultInfoReq) (*GetRechargeResultInfoResp, error)
	AddUserNobilityValue(context.Context, *AddUserNobilityValueReq) (*AddUserNobilityValueResp, error)
	// 设置房间临时隐身状态
	GetMemberNobilityInfo(context.Context, *GetMemberNobilityInfoReq) (*GetMemberNobilityInfoResp, error)
	PushNobilityLevelUpMsg(context.Context, *PushNobilityLevelUpMsgReq) (*PushNobilityLevelUpMsgResp, error)
	AddRechargeTbean(context.Context, *AddRechargeTbeanReq) (*AddRechargeTbeanResp, error)
	AddTempNobility(context.Context, *AddTempNobilityReq) (*AddTempNobilityResp, error)
	GetTempNobilityList(context.Context, *GetTempNobilityListReq) (*GetTempNobilityListResp, error)
	GetConsumeOrderCount(context.Context, *GetConsumeOrderCountReq) (*GetConsumeOrderCountResp, error)
	GetConsumeOrderList(context.Context, *GetConsumeOrderListReq) (*GetConsumeOrderListResp, error)
	FixConsumeOrder(context.Context, *FixConsumeOrderReq) (*FixConsumeOrderResp, error)
}

func RegisterNobilitySvrServer(s *grpc.Server, srv NobilitySvrServer) {
	s.RegisterService(&_NobilitySvr_serviceDesc, srv)
}

func _NobilitySvr_GetNobilityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNobilityInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NobilitySvrServer).GetNobilityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/NobilitySvr.NobilitySvr/GetNobilityInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NobilitySvrServer).GetNobilityInfo(ctx, req.(*GetNobilityInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NobilitySvr_BatchGetNobilityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetNobilityInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NobilitySvrServer).BatchGetNobilityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/NobilitySvr.NobilitySvr/BatchGetNobilityInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NobilitySvrServer).BatchGetNobilityInfo(ctx, req.(*BatchGetNobilityInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NobilitySvr_SetInvisibleStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetInvisibleStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NobilitySvrServer).SetInvisibleStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/NobilitySvr.NobilitySvr/SetInvisibleStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NobilitySvrServer).SetInvisibleStatus(ctx, req.(*SetInvisibleStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NobilitySvr_GetInvisibleStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInvisibleStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NobilitySvrServer).GetInvisibleStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/NobilitySvr.NobilitySvr/GetInvisibleStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NobilitySvrServer).GetInvisibleStatus(ctx, req.(*GetInvisibleStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NobilitySvr_BatchGetInvisibleStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetInvisibleStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NobilitySvrServer).BatchGetInvisibleStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/NobilitySvr.NobilitySvr/BatchGetInvisibleStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NobilitySvrServer).BatchGetInvisibleStatus(ctx, req.(*BatchGetInvisibleStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NobilitySvr_GetTrumpetLeftCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTrumpetLeftCntReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NobilitySvrServer).GetTrumpetLeftCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/NobilitySvr.NobilitySvr/GetTrumpetLeftCnt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NobilitySvrServer).GetTrumpetLeftCnt(ctx, req.(*GetTrumpetLeftCntReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NobilitySvr_ReduceTrumpetLeftCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReduceTrumpetLeftCntReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NobilitySvrServer).ReduceTrumpetLeftCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/NobilitySvr.NobilitySvr/ReduceTrumpetLeftCnt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NobilitySvrServer).ReduceTrumpetLeftCnt(ctx, req.(*ReduceTrumpetLeftCntReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NobilitySvr_AddRecharge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRechargeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NobilitySvrServer).AddRecharge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/NobilitySvr.NobilitySvr/AddRecharge",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NobilitySvrServer).AddRecharge(ctx, req.(*AddRechargeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NobilitySvr_GetRechargeResultInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRechargeResultInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NobilitySvrServer).GetRechargeResultInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/NobilitySvr.NobilitySvr/GetRechargeResultInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NobilitySvrServer).GetRechargeResultInfo(ctx, req.(*GetRechargeResultInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NobilitySvr_AddUserNobilityValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserNobilityValueReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NobilitySvrServer).AddUserNobilityValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/NobilitySvr.NobilitySvr/AddUserNobilityValue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NobilitySvrServer).AddUserNobilityValue(ctx, req.(*AddUserNobilityValueReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NobilitySvr_GetMemberNobilityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberNobilityInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NobilitySvrServer).GetMemberNobilityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/NobilitySvr.NobilitySvr/GetMemberNobilityInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NobilitySvrServer).GetMemberNobilityInfo(ctx, req.(*GetMemberNobilityInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NobilitySvr_PushNobilityLevelUpMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushNobilityLevelUpMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NobilitySvrServer).PushNobilityLevelUpMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/NobilitySvr.NobilitySvr/PushNobilityLevelUpMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NobilitySvrServer).PushNobilityLevelUpMsg(ctx, req.(*PushNobilityLevelUpMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NobilitySvr_AddRechargeTbean_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRechargeTbeanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NobilitySvrServer).AddRechargeTbean(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/NobilitySvr.NobilitySvr/AddRechargeTbean",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NobilitySvrServer).AddRechargeTbean(ctx, req.(*AddRechargeTbeanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NobilitySvr_AddTempNobility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTempNobilityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NobilitySvrServer).AddTempNobility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/NobilitySvr.NobilitySvr/AddTempNobility",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NobilitySvrServer).AddTempNobility(ctx, req.(*AddTempNobilityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NobilitySvr_GetTempNobilityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTempNobilityListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NobilitySvrServer).GetTempNobilityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/NobilitySvr.NobilitySvr/GetTempNobilityList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NobilitySvrServer).GetTempNobilityList(ctx, req.(*GetTempNobilityListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NobilitySvr_GetConsumeOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConsumeOrderCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NobilitySvrServer).GetConsumeOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/NobilitySvr.NobilitySvr/GetConsumeOrderCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NobilitySvrServer).GetConsumeOrderCount(ctx, req.(*GetConsumeOrderCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NobilitySvr_GetConsumeOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConsumeOrderListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NobilitySvrServer).GetConsumeOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/NobilitySvr.NobilitySvr/GetConsumeOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NobilitySvrServer).GetConsumeOrderList(ctx, req.(*GetConsumeOrderListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NobilitySvr_FixConsumeOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FixConsumeOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NobilitySvrServer).FixConsumeOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/NobilitySvr.NobilitySvr/FixConsumeOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NobilitySvrServer).FixConsumeOrder(ctx, req.(*FixConsumeOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _NobilitySvr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "NobilitySvr.NobilitySvr",
	HandlerType: (*NobilitySvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNobilityInfo",
			Handler:    _NobilitySvr_GetNobilityInfo_Handler,
		},
		{
			MethodName: "BatchGetNobilityInfo",
			Handler:    _NobilitySvr_BatchGetNobilityInfo_Handler,
		},
		{
			MethodName: "SetInvisibleStatus",
			Handler:    _NobilitySvr_SetInvisibleStatus_Handler,
		},
		{
			MethodName: "GetInvisibleStatus",
			Handler:    _NobilitySvr_GetInvisibleStatus_Handler,
		},
		{
			MethodName: "BatchGetInvisibleStatus",
			Handler:    _NobilitySvr_BatchGetInvisibleStatus_Handler,
		},
		{
			MethodName: "GetTrumpetLeftCnt",
			Handler:    _NobilitySvr_GetTrumpetLeftCnt_Handler,
		},
		{
			MethodName: "ReduceTrumpetLeftCnt",
			Handler:    _NobilitySvr_ReduceTrumpetLeftCnt_Handler,
		},
		{
			MethodName: "AddRecharge",
			Handler:    _NobilitySvr_AddRecharge_Handler,
		},
		{
			MethodName: "GetRechargeResultInfo",
			Handler:    _NobilitySvr_GetRechargeResultInfo_Handler,
		},
		{
			MethodName: "AddUserNobilityValue",
			Handler:    _NobilitySvr_AddUserNobilityValue_Handler,
		},
		{
			MethodName: "GetMemberNobilityInfo",
			Handler:    _NobilitySvr_GetMemberNobilityInfo_Handler,
		},
		{
			MethodName: "PushNobilityLevelUpMsg",
			Handler:    _NobilitySvr_PushNobilityLevelUpMsg_Handler,
		},
		{
			MethodName: "AddRechargeTbean",
			Handler:    _NobilitySvr_AddRechargeTbean_Handler,
		},
		{
			MethodName: "AddTempNobility",
			Handler:    _NobilitySvr_AddTempNobility_Handler,
		},
		{
			MethodName: "GetTempNobilityList",
			Handler:    _NobilitySvr_GetTempNobilityList_Handler,
		},
		{
			MethodName: "GetConsumeOrderCount",
			Handler:    _NobilitySvr_GetConsumeOrderCount_Handler,
		},
		{
			MethodName: "GetConsumeOrderList",
			Handler:    _NobilitySvr_GetConsumeOrderList_Handler,
		},
		{
			MethodName: "FixConsumeOrder",
			Handler:    _NobilitySvr_FixConsumeOrder_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/nobilitysvr/nobilitysvr.proto",
}

func (m *NobilityRecored) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NobilityRecored) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Ts))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Level))
	return i, nil
}

func (m *NoilityInvestInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NoilityInvestInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Level))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.GapValue))
	return i, nil
}

func (m *NobilityInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NobilityInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Value))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.KeepValue))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Level))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.CycleTs))
	dAtA[i] = 0x28
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x30
	i++
	if m.Invisible {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x38
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.WaitCostValue))
	dAtA[i] = 0x40
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.TotalWaitCostValue))
	dAtA[i] = 0x4a
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(len(m.LevelName)))
	i += copy(dAtA[i:], m.LevelName)
	if len(m.InvestInfoList) > 0 {
		for _, msg := range m.InvestInfoList {
			dAtA[i] = 0x52
			i++
			i = encodeVarintNobilitysvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x58
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.TempNobilityRemainTs))
	dAtA[i] = 0x60
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.RealLevel))
	dAtA[i] = 0x68
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.ExtentCnt))
	dAtA[i] = 0x70
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.LackExtentVal))
	dAtA[i] = 0x7d
	i++
	i = encodeFixed32Nobilitysvr(dAtA, i, uint32(math3.Float32bits(float32(m.FLevel))))
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.LevelValue))
	return i, nil
}

func (m *GetNobilityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetNobilityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.NeedRecoreds {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x12
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(len(m.Source)))
	i += copy(dAtA[i:], m.Source)
	dAtA[i] = 0x18
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetNobilityInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetNobilityInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Info == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Info.Size()))
		n1, err := m.Info.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if len(m.Records) > 0 {
		for _, msg := range m.Records {
			dAtA[i] = 0x12
			i++
			i = encodeVarintNobilitysvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchGetNobilityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetNobilityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Uids) > 0 {
		for _, num := range m.Uids {
			dAtA[i] = 0x8
			i++
			i = encodeVarintNobilitysvr(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchGetNobilityInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetNobilityInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.NobilityInfos) > 0 {
		for _, msg := range m.NobilityInfos {
			dAtA[i] = 0xa
			i++
			i = encodeVarintNobilitysvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetInvisibleStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetInvisibleStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.StatusType))
	return i, nil
}

func (m *SetInvisibleStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetInvisibleStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *GetInvisibleStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetInvisibleStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetInvisibleStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetInvisibleStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *BatchGetInvisibleStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetInvisibleStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Uids) > 0 {
		for _, num := range m.Uids {
			dAtA[i] = 0x8
			i++
			i = encodeVarintNobilitysvr(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchGetInvisibleStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetInvisibleStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.VisibleStatusList) > 0 {
		for _, b := range m.VisibleStatusList {
			dAtA[i] = 0x8
			i++
			if b {
				dAtA[i] = 1
			} else {
				dAtA[i] = 0
			}
			i++
		}
	}
	return i, nil
}

func (m *GetTrumpetLeftCntReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTrumpetLeftCntReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.PrivilegeId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.CycleTs))
	return i, nil
}

func (m *GetTrumpetLeftCntResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTrumpetLeftCntResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.LeftCnt))
	return i, nil
}

func (m *ReduceTrumpetLeftCntReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReduceTrumpetLeftCntReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.PrivilegeId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.CycleTs))
	return i, nil
}

func (m *ReduceTrumpetLeftCntResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReduceTrumpetLeftCntResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.LeftCnt))
	return i, nil
}

func (m *NobilityNotifyMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NobilityNotifyMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Ty))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Level))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Day))
	dAtA[i] = 0x28
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Left))
	return i, nil
}

func (m *AddRechargeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddRechargeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.AddValue))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.GoalLevel))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *AddRechargeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddRechargeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetRechargeResultInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRechargeResultInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.AddValue))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.GoalLevel))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetRechargeResultInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRechargeResultInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CurrInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("curr_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintNobilitysvr(dAtA, i, uint64(m.CurrInfo.Size()))
		n2, err := m.CurrInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if m.ResultInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("result_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintNobilitysvr(dAtA, i, uint64(m.ResultInfo.Size()))
		n3, err := m.ResultInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *AddUserNobilityValueReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserNobilityValueReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Value))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x22
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(len(m.Source)))
	i += copy(dAtA[i:], m.Source)
	return i, nil
}

func (m *AddUserNobilityValueResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserNobilityValueResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetMemberNobilityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMemberNobilityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetMemberNobilityInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMemberNobilityInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.NobilityLevel))
	dAtA[i] = 0x10
	i++
	if m.Invisible {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *PushNobilityLevelUpMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PushNobilityLevelUpMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(len(m.LevelName)))
	i += copy(dAtA[i:], m.LevelName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Level))
	dAtA[i] = 0x28
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Trigger))
	return i, nil
}

func (m *PushNobilityLevelUpMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PushNobilityLevelUpMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *AddRechargeTbeanReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddRechargeTbeanReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Num))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x22
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(len(m.ChargeType)))
	i += copy(dAtA[i:], m.ChargeType)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(len(m.PayChannel)))
	i += copy(dAtA[i:], m.PayChannel)
	dAtA[i] = 0x32
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(len(m.ChargeTime)))
	i += copy(dAtA[i:], m.ChargeTime)
	return i, nil
}

func (m *AddRechargeTbeanResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddRechargeTbeanResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *TempNobilityInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TempNobilityInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.BeginTs))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.EndTs))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.UpdateTs))
	dAtA[i] = 0x28
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Level))
	dAtA[i] = 0x30
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Id))
	return i, nil
}

func (m *TempNobilityAwardInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TempNobilityAwardInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(len(m.AwardImgUrl)))
	i += copy(dAtA[i:], m.AwardImgUrl)
	dAtA[i] = 0x12
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(len(m.AwardName)))
	i += copy(dAtA[i:], m.AwardName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Cnt))
	return i, nil
}

func (m *AddTempNobilityReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddTempNobilityReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.BeginTs))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Level))
	return i, nil
}

func (m *AddTempNobilityResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddTempNobilityResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetTempNobilityListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTempNobilityListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsValid {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetTempNobilityListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTempNobilityListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, msg := range m.InfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintNobilitysvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.AwardInfoList) > 0 {
		for _, msg := range m.AwardInfoList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintNobilitysvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetConsumeOrderCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetConsumeOrderCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.BeginTs))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.EndTs))
	return i, nil
}

func (m *GetConsumeOrderCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetConsumeOrderCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Value))
	return i, nil
}

func (m *GetConsumeOrderListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetConsumeOrderListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.BeginTs))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.EndTs))
	return i, nil
}

func (m *GetConsumeOrderListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetConsumeOrderListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.OrderList) > 0 {
		for _, s := range m.OrderList {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *FixConsumeOrderReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FixConsumeOrderReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x10
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.IncrValue))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNobilitysvr(dAtA, i, uint64(m.ConsumeTs))
	return i, nil
}

func (m *FixConsumeOrderResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FixConsumeOrderResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Nobilitysvr(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Nobilitysvr(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintNobilitysvr(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *NobilityRecored) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.Ts))
	n += 1 + sovNobilitysvr(uint64(m.Level))
	return n
}

func (m *NoilityInvestInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.Level))
	n += 1 + sovNobilitysvr(uint64(m.GapValue))
	return n
}

func (m *NobilityInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.Value))
	n += 1 + sovNobilitysvr(uint64(m.KeepValue))
	n += 1 + sovNobilitysvr(uint64(m.Level))
	n += 1 + sovNobilitysvr(uint64(m.CycleTs))
	n += 1 + sovNobilitysvr(uint64(m.Uid))
	n += 2
	n += 1 + sovNobilitysvr(uint64(m.WaitCostValue))
	n += 1 + sovNobilitysvr(uint64(m.TotalWaitCostValue))
	l = len(m.LevelName)
	n += 1 + l + sovNobilitysvr(uint64(l))
	if len(m.InvestInfoList) > 0 {
		for _, e := range m.InvestInfoList {
			l = e.Size()
			n += 1 + l + sovNobilitysvr(uint64(l))
		}
	}
	n += 1 + sovNobilitysvr(uint64(m.TempNobilityRemainTs))
	n += 1 + sovNobilitysvr(uint64(m.RealLevel))
	n += 1 + sovNobilitysvr(uint64(m.ExtentCnt))
	n += 1 + sovNobilitysvr(uint64(m.LackExtentVal))
	n += 5
	n += 2 + sovNobilitysvr(uint64(m.LevelValue))
	return n
}

func (m *GetNobilityInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 2
	l = len(m.Source)
	n += 1 + l + sovNobilitysvr(uint64(l))
	n += 1 + sovNobilitysvr(uint64(m.Uid))
	return n
}

func (m *GetNobilityInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.Info != nil {
		l = m.Info.Size()
		n += 1 + l + sovNobilitysvr(uint64(l))
	}
	if len(m.Records) > 0 {
		for _, e := range m.Records {
			l = e.Size()
			n += 1 + l + sovNobilitysvr(uint64(l))
		}
	}
	return n
}

func (m *BatchGetNobilityInfoReq) Size() (n int) {
	var l int
	_ = l
	if len(m.Uids) > 0 {
		for _, e := range m.Uids {
			n += 1 + sovNobilitysvr(uint64(e))
		}
	}
	return n
}

func (m *BatchGetNobilityInfoResp) Size() (n int) {
	var l int
	_ = l
	if len(m.NobilityInfos) > 0 {
		for _, e := range m.NobilityInfos {
			l = e.Size()
			n += 1 + l + sovNobilitysvr(uint64(l))
		}
	}
	return n
}

func (m *SetInvisibleStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.Status))
	n += 1 + sovNobilitysvr(uint64(m.StatusType))
	return n
}

func (m *SetInvisibleStatusResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.Status))
	return n
}

func (m *GetInvisibleStatusReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetInvisibleStatusResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.Status))
	return n
}

func (m *BatchGetInvisibleStatusReq) Size() (n int) {
	var l int
	_ = l
	if len(m.Uids) > 0 {
		for _, e := range m.Uids {
			n += 1 + sovNobilitysvr(uint64(e))
		}
	}
	return n
}

func (m *BatchGetInvisibleStatusResp) Size() (n int) {
	var l int
	_ = l
	if len(m.VisibleStatusList) > 0 {
		n += 2 * len(m.VisibleStatusList)
	}
	return n
}

func (m *GetTrumpetLeftCntReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.PrivilegeId))
	n += 1 + sovNobilitysvr(uint64(m.CycleTs))
	return n
}

func (m *GetTrumpetLeftCntResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.LeftCnt))
	return n
}

func (m *ReduceTrumpetLeftCntReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.PrivilegeId))
	n += 1 + sovNobilitysvr(uint64(m.CycleTs))
	return n
}

func (m *ReduceTrumpetLeftCntResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.LeftCnt))
	return n
}

func (m *NobilityNotifyMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.Ty))
	n += 1 + sovNobilitysvr(uint64(m.Uid))
	n += 1 + sovNobilitysvr(uint64(m.Level))
	n += 1 + sovNobilitysvr(uint64(m.Day))
	n += 1 + sovNobilitysvr(uint64(m.Left))
	return n
}

func (m *AddRechargeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.AddValue))
	n += 1 + sovNobilitysvr(uint64(m.GoalLevel))
	n += 1 + sovNobilitysvr(uint64(m.Uid))
	return n
}

func (m *AddRechargeResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetRechargeResultInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.AddValue))
	n += 1 + sovNobilitysvr(uint64(m.GoalLevel))
	n += 1 + sovNobilitysvr(uint64(m.Uid))
	return n
}

func (m *GetRechargeResultInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.CurrInfo != nil {
		l = m.CurrInfo.Size()
		n += 1 + l + sovNobilitysvr(uint64(l))
	}
	if m.ResultInfo != nil {
		l = m.ResultInfo.Size()
		n += 1 + l + sovNobilitysvr(uint64(l))
	}
	return n
}

func (m *AddUserNobilityValueReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.Uid))
	n += 1 + sovNobilitysvr(uint64(m.Value))
	l = len(m.OrderId)
	n += 1 + l + sovNobilitysvr(uint64(l))
	l = len(m.Source)
	n += 1 + l + sovNobilitysvr(uint64(l))
	return n
}

func (m *AddUserNobilityValueResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetMemberNobilityInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.Uid))
	n += 1 + sovNobilitysvr(uint64(m.ChannelId))
	return n
}

func (m *GetMemberNobilityInfoResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.NobilityLevel))
	n += 2
	return n
}

func (m *PushNobilityLevelUpMsgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.Uid))
	l = len(m.LevelName)
	n += 1 + l + sovNobilitysvr(uint64(l))
	n += 1 + sovNobilitysvr(uint64(m.ChannelId))
	n += 1 + sovNobilitysvr(uint64(m.Level))
	n += 1 + sovNobilitysvr(uint64(m.Trigger))
	return n
}

func (m *PushNobilityLevelUpMsgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *AddRechargeTbeanReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.Uid))
	n += 1 + sovNobilitysvr(uint64(m.Num))
	l = len(m.OrderId)
	n += 1 + l + sovNobilitysvr(uint64(l))
	l = len(m.ChargeType)
	n += 1 + l + sovNobilitysvr(uint64(l))
	l = len(m.PayChannel)
	n += 1 + l + sovNobilitysvr(uint64(l))
	l = len(m.ChargeTime)
	n += 1 + l + sovNobilitysvr(uint64(l))
	return n
}

func (m *AddRechargeTbeanResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *TempNobilityInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.Uid))
	n += 1 + sovNobilitysvr(uint64(m.BeginTs))
	n += 1 + sovNobilitysvr(uint64(m.EndTs))
	n += 1 + sovNobilitysvr(uint64(m.UpdateTs))
	n += 1 + sovNobilitysvr(uint64(m.Level))
	n += 1 + sovNobilitysvr(uint64(m.Id))
	return n
}

func (m *TempNobilityAwardInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.AwardImgUrl)
	n += 1 + l + sovNobilitysvr(uint64(l))
	l = len(m.AwardName)
	n += 1 + l + sovNobilitysvr(uint64(l))
	n += 1 + sovNobilitysvr(uint64(m.Cnt))
	return n
}

func (m *AddTempNobilityReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.Uid))
	n += 1 + sovNobilitysvr(uint64(m.BeginTs))
	n += 1 + sovNobilitysvr(uint64(m.Level))
	return n
}

func (m *AddTempNobilityResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetTempNobilityListReq) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *GetTempNobilityListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, e := range m.InfoList {
			l = e.Size()
			n += 1 + l + sovNobilitysvr(uint64(l))
		}
	}
	if len(m.AwardInfoList) > 0 {
		for _, e := range m.AwardInfoList {
			l = e.Size()
			n += 1 + l + sovNobilitysvr(uint64(l))
		}
	}
	return n
}

func (m *GetConsumeOrderCountReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.BeginTs))
	n += 1 + sovNobilitysvr(uint64(m.EndTs))
	return n
}

func (m *GetConsumeOrderCountResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.Count))
	n += 1 + sovNobilitysvr(uint64(m.Value))
	return n
}

func (m *GetConsumeOrderListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNobilitysvr(uint64(m.BeginTs))
	n += 1 + sovNobilitysvr(uint64(m.EndTs))
	return n
}

func (m *GetConsumeOrderListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.OrderList) > 0 {
		for _, s := range m.OrderList {
			l = len(s)
			n += 1 + l + sovNobilitysvr(uint64(l))
		}
	}
	return n
}

func (m *FixConsumeOrderReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.OrderId)
	n += 1 + l + sovNobilitysvr(uint64(l))
	n += 1 + sovNobilitysvr(uint64(m.Uid))
	n += 1 + sovNobilitysvr(uint64(m.IncrValue))
	n += 1 + sovNobilitysvr(uint64(m.ConsumeTs))
	return n
}

func (m *FixConsumeOrderResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovNobilitysvr(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozNobilitysvr(x uint64) (n int) {
	return sovNobilitysvr(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *NobilityRecored) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NobilityRecored: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NobilityRecored: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ts", wireType)
			}
			m.Ts = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ts |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ts")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NoilityInvestInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NoilityInvestInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NoilityInvestInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GapValue", wireType)
			}
			m.GapValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GapValue |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NobilityInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NobilityInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NobilityInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KeepValue", wireType)
			}
			m.KeepValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KeepValue |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CycleTs", wireType)
			}
			m.CycleTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CycleTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Invisible", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Invisible = bool(v != 0)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WaitCostValue", wireType)
			}
			m.WaitCostValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.WaitCostValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalWaitCostValue", wireType)
			}
			m.TotalWaitCostValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalWaitCostValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LevelName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InvestInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.InvestInfoList = append(m.InvestInfoList, &NoilityInvestInfo{})
			if err := m.InvestInfoList[len(m.InvestInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TempNobilityRemainTs", wireType)
			}
			m.TempNobilityRemainTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TempNobilityRemainTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RealLevel", wireType)
			}
			m.RealLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RealLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtentCnt", wireType)
			}
			m.ExtentCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtentCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LackExtentVal", wireType)
			}
			m.LackExtentVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LackExtentVal |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 5 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FLevel", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 4
			v = uint32(dAtA[iNdEx-4])
			v |= uint32(dAtA[iNdEx-3]) << 8
			v |= uint32(dAtA[iNdEx-2]) << 16
			v |= uint32(dAtA[iNdEx-1]) << 24
			m.FLevel = float32(math4.Float32frombits(v))
		case 16:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelValue", wireType)
			}
			m.LevelValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LevelValue |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("value")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("keep_value")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cycle_ts")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetNobilityInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetNobilityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetNobilityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NeedRecoreds", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.NeedRecoreds = bool(v != 0)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Source = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetNobilityInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetNobilityInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetNobilityInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Info", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Info == nil {
				m.Info = &NobilityInfo{}
			}
			if err := m.Info.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Records", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Records = append(m.Records, &NobilityRecored{})
			if err := m.Records[len(m.Records)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetNobilityInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetNobilityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetNobilityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNobilitysvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Uids = append(m.Uids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNobilitysvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthNobilitysvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowNobilitysvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Uids = append(m.Uids, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uids", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetNobilityInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetNobilityInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetNobilityInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NobilityInfos", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NobilityInfos = append(m.NobilityInfos, &NobilityInfo{})
			if err := m.NobilityInfos[len(m.NobilityInfos)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetInvisibleStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetInvisibleStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetInvisibleStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StatusType", wireType)
			}
			m.StatusType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StatusType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetInvisibleStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetInvisibleStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetInvisibleStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetInvisibleStatusReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetInvisibleStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetInvisibleStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetInvisibleStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetInvisibleStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetInvisibleStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetInvisibleStatusReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetInvisibleStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetInvisibleStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNobilitysvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Uids = append(m.Uids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNobilitysvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthNobilitysvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowNobilitysvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Uids = append(m.Uids, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uids", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetInvisibleStatusResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetInvisibleStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetInvisibleStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNobilitysvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.VisibleStatusList = append(m.VisibleStatusList, bool(v != 0))
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNobilitysvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthNobilitysvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v int
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowNobilitysvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (int(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.VisibleStatusList = append(m.VisibleStatusList, bool(v != 0))
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field VisibleStatusList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTrumpetLeftCntReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTrumpetLeftCntReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTrumpetLeftCntReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PrivilegeId", wireType)
			}
			m.PrivilegeId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PrivilegeId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CycleTs", wireType)
			}
			m.CycleTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CycleTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("privilege_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cycle_ts")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTrumpetLeftCntResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTrumpetLeftCntResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTrumpetLeftCntResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeftCnt", wireType)
			}
			m.LeftCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeftCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReduceTrumpetLeftCntReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReduceTrumpetLeftCntReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReduceTrumpetLeftCntReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PrivilegeId", wireType)
			}
			m.PrivilegeId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PrivilegeId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CycleTs", wireType)
			}
			m.CycleTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CycleTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("privilege_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cycle_ts")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReduceTrumpetLeftCntResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReduceTrumpetLeftCntResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReduceTrumpetLeftCntResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeftCnt", wireType)
			}
			m.LeftCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeftCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NobilityNotifyMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NobilityNotifyMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NobilityNotifyMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ty", wireType)
			}
			m.Ty = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ty |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Day", wireType)
			}
			m.Day = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Day |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Left", wireType)
			}
			m.Left = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Left |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ty")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddRechargeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddRechargeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddRechargeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AddValue", wireType)
			}
			m.AddValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AddValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GoalLevel", wireType)
			}
			m.GoalLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GoalLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("add_value")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("goal_level")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddRechargeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddRechargeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddRechargeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRechargeResultInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRechargeResultInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRechargeResultInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AddValue", wireType)
			}
			m.AddValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AddValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GoalLevel", wireType)
			}
			m.GoalLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GoalLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("add_value")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("goal_level")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRechargeResultInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRechargeResultInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRechargeResultInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CurrInfo == nil {
				m.CurrInfo = &NobilityInfo{}
			}
			if err := m.CurrInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ResultInfo == nil {
				m.ResultInfo = &NobilityInfo{}
			}
			if err := m.ResultInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("curr_info")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("result_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserNobilityValueReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserNobilityValueReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserNobilityValueReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Source = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("value")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("source")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserNobilityValueResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserNobilityValueResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserNobilityValueResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMemberNobilityInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMemberNobilityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMemberNobilityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMemberNobilityInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMemberNobilityInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMemberNobilityInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NobilityLevel", wireType)
			}
			m.NobilityLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NobilityLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Invisible", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Invisible = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("nobility_level")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("invisible")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PushNobilityLevelUpMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PushNobilityLevelUpMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PushNobilityLevelUpMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LevelName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Trigger", wireType)
			}
			m.Trigger = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Trigger |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PushNobilityLevelUpMsgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PushNobilityLevelUpMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PushNobilityLevelUpMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddRechargeTbeanReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddRechargeTbeanReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddRechargeTbeanReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Num", wireType)
			}
			m.Num = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Num |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChargeType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChargeType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PayChannel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PayChannel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChargeTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChargeTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("num")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("orderId")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("chargeType")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("payChannel")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("chargeTime")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddRechargeTbeanResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddRechargeTbeanResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddRechargeTbeanResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TempNobilityInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TempNobilityInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TempNobilityInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTs", wireType)
			}
			m.BeginTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTs", wireType)
			}
			m.EndTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTs", wireType)
			}
			m.UpdateTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TempNobilityAwardInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TempNobilityAwardInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TempNobilityAwardInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AwardImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AwardName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cnt", wireType)
			}
			m.Cnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddTempNobilityReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddTempNobilityReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddTempNobilityReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTs", wireType)
			}
			m.BeginTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddTempNobilityResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddTempNobilityResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddTempNobilityResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTempNobilityListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTempNobilityListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTempNobilityListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsValid", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsValid = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTempNobilityListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTempNobilityListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTempNobilityListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.InfoList = append(m.InfoList, &TempNobilityInfo{})
			if err := m.InfoList[len(m.InfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AwardInfoList = append(m.AwardInfoList, &TempNobilityAwardInfo{})
			if err := m.AwardInfoList[len(m.AwardInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetConsumeOrderCountReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetConsumeOrderCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetConsumeOrderCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTs", wireType)
			}
			m.BeginTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTs", wireType)
			}
			m.EndTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetConsumeOrderCountResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetConsumeOrderCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetConsumeOrderCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetConsumeOrderListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetConsumeOrderListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetConsumeOrderListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTs", wireType)
			}
			m.BeginTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTs", wireType)
			}
			m.EndTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetConsumeOrderListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetConsumeOrderListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetConsumeOrderListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderList = append(m.OrderList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FixConsumeOrderReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FixConsumeOrderReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FixConsumeOrderReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IncrValue", wireType)
			}
			m.IncrValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IncrValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConsumeTs", wireType)
			}
			m.ConsumeTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConsumeTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FixConsumeOrderResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FixConsumeOrderResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FixConsumeOrderResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipNobilitysvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNobilitysvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipNobilitysvr(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowNobilitysvr
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowNobilitysvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthNobilitysvr
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowNobilitysvr
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipNobilitysvr(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthNobilitysvr = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowNobilitysvr   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/nobilitysvr/nobilitysvr.proto", fileDescriptorNobilitysvr) }

var fileDescriptorNobilitysvr = []byte{
	// 2372 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x58, 0xcd, 0x6f, 0x1c, 0x49,
	0x15, 0x4f, 0xcf, 0x8c, 0xed, 0x99, 0x67, 0x8f, 0x3d, 0xae, 0xf8, 0x63, 0x32, 0x49, 0x9c, 0x76,
	0xc7, 0x21, 0x26, 0xbb, 0x4e, 0x50, 0x84, 0xc2, 0x32, 0x6b, 0x59, 0x24, 0x26, 0x6b, 0xcc, 0xc6,
	0x26, 0x9a, 0x4c, 0x12, 0x21, 0x21, 0x35, 0xed, 0xe9, 0xf2, 0xa4, 0xe5, 0x9e, 0xee, 0x4e, 0x57,
	0xb7, 0xd7, 0xb3, 0xa0, 0x15, 0x12, 0x12, 0x20, 0xb4, 0xd2, 0x22, 0x38, 0x20, 0x8e, 0x2b, 0xe5,
	0xc0, 0x91, 0x2b, 0x07, 0x24, 0xc4, 0x69, 0x8f, 0x48, 0x1c, 0xb8, 0x21, 0x14, 0x0e, 0xe4, 0xcf,
	0x40, 0x55, 0xd5, 0xd5, 0x53, 0xdd, 0x5d, 0x63, 0x5b, 0x41, 0xb9, 0xcd, 0xbc, 0x7a, 0xf5, 0xde,
	0xef, 0xbd, 0x7a, 0x9f, 0x0d, 0xab, 0x24, 0xec, 0xdd, 0xf1, 0xfc, 0x03, 0xc7, 0x75, 0xa2, 0x21,
	0x39, 0x0e, 0xe5, 0xdf, 0xb7, 0x83, 0xd0, 0x8f, 0x7c, 0x34, 0xbd, 0x9f, 0x90, 0x9e, 0x1c, 0x87,
	0xad, 0xb5, 0x9e, 0x3f, 0x18, 0xf8, 0xde, 0x9d, 0xc8, 0x3d, 0x0e, 0x9c, 0xde, 0x91, 0x8b, 0xef,
	0x90, 0xa3, 0x83, 0xd8, 0x71, 0x23, 0xc7, 0x8b, 0x86, 0x01, 0xe6, 0x57, 0x8c, 0x6d, 0x98, 0x13,
	0x97, 0x3a, 0xb8, 0xe7, 0x87, 0xd8, 0x46, 0x0b, 0x50, 0x8a, 0x48, 0x53, 0xd3, 0x4b, 0xeb, 0xf5,
	0x07, 0x95, 0xaf, 0xfe, 0x75, 0xed, 0x42, 0xa7, 0x14, 0x11, 0xd4, 0x82, 0x09, 0x17, 0x1f, 0x63,
	0xb7, 0x59, 0x92, 0x0e, 0x38, 0xc9, 0xe8, 0xc0, 0xfc, 0xbe, 0xcf, 0x64, 0xec, 0x7a, 0xc7, 0x98,
	0x44, 0xbb, 0xde, 0xa1, 0x3f, 0xba, 0xa0, 0xe9, 0x5a, 0xee, 0x02, 0x5a, 0x85, 0x5a, 0xdf, 0x0a,
	0xcc, 0x63, 0xcb, 0x8d, 0x71, 0xb3, 0xa4, 0x6b, 0xeb, 0xe5, 0xe4, 0xbc, 0xda, 0xb7, 0x82, 0x67,
	0x94, 0x6a, 0xfc, 0x61, 0x02, 0x66, 0x04, 0x32, 0x21, 0x8f, 0xf3, 0x53, 0x64, 0x15, 0x21, 0x8f,
	0x91, 0xd0, 0x75, 0x80, 0x23, 0x8c, 0x47, 0x02, 0x47, 0x0c, 0x35, 0x4a, 0x67, 0x12, 0x47, 0x80,
	0xca, 0x05, 0x0b, 0xd0, 0x35, 0xa8, 0xf6, 0x86, 0x3d, 0x17, 0x9b, 0x11, 0x69, 0x56, 0xa4, 0xe3,
	0x29, 0x46, 0xed, 0x12, 0xb4, 0x04, 0xe5, 0xd8, 0xb1, 0x9b, 0x13, 0xd2, 0x19, 0x25, 0x20, 0x03,
	0x6a, 0x8e, 0x77, 0xec, 0x10, 0xe7, 0xc0, 0xc5, 0xcd, 0x49, 0x5d, 0x5b, 0xaf, 0x0a, 0xc5, 0x29,
	0x19, 0xbd, 0x0f, 0x73, 0x9f, 0x58, 0x4e, 0x64, 0xf6, 0x7c, 0x12, 0x25, 0x10, 0xa7, 0x24, 0x9f,
	0xd4, 0xe9, 0xe1, 0xb6, 0x4f, 0x22, 0x0e, 0xf3, 0x5b, 0xb0, 0x18, 0xf9, 0x91, 0xe5, 0x9a, 0xf9,
	0x3b, 0x55, 0xe9, 0x0e, 0x62, 0x2c, 0xcf, 0x33, 0x17, 0xaf, 0x03, 0x30, 0x63, 0x4c, 0xcf, 0x1a,
	0xe0, 0x66, 0x4d, 0xd7, 0xd6, 0x6b, 0x02, 0x0b, 0xa3, 0xef, 0x5b, 0x03, 0x8c, 0xbe, 0x07, 0x0d,
	0x87, 0xbd, 0x91, 0xe9, 0x78, 0x87, 0xbe, 0xe9, 0x3a, 0x24, 0x6a, 0x82, 0x5e, 0x5e, 0x9f, 0xbe,
	0xbb, 0x72, 0x5b, 0x8a, 0x9e, 0xdb, 0x85, 0xf7, 0xec, 0xcc, 0x3a, 0xe9, 0xef, 0x47, 0x0e, 0x89,
	0xd0, 0x87, 0xb0, 0x1c, 0xe1, 0x41, 0x60, 0x8a, 0x30, 0x34, 0x43, 0x3c, 0xb0, 0x1c, 0x8f, 0x7a,
	0x70, 0x5a, 0x42, 0xba, 0x40, 0x99, 0x46, 0x21, 0x46, 0x59, 0xba, 0x84, 0x62, 0x0d, 0xb1, 0xe5,
	0x9a, 0xfc, 0x41, 0x66, 0x24, 0xfe, 0x1a, 0xa5, 0x3f, 0x62, 0x8f, 0x72, 0x1d, 0x00, 0x9f, 0x44,
	0xd8, 0x8b, 0xcc, 0x9e, 0x17, 0x35, 0xeb, 0x32, 0x13, 0xa7, 0x6f, 0x7b, 0x11, 0x75, 0xae, 0x6b,
	0xf5, 0x8e, 0xcc, 0x84, 0xf3, 0xd8, 0x72, 0x9b, 0xb3, 0xb2, 0x73, 0xe9, 0xe1, 0x43, 0x76, 0xf6,
	0xcc, 0x72, 0xd1, 0x55, 0x98, 0x3a, 0x4c, 0x94, 0xce, 0xe9, 0xda, 0x7a, 0x29, 0xe1, 0x9a, 0x3c,
	0xe4, 0x1a, 0x6f, 0xc0, 0x34, 0x77, 0x21, 0xf7, 0x78, 0x43, 0xd7, 0xd2, 0x40, 0xe2, 0xbe, 0xe5,
	0xb1, 0x19, 0x03, 0xda, 0xc1, 0x91, 0x1c, 0x9d, 0x1d, 0xfc, 0x12, 0x7d, 0x1d, 0xea, 0x1e, 0xc6,
	0xb6, 0x19, 0xf2, 0x3c, 0x22, 0x2c, 0xf0, 0x45, 0x38, 0xcc, 0xd0, 0xa3, 0x24, 0xc3, 0x08, 0xba,
	0x02, 0x93, 0xc4, 0x8f, 0xc3, 0x1e, 0x0f, 0x7e, 0xf1, 0x4c, 0x09, 0x4d, 0xc4, 0x5a, 0x59, 0x32,
	0x83, 0x12, 0x8c, 0x9f, 0xc2, 0xc5, 0x82, 0x5a, 0x12, 0xa0, 0x0d, 0xa8, 0xd0, 0xb7, 0x64, 0x79,
	0x31, 0x7d, 0xf7, 0x52, 0xee, 0x19, 0x25, 0x66, 0xc6, 0x86, 0xee, 0xc1, 0x14, 0x43, 0x68, 0x93,
	0x66, 0x89, 0x3d, 0xfc, 0x15, 0xe5, 0x8d, 0x04, 0x6b, 0x47, 0x30, 0x1b, 0x1b, 0xb0, 0xfc, 0xc0,
	0x8a, 0x7a, 0x2f, 0x14, 0x96, 0x23, 0xa8, 0xc4, 0x0e, 0x33, 0xb8, 0xbc, 0x5e, 0xef, 0xb0, 0xdf,
	0xc6, 0x8f, 0xa0, 0xa9, 0x66, 0x27, 0x01, 0xfa, 0x0e, 0xcc, 0xa6, 0x51, 0x43, 0x31, 0xf1, 0x9b,
	0xa7, 0x62, 0xaf, 0x7b, 0xd2, 0x3f, 0x2a, 0x7d, 0xf1, 0x09, 0x8e, 0x76, 0x45, 0x8a, 0x3d, 0x89,
	0xac, 0x28, 0x26, 0x14, 0x0a, 0xf5, 0x2c, 0xfb, 0xc3, 0xdc, 0x31, 0x91, 0x7a, 0x96, 0xd1, 0xe8,
	0xfb, 0xf2, 0x5f, 0x26, 0x2d, 0x81, 0xcc, 0xf9, 0xc2, 0xc3, 0xc0, 0x0f, 0xba, 0xc3, 0x00, 0x1b,
	0xf7, 0x60, 0x49, 0x25, 0x9d, 0x04, 0xa7, 0x8b, 0x37, 0x96, 0x61, 0x71, 0x47, 0x85, 0x8a, 0x0a,
	0xdc, 0x79, 0x1b, 0x81, 0xdf, 0x80, 0x96, 0x70, 0xa2, 0xc2, 0x56, 0x95, 0xdb, 0xf7, 0xe0, 0xf2,
	0xd8, 0x1b, 0x24, 0x40, 0xb7, 0xe1, 0x62, 0x42, 0x34, 0x13, 0x47, 0xb0, 0x0a, 0x40, 0x25, 0x54,
	0x3b, 0xf3, 0x19, 0x7e, 0x9a, 0xe4, 0xc6, 0x8f, 0x61, 0x61, 0x07, 0x47, 0xdd, 0x30, 0x1e, 0x04,
	0x38, 0x7a, 0x84, 0x0f, 0x69, 0xca, 0x51, 0xd5, 0x37, 0x61, 0x26, 0x08, 0x9d, 0x63, 0xc7, 0xc5,
	0x7d, 0x6c, 0x3a, 0x76, 0xa6, 0x5b, 0x4c, 0xa7, 0x27, 0xbb, 0x76, 0xa6, 0xb0, 0x96, 0x14, 0x85,
	0xd5, 0xf8, 0x80, 0xf9, 0x2c, 0xaf, 0x81, 0x04, 0xf4, 0xa6, 0x8b, 0x0f, 0x79, 0xee, 0xcb, 0x2d,
	0x64, 0xca, 0xe5, 0x4c, 0x46, 0x0f, 0x96, 0x3b, 0xd8, 0x8e, 0x7b, 0xf8, 0x5d, 0xc2, 0xfb, 0x10,
	0x9a, 0x6a, 0x25, 0xe7, 0x41, 0xf8, 0x85, 0x46, 0x1b, 0x23, 0x8f, 0xdb, 0x7d, 0x3f, 0x72, 0x0e,
	0x87, 0x7b, 0xa4, 0xcf, 0xfa, 0xeb, 0x30, 0xd7, 0x5f, 0x87, 0x22, 0xe9, 0x4b, 0xf9, 0x06, 0x73,
	0x5a, 0xd7, 0x5a, 0x82, 0xb2, 0x6d, 0x0d, 0x9b, 0x15, 0xb9, 0x50, 0xd8, 0xd6, 0x10, 0x35, 0xa1,
	0x42, 0x21, 0x34, 0x27, 0xa4, 0x03, 0x46, 0x31, 0x02, 0x98, 0xbd, 0x6f, 0xd3, 0x3a, 0xf4, 0xc2,
	0x0a, 0xfb, 0x98, 0xba, 0x6a, 0x15, 0x6a, 0x96, 0x6d, 0x9b, 0xa3, 0xd6, 0x2a, 0x2e, 0x54, 0x2d,
	0xdb, 0x4e, 0x1b, 0x4b, 0xdf, 0x4f, 0x8b, 0xb5, 0x8c, 0xb0, 0x46, 0xe9, 0x8f, 0x04, 0x16, 0x65,
	0xd1, 0x9a, 0x87, 0xb9, 0x8c, 0x46, 0x12, 0x18, 0x9f, 0x42, 0x73, 0x07, 0x47, 0x12, 0x29, 0x76,
	0x23, 0x51, 0x4a, 0xde, 0x35, 0x9c, 0x2f, 0x34, 0xb8, 0x34, 0x46, 0x39, 0x09, 0xd0, 0x3d, 0xa8,
	0xf5, 0xe2, 0x30, 0x34, 0xcf, 0x57, 0x4f, 0xab, 0x94, 0x97, 0xcd, 0x26, 0x6d, 0x98, 0x0e, 0x99,
	0x24, 0x7e, 0xb3, 0x74, 0xd6, 0x4d, 0x08, 0x53, 0xbd, 0xc6, 0xe7, 0x1a, 0x2c, 0xdf, 0xb7, 0xed,
	0xa7, 0x04, 0x87, 0x82, 0x87, 0xd9, 0x49, 0xbd, 0x91, 0x58, 0xa1, 0x29, 0x82, 0xa2, 0x38, 0xea,
	0x24, 0xb3, 0xd0, 0x35, 0xa8, 0xfa, 0xa1, 0x8d, 0x43, 0x93, 0x99, 0x5f, 0x4a, 0xbb, 0xcb, 0x14,
	0xa3, 0xee, 0xda, 0x52, 0xf3, 0xa9, 0x48, 0xc7, 0x09, 0xcd, 0x68, 0x41, 0x53, 0x8d, 0x86, 0x04,
	0xc6, 0x73, 0xf6, 0x70, 0x7b, 0x78, 0x70, 0x30, 0x3a, 0x15, 0x0f, 0x37, 0x0e, 0xea, 0x75, 0x80,
	0xde, 0x0b, 0xcb, 0xf3, 0xb0, 0x6b, 0xe6, 0xc2, 0xbb, 0x96, 0xd0, 0x77, 0x6d, 0xc3, 0x65, 0x8f,
	0xa2, 0x12, 0x4c, 0x02, 0xf4, 0x9e, 0xd4, 0x2d, 0xc4, 0x44, 0x39, 0x92, 0x92, 0x36, 0x06, 0xfe,
	0xee, 0x99, 0x79, 0x8c, 0x6a, 0x2b, 0xce, 0x63, 0xc6, 0x9f, 0x35, 0xb8, 0xf4, 0x38, 0x26, 0x2f,
	0xf6, 0xe5, 0x9b, 0x4f, 0x83, 0x3d, 0xd2, 0x3f, 0xc3, 0x10, 0x69, 0xbc, 0x2a, 0x49, 0xae, 0x93,
	0xc6, 0xab, 0xac, 0xb5, 0x65, 0xa5, 0xb5, 0xa3, 0x94, 0xae, 0x14, 0x53, 0x7a, 0x05, 0xa6, 0xa2,
	0xd0, 0xe9, 0xf7, 0x71, 0x98, 0xc9, 0x5e, 0x41, 0x34, 0xae, 0x40, 0x6b, 0x1c, 0x74, 0x12, 0x18,
	0xff, 0xd4, 0xe0, 0xa2, 0x94, 0x6d, 0xdd, 0x03, 0x6c, 0x79, 0xa7, 0xd9, 0xb4, 0x04, 0x65, 0x2f,
	0x1e, 0x30, 0x63, 0xc4, 0x04, 0x4e, 0x09, 0x14, 0x45, 0x12, 0x2d, 0xea, 0x10, 0x5a, 0x63, 0x66,
	0x52, 0x0d, 0xb4, 0x8d, 0xca, 0x61, 0x24, 0xd1, 0x29, 0x57, 0x60, 0x0d, 0xb7, 0xb9, 0xdd, 0x6c,
	0x74, 0x4e, 0xb9, 0x46, 0x74, 0x49, 0x96, 0x33, 0xa0, 0x23, 0x74, 0x51, 0x96, 0x33, 0xc0, 0xc6,
	0x12, 0x2c, 0x14, 0x0d, 0x23, 0x81, 0xf1, 0x17, 0x0d, 0x1a, 0x5d, 0x69, 0xc2, 0x64, 0xe9, 0x98,
	0x9a, 0x9b, 0x4d, 0x7e, 0x9a, 0x1a, 0x07, 0xb8, 0xcf, 0x67, 0x54, 0xb9, 0xf7, 0x4f, 0x31, 0x6a,
	0x97, 0xa0, 0xcb, 0x30, 0x89, 0x3d, 0x9b, 0x1e, 0xcb, 0x85, 0x63, 0x02, 0x7b, 0x76, 0x97, 0xd0,
	0xd2, 0x14, 0x07, 0xb6, 0x15, 0x25, 0x4b, 0xc2, 0xe8, 0xbc, 0xca, 0xc9, 0x5d, 0x69, 0x49, 0x9a,
	0x28, 0xee, 0x3c, 0x0b, 0x50, 0x72, 0x6c, 0xb6, 0x22, 0xa4, 0x65, 0xdf, 0xb1, 0x8d, 0xcf, 0x60,
	0x51, 0x86, 0x7f, 0xff, 0x13, 0x2b, 0xb4, 0x99, 0x0d, 0xeb, 0x50, 0xb7, 0xe8, 0x1f, 0xd3, 0x19,
	0xf4, 0xcd, 0x38, 0xe4, 0x6b, 0x94, 0xf0, 0xcc, 0x34, 0x3b, 0xda, 0x1d, 0xf4, 0x9f, 0x86, 0x6c,
	0x4c, 0xe6, 0x9c, 0x49, 0x60, 0x4a, 0x73, 0x3f, 0xa3, 0xb3, 0xc0, 0x5c, 0x82, 0x32, 0x6d, 0x53,
	0x99, 0x7a, 0xd8, 0xf3, 0x22, 0xc3, 0x01, 0x74, 0xdf, 0xb6, 0xbb, 0x99, 0x19, 0xfd, 0xe5, 0xdb,
	0x3b, 0x50, 0xea, 0x56, 0x79, 0x07, 0x18, 0x8b, 0x2c, 0x36, 0xb3, 0xaa, 0x48, 0x60, 0x7c, 0x9b,
	0xcd, 0x46, 0x32, 0x99, 0x4e, 0x1e, 0x14, 0xc5, 0x35, 0xa8, 0x3a, 0x84, 0xb6, 0x82, 0x04, 0x8a,
	0x48, 0xe5, 0x29, 0x87, 0x3c, 0xa3, 0x44, 0xe3, 0x4b, 0x0d, 0x96, 0x95, 0x77, 0x49, 0x80, 0xda,
	0xb4, 0x10, 0x88, 0x0d, 0x87, 0x8f, 0x97, 0x57, 0x33, 0x05, 0x39, 0x1f, 0x35, 0x9d, 0xaa, 0x23,
	0x56, 0x9b, 0xef, 0xc3, 0x5c, 0xe2, 0xfb, 0x54, 0x02, 0x1f, 0x95, 0x8d, 0xb1, 0x12, 0xd2, 0x87,
	0xeb, 0xf0, 0x67, 0x13, 0x6b, 0x92, 0xf1, 0x9c, 0x41, 0xdc, 0xf6, 0x3d, 0x12, 0x0f, 0xf0, 0x0f,
	0x68, 0xfe, 0x6c, 0xfb, 0xb1, 0x27, 0xec, 0x4b, 0xbd, 0xa9, 0x9d, 0x1e, 0x8e, 0xa5, 0x42, 0x38,
	0x1a, 0x1d, 0x56, 0x8c, 0x15, 0x82, 0x49, 0x40, 0x9f, 0xa1, 0x47, 0xff, 0x64, 0x77, 0x6f, 0x46,
	0x92, 0x7b, 0x87, 0x74, 0xc6, 0x48, 0xc6, 0x33, 0xf6, 0x16, 0xb2, 0x4c, 0xe9, 0x2d, 0xfe, 0x0f,
	0xac, 0x1f, 0x14, 0x9c, 0x90, 0xbe, 0xd3, 0x55, 0x00, 0xde, 0xae, 0xd2, 0x87, 0xaa, 0x75, 0x6a,
	0xbe, 0x60, 0x31, 0x7e, 0xaf, 0x01, 0xfa, 0xc8, 0x39, 0x91, 0xaf, 0x26, 0x70, 0xd2, 0x26, 0x27,
	0x27, 0x46, 0x5a, 0xa1, 0xd2, 0x71, 0x4a, 0x2b, 0x54, 0x71, 0xc7, 0xeb, 0x85, 0xc9, 0x80, 0x21,
	0x47, 0x69, 0x8d, 0xd2, 0xd3, 0x09, 0xa3, 0xc7, 0x15, 0xe6, 0x53, 0xbd, 0x96, 0xd0, 0xbb, 0x84,
	0x86, 0x73, 0x01, 0x18, 0x09, 0x6e, 0x7d, 0x13, 0xea, 0x1f, 0x1f, 0x1e, 0x3d, 0xf7, 0xc3, 0x23,
	0x1c, 0xb2, 0x2a, 0x38, 0x0f, 0xf5, 0xc7, 0x21, 0x26, 0xd8, 0x8b, 0x38, 0xb1, 0xa1, 0xa1, 0x06,
	0xcc, 0x6c, 0x5b, 0xd1, 0x47, 0x0e, 0x79, 0xf1, 0xdc, 0xa7, 0x94, 0xd2, 0xad, 0x36, 0xd4, 0xd3,
	0x71, 0x9d, 0xdd, 0x5a, 0x84, 0xf9, 0x87, 0xfb, 0x4f, 0xf7, 0xcc, 0x7d, 0x7f, 0x34, 0xc7, 0x37,
	0x2e, 0x20, 0x04, 0xb3, 0x8c, 0x3c, 0xa2, 0x69, 0xb7, 0xfe, 0x5a, 0x1a, 0x4d, 0x99, 0xac, 0x1f,
	0x30, 0x01, 0x2d, 0x58, 0x12, 0x44, 0x93, 0x51, 0xe9, 0x1d, 0x9a, 0x35, 0x8d, 0x0b, 0xa8, 0x09,
	0x0b, 0xb9, 0xb3, 0x07, 0x56, 0xe8, 0x7b, 0x0d, 0x0d, 0x5d, 0x86, 0xe5, 0xdc, 0xc9, 0x33, 0x87,
	0xb0, 0xb8, 0x69, 0x94, 0xd0, 0x32, 0x5c, 0xcc, 0x1d, 0x3e, 0xb4, 0x42, 0xb7, 0x51, 0x56, 0xe8,
	0xda, 0xb3, 0xc2, 0x97, 0xb1, 0x43, 0x1a, 0x15, 0xc5, 0xa5, 0xef, 0xc6, 0x47, 0xb8, 0x31, 0x81,
	0xd6, 0x40, 0xcf, 0x1d, 0x6c, 0xfb, 0x83, 0x81, 0xe5, 0xd9, 0x38, 0x1c, 0x9a, 0x8f, 0x43, 0xc7,
	0xeb, 0xe1, 0xc6, 0x24, 0xba, 0x04, 0x8b, 0x39, 0xae, 0xe4, 0x68, 0x4a, 0x21, 0xf9, 0x63, 0xc7,
	0xeb, 0x37, 0xaa, 0x68, 0x09, 0x50, 0xee, 0x60, 0xc7, 0xb7, 0x1b, 0x35, 0x85, 0xd9, 0x3f, 0xf4,
	0x63, 0xaf, 0xdf, 0x80, 0xbb, 0xff, 0xbd, 0x08, 0xf2, 0xb7, 0x33, 0xe4, 0xc3, 0x5c, 0x6e, 0x6f,
	0x45, 0xd7, 0x32, 0xa9, 0x5f, 0x5c, 0x82, 0x5b, 0xfa, 0xe9, 0x0c, 0x24, 0x30, 0x2e, 0xfd, 0xec,
	0xd5, 0x9b, 0xb2, 0xf6, 0xeb, 0x57, 0x6f, 0xca, 0xa5, 0xb8, 0xfd, 0xdb, 0x57, 0x6f, 0xca, 0xd5,
	0x8d, 0x58, 0xdf, 0x8c, 0x1d, 0x7b, 0x0b, 0x7d, 0x06, 0x0b, 0xaa, 0x6d, 0x19, 0xad, 0x65, 0x84,
	0x8e, 0xd9, 0xbf, 0x5b, 0x37, 0xce, 0xc1, 0x25, 0xf4, 0x97, 0x94, 0xfa, 0x7f, 0xa1, 0x01, 0x2a,
	0xae, 0xbc, 0x28, 0x5b, 0xef, 0x94, 0x1b, 0x77, 0xeb, 0xfa, 0x99, 0x3c, 0x24, 0x30, 0x6e, 0x52,
	0xd5, 0x65, 0xaa, 0xba, 0x12, 0xb7, 0x2d, 0xa6, 0x7c, 0x41, 0x28, 0xd7, 0x37, 0x2c, 0x7d, 0x93,
	0x6f, 0xa3, 0x5b, 0xe8, 0x84, 0x7d, 0x5a, 0x39, 0x1d, 0xc7, 0xce, 0x39, 0x70, 0xa8, 0xf7, 0x5f,
	0xee, 0x82, 0x8a, 0xd2, 0x05, 0xbf, 0xd4, 0x46, 0x1f, 0x38, 0xf2, 0xfa, 0x6f, 0x2a, 0x1d, 0xac,
	0x00, 0xb1, 0x7e, 0x3e, 0x46, 0x81, 0x64, 0x42, 0x89, 0x24, 0x86, 0xf9, 0xc2, 0x4a, 0x8c, 0x56,
	0xf3, 0xe6, 0x15, 0xb6, 0xde, 0x96, 0x71, 0x16, 0x8b, 0x50, 0x3b, 0x39, 0x2e, 0x06, 0x55, 0xab,
	0x6e, 0x2e, 0x06, 0xc7, 0xac, 0xdc, 0xb9, 0x18, 0x1c, 0xb7, 0x33, 0x73, 0xfd, 0x53, 0x4a, 0xfd,
	0x3f, 0xd7, 0x60, 0x5a, 0x9a, 0xf1, 0xd0, 0xe5, 0x8c, 0xc4, 0xec, 0xda, 0xda, 0xba, 0x32, 0xfe,
	0x90, 0xce, 0x14, 0x54, 0x4b, 0x95, 0x6a, 0x99, 0x8c, 0xdb, 0x61, 0xdb, 0x65, 0x9a, 0xd6, 0x46,
	0x01, 0x17, 0x6e, 0x86, 0x09, 0xfb, 0x96, 0xbe, 0xe1, 0xea, 0x9b, 0xe9, 0xde, 0xb8, 0x85, 0xbe,
	0xd4, 0xd8, 0x07, 0x89, 0xe2, 0x82, 0x88, 0x6e, 0xe4, 0xdd, 0xab, 0xdc, 0x60, 0x5b, 0x5f, 0x3b,
	0x0f, 0x9b, 0xc0, 0x58, 0x7b, 0x2b, 0x8c, 0x7f, 0xd4, 0xd8, 0x34, 0x5c, 0x58, 0xd2, 0x72, 0x4f,
	0x35, 0x66, 0xab, 0xcc, 0x3d, 0xd5, 0xd8, 0x6d, 0x6f, 0x9b, 0x02, 0x04, 0x0a, 0xb0, 0x1a, 0xb7,
	0xdd, 0xb6, 0xdf, 0x26, 0x0c, 0xe2, 0xfb, 0x23, 0x88, 0xae, 0xbe, 0xc9, 0xfa, 0xe8, 0x96, 0xbe,
	0xe1, 0xeb, 0x9b, 0x49, 0xeb, 0xdd, 0xd2, 0x37, 0x88, 0xbe, 0xc9, 0xb7, 0xc9, 0x2d, 0xf4, 0x3b,
	0xee, 0xce, 0xe2, 0x6a, 0x57, 0x74, 0xa7, 0x72, 0xaf, 0x2c, 0xba, 0x53, 0xbd, 0x25, 0x1a, 0xef,
	0x51, 0xb4, 0xd3, 0xac, 0xc2, 0x9c, 0xb4, 0x79, 0x68, 0x35, 0x37, 0x4e, 0xf4, 0xcd, 0x64, 0xed,
	0xd2, 0x19, 0x60, 0x11, 0x6a, 0x7f, 0xd3, 0x60, 0x49, 0xbd, 0x46, 0xa1, 0xac, 0xbe, 0xb1, 0x6b,
	0x62, 0xeb, 0xe6, 0xb9, 0xf8, 0xe8, 0xd2, 0x4c, 0x81, 0xcd, 0x50, 0x60, 0x10, 0xb7, 0x4f, 0xda,
	0x6e, 0xdb, 0x6a, 0x47, 0x0c, 0xde, 0xd6, 0xc8, 0x91, 0x23, 0x9c, 0xdc, 0xaf, 0x9b, 0x6c, 0x62,
	0xe6, 0x95, 0x91, 0xfd, 0xd4, 0xe9, 0x6c, 0xbf, 0xa5, 0x6f, 0x44, 0xfa, 0x66, 0xb2, 0x09, 0x6e,
	0xa1, 0x3f, 0x69, 0xd0, 0xc8, 0xef, 0x44, 0x48, 0x1f, 0x97, 0x17, 0x62, 0x17, 0x6c, 0xad, 0x9e,
	0xc1, 0x21, 0x20, 0xd7, 0xdf, 0x01, 0xe4, 0xcf, 0x35, 0xf6, 0x35, 0x48, 0x1e, 0x9c, 0x73, 0x8d,
	0xb5, 0xb8, 0x8c, 0xb4, 0xf4, 0xd3, 0x19, 0x48, 0x60, 0xdc, 0xa3, 0x78, 0x67, 0x93, 0x54, 0x3a,
	0x48, 0x52, 0x69, 0x35, 0x83, 0x55, 0x0c, 0xaf, 0x3c, 0x6a, 0x39, 0x56, 0xf4, 0x13, 0xf6, 0x41,
	0x3d, 0xbf, 0x3e, 0xa0, 0x42, 0x27, 0x51, 0x2c, 0x27, 0xad, 0xb5, 0xb3, 0x99, 0x44, 0xb9, 0x9b,
	0x1b, 0x57, 0x6e, 0x55, 0xf3, 0x3b, 0x2a, 0x08, 0x56, 0xed, 0x0e, 0xad, 0x1b, 0xe7, 0xe0, 0x12,
	0xfa, 0x1b, 0x4a, 0xfd, 0xdc, 0xf8, 0xfc, 0x4c, 0x5e, 0x34, 0x5e, 0xb1, 0x0d, 0xb4, 0xd6, 0xce,
	0x66, 0x12, 0xca, 0xe7, 0x95, 0xca, 0x7d, 0x98, 0xcb, 0x0d, 0xcf, 0xb9, 0x38, 0x28, 0xce, 0xfc,
	0xb9, 0x38, 0x50, 0xcc, 0xde, 0x5c, 0x21, 0x52, 0x29, 0x6c, 0x4d, 0xfe, 0xea, 0xd5, 0x9b, 0xf2,
	0x3f, 0x3e, 0x7d, 0xd0, 0xf8, 0xea, 0xf5, 0x8a, 0xf6, 0xf7, 0xd7, 0x2b, 0xda, 0xbf, 0x5f, 0xaf,
	0x68, 0xbf, 0xf9, 0xcf, 0xca, 0x85, 0xff, 0x05, 0x00, 0x00, 0xff, 0xff, 0x1e, 0x3e, 0x1a, 0x52,
	0x58, 0x1d, 0x00, 0x00,
}
