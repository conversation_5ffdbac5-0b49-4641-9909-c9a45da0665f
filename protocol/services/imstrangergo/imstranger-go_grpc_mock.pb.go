// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/imstranger-go/imstranger-go.proto

package imstrangergo

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockImstrangerGoClient is a mock of ImstrangerGoClient interface.
type MockImstrangerGoClient struct {
	ctrl     *gomock.Controller
	recorder *MockImstrangerGoClientMockRecorder
}

// MockImstrangerGoClientMockRecorder is the mock recorder for MockImstrangerGoClient.
type MockImstrangerGoClientMockRecorder struct {
	mock *MockImstrangerGoClient
}

// NewMockImstrangerGoClient creates a new mock instance.
func NewMockImstrangerGoClient(ctrl *gomock.Controller) *MockImstrangerGoClient {
	mock := &MockImstrangerGoClient{ctrl: ctrl}
	mock.recorder = &MockImstrangerGoClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockImstrangerGoClient) EXPECT() *MockImstrangerGoClientMockRecorder {
	return m.recorder
}

// BatchCheckNobilityPrivilege mocks base method.
func (m *MockImstrangerGoClient) BatchCheckNobilityPrivilege(ctx context.Context, in *BatchCheckNobilityPrivilegeReq, opts ...grpc.CallOption) (*BatchCheckNobilityPrivilegeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchCheckNobilityPrivilege", varargs...)
	ret0, _ := ret[0].(*BatchCheckNobilityPrivilegeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckNobilityPrivilege indicates an expected call of BatchCheckNobilityPrivilege.
func (mr *MockImstrangerGoClientMockRecorder) BatchCheckNobilityPrivilege(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckNobilityPrivilege", reflect.TypeOf((*MockImstrangerGoClient)(nil).BatchCheckNobilityPrivilege), varargs...)
}

// BatchCheckUserRecvLimit mocks base method.
func (m *MockImstrangerGoClient) BatchCheckUserRecvLimit(ctx context.Context, in *BatchCheckUserRecvLimitReq, opts ...grpc.CallOption) (*BatchCheckUserRecvLimitResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchCheckUserRecvLimit", varargs...)
	ret0, _ := ret[0].(*BatchCheckUserRecvLimitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckUserRecvLimit indicates an expected call of BatchCheckUserRecvLimit.
func (mr *MockImstrangerGoClientMockRecorder) BatchCheckUserRecvLimit(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckUserRecvLimit", reflect.TypeOf((*MockImstrangerGoClient)(nil).BatchCheckUserRecvLimit), varargs...)
}

// BatchGetStrangerRecvMsgLimit mocks base method.
func (m *MockImstrangerGoClient) BatchGetStrangerRecvMsgLimit(ctx context.Context, in *BatchGetStrangerRecvMsgLimitReq, opts ...grpc.CallOption) (*BatchGetStrangerRecvMsgLimitResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetStrangerRecvMsgLimit", varargs...)
	ret0, _ := ret[0].(*BatchGetStrangerRecvMsgLimitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetStrangerRecvMsgLimit indicates an expected call of BatchGetStrangerRecvMsgLimit.
func (mr *MockImstrangerGoClientMockRecorder) BatchGetStrangerRecvMsgLimit(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetStrangerRecvMsgLimit", reflect.TypeOf((*MockImstrangerGoClient)(nil).BatchGetStrangerRecvMsgLimit), varargs...)
}

// ChatAward mocks base method.
func (m *MockImstrangerGoClient) ChatAward(ctx context.Context, in *ChatAwardReq, opts ...grpc.CallOption) (*ChatAwardResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ChatAward", varargs...)
	ret0, _ := ret[0].(*ChatAwardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChatAward indicates an expected call of ChatAward.
func (mr *MockImstrangerGoClientMockRecorder) ChatAward(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChatAward", reflect.TypeOf((*MockImstrangerGoClient)(nil).ChatAward), varargs...)
}

// CheckIMPresentToTarget mocks base method.
func (m *MockImstrangerGoClient) CheckIMPresentToTarget(ctx context.Context, in *CheckIMPresentToTargetReq, opts ...grpc.CallOption) (*CheckIMPresentToTargetResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckIMPresentToTarget", varargs...)
	ret0, _ := ret[0].(*CheckIMPresentToTargetResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIMPresentToTarget indicates an expected call of CheckIMPresentToTarget.
func (mr *MockImstrangerGoClientMockRecorder) CheckIMPresentToTarget(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIMPresentToTarget", reflect.TypeOf((*MockImstrangerGoClient)(nil).CheckIMPresentToTarget), varargs...)
}

// CheckNobilityPrivilege mocks base method.
func (m *MockImstrangerGoClient) CheckNobilityPrivilege(ctx context.Context, in *CheckNobilityPrivilegeReq, opts ...grpc.CallOption) (*CheckNobilityPrivilegeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckNobilityPrivilege", varargs...)
	ret0, _ := ret[0].(*CheckNobilityPrivilegeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckNobilityPrivilege indicates an expected call of CheckNobilityPrivilege.
func (mr *MockImstrangerGoClientMockRecorder) CheckNobilityPrivilege(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckNobilityPrivilege", reflect.TypeOf((*MockImstrangerGoClient)(nil).CheckNobilityPrivilege), varargs...)
}

// CheckStrangerMsgLimit mocks base method.
func (m *MockImstrangerGoClient) CheckStrangerMsgLimit(ctx context.Context, in *CheckStrangerMsgLimitReq, opts ...grpc.CallOption) (*CheckStrangerMsgLimitResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckStrangerMsgLimit", varargs...)
	ret0, _ := ret[0].(*CheckStrangerMsgLimitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckStrangerMsgLimit indicates an expected call of CheckStrangerMsgLimit.
func (mr *MockImstrangerGoClientMockRecorder) CheckStrangerMsgLimit(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckStrangerMsgLimit", reflect.TypeOf((*MockImstrangerGoClient)(nil).CheckStrangerMsgLimit), varargs...)
}

// CheckStrangerMsgTargeter mocks base method.
func (m *MockImstrangerGoClient) CheckStrangerMsgTargeter(ctx context.Context, in *CheckStrangerMsgTargeterReq, opts ...grpc.CallOption) (*CheckStrangerMsgTargeterResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckStrangerMsgTargeter", varargs...)
	ret0, _ := ret[0].(*CheckStrangerMsgTargeterResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckStrangerMsgTargeter indicates an expected call of CheckStrangerMsgTargeter.
func (mr *MockImstrangerGoClientMockRecorder) CheckStrangerMsgTargeter(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckStrangerMsgTargeter", reflect.TypeOf((*MockImstrangerGoClient)(nil).CheckStrangerMsgTargeter), varargs...)
}

// CheckSuperPlayerStatus mocks base method.
func (m *MockImstrangerGoClient) CheckSuperPlayerStatus(ctx context.Context, in *CheckSuperPlayerStatusReq, opts ...grpc.CallOption) (*CheckSuperPlayerStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckSuperPlayerStatus", varargs...)
	ret0, _ := ret[0].(*CheckSuperPlayerStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckSuperPlayerStatus indicates an expected call of CheckSuperPlayerStatus.
func (mr *MockImstrangerGoClientMockRecorder) CheckSuperPlayerStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckSuperPlayerStatus", reflect.TypeOf((*MockImstrangerGoClient)(nil).CheckSuperPlayerStatus), varargs...)
}

// GetStrangerGreetDetail mocks base method.
func (m *MockImstrangerGoClient) GetStrangerGreetDetail(ctx context.Context, in *GetStrangerGreetDetailReq, opts ...grpc.CallOption) (*GetStrangerGreetDetailResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStrangerGreetDetail", varargs...)
	ret0, _ := ret[0].(*GetStrangerGreetDetailResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStrangerGreetDetail indicates an expected call of GetStrangerGreetDetail.
func (mr *MockImstrangerGoClientMockRecorder) GetStrangerGreetDetail(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStrangerGreetDetail", reflect.TypeOf((*MockImstrangerGoClient)(nil).GetStrangerGreetDetail), varargs...)
}

// IncrStrangerMsgLimit mocks base method.
func (m *MockImstrangerGoClient) IncrStrangerMsgLimit(ctx context.Context, in *IncrStrangerMsgLimitReq, opts ...grpc.CallOption) (*IncrStrangerMsgLimitResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IncrStrangerMsgLimit", varargs...)
	ret0, _ := ret[0].(*IncrStrangerMsgLimitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrStrangerMsgLimit indicates an expected call of IncrStrangerMsgLimit.
func (mr *MockImstrangerGoClientMockRecorder) IncrStrangerMsgLimit(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrStrangerMsgLimit", reflect.TypeOf((*MockImstrangerGoClient)(nil).IncrStrangerMsgLimit), varargs...)
}

// IsContractGuildSuperiorAndSubordinate mocks base method.
func (m *MockImstrangerGoClient) IsContractGuildSuperiorAndSubordinate(ctx context.Context, in *IsContractGuildSuperiorAndSubordinateReq, opts ...grpc.CallOption) (*IsContractGuildSuperiorAndSubordinateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsContractGuildSuperiorAndSubordinate", varargs...)
	ret0, _ := ret[0].(*IsContractGuildSuperiorAndSubordinateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsContractGuildSuperiorAndSubordinate indicates an expected call of IsContractGuildSuperiorAndSubordinate.
func (mr *MockImstrangerGoClientMockRecorder) IsContractGuildSuperiorAndSubordinate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsContractGuildSuperiorAndSubordinate", reflect.TypeOf((*MockImstrangerGoClient)(nil).IsContractGuildSuperiorAndSubordinate), varargs...)
}

// IsHighConsumeIn24Hour mocks base method.
func (m *MockImstrangerGoClient) IsHighConsumeIn24Hour(ctx context.Context, in *IsHighConsumeIn24HourReq, opts ...grpc.CallOption) (*IsHighConsumeIn24HourResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsHighConsumeIn24Hour", varargs...)
	ret0, _ := ret[0].(*IsHighConsumeIn24HourResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsHighConsumeIn24Hour indicates an expected call of IsHighConsumeIn24Hour.
func (mr *MockImstrangerGoClientMockRecorder) IsHighConsumeIn24Hour(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsHighConsumeIn24Hour", reflect.TypeOf((*MockImstrangerGoClient)(nil).IsHighConsumeIn24Hour), varargs...)
}

// IsStrangerMsgSender mocks base method.
func (m *MockImstrangerGoClient) IsStrangerMsgSender(ctx context.Context, in *IsStrangerMsgSenderReq, opts ...grpc.CallOption) (*IsStrangerMsgSenderResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsStrangerMsgSender", varargs...)
	ret0, _ := ret[0].(*IsStrangerMsgSenderResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsStrangerMsgSender indicates an expected call of IsStrangerMsgSender.
func (mr *MockImstrangerGoClientMockRecorder) IsStrangerMsgSender(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsStrangerMsgSender", reflect.TypeOf((*MockImstrangerGoClient)(nil).IsStrangerMsgSender), varargs...)
}

// SetHighConsumeIn24Hour mocks base method.
func (m *MockImstrangerGoClient) SetHighConsumeIn24Hour(ctx context.Context, in *SetHighConsumeIn24HourReq, opts ...grpc.CallOption) (*SetHighConsumeIn24HourResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetHighConsumeIn24Hour", varargs...)
	ret0, _ := ret[0].(*SetHighConsumeIn24HourResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetHighConsumeIn24Hour indicates an expected call of SetHighConsumeIn24Hour.
func (mr *MockImstrangerGoClientMockRecorder) SetHighConsumeIn24Hour(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHighConsumeIn24Hour", reflect.TypeOf((*MockImstrangerGoClient)(nil).SetHighConsumeIn24Hour), varargs...)
}

// SetIMPresentToTarget mocks base method.
func (m *MockImstrangerGoClient) SetIMPresentToTarget(ctx context.Context, in *SetIMPresentToTargetReq, opts ...grpc.CallOption) (*SetIMPresentToTargetResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetIMPresentToTarget", varargs...)
	ret0, _ := ret[0].(*SetIMPresentToTargetResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetIMPresentToTarget indicates an expected call of SetIMPresentToTarget.
func (mr *MockImstrangerGoClientMockRecorder) SetIMPresentToTarget(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetIMPresentToTarget", reflect.TypeOf((*MockImstrangerGoClient)(nil).SetIMPresentToTarget), varargs...)
}

// MockImstrangerGoServer is a mock of ImstrangerGoServer interface.
type MockImstrangerGoServer struct {
	ctrl     *gomock.Controller
	recorder *MockImstrangerGoServerMockRecorder
}

// MockImstrangerGoServerMockRecorder is the mock recorder for MockImstrangerGoServer.
type MockImstrangerGoServerMockRecorder struct {
	mock *MockImstrangerGoServer
}

// NewMockImstrangerGoServer creates a new mock instance.
func NewMockImstrangerGoServer(ctrl *gomock.Controller) *MockImstrangerGoServer {
	mock := &MockImstrangerGoServer{ctrl: ctrl}
	mock.recorder = &MockImstrangerGoServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockImstrangerGoServer) EXPECT() *MockImstrangerGoServerMockRecorder {
	return m.recorder
}

// BatchCheckNobilityPrivilege mocks base method.
func (m *MockImstrangerGoServer) BatchCheckNobilityPrivilege(ctx context.Context, in *BatchCheckNobilityPrivilegeReq) (*BatchCheckNobilityPrivilegeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCheckNobilityPrivilege", ctx, in)
	ret0, _ := ret[0].(*BatchCheckNobilityPrivilegeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckNobilityPrivilege indicates an expected call of BatchCheckNobilityPrivilege.
func (mr *MockImstrangerGoServerMockRecorder) BatchCheckNobilityPrivilege(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckNobilityPrivilege", reflect.TypeOf((*MockImstrangerGoServer)(nil).BatchCheckNobilityPrivilege), ctx, in)
}

// BatchCheckUserRecvLimit mocks base method.
func (m *MockImstrangerGoServer) BatchCheckUserRecvLimit(ctx context.Context, in *BatchCheckUserRecvLimitReq) (*BatchCheckUserRecvLimitResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCheckUserRecvLimit", ctx, in)
	ret0, _ := ret[0].(*BatchCheckUserRecvLimitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckUserRecvLimit indicates an expected call of BatchCheckUserRecvLimit.
func (mr *MockImstrangerGoServerMockRecorder) BatchCheckUserRecvLimit(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckUserRecvLimit", reflect.TypeOf((*MockImstrangerGoServer)(nil).BatchCheckUserRecvLimit), ctx, in)
}

// BatchGetStrangerRecvMsgLimit mocks base method.
func (m *MockImstrangerGoServer) BatchGetStrangerRecvMsgLimit(ctx context.Context, in *BatchGetStrangerRecvMsgLimitReq) (*BatchGetStrangerRecvMsgLimitResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetStrangerRecvMsgLimit", ctx, in)
	ret0, _ := ret[0].(*BatchGetStrangerRecvMsgLimitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetStrangerRecvMsgLimit indicates an expected call of BatchGetStrangerRecvMsgLimit.
func (mr *MockImstrangerGoServerMockRecorder) BatchGetStrangerRecvMsgLimit(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetStrangerRecvMsgLimit", reflect.TypeOf((*MockImstrangerGoServer)(nil).BatchGetStrangerRecvMsgLimit), ctx, in)
}

// ChatAward mocks base method.
func (m *MockImstrangerGoServer) ChatAward(ctx context.Context, in *ChatAwardReq) (*ChatAwardResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChatAward", ctx, in)
	ret0, _ := ret[0].(*ChatAwardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChatAward indicates an expected call of ChatAward.
func (mr *MockImstrangerGoServerMockRecorder) ChatAward(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChatAward", reflect.TypeOf((*MockImstrangerGoServer)(nil).ChatAward), ctx, in)
}

// CheckIMPresentToTarget mocks base method.
func (m *MockImstrangerGoServer) CheckIMPresentToTarget(ctx context.Context, in *CheckIMPresentToTargetReq) (*CheckIMPresentToTargetResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIMPresentToTarget", ctx, in)
	ret0, _ := ret[0].(*CheckIMPresentToTargetResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIMPresentToTarget indicates an expected call of CheckIMPresentToTarget.
func (mr *MockImstrangerGoServerMockRecorder) CheckIMPresentToTarget(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIMPresentToTarget", reflect.TypeOf((*MockImstrangerGoServer)(nil).CheckIMPresentToTarget), ctx, in)
}

// CheckNobilityPrivilege mocks base method.
func (m *MockImstrangerGoServer) CheckNobilityPrivilege(ctx context.Context, in *CheckNobilityPrivilegeReq) (*CheckNobilityPrivilegeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckNobilityPrivilege", ctx, in)
	ret0, _ := ret[0].(*CheckNobilityPrivilegeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckNobilityPrivilege indicates an expected call of CheckNobilityPrivilege.
func (mr *MockImstrangerGoServerMockRecorder) CheckNobilityPrivilege(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckNobilityPrivilege", reflect.TypeOf((*MockImstrangerGoServer)(nil).CheckNobilityPrivilege), ctx, in)
}

// CheckStrangerMsgLimit mocks base method.
func (m *MockImstrangerGoServer) CheckStrangerMsgLimit(ctx context.Context, in *CheckStrangerMsgLimitReq) (*CheckStrangerMsgLimitResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckStrangerMsgLimit", ctx, in)
	ret0, _ := ret[0].(*CheckStrangerMsgLimitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckStrangerMsgLimit indicates an expected call of CheckStrangerMsgLimit.
func (mr *MockImstrangerGoServerMockRecorder) CheckStrangerMsgLimit(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckStrangerMsgLimit", reflect.TypeOf((*MockImstrangerGoServer)(nil).CheckStrangerMsgLimit), ctx, in)
}

// CheckStrangerMsgTargeter mocks base method.
func (m *MockImstrangerGoServer) CheckStrangerMsgTargeter(ctx context.Context, in *CheckStrangerMsgTargeterReq) (*CheckStrangerMsgTargeterResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckStrangerMsgTargeter", ctx, in)
	ret0, _ := ret[0].(*CheckStrangerMsgTargeterResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckStrangerMsgTargeter indicates an expected call of CheckStrangerMsgTargeter.
func (mr *MockImstrangerGoServerMockRecorder) CheckStrangerMsgTargeter(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckStrangerMsgTargeter", reflect.TypeOf((*MockImstrangerGoServer)(nil).CheckStrangerMsgTargeter), ctx, in)
}

// CheckSuperPlayerStatus mocks base method.
func (m *MockImstrangerGoServer) CheckSuperPlayerStatus(ctx context.Context, in *CheckSuperPlayerStatusReq) (*CheckSuperPlayerStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckSuperPlayerStatus", ctx, in)
	ret0, _ := ret[0].(*CheckSuperPlayerStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckSuperPlayerStatus indicates an expected call of CheckSuperPlayerStatus.
func (mr *MockImstrangerGoServerMockRecorder) CheckSuperPlayerStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckSuperPlayerStatus", reflect.TypeOf((*MockImstrangerGoServer)(nil).CheckSuperPlayerStatus), ctx, in)
}

// GetStrangerGreetDetail mocks base method.
func (m *MockImstrangerGoServer) GetStrangerGreetDetail(ctx context.Context, in *GetStrangerGreetDetailReq) (*GetStrangerGreetDetailResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStrangerGreetDetail", ctx, in)
	ret0, _ := ret[0].(*GetStrangerGreetDetailResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStrangerGreetDetail indicates an expected call of GetStrangerGreetDetail.
func (mr *MockImstrangerGoServerMockRecorder) GetStrangerGreetDetail(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStrangerGreetDetail", reflect.TypeOf((*MockImstrangerGoServer)(nil).GetStrangerGreetDetail), ctx, in)
}

// IncrStrangerMsgLimit mocks base method.
func (m *MockImstrangerGoServer) IncrStrangerMsgLimit(ctx context.Context, in *IncrStrangerMsgLimitReq) (*IncrStrangerMsgLimitResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrStrangerMsgLimit", ctx, in)
	ret0, _ := ret[0].(*IncrStrangerMsgLimitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrStrangerMsgLimit indicates an expected call of IncrStrangerMsgLimit.
func (mr *MockImstrangerGoServerMockRecorder) IncrStrangerMsgLimit(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrStrangerMsgLimit", reflect.TypeOf((*MockImstrangerGoServer)(nil).IncrStrangerMsgLimit), ctx, in)
}

// IsContractGuildSuperiorAndSubordinate mocks base method.
func (m *MockImstrangerGoServer) IsContractGuildSuperiorAndSubordinate(ctx context.Context, in *IsContractGuildSuperiorAndSubordinateReq) (*IsContractGuildSuperiorAndSubordinateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsContractGuildSuperiorAndSubordinate", ctx, in)
	ret0, _ := ret[0].(*IsContractGuildSuperiorAndSubordinateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsContractGuildSuperiorAndSubordinate indicates an expected call of IsContractGuildSuperiorAndSubordinate.
func (mr *MockImstrangerGoServerMockRecorder) IsContractGuildSuperiorAndSubordinate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsContractGuildSuperiorAndSubordinate", reflect.TypeOf((*MockImstrangerGoServer)(nil).IsContractGuildSuperiorAndSubordinate), ctx, in)
}

// IsHighConsumeIn24Hour mocks base method.
func (m *MockImstrangerGoServer) IsHighConsumeIn24Hour(ctx context.Context, in *IsHighConsumeIn24HourReq) (*IsHighConsumeIn24HourResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsHighConsumeIn24Hour", ctx, in)
	ret0, _ := ret[0].(*IsHighConsumeIn24HourResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsHighConsumeIn24Hour indicates an expected call of IsHighConsumeIn24Hour.
func (mr *MockImstrangerGoServerMockRecorder) IsHighConsumeIn24Hour(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsHighConsumeIn24Hour", reflect.TypeOf((*MockImstrangerGoServer)(nil).IsHighConsumeIn24Hour), ctx, in)
}

// IsStrangerMsgSender mocks base method.
func (m *MockImstrangerGoServer) IsStrangerMsgSender(ctx context.Context, in *IsStrangerMsgSenderReq) (*IsStrangerMsgSenderResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsStrangerMsgSender", ctx, in)
	ret0, _ := ret[0].(*IsStrangerMsgSenderResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsStrangerMsgSender indicates an expected call of IsStrangerMsgSender.
func (mr *MockImstrangerGoServerMockRecorder) IsStrangerMsgSender(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsStrangerMsgSender", reflect.TypeOf((*MockImstrangerGoServer)(nil).IsStrangerMsgSender), ctx, in)
}

// SetHighConsumeIn24Hour mocks base method.
func (m *MockImstrangerGoServer) SetHighConsumeIn24Hour(ctx context.Context, in *SetHighConsumeIn24HourReq) (*SetHighConsumeIn24HourResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetHighConsumeIn24Hour", ctx, in)
	ret0, _ := ret[0].(*SetHighConsumeIn24HourResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetHighConsumeIn24Hour indicates an expected call of SetHighConsumeIn24Hour.
func (mr *MockImstrangerGoServerMockRecorder) SetHighConsumeIn24Hour(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHighConsumeIn24Hour", reflect.TypeOf((*MockImstrangerGoServer)(nil).SetHighConsumeIn24Hour), ctx, in)
}

// SetIMPresentToTarget mocks base method.
func (m *MockImstrangerGoServer) SetIMPresentToTarget(ctx context.Context, in *SetIMPresentToTargetReq) (*SetIMPresentToTargetResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetIMPresentToTarget", ctx, in)
	ret0, _ := ret[0].(*SetIMPresentToTargetResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetIMPresentToTarget indicates an expected call of SetIMPresentToTarget.
func (mr *MockImstrangerGoServerMockRecorder) SetIMPresentToTarget(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetIMPresentToTarget", reflect.TypeOf((*MockImstrangerGoServer)(nil).SetIMPresentToTarget), ctx, in)
}
