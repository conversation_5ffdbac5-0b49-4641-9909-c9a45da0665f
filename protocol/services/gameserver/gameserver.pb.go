// Code generated by protoc-gen-go. DO NOT EDIT.
// source: gameserver/gameserver.proto

package gameserver // import "golang.52tt.com/protocol/services/gameserver"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GameServerErrorType int32

const (
	GameServerErrorType_GameOk             GameServerErrorType = 0
	GameServerErrorType_GameNotStart       GameServerErrorType = -1
	GameServerErrorType_GamePeopleExceed   GameServerErrorType = -2
	GameServerErrorType_GameNotLogin       GameServerErrorType = -3
	GameServerErrorType_GameNotReportReady GameServerErrorType = -4
	GameServerErrorType_GameTokenErr       GameServerErrorType = -5
	GameServerErrorType_GameCodeErr        GameServerErrorType = -6
	GameServerErrorType_GameSecretErr      GameServerErrorType = -7
	GameServerErrorType_GameReqParamErr    GameServerErrorType = -8
	GameServerErrorType_GameNotConfig      GameServerErrorType = -9
	GameServerErrorType_GameNotGrayRoom    GameServerErrorType = -10
	GameServerErrorType_GetUidGameErr      GameServerErrorType = -11
	GameServerErrorType_GetUidRoomErr      GameServerErrorType = -12
	GameServerErrorType_GameNotExistErr    GameServerErrorType = -13
	GameServerErrorType_GameIdErr          GameServerErrorType = -14
	GameServerErrorType_GameTokenRoomidErr GameServerErrorType = -15
	GameServerErrorType_GameTokenGameidErr GameServerErrorType = -16
	GameServerErrorType_GameCpid2GameidErr GameServerErrorType = -17
)

var GameServerErrorType_name = map[int32]string{
	0:   "GameOk",
	-1:  "GameNotStart",
	-2:  "GamePeopleExceed",
	-3:  "GameNotLogin",
	-4:  "GameNotReportReady",
	-5:  "GameTokenErr",
	-6:  "GameCodeErr",
	-7:  "GameSecretErr",
	-8:  "GameReqParamErr",
	-9:  "GameNotConfig",
	-10: "GameNotGrayRoom",
	-11: "GetUidGameErr",
	-12: "GetUidRoomErr",
	-13: "GameNotExistErr",
	-14: "GameIdErr",
	-15: "GameTokenRoomidErr",
	-16: "GameTokenGameidErr",
	-17: "GameCpid2GameidErr",
}
var GameServerErrorType_value = map[string]int32{
	"GameOk":             0,
	"GameNotStart":       -1,
	"GamePeopleExceed":   -2,
	"GameNotLogin":       -3,
	"GameNotReportReady": -4,
	"GameTokenErr":       -5,
	"GameCodeErr":        -6,
	"GameSecretErr":      -7,
	"GameReqParamErr":    -8,
	"GameNotConfig":      -9,
	"GameNotGrayRoom":    -10,
	"GetUidGameErr":      -11,
	"GetUidRoomErr":      -12,
	"GameNotExistErr":    -13,
	"GameIdErr":          -14,
	"GameTokenRoomidErr": -15,
	"GameTokenGameidErr": -16,
	"GameCpid2GameidErr": -17,
}

func (x GameServerErrorType) String() string {
	return proto.EnumName(GameServerErrorType_name, int32(x))
}
func (GameServerErrorType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{0}
}

type GameResultType int32

const (
	GameResultType_GameWin          GameResultType = 0
	GameResultType_GameTie          GameResultType = 1
	GameResultType_GameRank         GameResultType = 2
	GameResultType_GameWinWithScore GameResultType = 3
	GameResultType_GameTieWithScore GameResultType = 4
	GameResultType_GameOver         GameResultType = 5
)

var GameResultType_name = map[int32]string{
	0: "GameWin",
	1: "GameTie",
	2: "GameRank",
	3: "GameWinWithScore",
	4: "GameTieWithScore",
	5: "GameOver",
}
var GameResultType_value = map[string]int32{
	"GameWin":          0,
	"GameTie":          1,
	"GameRank":         2,
	"GameWinWithScore": 3,
	"GameTieWithScore": 4,
	"GameOver":         5,
}

func (x GameResultType) String() string {
	return proto.EnumName(GameResultType_name, int32(x))
}
func (GameResultType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{1}
}

type GameServerRequestType int32

const (
	GameServerRequestType_GameRequestTypeNone            GameServerRequestType = 0
	GameServerRequestType_GameRequestTypeAdd             GameServerRequestType = 1
	GameServerRequestType_GameRequestTypeModify          GameServerRequestType = 2
	GameServerRequestType_GameRequestTypeVersionRelation GameServerRequestType = 3
)

var GameServerRequestType_name = map[int32]string{
	0: "GameRequestTypeNone",
	1: "GameRequestTypeAdd",
	2: "GameRequestTypeModify",
	3: "GameRequestTypeVersionRelation",
}
var GameServerRequestType_value = map[string]int32{
	"GameRequestTypeNone":            0,
	"GameRequestTypeAdd":             1,
	"GameRequestTypeModify":          2,
	"GameRequestTypeVersionRelation": 3,
}

func (x GameServerRequestType) String() string {
	return proto.EnumName(GameServerRequestType_name, int32(x))
}
func (GameServerRequestType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{2}
}

type UserLeaveType int32

const (
	UserLeaveType_UserSimpleLeave UserLeaveType = 0
	UserLeaveType_UserExpireLeave UserLeaveType = 1
)

var UserLeaveType_name = map[int32]string{
	0: "UserSimpleLeave",
	1: "UserExpireLeave",
}
var UserLeaveType_value = map[string]int32{
	"UserSimpleLeave": 0,
	"UserExpireLeave": 1,
}

func (x UserLeaveType) String() string {
	return proto.EnumName(UserLeaveType_name, int32(x))
}
func (UserLeaveType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{3}
}

type GameStartReq struct {
	Setid                string   `protobuf:"bytes,1,opt,name=setid,proto3" json:"setid,omitempty"`
	Cpid                 uint32   `protobuf:"varint,2,opt,name=cpid,proto3" json:"cpid,omitempty"`
	Gameid               uint32   `protobuf:"varint,3,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Roomid               uint32   `protobuf:"varint,4,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Appid                uint32   `protobuf:"varint,5,opt,name=appid,proto3" json:"appid,omitempty"`
	Openids              []string `protobuf:"bytes,6,rep,name=openids,proto3" json:"openids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameStartReq) Reset()         { *m = GameStartReq{} }
func (m *GameStartReq) String() string { return proto.CompactTextString(m) }
func (*GameStartReq) ProtoMessage()    {}
func (*GameStartReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{0}
}
func (m *GameStartReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameStartReq.Unmarshal(m, b)
}
func (m *GameStartReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameStartReq.Marshal(b, m, deterministic)
}
func (dst *GameStartReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameStartReq.Merge(dst, src)
}
func (m *GameStartReq) XXX_Size() int {
	return xxx_messageInfo_GameStartReq.Size(m)
}
func (m *GameStartReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GameStartReq.DiscardUnknown(m)
}

var xxx_messageInfo_GameStartReq proto.InternalMessageInfo

func (m *GameStartReq) GetSetid() string {
	if m != nil {
		return m.Setid
	}
	return ""
}

func (m *GameStartReq) GetCpid() uint32 {
	if m != nil {
		return m.Cpid
	}
	return 0
}

func (m *GameStartReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *GameStartReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *GameStartReq) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

func (m *GameStartReq) GetOpenids() []string {
	if m != nil {
		return m.Openids
	}
	return nil
}

type GameStartRsp struct {
	ErrorType            GameServerErrorType `protobuf:"varint,1,opt,name=error_type,json=errorType,proto3,enum=gameserver.GameServerErrorType" json:"error_type,omitempty"`
	ErrorMsg             string              `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GameStartRsp) Reset()         { *m = GameStartRsp{} }
func (m *GameStartRsp) String() string { return proto.CompactTextString(m) }
func (*GameStartRsp) ProtoMessage()    {}
func (*GameStartRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{1}
}
func (m *GameStartRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameStartRsp.Unmarshal(m, b)
}
func (m *GameStartRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameStartRsp.Marshal(b, m, deterministic)
}
func (dst *GameStartRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameStartRsp.Merge(dst, src)
}
func (m *GameStartRsp) XXX_Size() int {
	return xxx_messageInfo_GameStartRsp.Size(m)
}
func (m *GameStartRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GameStartRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GameStartRsp proto.InternalMessageInfo

func (m *GameStartRsp) GetErrorType() GameServerErrorType {
	if m != nil {
		return m.ErrorType
	}
	return GameServerErrorType_GameOk
}

func (m *GameStartRsp) GetErrorMsg() string {
	if m != nil {
		return m.ErrorMsg
	}
	return ""
}

type UserInfo struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  string   `protobuf:"bytes,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Headurl              string   `protobuf:"bytes,4,opt,name=headurl,proto3" json:"headurl,omitempty"`
	Uid                  uint32   `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfo) Reset()         { *m = UserInfo{} }
func (m *UserInfo) String() string { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()    {}
func (*UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{2}
}
func (m *UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo.Unmarshal(m, b)
}
func (m *UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo.Marshal(b, m, deterministic)
}
func (dst *UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo.Merge(dst, src)
}
func (m *UserInfo) XXX_Size() int {
	return xxx_messageInfo_UserInfo.Size(m)
}
func (m *UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo proto.InternalMessageInfo

func (m *UserInfo) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *UserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserInfo) GetSex() string {
	if m != nil {
		return m.Sex
	}
	return ""
}

func (m *UserInfo) GetHeadurl() string {
	if m != nil {
		return m.Headurl
	}
	return ""
}

func (m *UserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GameStartV2Req struct {
	Setid                string      `protobuf:"bytes,1,opt,name=setid,proto3" json:"setid,omitempty"`
	Cpid                 uint32      `protobuf:"varint,2,opt,name=cpid,proto3" json:"cpid,omitempty"`
	Gameid               uint32      `protobuf:"varint,3,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Roomid               uint32      `protobuf:"varint,4,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Appid                uint32      `protobuf:"varint,5,opt,name=appid,proto3" json:"appid,omitempty"`
	UserInfo             []*UserInfo `protobuf:"bytes,6,rep,name=userInfo,proto3" json:"userInfo,omitempty"`
	Mode                 string      `protobuf:"bytes,7,opt,name=mode,proto3" json:"mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GameStartV2Req) Reset()         { *m = GameStartV2Req{} }
func (m *GameStartV2Req) String() string { return proto.CompactTextString(m) }
func (*GameStartV2Req) ProtoMessage()    {}
func (*GameStartV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{3}
}
func (m *GameStartV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameStartV2Req.Unmarshal(m, b)
}
func (m *GameStartV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameStartV2Req.Marshal(b, m, deterministic)
}
func (dst *GameStartV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameStartV2Req.Merge(dst, src)
}
func (m *GameStartV2Req) XXX_Size() int {
	return xxx_messageInfo_GameStartV2Req.Size(m)
}
func (m *GameStartV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GameStartV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GameStartV2Req proto.InternalMessageInfo

func (m *GameStartV2Req) GetSetid() string {
	if m != nil {
		return m.Setid
	}
	return ""
}

func (m *GameStartV2Req) GetCpid() uint32 {
	if m != nil {
		return m.Cpid
	}
	return 0
}

func (m *GameStartV2Req) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *GameStartV2Req) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *GameStartV2Req) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

func (m *GameStartV2Req) GetUserInfo() []*UserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *GameStartV2Req) GetMode() string {
	if m != nil {
		return m.Mode
	}
	return ""
}

type GameStartV2Rsp struct {
	ErrorType            GameServerErrorType `protobuf:"varint,1,opt,name=error_type,json=errorType,proto3,enum=gameserver.GameServerErrorType" json:"error_type,omitempty"`
	ErrorMsg             string              `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GameStartV2Rsp) Reset()         { *m = GameStartV2Rsp{} }
func (m *GameStartV2Rsp) String() string { return proto.CompactTextString(m) }
func (*GameStartV2Rsp) ProtoMessage()    {}
func (*GameStartV2Rsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{4}
}
func (m *GameStartV2Rsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameStartV2Rsp.Unmarshal(m, b)
}
func (m *GameStartV2Rsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameStartV2Rsp.Marshal(b, m, deterministic)
}
func (dst *GameStartV2Rsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameStartV2Rsp.Merge(dst, src)
}
func (m *GameStartV2Rsp) XXX_Size() int {
	return xxx_messageInfo_GameStartV2Rsp.Size(m)
}
func (m *GameStartV2Rsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GameStartV2Rsp.DiscardUnknown(m)
}

var xxx_messageInfo_GameStartV2Rsp proto.InternalMessageInfo

func (m *GameStartV2Rsp) GetErrorType() GameServerErrorType {
	if m != nil {
		return m.ErrorType
	}
	return GameServerErrorType_GameOk
}

func (m *GameStartV2Rsp) GetErrorMsg() string {
	if m != nil {
		return m.ErrorMsg
	}
	return ""
}

type GameResultT struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	Score                uint32   `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	Iswin                bool     `protobuf:"varint,3,opt,name=iswin,proto3" json:"iswin,omitempty"`
	TeamId               uint32   `protobuf:"varint,4,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	TeamDesc             string   `protobuf:"bytes,5,opt,name=team_desc,json=teamDesc,proto3" json:"team_desc,omitempty"`
	UserDesc             string   `protobuf:"bytes,6,opt,name=user_desc,json=userDesc,proto3" json:"user_desc,omitempty"`
	State                uint32   `protobuf:"varint,7,opt,name=state,proto3" json:"state,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameResultT) Reset()         { *m = GameResultT{} }
func (m *GameResultT) String() string { return proto.CompactTextString(m) }
func (*GameResultT) ProtoMessage()    {}
func (*GameResultT) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{5}
}
func (m *GameResultT) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameResultT.Unmarshal(m, b)
}
func (m *GameResultT) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameResultT.Marshal(b, m, deterministic)
}
func (dst *GameResultT) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameResultT.Merge(dst, src)
}
func (m *GameResultT) XXX_Size() int {
	return xxx_messageInfo_GameResultT.Size(m)
}
func (m *GameResultT) XXX_DiscardUnknown() {
	xxx_messageInfo_GameResultT.DiscardUnknown(m)
}

var xxx_messageInfo_GameResultT proto.InternalMessageInfo

func (m *GameResultT) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *GameResultT) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *GameResultT) GetIswin() bool {
	if m != nil {
		return m.Iswin
	}
	return false
}

func (m *GameResultT) GetTeamId() uint32 {
	if m != nil {
		return m.TeamId
	}
	return 0
}

func (m *GameResultT) GetTeamDesc() string {
	if m != nil {
		return m.TeamDesc
	}
	return ""
}

func (m *GameResultT) GetUserDesc() string {
	if m != nil {
		return m.UserDesc
	}
	return ""
}

func (m *GameResultT) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

type GameEndReq struct {
	Setid                string         `protobuf:"bytes,1,opt,name=setid,proto3" json:"setid,omitempty"`
	Cpid                 uint32         `protobuf:"varint,2,opt,name=cpid,proto3" json:"cpid,omitempty"`
	Gameid               uint32         `protobuf:"varint,3,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Roomid               uint32         `protobuf:"varint,4,opt,name=roomid,proto3" json:"roomid,omitempty"`
	GameResult           []*GameResultT `protobuf:"bytes,5,rep,name=game_result,json=gameResult,proto3" json:"game_result,omitempty"`
	ResultType           GameResultType `protobuf:"varint,6,opt,name=resultType,proto3,enum=gameserver.GameResultType" json:"resultType,omitempty"`
	Unit                 string         `protobuf:"bytes,7,opt,name=unit,proto3" json:"unit,omitempty"`
	State                uint32         `protobuf:"varint,8,opt,name=state,proto3" json:"state,omitempty"`
	ResultDesc           string         `protobuf:"bytes,9,opt,name=result_desc,json=resultDesc,proto3" json:"result_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GameEndReq) Reset()         { *m = GameEndReq{} }
func (m *GameEndReq) String() string { return proto.CompactTextString(m) }
func (*GameEndReq) ProtoMessage()    {}
func (*GameEndReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{6}
}
func (m *GameEndReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameEndReq.Unmarshal(m, b)
}
func (m *GameEndReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameEndReq.Marshal(b, m, deterministic)
}
func (dst *GameEndReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameEndReq.Merge(dst, src)
}
func (m *GameEndReq) XXX_Size() int {
	return xxx_messageInfo_GameEndReq.Size(m)
}
func (m *GameEndReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GameEndReq.DiscardUnknown(m)
}

var xxx_messageInfo_GameEndReq proto.InternalMessageInfo

func (m *GameEndReq) GetSetid() string {
	if m != nil {
		return m.Setid
	}
	return ""
}

func (m *GameEndReq) GetCpid() uint32 {
	if m != nil {
		return m.Cpid
	}
	return 0
}

func (m *GameEndReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *GameEndReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *GameEndReq) GetGameResult() []*GameResultT {
	if m != nil {
		return m.GameResult
	}
	return nil
}

func (m *GameEndReq) GetResultType() GameResultType {
	if m != nil {
		return m.ResultType
	}
	return GameResultType_GameWin
}

func (m *GameEndReq) GetUnit() string {
	if m != nil {
		return m.Unit
	}
	return ""
}

func (m *GameEndReq) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *GameEndReq) GetResultDesc() string {
	if m != nil {
		return m.ResultDesc
	}
	return ""
}

type GameEndRsp struct {
	ErrorType            GameServerErrorType `protobuf:"varint,1,opt,name=error_type,json=errorType,proto3,enum=gameserver.GameServerErrorType" json:"error_type,omitempty"`
	ErrorMsg             string              `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GameEndRsp) Reset()         { *m = GameEndRsp{} }
func (m *GameEndRsp) String() string { return proto.CompactTextString(m) }
func (*GameEndRsp) ProtoMessage()    {}
func (*GameEndRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{7}
}
func (m *GameEndRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameEndRsp.Unmarshal(m, b)
}
func (m *GameEndRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameEndRsp.Marshal(b, m, deterministic)
}
func (dst *GameEndRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameEndRsp.Merge(dst, src)
}
func (m *GameEndRsp) XXX_Size() int {
	return xxx_messageInfo_GameEndRsp.Size(m)
}
func (m *GameEndRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GameEndRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GameEndRsp proto.InternalMessageInfo

func (m *GameEndRsp) GetErrorType() GameServerErrorType {
	if m != nil {
		return m.ErrorType
	}
	return GameServerErrorType_GameOk
}

func (m *GameEndRsp) GetErrorMsg() string {
	if m != nil {
		return m.ErrorMsg
	}
	return ""
}

type GameEndV2Req struct {
	Setid                string         `protobuf:"bytes,1,opt,name=setid,proto3" json:"setid,omitempty"`
	Cpid                 uint32         `protobuf:"varint,2,opt,name=cpid,proto3" json:"cpid,omitempty"`
	Gameid               uint32         `protobuf:"varint,3,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Roomid               uint32         `protobuf:"varint,4,opt,name=roomid,proto3" json:"roomid,omitempty"`
	GameResult           []*GameResultT `protobuf:"bytes,5,rep,name=game_result,json=gameResult,proto3" json:"game_result,omitempty"`
	ResultType           GameResultType `protobuf:"varint,6,opt,name=resultType,proto3,enum=gameserver.GameResultType" json:"resultType,omitempty"`
	Unit                 string         `protobuf:"bytes,7,opt,name=unit,proto3" json:"unit,omitempty"`
	State                uint32         `protobuf:"varint,8,opt,name=state,proto3" json:"state,omitempty"`
	Secret               string         `protobuf:"bytes,9,opt,name=secret,proto3" json:"secret,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GameEndV2Req) Reset()         { *m = GameEndV2Req{} }
func (m *GameEndV2Req) String() string { return proto.CompactTextString(m) }
func (*GameEndV2Req) ProtoMessage()    {}
func (*GameEndV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{8}
}
func (m *GameEndV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameEndV2Req.Unmarshal(m, b)
}
func (m *GameEndV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameEndV2Req.Marshal(b, m, deterministic)
}
func (dst *GameEndV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameEndV2Req.Merge(dst, src)
}
func (m *GameEndV2Req) XXX_Size() int {
	return xxx_messageInfo_GameEndV2Req.Size(m)
}
func (m *GameEndV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GameEndV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GameEndV2Req proto.InternalMessageInfo

func (m *GameEndV2Req) GetSetid() string {
	if m != nil {
		return m.Setid
	}
	return ""
}

func (m *GameEndV2Req) GetCpid() uint32 {
	if m != nil {
		return m.Cpid
	}
	return 0
}

func (m *GameEndV2Req) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *GameEndV2Req) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *GameEndV2Req) GetGameResult() []*GameResultT {
	if m != nil {
		return m.GameResult
	}
	return nil
}

func (m *GameEndV2Req) GetResultType() GameResultType {
	if m != nil {
		return m.ResultType
	}
	return GameResultType_GameWin
}

func (m *GameEndV2Req) GetUnit() string {
	if m != nil {
		return m.Unit
	}
	return ""
}

func (m *GameEndV2Req) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *GameEndV2Req) GetSecret() string {
	if m != nil {
		return m.Secret
	}
	return ""
}

type GameEndV2Rsp struct {
	ErrorType            GameServerErrorType `protobuf:"varint,1,opt,name=error_type,json=errorType,proto3,enum=gameserver.GameServerErrorType" json:"error_type,omitempty"`
	ErrorMsg             string              `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GameEndV2Rsp) Reset()         { *m = GameEndV2Rsp{} }
func (m *GameEndV2Rsp) String() string { return proto.CompactTextString(m) }
func (*GameEndV2Rsp) ProtoMessage()    {}
func (*GameEndV2Rsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{9}
}
func (m *GameEndV2Rsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameEndV2Rsp.Unmarshal(m, b)
}
func (m *GameEndV2Rsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameEndV2Rsp.Marshal(b, m, deterministic)
}
func (dst *GameEndV2Rsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameEndV2Rsp.Merge(dst, src)
}
func (m *GameEndV2Rsp) XXX_Size() int {
	return xxx_messageInfo_GameEndV2Rsp.Size(m)
}
func (m *GameEndV2Rsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GameEndV2Rsp.DiscardUnknown(m)
}

var xxx_messageInfo_GameEndV2Rsp proto.InternalMessageInfo

func (m *GameEndV2Rsp) GetErrorType() GameServerErrorType {
	if m != nil {
		return m.ErrorType
	}
	return GameServerErrorType_GameOk
}

func (m *GameEndV2Rsp) GetErrorMsg() string {
	if m != nil {
		return m.ErrorMsg
	}
	return ""
}

type GameEndExceptionReq struct {
	Setid                string   `protobuf:"bytes,1,opt,name=setid,proto3" json:"setid,omitempty"`
	Cpid                 uint32   `protobuf:"varint,2,opt,name=cpid,proto3" json:"cpid,omitempty"`
	Gameid               uint32   `protobuf:"varint,3,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Roomid               uint32   `protobuf:"varint,4,opt,name=roomid,proto3" json:"roomid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameEndExceptionReq) Reset()         { *m = GameEndExceptionReq{} }
func (m *GameEndExceptionReq) String() string { return proto.CompactTextString(m) }
func (*GameEndExceptionReq) ProtoMessage()    {}
func (*GameEndExceptionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{10}
}
func (m *GameEndExceptionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameEndExceptionReq.Unmarshal(m, b)
}
func (m *GameEndExceptionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameEndExceptionReq.Marshal(b, m, deterministic)
}
func (dst *GameEndExceptionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameEndExceptionReq.Merge(dst, src)
}
func (m *GameEndExceptionReq) XXX_Size() int {
	return xxx_messageInfo_GameEndExceptionReq.Size(m)
}
func (m *GameEndExceptionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GameEndExceptionReq.DiscardUnknown(m)
}

var xxx_messageInfo_GameEndExceptionReq proto.InternalMessageInfo

func (m *GameEndExceptionReq) GetSetid() string {
	if m != nil {
		return m.Setid
	}
	return ""
}

func (m *GameEndExceptionReq) GetCpid() uint32 {
	if m != nil {
		return m.Cpid
	}
	return 0
}

func (m *GameEndExceptionReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *GameEndExceptionReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

type GameEndExceptionRsp struct {
	ErrorType            GameServerErrorType `protobuf:"varint,1,opt,name=error_type,json=errorType,proto3,enum=gameserver.GameServerErrorType" json:"error_type,omitempty"`
	ErrorMsg             string              `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GameEndExceptionRsp) Reset()         { *m = GameEndExceptionRsp{} }
func (m *GameEndExceptionRsp) String() string { return proto.CompactTextString(m) }
func (*GameEndExceptionRsp) ProtoMessage()    {}
func (*GameEndExceptionRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{11}
}
func (m *GameEndExceptionRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameEndExceptionRsp.Unmarshal(m, b)
}
func (m *GameEndExceptionRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameEndExceptionRsp.Marshal(b, m, deterministic)
}
func (dst *GameEndExceptionRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameEndExceptionRsp.Merge(dst, src)
}
func (m *GameEndExceptionRsp) XXX_Size() int {
	return xxx_messageInfo_GameEndExceptionRsp.Size(m)
}
func (m *GameEndExceptionRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GameEndExceptionRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GameEndExceptionRsp proto.InternalMessageInfo

func (m *GameEndExceptionRsp) GetErrorType() GameServerErrorType {
	if m != nil {
		return m.ErrorType
	}
	return GameServerErrorType_GameOk
}

func (m *GameEndExceptionRsp) GetErrorMsg() string {
	if m != nil {
		return m.ErrorMsg
	}
	return ""
}

type StartRoomGameReq struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	Gameid               uint32   `protobuf:"varint,2,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Roomid               uint32   `protobuf:"varint,3,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Appid                uint32   `protobuf:"varint,4,opt,name=appid,proto3" json:"appid,omitempty"`
	InitialUid           uint32   `protobuf:"varint,5,opt,name=initial_uid,json=initialUid,proto3" json:"initial_uid,omitempty"`
	CurrentVerion        string   `protobuf:"bytes,6,opt,name=current_verion,json=currentVerion,proto3" json:"current_verion,omitempty"`
	Bussid               uint32   `protobuf:"varint,7,opt,name=bussid,proto3" json:"bussid,omitempty"`
	GamePkModel          string   `protobuf:"bytes,8,opt,name=game_pk_model,json=gamePkModel,proto3" json:"game_pk_model,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartRoomGameReq) Reset()         { *m = StartRoomGameReq{} }
func (m *StartRoomGameReq) String() string { return proto.CompactTextString(m) }
func (*StartRoomGameReq) ProtoMessage()    {}
func (*StartRoomGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{12}
}
func (m *StartRoomGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartRoomGameReq.Unmarshal(m, b)
}
func (m *StartRoomGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartRoomGameReq.Marshal(b, m, deterministic)
}
func (dst *StartRoomGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartRoomGameReq.Merge(dst, src)
}
func (m *StartRoomGameReq) XXX_Size() int {
	return xxx_messageInfo_StartRoomGameReq.Size(m)
}
func (m *StartRoomGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartRoomGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartRoomGameReq proto.InternalMessageInfo

func (m *StartRoomGameReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *StartRoomGameReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *StartRoomGameReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *StartRoomGameReq) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

func (m *StartRoomGameReq) GetInitialUid() uint32 {
	if m != nil {
		return m.InitialUid
	}
	return 0
}

func (m *StartRoomGameReq) GetCurrentVerion() string {
	if m != nil {
		return m.CurrentVerion
	}
	return ""
}

func (m *StartRoomGameReq) GetBussid() uint32 {
	if m != nil {
		return m.Bussid
	}
	return 0
}

func (m *StartRoomGameReq) GetGamePkModel() string {
	if m != nil {
		return m.GamePkModel
	}
	return ""
}

type StartRoomGameRsp struct {
	ErrorType            GameServerErrorType `protobuf:"varint,1,opt,name=error_type,json=errorType,proto3,enum=gameserver.GameServerErrorType" json:"error_type,omitempty"`
	ErrorMsg             string              `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *StartRoomGameRsp) Reset()         { *m = StartRoomGameRsp{} }
func (m *StartRoomGameRsp) String() string { return proto.CompactTextString(m) }
func (*StartRoomGameRsp) ProtoMessage()    {}
func (*StartRoomGameRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{13}
}
func (m *StartRoomGameRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartRoomGameRsp.Unmarshal(m, b)
}
func (m *StartRoomGameRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartRoomGameRsp.Marshal(b, m, deterministic)
}
func (dst *StartRoomGameRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartRoomGameRsp.Merge(dst, src)
}
func (m *StartRoomGameRsp) XXX_Size() int {
	return xxx_messageInfo_StartRoomGameRsp.Size(m)
}
func (m *StartRoomGameRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartRoomGameRsp.DiscardUnknown(m)
}

var xxx_messageInfo_StartRoomGameRsp proto.InternalMessageInfo

func (m *StartRoomGameRsp) GetErrorType() GameServerErrorType {
	if m != nil {
		return m.ErrorType
	}
	return GameServerErrorType_GameOk
}

func (m *StartRoomGameRsp) GetErrorMsg() string {
	if m != nil {
		return m.ErrorMsg
	}
	return ""
}

type EndRoomGameReq struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	Gameid               uint32   `protobuf:"varint,2,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Roomid               uint32   `protobuf:"varint,3,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Appid                uint32   `protobuf:"varint,4,opt,name=appid,proto3" json:"appid,omitempty"`
	Bussid               uint32   `protobuf:"varint,5,opt,name=bussid,proto3" json:"bussid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EndRoomGameReq) Reset()         { *m = EndRoomGameReq{} }
func (m *EndRoomGameReq) String() string { return proto.CompactTextString(m) }
func (*EndRoomGameReq) ProtoMessage()    {}
func (*EndRoomGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{14}
}
func (m *EndRoomGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EndRoomGameReq.Unmarshal(m, b)
}
func (m *EndRoomGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EndRoomGameReq.Marshal(b, m, deterministic)
}
func (dst *EndRoomGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EndRoomGameReq.Merge(dst, src)
}
func (m *EndRoomGameReq) XXX_Size() int {
	return xxx_messageInfo_EndRoomGameReq.Size(m)
}
func (m *EndRoomGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EndRoomGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_EndRoomGameReq proto.InternalMessageInfo

func (m *EndRoomGameReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *EndRoomGameReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *EndRoomGameReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *EndRoomGameReq) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

func (m *EndRoomGameReq) GetBussid() uint32 {
	if m != nil {
		return m.Bussid
	}
	return 0
}

type EndRoomGameRsp struct {
	ErrorType            GameServerErrorType `protobuf:"varint,1,opt,name=error_type,json=errorType,proto3,enum=gameserver.GameServerErrorType" json:"error_type,omitempty"`
	ErrorMsg             string              `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *EndRoomGameRsp) Reset()         { *m = EndRoomGameRsp{} }
func (m *EndRoomGameRsp) String() string { return proto.CompactTextString(m) }
func (*EndRoomGameRsp) ProtoMessage()    {}
func (*EndRoomGameRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{15}
}
func (m *EndRoomGameRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EndRoomGameRsp.Unmarshal(m, b)
}
func (m *EndRoomGameRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EndRoomGameRsp.Marshal(b, m, deterministic)
}
func (dst *EndRoomGameRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EndRoomGameRsp.Merge(dst, src)
}
func (m *EndRoomGameRsp) XXX_Size() int {
	return xxx_messageInfo_EndRoomGameRsp.Size(m)
}
func (m *EndRoomGameRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_EndRoomGameRsp.DiscardUnknown(m)
}

var xxx_messageInfo_EndRoomGameRsp proto.InternalMessageInfo

func (m *EndRoomGameRsp) GetErrorType() GameServerErrorType {
	if m != nil {
		return m.ErrorType
	}
	return GameServerErrorType_GameOk
}

func (m *EndRoomGameRsp) GetErrorMsg() string {
	if m != nil {
		return m.ErrorMsg
	}
	return ""
}

type LoginGameReq struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	Gameid               uint32   `protobuf:"varint,2,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Roomid               uint32   `protobuf:"varint,3,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Appid                uint32   `protobuf:"varint,4,opt,name=appid,proto3" json:"appid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LoginGameReq) Reset()         { *m = LoginGameReq{} }
func (m *LoginGameReq) String() string { return proto.CompactTextString(m) }
func (*LoginGameReq) ProtoMessage()    {}
func (*LoginGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{16}
}
func (m *LoginGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LoginGameReq.Unmarshal(m, b)
}
func (m *LoginGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LoginGameReq.Marshal(b, m, deterministic)
}
func (dst *LoginGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LoginGameReq.Merge(dst, src)
}
func (m *LoginGameReq) XXX_Size() int {
	return xxx_messageInfo_LoginGameReq.Size(m)
}
func (m *LoginGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LoginGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_LoginGameReq proto.InternalMessageInfo

func (m *LoginGameReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *LoginGameReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *LoginGameReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *LoginGameReq) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

type LoginGameRsp struct {
	Code                 int32               `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string              `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	LoginCnt             uint32              `protobuf:"varint,3,opt,name=login_cnt,json=loginCnt,proto3" json:"login_cnt,omitempty"`
	JoinCnt              uint32              `protobuf:"varint,4,opt,name=join_cnt,json=joinCnt,proto3" json:"join_cnt,omitempty"`
	JoinMaxCnt           uint32              `protobuf:"varint,5,opt,name=join_max_cnt,json=joinMaxCnt,proto3" json:"join_max_cnt,omitempty"`
	ErrorType            GameServerErrorType `protobuf:"varint,6,opt,name=error_type,json=errorType,proto3,enum=gameserver.GameServerErrorType" json:"error_type,omitempty"`
	ErrorMsg             string              `protobuf:"bytes,7,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *LoginGameRsp) Reset()         { *m = LoginGameRsp{} }
func (m *LoginGameRsp) String() string { return proto.CompactTextString(m) }
func (*LoginGameRsp) ProtoMessage()    {}
func (*LoginGameRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{17}
}
func (m *LoginGameRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LoginGameRsp.Unmarshal(m, b)
}
func (m *LoginGameRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LoginGameRsp.Marshal(b, m, deterministic)
}
func (dst *LoginGameRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LoginGameRsp.Merge(dst, src)
}
func (m *LoginGameRsp) XXX_Size() int {
	return xxx_messageInfo_LoginGameRsp.Size(m)
}
func (m *LoginGameRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_LoginGameRsp.DiscardUnknown(m)
}

var xxx_messageInfo_LoginGameRsp proto.InternalMessageInfo

func (m *LoginGameRsp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *LoginGameRsp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *LoginGameRsp) GetLoginCnt() uint32 {
	if m != nil {
		return m.LoginCnt
	}
	return 0
}

func (m *LoginGameRsp) GetJoinCnt() uint32 {
	if m != nil {
		return m.JoinCnt
	}
	return 0
}

func (m *LoginGameRsp) GetJoinMaxCnt() uint32 {
	if m != nil {
		return m.JoinMaxCnt
	}
	return 0
}

func (m *LoginGameRsp) GetErrorType() GameServerErrorType {
	if m != nil {
		return m.ErrorType
	}
	return GameServerErrorType_GameOk
}

func (m *LoginGameRsp) GetErrorMsg() string {
	if m != nil {
		return m.ErrorMsg
	}
	return ""
}

type LogoutGameReq struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	Gameid               uint32   `protobuf:"varint,2,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Roomid               uint32   `protobuf:"varint,3,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Appid                uint32   `protobuf:"varint,4,opt,name=appid,proto3" json:"appid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LogoutGameReq) Reset()         { *m = LogoutGameReq{} }
func (m *LogoutGameReq) String() string { return proto.CompactTextString(m) }
func (*LogoutGameReq) ProtoMessage()    {}
func (*LogoutGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{18}
}
func (m *LogoutGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LogoutGameReq.Unmarshal(m, b)
}
func (m *LogoutGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LogoutGameReq.Marshal(b, m, deterministic)
}
func (dst *LogoutGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LogoutGameReq.Merge(dst, src)
}
func (m *LogoutGameReq) XXX_Size() int {
	return xxx_messageInfo_LogoutGameReq.Size(m)
}
func (m *LogoutGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LogoutGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_LogoutGameReq proto.InternalMessageInfo

func (m *LogoutGameReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *LogoutGameReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *LogoutGameReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *LogoutGameReq) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

type LogoutGameRsp struct {
	ErrorType            GameServerErrorType `protobuf:"varint,1,opt,name=error_type,json=errorType,proto3,enum=gameserver.GameServerErrorType" json:"error_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *LogoutGameRsp) Reset()         { *m = LogoutGameRsp{} }
func (m *LogoutGameRsp) String() string { return proto.CompactTextString(m) }
func (*LogoutGameRsp) ProtoMessage()    {}
func (*LogoutGameRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{19}
}
func (m *LogoutGameRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LogoutGameRsp.Unmarshal(m, b)
}
func (m *LogoutGameRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LogoutGameRsp.Marshal(b, m, deterministic)
}
func (dst *LogoutGameRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LogoutGameRsp.Merge(dst, src)
}
func (m *LogoutGameRsp) XXX_Size() int {
	return xxx_messageInfo_LogoutGameRsp.Size(m)
}
func (m *LogoutGameRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_LogoutGameRsp.DiscardUnknown(m)
}

var xxx_messageInfo_LogoutGameRsp proto.InternalMessageInfo

func (m *LogoutGameRsp) GetErrorType() GameServerErrorType {
	if m != nil {
		return m.ErrorType
	}
	return GameServerErrorType_GameOk
}

type JoinGameReq struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	Gameid               uint32   `protobuf:"varint,2,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Roomid               uint32   `protobuf:"varint,3,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Appid                uint32   `protobuf:"varint,4,opt,name=appid,proto3" json:"appid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinGameReq) Reset()         { *m = JoinGameReq{} }
func (m *JoinGameReq) String() string { return proto.CompactTextString(m) }
func (*JoinGameReq) ProtoMessage()    {}
func (*JoinGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{20}
}
func (m *JoinGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinGameReq.Unmarshal(m, b)
}
func (m *JoinGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinGameReq.Marshal(b, m, deterministic)
}
func (dst *JoinGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinGameReq.Merge(dst, src)
}
func (m *JoinGameReq) XXX_Size() int {
	return xxx_messageInfo_JoinGameReq.Size(m)
}
func (m *JoinGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinGameReq proto.InternalMessageInfo

func (m *JoinGameReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *JoinGameReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *JoinGameReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *JoinGameReq) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

type JoinGameRsp struct {
	ErrorType            GameServerErrorType `protobuf:"varint,1,opt,name=error_type,json=errorType,proto3,enum=gameserver.GameServerErrorType" json:"error_type,omitempty"`
	ErrorMsg             string              `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *JoinGameRsp) Reset()         { *m = JoinGameRsp{} }
func (m *JoinGameRsp) String() string { return proto.CompactTextString(m) }
func (*JoinGameRsp) ProtoMessage()    {}
func (*JoinGameRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{21}
}
func (m *JoinGameRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinGameRsp.Unmarshal(m, b)
}
func (m *JoinGameRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinGameRsp.Marshal(b, m, deterministic)
}
func (dst *JoinGameRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinGameRsp.Merge(dst, src)
}
func (m *JoinGameRsp) XXX_Size() int {
	return xxx_messageInfo_JoinGameRsp.Size(m)
}
func (m *JoinGameRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinGameRsp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinGameRsp proto.InternalMessageInfo

func (m *JoinGameRsp) GetErrorType() GameServerErrorType {
	if m != nil {
		return m.ErrorType
	}
	return GameServerErrorType_GameOk
}

func (m *JoinGameRsp) GetErrorMsg() string {
	if m != nil {
		return m.ErrorMsg
	}
	return ""
}

type JoinOutGameReq struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	Gameid               uint32   `protobuf:"varint,2,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Roomid               uint32   `protobuf:"varint,3,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Appid                uint32   `protobuf:"varint,4,opt,name=appid,proto3" json:"appid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinOutGameReq) Reset()         { *m = JoinOutGameReq{} }
func (m *JoinOutGameReq) String() string { return proto.CompactTextString(m) }
func (*JoinOutGameReq) ProtoMessage()    {}
func (*JoinOutGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{22}
}
func (m *JoinOutGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinOutGameReq.Unmarshal(m, b)
}
func (m *JoinOutGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinOutGameReq.Marshal(b, m, deterministic)
}
func (dst *JoinOutGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinOutGameReq.Merge(dst, src)
}
func (m *JoinOutGameReq) XXX_Size() int {
	return xxx_messageInfo_JoinOutGameReq.Size(m)
}
func (m *JoinOutGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinOutGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinOutGameReq proto.InternalMessageInfo

func (m *JoinOutGameReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *JoinOutGameReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *JoinOutGameReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *JoinOutGameReq) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

type JoinOutGameRsp struct {
	ErrorType            GameServerErrorType `protobuf:"varint,1,opt,name=error_type,json=errorType,proto3,enum=gameserver.GameServerErrorType" json:"error_type,omitempty"`
	ErrorMsg             string              `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *JoinOutGameRsp) Reset()         { *m = JoinOutGameRsp{} }
func (m *JoinOutGameRsp) String() string { return proto.CompactTextString(m) }
func (*JoinOutGameRsp) ProtoMessage()    {}
func (*JoinOutGameRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{23}
}
func (m *JoinOutGameRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinOutGameRsp.Unmarshal(m, b)
}
func (m *JoinOutGameRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinOutGameRsp.Marshal(b, m, deterministic)
}
func (dst *JoinOutGameRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinOutGameRsp.Merge(dst, src)
}
func (m *JoinOutGameRsp) XXX_Size() int {
	return xxx_messageInfo_JoinOutGameRsp.Size(m)
}
func (m *JoinOutGameRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinOutGameRsp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinOutGameRsp proto.InternalMessageInfo

func (m *JoinOutGameRsp) GetErrorType() GameServerErrorType {
	if m != nil {
		return m.ErrorType
	}
	return GameServerErrorType_GameOk
}

func (m *JoinOutGameRsp) GetErrorMsg() string {
	if m != nil {
		return m.ErrorMsg
	}
	return ""
}

type ReadyGameReq struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	Gameid               uint32   `protobuf:"varint,2,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Roomid               uint32   `protobuf:"varint,3,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Appid                uint32   `protobuf:"varint,4,opt,name=appid,proto3" json:"appid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReadyGameReq) Reset()         { *m = ReadyGameReq{} }
func (m *ReadyGameReq) String() string { return proto.CompactTextString(m) }
func (*ReadyGameReq) ProtoMessage()    {}
func (*ReadyGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{24}
}
func (m *ReadyGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadyGameReq.Unmarshal(m, b)
}
func (m *ReadyGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadyGameReq.Marshal(b, m, deterministic)
}
func (dst *ReadyGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadyGameReq.Merge(dst, src)
}
func (m *ReadyGameReq) XXX_Size() int {
	return xxx_messageInfo_ReadyGameReq.Size(m)
}
func (m *ReadyGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadyGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReadyGameReq proto.InternalMessageInfo

func (m *ReadyGameReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *ReadyGameReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *ReadyGameReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *ReadyGameReq) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

type ReadyGameRsp struct {
	ErrorType            GameServerErrorType `protobuf:"varint,1,opt,name=error_type,json=errorType,proto3,enum=gameserver.GameServerErrorType" json:"error_type,omitempty"`
	ErrorMsg             string              `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ReadyGameRsp) Reset()         { *m = ReadyGameRsp{} }
func (m *ReadyGameRsp) String() string { return proto.CompactTextString(m) }
func (*ReadyGameRsp) ProtoMessage()    {}
func (*ReadyGameRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{25}
}
func (m *ReadyGameRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadyGameRsp.Unmarshal(m, b)
}
func (m *ReadyGameRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadyGameRsp.Marshal(b, m, deterministic)
}
func (dst *ReadyGameRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadyGameRsp.Merge(dst, src)
}
func (m *ReadyGameRsp) XXX_Size() int {
	return xxx_messageInfo_ReadyGameRsp.Size(m)
}
func (m *ReadyGameRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadyGameRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ReadyGameRsp proto.InternalMessageInfo

func (m *ReadyGameRsp) GetErrorType() GameServerErrorType {
	if m != nil {
		return m.ErrorType
	}
	return GameServerErrorType_GameOk
}

func (m *ReadyGameRsp) GetErrorMsg() string {
	if m != nil {
		return m.ErrorMsg
	}
	return ""
}

type ReadyOutGameReq struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	Gameid               uint32   `protobuf:"varint,2,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Roomid               uint32   `protobuf:"varint,3,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Appid                uint32   `protobuf:"varint,4,opt,name=appid,proto3" json:"appid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReadyOutGameReq) Reset()         { *m = ReadyOutGameReq{} }
func (m *ReadyOutGameReq) String() string { return proto.CompactTextString(m) }
func (*ReadyOutGameReq) ProtoMessage()    {}
func (*ReadyOutGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{26}
}
func (m *ReadyOutGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadyOutGameReq.Unmarshal(m, b)
}
func (m *ReadyOutGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadyOutGameReq.Marshal(b, m, deterministic)
}
func (dst *ReadyOutGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadyOutGameReq.Merge(dst, src)
}
func (m *ReadyOutGameReq) XXX_Size() int {
	return xxx_messageInfo_ReadyOutGameReq.Size(m)
}
func (m *ReadyOutGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadyOutGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReadyOutGameReq proto.InternalMessageInfo

func (m *ReadyOutGameReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *ReadyOutGameReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *ReadyOutGameReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *ReadyOutGameReq) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

type ReadyOutGameRsp struct {
	ErrorType            GameServerErrorType `protobuf:"varint,1,opt,name=error_type,json=errorType,proto3,enum=gameserver.GameServerErrorType" json:"error_type,omitempty"`
	ErrorMsg             string              `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ReadyOutGameRsp) Reset()         { *m = ReadyOutGameRsp{} }
func (m *ReadyOutGameRsp) String() string { return proto.CompactTextString(m) }
func (*ReadyOutGameRsp) ProtoMessage()    {}
func (*ReadyOutGameRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{27}
}
func (m *ReadyOutGameRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadyOutGameRsp.Unmarshal(m, b)
}
func (m *ReadyOutGameRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadyOutGameRsp.Marshal(b, m, deterministic)
}
func (dst *ReadyOutGameRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadyOutGameRsp.Merge(dst, src)
}
func (m *ReadyOutGameRsp) XXX_Size() int {
	return xxx_messageInfo_ReadyOutGameRsp.Size(m)
}
func (m *ReadyOutGameRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadyOutGameRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ReadyOutGameRsp proto.InternalMessageInfo

func (m *ReadyOutGameRsp) GetErrorType() GameServerErrorType {
	if m != nil {
		return m.ErrorType
	}
	return GameServerErrorType_GameOk
}

func (m *ReadyOutGameRsp) GetErrorMsg() string {
	if m != nil {
		return m.ErrorMsg
	}
	return ""
}

type VersionReq struct {
	Gameid               uint32   `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Version              string   `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VersionReq) Reset()         { *m = VersionReq{} }
func (m *VersionReq) String() string { return proto.CompactTextString(m) }
func (*VersionReq) ProtoMessage()    {}
func (*VersionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{28}
}
func (m *VersionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VersionReq.Unmarshal(m, b)
}
func (m *VersionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VersionReq.Marshal(b, m, deterministic)
}
func (dst *VersionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VersionReq.Merge(dst, src)
}
func (m *VersionReq) XXX_Size() int {
	return xxx_messageInfo_VersionReq.Size(m)
}
func (m *VersionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_VersionReq.DiscardUnknown(m)
}

var xxx_messageInfo_VersionReq proto.InternalMessageInfo

func (m *VersionReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *VersionReq) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type VersionRsp struct {
	Picurl               string   `protobuf:"bytes,1,opt,name=picurl,proto3" json:"picurl,omitempty"`
	Gameurl              string   `protobuf:"bytes,2,opt,name=gameurl,proto3" json:"gameurl,omitempty"`
	Engineversion        uint32   `protobuf:"varint,3,opt,name=engineversion,proto3" json:"engineversion,omitempty"`
	PackagePath          string   `protobuf:"bytes,4,opt,name=packagePath,proto3" json:"packagePath,omitempty"`
	Enginetype           uint32   `protobuf:"varint,5,opt,name=enginetype,proto3" json:"enginetype,omitempty"`
	Resurl               string   `protobuf:"bytes,6,opt,name=resurl,proto3" json:"resurl,omitempty"`
	Extraproperty        string   `protobuf:"bytes,7,opt,name=extraproperty,proto3" json:"extraproperty,omitempty"`
	PackageMD5           string   `protobuf:"bytes,8,opt,name=packageMD5,proto3" json:"packageMD5,omitempty"`
	ResMD5               string   `protobuf:"bytes,9,opt,name=resMD5,proto3" json:"resMD5,omitempty"`
	Statetemplate        string   `protobuf:"bytes,10,opt,name=statetemplate,proto3" json:"statetemplate,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VersionRsp) Reset()         { *m = VersionRsp{} }
func (m *VersionRsp) String() string { return proto.CompactTextString(m) }
func (*VersionRsp) ProtoMessage()    {}
func (*VersionRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{29}
}
func (m *VersionRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VersionRsp.Unmarshal(m, b)
}
func (m *VersionRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VersionRsp.Marshal(b, m, deterministic)
}
func (dst *VersionRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VersionRsp.Merge(dst, src)
}
func (m *VersionRsp) XXX_Size() int {
	return xxx_messageInfo_VersionRsp.Size(m)
}
func (m *VersionRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_VersionRsp.DiscardUnknown(m)
}

var xxx_messageInfo_VersionRsp proto.InternalMessageInfo

func (m *VersionRsp) GetPicurl() string {
	if m != nil {
		return m.Picurl
	}
	return ""
}

func (m *VersionRsp) GetGameurl() string {
	if m != nil {
		return m.Gameurl
	}
	return ""
}

func (m *VersionRsp) GetEngineversion() uint32 {
	if m != nil {
		return m.Engineversion
	}
	return 0
}

func (m *VersionRsp) GetPackagePath() string {
	if m != nil {
		return m.PackagePath
	}
	return ""
}

func (m *VersionRsp) GetEnginetype() uint32 {
	if m != nil {
		return m.Enginetype
	}
	return 0
}

func (m *VersionRsp) GetResurl() string {
	if m != nil {
		return m.Resurl
	}
	return ""
}

func (m *VersionRsp) GetExtraproperty() string {
	if m != nil {
		return m.Extraproperty
	}
	return ""
}

func (m *VersionRsp) GetPackageMD5() string {
	if m != nil {
		return m.PackageMD5
	}
	return ""
}

func (m *VersionRsp) GetResMD5() string {
	if m != nil {
		return m.ResMD5
	}
	return ""
}

func (m *VersionRsp) GetStatetemplate() string {
	if m != nil {
		return m.Statetemplate
	}
	return ""
}

type GameInfoReq struct {
	Bussid               uint32   `protobuf:"varint,1,opt,name=bussid,proto3" json:"bussid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameInfoReq) Reset()         { *m = GameInfoReq{} }
func (m *GameInfoReq) String() string { return proto.CompactTextString(m) }
func (*GameInfoReq) ProtoMessage()    {}
func (*GameInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{30}
}
func (m *GameInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameInfoReq.Unmarshal(m, b)
}
func (m *GameInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameInfoReq.Marshal(b, m, deterministic)
}
func (dst *GameInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameInfoReq.Merge(dst, src)
}
func (m *GameInfoReq) XXX_Size() int {
	return xxx_messageInfo_GameInfoReq.Size(m)
}
func (m *GameInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GameInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GameInfoReq proto.InternalMessageInfo

func (m *GameInfoReq) GetBussid() uint32 {
	if m != nil {
		return m.Bussid
	}
	return 0
}

type GameInfoT struct {
	Cpid                 uint32   `protobuf:"varint,1,opt,name=cpid,proto3" json:"cpid,omitempty"`
	Gameid               uint32   `protobuf:"varint,2,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Gamename             string   `protobuf:"bytes,3,opt,name=gamename,proto3" json:"gamename,omitempty"`
	Gametype             uint32   `protobuf:"varint,4,opt,name=gametype,proto3" json:"gametype,omitempty"`
	Gameversion          string   `protobuf:"bytes,5,opt,name=gameversion,proto3" json:"gameversion,omitempty"`
	PicUrl               string   `protobuf:"bytes,6,opt,name=pic_url,json=picUrl,proto3" json:"pic_url,omitempty"`
	GameUrl              string   `protobuf:"bytes,7,opt,name=game_url,json=gameUrl,proto3" json:"game_url,omitempty"`
	EngineVer            uint32   `protobuf:"varint,8,opt,name=engine_ver,json=engineVer,proto3" json:"engine_ver,omitempty"`
	PackagePath          string   `protobuf:"bytes,9,opt,name=package_path,json=packagePath,proto3" json:"package_path,omitempty"`
	MaxLoginCnt          uint32   `protobuf:"varint,10,opt,name=max_login_cnt,json=maxLoginCnt,proto3" json:"max_login_cnt,omitempty"`
	Appids               []uint32 `protobuf:"varint,11,rep,packed,name=appids,proto3" json:"appids,omitempty"`
	Platform             []uint32 `protobuf:"varint,12,rep,packed,name=platform,proto3" json:"platform,omitempty"`
	EngineType           uint32   `protobuf:"varint,13,opt,name=engine_type,json=engineType,proto3" json:"engine_type,omitempty"`
	Resurl               string   `protobuf:"bytes,14,opt,name=resurl,proto3" json:"resurl,omitempty"`
	GrayChids            []uint32 `protobuf:"varint,15,rep,packed,name=gray_chids,json=grayChids,proto3" json:"gray_chids,omitempty"`
	Extraproperty        string   `protobuf:"bytes,16,opt,name=extraproperty,proto3" json:"extraproperty,omitempty"`
	PackageMD5           string   `protobuf:"bytes,17,opt,name=packageMD5,proto3" json:"packageMD5,omitempty"`
	ResMD5               string   `protobuf:"bytes,18,opt,name=resMD5,proto3" json:"resMD5,omitempty"`
	Statetemplate        string   `protobuf:"bytes,19,opt,name=statetemplate,proto3" json:"statetemplate,omitempty"`
	Modeinfo             string   `protobuf:"bytes,20,opt,name=modeinfo,proto3" json:"modeinfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameInfoT) Reset()         { *m = GameInfoT{} }
func (m *GameInfoT) String() string { return proto.CompactTextString(m) }
func (*GameInfoT) ProtoMessage()    {}
func (*GameInfoT) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{31}
}
func (m *GameInfoT) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameInfoT.Unmarshal(m, b)
}
func (m *GameInfoT) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameInfoT.Marshal(b, m, deterministic)
}
func (dst *GameInfoT) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameInfoT.Merge(dst, src)
}
func (m *GameInfoT) XXX_Size() int {
	return xxx_messageInfo_GameInfoT.Size(m)
}
func (m *GameInfoT) XXX_DiscardUnknown() {
	xxx_messageInfo_GameInfoT.DiscardUnknown(m)
}

var xxx_messageInfo_GameInfoT proto.InternalMessageInfo

func (m *GameInfoT) GetCpid() uint32 {
	if m != nil {
		return m.Cpid
	}
	return 0
}

func (m *GameInfoT) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *GameInfoT) GetGamename() string {
	if m != nil {
		return m.Gamename
	}
	return ""
}

func (m *GameInfoT) GetGametype() uint32 {
	if m != nil {
		return m.Gametype
	}
	return 0
}

func (m *GameInfoT) GetGameversion() string {
	if m != nil {
		return m.Gameversion
	}
	return ""
}

func (m *GameInfoT) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *GameInfoT) GetGameUrl() string {
	if m != nil {
		return m.GameUrl
	}
	return ""
}

func (m *GameInfoT) GetEngineVer() uint32 {
	if m != nil {
		return m.EngineVer
	}
	return 0
}

func (m *GameInfoT) GetPackagePath() string {
	if m != nil {
		return m.PackagePath
	}
	return ""
}

func (m *GameInfoT) GetMaxLoginCnt() uint32 {
	if m != nil {
		return m.MaxLoginCnt
	}
	return 0
}

func (m *GameInfoT) GetAppids() []uint32 {
	if m != nil {
		return m.Appids
	}
	return nil
}

func (m *GameInfoT) GetPlatform() []uint32 {
	if m != nil {
		return m.Platform
	}
	return nil
}

func (m *GameInfoT) GetEngineType() uint32 {
	if m != nil {
		return m.EngineType
	}
	return 0
}

func (m *GameInfoT) GetResurl() string {
	if m != nil {
		return m.Resurl
	}
	return ""
}

func (m *GameInfoT) GetGrayChids() []uint32 {
	if m != nil {
		return m.GrayChids
	}
	return nil
}

func (m *GameInfoT) GetExtraproperty() string {
	if m != nil {
		return m.Extraproperty
	}
	return ""
}

func (m *GameInfoT) GetPackageMD5() string {
	if m != nil {
		return m.PackageMD5
	}
	return ""
}

func (m *GameInfoT) GetResMD5() string {
	if m != nil {
		return m.ResMD5
	}
	return ""
}

func (m *GameInfoT) GetStatetemplate() string {
	if m != nil {
		return m.Statetemplate
	}
	return ""
}

func (m *GameInfoT) GetModeinfo() string {
	if m != nil {
		return m.Modeinfo
	}
	return ""
}

type GameInfoRsp struct {
	Gameinfo             []*GameInfoT `protobuf:"bytes,1,rep,name=gameinfo,proto3" json:"gameinfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GameInfoRsp) Reset()         { *m = GameInfoRsp{} }
func (m *GameInfoRsp) String() string { return proto.CompactTextString(m) }
func (*GameInfoRsp) ProtoMessage()    {}
func (*GameInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{32}
}
func (m *GameInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameInfoRsp.Unmarshal(m, b)
}
func (m *GameInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameInfoRsp.Marshal(b, m, deterministic)
}
func (dst *GameInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameInfoRsp.Merge(dst, src)
}
func (m *GameInfoRsp) XXX_Size() int {
	return xxx_messageInfo_GameInfoRsp.Size(m)
}
func (m *GameInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GameInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GameInfoRsp proto.InternalMessageInfo

func (m *GameInfoRsp) GetGameinfo() []*GameInfoT {
	if m != nil {
		return m.Gameinfo
	}
	return nil
}

type CpCheckReq struct {
	Cpid                 uint32   `protobuf:"varint,1,opt,name=cpid,proto3" json:"cpid,omitempty"`
	Gameid               uint32   `protobuf:"varint,2,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Secret               string   `protobuf:"bytes,3,opt,name=secret,proto3" json:"secret,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CpCheckReq) Reset()         { *m = CpCheckReq{} }
func (m *CpCheckReq) String() string { return proto.CompactTextString(m) }
func (*CpCheckReq) ProtoMessage()    {}
func (*CpCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{33}
}
func (m *CpCheckReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CpCheckReq.Unmarshal(m, b)
}
func (m *CpCheckReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CpCheckReq.Marshal(b, m, deterministic)
}
func (dst *CpCheckReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CpCheckReq.Merge(dst, src)
}
func (m *CpCheckReq) XXX_Size() int {
	return xxx_messageInfo_CpCheckReq.Size(m)
}
func (m *CpCheckReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CpCheckReq.DiscardUnknown(m)
}

var xxx_messageInfo_CpCheckReq proto.InternalMessageInfo

func (m *CpCheckReq) GetCpid() uint32 {
	if m != nil {
		return m.Cpid
	}
	return 0
}

func (m *CpCheckReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *CpCheckReq) GetSecret() string {
	if m != nil {
		return m.Secret
	}
	return ""
}

type CpCheckRsp struct {
	Hit                  bool     `protobuf:"varint,1,opt,name=hit,proto3" json:"hit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CpCheckRsp) Reset()         { *m = CpCheckRsp{} }
func (m *CpCheckRsp) String() string { return proto.CompactTextString(m) }
func (*CpCheckRsp) ProtoMessage()    {}
func (*CpCheckRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{34}
}
func (m *CpCheckRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CpCheckRsp.Unmarshal(m, b)
}
func (m *CpCheckRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CpCheckRsp.Marshal(b, m, deterministic)
}
func (dst *CpCheckRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CpCheckRsp.Merge(dst, src)
}
func (m *CpCheckRsp) XXX_Size() int {
	return xxx_messageInfo_CpCheckRsp.Size(m)
}
func (m *CpCheckRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_CpCheckRsp.DiscardUnknown(m)
}

var xxx_messageInfo_CpCheckRsp proto.InternalMessageInfo

func (m *CpCheckRsp) GetHit() bool {
	if m != nil {
		return m.Hit
	}
	return false
}

type UserInfoSaveReq struct {
	Cpid                 uint32   `protobuf:"varint,1,opt,name=cpid,proto3" json:"cpid,omitempty"`
	Gameid               uint32   `protobuf:"varint,2,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Appid                uint32   `protobuf:"varint,3,opt,name=appid,proto3" json:"appid,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	Openid               string   `protobuf:"bytes,5,opt,name=openid,proto3" json:"openid,omitempty"`
	Roomid               uint32   `protobuf:"varint,6,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Ttid                 string   `protobuf:"bytes,7,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Bussid               uint32   `protobuf:"varint,8,opt,name=bussid,proto3" json:"bussid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfoSaveReq) Reset()         { *m = UserInfoSaveReq{} }
func (m *UserInfoSaveReq) String() string { return proto.CompactTextString(m) }
func (*UserInfoSaveReq) ProtoMessage()    {}
func (*UserInfoSaveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{35}
}
func (m *UserInfoSaveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfoSaveReq.Unmarshal(m, b)
}
func (m *UserInfoSaveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfoSaveReq.Marshal(b, m, deterministic)
}
func (dst *UserInfoSaveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfoSaveReq.Merge(dst, src)
}
func (m *UserInfoSaveReq) XXX_Size() int {
	return xxx_messageInfo_UserInfoSaveReq.Size(m)
}
func (m *UserInfoSaveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfoSaveReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfoSaveReq proto.InternalMessageInfo

func (m *UserInfoSaveReq) GetCpid() uint32 {
	if m != nil {
		return m.Cpid
	}
	return 0
}

func (m *UserInfoSaveReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *UserInfoSaveReq) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

func (m *UserInfoSaveReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserInfoSaveReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *UserInfoSaveReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *UserInfoSaveReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *UserInfoSaveReq) GetBussid() uint32 {
	if m != nil {
		return m.Bussid
	}
	return 0
}

type UserInfoSaveRsp struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfoSaveRsp) Reset()         { *m = UserInfoSaveRsp{} }
func (m *UserInfoSaveRsp) String() string { return proto.CompactTextString(m) }
func (*UserInfoSaveRsp) ProtoMessage()    {}
func (*UserInfoSaveRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{36}
}
func (m *UserInfoSaveRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfoSaveRsp.Unmarshal(m, b)
}
func (m *UserInfoSaveRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfoSaveRsp.Marshal(b, m, deterministic)
}
func (dst *UserInfoSaveRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfoSaveRsp.Merge(dst, src)
}
func (m *UserInfoSaveRsp) XXX_Size() int {
	return xxx_messageInfo_UserInfoSaveRsp.Size(m)
}
func (m *UserInfoSaveRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfoSaveRsp.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfoSaveRsp proto.InternalMessageInfo

func (m *UserInfoSaveRsp) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

// 游戏信息管理协议
type AddGameInfoReq struct {
	Cpid                 uint32   `protobuf:"varint,1,opt,name=cpid,proto3" json:"cpid,omitempty"`
	Gamename             string   `protobuf:"bytes,2,opt,name=gamename,proto3" json:"gamename,omitempty"`
	Maxlogincnt          uint32   `protobuf:"varint,3,opt,name=maxlogincnt,proto3" json:"maxlogincnt,omitempty"`
	Maxjoincnt           uint32   `protobuf:"varint,4,opt,name=maxjoincnt,proto3" json:"maxjoincnt,omitempty"`
	Maxreadycnt          uint32   `protobuf:"varint,5,opt,name=maxreadycnt,proto3" json:"maxreadycnt,omitempty"`
	Cpurl                string   `protobuf:"bytes,6,opt,name=cpurl,proto3" json:"cpurl,omitempty"`
	Gameid               uint32   `protobuf:"varint,7,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Version              string   `protobuf:"bytes,8,opt,name=version,proto3" json:"version,omitempty"`
	GrayChids            []uint32 `protobuf:"varint,9,rep,packed,name=gray_chids,json=grayChids,proto3" json:"gray_chids,omitempty"`
	Gameextraproperty    string   `protobuf:"bytes,10,opt,name=gameextraproperty,proto3" json:"gameextraproperty,omitempty"`
	Gamestatetemplate    string   `protobuf:"bytes,11,opt,name=gamestatetemplate,proto3" json:"gamestatetemplate,omitempty"`
	Bussid               uint32   `protobuf:"varint,12,opt,name=bussid,proto3" json:"bussid,omitempty"`
	Requesttype          uint32   `protobuf:"varint,13,opt,name=requesttype,proto3" json:"requesttype,omitempty"`
	Modeinfo             string   `protobuf:"bytes,14,opt,name=modeinfo,proto3" json:"modeinfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddGameInfoReq) Reset()         { *m = AddGameInfoReq{} }
func (m *AddGameInfoReq) String() string { return proto.CompactTextString(m) }
func (*AddGameInfoReq) ProtoMessage()    {}
func (*AddGameInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{37}
}
func (m *AddGameInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddGameInfoReq.Unmarshal(m, b)
}
func (m *AddGameInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddGameInfoReq.Marshal(b, m, deterministic)
}
func (dst *AddGameInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddGameInfoReq.Merge(dst, src)
}
func (m *AddGameInfoReq) XXX_Size() int {
	return xxx_messageInfo_AddGameInfoReq.Size(m)
}
func (m *AddGameInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddGameInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddGameInfoReq proto.InternalMessageInfo

func (m *AddGameInfoReq) GetCpid() uint32 {
	if m != nil {
		return m.Cpid
	}
	return 0
}

func (m *AddGameInfoReq) GetGamename() string {
	if m != nil {
		return m.Gamename
	}
	return ""
}

func (m *AddGameInfoReq) GetMaxlogincnt() uint32 {
	if m != nil {
		return m.Maxlogincnt
	}
	return 0
}

func (m *AddGameInfoReq) GetMaxjoincnt() uint32 {
	if m != nil {
		return m.Maxjoincnt
	}
	return 0
}

func (m *AddGameInfoReq) GetMaxreadycnt() uint32 {
	if m != nil {
		return m.Maxreadycnt
	}
	return 0
}

func (m *AddGameInfoReq) GetCpurl() string {
	if m != nil {
		return m.Cpurl
	}
	return ""
}

func (m *AddGameInfoReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *AddGameInfoReq) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *AddGameInfoReq) GetGrayChids() []uint32 {
	if m != nil {
		return m.GrayChids
	}
	return nil
}

func (m *AddGameInfoReq) GetGameextraproperty() string {
	if m != nil {
		return m.Gameextraproperty
	}
	return ""
}

func (m *AddGameInfoReq) GetGamestatetemplate() string {
	if m != nil {
		return m.Gamestatetemplate
	}
	return ""
}

func (m *AddGameInfoReq) GetBussid() uint32 {
	if m != nil {
		return m.Bussid
	}
	return 0
}

func (m *AddGameInfoReq) GetRequesttype() uint32 {
	if m != nil {
		return m.Requesttype
	}
	return 0
}

func (m *AddGameInfoReq) GetModeinfo() string {
	if m != nil {
		return m.Modeinfo
	}
	return ""
}

type AddGameInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddGameInfoResp) Reset()         { *m = AddGameInfoResp{} }
func (m *AddGameInfoResp) String() string { return proto.CompactTextString(m) }
func (*AddGameInfoResp) ProtoMessage()    {}
func (*AddGameInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{38}
}
func (m *AddGameInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddGameInfoResp.Unmarshal(m, b)
}
func (m *AddGameInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddGameInfoResp.Marshal(b, m, deterministic)
}
func (dst *AddGameInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddGameInfoResp.Merge(dst, src)
}
func (m *AddGameInfoResp) XXX_Size() int {
	return xxx_messageInfo_AddGameInfoResp.Size(m)
}
func (m *AddGameInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddGameInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddGameInfoResp proto.InternalMessageInfo

type GetGameInfoReq struct {
	Cpid                 uint32   `protobuf:"varint,1,opt,name=cpid,proto3" json:"cpid,omitempty"`
	Gameid               uint32   `protobuf:"varint,2,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Rows                 uint32   `protobuf:"varint,4,opt,name=rows,proto3" json:"rows,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameInfoReq) Reset()         { *m = GetGameInfoReq{} }
func (m *GetGameInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetGameInfoReq) ProtoMessage()    {}
func (*GetGameInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{39}
}
func (m *GetGameInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameInfoReq.Unmarshal(m, b)
}
func (m *GetGameInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetGameInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameInfoReq.Merge(dst, src)
}
func (m *GetGameInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetGameInfoReq.Size(m)
}
func (m *GetGameInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameInfoReq proto.InternalMessageInfo

func (m *GetGameInfoReq) GetCpid() uint32 {
	if m != nil {
		return m.Cpid
	}
	return 0
}

func (m *GetGameInfoReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *GetGameInfoReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetGameInfoReq) GetRows() uint32 {
	if m != nil {
		return m.Rows
	}
	return 0
}

type GetGameBaseInfo struct {
	Cpid                 uint32   `protobuf:"varint,1,opt,name=cpid,proto3" json:"cpid,omitempty"`
	Gameid               uint32   `protobuf:"varint,2,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Gamename             string   `protobuf:"bytes,3,opt,name=gamename,proto3" json:"gamename,omitempty"`
	Version              string   `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	Gamesecret           string   `protobuf:"bytes,5,opt,name=gamesecret,proto3" json:"gamesecret,omitempty"`
	Maxlogincnt          uint32   `protobuf:"varint,6,opt,name=maxlogincnt,proto3" json:"maxlogincnt,omitempty"`
	Maxjoincnt           uint32   `protobuf:"varint,7,opt,name=maxjoincnt,proto3" json:"maxjoincnt,omitempty"`
	Maxreadycnt          uint32   `protobuf:"varint,8,opt,name=maxreadycnt,proto3" json:"maxreadycnt,omitempty"`
	Cpurl                string   `protobuf:"bytes,9,opt,name=cpurl,proto3" json:"cpurl,omitempty"`
	GrayChids            []uint32 `protobuf:"varint,10,rep,packed,name=gray_chids,json=grayChids,proto3" json:"gray_chids,omitempty"`
	Gameextraproperty    string   `protobuf:"bytes,11,opt,name=gameextraproperty,proto3" json:"gameextraproperty,omitempty"`
	Gamestatetemplate    string   `protobuf:"bytes,12,opt,name=gamestatetemplate,proto3" json:"gamestatetemplate,omitempty"`
	Bussid               uint32   `protobuf:"varint,13,opt,name=bussid,proto3" json:"bussid,omitempty"`
	Modeinfo             string   `protobuf:"bytes,14,opt,name=modeinfo,proto3" json:"modeinfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameBaseInfo) Reset()         { *m = GetGameBaseInfo{} }
func (m *GetGameBaseInfo) String() string { return proto.CompactTextString(m) }
func (*GetGameBaseInfo) ProtoMessage()    {}
func (*GetGameBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{40}
}
func (m *GetGameBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameBaseInfo.Unmarshal(m, b)
}
func (m *GetGameBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameBaseInfo.Marshal(b, m, deterministic)
}
func (dst *GetGameBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameBaseInfo.Merge(dst, src)
}
func (m *GetGameBaseInfo) XXX_Size() int {
	return xxx_messageInfo_GetGameBaseInfo.Size(m)
}
func (m *GetGameBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameBaseInfo proto.InternalMessageInfo

func (m *GetGameBaseInfo) GetCpid() uint32 {
	if m != nil {
		return m.Cpid
	}
	return 0
}

func (m *GetGameBaseInfo) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *GetGameBaseInfo) GetGamename() string {
	if m != nil {
		return m.Gamename
	}
	return ""
}

func (m *GetGameBaseInfo) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *GetGameBaseInfo) GetGamesecret() string {
	if m != nil {
		return m.Gamesecret
	}
	return ""
}

func (m *GetGameBaseInfo) GetMaxlogincnt() uint32 {
	if m != nil {
		return m.Maxlogincnt
	}
	return 0
}

func (m *GetGameBaseInfo) GetMaxjoincnt() uint32 {
	if m != nil {
		return m.Maxjoincnt
	}
	return 0
}

func (m *GetGameBaseInfo) GetMaxreadycnt() uint32 {
	if m != nil {
		return m.Maxreadycnt
	}
	return 0
}

func (m *GetGameBaseInfo) GetCpurl() string {
	if m != nil {
		return m.Cpurl
	}
	return ""
}

func (m *GetGameBaseInfo) GetGrayChids() []uint32 {
	if m != nil {
		return m.GrayChids
	}
	return nil
}

func (m *GetGameBaseInfo) GetGameextraproperty() string {
	if m != nil {
		return m.Gameextraproperty
	}
	return ""
}

func (m *GetGameBaseInfo) GetGamestatetemplate() string {
	if m != nil {
		return m.Gamestatetemplate
	}
	return ""
}

func (m *GetGameBaseInfo) GetBussid() uint32 {
	if m != nil {
		return m.Bussid
	}
	return 0
}

func (m *GetGameBaseInfo) GetModeinfo() string {
	if m != nil {
		return m.Modeinfo
	}
	return ""
}

type GetGameInfoResp struct {
	Info                 []*GetGameBaseInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	Totalnum             uint32             `protobuf:"varint,2,opt,name=totalnum,proto3" json:"totalnum,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetGameInfoResp) Reset()         { *m = GetGameInfoResp{} }
func (m *GetGameInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetGameInfoResp) ProtoMessage()    {}
func (*GetGameInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{41}
}
func (m *GetGameInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameInfoResp.Unmarshal(m, b)
}
func (m *GetGameInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetGameInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameInfoResp.Merge(dst, src)
}
func (m *GetGameInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetGameInfoResp.Size(m)
}
func (m *GetGameInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameInfoResp proto.InternalMessageInfo

func (m *GetGameInfoResp) GetInfo() []*GetGameBaseInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetGameInfoResp) GetTotalnum() uint32 {
	if m != nil {
		return m.Totalnum
	}
	return 0
}

type DelGameInfoReq struct {
	Gameid               uint32   `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelGameInfoReq) Reset()         { *m = DelGameInfoReq{} }
func (m *DelGameInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelGameInfoReq) ProtoMessage()    {}
func (*DelGameInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{42}
}
func (m *DelGameInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelGameInfoReq.Unmarshal(m, b)
}
func (m *DelGameInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelGameInfoReq.Marshal(b, m, deterministic)
}
func (dst *DelGameInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelGameInfoReq.Merge(dst, src)
}
func (m *DelGameInfoReq) XXX_Size() int {
	return xxx_messageInfo_DelGameInfoReq.Size(m)
}
func (m *DelGameInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelGameInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelGameInfoReq proto.InternalMessageInfo

func (m *DelGameInfoReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

type DelGameInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelGameInfoResp) Reset()         { *m = DelGameInfoResp{} }
func (m *DelGameInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelGameInfoResp) ProtoMessage()    {}
func (*DelGameInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{43}
}
func (m *DelGameInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelGameInfoResp.Unmarshal(m, b)
}
func (m *DelGameInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelGameInfoResp.Marshal(b, m, deterministic)
}
func (dst *DelGameInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelGameInfoResp.Merge(dst, src)
}
func (m *DelGameInfoResp) XXX_Size() int {
	return xxx_messageInfo_DelGameInfoResp.Size(m)
}
func (m *DelGameInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelGameInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelGameInfoResp proto.InternalMessageInfo

type ModifyGameInfoReq struct {
	Gameid               uint32   `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Gamename             string   `protobuf:"bytes,2,opt,name=gamename,proto3" json:"gamename,omitempty"`
	Maxlogincnt          uint32   `protobuf:"varint,3,opt,name=maxlogincnt,proto3" json:"maxlogincnt,omitempty"`
	Maxjoincnt           uint32   `protobuf:"varint,4,opt,name=maxjoincnt,proto3" json:"maxjoincnt,omitempty"`
	Maxreadycnt          uint32   `protobuf:"varint,5,opt,name=maxreadycnt,proto3" json:"maxreadycnt,omitempty"`
	Cpurl                string   `protobuf:"bytes,6,opt,name=cpurl,proto3" json:"cpurl,omitempty"`
	GrayChids            []uint32 `protobuf:"varint,7,rep,packed,name=gray_chids,json=grayChids,proto3" json:"gray_chids,omitempty"`
	Gameextraproperty    string   `protobuf:"bytes,8,opt,name=gameextraproperty,proto3" json:"gameextraproperty,omitempty"`
	Gamestatetemplate    string   `protobuf:"bytes,9,opt,name=gamestatetemplate,proto3" json:"gamestatetemplate,omitempty"`
	Bussid               uint32   `protobuf:"varint,10,opt,name=bussid,proto3" json:"bussid,omitempty"`
	Modeinfo             string   `protobuf:"bytes,11,opt,name=modeinfo,proto3" json:"modeinfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyGameInfoReq) Reset()         { *m = ModifyGameInfoReq{} }
func (m *ModifyGameInfoReq) String() string { return proto.CompactTextString(m) }
func (*ModifyGameInfoReq) ProtoMessage()    {}
func (*ModifyGameInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{44}
}
func (m *ModifyGameInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyGameInfoReq.Unmarshal(m, b)
}
func (m *ModifyGameInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyGameInfoReq.Marshal(b, m, deterministic)
}
func (dst *ModifyGameInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyGameInfoReq.Merge(dst, src)
}
func (m *ModifyGameInfoReq) XXX_Size() int {
	return xxx_messageInfo_ModifyGameInfoReq.Size(m)
}
func (m *ModifyGameInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyGameInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyGameInfoReq proto.InternalMessageInfo

func (m *ModifyGameInfoReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *ModifyGameInfoReq) GetGamename() string {
	if m != nil {
		return m.Gamename
	}
	return ""
}

func (m *ModifyGameInfoReq) GetMaxlogincnt() uint32 {
	if m != nil {
		return m.Maxlogincnt
	}
	return 0
}

func (m *ModifyGameInfoReq) GetMaxjoincnt() uint32 {
	if m != nil {
		return m.Maxjoincnt
	}
	return 0
}

func (m *ModifyGameInfoReq) GetMaxreadycnt() uint32 {
	if m != nil {
		return m.Maxreadycnt
	}
	return 0
}

func (m *ModifyGameInfoReq) GetCpurl() string {
	if m != nil {
		return m.Cpurl
	}
	return ""
}

func (m *ModifyGameInfoReq) GetGrayChids() []uint32 {
	if m != nil {
		return m.GrayChids
	}
	return nil
}

func (m *ModifyGameInfoReq) GetGameextraproperty() string {
	if m != nil {
		return m.Gameextraproperty
	}
	return ""
}

func (m *ModifyGameInfoReq) GetGamestatetemplate() string {
	if m != nil {
		return m.Gamestatetemplate
	}
	return ""
}

func (m *ModifyGameInfoReq) GetBussid() uint32 {
	if m != nil {
		return m.Bussid
	}
	return 0
}

func (m *ModifyGameInfoReq) GetModeinfo() string {
	if m != nil {
		return m.Modeinfo
	}
	return ""
}

type ModifyGameInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyGameInfoResp) Reset()         { *m = ModifyGameInfoResp{} }
func (m *ModifyGameInfoResp) String() string { return proto.CompactTextString(m) }
func (*ModifyGameInfoResp) ProtoMessage()    {}
func (*ModifyGameInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{45}
}
func (m *ModifyGameInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyGameInfoResp.Unmarshal(m, b)
}
func (m *ModifyGameInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyGameInfoResp.Marshal(b, m, deterministic)
}
func (dst *ModifyGameInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyGameInfoResp.Merge(dst, src)
}
func (m *ModifyGameInfoResp) XXX_Size() int {
	return xxx_messageInfo_ModifyGameInfoResp.Size(m)
}
func (m *ModifyGameInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyGameInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyGameInfoResp proto.InternalMessageInfo

// 游戏版本管理协议
type UpdateVersionInfoReq struct {
	Gameid               uint32   `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Version              string   `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Engineversion        uint32   `protobuf:"varint,3,opt,name=engineversion,proto3" json:"engineversion,omitempty"`
	Enginetype           uint32   `protobuf:"varint,4,opt,name=enginetype,proto3" json:"enginetype,omitempty"`
	Packagepath          string   `protobuf:"bytes,5,opt,name=packagepath,proto3" json:"packagepath,omitempty"`
	Picurl               string   `protobuf:"bytes,6,opt,name=picurl,proto3" json:"picurl,omitempty"`
	Gameurl              string   `protobuf:"bytes,7,opt,name=gameurl,proto3" json:"gameurl,omitempty"`
	Resurl               string   `protobuf:"bytes,8,opt,name=resurl,proto3" json:"resurl,omitempty"`
	Extraproperty        string   `protobuf:"bytes,9,opt,name=extraproperty,proto3" json:"extraproperty,omitempty"`
	PackageMD5           string   `protobuf:"bytes,10,opt,name=packageMD5,proto3" json:"packageMD5,omitempty"`
	ResMD5               string   `protobuf:"bytes,11,opt,name=resMD5,proto3" json:"resMD5,omitempty"`
	Statetemplate        string   `protobuf:"bytes,12,opt,name=statetemplate,proto3" json:"statetemplate,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateVersionInfoReq) Reset()         { *m = UpdateVersionInfoReq{} }
func (m *UpdateVersionInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateVersionInfoReq) ProtoMessage()    {}
func (*UpdateVersionInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{46}
}
func (m *UpdateVersionInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateVersionInfoReq.Unmarshal(m, b)
}
func (m *UpdateVersionInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateVersionInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpdateVersionInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateVersionInfoReq.Merge(dst, src)
}
func (m *UpdateVersionInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpdateVersionInfoReq.Size(m)
}
func (m *UpdateVersionInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateVersionInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateVersionInfoReq proto.InternalMessageInfo

func (m *UpdateVersionInfoReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *UpdateVersionInfoReq) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *UpdateVersionInfoReq) GetEngineversion() uint32 {
	if m != nil {
		return m.Engineversion
	}
	return 0
}

func (m *UpdateVersionInfoReq) GetEnginetype() uint32 {
	if m != nil {
		return m.Enginetype
	}
	return 0
}

func (m *UpdateVersionInfoReq) GetPackagepath() string {
	if m != nil {
		return m.Packagepath
	}
	return ""
}

func (m *UpdateVersionInfoReq) GetPicurl() string {
	if m != nil {
		return m.Picurl
	}
	return ""
}

func (m *UpdateVersionInfoReq) GetGameurl() string {
	if m != nil {
		return m.Gameurl
	}
	return ""
}

func (m *UpdateVersionInfoReq) GetResurl() string {
	if m != nil {
		return m.Resurl
	}
	return ""
}

func (m *UpdateVersionInfoReq) GetExtraproperty() string {
	if m != nil {
		return m.Extraproperty
	}
	return ""
}

func (m *UpdateVersionInfoReq) GetPackageMD5() string {
	if m != nil {
		return m.PackageMD5
	}
	return ""
}

func (m *UpdateVersionInfoReq) GetResMD5() string {
	if m != nil {
		return m.ResMD5
	}
	return ""
}

func (m *UpdateVersionInfoReq) GetStatetemplate() string {
	if m != nil {
		return m.Statetemplate
	}
	return ""
}

type UpdateVersionInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateVersionInfoResp) Reset()         { *m = UpdateVersionInfoResp{} }
func (m *UpdateVersionInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpdateVersionInfoResp) ProtoMessage()    {}
func (*UpdateVersionInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{47}
}
func (m *UpdateVersionInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateVersionInfoResp.Unmarshal(m, b)
}
func (m *UpdateVersionInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateVersionInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpdateVersionInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateVersionInfoResp.Merge(dst, src)
}
func (m *UpdateVersionInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpdateVersionInfoResp.Size(m)
}
func (m *UpdateVersionInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateVersionInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateVersionInfoResp proto.InternalMessageInfo

type GetVersionInfoReq struct {
	Gameid               uint32   `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Rows                 uint32   `protobuf:"varint,3,opt,name=rows,proto3" json:"rows,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVersionInfoReq) Reset()         { *m = GetVersionInfoReq{} }
func (m *GetVersionInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetVersionInfoReq) ProtoMessage()    {}
func (*GetVersionInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{48}
}
func (m *GetVersionInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVersionInfoReq.Unmarshal(m, b)
}
func (m *GetVersionInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVersionInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetVersionInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVersionInfoReq.Merge(dst, src)
}
func (m *GetVersionInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetVersionInfoReq.Size(m)
}
func (m *GetVersionInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVersionInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetVersionInfoReq proto.InternalMessageInfo

func (m *GetVersionInfoReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *GetVersionInfoReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetVersionInfoReq) GetRows() uint32 {
	if m != nil {
		return m.Rows
	}
	return 0
}

type VersionBaseInfo struct {
	Gameid               uint32   `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Version              string   `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Engineversion        uint32   `protobuf:"varint,3,opt,name=engineversion,proto3" json:"engineversion,omitempty"`
	Enginetype           uint32   `protobuf:"varint,4,opt,name=enginetype,proto3" json:"enginetype,omitempty"`
	Packagepath          string   `protobuf:"bytes,5,opt,name=packagepath,proto3" json:"packagepath,omitempty"`
	Picurl               string   `protobuf:"bytes,6,opt,name=picurl,proto3" json:"picurl,omitempty"`
	Gameurl              string   `protobuf:"bytes,7,opt,name=gameurl,proto3" json:"gameurl,omitempty"`
	Resurl               string   `protobuf:"bytes,8,opt,name=resurl,proto3" json:"resurl,omitempty"`
	Extraproperty        string   `protobuf:"bytes,9,opt,name=extraproperty,proto3" json:"extraproperty,omitempty"`
	PackageMD5           string   `protobuf:"bytes,10,opt,name=packageMD5,proto3" json:"packageMD5,omitempty"`
	ResMD5               string   `protobuf:"bytes,11,opt,name=resMD5,proto3" json:"resMD5,omitempty"`
	Statetemplate        string   `protobuf:"bytes,12,opt,name=statetemplate,proto3" json:"statetemplate,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VersionBaseInfo) Reset()         { *m = VersionBaseInfo{} }
func (m *VersionBaseInfo) String() string { return proto.CompactTextString(m) }
func (*VersionBaseInfo) ProtoMessage()    {}
func (*VersionBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{49}
}
func (m *VersionBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VersionBaseInfo.Unmarshal(m, b)
}
func (m *VersionBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VersionBaseInfo.Marshal(b, m, deterministic)
}
func (dst *VersionBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VersionBaseInfo.Merge(dst, src)
}
func (m *VersionBaseInfo) XXX_Size() int {
	return xxx_messageInfo_VersionBaseInfo.Size(m)
}
func (m *VersionBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VersionBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VersionBaseInfo proto.InternalMessageInfo

func (m *VersionBaseInfo) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *VersionBaseInfo) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *VersionBaseInfo) GetEngineversion() uint32 {
	if m != nil {
		return m.Engineversion
	}
	return 0
}

func (m *VersionBaseInfo) GetEnginetype() uint32 {
	if m != nil {
		return m.Enginetype
	}
	return 0
}

func (m *VersionBaseInfo) GetPackagepath() string {
	if m != nil {
		return m.Packagepath
	}
	return ""
}

func (m *VersionBaseInfo) GetPicurl() string {
	if m != nil {
		return m.Picurl
	}
	return ""
}

func (m *VersionBaseInfo) GetGameurl() string {
	if m != nil {
		return m.Gameurl
	}
	return ""
}

func (m *VersionBaseInfo) GetResurl() string {
	if m != nil {
		return m.Resurl
	}
	return ""
}

func (m *VersionBaseInfo) GetExtraproperty() string {
	if m != nil {
		return m.Extraproperty
	}
	return ""
}

func (m *VersionBaseInfo) GetPackageMD5() string {
	if m != nil {
		return m.PackageMD5
	}
	return ""
}

func (m *VersionBaseInfo) GetResMD5() string {
	if m != nil {
		return m.ResMD5
	}
	return ""
}

func (m *VersionBaseInfo) GetStatetemplate() string {
	if m != nil {
		return m.Statetemplate
	}
	return ""
}

type GetVersionInfoResp struct {
	Info                 []*VersionBaseInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	Totalnum             uint32             `protobuf:"varint,2,opt,name=totalnum,proto3" json:"totalnum,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetVersionInfoResp) Reset()         { *m = GetVersionInfoResp{} }
func (m *GetVersionInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetVersionInfoResp) ProtoMessage()    {}
func (*GetVersionInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{50}
}
func (m *GetVersionInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVersionInfoResp.Unmarshal(m, b)
}
func (m *GetVersionInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVersionInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetVersionInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVersionInfoResp.Merge(dst, src)
}
func (m *GetVersionInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetVersionInfoResp.Size(m)
}
func (m *GetVersionInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVersionInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetVersionInfoResp proto.InternalMessageInfo

func (m *GetVersionInfoResp) GetInfo() []*VersionBaseInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetVersionInfoResp) GetTotalnum() uint32 {
	if m != nil {
		return m.Totalnum
	}
	return 0
}

type DelVersionInfoReq struct {
	Gameid               uint32   `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Version              string   `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelVersionInfoReq) Reset()         { *m = DelVersionInfoReq{} }
func (m *DelVersionInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelVersionInfoReq) ProtoMessage()    {}
func (*DelVersionInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{51}
}
func (m *DelVersionInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelVersionInfoReq.Unmarshal(m, b)
}
func (m *DelVersionInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelVersionInfoReq.Marshal(b, m, deterministic)
}
func (dst *DelVersionInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelVersionInfoReq.Merge(dst, src)
}
func (m *DelVersionInfoReq) XXX_Size() int {
	return xxx_messageInfo_DelVersionInfoReq.Size(m)
}
func (m *DelVersionInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelVersionInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelVersionInfoReq proto.InternalMessageInfo

func (m *DelVersionInfoReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *DelVersionInfoReq) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type DelVersionInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelVersionInfoResp) Reset()         { *m = DelVersionInfoResp{} }
func (m *DelVersionInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelVersionInfoResp) ProtoMessage()    {}
func (*DelVersionInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{52}
}
func (m *DelVersionInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelVersionInfoResp.Unmarshal(m, b)
}
func (m *DelVersionInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelVersionInfoResp.Marshal(b, m, deterministic)
}
func (dst *DelVersionInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelVersionInfoResp.Merge(dst, src)
}
func (m *DelVersionInfoResp) XXX_Size() int {
	return xxx_messageInfo_DelVersionInfoResp.Size(m)
}
func (m *DelVersionInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelVersionInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelVersionInfoResp proto.InternalMessageInfo

type UserPresidentReq struct {
	Gameid               uint32   `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Roomid               uint32   `protobuf:"varint,2,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Openid               string   `protobuf:"bytes,3,opt,name=openid,proto3" json:"openid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPresidentReq) Reset()         { *m = UserPresidentReq{} }
func (m *UserPresidentReq) String() string { return proto.CompactTextString(m) }
func (*UserPresidentReq) ProtoMessage()    {}
func (*UserPresidentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{53}
}
func (m *UserPresidentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPresidentReq.Unmarshal(m, b)
}
func (m *UserPresidentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPresidentReq.Marshal(b, m, deterministic)
}
func (dst *UserPresidentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPresidentReq.Merge(dst, src)
}
func (m *UserPresidentReq) XXX_Size() int {
	return xxx_messageInfo_UserPresidentReq.Size(m)
}
func (m *UserPresidentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPresidentReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserPresidentReq proto.InternalMessageInfo

func (m *UserPresidentReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *UserPresidentReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *UserPresidentReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

type UserPresidentRsp struct {
	Gameid               uint32              `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Roomid               uint32              `protobuf:"varint,2,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Openid               string              `protobuf:"bytes,3,opt,name=openid,proto3" json:"openid,omitempty"`
	ErrorType            GameServerErrorType `protobuf:"varint,4,opt,name=error_type,json=errorType,proto3,enum=gameserver.GameServerErrorType" json:"error_type,omitempty"`
	ErrorMsg             string              `protobuf:"bytes,5,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UserPresidentRsp) Reset()         { *m = UserPresidentRsp{} }
func (m *UserPresidentRsp) String() string { return proto.CompactTextString(m) }
func (*UserPresidentRsp) ProtoMessage()    {}
func (*UserPresidentRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{54}
}
func (m *UserPresidentRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPresidentRsp.Unmarshal(m, b)
}
func (m *UserPresidentRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPresidentRsp.Marshal(b, m, deterministic)
}
func (dst *UserPresidentRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPresidentRsp.Merge(dst, src)
}
func (m *UserPresidentRsp) XXX_Size() int {
	return xxx_messageInfo_UserPresidentRsp.Size(m)
}
func (m *UserPresidentRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPresidentRsp.DiscardUnknown(m)
}

var xxx_messageInfo_UserPresidentRsp proto.InternalMessageInfo

func (m *UserPresidentRsp) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *UserPresidentRsp) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *UserPresidentRsp) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *UserPresidentRsp) GetErrorType() GameServerErrorType {
	if m != nil {
		return m.ErrorType
	}
	return GameServerErrorType_GameOk
}

func (m *UserPresidentRsp) GetErrorMsg() string {
	if m != nil {
		return m.ErrorMsg
	}
	return ""
}

type GetOpenIdByTTidReq struct {
	Ttid                 string   `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOpenIdByTTidReq) Reset()         { *m = GetOpenIdByTTidReq{} }
func (m *GetOpenIdByTTidReq) String() string { return proto.CompactTextString(m) }
func (*GetOpenIdByTTidReq) ProtoMessage()    {}
func (*GetOpenIdByTTidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{55}
}
func (m *GetOpenIdByTTidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOpenIdByTTidReq.Unmarshal(m, b)
}
func (m *GetOpenIdByTTidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOpenIdByTTidReq.Marshal(b, m, deterministic)
}
func (dst *GetOpenIdByTTidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOpenIdByTTidReq.Merge(dst, src)
}
func (m *GetOpenIdByTTidReq) XXX_Size() int {
	return xxx_messageInfo_GetOpenIdByTTidReq.Size(m)
}
func (m *GetOpenIdByTTidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOpenIdByTTidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOpenIdByTTidReq proto.InternalMessageInfo

func (m *GetOpenIdByTTidReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

type GetOpenIdByTTidInfo struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	Gameid               uint32   `protobuf:"varint,2,opt,name=gameid,proto3" json:"gameid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOpenIdByTTidInfo) Reset()         { *m = GetOpenIdByTTidInfo{} }
func (m *GetOpenIdByTTidInfo) String() string { return proto.CompactTextString(m) }
func (*GetOpenIdByTTidInfo) ProtoMessage()    {}
func (*GetOpenIdByTTidInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{56}
}
func (m *GetOpenIdByTTidInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOpenIdByTTidInfo.Unmarshal(m, b)
}
func (m *GetOpenIdByTTidInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOpenIdByTTidInfo.Marshal(b, m, deterministic)
}
func (dst *GetOpenIdByTTidInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOpenIdByTTidInfo.Merge(dst, src)
}
func (m *GetOpenIdByTTidInfo) XXX_Size() int {
	return xxx_messageInfo_GetOpenIdByTTidInfo.Size(m)
}
func (m *GetOpenIdByTTidInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOpenIdByTTidInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetOpenIdByTTidInfo proto.InternalMessageInfo

func (m *GetOpenIdByTTidInfo) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *GetOpenIdByTTidInfo) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

type GetOpenIdByTTidRsp struct {
	Infos                []*GetOpenIdByTTidInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetOpenIdByTTidRsp) Reset()         { *m = GetOpenIdByTTidRsp{} }
func (m *GetOpenIdByTTidRsp) String() string { return proto.CompactTextString(m) }
func (*GetOpenIdByTTidRsp) ProtoMessage()    {}
func (*GetOpenIdByTTidRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{57}
}
func (m *GetOpenIdByTTidRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOpenIdByTTidRsp.Unmarshal(m, b)
}
func (m *GetOpenIdByTTidRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOpenIdByTTidRsp.Marshal(b, m, deterministic)
}
func (dst *GetOpenIdByTTidRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOpenIdByTTidRsp.Merge(dst, src)
}
func (m *GetOpenIdByTTidRsp) XXX_Size() int {
	return xxx_messageInfo_GetOpenIdByTTidRsp.Size(m)
}
func (m *GetOpenIdByTTidRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOpenIdByTTidRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOpenIdByTTidRsp proto.InternalMessageInfo

func (m *GetOpenIdByTTidRsp) GetInfos() []*GetOpenIdByTTidInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

type GetUidByOpenidInfo struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUidByOpenidInfo) Reset()         { *m = GetUidByOpenidInfo{} }
func (m *GetUidByOpenidInfo) String() string { return proto.CompactTextString(m) }
func (*GetUidByOpenidInfo) ProtoMessage()    {}
func (*GetUidByOpenidInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{58}
}
func (m *GetUidByOpenidInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUidByOpenidInfo.Unmarshal(m, b)
}
func (m *GetUidByOpenidInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUidByOpenidInfo.Marshal(b, m, deterministic)
}
func (dst *GetUidByOpenidInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUidByOpenidInfo.Merge(dst, src)
}
func (m *GetUidByOpenidInfo) XXX_Size() int {
	return xxx_messageInfo_GetUidByOpenidInfo.Size(m)
}
func (m *GetUidByOpenidInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUidByOpenidInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetUidByOpenidInfo proto.InternalMessageInfo

func (m *GetUidByOpenidInfo) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *GetUidByOpenidInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUidByOpenidReq struct {
	Gameid               uint32   `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Openid               string   `protobuf:"bytes,2,opt,name=openid,proto3" json:"openid,omitempty"`
	Roomid               uint32   `protobuf:"varint,3,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Openids              []string `protobuf:"bytes,4,rep,name=openids,proto3" json:"openids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUidByOpenidReq) Reset()         { *m = GetUidByOpenidReq{} }
func (m *GetUidByOpenidReq) String() string { return proto.CompactTextString(m) }
func (*GetUidByOpenidReq) ProtoMessage()    {}
func (*GetUidByOpenidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{59}
}
func (m *GetUidByOpenidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUidByOpenidReq.Unmarshal(m, b)
}
func (m *GetUidByOpenidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUidByOpenidReq.Marshal(b, m, deterministic)
}
func (dst *GetUidByOpenidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUidByOpenidReq.Merge(dst, src)
}
func (m *GetUidByOpenidReq) XXX_Size() int {
	return xxx_messageInfo_GetUidByOpenidReq.Size(m)
}
func (m *GetUidByOpenidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUidByOpenidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUidByOpenidReq proto.InternalMessageInfo

func (m *GetUidByOpenidReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *GetUidByOpenidReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *GetUidByOpenidReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *GetUidByOpenidReq) GetOpenids() []string {
	if m != nil {
		return m.Openids
	}
	return nil
}

type GetUidByOpenidRsp struct {
	Infos                []*GetUidByOpenidInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	ErrorType            GameServerErrorType   `protobuf:"varint,2,opt,name=error_type,json=errorType,proto3,enum=gameserver.GameServerErrorType" json:"error_type,omitempty"`
	ErrorMsg             string                `protobuf:"bytes,3,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetUidByOpenidRsp) Reset()         { *m = GetUidByOpenidRsp{} }
func (m *GetUidByOpenidRsp) String() string { return proto.CompactTextString(m) }
func (*GetUidByOpenidRsp) ProtoMessage()    {}
func (*GetUidByOpenidRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{60}
}
func (m *GetUidByOpenidRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUidByOpenidRsp.Unmarshal(m, b)
}
func (m *GetUidByOpenidRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUidByOpenidRsp.Marshal(b, m, deterministic)
}
func (dst *GetUidByOpenidRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUidByOpenidRsp.Merge(dst, src)
}
func (m *GetUidByOpenidRsp) XXX_Size() int {
	return xxx_messageInfo_GetUidByOpenidRsp.Size(m)
}
func (m *GetUidByOpenidRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUidByOpenidRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUidByOpenidRsp proto.InternalMessageInfo

func (m *GetUidByOpenidRsp) GetInfos() []*GetUidByOpenidInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

func (m *GetUidByOpenidRsp) GetErrorType() GameServerErrorType {
	if m != nil {
		return m.ErrorType
	}
	return GameServerErrorType_GameOk
}

func (m *GetUidByOpenidRsp) GetErrorMsg() string {
	if m != nil {
		return m.ErrorMsg
	}
	return ""
}

type UserExitReq struct {
	LeaveType            UserLeaveType `protobuf:"varint,1,opt,name=leaveType,proto3,enum=gameserver.UserLeaveType" json:"leaveType,omitempty"`
	Uid                  uint32        `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Roomid               uint32        `protobuf:"varint,3,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Bussid               uint32        `protobuf:"varint,4,opt,name=bussid,proto3" json:"bussid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UserExitReq) Reset()         { *m = UserExitReq{} }
func (m *UserExitReq) String() string { return proto.CompactTextString(m) }
func (*UserExitReq) ProtoMessage()    {}
func (*UserExitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{61}
}
func (m *UserExitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserExitReq.Unmarshal(m, b)
}
func (m *UserExitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserExitReq.Marshal(b, m, deterministic)
}
func (dst *UserExitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserExitReq.Merge(dst, src)
}
func (m *UserExitReq) XXX_Size() int {
	return xxx_messageInfo_UserExitReq.Size(m)
}
func (m *UserExitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserExitReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserExitReq proto.InternalMessageInfo

func (m *UserExitReq) GetLeaveType() UserLeaveType {
	if m != nil {
		return m.LeaveType
	}
	return UserLeaveType_UserSimpleLeave
}

func (m *UserExitReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserExitReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *UserExitReq) GetBussid() uint32 {
	if m != nil {
		return m.Bussid
	}
	return 0
}

type UserExitRsp struct {
	ErrorType            GameServerErrorType `protobuf:"varint,1,opt,name=error_type,json=errorType,proto3,enum=gameserver.GameServerErrorType" json:"error_type,omitempty"`
	ErrorMsg             string              `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UserExitRsp) Reset()         { *m = UserExitRsp{} }
func (m *UserExitRsp) String() string { return proto.CompactTextString(m) }
func (*UserExitRsp) ProtoMessage()    {}
func (*UserExitRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{62}
}
func (m *UserExitRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserExitRsp.Unmarshal(m, b)
}
func (m *UserExitRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserExitRsp.Marshal(b, m, deterministic)
}
func (dst *UserExitRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserExitRsp.Merge(dst, src)
}
func (m *UserExitRsp) XXX_Size() int {
	return xxx_messageInfo_UserExitRsp.Size(m)
}
func (m *UserExitRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserExitRsp.DiscardUnknown(m)
}

var xxx_messageInfo_UserExitRsp proto.InternalMessageInfo

func (m *UserExitRsp) GetErrorType() GameServerErrorType {
	if m != nil {
		return m.ErrorType
	}
	return GameServerErrorType_GameOk
}

func (m *UserExitRsp) GetErrorMsg() string {
	if m != nil {
		return m.ErrorMsg
	}
	return ""
}

type RobotInfo struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  string   `protobuf:"bytes,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Headurl              string   `protobuf:"bytes,4,opt,name=headurl,proto3" json:"headurl,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RobotInfo) Reset()         { *m = RobotInfo{} }
func (m *RobotInfo) String() string { return proto.CompactTextString(m) }
func (*RobotInfo) ProtoMessage()    {}
func (*RobotInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{63}
}
func (m *RobotInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RobotInfo.Unmarshal(m, b)
}
func (m *RobotInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RobotInfo.Marshal(b, m, deterministic)
}
func (dst *RobotInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RobotInfo.Merge(dst, src)
}
func (m *RobotInfo) XXX_Size() int {
	return xxx_messageInfo_RobotInfo.Size(m)
}
func (m *RobotInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RobotInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RobotInfo proto.InternalMessageInfo

func (m *RobotInfo) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *RobotInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *RobotInfo) GetSex() string {
	if m != nil {
		return m.Sex
	}
	return ""
}

func (m *RobotInfo) GetHeadurl() string {
	if m != nil {
		return m.Headurl
	}
	return ""
}

type GetRobotUidsReq struct {
	Gameid               uint32   `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Roomid               uint32   `protobuf:"varint,2,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Setid                uint32   `protobuf:"varint,3,opt,name=setid,proto3" json:"setid,omitempty"`
	RobotCnt             uint32   `protobuf:"varint,4,opt,name=robot_cnt,json=robotCnt,proto3" json:"robot_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRobotUidsReq) Reset()         { *m = GetRobotUidsReq{} }
func (m *GetRobotUidsReq) String() string { return proto.CompactTextString(m) }
func (*GetRobotUidsReq) ProtoMessage()    {}
func (*GetRobotUidsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{64}
}
func (m *GetRobotUidsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRobotUidsReq.Unmarshal(m, b)
}
func (m *GetRobotUidsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRobotUidsReq.Marshal(b, m, deterministic)
}
func (dst *GetRobotUidsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRobotUidsReq.Merge(dst, src)
}
func (m *GetRobotUidsReq) XXX_Size() int {
	return xxx_messageInfo_GetRobotUidsReq.Size(m)
}
func (m *GetRobotUidsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRobotUidsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRobotUidsReq proto.InternalMessageInfo

func (m *GetRobotUidsReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *GetRobotUidsReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *GetRobotUidsReq) GetSetid() uint32 {
	if m != nil {
		return m.Setid
	}
	return 0
}

func (m *GetRobotUidsReq) GetRobotCnt() uint32 {
	if m != nil {
		return m.RobotCnt
	}
	return 0
}

type GetRobotUidsRsp struct {
	RobotList            []*RobotInfo `protobuf:"bytes,1,rep,name=robot_list,json=robotList,proto3" json:"robot_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRobotUidsRsp) Reset()         { *m = GetRobotUidsRsp{} }
func (m *GetRobotUidsRsp) String() string { return proto.CompactTextString(m) }
func (*GetRobotUidsRsp) ProtoMessage()    {}
func (*GetRobotUidsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{65}
}
func (m *GetRobotUidsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRobotUidsRsp.Unmarshal(m, b)
}
func (m *GetRobotUidsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRobotUidsRsp.Marshal(b, m, deterministic)
}
func (dst *GetRobotUidsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRobotUidsRsp.Merge(dst, src)
}
func (m *GetRobotUidsRsp) XXX_Size() int {
	return xxx_messageInfo_GetRobotUidsRsp.Size(m)
}
func (m *GetRobotUidsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRobotUidsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRobotUidsRsp proto.InternalMessageInfo

func (m *GetRobotUidsRsp) GetRobotList() []*RobotInfo {
	if m != nil {
		return m.RobotList
	}
	return nil
}

type JoinOutByServerReq struct {
	Gameid               uint32   `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Roomid               uint32   `protobuf:"varint,2,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Bussid               uint32   `protobuf:"varint,4,opt,name=bussid,proto3" json:"bussid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinOutByServerReq) Reset()         { *m = JoinOutByServerReq{} }
func (m *JoinOutByServerReq) String() string { return proto.CompactTextString(m) }
func (*JoinOutByServerReq) ProtoMessage()    {}
func (*JoinOutByServerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{66}
}
func (m *JoinOutByServerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinOutByServerReq.Unmarshal(m, b)
}
func (m *JoinOutByServerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinOutByServerReq.Marshal(b, m, deterministic)
}
func (dst *JoinOutByServerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinOutByServerReq.Merge(dst, src)
}
func (m *JoinOutByServerReq) XXX_Size() int {
	return xxx_messageInfo_JoinOutByServerReq.Size(m)
}
func (m *JoinOutByServerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinOutByServerReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinOutByServerReq proto.InternalMessageInfo

func (m *JoinOutByServerReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *JoinOutByServerReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *JoinOutByServerReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *JoinOutByServerReq) GetBussid() uint32 {
	if m != nil {
		return m.Bussid
	}
	return 0
}

type JoinOutByServerRsp struct {
	ErrorType            GameServerErrorType `protobuf:"varint,1,opt,name=error_type,json=errorType,proto3,enum=gameserver.GameServerErrorType" json:"error_type,omitempty"`
	ErrorMsg             string              `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *JoinOutByServerRsp) Reset()         { *m = JoinOutByServerRsp{} }
func (m *JoinOutByServerRsp) String() string { return proto.CompactTextString(m) }
func (*JoinOutByServerRsp) ProtoMessage()    {}
func (*JoinOutByServerRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{67}
}
func (m *JoinOutByServerRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinOutByServerRsp.Unmarshal(m, b)
}
func (m *JoinOutByServerRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinOutByServerRsp.Marshal(b, m, deterministic)
}
func (dst *JoinOutByServerRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinOutByServerRsp.Merge(dst, src)
}
func (m *JoinOutByServerRsp) XXX_Size() int {
	return xxx_messageInfo_JoinOutByServerRsp.Size(m)
}
func (m *JoinOutByServerRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinOutByServerRsp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinOutByServerRsp proto.InternalMessageInfo

func (m *JoinOutByServerRsp) GetErrorType() GameServerErrorType {
	if m != nil {
		return m.ErrorType
	}
	return GameServerErrorType_GameOk
}

func (m *JoinOutByServerRsp) GetErrorMsg() string {
	if m != nil {
		return m.ErrorMsg
	}
	return ""
}

type UserEscapeReq struct {
	Gameid               uint32   `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Roomid               uint32   `protobuf:"varint,2,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Openid               string   `protobuf:"bytes,3,opt,name=openid,proto3" json:"openid,omitempty"`
	Bussid               uint32   `protobuf:"varint,4,opt,name=bussid,proto3" json:"bussid,omitempty"`
	Setid                string   `protobuf:"bytes,5,opt,name=setid,proto3" json:"setid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserEscapeReq) Reset()         { *m = UserEscapeReq{} }
func (m *UserEscapeReq) String() string { return proto.CompactTextString(m) }
func (*UserEscapeReq) ProtoMessage()    {}
func (*UserEscapeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{68}
}
func (m *UserEscapeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserEscapeReq.Unmarshal(m, b)
}
func (m *UserEscapeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserEscapeReq.Marshal(b, m, deterministic)
}
func (dst *UserEscapeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserEscapeReq.Merge(dst, src)
}
func (m *UserEscapeReq) XXX_Size() int {
	return xxx_messageInfo_UserEscapeReq.Size(m)
}
func (m *UserEscapeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserEscapeReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserEscapeReq proto.InternalMessageInfo

func (m *UserEscapeReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *UserEscapeReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *UserEscapeReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *UserEscapeReq) GetBussid() uint32 {
	if m != nil {
		return m.Bussid
	}
	return 0
}

func (m *UserEscapeReq) GetSetid() string {
	if m != nil {
		return m.Setid
	}
	return ""
}

type UserEscapeRsp struct {
	ErrorType            GameServerErrorType `protobuf:"varint,1,opt,name=error_type,json=errorType,proto3,enum=gameserver.GameServerErrorType" json:"error_type,omitempty"`
	ErrorMsg             string              `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UserEscapeRsp) Reset()         { *m = UserEscapeRsp{} }
func (m *UserEscapeRsp) String() string { return proto.CompactTextString(m) }
func (*UserEscapeRsp) ProtoMessage()    {}
func (*UserEscapeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{69}
}
func (m *UserEscapeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserEscapeRsp.Unmarshal(m, b)
}
func (m *UserEscapeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserEscapeRsp.Marshal(b, m, deterministic)
}
func (dst *UserEscapeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserEscapeRsp.Merge(dst, src)
}
func (m *UserEscapeRsp) XXX_Size() int {
	return xxx_messageInfo_UserEscapeRsp.Size(m)
}
func (m *UserEscapeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserEscapeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_UserEscapeRsp proto.InternalMessageInfo

func (m *UserEscapeRsp) GetErrorType() GameServerErrorType {
	if m != nil {
		return m.ErrorType
	}
	return GameServerErrorType_GameOk
}

func (m *UserEscapeRsp) GetErrorMsg() string {
	if m != nil {
		return m.ErrorMsg
	}
	return ""
}

type CpHeartbeatReq struct {
	Gameid               uint32   `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Roomid               uint32   `protobuf:"varint,2,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Setid                string   `protobuf:"bytes,3,opt,name=setid,proto3" json:"setid,omitempty"`
	Checktime            int64    `protobuf:"varint,4,opt,name=checktime,proto3" json:"checktime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CpHeartbeatReq) Reset()         { *m = CpHeartbeatReq{} }
func (m *CpHeartbeatReq) String() string { return proto.CompactTextString(m) }
func (*CpHeartbeatReq) ProtoMessage()    {}
func (*CpHeartbeatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{70}
}
func (m *CpHeartbeatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CpHeartbeatReq.Unmarshal(m, b)
}
func (m *CpHeartbeatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CpHeartbeatReq.Marshal(b, m, deterministic)
}
func (dst *CpHeartbeatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CpHeartbeatReq.Merge(dst, src)
}
func (m *CpHeartbeatReq) XXX_Size() int {
	return xxx_messageInfo_CpHeartbeatReq.Size(m)
}
func (m *CpHeartbeatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CpHeartbeatReq.DiscardUnknown(m)
}

var xxx_messageInfo_CpHeartbeatReq proto.InternalMessageInfo

func (m *CpHeartbeatReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *CpHeartbeatReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *CpHeartbeatReq) GetSetid() string {
	if m != nil {
		return m.Setid
	}
	return ""
}

func (m *CpHeartbeatReq) GetChecktime() int64 {
	if m != nil {
		return m.Checktime
	}
	return 0
}

type CpHeartbeatRsp struct {
	ErrorType            GameServerErrorType `protobuf:"varint,1,opt,name=error_type,json=errorType,proto3,enum=gameserver.GameServerErrorType" json:"error_type,omitempty"`
	ErrorMsg             string              `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *CpHeartbeatRsp) Reset()         { *m = CpHeartbeatRsp{} }
func (m *CpHeartbeatRsp) String() string { return proto.CompactTextString(m) }
func (*CpHeartbeatRsp) ProtoMessage()    {}
func (*CpHeartbeatRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameserver_1ea4ef309068da82, []int{71}
}
func (m *CpHeartbeatRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CpHeartbeatRsp.Unmarshal(m, b)
}
func (m *CpHeartbeatRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CpHeartbeatRsp.Marshal(b, m, deterministic)
}
func (dst *CpHeartbeatRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CpHeartbeatRsp.Merge(dst, src)
}
func (m *CpHeartbeatRsp) XXX_Size() int {
	return xxx_messageInfo_CpHeartbeatRsp.Size(m)
}
func (m *CpHeartbeatRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_CpHeartbeatRsp.DiscardUnknown(m)
}

var xxx_messageInfo_CpHeartbeatRsp proto.InternalMessageInfo

func (m *CpHeartbeatRsp) GetErrorType() GameServerErrorType {
	if m != nil {
		return m.ErrorType
	}
	return GameServerErrorType_GameOk
}

func (m *CpHeartbeatRsp) GetErrorMsg() string {
	if m != nil {
		return m.ErrorMsg
	}
	return ""
}

func init() {
	proto.RegisterType((*GameStartReq)(nil), "gameserver.GameStartReq")
	proto.RegisterType((*GameStartRsp)(nil), "gameserver.GameStartRsp")
	proto.RegisterType((*UserInfo)(nil), "gameserver.UserInfo")
	proto.RegisterType((*GameStartV2Req)(nil), "gameserver.GameStartV2Req")
	proto.RegisterType((*GameStartV2Rsp)(nil), "gameserver.GameStartV2Rsp")
	proto.RegisterType((*GameResultT)(nil), "gameserver.GameResultT")
	proto.RegisterType((*GameEndReq)(nil), "gameserver.GameEndReq")
	proto.RegisterType((*GameEndRsp)(nil), "gameserver.GameEndRsp")
	proto.RegisterType((*GameEndV2Req)(nil), "gameserver.GameEndV2Req")
	proto.RegisterType((*GameEndV2Rsp)(nil), "gameserver.GameEndV2Rsp")
	proto.RegisterType((*GameEndExceptionReq)(nil), "gameserver.GameEndExceptionReq")
	proto.RegisterType((*GameEndExceptionRsp)(nil), "gameserver.GameEndExceptionRsp")
	proto.RegisterType((*StartRoomGameReq)(nil), "gameserver.StartRoomGameReq")
	proto.RegisterType((*StartRoomGameRsp)(nil), "gameserver.StartRoomGameRsp")
	proto.RegisterType((*EndRoomGameReq)(nil), "gameserver.EndRoomGameReq")
	proto.RegisterType((*EndRoomGameRsp)(nil), "gameserver.EndRoomGameRsp")
	proto.RegisterType((*LoginGameReq)(nil), "gameserver.LoginGameReq")
	proto.RegisterType((*LoginGameRsp)(nil), "gameserver.LoginGameRsp")
	proto.RegisterType((*LogoutGameReq)(nil), "gameserver.LogoutGameReq")
	proto.RegisterType((*LogoutGameRsp)(nil), "gameserver.LogoutGameRsp")
	proto.RegisterType((*JoinGameReq)(nil), "gameserver.JoinGameReq")
	proto.RegisterType((*JoinGameRsp)(nil), "gameserver.JoinGameRsp")
	proto.RegisterType((*JoinOutGameReq)(nil), "gameserver.JoinOutGameReq")
	proto.RegisterType((*JoinOutGameRsp)(nil), "gameserver.JoinOutGameRsp")
	proto.RegisterType((*ReadyGameReq)(nil), "gameserver.ReadyGameReq")
	proto.RegisterType((*ReadyGameRsp)(nil), "gameserver.ReadyGameRsp")
	proto.RegisterType((*ReadyOutGameReq)(nil), "gameserver.ReadyOutGameReq")
	proto.RegisterType((*ReadyOutGameRsp)(nil), "gameserver.ReadyOutGameRsp")
	proto.RegisterType((*VersionReq)(nil), "gameserver.VersionReq")
	proto.RegisterType((*VersionRsp)(nil), "gameserver.VersionRsp")
	proto.RegisterType((*GameInfoReq)(nil), "gameserver.GameInfoReq")
	proto.RegisterType((*GameInfoT)(nil), "gameserver.GameInfoT")
	proto.RegisterType((*GameInfoRsp)(nil), "gameserver.GameInfoRsp")
	proto.RegisterType((*CpCheckReq)(nil), "gameserver.CpCheckReq")
	proto.RegisterType((*CpCheckRsp)(nil), "gameserver.CpCheckRsp")
	proto.RegisterType((*UserInfoSaveReq)(nil), "gameserver.UserInfoSaveReq")
	proto.RegisterType((*UserInfoSaveRsp)(nil), "gameserver.UserInfoSaveRsp")
	proto.RegisterType((*AddGameInfoReq)(nil), "gameserver.AddGameInfoReq")
	proto.RegisterType((*AddGameInfoResp)(nil), "gameserver.AddGameInfoResp")
	proto.RegisterType((*GetGameInfoReq)(nil), "gameserver.GetGameInfoReq")
	proto.RegisterType((*GetGameBaseInfo)(nil), "gameserver.GetGameBaseInfo")
	proto.RegisterType((*GetGameInfoResp)(nil), "gameserver.GetGameInfoResp")
	proto.RegisterType((*DelGameInfoReq)(nil), "gameserver.DelGameInfoReq")
	proto.RegisterType((*DelGameInfoResp)(nil), "gameserver.DelGameInfoResp")
	proto.RegisterType((*ModifyGameInfoReq)(nil), "gameserver.ModifyGameInfoReq")
	proto.RegisterType((*ModifyGameInfoResp)(nil), "gameserver.ModifyGameInfoResp")
	proto.RegisterType((*UpdateVersionInfoReq)(nil), "gameserver.UpdateVersionInfoReq")
	proto.RegisterType((*UpdateVersionInfoResp)(nil), "gameserver.UpdateVersionInfoResp")
	proto.RegisterType((*GetVersionInfoReq)(nil), "gameserver.GetVersionInfoReq")
	proto.RegisterType((*VersionBaseInfo)(nil), "gameserver.VersionBaseInfo")
	proto.RegisterType((*GetVersionInfoResp)(nil), "gameserver.GetVersionInfoResp")
	proto.RegisterType((*DelVersionInfoReq)(nil), "gameserver.DelVersionInfoReq")
	proto.RegisterType((*DelVersionInfoResp)(nil), "gameserver.DelVersionInfoResp")
	proto.RegisterType((*UserPresidentReq)(nil), "gameserver.UserPresidentReq")
	proto.RegisterType((*UserPresidentRsp)(nil), "gameserver.UserPresidentRsp")
	proto.RegisterType((*GetOpenIdByTTidReq)(nil), "gameserver.GetOpenIdByTTidReq")
	proto.RegisterType((*GetOpenIdByTTidInfo)(nil), "gameserver.GetOpenIdByTTidInfo")
	proto.RegisterType((*GetOpenIdByTTidRsp)(nil), "gameserver.GetOpenIdByTTidRsp")
	proto.RegisterType((*GetUidByOpenidInfo)(nil), "gameserver.GetUidByOpenidInfo")
	proto.RegisterType((*GetUidByOpenidReq)(nil), "gameserver.GetUidByOpenidReq")
	proto.RegisterType((*GetUidByOpenidRsp)(nil), "gameserver.GetUidByOpenidRsp")
	proto.RegisterType((*UserExitReq)(nil), "gameserver.UserExitReq")
	proto.RegisterType((*UserExitRsp)(nil), "gameserver.UserExitRsp")
	proto.RegisterType((*RobotInfo)(nil), "gameserver.RobotInfo")
	proto.RegisterType((*GetRobotUidsReq)(nil), "gameserver.GetRobotUidsReq")
	proto.RegisterType((*GetRobotUidsRsp)(nil), "gameserver.GetRobotUidsRsp")
	proto.RegisterType((*JoinOutByServerReq)(nil), "gameserver.JoinOutByServerReq")
	proto.RegisterType((*JoinOutByServerRsp)(nil), "gameserver.JoinOutByServerRsp")
	proto.RegisterType((*UserEscapeReq)(nil), "gameserver.UserEscapeReq")
	proto.RegisterType((*UserEscapeRsp)(nil), "gameserver.UserEscapeRsp")
	proto.RegisterType((*CpHeartbeatReq)(nil), "gameserver.CpHeartbeatReq")
	proto.RegisterType((*CpHeartbeatRsp)(nil), "gameserver.CpHeartbeatRsp")
	proto.RegisterEnum("gameserver.GameServerErrorType", GameServerErrorType_name, GameServerErrorType_value)
	proto.RegisterEnum("gameserver.GameResultType", GameResultType_name, GameResultType_value)
	proto.RegisterEnum("gameserver.GameServerRequestType", GameServerRequestType_name, GameServerRequestType_value)
	proto.RegisterEnum("gameserver.UserLeaveType", UserLeaveType_name, UserLeaveType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GameServerClient is the client API for GameServer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GameServerClient interface {
	GameStart(ctx context.Context, in *GameStartReq, opts ...grpc.CallOption) (*GameStartRsp, error)
	GameStartV2(ctx context.Context, in *GameStartV2Req, opts ...grpc.CallOption) (*GameStartV2Rsp, error)
	GameEnd(ctx context.Context, in *GameEndReq, opts ...grpc.CallOption) (*GameEndRsp, error)
	GameEndV2(ctx context.Context, in *GameEndV2Req, opts ...grpc.CallOption) (*GameEndV2Rsp, error)
	GameEndException(ctx context.Context, in *GameEndExceptionReq, opts ...grpc.CallOption) (*GameEndExceptionRsp, error)
	StartRoomGame(ctx context.Context, in *StartRoomGameReq, opts ...grpc.CallOption) (*StartRoomGameRsp, error)
	EndRoomGame(ctx context.Context, in *EndRoomGameReq, opts ...grpc.CallOption) (*EndRoomGameRsp, error)
	LoginGame(ctx context.Context, in *LoginGameReq, opts ...grpc.CallOption) (*LoginGameRsp, error)
	LogoutGame(ctx context.Context, in *LogoutGameReq, opts ...grpc.CallOption) (*LogoutGameRsp, error)
	JoinGame(ctx context.Context, in *JoinGameReq, opts ...grpc.CallOption) (*JoinGameRsp, error)
	JoinOutGame(ctx context.Context, in *JoinOutGameReq, opts ...grpc.CallOption) (*JoinOutGameRsp, error)
	ReadyGame(ctx context.Context, in *ReadyGameReq, opts ...grpc.CallOption) (*ReadyGameRsp, error)
	ReadyOutGame(ctx context.Context, in *ReadyOutGameReq, opts ...grpc.CallOption) (*ReadyOutGameRsp, error)
	GetGameInfo(ctx context.Context, in *GameInfoReq, opts ...grpc.CallOption) (*GameInfoRsp, error)
	GetVersion(ctx context.Context, in *VersionReq, opts ...grpc.CallOption) (*VersionRsp, error)
	CpCheck(ctx context.Context, in *CpCheckReq, opts ...grpc.CallOption) (*CpCheckRsp, error)
	UserInfoSave(ctx context.Context, in *UserInfoSaveReq, opts ...grpc.CallOption) (*UserInfoSaveRsp, error)
	AddGameInfo(ctx context.Context, in *AddGameInfoReq, opts ...grpc.CallOption) (*AddGameInfoResp, error)
	RequestGameInfo(ctx context.Context, in *GetGameInfoReq, opts ...grpc.CallOption) (*GetGameInfoResp, error)
	DelGameInfo(ctx context.Context, in *DelGameInfoReq, opts ...grpc.CallOption) (*DelGameInfoResp, error)
	ModifyGameInfo(ctx context.Context, in *ModifyGameInfoReq, opts ...grpc.CallOption) (*ModifyGameInfoResp, error)
	UserPresidentNotify(ctx context.Context, in *UserPresidentReq, opts ...grpc.CallOption) (*UserPresidentRsp, error)
	UpdateVersionInfo(ctx context.Context, in *UpdateVersionInfoReq, opts ...grpc.CallOption) (*UpdateVersionInfoResp, error)
	GetVersionInfo(ctx context.Context, in *GetVersionInfoReq, opts ...grpc.CallOption) (*GetVersionInfoResp, error)
	DelVersionInfo(ctx context.Context, in *DelVersionInfoReq, opts ...grpc.CallOption) (*DelVersionInfoResp, error)
	GetOpenidByTTid(ctx context.Context, in *GetOpenIdByTTidReq, opts ...grpc.CallOption) (*GetOpenIdByTTidRsp, error)
	GetUidByOpenid(ctx context.Context, in *GetUidByOpenidReq, opts ...grpc.CallOption) (*GetUidByOpenidRsp, error)
	JoinOutByServer(ctx context.Context, in *JoinOutByServerReq, opts ...grpc.CallOption) (*JoinOutByServerRsp, error)
	UserExit(ctx context.Context, in *UserExitReq, opts ...grpc.CallOption) (*UserExitRsp, error)
	GetRobotUids(ctx context.Context, in *GetRobotUidsReq, opts ...grpc.CallOption) (*GetRobotUidsRsp, error)
	UserEscape(ctx context.Context, in *UserEscapeReq, opts ...grpc.CallOption) (*UserEscapeRsp, error)
	Heatbeat(ctx context.Context, in *CpHeartbeatReq, opts ...grpc.CallOption) (*CpHeartbeatRsp, error)
}

type gameServerClient struct {
	cc *grpc.ClientConn
}

func NewGameServerClient(cc *grpc.ClientConn) GameServerClient {
	return &gameServerClient{cc}
}

func (c *gameServerClient) GameStart(ctx context.Context, in *GameStartReq, opts ...grpc.CallOption) (*GameStartRsp, error) {
	out := new(GameStartRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/GameStart", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) GameStartV2(ctx context.Context, in *GameStartV2Req, opts ...grpc.CallOption) (*GameStartV2Rsp, error) {
	out := new(GameStartV2Rsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/GameStartV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) GameEnd(ctx context.Context, in *GameEndReq, opts ...grpc.CallOption) (*GameEndRsp, error) {
	out := new(GameEndRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/GameEnd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) GameEndV2(ctx context.Context, in *GameEndV2Req, opts ...grpc.CallOption) (*GameEndV2Rsp, error) {
	out := new(GameEndV2Rsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/GameEndV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) GameEndException(ctx context.Context, in *GameEndExceptionReq, opts ...grpc.CallOption) (*GameEndExceptionRsp, error) {
	out := new(GameEndExceptionRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/GameEndException", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) StartRoomGame(ctx context.Context, in *StartRoomGameReq, opts ...grpc.CallOption) (*StartRoomGameRsp, error) {
	out := new(StartRoomGameRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/StartRoomGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) EndRoomGame(ctx context.Context, in *EndRoomGameReq, opts ...grpc.CallOption) (*EndRoomGameRsp, error) {
	out := new(EndRoomGameRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/EndRoomGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) LoginGame(ctx context.Context, in *LoginGameReq, opts ...grpc.CallOption) (*LoginGameRsp, error) {
	out := new(LoginGameRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/LoginGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) LogoutGame(ctx context.Context, in *LogoutGameReq, opts ...grpc.CallOption) (*LogoutGameRsp, error) {
	out := new(LogoutGameRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/LogoutGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) JoinGame(ctx context.Context, in *JoinGameReq, opts ...grpc.CallOption) (*JoinGameRsp, error) {
	out := new(JoinGameRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/JoinGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) JoinOutGame(ctx context.Context, in *JoinOutGameReq, opts ...grpc.CallOption) (*JoinOutGameRsp, error) {
	out := new(JoinOutGameRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/JoinOutGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) ReadyGame(ctx context.Context, in *ReadyGameReq, opts ...grpc.CallOption) (*ReadyGameRsp, error) {
	out := new(ReadyGameRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/ReadyGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) ReadyOutGame(ctx context.Context, in *ReadyOutGameReq, opts ...grpc.CallOption) (*ReadyOutGameRsp, error) {
	out := new(ReadyOutGameRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/ReadyOutGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) GetGameInfo(ctx context.Context, in *GameInfoReq, opts ...grpc.CallOption) (*GameInfoRsp, error) {
	out := new(GameInfoRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/GetGameInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) GetVersion(ctx context.Context, in *VersionReq, opts ...grpc.CallOption) (*VersionRsp, error) {
	out := new(VersionRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/GetVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) CpCheck(ctx context.Context, in *CpCheckReq, opts ...grpc.CallOption) (*CpCheckRsp, error) {
	out := new(CpCheckRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/CpCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) UserInfoSave(ctx context.Context, in *UserInfoSaveReq, opts ...grpc.CallOption) (*UserInfoSaveRsp, error) {
	out := new(UserInfoSaveRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/UserInfoSave", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) AddGameInfo(ctx context.Context, in *AddGameInfoReq, opts ...grpc.CallOption) (*AddGameInfoResp, error) {
	out := new(AddGameInfoResp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/AddGameInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) RequestGameInfo(ctx context.Context, in *GetGameInfoReq, opts ...grpc.CallOption) (*GetGameInfoResp, error) {
	out := new(GetGameInfoResp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/RequestGameInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) DelGameInfo(ctx context.Context, in *DelGameInfoReq, opts ...grpc.CallOption) (*DelGameInfoResp, error) {
	out := new(DelGameInfoResp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/DelGameInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) ModifyGameInfo(ctx context.Context, in *ModifyGameInfoReq, opts ...grpc.CallOption) (*ModifyGameInfoResp, error) {
	out := new(ModifyGameInfoResp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/ModifyGameInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) UserPresidentNotify(ctx context.Context, in *UserPresidentReq, opts ...grpc.CallOption) (*UserPresidentRsp, error) {
	out := new(UserPresidentRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/UserPresidentNotify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) UpdateVersionInfo(ctx context.Context, in *UpdateVersionInfoReq, opts ...grpc.CallOption) (*UpdateVersionInfoResp, error) {
	out := new(UpdateVersionInfoResp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/UpdateVersionInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) GetVersionInfo(ctx context.Context, in *GetVersionInfoReq, opts ...grpc.CallOption) (*GetVersionInfoResp, error) {
	out := new(GetVersionInfoResp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/GetVersionInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) DelVersionInfo(ctx context.Context, in *DelVersionInfoReq, opts ...grpc.CallOption) (*DelVersionInfoResp, error) {
	out := new(DelVersionInfoResp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/DelVersionInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) GetOpenidByTTid(ctx context.Context, in *GetOpenIdByTTidReq, opts ...grpc.CallOption) (*GetOpenIdByTTidRsp, error) {
	out := new(GetOpenIdByTTidRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/GetOpenidByTTid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) GetUidByOpenid(ctx context.Context, in *GetUidByOpenidReq, opts ...grpc.CallOption) (*GetUidByOpenidRsp, error) {
	out := new(GetUidByOpenidRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/GetUidByOpenid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) JoinOutByServer(ctx context.Context, in *JoinOutByServerReq, opts ...grpc.CallOption) (*JoinOutByServerRsp, error) {
	out := new(JoinOutByServerRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/JoinOutByServer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) UserExit(ctx context.Context, in *UserExitReq, opts ...grpc.CallOption) (*UserExitRsp, error) {
	out := new(UserExitRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/UserExit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) GetRobotUids(ctx context.Context, in *GetRobotUidsReq, opts ...grpc.CallOption) (*GetRobotUidsRsp, error) {
	out := new(GetRobotUidsRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/GetRobotUids", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) UserEscape(ctx context.Context, in *UserEscapeReq, opts ...grpc.CallOption) (*UserEscapeRsp, error) {
	out := new(UserEscapeRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/UserEscape", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerClient) Heatbeat(ctx context.Context, in *CpHeartbeatReq, opts ...grpc.CallOption) (*CpHeartbeatRsp, error) {
	out := new(CpHeartbeatRsp)
	err := c.cc.Invoke(ctx, "/gameserver.GameServer/Heatbeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameServerServer is the server API for GameServer service.
type GameServerServer interface {
	GameStart(context.Context, *GameStartReq) (*GameStartRsp, error)
	GameStartV2(context.Context, *GameStartV2Req) (*GameStartV2Rsp, error)
	GameEnd(context.Context, *GameEndReq) (*GameEndRsp, error)
	GameEndV2(context.Context, *GameEndV2Req) (*GameEndV2Rsp, error)
	GameEndException(context.Context, *GameEndExceptionReq) (*GameEndExceptionRsp, error)
	StartRoomGame(context.Context, *StartRoomGameReq) (*StartRoomGameRsp, error)
	EndRoomGame(context.Context, *EndRoomGameReq) (*EndRoomGameRsp, error)
	LoginGame(context.Context, *LoginGameReq) (*LoginGameRsp, error)
	LogoutGame(context.Context, *LogoutGameReq) (*LogoutGameRsp, error)
	JoinGame(context.Context, *JoinGameReq) (*JoinGameRsp, error)
	JoinOutGame(context.Context, *JoinOutGameReq) (*JoinOutGameRsp, error)
	ReadyGame(context.Context, *ReadyGameReq) (*ReadyGameRsp, error)
	ReadyOutGame(context.Context, *ReadyOutGameReq) (*ReadyOutGameRsp, error)
	GetGameInfo(context.Context, *GameInfoReq) (*GameInfoRsp, error)
	GetVersion(context.Context, *VersionReq) (*VersionRsp, error)
	CpCheck(context.Context, *CpCheckReq) (*CpCheckRsp, error)
	UserInfoSave(context.Context, *UserInfoSaveReq) (*UserInfoSaveRsp, error)
	AddGameInfo(context.Context, *AddGameInfoReq) (*AddGameInfoResp, error)
	RequestGameInfo(context.Context, *GetGameInfoReq) (*GetGameInfoResp, error)
	DelGameInfo(context.Context, *DelGameInfoReq) (*DelGameInfoResp, error)
	ModifyGameInfo(context.Context, *ModifyGameInfoReq) (*ModifyGameInfoResp, error)
	UserPresidentNotify(context.Context, *UserPresidentReq) (*UserPresidentRsp, error)
	UpdateVersionInfo(context.Context, *UpdateVersionInfoReq) (*UpdateVersionInfoResp, error)
	GetVersionInfo(context.Context, *GetVersionInfoReq) (*GetVersionInfoResp, error)
	DelVersionInfo(context.Context, *DelVersionInfoReq) (*DelVersionInfoResp, error)
	GetOpenidByTTid(context.Context, *GetOpenIdByTTidReq) (*GetOpenIdByTTidRsp, error)
	GetUidByOpenid(context.Context, *GetUidByOpenidReq) (*GetUidByOpenidRsp, error)
	JoinOutByServer(context.Context, *JoinOutByServerReq) (*JoinOutByServerRsp, error)
	UserExit(context.Context, *UserExitReq) (*UserExitRsp, error)
	GetRobotUids(context.Context, *GetRobotUidsReq) (*GetRobotUidsRsp, error)
	UserEscape(context.Context, *UserEscapeReq) (*UserEscapeRsp, error)
	Heatbeat(context.Context, *CpHeartbeatReq) (*CpHeartbeatRsp, error)
}

func RegisterGameServerServer(s *grpc.Server, srv GameServerServer) {
	s.RegisterService(&_GameServer_serviceDesc, srv)
}

func _GameServer_GameStart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameStartReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).GameStart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/GameStart",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).GameStart(ctx, req.(*GameStartReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_GameStartV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameStartV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).GameStartV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/GameStartV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).GameStartV2(ctx, req.(*GameStartV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_GameEnd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameEndReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).GameEnd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/GameEnd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).GameEnd(ctx, req.(*GameEndReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_GameEndV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameEndV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).GameEndV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/GameEndV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).GameEndV2(ctx, req.(*GameEndV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_GameEndException_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameEndExceptionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).GameEndException(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/GameEndException",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).GameEndException(ctx, req.(*GameEndExceptionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_StartRoomGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartRoomGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).StartRoomGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/StartRoomGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).StartRoomGame(ctx, req.(*StartRoomGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_EndRoomGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EndRoomGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).EndRoomGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/EndRoomGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).EndRoomGame(ctx, req.(*EndRoomGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_LoginGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).LoginGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/LoginGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).LoginGame(ctx, req.(*LoginGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_LogoutGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogoutGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).LogoutGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/LogoutGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).LogoutGame(ctx, req.(*LogoutGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_JoinGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).JoinGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/JoinGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).JoinGame(ctx, req.(*JoinGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_JoinOutGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinOutGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).JoinOutGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/JoinOutGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).JoinOutGame(ctx, req.(*JoinOutGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_ReadyGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadyGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).ReadyGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/ReadyGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).ReadyGame(ctx, req.(*ReadyGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_ReadyOutGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadyOutGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).ReadyOutGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/ReadyOutGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).ReadyOutGame(ctx, req.(*ReadyOutGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_GetGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).GetGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/GetGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).GetGameInfo(ctx, req.(*GameInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_GetVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).GetVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/GetVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).GetVersion(ctx, req.(*VersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_CpCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CpCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).CpCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/CpCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).CpCheck(ctx, req.(*CpCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_UserInfoSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserInfoSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).UserInfoSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/UserInfoSave",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).UserInfoSave(ctx, req.(*UserInfoSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_AddGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddGameInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).AddGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/AddGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).AddGameInfo(ctx, req.(*AddGameInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_RequestGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).RequestGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/RequestGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).RequestGameInfo(ctx, req.(*GetGameInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_DelGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelGameInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).DelGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/DelGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).DelGameInfo(ctx, req.(*DelGameInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_ModifyGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyGameInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).ModifyGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/ModifyGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).ModifyGameInfo(ctx, req.(*ModifyGameInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_UserPresidentNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserPresidentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).UserPresidentNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/UserPresidentNotify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).UserPresidentNotify(ctx, req.(*UserPresidentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_UpdateVersionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateVersionInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).UpdateVersionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/UpdateVersionInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).UpdateVersionInfo(ctx, req.(*UpdateVersionInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_GetVersionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVersionInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).GetVersionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/GetVersionInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).GetVersionInfo(ctx, req.(*GetVersionInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_DelVersionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelVersionInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).DelVersionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/DelVersionInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).DelVersionInfo(ctx, req.(*DelVersionInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_GetOpenidByTTid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOpenIdByTTidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).GetOpenidByTTid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/GetOpenidByTTid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).GetOpenidByTTid(ctx, req.(*GetOpenIdByTTidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_GetUidByOpenid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUidByOpenidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).GetUidByOpenid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/GetUidByOpenid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).GetUidByOpenid(ctx, req.(*GetUidByOpenidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_JoinOutByServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinOutByServerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).JoinOutByServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/JoinOutByServer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).JoinOutByServer(ctx, req.(*JoinOutByServerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_UserExit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserExitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).UserExit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/UserExit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).UserExit(ctx, req.(*UserExitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_GetRobotUids_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRobotUidsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).GetRobotUids(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/GetRobotUids",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).GetRobotUids(ctx, req.(*GetRobotUidsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_UserEscape_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserEscapeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).UserEscape(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/UserEscape",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).UserEscape(ctx, req.(*UserEscapeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServer_Heatbeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CpHeartbeatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerServer).Heatbeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameserver.GameServer/Heatbeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerServer).Heatbeat(ctx, req.(*CpHeartbeatReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GameServer_serviceDesc = grpc.ServiceDesc{
	ServiceName: "gameserver.GameServer",
	HandlerType: (*GameServerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GameStart",
			Handler:    _GameServer_GameStart_Handler,
		},
		{
			MethodName: "GameStartV2",
			Handler:    _GameServer_GameStartV2_Handler,
		},
		{
			MethodName: "GameEnd",
			Handler:    _GameServer_GameEnd_Handler,
		},
		{
			MethodName: "GameEndV2",
			Handler:    _GameServer_GameEndV2_Handler,
		},
		{
			MethodName: "GameEndException",
			Handler:    _GameServer_GameEndException_Handler,
		},
		{
			MethodName: "StartRoomGame",
			Handler:    _GameServer_StartRoomGame_Handler,
		},
		{
			MethodName: "EndRoomGame",
			Handler:    _GameServer_EndRoomGame_Handler,
		},
		{
			MethodName: "LoginGame",
			Handler:    _GameServer_LoginGame_Handler,
		},
		{
			MethodName: "LogoutGame",
			Handler:    _GameServer_LogoutGame_Handler,
		},
		{
			MethodName: "JoinGame",
			Handler:    _GameServer_JoinGame_Handler,
		},
		{
			MethodName: "JoinOutGame",
			Handler:    _GameServer_JoinOutGame_Handler,
		},
		{
			MethodName: "ReadyGame",
			Handler:    _GameServer_ReadyGame_Handler,
		},
		{
			MethodName: "ReadyOutGame",
			Handler:    _GameServer_ReadyOutGame_Handler,
		},
		{
			MethodName: "GetGameInfo",
			Handler:    _GameServer_GetGameInfo_Handler,
		},
		{
			MethodName: "GetVersion",
			Handler:    _GameServer_GetVersion_Handler,
		},
		{
			MethodName: "CpCheck",
			Handler:    _GameServer_CpCheck_Handler,
		},
		{
			MethodName: "UserInfoSave",
			Handler:    _GameServer_UserInfoSave_Handler,
		},
		{
			MethodName: "AddGameInfo",
			Handler:    _GameServer_AddGameInfo_Handler,
		},
		{
			MethodName: "RequestGameInfo",
			Handler:    _GameServer_RequestGameInfo_Handler,
		},
		{
			MethodName: "DelGameInfo",
			Handler:    _GameServer_DelGameInfo_Handler,
		},
		{
			MethodName: "ModifyGameInfo",
			Handler:    _GameServer_ModifyGameInfo_Handler,
		},
		{
			MethodName: "UserPresidentNotify",
			Handler:    _GameServer_UserPresidentNotify_Handler,
		},
		{
			MethodName: "UpdateVersionInfo",
			Handler:    _GameServer_UpdateVersionInfo_Handler,
		},
		{
			MethodName: "GetVersionInfo",
			Handler:    _GameServer_GetVersionInfo_Handler,
		},
		{
			MethodName: "DelVersionInfo",
			Handler:    _GameServer_DelVersionInfo_Handler,
		},
		{
			MethodName: "GetOpenidByTTid",
			Handler:    _GameServer_GetOpenidByTTid_Handler,
		},
		{
			MethodName: "GetUidByOpenid",
			Handler:    _GameServer_GetUidByOpenid_Handler,
		},
		{
			MethodName: "JoinOutByServer",
			Handler:    _GameServer_JoinOutByServer_Handler,
		},
		{
			MethodName: "UserExit",
			Handler:    _GameServer_UserExit_Handler,
		},
		{
			MethodName: "GetRobotUids",
			Handler:    _GameServer_GetRobotUids_Handler,
		},
		{
			MethodName: "UserEscape",
			Handler:    _GameServer_UserEscape_Handler,
		},
		{
			MethodName: "Heatbeat",
			Handler:    _GameServer_Heatbeat_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gameserver/gameserver.proto",
}

func init() {
	proto.RegisterFile("gameserver/gameserver.proto", fileDescriptor_gameserver_1ea4ef309068da82)
}

var fileDescriptor_gameserver_1ea4ef309068da82 = []byte{
	// 3029 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x1b, 0xcb, 0x6e, 0x1c, 0xc7,
	0x91, 0xfb, 0x20, 0x77, 0xb7, 0x96, 0x5c, 0x2e, 0x5b, 0xaf, 0xd5, 0xc8, 0xa2, 0xe8, 0x81, 0x0d,
	0x28, 0x82, 0x21, 0x25, 0x4c, 0x8c, 0xbc, 0x00, 0x21, 0x16, 0x45, 0xd0, 0x76, 0xf4, 0x60, 0x46,
	0x94, 0x0c, 0xf8, 0x10, 0x62, 0x34, 0xd3, 0x22, 0xc7, 0xbb, 0x3b, 0x33, 0x9a, 0x69, 0xca, 0x24,
	0x90, 0x6b, 0x1e, 0xd7, 0xdc, 0x72, 0xcc, 0x29, 0xf9, 0x84, 0x00, 0x41, 0x72, 0x48, 0x0e, 0xf9,
	0x83, 0x20, 0x1f, 0x91, 0x43, 0x9c, 0xc4, 0x79, 0x38, 0x0f, 0x27, 0xa8, 0xee, 0x9e, 0xed, 0xee,
	0x79, 0x2c, 0x25, 0xd3, 0xcb, 0xf8, 0x90, 0x3d, 0x4d, 0x57, 0x75, 0x57, 0x77, 0x3d, 0xba, 0xba,
	0xba, 0xba, 0x16, 0x2e, 0xed, 0xb9, 0x63, 0x9a, 0xd2, 0xe4, 0x19, 0x4d, 0x6e, 0xa8, 0xcf, 0xeb,
	0x71, 0x12, 0xb1, 0x88, 0x80, 0x82, 0xd8, 0x3f, 0xaa, 0xc1, 0xe2, 0x96, 0x3b, 0xa6, 0x0f, 0x98,
	0x9b, 0x30, 0x87, 0x3e, 0x25, 0x67, 0x61, 0x3e, 0xa5, 0x2c, 0xf0, 0x07, 0xb5, 0xb5, 0xda, 0xd5,
	0x8e, 0x23, 0x1a, 0x84, 0x40, 0xd3, 0x8b, 0x03, 0x7f, 0x50, 0x5f, 0xab, 0x5d, 0x5d, 0x72, 0xf8,
	0x37, 0x39, 0x0f, 0x0b, 0x48, 0x28, 0xf0, 0x07, 0x0d, 0x0e, 0x95, 0x2d, 0x84, 0x27, 0x51, 0x34,
	0x0e, 0xfc, 0x41, 0x53, 0xc0, 0x45, 0x0b, 0x29, 0xbb, 0x31, 0x12, 0x99, 0xe7, 0x60, 0xd1, 0x20,
	0x03, 0x68, 0x45, 0x31, 0x0d, 0x03, 0x3f, 0x1d, 0x2c, 0xac, 0x35, 0xae, 0x76, 0x9c, 0xac, 0x69,
	0x0f, 0xf5, 0x95, 0xa5, 0x31, 0xb9, 0x09, 0x40, 0x93, 0x24, 0x4a, 0x76, 0xd9, 0x51, 0x4c, 0xf9,
	0xf2, 0x7a, 0xeb, 0x57, 0xae, 0x6b, 0xdc, 0xf1, 0xde, 0xfc, 0x73, 0x13, 0xfb, 0xed, 0x1c, 0xc5,
	0xd4, 0xe9, 0xd0, 0xec, 0x93, 0x5c, 0x02, 0xd1, 0xd8, 0x1d, 0xa7, 0x7b, 0x9c, 0x91, 0x8e, 0xd3,
	0xe6, 0x80, 0xbb, 0xe9, 0x9e, 0xfd, 0x1d, 0x68, 0x3f, 0x4c, 0x69, 0xf2, 0x56, 0xf8, 0x24, 0x42,
	0x06, 0xc4, 0x1a, 0xa4, 0x0c, 0x64, 0x8b, 0x58, 0xd0, 0x0e, 0x03, 0x6f, 0x18, 0xba, 0x63, 0x9a,
	0x8d, 0xcf, 0xda, 0xa4, 0x0f, 0x8d, 0x94, 0x1e, 0x72, 0x49, 0x74, 0x1c, 0xfc, 0x44, 0xc6, 0xf6,
	0xa9, 0xeb, 0x1f, 0x24, 0x23, 0x2e, 0x87, 0x8e, 0x93, 0x35, 0xb1, 0xef, 0xc1, 0x44, 0x0c, 0xf8,
	0x69, 0xff, 0xa6, 0x06, 0xbd, 0x09, 0xaf, 0x8f, 0xd6, 0xff, 0x17, 0x7a, 0xf8, 0x3c, 0xb4, 0x0f,
	0xa4, 0x00, 0xb8, 0x22, 0xba, 0xeb, 0x67, 0x75, 0xd9, 0x66, 0xc2, 0x71, 0x26, 0xbd, 0x70, 0x2d,
	0xe3, 0xc8, 0xa7, 0x83, 0x16, 0x5f, 0x20, 0xff, 0xb6, 0xc7, 0x26, 0x1f, 0xb3, 0xd6, 0xda, 0xcf,
	0x6b, 0xd0, 0xc5, 0xf1, 0x0e, 0x4d, 0x0f, 0x46, 0x6c, 0xa7, 0x52, 0x73, 0x28, 0x4c, 0x2f, 0x4a,
	0xa8, 0x94, 0x9b, 0x68, 0x20, 0x34, 0x48, 0xdf, 0x0f, 0x42, 0x2e, 0xb7, 0xb6, 0x23, 0x1a, 0xe4,
	0x02, 0xb4, 0x18, 0x75, 0xc7, 0xbb, 0x4a, 0x6e, 0xd8, 0x7c, 0xcb, 0xc7, 0x95, 0x70, 0x84, 0x4f,
	0x53, 0x8f, 0xcb, 0xae, 0xe3, 0xb4, 0x11, 0x70, 0x9b, 0xa6, 0x1e, 0x22, 0x51, 0x30, 0x02, 0xb9,
	0x20, 0x90, 0x08, 0xe0, 0x48, 0x9c, 0x9e, 0xb9, 0x4c, 0x88, 0x0a, 0xa7, 0xc7, 0x86, 0xfd, 0xd3,
	0x3a, 0x00, 0x2e, 0x7e, 0x33, 0xf4, 0x67, 0xab, 0xf0, 0xaf, 0x40, 0x17, 0x7b, 0xec, 0x26, 0x5c,
	0x4a, 0x83, 0x79, 0xae, 0xdd, 0x0b, 0x79, 0x1d, 0x48, 0x19, 0x3a, 0xdc, 0x3b, 0x88, 0x06, 0xf9,
	0x1a, 0x80, 0x18, 0x84, 0xaa, 0xe0, 0x6c, 0xf5, 0xd6, 0xad, 0x8a, 0x81, 0xa8, 0x37, 0xad, 0x37,
	0xae, 0xfc, 0x20, 0x0c, 0x58, 0x66, 0x1e, 0xf8, 0xad, 0x04, 0xd1, 0xd6, 0x04, 0x41, 0xae, 0x40,
	0x57, 0x8c, 0x13, 0xd2, 0xeb, 0xf0, 0x01, 0x92, 0x14, 0xca, 0xcf, 0x0e, 0x94, 0xa0, 0x66, 0x6d,
	0x51, 0x3f, 0xae, 0x0b, 0xaf, 0xb3, 0x19, 0xfa, 0xb3, 0xde, 0x87, 0x9f, 0x75, 0xb5, 0x9c, 0x87,
	0x85, 0x94, 0x7a, 0x09, 0x65, 0x52, 0x23, 0xb2, 0x95, 0xf9, 0x65, 0x21, 0xa1, 0x59, 0xeb, 0x23,
	0x82, 0x33, 0x72, 0xb2, 0xcd, 0x43, 0x8f, 0xc6, 0x2c, 0x88, 0xc2, 0x99, 0x6a, 0xc5, 0x4e, 0x4a,
	0x26, 0x9c, 0x35, 0x93, 0x1f, 0xd6, 0xa0, 0x2f, 0x8e, 0xb9, 0x28, 0x1a, 0x0b, 0xdd, 0x3d, 0xad,
	0xf4, 0x65, 0x8a, 0xa1, 0x7a, 0x05, 0x43, 0x8d, 0x72, 0x77, 0xdf, 0xd4, 0xdd, 0xfd, 0x15, 0xe8,
	0x06, 0x61, 0xc0, 0x02, 0x77, 0xb4, 0xab, 0xce, 0x22, 0x90, 0xa0, 0x87, 0x81, 0x4f, 0x5e, 0x85,
	0x9e, 0x77, 0x90, 0x24, 0x34, 0x64, 0xbb, 0xcf, 0x68, 0x12, 0x44, 0xa1, 0xf4, 0x6a, 0x4b, 0x12,
	0xfa, 0x88, 0x03, 0x71, 0xd6, 0xc7, 0x07, 0x69, 0x1a, 0xf8, 0xd2, 0xb7, 0xc9, 0x16, 0xb1, 0x61,
	0x89, 0x1b, 0x77, 0x3c, 0xdc, 0xc5, 0x83, 0x61, 0xc4, 0x4d, 0xab, 0xe3, 0x70, 0x8b, 0xdf, 0x1e,
	0xde, 0x45, 0x90, 0x1d, 0xe5, 0xb9, 0x9e, 0xb5, 0x9c, 0xbf, 0x57, 0x83, 0x1e, 0x3a, 0x91, 0x53,
	0x93, 0xb2, 0x92, 0xce, 0xbc, 0x2e, 0x1d, 0x3c, 0x26, 0xf5, 0x75, 0xcc, 0x9a, 0xef, 0x11, 0x2c,
	0xde, 0x89, 0xf6, 0x82, 0xf0, 0x54, 0x98, 0xb6, 0x7f, 0x5f, 0xd3, 0xa7, 0x4b, 0x63, 0xbe, 0x2d,
	0x31, 0x50, 0xc0, 0xc9, 0xe6, 0x1d, 0xfe, 0x8d, 0x31, 0x90, 0x5a, 0x29, 0x7e, 0x22, 0x07, 0x23,
	0x1c, 0xb5, 0xeb, 0x85, 0x4c, 0xce, 0xd3, 0xe6, 0x80, 0x8d, 0x90, 0x91, 0x8b, 0xd0, 0x7e, 0x2f,
	0x92, 0x38, 0x31, 0x59, 0x0b, 0xdb, 0x88, 0x5a, 0x83, 0x45, 0x8e, 0x1a, 0xbb, 0x87, 0x1c, 0x2d,
	0x4d, 0x19, 0x61, 0x77, 0xdd, 0x43, 0xec, 0x61, 0xca, 0x76, 0xe1, 0x64, 0xb2, 0x6d, 0xe5, 0x64,
	0x3b, 0x86, 0xa5, 0x3b, 0xd1, 0x5e, 0x74, 0xc0, 0x4e, 0x47, 0xb8, 0xf7, 0x8d, 0xe9, 0x4e, 0x6e,
	0x38, 0xf6, 0x10, 0xba, 0x6f, 0x47, 0xa7, 0x65, 0x1a, 0xef, 0x69, 0x93, 0xcd, 0xda, 0xe8, 0x43,
	0xe8, 0xe1, 0x5c, 0xf7, 0x4f, 0x4b, 0x33, 0x63, 0x73, 0xbe, 0x53, 0xd8, 0xd3, 0x0e, 0x75, 0xfd,
	0xa3, 0xd3, 0x61, 0x6e, 0xa8, 0xcf, 0x36, 0xfb, 0x33, 0x7f, 0x99, 0x4f, 0x76, 0x6a, 0xaa, 0x0b,
	0x73, 0x13, 0xce, 0x9a, 0xc1, 0x9b, 0x00, 0x8f, 0x68, 0x92, 0xca, 0x58, 0x46, 0xf1, 0x50, 0x33,
	0x78, 0x18, 0x40, 0xeb, 0x99, 0xe8, 0x25, 0x09, 0x64, 0x4d, 0xfb, 0xd7, 0x75, 0x45, 0x20, 0x8d,
	0x91, 0x40, 0x1c, 0x78, 0x78, 0xd1, 0x94, 0xc2, 0x11, 0x2d, 0x24, 0x80, 0xa4, 0x10, 0x21, 0x09,
	0xc8, 0x26, 0x79, 0x05, 0x96, 0x68, 0xb8, 0x17, 0x84, 0x34, 0x9b, 0x40, 0x48, 0xc9, 0x04, 0x92,
	0x35, 0xe8, 0xc6, 0xae, 0x37, 0x74, 0xf7, 0xe8, 0xb6, 0xcb, 0xf6, 0xe5, 0x2d, 0x56, 0x07, 0x91,
	0x55, 0x00, 0x31, 0x84, 0x4b, 0x49, 0x7a, 0x5e, 0x05, 0xe1, 0x6a, 0xa0, 0x29, 0x2e, 0x40, 0x04,
	0x0f, 0xb2, 0xc5, 0xe7, 0x3f, 0x64, 0x89, 0x1b, 0x27, 0x51, 0x4c, 0x13, 0x76, 0x24, 0xbd, 0xaa,
	0x09, 0x44, 0xea, 0x72, 0xb2, 0xbb, 0xb7, 0x5f, 0x97, 0x01, 0x84, 0x06, 0x91, 0xd4, 0x11, 0xd7,
	0x99, 0x50, 0x47, 0xf8, 0x2b, 0xb0, 0xc4, 0x23, 0x58, 0x46, 0xc7, 0xf1, 0x08, 0xc3, 0x5a, 0x10,
	0xd4, 0x0d, 0xa0, 0xfd, 0xaa, 0xb8, 0x3a, 0xf2, 0x4b, 0xad, 0xd0, 0x82, 0x3c, 0xaa, 0x6b, 0xc6,
	0x51, 0xfd, 0xbb, 0x26, 0x74, 0xb2, 0x7e, 0x3b, 0x93, 0x08, 0xb3, 0x56, 0x1a, 0x61, 0x9a, 0x36,
	0x68, 0x41, 0x1b, 0xbf, 0x78, 0xba, 0x40, 0xe4, 0x05, 0x26, 0xed, 0x0c, 0xc7, 0xc5, 0x26, 0x4c,
	0x71, 0xd2, 0x46, 0xb1, 0xe3, 0x77, 0xa6, 0x9a, 0x79, 0x15, 0x38, 0x65, 0x8a, 0xb9, 0x00, 0xad,
	0x38, 0xf0, 0x76, 0x35, 0xb9, 0xc6, 0x81, 0xf7, 0x30, 0x19, 0xe1, 0x31, 0xc9, 0xa3, 0x2e, 0xc4,
	0xb4, 0x94, 0xca, 0x11, 0x75, 0x39, 0x53, 0x15, 0x86, 0x73, 0x32, 0xd0, 0xef, 0x08, 0xc8, 0x23,
	0x9a, 0x90, 0x97, 0x61, 0x51, 0x4a, 0x76, 0x37, 0x46, 0x65, 0x77, 0x8a, 0xca, 0xb6, 0x61, 0x09,
	0xcf, 0x58, 0x75, 0x48, 0x03, 0x27, 0xd2, 0x1d, 0xbb, 0x87, 0x77, 0xb2, 0x73, 0xfa, 0x3c, 0x2c,
	0xf0, 0x2d, 0x95, 0x0e, 0xba, 0x6b, 0x0d, 0x94, 0x85, 0x68, 0x21, 0xbf, 0x28, 0xf5, 0x27, 0x51,
	0x32, 0x1e, 0x2c, 0x72, 0xcc, 0xa4, 0x8d, 0xa1, 0xa8, 0x5c, 0x19, 0x17, 0xc7, 0x92, 0x6e, 0x45,
	0x3b, 0xa6, 0x15, 0xf5, 0x0c, 0x2b, 0xba, 0x0c, 0xb0, 0x97, 0xb8, 0x47, 0xbb, 0xde, 0x3e, 0x4e,
	0xb8, 0xcc, 0xc9, 0x76, 0x10, 0xb2, 0x81, 0x80, 0xa2, 0x91, 0xf5, 0x8f, 0x37, 0xb2, 0x95, 0x29,
	0x46, 0x46, 0xa6, 0x1b, 0xd9, 0x99, 0x12, 0x23, 0x43, 0xbe, 0x31, 0xfc, 0x0d, 0xc2, 0x27, 0xd1,
	0xe0, 0xac, 0xb0, 0x81, 0xac, 0x6d, 0x7f, 0x43, 0x33, 0xc0, 0x34, 0x26, 0x5f, 0x10, 0xba, 0xe3,
	0x5d, 0x6b, 0xfc, 0x2e, 0x78, 0x2e, 0xef, 0x6f, 0xb8, 0x0d, 0x3a, 0x93, 0x6e, 0xf6, 0x36, 0xc0,
	0x46, 0xbc, 0xb1, 0x4f, 0xbd, 0x21, 0x5a, 0xf0, 0x8b, 0xd8, 0xa6, 0xba, 0xdb, 0x35, 0x8c, 0xbb,
	0xdd, 0xaa, 0xa2, 0x98, 0xc6, 0x18, 0xa4, 0xed, 0x07, 0x8c, 0x13, 0x6c, 0x3b, 0xf8, 0x69, 0xff,
	0xaa, 0x06, 0xcb, 0x59, 0x2a, 0xe8, 0x81, 0xfb, 0x8c, 0xbe, 0xe8, 0xbc, 0x13, 0xff, 0xdb, 0xd0,
	0xc3, 0x64, 0x99, 0x10, 0x6b, 0x4e, 0x12, 0x62, 0x9a, 0xbf, 0x9f, 0xcf, 0xfb, 0x7b, 0xe9, 0xd7,
	0x17, 0x0c, 0xbf, 0x4e, 0xa0, 0xc9, 0x98, 0xbc, 0x84, 0x74, 0x1c, 0xfe, 0xad, 0xed, 0xe8, 0xb6,
	0xb1, 0xa3, 0x3f, 0x97, 0x63, 0x41, 0x78, 0xd0, 0xb2, 0xe3, 0xc5, 0xfe, 0x65, 0x03, 0x7a, 0x6f,
	0xf8, 0xbe, 0xee, 0x27, 0xca, 0xb8, 0xd5, 0x77, 0x7a, 0x3d, 0xb7, 0xd3, 0xd7, 0x00, 0x37, 0x08,
	0xdf, 0x34, 0x2a, 0xb0, 0xd5, 0x41, 0x68, 0x81, 0x63, 0xf7, 0x10, 0xe3, 0x55, 0x15, 0xdd, 0x6a,
	0x10, 0x49, 0x21, 0xc1, 0x03, 0x4a, 0xc5, 0xb7, 0x3a, 0x08, 0xa5, 0xea, 0xc5, 0xca, 0x1b, 0x88,
	0x86, 0xa6, 0x83, 0x56, 0xd5, 0xb9, 0xd2, 0x36, 0xce, 0x95, 0xdc, 0x86, 0xea, 0xe4, 0x37, 0xd4,
	0x6b, 0xb0, 0x82, 0x24, 0xcc, 0x4d, 0x25, 0x7c, 0x6b, 0x11, 0x91, 0xf5, 0x36, 0x37, 0x49, 0x57,
	0xf5, 0x36, 0x37, 0x8a, 0x52, 0xd6, 0xa2, 0x71, 0x8f, 0x5c, 0x83, 0x6e, 0x42, 0x9f, 0x1e, 0xd0,
	0x94, 0x69, 0xce, 0x41, 0x07, 0x19, 0x5b, 0xac, 0x97, 0xdb, 0x62, 0x2b, 0xb0, 0x6c, 0xa8, 0x2f,
	0x8d, 0x6d, 0x1f, 0x7a, 0x5b, 0x94, 0x1d, 0xa7, 0xd1, 0x2a, 0xfb, 0x25, 0xd0, 0x8c, 0xdd, 0x3d,
	0x2a, 0xd5, 0xc8, 0xbf, 0x11, 0x96, 0x44, 0xef, 0xa7, 0x52, 0x73, 0xfc, 0xdb, 0xfe, 0x45, 0x03,
	0x96, 0xe5, 0x34, 0xb7, 0xdc, 0x94, 0x66, 0xf9, 0xd2, 0x4f, 0xe5, 0xec, 0xd0, 0xf4, 0xd7, 0x34,
	0xf5, 0xb7, 0x0a, 0x32, 0xb5, 0xcf, 0x77, 0xb6, 0xd8, 0x39, 0x1a, 0x24, 0x6f, 0x8b, 0x0b, 0xc7,
	0xd9, 0x62, 0xeb, 0x38, 0x5b, 0x6c, 0x4f, 0xb1, 0xc5, 0x8e, 0x6e, 0x8b, 0xa6, 0x65, 0xc1, 0x73,
	0x59, 0x56, 0xf7, 0x85, 0x2c, 0x6b, 0xf1, 0x78, 0xcb, 0x5a, 0x32, 0x2c, 0x6b, 0x9a, 0xdd, 0x7c,
	0x7b, 0xa2, 0xbd, 0xcc, 0x6e, 0xc8, 0x0d, 0x68, 0x6a, 0xae, 0xf9, 0x92, 0xe1, 0x9a, 0x4d, 0x45,
	0x3b, 0xbc, 0x23, 0xd2, 0x67, 0x11, 0x73, 0x47, 0xe1, 0xc1, 0x58, 0x2a, 0x77, 0xd2, 0xb6, 0xaf,
	0x42, 0xef, 0x36, 0x1d, 0xe5, 0xc2, 0x8f, 0xb2, 0x20, 0x10, 0x2d, 0xd8, 0xe8, 0x99, 0xc6, 0xf6,
	0x07, 0x75, 0x58, 0xb9, 0x1b, 0xf9, 0xc1, 0x93, 0xa3, 0xe7, 0x20, 0xf0, 0x19, 0xf5, 0x4d, 0xa6,
	0x3d, 0xb4, 0x9e, 0xcb, 0x1e, 0xda, 0x2f, 0x64, 0x0f, 0x9d, 0xe3, 0xed, 0x01, 0x2a, 0xed, 0xa1,
	0x9b, 0xb3, 0x87, 0xb3, 0x40, 0xf2, 0x12, 0x4f, 0x63, 0xfb, 0xe3, 0x3a, 0x9c, 0x7d, 0x18, 0xfb,
	0x2e, 0xa3, 0x32, 0x18, 0x3f, 0x4e, 0x17, 0x95, 0x11, 0xfd, 0x73, 0x06, 0xe4, 0x66, 0xb8, 0xdd,
	0x2c, 0x84, 0xdb, 0x2a, 0x60, 0xe7, 0x31, 0xdc, 0xbc, 0x11, 0xc3, 0x21, 0x48, 0xbb, 0x2a, 0x2c,
	0x54, 0x5d, 0x15, 0x5a, 0xe6, 0x55, 0x41, 0x05, 0x5f, 0xed, 0xe9, 0x21, 0x7c, 0xe7, 0xf8, 0xe8,
	0x0a, 0xa6, 0x44, 0x57, 0xdd, 0xe9, 0xd1, 0xd5, 0x62, 0x59, 0x08, 0x7f, 0x01, 0xce, 0x95, 0xc8,
	0x3f, 0x8d, 0xed, 0x07, 0xb0, 0xb2, 0x45, 0xd9, 0x73, 0x6a, 0x25, 0xf3, 0xe9, 0xf5, 0x12, 0x9f,
	0xde, 0xd0, 0x7c, 0xfa, 0x47, 0x75, 0x58, 0x96, 0x24, 0x27, 0x3e, 0xfd, 0xff, 0x9a, 0x3e, 0x1d,
	0x4d, 0xbb, 0x40, 0xf2, 0x0a, 0x9d, 0xee, 0x93, 0x73, 0x8a, 0x7a, 0x0e, 0x9f, 0xbc, 0x09, 0x2b,
	0xb7, 0xe9, 0xe8, 0xa4, 0x3b, 0x19, 0x5d, 0x45, 0x9e, 0x4c, 0x1a, 0xdb, 0xef, 0x42, 0x1f, 0x63,
	0xce, 0xed, 0x84, 0xa6, 0x81, 0x4f, 0x43, 0x36, 0x8d, 0xb6, 0x8a, 0x71, 0xeb, 0x46, 0x8c, 0xab,
	0x82, 0xd4, 0x86, 0x11, 0xa4, 0xfe, 0xac, 0x96, 0x27, 0x2e, 0x22, 0xda, 0x4f, 0x83, 0x78, 0x2e,
	0x0f, 0xd2, 0x3c, 0x59, 0x1e, 0x64, 0x3e, 0x97, 0x07, 0xb9, 0xca, 0xb5, 0x7a, 0x3f, 0xa6, 0xe1,
	0x5b, 0xfe, 0xad, 0xa3, 0x9d, 0x9d, 0xc0, 0x97, 0xf1, 0x18, 0x53, 0x4f, 0x3b, 0xfc, 0xdb, 0xde,
	0x84, 0x33, 0xb9, 0x9e, 0x53, 0x5f, 0xea, 0x2b, 0xc2, 0x2a, 0xfb, 0x9b, 0xc5, 0x09, 0xd3, 0x98,
	0xbc, 0x0e, 0xf3, 0x68, 0x1d, 0xa9, 0xb4, 0xa3, 0x2b, 0xb9, 0xb3, 0x3d, 0x3f, 0xab, 0x23, 0x7a,
	0xdb, 0x37, 0x39, 0xb1, 0x87, 0x81, 0x7f, 0xeb, 0xe8, 0x3e, 0x9f, 0x76, 0xea, 0x92, 0xe4, 0x1d,
	0xa7, 0xae, 0x1e, 0xfd, 0x0f, 0xb8, 0x93, 0xd2, 0xc6, 0x1f, 0x63, 0x14, 0x92, 0x6c, 0xbd, 0xe2,
	0x42, 0x64, 0x26, 0xba, 0xb4, 0xb2, 0x8a, 0xa6, 0x59, 0x56, 0xf1, 0x93, 0x5a, 0x61, 0xde, 0x34,
	0x26, 0x5f, 0x32, 0x65, 0xb0, 0x9a, 0x93, 0x41, 0x8e, 0x4b, 0x29, 0x82, 0x9c, 0x75, 0xd4, 0x4f,
	0x66, 0x1d, 0x8d, 0x9c, 0x75, 0xfc, 0xa0, 0x06, 0x5d, 0xb4, 0xeb, 0xcd, 0xc3, 0x80, 0xef, 0x97,
	0x2f, 0x43, 0x67, 0x44, 0xdd, 0x67, 0x3c, 0x27, 0x20, 0x33, 0x72, 0x17, 0xf3, 0x25, 0x0a, 0x77,
	0xb2, 0x0e, 0x8e, 0xea, 0x5b, 0x14, 0x7d, 0xa5, 0xd4, 0x54, 0x6c, 0xd0, 0x34, 0xae, 0x8c, 0xef,
	0x69, 0x2b, 0x99, 0x75, 0x72, 0x70, 0x08, 0x1d, 0x27, 0x7a, 0x1c, 0xb1, 0xd3, 0x28, 0x45, 0xb1,
	0x19, 0x0f, 0x74, 0xf9, 0x7c, 0x0f, 0x03, 0x3f, 0xfd, 0x24, 0x6e, 0x69, 0xf2, 0x14, 0x2b, 0xaf,
	0xf4, 0xe2, 0x29, 0xf6, 0x12, 0x74, 0x12, 0xa4, 0xaa, 0xbd, 0xd8, 0xb4, 0x39, 0x60, 0x23, 0x64,
	0xf6, 0x56, 0x6e, 0x56, 0x6e, 0x7f, 0x20, 0xfa, 0x8f, 0x82, 0x94, 0x95, 0xe5, 0x3f, 0x26, 0x32,
	0x71, 0x04, 0xe1, 0x3b, 0x41, 0xca, 0xec, 0x10, 0x88, 0xcc, 0xb9, 0xdf, 0x3a, 0x12, 0xf2, 0xfe,
	0x24, 0x1c, 0x48, 0xfb, 0x68, 0x18, 0xf6, 0x51, 0x6a, 0x07, 0x4f, 0x8b, 0xf3, 0xcd, 0xda, 0x1c,
	0xbe, 0x5b, 0x83, 0x25, 0x6e, 0x7b, 0xa9, 0xe7, 0xc6, 0xf4, 0x53, 0x3c, 0x37, 0xaa, 0x98, 0x54,
	0x0a, 0x9d, 0xd7, 0xde, 0xd6, 0xed, 0x91, 0xb1, 0x8c, 0x59, 0x73, 0xcd, 0xa0, 0xb7, 0x11, 0xbf,
	0x49, 0xdd, 0x84, 0x3d, 0xa6, 0x2e, 0x3b, 0xb1, 0x59, 0x4e, 0x2a, 0x04, 0x5e, 0x82, 0x8e, 0xb7,
	0x4f, 0xbd, 0x21, 0x0b, 0xc6, 0xe2, 0x34, 0x6b, 0x38, 0x0a, 0x60, 0x8f, 0xcd, 0x59, 0x67, 0xcc,
	0xe4, 0xb5, 0x1f, 0x36, 0x45, 0xad, 0x41, 0x6e, 0x3c, 0x01, 0x58, 0x40, 0xf0, 0xfd, 0x61, 0x7f,
	0x8e, 0x5c, 0x14, 0xc5, 0x16, 0xf7, 0x22, 0xc6, 0x9f, 0xca, 0xfb, 0xff, 0xc9, 0x7e, 0x35, 0x72,
	0x19, 0xfa, 0x88, 0xda, 0xa6, 0x51, 0x3c, 0xa2, 0x9b, 0x87, 0x1e, 0xa5, 0x7e, 0xff, 0x63, 0x85,
	0x56, 0x23, 0x79, 0x76, 0xb6, 0xff, 0x6f, 0x85, 0xba, 0x02, 0x44, 0xa2, 0x1c, 0x1a, 0x47, 0x09,
	0xe3, 0x8f, 0x1f, 0xfd, 0x7f, 0x15, 0xc6, 0xee, 0x44, 0x43, 0x1a, 0x6e, 0x26, 0x49, 0xff, 0x9f,
	0x0a, 0x35, 0x10, 0x59, 0xcb, 0x8d, 0xc8, 0xa7, 0x88, 0xf9, 0x87, 0xc2, 0x58, 0xb0, 0x24, 0xb8,
	0xf1, 0x12, 0xca, 0x10, 0xf7, 0x91, 0xc2, 0xbd, 0x04, 0xcb, 0xf2, 0x29, 0x67, 0xdb, 0x4d, 0xdc,
	0x31, 0x62, 0xff, 0x5e, 0x18, 0x79, 0x2f, 0x62, 0x1b, 0x51, 0xf8, 0x24, 0xd8, 0xeb, 0xff, 0xad,
	0x30, 0xf2, 0x5e, 0xc4, 0xb6, 0x12, 0xf7, 0xc8, 0x89, 0xa2, 0x71, 0xff, 0xaf, 0xe6, 0x48, 0x7e,
	0x3a, 0xf1, 0x9a, 0x8d, 0x24, 0xe9, 0xff, 0xa5, 0x04, 0x87, 0x83, 0x10, 0xf7, 0x61, 0x19, 0xd5,
	0xcd, 0xc3, 0x20, 0xe5, 0xab, 0xfd, 0xb3, 0xc2, 0x9e, 0x97, 0x29, 0x7f, 0x1f, 0xe1, 0x7f, 0x2a,
	0xc8, 0x8d, 0x8b, 0xc5, 0xe1, 0x66, 0x86, 0x1d, 0xfe, 0x58, 0xde, 0x61, 0x8b, 0xdb, 0x27, 0x76,
	0xf8, 0xa0, 0xd0, 0x61, 0x23, 0x0e, 0xfc, 0x75, 0xd5, 0xe1, 0x0f, 0x93, 0x0e, 0xd7, 0x12, 0x51,
	0x40, 0xa7, 0x8a, 0x77, 0x48, 0x17, 0x5a, 0x08, 0x79, 0x27, 0x08, 0xfb, 0x73, 0x59, 0x63, 0x27,
	0xa0, 0xfd, 0x1a, 0x59, 0x84, 0x36, 0xef, 0xeb, 0x86, 0xc3, 0x7e, 0x9d, 0x9c, 0x15, 0xe6, 0xf0,
	0x4e, 0x10, 0xbe, 0x13, 0xb0, 0xfd, 0x07, 0x5e, 0x94, 0xd0, 0x7e, 0x23, 0x83, 0xee, 0x04, 0x54,
	0x41, 0x9b, 0xd9, 0xc8, 0xfb, 0xcf, 0x68, 0xd2, 0x9f, 0xbf, 0xf6, 0xfd, 0x1a, 0x9c, 0x53, 0x76,
	0xe8, 0x88, 0xdc, 0x1a, 0x9f, 0xfb, 0x82, 0x30, 0x50, 0x0d, 0x74, 0x2f, 0x0a, 0x69, 0x7f, 0x8e,
	0x9c, 0x17, 0x7c, 0x68, 0x88, 0x37, 0x7c, 0xbf, 0x8f, 0x86, 0x73, 0x2e, 0x07, 0x17, 0xf7, 0xe6,
	0x7e, 0x9d, 0xd8, 0xb0, 0x9a, 0x43, 0x4d, 0xde, 0xc0, 0x46, 0x2e, 0x0b, 0xa2, 0xb0, 0xdf, 0xb8,
	0xf6, 0x55, 0xe1, 0x64, 0x26, 0xa7, 0x38, 0x39, 0x23, 0x72, 0xb5, 0x0f, 0x82, 0x71, 0x3c, 0xa2,
	0x1c, 0xdc, 0x9f, 0xcb, 0x80, 0x9b, 0x87, 0x71, 0x90, 0x48, 0x60, 0x6d, 0xfd, 0xb7, 0x44, 0x14,
	0x89, 0x09, 0x26, 0xc8, 0x1b, 0x42, 0x85, 0x7c, 0xd3, 0x90, 0x41, 0x61, 0xc7, 0xca, 0x6a, 0x57,
	0xab, 0x02, 0x93, 0xc6, 0xf6, 0x1c, 0xd9, 0x12, 0x96, 0x2e, 0x6b, 0x19, 0x89, 0x55, 0xda, 0x95,
	0x17, 0x89, 0x59, 0x95, 0x38, 0x4e, 0xe8, 0xeb, 0x42, 0x69, 0x9b, 0xa1, 0x4f, 0xce, 0xe7, 0x3b,
	0x8a, 0xe2, 0x3f, 0xab, 0x14, 0xce, 0x07, 0x4b, 0x46, 0x78, 0xb5, 0x55, 0x91, 0x91, 0xac, 0x4c,
	0xcd, 0xaa, 0xc0, 0x70, 0x12, 0x8f, 0x84, 0x0d, 0xe8, 0x25, 0x4d, 0xe4, 0x4a, 0x49, 0x7f, 0xbd,
	0xc2, 0xca, 0x9a, 0xde, 0x81, 0xd3, 0xbd, 0x0b, 0x4b, 0x46, 0xfd, 0x0e, 0x79, 0x49, 0x1f, 0x93,
	0x2f, 0x68, 0xb2, 0xa6, 0x60, 0x33, 0x79, 0x6b, 0x45, 0x31, 0xa6, 0xbc, 0xcd, 0xaa, 0x1d, 0xab,
	0x12, 0x97, 0x89, 0x6c, 0x52, 0x7f, 0x62, 0x8a, 0x4c, 0xaf, 0x82, 0xb1, 0x2a, 0x30, 0x9c, 0xc4,
	0x6d, 0x00, 0x55, 0x66, 0x41, 0x2e, 0xe6, 0x7a, 0xaa, 0x6a, 0x0f, 0xab, 0x0a, 0xc5, 0xa9, 0xdc,
	0x84, 0x76, 0x56, 0xee, 0x40, 0x8c, 0xc2, 0x3e, 0xad, 0xe2, 0xc2, 0x2a, 0x47, 0x64, 0x12, 0xd1,
	0x4a, 0x0a, 0x4c, 0x89, 0x98, 0xb5, 0x0d, 0x56, 0x25, 0x2e, 0x93, 0xc8, 0xe4, 0xf9, 0xde, 0x94,
	0x88, 0x5e, 0x43, 0x60, 0x55, 0x60, 0x38, 0x89, 0xb7, 0x65, 0x05, 0x40, 0xb6, 0x98, 0x4b, 0x85,
	0xbe, 0xda, 0x6a, 0xaa, 0x91, 0x72, 0x39, 0x5d, 0x2d, 0xbd, 0x4a, 0x2e, 0x94, 0xbd, 0x73, 0x15,
	0x44, 0xa3, 0xbd, 0x95, 0x71, 0xd1, 0x82, 0x4a, 0x08, 0x98, 0xdb, 0x4a, 0x3d, 0xad, 0x5b, 0xa5,
	0xf0, 0x6c, 0x4f, 0xca, 0x87, 0x2e, 0x73, 0xb0, 0x7a, 0x4f, 0xb3, 0x4a, 0xe1, 0x99, 0x2c, 0xf4,
	0x17, 0x24, 0x53, 0x16, 0xb9, 0xe7, 0x31, 0xab, 0x1a, 0xc9, 0x69, 0xbd, 0x09, 0x5d, 0xed, 0x89,
	0xc2, 0xd4, 0xb1, 0xf9, 0xf4, 0x64, 0x52, 0xca, 0xbf, 0x6b, 0xcc, 0x91, 0x3b, 0xb0, 0x2c, 0x9d,
	0x6b, 0x39, 0x35, 0xf3, 0xd9, 0xc3, 0xba, 0x54, 0x89, 0xcb, 0xd6, 0xa5, 0x25, 0x9e, 0x4d, 0x4a,
	0x66, 0xee, 0xda, 0xa4, 0x94, 0xcf, 0x56, 0xcf, 0x91, 0x6f, 0x41, 0xcf, 0x4c, 0x9e, 0x92, 0xcb,
	0xfa, 0x80, 0x42, 0x2a, 0xdb, 0x5a, 0x9d, 0x86, 0xe6, 0x24, 0x1f, 0xc0, 0x19, 0x23, 0xe3, 0x71,
	0x2f, 0x62, 0xc1, 0x93, 0x23, 0xd3, 0xff, 0xe4, 0xf3, 0x2d, 0xd6, 0x14, 0x2c, 0x27, 0xfa, 0x2e,
	0xac, 0x14, 0xb2, 0x89, 0x64, 0xcd, 0x18, 0x54, 0x92, 0xec, 0xb5, 0x5e, 0x3e, 0xa6, 0x47, 0x26,
	0x03, 0x33, 0x7f, 0x65, 0xca, 0xa0, 0x90, 0xac, 0xb4, 0x56, 0xa7, 0xa1, 0x33, 0x92, 0x66, 0xa2,
	0xc9, 0x24, 0x59, 0xc8, 0x65, 0x99, 0x24, 0x4b, 0x72, 0x54, 0x28, 0xd6, 0x65, 0x99, 0xef, 0x08,
	0x64, 0xbe, 0x83, 0xac, 0x4e, 0x49, 0x86, 0x94, 0xad, 0x33, 0x97, 0x5b, 0xb1, 0xe7, 0xc8, 0x36,
	0x67, 0x5d, 0x4b, 0x20, 0x14, 0x58, 0x37, 0x53, 0x20, 0xd6, 0x34, 0x74, 0xb6, 0xcc, 0xdc, 0x2d,
	0xcc, 0x5c, 0x66, 0xf1, 0x4a, 0x68, 0x4d, 0xc5, 0x67, 0xbe, 0x3a, 0xbb, 0xe2, 0x9b, 0x0e, 0x49,
	0x4b, 0x41, 0x58, 0xe5, 0x88, 0xcc, 0x27, 0xe8, 0x77, 0x5a, 0x92, 0xdf, 0x5e, 0xfa, 0x1d, 0xdb,
	0xaa, 0x46, 0x66, 0xa7, 0x8f, 0xba, 0x6b, 0x91, 0x42, 0x92, 0x63, 0x72, 0x15, 0xb4, 0xaa, 0x50,
	0x92, 0x4a, 0xfb, 0x4d, 0xea, 0xf2, 0xab, 0x8c, 0xb9, 0x7d, 0xcd, 0x9b, 0x95, 0x55, 0x89, 0x43,
	0x2a, 0xb7, 0xae, 0xbf, 0xfb, 0xda, 0x5e, 0x34, 0x72, 0xc3, 0xbd, 0xeb, 0xaf, 0xaf, 0x33, 0x76,
	0xdd, 0x8b, 0xc6, 0x37, 0xf8, 0xbf, 0x88, 0xbc, 0x68, 0x74, 0x03, 0xc7, 0x04, 0x1e, 0x4d, 0xb5,
	0xbf, 0x18, 0x3d, 0x5e, 0xe0, 0xd8, 0x2f, 0xfe, 0x37, 0x00, 0x00, 0xff, 0xff, 0xb9, 0x31, 0x61,
	0xbc, 0x82, 0x34, 0x00, 0x00,
}
