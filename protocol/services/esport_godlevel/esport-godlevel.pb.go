// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/esport-godlevel/esport-godlevel.proto

package esport_godlevel // import "golang.52tt.com/protocol/services/esport_godlevel"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 权益
type GodPrivilege struct {
	PrivilegeId          uint32   `protobuf:"varint,1,opt,name=privilege_id,json=privilegeId,proto3" json:"privilege_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Url                  string   `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
	UnlockLv             uint32   `protobuf:"varint,5,opt,name=unlock_lv,json=unlockLv,proto3" json:"unlock_lv,omitempty"`
	BigUrl               string   `protobuf:"bytes,6,opt,name=big_url,json=bigUrl,proto3" json:"big_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GodPrivilege) Reset()         { *m = GodPrivilege{} }
func (m *GodPrivilege) String() string { return proto.CompactTextString(m) }
func (*GodPrivilege) ProtoMessage()    {}
func (*GodPrivilege) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_godlevel_d0ffafed36342803, []int{0}
}
func (m *GodPrivilege) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GodPrivilege.Unmarshal(m, b)
}
func (m *GodPrivilege) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GodPrivilege.Marshal(b, m, deterministic)
}
func (dst *GodPrivilege) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GodPrivilege.Merge(dst, src)
}
func (m *GodPrivilege) XXX_Size() int {
	return xxx_messageInfo_GodPrivilege.Size(m)
}
func (m *GodPrivilege) XXX_DiscardUnknown() {
	xxx_messageInfo_GodPrivilege.DiscardUnknown(m)
}

var xxx_messageInfo_GodPrivilege proto.InternalMessageInfo

func (m *GodPrivilege) GetPrivilegeId() uint32 {
	if m != nil {
		return m.PrivilegeId
	}
	return 0
}

func (m *GodPrivilege) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GodPrivilege) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *GodPrivilege) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *GodPrivilege) GetUnlockLv() uint32 {
	if m != nil {
		return m.UnlockLv
	}
	return 0
}

func (m *GodPrivilege) GetBigUrl() string {
	if m != nil {
		return m.BigUrl
	}
	return ""
}

// 当前指标
type GodLevelKpi struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Score                string   `protobuf:"bytes,2,opt,name=score,proto3" json:"score,omitempty"`
	UpgradeDesc          string   `protobuf:"bytes,3,opt,name=upgrade_desc,json=upgradeDesc,proto3" json:"upgrade_desc,omitempty"`
	Progress             string   `protobuf:"bytes,4,opt,name=progress,proto3" json:"progress,omitempty"`
	Finished             bool     `protobuf:"varint,5,opt,name=finished,proto3" json:"finished,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GodLevelKpi) Reset()         { *m = GodLevelKpi{} }
func (m *GodLevelKpi) String() string { return proto.CompactTextString(m) }
func (*GodLevelKpi) ProtoMessage()    {}
func (*GodLevelKpi) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_godlevel_d0ffafed36342803, []int{1}
}
func (m *GodLevelKpi) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GodLevelKpi.Unmarshal(m, b)
}
func (m *GodLevelKpi) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GodLevelKpi.Marshal(b, m, deterministic)
}
func (dst *GodLevelKpi) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GodLevelKpi.Merge(dst, src)
}
func (m *GodLevelKpi) XXX_Size() int {
	return xxx_messageInfo_GodLevelKpi.Size(m)
}
func (m *GodLevelKpi) XXX_DiscardUnknown() {
	xxx_messageInfo_GodLevelKpi.DiscardUnknown(m)
}

var xxx_messageInfo_GodLevelKpi proto.InternalMessageInfo

func (m *GodLevelKpi) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GodLevelKpi) GetScore() string {
	if m != nil {
		return m.Score
	}
	return ""
}

func (m *GodLevelKpi) GetUpgradeDesc() string {
	if m != nil {
		return m.UpgradeDesc
	}
	return ""
}

func (m *GodLevelKpi) GetProgress() string {
	if m != nil {
		return m.Progress
	}
	return ""
}

func (m *GodLevelKpi) GetFinished() bool {
	if m != nil {
		return m.Finished
	}
	return false
}

// 等级
type GodLevel struct {
	Level                uint32          `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	Name                 string          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Url                  string          `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	PrivilegeList        []*GodPrivilege `protobuf:"bytes,4,rep,name=privilege_list,json=privilegeList,proto3" json:"privilege_list,omitempty"`
	KpiList              []*GodLevelKpi  `protobuf:"bytes,5,rep,name=kpi_list,json=kpiList,proto3" json:"kpi_list,omitempty"`
	LockedLvIcon         string          `protobuf:"bytes,6,opt,name=locked_lv_icon,json=lockedLvIcon,proto3" json:"locked_lv_icon,omitempty"`
	CurLvIcon            string          `protobuf:"bytes,7,opt,name=cur_lv_icon,json=curLvIcon,proto3" json:"cur_lv_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GodLevel) Reset()         { *m = GodLevel{} }
func (m *GodLevel) String() string { return proto.CompactTextString(m) }
func (*GodLevel) ProtoMessage()    {}
func (*GodLevel) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_godlevel_d0ffafed36342803, []int{2}
}
func (m *GodLevel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GodLevel.Unmarshal(m, b)
}
func (m *GodLevel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GodLevel.Marshal(b, m, deterministic)
}
func (dst *GodLevel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GodLevel.Merge(dst, src)
}
func (m *GodLevel) XXX_Size() int {
	return xxx_messageInfo_GodLevel.Size(m)
}
func (m *GodLevel) XXX_DiscardUnknown() {
	xxx_messageInfo_GodLevel.DiscardUnknown(m)
}

var xxx_messageInfo_GodLevel proto.InternalMessageInfo

func (m *GodLevel) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GodLevel) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GodLevel) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *GodLevel) GetPrivilegeList() []*GodPrivilege {
	if m != nil {
		return m.PrivilegeList
	}
	return nil
}

func (m *GodLevel) GetKpiList() []*GodLevelKpi {
	if m != nil {
		return m.KpiList
	}
	return nil
}

func (m *GodLevel) GetLockedLvIcon() string {
	if m != nil {
		return m.LockedLvIcon
	}
	return ""
}

func (m *GodLevel) GetCurLvIcon() string {
	if m != nil {
		return m.CurLvIcon
	}
	return ""
}

// 获取用户大神等级
type GetGodLevelReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGodLevelReq) Reset()         { *m = GetGodLevelReq{} }
func (m *GetGodLevelReq) String() string { return proto.CompactTextString(m) }
func (*GetGodLevelReq) ProtoMessage()    {}
func (*GetGodLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_godlevel_d0ffafed36342803, []int{3}
}
func (m *GetGodLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGodLevelReq.Unmarshal(m, b)
}
func (m *GetGodLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGodLevelReq.Marshal(b, m, deterministic)
}
func (dst *GetGodLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGodLevelReq.Merge(dst, src)
}
func (m *GetGodLevelReq) XXX_Size() int {
	return xxx_messageInfo_GetGodLevelReq.Size(m)
}
func (m *GetGodLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGodLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGodLevelReq proto.InternalMessageInfo

func (m *GetGodLevelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetGodLevelRsp struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	IconUrl              string   `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	NoShow               bool     `protobuf:"varint,3,opt,name=no_show,json=noShow,proto3" json:"no_show,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGodLevelRsp) Reset()         { *m = GetGodLevelRsp{} }
func (m *GetGodLevelRsp) String() string { return proto.CompactTextString(m) }
func (*GetGodLevelRsp) ProtoMessage()    {}
func (*GetGodLevelRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_godlevel_d0ffafed36342803, []int{4}
}
func (m *GetGodLevelRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGodLevelRsp.Unmarshal(m, b)
}
func (m *GetGodLevelRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGodLevelRsp.Marshal(b, m, deterministic)
}
func (dst *GetGodLevelRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGodLevelRsp.Merge(dst, src)
}
func (m *GetGodLevelRsp) XXX_Size() int {
	return xxx_messageInfo_GetGodLevelRsp.Size(m)
}
func (m *GetGodLevelRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGodLevelRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGodLevelRsp proto.InternalMessageInfo

func (m *GetGodLevelRsp) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GetGodLevelRsp) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *GetGodLevelRsp) GetNoShow() bool {
	if m != nil {
		return m.NoShow
	}
	return false
}

// 获取用户大神等级详情
type GetGodLevelDetailReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGodLevelDetailReq) Reset()         { *m = GetGodLevelDetailReq{} }
func (m *GetGodLevelDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetGodLevelDetailReq) ProtoMessage()    {}
func (*GetGodLevelDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_godlevel_d0ffafed36342803, []int{5}
}
func (m *GetGodLevelDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGodLevelDetailReq.Unmarshal(m, b)
}
func (m *GetGodLevelDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGodLevelDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetGodLevelDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGodLevelDetailReq.Merge(dst, src)
}
func (m *GetGodLevelDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetGodLevelDetailReq.Size(m)
}
func (m *GetGodLevelDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGodLevelDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGodLevelDetailReq proto.InternalMessageInfo

func (m *GetGodLevelDetailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetGodLevelDetailRsp struct {
	AllLevelInfo         []*GodLevel `protobuf:"bytes,1,rep,name=all_level_info,json=allLevelInfo,proto3" json:"all_level_info,omitempty"`
	Level                uint32      `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	UpgradeDesc          string      `protobuf:"bytes,3,opt,name=upgrade_desc,json=upgradeDesc,proto3" json:"upgrade_desc,omitempty"`
	KeepDesc             string      `protobuf:"bytes,4,opt,name=keep_desc,json=keepDesc,proto3" json:"keep_desc,omitempty"`
	UpgradeDescV2        string      `protobuf:"bytes,5,opt,name=upgrade_desc_v2,json=upgradeDescV2,proto3" json:"upgrade_desc_v2,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetGodLevelDetailRsp) Reset()         { *m = GetGodLevelDetailRsp{} }
func (m *GetGodLevelDetailRsp) String() string { return proto.CompactTextString(m) }
func (*GetGodLevelDetailRsp) ProtoMessage()    {}
func (*GetGodLevelDetailRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_godlevel_d0ffafed36342803, []int{6}
}
func (m *GetGodLevelDetailRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGodLevelDetailRsp.Unmarshal(m, b)
}
func (m *GetGodLevelDetailRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGodLevelDetailRsp.Marshal(b, m, deterministic)
}
func (dst *GetGodLevelDetailRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGodLevelDetailRsp.Merge(dst, src)
}
func (m *GetGodLevelDetailRsp) XXX_Size() int {
	return xxx_messageInfo_GetGodLevelDetailRsp.Size(m)
}
func (m *GetGodLevelDetailRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGodLevelDetailRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGodLevelDetailRsp proto.InternalMessageInfo

func (m *GetGodLevelDetailRsp) GetAllLevelInfo() []*GodLevel {
	if m != nil {
		return m.AllLevelInfo
	}
	return nil
}

func (m *GetGodLevelDetailRsp) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GetGodLevelDetailRsp) GetUpgradeDesc() string {
	if m != nil {
		return m.UpgradeDesc
	}
	return ""
}

func (m *GetGodLevelDetailRsp) GetKeepDesc() string {
	if m != nil {
		return m.KeepDesc
	}
	return ""
}

func (m *GetGodLevelDetailRsp) GetUpgradeDescV2() string {
	if m != nil {
		return m.UpgradeDescV2
	}
	return ""
}

type GodLevelKpiData struct {
	Uid                   uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RepurchaseRate        float32  `protobuf:"fixed32,2,opt,name=repurchase_rate,json=repurchaseRate,proto3" json:"repurchase_rate,omitempty"`
	OrderCnt              uint32   `protobuf:"varint,3,opt,name=order_cnt,json=orderCnt,proto3" json:"order_cnt,omitempty"`
	ImResponseDr          uint32   `protobuf:"varint,4,opt,name=im_response_dr,json=imResponseDr,proto3" json:"im_response_dr,omitempty"`
	PositiveRatio         float32  `protobuf:"fixed32,5,opt,name=positive_ratio,json=positiveRatio,proto3" json:"positive_ratio,omitempty"`
	ExposureOrderRatio    float32  `protobuf:"fixed32,6,opt,name=exposure_order_ratio,json=exposureOrderRatio,proto3" json:"exposure_order_ratio,omitempty"`
	Sigma                 float32  `protobuf:"fixed32,7,opt,name=sigma,proto3" json:"sigma,omitempty"`
	ActiveDays            uint32   `protobuf:"varint,8,opt,name=active_days,json=activeDays,proto3" json:"active_days,omitempty"`
	ExposureOrderRatioAvg float32  `protobuf:"fixed32,9,opt,name=exposure_order_ratio_avg,json=exposureOrderRatioAvg,proto3" json:"exposure_order_ratio_avg,omitempty"`
	KpiUpdateTs           uint32   `protobuf:"varint,10,opt,name=kpi_update_ts,json=kpiUpdateTs,proto3" json:"kpi_update_ts,omitempty"`
	CommitUv              uint32   `protobuf:"varint,11,opt,name=commit_uv,json=commitUv,proto3" json:"commit_uv,omitempty"`
	CommitAmt             float32  `protobuf:"fixed32,12,opt,name=commit_amt,json=commitAmt,proto3" json:"commit_amt,omitempty"`
	OrderRecTime          float32  `protobuf:"fixed32,13,opt,name=order_rec_time,json=orderRecTime,proto3" json:"order_rec_time,omitempty"`
	ActiveDaysV_2         uint32   `protobuf:"varint,14,opt,name=active_days_v_2,json=activeDaysV2,proto3" json:"active_days_v_2,omitempty"`
	LastWeekCommitUv      uint32   `protobuf:"varint,15,opt,name=last_week_commit_uv,json=lastWeekCommitUv,proto3" json:"last_week_commit_uv,omitempty"`
	LastWeekCommitCnt     uint32   `protobuf:"varint,16,opt,name=last_week_commit_cnt,json=lastWeekCommitCnt,proto3" json:"last_week_commit_cnt,omitempty"`
	LastWeekOrderRecTime  float32  `protobuf:"fixed32,17,opt,name=last_week_order_rec_time,json=lastWeekOrderRecTime,proto3" json:"last_week_order_rec_time,omitempty"`
	LastWeekActiveDays    uint32   `protobuf:"varint,18,opt,name=last_week_active_days,json=lastWeekActiveDays,proto3" json:"last_week_active_days,omitempty"`
	ThisWeekCommitUv      uint32   `protobuf:"varint,19,opt,name=this_week_commit_uv,json=thisWeekCommitUv,proto3" json:"this_week_commit_uv,omitempty"`
	ThisWeekCommitCnt     uint32   `protobuf:"varint,20,opt,name=this_week_commit_cnt,json=thisWeekCommitCnt,proto3" json:"this_week_commit_cnt,omitempty"`
	ThisWeekOrderRecTime  float32  `protobuf:"fixed32,21,opt,name=this_week_order_rec_time,json=thisWeekOrderRecTime,proto3" json:"this_week_order_rec_time,omitempty"`
	ThisWeekActiveDays    uint32   `protobuf:"varint,22,opt,name=this_week_active_days,json=thisWeekActiveDays,proto3" json:"this_week_active_days,omitempty"`
	LastWeekCommitRound   uint32   `protobuf:"varint,23,opt,name=last_week_commit_round,json=lastWeekCommitRound,proto3" json:"last_week_commit_round,omitempty"`
	ThisWeekCommitRound   uint32   `protobuf:"varint,24,opt,name=this_week_commit_round,json=thisWeekCommitRound,proto3" json:"this_week_commit_round,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *GodLevelKpiData) Reset()         { *m = GodLevelKpiData{} }
func (m *GodLevelKpiData) String() string { return proto.CompactTextString(m) }
func (*GodLevelKpiData) ProtoMessage()    {}
func (*GodLevelKpiData) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_godlevel_d0ffafed36342803, []int{7}
}
func (m *GodLevelKpiData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GodLevelKpiData.Unmarshal(m, b)
}
func (m *GodLevelKpiData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GodLevelKpiData.Marshal(b, m, deterministic)
}
func (dst *GodLevelKpiData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GodLevelKpiData.Merge(dst, src)
}
func (m *GodLevelKpiData) XXX_Size() int {
	return xxx_messageInfo_GodLevelKpiData.Size(m)
}
func (m *GodLevelKpiData) XXX_DiscardUnknown() {
	xxx_messageInfo_GodLevelKpiData.DiscardUnknown(m)
}

var xxx_messageInfo_GodLevelKpiData proto.InternalMessageInfo

func (m *GodLevelKpiData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GodLevelKpiData) GetRepurchaseRate() float32 {
	if m != nil {
		return m.RepurchaseRate
	}
	return 0
}

func (m *GodLevelKpiData) GetOrderCnt() uint32 {
	if m != nil {
		return m.OrderCnt
	}
	return 0
}

func (m *GodLevelKpiData) GetImResponseDr() uint32 {
	if m != nil {
		return m.ImResponseDr
	}
	return 0
}

func (m *GodLevelKpiData) GetPositiveRatio() float32 {
	if m != nil {
		return m.PositiveRatio
	}
	return 0
}

func (m *GodLevelKpiData) GetExposureOrderRatio() float32 {
	if m != nil {
		return m.ExposureOrderRatio
	}
	return 0
}

func (m *GodLevelKpiData) GetSigma() float32 {
	if m != nil {
		return m.Sigma
	}
	return 0
}

func (m *GodLevelKpiData) GetActiveDays() uint32 {
	if m != nil {
		return m.ActiveDays
	}
	return 0
}

func (m *GodLevelKpiData) GetExposureOrderRatioAvg() float32 {
	if m != nil {
		return m.ExposureOrderRatioAvg
	}
	return 0
}

func (m *GodLevelKpiData) GetKpiUpdateTs() uint32 {
	if m != nil {
		return m.KpiUpdateTs
	}
	return 0
}

func (m *GodLevelKpiData) GetCommitUv() uint32 {
	if m != nil {
		return m.CommitUv
	}
	return 0
}

func (m *GodLevelKpiData) GetCommitAmt() float32 {
	if m != nil {
		return m.CommitAmt
	}
	return 0
}

func (m *GodLevelKpiData) GetOrderRecTime() float32 {
	if m != nil {
		return m.OrderRecTime
	}
	return 0
}

func (m *GodLevelKpiData) GetActiveDaysV_2() uint32 {
	if m != nil {
		return m.ActiveDaysV_2
	}
	return 0
}

func (m *GodLevelKpiData) GetLastWeekCommitUv() uint32 {
	if m != nil {
		return m.LastWeekCommitUv
	}
	return 0
}

func (m *GodLevelKpiData) GetLastWeekCommitCnt() uint32 {
	if m != nil {
		return m.LastWeekCommitCnt
	}
	return 0
}

func (m *GodLevelKpiData) GetLastWeekOrderRecTime() float32 {
	if m != nil {
		return m.LastWeekOrderRecTime
	}
	return 0
}

func (m *GodLevelKpiData) GetLastWeekActiveDays() uint32 {
	if m != nil {
		return m.LastWeekActiveDays
	}
	return 0
}

func (m *GodLevelKpiData) GetThisWeekCommitUv() uint32 {
	if m != nil {
		return m.ThisWeekCommitUv
	}
	return 0
}

func (m *GodLevelKpiData) GetThisWeekCommitCnt() uint32 {
	if m != nil {
		return m.ThisWeekCommitCnt
	}
	return 0
}

func (m *GodLevelKpiData) GetThisWeekOrderRecTime() float32 {
	if m != nil {
		return m.ThisWeekOrderRecTime
	}
	return 0
}

func (m *GodLevelKpiData) GetThisWeekActiveDays() uint32 {
	if m != nil {
		return m.ThisWeekActiveDays
	}
	return 0
}

func (m *GodLevelKpiData) GetLastWeekCommitRound() uint32 {
	if m != nil {
		return m.LastWeekCommitRound
	}
	return 0
}

func (m *GodLevelKpiData) GetThisWeekCommitRound() uint32 {
	if m != nil {
		return m.ThisWeekCommitRound
	}
	return 0
}

// 批量获取用户大神等级
type BatchGetGodLevelByUidReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetGodLevelByUidReq) Reset()         { *m = BatchGetGodLevelByUidReq{} }
func (m *BatchGetGodLevelByUidReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetGodLevelByUidReq) ProtoMessage()    {}
func (*BatchGetGodLevelByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_godlevel_d0ffafed36342803, []int{8}
}
func (m *BatchGetGodLevelByUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGodLevelByUidReq.Unmarshal(m, b)
}
func (m *BatchGetGodLevelByUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGodLevelByUidReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetGodLevelByUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGodLevelByUidReq.Merge(dst, src)
}
func (m *BatchGetGodLevelByUidReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetGodLevelByUidReq.Size(m)
}
func (m *BatchGetGodLevelByUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGodLevelByUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGodLevelByUidReq proto.InternalMessageInfo

func (m *BatchGetGodLevelByUidReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetGodLevelByUidRsp struct {
	UidLvs               map[uint32]uint32 `protobuf:"bytes,1,rep,name=uid_lvs,json=uidLvs,proto3" json:"uid_lvs,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetGodLevelByUidRsp) Reset()         { *m = BatchGetGodLevelByUidRsp{} }
func (m *BatchGetGodLevelByUidRsp) String() string { return proto.CompactTextString(m) }
func (*BatchGetGodLevelByUidRsp) ProtoMessage()    {}
func (*BatchGetGodLevelByUidRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_godlevel_d0ffafed36342803, []int{9}
}
func (m *BatchGetGodLevelByUidRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGodLevelByUidRsp.Unmarshal(m, b)
}
func (m *BatchGetGodLevelByUidRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGodLevelByUidRsp.Marshal(b, m, deterministic)
}
func (dst *BatchGetGodLevelByUidRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGodLevelByUidRsp.Merge(dst, src)
}
func (m *BatchGetGodLevelByUidRsp) XXX_Size() int {
	return xxx_messageInfo_BatchGetGodLevelByUidRsp.Size(m)
}
func (m *BatchGetGodLevelByUidRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGodLevelByUidRsp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGodLevelByUidRsp proto.InternalMessageInfo

func (m *BatchGetGodLevelByUidRsp) GetUidLvs() map[uint32]uint32 {
	if m != nil {
		return m.UidLvs
	}
	return nil
}

type GodLevelBaseConf struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GodLevelBaseConf) Reset()         { *m = GodLevelBaseConf{} }
func (m *GodLevelBaseConf) String() string { return proto.CompactTextString(m) }
func (*GodLevelBaseConf) ProtoMessage()    {}
func (*GodLevelBaseConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_godlevel_d0ffafed36342803, []int{10}
}
func (m *GodLevelBaseConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GodLevelBaseConf.Unmarshal(m, b)
}
func (m *GodLevelBaseConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GodLevelBaseConf.Marshal(b, m, deterministic)
}
func (dst *GodLevelBaseConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GodLevelBaseConf.Merge(dst, src)
}
func (m *GodLevelBaseConf) XXX_Size() int {
	return xxx_messageInfo_GodLevelBaseConf.Size(m)
}
func (m *GodLevelBaseConf) XXX_DiscardUnknown() {
	xxx_messageInfo_GodLevelBaseConf.DiscardUnknown(m)
}

var xxx_messageInfo_GodLevelBaseConf proto.InternalMessageInfo

func (m *GodLevelBaseConf) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GodLevelBaseConf) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// 获取大神等级基础配置
type GetGodLevelConfsReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGodLevelConfsReq) Reset()         { *m = GetGodLevelConfsReq{} }
func (m *GetGodLevelConfsReq) String() string { return proto.CompactTextString(m) }
func (*GetGodLevelConfsReq) ProtoMessage()    {}
func (*GetGodLevelConfsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_godlevel_d0ffafed36342803, []int{11}
}
func (m *GetGodLevelConfsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGodLevelConfsReq.Unmarshal(m, b)
}
func (m *GetGodLevelConfsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGodLevelConfsReq.Marshal(b, m, deterministic)
}
func (dst *GetGodLevelConfsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGodLevelConfsReq.Merge(dst, src)
}
func (m *GetGodLevelConfsReq) XXX_Size() int {
	return xxx_messageInfo_GetGodLevelConfsReq.Size(m)
}
func (m *GetGodLevelConfsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGodLevelConfsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGodLevelConfsReq proto.InternalMessageInfo

type GetGodLevelConfsRsp struct {
	Confs                []*GodLevelBaseConf `protobuf:"bytes,1,rep,name=confs,proto3" json:"confs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetGodLevelConfsRsp) Reset()         { *m = GetGodLevelConfsRsp{} }
func (m *GetGodLevelConfsRsp) String() string { return proto.CompactTextString(m) }
func (*GetGodLevelConfsRsp) ProtoMessage()    {}
func (*GetGodLevelConfsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_godlevel_d0ffafed36342803, []int{12}
}
func (m *GetGodLevelConfsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGodLevelConfsRsp.Unmarshal(m, b)
}
func (m *GetGodLevelConfsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGodLevelConfsRsp.Marshal(b, m, deterministic)
}
func (dst *GetGodLevelConfsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGodLevelConfsRsp.Merge(dst, src)
}
func (m *GetGodLevelConfsRsp) XXX_Size() int {
	return xxx_messageInfo_GetGodLevelConfsRsp.Size(m)
}
func (m *GetGodLevelConfsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGodLevelConfsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGodLevelConfsRsp proto.InternalMessageInfo

func (m *GetGodLevelConfsRsp) GetConfs() []*GodLevelBaseConf {
	if m != nil {
		return m.Confs
	}
	return nil
}

// 更新KPI数据
type UpdateGodLevelKpiReq struct {
	Data                 *GodLevelKpiData `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UpdateGodLevelKpiReq) Reset()         { *m = UpdateGodLevelKpiReq{} }
func (m *UpdateGodLevelKpiReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGodLevelKpiReq) ProtoMessage()    {}
func (*UpdateGodLevelKpiReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_godlevel_d0ffafed36342803, []int{13}
}
func (m *UpdateGodLevelKpiReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGodLevelKpiReq.Unmarshal(m, b)
}
func (m *UpdateGodLevelKpiReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGodLevelKpiReq.Marshal(b, m, deterministic)
}
func (dst *UpdateGodLevelKpiReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGodLevelKpiReq.Merge(dst, src)
}
func (m *UpdateGodLevelKpiReq) XXX_Size() int {
	return xxx_messageInfo_UpdateGodLevelKpiReq.Size(m)
}
func (m *UpdateGodLevelKpiReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGodLevelKpiReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGodLevelKpiReq proto.InternalMessageInfo

func (m *UpdateGodLevelKpiReq) GetData() *GodLevelKpiData {
	if m != nil {
		return m.Data
	}
	return nil
}

type UpdateGodLevelKpiRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGodLevelKpiRsp) Reset()         { *m = UpdateGodLevelKpiRsp{} }
func (m *UpdateGodLevelKpiRsp) String() string { return proto.CompactTextString(m) }
func (*UpdateGodLevelKpiRsp) ProtoMessage()    {}
func (*UpdateGodLevelKpiRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_godlevel_d0ffafed36342803, []int{14}
}
func (m *UpdateGodLevelKpiRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGodLevelKpiRsp.Unmarshal(m, b)
}
func (m *UpdateGodLevelKpiRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGodLevelKpiRsp.Marshal(b, m, deterministic)
}
func (dst *UpdateGodLevelKpiRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGodLevelKpiRsp.Merge(dst, src)
}
func (m *UpdateGodLevelKpiRsp) XXX_Size() int {
	return xxx_messageInfo_UpdateGodLevelKpiRsp.Size(m)
}
func (m *UpdateGodLevelKpiRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGodLevelKpiRsp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGodLevelKpiRsp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*GodPrivilege)(nil), "esport_godlevel.GodPrivilege")
	proto.RegisterType((*GodLevelKpi)(nil), "esport_godlevel.GodLevelKpi")
	proto.RegisterType((*GodLevel)(nil), "esport_godlevel.GodLevel")
	proto.RegisterType((*GetGodLevelReq)(nil), "esport_godlevel.GetGodLevelReq")
	proto.RegisterType((*GetGodLevelRsp)(nil), "esport_godlevel.GetGodLevelRsp")
	proto.RegisterType((*GetGodLevelDetailReq)(nil), "esport_godlevel.GetGodLevelDetailReq")
	proto.RegisterType((*GetGodLevelDetailRsp)(nil), "esport_godlevel.GetGodLevelDetailRsp")
	proto.RegisterType((*GodLevelKpiData)(nil), "esport_godlevel.GodLevelKpiData")
	proto.RegisterType((*BatchGetGodLevelByUidReq)(nil), "esport_godlevel.BatchGetGodLevelByUidReq")
	proto.RegisterType((*BatchGetGodLevelByUidRsp)(nil), "esport_godlevel.BatchGetGodLevelByUidRsp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "esport_godlevel.BatchGetGodLevelByUidRsp.UidLvsEntry")
	proto.RegisterType((*GodLevelBaseConf)(nil), "esport_godlevel.GodLevelBaseConf")
	proto.RegisterType((*GetGodLevelConfsReq)(nil), "esport_godlevel.GetGodLevelConfsReq")
	proto.RegisterType((*GetGodLevelConfsRsp)(nil), "esport_godlevel.GetGodLevelConfsRsp")
	proto.RegisterType((*UpdateGodLevelKpiReq)(nil), "esport_godlevel.UpdateGodLevelKpiReq")
	proto.RegisterType((*UpdateGodLevelKpiRsp)(nil), "esport_godlevel.UpdateGodLevelKpiRsp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ESportGodLevelServiceClient is the client API for ESportGodLevelService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ESportGodLevelServiceClient interface {
	// 获取用户大神等级
	GetGodLevel(ctx context.Context, in *GetGodLevelReq, opts ...grpc.CallOption) (*GetGodLevelRsp, error)
	// 获取用户大神等级详情
	GetGodLevelDetail(ctx context.Context, in *GetGodLevelDetailReq, opts ...grpc.CallOption) (*GetGodLevelDetailRsp, error)
	// 批量获取用户大神等级
	BatchGetGodLevelByUid(ctx context.Context, in *BatchGetGodLevelByUidReq, opts ...grpc.CallOption) (*BatchGetGodLevelByUidRsp, error)
	// 获取大神等级基础配置
	GetGodLevelConfs(ctx context.Context, in *GetGodLevelConfsReq, opts ...grpc.CallOption) (*GetGodLevelConfsRsp, error)
	UpdateGodLevelKpi(ctx context.Context, in *UpdateGodLevelKpiReq, opts ...grpc.CallOption) (*UpdateGodLevelKpiRsp, error)
}

type eSportGodLevelServiceClient struct {
	cc *grpc.ClientConn
}

func NewESportGodLevelServiceClient(cc *grpc.ClientConn) ESportGodLevelServiceClient {
	return &eSportGodLevelServiceClient{cc}
}

func (c *eSportGodLevelServiceClient) GetGodLevel(ctx context.Context, in *GetGodLevelReq, opts ...grpc.CallOption) (*GetGodLevelRsp, error) {
	out := new(GetGodLevelRsp)
	err := c.cc.Invoke(ctx, "/esport_godlevel.ESportGodLevelService/GetGodLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportGodLevelServiceClient) GetGodLevelDetail(ctx context.Context, in *GetGodLevelDetailReq, opts ...grpc.CallOption) (*GetGodLevelDetailRsp, error) {
	out := new(GetGodLevelDetailRsp)
	err := c.cc.Invoke(ctx, "/esport_godlevel.ESportGodLevelService/GetGodLevelDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportGodLevelServiceClient) BatchGetGodLevelByUid(ctx context.Context, in *BatchGetGodLevelByUidReq, opts ...grpc.CallOption) (*BatchGetGodLevelByUidRsp, error) {
	out := new(BatchGetGodLevelByUidRsp)
	err := c.cc.Invoke(ctx, "/esport_godlevel.ESportGodLevelService/BatchGetGodLevelByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportGodLevelServiceClient) GetGodLevelConfs(ctx context.Context, in *GetGodLevelConfsReq, opts ...grpc.CallOption) (*GetGodLevelConfsRsp, error) {
	out := new(GetGodLevelConfsRsp)
	err := c.cc.Invoke(ctx, "/esport_godlevel.ESportGodLevelService/GetGodLevelConfs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportGodLevelServiceClient) UpdateGodLevelKpi(ctx context.Context, in *UpdateGodLevelKpiReq, opts ...grpc.CallOption) (*UpdateGodLevelKpiRsp, error) {
	out := new(UpdateGodLevelKpiRsp)
	err := c.cc.Invoke(ctx, "/esport_godlevel.ESportGodLevelService/UpdateGodLevelKpi", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ESportGodLevelServiceServer is the server API for ESportGodLevelService service.
type ESportGodLevelServiceServer interface {
	// 获取用户大神等级
	GetGodLevel(context.Context, *GetGodLevelReq) (*GetGodLevelRsp, error)
	// 获取用户大神等级详情
	GetGodLevelDetail(context.Context, *GetGodLevelDetailReq) (*GetGodLevelDetailRsp, error)
	// 批量获取用户大神等级
	BatchGetGodLevelByUid(context.Context, *BatchGetGodLevelByUidReq) (*BatchGetGodLevelByUidRsp, error)
	// 获取大神等级基础配置
	GetGodLevelConfs(context.Context, *GetGodLevelConfsReq) (*GetGodLevelConfsRsp, error)
	UpdateGodLevelKpi(context.Context, *UpdateGodLevelKpiReq) (*UpdateGodLevelKpiRsp, error)
}

func RegisterESportGodLevelServiceServer(s *grpc.Server, srv ESportGodLevelServiceServer) {
	s.RegisterService(&_ESportGodLevelService_serviceDesc, srv)
}

func _ESportGodLevelService_GetGodLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGodLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportGodLevelServiceServer).GetGodLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_godlevel.ESportGodLevelService/GetGodLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportGodLevelServiceServer).GetGodLevel(ctx, req.(*GetGodLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportGodLevelService_GetGodLevelDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGodLevelDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportGodLevelServiceServer).GetGodLevelDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_godlevel.ESportGodLevelService/GetGodLevelDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportGodLevelServiceServer).GetGodLevelDetail(ctx, req.(*GetGodLevelDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportGodLevelService_BatchGetGodLevelByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGodLevelByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportGodLevelServiceServer).BatchGetGodLevelByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_godlevel.ESportGodLevelService/BatchGetGodLevelByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportGodLevelServiceServer).BatchGetGodLevelByUid(ctx, req.(*BatchGetGodLevelByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportGodLevelService_GetGodLevelConfs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGodLevelConfsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportGodLevelServiceServer).GetGodLevelConfs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_godlevel.ESportGodLevelService/GetGodLevelConfs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportGodLevelServiceServer).GetGodLevelConfs(ctx, req.(*GetGodLevelConfsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportGodLevelService_UpdateGodLevelKpi_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGodLevelKpiReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportGodLevelServiceServer).UpdateGodLevelKpi(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_godlevel.ESportGodLevelService/UpdateGodLevelKpi",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportGodLevelServiceServer).UpdateGodLevelKpi(ctx, req.(*UpdateGodLevelKpiReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ESportGodLevelService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "esport_godlevel.ESportGodLevelService",
	HandlerType: (*ESportGodLevelServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGodLevel",
			Handler:    _ESportGodLevelService_GetGodLevel_Handler,
		},
		{
			MethodName: "GetGodLevelDetail",
			Handler:    _ESportGodLevelService_GetGodLevelDetail_Handler,
		},
		{
			MethodName: "BatchGetGodLevelByUid",
			Handler:    _ESportGodLevelService_BatchGetGodLevelByUid_Handler,
		},
		{
			MethodName: "GetGodLevelConfs",
			Handler:    _ESportGodLevelService_GetGodLevelConfs_Handler,
		},
		{
			MethodName: "UpdateGodLevelKpi",
			Handler:    _ESportGodLevelService_UpdateGodLevelKpi_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/esport-godlevel/esport-godlevel.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/esport-godlevel/esport-godlevel.proto", fileDescriptor_esport_godlevel_d0ffafed36342803)
}

var fileDescriptor_esport_godlevel_d0ffafed36342803 = []byte{
	// 1227 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x56, 0x5d, 0x73, 0x13, 0x37,
	0x17, 0xc6, 0xce, 0x97, 0x73, 0xfc, 0x91, 0xa0, 0x38, 0xb0, 0x98, 0x97, 0x97, 0xb0, 0x03, 0x6d,
	0xb8, 0x20, 0x29, 0x06, 0x4a, 0xdb, 0xe9, 0x4c, 0x07, 0x08, 0xc3, 0x30, 0xcd, 0x40, 0x2b, 0x08,
	0x9d, 0xe1, 0x46, 0x15, 0xbb, 0x8a, 0xad, 0xf1, 0x7a, 0xb5, 0x91, 0xb4, 0x4b, 0xfd, 0x03, 0x3a,
	0xbd, 0xe8, 0x5d, 0xff, 0x41, 0x2f, 0xfa, 0x93, 0xfa, 0x7f, 0x3a, 0x92, 0x76, 0xed, 0xf5, 0x57,
	0xc6, 0x77, 0xab, 0xa3, 0xf3, 0xe8, 0x3c, 0xcf, 0xa3, 0xe3, 0x23, 0xc3, 0x63, 0xad, 0x8f, 0x2f,
	0x52, 0x1e, 0x0c, 0x14, 0x8f, 0x32, 0x26, 0x8f, 0x99, 0x4a, 0x84, 0xd4, 0x0f, 0x7a, 0x22, 0x8c,
	0x58, 0xc6, 0xa2, 0xd9, 0xf5, 0x51, 0x22, 0x85, 0x16, 0x68, 0xc7, 0x85, 0x49, 0x11, 0xf6, 0xff,
	0xae, 0x40, 0xe3, 0x95, 0x08, 0x7f, 0x92, 0x3c, 0xe3, 0x11, 0xeb, 0x31, 0x74, 0x07, 0x1a, 0x49,
	0xb1, 0x20, 0x3c, 0xf4, 0x2a, 0x07, 0x95, 0xc3, 0x26, 0xae, 0x8f, 0x63, 0xaf, 0x43, 0x84, 0x60,
	0x3d, 0xa6, 0x43, 0xe6, 0x55, 0x0f, 0x2a, 0x87, 0xdb, 0xd8, 0x7e, 0x9b, 0x58, 0xc8, 0x54, 0xe0,
	0xad, 0xb9, 0x98, 0xf9, 0x46, 0xbb, 0xb0, 0x96, 0xca, 0xc8, 0x5b, 0xb7, 0x21, 0xf3, 0x89, 0x6e,
	0xc2, 0x76, 0x1a, 0x47, 0x22, 0x18, 0x90, 0x28, 0xf3, 0x36, 0xec, 0xc9, 0x35, 0x17, 0x38, 0xcd,
	0xd0, 0x75, 0xd8, 0xfa, 0xc4, 0x7b, 0xc4, 0x40, 0x36, 0x2d, 0x64, 0xf3, 0x13, 0xef, 0x9d, 0xc9,
	0xc8, 0xff, 0xab, 0x02, 0xf5, 0x57, 0x22, 0x3c, 0x35, 0x84, 0x7f, 0x4c, 0xf8, 0xb8, 0x7e, 0xa5,
	0x54, 0xbf, 0x0d, 0x1b, 0x2a, 0x10, 0xb2, 0x20, 0xe5, 0x16, 0x46, 0x4c, 0x9a, 0xf4, 0x24, 0x0d,
	0x19, 0x29, 0xb1, 0xab, 0xe7, 0xb1, 0x13, 0x43, 0xb2, 0x03, 0xb5, 0x44, 0x8a, 0x9e, 0x64, 0x4a,
	0xe5, 0x4c, 0xc7, 0x6b, 0xb3, 0x77, 0xce, 0x63, 0xae, 0xfa, 0x2c, 0xb4, 0x6c, 0x6b, 0x78, 0xbc,
	0xf6, 0xff, 0xac, 0x42, 0xad, 0x20, 0x65, 0xaa, 0x5b, 0x3b, 0x73, 0xb7, 0xdc, 0x62, 0xa1, 0x4f,
	0xb9, 0x27, 0x6b, 0x13, 0x4f, 0x4e, 0xa0, 0x35, 0x31, 0x3c, 0xe2, 0x4a, 0x7b, 0xeb, 0x07, 0x6b,
	0x87, 0xf5, 0xee, 0xad, 0xa3, 0x99, 0xbb, 0x3a, 0x2a, 0xdf, 0x13, 0x6e, 0x8e, 0x41, 0xa7, 0x5c,
	0x69, 0xf4, 0x14, 0x6a, 0x83, 0x84, 0x3b, 0xfc, 0x86, 0xc5, 0xff, 0x6f, 0x11, 0xbe, 0xf0, 0x10,
	0x6f, 0x0d, 0x12, 0x6e, 0x81, 0x77, 0xa1, 0x65, 0xfc, 0x67, 0x21, 0x89, 0x32, 0xc2, 0x03, 0x11,
	0xe7, 0xe6, 0x37, 0x5c, 0xf4, 0x34, 0x7b, 0x1d, 0x88, 0x18, 0xfd, 0x1f, 0xea, 0x41, 0x2a, 0xc7,
	0x29, 0x5b, 0x36, 0x65, 0x3b, 0x48, 0xa5, 0xdb, 0xf7, 0x7d, 0x68, 0xbd, 0x62, 0xba, 0x28, 0x80,
	0xd9, 0x85, 0x15, 0x3a, 0x6e, 0x1f, 0xf3, 0xe9, 0x7f, 0x9c, 0xce, 0x51, 0xc9, 0x12, 0xdb, 0x6e,
	0x40, 0xcd, 0x14, 0xb1, 0x8d, 0xe0, 0xac, 0xdb, 0x32, 0xeb, 0x33, 0x19, 0x99, 0x16, 0x89, 0x05,
	0x51, 0x7d, 0xf1, 0xd9, 0x3a, 0x58, 0xc3, 0x9b, 0xb1, 0x78, 0xd7, 0x17, 0x9f, 0xfd, 0x43, 0x68,
	0x97, 0xce, 0x3e, 0x61, 0x9a, 0xf2, 0x25, 0x2c, 0xfe, 0xad, 0x2c, 0x4a, 0x55, 0x09, 0xfa, 0x01,
	0x5a, 0x34, 0x8a, 0x88, 0xe5, 0x40, 0x78, 0x7c, 0x2e, 0xbc, 0x8a, 0xf5, 0xf1, 0xc6, 0x52, 0x1f,
	0x71, 0x83, 0x46, 0x91, 0xfd, 0x7a, 0x1d, 0x9f, 0x8b, 0x89, 0x9a, 0x6a, 0x59, 0xcd, 0x0a, 0x2d,
	0x78, 0x13, 0xb6, 0x07, 0x8c, 0x25, 0x6e, 0x3f, 0xef, 0x41, 0x13, 0xb0, 0x9b, 0x5f, 0xc0, 0x4e,
	0x19, 0x4f, 0xb2, 0xae, 0x6d, 0xc5, 0x6d, 0xdc, 0x2c, 0x1d, 0xf1, 0xa1, 0xeb, 0xff, 0x5e, 0x83,
	0x9d, 0xd2, 0x05, 0x9f, 0x50, 0x4d, 0xe7, 0xd5, 0xa3, 0x2f, 0x61, 0x47, 0xb2, 0x24, 0x95, 0x41,
	0x9f, 0x2a, 0x46, 0x24, 0xd5, 0xae, 0x3b, 0xab, 0xb8, 0x35, 0x09, 0x63, 0xaa, 0x99, 0xe1, 0x24,
	0x64, 0xc8, 0x24, 0x09, 0x62, 0x6d, 0x39, 0x37, 0x71, 0xcd, 0x06, 0x5e, 0xc4, 0xb6, 0x67, 0xf8,
	0x90, 0x48, 0xe3, 0x4b, 0xac, 0x18, 0x09, 0xa5, 0x65, 0xdd, 0xc4, 0x0d, 0x3e, 0xc4, 0x79, 0xf0,
	0x44, 0xa2, 0x7b, 0xd0, 0x4a, 0x84, 0xe2, 0x9a, 0x67, 0xb6, 0x12, 0x17, 0x96, 0x78, 0x15, 0x37,
	0x8b, 0x28, 0x36, 0x41, 0xf4, 0x15, 0xb4, 0xd9, 0x6f, 0x89, 0x50, 0xa9, 0x64, 0xc4, 0x95, 0x74,
	0xc9, 0x9b, 0x36, 0x19, 0x15, 0x7b, 0x6f, 0xcd, 0x96, 0x43, 0x98, 0xdf, 0x3a, 0xef, 0x0d, 0xa9,
	0x6d, 0xc3, 0x2a, 0x76, 0x0b, 0x74, 0x1b, 0xea, 0x34, 0xb0, 0xc5, 0x42, 0x3a, 0x52, 0x5e, 0xcd,
	0x32, 0x02, 0x17, 0x3a, 0xa1, 0x23, 0x85, 0x9e, 0x82, 0xb7, 0xa8, 0x10, 0xa1, 0x59, 0xcf, 0xdb,
	0xb6, 0x27, 0xed, 0xcf, 0x17, 0x7b, 0x96, 0xf5, 0x90, 0x0f, 0x4d, 0xf3, 0xdb, 0x4a, 0x93, 0x90,
	0x6a, 0x46, 0xb4, 0xf2, 0xc0, 0xcd, 0xc4, 0x41, 0xc2, 0xcf, 0x6c, 0xec, 0xbd, 0x32, 0x7e, 0x05,
	0x62, 0x38, 0xe4, 0x9a, 0xa4, 0x99, 0x57, 0x77, 0x7e, 0xb9, 0xc0, 0x59, 0x86, 0x6e, 0x01, 0xe4,
	0x9b, 0x74, 0xa8, 0xbd, 0x86, 0xad, 0x95, 0xa7, 0x3f, 0x1b, 0x5a, 0x3b, 0x73, 0x3e, 0x2c, 0x20,
	0x9a, 0x0f, 0x99, 0xd7, 0xb4, 0x29, 0x0d, 0x1b, 0xc5, 0x2c, 0x78, 0xcf, 0x87, 0x0c, 0xdd, 0x83,
	0x9d, 0x92, 0x3e, 0x92, 0x91, 0xae, 0xd7, 0x72, 0xae, 0x4f, 0x34, 0x7e, 0xe8, 0xa2, 0x07, 0xb0,
	0x17, 0x51, 0xa5, 0xc9, 0x67, 0xc6, 0x06, 0x64, 0x42, 0x69, 0xc7, 0xa6, 0xee, 0x9a, 0xad, 0x5f,
	0x18, 0x1b, 0xbc, 0x28, 0xa8, 0x1d, 0x43, 0x7b, 0x2e, 0xdd, 0x5c, 0xf9, 0xae, 0xcd, 0xbf, 0x3a,
	0x9d, 0x6f, 0xee, 0xfe, 0x6b, 0xf0, 0x26, 0x80, 0x19, 0xda, 0x57, 0x2d, 0xed, 0x76, 0x01, 0x7a,
	0x5b, 0xa6, 0xff, 0x10, 0xf6, 0x27, 0xb8, 0xf2, 0x45, 0x21, 0x5b, 0x09, 0x15, 0xa0, 0x67, 0x93,
	0x0b, 0x7b, 0x00, 0x7b, 0xba, 0xcf, 0xd5, 0xac, 0x94, 0x3d, 0x27, 0xc5, 0x6c, 0xcd, 0x4a, 0x99,
	0x4b, 0x37, 0x52, 0xda, 0x4e, 0xca, 0x74, 0x7e, 0x2e, 0x65, 0x02, 0x98, 0x91, 0xb2, 0xef, 0xa4,
	0x14, 0xa0, 0x59, 0x29, 0x13, 0x5c, 0x59, 0xca, 0x35, 0x27, 0xa5, 0x00, 0x95, 0xa4, 0x3c, 0x82,
	0x6b, 0x73, 0x36, 0x4b, 0x91, 0xc6, 0xa1, 0x77, 0xdd, 0x62, 0xf6, 0xa6, 0x8d, 0xc6, 0x66, 0xcb,
	0x80, 0xe6, 0x04, 0x39, 0x90, 0xe7, 0x40, 0xd3, 0x92, 0x2c, 0xc8, 0x7f, 0x02, 0xde, 0x73, 0xaa,
	0x83, 0x7e, 0x69, 0xc6, 0x3d, 0x1f, 0x9d, 0xf1, 0xd0, 0x4c, 0xc3, 0x1b, 0x50, 0x4b, 0x79, 0xe8,
	0x1e, 0x09, 0x33, 0xdc, 0x9a, 0x78, 0x2b, 0xe5, 0xa1, 0x79, 0x06, 0xfc, 0x7f, 0x2a, 0xcb, 0x70,
	0x2a, 0x41, 0x6f, 0x60, 0xcb, 0xe2, 0x32, 0x95, 0xcf, 0xc4, 0x27, 0x73, 0x33, 0x71, 0x19, 0xf6,
	0xe8, 0x8c, 0x87, 0xa7, 0x99, 0x7a, 0x19, 0x6b, 0x39, 0xc2, 0x9b, 0xa9, 0x5d, 0x74, 0xbe, 0x85,
	0x7a, 0x29, 0x6c, 0xc6, 0xd4, 0x80, 0x8d, 0x8a, 0x31, 0x35, 0x60, 0x23, 0xf3, 0x0b, 0xcf, 0x68,
	0x94, 0xb2, 0x62, 0x94, 0xda, 0xc5, 0x77, 0xd5, 0x6f, 0x2a, 0xfe, 0xf7, 0xb0, 0x3b, 0x2e, 0x41,
	0x15, 0x7b, 0x21, 0xe2, 0xf3, 0xd5, 0x5f, 0x5f, 0x7f, 0x1f, 0xf6, 0x4a, 0x1c, 0x0d, 0x58, 0x61,
	0x76, 0xe1, 0xbf, 0x59, 0x10, 0x56, 0x09, 0x7a, 0x0a, 0x1b, 0x81, 0xf9, 0xce, 0x45, 0xdf, 0x59,
	0xfa, 0x10, 0x14, 0x4c, 0xb0, 0xcb, 0xf7, 0x4f, 0xa1, 0xed, 0x06, 0x43, 0xf9, 0xc5, 0x65, 0x17,
	0xe8, 0x31, 0xac, 0x87, 0x54, 0x53, 0xcb, 0xb3, 0xde, 0x3d, 0xb8, 0xec, 0x81, 0x36, 0xf3, 0x1b,
	0xdb, 0x6c, 0xff, 0xda, 0xa2, 0xd3, 0x54, 0xd2, 0xfd, 0x63, 0x1d, 0xf6, 0x5f, 0xbe, 0x33, 0x27,
	0x14, 0x1b, 0xef, 0x98, 0xcc, 0x78, 0xc0, 0xd0, 0xcf, 0x50, 0x2f, 0xe9, 0x41, 0xb7, 0xe7, 0x0b,
	0x4d, 0xbd, 0xd5, 0x9d, 0xcb, 0x13, 0x54, 0xe2, 0x5f, 0x41, 0x01, 0x5c, 0x9d, 0x7b, 0x35, 0xd1,
	0xbd, 0xcb, 0x70, 0xe3, 0x47, 0xb8, 0xb3, 0x4a, 0x9a, 0x2d, 0x22, 0x60, 0x7f, 0x61, 0x1f, 0xa1,
	0xfb, 0x2b, 0xf6, 0x1b, 0xbb, 0xe8, 0xdc, 0x5f, 0xb9, 0x35, 0xfd, 0x2b, 0xe8, 0x57, 0xd8, 0x9d,
	0xbd, 0x78, 0x74, 0xf7, 0x32, 0xb6, 0x45, 0xcb, 0x74, 0x56, 0xc8, 0x2a, 0x7c, 0x9b, 0xbb, 0xbc,
	0x05, 0xbe, 0x2d, 0x6a, 0x97, 0xce, 0x2a, 0x69, 0xa6, 0xc8, 0xf3, 0x47, 0x1f, 0x1f, 0xf6, 0x44,
	0x44, 0xe3, 0xde, 0xd1, 0x93, 0xae, 0xd6, 0x47, 0x81, 0x18, 0x1e, 0xdb, 0xbf, 0xfb, 0x81, 0x88,
	0x8e, 0x95, 0xeb, 0x09, 0x75, 0x3c, 0x73, 0xd6, 0xa7, 0x4d, 0x9b, 0xf2, 0xe8, 0xbf, 0x00, 0x00,
	0x00, 0xff, 0xff, 0x24, 0x92, 0xd1, 0xcd, 0x49, 0x0c, 0x00, 0x00,
}
