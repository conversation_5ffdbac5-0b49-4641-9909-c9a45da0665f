// Code generated by protoc-gen-go. DO NOT EDIT.
// source: star-tour/star-tour.proto

package star_tour // import "golang.52tt.com/protocol/services/star-tour"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type TourResultType int32

const (
	TourResultType_AllType TourResultType = 0
	TourResultType_Success TourResultType = 1
	TourResultType_Failed  TourResultType = 2
)

var TourResultType_name = map[int32]string{
	0: "AllType",
	1: "Success",
	2: "Failed",
}
var TourResultType_value = map[string]int32{
	"AllType": 0,
	"Success": 1,
	"Failed":  2,
}

func (x TourResultType) String() string {
	return proto.EnumName(TourResultType_name, int32(x))
}
func (TourResultType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{0}
}

// ************* 运营后台 ******************
type StarTourWeekDay int32

const (
	StarTourWeekDay_UnkownDay StarTourWeekDay = 0
	StarTourWeekDay_Monday    StarTourWeekDay = 1
	StarTourWeekDay_Tuesday   StarTourWeekDay = 2
	StarTourWeekDay_Wednesday StarTourWeekDay = 3
	StarTourWeekDay_Thursday  StarTourWeekDay = 4
	StarTourWeekDay_Friday    StarTourWeekDay = 5
	StarTourWeekDay_Saturday  StarTourWeekDay = 6
	StarTourWeekDay_Sunday    StarTourWeekDay = 7
)

var StarTourWeekDay_name = map[int32]string{
	0: "UnkownDay",
	1: "Monday",
	2: "Tuesday",
	3: "Wednesday",
	4: "Thursday",
	5: "Friday",
	6: "Saturday",
	7: "Sunday",
}
var StarTourWeekDay_value = map[string]int32{
	"UnkownDay": 0,
	"Monday":    1,
	"Tuesday":   2,
	"Wednesday": 3,
	"Thursday":  4,
	"Friday":    5,
	"Saturday":  6,
	"Sunday":    7,
}

func (x StarTourWeekDay) String() string {
	return proto.EnumName(StarTourWeekDay_name, int32(x))
}
func (StarTourWeekDay) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{1}
}

// 活动状态
type StarTourStatus int32

const (
	StarTourStatus_AllStatus StarTourStatus = 0
	StarTourStatus_Using     StarTourStatus = 1
	StarTourStatus_Unused    StarTourStatus = 2
	StarTourStatus_Finished  StarTourStatus = 3
)

var StarTourStatus_name = map[int32]string{
	0: "AllStatus",
	1: "Using",
	2: "Unused",
	3: "Finished",
}
var StarTourStatus_value = map[string]int32{
	"AllStatus": 0,
	"Using":     1,
	"Unused":    2,
	"Finished":  3,
}

func (x StarTourStatus) String() string {
	return proto.EnumName(StarTourStatus_name, int32(x))
}
func (StarTourStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{2}
}

type StarTourSupplyType int32

const (
	StarTourSupplyType_UnKnowType StarTourSupplyType = 0
	StarTourSupplyType_TBean      StarTourSupplyType = 1
	StarTourSupplyType_Fragment   StarTourSupplyType = 2
	StarTourSupplyType_RedDiamond StarTourSupplyType = 3
)

var StarTourSupplyType_name = map[int32]string{
	0: "UnKnowType",
	1: "TBean",
	2: "Fragment",
	3: "RedDiamond",
}
var StarTourSupplyType_value = map[string]int32{
	"UnKnowType": 0,
	"TBean":      1,
	"Fragment":   2,
	"RedDiamond": 3,
}

func (x StarTourSupplyType) String() string {
	return proto.EnumName(StarTourSupplyType_name, int32(x))
}
func (StarTourSupplyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{3}
}

// 奖励包裹信息
type StarTAwardInfo struct {
	PackId               uint32   `protobuf:"varint,1,opt,name=pack_id,json=packId,proto3" json:"pack_id,omitempty"`
	PackName             string   `protobuf:"bytes,2,opt,name=pack_name,json=packName,proto3" json:"pack_name,omitempty"`
	PackPic              string   `protobuf:"bytes,3,opt,name=pack_pic,json=packPic,proto3" json:"pack_pic,omitempty"`
	PackAmount           uint32   `protobuf:"varint,4,opt,name=pack_amount,json=packAmount,proto3" json:"pack_amount,omitempty"`
	UnitPrice            uint32   `protobuf:"varint,5,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"`
	FinTime              uint32   `protobuf:"varint,6,opt,name=fin_time,json=finTime,proto3" json:"fin_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StarTAwardInfo) Reset()         { *m = StarTAwardInfo{} }
func (m *StarTAwardInfo) String() string { return proto.CompactTextString(m) }
func (*StarTAwardInfo) ProtoMessage()    {}
func (*StarTAwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{0}
}
func (m *StarTAwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTAwardInfo.Unmarshal(m, b)
}
func (m *StarTAwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTAwardInfo.Marshal(b, m, deterministic)
}
func (dst *StarTAwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTAwardInfo.Merge(dst, src)
}
func (m *StarTAwardInfo) XXX_Size() int {
	return xxx_messageInfo_StarTAwardInfo.Size(m)
}
func (m *StarTAwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTAwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StarTAwardInfo proto.InternalMessageInfo

func (m *StarTAwardInfo) GetPackId() uint32 {
	if m != nil {
		return m.PackId
	}
	return 0
}

func (m *StarTAwardInfo) GetPackName() string {
	if m != nil {
		return m.PackName
	}
	return ""
}

func (m *StarTAwardInfo) GetPackPic() string {
	if m != nil {
		return m.PackPic
	}
	return ""
}

func (m *StarTAwardInfo) GetPackAmount() uint32 {
	if m != nil {
		return m.PackAmount
	}
	return 0
}

func (m *StarTAwardInfo) GetUnitPrice() uint32 {
	if m != nil {
		return m.UnitPrice
	}
	return 0
}

func (m *StarTAwardInfo) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

type StarTUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	HdImg                string   `protobuf:"bytes,3,opt,name=hd_img,json=hdImg,proto3" json:"hd_img,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StarTUserInfo) Reset()         { *m = StarTUserInfo{} }
func (m *StarTUserInfo) String() string { return proto.CompactTextString(m) }
func (*StarTUserInfo) ProtoMessage()    {}
func (*StarTUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{1}
}
func (m *StarTUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTUserInfo.Unmarshal(m, b)
}
func (m *StarTUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTUserInfo.Marshal(b, m, deterministic)
}
func (dst *StarTUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTUserInfo.Merge(dst, src)
}
func (m *StarTUserInfo) XXX_Size() int {
	return xxx_messageInfo_StarTUserInfo.Size(m)
}
func (m *StarTUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StarTUserInfo proto.InternalMessageInfo

func (m *StarTUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StarTUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *StarTUserInfo) GetHdImg() string {
	if m != nil {
		return m.HdImg
	}
	return ""
}

// 上期回顾
type LastSessionReview struct {
	SessionId            uint32          `protobuf:"varint,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	SessionTime          uint32          `protobuf:"varint,2,opt,name=session_time,json=sessionTime,proto3" json:"session_time,omitempty"`
	AwardPack            *StarTAwardInfo `protobuf:"bytes,3,opt,name=award_pack,json=awardPack,proto3" json:"award_pack,omitempty"`
	UserInfo             *StarTUserInfo  `protobuf:"bytes,4,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *LastSessionReview) Reset()         { *m = LastSessionReview{} }
func (m *LastSessionReview) String() string { return proto.CompactTextString(m) }
func (*LastSessionReview) ProtoMessage()    {}
func (*LastSessionReview) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{2}
}
func (m *LastSessionReview) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LastSessionReview.Unmarshal(m, b)
}
func (m *LastSessionReview) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LastSessionReview.Marshal(b, m, deterministic)
}
func (dst *LastSessionReview) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LastSessionReview.Merge(dst, src)
}
func (m *LastSessionReview) XXX_Size() int {
	return xxx_messageInfo_LastSessionReview.Size(m)
}
func (m *LastSessionReview) XXX_DiscardUnknown() {
	xxx_messageInfo_LastSessionReview.DiscardUnknown(m)
}

var xxx_messageInfo_LastSessionReview proto.InternalMessageInfo

func (m *LastSessionReview) GetSessionId() uint32 {
	if m != nil {
		return m.SessionId
	}
	return 0
}

func (m *LastSessionReview) GetSessionTime() uint32 {
	if m != nil {
		return m.SessionTime
	}
	return 0
}

func (m *LastSessionReview) GetAwardPack() *StarTAwardInfo {
	if m != nil {
		return m.AwardPack
	}
	return nil
}

func (m *LastSessionReview) GetUserInfo() *StarTUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

// 下期预告
type NextSessionForecast struct {
	SessionId            uint32          `protobuf:"varint,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	AwardPack            *StarTAwardInfo `protobuf:"bytes,2,opt,name=award_pack,json=awardPack,proto3" json:"award_pack,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *NextSessionForecast) Reset()         { *m = NextSessionForecast{} }
func (m *NextSessionForecast) String() string { return proto.CompactTextString(m) }
func (*NextSessionForecast) ProtoMessage()    {}
func (*NextSessionForecast) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{3}
}
func (m *NextSessionForecast) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NextSessionForecast.Unmarshal(m, b)
}
func (m *NextSessionForecast) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NextSessionForecast.Marshal(b, m, deterministic)
}
func (dst *NextSessionForecast) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NextSessionForecast.Merge(dst, src)
}
func (m *NextSessionForecast) XXX_Size() int {
	return xxx_messageInfo_NextSessionForecast.Size(m)
}
func (m *NextSessionForecast) XXX_DiscardUnknown() {
	xxx_messageInfo_NextSessionForecast.DiscardUnknown(m)
}

var xxx_messageInfo_NextSessionForecast proto.InternalMessageInfo

func (m *NextSessionForecast) GetSessionId() uint32 {
	if m != nil {
		return m.SessionId
	}
	return 0
}

func (m *NextSessionForecast) GetAwardPack() *StarTAwardInfo {
	if m != nil {
		return m.AwardPack
	}
	return nil
}

// 获取星际巡航信息
type GetStatTourInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SessionId            uint32   `protobuf:"varint,2,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	NeedPreAndFore       bool     `protobuf:"varint,3,opt,name=need_pre_and_fore,json=needPreAndFore,proto3" json:"need_pre_and_fore,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStatTourInfoReq) Reset()         { *m = GetStatTourInfoReq{} }
func (m *GetStatTourInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetStatTourInfoReq) ProtoMessage()    {}
func (*GetStatTourInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{4}
}
func (m *GetStatTourInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStatTourInfoReq.Unmarshal(m, b)
}
func (m *GetStatTourInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStatTourInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetStatTourInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStatTourInfoReq.Merge(dst, src)
}
func (m *GetStatTourInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetStatTourInfoReq.Size(m)
}
func (m *GetStatTourInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStatTourInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStatTourInfoReq proto.InternalMessageInfo

func (m *GetStatTourInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetStatTourInfoReq) GetSessionId() uint32 {
	if m != nil {
		return m.SessionId
	}
	return 0
}

func (m *GetStatTourInfoReq) GetNeedPreAndFore() bool {
	if m != nil {
		return m.NeedPreAndFore
	}
	return false
}

type GetStatTourInfoResp struct {
	SessionId            uint32               `protobuf:"varint,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	CountDown            uint32               `protobuf:"varint,2,opt,name=count_down,json=countDown,proto3" json:"count_down,omitempty"`
	AwardPack            *StarTAwardInfo      `protobuf:"bytes,3,opt,name=award_pack,json=awardPack,proto3" json:"award_pack,omitempty"`
	InvestProgress       uint32               `protobuf:"varint,4,opt,name=invest_progress,json=investProgress,proto3" json:"invest_progress,omitempty"`
	UserCount            uint32               `protobuf:"varint,5,opt,name=user_count,json=userCount,proto3" json:"user_count,omitempty"`
	MySupply             uint32               `protobuf:"varint,6,opt,name=my_supply,json=mySupply,proto3" json:"my_supply,omitempty"`
	MinInvest            uint32               `protobuf:"varint,7,opt,name=min_invest,json=minInvest,proto3" json:"min_invest,omitempty"`
	SessionSupLimit      uint32               `protobuf:"varint,8,opt,name=session_sup_limit,json=sessionSupLimit,proto3" json:"session_sup_limit,omitempty"`
	LatestPartitions     []*StarTUserInfo     `protobuf:"bytes,9,rep,name=latest_partitions,json=latestPartitions,proto3" json:"latest_partitions,omitempty"`
	LastS                *LastSessionReview   `protobuf:"bytes,10,opt,name=last_s,json=lastS,proto3" json:"last_s,omitempty"`
	NextS                *NextSessionForecast `protobuf:"bytes,11,opt,name=next_s,json=nextS,proto3" json:"next_s,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetStatTourInfoResp) Reset()         { *m = GetStatTourInfoResp{} }
func (m *GetStatTourInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetStatTourInfoResp) ProtoMessage()    {}
func (*GetStatTourInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{5}
}
func (m *GetStatTourInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStatTourInfoResp.Unmarshal(m, b)
}
func (m *GetStatTourInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStatTourInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetStatTourInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStatTourInfoResp.Merge(dst, src)
}
func (m *GetStatTourInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetStatTourInfoResp.Size(m)
}
func (m *GetStatTourInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStatTourInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStatTourInfoResp proto.InternalMessageInfo

func (m *GetStatTourInfoResp) GetSessionId() uint32 {
	if m != nil {
		return m.SessionId
	}
	return 0
}

func (m *GetStatTourInfoResp) GetCountDown() uint32 {
	if m != nil {
		return m.CountDown
	}
	return 0
}

func (m *GetStatTourInfoResp) GetAwardPack() *StarTAwardInfo {
	if m != nil {
		return m.AwardPack
	}
	return nil
}

func (m *GetStatTourInfoResp) GetInvestProgress() uint32 {
	if m != nil {
		return m.InvestProgress
	}
	return 0
}

func (m *GetStatTourInfoResp) GetUserCount() uint32 {
	if m != nil {
		return m.UserCount
	}
	return 0
}

func (m *GetStatTourInfoResp) GetMySupply() uint32 {
	if m != nil {
		return m.MySupply
	}
	return 0
}

func (m *GetStatTourInfoResp) GetMinInvest() uint32 {
	if m != nil {
		return m.MinInvest
	}
	return 0
}

func (m *GetStatTourInfoResp) GetSessionSupLimit() uint32 {
	if m != nil {
		return m.SessionSupLimit
	}
	return 0
}

func (m *GetStatTourInfoResp) GetLatestPartitions() []*StarTUserInfo {
	if m != nil {
		return m.LatestPartitions
	}
	return nil
}

func (m *GetStatTourInfoResp) GetLastS() *LastSessionReview {
	if m != nil {
		return m.LastS
	}
	return nil
}

func (m *GetStatTourInfoResp) GetNextS() *NextSessionForecast {
	if m != nil {
		return m.NextS
	}
	return nil
}

type SupplyInfo struct {
	UserItemId           uint32   `protobuf:"varint,1,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
	ItemNum              uint32   `protobuf:"varint,2,opt,name=item_num,json=itemNum,proto3" json:"item_num,omitempty"`
	GiftId               uint32   `protobuf:"varint,3,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftType             uint32   `protobuf:"varint,4,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	GiftName             string   `protobuf:"bytes,5,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GiftPrice            uint32   `protobuf:"varint,6,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price,omitempty"`
	GiftUrl              string   `protobuf:"bytes,7,opt,name=gift_url,json=giftUrl,proto3" json:"gift_url,omitempty"`
	FinTime              uint32   `protobuf:"varint,8,opt,name=fin_time,json=finTime,proto3" json:"fin_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SupplyInfo) Reset()         { *m = SupplyInfo{} }
func (m *SupplyInfo) String() string { return proto.CompactTextString(m) }
func (*SupplyInfo) ProtoMessage()    {}
func (*SupplyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{6}
}
func (m *SupplyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SupplyInfo.Unmarshal(m, b)
}
func (m *SupplyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SupplyInfo.Marshal(b, m, deterministic)
}
func (dst *SupplyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SupplyInfo.Merge(dst, src)
}
func (m *SupplyInfo) XXX_Size() int {
	return xxx_messageInfo_SupplyInfo.Size(m)
}
func (m *SupplyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SupplyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SupplyInfo proto.InternalMessageInfo

func (m *SupplyInfo) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *SupplyInfo) GetItemNum() uint32 {
	if m != nil {
		return m.ItemNum
	}
	return 0
}

func (m *SupplyInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *SupplyInfo) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *SupplyInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *SupplyInfo) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *SupplyInfo) GetGiftUrl() string {
	if m != nil {
		return m.GiftUrl
	}
	return ""
}

func (m *SupplyInfo) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

// 获取用户补给库存（默认先过期的排在前面）
type GetUserSupplyListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSupplyListReq) Reset()         { *m = GetUserSupplyListReq{} }
func (m *GetUserSupplyListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserSupplyListReq) ProtoMessage()    {}
func (*GetUserSupplyListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{7}
}
func (m *GetUserSupplyListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSupplyListReq.Unmarshal(m, b)
}
func (m *GetUserSupplyListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSupplyListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserSupplyListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSupplyListReq.Merge(dst, src)
}
func (m *GetUserSupplyListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserSupplyListReq.Size(m)
}
func (m *GetUserSupplyListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSupplyListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSupplyListReq proto.InternalMessageInfo

func (m *GetUserSupplyListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserSupplyListResp struct {
	MySupplyList         []*SupplyInfo `protobuf:"bytes,1,rep,name=my_supply_list,json=mySupplyList,proto3" json:"my_supply_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserSupplyListResp) Reset()         { *m = GetUserSupplyListResp{} }
func (m *GetUserSupplyListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserSupplyListResp) ProtoMessage()    {}
func (*GetUserSupplyListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{8}
}
func (m *GetUserSupplyListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSupplyListResp.Unmarshal(m, b)
}
func (m *GetUserSupplyListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSupplyListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserSupplyListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSupplyListResp.Merge(dst, src)
}
func (m *GetUserSupplyListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserSupplyListResp.Size(m)
}
func (m *GetUserSupplyListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSupplyListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSupplyListResp proto.InternalMessageInfo

func (m *GetUserSupplyListResp) GetMySupplyList() []*SupplyInfo {
	if m != nil {
		return m.MySupplyList
	}
	return nil
}

type CostSupplyInfo struct {
	UserItemId           uint32   `protobuf:"varint,1,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
	GiftId               uint32   `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftType             uint32   `protobuf:"varint,3,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	GiftNum              uint32   `protobuf:"varint,4,opt,name=gift_num,json=giftNum,proto3" json:"gift_num,omitempty"`
	GiftPrice            uint32   `protobuf:"varint,5,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CostSupplyInfo) Reset()         { *m = CostSupplyInfo{} }
func (m *CostSupplyInfo) String() string { return proto.CompactTextString(m) }
func (*CostSupplyInfo) ProtoMessage()    {}
func (*CostSupplyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{9}
}
func (m *CostSupplyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CostSupplyInfo.Unmarshal(m, b)
}
func (m *CostSupplyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CostSupplyInfo.Marshal(b, m, deterministic)
}
func (dst *CostSupplyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CostSupplyInfo.Merge(dst, src)
}
func (m *CostSupplyInfo) XXX_Size() int {
	return xxx_messageInfo_CostSupplyInfo.Size(m)
}
func (m *CostSupplyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CostSupplyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CostSupplyInfo proto.InternalMessageInfo

func (m *CostSupplyInfo) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *CostSupplyInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *CostSupplyInfo) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *CostSupplyInfo) GetGiftNum() uint32 {
	if m != nil {
		return m.GiftNum
	}
	return 0
}

func (m *CostSupplyInfo) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

// 去探险
type DoInvestReq struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32            `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SessionId            uint32            `protobuf:"varint,3,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	SupplyList           []*CostSupplyInfo `protobuf:"bytes,4,rep,name=supply_list,json=supplyList,proto3" json:"supply_list,omitempty"`
	Cost                 uint32            `protobuf:"varint,5,opt,name=cost,proto3" json:"cost,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *DoInvestReq) Reset()         { *m = DoInvestReq{} }
func (m *DoInvestReq) String() string { return proto.CompactTextString(m) }
func (*DoInvestReq) ProtoMessage()    {}
func (*DoInvestReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{10}
}
func (m *DoInvestReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoInvestReq.Unmarshal(m, b)
}
func (m *DoInvestReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoInvestReq.Marshal(b, m, deterministic)
}
func (dst *DoInvestReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoInvestReq.Merge(dst, src)
}
func (m *DoInvestReq) XXX_Size() int {
	return xxx_messageInfo_DoInvestReq.Size(m)
}
func (m *DoInvestReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DoInvestReq.DiscardUnknown(m)
}

var xxx_messageInfo_DoInvestReq proto.InternalMessageInfo

func (m *DoInvestReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DoInvestReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DoInvestReq) GetSessionId() uint32 {
	if m != nil {
		return m.SessionId
	}
	return 0
}

func (m *DoInvestReq) GetSupplyList() []*CostSupplyInfo {
	if m != nil {
		return m.SupplyList
	}
	return nil
}

func (m *DoInvestReq) GetCost() uint32 {
	if m != nil {
		return m.Cost
	}
	return 0
}

type DoInvestResp struct {
	SessionId            uint32   `protobuf:"varint,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	MySupply             uint32   `protobuf:"varint,2,opt,name=my_supply,json=mySupply,proto3" json:"my_supply,omitempty"`
	SessionSupLimit      uint32   `protobuf:"varint,3,opt,name=session_sup_limit,json=sessionSupLimit,proto3" json:"session_sup_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoInvestResp) Reset()         { *m = DoInvestResp{} }
func (m *DoInvestResp) String() string { return proto.CompactTextString(m) }
func (*DoInvestResp) ProtoMessage()    {}
func (*DoInvestResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{11}
}
func (m *DoInvestResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoInvestResp.Unmarshal(m, b)
}
func (m *DoInvestResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoInvestResp.Marshal(b, m, deterministic)
}
func (dst *DoInvestResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoInvestResp.Merge(dst, src)
}
func (m *DoInvestResp) XXX_Size() int {
	return xxx_messageInfo_DoInvestResp.Size(m)
}
func (m *DoInvestResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DoInvestResp.DiscardUnknown(m)
}

var xxx_messageInfo_DoInvestResp proto.InternalMessageInfo

func (m *DoInvestResp) GetSessionId() uint32 {
	if m != nil {
		return m.SessionId
	}
	return 0
}

func (m *DoInvestResp) GetMySupply() uint32 {
	if m != nil {
		return m.MySupply
	}
	return 0
}

func (m *DoInvestResp) GetSessionSupLimit() uint32 {
	if m != nil {
		return m.SessionSupLimit
	}
	return 0
}

// 获取用户巡航记录
type GetMyTourRecordReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TourResult           uint32   `protobuf:"varint,2,opt,name=tour_result,json=tourResult,proto3" json:"tour_result,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMyTourRecordReq) Reset()         { *m = GetMyTourRecordReq{} }
func (m *GetMyTourRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetMyTourRecordReq) ProtoMessage()    {}
func (*GetMyTourRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{12}
}
func (m *GetMyTourRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyTourRecordReq.Unmarshal(m, b)
}
func (m *GetMyTourRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyTourRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetMyTourRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyTourRecordReq.Merge(dst, src)
}
func (m *GetMyTourRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetMyTourRecordReq.Size(m)
}
func (m *GetMyTourRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyTourRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyTourRecordReq proto.InternalMessageInfo

func (m *GetMyTourRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMyTourRecordReq) GetTourResult() uint32 {
	if m != nil {
		return m.TourResult
	}
	return 0
}

func (m *GetMyTourRecordReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetMyTourRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type MyTourRecord struct {
	Uid         uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SessionId   uint32 `protobuf:"varint,2,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	SessionTime uint32 `protobuf:"varint,3,opt,name=session_time,json=sessionTime,proto3" json:"session_time,omitempty"`
	TourResult  bool   `protobuf:"varint,4,opt,name=tour_result,json=tourResult,proto3" json:"tour_result,omitempty"`
	// StarTAwardInfo award_pack = 5;  // 上期奖品
	// StarTUserInfo user_info = 6;    // 中奖用户
	BingoAward           string   `protobuf:"bytes,5,opt,name=bingo_award,json=bingoAward,proto3" json:"bingo_award,omitempty"`
	LuckyUser            string   `protobuf:"bytes,6,opt,name=lucky_user,json=luckyUser,proto3" json:"lucky_user,omitempty"`
	MyInvest             uint32   `protobuf:"varint,7,opt,name=my_invest,json=myInvest,proto3" json:"my_invest,omitempty"`
	MySupplyDec          string   `protobuf:"bytes,8,opt,name=my_supply_dec,json=mySupplyDec,proto3" json:"my_supply_dec,omitempty"`
	MyAwardDec           string   `protobuf:"bytes,9,opt,name=my_award_dec,json=myAwardDec,proto3" json:"my_award_dec,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MyTourRecord) Reset()         { *m = MyTourRecord{} }
func (m *MyTourRecord) String() string { return proto.CompactTextString(m) }
func (*MyTourRecord) ProtoMessage()    {}
func (*MyTourRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{13}
}
func (m *MyTourRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MyTourRecord.Unmarshal(m, b)
}
func (m *MyTourRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MyTourRecord.Marshal(b, m, deterministic)
}
func (dst *MyTourRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MyTourRecord.Merge(dst, src)
}
func (m *MyTourRecord) XXX_Size() int {
	return xxx_messageInfo_MyTourRecord.Size(m)
}
func (m *MyTourRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_MyTourRecord.DiscardUnknown(m)
}

var xxx_messageInfo_MyTourRecord proto.InternalMessageInfo

func (m *MyTourRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MyTourRecord) GetSessionId() uint32 {
	if m != nil {
		return m.SessionId
	}
	return 0
}

func (m *MyTourRecord) GetSessionTime() uint32 {
	if m != nil {
		return m.SessionTime
	}
	return 0
}

func (m *MyTourRecord) GetTourResult() bool {
	if m != nil {
		return m.TourResult
	}
	return false
}

func (m *MyTourRecord) GetBingoAward() string {
	if m != nil {
		return m.BingoAward
	}
	return ""
}

func (m *MyTourRecord) GetLuckyUser() string {
	if m != nil {
		return m.LuckyUser
	}
	return ""
}

func (m *MyTourRecord) GetMyInvest() uint32 {
	if m != nil {
		return m.MyInvest
	}
	return 0
}

func (m *MyTourRecord) GetMySupplyDec() string {
	if m != nil {
		return m.MySupplyDec
	}
	return ""
}

func (m *MyTourRecord) GetMyAwardDec() string {
	if m != nil {
		return m.MyAwardDec
	}
	return ""
}

type GetMyTourRecordResp struct {
	MyRecordList         []*MyTourRecord `protobuf:"bytes,1,rep,name=my_record_list,json=myRecordList,proto3" json:"my_record_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetMyTourRecordResp) Reset()         { *m = GetMyTourRecordResp{} }
func (m *GetMyTourRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetMyTourRecordResp) ProtoMessage()    {}
func (*GetMyTourRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{14}
}
func (m *GetMyTourRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyTourRecordResp.Unmarshal(m, b)
}
func (m *GetMyTourRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyTourRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetMyTourRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyTourRecordResp.Merge(dst, src)
}
func (m *GetMyTourRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetMyTourRecordResp.Size(m)
}
func (m *GetMyTourRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyTourRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyTourRecordResp proto.InternalMessageInfo

func (m *GetMyTourRecordResp) GetMyRecordList() []*MyTourRecord {
	if m != nil {
		return m.MyRecordList
	}
	return nil
}

// 往期回顾
type GetAllTourHistoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllTourHistoryReq) Reset()         { *m = GetAllTourHistoryReq{} }
func (m *GetAllTourHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetAllTourHistoryReq) ProtoMessage()    {}
func (*GetAllTourHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{15}
}
func (m *GetAllTourHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllTourHistoryReq.Unmarshal(m, b)
}
func (m *GetAllTourHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllTourHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetAllTourHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllTourHistoryReq.Merge(dst, src)
}
func (m *GetAllTourHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetAllTourHistoryReq.Size(m)
}
func (m *GetAllTourHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllTourHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllTourHistoryReq proto.InternalMessageInfo

func (m *GetAllTourHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAllTourHistoryReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetAllTourHistoryReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetAllTourHistoryResp struct {
	ReviewList           []*LastSessionReview `protobuf:"bytes,1,rep,name=review_list,json=reviewList,proto3" json:"review_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetAllTourHistoryResp) Reset()         { *m = GetAllTourHistoryResp{} }
func (m *GetAllTourHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetAllTourHistoryResp) ProtoMessage()    {}
func (*GetAllTourHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{16}
}
func (m *GetAllTourHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllTourHistoryResp.Unmarshal(m, b)
}
func (m *GetAllTourHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllTourHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetAllTourHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllTourHistoryResp.Merge(dst, src)
}
func (m *GetAllTourHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetAllTourHistoryResp.Size(m)
}
func (m *GetAllTourHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllTourHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllTourHistoryResp proto.InternalMessageInfo

func (m *GetAllTourHistoryResp) GetReviewList() []*LastSessionReview {
	if m != nil {
		return m.ReviewList
	}
	return nil
}

// 奖励包裹信息
type AwardPackInfo struct {
	PackId               uint32   `protobuf:"varint,1,opt,name=pack_id,json=packId,proto3" json:"pack_id,omitempty"`
	PackName             string   `protobuf:"bytes,2,opt,name=pack_name,json=packName,proto3" json:"pack_name,omitempty"`
	PackPic              string   `protobuf:"bytes,3,opt,name=pack_pic,json=packPic,proto3" json:"pack_pic,omitempty"`
	PackAmount           uint32   `protobuf:"varint,4,opt,name=pack_amount,json=packAmount,proto3" json:"pack_amount,omitempty"`
	UnitPrice            uint32   `protobuf:"varint,5,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"`
	FinTime              uint32   `protobuf:"varint,6,opt,name=fin_time,json=finTime,proto3" json:"fin_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardPackInfo) Reset()         { *m = AwardPackInfo{} }
func (m *AwardPackInfo) String() string { return proto.CompactTextString(m) }
func (*AwardPackInfo) ProtoMessage()    {}
func (*AwardPackInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{17}
}
func (m *AwardPackInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardPackInfo.Unmarshal(m, b)
}
func (m *AwardPackInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardPackInfo.Marshal(b, m, deterministic)
}
func (dst *AwardPackInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardPackInfo.Merge(dst, src)
}
func (m *AwardPackInfo) XXX_Size() int {
	return xxx_messageInfo_AwardPackInfo.Size(m)
}
func (m *AwardPackInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardPackInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AwardPackInfo proto.InternalMessageInfo

func (m *AwardPackInfo) GetPackId() uint32 {
	if m != nil {
		return m.PackId
	}
	return 0
}

func (m *AwardPackInfo) GetPackName() string {
	if m != nil {
		return m.PackName
	}
	return ""
}

func (m *AwardPackInfo) GetPackPic() string {
	if m != nil {
		return m.PackPic
	}
	return ""
}

func (m *AwardPackInfo) GetPackAmount() uint32 {
	if m != nil {
		return m.PackAmount
	}
	return 0
}

func (m *AwardPackInfo) GetUnitPrice() uint32 {
	if m != nil {
		return m.UnitPrice
	}
	return 0
}

func (m *AwardPackInfo) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

// 获取中奖轮播信息
type GetRecentWinningRecordsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecentWinningRecordsReq) Reset()         { *m = GetRecentWinningRecordsReq{} }
func (m *GetRecentWinningRecordsReq) String() string { return proto.CompactTextString(m) }
func (*GetRecentWinningRecordsReq) ProtoMessage()    {}
func (*GetRecentWinningRecordsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{18}
}
func (m *GetRecentWinningRecordsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecentWinningRecordsReq.Unmarshal(m, b)
}
func (m *GetRecentWinningRecordsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecentWinningRecordsReq.Marshal(b, m, deterministic)
}
func (dst *GetRecentWinningRecordsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecentWinningRecordsReq.Merge(dst, src)
}
func (m *GetRecentWinningRecordsReq) XXX_Size() int {
	return xxx_messageInfo_GetRecentWinningRecordsReq.Size(m)
}
func (m *GetRecentWinningRecordsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecentWinningRecordsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecentWinningRecordsReq proto.InternalMessageInfo

func (m *GetRecentWinningRecordsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRecentWinningRecordsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type RecentWinningRecord struct {
	UserInfo             *StarTUserInfo  `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	Award                *StarTAwardInfo `protobuf:"bytes,2,opt,name=award,proto3" json:"award,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *RecentWinningRecord) Reset()         { *m = RecentWinningRecord{} }
func (m *RecentWinningRecord) String() string { return proto.CompactTextString(m) }
func (*RecentWinningRecord) ProtoMessage()    {}
func (*RecentWinningRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{19}
}
func (m *RecentWinningRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecentWinningRecord.Unmarshal(m, b)
}
func (m *RecentWinningRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecentWinningRecord.Marshal(b, m, deterministic)
}
func (dst *RecentWinningRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecentWinningRecord.Merge(dst, src)
}
func (m *RecentWinningRecord) XXX_Size() int {
	return xxx_messageInfo_RecentWinningRecord.Size(m)
}
func (m *RecentWinningRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_RecentWinningRecord.DiscardUnknown(m)
}

var xxx_messageInfo_RecentWinningRecord proto.InternalMessageInfo

func (m *RecentWinningRecord) GetUserInfo() *StarTUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *RecentWinningRecord) GetAward() *StarTAwardInfo {
	if m != nil {
		return m.Award
	}
	return nil
}

type GetRecentWinningRecordsResp struct {
	Records              []*RecentWinningRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetRecentWinningRecordsResp) Reset()         { *m = GetRecentWinningRecordsResp{} }
func (m *GetRecentWinningRecordsResp) String() string { return proto.CompactTextString(m) }
func (*GetRecentWinningRecordsResp) ProtoMessage()    {}
func (*GetRecentWinningRecordsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{20}
}
func (m *GetRecentWinningRecordsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecentWinningRecordsResp.Unmarshal(m, b)
}
func (m *GetRecentWinningRecordsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecentWinningRecordsResp.Marshal(b, m, deterministic)
}
func (dst *GetRecentWinningRecordsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecentWinningRecordsResp.Merge(dst, src)
}
func (m *GetRecentWinningRecordsResp) XXX_Size() int {
	return xxx_messageInfo_GetRecentWinningRecordsResp.Size(m)
}
func (m *GetRecentWinningRecordsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecentWinningRecordsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecentWinningRecordsResp proto.InternalMessageInfo

func (m *GetRecentWinningRecordsResp) GetRecords() []*RecentWinningRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

// 星际礼物信息
type StarTourPrizeInfo struct {
	BgId                 uint32   `protobuf:"varint,1,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	Weight               uint32   `protobuf:"varint,2,opt,name=weight,proto3" json:"weight,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StarTourPrizeInfo) Reset()         { *m = StarTourPrizeInfo{} }
func (m *StarTourPrizeInfo) String() string { return proto.CompactTextString(m) }
func (*StarTourPrizeInfo) ProtoMessage()    {}
func (*StarTourPrizeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{21}
}
func (m *StarTourPrizeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTourPrizeInfo.Unmarshal(m, b)
}
func (m *StarTourPrizeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTourPrizeInfo.Marshal(b, m, deterministic)
}
func (dst *StarTourPrizeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTourPrizeInfo.Merge(dst, src)
}
func (m *StarTourPrizeInfo) XXX_Size() int {
	return xxx_messageInfo_StarTourPrizeInfo.Size(m)
}
func (m *StarTourPrizeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTourPrizeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StarTourPrizeInfo proto.InternalMessageInfo

func (m *StarTourPrizeInfo) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *StarTourPrizeInfo) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

// 活动配置
type StarTourConf struct {
	ActivityBegin        uint32               `protobuf:"varint,1,opt,name=activity_begin,json=activityBegin,proto3" json:"activity_begin,omitempty"`
	ActivityEnd          uint32               `protobuf:"varint,2,opt,name=activity_end,json=activityEnd,proto3" json:"activity_end,omitempty"`
	DayBegin             uint32               `protobuf:"varint,3,opt,name=day_begin,json=dayBegin,proto3" json:"day_begin,omitempty"`
	DayEnd               uint32               `protobuf:"varint,4,opt,name=day_end,json=dayEnd,proto3" json:"day_end,omitempty"`
	WDayList             []uint32             `protobuf:"varint,5,rep,packed,name=w_day_list,json=wDayList,proto3" json:"w_day_list,omitempty"`
	PrizeList            []*StarTourPrizeInfo `protobuf:"bytes,6,rep,name=prize_list,json=prizeList,proto3" json:"prize_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *StarTourConf) Reset()         { *m = StarTourConf{} }
func (m *StarTourConf) String() string { return proto.CompactTextString(m) }
func (*StarTourConf) ProtoMessage()    {}
func (*StarTourConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{22}
}
func (m *StarTourConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTourConf.Unmarshal(m, b)
}
func (m *StarTourConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTourConf.Marshal(b, m, deterministic)
}
func (dst *StarTourConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTourConf.Merge(dst, src)
}
func (m *StarTourConf) XXX_Size() int {
	return xxx_messageInfo_StarTourConf.Size(m)
}
func (m *StarTourConf) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTourConf.DiscardUnknown(m)
}

var xxx_messageInfo_StarTourConf proto.InternalMessageInfo

func (m *StarTourConf) GetActivityBegin() uint32 {
	if m != nil {
		return m.ActivityBegin
	}
	return 0
}

func (m *StarTourConf) GetActivityEnd() uint32 {
	if m != nil {
		return m.ActivityEnd
	}
	return 0
}

func (m *StarTourConf) GetDayBegin() uint32 {
	if m != nil {
		return m.DayBegin
	}
	return 0
}

func (m *StarTourConf) GetDayEnd() uint32 {
	if m != nil {
		return m.DayEnd
	}
	return 0
}

func (m *StarTourConf) GetWDayList() []uint32 {
	if m != nil {
		return m.WDayList
	}
	return nil
}

func (m *StarTourConf) GetPrizeList() []*StarTourPrizeInfo {
	if m != nil {
		return m.PrizeList
	}
	return nil
}

type SetStarTourConfReq struct {
	Conf                 *StarTourConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetStarTourConfReq) Reset()         { *m = SetStarTourConfReq{} }
func (m *SetStarTourConfReq) String() string { return proto.CompactTextString(m) }
func (*SetStarTourConfReq) ProtoMessage()    {}
func (*SetStarTourConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{23}
}
func (m *SetStarTourConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetStarTourConfReq.Unmarshal(m, b)
}
func (m *SetStarTourConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetStarTourConfReq.Marshal(b, m, deterministic)
}
func (dst *SetStarTourConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetStarTourConfReq.Merge(dst, src)
}
func (m *SetStarTourConfReq) XXX_Size() int {
	return xxx_messageInfo_SetStarTourConfReq.Size(m)
}
func (m *SetStarTourConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetStarTourConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetStarTourConfReq proto.InternalMessageInfo

func (m *SetStarTourConfReq) GetConf() *StarTourConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type SetStarTourConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetStarTourConfResp) Reset()         { *m = SetStarTourConfResp{} }
func (m *SetStarTourConfResp) String() string { return proto.CompactTextString(m) }
func (*SetStarTourConfResp) ProtoMessage()    {}
func (*SetStarTourConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{24}
}
func (m *SetStarTourConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetStarTourConfResp.Unmarshal(m, b)
}
func (m *SetStarTourConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetStarTourConfResp.Marshal(b, m, deterministic)
}
func (dst *SetStarTourConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetStarTourConfResp.Merge(dst, src)
}
func (m *SetStarTourConfResp) XXX_Size() int {
	return xxx_messageInfo_SetStarTourConfResp.Size(m)
}
func (m *SetStarTourConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetStarTourConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetStarTourConfResp proto.InternalMessageInfo

type GetStarTourConfReq struct {
	Status               StarTourStatus `protobuf:"varint,1,opt,name=status,proto3,enum=star_tour.StarTourStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetStarTourConfReq) Reset()         { *m = GetStarTourConfReq{} }
func (m *GetStarTourConfReq) String() string { return proto.CompactTextString(m) }
func (*GetStarTourConfReq) ProtoMessage()    {}
func (*GetStarTourConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{25}
}
func (m *GetStarTourConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTourConfReq.Unmarshal(m, b)
}
func (m *GetStarTourConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTourConfReq.Marshal(b, m, deterministic)
}
func (dst *GetStarTourConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTourConfReq.Merge(dst, src)
}
func (m *GetStarTourConfReq) XXX_Size() int {
	return xxx_messageInfo_GetStarTourConfReq.Size(m)
}
func (m *GetStarTourConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTourConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTourConfReq proto.InternalMessageInfo

func (m *GetStarTourConfReq) GetStatus() StarTourStatus {
	if m != nil {
		return m.Status
	}
	return StarTourStatus_AllStatus
}

type GetStarTourConfResp struct {
	ConfList             []*StarTourConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetStarTourConfResp) Reset()         { *m = GetStarTourConfResp{} }
func (m *GetStarTourConfResp) String() string { return proto.CompactTextString(m) }
func (*GetStarTourConfResp) ProtoMessage()    {}
func (*GetStarTourConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{26}
}
func (m *GetStarTourConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTourConfResp.Unmarshal(m, b)
}
func (m *GetStarTourConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTourConfResp.Marshal(b, m, deterministic)
}
func (dst *GetStarTourConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTourConfResp.Merge(dst, src)
}
func (m *GetStarTourConfResp) XXX_Size() int {
	return xxx_messageInfo_GetStarTourConfResp.Size(m)
}
func (m *GetStarTourConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTourConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTourConfResp proto.InternalMessageInfo

func (m *GetStarTourConfResp) GetConfList() []*StarTourConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type UpdateStarTourConfReq struct {
	ConfId               uint32        `protobuf:"varint,1,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	Conf                 *StarTourConf `protobuf:"bytes,2,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdateStarTourConfReq) Reset()         { *m = UpdateStarTourConfReq{} }
func (m *UpdateStarTourConfReq) String() string { return proto.CompactTextString(m) }
func (*UpdateStarTourConfReq) ProtoMessage()    {}
func (*UpdateStarTourConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{27}
}
func (m *UpdateStarTourConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStarTourConfReq.Unmarshal(m, b)
}
func (m *UpdateStarTourConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStarTourConfReq.Marshal(b, m, deterministic)
}
func (dst *UpdateStarTourConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStarTourConfReq.Merge(dst, src)
}
func (m *UpdateStarTourConfReq) XXX_Size() int {
	return xxx_messageInfo_UpdateStarTourConfReq.Size(m)
}
func (m *UpdateStarTourConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStarTourConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStarTourConfReq proto.InternalMessageInfo

func (m *UpdateStarTourConfReq) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

func (m *UpdateStarTourConfReq) GetConf() *StarTourConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type UpdateStarTourConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateStarTourConfResp) Reset()         { *m = UpdateStarTourConfResp{} }
func (m *UpdateStarTourConfResp) String() string { return proto.CompactTextString(m) }
func (*UpdateStarTourConfResp) ProtoMessage()    {}
func (*UpdateStarTourConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{28}
}
func (m *UpdateStarTourConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStarTourConfResp.Unmarshal(m, b)
}
func (m *UpdateStarTourConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStarTourConfResp.Marshal(b, m, deterministic)
}
func (dst *UpdateStarTourConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStarTourConfResp.Merge(dst, src)
}
func (m *UpdateStarTourConfResp) XXX_Size() int {
	return xxx_messageInfo_UpdateStarTourConfResp.Size(m)
}
func (m *UpdateStarTourConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStarTourConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStarTourConfResp proto.InternalMessageInfo

type DelStarTourConfReq struct {
	ConfId               uint32   `protobuf:"varint,1,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelStarTourConfReq) Reset()         { *m = DelStarTourConfReq{} }
func (m *DelStarTourConfReq) String() string { return proto.CompactTextString(m) }
func (*DelStarTourConfReq) ProtoMessage()    {}
func (*DelStarTourConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{29}
}
func (m *DelStarTourConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelStarTourConfReq.Unmarshal(m, b)
}
func (m *DelStarTourConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelStarTourConfReq.Marshal(b, m, deterministic)
}
func (dst *DelStarTourConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelStarTourConfReq.Merge(dst, src)
}
func (m *DelStarTourConfReq) XXX_Size() int {
	return xxx_messageInfo_DelStarTourConfReq.Size(m)
}
func (m *DelStarTourConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelStarTourConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelStarTourConfReq proto.InternalMessageInfo

func (m *DelStarTourConfReq) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

type DelStarTourConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelStarTourConfResp) Reset()         { *m = DelStarTourConfResp{} }
func (m *DelStarTourConfResp) String() string { return proto.CompactTextString(m) }
func (*DelStarTourConfResp) ProtoMessage()    {}
func (*DelStarTourConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{30}
}
func (m *DelStarTourConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelStarTourConfResp.Unmarshal(m, b)
}
func (m *DelStarTourConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelStarTourConfResp.Marshal(b, m, deterministic)
}
func (dst *DelStarTourConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelStarTourConfResp.Merge(dst, src)
}
func (m *DelStarTourConfResp) XXX_Size() int {
	return xxx_messageInfo_DelStarTourConfResp.Size(m)
}
func (m *DelStarTourConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelStarTourConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelStarTourConfResp proto.InternalMessageInfo

type SupplyInfo struct {
	GiftType             StarTourSupplyType `protobuf:"varint,1,opt,name=gift_type,json=giftType,proto3,enum=star_tour.StarTourSupplyType" json:"gift_type,omitempty"`
	GiftId               uint32             `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SupplyInfo) Reset()         { *m = SupplyInfo{} }
func (m *SupplyInfo) String() string { return proto.CompactTextString(m) }
func (*SupplyInfo) ProtoMessage()    {}
func (*SupplyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{31}
}
func (m *SupplyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SupplyInfo.Unmarshal(m, b)
}
func (m *SupplyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SupplyInfo.Marshal(b, m, deterministic)
}
func (dst *SupplyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SupplyInfo.Merge(dst, src)
}
func (m *SupplyInfo) XXX_Size() int {
	return xxx_messageInfo_SupplyInfo.Size(m)
}
func (m *SupplyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SupplyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SupplyInfo proto.InternalMessageInfo

func (m *SupplyInfo) GetGiftType() StarTourSupplyType {
	if m != nil {
		return m.GiftType
	}
	return StarTourSupplyType_UnKnowType
}

func (m *SupplyInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

// 设置补给配置
type SetStarTourSupplyReq struct {
	SupplyList           []*SupplyInfo `protobuf:"bytes,1,rep,name=supply_list,json=supplyList,proto3" json:"supply_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetStarTourSupplyReq) Reset()         { *m = SetStarTourSupplyReq{} }
func (m *SetStarTourSupplyReq) String() string { return proto.CompactTextString(m) }
func (*SetStarTourSupplyReq) ProtoMessage()    {}
func (*SetStarTourSupplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{32}
}
func (m *SetStarTourSupplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetStarTourSupplyReq.Unmarshal(m, b)
}
func (m *SetStarTourSupplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetStarTourSupplyReq.Marshal(b, m, deterministic)
}
func (dst *SetStarTourSupplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetStarTourSupplyReq.Merge(dst, src)
}
func (m *SetStarTourSupplyReq) XXX_Size() int {
	return xxx_messageInfo_SetStarTourSupplyReq.Size(m)
}
func (m *SetStarTourSupplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetStarTourSupplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetStarTourSupplyReq proto.InternalMessageInfo

func (m *SetStarTourSupplyReq) GetSupplyList() []*SupplyInfo {
	if m != nil {
		return m.SupplyList
	}
	return nil
}

type SetStarTourSupplyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetStarTourSupplyResp) Reset()         { *m = SetStarTourSupplyResp{} }
func (m *SetStarTourSupplyResp) String() string { return proto.CompactTextString(m) }
func (*SetStarTourSupplyResp) ProtoMessage()    {}
func (*SetStarTourSupplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{33}
}
func (m *SetStarTourSupplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetStarTourSupplyResp.Unmarshal(m, b)
}
func (m *SetStarTourSupplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetStarTourSupplyResp.Marshal(b, m, deterministic)
}
func (dst *SetStarTourSupplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetStarTourSupplyResp.Merge(dst, src)
}
func (m *SetStarTourSupplyResp) XXX_Size() int {
	return xxx_messageInfo_SetStarTourSupplyResp.Size(m)
}
func (m *SetStarTourSupplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetStarTourSupplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetStarTourSupplyResp proto.InternalMessageInfo

type GetStarTourSupplyReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStarTourSupplyReq) Reset()         { *m = GetStarTourSupplyReq{} }
func (m *GetStarTourSupplyReq) String() string { return proto.CompactTextString(m) }
func (*GetStarTourSupplyReq) ProtoMessage()    {}
func (*GetStarTourSupplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{34}
}
func (m *GetStarTourSupplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTourSupplyReq.Unmarshal(m, b)
}
func (m *GetStarTourSupplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTourSupplyReq.Marshal(b, m, deterministic)
}
func (dst *GetStarTourSupplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTourSupplyReq.Merge(dst, src)
}
func (m *GetStarTourSupplyReq) XXX_Size() int {
	return xxx_messageInfo_GetStarTourSupplyReq.Size(m)
}
func (m *GetStarTourSupplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTourSupplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTourSupplyReq proto.InternalMessageInfo

type GetStarTourSupplyResp struct {
	SupplyList           []*SupplyInfo `protobuf:"bytes,1,rep,name=supply_list,json=supplyList,proto3" json:"supply_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetStarTourSupplyResp) Reset()         { *m = GetStarTourSupplyResp{} }
func (m *GetStarTourSupplyResp) String() string { return proto.CompactTextString(m) }
func (*GetStarTourSupplyResp) ProtoMessage()    {}
func (*GetStarTourSupplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_tour_d3a717cc6ba28d79, []int{35}
}
func (m *GetStarTourSupplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTourSupplyResp.Unmarshal(m, b)
}
func (m *GetStarTourSupplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTourSupplyResp.Marshal(b, m, deterministic)
}
func (dst *GetStarTourSupplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTourSupplyResp.Merge(dst, src)
}
func (m *GetStarTourSupplyResp) XXX_Size() int {
	return xxx_messageInfo_GetStarTourSupplyResp.Size(m)
}
func (m *GetStarTourSupplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTourSupplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTourSupplyResp proto.InternalMessageInfo

func (m *GetStarTourSupplyResp) GetSupplyList() []*SupplyInfo {
	if m != nil {
		return m.SupplyList
	}
	return nil
}

func init() {
	proto.RegisterType((*StarTAwardInfo)(nil), "star_tour.StarTAwardInfo")
	proto.RegisterType((*StarTUserInfo)(nil), "star_tour.StarTUserInfo")
	proto.RegisterType((*LastSessionReview)(nil), "star_tour.LastSessionReview")
	proto.RegisterType((*NextSessionForecast)(nil), "star_tour.NextSessionForecast")
	proto.RegisterType((*GetStatTourInfoReq)(nil), "star_tour.GetStatTourInfoReq")
	proto.RegisterType((*GetStatTourInfoResp)(nil), "star_tour.GetStatTourInfoResp")
	proto.RegisterType((*SupplyInfo)(nil), "star_tour.supplyInfo")
	proto.RegisterType((*GetUserSupplyListReq)(nil), "star_tour.GetUserSupplyListReq")
	proto.RegisterType((*GetUserSupplyListResp)(nil), "star_tour.GetUserSupplyListResp")
	proto.RegisterType((*CostSupplyInfo)(nil), "star_tour.CostSupplyInfo")
	proto.RegisterType((*DoInvestReq)(nil), "star_tour.DoInvestReq")
	proto.RegisterType((*DoInvestResp)(nil), "star_tour.DoInvestResp")
	proto.RegisterType((*GetMyTourRecordReq)(nil), "star_tour.GetMyTourRecordReq")
	proto.RegisterType((*MyTourRecord)(nil), "star_tour.MyTourRecord")
	proto.RegisterType((*GetMyTourRecordResp)(nil), "star_tour.GetMyTourRecordResp")
	proto.RegisterType((*GetAllTourHistoryReq)(nil), "star_tour.GetAllTourHistoryReq")
	proto.RegisterType((*GetAllTourHistoryResp)(nil), "star_tour.GetAllTourHistoryResp")
	proto.RegisterType((*AwardPackInfo)(nil), "star_tour.AwardPackInfo")
	proto.RegisterType((*GetRecentWinningRecordsReq)(nil), "star_tour.GetRecentWinningRecordsReq")
	proto.RegisterType((*RecentWinningRecord)(nil), "star_tour.RecentWinningRecord")
	proto.RegisterType((*GetRecentWinningRecordsResp)(nil), "star_tour.GetRecentWinningRecordsResp")
	proto.RegisterType((*StarTourPrizeInfo)(nil), "star_tour.StarTourPrizeInfo")
	proto.RegisterType((*StarTourConf)(nil), "star_tour.StarTourConf")
	proto.RegisterType((*SetStarTourConfReq)(nil), "star_tour.SetStarTourConfReq")
	proto.RegisterType((*SetStarTourConfResp)(nil), "star_tour.SetStarTourConfResp")
	proto.RegisterType((*GetStarTourConfReq)(nil), "star_tour.GetStarTourConfReq")
	proto.RegisterType((*GetStarTourConfResp)(nil), "star_tour.GetStarTourConfResp")
	proto.RegisterType((*UpdateStarTourConfReq)(nil), "star_tour.UpdateStarTourConfReq")
	proto.RegisterType((*UpdateStarTourConfResp)(nil), "star_tour.UpdateStarTourConfResp")
	proto.RegisterType((*DelStarTourConfReq)(nil), "star_tour.DelStarTourConfReq")
	proto.RegisterType((*DelStarTourConfResp)(nil), "star_tour.DelStarTourConfResp")
	proto.RegisterType((*SupplyInfo)(nil), "star_tour.SupplyInfo")
	proto.RegisterType((*SetStarTourSupplyReq)(nil), "star_tour.SetStarTourSupplyReq")
	proto.RegisterType((*SetStarTourSupplyResp)(nil), "star_tour.SetStarTourSupplyResp")
	proto.RegisterType((*GetStarTourSupplyReq)(nil), "star_tour.GetStarTourSupplyReq")
	proto.RegisterType((*GetStarTourSupplyResp)(nil), "star_tour.GetStarTourSupplyResp")
	proto.RegisterEnum("star_tour.TourResultType", TourResultType_name, TourResultType_value)
	proto.RegisterEnum("star_tour.StarTourWeekDay", StarTourWeekDay_name, StarTourWeekDay_value)
	proto.RegisterEnum("star_tour.StarTourStatus", StarTourStatus_name, StarTourStatus_value)
	proto.RegisterEnum("star_tour.StarTourSupplyType", StarTourSupplyType_name, StarTourSupplyType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// StarTourClient is the client API for StarTour service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type StarTourClient interface {
	// 获取星际巡航信息
	GetStatTourInfo(ctx context.Context, in *GetStatTourInfoReq, opts ...grpc.CallOption) (*GetStatTourInfoResp, error)
	// 获取用户补给库存（默认先过期的排在前面）
	GetUserSupplyList(ctx context.Context, in *GetUserSupplyListReq, opts ...grpc.CallOption) (*GetUserSupplyListResp, error)
	// 去探险
	DoInvest(ctx context.Context, in *DoInvestReq, opts ...grpc.CallOption) (*DoInvestResp, error)
	// 获取用户巡航记录
	GetMyTourRecord(ctx context.Context, in *GetMyTourRecordReq, opts ...grpc.CallOption) (*GetMyTourRecordResp, error)
	// 往期回顾
	GetAllTourHistory(ctx context.Context, in *GetAllTourHistoryReq, opts ...grpc.CallOption) (*GetAllTourHistoryResp, error)
	// 获取中奖轮播信息
	GetRecentWinningRecords(ctx context.Context, in *GetRecentWinningRecordsReq, opts ...grpc.CallOption) (*GetRecentWinningRecordsResp, error)
	// 活动配置（包括时间、奖池）
	SetStarTourConf(ctx context.Context, in *SetStarTourConfReq, opts ...grpc.CallOption) (*SetStarTourConfResp, error)
	// 获取活动配置
	GetStarTourConf(ctx context.Context, in *GetStarTourConfReq, opts ...grpc.CallOption) (*GetStarTourConfResp, error)
	// 修改活动配置
	UpdateStarTourConf(ctx context.Context, in *UpdateStarTourConfReq, opts ...grpc.CallOption) (*UpdateStarTourConfResp, error)
	// 删除活动配置
	DelStarTourConf(ctx context.Context, in *DelStarTourConfReq, opts ...grpc.CallOption) (*DelStarTourConfResp, error)
	// 补给配置
	SetStarTourSupply(ctx context.Context, in *SetStarTourSupplyReq, opts ...grpc.CallOption) (*SetStarTourSupplyResp, error)
	// 获取补给配置
	GetStarTourSupply(ctx context.Context, in *GetStarTourSupplyReq, opts ...grpc.CallOption) (*GetStarTourSupplyResp, error)
}

type starTourClient struct {
	cc *grpc.ClientConn
}

func NewStarTourClient(cc *grpc.ClientConn) StarTourClient {
	return &starTourClient{cc}
}

func (c *starTourClient) GetStatTourInfo(ctx context.Context, in *GetStatTourInfoReq, opts ...grpc.CallOption) (*GetStatTourInfoResp, error) {
	out := new(GetStatTourInfoResp)
	err := c.cc.Invoke(ctx, "/star_tour.StarTour/GetStatTourInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTourClient) GetUserSupplyList(ctx context.Context, in *GetUserSupplyListReq, opts ...grpc.CallOption) (*GetUserSupplyListResp, error) {
	out := new(GetUserSupplyListResp)
	err := c.cc.Invoke(ctx, "/star_tour.StarTour/GetUserSupplyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTourClient) DoInvest(ctx context.Context, in *DoInvestReq, opts ...grpc.CallOption) (*DoInvestResp, error) {
	out := new(DoInvestResp)
	err := c.cc.Invoke(ctx, "/star_tour.StarTour/DoInvest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTourClient) GetMyTourRecord(ctx context.Context, in *GetMyTourRecordReq, opts ...grpc.CallOption) (*GetMyTourRecordResp, error) {
	out := new(GetMyTourRecordResp)
	err := c.cc.Invoke(ctx, "/star_tour.StarTour/GetMyTourRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTourClient) GetAllTourHistory(ctx context.Context, in *GetAllTourHistoryReq, opts ...grpc.CallOption) (*GetAllTourHistoryResp, error) {
	out := new(GetAllTourHistoryResp)
	err := c.cc.Invoke(ctx, "/star_tour.StarTour/GetAllTourHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTourClient) GetRecentWinningRecords(ctx context.Context, in *GetRecentWinningRecordsReq, opts ...grpc.CallOption) (*GetRecentWinningRecordsResp, error) {
	out := new(GetRecentWinningRecordsResp)
	err := c.cc.Invoke(ctx, "/star_tour.StarTour/GetRecentWinningRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTourClient) SetStarTourConf(ctx context.Context, in *SetStarTourConfReq, opts ...grpc.CallOption) (*SetStarTourConfResp, error) {
	out := new(SetStarTourConfResp)
	err := c.cc.Invoke(ctx, "/star_tour.StarTour/SetStarTourConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTourClient) GetStarTourConf(ctx context.Context, in *GetStarTourConfReq, opts ...grpc.CallOption) (*GetStarTourConfResp, error) {
	out := new(GetStarTourConfResp)
	err := c.cc.Invoke(ctx, "/star_tour.StarTour/GetStarTourConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTourClient) UpdateStarTourConf(ctx context.Context, in *UpdateStarTourConfReq, opts ...grpc.CallOption) (*UpdateStarTourConfResp, error) {
	out := new(UpdateStarTourConfResp)
	err := c.cc.Invoke(ctx, "/star_tour.StarTour/UpdateStarTourConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTourClient) DelStarTourConf(ctx context.Context, in *DelStarTourConfReq, opts ...grpc.CallOption) (*DelStarTourConfResp, error) {
	out := new(DelStarTourConfResp)
	err := c.cc.Invoke(ctx, "/star_tour.StarTour/DelStarTourConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTourClient) SetStarTourSupply(ctx context.Context, in *SetStarTourSupplyReq, opts ...grpc.CallOption) (*SetStarTourSupplyResp, error) {
	out := new(SetStarTourSupplyResp)
	err := c.cc.Invoke(ctx, "/star_tour.StarTour/SetStarTourSupply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTourClient) GetStarTourSupply(ctx context.Context, in *GetStarTourSupplyReq, opts ...grpc.CallOption) (*GetStarTourSupplyResp, error) {
	out := new(GetStarTourSupplyResp)
	err := c.cc.Invoke(ctx, "/star_tour.StarTour/GetStarTourSupply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StarTourServer is the server API for StarTour service.
type StarTourServer interface {
	// 获取星际巡航信息
	GetStatTourInfo(context.Context, *GetStatTourInfoReq) (*GetStatTourInfoResp, error)
	// 获取用户补给库存（默认先过期的排在前面）
	GetUserSupplyList(context.Context, *GetUserSupplyListReq) (*GetUserSupplyListResp, error)
	// 去探险
	DoInvest(context.Context, *DoInvestReq) (*DoInvestResp, error)
	// 获取用户巡航记录
	GetMyTourRecord(context.Context, *GetMyTourRecordReq) (*GetMyTourRecordResp, error)
	// 往期回顾
	GetAllTourHistory(context.Context, *GetAllTourHistoryReq) (*GetAllTourHistoryResp, error)
	// 获取中奖轮播信息
	GetRecentWinningRecords(context.Context, *GetRecentWinningRecordsReq) (*GetRecentWinningRecordsResp, error)
	// 活动配置（包括时间、奖池）
	SetStarTourConf(context.Context, *SetStarTourConfReq) (*SetStarTourConfResp, error)
	// 获取活动配置
	GetStarTourConf(context.Context, *GetStarTourConfReq) (*GetStarTourConfResp, error)
	// 修改活动配置
	UpdateStarTourConf(context.Context, *UpdateStarTourConfReq) (*UpdateStarTourConfResp, error)
	// 删除活动配置
	DelStarTourConf(context.Context, *DelStarTourConfReq) (*DelStarTourConfResp, error)
	// 补给配置
	SetStarTourSupply(context.Context, *SetStarTourSupplyReq) (*SetStarTourSupplyResp, error)
	// 获取补给配置
	GetStarTourSupply(context.Context, *GetStarTourSupplyReq) (*GetStarTourSupplyResp, error)
}

func RegisterStarTourServer(s *grpc.Server, srv StarTourServer) {
	s.RegisterService(&_StarTour_serviceDesc, srv)
}

func _StarTour_GetStatTourInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStatTourInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTourServer).GetStatTourInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_tour.StarTour/GetStatTourInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTourServer).GetStatTourInfo(ctx, req.(*GetStatTourInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTour_GetUserSupplyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSupplyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTourServer).GetUserSupplyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_tour.StarTour/GetUserSupplyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTourServer).GetUserSupplyList(ctx, req.(*GetUserSupplyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTour_DoInvest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoInvestReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTourServer).DoInvest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_tour.StarTour/DoInvest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTourServer).DoInvest(ctx, req.(*DoInvestReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTour_GetMyTourRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMyTourRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTourServer).GetMyTourRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_tour.StarTour/GetMyTourRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTourServer).GetMyTourRecord(ctx, req.(*GetMyTourRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTour_GetAllTourHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllTourHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTourServer).GetAllTourHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_tour.StarTour/GetAllTourHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTourServer).GetAllTourHistory(ctx, req.(*GetAllTourHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTour_GetRecentWinningRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecentWinningRecordsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTourServer).GetRecentWinningRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_tour.StarTour/GetRecentWinningRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTourServer).GetRecentWinningRecords(ctx, req.(*GetRecentWinningRecordsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTour_SetStarTourConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetStarTourConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTourServer).SetStarTourConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_tour.StarTour/SetStarTourConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTourServer).SetStarTourConf(ctx, req.(*SetStarTourConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTour_GetStarTourConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStarTourConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTourServer).GetStarTourConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_tour.StarTour/GetStarTourConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTourServer).GetStarTourConf(ctx, req.(*GetStarTourConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTour_UpdateStarTourConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStarTourConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTourServer).UpdateStarTourConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_tour.StarTour/UpdateStarTourConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTourServer).UpdateStarTourConf(ctx, req.(*UpdateStarTourConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTour_DelStarTourConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelStarTourConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTourServer).DelStarTourConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_tour.StarTour/DelStarTourConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTourServer).DelStarTourConf(ctx, req.(*DelStarTourConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTour_SetStarTourSupply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetStarTourSupplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTourServer).SetStarTourSupply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_tour.StarTour/SetStarTourSupply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTourServer).SetStarTourSupply(ctx, req.(*SetStarTourSupplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTour_GetStarTourSupply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStarTourSupplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTourServer).GetStarTourSupply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_tour.StarTour/GetStarTourSupply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTourServer).GetStarTourSupply(ctx, req.(*GetStarTourSupplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _StarTour_serviceDesc = grpc.ServiceDesc{
	ServiceName: "star_tour.StarTour",
	HandlerType: (*StarTourServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetStatTourInfo",
			Handler:    _StarTour_GetStatTourInfo_Handler,
		},
		{
			MethodName: "GetUserSupplyList",
			Handler:    _StarTour_GetUserSupplyList_Handler,
		},
		{
			MethodName: "DoInvest",
			Handler:    _StarTour_DoInvest_Handler,
		},
		{
			MethodName: "GetMyTourRecord",
			Handler:    _StarTour_GetMyTourRecord_Handler,
		},
		{
			MethodName: "GetAllTourHistory",
			Handler:    _StarTour_GetAllTourHistory_Handler,
		},
		{
			MethodName: "GetRecentWinningRecords",
			Handler:    _StarTour_GetRecentWinningRecords_Handler,
		},
		{
			MethodName: "SetStarTourConf",
			Handler:    _StarTour_SetStarTourConf_Handler,
		},
		{
			MethodName: "GetStarTourConf",
			Handler:    _StarTour_GetStarTourConf_Handler,
		},
		{
			MethodName: "UpdateStarTourConf",
			Handler:    _StarTour_UpdateStarTourConf_Handler,
		},
		{
			MethodName: "DelStarTourConf",
			Handler:    _StarTour_DelStarTourConf_Handler,
		},
		{
			MethodName: "SetStarTourSupply",
			Handler:    _StarTour_SetStarTourSupply_Handler,
		},
		{
			MethodName: "GetStarTourSupply",
			Handler:    _StarTour_GetStarTourSupply_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "star-tour/star-tour.proto",
}

func init() {
	proto.RegisterFile("star-tour/star-tour.proto", fileDescriptor_star_tour_d3a717cc6ba28d79)
}

var fileDescriptor_star_tour_d3a717cc6ba28d79 = []byte{
	// 1916 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x58, 0xdd, 0x72, 0xdb, 0xc6,
	0x15, 0x16, 0x49, 0x91, 0x22, 0x0e, 0x25, 0x99, 0x5a, 0x5b, 0x16, 0x2d, 0xc7, 0xb1, 0x82, 0x99,
	0xb4, 0xae, 0x32, 0x96, 0xa7, 0x4e, 0x9d, 0xc9, 0x24, 0x93, 0x99, 0xca, 0x66, 0xac, 0x72, 0x62,
	0xbb, 0x1a, 0x90, 0x8a, 0x3b, 0xed, 0x74, 0x30, 0x10, 0xb0, 0xa4, 0xb6, 0x02, 0x16, 0x28, 0x76,
	0x21, 0x85, 0x9d, 0xf6, 0x55, 0x7a, 0xd1, 0x8b, 0xde, 0xf5, 0x09, 0xda, 0xeb, 0xbe, 0x4b, 0xef,
	0xda, 0x37, 0xe8, 0xec, 0x59, 0x80, 0xc4, 0x9f, 0x7e, 0xd2, 0x5e, 0xe5, 0x0e, 0x7b, 0xfe, 0xf6,
	0xfc, 0x7e, 0x7b, 0x48, 0x78, 0x20, 0xa4, 0x13, 0x3f, 0x95, 0x61, 0x12, 0x3f, 0x5b, 0x7c, 0x1d,
	0x44, 0x71, 0x28, 0x43, 0x62, 0x28, 0x82, 0xad, 0x08, 0xe6, 0x3f, 0x1a, 0xb0, 0x39, 0x96, 0x4e,
	0x3c, 0x39, 0xbc, 0x74, 0x62, 0x6f, 0xc4, 0xa7, 0x21, 0xd9, 0x81, 0xb5, 0xc8, 0x71, 0xcf, 0x6d,
	0xe6, 0x0d, 0x1a, 0x7b, 0x8d, 0x27, 0x1b, 0x56, 0x47, 0x1d, 0x47, 0x1e, 0x79, 0x08, 0x06, 0x32,
	0xb8, 0x13, 0xd0, 0x41, 0x73, 0xaf, 0xf1, 0xc4, 0xb0, 0xba, 0x8a, 0xf0, 0xce, 0x09, 0x28, 0x79,
	0x00, 0xf8, 0x6d, 0x47, 0xcc, 0x1d, 0xb4, 0x90, 0x87, 0x56, 0x8e, 0x99, 0x4b, 0x1e, 0x43, 0x0f,
	0x59, 0x4e, 0x10, 0x26, 0x5c, 0x0e, 0x56, 0xd1, 0x28, 0x28, 0xd2, 0x21, 0x52, 0xc8, 0x23, 0x80,
	0x84, 0x33, 0x69, 0x47, 0x31, 0x73, 0xe9, 0xa0, 0x8d, 0x7c, 0x43, 0x51, 0x8e, 0x15, 0x41, 0x99,
	0x9e, 0x32, 0x6e, 0x4b, 0x16, 0xd0, 0x41, 0x07, 0x99, 0x6b, 0x53, 0xc6, 0x27, 0x2c, 0xa0, 0xe6,
	0x04, 0x36, 0xd0, 0xfb, 0x13, 0x41, 0x63, 0x74, 0xbe, 0x0f, 0xad, 0x64, 0xe1, 0xb8, 0xfa, 0x24,
	0xbb, 0xd0, 0xe5, 0xcc, 0x3d, 0xcf, 0x3b, 0x9d, 0x9d, 0xc9, 0x36, 0x74, 0xce, 0x3c, 0x9b, 0x05,
	0xb3, 0xd4, 0xe5, 0xf6, 0x99, 0x37, 0x0a, 0x66, 0xe6, 0x3f, 0x1b, 0xb0, 0xf5, 0xc6, 0x11, 0x72,
	0x4c, 0x85, 0x60, 0x21, 0xb7, 0xe8, 0x05, 0xa3, 0x97, 0xca, 0x4b, 0xa1, 0x09, 0xcb, 0xd4, 0x18,
	0x29, 0x65, 0xe4, 0x91, 0x8f, 0x60, 0x3d, 0x63, 0xa3, 0xa7, 0x4d, 0x14, 0xe8, 0xa5, 0x34, 0xe5,
	0x2d, 0xf9, 0x1c, 0xc0, 0x51, 0x69, 0xb6, 0x55, 0xec, 0x78, 0x65, 0xef, 0xf9, 0x83, 0x83, 0x45,
	0x31, 0x0e, 0x8a, 0x85, 0xb0, 0x0c, 0x14, 0x3e, 0x76, 0xdc, 0x73, 0xf2, 0x02, 0x8c, 0x44, 0xd0,
	0xd8, 0x66, 0x7c, 0x1a, 0x62, 0x02, 0x7b, 0xcf, 0x07, 0x65, 0xc5, 0x2c, 0x07, 0x56, 0x37, 0x49,
	0xbf, 0x4c, 0x0e, 0x77, 0xdf, 0xd1, 0xef, 0xb2, 0x38, 0x5e, 0x87, 0x31, 0x75, 0x1d, 0x21, 0x6f,
	0x8a, 0xa4, 0xe8, 0x66, 0xf3, 0xf6, 0x6e, 0x9a, 0x11, 0x90, 0x23, 0x2a, 0xc7, 0xd2, 0x91, 0x93,
	0x30, 0xd1, 0xce, 0xd0, 0xdf, 0xd7, 0xd4, 0xa4, 0xe8, 0x40, 0xb3, 0xec, 0xc0, 0x4f, 0x60, 0x8b,
	0x53, 0xea, 0xd9, 0x51, 0x4c, 0x6d, 0x87, 0x7b, 0xf6, 0x34, 0x8c, 0x29, 0xa6, 0xab, 0x6b, 0x6d,
	0x2a, 0xc6, 0x71, 0x4c, 0x0f, 0xb9, 0xa7, 0xc2, 0x31, 0xff, 0xdd, 0x82, 0xbb, 0x95, 0x2b, 0x45,
	0x74, 0x53, 0x88, 0x8f, 0x00, 0x5c, 0xd5, 0x7a, 0xb6, 0x17, 0x5e, 0xf2, 0xcc, 0x01, 0xa4, 0x0c,
	0xc3, 0x4b, 0xfe, 0x7f, 0x14, 0xea, 0xc7, 0x70, 0x87, 0xf1, 0x0b, 0x2a, 0x54, 0x33, 0x87, 0xb3,
	0x98, 0x0a, 0x91, 0xf6, 0xfb, 0xa6, 0x26, 0x1f, 0xa7, 0x54, 0xec, 0x79, 0x55, 0x51, 0xbc, 0x74,
	0xd1, 0xf3, 0x82, 0xc6, 0xaf, 0x70, 0x24, 0x1e, 0x82, 0x11, 0xcc, 0x6d, 0x91, 0x44, 0x91, 0x3f,
	0x4f, 0x9b, 0xbe, 0x1b, 0xcc, 0xc7, 0x78, 0x56, 0xba, 0x01, 0xe3, 0xb6, 0xb6, 0x38, 0x58, 0xd3,
	0xba, 0x01, 0xe3, 0x23, 0x24, 0x90, 0x7d, 0xd8, 0xca, 0x62, 0x17, 0x49, 0x64, 0xfb, 0x2c, 0x60,
	0x72, 0xd0, 0x45, 0xa9, 0x3b, 0x29, 0x63, 0x9c, 0x44, 0x6f, 0x14, 0x99, 0x7c, 0x0d, 0x5b, 0xbe,
	0x23, 0xd1, 0x5f, 0x27, 0x96, 0x4c, 0xb2, 0x90, 0x8b, 0x81, 0xb1, 0xd7, 0xba, 0xb6, 0xc1, 0xfa,
	0x5a, 0xe5, 0x78, 0xa1, 0x41, 0x3e, 0x85, 0x8e, 0xef, 0x08, 0x69, 0x8b, 0x01, 0x60, 0xb2, 0x3e,
	0xc8, 0xe9, 0x56, 0x26, 0xc9, 0x6a, 0x2b, 0xd9, 0x31, 0x79, 0x01, 0x1d, 0x4e, 0xbf, 0x53, 0x4a,
	0x3d, 0x54, 0xfa, 0x30, 0xa7, 0x54, 0xd3, 0xb6, 0x56, 0x5b, 0x49, 0x8f, 0xcd, 0xff, 0x34, 0x00,
	0x74, 0x62, 0x70, 0xe2, 0xf7, 0x60, 0x5d, 0x8f, 0x86, 0xa4, 0xc1, 0xb2, 0xd6, 0x98, 0xdc, 0x91,
	0xa4, 0xc1, 0xc8, 0x53, 0xf8, 0x81, 0x4c, 0x9e, 0x04, 0x69, 0xa9, 0xd7, 0xd4, 0xf9, 0x5d, 0x12,
	0x28, 0xac, 0x9b, 0xb1, 0xa9, 0x54, 0x7a, 0x2d, 0x8d, 0x75, 0xea, 0xa8, 0xb1, 0x0e, 0x19, 0x72,
	0x1e, 0xd1, 0xb4, 0x82, 0x5d, 0x45, 0x98, 0xcc, 0x23, 0xba, 0x60, 0x22, 0xa6, 0xb4, 0x35, 0xa6,
	0x28, 0x02, 0x02, 0xe1, 0x23, 0x00, 0x64, 0x6a, 0x30, 0xd3, 0xa5, 0x43, 0xf1, 0x05, 0x98, 0x21,
	0x3b, 0x89, 0x7d, 0xac, 0x9c, 0x61, 0xa1, 0x07, 0x27, 0xb1, 0x5f, 0xc0, 0xb9, 0x6e, 0x11, 0xe7,
	0x9e, 0xc0, 0xbd, 0x23, 0x2a, 0x55, 0x01, 0x74, 0x0b, 0xbc, 0x61, 0x42, 0xd6, 0x8e, 0x96, 0x39,
	0x81, 0xed, 0x1a, 0x49, 0x11, 0x91, 0x2f, 0x61, 0x73, 0xd1, 0x51, 0xb6, 0xcf, 0x84, 0x1c, 0x34,
	0xb0, 0xcc, 0xdb, 0xb9, 0xac, 0x2f, 0xd3, 0x6a, 0xad, 0x67, 0xdd, 0xa6, 0x0c, 0x98, 0x7f, 0x69,
	0xc0, 0xe6, 0xab, 0x50, 0xc8, 0xf1, 0xf7, 0xc9, 0x7b, 0x2e, 0xb9, 0xcd, 0xab, 0x93, 0xdb, 0x2a,
	0x25, 0x37, 0x4b, 0x90, 0xaa, 0x96, 0x4e, 0x3c, 0x5a, 0x51, 0xd5, 0x2a, 0xa6, 0xb6, 0x5d, 0x4a,
	0xad, 0xf9, 0xb7, 0x06, 0xf4, 0x86, 0xa1, 0x1e, 0x82, 0x2b, 0x71, 0xc7, 0x3d, 0x73, 0x38, 0xa7,
	0x7e, 0x0e, 0x77, 0x52, 0xca, 0xa8, 0x0c, 0x4b, 0xad, 0x32, 0x68, 0x7c, 0x01, 0xbd, 0x7c, 0xfa,
	0x56, 0x31, 0x7d, 0x79, 0x58, 0x28, 0x66, 0xc8, 0x4a, 0xbb, 0x54, 0x25, 0x90, 0x10, 0x58, 0x75,
	0x43, 0x91, 0x0d, 0x3a, 0x7e, 0x9b, 0x17, 0xb0, 0xbe, 0x74, 0xf7, 0x66, 0xcc, 0x2a, 0x40, 0x42,
	0xb3, 0x04, 0x09, 0xb5, 0x33, 0xdf, 0xaa, 0x9d, 0x79, 0x33, 0x41, 0x94, 0x7e, 0x3b, 0x57, 0x80,
	0x69, 0x51, 0x37, 0x8c, 0xbd, 0xfa, 0x6c, 0x3d, 0x86, 0x9e, 0x0a, 0xcb, 0x8e, 0xa9, 0x48, 0x7c,
	0x99, 0x5e, 0x09, 0x12, 0xb5, 0x14, 0x85, 0xdc, 0x87, 0x4e, 0x38, 0x9d, 0x0a, 0x9a, 0xdd, 0x94,
	0x9e, 0xc8, 0x3d, 0x68, 0x6b, 0x07, 0x74, 0xfd, 0xf4, 0xc1, 0xfc, 0x6b, 0x13, 0xd6, 0xf3, 0x97,
	0x7e, 0xff, 0x77, 0xa1, 0xfc, 0xc4, 0xb6, 0xaa, 0x4f, 0x6c, 0xc9, 0xe7, 0x55, 0x7c, 0x34, 0xf2,
	0x3e, 0x3f, 0x86, 0xde, 0x29, 0xe3, 0xb3, 0xd0, 0x46, 0xcc, 0x4e, 0xa7, 0x17, 0x90, 0x84, 0x80,
	0xae, 0x7c, 0xf0, 0x13, 0xf7, 0x7c, 0x6e, 0xab, 0x4e, 0xc6, 0xf9, 0x35, 0x2c, 0x03, 0x29, 0x6a,
	0xa8, 0xd2, 0x2a, 0x14, 0xa0, 0xb7, 0x1b, 0xcc, 0x53, 0xe4, 0x35, 0x61, 0x63, 0x39, 0x63, 0x1e,
	0x75, 0x71, 0x8c, 0x0d, 0xab, 0x97, 0x95, 0x69, 0x48, 0x5d, 0x35, 0x37, 0xc1, 0x5c, 0xdf, 0x8e,
	0x22, 0x86, 0xf6, 0x20, 0x98, 0xe3, 0xf5, 0x43, 0xea, 0x9a, 0x13, 0x7c, 0xd2, 0x8a, 0xf5, 0x11,
	0x11, 0xf9, 0x0a, 0x07, 0x38, 0x46, 0x42, 0x7e, 0x80, 0x77, 0x72, 0x1d, 0x58, 0x50, 0x5a, 0x0f,
	0xe6, 0xfa, 0x0b, 0x47, 0xf8, 0x5b, 0x84, 0x90, 0x43, 0xdf, 0x57, 0x12, 0xbf, 0x60, 0x42, 0x86,
	0xf1, 0xbc, 0xbe, 0xee, 0xcb, 0xb2, 0x36, 0xeb, 0xcb, 0xda, 0xca, 0x97, 0xf5, 0x5b, 0x04, 0x9c,
	0xb2, 0x5d, 0xf4, 0xb7, 0x17, 0x23, 0xde, 0xe7, 0x9d, 0xbd, 0xfe, 0x61, 0x00, 0xad, 0x80, 0xfe,
	0xfe, 0xbd, 0x01, 0x1b, 0x87, 0xd9, 0xbb, 0xfa, 0x83, 0x5b, 0x4c, 0x87, 0xb0, 0x7b, 0x44, 0xa5,
	0x45, 0x5d, 0xca, 0xe5, 0x7b, 0xc6, 0x39, 0xe3, 0x33, 0x5d, 0x0b, 0x51, 0x9f, 0xf3, 0x45, 0x6e,
	0x9b, 0xf9, 0xdc, 0xfe, 0x09, 0xee, 0xd6, 0x98, 0x28, 0x6e, 0x83, 0x8d, 0xdb, 0x6e, 0x83, 0xe4,
	0x19, 0xb4, 0x75, 0xd3, 0xdf, 0xb8, 0xd2, 0x69, 0x39, 0xf3, 0x3d, 0x3c, 0xbc, 0x32, 0x08, 0x11,
	0x91, 0xcf, 0x61, 0x4d, 0x77, 0xa3, 0x48, 0x8b, 0x9b, 0x7f, 0xc0, 0x6b, 0xb4, 0xac, 0x4c, 0xdc,
	0xfc, 0x39, 0x6c, 0xe1, 0x8d, 0x61, 0x12, 0x1f, 0xc7, 0xec, 0x0f, 0x14, 0xdd, 0xbb, 0x0b, 0xed,
	0xd3, 0xd9, 0xb2, 0xb8, 0xab, 0xa7, 0xb3, 0x11, 0xf6, 0xe2, 0x25, 0x65, 0xb3, 0xb3, 0x45, 0x2f,
	0xea, 0x93, 0xf9, 0xaf, 0x06, 0xac, 0x67, 0x26, 0x5e, 0x85, 0x7c, 0x4a, 0x3e, 0x86, 0x4d, 0xc7,
	0x95, 0xec, 0x82, 0xc9, 0xb9, 0x7d, 0x4a, 0x67, 0x8c, 0xa7, 0x66, 0x36, 0x32, 0xea, 0x4b, 0x45,
	0x54, 0x10, 0xb2, 0x10, 0xa3, 0x3c, 0xc3, 0x98, 0x5e, 0x46, 0xfb, 0x9a, 0x63, 0x37, 0x79, 0x4e,
	0x66, 0x24, 0x7d, 0x9d, 0x3c, 0x27, 0xd5, 0xdf, 0x81, 0x35, 0xc5, 0x54, 0xaa, 0xba, 0x5d, 0x3a,
	0x9e, 0x83, 0x5a, 0x1f, 0x00, 0x5c, 0xda, 0x8a, 0x85, 0xcd, 0xde, 0xde, 0x6b, 0x29, 0xb5, 0xcb,
	0xa1, 0xa3, 0xe1, 0xff, 0x4b, 0x80, 0x48, 0x05, 0xaa, 0xb9, 0x9d, 0xca, 0x28, 0x54, 0xb2, 0x61,
	0x19, 0x28, 0x8f, 0x93, 0x70, 0x08, 0x64, 0x8c, 0x2b, 0xee, 0x22, 0x5a, 0xd5, 0x43, 0x9f, 0xa8,
	0x17, 0x85, 0x4f, 0xd3, 0xfa, 0xef, 0xd4, 0x18, 0x43, 0x49, 0x14, 0x32, 0xb7, 0xe1, 0x6e, 0xc5,
	0x84, 0x88, 0xcc, 0xa3, 0x6c, 0x5f, 0x2f, 0x58, 0xfe, 0x29, 0x74, 0x84, 0x74, 0x64, 0x22, 0xd0,
	0xf6, 0x66, 0xb5, 0x51, 0xc2, 0x24, 0x1e, 0xa3, 0x80, 0x95, 0x0a, 0x9a, 0xdf, 0x64, 0x5b, 0x78,
	0xc1, 0x3e, 0xf9, 0x19, 0x18, 0xea, 0xfa, 0xab, 0xd0, 0xaa, 0x20, 0xdf, 0x55, 0x92, 0x18, 0xef,
	0x6f, 0x61, 0xfb, 0x24, 0xf2, 0x1c, 0x49, 0xcb, 0x8e, 0xed, 0xc0, 0x1a, 0x9a, 0x5b, 0x02, 0x80,
	0x3a, 0x8e, 0xbc, 0x45, 0x2e, 0x9a, 0xb7, 0xc9, 0xc5, 0x00, 0xee, 0xd7, 0x99, 0x17, 0x91, 0xf9,
	0x14, 0xc8, 0x90, 0xfa, 0xb7, 0xbd, 0x55, 0x25, 0xb5, 0x22, 0x2e, 0x22, 0xd3, 0x01, 0xc8, 0xad,
	0x49, 0x5f, 0xe4, 0x77, 0x1d, 0x9d, 0xcf, 0x47, 0x75, 0xf9, 0x44, 0x0d, 0xb5, 0x00, 0xe5, 0x56,
	0xa1, 0xab, 0x16, 0x28, 0xf3, 0x1d, 0xdc, 0xcb, 0x95, 0x53, 0xeb, 0x2a, 0x57, 0x3f, 0x2b, 0x6e,
	0x28, 0xd5, 0x05, 0xaf, 0x7e, 0x3b, 0x31, 0x77, 0x60, 0xbb, 0xc6, 0x9e, 0x88, 0xcc, 0xfb, 0xf8,
	0x68, 0x54, 0x2e, 0x32, 0x7f, 0x89, 0xa0, 0x5f, 0x55, 0xf8, 0x5f, 0x3d, 0xd8, 0xff, 0x0c, 0x36,
	0x27, 0x8b, 0x47, 0x1a, 0x83, 0xef, 0xc1, 0x9a, 0x7a, 0x54, 0xe6, 0x11, 0xed, 0xaf, 0xa8, 0xc3,
	0x38, 0x71, 0x5d, 0x2a, 0x44, 0xbf, 0x41, 0x00, 0x3a, 0xaf, 0x1d, 0xe6, 0x53, 0xaf, 0xdf, 0xdc,
	0xff, 0x23, 0xdc, 0xc9, 0xbc, 0x78, 0x4f, 0xe9, 0xf9, 0xd0, 0x99, 0x93, 0x0d, 0x30, 0x4e, 0xf8,
	0x79, 0x78, 0xc9, 0x87, 0xce, 0xbc, 0xbf, 0xa2, 0xa4, 0xdf, 0x86, 0xdc, 0x73, 0xe6, 0xfd, 0x86,
	0x32, 0x33, 0x49, 0xa8, 0x50, 0x87, 0xa6, 0x92, 0x7b, 0x4f, 0x3d, 0xae, 0x8f, 0x2d, 0xb2, 0x0e,
	0xdd, 0xc9, 0x59, 0x12, 0xe3, 0x69, 0x15, 0xef, 0x88, 0x99, 0xfa, 0x6e, 0x2b, 0xce, 0xd8, 0x91,
	0x49, 0xac, 0x4e, 0x1d, 0xc5, 0x19, 0x27, 0x68, 0x6f, 0x6d, 0x7f, 0x98, 0xfe, 0x79, 0xb2, 0x18,
	0x08, 0x65, 0xf4, 0xd0, 0xf7, 0xf5, 0xa1, 0xbf, 0x42, 0x0c, 0x68, 0x9f, 0x08, 0xc6, 0x67, 0xda,
	0xeb, 0x13, 0x9e, 0x08, 0xe5, 0xb5, 0xb2, 0xf8, 0x9a, 0x71, 0x26, 0xce, 0xa8, 0xd7, 0x6f, 0xed,
	0xbf, 0x05, 0x52, 0x6d, 0x03, 0xb2, 0x09, 0x70, 0xc2, 0xbf, 0xe1, 0xe1, 0x65, 0x9a, 0x02, 0x03,
	0xda, 0x93, 0x97, 0xd4, 0xe1, 0xfd, 0x06, 0xaa, 0xc7, 0xce, 0x2c, 0xa0, 0x5c, 0xf6, 0x9b, 0x4a,
	0xd0, 0xa2, 0xde, 0x90, 0x39, 0x41, 0xc8, 0xbd, 0x7e, 0xeb, 0xf9, 0x9f, 0xbb, 0xd0, 0xcd, 0xec,
	0x11, 0x0b, 0xee, 0x94, 0x7e, 0x1e, 0x93, 0x7c, 0xfb, 0x55, 0x7f, 0xad, 0xef, 0x7e, 0x78, 0x1d,
	0x5b, 0x44, 0xe6, 0x0a, 0xf9, 0x15, 0x6c, 0x55, 0x7e, 0x62, 0x90, 0xc7, 0x45, 0xb5, 0xca, 0x4f,
	0x95, 0xdd, 0xbd, 0xeb, 0x05, 0xd0, 0xf2, 0x57, 0xd0, 0xcd, 0x36, 0x62, 0x72, 0x3f, 0x27, 0x9f,
	0xdb, 0xea, 0x77, 0x77, 0x6a, 0xe9, 0xa8, 0xae, 0x83, 0x2d, 0xec, 0x98, 0xa5, 0x60, 0x4b, 0x4b,
	0x6f, 0x39, 0xd8, 0xf2, 0xce, 0xb5, 0x08, 0xb6, 0xb8, 0xde, 0x94, 0x83, 0xad, 0x2c, 0x55, 0xe5,
	0x60, 0xab, 0xdb, 0x91, 0xb9, 0x42, 0x7e, 0x07, 0x3b, 0x57, 0xbc, 0xae, 0xe4, 0xe3, 0xa2, 0xfa,
	0x15, 0x6b, 0xc4, 0xee, 0x8f, 0x6e, 0x23, 0x96, 0x65, 0xa6, 0x84, 0xff, 0x85, 0xcc, 0x54, 0x9f,
	0x97, 0x42, 0x66, 0xea, 0x9e, 0x8e, 0x95, 0x65, 0x6b, 0xd5, 0xdb, 0x3c, 0xba, 0xde, 0xe6, 0x51,
	0xad, 0xcd, 0xdf, 0x00, 0xa9, 0x62, 0x33, 0xc9, 0x67, 0xb3, 0xf6, 0x65, 0xd8, 0xfd, 0xe8, 0x06,
	0x89, 0xcc, 0xe1, 0x12, 0x5e, 0x17, 0x1c, 0xae, 0x42, 0x7f, 0xc1, 0xe1, 0x3a, 0xa8, 0xc7, 0xf6,
	0xa8, 0x20, 0x67, 0xa1, 0x3d, 0xea, 0x70, 0xba, 0xd0, 0x1e, 0xf5, 0xc0, 0x9b, 0x35, 0xde, 0x35,
	0x96, 0x8f, 0x6e, 0xb2, 0x7c, 0x54, 0x6f, 0xf9, 0xe5, 0xd3, 0x5f, 0x7f, 0x32, 0x0b, 0x7d, 0x87,
	0xcf, 0x0e, 0x5e, 0x3c, 0x97, 0xf2, 0xc0, 0x0d, 0x83, 0x67, 0xf8, 0xbf, 0xb0, 0x1b, 0xfa, 0xcf,
	0x04, 0x8d, 0x2f, 0x98, 0x4b, 0xc5, 0xf2, 0x3f, 0xe3, 0xd3, 0x0e, 0x32, 0x3f, 0xfd, 0x6f, 0x00,
	0x00, 0x00, 0xff, 0xff, 0x98, 0xec, 0xee, 0x50, 0x51, 0x16, 0x00, 0x00,
}
