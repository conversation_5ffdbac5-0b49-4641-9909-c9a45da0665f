// Code generated by protoc-gen-go. DO NOT EDIT.
// source: super-player-dress/super-player-dress.proto

package superplayerdress // import "golang.52tt.com/protocol/services/superplayerdress"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 装扮类型
type DressType int32

const (
	DressType_DRESS_TYPE_UNSPECIFIC      DressType = 0
	DressType_DRESS_TYPE_ROOM_SUIT       DressType = 1
	DressType_DRESS_TYPE_SPECIAL_CONCERN DressType = 2
	DressType_DRESS_TYPE_CHAT_BACKGROUND DressType = 3
	DressType_DRESS_TYPE_CHAT_BUBBLE     DressType = 4
)

var DressType_name = map[int32]string{
	0: "DRESS_TYPE_UNSPECIFIC",
	1: "DRESS_TYPE_ROOM_SUIT",
	2: "DRESS_TYPE_SPECIAL_CONCERN",
	3: "DRESS_TYPE_CHAT_BACKGROUND",
	4: "DRESS_TYPE_CHAT_BUBBLE",
}
var DressType_value = map[string]int32{
	"DRESS_TYPE_UNSPECIFIC":      0,
	"DRESS_TYPE_ROOM_SUIT":       1,
	"DRESS_TYPE_SPECIAL_CONCERN": 2,
	"DRESS_TYPE_CHAT_BACKGROUND": 3,
	"DRESS_TYPE_CHAT_BUBBLE":     4,
}

func (x DressType) String() string {
	return proto.EnumName(DressType_name, int32(x))
}
func (DressType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{0}
}

// 装扮状态
type DressStatusType int32

const (
	DressStatusType_DRESS_STATUS_SHELF   DressStatusType = 0
	DressStatusType_DRESS_STATUS_UNSHELF DressStatusType = 1
)

var DressStatusType_name = map[int32]string{
	0: "DRESS_STATUS_SHELF",
	1: "DRESS_STATUS_UNSHELF",
}
var DressStatusType_value = map[string]int32{
	"DRESS_STATUS_SHELF":   0,
	"DRESS_STATUS_UNSHELF": 1,
}

func (x DressStatusType) String() string {
	return proto.EnumName(DressStatusType_name, int32(x))
}
func (DressStatusType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{1}
}

// 体验装扮配置类型
type ExperienceConfigType int32

const (
	ExperienceConfigType_EXPERIENCE_CONFIG_TYPE_UNSPECIFIC ExperienceConfigType = 0
	ExperienceConfigType_EXPERIENCE_CONFIG_TYPE_LEVEL      ExperienceConfigType = 1
	ExperienceConfigType_EXPERIENCE_CONFIG_TYPE_ITEM       ExperienceConfigType = 2
)

var ExperienceConfigType_name = map[int32]string{
	0: "EXPERIENCE_CONFIG_TYPE_UNSPECIFIC",
	1: "EXPERIENCE_CONFIG_TYPE_LEVEL",
	2: "EXPERIENCE_CONFIG_TYPE_ITEM",
}
var ExperienceConfigType_value = map[string]int32{
	"EXPERIENCE_CONFIG_TYPE_UNSPECIFIC": 0,
	"EXPERIENCE_CONFIG_TYPE_LEVEL":      1,
	"EXPERIENCE_CONFIG_TYPE_ITEM":       2,
}

func (x ExperienceConfigType) String() string {
	return proto.EnumName(ExperienceConfigType_name, int32(x))
}
func (ExperienceConfigType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{2}
}

// 体验装扮等级类型
type LevelType int32

const (
	LevelType_LEVEL_TYPE_UNSPECIFIC         LevelType = 0
	LevelType_LEVEL_TYPE_LESS_THAN          LevelType = 1
	LevelType_LEVEL_TYPE_MORE_THAN          LevelType = 2
	LevelType_LEVEL_TYPE_EQUAL              LevelType = 3
	LevelType_LEVEL_TYPE_LESS_THAN_OR_EQUAL LevelType = 4
	LevelType_LEVEL_TYPE_MORE_THAN_OR_EQUAL LevelType = 5
)

var LevelType_name = map[int32]string{
	0: "LEVEL_TYPE_UNSPECIFIC",
	1: "LEVEL_TYPE_LESS_THAN",
	2: "LEVEL_TYPE_MORE_THAN",
	3: "LEVEL_TYPE_EQUAL",
	4: "LEVEL_TYPE_LESS_THAN_OR_EQUAL",
	5: "LEVEL_TYPE_MORE_THAN_OR_EQUAL",
}
var LevelType_value = map[string]int32{
	"LEVEL_TYPE_UNSPECIFIC":         0,
	"LEVEL_TYPE_LESS_THAN":          1,
	"LEVEL_TYPE_MORE_THAN":          2,
	"LEVEL_TYPE_EQUAL":              3,
	"LEVEL_TYPE_LESS_THAN_OR_EQUAL": 4,
	"LEVEL_TYPE_MORE_THAN_OR_EQUAL": 5,
}

func (x LevelType) String() string {
	return proto.EnumName(LevelType_name, int32(x))
}
func (LevelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{3}
}

// 会员装扮
type Dress struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	ResourceUrl          string   `protobuf:"bytes,3,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	ResourceMd5          string   `protobuf:"bytes,4,opt,name=resource_md5,json=resourceMd5,proto3" json:"resource_md5,omitempty"`
	MinClientVersion     string   `protobuf:"bytes,5,opt,name=min_client_version,json=minClientVersion,proto3" json:"min_client_version,omitempty"`
	Name                 string   `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string   `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc,omitempty"`
	Version              int64    `protobuf:"varint,8,opt,name=version,proto3" json:"version,omitempty"`
	WebList              string   `protobuf:"bytes,9,opt,name=web_list,json=webList,proto3" json:"web_list,omitempty"`
	WebPreview           string   `protobuf:"bytes,10,opt,name=web_preview,json=webPreview,proto3" json:"web_preview,omitempty"`
	WebAudio             string   `protobuf:"bytes,11,opt,name=web_audio,json=webAudio,proto3" json:"web_audio,omitempty"`
	WebVideo             string   `protobuf:"bytes,12,opt,name=web_video,json=webVideo,proto3" json:"web_video,omitempty"`
	WebConfig            string   `protobuf:"bytes,13,opt,name=web_config,json=webConfig,proto3" json:"web_config,omitempty"`
	WebTop               string   `protobuf:"bytes,14,opt,name=web_top,json=webTop,proto3" json:"web_top,omitempty"`
	WebStatic            string   `protobuf:"bytes,15,opt,name=web_static,json=webStatic,proto3" json:"web_static,omitempty"`
	Removed              uint32   `protobuf:"varint,16,opt,name=removed,proto3" json:"removed,omitempty"`
	ComDesc              string   `protobuf:"bytes,17,opt,name=com_desc,json=comDesc,proto3" json:"com_desc,omitempty"`
	BeginTs              uint64   `protobuf:"varint,18,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint64   `protobuf:"varint,19,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	IsDefault            uint32   `protobuf:"varint,20,opt,name=is_default,json=isDefault,proto3" json:"is_default,omitempty"`
	Operator             string   `protobuf:"bytes,21,opt,name=operator,proto3" json:"operator,omitempty"`
	Index                int64    `protobuf:"varint,22,opt,name=index,proto3" json:"index,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Dress) Reset()         { *m = Dress{} }
func (m *Dress) String() string { return proto.CompactTextString(m) }
func (*Dress) ProtoMessage()    {}
func (*Dress) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{0}
}
func (m *Dress) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Dress.Unmarshal(m, b)
}
func (m *Dress) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Dress.Marshal(b, m, deterministic)
}
func (dst *Dress) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Dress.Merge(dst, src)
}
func (m *Dress) XXX_Size() int {
	return xxx_messageInfo_Dress.Size(m)
}
func (m *Dress) XXX_DiscardUnknown() {
	xxx_messageInfo_Dress.DiscardUnknown(m)
}

var xxx_messageInfo_Dress proto.InternalMessageInfo

func (m *Dress) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Dress) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *Dress) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *Dress) GetResourceMd5() string {
	if m != nil {
		return m.ResourceMd5
	}
	return ""
}

func (m *Dress) GetMinClientVersion() string {
	if m != nil {
		return m.MinClientVersion
	}
	return ""
}

func (m *Dress) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Dress) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *Dress) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *Dress) GetWebList() string {
	if m != nil {
		return m.WebList
	}
	return ""
}

func (m *Dress) GetWebPreview() string {
	if m != nil {
		return m.WebPreview
	}
	return ""
}

func (m *Dress) GetWebAudio() string {
	if m != nil {
		return m.WebAudio
	}
	return ""
}

func (m *Dress) GetWebVideo() string {
	if m != nil {
		return m.WebVideo
	}
	return ""
}

func (m *Dress) GetWebConfig() string {
	if m != nil {
		return m.WebConfig
	}
	return ""
}

func (m *Dress) GetWebTop() string {
	if m != nil {
		return m.WebTop
	}
	return ""
}

func (m *Dress) GetWebStatic() string {
	if m != nil {
		return m.WebStatic
	}
	return ""
}

func (m *Dress) GetRemoved() uint32 {
	if m != nil {
		return m.Removed
	}
	return 0
}

func (m *Dress) GetComDesc() string {
	if m != nil {
		return m.ComDesc
	}
	return ""
}

func (m *Dress) GetBeginTs() uint64 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *Dress) GetEndTs() uint64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *Dress) GetIsDefault() uint32 {
	if m != nil {
		return m.IsDefault
	}
	return 0
}

func (m *Dress) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *Dress) GetIndex() int64 {
	if m != nil {
		return m.Index
	}
	return 0
}

// 装扮信息
type DressInfo struct {
	DressType            uint32   `protobuf:"varint,1,opt,name=dress_type,json=dressType,proto3" json:"dress_type,omitempty"`
	DressInfo            *Dress   `protobuf:"bytes,2,opt,name=dress_info,json=dressInfo,proto3" json:"dress_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DressInfo) Reset()         { *m = DressInfo{} }
func (m *DressInfo) String() string { return proto.CompactTextString(m) }
func (*DressInfo) ProtoMessage()    {}
func (*DressInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{1}
}
func (m *DressInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DressInfo.Unmarshal(m, b)
}
func (m *DressInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DressInfo.Marshal(b, m, deterministic)
}
func (dst *DressInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DressInfo.Merge(dst, src)
}
func (m *DressInfo) XXX_Size() int {
	return xxx_messageInfo_DressInfo.Size(m)
}
func (m *DressInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DressInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DressInfo proto.InternalMessageInfo

func (m *DressInfo) GetDressType() uint32 {
	if m != nil {
		return m.DressType
	}
	return 0
}

func (m *DressInfo) GetDressInfo() *Dress {
	if m != nil {
		return m.DressInfo
	}
	return nil
}

// 添加装扮
type AddDressInfoReq struct {
	Info                 *DressInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *AddDressInfoReq) Reset()         { *m = AddDressInfoReq{} }
func (m *AddDressInfoReq) String() string { return proto.CompactTextString(m) }
func (*AddDressInfoReq) ProtoMessage()    {}
func (*AddDressInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{2}
}
func (m *AddDressInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddDressInfoReq.Unmarshal(m, b)
}
func (m *AddDressInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddDressInfoReq.Marshal(b, m, deterministic)
}
func (dst *AddDressInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddDressInfoReq.Merge(dst, src)
}
func (m *AddDressInfoReq) XXX_Size() int {
	return xxx_messageInfo_AddDressInfoReq.Size(m)
}
func (m *AddDressInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddDressInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddDressInfoReq proto.InternalMessageInfo

func (m *AddDressInfoReq) GetInfo() *DressInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type AddDressInfoResp struct {
	MaxVersion           uint32   `protobuf:"varint,1,opt,name=max_version,json=maxVersion,proto3" json:"max_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddDressInfoResp) Reset()         { *m = AddDressInfoResp{} }
func (m *AddDressInfoResp) String() string { return proto.CompactTextString(m) }
func (*AddDressInfoResp) ProtoMessage()    {}
func (*AddDressInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{3}
}
func (m *AddDressInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddDressInfoResp.Unmarshal(m, b)
}
func (m *AddDressInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddDressInfoResp.Marshal(b, m, deterministic)
}
func (dst *AddDressInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddDressInfoResp.Merge(dst, src)
}
func (m *AddDressInfoResp) XXX_Size() int {
	return xxx_messageInfo_AddDressInfoResp.Size(m)
}
func (m *AddDressInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddDressInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddDressInfoResp proto.InternalMessageInfo

func (m *AddDressInfoResp) GetMaxVersion() uint32 {
	if m != nil {
		return m.MaxVersion
	}
	return 0
}

// 更新装扮
type UpdateDressInfoReq struct {
	Info                 *DressInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	MaxVersion           uint32     `protobuf:"varint,2,opt,name=max_version,json=maxVersion,proto3" json:"max_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UpdateDressInfoReq) Reset()         { *m = UpdateDressInfoReq{} }
func (m *UpdateDressInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateDressInfoReq) ProtoMessage()    {}
func (*UpdateDressInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{4}
}
func (m *UpdateDressInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateDressInfoReq.Unmarshal(m, b)
}
func (m *UpdateDressInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateDressInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpdateDressInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateDressInfoReq.Merge(dst, src)
}
func (m *UpdateDressInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpdateDressInfoReq.Size(m)
}
func (m *UpdateDressInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateDressInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateDressInfoReq proto.InternalMessageInfo

func (m *UpdateDressInfoReq) GetInfo() *DressInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *UpdateDressInfoReq) GetMaxVersion() uint32 {
	if m != nil {
		return m.MaxVersion
	}
	return 0
}

type UpdateDressInfoResp struct {
	MaxVersion           uint32   `protobuf:"varint,1,opt,name=max_version,json=maxVersion,proto3" json:"max_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateDressInfoResp) Reset()         { *m = UpdateDressInfoResp{} }
func (m *UpdateDressInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpdateDressInfoResp) ProtoMessage()    {}
func (*UpdateDressInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{5}
}
func (m *UpdateDressInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateDressInfoResp.Unmarshal(m, b)
}
func (m *UpdateDressInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateDressInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpdateDressInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateDressInfoResp.Merge(dst, src)
}
func (m *UpdateDressInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpdateDressInfoResp.Size(m)
}
func (m *UpdateDressInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateDressInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateDressInfoResp proto.InternalMessageInfo

func (m *UpdateDressInfoResp) GetMaxVersion() uint32 {
	if m != nil {
		return m.MaxVersion
	}
	return 0
}

// 更新多个装扮
type UpdateDressInfoListReq struct {
	InfoList             []*DressInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	MaxVersion           uint32       `protobuf:"varint,2,opt,name=max_version,json=maxVersion,proto3" json:"max_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdateDressInfoListReq) Reset()         { *m = UpdateDressInfoListReq{} }
func (m *UpdateDressInfoListReq) String() string { return proto.CompactTextString(m) }
func (*UpdateDressInfoListReq) ProtoMessage()    {}
func (*UpdateDressInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{6}
}
func (m *UpdateDressInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateDressInfoListReq.Unmarshal(m, b)
}
func (m *UpdateDressInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateDressInfoListReq.Marshal(b, m, deterministic)
}
func (dst *UpdateDressInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateDressInfoListReq.Merge(dst, src)
}
func (m *UpdateDressInfoListReq) XXX_Size() int {
	return xxx_messageInfo_UpdateDressInfoListReq.Size(m)
}
func (m *UpdateDressInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateDressInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateDressInfoListReq proto.InternalMessageInfo

func (m *UpdateDressInfoListReq) GetInfoList() []*DressInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *UpdateDressInfoListReq) GetMaxVersion() uint32 {
	if m != nil {
		return m.MaxVersion
	}
	return 0
}

type UpdateDressInfoListResp struct {
	MaxVersion           uint32   `protobuf:"varint,1,opt,name=max_version,json=maxVersion,proto3" json:"max_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateDressInfoListResp) Reset()         { *m = UpdateDressInfoListResp{} }
func (m *UpdateDressInfoListResp) String() string { return proto.CompactTextString(m) }
func (*UpdateDressInfoListResp) ProtoMessage()    {}
func (*UpdateDressInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{7}
}
func (m *UpdateDressInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateDressInfoListResp.Unmarshal(m, b)
}
func (m *UpdateDressInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateDressInfoListResp.Marshal(b, m, deterministic)
}
func (dst *UpdateDressInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateDressInfoListResp.Merge(dst, src)
}
func (m *UpdateDressInfoListResp) XXX_Size() int {
	return xxx_messageInfo_UpdateDressInfoListResp.Size(m)
}
func (m *UpdateDressInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateDressInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateDressInfoListResp proto.InternalMessageInfo

func (m *UpdateDressInfoListResp) GetMaxVersion() uint32 {
	if m != nil {
		return m.MaxVersion
	}
	return 0
}

// 删除装扮
type DelDressInfoReq struct {
	DressType            uint32   `protobuf:"varint,1,opt,name=dress_type,json=dressType,proto3" json:"dress_type,omitempty"`
	DressId              uint32   `protobuf:"varint,2,opt,name=dress_id,json=dressId,proto3" json:"dress_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelDressInfoReq) Reset()         { *m = DelDressInfoReq{} }
func (m *DelDressInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelDressInfoReq) ProtoMessage()    {}
func (*DelDressInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{8}
}
func (m *DelDressInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelDressInfoReq.Unmarshal(m, b)
}
func (m *DelDressInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelDressInfoReq.Marshal(b, m, deterministic)
}
func (dst *DelDressInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelDressInfoReq.Merge(dst, src)
}
func (m *DelDressInfoReq) XXX_Size() int {
	return xxx_messageInfo_DelDressInfoReq.Size(m)
}
func (m *DelDressInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelDressInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelDressInfoReq proto.InternalMessageInfo

func (m *DelDressInfoReq) GetDressType() uint32 {
	if m != nil {
		return m.DressType
	}
	return 0
}

func (m *DelDressInfoReq) GetDressId() uint32 {
	if m != nil {
		return m.DressId
	}
	return 0
}

type DelDressInfoResp struct {
	MaxVersion           uint32   `protobuf:"varint,1,opt,name=max_version,json=maxVersion,proto3" json:"max_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelDressInfoResp) Reset()         { *m = DelDressInfoResp{} }
func (m *DelDressInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelDressInfoResp) ProtoMessage()    {}
func (*DelDressInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{9}
}
func (m *DelDressInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelDressInfoResp.Unmarshal(m, b)
}
func (m *DelDressInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelDressInfoResp.Marshal(b, m, deterministic)
}
func (dst *DelDressInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelDressInfoResp.Merge(dst, src)
}
func (m *DelDressInfoResp) XXX_Size() int {
	return xxx_messageInfo_DelDressInfoResp.Size(m)
}
func (m *DelDressInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelDressInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelDressInfoResp proto.InternalMessageInfo

func (m *DelDressInfoResp) GetMaxVersion() uint32 {
	if m != nil {
		return m.MaxVersion
	}
	return 0
}

// 获取装扮列表
type GetDressInfoListReq struct {
	DressType            uint32   `protobuf:"varint,1,opt,name=dress_type,json=dressType,proto3" json:"dress_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDressInfoListReq) Reset()         { *m = GetDressInfoListReq{} }
func (m *GetDressInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetDressInfoListReq) ProtoMessage()    {}
func (*GetDressInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{10}
}
func (m *GetDressInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDressInfoListReq.Unmarshal(m, b)
}
func (m *GetDressInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDressInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetDressInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDressInfoListReq.Merge(dst, src)
}
func (m *GetDressInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetDressInfoListReq.Size(m)
}
func (m *GetDressInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDressInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDressInfoListReq proto.InternalMessageInfo

func (m *GetDressInfoListReq) GetDressType() uint32 {
	if m != nil {
		return m.DressType
	}
	return 0
}

type GetDressInfoListResp struct {
	InfoList             []*DressInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	MaxVersion           uint32       `protobuf:"varint,2,opt,name=max_version,json=maxVersion,proto3" json:"max_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetDressInfoListResp) Reset()         { *m = GetDressInfoListResp{} }
func (m *GetDressInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetDressInfoListResp) ProtoMessage()    {}
func (*GetDressInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{11}
}
func (m *GetDressInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDressInfoListResp.Unmarshal(m, b)
}
func (m *GetDressInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDressInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetDressInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDressInfoListResp.Merge(dst, src)
}
func (m *GetDressInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetDressInfoListResp.Size(m)
}
func (m *GetDressInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDressInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDressInfoListResp proto.InternalMessageInfo

func (m *GetDressInfoListResp) GetInfoList() []*DressInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetDressInfoListResp) GetMaxVersion() uint32 {
	if m != nil {
		return m.MaxVersion
	}
	return 0
}

type GetDressConfigMaxVersionReq struct {
	MaxVersion           int64     `protobuf:"varint,1,opt,name=max_version,json=maxVersion,proto3" json:"max_version,omitempty"`
	DressType            DressType `protobuf:"varint,2,opt,name=dress_type,json=dressType,proto3,enum=superplayerdress.DressType" json:"dress_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetDressConfigMaxVersionReq) Reset()         { *m = GetDressConfigMaxVersionReq{} }
func (m *GetDressConfigMaxVersionReq) String() string { return proto.CompactTextString(m) }
func (*GetDressConfigMaxVersionReq) ProtoMessage()    {}
func (*GetDressConfigMaxVersionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{12}
}
func (m *GetDressConfigMaxVersionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDressConfigMaxVersionReq.Unmarshal(m, b)
}
func (m *GetDressConfigMaxVersionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDressConfigMaxVersionReq.Marshal(b, m, deterministic)
}
func (dst *GetDressConfigMaxVersionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDressConfigMaxVersionReq.Merge(dst, src)
}
func (m *GetDressConfigMaxVersionReq) XXX_Size() int {
	return xxx_messageInfo_GetDressConfigMaxVersionReq.Size(m)
}
func (m *GetDressConfigMaxVersionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDressConfigMaxVersionReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDressConfigMaxVersionReq proto.InternalMessageInfo

func (m *GetDressConfigMaxVersionReq) GetMaxVersion() int64 {
	if m != nil {
		return m.MaxVersion
	}
	return 0
}

func (m *GetDressConfigMaxVersionReq) GetDressType() DressType {
	if m != nil {
		return m.DressType
	}
	return DressType_DRESS_TYPE_UNSPECIFIC
}

type GetDressConfigMaxVersionResp struct {
	MaxVersion           int64     `protobuf:"varint,1,opt,name=max_version,json=maxVersion,proto3" json:"max_version,omitempty"`
	DressType            DressType `protobuf:"varint,2,opt,name=dress_type,json=dressType,proto3,enum=superplayerdress.DressType" json:"dress_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetDressConfigMaxVersionResp) Reset()         { *m = GetDressConfigMaxVersionResp{} }
func (m *GetDressConfigMaxVersionResp) String() string { return proto.CompactTextString(m) }
func (*GetDressConfigMaxVersionResp) ProtoMessage()    {}
func (*GetDressConfigMaxVersionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{13}
}
func (m *GetDressConfigMaxVersionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDressConfigMaxVersionResp.Unmarshal(m, b)
}
func (m *GetDressConfigMaxVersionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDressConfigMaxVersionResp.Marshal(b, m, deterministic)
}
func (dst *GetDressConfigMaxVersionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDressConfigMaxVersionResp.Merge(dst, src)
}
func (m *GetDressConfigMaxVersionResp) XXX_Size() int {
	return xxx_messageInfo_GetDressConfigMaxVersionResp.Size(m)
}
func (m *GetDressConfigMaxVersionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDressConfigMaxVersionResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDressConfigMaxVersionResp proto.InternalMessageInfo

func (m *GetDressConfigMaxVersionResp) GetMaxVersion() int64 {
	if m != nil {
		return m.MaxVersion
	}
	return 0
}

func (m *GetDressConfigMaxVersionResp) GetDressType() DressType {
	if m != nil {
		return m.DressType
	}
	return DressType_DRESS_TYPE_UNSPECIFIC
}

// 会员装扮配置列表
type GetDressConfigListReq struct {
	DressType            DressType `protobuf:"varint,1,opt,name=dress_type,json=dressType,proto3,enum=superplayerdress.DressType" json:"dress_type,omitempty"`
	Offset               uint32    `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32    `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetDressConfigListReq) Reset()         { *m = GetDressConfigListReq{} }
func (m *GetDressConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetDressConfigListReq) ProtoMessage()    {}
func (*GetDressConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{14}
}
func (m *GetDressConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDressConfigListReq.Unmarshal(m, b)
}
func (m *GetDressConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDressConfigListReq.Marshal(b, m, deterministic)
}
func (dst *GetDressConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDressConfigListReq.Merge(dst, src)
}
func (m *GetDressConfigListReq) XXX_Size() int {
	return xxx_messageInfo_GetDressConfigListReq.Size(m)
}
func (m *GetDressConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDressConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDressConfigListReq proto.InternalMessageInfo

func (m *GetDressConfigListReq) GetDressType() DressType {
	if m != nil {
		return m.DressType
	}
	return DressType_DRESS_TYPE_UNSPECIFIC
}

func (m *GetDressConfigListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetDressConfigListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetDressConfigListResp struct {
	DressType            DressType `protobuf:"varint,1,opt,name=dress_type,json=dressType,proto3,enum=superplayerdress.DressType" json:"dress_type,omitempty"`
	DressList            []*Dress  `protobuf:"bytes,2,rep,name=dress_list,json=dressList,proto3" json:"dress_list,omitempty"`
	Offset               uint32    `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32    `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	IsEnd                bool      `protobuf:"varint,5,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetDressConfigListResp) Reset()         { *m = GetDressConfigListResp{} }
func (m *GetDressConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetDressConfigListResp) ProtoMessage()    {}
func (*GetDressConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{15}
}
func (m *GetDressConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDressConfigListResp.Unmarshal(m, b)
}
func (m *GetDressConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDressConfigListResp.Marshal(b, m, deterministic)
}
func (dst *GetDressConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDressConfigListResp.Merge(dst, src)
}
func (m *GetDressConfigListResp) XXX_Size() int {
	return xxx_messageInfo_GetDressConfigListResp.Size(m)
}
func (m *GetDressConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDressConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDressConfigListResp proto.InternalMessageInfo

func (m *GetDressConfigListResp) GetDressType() DressType {
	if m != nil {
		return m.DressType
	}
	return DressType_DRESS_TYPE_UNSPECIFIC
}

func (m *GetDressConfigListResp) GetDressList() []*Dress {
	if m != nil {
		return m.DressList
	}
	return nil
}

func (m *GetDressConfigListResp) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetDressConfigListResp) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetDressConfigListResp) GetIsEnd() bool {
	if m != nil {
		return m.IsEnd
	}
	return false
}

// 获取正在使用的装扮（通用 只返回一个ID的）
type GetDressInUseReq struct {
	Id                   int64     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DressType            DressType `protobuf:"varint,2,opt,name=dress_type,json=dressType,proto3,enum=superplayerdress.DressType" json:"dress_type,omitempty"`
	Level                uint32    `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetDressInUseReq) Reset()         { *m = GetDressInUseReq{} }
func (m *GetDressInUseReq) String() string { return proto.CompactTextString(m) }
func (*GetDressInUseReq) ProtoMessage()    {}
func (*GetDressInUseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{16}
}
func (m *GetDressInUseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDressInUseReq.Unmarshal(m, b)
}
func (m *GetDressInUseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDressInUseReq.Marshal(b, m, deterministic)
}
func (dst *GetDressInUseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDressInUseReq.Merge(dst, src)
}
func (m *GetDressInUseReq) XXX_Size() int {
	return xxx_messageInfo_GetDressInUseReq.Size(m)
}
func (m *GetDressInUseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDressInUseReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDressInUseReq proto.InternalMessageInfo

func (m *GetDressInUseReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetDressInUseReq) GetDressType() DressType {
	if m != nil {
		return m.DressType
	}
	return DressType_DRESS_TYPE_UNSPECIFIC
}

func (m *GetDressInUseReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

// 获取正在使用的装扮
type GetDressInUseResp struct {
	Id                   int64     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DressType            DressType `protobuf:"varint,2,opt,name=dress_type,json=dressType,proto3,enum=superplayerdress.DressType" json:"dress_type,omitempty"`
	Level                uint32    `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	DressId              uint32    `protobuf:"varint,4,opt,name=dress_id,json=dressId,proto3" json:"dress_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetDressInUseResp) Reset()         { *m = GetDressInUseResp{} }
func (m *GetDressInUseResp) String() string { return proto.CompactTextString(m) }
func (*GetDressInUseResp) ProtoMessage()    {}
func (*GetDressInUseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{17}
}
func (m *GetDressInUseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDressInUseResp.Unmarshal(m, b)
}
func (m *GetDressInUseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDressInUseResp.Marshal(b, m, deterministic)
}
func (dst *GetDressInUseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDressInUseResp.Merge(dst, src)
}
func (m *GetDressInUseResp) XXX_Size() int {
	return xxx_messageInfo_GetDressInUseResp.Size(m)
}
func (m *GetDressInUseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDressInUseResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDressInUseResp proto.InternalMessageInfo

func (m *GetDressInUseResp) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetDressInUseResp) GetDressType() DressType {
	if m != nil {
		return m.DressType
	}
	return DressType_DRESS_TYPE_UNSPECIFIC
}

func (m *GetDressInUseResp) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GetDressInUseResp) GetDressId() uint32 {
	if m != nil {
		return m.DressId
	}
	return 0
}

// 设置正在使用的装扮
type SetDressInUseReq struct {
	Id                   int64     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DressType            DressType `protobuf:"varint,2,opt,name=dress_type,json=dressType,proto3,enum=superplayerdress.DressType" json:"dress_type,omitempty"`
	Level                uint32    `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	DressId              uint32    `protobuf:"varint,4,opt,name=dress_id,json=dressId,proto3" json:"dress_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *SetDressInUseReq) Reset()         { *m = SetDressInUseReq{} }
func (m *SetDressInUseReq) String() string { return proto.CompactTextString(m) }
func (*SetDressInUseReq) ProtoMessage()    {}
func (*SetDressInUseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{18}
}
func (m *SetDressInUseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDressInUseReq.Unmarshal(m, b)
}
func (m *SetDressInUseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDressInUseReq.Marshal(b, m, deterministic)
}
func (dst *SetDressInUseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDressInUseReq.Merge(dst, src)
}
func (m *SetDressInUseReq) XXX_Size() int {
	return xxx_messageInfo_SetDressInUseReq.Size(m)
}
func (m *SetDressInUseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDressInUseReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetDressInUseReq proto.InternalMessageInfo

func (m *SetDressInUseReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SetDressInUseReq) GetDressType() DressType {
	if m != nil {
		return m.DressType
	}
	return DressType_DRESS_TYPE_UNSPECIFIC
}

func (m *SetDressInUseReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *SetDressInUseReq) GetDressId() uint32 {
	if m != nil {
		return m.DressId
	}
	return 0
}

//
type SetDressInUseResp struct {
	Id                   int64     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DressType            DressType `protobuf:"varint,2,opt,name=dress_type,json=dressType,proto3,enum=superplayerdress.DressType" json:"dress_type,omitempty"`
	Level                uint32    `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	DressId              uint32    `protobuf:"varint,4,opt,name=dress_id,json=dressId,proto3" json:"dress_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *SetDressInUseResp) Reset()         { *m = SetDressInUseResp{} }
func (m *SetDressInUseResp) String() string { return proto.CompactTextString(m) }
func (*SetDressInUseResp) ProtoMessage()    {}
func (*SetDressInUseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{19}
}
func (m *SetDressInUseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDressInUseResp.Unmarshal(m, b)
}
func (m *SetDressInUseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDressInUseResp.Marshal(b, m, deterministic)
}
func (dst *SetDressInUseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDressInUseResp.Merge(dst, src)
}
func (m *SetDressInUseResp) XXX_Size() int {
	return xxx_messageInfo_SetDressInUseResp.Size(m)
}
func (m *SetDressInUseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDressInUseResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetDressInUseResp proto.InternalMessageInfo

func (m *SetDressInUseResp) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SetDressInUseResp) GetDressType() DressType {
	if m != nil {
		return m.DressType
	}
	return DressType_DRESS_TYPE_UNSPECIFIC
}

func (m *SetDressInUseResp) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *SetDressInUseResp) GetDressId() uint32 {
	if m != nil {
		return m.DressId
	}
	return 0
}

// 前端会员装扮配置列表
type GetWebDressConfigReq struct {
	DressType            DressType `protobuf:"varint,1,opt,name=dress_type,json=dressType,proto3,enum=superplayerdress.DressType" json:"dress_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetWebDressConfigReq) Reset()         { *m = GetWebDressConfigReq{} }
func (m *GetWebDressConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetWebDressConfigReq) ProtoMessage()    {}
func (*GetWebDressConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{20}
}
func (m *GetWebDressConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWebDressConfigReq.Unmarshal(m, b)
}
func (m *GetWebDressConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWebDressConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetWebDressConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWebDressConfigReq.Merge(dst, src)
}
func (m *GetWebDressConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetWebDressConfigReq.Size(m)
}
func (m *GetWebDressConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWebDressConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWebDressConfigReq proto.InternalMessageInfo

func (m *GetWebDressConfigReq) GetDressType() DressType {
	if m != nil {
		return m.DressType
	}
	return DressType_DRESS_TYPE_UNSPECIFIC
}

type GetWebDressConfigResp struct {
	DressType            DressType `protobuf:"varint,1,opt,name=dress_type,json=dressType,proto3,enum=superplayerdress.DressType" json:"dress_type,omitempty"`
	DressList            []*Dress  `protobuf:"bytes,2,rep,name=dress_list,json=dressList,proto3" json:"dress_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetWebDressConfigResp) Reset()         { *m = GetWebDressConfigResp{} }
func (m *GetWebDressConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetWebDressConfigResp) ProtoMessage()    {}
func (*GetWebDressConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{21}
}
func (m *GetWebDressConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWebDressConfigResp.Unmarshal(m, b)
}
func (m *GetWebDressConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWebDressConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetWebDressConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWebDressConfigResp.Merge(dst, src)
}
func (m *GetWebDressConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetWebDressConfigResp.Size(m)
}
func (m *GetWebDressConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWebDressConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWebDressConfigResp proto.InternalMessageInfo

func (m *GetWebDressConfigResp) GetDressType() DressType {
	if m != nil {
		return m.DressType
	}
	return DressType_DRESS_TYPE_UNSPECIFIC
}

func (m *GetWebDressConfigResp) GetDressList() []*Dress {
	if m != nil {
		return m.DressList
	}
	return nil
}

// 获取用户当前聊天背景
type GetUserCurrChatBgDressIdListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCurrChatBgDressIdListReq) Reset()         { *m = GetUserCurrChatBgDressIdListReq{} }
func (m *GetUserCurrChatBgDressIdListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserCurrChatBgDressIdListReq) ProtoMessage()    {}
func (*GetUserCurrChatBgDressIdListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{22}
}
func (m *GetUserCurrChatBgDressIdListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCurrChatBgDressIdListReq.Unmarshal(m, b)
}
func (m *GetUserCurrChatBgDressIdListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCurrChatBgDressIdListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserCurrChatBgDressIdListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCurrChatBgDressIdListReq.Merge(dst, src)
}
func (m *GetUserCurrChatBgDressIdListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserCurrChatBgDressIdListReq.Size(m)
}
func (m *GetUserCurrChatBgDressIdListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCurrChatBgDressIdListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCurrChatBgDressIdListReq proto.InternalMessageInfo

func (m *GetUserCurrChatBgDressIdListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserCurrChatBgDressIdListReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

// 特殊装扮
type ChatBgSpecial struct {
	ToAccount            string   `protobuf:"bytes,1,opt,name=to_account,json=toAccount,proto3" json:"to_account,omitempty"`
	DressId              uint32   `protobuf:"varint,2,opt,name=dress_id,json=dressId,proto3" json:"dress_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChatBgSpecial) Reset()         { *m = ChatBgSpecial{} }
func (m *ChatBgSpecial) String() string { return proto.CompactTextString(m) }
func (*ChatBgSpecial) ProtoMessage()    {}
func (*ChatBgSpecial) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{23}
}
func (m *ChatBgSpecial) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatBgSpecial.Unmarshal(m, b)
}
func (m *ChatBgSpecial) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatBgSpecial.Marshal(b, m, deterministic)
}
func (dst *ChatBgSpecial) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatBgSpecial.Merge(dst, src)
}
func (m *ChatBgSpecial) XXX_Size() int {
	return xxx_messageInfo_ChatBgSpecial.Size(m)
}
func (m *ChatBgSpecial) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatBgSpecial.DiscardUnknown(m)
}

var xxx_messageInfo_ChatBgSpecial proto.InternalMessageInfo

func (m *ChatBgSpecial) GetToAccount() string {
	if m != nil {
		return m.ToAccount
	}
	return ""
}

func (m *ChatBgSpecial) GetDressId() uint32 {
	if m != nil {
		return m.DressId
	}
	return 0
}

type GetUserCurrChatBgDressIdListResp struct {
	CommonDressId        uint32           `protobuf:"varint,1,opt,name=common_dress_id,json=commonDressId,proto3" json:"common_dress_id,omitempty"`
	DressList            []*ChatBgSpecial `protobuf:"bytes,2,rep,name=dress_list,json=dressList,proto3" json:"dress_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserCurrChatBgDressIdListResp) Reset()         { *m = GetUserCurrChatBgDressIdListResp{} }
func (m *GetUserCurrChatBgDressIdListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserCurrChatBgDressIdListResp) ProtoMessage()    {}
func (*GetUserCurrChatBgDressIdListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{24}
}
func (m *GetUserCurrChatBgDressIdListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCurrChatBgDressIdListResp.Unmarshal(m, b)
}
func (m *GetUserCurrChatBgDressIdListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCurrChatBgDressIdListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserCurrChatBgDressIdListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCurrChatBgDressIdListResp.Merge(dst, src)
}
func (m *GetUserCurrChatBgDressIdListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserCurrChatBgDressIdListResp.Size(m)
}
func (m *GetUserCurrChatBgDressIdListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCurrChatBgDressIdListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCurrChatBgDressIdListResp proto.InternalMessageInfo

func (m *GetUserCurrChatBgDressIdListResp) GetCommonDressId() uint32 {
	if m != nil {
		return m.CommonDressId
	}
	return 0
}

func (m *GetUserCurrChatBgDressIdListResp) GetDressList() []*ChatBgSpecial {
	if m != nil {
		return m.DressList
	}
	return nil
}

// 设置正在使用的装扮
type SetChatBgSpecialDressInUseReq struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Level                uint32   `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	DressId              uint32   `protobuf:"varint,4,opt,name=dress_id,json=dressId,proto3" json:"dress_id,omitempty"`
	ToAccount            string   `protobuf:"bytes,5,opt,name=to_account,json=toAccount,proto3" json:"to_account,omitempty"`
	ReplaceAccount       string   `protobuf:"bytes,6,opt,name=replace_account,json=replaceAccount,proto3" json:"replace_account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChatBgSpecialDressInUseReq) Reset()         { *m = SetChatBgSpecialDressInUseReq{} }
func (m *SetChatBgSpecialDressInUseReq) String() string { return proto.CompactTextString(m) }
func (*SetChatBgSpecialDressInUseReq) ProtoMessage()    {}
func (*SetChatBgSpecialDressInUseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{25}
}
func (m *SetChatBgSpecialDressInUseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChatBgSpecialDressInUseReq.Unmarshal(m, b)
}
func (m *SetChatBgSpecialDressInUseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChatBgSpecialDressInUseReq.Marshal(b, m, deterministic)
}
func (dst *SetChatBgSpecialDressInUseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChatBgSpecialDressInUseReq.Merge(dst, src)
}
func (m *SetChatBgSpecialDressInUseReq) XXX_Size() int {
	return xxx_messageInfo_SetChatBgSpecialDressInUseReq.Size(m)
}
func (m *SetChatBgSpecialDressInUseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChatBgSpecialDressInUseReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChatBgSpecialDressInUseReq proto.InternalMessageInfo

func (m *SetChatBgSpecialDressInUseReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SetChatBgSpecialDressInUseReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *SetChatBgSpecialDressInUseReq) GetDressId() uint32 {
	if m != nil {
		return m.DressId
	}
	return 0
}

func (m *SetChatBgSpecialDressInUseReq) GetToAccount() string {
	if m != nil {
		return m.ToAccount
	}
	return ""
}

func (m *SetChatBgSpecialDressInUseReq) GetReplaceAccount() string {
	if m != nil {
		return m.ReplaceAccount
	}
	return ""
}

//
type SetChatBgSpecialDressInUseResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChatBgSpecialDressInUseResp) Reset()         { *m = SetChatBgSpecialDressInUseResp{} }
func (m *SetChatBgSpecialDressInUseResp) String() string { return proto.CompactTextString(m) }
func (*SetChatBgSpecialDressInUseResp) ProtoMessage()    {}
func (*SetChatBgSpecialDressInUseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{26}
}
func (m *SetChatBgSpecialDressInUseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChatBgSpecialDressInUseResp.Unmarshal(m, b)
}
func (m *SetChatBgSpecialDressInUseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChatBgSpecialDressInUseResp.Marshal(b, m, deterministic)
}
func (dst *SetChatBgSpecialDressInUseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChatBgSpecialDressInUseResp.Merge(dst, src)
}
func (m *SetChatBgSpecialDressInUseResp) XXX_Size() int {
	return xxx_messageInfo_SetChatBgSpecialDressInUseResp.Size(m)
}
func (m *SetChatBgSpecialDressInUseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChatBgSpecialDressInUseResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChatBgSpecialDressInUseResp proto.InternalMessageInfo

// 设置默认使用的装扮
type SetDefaultDressInUseReq struct {
	Uid                  int64    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetDefaultDressInUseReq) Reset()         { *m = SetDefaultDressInUseReq{} }
func (m *SetDefaultDressInUseReq) String() string { return proto.CompactTextString(m) }
func (*SetDefaultDressInUseReq) ProtoMessage()    {}
func (*SetDefaultDressInUseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{27}
}
func (m *SetDefaultDressInUseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDefaultDressInUseReq.Unmarshal(m, b)
}
func (m *SetDefaultDressInUseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDefaultDressInUseReq.Marshal(b, m, deterministic)
}
func (dst *SetDefaultDressInUseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDefaultDressInUseReq.Merge(dst, src)
}
func (m *SetDefaultDressInUseReq) XXX_Size() int {
	return xxx_messageInfo_SetDefaultDressInUseReq.Size(m)
}
func (m *SetDefaultDressInUseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDefaultDressInUseReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetDefaultDressInUseReq proto.InternalMessageInfo

func (m *SetDefaultDressInUseReq) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SetDefaultDressInUseResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetDefaultDressInUseResp) Reset()         { *m = SetDefaultDressInUseResp{} }
func (m *SetDefaultDressInUseResp) String() string { return proto.CompactTextString(m) }
func (*SetDefaultDressInUseResp) ProtoMessage()    {}
func (*SetDefaultDressInUseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{28}
}
func (m *SetDefaultDressInUseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDefaultDressInUseResp.Unmarshal(m, b)
}
func (m *SetDefaultDressInUseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDefaultDressInUseResp.Marshal(b, m, deterministic)
}
func (dst *SetDefaultDressInUseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDefaultDressInUseResp.Merge(dst, src)
}
func (m *SetDefaultDressInUseResp) XXX_Size() int {
	return xxx_messageInfo_SetDefaultDressInUseResp.Size(m)
}
func (m *SetDefaultDressInUseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDefaultDressInUseResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetDefaultDressInUseResp proto.InternalMessageInfo

// 获取用户当前聊天背景
type GetUserCurrChatBgSpecialDressIdReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCurrChatBgSpecialDressIdReq) Reset()         { *m = GetUserCurrChatBgSpecialDressIdReq{} }
func (m *GetUserCurrChatBgSpecialDressIdReq) String() string { return proto.CompactTextString(m) }
func (*GetUserCurrChatBgSpecialDressIdReq) ProtoMessage()    {}
func (*GetUserCurrChatBgSpecialDressIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{29}
}
func (m *GetUserCurrChatBgSpecialDressIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCurrChatBgSpecialDressIdReq.Unmarshal(m, b)
}
func (m *GetUserCurrChatBgSpecialDressIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCurrChatBgSpecialDressIdReq.Marshal(b, m, deterministic)
}
func (dst *GetUserCurrChatBgSpecialDressIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCurrChatBgSpecialDressIdReq.Merge(dst, src)
}
func (m *GetUserCurrChatBgSpecialDressIdReq) XXX_Size() int {
	return xxx_messageInfo_GetUserCurrChatBgSpecialDressIdReq.Size(m)
}
func (m *GetUserCurrChatBgSpecialDressIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCurrChatBgSpecialDressIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCurrChatBgSpecialDressIdReq proto.InternalMessageInfo

func (m *GetUserCurrChatBgSpecialDressIdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserCurrChatBgSpecialDressIdReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GetUserCurrChatBgSpecialDressIdReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type GetUserCurrChatBgSpecialDressIdResp struct {
	DressId              uint32   `protobuf:"varint,1,opt,name=dress_id,json=dressId,proto3" json:"dress_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCurrChatBgSpecialDressIdResp) Reset()         { *m = GetUserCurrChatBgSpecialDressIdResp{} }
func (m *GetUserCurrChatBgSpecialDressIdResp) String() string { return proto.CompactTextString(m) }
func (*GetUserCurrChatBgSpecialDressIdResp) ProtoMessage()    {}
func (*GetUserCurrChatBgSpecialDressIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{30}
}
func (m *GetUserCurrChatBgSpecialDressIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCurrChatBgSpecialDressIdResp.Unmarshal(m, b)
}
func (m *GetUserCurrChatBgSpecialDressIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCurrChatBgSpecialDressIdResp.Marshal(b, m, deterministic)
}
func (dst *GetUserCurrChatBgSpecialDressIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCurrChatBgSpecialDressIdResp.Merge(dst, src)
}
func (m *GetUserCurrChatBgSpecialDressIdResp) XXX_Size() int {
	return xxx_messageInfo_GetUserCurrChatBgSpecialDressIdResp.Size(m)
}
func (m *GetUserCurrChatBgSpecialDressIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCurrChatBgSpecialDressIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCurrChatBgSpecialDressIdResp proto.InternalMessageInfo

func (m *GetUserCurrChatBgSpecialDressIdResp) GetDressId() uint32 {
	if m != nil {
		return m.DressId
	}
	return 0
}

// 获取用户会员装扮历史记录
type GetUserDressHistoryReq struct {
	Uid                  uint32    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Type                 DressType `protobuf:"varint,2,opt,name=type,proto3,enum=superplayerdress.DressType" json:"type,omitempty"`
	Offset               uint32    `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32    `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetUserDressHistoryReq) Reset()         { *m = GetUserDressHistoryReq{} }
func (m *GetUserDressHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetUserDressHistoryReq) ProtoMessage()    {}
func (*GetUserDressHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{31}
}
func (m *GetUserDressHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDressHistoryReq.Unmarshal(m, b)
}
func (m *GetUserDressHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDressHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetUserDressHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDressHistoryReq.Merge(dst, src)
}
func (m *GetUserDressHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetUserDressHistoryReq.Size(m)
}
func (m *GetUserDressHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDressHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDressHistoryReq proto.InternalMessageInfo

func (m *GetUserDressHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserDressHistoryReq) GetType() DressType {
	if m != nil {
		return m.Type
	}
	return DressType_DRESS_TYPE_UNSPECIFIC
}

func (m *GetUserDressHistoryReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetUserDressHistoryReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

// 装扮历史记录
type UserDressHistory struct {
	DressId              uint32   `protobuf:"varint,1,opt,name=dress_id,json=dressId,proto3" json:"dress_id,omitempty"`
	CreateTime           uint32   `protobuf:"varint,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Removed              uint32   `protobuf:"varint,3,opt,name=removed,proto3" json:"removed,omitempty"`
	Name                 string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	WebList              string   `protobuf:"bytes,5,opt,name=web_list,json=webList,proto3" json:"web_list,omitempty"`
	Level                uint32   `protobuf:"varint,6,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserDressHistory) Reset()         { *m = UserDressHistory{} }
func (m *UserDressHistory) String() string { return proto.CompactTextString(m) }
func (*UserDressHistory) ProtoMessage()    {}
func (*UserDressHistory) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{32}
}
func (m *UserDressHistory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDressHistory.Unmarshal(m, b)
}
func (m *UserDressHistory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDressHistory.Marshal(b, m, deterministic)
}
func (dst *UserDressHistory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDressHistory.Merge(dst, src)
}
func (m *UserDressHistory) XXX_Size() int {
	return xxx_messageInfo_UserDressHistory.Size(m)
}
func (m *UserDressHistory) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDressHistory.DiscardUnknown(m)
}

var xxx_messageInfo_UserDressHistory proto.InternalMessageInfo

func (m *UserDressHistory) GetDressId() uint32 {
	if m != nil {
		return m.DressId
	}
	return 0
}

func (m *UserDressHistory) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *UserDressHistory) GetRemoved() uint32 {
	if m != nil {
		return m.Removed
	}
	return 0
}

func (m *UserDressHistory) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UserDressHistory) GetWebList() string {
	if m != nil {
		return m.WebList
	}
	return ""
}

func (m *UserDressHistory) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

// 获取用户会员装扮历史记录
type GetUserDressHistoryResp struct {
	Uid                  uint32              `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Type                 DressType           `protobuf:"varint,2,opt,name=type,proto3,enum=superplayerdress.DressType" json:"type,omitempty"`
	Offset               uint32              `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32              `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	HistoryList          []*UserDressHistory `protobuf:"bytes,5,rep,name=history_list,json=historyList,proto3" json:"history_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetUserDressHistoryResp) Reset()         { *m = GetUserDressHistoryResp{} }
func (m *GetUserDressHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetUserDressHistoryResp) ProtoMessage()    {}
func (*GetUserDressHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{33}
}
func (m *GetUserDressHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDressHistoryResp.Unmarshal(m, b)
}
func (m *GetUserDressHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDressHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetUserDressHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDressHistoryResp.Merge(dst, src)
}
func (m *GetUserDressHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetUserDressHistoryResp.Size(m)
}
func (m *GetUserDressHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDressHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDressHistoryResp proto.InternalMessageInfo

func (m *GetUserDressHistoryResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserDressHistoryResp) GetType() DressType {
	if m != nil {
		return m.Type
	}
	return DressType_DRESS_TYPE_UNSPECIFIC
}

func (m *GetUserDressHistoryResp) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetUserDressHistoryResp) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetUserDressHistoryResp) GetHistoryList() []*UserDressHistory {
	if m != nil {
		return m.HistoryList
	}
	return nil
}

// 发放会员装扮体验包
type SendDressExperienceReq struct {
	SuperPlayerUid       uint32   `protobuf:"varint,1,opt,name=super_player_uid,json=superPlayerUid,proto3" json:"super_player_uid,omitempty"`
	IncrConfPackage      uint64   `protobuf:"varint,2,opt,name=incr_conf_package,json=incrConfPackage,proto3" json:"incr_conf_package,omitempty"`
	OrderId              string   `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Reason               string   `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	ServerTime           int64    `protobuf:"varint,5,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendDressExperienceReq) Reset()         { *m = SendDressExperienceReq{} }
func (m *SendDressExperienceReq) String() string { return proto.CompactTextString(m) }
func (*SendDressExperienceReq) ProtoMessage()    {}
func (*SendDressExperienceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{34}
}
func (m *SendDressExperienceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendDressExperienceReq.Unmarshal(m, b)
}
func (m *SendDressExperienceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendDressExperienceReq.Marshal(b, m, deterministic)
}
func (dst *SendDressExperienceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendDressExperienceReq.Merge(dst, src)
}
func (m *SendDressExperienceReq) XXX_Size() int {
	return xxx_messageInfo_SendDressExperienceReq.Size(m)
}
func (m *SendDressExperienceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendDressExperienceReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendDressExperienceReq proto.InternalMessageInfo

func (m *SendDressExperienceReq) GetSuperPlayerUid() uint32 {
	if m != nil {
		return m.SuperPlayerUid
	}
	return 0
}

func (m *SendDressExperienceReq) GetIncrConfPackage() uint64 {
	if m != nil {
		return m.IncrConfPackage
	}
	return 0
}

func (m *SendDressExperienceReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *SendDressExperienceReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *SendDressExperienceReq) GetServerTime() int64 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

type SendDressExperienceResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendDressExperienceResp) Reset()         { *m = SendDressExperienceResp{} }
func (m *SendDressExperienceResp) String() string { return proto.CompactTextString(m) }
func (*SendDressExperienceResp) ProtoMessage()    {}
func (*SendDressExperienceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{35}
}
func (m *SendDressExperienceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendDressExperienceResp.Unmarshal(m, b)
}
func (m *SendDressExperienceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendDressExperienceResp.Marshal(b, m, deterministic)
}
func (dst *SendDressExperienceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendDressExperienceResp.Merge(dst, src)
}
func (m *SendDressExperienceResp) XXX_Size() int {
	return xxx_messageInfo_SendDressExperienceResp.Size(m)
}
func (m *SendDressExperienceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendDressExperienceResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendDressExperienceResp proto.InternalMessageInfo

type ExperienceInfo struct {
	DressInfo            *Dress   `protobuf:"bytes,1,opt,name=dress_info,json=dressInfo,proto3" json:"dress_info,omitempty"`
	ExperienceEndTime    int64    `protobuf:"varint,2,opt,name=experience_end_time,json=experienceEndTime,proto3" json:"experience_end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExperienceInfo) Reset()         { *m = ExperienceInfo{} }
func (m *ExperienceInfo) String() string { return proto.CompactTextString(m) }
func (*ExperienceInfo) ProtoMessage()    {}
func (*ExperienceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{36}
}
func (m *ExperienceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExperienceInfo.Unmarshal(m, b)
}
func (m *ExperienceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExperienceInfo.Marshal(b, m, deterministic)
}
func (dst *ExperienceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExperienceInfo.Merge(dst, src)
}
func (m *ExperienceInfo) XXX_Size() int {
	return xxx_messageInfo_ExperienceInfo.Size(m)
}
func (m *ExperienceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ExperienceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ExperienceInfo proto.InternalMessageInfo

func (m *ExperienceInfo) GetDressInfo() *Dress {
	if m != nil {
		return m.DressInfo
	}
	return nil
}

func (m *ExperienceInfo) GetExperienceEndTime() int64 {
	if m != nil {
		return m.ExperienceEndTime
	}
	return 0
}

// 获取用户当前可体验装扮列表
type GetDressUserExperienceInfoReq struct {
	Uid                  uint32    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DressType            DressType `protobuf:"varint,2,opt,name=dress_type,json=dressType,proto3,enum=superplayerdress.DressType" json:"dress_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetDressUserExperienceInfoReq) Reset()         { *m = GetDressUserExperienceInfoReq{} }
func (m *GetDressUserExperienceInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetDressUserExperienceInfoReq) ProtoMessage()    {}
func (*GetDressUserExperienceInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{37}
}
func (m *GetDressUserExperienceInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDressUserExperienceInfoReq.Unmarshal(m, b)
}
func (m *GetDressUserExperienceInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDressUserExperienceInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetDressUserExperienceInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDressUserExperienceInfoReq.Merge(dst, src)
}
func (m *GetDressUserExperienceInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetDressUserExperienceInfoReq.Size(m)
}
func (m *GetDressUserExperienceInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDressUserExperienceInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDressUserExperienceInfoReq proto.InternalMessageInfo

func (m *GetDressUserExperienceInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetDressUserExperienceInfoReq) GetDressType() DressType {
	if m != nil {
		return m.DressType
	}
	return DressType_DRESS_TYPE_UNSPECIFIC
}

type GetDressUserExperienceInfoResp struct {
	ExperienceList       []*ExperienceInfo `protobuf:"bytes,1,rep,name=experience_list,json=experienceList,proto3" json:"experience_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetDressUserExperienceInfoResp) Reset()         { *m = GetDressUserExperienceInfoResp{} }
func (m *GetDressUserExperienceInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetDressUserExperienceInfoResp) ProtoMessage()    {}
func (*GetDressUserExperienceInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{38}
}
func (m *GetDressUserExperienceInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDressUserExperienceInfoResp.Unmarshal(m, b)
}
func (m *GetDressUserExperienceInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDressUserExperienceInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetDressUserExperienceInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDressUserExperienceInfoResp.Merge(dst, src)
}
func (m *GetDressUserExperienceInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetDressUserExperienceInfoResp.Size(m)
}
func (m *GetDressUserExperienceInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDressUserExperienceInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDressUserExperienceInfoResp proto.InternalMessageInfo

func (m *GetDressUserExperienceInfoResp) GetExperienceList() []*ExperienceInfo {
	if m != nil {
		return m.ExperienceList
	}
	return nil
}

// 体验装扮配置
type DressExperienceConfig struct {
	Type                 ExperienceConfigType `protobuf:"varint,1,opt,name=type,proto3,enum=superplayerdress.ExperienceConfigType" json:"type,omitempty"`
	Level                uint32               `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	LevelType            LevelType            `protobuf:"varint,3,opt,name=level_type,json=levelType,proto3,enum=superplayerdress.LevelType" json:"level_type,omitempty"`
	StartTime            int64                `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64                `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ItemId               uint32               `protobuf:"varint,6,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemType             DressType            `protobuf:"varint,7,opt,name=item_type,json=itemType,proto3,enum=superplayerdress.DressType" json:"item_type,omitempty"`
	Duration             int64                `protobuf:"varint,8,opt,name=duration,proto3" json:"duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *DressExperienceConfig) Reset()         { *m = DressExperienceConfig{} }
func (m *DressExperienceConfig) String() string { return proto.CompactTextString(m) }
func (*DressExperienceConfig) ProtoMessage()    {}
func (*DressExperienceConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{39}
}
func (m *DressExperienceConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DressExperienceConfig.Unmarshal(m, b)
}
func (m *DressExperienceConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DressExperienceConfig.Marshal(b, m, deterministic)
}
func (dst *DressExperienceConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DressExperienceConfig.Merge(dst, src)
}
func (m *DressExperienceConfig) XXX_Size() int {
	return xxx_messageInfo_DressExperienceConfig.Size(m)
}
func (m *DressExperienceConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_DressExperienceConfig.DiscardUnknown(m)
}

var xxx_messageInfo_DressExperienceConfig proto.InternalMessageInfo

func (m *DressExperienceConfig) GetType() ExperienceConfigType {
	if m != nil {
		return m.Type
	}
	return ExperienceConfigType_EXPERIENCE_CONFIG_TYPE_UNSPECIFIC
}

func (m *DressExperienceConfig) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *DressExperienceConfig) GetLevelType() LevelType {
	if m != nil {
		return m.LevelType
	}
	return LevelType_LEVEL_TYPE_UNSPECIFIC
}

func (m *DressExperienceConfig) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *DressExperienceConfig) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *DressExperienceConfig) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *DressExperienceConfig) GetItemType() DressType {
	if m != nil {
		return m.ItemType
	}
	return DressType_DRESS_TYPE_UNSPECIFIC
}

func (m *DressExperienceConfig) GetDuration() int64 {
	if m != nil {
		return m.Duration
	}
	return 0
}

type DressExperiencePackage struct {
	Id                   uint64                   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ConfigList           []*DressExperienceConfig `protobuf:"bytes,2,rep,name=config_list,json=configList,proto3" json:"config_list,omitempty"`
	OnlyCanGetOnce       bool                     `protobuf:"varint,3,opt,name=only_can_get_once,json=onlyCanGetOnce,proto3" json:"only_can_get_once,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *DressExperiencePackage) Reset()         { *m = DressExperiencePackage{} }
func (m *DressExperiencePackage) String() string { return proto.CompactTextString(m) }
func (*DressExperiencePackage) ProtoMessage()    {}
func (*DressExperiencePackage) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{40}
}
func (m *DressExperiencePackage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DressExperiencePackage.Unmarshal(m, b)
}
func (m *DressExperiencePackage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DressExperiencePackage.Marshal(b, m, deterministic)
}
func (dst *DressExperiencePackage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DressExperiencePackage.Merge(dst, src)
}
func (m *DressExperiencePackage) XXX_Size() int {
	return xxx_messageInfo_DressExperiencePackage.Size(m)
}
func (m *DressExperiencePackage) XXX_DiscardUnknown() {
	xxx_messageInfo_DressExperiencePackage.DiscardUnknown(m)
}

var xxx_messageInfo_DressExperiencePackage proto.InternalMessageInfo

func (m *DressExperiencePackage) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DressExperiencePackage) GetConfigList() []*DressExperienceConfig {
	if m != nil {
		return m.ConfigList
	}
	return nil
}

func (m *DressExperiencePackage) GetOnlyCanGetOnce() bool {
	if m != nil {
		return m.OnlyCanGetOnce
	}
	return false
}

// 获取体验装扮包裹
type GetDressExperiencePackageReq struct {
	Id                   uint64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDressExperiencePackageReq) Reset()         { *m = GetDressExperiencePackageReq{} }
func (m *GetDressExperiencePackageReq) String() string { return proto.CompactTextString(m) }
func (*GetDressExperiencePackageReq) ProtoMessage()    {}
func (*GetDressExperiencePackageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{41}
}
func (m *GetDressExperiencePackageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDressExperiencePackageReq.Unmarshal(m, b)
}
func (m *GetDressExperiencePackageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDressExperiencePackageReq.Marshal(b, m, deterministic)
}
func (dst *GetDressExperiencePackageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDressExperiencePackageReq.Merge(dst, src)
}
func (m *GetDressExperiencePackageReq) XXX_Size() int {
	return xxx_messageInfo_GetDressExperiencePackageReq.Size(m)
}
func (m *GetDressExperiencePackageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDressExperiencePackageReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDressExperiencePackageReq proto.InternalMessageInfo

func (m *GetDressExperiencePackageReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetDressExperiencePackageResp struct {
	Package              *DressExperiencePackage `protobuf:"bytes,1,opt,name=package,proto3" json:"package,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetDressExperiencePackageResp) Reset()         { *m = GetDressExperiencePackageResp{} }
func (m *GetDressExperiencePackageResp) String() string { return proto.CompactTextString(m) }
func (*GetDressExperiencePackageResp) ProtoMessage()    {}
func (*GetDressExperiencePackageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{42}
}
func (m *GetDressExperiencePackageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDressExperiencePackageResp.Unmarshal(m, b)
}
func (m *GetDressExperiencePackageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDressExperiencePackageResp.Marshal(b, m, deterministic)
}
func (dst *GetDressExperiencePackageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDressExperiencePackageResp.Merge(dst, src)
}
func (m *GetDressExperiencePackageResp) XXX_Size() int {
	return xxx_messageInfo_GetDressExperiencePackageResp.Size(m)
}
func (m *GetDressExperiencePackageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDressExperiencePackageResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDressExperiencePackageResp proto.InternalMessageInfo

func (m *GetDressExperiencePackageResp) GetPackage() *DressExperiencePackage {
	if m != nil {
		return m.Package
	}
	return nil
}

// 设置体验装扮包裹
type SetDressExperiencePackageReq struct {
	Package              *DressExperiencePackage `protobuf:"bytes,1,opt,name=package,proto3" json:"package,omitempty"`
	Operator             string                  `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SetDressExperiencePackageReq) Reset()         { *m = SetDressExperiencePackageReq{} }
func (m *SetDressExperiencePackageReq) String() string { return proto.CompactTextString(m) }
func (*SetDressExperiencePackageReq) ProtoMessage()    {}
func (*SetDressExperiencePackageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{43}
}
func (m *SetDressExperiencePackageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDressExperiencePackageReq.Unmarshal(m, b)
}
func (m *SetDressExperiencePackageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDressExperiencePackageReq.Marshal(b, m, deterministic)
}
func (dst *SetDressExperiencePackageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDressExperiencePackageReq.Merge(dst, src)
}
func (m *SetDressExperiencePackageReq) XXX_Size() int {
	return xxx_messageInfo_SetDressExperiencePackageReq.Size(m)
}
func (m *SetDressExperiencePackageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDressExperiencePackageReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetDressExperiencePackageReq proto.InternalMessageInfo

func (m *SetDressExperiencePackageReq) GetPackage() *DressExperiencePackage {
	if m != nil {
		return m.Package
	}
	return nil
}

func (m *SetDressExperiencePackageReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type SetDressExperiencePackageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetDressExperiencePackageResp) Reset()         { *m = SetDressExperiencePackageResp{} }
func (m *SetDressExperiencePackageResp) String() string { return proto.CompactTextString(m) }
func (*SetDressExperiencePackageResp) ProtoMessage()    {}
func (*SetDressExperiencePackageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{44}
}
func (m *SetDressExperiencePackageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDressExperiencePackageResp.Unmarshal(m, b)
}
func (m *SetDressExperiencePackageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDressExperiencePackageResp.Marshal(b, m, deterministic)
}
func (dst *SetDressExperiencePackageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDressExperiencePackageResp.Merge(dst, src)
}
func (m *SetDressExperiencePackageResp) XXX_Size() int {
	return xxx_messageInfo_SetDressExperiencePackageResp.Size(m)
}
func (m *SetDressExperiencePackageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDressExperiencePackageResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetDressExperiencePackageResp proto.InternalMessageInfo

// 会员穿戴装扮 新增
type SuperPlayerUseDressEvent struct {
	SuperPlayerUid       uint32    `protobuf:"varint,1,opt,name=super_player_uid,json=superPlayerUid,proto3" json:"super_player_uid,omitempty"`
	CreateTime           uint32    `protobuf:"varint,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Type                 DressType `protobuf:"varint,3,opt,name=type,proto3,enum=superplayerdress.DressType" json:"type,omitempty"`
	DressId              uint32    `protobuf:"varint,4,opt,name=dress_id,json=dressId,proto3" json:"dress_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *SuperPlayerUseDressEvent) Reset()         { *m = SuperPlayerUseDressEvent{} }
func (m *SuperPlayerUseDressEvent) String() string { return proto.CompactTextString(m) }
func (*SuperPlayerUseDressEvent) ProtoMessage()    {}
func (*SuperPlayerUseDressEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_dress_8f94ad45ff3f8afc, []int{45}
}
func (m *SuperPlayerUseDressEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuperPlayerUseDressEvent.Unmarshal(m, b)
}
func (m *SuperPlayerUseDressEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuperPlayerUseDressEvent.Marshal(b, m, deterministic)
}
func (dst *SuperPlayerUseDressEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuperPlayerUseDressEvent.Merge(dst, src)
}
func (m *SuperPlayerUseDressEvent) XXX_Size() int {
	return xxx_messageInfo_SuperPlayerUseDressEvent.Size(m)
}
func (m *SuperPlayerUseDressEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_SuperPlayerUseDressEvent.DiscardUnknown(m)
}

var xxx_messageInfo_SuperPlayerUseDressEvent proto.InternalMessageInfo

func (m *SuperPlayerUseDressEvent) GetSuperPlayerUid() uint32 {
	if m != nil {
		return m.SuperPlayerUid
	}
	return 0
}

func (m *SuperPlayerUseDressEvent) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *SuperPlayerUseDressEvent) GetType() DressType {
	if m != nil {
		return m.Type
	}
	return DressType_DRESS_TYPE_UNSPECIFIC
}

func (m *SuperPlayerUseDressEvent) GetDressId() uint32 {
	if m != nil {
		return m.DressId
	}
	return 0
}

func init() {
	proto.RegisterType((*Dress)(nil), "superplayerdress.Dress")
	proto.RegisterType((*DressInfo)(nil), "superplayerdress.DressInfo")
	proto.RegisterType((*AddDressInfoReq)(nil), "superplayerdress.AddDressInfoReq")
	proto.RegisterType((*AddDressInfoResp)(nil), "superplayerdress.AddDressInfoResp")
	proto.RegisterType((*UpdateDressInfoReq)(nil), "superplayerdress.UpdateDressInfoReq")
	proto.RegisterType((*UpdateDressInfoResp)(nil), "superplayerdress.UpdateDressInfoResp")
	proto.RegisterType((*UpdateDressInfoListReq)(nil), "superplayerdress.UpdateDressInfoListReq")
	proto.RegisterType((*UpdateDressInfoListResp)(nil), "superplayerdress.UpdateDressInfoListResp")
	proto.RegisterType((*DelDressInfoReq)(nil), "superplayerdress.DelDressInfoReq")
	proto.RegisterType((*DelDressInfoResp)(nil), "superplayerdress.DelDressInfoResp")
	proto.RegisterType((*GetDressInfoListReq)(nil), "superplayerdress.GetDressInfoListReq")
	proto.RegisterType((*GetDressInfoListResp)(nil), "superplayerdress.GetDressInfoListResp")
	proto.RegisterType((*GetDressConfigMaxVersionReq)(nil), "superplayerdress.GetDressConfigMaxVersionReq")
	proto.RegisterType((*GetDressConfigMaxVersionResp)(nil), "superplayerdress.GetDressConfigMaxVersionResp")
	proto.RegisterType((*GetDressConfigListReq)(nil), "superplayerdress.GetDressConfigListReq")
	proto.RegisterType((*GetDressConfigListResp)(nil), "superplayerdress.GetDressConfigListResp")
	proto.RegisterType((*GetDressInUseReq)(nil), "superplayerdress.GetDressInUseReq")
	proto.RegisterType((*GetDressInUseResp)(nil), "superplayerdress.GetDressInUseResp")
	proto.RegisterType((*SetDressInUseReq)(nil), "superplayerdress.SetDressInUseReq")
	proto.RegisterType((*SetDressInUseResp)(nil), "superplayerdress.SetDressInUseResp")
	proto.RegisterType((*GetWebDressConfigReq)(nil), "superplayerdress.GetWebDressConfigReq")
	proto.RegisterType((*GetWebDressConfigResp)(nil), "superplayerdress.GetWebDressConfigResp")
	proto.RegisterType((*GetUserCurrChatBgDressIdListReq)(nil), "superplayerdress.GetUserCurrChatBgDressIdListReq")
	proto.RegisterType((*ChatBgSpecial)(nil), "superplayerdress.ChatBgSpecial")
	proto.RegisterType((*GetUserCurrChatBgDressIdListResp)(nil), "superplayerdress.GetUserCurrChatBgDressIdListResp")
	proto.RegisterType((*SetChatBgSpecialDressInUseReq)(nil), "superplayerdress.SetChatBgSpecialDressInUseReq")
	proto.RegisterType((*SetChatBgSpecialDressInUseResp)(nil), "superplayerdress.SetChatBgSpecialDressInUseResp")
	proto.RegisterType((*SetDefaultDressInUseReq)(nil), "superplayerdress.SetDefaultDressInUseReq")
	proto.RegisterType((*SetDefaultDressInUseResp)(nil), "superplayerdress.SetDefaultDressInUseResp")
	proto.RegisterType((*GetUserCurrChatBgSpecialDressIdReq)(nil), "superplayerdress.GetUserCurrChatBgSpecialDressIdReq")
	proto.RegisterType((*GetUserCurrChatBgSpecialDressIdResp)(nil), "superplayerdress.GetUserCurrChatBgSpecialDressIdResp")
	proto.RegisterType((*GetUserDressHistoryReq)(nil), "superplayerdress.GetUserDressHistoryReq")
	proto.RegisterType((*UserDressHistory)(nil), "superplayerdress.UserDressHistory")
	proto.RegisterType((*GetUserDressHistoryResp)(nil), "superplayerdress.GetUserDressHistoryResp")
	proto.RegisterType((*SendDressExperienceReq)(nil), "superplayerdress.SendDressExperienceReq")
	proto.RegisterType((*SendDressExperienceResp)(nil), "superplayerdress.SendDressExperienceResp")
	proto.RegisterType((*ExperienceInfo)(nil), "superplayerdress.ExperienceInfo")
	proto.RegisterType((*GetDressUserExperienceInfoReq)(nil), "superplayerdress.GetDressUserExperienceInfoReq")
	proto.RegisterType((*GetDressUserExperienceInfoResp)(nil), "superplayerdress.GetDressUserExperienceInfoResp")
	proto.RegisterType((*DressExperienceConfig)(nil), "superplayerdress.DressExperienceConfig")
	proto.RegisterType((*DressExperiencePackage)(nil), "superplayerdress.DressExperiencePackage")
	proto.RegisterType((*GetDressExperiencePackageReq)(nil), "superplayerdress.GetDressExperiencePackageReq")
	proto.RegisterType((*GetDressExperiencePackageResp)(nil), "superplayerdress.GetDressExperiencePackageResp")
	proto.RegisterType((*SetDressExperiencePackageReq)(nil), "superplayerdress.SetDressExperiencePackageReq")
	proto.RegisterType((*SetDressExperiencePackageResp)(nil), "superplayerdress.SetDressExperiencePackageResp")
	proto.RegisterType((*SuperPlayerUseDressEvent)(nil), "superplayerdress.SuperPlayerUseDressEvent")
	proto.RegisterEnum("superplayerdress.DressType", DressType_name, DressType_value)
	proto.RegisterEnum("superplayerdress.DressStatusType", DressStatusType_name, DressStatusType_value)
	proto.RegisterEnum("superplayerdress.ExperienceConfigType", ExperienceConfigType_name, ExperienceConfigType_value)
	proto.RegisterEnum("superplayerdress.LevelType", LevelType_name, LevelType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SuperPlayerDressClient is the client API for SuperPlayerDress service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SuperPlayerDressClient interface {
	GetDressConfigMaxVersion(ctx context.Context, in *GetDressConfigMaxVersionReq, opts ...grpc.CallOption) (*GetDressConfigMaxVersionResp, error)
	GetDressConfigList(ctx context.Context, in *GetDressConfigListReq, opts ...grpc.CallOption) (*GetDressConfigListResp, error)
	GetDressInUse(ctx context.Context, in *GetDressInUseReq, opts ...grpc.CallOption) (*GetDressInUseResp, error)
	SetDressInUse(ctx context.Context, in *SetDressInUseReq, opts ...grpc.CallOption) (*SetDressInUseResp, error)
	GetWebDressConfig(ctx context.Context, in *GetWebDressConfigReq, opts ...grpc.CallOption) (*GetWebDressConfigResp, error)
	GetUserCurrChatBgDressIdList(ctx context.Context, in *GetUserCurrChatBgDressIdListReq, opts ...grpc.CallOption) (*GetUserCurrChatBgDressIdListResp, error)
	SetChatBgSpecialDressInUse(ctx context.Context, in *SetChatBgSpecialDressInUseReq, opts ...grpc.CallOption) (*SetChatBgSpecialDressInUseResp, error)
	SetDefaultDressInUse(ctx context.Context, in *SetDefaultDressInUseReq, opts ...grpc.CallOption) (*SetDefaultDressInUseResp, error)
	GetUserCurrChatBgSpecialDressId(ctx context.Context, in *GetUserCurrChatBgSpecialDressIdReq, opts ...grpc.CallOption) (*GetUserCurrChatBgSpecialDressIdResp, error)
	GetUserDressHistory(ctx context.Context, in *GetUserDressHistoryReq, opts ...grpc.CallOption) (*GetUserDressHistoryResp, error)
	// 运营后台装扮配置相关接口
	AddDressInfo(ctx context.Context, in *AddDressInfoReq, opts ...grpc.CallOption) (*AddDressInfoResp, error)
	UpdateDressInfo(ctx context.Context, in *UpdateDressInfoReq, opts ...grpc.CallOption) (*UpdateDressInfoResp, error)
	UpdateDressInfoList(ctx context.Context, in *UpdateDressInfoListReq, opts ...grpc.CallOption) (*UpdateDressInfoListResp, error)
	DelDressInfo(ctx context.Context, in *DelDressInfoReq, opts ...grpc.CallOption) (*DelDressInfoResp, error)
	GetDressInfoList(ctx context.Context, in *GetDressInfoListReq, opts ...grpc.CallOption) (*GetDressInfoListResp, error)
	// 体验装扮相关接口
	// 发放体验装扮
	SendDressExperience(ctx context.Context, in *SendDressExperienceReq, opts ...grpc.CallOption) (*SendDressExperienceResp, error)
	// 获取用户可体验装扮列表
	GetDressUserExperienceInfo(ctx context.Context, in *GetDressUserExperienceInfoReq, opts ...grpc.CallOption) (*GetDressUserExperienceInfoResp, error)
	// 体验装扮配置接口
	// 获取体验装扮包裹配置
	GetDressExperiencePackage(ctx context.Context, in *GetDressExperiencePackageReq, opts ...grpc.CallOption) (*GetDressExperiencePackageResp, error)
	// 设置体验装扮包裹配置
	SetDressExperiencePackage(ctx context.Context, in *SetDressExperiencePackageReq, opts ...grpc.CallOption) (*SetDressExperiencePackageResp, error)
}

type superPlayerDressClient struct {
	cc *grpc.ClientConn
}

func NewSuperPlayerDressClient(cc *grpc.ClientConn) SuperPlayerDressClient {
	return &superPlayerDressClient{cc}
}

func (c *superPlayerDressClient) GetDressConfigMaxVersion(ctx context.Context, in *GetDressConfigMaxVersionReq, opts ...grpc.CallOption) (*GetDressConfigMaxVersionResp, error) {
	out := new(GetDressConfigMaxVersionResp)
	err := c.cc.Invoke(ctx, "/superplayerdress.SuperPlayerDress/GetDressConfigMaxVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressClient) GetDressConfigList(ctx context.Context, in *GetDressConfigListReq, opts ...grpc.CallOption) (*GetDressConfigListResp, error) {
	out := new(GetDressConfigListResp)
	err := c.cc.Invoke(ctx, "/superplayerdress.SuperPlayerDress/GetDressConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressClient) GetDressInUse(ctx context.Context, in *GetDressInUseReq, opts ...grpc.CallOption) (*GetDressInUseResp, error) {
	out := new(GetDressInUseResp)
	err := c.cc.Invoke(ctx, "/superplayerdress.SuperPlayerDress/GetDressInUse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressClient) SetDressInUse(ctx context.Context, in *SetDressInUseReq, opts ...grpc.CallOption) (*SetDressInUseResp, error) {
	out := new(SetDressInUseResp)
	err := c.cc.Invoke(ctx, "/superplayerdress.SuperPlayerDress/SetDressInUse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressClient) GetWebDressConfig(ctx context.Context, in *GetWebDressConfigReq, opts ...grpc.CallOption) (*GetWebDressConfigResp, error) {
	out := new(GetWebDressConfigResp)
	err := c.cc.Invoke(ctx, "/superplayerdress.SuperPlayerDress/GetWebDressConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressClient) GetUserCurrChatBgDressIdList(ctx context.Context, in *GetUserCurrChatBgDressIdListReq, opts ...grpc.CallOption) (*GetUserCurrChatBgDressIdListResp, error) {
	out := new(GetUserCurrChatBgDressIdListResp)
	err := c.cc.Invoke(ctx, "/superplayerdress.SuperPlayerDress/GetUserCurrChatBgDressIdList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressClient) SetChatBgSpecialDressInUse(ctx context.Context, in *SetChatBgSpecialDressInUseReq, opts ...grpc.CallOption) (*SetChatBgSpecialDressInUseResp, error) {
	out := new(SetChatBgSpecialDressInUseResp)
	err := c.cc.Invoke(ctx, "/superplayerdress.SuperPlayerDress/SetChatBgSpecialDressInUse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressClient) SetDefaultDressInUse(ctx context.Context, in *SetDefaultDressInUseReq, opts ...grpc.CallOption) (*SetDefaultDressInUseResp, error) {
	out := new(SetDefaultDressInUseResp)
	err := c.cc.Invoke(ctx, "/superplayerdress.SuperPlayerDress/SetDefaultDressInUse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressClient) GetUserCurrChatBgSpecialDressId(ctx context.Context, in *GetUserCurrChatBgSpecialDressIdReq, opts ...grpc.CallOption) (*GetUserCurrChatBgSpecialDressIdResp, error) {
	out := new(GetUserCurrChatBgSpecialDressIdResp)
	err := c.cc.Invoke(ctx, "/superplayerdress.SuperPlayerDress/GetUserCurrChatBgSpecialDressId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressClient) GetUserDressHistory(ctx context.Context, in *GetUserDressHistoryReq, opts ...grpc.CallOption) (*GetUserDressHistoryResp, error) {
	out := new(GetUserDressHistoryResp)
	err := c.cc.Invoke(ctx, "/superplayerdress.SuperPlayerDress/GetUserDressHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressClient) AddDressInfo(ctx context.Context, in *AddDressInfoReq, opts ...grpc.CallOption) (*AddDressInfoResp, error) {
	out := new(AddDressInfoResp)
	err := c.cc.Invoke(ctx, "/superplayerdress.SuperPlayerDress/AddDressInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressClient) UpdateDressInfo(ctx context.Context, in *UpdateDressInfoReq, opts ...grpc.CallOption) (*UpdateDressInfoResp, error) {
	out := new(UpdateDressInfoResp)
	err := c.cc.Invoke(ctx, "/superplayerdress.SuperPlayerDress/UpdateDressInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressClient) UpdateDressInfoList(ctx context.Context, in *UpdateDressInfoListReq, opts ...grpc.CallOption) (*UpdateDressInfoListResp, error) {
	out := new(UpdateDressInfoListResp)
	err := c.cc.Invoke(ctx, "/superplayerdress.SuperPlayerDress/UpdateDressInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressClient) DelDressInfo(ctx context.Context, in *DelDressInfoReq, opts ...grpc.CallOption) (*DelDressInfoResp, error) {
	out := new(DelDressInfoResp)
	err := c.cc.Invoke(ctx, "/superplayerdress.SuperPlayerDress/DelDressInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressClient) GetDressInfoList(ctx context.Context, in *GetDressInfoListReq, opts ...grpc.CallOption) (*GetDressInfoListResp, error) {
	out := new(GetDressInfoListResp)
	err := c.cc.Invoke(ctx, "/superplayerdress.SuperPlayerDress/GetDressInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressClient) SendDressExperience(ctx context.Context, in *SendDressExperienceReq, opts ...grpc.CallOption) (*SendDressExperienceResp, error) {
	out := new(SendDressExperienceResp)
	err := c.cc.Invoke(ctx, "/superplayerdress.SuperPlayerDress/SendDressExperience", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressClient) GetDressUserExperienceInfo(ctx context.Context, in *GetDressUserExperienceInfoReq, opts ...grpc.CallOption) (*GetDressUserExperienceInfoResp, error) {
	out := new(GetDressUserExperienceInfoResp)
	err := c.cc.Invoke(ctx, "/superplayerdress.SuperPlayerDress/GetDressUserExperienceInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressClient) GetDressExperiencePackage(ctx context.Context, in *GetDressExperiencePackageReq, opts ...grpc.CallOption) (*GetDressExperiencePackageResp, error) {
	out := new(GetDressExperiencePackageResp)
	err := c.cc.Invoke(ctx, "/superplayerdress.SuperPlayerDress/GetDressExperiencePackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressClient) SetDressExperiencePackage(ctx context.Context, in *SetDressExperiencePackageReq, opts ...grpc.CallOption) (*SetDressExperiencePackageResp, error) {
	out := new(SetDressExperiencePackageResp)
	err := c.cc.Invoke(ctx, "/superplayerdress.SuperPlayerDress/SetDressExperiencePackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SuperPlayerDressServer is the server API for SuperPlayerDress service.
type SuperPlayerDressServer interface {
	GetDressConfigMaxVersion(context.Context, *GetDressConfigMaxVersionReq) (*GetDressConfigMaxVersionResp, error)
	GetDressConfigList(context.Context, *GetDressConfigListReq) (*GetDressConfigListResp, error)
	GetDressInUse(context.Context, *GetDressInUseReq) (*GetDressInUseResp, error)
	SetDressInUse(context.Context, *SetDressInUseReq) (*SetDressInUseResp, error)
	GetWebDressConfig(context.Context, *GetWebDressConfigReq) (*GetWebDressConfigResp, error)
	GetUserCurrChatBgDressIdList(context.Context, *GetUserCurrChatBgDressIdListReq) (*GetUserCurrChatBgDressIdListResp, error)
	SetChatBgSpecialDressInUse(context.Context, *SetChatBgSpecialDressInUseReq) (*SetChatBgSpecialDressInUseResp, error)
	SetDefaultDressInUse(context.Context, *SetDefaultDressInUseReq) (*SetDefaultDressInUseResp, error)
	GetUserCurrChatBgSpecialDressId(context.Context, *GetUserCurrChatBgSpecialDressIdReq) (*GetUserCurrChatBgSpecialDressIdResp, error)
	GetUserDressHistory(context.Context, *GetUserDressHistoryReq) (*GetUserDressHistoryResp, error)
	// 运营后台装扮配置相关接口
	AddDressInfo(context.Context, *AddDressInfoReq) (*AddDressInfoResp, error)
	UpdateDressInfo(context.Context, *UpdateDressInfoReq) (*UpdateDressInfoResp, error)
	UpdateDressInfoList(context.Context, *UpdateDressInfoListReq) (*UpdateDressInfoListResp, error)
	DelDressInfo(context.Context, *DelDressInfoReq) (*DelDressInfoResp, error)
	GetDressInfoList(context.Context, *GetDressInfoListReq) (*GetDressInfoListResp, error)
	// 体验装扮相关接口
	// 发放体验装扮
	SendDressExperience(context.Context, *SendDressExperienceReq) (*SendDressExperienceResp, error)
	// 获取用户可体验装扮列表
	GetDressUserExperienceInfo(context.Context, *GetDressUserExperienceInfoReq) (*GetDressUserExperienceInfoResp, error)
	// 体验装扮配置接口
	// 获取体验装扮包裹配置
	GetDressExperiencePackage(context.Context, *GetDressExperiencePackageReq) (*GetDressExperiencePackageResp, error)
	// 设置体验装扮包裹配置
	SetDressExperiencePackage(context.Context, *SetDressExperiencePackageReq) (*SetDressExperiencePackageResp, error)
}

func RegisterSuperPlayerDressServer(s *grpc.Server, srv SuperPlayerDressServer) {
	s.RegisterService(&_SuperPlayerDress_serviceDesc, srv)
}

func _SuperPlayerDress_GetDressConfigMaxVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDressConfigMaxVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressServer).GetDressConfigMaxVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/superplayerdress.SuperPlayerDress/GetDressConfigMaxVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressServer).GetDressConfigMaxVersion(ctx, req.(*GetDressConfigMaxVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDress_GetDressConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDressConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressServer).GetDressConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/superplayerdress.SuperPlayerDress/GetDressConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressServer).GetDressConfigList(ctx, req.(*GetDressConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDress_GetDressInUse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDressInUseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressServer).GetDressInUse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/superplayerdress.SuperPlayerDress/GetDressInUse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressServer).GetDressInUse(ctx, req.(*GetDressInUseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDress_SetDressInUse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetDressInUseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressServer).SetDressInUse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/superplayerdress.SuperPlayerDress/SetDressInUse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressServer).SetDressInUse(ctx, req.(*SetDressInUseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDress_GetWebDressConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWebDressConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressServer).GetWebDressConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/superplayerdress.SuperPlayerDress/GetWebDressConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressServer).GetWebDressConfig(ctx, req.(*GetWebDressConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDress_GetUserCurrChatBgDressIdList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCurrChatBgDressIdListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressServer).GetUserCurrChatBgDressIdList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/superplayerdress.SuperPlayerDress/GetUserCurrChatBgDressIdList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressServer).GetUserCurrChatBgDressIdList(ctx, req.(*GetUserCurrChatBgDressIdListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDress_SetChatBgSpecialDressInUse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChatBgSpecialDressInUseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressServer).SetChatBgSpecialDressInUse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/superplayerdress.SuperPlayerDress/SetChatBgSpecialDressInUse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressServer).SetChatBgSpecialDressInUse(ctx, req.(*SetChatBgSpecialDressInUseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDress_SetDefaultDressInUse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetDefaultDressInUseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressServer).SetDefaultDressInUse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/superplayerdress.SuperPlayerDress/SetDefaultDressInUse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressServer).SetDefaultDressInUse(ctx, req.(*SetDefaultDressInUseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDress_GetUserCurrChatBgSpecialDressId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCurrChatBgSpecialDressIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressServer).GetUserCurrChatBgSpecialDressId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/superplayerdress.SuperPlayerDress/GetUserCurrChatBgSpecialDressId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressServer).GetUserCurrChatBgSpecialDressId(ctx, req.(*GetUserCurrChatBgSpecialDressIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDress_GetUserDressHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDressHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressServer).GetUserDressHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/superplayerdress.SuperPlayerDress/GetUserDressHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressServer).GetUserDressHistory(ctx, req.(*GetUserDressHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDress_AddDressInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddDressInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressServer).AddDressInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/superplayerdress.SuperPlayerDress/AddDressInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressServer).AddDressInfo(ctx, req.(*AddDressInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDress_UpdateDressInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDressInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressServer).UpdateDressInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/superplayerdress.SuperPlayerDress/UpdateDressInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressServer).UpdateDressInfo(ctx, req.(*UpdateDressInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDress_UpdateDressInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDressInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressServer).UpdateDressInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/superplayerdress.SuperPlayerDress/UpdateDressInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressServer).UpdateDressInfoList(ctx, req.(*UpdateDressInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDress_DelDressInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelDressInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressServer).DelDressInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/superplayerdress.SuperPlayerDress/DelDressInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressServer).DelDressInfo(ctx, req.(*DelDressInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDress_GetDressInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDressInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressServer).GetDressInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/superplayerdress.SuperPlayerDress/GetDressInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressServer).GetDressInfoList(ctx, req.(*GetDressInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDress_SendDressExperience_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendDressExperienceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressServer).SendDressExperience(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/superplayerdress.SuperPlayerDress/SendDressExperience",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressServer).SendDressExperience(ctx, req.(*SendDressExperienceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDress_GetDressUserExperienceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDressUserExperienceInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressServer).GetDressUserExperienceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/superplayerdress.SuperPlayerDress/GetDressUserExperienceInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressServer).GetDressUserExperienceInfo(ctx, req.(*GetDressUserExperienceInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDress_GetDressExperiencePackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDressExperiencePackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressServer).GetDressExperiencePackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/superplayerdress.SuperPlayerDress/GetDressExperiencePackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressServer).GetDressExperiencePackage(ctx, req.(*GetDressExperiencePackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDress_SetDressExperiencePackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetDressExperiencePackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressServer).SetDressExperiencePackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/superplayerdress.SuperPlayerDress/SetDressExperiencePackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressServer).SetDressExperiencePackage(ctx, req.(*SetDressExperiencePackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SuperPlayerDress_serviceDesc = grpc.ServiceDesc{
	ServiceName: "superplayerdress.SuperPlayerDress",
	HandlerType: (*SuperPlayerDressServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetDressConfigMaxVersion",
			Handler:    _SuperPlayerDress_GetDressConfigMaxVersion_Handler,
		},
		{
			MethodName: "GetDressConfigList",
			Handler:    _SuperPlayerDress_GetDressConfigList_Handler,
		},
		{
			MethodName: "GetDressInUse",
			Handler:    _SuperPlayerDress_GetDressInUse_Handler,
		},
		{
			MethodName: "SetDressInUse",
			Handler:    _SuperPlayerDress_SetDressInUse_Handler,
		},
		{
			MethodName: "GetWebDressConfig",
			Handler:    _SuperPlayerDress_GetWebDressConfig_Handler,
		},
		{
			MethodName: "GetUserCurrChatBgDressIdList",
			Handler:    _SuperPlayerDress_GetUserCurrChatBgDressIdList_Handler,
		},
		{
			MethodName: "SetChatBgSpecialDressInUse",
			Handler:    _SuperPlayerDress_SetChatBgSpecialDressInUse_Handler,
		},
		{
			MethodName: "SetDefaultDressInUse",
			Handler:    _SuperPlayerDress_SetDefaultDressInUse_Handler,
		},
		{
			MethodName: "GetUserCurrChatBgSpecialDressId",
			Handler:    _SuperPlayerDress_GetUserCurrChatBgSpecialDressId_Handler,
		},
		{
			MethodName: "GetUserDressHistory",
			Handler:    _SuperPlayerDress_GetUserDressHistory_Handler,
		},
		{
			MethodName: "AddDressInfo",
			Handler:    _SuperPlayerDress_AddDressInfo_Handler,
		},
		{
			MethodName: "UpdateDressInfo",
			Handler:    _SuperPlayerDress_UpdateDressInfo_Handler,
		},
		{
			MethodName: "UpdateDressInfoList",
			Handler:    _SuperPlayerDress_UpdateDressInfoList_Handler,
		},
		{
			MethodName: "DelDressInfo",
			Handler:    _SuperPlayerDress_DelDressInfo_Handler,
		},
		{
			MethodName: "GetDressInfoList",
			Handler:    _SuperPlayerDress_GetDressInfoList_Handler,
		},
		{
			MethodName: "SendDressExperience",
			Handler:    _SuperPlayerDress_SendDressExperience_Handler,
		},
		{
			MethodName: "GetDressUserExperienceInfo",
			Handler:    _SuperPlayerDress_GetDressUserExperienceInfo_Handler,
		},
		{
			MethodName: "GetDressExperiencePackage",
			Handler:    _SuperPlayerDress_GetDressExperiencePackage_Handler,
		},
		{
			MethodName: "SetDressExperiencePackage",
			Handler:    _SuperPlayerDress_SetDressExperiencePackage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "super-player-dress/super-player-dress.proto",
}

func init() {
	proto.RegisterFile("super-player-dress/super-player-dress.proto", fileDescriptor_super_player_dress_8f94ad45ff3f8afc)
}

var fileDescriptor_super_player_dress_8f94ad45ff3f8afc = []byte{
	// 2222 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x5a, 0x4b, 0x73, 0xdb, 0xd6,
	0x15, 0x36, 0x48, 0x4a, 0x22, 0x8f, 0x6c, 0x09, 0xbe, 0x96, 0x25, 0x88, 0x7e, 0xc9, 0x48, 0x6d,
	0xcb, 0x4a, 0x2d, 0xb5, 0x8a, 0x9d, 0xe9, 0x68, 0xd1, 0xa9, 0x44, 0xc1, 0x12, 0x27, 0x7a, 0x95,
	0x20, 0x9d, 0x36, 0x1b, 0x14, 0x02, 0xae, 0x14, 0x4c, 0x48, 0x00, 0x01, 0x40, 0x3d, 0x9a, 0x99,
	0x4c, 0xb3, 0xea, 0xb4, 0x5d, 0x34, 0x3f, 0xa0, 0xdd, 0x77, 0xd3, 0x45, 0x37, 0xfd, 0x01, 0xfd,
	0x01, 0x9d, 0xc9, 0xbf, 0xe8, 0xbe, 0x7f, 0xa0, 0x73, 0xcf, 0x05, 0x48, 0xbc, 0xf8, 0xaa, 0x92,
	0xb4, 0x3b, 0xde, 0xf3, 0xbe, 0xdf, 0x39, 0xf7, 0x71, 0x2e, 0x08, 0xef, 0xfb, 0x5d, 0x97, 0x7a,
	0xaf, 0xdc, 0xb6, 0x7e, 0x4d, 0xbd, 0x57, 0xa6, 0x47, 0x7d, 0x7f, 0x23, 0x4b, 0x5a, 0x77, 0x3d,
	0x27, 0x70, 0x88, 0x88, 0x1c, 0xce, 0x40, 0xba, 0xfc, 0xaf, 0x12, 0x4c, 0xed, 0xb2, 0x5f, 0x64,
	0x0e, 0x0a, 0x96, 0x29, 0x09, 0x2b, 0xc2, 0xea, 0x9d, 0x46, 0xc1, 0x32, 0xc9, 0x02, 0x4c, 0xb5,
	0xe9, 0x05, 0x6d, 0x4b, 0x05, 0x24, 0xf1, 0x01, 0x79, 0x0a, 0xb7, 0x3d, 0xea, 0x3b, 0x5d, 0xcf,
	0xa0, 0x5a, 0xd7, 0x6b, 0x4b, 0xc5, 0x15, 0x61, 0xb5, 0xd2, 0x98, 0x8d, 0x68, 0x2d, 0x2f, 0x29,
	0xd2, 0x31, 0xdf, 0x48, 0xa5, 0xa4, 0xc8, 0xa1, 0xf9, 0x86, 0xfc, 0x10, 0x48, 0xc7, 0xb2, 0x35,
	0xa3, 0x6d, 0x51, 0x3b, 0xd0, 0x2e, 0xa8, 0xe7, 0x5b, 0x8e, 0x2d, 0x4d, 0xa1, 0xa0, 0xd8, 0xb1,
	0xec, 0x1a, 0x32, 0xde, 0x71, 0x3a, 0x21, 0x50, 0xb2, 0xf5, 0x0e, 0x95, 0xa6, 0x91, 0x8f, 0xbf,
	0x19, 0xcd, 0xa4, 0xbe, 0x21, 0xcd, 0x70, 0x1a, 0xfb, 0x4d, 0x24, 0x98, 0x89, 0x4c, 0x95, 0x57,
	0x84, 0xd5, 0x62, 0x23, 0x1a, 0x92, 0x65, 0x28, 0x5f, 0xd2, 0x53, 0xad, 0x6d, 0xf9, 0x81, 0x54,
	0x41, 0x8d, 0x99, 0x4b, 0x7a, 0x7a, 0x60, 0xf9, 0x01, 0x79, 0x02, 0xb3, 0x8c, 0xe5, 0x7a, 0xf4,
	0xc2, 0xa2, 0x97, 0x12, 0x20, 0x17, 0x2e, 0xe9, 0xe9, 0x09, 0xa7, 0x90, 0x07, 0x50, 0x61, 0x02,
	0x7a, 0xd7, 0xb4, 0x1c, 0x69, 0x16, 0xd9, 0xcc, 0xd8, 0x36, 0x1b, 0x47, 0xcc, 0x0b, 0xcb, 0xa4,
	0x8e, 0x74, 0xbb, 0xc7, 0x7c, 0xc7, 0xc6, 0xe4, 0x11, 0x30, 0x3b, 0x9a, 0xe1, 0xd8, 0x67, 0xd6,
	0xb9, 0x74, 0x07, 0xb9, 0x4c, 0xbc, 0x86, 0x04, 0xb2, 0x04, 0x2c, 0x08, 0x2d, 0x70, 0x5c, 0x69,
	0x0e, 0x79, 0xd3, 0x97, 0xf4, 0xb4, 0xe9, 0xb8, 0x91, 0x9e, 0x1f, 0xe8, 0x81, 0x65, 0x48, 0xf3,
	0x3d, 0x3d, 0x15, 0x09, 0x6c, 0x9a, 0x1e, 0xed, 0x38, 0x17, 0xd4, 0x94, 0x44, 0x4c, 0x4d, 0x34,
	0x64, 0xd3, 0x34, 0x9c, 0x8e, 0x86, 0xc0, 0xdc, 0xe5, 0xd3, 0x34, 0x9c, 0xce, 0x2e, 0xc3, 0x66,
	0x19, 0xca, 0xa7, 0xf4, 0xdc, 0xb2, 0xb5, 0xc0, 0x97, 0xc8, 0x8a, 0xb0, 0x5a, 0x6a, 0xcc, 0xe0,
	0xb8, 0xe9, 0x93, 0xfb, 0x30, 0x4d, 0x6d, 0x93, 0x31, 0xee, 0x21, 0x63, 0x8a, 0xda, 0x66, 0xd3,
	0x67, 0x51, 0x58, 0xbe, 0x66, 0xd2, 0x33, 0xbd, 0xdb, 0x0e, 0xa4, 0x05, 0xf4, 0x54, 0xb1, 0xfc,
	0x5d, 0x4e, 0x20, 0x55, 0x28, 0x3b, 0x2e, 0xf5, 0xf4, 0xc0, 0xf1, 0xa4, 0xfb, 0x7c, 0xe2, 0xd1,
	0x98, 0x95, 0x8e, 0x65, 0x9b, 0xf4, 0x4a, 0x5a, 0xc4, 0x34, 0xf0, 0x81, 0x7c, 0x0a, 0x15, 0xac,
	0xb4, 0xba, 0x7d, 0x86, 0xd8, 0x60, 0x01, 0x6a, 0xc1, 0xb5, 0x4b, 0xc3, 0xaa, 0xab, 0x20, 0xa5,
	0x79, 0xed, 0x52, 0xf2, 0x61, 0xc4, 0xb6, 0xec, 0x33, 0x07, 0x2b, 0x70, 0x76, 0x73, 0x69, 0x3d,
	0x5d, 0xbd, 0xeb, 0x68, 0x2f, 0xd4, 0x63, 0x66, 0xe5, 0x1d, 0x98, 0xdf, 0x36, 0xcd, 0x9e, 0x9b,
	0x06, 0xfd, 0x9c, 0x6c, 0x40, 0x09, 0x8d, 0x08, 0x68, 0xe4, 0xc1, 0x00, 0x23, 0x28, 0x8d, 0x82,
	0xf2, 0x07, 0x20, 0x26, 0x6d, 0xf8, 0x2e, 0xab, 0x92, 0x8e, 0x7e, 0xd5, 0xab, 0x54, 0x1e, 0x2f,
	0x74, 0xf4, 0xab, 0xb0, 0x46, 0xe5, 0x33, 0x20, 0x2d, 0xd7, 0xd4, 0x03, 0x7a, 0x23, 0xdf, 0x69,
	0x3f, 0x85, 0x8c, 0x9f, 0x0f, 0xe1, 0x5e, 0xc6, 0xcf, 0x38, 0xf1, 0xf9, 0xb0, 0x98, 0xd2, 0x63,
	0xd5, 0xcf, 0x62, 0xfc, 0x09, 0x54, 0x98, 0x6b, 0xbe, 0x38, 0x84, 0x95, 0xe2, 0xa8, 0x40, 0xcb,
	0x56, 0xa8, 0x3c, 0x3a, 0xd8, 0x2d, 0x58, 0xca, 0x75, 0x3a, 0x4e, 0xc0, 0x1f, 0xc1, 0xfc, 0x2e,
	0x6d, 0x27, 0xd0, 0x1c, 0x51, 0x33, 0xcb, 0x50, 0x0e, 0x6b, 0xc6, 0x0c, 0x63, 0x99, 0xe1, 0x85,
	0x61, 0xb2, 0x94, 0x26, 0x8d, 0x8d, 0x13, 0xc1, 0x6b, 0xb8, 0xb7, 0x47, 0x83, 0x0c, 0x5e, 0xc3,
	0xa3, 0x90, 0x3f, 0x87, 0x85, 0xac, 0x96, 0xef, 0x7e, 0x97, 0x30, 0xff, 0x1a, 0x1e, 0x44, 0x2e,
	0xf9, 0xd6, 0x72, 0xd8, 0xe3, 0xb1, 0x80, 0x73, 0x26, 0x5a, 0x8c, 0xeb, 0x93, 0xad, 0xc4, 0x8c,
	0x98, 0xfd, 0xb9, 0x81, 0xb1, 0xb1, 0x39, 0xc6, 0xa7, 0xfb, 0x05, 0x3c, 0x1c, 0xec, 0x3b, 0x1f,
	0xe5, 0x6f, 0xcf, 0xf9, 0x57, 0x02, 0xdc, 0x4f, 0x7a, 0x8f, 0x92, 0xb4, 0x95, 0x49, 0xd2, 0xd8,
	0x56, 0xc9, 0x22, 0x4c, 0x3b, 0x67, 0x67, 0x3e, 0x0d, 0x42, 0xa8, 0xc3, 0x11, 0x1e, 0x88, 0x56,
	0xc7, 0x0a, 0xf0, 0xcc, 0x63, 0x07, 0x22, 0x1b, 0xc8, 0xdf, 0x08, 0xb0, 0x98, 0x17, 0x83, 0xef,
	0xde, 0x28, 0x88, 0xde, 0x06, 0x88, 0xf5, 0x52, 0xc0, 0x7a, 0x19, 0xb1, 0x01, 0x62, 0xb1, 0xf4,
	0x83, 0x2f, 0xe6, 0x07, 0x5f, 0x8a, 0x05, 0xcf, 0xb6, 0x7e, 0xcb, 0xd7, 0xa8, 0x6d, 0xe2, 0xd9,
	0x5b, 0x6e, 0x4c, 0x59, 0xbe, 0x62, 0x9b, 0x72, 0x00, 0x62, 0xbf, 0x86, 0x5b, 0x3e, 0x65, 0x88,
	0xf6, 0xaf, 0x07, 0x45, 0xbc, 0x1e, 0xdc, 0x20, 0x6f, 0xfd, 0xab, 0x45, 0x31, 0x76, 0xb5, 0x90,
	0xbf, 0x16, 0xe0, 0x6e, 0xca, 0xad, 0xef, 0x7e, 0xf7, 0x7e, 0x13, 0xfb, 0x46, 0x29, 0xb9, 0x6f,
	0xfc, 0x51, 0x00, 0x51, 0xfd, 0xde, 0x91, 0x18, 0x16, 0x11, 0x03, 0x49, 0xfd, 0xff, 0x02, 0xa9,
	0x81, 0x3b, 0xde, 0xc7, 0xf4, 0x34, 0xb6, 0x06, 0x6e, 0xb8, 0x06, 0xe5, 0x3f, 0xf0, 0x95, 0x9d,
	0x36, 0xfa, 0xbf, 0x59, 0x54, 0x72, 0x1d, 0x9e, 0xec, 0xd1, 0xa0, 0xe5, 0x53, 0xaf, 0xd6, 0xf5,
	0xbc, 0xda, 0xa7, 0x7a, 0xb0, 0x73, 0xce, 0x33, 0x60, 0x46, 0x1b, 0x8e, 0x08, 0xc5, 0x6e, 0xef,
	0xfa, 0xcc, 0x7e, 0xe6, 0xdf, 0x9f, 0xe5, 0x3a, 0xdc, 0xe1, 0xfa, 0xaa, 0x4b, 0x0d, 0x4b, 0x6f,
	0xb3, 0xe3, 0x24, 0x70, 0x34, 0xdd, 0x30, 0x9c, 0xae, 0x1d, 0xa0, 0x7e, 0xa5, 0x51, 0x09, 0x9c,
	0x6d, 0x4e, 0x18, 0x76, 0xa8, 0xfd, 0x5e, 0x80, 0x95, 0xe1, 0x61, 0xf9, 0x2e, 0x79, 0x0e, 0xf3,
	0x86, 0xd3, 0xe9, 0x38, 0xb6, 0xd6, 0x33, 0xc3, 0x63, 0xbc, 0xc3, 0xc9, 0xa1, 0x3c, 0xf9, 0x69,
	0x0e, 0x34, 0x4f, 0xb2, 0xd0, 0x24, 0x62, 0x8f, 0x43, 0xf4, 0x17, 0x01, 0x1e, 0xa9, 0x34, 0x48,
	0xf0, 0x87, 0x2f, 0x9b, 0x49, 0xeb, 0x2c, 0x85, 0xd4, 0x54, 0x1a, 0xa9, 0x17, 0x30, 0xef, 0x51,
	0xb7, 0xad, 0x1b, 0xb4, 0x27, 0xc3, 0x1b, 0x86, 0xb9, 0x90, 0x1c, 0x0a, 0xca, 0x2b, 0xf0, 0x78,
	0x58, 0xa4, 0xbe, 0x2b, 0xbf, 0x0f, 0x4b, 0x6c, 0x8d, 0xf1, 0x9b, 0x6e, 0x72, 0x16, 0xb1, 0x3c,
	0x17, 0x31, 0xcf, 0x72, 0x15, 0xa4, 0x7c, 0x61, 0xdf, 0x95, 0xcf, 0x40, 0xce, 0x64, 0x28, 0xe1,
	0xd2, 0x9c, 0xa0, 0x76, 0xd8, 0xc5, 0x3f, 0x9a, 0x19, 0x6f, 0xbb, 0xa2, 0xa1, 0xfc, 0x33, 0x78,
	0x6f, 0xa4, 0x1f, 0xdf, 0x4d, 0x80, 0x2b, 0x24, 0x8b, 0xe9, 0x77, 0xfc, 0x18, 0x63, 0x26, 0x50,
	0x63, 0xdf, 0xf2, 0x03, 0xc7, 0xbb, 0xce, 0x0f, 0x6f, 0x03, 0x4a, 0xe3, 0x6e, 0x2c, 0x28, 0x38,
	0xd9, 0xa9, 0x24, 0xff, 0x55, 0x00, 0x31, 0x1d, 0xc8, 0x90, 0xd8, 0xd9, 0x1d, 0xc3, 0xf0, 0xa8,
	0x1e, 0x50, 0x2d, 0xb0, 0x3a, 0x34, 0xba, 0x20, 0x71, 0x52, 0xd3, 0xea, 0xd0, 0x78, 0xc7, 0x54,
	0x4c, 0x76, 0x4c, 0x51, 0x6b, 0x59, 0x8a, 0xb5, 0x96, 0xf1, 0x66, 0x71, 0x2a, 0xd9, 0x2c, 0xf6,
	0xf2, 0x32, 0x1d, 0x5f, 0xd3, 0xdf, 0x08, 0xb0, 0x94, 0x8b, 0x9d, 0xef, 0x7e, 0xef, 0xe0, 0x11,
	0x05, 0x6e, 0x7f, 0xca, 0xfd, 0x47, 0x33, 0x60, 0x4b, 0x59, 0xce, 0xba, 0xc9, 0x84, 0x3b, 0x1b,
	0xea, 0xe1, 0x7a, 0xfe, 0x87, 0x00, 0x8b, 0x2a, 0xb5, 0x79, 0x1b, 0xa4, 0x5c, 0xb9, 0xd4, 0xb3,
	0xa8, 0x6d, 0xe0, 0x12, 0x58, 0x05, 0xfe, 0x8c, 0xa0, 0x71, 0x6b, 0x5a, 0x7f, 0x7e, 0x73, 0x48,
	0x3f, 0x41, 0x72, 0xcb, 0x32, 0xc9, 0x1a, 0xdc, 0xb5, 0x6c, 0xc3, 0xc3, 0x0e, 0x58, 0x73, 0x75,
	0xe3, 0x33, 0xfd, 0x9c, 0xcf, 0xbb, 0xd4, 0x98, 0x67, 0x0c, 0xb6, 0xad, 0x9f, 0x70, 0x32, 0x43,
	0xdd, 0xf1, 0x4c, 0xea, 0xb1, 0xfc, 0x86, 0xd5, 0x8d, 0xe3, 0xba, 0xc9, 0x00, 0xf0, 0xa8, 0xee,
	0x3b, 0x76, 0x98, 0xa6, 0x70, 0xc4, 0xf2, 0xee, 0x53, 0xef, 0x82, 0x7a, 0x3c, 0xef, 0x53, 0xfc,
	0x6e, 0xc9, 0x49, 0x2c, 0xef, 0xf2, 0x32, 0x5b, 0xc7, 0x39, 0x73, 0xf0, 0x5d, 0xf9, 0x0a, 0xe6,
	0xfa, 0x14, 0xec, 0x48, 0x93, 0x2d, 0xa7, 0x30, 0x6e, 0xcb, 0x49, 0xd6, 0xe1, 0x1e, 0xed, 0x59,
	0xd2, 0xb0, 0x93, 0x8e, 0xaa, 0xb0, 0xd8, 0xb8, 0xdb, 0x67, 0x29, 0xb6, 0x89, 0x41, 0x75, 0xe0,
	0x51, 0x74, 0xcb, 0x61, 0x29, 0x48, 0x46, 0x91, 0xbf, 0xde, 0x6e, 0x72, 0x47, 0xfe, 0x0c, 0x1e,
	0x0f, 0x73, 0xe7, 0xbb, 0xa4, 0x0e, 0xf3, 0xb1, 0x09, 0xc4, 0xfa, 0x93, 0x95, 0xac, 0x8b, 0x94,
	0xfa, 0x5c, 0x5f, 0x11, 0xab, 0xe6, 0x9f, 0x05, 0xb8, 0x9f, 0x42, 0x3b, 0x7c, 0xec, 0xd8, 0x0a,
	0xab, 0x9e, 0x1f, 0xd8, 0xcf, 0x87, 0x59, 0xe6, 0x1a, 0xb1, 0x05, 0x90, 0xbf, 0x1b, 0x6e, 0x01,
	0xe0, 0x0f, 0x0e, 0x4a, 0x71, 0x10, 0x28, 0x07, 0x4c, 0x86, 0x83, 0xd2, 0x8e, 0x7e, 0xb2, 0xa3,
	0xc4, 0x0f, 0x74, 0x2f, 0xe0, 0xa9, 0x2a, 0x61, 0xaa, 0x2a, 0x48, 0xc1, 0xfd, 0x62, 0x19, 0xca,
	0xbd, 0x3c, 0xf2, 0xaa, 0x9a, 0xa1, 0x3c, 0x7b, 0x64, 0x09, 0x66, 0xac, 0x80, 0x76, 0x58, 0x95,
	0xf2, 0x3d, 0x60, 0x9a, 0x0d, 0xeb, 0x26, 0xf6, 0x77, 0x8c, 0x81, 0xd1, 0xcc, 0x8c, 0x4e, 0x51,
	0x99, 0x49, 0x63, 0x30, 0x55, 0x28, 0x9b, 0x5d, 0x4f, 0x0f, 0xfa, 0xef, 0x56, 0xbd, 0xb1, 0xfc,
	0x67, 0x01, 0x16, 0x53, 0x80, 0x46, 0x0b, 0xa6, 0x7f, 0x9e, 0x96, 0xf0, 0x3c, 0xdd, 0x87, 0x59,
	0xfe, 0xd2, 0x14, 0x3f, 0xc2, 0x5f, 0x0c, 0x08, 0x21, 0x8d, 0x76, 0x03, 0x8c, 0x5e, 0xef, 0x42,
	0x5e, 0xc2, 0x5d, 0xc7, 0x6e, 0x5f, 0x6b, 0x86, 0x6e, 0x6b, 0xe7, 0x34, 0xd0, 0x1c, 0xdb, 0xe0,
	0x00, 0x97, 0x1b, 0x73, 0x8c, 0x51, 0xd3, 0xed, 0x3d, 0x1a, 0x1c, 0xdb, 0x06, 0x95, 0xd7, 0xfb,
	0xed, 0x5f, 0x26, 0xc2, 0xe4, 0xa1, 0x8f, 0x41, 0xca, 0x46, 0xbf, 0xf8, 0x73, 0xe4, 0x7d, 0x97,
	0xec, 0xc0, 0x4c, 0xb4, 0x51, 0xf0, 0x25, 0xb8, 0x3a, 0x72, 0x06, 0x91, 0x7a, 0xa4, 0x28, 0x7f,
	0x09, 0x0f, 0xd5, 0x61, 0x41, 0x7d, 0x0b, 0x3e, 0x12, 0xcf, 0x5f, 0x85, 0xe4, 0xf3, 0x97, 0xfc,
	0x04, 0xaf, 0x42, 0x83, 0x27, 0x29, 0xff, 0x4d, 0x00, 0x49, 0x8d, 0x6d, 0x95, 0x3e, 0x7f, 0x20,
	0x51, 0x2e, 0xa8, 0x1d, 0x4c, 0xb0, 0xbd, 0x8e, 0x3c, 0xf7, 0xa2, 0xa3, 0xa6, 0x38, 0xee, 0x51,
	0x33, 0xf8, 0xf6, 0xb5, 0xf6, 0x27, 0x21, 0x7c, 0xbe, 0x0b, 0xdf, 0x5a, 0xee, 0xef, 0x36, 0x14,
	0x55, 0xd5, 0x9a, 0xbf, 0x3c, 0x51, 0xb4, 0xd6, 0x91, 0x7a, 0xa2, 0xd4, 0xea, 0x6f, 0xeb, 0x35,
	0xf1, 0x16, 0x91, 0x60, 0x21, 0xc6, 0x6a, 0x1c, 0x1f, 0x1f, 0x6a, 0x6a, 0xab, 0xde, 0x14, 0x05,
	0xf2, 0x18, 0xaa, 0x31, 0x0e, 0xaa, 0x6c, 0x1f, 0x68, 0xb5, 0xe3, 0xa3, 0x9a, 0xd2, 0x38, 0x12,
	0x0b, 0x29, 0x7e, 0x6d, 0x7f, 0xbb, 0xa9, 0xed, 0x6c, 0xd7, 0x3e, 0xda, 0x6b, 0x1c, 0xb7, 0x8e,
	0x76, 0xc5, 0x22, 0xa9, 0xc2, 0x62, 0x86, 0xdf, 0xda, 0xd9, 0x39, 0x50, 0xc4, 0xd2, 0x5a, 0x0d,
	0xe6, 0x31, 0x3a, 0x35, 0xd0, 0x83, 0x6e, 0xd4, 0xc7, 0x13, 0x2e, 0xae, 0x36, 0xb7, 0x9b, 0x2d,
	0x55, 0x53, 0xf7, 0x95, 0x83, 0xb7, 0xf1, 0x00, 0x43, 0x7a, 0xeb, 0x88, 0x73, 0x84, 0xb5, 0xdf,
	0x08, 0xb0, 0x90, 0xb7, 0x0f, 0x91, 0x67, 0xf0, 0x54, 0xf9, 0xc5, 0x89, 0xd2, 0xa8, 0x2b, 0x47,
	0x35, 0x85, 0x45, 0xfc, 0xb6, 0xbe, 0x97, 0x33, 0xf5, 0x15, 0x78, 0x38, 0x40, 0xec, 0x40, 0x79,
	0xa7, 0x1c, 0x88, 0x02, 0x79, 0x02, 0x0f, 0x06, 0x48, 0xd4, 0x9b, 0xca, 0xa1, 0x58, 0x58, 0xfb,
	0xbb, 0x00, 0x95, 0xde, 0x96, 0xc5, 0x60, 0x46, 0xcd, 0x7c, 0x98, 0x63, 0xac, 0x03, 0x84, 0x65,
	0x7f, 0xfb, 0x48, 0x14, 0x52, 0x9c, 0xc3, 0xe3, 0x86, 0xc2, 0x39, 0x05, 0xb2, 0x00, 0x62, 0x8c,
	0xa3, 0xfc, 0xbc, 0xb5, 0x7d, 0x20, 0x16, 0xc9, 0x53, 0x78, 0x94, 0x67, 0x49, 0x3b, 0x6e, 0x84,
	0x22, 0xa5, 0x94, 0x48, 0xcf, 0x64, 0x5f, 0x64, 0x6a, 0xf3, 0xdf, 0x22, 0x88, 0xb1, 0x9a, 0xe6,
	0xdf, 0x14, 0xbe, 0x00, 0x69, 0xd0, 0xeb, 0x10, 0x79, 0x95, 0x2d, 0xc7, 0x21, 0xaf, 0x58, 0xd5,
	0xf5, 0x49, 0xc4, 0x7d, 0x57, 0xbe, 0x45, 0x2c, 0x20, 0xd9, 0x87, 0x19, 0xf2, 0x62, 0x94, 0x9d,
	0xb0, 0xa3, 0xab, 0xae, 0x8e, 0x27, 0x88, 0xae, 0x3e, 0x81, 0x3b, 0x89, 0x97, 0x0b, 0x22, 0x0f,
	0x56, 0x8e, 0x5a, 0x89, 0xea, 0x7b, 0x23, 0x65, 0x22, 0xdb, 0xea, 0x28, 0xdb, 0xea, 0x18, 0xb6,
	0xd5, 0x1c, 0xdb, 0x67, 0xf8, 0xe2, 0x92, 0xec, 0xb2, 0xc9, 0xf3, 0xdc, 0xb8, 0x32, 0xfd, 0x7d,
	0xf5, 0xc5, 0x58, 0x72, 0xe8, 0xe7, 0xb7, 0x02, 0x9e, 0x13, 0x03, 0x5b, 0x55, 0xf2, 0xe3, 0x5c,
	0x5b, 0xc3, 0x3a, 0xee, 0xea, 0xe6, 0xa4, 0x2a, 0x18, 0xc9, 0x57, 0x02, 0x54, 0x07, 0x77, 0x7f,
	0x64, 0x23, 0x17, 0xb7, 0xc1, 0x5d, 0x6d, 0xf5, 0x47, 0x93, 0x29, 0x60, 0x0c, 0x0e, 0x2c, 0xe4,
	0x75, 0x8c, 0xe4, 0x65, 0x7e, 0xd2, 0x72, 0xda, 0xd0, 0xea, 0xda, 0xb8, 0xa2, 0xe8, 0xf0, 0x6b,
	0x21, 0xe7, 0x01, 0x23, 0xd9, 0x1f, 0x92, 0xd7, 0x63, 0xc0, 0x99, 0x69, 0x5d, 0xab, 0x6f, 0xfe,
	0x0b, 0x2d, 0x0c, 0xa9, 0x8d, 0x8f, 0xeb, 0x99, 0x2e, 0x6f, 0x75, 0xa0, 0xbd, 0x54, 0x57, 0x5a,
	0x7d, 0x39, 0xa6, 0x24, 0x7a, 0xfb, 0x18, 0x6e, 0xc7, 0x3f, 0xe9, 0x90, 0xa7, 0x59, 0xe5, 0xd4,
	0x67, 0xa3, 0xaa, 0x3c, 0x4a, 0x04, 0x0d, 0xff, 0x0a, 0xe6, 0x53, 0x5f, 0x38, 0xc8, 0x0f, 0x72,
	0x5a, 0xad, 0xcc, 0x97, 0xa1, 0xea, 0xb3, 0x31, 0xa4, 0x22, 0xa0, 0x72, 0xbe, 0xa1, 0xe4, 0x01,
	0x95, 0xff, 0x7d, 0x27, 0x0f, 0xa8, 0x01, 0x1f, 0x65, 0x38, 0x50, 0xf1, 0x0f, 0x25, 0x79, 0x40,
	0xa5, 0xbe, 0xca, 0xe4, 0x01, 0x95, 0xfe, 0xd6, 0x22, 0xdf, 0x22, 0x46, 0xfc, 0x49, 0x39, 0x9c,
	0xc3, 0xb3, 0x61, 0x1b, 0x60, 0x7f, 0x02, 0xcf, 0xc7, 0x11, 0x8b, 0xb0, 0xca, 0xe9, 0xf7, 0xf2,
	0xb0, 0xca, 0x6f, 0x6d, 0xab, 0x2f, 0xc7, 0x94, 0xec, 0x6d, 0x25, 0x83, 0x5b, 0xab, 0xbc, 0xad,
	0x64, 0x68, 0xdf, 0x97, 0xb7, 0x95, 0x0c, 0xef, 0xdc, 0xe4, 0x5b, 0xe4, 0x4b, 0x58, 0x1e, 0x78,
	0x9f, 0x26, 0x43, 0x8e, 0xcc, 0xbc, 0x7b, 0x71, 0x75, 0x63, 0x22, 0xf9, 0xc8, 0xbf, 0x3a, 0x89,
	0x7f, 0x75, 0x42, 0xff, 0xc3, 0xef, 0xd1, 0xb7, 0x76, 0x5e, 0x7f, 0xb2, 0x79, 0xee, 0xb4, 0x75,
	0xfb, 0x7c, 0xfd, 0xcd, 0x66, 0x10, 0xac, 0x1b, 0x4e, 0x67, 0x03, 0xff, 0xe9, 0x60, 0x38, 0xed,
	0x0d, 0x9f, 0x7a, 0x17, 0x96, 0x41, 0xc3, 0xbf, 0x43, 0xc4, 0xac, 0x9e, 0x4e, 0xa3, 0xcc, 0x07,
	0xff, 0x09, 0x00, 0x00, 0xff, 0xff, 0x49, 0x1e, 0xe4, 0x63, 0x3c, 0x21, 0x00, 0x00,
}
