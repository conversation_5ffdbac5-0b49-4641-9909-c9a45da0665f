// Code generated by protoc-gen-go. DO NOT EDIT.
// source: group/group-mangement/group-management.proto

package group_management // import "golang.52tt.com/protocol/services/group/group-management"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type E_GROUP_TYPE int32

const (
	E_GROUP_TYPE_E_GROUP_TYPE_GUILD_MAIN E_GROUP_TYPE = 0
	E_GROUP_TYPE_E_GROUP_TYPE_GAME_MAIN  E_GROUP_TYPE = 1
	E_GROUP_TYPE_E_GROUP_TYPE_GAME       E_GROUP_TYPE = 2
	E_GROUP_TYPE_E_GROUP_TYPE_TEMP       E_GROUP_TYPE = 3
	E_GROUP_TYPE_E_GROUP_TYPE_TGROUP     E_GROUP_TYPE = 4
	E_GROUP_TYPE_E_GROUP_TYPE_COMMUNITY  E_GROUP_TYPE = 5
	E_GROUP_TYPE_E_GROUP_TYPE_INVALID    E_GROUP_TYPE = 15
)

var E_GROUP_TYPE_name = map[int32]string{
	0:  "E_GROUP_TYPE_GUILD_MAIN",
	1:  "E_GROUP_TYPE_GAME_MAIN",
	2:  "E_GROUP_TYPE_GAME",
	3:  "E_GROUP_TYPE_TEMP",
	4:  "E_GROUP_TYPE_TGROUP",
	5:  "E_GROUP_TYPE_COMMUNITY",
	15: "E_GROUP_TYPE_INVALID",
}
var E_GROUP_TYPE_value = map[string]int32{
	"E_GROUP_TYPE_GUILD_MAIN": 0,
	"E_GROUP_TYPE_GAME_MAIN":  1,
	"E_GROUP_TYPE_GAME":       2,
	"E_GROUP_TYPE_TEMP":       3,
	"E_GROUP_TYPE_TGROUP":     4,
	"E_GROUP_TYPE_COMMUNITY":  5,
	"E_GROUP_TYPE_INVALID":    15,
}

func (x E_GROUP_TYPE) String() string {
	return proto.EnumName(E_GROUP_TYPE_name, int32(x))
}
func (E_GROUP_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{0}
}

type SortField int32

const (
	SortField_Role   SortField = 0
	SortField_JoinAt SortField = 1
	SortField_Uid    SortField = 2
)

var SortField_name = map[int32]string{
	0: "Role",
	1: "JoinAt",
	2: "Uid",
}
var SortField_value = map[string]int32{
	"Role":   0,
	"JoinAt": 1,
	"Uid":    2,
}

func (x SortField) String() string {
	return proto.EnumName(SortField_name, int32(x))
}
func (SortField) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{1}
}

type SortType int32

const (
	SortType_ASC  SortType = 0
	SortType_DESC SortType = 1
)

var SortType_name = map[int32]string{
	0: "ASC",
	1: "DESC",
}
var SortType_value = map[string]int32{
	"ASC":  0,
	"DESC": 1,
}

func (x SortType) String() string {
	return proto.EnumName(SortType_name, int32(x))
}
func (SortType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{2}
}

type GroupMemberRole int32

const (
	GroupMemberRole_NIL     GroupMemberRole = 0
	GroupMemberRole_OWNER   GroupMemberRole = 1
	GroupMemberRole_ADMIN   GroupMemberRole = 2
	GroupMemberRole_COMMON  GroupMemberRole = 3
	GroupMemberRole_UNKNOWN GroupMemberRole = 255
)

var GroupMemberRole_name = map[int32]string{
	0:   "NIL",
	1:   "OWNER",
	2:   "ADMIN",
	3:   "COMMON",
	255: "UNKNOWN",
}
var GroupMemberRole_value = map[string]int32{
	"NIL":     0,
	"OWNER":   1,
	"ADMIN":   2,
	"COMMON":  3,
	"UNKNOWN": 255,
}

func (x GroupMemberRole) String() string {
	return proto.EnumName(GroupMemberRole_name, int32(x))
}
func (GroupMemberRole) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{3}
}

type TGroupExtra_TYPE int32

const (
	TGroupExtra_GAME_NIL      TGroupExtra_TYPE = 0
	TGroupExtra_GAME_HOBBY    TGroupExtra_TYPE = 1
	TGroupExtra_GAME_OFFICIAL TGroupExtra_TYPE = 2
)

var TGroupExtra_TYPE_name = map[int32]string{
	0: "GAME_NIL",
	1: "GAME_HOBBY",
	2: "GAME_OFFICIAL",
}
var TGroupExtra_TYPE_value = map[string]int32{
	"GAME_NIL":      0,
	"GAME_HOBBY":    1,
	"GAME_OFFICIAL": 2,
}

func (x TGroupExtra_TYPE) String() string {
	return proto.EnumName(TGroupExtra_TYPE_name, int32(x))
}
func (TGroupExtra_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{20, 0}
}

type GroupMemberResp struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Role                 uint32   `protobuf:"varint,2,opt,name=role,proto3" json:"role,omitempty"`
	Remark               string   `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark,omitempty"`
	Weight               uint32   `protobuf:"varint,4,opt,name=weight,proto3" json:"weight,omitempty"`
	GroupCard            string   `protobuf:"bytes,5,opt,name=group_card,json=groupCard,proto3" json:"group_card,omitempty"`
	GroupMute            bool     `protobuf:"varint,6,opt,name=group_mute,json=groupMute,proto3" json:"group_mute,omitempty"`
	JoinedAt             uint32   `protobuf:"varint,7,opt,name=joined_at,json=joinedAt,proto3" json:"joined_at,omitempty"`
	RecvMsgOpt           uint32   `protobuf:"varint,8,opt,name=recv_msg_opt,json=recvMsgOpt,proto3" json:"recv_msg_opt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupMemberResp) Reset()         { *m = GroupMemberResp{} }
func (m *GroupMemberResp) String() string { return proto.CompactTextString(m) }
func (*GroupMemberResp) ProtoMessage()    {}
func (*GroupMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{0}
}
func (m *GroupMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupMemberResp.Unmarshal(m, b)
}
func (m *GroupMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupMemberResp.Marshal(b, m, deterministic)
}
func (dst *GroupMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupMemberResp.Merge(dst, src)
}
func (m *GroupMemberResp) XXX_Size() int {
	return xxx_messageInfo_GroupMemberResp.Size(m)
}
func (m *GroupMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_GroupMemberResp proto.InternalMessageInfo

func (m *GroupMemberResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GroupMemberResp) GetRole() uint32 {
	if m != nil {
		return m.Role
	}
	return 0
}

func (m *GroupMemberResp) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *GroupMemberResp) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *GroupMemberResp) GetGroupCard() string {
	if m != nil {
		return m.GroupCard
	}
	return ""
}

func (m *GroupMemberResp) GetGroupMute() bool {
	if m != nil {
		return m.GroupMute
	}
	return false
}

func (m *GroupMemberResp) GetJoinedAt() uint32 {
	if m != nil {
		return m.JoinedAt
	}
	return 0
}

func (m *GroupMemberResp) GetRecvMsgOpt() uint32 {
	if m != nil {
		return m.RecvMsgOpt
	}
	return 0
}

type GetGroupMemberListReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	OnlyAdmin            bool     `protobuf:"varint,4,opt,name=only_admin,json=onlyAdmin,proto3" json:"only_admin,omitempty"`
	GuildId              uint32   `protobuf:"varint,5,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	IsAllDuty            bool     `protobuf:"varint,6,opt,name=is_all_duty,json=isAllDuty,proto3" json:"is_all_duty,omitempty"`
	SortFields           []int32  `protobuf:"varint,7,rep,packed,name=sort_fields,json=sortFields,proto3" json:"sort_fields,omitempty"`
	SortType             int32    `protobuf:"varint,8,opt,name=sort_type,json=sortType,proto3" json:"sort_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupMemberListReq) Reset()         { *m = GetGroupMemberListReq{} }
func (m *GetGroupMemberListReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupMemberListReq) ProtoMessage()    {}
func (*GetGroupMemberListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{1}
}
func (m *GetGroupMemberListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupMemberListReq.Unmarshal(m, b)
}
func (m *GetGroupMemberListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupMemberListReq.Marshal(b, m, deterministic)
}
func (dst *GetGroupMemberListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupMemberListReq.Merge(dst, src)
}
func (m *GetGroupMemberListReq) XXX_Size() int {
	return xxx_messageInfo_GetGroupMemberListReq.Size(m)
}
func (m *GetGroupMemberListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupMemberListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupMemberListReq proto.InternalMessageInfo

func (m *GetGroupMemberListReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GetGroupMemberListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGroupMemberListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetGroupMemberListReq) GetOnlyAdmin() bool {
	if m != nil {
		return m.OnlyAdmin
	}
	return false
}

func (m *GetGroupMemberListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGroupMemberListReq) GetIsAllDuty() bool {
	if m != nil {
		return m.IsAllDuty
	}
	return false
}

func (m *GetGroupMemberListReq) GetSortFields() []int32 {
	if m != nil {
		return m.SortFields
	}
	return nil
}

func (m *GetGroupMemberListReq) GetSortType() int32 {
	if m != nil {
		return m.SortType
	}
	return 0
}

type GroupMemberListResp struct {
	Members              []*GroupMemberResp `protobuf:"bytes,1,rep,name=members,proto3" json:"members,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GroupMemberListResp) Reset()         { *m = GroupMemberListResp{} }
func (m *GroupMemberListResp) String() string { return proto.CompactTextString(m) }
func (*GroupMemberListResp) ProtoMessage()    {}
func (*GroupMemberListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{2}
}
func (m *GroupMemberListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupMemberListResp.Unmarshal(m, b)
}
func (m *GroupMemberListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupMemberListResp.Marshal(b, m, deterministic)
}
func (dst *GroupMemberListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupMemberListResp.Merge(dst, src)
}
func (m *GroupMemberListResp) XXX_Size() int {
	return xxx_messageInfo_GroupMemberListResp.Size(m)
}
func (m *GroupMemberListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupMemberListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GroupMemberListResp proto.InternalMessageInfo

func (m *GroupMemberListResp) GetMembers() []*GroupMemberResp {
	if m != nil {
		return m.Members
	}
	return nil
}

type GetGroupMembersByUidsReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uids                 []uint32 `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupMembersByUidsReq) Reset()         { *m = GetGroupMembersByUidsReq{} }
func (m *GetGroupMembersByUidsReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupMembersByUidsReq) ProtoMessage()    {}
func (*GetGroupMembersByUidsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{3}
}
func (m *GetGroupMembersByUidsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupMembersByUidsReq.Unmarshal(m, b)
}
func (m *GetGroupMembersByUidsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupMembersByUidsReq.Marshal(b, m, deterministic)
}
func (dst *GetGroupMembersByUidsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupMembersByUidsReq.Merge(dst, src)
}
func (m *GetGroupMembersByUidsReq) XXX_Size() int {
	return xxx_messageInfo_GetGroupMembersByUidsReq.Size(m)
}
func (m *GetGroupMembersByUidsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupMembersByUidsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupMembersByUidsReq proto.InternalMessageInfo

func (m *GetGroupMembersByUidsReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GetGroupMembersByUidsReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type GetGroupMemberReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupMemberReq) Reset()         { *m = GetGroupMemberReq{} }
func (m *GetGroupMemberReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupMemberReq) ProtoMessage()    {}
func (*GetGroupMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{4}
}
func (m *GetGroupMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupMemberReq.Unmarshal(m, b)
}
func (m *GetGroupMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupMemberReq.Marshal(b, m, deterministic)
}
func (dst *GetGroupMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupMemberReq.Merge(dst, src)
}
func (m *GetGroupMemberReq) XXX_Size() int {
	return xxx_messageInfo_GetGroupMemberReq.Size(m)
}
func (m *GetGroupMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupMemberReq proto.InternalMessageInfo

func (m *GetGroupMemberReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GetGroupMemberReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 记录需要管理员批准的入群申请
type ApplyGroupReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GroupId              uint32   `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Verify               string   `protobuf:"bytes,3,opt,name=verify,proto3" json:"verify,omitempty"`
	Seq                  uint32   `protobuf:"varint,4,opt,name=seq,proto3" json:"seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyGroupReq) Reset()         { *m = ApplyGroupReq{} }
func (m *ApplyGroupReq) String() string { return proto.CompactTextString(m) }
func (*ApplyGroupReq) ProtoMessage()    {}
func (*ApplyGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{5}
}
func (m *ApplyGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyGroupReq.Unmarshal(m, b)
}
func (m *ApplyGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyGroupReq.Marshal(b, m, deterministic)
}
func (dst *ApplyGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyGroupReq.Merge(dst, src)
}
func (m *ApplyGroupReq) XXX_Size() int {
	return xxx_messageInfo_ApplyGroupReq.Size(m)
}
func (m *ApplyGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyGroupReq proto.InternalMessageInfo

func (m *ApplyGroupReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApplyGroupReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *ApplyGroupReq) GetVerify() string {
	if m != nil {
		return m.Verify
	}
	return ""
}

func (m *ApplyGroupReq) GetSeq() uint32 {
	if m != nil {
		return m.Seq
	}
	return 0
}

type ApplyGroupResp struct {
	ApplyId              uint32   `protobuf:"varint,1,opt,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyGroupResp) Reset()         { *m = ApplyGroupResp{} }
func (m *ApplyGroupResp) String() string { return proto.CompactTextString(m) }
func (*ApplyGroupResp) ProtoMessage()    {}
func (*ApplyGroupResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{6}
}
func (m *ApplyGroupResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyGroupResp.Unmarshal(m, b)
}
func (m *ApplyGroupResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyGroupResp.Marshal(b, m, deterministic)
}
func (dst *ApplyGroupResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyGroupResp.Merge(dst, src)
}
func (m *ApplyGroupResp) XXX_Size() int {
	return xxx_messageInfo_ApplyGroupResp.Size(m)
}
func (m *ApplyGroupResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyGroupResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyGroupResp proto.InternalMessageInfo

func (m *ApplyGroupResp) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

// 根据申请ID 获取需要管理员批准的入群申请详情
type GetGroupApplyReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ApplyId              uint32   `protobuf:"varint,2,opt,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupApplyReq) Reset()         { *m = GetGroupApplyReq{} }
func (m *GetGroupApplyReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupApplyReq) ProtoMessage()    {}
func (*GetGroupApplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{7}
}
func (m *GetGroupApplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupApplyReq.Unmarshal(m, b)
}
func (m *GetGroupApplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupApplyReq.Marshal(b, m, deterministic)
}
func (dst *GetGroupApplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupApplyReq.Merge(dst, src)
}
func (m *GetGroupApplyReq) XXX_Size() int {
	return xxx_messageInfo_GetGroupApplyReq.Size(m)
}
func (m *GetGroupApplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupApplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupApplyReq proto.InternalMessageInfo

func (m *GetGroupApplyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGroupApplyReq) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

type GroupApplyResp struct {
	ApplyId              uint32   `protobuf:"varint,1,opt,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	GroupId              uint32   `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Verify               string   `protobuf:"bytes,4,opt,name=verify,proto3" json:"verify,omitempty"`
	Status               uint32   `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	ApplyAt              uint32   `protobuf:"varint,6,opt,name=apply_at,json=applyAt,proto3" json:"apply_at,omitempty"`
	Reviewer             uint32   `protobuf:"varint,7,opt,name=reviewer,proto3" json:"reviewer,omitempty"`
	ReviewedAt           uint32   `protobuf:"varint,8,opt,name=reviewed_at,json=reviewedAt,proto3" json:"reviewed_at,omitempty"`
	Seq                  uint32   `protobuf:"varint,9,opt,name=seq,proto3" json:"seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupApplyResp) Reset()         { *m = GroupApplyResp{} }
func (m *GroupApplyResp) String() string { return proto.CompactTextString(m) }
func (*GroupApplyResp) ProtoMessage()    {}
func (*GroupApplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{8}
}
func (m *GroupApplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupApplyResp.Unmarshal(m, b)
}
func (m *GroupApplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupApplyResp.Marshal(b, m, deterministic)
}
func (dst *GroupApplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupApplyResp.Merge(dst, src)
}
func (m *GroupApplyResp) XXX_Size() int {
	return xxx_messageInfo_GroupApplyResp.Size(m)
}
func (m *GroupApplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupApplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GroupApplyResp proto.InternalMessageInfo

func (m *GroupApplyResp) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

func (m *GroupApplyResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GroupApplyResp) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupApplyResp) GetVerify() string {
	if m != nil {
		return m.Verify
	}
	return ""
}

func (m *GroupApplyResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GroupApplyResp) GetApplyAt() uint32 {
	if m != nil {
		return m.ApplyAt
	}
	return 0
}

func (m *GroupApplyResp) GetReviewer() uint32 {
	if m != nil {
		return m.Reviewer
	}
	return 0
}

func (m *GroupApplyResp) GetReviewedAt() uint32 {
	if m != nil {
		return m.ReviewedAt
	}
	return 0
}

func (m *GroupApplyResp) GetSeq() uint32 {
	if m != nil {
		return m.Seq
	}
	return 0
}

type GroupApply struct {
	ApplyId              uint32   `protobuf:"varint,1,opt,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	GroupId              uint32   `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Verify               string   `protobuf:"bytes,4,opt,name=verify,proto3" json:"verify,omitempty"`
	Status               uint32   `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	ApplyAt              uint32   `protobuf:"varint,6,opt,name=apply_at,json=applyAt,proto3" json:"apply_at,omitempty"`
	Reviewer             uint32   `protobuf:"varint,7,opt,name=reviewer,proto3" json:"reviewer,omitempty"`
	ReviewedAt           uint32   `protobuf:"varint,8,opt,name=reviewed_at,json=reviewedAt,proto3" json:"reviewed_at,omitempty"`
	Seq                  uint32   `protobuf:"varint,9,opt,name=seq,proto3" json:"seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupApply) Reset()         { *m = GroupApply{} }
func (m *GroupApply) String() string { return proto.CompactTextString(m) }
func (*GroupApply) ProtoMessage()    {}
func (*GroupApply) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{9}
}
func (m *GroupApply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupApply.Unmarshal(m, b)
}
func (m *GroupApply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupApply.Marshal(b, m, deterministic)
}
func (dst *GroupApply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupApply.Merge(dst, src)
}
func (m *GroupApply) XXX_Size() int {
	return xxx_messageInfo_GroupApply.Size(m)
}
func (m *GroupApply) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupApply.DiscardUnknown(m)
}

var xxx_messageInfo_GroupApply proto.InternalMessageInfo

func (m *GroupApply) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

func (m *GroupApply) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GroupApply) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupApply) GetVerify() string {
	if m != nil {
		return m.Verify
	}
	return ""
}

func (m *GroupApply) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GroupApply) GetApplyAt() uint32 {
	if m != nil {
		return m.ApplyAt
	}
	return 0
}

func (m *GroupApply) GetReviewer() uint32 {
	if m != nil {
		return m.Reviewer
	}
	return 0
}

func (m *GroupApply) GetReviewedAt() uint32 {
	if m != nil {
		return m.ReviewedAt
	}
	return 0
}

func (m *GroupApply) GetSeq() uint32 {
	if m != nil {
		return m.Seq
	}
	return 0
}

type GetGroupApplyByGroupIdReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GroupId              uint32   `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupApplyByGroupIdReq) Reset()         { *m = GetGroupApplyByGroupIdReq{} }
func (m *GetGroupApplyByGroupIdReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupApplyByGroupIdReq) ProtoMessage()    {}
func (*GetGroupApplyByGroupIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{10}
}
func (m *GetGroupApplyByGroupIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupApplyByGroupIdReq.Unmarshal(m, b)
}
func (m *GetGroupApplyByGroupIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupApplyByGroupIdReq.Marshal(b, m, deterministic)
}
func (dst *GetGroupApplyByGroupIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupApplyByGroupIdReq.Merge(dst, src)
}
func (m *GetGroupApplyByGroupIdReq) XXX_Size() int {
	return xxx_messageInfo_GetGroupApplyByGroupIdReq.Size(m)
}
func (m *GetGroupApplyByGroupIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupApplyByGroupIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupApplyByGroupIdReq proto.InternalMessageInfo

func (m *GetGroupApplyByGroupIdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGroupApplyByGroupIdReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type GetGroupApplyByGroupIdResp struct {
	GroupApply           *GroupApply `protobuf:"bytes,1,opt,name=group_apply,json=groupApply,proto3" json:"group_apply,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetGroupApplyByGroupIdResp) Reset()         { *m = GetGroupApplyByGroupIdResp{} }
func (m *GetGroupApplyByGroupIdResp) String() string { return proto.CompactTextString(m) }
func (*GetGroupApplyByGroupIdResp) ProtoMessage()    {}
func (*GetGroupApplyByGroupIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{11}
}
func (m *GetGroupApplyByGroupIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupApplyByGroupIdResp.Unmarshal(m, b)
}
func (m *GetGroupApplyByGroupIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupApplyByGroupIdResp.Marshal(b, m, deterministic)
}
func (dst *GetGroupApplyByGroupIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupApplyByGroupIdResp.Merge(dst, src)
}
func (m *GetGroupApplyByGroupIdResp) XXX_Size() int {
	return xxx_messageInfo_GetGroupApplyByGroupIdResp.Size(m)
}
func (m *GetGroupApplyByGroupIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupApplyByGroupIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupApplyByGroupIdResp proto.InternalMessageInfo

func (m *GetGroupApplyByGroupIdResp) GetGroupApply() *GroupApply {
	if m != nil {
		return m.GroupApply
	}
	return nil
}

type ReviewApplyGuildReq struct {
	Reviewer             uint32   `protobuf:"varint,1,opt,name=reviewer,proto3" json:"reviewer,omitempty"`
	ApplyUid             uint32   `protobuf:"varint,2,opt,name=apply_uid,json=applyUid,proto3" json:"apply_uid,omitempty"`
	ApplyId              uint32   `protobuf:"varint,3,opt,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	Agree                bool     `protobuf:"varint,4,opt,name=agree,proto3" json:"agree,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReviewApplyGuildReq) Reset()         { *m = ReviewApplyGuildReq{} }
func (m *ReviewApplyGuildReq) String() string { return proto.CompactTextString(m) }
func (*ReviewApplyGuildReq) ProtoMessage()    {}
func (*ReviewApplyGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{12}
}
func (m *ReviewApplyGuildReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReviewApplyGuildReq.Unmarshal(m, b)
}
func (m *ReviewApplyGuildReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReviewApplyGuildReq.Marshal(b, m, deterministic)
}
func (dst *ReviewApplyGuildReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReviewApplyGuildReq.Merge(dst, src)
}
func (m *ReviewApplyGuildReq) XXX_Size() int {
	return xxx_messageInfo_ReviewApplyGuildReq.Size(m)
}
func (m *ReviewApplyGuildReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReviewApplyGuildReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReviewApplyGuildReq proto.InternalMessageInfo

func (m *ReviewApplyGuildReq) GetReviewer() uint32 {
	if m != nil {
		return m.Reviewer
	}
	return 0
}

func (m *ReviewApplyGuildReq) GetApplyUid() uint32 {
	if m != nil {
		return m.ApplyUid
	}
	return 0
}

func (m *ReviewApplyGuildReq) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

func (m *ReviewApplyGuildReq) GetAgree() bool {
	if m != nil {
		return m.Agree
	}
	return false
}

type ReviewApplyGroupReq struct {
	Reviewer             uint32   `protobuf:"varint,1,opt,name=reviewer,proto3" json:"reviewer,omitempty"`
	ApplyUid             uint32   `protobuf:"varint,2,opt,name=apply_uid,json=applyUid,proto3" json:"apply_uid,omitempty"`
	ApplyId              uint32   `protobuf:"varint,3,opt,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	Agree                bool     `protobuf:"varint,4,opt,name=agree,proto3" json:"agree,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReviewApplyGroupReq) Reset()         { *m = ReviewApplyGroupReq{} }
func (m *ReviewApplyGroupReq) String() string { return proto.CompactTextString(m) }
func (*ReviewApplyGroupReq) ProtoMessage()    {}
func (*ReviewApplyGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{13}
}
func (m *ReviewApplyGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReviewApplyGroupReq.Unmarshal(m, b)
}
func (m *ReviewApplyGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReviewApplyGroupReq.Marshal(b, m, deterministic)
}
func (dst *ReviewApplyGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReviewApplyGroupReq.Merge(dst, src)
}
func (m *ReviewApplyGroupReq) XXX_Size() int {
	return xxx_messageInfo_ReviewApplyGroupReq.Size(m)
}
func (m *ReviewApplyGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReviewApplyGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReviewApplyGroupReq proto.InternalMessageInfo

func (m *ReviewApplyGroupReq) GetReviewer() uint32 {
	if m != nil {
		return m.Reviewer
	}
	return 0
}

func (m *ReviewApplyGroupReq) GetApplyUid() uint32 {
	if m != nil {
		return m.ApplyUid
	}
	return 0
}

func (m *ReviewApplyGroupReq) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

func (m *ReviewApplyGroupReq) GetAgree() bool {
	if m != nil {
		return m.Agree
	}
	return false
}

type ReviewApplyGroupResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReviewApplyGroupResp) Reset()         { *m = ReviewApplyGroupResp{} }
func (m *ReviewApplyGroupResp) String() string { return proto.CompactTextString(m) }
func (*ReviewApplyGroupResp) ProtoMessage()    {}
func (*ReviewApplyGroupResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{14}
}
func (m *ReviewApplyGroupResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReviewApplyGroupResp.Unmarshal(m, b)
}
func (m *ReviewApplyGroupResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReviewApplyGroupResp.Marshal(b, m, deterministic)
}
func (dst *ReviewApplyGroupResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReviewApplyGroupResp.Merge(dst, src)
}
func (m *ReviewApplyGroupResp) XXX_Size() int {
	return xxx_messageInfo_ReviewApplyGroupResp.Size(m)
}
func (m *ReviewApplyGroupResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReviewApplyGroupResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReviewApplyGroupResp proto.InternalMessageInfo

type RemoveGuildMemberReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	QuitType             uint32   `protobuf:"varint,3,opt,name=quit_type,json=quitType,proto3" json:"quit_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveGuildMemberReq) Reset()         { *m = RemoveGuildMemberReq{} }
func (m *RemoveGuildMemberReq) String() string { return proto.CompactTextString(m) }
func (*RemoveGuildMemberReq) ProtoMessage()    {}
func (*RemoveGuildMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{15}
}
func (m *RemoveGuildMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveGuildMemberReq.Unmarshal(m, b)
}
func (m *RemoveGuildMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveGuildMemberReq.Marshal(b, m, deterministic)
}
func (dst *RemoveGuildMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveGuildMemberReq.Merge(dst, src)
}
func (m *RemoveGuildMemberReq) XXX_Size() int {
	return xxx_messageInfo_RemoveGuildMemberReq.Size(m)
}
func (m *RemoveGuildMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveGuildMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveGuildMemberReq proto.InternalMessageInfo

func (m *RemoveGuildMemberReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *RemoveGuildMemberReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RemoveGuildMemberReq) GetQuitType() uint32 {
	if m != nil {
		return m.QuitType
	}
	return 0
}

type AddGroupMemberReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Weight               uint32   `protobuf:"varint,3,opt,name=weight,proto3" json:"weight,omitempty"`
	Role                 uint32   `protobuf:"varint,4,opt,name=role,proto3" json:"role,omitempty"`
	Operator             uint64   `protobuf:"varint,5,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddGroupMemberReq) Reset()         { *m = AddGroupMemberReq{} }
func (m *AddGroupMemberReq) String() string { return proto.CompactTextString(m) }
func (*AddGroupMemberReq) ProtoMessage()    {}
func (*AddGroupMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{16}
}
func (m *AddGroupMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddGroupMemberReq.Unmarshal(m, b)
}
func (m *AddGroupMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddGroupMemberReq.Marshal(b, m, deterministic)
}
func (dst *AddGroupMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddGroupMemberReq.Merge(dst, src)
}
func (m *AddGroupMemberReq) XXX_Size() int {
	return xxx_messageInfo_AddGroupMemberReq.Size(m)
}
func (m *AddGroupMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddGroupMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddGroupMemberReq proto.InternalMessageInfo

func (m *AddGroupMemberReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *AddGroupMemberReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddGroupMemberReq) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *AddGroupMemberReq) GetRole() uint32 {
	if m != nil {
		return m.Role
	}
	return 0
}

func (m *AddGroupMemberReq) GetOperator() uint64 {
	if m != nil {
		return m.Operator
	}
	return 0
}

type AddGroupMemberResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddGroupMemberResp) Reset()         { *m = AddGroupMemberResp{} }
func (m *AddGroupMemberResp) String() string { return proto.CompactTextString(m) }
func (*AddGroupMemberResp) ProtoMessage()    {}
func (*AddGroupMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{17}
}
func (m *AddGroupMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddGroupMemberResp.Unmarshal(m, b)
}
func (m *AddGroupMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddGroupMemberResp.Marshal(b, m, deterministic)
}
func (dst *AddGroupMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddGroupMemberResp.Merge(dst, src)
}
func (m *AddGroupMemberResp) XXX_Size() int {
	return xxx_messageInfo_AddGroupMemberResp.Size(m)
}
func (m *AddGroupMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddGroupMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddGroupMemberResp proto.InternalMessageInfo

type GetGroupReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupReq) Reset()         { *m = GetGroupReq{} }
func (m *GetGroupReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupReq) ProtoMessage()    {}
func (*GetGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{18}
}
func (m *GetGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupReq.Unmarshal(m, b)
}
func (m *GetGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupReq.Marshal(b, m, deterministic)
}
func (dst *GetGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupReq.Merge(dst, src)
}
func (m *GetGroupReq) XXX_Size() int {
	return xxx_messageInfo_GetGroupReq.Size(m)
}
func (m *GetGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupReq proto.InternalMessageInfo

func (m *GetGroupReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type MemberMap struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	MemberLimit          uint32   `protobuf:"varint,2,opt,name=memberLimit,proto3" json:"memberLimit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberMap) Reset()         { *m = MemberMap{} }
func (m *MemberMap) String() string { return proto.CompactTextString(m) }
func (*MemberMap) ProtoMessage()    {}
func (*MemberMap) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{19}
}
func (m *MemberMap) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberMap.Unmarshal(m, b)
}
func (m *MemberMap) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberMap.Marshal(b, m, deterministic)
}
func (dst *MemberMap) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberMap.Merge(dst, src)
}
func (m *MemberMap) XXX_Size() int {
	return xxx_messageInfo_MemberMap.Size(m)
}
func (m *MemberMap) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberMap.DiscardUnknown(m)
}

var xxx_messageInfo_MemberMap proto.InternalMessageInfo

func (m *MemberMap) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *MemberMap) GetMemberLimit() uint32 {
	if m != nil {
		return m.MemberLimit
	}
	return 0
}

type TGroupExtra struct {
	GroupDesc            string       `protobuf:"bytes,1,opt,name=group_desc,json=groupDesc,proto3" json:"group_desc,omitempty"`
	CityCode             string       `protobuf:"bytes,2,opt,name=city_code,json=cityCode,proto3" json:"city_code,omitempty"`
	CityName             string       `protobuf:"bytes,3,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	GameId               uint32       `protobuf:"varint,4,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	TgroupType           uint32       `protobuf:"varint,5,opt,name=tgroup_type,json=tgroupType,proto3" json:"tgroup_type,omitempty"`
	Level                uint32       `protobuf:"varint,6,opt,name=level,proto3" json:"level,omitempty"`
	MemberMap            []*MemberMap `protobuf:"bytes,7,rep,name=member_map,json=memberMap,proto3" json:"member_map,omitempty"`
	CreateFrom           string       `protobuf:"bytes,8,opt,name=create_from,json=createFrom,proto3" json:"create_from,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *TGroupExtra) Reset()         { *m = TGroupExtra{} }
func (m *TGroupExtra) String() string { return proto.CompactTextString(m) }
func (*TGroupExtra) ProtoMessage()    {}
func (*TGroupExtra) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{20}
}
func (m *TGroupExtra) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TGroupExtra.Unmarshal(m, b)
}
func (m *TGroupExtra) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TGroupExtra.Marshal(b, m, deterministic)
}
func (dst *TGroupExtra) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TGroupExtra.Merge(dst, src)
}
func (m *TGroupExtra) XXX_Size() int {
	return xxx_messageInfo_TGroupExtra.Size(m)
}
func (m *TGroupExtra) XXX_DiscardUnknown() {
	xxx_messageInfo_TGroupExtra.DiscardUnknown(m)
}

var xxx_messageInfo_TGroupExtra proto.InternalMessageInfo

func (m *TGroupExtra) GetGroupDesc() string {
	if m != nil {
		return m.GroupDesc
	}
	return ""
}

func (m *TGroupExtra) GetCityCode() string {
	if m != nil {
		return m.CityCode
	}
	return ""
}

func (m *TGroupExtra) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *TGroupExtra) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *TGroupExtra) GetTgroupType() uint32 {
	if m != nil {
		return m.TgroupType
	}
	return 0
}

func (m *TGroupExtra) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *TGroupExtra) GetMemberMap() []*MemberMap {
	if m != nil {
		return m.MemberMap
	}
	return nil
}

func (m *TGroupExtra) GetCreateFrom() string {
	if m != nil {
		return m.CreateFrom
	}
	return ""
}

type GroupResp struct {
	GroupId              uint32       `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	GuildId              uint32       `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GameId               uint32       `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GroupType            uint32       `protobuf:"varint,4,opt,name=group_type,json=groupType,proto3" json:"group_type,omitempty"`
	Name                 string       `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	MemberCount          uint32       `protobuf:"varint,6,opt,name=member_count,json=memberCount,proto3" json:"member_count,omitempty"`
	Creator              uint32       `protobuf:"varint,7,opt,name=creator,proto3" json:"creator,omitempty"`
	Owner                uint32       `protobuf:"varint,8,opt,name=owner,proto3" json:"owner,omitempty"`
	CreatedAt            uint32       `protobuf:"varint,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	NeedVerify           uint32       `protobuf:"varint,10,opt,name=need_verify,json=needVerify,proto3" json:"need_verify,omitempty"`
	AllMute              uint32       `protobuf:"varint,11,opt,name=all_mute,json=allMute,proto3" json:"all_mute,omitempty"`
	DisplayId            uint32       `protobuf:"varint,12,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	TgroupExtra          *TGroupExtra `protobuf:"bytes,13,opt,name=tgroup_extra,json=tgroupExtra,proto3" json:"tgroup_extra,omitempty"`
	NeedVerifyV2         uint32       `protobuf:"varint,14,opt,name=need_verify_v2,json=needVerifyV2,proto3" json:"need_verify_v2,omitempty"`
	MemberLimit          uint32       `protobuf:"varint,15,opt,name=member_limit,json=memberLimit,proto3" json:"member_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GroupResp) Reset()         { *m = GroupResp{} }
func (m *GroupResp) String() string { return proto.CompactTextString(m) }
func (*GroupResp) ProtoMessage()    {}
func (*GroupResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{21}
}
func (m *GroupResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupResp.Unmarshal(m, b)
}
func (m *GroupResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupResp.Marshal(b, m, deterministic)
}
func (dst *GroupResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupResp.Merge(dst, src)
}
func (m *GroupResp) XXX_Size() int {
	return xxx_messageInfo_GroupResp.Size(m)
}
func (m *GroupResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupResp.DiscardUnknown(m)
}

var xxx_messageInfo_GroupResp proto.InternalMessageInfo

func (m *GroupResp) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GroupResp) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GroupResp) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *GroupResp) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GroupResp) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

func (m *GroupResp) GetCreator() uint32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

func (m *GroupResp) GetOwner() uint32 {
	if m != nil {
		return m.Owner
	}
	return 0
}

func (m *GroupResp) GetCreatedAt() uint32 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *GroupResp) GetNeedVerify() uint32 {
	if m != nil {
		return m.NeedVerify
	}
	return 0
}

func (m *GroupResp) GetAllMute() uint32 {
	if m != nil {
		return m.AllMute
	}
	return 0
}

func (m *GroupResp) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *GroupResp) GetTgroupExtra() *TGroupExtra {
	if m != nil {
		return m.TgroupExtra
	}
	return nil
}

func (m *GroupResp) GetNeedVerifyV2() uint32 {
	if m != nil {
		return m.NeedVerifyV2
	}
	return 0
}

func (m *GroupResp) GetMemberLimit() uint32 {
	if m != nil {
		return m.MemberLimit
	}
	return 0
}

type MyGroupInfo struct {
	Groupinfo            *GroupResp           `protobuf:"bytes,1,opt,name=groupinfo,proto3" json:"groupinfo,omitempty"`
	Myrole               *GroupMemberResp     `protobuf:"bytes,2,opt,name=myrole,proto3" json:"myrole,omitempty"`
	Adminlist            *GroupMemberListResp `protobuf:"bytes,3,opt,name=adminlist,proto3" json:"adminlist,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *MyGroupInfo) Reset()         { *m = MyGroupInfo{} }
func (m *MyGroupInfo) String() string { return proto.CompactTextString(m) }
func (*MyGroupInfo) ProtoMessage()    {}
func (*MyGroupInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{22}
}
func (m *MyGroupInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MyGroupInfo.Unmarshal(m, b)
}
func (m *MyGroupInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MyGroupInfo.Marshal(b, m, deterministic)
}
func (dst *MyGroupInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MyGroupInfo.Merge(dst, src)
}
func (m *MyGroupInfo) XXX_Size() int {
	return xxx_messageInfo_MyGroupInfo.Size(m)
}
func (m *MyGroupInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MyGroupInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MyGroupInfo proto.InternalMessageInfo

func (m *MyGroupInfo) GetGroupinfo() *GroupResp {
	if m != nil {
		return m.Groupinfo
	}
	return nil
}

func (m *MyGroupInfo) GetMyrole() *GroupMemberResp {
	if m != nil {
		return m.Myrole
	}
	return nil
}

func (m *MyGroupInfo) GetAdminlist() *GroupMemberListResp {
	if m != nil {
		return m.Adminlist
	}
	return nil
}

type BatchGetGroupReq struct {
	GroupList            []uint32 `protobuf:"varint,1,rep,packed,name=group_list,json=groupList,proto3" json:"group_list,omitempty"`
	IsNeedmyrole         bool     `protobuf:"varint,2,opt,name=is_needmyrole,json=isNeedmyrole,proto3" json:"is_needmyrole,omitempty"`
	IsNeedadmin          bool     `protobuf:"varint,3,opt,name=is_needadmin,json=isNeedadmin,proto3" json:"is_needadmin,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetGroupReq) Reset()         { *m = BatchGetGroupReq{} }
func (m *BatchGetGroupReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetGroupReq) ProtoMessage()    {}
func (*BatchGetGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{23}
}
func (m *BatchGetGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGroupReq.Unmarshal(m, b)
}
func (m *BatchGetGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGroupReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGroupReq.Merge(dst, src)
}
func (m *BatchGetGroupReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetGroupReq.Size(m)
}
func (m *BatchGetGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGroupReq proto.InternalMessageInfo

func (m *BatchGetGroupReq) GetGroupList() []uint32 {
	if m != nil {
		return m.GroupList
	}
	return nil
}

func (m *BatchGetGroupReq) GetIsNeedmyrole() bool {
	if m != nil {
		return m.IsNeedmyrole
	}
	return false
}

func (m *BatchGetGroupReq) GetIsNeedadmin() bool {
	if m != nil {
		return m.IsNeedadmin
	}
	return false
}

func (m *BatchGetGroupReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type BatchGetGroupResp struct {
	GroupinfoList        []*MyGroupInfo `protobuf:"bytes,1,rep,name=groupinfo_list,json=groupinfoList,proto3" json:"groupinfo_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatchGetGroupResp) Reset()         { *m = BatchGetGroupResp{} }
func (m *BatchGetGroupResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetGroupResp) ProtoMessage()    {}
func (*BatchGetGroupResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{24}
}
func (m *BatchGetGroupResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGroupResp.Unmarshal(m, b)
}
func (m *BatchGetGroupResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGroupResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetGroupResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGroupResp.Merge(dst, src)
}
func (m *BatchGetGroupResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetGroupResp.Size(m)
}
func (m *BatchGetGroupResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGroupResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGroupResp proto.InternalMessageInfo

func (m *BatchGetGroupResp) GetGroupinfoList() []*MyGroupInfo {
	if m != nil {
		return m.GroupinfoList
	}
	return nil
}

type CreateTGroupExtra struct {
	CityCode             string   `protobuf:"bytes,1,opt,name=city_code,json=cityCode,proto3" json:"city_code,omitempty"`
	CityName             string   `protobuf:"bytes,2,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	TgroupDesc           string   `protobuf:"bytes,3,opt,name=tgroup_desc,json=tgroupDesc,proto3" json:"tgroup_desc,omitempty"`
	TgroupType           uint32   `protobuf:"varint,4,opt,name=tgroup_type,json=tgroupType,proto3" json:"tgroup_type,omitempty"`
	InitLevel            uint32   `protobuf:"varint,5,opt,name=init_level,json=initLevel,proto3" json:"init_level,omitempty"`
	CreateFrom           string   `protobuf:"bytes,6,opt,name=create_from,json=createFrom,proto3" json:"create_from,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateTGroupExtra) Reset()         { *m = CreateTGroupExtra{} }
func (m *CreateTGroupExtra) String() string { return proto.CompactTextString(m) }
func (*CreateTGroupExtra) ProtoMessage()    {}
func (*CreateTGroupExtra) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{25}
}
func (m *CreateTGroupExtra) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTGroupExtra.Unmarshal(m, b)
}
func (m *CreateTGroupExtra) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTGroupExtra.Marshal(b, m, deterministic)
}
func (dst *CreateTGroupExtra) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTGroupExtra.Merge(dst, src)
}
func (m *CreateTGroupExtra) XXX_Size() int {
	return xxx_messageInfo_CreateTGroupExtra.Size(m)
}
func (m *CreateTGroupExtra) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTGroupExtra.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTGroupExtra proto.InternalMessageInfo

func (m *CreateTGroupExtra) GetCityCode() string {
	if m != nil {
		return m.CityCode
	}
	return ""
}

func (m *CreateTGroupExtra) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *CreateTGroupExtra) GetTgroupDesc() string {
	if m != nil {
		return m.TgroupDesc
	}
	return ""
}

func (m *CreateTGroupExtra) GetTgroupType() uint32 {
	if m != nil {
		return m.TgroupType
	}
	return 0
}

func (m *CreateTGroupExtra) GetInitLevel() uint32 {
	if m != nil {
		return m.InitLevel
	}
	return 0
}

func (m *CreateTGroupExtra) GetCreateFrom() string {
	if m != nil {
		return m.CreateFrom
	}
	return ""
}

type CreateBasicGroupReq struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	GroupType            uint32   `protobuf:"varint,2,opt,name=group_type,json=groupType,proto3" json:"group_type,omitempty"`
	NeedVerify           uint32   `protobuf:"varint,3,opt,name=need_verify,json=needVerify,proto3" json:"need_verify,omitempty"`
	Owner                uint64   `protobuf:"varint,4,opt,name=owner,proto3" json:"owner,omitempty"`
	NeedDisplayId        bool     `protobuf:"varint,5,opt,name=need_display_id,json=needDisplayId,proto3" json:"need_display_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateBasicGroupReq) Reset()         { *m = CreateBasicGroupReq{} }
func (m *CreateBasicGroupReq) String() string { return proto.CompactTextString(m) }
func (*CreateBasicGroupReq) ProtoMessage()    {}
func (*CreateBasicGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{26}
}
func (m *CreateBasicGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateBasicGroupReq.Unmarshal(m, b)
}
func (m *CreateBasicGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateBasicGroupReq.Marshal(b, m, deterministic)
}
func (dst *CreateBasicGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateBasicGroupReq.Merge(dst, src)
}
func (m *CreateBasicGroupReq) XXX_Size() int {
	return xxx_messageInfo_CreateBasicGroupReq.Size(m)
}
func (m *CreateBasicGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateBasicGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateBasicGroupReq proto.InternalMessageInfo

func (m *CreateBasicGroupReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreateBasicGroupReq) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *CreateBasicGroupReq) GetNeedVerify() uint32 {
	if m != nil {
		return m.NeedVerify
	}
	return 0
}

func (m *CreateBasicGroupReq) GetOwner() uint64 {
	if m != nil {
		return m.Owner
	}
	return 0
}

func (m *CreateBasicGroupReq) GetNeedDisplayId() bool {
	if m != nil {
		return m.NeedDisplayId
	}
	return false
}

type CreateBasicGroupResp struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateBasicGroupResp) Reset()         { *m = CreateBasicGroupResp{} }
func (m *CreateBasicGroupResp) String() string { return proto.CompactTextString(m) }
func (*CreateBasicGroupResp) ProtoMessage()    {}
func (*CreateBasicGroupResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{27}
}
func (m *CreateBasicGroupResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateBasicGroupResp.Unmarshal(m, b)
}
func (m *CreateBasicGroupResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateBasicGroupResp.Marshal(b, m, deterministic)
}
func (dst *CreateBasicGroupResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateBasicGroupResp.Merge(dst, src)
}
func (m *CreateBasicGroupResp) XXX_Size() int {
	return xxx_messageInfo_CreateBasicGroupResp.Size(m)
}
func (m *CreateBasicGroupResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateBasicGroupResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateBasicGroupResp proto.InternalMessageInfo

func (m *CreateBasicGroupResp) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type CreateTGroupReq struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	NeedVerify           uint32   `protobuf:"varint,2,opt,name=need_verify,json=needVerify,proto3" json:"need_verify,omitempty"`
	Owner                uint64   `protobuf:"varint,3,opt,name=owner,proto3" json:"owner,omitempty"`
	GameId               uint32   `protobuf:"varint,4,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	CityCode             string   `protobuf:"bytes,5,opt,name=city_code,json=cityCode,proto3" json:"city_code,omitempty"`
	CityName             string   `protobuf:"bytes,6,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	Desc                 string   `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc,omitempty"`
	Type                 uint32   `protobuf:"varint,8,opt,name=type,proto3" json:"type,omitempty"`
	InitLevel            uint32   `protobuf:"varint,9,opt,name=init_level,json=initLevel,proto3" json:"init_level,omitempty"`
	CreateFrom           string   `protobuf:"bytes,10,opt,name=create_from,json=createFrom,proto3" json:"create_from,omitempty"`
	Ip                   string   `protobuf:"bytes,11,opt,name=ip,proto3" json:"ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateTGroupReq) Reset()         { *m = CreateTGroupReq{} }
func (m *CreateTGroupReq) String() string { return proto.CompactTextString(m) }
func (*CreateTGroupReq) ProtoMessage()    {}
func (*CreateTGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{28}
}
func (m *CreateTGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTGroupReq.Unmarshal(m, b)
}
func (m *CreateTGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTGroupReq.Marshal(b, m, deterministic)
}
func (dst *CreateTGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTGroupReq.Merge(dst, src)
}
func (m *CreateTGroupReq) XXX_Size() int {
	return xxx_messageInfo_CreateTGroupReq.Size(m)
}
func (m *CreateTGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTGroupReq proto.InternalMessageInfo

func (m *CreateTGroupReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreateTGroupReq) GetNeedVerify() uint32 {
	if m != nil {
		return m.NeedVerify
	}
	return 0
}

func (m *CreateTGroupReq) GetOwner() uint64 {
	if m != nil {
		return m.Owner
	}
	return 0
}

func (m *CreateTGroupReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CreateTGroupReq) GetCityCode() string {
	if m != nil {
		return m.CityCode
	}
	return ""
}

func (m *CreateTGroupReq) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *CreateTGroupReq) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *CreateTGroupReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *CreateTGroupReq) GetInitLevel() uint32 {
	if m != nil {
		return m.InitLevel
	}
	return 0
}

func (m *CreateTGroupReq) GetCreateFrom() string {
	if m != nil {
		return m.CreateFrom
	}
	return ""
}

func (m *CreateTGroupReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

type CreateTGroupResp struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateTGroupResp) Reset()         { *m = CreateTGroupResp{} }
func (m *CreateTGroupResp) String() string { return proto.CompactTextString(m) }
func (*CreateTGroupResp) ProtoMessage()    {}
func (*CreateTGroupResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{29}
}
func (m *CreateTGroupResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTGroupResp.Unmarshal(m, b)
}
func (m *CreateTGroupResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTGroupResp.Marshal(b, m, deterministic)
}
func (dst *CreateTGroupResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTGroupResp.Merge(dst, src)
}
func (m *CreateTGroupResp) XXX_Size() int {
	return xxx_messageInfo_CreateTGroupResp.Size(m)
}
func (m *CreateTGroupResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTGroupResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTGroupResp proto.InternalMessageInfo

func (m *CreateTGroupResp) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type DismissBasicGroupReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DismissBasicGroupReq) Reset()         { *m = DismissBasicGroupReq{} }
func (m *DismissBasicGroupReq) String() string { return proto.CompactTextString(m) }
func (*DismissBasicGroupReq) ProtoMessage()    {}
func (*DismissBasicGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{30}
}
func (m *DismissBasicGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DismissBasicGroupReq.Unmarshal(m, b)
}
func (m *DismissBasicGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DismissBasicGroupReq.Marshal(b, m, deterministic)
}
func (dst *DismissBasicGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DismissBasicGroupReq.Merge(dst, src)
}
func (m *DismissBasicGroupReq) XXX_Size() int {
	return xxx_messageInfo_DismissBasicGroupReq.Size(m)
}
func (m *DismissBasicGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DismissBasicGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_DismissBasicGroupReq proto.InternalMessageInfo

func (m *DismissBasicGroupReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type DismissBasicGroupResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DismissBasicGroupResp) Reset()         { *m = DismissBasicGroupResp{} }
func (m *DismissBasicGroupResp) String() string { return proto.CompactTextString(m) }
func (*DismissBasicGroupResp) ProtoMessage()    {}
func (*DismissBasicGroupResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{31}
}
func (m *DismissBasicGroupResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DismissBasicGroupResp.Unmarshal(m, b)
}
func (m *DismissBasicGroupResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DismissBasicGroupResp.Marshal(b, m, deterministic)
}
func (dst *DismissBasicGroupResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DismissBasicGroupResp.Merge(dst, src)
}
func (m *DismissBasicGroupResp) XXX_Size() int {
	return xxx_messageInfo_DismissBasicGroupResp.Size(m)
}
func (m *DismissBasicGroupResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DismissBasicGroupResp.DiscardUnknown(m)
}

var xxx_messageInfo_DismissBasicGroupResp proto.InternalMessageInfo

type CreateGroupReq struct {
	GuildId              uint32             `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GameId               uint32             `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Uid                  uint32             `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Name                 string             `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	GroupType            uint32             `protobuf:"varint,5,opt,name=group_type,json=groupType,proto3" json:"group_type,omitempty"`
	NeedVerify           uint32             `protobuf:"varint,6,opt,name=need_verify,json=needVerify,proto3" json:"need_verify,omitempty"`
	TgroupExtra          *CreateTGroupExtra `protobuf:"bytes,7,opt,name=tgroup_extra,json=tgroupExtra,proto3" json:"tgroup_extra,omitempty"`
	NeedDisplayId        bool               `protobuf:"varint,8,opt,name=need_display_id,json=needDisplayId,proto3" json:"need_display_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CreateGroupReq) Reset()         { *m = CreateGroupReq{} }
func (m *CreateGroupReq) String() string { return proto.CompactTextString(m) }
func (*CreateGroupReq) ProtoMessage()    {}
func (*CreateGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{32}
}
func (m *CreateGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGroupReq.Unmarshal(m, b)
}
func (m *CreateGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGroupReq.Marshal(b, m, deterministic)
}
func (dst *CreateGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGroupReq.Merge(dst, src)
}
func (m *CreateGroupReq) XXX_Size() int {
	return xxx_messageInfo_CreateGroupReq.Size(m)
}
func (m *CreateGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGroupReq proto.InternalMessageInfo

func (m *CreateGroupReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CreateGroupReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CreateGroupReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CreateGroupReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreateGroupReq) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *CreateGroupReq) GetNeedVerify() uint32 {
	if m != nil {
		return m.NeedVerify
	}
	return 0
}

func (m *CreateGroupReq) GetTgroupExtra() *CreateTGroupExtra {
	if m != nil {
		return m.TgroupExtra
	}
	return nil
}

func (m *CreateGroupReq) GetNeedDisplayId() bool {
	if m != nil {
		return m.NeedDisplayId
	}
	return false
}

type CreateGroupResp struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGroupResp) Reset()         { *m = CreateGroupResp{} }
func (m *CreateGroupResp) String() string { return proto.CompactTextString(m) }
func (*CreateGroupResp) ProtoMessage()    {}
func (*CreateGroupResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{33}
}
func (m *CreateGroupResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGroupResp.Unmarshal(m, b)
}
func (m *CreateGroupResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGroupResp.Marshal(b, m, deterministic)
}
func (dst *CreateGroupResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGroupResp.Merge(dst, src)
}
func (m *CreateGroupResp) XXX_Size() int {
	return xxx_messageInfo_CreateGroupResp.Size(m)
}
func (m *CreateGroupResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGroupResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGroupResp proto.InternalMessageInfo

func (m *CreateGroupResp) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type UpdateGroupNameReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGroupNameReq) Reset()         { *m = UpdateGroupNameReq{} }
func (m *UpdateGroupNameReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupNameReq) ProtoMessage()    {}
func (*UpdateGroupNameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{34}
}
func (m *UpdateGroupNameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupNameReq.Unmarshal(m, b)
}
func (m *UpdateGroupNameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupNameReq.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupNameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupNameReq.Merge(dst, src)
}
func (m *UpdateGroupNameReq) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupNameReq.Size(m)
}
func (m *UpdateGroupNameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupNameReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupNameReq proto.InternalMessageInfo

func (m *UpdateGroupNameReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *UpdateGroupNameReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type UpdateGroupNameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGroupNameResp) Reset()         { *m = UpdateGroupNameResp{} }
func (m *UpdateGroupNameResp) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupNameResp) ProtoMessage()    {}
func (*UpdateGroupNameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{35}
}
func (m *UpdateGroupNameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupNameResp.Unmarshal(m, b)
}
func (m *UpdateGroupNameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupNameResp.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupNameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupNameResp.Merge(dst, src)
}
func (m *UpdateGroupNameResp) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupNameResp.Size(m)
}
func (m *UpdateGroupNameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupNameResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupNameResp proto.InternalMessageInfo

type RemoveGroupMemberReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Operator             uint64   `protobuf:"varint,3,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveGroupMemberReq) Reset()         { *m = RemoveGroupMemberReq{} }
func (m *RemoveGroupMemberReq) String() string { return proto.CompactTextString(m) }
func (*RemoveGroupMemberReq) ProtoMessage()    {}
func (*RemoveGroupMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{36}
}
func (m *RemoveGroupMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveGroupMemberReq.Unmarshal(m, b)
}
func (m *RemoveGroupMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveGroupMemberReq.Marshal(b, m, deterministic)
}
func (dst *RemoveGroupMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveGroupMemberReq.Merge(dst, src)
}
func (m *RemoveGroupMemberReq) XXX_Size() int {
	return xxx_messageInfo_RemoveGroupMemberReq.Size(m)
}
func (m *RemoveGroupMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveGroupMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveGroupMemberReq proto.InternalMessageInfo

func (m *RemoveGroupMemberReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *RemoveGroupMemberReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RemoveGroupMemberReq) GetOperator() uint64 {
	if m != nil {
		return m.Operator
	}
	return 0
}

type RemoveGroupMemberResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveGroupMemberResp) Reset()         { *m = RemoveGroupMemberResp{} }
func (m *RemoveGroupMemberResp) String() string { return proto.CompactTextString(m) }
func (*RemoveGroupMemberResp) ProtoMessage()    {}
func (*RemoveGroupMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{37}
}
func (m *RemoveGroupMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveGroupMemberResp.Unmarshal(m, b)
}
func (m *RemoveGroupMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveGroupMemberResp.Marshal(b, m, deterministic)
}
func (dst *RemoveGroupMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveGroupMemberResp.Merge(dst, src)
}
func (m *RemoveGroupMemberResp) XXX_Size() int {
	return xxx_messageInfo_RemoveGroupMemberResp.Size(m)
}
func (m *RemoveGroupMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveGroupMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveGroupMemberResp proto.InternalMessageInfo

type SetGuildAdminToMemberReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGuildAdminToMemberReq) Reset()         { *m = SetGuildAdminToMemberReq{} }
func (m *SetGuildAdminToMemberReq) String() string { return proto.CompactTextString(m) }
func (*SetGuildAdminToMemberReq) ProtoMessage()    {}
func (*SetGuildAdminToMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{38}
}
func (m *SetGuildAdminToMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGuildAdminToMemberReq.Unmarshal(m, b)
}
func (m *SetGuildAdminToMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGuildAdminToMemberReq.Marshal(b, m, deterministic)
}
func (dst *SetGuildAdminToMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGuildAdminToMemberReq.Merge(dst, src)
}
func (m *SetGuildAdminToMemberReq) XXX_Size() int {
	return xxx_messageInfo_SetGuildAdminToMemberReq.Size(m)
}
func (m *SetGuildAdminToMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGuildAdminToMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGuildAdminToMemberReq proto.InternalMessageInfo

func (m *SetGuildAdminToMemberReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SetGuildAdminToMemberReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SetGroupMemberToOwnerReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGroupMemberToOwnerReq) Reset()         { *m = SetGroupMemberToOwnerReq{} }
func (m *SetGroupMemberToOwnerReq) String() string { return proto.CompactTextString(m) }
func (*SetGroupMemberToOwnerReq) ProtoMessage()    {}
func (*SetGroupMemberToOwnerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{39}
}
func (m *SetGroupMemberToOwnerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGroupMemberToOwnerReq.Unmarshal(m, b)
}
func (m *SetGroupMemberToOwnerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGroupMemberToOwnerReq.Marshal(b, m, deterministic)
}
func (dst *SetGroupMemberToOwnerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGroupMemberToOwnerReq.Merge(dst, src)
}
func (m *SetGroupMemberToOwnerReq) XXX_Size() int {
	return xxx_messageInfo_SetGroupMemberToOwnerReq.Size(m)
}
func (m *SetGroupMemberToOwnerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGroupMemberToOwnerReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGroupMemberToOwnerReq proto.InternalMessageInfo

func (m *SetGroupMemberToOwnerReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *SetGroupMemberToOwnerReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SetGroupMemberToOwnerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGroupMemberToOwnerResp) Reset()         { *m = SetGroupMemberToOwnerResp{} }
func (m *SetGroupMemberToOwnerResp) String() string { return proto.CompactTextString(m) }
func (*SetGroupMemberToOwnerResp) ProtoMessage()    {}
func (*SetGroupMemberToOwnerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{40}
}
func (m *SetGroupMemberToOwnerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGroupMemberToOwnerResp.Unmarshal(m, b)
}
func (m *SetGroupMemberToOwnerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGroupMemberToOwnerResp.Marshal(b, m, deterministic)
}
func (dst *SetGroupMemberToOwnerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGroupMemberToOwnerResp.Merge(dst, src)
}
func (m *SetGroupMemberToOwnerResp) XXX_Size() int {
	return xxx_messageInfo_SetGroupMemberToOwnerResp.Size(m)
}
func (m *SetGroupMemberToOwnerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGroupMemberToOwnerResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGroupMemberToOwnerResp proto.InternalMessageInfo

type SetGroupOwnerToMemberReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGroupOwnerToMemberReq) Reset()         { *m = SetGroupOwnerToMemberReq{} }
func (m *SetGroupOwnerToMemberReq) String() string { return proto.CompactTextString(m) }
func (*SetGroupOwnerToMemberReq) ProtoMessage()    {}
func (*SetGroupOwnerToMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{41}
}
func (m *SetGroupOwnerToMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGroupOwnerToMemberReq.Unmarshal(m, b)
}
func (m *SetGroupOwnerToMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGroupOwnerToMemberReq.Marshal(b, m, deterministic)
}
func (dst *SetGroupOwnerToMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGroupOwnerToMemberReq.Merge(dst, src)
}
func (m *SetGroupOwnerToMemberReq) XXX_Size() int {
	return xxx_messageInfo_SetGroupOwnerToMemberReq.Size(m)
}
func (m *SetGroupOwnerToMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGroupOwnerToMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGroupOwnerToMemberReq proto.InternalMessageInfo

func (m *SetGroupOwnerToMemberReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *SetGroupOwnerToMemberReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SetGroupOwnerToMemberResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGroupOwnerToMemberResp) Reset()         { *m = SetGroupOwnerToMemberResp{} }
func (m *SetGroupOwnerToMemberResp) String() string { return proto.CompactTextString(m) }
func (*SetGroupOwnerToMemberResp) ProtoMessage()    {}
func (*SetGroupOwnerToMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{42}
}
func (m *SetGroupOwnerToMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGroupOwnerToMemberResp.Unmarshal(m, b)
}
func (m *SetGroupOwnerToMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGroupOwnerToMemberResp.Marshal(b, m, deterministic)
}
func (dst *SetGroupOwnerToMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGroupOwnerToMemberResp.Merge(dst, src)
}
func (m *SetGroupOwnerToMemberResp) XXX_Size() int {
	return xxx_messageInfo_SetGroupOwnerToMemberResp.Size(m)
}
func (m *SetGroupOwnerToMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGroupOwnerToMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGroupOwnerToMemberResp proto.InternalMessageInfo

type SetGroupMemberToAdminReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGroupMemberToAdminReq) Reset()         { *m = SetGroupMemberToAdminReq{} }
func (m *SetGroupMemberToAdminReq) String() string { return proto.CompactTextString(m) }
func (*SetGroupMemberToAdminReq) ProtoMessage()    {}
func (*SetGroupMemberToAdminReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{43}
}
func (m *SetGroupMemberToAdminReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGroupMemberToAdminReq.Unmarshal(m, b)
}
func (m *SetGroupMemberToAdminReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGroupMemberToAdminReq.Marshal(b, m, deterministic)
}
func (dst *SetGroupMemberToAdminReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGroupMemberToAdminReq.Merge(dst, src)
}
func (m *SetGroupMemberToAdminReq) XXX_Size() int {
	return xxx_messageInfo_SetGroupMemberToAdminReq.Size(m)
}
func (m *SetGroupMemberToAdminReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGroupMemberToAdminReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGroupMemberToAdminReq proto.InternalMessageInfo

func (m *SetGroupMemberToAdminReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *SetGroupMemberToAdminReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SetGroupAdminToMemberReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGroupAdminToMemberReq) Reset()         { *m = SetGroupAdminToMemberReq{} }
func (m *SetGroupAdminToMemberReq) String() string { return proto.CompactTextString(m) }
func (*SetGroupAdminToMemberReq) ProtoMessage()    {}
func (*SetGroupAdminToMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{44}
}
func (m *SetGroupAdminToMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGroupAdminToMemberReq.Unmarshal(m, b)
}
func (m *SetGroupAdminToMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGroupAdminToMemberReq.Marshal(b, m, deterministic)
}
func (dst *SetGroupAdminToMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGroupAdminToMemberReq.Merge(dst, src)
}
func (m *SetGroupAdminToMemberReq) XXX_Size() int {
	return xxx_messageInfo_SetGroupAdminToMemberReq.Size(m)
}
func (m *SetGroupAdminToMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGroupAdminToMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGroupAdminToMemberReq proto.InternalMessageInfo

func (m *SetGroupAdminToMemberReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *SetGroupAdminToMemberReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SetGroupAdminToMemberResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGroupAdminToMemberResp) Reset()         { *m = SetGroupAdminToMemberResp{} }
func (m *SetGroupAdminToMemberResp) String() string { return proto.CompactTextString(m) }
func (*SetGroupAdminToMemberResp) ProtoMessage()    {}
func (*SetGroupAdminToMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{45}
}
func (m *SetGroupAdminToMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGroupAdminToMemberResp.Unmarshal(m, b)
}
func (m *SetGroupAdminToMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGroupAdminToMemberResp.Marshal(b, m, deterministic)
}
func (dst *SetGroupAdminToMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGroupAdminToMemberResp.Merge(dst, src)
}
func (m *SetGroupAdminToMemberResp) XXX_Size() int {
	return xxx_messageInfo_SetGroupAdminToMemberResp.Size(m)
}
func (m *SetGroupAdminToMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGroupAdminToMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGroupAdminToMemberResp proto.InternalMessageInfo

type GetGuildGroupListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildGroupListReq) Reset()         { *m = GetGuildGroupListReq{} }
func (m *GetGuildGroupListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildGroupListReq) ProtoMessage()    {}
func (*GetGuildGroupListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{46}
}
func (m *GetGuildGroupListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildGroupListReq.Unmarshal(m, b)
}
func (m *GetGuildGroupListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildGroupListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildGroupListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildGroupListReq.Merge(dst, src)
}
func (m *GetGuildGroupListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildGroupListReq.Size(m)
}
func (m *GetGuildGroupListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildGroupListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildGroupListReq proto.InternalMessageInfo

func (m *GetGuildGroupListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetUserGroupListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserGroupListReq) Reset()         { *m = GetUserGroupListReq{} }
func (m *GetUserGroupListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserGroupListReq) ProtoMessage()    {}
func (*GetUserGroupListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{47}
}
func (m *GetUserGroupListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGroupListReq.Unmarshal(m, b)
}
func (m *GetUserGroupListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGroupListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserGroupListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGroupListReq.Merge(dst, src)
}
func (m *GetUserGroupListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserGroupListReq.Size(m)
}
func (m *GetUserGroupListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGroupListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGroupListReq proto.InternalMessageInfo

func (m *GetUserGroupListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserGroupListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GroupListResp struct {
	GroupList            []*GroupResp `protobuf:"bytes,1,rep,name=group_list,json=groupList,proto3" json:"group_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GroupListResp) Reset()         { *m = GroupListResp{} }
func (m *GroupListResp) String() string { return proto.CompactTextString(m) }
func (*GroupListResp) ProtoMessage()    {}
func (*GroupListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{48}
}
func (m *GroupListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupListResp.Unmarshal(m, b)
}
func (m *GroupListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupListResp.Marshal(b, m, deterministic)
}
func (dst *GroupListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupListResp.Merge(dst, src)
}
func (m *GroupListResp) XXX_Size() int {
	return xxx_messageInfo_GroupListResp.Size(m)
}
func (m *GroupListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GroupListResp proto.InternalMessageInfo

func (m *GroupListResp) GetGroupList() []*GroupResp {
	if m != nil {
		return m.GroupList
	}
	return nil
}

type DelGuildInvalidGroupReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelGuildInvalidGroupReq) Reset()         { *m = DelGuildInvalidGroupReq{} }
func (m *DelGuildInvalidGroupReq) String() string { return proto.CompactTextString(m) }
func (*DelGuildInvalidGroupReq) ProtoMessage()    {}
func (*DelGuildInvalidGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{49}
}
func (m *DelGuildInvalidGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelGuildInvalidGroupReq.Unmarshal(m, b)
}
func (m *DelGuildInvalidGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelGuildInvalidGroupReq.Marshal(b, m, deterministic)
}
func (dst *DelGuildInvalidGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelGuildInvalidGroupReq.Merge(dst, src)
}
func (m *DelGuildInvalidGroupReq) XXX_Size() int {
	return xxx_messageInfo_DelGuildInvalidGroupReq.Size(m)
}
func (m *DelGuildInvalidGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelGuildInvalidGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelGuildInvalidGroupReq proto.InternalMessageInfo

func (m *DelGuildInvalidGroupReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type DelGuildInvalidGroupResp struct {
	DelList              []uint32 `protobuf:"varint,1,rep,packed,name=del_list,json=delList,proto3" json:"del_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelGuildInvalidGroupResp) Reset()         { *m = DelGuildInvalidGroupResp{} }
func (m *DelGuildInvalidGroupResp) String() string { return proto.CompactTextString(m) }
func (*DelGuildInvalidGroupResp) ProtoMessage()    {}
func (*DelGuildInvalidGroupResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{50}
}
func (m *DelGuildInvalidGroupResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelGuildInvalidGroupResp.Unmarshal(m, b)
}
func (m *DelGuildInvalidGroupResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelGuildInvalidGroupResp.Marshal(b, m, deterministic)
}
func (dst *DelGuildInvalidGroupResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelGuildInvalidGroupResp.Merge(dst, src)
}
func (m *DelGuildInvalidGroupResp) XXX_Size() int {
	return xxx_messageInfo_DelGuildInvalidGroupResp.Size(m)
}
func (m *DelGuildInvalidGroupResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelGuildInvalidGroupResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelGuildInvalidGroupResp proto.InternalMessageInfo

func (m *DelGuildInvalidGroupResp) GetDelList() []uint32 {
	if m != nil {
		return m.DelList
	}
	return nil
}

type GroupIdListResp struct {
	GroupIdList          []uint32 `protobuf:"varint,1,rep,packed,name=group_id_list,json=groupIdList,proto3" json:"group_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupIdListResp) Reset()         { *m = GroupIdListResp{} }
func (m *GroupIdListResp) String() string { return proto.CompactTextString(m) }
func (*GroupIdListResp) ProtoMessage()    {}
func (*GroupIdListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{51}
}
func (m *GroupIdListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupIdListResp.Unmarshal(m, b)
}
func (m *GroupIdListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupIdListResp.Marshal(b, m, deterministic)
}
func (dst *GroupIdListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupIdListResp.Merge(dst, src)
}
func (m *GroupIdListResp) XXX_Size() int {
	return xxx_messageInfo_GroupIdListResp.Size(m)
}
func (m *GroupIdListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupIdListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GroupIdListResp proto.InternalMessageInfo

func (m *GroupIdListResp) GetGroupIdList() []uint32 {
	if m != nil {
		return m.GroupIdList
	}
	return nil
}

type UpdateGroupMemberMuteReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Mute                 bool     `protobuf:"varint,3,opt,name=mute,proto3" json:"mute,omitempty"`
	MuteSecond           uint32   `protobuf:"varint,4,opt,name=mute_second,json=muteSecond,proto3" json:"mute_second,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGroupMemberMuteReq) Reset()         { *m = UpdateGroupMemberMuteReq{} }
func (m *UpdateGroupMemberMuteReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupMemberMuteReq) ProtoMessage()    {}
func (*UpdateGroupMemberMuteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{52}
}
func (m *UpdateGroupMemberMuteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupMemberMuteReq.Unmarshal(m, b)
}
func (m *UpdateGroupMemberMuteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupMemberMuteReq.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupMemberMuteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupMemberMuteReq.Merge(dst, src)
}
func (m *UpdateGroupMemberMuteReq) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupMemberMuteReq.Size(m)
}
func (m *UpdateGroupMemberMuteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupMemberMuteReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupMemberMuteReq proto.InternalMessageInfo

func (m *UpdateGroupMemberMuteReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *UpdateGroupMemberMuteReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateGroupMemberMuteReq) GetMute() bool {
	if m != nil {
		return m.Mute
	}
	return false
}

func (m *UpdateGroupMemberMuteReq) GetMuteSecond() uint32 {
	if m != nil {
		return m.MuteSecond
	}
	return 0
}

type UpdateGroupMemberMuteResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGroupMemberMuteResp) Reset()         { *m = UpdateGroupMemberMuteResp{} }
func (m *UpdateGroupMemberMuteResp) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupMemberMuteResp) ProtoMessage()    {}
func (*UpdateGroupMemberMuteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{53}
}
func (m *UpdateGroupMemberMuteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupMemberMuteResp.Unmarshal(m, b)
}
func (m *UpdateGroupMemberMuteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupMemberMuteResp.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupMemberMuteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupMemberMuteResp.Merge(dst, src)
}
func (m *UpdateGroupMemberMuteResp) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupMemberMuteResp.Size(m)
}
func (m *UpdateGroupMemberMuteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupMemberMuteResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupMemberMuteResp proto.InternalMessageInfo

type UpdateGroupMemberCardReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Card                 string   `protobuf:"bytes,3,opt,name=card,proto3" json:"card,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGroupMemberCardReq) Reset()         { *m = UpdateGroupMemberCardReq{} }
func (m *UpdateGroupMemberCardReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupMemberCardReq) ProtoMessage()    {}
func (*UpdateGroupMemberCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{54}
}
func (m *UpdateGroupMemberCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupMemberCardReq.Unmarshal(m, b)
}
func (m *UpdateGroupMemberCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupMemberCardReq.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupMemberCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupMemberCardReq.Merge(dst, src)
}
func (m *UpdateGroupMemberCardReq) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupMemberCardReq.Size(m)
}
func (m *UpdateGroupMemberCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupMemberCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupMemberCardReq proto.InternalMessageInfo

func (m *UpdateGroupMemberCardReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *UpdateGroupMemberCardReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateGroupMemberCardReq) GetCard() string {
	if m != nil {
		return m.Card
	}
	return ""
}

type UpdateGroupMemberCardResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGroupMemberCardResp) Reset()         { *m = UpdateGroupMemberCardResp{} }
func (m *UpdateGroupMemberCardResp) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupMemberCardResp) ProtoMessage()    {}
func (*UpdateGroupMemberCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{55}
}
func (m *UpdateGroupMemberCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupMemberCardResp.Unmarshal(m, b)
}
func (m *UpdateGroupMemberCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupMemberCardResp.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupMemberCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupMemberCardResp.Merge(dst, src)
}
func (m *UpdateGroupMemberCardResp) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupMemberCardResp.Size(m)
}
func (m *UpdateGroupMemberCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupMemberCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupMemberCardResp proto.InternalMessageInfo

type UpdateGroupNeedVerifyReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	NeedVerify           uint32   `protobuf:"varint,2,opt,name=need_verify,json=needVerify,proto3" json:"need_verify,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGroupNeedVerifyReq) Reset()         { *m = UpdateGroupNeedVerifyReq{} }
func (m *UpdateGroupNeedVerifyReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupNeedVerifyReq) ProtoMessage()    {}
func (*UpdateGroupNeedVerifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{56}
}
func (m *UpdateGroupNeedVerifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupNeedVerifyReq.Unmarshal(m, b)
}
func (m *UpdateGroupNeedVerifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupNeedVerifyReq.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupNeedVerifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupNeedVerifyReq.Merge(dst, src)
}
func (m *UpdateGroupNeedVerifyReq) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupNeedVerifyReq.Size(m)
}
func (m *UpdateGroupNeedVerifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupNeedVerifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupNeedVerifyReq proto.InternalMessageInfo

func (m *UpdateGroupNeedVerifyReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *UpdateGroupNeedVerifyReq) GetNeedVerify() uint32 {
	if m != nil {
		return m.NeedVerify
	}
	return 0
}

type UpdateGroupNeedVerifyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGroupNeedVerifyResp) Reset()         { *m = UpdateGroupNeedVerifyResp{} }
func (m *UpdateGroupNeedVerifyResp) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupNeedVerifyResp) ProtoMessage()    {}
func (*UpdateGroupNeedVerifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{57}
}
func (m *UpdateGroupNeedVerifyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupNeedVerifyResp.Unmarshal(m, b)
}
func (m *UpdateGroupNeedVerifyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupNeedVerifyResp.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupNeedVerifyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupNeedVerifyResp.Merge(dst, src)
}
func (m *UpdateGroupNeedVerifyResp) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupNeedVerifyResp.Size(m)
}
func (m *UpdateGroupNeedVerifyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupNeedVerifyResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupNeedVerifyResp proto.InternalMessageInfo

type UpdateGroupAllMuteReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	AllMute              bool     `protobuf:"varint,2,opt,name=all_mute,json=allMute,proto3" json:"all_mute,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGroupAllMuteReq) Reset()         { *m = UpdateGroupAllMuteReq{} }
func (m *UpdateGroupAllMuteReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupAllMuteReq) ProtoMessage()    {}
func (*UpdateGroupAllMuteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{58}
}
func (m *UpdateGroupAllMuteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupAllMuteReq.Unmarshal(m, b)
}
func (m *UpdateGroupAllMuteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupAllMuteReq.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupAllMuteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupAllMuteReq.Merge(dst, src)
}
func (m *UpdateGroupAllMuteReq) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupAllMuteReq.Size(m)
}
func (m *UpdateGroupAllMuteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupAllMuteReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupAllMuteReq proto.InternalMessageInfo

func (m *UpdateGroupAllMuteReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *UpdateGroupAllMuteReq) GetAllMute() bool {
	if m != nil {
		return m.AllMute
	}
	return false
}

type UpdateGroupAllMuteResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGroupAllMuteResp) Reset()         { *m = UpdateGroupAllMuteResp{} }
func (m *UpdateGroupAllMuteResp) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupAllMuteResp) ProtoMessage()    {}
func (*UpdateGroupAllMuteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{59}
}
func (m *UpdateGroupAllMuteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupAllMuteResp.Unmarshal(m, b)
}
func (m *UpdateGroupAllMuteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupAllMuteResp.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupAllMuteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupAllMuteResp.Merge(dst, src)
}
func (m *UpdateGroupAllMuteResp) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupAllMuteResp.Size(m)
}
func (m *UpdateGroupAllMuteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupAllMuteResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupAllMuteResp proto.InternalMessageInfo

type DismissGroupReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DismissGroupReq) Reset()         { *m = DismissGroupReq{} }
func (m *DismissGroupReq) String() string { return proto.CompactTextString(m) }
func (*DismissGroupReq) ProtoMessage()    {}
func (*DismissGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{60}
}
func (m *DismissGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DismissGroupReq.Unmarshal(m, b)
}
func (m *DismissGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DismissGroupReq.Marshal(b, m, deterministic)
}
func (dst *DismissGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DismissGroupReq.Merge(dst, src)
}
func (m *DismissGroupReq) XXX_Size() int {
	return xxx_messageInfo_DismissGroupReq.Size(m)
}
func (m *DismissGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DismissGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_DismissGroupReq proto.InternalMessageInfo

func (m *DismissGroupReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *DismissGroupReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type DismissGroupResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DismissGroupResp) Reset()         { *m = DismissGroupResp{} }
func (m *DismissGroupResp) String() string { return proto.CompactTextString(m) }
func (*DismissGroupResp) ProtoMessage()    {}
func (*DismissGroupResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{61}
}
func (m *DismissGroupResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DismissGroupResp.Unmarshal(m, b)
}
func (m *DismissGroupResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DismissGroupResp.Marshal(b, m, deterministic)
}
func (dst *DismissGroupResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DismissGroupResp.Merge(dst, src)
}
func (m *DismissGroupResp) XXX_Size() int {
	return xxx_messageInfo_DismissGroupResp.Size(m)
}
func (m *DismissGroupResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DismissGroupResp.DiscardUnknown(m)
}

var xxx_messageInfo_DismissGroupResp proto.InternalMessageInfo

type SetGroupNotRecvMsgReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	RecvMsgOpt           uint32   `protobuf:"varint,2,opt,name=recv_msg_opt,json=recvMsgOpt,proto3" json:"recv_msg_opt,omitempty"`
	GuildId              uint32   `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGroupNotRecvMsgReq) Reset()         { *m = SetGroupNotRecvMsgReq{} }
func (m *SetGroupNotRecvMsgReq) String() string { return proto.CompactTextString(m) }
func (*SetGroupNotRecvMsgReq) ProtoMessage()    {}
func (*SetGroupNotRecvMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{62}
}
func (m *SetGroupNotRecvMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGroupNotRecvMsgReq.Unmarshal(m, b)
}
func (m *SetGroupNotRecvMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGroupNotRecvMsgReq.Marshal(b, m, deterministic)
}
func (dst *SetGroupNotRecvMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGroupNotRecvMsgReq.Merge(dst, src)
}
func (m *SetGroupNotRecvMsgReq) XXX_Size() int {
	return xxx_messageInfo_SetGroupNotRecvMsgReq.Size(m)
}
func (m *SetGroupNotRecvMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGroupNotRecvMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGroupNotRecvMsgReq proto.InternalMessageInfo

func (m *SetGroupNotRecvMsgReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *SetGroupNotRecvMsgReq) GetRecvMsgOpt() uint32 {
	if m != nil {
		return m.RecvMsgOpt
	}
	return 0
}

func (m *SetGroupNotRecvMsgReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SetGroupNotRecvMsgReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SetGroupNotRecvMsgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGroupNotRecvMsgResp) Reset()         { *m = SetGroupNotRecvMsgResp{} }
func (m *SetGroupNotRecvMsgResp) String() string { return proto.CompactTextString(m) }
func (*SetGroupNotRecvMsgResp) ProtoMessage()    {}
func (*SetGroupNotRecvMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{63}
}
func (m *SetGroupNotRecvMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGroupNotRecvMsgResp.Unmarshal(m, b)
}
func (m *SetGroupNotRecvMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGroupNotRecvMsgResp.Marshal(b, m, deterministic)
}
func (dst *SetGroupNotRecvMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGroupNotRecvMsgResp.Merge(dst, src)
}
func (m *SetGroupNotRecvMsgResp) XXX_Size() int {
	return xxx_messageInfo_SetGroupNotRecvMsgResp.Size(m)
}
func (m *SetGroupNotRecvMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGroupNotRecvMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGroupNotRecvMsgResp proto.InternalMessageInfo

type GuildModifyGameGroupOrderReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GameGroupId          []uint32 `protobuf:"varint,2,rep,packed,name=game_group_id,json=gameGroupId,proto3" json:"game_group_id,omitempty"`
	Order                []uint32 `protobuf:"varint,3,rep,packed,name=order,proto3" json:"order,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildModifyGameGroupOrderReq) Reset()         { *m = GuildModifyGameGroupOrderReq{} }
func (m *GuildModifyGameGroupOrderReq) String() string { return proto.CompactTextString(m) }
func (*GuildModifyGameGroupOrderReq) ProtoMessage()    {}
func (*GuildModifyGameGroupOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{64}
}
func (m *GuildModifyGameGroupOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildModifyGameGroupOrderReq.Unmarshal(m, b)
}
func (m *GuildModifyGameGroupOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildModifyGameGroupOrderReq.Marshal(b, m, deterministic)
}
func (dst *GuildModifyGameGroupOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildModifyGameGroupOrderReq.Merge(dst, src)
}
func (m *GuildModifyGameGroupOrderReq) XXX_Size() int {
	return xxx_messageInfo_GuildModifyGameGroupOrderReq.Size(m)
}
func (m *GuildModifyGameGroupOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildModifyGameGroupOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildModifyGameGroupOrderReq proto.InternalMessageInfo

func (m *GuildModifyGameGroupOrderReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildModifyGameGroupOrderReq) GetGameGroupId() []uint32 {
	if m != nil {
		return m.GameGroupId
	}
	return nil
}

func (m *GuildModifyGameGroupOrderReq) GetOrder() []uint32 {
	if m != nil {
		return m.Order
	}
	return nil
}

type GuildModifyGameGroupOrderResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildModifyGameGroupOrderResp) Reset()         { *m = GuildModifyGameGroupOrderResp{} }
func (m *GuildModifyGameGroupOrderResp) String() string { return proto.CompactTextString(m) }
func (*GuildModifyGameGroupOrderResp) ProtoMessage()    {}
func (*GuildModifyGameGroupOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{65}
}
func (m *GuildModifyGameGroupOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildModifyGameGroupOrderResp.Unmarshal(m, b)
}
func (m *GuildModifyGameGroupOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildModifyGameGroupOrderResp.Marshal(b, m, deterministic)
}
func (dst *GuildModifyGameGroupOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildModifyGameGroupOrderResp.Merge(dst, src)
}
func (m *GuildModifyGameGroupOrderResp) XXX_Size() int {
	return xxx_messageInfo_GuildModifyGameGroupOrderResp.Size(m)
}
func (m *GuildModifyGameGroupOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildModifyGameGroupOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildModifyGameGroupOrderResp proto.InternalMessageInfo

type GuildGetGroupOrderReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildGetGroupOrderReq) Reset()         { *m = GuildGetGroupOrderReq{} }
func (m *GuildGetGroupOrderReq) String() string { return proto.CompactTextString(m) }
func (*GuildGetGroupOrderReq) ProtoMessage()    {}
func (*GuildGetGroupOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{66}
}
func (m *GuildGetGroupOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildGetGroupOrderReq.Unmarshal(m, b)
}
func (m *GuildGetGroupOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildGetGroupOrderReq.Marshal(b, m, deterministic)
}
func (dst *GuildGetGroupOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildGetGroupOrderReq.Merge(dst, src)
}
func (m *GuildGetGroupOrderReq) XXX_Size() int {
	return xxx_messageInfo_GuildGetGroupOrderReq.Size(m)
}
func (m *GuildGetGroupOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildGetGroupOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildGetGroupOrderReq proto.InternalMessageInfo

func (m *GuildGetGroupOrderReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GroupOrder struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupOrder) Reset()         { *m = GroupOrder{} }
func (m *GroupOrder) String() string { return proto.CompactTextString(m) }
func (*GroupOrder) ProtoMessage()    {}
func (*GroupOrder) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{67}
}
func (m *GroupOrder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupOrder.Unmarshal(m, b)
}
func (m *GroupOrder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupOrder.Marshal(b, m, deterministic)
}
func (dst *GroupOrder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupOrder.Merge(dst, src)
}
func (m *GroupOrder) XXX_Size() int {
	return xxx_messageInfo_GroupOrder.Size(m)
}
func (m *GroupOrder) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupOrder.DiscardUnknown(m)
}

var xxx_messageInfo_GroupOrder proto.InternalMessageInfo

func (m *GroupOrder) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupOrder) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GuildGetGroupOrderResp struct {
	GroupList            []*GroupOrder `protobuf:"bytes,1,rep,name=group_list,json=groupList,proto3" json:"group_list,omitempty"`
	IsSetOrder           bool          `protobuf:"varint,2,opt,name=is_set_order,json=isSetOrder,proto3" json:"is_set_order,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GuildGetGroupOrderResp) Reset()         { *m = GuildGetGroupOrderResp{} }
func (m *GuildGetGroupOrderResp) String() string { return proto.CompactTextString(m) }
func (*GuildGetGroupOrderResp) ProtoMessage()    {}
func (*GuildGetGroupOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{68}
}
func (m *GuildGetGroupOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildGetGroupOrderResp.Unmarshal(m, b)
}
func (m *GuildGetGroupOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildGetGroupOrderResp.Marshal(b, m, deterministic)
}
func (dst *GuildGetGroupOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildGetGroupOrderResp.Merge(dst, src)
}
func (m *GuildGetGroupOrderResp) XXX_Size() int {
	return xxx_messageInfo_GuildGetGroupOrderResp.Size(m)
}
func (m *GuildGetGroupOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildGetGroupOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildGetGroupOrderResp proto.InternalMessageInfo

func (m *GuildGetGroupOrderResp) GetGroupList() []*GroupOrder {
	if m != nil {
		return m.GroupList
	}
	return nil
}

func (m *GuildGetGroupOrderResp) GetIsSetOrder() bool {
	if m != nil {
		return m.IsSetOrder
	}
	return false
}

type GroupCheckUidNeedVerifyReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupCheckUidNeedVerifyReq) Reset()         { *m = GroupCheckUidNeedVerifyReq{} }
func (m *GroupCheckUidNeedVerifyReq) String() string { return proto.CompactTextString(m) }
func (*GroupCheckUidNeedVerifyReq) ProtoMessage()    {}
func (*GroupCheckUidNeedVerifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{69}
}
func (m *GroupCheckUidNeedVerifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupCheckUidNeedVerifyReq.Unmarshal(m, b)
}
func (m *GroupCheckUidNeedVerifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupCheckUidNeedVerifyReq.Marshal(b, m, deterministic)
}
func (dst *GroupCheckUidNeedVerifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupCheckUidNeedVerifyReq.Merge(dst, src)
}
func (m *GroupCheckUidNeedVerifyReq) XXX_Size() int {
	return xxx_messageInfo_GroupCheckUidNeedVerifyReq.Size(m)
}
func (m *GroupCheckUidNeedVerifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupCheckUidNeedVerifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GroupCheckUidNeedVerifyReq proto.InternalMessageInfo

func (m *GroupCheckUidNeedVerifyReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupCheckUidNeedVerifyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GroupCheckUidNeedVerifyResp struct {
	IsNeedVerify         bool     `protobuf:"varint,1,opt,name=is_need_verify,json=isNeedVerify,proto3" json:"is_need_verify,omitempty"`
	KickedCount          uint32   `protobuf:"varint,2,opt,name=kicked_count,json=kickedCount,proto3" json:"kicked_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupCheckUidNeedVerifyResp) Reset()         { *m = GroupCheckUidNeedVerifyResp{} }
func (m *GroupCheckUidNeedVerifyResp) String() string { return proto.CompactTextString(m) }
func (*GroupCheckUidNeedVerifyResp) ProtoMessage()    {}
func (*GroupCheckUidNeedVerifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{70}
}
func (m *GroupCheckUidNeedVerifyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupCheckUidNeedVerifyResp.Unmarshal(m, b)
}
func (m *GroupCheckUidNeedVerifyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupCheckUidNeedVerifyResp.Marshal(b, m, deterministic)
}
func (dst *GroupCheckUidNeedVerifyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupCheckUidNeedVerifyResp.Merge(dst, src)
}
func (m *GroupCheckUidNeedVerifyResp) XXX_Size() int {
	return xxx_messageInfo_GroupCheckUidNeedVerifyResp.Size(m)
}
func (m *GroupCheckUidNeedVerifyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupCheckUidNeedVerifyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GroupCheckUidNeedVerifyResp proto.InternalMessageInfo

func (m *GroupCheckUidNeedVerifyResp) GetIsNeedVerify() bool {
	if m != nil {
		return m.IsNeedVerify
	}
	return false
}

func (m *GroupCheckUidNeedVerifyResp) GetKickedCount() uint32 {
	if m != nil {
		return m.KickedCount
	}
	return 0
}

type GroupSetUidNeedVerifyReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupSetUidNeedVerifyReq) Reset()         { *m = GroupSetUidNeedVerifyReq{} }
func (m *GroupSetUidNeedVerifyReq) String() string { return proto.CompactTextString(m) }
func (*GroupSetUidNeedVerifyReq) ProtoMessage()    {}
func (*GroupSetUidNeedVerifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{71}
}
func (m *GroupSetUidNeedVerifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupSetUidNeedVerifyReq.Unmarshal(m, b)
}
func (m *GroupSetUidNeedVerifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupSetUidNeedVerifyReq.Marshal(b, m, deterministic)
}
func (dst *GroupSetUidNeedVerifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupSetUidNeedVerifyReq.Merge(dst, src)
}
func (m *GroupSetUidNeedVerifyReq) XXX_Size() int {
	return xxx_messageInfo_GroupSetUidNeedVerifyReq.Size(m)
}
func (m *GroupSetUidNeedVerifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupSetUidNeedVerifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GroupSetUidNeedVerifyReq proto.InternalMessageInfo

func (m *GroupSetUidNeedVerifyReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupSetUidNeedVerifyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GroupSetUidNeedVerifyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupSetUidNeedVerifyResp) Reset()         { *m = GroupSetUidNeedVerifyResp{} }
func (m *GroupSetUidNeedVerifyResp) String() string { return proto.CompactTextString(m) }
func (*GroupSetUidNeedVerifyResp) ProtoMessage()    {}
func (*GroupSetUidNeedVerifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{72}
}
func (m *GroupSetUidNeedVerifyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupSetUidNeedVerifyResp.Unmarshal(m, b)
}
func (m *GroupSetUidNeedVerifyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupSetUidNeedVerifyResp.Marshal(b, m, deterministic)
}
func (dst *GroupSetUidNeedVerifyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupSetUidNeedVerifyResp.Merge(dst, src)
}
func (m *GroupSetUidNeedVerifyResp) XXX_Size() int {
	return xxx_messageInfo_GroupSetUidNeedVerifyResp.Size(m)
}
func (m *GroupSetUidNeedVerifyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupSetUidNeedVerifyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GroupSetUidNeedVerifyResp proto.InternalMessageInfo

// 查用户当前创建了多少个TGroup
type TGroupGetUserCreateGroupCountReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TGroupGetUserCreateGroupCountReq) Reset()         { *m = TGroupGetUserCreateGroupCountReq{} }
func (m *TGroupGetUserCreateGroupCountReq) String() string { return proto.CompactTextString(m) }
func (*TGroupGetUserCreateGroupCountReq) ProtoMessage()    {}
func (*TGroupGetUserCreateGroupCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{73}
}
func (m *TGroupGetUserCreateGroupCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TGroupGetUserCreateGroupCountReq.Unmarshal(m, b)
}
func (m *TGroupGetUserCreateGroupCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TGroupGetUserCreateGroupCountReq.Marshal(b, m, deterministic)
}
func (dst *TGroupGetUserCreateGroupCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TGroupGetUserCreateGroupCountReq.Merge(dst, src)
}
func (m *TGroupGetUserCreateGroupCountReq) XXX_Size() int {
	return xxx_messageInfo_TGroupGetUserCreateGroupCountReq.Size(m)
}
func (m *TGroupGetUserCreateGroupCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TGroupGetUserCreateGroupCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_TGroupGetUserCreateGroupCountReq proto.InternalMessageInfo

func (m *TGroupGetUserCreateGroupCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type TGroupGetUserCreateGroupCountResp struct {
	GroupCount           uint32   `protobuf:"varint,1,opt,name=group_count,json=groupCount,proto3" json:"group_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TGroupGetUserCreateGroupCountResp) Reset()         { *m = TGroupGetUserCreateGroupCountResp{} }
func (m *TGroupGetUserCreateGroupCountResp) String() string { return proto.CompactTextString(m) }
func (*TGroupGetUserCreateGroupCountResp) ProtoMessage()    {}
func (*TGroupGetUserCreateGroupCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{74}
}
func (m *TGroupGetUserCreateGroupCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TGroupGetUserCreateGroupCountResp.Unmarshal(m, b)
}
func (m *TGroupGetUserCreateGroupCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TGroupGetUserCreateGroupCountResp.Marshal(b, m, deterministic)
}
func (dst *TGroupGetUserCreateGroupCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TGroupGetUserCreateGroupCountResp.Merge(dst, src)
}
func (m *TGroupGetUserCreateGroupCountResp) XXX_Size() int {
	return xxx_messageInfo_TGroupGetUserCreateGroupCountResp.Size(m)
}
func (m *TGroupGetUserCreateGroupCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TGroupGetUserCreateGroupCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_TGroupGetUserCreateGroupCountResp proto.InternalMessageInfo

func (m *TGroupGetUserCreateGroupCountResp) GetGroupCount() uint32 {
	if m != nil {
		return m.GroupCount
	}
	return 0
}

// 修改T群的描述 地理位置 游戏ID 等级
type ModifyTGroupDescReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	GroupDesc            string   `protobuf:"bytes,2,opt,name=group_desc,json=groupDesc,proto3" json:"group_desc,omitempty"`
	CityCode             string   `protobuf:"bytes,3,opt,name=city_code,json=cityCode,proto3" json:"city_code,omitempty"`
	CityName             string   `protobuf:"bytes,4,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	GameId               uint32   `protobuf:"varint,5,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Level                uint32   `protobuf:"varint,6,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyTGroupDescReq) Reset()         { *m = ModifyTGroupDescReq{} }
func (m *ModifyTGroupDescReq) String() string { return proto.CompactTextString(m) }
func (*ModifyTGroupDescReq) ProtoMessage()    {}
func (*ModifyTGroupDescReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{75}
}
func (m *ModifyTGroupDescReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyTGroupDescReq.Unmarshal(m, b)
}
func (m *ModifyTGroupDescReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyTGroupDescReq.Marshal(b, m, deterministic)
}
func (dst *ModifyTGroupDescReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyTGroupDescReq.Merge(dst, src)
}
func (m *ModifyTGroupDescReq) XXX_Size() int {
	return xxx_messageInfo_ModifyTGroupDescReq.Size(m)
}
func (m *ModifyTGroupDescReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyTGroupDescReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyTGroupDescReq proto.InternalMessageInfo

func (m *ModifyTGroupDescReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *ModifyTGroupDescReq) GetGroupDesc() string {
	if m != nil {
		return m.GroupDesc
	}
	return ""
}

func (m *ModifyTGroupDescReq) GetCityCode() string {
	if m != nil {
		return m.CityCode
	}
	return ""
}

func (m *ModifyTGroupDescReq) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *ModifyTGroupDescReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ModifyTGroupDescReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type ModifyTGroupDescResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyTGroupDescResp) Reset()         { *m = ModifyTGroupDescResp{} }
func (m *ModifyTGroupDescResp) String() string { return proto.CompactTextString(m) }
func (*ModifyTGroupDescResp) ProtoMessage()    {}
func (*ModifyTGroupDescResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{76}
}
func (m *ModifyTGroupDescResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyTGroupDescResp.Unmarshal(m, b)
}
func (m *ModifyTGroupDescResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyTGroupDescResp.Marshal(b, m, deterministic)
}
func (dst *ModifyTGroupDescResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyTGroupDescResp.Merge(dst, src)
}
func (m *ModifyTGroupDescResp) XXX_Size() int {
	return xxx_messageInfo_ModifyTGroupDescResp.Size(m)
}
func (m *ModifyTGroupDescResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyTGroupDescResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyTGroupDescResp proto.InternalMessageInfo

type GetTGroupByDisplayIdReq struct {
	TgroupDisplayId      uint32   `protobuf:"varint,1,opt,name=tgroup_display_id,json=tgroupDisplayId,proto3" json:"tgroup_display_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTGroupByDisplayIdReq) Reset()         { *m = GetTGroupByDisplayIdReq{} }
func (m *GetTGroupByDisplayIdReq) String() string { return proto.CompactTextString(m) }
func (*GetTGroupByDisplayIdReq) ProtoMessage()    {}
func (*GetTGroupByDisplayIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{77}
}
func (m *GetTGroupByDisplayIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTGroupByDisplayIdReq.Unmarshal(m, b)
}
func (m *GetTGroupByDisplayIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTGroupByDisplayIdReq.Marshal(b, m, deterministic)
}
func (dst *GetTGroupByDisplayIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTGroupByDisplayIdReq.Merge(dst, src)
}
func (m *GetTGroupByDisplayIdReq) XXX_Size() int {
	return xxx_messageInfo_GetTGroupByDisplayIdReq.Size(m)
}
func (m *GetTGroupByDisplayIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTGroupByDisplayIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTGroupByDisplayIdReq proto.InternalMessageInfo

func (m *GetTGroupByDisplayIdReq) GetTgroupDisplayId() uint32 {
	if m != nil {
		return m.TgroupDisplayId
	}
	return 0
}

type GetTGroupByDisplayIdResp struct {
	GroupResp            *GroupResp `protobuf:"bytes,1,opt,name=groupResp,proto3" json:"groupResp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetTGroupByDisplayIdResp) Reset()         { *m = GetTGroupByDisplayIdResp{} }
func (m *GetTGroupByDisplayIdResp) String() string { return proto.CompactTextString(m) }
func (*GetTGroupByDisplayIdResp) ProtoMessage()    {}
func (*GetTGroupByDisplayIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{78}
}
func (m *GetTGroupByDisplayIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTGroupByDisplayIdResp.Unmarshal(m, b)
}
func (m *GetTGroupByDisplayIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTGroupByDisplayIdResp.Marshal(b, m, deterministic)
}
func (dst *GetTGroupByDisplayIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTGroupByDisplayIdResp.Merge(dst, src)
}
func (m *GetTGroupByDisplayIdResp) XXX_Size() int {
	return xxx_messageInfo_GetTGroupByDisplayIdResp.Size(m)
}
func (m *GetTGroupByDisplayIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTGroupByDisplayIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTGroupByDisplayIdResp proto.InternalMessageInfo

func (m *GetTGroupByDisplayIdResp) GetGroupResp() *GroupResp {
	if m != nil {
		return m.GroupResp
	}
	return nil
}

// 获取用户所在的群ID列表
type GetUserGroupIDListByGuildIDReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildID              uint32   `protobuf:"varint,2,opt,name=guildID,proto3" json:"guildID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserGroupIDListByGuildIDReq) Reset()         { *m = GetUserGroupIDListByGuildIDReq{} }
func (m *GetUserGroupIDListByGuildIDReq) String() string { return proto.CompactTextString(m) }
func (*GetUserGroupIDListByGuildIDReq) ProtoMessage()    {}
func (*GetUserGroupIDListByGuildIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{79}
}
func (m *GetUserGroupIDListByGuildIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGroupIDListByGuildIDReq.Unmarshal(m, b)
}
func (m *GetUserGroupIDListByGuildIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGroupIDListByGuildIDReq.Marshal(b, m, deterministic)
}
func (dst *GetUserGroupIDListByGuildIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGroupIDListByGuildIDReq.Merge(dst, src)
}
func (m *GetUserGroupIDListByGuildIDReq) XXX_Size() int {
	return xxx_messageInfo_GetUserGroupIDListByGuildIDReq.Size(m)
}
func (m *GetUserGroupIDListByGuildIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGroupIDListByGuildIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGroupIDListByGuildIDReq proto.InternalMessageInfo

func (m *GetUserGroupIDListByGuildIDReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserGroupIDListByGuildIDReq) GetGuildID() uint32 {
	if m != nil {
		return m.GuildID
	}
	return 0
}

type GetUserGroupIDListByGuildIDResp struct {
	GroupidList          []uint32 `protobuf:"varint,1,rep,packed,name=groupid_list,json=groupidList,proto3" json:"groupid_list,omitempty"`
	NotRecvGroups        []uint32 `protobuf:"varint,2,rep,packed,name=not_recv_groups,json=notRecvGroups,proto3" json:"not_recv_groups,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserGroupIDListByGuildIDResp) Reset()         { *m = GetUserGroupIDListByGuildIDResp{} }
func (m *GetUserGroupIDListByGuildIDResp) String() string { return proto.CompactTextString(m) }
func (*GetUserGroupIDListByGuildIDResp) ProtoMessage()    {}
func (*GetUserGroupIDListByGuildIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{80}
}
func (m *GetUserGroupIDListByGuildIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGroupIDListByGuildIDResp.Unmarshal(m, b)
}
func (m *GetUserGroupIDListByGuildIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGroupIDListByGuildIDResp.Marshal(b, m, deterministic)
}
func (dst *GetUserGroupIDListByGuildIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGroupIDListByGuildIDResp.Merge(dst, src)
}
func (m *GetUserGroupIDListByGuildIDResp) XXX_Size() int {
	return xxx_messageInfo_GetUserGroupIDListByGuildIDResp.Size(m)
}
func (m *GetUserGroupIDListByGuildIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGroupIDListByGuildIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGroupIDListByGuildIDResp proto.InternalMessageInfo

func (m *GetUserGroupIDListByGuildIDResp) GetGroupidList() []uint32 {
	if m != nil {
		return m.GroupidList
	}
	return nil
}

func (m *GetUserGroupIDListByGuildIDResp) GetNotRecvGroups() []uint32 {
	if m != nil {
		return m.NotRecvGroups
	}
	return nil
}

type TGroupSearchByGameIdReq struct {
	GameId               []uint32 `protobuf:"varint,1,rep,packed,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	CityCode             string   `protobuf:"bytes,2,opt,name=city_code,json=cityCode,proto3" json:"city_code,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	IsActive             uint32   `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TGroupSearchByGameIdReq) Reset()         { *m = TGroupSearchByGameIdReq{} }
func (m *TGroupSearchByGameIdReq) String() string { return proto.CompactTextString(m) }
func (*TGroupSearchByGameIdReq) ProtoMessage()    {}
func (*TGroupSearchByGameIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{81}
}
func (m *TGroupSearchByGameIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TGroupSearchByGameIdReq.Unmarshal(m, b)
}
func (m *TGroupSearchByGameIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TGroupSearchByGameIdReq.Marshal(b, m, deterministic)
}
func (dst *TGroupSearchByGameIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TGroupSearchByGameIdReq.Merge(dst, src)
}
func (m *TGroupSearchByGameIdReq) XXX_Size() int {
	return xxx_messageInfo_TGroupSearchByGameIdReq.Size(m)
}
func (m *TGroupSearchByGameIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TGroupSearchByGameIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_TGroupSearchByGameIdReq proto.InternalMessageInfo

func (m *TGroupSearchByGameIdReq) GetGameId() []uint32 {
	if m != nil {
		return m.GameId
	}
	return nil
}

func (m *TGroupSearchByGameIdReq) GetCityCode() string {
	if m != nil {
		return m.CityCode
	}
	return ""
}

func (m *TGroupSearchByGameIdReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *TGroupSearchByGameIdReq) GetIsActive() uint32 {
	if m != nil {
		return m.IsActive
	}
	return 0
}

type TGroupGameGroupIdList struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GroupIdList          []uint32 `protobuf:"varint,2,rep,packed,name=group_id_list,json=groupIdList,proto3" json:"group_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TGroupGameGroupIdList) Reset()         { *m = TGroupGameGroupIdList{} }
func (m *TGroupGameGroupIdList) String() string { return proto.CompactTextString(m) }
func (*TGroupGameGroupIdList) ProtoMessage()    {}
func (*TGroupGameGroupIdList) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{82}
}
func (m *TGroupGameGroupIdList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TGroupGameGroupIdList.Unmarshal(m, b)
}
func (m *TGroupGameGroupIdList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TGroupGameGroupIdList.Marshal(b, m, deterministic)
}
func (dst *TGroupGameGroupIdList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TGroupGameGroupIdList.Merge(dst, src)
}
func (m *TGroupGameGroupIdList) XXX_Size() int {
	return xxx_messageInfo_TGroupGameGroupIdList.Size(m)
}
func (m *TGroupGameGroupIdList) XXX_DiscardUnknown() {
	xxx_messageInfo_TGroupGameGroupIdList.DiscardUnknown(m)
}

var xxx_messageInfo_TGroupGameGroupIdList proto.InternalMessageInfo

func (m *TGroupGameGroupIdList) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *TGroupGameGroupIdList) GetGroupIdList() []uint32 {
	if m != nil {
		return m.GroupIdList
	}
	return nil
}

type TGroupSearchByGameIdResp struct {
	GroupList            []*TGroupGameGroupIdList `protobuf:"bytes,1,rep,name=group_list,json=groupList,proto3" json:"group_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *TGroupSearchByGameIdResp) Reset()         { *m = TGroupSearchByGameIdResp{} }
func (m *TGroupSearchByGameIdResp) String() string { return proto.CompactTextString(m) }
func (*TGroupSearchByGameIdResp) ProtoMessage()    {}
func (*TGroupSearchByGameIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{83}
}
func (m *TGroupSearchByGameIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TGroupSearchByGameIdResp.Unmarshal(m, b)
}
func (m *TGroupSearchByGameIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TGroupSearchByGameIdResp.Marshal(b, m, deterministic)
}
func (dst *TGroupSearchByGameIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TGroupSearchByGameIdResp.Merge(dst, src)
}
func (m *TGroupSearchByGameIdResp) XXX_Size() int {
	return xxx_messageInfo_TGroupSearchByGameIdResp.Size(m)
}
func (m *TGroupSearchByGameIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TGroupSearchByGameIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_TGroupSearchByGameIdResp proto.InternalMessageInfo

func (m *TGroupSearchByGameIdResp) GetGroupList() []*TGroupGameGroupIdList {
	if m != nil {
		return m.GroupList
	}
	return nil
}

type TGroupGetUserJoinGroupListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TGroupGetUserJoinGroupListReq) Reset()         { *m = TGroupGetUserJoinGroupListReq{} }
func (m *TGroupGetUserJoinGroupListReq) String() string { return proto.CompactTextString(m) }
func (*TGroupGetUserJoinGroupListReq) ProtoMessage()    {}
func (*TGroupGetUserJoinGroupListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{84}
}
func (m *TGroupGetUserJoinGroupListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TGroupGetUserJoinGroupListReq.Unmarshal(m, b)
}
func (m *TGroupGetUserJoinGroupListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TGroupGetUserJoinGroupListReq.Marshal(b, m, deterministic)
}
func (dst *TGroupGetUserJoinGroupListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TGroupGetUserJoinGroupListReq.Merge(dst, src)
}
func (m *TGroupGetUserJoinGroupListReq) XXX_Size() int {
	return xxx_messageInfo_TGroupGetUserJoinGroupListReq.Size(m)
}
func (m *TGroupGetUserJoinGroupListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TGroupGetUserJoinGroupListReq.DiscardUnknown(m)
}

var xxx_messageInfo_TGroupGetUserJoinGroupListReq proto.InternalMessageInfo

func (m *TGroupGetUserJoinGroupListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type TGroupGetUserJoinGroupListResp struct {
	CreateGroupIdList    []uint32 `protobuf:"varint,1,rep,packed,name=create_group_id_list,json=createGroupIdList,proto3" json:"create_group_id_list,omitempty"`
	JoinGroupIdList      []uint32 `protobuf:"varint,2,rep,packed,name=join_group_id_list,json=joinGroupIdList,proto3" json:"join_group_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TGroupGetUserJoinGroupListResp) Reset()         { *m = TGroupGetUserJoinGroupListResp{} }
func (m *TGroupGetUserJoinGroupListResp) String() string { return proto.CompactTextString(m) }
func (*TGroupGetUserJoinGroupListResp) ProtoMessage()    {}
func (*TGroupGetUserJoinGroupListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{85}
}
func (m *TGroupGetUserJoinGroupListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TGroupGetUserJoinGroupListResp.Unmarshal(m, b)
}
func (m *TGroupGetUserJoinGroupListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TGroupGetUserJoinGroupListResp.Marshal(b, m, deterministic)
}
func (dst *TGroupGetUserJoinGroupListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TGroupGetUserJoinGroupListResp.Merge(dst, src)
}
func (m *TGroupGetUserJoinGroupListResp) XXX_Size() int {
	return xxx_messageInfo_TGroupGetUserJoinGroupListResp.Size(m)
}
func (m *TGroupGetUserJoinGroupListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TGroupGetUserJoinGroupListResp.DiscardUnknown(m)
}

var xxx_messageInfo_TGroupGetUserJoinGroupListResp proto.InternalMessageInfo

func (m *TGroupGetUserJoinGroupListResp) GetCreateGroupIdList() []uint32 {
	if m != nil {
		return m.CreateGroupIdList
	}
	return nil
}

func (m *TGroupGetUserJoinGroupListResp) GetJoinGroupIdList() []uint32 {
	if m != nil {
		return m.JoinGroupIdList
	}
	return nil
}

type GetGroupMuteUserListReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupMuteUserListReq) Reset()         { *m = GetGroupMuteUserListReq{} }
func (m *GetGroupMuteUserListReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupMuteUserListReq) ProtoMessage()    {}
func (*GetGroupMuteUserListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{86}
}
func (m *GetGroupMuteUserListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupMuteUserListReq.Unmarshal(m, b)
}
func (m *GetGroupMuteUserListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupMuteUserListReq.Marshal(b, m, deterministic)
}
func (dst *GetGroupMuteUserListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupMuteUserListReq.Merge(dst, src)
}
func (m *GetGroupMuteUserListReq) XXX_Size() int {
	return xxx_messageInfo_GetGroupMuteUserListReq.Size(m)
}
func (m *GetGroupMuteUserListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupMuteUserListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupMuteUserListReq proto.InternalMessageInfo

func (m *GetGroupMuteUserListReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type GetGroupMuteUserListResp struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupMuteUserListResp) Reset()         { *m = GetGroupMuteUserListResp{} }
func (m *GetGroupMuteUserListResp) String() string { return proto.CompactTextString(m) }
func (*GetGroupMuteUserListResp) ProtoMessage()    {}
func (*GetGroupMuteUserListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{87}
}
func (m *GetGroupMuteUserListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupMuteUserListResp.Unmarshal(m, b)
}
func (m *GetGroupMuteUserListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupMuteUserListResp.Marshal(b, m, deterministic)
}
func (dst *GetGroupMuteUserListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupMuteUserListResp.Merge(dst, src)
}
func (m *GetGroupMuteUserListResp) XXX_Size() int {
	return xxx_messageInfo_GetGroupMuteUserListResp.Size(m)
}
func (m *GetGroupMuteUserListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupMuteUserListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupMuteUserListResp proto.InternalMessageInfo

func (m *GetGroupMuteUserListResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type GetAllGroupIdListReq struct {
	GroupType            uint32   `protobuf:"varint,1,opt,name=group_type,json=groupType,proto3" json:"group_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllGroupIdListReq) Reset()         { *m = GetAllGroupIdListReq{} }
func (m *GetAllGroupIdListReq) String() string { return proto.CompactTextString(m) }
func (*GetAllGroupIdListReq) ProtoMessage()    {}
func (*GetAllGroupIdListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{88}
}
func (m *GetAllGroupIdListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllGroupIdListReq.Unmarshal(m, b)
}
func (m *GetAllGroupIdListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllGroupIdListReq.Marshal(b, m, deterministic)
}
func (dst *GetAllGroupIdListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllGroupIdListReq.Merge(dst, src)
}
func (m *GetAllGroupIdListReq) XXX_Size() int {
	return xxx_messageInfo_GetAllGroupIdListReq.Size(m)
}
func (m *GetAllGroupIdListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllGroupIdListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllGroupIdListReq proto.InternalMessageInfo

func (m *GetAllGroupIdListReq) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

type GetAllGroupIdListResp struct {
	GroupList            []uint32 `protobuf:"varint,1,rep,packed,name=group_list,json=groupList,proto3" json:"group_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllGroupIdListResp) Reset()         { *m = GetAllGroupIdListResp{} }
func (m *GetAllGroupIdListResp) String() string { return proto.CompactTextString(m) }
func (*GetAllGroupIdListResp) ProtoMessage()    {}
func (*GetAllGroupIdListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{89}
}
func (m *GetAllGroupIdListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllGroupIdListResp.Unmarshal(m, b)
}
func (m *GetAllGroupIdListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllGroupIdListResp.Marshal(b, m, deterministic)
}
func (dst *GetAllGroupIdListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllGroupIdListResp.Merge(dst, src)
}
func (m *GetAllGroupIdListResp) XXX_Size() int {
	return xxx_messageInfo_GetAllGroupIdListResp.Size(m)
}
func (m *GetAllGroupIdListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllGroupIdListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllGroupIdListResp proto.InternalMessageInfo

func (m *GetAllGroupIdListResp) GetGroupList() []uint32 {
	if m != nil {
		return m.GroupList
	}
	return nil
}

// 对指定用户检查所在公会的全部群 获取这些用户在群内管理权限
type CheckUserGroupAdminInfoInAllGroupReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	ExceptGroupId        uint32   `protobuf:"varint,3,opt,name=except_group_id,json=exceptGroupId,proto3" json:"except_group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserGroupAdminInfoInAllGroupReq) Reset()         { *m = CheckUserGroupAdminInfoInAllGroupReq{} }
func (m *CheckUserGroupAdminInfoInAllGroupReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserGroupAdminInfoInAllGroupReq) ProtoMessage()    {}
func (*CheckUserGroupAdminInfoInAllGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{90}
}
func (m *CheckUserGroupAdminInfoInAllGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserGroupAdminInfoInAllGroupReq.Unmarshal(m, b)
}
func (m *CheckUserGroupAdminInfoInAllGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserGroupAdminInfoInAllGroupReq.Marshal(b, m, deterministic)
}
func (dst *CheckUserGroupAdminInfoInAllGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserGroupAdminInfoInAllGroupReq.Merge(dst, src)
}
func (m *CheckUserGroupAdminInfoInAllGroupReq) XXX_Size() int {
	return xxx_messageInfo_CheckUserGroupAdminInfoInAllGroupReq.Size(m)
}
func (m *CheckUserGroupAdminInfoInAllGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserGroupAdminInfoInAllGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserGroupAdminInfoInAllGroupReq proto.InternalMessageInfo

func (m *CheckUserGroupAdminInfoInAllGroupReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CheckUserGroupAdminInfoInAllGroupReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *CheckUserGroupAdminInfoInAllGroupReq) GetExceptGroupId() uint32 {
	if m != nil {
		return m.ExceptGroupId
	}
	return 0
}

type StUserGroupAdminInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OwnerGroupList       []uint32 `protobuf:"varint,2,rep,packed,name=owner_group_list,json=ownerGroupList,proto3" json:"owner_group_list,omitempty"`
	AdminGroupList       []uint32 `protobuf:"varint,3,rep,packed,name=admin_group_list,json=adminGroupList,proto3" json:"admin_group_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StUserGroupAdminInfo) Reset()         { *m = StUserGroupAdminInfo{} }
func (m *StUserGroupAdminInfo) String() string { return proto.CompactTextString(m) }
func (*StUserGroupAdminInfo) ProtoMessage()    {}
func (*StUserGroupAdminInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{91}
}
func (m *StUserGroupAdminInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StUserGroupAdminInfo.Unmarshal(m, b)
}
func (m *StUserGroupAdminInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StUserGroupAdminInfo.Marshal(b, m, deterministic)
}
func (dst *StUserGroupAdminInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StUserGroupAdminInfo.Merge(dst, src)
}
func (m *StUserGroupAdminInfo) XXX_Size() int {
	return xxx_messageInfo_StUserGroupAdminInfo.Size(m)
}
func (m *StUserGroupAdminInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StUserGroupAdminInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StUserGroupAdminInfo proto.InternalMessageInfo

func (m *StUserGroupAdminInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StUserGroupAdminInfo) GetOwnerGroupList() []uint32 {
	if m != nil {
		return m.OwnerGroupList
	}
	return nil
}

func (m *StUserGroupAdminInfo) GetAdminGroupList() []uint32 {
	if m != nil {
		return m.AdminGroupList
	}
	return nil
}

type CheckUserGroupAdminInfoInAllGroupResp struct {
	GuildId               uint32                  `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	UserGroupAdminInfList []*StUserGroupAdminInfo `protobuf:"bytes,2,rep,name=userGroupAdminInf_list,json=userGroupAdminInfList,proto3" json:"userGroupAdminInf_list,omitempty"`
	ExceptGroupId         uint32                  `protobuf:"varint,3,opt,name=except_group_id,json=exceptGroupId,proto3" json:"except_group_id,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                `json:"-"`
	XXX_unrecognized      []byte                  `json:"-"`
	XXX_sizecache         int32                   `json:"-"`
}

func (m *CheckUserGroupAdminInfoInAllGroupResp) Reset()         { *m = CheckUserGroupAdminInfoInAllGroupResp{} }
func (m *CheckUserGroupAdminInfoInAllGroupResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserGroupAdminInfoInAllGroupResp) ProtoMessage()    {}
func (*CheckUserGroupAdminInfoInAllGroupResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{92}
}
func (m *CheckUserGroupAdminInfoInAllGroupResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserGroupAdminInfoInAllGroupResp.Unmarshal(m, b)
}
func (m *CheckUserGroupAdminInfoInAllGroupResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserGroupAdminInfoInAllGroupResp.Marshal(b, m, deterministic)
}
func (dst *CheckUserGroupAdminInfoInAllGroupResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserGroupAdminInfoInAllGroupResp.Merge(dst, src)
}
func (m *CheckUserGroupAdminInfoInAllGroupResp) XXX_Size() int {
	return xxx_messageInfo_CheckUserGroupAdminInfoInAllGroupResp.Size(m)
}
func (m *CheckUserGroupAdminInfoInAllGroupResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserGroupAdminInfoInAllGroupResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserGroupAdminInfoInAllGroupResp proto.InternalMessageInfo

func (m *CheckUserGroupAdminInfoInAllGroupResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CheckUserGroupAdminInfoInAllGroupResp) GetUserGroupAdminInfList() []*StUserGroupAdminInfo {
	if m != nil {
		return m.UserGroupAdminInfList
	}
	return nil
}

func (m *CheckUserGroupAdminInfoInAllGroupResp) GetExceptGroupId() uint32 {
	if m != nil {
		return m.ExceptGroupId
	}
	return 0
}

type UpdateGuildApplyExceedReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ApplyId              uint32   `protobuf:"varint,2,opt,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	GuildId              uint32   `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGuildApplyExceedReq) Reset()         { *m = UpdateGuildApplyExceedReq{} }
func (m *UpdateGuildApplyExceedReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGuildApplyExceedReq) ProtoMessage()    {}
func (*UpdateGuildApplyExceedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{93}
}
func (m *UpdateGuildApplyExceedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGuildApplyExceedReq.Unmarshal(m, b)
}
func (m *UpdateGuildApplyExceedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGuildApplyExceedReq.Marshal(b, m, deterministic)
}
func (dst *UpdateGuildApplyExceedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGuildApplyExceedReq.Merge(dst, src)
}
func (m *UpdateGuildApplyExceedReq) XXX_Size() int {
	return xxx_messageInfo_UpdateGuildApplyExceedReq.Size(m)
}
func (m *UpdateGuildApplyExceedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGuildApplyExceedReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGuildApplyExceedReq proto.InternalMessageInfo

func (m *UpdateGuildApplyExceedReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateGuildApplyExceedReq) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

func (m *UpdateGuildApplyExceedReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type UpdateGuildApplyExceedResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGuildApplyExceedResp) Reset()         { *m = UpdateGuildApplyExceedResp{} }
func (m *UpdateGuildApplyExceedResp) String() string { return proto.CompactTextString(m) }
func (*UpdateGuildApplyExceedResp) ProtoMessage()    {}
func (*UpdateGuildApplyExceedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{94}
}
func (m *UpdateGuildApplyExceedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGuildApplyExceedResp.Unmarshal(m, b)
}
func (m *UpdateGuildApplyExceedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGuildApplyExceedResp.Marshal(b, m, deterministic)
}
func (dst *UpdateGuildApplyExceedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGuildApplyExceedResp.Merge(dst, src)
}
func (m *UpdateGuildApplyExceedResp) XXX_Size() int {
	return xxx_messageInfo_UpdateGuildApplyExceedResp.Size(m)
}
func (m *UpdateGuildApplyExceedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGuildApplyExceedResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGuildApplyExceedResp proto.InternalMessageInfo

type UpdateGroupApplyExceedReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ApplyId              uint32   `protobuf:"varint,2,opt,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	GroupId              uint32   `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGroupApplyExceedReq) Reset()         { *m = UpdateGroupApplyExceedReq{} }
func (m *UpdateGroupApplyExceedReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupApplyExceedReq) ProtoMessage()    {}
func (*UpdateGroupApplyExceedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{95}
}
func (m *UpdateGroupApplyExceedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupApplyExceedReq.Unmarshal(m, b)
}
func (m *UpdateGroupApplyExceedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupApplyExceedReq.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupApplyExceedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupApplyExceedReq.Merge(dst, src)
}
func (m *UpdateGroupApplyExceedReq) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupApplyExceedReq.Size(m)
}
func (m *UpdateGroupApplyExceedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupApplyExceedReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupApplyExceedReq proto.InternalMessageInfo

func (m *UpdateGroupApplyExceedReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateGroupApplyExceedReq) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

func (m *UpdateGroupApplyExceedReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type UpdateGroupApplyExceedResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGroupApplyExceedResp) Reset()         { *m = UpdateGroupApplyExceedResp{} }
func (m *UpdateGroupApplyExceedResp) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupApplyExceedResp) ProtoMessage()    {}
func (*UpdateGroupApplyExceedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{96}
}
func (m *UpdateGroupApplyExceedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupApplyExceedResp.Unmarshal(m, b)
}
func (m *UpdateGroupApplyExceedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupApplyExceedResp.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupApplyExceedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupApplyExceedResp.Merge(dst, src)
}
func (m *UpdateGroupApplyExceedResp) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupApplyExceedResp.Size(m)
}
func (m *UpdateGroupApplyExceedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupApplyExceedResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupApplyExceedResp proto.InternalMessageInfo

// 根据display id获取group id
type GetTGroupIdByDisplayIdReq struct {
	DisplayIds           []uint32 `protobuf:"varint,1,rep,packed,name=display_ids,json=displayIds,proto3" json:"display_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTGroupIdByDisplayIdReq) Reset()         { *m = GetTGroupIdByDisplayIdReq{} }
func (m *GetTGroupIdByDisplayIdReq) String() string { return proto.CompactTextString(m) }
func (*GetTGroupIdByDisplayIdReq) ProtoMessage()    {}
func (*GetTGroupIdByDisplayIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{97}
}
func (m *GetTGroupIdByDisplayIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTGroupIdByDisplayIdReq.Unmarshal(m, b)
}
func (m *GetTGroupIdByDisplayIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTGroupIdByDisplayIdReq.Marshal(b, m, deterministic)
}
func (dst *GetTGroupIdByDisplayIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTGroupIdByDisplayIdReq.Merge(dst, src)
}
func (m *GetTGroupIdByDisplayIdReq) XXX_Size() int {
	return xxx_messageInfo_GetTGroupIdByDisplayIdReq.Size(m)
}
func (m *GetTGroupIdByDisplayIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTGroupIdByDisplayIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTGroupIdByDisplayIdReq proto.InternalMessageInfo

func (m *GetTGroupIdByDisplayIdReq) GetDisplayIds() []uint32 {
	if m != nil {
		return m.DisplayIds
	}
	return nil
}

type TGroupDisplayIdMapping struct {
	DisplayId            uint32   `protobuf:"varint,1,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	GroupId              uint32   `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TGroupDisplayIdMapping) Reset()         { *m = TGroupDisplayIdMapping{} }
func (m *TGroupDisplayIdMapping) String() string { return proto.CompactTextString(m) }
func (*TGroupDisplayIdMapping) ProtoMessage()    {}
func (*TGroupDisplayIdMapping) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{98}
}
func (m *TGroupDisplayIdMapping) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TGroupDisplayIdMapping.Unmarshal(m, b)
}
func (m *TGroupDisplayIdMapping) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TGroupDisplayIdMapping.Marshal(b, m, deterministic)
}
func (dst *TGroupDisplayIdMapping) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TGroupDisplayIdMapping.Merge(dst, src)
}
func (m *TGroupDisplayIdMapping) XXX_Size() int {
	return xxx_messageInfo_TGroupDisplayIdMapping.Size(m)
}
func (m *TGroupDisplayIdMapping) XXX_DiscardUnknown() {
	xxx_messageInfo_TGroupDisplayIdMapping.DiscardUnknown(m)
}

var xxx_messageInfo_TGroupDisplayIdMapping proto.InternalMessageInfo

func (m *TGroupDisplayIdMapping) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *TGroupDisplayIdMapping) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type GetTGroupIdByDisplayIdResp struct {
	TgroupIdMappings     []*TGroupDisplayIdMapping `protobuf:"bytes,1,rep,name=tgroup_id_mappings,json=tgroupIdMappings,proto3" json:"tgroup_id_mappings,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetTGroupIdByDisplayIdResp) Reset()         { *m = GetTGroupIdByDisplayIdResp{} }
func (m *GetTGroupIdByDisplayIdResp) String() string { return proto.CompactTextString(m) }
func (*GetTGroupIdByDisplayIdResp) ProtoMessage()    {}
func (*GetTGroupIdByDisplayIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{99}
}
func (m *GetTGroupIdByDisplayIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTGroupIdByDisplayIdResp.Unmarshal(m, b)
}
func (m *GetTGroupIdByDisplayIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTGroupIdByDisplayIdResp.Marshal(b, m, deterministic)
}
func (dst *GetTGroupIdByDisplayIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTGroupIdByDisplayIdResp.Merge(dst, src)
}
func (m *GetTGroupIdByDisplayIdResp) XXX_Size() int {
	return xxx_messageInfo_GetTGroupIdByDisplayIdResp.Size(m)
}
func (m *GetTGroupIdByDisplayIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTGroupIdByDisplayIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTGroupIdByDisplayIdResp proto.InternalMessageInfo

func (m *GetTGroupIdByDisplayIdResp) GetTgroupIdMappings() []*TGroupDisplayIdMapping {
	if m != nil {
		return m.TgroupIdMappings
	}
	return nil
}

// 根据group type获取group id
type GetGroupIdsByTypeReq struct {
	GroupTypeList        []uint32 `protobuf:"varint,1,rep,packed,name=group_type_list,json=groupTypeList,proto3" json:"group_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupIdsByTypeReq) Reset()         { *m = GetGroupIdsByTypeReq{} }
func (m *GetGroupIdsByTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupIdsByTypeReq) ProtoMessage()    {}
func (*GetGroupIdsByTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{100}
}
func (m *GetGroupIdsByTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupIdsByTypeReq.Unmarshal(m, b)
}
func (m *GetGroupIdsByTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupIdsByTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetGroupIdsByTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupIdsByTypeReq.Merge(dst, src)
}
func (m *GetGroupIdsByTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetGroupIdsByTypeReq.Size(m)
}
func (m *GetGroupIdsByTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupIdsByTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupIdsByTypeReq proto.InternalMessageInfo

func (m *GetGroupIdsByTypeReq) GetGroupTypeList() []uint32 {
	if m != nil {
		return m.GroupTypeList
	}
	return nil
}

type GroupIdTypeResp struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	GroupType            uint32   `protobuf:"varint,2,opt,name=group_type,json=groupType,proto3" json:"group_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupIdTypeResp) Reset()         { *m = GroupIdTypeResp{} }
func (m *GroupIdTypeResp) String() string { return proto.CompactTextString(m) }
func (*GroupIdTypeResp) ProtoMessage()    {}
func (*GroupIdTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{101}
}
func (m *GroupIdTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupIdTypeResp.Unmarshal(m, b)
}
func (m *GroupIdTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupIdTypeResp.Marshal(b, m, deterministic)
}
func (dst *GroupIdTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupIdTypeResp.Merge(dst, src)
}
func (m *GroupIdTypeResp) XXX_Size() int {
	return xxx_messageInfo_GroupIdTypeResp.Size(m)
}
func (m *GroupIdTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupIdTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GroupIdTypeResp proto.InternalMessageInfo

func (m *GroupIdTypeResp) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupIdTypeResp) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

type GetGroupIdsByTypeResp struct {
	GroupRespList        []*GroupIdTypeResp `protobuf:"bytes,1,rep,name=group_resp_list,json=groupRespList,proto3" json:"group_resp_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetGroupIdsByTypeResp) Reset()         { *m = GetGroupIdsByTypeResp{} }
func (m *GetGroupIdsByTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetGroupIdsByTypeResp) ProtoMessage()    {}
func (*GetGroupIdsByTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{102}
}
func (m *GetGroupIdsByTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupIdsByTypeResp.Unmarshal(m, b)
}
func (m *GetGroupIdsByTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupIdsByTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetGroupIdsByTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupIdsByTypeResp.Merge(dst, src)
}
func (m *GetGroupIdsByTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetGroupIdsByTypeResp.Size(m)
}
func (m *GetGroupIdsByTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupIdsByTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupIdsByTypeResp proto.InternalMessageInfo

func (m *GetGroupIdsByTypeResp) GetGroupRespList() []*GroupIdTypeResp {
	if m != nil {
		return m.GroupRespList
	}
	return nil
}

// 查询tgroup禁言列表
type GetTGroupMuteListReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTGroupMuteListReq) Reset()         { *m = GetTGroupMuteListReq{} }
func (m *GetTGroupMuteListReq) String() string { return proto.CompactTextString(m) }
func (*GetTGroupMuteListReq) ProtoMessage()    {}
func (*GetTGroupMuteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{103}
}
func (m *GetTGroupMuteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTGroupMuteListReq.Unmarshal(m, b)
}
func (m *GetTGroupMuteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTGroupMuteListReq.Marshal(b, m, deterministic)
}
func (dst *GetTGroupMuteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTGroupMuteListReq.Merge(dst, src)
}
func (m *GetTGroupMuteListReq) XXX_Size() int {
	return xxx_messageInfo_GetTGroupMuteListReq.Size(m)
}
func (m *GetTGroupMuteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTGroupMuteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTGroupMuteListReq proto.InternalMessageInfo

func (m *GetTGroupMuteListReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type GetTGroupMuteListResp struct {
	MuteUidList          []uint32 `protobuf:"varint,1,rep,packed,name=mute_uid_list,json=muteUidList,proto3" json:"mute_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTGroupMuteListResp) Reset()         { *m = GetTGroupMuteListResp{} }
func (m *GetTGroupMuteListResp) String() string { return proto.CompactTextString(m) }
func (*GetTGroupMuteListResp) ProtoMessage()    {}
func (*GetTGroupMuteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{104}
}
func (m *GetTGroupMuteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTGroupMuteListResp.Unmarshal(m, b)
}
func (m *GetTGroupMuteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTGroupMuteListResp.Marshal(b, m, deterministic)
}
func (dst *GetTGroupMuteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTGroupMuteListResp.Merge(dst, src)
}
func (m *GetTGroupMuteListResp) XXX_Size() int {
	return xxx_messageInfo_GetTGroupMuteListResp.Size(m)
}
func (m *GetTGroupMuteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTGroupMuteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTGroupMuteListResp proto.InternalMessageInfo

func (m *GetTGroupMuteListResp) GetMuteUidList() []uint32 {
	if m != nil {
		return m.MuteUidList
	}
	return nil
}

type ChangeMemberRoleReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uid                  uint64   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Role                 uint32   `protobuf:"varint,3,opt,name=role,proto3" json:"role,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeMemberRoleReq) Reset()         { *m = ChangeMemberRoleReq{} }
func (m *ChangeMemberRoleReq) String() string { return proto.CompactTextString(m) }
func (*ChangeMemberRoleReq) ProtoMessage()    {}
func (*ChangeMemberRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{105}
}
func (m *ChangeMemberRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeMemberRoleReq.Unmarshal(m, b)
}
func (m *ChangeMemberRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeMemberRoleReq.Marshal(b, m, deterministic)
}
func (dst *ChangeMemberRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeMemberRoleReq.Merge(dst, src)
}
func (m *ChangeMemberRoleReq) XXX_Size() int {
	return xxx_messageInfo_ChangeMemberRoleReq.Size(m)
}
func (m *ChangeMemberRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeMemberRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeMemberRoleReq proto.InternalMessageInfo

func (m *ChangeMemberRoleReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *ChangeMemberRoleReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChangeMemberRoleReq) GetRole() uint32 {
	if m != nil {
		return m.Role
	}
	return 0
}

type ChangeMemberRoleResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeMemberRoleResp) Reset()         { *m = ChangeMemberRoleResp{} }
func (m *ChangeMemberRoleResp) String() string { return proto.CompactTextString(m) }
func (*ChangeMemberRoleResp) ProtoMessage()    {}
func (*ChangeMemberRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{106}
}
func (m *ChangeMemberRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeMemberRoleResp.Unmarshal(m, b)
}
func (m *ChangeMemberRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeMemberRoleResp.Marshal(b, m, deterministic)
}
func (dst *ChangeMemberRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeMemberRoleResp.Merge(dst, src)
}
func (m *ChangeMemberRoleResp) XXX_Size() int {
	return xxx_messageInfo_ChangeMemberRoleResp.Size(m)
}
func (m *ChangeMemberRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeMemberRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeMemberRoleResp proto.InternalMessageInfo

type GetUserGroupListWithTypeReq struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GroupType            []uint32 `protobuf:"varint,2,rep,packed,name=group_type,json=groupType,proto3" json:"group_type,omitempty"`
	NotCache             bool     `protobuf:"varint,3,opt,name=not_cache,json=notCache,proto3" json:"not_cache,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserGroupListWithTypeReq) Reset()         { *m = GetUserGroupListWithTypeReq{} }
func (m *GetUserGroupListWithTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetUserGroupListWithTypeReq) ProtoMessage()    {}
func (*GetUserGroupListWithTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{107}
}
func (m *GetUserGroupListWithTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGroupListWithTypeReq.Unmarshal(m, b)
}
func (m *GetUserGroupListWithTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGroupListWithTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetUserGroupListWithTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGroupListWithTypeReq.Merge(dst, src)
}
func (m *GetUserGroupListWithTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetUserGroupListWithTypeReq.Size(m)
}
func (m *GetUserGroupListWithTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGroupListWithTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGroupListWithTypeReq proto.InternalMessageInfo

func (m *GetUserGroupListWithTypeReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserGroupListWithTypeReq) GetGroupType() []uint32 {
	if m != nil {
		return m.GroupType
	}
	return nil
}

func (m *GetUserGroupListWithTypeReq) GetNotCache() bool {
	if m != nil {
		return m.NotCache
	}
	return false
}

type GetUserGroupListWithTypeResp struct {
	Groups               map[uint32]uint32 `protobuf:"bytes,1,rep,name=groups,proto3" json:"groups,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetUserGroupListWithTypeResp) Reset()         { *m = GetUserGroupListWithTypeResp{} }
func (m *GetUserGroupListWithTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetUserGroupListWithTypeResp) ProtoMessage()    {}
func (*GetUserGroupListWithTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{108}
}
func (m *GetUserGroupListWithTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGroupListWithTypeResp.Unmarshal(m, b)
}
func (m *GetUserGroupListWithTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGroupListWithTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetUserGroupListWithTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGroupListWithTypeResp.Merge(dst, src)
}
func (m *GetUserGroupListWithTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetUserGroupListWithTypeResp.Size(m)
}
func (m *GetUserGroupListWithTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGroupListWithTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGroupListWithTypeResp proto.InternalMessageInfo

func (m *GetUserGroupListWithTypeResp) GetGroups() map[uint32]uint32 {
	if m != nil {
		return m.Groups
	}
	return nil
}

type GenAvatarReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	VersionPrefix        string   `protobuf:"bytes,2,opt,name=version_prefix,json=versionPrefix,proto3" json:"version_prefix,omitempty"`
	ForceGen             bool     `protobuf:"varint,3,opt,name=force_gen,json=forceGen,proto3" json:"force_gen,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenAvatarReq) Reset()         { *m = GenAvatarReq{} }
func (m *GenAvatarReq) String() string { return proto.CompactTextString(m) }
func (*GenAvatarReq) ProtoMessage()    {}
func (*GenAvatarReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{109}
}
func (m *GenAvatarReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenAvatarReq.Unmarshal(m, b)
}
func (m *GenAvatarReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenAvatarReq.Marshal(b, m, deterministic)
}
func (dst *GenAvatarReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenAvatarReq.Merge(dst, src)
}
func (m *GenAvatarReq) XXX_Size() int {
	return xxx_messageInfo_GenAvatarReq.Size(m)
}
func (m *GenAvatarReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GenAvatarReq.DiscardUnknown(m)
}

var xxx_messageInfo_GenAvatarReq proto.InternalMessageInfo

func (m *GenAvatarReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GenAvatarReq) GetVersionPrefix() string {
	if m != nil {
		return m.VersionPrefix
	}
	return ""
}

func (m *GenAvatarReq) GetForceGen() bool {
	if m != nil {
		return m.ForceGen
	}
	return false
}

type GenAvatarResp struct {
	Md5                  string   `protobuf:"bytes,1,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenAvatarResp) Reset()         { *m = GenAvatarResp{} }
func (m *GenAvatarResp) String() string { return proto.CompactTextString(m) }
func (*GenAvatarResp) ProtoMessage()    {}
func (*GenAvatarResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_group_management_1f292ce31ffcf51b, []int{110}
}
func (m *GenAvatarResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenAvatarResp.Unmarshal(m, b)
}
func (m *GenAvatarResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenAvatarResp.Marshal(b, m, deterministic)
}
func (dst *GenAvatarResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenAvatarResp.Merge(dst, src)
}
func (m *GenAvatarResp) XXX_Size() int {
	return xxx_messageInfo_GenAvatarResp.Size(m)
}
func (m *GenAvatarResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GenAvatarResp.DiscardUnknown(m)
}

var xxx_messageInfo_GenAvatarResp proto.InternalMessageInfo

func (m *GenAvatarResp) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func init() {
	proto.RegisterType((*GroupMemberResp)(nil), "group_management.GroupMemberResp")
	proto.RegisterType((*GetGroupMemberListReq)(nil), "group_management.GetGroupMemberListReq")
	proto.RegisterType((*GroupMemberListResp)(nil), "group_management.GroupMemberListResp")
	proto.RegisterType((*GetGroupMembersByUidsReq)(nil), "group_management.GetGroupMembersByUidsReq")
	proto.RegisterType((*GetGroupMemberReq)(nil), "group_management.GetGroupMemberReq")
	proto.RegisterType((*ApplyGroupReq)(nil), "group_management.ApplyGroupReq")
	proto.RegisterType((*ApplyGroupResp)(nil), "group_management.ApplyGroupResp")
	proto.RegisterType((*GetGroupApplyReq)(nil), "group_management.GetGroupApplyReq")
	proto.RegisterType((*GroupApplyResp)(nil), "group_management.GroupApplyResp")
	proto.RegisterType((*GroupApply)(nil), "group_management.GroupApply")
	proto.RegisterType((*GetGroupApplyByGroupIdReq)(nil), "group_management.GetGroupApplyByGroupIdReq")
	proto.RegisterType((*GetGroupApplyByGroupIdResp)(nil), "group_management.GetGroupApplyByGroupIdResp")
	proto.RegisterType((*ReviewApplyGuildReq)(nil), "group_management.ReviewApplyGuildReq")
	proto.RegisterType((*ReviewApplyGroupReq)(nil), "group_management.ReviewApplyGroupReq")
	proto.RegisterType((*ReviewApplyGroupResp)(nil), "group_management.ReviewApplyGroupResp")
	proto.RegisterType((*RemoveGuildMemberReq)(nil), "group_management.RemoveGuildMemberReq")
	proto.RegisterType((*AddGroupMemberReq)(nil), "group_management.AddGroupMemberReq")
	proto.RegisterType((*AddGroupMemberResp)(nil), "group_management.AddGroupMemberResp")
	proto.RegisterType((*GetGroupReq)(nil), "group_management.GetGroupReq")
	proto.RegisterType((*MemberMap)(nil), "group_management.MemberMap")
	proto.RegisterType((*TGroupExtra)(nil), "group_management.TGroupExtra")
	proto.RegisterType((*GroupResp)(nil), "group_management.GroupResp")
	proto.RegisterType((*MyGroupInfo)(nil), "group_management.MyGroupInfo")
	proto.RegisterType((*BatchGetGroupReq)(nil), "group_management.BatchGetGroupReq")
	proto.RegisterType((*BatchGetGroupResp)(nil), "group_management.BatchGetGroupResp")
	proto.RegisterType((*CreateTGroupExtra)(nil), "group_management.CreateTGroupExtra")
	proto.RegisterType((*CreateBasicGroupReq)(nil), "group_management.CreateBasicGroupReq")
	proto.RegisterType((*CreateBasicGroupResp)(nil), "group_management.CreateBasicGroupResp")
	proto.RegisterType((*CreateTGroupReq)(nil), "group_management.CreateTGroupReq")
	proto.RegisterType((*CreateTGroupResp)(nil), "group_management.CreateTGroupResp")
	proto.RegisterType((*DismissBasicGroupReq)(nil), "group_management.DismissBasicGroupReq")
	proto.RegisterType((*DismissBasicGroupResp)(nil), "group_management.DismissBasicGroupResp")
	proto.RegisterType((*CreateGroupReq)(nil), "group_management.CreateGroupReq")
	proto.RegisterType((*CreateGroupResp)(nil), "group_management.CreateGroupResp")
	proto.RegisterType((*UpdateGroupNameReq)(nil), "group_management.UpdateGroupNameReq")
	proto.RegisterType((*UpdateGroupNameResp)(nil), "group_management.UpdateGroupNameResp")
	proto.RegisterType((*RemoveGroupMemberReq)(nil), "group_management.RemoveGroupMemberReq")
	proto.RegisterType((*RemoveGroupMemberResp)(nil), "group_management.RemoveGroupMemberResp")
	proto.RegisterType((*SetGuildAdminToMemberReq)(nil), "group_management.SetGuildAdminToMemberReq")
	proto.RegisterType((*SetGroupMemberToOwnerReq)(nil), "group_management.SetGroupMemberToOwnerReq")
	proto.RegisterType((*SetGroupMemberToOwnerResp)(nil), "group_management.SetGroupMemberToOwnerResp")
	proto.RegisterType((*SetGroupOwnerToMemberReq)(nil), "group_management.SetGroupOwnerToMemberReq")
	proto.RegisterType((*SetGroupOwnerToMemberResp)(nil), "group_management.SetGroupOwnerToMemberResp")
	proto.RegisterType((*SetGroupMemberToAdminReq)(nil), "group_management.SetGroupMemberToAdminReq")
	proto.RegisterType((*SetGroupAdminToMemberReq)(nil), "group_management.SetGroupAdminToMemberReq")
	proto.RegisterType((*SetGroupAdminToMemberResp)(nil), "group_management.SetGroupAdminToMemberResp")
	proto.RegisterType((*GetGuildGroupListReq)(nil), "group_management.GetGuildGroupListReq")
	proto.RegisterType((*GetUserGroupListReq)(nil), "group_management.GetUserGroupListReq")
	proto.RegisterType((*GroupListResp)(nil), "group_management.GroupListResp")
	proto.RegisterType((*DelGuildInvalidGroupReq)(nil), "group_management.DelGuildInvalidGroupReq")
	proto.RegisterType((*DelGuildInvalidGroupResp)(nil), "group_management.DelGuildInvalidGroupResp")
	proto.RegisterType((*GroupIdListResp)(nil), "group_management.GroupIdListResp")
	proto.RegisterType((*UpdateGroupMemberMuteReq)(nil), "group_management.UpdateGroupMemberMuteReq")
	proto.RegisterType((*UpdateGroupMemberMuteResp)(nil), "group_management.UpdateGroupMemberMuteResp")
	proto.RegisterType((*UpdateGroupMemberCardReq)(nil), "group_management.UpdateGroupMemberCardReq")
	proto.RegisterType((*UpdateGroupMemberCardResp)(nil), "group_management.UpdateGroupMemberCardResp")
	proto.RegisterType((*UpdateGroupNeedVerifyReq)(nil), "group_management.UpdateGroupNeedVerifyReq")
	proto.RegisterType((*UpdateGroupNeedVerifyResp)(nil), "group_management.UpdateGroupNeedVerifyResp")
	proto.RegisterType((*UpdateGroupAllMuteReq)(nil), "group_management.UpdateGroupAllMuteReq")
	proto.RegisterType((*UpdateGroupAllMuteResp)(nil), "group_management.UpdateGroupAllMuteResp")
	proto.RegisterType((*DismissGroupReq)(nil), "group_management.DismissGroupReq")
	proto.RegisterType((*DismissGroupResp)(nil), "group_management.DismissGroupResp")
	proto.RegisterType((*SetGroupNotRecvMsgReq)(nil), "group_management.SetGroupNotRecvMsgReq")
	proto.RegisterType((*SetGroupNotRecvMsgResp)(nil), "group_management.SetGroupNotRecvMsgResp")
	proto.RegisterType((*GuildModifyGameGroupOrderReq)(nil), "group_management.GuildModifyGameGroupOrderReq")
	proto.RegisterType((*GuildModifyGameGroupOrderResp)(nil), "group_management.GuildModifyGameGroupOrderResp")
	proto.RegisterType((*GuildGetGroupOrderReq)(nil), "group_management.GuildGetGroupOrderReq")
	proto.RegisterType((*GroupOrder)(nil), "group_management.GroupOrder")
	proto.RegisterType((*GuildGetGroupOrderResp)(nil), "group_management.GuildGetGroupOrderResp")
	proto.RegisterType((*GroupCheckUidNeedVerifyReq)(nil), "group_management.GroupCheckUidNeedVerifyReq")
	proto.RegisterType((*GroupCheckUidNeedVerifyResp)(nil), "group_management.GroupCheckUidNeedVerifyResp")
	proto.RegisterType((*GroupSetUidNeedVerifyReq)(nil), "group_management.GroupSetUidNeedVerifyReq")
	proto.RegisterType((*GroupSetUidNeedVerifyResp)(nil), "group_management.GroupSetUidNeedVerifyResp")
	proto.RegisterType((*TGroupGetUserCreateGroupCountReq)(nil), "group_management.TGroupGetUserCreateGroupCountReq")
	proto.RegisterType((*TGroupGetUserCreateGroupCountResp)(nil), "group_management.TGroupGetUserCreateGroupCountResp")
	proto.RegisterType((*ModifyTGroupDescReq)(nil), "group_management.ModifyTGroupDescReq")
	proto.RegisterType((*ModifyTGroupDescResp)(nil), "group_management.ModifyTGroupDescResp")
	proto.RegisterType((*GetTGroupByDisplayIdReq)(nil), "group_management.GetTGroupByDisplayIdReq")
	proto.RegisterType((*GetTGroupByDisplayIdResp)(nil), "group_management.GetTGroupByDisplayIdResp")
	proto.RegisterType((*GetUserGroupIDListByGuildIDReq)(nil), "group_management.GetUserGroupIDListByGuildIDReq")
	proto.RegisterType((*GetUserGroupIDListByGuildIDResp)(nil), "group_management.GetUserGroupIDListByGuildIDResp")
	proto.RegisterType((*TGroupSearchByGameIdReq)(nil), "group_management.TGroupSearchByGameIdReq")
	proto.RegisterType((*TGroupGameGroupIdList)(nil), "group_management.TGroupGameGroupIdList")
	proto.RegisterType((*TGroupSearchByGameIdResp)(nil), "group_management.TGroupSearchByGameIdResp")
	proto.RegisterType((*TGroupGetUserJoinGroupListReq)(nil), "group_management.TGroupGetUserJoinGroupListReq")
	proto.RegisterType((*TGroupGetUserJoinGroupListResp)(nil), "group_management.TGroupGetUserJoinGroupListResp")
	proto.RegisterType((*GetGroupMuteUserListReq)(nil), "group_management.GetGroupMuteUserListReq")
	proto.RegisterType((*GetGroupMuteUserListResp)(nil), "group_management.GetGroupMuteUserListResp")
	proto.RegisterType((*GetAllGroupIdListReq)(nil), "group_management.GetAllGroupIdListReq")
	proto.RegisterType((*GetAllGroupIdListResp)(nil), "group_management.GetAllGroupIdListResp")
	proto.RegisterType((*CheckUserGroupAdminInfoInAllGroupReq)(nil), "group_management.CheckUserGroupAdminInfoInAllGroupReq")
	proto.RegisterType((*StUserGroupAdminInfo)(nil), "group_management.stUserGroupAdminInfo")
	proto.RegisterType((*CheckUserGroupAdminInfoInAllGroupResp)(nil), "group_management.CheckUserGroupAdminInfoInAllGroupResp")
	proto.RegisterType((*UpdateGuildApplyExceedReq)(nil), "group_management.UpdateGuildApplyExceedReq")
	proto.RegisterType((*UpdateGuildApplyExceedResp)(nil), "group_management.UpdateGuildApplyExceedResp")
	proto.RegisterType((*UpdateGroupApplyExceedReq)(nil), "group_management.UpdateGroupApplyExceedReq")
	proto.RegisterType((*UpdateGroupApplyExceedResp)(nil), "group_management.UpdateGroupApplyExceedResp")
	proto.RegisterType((*GetTGroupIdByDisplayIdReq)(nil), "group_management.GetTGroupIdByDisplayIdReq")
	proto.RegisterType((*TGroupDisplayIdMapping)(nil), "group_management.TGroupDisplayIdMapping")
	proto.RegisterType((*GetTGroupIdByDisplayIdResp)(nil), "group_management.GetTGroupIdByDisplayIdResp")
	proto.RegisterType((*GetGroupIdsByTypeReq)(nil), "group_management.GetGroupIdsByTypeReq")
	proto.RegisterType((*GroupIdTypeResp)(nil), "group_management.GroupIdTypeResp")
	proto.RegisterType((*GetGroupIdsByTypeResp)(nil), "group_management.GetGroupIdsByTypeResp")
	proto.RegisterType((*GetTGroupMuteListReq)(nil), "group_management.GetTGroupMuteListReq")
	proto.RegisterType((*GetTGroupMuteListResp)(nil), "group_management.GetTGroupMuteListResp")
	proto.RegisterType((*ChangeMemberRoleReq)(nil), "group_management.ChangeMemberRoleReq")
	proto.RegisterType((*ChangeMemberRoleResp)(nil), "group_management.ChangeMemberRoleResp")
	proto.RegisterType((*GetUserGroupListWithTypeReq)(nil), "group_management.GetUserGroupListWithTypeReq")
	proto.RegisterType((*GetUserGroupListWithTypeResp)(nil), "group_management.GetUserGroupListWithTypeResp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "group_management.GetUserGroupListWithTypeResp.GroupsEntry")
	proto.RegisterType((*GenAvatarReq)(nil), "group_management.GenAvatarReq")
	proto.RegisterType((*GenAvatarResp)(nil), "group_management.GenAvatarResp")
	proto.RegisterEnum("group_management.E_GROUP_TYPE", E_GROUP_TYPE_name, E_GROUP_TYPE_value)
	proto.RegisterEnum("group_management.SortField", SortField_name, SortField_value)
	proto.RegisterEnum("group_management.SortType", SortType_name, SortType_value)
	proto.RegisterEnum("group_management.GroupMemberRole", GroupMemberRole_name, GroupMemberRole_value)
	proto.RegisterEnum("group_management.TGroupExtra_TYPE", TGroupExtra_TYPE_name, TGroupExtra_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GroupManagementClient is the client API for GroupManagement service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GroupManagementClient interface {
	CreateGroup(ctx context.Context, in *CreateGroupReq, opts ...grpc.CallOption) (*CreateGroupResp, error)
	DismissGroup(ctx context.Context, in *DismissGroupReq, opts ...grpc.CallOption) (*DismissGroupResp, error)
	CreateBasicGroup(ctx context.Context, in *CreateBasicGroupReq, opts ...grpc.CallOption) (*CreateBasicGroupResp, error)
	CreateTGroup(ctx context.Context, in *CreateTGroupReq, opts ...grpc.CallOption) (*CreateTGroupResp, error)
	DismissBasicGroup(ctx context.Context, in *DismissBasicGroupReq, opts ...grpc.CallOption) (*DismissBasicGroupResp, error)
	GenAvatar(ctx context.Context, in *GenAvatarReq, opts ...grpc.CallOption) (*GenAvatarResp, error)
	GetGroup(ctx context.Context, in *GetGroupReq, opts ...grpc.CallOption) (*GroupResp, error)
	BatchGetGroup(ctx context.Context, in *BatchGetGroupReq, opts ...grpc.CallOption) (*BatchGetGroupResp, error)
	UpdateGroupName(ctx context.Context, in *UpdateGroupNameReq, opts ...grpc.CallOption) (*UpdateGroupNameResp, error)
	UpdateGroupNeedVerify(ctx context.Context, in *UpdateGroupNeedVerifyReq, opts ...grpc.CallOption) (*UpdateGroupNeedVerifyResp, error)
	GetGroupMemberList(ctx context.Context, in *GetGroupMemberListReq, opts ...grpc.CallOption) (*GroupMemberListResp, error)
	GetGroupMember(ctx context.Context, in *GetGroupMemberReq, opts ...grpc.CallOption) (*GroupMemberResp, error)
	GetGroupMembersByUids(ctx context.Context, in *GetGroupMembersByUidsReq, opts ...grpc.CallOption) (*GroupMemberListResp, error)
	AddGroupMember(ctx context.Context, in *AddGroupMemberReq, opts ...grpc.CallOption) (*AddGroupMemberResp, error)
	RemoveGroupMember(ctx context.Context, in *RemoveGroupMemberReq, opts ...grpc.CallOption) (*RemoveGroupMemberResp, error)
	ChangeMemberRole(ctx context.Context, in *ChangeMemberRoleReq, opts ...grpc.CallOption) (*ChangeMemberRoleResp, error)
	SetGroupMemberToOwner(ctx context.Context, in *SetGroupMemberToOwnerReq, opts ...grpc.CallOption) (*SetGroupMemberToOwnerResp, error)
	SetGroupAdminToMember(ctx context.Context, in *SetGroupAdminToMemberReq, opts ...grpc.CallOption) (*SetGroupAdminToMemberResp, error)
	SetGroupOwnerToMember(ctx context.Context, in *SetGroupOwnerToMemberReq, opts ...grpc.CallOption) (*SetGroupOwnerToMemberResp, error)
	GetUserGroupListWithType(ctx context.Context, in *GetUserGroupListWithTypeReq, opts ...grpc.CallOption) (*GetUserGroupListWithTypeResp, error)
	ApplyGroup(ctx context.Context, in *ApplyGroupReq, opts ...grpc.CallOption) (*ApplyGroupResp, error)
	GetGroupApply(ctx context.Context, in *GetGroupApplyReq, opts ...grpc.CallOption) (*GroupApplyResp, error)
	ReviewApplyGroup(ctx context.Context, in *ReviewApplyGroupReq, opts ...grpc.CallOption) (*ReviewApplyGroupResp, error)
	SetGroupNotRecvMsg(ctx context.Context, in *SetGroupNotRecvMsgReq, opts ...grpc.CallOption) (*SetGroupNotRecvMsgResp, error)
	UpdateGroupMemberCard(ctx context.Context, in *UpdateGroupMemberCardReq, opts ...grpc.CallOption) (*UpdateGroupMemberCardResp, error)
	UpdateGroupMemberMute(ctx context.Context, in *UpdateGroupMemberMuteReq, opts ...grpc.CallOption) (*UpdateGroupMemberMuteResp, error)
	UpdateGroupAllMute(ctx context.Context, in *UpdateGroupAllMuteReq, opts ...grpc.CallOption) (*UpdateGroupAllMuteResp, error)
	GetGroupMuteUserList(ctx context.Context, in *GetGroupMuteUserListReq, opts ...grpc.CallOption) (*GetGroupMuteUserListResp, error)
	// guild group ---
	GetUserGroupIDListByGuildID(ctx context.Context, in *GetUserGroupIDListByGuildIDReq, opts ...grpc.CallOption) (*GetUserGroupIDListByGuildIDResp, error)
	GetUserGroupList(ctx context.Context, in *GetUserGroupListReq, opts ...grpc.CallOption) (*GroupListResp, error)
	DelGuildInvalidGroup(ctx context.Context, in *DelGuildInvalidGroupReq, opts ...grpc.CallOption) (*DelGuildInvalidGroupResp, error)
	GetGuildGroupList(ctx context.Context, in *GetGuildGroupListReq, opts ...grpc.CallOption) (*GroupIdListResp, error)
	GuildModifyGameGroupOrder(ctx context.Context, in *GuildModifyGameGroupOrderReq, opts ...grpc.CallOption) (*GuildModifyGameGroupOrderResp, error)
	GuildGetGroupOrder(ctx context.Context, in *GuildGetGroupOrderReq, opts ...grpc.CallOption) (*GuildGetGroupOrderResp, error)
	TGroupGetUserCreateGroupCount(ctx context.Context, in *TGroupGetUserCreateGroupCountReq, opts ...grpc.CallOption) (*TGroupGetUserCreateGroupCountResp, error)
	ModifyTGroupDesc(ctx context.Context, in *ModifyTGroupDescReq, opts ...grpc.CallOption) (*ModifyTGroupDescResp, error)
	GetTGroupByDisplayId(ctx context.Context, in *GetTGroupByDisplayIdReq, opts ...grpc.CallOption) (*GetTGroupByDisplayIdResp, error)
	TGroupGetUserJoinGroupList(ctx context.Context, in *TGroupGetUserJoinGroupListReq, opts ...grpc.CallOption) (*TGroupGetUserJoinGroupListResp, error)
	// 对指定用户检查所在公会的全部群 获取这些用户在群内管理权限
	CheckUserGroupAdminInfoInAllGroup(ctx context.Context, in *CheckUserGroupAdminInfoInAllGroupReq, opts ...grpc.CallOption) (*CheckUserGroupAdminInfoInAllGroupResp, error)
	UpdateGroupApplyExceed(ctx context.Context, in *UpdateGroupApplyExceedReq, opts ...grpc.CallOption) (*UpdateGroupApplyExceedResp, error)
	GetGroupApplyByGroupId(ctx context.Context, in *GetGroupApplyByGroupIdReq, opts ...grpc.CallOption) (*GetGroupApplyByGroupIdResp, error)
}

type groupManagementClient struct {
	cc *grpc.ClientConn
}

func NewGroupManagementClient(cc *grpc.ClientConn) GroupManagementClient {
	return &groupManagementClient{cc}
}

func (c *groupManagementClient) CreateGroup(ctx context.Context, in *CreateGroupReq, opts ...grpc.CallOption) (*CreateGroupResp, error) {
	out := new(CreateGroupResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/CreateGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) DismissGroup(ctx context.Context, in *DismissGroupReq, opts ...grpc.CallOption) (*DismissGroupResp, error) {
	out := new(DismissGroupResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/DismissGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) CreateBasicGroup(ctx context.Context, in *CreateBasicGroupReq, opts ...grpc.CallOption) (*CreateBasicGroupResp, error) {
	out := new(CreateBasicGroupResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/CreateBasicGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) CreateTGroup(ctx context.Context, in *CreateTGroupReq, opts ...grpc.CallOption) (*CreateTGroupResp, error) {
	out := new(CreateTGroupResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/CreateTGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) DismissBasicGroup(ctx context.Context, in *DismissBasicGroupReq, opts ...grpc.CallOption) (*DismissBasicGroupResp, error) {
	out := new(DismissBasicGroupResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/DismissBasicGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) GenAvatar(ctx context.Context, in *GenAvatarReq, opts ...grpc.CallOption) (*GenAvatarResp, error) {
	out := new(GenAvatarResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/GenAvatar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) GetGroup(ctx context.Context, in *GetGroupReq, opts ...grpc.CallOption) (*GroupResp, error) {
	out := new(GroupResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/GetGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) BatchGetGroup(ctx context.Context, in *BatchGetGroupReq, opts ...grpc.CallOption) (*BatchGetGroupResp, error) {
	out := new(BatchGetGroupResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/BatchGetGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) UpdateGroupName(ctx context.Context, in *UpdateGroupNameReq, opts ...grpc.CallOption) (*UpdateGroupNameResp, error) {
	out := new(UpdateGroupNameResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/UpdateGroupName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) UpdateGroupNeedVerify(ctx context.Context, in *UpdateGroupNeedVerifyReq, opts ...grpc.CallOption) (*UpdateGroupNeedVerifyResp, error) {
	out := new(UpdateGroupNeedVerifyResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/UpdateGroupNeedVerify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) GetGroupMemberList(ctx context.Context, in *GetGroupMemberListReq, opts ...grpc.CallOption) (*GroupMemberListResp, error) {
	out := new(GroupMemberListResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/GetGroupMemberList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) GetGroupMember(ctx context.Context, in *GetGroupMemberReq, opts ...grpc.CallOption) (*GroupMemberResp, error) {
	out := new(GroupMemberResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/GetGroupMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) GetGroupMembersByUids(ctx context.Context, in *GetGroupMembersByUidsReq, opts ...grpc.CallOption) (*GroupMemberListResp, error) {
	out := new(GroupMemberListResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/GetGroupMembersByUids", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) AddGroupMember(ctx context.Context, in *AddGroupMemberReq, opts ...grpc.CallOption) (*AddGroupMemberResp, error) {
	out := new(AddGroupMemberResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/AddGroupMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) RemoveGroupMember(ctx context.Context, in *RemoveGroupMemberReq, opts ...grpc.CallOption) (*RemoveGroupMemberResp, error) {
	out := new(RemoveGroupMemberResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/RemoveGroupMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) ChangeMemberRole(ctx context.Context, in *ChangeMemberRoleReq, opts ...grpc.CallOption) (*ChangeMemberRoleResp, error) {
	out := new(ChangeMemberRoleResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/ChangeMemberRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) SetGroupMemberToOwner(ctx context.Context, in *SetGroupMemberToOwnerReq, opts ...grpc.CallOption) (*SetGroupMemberToOwnerResp, error) {
	out := new(SetGroupMemberToOwnerResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/SetGroupMemberToOwner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) SetGroupAdminToMember(ctx context.Context, in *SetGroupAdminToMemberReq, opts ...grpc.CallOption) (*SetGroupAdminToMemberResp, error) {
	out := new(SetGroupAdminToMemberResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/SetGroupAdminToMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) SetGroupOwnerToMember(ctx context.Context, in *SetGroupOwnerToMemberReq, opts ...grpc.CallOption) (*SetGroupOwnerToMemberResp, error) {
	out := new(SetGroupOwnerToMemberResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/SetGroupOwnerToMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) GetUserGroupListWithType(ctx context.Context, in *GetUserGroupListWithTypeReq, opts ...grpc.CallOption) (*GetUserGroupListWithTypeResp, error) {
	out := new(GetUserGroupListWithTypeResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/GetUserGroupListWithType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) ApplyGroup(ctx context.Context, in *ApplyGroupReq, opts ...grpc.CallOption) (*ApplyGroupResp, error) {
	out := new(ApplyGroupResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/ApplyGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) GetGroupApply(ctx context.Context, in *GetGroupApplyReq, opts ...grpc.CallOption) (*GroupApplyResp, error) {
	out := new(GroupApplyResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/GetGroupApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) ReviewApplyGroup(ctx context.Context, in *ReviewApplyGroupReq, opts ...grpc.CallOption) (*ReviewApplyGroupResp, error) {
	out := new(ReviewApplyGroupResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/ReviewApplyGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) SetGroupNotRecvMsg(ctx context.Context, in *SetGroupNotRecvMsgReq, opts ...grpc.CallOption) (*SetGroupNotRecvMsgResp, error) {
	out := new(SetGroupNotRecvMsgResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/SetGroupNotRecvMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) UpdateGroupMemberCard(ctx context.Context, in *UpdateGroupMemberCardReq, opts ...grpc.CallOption) (*UpdateGroupMemberCardResp, error) {
	out := new(UpdateGroupMemberCardResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/UpdateGroupMemberCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) UpdateGroupMemberMute(ctx context.Context, in *UpdateGroupMemberMuteReq, opts ...grpc.CallOption) (*UpdateGroupMemberMuteResp, error) {
	out := new(UpdateGroupMemberMuteResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/UpdateGroupMemberMute", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) UpdateGroupAllMute(ctx context.Context, in *UpdateGroupAllMuteReq, opts ...grpc.CallOption) (*UpdateGroupAllMuteResp, error) {
	out := new(UpdateGroupAllMuteResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/UpdateGroupAllMute", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) GetGroupMuteUserList(ctx context.Context, in *GetGroupMuteUserListReq, opts ...grpc.CallOption) (*GetGroupMuteUserListResp, error) {
	out := new(GetGroupMuteUserListResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/GetGroupMuteUserList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) GetUserGroupIDListByGuildID(ctx context.Context, in *GetUserGroupIDListByGuildIDReq, opts ...grpc.CallOption) (*GetUserGroupIDListByGuildIDResp, error) {
	out := new(GetUserGroupIDListByGuildIDResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/GetUserGroupIDListByGuildID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) GetUserGroupList(ctx context.Context, in *GetUserGroupListReq, opts ...grpc.CallOption) (*GroupListResp, error) {
	out := new(GroupListResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/GetUserGroupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) DelGuildInvalidGroup(ctx context.Context, in *DelGuildInvalidGroupReq, opts ...grpc.CallOption) (*DelGuildInvalidGroupResp, error) {
	out := new(DelGuildInvalidGroupResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/DelGuildInvalidGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) GetGuildGroupList(ctx context.Context, in *GetGuildGroupListReq, opts ...grpc.CallOption) (*GroupIdListResp, error) {
	out := new(GroupIdListResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/GetGuildGroupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) GuildModifyGameGroupOrder(ctx context.Context, in *GuildModifyGameGroupOrderReq, opts ...grpc.CallOption) (*GuildModifyGameGroupOrderResp, error) {
	out := new(GuildModifyGameGroupOrderResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/GuildModifyGameGroupOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) GuildGetGroupOrder(ctx context.Context, in *GuildGetGroupOrderReq, opts ...grpc.CallOption) (*GuildGetGroupOrderResp, error) {
	out := new(GuildGetGroupOrderResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/GuildGetGroupOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) TGroupGetUserCreateGroupCount(ctx context.Context, in *TGroupGetUserCreateGroupCountReq, opts ...grpc.CallOption) (*TGroupGetUserCreateGroupCountResp, error) {
	out := new(TGroupGetUserCreateGroupCountResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/TGroupGetUserCreateGroupCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) ModifyTGroupDesc(ctx context.Context, in *ModifyTGroupDescReq, opts ...grpc.CallOption) (*ModifyTGroupDescResp, error) {
	out := new(ModifyTGroupDescResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/ModifyTGroupDesc", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) GetTGroupByDisplayId(ctx context.Context, in *GetTGroupByDisplayIdReq, opts ...grpc.CallOption) (*GetTGroupByDisplayIdResp, error) {
	out := new(GetTGroupByDisplayIdResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/GetTGroupByDisplayId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) TGroupGetUserJoinGroupList(ctx context.Context, in *TGroupGetUserJoinGroupListReq, opts ...grpc.CallOption) (*TGroupGetUserJoinGroupListResp, error) {
	out := new(TGroupGetUserJoinGroupListResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/TGroupGetUserJoinGroupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) CheckUserGroupAdminInfoInAllGroup(ctx context.Context, in *CheckUserGroupAdminInfoInAllGroupReq, opts ...grpc.CallOption) (*CheckUserGroupAdminInfoInAllGroupResp, error) {
	out := new(CheckUserGroupAdminInfoInAllGroupResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/CheckUserGroupAdminInfoInAllGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) UpdateGroupApplyExceed(ctx context.Context, in *UpdateGroupApplyExceedReq, opts ...grpc.CallOption) (*UpdateGroupApplyExceedResp, error) {
	out := new(UpdateGroupApplyExceedResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/UpdateGroupApplyExceed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupManagementClient) GetGroupApplyByGroupId(ctx context.Context, in *GetGroupApplyByGroupIdReq, opts ...grpc.CallOption) (*GetGroupApplyByGroupIdResp, error) {
	out := new(GetGroupApplyByGroupIdResp)
	err := c.cc.Invoke(ctx, "/group_management.GroupManagement/GetGroupApplyByGroupId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GroupManagementServer is the server API for GroupManagement service.
type GroupManagementServer interface {
	CreateGroup(context.Context, *CreateGroupReq) (*CreateGroupResp, error)
	DismissGroup(context.Context, *DismissGroupReq) (*DismissGroupResp, error)
	CreateBasicGroup(context.Context, *CreateBasicGroupReq) (*CreateBasicGroupResp, error)
	CreateTGroup(context.Context, *CreateTGroupReq) (*CreateTGroupResp, error)
	DismissBasicGroup(context.Context, *DismissBasicGroupReq) (*DismissBasicGroupResp, error)
	GenAvatar(context.Context, *GenAvatarReq) (*GenAvatarResp, error)
	GetGroup(context.Context, *GetGroupReq) (*GroupResp, error)
	BatchGetGroup(context.Context, *BatchGetGroupReq) (*BatchGetGroupResp, error)
	UpdateGroupName(context.Context, *UpdateGroupNameReq) (*UpdateGroupNameResp, error)
	UpdateGroupNeedVerify(context.Context, *UpdateGroupNeedVerifyReq) (*UpdateGroupNeedVerifyResp, error)
	GetGroupMemberList(context.Context, *GetGroupMemberListReq) (*GroupMemberListResp, error)
	GetGroupMember(context.Context, *GetGroupMemberReq) (*GroupMemberResp, error)
	GetGroupMembersByUids(context.Context, *GetGroupMembersByUidsReq) (*GroupMemberListResp, error)
	AddGroupMember(context.Context, *AddGroupMemberReq) (*AddGroupMemberResp, error)
	RemoveGroupMember(context.Context, *RemoveGroupMemberReq) (*RemoveGroupMemberResp, error)
	ChangeMemberRole(context.Context, *ChangeMemberRoleReq) (*ChangeMemberRoleResp, error)
	SetGroupMemberToOwner(context.Context, *SetGroupMemberToOwnerReq) (*SetGroupMemberToOwnerResp, error)
	SetGroupAdminToMember(context.Context, *SetGroupAdminToMemberReq) (*SetGroupAdminToMemberResp, error)
	SetGroupOwnerToMember(context.Context, *SetGroupOwnerToMemberReq) (*SetGroupOwnerToMemberResp, error)
	GetUserGroupListWithType(context.Context, *GetUserGroupListWithTypeReq) (*GetUserGroupListWithTypeResp, error)
	ApplyGroup(context.Context, *ApplyGroupReq) (*ApplyGroupResp, error)
	GetGroupApply(context.Context, *GetGroupApplyReq) (*GroupApplyResp, error)
	ReviewApplyGroup(context.Context, *ReviewApplyGroupReq) (*ReviewApplyGroupResp, error)
	SetGroupNotRecvMsg(context.Context, *SetGroupNotRecvMsgReq) (*SetGroupNotRecvMsgResp, error)
	UpdateGroupMemberCard(context.Context, *UpdateGroupMemberCardReq) (*UpdateGroupMemberCardResp, error)
	UpdateGroupMemberMute(context.Context, *UpdateGroupMemberMuteReq) (*UpdateGroupMemberMuteResp, error)
	UpdateGroupAllMute(context.Context, *UpdateGroupAllMuteReq) (*UpdateGroupAllMuteResp, error)
	GetGroupMuteUserList(context.Context, *GetGroupMuteUserListReq) (*GetGroupMuteUserListResp, error)
	// guild group ---
	GetUserGroupIDListByGuildID(context.Context, *GetUserGroupIDListByGuildIDReq) (*GetUserGroupIDListByGuildIDResp, error)
	GetUserGroupList(context.Context, *GetUserGroupListReq) (*GroupListResp, error)
	DelGuildInvalidGroup(context.Context, *DelGuildInvalidGroupReq) (*DelGuildInvalidGroupResp, error)
	GetGuildGroupList(context.Context, *GetGuildGroupListReq) (*GroupIdListResp, error)
	GuildModifyGameGroupOrder(context.Context, *GuildModifyGameGroupOrderReq) (*GuildModifyGameGroupOrderResp, error)
	GuildGetGroupOrder(context.Context, *GuildGetGroupOrderReq) (*GuildGetGroupOrderResp, error)
	TGroupGetUserCreateGroupCount(context.Context, *TGroupGetUserCreateGroupCountReq) (*TGroupGetUserCreateGroupCountResp, error)
	ModifyTGroupDesc(context.Context, *ModifyTGroupDescReq) (*ModifyTGroupDescResp, error)
	GetTGroupByDisplayId(context.Context, *GetTGroupByDisplayIdReq) (*GetTGroupByDisplayIdResp, error)
	TGroupGetUserJoinGroupList(context.Context, *TGroupGetUserJoinGroupListReq) (*TGroupGetUserJoinGroupListResp, error)
	// 对指定用户检查所在公会的全部群 获取这些用户在群内管理权限
	CheckUserGroupAdminInfoInAllGroup(context.Context, *CheckUserGroupAdminInfoInAllGroupReq) (*CheckUserGroupAdminInfoInAllGroupResp, error)
	UpdateGroupApplyExceed(context.Context, *UpdateGroupApplyExceedReq) (*UpdateGroupApplyExceedResp, error)
	GetGroupApplyByGroupId(context.Context, *GetGroupApplyByGroupIdReq) (*GetGroupApplyByGroupIdResp, error)
}

func RegisterGroupManagementServer(s *grpc.Server, srv GroupManagementServer) {
	s.RegisterService(&_GroupManagement_serviceDesc, srv)
}

func _GroupManagement_CreateGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).CreateGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/CreateGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).CreateGroup(ctx, req.(*CreateGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_DismissGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DismissGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).DismissGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/DismissGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).DismissGroup(ctx, req.(*DismissGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_CreateBasicGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBasicGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).CreateBasicGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/CreateBasicGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).CreateBasicGroup(ctx, req.(*CreateBasicGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_CreateTGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).CreateTGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/CreateTGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).CreateTGroup(ctx, req.(*CreateTGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_DismissBasicGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DismissBasicGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).DismissBasicGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/DismissBasicGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).DismissBasicGroup(ctx, req.(*DismissBasicGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_GenAvatar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenAvatarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).GenAvatar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/GenAvatar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).GenAvatar(ctx, req.(*GenAvatarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_GetGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).GetGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/GetGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).GetGroup(ctx, req.(*GetGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_BatchGetGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).BatchGetGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/BatchGetGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).BatchGetGroup(ctx, req.(*BatchGetGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_UpdateGroupName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGroupNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).UpdateGroupName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/UpdateGroupName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).UpdateGroupName(ctx, req.(*UpdateGroupNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_UpdateGroupNeedVerify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGroupNeedVerifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).UpdateGroupNeedVerify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/UpdateGroupNeedVerify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).UpdateGroupNeedVerify(ctx, req.(*UpdateGroupNeedVerifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_GetGroupMemberList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupMemberListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).GetGroupMemberList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/GetGroupMemberList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).GetGroupMemberList(ctx, req.(*GetGroupMemberListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_GetGroupMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).GetGroupMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/GetGroupMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).GetGroupMember(ctx, req.(*GetGroupMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_GetGroupMembersByUids_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupMembersByUidsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).GetGroupMembersByUids(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/GetGroupMembersByUids",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).GetGroupMembersByUids(ctx, req.(*GetGroupMembersByUidsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_AddGroupMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddGroupMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).AddGroupMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/AddGroupMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).AddGroupMember(ctx, req.(*AddGroupMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_RemoveGroupMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveGroupMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).RemoveGroupMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/RemoveGroupMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).RemoveGroupMember(ctx, req.(*RemoveGroupMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_ChangeMemberRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeMemberRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).ChangeMemberRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/ChangeMemberRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).ChangeMemberRole(ctx, req.(*ChangeMemberRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_SetGroupMemberToOwner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGroupMemberToOwnerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).SetGroupMemberToOwner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/SetGroupMemberToOwner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).SetGroupMemberToOwner(ctx, req.(*SetGroupMemberToOwnerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_SetGroupAdminToMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGroupAdminToMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).SetGroupAdminToMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/SetGroupAdminToMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).SetGroupAdminToMember(ctx, req.(*SetGroupAdminToMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_SetGroupOwnerToMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGroupOwnerToMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).SetGroupOwnerToMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/SetGroupOwnerToMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).SetGroupOwnerToMember(ctx, req.(*SetGroupOwnerToMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_GetUserGroupListWithType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserGroupListWithTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).GetUserGroupListWithType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/GetUserGroupListWithType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).GetUserGroupListWithType(ctx, req.(*GetUserGroupListWithTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_ApplyGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).ApplyGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/ApplyGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).ApplyGroup(ctx, req.(*ApplyGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_GetGroupApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupApplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).GetGroupApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/GetGroupApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).GetGroupApply(ctx, req.(*GetGroupApplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_ReviewApplyGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReviewApplyGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).ReviewApplyGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/ReviewApplyGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).ReviewApplyGroup(ctx, req.(*ReviewApplyGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_SetGroupNotRecvMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGroupNotRecvMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).SetGroupNotRecvMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/SetGroupNotRecvMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).SetGroupNotRecvMsg(ctx, req.(*SetGroupNotRecvMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_UpdateGroupMemberCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGroupMemberCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).UpdateGroupMemberCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/UpdateGroupMemberCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).UpdateGroupMemberCard(ctx, req.(*UpdateGroupMemberCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_UpdateGroupMemberMute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGroupMemberMuteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).UpdateGroupMemberMute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/UpdateGroupMemberMute",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).UpdateGroupMemberMute(ctx, req.(*UpdateGroupMemberMuteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_UpdateGroupAllMute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGroupAllMuteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).UpdateGroupAllMute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/UpdateGroupAllMute",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).UpdateGroupAllMute(ctx, req.(*UpdateGroupAllMuteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_GetGroupMuteUserList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupMuteUserListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).GetGroupMuteUserList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/GetGroupMuteUserList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).GetGroupMuteUserList(ctx, req.(*GetGroupMuteUserListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_GetUserGroupIDListByGuildID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserGroupIDListByGuildIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).GetUserGroupIDListByGuildID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/GetUserGroupIDListByGuildID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).GetUserGroupIDListByGuildID(ctx, req.(*GetUserGroupIDListByGuildIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_GetUserGroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserGroupListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).GetUserGroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/GetUserGroupList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).GetUserGroupList(ctx, req.(*GetUserGroupListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_DelGuildInvalidGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelGuildInvalidGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).DelGuildInvalidGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/DelGuildInvalidGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).DelGuildInvalidGroup(ctx, req.(*DelGuildInvalidGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_GetGuildGroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildGroupListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).GetGuildGroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/GetGuildGroupList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).GetGuildGroupList(ctx, req.(*GetGuildGroupListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_GuildModifyGameGroupOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildModifyGameGroupOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).GuildModifyGameGroupOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/GuildModifyGameGroupOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).GuildModifyGameGroupOrder(ctx, req.(*GuildModifyGameGroupOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_GuildGetGroupOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildGetGroupOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).GuildGetGroupOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/GuildGetGroupOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).GuildGetGroupOrder(ctx, req.(*GuildGetGroupOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_TGroupGetUserCreateGroupCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TGroupGetUserCreateGroupCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).TGroupGetUserCreateGroupCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/TGroupGetUserCreateGroupCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).TGroupGetUserCreateGroupCount(ctx, req.(*TGroupGetUserCreateGroupCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_ModifyTGroupDesc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyTGroupDescReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).ModifyTGroupDesc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/ModifyTGroupDesc",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).ModifyTGroupDesc(ctx, req.(*ModifyTGroupDescReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_GetTGroupByDisplayId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTGroupByDisplayIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).GetTGroupByDisplayId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/GetTGroupByDisplayId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).GetTGroupByDisplayId(ctx, req.(*GetTGroupByDisplayIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_TGroupGetUserJoinGroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TGroupGetUserJoinGroupListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).TGroupGetUserJoinGroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/TGroupGetUserJoinGroupList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).TGroupGetUserJoinGroupList(ctx, req.(*TGroupGetUserJoinGroupListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_CheckUserGroupAdminInfoInAllGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserGroupAdminInfoInAllGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).CheckUserGroupAdminInfoInAllGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/CheckUserGroupAdminInfoInAllGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).CheckUserGroupAdminInfoInAllGroup(ctx, req.(*CheckUserGroupAdminInfoInAllGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_UpdateGroupApplyExceed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGroupApplyExceedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).UpdateGroupApplyExceed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/UpdateGroupApplyExceed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).UpdateGroupApplyExceed(ctx, req.(*UpdateGroupApplyExceedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupManagement_GetGroupApplyByGroupId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupApplyByGroupIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupManagementServer).GetGroupApplyByGroupId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/group_management.GroupManagement/GetGroupApplyByGroupId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupManagementServer).GetGroupApplyByGroupId(ctx, req.(*GetGroupApplyByGroupIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GroupManagement_serviceDesc = grpc.ServiceDesc{
	ServiceName: "group_management.GroupManagement",
	HandlerType: (*GroupManagementServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateGroup",
			Handler:    _GroupManagement_CreateGroup_Handler,
		},
		{
			MethodName: "DismissGroup",
			Handler:    _GroupManagement_DismissGroup_Handler,
		},
		{
			MethodName: "CreateBasicGroup",
			Handler:    _GroupManagement_CreateBasicGroup_Handler,
		},
		{
			MethodName: "CreateTGroup",
			Handler:    _GroupManagement_CreateTGroup_Handler,
		},
		{
			MethodName: "DismissBasicGroup",
			Handler:    _GroupManagement_DismissBasicGroup_Handler,
		},
		{
			MethodName: "GenAvatar",
			Handler:    _GroupManagement_GenAvatar_Handler,
		},
		{
			MethodName: "GetGroup",
			Handler:    _GroupManagement_GetGroup_Handler,
		},
		{
			MethodName: "BatchGetGroup",
			Handler:    _GroupManagement_BatchGetGroup_Handler,
		},
		{
			MethodName: "UpdateGroupName",
			Handler:    _GroupManagement_UpdateGroupName_Handler,
		},
		{
			MethodName: "UpdateGroupNeedVerify",
			Handler:    _GroupManagement_UpdateGroupNeedVerify_Handler,
		},
		{
			MethodName: "GetGroupMemberList",
			Handler:    _GroupManagement_GetGroupMemberList_Handler,
		},
		{
			MethodName: "GetGroupMember",
			Handler:    _GroupManagement_GetGroupMember_Handler,
		},
		{
			MethodName: "GetGroupMembersByUids",
			Handler:    _GroupManagement_GetGroupMembersByUids_Handler,
		},
		{
			MethodName: "AddGroupMember",
			Handler:    _GroupManagement_AddGroupMember_Handler,
		},
		{
			MethodName: "RemoveGroupMember",
			Handler:    _GroupManagement_RemoveGroupMember_Handler,
		},
		{
			MethodName: "ChangeMemberRole",
			Handler:    _GroupManagement_ChangeMemberRole_Handler,
		},
		{
			MethodName: "SetGroupMemberToOwner",
			Handler:    _GroupManagement_SetGroupMemberToOwner_Handler,
		},
		{
			MethodName: "SetGroupAdminToMember",
			Handler:    _GroupManagement_SetGroupAdminToMember_Handler,
		},
		{
			MethodName: "SetGroupOwnerToMember",
			Handler:    _GroupManagement_SetGroupOwnerToMember_Handler,
		},
		{
			MethodName: "GetUserGroupListWithType",
			Handler:    _GroupManagement_GetUserGroupListWithType_Handler,
		},
		{
			MethodName: "ApplyGroup",
			Handler:    _GroupManagement_ApplyGroup_Handler,
		},
		{
			MethodName: "GetGroupApply",
			Handler:    _GroupManagement_GetGroupApply_Handler,
		},
		{
			MethodName: "ReviewApplyGroup",
			Handler:    _GroupManagement_ReviewApplyGroup_Handler,
		},
		{
			MethodName: "SetGroupNotRecvMsg",
			Handler:    _GroupManagement_SetGroupNotRecvMsg_Handler,
		},
		{
			MethodName: "UpdateGroupMemberCard",
			Handler:    _GroupManagement_UpdateGroupMemberCard_Handler,
		},
		{
			MethodName: "UpdateGroupMemberMute",
			Handler:    _GroupManagement_UpdateGroupMemberMute_Handler,
		},
		{
			MethodName: "UpdateGroupAllMute",
			Handler:    _GroupManagement_UpdateGroupAllMute_Handler,
		},
		{
			MethodName: "GetGroupMuteUserList",
			Handler:    _GroupManagement_GetGroupMuteUserList_Handler,
		},
		{
			MethodName: "GetUserGroupIDListByGuildID",
			Handler:    _GroupManagement_GetUserGroupIDListByGuildID_Handler,
		},
		{
			MethodName: "GetUserGroupList",
			Handler:    _GroupManagement_GetUserGroupList_Handler,
		},
		{
			MethodName: "DelGuildInvalidGroup",
			Handler:    _GroupManagement_DelGuildInvalidGroup_Handler,
		},
		{
			MethodName: "GetGuildGroupList",
			Handler:    _GroupManagement_GetGuildGroupList_Handler,
		},
		{
			MethodName: "GuildModifyGameGroupOrder",
			Handler:    _GroupManagement_GuildModifyGameGroupOrder_Handler,
		},
		{
			MethodName: "GuildGetGroupOrder",
			Handler:    _GroupManagement_GuildGetGroupOrder_Handler,
		},
		{
			MethodName: "TGroupGetUserCreateGroupCount",
			Handler:    _GroupManagement_TGroupGetUserCreateGroupCount_Handler,
		},
		{
			MethodName: "ModifyTGroupDesc",
			Handler:    _GroupManagement_ModifyTGroupDesc_Handler,
		},
		{
			MethodName: "GetTGroupByDisplayId",
			Handler:    _GroupManagement_GetTGroupByDisplayId_Handler,
		},
		{
			MethodName: "TGroupGetUserJoinGroupList",
			Handler:    _GroupManagement_TGroupGetUserJoinGroupList_Handler,
		},
		{
			MethodName: "CheckUserGroupAdminInfoInAllGroup",
			Handler:    _GroupManagement_CheckUserGroupAdminInfoInAllGroup_Handler,
		},
		{
			MethodName: "UpdateGroupApplyExceed",
			Handler:    _GroupManagement_UpdateGroupApplyExceed_Handler,
		},
		{
			MethodName: "GetGroupApplyByGroupId",
			Handler:    _GroupManagement_GetGroupApplyByGroupId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "group/group-mangement/group-management.proto",
}

func init() {
	proto.RegisterFile("group/group-mangement/group-management.proto", fileDescriptor_group_management_1f292ce31ffcf51b)
}

var fileDescriptor_group_management_1f292ce31ffcf51b = []byte{
	// 3980 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe4, 0x5b, 0x4f, 0x73, 0x23, 0xc7,
	0x75, 0xe7, 0x00, 0x20, 0x09, 0x3c, 0x10, 0x24, 0xd8, 0x4b, 0x72, 0xb1, 0xa0, 0x56, 0xcb, 0x6d,
	0x69, 0x57, 0x0c, 0x25, 0xed, 0x5a, 0xb4, 0xd6, 0xb6, 0xe4, 0xc4, 0x11, 0xf8, 0x67, 0x29, 0x58,
	0x4b, 0x52, 0x01, 0xc9, 0x55, 0xc9, 0x8e, 0x0b, 0x1e, 0x61, 0x9a, 0xdc, 0xd1, 0x02, 0x98, 0x59,
	0xf4, 0x80, 0x12, 0x1c, 0x97, 0x53, 0x4e, 0xf9, 0x90, 0x4a, 0xee, 0x39, 0xe4, 0x0b, 0xf8, 0x94,
	0x7b, 0x2a, 0xe7, 0x1c, 0x92, 0xaa, 0x54, 0xe5, 0x4b, 0x24, 0xd7, 0x1c, 0xf2, 0x05, 0x9c, 0xea,
	0xd7, 0x3d, 0xff, 0x7b, 0x06, 0xe0, 0xaa, 0x2a, 0x17, 0x5f, 0x76, 0xa7, 0x1f, 0xba, 0xdf, 0x7b,
	0xfd, 0xfa, 0xf5, 0xaf, 0x5f, 0xbf, 0xd7, 0x84, 0xf7, 0xae, 0x46, 0xce, 0xd8, 0x7d, 0x8c, 0xff,
	0xbe, 0x3f, 0x30, 0x87, 0x57, 0x6c, 0xc0, 0x86, 0x5e, 0xd8, 0x36, 0x25, 0xe1, 0x91, 0x3b, 0x72,
	0x3c, 0x87, 0xd4, 0x91, 0xde, 0x0d, 0xe9, 0xf4, 0xbf, 0x0c, 0x58, 0x39, 0x12, 0xc4, 0x63, 0x36,
	0xf8, 0x8a, 0x8d, 0x3a, 0x8c, 0xbb, 0xa4, 0x0e, 0xc5, 0xb1, 0x6d, 0x35, 0x8c, 0x2d, 0x63, 0xbb,
	0xd6, 0x11, 0x9f, 0x84, 0x40, 0x69, 0xe4, 0xf4, 0x59, 0xa3, 0x80, 0x24, 0xfc, 0x26, 0x1b, 0xb0,
	0x30, 0x62, 0x03, 0x73, 0xf4, 0xb2, 0x51, 0xdc, 0x32, 0xb6, 0x2b, 0x1d, 0xd5, 0x12, 0xf4, 0x6f,
	0x98, 0x7d, 0xf5, 0xc2, 0x6b, 0x94, 0xb0, 0xb7, 0x6a, 0x91, 0xbb, 0x00, 0x52, 0x7a, 0xcf, 0x1c,
	0x59, 0x8d, 0x79, 0x1c, 0x53, 0x41, 0xca, 0xbe, 0x39, 0xb2, 0xc2, 0x9f, 0x07, 0x63, 0x8f, 0x35,
	0x16, 0xb6, 0x8c, 0xed, 0xb2, 0xfa, 0xf9, 0x78, 0xec, 0x31, 0xb2, 0x09, 0x95, 0xaf, 0x1d, 0x7b,
	0xc8, 0xac, 0xae, 0xe9, 0x35, 0x16, 0x91, 0x71, 0x59, 0x12, 0x5a, 0x1e, 0xd9, 0x82, 0xa5, 0x11,
	0xeb, 0x5d, 0x77, 0x07, 0xfc, 0xaa, 0xeb, 0xb8, 0x5e, 0xa3, 0x8c, 0xbf, 0x83, 0xa0, 0x1d, 0xf3,
	0xab, 0x53, 0xd7, 0xa3, 0x7f, 0x30, 0x60, 0xfd, 0x88, 0x79, 0x91, 0x99, 0x3e, 0xb3, 0xb9, 0xd7,
	0x61, 0xaf, 0xc8, 0x1d, 0x28, 0x4b, 0xb9, 0xc1, 0x8c, 0x17, 0xb1, 0xdd, 0xb6, 0xc4, 0x4c, 0x9c,
	0xcb, 0x4b, 0xce, 0x3c, 0x35, 0x6f, 0xd5, 0x12, 0xba, 0xb8, 0xe6, 0x15, 0xeb, 0x72, 0xfb, 0x57,
	0x0c, 0x27, 0x5f, 0xeb, 0x94, 0x05, 0xe1, 0xcc, 0xfe, 0x15, 0x13, 0xf3, 0x70, 0x86, 0xfd, 0x49,
	0xd7, 0xb4, 0x06, 0xf6, 0x10, 0x4d, 0x50, 0xee, 0x54, 0x04, 0xa5, 0x25, 0x08, 0x28, 0x6e, 0x6c,
	0xf7, 0x2d, 0x21, 0x6e, 0x5e, 0x89, 0x13, 0xed, 0xb6, 0x45, 0xde, 0x84, 0xaa, 0xcd, 0xbb, 0x66,
	0xbf, 0xdf, 0xb5, 0xc6, 0xde, 0xc4, 0x37, 0x81, 0xcd, 0x5b, 0xfd, 0xfe, 0xc1, 0xd8, 0x9b, 0x90,
	0x7b, 0x50, 0xe5, 0xce, 0xc8, 0xeb, 0x5e, 0xda, 0xac, 0x6f, 0xf1, 0xc6, 0xe2, 0x56, 0x71, 0x7b,
	0xbe, 0x03, 0x82, 0xf4, 0x14, 0x29, 0x42, 0x2f, 0xec, 0xe0, 0x4d, 0x5c, 0x86, 0x36, 0x98, 0xef,
	0x94, 0x05, 0xe1, 0x7c, 0xe2, 0x32, 0xda, 0x81, 0x5b, 0xa9, 0xd9, 0x73, 0x97, 0xfc, 0x18, 0x16,
	0x07, 0x48, 0xe1, 0x0d, 0x63, 0xab, 0xb8, 0x5d, 0xdd, 0xbd, 0xff, 0x28, 0xe9, 0x23, 0x8f, 0x12,
	0xfe, 0xd1, 0xf1, 0x47, 0xd0, 0x36, 0x34, 0xe2, 0x46, 0xe5, 0x7b, 0x93, 0x0b, 0xdb, 0xe2, 0x53,
	0xec, 0x4a, 0xa0, 0x34, 0xb6, 0x2d, 0xde, 0x28, 0x6c, 0x15, 0x85, 0x37, 0x89, 0x6f, 0xfa, 0x09,
	0xac, 0xc6, 0x59, 0x4d, 0xe1, 0xa1, 0x7c, 0xb4, 0x10, 0xf8, 0x28, 0xbd, 0x84, 0x5a, 0xcb, 0x75,
	0xfb, 0x13, 0xe4, 0x21, 0x46, 0xa7, 0xdd, 0x38, 0xca, 0xaf, 0x90, 0x5a, 0xeb, 0x6b, 0x36, 0xb2,
	0x2f, 0x27, 0xbe, 0x37, 0xcb, 0x96, 0x60, 0xc2, 0xd9, 0x2b, 0xe5, 0xca, 0xe2, 0x93, 0xbe, 0x0b,
	0xcb, 0x51, 0x39, 0xdc, 0x15, 0x6c, 0x4d, 0x41, 0x89, 0xa8, 0x89, 0xed, 0xb6, 0x45, 0xff, 0x1c,
	0xea, 0xfe, 0xb4, 0x70, 0x50, 0xa6, 0x5e, 0x01, 0x83, 0x42, 0x9c, 0xc1, 0xff, 0x1a, 0xb0, 0x1c,
	0x1d, 0x9e, 0x2b, 0x2e, 0x6d, 0x95, 0xd8, 0x94, 0x8b, 0x59, 0x53, 0x2e, 0xc5, 0xa6, 0xbc, 0x01,
	0x0b, 0xdc, 0x33, 0xbd, 0x31, 0x57, 0x0e, 0xaa, 0x5a, 0xa1, 0x5c, 0xd3, 0x43, 0xe7, 0xf4, 0xe5,
	0xb6, 0x3c, 0xd2, 0x84, 0xf2, 0x88, 0x5d, 0xdb, 0xec, 0x1b, 0x36, 0xf2, 0x37, 0xa7, 0xdf, 0x16,
	0x6e, 0xab, 0xbe, 0x71, 0xef, 0x06, 0x7b, 0x53, 0x92, 0x5a, 0x9e, 0x6f, 0xe2, 0x4a, 0x68, 0xe2,
	0xff, 0x31, 0x00, 0xc2, 0x49, 0xff, 0x11, 0x4c, 0xf8, 0x53, 0xb8, 0x13, 0x73, 0x93, 0x3d, 0xe9,
	0x5d, 0x6d, 0xeb, 0xa6, 0x7e, 0x4c, 0x7f, 0x0e, 0xcd, 0x2c, 0x4e, 0xdc, 0x25, 0x7f, 0x06, 0x55,
	0x39, 0x10, 0xe7, 0x81, 0x2c, 0xab, 0xbb, 0x6f, 0x64, 0xec, 0x78, 0xe9, 0x71, 0x12, 0x95, 0xf1,
	0x9b, 0xfe, 0x35, 0xdc, 0xea, 0xe0, 0x34, 0xe4, 0x06, 0x10, 0xb8, 0x25, 0x14, 0x8c, 0x1a, 0xc3,
	0x48, 0x18, 0x63, 0x13, 0x2a, 0xd2, 0x86, 0xe1, 0x32, 0x49, 0xa3, 0x5e, 0x24, 0xfc, 0xbe, 0x18,
	0x5f, 0xd8, 0x35, 0x98, 0x37, 0xaf, 0x46, 0x8c, 0x29, 0x04, 0x95, 0x8d, 0xa4, 0x02, 0xfe, 0x4e,
	0xff, 0xff, 0x53, 0x60, 0x03, 0xd6, 0xd2, 0x0a, 0x70, 0x97, 0xfe, 0x52, 0xd0, 0x07, 0xce, 0x35,
	0x43, 0xa3, 0xc4, 0x11, 0xcc, 0x87, 0x7b, 0x23, 0x0e, 0xf7, 0x69, 0xd7, 0xdd, 0x84, 0xca, 0xab,
	0xb1, 0xad, 0xf0, 0x5b, 0x9d, 0x2b, 0x82, 0x80, 0xf8, 0xfd, 0xb7, 0x06, 0xac, 0xb6, 0x2c, 0xeb,
	0x3b, 0x20, 0x64, 0xe4, 0x64, 0x2e, 0xc6, 0x4e, 0x66, 0xff, 0x74, 0x2f, 0x45, 0x4e, 0xf7, 0x26,
	0x94, 0x1d, 0x97, 0x8d, 0x4c, 0xcf, 0x19, 0xe1, 0xae, 0x28, 0x75, 0x82, 0x36, 0x5d, 0x03, 0x92,
	0xd4, 0x84, 0xbb, 0x74, 0x1b, 0xaa, 0xbe, 0xe7, 0xe5, 0x6b, 0x46, 0xf7, 0xa1, 0x22, 0xc7, 0x1d,
	0x9b, 0xae, 0xb0, 0x73, 0x9f, 0x5d, 0xb3, 0xbe, 0xea, 0x24, 0x1b, 0x64, 0x0b, 0xaa, 0x03, 0x75,
	0x50, 0x0d, 0x6c, 0xff, 0xfc, 0x8d, 0x92, 0xe8, 0x7f, 0x14, 0xa0, 0x7a, 0x8e, 0xd2, 0x0e, 0xbf,
	0xf5, 0x46, 0x66, 0x18, 0x3f, 0x58, 0x8c, 0xf7, 0x90, 0x99, 0x1f, 0x5e, 0x1c, 0x30, 0xde, 0x13,
	0xb6, 0xed, 0xd9, 0xde, 0xa4, 0xdb, 0x73, 0x2c, 0x19, 0xc6, 0x54, 0x3a, 0x65, 0x41, 0xd8, 0x77,
	0x2c, 0x16, 0xfc, 0x38, 0x34, 0x07, 0x4c, 0xe1, 0x3f, 0xfe, 0x78, 0x62, 0x0e, 0x18, 0xb9, 0x0d,
	0x8b, 0x57, 0xe6, 0x80, 0x89, 0x79, 0xa8, 0x80, 0x46, 0x34, 0xdb, 0x96, 0xd8, 0xe7, 0x9e, 0x14,
	0x89, 0x0b, 0x26, 0xb1, 0x03, 0x24, 0x49, 0x2c, 0x59, 0x38, 0xb5, 0x85, 0xe8, 0xd4, 0x3e, 0x06,
	0x90, 0xf3, 0xe8, 0x0e, 0x4c, 0x17, 0x4f, 0xf1, 0xea, 0xee, 0x66, 0x7a, 0x0b, 0x06, 0x16, 0xea,
	0x54, 0x06, 0x81, 0xb1, 0xee, 0x41, 0xb5, 0x37, 0x62, 0xa6, 0xc7, 0xba, 0x97, 0x23, 0x67, 0x80,
	0xd0, 0x52, 0xe9, 0x80, 0x24, 0x3d, 0x1d, 0x39, 0x03, 0xfa, 0x43, 0x28, 0x9d, 0x7f, 0xf9, 0xf9,
	0x21, 0x59, 0x82, 0xf2, 0x51, 0xeb, 0xf8, 0xb0, 0x7b, 0xd2, 0x7e, 0x56, 0x9f, 0x23, 0xcb, 0x00,
	0xd8, 0xfa, 0xf4, 0x74, 0x6f, 0xef, 0xcb, 0xba, 0x41, 0x56, 0xa1, 0x86, 0xed, 0xd3, 0xa7, 0x4f,
	0xdb, 0xfb, 0xed, 0xd6, 0xb3, 0x7a, 0x81, 0xfe, 0x77, 0x11, 0x2a, 0xb1, 0x13, 0x2d, 0xcb, 0xad,
	0xa2, 0x1e, 0x5d, 0x88, 0x7b, 0x74, 0xc4, 0x52, 0xc5, 0x98, 0xa5, 0x82, 0xb5, 0x41, 0x43, 0x49,
	0x2b, 0x56, 0x42, 0x3b, 0x11, 0x28, 0xa1, 0xe5, 0x65, 0x4c, 0x88, 0xdf, 0xe4, 0x3e, 0x2c, 0x29,
	0x2b, 0xf5, 0x9c, 0xf1, 0xd0, 0xc7, 0x5f, 0xe5, 0x01, 0xfb, 0x82, 0x44, 0x1a, 0xb0, 0x88, 0x33,
	0x77, 0x7c, 0x08, 0xf6, 0x9b, 0xc2, 0xf0, 0xce, 0x37, 0x43, 0x36, 0x52, 0xd8, 0x2b, 0x1b, 0x42,
	0x0b, 0x69, 0x29, 0x84, 0x65, 0x89, 0xbe, 0x15, 0x45, 0x69, 0x79, 0xc2, 0xb6, 0x43, 0xc6, 0xac,
	0xae, 0x3a, 0x22, 0x40, 0x2e, 0xa7, 0x20, 0x3d, 0x97, 0xc7, 0x84, 0x00, 0x8b, 0x7e, 0x5f, 0xc6,
	0xa7, 0x55, 0x05, 0x16, 0xfd, 0x3e, 0x46, 0xa7, 0x77, 0x01, 0x2c, 0x9b, 0xbb, 0x7d, 0x13, 0x91,
	0x64, 0x49, 0xb2, 0x56, 0x94, 0xb6, 0x45, 0x3e, 0x81, 0x25, 0xe5, 0x29, 0x4c, 0xf8, 0x6a, 0xa3,
	0x86, 0xb8, 0x7b, 0x37, 0xbd, 0xe8, 0x11, 0x87, 0xee, 0x28, 0xe7, 0x92, 0xde, 0xfd, 0x36, 0x2c,
	0x47, 0x94, 0xeb, 0x5e, 0xef, 0x36, 0x96, 0x51, 0xc8, 0x52, 0xa8, 0xdf, 0xf3, 0xdd, 0x88, 0xd1,
	0xfa, 0xb8, 0x6d, 0x56, 0xd2, 0xdb, 0xe6, 0xdf, 0x0c, 0xa8, 0x1e, 0xab, 0x33, 0x61, 0x78, 0xe9,
	0x90, 0x8f, 0x40, 0x2e, 0x84, 0x3d, 0xbc, 0x74, 0xd4, 0x79, 0xb0, 0x99, 0x71, 0x1e, 0x60, 0xec,
	0x17, 0xf6, 0x26, 0x1f, 0xc1, 0xc2, 0x60, 0x12, 0x5c, 0x0b, 0x66, 0x8a, 0x1c, 0xd5, 0x00, 0xb2,
	0x0f, 0x15, 0x8c, 0x8f, 0xfb, 0x36, 0x97, 0x60, 0x54, 0xdd, 0x7d, 0x90, 0x3b, 0xda, 0x8f, 0x57,
	0x3b, 0xe1, 0x38, 0xfa, 0xf7, 0x06, 0xd4, 0xf7, 0x4c, 0xaf, 0xf7, 0x22, 0x0a, 0x3b, 0x81, 0xab,
	0x21, 0x6b, 0x03, 0x23, 0x4c, 0xa9, 0xb3, 0x60, 0x41, 0xde, 0x82, 0x9a, 0xcd, 0xbb, 0xc2, 0x68,
	0x11, 0xd5, 0xcb, 0x9d, 0x25, 0x9b, 0x9f, 0x04, 0x34, 0x61, 0x46, 0xd5, 0x49, 0x06, 0xf1, 0x45,
	0xec, 0x53, 0x95, 0x7d, 0x90, 0xe4, 0x83, 0x6b, 0x29, 0x0c, 0x3f, 0xbf, 0x84, 0xd5, 0x84, 0x32,
	0xdc, 0x25, 0x07, 0xb0, 0x1c, 0xd8, 0x2b, 0xd4, 0x48, 0xbb, 0xf4, 0x91, 0x45, 0xe9, 0xd4, 0x82,
	0x41, 0x42, 0x69, 0xfa, 0x9f, 0x06, 0xac, 0xee, 0xa3, 0x9f, 0x46, 0x01, 0x2f, 0x86, 0x68, 0x46,
	0x1e, 0xa2, 0x15, 0x12, 0x88, 0x16, 0x02, 0x17, 0x62, 0xa5, 0x04, 0x3c, 0x05, 0x5c, 0x08, 0x96,
	0x09, 0x64, 0x2b, 0xa5, 0x90, 0xed, 0x2e, 0x80, 0x3d, 0xb4, 0xbd, 0xae, 0x84, 0x37, 0x89, 0x7c,
	0x15, 0x41, 0x79, 0x86, 0x10, 0x97, 0x80, 0xa9, 0x85, 0x14, 0x4c, 0xfd, 0xde, 0x80, 0x5b, 0x72,
	0x46, 0x7b, 0x26, 0xb7, 0x7b, 0xc1, 0xea, 0xf9, 0x48, 0x60, 0x44, 0x90, 0x20, 0x0e, 0x1e, 0x85,
	0x24, 0x78, 0x24, 0xb6, 0x6d, 0x31, 0xb5, 0x6d, 0x03, 0x30, 0x28, 0xe1, 0x31, 0xa6, 0xc0, 0xe0,
	0x21, 0xac, 0xe0, 0xb0, 0xc8, 0xb6, 0x9d, 0xc7, 0x65, 0xae, 0x09, 0xf2, 0x81, 0xbf, 0x75, 0xe9,
	0x07, 0xb0, 0x96, 0x56, 0x34, 0x17, 0x21, 0xe9, 0xef, 0x0b, 0xb0, 0x12, 0x5d, 0xae, 0xac, 0x89,
	0x25, 0x34, 0x2f, 0x64, 0x6b, 0x5e, 0x8c, 0x6a, 0x9e, 0x79, 0x1e, 0xc5, 0x1c, 0x62, 0x3e, 0xcf,
	0x21, 0x16, 0x12, 0x0e, 0x41, 0xa0, 0x84, 0x9e, 0xb0, 0x28, 0xb5, 0x13, 0xdf, 0x82, 0x16, 0xdc,
	0x23, 0x6b, 0x1d, 0xfc, 0x4e, 0x2c, 0x7b, 0x65, 0xca, 0xb2, 0x43, 0x72, 0xd9, 0xc9, 0x32, 0x14,
	0x6c, 0x17, 0xb1, 0xb3, 0xd2, 0x29, 0xd8, 0x2e, 0x7d, 0x1f, 0xea, 0x71, 0x43, 0xe5, 0x1b, 0xf6,
	0x03, 0x58, 0x3b, 0xb0, 0xf9, 0xc0, 0xe6, 0x3c, 0xee, 0x35, 0x39, 0x43, 0x6e, 0xc3, 0xba, 0x66,
	0x08, 0x77, 0xe9, 0x3f, 0x16, 0x60, 0x59, 0xca, 0x8e, 0xb1, 0xc9, 0x88, 0xd5, 0x22, 0x36, 0x2f,
	0xc4, 0x6c, 0xae, 0x70, 0xa0, 0x18, 0x4b, 0x95, 0xa0, 0x8d, 0x4b, 0x99, 0x2e, 0x3c, 0x3f, 0xc5,
	0x85, 0x17, 0x52, 0x8e, 0xf0, 0x34, 0x71, 0x7e, 0x2c, 0x22, 0x62, 0xbe, 0x95, 0x06, 0x91, 0x14,
	0x4a, 0xc4, 0x4f, 0x11, 0x8d, 0xd3, 0x97, 0x75, 0x4e, 0xff, 0x9e, 0xef, 0xc0, 0x33, 0x2d, 0xcb,
	0x3e, 0x90, 0x0b, 0xd7, 0xf2, 0x7b, 0x0b, 0x87, 0x9a, 0x7e, 0xff, 0x8f, 0xe0, 0x12, 0x7e, 0xd3,
	0x75, 0xb8, 0x95, 0x62, 0xc2, 0x5d, 0xda, 0x0d, 0xe2, 0xea, 0xef, 0x10, 0xf7, 0x46, 0x63, 0xd9,
	0x62, 0x22, 0x96, 0xbd, 0x0d, 0xeb, 0x1a, 0x01, 0xdc, 0xa5, 0x47, 0xd0, 0x38, 0x63, 0x1e, 0x86,
	0xf3, 0x98, 0xb9, 0x39, 0x77, 0x5e, 0x2f, 0xaa, 0xf7, 0x19, 0x85, 0xec, 0xcf, 0x9d, 0x53, 0xb1,
	0x91, 0x6f, 0x9c, 0xe0, 0xd8, 0x84, 0x3b, 0x19, 0x8c, 0x42, 0x75, 0xc5, 0x8f, 0x48, 0x4c, 0xaa,
	0xfb, 0x3a, 0x52, 0x12, 0x8c, 0xe2, 0x52, 0x7c, 0x15, 0xd0, 0x38, 0x37, 0x96, 0x12, 0x61, 0xa4,
	0xb5, 0xee, 0xeb, 0xa8, 0x9b, 0x60, 0xc4, 0x5d, 0x01, 0x18, 0x47, 0x6a, 0x0d, 0x8f, 0xfc, 0x10,
	0x20, 0x7f, 0xfd, 0xe8, 0x1e, 0xdc, 0x3a, 0x62, 0xde, 0x05, 0x67, 0xa3, 0xd8, 0x08, 0xfd, 0x1d,
	0x5c, 0x1f, 0x07, 0xd3, 0xcf, 0xa0, 0x16, 0x19, 0xcc, 0x5d, 0x11, 0xf2, 0x27, 0x82, 0x92, 0x99,
	0xa2, 0x2c, 0x3c, 0xfc, 0x3f, 0x84, 0xdb, 0x07, 0xac, 0x8f, 0x73, 0x68, 0x0f, 0xaf, 0xcd, 0xbe,
	0x6d, 0xcd, 0x00, 0x58, 0xf4, 0x09, 0x34, 0xf4, 0xa3, 0xe4, 0x56, 0xb6, 0x58, 0x3f, 0x1a, 0x20,
	0x2d, 0x5a, 0xac, 0x8f, 0xc2, 0x9e, 0xa8, 0x64, 0x70, 0xdb, 0x0a, 0x74, 0xa7, 0x50, 0xf3, 0x57,
	0x23, 0x3a, 0xa4, 0x7a, 0x15, 0xf6, 0xa3, 0xbf, 0x86, 0x46, 0x64, 0xf3, 0xaa, 0x9b, 0xcb, 0xd8,
	0x63, 0x37, 0xde, 0xa9, 0x04, 0x4a, 0x18, 0x5e, 0xcb, 0x88, 0x0b, 0xbf, 0x05, 0x3a, 0x8a, 0xff,
	0xbb, 0x9c, 0xf5, 0x9c, 0xa1, 0x7f, 0xe6, 0x81, 0x20, 0x9d, 0x21, 0x45, 0xb8, 0x40, 0x86, 0x74,
	0xee, 0xd2, 0x9f, 0x6b, 0x54, 0xdb, 0x37, 0x47, 0xd6, 0xeb, 0xa8, 0x86, 0x89, 0x6b, 0x19, 0x2d,
	0xe1, 0xb7, 0x56, 0xb2, 0x64, 0xce, 0x5d, 0xfa, 0x3c, 0x26, 0xf9, 0x24, 0x40, 0xf3, 0x29, 0x92,
	0xa7, 0x45, 0x05, 0x09, 0xa1, 0x51, 0xbe, 0xdc, 0xa5, 0xc7, 0xb0, 0x1e, 0xf9, 0xb1, 0x25, 0xaf,
	0x27, 0x53, 0x24, 0x46, 0xef, 0x35, 0x32, 0x1c, 0xf6, 0xef, 0x35, 0xb4, 0x01, 0x1b, 0x3a, 0x76,
	0xdc, 0xa5, 0x3f, 0x81, 0x15, 0x75, 0xb0, 0xce, 0x70, 0x0c, 0x6b, 0xf6, 0x2d, 0x81, 0x7a, 0x7c,
	0x3c, 0x77, 0xe9, 0x6f, 0x0d, 0x58, 0xf7, 0x37, 0xf3, 0x89, 0xe3, 0x75, 0x64, 0xfa, 0x7e, 0x0a,
	0xeb, 0x64, 0xee, 0xbf, 0x90, 0xcc, 0xfd, 0xc7, 0xb6, 0x49, 0x51, 0x8b, 0xd6, 0x91, 0x30, 0xbe,
	0x01, 0x1b, 0x3a, 0x15, 0xb8, 0x4b, 0x39, 0xbc, 0x21, 0x93, 0x3b, 0x8e, 0x65, 0x5f, 0x4e, 0x8e,
	0xcc, 0x81, 0x34, 0xca, 0xe9, 0xc8, 0x9a, 0x7a, 0x28, 0x88, 0x3d, 0x24, 0xc2, 0x87, 0x48, 0xd2,
	0x4e, 0xee, 0x21, 0x9f, 0x89, 0xcc, 0x37, 0x39, 0x82, 0x55, 0xa3, 0x88, 0xbf, 0xc9, 0x06, 0xbd,
	0x07, 0x77, 0x73, 0x84, 0x72, 0x97, 0xee, 0xc2, 0xba, 0xc4, 0x37, 0x1f, 0xb3, 0xa7, 0xab, 0x43,
	0x3f, 0x51, 0xd9, 0x55, 0xec, 0x9b, 0x67, 0xdb, 0xac, 0xb0, 0x87, 0x7e, 0x03, 0x1b, 0x3a, 0xa9,
	0x58, 0x4f, 0x48, 0x43, 0x5d, 0x56, 0x82, 0x51, 0x8e, 0x8a, 0xdc, 0xce, 0xb6, 0xf0, 0xe2, 0xc5,
	0x99, 0xd7, 0x95, 0xa6, 0x90, 0xde, 0x08, 0x36, 0x3f, 0x63, 0x1e, 0x76, 0xa6, 0x6d, 0x68, 0xe2,
	0xd0, 0xfd, 0x17, 0xac, 0xf7, 0xf2, 0xc2, 0xb6, 0x66, 0xde, 0x56, 0xba, 0x7a, 0xc1, 0x66, 0x26,
	0x2b, 0xee, 0x8a, 0x1b, 0xb7, 0xba, 0x04, 0xfa, 0x5b, 0xd1, 0x88, 0x5e, 0x15, 0x55, 0x64, 0x76,
	0x1f, 0x96, 0x5e, 0xda, 0xbd, 0x97, 0xcc, 0x52, 0x69, 0x0a, 0x95, 0xa8, 0x92, 0x34, 0x4c, 0x53,
	0x88, 0xa3, 0x0e, 0xe5, 0x9c, 0x31, 0xef, 0xbb, 0x29, 0xbc, 0x09, 0x77, 0x32, 0x18, 0x71, 0x97,
	0x7e, 0x08, 0x5b, 0x32, 0xec, 0x53, 0xa7, 0x57, 0x24, 0x7e, 0x43, 0x35, 0xb4, 0x87, 0x18, 0x3d,
	0x80, 0xfb, 0x53, 0x46, 0x71, 0x4c, 0x3a, 0xa9, 0xc2, 0x1d, 0x4e, 0x51, 0x0e, 0x97, 0xab, 0x2c,
	0x67, 0xf8, 0xcf, 0x06, 0xdc, 0x92, 0x0e, 0x2a, 0x99, 0x89, 0x3b, 0xe4, 0x94, 0xd9, 0xc5, 0xb3,
	0x75, 0x85, 0xdc, 0x6c, 0x5d, 0x31, 0xef, 0x2a, 0x53, 0xca, 0xce, 0xd6, 0xcd, 0xc7, 0x22, 0x75,
	0x6d, 0x32, 0x8e, 0x6e, 0xc0, 0x5a, 0x5a, 0x73, 0xee, 0xd2, 0x43, 0xb8, 0x7d, 0xc4, 0x3c, 0x49,
	0xdc, 0x9b, 0x04, 0x91, 0xb1, 0x98, 0xd5, 0x0e, 0xac, 0xfa, 0xb7, 0xe7, 0x30, 0x8c, 0x96, 0xd3,
	0x5b, 0x51, 0x77, 0xe8, 0x20, 0x90, 0xbe, 0xc0, 0x02, 0x99, 0x86, 0x0d, 0x77, 0x83, 0xcc, 0x8b,
	0x68, 0xcc, 0x9e, 0x79, 0x41, 0xed, 0x9e, 0xc1, 0x9b, 0xd1, 0x20, 0xa5, 0x7d, 0x20, 0x76, 0xcf,
	0x9e, 0x4c, 0xc8, 0xb7, 0x0f, 0xf4, 0xf1, 0x4a, 0x03, 0xd4, 0xfe, 0x3f, 0x88, 0x87, 0x2b, 0x07,
	0xb4, 0x0f, 0xf7, 0x72, 0xb9, 0x71, 0x57, 0xb8, 0xb9, 0x4c, 0x49, 0x68, 0x62, 0x00, 0x1b, 0x63,
	0x00, 0xbc, 0x5b, 0x38, 0x5e, 0x17, 0xb1, 0x18, 0xe9, 0x7e, 0x7d, 0xaf, 0x36, 0x94, 0x38, 0x8a,
	0xcc, 0x39, 0xfd, 0x1b, 0x03, 0x6e, 0x9f, 0x2b, 0x3f, 0x36, 0x47, 0xbd, 0x17, 0x7b, 0x88, 0x6a,
	0xd2, 0xb4, 0x91, 0xc5, 0x93, 0x12, 0xb4, 0x57, 0xdb, 0x64, 0xf6, 0x56, 0xac, 0x2c, 0xa6, 0xbb,
	0x8a, 0x6a, 0x65, 0x45, 0x43, 0x0c, 0xb1, 0x79, 0xd7, 0xec, 0x79, 0xf6, 0xb5, 0x9f, 0xc1, 0x28,
	0xdb, 0xbc, 0x85, 0x6d, 0x7a, 0x0e, 0xeb, 0xca, 0xef, 0x43, 0x04, 0xc6, 0x59, 0xc4, 0x34, 0x88,
	0xba, 0x4f, 0x2a, 0x0c, 0x2a, 0xa4, 0xc3, 0xa0, 0xaf, 0xa0, 0xa1, 0x9f, 0x19, 0x77, 0xc9, 0x53,
	0x0d, 0x2e, 0xbe, 0x93, 0x95, 0x00, 0x4c, 0x68, 0x15, 0x0d, 0x07, 0x3f, 0x80, 0xbb, 0xb1, 0x1d,
	0xfb, 0x53, 0xc7, 0x1e, 0xe6, 0x47, 0xaa, 0xf4, 0x37, 0xf0, 0x66, 0xde, 0x10, 0xee, 0x92, 0xc7,
	0xb0, 0xa6, 0x2e, 0xee, 0xba, 0x50, 0x6f, 0xb5, 0x17, 0xa2, 0x82, 0x32, 0xd3, 0xbb, 0x40, 0xbe,
	0x76, 0xec, 0x61, 0x57, 0x67, 0x92, 0x95, 0xaf, 0x7d, 0xfe, 0xca, 0x2c, 0x1f, 0xe2, 0x5e, 0x3a,
	0xf2, 0x4b, 0xf9, 0x42, 0x85, 0xe9, 0xc5, 0x77, 0x11, 0xc1, 0xea, 0x47, 0xc9, 0x08, 0x76, 0x1c,
	0xd7, 0x71, 0x71, 0x2c, 0xdd, 0x90, 0x3e, 0xc1, 0x90, 0xbf, 0xd5, 0xef, 0xc7, 0xe2, 0xd8, 0x57,
	0x89, 0x2b, 0xb8, 0x91, 0xb8, 0x82, 0xd3, 0x1f, 0xe0, 0xf3, 0x80, 0xe4, 0x30, 0xee, 0x4e, 0xc9,
	0x27, 0xd2, 0xdf, 0x19, 0xf0, 0xb6, 0x3c, 0x40, 0xfc, 0xed, 0x83, 0xb7, 0x90, 0xf6, 0xf0, 0xd2,
	0x69, 0x0f, 0x7d, 0x76, 0x53, 0xa2, 0x83, 0xe8, 0x6c, 0x0a, 0xb1, 0xd9, 0x88, 0x4d, 0xc5, 0xbe,
	0xed, 0x31, 0xd7, 0xeb, 0x26, 0x6a, 0x9a, 0x35, 0x49, 0x56, 0xda, 0xd2, 0x5f, 0xc3, 0x1a, 0xf7,
	0xd2, 0x2a, 0x68, 0x60, 0x60, 0x1b, 0xea, 0x98, 0x46, 0xea, 0x46, 0x66, 0x25, 0x85, 0x2e, 0x23,
	0x3d, 0x70, 0x0c, 0xd1, 0x13, 0x73, 0x9d, 0xd1, 0x9e, 0x32, 0x36, 0x59, 0x46, 0x7a, 0xd0, 0x93,
	0xfe, 0xbb, 0x01, 0x0f, 0x66, 0x30, 0x82, 0xca, 0x22, 0x64, 0x58, 0xe1, 0x17, 0xb0, 0x31, 0x4e,
	0x0e, 0x0f, 0xd5, 0xab, 0xee, 0x3e, 0x4c, 0x6f, 0x16, 0xdd, 0x94, 0x3b, 0xeb, 0x29, 0x2e, 0x37,
	0xb2, 0x64, 0x2f, 0x88, 0xae, 0xf1, 0xe6, 0xef, 0xba, 0xfd, 0xc9, 0xe1, 0xb7, 0x3d, 0xc6, 0xac,
	0x9b, 0x56, 0xee, 0x73, 0xc2, 0x4e, 0xfa, 0x06, 0x34, 0xb3, 0x84, 0x70, 0x37, 0xa2, 0x42, 0x50,
	0x79, 0xfd, 0x0e, 0x2a, 0xe8, 0x6b, 0xe1, 0x11, 0x15, 0x52, 0x42, 0xb8, 0x4b, 0xff, 0x14, 0xeb,
	0xd1, 0xe7, 0xca, 0x28, 0x89, 0x03, 0xf0, 0x1e, 0x54, 0xc3, 0x93, 0x8f, 0xab, 0x3d, 0x01, 0x41,
	0xb5, 0x83, 0xd3, 0x0e, 0x6c, 0xa8, 0xe3, 0xd4, 0xa7, 0x1d, 0x9b, 0xae, 0x6b, 0x0f, 0xaf, 0x12,
	0x75, 0x12, 0x23, 0x59, 0x27, 0xc9, 0xa9, 0x6b, 0x7b, 0x58, 0xd7, 0xd6, 0x6a, 0xc4, 0x5d, 0xf2,
	0x1c, 0x88, 0x17, 0x60, 0xd1, 0x40, 0x0a, 0xf3, 0x1f, 0xb4, 0x6c, 0x67, 0xa1, 0x6c, 0x52, 0xbb,
	0x4e, 0xdd, 0x53, 0xd2, 0x14, 0x81, 0xd3, 0x9f, 0xc8, 0x04, 0x82, 0xa4, 0xf2, 0xbd, 0x89, 0xc0,
	0x0a, 0x61, 0x82, 0x87, 0xb0, 0x12, 0xa2, 0x49, 0x14, 0x1a, 0x6a, 0x01, 0xa4, 0xe0, 0xce, 0xf8,
	0x2c, 0xb8, 0x4f, 0xcb, 0x91, 0xf9, 0xa5, 0xb5, 0xfc, 0x4c, 0x37, 0xfd, 0x2a, 0x7c, 0xc2, 0x14,
	0x51, 0x86, 0xbb, 0xa4, 0xed, 0x6b, 0x33, 0x62, 0x3c, 0x76, 0xc0, 0x64, 0x55, 0x64, 0x42, 0x75,
	0x94, 0xc2, 0xe2, 0x53, 0x1d, 0x2f, 0x6b, 0x81, 0x99, 0x05, 0xec, 0xce, 0x00, 0xd4, 0x3f, 0x46,
	0xb5, 0x92, 0x43, 0x64, 0xe6, 0x00, 0x2f, 0xee, 0x09, 0xa8, 0xc6, 0xdb, 0xfc, 0x85, 0x82, 0xeb,
	0xe7, 0x70, 0x6b, 0xff, 0x85, 0x39, 0xbc, 0x62, 0x2a, 0x6b, 0xe3, 0xf4, 0x6f, 0x90, 0x34, 0x28,
	0xc5, 0x1f, 0xa7, 0x15, 0xc3, 0xf2, 0xb5, 0x88, 0xeb, 0xd2, 0x7c, 0xb9, 0x4b, 0x5f, 0xc2, 0x66,
	0x32, 0xbd, 0xf3, 0x85, 0xed, 0xbd, 0xf0, 0xd7, 0x35, 0xb2, 0xbb, 0x14, 0xf3, 0xe4, 0x9a, 0x14,
	0xe3, 0xa9, 0xdb, 0x4d, 0xa8, 0x88, 0xa8, 0xa7, 0x67, 0xf6, 0x5e, 0xf8, 0x59, 0x8b, 0xf2, 0xd0,
	0xf1, 0xf6, 0x45, 0x9b, 0xfe, 0x93, 0x01, 0x6f, 0x64, 0x4b, 0xe3, 0x2e, 0xe9, 0xc0, 0x82, 0x0a,
	0x95, 0xe4, 0x7a, 0x7d, 0xac, 0x59, 0xaf, 0x9c, 0xf1, 0x72, 0x31, 0xf9, 0xe1, 0xd0, 0x1b, 0x4d,
	0x3a, 0x8a, 0x53, 0xf3, 0x23, 0xa8, 0x46, 0xc8, 0x62, 0x46, 0x2f, 0xd9, 0xc4, 0xc7, 0x8b, 0x97,
	0x0c, 0xab, 0x0a, 0xd7, 0x66, 0x7f, 0xec, 0x3b, 0x98, 0x6c, 0x7c, 0x5c, 0xf8, 0x91, 0x41, 0x07,
	0xb0, 0x74, 0xc4, 0x86, 0xad, 0x6b, 0xd3, 0x33, 0xa7, 0x25, 0xe2, 0x1e, 0xc0, 0xf2, 0x35, 0x1b,
	0x71, 0xdb, 0x19, 0x76, 0xdd, 0x11, 0xbb, 0xb4, 0xbf, 0x55, 0x51, 0x59, 0x4d, 0x51, 0x3f, 0x47,
	0xa2, 0x30, 0xcf, 0xa5, 0x33, 0xea, 0xb1, 0xee, 0x15, 0xf3, 0xcb, 0x68, 0x65, 0x24, 0x1c, 0xb1,
	0x21, 0xbd, 0x0f, 0xb5, 0x88, 0x38, 0xf9, 0xee, 0x70, 0x60, 0x3d, 0x51, 0x35, 0x12, 0xf1, 0xb9,
	0xf3, 0xaf, 0x06, 0x2c, 0x1d, 0x76, 0x8f, 0x3a, 0xa7, 0x17, 0x9f, 0x77, 0xb1, 0xae, 0xbd, 0x09,
	0xb7, 0xa3, 0xed, 0xee, 0xd1, 0x45, 0xfb, 0xd9, 0x41, 0xf7, 0xb8, 0xd5, 0x3e, 0xa9, 0xcf, 0x91,
	0x26, 0x6c, 0xc4, 0x7f, 0x6c, 0x1d, 0x1f, 0xca, 0xdf, 0x0c, 0xb2, 0x0e, 0xab, 0xa9, 0xdf, 0xea,
	0x85, 0x14, 0xf9, 0xfc, 0xf0, 0xf8, 0xf3, 0x7a, 0x91, 0xdc, 0x86, 0x5b, 0x71, 0x32, 0x7e, 0xd7,
	0x4b, 0x29, 0x11, 0xfb, 0xa7, 0xc7, 0xc7, 0x17, 0x27, 0xed, 0xf3, 0x2f, 0xeb, 0xf3, 0xa4, 0x01,
	0x6b, 0xb1, 0xdf, 0xda, 0x27, 0xcf, 0x5b, 0xcf, 0xda, 0x07, 0xf5, 0x95, 0x9d, 0x1d, 0xa8, 0x9c,
	0xf9, 0xcf, 0xf4, 0x48, 0x19, 0x4a, 0xc2, 0x1d, 0xeb, 0x73, 0x04, 0x60, 0x41, 0xc4, 0x62, 0x2d,
	0xaf, 0x6e, 0x90, 0x45, 0x28, 0x5e, 0xd8, 0x56, 0xbd, 0xb0, 0x73, 0x17, 0xca, 0x67, 0xea, 0xcd,
	0x9e, 0x20, 0xb6, 0xce, 0xf6, 0xeb, 0x73, 0x62, 0xcc, 0xc1, 0xe1, 0xd9, 0x7e, 0xdd, 0xd8, 0xf9,
	0x69, 0xfc, 0xb9, 0xa6, 0xd3, 0xc7, 0x5e, 0xb2, 0xcc, 0x5f, 0x81, 0xf9, 0xd3, 0x2f, 0x4e, 0x0e,
	0x3b, 0x75, 0x43, 0x7c, 0xb6, 0x0e, 0x8e, 0xdb, 0x27, 0xf5, 0x82, 0x90, 0x22, 0xb4, 0x3c, 0x3d,
	0xa9, 0x17, 0xc9, 0x12, 0x2c, 0x5e, 0x9c, 0x7c, 0x76, 0x72, 0xfa, 0xc5, 0x49, 0xfd, 0x0f, 0xc6,
	0xee, 0xbf, 0x50, 0x9f, 0x59, 0xe0, 0x6f, 0xe4, 0x1c, 0xaa, 0x91, 0x4b, 0x20, 0xd9, 0xca, 0xaa,
	0x31, 0xf8, 0x81, 0x4d, 0xf3, 0xfe, 0x94, 0x1e, 0xdc, 0xa5, 0x73, 0xe4, 0x0b, 0x58, 0x8a, 0x66,
	0x7b, 0x88, 0x66, 0x50, 0x22, 0x9b, 0xd4, 0xa4, 0xd3, 0xba, 0x20, 0xe3, 0x9e, 0x5f, 0x41, 0x0a,
	0xcb, 0x3b, 0xe4, 0x41, 0x96, 0x46, 0xb1, 0xaa, 0x51, 0xf3, 0xe1, 0x2c, 0xdd, 0x7c, 0xed, 0xa3,
	0x85, 0x15, 0x72, 0x3f, 0xbf, 0xf0, 0x92, 0xa1, 0x7d, 0xb2, 0xd2, 0x45, 0xe7, 0xc8, 0x25, 0xac,
	0xa6, 0xaa, 0x53, 0xe4, 0x61, 0xe6, 0xc4, 0xe3, 0xfa, 0xbf, 0x33, 0x53, 0x3f, 0x94, 0x73, 0x02,
	0x95, 0x60, 0xa7, 0x91, 0x37, 0x75, 0x20, 0x13, 0xee, 0xfa, 0xe6, 0xbd, 0xdc, 0xdf, 0x91, 0xdf,
	0xa7, 0x50, 0xf6, 0x4f, 0x22, 0x72, 0x57, 0x8b, 0x59, 0x81, 0x96, 0x79, 0x57, 0x5a, 0x3a, 0x47,
	0x7e, 0x06, 0xb5, 0x58, 0xd5, 0x9c, 0x68, 0x0c, 0x97, 0xac, 0xf1, 0x37, 0xdf, 0x9a, 0xda, 0x07,
	0x79, 0xff, 0x12, 0x56, 0x12, 0x25, 0x25, 0xf2, 0x76, 0x7a, 0x64, 0xba, 0x74, 0xd5, 0x7c, 0x30,
	0x43, 0x2f, 0x94, 0x30, 0x8a, 0x65, 0x5b, 0x23, 0x69, 0xa1, 0x9d, 0x7c, 0x0e, 0xd1, 0x1c, 0x50,
	0xf3, 0xdd, 0x99, 0xfb, 0x2a, 0x9f, 0x21, 0xe9, 0x87, 0xcc, 0xe4, 0x9d, 0xec, 0x55, 0x88, 0x3d,
	0x77, 0x6e, 0xce, 0xf6, 0xcc, 0x02, 0x57, 0x66, 0x39, 0xce, 0x81, 0xbc, 0x35, 0x4d, 0x46, 0x06,
	0x1c, 0x24, 0x2b, 0x6b, 0x73, 0xa4, 0x9f, 0x7c, 0x8c, 0xad, 0xde, 0x0d, 0xeb, 0xec, 0x96, 0xf5,
	0xc0, 0x78, 0xf6, 0x99, 0xfc, 0x02, 0x96, 0xe3, 0xcf, 0xd5, 0x74, 0x33, 0x49, 0x3d, 0xad, 0x6b,
	0xbe, 0x3d, 0xbd, 0x93, 0xbf, 0x89, 0x53, 0x15, 0x44, 0xdd, 0x26, 0xd6, 0xd5, 0x31, 0x75, 0x9b,
	0x58, 0x5f, 0x8e, 0x94, 0x50, 0x97, 0x08, 0x69, 0xb4, 0x50, 0x97, 0x0e, 0xa7, 0xb4, 0x50, 0xa7,
	0x8b, 0x8e, 0xd0, 0xa3, 0xb5, 0x35, 0x46, 0xdd, 0xca, 0x64, 0x55, 0x35, 0x75, 0x1e, 0x9d, 0x5d,
	0xb8, 0x8c, 0xc9, 0x8c, 0x95, 0xf0, 0xf2, 0x64, 0x26, 0x8b, 0x86, 0x79, 0x32, 0xd3, 0x75, 0xc1,
	0x98, 0xcc, 0x58, 0x95, 0x33, 0x4f, 0x66, 0xb2, 0xae, 0x9a, 0x27, 0x33, 0x5d, 0x3a, 0x9d, 0x23,
	0x7f, 0x85, 0x19, 0x0d, 0x6d, 0x34, 0x47, 0xde, 0xbf, 0x49, 0xe4, 0xf7, 0xaa, 0xf9, 0xe8, 0x66,
	0x81, 0x22, 0x9d, 0x23, 0x7f, 0x01, 0x10, 0x3e, 0x59, 0x25, 0x1a, 0x8c, 0x8f, 0xbd, 0xa8, 0x6d,
	0x6e, 0xe5, 0x77, 0x50, 0xc7, 0x62, 0x2d, 0xf6, 0xd4, 0x58, 0x87, 0xdd, 0xc9, 0xc7, 0xef, 0x3a,
	0xc6, 0xf1, 0xe7, 0xed, 0xd2, 0xd3, 0x93, 0x8f, 0x6c, 0x75, 0x9e, 0xae, 0x79, 0x09, 0xdc, 0x7c,
	0x38, 0x4b, 0x37, 0x14, 0x62, 0x03, 0x49, 0x17, 0x7a, 0x74, 0x38, 0xaa, 0xad, 0x48, 0x35, 0xb7,
	0x67, 0xeb, 0xa8, 0x39, 0x26, 0xc2, 0x32, 0xe1, 0x94, 0x63, 0x22, 0x56, 0xac, 0x9c, 0x72, 0x4c,
	0x24, 0x6a, 0x8f, 0x7a, 0x99, 0xf8, 0x54, 0x71, 0x16, 0x99, 0xaa, 0x68, 0x38, 0x93, 0xcc, 0xa0,
	0x22, 0x88, 0x26, 0x4d, 0x57, 0x0b, 0x75, 0x26, 0xd5, 0x96, 0x28, 0x75, 0x26, 0xcd, 0x28, 0x3e,
	0xce, 0x11, 0x27, 0xbc, 0x98, 0x47, 0xb3, 0x83, 0xe4, 0x4f, 0x72, 0x0e, 0x90, 0x78, 0xee, 0xb1,
	0xb9, 0x33, 0x6b, 0x57, 0x14, 0xf8, 0x3b, 0x23, 0x7e, 0x73, 0x4c, 0x64, 0xc9, 0xc9, 0xf7, 0xf2,
	0x77, 0x64, 0x3a, 0x45, 0xdf, 0xfc, 0xe0, 0x86, 0x23, 0x50, 0x8d, 0xbf, 0xc4, 0xbf, 0x27, 0x89,
	0x6d, 0x74, 0xdd, 0xd6, 0xd0, 0x3c, 0x61, 0xd0, 0xc6, 0x75, 0xd1, 0x2c, 0xb0, 0xb4, 0xaa, 0xee,
	0xd5, 0x80, 0xce, 0xaa, 0x19, 0x6f, 0x12, 0x74, 0x56, 0xcd, 0x7a, 0x88, 0x80, 0x21, 0xda, 0x6a,
	0xea, 0x81, 0x86, 0xee, 0xec, 0xd4, 0xbd, 0xe2, 0x68, 0x66, 0x67, 0x37, 0x22, 0x53, 0xfa, 0x0d,
	0xdc, 0xc9, 0x2c, 0xa0, 0x12, 0x1d, 0x8c, 0xe6, 0x94, 0x78, 0x9b, 0x8f, 0x6f, 0xd4, 0xdf, 0xdf,
	0x13, 0xe9, 0x4a, 0xa9, 0x36, 0x5c, 0xd3, 0x55, 0x71, 0x75, 0x7b, 0x42, 0x5f, 0x78, 0xa5, 0x73,
	0xe4, 0xef, 0x8c, 0x44, 0x6d, 0x20, 0x59, 0xcd, 0x23, 0xbb, 0x99, 0x05, 0x87, 0xcc, 0xa2, 0x61,
	0xf3, 0xfb, 0x37, 0x1e, 0xe3, 0x63, 0x78, 0xb2, 0xb0, 0xa6, 0x73, 0x54, 0x4d, 0xd9, 0x50, 0x87,
	0xe1, 0xda, 0x1a, 0x9d, 0x8f, 0x02, 0xa9, 0xf2, 0x5a, 0x06, 0x0a, 0xe8, 0xaa, 0x79, 0x19, 0x28,
	0xa0, 0xad, 0xd8, 0xd1, 0x39, 0xf2, 0x5b, 0x03, 0x9a, 0xd9, 0xb5, 0x14, 0xf2, 0x78, 0x8a, 0xad,
	0x92, 0xc5, 0x9a, 0xe6, 0xf7, 0x6e, 0x36, 0x00, 0x75, 0xf8, 0x07, 0x03, 0xee, 0x4f, 0xcd, 0xb6,
	0x93, 0x1f, 0xe8, 0x42, 0xbe, 0xe9, 0x75, 0x8a, 0xe6, 0x0f, 0x5f, 0x6b, 0x1c, 0x2a, 0x36, 0x8e,
	0x3f, 0x16, 0x09, 0x53, 0xca, 0x24, 0xff, 0x1c, 0x89, 0x67, 0xb8, 0x9b, 0xef, 0xcd, 0xde, 0xd9,
	0x17, 0xab, 0xff, 0x8b, 0x27, 0x9d, 0xd8, 0xcc, 0xbf, 0xb2, 0xd2, 0x89, 0xcd, 0xfe, 0x43, 0x2a,
	0x3a, 0xb7, 0xf7, 0xf1, 0xcf, 0x7e, 0x74, 0xe5, 0xf4, 0xcd, 0xe1, 0xd5, 0xa3, 0x27, 0xbb, 0x9e,
	0xf7, 0xa8, 0xe7, 0x0c, 0x1e, 0xe3, 0xdf, 0xd8, 0xf6, 0x9c, 0xfe, 0x63, 0xce, 0x46, 0xd7, 0x76,
	0x8f, 0xf1, 0xc7, 0x89, 0x3f, 0xce, 0x55, 0x8c, 0xbf, 0x5a, 0xc0, 0x9e, 0xdf, 0xff, 0xbf, 0x00,
	0x00, 0x00, 0xff, 0xff, 0xcb, 0x36, 0x7f, 0x74, 0xbd, 0x3b, 0x00, 0x00,
}
