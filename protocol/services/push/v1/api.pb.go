// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api.proto

package push

import (
	context "context"
	fmt "fmt"
	math "math"

	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type ProxyNotification_Type int32

const (
	ProxyNotification_INVALID ProxyNotification_Type = 0
	// 使用CMD_Notify进行推送, 仅限TT使用(appId将被忽略)
	ProxyNotification_NOTIFY ProxyNotification_Type = 1
	// 使用CMD_Push进行推送, 仅限TT使用(appId将被忽略), 消息体为ga.PushMessage
	ProxyNotification_PUSH ProxyNotification_Type = 2
	// 使用CMD_TransmissionPush透传, 并用TransmissionPacket对payload进行包装
	ProxyNotification_TRANSMISSION_PUSH ProxyNotification_Type = 3
	// 使用CMD_KICKOUT进行推送, 仅限TT使用(appId将被忽略)
	ProxyNotification_KICKOUT ProxyNotification_Type = 4
)

var ProxyNotification_Type_name = map[int32]string{
	0: "INVALID",
	1: "NOTIFY",
	2: "PUSH",
	3: "TRANSMISSION_PUSH",
	4: "KICKOUT",
}

var ProxyNotification_Type_value = map[string]int32{
	"INVALID":           0,
	"NOTIFY":            1,
	"PUSH":              2,
	"TRANSMISSION_PUSH": 3,
	"KICKOUT":           4,
}

func (x ProxyNotification_Type) String() string {
	return proto.EnumName(ProxyNotification_Type_name, int32(x))
}

func (ProxyNotification_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{0, 0}
}

type ProxyNotification_Policy int32

const (
	ProxyNotification_DEFAULT  ProxyNotification_Policy = 0
	ProxyNotification_RELIABLE ProxyNotification_Policy = 1
)

var ProxyNotification_Policy_name = map[int32]string{
	0: "DEFAULT",
	1: "RELIABLE",
}

var ProxyNotification_Policy_value = map[string]int32{
	"DEFAULT":  0,
	"RELIABLE": 1,
}

func (x ProxyNotification_Policy) String() string {
	return proto.EnumName(ProxyNotification_Policy_name, int32(x))
}

func (ProxyNotification_Policy) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{0, 1}
}

type ProxyNotification struct {
	Type    uint32                   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Payload []byte                   `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	Policy  ProxyNotification_Policy `protobuf:"varint,3,opt,name=policy,proto3,enum=push.v1.ProxyNotification_Policy" json:"policy,omitempty"`
	// unix timestamp, not ttl, default is server time + 3600s
	ExpireTime           uint32   `protobuf:"varint,4,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	Sync                 bool     `protobuf:"varint,5,opt,name=sync,proto3" json:"sync,omitempty"`
	Priority             uint32   `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProxyNotification) Reset()         { *m = ProxyNotification{} }
func (m *ProxyNotification) String() string { return proto.CompactTextString(m) }
func (*ProxyNotification) ProtoMessage()    {}
func (*ProxyNotification) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{0}
}

func (m *ProxyNotification) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProxyNotification.Unmarshal(m, b)
}
func (m *ProxyNotification) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProxyNotification.Marshal(b, m, deterministic)
}
func (m *ProxyNotification) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProxyNotification.Merge(m, src)
}
func (m *ProxyNotification) XXX_Size() int {
	return xxx_messageInfo_ProxyNotification.Size(m)
}
func (m *ProxyNotification) XXX_DiscardUnknown() {
	xxx_messageInfo_ProxyNotification.DiscardUnknown(m)
}

var xxx_messageInfo_ProxyNotification proto.InternalMessageInfo

func (m *ProxyNotification) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ProxyNotification) GetPayload() []byte {
	if m != nil {
		return m.Payload
	}
	return nil
}

func (m *ProxyNotification) GetPolicy() ProxyNotification_Policy {
	if m != nil {
		return m.Policy
	}
	return ProxyNotification_DEFAULT
}

func (m *ProxyNotification) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *ProxyNotification) GetSync() bool {
	if m != nil {
		return m.Sync
	}
	return false
}

func (m *ProxyNotification) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

type CompositiveNotification struct {
	Sequence             uint32              `protobuf:"varint,1,opt,name=sequence,proto3" json:"sequence,omitempty"`
	TerminalTypeList     []uint32            `protobuf:"varint,2,rep,packed,name=terminal_type_list,json=terminalTypeList,proto3" json:"terminal_type_list,omitempty"`
	AppId                uint32              `protobuf:"varint,3,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	ProxyNotification    *ProxyNotification  `protobuf:"bytes,4,opt,name=proxy_notification,json=proxyNotification,proto3" json:"proxy_notification,omitempty"`
	ClientTimestamp      uint32              `protobuf:"varint,7,opt,name=client_timestamp,json=clientTimestamp,proto3" json:"client_timestamp,omitempty"`
	TerminalTypePolicy   *TerminalTypePolicy `protobuf:"bytes,8,opt,name=terminal_type_policy,json=terminalTypePolicy,proto3" json:"terminal_type_policy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *CompositiveNotification) Reset()         { *m = CompositiveNotification{} }
func (m *CompositiveNotification) String() string { return proto.CompactTextString(m) }
func (*CompositiveNotification) ProtoMessage()    {}
func (*CompositiveNotification) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{1}
}

func (m *CompositiveNotification) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompositiveNotification.Unmarshal(m, b)
}
func (m *CompositiveNotification) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompositiveNotification.Marshal(b, m, deterministic)
}
func (m *CompositiveNotification) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompositiveNotification.Merge(m, src)
}
func (m *CompositiveNotification) XXX_Size() int {
	return xxx_messageInfo_CompositiveNotification.Size(m)
}
func (m *CompositiveNotification) XXX_DiscardUnknown() {
	xxx_messageInfo_CompositiveNotification.DiscardUnknown(m)
}

var xxx_messageInfo_CompositiveNotification proto.InternalMessageInfo

func (m *CompositiveNotification) GetSequence() uint32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func (m *CompositiveNotification) GetTerminalTypeList() []uint32 {
	if m != nil {
		return m.TerminalTypeList
	}
	return nil
}

func (m *CompositiveNotification) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *CompositiveNotification) GetProxyNotification() *ProxyNotification {
	if m != nil {
		return m.ProxyNotification
	}
	return nil
}

func (m *CompositiveNotification) GetClientTimestamp() uint32 {
	if m != nil {
		return m.ClientTimestamp
	}
	return 0
}

func (m *CompositiveNotification) GetTerminalTypePolicy() *TerminalTypePolicy {
	if m != nil {
		return m.TerminalTypePolicy
	}
	return nil
}

type TerminalTypePolicy struct {
	PolicyGroup          string   `protobuf:"bytes,1,opt,name=policy_group,json=policyGroup,proto3" json:"policy_group,omitempty"`
	PolicyName           string   `protobuf:"bytes,2,opt,name=policy_name,json=policyName,proto3" json:"policy_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TerminalTypePolicy) Reset()         { *m = TerminalTypePolicy{} }
func (m *TerminalTypePolicy) String() string { return proto.CompactTextString(m) }
func (*TerminalTypePolicy) ProtoMessage()    {}
func (*TerminalTypePolicy) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{2}
}

func (m *TerminalTypePolicy) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TerminalTypePolicy.Unmarshal(m, b)
}
func (m *TerminalTypePolicy) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TerminalTypePolicy.Marshal(b, m, deterministic)
}
func (m *TerminalTypePolicy) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TerminalTypePolicy.Merge(m, src)
}
func (m *TerminalTypePolicy) XXX_Size() int {
	return xxx_messageInfo_TerminalTypePolicy.Size(m)
}
func (m *TerminalTypePolicy) XXX_DiscardUnknown() {
	xxx_messageInfo_TerminalTypePolicy.DiscardUnknown(m)
}

var xxx_messageInfo_TerminalTypePolicy proto.InternalMessageInfo

func (m *TerminalTypePolicy) GetPolicyGroup() string {
	if m != nil {
		return m.PolicyGroup
	}
	return ""
}

func (m *TerminalTypePolicy) GetPolicyName() string {
	if m != nil {
		return m.PolicyName
	}
	return ""
}

type GetTerminalTypePolicyResp struct {
	TerminalTypePolicyInfoList []*TerminalTypePolicyInfo `protobuf:"bytes,1,rep,name=terminal_type_policy_info_list,json=terminalTypePolicyInfoList,proto3" json:"terminal_type_policy_info_list,omitempty"`
	XXX_NoUnkeyedLiteral       struct{}                  `json:"-"`
	XXX_unrecognized           []byte                    `json:"-"`
	XXX_sizecache              int32                     `json:"-"`
}

func (m *GetTerminalTypePolicyResp) Reset()         { *m = GetTerminalTypePolicyResp{} }
func (m *GetTerminalTypePolicyResp) String() string { return proto.CompactTextString(m) }
func (*GetTerminalTypePolicyResp) ProtoMessage()    {}
func (*GetTerminalTypePolicyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{3}
}

func (m *GetTerminalTypePolicyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTerminalTypePolicyResp.Unmarshal(m, b)
}
func (m *GetTerminalTypePolicyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTerminalTypePolicyResp.Marshal(b, m, deterministic)
}
func (m *GetTerminalTypePolicyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTerminalTypePolicyResp.Merge(m, src)
}
func (m *GetTerminalTypePolicyResp) XXX_Size() int {
	return xxx_messageInfo_GetTerminalTypePolicyResp.Size(m)
}
func (m *GetTerminalTypePolicyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTerminalTypePolicyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTerminalTypePolicyResp proto.InternalMessageInfo

func (m *GetTerminalTypePolicyResp) GetTerminalTypePolicyInfoList() []*TerminalTypePolicyInfo {
	if m != nil {
		return m.TerminalTypePolicyInfoList
	}
	return nil
}

type SetTerminalTypePolicyResp struct {
	Success              bool     `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetTerminalTypePolicyResp) Reset()         { *m = SetTerminalTypePolicyResp{} }
func (m *SetTerminalTypePolicyResp) String() string { return proto.CompactTextString(m) }
func (*SetTerminalTypePolicyResp) ProtoMessage()    {}
func (*SetTerminalTypePolicyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{4}
}

func (m *SetTerminalTypePolicyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTerminalTypePolicyResp.Unmarshal(m, b)
}
func (m *SetTerminalTypePolicyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTerminalTypePolicyResp.Marshal(b, m, deterministic)
}
func (m *SetTerminalTypePolicyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTerminalTypePolicyResp.Merge(m, src)
}
func (m *SetTerminalTypePolicyResp) XXX_Size() int {
	return xxx_messageInfo_SetTerminalTypePolicyResp.Size(m)
}
func (m *SetTerminalTypePolicyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTerminalTypePolicyResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetTerminalTypePolicyResp proto.InternalMessageInfo

func (m *SetTerminalTypePolicyResp) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

type TerminalTypePolicyInfo struct {
	TerminalTypePolicy   *TerminalTypePolicy `protobuf:"bytes,1,opt,name=terminal_type_policy,json=terminalTypePolicy,proto3" json:"terminal_type_policy,omitempty"`
	TerminalTypeList     []uint32            `protobuf:"varint,2,rep,packed,name=terminal_type_list,json=terminalTypeList,proto3" json:"terminal_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *TerminalTypePolicyInfo) Reset()         { *m = TerminalTypePolicyInfo{} }
func (m *TerminalTypePolicyInfo) String() string { return proto.CompactTextString(m) }
func (*TerminalTypePolicyInfo) ProtoMessage()    {}
func (*TerminalTypePolicyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{5}
}

func (m *TerminalTypePolicyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TerminalTypePolicyInfo.Unmarshal(m, b)
}
func (m *TerminalTypePolicyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TerminalTypePolicyInfo.Marshal(b, m, deterministic)
}
func (m *TerminalTypePolicyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TerminalTypePolicyInfo.Merge(m, src)
}
func (m *TerminalTypePolicyInfo) XXX_Size() int {
	return xxx_messageInfo_TerminalTypePolicyInfo.Size(m)
}
func (m *TerminalTypePolicyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TerminalTypePolicyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TerminalTypePolicyInfo proto.InternalMessageInfo

func (m *TerminalTypePolicyInfo) GetTerminalTypePolicy() *TerminalTypePolicy {
	if m != nil {
		return m.TerminalTypePolicy
	}
	return nil
}

func (m *TerminalTypePolicyInfo) GetTerminalTypeList() []uint32 {
	if m != nil {
		return m.TerminalTypeList
	}
	return nil
}

type MulticastAccount struct {
	Id                   uint64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MulticastAccount) Reset()         { *m = MulticastAccount{} }
func (m *MulticastAccount) String() string { return proto.CompactTextString(m) }
func (*MulticastAccount) ProtoMessage()    {}
func (*MulticastAccount) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{6}
}

func (m *MulticastAccount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MulticastAccount.Unmarshal(m, b)
}
func (m *MulticastAccount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MulticastAccount.Marshal(b, m, deterministic)
}
func (m *MulticastAccount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MulticastAccount.Merge(m, src)
}
func (m *MulticastAccount) XXX_Size() int {
	return xxx_messageInfo_MulticastAccount.Size(m)
}
func (m *MulticastAccount) XXX_DiscardUnknown() {
	xxx_messageInfo_MulticastAccount.DiscardUnknown(m)
}

var xxx_messageInfo_MulticastAccount proto.InternalMessageInfo

func (m *MulticastAccount) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MulticastAccount) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type ProxyPushResult struct {
	Result               int32             `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	UserNotifyCountsMap  map[uint32]uint32 `protobuf:"bytes,2,rep,name=user_notify_counts_map,json=userNotifyCountsMap,proto3" json:"user_notify_counts_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ProxyPushResult) Reset()         { *m = ProxyPushResult{} }
func (m *ProxyPushResult) String() string { return proto.CompactTextString(m) }
func (*ProxyPushResult) ProtoMessage()    {}
func (*ProxyPushResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{7}
}

func (m *ProxyPushResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProxyPushResult.Unmarshal(m, b)
}
func (m *ProxyPushResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProxyPushResult.Marshal(b, m, deterministic)
}
func (m *ProxyPushResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProxyPushResult.Merge(m, src)
}
func (m *ProxyPushResult) XXX_Size() int {
	return xxx_messageInfo_ProxyPushResult.Size(m)
}
func (m *ProxyPushResult) XXX_DiscardUnknown() {
	xxx_messageInfo_ProxyPushResult.DiscardUnknown(m)
}

var xxx_messageInfo_ProxyPushResult proto.InternalMessageInfo

func (m *ProxyPushResult) GetResult() int32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *ProxyPushResult) GetUserNotifyCountsMap() map[uint32]uint32 {
	if m != nil {
		return m.UserNotifyCountsMap
	}
	return nil
}

type PushToUsersReq struct {
	UidList              []uint32                 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Notification         *CompositiveNotification `protobuf:"bytes,2,opt,name=notification,proto3" json:"notification,omitempty"`
	Sync                 bool                     `protobuf:"varint,3,opt,name=sync,proto3" json:"sync,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *PushToUsersReq) Reset()         { *m = PushToUsersReq{} }
func (m *PushToUsersReq) String() string { return proto.CompactTextString(m) }
func (*PushToUsersReq) ProtoMessage()    {}
func (*PushToUsersReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{8}
}

func (m *PushToUsersReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushToUsersReq.Unmarshal(m, b)
}
func (m *PushToUsersReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushToUsersReq.Marshal(b, m, deterministic)
}
func (m *PushToUsersReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushToUsersReq.Merge(m, src)
}
func (m *PushToUsersReq) XXX_Size() int {
	return xxx_messageInfo_PushToUsersReq.Size(m)
}
func (m *PushToUsersReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushToUsersReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushToUsersReq proto.InternalMessageInfo

func (m *PushToUsersReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *PushToUsersReq) GetNotification() *CompositiveNotification {
	if m != nil {
		return m.Notification
	}
	return nil
}

func (m *PushToUsersReq) GetSync() bool {
	if m != nil {
		return m.Sync
	}
	return false
}

type PushMulticastReq struct {
	MulticastAccount  *MulticastAccount        `protobuf:"bytes,1,opt,name=multicast_account,json=multicastAccount,proto3" json:"multicast_account,omitempty"`
	Notification      *CompositiveNotification `protobuf:"bytes,2,opt,name=notification,proto3" json:"notification,omitempty"`
	SkipUids          []uint32                 `protobuf:"varint,3,rep,packed,name=skip_uids,json=skipUids,proto3" json:"skip_uids,omitempty"`
	MulticastAccounts []*MulticastAccount      `protobuf:"bytes,4,rep,name=multicast_accounts,json=multicastAccounts,proto3" json:"multicast_accounts,omitempty"`
	// ignored.
	Sync                 bool     `protobuf:"varint,5,opt,name=sync,proto3" json:"sync,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushMulticastReq) Reset()         { *m = PushMulticastReq{} }
func (m *PushMulticastReq) String() string { return proto.CompactTextString(m) }
func (*PushMulticastReq) ProtoMessage()    {}
func (*PushMulticastReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{9}
}

func (m *PushMulticastReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushMulticastReq.Unmarshal(m, b)
}
func (m *PushMulticastReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushMulticastReq.Marshal(b, m, deterministic)
}
func (m *PushMulticastReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushMulticastReq.Merge(m, src)
}
func (m *PushMulticastReq) XXX_Size() int {
	return xxx_messageInfo_PushMulticastReq.Size(m)
}
func (m *PushMulticastReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushMulticastReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushMulticastReq proto.InternalMessageInfo

func (m *PushMulticastReq) GetMulticastAccount() *MulticastAccount {
	if m != nil {
		return m.MulticastAccount
	}
	return nil
}

func (m *PushMulticastReq) GetNotification() *CompositiveNotification {
	if m != nil {
		return m.Notification
	}
	return nil
}

func (m *PushMulticastReq) GetSkipUids() []uint32 {
	if m != nil {
		return m.SkipUids
	}
	return nil
}

func (m *PushMulticastReq) GetMulticastAccounts() []*MulticastAccount {
	if m != nil {
		return m.MulticastAccounts
	}
	return nil
}

func (m *PushMulticastReq) GetSync() bool {
	if m != nil {
		return m.Sync
	}
	return false
}

type PushToUsersResp struct {
	Sequence             uint32           `protobuf:"varint,1,opt,name=sequence,proto3" json:"sequence,omitempty"`
	ProxyPushResult      *ProxyPushResult `protobuf:"bytes,2,opt,name=proxy_push_result,json=proxyPushResult,proto3" json:"proxy_push_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PushToUsersResp) Reset()         { *m = PushToUsersResp{} }
func (m *PushToUsersResp) String() string { return proto.CompactTextString(m) }
func (*PushToUsersResp) ProtoMessage()    {}
func (*PushToUsersResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{10}
}

func (m *PushToUsersResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushToUsersResp.Unmarshal(m, b)
}
func (m *PushToUsersResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushToUsersResp.Marshal(b, m, deterministic)
}
func (m *PushToUsersResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushToUsersResp.Merge(m, src)
}
func (m *PushToUsersResp) XXX_Size() int {
	return xxx_messageInfo_PushToUsersResp.Size(m)
}
func (m *PushToUsersResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushToUsersResp.DiscardUnknown(m)
}

var xxx_messageInfo_PushToUsersResp proto.InternalMessageInfo

func (m *PushToUsersResp) GetSequence() uint32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func (m *PushToUsersResp) GetProxyPushResult() *ProxyPushResult {
	if m != nil {
		return m.ProxyPushResult
	}
	return nil
}

type PushMulticastResp struct {
	Sequence             uint32   `protobuf:"varint,1,opt,name=sequence,proto3" json:"sequence,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushMulticastResp) Reset()         { *m = PushMulticastResp{} }
func (m *PushMulticastResp) String() string { return proto.CompactTextString(m) }
func (*PushMulticastResp) ProtoMessage()    {}
func (*PushMulticastResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{11}
}

func (m *PushMulticastResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushMulticastResp.Unmarshal(m, b)
}
func (m *PushMulticastResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushMulticastResp.Marshal(b, m, deterministic)
}
func (m *PushMulticastResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushMulticastResp.Merge(m, src)
}
func (m *PushMulticastResp) XXX_Size() int {
	return xxx_messageInfo_PushMulticastResp.Size(m)
}
func (m *PushMulticastResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushMulticastResp.DiscardUnknown(m)
}

var xxx_messageInfo_PushMulticastResp proto.InternalMessageInfo

func (m *PushMulticastResp) GetSequence() uint32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

type Presence struct {
	ProxyIp              uint32   `protobuf:"varint,1,opt,name=proxy_ip,json=proxyIp,proto3" json:"proxy_ip,omitempty"`
	ProxyPort            uint32   `protobuf:"varint,2,opt,name=proxy_port,json=proxyPort,proto3" json:"proxy_port,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	ClientId             uint32   `protobuf:"varint,4,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	TerminalType         uint32   `protobuf:"varint,5,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Presence) Reset()         { *m = Presence{} }
func (m *Presence) String() string { return proto.CompactTextString(m) }
func (*Presence) ProtoMessage()    {}
func (*Presence) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{12}
}

func (m *Presence) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Presence.Unmarshal(m, b)
}
func (m *Presence) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Presence.Marshal(b, m, deterministic)
}
func (m *Presence) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Presence.Merge(m, src)
}
func (m *Presence) XXX_Size() int {
	return xxx_messageInfo_Presence.Size(m)
}
func (m *Presence) XXX_DiscardUnknown() {
	xxx_messageInfo_Presence.DiscardUnknown(m)
}

var xxx_messageInfo_Presence proto.InternalMessageInfo

func (m *Presence) GetProxyIp() uint32 {
	if m != nil {
		return m.ProxyIp
	}
	return 0
}

func (m *Presence) GetProxyPort() uint32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

func (m *Presence) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Presence) GetClientId() uint32 {
	if m != nil {
		return m.ClientId
	}
	return 0
}

func (m *Presence) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

type PushToPresenceListReq struct {
	PresenceList         []*Presence              `protobuf:"bytes,1,rep,name=presence_list,json=presenceList,proto3" json:"presence_list,omitempty"`
	Notification         *CompositiveNotification `protobuf:"bytes,2,opt,name=notification,proto3" json:"notification,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *PushToPresenceListReq) Reset()         { *m = PushToPresenceListReq{} }
func (m *PushToPresenceListReq) String() string { return proto.CompactTextString(m) }
func (*PushToPresenceListReq) ProtoMessage()    {}
func (*PushToPresenceListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{13}
}

func (m *PushToPresenceListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushToPresenceListReq.Unmarshal(m, b)
}
func (m *PushToPresenceListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushToPresenceListReq.Marshal(b, m, deterministic)
}
func (m *PushToPresenceListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushToPresenceListReq.Merge(m, src)
}
func (m *PushToPresenceListReq) XXX_Size() int {
	return xxx_messageInfo_PushToPresenceListReq.Size(m)
}
func (m *PushToPresenceListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushToPresenceListReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushToPresenceListReq proto.InternalMessageInfo

func (m *PushToPresenceListReq) GetPresenceList() []*Presence {
	if m != nil {
		return m.PresenceList
	}
	return nil
}

func (m *PushToPresenceListReq) GetNotification() *CompositiveNotification {
	if m != nil {
		return m.Notification
	}
	return nil
}

type GetReliableProxyNotificationsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SequenceBegin        uint32   `protobuf:"varint,2,opt,name=sequence_begin,json=sequenceBegin,proto3" json:"sequence_begin,omitempty"`
	SequenceEnd          uint32   `protobuf:"varint,3,opt,name=sequence_end,json=sequenceEnd,proto3" json:"sequence_end,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReliableProxyNotificationsReq) Reset()         { *m = GetReliableProxyNotificationsReq{} }
func (m *GetReliableProxyNotificationsReq) String() string { return proto.CompactTextString(m) }
func (*GetReliableProxyNotificationsReq) ProtoMessage()    {}
func (*GetReliableProxyNotificationsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{14}
}

func (m *GetReliableProxyNotificationsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReliableProxyNotificationsReq.Unmarshal(m, b)
}
func (m *GetReliableProxyNotificationsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReliableProxyNotificationsReq.Marshal(b, m, deterministic)
}
func (m *GetReliableProxyNotificationsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReliableProxyNotificationsReq.Merge(m, src)
}
func (m *GetReliableProxyNotificationsReq) XXX_Size() int {
	return xxx_messageInfo_GetReliableProxyNotificationsReq.Size(m)
}
func (m *GetReliableProxyNotificationsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReliableProxyNotificationsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetReliableProxyNotificationsReq proto.InternalMessageInfo

func (m *GetReliableProxyNotificationsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetReliableProxyNotificationsReq) GetSequenceBegin() uint32 {
	if m != nil {
		return m.SequenceBegin
	}
	return 0
}

func (m *GetReliableProxyNotificationsReq) GetSequenceEnd() uint32 {
	if m != nil {
		return m.SequenceEnd
	}
	return 0
}

func (m *GetReliableProxyNotificationsReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type ReliableProxyNotification struct {
	Sequence             uint32             `protobuf:"varint,1,opt,name=sequence,proto3" json:"sequence,omitempty"`
	TerminalTypeList     []uint32           `protobuf:"varint,2,rep,packed,name=terminal_type_list,json=terminalTypeList,proto3" json:"terminal_type_list,omitempty"`
	AppId                uint32             `protobuf:"varint,3,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	ProxyNotification    *ProxyNotification `protobuf:"bytes,4,opt,name=proxy_notification,json=proxyNotification,proto3" json:"proxy_notification,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ReliableProxyNotification) Reset()         { *m = ReliableProxyNotification{} }
func (m *ReliableProxyNotification) String() string { return proto.CompactTextString(m) }
func (*ReliableProxyNotification) ProtoMessage()    {}
func (*ReliableProxyNotification) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{15}
}

func (m *ReliableProxyNotification) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReliableProxyNotification.Unmarshal(m, b)
}
func (m *ReliableProxyNotification) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReliableProxyNotification.Marshal(b, m, deterministic)
}
func (m *ReliableProxyNotification) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReliableProxyNotification.Merge(m, src)
}
func (m *ReliableProxyNotification) XXX_Size() int {
	return xxx_messageInfo_ReliableProxyNotification.Size(m)
}
func (m *ReliableProxyNotification) XXX_DiscardUnknown() {
	xxx_messageInfo_ReliableProxyNotification.DiscardUnknown(m)
}

var xxx_messageInfo_ReliableProxyNotification proto.InternalMessageInfo

func (m *ReliableProxyNotification) GetSequence() uint32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func (m *ReliableProxyNotification) GetTerminalTypeList() []uint32 {
	if m != nil {
		return m.TerminalTypeList
	}
	return nil
}

func (m *ReliableProxyNotification) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *ReliableProxyNotification) GetProxyNotification() *ProxyNotification {
	if m != nil {
		return m.ProxyNotification
	}
	return nil
}

// Clients are responsible for filtering the notifications with terminal types
// or app ids
type GetReliableProxyNotificationsResp struct {
	Notifications        []*ReliableProxyNotification `protobuf:"bytes,1,rep,name=notifications,proto3" json:"notifications,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetReliableProxyNotificationsResp) Reset()         { *m = GetReliableProxyNotificationsResp{} }
func (m *GetReliableProxyNotificationsResp) String() string { return proto.CompactTextString(m) }
func (*GetReliableProxyNotificationsResp) ProtoMessage()    {}
func (*GetReliableProxyNotificationsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{16}
}

func (m *GetReliableProxyNotificationsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReliableProxyNotificationsResp.Unmarshal(m, b)
}
func (m *GetReliableProxyNotificationsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReliableProxyNotificationsResp.Marshal(b, m, deterministic)
}
func (m *GetReliableProxyNotificationsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReliableProxyNotificationsResp.Merge(m, src)
}
func (m *GetReliableProxyNotificationsResp) XXX_Size() int {
	return xxx_messageInfo_GetReliableProxyNotificationsResp.Size(m)
}
func (m *GetReliableProxyNotificationsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReliableProxyNotificationsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetReliableProxyNotificationsResp proto.InternalMessageInfo

func (m *GetReliableProxyNotificationsResp) GetNotifications() []*ReliableProxyNotification {
	if m != nil {
		return m.Notifications
	}
	return nil
}

type MessageReceivedAckReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SequenceList         []uint32 `protobuf:"varint,2,rep,packed,name=sequence_list,json=sequenceList,proto3" json:"sequence_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MessageReceivedAckReq) Reset()         { *m = MessageReceivedAckReq{} }
func (m *MessageReceivedAckReq) String() string { return proto.CompactTextString(m) }
func (*MessageReceivedAckReq) ProtoMessage()    {}
func (*MessageReceivedAckReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{17}
}

func (m *MessageReceivedAckReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MessageReceivedAckReq.Unmarshal(m, b)
}
func (m *MessageReceivedAckReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MessageReceivedAckReq.Marshal(b, m, deterministic)
}
func (m *MessageReceivedAckReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MessageReceivedAckReq.Merge(m, src)
}
func (m *MessageReceivedAckReq) XXX_Size() int {
	return xxx_messageInfo_MessageReceivedAckReq.Size(m)
}
func (m *MessageReceivedAckReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MessageReceivedAckReq.DiscardUnknown(m)
}

var xxx_messageInfo_MessageReceivedAckReq proto.InternalMessageInfo

func (m *MessageReceivedAckReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MessageReceivedAckReq) GetSequenceList() []uint32 {
	if m != nil {
		return m.SequenceList
	}
	return nil
}

type MessageReceivedAckResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MessageReceivedAckResp) Reset()         { *m = MessageReceivedAckResp{} }
func (m *MessageReceivedAckResp) String() string { return proto.CompactTextString(m) }
func (*MessageReceivedAckResp) ProtoMessage()    {}
func (*MessageReceivedAckResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{18}
}

func (m *MessageReceivedAckResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MessageReceivedAckResp.Unmarshal(m, b)
}
func (m *MessageReceivedAckResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MessageReceivedAckResp.Marshal(b, m, deterministic)
}
func (m *MessageReceivedAckResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MessageReceivedAckResp.Merge(m, src)
}
func (m *MessageReceivedAckResp) XXX_Size() int {
	return xxx_messageInfo_MessageReceivedAckResp.Size(m)
}
func (m *MessageReceivedAckResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MessageReceivedAckResp.DiscardUnknown(m)
}

var xxx_messageInfo_MessageReceivedAckResp proto.InternalMessageInfo

type ExpireKey struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Seq                  uint32   `protobuf:"varint,2,opt,name=seq,proto3" json:"seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExpireKey) Reset()         { *m = ExpireKey{} }
func (m *ExpireKey) String() string { return proto.CompactTextString(m) }
func (*ExpireKey) ProtoMessage()    {}
func (*ExpireKey) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_6b2dd2bf2e0fc6b4, []int{19}
}

func (m *ExpireKey) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpireKey.Unmarshal(m, b)
}
func (m *ExpireKey) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpireKey.Marshal(b, m, deterministic)
}
func (m *ExpireKey) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpireKey.Merge(m, src)
}
func (m *ExpireKey) XXX_Size() int {
	return xxx_messageInfo_ExpireKey.Size(m)
}
func (m *ExpireKey) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpireKey.DiscardUnknown(m)
}

var xxx_messageInfo_ExpireKey proto.InternalMessageInfo

func (m *ExpireKey) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ExpireKey) GetSeq() uint32 {
	if m != nil {
		return m.Seq
	}
	return 0
}

func init() {
	proto.RegisterEnum("push.v1.ProxyNotification_Type", ProxyNotification_Type_name, ProxyNotification_Type_value)
	proto.RegisterEnum("push.v1.ProxyNotification_Policy", ProxyNotification_Policy_name, ProxyNotification_Policy_value)
	proto.RegisterType((*ProxyNotification)(nil), "push.v1.ProxyNotification")
	proto.RegisterType((*CompositiveNotification)(nil), "push.v1.CompositiveNotification")
	proto.RegisterType((*TerminalTypePolicy)(nil), "push.v1.TerminalTypePolicy")
	proto.RegisterType((*GetTerminalTypePolicyResp)(nil), "push.v1.GetTerminalTypePolicyResp")
	proto.RegisterType((*SetTerminalTypePolicyResp)(nil), "push.v1.SetTerminalTypePolicyResp")
	proto.RegisterType((*TerminalTypePolicyInfo)(nil), "push.v1.TerminalTypePolicyInfo")
	proto.RegisterType((*MulticastAccount)(nil), "push.v1.MulticastAccount")
	proto.RegisterType((*ProxyPushResult)(nil), "push.v1.ProxyPushResult")
	proto.RegisterMapType((map[uint32]uint32)(nil), "push.v1.ProxyPushResult.UserNotifyCountsMapEntry")
	proto.RegisterType((*PushToUsersReq)(nil), "push.v1.PushToUsersReq")
	proto.RegisterType((*PushMulticastReq)(nil), "push.v1.PushMulticastReq")
	proto.RegisterType((*PushToUsersResp)(nil), "push.v1.PushToUsersResp")
	proto.RegisterType((*PushMulticastResp)(nil), "push.v1.PushMulticastResp")
	proto.RegisterType((*Presence)(nil), "push.v1.Presence")
	proto.RegisterType((*PushToPresenceListReq)(nil), "push.v1.PushToPresenceListReq")
	proto.RegisterType((*GetReliableProxyNotificationsReq)(nil), "push.v1.GetReliableProxyNotificationsReq")
	proto.RegisterType((*ReliableProxyNotification)(nil), "push.v1.ReliableProxyNotification")
	proto.RegisterType((*GetReliableProxyNotificationsResp)(nil), "push.v1.GetReliableProxyNotificationsResp")
	proto.RegisterType((*MessageReceivedAckReq)(nil), "push.v1.MessageReceivedAckReq")
	proto.RegisterType((*MessageReceivedAckResp)(nil), "push.v1.MessageReceivedAckResp")
	proto.RegisterType((*ExpireKey)(nil), "push.v1.ExpireKey")
}

func init() { proto.RegisterFile("api.proto", fileDescriptor_00212fb1f9d3bf1c) }

var fileDescriptor_00212fb1f9d3bf1c = []byte{
	// 1313 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x57, 0xcb, 0x6e, 0x1b, 0x37,
	0x17, 0xf6, 0x48, 0xb2, 0x24, 0x1f, 0x59, 0xf6, 0x88, 0x7f, 0xec, 0x8c, 0x14, 0x24, 0x51, 0x26,
	0xf8, 0x01, 0xa5, 0x28, 0xe4, 0x44, 0x45, 0x8a, 0xde, 0x36, 0x4e, 0x7c, 0x89, 0x10, 0x4b, 0x31,
	0x68, 0xa9, 0x97, 0x2c, 0x3a, 0x98, 0xcc, 0xd0, 0x0e, 0x11, 0x69, 0x86, 0x19, 0x72, 0x8c, 0x68,
	0xd7, 0x4d, 0xdf, 0xa0, 0x05, 0x0a, 0xf4, 0x61, 0x8a, 0xae, 0xfa, 0x08, 0x45, 0x37, 0x7d, 0x96,
	0x82, 0x9c, 0x8b, 0x47, 0xd7, 0xb4, 0x4d, 0x36, 0x5d, 0x69, 0x78, 0xc8, 0x73, 0xce, 0xc7, 0xef,
	0x7c, 0x3c, 0xa4, 0x60, 0xc3, 0x66, 0xb4, 0xcd, 0x02, 0x5f, 0xf8, 0xa8, 0xc4, 0x42, 0xfe, 0xb2,
	0x7d, 0xf9, 0xc0, 0xfc, 0x35, 0x07, 0xb5, 0xd3, 0xc0, 0x7f, 0x33, 0xe9, 0xfb, 0x82, 0x9e, 0x53,
	0xc7, 0x16, 0xd4, 0xf7, 0x10, 0x82, 0x82, 0x98, 0x30, 0x62, 0x68, 0x4d, 0xad, 0x55, 0xc5, 0xea,
	0x1b, 0x19, 0x50, 0x62, 0xf6, 0x64, 0xe4, 0xdb, 0xae, 0x91, 0x6b, 0x6a, 0xad, 0x4d, 0x9c, 0x0c,
	0xd1, 0xa7, 0x50, 0x64, 0xfe, 0x88, 0x3a, 0x13, 0x23, 0xdf, 0xd4, 0x5a, 0x5b, 0x9d, 0x3b, 0xed,
	0x38, 0x7a, 0x7b, 0x2e, 0x72, 0xfb, 0x54, 0x2d, 0xc4, 0xb1, 0x03, 0xba, 0x0d, 0x15, 0xf2, 0x86,
	0xd1, 0x80, 0x58, 0x82, 0x8e, 0x89, 0x51, 0x50, 0xf9, 0x20, 0x32, 0x0d, 0xe8, 0x98, 0x48, 0x24,
	0x7c, 0xe2, 0x39, 0xc6, 0x7a, 0x53, 0x6b, 0x95, 0xb1, 0xfa, 0x46, 0x0d, 0x28, 0xb3, 0x80, 0xfa,
	0x01, 0x15, 0x13, 0xa3, 0xa8, 0x3c, 0xd2, 0xb1, 0xd9, 0x83, 0xc2, 0x40, 0xa2, 0xad, 0x40, 0xa9,
	0xdb, 0xff, 0x72, 0xff, 0xa4, 0x7b, 0xa0, 0xaf, 0x21, 0x80, 0x62, 0xff, 0xd9, 0xa0, 0x7b, 0xf4,
	0x8d, 0xae, 0xa1, 0x32, 0x14, 0x4e, 0x87, 0x67, 0x4f, 0xf4, 0x1c, 0xda, 0x81, 0xda, 0x00, 0xef,
	0xf7, 0xcf, 0x7a, 0xdd, 0xb3, 0xb3, 0xee, 0xb3, 0xbe, 0xa5, 0xcc, 0x79, 0xe9, 0xf9, 0xb4, 0xfb,
	0xf8, 0xe9, 0xb3, 0xe1, 0x40, 0x2f, 0x98, 0x77, 0xa1, 0x18, 0x21, 0x96, 0xe6, 0x83, 0xc3, 0xa3,
	0xfd, 0xe1, 0xc9, 0x40, 0x5f, 0x43, 0x9b, 0x50, 0xc6, 0x87, 0x27, 0xdd, 0xfd, 0x47, 0x27, 0x87,
	0xba, 0x66, 0xfe, 0x92, 0x83, 0xeb, 0x8f, 0xfd, 0x31, 0xf3, 0x39, 0x15, 0xf4, 0x92, 0x4c, 0x31,
	0xd9, 0x80, 0x32, 0x27, 0xaf, 0x43, 0xe2, 0x39, 0x09, 0x9b, 0xe9, 0x18, 0x7d, 0x08, 0x48, 0x90,
	0x60, 0x4c, 0x3d, 0x7b, 0x64, 0x49, 0x8a, 0xad, 0x11, 0xe5, 0xc2, 0xc8, 0x35, 0xf3, 0xad, 0x2a,
	0xd6, 0x93, 0x19, 0xb9, 0x9b, 0x13, 0xca, 0x05, 0xda, 0x81, 0xa2, 0xcd, 0x98, 0x45, 0x5d, 0xc5,
	0x72, 0x15, 0xaf, 0xdb, 0x8c, 0x75, 0x5d, 0xd4, 0x05, 0xc4, 0x24, 0xcb, 0x96, 0x97, 0x49, 0xab,
	0x88, 0xac, 0x74, 0x1a, 0xcb, 0x0b, 0x81, 0x6b, 0x6c, 0xae, 0xea, 0xf7, 0x40, 0x77, 0x46, 0x94,
	0x78, 0x42, 0x15, 0x83, 0x0b, 0x7b, 0xcc, 0x8c, 0x92, 0xca, 0xb5, 0x1d, 0xd9, 0x07, 0x89, 0x19,
	0xf5, 0xe0, 0xda, 0x34, 0xf4, 0x58, 0x00, 0x65, 0x95, 0xf7, 0x46, 0x9a, 0x77, 0x90, 0xd9, 0x45,
	0x5c, 0x7a, 0x24, 0xe6, 0x6c, 0xe6, 0xd7, 0x80, 0xe6, 0x57, 0xa2, 0x3b, 0xb0, 0x19, 0x85, 0xb5,
	0x2e, 0x02, 0x3f, 0x64, 0x8a, 0xbf, 0x0d, 0x5c, 0x89, 0x6c, 0xc7, 0xd2, 0x24, 0xf5, 0x13, 0x2f,
	0xf1, 0xec, 0x31, 0x51, 0xc2, 0xdc, 0xc0, 0x10, 0x99, 0xfa, 0xf6, 0x98, 0x98, 0xdf, 0x69, 0x50,
	0x3f, 0x26, 0x62, 0x01, 0x0e, 0xc2, 0x19, 0x72, 0xe0, 0xd6, 0xa2, 0x6d, 0x58, 0xd4, 0x3b, 0xf7,
	0xa3, 0x6a, 0x68, 0xcd, 0x7c, 0xab, 0xd2, 0xb9, 0xbd, 0x62, 0x43, 0x5d, 0xef, 0xdc, 0xc7, 0x0d,
	0xb1, 0xd0, 0x2e, 0x0b, 0x67, 0x3e, 0x84, 0xfa, 0xd9, 0x52, 0x04, 0x06, 0x94, 0x78, 0xe8, 0x38,
	0x84, 0x73, 0xb5, 0xbd, 0x32, 0x4e, 0x86, 0xe6, 0x8f, 0x1a, 0xec, 0x2e, 0xce, 0xb6, 0x94, 0x7d,
	0xed, 0x5f, 0xb1, 0xff, 0xcf, 0x74, 0x68, 0x7e, 0x01, 0x7a, 0x2f, 0x1c, 0x09, 0xea, 0xd8, 0x5c,
	0xec, 0x3b, 0x8e, 0x1f, 0x7a, 0x02, 0x6d, 0x41, 0x8e, 0xba, 0x2a, 0x7d, 0x01, 0xe7, 0xa8, 0x2b,
	0x77, 0x65, 0x47, 0x53, 0x71, 0x49, 0x92, 0xa1, 0xf9, 0x87, 0x06, 0xdb, 0x4a, 0x8c, 0xa7, 0x21,
	0x7f, 0x89, 0x09, 0x0f, 0x47, 0x02, 0xed, 0x42, 0x31, 0x50, 0x5f, 0x2a, 0xc2, 0x3a, 0x8e, 0x47,
	0xe8, 0x1c, 0x76, 0x43, 0x4e, 0x82, 0x48, 0xd9, 0x13, 0x4b, 0x05, 0xe0, 0xd6, 0xd8, 0x66, 0x0a,
	0x5b, 0xa5, 0xf3, 0x60, 0x5a, 0xde, 0x57, 0x11, 0xdb, 0x43, 0x4e, 0x02, 0x25, 0xed, 0xc9, 0x63,
	0xe5, 0xd4, 0xb3, 0xd9, 0xa1, 0x27, 0x82, 0x09, 0xfe, 0x5f, 0x38, 0x3f, 0xd3, 0x38, 0x02, 0x63,
	0x99, 0x03, 0xd2, 0x21, 0xff, 0x8a, 0x4c, 0xe2, 0xa3, 0x2b, 0x3f, 0xd1, 0x35, 0x58, 0xbf, 0xb4,
	0x47, 0x61, 0x24, 0xb6, 0x2a, 0x8e, 0x06, 0x9f, 0xe5, 0x3e, 0xd1, 0xcc, 0xef, 0x35, 0xd8, 0x92,
	0x20, 0x06, 0xbe, 0x0c, 0xc7, 0x31, 0x79, 0x8d, 0xea, 0x50, 0x0e, 0xa9, 0x7b, 0x25, 0xa5, 0x2a,
	0x2e, 0x85, 0xd4, 0x55, 0xe7, 0xf9, 0x00, 0x36, 0xa7, 0x8e, 0x6c, 0x4e, 0x15, 0xaf, 0x99, 0xee,
	0x69, 0x49, 0x47, 0xc1, 0x53, 0x5e, 0x69, 0x7f, 0xcc, 0x5f, 0xf5, 0x47, 0xf3, 0xe7, 0x1c, 0xe8,
	0x12, 0x47, 0x5a, 0x26, 0x89, 0xe4, 0x08, 0x6a, 0xe3, 0x64, 0x6c, 0x25, 0xc5, 0x89, 0x04, 0x53,
	0x4f, 0x73, 0xce, 0x16, 0x16, 0xeb, 0xe3, 0xd9, 0x52, 0xbf, 0x1f, 0xd8, 0x37, 0x60, 0x83, 0xbf,
	0xa2, 0xcc, 0x0a, 0xa9, 0xcb, 0x8d, 0xbc, 0x22, 0xa6, 0x2c, 0x0d, 0x43, 0xea, 0x72, 0xf4, 0x04,
	0xd0, 0x1c, 0x54, 0x6e, 0x14, 0x54, 0xcd, 0x57, 0x60, 0xad, 0xcd, 0x62, 0xe5, 0x8b, 0x6e, 0x0f,
	0x93, 0xc3, 0xf6, 0x54, 0x91, 0x38, 0x5b, 0xd9, 0xa4, 0x0f, 0x20, 0xea, 0x94, 0x96, 0xcc, 0x6b,
	0xc5, 0x3a, 0x8d, 0x36, 0x6d, 0x2c, 0xd3, 0x1f, 0xde, 0x66, 0xd3, 0x06, 0x73, 0x0f, 0x6a, 0x33,
	0x15, 0x59, 0x9d, 0xd6, 0xfc, 0x49, 0x83, 0xf2, 0x69, 0x40, 0xb8, 0xc2, 0x50, 0x97, 0x17, 0x9e,
	0xc4, 0x40, 0x59, 0xbc, 0xb0, 0xa4, 0xc6, 0x5d, 0x86, 0x6e, 0x02, 0xc4, 0xf0, 0xfc, 0x40, 0xc4,
	0x92, 0xdc, 0x88, 0xb2, 0xfb, 0x81, 0x90, 0xf2, 0x0d, 0xd3, 0x1b, 0x43, 0x7e, 0x4a, 0xe6, 0xe3,
	0x26, 0x4f, 0xdd, 0xf8, 0xbe, 0x2d, 0x47, 0x86, 0xae, 0x8b, 0xee, 0x42, 0x75, 0xaa, 0x13, 0x28,
	0xe2, 0xaa, 0x78, 0x33, 0xdb, 0x04, 0x64, 0x63, 0xda, 0x89, 0x18, 0x4c, 0x00, 0x4a, 0x3d, 0x4b,
	0x8d, 0x7d, 0x0c, 0x55, 0x16, 0x9b, 0xb2, 0xdd, 0xb3, 0x96, 0xe1, 0x29, 0x9a, 0xc5, 0x9b, 0x2c,
	0xe3, 0xfa, 0x7e, 0x34, 0x65, 0xfe, 0xa0, 0x41, 0xf3, 0x98, 0x08, 0x4c, 0x46, 0xd4, 0x7e, 0x31,
	0x22, 0x73, 0x57, 0x9e, 0x3a, 0x90, 0x31, 0x21, 0xda, 0x15, 0x21, 0xff, 0x87, 0xad, 0x84, 0x75,
	0xeb, 0x05, 0xb9, 0xa0, 0x5e, 0xcc, 0x62, 0x35, 0xb1, 0x3e, 0x92, 0x46, 0x79, 0x19, 0xa5, 0xcb,
	0x88, 0x97, 0x50, 0x5a, 0x49, 0x6c, 0x87, 0x9e, 0x2b, 0x3b, 0x43, 0x74, 0xac, 0x22, 0x5a, 0xa3,
	0x81, 0xf9, 0x9b, 0x06, 0xf5, 0xa5, 0x98, 0xfe, 0x4b, 0xef, 0x03, 0x73, 0x0c, 0x77, 0xde, 0xc2,
	0x2f, 0x67, 0xe8, 0x09, 0x54, 0xb3, 0x99, 0x78, 0xac, 0x01, 0x33, 0x4d, 0xb5, 0xd4, 0x1f, 0x4f,
	0x3b, 0x9a, 0x7d, 0xd8, 0xe9, 0x11, 0xce, 0xed, 0x0b, 0x82, 0x89, 0x43, 0xe8, 0x25, 0x71, 0xf7,
	0x9d, 0x57, 0x8b, 0x6b, 0x78, 0x17, 0xd2, 0x6a, 0x65, 0x49, 0x4a, 0x2b, 0xa6, 0x2e, 0x2e, 0x03,
	0x76, 0x17, 0xc5, 0xe3, 0xcc, 0xdc, 0x83, 0x8d, 0x43, 0xf5, 0xe4, 0x7c, 0x4a, 0x26, 0x0b, 0xa2,
	0xeb, 0x90, 0xe7, 0xe4, 0x75, 0x2c, 0x0b, 0xf9, 0xd9, 0xf9, 0xb3, 0x00, 0x05, 0x79, 0x04, 0xd0,
	0x11, 0x54, 0x32, 0xcd, 0x04, 0x5d, 0xbf, 0x22, 0x74, 0xea, 0x1e, 0x68, 0x18, 0x8b, 0x27, 0x38,
	0x33, 0xd7, 0x5a, 0xda, 0x7d, 0x0d, 0x9d, 0x40, 0x75, 0xaa, 0x3f, 0xa0, 0xfa, 0x94, 0x43, 0xb6,
	0x93, 0x37, 0x1a, 0xcb, 0xa6, 0xd2, 0x68, 0xc7, 0xa0, 0x0f, 0x3d, 0x3b, 0x98, 0xbc, 0x2b, 0x34,
	0xd4, 0x03, 0x94, 0x06, 0x7a, 0x77, 0x6c, 0x68, 0x08, 0xd7, 0x33, 0xb8, 0xb2, 0xdd, 0x03, 0xdd,
	0x9a, 0x41, 0x31, 0xd3, 0x5a, 0x56, 0xa2, 0x7c, 0x03, 0x37, 0x57, 0xea, 0x12, 0xdd, 0x4b, 0x9d,
	0xdf, 0xd6, 0x1f, 0x1a, 0x1f, 0xfc, 0xdd, 0xa5, 0x2a, 0xf3, 0x57, 0x80, 0xe6, 0x25, 0x95, 0xd9,
	0xcb, 0x42, 0xfd, 0x36, 0x6e, 0xaf, 0x9c, 0x97, 0x81, 0x3b, 0xbf, 0x6b, 0x50, 0x9f, 0x7f, 0xbd,
	0xf5, 0x6c, 0xcf, 0xbe, 0x20, 0x01, 0xfa, 0x16, 0x76, 0x16, 0xbe, 0x28, 0xd1, 0xdb, 0xde, 0xa9,
	0x8d, 0xab, 0x63, 0xb8, 0xf4, 0x49, 0x6a, 0xae, 0xa1, 0xe7, 0xb0, 0xb3, 0xf0, 0xcd, 0x8c, 0x56,
	0x3d, 0x2d, 0x33, 0xb1, 0x8f, 0x97, 0xc7, 0x7e, 0x74, 0xff, 0x79, 0xfb, 0xc2, 0x1f, 0xd9, 0xde,
	0x45, 0xfb, 0x61, 0x47, 0x88, 0xb6, 0xe3, 0x8f, 0xf7, 0xd4, 0x5f, 0x52, 0xc7, 0x1f, 0xed, 0x71,
	0x12, 0x5c, 0x52, 0x87, 0xf0, 0x3d, 0x19, 0x68, 0xef, 0xf2, 0xc1, 0xe7, 0xf2, 0xf7, 0x45, 0x51,
	0xcd, 0x7f, 0xf4, 0x57, 0x00, 0x00, 0x00, 0xff, 0xff, 0x09, 0x58, 0xa8, 0x36, 0xbf, 0x0e, 0x00,
	0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PushClient is the client API for Push service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PushClient interface {
	PushToUsers(ctx context.Context, opts ...grpc.CallOption) (Push_PushToUsersClient, error)
	PushMulticast(ctx context.Context, opts ...grpc.CallOption) (Push_PushMulticastClient, error)
	UnaryPushToUsers(ctx context.Context, in *PushToUsersReq, opts ...grpc.CallOption) (*PushToUsersResp, error)
	UnaryPushMulticast(ctx context.Context, in *PushMulticastReq, opts ...grpc.CallOption) (*PushMulticastResp, error)
	UnaryPushToPresenceList(ctx context.Context, in *PushToPresenceListReq, opts ...grpc.CallOption) (*PushToUsersResp, error)
	// Reliable push.
	GetReliableProxyNotifications(ctx context.Context, in *GetReliableProxyNotificationsReq, opts ...grpc.CallOption) (*GetReliableProxyNotificationsResp, error)
	MessageReceivedAck(ctx context.Context, in *MessageReceivedAckReq, opts ...grpc.CallOption) (*MessageReceivedAckResp, error)
}

type pushClient struct {
	cc *grpc.ClientConn
}

func NewPushClient(cc *grpc.ClientConn) PushClient {
	return &pushClient{cc}
}

func (c *pushClient) PushToUsers(ctx context.Context, opts ...grpc.CallOption) (Push_PushToUsersClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Push_serviceDesc.Streams[0], "/push.v1.Push/PushToUsers", opts...)
	if err != nil {
		return nil, err
	}
	x := &pushPushToUsersClient{stream}
	return x, nil
}

type Push_PushToUsersClient interface {
	Send(*PushToUsersReq) error
	Recv() (*PushToUsersResp, error)
	grpc.ClientStream
}

type pushPushToUsersClient struct {
	grpc.ClientStream
}

func (x *pushPushToUsersClient) Send(m *PushToUsersReq) error {
	return x.ClientStream.SendMsg(m)
}

func (x *pushPushToUsersClient) Recv() (*PushToUsersResp, error) {
	m := new(PushToUsersResp)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *pushClient) PushMulticast(ctx context.Context, opts ...grpc.CallOption) (Push_PushMulticastClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Push_serviceDesc.Streams[1], "/push.v1.Push/PushMulticast", opts...)
	if err != nil {
		return nil, err
	}
	x := &pushPushMulticastClient{stream}
	return x, nil
}

type Push_PushMulticastClient interface {
	Send(*PushMulticastReq) error
	Recv() (*PushMulticastResp, error)
	grpc.ClientStream
}

type pushPushMulticastClient struct {
	grpc.ClientStream
}

func (x *pushPushMulticastClient) Send(m *PushMulticastReq) error {
	return x.ClientStream.SendMsg(m)
}

func (x *pushPushMulticastClient) Recv() (*PushMulticastResp, error) {
	m := new(PushMulticastResp)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *pushClient) UnaryPushToUsers(ctx context.Context, in *PushToUsersReq, opts ...grpc.CallOption) (*PushToUsersResp, error) {
	out := new(PushToUsersResp)
	err := c.cc.Invoke(ctx, "/push.v1.Push/UnaryPushToUsers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushClient) UnaryPushMulticast(ctx context.Context, in *PushMulticastReq, opts ...grpc.CallOption) (*PushMulticastResp, error) {
	out := new(PushMulticastResp)
	err := c.cc.Invoke(ctx, "/push.v1.Push/UnaryPushMulticast", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushClient) UnaryPushToPresenceList(ctx context.Context, in *PushToPresenceListReq, opts ...grpc.CallOption) (*PushToUsersResp, error) {
	out := new(PushToUsersResp)
	err := c.cc.Invoke(ctx, "/push.v1.Push/UnaryPushToPresenceList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushClient) GetReliableProxyNotifications(ctx context.Context, in *GetReliableProxyNotificationsReq, opts ...grpc.CallOption) (*GetReliableProxyNotificationsResp, error) {
	out := new(GetReliableProxyNotificationsResp)
	err := c.cc.Invoke(ctx, "/push.v1.Push/GetReliableProxyNotifications", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushClient) MessageReceivedAck(ctx context.Context, in *MessageReceivedAckReq, opts ...grpc.CallOption) (*MessageReceivedAckResp, error) {
	out := new(MessageReceivedAckResp)
	err := c.cc.Invoke(ctx, "/push.v1.Push/MessageReceivedAck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PushServer is the server API for Push service.
type PushServer interface {
	PushToUsers(Push_PushToUsersServer) error
	PushMulticast(Push_PushMulticastServer) error
	UnaryPushToUsers(context.Context, *PushToUsersReq) (*PushToUsersResp, error)
	UnaryPushMulticast(context.Context, *PushMulticastReq) (*PushMulticastResp, error)
	UnaryPushToPresenceList(context.Context, *PushToPresenceListReq) (*PushToUsersResp, error)
	// Reliable push.
	GetReliableProxyNotifications(context.Context, *GetReliableProxyNotificationsReq) (*GetReliableProxyNotificationsResp, error)
	MessageReceivedAck(context.Context, *MessageReceivedAckReq) (*MessageReceivedAckResp, error)
}

// UnimplementedPushServer can be embedded to have forward compatible implementations.
type UnimplementedPushServer struct {
}

func (*UnimplementedPushServer) PushToUsers(srv Push_PushToUsersServer) error {
	return status.Errorf(codes.Unimplemented, "method PushToUsers not implemented")
}
func (*UnimplementedPushServer) PushMulticast(srv Push_PushMulticastServer) error {
	return status.Errorf(codes.Unimplemented, "method PushMulticast not implemented")
}
func (*UnimplementedPushServer) UnaryPushToUsers(ctx context.Context, req *PushToUsersReq) (*PushToUsersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnaryPushToUsers not implemented")
}
func (*UnimplementedPushServer) UnaryPushMulticast(ctx context.Context, req *PushMulticastReq) (*PushMulticastResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnaryPushMulticast not implemented")
}
func (*UnimplementedPushServer) UnaryPushToPresenceList(ctx context.Context, req *PushToPresenceListReq) (*PushToUsersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnaryPushToPresenceList not implemented")
}
func (*UnimplementedPushServer) GetReliableProxyNotifications(ctx context.Context, req *GetReliableProxyNotificationsReq) (*GetReliableProxyNotificationsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReliableProxyNotifications not implemented")
}
func (*UnimplementedPushServer) MessageReceivedAck(ctx context.Context, req *MessageReceivedAckReq) (*MessageReceivedAckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MessageReceivedAck not implemented")
}

func RegisterPushServer(s *grpc.Server, srv PushServer) {
	s.RegisterService(&_Push_serviceDesc, srv)
}

func _Push_PushToUsers_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(PushServer).PushToUsers(&pushPushToUsersServer{stream})
}

type Push_PushToUsersServer interface {
	Send(*PushToUsersResp) error
	Recv() (*PushToUsersReq, error)
	grpc.ServerStream
}

type pushPushToUsersServer struct {
	grpc.ServerStream
}

func (x *pushPushToUsersServer) Send(m *PushToUsersResp) error {
	return x.ServerStream.SendMsg(m)
}

func (x *pushPushToUsersServer) Recv() (*PushToUsersReq, error) {
	m := new(PushToUsersReq)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _Push_PushMulticast_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(PushServer).PushMulticast(&pushPushMulticastServer{stream})
}

type Push_PushMulticastServer interface {
	Send(*PushMulticastResp) error
	Recv() (*PushMulticastReq, error)
	grpc.ServerStream
}

type pushPushMulticastServer struct {
	grpc.ServerStream
}

func (x *pushPushMulticastServer) Send(m *PushMulticastResp) error {
	return x.ServerStream.SendMsg(m)
}

func (x *pushPushMulticastServer) Recv() (*PushMulticastReq, error) {
	m := new(PushMulticastReq)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _Push_UnaryPushToUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushToUsersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushServer).UnaryPushToUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/push.v1.Push/UnaryPushToUsers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushServer).UnaryPushToUsers(ctx, req.(*PushToUsersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Push_UnaryPushMulticast_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushMulticastReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushServer).UnaryPushMulticast(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/push.v1.Push/UnaryPushMulticast",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushServer).UnaryPushMulticast(ctx, req.(*PushMulticastReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Push_UnaryPushToPresenceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushToPresenceListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushServer).UnaryPushToPresenceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/push.v1.Push/UnaryPushToPresenceList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushServer).UnaryPushToPresenceList(ctx, req.(*PushToPresenceListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Push_GetReliableProxyNotifications_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReliableProxyNotificationsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushServer).GetReliableProxyNotifications(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/push.v1.Push/GetReliableProxyNotifications",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushServer).GetReliableProxyNotifications(ctx, req.(*GetReliableProxyNotificationsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Push_MessageReceivedAck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MessageReceivedAckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushServer).MessageReceivedAck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/push.v1.Push/MessageReceivedAck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushServer).MessageReceivedAck(ctx, req.(*MessageReceivedAckReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Push_serviceDesc = grpc.ServiceDesc{
	ServiceName: "push.v1.Push",
	HandlerType: (*PushServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UnaryPushToUsers",
			Handler:    _Push_UnaryPushToUsers_Handler,
		},
		{
			MethodName: "UnaryPushMulticast",
			Handler:    _Push_UnaryPushMulticast_Handler,
		},
		{
			MethodName: "UnaryPushToPresenceList",
			Handler:    _Push_UnaryPushToPresenceList_Handler,
		},
		{
			MethodName: "GetReliableProxyNotifications",
			Handler:    _Push_GetReliableProxyNotifications_Handler,
		},
		{
			MethodName: "MessageReceivedAck",
			Handler:    _Push_MessageReceivedAck_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "PushToUsers",
			Handler:       _Push_PushToUsers_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "PushMulticast",
			Handler:       _Push_PushMulticast_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "api.proto",
}

// TerminalTypePolicyManagerClient is the client API for TerminalTypePolicyManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TerminalTypePolicyManagerClient interface {
	SetTerminalTypePolicy(ctx context.Context, in *TerminalTypePolicyInfo, opts ...grpc.CallOption) (*SetTerminalTypePolicyResp, error)
	GetTerminalTypePolicy(ctx context.Context, in *TerminalTypePolicy, opts ...grpc.CallOption) (*GetTerminalTypePolicyResp, error)
}

type terminalTypePolicyManagerClient struct {
	cc *grpc.ClientConn
}

func NewTerminalTypePolicyManagerClient(cc *grpc.ClientConn) TerminalTypePolicyManagerClient {
	return &terminalTypePolicyManagerClient{cc}
}

func (c *terminalTypePolicyManagerClient) SetTerminalTypePolicy(ctx context.Context, in *TerminalTypePolicyInfo, opts ...grpc.CallOption) (*SetTerminalTypePolicyResp, error) {
	out := new(SetTerminalTypePolicyResp)
	err := c.cc.Invoke(ctx, "/push.v1.TerminalTypePolicyManager/SetTerminalTypePolicy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *terminalTypePolicyManagerClient) GetTerminalTypePolicy(ctx context.Context, in *TerminalTypePolicy, opts ...grpc.CallOption) (*GetTerminalTypePolicyResp, error) {
	out := new(GetTerminalTypePolicyResp)
	err := c.cc.Invoke(ctx, "/push.v1.TerminalTypePolicyManager/GetTerminalTypePolicy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TerminalTypePolicyManagerServer is the server API for TerminalTypePolicyManager service.
type TerminalTypePolicyManagerServer interface {
	SetTerminalTypePolicy(context.Context, *TerminalTypePolicyInfo) (*SetTerminalTypePolicyResp, error)
	GetTerminalTypePolicy(context.Context, *TerminalTypePolicy) (*GetTerminalTypePolicyResp, error)
}

// UnimplementedTerminalTypePolicyManagerServer can be embedded to have forward compatible implementations.
type UnimplementedTerminalTypePolicyManagerServer struct {
}

func (*UnimplementedTerminalTypePolicyManagerServer) SetTerminalTypePolicy(ctx context.Context, req *TerminalTypePolicyInfo) (*SetTerminalTypePolicyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetTerminalTypePolicy not implemented")
}
func (*UnimplementedTerminalTypePolicyManagerServer) GetTerminalTypePolicy(ctx context.Context, req *TerminalTypePolicy) (*GetTerminalTypePolicyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTerminalTypePolicy not implemented")
}

func RegisterTerminalTypePolicyManagerServer(s *grpc.Server, srv TerminalTypePolicyManagerServer) {
	s.RegisterService(&_TerminalTypePolicyManager_serviceDesc, srv)
}

func _TerminalTypePolicyManager_SetTerminalTypePolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TerminalTypePolicyInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerminalTypePolicyManagerServer).SetTerminalTypePolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/push.v1.TerminalTypePolicyManager/SetTerminalTypePolicy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerminalTypePolicyManagerServer).SetTerminalTypePolicy(ctx, req.(*TerminalTypePolicyInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _TerminalTypePolicyManager_GetTerminalTypePolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TerminalTypePolicy)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerminalTypePolicyManagerServer).GetTerminalTypePolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/push.v1.TerminalTypePolicyManager/GetTerminalTypePolicy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerminalTypePolicyManagerServer).GetTerminalTypePolicy(ctx, req.(*TerminalTypePolicy))
	}
	return interceptor(ctx, in, info, handler)
}

var _TerminalTypePolicyManager_serviceDesc = grpc.ServiceDesc{
	ServiceName: "push.v1.TerminalTypePolicyManager",
	HandlerType: (*TerminalTypePolicyManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetTerminalTypePolicy",
			Handler:    _TerminalTypePolicyManager_SetTerminalTypePolicy_Handler,
		},
		{
			MethodName: "GetTerminalTypePolicy",
			Handler:    _TerminalTypePolicyManager_GetTerminalTypePolicy_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api.proto",
}

func init() { proto.RegisterFile("api.proto", fileDescriptor_api_6b2dd2bf2e0fc6b4) }

var fileDescriptor_api_6b2dd2bf2e0fc6b4 = []byte{
	// 1313 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x57, 0xcb, 0x6e, 0x1b, 0x37,
	0x17, 0xf6, 0x48, 0xb2, 0x24, 0x1f, 0x59, 0xf6, 0x88, 0x7f, 0xec, 0x8c, 0x14, 0x24, 0x51, 0x26,
	0xf8, 0x01, 0xa5, 0x28, 0xe4, 0x44, 0x45, 0x8a, 0xde, 0x36, 0x4e, 0x7c, 0x89, 0x10, 0x4b, 0x31,
	0x68, 0xa9, 0x97, 0x2c, 0x3a, 0x98, 0xcc, 0xd0, 0x0e, 0x11, 0x69, 0x86, 0x19, 0x72, 0x8c, 0x68,
	0xd7, 0x4d, 0xdf, 0xa0, 0x05, 0x0a, 0xf4, 0x61, 0x8a, 0xae, 0xfa, 0x08, 0x45, 0x37, 0x7d, 0x96,
	0x82, 0x9c, 0x8b, 0x47, 0xd7, 0xb4, 0x4d, 0x36, 0x5d, 0x69, 0x78, 0xc8, 0x73, 0xce, 0xc7, 0xef,
	0x7c, 0x3c, 0xa4, 0x60, 0xc3, 0x66, 0xb4, 0xcd, 0x02, 0x5f, 0xf8, 0xa8, 0xc4, 0x42, 0xfe, 0xb2,
	0x7d, 0xf9, 0xc0, 0xfc, 0x35, 0x07, 0xb5, 0xd3, 0xc0, 0x7f, 0x33, 0xe9, 0xfb, 0x82, 0x9e, 0x53,
	0xc7, 0x16, 0xd4, 0xf7, 0x10, 0x82, 0x82, 0x98, 0x30, 0x62, 0x68, 0x4d, 0xad, 0x55, 0xc5, 0xea,
	0x1b, 0x19, 0x50, 0x62, 0xf6, 0x64, 0xe4, 0xdb, 0xae, 0x91, 0x6b, 0x6a, 0xad, 0x4d, 0x9c, 0x0c,
	0xd1, 0xa7, 0x50, 0x64, 0xfe, 0x88, 0x3a, 0x13, 0x23, 0xdf, 0xd4, 0x5a, 0x5b, 0x9d, 0x3b, 0xed,
	0x38, 0x7a, 0x7b, 0x2e, 0x72, 0xfb, 0x54, 0x2d, 0xc4, 0xb1, 0x03, 0xba, 0x0d, 0x15, 0xf2, 0x86,
	0xd1, 0x80, 0x58, 0x82, 0x8e, 0x89, 0x51, 0x50, 0xf9, 0x20, 0x32, 0x0d, 0xe8, 0x98, 0x48, 0x24,
	0x7c, 0xe2, 0x39, 0xc6, 0x7a, 0x53, 0x6b, 0x95, 0xb1, 0xfa, 0x46, 0x0d, 0x28, 0xb3, 0x80, 0xfa,
	0x01, 0x15, 0x13, 0xa3, 0xa8, 0x3c, 0xd2, 0xb1, 0xd9, 0x83, 0xc2, 0x40, 0xa2, 0xad, 0x40, 0xa9,
	0xdb, 0xff, 0x72, 0xff, 0xa4, 0x7b, 0xa0, 0xaf, 0x21, 0x80, 0x62, 0xff, 0xd9, 0xa0, 0x7b, 0xf4,
	0x8d, 0xae, 0xa1, 0x32, 0x14, 0x4e, 0x87, 0x67, 0x4f, 0xf4, 0x1c, 0xda, 0x81, 0xda, 0x00, 0xef,
	0xf7, 0xcf, 0x7a, 0xdd, 0xb3, 0xb3, 0xee, 0xb3, 0xbe, 0xa5, 0xcc, 0x79, 0xe9, 0xf9, 0xb4, 0xfb,
	0xf8, 0xe9, 0xb3, 0xe1, 0x40, 0x2f, 0x98, 0x77, 0xa1, 0x18, 0x21, 0x96, 0xe6, 0x83, 0xc3, 0xa3,
	0xfd, 0xe1, 0xc9, 0x40, 0x5f, 0x43, 0x9b, 0x50, 0xc6, 0x87, 0x27, 0xdd, 0xfd, 0x47, 0x27, 0x87,
	0xba, 0x66, 0xfe, 0x92, 0x83, 0xeb, 0x8f, 0xfd, 0x31, 0xf3, 0x39, 0x15, 0xf4, 0x92, 0x4c, 0x31,
	0xd9, 0x80, 0x32, 0x27, 0xaf, 0x43, 0xe2, 0x39, 0x09, 0x9b, 0xe9, 0x18, 0x7d, 0x08, 0x48, 0x90,
	0x60, 0x4c, 0x3d, 0x7b, 0x64, 0x49, 0x8a, 0xad, 0x11, 0xe5, 0xc2, 0xc8, 0x35, 0xf3, 0xad, 0x2a,
	0xd6, 0x93, 0x19, 0xb9, 0x9b, 0x13, 0xca, 0x05, 0xda, 0x81, 0xa2, 0xcd, 0x98, 0x45, 0x5d, 0xc5,
	0x72, 0x15, 0xaf, 0xdb, 0x8c, 0x75, 0x5d, 0xd4, 0x05, 0xc4, 0x24, 0xcb, 0x96, 0x97, 0x49, 0xab,
	0x88, 0xac, 0x74, 0x1a, 0xcb, 0x0b, 0x81, 0x6b, 0x6c, 0xae, 0xea, 0xf7, 0x40, 0x77, 0x46, 0x94,
	0x78, 0x42, 0x15, 0x83, 0x0b, 0x7b, 0xcc, 0x8c, 0x92, 0xca, 0xb5, 0x1d, 0xd9, 0x07, 0x89, 0x19,
	0xf5, 0xe0, 0xda, 0x34, 0xf4, 0x58, 0x00, 0x65, 0x95, 0xf7, 0x46, 0x9a, 0x77, 0x90, 0xd9, 0x45,
	0x5c, 0x7a, 0x24, 0xe6, 0x6c, 0xe6, 0xd7, 0x80, 0xe6, 0x57, 0xa2, 0x3b, 0xb0, 0x19, 0x85, 0xb5,
	0x2e, 0x02, 0x3f, 0x64, 0x8a, 0xbf, 0x0d, 0x5c, 0x89, 0x6c, 0xc7, 0xd2, 0x24, 0xf5, 0x13, 0x2f,
	0xf1, 0xec, 0x31, 0x51, 0xc2, 0xdc, 0xc0, 0x10, 0x99, 0xfa, 0xf6, 0x98, 0x98, 0xdf, 0x69, 0x50,
	0x3f, 0x26, 0x62, 0x01, 0x0e, 0xc2, 0x19, 0x72, 0xe0, 0xd6, 0xa2, 0x6d, 0x58, 0xd4, 0x3b, 0xf7,
	0xa3, 0x6a, 0x68, 0xcd, 0x7c, 0xab, 0xd2, 0xb9, 0xbd, 0x62, 0x43, 0x5d, 0xef, 0xdc, 0xc7, 0x0d,
	0xb1, 0xd0, 0x2e, 0x0b, 0x67, 0x3e, 0x84, 0xfa, 0xd9, 0x52, 0x04, 0x06, 0x94, 0x78, 0xe8, 0x38,
	0x84, 0x73, 0xb5, 0xbd, 0x32, 0x4e, 0x86, 0xe6, 0x8f, 0x1a, 0xec, 0x2e, 0xce, 0xb6, 0x94, 0x7d,
	0xed, 0x5f, 0xb1, 0xff, 0xcf, 0x74, 0x68, 0x7e, 0x01, 0x7a, 0x2f, 0x1c, 0x09, 0xea, 0xd8, 0x5c,
	0xec, 0x3b, 0x8e, 0x1f, 0x7a, 0x02, 0x6d, 0x41, 0x8e, 0xba, 0x2a, 0x7d, 0x01, 0xe7, 0xa8, 0x2b,
	0x77, 0x65, 0x47, 0x53, 0x71, 0x49, 0x92, 0xa1, 0xf9, 0x87, 0x06, 0xdb, 0x4a, 0x8c, 0xa7, 0x21,
	0x7f, 0x89, 0x09, 0x0f, 0x47, 0x02, 0xed, 0x42, 0x31, 0x50, 0x5f, 0x2a, 0xc2, 0x3a, 0x8e, 0x47,
	0xe8, 0x1c, 0x76, 0x43, 0x4e, 0x82, 0x48, 0xd9, 0x13, 0x4b, 0x05, 0xe0, 0xd6, 0xd8, 0x66, 0x0a,
	0x5b, 0xa5, 0xf3, 0x60, 0x5a, 0xde, 0x57, 0x11, 0xdb, 0x43, 0x4e, 0x02, 0x25, 0xed, 0xc9, 0x63,
	0xe5, 0xd4, 0xb3, 0xd9, 0xa1, 0x27, 0x82, 0x09, 0xfe, 0x5f, 0x38, 0x3f, 0xd3, 0x38, 0x02, 0x63,
	0x99, 0x03, 0xd2, 0x21, 0xff, 0x8a, 0x4c, 0xe2, 0xa3, 0x2b, 0x3f, 0xd1, 0x35, 0x58, 0xbf, 0xb4,
	0x47, 0x61, 0x24, 0xb6, 0x2a, 0x8e, 0x06, 0x9f, 0xe5, 0x3e, 0xd1, 0xcc, 0xef, 0x35, 0xd8, 0x92,
	0x20, 0x06, 0xbe, 0x0c, 0xc7, 0x31, 0x79, 0x8d, 0xea, 0x50, 0x0e, 0xa9, 0x7b, 0x25, 0xa5, 0x2a,
	0x2e, 0x85, 0xd4, 0x55, 0xe7, 0xf9, 0x00, 0x36, 0xa7, 0x8e, 0x6c, 0x4e, 0x15, 0xaf, 0x99, 0xee,
	0x69, 0x49, 0x47, 0xc1, 0x53, 0x5e, 0x69, 0x7f, 0xcc, 0x5f, 0xf5, 0x47, 0xf3, 0xe7, 0x1c, 0xe8,
	0x12, 0x47, 0x5a, 0x26, 0x89, 0xe4, 0x08, 0x6a, 0xe3, 0x64, 0x6c, 0x25, 0xc5, 0x89, 0x04, 0x53,
	0x4f, 0x73, 0xce, 0x16, 0x16, 0xeb, 0xe3, 0xd9, 0x52, 0xbf, 0x1f, 0xd8, 0x37, 0x60, 0x83, 0xbf,
	0xa2, 0xcc, 0x0a, 0xa9, 0xcb, 0x8d, 0xbc, 0x22, 0xa6, 0x2c, 0x0d, 0x43, 0xea, 0x72, 0xf4, 0x04,
	0xd0, 0x1c, 0x54, 0x6e, 0x14, 0x54, 0xcd, 0x57, 0x60, 0xad, 0xcd, 0x62, 0xe5, 0x8b, 0x6e, 0x0f,
	0x93, 0xc3, 0xf6, 0x54, 0x91, 0x38, 0x5b, 0xd9, 0xa4, 0x0f, 0x20, 0xea, 0x94, 0x96, 0xcc, 0x6b,
	0xc5, 0x3a, 0x8d, 0x36, 0x6d, 0x2c, 0xd3, 0x1f, 0xde, 0x66, 0xd3, 0x06, 0x73, 0x0f, 0x6a, 0x33,
	0x15, 0x59, 0x9d, 0xd6, 0xfc, 0x49, 0x83, 0xf2, 0x69, 0x40, 0xb8, 0xc2, 0x50, 0x97, 0x17, 0x9e,
	0xc4, 0x40, 0x59, 0xbc, 0xb0, 0xa4, 0xc6, 0x5d, 0x86, 0x6e, 0x02, 0xc4, 0xf0, 0xfc, 0x40, 0xc4,
	0x92, 0xdc, 0x88, 0xb2, 0xfb, 0x81, 0x90, 0xf2, 0x0d, 0xd3, 0x1b, 0x43, 0x7e, 0x4a, 0xe6, 0xe3,
	0x26, 0x4f, 0xdd, 0xf8, 0xbe, 0x2d, 0x47, 0x86, 0xae, 0x8b, 0xee, 0x42, 0x75, 0xaa, 0x13, 0x28,
	0xe2, 0xaa, 0x78, 0x33, 0xdb, 0x04, 0x64, 0x63, 0xda, 0x89, 0x18, 0x4c, 0x00, 0x4a, 0x3d, 0x4b,
	0x8d, 0x7d, 0x0c, 0x55, 0x16, 0x9b, 0xb2, 0xdd, 0xb3, 0x96, 0xe1, 0x29, 0x9a, 0xc5, 0x9b, 0x2c,
	0xe3, 0xfa, 0x7e, 0x34, 0x65, 0xfe, 0xa0, 0x41, 0xf3, 0x98, 0x08, 0x4c, 0x46, 0xd4, 0x7e, 0x31,
	0x22, 0x73, 0x57, 0x9e, 0x3a, 0x90, 0x31, 0x21, 0xda, 0x15, 0x21, 0xff, 0x87, 0xad, 0x84, 0x75,
	0xeb, 0x05, 0xb9, 0xa0, 0x5e, 0xcc, 0x62, 0x35, 0xb1, 0x3e, 0x92, 0x46, 0x79, 0x19, 0xa5, 0xcb,
	0x88, 0x97, 0x50, 0x5a, 0x49, 0x6c, 0x87, 0x9e, 0x2b, 0x3b, 0x43, 0x74, 0xac, 0x22, 0x5a, 0xa3,
	0x81, 0xf9, 0x9b, 0x06, 0xf5, 0xa5, 0x98, 0xfe, 0x4b, 0xef, 0x03, 0x73, 0x0c, 0x77, 0xde, 0xc2,
	0x2f, 0x67, 0xe8, 0x09, 0x54, 0xb3, 0x99, 0x78, 0xac, 0x01, 0x33, 0x4d, 0xb5, 0xd4, 0x1f, 0x4f,
	0x3b, 0x9a, 0x7d, 0xd8, 0xe9, 0x11, 0xce, 0xed, 0x0b, 0x82, 0x89, 0x43, 0xe8, 0x25, 0x71, 0xf7,
	0x9d, 0x57, 0x8b, 0x6b, 0x78, 0x17, 0xd2, 0x6a, 0x65, 0x49, 0x4a, 0x2b, 0xa6, 0x2e, 0x2e, 0x03,
	0x76, 0x17, 0xc5, 0xe3, 0xcc, 0xdc, 0x83, 0x8d, 0x43, 0xf5, 0xe4, 0x7c, 0x4a, 0x26, 0x0b, 0xa2,
	0xeb, 0x90, 0xe7, 0xe4, 0x75, 0x2c, 0x0b, 0xf9, 0xd9, 0xf9, 0xb3, 0x00, 0x05, 0x79, 0x04, 0xd0,
	0x11, 0x54, 0x32, 0xcd, 0x04, 0x5d, 0xbf, 0x22, 0x74, 0xea, 0x1e, 0x68, 0x18, 0x8b, 0x27, 0x38,
	0x33, 0xd7, 0x5a, 0xda, 0x7d, 0x0d, 0x9d, 0x40, 0x75, 0xaa, 0x3f, 0xa0, 0xfa, 0x94, 0x43, 0xb6,
	0x93, 0x37, 0x1a, 0xcb, 0xa6, 0xd2, 0x68, 0xc7, 0xa0, 0x0f, 0x3d, 0x3b, 0x98, 0xbc, 0x2b, 0x34,
	0xd4, 0x03, 0x94, 0x06, 0x7a, 0x77, 0x6c, 0x68, 0x08, 0xd7, 0x33, 0xb8, 0xb2, 0xdd, 0x03, 0xdd,
	0x9a, 0x41, 0x31, 0xd3, 0x5a, 0x56, 0xa2, 0x7c, 0x03, 0x37, 0x57, 0xea, 0x12, 0xdd, 0x4b, 0x9d,
	0xdf, 0xd6, 0x1f, 0x1a, 0x1f, 0xfc, 0xdd, 0xa5, 0x2a, 0xf3, 0x57, 0x80, 0xe6, 0x25, 0x95, 0xd9,
	0xcb, 0x42, 0xfd, 0x36, 0x6e, 0xaf, 0x9c, 0x97, 0x81, 0x3b, 0xbf, 0x6b, 0x50, 0x9f, 0x7f, 0xbd,
	0xf5, 0x6c, 0xcf, 0xbe, 0x20, 0x01, 0xfa, 0x16, 0x76, 0x16, 0xbe, 0x28, 0xd1, 0xdb, 0xde, 0xa9,
	0x8d, 0xab, 0x63, 0xb8, 0xf4, 0x49, 0x6a, 0xae, 0xa1, 0xe7, 0xb0, 0xb3, 0xf0, 0xcd, 0x8c, 0x56,
	0x3d, 0x2d, 0x33, 0xb1, 0x8f, 0x97, 0xc7, 0x7e, 0x74, 0xff, 0x79, 0xfb, 0xc2, 0x1f, 0xd9, 0xde,
	0x45, 0xfb, 0x61, 0x47, 0x88, 0xb6, 0xe3, 0x8f, 0xf7, 0xd4, 0x5f, 0x52, 0xc7, 0x1f, 0xed, 0x71,
	0x12, 0x5c, 0x52, 0x87, 0xf0, 0x3d, 0x19, 0x68, 0xef, 0xf2, 0xc1, 0xe7, 0xf2, 0xf7, 0x45, 0x51,
	0xcd, 0x7f, 0xf4, 0x57, 0x00, 0x00, 0x00, 0xff, 0xff, 0x09, 0x58, 0xa8, 0x36, 0xbf, 0x0e, 0x00,
	0x00,
}
