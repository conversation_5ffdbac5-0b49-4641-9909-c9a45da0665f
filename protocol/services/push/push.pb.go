// Code generated by protoc-gen-gogo.
// source: services/push/push.proto
// DO NOT EDIT!

/*
	Package Push is a generated protocol buffer package.

	namespace

	It is generated from these files:
		services/push/push.proto

	It has these top-level messages:
		PushMessage
		CreatePushMessageReq
		CreatePushMessageResp
		ReviewPushMessageReq
		ReviewPushMessageResp
		DeletePushMessageReq
		DeletePushMessageResp
		GetPushMessageReq
		GetPushMessageResp
		GetPushMessageListReq
		GetPushMessageListResp
		BatGetPushMessageReq
		BatGetPushMessageResp
		PushMessageV2
		CreatePushMessageV2Req
		CreatePushMessageV2Resp
		GetPushMessageV2Req
		GetPushMessageV2Resp
		GetPushMessageV2ListForBizReq
		GetPushMessageV2ListForBizResp
		UpdatePushMessageStatusV2Req
		UpdatePushMessageStatusV2Resp
*/
package Push

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type PLATFORM int32

const (
	PLATFORM_ANDROID      PLATFORM = 1
	PLATFORM_IPHONE       PLATFORM = 2
	PLATFORM_ALL_PLATFORM PLATFORM = 65535
)

var PLATFORM_name = map[int32]string{
	1:     "ANDROID",
	2:     "IPHONE",
	65535: "ALL_PLATFORM",
}
var PLATFORM_value = map[string]int32{
	"ANDROID":      1,
	"IPHONE":       2,
	"ALL_PLATFORM": 65535,
}

func (x PLATFORM) Enum() *PLATFORM {
	p := new(PLATFORM)
	*p = x
	return p
}
func (x PLATFORM) String() string {
	return proto.EnumName(PLATFORM_name, int32(x))
}
func (x *PLATFORM) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PLATFORM_value, data, "PLATFORM")
	if err != nil {
		return err
	}
	*x = PLATFORM(value)
	return nil
}
func (PLATFORM) EnumDescriptor() ([]byte, []int) { return fileDescriptorPush, []int{0} }

type PushBizType int32

const (
	PushBizType_GLOBAL         PushBizType = 1
	PushBizType_PUBLIC_ACCOUNT PushBizType = 2
)

var PushBizType_name = map[int32]string{
	1: "GLOBAL",
	2: "PUBLIC_ACCOUNT",
}
var PushBizType_value = map[string]int32{
	"GLOBAL":         1,
	"PUBLIC_ACCOUNT": 2,
}

func (x PushBizType) Enum() *PushBizType {
	p := new(PushBizType)
	*p = x
	return p
}
func (x PushBizType) String() string {
	return proto.EnumName(PushBizType_name, int32(x))
}
func (x *PushBizType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PushBizType_value, data, "PushBizType")
	if err != nil {
		return err
	}
	*x = PushBizType(value)
	return nil
}
func (PushBizType) EnumDescriptor() ([]byte, []int) { return fileDescriptorPush, []int{1} }

type PushMessage_PUSH_MESSAGE_TYPE int32

const (
	PushMessage_ALL         PushMessage_PUSH_MESSAGE_TYPE = 0
	PushMessage_GUILD_OWNER PushMessage_PUSH_MESSAGE_TYPE = 1
)

var PushMessage_PUSH_MESSAGE_TYPE_name = map[int32]string{
	0: "ALL",
	1: "GUILD_OWNER",
}
var PushMessage_PUSH_MESSAGE_TYPE_value = map[string]int32{
	"ALL":         0,
	"GUILD_OWNER": 1,
}

func (x PushMessage_PUSH_MESSAGE_TYPE) Enum() *PushMessage_PUSH_MESSAGE_TYPE {
	p := new(PushMessage_PUSH_MESSAGE_TYPE)
	*p = x
	return p
}
func (x PushMessage_PUSH_MESSAGE_TYPE) String() string {
	return proto.EnumName(PushMessage_PUSH_MESSAGE_TYPE_name, int32(x))
}
func (x *PushMessage_PUSH_MESSAGE_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PushMessage_PUSH_MESSAGE_TYPE_value, data, "PushMessage_PUSH_MESSAGE_TYPE")
	if err != nil {
		return err
	}
	*x = PushMessage_PUSH_MESSAGE_TYPE(value)
	return nil
}
func (PushMessage_PUSH_MESSAGE_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorPush, []int{0, 0}
}

type PushMessage_PUSH_MESSAGE_STATUS int32

const (
	PushMessage_NEW      PushMessage_PUSH_MESSAGE_STATUS = 0
	PushMessage_REVIEWED PushMessage_PUSH_MESSAGE_STATUS = 1
	PushMessage_REFUSED  PushMessage_PUSH_MESSAGE_STATUS = 2
	PushMessage_DELETED  PushMessage_PUSH_MESSAGE_STATUS = 3
	PushMessage_SENT     PushMessage_PUSH_MESSAGE_STATUS = 4
)

var PushMessage_PUSH_MESSAGE_STATUS_name = map[int32]string{
	0: "NEW",
	1: "REVIEWED",
	2: "REFUSED",
	3: "DELETED",
	4: "SENT",
}
var PushMessage_PUSH_MESSAGE_STATUS_value = map[string]int32{
	"NEW":      0,
	"REVIEWED": 1,
	"REFUSED":  2,
	"DELETED":  3,
	"SENT":     4,
}

func (x PushMessage_PUSH_MESSAGE_STATUS) Enum() *PushMessage_PUSH_MESSAGE_STATUS {
	p := new(PushMessage_PUSH_MESSAGE_STATUS)
	*p = x
	return p
}
func (x PushMessage_PUSH_MESSAGE_STATUS) String() string {
	return proto.EnumName(PushMessage_PUSH_MESSAGE_STATUS_name, int32(x))
}
func (x *PushMessage_PUSH_MESSAGE_STATUS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PushMessage_PUSH_MESSAGE_STATUS_value, data, "PushMessage_PUSH_MESSAGE_STATUS")
	if err != nil {
		return err
	}
	*x = PushMessage_PUSH_MESSAGE_STATUS(value)
	return nil
}
func (PushMessage_PUSH_MESSAGE_STATUS) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorPush, []int{0, 1}
}

type PushMessage struct {
	PushMessageId uint32 `protobuf:"varint,1,req,name=push_message_id,json=pushMessageId" json:"push_message_id"`
	Content       string `protobuf:"bytes,2,req,name=content" json:"content"`
	Type          uint32 `protobuf:"varint,3,req,name=type" json:"type"`
	Status        uint32 `protobuf:"varint,4,req,name=status" json:"status"`
	CreatorUid    uint32 `protobuf:"varint,5,req,name=creator_uid,json=creatorUid" json:"creator_uid"`
	ReviewUid     uint32 `protobuf:"varint,6,req,name=review_uid,json=reviewUid" json:"review_uid"`
	DeleteUid     uint32 `protobuf:"varint,7,req,name=delete_uid,json=deleteUid" json:"delete_uid"`
	CreateTime    uint32 `protobuf:"varint,8,req,name=create_time,json=createTime" json:"create_time"`
	PushTime      uint32 `protobuf:"varint,9,req,name=push_time,json=pushTime" json:"push_time"`
	ExpireTime    uint32 `protobuf:"varint,10,req,name=expire_time,json=expireTime" json:"expire_time"`
	Badge         uint32 `protobuf:"varint,11,req,name=badge" json:"badge"`
	Sound         bool   `protobuf:"varint,12,req,name=sound" json:"sound"`
	ExtContent    string `protobuf:"bytes,13,req,name=ext_content,json=extContent" json:"ext_content"`
	ReceiverGroup string `protobuf:"bytes,14,req,name=receiver_group,json=receiverGroup" json:"receiver_group"`
	RefuseReason  string `protobuf:"bytes,15,req,name=refuse_reason,json=refuseReason" json:"refuse_reason"`
}

func (m *PushMessage) Reset()                    { *m = PushMessage{} }
func (m *PushMessage) String() string            { return proto.CompactTextString(m) }
func (*PushMessage) ProtoMessage()               {}
func (*PushMessage) Descriptor() ([]byte, []int) { return fileDescriptorPush, []int{0} }

func (m *PushMessage) GetPushMessageId() uint32 {
	if m != nil {
		return m.PushMessageId
	}
	return 0
}

func (m *PushMessage) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PushMessage) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *PushMessage) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *PushMessage) GetCreatorUid() uint32 {
	if m != nil {
		return m.CreatorUid
	}
	return 0
}

func (m *PushMessage) GetReviewUid() uint32 {
	if m != nil {
		return m.ReviewUid
	}
	return 0
}

func (m *PushMessage) GetDeleteUid() uint32 {
	if m != nil {
		return m.DeleteUid
	}
	return 0
}

func (m *PushMessage) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *PushMessage) GetPushTime() uint32 {
	if m != nil {
		return m.PushTime
	}
	return 0
}

func (m *PushMessage) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *PushMessage) GetBadge() uint32 {
	if m != nil {
		return m.Badge
	}
	return 0
}

func (m *PushMessage) GetSound() bool {
	if m != nil {
		return m.Sound
	}
	return false
}

func (m *PushMessage) GetExtContent() string {
	if m != nil {
		return m.ExtContent
	}
	return ""
}

func (m *PushMessage) GetReceiverGroup() string {
	if m != nil {
		return m.ReceiverGroup
	}
	return ""
}

func (m *PushMessage) GetRefuseReason() string {
	if m != nil {
		return m.RefuseReason
	}
	return ""
}

type CreatePushMessageReq struct {
	Content       string `protobuf:"bytes,1,req,name=content" json:"content"`
	Type          uint32 `protobuf:"varint,2,req,name=type" json:"type"`
	CreatorUid    uint32 `protobuf:"varint,3,req,name=creator_uid,json=creatorUid" json:"creator_uid"`
	PushTime      uint32 `protobuf:"varint,4,req,name=push_time,json=pushTime" json:"push_time"`
	ExpireTime    uint32 `protobuf:"varint,5,req,name=expire_time,json=expireTime" json:"expire_time"`
	Badge         uint32 `protobuf:"varint,6,req,name=badge" json:"badge"`
	Sound         bool   `protobuf:"varint,7,req,name=sound" json:"sound"`
	ExtContent    string `protobuf:"bytes,8,req,name=ext_content,json=extContent" json:"ext_content"`
	ReceiverGroup string `protobuf:"bytes,9,req,name=receiver_group,json=receiverGroup" json:"receiver_group"`
}

func (m *CreatePushMessageReq) Reset()                    { *m = CreatePushMessageReq{} }
func (m *CreatePushMessageReq) String() string            { return proto.CompactTextString(m) }
func (*CreatePushMessageReq) ProtoMessage()               {}
func (*CreatePushMessageReq) Descriptor() ([]byte, []int) { return fileDescriptorPush, []int{1} }

func (m *CreatePushMessageReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *CreatePushMessageReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *CreatePushMessageReq) GetCreatorUid() uint32 {
	if m != nil {
		return m.CreatorUid
	}
	return 0
}

func (m *CreatePushMessageReq) GetPushTime() uint32 {
	if m != nil {
		return m.PushTime
	}
	return 0
}

func (m *CreatePushMessageReq) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *CreatePushMessageReq) GetBadge() uint32 {
	if m != nil {
		return m.Badge
	}
	return 0
}

func (m *CreatePushMessageReq) GetSound() bool {
	if m != nil {
		return m.Sound
	}
	return false
}

func (m *CreatePushMessageReq) GetExtContent() string {
	if m != nil {
		return m.ExtContent
	}
	return ""
}

func (m *CreatePushMessageReq) GetReceiverGroup() string {
	if m != nil {
		return m.ReceiverGroup
	}
	return ""
}

type CreatePushMessageResp struct {
	PushMessageId uint32 `protobuf:"varint,1,req,name=push_message_id,json=pushMessageId" json:"push_message_id"`
}

func (m *CreatePushMessageResp) Reset()                    { *m = CreatePushMessageResp{} }
func (m *CreatePushMessageResp) String() string            { return proto.CompactTextString(m) }
func (*CreatePushMessageResp) ProtoMessage()               {}
func (*CreatePushMessageResp) Descriptor() ([]byte, []int) { return fileDescriptorPush, []int{2} }

func (m *CreatePushMessageResp) GetPushMessageId() uint32 {
	if m != nil {
		return m.PushMessageId
	}
	return 0
}

type ReviewPushMessageReq struct {
	PushMessageId uint32 `protobuf:"varint,1,req,name=push_message_id,json=pushMessageId" json:"push_message_id"`
	ReviewUid     uint32 `protobuf:"varint,2,req,name=review_uid,json=reviewUid" json:"review_uid"`
	Agree         bool   `protobuf:"varint,3,req,name=agree" json:"agree"`
	RefuseReason  string `protobuf:"bytes,4,req,name=refuse_reason,json=refuseReason" json:"refuse_reason"`
}

func (m *ReviewPushMessageReq) Reset()                    { *m = ReviewPushMessageReq{} }
func (m *ReviewPushMessageReq) String() string            { return proto.CompactTextString(m) }
func (*ReviewPushMessageReq) ProtoMessage()               {}
func (*ReviewPushMessageReq) Descriptor() ([]byte, []int) { return fileDescriptorPush, []int{3} }

func (m *ReviewPushMessageReq) GetPushMessageId() uint32 {
	if m != nil {
		return m.PushMessageId
	}
	return 0
}

func (m *ReviewPushMessageReq) GetReviewUid() uint32 {
	if m != nil {
		return m.ReviewUid
	}
	return 0
}

func (m *ReviewPushMessageReq) GetAgree() bool {
	if m != nil {
		return m.Agree
	}
	return false
}

func (m *ReviewPushMessageReq) GetRefuseReason() string {
	if m != nil {
		return m.RefuseReason
	}
	return ""
}

type ReviewPushMessageResp struct {
}

func (m *ReviewPushMessageResp) Reset()                    { *m = ReviewPushMessageResp{} }
func (m *ReviewPushMessageResp) String() string            { return proto.CompactTextString(m) }
func (*ReviewPushMessageResp) ProtoMessage()               {}
func (*ReviewPushMessageResp) Descriptor() ([]byte, []int) { return fileDescriptorPush, []int{4} }

type DeletePushMessageReq struct {
	PushMessageId uint32 `protobuf:"varint,1,req,name=push_message_id,json=pushMessageId" json:"push_message_id"`
	DeleteUid     uint32 `protobuf:"varint,2,req,name=delete_uid,json=deleteUid" json:"delete_uid"`
}

func (m *DeletePushMessageReq) Reset()                    { *m = DeletePushMessageReq{} }
func (m *DeletePushMessageReq) String() string            { return proto.CompactTextString(m) }
func (*DeletePushMessageReq) ProtoMessage()               {}
func (*DeletePushMessageReq) Descriptor() ([]byte, []int) { return fileDescriptorPush, []int{5} }

func (m *DeletePushMessageReq) GetPushMessageId() uint32 {
	if m != nil {
		return m.PushMessageId
	}
	return 0
}

func (m *DeletePushMessageReq) GetDeleteUid() uint32 {
	if m != nil {
		return m.DeleteUid
	}
	return 0
}

type DeletePushMessageResp struct {
}

func (m *DeletePushMessageResp) Reset()                    { *m = DeletePushMessageResp{} }
func (m *DeletePushMessageResp) String() string            { return proto.CompactTextString(m) }
func (*DeletePushMessageResp) ProtoMessage()               {}
func (*DeletePushMessageResp) Descriptor() ([]byte, []int) { return fileDescriptorPush, []int{6} }

type GetPushMessageReq struct {
	PushMessageId uint32 `protobuf:"varint,1,req,name=push_message_id,json=pushMessageId" json:"push_message_id"`
}

func (m *GetPushMessageReq) Reset()                    { *m = GetPushMessageReq{} }
func (m *GetPushMessageReq) String() string            { return proto.CompactTextString(m) }
func (*GetPushMessageReq) ProtoMessage()               {}
func (*GetPushMessageReq) Descriptor() ([]byte, []int) { return fileDescriptorPush, []int{7} }

func (m *GetPushMessageReq) GetPushMessageId() uint32 {
	if m != nil {
		return m.PushMessageId
	}
	return 0
}

type GetPushMessageResp struct {
	PushMessage *PushMessage `protobuf:"bytes,1,req,name=push_message,json=pushMessage" json:"push_message,omitempty"`
}

func (m *GetPushMessageResp) Reset()                    { *m = GetPushMessageResp{} }
func (m *GetPushMessageResp) String() string            { return proto.CompactTextString(m) }
func (*GetPushMessageResp) ProtoMessage()               {}
func (*GetPushMessageResp) Descriptor() ([]byte, []int) { return fileDescriptorPush, []int{8} }

func (m *GetPushMessageResp) GetPushMessage() *PushMessage {
	if m != nil {
		return m.PushMessage
	}
	return nil
}

type GetPushMessageListReq struct {
	Offset uint32 `protobuf:"varint,1,req,name=offset" json:"offset"`
	Limit  uint32 `protobuf:"varint,2,req,name=limit" json:"limit"`
}

func (m *GetPushMessageListReq) Reset()                    { *m = GetPushMessageListReq{} }
func (m *GetPushMessageListReq) String() string            { return proto.CompactTextString(m) }
func (*GetPushMessageListReq) ProtoMessage()               {}
func (*GetPushMessageListReq) Descriptor() ([]byte, []int) { return fileDescriptorPush, []int{9} }

func (m *GetPushMessageListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetPushMessageListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetPushMessageListResp struct {
	PushMessageList []*PushMessage `protobuf:"bytes,1,rep,name=push_message_list,json=pushMessageList" json:"push_message_list,omitempty"`
}

func (m *GetPushMessageListResp) Reset()                    { *m = GetPushMessageListResp{} }
func (m *GetPushMessageListResp) String() string            { return proto.CompactTextString(m) }
func (*GetPushMessageListResp) ProtoMessage()               {}
func (*GetPushMessageListResp) Descriptor() ([]byte, []int) { return fileDescriptorPush, []int{10} }

func (m *GetPushMessageListResp) GetPushMessageList() []*PushMessage {
	if m != nil {
		return m.PushMessageList
	}
	return nil
}

type BatGetPushMessageReq struct {
	PushMessageIdList []uint32 `protobuf:"varint,1,rep,name=push_message_id_list,json=pushMessageIdList" json:"push_message_id_list,omitempty"`
}

func (m *BatGetPushMessageReq) Reset()                    { *m = BatGetPushMessageReq{} }
func (m *BatGetPushMessageReq) String() string            { return proto.CompactTextString(m) }
func (*BatGetPushMessageReq) ProtoMessage()               {}
func (*BatGetPushMessageReq) Descriptor() ([]byte, []int) { return fileDescriptorPush, []int{11} }

func (m *BatGetPushMessageReq) GetPushMessageIdList() []uint32 {
	if m != nil {
		return m.PushMessageIdList
	}
	return nil
}

type BatGetPushMessageResp struct {
	PushMessageList []*PushMessage `protobuf:"bytes,1,rep,name=push_message_list,json=pushMessageList" json:"push_message_list,omitempty"`
}

func (m *BatGetPushMessageResp) Reset()                    { *m = BatGetPushMessageResp{} }
func (m *BatGetPushMessageResp) String() string            { return proto.CompactTextString(m) }
func (*BatGetPushMessageResp) ProtoMessage()               {}
func (*BatGetPushMessageResp) Descriptor() ([]byte, []int) { return fileDescriptorPush, []int{12} }

func (m *BatGetPushMessageResp) GetPushMessageList() []*PushMessage {
	if m != nil {
		return m.PushMessageList
	}
	return nil
}

type PushMessageV2 struct {
	PushId         uint32 `protobuf:"varint,1,req,name=push_id,json=pushId" json:"push_id"`
	MsgType        uint32 `protobuf:"varint,2,req,name=msg_type,json=msgType" json:"msg_type"`
	MsgBin         []byte `protobuf:"bytes,3,req,name=msg_bin,json=msgBin" json:"msg_bin"`
	BizType        uint32 `protobuf:"varint,4,req,name=biz_type,json=bizType" json:"biz_type"`
	BizId          uint64 `protobuf:"varint,5,req,name=biz_id,json=bizId" json:"biz_id"`
	BizData        []byte `protobuf:"bytes,6,req,name=biz_data,json=bizData" json:"biz_data"`
	CreatorUid     uint32 `protobuf:"varint,7,req,name=creator_uid,json=creatorUid" json:"creator_uid"`
	ReviewUid      uint32 `protobuf:"varint,8,req,name=review_uid,json=reviewUid" json:"review_uid"`
	DeleteUid      uint32 `protobuf:"varint,9,req,name=delete_uid,json=deleteUid" json:"delete_uid"`
	CreateTime     uint32 `protobuf:"varint,10,req,name=create_time,json=createTime" json:"create_time"`
	PushTime       uint32 `protobuf:"varint,11,req,name=push_time,json=pushTime" json:"push_time"`
	ExpireTime     uint32 `protobuf:"varint,12,req,name=expire_time,json=expireTime" json:"expire_time"`
	Badge          uint32 `protobuf:"varint,13,req,name=badge" json:"badge"`
	Sound          uint32 `protobuf:"varint,14,req,name=sound" json:"sound"`
	Status         uint32 `protobuf:"varint,15,req,name=status" json:"status"`
	RefuseReason   string `protobuf:"bytes,16,req,name=refuse_reason,json=refuseReason" json:"refuse_reason"`
	TargetPlatform uint32 `protobuf:"varint,17,opt,name=target_platform,json=targetPlatform" json:"target_platform"`
}

func (m *PushMessageV2) Reset()                    { *m = PushMessageV2{} }
func (m *PushMessageV2) String() string            { return proto.CompactTextString(m) }
func (*PushMessageV2) ProtoMessage()               {}
func (*PushMessageV2) Descriptor() ([]byte, []int) { return fileDescriptorPush, []int{13} }

func (m *PushMessageV2) GetPushId() uint32 {
	if m != nil {
		return m.PushId
	}
	return 0
}

func (m *PushMessageV2) GetMsgType() uint32 {
	if m != nil {
		return m.MsgType
	}
	return 0
}

func (m *PushMessageV2) GetMsgBin() []byte {
	if m != nil {
		return m.MsgBin
	}
	return nil
}

func (m *PushMessageV2) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

func (m *PushMessageV2) GetBizId() uint64 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *PushMessageV2) GetBizData() []byte {
	if m != nil {
		return m.BizData
	}
	return nil
}

func (m *PushMessageV2) GetCreatorUid() uint32 {
	if m != nil {
		return m.CreatorUid
	}
	return 0
}

func (m *PushMessageV2) GetReviewUid() uint32 {
	if m != nil {
		return m.ReviewUid
	}
	return 0
}

func (m *PushMessageV2) GetDeleteUid() uint32 {
	if m != nil {
		return m.DeleteUid
	}
	return 0
}

func (m *PushMessageV2) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *PushMessageV2) GetPushTime() uint32 {
	if m != nil {
		return m.PushTime
	}
	return 0
}

func (m *PushMessageV2) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *PushMessageV2) GetBadge() uint32 {
	if m != nil {
		return m.Badge
	}
	return 0
}

func (m *PushMessageV2) GetSound() uint32 {
	if m != nil {
		return m.Sound
	}
	return 0
}

func (m *PushMessageV2) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *PushMessageV2) GetRefuseReason() string {
	if m != nil {
		return m.RefuseReason
	}
	return ""
}

func (m *PushMessageV2) GetTargetPlatform() uint32 {
	if m != nil {
		return m.TargetPlatform
	}
	return 0
}

type CreatePushMessageV2Req struct {
	MsgType        uint32 `protobuf:"varint,1,req,name=msg_type,json=msgType" json:"msg_type"`
	MsgBin         []byte `protobuf:"bytes,2,req,name=msg_bin,json=msgBin" json:"msg_bin"`
	BizType        uint32 `protobuf:"varint,3,req,name=biz_type,json=bizType" json:"biz_type"`
	BizId          uint64 `protobuf:"varint,4,req,name=biz_id,json=bizId" json:"biz_id"`
	BizData        []byte `protobuf:"bytes,5,req,name=biz_data,json=bizData" json:"biz_data"`
	CreatorUid     uint32 `protobuf:"varint,6,req,name=creator_uid,json=creatorUid" json:"creator_uid"`
	ReviewUid      uint32 `protobuf:"varint,7,req,name=review_uid,json=reviewUid" json:"review_uid"`
	DeleteUid      uint32 `protobuf:"varint,8,req,name=delete_uid,json=deleteUid" json:"delete_uid"`
	PushTime       uint32 `protobuf:"varint,9,req,name=push_time,json=pushTime" json:"push_time"`
	ExpireTime     uint32 `protobuf:"varint,10,req,name=expire_time,json=expireTime" json:"expire_time"`
	Badge          uint32 `protobuf:"varint,11,req,name=badge" json:"badge"`
	Sound          uint32 `protobuf:"varint,12,req,name=sound" json:"sound"`
	TargetPlatform uint32 `protobuf:"varint,13,opt,name=target_platform,json=targetPlatform" json:"target_platform"`
}

func (m *CreatePushMessageV2Req) Reset()                    { *m = CreatePushMessageV2Req{} }
func (m *CreatePushMessageV2Req) String() string            { return proto.CompactTextString(m) }
func (*CreatePushMessageV2Req) ProtoMessage()               {}
func (*CreatePushMessageV2Req) Descriptor() ([]byte, []int) { return fileDescriptorPush, []int{14} }

func (m *CreatePushMessageV2Req) GetMsgType() uint32 {
	if m != nil {
		return m.MsgType
	}
	return 0
}

func (m *CreatePushMessageV2Req) GetMsgBin() []byte {
	if m != nil {
		return m.MsgBin
	}
	return nil
}

func (m *CreatePushMessageV2Req) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

func (m *CreatePushMessageV2Req) GetBizId() uint64 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *CreatePushMessageV2Req) GetBizData() []byte {
	if m != nil {
		return m.BizData
	}
	return nil
}

func (m *CreatePushMessageV2Req) GetCreatorUid() uint32 {
	if m != nil {
		return m.CreatorUid
	}
	return 0
}

func (m *CreatePushMessageV2Req) GetReviewUid() uint32 {
	if m != nil {
		return m.ReviewUid
	}
	return 0
}

func (m *CreatePushMessageV2Req) GetDeleteUid() uint32 {
	if m != nil {
		return m.DeleteUid
	}
	return 0
}

func (m *CreatePushMessageV2Req) GetPushTime() uint32 {
	if m != nil {
		return m.PushTime
	}
	return 0
}

func (m *CreatePushMessageV2Req) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *CreatePushMessageV2Req) GetBadge() uint32 {
	if m != nil {
		return m.Badge
	}
	return 0
}

func (m *CreatePushMessageV2Req) GetSound() uint32 {
	if m != nil {
		return m.Sound
	}
	return 0
}

func (m *CreatePushMessageV2Req) GetTargetPlatform() uint32 {
	if m != nil {
		return m.TargetPlatform
	}
	return 0
}

type CreatePushMessageV2Resp struct {
	PushId uint32 `protobuf:"varint,1,req,name=push_id,json=pushId" json:"push_id"`
}

func (m *CreatePushMessageV2Resp) Reset()                    { *m = CreatePushMessageV2Resp{} }
func (m *CreatePushMessageV2Resp) String() string            { return proto.CompactTextString(m) }
func (*CreatePushMessageV2Resp) ProtoMessage()               {}
func (*CreatePushMessageV2Resp) Descriptor() ([]byte, []int) { return fileDescriptorPush, []int{15} }

func (m *CreatePushMessageV2Resp) GetPushId() uint32 {
	if m != nil {
		return m.PushId
	}
	return 0
}

type GetPushMessageV2Req struct {
	PushId uint32 `protobuf:"varint,1,req,name=push_id,json=pushId" json:"push_id"`
}

func (m *GetPushMessageV2Req) Reset()                    { *m = GetPushMessageV2Req{} }
func (m *GetPushMessageV2Req) String() string            { return proto.CompactTextString(m) }
func (*GetPushMessageV2Req) ProtoMessage()               {}
func (*GetPushMessageV2Req) Descriptor() ([]byte, []int) { return fileDescriptorPush, []int{16} }

func (m *GetPushMessageV2Req) GetPushId() uint32 {
	if m != nil {
		return m.PushId
	}
	return 0
}

type GetPushMessageV2Resp struct {
	PushMessage *PushMessageV2 `protobuf:"bytes,1,req,name=push_message,json=pushMessage" json:"push_message,omitempty"`
}

func (m *GetPushMessageV2Resp) Reset()                    { *m = GetPushMessageV2Resp{} }
func (m *GetPushMessageV2Resp) String() string            { return proto.CompactTextString(m) }
func (*GetPushMessageV2Resp) ProtoMessage()               {}
func (*GetPushMessageV2Resp) Descriptor() ([]byte, []int) { return fileDescriptorPush, []int{17} }

func (m *GetPushMessageV2Resp) GetPushMessage() *PushMessageV2 {
	if m != nil {
		return m.PushMessage
	}
	return nil
}

type GetPushMessageV2ListForBizReq struct {
	BizType uint32 `protobuf:"varint,1,req,name=biz_type,json=bizType" json:"biz_type"`
	BizId   uint64 `protobuf:"varint,2,req,name=biz_id,json=bizId" json:"biz_id"`
}

func (m *GetPushMessageV2ListForBizReq) Reset()         { *m = GetPushMessageV2ListForBizReq{} }
func (m *GetPushMessageV2ListForBizReq) String() string { return proto.CompactTextString(m) }
func (*GetPushMessageV2ListForBizReq) ProtoMessage()    {}
func (*GetPushMessageV2ListForBizReq) Descriptor() ([]byte, []int) {
	return fileDescriptorPush, []int{18}
}

func (m *GetPushMessageV2ListForBizReq) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

func (m *GetPushMessageV2ListForBizReq) GetBizId() uint64 {
	if m != nil {
		return m.BizId
	}
	return 0
}

type GetPushMessageV2ListForBizResp struct {
	PushMessageList []*PushMessageV2 `protobuf:"bytes,1,rep,name=push_message_list,json=pushMessageList" json:"push_message_list,omitempty"`
}

func (m *GetPushMessageV2ListForBizResp) Reset()         { *m = GetPushMessageV2ListForBizResp{} }
func (m *GetPushMessageV2ListForBizResp) String() string { return proto.CompactTextString(m) }
func (*GetPushMessageV2ListForBizResp) ProtoMessage()    {}
func (*GetPushMessageV2ListForBizResp) Descriptor() ([]byte, []int) {
	return fileDescriptorPush, []int{19}
}

func (m *GetPushMessageV2ListForBizResp) GetPushMessageList() []*PushMessageV2 {
	if m != nil {
		return m.PushMessageList
	}
	return nil
}

type UpdatePushMessageStatusV2Req struct {
	PushId       uint32 `protobuf:"varint,1,req,name=push_id,json=pushId" json:"push_id"`
	Status       uint32 `protobuf:"varint,2,req,name=status" json:"status"`
	RefuseReason string `protobuf:"bytes,3,opt,name=refuse_reason,json=refuseReason" json:"refuse_reason"`
}

func (m *UpdatePushMessageStatusV2Req) Reset()         { *m = UpdatePushMessageStatusV2Req{} }
func (m *UpdatePushMessageStatusV2Req) String() string { return proto.CompactTextString(m) }
func (*UpdatePushMessageStatusV2Req) ProtoMessage()    {}
func (*UpdatePushMessageStatusV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptorPush, []int{20}
}

func (m *UpdatePushMessageStatusV2Req) GetPushId() uint32 {
	if m != nil {
		return m.PushId
	}
	return 0
}

func (m *UpdatePushMessageStatusV2Req) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *UpdatePushMessageStatusV2Req) GetRefuseReason() string {
	if m != nil {
		return m.RefuseReason
	}
	return ""
}

type UpdatePushMessageStatusV2Resp struct {
}

func (m *UpdatePushMessageStatusV2Resp) Reset()         { *m = UpdatePushMessageStatusV2Resp{} }
func (m *UpdatePushMessageStatusV2Resp) String() string { return proto.CompactTextString(m) }
func (*UpdatePushMessageStatusV2Resp) ProtoMessage()    {}
func (*UpdatePushMessageStatusV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptorPush, []int{21}
}

func init() {
	proto.RegisterType((*PushMessage)(nil), "Push.PushMessage")
	proto.RegisterType((*CreatePushMessageReq)(nil), "Push.CreatePushMessageReq")
	proto.RegisterType((*CreatePushMessageResp)(nil), "Push.CreatePushMessageResp")
	proto.RegisterType((*ReviewPushMessageReq)(nil), "Push.ReviewPushMessageReq")
	proto.RegisterType((*ReviewPushMessageResp)(nil), "Push.ReviewPushMessageResp")
	proto.RegisterType((*DeletePushMessageReq)(nil), "Push.DeletePushMessageReq")
	proto.RegisterType((*DeletePushMessageResp)(nil), "Push.DeletePushMessageResp")
	proto.RegisterType((*GetPushMessageReq)(nil), "Push.GetPushMessageReq")
	proto.RegisterType((*GetPushMessageResp)(nil), "Push.GetPushMessageResp")
	proto.RegisterType((*GetPushMessageListReq)(nil), "Push.GetPushMessageListReq")
	proto.RegisterType((*GetPushMessageListResp)(nil), "Push.GetPushMessageListResp")
	proto.RegisterType((*BatGetPushMessageReq)(nil), "Push.BatGetPushMessageReq")
	proto.RegisterType((*BatGetPushMessageResp)(nil), "Push.BatGetPushMessageResp")
	proto.RegisterType((*PushMessageV2)(nil), "Push.PushMessageV2")
	proto.RegisterType((*CreatePushMessageV2Req)(nil), "Push.CreatePushMessageV2Req")
	proto.RegisterType((*CreatePushMessageV2Resp)(nil), "Push.CreatePushMessageV2Resp")
	proto.RegisterType((*GetPushMessageV2Req)(nil), "Push.GetPushMessageV2Req")
	proto.RegisterType((*GetPushMessageV2Resp)(nil), "Push.GetPushMessageV2Resp")
	proto.RegisterType((*GetPushMessageV2ListForBizReq)(nil), "Push.GetPushMessageV2ListForBizReq")
	proto.RegisterType((*GetPushMessageV2ListForBizResp)(nil), "Push.GetPushMessageV2ListForBizResp")
	proto.RegisterType((*UpdatePushMessageStatusV2Req)(nil), "Push.UpdatePushMessageStatusV2Req")
	proto.RegisterType((*UpdatePushMessageStatusV2Resp)(nil), "Push.UpdatePushMessageStatusV2Resp")
	proto.RegisterEnum("Push.PLATFORM", PLATFORM_name, PLATFORM_value)
	proto.RegisterEnum("Push.PushBizType", PushBizType_name, PushBizType_value)
	proto.RegisterEnum("Push.PushMessage_PUSH_MESSAGE_TYPE", PushMessage_PUSH_MESSAGE_TYPE_name, PushMessage_PUSH_MESSAGE_TYPE_value)
	proto.RegisterEnum("Push.PushMessage_PUSH_MESSAGE_STATUS", PushMessage_PUSH_MESSAGE_STATUS_name, PushMessage_PUSH_MESSAGE_STATUS_value)
}
func (m *PushMessage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PushMessage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.PushMessageId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintPush(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x18
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x20
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x28
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.CreatorUid))
	dAtA[i] = 0x30
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.ReviewUid))
	dAtA[i] = 0x38
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.DeleteUid))
	dAtA[i] = 0x40
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.CreateTime))
	dAtA[i] = 0x48
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.PushTime))
	dAtA[i] = 0x50
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.ExpireTime))
	dAtA[i] = 0x58
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.Badge))
	dAtA[i] = 0x60
	i++
	if m.Sound {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x6a
	i++
	i = encodeVarintPush(dAtA, i, uint64(len(m.ExtContent)))
	i += copy(dAtA[i:], m.ExtContent)
	dAtA[i] = 0x72
	i++
	i = encodeVarintPush(dAtA, i, uint64(len(m.ReceiverGroup)))
	i += copy(dAtA[i:], m.ReceiverGroup)
	dAtA[i] = 0x7a
	i++
	i = encodeVarintPush(dAtA, i, uint64(len(m.RefuseReason)))
	i += copy(dAtA[i:], m.RefuseReason)
	return i, nil
}

func (m *CreatePushMessageReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreatePushMessageReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintPush(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x10
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x18
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.CreatorUid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.PushTime))
	dAtA[i] = 0x28
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.ExpireTime))
	dAtA[i] = 0x30
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.Badge))
	dAtA[i] = 0x38
	i++
	if m.Sound {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x42
	i++
	i = encodeVarintPush(dAtA, i, uint64(len(m.ExtContent)))
	i += copy(dAtA[i:], m.ExtContent)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintPush(dAtA, i, uint64(len(m.ReceiverGroup)))
	i += copy(dAtA[i:], m.ReceiverGroup)
	return i, nil
}

func (m *CreatePushMessageResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreatePushMessageResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.PushMessageId))
	return i, nil
}

func (m *ReviewPushMessageReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReviewPushMessageReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.PushMessageId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.ReviewUid))
	dAtA[i] = 0x18
	i++
	if m.Agree {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x22
	i++
	i = encodeVarintPush(dAtA, i, uint64(len(m.RefuseReason)))
	i += copy(dAtA[i:], m.RefuseReason)
	return i, nil
}

func (m *ReviewPushMessageResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReviewPushMessageResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DeletePushMessageReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeletePushMessageReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.PushMessageId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.DeleteUid))
	return i, nil
}

func (m *DeletePushMessageResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeletePushMessageResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetPushMessageReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPushMessageReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.PushMessageId))
	return i, nil
}

func (m *GetPushMessageResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPushMessageResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.PushMessage == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("push_message")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintPush(dAtA, i, uint64(m.PushMessage.Size()))
		n1, err := m.PushMessage.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *GetPushMessageListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPushMessageListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GetPushMessageListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPushMessageListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PushMessageList) > 0 {
		for _, msg := range m.PushMessageList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintPush(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatGetPushMessageReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetPushMessageReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PushMessageIdList) > 0 {
		for _, num := range m.PushMessageIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintPush(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatGetPushMessageResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetPushMessageResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PushMessageList) > 0 {
		for _, msg := range m.PushMessageList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintPush(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *PushMessageV2) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PushMessageV2) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.PushId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.MsgType))
	if m.MsgBin != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintPush(dAtA, i, uint64(len(m.MsgBin)))
		i += copy(dAtA[i:], m.MsgBin)
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.BizType))
	dAtA[i] = 0x28
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.BizId))
	if m.BizData != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintPush(dAtA, i, uint64(len(m.BizData)))
		i += copy(dAtA[i:], m.BizData)
	}
	dAtA[i] = 0x38
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.CreatorUid))
	dAtA[i] = 0x40
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.ReviewUid))
	dAtA[i] = 0x48
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.DeleteUid))
	dAtA[i] = 0x50
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.CreateTime))
	dAtA[i] = 0x58
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.PushTime))
	dAtA[i] = 0x60
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.ExpireTime))
	dAtA[i] = 0x68
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.Badge))
	dAtA[i] = 0x70
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.Sound))
	dAtA[i] = 0x78
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x82
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintPush(dAtA, i, uint64(len(m.RefuseReason)))
	i += copy(dAtA[i:], m.RefuseReason)
	dAtA[i] = 0x88
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.TargetPlatform))
	return i, nil
}

func (m *CreatePushMessageV2Req) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreatePushMessageV2Req) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.MsgType))
	if m.MsgBin != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintPush(dAtA, i, uint64(len(m.MsgBin)))
		i += copy(dAtA[i:], m.MsgBin)
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.BizType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.BizId))
	if m.BizData != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintPush(dAtA, i, uint64(len(m.BizData)))
		i += copy(dAtA[i:], m.BizData)
	}
	dAtA[i] = 0x30
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.CreatorUid))
	dAtA[i] = 0x38
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.ReviewUid))
	dAtA[i] = 0x40
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.DeleteUid))
	dAtA[i] = 0x48
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.PushTime))
	dAtA[i] = 0x50
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.ExpireTime))
	dAtA[i] = 0x58
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.Badge))
	dAtA[i] = 0x60
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.Sound))
	dAtA[i] = 0x68
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.TargetPlatform))
	return i, nil
}

func (m *CreatePushMessageV2Resp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreatePushMessageV2Resp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.PushId))
	return i, nil
}

func (m *GetPushMessageV2Req) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPushMessageV2Req) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.PushId))
	return i, nil
}

func (m *GetPushMessageV2Resp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPushMessageV2Resp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.PushMessage == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("push_message")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintPush(dAtA, i, uint64(m.PushMessage.Size()))
		n2, err := m.PushMessage.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *GetPushMessageV2ListForBizReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPushMessageV2ListForBizReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.BizType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.BizId))
	return i, nil
}

func (m *GetPushMessageV2ListForBizResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPushMessageV2ListForBizResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PushMessageList) > 0 {
		for _, msg := range m.PushMessageList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintPush(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UpdatePushMessageStatusV2Req) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdatePushMessageStatusV2Req) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.PushId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPush(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintPush(dAtA, i, uint64(len(m.RefuseReason)))
	i += copy(dAtA[i:], m.RefuseReason)
	return i, nil
}

func (m *UpdatePushMessageStatusV2Resp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdatePushMessageStatusV2Resp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Push(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Push(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintPush(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *PushMessage) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPush(uint64(m.PushMessageId))
	l = len(m.Content)
	n += 1 + l + sovPush(uint64(l))
	n += 1 + sovPush(uint64(m.Type))
	n += 1 + sovPush(uint64(m.Status))
	n += 1 + sovPush(uint64(m.CreatorUid))
	n += 1 + sovPush(uint64(m.ReviewUid))
	n += 1 + sovPush(uint64(m.DeleteUid))
	n += 1 + sovPush(uint64(m.CreateTime))
	n += 1 + sovPush(uint64(m.PushTime))
	n += 1 + sovPush(uint64(m.ExpireTime))
	n += 1 + sovPush(uint64(m.Badge))
	n += 2
	l = len(m.ExtContent)
	n += 1 + l + sovPush(uint64(l))
	l = len(m.ReceiverGroup)
	n += 1 + l + sovPush(uint64(l))
	l = len(m.RefuseReason)
	n += 1 + l + sovPush(uint64(l))
	return n
}

func (m *CreatePushMessageReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Content)
	n += 1 + l + sovPush(uint64(l))
	n += 1 + sovPush(uint64(m.Type))
	n += 1 + sovPush(uint64(m.CreatorUid))
	n += 1 + sovPush(uint64(m.PushTime))
	n += 1 + sovPush(uint64(m.ExpireTime))
	n += 1 + sovPush(uint64(m.Badge))
	n += 2
	l = len(m.ExtContent)
	n += 1 + l + sovPush(uint64(l))
	l = len(m.ReceiverGroup)
	n += 1 + l + sovPush(uint64(l))
	return n
}

func (m *CreatePushMessageResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPush(uint64(m.PushMessageId))
	return n
}

func (m *ReviewPushMessageReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPush(uint64(m.PushMessageId))
	n += 1 + sovPush(uint64(m.ReviewUid))
	n += 2
	l = len(m.RefuseReason)
	n += 1 + l + sovPush(uint64(l))
	return n
}

func (m *ReviewPushMessageResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DeletePushMessageReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPush(uint64(m.PushMessageId))
	n += 1 + sovPush(uint64(m.DeleteUid))
	return n
}

func (m *DeletePushMessageResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetPushMessageReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPush(uint64(m.PushMessageId))
	return n
}

func (m *GetPushMessageResp) Size() (n int) {
	var l int
	_ = l
	if m.PushMessage != nil {
		l = m.PushMessage.Size()
		n += 1 + l + sovPush(uint64(l))
	}
	return n
}

func (m *GetPushMessageListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPush(uint64(m.Offset))
	n += 1 + sovPush(uint64(m.Limit))
	return n
}

func (m *GetPushMessageListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.PushMessageList) > 0 {
		for _, e := range m.PushMessageList {
			l = e.Size()
			n += 1 + l + sovPush(uint64(l))
		}
	}
	return n
}

func (m *BatGetPushMessageReq) Size() (n int) {
	var l int
	_ = l
	if len(m.PushMessageIdList) > 0 {
		for _, e := range m.PushMessageIdList {
			n += 1 + sovPush(uint64(e))
		}
	}
	return n
}

func (m *BatGetPushMessageResp) Size() (n int) {
	var l int
	_ = l
	if len(m.PushMessageList) > 0 {
		for _, e := range m.PushMessageList {
			l = e.Size()
			n += 1 + l + sovPush(uint64(l))
		}
	}
	return n
}

func (m *PushMessageV2) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPush(uint64(m.PushId))
	n += 1 + sovPush(uint64(m.MsgType))
	if m.MsgBin != nil {
		l = len(m.MsgBin)
		n += 1 + l + sovPush(uint64(l))
	}
	n += 1 + sovPush(uint64(m.BizType))
	n += 1 + sovPush(uint64(m.BizId))
	if m.BizData != nil {
		l = len(m.BizData)
		n += 1 + l + sovPush(uint64(l))
	}
	n += 1 + sovPush(uint64(m.CreatorUid))
	n += 1 + sovPush(uint64(m.ReviewUid))
	n += 1 + sovPush(uint64(m.DeleteUid))
	n += 1 + sovPush(uint64(m.CreateTime))
	n += 1 + sovPush(uint64(m.PushTime))
	n += 1 + sovPush(uint64(m.ExpireTime))
	n += 1 + sovPush(uint64(m.Badge))
	n += 1 + sovPush(uint64(m.Sound))
	n += 1 + sovPush(uint64(m.Status))
	l = len(m.RefuseReason)
	n += 2 + l + sovPush(uint64(l))
	n += 2 + sovPush(uint64(m.TargetPlatform))
	return n
}

func (m *CreatePushMessageV2Req) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPush(uint64(m.MsgType))
	if m.MsgBin != nil {
		l = len(m.MsgBin)
		n += 1 + l + sovPush(uint64(l))
	}
	n += 1 + sovPush(uint64(m.BizType))
	n += 1 + sovPush(uint64(m.BizId))
	if m.BizData != nil {
		l = len(m.BizData)
		n += 1 + l + sovPush(uint64(l))
	}
	n += 1 + sovPush(uint64(m.CreatorUid))
	n += 1 + sovPush(uint64(m.ReviewUid))
	n += 1 + sovPush(uint64(m.DeleteUid))
	n += 1 + sovPush(uint64(m.PushTime))
	n += 1 + sovPush(uint64(m.ExpireTime))
	n += 1 + sovPush(uint64(m.Badge))
	n += 1 + sovPush(uint64(m.Sound))
	n += 1 + sovPush(uint64(m.TargetPlatform))
	return n
}

func (m *CreatePushMessageV2Resp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPush(uint64(m.PushId))
	return n
}

func (m *GetPushMessageV2Req) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPush(uint64(m.PushId))
	return n
}

func (m *GetPushMessageV2Resp) Size() (n int) {
	var l int
	_ = l
	if m.PushMessage != nil {
		l = m.PushMessage.Size()
		n += 1 + l + sovPush(uint64(l))
	}
	return n
}

func (m *GetPushMessageV2ListForBizReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPush(uint64(m.BizType))
	n += 1 + sovPush(uint64(m.BizId))
	return n
}

func (m *GetPushMessageV2ListForBizResp) Size() (n int) {
	var l int
	_ = l
	if len(m.PushMessageList) > 0 {
		for _, e := range m.PushMessageList {
			l = e.Size()
			n += 1 + l + sovPush(uint64(l))
		}
	}
	return n
}

func (m *UpdatePushMessageStatusV2Req) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPush(uint64(m.PushId))
	n += 1 + sovPush(uint64(m.Status))
	l = len(m.RefuseReason)
	n += 1 + l + sovPush(uint64(l))
	return n
}

func (m *UpdatePushMessageStatusV2Resp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovPush(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozPush(x uint64) (n int) {
	return sovPush(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *PushMessage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PushMessage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PushMessage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushMessageId", wireType)
			}
			m.PushMessageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PushMessageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPush
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreatorUid", wireType)
			}
			m.CreatorUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreatorUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReviewUid", wireType)
			}
			m.ReviewUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReviewUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeleteUid", wireType)
			}
			m.DeleteUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeleteUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushTime", wireType)
			}
			m.PushTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PushTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Badge", wireType)
			}
			m.Badge = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Badge |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000400)
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sound", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Sound = bool(v != 0)
			hasFields[0] |= uint64(0x00000800)
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPush
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExtContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00001000)
		case 14:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReceiverGroup", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPush
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReceiverGroup = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00002000)
		case 15:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RefuseReason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPush
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RefuseReason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00004000)
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_message_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("creator_uid")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("review_uid")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("delete_uid")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("create_time")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_time")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("expire_time")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("badge")
	}
	if hasFields[0]&uint64(0x00000800) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sound")
	}
	if hasFields[0]&uint64(0x00001000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ext_content")
	}
	if hasFields[0]&uint64(0x00002000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("receiver_group")
	}
	if hasFields[0]&uint64(0x00004000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("refuse_reason")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreatePushMessageReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreatePushMessageReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreatePushMessageReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPush
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreatorUid", wireType)
			}
			m.CreatorUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreatorUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushTime", wireType)
			}
			m.PushTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PushTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Badge", wireType)
			}
			m.Badge = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Badge |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sound", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Sound = bool(v != 0)
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPush
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExtContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReceiverGroup", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPush
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReceiverGroup = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000100)
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("creator_uid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_time")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("expire_time")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("badge")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sound")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ext_content")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("receiver_group")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreatePushMessageResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreatePushMessageResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreatePushMessageResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushMessageId", wireType)
			}
			m.PushMessageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PushMessageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_message_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReviewPushMessageReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReviewPushMessageReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReviewPushMessageReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushMessageId", wireType)
			}
			m.PushMessageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PushMessageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReviewUid", wireType)
			}
			m.ReviewUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReviewUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Agree", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Agree = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RefuseReason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPush
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RefuseReason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_message_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("review_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("agree")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("refuse_reason")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReviewPushMessageResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReviewPushMessageResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReviewPushMessageResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeletePushMessageReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeletePushMessageReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeletePushMessageReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushMessageId", wireType)
			}
			m.PushMessageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PushMessageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeleteUid", wireType)
			}
			m.DeleteUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeleteUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_message_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("delete_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeletePushMessageResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeletePushMessageResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeletePushMessageResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPushMessageReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPushMessageReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPushMessageReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushMessageId", wireType)
			}
			m.PushMessageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PushMessageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_message_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPushMessageResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPushMessageResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPushMessageResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushMessage", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPush
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.PushMessage == nil {
				m.PushMessage = &PushMessage{}
			}
			if err := m.PushMessage.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_message")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPushMessageListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPushMessageListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPushMessageListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offset")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPushMessageListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPushMessageListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPushMessageListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushMessageList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPush
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PushMessageList = append(m.PushMessageList, &PushMessage{})
			if err := m.PushMessageList[len(m.PushMessageList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetPushMessageReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetPushMessageReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetPushMessageReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPush
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.PushMessageIdList = append(m.PushMessageIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPush
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthPush
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPush
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.PushMessageIdList = append(m.PushMessageIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushMessageIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetPushMessageResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetPushMessageResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetPushMessageResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushMessageList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPush
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PushMessageList = append(m.PushMessageList, &PushMessage{})
			if err := m.PushMessageList[len(m.PushMessageList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PushMessageV2) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PushMessageV2: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PushMessageV2: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushId", wireType)
			}
			m.PushId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PushId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgType", wireType)
			}
			m.MsgType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgBin", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthPush
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MsgBin = append(m.MsgBin[:0], dAtA[iNdEx:postIndex]...)
			if m.MsgBin == nil {
				m.MsgBin = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizType", wireType)
			}
			m.BizType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizId", wireType)
			}
			m.BizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizData", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthPush
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BizData = append(m.BizData[:0], dAtA[iNdEx:postIndex]...)
			if m.BizData == nil {
				m.BizData = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreatorUid", wireType)
			}
			m.CreatorUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreatorUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReviewUid", wireType)
			}
			m.ReviewUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReviewUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeleteUid", wireType)
			}
			m.DeleteUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeleteUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushTime", wireType)
			}
			m.PushTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PushTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000400)
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000800)
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Badge", wireType)
			}
			m.Badge = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Badge |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00001000)
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sound", wireType)
			}
			m.Sound = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sound |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00002000)
		case 15:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00004000)
		case 16:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RefuseReason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPush
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RefuseReason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00008000)
		case 17:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetPlatform", wireType)
			}
			m.TargetPlatform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetPlatform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_bin")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("biz_type")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("biz_id")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("biz_data")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("creator_uid")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("review_uid")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("delete_uid")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("create_time")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_time")
	}
	if hasFields[0]&uint64(0x00000800) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("expire_time")
	}
	if hasFields[0]&uint64(0x00001000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("badge")
	}
	if hasFields[0]&uint64(0x00002000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sound")
	}
	if hasFields[0]&uint64(0x00004000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00008000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("refuse_reason")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreatePushMessageV2Req) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreatePushMessageV2Req: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreatePushMessageV2Req: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgType", wireType)
			}
			m.MsgType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgBin", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthPush
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MsgBin = append(m.MsgBin[:0], dAtA[iNdEx:postIndex]...)
			if m.MsgBin == nil {
				m.MsgBin = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizType", wireType)
			}
			m.BizType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizId", wireType)
			}
			m.BizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizData", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthPush
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BizData = append(m.BizData[:0], dAtA[iNdEx:postIndex]...)
			if m.BizData == nil {
				m.BizData = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreatorUid", wireType)
			}
			m.CreatorUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreatorUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReviewUid", wireType)
			}
			m.ReviewUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReviewUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeleteUid", wireType)
			}
			m.DeleteUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeleteUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushTime", wireType)
			}
			m.PushTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PushTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Badge", wireType)
			}
			m.Badge = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Badge |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000400)
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sound", wireType)
			}
			m.Sound = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sound |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000800)
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetPlatform", wireType)
			}
			m.TargetPlatform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetPlatform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_bin")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("biz_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("biz_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("biz_data")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("creator_uid")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("review_uid")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("delete_uid")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_time")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("expire_time")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("badge")
	}
	if hasFields[0]&uint64(0x00000800) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sound")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreatePushMessageV2Resp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreatePushMessageV2Resp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreatePushMessageV2Resp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushId", wireType)
			}
			m.PushId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PushId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPushMessageV2Req) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPushMessageV2Req: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPushMessageV2Req: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushId", wireType)
			}
			m.PushId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PushId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPushMessageV2Resp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPushMessageV2Resp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPushMessageV2Resp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushMessage", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPush
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.PushMessage == nil {
				m.PushMessage = &PushMessageV2{}
			}
			if err := m.PushMessage.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_message")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPushMessageV2ListForBizReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPushMessageV2ListForBizReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPushMessageV2ListForBizReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizType", wireType)
			}
			m.BizType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizId", wireType)
			}
			m.BizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("biz_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("biz_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPushMessageV2ListForBizResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPushMessageV2ListForBizResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPushMessageV2ListForBizResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushMessageList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPush
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PushMessageList = append(m.PushMessageList, &PushMessageV2{})
			if err := m.PushMessageList[len(m.PushMessageList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdatePushMessageStatusV2Req) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdatePushMessageStatusV2Req: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdatePushMessageStatusV2Req: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushId", wireType)
			}
			m.PushId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PushId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RefuseReason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPush
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPush
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RefuseReason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdatePushMessageStatusV2Resp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPush
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdatePushMessageStatusV2Resp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdatePushMessageStatusV2Resp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipPush(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPush
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipPush(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowPush
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowPush
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowPush
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthPush
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowPush
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipPush(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthPush = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowPush   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("services/push/push.proto", fileDescriptorPush) }

var fileDescriptorPush = []byte{
	// 1647 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x57, 0x4b, 0x6f, 0xdb, 0x56,
	0x16, 0x36, 0x29, 0x59, 0x92, 0x8f, 0x24, 0x5b, 0xba, 0xb6, 0x63, 0x45, 0xb1, 0x9d, 0x3b, 0xcc,
	0x03, 0x99, 0x24, 0x8a, 0x01, 0x23, 0x98, 0x87, 0xa0, 0xd1, 0x40, 0xb2, 0x18, 0xc7, 0x03, 0xc5,
	0xd6, 0xe8, 0x61, 0x63, 0x16, 0x33, 0x04, 0x6d, 0x5d, 0x2b, 0x44, 0xf4, 0xe0, 0xf0, 0x52, 0xae,
	0xe3, 0x6e, 0xba, 0x6a, 0x83, 0xae, 0x8a, 0x02, 0x41, 0xff, 0x80, 0x97, 0x2d, 0xd0, 0x6d, 0xff,
	0x41, 0x36, 0x05, 0xba, 0xed, 0x26, 0x2d, 0xd2, 0x8d, 0xff, 0x45, 0x8a, 0x7b, 0x49, 0xda, 0x94,
	0x48, 0x3d, 0x82, 0x2c, 0xba, 0x31, 0xcc, 0x7b, 0xce, 0x3d, 0xcf, 0xef, 0x7c, 0xf7, 0x08, 0x52,
	0x94, 0x18, 0x27, 0xda, 0x11, 0xa1, 0x1b, 0x7a, 0x9f, 0x3e, 0xe7, 0x7f, 0x1e, 0xe9, 0x46, 0xcf,
	0xec, 0xa1, 0x60, 0xa5, 0x4f, 0x9f, 0xa7, 0x6f, 0x1f, 0xf5, 0x3a, 0x9d, 0x5e, 0x77, 0xc3, 0x6c,
	0x9f, 0xe8, 0xda, 0xd1, 0x8b, 0x36, 0xd9, 0xa0, 0x2f, 0x0e, 0xfb, 0x5a, 0xdb, 0xd4, 0xba, 0xe6,
	0x4b, 0x9d, 0x58, 0xba, 0xd2, 0x77, 0xb3, 0x10, 0x65, 0xea, 0xcf, 0x08, 0xa5, 0x6a, 0x8b, 0xa0,
	0x87, 0xb0, 0xc0, 0x2c, 0x29, 0x1d, 0xeb, 0x5b, 0xd1, 0x9a, 0x29, 0x01, 0x8b, 0xf7, 0xe2, 0xc5,
	0xe0, 0x9b, 0xb7, 0x37, 0x67, 0xaa, 0x71, 0xfd, 0x4a, 0x77, 0xa7, 0x89, 0xd6, 0x21, 0x7c, 0xd4,
	0xeb, 0x9a, 0xa4, 0x6b, 0xa6, 0x44, 0x2c, 0xde, 0x9b, 0xb3, 0xb5, 0x9c, 0x43, 0x94, 0x82, 0x20,
	0xf3, 0x95, 0x0a, 0xb8, 0x4c, 0xf0, 0x13, 0xb4, 0x0a, 0x21, 0x6a, 0xaa, 0x66, 0x9f, 0xa6, 0x82,
	0x2e, 0x99, 0x7d, 0x86, 0xee, 0x40, 0xf4, 0xc8, 0x20, 0xaa, 0xd9, 0x33, 0x94, 0xbe, 0xd6, 0x4c,
	0xcd, 0xba, 0x54, 0xc0, 0x16, 0x34, 0xb4, 0x26, 0xba, 0x05, 0x60, 0x90, 0x13, 0x8d, 0x7c, 0xc2,
	0xb5, 0x42, 0x2e, 0xad, 0x39, 0xeb, 0xdc, 0x56, 0x6a, 0x92, 0x36, 0x31, 0x09, 0x57, 0x0a, 0xbb,
	0x95, 0xac, 0x73, 0xa6, 0xe4, 0x38, 0x24, 0x8a, 0xa9, 0x75, 0x48, 0x2a, 0xe2, 0x71, 0x48, 0xea,
	0x5a, 0x87, 0xa0, 0x3f, 0xc1, 0x1c, 0xaf, 0x0e, 0x57, 0x9a, 0x73, 0x29, 0x45, 0xd8, 0x31, 0x57,
	0xb9, 0x03, 0x51, 0x72, 0xaa, 0x6b, 0x86, 0x6d, 0x09, 0xdc, 0x96, 0x2c, 0x01, 0x57, 0x4b, 0xc3,
	0xec, 0xa1, 0xda, 0x6c, 0x91, 0x54, 0xd4, 0xa5, 0x60, 0x1d, 0x31, 0x19, 0xed, 0xf5, 0xbb, 0xcd,
	0x54, 0x0c, 0x8b, 0xf7, 0x22, 0x8e, 0x8c, 0x1f, 0x59, 0xe6, 0x4d, 0xc5, 0xa9, 0x7a, 0xdc, 0x55,
	0x75, 0x20, 0xa7, 0xe6, 0x96, 0x5d, 0xf8, 0x07, 0x30, 0x6f, 0x90, 0x23, 0xa2, 0x9d, 0x10, 0x43,
	0x69, 0x19, 0xbd, 0xbe, 0x9e, 0x9a, 0x77, 0x69, 0xc6, 0x1d, 0xd9, 0x36, 0x13, 0xa1, 0x3f, 0x43,
	0xdc, 0x20, 0xc7, 0x7d, 0x4a, 0x14, 0x83, 0xa8, 0xb4, 0xd7, 0x4d, 0x2d, 0xb8, 0x74, 0x63, 0x96,
	0xa8, 0xca, 0x25, 0x52, 0x06, 0x92, 0x95, 0x46, 0xed, 0xa9, 0xf2, 0x4c, 0xae, 0xd5, 0x0a, 0xdb,
	0xb2, 0x52, 0xff, 0x4f, 0x45, 0x46, 0x61, 0x08, 0x14, 0xca, 0xe5, 0xc4, 0x0c, 0x5a, 0x80, 0xe8,
	0x76, 0x63, 0xa7, 0x5c, 0x52, 0xf6, 0x0e, 0x76, 0xe5, 0x6a, 0x42, 0x90, 0x2a, 0xb0, 0x38, 0xa0,
	0x5e, 0xab, 0x17, 0xea, 0x8d, 0x1a, 0xbb, 0xb0, 0x2b, 0x1f, 0x24, 0x66, 0x50, 0x0c, 0x22, 0x55,
	0x79, 0x7f, 0x47, 0x3e, 0x90, 0x4b, 0x09, 0x01, 0x45, 0x21, 0x5c, 0x95, 0x9f, 0x34, 0x6a, 0x72,
	0x29, 0x21, 0xb2, 0x8f, 0x92, 0x5c, 0x96, 0xeb, 0x72, 0x29, 0x11, 0x40, 0x11, 0x08, 0xd6, 0xe4,
	0xdd, 0x7a, 0x22, 0x28, 0xfd, 0x28, 0xc2, 0xd2, 0x16, 0x6f, 0x88, 0x0b, 0xb5, 0x55, 0xf2, 0x7f,
	0x37, 0x14, 0x85, 0x71, 0x50, 0x14, 0x3d, 0x50, 0x1c, 0x02, 0x5b, 0x60, 0x04, 0xd8, 0x06, 0x7a,
	0x1f, 0x9c, 0xa6, 0xf7, 0xb3, 0x93, 0x7a, 0x1f, 0x1a, 0xd3, 0xfb, 0xf0, 0xc4, 0xde, 0x47, 0xa6,
	0xee, 0xfd, 0xdc, 0xc8, 0xde, 0x4b, 0x32, 0x2c, 0xfb, 0x94, 0x93, 0xea, 0x1f, 0x46, 0x04, 0xd2,
	0xf7, 0x02, 0x2c, 0x55, 0xf9, 0xc8, 0x0d, 0xb5, 0xe5, 0xc3, 0xf8, 0x64, 0x70, 0xa0, 0x45, 0xff,
	0x81, 0x4e, 0xc3, 0xac, 0xda, 0x32, 0x88, 0xc5, 0x2a, 0x97, 0x25, 0xe2, 0x47, 0x5e, 0x28, 0x07,
	0x47, 0x42, 0x79, 0x05, 0x96, 0x7d, 0x22, 0xa6, 0xba, 0xa4, 0xc1, 0x52, 0x89, 0x13, 0xc3, 0xc7,
	0xa6, 0xe2, 0xa2, 0x1d, 0xd1, 0x97, 0x76, 0x58, 0x0c, 0x3e, 0xae, 0xa8, 0x2e, 0x15, 0x20, 0xb9,
	0x4d, 0xcc, 0x8f, 0x09, 0x40, 0xfa, 0x17, 0xa0, 0x61, 0x13, 0x54, 0x47, 0x8f, 0x21, 0xe6, 0xb6,
	0xc1, 0x0d, 0x44, 0x37, 0x93, 0x8f, 0x98, 0xde, 0x23, 0xb7, 0x72, 0xd4, 0x65, 0x4d, 0xfa, 0x37,
	0x2c, 0x0f, 0xda, 0x2a, 0x6b, 0xd4, 0x64, 0x21, 0xad, 0x42, 0xa8, 0x77, 0x7c, 0x4c, 0x89, 0x39,
	0x10, 0x89, 0x7d, 0xc6, 0x3a, 0xd5, 0xd6, 0x3a, 0x9a, 0x39, 0x90, 0xbe, 0x75, 0x24, 0x1d, 0xc0,
	0x35, 0x3f, 0x93, 0x54, 0x47, 0xff, 0x80, 0xe4, 0x40, 0x9a, 0x6d, 0x8d, 0x32, 0xf3, 0x01, 0xff,
	0x38, 0x17, 0xf4, 0x41, 0x13, 0xd2, 0x36, 0x2c, 0x15, 0x55, 0xd3, 0x5b, 0xbd, 0x0d, 0x58, 0x1a,
	0xaa, 0xde, 0x95, 0xe5, 0x78, 0x35, 0x39, 0x50, 0x3c, 0x6e, 0x68, 0x1f, 0x96, 0x7d, 0x0c, 0x7d,
	0x7c, 0x80, 0xbf, 0x04, 0x21, 0xee, 0x52, 0xd8, 0xdf, 0x44, 0x6b, 0x10, 0xe6, 0x06, 0x87, 0x1a,
	0x1a, 0x62, 0x87, 0x3b, 0x4d, 0x74, 0x13, 0x22, 0x1d, 0xda, 0x52, 0x3c, 0xf4, 0x15, 0xee, 0xd0,
	0x56, 0x9d, 0x31, 0xd8, 0x1a, 0xb0, 0x7f, 0x95, 0x43, 0xad, 0xcb, 0x67, 0x22, 0xe6, 0xdc, 0xef,
	0xd0, 0x56, 0x51, 0xeb, 0xb2, 0xfb, 0x87, 0xda, 0x99, 0x75, 0xdf, 0x4d, 0x5c, 0xe1, 0x43, 0xed,
	0x8c, 0xdf, 0xbf, 0x01, 0x21, 0xa6, 0x60, 0xbf, 0xb4, 0xc1, 0x4b, 0x46, 0xd2, 0xce, 0x2c, 0xef,
	0x4c, 0xd8, 0x54, 0x4d, 0x95, 0x13, 0x56, 0xcc, 0x75, 0xbb, 0xa4, 0x9a, 0xea, 0x30, 0x7f, 0x86,
	0xa7, 0x7a, 0xac, 0x23, 0xd3, 0x3c, 0xd6, 0x73, 0x53, 0x3d, 0xd6, 0x30, 0xcd, 0x63, 0x1d, 0x9d,
	0x86, 0xb0, 0x63, 0x93, 0x08, 0x3b, 0x3e, 0x86, 0xb0, 0xe7, 0xdd, 0x32, 0x8b, 0xb0, 0xaf, 0x96,
	0x9c, 0x05, 0x9f, 0x25, 0xc7, 0xc3, 0x55, 0x89, 0x51, 0x5c, 0x85, 0x32, 0xb0, 0x60, 0xaa, 0x46,
	0x8b, 0x98, 0x8a, 0xde, 0x56, 0xcd, 0xe3, 0x9e, 0xd1, 0x49, 0x25, 0xb1, 0x70, 0x69, 0x71, 0xde,
	0x12, 0x56, 0x6c, 0x99, 0xf4, 0x73, 0x00, 0xae, 0x79, 0x58, 0x7d, 0x7f, 0x93, 0x4d, 0x81, 0x1b,
	0x4b, 0xc2, 0x04, 0x2c, 0x89, 0x13, 0xb0, 0x14, 0x18, 0x8f, 0xa5, 0xe0, 0x78, 0x2c, 0xcd, 0x4e,
	0x81, 0xa5, 0xd0, 0x54, 0x58, 0x0a, 0x4f, 0x83, 0xa5, 0x88, 0x3f, 0x96, 0xfe, 0x80, 0x8d, 0x6e,
	0x08, 0x24, 0x3e, 0xbd, 0x8d, 0x8f, 0xe9, 0xed, 0xdf, 0x60, 0xc5, 0xb7, 0xb5, 0x54, 0x9f, 0x40,
	0x23, 0xd2, 0x63, 0x58, 0x1c, 0x24, 0x33, 0x0b, 0x11, 0x13, 0x6e, 0xed, 0xc2, 0x92, 0xf7, 0x16,
	0xd5, 0xd1, 0x5f, 0x7c, 0x1f, 0x92, 0x45, 0x0f, 0xff, 0xed, 0x6f, 0x0e, 0x3e, 0x25, 0xff, 0x85,
	0xb5, 0x61, 0x7b, 0x8c, 0x15, 0x9f, 0xf4, 0x8c, 0xa2, 0x76, 0x66, 0x23, 0xf4, 0x12, 0x61, 0xc2,
	0x78, 0x84, 0x89, 0x1e, 0x84, 0x49, 0x2a, 0xac, 0x8f, 0x33, 0x4f, 0x75, 0xf4, 0xcf, 0xd1, 0xec,
	0xed, 0x1b, 0xbd, 0x87, 0xbf, 0x3f, 0x17, 0x60, 0xb5, 0xa1, 0x37, 0x07, 0x5b, 0x50, 0xe3, 0x33,
	0x3d, 0x4d, 0x45, 0x5d, 0xac, 0x20, 0x4e, 0xc3, 0x0a, 0x01, 0x2c, 0x8c, 0xd8, 0x60, 0x6e, 0xc2,
	0xda, 0x98, 0x38, 0xa8, 0x7e, 0xff, 0xaf, 0x10, 0xa9, 0x94, 0x0b, 0xf5, 0x27, 0x7b, 0xd5, 0x67,
	0x6c, 0x9f, 0x2e, 0xec, 0x96, 0xaa, 0x7b, 0x3b, 0x6c, 0xd3, 0x06, 0x08, 0xed, 0x54, 0x9e, 0xee,
	0xed, 0xca, 0x09, 0x11, 0x21, 0x88, 0x15, 0xca, 0x65, 0xc5, 0x51, 0x4c, 0xbc, 0x7f, 0x1f, 0xb8,
	0x9f, 0xb1, 0x7e, 0x14, 0x16, 0xed, 0x8a, 0x03, 0x84, 0xb6, 0xcb, 0x7b, 0xc5, 0x42, 0x39, 0x21,
	0x20, 0x04, 0xf3, 0x95, 0x46, 0xb1, 0xbc, 0xb3, 0xa5, 0x14, 0xb6, 0xb6, 0xf6, 0x1a, 0xbb, 0xf5,
	0x84, 0xb8, 0xf9, 0x3a, 0x0a, 0xfc, 0x37, 0x27, 0x7a, 0x25, 0x42, 0xd2, 0x83, 0x4e, 0x94, 0xb6,
	0xca, 0xea, 0xb7, 0xb6, 0xa7, 0x6f, 0x8c, 0x94, 0x51, 0x5d, 0xfa, 0x41, 0xf8, 0xec, 0xfc, 0x22,
	0x20, 0x7c, 0x79, 0x7e, 0x11, 0x40, 0x9d, 0xac, 0x99, 0xed, 0x67, 0x49, 0x56, 0xcf, 0x1e, 0x66,
	0x69, 0xf6, 0x34, 0x6b, 0x64, 0xbf, 0x3e, 0xbf, 0x08, 0x7c, 0x23, 0x64, 0x3a, 0x38, 0x67, 0xb7,
	0x12, 0xdb, 0x9b, 0x71, 0x1e, 0x67, 0x4c, 0x9c, 0x63, 0x00, 0xca, 0xe3, 0x4c, 0x1f, 0xe7, 0x6c,
	0xe2, 0xc0, 0x7d, 0xad, 0x99, 0xc7, 0x19, 0x82, 0x73, 0xd6, 0xd4, 0x62, 0x36, 0xce, 0x79, 0x9c,
	0xd1, 0x71, 0x8e, 0x75, 0xc6, 0xf9, 0x3c, 0xc4, 0x39, 0x3e, 0xb4, 0x79, 0x9c, 0xa1, 0x38, 0xc7,
	0x67, 0x34, 0x8f, 0x33, 0xa7, 0x57, 0x5e, 0xc8, 0xa9, 0x49, 0xba, 0x4d, 0x97, 0x33, 0x03, 0xe7,
	0xb0, 0xb3, 0x55, 0x63, 0xbe, 0x71, 0xe3, 0x3c, 0xfa, 0x56, 0x80, 0xa4, 0x67, 0xbf, 0x74, 0x4a,
	0xe1, 0xb7, 0x2a, 0x3b, 0xa5, 0xf0, 0x5f, 0x4a, 0xff, 0xc7, 0x2a, 0x21, 0xb2, 0x4a, 0x44, 0x3a,
	0xd9, 0x7e, 0x56, 0xb5, 0xf3, 0xdf, 0x66, 0xe9, 0xf3, 0xe8, 0x9d, 0xe8, 0x58, 0x8e, 0x3c, 0x6b,
	0x8b, 0x08, 0xed, 0xa4, 0x55, 0x9c, 0xd3, 0x28, 0xe6, 0x3b, 0xb2, 0x15, 0xb0, 0x05, 0x24, 0x6c,
	0x61, 0x2c, 0x8f, 0xd1, 0x17, 0x02, 0x24, 0x3d, 0xab, 0xa8, 0x13, 0xae, 0xdf, 0x3a, 0xec, 0x84,
	0xeb, 0xbf, 0xbf, 0xfe, 0x9d, 0x85, 0x1b, 0x60, 0xe1, 0x06, 0x59, 0xb8, 0x2c, 0xd4, 0xbb, 0xa3,
	0x43, 0xb5, 0xe8, 0xd8, 0x0a, 0x15, 0xbd, 0x80, 0xf9, 0xc1, 0x09, 0x46, 0x2b, 0x96, 0x27, 0xcf,
	0x4a, 0x97, 0x4e, 0xf9, 0x0b, 0xa8, 0x2e, 0xdd, 0x65, 0xfe, 0x83, 0xcc, 0xbf, 0xd8, 0xe1, 0xde,
	0x97, 0x7d, 0xbd, 0xa3, 0x4f, 0x87, 0x97, 0x64, 0x36, 0xe1, 0xe8, 0x86, 0x9f, 0x5d, 0x7b, 0xe5,
	0x4d, 0xaf, 0x8e, 0x16, 0x52, 0x5d, 0xba, 0xcf, 0x1c, 0xcf, 0xf2, 0xc4, 0x7b, 0xd9, 0x36, 0x77,
	0xbd, 0x92, 0xe9, 0xe1, 0x9c, 0xb5, 0x11, 0xe7, 0x71, 0xa6, 0x8d, 0x73, 0x7c, 0x01, 0xce, 0x63,
	0xf4, 0x12, 0x92, 0x9e, 0x05, 0xd3, 0x29, 0xb9, 0xdf, 0x0a, 0xeb, 0x94, 0xdc, 0x77, 0x2b, 0x95,
	0x1e, 0x30, 0xcf, 0x21, 0x57, 0xca, 0x69, 0x9f, 0x94, 0x31, 0x63, 0xbb, 0x3c, 0x46, 0x47, 0xb0,
	0xe8, 0xf3, 0x8a, 0xa0, 0xd5, 0x11, 0xd3, 0xc8, 0x79, 0x2d, 0xbd, 0x36, 0x46, 0x4a, 0x75, 0x69,
	0x81, 0x05, 0x10, 0x66, 0x01, 0xcc, 0x30, 0xf7, 0x33, 0xe8, 0x39, 0x24, 0x86, 0xb9, 0x18, 0x5d,
	0xf7, 0xab, 0x9e, 0x65, 0x3e, 0x3d, 0x4a, 0x44, 0x75, 0x69, 0x8d, 0xd9, 0x8e, 0xf0, 0xe4, 0x74,
	0x9e, 0x5c, 0xcc, 0x19, 0x5b, 0x45, 0x6b, 0xe6, 0xd1, 0x6b, 0x01, 0xd2, 0xa3, 0x69, 0x1f, 0xdd,
	0xf2, 0xb7, 0x3c, 0xf0, 0xee, 0xa4, 0x6f, 0x4f, 0x56, 0xa2, 0xba, 0xf4, 0x90, 0x05, 0x32, 0xc7,
	0xfb, 0x6b, 0x66, 0xcf, 0x78, 0x28, 0xd7, 0x19, 0xdb, 0x38, 0x4f, 0x56, 0x1e, 0x67, 0xce, 0xac,
	0x2f, 0x16, 0x57, 0x1f, 0xae, 0x8f, 0x64, 0x68, 0x24, 0x59, 0x0e, 0xc7, 0x3d, 0x25, 0xe9, 0x5b,
	0x13, 0x75, 0x9c, 0xc2, 0xc3, 0x55, 0xe1, 0xd3, 0xa1, 0x57, 0xe7, 0x17, 0x81, 0xb7, 0xfd, 0x62,
	0xe2, 0xcd, 0xbb, 0x75, 0xe1, 0xa7, 0x77, 0xeb, 0xc2, 0xaf, 0xef, 0xd6, 0x85, 0xaf, 0x7e, 0x5b,
	0x9f, 0xf9, 0x3d, 0x00, 0x00, 0xff, 0xff, 0xd3, 0xab, 0xee, 0xd4, 0x35, 0x14, 0x00, 0x00,
}
