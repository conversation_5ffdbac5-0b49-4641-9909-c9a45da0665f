// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/iop-iaa-ad/iop-iaa-ad.proto

package iop_iaa_ad // import "golang.52tt.com/protocol/services/iop-iaa-ad"

/*
buf:lint:ignore PACKAGE_DIRECTORY_MATCH
buf:lint:ignore PACKAGE_SAME_DIRECTORY
*/

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type E_OsType int32

const (
	E_OsType_E_OsType_Unknown E_OsType = 0
	E_OsType_E_OsType_Android E_OsType = 1
	E_OsType_E_OsType_Ios     E_OsType = 2
)

var E_OsType_name = map[int32]string{
	0: "E_OsType_Unknown",
	1: "E_OsType_Android",
	2: "E_OsType_Ios",
}
var E_OsType_value = map[string]int32{
	"E_OsType_Unknown": 0,
	"E_OsType_Android": 1,
	"E_OsType_Ios":     2,
}

func (x E_OsType) String() string {
	return proto.EnumName(E_OsType_name, int32(x))
}
func (E_OsType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_iop_iaa_ad_81b53111d89651ca, []int{0}
}

type GetAdPosInfosReq struct {
	AppName              string   `protobuf:"bytes,1,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	AppType              string   `protobuf:"bytes,2,opt,name=app_type,json=appType,proto3" json:"app_type,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	BundleId             string   `protobuf:"bytes,4,opt,name=bundle_id,json=bundleId,proto3" json:"bundle_id,omitempty"`
	Os                   uint32   `protobuf:"varint,5,opt,name=os,proto3" json:"os,omitempty"`
	ClientVer            string   `protobuf:"bytes,6,opt,name=client_ver,json=clientVer,proto3" json:"client_ver,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAdPosInfosReq) Reset()         { *m = GetAdPosInfosReq{} }
func (m *GetAdPosInfosReq) String() string { return proto.CompactTextString(m) }
func (*GetAdPosInfosReq) ProtoMessage()    {}
func (*GetAdPosInfosReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_iop_iaa_ad_81b53111d89651ca, []int{0}
}
func (m *GetAdPosInfosReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAdPosInfosReq.Unmarshal(m, b)
}
func (m *GetAdPosInfosReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAdPosInfosReq.Marshal(b, m, deterministic)
}
func (dst *GetAdPosInfosReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAdPosInfosReq.Merge(dst, src)
}
func (m *GetAdPosInfosReq) XXX_Size() int {
	return xxx_messageInfo_GetAdPosInfosReq.Size(m)
}
func (m *GetAdPosInfosReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAdPosInfosReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAdPosInfosReq proto.InternalMessageInfo

func (m *GetAdPosInfosReq) GetAppName() string {
	if m != nil {
		return m.AppName
	}
	return ""
}

func (m *GetAdPosInfosReq) GetAppType() string {
	if m != nil {
		return m.AppType
	}
	return ""
}

func (m *GetAdPosInfosReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAdPosInfosReq) GetBundleId() string {
	if m != nil {
		return m.BundleId
	}
	return ""
}

func (m *GetAdPosInfosReq) GetOs() uint32 {
	if m != nil {
		return m.Os
	}
	return 0
}

func (m *GetAdPosInfosReq) GetClientVer() string {
	if m != nil {
		return m.ClientVer
	}
	return ""
}

// 广告位信息
type AdPosInfo struct {
	CliPosId             string   `protobuf:"bytes,1,opt,name=cli_pos_id,json=cliPosId,proto3" json:"cli_pos_id,omitempty"`
	AdPosId              string   `protobuf:"bytes,2,opt,name=ad_pos_id,json=adPosId,proto3" json:"ad_pos_id,omitempty"`
	Params               []byte   `protobuf:"bytes,3,opt,name=params,proto3" json:"params,omitempty"`
	AdAppId              string   `protobuf:"bytes,4,opt,name=ad_app_id,json=adAppId,proto3" json:"ad_app_id,omitempty"`
	IopParam             string   `protobuf:"bytes,5,opt,name=iop_param,json=iopParam,proto3" json:"iop_param,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AdPosInfo) Reset()         { *m = AdPosInfo{} }
func (m *AdPosInfo) String() string { return proto.CompactTextString(m) }
func (*AdPosInfo) ProtoMessage()    {}
func (*AdPosInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_iop_iaa_ad_81b53111d89651ca, []int{1}
}
func (m *AdPosInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdPosInfo.Unmarshal(m, b)
}
func (m *AdPosInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdPosInfo.Marshal(b, m, deterministic)
}
func (dst *AdPosInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdPosInfo.Merge(dst, src)
}
func (m *AdPosInfo) XXX_Size() int {
	return xxx_messageInfo_AdPosInfo.Size(m)
}
func (m *AdPosInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AdPosInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AdPosInfo proto.InternalMessageInfo

func (m *AdPosInfo) GetCliPosId() string {
	if m != nil {
		return m.CliPosId
	}
	return ""
}

func (m *AdPosInfo) GetAdPosId() string {
	if m != nil {
		return m.AdPosId
	}
	return ""
}

func (m *AdPosInfo) GetParams() []byte {
	if m != nil {
		return m.Params
	}
	return nil
}

func (m *AdPosInfo) GetAdAppId() string {
	if m != nil {
		return m.AdAppId
	}
	return ""
}

func (m *AdPosInfo) GetIopParam() string {
	if m != nil {
		return m.IopParam
	}
	return ""
}

type GetAdPosInfosResp struct {
	AdPosInfos           []*AdPosInfo `protobuf:"bytes,1,rep,name=ad_pos_infos,json=adPosInfos,proto3" json:"ad_pos_infos,omitempty"`
	AdnConfigs           string       `protobuf:"bytes,2,opt,name=adn_configs,json=adnConfigs,proto3" json:"adn_configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetAdPosInfosResp) Reset()         { *m = GetAdPosInfosResp{} }
func (m *GetAdPosInfosResp) String() string { return proto.CompactTextString(m) }
func (*GetAdPosInfosResp) ProtoMessage()    {}
func (*GetAdPosInfosResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_iop_iaa_ad_81b53111d89651ca, []int{2}
}
func (m *GetAdPosInfosResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAdPosInfosResp.Unmarshal(m, b)
}
func (m *GetAdPosInfosResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAdPosInfosResp.Marshal(b, m, deterministic)
}
func (dst *GetAdPosInfosResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAdPosInfosResp.Merge(dst, src)
}
func (m *GetAdPosInfosResp) XXX_Size() int {
	return xxx_messageInfo_GetAdPosInfosResp.Size(m)
}
func (m *GetAdPosInfosResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAdPosInfosResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAdPosInfosResp proto.InternalMessageInfo

func (m *GetAdPosInfosResp) GetAdPosInfos() []*AdPosInfo {
	if m != nil {
		return m.AdPosInfos
	}
	return nil
}

func (m *GetAdPosInfosResp) GetAdnConfigs() string {
	if m != nil {
		return m.AdnConfigs
	}
	return ""
}

// 信息流-广场 广告位参数(对应位置： ad_feed_square, ad_feed_square_other)
type AdFeedSquareParam struct {
	StartPos             uint32   `protobuf:"varint,1,opt,name=start_pos,json=startPos,proto3" json:"start_pos,omitempty"`
	EndPos               uint32   `protobuf:"varint,2,opt,name=end_pos,json=endPos,proto3" json:"end_pos,omitempty"`
	IntervalNum          uint32   `protobuf:"varint,3,opt,name=interval_num,json=intervalNum,proto3" json:"interval_num,omitempty"`
	AdNum                uint32   `protobuf:"varint,4,opt,name=ad_num,json=adNum,proto3" json:"ad_num,omitempty"`
	TabName              string   `protobuf:"bytes,5,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AdFeedSquareParam) Reset()         { *m = AdFeedSquareParam{} }
func (m *AdFeedSquareParam) String() string { return proto.CompactTextString(m) }
func (*AdFeedSquareParam) ProtoMessage()    {}
func (*AdFeedSquareParam) Descriptor() ([]byte, []int) {
	return fileDescriptor_iop_iaa_ad_81b53111d89651ca, []int{3}
}
func (m *AdFeedSquareParam) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdFeedSquareParam.Unmarshal(m, b)
}
func (m *AdFeedSquareParam) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdFeedSquareParam.Marshal(b, m, deterministic)
}
func (dst *AdFeedSquareParam) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdFeedSquareParam.Merge(dst, src)
}
func (m *AdFeedSquareParam) XXX_Size() int {
	return xxx_messageInfo_AdFeedSquareParam.Size(m)
}
func (m *AdFeedSquareParam) XXX_DiscardUnknown() {
	xxx_messageInfo_AdFeedSquareParam.DiscardUnknown(m)
}

var xxx_messageInfo_AdFeedSquareParam proto.InternalMessageInfo

func (m *AdFeedSquareParam) GetStartPos() uint32 {
	if m != nil {
		return m.StartPos
	}
	return 0
}

func (m *AdFeedSquareParam) GetEndPos() uint32 {
	if m != nil {
		return m.EndPos
	}
	return 0
}

func (m *AdFeedSquareParam) GetIntervalNum() uint32 {
	if m != nil {
		return m.IntervalNum
	}
	return 0
}

func (m *AdFeedSquareParam) GetAdNum() uint32 {
	if m != nil {
		return m.AdNum
	}
	return 0
}

func (m *AdFeedSquareParam) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

// 信息流-个人主页-动态 广告位参数(对应位置：ad_feed_home_dynamic)
type AdFeedHomePageDynamicParam struct {
	MinDynamicNum        uint32   `protobuf:"varint,1,opt,name=min_dynamic_num,json=minDynamicNum,proto3" json:"min_dynamic_num,omitempty"`
	StartPos             uint32   `protobuf:"varint,2,opt,name=start_pos,json=startPos,proto3" json:"start_pos,omitempty"`
	IntervalNum          uint32   `protobuf:"varint,3,opt,name=interval_num,json=intervalNum,proto3" json:"interval_num,omitempty"`
	AdNum                uint32   `protobuf:"varint,4,opt,name=ad_num,json=adNum,proto3" json:"ad_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AdFeedHomePageDynamicParam) Reset()         { *m = AdFeedHomePageDynamicParam{} }
func (m *AdFeedHomePageDynamicParam) String() string { return proto.CompactTextString(m) }
func (*AdFeedHomePageDynamicParam) ProtoMessage()    {}
func (*AdFeedHomePageDynamicParam) Descriptor() ([]byte, []int) {
	return fileDescriptor_iop_iaa_ad_81b53111d89651ca, []int{4}
}
func (m *AdFeedHomePageDynamicParam) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdFeedHomePageDynamicParam.Unmarshal(m, b)
}
func (m *AdFeedHomePageDynamicParam) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdFeedHomePageDynamicParam.Marshal(b, m, deterministic)
}
func (dst *AdFeedHomePageDynamicParam) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdFeedHomePageDynamicParam.Merge(dst, src)
}
func (m *AdFeedHomePageDynamicParam) XXX_Size() int {
	return xxx_messageInfo_AdFeedHomePageDynamicParam.Size(m)
}
func (m *AdFeedHomePageDynamicParam) XXX_DiscardUnknown() {
	xxx_messageInfo_AdFeedHomePageDynamicParam.DiscardUnknown(m)
}

var xxx_messageInfo_AdFeedHomePageDynamicParam proto.InternalMessageInfo

func (m *AdFeedHomePageDynamicParam) GetMinDynamicNum() uint32 {
	if m != nil {
		return m.MinDynamicNum
	}
	return 0
}

func (m *AdFeedHomePageDynamicParam) GetStartPos() uint32 {
	if m != nil {
		return m.StartPos
	}
	return 0
}

func (m *AdFeedHomePageDynamicParam) GetIntervalNum() uint32 {
	if m != nil {
		return m.IntervalNum
	}
	return 0
}

func (m *AdFeedHomePageDynamicParam) GetAdNum() uint32 {
	if m != nil {
		return m.AdNum
	}
	return 0
}

func init() {
	proto.RegisterType((*GetAdPosInfosReq)(nil), "com.quwan.dspiopother.proto.GetAdPosInfosReq")
	proto.RegisterType((*AdPosInfo)(nil), "com.quwan.dspiopother.proto.AdPosInfo")
	proto.RegisterType((*GetAdPosInfosResp)(nil), "com.quwan.dspiopother.proto.GetAdPosInfosResp")
	proto.RegisterType((*AdFeedSquareParam)(nil), "com.quwan.dspiopother.proto.AdFeedSquareParam")
	proto.RegisterType((*AdFeedHomePageDynamicParam)(nil), "com.quwan.dspiopother.proto.AdFeedHomePageDynamicParam")
	proto.RegisterEnum("com.quwan.dspiopother.proto.E_OsType", E_OsType_name, E_OsType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// IAARevenueAdServiceClient is the client API for IAARevenueAdService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type IAARevenueAdServiceClient interface {
	GetAdPosInfos(ctx context.Context, in *GetAdPosInfosReq, opts ...grpc.CallOption) (*GetAdPosInfosResp, error)
	GetDspAdInfos(ctx context.Context, in *GetAdPosInfosReq, opts ...grpc.CallOption) (*GetAdPosInfosResp, error)
}

type iAARevenueAdServiceClient struct {
	cc *grpc.ClientConn
}

func NewIAARevenueAdServiceClient(cc *grpc.ClientConn) IAARevenueAdServiceClient {
	return &iAARevenueAdServiceClient{cc}
}

func (c *iAARevenueAdServiceClient) GetAdPosInfos(ctx context.Context, in *GetAdPosInfosReq, opts ...grpc.CallOption) (*GetAdPosInfosResp, error) {
	out := new(GetAdPosInfosResp)
	err := c.cc.Invoke(ctx, "/com.quwan.dspiopother.proto.IAARevenueAdService/GetAdPosInfos", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iAARevenueAdServiceClient) GetDspAdInfos(ctx context.Context, in *GetAdPosInfosReq, opts ...grpc.CallOption) (*GetAdPosInfosResp, error) {
	out := new(GetAdPosInfosResp)
	err := c.cc.Invoke(ctx, "/com.quwan.dspiopother.proto.IAARevenueAdService/GetDspAdInfos", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// IAARevenueAdServiceServer is the server API for IAARevenueAdService service.
type IAARevenueAdServiceServer interface {
	GetAdPosInfos(context.Context, *GetAdPosInfosReq) (*GetAdPosInfosResp, error)
	GetDspAdInfos(context.Context, *GetAdPosInfosReq) (*GetAdPosInfosResp, error)
}

func RegisterIAARevenueAdServiceServer(s *grpc.Server, srv IAARevenueAdServiceServer) {
	s.RegisterService(&_IAARevenueAdService_serviceDesc, srv)
}

func _IAARevenueAdService_GetAdPosInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAdPosInfosReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IAARevenueAdServiceServer).GetAdPosInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/com.quwan.dspiopother.proto.IAARevenueAdService/GetAdPosInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IAARevenueAdServiceServer).GetAdPosInfos(ctx, req.(*GetAdPosInfosReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _IAARevenueAdService_GetDspAdInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAdPosInfosReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IAARevenueAdServiceServer).GetDspAdInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/com.quwan.dspiopother.proto.IAARevenueAdService/GetDspAdInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IAARevenueAdServiceServer).GetDspAdInfos(ctx, req.(*GetAdPosInfosReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _IAARevenueAdService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "com.quwan.dspiopother.proto.IAARevenueAdService",
	HandlerType: (*IAARevenueAdServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAdPosInfos",
			Handler:    _IAARevenueAdService_GetAdPosInfos_Handler,
		},
		{
			MethodName: "GetDspAdInfos",
			Handler:    _IAARevenueAdService_GetDspAdInfos_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/iop-iaa-ad/iop-iaa-ad.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/iop-iaa-ad/iop-iaa-ad.proto", fileDescriptor_iop_iaa_ad_81b53111d89651ca)
}

var fileDescriptor_iop_iaa_ad_81b53111d89651ca = []byte{
	// 609 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x94, 0xcf, 0x6e, 0xd3, 0x40,
	0x10, 0xc6, 0xeb, 0xb4, 0x4d, 0x9c, 0x69, 0x02, 0xee, 0xf2, 0xcf, 0x6d, 0x41, 0x94, 0x1c, 0xaa,
	0xaa, 0xa2, 0x8e, 0x54, 0xc4, 0x03, 0x18, 0x0a, 0x34, 0x42, 0x2a, 0x91, 0x0b, 0x1c, 0xb8, 0x58,
	0x1b, 0xef, 0x34, 0xac, 0x6a, 0xef, 0x6e, 0xbc, 0xeb, 0x54, 0xbd, 0xf0, 0x18, 0x5c, 0x90, 0x38,
	0xf2, 0x8e, 0xdc, 0x90, 0x77, 0x93, 0xa8, 0xed, 0x21, 0x12, 0x1c, 0xb8, 0xad, 0xbf, 0x6f, 0x32,
	0xf3, 0x9b, 0xd9, 0xc9, 0xc2, 0x81, 0x31, 0xfd, 0x49, 0xc5, 0xb3, 0x0b, 0xcd, 0xf3, 0x29, 0x96,
	0x7d, 0x2e, 0xd5, 0x21, 0xa7, 0xf4, 0x90, 0xb2, 0x6b, 0xc7, 0x48, 0x95, 0xd2, 0x48, 0xb2, 0x93,
	0xc9, 0x22, 0x9a, 0x54, 0x97, 0x54, 0x44, 0x4c, 0x2b, 0x2e, 0x95, 0x34, 0x5f, 0xb1, 0x74, 0x66,
	0xef, 0x97, 0x07, 0xc1, 0x3b, 0x34, 0x31, 0x1b, 0x4a, 0x3d, 0x10, 0xe7, 0x52, 0x27, 0x38, 0x21,
	0x5b, 0xe0, 0x53, 0xa5, 0x52, 0x41, 0x0b, 0x0c, 0xbd, 0x5d, 0x6f, 0xbf, 0x9d, 0xb4, 0xa8, 0x52,
	0xa7, 0xb4, 0xc0, 0xb9, 0x65, 0xae, 0x14, 0x86, 0x8d, 0x85, 0xf5, 0xf1, 0x4a, 0x21, 0x09, 0x60,
	0xb5, 0xe2, 0x2c, 0x5c, 0xdd, 0xf5, 0xf6, 0xbb, 0x49, 0x7d, 0x24, 0x3b, 0xd0, 0x1e, 0x55, 0x82,
	0xe5, 0x98, 0x72, 0x16, 0xae, 0xd9, 0x68, 0xdf, 0x09, 0x03, 0x46, 0xee, 0x40, 0x43, 0xea, 0x70,
	0xdd, 0x46, 0x37, 0xa4, 0x26, 0x4f, 0x00, 0xb2, 0x9c, 0xa3, 0x30, 0xe9, 0x14, 0xcb, 0xb0, 0x69,
	0xa3, 0xdb, 0x4e, 0xf9, 0x8c, 0x65, 0xef, 0xbb, 0x07, 0xed, 0x05, 0x25, 0x79, 0x6c, 0x83, 0x53,
	0x25, 0x75, 0x9d, 0xda, 0x31, 0xfa, 0x59, 0xce, 0x6b, 0x9f, 0x91, 0x6d, 0x68, 0x53, 0x36, 0x37,
	0xe7, 0x94, 0xcc, 0x79, 0x0f, 0xa1, 0xa9, 0x68, 0x49, 0x0b, 0x6d, 0x41, 0x3b, 0xc9, 0xec, 0x6b,
	0xf6, 0x9b, 0xba, 0xb7, 0x05, 0x6b, 0x8b, 0xb2, 0x58, 0xa9, 0x81, 0xed, 0x83, 0x4b, 0x95, 0xda,
	0x48, 0x4b, 0xdc, 0x4e, 0x7c, 0x2e, 0xd5, 0xb0, 0xfe, 0xee, 0x7d, 0x83, 0xcd, 0x5b, 0x03, 0xd4,
	0x8a, 0x9c, 0x40, 0x67, 0x4e, 0x50, 0x6b, 0xa1, 0xb7, 0xbb, 0xba, 0xbf, 0x71, 0xb4, 0x17, 0x2d,
	0xb9, 0x8a, 0x68, 0x91, 0x22, 0x01, 0xba, 0xc8, 0x46, 0x9e, 0xc2, 0x06, 0x65, 0x22, 0xcd, 0xa4,
	0x38, 0xe7, 0x63, 0x3d, 0xeb, 0x06, 0x28, 0x13, 0xaf, 0x9d, 0xd2, 0xfb, 0xe9, 0xc1, 0x66, 0xcc,
	0xde, 0x22, 0xb2, 0xb3, 0x49, 0x45, 0x4b, 0xb4, 0x54, 0x35, 0xb2, 0x36, 0xb4, 0x34, 0x35, 0x83,
	0x9d, 0x4f, 0x37, 0xf1, 0xad, 0x30, 0x94, 0x9a, 0x3c, 0x82, 0x16, 0x0a, 0x8b, 0x67, 0xf3, 0x75,
	0x93, 0x26, 0x8a, 0xba, 0x22, 0x79, 0x06, 0x1d, 0x2e, 0x0c, 0x96, 0x53, 0x9a, 0xa7, 0xa2, 0x2a,
	0x66, 0x77, 0xb9, 0x31, 0xd7, 0x4e, 0xab, 0x82, 0x3c, 0x80, 0x26, 0x65, 0xd6, 0x5c, 0xb3, 0xe6,
	0x3a, 0x65, 0xb5, 0xbc, 0x05, 0xbe, 0xa1, 0x23, 0xb7, 0x32, 0x6e, 0x42, 0x2d, 0x43, 0x47, 0xf5,
	0xca, 0xf4, 0x7e, 0x78, 0xb0, 0xed, 0x00, 0x4f, 0x64, 0x81, 0x43, 0x3a, 0xc6, 0xe3, 0x2b, 0x41,
	0x0b, 0x9e, 0x39, 0xd2, 0x3d, 0xb8, 0x5b, 0x70, 0x91, 0x32, 0xa7, 0xd9, 0xcc, 0x8e, 0xb7, 0x5b,
	0x70, 0x31, 0x8b, 0xac, 0x2b, 0xdc, 0xe8, 0xa8, 0x71, 0xab, 0xa3, 0x7f, 0x06, 0x3f, 0x38, 0x01,
	0xff, 0x4d, 0xfa, 0x41, 0xdb, 0x0d, 0xbe, 0x0f, 0xc1, 0xfc, 0x9c, 0x7e, 0x12, 0x17, 0x42, 0x5e,
	0x8a, 0x60, 0xe5, 0x86, 0x1a, 0x0b, 0x56, 0x4a, 0xce, 0x02, 0x8f, 0x04, 0xd0, 0x59, 0xa8, 0x03,
	0xa9, 0x83, 0xc6, 0xd1, 0x6f, 0x0f, 0xee, 0x0d, 0xe2, 0x38, 0xc1, 0x29, 0x8a, 0x0a, 0x63, 0x76,
	0x86, 0xe5, 0x94, 0x67, 0x48, 0x14, 0x74, 0x6f, 0x2c, 0x08, 0x39, 0x5c, 0xba, 0x06, 0xb7, 0xff,
	0x8d, 0xdb, 0xd1, 0xdf, 0x84, 0x6b, 0xd5, 0x5b, 0x99, 0x55, 0x3c, 0xd6, 0x2a, 0x66, 0xff, 0xa7,
	0xe2, 0xab, 0xf7, 0xb0, 0xec, 0x95, 0xf9, 0xf2, 0x7c, 0x2c, 0x73, 0x2a, 0xc6, 0xd1, 0xcb, 0x23,
	0x63, 0xea, 0xdc, 0x7d, 0x2b, 0x67, 0x32, 0xef, 0x6b, 0x37, 0x24, 0x7d, 0xed, 0xd9, 0x1a, 0x35,
	0xad, 0xfb, 0xe2, 0x4f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x68, 0xc1, 0xbf, 0xe7, 0xe5, 0x04, 0x00,
	0x00,
}
