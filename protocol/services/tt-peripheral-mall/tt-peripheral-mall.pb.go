// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt-peripheral-mall/tt-peripheral-mall.proto

package tt_peripheral_mall // import "golang.52tt.com/protocol/services/tt-peripheral-mall"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"
import unified_pay_rmb "golang.52tt.com/protocol/services/unified-pay-rmb"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ProductSimple struct {
	ProductId            uint32   `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	HomepageUrl          string   `protobuf:"bytes,3,opt,name=homepage_url,json=homepageUrl,proto3" json:"homepage_url,omitempty"`
	Price                uint32   `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	IsStockOut           bool     `protobuf:"varint,5,opt,name=is_stock_out,json=isStockOut,proto3" json:"is_stock_out,omitempty"`
	OriginPrice          uint32   `protobuf:"varint,6,opt,name=origin_price,json=originPrice,proto3" json:"origin_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductSimple) Reset()         { *m = ProductSimple{} }
func (m *ProductSimple) String() string { return proto.CompactTextString(m) }
func (*ProductSimple) ProtoMessage()    {}
func (*ProductSimple) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{0}
}
func (m *ProductSimple) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductSimple.Unmarshal(m, b)
}
func (m *ProductSimple) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductSimple.Marshal(b, m, deterministic)
}
func (dst *ProductSimple) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductSimple.Merge(dst, src)
}
func (m *ProductSimple) XXX_Size() int {
	return xxx_messageInfo_ProductSimple.Size(m)
}
func (m *ProductSimple) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductSimple.DiscardUnknown(m)
}

var xxx_messageInfo_ProductSimple proto.InternalMessageInfo

func (m *ProductSimple) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *ProductSimple) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ProductSimple) GetHomepageUrl() string {
	if m != nil {
		return m.HomepageUrl
	}
	return ""
}

func (m *ProductSimple) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *ProductSimple) GetIsStockOut() bool {
	if m != nil {
		return m.IsStockOut
	}
	return false
}

func (m *ProductSimple) GetOriginPrice() uint32 {
	if m != nil {
		return m.OriginPrice
	}
	return 0
}

type GetProductSimpleInfoListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetProductSimpleInfoListReq) Reset()         { *m = GetProductSimpleInfoListReq{} }
func (m *GetProductSimpleInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetProductSimpleInfoListReq) ProtoMessage()    {}
func (*GetProductSimpleInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{1}
}
func (m *GetProductSimpleInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProductSimpleInfoListReq.Unmarshal(m, b)
}
func (m *GetProductSimpleInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProductSimpleInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetProductSimpleInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProductSimpleInfoListReq.Merge(dst, src)
}
func (m *GetProductSimpleInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetProductSimpleInfoListReq.Size(m)
}
func (m *GetProductSimpleInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProductSimpleInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetProductSimpleInfoListReq proto.InternalMessageInfo

func (m *GetProductSimpleInfoListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetProductSimpleInfoListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetProductSimpleInfoListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetProductSimpleInfoListResp struct {
	ProductList          []*ProductSimple `protobuf:"bytes,1,rep,name=product_list,json=productList,proto3" json:"product_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetProductSimpleInfoListResp) Reset()         { *m = GetProductSimpleInfoListResp{} }
func (m *GetProductSimpleInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetProductSimpleInfoListResp) ProtoMessage()    {}
func (*GetProductSimpleInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{2}
}
func (m *GetProductSimpleInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProductSimpleInfoListResp.Unmarshal(m, b)
}
func (m *GetProductSimpleInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProductSimpleInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetProductSimpleInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProductSimpleInfoListResp.Merge(dst, src)
}
func (m *GetProductSimpleInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetProductSimpleInfoListResp.Size(m)
}
func (m *GetProductSimpleInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProductSimpleInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetProductSimpleInfoListResp proto.InternalMessageInfo

func (m *GetProductSimpleInfoListResp) GetProductList() []*ProductSimple {
	if m != nil {
		return m.ProductList
	}
	return nil
}

type ProductDetail struct {
	ProductId            uint32   `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	HomepageUrl          string   `protobuf:"bytes,3,opt,name=homepage_url,json=homepageUrl,proto3" json:"homepage_url,omitempty"`
	DetailDesc           string   `protobuf:"bytes,4,opt,name=detail_desc,json=detailDesc,proto3" json:"detail_desc,omitempty"`
	Price                uint32   `protobuf:"varint,5,opt,name=price,proto3" json:"price,omitempty"`
	Stock                uint32   `protobuf:"varint,6,opt,name=stock,proto3" json:"stock,omitempty"`
	LimitPerOrder        uint32   `protobuf:"varint,7,opt,name=limit_per_order,json=limitPerOrder,proto3" json:"limit_per_order,omitempty"`
	PicUrls              []string `protobuf:"bytes,8,rep,name=pic_urls,json=picUrls,proto3" json:"pic_urls,omitempty"`
	Priority             uint32   `protobuf:"varint,9,opt,name=priority,proto3" json:"priority,omitempty"`
	OriginPrice          uint32   `protobuf:"varint,10,opt,name=origin_price,json=originPrice,proto3" json:"origin_price,omitempty"`
	UserOrderedCnt       uint32   `protobuf:"varint,11,opt,name=user_ordered_cnt,json=userOrderedCnt,proto3" json:"user_ordered_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductDetail) Reset()         { *m = ProductDetail{} }
func (m *ProductDetail) String() string { return proto.CompactTextString(m) }
func (*ProductDetail) ProtoMessage()    {}
func (*ProductDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{3}
}
func (m *ProductDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductDetail.Unmarshal(m, b)
}
func (m *ProductDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductDetail.Marshal(b, m, deterministic)
}
func (dst *ProductDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductDetail.Merge(dst, src)
}
func (m *ProductDetail) XXX_Size() int {
	return xxx_messageInfo_ProductDetail.Size(m)
}
func (m *ProductDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductDetail.DiscardUnknown(m)
}

var xxx_messageInfo_ProductDetail proto.InternalMessageInfo

func (m *ProductDetail) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *ProductDetail) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ProductDetail) GetHomepageUrl() string {
	if m != nil {
		return m.HomepageUrl
	}
	return ""
}

func (m *ProductDetail) GetDetailDesc() string {
	if m != nil {
		return m.DetailDesc
	}
	return ""
}

func (m *ProductDetail) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *ProductDetail) GetStock() uint32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *ProductDetail) GetLimitPerOrder() uint32 {
	if m != nil {
		return m.LimitPerOrder
	}
	return 0
}

func (m *ProductDetail) GetPicUrls() []string {
	if m != nil {
		return m.PicUrls
	}
	return nil
}

func (m *ProductDetail) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func (m *ProductDetail) GetOriginPrice() uint32 {
	if m != nil {
		return m.OriginPrice
	}
	return 0
}

func (m *ProductDetail) GetUserOrderedCnt() uint32 {
	if m != nil {
		return m.UserOrderedCnt
	}
	return 0
}

// 下单地址
type OrderAddrInfo struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Phone                string   `protobuf:"bytes,2,opt,name=phone,proto3" json:"phone,omitempty"`
	Address              string   `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderAddrInfo) Reset()         { *m = OrderAddrInfo{} }
func (m *OrderAddrInfo) String() string { return proto.CompactTextString(m) }
func (*OrderAddrInfo) ProtoMessage()    {}
func (*OrderAddrInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{4}
}
func (m *OrderAddrInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderAddrInfo.Unmarshal(m, b)
}
func (m *OrderAddrInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderAddrInfo.Marshal(b, m, deterministic)
}
func (dst *OrderAddrInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderAddrInfo.Merge(dst, src)
}
func (m *OrderAddrInfo) XXX_Size() int {
	return xxx_messageInfo_OrderAddrInfo.Size(m)
}
func (m *OrderAddrInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderAddrInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OrderAddrInfo proto.InternalMessageInfo

func (m *OrderAddrInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *OrderAddrInfo) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *OrderAddrInfo) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

type GetProductDetailInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ProductId            uint32   `protobuf:"varint,2,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetProductDetailInfoReq) Reset()         { *m = GetProductDetailInfoReq{} }
func (m *GetProductDetailInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetProductDetailInfoReq) ProtoMessage()    {}
func (*GetProductDetailInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{5}
}
func (m *GetProductDetailInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProductDetailInfoReq.Unmarshal(m, b)
}
func (m *GetProductDetailInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProductDetailInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetProductDetailInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProductDetailInfoReq.Merge(dst, src)
}
func (m *GetProductDetailInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetProductDetailInfoReq.Size(m)
}
func (m *GetProductDetailInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProductDetailInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetProductDetailInfoReq proto.InternalMessageInfo

func (m *GetProductDetailInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetProductDetailInfoReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

type GetProductDetailInfoResp struct {
	ProductDetail        *ProductDetail `protobuf:"bytes,1,opt,name=product_detail,json=productDetail,proto3" json:"product_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetProductDetailInfoResp) Reset()         { *m = GetProductDetailInfoResp{} }
func (m *GetProductDetailInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetProductDetailInfoResp) ProtoMessage()    {}
func (*GetProductDetailInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{6}
}
func (m *GetProductDetailInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProductDetailInfoResp.Unmarshal(m, b)
}
func (m *GetProductDetailInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProductDetailInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetProductDetailInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProductDetailInfoResp.Merge(dst, src)
}
func (m *GetProductDetailInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetProductDetailInfoResp.Size(m)
}
func (m *GetProductDetailInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProductDetailInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetProductDetailInfoResp proto.InternalMessageInfo

func (m *GetProductDetailInfoResp) GetProductDetail() *ProductDetail {
	if m != nil {
		return m.ProductDetail
	}
	return nil
}

type SingleProductOrder struct {
	ProductId            uint32   `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	Amount               uint32   `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingleProductOrder) Reset()         { *m = SingleProductOrder{} }
func (m *SingleProductOrder) String() string { return proto.CompactTextString(m) }
func (*SingleProductOrder) ProtoMessage()    {}
func (*SingleProductOrder) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{7}
}
func (m *SingleProductOrder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleProductOrder.Unmarshal(m, b)
}
func (m *SingleProductOrder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleProductOrder.Marshal(b, m, deterministic)
}
func (dst *SingleProductOrder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleProductOrder.Merge(dst, src)
}
func (m *SingleProductOrder) XXX_Size() int {
	return xxx_messageInfo_SingleProductOrder.Size(m)
}
func (m *SingleProductOrder) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleProductOrder.DiscardUnknown(m)
}

var xxx_messageInfo_SingleProductOrder proto.InternalMessageInfo

func (m *SingleProductOrder) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *SingleProductOrder) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

type PeripheralPayReq struct {
	PayChannel           string   `protobuf:"bytes,1,opt,name=pay_channel,json=payChannel,proto3" json:"pay_channel,omitempty"`
	BundleId             string   `protobuf:"bytes,2,opt,name=bundle_id,json=bundleId,proto3" json:"bundle_id,omitempty"`
	PayProductId         string   `protobuf:"bytes,3,opt,name=pay_product_id,json=payProductId,proto3" json:"pay_product_id,omitempty"`
	MarketId             uint32   `protobuf:"varint,4,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	OsType               string   `protobuf:"bytes,5,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`
	Version              string   `protobuf:"bytes,6,opt,name=version,proto3" json:"version,omitempty"`
	UserIp               string   `protobuf:"bytes,7,opt,name=user_ip,json=userIp,proto3" json:"user_ip,omitempty"`
	DeviceId             string   `protobuf:"bytes,8,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PeripheralPayReq) Reset()         { *m = PeripheralPayReq{} }
func (m *PeripheralPayReq) String() string { return proto.CompactTextString(m) }
func (*PeripheralPayReq) ProtoMessage()    {}
func (*PeripheralPayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{8}
}
func (m *PeripheralPayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PeripheralPayReq.Unmarshal(m, b)
}
func (m *PeripheralPayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PeripheralPayReq.Marshal(b, m, deterministic)
}
func (dst *PeripheralPayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PeripheralPayReq.Merge(dst, src)
}
func (m *PeripheralPayReq) XXX_Size() int {
	return xxx_messageInfo_PeripheralPayReq.Size(m)
}
func (m *PeripheralPayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PeripheralPayReq.DiscardUnknown(m)
}

var xxx_messageInfo_PeripheralPayReq proto.InternalMessageInfo

func (m *PeripheralPayReq) GetPayChannel() string {
	if m != nil {
		return m.PayChannel
	}
	return ""
}

func (m *PeripheralPayReq) GetBundleId() string {
	if m != nil {
		return m.BundleId
	}
	return ""
}

func (m *PeripheralPayReq) GetPayProductId() string {
	if m != nil {
		return m.PayProductId
	}
	return ""
}

func (m *PeripheralPayReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *PeripheralPayReq) GetOsType() string {
	if m != nil {
		return m.OsType
	}
	return ""
}

func (m *PeripheralPayReq) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *PeripheralPayReq) GetUserIp() string {
	if m != nil {
		return m.UserIp
	}
	return ""
}

func (m *PeripheralPayReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type PeripheralPayResp struct {
	Token                string   `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	PayOrderId           string   `protobuf:"bytes,2,opt,name=pay_order_id,json=payOrderId,proto3" json:"pay_order_id,omitempty"`
	CliOrderTitle        string   `protobuf:"bytes,3,opt,name=cli_order_title,json=cliOrderTitle,proto3" json:"cli_order_title,omitempty"`
	Tsk                  string   `protobuf:"bytes,4,opt,name=tsk,proto3" json:"tsk,omitempty"`
	ChannelMap           string   `protobuf:"bytes,5,opt,name=channel_map,json=channelMap,proto3" json:"channel_map,omitempty"`
	OrderTime            uint32   `protobuf:"varint,6,opt,name=order_time,json=orderTime,proto3" json:"order_time,omitempty"`
	TPayOrderNo          string   `protobuf:"bytes,7,opt,name=t_pay_order_no,json=tPayOrderNo,proto3" json:"t_pay_order_no,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PeripheralPayResp) Reset()         { *m = PeripheralPayResp{} }
func (m *PeripheralPayResp) String() string { return proto.CompactTextString(m) }
func (*PeripheralPayResp) ProtoMessage()    {}
func (*PeripheralPayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{9}
}
func (m *PeripheralPayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PeripheralPayResp.Unmarshal(m, b)
}
func (m *PeripheralPayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PeripheralPayResp.Marshal(b, m, deterministic)
}
func (dst *PeripheralPayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PeripheralPayResp.Merge(dst, src)
}
func (m *PeripheralPayResp) XXX_Size() int {
	return xxx_messageInfo_PeripheralPayResp.Size(m)
}
func (m *PeripheralPayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PeripheralPayResp.DiscardUnknown(m)
}

var xxx_messageInfo_PeripheralPayResp proto.InternalMessageInfo

func (m *PeripheralPayResp) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *PeripheralPayResp) GetPayOrderId() string {
	if m != nil {
		return m.PayOrderId
	}
	return ""
}

func (m *PeripheralPayResp) GetCliOrderTitle() string {
	if m != nil {
		return m.CliOrderTitle
	}
	return ""
}

func (m *PeripheralPayResp) GetTsk() string {
	if m != nil {
		return m.Tsk
	}
	return ""
}

func (m *PeripheralPayResp) GetChannelMap() string {
	if m != nil {
		return m.ChannelMap
	}
	return ""
}

func (m *PeripheralPayResp) GetOrderTime() uint32 {
	if m != nil {
		return m.OrderTime
	}
	return 0
}

func (m *PeripheralPayResp) GetTPayOrderNo() string {
	if m != nil {
		return m.TPayOrderNo
	}
	return ""
}

type OrderProductReq struct {
	Uid                  uint32                `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	List                 []*SingleProductOrder `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	Addr                 *OrderAddrInfo        `protobuf:"bytes,3,opt,name=addr,proto3" json:"addr,omitempty"`
	PayInfo              *PeripheralPayReq     `protobuf:"bytes,4,opt,name=pay_info,json=payInfo,proto3" json:"pay_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *OrderProductReq) Reset()         { *m = OrderProductReq{} }
func (m *OrderProductReq) String() string { return proto.CompactTextString(m) }
func (*OrderProductReq) ProtoMessage()    {}
func (*OrderProductReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{10}
}
func (m *OrderProductReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderProductReq.Unmarshal(m, b)
}
func (m *OrderProductReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderProductReq.Marshal(b, m, deterministic)
}
func (dst *OrderProductReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderProductReq.Merge(dst, src)
}
func (m *OrderProductReq) XXX_Size() int {
	return xxx_messageInfo_OrderProductReq.Size(m)
}
func (m *OrderProductReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderProductReq.DiscardUnknown(m)
}

var xxx_messageInfo_OrderProductReq proto.InternalMessageInfo

func (m *OrderProductReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *OrderProductReq) GetList() []*SingleProductOrder {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *OrderProductReq) GetAddr() *OrderAddrInfo {
	if m != nil {
		return m.Addr
	}
	return nil
}

func (m *OrderProductReq) GetPayInfo() *PeripheralPayReq {
	if m != nil {
		return m.PayInfo
	}
	return nil
}

type OrderProductResp struct {
	PayInfo              *PeripheralPayResp `protobuf:"bytes,1,opt,name=pay_info,json=payInfo,proto3" json:"pay_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *OrderProductResp) Reset()         { *m = OrderProductResp{} }
func (m *OrderProductResp) String() string { return proto.CompactTextString(m) }
func (*OrderProductResp) ProtoMessage()    {}
func (*OrderProductResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{11}
}
func (m *OrderProductResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderProductResp.Unmarshal(m, b)
}
func (m *OrderProductResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderProductResp.Marshal(b, m, deterministic)
}
func (dst *OrderProductResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderProductResp.Merge(dst, src)
}
func (m *OrderProductResp) XXX_Size() int {
	return xxx_messageInfo_OrderProductResp.Size(m)
}
func (m *OrderProductResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderProductResp.DiscardUnknown(m)
}

var xxx_messageInfo_OrderProductResp proto.InternalMessageInfo

func (m *OrderProductResp) GetPayInfo() *PeripheralPayResp {
	if m != nil {
		return m.PayInfo
	}
	return nil
}

type OrderDetail struct {
	OrderId              string           `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ProductList          []*ProductDetail `protobuf:"bytes,2,rep,name=product_list,json=productList,proto3" json:"product_list,omitempty"`
	Addr                 *OrderAddrInfo   `protobuf:"bytes,3,opt,name=addr,proto3" json:"addr,omitempty"`
	TotalPrice           uint32           `protobuf:"varint,4,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	Timestamp            int64            `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *OrderDetail) Reset()         { *m = OrderDetail{} }
func (m *OrderDetail) String() string { return proto.CompactTextString(m) }
func (*OrderDetail) ProtoMessage()    {}
func (*OrderDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{12}
}
func (m *OrderDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderDetail.Unmarshal(m, b)
}
func (m *OrderDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderDetail.Marshal(b, m, deterministic)
}
func (dst *OrderDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderDetail.Merge(dst, src)
}
func (m *OrderDetail) XXX_Size() int {
	return xxx_messageInfo_OrderDetail.Size(m)
}
func (m *OrderDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderDetail.DiscardUnknown(m)
}

var xxx_messageInfo_OrderDetail proto.InternalMessageInfo

func (m *OrderDetail) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *OrderDetail) GetProductList() []*ProductDetail {
	if m != nil {
		return m.ProductList
	}
	return nil
}

func (m *OrderDetail) GetAddr() *OrderAddrInfo {
	if m != nil {
		return m.Addr
	}
	return nil
}

func (m *OrderDetail) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *OrderDetail) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

type GetUserOrderListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserOrderListReq) Reset()         { *m = GetUserOrderListReq{} }
func (m *GetUserOrderListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserOrderListReq) ProtoMessage()    {}
func (*GetUserOrderListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{13}
}
func (m *GetUserOrderListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOrderListReq.Unmarshal(m, b)
}
func (m *GetUserOrderListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOrderListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserOrderListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOrderListReq.Merge(dst, src)
}
func (m *GetUserOrderListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserOrderListReq.Size(m)
}
func (m *GetUserOrderListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOrderListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOrderListReq proto.InternalMessageInfo

func (m *GetUserOrderListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserOrderListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetUserOrderListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetUserOrderListResp struct {
	OrderList            []*OrderDetail `protobuf:"bytes,1,rep,name=order_list,json=orderList,proto3" json:"order_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetUserOrderListResp) Reset()         { *m = GetUserOrderListResp{} }
func (m *GetUserOrderListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserOrderListResp) ProtoMessage()    {}
func (*GetUserOrderListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{14}
}
func (m *GetUserOrderListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOrderListResp.Unmarshal(m, b)
}
func (m *GetUserOrderListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOrderListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserOrderListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOrderListResp.Merge(dst, src)
}
func (m *GetUserOrderListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserOrderListResp.Size(m)
}
func (m *GetUserOrderListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOrderListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOrderListResp proto.InternalMessageInfo

func (m *GetUserOrderListResp) GetOrderList() []*OrderDetail {
	if m != nil {
		return m.OrderList
	}
	return nil
}

// 支付结果回调
type PayRmbResultNotifyReq struct {
	OrderResult          *unified_pay_rmb.RmbPayOrderInfo `protobuf:"bytes,1,opt,name=order_result,json=orderResult,proto3" json:"order_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *PayRmbResultNotifyReq) Reset()         { *m = PayRmbResultNotifyReq{} }
func (m *PayRmbResultNotifyReq) String() string { return proto.CompactTextString(m) }
func (*PayRmbResultNotifyReq) ProtoMessage()    {}
func (*PayRmbResultNotifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{15}
}
func (m *PayRmbResultNotifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayRmbResultNotifyReq.Unmarshal(m, b)
}
func (m *PayRmbResultNotifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayRmbResultNotifyReq.Marshal(b, m, deterministic)
}
func (dst *PayRmbResultNotifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayRmbResultNotifyReq.Merge(dst, src)
}
func (m *PayRmbResultNotifyReq) XXX_Size() int {
	return xxx_messageInfo_PayRmbResultNotifyReq.Size(m)
}
func (m *PayRmbResultNotifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PayRmbResultNotifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_PayRmbResultNotifyReq proto.InternalMessageInfo

func (m *PayRmbResultNotifyReq) GetOrderResult() *unified_pay_rmb.RmbPayOrderInfo {
	if m != nil {
		return m.OrderResult
	}
	return nil
}

type PayRmbResultNotifyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayRmbResultNotifyResp) Reset()         { *m = PayRmbResultNotifyResp{} }
func (m *PayRmbResultNotifyResp) String() string { return proto.CompactTextString(m) }
func (*PayRmbResultNotifyResp) ProtoMessage()    {}
func (*PayRmbResultNotifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{16}
}
func (m *PayRmbResultNotifyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayRmbResultNotifyResp.Unmarshal(m, b)
}
func (m *PayRmbResultNotifyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayRmbResultNotifyResp.Marshal(b, m, deterministic)
}
func (dst *PayRmbResultNotifyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayRmbResultNotifyResp.Merge(dst, src)
}
func (m *PayRmbResultNotifyResp) XXX_Size() int {
	return xxx_messageInfo_PayRmbResultNotifyResp.Size(m)
}
func (m *PayRmbResultNotifyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PayRmbResultNotifyResp.DiscardUnknown(m)
}

var xxx_messageInfo_PayRmbResultNotifyResp proto.InternalMessageInfo

type BatAddProductReq struct {
	OpUser               uint32           `protobuf:"varint,1,opt,name=op_user,json=opUser,proto3" json:"op_user,omitempty"`
	ProductList          []*ProductDetail `protobuf:"bytes,2,rep,name=product_list,json=productList,proto3" json:"product_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatAddProductReq) Reset()         { *m = BatAddProductReq{} }
func (m *BatAddProductReq) String() string { return proto.CompactTextString(m) }
func (*BatAddProductReq) ProtoMessage()    {}
func (*BatAddProductReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{17}
}
func (m *BatAddProductReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatAddProductReq.Unmarshal(m, b)
}
func (m *BatAddProductReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatAddProductReq.Marshal(b, m, deterministic)
}
func (dst *BatAddProductReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatAddProductReq.Merge(dst, src)
}
func (m *BatAddProductReq) XXX_Size() int {
	return xxx_messageInfo_BatAddProductReq.Size(m)
}
func (m *BatAddProductReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatAddProductReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatAddProductReq proto.InternalMessageInfo

func (m *BatAddProductReq) GetOpUser() uint32 {
	if m != nil {
		return m.OpUser
	}
	return 0
}

func (m *BatAddProductReq) GetProductList() []*ProductDetail {
	if m != nil {
		return m.ProductList
	}
	return nil
}

type BatAddProductResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatAddProductResp) Reset()         { *m = BatAddProductResp{} }
func (m *BatAddProductResp) String() string { return proto.CompactTextString(m) }
func (*BatAddProductResp) ProtoMessage()    {}
func (*BatAddProductResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{18}
}
func (m *BatAddProductResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatAddProductResp.Unmarshal(m, b)
}
func (m *BatAddProductResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatAddProductResp.Marshal(b, m, deterministic)
}
func (dst *BatAddProductResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatAddProductResp.Merge(dst, src)
}
func (m *BatAddProductResp) XXX_Size() int {
	return xxx_messageInfo_BatAddProductResp.Size(m)
}
func (m *BatAddProductResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatAddProductResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatAddProductResp proto.InternalMessageInfo

type UpdateProductReq struct {
	OpUser               uint32         `protobuf:"varint,1,opt,name=op_user,json=opUser,proto3" json:"op_user,omitempty"`
	Info                 *ProductDetail `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UpdateProductReq) Reset()         { *m = UpdateProductReq{} }
func (m *UpdateProductReq) String() string { return proto.CompactTextString(m) }
func (*UpdateProductReq) ProtoMessage()    {}
func (*UpdateProductReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{19}
}
func (m *UpdateProductReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateProductReq.Unmarshal(m, b)
}
func (m *UpdateProductReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateProductReq.Marshal(b, m, deterministic)
}
func (dst *UpdateProductReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateProductReq.Merge(dst, src)
}
func (m *UpdateProductReq) XXX_Size() int {
	return xxx_messageInfo_UpdateProductReq.Size(m)
}
func (m *UpdateProductReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateProductReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateProductReq proto.InternalMessageInfo

func (m *UpdateProductReq) GetOpUser() uint32 {
	if m != nil {
		return m.OpUser
	}
	return 0
}

func (m *UpdateProductReq) GetInfo() *ProductDetail {
	if m != nil {
		return m.Info
	}
	return nil
}

type UpdateProductResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateProductResp) Reset()         { *m = UpdateProductResp{} }
func (m *UpdateProductResp) String() string { return proto.CompactTextString(m) }
func (*UpdateProductResp) ProtoMessage()    {}
func (*UpdateProductResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{20}
}
func (m *UpdateProductResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateProductResp.Unmarshal(m, b)
}
func (m *UpdateProductResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateProductResp.Marshal(b, m, deterministic)
}
func (dst *UpdateProductResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateProductResp.Merge(dst, src)
}
func (m *UpdateProductResp) XXX_Size() int {
	return xxx_messageInfo_UpdateProductResp.Size(m)
}
func (m *UpdateProductResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateProductResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateProductResp proto.InternalMessageInfo

type DeleteProductReq struct {
	OpUser               uint32   `protobuf:"varint,1,opt,name=op_user,json=opUser,proto3" json:"op_user,omitempty"`
	ProductId            uint32   `protobuf:"varint,2,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteProductReq) Reset()         { *m = DeleteProductReq{} }
func (m *DeleteProductReq) String() string { return proto.CompactTextString(m) }
func (*DeleteProductReq) ProtoMessage()    {}
func (*DeleteProductReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{21}
}
func (m *DeleteProductReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteProductReq.Unmarshal(m, b)
}
func (m *DeleteProductReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteProductReq.Marshal(b, m, deterministic)
}
func (dst *DeleteProductReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteProductReq.Merge(dst, src)
}
func (m *DeleteProductReq) XXX_Size() int {
	return xxx_messageInfo_DeleteProductReq.Size(m)
}
func (m *DeleteProductReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteProductReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteProductReq proto.InternalMessageInfo

func (m *DeleteProductReq) GetOpUser() uint32 {
	if m != nil {
		return m.OpUser
	}
	return 0
}

func (m *DeleteProductReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

type DeleteProductResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteProductResp) Reset()         { *m = DeleteProductResp{} }
func (m *DeleteProductResp) String() string { return proto.CompactTextString(m) }
func (*DeleteProductResp) ProtoMessage()    {}
func (*DeleteProductResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{22}
}
func (m *DeleteProductResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteProductResp.Unmarshal(m, b)
}
func (m *DeleteProductResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteProductResp.Marshal(b, m, deterministic)
}
func (dst *DeleteProductResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteProductResp.Merge(dst, src)
}
func (m *DeleteProductResp) XXX_Size() int {
	return xxx_messageInfo_DeleteProductResp.Size(m)
}
func (m *DeleteProductResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteProductResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteProductResp proto.InternalMessageInfo

// 获取用户下单地址
type GetUserOrderAddrReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserOrderAddrReq) Reset()         { *m = GetUserOrderAddrReq{} }
func (m *GetUserOrderAddrReq) String() string { return proto.CompactTextString(m) }
func (*GetUserOrderAddrReq) ProtoMessage()    {}
func (*GetUserOrderAddrReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{23}
}
func (m *GetUserOrderAddrReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOrderAddrReq.Unmarshal(m, b)
}
func (m *GetUserOrderAddrReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOrderAddrReq.Marshal(b, m, deterministic)
}
func (dst *GetUserOrderAddrReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOrderAddrReq.Merge(dst, src)
}
func (m *GetUserOrderAddrReq) XXX_Size() int {
	return xxx_messageInfo_GetUserOrderAddrReq.Size(m)
}
func (m *GetUserOrderAddrReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOrderAddrReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOrderAddrReq proto.InternalMessageInfo

func (m *GetUserOrderAddrReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserOrderAddrResp struct {
	Addr                 *OrderAddrInfo `protobuf:"bytes,1,opt,name=addr,proto3" json:"addr,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetUserOrderAddrResp) Reset()         { *m = GetUserOrderAddrResp{} }
func (m *GetUserOrderAddrResp) String() string { return proto.CompactTextString(m) }
func (*GetUserOrderAddrResp) ProtoMessage()    {}
func (*GetUserOrderAddrResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{24}
}
func (m *GetUserOrderAddrResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOrderAddrResp.Unmarshal(m, b)
}
func (m *GetUserOrderAddrResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOrderAddrResp.Marshal(b, m, deterministic)
}
func (dst *GetUserOrderAddrResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOrderAddrResp.Merge(dst, src)
}
func (m *GetUserOrderAddrResp) XXX_Size() int {
	return xxx_messageInfo_GetUserOrderAddrResp.Size(m)
}
func (m *GetUserOrderAddrResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOrderAddrResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOrderAddrResp proto.InternalMessageInfo

func (m *GetUserOrderAddrResp) GetAddr() *OrderAddrInfo {
	if m != nil {
		return m.Addr
	}
	return nil
}

// 保存用户下单地址
type SetUserOrderAddrReq struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Addr                 *OrderAddrInfo `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SetUserOrderAddrReq) Reset()         { *m = SetUserOrderAddrReq{} }
func (m *SetUserOrderAddrReq) String() string { return proto.CompactTextString(m) }
func (*SetUserOrderAddrReq) ProtoMessage()    {}
func (*SetUserOrderAddrReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{25}
}
func (m *SetUserOrderAddrReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserOrderAddrReq.Unmarshal(m, b)
}
func (m *SetUserOrderAddrReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserOrderAddrReq.Marshal(b, m, deterministic)
}
func (dst *SetUserOrderAddrReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserOrderAddrReq.Merge(dst, src)
}
func (m *SetUserOrderAddrReq) XXX_Size() int {
	return xxx_messageInfo_SetUserOrderAddrReq.Size(m)
}
func (m *SetUserOrderAddrReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserOrderAddrReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserOrderAddrReq proto.InternalMessageInfo

func (m *SetUserOrderAddrReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserOrderAddrReq) GetAddr() *OrderAddrInfo {
	if m != nil {
		return m.Addr
	}
	return nil
}

type SetUserOrderAddrResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserOrderAddrResp) Reset()         { *m = SetUserOrderAddrResp{} }
func (m *SetUserOrderAddrResp) String() string { return proto.CompactTextString(m) }
func (*SetUserOrderAddrResp) ProtoMessage()    {}
func (*SetUserOrderAddrResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8, []int{26}
}
func (m *SetUserOrderAddrResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserOrderAddrResp.Unmarshal(m, b)
}
func (m *SetUserOrderAddrResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserOrderAddrResp.Marshal(b, m, deterministic)
}
func (dst *SetUserOrderAddrResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserOrderAddrResp.Merge(dst, src)
}
func (m *SetUserOrderAddrResp) XXX_Size() int {
	return xxx_messageInfo_SetUserOrderAddrResp.Size(m)
}
func (m *SetUserOrderAddrResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserOrderAddrResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserOrderAddrResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*ProductSimple)(nil), "tt_peripheral_mall.ProductSimple")
	proto.RegisterType((*GetProductSimpleInfoListReq)(nil), "tt_peripheral_mall.GetProductSimpleInfoListReq")
	proto.RegisterType((*GetProductSimpleInfoListResp)(nil), "tt_peripheral_mall.GetProductSimpleInfoListResp")
	proto.RegisterType((*ProductDetail)(nil), "tt_peripheral_mall.ProductDetail")
	proto.RegisterType((*OrderAddrInfo)(nil), "tt_peripheral_mall.OrderAddrInfo")
	proto.RegisterType((*GetProductDetailInfoReq)(nil), "tt_peripheral_mall.GetProductDetailInfoReq")
	proto.RegisterType((*GetProductDetailInfoResp)(nil), "tt_peripheral_mall.GetProductDetailInfoResp")
	proto.RegisterType((*SingleProductOrder)(nil), "tt_peripheral_mall.SingleProductOrder")
	proto.RegisterType((*PeripheralPayReq)(nil), "tt_peripheral_mall.PeripheralPayReq")
	proto.RegisterType((*PeripheralPayResp)(nil), "tt_peripheral_mall.PeripheralPayResp")
	proto.RegisterType((*OrderProductReq)(nil), "tt_peripheral_mall.OrderProductReq")
	proto.RegisterType((*OrderProductResp)(nil), "tt_peripheral_mall.OrderProductResp")
	proto.RegisterType((*OrderDetail)(nil), "tt_peripheral_mall.OrderDetail")
	proto.RegisterType((*GetUserOrderListReq)(nil), "tt_peripheral_mall.GetUserOrderListReq")
	proto.RegisterType((*GetUserOrderListResp)(nil), "tt_peripheral_mall.GetUserOrderListResp")
	proto.RegisterType((*PayRmbResultNotifyReq)(nil), "tt_peripheral_mall.PayRmbResultNotifyReq")
	proto.RegisterType((*PayRmbResultNotifyResp)(nil), "tt_peripheral_mall.PayRmbResultNotifyResp")
	proto.RegisterType((*BatAddProductReq)(nil), "tt_peripheral_mall.BatAddProductReq")
	proto.RegisterType((*BatAddProductResp)(nil), "tt_peripheral_mall.BatAddProductResp")
	proto.RegisterType((*UpdateProductReq)(nil), "tt_peripheral_mall.UpdateProductReq")
	proto.RegisterType((*UpdateProductResp)(nil), "tt_peripheral_mall.UpdateProductResp")
	proto.RegisterType((*DeleteProductReq)(nil), "tt_peripheral_mall.DeleteProductReq")
	proto.RegisterType((*DeleteProductResp)(nil), "tt_peripheral_mall.DeleteProductResp")
	proto.RegisterType((*GetUserOrderAddrReq)(nil), "tt_peripheral_mall.GetUserOrderAddrReq")
	proto.RegisterType((*GetUserOrderAddrResp)(nil), "tt_peripheral_mall.GetUserOrderAddrResp")
	proto.RegisterType((*SetUserOrderAddrReq)(nil), "tt_peripheral_mall.SetUserOrderAddrReq")
	proto.RegisterType((*SetUserOrderAddrResp)(nil), "tt_peripheral_mall.SetUserOrderAddrResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// TTPeripheralMallClient is the client API for TTPeripheralMall service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TTPeripheralMallClient interface {
	// 获取商品简略信息列表
	GetProductSimpleInfoList(ctx context.Context, in *GetProductSimpleInfoListReq, opts ...grpc.CallOption) (*GetProductSimpleInfoListResp, error)
	// 获取商品详细信息
	GetProductDetailInfo(ctx context.Context, in *GetProductDetailInfoReq, opts ...grpc.CallOption) (*GetProductDetailInfoResp, error)
	// 下单
	OrderProduct(ctx context.Context, in *OrderProductReq, opts ...grpc.CallOption) (*OrderProductResp, error)
	// 获取用户历史订单列表
	GetUserOrderList(ctx context.Context, in *GetUserOrderListReq, opts ...grpc.CallOption) (*GetUserOrderListResp, error)
	// 支付结果回调
	PayRmbResultNotify(ctx context.Context, in *PayRmbResultNotifyReq, opts ...grpc.CallOption) (*PayRmbResultNotifyResp, error)
	// 获取用户订单地址
	GetUserOrderAddr(ctx context.Context, in *GetUserOrderAddrReq, opts ...grpc.CallOption) (*GetUserOrderAddrResp, error)
	// 设置用户订单地址
	SetUserOrderAddr(ctx context.Context, in *SetUserOrderAddrReq, opts ...grpc.CallOption) (*SetUserOrderAddrResp, error)
	// 批量新增商品
	BatAddProduct(ctx context.Context, in *BatAddProductReq, opts ...grpc.CallOption) (*BatAddProductResp, error)
	// 更新商品信息
	UpdateProduct(ctx context.Context, in *UpdateProductReq, opts ...grpc.CallOption) (*UpdateProductResp, error)
	// 删除商品
	DeleteProduct(ctx context.Context, in *DeleteProductReq, opts ...grpc.CallOption) (*DeleteProductResp, error)
}

type tTPeripheralMallClient struct {
	cc *grpc.ClientConn
}

func NewTTPeripheralMallClient(cc *grpc.ClientConn) TTPeripheralMallClient {
	return &tTPeripheralMallClient{cc}
}

func (c *tTPeripheralMallClient) GetProductSimpleInfoList(ctx context.Context, in *GetProductSimpleInfoListReq, opts ...grpc.CallOption) (*GetProductSimpleInfoListResp, error) {
	out := new(GetProductSimpleInfoListResp)
	err := c.cc.Invoke(ctx, "/tt_peripheral_mall.TTPeripheralMall/GetProductSimpleInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTPeripheralMallClient) GetProductDetailInfo(ctx context.Context, in *GetProductDetailInfoReq, opts ...grpc.CallOption) (*GetProductDetailInfoResp, error) {
	out := new(GetProductDetailInfoResp)
	err := c.cc.Invoke(ctx, "/tt_peripheral_mall.TTPeripheralMall/GetProductDetailInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTPeripheralMallClient) OrderProduct(ctx context.Context, in *OrderProductReq, opts ...grpc.CallOption) (*OrderProductResp, error) {
	out := new(OrderProductResp)
	err := c.cc.Invoke(ctx, "/tt_peripheral_mall.TTPeripheralMall/OrderProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTPeripheralMallClient) GetUserOrderList(ctx context.Context, in *GetUserOrderListReq, opts ...grpc.CallOption) (*GetUserOrderListResp, error) {
	out := new(GetUserOrderListResp)
	err := c.cc.Invoke(ctx, "/tt_peripheral_mall.TTPeripheralMall/GetUserOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTPeripheralMallClient) PayRmbResultNotify(ctx context.Context, in *PayRmbResultNotifyReq, opts ...grpc.CallOption) (*PayRmbResultNotifyResp, error) {
	out := new(PayRmbResultNotifyResp)
	err := c.cc.Invoke(ctx, "/tt_peripheral_mall.TTPeripheralMall/PayRmbResultNotify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTPeripheralMallClient) GetUserOrderAddr(ctx context.Context, in *GetUserOrderAddrReq, opts ...grpc.CallOption) (*GetUserOrderAddrResp, error) {
	out := new(GetUserOrderAddrResp)
	err := c.cc.Invoke(ctx, "/tt_peripheral_mall.TTPeripheralMall/GetUserOrderAddr", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTPeripheralMallClient) SetUserOrderAddr(ctx context.Context, in *SetUserOrderAddrReq, opts ...grpc.CallOption) (*SetUserOrderAddrResp, error) {
	out := new(SetUserOrderAddrResp)
	err := c.cc.Invoke(ctx, "/tt_peripheral_mall.TTPeripheralMall/SetUserOrderAddr", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTPeripheralMallClient) BatAddProduct(ctx context.Context, in *BatAddProductReq, opts ...grpc.CallOption) (*BatAddProductResp, error) {
	out := new(BatAddProductResp)
	err := c.cc.Invoke(ctx, "/tt_peripheral_mall.TTPeripheralMall/BatAddProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTPeripheralMallClient) UpdateProduct(ctx context.Context, in *UpdateProductReq, opts ...grpc.CallOption) (*UpdateProductResp, error) {
	out := new(UpdateProductResp)
	err := c.cc.Invoke(ctx, "/tt_peripheral_mall.TTPeripheralMall/UpdateProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTPeripheralMallClient) DeleteProduct(ctx context.Context, in *DeleteProductReq, opts ...grpc.CallOption) (*DeleteProductResp, error) {
	out := new(DeleteProductResp)
	err := c.cc.Invoke(ctx, "/tt_peripheral_mall.TTPeripheralMall/DeleteProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TTPeripheralMallServer is the server API for TTPeripheralMall service.
type TTPeripheralMallServer interface {
	// 获取商品简略信息列表
	GetProductSimpleInfoList(context.Context, *GetProductSimpleInfoListReq) (*GetProductSimpleInfoListResp, error)
	// 获取商品详细信息
	GetProductDetailInfo(context.Context, *GetProductDetailInfoReq) (*GetProductDetailInfoResp, error)
	// 下单
	OrderProduct(context.Context, *OrderProductReq) (*OrderProductResp, error)
	// 获取用户历史订单列表
	GetUserOrderList(context.Context, *GetUserOrderListReq) (*GetUserOrderListResp, error)
	// 支付结果回调
	PayRmbResultNotify(context.Context, *PayRmbResultNotifyReq) (*PayRmbResultNotifyResp, error)
	// 获取用户订单地址
	GetUserOrderAddr(context.Context, *GetUserOrderAddrReq) (*GetUserOrderAddrResp, error)
	// 设置用户订单地址
	SetUserOrderAddr(context.Context, *SetUserOrderAddrReq) (*SetUserOrderAddrResp, error)
	// 批量新增商品
	BatAddProduct(context.Context, *BatAddProductReq) (*BatAddProductResp, error)
	// 更新商品信息
	UpdateProduct(context.Context, *UpdateProductReq) (*UpdateProductResp, error)
	// 删除商品
	DeleteProduct(context.Context, *DeleteProductReq) (*DeleteProductResp, error)
}

func RegisterTTPeripheralMallServer(s *grpc.Server, srv TTPeripheralMallServer) {
	s.RegisterService(&_TTPeripheralMall_serviceDesc, srv)
}

func _TTPeripheralMall_GetProductSimpleInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProductSimpleInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTPeripheralMallServer).GetProductSimpleInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_peripheral_mall.TTPeripheralMall/GetProductSimpleInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTPeripheralMallServer).GetProductSimpleInfoList(ctx, req.(*GetProductSimpleInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTPeripheralMall_GetProductDetailInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProductDetailInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTPeripheralMallServer).GetProductDetailInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_peripheral_mall.TTPeripheralMall/GetProductDetailInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTPeripheralMallServer).GetProductDetailInfo(ctx, req.(*GetProductDetailInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTPeripheralMall_OrderProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTPeripheralMallServer).OrderProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_peripheral_mall.TTPeripheralMall/OrderProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTPeripheralMallServer).OrderProduct(ctx, req.(*OrderProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTPeripheralMall_GetUserOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserOrderListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTPeripheralMallServer).GetUserOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_peripheral_mall.TTPeripheralMall/GetUserOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTPeripheralMallServer).GetUserOrderList(ctx, req.(*GetUserOrderListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTPeripheralMall_PayRmbResultNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayRmbResultNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTPeripheralMallServer).PayRmbResultNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_peripheral_mall.TTPeripheralMall/PayRmbResultNotify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTPeripheralMallServer).PayRmbResultNotify(ctx, req.(*PayRmbResultNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTPeripheralMall_GetUserOrderAddr_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserOrderAddrReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTPeripheralMallServer).GetUserOrderAddr(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_peripheral_mall.TTPeripheralMall/GetUserOrderAddr",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTPeripheralMallServer).GetUserOrderAddr(ctx, req.(*GetUserOrderAddrReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTPeripheralMall_SetUserOrderAddr_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserOrderAddrReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTPeripheralMallServer).SetUserOrderAddr(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_peripheral_mall.TTPeripheralMall/SetUserOrderAddr",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTPeripheralMallServer).SetUserOrderAddr(ctx, req.(*SetUserOrderAddrReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTPeripheralMall_BatAddProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatAddProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTPeripheralMallServer).BatAddProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_peripheral_mall.TTPeripheralMall/BatAddProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTPeripheralMallServer).BatAddProduct(ctx, req.(*BatAddProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTPeripheralMall_UpdateProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTPeripheralMallServer).UpdateProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_peripheral_mall.TTPeripheralMall/UpdateProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTPeripheralMallServer).UpdateProduct(ctx, req.(*UpdateProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTPeripheralMall_DeleteProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTPeripheralMallServer).DeleteProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_peripheral_mall.TTPeripheralMall/DeleteProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTPeripheralMallServer).DeleteProduct(ctx, req.(*DeleteProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _TTPeripheralMall_serviceDesc = grpc.ServiceDesc{
	ServiceName: "tt_peripheral_mall.TTPeripheralMall",
	HandlerType: (*TTPeripheralMallServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetProductSimpleInfoList",
			Handler:    _TTPeripheralMall_GetProductSimpleInfoList_Handler,
		},
		{
			MethodName: "GetProductDetailInfo",
			Handler:    _TTPeripheralMall_GetProductDetailInfo_Handler,
		},
		{
			MethodName: "OrderProduct",
			Handler:    _TTPeripheralMall_OrderProduct_Handler,
		},
		{
			MethodName: "GetUserOrderList",
			Handler:    _TTPeripheralMall_GetUserOrderList_Handler,
		},
		{
			MethodName: "PayRmbResultNotify",
			Handler:    _TTPeripheralMall_PayRmbResultNotify_Handler,
		},
		{
			MethodName: "GetUserOrderAddr",
			Handler:    _TTPeripheralMall_GetUserOrderAddr_Handler,
		},
		{
			MethodName: "SetUserOrderAddr",
			Handler:    _TTPeripheralMall_SetUserOrderAddr_Handler,
		},
		{
			MethodName: "BatAddProduct",
			Handler:    _TTPeripheralMall_BatAddProduct_Handler,
		},
		{
			MethodName: "UpdateProduct",
			Handler:    _TTPeripheralMall_UpdateProduct_Handler,
		},
		{
			MethodName: "DeleteProduct",
			Handler:    _TTPeripheralMall_DeleteProduct_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt-peripheral-mall/tt-peripheral-mall.proto",
}

func init() {
	proto.RegisterFile("tt-peripheral-mall/tt-peripheral-mall.proto", fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8)
}

var fileDescriptor_tt_peripheral_mall_ad854b9a011cadc8 = []byte{
	// 1394 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x58, 0x4d, 0x73, 0x1b, 0x45,
	0x13, 0x7e, 0xe5, 0x0f, 0x59, 0xdb, 0xb2, 0x1c, 0x65, 0xec, 0xd7, 0xd9, 0x28, 0xa1, 0xac, 0x6c,
	0xbe, 0x04, 0xc1, 0x36, 0x65, 0x08, 0x07, 0x0e, 0x40, 0x12, 0x57, 0x05, 0x05, 0x92, 0xa8, 0x56,
	0x36, 0x55, 0x50, 0x81, 0xad, 0xf5, 0xee, 0xd8, 0x9e, 0xf2, 0xee, 0xce, 0x64, 0x67, 0x94, 0x42,
	0x07, 0x7e, 0x17, 0x9c, 0x39, 0x73, 0xe7, 0xca, 0x81, 0x13, 0x3f, 0x81, 0xe2, 0x40, 0x4d, 0xcf,
	0xae, 0x3e, 0x56, 0xeb, 0x58, 0xa1, 0xc2, 0xc9, 0xea, 0xde, 0x9e, 0x67, 0xa6, 0xbb, 0x9f, 0x7e,
	0x66, 0xca, 0x70, 0x4f, 0xa9, 0x6d, 0x41, 0x53, 0x26, 0x4e, 0x69, 0xea, 0x47, 0xdb, 0xb1, 0x1f,
	0x45, 0xbb, 0xb3, 0xae, 0x1d, 0x91, 0x72, 0xc5, 0x09, 0x51, 0xca, 0x1b, 0x7f, 0xf1, 0xf4, 0x97,
	0xd6, 0x16, 0xfd, 0x41, 0xd1, 0x44, 0x32, 0x9e, 0xec, 0x72, 0xa1, 0x18, 0x4f, 0x64, 0xfe, 0xd7,
	0x2c, 0x6a, 0xdd, 0x1e, 0x24, 0xec, 0x98, 0xd1, 0x70, 0x5b, 0xf8, 0xc3, 0xed, 0x34, 0x3e, 0xda,
	0x2d, 0xd8, 0x26, 0xcc, 0xf9, 0xa5, 0x02, 0x8d, 0x5e, 0xca, 0xc3, 0x41, 0xa0, 0xfa, 0x2c, 0x16,
	0x11, 0x25, 0xef, 0x00, 0x08, 0xe3, 0xf0, 0x58, 0x68, 0x57, 0xda, 0x95, 0x4e, 0xc3, 0xb5, 0x32,
	0x4f, 0x37, 0x24, 0x1b, 0xb0, 0xac, 0x98, 0x8a, 0xa8, 0xbd, 0xd0, 0xae, 0x74, 0x2c, 0xd7, 0x18,
	0xe4, 0x06, 0xac, 0x9e, 0xf2, 0x98, 0x0a, 0xff, 0x84, 0x7a, 0x83, 0x34, 0xb2, 0x17, 0xf1, 0x63,
	0x3d, 0xf7, 0x1d, 0xa6, 0x91, 0x5e, 0x28, 0x52, 0x16, 0x50, 0x7b, 0x09, 0x21, 0x8d, 0x41, 0xda,
	0xb0, 0xca, 0xa4, 0x27, 0x15, 0x0f, 0xce, 0x3c, 0x3e, 0x50, 0xf6, 0x72, 0xbb, 0xd2, 0xa9, 0xb9,
	0xc0, 0x64, 0x5f, 0xbb, 0x9e, 0x0f, 0x94, 0x86, 0xe6, 0x29, 0x3b, 0x61, 0x89, 0x67, 0x96, 0x57,
	0x71, 0x79, 0xdd, 0xf8, 0x7a, 0xda, 0xe5, 0x7c, 0x07, 0xd7, 0x1e, 0x53, 0x35, 0x95, 0x46, 0x37,
	0x39, 0xe6, 0x5f, 0x31, 0xa9, 0x5c, 0xfa, 0x92, 0x34, 0x61, 0x71, 0x30, 0x4a, 0x45, 0xff, 0x24,
	0x9b, 0x50, 0xe5, 0xc7, 0xc7, 0x92, 0x2a, 0xcc, 0xa2, 0xe1, 0x66, 0x96, 0x3e, 0x63, 0xc4, 0x62,
	0xa6, 0xf0, 0xfc, 0x0d, 0xd7, 0x18, 0x4e, 0x08, 0xd7, 0xcf, 0x87, 0x97, 0x82, 0xec, 0xc3, 0x6a,
	0x5e, 0xb1, 0x88, 0x49, 0x65, 0x57, 0xda, 0x8b, 0x9d, 0xfa, 0xde, 0x8d, 0x9d, 0xd9, 0xb6, 0xed,
	0x4c, 0x81, 0xb8, 0xf5, 0x6c, 0x99, 0x46, 0x72, 0x7e, 0x5f, 0x18, 0x75, 0x62, 0x9f, 0x2a, 0x9f,
	0x45, 0xff, 0x59, 0x27, 0xb6, 0xa0, 0x1e, 0xe2, 0x0e, 0x5e, 0x48, 0x65, 0x80, 0xfd, 0xb0, 0x5c,
	0x30, 0xae, 0x7d, 0x2a, 0x83, 0x71, 0xab, 0x96, 0x27, 0x5b, 0xb5, 0x01, 0xcb, 0xd8, 0xa7, 0xac,
	0x03, 0xc6, 0x20, 0x77, 0xe0, 0x12, 0x56, 0x49, 0xa7, 0xea, 0xf1, 0x34, 0xa4, 0xa9, 0xbd, 0x82,
	0xdf, 0x1b, 0xe8, 0xee, 0xd1, 0xf4, 0xb9, 0x76, 0x92, 0xab, 0x50, 0x13, 0x2c, 0xd0, 0x47, 0x92,
	0x76, 0xad, 0xbd, 0xd8, 0xb1, 0xdc, 0x15, 0xc1, 0x82, 0xc3, 0x34, 0x92, 0xa4, 0x05, 0x35, 0x91,
	0x32, 0x9e, 0x32, 0x35, 0xb4, 0x2d, 0x5c, 0x3b, 0xb2, 0x67, 0xba, 0x0f, 0x33, 0xdd, 0x27, 0x1d,
	0x68, 0x0e, 0x64, 0xbe, 0x39, 0x0d, 0xbd, 0x20, 0x51, 0x76, 0x1d, 0xc3, 0xd6, 0xb4, 0xff, 0xb9,
	0x71, 0x3f, 0x4a, 0x94, 0xd3, 0x87, 0x06, 0x5a, 0x0f, 0xc2, 0x30, 0xd5, 0x1d, 0x24, 0x04, 0x96,
	0x12, 0x3f, 0xa6, 0x58, 0x5b, 0xcb, 0xc5, 0xdf, 0x98, 0xfc, 0x29, 0x4f, 0x46, 0x65, 0x45, 0x83,
	0xd8, 0xb0, 0xe2, 0x87, 0x61, 0x4a, 0xa5, 0xcc, 0x2a, 0x9a, 0x9b, 0xce, 0x13, 0xb8, 0x32, 0x66,
	0x87, 0xe9, 0x9c, 0xc6, 0x2e, 0x27, 0xde, 0x74, 0x4b, 0x17, 0x0a, 0x2d, 0x75, 0x42, 0xb0, 0xcb,
	0xb1, 0xa4, 0x20, 0x5f, 0xc0, 0x5a, 0xbe, 0xd4, 0xb4, 0x0a, 0x71, 0x5f, 0xcf, 0x33, 0x03, 0xe1,
	0x36, 0xc4, 0xa4, 0xe9, 0x7c, 0x09, 0xa4, 0xcf, 0x92, 0x93, 0x88, 0x66, 0x51, 0xa6, 0x41, 0x17,
	0xb0, 0x6d, 0x13, 0xaa, 0x7e, 0xcc, 0x07, 0xc9, 0x68, 0x64, 0x8c, 0xe5, 0xfc, 0x5d, 0x81, 0x66,
	0x6f, 0xb4, 0x7b, 0xcf, 0x1f, 0xea, 0xc4, 0xb7, 0xa0, 0x2e, 0xfc, 0xa1, 0x17, 0x9c, 0xfa, 0x49,
	0x42, 0xa3, 0xac, 0xbc, 0x20, 0xfc, 0xe1, 0x23, 0xe3, 0x21, 0xd7, 0xc0, 0x3a, 0x1a, 0x24, 0x61,
	0x44, 0xf3, 0x32, 0x58, 0x6e, 0xcd, 0x38, 0xba, 0x21, 0xb9, 0x05, 0x6b, 0x7a, 0xf5, 0xc4, 0x69,
	0x4c, 0xc9, 0x57, 0x85, 0x3f, 0xec, 0x8d, 0x0e, 0x74, 0x0d, 0xac, 0xd8, 0x4f, 0xcf, 0x28, 0x06,
	0x18, 0x4d, 0xa9, 0x19, 0x47, 0x37, 0x24, 0x57, 0x60, 0x85, 0x4b, 0x4f, 0x0d, 0x85, 0xe1, 0xb0,
	0xe5, 0x56, 0xb9, 0x3c, 0x18, 0x0a, 0xec, 0xe3, 0x2b, 0x9a, 0x6a, 0xdd, 0x44, 0x1a, 0x5b, 0x6e,
	0x6e, 0xea, 0x25, 0x48, 0x23, 0x26, 0x90, 0xc0, 0x96, 0x5b, 0xd5, 0x66, 0x57, 0xe8, 0x8d, 0x42,
	0xfa, 0x8a, 0x05, 0x78, 0xd6, 0x9a, 0x39, 0xab, 0x71, 0x74, 0x43, 0xe7, 0xcf, 0x0a, 0x5c, 0x2e,
	0xa4, 0x2f, 0x05, 0x8e, 0x26, 0x3f, 0xa3, 0x49, 0x96, 0xb9, 0x31, 0xb4, 0xd6, 0xe9, 0xbc, 0x90,
	0xa7, 0xe3, 0xbc, 0x75, 0x59, 0xb0, 0x03, 0xdd, 0x50, 0x0f, 0x53, 0x10, 0xb1, 0x2c, 0xc2, 0x0c,
	0xb7, 0x49, 0xbd, 0x11, 0x44, 0x0c, 0x83, 0x0e, 0x70, 0xc8, 0x9b, 0xb0, 0xa8, 0xe4, 0x59, 0x36,
	0xb9, 0xfa, 0xa7, 0xae, 0x78, 0x56, 0x6d, 0x2f, 0xf6, 0x45, 0x96, 0x34, 0x64, 0xae, 0xa7, 0xbe,
	0xd0, 0xed, 0xcd, 0x61, 0xe3, 0x5c, 0x44, 0x2d, 0x6e, 0x20, 0x63, 0x4a, 0x6e, 0xc2, 0x9a, 0xf2,
	0xc6, 0xa7, 0x4b, 0x78, 0x56, 0x84, 0xba, 0xea, 0x65, 0xc7, 0x7b, 0xc6, 0x9d, 0xdf, 0x2a, 0x70,
	0x09, 0x7f, 0x67, 0x5d, 0x28, 0xe7, 0xf8, 0x27, 0xb0, 0x84, 0x32, 0xb8, 0x80, 0x32, 0x78, 0xa7,
	0x8c, 0x9e, 0xb3, 0xf4, 0x73, 0x71, 0x0d, 0xb9, 0x0f, 0x4b, 0x7a, 0xae, 0x30, 0xeb, 0x73, 0xa8,
	0x3d, 0x35, 0xc1, 0x2e, 0x86, 0x93, 0xcf, 0xa0, 0xa6, 0xcf, 0xce, 0x92, 0x63, 0x8e, 0x45, 0xa9,
	0xef, 0xdd, 0x2a, 0x9d, 0x8a, 0x02, 0x4f, 0xdd, 0x15, 0xe1, 0x0f, 0x35, 0x8c, 0x73, 0x00, 0xcd,
	0xe9, 0xc4, 0xa4, 0x20, 0x9f, 0x4f, 0x80, 0x9a, 0x51, 0xbb, 0x3d, 0x07, 0xa8, 0x14, 0x63, 0xd4,
	0x3f, 0x2a, 0x50, 0x47, 0xd8, 0x4c, 0xd0, 0xaf, 0x42, 0x6d, 0xd4, 0x7c, 0xc3, 0x8c, 0x15, 0x9e,
	0x75, 0xbe, 0x78, 0x87, 0x2c, 0x5c, 0x78, 0x87, 0x64, 0xb3, 0x3d, 0x79, 0x87, 0xfc, 0xdb, 0xf2,
	0x6d, 0x41, 0x5d, 0x71, 0xe5, 0x47, 0xde, 0xe4, 0x05, 0x0d, 0xe8, 0x32, 0x12, 0x7b, 0x1d, 0x2c,
	0x4d, 0x1b, 0xa9, 0xfc, 0xd8, 0x70, 0x6b, 0xd1, 0x1d, 0x3b, 0x9c, 0x43, 0x58, 0x7f, 0x4c, 0xd5,
	0x61, 0xae, 0xb5, 0x6f, 0xeb, 0xda, 0xfd, 0x1a, 0x36, 0x66, 0x61, 0xa5, 0x20, 0x9f, 0xe6, 0x4c,
	0x9e, 0xb8, 0x6c, 0xb7, 0xce, 0x4d, 0x35, 0x2b, 0x93, 0xa1, 0x3a, 0x5e, 0xb4, 0x2f, 0xe0, 0xff,
	0xba, 0x53, 0xf1, 0x91, 0x4b, 0xe5, 0x20, 0x52, 0xcf, 0xb8, 0x62, 0xc7, 0xa8, 0x5a, 0x8f, 0xf4,
	0x5d, 0xa3, 0x81, 0x53, 0xfc, 0x90, 0x35, 0xbd, 0xbd, 0x93, 0xbd, 0x9c, 0x70, 0x3c, 0xf4, 0xcb,
	0xc9, 0x8d, 0x8f, 0xf2, 0xa1, 0xc0, 0x22, 0xd6, 0x71, 0x95, 0x41, 0x73, 0x6c, 0xd8, 0x2c, 0x43,
	0x97, 0xc2, 0x79, 0x09, 0xcd, 0x87, 0xbe, 0x7a, 0x10, 0x86, 0x13, 0xd3, 0xa3, 0x75, 0x4a, 0x78,
	0x5a, 0x68, 0xb2, 0x3a, 0x55, 0xb9, 0xd0, 0x19, 0xbf, 0x1d, 0x3e, 0x38, 0xeb, 0x70, 0xb9, 0xb0,
	0xa5, 0x14, 0xce, 0x11, 0x34, 0x0f, 0x45, 0xe8, 0x2b, 0x3a, 0xcf, 0x39, 0xee, 0xc3, 0x12, 0x0e,
	0xc0, 0xe2, 0xbc, 0x77, 0x0d, 0x86, 0xeb, 0x8d, 0x0b, 0x7b, 0x48, 0xe1, 0x3c, 0x81, 0xe6, 0x3e,
	0x8d, 0xe8, 0x7c, 0x1b, 0x5f, 0x70, 0x53, 0xae, 0xc3, 0xe5, 0x02, 0x96, 0x14, 0xce, 0xdd, 0x69,
	0x22, 0x6a, 0x96, 0x97, 0x12, 0xd1, 0x79, 0x3a, 0x4d, 0x2d, 0x13, 0x28, 0xc5, 0x68, 0x7e, 0x2a,
	0x6f, 0x34, 0x3f, 0xce, 0xf7, 0xb0, 0xde, 0x9f, 0x67, 0xdf, 0x11, 0xfe, 0xc2, 0x9b, 0xe1, 0x6f,
	0xc2, 0x46, 0xbf, 0xe4, 0xb8, 0x7b, 0x3f, 0xd7, 0xa0, 0x79, 0x70, 0x30, 0x16, 0xa0, 0xa7, 0x7e,
	0x14, 0x91, 0x1f, 0x27, 0xdf, 0x10, 0xd3, 0xaf, 0x55, 0xb2, 0x5b, 0xb6, 0xe3, 0x6b, 0x9e, 0xce,
	0xad, 0x0f, 0xde, 0x6c, 0x81, 0x14, 0xe4, 0x25, 0x96, 0x76, 0xe6, 0x09, 0x43, 0xee, 0xbd, 0x1e,
	0x69, 0xea, 0xe1, 0xd4, 0x7a, 0x7f, 0xfe, 0x60, 0x29, 0xc8, 0x37, 0xb0, 0x3a, 0x29, 0xde, 0xe4,
	0xe6, 0xb9, 0x75, 0x1d, 0x13, 0xaf, 0x75, 0xeb, 0xe2, 0x20, 0x29, 0x08, 0x85, 0x66, 0x51, 0x83,
	0xc8, 0xdd, 0x73, 0x0e, 0x57, 0x14, 0xc0, 0x56, 0x67, 0xbe, 0x40, 0x29, 0x48, 0x0c, 0x64, 0x56,
	0x34, 0xc8, 0xbb, 0xa5, 0xd3, 0x56, 0x26, 0x5d, 0xad, 0xf7, 0xe6, 0x0d, 0x95, 0xc2, 0xf9, 0x5f,
	0x31, 0x2b, 0xcd, 0xa7, 0x8b, 0xb3, 0xca, 0x58, 0x7d, 0x71, 0x56, 0xa3, 0x69, 0xa2, 0xd0, 0xec,
	0xcf, 0xb5, 0x4d, 0x7f, 0xde, 0x6d, 0xca, 0xa6, 0x80, 0xbc, 0x80, 0xc6, 0x94, 0xc8, 0x91, 0xd2,
	0xd6, 0x16, 0xa5, 0xb7, 0x75, 0x7b, 0x8e, 0x28, 0x83, 0x3e, 0xa5, 0x64, 0xe5, 0xe8, 0x45, 0x41,
	0x2d, 0x47, 0x9f, 0x91, 0x44, 0x8d, 0x3e, 0x25, 0x63, 0xe5, 0xe8, 0x45, 0xd5, 0x2c, 0x47, 0x9f,
	0xd1, 0xc3, 0xd6, 0xd5, 0xbf, 0x7e, 0xfa, 0xf5, 0x60, 0x03, 0xc8, 0xec, 0x7f, 0x16, 0x1e, 0x7e,
	0xfc, 0xed, 0x47, 0x27, 0x3c, 0xf2, 0x93, 0x93, 0x9d, 0xfb, 0x7b, 0x4a, 0xed, 0x04, 0x3c, 0xde,
	0xc5, 0x7f, 0x08, 0x04, 0x3c, 0xda, 0x95, 0x34, 0xd5, 0xaf, 0x5b, 0x59, 0xf2, 0x1f, 0x89, 0xa3,
	0x2a, 0x46, 0x7d, 0xf8, 0x4f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x78, 0x27, 0xd4, 0x46, 0xc1, 0x10,
	0x00, 0x00,
}
