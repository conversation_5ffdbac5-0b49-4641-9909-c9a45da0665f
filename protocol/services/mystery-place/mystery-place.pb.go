// Code generated by protoc-gen-go. DO NOT EDIT.
// source: mystery-place/mystery-place.proto

package mystery_place // import "golang.52tt.com/protocol/services/mystery-place"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PlaymateTagType int32

const (
	PlaymateTagType_PlaymateTagType_UNDEFINED PlaymateTagType = 0
	PlaymateTagType_PlaymateTagType_GOOD      PlaymateTagType = 1
	PlaymateTagType_PlaymateTagType_BAD       PlaymateTagType = 2
)

var PlaymateTagType_name = map[int32]string{
	0: "PlaymateTagType_UNDEFINED",
	1: "PlaymateTagType_GOOD",
	2: "PlaymateTagType_BAD",
}
var PlaymateTagType_value = map[string]int32{
	"PlaymateTagType_UNDEFINED": 0,
	"PlaymateTagType_GOOD":      1,
	"PlaymateTagType_BAD":       2,
}

func (x PlaymateTagType) String() string {
	return proto.EnumName(PlaymateTagType_name, int32(x))
}
func (PlaymateTagType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{0}
}

type GameOrientation int32

const (
	GameOrientation_GAME_ORIENTATION_UNDEFINED  GameOrientation = 0
	GameOrientation_GAME_ORIENTATION_HORIZONTAL GameOrientation = 1
	GameOrientation_GAME_ORIENTATION_VERTICAL   GameOrientation = 2
)

var GameOrientation_name = map[int32]string{
	0: "GAME_ORIENTATION_UNDEFINED",
	1: "GAME_ORIENTATION_HORIZONTAL",
	2: "GAME_ORIENTATION_VERTICAL",
}
var GameOrientation_value = map[string]int32{
	"GAME_ORIENTATION_UNDEFINED":  0,
	"GAME_ORIENTATION_HORIZONTAL": 1,
	"GAME_ORIENTATION_VERTICAL":   2,
}

func (x GameOrientation) String() string {
	return proto.EnumName(GameOrientation_name, int32(x))
}
func (GameOrientation) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{1}
}

type ScenarioTagType int32

const (
	ScenarioTagType_ScenarioTagType_UNDEFINED ScenarioTagType = 0
	ScenarioTagType_ScenarioTagType_SUBSCRIBE ScenarioTagType = 1
	ScenarioTagType_ScenarioTagType_NEW       ScenarioTagType = 2
	ScenarioTagType_ScenarioTagType_HOT       ScenarioTagType = 3
)

var ScenarioTagType_name = map[int32]string{
	0: "ScenarioTagType_UNDEFINED",
	1: "ScenarioTagType_SUBSCRIBE",
	2: "ScenarioTagType_NEW",
	3: "ScenarioTagType_HOT",
}
var ScenarioTagType_value = map[string]int32{
	"ScenarioTagType_UNDEFINED": 0,
	"ScenarioTagType_SUBSCRIBE": 1,
	"ScenarioTagType_NEW":       2,
	"ScenarioTagType_HOT":       3,
}

func (x ScenarioTagType) String() string {
	return proto.EnumName(ScenarioTagType_name, int32(x))
}
func (ScenarioTagType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{2}
}

type PlayMode int32

const (
	PlayMode_None   PlayMode = 0
	PlayMode_Single PlayMode = 1
	PlayMode_Multi  PlayMode = 2
)

var PlayMode_name = map[int32]string{
	0: "None",
	1: "Single",
	2: "Multi",
}
var PlayMode_value = map[string]int32{
	"None":   0,
	"Single": 1,
	"Multi":  2,
}

func (x PlayMode) String() string {
	return proto.EnumName(PlayMode_name, int32(x))
}
func (PlayMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{3}
}

type ScenarioTabStatus int32

const (
	ScenarioTabStatus_ScenarioTabStatus_UNDEFINED ScenarioTabStatus = 0
	ScenarioTabStatus_ScenarioTabStatus_NEW       ScenarioTabStatus = 1
)

var ScenarioTabStatus_name = map[int32]string{
	0: "ScenarioTabStatus_UNDEFINED",
	1: "ScenarioTabStatus_NEW",
}
var ScenarioTabStatus_value = map[string]int32{
	"ScenarioTabStatus_UNDEFINED": 0,
	"ScenarioTabStatus_NEW":       1,
}

func (x ScenarioTabStatus) String() string {
	return proto.EnumName(ScenarioTabStatus_name, int32(x))
}
func (ScenarioTabStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{4}
}

type SortType int32

const (
	SortType_OLD_USER_SORT SortType = 0
	SortType_NEW_USER_SORT SortType = 1
)

var SortType_name = map[int32]string{
	0: "OLD_USER_SORT",
	1: "NEW_USER_SORT",
}
var SortType_value = map[string]int32{
	"OLD_USER_SORT": 0,
	"NEW_USER_SORT": 1,
}

func (x SortType) String() string {
	return proto.EnumName(SortType_name, int32(x))
}
func (SortType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{5}
}

// 密室逃脱房，邀请进房结果
type MysteryInviteResult int32

const (
	MysteryInviteResult_NOT_FOUND   MysteryInviteResult = 0
	MysteryInviteResult_NOT_RESPOND MysteryInviteResult = 1
	MysteryInviteResult_ACCEPT      MysteryInviteResult = 2
	MysteryInviteResult_REJECT      MysteryInviteResult = 3
)

var MysteryInviteResult_name = map[int32]string{
	0: "NOT_FOUND",
	1: "NOT_RESPOND",
	2: "ACCEPT",
	3: "REJECT",
}
var MysteryInviteResult_value = map[string]int32{
	"NOT_FOUND":   0,
	"NOT_RESPOND": 1,
	"ACCEPT":      2,
	"REJECT":      3,
}

func (x MysteryInviteResult) String() string {
	return proto.EnumName(MysteryInviteResult_name, int32(x))
}
func (MysteryInviteResult) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{6}
}

type CommentTypeFilter int32

const (
	CommentTypeFilter_ALL_COMMENT_TYPE   CommentTypeFilter = 0
	CommentTypeFilter_UNFINISHED_COMMENT CommentTypeFilter = 1
	CommentTypeFilter_FINISHED_COMMENT   CommentTypeFilter = 2
)

var CommentTypeFilter_name = map[int32]string{
	0: "ALL_COMMENT_TYPE",
	1: "UNFINISHED_COMMENT",
	2: "FINISHED_COMMENT",
}
var CommentTypeFilter_value = map[string]int32{
	"ALL_COMMENT_TYPE":   0,
	"UNFINISHED_COMMENT": 1,
	"FINISHED_COMMENT":   2,
}

func (x CommentTypeFilter) String() string {
	return proto.EnumName(CommentTypeFilter_name, int32(x))
}
func (CommentTypeFilter) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{7}
}

type DisplayFilter int32

const (
	DisplayFilter_IGNORE_DISPLAY_TYPE DisplayFilter = 0
	DisplayFilter_NO_DISPLAY_COMMENT  DisplayFilter = 1
	DisplayFilter_DISPLAY_COMMENT     DisplayFilter = 2
)

var DisplayFilter_name = map[int32]string{
	0: "IGNORE_DISPLAY_TYPE",
	1: "NO_DISPLAY_COMMENT",
	2: "DISPLAY_COMMENT",
}
var DisplayFilter_value = map[string]int32{
	"IGNORE_DISPLAY_TYPE": 0,
	"NO_DISPLAY_COMMENT":  1,
	"DISPLAY_COMMENT":     2,
}

func (x DisplayFilter) String() string {
	return proto.EnumName(DisplayFilter_name, int32(x))
}
func (DisplayFilter) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{8}
}

type NewScenarioTipType int32

const (
	NewScenarioTipType_NewScenarioTipType_UNDEFINED NewScenarioTipType = 0
	NewScenarioTipType_NewScenarioTipType_NEW_TAB   NewScenarioTipType = 1
	NewScenarioTipType_NewScenarioTipType_SUBSCRIBE NewScenarioTipType = 2
	NewScenarioTipType_NewScenarioTipType_NEW       NewScenarioTipType = 3
)

var NewScenarioTipType_name = map[int32]string{
	0: "NewScenarioTipType_UNDEFINED",
	1: "NewScenarioTipType_NEW_TAB",
	2: "NewScenarioTipType_SUBSCRIBE",
	3: "NewScenarioTipType_NEW",
}
var NewScenarioTipType_value = map[string]int32{
	"NewScenarioTipType_UNDEFINED": 0,
	"NewScenarioTipType_NEW_TAB":   1,
	"NewScenarioTipType_SUBSCRIBE": 2,
	"NewScenarioTipType_NEW":       3,
}

func (x NewScenarioTipType) String() string {
	return proto.EnumName(NewScenarioTipType_name, int32(x))
}
func (NewScenarioTipType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{9}
}

type ResType int32

const (
	ResType_ResType_UNDEFINED ResType = 0
	ResType_ResType_IMG       ResType = 1
	ResType_ResType_VIDEO     ResType = 2
)

var ResType_name = map[int32]string{
	0: "ResType_UNDEFINED",
	1: "ResType_IMG",
	2: "ResType_VIDEO",
}
var ResType_value = map[string]int32{
	"ResType_UNDEFINED": 0,
	"ResType_IMG":       1,
	"ResType_VIDEO":     2,
}

func (x ResType) String() string {
	return proto.EnumName(ResType_name, int32(x))
}
func (ResType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{10}
}

type UserSex int32

const (
	UserSex_UserSex_FEMALE UserSex = 0
	UserSex_UserSex_MALE   UserSex = 1
)

var UserSex_name = map[int32]string{
	0: "UserSex_FEMALE",
	1: "UserSex_MALE",
}
var UserSex_value = map[string]int32{
	"UserSex_FEMALE": 0,
	"UserSex_MALE":   1,
}

func (x UserSex) String() string {
	return proto.EnumName(UserSex_name, int32(x))
}
func (UserSex) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{11}
}

type ScenarioIncWeightInfo_Weightstatus int32

const (
	ScenarioIncWeightInfo_WEIGHTSTATUS_UNSPECIFIED ScenarioIncWeightInfo_Weightstatus = 0
	ScenarioIncWeightInfo_WEIGHTSTATUS_ON          ScenarioIncWeightInfo_Weightstatus = 1
	ScenarioIncWeightInfo_WEIGHTSTATUS_OFF         ScenarioIncWeightInfo_Weightstatus = 2
	ScenarioIncWeightInfo_WEIGHTSTATUS_INEFFECTIVE ScenarioIncWeightInfo_Weightstatus = 3
)

var ScenarioIncWeightInfo_Weightstatus_name = map[int32]string{
	0: "WEIGHTSTATUS_UNSPECIFIED",
	1: "WEIGHTSTATUS_ON",
	2: "WEIGHTSTATUS_OFF",
	3: "WEIGHTSTATUS_INEFFECTIVE",
}
var ScenarioIncWeightInfo_Weightstatus_value = map[string]int32{
	"WEIGHTSTATUS_UNSPECIFIED": 0,
	"WEIGHTSTATUS_ON":          1,
	"WEIGHTSTATUS_OFF":         2,
	"WEIGHTSTATUS_INEFFECTIVE": 3,
}

func (x ScenarioIncWeightInfo_Weightstatus) String() string {
	return proto.EnumName(ScenarioIncWeightInfo_Weightstatus_name, int32(x))
}
func (ScenarioIncWeightInfo_Weightstatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{30, 0}
}

type PlaymateTag struct {
	Id                   string          `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Content              string          `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Score                uint32          `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`
	TagType              PlaymateTagType `protobuf:"varint,4,opt,name=tag_type,json=tagType,proto3,enum=mystery_place.PlaymateTagType" json:"tag_type,omitempty"`
	Order                uint32          `protobuf:"varint,5,opt,name=order,proto3" json:"order,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *PlaymateTag) Reset()         { *m = PlaymateTag{} }
func (m *PlaymateTag) String() string { return proto.CompactTextString(m) }
func (*PlaymateTag) ProtoMessage()    {}
func (*PlaymateTag) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{0}
}
func (m *PlaymateTag) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaymateTag.Unmarshal(m, b)
}
func (m *PlaymateTag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaymateTag.Marshal(b, m, deterministic)
}
func (dst *PlaymateTag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaymateTag.Merge(dst, src)
}
func (m *PlaymateTag) XXX_Size() int {
	return xxx_messageInfo_PlaymateTag.Size(m)
}
func (m *PlaymateTag) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaymateTag.DiscardUnknown(m)
}

var xxx_messageInfo_PlaymateTag proto.InternalMessageInfo

func (m *PlaymateTag) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *PlaymateTag) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PlaymateTag) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *PlaymateTag) GetTagType() PlaymateTagType {
	if m != nil {
		return m.TagType
	}
	return PlaymateTagType_PlaymateTagType_UNDEFINED
}

func (m *PlaymateTag) GetOrder() uint32 {
	if m != nil {
		return m.Order
	}
	return 0
}

// 新增玩伴标签
type AddPlaymateTagReq struct {
	Tag                  *PlaymateTag `protobuf:"bytes,1,opt,name=tag,proto3" json:"tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AddPlaymateTagReq) Reset()         { *m = AddPlaymateTagReq{} }
func (m *AddPlaymateTagReq) String() string { return proto.CompactTextString(m) }
func (*AddPlaymateTagReq) ProtoMessage()    {}
func (*AddPlaymateTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{1}
}
func (m *AddPlaymateTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPlaymateTagReq.Unmarshal(m, b)
}
func (m *AddPlaymateTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPlaymateTagReq.Marshal(b, m, deterministic)
}
func (dst *AddPlaymateTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPlaymateTagReq.Merge(dst, src)
}
func (m *AddPlaymateTagReq) XXX_Size() int {
	return xxx_messageInfo_AddPlaymateTagReq.Size(m)
}
func (m *AddPlaymateTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPlaymateTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPlaymateTagReq proto.InternalMessageInfo

func (m *AddPlaymateTagReq) GetTag() *PlaymateTag {
	if m != nil {
		return m.Tag
	}
	return nil
}

type AddPlaymateTagResp struct {
	Tag                  *PlaymateTag `protobuf:"bytes,1,opt,name=tag,proto3" json:"tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AddPlaymateTagResp) Reset()         { *m = AddPlaymateTagResp{} }
func (m *AddPlaymateTagResp) String() string { return proto.CompactTextString(m) }
func (*AddPlaymateTagResp) ProtoMessage()    {}
func (*AddPlaymateTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{2}
}
func (m *AddPlaymateTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPlaymateTagResp.Unmarshal(m, b)
}
func (m *AddPlaymateTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPlaymateTagResp.Marshal(b, m, deterministic)
}
func (dst *AddPlaymateTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPlaymateTagResp.Merge(dst, src)
}
func (m *AddPlaymateTagResp) XXX_Size() int {
	return xxx_messageInfo_AddPlaymateTagResp.Size(m)
}
func (m *AddPlaymateTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPlaymateTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPlaymateTagResp proto.InternalMessageInfo

func (m *AddPlaymateTagResp) GetTag() *PlaymateTag {
	if m != nil {
		return m.Tag
	}
	return nil
}

// 删除玩伴标签
type DeletePlaymateTagReq struct {
	TagId                string   `protobuf:"bytes,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePlaymateTagReq) Reset()         { *m = DeletePlaymateTagReq{} }
func (m *DeletePlaymateTagReq) String() string { return proto.CompactTextString(m) }
func (*DeletePlaymateTagReq) ProtoMessage()    {}
func (*DeletePlaymateTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{3}
}
func (m *DeletePlaymateTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeletePlaymateTagReq.Unmarshal(m, b)
}
func (m *DeletePlaymateTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeletePlaymateTagReq.Marshal(b, m, deterministic)
}
func (dst *DeletePlaymateTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePlaymateTagReq.Merge(dst, src)
}
func (m *DeletePlaymateTagReq) XXX_Size() int {
	return xxx_messageInfo_DeletePlaymateTagReq.Size(m)
}
func (m *DeletePlaymateTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePlaymateTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePlaymateTagReq proto.InternalMessageInfo

func (m *DeletePlaymateTagReq) GetTagId() string {
	if m != nil {
		return m.TagId
	}
	return ""
}

type DeletePlaymateTagResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePlaymateTagResp) Reset()         { *m = DeletePlaymateTagResp{} }
func (m *DeletePlaymateTagResp) String() string { return proto.CompactTextString(m) }
func (*DeletePlaymateTagResp) ProtoMessage()    {}
func (*DeletePlaymateTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{4}
}
func (m *DeletePlaymateTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeletePlaymateTagResp.Unmarshal(m, b)
}
func (m *DeletePlaymateTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeletePlaymateTagResp.Marshal(b, m, deterministic)
}
func (dst *DeletePlaymateTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePlaymateTagResp.Merge(dst, src)
}
func (m *DeletePlaymateTagResp) XXX_Size() int {
	return xxx_messageInfo_DeletePlaymateTagResp.Size(m)
}
func (m *DeletePlaymateTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePlaymateTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePlaymateTagResp proto.InternalMessageInfo

// 修改玩伴标签
type UpdatePlaymateTagReq struct {
	Tag                  *PlaymateTag `protobuf:"bytes,1,opt,name=tag,proto3" json:"tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdatePlaymateTagReq) Reset()         { *m = UpdatePlaymateTagReq{} }
func (m *UpdatePlaymateTagReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePlaymateTagReq) ProtoMessage()    {}
func (*UpdatePlaymateTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{5}
}
func (m *UpdatePlaymateTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePlaymateTagReq.Unmarshal(m, b)
}
func (m *UpdatePlaymateTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePlaymateTagReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePlaymateTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePlaymateTagReq.Merge(dst, src)
}
func (m *UpdatePlaymateTagReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePlaymateTagReq.Size(m)
}
func (m *UpdatePlaymateTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePlaymateTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePlaymateTagReq proto.InternalMessageInfo

func (m *UpdatePlaymateTagReq) GetTag() *PlaymateTag {
	if m != nil {
		return m.Tag
	}
	return nil
}

type UpdatePlaymateTagResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePlaymateTagResp) Reset()         { *m = UpdatePlaymateTagResp{} }
func (m *UpdatePlaymateTagResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePlaymateTagResp) ProtoMessage()    {}
func (*UpdatePlaymateTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{6}
}
func (m *UpdatePlaymateTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePlaymateTagResp.Unmarshal(m, b)
}
func (m *UpdatePlaymateTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePlaymateTagResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePlaymateTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePlaymateTagResp.Merge(dst, src)
}
func (m *UpdatePlaymateTagResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePlaymateTagResp.Size(m)
}
func (m *UpdatePlaymateTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePlaymateTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePlaymateTagResp proto.InternalMessageInfo

// 重排序玩伴标签
type ReorderPlaymateTagsReq struct {
	TagIds               []string `protobuf:"bytes,1,rep,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReorderPlaymateTagsReq) Reset()         { *m = ReorderPlaymateTagsReq{} }
func (m *ReorderPlaymateTagsReq) String() string { return proto.CompactTextString(m) }
func (*ReorderPlaymateTagsReq) ProtoMessage()    {}
func (*ReorderPlaymateTagsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{7}
}
func (m *ReorderPlaymateTagsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReorderPlaymateTagsReq.Unmarshal(m, b)
}
func (m *ReorderPlaymateTagsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReorderPlaymateTagsReq.Marshal(b, m, deterministic)
}
func (dst *ReorderPlaymateTagsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReorderPlaymateTagsReq.Merge(dst, src)
}
func (m *ReorderPlaymateTagsReq) XXX_Size() int {
	return xxx_messageInfo_ReorderPlaymateTagsReq.Size(m)
}
func (m *ReorderPlaymateTagsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReorderPlaymateTagsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReorderPlaymateTagsReq proto.InternalMessageInfo

func (m *ReorderPlaymateTagsReq) GetTagIds() []string {
	if m != nil {
		return m.TagIds
	}
	return nil
}

type ReorderPlaymateTagsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReorderPlaymateTagsResp) Reset()         { *m = ReorderPlaymateTagsResp{} }
func (m *ReorderPlaymateTagsResp) String() string { return proto.CompactTextString(m) }
func (*ReorderPlaymateTagsResp) ProtoMessage()    {}
func (*ReorderPlaymateTagsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{8}
}
func (m *ReorderPlaymateTagsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReorderPlaymateTagsResp.Unmarshal(m, b)
}
func (m *ReorderPlaymateTagsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReorderPlaymateTagsResp.Marshal(b, m, deterministic)
}
func (dst *ReorderPlaymateTagsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReorderPlaymateTagsResp.Merge(dst, src)
}
func (m *ReorderPlaymateTagsResp) XXX_Size() int {
	return xxx_messageInfo_ReorderPlaymateTagsResp.Size(m)
}
func (m *ReorderPlaymateTagsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReorderPlaymateTagsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReorderPlaymateTagsResp proto.InternalMessageInfo

// 获取玩伴标签列表
type GetPlaymateTagsReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlaymateTagsReq) Reset()         { *m = GetPlaymateTagsReq{} }
func (m *GetPlaymateTagsReq) String() string { return proto.CompactTextString(m) }
func (*GetPlaymateTagsReq) ProtoMessage()    {}
func (*GetPlaymateTagsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{9}
}
func (m *GetPlaymateTagsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlaymateTagsReq.Unmarshal(m, b)
}
func (m *GetPlaymateTagsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlaymateTagsReq.Marshal(b, m, deterministic)
}
func (dst *GetPlaymateTagsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlaymateTagsReq.Merge(dst, src)
}
func (m *GetPlaymateTagsReq) XXX_Size() int {
	return xxx_messageInfo_GetPlaymateTagsReq.Size(m)
}
func (m *GetPlaymateTagsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlaymateTagsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlaymateTagsReq proto.InternalMessageInfo

type GetPlaymateTagsResp struct {
	Tags                 []*PlaymateTag `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetPlaymateTagsResp) Reset()         { *m = GetPlaymateTagsResp{} }
func (m *GetPlaymateTagsResp) String() string { return proto.CompactTextString(m) }
func (*GetPlaymateTagsResp) ProtoMessage()    {}
func (*GetPlaymateTagsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{10}
}
func (m *GetPlaymateTagsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlaymateTagsResp.Unmarshal(m, b)
}
func (m *GetPlaymateTagsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlaymateTagsResp.Marshal(b, m, deterministic)
}
func (dst *GetPlaymateTagsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlaymateTagsResp.Merge(dst, src)
}
func (m *GetPlaymateTagsResp) XXX_Size() int {
	return xxx_messageInfo_GetPlaymateTagsResp.Size(m)
}
func (m *GetPlaymateTagsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlaymateTagsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlaymateTagsResp proto.InternalMessageInfo

func (m *GetPlaymateTagsResp) GetTags() []*PlaymateTag {
	if m != nil {
		return m.Tags
	}
	return nil
}

// 通过id查询玩伴标签
type GetPlaymateTagByIdsReq struct {
	TagIds               []string `protobuf:"bytes,1,rep,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlaymateTagByIdsReq) Reset()         { *m = GetPlaymateTagByIdsReq{} }
func (m *GetPlaymateTagByIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetPlaymateTagByIdsReq) ProtoMessage()    {}
func (*GetPlaymateTagByIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{11}
}
func (m *GetPlaymateTagByIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlaymateTagByIdsReq.Unmarshal(m, b)
}
func (m *GetPlaymateTagByIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlaymateTagByIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetPlaymateTagByIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlaymateTagByIdsReq.Merge(dst, src)
}
func (m *GetPlaymateTagByIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetPlaymateTagByIdsReq.Size(m)
}
func (m *GetPlaymateTagByIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlaymateTagByIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlaymateTagByIdsReq proto.InternalMessageInfo

func (m *GetPlaymateTagByIdsReq) GetTagIds() []string {
	if m != nil {
		return m.TagIds
	}
	return nil
}

type GetPlaymateTagByIdsResp struct {
	Tags                 []*PlaymateTag `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetPlaymateTagByIdsResp) Reset()         { *m = GetPlaymateTagByIdsResp{} }
func (m *GetPlaymateTagByIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetPlaymateTagByIdsResp) ProtoMessage()    {}
func (*GetPlaymateTagByIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{12}
}
func (m *GetPlaymateTagByIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlaymateTagByIdsResp.Unmarshal(m, b)
}
func (m *GetPlaymateTagByIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlaymateTagByIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetPlaymateTagByIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlaymateTagByIdsResp.Merge(dst, src)
}
func (m *GetPlaymateTagByIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetPlaymateTagByIdsResp.Size(m)
}
func (m *GetPlaymateTagByIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlaymateTagByIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlaymateTagByIdsResp proto.InternalMessageInfo

func (m *GetPlaymateTagByIdsResp) GetTags() []*PlaymateTag {
	if m != nil {
		return m.Tags
	}
	return nil
}

// 新增用户标签记录
type AddUserPlaymateTagReq struct {
	FromUid              uint32          `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	ToUid                uint32          `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	TagType              PlaymateTagType `protobuf:"varint,3,opt,name=tag_type,json=tagType,proto3,enum=mystery_place.PlaymateTagType" json:"tag_type,omitempty"`
	TagIds               []string        `protobuf:"bytes,4,rep,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	CreatedAt            uint32          `protobuf:"varint,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	SetId                string          `protobuf:"bytes,6,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AddUserPlaymateTagReq) Reset()         { *m = AddUserPlaymateTagReq{} }
func (m *AddUserPlaymateTagReq) String() string { return proto.CompactTextString(m) }
func (*AddUserPlaymateTagReq) ProtoMessage()    {}
func (*AddUserPlaymateTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{13}
}
func (m *AddUserPlaymateTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserPlaymateTagReq.Unmarshal(m, b)
}
func (m *AddUserPlaymateTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserPlaymateTagReq.Marshal(b, m, deterministic)
}
func (dst *AddUserPlaymateTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserPlaymateTagReq.Merge(dst, src)
}
func (m *AddUserPlaymateTagReq) XXX_Size() int {
	return xxx_messageInfo_AddUserPlaymateTagReq.Size(m)
}
func (m *AddUserPlaymateTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserPlaymateTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserPlaymateTagReq proto.InternalMessageInfo

func (m *AddUserPlaymateTagReq) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *AddUserPlaymateTagReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *AddUserPlaymateTagReq) GetTagType() PlaymateTagType {
	if m != nil {
		return m.TagType
	}
	return PlaymateTagType_PlaymateTagType_UNDEFINED
}

func (m *AddUserPlaymateTagReq) GetTagIds() []string {
	if m != nil {
		return m.TagIds
	}
	return nil
}

func (m *AddUserPlaymateTagReq) GetCreatedAt() uint32 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *AddUserPlaymateTagReq) GetSetId() string {
	if m != nil {
		return m.SetId
	}
	return ""
}

type AddUserPlaymateTagResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserPlaymateTagResp) Reset()         { *m = AddUserPlaymateTagResp{} }
func (m *AddUserPlaymateTagResp) String() string { return proto.CompactTextString(m) }
func (*AddUserPlaymateTagResp) ProtoMessage()    {}
func (*AddUserPlaymateTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{14}
}
func (m *AddUserPlaymateTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserPlaymateTagResp.Unmarshal(m, b)
}
func (m *AddUserPlaymateTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserPlaymateTagResp.Marshal(b, m, deterministic)
}
func (dst *AddUserPlaymateTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserPlaymateTagResp.Merge(dst, src)
}
func (m *AddUserPlaymateTagResp) XXX_Size() int {
	return xxx_messageInfo_AddUserPlaymateTagResp.Size(m)
}
func (m *AddUserPlaymateTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserPlaymateTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserPlaymateTagResp proto.InternalMessageInfo

type BatGetUserHotPlaymateTagReq struct {
	UidList              []uint32        `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	ReturnTagCount       uint32          `protobuf:"varint,2,opt,name=return_tag_count,json=returnTagCount,proto3" json:"return_tag_count,omitempty"`
	StatCount            uint32          `protobuf:"varint,3,opt,name=stat_count,json=statCount,proto3" json:"stat_count,omitempty"`
	StatTagType          PlaymateTagType `protobuf:"varint,4,opt,name=stat_tag_type,json=statTagType,proto3,enum=mystery_place.PlaymateTagType" json:"stat_tag_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatGetUserHotPlaymateTagReq) Reset()         { *m = BatGetUserHotPlaymateTagReq{} }
func (m *BatGetUserHotPlaymateTagReq) String() string { return proto.CompactTextString(m) }
func (*BatGetUserHotPlaymateTagReq) ProtoMessage()    {}
func (*BatGetUserHotPlaymateTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{15}
}
func (m *BatGetUserHotPlaymateTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetUserHotPlaymateTagReq.Unmarshal(m, b)
}
func (m *BatGetUserHotPlaymateTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetUserHotPlaymateTagReq.Marshal(b, m, deterministic)
}
func (dst *BatGetUserHotPlaymateTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetUserHotPlaymateTagReq.Merge(dst, src)
}
func (m *BatGetUserHotPlaymateTagReq) XXX_Size() int {
	return xxx_messageInfo_BatGetUserHotPlaymateTagReq.Size(m)
}
func (m *BatGetUserHotPlaymateTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetUserHotPlaymateTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetUserHotPlaymateTagReq proto.InternalMessageInfo

func (m *BatGetUserHotPlaymateTagReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatGetUserHotPlaymateTagReq) GetReturnTagCount() uint32 {
	if m != nil {
		return m.ReturnTagCount
	}
	return 0
}

func (m *BatGetUserHotPlaymateTagReq) GetStatCount() uint32 {
	if m != nil {
		return m.StatCount
	}
	return 0
}

func (m *BatGetUserHotPlaymateTagReq) GetStatTagType() PlaymateTagType {
	if m != nil {
		return m.StatTagType
	}
	return PlaymateTagType_PlaymateTagType_UNDEFINED
}

type HotPlaymateTag struct {
	Id                   string          `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Content              string          `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Score                uint32          `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`
	TagType              PlaymateTagType `protobuf:"varint,4,opt,name=tag_type,json=tagType,proto3,enum=mystery_place.PlaymateTagType" json:"tag_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *HotPlaymateTag) Reset()         { *m = HotPlaymateTag{} }
func (m *HotPlaymateTag) String() string { return proto.CompactTextString(m) }
func (*HotPlaymateTag) ProtoMessage()    {}
func (*HotPlaymateTag) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{16}
}
func (m *HotPlaymateTag) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HotPlaymateTag.Unmarshal(m, b)
}
func (m *HotPlaymateTag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HotPlaymateTag.Marshal(b, m, deterministic)
}
func (dst *HotPlaymateTag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HotPlaymateTag.Merge(dst, src)
}
func (m *HotPlaymateTag) XXX_Size() int {
	return xxx_messageInfo_HotPlaymateTag.Size(m)
}
func (m *HotPlaymateTag) XXX_DiscardUnknown() {
	xxx_messageInfo_HotPlaymateTag.DiscardUnknown(m)
}

var xxx_messageInfo_HotPlaymateTag proto.InternalMessageInfo

func (m *HotPlaymateTag) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *HotPlaymateTag) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *HotPlaymateTag) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *HotPlaymateTag) GetTagType() PlaymateTagType {
	if m != nil {
		return m.TagType
	}
	return PlaymateTagType_PlaymateTagType_UNDEFINED
}

type UserHotPlaymateTag struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	HotTags              []*HotPlaymateTag `protobuf:"bytes,2,rep,name=hot_tags,json=hotTags,proto3" json:"hot_tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserHotPlaymateTag) Reset()         { *m = UserHotPlaymateTag{} }
func (m *UserHotPlaymateTag) String() string { return proto.CompactTextString(m) }
func (*UserHotPlaymateTag) ProtoMessage()    {}
func (*UserHotPlaymateTag) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{17}
}
func (m *UserHotPlaymateTag) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserHotPlaymateTag.Unmarshal(m, b)
}
func (m *UserHotPlaymateTag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserHotPlaymateTag.Marshal(b, m, deterministic)
}
func (dst *UserHotPlaymateTag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserHotPlaymateTag.Merge(dst, src)
}
func (m *UserHotPlaymateTag) XXX_Size() int {
	return xxx_messageInfo_UserHotPlaymateTag.Size(m)
}
func (m *UserHotPlaymateTag) XXX_DiscardUnknown() {
	xxx_messageInfo_UserHotPlaymateTag.DiscardUnknown(m)
}

var xxx_messageInfo_UserHotPlaymateTag proto.InternalMessageInfo

func (m *UserHotPlaymateTag) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserHotPlaymateTag) GetHotTags() []*HotPlaymateTag {
	if m != nil {
		return m.HotTags
	}
	return nil
}

type BatGetUserHotPlaymateTagResp struct {
	UserList             []*UserHotPlaymateTag `protobuf:"bytes,1,rep,name=user_list,json=userList,proto3" json:"user_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *BatGetUserHotPlaymateTagResp) Reset()         { *m = BatGetUserHotPlaymateTagResp{} }
func (m *BatGetUserHotPlaymateTagResp) String() string { return proto.CompactTextString(m) }
func (*BatGetUserHotPlaymateTagResp) ProtoMessage()    {}
func (*BatGetUserHotPlaymateTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{18}
}
func (m *BatGetUserHotPlaymateTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetUserHotPlaymateTagResp.Unmarshal(m, b)
}
func (m *BatGetUserHotPlaymateTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetUserHotPlaymateTagResp.Marshal(b, m, deterministic)
}
func (dst *BatGetUserHotPlaymateTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetUserHotPlaymateTagResp.Merge(dst, src)
}
func (m *BatGetUserHotPlaymateTagResp) XXX_Size() int {
	return xxx_messageInfo_BatGetUserHotPlaymateTagResp.Size(m)
}
func (m *BatGetUserHotPlaymateTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetUserHotPlaymateTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetUserHotPlaymateTagResp proto.InternalMessageInfo

func (m *BatGetUserHotPlaymateTagResp) GetUserList() []*UserHotPlaymateTag {
	if m != nil {
		return m.UserList
	}
	return nil
}

type SetScenarioShareLinkReq struct {
	ScenarioShareLink    *ScenarioShareLink `protobuf:"bytes,1,opt,name=scenario_share_link,json=scenarioShareLink,proto3" json:"scenario_share_link,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SetScenarioShareLinkReq) Reset()         { *m = SetScenarioShareLinkReq{} }
func (m *SetScenarioShareLinkReq) String() string { return proto.CompactTextString(m) }
func (*SetScenarioShareLinkReq) ProtoMessage()    {}
func (*SetScenarioShareLinkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{19}
}
func (m *SetScenarioShareLinkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetScenarioShareLinkReq.Unmarshal(m, b)
}
func (m *SetScenarioShareLinkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetScenarioShareLinkReq.Marshal(b, m, deterministic)
}
func (dst *SetScenarioShareLinkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetScenarioShareLinkReq.Merge(dst, src)
}
func (m *SetScenarioShareLinkReq) XXX_Size() int {
	return xxx_messageInfo_SetScenarioShareLinkReq.Size(m)
}
func (m *SetScenarioShareLinkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetScenarioShareLinkReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetScenarioShareLinkReq proto.InternalMessageInfo

func (m *SetScenarioShareLinkReq) GetScenarioShareLink() *ScenarioShareLink {
	if m != nil {
		return m.ScenarioShareLink
	}
	return nil
}

type SetScenarioShareLinkResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetScenarioShareLinkResp) Reset()         { *m = SetScenarioShareLinkResp{} }
func (m *SetScenarioShareLinkResp) String() string { return proto.CompactTextString(m) }
func (*SetScenarioShareLinkResp) ProtoMessage()    {}
func (*SetScenarioShareLinkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{20}
}
func (m *SetScenarioShareLinkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetScenarioShareLinkResp.Unmarshal(m, b)
}
func (m *SetScenarioShareLinkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetScenarioShareLinkResp.Marshal(b, m, deterministic)
}
func (dst *SetScenarioShareLinkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetScenarioShareLinkResp.Merge(dst, src)
}
func (m *SetScenarioShareLinkResp) XXX_Size() int {
	return xxx_messageInfo_SetScenarioShareLinkResp.Size(m)
}
func (m *SetScenarioShareLinkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetScenarioShareLinkResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetScenarioShareLinkResp proto.InternalMessageInfo

type BatchGetScenarioShareLinkReq struct {
	Ids                  []uint32 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetScenarioShareLinkReq) Reset()         { *m = BatchGetScenarioShareLinkReq{} }
func (m *BatchGetScenarioShareLinkReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetScenarioShareLinkReq) ProtoMessage()    {}
func (*BatchGetScenarioShareLinkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{21}
}
func (m *BatchGetScenarioShareLinkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetScenarioShareLinkReq.Unmarshal(m, b)
}
func (m *BatchGetScenarioShareLinkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetScenarioShareLinkReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetScenarioShareLinkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetScenarioShareLinkReq.Merge(dst, src)
}
func (m *BatchGetScenarioShareLinkReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetScenarioShareLinkReq.Size(m)
}
func (m *BatchGetScenarioShareLinkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetScenarioShareLinkReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetScenarioShareLinkReq proto.InternalMessageInfo

func (m *BatchGetScenarioShareLinkReq) GetIds() []uint32 {
	if m != nil {
		return m.Ids
	}
	return nil
}

type BatchGetScenarioShareLinkResp struct {
	ScenarioShareLink    []*ScenarioShareLink `protobuf:"bytes,1,rep,name=scenario_share_link,json=scenarioShareLink,proto3" json:"scenario_share_link,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatchGetScenarioShareLinkResp) Reset()         { *m = BatchGetScenarioShareLinkResp{} }
func (m *BatchGetScenarioShareLinkResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetScenarioShareLinkResp) ProtoMessage()    {}
func (*BatchGetScenarioShareLinkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{22}
}
func (m *BatchGetScenarioShareLinkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetScenarioShareLinkResp.Unmarshal(m, b)
}
func (m *BatchGetScenarioShareLinkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetScenarioShareLinkResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetScenarioShareLinkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetScenarioShareLinkResp.Merge(dst, src)
}
func (m *BatchGetScenarioShareLinkResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetScenarioShareLinkResp.Size(m)
}
func (m *BatchGetScenarioShareLinkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetScenarioShareLinkResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetScenarioShareLinkResp proto.InternalMessageInfo

func (m *BatchGetScenarioShareLinkResp) GetScenarioShareLink() []*ScenarioShareLink {
	if m != nil {
		return m.ScenarioShareLink
	}
	return nil
}

type DelScenarioShareLinkReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelScenarioShareLinkReq) Reset()         { *m = DelScenarioShareLinkReq{} }
func (m *DelScenarioShareLinkReq) String() string { return proto.CompactTextString(m) }
func (*DelScenarioShareLinkReq) ProtoMessage()    {}
func (*DelScenarioShareLinkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{23}
}
func (m *DelScenarioShareLinkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelScenarioShareLinkReq.Unmarshal(m, b)
}
func (m *DelScenarioShareLinkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelScenarioShareLinkReq.Marshal(b, m, deterministic)
}
func (dst *DelScenarioShareLinkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelScenarioShareLinkReq.Merge(dst, src)
}
func (m *DelScenarioShareLinkReq) XXX_Size() int {
	return xxx_messageInfo_DelScenarioShareLinkReq.Size(m)
}
func (m *DelScenarioShareLinkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelScenarioShareLinkReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelScenarioShareLinkReq proto.InternalMessageInfo

func (m *DelScenarioShareLinkReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelScenarioShareLinkResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelScenarioShareLinkResp) Reset()         { *m = DelScenarioShareLinkResp{} }
func (m *DelScenarioShareLinkResp) String() string { return proto.CompactTextString(m) }
func (*DelScenarioShareLinkResp) ProtoMessage()    {}
func (*DelScenarioShareLinkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{24}
}
func (m *DelScenarioShareLinkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelScenarioShareLinkResp.Unmarshal(m, b)
}
func (m *DelScenarioShareLinkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelScenarioShareLinkResp.Marshal(b, m, deterministic)
}
func (dst *DelScenarioShareLinkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelScenarioShareLinkResp.Merge(dst, src)
}
func (m *DelScenarioShareLinkResp) XXX_Size() int {
	return xxx_messageInfo_DelScenarioShareLinkResp.Size(m)
}
func (m *DelScenarioShareLinkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelScenarioShareLinkResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelScenarioShareLinkResp proto.InternalMessageInfo

type GetNewUsersReq struct {
	StartedAt            int64    `protobuf:"varint,1,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	EndedAt              int64    `protobuf:"varint,2,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewUsersReq) Reset()         { *m = GetNewUsersReq{} }
func (m *GetNewUsersReq) String() string { return proto.CompactTextString(m) }
func (*GetNewUsersReq) ProtoMessage()    {}
func (*GetNewUsersReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{25}
}
func (m *GetNewUsersReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewUsersReq.Unmarshal(m, b)
}
func (m *GetNewUsersReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewUsersReq.Marshal(b, m, deterministic)
}
func (dst *GetNewUsersReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewUsersReq.Merge(dst, src)
}
func (m *GetNewUsersReq) XXX_Size() int {
	return xxx_messageInfo_GetNewUsersReq.Size(m)
}
func (m *GetNewUsersReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewUsersReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewUsersReq proto.InternalMessageInfo

func (m *GetNewUsersReq) GetStartedAt() int64 {
	if m != nil {
		return m.StartedAt
	}
	return 0
}

func (m *GetNewUsersReq) GetEndedAt() int64 {
	if m != nil {
		return m.EndedAt
	}
	return 0
}

func (m *GetNewUsersReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetNewUsersReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetNewUsersResp struct {
	Users                []*User  `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewUsersResp) Reset()         { *m = GetNewUsersResp{} }
func (m *GetNewUsersResp) String() string { return proto.CompactTextString(m) }
func (*GetNewUsersResp) ProtoMessage()    {}
func (*GetNewUsersResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{26}
}
func (m *GetNewUsersResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewUsersResp.Unmarshal(m, b)
}
func (m *GetNewUsersResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewUsersResp.Marshal(b, m, deterministic)
}
func (dst *GetNewUsersResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewUsersResp.Merge(dst, src)
}
func (m *GetNewUsersResp) XXX_Size() int {
	return xxx_messageInfo_GetNewUsersResp.Size(m)
}
func (m *GetNewUsersResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewUsersResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewUsersResp proto.InternalMessageInfo

func (m *GetNewUsersResp) GetUsers() []*User {
	if m != nil {
		return m.Users
	}
	return nil
}

type User struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RegisteredAt         int64    `protobuf:"varint,2,opt,name=registered_at,json=registeredAt,proto3" json:"registered_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *User) Reset()         { *m = User{} }
func (m *User) String() string { return proto.CompactTextString(m) }
func (*User) ProtoMessage()    {}
func (*User) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{27}
}
func (m *User) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_User.Unmarshal(m, b)
}
func (m *User) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_User.Marshal(b, m, deterministic)
}
func (dst *User) XXX_Merge(src proto.Message) {
	xxx_messageInfo_User.Merge(dst, src)
}
func (m *User) XXX_Size() int {
	return xxx_messageInfo_User.Size(m)
}
func (m *User) XXX_DiscardUnknown() {
	xxx_messageInfo_User.DiscardUnknown(m)
}

var xxx_messageInfo_User proto.InternalMessageInfo

func (m *User) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *User) GetRegisteredAt() int64 {
	if m != nil {
		return m.RegisteredAt
	}
	return 0
}

type ScenarioShareLink struct {
	Id                   uint32        `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ExpId                uint32        `protobuf:"varint,2,opt,name=exp_id,json=expId,proto3" json:"exp_id,omitempty"`
	LayerTag             string        `protobuf:"bytes,3,opt,name=layer_tag,json=layerTag,proto3" json:"layer_tag,omitempty"`
	AbTestLink           []*ABTestLink `protobuf:"bytes,4,rep,name=ab_test_link,json=abTestLink,proto3" json:"ab_test_link,omitempty"`
	Link                 string        `protobuf:"bytes,5,opt,name=link,proto3" json:"link,omitempty"`
	UpdateTime           int64         `protobuf:"varint,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	SharePicture         string        `protobuf:"bytes,7,opt,name=share_picture,json=sharePicture,proto3" json:"share_picture,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ScenarioShareLink) Reset()         { *m = ScenarioShareLink{} }
func (m *ScenarioShareLink) String() string { return proto.CompactTextString(m) }
func (*ScenarioShareLink) ProtoMessage()    {}
func (*ScenarioShareLink) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{28}
}
func (m *ScenarioShareLink) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScenarioShareLink.Unmarshal(m, b)
}
func (m *ScenarioShareLink) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScenarioShareLink.Marshal(b, m, deterministic)
}
func (dst *ScenarioShareLink) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScenarioShareLink.Merge(dst, src)
}
func (m *ScenarioShareLink) XXX_Size() int {
	return xxx_messageInfo_ScenarioShareLink.Size(m)
}
func (m *ScenarioShareLink) XXX_DiscardUnknown() {
	xxx_messageInfo_ScenarioShareLink.DiscardUnknown(m)
}

var xxx_messageInfo_ScenarioShareLink proto.InternalMessageInfo

func (m *ScenarioShareLink) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ScenarioShareLink) GetExpId() uint32 {
	if m != nil {
		return m.ExpId
	}
	return 0
}

func (m *ScenarioShareLink) GetLayerTag() string {
	if m != nil {
		return m.LayerTag
	}
	return ""
}

func (m *ScenarioShareLink) GetAbTestLink() []*ABTestLink {
	if m != nil {
		return m.AbTestLink
	}
	return nil
}

func (m *ScenarioShareLink) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

func (m *ScenarioShareLink) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *ScenarioShareLink) GetSharePicture() string {
	if m != nil {
		return m.SharePicture
	}
	return ""
}

type ABTestLink struct {
	ExpTvId              uint32   `protobuf:"varint,1,opt,name=exp_tv_id,json=expTvId,proto3" json:"exp_tv_id,omitempty"`
	Link                 string   `protobuf:"bytes,2,opt,name=link,proto3" json:"link,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ABTestLink) Reset()         { *m = ABTestLink{} }
func (m *ABTestLink) String() string { return proto.CompactTextString(m) }
func (*ABTestLink) ProtoMessage()    {}
func (*ABTestLink) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{29}
}
func (m *ABTestLink) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ABTestLink.Unmarshal(m, b)
}
func (m *ABTestLink) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ABTestLink.Marshal(b, m, deterministic)
}
func (dst *ABTestLink) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ABTestLink.Merge(dst, src)
}
func (m *ABTestLink) XXX_Size() int {
	return xxx_messageInfo_ABTestLink.Size(m)
}
func (m *ABTestLink) XXX_DiscardUnknown() {
	xxx_messageInfo_ABTestLink.DiscardUnknown(m)
}

var xxx_messageInfo_ABTestLink proto.InternalMessageInfo

func (m *ABTestLink) GetExpTvId() uint32 {
	if m != nil {
		return m.ExpTvId
	}
	return 0
}

func (m *ABTestLink) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

// 剧本提权信息
type ScenarioIncWeightInfo struct {
	IsIncrease           bool                               `protobuf:"varint,1,opt,name=is_increase,json=isIncrease,proto3" json:"is_increase,omitempty"`
	Weight               uint32                             `protobuf:"varint,2,opt,name=weight,proto3" json:"weight,omitempty"`
	StartTime            uint32                             `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32                             `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Status               ScenarioIncWeightInfo_Weightstatus `protobuf:"varint,5,opt,name=status,proto3,enum=mystery_place.ScenarioIncWeightInfo_Weightstatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *ScenarioIncWeightInfo) Reset()         { *m = ScenarioIncWeightInfo{} }
func (m *ScenarioIncWeightInfo) String() string { return proto.CompactTextString(m) }
func (*ScenarioIncWeightInfo) ProtoMessage()    {}
func (*ScenarioIncWeightInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{30}
}
func (m *ScenarioIncWeightInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScenarioIncWeightInfo.Unmarshal(m, b)
}
func (m *ScenarioIncWeightInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScenarioIncWeightInfo.Marshal(b, m, deterministic)
}
func (dst *ScenarioIncWeightInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScenarioIncWeightInfo.Merge(dst, src)
}
func (m *ScenarioIncWeightInfo) XXX_Size() int {
	return xxx_messageInfo_ScenarioIncWeightInfo.Size(m)
}
func (m *ScenarioIncWeightInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ScenarioIncWeightInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ScenarioIncWeightInfo proto.InternalMessageInfo

func (m *ScenarioIncWeightInfo) GetIsIncrease() bool {
	if m != nil {
		return m.IsIncrease
	}
	return false
}

func (m *ScenarioIncWeightInfo) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *ScenarioIncWeightInfo) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *ScenarioIncWeightInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ScenarioIncWeightInfo) GetStatus() ScenarioIncWeightInfo_Weightstatus {
	if m != nil {
		return m.Status
	}
	return ScenarioIncWeightInfo_WEIGHTSTATUS_UNSPECIFIED
}

type ScenarioInfo struct {
	Id                       uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	PlayMode                 PlayMode               `protobuf:"varint,2,opt,name=play_mode,json=playMode,proto3,enum=mystery_place.PlayMode" json:"play_mode,omitempty"`
	TabId                    uint32                 `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName                  string                 `protobuf:"bytes,4,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	Link                     string                 `protobuf:"bytes,5,opt,name=link,proto3" json:"link,omitempty"`
	Introduction             string                 `protobuf:"bytes,6,opt,name=introduction,proto3" json:"introduction,omitempty"`
	Labels                   []string               `protobuf:"bytes,7,rep,name=labels,proto3" json:"labels,omitempty"`
	NewDisplayPicture        string                 `protobuf:"bytes,8,opt,name=new_display_picture,json=newDisplayPicture,proto3" json:"new_display_picture,omitempty"`
	OldDisplayPicture        string                 `protobuf:"bytes,9,opt,name=old_display_picture,json=oldDisplayPicture,proto3" json:"old_display_picture,omitempty"`
	BigSharePicture          string                 `protobuf:"bytes,10,opt,name=big_share_picture,json=bigSharePicture,proto3" json:"big_share_picture,omitempty"`
	SmallSharePicture        string                 `protobuf:"bytes,11,opt,name=small_share_picture,json=smallSharePicture,proto3" json:"small_share_picture,omitempty"`
	Video                    string                 `protobuf:"bytes,12,opt,name=video,proto3" json:"video,omitempty"`
	LabelsColor              string                 `protobuf:"bytes,13,opt,name=labels_color,json=labelsColor,proto3" json:"labels_color,omitempty"`
	UpdateAt                 int64                  `protobuf:"varint,14,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	Title                    string                 `protobuf:"bytes,15,opt,name=title,proto3" json:"title,omitempty"`
	TabInfos                 []*ScenarioTabInfo     `protobuf:"bytes,16,rep,name=tab_infos,json=tabInfos,proto3" json:"tab_infos,omitempty"`
	IsRecommend              bool                   `protobuf:"varint,17,opt,name=is_recommend,json=isRecommend,proto3" json:"is_recommend,omitempty"`
	Priority                 uint32                 `protobuf:"varint,18,opt,name=priority,proto3" json:"priority,omitempty"`
	IosLowerVersionLimit     string                 `protobuf:"bytes,19,opt,name=ios_lower_version_limit,json=iosLowerVersionLimit,proto3" json:"ios_lower_version_limit,omitempty"`
	AndroidLowerVersionLimit string                 `protobuf:"bytes,20,opt,name=android_lower_version_limit,json=androidLowerVersionLimit,proto3" json:"android_lower_version_limit,omitempty"`
	Barrage                  []string               `protobuf:"bytes,21,rep,name=barrage,proto3" json:"barrage,omitempty"`
	RecommendVideo           string                 `protobuf:"bytes,22,opt,name=recommend_video,json=recommendVideo,proto3" json:"recommend_video,omitempty"`
	RecommendPicture         []string               `protobuf:"bytes,23,rep,name=recommend_picture,json=recommendPicture,proto3" json:"recommend_picture,omitempty"`
	ScenarioSmallPicture     string                 `protobuf:"bytes,24,opt,name=scenario_small_picture,json=scenarioSmallPicture,proto3" json:"scenario_small_picture,omitempty"`
	EstimateFinishTime       string                 `protobuf:"bytes,25,opt,name=estimate_finish_time,json=estimateFinishTime,proto3" json:"estimate_finish_time,omitempty"`
	AvatarToast              []string               `protobuf:"bytes,26,rep,name=avatar_toast,json=avatarToast,proto3" json:"avatar_toast,omitempty"`
	IsReservation            bool                   `protobuf:"varint,27,opt,name=is_reservation,json=isReservation,proto3" json:"is_reservation,omitempty"`
	PiecingGroupBg           string                 `protobuf:"bytes,28,opt,name=piecing_group_bg,json=piecingGroupBg,proto3" json:"piecing_group_bg,omitempty"`
	NewScenarioSmallPicture  string                 `protobuf:"bytes,29,opt,name=new_scenario_small_picture,json=newScenarioSmallPicture,proto3" json:"new_scenario_small_picture,omitempty"`
	NewRecommendPicture      string                 `protobuf:"bytes,30,opt,name=new_recommend_picture,json=newRecommendPicture,proto3" json:"new_recommend_picture,omitempty"`
	TagType                  ScenarioTagType        `protobuf:"varint,31,opt,name=tag_type,json=tagType,proto3,enum=mystery_place.ScenarioTagType" json:"tag_type,omitempty"`
	MinPlayerNum             uint32                 `protobuf:"varint,32,opt,name=min_player_num,json=minPlayerNum,proto3" json:"min_player_num,omitempty"`
	GameDifficulty           string                 `protobuf:"bytes,33,opt,name=game_difficulty,json=gameDifficulty,proto3" json:"game_difficulty,omitempty"`
	RolePicture              string                 `protobuf:"bytes,34,opt,name=role_picture,json=rolePicture,proto3" json:"role_picture,omitempty"`
	NewerPriority            uint32                 `protobuf:"varint,35,opt,name=newer_priority,json=newerPriority,proto3" json:"newer_priority,omitempty"`
	ShortName                string                 `protobuf:"bytes,36,opt,name=short_name,json=shortName,proto3" json:"short_name,omitempty"`
	CharacterInfo            []*CharacterInfo       `protobuf:"bytes,37,rep,name=character_info,json=characterInfo,proto3" json:"character_info,omitempty"`
	IsShowPrice              bool                   `protobuf:"varint,38,opt,name=is_show_price,json=isShowPrice,proto3" json:"is_show_price,omitempty"`
	PriceNum                 uint32                 `protobuf:"varint,39,opt,name=price_num,json=priceNum,proto3" json:"price_num,omitempty"`
	ListingTime              int64                  `protobuf:"varint,40,opt,name=listing_time,json=listingTime,proto3" json:"listing_time,omitempty"`
	IsTrial                  bool                   `protobuf:"varint,41,opt,name=is_trial,json=isTrial,proto3" json:"is_trial,omitempty"`
	BindTopicIds             []string               `protobuf:"bytes,42,rep,name=bind_topic_ids,json=bindTopicIds,proto3" json:"bind_topic_ids,omitempty"`
	Score                    float32                `protobuf:"fixed32,43,opt,name=score,proto3" json:"score,omitempty"`
	TvPicture                string                 `protobuf:"bytes,44,opt,name=tv_picture,json=tvPicture,proto3" json:"tv_picture,omitempty"`
	WeightInfo               *ScenarioIncWeightInfo `protobuf:"bytes,45,opt,name=weight_info,json=weightInfo,proto3" json:"weight_info,omitempty"`
	GameOrientation          GameOrientation        `protobuf:"varint,46,opt,name=game_orientation,json=gameOrientation,proto3,enum=mystery_place.GameOrientation" json:"game_orientation,omitempty"`
	InvitationActivity       string                 `protobuf:"bytes,47,opt,name=invitation_activity,json=invitationActivity,proto3" json:"invitation_activity,omitempty"`
	XXX_NoUnkeyedLiteral     struct{}               `json:"-"`
	XXX_unrecognized         []byte                 `json:"-"`
	XXX_sizecache            int32                  `json:"-"`
}

func (m *ScenarioInfo) Reset()         { *m = ScenarioInfo{} }
func (m *ScenarioInfo) String() string { return proto.CompactTextString(m) }
func (*ScenarioInfo) ProtoMessage()    {}
func (*ScenarioInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{31}
}
func (m *ScenarioInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScenarioInfo.Unmarshal(m, b)
}
func (m *ScenarioInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScenarioInfo.Marshal(b, m, deterministic)
}
func (dst *ScenarioInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScenarioInfo.Merge(dst, src)
}
func (m *ScenarioInfo) XXX_Size() int {
	return xxx_messageInfo_ScenarioInfo.Size(m)
}
func (m *ScenarioInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ScenarioInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ScenarioInfo proto.InternalMessageInfo

func (m *ScenarioInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ScenarioInfo) GetPlayMode() PlayMode {
	if m != nil {
		return m.PlayMode
	}
	return PlayMode_None
}

func (m *ScenarioInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ScenarioInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *ScenarioInfo) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

func (m *ScenarioInfo) GetIntroduction() string {
	if m != nil {
		return m.Introduction
	}
	return ""
}

func (m *ScenarioInfo) GetLabels() []string {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *ScenarioInfo) GetNewDisplayPicture() string {
	if m != nil {
		return m.NewDisplayPicture
	}
	return ""
}

func (m *ScenarioInfo) GetOldDisplayPicture() string {
	if m != nil {
		return m.OldDisplayPicture
	}
	return ""
}

func (m *ScenarioInfo) GetBigSharePicture() string {
	if m != nil {
		return m.BigSharePicture
	}
	return ""
}

func (m *ScenarioInfo) GetSmallSharePicture() string {
	if m != nil {
		return m.SmallSharePicture
	}
	return ""
}

func (m *ScenarioInfo) GetVideo() string {
	if m != nil {
		return m.Video
	}
	return ""
}

func (m *ScenarioInfo) GetLabelsColor() string {
	if m != nil {
		return m.LabelsColor
	}
	return ""
}

func (m *ScenarioInfo) GetUpdateAt() int64 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

func (m *ScenarioInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ScenarioInfo) GetTabInfos() []*ScenarioTabInfo {
	if m != nil {
		return m.TabInfos
	}
	return nil
}

func (m *ScenarioInfo) GetIsRecommend() bool {
	if m != nil {
		return m.IsRecommend
	}
	return false
}

func (m *ScenarioInfo) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func (m *ScenarioInfo) GetIosLowerVersionLimit() string {
	if m != nil {
		return m.IosLowerVersionLimit
	}
	return ""
}

func (m *ScenarioInfo) GetAndroidLowerVersionLimit() string {
	if m != nil {
		return m.AndroidLowerVersionLimit
	}
	return ""
}

func (m *ScenarioInfo) GetBarrage() []string {
	if m != nil {
		return m.Barrage
	}
	return nil
}

func (m *ScenarioInfo) GetRecommendVideo() string {
	if m != nil {
		return m.RecommendVideo
	}
	return ""
}

func (m *ScenarioInfo) GetRecommendPicture() []string {
	if m != nil {
		return m.RecommendPicture
	}
	return nil
}

func (m *ScenarioInfo) GetScenarioSmallPicture() string {
	if m != nil {
		return m.ScenarioSmallPicture
	}
	return ""
}

func (m *ScenarioInfo) GetEstimateFinishTime() string {
	if m != nil {
		return m.EstimateFinishTime
	}
	return ""
}

func (m *ScenarioInfo) GetAvatarToast() []string {
	if m != nil {
		return m.AvatarToast
	}
	return nil
}

func (m *ScenarioInfo) GetIsReservation() bool {
	if m != nil {
		return m.IsReservation
	}
	return false
}

func (m *ScenarioInfo) GetPiecingGroupBg() string {
	if m != nil {
		return m.PiecingGroupBg
	}
	return ""
}

func (m *ScenarioInfo) GetNewScenarioSmallPicture() string {
	if m != nil {
		return m.NewScenarioSmallPicture
	}
	return ""
}

func (m *ScenarioInfo) GetNewRecommendPicture() string {
	if m != nil {
		return m.NewRecommendPicture
	}
	return ""
}

func (m *ScenarioInfo) GetTagType() ScenarioTagType {
	if m != nil {
		return m.TagType
	}
	return ScenarioTagType_ScenarioTagType_UNDEFINED
}

func (m *ScenarioInfo) GetMinPlayerNum() uint32 {
	if m != nil {
		return m.MinPlayerNum
	}
	return 0
}

func (m *ScenarioInfo) GetGameDifficulty() string {
	if m != nil {
		return m.GameDifficulty
	}
	return ""
}

func (m *ScenarioInfo) GetRolePicture() string {
	if m != nil {
		return m.RolePicture
	}
	return ""
}

func (m *ScenarioInfo) GetNewerPriority() uint32 {
	if m != nil {
		return m.NewerPriority
	}
	return 0
}

func (m *ScenarioInfo) GetShortName() string {
	if m != nil {
		return m.ShortName
	}
	return ""
}

func (m *ScenarioInfo) GetCharacterInfo() []*CharacterInfo {
	if m != nil {
		return m.CharacterInfo
	}
	return nil
}

func (m *ScenarioInfo) GetIsShowPrice() bool {
	if m != nil {
		return m.IsShowPrice
	}
	return false
}

func (m *ScenarioInfo) GetPriceNum() uint32 {
	if m != nil {
		return m.PriceNum
	}
	return 0
}

func (m *ScenarioInfo) GetListingTime() int64 {
	if m != nil {
		return m.ListingTime
	}
	return 0
}

func (m *ScenarioInfo) GetIsTrial() bool {
	if m != nil {
		return m.IsTrial
	}
	return false
}

func (m *ScenarioInfo) GetBindTopicIds() []string {
	if m != nil {
		return m.BindTopicIds
	}
	return nil
}

func (m *ScenarioInfo) GetScore() float32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ScenarioInfo) GetTvPicture() string {
	if m != nil {
		return m.TvPicture
	}
	return ""
}

func (m *ScenarioInfo) GetWeightInfo() *ScenarioIncWeightInfo {
	if m != nil {
		return m.WeightInfo
	}
	return nil
}

func (m *ScenarioInfo) GetGameOrientation() GameOrientation {
	if m != nil {
		return m.GameOrientation
	}
	return GameOrientation_GAME_ORIENTATION_UNDEFINED
}

func (m *ScenarioInfo) GetInvitationActivity() string {
	if m != nil {
		return m.InvitationActivity
	}
	return ""
}

type ScenarioTabInfo struct {
	PlayMode             PlayMode          `protobuf:"varint,2,opt,name=play_mode,json=playMode,proto3,enum=mystery_place.PlayMode" json:"play_mode,omitempty"`
	TabId                uint32            `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Icon                 string            `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	TabStatus            ScenarioTabStatus `protobuf:"varint,5,opt,name=tab_status,json=tabStatus,proto3,enum=mystery_place.ScenarioTabStatus" json:"tab_status,omitempty"`
	ChapterNum           uint32            `protobuf:"varint,6,opt,name=chapter_num,json=chapterNum,proto3" json:"chapter_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ScenarioTabInfo) Reset()         { *m = ScenarioTabInfo{} }
func (m *ScenarioTabInfo) String() string { return proto.CompactTextString(m) }
func (*ScenarioTabInfo) ProtoMessage()    {}
func (*ScenarioTabInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{32}
}
func (m *ScenarioTabInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScenarioTabInfo.Unmarshal(m, b)
}
func (m *ScenarioTabInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScenarioTabInfo.Marshal(b, m, deterministic)
}
func (dst *ScenarioTabInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScenarioTabInfo.Merge(dst, src)
}
func (m *ScenarioTabInfo) XXX_Size() int {
	return xxx_messageInfo_ScenarioTabInfo.Size(m)
}
func (m *ScenarioTabInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ScenarioTabInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ScenarioTabInfo proto.InternalMessageInfo

func (m *ScenarioTabInfo) GetPlayMode() PlayMode {
	if m != nil {
		return m.PlayMode
	}
	return PlayMode_None
}

func (m *ScenarioTabInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ScenarioTabInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *ScenarioTabInfo) GetTabStatus() ScenarioTabStatus {
	if m != nil {
		return m.TabStatus
	}
	return ScenarioTabStatus_ScenarioTabStatus_UNDEFINED
}

func (m *ScenarioTabInfo) GetChapterNum() uint32 {
	if m != nil {
		return m.ChapterNum
	}
	return 0
}

// 角色介绍
type CharacterInfo struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Img                  string   `protobuf:"bytes,2,opt,name=img,proto3" json:"img,omitempty"`
	Info                 string   `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CharacterInfo) Reset()         { *m = CharacterInfo{} }
func (m *CharacterInfo) String() string { return proto.CompactTextString(m) }
func (*CharacterInfo) ProtoMessage()    {}
func (*CharacterInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{33}
}
func (m *CharacterInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CharacterInfo.Unmarshal(m, b)
}
func (m *CharacterInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CharacterInfo.Marshal(b, m, deterministic)
}
func (dst *CharacterInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CharacterInfo.Merge(dst, src)
}
func (m *CharacterInfo) XXX_Size() int {
	return xxx_messageInfo_CharacterInfo.Size(m)
}
func (m *CharacterInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CharacterInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CharacterInfo proto.InternalMessageInfo

func (m *CharacterInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CharacterInfo) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *CharacterInfo) GetInfo() string {
	if m != nil {
		return m.Info
	}
	return ""
}

// 获取列表接口
type ListScenarioInfoReq struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	AndroidVersion       string   `protobuf:"bytes,3,opt,name=android_version,json=androidVersion,proto3" json:"android_version,omitempty"`
	IosVersion           string   `protobuf:"bytes,4,opt,name=ios_version,json=iosVersion,proto3" json:"ios_version,omitempty"`
	SortType             SortType `protobuf:"varint,5,opt,name=sort_type,json=sortType,proto3,enum=mystery_place.SortType" json:"sort_type,omitempty"`
	ListingTime          int64    `protobuf:"varint,6,opt,name=listing_time,json=listingTime,proto3" json:"listing_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListScenarioInfoReq) Reset()         { *m = ListScenarioInfoReq{} }
func (m *ListScenarioInfoReq) String() string { return proto.CompactTextString(m) }
func (*ListScenarioInfoReq) ProtoMessage()    {}
func (*ListScenarioInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{34}
}
func (m *ListScenarioInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListScenarioInfoReq.Unmarshal(m, b)
}
func (m *ListScenarioInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListScenarioInfoReq.Marshal(b, m, deterministic)
}
func (dst *ListScenarioInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListScenarioInfoReq.Merge(dst, src)
}
func (m *ListScenarioInfoReq) XXX_Size() int {
	return xxx_messageInfo_ListScenarioInfoReq.Size(m)
}
func (m *ListScenarioInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListScenarioInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListScenarioInfoReq proto.InternalMessageInfo

func (m *ListScenarioInfoReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ListScenarioInfoReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *ListScenarioInfoReq) GetAndroidVersion() string {
	if m != nil {
		return m.AndroidVersion
	}
	return ""
}

func (m *ListScenarioInfoReq) GetIosVersion() string {
	if m != nil {
		return m.IosVersion
	}
	return ""
}

func (m *ListScenarioInfoReq) GetSortType() SortType {
	if m != nil {
		return m.SortType
	}
	return SortType_OLD_USER_SORT
}

func (m *ListScenarioInfoReq) GetListingTime() int64 {
	if m != nil {
		return m.ListingTime
	}
	return 0
}

type ListScenarioInfoResp struct {
	Infos                []*ScenarioInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListScenarioInfoResp) Reset()         { *m = ListScenarioInfoResp{} }
func (m *ListScenarioInfoResp) String() string { return proto.CompactTextString(m) }
func (*ListScenarioInfoResp) ProtoMessage()    {}
func (*ListScenarioInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{35}
}
func (m *ListScenarioInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListScenarioInfoResp.Unmarshal(m, b)
}
func (m *ListScenarioInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListScenarioInfoResp.Marshal(b, m, deterministic)
}
func (dst *ListScenarioInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListScenarioInfoResp.Merge(dst, src)
}
func (m *ListScenarioInfoResp) XXX_Size() int {
	return xxx_messageInfo_ListScenarioInfoResp.Size(m)
}
func (m *ListScenarioInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListScenarioInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListScenarioInfoResp proto.InternalMessageInfo

func (m *ListScenarioInfoResp) GetInfos() []*ScenarioInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

// 获取可见剧本总数
type GetScenarioInfoTotalCountReq struct {
	AndroidVersion       string   `protobuf:"bytes,3,opt,name=android_version,json=androidVersion,proto3" json:"android_version,omitempty"`
	IosVersion           string   `protobuf:"bytes,4,opt,name=ios_version,json=iosVersion,proto3" json:"ios_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScenarioInfoTotalCountReq) Reset()         { *m = GetScenarioInfoTotalCountReq{} }
func (m *GetScenarioInfoTotalCountReq) String() string { return proto.CompactTextString(m) }
func (*GetScenarioInfoTotalCountReq) ProtoMessage()    {}
func (*GetScenarioInfoTotalCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{36}
}
func (m *GetScenarioInfoTotalCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenarioInfoTotalCountReq.Unmarshal(m, b)
}
func (m *GetScenarioInfoTotalCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenarioInfoTotalCountReq.Marshal(b, m, deterministic)
}
func (dst *GetScenarioInfoTotalCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenarioInfoTotalCountReq.Merge(dst, src)
}
func (m *GetScenarioInfoTotalCountReq) XXX_Size() int {
	return xxx_messageInfo_GetScenarioInfoTotalCountReq.Size(m)
}
func (m *GetScenarioInfoTotalCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenarioInfoTotalCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenarioInfoTotalCountReq proto.InternalMessageInfo

func (m *GetScenarioInfoTotalCountReq) GetAndroidVersion() string {
	if m != nil {
		return m.AndroidVersion
	}
	return ""
}

func (m *GetScenarioInfoTotalCountReq) GetIosVersion() string {
	if m != nil {
		return m.IosVersion
	}
	return ""
}

type GetScenarioInfoTotalCountResp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScenarioInfoTotalCountResp) Reset()         { *m = GetScenarioInfoTotalCountResp{} }
func (m *GetScenarioInfoTotalCountResp) String() string { return proto.CompactTextString(m) }
func (*GetScenarioInfoTotalCountResp) ProtoMessage()    {}
func (*GetScenarioInfoTotalCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{37}
}
func (m *GetScenarioInfoTotalCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenarioInfoTotalCountResp.Unmarshal(m, b)
}
func (m *GetScenarioInfoTotalCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenarioInfoTotalCountResp.Marshal(b, m, deterministic)
}
func (dst *GetScenarioInfoTotalCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenarioInfoTotalCountResp.Merge(dst, src)
}
func (m *GetScenarioInfoTotalCountResp) XXX_Size() int {
	return xxx_messageInfo_GetScenarioInfoTotalCountResp.Size(m)
}
func (m *GetScenarioInfoTotalCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenarioInfoTotalCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenarioInfoTotalCountResp proto.InternalMessageInfo

func (m *GetScenarioInfoTotalCountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

// 详情页面
type GetScenarioInfoReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScenarioInfoReq) Reset()         { *m = GetScenarioInfoReq{} }
func (m *GetScenarioInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetScenarioInfoReq) ProtoMessage()    {}
func (*GetScenarioInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{38}
}
func (m *GetScenarioInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenarioInfoReq.Unmarshal(m, b)
}
func (m *GetScenarioInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenarioInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetScenarioInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenarioInfoReq.Merge(dst, src)
}
func (m *GetScenarioInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetScenarioInfoReq.Size(m)
}
func (m *GetScenarioInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenarioInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenarioInfoReq proto.InternalMessageInfo

func (m *GetScenarioInfoReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetScenarioInfoResp struct {
	Info                 *ScenarioInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetScenarioInfoResp) Reset()         { *m = GetScenarioInfoResp{} }
func (m *GetScenarioInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetScenarioInfoResp) ProtoMessage()    {}
func (*GetScenarioInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{39}
}
func (m *GetScenarioInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenarioInfoResp.Unmarshal(m, b)
}
func (m *GetScenarioInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenarioInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetScenarioInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenarioInfoResp.Merge(dst, src)
}
func (m *GetScenarioInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetScenarioInfoResp.Size(m)
}
func (m *GetScenarioInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenarioInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenarioInfoResp proto.InternalMessageInfo

func (m *GetScenarioInfoResp) GetInfo() *ScenarioInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type DelScenarioInfoReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelScenarioInfoReq) Reset()         { *m = DelScenarioInfoReq{} }
func (m *DelScenarioInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelScenarioInfoReq) ProtoMessage()    {}
func (*DelScenarioInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{40}
}
func (m *DelScenarioInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelScenarioInfoReq.Unmarshal(m, b)
}
func (m *DelScenarioInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelScenarioInfoReq.Marshal(b, m, deterministic)
}
func (dst *DelScenarioInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelScenarioInfoReq.Merge(dst, src)
}
func (m *DelScenarioInfoReq) XXX_Size() int {
	return xxx_messageInfo_DelScenarioInfoReq.Size(m)
}
func (m *DelScenarioInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelScenarioInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelScenarioInfoReq proto.InternalMessageInfo

func (m *DelScenarioInfoReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelScenarioInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelScenarioInfoResp) Reset()         { *m = DelScenarioInfoResp{} }
func (m *DelScenarioInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelScenarioInfoResp) ProtoMessage()    {}
func (*DelScenarioInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{41}
}
func (m *DelScenarioInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelScenarioInfoResp.Unmarshal(m, b)
}
func (m *DelScenarioInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelScenarioInfoResp.Marshal(b, m, deterministic)
}
func (dst *DelScenarioInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelScenarioInfoResp.Merge(dst, src)
}
func (m *DelScenarioInfoResp) XXX_Size() int {
	return xxx_messageInfo_DelScenarioInfoResp.Size(m)
}
func (m *DelScenarioInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelScenarioInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelScenarioInfoResp proto.InternalMessageInfo

type UpsertScenarioInfoReq struct {
	Info                 *ScenarioInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpsertScenarioInfoReq) Reset()         { *m = UpsertScenarioInfoReq{} }
func (m *UpsertScenarioInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpsertScenarioInfoReq) ProtoMessage()    {}
func (*UpsertScenarioInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{42}
}
func (m *UpsertScenarioInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertScenarioInfoReq.Unmarshal(m, b)
}
func (m *UpsertScenarioInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertScenarioInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpsertScenarioInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertScenarioInfoReq.Merge(dst, src)
}
func (m *UpsertScenarioInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpsertScenarioInfoReq.Size(m)
}
func (m *UpsertScenarioInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertScenarioInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertScenarioInfoReq proto.InternalMessageInfo

func (m *UpsertScenarioInfoReq) GetInfo() *ScenarioInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type UpsertScenarioInfoResp struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertScenarioInfoResp) Reset()         { *m = UpsertScenarioInfoResp{} }
func (m *UpsertScenarioInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpsertScenarioInfoResp) ProtoMessage()    {}
func (*UpsertScenarioInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{43}
}
func (m *UpsertScenarioInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertScenarioInfoResp.Unmarshal(m, b)
}
func (m *UpsertScenarioInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertScenarioInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpsertScenarioInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertScenarioInfoResp.Merge(dst, src)
}
func (m *UpsertScenarioInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpsertScenarioInfoResp.Size(m)
}
func (m *UpsertScenarioInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertScenarioInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertScenarioInfoResp proto.InternalMessageInfo

func (m *UpsertScenarioInfoResp) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 判断新老用户
type IsNewUserReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsNewUserReq) Reset()         { *m = IsNewUserReq{} }
func (m *IsNewUserReq) String() string { return proto.CompactTextString(m) }
func (*IsNewUserReq) ProtoMessage()    {}
func (*IsNewUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{44}
}
func (m *IsNewUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsNewUserReq.Unmarshal(m, b)
}
func (m *IsNewUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsNewUserReq.Marshal(b, m, deterministic)
}
func (dst *IsNewUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsNewUserReq.Merge(dst, src)
}
func (m *IsNewUserReq) XXX_Size() int {
	return xxx_messageInfo_IsNewUserReq.Size(m)
}
func (m *IsNewUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsNewUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsNewUserReq proto.InternalMessageInfo

func (m *IsNewUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type IsNewUserResp struct {
	IsNew                bool     `protobuf:"varint,1,opt,name=is_new,json=isNew,proto3" json:"is_new,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsNewUserResp) Reset()         { *m = IsNewUserResp{} }
func (m *IsNewUserResp) String() string { return proto.CompactTextString(m) }
func (*IsNewUserResp) ProtoMessage()    {}
func (*IsNewUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{45}
}
func (m *IsNewUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsNewUserResp.Unmarshal(m, b)
}
func (m *IsNewUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsNewUserResp.Marshal(b, m, deterministic)
}
func (dst *IsNewUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsNewUserResp.Merge(dst, src)
}
func (m *IsNewUserResp) XXX_Size() int {
	return xxx_messageInfo_IsNewUserResp.Size(m)
}
func (m *IsNewUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsNewUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsNewUserResp proto.InternalMessageInfo

func (m *IsNewUserResp) GetIsNew() bool {
	if m != nil {
		return m.IsNew
	}
	return false
}

type BatchGetGamePercentByChannelIdsReq struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetGamePercentByChannelIdsReq) Reset()         { *m = BatchGetGamePercentByChannelIdsReq{} }
func (m *BatchGetGamePercentByChannelIdsReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetGamePercentByChannelIdsReq) ProtoMessage()    {}
func (*BatchGetGamePercentByChannelIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{46}
}
func (m *BatchGetGamePercentByChannelIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGamePercentByChannelIdsReq.Unmarshal(m, b)
}
func (m *BatchGetGamePercentByChannelIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGamePercentByChannelIdsReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetGamePercentByChannelIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGamePercentByChannelIdsReq.Merge(dst, src)
}
func (m *BatchGetGamePercentByChannelIdsReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetGamePercentByChannelIdsReq.Size(m)
}
func (m *BatchGetGamePercentByChannelIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGamePercentByChannelIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGamePercentByChannelIdsReq proto.InternalMessageInfo

func (m *BatchGetGamePercentByChannelIdsReq) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type BatchGetGamePercentByChannelIdsResp struct {
	GamePercents         []*RoomGamePercent `protobuf:"bytes,1,rep,name=game_percents,json=gamePercents,proto3" json:"game_percents,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchGetGamePercentByChannelIdsResp) Reset()         { *m = BatchGetGamePercentByChannelIdsResp{} }
func (m *BatchGetGamePercentByChannelIdsResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetGamePercentByChannelIdsResp) ProtoMessage()    {}
func (*BatchGetGamePercentByChannelIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{47}
}
func (m *BatchGetGamePercentByChannelIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGamePercentByChannelIdsResp.Unmarshal(m, b)
}
func (m *BatchGetGamePercentByChannelIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGamePercentByChannelIdsResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetGamePercentByChannelIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGamePercentByChannelIdsResp.Merge(dst, src)
}
func (m *BatchGetGamePercentByChannelIdsResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetGamePercentByChannelIdsResp.Size(m)
}
func (m *BatchGetGamePercentByChannelIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGamePercentByChannelIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGamePercentByChannelIdsResp proto.InternalMessageInfo

func (m *BatchGetGamePercentByChannelIdsResp) GetGamePercents() []*RoomGamePercent {
	if m != nil {
		return m.GamePercents
	}
	return nil
}

type GetPlayerLastRecordReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlayerLastRecordReq) Reset()         { *m = GetPlayerLastRecordReq{} }
func (m *GetPlayerLastRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetPlayerLastRecordReq) ProtoMessage()    {}
func (*GetPlayerLastRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{48}
}
func (m *GetPlayerLastRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayerLastRecordReq.Unmarshal(m, b)
}
func (m *GetPlayerLastRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayerLastRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetPlayerLastRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayerLastRecordReq.Merge(dst, src)
}
func (m *GetPlayerLastRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetPlayerLastRecordReq.Size(m)
}
func (m *GetPlayerLastRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayerLastRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayerLastRecordReq proto.InternalMessageInfo

func (m *GetPlayerLastRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPlayerLastRecordResp struct {
	GamePercent          *UserGamePercent `protobuf:"bytes,1,opt,name=game_percent,json=gamePercent,proto3" json:"game_percent,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetPlayerLastRecordResp) Reset()         { *m = GetPlayerLastRecordResp{} }
func (m *GetPlayerLastRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetPlayerLastRecordResp) ProtoMessage()    {}
func (*GetPlayerLastRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{49}
}
func (m *GetPlayerLastRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayerLastRecordResp.Unmarshal(m, b)
}
func (m *GetPlayerLastRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayerLastRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetPlayerLastRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayerLastRecordResp.Merge(dst, src)
}
func (m *GetPlayerLastRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetPlayerLastRecordResp.Size(m)
}
func (m *GetPlayerLastRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayerLastRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayerLastRecordResp proto.InternalMessageInfo

func (m *GetPlayerLastRecordResp) GetGamePercent() *UserGamePercent {
	if m != nil {
		return m.GamePercent
	}
	return nil
}

type BatchGetPlayerGamePercentByGameIdsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameIds              []uint32 `protobuf:"varint,2,rep,packed,name=game_ids,json=gameIds,proto3" json:"game_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetPlayerGamePercentByGameIdsReq) Reset()         { *m = BatchGetPlayerGamePercentByGameIdsReq{} }
func (m *BatchGetPlayerGamePercentByGameIdsReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetPlayerGamePercentByGameIdsReq) ProtoMessage()    {}
func (*BatchGetPlayerGamePercentByGameIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{50}
}
func (m *BatchGetPlayerGamePercentByGameIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetPlayerGamePercentByGameIdsReq.Unmarshal(m, b)
}
func (m *BatchGetPlayerGamePercentByGameIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetPlayerGamePercentByGameIdsReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetPlayerGamePercentByGameIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetPlayerGamePercentByGameIdsReq.Merge(dst, src)
}
func (m *BatchGetPlayerGamePercentByGameIdsReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetPlayerGamePercentByGameIdsReq.Size(m)
}
func (m *BatchGetPlayerGamePercentByGameIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetPlayerGamePercentByGameIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetPlayerGamePercentByGameIdsReq proto.InternalMessageInfo

func (m *BatchGetPlayerGamePercentByGameIdsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchGetPlayerGamePercentByGameIdsReq) GetGameIds() []uint32 {
	if m != nil {
		return m.GameIds
	}
	return nil
}

type BatchGetPlayerGamePercentByGameIdsResp struct {
	GamePercents         []*UserGamePercent `protobuf:"bytes,1,rep,name=game_percents,json=gamePercents,proto3" json:"game_percents,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchGetPlayerGamePercentByGameIdsResp) Reset() {
	*m = BatchGetPlayerGamePercentByGameIdsResp{}
}
func (m *BatchGetPlayerGamePercentByGameIdsResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetPlayerGamePercentByGameIdsResp) ProtoMessage()    {}
func (*BatchGetPlayerGamePercentByGameIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{51}
}
func (m *BatchGetPlayerGamePercentByGameIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetPlayerGamePercentByGameIdsResp.Unmarshal(m, b)
}
func (m *BatchGetPlayerGamePercentByGameIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetPlayerGamePercentByGameIdsResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetPlayerGamePercentByGameIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetPlayerGamePercentByGameIdsResp.Merge(dst, src)
}
func (m *BatchGetPlayerGamePercentByGameIdsResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetPlayerGamePercentByGameIdsResp.Size(m)
}
func (m *BatchGetPlayerGamePercentByGameIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetPlayerGamePercentByGameIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetPlayerGamePercentByGameIdsResp proto.InternalMessageInfo

func (m *BatchGetPlayerGamePercentByGameIdsResp) GetGamePercents() []*UserGamePercent {
	if m != nil {
		return m.GamePercents
	}
	return nil
}

type BatchCheckHelperListReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	PlayerUid            uint32   `protobuf:"varint,3,opt,name=player_uid,json=playerUid,proto3" json:"player_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchCheckHelperListReq) Reset()         { *m = BatchCheckHelperListReq{} }
func (m *BatchCheckHelperListReq) String() string { return proto.CompactTextString(m) }
func (*BatchCheckHelperListReq) ProtoMessage()    {}
func (*BatchCheckHelperListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{52}
}
func (m *BatchCheckHelperListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckHelperListReq.Unmarshal(m, b)
}
func (m *BatchCheckHelperListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckHelperListReq.Marshal(b, m, deterministic)
}
func (dst *BatchCheckHelperListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckHelperListReq.Merge(dst, src)
}
func (m *BatchCheckHelperListReq) XXX_Size() int {
	return xxx_messageInfo_BatchCheckHelperListReq.Size(m)
}
func (m *BatchCheckHelperListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckHelperListReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckHelperListReq proto.InternalMessageInfo

func (m *BatchCheckHelperListReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *BatchCheckHelperListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchCheckHelperListReq) GetPlayerUid() uint32 {
	if m != nil {
		return m.PlayerUid
	}
	return 0
}

type BatchCheckHelperListResp struct {
	CanWelcomeBeHelper   []uint32 `protobuf:"varint,1,rep,packed,name=can_welcome_be_helper,json=canWelcomeBeHelper,proto3" json:"can_welcome_be_helper,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchCheckHelperListResp) Reset()         { *m = BatchCheckHelperListResp{} }
func (m *BatchCheckHelperListResp) String() string { return proto.CompactTextString(m) }
func (*BatchCheckHelperListResp) ProtoMessage()    {}
func (*BatchCheckHelperListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{53}
}
func (m *BatchCheckHelperListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckHelperListResp.Unmarshal(m, b)
}
func (m *BatchCheckHelperListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckHelperListResp.Marshal(b, m, deterministic)
}
func (dst *BatchCheckHelperListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckHelperListResp.Merge(dst, src)
}
func (m *BatchCheckHelperListResp) XXX_Size() int {
	return xxx_messageInfo_BatchCheckHelperListResp.Size(m)
}
func (m *BatchCheckHelperListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckHelperListResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckHelperListResp proto.InternalMessageInfo

func (m *BatchCheckHelperListResp) GetCanWelcomeBeHelper() []uint32 {
	if m != nil {
		return m.CanWelcomeBeHelper
	}
	return nil
}

type GameCommonMsg struct {
	GameData             *GameCommonData `protobuf:"bytes,1,opt,name=game_data,json=gameData,proto3" json:"game_data,omitempty"`
	UpdateTime           uint32          `protobuf:"varint,2,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GameCommonMsg) Reset()         { *m = GameCommonMsg{} }
func (m *GameCommonMsg) String() string { return proto.CompactTextString(m) }
func (*GameCommonMsg) ProtoMessage()    {}
func (*GameCommonMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{54}
}
func (m *GameCommonMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameCommonMsg.Unmarshal(m, b)
}
func (m *GameCommonMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameCommonMsg.Marshal(b, m, deterministic)
}
func (dst *GameCommonMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameCommonMsg.Merge(dst, src)
}
func (m *GameCommonMsg) XXX_Size() int {
	return xxx_messageInfo_GameCommonMsg.Size(m)
}
func (m *GameCommonMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GameCommonMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GameCommonMsg proto.InternalMessageInfo

func (m *GameCommonMsg) GetGameData() *GameCommonData {
	if m != nil {
		return m.GameData
	}
	return nil
}

func (m *GameCommonMsg) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

// 游戏进度，可定义的非通用结构，按需扩展
type GameCommonData struct {
	// Types that are valid to be assigned to Data:
	//	*GameCommonData_RoomPercent
	//	*GameCommonData_UserPercent
	//	*GameCommonData_UserHelpData
	//	*GameCommonData_UserChapterData
	//	*GameCommonData_UserGameTaskData
	Data                 isGameCommonData_Data `protobuf_oneof:"data"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GameCommonData) Reset()         { *m = GameCommonData{} }
func (m *GameCommonData) String() string { return proto.CompactTextString(m) }
func (*GameCommonData) ProtoMessage()    {}
func (*GameCommonData) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{55}
}
func (m *GameCommonData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameCommonData.Unmarshal(m, b)
}
func (m *GameCommonData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameCommonData.Marshal(b, m, deterministic)
}
func (dst *GameCommonData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameCommonData.Merge(dst, src)
}
func (m *GameCommonData) XXX_Size() int {
	return xxx_messageInfo_GameCommonData.Size(m)
}
func (m *GameCommonData) XXX_DiscardUnknown() {
	xxx_messageInfo_GameCommonData.DiscardUnknown(m)
}

var xxx_messageInfo_GameCommonData proto.InternalMessageInfo

type isGameCommonData_Data interface {
	isGameCommonData_Data()
}

type GameCommonData_RoomPercent struct {
	RoomPercent *RoomGamePercent `protobuf:"bytes,1,opt,name=room_percent,json=roomPercent,proto3,oneof"`
}

type GameCommonData_UserPercent struct {
	UserPercent *UserGamePercent `protobuf:"bytes,2,opt,name=user_percent,json=userPercent,proto3,oneof"`
}

type GameCommonData_UserHelpData struct {
	UserHelpData *UserHelpData `protobuf:"bytes,3,opt,name=user_help_data,json=userHelpData,proto3,oneof"`
}

type GameCommonData_UserChapterData struct {
	UserChapterData *UserChapterData `protobuf:"bytes,4,opt,name=user_chapter_data,json=userChapterData,proto3,oneof"`
}

type GameCommonData_UserGameTaskData struct {
	UserGameTaskData *UserGameTaskData `protobuf:"bytes,5,opt,name=user_game_task_data,json=userGameTaskData,proto3,oneof"`
}

func (*GameCommonData_RoomPercent) isGameCommonData_Data() {}

func (*GameCommonData_UserPercent) isGameCommonData_Data() {}

func (*GameCommonData_UserHelpData) isGameCommonData_Data() {}

func (*GameCommonData_UserChapterData) isGameCommonData_Data() {}

func (*GameCommonData_UserGameTaskData) isGameCommonData_Data() {}

func (m *GameCommonData) GetData() isGameCommonData_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *GameCommonData) GetRoomPercent() *RoomGamePercent {
	if x, ok := m.GetData().(*GameCommonData_RoomPercent); ok {
		return x.RoomPercent
	}
	return nil
}

func (m *GameCommonData) GetUserPercent() *UserGamePercent {
	if x, ok := m.GetData().(*GameCommonData_UserPercent); ok {
		return x.UserPercent
	}
	return nil
}

func (m *GameCommonData) GetUserHelpData() *UserHelpData {
	if x, ok := m.GetData().(*GameCommonData_UserHelpData); ok {
		return x.UserHelpData
	}
	return nil
}

func (m *GameCommonData) GetUserChapterData() *UserChapterData {
	if x, ok := m.GetData().(*GameCommonData_UserChapterData); ok {
		return x.UserChapterData
	}
	return nil
}

func (m *GameCommonData) GetUserGameTaskData() *UserGameTaskData {
	if x, ok := m.GetData().(*GameCommonData_UserGameTaskData); ok {
		return x.UserGameTaskData
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*GameCommonData) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _GameCommonData_OneofMarshaler, _GameCommonData_OneofUnmarshaler, _GameCommonData_OneofSizer, []interface{}{
		(*GameCommonData_RoomPercent)(nil),
		(*GameCommonData_UserPercent)(nil),
		(*GameCommonData_UserHelpData)(nil),
		(*GameCommonData_UserChapterData)(nil),
		(*GameCommonData_UserGameTaskData)(nil),
	}
}

func _GameCommonData_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*GameCommonData)
	// data
	switch x := m.Data.(type) {
	case *GameCommonData_RoomPercent:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.RoomPercent); err != nil {
			return err
		}
	case *GameCommonData_UserPercent:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.UserPercent); err != nil {
			return err
		}
	case *GameCommonData_UserHelpData:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.UserHelpData); err != nil {
			return err
		}
	case *GameCommonData_UserChapterData:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.UserChapterData); err != nil {
			return err
		}
	case *GameCommonData_UserGameTaskData:
		b.EncodeVarint(5<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.UserGameTaskData); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("GameCommonData.Data has unexpected type %T", x)
	}
	return nil
}

func _GameCommonData_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*GameCommonData)
	switch tag {
	case 1: // data.room_percent
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(RoomGamePercent)
		err := b.DecodeMessage(msg)
		m.Data = &GameCommonData_RoomPercent{msg}
		return true, err
	case 2: // data.user_percent
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(UserGamePercent)
		err := b.DecodeMessage(msg)
		m.Data = &GameCommonData_UserPercent{msg}
		return true, err
	case 3: // data.user_help_data
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(UserHelpData)
		err := b.DecodeMessage(msg)
		m.Data = &GameCommonData_UserHelpData{msg}
		return true, err
	case 4: // data.user_chapter_data
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(UserChapterData)
		err := b.DecodeMessage(msg)
		m.Data = &GameCommonData_UserChapterData{msg}
		return true, err
	case 5: // data.user_game_task_data
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(UserGameTaskData)
		err := b.DecodeMessage(msg)
		m.Data = &GameCommonData_UserGameTaskData{msg}
		return true, err
	default:
		return false, nil
	}
}

func _GameCommonData_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*GameCommonData)
	// data
	switch x := m.Data.(type) {
	case *GameCommonData_RoomPercent:
		s := proto.Size(x.RoomPercent)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *GameCommonData_UserPercent:
		s := proto.Size(x.UserPercent)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *GameCommonData_UserHelpData:
		s := proto.Size(x.UserHelpData)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *GameCommonData_UserChapterData:
		s := proto.Size(x.UserChapterData)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *GameCommonData_UserGameTaskData:
		s := proto.Size(x.UserGameTaskData)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type RoomGamePercent struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	RoomId               uint32   `protobuf:"varint,2,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	Chapter              uint32   `protobuf:"varint,3,opt,name=chapter,proto3" json:"chapter,omitempty"`
	Percent              float64  `protobuf:"fixed64,4,opt,name=percent,proto3" json:"percent,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoomGamePercent) Reset()         { *m = RoomGamePercent{} }
func (m *RoomGamePercent) String() string { return proto.CompactTextString(m) }
func (*RoomGamePercent) ProtoMessage()    {}
func (*RoomGamePercent) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{56}
}
func (m *RoomGamePercent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoomGamePercent.Unmarshal(m, b)
}
func (m *RoomGamePercent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoomGamePercent.Marshal(b, m, deterministic)
}
func (dst *RoomGamePercent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoomGamePercent.Merge(dst, src)
}
func (m *RoomGamePercent) XXX_Size() int {
	return xxx_messageInfo_RoomGamePercent.Size(m)
}
func (m *RoomGamePercent) XXX_DiscardUnknown() {
	xxx_messageInfo_RoomGamePercent.DiscardUnknown(m)
}

var xxx_messageInfo_RoomGamePercent proto.InternalMessageInfo

func (m *RoomGamePercent) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *RoomGamePercent) GetRoomId() uint32 {
	if m != nil {
		return m.RoomId
	}
	return 0
}

func (m *RoomGamePercent) GetChapter() uint32 {
	if m != nil {
		return m.Chapter
	}
	return 0
}

func (m *RoomGamePercent) GetPercent() float64 {
	if m != nil {
		return m.Percent
	}
	return 0
}

type UserGamePercent struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Chapter              uint32   `protobuf:"varint,3,opt,name=chapter,proto3" json:"chapter,omitempty"`
	Percent              float64  `protobuf:"fixed64,4,opt,name=percent,proto3" json:"percent,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserGamePercent) Reset()         { *m = UserGamePercent{} }
func (m *UserGamePercent) String() string { return proto.CompactTextString(m) }
func (*UserGamePercent) ProtoMessage()    {}
func (*UserGamePercent) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{57}
}
func (m *UserGamePercent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserGamePercent.Unmarshal(m, b)
}
func (m *UserGamePercent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserGamePercent.Marshal(b, m, deterministic)
}
func (dst *UserGamePercent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserGamePercent.Merge(dst, src)
}
func (m *UserGamePercent) XXX_Size() int {
	return xxx_messageInfo_UserGamePercent.Size(m)
}
func (m *UserGamePercent) XXX_DiscardUnknown() {
	xxx_messageInfo_UserGamePercent.DiscardUnknown(m)
}

var xxx_messageInfo_UserGamePercent proto.InternalMessageInfo

func (m *UserGamePercent) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UserGamePercent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserGamePercent) GetChapter() uint32 {
	if m != nil {
		return m.Chapter
	}
	return 0
}

func (m *UserGamePercent) GetPercent() float64 {
	if m != nil {
		return m.Percent
	}
	return 0
}

type UserHelpData struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	HelpUid              uint32   `protobuf:"varint,2,opt,name=help_uid,json=helpUid,proto3" json:"help_uid,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	DailyLimit           bool     `protobuf:"varint,4,opt,name=daily_limit,json=dailyLimit,proto3" json:"daily_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserHelpData) Reset()         { *m = UserHelpData{} }
func (m *UserHelpData) String() string { return proto.CompactTextString(m) }
func (*UserHelpData) ProtoMessage()    {}
func (*UserHelpData) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{58}
}
func (m *UserHelpData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserHelpData.Unmarshal(m, b)
}
func (m *UserHelpData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserHelpData.Marshal(b, m, deterministic)
}
func (dst *UserHelpData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserHelpData.Merge(dst, src)
}
func (m *UserHelpData) XXX_Size() int {
	return xxx_messageInfo_UserHelpData.Size(m)
}
func (m *UserHelpData) XXX_DiscardUnknown() {
	xxx_messageInfo_UserHelpData.DiscardUnknown(m)
}

var xxx_messageInfo_UserHelpData proto.InternalMessageInfo

func (m *UserHelpData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserHelpData) GetHelpUid() uint32 {
	if m != nil {
		return m.HelpUid
	}
	return 0
}

func (m *UserHelpData) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UserHelpData) GetDailyLimit() bool {
	if m != nil {
		return m.DailyLimit
	}
	return false
}

type UserChapterData struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Uids                 []uint32 `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	BeginChapter         uint32   `protobuf:"varint,3,opt,name=begin_chapter,json=beginChapter,proto3" json:"begin_chapter,omitempty"`
	EndChapter           uint32   `protobuf:"varint,4,opt,name=end_chapter,json=endChapter,proto3" json:"end_chapter,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserChapterData) Reset()         { *m = UserChapterData{} }
func (m *UserChapterData) String() string { return proto.CompactTextString(m) }
func (*UserChapterData) ProtoMessage()    {}
func (*UserChapterData) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{59}
}
func (m *UserChapterData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserChapterData.Unmarshal(m, b)
}
func (m *UserChapterData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserChapterData.Marshal(b, m, deterministic)
}
func (dst *UserChapterData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserChapterData.Merge(dst, src)
}
func (m *UserChapterData) XXX_Size() int {
	return xxx_messageInfo_UserChapterData.Size(m)
}
func (m *UserChapterData) XXX_DiscardUnknown() {
	xxx_messageInfo_UserChapterData.DiscardUnknown(m)
}

var xxx_messageInfo_UserChapterData proto.InternalMessageInfo

func (m *UserChapterData) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UserChapterData) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *UserChapterData) GetBeginChapter() uint32 {
	if m != nil {
		return m.BeginChapter
	}
	return 0
}

func (m *UserChapterData) GetEndChapter() uint32 {
	if m != nil {
		return m.EndChapter
	}
	return 0
}

type UserGameTaskData struct {
	Uid          uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TeammateUids []uint32 `protobuf:"varint,2,rep,packed,name=teammate_uids,json=teammateUids,proto3" json:"teammate_uids,omitempty"`
	GameId       uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Chapter      uint32   `protobuf:"varint,4,opt,name=chapter,proto3" json:"chapter,omitempty"`
	DramaId      string   `protobuf:"bytes,5,opt,name=drama_id,json=dramaId,proto3" json:"drama_id,omitempty"`
	PlotId       string   `protobuf:"bytes,6,opt,name=plot_id,json=plotId,proto3" json:"plot_id,omitempty"`
	//
	// 任务ID，普通任务传对应的ID。特殊任务约定：
	// 传递道具：transfer_item
	// 合成道具：compound_item
	TaskId               string   `protobuf:"bytes,7,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	RoundId              string   `protobuf:"bytes,8,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserGameTaskData) Reset()         { *m = UserGameTaskData{} }
func (m *UserGameTaskData) String() string { return proto.CompactTextString(m) }
func (*UserGameTaskData) ProtoMessage()    {}
func (*UserGameTaskData) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{60}
}
func (m *UserGameTaskData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserGameTaskData.Unmarshal(m, b)
}
func (m *UserGameTaskData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserGameTaskData.Marshal(b, m, deterministic)
}
func (dst *UserGameTaskData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserGameTaskData.Merge(dst, src)
}
func (m *UserGameTaskData) XXX_Size() int {
	return xxx_messageInfo_UserGameTaskData.Size(m)
}
func (m *UserGameTaskData) XXX_DiscardUnknown() {
	xxx_messageInfo_UserGameTaskData.DiscardUnknown(m)
}

var xxx_messageInfo_UserGameTaskData proto.InternalMessageInfo

func (m *UserGameTaskData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserGameTaskData) GetTeammateUids() []uint32 {
	if m != nil {
		return m.TeammateUids
	}
	return nil
}

func (m *UserGameTaskData) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UserGameTaskData) GetChapter() uint32 {
	if m != nil {
		return m.Chapter
	}
	return 0
}

func (m *UserGameTaskData) GetDramaId() string {
	if m != nil {
		return m.DramaId
	}
	return ""
}

func (m *UserGameTaskData) GetPlotId() string {
	if m != nil {
		return m.PlotId
	}
	return ""
}

func (m *UserGameTaskData) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *UserGameTaskData) GetRoundId() string {
	if m != nil {
		return m.RoundId
	}
	return ""
}

type RearrangeScenarioReq struct {
	ScenarioIds          []uint32 `protobuf:"varint,1,rep,packed,name=scenario_ids,json=scenarioIds,proto3" json:"scenario_ids,omitempty"`
	SortType             SortType `protobuf:"varint,2,opt,name=sort_type,json=sortType,proto3,enum=mystery_place.SortType" json:"sort_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RearrangeScenarioReq) Reset()         { *m = RearrangeScenarioReq{} }
func (m *RearrangeScenarioReq) String() string { return proto.CompactTextString(m) }
func (*RearrangeScenarioReq) ProtoMessage()    {}
func (*RearrangeScenarioReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{61}
}
func (m *RearrangeScenarioReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RearrangeScenarioReq.Unmarshal(m, b)
}
func (m *RearrangeScenarioReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RearrangeScenarioReq.Marshal(b, m, deterministic)
}
func (dst *RearrangeScenarioReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RearrangeScenarioReq.Merge(dst, src)
}
func (m *RearrangeScenarioReq) XXX_Size() int {
	return xxx_messageInfo_RearrangeScenarioReq.Size(m)
}
func (m *RearrangeScenarioReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RearrangeScenarioReq.DiscardUnknown(m)
}

var xxx_messageInfo_RearrangeScenarioReq proto.InternalMessageInfo

func (m *RearrangeScenarioReq) GetScenarioIds() []uint32 {
	if m != nil {
		return m.ScenarioIds
	}
	return nil
}

func (m *RearrangeScenarioReq) GetSortType() SortType {
	if m != nil {
		return m.SortType
	}
	return SortType_OLD_USER_SORT
}

type RearrangeScenarioResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RearrangeScenarioResp) Reset()         { *m = RearrangeScenarioResp{} }
func (m *RearrangeScenarioResp) String() string { return proto.CompactTextString(m) }
func (*RearrangeScenarioResp) ProtoMessage()    {}
func (*RearrangeScenarioResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{62}
}
func (m *RearrangeScenarioResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RearrangeScenarioResp.Unmarshal(m, b)
}
func (m *RearrangeScenarioResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RearrangeScenarioResp.Marshal(b, m, deterministic)
}
func (dst *RearrangeScenarioResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RearrangeScenarioResp.Merge(dst, src)
}
func (m *RearrangeScenarioResp) XXX_Size() int {
	return xxx_messageInfo_RearrangeScenarioResp.Size(m)
}
func (m *RearrangeScenarioResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RearrangeScenarioResp.DiscardUnknown(m)
}

var xxx_messageInfo_RearrangeScenarioResp proto.InternalMessageInfo

// 邀请别人来我的密逃房，结果通知
type Invite2MyRoomResultPush struct {
	InviteUid            uint32              `protobuf:"varint,1,opt,name=invite_uid,json=inviteUid,proto3" json:"invite_uid,omitempty"`
	ToChannel            uint32              `protobuf:"varint,2,opt,name=to_channel,json=toChannel,proto3" json:"to_channel,omitempty"`
	InviteResult         MysteryInviteResult `protobuf:"varint,3,opt,name=invite_result,json=inviteResult,proto3,enum=mystery_place.MysteryInviteResult" json:"invite_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *Invite2MyRoomResultPush) Reset()         { *m = Invite2MyRoomResultPush{} }
func (m *Invite2MyRoomResultPush) String() string { return proto.CompactTextString(m) }
func (*Invite2MyRoomResultPush) ProtoMessage()    {}
func (*Invite2MyRoomResultPush) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{63}
}
func (m *Invite2MyRoomResultPush) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Invite2MyRoomResultPush.Unmarshal(m, b)
}
func (m *Invite2MyRoomResultPush) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Invite2MyRoomResultPush.Marshal(b, m, deterministic)
}
func (dst *Invite2MyRoomResultPush) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Invite2MyRoomResultPush.Merge(dst, src)
}
func (m *Invite2MyRoomResultPush) XXX_Size() int {
	return xxx_messageInfo_Invite2MyRoomResultPush.Size(m)
}
func (m *Invite2MyRoomResultPush) XXX_DiscardUnknown() {
	xxx_messageInfo_Invite2MyRoomResultPush.DiscardUnknown(m)
}

var xxx_messageInfo_Invite2MyRoomResultPush proto.InternalMessageInfo

func (m *Invite2MyRoomResultPush) GetInviteUid() uint32 {
	if m != nil {
		return m.InviteUid
	}
	return 0
}

func (m *Invite2MyRoomResultPush) GetToChannel() uint32 {
	if m != nil {
		return m.ToChannel
	}
	return 0
}

func (m *Invite2MyRoomResultPush) GetInviteResult() MysteryInviteResult {
	if m != nil {
		return m.InviteResult
	}
	return MysteryInviteResult_NOT_FOUND
}

type UserCommentScenarioRecord struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ScenarioId           uint32   `protobuf:"varint,3,opt,name=scenario_id,json=scenarioId,proto3" json:"scenario_id,omitempty"`
	IsFinishedComment    bool     `protobuf:"varint,4,opt,name=is_finished_comment,json=isFinishedComment,proto3" json:"is_finished_comment,omitempty"`
	Score                uint32   `protobuf:"varint,5,opt,name=score,proto3" json:"score,omitempty"`
	CommentText          string   `protobuf:"bytes,6,opt,name=comment_text,json=commentText,proto3" json:"comment_text,omitempty"`
	IsDisplay            bool     `protobuf:"varint,7,opt,name=is_display,json=isDisplay,proto3" json:"is_display,omitempty"`
	CreateAt             int64    `protobuf:"varint,8,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	PostId               string   `protobuf:"bytes,9,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserCommentScenarioRecord) Reset()         { *m = UserCommentScenarioRecord{} }
func (m *UserCommentScenarioRecord) String() string { return proto.CompactTextString(m) }
func (*UserCommentScenarioRecord) ProtoMessage()    {}
func (*UserCommentScenarioRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{64}
}
func (m *UserCommentScenarioRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserCommentScenarioRecord.Unmarshal(m, b)
}
func (m *UserCommentScenarioRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserCommentScenarioRecord.Marshal(b, m, deterministic)
}
func (dst *UserCommentScenarioRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserCommentScenarioRecord.Merge(dst, src)
}
func (m *UserCommentScenarioRecord) XXX_Size() int {
	return xxx_messageInfo_UserCommentScenarioRecord.Size(m)
}
func (m *UserCommentScenarioRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_UserCommentScenarioRecord.DiscardUnknown(m)
}

var xxx_messageInfo_UserCommentScenarioRecord proto.InternalMessageInfo

func (m *UserCommentScenarioRecord) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UserCommentScenarioRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserCommentScenarioRecord) GetScenarioId() uint32 {
	if m != nil {
		return m.ScenarioId
	}
	return 0
}

func (m *UserCommentScenarioRecord) GetIsFinishedComment() bool {
	if m != nil {
		return m.IsFinishedComment
	}
	return false
}

func (m *UserCommentScenarioRecord) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *UserCommentScenarioRecord) GetCommentText() string {
	if m != nil {
		return m.CommentText
	}
	return ""
}

func (m *UserCommentScenarioRecord) GetIsDisplay() bool {
	if m != nil {
		return m.IsDisplay
	}
	return false
}

func (m *UserCommentScenarioRecord) GetCreateAt() int64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *UserCommentScenarioRecord) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type AddUserCommentScenarioReq struct {
	Record               *UserCommentScenarioRecord `protobuf:"bytes,1,opt,name=record,proto3" json:"record,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *AddUserCommentScenarioReq) Reset()         { *m = AddUserCommentScenarioReq{} }
func (m *AddUserCommentScenarioReq) String() string { return proto.CompactTextString(m) }
func (*AddUserCommentScenarioReq) ProtoMessage()    {}
func (*AddUserCommentScenarioReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{65}
}
func (m *AddUserCommentScenarioReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserCommentScenarioReq.Unmarshal(m, b)
}
func (m *AddUserCommentScenarioReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserCommentScenarioReq.Marshal(b, m, deterministic)
}
func (dst *AddUserCommentScenarioReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserCommentScenarioReq.Merge(dst, src)
}
func (m *AddUserCommentScenarioReq) XXX_Size() int {
	return xxx_messageInfo_AddUserCommentScenarioReq.Size(m)
}
func (m *AddUserCommentScenarioReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserCommentScenarioReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserCommentScenarioReq proto.InternalMessageInfo

func (m *AddUserCommentScenarioReq) GetRecord() *UserCommentScenarioRecord {
	if m != nil {
		return m.Record
	}
	return nil
}

type AddUserCommentScenarioResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserCommentScenarioResp) Reset()         { *m = AddUserCommentScenarioResp{} }
func (m *AddUserCommentScenarioResp) String() string { return proto.CompactTextString(m) }
func (*AddUserCommentScenarioResp) ProtoMessage()    {}
func (*AddUserCommentScenarioResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{66}
}
func (m *AddUserCommentScenarioResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserCommentScenarioResp.Unmarshal(m, b)
}
func (m *AddUserCommentScenarioResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserCommentScenarioResp.Marshal(b, m, deterministic)
}
func (dst *AddUserCommentScenarioResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserCommentScenarioResp.Merge(dst, src)
}
func (m *AddUserCommentScenarioResp) XXX_Size() int {
	return xxx_messageInfo_AddUserCommentScenarioResp.Size(m)
}
func (m *AddUserCommentScenarioResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserCommentScenarioResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserCommentScenarioResp proto.InternalMessageInfo

type BatchGetUserCommentScenarioReq struct {
	PostIds              []string `protobuf:"bytes,1,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserCommentScenarioReq) Reset()         { *m = BatchGetUserCommentScenarioReq{} }
func (m *BatchGetUserCommentScenarioReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserCommentScenarioReq) ProtoMessage()    {}
func (*BatchGetUserCommentScenarioReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{67}
}
func (m *BatchGetUserCommentScenarioReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserCommentScenarioReq.Unmarshal(m, b)
}
func (m *BatchGetUserCommentScenarioReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserCommentScenarioReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserCommentScenarioReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserCommentScenarioReq.Merge(dst, src)
}
func (m *BatchGetUserCommentScenarioReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserCommentScenarioReq.Size(m)
}
func (m *BatchGetUserCommentScenarioReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserCommentScenarioReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserCommentScenarioReq proto.InternalMessageInfo

func (m *BatchGetUserCommentScenarioReq) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

type BatchGetUserCommentScenarioResp struct {
	Records              []*UserCommentScenarioRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *BatchGetUserCommentScenarioResp) Reset()         { *m = BatchGetUserCommentScenarioResp{} }
func (m *BatchGetUserCommentScenarioResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserCommentScenarioResp) ProtoMessage()    {}
func (*BatchGetUserCommentScenarioResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{68}
}
func (m *BatchGetUserCommentScenarioResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserCommentScenarioResp.Unmarshal(m, b)
}
func (m *BatchGetUserCommentScenarioResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserCommentScenarioResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserCommentScenarioResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserCommentScenarioResp.Merge(dst, src)
}
func (m *BatchGetUserCommentScenarioResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserCommentScenarioResp.Size(m)
}
func (m *BatchGetUserCommentScenarioResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserCommentScenarioResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserCommentScenarioResp proto.InternalMessageInfo

func (m *BatchGetUserCommentScenarioResp) GetRecords() []*UserCommentScenarioRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

type ListScenarioCommentReq struct {
	UidList              []uint32          `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	ScenarioIds          []uint32          `protobuf:"varint,2,rep,packed,name=scenario_ids,json=scenarioIds,proto3" json:"scenario_ids,omitempty"`
	CommentType          CommentTypeFilter `protobuf:"varint,3,opt,name=comment_type,json=commentType,proto3,enum=mystery_place.CommentTypeFilter" json:"comment_type,omitempty"`
	StartTime            int64             `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64             `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	DisplayFilter        DisplayFilter     `protobuf:"varint,6,opt,name=display_filter,json=displayFilter,proto3,enum=mystery_place.DisplayFilter" json:"display_filter,omitempty"`
	Limit                int64             `protobuf:"varint,7,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset               int64             `protobuf:"varint,8,opt,name=offset,proto3" json:"offset,omitempty"`
	OnlyText             bool              `protobuf:"varint,9,opt,name=only_text,json=onlyText,proto3" json:"only_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ListScenarioCommentReq) Reset()         { *m = ListScenarioCommentReq{} }
func (m *ListScenarioCommentReq) String() string { return proto.CompactTextString(m) }
func (*ListScenarioCommentReq) ProtoMessage()    {}
func (*ListScenarioCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{69}
}
func (m *ListScenarioCommentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListScenarioCommentReq.Unmarshal(m, b)
}
func (m *ListScenarioCommentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListScenarioCommentReq.Marshal(b, m, deterministic)
}
func (dst *ListScenarioCommentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListScenarioCommentReq.Merge(dst, src)
}
func (m *ListScenarioCommentReq) XXX_Size() int {
	return xxx_messageInfo_ListScenarioCommentReq.Size(m)
}
func (m *ListScenarioCommentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListScenarioCommentReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListScenarioCommentReq proto.InternalMessageInfo

func (m *ListScenarioCommentReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *ListScenarioCommentReq) GetScenarioIds() []uint32 {
	if m != nil {
		return m.ScenarioIds
	}
	return nil
}

func (m *ListScenarioCommentReq) GetCommentType() CommentTypeFilter {
	if m != nil {
		return m.CommentType
	}
	return CommentTypeFilter_ALL_COMMENT_TYPE
}

func (m *ListScenarioCommentReq) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *ListScenarioCommentReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ListScenarioCommentReq) GetDisplayFilter() DisplayFilter {
	if m != nil {
		return m.DisplayFilter
	}
	return DisplayFilter_IGNORE_DISPLAY_TYPE
}

func (m *ListScenarioCommentReq) GetLimit() int64 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *ListScenarioCommentReq) GetOffset() int64 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *ListScenarioCommentReq) GetOnlyText() bool {
	if m != nil {
		return m.OnlyText
	}
	return false
}

type ListScenarioCommentResp struct {
	Records              []*UserCommentScenarioRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	Total                int64                        `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *ListScenarioCommentResp) Reset()         { *m = ListScenarioCommentResp{} }
func (m *ListScenarioCommentResp) String() string { return proto.CompactTextString(m) }
func (*ListScenarioCommentResp) ProtoMessage()    {}
func (*ListScenarioCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{70}
}
func (m *ListScenarioCommentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListScenarioCommentResp.Unmarshal(m, b)
}
func (m *ListScenarioCommentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListScenarioCommentResp.Marshal(b, m, deterministic)
}
func (dst *ListScenarioCommentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListScenarioCommentResp.Merge(dst, src)
}
func (m *ListScenarioCommentResp) XXX_Size() int {
	return xxx_messageInfo_ListScenarioCommentResp.Size(m)
}
func (m *ListScenarioCommentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListScenarioCommentResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListScenarioCommentResp proto.InternalMessageInfo

func (m *ListScenarioCommentResp) GetRecords() []*UserCommentScenarioRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

func (m *ListScenarioCommentResp) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

type UpdateScenarioCommentReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	IsDisplay            bool     `protobuf:"varint,2,opt,name=is_display,json=isDisplay,proto3" json:"is_display,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateScenarioCommentReq) Reset()         { *m = UpdateScenarioCommentReq{} }
func (m *UpdateScenarioCommentReq) String() string { return proto.CompactTextString(m) }
func (*UpdateScenarioCommentReq) ProtoMessage()    {}
func (*UpdateScenarioCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{71}
}
func (m *UpdateScenarioCommentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateScenarioCommentReq.Unmarshal(m, b)
}
func (m *UpdateScenarioCommentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateScenarioCommentReq.Marshal(b, m, deterministic)
}
func (dst *UpdateScenarioCommentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateScenarioCommentReq.Merge(dst, src)
}
func (m *UpdateScenarioCommentReq) XXX_Size() int {
	return xxx_messageInfo_UpdateScenarioCommentReq.Size(m)
}
func (m *UpdateScenarioCommentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateScenarioCommentReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateScenarioCommentReq proto.InternalMessageInfo

func (m *UpdateScenarioCommentReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpdateScenarioCommentReq) GetIsDisplay() bool {
	if m != nil {
		return m.IsDisplay
	}
	return false
}

type UpdateScenarioCommentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateScenarioCommentResp) Reset()         { *m = UpdateScenarioCommentResp{} }
func (m *UpdateScenarioCommentResp) String() string { return proto.CompactTextString(m) }
func (*UpdateScenarioCommentResp) ProtoMessage()    {}
func (*UpdateScenarioCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{72}
}
func (m *UpdateScenarioCommentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateScenarioCommentResp.Unmarshal(m, b)
}
func (m *UpdateScenarioCommentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateScenarioCommentResp.Marshal(b, m, deterministic)
}
func (dst *UpdateScenarioCommentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateScenarioCommentResp.Merge(dst, src)
}
func (m *UpdateScenarioCommentResp) XXX_Size() int {
	return xxx_messageInfo_UpdateScenarioCommentResp.Size(m)
}
func (m *UpdateScenarioCommentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateScenarioCommentResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateScenarioCommentResp proto.InternalMessageInfo

type ScenarioCommentSummaryRecord struct {
	ScenarioId           uint32   `protobuf:"varint,1,opt,name=scenario_id,json=scenarioId,proto3" json:"scenario_id,omitempty"`
	TotalAverage         float32  `protobuf:"fixed32,2,opt,name=total_average,json=totalAverage,proto3" json:"total_average,omitempty"`
	FinishedAverage      float32  `protobuf:"fixed32,3,opt,name=finished_average,json=finishedAverage,proto3" json:"finished_average,omitempty"`
	UnfinishedAverage    float32  `protobuf:"fixed32,4,opt,name=unfinished_average,json=unfinishedAverage,proto3" json:"unfinished_average,omitempty"`
	CommentMember        uint32   `protobuf:"varint,5,opt,name=comment_member,json=commentMember,proto3" json:"comment_member,omitempty"`
	CommentCount         uint32   `protobuf:"varint,6,opt,name=comment_count,json=commentCount,proto3" json:"comment_count,omitempty"`
	ManualAverage        float32  `protobuf:"fixed32,7,opt,name=manual_average,json=manualAverage,proto3" json:"manual_average,omitempty"`
	MachineAverage       float32  `protobuf:"fixed32,8,opt,name=machine_average,json=machineAverage,proto3" json:"machine_average,omitempty"`
	RealAverage          float32  `protobuf:"fixed32,9,opt,name=real_average,json=realAverage,proto3" json:"real_average,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScenarioCommentSummaryRecord) Reset()         { *m = ScenarioCommentSummaryRecord{} }
func (m *ScenarioCommentSummaryRecord) String() string { return proto.CompactTextString(m) }
func (*ScenarioCommentSummaryRecord) ProtoMessage()    {}
func (*ScenarioCommentSummaryRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{73}
}
func (m *ScenarioCommentSummaryRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScenarioCommentSummaryRecord.Unmarshal(m, b)
}
func (m *ScenarioCommentSummaryRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScenarioCommentSummaryRecord.Marshal(b, m, deterministic)
}
func (dst *ScenarioCommentSummaryRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScenarioCommentSummaryRecord.Merge(dst, src)
}
func (m *ScenarioCommentSummaryRecord) XXX_Size() int {
	return xxx_messageInfo_ScenarioCommentSummaryRecord.Size(m)
}
func (m *ScenarioCommentSummaryRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_ScenarioCommentSummaryRecord.DiscardUnknown(m)
}

var xxx_messageInfo_ScenarioCommentSummaryRecord proto.InternalMessageInfo

func (m *ScenarioCommentSummaryRecord) GetScenarioId() uint32 {
	if m != nil {
		return m.ScenarioId
	}
	return 0
}

func (m *ScenarioCommentSummaryRecord) GetTotalAverage() float32 {
	if m != nil {
		return m.TotalAverage
	}
	return 0
}

func (m *ScenarioCommentSummaryRecord) GetFinishedAverage() float32 {
	if m != nil {
		return m.FinishedAverage
	}
	return 0
}

func (m *ScenarioCommentSummaryRecord) GetUnfinishedAverage() float32 {
	if m != nil {
		return m.UnfinishedAverage
	}
	return 0
}

func (m *ScenarioCommentSummaryRecord) GetCommentMember() uint32 {
	if m != nil {
		return m.CommentMember
	}
	return 0
}

func (m *ScenarioCommentSummaryRecord) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *ScenarioCommentSummaryRecord) GetManualAverage() float32 {
	if m != nil {
		return m.ManualAverage
	}
	return 0
}

func (m *ScenarioCommentSummaryRecord) GetMachineAverage() float32 {
	if m != nil {
		return m.MachineAverage
	}
	return 0
}

func (m *ScenarioCommentSummaryRecord) GetRealAverage() float32 {
	if m != nil {
		return m.RealAverage
	}
	return 0
}

type ListScenarioCommentSummaryReq struct {
	ScenarioIds          []uint32 `protobuf:"varint,1,rep,packed,name=scenario_ids,json=scenarioIds,proto3" json:"scenario_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListScenarioCommentSummaryReq) Reset()         { *m = ListScenarioCommentSummaryReq{} }
func (m *ListScenarioCommentSummaryReq) String() string { return proto.CompactTextString(m) }
func (*ListScenarioCommentSummaryReq) ProtoMessage()    {}
func (*ListScenarioCommentSummaryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{74}
}
func (m *ListScenarioCommentSummaryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListScenarioCommentSummaryReq.Unmarshal(m, b)
}
func (m *ListScenarioCommentSummaryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListScenarioCommentSummaryReq.Marshal(b, m, deterministic)
}
func (dst *ListScenarioCommentSummaryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListScenarioCommentSummaryReq.Merge(dst, src)
}
func (m *ListScenarioCommentSummaryReq) XXX_Size() int {
	return xxx_messageInfo_ListScenarioCommentSummaryReq.Size(m)
}
func (m *ListScenarioCommentSummaryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListScenarioCommentSummaryReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListScenarioCommentSummaryReq proto.InternalMessageInfo

func (m *ListScenarioCommentSummaryReq) GetScenarioIds() []uint32 {
	if m != nil {
		return m.ScenarioIds
	}
	return nil
}

type ListScenarioCommentSummaryResp struct {
	Records              []*ScenarioCommentSummaryRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *ListScenarioCommentSummaryResp) Reset()         { *m = ListScenarioCommentSummaryResp{} }
func (m *ListScenarioCommentSummaryResp) String() string { return proto.CompactTextString(m) }
func (*ListScenarioCommentSummaryResp) ProtoMessage()    {}
func (*ListScenarioCommentSummaryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{75}
}
func (m *ListScenarioCommentSummaryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListScenarioCommentSummaryResp.Unmarshal(m, b)
}
func (m *ListScenarioCommentSummaryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListScenarioCommentSummaryResp.Marshal(b, m, deterministic)
}
func (dst *ListScenarioCommentSummaryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListScenarioCommentSummaryResp.Merge(dst, src)
}
func (m *ListScenarioCommentSummaryResp) XXX_Size() int {
	return xxx_messageInfo_ListScenarioCommentSummaryResp.Size(m)
}
func (m *ListScenarioCommentSummaryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListScenarioCommentSummaryResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListScenarioCommentSummaryResp proto.InternalMessageInfo

func (m *ListScenarioCommentSummaryResp) GetRecords() []*ScenarioCommentSummaryRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

type SetScenarioManualAverageReq struct {
	ScenarioId           uint32   `protobuf:"varint,1,opt,name=scenario_id,json=scenarioId,proto3" json:"scenario_id,omitempty"`
	ManualAverage        float32  `protobuf:"fixed32,2,opt,name=manual_average,json=manualAverage,proto3" json:"manual_average,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetScenarioManualAverageReq) Reset()         { *m = SetScenarioManualAverageReq{} }
func (m *SetScenarioManualAverageReq) String() string { return proto.CompactTextString(m) }
func (*SetScenarioManualAverageReq) ProtoMessage()    {}
func (*SetScenarioManualAverageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{76}
}
func (m *SetScenarioManualAverageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetScenarioManualAverageReq.Unmarshal(m, b)
}
func (m *SetScenarioManualAverageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetScenarioManualAverageReq.Marshal(b, m, deterministic)
}
func (dst *SetScenarioManualAverageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetScenarioManualAverageReq.Merge(dst, src)
}
func (m *SetScenarioManualAverageReq) XXX_Size() int {
	return xxx_messageInfo_SetScenarioManualAverageReq.Size(m)
}
func (m *SetScenarioManualAverageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetScenarioManualAverageReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetScenarioManualAverageReq proto.InternalMessageInfo

func (m *SetScenarioManualAverageReq) GetScenarioId() uint32 {
	if m != nil {
		return m.ScenarioId
	}
	return 0
}

func (m *SetScenarioManualAverageReq) GetManualAverage() float32 {
	if m != nil {
		return m.ManualAverage
	}
	return 0
}

type SetScenarioManualAverageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetScenarioManualAverageResp) Reset()         { *m = SetScenarioManualAverageResp{} }
func (m *SetScenarioManualAverageResp) String() string { return proto.CompactTextString(m) }
func (*SetScenarioManualAverageResp) ProtoMessage()    {}
func (*SetScenarioManualAverageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{77}
}
func (m *SetScenarioManualAverageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetScenarioManualAverageResp.Unmarshal(m, b)
}
func (m *SetScenarioManualAverageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetScenarioManualAverageResp.Marshal(b, m, deterministic)
}
func (dst *SetScenarioManualAverageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetScenarioManualAverageResp.Merge(dst, src)
}
func (m *SetScenarioManualAverageResp) XXX_Size() int {
	return xxx_messageInfo_SetScenarioManualAverageResp.Size(m)
}
func (m *SetScenarioManualAverageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetScenarioManualAverageResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetScenarioManualAverageResp proto.InternalMessageInfo

type RefreshCommentSummaryReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefreshCommentSummaryReq) Reset()         { *m = RefreshCommentSummaryReq{} }
func (m *RefreshCommentSummaryReq) String() string { return proto.CompactTextString(m) }
func (*RefreshCommentSummaryReq) ProtoMessage()    {}
func (*RefreshCommentSummaryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{78}
}
func (m *RefreshCommentSummaryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefreshCommentSummaryReq.Unmarshal(m, b)
}
func (m *RefreshCommentSummaryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefreshCommentSummaryReq.Marshal(b, m, deterministic)
}
func (dst *RefreshCommentSummaryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefreshCommentSummaryReq.Merge(dst, src)
}
func (m *RefreshCommentSummaryReq) XXX_Size() int {
	return xxx_messageInfo_RefreshCommentSummaryReq.Size(m)
}
func (m *RefreshCommentSummaryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RefreshCommentSummaryReq.DiscardUnknown(m)
}

var xxx_messageInfo_RefreshCommentSummaryReq proto.InternalMessageInfo

type RefreshCommentSummaryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefreshCommentSummaryResp) Reset()         { *m = RefreshCommentSummaryResp{} }
func (m *RefreshCommentSummaryResp) String() string { return proto.CompactTextString(m) }
func (*RefreshCommentSummaryResp) ProtoMessage()    {}
func (*RefreshCommentSummaryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{79}
}
func (m *RefreshCommentSummaryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefreshCommentSummaryResp.Unmarshal(m, b)
}
func (m *RefreshCommentSummaryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefreshCommentSummaryResp.Marshal(b, m, deterministic)
}
func (dst *RefreshCommentSummaryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefreshCommentSummaryResp.Merge(dst, src)
}
func (m *RefreshCommentSummaryResp) XXX_Size() int {
	return xxx_messageInfo_RefreshCommentSummaryResp.Size(m)
}
func (m *RefreshCommentSummaryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RefreshCommentSummaryResp.DiscardUnknown(m)
}

var xxx_messageInfo_RefreshCommentSummaryResp proto.InternalMessageInfo

type NewScenarioTip struct {
	TipId                string             `protobuf:"bytes,1,opt,name=tip_id,json=tipId,proto3" json:"tip_id,omitempty"`
	ScenarioId           uint32             `protobuf:"varint,2,opt,name=scenario_id,json=scenarioId,proto3" json:"scenario_id,omitempty"`
	TabId                uint32             `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TipType              NewScenarioTipType `protobuf:"varint,4,opt,name=tip_type,json=tipType,proto3,enum=mystery_place.NewScenarioTipType" json:"tip_type,omitempty"`
	Content              string             `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *NewScenarioTip) Reset()         { *m = NewScenarioTip{} }
func (m *NewScenarioTip) String() string { return proto.CompactTextString(m) }
func (*NewScenarioTip) ProtoMessage()    {}
func (*NewScenarioTip) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{80}
}
func (m *NewScenarioTip) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewScenarioTip.Unmarshal(m, b)
}
func (m *NewScenarioTip) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewScenarioTip.Marshal(b, m, deterministic)
}
func (dst *NewScenarioTip) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewScenarioTip.Merge(dst, src)
}
func (m *NewScenarioTip) XXX_Size() int {
	return xxx_messageInfo_NewScenarioTip.Size(m)
}
func (m *NewScenarioTip) XXX_DiscardUnknown() {
	xxx_messageInfo_NewScenarioTip.DiscardUnknown(m)
}

var xxx_messageInfo_NewScenarioTip proto.InternalMessageInfo

func (m *NewScenarioTip) GetTipId() string {
	if m != nil {
		return m.TipId
	}
	return ""
}

func (m *NewScenarioTip) GetScenarioId() uint32 {
	if m != nil {
		return m.ScenarioId
	}
	return 0
}

func (m *NewScenarioTip) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *NewScenarioTip) GetTipType() NewScenarioTipType {
	if m != nil {
		return m.TipType
	}
	return NewScenarioTipType_NewScenarioTipType_UNDEFINED
}

func (m *NewScenarioTip) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type GetNewScenarioTipsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewScenarioTipsReq) Reset()         { *m = GetNewScenarioTipsReq{} }
func (m *GetNewScenarioTipsReq) String() string { return proto.CompactTextString(m) }
func (*GetNewScenarioTipsReq) ProtoMessage()    {}
func (*GetNewScenarioTipsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{81}
}
func (m *GetNewScenarioTipsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewScenarioTipsReq.Unmarshal(m, b)
}
func (m *GetNewScenarioTipsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewScenarioTipsReq.Marshal(b, m, deterministic)
}
func (dst *GetNewScenarioTipsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewScenarioTipsReq.Merge(dst, src)
}
func (m *GetNewScenarioTipsReq) XXX_Size() int {
	return xxx_messageInfo_GetNewScenarioTipsReq.Size(m)
}
func (m *GetNewScenarioTipsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewScenarioTipsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewScenarioTipsReq proto.InternalMessageInfo

func (m *GetNewScenarioTipsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetNewScenarioTipsResp struct {
	Tips                 []*NewScenarioTip `protobuf:"bytes,1,rep,name=tips,proto3" json:"tips,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetNewScenarioTipsResp) Reset()         { *m = GetNewScenarioTipsResp{} }
func (m *GetNewScenarioTipsResp) String() string { return proto.CompactTextString(m) }
func (*GetNewScenarioTipsResp) ProtoMessage()    {}
func (*GetNewScenarioTipsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{82}
}
func (m *GetNewScenarioTipsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewScenarioTipsResp.Unmarshal(m, b)
}
func (m *GetNewScenarioTipsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewScenarioTipsResp.Marshal(b, m, deterministic)
}
func (dst *GetNewScenarioTipsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewScenarioTipsResp.Merge(dst, src)
}
func (m *GetNewScenarioTipsResp) XXX_Size() int {
	return xxx_messageInfo_GetNewScenarioTipsResp.Size(m)
}
func (m *GetNewScenarioTipsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewScenarioTipsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewScenarioTipsResp proto.InternalMessageInfo

func (m *GetNewScenarioTipsResp) GetTips() []*NewScenarioTip {
	if m != nil {
		return m.Tips
	}
	return nil
}

type MarkNewScenarioTipReadReq struct {
	Uid                  uint32          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Tip                  *NewScenarioTip `protobuf:"bytes,2,opt,name=tip,proto3" json:"tip,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *MarkNewScenarioTipReadReq) Reset()         { *m = MarkNewScenarioTipReadReq{} }
func (m *MarkNewScenarioTipReadReq) String() string { return proto.CompactTextString(m) }
func (*MarkNewScenarioTipReadReq) ProtoMessage()    {}
func (*MarkNewScenarioTipReadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{83}
}
func (m *MarkNewScenarioTipReadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkNewScenarioTipReadReq.Unmarshal(m, b)
}
func (m *MarkNewScenarioTipReadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkNewScenarioTipReadReq.Marshal(b, m, deterministic)
}
func (dst *MarkNewScenarioTipReadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkNewScenarioTipReadReq.Merge(dst, src)
}
func (m *MarkNewScenarioTipReadReq) XXX_Size() int {
	return xxx_messageInfo_MarkNewScenarioTipReadReq.Size(m)
}
func (m *MarkNewScenarioTipReadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkNewScenarioTipReadReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkNewScenarioTipReadReq proto.InternalMessageInfo

func (m *MarkNewScenarioTipReadReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MarkNewScenarioTipReadReq) GetTip() *NewScenarioTip {
	if m != nil {
		return m.Tip
	}
	return nil
}

type MarkNewScenarioTipReadResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkNewScenarioTipReadResp) Reset()         { *m = MarkNewScenarioTipReadResp{} }
func (m *MarkNewScenarioTipReadResp) String() string { return proto.CompactTextString(m) }
func (*MarkNewScenarioTipReadResp) ProtoMessage()    {}
func (*MarkNewScenarioTipReadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{84}
}
func (m *MarkNewScenarioTipReadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkNewScenarioTipReadResp.Unmarshal(m, b)
}
func (m *MarkNewScenarioTipReadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkNewScenarioTipReadResp.Marshal(b, m, deterministic)
}
func (dst *MarkNewScenarioTipReadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkNewScenarioTipReadResp.Merge(dst, src)
}
func (m *MarkNewScenarioTipReadResp) XXX_Size() int {
	return xxx_messageInfo_MarkNewScenarioTipReadResp.Size(m)
}
func (m *MarkNewScenarioTipReadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkNewScenarioTipReadResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkNewScenarioTipReadResp proto.InternalMessageInfo

type GetScenarioSummaryReq struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScenarioSummaryReq) Reset()         { *m = GetScenarioSummaryReq{} }
func (m *GetScenarioSummaryReq) String() string { return proto.CompactTextString(m) }
func (*GetScenarioSummaryReq) ProtoMessage()    {}
func (*GetScenarioSummaryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{85}
}
func (m *GetScenarioSummaryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenarioSummaryReq.Unmarshal(m, b)
}
func (m *GetScenarioSummaryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenarioSummaryReq.Marshal(b, m, deterministic)
}
func (dst *GetScenarioSummaryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenarioSummaryReq.Merge(dst, src)
}
func (m *GetScenarioSummaryReq) XXX_Size() int {
	return xxx_messageInfo_GetScenarioSummaryReq.Size(m)
}
func (m *GetScenarioSummaryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenarioSummaryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenarioSummaryReq proto.InternalMessageInfo

func (m *GetScenarioSummaryReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

// 获取剧本记录
type GetPlayedScenarioRecordListReq struct {
	Uid                  uint32                `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	LoadMore             *MysteryPlaceLoadMore `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	Count                uint32                `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetPlayedScenarioRecordListReq) Reset()         { *m = GetPlayedScenarioRecordListReq{} }
func (m *GetPlayedScenarioRecordListReq) String() string { return proto.CompactTextString(m) }
func (*GetPlayedScenarioRecordListReq) ProtoMessage()    {}
func (*GetPlayedScenarioRecordListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{86}
}
func (m *GetPlayedScenarioRecordListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayedScenarioRecordListReq.Unmarshal(m, b)
}
func (m *GetPlayedScenarioRecordListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayedScenarioRecordListReq.Marshal(b, m, deterministic)
}
func (dst *GetPlayedScenarioRecordListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayedScenarioRecordListReq.Merge(dst, src)
}
func (m *GetPlayedScenarioRecordListReq) XXX_Size() int {
	return xxx_messageInfo_GetPlayedScenarioRecordListReq.Size(m)
}
func (m *GetPlayedScenarioRecordListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayedScenarioRecordListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayedScenarioRecordListReq proto.InternalMessageInfo

func (m *GetPlayedScenarioRecordListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPlayedScenarioRecordListReq) GetLoadMore() *MysteryPlaceLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetPlayedScenarioRecordListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetPlayedScenarioRecordListResp struct {
	LoadMore             *MysteryPlaceLoadMore `protobuf:"bytes,1,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	PlayedRecords        []*ScenarioRecord     `protobuf:"bytes,2,rep,name=played_records,json=playedRecords,proto3" json:"played_records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetPlayedScenarioRecordListResp) Reset()         { *m = GetPlayedScenarioRecordListResp{} }
func (m *GetPlayedScenarioRecordListResp) String() string { return proto.CompactTextString(m) }
func (*GetPlayedScenarioRecordListResp) ProtoMessage()    {}
func (*GetPlayedScenarioRecordListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{87}
}
func (m *GetPlayedScenarioRecordListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayedScenarioRecordListResp.Unmarshal(m, b)
}
func (m *GetPlayedScenarioRecordListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayedScenarioRecordListResp.Marshal(b, m, deterministic)
}
func (dst *GetPlayedScenarioRecordListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayedScenarioRecordListResp.Merge(dst, src)
}
func (m *GetPlayedScenarioRecordListResp) XXX_Size() int {
	return xxx_messageInfo_GetPlayedScenarioRecordListResp.Size(m)
}
func (m *GetPlayedScenarioRecordListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayedScenarioRecordListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayedScenarioRecordListResp proto.InternalMessageInfo

func (m *GetPlayedScenarioRecordListResp) GetLoadMore() *MysteryPlaceLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetPlayedScenarioRecordListResp) GetPlayedRecords() []*ScenarioRecord {
	if m != nil {
		return m.PlayedRecords
	}
	return nil
}

type ScenarioRecord struct {
	ScenarioId           uint32   `protobuf:"varint,1,opt,name=scenario_id,json=scenarioId,proto3" json:"scenario_id,omitempty"`
	ScenarioName         string   `protobuf:"bytes,2,opt,name=scenario_name,json=scenarioName,proto3" json:"scenario_name,omitempty"`
	Bg                   string   `protobuf:"bytes,3,opt,name=bg,proto3" json:"bg,omitempty"`
	PassedChapters       []uint32 `protobuf:"varint,4,rep,packed,name=passed_chapters,json=passedChapters,proto3" json:"passed_chapters,omitempty"`
	AllChapterNum        uint32   `protobuf:"varint,5,opt,name=all_chapter_num,json=allChapterNum,proto3" json:"all_chapter_num,omitempty"`
	Tip                  string   `protobuf:"bytes,6,opt,name=tip,proto3" json:"tip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScenarioRecord) Reset()         { *m = ScenarioRecord{} }
func (m *ScenarioRecord) String() string { return proto.CompactTextString(m) }
func (*ScenarioRecord) ProtoMessage()    {}
func (*ScenarioRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{88}
}
func (m *ScenarioRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScenarioRecord.Unmarshal(m, b)
}
func (m *ScenarioRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScenarioRecord.Marshal(b, m, deterministic)
}
func (dst *ScenarioRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScenarioRecord.Merge(dst, src)
}
func (m *ScenarioRecord) XXX_Size() int {
	return xxx_messageInfo_ScenarioRecord.Size(m)
}
func (m *ScenarioRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_ScenarioRecord.DiscardUnknown(m)
}

var xxx_messageInfo_ScenarioRecord proto.InternalMessageInfo

func (m *ScenarioRecord) GetScenarioId() uint32 {
	if m != nil {
		return m.ScenarioId
	}
	return 0
}

func (m *ScenarioRecord) GetScenarioName() string {
	if m != nil {
		return m.ScenarioName
	}
	return ""
}

func (m *ScenarioRecord) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

func (m *ScenarioRecord) GetPassedChapters() []uint32 {
	if m != nil {
		return m.PassedChapters
	}
	return nil
}

func (m *ScenarioRecord) GetAllChapterNum() uint32 {
	if m != nil {
		return m.AllChapterNum
	}
	return 0
}

func (m *ScenarioRecord) GetTip() string {
	if m != nil {
		return m.Tip
	}
	return ""
}

// 获取记录详情
type GetPlayedScenarioRecordDetailReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ScenarioId           uint32   `protobuf:"varint,2,opt,name=scenario_id,json=scenarioId,proto3" json:"scenario_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlayedScenarioRecordDetailReq) Reset()         { *m = GetPlayedScenarioRecordDetailReq{} }
func (m *GetPlayedScenarioRecordDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetPlayedScenarioRecordDetailReq) ProtoMessage()    {}
func (*GetPlayedScenarioRecordDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{89}
}
func (m *GetPlayedScenarioRecordDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayedScenarioRecordDetailReq.Unmarshal(m, b)
}
func (m *GetPlayedScenarioRecordDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayedScenarioRecordDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetPlayedScenarioRecordDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayedScenarioRecordDetailReq.Merge(dst, src)
}
func (m *GetPlayedScenarioRecordDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetPlayedScenarioRecordDetailReq.Size(m)
}
func (m *GetPlayedScenarioRecordDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayedScenarioRecordDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayedScenarioRecordDetailReq proto.InternalMessageInfo

func (m *GetPlayedScenarioRecordDetailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPlayedScenarioRecordDetailReq) GetScenarioId() uint32 {
	if m != nil {
		return m.ScenarioId
	}
	return 0
}

type GetPlayedScenarioRecordDetailResp struct {
	Detail               *ScenarioRecordDetail `protobuf:"bytes,1,opt,name=detail,proto3" json:"detail,omitempty"`
	IsVisible            bool                  `protobuf:"varint,2,opt,name=is_visible,json=isVisible,proto3" json:"is_visible,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetPlayedScenarioRecordDetailResp) Reset()         { *m = GetPlayedScenarioRecordDetailResp{} }
func (m *GetPlayedScenarioRecordDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetPlayedScenarioRecordDetailResp) ProtoMessage()    {}
func (*GetPlayedScenarioRecordDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{90}
}
func (m *GetPlayedScenarioRecordDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayedScenarioRecordDetailResp.Unmarshal(m, b)
}
func (m *GetPlayedScenarioRecordDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayedScenarioRecordDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetPlayedScenarioRecordDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayedScenarioRecordDetailResp.Merge(dst, src)
}
func (m *GetPlayedScenarioRecordDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetPlayedScenarioRecordDetailResp.Size(m)
}
func (m *GetPlayedScenarioRecordDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayedScenarioRecordDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayedScenarioRecordDetailResp proto.InternalMessageInfo

func (m *GetPlayedScenarioRecordDetailResp) GetDetail() *ScenarioRecordDetail {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (m *GetPlayedScenarioRecordDetailResp) GetIsVisible() bool {
	if m != nil {
		return m.IsVisible
	}
	return false
}

type ScenarioRecordDetail struct {
	ScenarioId           uint32              `protobuf:"varint,1,opt,name=scenario_id,json=scenarioId,proto3" json:"scenario_id,omitempty"`
	GameId               uint32              `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	TabId                uint32              `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Chapter              uint32              `protobuf:"varint,4,opt,name=chapter,proto3" json:"chapter,omitempty"`
	Player1              *MysteryPlacePlayer `protobuf:"bytes,5,opt,name=player1,proto3" json:"player1,omitempty"`
	Player2              *MysteryPlacePlayer `protobuf:"bytes,6,opt,name=player2,proto3" json:"player2,omitempty"`
	Summary              string              `protobuf:"bytes,7,opt,name=summary,proto3" json:"summary,omitempty"`
	CreatedAt            uint32              `protobuf:"varint,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ScenarioRecordDetail) Reset()         { *m = ScenarioRecordDetail{} }
func (m *ScenarioRecordDetail) String() string { return proto.CompactTextString(m) }
func (*ScenarioRecordDetail) ProtoMessage()    {}
func (*ScenarioRecordDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{91}
}
func (m *ScenarioRecordDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScenarioRecordDetail.Unmarshal(m, b)
}
func (m *ScenarioRecordDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScenarioRecordDetail.Marshal(b, m, deterministic)
}
func (dst *ScenarioRecordDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScenarioRecordDetail.Merge(dst, src)
}
func (m *ScenarioRecordDetail) XXX_Size() int {
	return xxx_messageInfo_ScenarioRecordDetail.Size(m)
}
func (m *ScenarioRecordDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_ScenarioRecordDetail.DiscardUnknown(m)
}

var xxx_messageInfo_ScenarioRecordDetail proto.InternalMessageInfo

func (m *ScenarioRecordDetail) GetScenarioId() uint32 {
	if m != nil {
		return m.ScenarioId
	}
	return 0
}

func (m *ScenarioRecordDetail) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ScenarioRecordDetail) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ScenarioRecordDetail) GetChapter() uint32 {
	if m != nil {
		return m.Chapter
	}
	return 0
}

func (m *ScenarioRecordDetail) GetPlayer1() *MysteryPlacePlayer {
	if m != nil {
		return m.Player1
	}
	return nil
}

func (m *ScenarioRecordDetail) GetPlayer2() *MysteryPlacePlayer {
	if m != nil {
		return m.Player2
	}
	return nil
}

func (m *ScenarioRecordDetail) GetSummary() string {
	if m != nil {
		return m.Summary
	}
	return ""
}

func (m *ScenarioRecordDetail) GetCreatedAt() uint32 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

type MysteryPlacePlayer struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MysteryPlacePlayer) Reset()         { *m = MysteryPlacePlayer{} }
func (m *MysteryPlacePlayer) String() string { return proto.CompactTextString(m) }
func (*MysteryPlacePlayer) ProtoMessage()    {}
func (*MysteryPlacePlayer) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{92}
}
func (m *MysteryPlacePlayer) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MysteryPlacePlayer.Unmarshal(m, b)
}
func (m *MysteryPlacePlayer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MysteryPlacePlayer.Marshal(b, m, deterministic)
}
func (dst *MysteryPlacePlayer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MysteryPlacePlayer.Merge(dst, src)
}
func (m *MysteryPlacePlayer) XXX_Size() int {
	return xxx_messageInfo_MysteryPlacePlayer.Size(m)
}
func (m *MysteryPlacePlayer) XXX_DiscardUnknown() {
	xxx_messageInfo_MysteryPlacePlayer.DiscardUnknown(m)
}

var xxx_messageInfo_MysteryPlacePlayer proto.InternalMessageInfo

func (m *MysteryPlacePlayer) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MysteryPlacePlayer) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *MysteryPlacePlayer) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

type MysteryPlaceLoadMore struct {
	LastPage             uint32   `protobuf:"varint,1,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	LastCount            uint32   `protobuf:"varint,2,opt,name=last_count,json=lastCount,proto3" json:"last_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MysteryPlaceLoadMore) Reset()         { *m = MysteryPlaceLoadMore{} }
func (m *MysteryPlaceLoadMore) String() string { return proto.CompactTextString(m) }
func (*MysteryPlaceLoadMore) ProtoMessage()    {}
func (*MysteryPlaceLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{93}
}
func (m *MysteryPlaceLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MysteryPlaceLoadMore.Unmarshal(m, b)
}
func (m *MysteryPlaceLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MysteryPlaceLoadMore.Marshal(b, m, deterministic)
}
func (dst *MysteryPlaceLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MysteryPlaceLoadMore.Merge(dst, src)
}
func (m *MysteryPlaceLoadMore) XXX_Size() int {
	return xxx_messageInfo_MysteryPlaceLoadMore.Size(m)
}
func (m *MysteryPlaceLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_MysteryPlaceLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_MysteryPlaceLoadMore proto.InternalMessageInfo

func (m *MysteryPlaceLoadMore) GetLastPage() uint32 {
	if m != nil {
		return m.LastPage
	}
	return 0
}

func (m *MysteryPlaceLoadMore) GetLastCount() uint32 {
	if m != nil {
		return m.LastCount
	}
	return 0
}

// 获取剧本章节概要
type GetScenarioChapterSummaryReq struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Chapter              uint32   `protobuf:"varint,2,opt,name=chapter,proto3" json:"chapter,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	PlaymateUid          uint32   `protobuf:"varint,4,opt,name=playmateUid,proto3" json:"playmateUid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScenarioChapterSummaryReq) Reset()         { *m = GetScenarioChapterSummaryReq{} }
func (m *GetScenarioChapterSummaryReq) String() string { return proto.CompactTextString(m) }
func (*GetScenarioChapterSummaryReq) ProtoMessage()    {}
func (*GetScenarioChapterSummaryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{94}
}
func (m *GetScenarioChapterSummaryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenarioChapterSummaryReq.Unmarshal(m, b)
}
func (m *GetScenarioChapterSummaryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenarioChapterSummaryReq.Marshal(b, m, deterministic)
}
func (dst *GetScenarioChapterSummaryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenarioChapterSummaryReq.Merge(dst, src)
}
func (m *GetScenarioChapterSummaryReq) XXX_Size() int {
	return xxx_messageInfo_GetScenarioChapterSummaryReq.Size(m)
}
func (m *GetScenarioChapterSummaryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenarioChapterSummaryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenarioChapterSummaryReq proto.InternalMessageInfo

func (m *GetScenarioChapterSummaryReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetScenarioChapterSummaryReq) GetChapter() uint32 {
	if m != nil {
		return m.Chapter
	}
	return 0
}

func (m *GetScenarioChapterSummaryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetScenarioChapterSummaryReq) GetPlaymateUid() uint32 {
	if m != nil {
		return m.PlaymateUid
	}
	return 0
}

type GetScenarioChapterSummaryResp struct {
	ChapterFragments     []string `protobuf:"bytes,1,rep,name=chapter_fragments,json=chapterFragments,proto3" json:"chapter_fragments,omitempty"`
	Summary              string   `protobuf:"bytes,2,opt,name=summary,proto3" json:"summary,omitempty"`
	IsVisible            bool     `protobuf:"varint,3,opt,name=is_visible,json=isVisible,proto3" json:"is_visible,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScenarioChapterSummaryResp) Reset()         { *m = GetScenarioChapterSummaryResp{} }
func (m *GetScenarioChapterSummaryResp) String() string { return proto.CompactTextString(m) }
func (*GetScenarioChapterSummaryResp) ProtoMessage()    {}
func (*GetScenarioChapterSummaryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{95}
}
func (m *GetScenarioChapterSummaryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenarioChapterSummaryResp.Unmarshal(m, b)
}
func (m *GetScenarioChapterSummaryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenarioChapterSummaryResp.Marshal(b, m, deterministic)
}
func (dst *GetScenarioChapterSummaryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenarioChapterSummaryResp.Merge(dst, src)
}
func (m *GetScenarioChapterSummaryResp) XXX_Size() int {
	return xxx_messageInfo_GetScenarioChapterSummaryResp.Size(m)
}
func (m *GetScenarioChapterSummaryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenarioChapterSummaryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenarioChapterSummaryResp proto.InternalMessageInfo

func (m *GetScenarioChapterSummaryResp) GetChapterFragments() []string {
	if m != nil {
		return m.ChapterFragments
	}
	return nil
}

func (m *GetScenarioChapterSummaryResp) GetSummary() string {
	if m != nil {
		return m.Summary
	}
	return ""
}

func (m *GetScenarioChapterSummaryResp) GetIsVisible() bool {
	if m != nil {
		return m.IsVisible
	}
	return false
}

// 设置记录可见
type SetPlayedScenarioRecordVisibilityReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	PlaymateUid          uint32   `protobuf:"varint,3,opt,name=playmate_uid,json=playmateUid,proto3" json:"playmate_uid,omitempty"`
	IsVisible            bool     `protobuf:"varint,4,opt,name=is_visible,json=isVisible,proto3" json:"is_visible,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPlayedScenarioRecordVisibilityReq) Reset()         { *m = SetPlayedScenarioRecordVisibilityReq{} }
func (m *SetPlayedScenarioRecordVisibilityReq) String() string { return proto.CompactTextString(m) }
func (*SetPlayedScenarioRecordVisibilityReq) ProtoMessage()    {}
func (*SetPlayedScenarioRecordVisibilityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{96}
}
func (m *SetPlayedScenarioRecordVisibilityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPlayedScenarioRecordVisibilityReq.Unmarshal(m, b)
}
func (m *SetPlayedScenarioRecordVisibilityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPlayedScenarioRecordVisibilityReq.Marshal(b, m, deterministic)
}
func (dst *SetPlayedScenarioRecordVisibilityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPlayedScenarioRecordVisibilityReq.Merge(dst, src)
}
func (m *SetPlayedScenarioRecordVisibilityReq) XXX_Size() int {
	return xxx_messageInfo_SetPlayedScenarioRecordVisibilityReq.Size(m)
}
func (m *SetPlayedScenarioRecordVisibilityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPlayedScenarioRecordVisibilityReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPlayedScenarioRecordVisibilityReq proto.InternalMessageInfo

func (m *SetPlayedScenarioRecordVisibilityReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetPlayedScenarioRecordVisibilityReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetPlayedScenarioRecordVisibilityReq) GetPlaymateUid() uint32 {
	if m != nil {
		return m.PlaymateUid
	}
	return 0
}

func (m *SetPlayedScenarioRecordVisibilityReq) GetIsVisible() bool {
	if m != nil {
		return m.IsVisible
	}
	return false
}

type SetPlayedScenarioRecordVisibilityResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPlayedScenarioRecordVisibilityResp) Reset()         { *m = SetPlayedScenarioRecordVisibilityResp{} }
func (m *SetPlayedScenarioRecordVisibilityResp) String() string { return proto.CompactTextString(m) }
func (*SetPlayedScenarioRecordVisibilityResp) ProtoMessage()    {}
func (*SetPlayedScenarioRecordVisibilityResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{97}
}
func (m *SetPlayedScenarioRecordVisibilityResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPlayedScenarioRecordVisibilityResp.Unmarshal(m, b)
}
func (m *SetPlayedScenarioRecordVisibilityResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPlayedScenarioRecordVisibilityResp.Marshal(b, m, deterministic)
}
func (dst *SetPlayedScenarioRecordVisibilityResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPlayedScenarioRecordVisibilityResp.Merge(dst, src)
}
func (m *SetPlayedScenarioRecordVisibilityResp) XXX_Size() int {
	return xxx_messageInfo_SetPlayedScenarioRecordVisibilityResp.Size(m)
}
func (m *SetPlayedScenarioRecordVisibilityResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPlayedScenarioRecordVisibilityResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPlayedScenarioRecordVisibilityResp proto.InternalMessageInfo

// 获取剧本前情提要和下章预告
type GetScenarioChapterReq struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScenarioChapterReq) Reset()         { *m = GetScenarioChapterReq{} }
func (m *GetScenarioChapterReq) String() string { return proto.CompactTextString(m) }
func (*GetScenarioChapterReq) ProtoMessage()    {}
func (*GetScenarioChapterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{98}
}
func (m *GetScenarioChapterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenarioChapterReq.Unmarshal(m, b)
}
func (m *GetScenarioChapterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenarioChapterReq.Marshal(b, m, deterministic)
}
func (dst *GetScenarioChapterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenarioChapterReq.Merge(dst, src)
}
func (m *GetScenarioChapterReq) XXX_Size() int {
	return xxx_messageInfo_GetScenarioChapterReq.Size(m)
}
func (m *GetScenarioChapterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenarioChapterReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenarioChapterReq proto.InternalMessageInfo

func (m *GetScenarioChapterReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type AddFeedbackReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Url                  string   `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	Name                 string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Account              string   `protobuf:"bytes,5,opt,name=account,proto3" json:"account,omitempty"`
	Channel              string   `protobuf:"bytes,6,opt,name=channel,proto3" json:"channel,omitempty"`
	DeviceId             string   `protobuf:"bytes,7,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Ip                   string   `protobuf:"bytes,8,opt,name=ip,proto3" json:"ip,omitempty"`
	Os                   string   `protobuf:"bytes,9,opt,name=os,proto3" json:"os,omitempty"`
	Version              string   `protobuf:"bytes,10,opt,name=version,proto3" json:"version,omitempty"`
	CreatedTime          uint32   `protobuf:"varint,11,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddFeedbackReq) Reset()         { *m = AddFeedbackReq{} }
func (m *AddFeedbackReq) String() string { return proto.CompactTextString(m) }
func (*AddFeedbackReq) ProtoMessage()    {}
func (*AddFeedbackReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{99}
}
func (m *AddFeedbackReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFeedbackReq.Unmarshal(m, b)
}
func (m *AddFeedbackReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFeedbackReq.Marshal(b, m, deterministic)
}
func (dst *AddFeedbackReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFeedbackReq.Merge(dst, src)
}
func (m *AddFeedbackReq) XXX_Size() int {
	return xxx_messageInfo_AddFeedbackReq.Size(m)
}
func (m *AddFeedbackReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFeedbackReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddFeedbackReq proto.InternalMessageInfo

func (m *AddFeedbackReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddFeedbackReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *AddFeedbackReq) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *AddFeedbackReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AddFeedbackReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *AddFeedbackReq) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *AddFeedbackReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *AddFeedbackReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *AddFeedbackReq) GetOs() string {
	if m != nil {
		return m.Os
	}
	return ""
}

func (m *AddFeedbackReq) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *AddFeedbackReq) GetCreatedTime() uint32 {
	if m != nil {
		return m.CreatedTime
	}
	return 0
}

type AddFeedbackResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddFeedbackResp) Reset()         { *m = AddFeedbackResp{} }
func (m *AddFeedbackResp) String() string { return proto.CompactTextString(m) }
func (*AddFeedbackResp) ProtoMessage()    {}
func (*AddFeedbackResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{100}
}
func (m *AddFeedbackResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFeedbackResp.Unmarshal(m, b)
}
func (m *AddFeedbackResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFeedbackResp.Marshal(b, m, deterministic)
}
func (dst *AddFeedbackResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFeedbackResp.Merge(dst, src)
}
func (m *AddFeedbackResp) XXX_Size() int {
	return xxx_messageInfo_AddFeedbackResp.Size(m)
}
func (m *AddFeedbackResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFeedbackResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddFeedbackResp proto.InternalMessageInfo

type Res struct {
	Type                 ResType  `protobuf:"varint,1,opt,name=type,proto3,enum=mystery_place.ResType" json:"type,omitempty"`
	Addr                 string   `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Md5                  string   `protobuf:"bytes,3,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Res) Reset()         { *m = Res{} }
func (m *Res) String() string { return proto.CompactTextString(m) }
func (*Res) ProtoMessage()    {}
func (*Res) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{101}
}
func (m *Res) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Res.Unmarshal(m, b)
}
func (m *Res) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Res.Marshal(b, m, deterministic)
}
func (dst *Res) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Res.Merge(dst, src)
}
func (m *Res) XXX_Size() int {
	return xxx_messageInfo_Res.Size(m)
}
func (m *Res) XXX_DiscardUnknown() {
	xxx_messageInfo_Res.DiscardUnknown(m)
}

var xxx_messageInfo_Res proto.InternalMessageInfo

func (m *Res) GetType() ResType {
	if m != nil {
		return m.Type
	}
	return ResType_ResType_UNDEFINED
}

func (m *Res) GetAddr() string {
	if m != nil {
		return m.Addr
	}
	return ""
}

func (m *Res) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

type GetScenarioChapterResp struct {
	ChapterList          []*ChapterSummary `protobuf:"bytes,1,rep,name=chapter_list,json=chapterList,proto3" json:"chapter_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetScenarioChapterResp) Reset()         { *m = GetScenarioChapterResp{} }
func (m *GetScenarioChapterResp) String() string { return proto.CompactTextString(m) }
func (*GetScenarioChapterResp) ProtoMessage()    {}
func (*GetScenarioChapterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{102}
}
func (m *GetScenarioChapterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenarioChapterResp.Unmarshal(m, b)
}
func (m *GetScenarioChapterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenarioChapterResp.Marshal(b, m, deterministic)
}
func (dst *GetScenarioChapterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenarioChapterResp.Merge(dst, src)
}
func (m *GetScenarioChapterResp) XXX_Size() int {
	return xxx_messageInfo_GetScenarioChapterResp.Size(m)
}
func (m *GetScenarioChapterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenarioChapterResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenarioChapterResp proto.InternalMessageInfo

func (m *GetScenarioChapterResp) GetChapterList() []*ChapterSummary {
	if m != nil {
		return m.ChapterList
	}
	return nil
}

type ChapterSummary struct {
	Chapter              uint32   `protobuf:"varint,1,opt,name=chapter,proto3" json:"chapter,omitempty"`
	ChapterDesc          []*Res   `protobuf:"bytes,2,rep,name=chapter_desc,json=chapterDesc,proto3" json:"chapter_desc,omitempty"`
	ChapterAdvanceNotice []*Res   `protobuf:"bytes,3,rep,name=chapter_advance_notice,json=chapterAdvanceNotice,proto3" json:"chapter_advance_notice,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChapterSummary) Reset()         { *m = ChapterSummary{} }
func (m *ChapterSummary) String() string { return proto.CompactTextString(m) }
func (*ChapterSummary) ProtoMessage()    {}
func (*ChapterSummary) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{103}
}
func (m *ChapterSummary) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChapterSummary.Unmarshal(m, b)
}
func (m *ChapterSummary) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChapterSummary.Marshal(b, m, deterministic)
}
func (dst *ChapterSummary) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChapterSummary.Merge(dst, src)
}
func (m *ChapterSummary) XXX_Size() int {
	return xxx_messageInfo_ChapterSummary.Size(m)
}
func (m *ChapterSummary) XXX_DiscardUnknown() {
	xxx_messageInfo_ChapterSummary.DiscardUnknown(m)
}

var xxx_messageInfo_ChapterSummary proto.InternalMessageInfo

func (m *ChapterSummary) GetChapter() uint32 {
	if m != nil {
		return m.Chapter
	}
	return 0
}

func (m *ChapterSummary) GetChapterDesc() []*Res {
	if m != nil {
		return m.ChapterDesc
	}
	return nil
}

func (m *ChapterSummary) GetChapterAdvanceNotice() []*Res {
	if m != nil {
		return m.ChapterAdvanceNotice
	}
	return nil
}

type AddScenarioChapterReq struct {
	ScenarioId           uint32   `protobuf:"varint,1,opt,name=scenario_id,json=scenarioId,proto3" json:"scenario_id,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Chapter              uint32   `protobuf:"varint,3,opt,name=chapter,proto3" json:"chapter,omitempty"`
	Fragments            []*Res   `protobuf:"bytes,4,rep,name=fragments,proto3" json:"fragments,omitempty"`
	ChapterDesc          []*Res   `protobuf:"bytes,5,rep,name=chapter_desc,json=chapterDesc,proto3" json:"chapter_desc,omitempty"`
	ChapterAdvanceNotice []*Res   `protobuf:"bytes,6,rep,name=chapter_advance_notice,json=chapterAdvanceNotice,proto3" json:"chapter_advance_notice,omitempty"`
	Summary              string   `protobuf:"bytes,7,opt,name=summary,proto3" json:"summary,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddScenarioChapterReq) Reset()         { *m = AddScenarioChapterReq{} }
func (m *AddScenarioChapterReq) String() string { return proto.CompactTextString(m) }
func (*AddScenarioChapterReq) ProtoMessage()    {}
func (*AddScenarioChapterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{104}
}
func (m *AddScenarioChapterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddScenarioChapterReq.Unmarshal(m, b)
}
func (m *AddScenarioChapterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddScenarioChapterReq.Marshal(b, m, deterministic)
}
func (dst *AddScenarioChapterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddScenarioChapterReq.Merge(dst, src)
}
func (m *AddScenarioChapterReq) XXX_Size() int {
	return xxx_messageInfo_AddScenarioChapterReq.Size(m)
}
func (m *AddScenarioChapterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddScenarioChapterReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddScenarioChapterReq proto.InternalMessageInfo

func (m *AddScenarioChapterReq) GetScenarioId() uint32 {
	if m != nil {
		return m.ScenarioId
	}
	return 0
}

func (m *AddScenarioChapterReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *AddScenarioChapterReq) GetChapter() uint32 {
	if m != nil {
		return m.Chapter
	}
	return 0
}

func (m *AddScenarioChapterReq) GetFragments() []*Res {
	if m != nil {
		return m.Fragments
	}
	return nil
}

func (m *AddScenarioChapterReq) GetChapterDesc() []*Res {
	if m != nil {
		return m.ChapterDesc
	}
	return nil
}

func (m *AddScenarioChapterReq) GetChapterAdvanceNotice() []*Res {
	if m != nil {
		return m.ChapterAdvanceNotice
	}
	return nil
}

func (m *AddScenarioChapterReq) GetSummary() string {
	if m != nil {
		return m.Summary
	}
	return ""
}

type AddScenarioChapterResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddScenarioChapterResp) Reset()         { *m = AddScenarioChapterResp{} }
func (m *AddScenarioChapterResp) String() string { return proto.CompactTextString(m) }
func (*AddScenarioChapterResp) ProtoMessage()    {}
func (*AddScenarioChapterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{105}
}
func (m *AddScenarioChapterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddScenarioChapterResp.Unmarshal(m, b)
}
func (m *AddScenarioChapterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddScenarioChapterResp.Marshal(b, m, deterministic)
}
func (dst *AddScenarioChapterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddScenarioChapterResp.Merge(dst, src)
}
func (m *AddScenarioChapterResp) XXX_Size() int {
	return xxx_messageInfo_AddScenarioChapterResp.Size(m)
}
func (m *AddScenarioChapterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddScenarioChapterResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddScenarioChapterResp proto.InternalMessageInfo

// 推荐玩法
type RcmdTab struct {
	Sex                  UserSex  `protobuf:"varint,1,opt,name=sex,proto3,enum=mystery_place.UserSex" json:"sex,omitempty"`
	RcmdTabId            uint32   `protobuf:"varint,2,opt,name=rcmd_tab_id,json=rcmdTabId,proto3" json:"rcmd_tab_id,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Tip                  string   `protobuf:"bytes,4,opt,name=tip,proto3" json:"tip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RcmdTab) Reset()         { *m = RcmdTab{} }
func (m *RcmdTab) String() string { return proto.CompactTextString(m) }
func (*RcmdTab) ProtoMessage()    {}
func (*RcmdTab) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{106}
}
func (m *RcmdTab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RcmdTab.Unmarshal(m, b)
}
func (m *RcmdTab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RcmdTab.Marshal(b, m, deterministic)
}
func (dst *RcmdTab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RcmdTab.Merge(dst, src)
}
func (m *RcmdTab) XXX_Size() int {
	return xxx_messageInfo_RcmdTab.Size(m)
}
func (m *RcmdTab) XXX_DiscardUnknown() {
	xxx_messageInfo_RcmdTab.DiscardUnknown(m)
}

var xxx_messageInfo_RcmdTab proto.InternalMessageInfo

func (m *RcmdTab) GetSex() UserSex {
	if m != nil {
		return m.Sex
	}
	return UserSex_UserSex_FEMALE
}

func (m *RcmdTab) GetRcmdTabId() uint32 {
	if m != nil {
		return m.RcmdTabId
	}
	return 0
}

func (m *RcmdTab) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *RcmdTab) GetTip() string {
	if m != nil {
		return m.Tip
	}
	return ""
}

// 更新推荐玩法（增/删/排序）
type UpdateTabSetReq struct {
	TabSet               *TabSet  `protobuf:"bytes,1,opt,name=tab_set,json=tabSet,proto3" json:"tab_set,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTabSetReq) Reset()         { *m = UpdateTabSetReq{} }
func (m *UpdateTabSetReq) String() string { return proto.CompactTextString(m) }
func (*UpdateTabSetReq) ProtoMessage()    {}
func (*UpdateTabSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{107}
}
func (m *UpdateTabSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTabSetReq.Unmarshal(m, b)
}
func (m *UpdateTabSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTabSetReq.Marshal(b, m, deterministic)
}
func (dst *UpdateTabSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTabSetReq.Merge(dst, src)
}
func (m *UpdateTabSetReq) XXX_Size() int {
	return xxx_messageInfo_UpdateTabSetReq.Size(m)
}
func (m *UpdateTabSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTabSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTabSetReq proto.InternalMessageInfo

func (m *UpdateTabSetReq) GetTabSet() *TabSet {
	if m != nil {
		return m.TabSet
	}
	return nil
}

type UpdateTabSetResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTabSetResp) Reset()         { *m = UpdateTabSetResp{} }
func (m *UpdateTabSetResp) String() string { return proto.CompactTextString(m) }
func (*UpdateTabSetResp) ProtoMessage()    {}
func (*UpdateTabSetResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{108}
}
func (m *UpdateTabSetResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTabSetResp.Unmarshal(m, b)
}
func (m *UpdateTabSetResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTabSetResp.Marshal(b, m, deterministic)
}
func (dst *UpdateTabSetResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTabSetResp.Merge(dst, src)
}
func (m *UpdateTabSetResp) XXX_Size() int {
	return xxx_messageInfo_UpdateTabSetResp.Size(m)
}
func (m *UpdateTabSetResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTabSetResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTabSetResp proto.InternalMessageInfo

type TabSet struct {
	TabId                uint32     `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	IsSexValid           bool       `protobuf:"varint,2,opt,name=is_sex_valid,json=isSexValid,proto3" json:"is_sex_valid,omitempty"`
	Tabs                 []*RcmdTab `protobuf:"bytes,3,rep,name=tabs,proto3" json:"tabs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *TabSet) Reset()         { *m = TabSet{} }
func (m *TabSet) String() string { return proto.CompactTextString(m) }
func (*TabSet) ProtoMessage()    {}
func (*TabSet) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{109}
}
func (m *TabSet) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TabSet.Unmarshal(m, b)
}
func (m *TabSet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TabSet.Marshal(b, m, deterministic)
}
func (dst *TabSet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabSet.Merge(dst, src)
}
func (m *TabSet) XXX_Size() int {
	return xxx_messageInfo_TabSet.Size(m)
}
func (m *TabSet) XXX_DiscardUnknown() {
	xxx_messageInfo_TabSet.DiscardUnknown(m)
}

var xxx_messageInfo_TabSet proto.InternalMessageInfo

func (m *TabSet) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *TabSet) GetIsSexValid() bool {
	if m != nil {
		return m.IsSexValid
	}
	return false
}

func (m *TabSet) GetTabs() []*RcmdTab {
	if m != nil {
		return m.Tabs
	}
	return nil
}

// 批量获取推荐tab
type BatchGetRcmdTabByIdReq struct {
	TabIds               []uint32 `protobuf:"varint,1,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetRcmdTabByIdReq) Reset()         { *m = BatchGetRcmdTabByIdReq{} }
func (m *BatchGetRcmdTabByIdReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetRcmdTabByIdReq) ProtoMessage()    {}
func (*BatchGetRcmdTabByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{110}
}
func (m *BatchGetRcmdTabByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRcmdTabByIdReq.Unmarshal(m, b)
}
func (m *BatchGetRcmdTabByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRcmdTabByIdReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetRcmdTabByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRcmdTabByIdReq.Merge(dst, src)
}
func (m *BatchGetRcmdTabByIdReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetRcmdTabByIdReq.Size(m)
}
func (m *BatchGetRcmdTabByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRcmdTabByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRcmdTabByIdReq proto.InternalMessageInfo

func (m *BatchGetRcmdTabByIdReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type BatchGetRcmdTabByIdResp struct {
	TabSets              []*TabSet `protobuf:"bytes,1,rep,name=tab_sets,json=tabSets,proto3" json:"tab_sets,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *BatchGetRcmdTabByIdResp) Reset()         { *m = BatchGetRcmdTabByIdResp{} }
func (m *BatchGetRcmdTabByIdResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetRcmdTabByIdResp) ProtoMessage()    {}
func (*BatchGetRcmdTabByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{111}
}
func (m *BatchGetRcmdTabByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRcmdTabByIdResp.Unmarshal(m, b)
}
func (m *BatchGetRcmdTabByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRcmdTabByIdResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetRcmdTabByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRcmdTabByIdResp.Merge(dst, src)
}
func (m *BatchGetRcmdTabByIdResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetRcmdTabByIdResp.Size(m)
}
func (m *BatchGetRcmdTabByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRcmdTabByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRcmdTabByIdResp proto.InternalMessageInfo

func (m *BatchGetRcmdTabByIdResp) GetTabSets() []*TabSet {
	if m != nil {
		return m.TabSets
	}
	return nil
}

// 拨测成都用proto
type CommonReq struct {
	Uri                  string   `protobuf:"bytes,1,opt,name=uri,proto3" json:"uri,omitempty"`
	ReqId                string   `protobuf:"bytes,2,opt,name=reqId,proto3" json:"reqId,omitempty"`
	Req                  string   `protobuf:"bytes,3,opt,name=req,proto3" json:"req,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonReq) Reset()         { *m = CommonReq{} }
func (m *CommonReq) String() string { return proto.CompactTextString(m) }
func (*CommonReq) ProtoMessage()    {}
func (*CommonReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{112}
}
func (m *CommonReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonReq.Unmarshal(m, b)
}
func (m *CommonReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonReq.Marshal(b, m, deterministic)
}
func (dst *CommonReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonReq.Merge(dst, src)
}
func (m *CommonReq) XXX_Size() int {
	return xxx_messageInfo_CommonReq.Size(m)
}
func (m *CommonReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommonReq proto.InternalMessageInfo

func (m *CommonReq) GetUri() string {
	if m != nil {
		return m.Uri
	}
	return ""
}

func (m *CommonReq) GetReqId() string {
	if m != nil {
		return m.ReqId
	}
	return ""
}

func (m *CommonReq) GetReq() string {
	if m != nil {
		return m.Req
	}
	return ""
}

type CommonResp struct {
	Uri                  string   `protobuf:"bytes,1,opt,name=uri,proto3" json:"uri,omitempty"`
	MessageId            string   `protobuf:"bytes,2,opt,name=messageId,proto3" json:"messageId,omitempty"`
	NeedDecrypt          bool     `protobuf:"varint,3,opt,name=needDecrypt,proto3" json:"needDecrypt,omitempty"`
	Message              string   `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonResp) Reset()         { *m = CommonResp{} }
func (m *CommonResp) String() string { return proto.CompactTextString(m) }
func (*CommonResp) ProtoMessage()    {}
func (*CommonResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{113}
}
func (m *CommonResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonResp.Unmarshal(m, b)
}
func (m *CommonResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonResp.Marshal(b, m, deterministic)
}
func (dst *CommonResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonResp.Merge(dst, src)
}
func (m *CommonResp) XXX_Size() int {
	return xxx_messageInfo_CommonResp.Size(m)
}
func (m *CommonResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonResp.DiscardUnknown(m)
}

var xxx_messageInfo_CommonResp proto.InternalMessageInfo

func (m *CommonResp) GetUri() string {
	if m != nil {
		return m.Uri
	}
	return ""
}

func (m *CommonResp) GetMessageId() string {
	if m != nil {
		return m.MessageId
	}
	return ""
}

func (m *CommonResp) GetNeedDecrypt() bool {
	if m != nil {
		return m.NeedDecrypt
	}
	return false
}

func (m *CommonResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type SeriesInfo struct {
	// 成都系列ID
	SeriesId string `protobuf:"bytes,1,opt,name=series_id,json=seriesId,proto3" json:"series_id,omitempty"`
	// 系列名称
	SeriesName string `protobuf:"bytes,2,opt,name=series_name,json=seriesName,proto3" json:"series_name,omitempty"`
	// TT系列ID
	TtSeriesId           uint32       `protobuf:"varint,3,opt,name=tt_series_id,json=ttSeriesId,proto3" json:"tt_series_id,omitempty"`
	DramaList            []*DramaInfo `protobuf:"bytes,4,rep,name=drama_list,json=dramaList,proto3" json:"drama_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SeriesInfo) Reset()         { *m = SeriesInfo{} }
func (m *SeriesInfo) String() string { return proto.CompactTextString(m) }
func (*SeriesInfo) ProtoMessage()    {}
func (*SeriesInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{114}
}
func (m *SeriesInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeriesInfo.Unmarshal(m, b)
}
func (m *SeriesInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeriesInfo.Marshal(b, m, deterministic)
}
func (dst *SeriesInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeriesInfo.Merge(dst, src)
}
func (m *SeriesInfo) XXX_Size() int {
	return xxx_messageInfo_SeriesInfo.Size(m)
}
func (m *SeriesInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SeriesInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SeriesInfo proto.InternalMessageInfo

func (m *SeriesInfo) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *SeriesInfo) GetSeriesName() string {
	if m != nil {
		return m.SeriesName
	}
	return ""
}

func (m *SeriesInfo) GetTtSeriesId() uint32 {
	if m != nil {
		return m.TtSeriesId
	}
	return 0
}

func (m *SeriesInfo) GetDramaList() []*DramaInfo {
	if m != nil {
		return m.DramaList
	}
	return nil
}

type DramaInfo struct {
	// 剧本id
	DramaId string `protobuf:"bytes,1,opt,name=drama_id,json=dramaId,proto3" json:"drama_id,omitempty"`
	// 剧本名称
	DramaName string `protobuf:"bytes,2,opt,name=drama_name,json=dramaName,proto3" json:"drama_name,omitempty"`
	TabId     uint32 `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	// 幕列表
	PlotList []*PlotInfo `protobuf:"bytes,4,rep,name=plot_list,json=plotList,proto3" json:"plot_list,omitempty"`
	// 接口有返回，要自己转成tabId
	TtTabId string `protobuf:"bytes,5,opt,name=tt_tab_id,json=ttTabId,proto3" json:"tt_tab_id,omitempty"`
	// 原始金额
	TicketOriginCount uint32 `protobuf:"varint,6,opt,name=ticket_origin_count,json=ticketOriginCount,proto3" json:"ticket_origin_count,omitempty"`
	// 打包金额
	TicketPackCount      uint32   `protobuf:"varint,7,opt,name=ticket_pack_count,json=ticketPackCount,proto3" json:"ticket_pack_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DramaInfo) Reset()         { *m = DramaInfo{} }
func (m *DramaInfo) String() string { return proto.CompactTextString(m) }
func (*DramaInfo) ProtoMessage()    {}
func (*DramaInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{115}
}
func (m *DramaInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DramaInfo.Unmarshal(m, b)
}
func (m *DramaInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DramaInfo.Marshal(b, m, deterministic)
}
func (dst *DramaInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DramaInfo.Merge(dst, src)
}
func (m *DramaInfo) XXX_Size() int {
	return xxx_messageInfo_DramaInfo.Size(m)
}
func (m *DramaInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DramaInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DramaInfo proto.InternalMessageInfo

func (m *DramaInfo) GetDramaId() string {
	if m != nil {
		return m.DramaId
	}
	return ""
}

func (m *DramaInfo) GetDramaName() string {
	if m != nil {
		return m.DramaName
	}
	return ""
}

func (m *DramaInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *DramaInfo) GetPlotList() []*PlotInfo {
	if m != nil {
		return m.PlotList
	}
	return nil
}

func (m *DramaInfo) GetTtTabId() string {
	if m != nil {
		return m.TtTabId
	}
	return ""
}

func (m *DramaInfo) GetTicketOriginCount() uint32 {
	if m != nil {
		return m.TicketOriginCount
	}
	return 0
}

func (m *DramaInfo) GetTicketPackCount() uint32 {
	if m != nil {
		return m.TicketPackCount
	}
	return 0
}

type PlotInfo struct {
	// 幕的顺序
	Index uint32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	// 幕的id
	PlotId string `protobuf:"bytes,2,opt,name=plot_id,json=plotId,proto3" json:"plot_id,omitempty"`
	// 幕的名称
	PlotName string `protobuf:"bytes,3,opt,name=plot_name,json=plotName,proto3" json:"plot_name,omitempty"`
	// 前面的幕，初始第一幕为null
	ParentIds []string `protobuf:"bytes,4,rep,name=parent_ids,json=parentIds,proto3" json:"parent_ids,omitempty"`
	// 单金额
	TicketCount uint32 `protobuf:"varint,5,opt,name=ticket_count,json=ticketCount,proto3" json:"ticket_count,omitempty"`
	// 原始金额
	TicketOriginCount uint32 `protobuf:"varint,6,opt,name=ticket_origin_count,json=ticketOriginCount,proto3" json:"ticket_origin_count,omitempty"`
	// 打包金额
	TicketPackCount      uint32   `protobuf:"varint,7,opt,name=ticket_pack_count,json=ticketPackCount,proto3" json:"ticket_pack_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlotInfo) Reset()         { *m = PlotInfo{} }
func (m *PlotInfo) String() string { return proto.CompactTextString(m) }
func (*PlotInfo) ProtoMessage()    {}
func (*PlotInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{116}
}
func (m *PlotInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlotInfo.Unmarshal(m, b)
}
func (m *PlotInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlotInfo.Marshal(b, m, deterministic)
}
func (dst *PlotInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlotInfo.Merge(dst, src)
}
func (m *PlotInfo) XXX_Size() int {
	return xxx_messageInfo_PlotInfo.Size(m)
}
func (m *PlotInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PlotInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PlotInfo proto.InternalMessageInfo

func (m *PlotInfo) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *PlotInfo) GetPlotId() string {
	if m != nil {
		return m.PlotId
	}
	return ""
}

func (m *PlotInfo) GetPlotName() string {
	if m != nil {
		return m.PlotName
	}
	return ""
}

func (m *PlotInfo) GetParentIds() []string {
	if m != nil {
		return m.ParentIds
	}
	return nil
}

func (m *PlotInfo) GetTicketCount() uint32 {
	if m != nil {
		return m.TicketCount
	}
	return 0
}

func (m *PlotInfo) GetTicketOriginCount() uint32 {
	if m != nil {
		return m.TicketOriginCount
	}
	return 0
}

func (m *PlotInfo) GetTicketPackCount() uint32 {
	if m != nil {
		return m.TicketPackCount
	}
	return 0
}

// 获取剧本热度值排名
type GetScenarioHotRankReq struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScenarioHotRankReq) Reset()         { *m = GetScenarioHotRankReq{} }
func (m *GetScenarioHotRankReq) String() string { return proto.CompactTextString(m) }
func (*GetScenarioHotRankReq) ProtoMessage()    {}
func (*GetScenarioHotRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{117}
}
func (m *GetScenarioHotRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenarioHotRankReq.Unmarshal(m, b)
}
func (m *GetScenarioHotRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenarioHotRankReq.Marshal(b, m, deterministic)
}
func (dst *GetScenarioHotRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenarioHotRankReq.Merge(dst, src)
}
func (m *GetScenarioHotRankReq) XXX_Size() int {
	return xxx_messageInfo_GetScenarioHotRankReq.Size(m)
}
func (m *GetScenarioHotRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenarioHotRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenarioHotRankReq proto.InternalMessageInfo

func (m *GetScenarioHotRankReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type ScenarioHotRank struct {
	ScenarioId           string   `protobuf:"bytes,1,opt,name=scenario_id,json=scenarioId,proto3" json:"scenario_id,omitempty"`
	HotScore             uint32   `protobuf:"varint,2,opt,name=hot_score,json=hotScore,proto3" json:"hot_score,omitempty"`
	PlayUsers            uint32   `protobuf:"varint,3,opt,name=play_users,json=playUsers,proto3" json:"play_users,omitempty"`
	ExtData              []string `protobuf:"bytes,4,rep,name=ext_data,json=extData,proto3" json:"ext_data,omitempty"`
	Exposures            uint32   `protobuf:"varint,5,opt,name=exposures,proto3" json:"exposures,omitempty"`
	Title                string   `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	Labels               []string `protobuf:"bytes,7,rep,name=labels,proto3" json:"labels,omitempty"`
	SmallPicture         string   `protobuf:"bytes,8,opt,name=small_picture,json=smallPicture,proto3" json:"small_picture,omitempty"`
	MinPlayerNum         uint32   `protobuf:"varint,9,opt,name=min_player_num,json=minPlayerNum,proto3" json:"min_player_num,omitempty"`
	Introduction         string   `protobuf:"bytes,10,opt,name=introduction,proto3" json:"introduction,omitempty"`
	RailBg               string   `protobuf:"bytes,11,opt,name=rail_bg,json=railBg,proto3" json:"rail_bg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScenarioHotRank) Reset()         { *m = ScenarioHotRank{} }
func (m *ScenarioHotRank) String() string { return proto.CompactTextString(m) }
func (*ScenarioHotRank) ProtoMessage()    {}
func (*ScenarioHotRank) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{118}
}
func (m *ScenarioHotRank) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScenarioHotRank.Unmarshal(m, b)
}
func (m *ScenarioHotRank) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScenarioHotRank.Marshal(b, m, deterministic)
}
func (dst *ScenarioHotRank) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScenarioHotRank.Merge(dst, src)
}
func (m *ScenarioHotRank) XXX_Size() int {
	return xxx_messageInfo_ScenarioHotRank.Size(m)
}
func (m *ScenarioHotRank) XXX_DiscardUnknown() {
	xxx_messageInfo_ScenarioHotRank.DiscardUnknown(m)
}

var xxx_messageInfo_ScenarioHotRank proto.InternalMessageInfo

func (m *ScenarioHotRank) GetScenarioId() string {
	if m != nil {
		return m.ScenarioId
	}
	return ""
}

func (m *ScenarioHotRank) GetHotScore() uint32 {
	if m != nil {
		return m.HotScore
	}
	return 0
}

func (m *ScenarioHotRank) GetPlayUsers() uint32 {
	if m != nil {
		return m.PlayUsers
	}
	return 0
}

func (m *ScenarioHotRank) GetExtData() []string {
	if m != nil {
		return m.ExtData
	}
	return nil
}

func (m *ScenarioHotRank) GetExposures() uint32 {
	if m != nil {
		return m.Exposures
	}
	return 0
}

func (m *ScenarioHotRank) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ScenarioHotRank) GetLabels() []string {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *ScenarioHotRank) GetSmallPicture() string {
	if m != nil {
		return m.SmallPicture
	}
	return ""
}

func (m *ScenarioHotRank) GetMinPlayerNum() uint32 {
	if m != nil {
		return m.MinPlayerNum
	}
	return 0
}

func (m *ScenarioHotRank) GetIntroduction() string {
	if m != nil {
		return m.Introduction
	}
	return ""
}

func (m *ScenarioHotRank) GetRailBg() string {
	if m != nil {
		return m.RailBg
	}
	return ""
}

type GetScenarioHotRankResp struct {
	InfoList             []*ScenarioHotRank `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	LimitDay             uint32             `protobuf:"varint,2,opt,name=limit_day,json=limitDay,proto3" json:"limit_day,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetScenarioHotRankResp) Reset()         { *m = GetScenarioHotRankResp{} }
func (m *GetScenarioHotRankResp) String() string { return proto.CompactTextString(m) }
func (*GetScenarioHotRankResp) ProtoMessage()    {}
func (*GetScenarioHotRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_c70a2cf0a0ebb671, []int{119}
}
func (m *GetScenarioHotRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenarioHotRankResp.Unmarshal(m, b)
}
func (m *GetScenarioHotRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenarioHotRankResp.Marshal(b, m, deterministic)
}
func (dst *GetScenarioHotRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenarioHotRankResp.Merge(dst, src)
}
func (m *GetScenarioHotRankResp) XXX_Size() int {
	return xxx_messageInfo_GetScenarioHotRankResp.Size(m)
}
func (m *GetScenarioHotRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenarioHotRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenarioHotRankResp proto.InternalMessageInfo

func (m *GetScenarioHotRankResp) GetInfoList() []*ScenarioHotRank {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetScenarioHotRankResp) GetLimitDay() uint32 {
	if m != nil {
		return m.LimitDay
	}
	return 0
}

func init() {
	proto.RegisterType((*PlaymateTag)(nil), "mystery_place.PlaymateTag")
	proto.RegisterType((*AddPlaymateTagReq)(nil), "mystery_place.AddPlaymateTagReq")
	proto.RegisterType((*AddPlaymateTagResp)(nil), "mystery_place.AddPlaymateTagResp")
	proto.RegisterType((*DeletePlaymateTagReq)(nil), "mystery_place.DeletePlaymateTagReq")
	proto.RegisterType((*DeletePlaymateTagResp)(nil), "mystery_place.DeletePlaymateTagResp")
	proto.RegisterType((*UpdatePlaymateTagReq)(nil), "mystery_place.UpdatePlaymateTagReq")
	proto.RegisterType((*UpdatePlaymateTagResp)(nil), "mystery_place.UpdatePlaymateTagResp")
	proto.RegisterType((*ReorderPlaymateTagsReq)(nil), "mystery_place.ReorderPlaymateTagsReq")
	proto.RegisterType((*ReorderPlaymateTagsResp)(nil), "mystery_place.ReorderPlaymateTagsResp")
	proto.RegisterType((*GetPlaymateTagsReq)(nil), "mystery_place.GetPlaymateTagsReq")
	proto.RegisterType((*GetPlaymateTagsResp)(nil), "mystery_place.GetPlaymateTagsResp")
	proto.RegisterType((*GetPlaymateTagByIdsReq)(nil), "mystery_place.GetPlaymateTagByIdsReq")
	proto.RegisterType((*GetPlaymateTagByIdsResp)(nil), "mystery_place.GetPlaymateTagByIdsResp")
	proto.RegisterType((*AddUserPlaymateTagReq)(nil), "mystery_place.AddUserPlaymateTagReq")
	proto.RegisterType((*AddUserPlaymateTagResp)(nil), "mystery_place.AddUserPlaymateTagResp")
	proto.RegisterType((*BatGetUserHotPlaymateTagReq)(nil), "mystery_place.BatGetUserHotPlaymateTagReq")
	proto.RegisterType((*HotPlaymateTag)(nil), "mystery_place.HotPlaymateTag")
	proto.RegisterType((*UserHotPlaymateTag)(nil), "mystery_place.UserHotPlaymateTag")
	proto.RegisterType((*BatGetUserHotPlaymateTagResp)(nil), "mystery_place.BatGetUserHotPlaymateTagResp")
	proto.RegisterType((*SetScenarioShareLinkReq)(nil), "mystery_place.SetScenarioShareLinkReq")
	proto.RegisterType((*SetScenarioShareLinkResp)(nil), "mystery_place.SetScenarioShareLinkResp")
	proto.RegisterType((*BatchGetScenarioShareLinkReq)(nil), "mystery_place.BatchGetScenarioShareLinkReq")
	proto.RegisterType((*BatchGetScenarioShareLinkResp)(nil), "mystery_place.BatchGetScenarioShareLinkResp")
	proto.RegisterType((*DelScenarioShareLinkReq)(nil), "mystery_place.DelScenarioShareLinkReq")
	proto.RegisterType((*DelScenarioShareLinkResp)(nil), "mystery_place.DelScenarioShareLinkResp")
	proto.RegisterType((*GetNewUsersReq)(nil), "mystery_place.GetNewUsersReq")
	proto.RegisterType((*GetNewUsersResp)(nil), "mystery_place.GetNewUsersResp")
	proto.RegisterType((*User)(nil), "mystery_place.User")
	proto.RegisterType((*ScenarioShareLink)(nil), "mystery_place.ScenarioShareLink")
	proto.RegisterType((*ABTestLink)(nil), "mystery_place.ABTestLink")
	proto.RegisterType((*ScenarioIncWeightInfo)(nil), "mystery_place.ScenarioIncWeightInfo")
	proto.RegisterType((*ScenarioInfo)(nil), "mystery_place.ScenarioInfo")
	proto.RegisterType((*ScenarioTabInfo)(nil), "mystery_place.ScenarioTabInfo")
	proto.RegisterType((*CharacterInfo)(nil), "mystery_place.CharacterInfo")
	proto.RegisterType((*ListScenarioInfoReq)(nil), "mystery_place.ListScenarioInfoReq")
	proto.RegisterType((*ListScenarioInfoResp)(nil), "mystery_place.ListScenarioInfoResp")
	proto.RegisterType((*GetScenarioInfoTotalCountReq)(nil), "mystery_place.GetScenarioInfoTotalCountReq")
	proto.RegisterType((*GetScenarioInfoTotalCountResp)(nil), "mystery_place.GetScenarioInfoTotalCountResp")
	proto.RegisterType((*GetScenarioInfoReq)(nil), "mystery_place.GetScenarioInfoReq")
	proto.RegisterType((*GetScenarioInfoResp)(nil), "mystery_place.GetScenarioInfoResp")
	proto.RegisterType((*DelScenarioInfoReq)(nil), "mystery_place.DelScenarioInfoReq")
	proto.RegisterType((*DelScenarioInfoResp)(nil), "mystery_place.DelScenarioInfoResp")
	proto.RegisterType((*UpsertScenarioInfoReq)(nil), "mystery_place.UpsertScenarioInfoReq")
	proto.RegisterType((*UpsertScenarioInfoResp)(nil), "mystery_place.UpsertScenarioInfoResp")
	proto.RegisterType((*IsNewUserReq)(nil), "mystery_place.IsNewUserReq")
	proto.RegisterType((*IsNewUserResp)(nil), "mystery_place.IsNewUserResp")
	proto.RegisterType((*BatchGetGamePercentByChannelIdsReq)(nil), "mystery_place.BatchGetGamePercentByChannelIdsReq")
	proto.RegisterType((*BatchGetGamePercentByChannelIdsResp)(nil), "mystery_place.BatchGetGamePercentByChannelIdsResp")
	proto.RegisterType((*GetPlayerLastRecordReq)(nil), "mystery_place.GetPlayerLastRecordReq")
	proto.RegisterType((*GetPlayerLastRecordResp)(nil), "mystery_place.GetPlayerLastRecordResp")
	proto.RegisterType((*BatchGetPlayerGamePercentByGameIdsReq)(nil), "mystery_place.BatchGetPlayerGamePercentByGameIdsReq")
	proto.RegisterType((*BatchGetPlayerGamePercentByGameIdsResp)(nil), "mystery_place.BatchGetPlayerGamePercentByGameIdsResp")
	proto.RegisterType((*BatchCheckHelperListReq)(nil), "mystery_place.BatchCheckHelperListReq")
	proto.RegisterType((*BatchCheckHelperListResp)(nil), "mystery_place.BatchCheckHelperListResp")
	proto.RegisterType((*GameCommonMsg)(nil), "mystery_place.GameCommonMsg")
	proto.RegisterType((*GameCommonData)(nil), "mystery_place.GameCommonData")
	proto.RegisterType((*RoomGamePercent)(nil), "mystery_place.RoomGamePercent")
	proto.RegisterType((*UserGamePercent)(nil), "mystery_place.UserGamePercent")
	proto.RegisterType((*UserHelpData)(nil), "mystery_place.UserHelpData")
	proto.RegisterType((*UserChapterData)(nil), "mystery_place.UserChapterData")
	proto.RegisterType((*UserGameTaskData)(nil), "mystery_place.UserGameTaskData")
	proto.RegisterType((*RearrangeScenarioReq)(nil), "mystery_place.RearrangeScenarioReq")
	proto.RegisterType((*RearrangeScenarioResp)(nil), "mystery_place.RearrangeScenarioResp")
	proto.RegisterType((*Invite2MyRoomResultPush)(nil), "mystery_place.Invite2MyRoomResultPush")
	proto.RegisterType((*UserCommentScenarioRecord)(nil), "mystery_place.UserCommentScenarioRecord")
	proto.RegisterType((*AddUserCommentScenarioReq)(nil), "mystery_place.AddUserCommentScenarioReq")
	proto.RegisterType((*AddUserCommentScenarioResp)(nil), "mystery_place.AddUserCommentScenarioResp")
	proto.RegisterType((*BatchGetUserCommentScenarioReq)(nil), "mystery_place.BatchGetUserCommentScenarioReq")
	proto.RegisterType((*BatchGetUserCommentScenarioResp)(nil), "mystery_place.BatchGetUserCommentScenarioResp")
	proto.RegisterType((*ListScenarioCommentReq)(nil), "mystery_place.ListScenarioCommentReq")
	proto.RegisterType((*ListScenarioCommentResp)(nil), "mystery_place.ListScenarioCommentResp")
	proto.RegisterType((*UpdateScenarioCommentReq)(nil), "mystery_place.UpdateScenarioCommentReq")
	proto.RegisterType((*UpdateScenarioCommentResp)(nil), "mystery_place.UpdateScenarioCommentResp")
	proto.RegisterType((*ScenarioCommentSummaryRecord)(nil), "mystery_place.ScenarioCommentSummaryRecord")
	proto.RegisterType((*ListScenarioCommentSummaryReq)(nil), "mystery_place.ListScenarioCommentSummaryReq")
	proto.RegisterType((*ListScenarioCommentSummaryResp)(nil), "mystery_place.ListScenarioCommentSummaryResp")
	proto.RegisterType((*SetScenarioManualAverageReq)(nil), "mystery_place.SetScenarioManualAverageReq")
	proto.RegisterType((*SetScenarioManualAverageResp)(nil), "mystery_place.SetScenarioManualAverageResp")
	proto.RegisterType((*RefreshCommentSummaryReq)(nil), "mystery_place.RefreshCommentSummaryReq")
	proto.RegisterType((*RefreshCommentSummaryResp)(nil), "mystery_place.RefreshCommentSummaryResp")
	proto.RegisterType((*NewScenarioTip)(nil), "mystery_place.NewScenarioTip")
	proto.RegisterType((*GetNewScenarioTipsReq)(nil), "mystery_place.GetNewScenarioTipsReq")
	proto.RegisterType((*GetNewScenarioTipsResp)(nil), "mystery_place.GetNewScenarioTipsResp")
	proto.RegisterType((*MarkNewScenarioTipReadReq)(nil), "mystery_place.MarkNewScenarioTipReadReq")
	proto.RegisterType((*MarkNewScenarioTipReadResp)(nil), "mystery_place.MarkNewScenarioTipReadResp")
	proto.RegisterType((*GetScenarioSummaryReq)(nil), "mystery_place.GetScenarioSummaryReq")
	proto.RegisterType((*GetPlayedScenarioRecordListReq)(nil), "mystery_place.GetPlayedScenarioRecordListReq")
	proto.RegisterType((*GetPlayedScenarioRecordListResp)(nil), "mystery_place.GetPlayedScenarioRecordListResp")
	proto.RegisterType((*ScenarioRecord)(nil), "mystery_place.ScenarioRecord")
	proto.RegisterType((*GetPlayedScenarioRecordDetailReq)(nil), "mystery_place.GetPlayedScenarioRecordDetailReq")
	proto.RegisterType((*GetPlayedScenarioRecordDetailResp)(nil), "mystery_place.GetPlayedScenarioRecordDetailResp")
	proto.RegisterType((*ScenarioRecordDetail)(nil), "mystery_place.ScenarioRecordDetail")
	proto.RegisterType((*MysteryPlacePlayer)(nil), "mystery_place.MysteryPlacePlayer")
	proto.RegisterType((*MysteryPlaceLoadMore)(nil), "mystery_place.MysteryPlaceLoadMore")
	proto.RegisterType((*GetScenarioChapterSummaryReq)(nil), "mystery_place.GetScenarioChapterSummaryReq")
	proto.RegisterType((*GetScenarioChapterSummaryResp)(nil), "mystery_place.GetScenarioChapterSummaryResp")
	proto.RegisterType((*SetPlayedScenarioRecordVisibilityReq)(nil), "mystery_place.SetPlayedScenarioRecordVisibilityReq")
	proto.RegisterType((*SetPlayedScenarioRecordVisibilityResp)(nil), "mystery_place.SetPlayedScenarioRecordVisibilityResp")
	proto.RegisterType((*GetScenarioChapterReq)(nil), "mystery_place.GetScenarioChapterReq")
	proto.RegisterType((*AddFeedbackReq)(nil), "mystery_place.AddFeedbackReq")
	proto.RegisterType((*AddFeedbackResp)(nil), "mystery_place.AddFeedbackResp")
	proto.RegisterType((*Res)(nil), "mystery_place.Res")
	proto.RegisterType((*GetScenarioChapterResp)(nil), "mystery_place.GetScenarioChapterResp")
	proto.RegisterType((*ChapterSummary)(nil), "mystery_place.ChapterSummary")
	proto.RegisterType((*AddScenarioChapterReq)(nil), "mystery_place.AddScenarioChapterReq")
	proto.RegisterType((*AddScenarioChapterResp)(nil), "mystery_place.AddScenarioChapterResp")
	proto.RegisterType((*RcmdTab)(nil), "mystery_place.RcmdTab")
	proto.RegisterType((*UpdateTabSetReq)(nil), "mystery_place.UpdateTabSetReq")
	proto.RegisterType((*UpdateTabSetResp)(nil), "mystery_place.UpdateTabSetResp")
	proto.RegisterType((*TabSet)(nil), "mystery_place.TabSet")
	proto.RegisterType((*BatchGetRcmdTabByIdReq)(nil), "mystery_place.BatchGetRcmdTabByIdReq")
	proto.RegisterType((*BatchGetRcmdTabByIdResp)(nil), "mystery_place.BatchGetRcmdTabByIdResp")
	proto.RegisterType((*CommonReq)(nil), "mystery_place.CommonReq")
	proto.RegisterType((*CommonResp)(nil), "mystery_place.CommonResp")
	proto.RegisterType((*SeriesInfo)(nil), "mystery_place.SeriesInfo")
	proto.RegisterType((*DramaInfo)(nil), "mystery_place.DramaInfo")
	proto.RegisterType((*PlotInfo)(nil), "mystery_place.PlotInfo")
	proto.RegisterType((*GetScenarioHotRankReq)(nil), "mystery_place.GetScenarioHotRankReq")
	proto.RegisterType((*ScenarioHotRank)(nil), "mystery_place.ScenarioHotRank")
	proto.RegisterType((*GetScenarioHotRankResp)(nil), "mystery_place.GetScenarioHotRankResp")
	proto.RegisterEnum("mystery_place.PlaymateTagType", PlaymateTagType_name, PlaymateTagType_value)
	proto.RegisterEnum("mystery_place.GameOrientation", GameOrientation_name, GameOrientation_value)
	proto.RegisterEnum("mystery_place.ScenarioTagType", ScenarioTagType_name, ScenarioTagType_value)
	proto.RegisterEnum("mystery_place.PlayMode", PlayMode_name, PlayMode_value)
	proto.RegisterEnum("mystery_place.ScenarioTabStatus", ScenarioTabStatus_name, ScenarioTabStatus_value)
	proto.RegisterEnum("mystery_place.SortType", SortType_name, SortType_value)
	proto.RegisterEnum("mystery_place.MysteryInviteResult", MysteryInviteResult_name, MysteryInviteResult_value)
	proto.RegisterEnum("mystery_place.CommentTypeFilter", CommentTypeFilter_name, CommentTypeFilter_value)
	proto.RegisterEnum("mystery_place.DisplayFilter", DisplayFilter_name, DisplayFilter_value)
	proto.RegisterEnum("mystery_place.NewScenarioTipType", NewScenarioTipType_name, NewScenarioTipType_value)
	proto.RegisterEnum("mystery_place.ResType", ResType_name, ResType_value)
	proto.RegisterEnum("mystery_place.UserSex", UserSex_name, UserSex_value)
	proto.RegisterEnum("mystery_place.ScenarioIncWeightInfo_Weightstatus", ScenarioIncWeightInfo_Weightstatus_name, ScenarioIncWeightInfo_Weightstatus_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MysteryPlaceClient is the client API for MysteryPlace service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MysteryPlaceClient interface {
	// 创建/更新剧本配置
	UpsertScenarioInfo(ctx context.Context, in *UpsertScenarioInfoReq, opts ...grpc.CallOption) (*UpsertScenarioInfoResp, error)
	// 删除剧本配置
	DelScenarioInfo(ctx context.Context, in *DelScenarioInfoReq, opts ...grpc.CallOption) (*DelScenarioInfoResp, error)
	// 获取剧本配置列表
	ListScenarioInfo(ctx context.Context, in *ListScenarioInfoReq, opts ...grpc.CallOption) (*ListScenarioInfoResp, error)
	// 获取剧本总数
	GetScenarioInfoTotalCount(ctx context.Context, in *GetScenarioInfoTotalCountReq, opts ...grpc.CallOption) (*GetScenarioInfoTotalCountResp, error)
	// 获取指定剧本配置
	GetScenarioInfo(ctx context.Context, in *GetScenarioInfoReq, opts ...grpc.CallOption) (*GetScenarioInfoResp, error)
	// 判断新老用户
	IsNewUser(ctx context.Context, in *IsNewUserReq, opts ...grpc.CallOption) (*IsNewUserResp, error)
	// 批量获取房间剧本进度
	BatchGetGamePercentByChannelIds(ctx context.Context, in *BatchGetGamePercentByChannelIdsReq, opts ...grpc.CallOption) (*BatchGetGamePercentByChannelIdsResp, error)
	// 获取用户最近剧本记录
	GetPlayerLastRecord(ctx context.Context, in *GetPlayerLastRecordReq, opts ...grpc.CallOption) (*GetPlayerLastRecordResp, error)
	// 批量获取用户游戏进度
	BatchGetPlayerGamePercentByGameIds(ctx context.Context, in *BatchGetPlayerGamePercentByGameIdsReq, opts ...grpc.CallOption) (*BatchGetPlayerGamePercentByGameIdsResp, error)
	// 批量获取用户助力进度（过滤不可助力用户uid。剩下为可邀请助力的uid）
	BatchCheckHelperList(ctx context.Context, in *BatchCheckHelperListReq, opts ...grpc.CallOption) (*BatchCheckHelperListResp, error)
	// 剧本排序
	RearrangeScenario(ctx context.Context, in *RearrangeScenarioReq, opts ...grpc.CallOption) (*RearrangeScenarioResp, error)
	// 设置剧本分享链接
	SetScenarioShareLink(ctx context.Context, in *SetScenarioShareLinkReq, opts ...grpc.CallOption) (*SetScenarioShareLinkResp, error)
	// 获取剧本分享链接配置
	BatchGetScenarioShareLink(ctx context.Context, in *BatchGetScenarioShareLinkReq, opts ...grpc.CallOption) (*BatchGetScenarioShareLinkResp, error)
	// 删除剧本分享链接配置
	DelScenarioShareLink(ctx context.Context, in *DelScenarioShareLinkReq, opts ...grpc.CallOption) (*DelScenarioShareLinkResp, error)
	// 获取新注册的用户
	GetNewUsers(ctx context.Context, in *GetNewUsersReq, opts ...grpc.CallOption) (*GetNewUsersResp, error)
	// 用户评论剧本
	AddUserCommentScenario(ctx context.Context, in *AddUserCommentScenarioReq, opts ...grpc.CallOption) (*AddUserCommentScenarioResp, error)
	// 批量获取用户评价
	BatchGetUserCommentScenario(ctx context.Context, in *BatchGetUserCommentScenarioReq, opts ...grpc.CallOption) (*BatchGetUserCommentScenarioResp, error)
	// 剧本列表
	ListScenarioComment(ctx context.Context, in *ListScenarioCommentReq, opts ...grpc.CallOption) (*ListScenarioCommentResp, error)
	// 更新剧本 目前只支持修改显示属性
	UpdateScenarioComment(ctx context.Context, in *UpdateScenarioCommentReq, opts ...grpc.CallOption) (*UpdateScenarioCommentResp, error)
	// 查看剧本评论统计
	ListScenarioCommentSummary(ctx context.Context, in *ListScenarioCommentSummaryReq, opts ...grpc.CallOption) (*ListScenarioCommentSummaryResp, error)
	// 设置剧本运营分数
	SetScenarioManualAverage(ctx context.Context, in *SetScenarioManualAverageReq, opts ...grpc.CallOption) (*SetScenarioManualAverageResp, error)
	// 手动刷新统计结果，测试用
	RefreshCommentSummary(ctx context.Context, in *RefreshCommentSummaryReq, opts ...grpc.CallOption) (*RefreshCommentSummaryResp, error)
	// 新增玩伴标签
	AddPlaymateTag(ctx context.Context, in *AddPlaymateTagReq, opts ...grpc.CallOption) (*AddPlaymateTagResp, error)
	// 删除玩伴标签
	DeletePlaymateTag(ctx context.Context, in *DeletePlaymateTagReq, opts ...grpc.CallOption) (*DeletePlaymateTagResp, error)
	// 修改玩伴标签
	UpdatePlaymateTag(ctx context.Context, in *UpdatePlaymateTagReq, opts ...grpc.CallOption) (*UpdatePlaymateTagResp, error)
	// 重排序玩伴标签
	ReorderPlaymateTags(ctx context.Context, in *ReorderPlaymateTagsReq, opts ...grpc.CallOption) (*ReorderPlaymateTagsResp, error)
	// 获取玩伴标签列表
	GetPlaymateTags(ctx context.Context, in *GetPlaymateTagsReq, opts ...grpc.CallOption) (*GetPlaymateTagsResp, error)
	// 通过id查询玩伴标签
	GetPlaymateTagByIds(ctx context.Context, in *GetPlaymateTagByIdsReq, opts ...grpc.CallOption) (*GetPlaymateTagByIdsResp, error)
	// 新增用户标签记录
	AddUserPlaymateTag(ctx context.Context, in *AddUserPlaymateTagReq, opts ...grpc.CallOption) (*AddUserPlaymateTagResp, error)
	// 批量获取用户被评价最热门的标签
	BatGetUserHotPlaymateTag(ctx context.Context, in *BatGetUserHotPlaymateTagReq, opts ...grpc.CallOption) (*BatGetUserHotPlaymateTagResp, error)
	//  获取上新提示
	GetNewScenarioTips(ctx context.Context, in *GetNewScenarioTipsReq, opts ...grpc.CallOption) (*GetNewScenarioTipsResp, error)
	// 标记提示已读
	MarkNewScenarioTipRead(ctx context.Context, in *MarkNewScenarioTipReadReq, opts ...grpc.CallOption) (*MarkNewScenarioTipReadResp, error)
	// 新增剧本资源
	AddScenarioChapter(ctx context.Context, in *AddScenarioChapterReq, opts ...grpc.CallOption) (*AddScenarioChapterResp, error)
	// 获取剧本记录
	GetPlayedScenarioRecordList(ctx context.Context, in *GetPlayedScenarioRecordListReq, opts ...grpc.CallOption) (*GetPlayedScenarioRecordListResp, error)
	// 获取记录详情
	GetPlayedScenarioRecordDetail(ctx context.Context, in *GetPlayedScenarioRecordDetailReq, opts ...grpc.CallOption) (*GetPlayedScenarioRecordDetailResp, error)
	// 获取剧本章节概要
	GetScenarioChapterSummary(ctx context.Context, in *GetScenarioChapterSummaryReq, opts ...grpc.CallOption) (*GetScenarioChapterSummaryResp, error)
	// 设置记录可见
	SetPlayedScenarioRecordVisibility(ctx context.Context, in *SetPlayedScenarioRecordVisibilityReq, opts ...grpc.CallOption) (*SetPlayedScenarioRecordVisibilityResp, error)
	// 获取剧本前情提要和下章预告
	GetScenarioChapter(ctx context.Context, in *GetScenarioChapterReq, opts ...grpc.CallOption) (*GetScenarioChapterResp, error)
	// 新增反馈
	AddFeedback(ctx context.Context, in *AddFeedbackReq, opts ...grpc.CallOption) (*AddFeedbackResp, error)
	// 更新推荐玩法（增/删/排序）
	UpdateTabSet(ctx context.Context, in *UpdateTabSetReq, opts ...grpc.CallOption) (*UpdateTabSetResp, error)
	// 批量获取推荐tab
	BatchGetRcmdTabById(ctx context.Context, in *BatchGetRcmdTabByIdReq, opts ...grpc.CallOption) (*BatchGetRcmdTabByIdResp, error)
	// 获取剧本热度值排名
	GetScenarioHotRank(ctx context.Context, in *GetScenarioHotRankReq, opts ...grpc.CallOption) (*GetScenarioHotRankResp, error)
}

type mysteryPlaceClient struct {
	cc *grpc.ClientConn
}

func NewMysteryPlaceClient(cc *grpc.ClientConn) MysteryPlaceClient {
	return &mysteryPlaceClient{cc}
}

func (c *mysteryPlaceClient) UpsertScenarioInfo(ctx context.Context, in *UpsertScenarioInfoReq, opts ...grpc.CallOption) (*UpsertScenarioInfoResp, error) {
	out := new(UpsertScenarioInfoResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/UpsertScenarioInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) DelScenarioInfo(ctx context.Context, in *DelScenarioInfoReq, opts ...grpc.CallOption) (*DelScenarioInfoResp, error) {
	out := new(DelScenarioInfoResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/DelScenarioInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) ListScenarioInfo(ctx context.Context, in *ListScenarioInfoReq, opts ...grpc.CallOption) (*ListScenarioInfoResp, error) {
	out := new(ListScenarioInfoResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/ListScenarioInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) GetScenarioInfoTotalCount(ctx context.Context, in *GetScenarioInfoTotalCountReq, opts ...grpc.CallOption) (*GetScenarioInfoTotalCountResp, error) {
	out := new(GetScenarioInfoTotalCountResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/GetScenarioInfoTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) GetScenarioInfo(ctx context.Context, in *GetScenarioInfoReq, opts ...grpc.CallOption) (*GetScenarioInfoResp, error) {
	out := new(GetScenarioInfoResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/GetScenarioInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) IsNewUser(ctx context.Context, in *IsNewUserReq, opts ...grpc.CallOption) (*IsNewUserResp, error) {
	out := new(IsNewUserResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/IsNewUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) BatchGetGamePercentByChannelIds(ctx context.Context, in *BatchGetGamePercentByChannelIdsReq, opts ...grpc.CallOption) (*BatchGetGamePercentByChannelIdsResp, error) {
	out := new(BatchGetGamePercentByChannelIdsResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/BatchGetGamePercentByChannelIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) GetPlayerLastRecord(ctx context.Context, in *GetPlayerLastRecordReq, opts ...grpc.CallOption) (*GetPlayerLastRecordResp, error) {
	out := new(GetPlayerLastRecordResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/GetPlayerLastRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) BatchGetPlayerGamePercentByGameIds(ctx context.Context, in *BatchGetPlayerGamePercentByGameIdsReq, opts ...grpc.CallOption) (*BatchGetPlayerGamePercentByGameIdsResp, error) {
	out := new(BatchGetPlayerGamePercentByGameIdsResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/BatchGetPlayerGamePercentByGameIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) BatchCheckHelperList(ctx context.Context, in *BatchCheckHelperListReq, opts ...grpc.CallOption) (*BatchCheckHelperListResp, error) {
	out := new(BatchCheckHelperListResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/BatchCheckHelperList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) RearrangeScenario(ctx context.Context, in *RearrangeScenarioReq, opts ...grpc.CallOption) (*RearrangeScenarioResp, error) {
	out := new(RearrangeScenarioResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/RearrangeScenario", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) SetScenarioShareLink(ctx context.Context, in *SetScenarioShareLinkReq, opts ...grpc.CallOption) (*SetScenarioShareLinkResp, error) {
	out := new(SetScenarioShareLinkResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/SetScenarioShareLink", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) BatchGetScenarioShareLink(ctx context.Context, in *BatchGetScenarioShareLinkReq, opts ...grpc.CallOption) (*BatchGetScenarioShareLinkResp, error) {
	out := new(BatchGetScenarioShareLinkResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/BatchGetScenarioShareLink", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) DelScenarioShareLink(ctx context.Context, in *DelScenarioShareLinkReq, opts ...grpc.CallOption) (*DelScenarioShareLinkResp, error) {
	out := new(DelScenarioShareLinkResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/DelScenarioShareLink", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) GetNewUsers(ctx context.Context, in *GetNewUsersReq, opts ...grpc.CallOption) (*GetNewUsersResp, error) {
	out := new(GetNewUsersResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/GetNewUsers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) AddUserCommentScenario(ctx context.Context, in *AddUserCommentScenarioReq, opts ...grpc.CallOption) (*AddUserCommentScenarioResp, error) {
	out := new(AddUserCommentScenarioResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/AddUserCommentScenario", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) BatchGetUserCommentScenario(ctx context.Context, in *BatchGetUserCommentScenarioReq, opts ...grpc.CallOption) (*BatchGetUserCommentScenarioResp, error) {
	out := new(BatchGetUserCommentScenarioResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/BatchGetUserCommentScenario", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) ListScenarioComment(ctx context.Context, in *ListScenarioCommentReq, opts ...grpc.CallOption) (*ListScenarioCommentResp, error) {
	out := new(ListScenarioCommentResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/ListScenarioComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) UpdateScenarioComment(ctx context.Context, in *UpdateScenarioCommentReq, opts ...grpc.CallOption) (*UpdateScenarioCommentResp, error) {
	out := new(UpdateScenarioCommentResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/UpdateScenarioComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) ListScenarioCommentSummary(ctx context.Context, in *ListScenarioCommentSummaryReq, opts ...grpc.CallOption) (*ListScenarioCommentSummaryResp, error) {
	out := new(ListScenarioCommentSummaryResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/ListScenarioCommentSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) SetScenarioManualAverage(ctx context.Context, in *SetScenarioManualAverageReq, opts ...grpc.CallOption) (*SetScenarioManualAverageResp, error) {
	out := new(SetScenarioManualAverageResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/SetScenarioManualAverage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) RefreshCommentSummary(ctx context.Context, in *RefreshCommentSummaryReq, opts ...grpc.CallOption) (*RefreshCommentSummaryResp, error) {
	out := new(RefreshCommentSummaryResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/RefreshCommentSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) AddPlaymateTag(ctx context.Context, in *AddPlaymateTagReq, opts ...grpc.CallOption) (*AddPlaymateTagResp, error) {
	out := new(AddPlaymateTagResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/AddPlaymateTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) DeletePlaymateTag(ctx context.Context, in *DeletePlaymateTagReq, opts ...grpc.CallOption) (*DeletePlaymateTagResp, error) {
	out := new(DeletePlaymateTagResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/DeletePlaymateTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) UpdatePlaymateTag(ctx context.Context, in *UpdatePlaymateTagReq, opts ...grpc.CallOption) (*UpdatePlaymateTagResp, error) {
	out := new(UpdatePlaymateTagResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/UpdatePlaymateTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) ReorderPlaymateTags(ctx context.Context, in *ReorderPlaymateTagsReq, opts ...grpc.CallOption) (*ReorderPlaymateTagsResp, error) {
	out := new(ReorderPlaymateTagsResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/ReorderPlaymateTags", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) GetPlaymateTags(ctx context.Context, in *GetPlaymateTagsReq, opts ...grpc.CallOption) (*GetPlaymateTagsResp, error) {
	out := new(GetPlaymateTagsResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/GetPlaymateTags", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) GetPlaymateTagByIds(ctx context.Context, in *GetPlaymateTagByIdsReq, opts ...grpc.CallOption) (*GetPlaymateTagByIdsResp, error) {
	out := new(GetPlaymateTagByIdsResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/GetPlaymateTagByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) AddUserPlaymateTag(ctx context.Context, in *AddUserPlaymateTagReq, opts ...grpc.CallOption) (*AddUserPlaymateTagResp, error) {
	out := new(AddUserPlaymateTagResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/AddUserPlaymateTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) BatGetUserHotPlaymateTag(ctx context.Context, in *BatGetUserHotPlaymateTagReq, opts ...grpc.CallOption) (*BatGetUserHotPlaymateTagResp, error) {
	out := new(BatGetUserHotPlaymateTagResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/BatGetUserHotPlaymateTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) GetNewScenarioTips(ctx context.Context, in *GetNewScenarioTipsReq, opts ...grpc.CallOption) (*GetNewScenarioTipsResp, error) {
	out := new(GetNewScenarioTipsResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/GetNewScenarioTips", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) MarkNewScenarioTipRead(ctx context.Context, in *MarkNewScenarioTipReadReq, opts ...grpc.CallOption) (*MarkNewScenarioTipReadResp, error) {
	out := new(MarkNewScenarioTipReadResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/MarkNewScenarioTipRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) AddScenarioChapter(ctx context.Context, in *AddScenarioChapterReq, opts ...grpc.CallOption) (*AddScenarioChapterResp, error) {
	out := new(AddScenarioChapterResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/AddScenarioChapter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) GetPlayedScenarioRecordList(ctx context.Context, in *GetPlayedScenarioRecordListReq, opts ...grpc.CallOption) (*GetPlayedScenarioRecordListResp, error) {
	out := new(GetPlayedScenarioRecordListResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/GetPlayedScenarioRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) GetPlayedScenarioRecordDetail(ctx context.Context, in *GetPlayedScenarioRecordDetailReq, opts ...grpc.CallOption) (*GetPlayedScenarioRecordDetailResp, error) {
	out := new(GetPlayedScenarioRecordDetailResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/GetPlayedScenarioRecordDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) GetScenarioChapterSummary(ctx context.Context, in *GetScenarioChapterSummaryReq, opts ...grpc.CallOption) (*GetScenarioChapterSummaryResp, error) {
	out := new(GetScenarioChapterSummaryResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/GetScenarioChapterSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) SetPlayedScenarioRecordVisibility(ctx context.Context, in *SetPlayedScenarioRecordVisibilityReq, opts ...grpc.CallOption) (*SetPlayedScenarioRecordVisibilityResp, error) {
	out := new(SetPlayedScenarioRecordVisibilityResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/SetPlayedScenarioRecordVisibility", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) GetScenarioChapter(ctx context.Context, in *GetScenarioChapterReq, opts ...grpc.CallOption) (*GetScenarioChapterResp, error) {
	out := new(GetScenarioChapterResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/GetScenarioChapter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) AddFeedback(ctx context.Context, in *AddFeedbackReq, opts ...grpc.CallOption) (*AddFeedbackResp, error) {
	out := new(AddFeedbackResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/AddFeedback", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) UpdateTabSet(ctx context.Context, in *UpdateTabSetReq, opts ...grpc.CallOption) (*UpdateTabSetResp, error) {
	out := new(UpdateTabSetResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/UpdateTabSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) BatchGetRcmdTabById(ctx context.Context, in *BatchGetRcmdTabByIdReq, opts ...grpc.CallOption) (*BatchGetRcmdTabByIdResp, error) {
	out := new(BatchGetRcmdTabByIdResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/BatchGetRcmdTabById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceClient) GetScenarioHotRank(ctx context.Context, in *GetScenarioHotRankReq, opts ...grpc.CallOption) (*GetScenarioHotRankResp, error) {
	out := new(GetScenarioHotRankResp)
	err := c.cc.Invoke(ctx, "/mystery_place.MysteryPlace/GetScenarioHotRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MysteryPlaceServer is the server API for MysteryPlace service.
type MysteryPlaceServer interface {
	// 创建/更新剧本配置
	UpsertScenarioInfo(context.Context, *UpsertScenarioInfoReq) (*UpsertScenarioInfoResp, error)
	// 删除剧本配置
	DelScenarioInfo(context.Context, *DelScenarioInfoReq) (*DelScenarioInfoResp, error)
	// 获取剧本配置列表
	ListScenarioInfo(context.Context, *ListScenarioInfoReq) (*ListScenarioInfoResp, error)
	// 获取剧本总数
	GetScenarioInfoTotalCount(context.Context, *GetScenarioInfoTotalCountReq) (*GetScenarioInfoTotalCountResp, error)
	// 获取指定剧本配置
	GetScenarioInfo(context.Context, *GetScenarioInfoReq) (*GetScenarioInfoResp, error)
	// 判断新老用户
	IsNewUser(context.Context, *IsNewUserReq) (*IsNewUserResp, error)
	// 批量获取房间剧本进度
	BatchGetGamePercentByChannelIds(context.Context, *BatchGetGamePercentByChannelIdsReq) (*BatchGetGamePercentByChannelIdsResp, error)
	// 获取用户最近剧本记录
	GetPlayerLastRecord(context.Context, *GetPlayerLastRecordReq) (*GetPlayerLastRecordResp, error)
	// 批量获取用户游戏进度
	BatchGetPlayerGamePercentByGameIds(context.Context, *BatchGetPlayerGamePercentByGameIdsReq) (*BatchGetPlayerGamePercentByGameIdsResp, error)
	// 批量获取用户助力进度（过滤不可助力用户uid。剩下为可邀请助力的uid）
	BatchCheckHelperList(context.Context, *BatchCheckHelperListReq) (*BatchCheckHelperListResp, error)
	// 剧本排序
	RearrangeScenario(context.Context, *RearrangeScenarioReq) (*RearrangeScenarioResp, error)
	// 设置剧本分享链接
	SetScenarioShareLink(context.Context, *SetScenarioShareLinkReq) (*SetScenarioShareLinkResp, error)
	// 获取剧本分享链接配置
	BatchGetScenarioShareLink(context.Context, *BatchGetScenarioShareLinkReq) (*BatchGetScenarioShareLinkResp, error)
	// 删除剧本分享链接配置
	DelScenarioShareLink(context.Context, *DelScenarioShareLinkReq) (*DelScenarioShareLinkResp, error)
	// 获取新注册的用户
	GetNewUsers(context.Context, *GetNewUsersReq) (*GetNewUsersResp, error)
	// 用户评论剧本
	AddUserCommentScenario(context.Context, *AddUserCommentScenarioReq) (*AddUserCommentScenarioResp, error)
	// 批量获取用户评价
	BatchGetUserCommentScenario(context.Context, *BatchGetUserCommentScenarioReq) (*BatchGetUserCommentScenarioResp, error)
	// 剧本列表
	ListScenarioComment(context.Context, *ListScenarioCommentReq) (*ListScenarioCommentResp, error)
	// 更新剧本 目前只支持修改显示属性
	UpdateScenarioComment(context.Context, *UpdateScenarioCommentReq) (*UpdateScenarioCommentResp, error)
	// 查看剧本评论统计
	ListScenarioCommentSummary(context.Context, *ListScenarioCommentSummaryReq) (*ListScenarioCommentSummaryResp, error)
	// 设置剧本运营分数
	SetScenarioManualAverage(context.Context, *SetScenarioManualAverageReq) (*SetScenarioManualAverageResp, error)
	// 手动刷新统计结果，测试用
	RefreshCommentSummary(context.Context, *RefreshCommentSummaryReq) (*RefreshCommentSummaryResp, error)
	// 新增玩伴标签
	AddPlaymateTag(context.Context, *AddPlaymateTagReq) (*AddPlaymateTagResp, error)
	// 删除玩伴标签
	DeletePlaymateTag(context.Context, *DeletePlaymateTagReq) (*DeletePlaymateTagResp, error)
	// 修改玩伴标签
	UpdatePlaymateTag(context.Context, *UpdatePlaymateTagReq) (*UpdatePlaymateTagResp, error)
	// 重排序玩伴标签
	ReorderPlaymateTags(context.Context, *ReorderPlaymateTagsReq) (*ReorderPlaymateTagsResp, error)
	// 获取玩伴标签列表
	GetPlaymateTags(context.Context, *GetPlaymateTagsReq) (*GetPlaymateTagsResp, error)
	// 通过id查询玩伴标签
	GetPlaymateTagByIds(context.Context, *GetPlaymateTagByIdsReq) (*GetPlaymateTagByIdsResp, error)
	// 新增用户标签记录
	AddUserPlaymateTag(context.Context, *AddUserPlaymateTagReq) (*AddUserPlaymateTagResp, error)
	// 批量获取用户被评价最热门的标签
	BatGetUserHotPlaymateTag(context.Context, *BatGetUserHotPlaymateTagReq) (*BatGetUserHotPlaymateTagResp, error)
	//  获取上新提示
	GetNewScenarioTips(context.Context, *GetNewScenarioTipsReq) (*GetNewScenarioTipsResp, error)
	// 标记提示已读
	MarkNewScenarioTipRead(context.Context, *MarkNewScenarioTipReadReq) (*MarkNewScenarioTipReadResp, error)
	// 新增剧本资源
	AddScenarioChapter(context.Context, *AddScenarioChapterReq) (*AddScenarioChapterResp, error)
	// 获取剧本记录
	GetPlayedScenarioRecordList(context.Context, *GetPlayedScenarioRecordListReq) (*GetPlayedScenarioRecordListResp, error)
	// 获取记录详情
	GetPlayedScenarioRecordDetail(context.Context, *GetPlayedScenarioRecordDetailReq) (*GetPlayedScenarioRecordDetailResp, error)
	// 获取剧本章节概要
	GetScenarioChapterSummary(context.Context, *GetScenarioChapterSummaryReq) (*GetScenarioChapterSummaryResp, error)
	// 设置记录可见
	SetPlayedScenarioRecordVisibility(context.Context, *SetPlayedScenarioRecordVisibilityReq) (*SetPlayedScenarioRecordVisibilityResp, error)
	// 获取剧本前情提要和下章预告
	GetScenarioChapter(context.Context, *GetScenarioChapterReq) (*GetScenarioChapterResp, error)
	// 新增反馈
	AddFeedback(context.Context, *AddFeedbackReq) (*AddFeedbackResp, error)
	// 更新推荐玩法（增/删/排序）
	UpdateTabSet(context.Context, *UpdateTabSetReq) (*UpdateTabSetResp, error)
	// 批量获取推荐tab
	BatchGetRcmdTabById(context.Context, *BatchGetRcmdTabByIdReq) (*BatchGetRcmdTabByIdResp, error)
	// 获取剧本热度值排名
	GetScenarioHotRank(context.Context, *GetScenarioHotRankReq) (*GetScenarioHotRankResp, error)
}

func RegisterMysteryPlaceServer(s *grpc.Server, srv MysteryPlaceServer) {
	s.RegisterService(&_MysteryPlace_serviceDesc, srv)
}

func _MysteryPlace_UpsertScenarioInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertScenarioInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).UpsertScenarioInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/UpsertScenarioInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).UpsertScenarioInfo(ctx, req.(*UpsertScenarioInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_DelScenarioInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelScenarioInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).DelScenarioInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/DelScenarioInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).DelScenarioInfo(ctx, req.(*DelScenarioInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_ListScenarioInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListScenarioInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).ListScenarioInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/ListScenarioInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).ListScenarioInfo(ctx, req.(*ListScenarioInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_GetScenarioInfoTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScenarioInfoTotalCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).GetScenarioInfoTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/GetScenarioInfoTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).GetScenarioInfoTotalCount(ctx, req.(*GetScenarioInfoTotalCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_GetScenarioInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScenarioInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).GetScenarioInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/GetScenarioInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).GetScenarioInfo(ctx, req.(*GetScenarioInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_IsNewUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsNewUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).IsNewUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/IsNewUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).IsNewUser(ctx, req.(*IsNewUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_BatchGetGamePercentByChannelIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGamePercentByChannelIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).BatchGetGamePercentByChannelIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/BatchGetGamePercentByChannelIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).BatchGetGamePercentByChannelIds(ctx, req.(*BatchGetGamePercentByChannelIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_GetPlayerLastRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlayerLastRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).GetPlayerLastRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/GetPlayerLastRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).GetPlayerLastRecord(ctx, req.(*GetPlayerLastRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_BatchGetPlayerGamePercentByGameIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetPlayerGamePercentByGameIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).BatchGetPlayerGamePercentByGameIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/BatchGetPlayerGamePercentByGameIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).BatchGetPlayerGamePercentByGameIds(ctx, req.(*BatchGetPlayerGamePercentByGameIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_BatchCheckHelperList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckHelperListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).BatchCheckHelperList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/BatchCheckHelperList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).BatchCheckHelperList(ctx, req.(*BatchCheckHelperListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_RearrangeScenario_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RearrangeScenarioReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).RearrangeScenario(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/RearrangeScenario",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).RearrangeScenario(ctx, req.(*RearrangeScenarioReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_SetScenarioShareLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetScenarioShareLinkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).SetScenarioShareLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/SetScenarioShareLink",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).SetScenarioShareLink(ctx, req.(*SetScenarioShareLinkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_BatchGetScenarioShareLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetScenarioShareLinkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).BatchGetScenarioShareLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/BatchGetScenarioShareLink",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).BatchGetScenarioShareLink(ctx, req.(*BatchGetScenarioShareLinkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_DelScenarioShareLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelScenarioShareLinkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).DelScenarioShareLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/DelScenarioShareLink",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).DelScenarioShareLink(ctx, req.(*DelScenarioShareLinkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_GetNewUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewUsersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).GetNewUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/GetNewUsers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).GetNewUsers(ctx, req.(*GetNewUsersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_AddUserCommentScenario_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserCommentScenarioReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).AddUserCommentScenario(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/AddUserCommentScenario",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).AddUserCommentScenario(ctx, req.(*AddUserCommentScenarioReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_BatchGetUserCommentScenario_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserCommentScenarioReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).BatchGetUserCommentScenario(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/BatchGetUserCommentScenario",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).BatchGetUserCommentScenario(ctx, req.(*BatchGetUserCommentScenarioReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_ListScenarioComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListScenarioCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).ListScenarioComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/ListScenarioComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).ListScenarioComment(ctx, req.(*ListScenarioCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_UpdateScenarioComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateScenarioCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).UpdateScenarioComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/UpdateScenarioComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).UpdateScenarioComment(ctx, req.(*UpdateScenarioCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_ListScenarioCommentSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListScenarioCommentSummaryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).ListScenarioCommentSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/ListScenarioCommentSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).ListScenarioCommentSummary(ctx, req.(*ListScenarioCommentSummaryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_SetScenarioManualAverage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetScenarioManualAverageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).SetScenarioManualAverage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/SetScenarioManualAverage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).SetScenarioManualAverage(ctx, req.(*SetScenarioManualAverageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_RefreshCommentSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshCommentSummaryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).RefreshCommentSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/RefreshCommentSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).RefreshCommentSummary(ctx, req.(*RefreshCommentSummaryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_AddPlaymateTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPlaymateTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).AddPlaymateTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/AddPlaymateTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).AddPlaymateTag(ctx, req.(*AddPlaymateTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_DeletePlaymateTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePlaymateTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).DeletePlaymateTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/DeletePlaymateTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).DeletePlaymateTag(ctx, req.(*DeletePlaymateTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_UpdatePlaymateTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePlaymateTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).UpdatePlaymateTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/UpdatePlaymateTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).UpdatePlaymateTag(ctx, req.(*UpdatePlaymateTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_ReorderPlaymateTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReorderPlaymateTagsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).ReorderPlaymateTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/ReorderPlaymateTags",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).ReorderPlaymateTags(ctx, req.(*ReorderPlaymateTagsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_GetPlaymateTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlaymateTagsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).GetPlaymateTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/GetPlaymateTags",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).GetPlaymateTags(ctx, req.(*GetPlaymateTagsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_GetPlaymateTagByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlaymateTagByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).GetPlaymateTagByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/GetPlaymateTagByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).GetPlaymateTagByIds(ctx, req.(*GetPlaymateTagByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_AddUserPlaymateTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserPlaymateTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).AddUserPlaymateTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/AddUserPlaymateTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).AddUserPlaymateTag(ctx, req.(*AddUserPlaymateTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_BatGetUserHotPlaymateTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetUserHotPlaymateTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).BatGetUserHotPlaymateTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/BatGetUserHotPlaymateTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).BatGetUserHotPlaymateTag(ctx, req.(*BatGetUserHotPlaymateTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_GetNewScenarioTips_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewScenarioTipsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).GetNewScenarioTips(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/GetNewScenarioTips",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).GetNewScenarioTips(ctx, req.(*GetNewScenarioTipsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_MarkNewScenarioTipRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkNewScenarioTipReadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).MarkNewScenarioTipRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/MarkNewScenarioTipRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).MarkNewScenarioTipRead(ctx, req.(*MarkNewScenarioTipReadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_AddScenarioChapter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddScenarioChapterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).AddScenarioChapter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/AddScenarioChapter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).AddScenarioChapter(ctx, req.(*AddScenarioChapterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_GetPlayedScenarioRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlayedScenarioRecordListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).GetPlayedScenarioRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/GetPlayedScenarioRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).GetPlayedScenarioRecordList(ctx, req.(*GetPlayedScenarioRecordListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_GetPlayedScenarioRecordDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlayedScenarioRecordDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).GetPlayedScenarioRecordDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/GetPlayedScenarioRecordDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).GetPlayedScenarioRecordDetail(ctx, req.(*GetPlayedScenarioRecordDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_GetScenarioChapterSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScenarioChapterSummaryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).GetScenarioChapterSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/GetScenarioChapterSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).GetScenarioChapterSummary(ctx, req.(*GetScenarioChapterSummaryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_SetPlayedScenarioRecordVisibility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPlayedScenarioRecordVisibilityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).SetPlayedScenarioRecordVisibility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/SetPlayedScenarioRecordVisibility",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).SetPlayedScenarioRecordVisibility(ctx, req.(*SetPlayedScenarioRecordVisibilityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_GetScenarioChapter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScenarioChapterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).GetScenarioChapter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/GetScenarioChapter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).GetScenarioChapter(ctx, req.(*GetScenarioChapterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_AddFeedback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFeedbackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).AddFeedback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/AddFeedback",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).AddFeedback(ctx, req.(*AddFeedbackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_UpdateTabSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTabSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).UpdateTabSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/UpdateTabSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).UpdateTabSet(ctx, req.(*UpdateTabSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_BatchGetRcmdTabById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetRcmdTabByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).BatchGetRcmdTabById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/BatchGetRcmdTabById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).BatchGetRcmdTabById(ctx, req.(*BatchGetRcmdTabByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlace_GetScenarioHotRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScenarioHotRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceServer).GetScenarioHotRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place.MysteryPlace/GetScenarioHotRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceServer).GetScenarioHotRank(ctx, req.(*GetScenarioHotRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MysteryPlace_serviceDesc = grpc.ServiceDesc{
	ServiceName: "mystery_place.MysteryPlace",
	HandlerType: (*MysteryPlaceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpsertScenarioInfo",
			Handler:    _MysteryPlace_UpsertScenarioInfo_Handler,
		},
		{
			MethodName: "DelScenarioInfo",
			Handler:    _MysteryPlace_DelScenarioInfo_Handler,
		},
		{
			MethodName: "ListScenarioInfo",
			Handler:    _MysteryPlace_ListScenarioInfo_Handler,
		},
		{
			MethodName: "GetScenarioInfoTotalCount",
			Handler:    _MysteryPlace_GetScenarioInfoTotalCount_Handler,
		},
		{
			MethodName: "GetScenarioInfo",
			Handler:    _MysteryPlace_GetScenarioInfo_Handler,
		},
		{
			MethodName: "IsNewUser",
			Handler:    _MysteryPlace_IsNewUser_Handler,
		},
		{
			MethodName: "BatchGetGamePercentByChannelIds",
			Handler:    _MysteryPlace_BatchGetGamePercentByChannelIds_Handler,
		},
		{
			MethodName: "GetPlayerLastRecord",
			Handler:    _MysteryPlace_GetPlayerLastRecord_Handler,
		},
		{
			MethodName: "BatchGetPlayerGamePercentByGameIds",
			Handler:    _MysteryPlace_BatchGetPlayerGamePercentByGameIds_Handler,
		},
		{
			MethodName: "BatchCheckHelperList",
			Handler:    _MysteryPlace_BatchCheckHelperList_Handler,
		},
		{
			MethodName: "RearrangeScenario",
			Handler:    _MysteryPlace_RearrangeScenario_Handler,
		},
		{
			MethodName: "SetScenarioShareLink",
			Handler:    _MysteryPlace_SetScenarioShareLink_Handler,
		},
		{
			MethodName: "BatchGetScenarioShareLink",
			Handler:    _MysteryPlace_BatchGetScenarioShareLink_Handler,
		},
		{
			MethodName: "DelScenarioShareLink",
			Handler:    _MysteryPlace_DelScenarioShareLink_Handler,
		},
		{
			MethodName: "GetNewUsers",
			Handler:    _MysteryPlace_GetNewUsers_Handler,
		},
		{
			MethodName: "AddUserCommentScenario",
			Handler:    _MysteryPlace_AddUserCommentScenario_Handler,
		},
		{
			MethodName: "BatchGetUserCommentScenario",
			Handler:    _MysteryPlace_BatchGetUserCommentScenario_Handler,
		},
		{
			MethodName: "ListScenarioComment",
			Handler:    _MysteryPlace_ListScenarioComment_Handler,
		},
		{
			MethodName: "UpdateScenarioComment",
			Handler:    _MysteryPlace_UpdateScenarioComment_Handler,
		},
		{
			MethodName: "ListScenarioCommentSummary",
			Handler:    _MysteryPlace_ListScenarioCommentSummary_Handler,
		},
		{
			MethodName: "SetScenarioManualAverage",
			Handler:    _MysteryPlace_SetScenarioManualAverage_Handler,
		},
		{
			MethodName: "RefreshCommentSummary",
			Handler:    _MysteryPlace_RefreshCommentSummary_Handler,
		},
		{
			MethodName: "AddPlaymateTag",
			Handler:    _MysteryPlace_AddPlaymateTag_Handler,
		},
		{
			MethodName: "DeletePlaymateTag",
			Handler:    _MysteryPlace_DeletePlaymateTag_Handler,
		},
		{
			MethodName: "UpdatePlaymateTag",
			Handler:    _MysteryPlace_UpdatePlaymateTag_Handler,
		},
		{
			MethodName: "ReorderPlaymateTags",
			Handler:    _MysteryPlace_ReorderPlaymateTags_Handler,
		},
		{
			MethodName: "GetPlaymateTags",
			Handler:    _MysteryPlace_GetPlaymateTags_Handler,
		},
		{
			MethodName: "GetPlaymateTagByIds",
			Handler:    _MysteryPlace_GetPlaymateTagByIds_Handler,
		},
		{
			MethodName: "AddUserPlaymateTag",
			Handler:    _MysteryPlace_AddUserPlaymateTag_Handler,
		},
		{
			MethodName: "BatGetUserHotPlaymateTag",
			Handler:    _MysteryPlace_BatGetUserHotPlaymateTag_Handler,
		},
		{
			MethodName: "GetNewScenarioTips",
			Handler:    _MysteryPlace_GetNewScenarioTips_Handler,
		},
		{
			MethodName: "MarkNewScenarioTipRead",
			Handler:    _MysteryPlace_MarkNewScenarioTipRead_Handler,
		},
		{
			MethodName: "AddScenarioChapter",
			Handler:    _MysteryPlace_AddScenarioChapter_Handler,
		},
		{
			MethodName: "GetPlayedScenarioRecordList",
			Handler:    _MysteryPlace_GetPlayedScenarioRecordList_Handler,
		},
		{
			MethodName: "GetPlayedScenarioRecordDetail",
			Handler:    _MysteryPlace_GetPlayedScenarioRecordDetail_Handler,
		},
		{
			MethodName: "GetScenarioChapterSummary",
			Handler:    _MysteryPlace_GetScenarioChapterSummary_Handler,
		},
		{
			MethodName: "SetPlayedScenarioRecordVisibility",
			Handler:    _MysteryPlace_SetPlayedScenarioRecordVisibility_Handler,
		},
		{
			MethodName: "GetScenarioChapter",
			Handler:    _MysteryPlace_GetScenarioChapter_Handler,
		},
		{
			MethodName: "AddFeedback",
			Handler:    _MysteryPlace_AddFeedback_Handler,
		},
		{
			MethodName: "UpdateTabSet",
			Handler:    _MysteryPlace_UpdateTabSet_Handler,
		},
		{
			MethodName: "BatchGetRcmdTabById",
			Handler:    _MysteryPlace_BatchGetRcmdTabById_Handler,
		},
		{
			MethodName: "GetScenarioHotRank",
			Handler:    _MysteryPlace_GetScenarioHotRank_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "mystery-place/mystery-place.proto",
}

func init() {
	proto.RegisterFile("mystery-place/mystery-place.proto", fileDescriptor_mystery_place_c70a2cf0a0ebb671)
}

var fileDescriptor_mystery_place_c70a2cf0a0ebb671 = []byte{
	// 6237 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x7c, 0xcd, 0x6f, 0x1b, 0x4b,
	0x72, 0xb8, 0x48, 0x7d, 0x90, 0x2c, 0x91, 0x14, 0xd5, 0x92, 0x2c, 0x8a, 0xb6, 0x6c, 0x69, 0xfc,
	0xf9, 0xe4, 0xf7, 0xec, 0xf7, 0xbc, 0xcf, 0xf8, 0xfd, 0x92, 0xb7, 0x9b, 0xac, 0x3e, 0x28, 0x9b,
	0x59, 0x89, 0x52, 0x86, 0x94, 0x8d, 0x7d, 0xd8, 0xdd, 0xd9, 0x11, 0xa7, 0x45, 0xcd, 0x9a, 0x9c,
	0x19, 0x4f, 0x0f, 0x65, 0x09, 0x01, 0x12, 0x24, 0xd8, 0x20, 0xc0, 0xe6, 0xb2, 0xc0, 0xe6, 0x18,
	0x04, 0xb9, 0xe4, 0x10, 0xe4, 0x1a, 0x20, 0xfb, 0x0f, 0xe4, 0x10, 0x20, 0xb7, 0xe4, 0x9a, 0x4b,
	0x90, 0x5c, 0x73, 0xca, 0x35, 0x40, 0xd0, 0xd5, 0x3d, 0x9c, 0x6f, 0x4a, 0x7e, 0x79, 0x41, 0x6e,
	0xdd, 0xd5, 0xd5, 0xd5, 0xd5, 0xd5, 0x55, 0xd5, 0xdd, 0xd5, 0x35, 0x03, 0x9b, 0xc3, 0x2b, 0xe6,
	0x51, 0xf7, 0xea, 0x33, 0x67, 0xa0, 0xf7, 0xe8, 0xf3, 0x48, 0xed, 0x99, 0xe3, 0xda, 0x9e, 0x4d,
	0x2a, 0x12, 0xa8, 0x21, 0x50, 0xf9, 0x8b, 0x1c, 0xcc, 0x1f, 0x0f, 0xf4, 0xab, 0xa1, 0xee, 0xd1,
	0xae, 0xde, 0x27, 0x55, 0xc8, 0x9b, 0x46, 0x3d, 0xb7, 0x91, 0x7b, 0x52, 0x52, 0xf3, 0xa6, 0x41,
	0xea, 0x50, 0xe8, 0xd9, 0x96, 0x47, 0x2d, 0xaf, 0x9e, 0x47, 0xa0, 0x5f, 0x25, 0xcb, 0x30, 0xcb,
	0x7a, 0xb6, 0x4b, 0xeb, 0xd3, 0x1b, 0xb9, 0x27, 0x15, 0x55, 0x54, 0xc8, 0x6f, 0x40, 0xd1, 0xd3,
	0xfb, 0x9a, 0x77, 0xe5, 0xd0, 0xfa, 0xcc, 0x46, 0xee, 0x49, 0xf5, 0xc5, 0xdd, 0x67, 0x91, 0x11,
	0x9f, 0x85, 0x46, 0xeb, 0x5e, 0x39, 0x54, 0x2d, 0x78, 0xa2, 0xc0, 0x09, 0xda, 0xae, 0x41, 0xdd,
	0xfa, 0xac, 0x20, 0x88, 0x15, 0x65, 0x1b, 0x16, 0xb7, 0x0d, 0x23, 0xd4, 0x49, 0xa5, 0xef, 0xc9,
	0xa7, 0x30, 0xed, 0xe9, 0x7d, 0x64, 0x73, 0xfe, 0x45, 0x23, 0x7b, 0x00, 0x95, 0xa3, 0x29, 0x3b,
	0x40, 0xe2, 0x24, 0x98, 0xf3, 0x91, 0x34, 0x3e, 0x83, 0xe5, 0x3d, 0x3a, 0xa0, 0x1e, 0x8d, 0x71,
	0xb2, 0x02, 0x73, 0x7c, 0xbe, 0x63, 0x99, 0xcd, 0x7a, 0x7a, 0xbf, 0x65, 0x28, 0xab, 0xb0, 0x92,
	0x82, 0xce, 0x1c, 0x65, 0x0f, 0x96, 0x4f, 0x1c, 0x43, 0x4f, 0xd0, 0xf9, 0x38, 0x6e, 0x56, 0x61,
	0x25, 0x85, 0x0a, 0x73, 0x94, 0x2f, 0xe0, 0x96, 0x4a, 0x51, 0x70, 0xa1, 0x16, 0xc6, 0x07, 0x58,
	0x85, 0x82, 0x60, 0x94, 0xd5, 0x73, 0x1b, 0xd3, 0x4f, 0x4a, 0xea, 0x1c, 0x72, 0xca, 0x94, 0x35,
	0x58, 0x4d, 0xed, 0xc2, 0x1c, 0x65, 0x19, 0xc8, 0x2b, 0xea, 0xc5, 0x28, 0x29, 0x4d, 0x58, 0x4a,
	0x40, 0x99, 0x43, 0x9e, 0xc1, 0x8c, 0xa7, 0xf7, 0x05, 0xf5, 0xc9, 0x53, 0x40, 0x3c, 0xce, 0x6a,
	0x94, 0xcc, 0xce, 0x55, 0xcb, 0x98, 0xcc, 0x6a, 0x0b, 0x56, 0x53, 0xbb, 0x7c, 0x83, 0xd1, 0xff,
	0x29, 0x07, 0x2b, 0xdb, 0x86, 0x71, 0xc2, 0x22, 0xd3, 0xe6, 0xa3, 0xaf, 0x41, 0xf1, 0xcc, 0xb5,
	0x87, 0xda, 0x48, 0xae, 0x69, 0x45, 0x2d, 0xf0, 0xfa, 0x89, 0x69, 0xe0, 0x62, 0xdb, 0xd8, 0x90,
	0x17, 0x2a, 0xea, 0xd9, 0x1c, 0x1c, 0xd6, 0xf9, 0xe9, 0x8f, 0xd3, 0xf9, 0xd0, 0x54, 0x67, 0xc2,
	0x53, 0x25, 0xeb, 0x00, 0x3d, 0x97, 0xea, 0x1e, 0x35, 0x34, 0xdd, 0x93, 0x16, 0x51, 0x92, 0x90,
	0x6d, 0x8f, 0x73, 0xc2, 0xa8, 0xc7, 0xd5, 0x6e, 0x4e, 0xa8, 0x1d, 0xa3, 0x5e, 0xcb, 0x50, 0xea,
	0x70, 0x2b, 0x6d, 0x52, 0xcc, 0x51, 0xfe, 0x3e, 0x07, 0xb7, 0x77, 0x74, 0xef, 0x15, 0xf5, 0x78,
	0xeb, 0x6b, 0xdb, 0x4b, 0xce, 0x7a, 0x64, 0x1a, 0xda, 0xc0, 0x64, 0x1e, 0xca, 0xb0, 0xa2, 0x16,
	0x46, 0xa6, 0x71, 0x60, 0x32, 0x8f, 0x3c, 0x81, 0x9a, 0x4b, 0xbd, 0x91, 0x6b, 0x69, 0x9c, 0xd5,
	0x9e, 0x3d, 0x92, 0xbe, 0xa0, 0xa2, 0x56, 0x05, 0xbc, 0xab, 0xf7, 0x77, 0x39, 0x94, 0x33, 0xcd,
	0x3c, 0xdd, 0x93, 0x38, 0xc2, 0x2f, 0x94, 0x38, 0x44, 0x34, 0xef, 0x40, 0x05, 0x9b, 0x3f, 0xd2,
	0x41, 0xcc, 0xf3, 0x4e, 0xb2, 0xa2, 0xfc, 0x69, 0x0e, 0xaa, 0x51, 0xee, 0xff, 0x0f, 0x5d, 0x96,
	0xf2, 0x53, 0x20, 0x49, 0x71, 0x92, 0x1a, 0x4c, 0x07, 0xca, 0xc3, 0x8b, 0xe4, 0xff, 0x43, 0xf1,
	0xdc, 0xc6, 0x89, 0xb3, 0x7a, 0x1e, 0x35, 0x74, 0x3d, 0x36, 0x44, 0x6c, 0x45, 0x0a, 0xe7, 0x36,
	0x9f, 0x32, 0x53, 0x7e, 0x02, 0x77, 0xb2, 0x97, 0x8d, 0x39, 0xe4, 0xb7, 0xa0, 0x34, 0x62, 0xd4,
	0x0d, 0x16, 0x6e, 0xfe, 0xc5, 0x66, 0x8c, 0x74, 0x4a, 0xcf, 0x22, 0xef, 0xc3, 0x17, 0x57, 0x79,
	0x07, 0xab, 0x1d, 0xea, 0x75, 0x7a, 0xd4, 0xd2, 0x5d, 0xd3, 0xee, 0x9c, 0xeb, 0x2e, 0x3d, 0x30,
	0xad, 0x77, 0x5c, 0x25, 0x8e, 0x61, 0x89, 0x49, 0xb8, 0xc6, 0x78, 0x83, 0x36, 0x30, 0xad, 0x77,
	0xd2, 0x45, 0x6d, 0xc4, 0x06, 0x49, 0x52, 0x58, 0x64, 0x71, 0x90, 0xd2, 0x80, 0x7a, 0xfa, 0x60,
	0xcc, 0x51, 0x3e, 0xc7, 0x89, 0xf6, 0xce, 0x5f, 0x65, 0x70, 0x53, 0x83, 0x69, 0xdf, 0x21, 0x54,
	0x54, 0x5e, 0x54, 0xde, 0xc3, 0xfa, 0x84, 0x1e, 0xcc, 0xc9, 0x9e, 0xc0, 0xf4, 0x37, 0x9d, 0xc0,
	0x27, 0xb0, 0xba, 0x47, 0x07, 0xa9, 0xfc, 0x05, 0x5a, 0x58, 0xe1, 0x5a, 0xc8, 0xe7, 0x9a, 0x8e,
	0xca, 0x1c, 0xc5, 0x83, 0xea, 0x2b, 0xea, 0xb5, 0xe9, 0x07, 0xbe, 0x34, 0xe8, 0xf2, 0x84, 0xe5,
	0xb8, 0xd2, 0xdc, 0x39, 0x95, 0x69, 0xb4, 0x1c, 0x57, 0x98, 0xfb, 0x1a, 0x14, 0xa9, 0x65, 0x88,
	0xc6, 0x3c, 0x36, 0x16, 0xb0, 0xbe, 0xed, 0x11, 0x02, 0x33, 0x8e, 0xde, 0xf7, 0x55, 0x1a, 0xcb,
	0x5c, 0xcf, 0x85, 0x09, 0xce, 0x08, 0x3d, 0xc7, 0x8a, 0xf2, 0x5d, 0x58, 0x88, 0x8c, 0xca, 0x1c,
	0xf2, 0x09, 0xcc, 0x72, 0x4d, 0xf0, 0xdd, 0xe6, 0x52, 0x8a, 0xe6, 0xa8, 0x02, 0x43, 0xf9, 0x1e,
	0xcc, 0xf0, 0x6a, 0x8a, 0x72, 0xdf, 0x87, 0x8a, 0x4b, 0xfb, 0x26, 0xef, 0x18, 0xe6, 0xb0, 0x1c,
	0x00, 0xb7, 0x3d, 0xe5, 0x3f, 0x72, 0xb0, 0x98, 0x10, 0x46, 0x5c, 0x68, 0xdc, 0xad, 0xd1, 0x4b,
	0x47, 0x0b, 0x1c, 0x2c, 0xbd, 0x74, 0x5a, 0x06, 0xb9, 0x0d, 0xa5, 0x81, 0x7e, 0x45, 0x5d, 0x6e,
	0x40, 0x38, 0xd1, 0x92, 0x5a, 0x44, 0x00, 0xb7, 0xb6, 0xaf, 0xa0, 0xac, 0x9f, 0x6a, 0x1e, 0x65,
	0x9e, 0x58, 0xde, 0x19, 0x9c, 0xca, 0x5a, 0x6c, 0x2a, 0xdb, 0x3b, 0x5d, 0xca, 0x3c, 0x5c, 0x01,
	0xd0, 0x4f, 0xfd, 0x32, 0x97, 0x1e, 0x76, 0x9a, 0x45, 0xa2, 0x58, 0x26, 0xf7, 0x60, 0x7e, 0x84,
	0x9b, 0xab, 0xe6, 0x99, 0x43, 0x8a, 0x0e, 0x76, 0x5a, 0x05, 0x01, 0xea, 0x9a, 0x43, 0xca, 0x27,
	0x2c, 0xd4, 0xc9, 0x31, 0x7b, 0xde, 0xc8, 0xa5, 0xf5, 0x02, 0xf6, 0x2e, 0x23, 0xf0, 0x58, 0xc0,
	0x94, 0xef, 0x02, 0x04, 0x63, 0x92, 0x06, 0x94, 0xf8, 0xc4, 0xbc, 0x0b, 0x2d, 0xd8, 0x55, 0xe8,
	0xa5, 0xd3, 0xbd, 0x68, 0x19, 0x63, 0x1e, 0xf2, 0x01, 0x0f, 0xca, 0x3f, 0xe6, 0x61, 0xc5, 0x17,
	0x57, 0xcb, 0xea, 0xbd, 0xa5, 0x66, 0xff, 0xdc, 0x6b, 0x59, 0x67, 0x36, 0xe7, 0xce, 0x64, 0x9a,
	0x69, 0xf1, 0xbd, 0x80, 0x51, 0xa4, 0x55, 0x54, 0xc1, 0x64, 0x2d, 0x09, 0x21, 0xb7, 0x60, 0xee,
	0x03, 0xa2, 0x4b, 0x19, 0xca, 0xda, 0x58, 0xc5, 0xc4, 0xac, 0x02, 0xe7, 0xec, 0x7a, 0x38, 0x29,
	0xa1, 0x62, 0xa2, 0x71, 0x46, 0x32, 0x68, 0x19, 0xd8, 0xd4, 0x82, 0x39, 0xee, 0x82, 0x47, 0x0c,
	0xc5, 0x54, 0x7d, 0xf1, 0x45, 0x86, 0xe9, 0x44, 0x18, 0x7d, 0x26, 0x8a, 0xa2, 0xa3, 0x2a, 0x09,
	0x28, 0x23, 0x28, 0x87, 0xe1, 0xe4, 0x0e, 0xd4, 0xdf, 0x36, 0x5b, 0xaf, 0x5e, 0x77, 0x3b, 0xdd,
	0xed, 0xee, 0x49, 0x47, 0x3b, 0x69, 0x77, 0x8e, 0x9b, 0xbb, 0xad, 0xfd, 0x56, 0x73, 0xaf, 0x36,
	0x45, 0x96, 0x60, 0x21, 0xd2, 0x7a, 0xd4, 0xae, 0xe5, 0xc8, 0x32, 0xd4, 0xa2, 0xc0, 0xfd, 0xfd,
	0x5a, 0x3e, 0x41, 0xa8, 0xd5, 0x6e, 0xee, 0xef, 0x37, 0x77, 0xbb, 0xad, 0x37, 0xcd, 0xda, 0xb4,
	0xf2, 0x47, 0x0b, 0x50, 0x0e, 0xb8, 0x3c, 0xb3, 0x13, 0x8a, 0xf7, 0x25, 0x94, 0x9c, 0x81, 0x7e,
	0xa5, 0x0d, 0x6d, 0x83, 0xa2, 0xdc, 0xaa, 0x2f, 0x56, 0x53, 0x36, 0x81, 0x43, 0xdb, 0xa0, 0x6a,
	0xd1, 0x91, 0x25, 0x71, 0xf8, 0x3b, 0xe5, 0x4b, 0x2a, 0x37, 0x14, 0x4f, 0x3f, 0x6d, 0x19, 0x5c,
	0x94, 0x1c, 0x6c, 0xe9, 0x52, 0x94, 0x25, 0xbe, 0x61, 0x9c, 0xb6, 0xf5, 0x21, 0x4d, 0xd5, 0x37,
	0x05, 0xca, 0xa6, 0xe5, 0xb9, 0xb6, 0x31, 0xea, 0x79, 0xa6, 0x6d, 0xc9, 0x1d, 0x3d, 0x02, 0xe3,
	0x8b, 0x3a, 0xd0, 0x4f, 0xe9, 0x80, 0xd5, 0x0b, 0xe2, 0x98, 0x20, 0x6a, 0xe4, 0x19, 0x2c, 0x59,
	0xf4, 0x83, 0x66, 0x98, 0x0c, 0xd9, 0xf7, 0x15, 0xb2, 0x88, 0x24, 0x16, 0x2d, 0xfa, 0x61, 0x4f,
	0xb4, 0x48, 0xad, 0xe4, 0xf8, 0xf6, 0xc0, 0x48, 0xe0, 0x97, 0x04, 0xbe, 0x3d, 0x30, 0x62, 0xf8,
	0x5b, 0xb0, 0x78, 0x6a, 0xf6, 0xb5, 0xa8, 0xba, 0x03, 0x62, 0x2f, 0x9c, 0x9a, 0xfd, 0x4e, 0x48,
	0xe3, 0x39, 0x6d, 0x36, 0xd4, 0x07, 0x83, 0x18, 0xf6, 0xbc, 0xa0, 0x8d, 0x4d, 0x11, 0xfc, 0x65,
	0x98, 0xbd, 0x30, 0x0d, 0x6a, 0xd7, 0xcb, 0xe2, 0x08, 0x83, 0x15, 0xb2, 0x09, 0x65, 0x31, 0x37,
	0xad, 0x67, 0x0f, 0x6c, 0xb7, 0x5e, 0xc1, 0xc6, 0x79, 0x01, 0xdb, 0xe5, 0x20, 0xee, 0x0e, 0xa4,
	0x81, 0xea, 0x5e, 0xbd, 0x8a, 0xe6, 0x59, 0x14, 0x80, 0x6d, 0xdc, 0xe3, 0x3d, 0xd3, 0x1b, 0xd0,
	0xfa, 0x82, 0x3c, 0x8f, 0xf3, 0x0a, 0xf9, 0x0a, 0x4a, 0xb8, 0x52, 0xd6, 0x99, 0xcd, 0xea, 0x35,
	0xf4, 0x10, 0x77, 0x33, 0xb4, 0xb8, 0xab, 0x9f, 0x72, 0x15, 0x51, 0xf9, 0x1a, 0xf2, 0x02, 0xe3,
	0x2c, 0x99, 0x4c, 0x73, 0x69, 0xcf, 0x1e, 0x0e, 0xa9, 0x65, 0xd4, 0x17, 0xd1, 0xe6, 0xe6, 0x4d,
	0xa6, 0xfa, 0x20, 0xd2, 0x80, 0xa2, 0xe3, 0x9a, 0xb6, 0x6b, 0x7a, 0x57, 0x75, 0x82, 0xba, 0x30,
	0xae, 0x93, 0x97, 0xb0, 0x6a, 0xda, 0x4c, 0x1b, 0xd8, 0x1f, 0xa8, 0xab, 0x5d, 0x50, 0x97, 0x99,
	0xb6, 0xa5, 0x0d, 0xcc, 0xa1, 0xe9, 0xd5, 0x97, 0x90, 0xc7, 0x65, 0xd3, 0x66, 0x07, 0xbc, 0xf5,
	0x8d, 0x68, 0x3c, 0xe0, 0x6d, 0xe4, 0x7b, 0x70, 0x5b, 0xb7, 0x0c, 0xd7, 0xe6, 0xa7, 0xb2, 0x94,
	0xae, 0xcb, 0xd8, 0xb5, 0x2e, 0x51, 0x92, 0xdd, 0xeb, 0x50, 0x38, 0xd5, 0x5d, 0x97, 0x6f, 0x0d,
	0x2b, 0xa8, 0x32, 0x7e, 0x95, 0x3c, 0x86, 0x85, 0xf1, 0x5c, 0x34, 0xb1, 0x02, 0xb7, 0x90, 0x58,
	0x75, 0x0c, 0x7e, 0x83, 0x4b, 0xf1, 0x14, 0x16, 0x03, 0x44, 0x7f, 0x39, 0x57, 0x91, 0x58, 0x6d,
	0xdc, 0xe0, 0xaf, 0xe6, 0x97, 0x70, 0x2b, 0xd8, 0x6c, 0x51, 0x0d, 0xfc, 0x1e, 0x75, 0x31, 0xc9,
	0xf1, 0x6e, 0xca, 0x1b, 0xfd, 0x5e, 0x9f, 0xc3, 0x32, 0x65, 0x9e, 0xc9, 0xcf, 0x25, 0xda, 0x99,
	0x69, 0x99, 0xec, 0x5c, 0x78, 0xa0, 0x35, 0xec, 0x43, 0xfc, 0xb6, 0x7d, 0x6c, 0x42, 0x67, 0xb4,
	0x09, 0x65, 0xfd, 0x42, 0xf7, 0x74, 0x57, 0xf3, 0x6c, 0x9d, 0x79, 0xf5, 0x06, 0xf2, 0x33, 0x2f,
	0x60, 0x5d, 0x0e, 0x22, 0x0f, 0xa1, 0x8a, 0xeb, 0xc5, 0xa8, 0x7b, 0xa1, 0xa3, 0x49, 0xdd, 0xc6,
	0x15, 0xab, 0xf0, 0x15, 0x1b, 0x03, 0xf9, 0xb9, 0xd6, 0x31, 0x69, 0xcf, 0xb4, 0xfa, 0x5a, 0xdf,
	0xb5, 0x47, 0x8e, 0x76, 0xda, 0xaf, 0xdf, 0x11, 0x82, 0x90, 0xf0, 0x57, 0x1c, 0xbc, 0xc3, 0xb7,
	0x98, 0x06, 0xb7, 0xb2, 0x8c, 0xf9, 0xad, 0x63, 0x9f, 0x55, 0x8b, 0x7e, 0xe8, 0xa4, 0x4d, 0xf1,
	0x05, 0xac, 0xf0, 0xce, 0x49, 0x49, 0xde, 0xc5, 0x7e, 0xdc, 0x7e, 0xd5, 0xb8, 0x30, 0xc3, 0x47,
	0xd2, 0x7b, 0xa9, 0x47, 0xd2, 0x40, 0x5b, 0x63, 0x37, 0x8a, 0x07, 0x50, 0x1d, 0x9a, 0x16, 0xc7,
	0xe2, 0x1b, 0xa6, 0x35, 0x1a, 0xd6, 0x37, 0x50, 0x1f, 0xcb, 0x43, 0xd3, 0x3a, 0x46, 0x60, 0x7b,
	0x34, 0xe4, 0x3a, 0xd0, 0xd7, 0x87, 0x54, 0x33, 0xcc, 0xb3, 0x33, 0xb3, 0x37, 0x1a, 0x78, 0x57,
	0xf5, 0x4d, 0x31, 0x75, 0x0e, 0xde, 0x1b, 0x43, 0xb9, 0xb8, 0x5d, 0x7b, 0x10, 0x58, 0xb3, 0x22,
	0xcc, 0x91, 0xc3, 0x7c, 0x66, 0x1f, 0x42, 0xd5, 0xa2, 0x5c, 0x41, 0xc7, 0x16, 0x70, 0x1f, 0x47,
	0xac, 0x20, 0xf4, 0xd8, 0x37, 0x03, 0xbe, 0xff, 0x9c, 0xdb, 0xae, 0x27, 0xfc, 0xe2, 0x03, 0xa4,
	0x53, 0x42, 0x08, 0x7a, 0xc6, 0x5d, 0xa8, 0xf6, 0xce, 0x75, 0x57, 0xef, 0x79, 0xd4, 0x45, 0x3b,
	0xad, 0x3f, 0x44, 0x33, 0xbd, 0x13, 0x9b, 0xf8, 0xae, 0x8f, 0x84, 0x46, 0x5a, 0xe9, 0x85, 0xab,
	0x44, 0x81, 0x8a, 0xc9, 0x34, 0x76, 0x6e, 0x7f, 0xe0, 0xcc, 0xf4, 0x68, 0xfd, 0x91, 0x6f, 0xaa,
	0x9d, 0x73, 0xfb, 0xc3, 0x31, 0x07, 0x71, 0xef, 0x81, 0x6d, 0x28, 0x9b, 0xc7, 0x63, 0x5b, 0xed,
	0x51, 0x2e, 0x17, 0xee, 0x7d, 0x4c, 0xe6, 0x71, 0x9d, 0x40, 0x3d, 0x7c, 0x82, 0xde, 0x65, 0x5e,
	0xc2, 0xfc, 0x8d, 0xd2, 0x64, 0x9a, 0xe7, 0x9a, 0xfa, 0xa0, 0xfe, 0x09, 0x92, 0x2f, 0x98, 0xac,
	0xcb, 0xab, 0x5c, 0xf6, 0xa7, 0x26, 0xdf, 0x44, 0x6d, 0xc7, 0xec, 0xe1, 0xa5, 0x6e, 0x0b, 0xb5,
	0xb3, 0xcc, 0xa1, 0x5d, 0x0e, 0xe4, 0x57, 0xbb, 0xf1, 0x2d, 0xe4, 0xe9, 0x46, 0xee, 0x49, 0xde,
	0xbf, 0x85, 0xac, 0x03, 0x78, 0x17, 0x63, 0x31, 0x7f, 0x2a, 0xc4, 0xe3, 0x5d, 0xf8, 0x42, 0x6e,
	0xc2, 0xbc, 0xd8, 0xc7, 0x85, 0x6c, 0x3e, 0xc3, 0x43, 0xf8, 0x83, 0x9b, 0x6c, 0xc4, 0x2a, 0x7c,
	0x08, 0x4e, 0x0f, 0x2d, 0xa8, 0xe1, 0xba, 0xdb, 0xae, 0x49, 0x2d, 0x4f, 0x18, 0xc7, 0xb3, 0x54,
	0x05, 0x7b, 0xa5, 0x0f, 0xe9, 0x51, 0x80, 0xa5, 0xa2, 0xbe, 0x84, 0x00, 0xe4, 0x39, 0x2c, 0x99,
	0xd6, 0x85, 0x29, 0x6a, 0x9a, 0xde, 0xf3, 0xcc, 0x0b, 0xbe, 0xf6, 0xcf, 0x85, 0xe5, 0x06, 0x4d,
	0xdb, 0xb2, 0x45, 0xf9, 0xe7, 0x1c, 0x2c, 0xc4, 0x9c, 0xec, 0xb7, 0xbb, 0xef, 0x12, 0x98, 0x31,
	0x7b, 0xb6, 0x25, 0xf7, 0x5c, 0x2c, 0x93, 0xdf, 0x06, 0xe0, 0xa8, 0x91, 0xf3, 0xcb, 0x46, 0xb6,
	0xe7, 0xef, 0x88, 0xe3, 0x0a, 0xdf, 0x2c, 0x44, 0x91, 0x9f, 0xb7, 0x7a, 0xe7, 0xba, 0xe3, 0x49,
	0x63, 0x9a, 0xc3, 0x01, 0x41, 0x82, 0xda, 0xa3, 0xa1, 0xd2, 0x82, 0x4a, 0x44, 0x27, 0x39, 0x1b,
	0xa8, 0xe2, 0xe2, 0x46, 0x8a, 0x65, 0xbc, 0xbd, 0x0c, 0xfb, 0xf2, 0x88, 0xc7, 0x8b, 0xc8, 0x2c,
	0x5f, 0xc9, 0x69, 0xc9, 0xac, 0x75, 0x66, 0x2b, 0xff, 0x9a, 0x83, 0x25, 0x7e, 0x2b, 0x0b, 0x1f,
	0x55, 0xf8, 0xed, 0x60, 0x7c, 0x9e, 0xcf, 0x85, 0xce, 0xf3, 0xe3, 0x93, 0x7f, 0x3e, 0x74, 0xf2,
	0x7f, 0x0c, 0x0b, 0xfe, 0xa6, 0x21, 0xb7, 0x0b, 0x39, 0x40, 0x55, 0x82, 0xe5, 0x1e, 0x81, 0xc7,
	0x48, 0x9b, 0x8d, 0x91, 0x84, 0xc8, 0xc0, 0xb4, 0x99, 0x8f, 0xf0, 0x25, 0x94, 0x18, 0xb7, 0x56,
	0xf4, 0x41, 0xb3, 0xa9, 0x2b, 0xd3, 0xb1, 0x5d, 0x0f, 0x9d, 0x4f, 0x91, 0xc9, 0x52, 0xc2, 0x7e,
	0xe6, 0x12, 0xf6, 0xa3, 0xb4, 0x60, 0x39, 0x39, 0x47, 0xe6, 0x90, 0x2f, 0x60, 0x56, 0x6c, 0xcf,
	0xe2, 0x2e, 0x72, 0x3b, 0x53, 0xb7, 0xcf, 0x6c, 0x55, 0x60, 0x2a, 0xe7, 0x70, 0x27, 0x74, 0xf9,
	0xe3, 0x2d, 0x5d, 0xdb, 0xd3, 0x07, 0x18, 0x6d, 0xe0, 0x72, 0xfb, 0xd6, 0xa4, 0xa1, 0xbc, 0x84,
	0xf5, 0x09, 0x23, 0x31, 0x27, 0x7d, 0x89, 0x94, 0x07, 0x18, 0x40, 0x8b, 0x2f, 0x67, 0xfc, 0xaa,
	0xb8, 0x8f, 0x01, 0xb5, 0x84, 0x40, 0x9e, 0x4b, 0x0d, 0x11, 0x17, 0xee, 0x89, 0xf2, 0x10, 0xea,
	0xf3, 0x00, 0x48, 0xe8, 0xca, 0x99, 0x35, 0xda, 0x0a, 0x2c, 0x25, 0xb0, 0x98, 0xa3, 0xbc, 0x86,
	0x95, 0x13, 0x87, 0x51, 0x37, 0xc1, 0xed, 0x47, 0xb3, 0xf1, 0x04, 0x6e, 0xa5, 0x51, 0x62, 0x4e,
	0x82, 0x95, 0x0d, 0x28, 0xb7, 0x98, 0xbc, 0x90, 0xca, 0x3b, 0x7e, 0xf4, 0x6e, 0xa9, 0x3c, 0x82,
	0x4a, 0x08, 0x83, 0x39, 0xdc, 0xf4, 0x4d, 0xa6, 0x59, 0xf4, 0x83, 0xbc, 0xf9, 0xcc, 0x9a, 0xbc,
	0x59, 0x69, 0x82, 0xe2, 0xc7, 0x02, 0xb8, 0xe3, 0x3a, 0xa6, 0x6e, 0x8f, 0x5a, 0xde, 0xce, 0xd5,
	0xee, 0xb9, 0x6e, 0x59, 0x74, 0x20, 0x03, 0x8b, 0xc2, 0x96, 0x39, 0x40, 0x0b, 0x62, 0x09, 0xd0,
	0x1b, 0xe3, 0x28, 0x3f, 0x83, 0xfb, 0xd7, 0x92, 0x61, 0x0e, 0xd9, 0x85, 0x0a, 0x7a, 0x51, 0x47,
	0xb4, 0xfb, 0x2a, 0x1b, 0x77, 0xa1, 0xaa, 0x6d, 0x0f, 0x43, 0x64, 0xd4, 0x72, 0x3f, 0xa8, 0x30,
	0x65, 0x6b, 0x1c, 0xff, 0xa4, 0xee, 0x81, 0xce, 0x3c, 0x7e, 0x08, 0x70, 0x8d, 0x74, 0x31, 0xfc,
	0x68, 0x1c, 0xf8, 0x8c, 0xe2, 0x32, 0x87, 0x6c, 0x43, 0x39, 0xcc, 0x8b, 0x5c, 0xa6, 0xbb, 0x29,
	0x37, 0xf9, 0x30, 0x2b, 0xf3, 0x21, 0x56, 0x94, 0x2e, 0x3c, 0xf4, 0x67, 0x2d, 0x86, 0x88, 0xcc,
	0x9d, 0x57, 0xa4, 0xfc, 0x92, 0x77, 0xff, 0x35, 0x28, 0xe2, 0xe8, 0x5c, 0x9c, 0x79, 0x11, 0x36,
	0xec, 0x0b, 0x7c, 0x65, 0x08, 0x8f, 0x6e, 0x42, 0xf5, 0xe6, 0xe2, 0x8c, 0xcf, 0x21, 0x2a, 0xce,
	0x9f, 0xc1, 0x2a, 0x0e, 0xb7, 0x7b, 0x4e, 0x7b, 0xef, 0x5e, 0xd3, 0x81, 0x23, 0x02, 0x5c, 0xe3,
	0x18, 0xfd, 0x69, 0x70, 0xf3, 0x0e, 0xae, 0x69, 0xe3, 0x90, 0x67, 0x3e, 0x1a, 0xf2, 0x5c, 0x07,
	0x90, 0x07, 0xa8, 0xd1, 0x78, 0x93, 0x29, 0x09, 0xc8, 0x89, 0x69, 0x28, 0x87, 0x50, 0x4f, 0x1f,
	0x0b, 0xdd, 0xd8, 0x4a, 0x4f, 0xb7, 0xb4, 0x0f, 0x74, 0xd0, 0xb3, 0x87, 0x54, 0x3b, 0xa5, 0xda,
	0x39, 0x22, 0x48, 0x6d, 0x23, 0x3d, 0xdd, 0x7a, 0x2b, 0xda, 0x76, 0xa8, 0xe8, 0xaa, 0x0c, 0xa0,
	0xc2, 0xe7, 0xb5, 0x6b, 0x0f, 0x87, 0xb6, 0x75, 0xc8, 0xfa, 0xe4, 0x37, 0xa1, 0x24, 0x4e, 0x67,
	0xba, 0xa7, 0xcb, 0x05, 0x5d, 0x4f, 0xd9, 0x9e, 0x45, 0x87, 0x3d, 0xdd, 0xd3, 0x55, 0x5c, 0x05,
	0x5e, 0x8a, 0x47, 0x2f, 0xc4, 0xe6, 0x10, 0x8a, 0x5e, 0x28, 0x7f, 0x32, 0x0d, 0xd5, 0x68, 0x6f,
	0xb2, 0xcb, 0x0f, 0x79, 0xf6, 0xf0, 0x1a, 0x1d, 0x8a, 0xa9, 0xf3, 0xeb, 0x29, 0x7e, 0x0c, 0xb4,
	0x87, 0xb2, 0xca, 0x89, 0x60, 0x24, 0xd2, 0x27, 0x92, 0xbf, 0x89, 0x22, 0x72, 0x22, 0xbc, 0x57,
	0x40, 0xa4, 0x8a, 0x44, 0xb8, 0xcc, 0xc4, 0xf4, 0xa7, 0x53, 0xdd, 0x0e, 0xc6, 0x34, 0xe9, 0xc0,
	0xe1, 0xec, 0xbf, 0x9e, 0x52, 0x71, 0x64, 0xbf, 0x4e, 0x0e, 0x60, 0x11, 0x89, 0xf8, 0xfb, 0x36,
	0xd2, 0x99, 0xc9, 0x64, 0x67, 0x57, 0xa0, 0x49, 0x52, 0x0b, 0xa3, 0x28, 0x88, 0x1c, 0xc3, 0x12,
	0x52, 0xc3, 0x15, 0xf1, 0x74, 0xf6, 0x4e, 0xd0, 0x9b, 0x45, 0x7a, 0xf7, 0x32, 0xa6, 0xd7, 0xd5,
	0xd9, 0x3b, 0x49, 0xb0, 0x36, 0x8a, 0xc1, 0x76, 0xe6, 0x60, 0x86, 0x93, 0x50, 0x3e, 0xc0, 0x42,
	0x4c, 0xa6, 0x64, 0x15, 0x0a, 0xd2, 0x9e, 0xa4, 0xae, 0xce, 0x09, 0x73, 0xe2, 0x0d, 0xb8, 0x44,
	0xe3, 0xd0, 0xd8, 0x1c, 0xaf, 0xb6, 0x44, 0xb4, 0x5b, 0x70, 0x2b, 0xf5, 0xd4, 0xaf, 0xf2, 0x16,
	0x7f, 0x2d, 0xf8, 0xe4, 0x73, 0xaa, 0x5f, 0x55, 0x1c, 0x58, 0x88, 0xad, 0x43, 0xf6, 0xc0, 0xd2,
	0xe6, 0xf3, 0x81, 0xcd, 0x7f, 0x93, 0x11, 0x47, 0x50, 0x0e, 0x2f, 0x59, 0xba, 0x27, 0xc1, 0x45,
	0x0f, 0x06, 0x2b, 0xf0, 0xfa, 0x89, 0x69, 0x84, 0x79, 0x9b, 0x8e, 0xf0, 0x76, 0x0f, 0xe6, 0x0d,
	0xdd, 0x1c, 0x5c, 0xc9, 0x2b, 0xf1, 0x8c, 0x88, 0x85, 0x21, 0x08, 0x2f, 0xc1, 0xca, 0xcf, 0x73,
	0x62, 0xa6, 0xe1, 0xf5, 0xcc, 0x9c, 0x29, 0x81, 0x99, 0x51, 0xe0, 0xc7, 0xb0, 0x4c, 0xee, 0x43,
	0xe5, 0x94, 0xf6, 0x4d, 0x4b, 0x8b, 0xce, 0xb8, 0x8c, 0x40, 0x49, 0x95, 0xb3, 0xc1, 0xef, 0x75,
	0x3e, 0x8a, 0x88, 0x9e, 0x01, 0xb5, 0x0c, 0x89, 0xa0, 0xfc, 0x5b, 0x0e, 0x6a, 0x71, 0xcd, 0x48,
	0x0f, 0xa4, 0x7a, 0x54, 0x1f, 0xe2, 0x65, 0x38, 0xc4, 0x49, 0xd9, 0x07, 0x9e, 0x70, 0x8e, 0x32,
	0x85, 0x11, 0x5a, 0x96, 0x99, 0xe8, 0xb2, 0xac, 0x41, 0xd1, 0x70, 0xf5, 0xa1, 0xce, 0xfb, 0x88,
	0xc0, 0x53, 0x01, 0xeb, 0x42, 0xad, 0x9c, 0x81, 0x1d, 0x7a, 0x48, 0x9a, 0xe3, 0x55, 0xd1, 0x80,
	0xba, 0x6e, 0x1a, 0x32, 0xba, 0x39, 0xc7, 0xab, 0xc2, 0x6b, 0xba, 0xf6, 0xc8, 0x32, 0x78, 0x8b,
	0x08, 0x33, 0x15, 0xb0, 0xde, 0x32, 0x14, 0x1b, 0x96, 0x55, 0xaa, 0xbb, 0xae, 0x6e, 0xf5, 0xa9,
	0xbf, 0xf7, 0x73, 0xff, 0xbb, 0x09, 0xe5, 0xf1, 0xd5, 0x39, 0xd8, 0x77, 0xe7, 0x7d, 0x18, 0xbf,
	0x13, 0x45, 0x4e, 0x9b, 0xf9, 0x1b, 0x9e, 0x36, 0x95, 0x55, 0x58, 0x49, 0x19, 0x90, 0x39, 0xca,
	0x5f, 0xe6, 0x60, 0xb5, 0xc5, 0x6f, 0x20, 0xf4, 0xc5, 0xe1, 0x15, 0xb7, 0x31, 0x95, 0xb2, 0xd1,
	0xc0, 0x3b, 0x1e, 0xb1, 0x73, 0xee, 0xdb, 0xf1, 0x72, 0x42, 0x43, 0x2f, 0x7c, 0x25, 0x01, 0xe1,
	0xca, 0xc6, 0xef, 0x61, 0xb6, 0x26, 0xcf, 0x04, 0x52, 0x13, 0x4b, 0x9e, 0x2d, 0x4f, 0x00, 0xe4,
	0x15, 0x54, 0x64, 0x6f, 0x17, 0x49, 0xca, 0x07, 0x3f, 0x25, 0xc6, 0xec, 0xa1, 0xa8, 0x09, 0x1e,
	0xc4, 0xe0, 0x6a, 0xd9, 0x0c, 0xd5, 0x94, 0x3f, 0xcf, 0xc3, 0x1a, 0xaa, 0x26, 0xde, 0xfc, 0xbd,
	0x80, 0x7d, 0xbe, 0xb3, 0x27, 0xde, 0xb4, 0x92, 0x56, 0x78, 0x0f, 0xe6, 0x43, 0x42, 0x95, 0xba,
	0x00, 0x81, 0x4c, 0xc9, 0x33, 0x58, 0x32, 0x99, 0x0c, 0xaa, 0x50, 0x43, 0x13, 0x11, 0x06, 0xdf,
	0x48, 0x16, 0x4d, 0xb6, 0x2f, 0x5b, 0x24, 0x03, 0xc1, 0xb5, 0x74, 0x36, 0xfc, 0x38, 0xb6, 0x09,
	0x65, 0xd9, 0x53, 0xf3, 0xe8, 0xa5, 0x27, 0xb5, 0x64, 0x5e, 0xc2, 0xba, 0xf4, 0x12, 0x37, 0x4b,
	0x93, 0xf9, 0x21, 0x45, 0xd4, 0x96, 0xa2, 0x5a, 0x32, 0x99, 0x8c, 0x24, 0xf2, 0xfb, 0xb6, 0x78,
	0xb7, 0xd4, 0x74, 0x0f, 0x35, 0x66, 0x5a, 0x2d, 0x0a, 0xc0, 0x36, 0xba, 0x1d, 0xc7, 0x66, 0xa8,
	0x7f, 0x25, 0xa9, 0x7f, 0x36, 0xf3, 0x5a, 0x86, 0xf2, 0x63, 0x58, 0x93, 0x2f, 0x99, 0x09, 0x01,
	0xbd, 0x27, 0xdf, 0x87, 0x39, 0x17, 0xe5, 0x24, 0x77, 0xaa, 0x27, 0x69, 0x5e, 0x3d, 0x4d, 0xae,
	0xaa, 0xec, 0xa7, 0xdc, 0x81, 0x46, 0x16, 0x79, 0xe6, 0x28, 0x5f, 0xc1, 0x5d, 0xff, 0xe8, 0x92,
	0xc1, 0xc1, 0x1a, 0x14, 0x25, 0xdf, 0xfe, 0x1b, 0x75, 0x41, 0x30, 0xce, 0x14, 0x0a, 0xf7, 0x26,
	0x76, 0x66, 0x0e, 0xd9, 0x81, 0x82, 0xe0, 0xc3, 0x3f, 0xea, 0xdc, 0x7c, 0x02, 0x7e, 0x47, 0xe5,
	0xdf, 0xf3, 0x70, 0x2b, 0x7c, 0x8f, 0x92, 0xe8, 0xd7, 0xbc, 0xe5, 0xc6, 0x4d, 0x31, 0x9f, 0x34,
	0xc5, 0xdd, 0xd0, 0x8a, 0x07, 0x2f, 0xda, 0xf1, 0x3b, 0xb3, 0x1c, 0x8e, 0x9b, 0xe1, 0xbe, 0x39,
	0xf0, 0xa8, 0x1b, 0xe8, 0x04, 0xbf, 0x07, 0x46, 0x1f, 0x1b, 0x66, 0x42, 0xef, 0x59, 0x89, 0xc7,
	0x86, 0xd9, 0xf1, 0x7b, 0x16, 0x36, 0xed, 0x42, 0xd5, 0x8f, 0x4e, 0x9f, 0x21, 0x61, 0x54, 0xb9,
	0x6a, 0x22, 0x0e, 0x24, 0xd5, 0x4b, 0x0e, 0x5e, 0x31, 0xc2, 0x55, 0xae, 0xcb, 0x62, 0x4b, 0x28,
	0x20, 0x71, 0x51, 0x21, 0xb7, 0x60, 0xce, 0x3e, 0x3b, 0x63, 0xd4, 0x57, 0x43, 0x59, 0xe3, 0x1a,
	0x6a, 0x5b, 0x83, 0x2b, 0xa1, 0xe0, 0x25, 0xd4, 0xdf, 0x22, 0x07, 0x70, 0xed, 0x56, 0x18, 0xac,
	0xa6, 0x8a, 0xf9, 0xdb, 0x59, 0x46, 0x0c, 0x57, 0xf3, 0x9b, 0xa4, 0x7c, 0x34, 0x13, 0x15, 0xa5,
	0x05, 0x75, 0x91, 0xdf, 0x91, 0xb2, 0xba, 0x71, 0xd7, 0x10, 0x35, 0xbf, 0x7c, 0xcc, 0xfc, 0x94,
	0xdb, 0xb0, 0x96, 0x41, 0x8a, 0x39, 0xca, 0x7f, 0xe5, 0xe1, 0x4e, 0x0c, 0xde, 0x19, 0x0d, 0x87,
	0xba, 0x7b, 0x25, 0xfd, 0x50, 0xcc, 0xcb, 0xe4, 0x12, 0x5e, 0x86, 0xef, 0x59, 0x9c, 0x65, 0x4d,
	0xbf, 0xa0, 0xae, 0x1f, 0x8d, 0xc8, 0xab, 0x65, 0x04, 0x6e, 0x0b, 0x18, 0xf9, 0x04, 0x6a, 0x63,
	0x3f, 0xe4, 0xe3, 0x4d, 0x23, 0xde, 0x82, 0x0f, 0xf7, 0x51, 0x3f, 0x03, 0x32, 0xb2, 0x12, 0xc8,
	0x33, 0x88, 0xbc, 0x18, 0xb4, 0xf8, 0xe8, 0x0f, 0xa1, 0xea, 0x2b, 0xeb, 0x90, 0x0e, 0x4f, 0xc7,
	0xc9, 0x43, 0x15, 0x09, 0x3d, 0x44, 0x20, 0xe7, 0xd2, 0x47, 0x13, 0xb7, 0x74, 0x11, 0xc6, 0xf1,
	0x15, 0x5d, 0xa4, 0x27, 0x3c, 0x84, 0xea, 0x50, 0xb7, 0x46, 0xa1, 0xb9, 0x14, 0x70, 0xd8, 0x8a,
	0x80, 0xfa, 0x43, 0x3e, 0x86, 0x85, 0xa1, 0xde, 0x3b, 0x37, 0x2d, 0x3a, 0xc6, 0x2b, 0x22, 0x5e,
	0x55, 0x82, 0x7d, 0xc4, 0x4d, 0x28, 0xbb, 0x34, 0x44, 0xad, 0x84, 0x58, 0xf3, 0x1c, 0x26, 0x51,
	0x94, 0x1d, 0x58, 0x4f, 0x51, 0xae, 0xf1, 0x12, 0xdc, 0x64, 0xeb, 0x54, 0xfa, 0x70, 0x77, 0x12,
	0x0d, 0xe6, 0x90, 0x66, 0x5c, 0x4f, 0x9f, 0x66, 0x5c, 0xe2, 0xd3, 0x54, 0x20, 0xf0, 0x38, 0x14,
	0x6e, 0x87, 0x5e, 0xef, 0x0f, 0xc3, 0x42, 0x91, 0x97, 0xeb, 0xc9, 0xaa, 0x92, 0x94, 0x6f, 0x3e,
	0x45, 0xbe, 0xca, 0x5d, 0xb8, 0x93, 0x3d, 0x0c, 0x73, 0x94, 0x06, 0xd4, 0x55, 0x7a, 0xe6, 0x52,
	0x76, 0x9e, 0x10, 0x17, 0x57, 0xf6, 0x8c, 0x36, 0xe6, 0x28, 0x7f, 0x97, 0x83, 0x6a, 0x3b, 0x08,
	0xd2, 0x77, 0x4d, 0x8c, 0x26, 0x78, 0xa6, 0x13, 0xce, 0xde, 0x32, 0x9d, 0x56, 0x42, 0xeb, 0xf3,
	0x89, 0xa9, 0x64, 0x04, 0x20, 0xbf, 0x0b, 0x45, 0x4e, 0x2e, 0x94, 0x49, 0x12, 0xcf, 0xc5, 0x88,
	0x8e, 0x2f, 0x23, 0xf7, 0xa2, 0x10, 0xce, 0x5b, 0x99, 0x8d, 0xe4, 0xad, 0x28, 0x9f, 0xc0, 0x8a,
	0x78, 0xb9, 0x0f, 0xf5, 0x4d, 0xbf, 0x90, 0x2b, 0x3f, 0xc0, 0xa8, 0x42, 0x02, 0x15, 0x2f, 0xa6,
	0x33, 0x9e, 0xe9, 0xf8, 0x2a, 0xb0, 0x3e, 0x91, 0x31, 0x15, 0x51, 0x95, 0x9f, 0xc0, 0xda, 0xa1,
	0xee, 0xbe, 0x8b, 0xb5, 0x51, 0x3d, 0x3d, 0x4a, 0x41, 0x9e, 0xc3, 0xb4, 0x67, 0x3a, 0xf2, 0xe2,
	0x77, 0xcd, 0x00, 0x1c, 0x93, 0xef, 0xc2, 0x59, 0xf4, 0x31, 0x23, 0x64, 0x25, 0x9c, 0xda, 0x11,
	0x18, 0x45, 0xd6, 0x09, 0x5e, 0xf9, 0x45, 0x0e, 0xee, 0xfa, 0xe1, 0x06, 0x23, 0xea, 0x71, 0xfd,
	0x58, 0x40, 0x92, 0xeb, 0xef, 0x43, 0x69, 0x60, 0xeb, 0x86, 0x36, 0xe4, 0x67, 0x1f, 0xc1, 0xfb,
	0xfd, 0xf4, 0xd3, 0xdc, 0x31, 0xaf, 0x1c, 0xd8, 0xba, 0x71, 0x68, 0xbb, 0x54, 0x2d, 0x0e, 0x64,
	0x29, 0x88, 0xfd, 0x4d, 0x87, 0x63, 0x7f, 0x7f, 0x9d, 0x83, 0x7b, 0x13, 0x99, 0x61, 0x4e, 0x74,
	0xec, 0xdc, 0x37, 0x19, 0x7b, 0x0f, 0xaa, 0x18, 0x97, 0x30, 0x34, 0xdf, 0xc4, 0xd3, 0xf3, 0x8b,
	0x62, 0xfb, 0x4f, 0x45, 0x74, 0x52, 0xa5, 0x69, 0xff, 0x43, 0x0e, 0xaa, 0xb1, 0x13, 0xe8, 0x4d,
	0x3c, 0xff, 0x18, 0x01, 0xe3, 0xdd, 0x79, 0x99, 0x05, 0x21, 0x81, 0xf8, 0xaa, 0x53, 0x85, 0xfc,
	0xa9, 0x9f, 0xb2, 0x91, 0x3f, 0xed, 0x73, 0xe7, 0xe9, 0xe8, 0x8c, 0xd1, 0xf1, 0x6d, 0x49, 0xe4,
	0xbd, 0x55, 0xd4, 0xaa, 0x00, 0xcb, 0x1b, 0x13, 0x23, 0x8f, 0x60, 0x41, 0x1f, 0x0c, 0xb4, 0x70,
	0xe8, 0x5d, 0x7a, 0x76, 0x7d, 0x30, 0xd8, 0x1d, 0x47, 0xdf, 0xf9, 0x7a, 0x72, 0x9d, 0x13, 0xc7,
	0x52, 0x54, 0xaa, 0x13, 0xd8, 0xc8, 0x10, 0xfb, 0x1e, 0xf5, 0x74, 0x73, 0x90, 0xae, 0x05, 0xd7,
	0x99, 0xbc, 0xf2, 0x07, 0xb0, 0x79, 0x0d, 0x59, 0xe6, 0x90, 0xaf, 0x60, 0xce, 0xc0, 0x5a, 0xc6,
	0x62, 0xa6, 0x76, 0x94, 0x5d, 0xe4, 0x46, 0x7e, 0x61, 0x32, 0xf3, 0x74, 0x40, 0x83, 0x8d, 0xfc,
	0x8d, 0x00, 0x28, 0x7f, 0x9b, 0x87, 0xe5, 0xb4, 0xfe, 0xd7, 0xaf, 0x54, 0xc8, 0x5e, 0xf2, 0x91,
	0x2b, 0x63, 0x86, 0x1b, 0xcb, 0xbe, 0x49, 0x7e, 0xc5, 0xaf, 0x8b, 0xfa, 0x15, 0x75, 0xbf, 0x90,
	0xf1, 0x8f, 0xcd, 0x09, 0xda, 0x2a, 0xa2, 0x7e, 0xaa, 0xdf, 0x23, 0xe8, 0xfc, 0x02, 0x97, 0xeb,
	0x63, 0x3a, 0xbf, 0xe0, 0x3c, 0x31, 0xe1, 0x01, 0xe4, 0x7d, 0xd4, 0xaf, 0xc6, 0x32, 0x25, 0x8b,
	0xb1, 0x4c, 0x49, 0xe5, 0x47, 0x40, 0x92, 0x74, 0x53, 0x14, 0xa0, 0x0e, 0x05, 0xbd, 0x17, 0x24,
	0x37, 0x96, 0x54, 0xbf, 0x4a, 0x1a, 0x50, 0xb4, 0xcc, 0xde, 0x3b, 0xd4, 0x71, 0x99, 0x7c, 0xe4,
	0xd7, 0x15, 0x15, 0x96, 0xd3, 0x0c, 0x54, 0x64, 0x2c, 0x31, 0x4f, 0xc3, 0x07, 0x1a, 0x31, 0x4a,
	0x91, 0x03, 0x8e, 0xf9, 0xc1, 0x60, 0x1d, 0x00, 0x1b, 0xc3, 0xa9, 0x94, 0x88, 0x8e, 0xe7, 0x10,
	0xe5, 0x8f, 0x73, 0x91, 0x67, 0x0d, 0xa9, 0xec, 0x37, 0xf0, 0x7f, 0xe1, 0x85, 0xcb, 0x47, 0x17,
	0x4e, 0xce, 0x77, 0x3a, 0x98, 0xef, 0x06, 0xcc, 0x3b, 0x32, 0x23, 0xf0, 0xc4, 0x34, 0xe4, 0x42,
	0x87, 0x41, 0x9c, 0x8f, 0xf5, 0x09, 0x7c, 0x30, 0x87, 0x3c, 0x85, 0x45, 0xdf, 0x40, 0xcf, 0x5c,
	0xbd, 0x3f, 0x1c, 0x07, 0x6f, 0x4b, 0x6a, 0x4d, 0x36, 0xec, 0xfb, 0xf0, 0xf0, 0x0a, 0xe6, 0x13,
	0x2b, 0x18, 0x52, 0xfc, 0xe9, 0xb8, 0xe2, 0xff, 0x2a, 0x07, 0x0f, 0x3a, 0xe9, 0xa6, 0x87, 0x28,
	0xe6, 0xc0, 0xf4, 0xae, 0xd2, 0xad, 0x3a, 0x53, 0xf3, 0x37, 0xa1, 0xec, 0x4f, 0x35, 0x14, 0xe2,
	0x0d, 0x4f, 0x3f, 0xc6, 0xd5, 0x4c, 0x9c, 0xab, 0xc7, 0xf0, 0xf0, 0x06, 0x4c, 0x25, 0xb6, 0x31,
	0x29, 0xc5, 0x89, 0xdb, 0xd8, 0x2f, 0xf3, 0x50, 0xdd, 0x36, 0x8c, 0x7d, 0x4a, 0x8d, 0x53, 0xbd,
	0xf7, 0x2e, 0x7d, 0x6a, 0xd9, 0x59, 0xae, 0x1c, 0xd7, 0x1d, 0x48, 0x55, 0xe5, 0xc5, 0xf1, 0x8b,
	0xe4, 0x4c, 0xe8, 0x45, 0x32, 0xa4, 0xef, 0xb3, 0x51, 0x7d, 0x17, 0x5a, 0x84, 0xe1, 0x8f, 0x39,
	0x49, 0x59, 0x06, 0x3f, 0x6e, 0x43, 0xc9, 0xa0, 0x17, 0x66, 0x8f, 0x06, 0x61, 0xa1, 0xa2, 0x00,
	0xb4, 0x44, 0xc8, 0xc2, 0x91, 0x21, 0xa1, 0xbc, 0x89, 0x8f, 0x3d, 0x36, 0x93, 0xb7, 0xfa, 0xbc,
	0x8d, 0xeb, 0xef, 0xbf, 0xaf, 0x89, 0x04, 0x22, 0xbf, 0x8a, 0x31, 0x06, 0x69, 0xc1, 0x78, 0x23,
	0x9c, 0x17, 0x8b, 0x21, 0x61, 0x18, 0xb4, 0x5e, 0x84, 0x85, 0x88, 0x44, 0x98, 0xa3, 0xbc, 0x85,
	0x69, 0x95, 0x32, 0xb2, 0x05, 0x33, 0x78, 0xde, 0xca, 0xe1, 0x79, 0xeb, 0x56, 0x3c, 0x66, 0x4d,
	0x19, 0x1e, 0xb2, 0x10, 0x87, 0xcb, 0x41, 0x37, 0x0c, 0xd7, 0xcf, 0xb4, 0xe3, 0x65, 0x2e, 0xad,
	0xa1, 0xf1, 0xd2, 0x97, 0xd6, 0xd0, 0x78, 0xa9, 0x7c, 0x8d, 0x47, 0xa8, 0xc4, 0x82, 0xe1, 0x76,
	0x5d, 0xf6, 0xf5, 0x3d, 0x94, 0x6f, 0xbb, 0x9e, 0xcc, 0x50, 0x08, 0x1b, 0x8a, 0xff, 0x7c, 0x8c,
	0xe9, 0xb6, 0x7f, 0x93, 0x83, 0x6a, 0xb4, 0x3d, 0x6c, 0xb4, 0xb9, 0xa8, 0xd1, 0xbe, 0x0c, 0x86,
	0x33, 0x28, 0xeb, 0xc9, 0x9d, 0x9d, 0x24, 0xa7, 0x38, 0x1e, 0x63, 0x8f, 0xb2, 0x1e, 0x79, 0x0d,
	0xb7, 0xfc, 0x6e, 0xba, 0x71, 0xa1, 0x5b, 0x3d, 0xaa, 0x59, 0xb6, 0x67, 0xf6, 0xb8, 0x69, 0x65,
	0x11, 0x58, 0x96, 0x3d, 0xb6, 0x45, 0x87, 0x36, 0xe2, 0x2b, 0xbf, 0xce, 0x63, 0x92, 0x7c, 0x8a,
	0xee, 0x7e, 0xf3, 0x3d, 0x27, 0x3b, 0x7a, 0xfc, 0x39, 0x94, 0x02, 0x2f, 0x32, 0x93, 0xc9, 0x6a,
	0x80, 0x94, 0x10, 0xd0, 0xec, 0xff, 0x54, 0x40, 0x73, 0x1f, 0x27, 0xa0, 0xec, 0x5d, 0x49, 0x66,
	0xe2, 0xa7, 0x28, 0x91, 0x72, 0x05, 0x05, 0xb5, 0x37, 0x34, 0xba, 0xfa, 0x29, 0x79, 0x02, 0xd3,
	0x8c, 0x5e, 0x66, 0xa8, 0xee, 0x09, 0xa3, 0x6e, 0x87, 0x5e, 0xaa, 0x1c, 0x85, 0xdc, 0x85, 0x79,
	0xb7, 0x37, 0x34, 0x34, 0xb9, 0x5d, 0xcb, 0x3d, 0xc3, 0x15, 0x74, 0x44, 0xec, 0x1a, 0x25, 0x20,
	0xb3, 0x09, 0x78, 0xd9, 0x3f, 0x1a, 0xcd, 0x04, 0x47, 0xa3, 0x6d, 0x58, 0x10, 0xb1, 0x80, 0xae,
	0x7e, 0xda, 0xa1, 0x78, 0x1e, 0x7e, 0x06, 0x05, 0xcc, 0x8f, 0xa0, 0xfe, 0xab, 0xcf, 0x4a, 0x8c,
	0x0d, 0x89, 0xca, 0x0f, 0x0a, 0x1d, 0xea, 0x29, 0x04, 0x6a, 0x51, 0x12, 0xcc, 0x51, 0x86, 0x30,
	0x27, 0x6a, 0x59, 0x2f, 0x6d, 0x1b, 0x98, 0x40, 0xc7, 0xe8, 0xa5, 0x76, 0xa1, 0x0f, 0x24, 0xfb,
	0x98, 0xb4, 0xda, 0xa1, 0x97, 0x6f, 0x38, 0x04, 0xad, 0x58, 0x3f, 0x65, 0x52, 0x43, 0x13, 0x56,
	0x2c, 0xe6, 0xa9, 0x22, 0x8e, 0xf2, 0x05, 0xdc, 0xf2, 0x03, 0x6c, 0xb2, 0x61, 0xe7, 0xaa, 0x65,
	0x8c, 0x3f, 0x1c, 0x39, 0x0d, 0x5d, 0x94, 0xe7, 0x70, 0x7c, 0xa6, 0xfc, 0x40, 0x3e, 0x0e, 0xc6,
	0xbb, 0x30, 0x87, 0x7c, 0x2e, 0x92, 0x35, 0x19, 0x1d, 0xbf, 0x3b, 0x66, 0x48, 0xa0, 0x20, 0x24,
	0xc0, 0x94, 0x26, 0x94, 0xc4, 0xdb, 0x99, 0xef, 0x98, 0x5d, 0x53, 0x5e, 0x1f, 0x79, 0x91, 0xdf,
	0x06, 0x5c, 0xfa, 0xbe, 0x65, 0x48, 0x2f, 0x23, 0x2a, 0x1c, 0xcf, 0xa5, 0xef, 0x7d, 0x37, 0xe3,
	0xd2, 0xf7, 0xca, 0x25, 0x80, 0x4f, 0x86, 0x39, 0x29, 0x74, 0xee, 0x40, 0x69, 0x48, 0x19, 0xd3,
	0xfb, 0x74, 0x4c, 0x2b, 0x00, 0xf0, 0xed, 0xdb, 0xa2, 0xd4, 0xd8, 0xa3, 0x3d, 0xf7, 0xca, 0xf1,
	0xe4, 0xa6, 0x19, 0x06, 0x71, 0xdd, 0x94, 0xe8, 0x7e, 0x12, 0xaa, 0xac, 0x2a, 0x7f, 0x95, 0x03,
	0xe8, 0x50, 0xd7, 0xa4, 0x0c, 0xf3, 0x55, 0x6e, 0x43, 0x89, 0x61, 0x2d, 0xb8, 0x07, 0x17, 0x05,
	0x40, 0x5e, 0x85, 0x45, 0x63, 0xe8, 0x8c, 0x0f, 0x02, 0x84, 0x27, 0xfc, 0x0d, 0x28, 0x7b, 0x9e,
	0x16, 0x10, 0x90, 0x81, 0x68, 0xcf, 0xeb, 0xf8, 0x24, 0xfe, 0x1f, 0x80, 0x78, 0x7e, 0x40, 0x9f,
	0x29, 0x0c, 0xbb, 0x1e, 0x8f, 0xe6, 0xe1, 0x7b, 0x84, 0x75, 0x66, 0xab, 0x25, 0xc4, 0x45, 0x67,
	0xf9, 0x8b, 0x3c, 0x94, 0xc6, 0x0d, 0x91, 0x57, 0x8c, 0x5c, 0xf4, 0x15, 0x63, 0xdd, 0x1f, 0x21,
	0xc4, 0xa3, 0xa0, 0x83, 0x2c, 0x66, 0x1c, 0x73, 0x31, 0xf7, 0xc8, 0xf6, 0xc2, 0x6c, 0x25, 0x73,
	0x8f, 0x6c, 0x91, 0x43, 0x55, 0xe4, 0x98, 0x18, 0x41, 0x6d, 0x40, 0xc9, 0xf3, 0x7c, 0x3b, 0x94,
	0x3b, 0xa7, 0xe7, 0x09, 0x2b, 0x7c, 0x06, 0x4b, 0x9e, 0xd9, 0x7b, 0x47, 0x3d, 0xcd, 0x76, 0x4d,
	0x7c, 0x35, 0x0a, 0x05, 0x9b, 0x16, 0x45, 0xd3, 0x11, 0xb6, 0x88, 0x88, 0xd3, 0x16, 0x48, 0xa0,
	0xe6, 0xe8, 0xbd, 0x77, 0x12, 0xbb, 0x80, 0xd8, 0x0b, 0xa2, 0xe1, 0x58, 0xef, 0xbd, 0x13, 0xa7,
	0xc2, 0xff, 0xcc, 0x41, 0xd1, 0x67, 0x87, 0xeb, 0x98, 0x69, 0x19, 0xd2, 0x75, 0x54, 0x54, 0x51,
	0x09, 0x3f, 0xe6, 0xe4, 0x23, 0x8f, 0x39, 0xb7, 0xe5, 0x4c, 0xc3, 0x47, 0x58, 0x0e, 0x40, 0xe9,
	0xac, 0x03, 0x38, 0xba, 0x4b, 0x2d, 0x2f, 0xf4, 0x15, 0x52, 0x49, 0x40, 0x5a, 0x06, 0x26, 0xbf,
	0x4a, 0x1e, 0x83, 0xc3, 0x42, 0x45, 0x9d, 0x17, 0x30, 0x31, 0x8d, 0xff, 0xcd, 0x69, 0x3f, 0x8d,
	0x9c, 0x9e, 0x5e, 0xdb, 0x9e, 0xaa, 0x8b, 0xef, 0x2d, 0x48, 0x68, 0xdf, 0xaf, 0x88, 0xfd, 0x5d,
	0xf9, 0x97, 0x7c, 0x90, 0x61, 0x26, 0x51, 0xd3, 0x76, 0xaa, 0x52, 0x64, 0xa7, 0xba, 0x0d, 0xa5,
	0x73, 0xdb, 0xd3, 0xc4, 0xdb, 0x87, 0x70, 0xac, 0xc5, 0x73, 0xdb, 0xeb, 0xf8, 0x59, 0x79, 0x18,
	0x8a, 0x16, 0x5f, 0x49, 0x84, 0x12, 0x01, 0xf0, 0x1b, 0x0a, 0x8c, 0x63, 0x5f, 0x7a, 0xfe, 0x03,
	0x33, 0x3e, 0x03, 0xd0, 0x4b, 0x0f, 0x9f, 0xf7, 0xee, 0x60, 0xc6, 0xbf, 0xcd, 0x46, 0x2e, 0x65,
	0xfe, 0xf7, 0x5b, 0x63, 0x40, 0x90, 0xa5, 0x3c, 0x17, 0xce, 0x52, 0xce, 0xca, 0xf2, 0xe6, 0x57,
	0xed, 0x48, 0xca, 0x69, 0x51, 0x5e, 0xb5, 0xc3, 0x79, 0xa6, 0xc9, 0xc4, 0xcf, 0x52, 0x4a, 0xe2,
	0x67, 0x3c, 0xd9, 0x1c, 0x52, 0x92, 0xcd, 0x57, 0xa1, 0xe0, 0xea, 0xe6, 0x40, 0x3b, 0xed, 0xcb,
	0xe4, 0xed, 0x39, 0x5e, 0xdd, 0xe9, 0x2b, 0x6e, 0xe4, 0x64, 0x34, 0x5e, 0x0c, 0xbc, 0xf8, 0x96,
	0x4c, 0xeb, 0xcc, 0x0e, 0x1f, 0x8b, 0xb2, 0x32, 0x56, 0xfd, 0x6e, 0x45, 0xde, 0x01, 0x4d, 0x8a,
	0x5f, 0x96, 0xcc, 0xa1, 0xc9, 0xe5, 0x78, 0xe5, 0xaf, 0x00, 0x02, 0xf6, 0xf4, 0xab, 0xad, 0x1e,
	0x2c, 0xc4, 0x3e, 0xbf, 0x22, 0xeb, 0xb0, 0x16, 0x03, 0x69, 0x27, 0xed, 0xbd, 0xe6, 0x7e, 0xab,
	0x8d, 0x5f, 0x0d, 0xd4, 0x61, 0x39, 0xde, 0xfc, 0xea, 0xe8, 0x68, 0xaf, 0x96, 0x23, 0xab, 0xb0,
	0x14, 0x6f, 0xd9, 0xd9, 0xde, 0xab, 0xe5, 0xb7, 0xde, 0xc3, 0x42, 0x2c, 0xdf, 0x91, 0xdc, 0x85,
	0xc6, 0xab, 0xed, 0xc3, 0xa6, 0x76, 0xa4, 0xb6, 0x9a, 0xed, 0xee, 0x76, 0xb7, 0x75, 0xd4, 0x8e,
	0x8c, 0x72, 0x0f, 0x6e, 0x27, 0xda, 0x5f, 0x1f, 0xa9, 0xad, 0xaf, 0x8f, 0xda, 0xdd, 0xed, 0x83,
	0x5a, 0x8e, 0x73, 0x99, 0x40, 0x78, 0xd3, 0x54, 0xbb, 0xad, 0xdd, 0xed, 0x83, 0x5a, 0x7e, 0xeb,
	0x0f, 0x23, 0xd9, 0x90, 0xe3, 0x89, 0xc5, 0x40, 0x91, 0x21, 0x53, 0x9a, 0x3b, 0x27, 0x3b, 0x9d,
	0x5d, 0xb5, 0xb5, 0xd3, 0x14, 0xb3, 0x8b, 0x37, 0xb7, 0x9b, 0x6f, 0x6b, 0xf9, 0xb4, 0x86, 0xd7,
	0x47, 0xdd, 0xda, 0xf4, 0xd6, 0x53, 0xee, 0x52, 0x64, 0x4e, 0x65, 0x11, 0x66, 0xda, 0xb6, 0x45,
	0x6b, 0x53, 0x04, 0x60, 0xae, 0x63, 0x5a, 0xfd, 0x01, 0xad, 0xe5, 0x48, 0x09, 0x66, 0x0f, 0x47,
	0x03, 0xcf, 0xac, 0xe5, 0xb7, 0x8e, 0x82, 0x0f, 0x78, 0xba, 0xa1, 0xec, 0xc8, 0xdb, 0x09, 0x60,
	0x84, 0xe7, 0xb5, 0xe0, 0x3b, 0x96, 0x00, 0x81, 0xb3, 0x95, 0xdb, 0xfa, 0x1c, 0x8a, 0xfe, 0x9b,
	0x2e, 0x59, 0x84, 0xca, 0xd1, 0xc1, 0x9e, 0x76, 0xd2, 0x69, 0xaa, 0x5a, 0xe7, 0x48, 0xed, 0xd6,
	0xa6, 0x38, 0xa8, 0xdd, 0x7c, 0x1b, 0x02, 0xe5, 0xb6, 0x0e, 0x61, 0x29, 0xe5, 0x61, 0x95, 0x54,
	0xa0, 0xd4, 0x3e, 0xea, 0x6a, 0xfb, 0x47, 0x27, 0x6d, 0x3e, 0xe4, 0x02, 0xcc, 0xf3, 0xaa, 0xda,
	0xec, 0x1c, 0x1f, 0xb5, 0xf9, 0xb2, 0x03, 0xcc, 0x6d, 0xef, 0xee, 0x36, 0x8f, 0xbb, 0xb5, 0x3c,
	0x2f, 0xab, 0xcd, 0xdf, 0x69, 0xee, 0xf2, 0xe9, 0xbf, 0x85, 0xc5, 0xc4, 0x33, 0x16, 0x59, 0x86,
	0xda, 0xf6, 0xc1, 0x81, 0xb6, 0x7b, 0x74, 0x78, 0xd8, 0x6c, 0x77, 0xb5, 0xee, 0x0f, 0x8f, 0x9b,
	0xb5, 0x29, 0x72, 0x0b, 0xc8, 0x49, 0x7b, 0xbf, 0xd5, 0x6e, 0x75, 0x5e, 0x37, 0xf7, 0xfc, 0x46,
	0xf1, 0x31, 0x4a, 0x02, 0x9a, 0xdf, 0x3a, 0x81, 0x4a, 0xe4, 0x79, 0x8a, 0xaf, 0x40, 0xeb, 0x55,
	0xfb, 0x48, 0x6d, 0x6a, 0x7b, 0xad, 0xce, 0xf1, 0xc1, 0xf6, 0x0f, 0x43, 0x74, 0xdb, 0x47, 0x63,
	0x60, 0x40, 0x77, 0x09, 0x16, 0xe2, 0xc0, 0xfc, 0xd6, 0x9f, 0xe5, 0x80, 0x24, 0x03, 0xc8, 0x64,
	0x03, 0xee, 0x24, 0xa1, 0x91, 0x45, 0xb8, 0x0b, 0x8d, 0x14, 0x0c, 0x2e, 0xdd, 0xee, 0xf6, 0x4e,
	0x2d, 0x97, 0x41, 0x21, 0xd0, 0xad, 0x3c, 0x69, 0xc0, 0xad, 0x74, 0x0a, 0xb5, 0xe9, 0xad, 0x3d,
	0x28, 0xc8, 0x6b, 0x16, 0x59, 0x81, 0x45, 0x59, 0x8c, 0x8c, 0xbf, 0x00, 0xf3, 0x3e, 0xb8, 0x75,
	0xf8, 0xaa, 0x96, 0xe3, 0x6b, 0xeb, 0x03, 0xde, 0xb4, 0xf6, 0x9a, 0x47, 0xb5, 0xfc, 0xd6, 0x73,
	0x28, 0xc8, 0x13, 0x2f, 0x21, 0x50, 0x95, 0x45, 0x6d, 0xbf, 0x79, 0xb8, 0x7d, 0xc0, 0x05, 0x55,
	0x13, 0x09, 0x24, 0x1c, 0x86, 0x90, 0xdc, 0x8b, 0x5f, 0xdf, 0x87, 0x72, 0x38, 0xf6, 0x42, 0x7a,
	0x40, 0x92, 0x79, 0x87, 0x24, 0x9e, 0x23, 0x9d, 0x9a, 0xe4, 0xd8, 0x78, 0x78, 0x03, 0x2c, 0xe6,
	0x28, 0x53, 0xe4, 0x6b, 0x58, 0x88, 0x65, 0x4f, 0x92, 0x78, 0x18, 0x2b, 0x99, 0x83, 0xd9, 0x50,
	0xae, 0x43, 0x41, 0xda, 0x3f, 0x86, 0x5a, 0x3c, 0x33, 0x96, 0xc4, 0x7b, 0xa6, 0xa4, 0x07, 0x37,
	0xee, 0x5f, 0x8b, 0x83, 0xe4, 0x2f, 0x61, 0x2d, 0x33, 0x87, 0x95, 0xc4, 0x9f, 0x84, 0x26, 0xe5,
	0xd5, 0x36, 0x3e, 0xbd, 0x39, 0xb2, 0x2f, 0xb4, 0x18, 0x4a, 0x42, 0x68, 0xc9, 0x34, 0xd9, 0x84,
	0xd0, 0x52, 0x72, 0x64, 0x95, 0x29, 0xf2, 0x1a, 0x4a, 0xe3, 0x0c, 0x51, 0x12, 0x4f, 0x13, 0x0b,
	0x67, 0x97, 0x36, 0xee, 0x64, 0x37, 0x22, 0xa5, 0x5f, 0xe4, 0x82, 0x97, 0xfb, 0x8c, 0xec, 0x4f,
	0x12, 0xff, 0xf4, 0xed, 0xfa, 0xa4, 0xd3, 0xc6, 0x8b, 0x8f, 0xed, 0x82, 0xcc, 0x9c, 0x8d, 0x3f,
	0xb2, 0x0f, 0x67, 0x7c, 0x92, 0x87, 0x49, 0x99, 0xa4, 0x64, 0x90, 0x36, 0x1e, 0xdd, 0x04, 0x0d,
	0xc7, 0xf9, 0x55, 0x2e, 0xc8, 0x9c, 0xcd, 0x4e, 0xd3, 0x24, 0x5f, 0x66, 0x4c, 0x62, 0x62, 0xbe,
	0x68, 0xe3, 0xe5, 0x37, 0xe8, 0x85, 0x5c, 0x99, 0xb0, 0x9c, 0x96, 0x60, 0x49, 0x1e, 0xa5, 0x11,
	0x4c, 0x66, 0x7c, 0x36, 0x1e, 0xdf, 0x08, 0x0f, 0x87, 0xfa, 0x29, 0x77, 0x59, 0xb1, 0x1c, 0x22,
	0x72, 0x3f, 0x11, 0x01, 0x48, 0xa6, 0x35, 0x35, 0x1e, 0x5c, 0x8f, 0xe4, 0x4f, 0x26, 0xed, 0xab,
	0xe7, 0xc4, 0x64, 0x32, 0xbe, 0xc3, 0x4e, 0x4c, 0x26, 0xf3, 0x13, 0x6a, 0x34, 0xf1, 0xcc, 0x4f,
	0xa2, 0x13, 0x26, 0x3e, 0xe9, 0x73, 0xeb, 0x84, 0x89, 0x4f, 0xfc, 0xd2, 0x5a, 0x4c, 0x32, 0xed,
	0x73, 0xe7, 0xc4, 0x24, 0x33, 0x3e, 0x9f, 0x4e, 0x4c, 0x32, 0xf3, 0xdb, 0xe9, 0x29, 0xd2, 0x86,
	0xf9, 0xd0, 0x77, 0xcc, 0x64, 0x3d, 0xa9, 0xeb, 0xa1, 0x2f, 0xab, 0x1b, 0x77, 0x27, 0x35, 0x23,
	0x3d, 0x7b, 0xfc, 0xd3, 0x84, 0x58, 0xae, 0x06, 0x89, 0xe7, 0x73, 0x64, 0x66, 0x24, 0x35, 0x3e,
	0xb9, 0x21, 0x26, 0x0e, 0xf8, 0xfb, 0xf8, 0x2b, 0x86, 0xac, 0x0c, 0x21, 0xf2, 0x59, 0x86, 0xe8,
	0x33, 0x86, 0x7e, 0xf6, 0x31, 0xe8, 0xbe, 0x6f, 0x49, 0xc9, 0x18, 0x48, 0xf8, 0x96, 0xf4, 0xec,
	0xa2, 0x84, 0x6f, 0xc9, 0xc8, 0x8e, 0x51, 0xa6, 0xc8, 0xc0, 0xff, 0x4b, 0x49, 0x7c, 0xa4, 0xc7,
	0x89, 0xdd, 0x36, 0x3d, 0xd7, 0xa5, 0xf1, 0xe4, 0x66, 0x88, 0x38, 0xda, 0xef, 0x41, 0x23, 0x3b,
	0x0f, 0x82, 0x7c, 0x7a, 0x3d, 0xd7, 0xc1, 0x0b, 0x4b, 0xe3, 0xb3, 0x8f, 0xc0, 0xc6, 0xc1, 0x47,
	0x91, 0x3f, 0x1b, 0x44, 0x92, 0x16, 0xc8, 0x56, 0xb6, 0xfd, 0xc6, 0x93, 0x28, 0x1a, 0x4f, 0x6f,
	0x8c, 0xeb, 0x4b, 0x38, 0x35, 0xdf, 0x21, 0x21, 0xe1, 0xac, 0x8c, 0x89, 0x84, 0x84, 0xb3, 0xd3,
	0x27, 0xa6, 0xc8, 0x5b, 0x7c, 0x96, 0x08, 0xff, 0xe9, 0x62, 0x23, 0xa9, 0xf6, 0xd1, 0xff, 0x8a,
	0x34, 0x36, 0xaf, 0xc1, 0xf0, 0x7d, 0x70, 0xe2, 0x6f, 0x39, 0x09, 0x1f, 0x9c, 0xf6, 0xfb, 0x9d,
	0x84, 0x0f, 0x4e, 0xff, 0xe9, 0x0e, 0x8e, 0x90, 0xf8, 0x61, 0x4e, 0x62, 0x84, 0xb4, 0x1f, 0xf3,
	0x34, 0x1e, 0x5c, 0x8f, 0xe4, 0x1b, 0x55, 0xca, 0x6f, 0x74, 0x12, 0x46, 0x95, 0xfe, 0x77, 0x9e,
	0x84, 0x51, 0x65, 0xfd, 0x91, 0xc7, 0x3f, 0x4b, 0x45, 0xc6, 0xd8, 0x4c, 0xdf, 0xed, 0xc3, 0xf4,
	0x95, 0xeb, 0x50, 0x62, 0x87, 0x8e, 0xf0, 0xff, 0x75, 0xb2, 0x0e, 0x1d, 0xb1, 0xdf, 0xf6, 0x64,
	0x1d, 0x3a, 0xe2, 0xbf, 0xea, 0x51, 0xa6, 0xf8, 0x49, 0x3d, 0xf9, 0x9b, 0x9a, 0xc4, 0x49, 0x3d,
	0xf5, 0xf7, 0x3c, 0x89, 0x93, 0x7a, 0xc6, 0xff, 0x6e, 0xd0, 0x24, 0xb3, 0xfe, 0x9c, 0x92, 0x30,
	0xc9, 0x09, 0x7f, 0xc6, 0x69, 0x3c, 0xbd, 0x31, 0xae, 0x3f, 0xb7, 0x64, 0x02, 0x4e, 0x62, 0x6e,
	0xa9, 0xe9, 0x3c, 0x8d, 0x87, 0x37, 0xc0, 0xf2, 0xb7, 0xac, 0xf4, 0xc4, 0x99, 0xc4, 0x96, 0x95,
	0x99, 0xbf, 0x93, 0xd8, 0xb2, 0x26, 0x64, 0xe2, 0xf8, 0x2b, 0x16, 0x7b, 0xce, 0x48, 0x5b, 0xb1,
	0xe4, 0x5b, 0x51, 0xda, 0x8a, 0xa5, 0xbd, 0x8b, 0xe0, 0xbe, 0x38, 0x21, 0x61, 0x26, 0xb1, 0x2f,
	0x4e, 0xce, 0xf4, 0x49, 0xec, 0x8b, 0xd7, 0xe4, 0xe2, 0x28, 0x53, 0xe4, 0xe7, 0xe2, 0xc1, 0x3b,
	0x3b, 0xc7, 0x83, 0x3c, 0xbf, 0x19, 0xcd, 0x71, 0xa2, 0x49, 0xe3, 0xf3, 0x8f, 0xeb, 0x90, 0x72,
	0x4f, 0x8b, 0xbd, 0x16, 0x4e, 0xb8, 0xa7, 0x25, 0x12, 0x05, 0x26, 0xdd, 0xd3, 0x92, 0xaf, 0xf9,
	0xca, 0x14, 0xf9, 0x65, 0x0e, 0x36, 0xaf, 0x7d, 0xd4, 0x26, 0xdf, 0x49, 0xee, 0x51, 0xd7, 0xbe,
	0xcd, 0x37, 0xbe, 0xfc, 0xf8, 0x4e, 0x21, 0x73, 0xba, 0x4e, 0xf1, 0x52, 0x1f, 0xd8, 0xd3, 0xcc,
	0x29, 0x5d, 0xf1, 0xda, 0x30, 0x1f, 0x7a, 0x5d, 0x4e, 0x9c, 0x28, 0xa3, 0x6f, 0xf1, 0x89, 0x13,
	0x65, 0xfc, 0x61, 0x7a, 0x8a, 0xfc, 0x2e, 0x94, 0xc3, 0x8f, 0x64, 0xe4, 0x6e, 0xea, 0x1e, 0x32,
	0x7e, 0x84, 0x6b, 0xdc, 0x9b, 0xd8, 0xee, 0xbb, 0xe6, 0x94, 0x17, 0xac, 0x84, 0x6b, 0x4e, 0x7f,
	0x18, 0x6b, 0x3c, 0xba, 0x09, 0x5a, 0x8a, 0xbc, 0xfd, 0x20, 0xfa, 0x04, 0x79, 0x07, 0x21, 0xf9,
	0x49, 0xf2, 0x0e, 0xc5, 0x8a, 0x95, 0xa9, 0x9d, 0x2f, 0xbe, 0x7e, 0xde, 0xb7, 0x07, 0xba, 0xd5,
	0x7f, 0xf6, 0xf2, 0x85, 0xe7, 0x3d, 0xeb, 0xd9, 0xc3, 0xe7, 0xf8, 0x73, 0xc2, 0x9e, 0x3d, 0x78,
	0xce, 0xa8, 0x7b, 0x61, 0xf6, 0x28, 0x8b, 0xfe, 0xbc, 0xf0, 0x74, 0x0e, 0x11, 0xbe, 0xf3, 0xdf,
	0x01, 0x00, 0x00, 0xff, 0xff, 0xc8, 0xc8, 0x9a, 0xe9, 0xe2, 0x50, 0x00, 0x00,
}
