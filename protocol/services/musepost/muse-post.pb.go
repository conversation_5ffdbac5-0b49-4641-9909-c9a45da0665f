// Code generated by protoc-gen-go. DO NOT EDIT.
// source: muse-post/muse-post.proto

package musepost // import "golang.52tt.com/protocol/services/musepost"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type MuseContentType int32

const (
	MuseContentType_MuseContentType_UNDEFINED MuseContentType = 0
	MuseContentType_MuseContentType_PLAINTEXT MuseContentType = 1
)

var MuseContentType_name = map[int32]string{
	0: "MuseContentType_UNDEFINED",
	1: "MuseContentType_PLAINTEXT",
}
var MuseContentType_value = map[string]int32{
	"MuseContentType_UNDEFINED": 0,
	"MuseContentType_PLAINTEXT": 1,
}

func (x MuseContentType) String() string {
	return proto.EnumName(MuseContentType_name, int32(x))
}
func (MuseContentType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{0}
}

type ContentStatus int32

const (
	ContentStatus_ContentStatus_UNDEFINED ContentStatus = 0
	ContentStatus_ContentStatus_EXAMINING ContentStatus = 1
	ContentStatus_ContentStatus_ILLEGAL   ContentStatus = 2
	ContentStatus_ContentStatus_NORMAL    ContentStatus = 3
	ContentStatus_ContentStatus_DELETED   ContentStatus = 4
)

var ContentStatus_name = map[int32]string{
	0: "ContentStatus_UNDEFINED",
	1: "ContentStatus_EXAMINING",
	2: "ContentStatus_ILLEGAL",
	3: "ContentStatus_NORMAL",
	4: "ContentStatus_DELETED",
}
var ContentStatus_value = map[string]int32{
	"ContentStatus_UNDEFINED": 0,
	"ContentStatus_EXAMINING": 1,
	"ContentStatus_ILLEGAL":   2,
	"ContentStatus_NORMAL":    3,
	"ContentStatus_DELETED":   4,
}

func (x ContentStatus) String() string {
	return proto.EnumName(ContentStatus_name, int32(x))
}
func (ContentStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{1}
}

type ConflictPolicy int32

const (
	ConflictPolicy_UPDATE_SCORE_AND_FILTER ConflictPolicy = 0
	ConflictPolicy_IGNORE                  ConflictPolicy = 1
	ConflictPolicy_UPDATE_SCORE            ConflictPolicy = 2
	ConflictPolicy_UPDATE_FILTER           ConflictPolicy = 4
)

var ConflictPolicy_name = map[int32]string{
	0: "UPDATE_SCORE_AND_FILTER",
	1: "IGNORE",
	2: "UPDATE_SCORE",
	4: "UPDATE_FILTER",
}
var ConflictPolicy_value = map[string]int32{
	"UPDATE_SCORE_AND_FILTER": 0,
	"IGNORE":                  1,
	"UPDATE_SCORE":            2,
	"UPDATE_FILTER":           4,
}

func (x ConflictPolicy) String() string {
	return proto.EnumName(ConflictPolicy_name, int32(x))
}
func (ConflictPolicy) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{2}
}

type MuseInteractiveMsgType int32

const (
	MuseInteractiveMsgType_MuseInteractiveMsgType_UNDEFINED MuseInteractiveMsgType = 0
	MuseInteractiveMsgType_MuseInteractiveMsgType_COMMENT   MuseInteractiveMsgType = 1
	MuseInteractiveMsgType_MuseInteractiveMsgType_LIKE      MuseInteractiveMsgType = 2
)

var MuseInteractiveMsgType_name = map[int32]string{
	0: "MuseInteractiveMsgType_UNDEFINED",
	1: "MuseInteractiveMsgType_COMMENT",
	2: "MuseInteractiveMsgType_LIKE",
}
var MuseInteractiveMsgType_value = map[string]int32{
	"MuseInteractiveMsgType_UNDEFINED": 0,
	"MuseInteractiveMsgType_COMMENT":   1,
	"MuseInteractiveMsgType_LIKE":      2,
}

func (x MuseInteractiveMsgType) String() string {
	return proto.EnumName(MuseInteractiveMsgType_name, int32(x))
}
func (MuseInteractiveMsgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{3}
}

type QueryOptions_Sort int32

const (
	QueryOptions_ASC  QueryOptions_Sort = 0
	QueryOptions_DESC QueryOptions_Sort = 1
)

var QueryOptions_Sort_name = map[int32]string{
	0: "ASC",
	1: "DESC",
}
var QueryOptions_Sort_value = map[string]int32{
	"ASC":  0,
	"DESC": 1,
}

func (x QueryOptions_Sort) String() string {
	return proto.EnumName(QueryOptions_Sort_name, int32(x))
}
func (QueryOptions_Sort) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{50, 0}
}

type GetInteractiveMsgHistoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetInteractiveMsgHistoryReq) Reset()         { *m = GetInteractiveMsgHistoryReq{} }
func (m *GetInteractiveMsgHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetInteractiveMsgHistoryReq) ProtoMessage()    {}
func (*GetInteractiveMsgHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{0}
}
func (m *GetInteractiveMsgHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInteractiveMsgHistoryReq.Unmarshal(m, b)
}
func (m *GetInteractiveMsgHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInteractiveMsgHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetInteractiveMsgHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInteractiveMsgHistoryReq.Merge(dst, src)
}
func (m *GetInteractiveMsgHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetInteractiveMsgHistoryReq.Size(m)
}
func (m *GetInteractiveMsgHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInteractiveMsgHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetInteractiveMsgHistoryReq proto.InternalMessageInfo

func (m *GetInteractiveMsgHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetInteractiveMsgHistoryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetInteractiveMsgHistoryResp struct {
	LastReadPostId       string   `protobuf:"bytes,1,opt,name=last_read_post_id,json=lastReadPostId,proto3" json:"last_read_post_id,omitempty"`
	UnreadLikeCount      uint32   `protobuf:"varint,2,opt,name=unread_like_count,json=unreadLikeCount,proto3" json:"unread_like_count,omitempty"`
	UnreadCommentCount   uint32   `protobuf:"varint,3,opt,name=unread_comment_count,json=unreadCommentCount,proto3" json:"unread_comment_count,omitempty"`
	UnreadAtCount        uint32   `protobuf:"varint,4,opt,name=unread_at_count,json=unreadAtCount,proto3" json:"unread_at_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetInteractiveMsgHistoryResp) Reset()         { *m = GetInteractiveMsgHistoryResp{} }
func (m *GetInteractiveMsgHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetInteractiveMsgHistoryResp) ProtoMessage()    {}
func (*GetInteractiveMsgHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{1}
}
func (m *GetInteractiveMsgHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInteractiveMsgHistoryResp.Unmarshal(m, b)
}
func (m *GetInteractiveMsgHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInteractiveMsgHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetInteractiveMsgHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInteractiveMsgHistoryResp.Merge(dst, src)
}
func (m *GetInteractiveMsgHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetInteractiveMsgHistoryResp.Size(m)
}
func (m *GetInteractiveMsgHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInteractiveMsgHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetInteractiveMsgHistoryResp proto.InternalMessageInfo

func (m *GetInteractiveMsgHistoryResp) GetLastReadPostId() string {
	if m != nil {
		return m.LastReadPostId
	}
	return ""
}

func (m *GetInteractiveMsgHistoryResp) GetUnreadLikeCount() uint32 {
	if m != nil {
		return m.UnreadLikeCount
	}
	return 0
}

func (m *GetInteractiveMsgHistoryResp) GetUnreadCommentCount() uint32 {
	if m != nil {
		return m.UnreadCommentCount
	}
	return 0
}

func (m *GetInteractiveMsgHistoryResp) GetUnreadAtCount() uint32 {
	if m != nil {
		return m.UnreadAtCount
	}
	return 0
}

type DeleteContentReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string   `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteContentReq) Reset()         { *m = DeleteContentReq{} }
func (m *DeleteContentReq) String() string { return proto.CompactTextString(m) }
func (*DeleteContentReq) ProtoMessage()    {}
func (*DeleteContentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{2}
}
func (m *DeleteContentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteContentReq.Unmarshal(m, b)
}
func (m *DeleteContentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteContentReq.Marshal(b, m, deterministic)
}
func (dst *DeleteContentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteContentReq.Merge(dst, src)
}
func (m *DeleteContentReq) XXX_Size() int {
	return xxx_messageInfo_DeleteContentReq.Size(m)
}
func (m *DeleteContentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteContentReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteContentReq proto.InternalMessageInfo

func (m *DeleteContentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DeleteContentReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *DeleteContentReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

type DeleteContentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteContentResp) Reset()         { *m = DeleteContentResp{} }
func (m *DeleteContentResp) String() string { return proto.CompactTextString(m) }
func (*DeleteContentResp) ProtoMessage()    {}
func (*DeleteContentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{3}
}
func (m *DeleteContentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteContentResp.Unmarshal(m, b)
}
func (m *DeleteContentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteContentResp.Marshal(b, m, deterministic)
}
func (dst *DeleteContentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteContentResp.Merge(dst, src)
}
func (m *DeleteContentResp) XXX_Size() int {
	return xxx_messageInfo_DeleteContentResp.Size(m)
}
func (m *DeleteContentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteContentResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteContentResp proto.InternalMessageInfo

type UpdatePostStatusReq struct {
	PostId               string        `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Status               ContentStatus `protobuf:"varint,2,opt,name=status,proto3,enum=musepost.ContentStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdatePostStatusReq) Reset()         { *m = UpdatePostStatusReq{} }
func (m *UpdatePostStatusReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePostStatusReq) ProtoMessage()    {}
func (*UpdatePostStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{4}
}
func (m *UpdatePostStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePostStatusReq.Unmarshal(m, b)
}
func (m *UpdatePostStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePostStatusReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePostStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePostStatusReq.Merge(dst, src)
}
func (m *UpdatePostStatusReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePostStatusReq.Size(m)
}
func (m *UpdatePostStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePostStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePostStatusReq proto.InternalMessageInfo

func (m *UpdatePostStatusReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpdatePostStatusReq) GetStatus() ContentStatus {
	if m != nil {
		return m.Status
	}
	return ContentStatus_ContentStatus_UNDEFINED
}

type UpdatePostStatusResp struct {
	Post                 *MusePost `protobuf:"bytes,1,opt,name=post,proto3" json:"post,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *UpdatePostStatusResp) Reset()         { *m = UpdatePostStatusResp{} }
func (m *UpdatePostStatusResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePostStatusResp) ProtoMessage()    {}
func (*UpdatePostStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{5}
}
func (m *UpdatePostStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePostStatusResp.Unmarshal(m, b)
}
func (m *UpdatePostStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePostStatusResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePostStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePostStatusResp.Merge(dst, src)
}
func (m *UpdatePostStatusResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePostStatusResp.Size(m)
}
func (m *UpdatePostStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePostStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePostStatusResp proto.InternalMessageInfo

func (m *UpdatePostStatusResp) GetPost() *MusePost {
	if m != nil {
		return m.Post
	}
	return nil
}

type UpdateCommentStatusReq struct {
	CommentId            string        `protobuf:"bytes,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	Status               ContentStatus `protobuf:"varint,2,opt,name=status,proto3,enum=musepost.ContentStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdateCommentStatusReq) Reset()         { *m = UpdateCommentStatusReq{} }
func (m *UpdateCommentStatusReq) String() string { return proto.CompactTextString(m) }
func (*UpdateCommentStatusReq) ProtoMessage()    {}
func (*UpdateCommentStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{6}
}
func (m *UpdateCommentStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCommentStatusReq.Unmarshal(m, b)
}
func (m *UpdateCommentStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCommentStatusReq.Marshal(b, m, deterministic)
}
func (dst *UpdateCommentStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCommentStatusReq.Merge(dst, src)
}
func (m *UpdateCommentStatusReq) XXX_Size() int {
	return xxx_messageInfo_UpdateCommentStatusReq.Size(m)
}
func (m *UpdateCommentStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCommentStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCommentStatusReq proto.InternalMessageInfo

func (m *UpdateCommentStatusReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *UpdateCommentStatusReq) GetStatus() ContentStatus {
	if m != nil {
		return m.Status
	}
	return ContentStatus_ContentStatus_UNDEFINED
}

type UpdateCommentStatusResp struct {
	Comment              *MuseComment `protobuf:"bytes,1,opt,name=comment,proto3" json:"comment,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdateCommentStatusResp) Reset()         { *m = UpdateCommentStatusResp{} }
func (m *UpdateCommentStatusResp) String() string { return proto.CompactTextString(m) }
func (*UpdateCommentStatusResp) ProtoMessage()    {}
func (*UpdateCommentStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{7}
}
func (m *UpdateCommentStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCommentStatusResp.Unmarshal(m, b)
}
func (m *UpdateCommentStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCommentStatusResp.Marshal(b, m, deterministic)
}
func (dst *UpdateCommentStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCommentStatusResp.Merge(dst, src)
}
func (m *UpdateCommentStatusResp) XXX_Size() int {
	return xxx_messageInfo_UpdateCommentStatusResp.Size(m)
}
func (m *UpdateCommentStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCommentStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCommentStatusResp proto.InternalMessageInfo

func (m *UpdateCommentStatusResp) GetComment() *MuseComment {
	if m != nil {
		return m.Comment
	}
	return nil
}

type AddLikeContentHistoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string   `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddLikeContentHistoryReq) Reset()         { *m = AddLikeContentHistoryReq{} }
func (m *AddLikeContentHistoryReq) String() string { return proto.CompactTextString(m) }
func (*AddLikeContentHistoryReq) ProtoMessage()    {}
func (*AddLikeContentHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{8}
}
func (m *AddLikeContentHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddLikeContentHistoryReq.Unmarshal(m, b)
}
func (m *AddLikeContentHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddLikeContentHistoryReq.Marshal(b, m, deterministic)
}
func (dst *AddLikeContentHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddLikeContentHistoryReq.Merge(dst, src)
}
func (m *AddLikeContentHistoryReq) XXX_Size() int {
	return xxx_messageInfo_AddLikeContentHistoryReq.Size(m)
}
func (m *AddLikeContentHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddLikeContentHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddLikeContentHistoryReq proto.InternalMessageInfo

func (m *AddLikeContentHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddLikeContentHistoryReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *AddLikeContentHistoryReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

type AddLikeContentHistoryResp struct {
	Succ                 bool     `protobuf:"varint,1,opt,name=succ,proto3" json:"succ,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddLikeContentHistoryResp) Reset()         { *m = AddLikeContentHistoryResp{} }
func (m *AddLikeContentHistoryResp) String() string { return proto.CompactTextString(m) }
func (*AddLikeContentHistoryResp) ProtoMessage()    {}
func (*AddLikeContentHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{9}
}
func (m *AddLikeContentHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddLikeContentHistoryResp.Unmarshal(m, b)
}
func (m *AddLikeContentHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddLikeContentHistoryResp.Marshal(b, m, deterministic)
}
func (dst *AddLikeContentHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddLikeContentHistoryResp.Merge(dst, src)
}
func (m *AddLikeContentHistoryResp) XXX_Size() int {
	return xxx_messageInfo_AddLikeContentHistoryResp.Size(m)
}
func (m *AddLikeContentHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddLikeContentHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddLikeContentHistoryResp proto.InternalMessageInfo

func (m *AddLikeContentHistoryResp) GetSucc() bool {
	if m != nil {
		return m.Succ
	}
	return false
}

type DeleteLikeContentHistoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string   `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteLikeContentHistoryReq) Reset()         { *m = DeleteLikeContentHistoryReq{} }
func (m *DeleteLikeContentHistoryReq) String() string { return proto.CompactTextString(m) }
func (*DeleteLikeContentHistoryReq) ProtoMessage()    {}
func (*DeleteLikeContentHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{10}
}
func (m *DeleteLikeContentHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteLikeContentHistoryReq.Unmarshal(m, b)
}
func (m *DeleteLikeContentHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteLikeContentHistoryReq.Marshal(b, m, deterministic)
}
func (dst *DeleteLikeContentHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteLikeContentHistoryReq.Merge(dst, src)
}
func (m *DeleteLikeContentHistoryReq) XXX_Size() int {
	return xxx_messageInfo_DeleteLikeContentHistoryReq.Size(m)
}
func (m *DeleteLikeContentHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteLikeContentHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteLikeContentHistoryReq proto.InternalMessageInfo

func (m *DeleteLikeContentHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DeleteLikeContentHistoryReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *DeleteLikeContentHistoryReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

type DeleteLikeContentHistoryResp struct {
	Succ                 bool     `protobuf:"varint,1,opt,name=succ,proto3" json:"succ,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteLikeContentHistoryResp) Reset()         { *m = DeleteLikeContentHistoryResp{} }
func (m *DeleteLikeContentHistoryResp) String() string { return proto.CompactTextString(m) }
func (*DeleteLikeContentHistoryResp) ProtoMessage()    {}
func (*DeleteLikeContentHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{11}
}
func (m *DeleteLikeContentHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteLikeContentHistoryResp.Unmarshal(m, b)
}
func (m *DeleteLikeContentHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteLikeContentHistoryResp.Marshal(b, m, deterministic)
}
func (dst *DeleteLikeContentHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteLikeContentHistoryResp.Merge(dst, src)
}
func (m *DeleteLikeContentHistoryResp) XXX_Size() int {
	return xxx_messageInfo_DeleteLikeContentHistoryResp.Size(m)
}
func (m *DeleteLikeContentHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteLikeContentHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteLikeContentHistoryResp proto.InternalMessageInfo

func (m *DeleteLikeContentHistoryResp) GetSucc() bool {
	if m != nil {
		return m.Succ
	}
	return false
}

type GetLikeContentHistoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PostIds              []string `protobuf:"bytes,2,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	CommentIds           []string `protobuf:"bytes,3,rep,name=comment_ids,json=commentIds,proto3" json:"comment_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLikeContentHistoryReq) Reset()         { *m = GetLikeContentHistoryReq{} }
func (m *GetLikeContentHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetLikeContentHistoryReq) ProtoMessage()    {}
func (*GetLikeContentHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{12}
}
func (m *GetLikeContentHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLikeContentHistoryReq.Unmarshal(m, b)
}
func (m *GetLikeContentHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLikeContentHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetLikeContentHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLikeContentHistoryReq.Merge(dst, src)
}
func (m *GetLikeContentHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetLikeContentHistoryReq.Size(m)
}
func (m *GetLikeContentHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLikeContentHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLikeContentHistoryReq proto.InternalMessageInfo

func (m *GetLikeContentHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLikeContentHistoryReq) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

func (m *GetLikeContentHistoryReq) GetCommentIds() []string {
	if m != nil {
		return m.CommentIds
	}
	return nil
}

type GetLikeContentHistoryResp struct {
	History              map[string]bool `protobuf:"bytes,1,rep,name=history,proto3" json:"history,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetLikeContentHistoryResp) Reset()         { *m = GetLikeContentHistoryResp{} }
func (m *GetLikeContentHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetLikeContentHistoryResp) ProtoMessage()    {}
func (*GetLikeContentHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{13}
}
func (m *GetLikeContentHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLikeContentHistoryResp.Unmarshal(m, b)
}
func (m *GetLikeContentHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLikeContentHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetLikeContentHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLikeContentHistoryResp.Merge(dst, src)
}
func (m *GetLikeContentHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetLikeContentHistoryResp.Size(m)
}
func (m *GetLikeContentHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLikeContentHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLikeContentHistoryResp proto.InternalMessageInfo

func (m *GetLikeContentHistoryResp) GetHistory() map[string]bool {
	if m != nil {
		return m.History
	}
	return nil
}

type MarkInteractiveMsgReadReq struct {
	ChannelId            uint32                 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32                 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	MsgId                string                 `protobuf:"bytes,3,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	MsgType              MuseInteractiveMsgType `protobuf:"varint,4,opt,name=msg_type,json=msgType,proto3,enum=musepost.MuseInteractiveMsgType" json:"msg_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *MarkInteractiveMsgReadReq) Reset()         { *m = MarkInteractiveMsgReadReq{} }
func (m *MarkInteractiveMsgReadReq) String() string { return proto.CompactTextString(m) }
func (*MarkInteractiveMsgReadReq) ProtoMessage()    {}
func (*MarkInteractiveMsgReadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{14}
}
func (m *MarkInteractiveMsgReadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkInteractiveMsgReadReq.Unmarshal(m, b)
}
func (m *MarkInteractiveMsgReadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkInteractiveMsgReadReq.Marshal(b, m, deterministic)
}
func (dst *MarkInteractiveMsgReadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkInteractiveMsgReadReq.Merge(dst, src)
}
func (m *MarkInteractiveMsgReadReq) XXX_Size() int {
	return xxx_messageInfo_MarkInteractiveMsgReadReq.Size(m)
}
func (m *MarkInteractiveMsgReadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkInteractiveMsgReadReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkInteractiveMsgReadReq proto.InternalMessageInfo

func (m *MarkInteractiveMsgReadReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MarkInteractiveMsgReadReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MarkInteractiveMsgReadReq) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *MarkInteractiveMsgReadReq) GetMsgType() MuseInteractiveMsgType {
	if m != nil {
		return m.MsgType
	}
	return MuseInteractiveMsgType_MuseInteractiveMsgType_UNDEFINED
}

type MarkInteractiveMsgReadResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkInteractiveMsgReadResp) Reset()         { *m = MarkInteractiveMsgReadResp{} }
func (m *MarkInteractiveMsgReadResp) String() string { return proto.CompactTextString(m) }
func (*MarkInteractiveMsgReadResp) ProtoMessage()    {}
func (*MarkInteractiveMsgReadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{15}
}
func (m *MarkInteractiveMsgReadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkInteractiveMsgReadResp.Unmarshal(m, b)
}
func (m *MarkInteractiveMsgReadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkInteractiveMsgReadResp.Marshal(b, m, deterministic)
}
func (dst *MarkInteractiveMsgReadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkInteractiveMsgReadResp.Merge(dst, src)
}
func (m *MarkInteractiveMsgReadResp) XXX_Size() int {
	return xxx_messageInfo_MarkInteractiveMsgReadResp.Size(m)
}
func (m *MarkInteractiveMsgReadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkInteractiveMsgReadResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkInteractiveMsgReadResp proto.InternalMessageInfo

type MarkPostReadReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	PostId               string   `protobuf:"bytes,3,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkPostReadReq) Reset()         { *m = MarkPostReadReq{} }
func (m *MarkPostReadReq) String() string { return proto.CompactTextString(m) }
func (*MarkPostReadReq) ProtoMessage()    {}
func (*MarkPostReadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{16}
}
func (m *MarkPostReadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkPostReadReq.Unmarshal(m, b)
}
func (m *MarkPostReadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkPostReadReq.Marshal(b, m, deterministic)
}
func (dst *MarkPostReadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkPostReadReq.Merge(dst, src)
}
func (m *MarkPostReadReq) XXX_Size() int {
	return xxx_messageInfo_MarkPostReadReq.Size(m)
}
func (m *MarkPostReadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkPostReadReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkPostReadReq proto.InternalMessageInfo

func (m *MarkPostReadReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MarkPostReadReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MarkPostReadReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type MarkPostReadResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkPostReadResp) Reset()         { *m = MarkPostReadResp{} }
func (m *MarkPostReadResp) String() string { return proto.CompactTextString(m) }
func (*MarkPostReadResp) ProtoMessage()    {}
func (*MarkPostReadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{17}
}
func (m *MarkPostReadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkPostReadResp.Unmarshal(m, b)
}
func (m *MarkPostReadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkPostReadResp.Marshal(b, m, deterministic)
}
func (dst *MarkPostReadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkPostReadResp.Merge(dst, src)
}
func (m *MarkPostReadResp) XXX_Size() int {
	return xxx_messageInfo_MarkPostReadResp.Size(m)
}
func (m *MarkPostReadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkPostReadResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkPostReadResp proto.InternalMessageInfo

type IncrInteractiveMsgUnreadCountReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	UnreadLikeCount      uint32   `protobuf:"varint,3,opt,name=unread_like_count,json=unreadLikeCount,proto3" json:"unread_like_count,omitempty"`
	UnreadCommentCount   uint32   `protobuf:"varint,4,opt,name=unread_comment_count,json=unreadCommentCount,proto3" json:"unread_comment_count,omitempty"`
	UnreadAtCount        uint32   `protobuf:"varint,5,opt,name=unread_at_count,json=unreadAtCount,proto3" json:"unread_at_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IncrInteractiveMsgUnreadCountReq) Reset()         { *m = IncrInteractiveMsgUnreadCountReq{} }
func (m *IncrInteractiveMsgUnreadCountReq) String() string { return proto.CompactTextString(m) }
func (*IncrInteractiveMsgUnreadCountReq) ProtoMessage()    {}
func (*IncrInteractiveMsgUnreadCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{18}
}
func (m *IncrInteractiveMsgUnreadCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IncrInteractiveMsgUnreadCountReq.Unmarshal(m, b)
}
func (m *IncrInteractiveMsgUnreadCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IncrInteractiveMsgUnreadCountReq.Marshal(b, m, deterministic)
}
func (dst *IncrInteractiveMsgUnreadCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IncrInteractiveMsgUnreadCountReq.Merge(dst, src)
}
func (m *IncrInteractiveMsgUnreadCountReq) XXX_Size() int {
	return xxx_messageInfo_IncrInteractiveMsgUnreadCountReq.Size(m)
}
func (m *IncrInteractiveMsgUnreadCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IncrInteractiveMsgUnreadCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_IncrInteractiveMsgUnreadCountReq proto.InternalMessageInfo

func (m *IncrInteractiveMsgUnreadCountReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *IncrInteractiveMsgUnreadCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *IncrInteractiveMsgUnreadCountReq) GetUnreadLikeCount() uint32 {
	if m != nil {
		return m.UnreadLikeCount
	}
	return 0
}

func (m *IncrInteractiveMsgUnreadCountReq) GetUnreadCommentCount() uint32 {
	if m != nil {
		return m.UnreadCommentCount
	}
	return 0
}

func (m *IncrInteractiveMsgUnreadCountReq) GetUnreadAtCount() uint32 {
	if m != nil {
		return m.UnreadAtCount
	}
	return 0
}

type IncrInteractiveMsgUnreadCountResp struct {
	UnreadLikeCount      uint32   `protobuf:"varint,1,opt,name=unread_like_count,json=unreadLikeCount,proto3" json:"unread_like_count,omitempty"`
	UnreadCommentCount   uint32   `protobuf:"varint,2,opt,name=unread_comment_count,json=unreadCommentCount,proto3" json:"unread_comment_count,omitempty"`
	UnreadAtCount        uint32   `protobuf:"varint,3,opt,name=unread_at_count,json=unreadAtCount,proto3" json:"unread_at_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IncrInteractiveMsgUnreadCountResp) Reset()         { *m = IncrInteractiveMsgUnreadCountResp{} }
func (m *IncrInteractiveMsgUnreadCountResp) String() string { return proto.CompactTextString(m) }
func (*IncrInteractiveMsgUnreadCountResp) ProtoMessage()    {}
func (*IncrInteractiveMsgUnreadCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{19}
}
func (m *IncrInteractiveMsgUnreadCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IncrInteractiveMsgUnreadCountResp.Unmarshal(m, b)
}
func (m *IncrInteractiveMsgUnreadCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IncrInteractiveMsgUnreadCountResp.Marshal(b, m, deterministic)
}
func (dst *IncrInteractiveMsgUnreadCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IncrInteractiveMsgUnreadCountResp.Merge(dst, src)
}
func (m *IncrInteractiveMsgUnreadCountResp) XXX_Size() int {
	return xxx_messageInfo_IncrInteractiveMsgUnreadCountResp.Size(m)
}
func (m *IncrInteractiveMsgUnreadCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IncrInteractiveMsgUnreadCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_IncrInteractiveMsgUnreadCountResp proto.InternalMessageInfo

func (m *IncrInteractiveMsgUnreadCountResp) GetUnreadLikeCount() uint32 {
	if m != nil {
		return m.UnreadLikeCount
	}
	return 0
}

func (m *IncrInteractiveMsgUnreadCountResp) GetUnreadCommentCount() uint32 {
	if m != nil {
		return m.UnreadCommentCount
	}
	return 0
}

func (m *IncrInteractiveMsgUnreadCountResp) GetUnreadAtCount() uint32 {
	if m != nil {
		return m.UnreadAtCount
	}
	return 0
}

type PublishPostReq struct {
	Post                 *MusePost `protobuf:"bytes,1,opt,name=post,proto3" json:"post,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *PublishPostReq) Reset()         { *m = PublishPostReq{} }
func (m *PublishPostReq) String() string { return proto.CompactTextString(m) }
func (*PublishPostReq) ProtoMessage()    {}
func (*PublishPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{20}
}
func (m *PublishPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishPostReq.Unmarshal(m, b)
}
func (m *PublishPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishPostReq.Marshal(b, m, deterministic)
}
func (dst *PublishPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishPostReq.Merge(dst, src)
}
func (m *PublishPostReq) XXX_Size() int {
	return xxx_messageInfo_PublishPostReq.Size(m)
}
func (m *PublishPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_PublishPostReq proto.InternalMessageInfo

func (m *PublishPostReq) GetPost() *MusePost {
	if m != nil {
		return m.Post
	}
	return nil
}

type PublishPostResp struct {
	Post                 *MusePost `protobuf:"bytes,1,opt,name=post,proto3" json:"post,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *PublishPostResp) Reset()         { *m = PublishPostResp{} }
func (m *PublishPostResp) String() string { return proto.CompactTextString(m) }
func (*PublishPostResp) ProtoMessage()    {}
func (*PublishPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{21}
}
func (m *PublishPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishPostResp.Unmarshal(m, b)
}
func (m *PublishPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishPostResp.Marshal(b, m, deterministic)
}
func (dst *PublishPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishPostResp.Merge(dst, src)
}
func (m *PublishPostResp) XXX_Size() int {
	return xxx_messageInfo_PublishPostResp.Size(m)
}
func (m *PublishPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_PublishPostResp proto.InternalMessageInfo

func (m *PublishPostResp) GetPost() *MusePost {
	if m != nil {
		return m.Post
	}
	return nil
}

type PublishCommentReq struct {
	Comment              *MuseComment `protobuf:"bytes,1,opt,name=comment,proto3" json:"comment,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PublishCommentReq) Reset()         { *m = PublishCommentReq{} }
func (m *PublishCommentReq) String() string { return proto.CompactTextString(m) }
func (*PublishCommentReq) ProtoMessage()    {}
func (*PublishCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{22}
}
func (m *PublishCommentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishCommentReq.Unmarshal(m, b)
}
func (m *PublishCommentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishCommentReq.Marshal(b, m, deterministic)
}
func (dst *PublishCommentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishCommentReq.Merge(dst, src)
}
func (m *PublishCommentReq) XXX_Size() int {
	return xxx_messageInfo_PublishCommentReq.Size(m)
}
func (m *PublishCommentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishCommentReq.DiscardUnknown(m)
}

var xxx_messageInfo_PublishCommentReq proto.InternalMessageInfo

func (m *PublishCommentReq) GetComment() *MuseComment {
	if m != nil {
		return m.Comment
	}
	return nil
}

type PublishCommentResp struct {
	Comment              *MuseComment `protobuf:"bytes,1,opt,name=comment,proto3" json:"comment,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PublishCommentResp) Reset()         { *m = PublishCommentResp{} }
func (m *PublishCommentResp) String() string { return proto.CompactTextString(m) }
func (*PublishCommentResp) ProtoMessage()    {}
func (*PublishCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{23}
}
func (m *PublishCommentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishCommentResp.Unmarshal(m, b)
}
func (m *PublishCommentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishCommentResp.Marshal(b, m, deterministic)
}
func (dst *PublishCommentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishCommentResp.Merge(dst, src)
}
func (m *PublishCommentResp) XXX_Size() int {
	return xxx_messageInfo_PublishCommentResp.Size(m)
}
func (m *PublishCommentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishCommentResp.DiscardUnknown(m)
}

var xxx_messageInfo_PublishCommentResp proto.InternalMessageInfo

func (m *PublishCommentResp) GetComment() *MuseComment {
	if m != nil {
		return m.Comment
	}
	return nil
}

type IncrLikeCountReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IncrLikeCountReq) Reset()         { *m = IncrLikeCountReq{} }
func (m *IncrLikeCountReq) String() string { return proto.CompactTextString(m) }
func (*IncrLikeCountReq) ProtoMessage()    {}
func (*IncrLikeCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{24}
}
func (m *IncrLikeCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IncrLikeCountReq.Unmarshal(m, b)
}
func (m *IncrLikeCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IncrLikeCountReq.Marshal(b, m, deterministic)
}
func (dst *IncrLikeCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IncrLikeCountReq.Merge(dst, src)
}
func (m *IncrLikeCountReq) XXX_Size() int {
	return xxx_messageInfo_IncrLikeCountReq.Size(m)
}
func (m *IncrLikeCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IncrLikeCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_IncrLikeCountReq proto.InternalMessageInfo

func (m *IncrLikeCountReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type IncrLikeCountResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IncrLikeCountResp) Reset()         { *m = IncrLikeCountResp{} }
func (m *IncrLikeCountResp) String() string { return proto.CompactTextString(m) }
func (*IncrLikeCountResp) ProtoMessage()    {}
func (*IncrLikeCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{25}
}
func (m *IncrLikeCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IncrLikeCountResp.Unmarshal(m, b)
}
func (m *IncrLikeCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IncrLikeCountResp.Marshal(b, m, deterministic)
}
func (dst *IncrLikeCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IncrLikeCountResp.Merge(dst, src)
}
func (m *IncrLikeCountResp) XXX_Size() int {
	return xxx_messageInfo_IncrLikeCountResp.Size(m)
}
func (m *IncrLikeCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IncrLikeCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_IncrLikeCountResp proto.InternalMessageInfo

type DescLikeCountReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DescLikeCountReq) Reset()         { *m = DescLikeCountReq{} }
func (m *DescLikeCountReq) String() string { return proto.CompactTextString(m) }
func (*DescLikeCountReq) ProtoMessage()    {}
func (*DescLikeCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{26}
}
func (m *DescLikeCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DescLikeCountReq.Unmarshal(m, b)
}
func (m *DescLikeCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DescLikeCountReq.Marshal(b, m, deterministic)
}
func (dst *DescLikeCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescLikeCountReq.Merge(dst, src)
}
func (m *DescLikeCountReq) XXX_Size() int {
	return xxx_messageInfo_DescLikeCountReq.Size(m)
}
func (m *DescLikeCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DescLikeCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_DescLikeCountReq proto.InternalMessageInfo

func (m *DescLikeCountReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type DescLikeCountResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DescLikeCountResp) Reset()         { *m = DescLikeCountResp{} }
func (m *DescLikeCountResp) String() string { return proto.CompactTextString(m) }
func (*DescLikeCountResp) ProtoMessage()    {}
func (*DescLikeCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{27}
}
func (m *DescLikeCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DescLikeCountResp.Unmarshal(m, b)
}
func (m *DescLikeCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DescLikeCountResp.Marshal(b, m, deterministic)
}
func (dst *DescLikeCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescLikeCountResp.Merge(dst, src)
}
func (m *DescLikeCountResp) XXX_Size() int {
	return xxx_messageInfo_DescLikeCountResp.Size(m)
}
func (m *DescLikeCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DescLikeCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_DescLikeCountResp proto.InternalMessageInfo

type IncrCommentCountReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IncrCommentCountReq) Reset()         { *m = IncrCommentCountReq{} }
func (m *IncrCommentCountReq) String() string { return proto.CompactTextString(m) }
func (*IncrCommentCountReq) ProtoMessage()    {}
func (*IncrCommentCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{28}
}
func (m *IncrCommentCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IncrCommentCountReq.Unmarshal(m, b)
}
func (m *IncrCommentCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IncrCommentCountReq.Marshal(b, m, deterministic)
}
func (dst *IncrCommentCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IncrCommentCountReq.Merge(dst, src)
}
func (m *IncrCommentCountReq) XXX_Size() int {
	return xxx_messageInfo_IncrCommentCountReq.Size(m)
}
func (m *IncrCommentCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IncrCommentCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_IncrCommentCountReq proto.InternalMessageInfo

func (m *IncrCommentCountReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type IncrCommentCountResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IncrCommentCountResp) Reset()         { *m = IncrCommentCountResp{} }
func (m *IncrCommentCountResp) String() string { return proto.CompactTextString(m) }
func (*IncrCommentCountResp) ProtoMessage()    {}
func (*IncrCommentCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{29}
}
func (m *IncrCommentCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IncrCommentCountResp.Unmarshal(m, b)
}
func (m *IncrCommentCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IncrCommentCountResp.Marshal(b, m, deterministic)
}
func (dst *IncrCommentCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IncrCommentCountResp.Merge(dst, src)
}
func (m *IncrCommentCountResp) XXX_Size() int {
	return xxx_messageInfo_IncrCommentCountResp.Size(m)
}
func (m *IncrCommentCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IncrCommentCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_IncrCommentCountResp proto.InternalMessageInfo

type DescCommentCountReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DescCommentCountReq) Reset()         { *m = DescCommentCountReq{} }
func (m *DescCommentCountReq) String() string { return proto.CompactTextString(m) }
func (*DescCommentCountReq) ProtoMessage()    {}
func (*DescCommentCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{30}
}
func (m *DescCommentCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DescCommentCountReq.Unmarshal(m, b)
}
func (m *DescCommentCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DescCommentCountReq.Marshal(b, m, deterministic)
}
func (dst *DescCommentCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescCommentCountReq.Merge(dst, src)
}
func (m *DescCommentCountReq) XXX_Size() int {
	return xxx_messageInfo_DescCommentCountReq.Size(m)
}
func (m *DescCommentCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DescCommentCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_DescCommentCountReq proto.InternalMessageInfo

func (m *DescCommentCountReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type DescCommentCountResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DescCommentCountResp) Reset()         { *m = DescCommentCountResp{} }
func (m *DescCommentCountResp) String() string { return proto.CompactTextString(m) }
func (*DescCommentCountResp) ProtoMessage()    {}
func (*DescCommentCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{31}
}
func (m *DescCommentCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DescCommentCountResp.Unmarshal(m, b)
}
func (m *DescCommentCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DescCommentCountResp.Marshal(b, m, deterministic)
}
func (dst *DescCommentCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescCommentCountResp.Merge(dst, src)
}
func (m *DescCommentCountResp) XXX_Size() int {
	return xxx_messageInfo_DescCommentCountResp.Size(m)
}
func (m *DescCommentCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DescCommentCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_DescCommentCountResp proto.InternalMessageInfo

type GetInteractiveContentCountReq struct {
	PostIds              []string `protobuf:"bytes,1,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetInteractiveContentCountReq) Reset()         { *m = GetInteractiveContentCountReq{} }
func (m *GetInteractiveContentCountReq) String() string { return proto.CompactTextString(m) }
func (*GetInteractiveContentCountReq) ProtoMessage()    {}
func (*GetInteractiveContentCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{32}
}
func (m *GetInteractiveContentCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInteractiveContentCountReq.Unmarshal(m, b)
}
func (m *GetInteractiveContentCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInteractiveContentCountReq.Marshal(b, m, deterministic)
}
func (dst *GetInteractiveContentCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInteractiveContentCountReq.Merge(dst, src)
}
func (m *GetInteractiveContentCountReq) XXX_Size() int {
	return xxx_messageInfo_GetInteractiveContentCountReq.Size(m)
}
func (m *GetInteractiveContentCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInteractiveContentCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetInteractiveContentCountReq proto.InternalMessageInfo

func (m *GetInteractiveContentCountReq) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

type GetInteractiveContentCountResp struct {
	LikeCount            map[string]uint32 `protobuf:"bytes,1,rep,name=like_count,json=likeCount,proto3" json:"like_count,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	CommentCount         map[string]uint32 `protobuf:"bytes,2,rep,name=comment_count,json=commentCount,proto3" json:"comment_count,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetInteractiveContentCountResp) Reset()         { *m = GetInteractiveContentCountResp{} }
func (m *GetInteractiveContentCountResp) String() string { return proto.CompactTextString(m) }
func (*GetInteractiveContentCountResp) ProtoMessage()    {}
func (*GetInteractiveContentCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{33}
}
func (m *GetInteractiveContentCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInteractiveContentCountResp.Unmarshal(m, b)
}
func (m *GetInteractiveContentCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInteractiveContentCountResp.Marshal(b, m, deterministic)
}
func (dst *GetInteractiveContentCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInteractiveContentCountResp.Merge(dst, src)
}
func (m *GetInteractiveContentCountResp) XXX_Size() int {
	return xxx_messageInfo_GetInteractiveContentCountResp.Size(m)
}
func (m *GetInteractiveContentCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInteractiveContentCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetInteractiveContentCountResp proto.InternalMessageInfo

func (m *GetInteractiveContentCountResp) GetLikeCount() map[string]uint32 {
	if m != nil {
		return m.LikeCount
	}
	return nil
}

func (m *GetInteractiveContentCountResp) GetCommentCount() map[string]uint32 {
	if m != nil {
		return m.CommentCount
	}
	return nil
}

type GetPostByIdsReq struct {
	PostIds              []string `protobuf:"bytes,1,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPostByIdsReq) Reset()         { *m = GetPostByIdsReq{} }
func (m *GetPostByIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetPostByIdsReq) ProtoMessage()    {}
func (*GetPostByIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{34}
}
func (m *GetPostByIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostByIdsReq.Unmarshal(m, b)
}
func (m *GetPostByIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostByIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetPostByIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostByIdsReq.Merge(dst, src)
}
func (m *GetPostByIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetPostByIdsReq.Size(m)
}
func (m *GetPostByIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostByIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostByIdsReq proto.InternalMessageInfo

func (m *GetPostByIdsReq) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

type GetPostByIdsResp struct {
	Posts                []*MusePost `protobuf:"bytes,1,rep,name=posts,proto3" json:"posts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetPostByIdsResp) Reset()         { *m = GetPostByIdsResp{} }
func (m *GetPostByIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetPostByIdsResp) ProtoMessage()    {}
func (*GetPostByIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{35}
}
func (m *GetPostByIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostByIdsResp.Unmarshal(m, b)
}
func (m *GetPostByIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostByIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetPostByIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostByIdsResp.Merge(dst, src)
}
func (m *GetPostByIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetPostByIdsResp.Size(m)
}
func (m *GetPostByIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostByIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostByIdsResp proto.InternalMessageInfo

func (m *GetPostByIdsResp) GetPosts() []*MusePost {
	if m != nil {
		return m.Posts
	}
	return nil
}

type GetCommentByIdsReq struct {
	CommentIds           []string `protobuf:"bytes,1,rep,name=comment_ids,json=commentIds,proto3" json:"comment_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCommentByIdsReq) Reset()         { *m = GetCommentByIdsReq{} }
func (m *GetCommentByIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetCommentByIdsReq) ProtoMessage()    {}
func (*GetCommentByIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{36}
}
func (m *GetCommentByIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommentByIdsReq.Unmarshal(m, b)
}
func (m *GetCommentByIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommentByIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetCommentByIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommentByIdsReq.Merge(dst, src)
}
func (m *GetCommentByIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetCommentByIdsReq.Size(m)
}
func (m *GetCommentByIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommentByIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommentByIdsReq proto.InternalMessageInfo

func (m *GetCommentByIdsReq) GetCommentIds() []string {
	if m != nil {
		return m.CommentIds
	}
	return nil
}

type GetCommentByIdsResp struct {
	Comments             []*MuseComment `protobuf:"bytes,1,rep,name=comments,proto3" json:"comments,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetCommentByIdsResp) Reset()         { *m = GetCommentByIdsResp{} }
func (m *GetCommentByIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetCommentByIdsResp) ProtoMessage()    {}
func (*GetCommentByIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{37}
}
func (m *GetCommentByIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommentByIdsResp.Unmarshal(m, b)
}
func (m *GetCommentByIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommentByIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetCommentByIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommentByIdsResp.Merge(dst, src)
}
func (m *GetCommentByIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetCommentByIdsResp.Size(m)
}
func (m *GetCommentByIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommentByIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommentByIdsResp proto.InternalMessageInfo

func (m *GetCommentByIdsResp) GetComments() []*MuseComment {
	if m != nil {
		return m.Comments
	}
	return nil
}

type MusePost struct {
	ChannelId            uint32                `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Id                   string                `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	OwnerId              uint32                `protobuf:"varint,3,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	ContentType          MuseContentType       `protobuf:"varint,4,opt,name=content_type,json=contentType,proto3,enum=musepost.MuseContentType" json:"content_type,omitempty"`
	Text                 string                `protobuf:"bytes,5,opt,name=text,proto3" json:"text,omitempty"`
	Status               ContentStatus         `protobuf:"varint,6,opt,name=status,proto3,enum=musepost.ContentStatus" json:"status,omitempty"`
	DeletedBy            uint32                `protobuf:"varint,7,opt,name=deleted_by,json=deletedBy,proto3" json:"deleted_by,omitempty"`
	CreatedAt            uint32                `protobuf:"varint,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	MoodInfo             *MusePostUserMoodInfo `protobuf:"bytes,9,opt,name=mood_info,json=moodInfo,proto3" json:"mood_info,omitempty"`
	HaveAt               bool                  `protobuf:"varint,10,opt,name=have_at,json=haveAt,proto3" json:"have_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *MusePost) Reset()         { *m = MusePost{} }
func (m *MusePost) String() string { return proto.CompactTextString(m) }
func (*MusePost) ProtoMessage()    {}
func (*MusePost) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{38}
}
func (m *MusePost) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusePost.Unmarshal(m, b)
}
func (m *MusePost) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusePost.Marshal(b, m, deterministic)
}
func (dst *MusePost) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusePost.Merge(dst, src)
}
func (m *MusePost) XXX_Size() int {
	return xxx_messageInfo_MusePost.Size(m)
}
func (m *MusePost) XXX_DiscardUnknown() {
	xxx_messageInfo_MusePost.DiscardUnknown(m)
}

var xxx_messageInfo_MusePost proto.InternalMessageInfo

func (m *MusePost) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MusePost) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *MusePost) GetOwnerId() uint32 {
	if m != nil {
		return m.OwnerId
	}
	return 0
}

func (m *MusePost) GetContentType() MuseContentType {
	if m != nil {
		return m.ContentType
	}
	return MuseContentType_MuseContentType_UNDEFINED
}

func (m *MusePost) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *MusePost) GetStatus() ContentStatus {
	if m != nil {
		return m.Status
	}
	return ContentStatus_ContentStatus_UNDEFINED
}

func (m *MusePost) GetDeletedBy() uint32 {
	if m != nil {
		return m.DeletedBy
	}
	return 0
}

func (m *MusePost) GetCreatedAt() uint32 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *MusePost) GetMoodInfo() *MusePostUserMoodInfo {
	if m != nil {
		return m.MoodInfo
	}
	return nil
}

func (m *MusePost) GetHaveAt() bool {
	if m != nil {
		return m.HaveAt
	}
	return false
}

type MusePostUserMoodInfo struct {
	MoodId               string   `protobuf:"bytes,1,opt,name=mood_id,json=moodId,proto3" json:"mood_id,omitempty"`
	MoodUrl              string   `protobuf:"bytes,2,opt,name=mood_url,json=moodUrl,proto3" json:"mood_url,omitempty"`
	MoodMd5              string   `protobuf:"bytes,3,opt,name=mood_md5,json=moodMd5,proto3" json:"mood_md5,omitempty"`
	MoodText             string   `protobuf:"bytes,4,opt,name=mood_text,json=moodText,proto3" json:"mood_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusePostUserMoodInfo) Reset()         { *m = MusePostUserMoodInfo{} }
func (m *MusePostUserMoodInfo) String() string { return proto.CompactTextString(m) }
func (*MusePostUserMoodInfo) ProtoMessage()    {}
func (*MusePostUserMoodInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{39}
}
func (m *MusePostUserMoodInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusePostUserMoodInfo.Unmarshal(m, b)
}
func (m *MusePostUserMoodInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusePostUserMoodInfo.Marshal(b, m, deterministic)
}
func (dst *MusePostUserMoodInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusePostUserMoodInfo.Merge(dst, src)
}
func (m *MusePostUserMoodInfo) XXX_Size() int {
	return xxx_messageInfo_MusePostUserMoodInfo.Size(m)
}
func (m *MusePostUserMoodInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MusePostUserMoodInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MusePostUserMoodInfo proto.InternalMessageInfo

func (m *MusePostUserMoodInfo) GetMoodId() string {
	if m != nil {
		return m.MoodId
	}
	return ""
}

func (m *MusePostUserMoodInfo) GetMoodUrl() string {
	if m != nil {
		return m.MoodUrl
	}
	return ""
}

func (m *MusePostUserMoodInfo) GetMoodMd5() string {
	if m != nil {
		return m.MoodMd5
	}
	return ""
}

func (m *MusePostUserMoodInfo) GetMoodText() string {
	if m != nil {
		return m.MoodText
	}
	return ""
}

type MuseComment struct {
	Id                   string          `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	PostId               string          `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	OwnerId              uint32          `protobuf:"varint,3,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	ContentType          MuseContentType `protobuf:"varint,4,opt,name=content_type,json=contentType,proto3,enum=musepost.MuseContentType" json:"content_type,omitempty"`
	Text                 string          `protobuf:"bytes,5,opt,name=text,proto3" json:"text,omitempty"`
	Status               ContentStatus   `protobuf:"varint,6,opt,name=status,proto3,enum=musepost.ContentStatus" json:"status,omitempty"`
	DeletedBy            uint32          `protobuf:"varint,7,opt,name=deleted_by,json=deletedBy,proto3" json:"deleted_by,omitempty"`
	CreatedAt            uint32          `protobuf:"varint,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	ParentCommentId      string          `protobuf:"bytes,9,opt,name=parent_comment_id,json=parentCommentId,proto3" json:"parent_comment_id,omitempty"`
	ChannelId            uint32          `protobuf:"varint,10,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ReplyToUid           uint32          `protobuf:"varint,11,opt,name=reply_to_uid,json=replyToUid,proto3" json:"reply_to_uid,omitempty"`
	HaveAt               bool            `protobuf:"varint,12,opt,name=have_at,json=haveAt,proto3" json:"have_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *MuseComment) Reset()         { *m = MuseComment{} }
func (m *MuseComment) String() string { return proto.CompactTextString(m) }
func (*MuseComment) ProtoMessage()    {}
func (*MuseComment) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{40}
}
func (m *MuseComment) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseComment.Unmarshal(m, b)
}
func (m *MuseComment) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseComment.Marshal(b, m, deterministic)
}
func (dst *MuseComment) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseComment.Merge(dst, src)
}
func (m *MuseComment) XXX_Size() int {
	return xxx_messageInfo_MuseComment.Size(m)
}
func (m *MuseComment) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseComment.DiscardUnknown(m)
}

var xxx_messageInfo_MuseComment proto.InternalMessageInfo

func (m *MuseComment) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *MuseComment) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *MuseComment) GetOwnerId() uint32 {
	if m != nil {
		return m.OwnerId
	}
	return 0
}

func (m *MuseComment) GetContentType() MuseContentType {
	if m != nil {
		return m.ContentType
	}
	return MuseContentType_MuseContentType_UNDEFINED
}

func (m *MuseComment) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *MuseComment) GetStatus() ContentStatus {
	if m != nil {
		return m.Status
	}
	return ContentStatus_ContentStatus_UNDEFINED
}

func (m *MuseComment) GetDeletedBy() uint32 {
	if m != nil {
		return m.DeletedBy
	}
	return 0
}

func (m *MuseComment) GetCreatedAt() uint32 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *MuseComment) GetParentCommentId() string {
	if m != nil {
		return m.ParentCommentId
	}
	return ""
}

func (m *MuseComment) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuseComment) GetReplyToUid() uint32 {
	if m != nil {
		return m.ReplyToUid
	}
	return 0
}

func (m *MuseComment) GetHaveAt() bool {
	if m != nil {
		return m.HaveAt
	}
	return false
}

type TrimActivitiesByCapacityReq struct {
	Feed                 *Feed    `protobuf:"bytes,1,opt,name=feed,proto3" json:"feed,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TrimActivitiesByCapacityReq) Reset()         { *m = TrimActivitiesByCapacityReq{} }
func (m *TrimActivitiesByCapacityReq) String() string { return proto.CompactTextString(m) }
func (*TrimActivitiesByCapacityReq) ProtoMessage()    {}
func (*TrimActivitiesByCapacityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{41}
}
func (m *TrimActivitiesByCapacityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TrimActivitiesByCapacityReq.Unmarshal(m, b)
}
func (m *TrimActivitiesByCapacityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TrimActivitiesByCapacityReq.Marshal(b, m, deterministic)
}
func (dst *TrimActivitiesByCapacityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TrimActivitiesByCapacityReq.Merge(dst, src)
}
func (m *TrimActivitiesByCapacityReq) XXX_Size() int {
	return xxx_messageInfo_TrimActivitiesByCapacityReq.Size(m)
}
func (m *TrimActivitiesByCapacityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TrimActivitiesByCapacityReq.DiscardUnknown(m)
}

var xxx_messageInfo_TrimActivitiesByCapacityReq proto.InternalMessageInfo

func (m *TrimActivitiesByCapacityReq) GetFeed() *Feed {
	if m != nil {
		return m.Feed
	}
	return nil
}

type TrimActivitiesByCapacityResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TrimActivitiesByCapacityResp) Reset()         { *m = TrimActivitiesByCapacityResp{} }
func (m *TrimActivitiesByCapacityResp) String() string { return proto.CompactTextString(m) }
func (*TrimActivitiesByCapacityResp) ProtoMessage()    {}
func (*TrimActivitiesByCapacityResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{42}
}
func (m *TrimActivitiesByCapacityResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TrimActivitiesByCapacityResp.Unmarshal(m, b)
}
func (m *TrimActivitiesByCapacityResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TrimActivitiesByCapacityResp.Marshal(b, m, deterministic)
}
func (dst *TrimActivitiesByCapacityResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TrimActivitiesByCapacityResp.Merge(dst, src)
}
func (m *TrimActivitiesByCapacityResp) XXX_Size() int {
	return xxx_messageInfo_TrimActivitiesByCapacityResp.Size(m)
}
func (m *TrimActivitiesByCapacityResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TrimActivitiesByCapacityResp.DiscardUnknown(m)
}

var xxx_messageInfo_TrimActivitiesByCapacityResp proto.InternalMessageInfo

type IsActivityKeysExistingReq struct {
	Feed                 *Feed    `protobuf:"bytes,1,opt,name=feed,proto3" json:"feed,omitempty"`
	ActivityKeys         []string `protobuf:"bytes,2,rep,name=activity_keys,json=activityKeys,proto3" json:"activity_keys,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsActivityKeysExistingReq) Reset()         { *m = IsActivityKeysExistingReq{} }
func (m *IsActivityKeysExistingReq) String() string { return proto.CompactTextString(m) }
func (*IsActivityKeysExistingReq) ProtoMessage()    {}
func (*IsActivityKeysExistingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{43}
}
func (m *IsActivityKeysExistingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsActivityKeysExistingReq.Unmarshal(m, b)
}
func (m *IsActivityKeysExistingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsActivityKeysExistingReq.Marshal(b, m, deterministic)
}
func (dst *IsActivityKeysExistingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsActivityKeysExistingReq.Merge(dst, src)
}
func (m *IsActivityKeysExistingReq) XXX_Size() int {
	return xxx_messageInfo_IsActivityKeysExistingReq.Size(m)
}
func (m *IsActivityKeysExistingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsActivityKeysExistingReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsActivityKeysExistingReq proto.InternalMessageInfo

func (m *IsActivityKeysExistingReq) GetFeed() *Feed {
	if m != nil {
		return m.Feed
	}
	return nil
}

func (m *IsActivityKeysExistingReq) GetActivityKeys() []string {
	if m != nil {
		return m.ActivityKeys
	}
	return nil
}

type IsActivityKeysExistResp struct {
	ExistMap             map[string]bool `protobuf:"bytes,1,rep,name=exist_map,json=existMap,proto3" json:"exist_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *IsActivityKeysExistResp) Reset()         { *m = IsActivityKeysExistResp{} }
func (m *IsActivityKeysExistResp) String() string { return proto.CompactTextString(m) }
func (*IsActivityKeysExistResp) ProtoMessage()    {}
func (*IsActivityKeysExistResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{44}
}
func (m *IsActivityKeysExistResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsActivityKeysExistResp.Unmarshal(m, b)
}
func (m *IsActivityKeysExistResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsActivityKeysExistResp.Marshal(b, m, deterministic)
}
func (dst *IsActivityKeysExistResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsActivityKeysExistResp.Merge(dst, src)
}
func (m *IsActivityKeysExistResp) XXX_Size() int {
	return xxx_messageInfo_IsActivityKeysExistResp.Size(m)
}
func (m *IsActivityKeysExistResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsActivityKeysExistResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsActivityKeysExistResp proto.InternalMessageInfo

func (m *IsActivityKeysExistResp) GetExistMap() map[string]bool {
	if m != nil {
		return m.ExistMap
	}
	return nil
}

type RemoveActivitiesReq struct {
	Feeds *Feeds `protobuf:"bytes,1,opt,name=feeds,proto3" json:"feeds,omitempty"`
	// Types that are valid to be assigned to DeleteBy:
	//	*RemoveActivitiesReq_Actor
	//	*RemoveActivitiesReq_ActivityKeys
	DeleteBy             isRemoveActivitiesReq_DeleteBy `protobuf_oneof:"delete_by"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *RemoveActivitiesReq) Reset()         { *m = RemoveActivitiesReq{} }
func (m *RemoveActivitiesReq) String() string { return proto.CompactTextString(m) }
func (*RemoveActivitiesReq) ProtoMessage()    {}
func (*RemoveActivitiesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{45}
}
func (m *RemoveActivitiesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveActivitiesReq.Unmarshal(m, b)
}
func (m *RemoveActivitiesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveActivitiesReq.Marshal(b, m, deterministic)
}
func (dst *RemoveActivitiesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveActivitiesReq.Merge(dst, src)
}
func (m *RemoveActivitiesReq) XXX_Size() int {
	return xxx_messageInfo_RemoveActivitiesReq.Size(m)
}
func (m *RemoveActivitiesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveActivitiesReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveActivitiesReq proto.InternalMessageInfo

func (m *RemoveActivitiesReq) GetFeeds() *Feeds {
	if m != nil {
		return m.Feeds
	}
	return nil
}

type isRemoveActivitiesReq_DeleteBy interface {
	isRemoveActivitiesReq_DeleteBy()
}

type RemoveActivitiesReq_Actor struct {
	Actor string `protobuf:"bytes,2,opt,name=actor,proto3,oneof"`
}

type RemoveActivitiesReq_ActivityKeys struct {
	ActivityKeys *ActivityKeys `protobuf:"bytes,3,opt,name=activity_keys,json=activityKeys,proto3,oneof"`
}

func (*RemoveActivitiesReq_Actor) isRemoveActivitiesReq_DeleteBy() {}

func (*RemoveActivitiesReq_ActivityKeys) isRemoveActivitiesReq_DeleteBy() {}

func (m *RemoveActivitiesReq) GetDeleteBy() isRemoveActivitiesReq_DeleteBy {
	if m != nil {
		return m.DeleteBy
	}
	return nil
}

func (m *RemoveActivitiesReq) GetActor() string {
	if x, ok := m.GetDeleteBy().(*RemoveActivitiesReq_Actor); ok {
		return x.Actor
	}
	return ""
}

func (m *RemoveActivitiesReq) GetActivityKeys() *ActivityKeys {
	if x, ok := m.GetDeleteBy().(*RemoveActivitiesReq_ActivityKeys); ok {
		return x.ActivityKeys
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*RemoveActivitiesReq) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _RemoveActivitiesReq_OneofMarshaler, _RemoveActivitiesReq_OneofUnmarshaler, _RemoveActivitiesReq_OneofSizer, []interface{}{
		(*RemoveActivitiesReq_Actor)(nil),
		(*RemoveActivitiesReq_ActivityKeys)(nil),
	}
}

func _RemoveActivitiesReq_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*RemoveActivitiesReq)
	// delete_by
	switch x := m.DeleteBy.(type) {
	case *RemoveActivitiesReq_Actor:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.Actor)
	case *RemoveActivitiesReq_ActivityKeys:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.ActivityKeys); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("RemoveActivitiesReq.DeleteBy has unexpected type %T", x)
	}
	return nil
}

func _RemoveActivitiesReq_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*RemoveActivitiesReq)
	switch tag {
	case 2: // delete_by.actor
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.DeleteBy = &RemoveActivitiesReq_Actor{x}
		return true, err
	case 3: // delete_by.activity_keys
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ActivityKeys)
		err := b.DecodeMessage(msg)
		m.DeleteBy = &RemoveActivitiesReq_ActivityKeys{msg}
		return true, err
	default:
		return false, nil
	}
}

func _RemoveActivitiesReq_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*RemoveActivitiesReq)
	// delete_by
	switch x := m.DeleteBy.(type) {
	case *RemoveActivitiesReq_Actor:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(len(x.Actor)))
		n += len(x.Actor)
	case *RemoveActivitiesReq_ActivityKeys:
		s := proto.Size(x.ActivityKeys)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type RemoveActivitiesResp struct {
	RecordsAffected      int64    `protobuf:"varint,1,opt,name=records_affected,json=recordsAffected,proto3" json:"records_affected,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveActivitiesResp) Reset()         { *m = RemoveActivitiesResp{} }
func (m *RemoveActivitiesResp) String() string { return proto.CompactTextString(m) }
func (*RemoveActivitiesResp) ProtoMessage()    {}
func (*RemoveActivitiesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{46}
}
func (m *RemoveActivitiesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveActivitiesResp.Unmarshal(m, b)
}
func (m *RemoveActivitiesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveActivitiesResp.Marshal(b, m, deterministic)
}
func (dst *RemoveActivitiesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveActivitiesResp.Merge(dst, src)
}
func (m *RemoveActivitiesResp) XXX_Size() int {
	return xxx_messageInfo_RemoveActivitiesResp.Size(m)
}
func (m *RemoveActivitiesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveActivitiesResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveActivitiesResp proto.InternalMessageInfo

func (m *RemoveActivitiesResp) GetRecordsAffected() int64 {
	if m != nil {
		return m.RecordsAffected
	}
	return 0
}

type GetActivitiesByRangeReq struct {
	Feed                 *Feed         `protobuf:"bytes,1,opt,name=feed,proto3" json:"feed,omitempty"`
	Options              *QueryOptions `protobuf:"bytes,5,opt,name=options,proto3" json:"options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetActivitiesByRangeReq) Reset()         { *m = GetActivitiesByRangeReq{} }
func (m *GetActivitiesByRangeReq) String() string { return proto.CompactTextString(m) }
func (*GetActivitiesByRangeReq) ProtoMessage()    {}
func (*GetActivitiesByRangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{47}
}
func (m *GetActivitiesByRangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivitiesByRangeReq.Unmarshal(m, b)
}
func (m *GetActivitiesByRangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivitiesByRangeReq.Marshal(b, m, deterministic)
}
func (dst *GetActivitiesByRangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivitiesByRangeReq.Merge(dst, src)
}
func (m *GetActivitiesByRangeReq) XXX_Size() int {
	return xxx_messageInfo_GetActivitiesByRangeReq.Size(m)
}
func (m *GetActivitiesByRangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivitiesByRangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivitiesByRangeReq proto.InternalMessageInfo

func (m *GetActivitiesByRangeReq) GetFeed() *Feed {
	if m != nil {
		return m.Feed
	}
	return nil
}

func (m *GetActivitiesByRangeReq) GetOptions() *QueryOptions {
	if m != nil {
		return m.Options
	}
	return nil
}

type ActivityKey struct {
	Time                 uint64   `protobuf:"varint,2,opt,name=time,proto3" json:"time,omitempty"`
	ForeignId            string   `protobuf:"bytes,3,opt,name=foreign_id,json=foreignId,proto3" json:"foreign_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActivityKey) Reset()         { *m = ActivityKey{} }
func (m *ActivityKey) String() string { return proto.CompactTextString(m) }
func (*ActivityKey) ProtoMessage()    {}
func (*ActivityKey) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{48}
}
func (m *ActivityKey) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivityKey.Unmarshal(m, b)
}
func (m *ActivityKey) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivityKey.Marshal(b, m, deterministic)
}
func (dst *ActivityKey) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivityKey.Merge(dst, src)
}
func (m *ActivityKey) XXX_Size() int {
	return xxx_messageInfo_ActivityKey.Size(m)
}
func (m *ActivityKey) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivityKey.DiscardUnknown(m)
}

var xxx_messageInfo_ActivityKey proto.InternalMessageInfo

func (m *ActivityKey) GetTime() uint64 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *ActivityKey) GetForeignId() string {
	if m != nil {
		return m.ForeignId
	}
	return ""
}

type ActivityKeys struct {
	Keys                 []*ActivityKey `protobuf:"bytes,1,rep,name=keys,proto3" json:"keys,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ActivityKeys) Reset()         { *m = ActivityKeys{} }
func (m *ActivityKeys) String() string { return proto.CompactTextString(m) }
func (*ActivityKeys) ProtoMessage()    {}
func (*ActivityKeys) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{49}
}
func (m *ActivityKeys) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivityKeys.Unmarshal(m, b)
}
func (m *ActivityKeys) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivityKeys.Marshal(b, m, deterministic)
}
func (dst *ActivityKeys) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivityKeys.Merge(dst, src)
}
func (m *ActivityKeys) XXX_Size() int {
	return xxx_messageInfo_ActivityKeys.Size(m)
}
func (m *ActivityKeys) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivityKeys.DiscardUnknown(m)
}

var xxx_messageInfo_ActivityKeys proto.InternalMessageInfo

func (m *ActivityKeys) GetKeys() []*ActivityKey {
	if m != nil {
		return m.Keys
	}
	return nil
}

type QueryOptions struct {
	RangeStartInclusive bool `protobuf:"varint,1,opt,name=range_start_inclusive,json=rangeStartInclusive,proto3" json:"range_start_inclusive,omitempty"`
	RangeStopInclusive  bool `protobuf:"varint,2,opt,name=range_stop_inclusive,json=rangeStopInclusive,proto3" json:"range_stop_inclusive,omitempty"`
	// Types that are valid to be assigned to Range:
	//	*QueryOptions_ActivityKeyRange_
	//	*QueryOptions_CustomScoreRange_
	Range isQueryOptions_Range `protobuf_oneof:"range"`
	// Types that are valid to be assigned to OrderBy:
	//	*QueryOptions_DependsOnRange
	//	*QueryOptions_ActivityKey
	//	*QueryOptions_CustomScore
	OrderBy isQueryOptions_OrderBy `protobuf_oneof:"order_by"`
	Sort    QueryOptions_Sort      `protobuf:"varint,20,opt,name=sort,proto3,enum=musepost.QueryOptions_Sort" json:"sort,omitempty"`
	Limit   uint32                 `protobuf:"varint,21,opt,name=limit,proto3" json:"limit,omitempty"`
	// All the filters below are **not supported** in REDIS-stored feeds
	Actors               []string `protobuf:"bytes,22,rep,name=actors,proto3" json:"actors,omitempty"`
	Verbs                []string `protobuf:"bytes,23,rep,name=verbs,proto3" json:"verbs,omitempty"`
	CustomFilters        []string `protobuf:"bytes,24,rep,name=custom_filters,json=customFilters,proto3" json:"custom_filters,omitempty"`
	ActivityKeys         []string `protobuf:"bytes,25,rep,name=activity_keys,json=activityKeys,proto3" json:"activity_keys,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryOptions) Reset()         { *m = QueryOptions{} }
func (m *QueryOptions) String() string { return proto.CompactTextString(m) }
func (*QueryOptions) ProtoMessage()    {}
func (*QueryOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{50}
}
func (m *QueryOptions) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryOptions.Unmarshal(m, b)
}
func (m *QueryOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryOptions.Marshal(b, m, deterministic)
}
func (dst *QueryOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryOptions.Merge(dst, src)
}
func (m *QueryOptions) XXX_Size() int {
	return xxx_messageInfo_QueryOptions.Size(m)
}
func (m *QueryOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryOptions.DiscardUnknown(m)
}

var xxx_messageInfo_QueryOptions proto.InternalMessageInfo

func (m *QueryOptions) GetRangeStartInclusive() bool {
	if m != nil {
		return m.RangeStartInclusive
	}
	return false
}

func (m *QueryOptions) GetRangeStopInclusive() bool {
	if m != nil {
		return m.RangeStopInclusive
	}
	return false
}

type isQueryOptions_Range interface {
	isQueryOptions_Range()
}

type QueryOptions_ActivityKeyRange_ struct {
	ActivityKeyRange *QueryOptions_ActivityKeyRange `protobuf:"bytes,3,opt,name=activity_key_range,json=activityKeyRange,proto3,oneof"`
}

type QueryOptions_CustomScoreRange_ struct {
	CustomScoreRange *QueryOptions_CustomScoreRange `protobuf:"bytes,4,opt,name=custom_score_range,json=customScoreRange,proto3,oneof"`
}

func (*QueryOptions_ActivityKeyRange_) isQueryOptions_Range() {}

func (*QueryOptions_CustomScoreRange_) isQueryOptions_Range() {}

func (m *QueryOptions) GetRange() isQueryOptions_Range {
	if m != nil {
		return m.Range
	}
	return nil
}

func (m *QueryOptions) GetActivityKeyRange() *QueryOptions_ActivityKeyRange {
	if x, ok := m.GetRange().(*QueryOptions_ActivityKeyRange_); ok {
		return x.ActivityKeyRange
	}
	return nil
}

func (m *QueryOptions) GetCustomScoreRange() *QueryOptions_CustomScoreRange {
	if x, ok := m.GetRange().(*QueryOptions_CustomScoreRange_); ok {
		return x.CustomScoreRange
	}
	return nil
}

type isQueryOptions_OrderBy interface {
	isQueryOptions_OrderBy()
}

type QueryOptions_DependsOnRange struct {
	DependsOnRange string `protobuf:"bytes,10,opt,name=depends_on_range,json=dependsOnRange,proto3,oneof"`
}

type QueryOptions_ActivityKey struct {
	ActivityKey string `protobuf:"bytes,11,opt,name=activity_key,json=activityKey,proto3,oneof"`
}

type QueryOptions_CustomScore struct {
	CustomScore string `protobuf:"bytes,12,opt,name=custom_score,json=customScore,proto3,oneof"`
}

func (*QueryOptions_DependsOnRange) isQueryOptions_OrderBy() {}

func (*QueryOptions_ActivityKey) isQueryOptions_OrderBy() {}

func (*QueryOptions_CustomScore) isQueryOptions_OrderBy() {}

func (m *QueryOptions) GetOrderBy() isQueryOptions_OrderBy {
	if m != nil {
		return m.OrderBy
	}
	return nil
}

func (m *QueryOptions) GetDependsOnRange() string {
	if x, ok := m.GetOrderBy().(*QueryOptions_DependsOnRange); ok {
		return x.DependsOnRange
	}
	return ""
}

func (m *QueryOptions) GetActivityKey() string {
	if x, ok := m.GetOrderBy().(*QueryOptions_ActivityKey); ok {
		return x.ActivityKey
	}
	return ""
}

func (m *QueryOptions) GetCustomScore() string {
	if x, ok := m.GetOrderBy().(*QueryOptions_CustomScore); ok {
		return x.CustomScore
	}
	return ""
}

func (m *QueryOptions) GetSort() QueryOptions_Sort {
	if m != nil {
		return m.Sort
	}
	return QueryOptions_ASC
}

func (m *QueryOptions) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *QueryOptions) GetActors() []string {
	if m != nil {
		return m.Actors
	}
	return nil
}

func (m *QueryOptions) GetVerbs() []string {
	if m != nil {
		return m.Verbs
	}
	return nil
}

func (m *QueryOptions) GetCustomFilters() []string {
	if m != nil {
		return m.CustomFilters
	}
	return nil
}

func (m *QueryOptions) GetActivityKeys() []string {
	if m != nil {
		return m.ActivityKeys
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*QueryOptions) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _QueryOptions_OneofMarshaler, _QueryOptions_OneofUnmarshaler, _QueryOptions_OneofSizer, []interface{}{
		(*QueryOptions_ActivityKeyRange_)(nil),
		(*QueryOptions_CustomScoreRange_)(nil),
		(*QueryOptions_DependsOnRange)(nil),
		(*QueryOptions_ActivityKey)(nil),
		(*QueryOptions_CustomScore)(nil),
	}
}

func _QueryOptions_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*QueryOptions)
	// range
	switch x := m.Range.(type) {
	case *QueryOptions_ActivityKeyRange_:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.ActivityKeyRange); err != nil {
			return err
		}
	case *QueryOptions_CustomScoreRange_:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.CustomScoreRange); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("QueryOptions.Range has unexpected type %T", x)
	}
	// order_by
	switch x := m.OrderBy.(type) {
	case *QueryOptions_DependsOnRange:
		b.EncodeVarint(10<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.DependsOnRange)
	case *QueryOptions_ActivityKey:
		b.EncodeVarint(11<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.ActivityKey)
	case *QueryOptions_CustomScore:
		b.EncodeVarint(12<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.CustomScore)
	case nil:
	default:
		return fmt.Errorf("QueryOptions.OrderBy has unexpected type %T", x)
	}
	return nil
}

func _QueryOptions_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*QueryOptions)
	switch tag {
	case 3: // range.activity_key_range
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(QueryOptions_ActivityKeyRange)
		err := b.DecodeMessage(msg)
		m.Range = &QueryOptions_ActivityKeyRange_{msg}
		return true, err
	case 4: // range.custom_score_range
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(QueryOptions_CustomScoreRange)
		err := b.DecodeMessage(msg)
		m.Range = &QueryOptions_CustomScoreRange_{msg}
		return true, err
	case 10: // order_by.depends_on_range
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.OrderBy = &QueryOptions_DependsOnRange{x}
		return true, err
	case 11: // order_by.activity_key
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.OrderBy = &QueryOptions_ActivityKey{x}
		return true, err
	case 12: // order_by.custom_score
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.OrderBy = &QueryOptions_CustomScore{x}
		return true, err
	default:
		return false, nil
	}
}

func _QueryOptions_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*QueryOptions)
	// range
	switch x := m.Range.(type) {
	case *QueryOptions_ActivityKeyRange_:
		s := proto.Size(x.ActivityKeyRange)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *QueryOptions_CustomScoreRange_:
		s := proto.Size(x.CustomScoreRange)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	// order_by
	switch x := m.OrderBy.(type) {
	case *QueryOptions_DependsOnRange:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(len(x.DependsOnRange)))
		n += len(x.DependsOnRange)
	case *QueryOptions_ActivityKey:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(len(x.ActivityKey)))
		n += len(x.ActivityKey)
	case *QueryOptions_CustomScore:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(len(x.CustomScore)))
		n += len(x.CustomScore)
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type QueryOptions_CustomScoreRange struct {
	Start                float64  `protobuf:"fixed64,1,opt,name=start,proto3" json:"start,omitempty"`
	Stop                 float64  `protobuf:"fixed64,2,opt,name=stop,proto3" json:"stop,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryOptions_CustomScoreRange) Reset()         { *m = QueryOptions_CustomScoreRange{} }
func (m *QueryOptions_CustomScoreRange) String() string { return proto.CompactTextString(m) }
func (*QueryOptions_CustomScoreRange) ProtoMessage()    {}
func (*QueryOptions_CustomScoreRange) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{50, 0}
}
func (m *QueryOptions_CustomScoreRange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryOptions_CustomScoreRange.Unmarshal(m, b)
}
func (m *QueryOptions_CustomScoreRange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryOptions_CustomScoreRange.Marshal(b, m, deterministic)
}
func (dst *QueryOptions_CustomScoreRange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryOptions_CustomScoreRange.Merge(dst, src)
}
func (m *QueryOptions_CustomScoreRange) XXX_Size() int {
	return xxx_messageInfo_QueryOptions_CustomScoreRange.Size(m)
}
func (m *QueryOptions_CustomScoreRange) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryOptions_CustomScoreRange.DiscardUnknown(m)
}

var xxx_messageInfo_QueryOptions_CustomScoreRange proto.InternalMessageInfo

func (m *QueryOptions_CustomScoreRange) GetStart() float64 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *QueryOptions_CustomScoreRange) GetStop() float64 {
	if m != nil {
		return m.Stop
	}
	return 0
}

// 按照time-foreign排序, redis中不支持
type QueryOptions_ActivityKeyRange struct {
	Start                *ActivityKey `protobuf:"bytes,1,opt,name=start,proto3" json:"start,omitempty"`
	Stop                 *ActivityKey `protobuf:"bytes,2,opt,name=stop,proto3" json:"stop,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *QueryOptions_ActivityKeyRange) Reset()         { *m = QueryOptions_ActivityKeyRange{} }
func (m *QueryOptions_ActivityKeyRange) String() string { return proto.CompactTextString(m) }
func (*QueryOptions_ActivityKeyRange) ProtoMessage()    {}
func (*QueryOptions_ActivityKeyRange) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{50, 1}
}
func (m *QueryOptions_ActivityKeyRange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryOptions_ActivityKeyRange.Unmarshal(m, b)
}
func (m *QueryOptions_ActivityKeyRange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryOptions_ActivityKeyRange.Marshal(b, m, deterministic)
}
func (dst *QueryOptions_ActivityKeyRange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryOptions_ActivityKeyRange.Merge(dst, src)
}
func (m *QueryOptions_ActivityKeyRange) XXX_Size() int {
	return xxx_messageInfo_QueryOptions_ActivityKeyRange.Size(m)
}
func (m *QueryOptions_ActivityKeyRange) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryOptions_ActivityKeyRange.DiscardUnknown(m)
}

var xxx_messageInfo_QueryOptions_ActivityKeyRange proto.InternalMessageInfo

func (m *QueryOptions_ActivityKeyRange) GetStart() *ActivityKey {
	if m != nil {
		return m.Start
	}
	return nil
}

func (m *QueryOptions_ActivityKeyRange) GetStop() *ActivityKey {
	if m != nil {
		return m.Stop
	}
	return nil
}

type GetActivitiesByRangeResp struct {
	Activities           []*Activity `protobuf:"bytes,1,rep,name=activities,proto3" json:"activities,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetActivitiesByRangeResp) Reset()         { *m = GetActivitiesByRangeResp{} }
func (m *GetActivitiesByRangeResp) String() string { return proto.CompactTextString(m) }
func (*GetActivitiesByRangeResp) ProtoMessage()    {}
func (*GetActivitiesByRangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{51}
}
func (m *GetActivitiesByRangeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivitiesByRangeResp.Unmarshal(m, b)
}
func (m *GetActivitiesByRangeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivitiesByRangeResp.Marshal(b, m, deterministic)
}
func (dst *GetActivitiesByRangeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivitiesByRangeResp.Merge(dst, src)
}
func (m *GetActivitiesByRangeResp) XXX_Size() int {
	return xxx_messageInfo_GetActivitiesByRangeResp.Size(m)
}
func (m *GetActivitiesByRangeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivitiesByRangeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivitiesByRangeResp proto.InternalMessageInfo

func (m *GetActivitiesByRangeResp) GetActivities() []*Activity {
	if m != nil {
		return m.Activities
	}
	return nil
}

type BatchGetActivitiesByRangeReq struct {
	Feeds                *Feeds        `protobuf:"bytes,1,opt,name=feeds,proto3" json:"feeds,omitempty"`
	Options              *QueryOptions `protobuf:"bytes,2,opt,name=options,proto3" json:"options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BatchGetActivitiesByRangeReq) Reset()         { *m = BatchGetActivitiesByRangeReq{} }
func (m *BatchGetActivitiesByRangeReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetActivitiesByRangeReq) ProtoMessage()    {}
func (*BatchGetActivitiesByRangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{52}
}
func (m *BatchGetActivitiesByRangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetActivitiesByRangeReq.Unmarshal(m, b)
}
func (m *BatchGetActivitiesByRangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetActivitiesByRangeReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetActivitiesByRangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetActivitiesByRangeReq.Merge(dst, src)
}
func (m *BatchGetActivitiesByRangeReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetActivitiesByRangeReq.Size(m)
}
func (m *BatchGetActivitiesByRangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetActivitiesByRangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetActivitiesByRangeReq proto.InternalMessageInfo

func (m *BatchGetActivitiesByRangeReq) GetFeeds() *Feeds {
	if m != nil {
		return m.Feeds
	}
	return nil
}

func (m *BatchGetActivitiesByRangeReq) GetOptions() *QueryOptions {
	if m != nil {
		return m.Options
	}
	return nil
}

type BatchGetActivitiesByRangeResp struct {
	Feeds                []*BatchGetActivitiesByRangeResp_FeedActivities `protobuf:"bytes,1,rep,name=feeds,proto3" json:"feeds,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                        `json:"-"`
	XXX_unrecognized     []byte                                          `json:"-"`
	XXX_sizecache        int32                                           `json:"-"`
}

func (m *BatchGetActivitiesByRangeResp) Reset()         { *m = BatchGetActivitiesByRangeResp{} }
func (m *BatchGetActivitiesByRangeResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetActivitiesByRangeResp) ProtoMessage()    {}
func (*BatchGetActivitiesByRangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{53}
}
func (m *BatchGetActivitiesByRangeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetActivitiesByRangeResp.Unmarshal(m, b)
}
func (m *BatchGetActivitiesByRangeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetActivitiesByRangeResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetActivitiesByRangeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetActivitiesByRangeResp.Merge(dst, src)
}
func (m *BatchGetActivitiesByRangeResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetActivitiesByRangeResp.Size(m)
}
func (m *BatchGetActivitiesByRangeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetActivitiesByRangeResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetActivitiesByRangeResp proto.InternalMessageInfo

func (m *BatchGetActivitiesByRangeResp) GetFeeds() []*BatchGetActivitiesByRangeResp_FeedActivities {
	if m != nil {
		return m.Feeds
	}
	return nil
}

type BatchGetActivitiesByRangeResp_FeedActivities struct {
	Feed                 *Feed       `protobuf:"bytes,1,opt,name=feed,proto3" json:"feed,omitempty"`
	Activities           []*Activity `protobuf:"bytes,2,rep,name=activities,proto3" json:"activities,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *BatchGetActivitiesByRangeResp_FeedActivities) Reset() {
	*m = BatchGetActivitiesByRangeResp_FeedActivities{}
}
func (m *BatchGetActivitiesByRangeResp_FeedActivities) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetActivitiesByRangeResp_FeedActivities) ProtoMessage() {}
func (*BatchGetActivitiesByRangeResp_FeedActivities) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{53, 0}
}
func (m *BatchGetActivitiesByRangeResp_FeedActivities) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetActivitiesByRangeResp_FeedActivities.Unmarshal(m, b)
}
func (m *BatchGetActivitiesByRangeResp_FeedActivities) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetActivitiesByRangeResp_FeedActivities.Marshal(b, m, deterministic)
}
func (dst *BatchGetActivitiesByRangeResp_FeedActivities) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetActivitiesByRangeResp_FeedActivities.Merge(dst, src)
}
func (m *BatchGetActivitiesByRangeResp_FeedActivities) XXX_Size() int {
	return xxx_messageInfo_BatchGetActivitiesByRangeResp_FeedActivities.Size(m)
}
func (m *BatchGetActivitiesByRangeResp_FeedActivities) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetActivitiesByRangeResp_FeedActivities.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetActivitiesByRangeResp_FeedActivities proto.InternalMessageInfo

func (m *BatchGetActivitiesByRangeResp_FeedActivities) GetFeed() *Feed {
	if m != nil {
		return m.Feed
	}
	return nil
}

func (m *BatchGetActivitiesByRangeResp_FeedActivities) GetActivities() []*Activity {
	if m != nil {
		return m.Activities
	}
	return nil
}

type Activity struct {
	Time                 uint64   `protobuf:"varint,1,opt,name=time,proto3" json:"time,omitempty"`
	ForeignId            string   `protobuf:"bytes,2,opt,name=foreign_id,json=foreignId,proto3" json:"foreign_id,omitempty"`
	Actor                string   `protobuf:"bytes,3,opt,name=actor,proto3" json:"actor,omitempty"`
	Verb                 string   `protobuf:"bytes,4,opt,name=verb,proto3" json:"verb,omitempty"`
	Object               string   `protobuf:"bytes,5,opt,name=object,proto3" json:"object,omitempty"`
	CustomScore          uint32   `protobuf:"varint,11,opt,name=custom_score,json=customScore,proto3" json:"custom_score,omitempty"`
	CustomFilter         string   `protobuf:"bytes,12,opt,name=custom_filter,json=customFilter,proto3" json:"custom_filter,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Activity) Reset()         { *m = Activity{} }
func (m *Activity) String() string { return proto.CompactTextString(m) }
func (*Activity) ProtoMessage()    {}
func (*Activity) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{54}
}
func (m *Activity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Activity.Unmarshal(m, b)
}
func (m *Activity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Activity.Marshal(b, m, deterministic)
}
func (dst *Activity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Activity.Merge(dst, src)
}
func (m *Activity) XXX_Size() int {
	return xxx_messageInfo_Activity.Size(m)
}
func (m *Activity) XXX_DiscardUnknown() {
	xxx_messageInfo_Activity.DiscardUnknown(m)
}

var xxx_messageInfo_Activity proto.InternalMessageInfo

func (m *Activity) GetTime() uint64 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *Activity) GetForeignId() string {
	if m != nil {
		return m.ForeignId
	}
	return ""
}

func (m *Activity) GetActor() string {
	if m != nil {
		return m.Actor
	}
	return ""
}

func (m *Activity) GetVerb() string {
	if m != nil {
		return m.Verb
	}
	return ""
}

func (m *Activity) GetObject() string {
	if m != nil {
		return m.Object
	}
	return ""
}

func (m *Activity) GetCustomScore() uint32 {
	if m != nil {
		return m.CustomScore
	}
	return 0
}

func (m *Activity) GetCustomFilter() string {
	if m != nil {
		return m.CustomFilter
	}
	return ""
}

type InsertActivitiesReq struct {
	Feeds                *Feeds      `protobuf:"bytes,1,opt,name=feeds,proto3" json:"feeds,omitempty"`
	Activities           []*Activity `protobuf:"bytes,2,rep,name=activities,proto3" json:"activities,omitempty"`
	ConfilctPolicy       uint32      `protobuf:"varint,3,opt,name=confilct_policy,json=confilctPolicy,proto3" json:"confilct_policy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *InsertActivitiesReq) Reset()         { *m = InsertActivitiesReq{} }
func (m *InsertActivitiesReq) String() string { return proto.CompactTextString(m) }
func (*InsertActivitiesReq) ProtoMessage()    {}
func (*InsertActivitiesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{55}
}
func (m *InsertActivitiesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertActivitiesReq.Unmarshal(m, b)
}
func (m *InsertActivitiesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertActivitiesReq.Marshal(b, m, deterministic)
}
func (dst *InsertActivitiesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertActivitiesReq.Merge(dst, src)
}
func (m *InsertActivitiesReq) XXX_Size() int {
	return xxx_messageInfo_InsertActivitiesReq.Size(m)
}
func (m *InsertActivitiesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertActivitiesReq.DiscardUnknown(m)
}

var xxx_messageInfo_InsertActivitiesReq proto.InternalMessageInfo

func (m *InsertActivitiesReq) GetFeeds() *Feeds {
	if m != nil {
		return m.Feeds
	}
	return nil
}

func (m *InsertActivitiesReq) GetActivities() []*Activity {
	if m != nil {
		return m.Activities
	}
	return nil
}

func (m *InsertActivitiesReq) GetConfilctPolicy() uint32 {
	if m != nil {
		return m.ConfilctPolicy
	}
	return 0
}

type InsertActivitiesResp struct {
	RecordsAffected      int64    `protobuf:"varint,1,opt,name=records_affected,json=recordsAffected,proto3" json:"records_affected,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsertActivitiesResp) Reset()         { *m = InsertActivitiesResp{} }
func (m *InsertActivitiesResp) String() string { return proto.CompactTextString(m) }
func (*InsertActivitiesResp) ProtoMessage()    {}
func (*InsertActivitiesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{56}
}
func (m *InsertActivitiesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertActivitiesResp.Unmarshal(m, b)
}
func (m *InsertActivitiesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertActivitiesResp.Marshal(b, m, deterministic)
}
func (dst *InsertActivitiesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertActivitiesResp.Merge(dst, src)
}
func (m *InsertActivitiesResp) XXX_Size() int {
	return xxx_messageInfo_InsertActivitiesResp.Size(m)
}
func (m *InsertActivitiesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertActivitiesResp.DiscardUnknown(m)
}

var xxx_messageInfo_InsertActivitiesResp proto.InternalMessageInfo

func (m *InsertActivitiesResp) GetRecordsAffected() int64 {
	if m != nil {
		return m.RecordsAffected
	}
	return 0
}

// feed_id format: <group>-<id>
type Feed struct {
	Group                string   `protobuf:"bytes,1,opt,name=group,proto3" json:"group,omitempty"`
	Id                   string   `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Feed) Reset()         { *m = Feed{} }
func (m *Feed) String() string { return proto.CompactTextString(m) }
func (*Feed) ProtoMessage()    {}
func (*Feed) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{57}
}
func (m *Feed) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Feed.Unmarshal(m, b)
}
func (m *Feed) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Feed.Marshal(b, m, deterministic)
}
func (dst *Feed) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Feed.Merge(dst, src)
}
func (m *Feed) XXX_Size() int {
	return xxx_messageInfo_Feed.Size(m)
}
func (m *Feed) XXX_DiscardUnknown() {
	xxx_messageInfo_Feed.DiscardUnknown(m)
}

var xxx_messageInfo_Feed proto.InternalMessageInfo

func (m *Feed) GetGroup() string {
	if m != nil {
		return m.Group
	}
	return ""
}

func (m *Feed) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type Feeds struct {
	Group                string   `protobuf:"bytes,1,opt,name=group,proto3" json:"group,omitempty"`
	IdList               []string `protobuf:"bytes,2,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Feeds) Reset()         { *m = Feeds{} }
func (m *Feeds) String() string { return proto.CompactTextString(m) }
func (*Feeds) ProtoMessage()    {}
func (*Feeds) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{58}
}
func (m *Feeds) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Feeds.Unmarshal(m, b)
}
func (m *Feeds) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Feeds.Marshal(b, m, deterministic)
}
func (dst *Feeds) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Feeds.Merge(dst, src)
}
func (m *Feeds) XXX_Size() int {
	return xxx_messageInfo_Feeds.Size(m)
}
func (m *Feeds) XXX_DiscardUnknown() {
	xxx_messageInfo_Feeds.DiscardUnknown(m)
}

var xxx_messageInfo_Feeds proto.InternalMessageInfo

func (m *Feeds) GetGroup() string {
	if m != nil {
		return m.Group
	}
	return ""
}

func (m *Feeds) GetIdList() []string {
	if m != nil {
		return m.IdList
	}
	return nil
}

type LikeMsg struct {
	PostObjectId         string   `protobuf:"bytes,1,opt,name=post_object_id,json=postObjectId,proto3" json:"post_object_id,omitempty"`
	CommentObjectId      string   `protobuf:"bytes,2,opt,name=comment_object_id,json=commentObjectId,proto3" json:"comment_object_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LikeMsg) Reset()         { *m = LikeMsg{} }
func (m *LikeMsg) String() string { return proto.CompactTextString(m) }
func (*LikeMsg) ProtoMessage()    {}
func (*LikeMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{59}
}
func (m *LikeMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LikeMsg.Unmarshal(m, b)
}
func (m *LikeMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LikeMsg.Marshal(b, m, deterministic)
}
func (dst *LikeMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LikeMsg.Merge(dst, src)
}
func (m *LikeMsg) XXX_Size() int {
	return xxx_messageInfo_LikeMsg.Size(m)
}
func (m *LikeMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_LikeMsg.DiscardUnknown(m)
}

var xxx_messageInfo_LikeMsg proto.InternalMessageInfo

func (m *LikeMsg) GetPostObjectId() string {
	if m != nil {
		return m.PostObjectId
	}
	return ""
}

func (m *LikeMsg) GetCommentObjectId() string {
	if m != nil {
		return m.CommentObjectId
	}
	return ""
}

type CommentMsg struct {
	PostObjectId         string   `protobuf:"bytes,1,opt,name=post_object_id,json=postObjectId,proto3" json:"post_object_id,omitempty"`
	CommentObjectId      string   `protobuf:"bytes,2,opt,name=comment_object_id,json=commentObjectId,proto3" json:"comment_object_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommentMsg) Reset()         { *m = CommentMsg{} }
func (m *CommentMsg) String() string { return proto.CompactTextString(m) }
func (*CommentMsg) ProtoMessage()    {}
func (*CommentMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{60}
}
func (m *CommentMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentMsg.Unmarshal(m, b)
}
func (m *CommentMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentMsg.Marshal(b, m, deterministic)
}
func (dst *CommentMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentMsg.Merge(dst, src)
}
func (m *CommentMsg) XXX_Size() int {
	return xxx_messageInfo_CommentMsg.Size(m)
}
func (m *CommentMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentMsg.DiscardUnknown(m)
}

var xxx_messageInfo_CommentMsg proto.InternalMessageInfo

func (m *CommentMsg) GetPostObjectId() string {
	if m != nil {
		return m.PostObjectId
	}
	return ""
}

func (m *CommentMsg) GetCommentObjectId() string {
	if m != nil {
		return m.CommentObjectId
	}
	return ""
}

// 获取评论的二级评论总数
type BatGetSubCommentCountRequest struct {
	ParentCommentIdList  []string `protobuf:"bytes,1,rep,name=parent_comment_id_list,json=parentCommentIdList,proto3" json:"parent_comment_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetSubCommentCountRequest) Reset()         { *m = BatGetSubCommentCountRequest{} }
func (m *BatGetSubCommentCountRequest) String() string { return proto.CompactTextString(m) }
func (*BatGetSubCommentCountRequest) ProtoMessage()    {}
func (*BatGetSubCommentCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{61}
}
func (m *BatGetSubCommentCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetSubCommentCountRequest.Unmarshal(m, b)
}
func (m *BatGetSubCommentCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetSubCommentCountRequest.Marshal(b, m, deterministic)
}
func (dst *BatGetSubCommentCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetSubCommentCountRequest.Merge(dst, src)
}
func (m *BatGetSubCommentCountRequest) XXX_Size() int {
	return xxx_messageInfo_BatGetSubCommentCountRequest.Size(m)
}
func (m *BatGetSubCommentCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetSubCommentCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetSubCommentCountRequest proto.InternalMessageInfo

func (m *BatGetSubCommentCountRequest) GetParentCommentIdList() []string {
	if m != nil {
		return m.ParentCommentIdList
	}
	return nil
}

type BatGetSubCommentCountResponse struct {
	CommentCountMap      map[string]uint32 `protobuf:"bytes,1,rep,name=comment_count_map,json=commentCountMap,proto3" json:"comment_count_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatGetSubCommentCountResponse) Reset()         { *m = BatGetSubCommentCountResponse{} }
func (m *BatGetSubCommentCountResponse) String() string { return proto.CompactTextString(m) }
func (*BatGetSubCommentCountResponse) ProtoMessage()    {}
func (*BatGetSubCommentCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_6f7f42c83473ec6d, []int{62}
}
func (m *BatGetSubCommentCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetSubCommentCountResponse.Unmarshal(m, b)
}
func (m *BatGetSubCommentCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetSubCommentCountResponse.Marshal(b, m, deterministic)
}
func (dst *BatGetSubCommentCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetSubCommentCountResponse.Merge(dst, src)
}
func (m *BatGetSubCommentCountResponse) XXX_Size() int {
	return xxx_messageInfo_BatGetSubCommentCountResponse.Size(m)
}
func (m *BatGetSubCommentCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetSubCommentCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetSubCommentCountResponse proto.InternalMessageInfo

func (m *BatGetSubCommentCountResponse) GetCommentCountMap() map[string]uint32 {
	if m != nil {
		return m.CommentCountMap
	}
	return nil
}

func init() {
	proto.RegisterType((*GetInteractiveMsgHistoryReq)(nil), "musepost.GetInteractiveMsgHistoryReq")
	proto.RegisterType((*GetInteractiveMsgHistoryResp)(nil), "musepost.GetInteractiveMsgHistoryResp")
	proto.RegisterType((*DeleteContentReq)(nil), "musepost.DeleteContentReq")
	proto.RegisterType((*DeleteContentResp)(nil), "musepost.DeleteContentResp")
	proto.RegisterType((*UpdatePostStatusReq)(nil), "musepost.UpdatePostStatusReq")
	proto.RegisterType((*UpdatePostStatusResp)(nil), "musepost.UpdatePostStatusResp")
	proto.RegisterType((*UpdateCommentStatusReq)(nil), "musepost.UpdateCommentStatusReq")
	proto.RegisterType((*UpdateCommentStatusResp)(nil), "musepost.UpdateCommentStatusResp")
	proto.RegisterType((*AddLikeContentHistoryReq)(nil), "musepost.AddLikeContentHistoryReq")
	proto.RegisterType((*AddLikeContentHistoryResp)(nil), "musepost.AddLikeContentHistoryResp")
	proto.RegisterType((*DeleteLikeContentHistoryReq)(nil), "musepost.DeleteLikeContentHistoryReq")
	proto.RegisterType((*DeleteLikeContentHistoryResp)(nil), "musepost.DeleteLikeContentHistoryResp")
	proto.RegisterType((*GetLikeContentHistoryReq)(nil), "musepost.GetLikeContentHistoryReq")
	proto.RegisterType((*GetLikeContentHistoryResp)(nil), "musepost.GetLikeContentHistoryResp")
	proto.RegisterMapType((map[string]bool)(nil), "musepost.GetLikeContentHistoryResp.HistoryEntry")
	proto.RegisterType((*MarkInteractiveMsgReadReq)(nil), "musepost.MarkInteractiveMsgReadReq")
	proto.RegisterType((*MarkInteractiveMsgReadResp)(nil), "musepost.MarkInteractiveMsgReadResp")
	proto.RegisterType((*MarkPostReadReq)(nil), "musepost.MarkPostReadReq")
	proto.RegisterType((*MarkPostReadResp)(nil), "musepost.MarkPostReadResp")
	proto.RegisterType((*IncrInteractiveMsgUnreadCountReq)(nil), "musepost.IncrInteractiveMsgUnreadCountReq")
	proto.RegisterType((*IncrInteractiveMsgUnreadCountResp)(nil), "musepost.IncrInteractiveMsgUnreadCountResp")
	proto.RegisterType((*PublishPostReq)(nil), "musepost.PublishPostReq")
	proto.RegisterType((*PublishPostResp)(nil), "musepost.PublishPostResp")
	proto.RegisterType((*PublishCommentReq)(nil), "musepost.PublishCommentReq")
	proto.RegisterType((*PublishCommentResp)(nil), "musepost.PublishCommentResp")
	proto.RegisterType((*IncrLikeCountReq)(nil), "musepost.IncrLikeCountReq")
	proto.RegisterType((*IncrLikeCountResp)(nil), "musepost.IncrLikeCountResp")
	proto.RegisterType((*DescLikeCountReq)(nil), "musepost.DescLikeCountReq")
	proto.RegisterType((*DescLikeCountResp)(nil), "musepost.DescLikeCountResp")
	proto.RegisterType((*IncrCommentCountReq)(nil), "musepost.IncrCommentCountReq")
	proto.RegisterType((*IncrCommentCountResp)(nil), "musepost.IncrCommentCountResp")
	proto.RegisterType((*DescCommentCountReq)(nil), "musepost.DescCommentCountReq")
	proto.RegisterType((*DescCommentCountResp)(nil), "musepost.DescCommentCountResp")
	proto.RegisterType((*GetInteractiveContentCountReq)(nil), "musepost.GetInteractiveContentCountReq")
	proto.RegisterType((*GetInteractiveContentCountResp)(nil), "musepost.GetInteractiveContentCountResp")
	proto.RegisterMapType((map[string]uint32)(nil), "musepost.GetInteractiveContentCountResp.CommentCountEntry")
	proto.RegisterMapType((map[string]uint32)(nil), "musepost.GetInteractiveContentCountResp.LikeCountEntry")
	proto.RegisterType((*GetPostByIdsReq)(nil), "musepost.GetPostByIdsReq")
	proto.RegisterType((*GetPostByIdsResp)(nil), "musepost.GetPostByIdsResp")
	proto.RegisterType((*GetCommentByIdsReq)(nil), "musepost.GetCommentByIdsReq")
	proto.RegisterType((*GetCommentByIdsResp)(nil), "musepost.GetCommentByIdsResp")
	proto.RegisterType((*MusePost)(nil), "musepost.MusePost")
	proto.RegisterType((*MusePostUserMoodInfo)(nil), "musepost.MusePostUserMoodInfo")
	proto.RegisterType((*MuseComment)(nil), "musepost.MuseComment")
	proto.RegisterType((*TrimActivitiesByCapacityReq)(nil), "musepost.TrimActivitiesByCapacityReq")
	proto.RegisterType((*TrimActivitiesByCapacityResp)(nil), "musepost.TrimActivitiesByCapacityResp")
	proto.RegisterType((*IsActivityKeysExistingReq)(nil), "musepost.IsActivityKeysExistingReq")
	proto.RegisterType((*IsActivityKeysExistResp)(nil), "musepost.IsActivityKeysExistResp")
	proto.RegisterMapType((map[string]bool)(nil), "musepost.IsActivityKeysExistResp.ExistMapEntry")
	proto.RegisterType((*RemoveActivitiesReq)(nil), "musepost.RemoveActivitiesReq")
	proto.RegisterType((*RemoveActivitiesResp)(nil), "musepost.RemoveActivitiesResp")
	proto.RegisterType((*GetActivitiesByRangeReq)(nil), "musepost.GetActivitiesByRangeReq")
	proto.RegisterType((*ActivityKey)(nil), "musepost.ActivityKey")
	proto.RegisterType((*ActivityKeys)(nil), "musepost.ActivityKeys")
	proto.RegisterType((*QueryOptions)(nil), "musepost.QueryOptions")
	proto.RegisterType((*QueryOptions_CustomScoreRange)(nil), "musepost.QueryOptions.CustomScoreRange")
	proto.RegisterType((*QueryOptions_ActivityKeyRange)(nil), "musepost.QueryOptions.ActivityKeyRange")
	proto.RegisterType((*GetActivitiesByRangeResp)(nil), "musepost.GetActivitiesByRangeResp")
	proto.RegisterType((*BatchGetActivitiesByRangeReq)(nil), "musepost.BatchGetActivitiesByRangeReq")
	proto.RegisterType((*BatchGetActivitiesByRangeResp)(nil), "musepost.BatchGetActivitiesByRangeResp")
	proto.RegisterType((*BatchGetActivitiesByRangeResp_FeedActivities)(nil), "musepost.BatchGetActivitiesByRangeResp.FeedActivities")
	proto.RegisterType((*Activity)(nil), "musepost.Activity")
	proto.RegisterType((*InsertActivitiesReq)(nil), "musepost.InsertActivitiesReq")
	proto.RegisterType((*InsertActivitiesResp)(nil), "musepost.InsertActivitiesResp")
	proto.RegisterType((*Feed)(nil), "musepost.Feed")
	proto.RegisterType((*Feeds)(nil), "musepost.Feeds")
	proto.RegisterType((*LikeMsg)(nil), "musepost.LikeMsg")
	proto.RegisterType((*CommentMsg)(nil), "musepost.CommentMsg")
	proto.RegisterType((*BatGetSubCommentCountRequest)(nil), "musepost.BatGetSubCommentCountRequest")
	proto.RegisterType((*BatGetSubCommentCountResponse)(nil), "musepost.BatGetSubCommentCountResponse")
	proto.RegisterMapType((map[string]uint32)(nil), "musepost.BatGetSubCommentCountResponse.CommentCountMapEntry")
	proto.RegisterEnum("musepost.MuseContentType", MuseContentType_name, MuseContentType_value)
	proto.RegisterEnum("musepost.ContentStatus", ContentStatus_name, ContentStatus_value)
	proto.RegisterEnum("musepost.ConflictPolicy", ConflictPolicy_name, ConflictPolicy_value)
	proto.RegisterEnum("musepost.MuseInteractiveMsgType", MuseInteractiveMsgType_name, MuseInteractiveMsgType_value)
	proto.RegisterEnum("musepost.QueryOptions_Sort", QueryOptions_Sort_name, QueryOptions_Sort_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MuseContentSvrClient is the client API for MuseContentSvr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MuseContentSvrClient interface {
	// ugc
	GetPostByIds(ctx context.Context, in *GetPostByIdsReq, opts ...grpc.CallOption) (*GetPostByIdsResp, error)
	GetCommentByIds(ctx context.Context, in *GetCommentByIdsReq, opts ...grpc.CallOption) (*GetCommentByIdsResp, error)
	PublishPost(ctx context.Context, in *PublishPostReq, opts ...grpc.CallOption) (*PublishPostResp, error)
	PublishComment(ctx context.Context, in *PublishCommentReq, opts ...grpc.CallOption) (*PublishCommentResp, error)
	DeleteContent(ctx context.Context, in *DeleteContentReq, opts ...grpc.CallOption) (*DeleteContentResp, error)
	UpdatePostStatus(ctx context.Context, in *UpdatePostStatusReq, opts ...grpc.CallOption) (*UpdatePostStatusResp, error)
	UpdateCommentStatus(ctx context.Context, in *UpdateCommentStatusReq, opts ...grpc.CallOption) (*UpdateCommentStatusResp, error)
	// 互动历史
	AddLikeContentHistory(ctx context.Context, in *AddLikeContentHistoryReq, opts ...grpc.CallOption) (*AddLikeContentHistoryResp, error)
	DeleteLikeContentHistory(ctx context.Context, in *DeleteLikeContentHistoryReq, opts ...grpc.CallOption) (*DeleteLikeContentHistoryResp, error)
	GetLikeContentHistory(ctx context.Context, in *GetLikeContentHistoryReq, opts ...grpc.CallOption) (*GetLikeContentHistoryResp, error)
	// 互动数量
	IncrLikeCount(ctx context.Context, in *IncrLikeCountReq, opts ...grpc.CallOption) (*IncrLikeCountResp, error)
	DescLikeCount(ctx context.Context, in *DescLikeCountReq, opts ...grpc.CallOption) (*DescLikeCountResp, error)
	IncrCommentCount(ctx context.Context, in *IncrCommentCountReq, opts ...grpc.CallOption) (*IncrCommentCountResp, error)
	DescCommentCount(ctx context.Context, in *DescCommentCountReq, opts ...grpc.CallOption) (*DescCommentCountResp, error)
	GetInteractiveContentCount(ctx context.Context, in *GetInteractiveContentCountReq, opts ...grpc.CallOption) (*GetInteractiveContentCountResp, error)
	GetInteractiveMsgHistory(ctx context.Context, in *GetInteractiveMsgHistoryReq, opts ...grpc.CallOption) (*GetInteractiveMsgHistoryResp, error)
	MarkInteractiveMsgRead(ctx context.Context, in *MarkInteractiveMsgReadReq, opts ...grpc.CallOption) (*MarkInteractiveMsgReadResp, error)
	MarkPostRead(ctx context.Context, in *MarkPostReadReq, opts ...grpc.CallOption) (*MarkPostReadResp, error)
	IncrInteractiveMsgUnreadCount(ctx context.Context, in *IncrInteractiveMsgUnreadCountReq, opts ...grpc.CallOption) (*IncrInteractiveMsgUnreadCountResp, error)
	// 获取评论的二级评论总数
	BatGetSubCommentCount(ctx context.Context, in *BatGetSubCommentCountRequest, opts ...grpc.CallOption) (*BatGetSubCommentCountResponse, error)
}

type museContentSvrClient struct {
	cc *grpc.ClientConn
}

func NewMuseContentSvrClient(cc *grpc.ClientConn) MuseContentSvrClient {
	return &museContentSvrClient{cc}
}

func (c *museContentSvrClient) GetPostByIds(ctx context.Context, in *GetPostByIdsReq, opts ...grpc.CallOption) (*GetPostByIdsResp, error) {
	out := new(GetPostByIdsResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/GetPostByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museContentSvrClient) GetCommentByIds(ctx context.Context, in *GetCommentByIdsReq, opts ...grpc.CallOption) (*GetCommentByIdsResp, error) {
	out := new(GetCommentByIdsResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/GetCommentByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museContentSvrClient) PublishPost(ctx context.Context, in *PublishPostReq, opts ...grpc.CallOption) (*PublishPostResp, error) {
	out := new(PublishPostResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/PublishPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museContentSvrClient) PublishComment(ctx context.Context, in *PublishCommentReq, opts ...grpc.CallOption) (*PublishCommentResp, error) {
	out := new(PublishCommentResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/PublishComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museContentSvrClient) DeleteContent(ctx context.Context, in *DeleteContentReq, opts ...grpc.CallOption) (*DeleteContentResp, error) {
	out := new(DeleteContentResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/DeleteContent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museContentSvrClient) UpdatePostStatus(ctx context.Context, in *UpdatePostStatusReq, opts ...grpc.CallOption) (*UpdatePostStatusResp, error) {
	out := new(UpdatePostStatusResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/UpdatePostStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museContentSvrClient) UpdateCommentStatus(ctx context.Context, in *UpdateCommentStatusReq, opts ...grpc.CallOption) (*UpdateCommentStatusResp, error) {
	out := new(UpdateCommentStatusResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/UpdateCommentStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museContentSvrClient) AddLikeContentHistory(ctx context.Context, in *AddLikeContentHistoryReq, opts ...grpc.CallOption) (*AddLikeContentHistoryResp, error) {
	out := new(AddLikeContentHistoryResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/AddLikeContentHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museContentSvrClient) DeleteLikeContentHistory(ctx context.Context, in *DeleteLikeContentHistoryReq, opts ...grpc.CallOption) (*DeleteLikeContentHistoryResp, error) {
	out := new(DeleteLikeContentHistoryResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/DeleteLikeContentHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museContentSvrClient) GetLikeContentHistory(ctx context.Context, in *GetLikeContentHistoryReq, opts ...grpc.CallOption) (*GetLikeContentHistoryResp, error) {
	out := new(GetLikeContentHistoryResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/GetLikeContentHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museContentSvrClient) IncrLikeCount(ctx context.Context, in *IncrLikeCountReq, opts ...grpc.CallOption) (*IncrLikeCountResp, error) {
	out := new(IncrLikeCountResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/IncrLikeCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museContentSvrClient) DescLikeCount(ctx context.Context, in *DescLikeCountReq, opts ...grpc.CallOption) (*DescLikeCountResp, error) {
	out := new(DescLikeCountResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/DescLikeCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museContentSvrClient) IncrCommentCount(ctx context.Context, in *IncrCommentCountReq, opts ...grpc.CallOption) (*IncrCommentCountResp, error) {
	out := new(IncrCommentCountResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/IncrCommentCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museContentSvrClient) DescCommentCount(ctx context.Context, in *DescCommentCountReq, opts ...grpc.CallOption) (*DescCommentCountResp, error) {
	out := new(DescCommentCountResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/DescCommentCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museContentSvrClient) GetInteractiveContentCount(ctx context.Context, in *GetInteractiveContentCountReq, opts ...grpc.CallOption) (*GetInteractiveContentCountResp, error) {
	out := new(GetInteractiveContentCountResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/GetInteractiveContentCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museContentSvrClient) GetInteractiveMsgHistory(ctx context.Context, in *GetInteractiveMsgHistoryReq, opts ...grpc.CallOption) (*GetInteractiveMsgHistoryResp, error) {
	out := new(GetInteractiveMsgHistoryResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/GetInteractiveMsgHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museContentSvrClient) MarkInteractiveMsgRead(ctx context.Context, in *MarkInteractiveMsgReadReq, opts ...grpc.CallOption) (*MarkInteractiveMsgReadResp, error) {
	out := new(MarkInteractiveMsgReadResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/MarkInteractiveMsgRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museContentSvrClient) MarkPostRead(ctx context.Context, in *MarkPostReadReq, opts ...grpc.CallOption) (*MarkPostReadResp, error) {
	out := new(MarkPostReadResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/MarkPostRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museContentSvrClient) IncrInteractiveMsgUnreadCount(ctx context.Context, in *IncrInteractiveMsgUnreadCountReq, opts ...grpc.CallOption) (*IncrInteractiveMsgUnreadCountResp, error) {
	out := new(IncrInteractiveMsgUnreadCountResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/IncrInteractiveMsgUnreadCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museContentSvrClient) BatGetSubCommentCount(ctx context.Context, in *BatGetSubCommentCountRequest, opts ...grpc.CallOption) (*BatGetSubCommentCountResponse, error) {
	out := new(BatGetSubCommentCountResponse)
	err := c.cc.Invoke(ctx, "/musepost.MuseContentSvr/BatGetSubCommentCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MuseContentSvrServer is the server API for MuseContentSvr service.
type MuseContentSvrServer interface {
	// ugc
	GetPostByIds(context.Context, *GetPostByIdsReq) (*GetPostByIdsResp, error)
	GetCommentByIds(context.Context, *GetCommentByIdsReq) (*GetCommentByIdsResp, error)
	PublishPost(context.Context, *PublishPostReq) (*PublishPostResp, error)
	PublishComment(context.Context, *PublishCommentReq) (*PublishCommentResp, error)
	DeleteContent(context.Context, *DeleteContentReq) (*DeleteContentResp, error)
	UpdatePostStatus(context.Context, *UpdatePostStatusReq) (*UpdatePostStatusResp, error)
	UpdateCommentStatus(context.Context, *UpdateCommentStatusReq) (*UpdateCommentStatusResp, error)
	// 互动历史
	AddLikeContentHistory(context.Context, *AddLikeContentHistoryReq) (*AddLikeContentHistoryResp, error)
	DeleteLikeContentHistory(context.Context, *DeleteLikeContentHistoryReq) (*DeleteLikeContentHistoryResp, error)
	GetLikeContentHistory(context.Context, *GetLikeContentHistoryReq) (*GetLikeContentHistoryResp, error)
	// 互动数量
	IncrLikeCount(context.Context, *IncrLikeCountReq) (*IncrLikeCountResp, error)
	DescLikeCount(context.Context, *DescLikeCountReq) (*DescLikeCountResp, error)
	IncrCommentCount(context.Context, *IncrCommentCountReq) (*IncrCommentCountResp, error)
	DescCommentCount(context.Context, *DescCommentCountReq) (*DescCommentCountResp, error)
	GetInteractiveContentCount(context.Context, *GetInteractiveContentCountReq) (*GetInteractiveContentCountResp, error)
	GetInteractiveMsgHistory(context.Context, *GetInteractiveMsgHistoryReq) (*GetInteractiveMsgHistoryResp, error)
	MarkInteractiveMsgRead(context.Context, *MarkInteractiveMsgReadReq) (*MarkInteractiveMsgReadResp, error)
	MarkPostRead(context.Context, *MarkPostReadReq) (*MarkPostReadResp, error)
	IncrInteractiveMsgUnreadCount(context.Context, *IncrInteractiveMsgUnreadCountReq) (*IncrInteractiveMsgUnreadCountResp, error)
	// 获取评论的二级评论总数
	BatGetSubCommentCount(context.Context, *BatGetSubCommentCountRequest) (*BatGetSubCommentCountResponse, error)
}

func RegisterMuseContentSvrServer(s *grpc.Server, srv MuseContentSvrServer) {
	s.RegisterService(&_MuseContentSvr_serviceDesc, srv)
}

func _MuseContentSvr_GetPostByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).GetPostByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/GetPostByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).GetPostByIds(ctx, req.(*GetPostByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseContentSvr_GetCommentByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommentByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).GetCommentByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/GetCommentByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).GetCommentByIds(ctx, req.(*GetCommentByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseContentSvr_PublishPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PublishPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).PublishPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/PublishPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).PublishPost(ctx, req.(*PublishPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseContentSvr_PublishComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PublishCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).PublishComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/PublishComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).PublishComment(ctx, req.(*PublishCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseContentSvr_DeleteContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteContentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).DeleteContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/DeleteContent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).DeleteContent(ctx, req.(*DeleteContentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseContentSvr_UpdatePostStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePostStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).UpdatePostStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/UpdatePostStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).UpdatePostStatus(ctx, req.(*UpdatePostStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseContentSvr_UpdateCommentStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCommentStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).UpdateCommentStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/UpdateCommentStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).UpdateCommentStatus(ctx, req.(*UpdateCommentStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseContentSvr_AddLikeContentHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddLikeContentHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).AddLikeContentHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/AddLikeContentHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).AddLikeContentHistory(ctx, req.(*AddLikeContentHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseContentSvr_DeleteLikeContentHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteLikeContentHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).DeleteLikeContentHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/DeleteLikeContentHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).DeleteLikeContentHistory(ctx, req.(*DeleteLikeContentHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseContentSvr_GetLikeContentHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLikeContentHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).GetLikeContentHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/GetLikeContentHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).GetLikeContentHistory(ctx, req.(*GetLikeContentHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseContentSvr_IncrLikeCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IncrLikeCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).IncrLikeCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/IncrLikeCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).IncrLikeCount(ctx, req.(*IncrLikeCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseContentSvr_DescLikeCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescLikeCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).DescLikeCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/DescLikeCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).DescLikeCount(ctx, req.(*DescLikeCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseContentSvr_IncrCommentCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IncrCommentCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).IncrCommentCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/IncrCommentCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).IncrCommentCount(ctx, req.(*IncrCommentCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseContentSvr_DescCommentCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescCommentCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).DescCommentCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/DescCommentCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).DescCommentCount(ctx, req.(*DescCommentCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseContentSvr_GetInteractiveContentCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInteractiveContentCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).GetInteractiveContentCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/GetInteractiveContentCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).GetInteractiveContentCount(ctx, req.(*GetInteractiveContentCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseContentSvr_GetInteractiveMsgHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInteractiveMsgHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).GetInteractiveMsgHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/GetInteractiveMsgHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).GetInteractiveMsgHistory(ctx, req.(*GetInteractiveMsgHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseContentSvr_MarkInteractiveMsgRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkInteractiveMsgReadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).MarkInteractiveMsgRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/MarkInteractiveMsgRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).MarkInteractiveMsgRead(ctx, req.(*MarkInteractiveMsgReadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseContentSvr_MarkPostRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkPostReadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).MarkPostRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/MarkPostRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).MarkPostRead(ctx, req.(*MarkPostReadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseContentSvr_IncrInteractiveMsgUnreadCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IncrInteractiveMsgUnreadCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).IncrInteractiveMsgUnreadCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/IncrInteractiveMsgUnreadCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).IncrInteractiveMsgUnreadCount(ctx, req.(*IncrInteractiveMsgUnreadCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseContentSvr_BatGetSubCommentCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetSubCommentCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseContentSvrServer).BatGetSubCommentCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseContentSvr/BatGetSubCommentCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseContentSvrServer).BatGetSubCommentCount(ctx, req.(*BatGetSubCommentCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _MuseContentSvr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "musepost.MuseContentSvr",
	HandlerType: (*MuseContentSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPostByIds",
			Handler:    _MuseContentSvr_GetPostByIds_Handler,
		},
		{
			MethodName: "GetCommentByIds",
			Handler:    _MuseContentSvr_GetCommentByIds_Handler,
		},
		{
			MethodName: "PublishPost",
			Handler:    _MuseContentSvr_PublishPost_Handler,
		},
		{
			MethodName: "PublishComment",
			Handler:    _MuseContentSvr_PublishComment_Handler,
		},
		{
			MethodName: "DeleteContent",
			Handler:    _MuseContentSvr_DeleteContent_Handler,
		},
		{
			MethodName: "UpdatePostStatus",
			Handler:    _MuseContentSvr_UpdatePostStatus_Handler,
		},
		{
			MethodName: "UpdateCommentStatus",
			Handler:    _MuseContentSvr_UpdateCommentStatus_Handler,
		},
		{
			MethodName: "AddLikeContentHistory",
			Handler:    _MuseContentSvr_AddLikeContentHistory_Handler,
		},
		{
			MethodName: "DeleteLikeContentHistory",
			Handler:    _MuseContentSvr_DeleteLikeContentHistory_Handler,
		},
		{
			MethodName: "GetLikeContentHistory",
			Handler:    _MuseContentSvr_GetLikeContentHistory_Handler,
		},
		{
			MethodName: "IncrLikeCount",
			Handler:    _MuseContentSvr_IncrLikeCount_Handler,
		},
		{
			MethodName: "DescLikeCount",
			Handler:    _MuseContentSvr_DescLikeCount_Handler,
		},
		{
			MethodName: "IncrCommentCount",
			Handler:    _MuseContentSvr_IncrCommentCount_Handler,
		},
		{
			MethodName: "DescCommentCount",
			Handler:    _MuseContentSvr_DescCommentCount_Handler,
		},
		{
			MethodName: "GetInteractiveContentCount",
			Handler:    _MuseContentSvr_GetInteractiveContentCount_Handler,
		},
		{
			MethodName: "GetInteractiveMsgHistory",
			Handler:    _MuseContentSvr_GetInteractiveMsgHistory_Handler,
		},
		{
			MethodName: "MarkInteractiveMsgRead",
			Handler:    _MuseContentSvr_MarkInteractiveMsgRead_Handler,
		},
		{
			MethodName: "MarkPostRead",
			Handler:    _MuseContentSvr_MarkPostRead_Handler,
		},
		{
			MethodName: "IncrInteractiveMsgUnreadCount",
			Handler:    _MuseContentSvr_IncrInteractiveMsgUnreadCount_Handler,
		},
		{
			MethodName: "BatGetSubCommentCount",
			Handler:    _MuseContentSvr_BatGetSubCommentCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "muse-post/muse-post.proto",
}

// MuseStreamSvrClient is the client API for MuseStreamSvr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MuseStreamSvrClient interface {
	// 帖子流、评论流、点赞流
	InsertActivities(ctx context.Context, in *InsertActivitiesReq, opts ...grpc.CallOption) (*InsertActivitiesResp, error)
	GetActivitiesByRange(ctx context.Context, in *GetActivitiesByRangeReq, opts ...grpc.CallOption) (*GetActivitiesByRangeResp, error)
	RemoveActivities(ctx context.Context, in *RemoveActivitiesReq, opts ...grpc.CallOption) (*RemoveActivitiesResp, error)
	IsActivityKeysExisting(ctx context.Context, in *IsActivityKeysExistingReq, opts ...grpc.CallOption) (*IsActivityKeysExistResp, error)
	TrimActivitiesByCapacity(ctx context.Context, in *TrimActivitiesByCapacityReq, opts ...grpc.CallOption) (*TrimActivitiesByCapacityResp, error)
	BatchGetActivitiesByRange(ctx context.Context, in *BatchGetActivitiesByRangeReq, opts ...grpc.CallOption) (*BatchGetActivitiesByRangeResp, error)
}

type museStreamSvrClient struct {
	cc *grpc.ClientConn
}

func NewMuseStreamSvrClient(cc *grpc.ClientConn) MuseStreamSvrClient {
	return &museStreamSvrClient{cc}
}

func (c *museStreamSvrClient) InsertActivities(ctx context.Context, in *InsertActivitiesReq, opts ...grpc.CallOption) (*InsertActivitiesResp, error) {
	out := new(InsertActivitiesResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseStreamSvr/InsertActivities", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museStreamSvrClient) GetActivitiesByRange(ctx context.Context, in *GetActivitiesByRangeReq, opts ...grpc.CallOption) (*GetActivitiesByRangeResp, error) {
	out := new(GetActivitiesByRangeResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseStreamSvr/GetActivitiesByRange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museStreamSvrClient) RemoveActivities(ctx context.Context, in *RemoveActivitiesReq, opts ...grpc.CallOption) (*RemoveActivitiesResp, error) {
	out := new(RemoveActivitiesResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseStreamSvr/RemoveActivities", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museStreamSvrClient) IsActivityKeysExisting(ctx context.Context, in *IsActivityKeysExistingReq, opts ...grpc.CallOption) (*IsActivityKeysExistResp, error) {
	out := new(IsActivityKeysExistResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseStreamSvr/IsActivityKeysExisting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museStreamSvrClient) TrimActivitiesByCapacity(ctx context.Context, in *TrimActivitiesByCapacityReq, opts ...grpc.CallOption) (*TrimActivitiesByCapacityResp, error) {
	out := new(TrimActivitiesByCapacityResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseStreamSvr/TrimActivitiesByCapacity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museStreamSvrClient) BatchGetActivitiesByRange(ctx context.Context, in *BatchGetActivitiesByRangeReq, opts ...grpc.CallOption) (*BatchGetActivitiesByRangeResp, error) {
	out := new(BatchGetActivitiesByRangeResp)
	err := c.cc.Invoke(ctx, "/musepost.MuseStreamSvr/BatchGetActivitiesByRange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MuseStreamSvrServer is the server API for MuseStreamSvr service.
type MuseStreamSvrServer interface {
	// 帖子流、评论流、点赞流
	InsertActivities(context.Context, *InsertActivitiesReq) (*InsertActivitiesResp, error)
	GetActivitiesByRange(context.Context, *GetActivitiesByRangeReq) (*GetActivitiesByRangeResp, error)
	RemoveActivities(context.Context, *RemoveActivitiesReq) (*RemoveActivitiesResp, error)
	IsActivityKeysExisting(context.Context, *IsActivityKeysExistingReq) (*IsActivityKeysExistResp, error)
	TrimActivitiesByCapacity(context.Context, *TrimActivitiesByCapacityReq) (*TrimActivitiesByCapacityResp, error)
	BatchGetActivitiesByRange(context.Context, *BatchGetActivitiesByRangeReq) (*BatchGetActivitiesByRangeResp, error)
}

func RegisterMuseStreamSvrServer(s *grpc.Server, srv MuseStreamSvrServer) {
	s.RegisterService(&_MuseStreamSvr_serviceDesc, srv)
}

func _MuseStreamSvr_InsertActivities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsertActivitiesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseStreamSvrServer).InsertActivities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseStreamSvr/InsertActivities",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseStreamSvrServer).InsertActivities(ctx, req.(*InsertActivitiesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseStreamSvr_GetActivitiesByRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActivitiesByRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseStreamSvrServer).GetActivitiesByRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseStreamSvr/GetActivitiesByRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseStreamSvrServer).GetActivitiesByRange(ctx, req.(*GetActivitiesByRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseStreamSvr_RemoveActivities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveActivitiesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseStreamSvrServer).RemoveActivities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseStreamSvr/RemoveActivities",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseStreamSvrServer).RemoveActivities(ctx, req.(*RemoveActivitiesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseStreamSvr_IsActivityKeysExisting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsActivityKeysExistingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseStreamSvrServer).IsActivityKeysExisting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseStreamSvr/IsActivityKeysExisting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseStreamSvrServer).IsActivityKeysExisting(ctx, req.(*IsActivityKeysExistingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseStreamSvr_TrimActivitiesByCapacity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TrimActivitiesByCapacityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseStreamSvrServer).TrimActivitiesByCapacity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseStreamSvr/TrimActivitiesByCapacity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseStreamSvrServer).TrimActivitiesByCapacity(ctx, req.(*TrimActivitiesByCapacityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseStreamSvr_BatchGetActivitiesByRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetActivitiesByRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseStreamSvrServer).BatchGetActivitiesByRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/musepost.MuseStreamSvr/BatchGetActivitiesByRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseStreamSvrServer).BatchGetActivitiesByRange(ctx, req.(*BatchGetActivitiesByRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MuseStreamSvr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "musepost.MuseStreamSvr",
	HandlerType: (*MuseStreamSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "InsertActivities",
			Handler:    _MuseStreamSvr_InsertActivities_Handler,
		},
		{
			MethodName: "GetActivitiesByRange",
			Handler:    _MuseStreamSvr_GetActivitiesByRange_Handler,
		},
		{
			MethodName: "RemoveActivities",
			Handler:    _MuseStreamSvr_RemoveActivities_Handler,
		},
		{
			MethodName: "IsActivityKeysExisting",
			Handler:    _MuseStreamSvr_IsActivityKeysExisting_Handler,
		},
		{
			MethodName: "TrimActivitiesByCapacity",
			Handler:    _MuseStreamSvr_TrimActivitiesByCapacity_Handler,
		},
		{
			MethodName: "BatchGetActivitiesByRange",
			Handler:    _MuseStreamSvr_BatchGetActivitiesByRange_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "muse-post/muse-post.proto",
}

func init() {
	proto.RegisterFile("muse-post/muse-post.proto", fileDescriptor_muse_post_6f7f42c83473ec6d)
}

var fileDescriptor_muse_post_6f7f42c83473ec6d = []byte{
	// 2862 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x3a, 0x4b, 0x73, 0xe3, 0xc6,
	0xd1, 0x04, 0x45, 0xbd, 0x5a, 0x14, 0xc5, 0x1d, 0xbd, 0x48, 0x4a, 0xbb, 0xd6, 0x62, 0xed, 0xdd,
	0xb5, 0xec, 0x4f, 0xf2, 0x27, 0xd7, 0x3a, 0xf6, 0x7a, 0xf3, 0xa0, 0x24, 0xae, 0x96, 0xb6, 0x1e,
	0x1b, 0x48, 0x4a, 0x5c, 0xb1, 0xcb, 0x28, 0x08, 0x18, 0x49, 0xf0, 0x92, 0x00, 0x16, 0x33, 0x94,
	0xcd, 0x1c, 0x93, 0x3f, 0x11, 0xdf, 0x52, 0x95, 0x5c, 0x72, 0xc9, 0x35, 0x3f, 0x21, 0xa7, 0x54,
	0x8e, 0x49, 0x2a, 0x3f, 0x21, 0x7f, 0x22, 0x35, 0x0f, 0x90, 0x03, 0x10, 0xa0, 0xc8, 0xad, 0xf2,
	0x25, 0x37, 0xcc, 0x74, 0x4f, 0x77, 0x4f, 0x4f, 0x77, 0x4f, 0x4f, 0x37, 0xa0, 0xda, 0xee, 0x10,
	0xfc, 0x7f, 0x81, 0x4f, 0xe8, 0x76, 0xef, 0x6b, 0x2b, 0x08, 0x7d, 0xea, 0xa3, 0x19, 0x36, 0xc1,
	0xc6, 0xfa, 0x31, 0xac, 0x1d, 0x60, 0xda, 0xf4, 0x28, 0x0e, 0x2d, 0x9b, 0xba, 0x37, 0xf8, 0x88,
	0x5c, 0xbd, 0x70, 0x09, 0xf5, 0xc3, 0xae, 0x81, 0x5f, 0xa3, 0x32, 0x4c, 0x74, 0x5c, 0xa7, 0xa2,
	0x6d, 0x68, 0x8f, 0xe7, 0x0d, 0xf6, 0x89, 0xee, 0x02, 0xd8, 0xd7, 0x96, 0xe7, 0xe1, 0x96, 0xe9,
	0x3a, 0x95, 0x3c, 0x07, 0xcc, 0xca, 0x99, 0xa6, 0xa3, 0xff, 0x5d, 0x83, 0xf5, 0x6c, 0x82, 0x24,
	0x40, 0xef, 0xc2, 0x9d, 0x96, 0x45, 0xa8, 0x19, 0x62, 0xcb, 0x31, 0x99, 0x08, 0xa6, 0xa4, 0x3f,
	0x6b, 0x94, 0x18, 0xc0, 0xc0, 0x96, 0xf3, 0xd2, 0x27, 0xb4, 0xe9, 0xa0, 0x4d, 0xb8, 0xd3, 0xf1,
	0x38, 0x5e, 0xcb, 0x7d, 0x85, 0x4d, 0xdb, 0xef, 0x78, 0x54, 0x72, 0x5c, 0x10, 0x80, 0x43, 0xf7,
	0x15, 0xde, 0x63, 0xd3, 0xe8, 0x03, 0x58, 0x92, 0xb8, 0xb6, 0xdf, 0x6e, 0x63, 0x8f, 0x4a, 0xf4,
	0x09, 0x8e, 0x8e, 0x04, 0x6c, 0x4f, 0x80, 0xc4, 0x8a, 0x87, 0x20, 0x89, 0x98, 0x56, 0x84, 0x5c,
	0xe0, 0xc8, 0xf3, 0x62, 0xba, 0x2e, 0xf0, 0xf4, 0xaf, 0xa0, 0xbc, 0x8f, 0x5b, 0x98, 0xe2, 0x3d,
	0xdf, 0xa3, 0xd8, 0xa3, 0xe9, 0x6a, 0x59, 0x85, 0xe9, 0x68, 0x33, 0x79, 0xbe, 0x99, 0xa9, 0x40,
	0x6c, 0x82, 0xe9, 0x4b, 0x4a, 0xe4, 0x3a, 0x5c, 0x9c, 0x59, 0x63, 0x56, 0xce, 0x34, 0x1d, 0x7d,
	0x11, 0xee, 0x24, 0xa8, 0x93, 0x40, 0x37, 0x61, 0xf1, 0x3c, 0x70, 0x2c, 0x8a, 0x99, 0x22, 0x4e,
	0xa9, 0x45, 0x3b, 0x84, 0x71, 0x55, 0x78, 0x68, 0x31, 0x1e, 0xdb, 0x30, 0x45, 0x38, 0x16, 0xe7,
	0x5d, 0xda, 0x59, 0xdd, 0x8a, 0xce, 0x77, 0x4b, 0x92, 0x95, 0x44, 0x24, 0x9a, 0xfe, 0x13, 0x58,
	0x1a, 0x64, 0x40, 0x02, 0xf4, 0x10, 0x0a, 0x6c, 0x15, 0x27, 0x3f, 0xb7, 0x83, 0xfa, 0x64, 0x8e,
	0x3a, 0x84, 0xe3, 0x1a, 0x1c, 0xae, 0x5f, 0xc3, 0x8a, 0x58, 0x2f, 0x35, 0xda, 0x97, 0x31, 0xbe,
	0x5d, 0x2d, 0xb1, 0xdd, 0xf1, 0x25, 0xfd, 0x0c, 0x56, 0x53, 0x39, 0x91, 0x00, 0x6d, 0xc3, 0xb4,
	0x24, 0x2c, 0xe5, 0x5d, 0x8e, 0xcb, 0x2b, 0x57, 0x18, 0x11, 0x96, 0xee, 0x40, 0xa5, 0xee, 0x48,
	0x9b, 0xe1, 0xbc, 0x86, 0x1a, 0xfa, 0x9b, 0x9e, 0xe8, 0x36, 0x54, 0x33, 0xb8, 0x90, 0x00, 0x21,
	0x28, 0x90, 0x8e, 0x6d, 0x73, 0x3e, 0x33, 0x06, 0xff, 0xd6, 0xaf, 0x60, 0x4d, 0x98, 0xc0, 0x0f,
	0x2d, 0xd9, 0x0e, 0xac, 0x67, 0x33, 0xca, 0x10, 0xee, 0x1a, 0x2a, 0x07, 0x98, 0x8e, 0x2a, 0x59,
	0x15, 0x66, 0xa4, 0x64, 0xec, 0x80, 0x27, 0x1e, 0xcf, 0x1a, 0xd3, 0x42, 0x34, 0x82, 0xde, 0x82,
	0xb9, 0xbe, 0x6c, 0xa4, 0x32, 0xc1, 0xa1, 0xd0, 0x13, 0x8e, 0xe8, 0x7f, 0xd0, 0xa0, 0x9a, 0xc1,
	0x8a, 0x04, 0xe8, 0x33, 0x98, 0xbe, 0x16, 0xc3, 0x8a, 0xb6, 0x31, 0xf1, 0x78, 0x6e, 0xe7, 0x83,
	0xfe, 0x61, 0x67, 0xae, 0xda, 0x92, 0xdf, 0x0d, 0x8f, 0x86, 0x5d, 0x23, 0x22, 0x50, 0x7b, 0x0a,
	0x45, 0x15, 0xc0, 0xf6, 0xf1, 0x0a, 0x77, 0xa5, 0xb1, 0xb2, 0x4f, 0xb4, 0x04, 0x93, 0x37, 0x56,
	0xab, 0x83, 0xb9, 0x7e, 0x67, 0x0c, 0x31, 0x78, 0x9a, 0xff, 0x58, 0xd3, 0x7f, 0xaf, 0x41, 0xf5,
	0xc8, 0x0a, 0x5f, 0xc5, 0x03, 0x1c, 0x0b, 0x5a, 0x91, 0xf5, 0xf7, 0x83, 0xa3, 0x96, 0x08, 0x8e,
	0x91, 0xc2, 0xf2, 0x7d, 0x85, 0x2d, 0xc3, 0x54, 0x9b, 0x5c, 0xf5, 0x4f, 0x6b, 0xb2, 0x4d, 0xae,
	0x9a, 0x0e, 0xfa, 0x14, 0x66, 0xd8, 0x34, 0xed, 0x06, 0x98, 0x07, 0xa5, 0xd2, 0xce, 0x46, 0xdc,
	0xb6, 0xe3, 0xec, 0xcf, 0xba, 0x01, 0x36, 0xa6, 0xdb, 0xe2, 0x43, 0x5f, 0x87, 0x5a, 0x96, 0x84,
	0x24, 0xd0, 0xbf, 0x84, 0x05, 0x06, 0xe5, 0xce, 0xfc, 0xa6, 0x52, 0x2b, 0x06, 0x38, 0xa1, 0x1a,
	0xa0, 0x8e, 0xa0, 0x1c, 0x27, 0x4e, 0x02, 0xfd, 0x5f, 0x1a, 0x6c, 0x34, 0x3d, 0x3b, 0x8c, 0xcb,
	0x73, 0x2e, 0x03, 0x72, 0x47, 0x04, 0xd4, 0xb1, 0x45, 0x48, 0xbd, 0x1b, 0x26, 0xc6, 0xbb, 0x1b,
	0x0a, 0xe3, 0xdc, 0x0d, 0x93, 0x69, 0x77, 0xc3, 0x9f, 0x34, 0xb8, 0x7f, 0xcb, 0xde, 0x48, 0x90,
	0x2e, 0xab, 0x36, 0x9e, 0xac, 0xf9, 0x71, 0x64, 0x9d, 0x48, 0x93, 0xf5, 0x63, 0x28, 0xbd, 0xec,
	0x5c, 0xb4, 0x5c, 0x72, 0x2d, 0x8e, 0xe7, 0xf5, 0xc8, 0xd1, 0xfe, 0x13, 0x58, 0x88, 0xad, 0x1c,
	0xe3, 0xa2, 0xd8, 0x87, 0x3b, 0x72, 0x69, 0x14, 0x8d, 0xf1, 0xeb, 0xf1, 0x03, 0x77, 0x03, 0x50,
	0x92, 0xca, 0x9b, 0xc4, 0xff, 0xf7, 0xa0, 0xcc, 0x0e, 0xab, 0xa7, 0xec, 0x61, 0x77, 0x2a, 0xbb,
	0x98, 0x13, 0xc8, 0x24, 0x60, 0x14, 0xf6, 0x31, 0xb1, 0x47, 0xa6, 0x90, 0x40, 0x26, 0x81, 0xbe,
	0x05, 0x8b, 0x8c, 0xac, 0x7a, 0x82, 0x43, 0x89, 0xac, 0xc0, 0xd2, 0x20, 0xbe, 0xa0, 0xc3, 0x88,
	0x8f, 0x43, 0x67, 0x10, 0x9f, 0x04, 0xfa, 0x53, 0xb8, 0x1b, 0x4f, 0xd7, 0x64, 0x14, 0xed, 0x51,
	0x54, 0x43, 0xba, 0x16, 0x0b, 0xe9, 0xfa, 0x3f, 0xf3, 0x70, 0x6f, 0xd8, 0x62, 0x12, 0xa0, 0x5f,
	0x00, 0xc4, 0x6c, 0x9e, 0x45, 0xee, 0x1f, 0xc5, 0x22, 0xf7, 0x90, 0xd5, 0x5b, 0x3d, 0xd5, 0x89,
	0x00, 0x3e, 0xdb, 0xea, 0xb9, 0x89, 0x09, 0xf3, 0x49, 0xff, 0x60, 0xa4, 0x9f, 0x8e, 0x4c, 0x5a,
	0x55, 0x84, 0xa0, 0x5e, 0xb4, 0x95, 0xa9, 0xda, 0x33, 0x28, 0xc5, 0xb9, 0xdf, 0x76, 0x4b, 0xcc,
	0x2b, 0xb7, 0x44, 0xed, 0xa7, 0x70, 0x67, 0x80, 0xc1, 0x38, 0x04, 0xf4, 0xf7, 0x61, 0xe1, 0x00,
	0x53, 0xe6, 0x48, 0xbb, 0xdd, 0xa6, 0x43, 0x6e, 0x39, 0x88, 0x67, 0x50, 0x8e, 0x63, 0x93, 0x00,
	0x3d, 0x86, 0x49, 0x06, 0x26, 0x52, 0xe9, 0x69, 0x2e, 0x2a, 0x10, 0xf4, 0x27, 0x80, 0x0e, 0x30,
	0x95, 0xf2, 0xf6, 0xd8, 0x25, 0xee, 0x6b, 0x6d, 0xe0, 0xbe, 0x7e, 0x01, 0x8b, 0x03, 0xcb, 0x48,
	0x80, 0xfe, 0x1f, 0x66, 0x24, 0x52, 0xc4, 0x3a, 0xc3, 0x2d, 0x7b, 0x68, 0xfa, 0x7f, 0xf2, 0x30,
	0x13, 0x09, 0x75, 0xdb, 0x4d, 0x50, 0x82, 0x7c, 0x2f, 0xed, 0xc9, 0x8b, 0x8c, 0xc3, 0xff, 0xd6,
	0xc3, 0x61, 0x74, 0x17, 0xcd, 0x1b, 0xd3, 0x7c, 0xdc, 0x74, 0xd0, 0x33, 0x28, 0xda, 0xe2, 0xd8,
	0xd5, 0x8b, 0xb4, 0x9a, 0x94, 0x86, 0x63, 0xf0, 0x1b, 0x74, 0xce, 0xee, 0x0f, 0x58, 0x32, 0x44,
	0xf1, 0x77, 0x22, 0xee, 0xcf, 0x1a, 0xfc, 0x5b, 0xc9, 0x5e, 0xa7, 0x46, 0xca, 0x5e, 0xd9, 0x66,
	0x1c, 0x9e, 0x71, 0x39, 0xe6, 0x45, 0xb7, 0x32, 0x2d, 0x36, 0x23, 0x67, 0x76, 0xbb, 0x7c, 0xaf,
	0x21, 0xb6, 0x18, 0xd8, 0xa2, 0x95, 0x19, 0xb9, 0x57, 0x31, 0x53, 0xa7, 0xe8, 0x53, 0x98, 0x6d,
	0xfb, 0xbe, 0x63, 0xba, 0xde, 0xa5, 0x5f, 0x99, 0xe5, 0x21, 0xee, 0xde, 0xe0, 0x31, 0x9e, 0x13,
	0x1c, 0x1e, 0xf9, 0xbe, 0xd3, 0xf4, 0x2e, 0x7d, 0x63, 0xa6, 0x2d, 0xbf, 0x58, 0x24, 0xb8, 0xb6,
	0x6e, 0x30, 0x23, 0x0c, 0x3c, 0x89, 0x99, 0x62, 0xc3, 0x3a, 0xd5, 0x7f, 0xa3, 0xc1, 0x52, 0xda,
	0x5a, 0xb6, 0x42, 0xb0, 0xeb, 0xc5, 0x0e, 0x4e, 0x8c, 0xeb, 0x98, 0x03, 0x3a, 0x61, 0x4b, 0x6a,
	0x9e, 0x23, 0x9e, 0x87, 0xad, 0x1e, 0xa8, 0xed, 0x3c, 0x91, 0xa9, 0x00, 0x07, 0x1d, 0x39, 0x4f,
	0xd0, 0x9a, 0x94, 0x9e, 0x6b, 0xb1, 0xc0, 0x61, 0x1c, 0xf7, 0x0c, 0x7f, 0x47, 0xf5, 0xdf, 0x4d,
	0xc0, 0x9c, 0x62, 0x0c, 0xf2, 0x58, 0xb5, 0xde, 0xb1, 0x66, 0xa6, 0xb8, 0xff, 0xcb, 0xe7, 0xbd,
	0x09, 0x77, 0x02, 0x2b, 0x14, 0x31, 0xad, 0x97, 0xc5, 0xcf, 0x72, 0x79, 0x16, 0x04, 0x60, 0xaf,
	0xf7, 0x90, 0x8a, 0xbb, 0x09, 0x24, 0xdd, 0x64, 0x03, 0x8a, 0x21, 0x0e, 0x5a, 0x5d, 0x93, 0xfa,
	0x26, 0xcb, 0x9c, 0xe6, 0x38, 0x02, 0xf0, 0xb9, 0x33, 0xff, 0x5c, 0x68, 0x38, 0xb2, 0x8f, 0x62,
	0xcc, 0x3e, 0xea, 0xb0, 0x76, 0x16, 0xba, 0xed, 0x3a, 0x8b, 0x9b, 0x2e, 0x75, 0x31, 0xd9, 0xed,
	0xee, 0x59, 0x81, 0x65, 0xbb, 0x94, 0x27, 0xfd, 0x3a, 0x14, 0x2e, 0x31, 0x76, 0xe4, 0x95, 0x5b,
	0xea, 0x6b, 0xe4, 0x39, 0xc6, 0x8e, 0xc1, 0x61, 0xfa, 0x3d, 0x58, 0xcf, 0x26, 0x41, 0x02, 0xdd,
	0x81, 0x6a, 0x93, 0x48, 0x68, 0xf7, 0x73, 0xdc, 0x25, 0x8d, 0xef, 0x5c, 0x42, 0x5d, 0xef, 0x6a,
	0x44, 0x06, 0xe8, 0x01, 0xcc, 0x5b, 0x72, 0xb9, 0xf9, 0x0a, 0x77, 0xa3, 0xc7, 0x46, 0xd1, 0x52,
	0x68, 0xea, 0x7f, 0xd4, 0x60, 0x35, 0x85, 0x0d, 0x8f, 0x52, 0x87, 0x30, 0x8b, 0xd9, 0xc0, 0x6c,
	0x5b, 0x81, 0x0c, 0x53, 0xdb, 0x7d, 0x4e, 0x19, 0xab, 0xb6, 0xf8, 0xd7, 0x91, 0x15, 0x88, 0x0b,
	0x63, 0x06, 0xcb, 0x61, 0xed, 0x53, 0x98, 0x8f, 0x81, 0xc6, 0x7d, 0x51, 0x2c, 0x1a, 0xb8, 0xed,
	0xdf, 0xe0, 0xbe, 0xbe, 0x98, 0x1e, 0xde, 0x81, 0x49, 0xb6, 0x57, 0x22, 0x15, 0xb1, 0x10, 0x57,
	0x04, 0x31, 0x04, 0x14, 0xad, 0xc0, 0xa4, 0x65, 0x53, 0x3f, 0x14, 0x7e, 0xf2, 0x22, 0x67, 0x88,
	0x21, 0xfa, 0x71, 0x52, 0x45, 0x13, 0x9c, 0xcc, 0x4a, 0x9f, 0x8c, 0xba, 0xc7, 0x17, 0xb9, 0xb8,
	0xf2, 0x76, 0xe7, 0x40, 0xda, 0xad, 0x79, 0xd1, 0xd5, 0xeb, 0xb0, 0x34, 0x28, 0x21, 0xaf, 0xe5,
	0x94, 0x43, 0x6c, 0xfb, 0xa1, 0x43, 0x4c, 0xeb, 0xf2, 0x12, 0xdb, 0x54, 0x1e, 0xdb, 0x84, 0xb1,
	0x20, 0xe7, 0xeb, 0x72, 0x5a, 0xf7, 0x61, 0xf5, 0x00, 0x53, 0xd5, 0x22, 0x0c, 0xcb, 0xbb, 0xc2,
	0xa3, 0x1e, 0xf8, 0x07, 0x30, 0xed, 0x07, 0xd4, 0xf5, 0x3d, 0xc2, 0x1d, 0x34, 0xb6, 0x8f, 0x9f,
	0x77, 0x70, 0xd8, 0x3d, 0x11, 0x50, 0x23, 0x42, 0xd3, 0x7f, 0x06, 0x73, 0xca, 0x06, 0xb9, 0x7b,
	0xbb, 0x6d, 0xa1, 0xfe, 0x82, 0xc1, 0xbf, 0x99, 0x0f, 0x5d, 0xfa, 0x21, 0x76, 0xaf, 0x3c, 0xe5,
	0xb9, 0x2c, 0x67, 0x9a, 0x8e, 0xfe, 0x09, 0x14, 0x55, 0x15, 0xa1, 0x77, 0xa1, 0xc0, 0x15, 0x39,
	0x70, 0xab, 0x29, 0x58, 0x06, 0x47, 0xd1, 0xff, 0x32, 0x05, 0x45, 0x55, 0x2c, 0xb4, 0x03, 0xcb,
	0x21, 0xdb, 0xaf, 0x49, 0xa8, 0x15, 0x52, 0xd3, 0xf5, 0xec, 0x56, 0x87, 0xb8, 0x37, 0x58, 0xbe,
	0xb5, 0x17, 0x39, 0xf0, 0x94, 0xc1, 0x9a, 0x11, 0x88, 0x3d, 0x05, 0xa2, 0x35, 0x7e, 0xa0, 0x2c,
	0x11, 0x16, 0x84, 0xe4, 0x12, 0x3f, 0xe8, 0xaf, 0xf8, 0x25, 0x20, 0xf5, 0xcc, 0x4d, 0x8e, 0x22,
	0x0f, 0xfe, 0x51, 0xba, 0xc2, 0x62, 0xc2, 0x33, 0xf4, 0x17, 0x39, 0xa3, 0x6c, 0x25, 0xe6, 0x18,
	0x61, 0xbb, 0x43, 0xa8, 0xdf, 0x36, 0x89, 0xed, 0x87, 0x58, 0x12, 0x2e, 0x0c, 0x25, 0xbc, 0xc7,
	0x17, 0x9c, 0x32, 0xfc, 0x1e, 0x61, 0x3b, 0x31, 0x87, 0x36, 0xa1, 0xec, 0xe0, 0x00, 0x7b, 0x0e,
	0x31, 0x7d, 0x4f, 0x92, 0x05, 0x6e, 0xc8, 0x9a, 0x51, 0x92, 0x90, 0x13, 0x4f, 0xe0, 0x3e, 0x80,
	0xa2, 0xba, 0x3b, 0x1e, 0xd3, 0x18, 0xde, 0x9c, 0x22, 0x2e, 0x43, 0x52, 0x25, 0xe5, 0xb1, 0x8d,
	0x23, 0x29, 0xac, 0xd1, 0x36, 0x14, 0x88, 0x1f, 0xd2, 0xca, 0x12, 0x8f, 0xea, 0x6b, 0x19, 0x1b,
	0x38, 0xf5, 0x43, 0x6a, 0x70, 0x44, 0xe6, 0xbd, 0x2d, 0xb7, 0xed, 0xd2, 0xca, 0xb2, 0x48, 0xd4,
	0xf8, 0x00, 0xad, 0xc0, 0x14, 0xf7, 0x35, 0x52, 0x59, 0xe1, 0xe1, 0x47, 0x8e, 0xb8, 0xaf, 0xe3,
	0xf0, 0x82, 0x54, 0x56, 0xf9, 0xb4, 0x18, 0xa0, 0x77, 0xa0, 0x24, 0x25, 0xbb, 0x74, 0x5b, 0x14,
	0x87, 0xa4, 0x52, 0xe1, 0xe0, 0x79, 0x31, 0xfb, 0x5c, 0x4c, 0x0e, 0x86, 0xb6, 0xea, 0x60, 0x68,
	0xab, 0x3d, 0x83, 0x72, 0x52, 0xbd, 0x8c, 0x2b, 0x37, 0x2e, 0x6e, 0x52, 0x9a, 0x21, 0x06, 0xbc,
	0xa6, 0x43, 0xfd, 0x80, 0x1b, 0x8d, 0x66, 0xf0, 0xef, 0xda, 0x37, 0x50, 0x4e, 0x9e, 0x3a, 0x7a,
	0x4f, 0x5d, 0x9d, 0x69, 0xdd, 0x92, 0xe8, 0xbb, 0x0a, 0xd1, 0x6c, 0x4f, 0x60, 0x28, 0x7a, 0x15,
	0x0a, 0x4c, 0x8f, 0x68, 0x1a, 0x26, 0xea, 0xa7, 0x7b, 0xe5, 0x1c, 0x9a, 0x81, 0xc2, 0x7e, 0xe3,
	0x74, 0xaf, 0xac, 0xed, 0x4e, 0xc3, 0x24, 0x3f, 0xf0, 0x5d, 0x80, 0x19, 0x3f, 0x74, 0x70, 0xc8,
	0x42, 0xcd, 0x31, 0xaf, 0x37, 0xa5, 0xc4, 0x09, 0x12, 0xa0, 0x1d, 0x00, 0xab, 0x07, 0x18, 0xcc,
	0x6b, 0x23, 0xe6, 0x86, 0x82, 0xa5, 0x7f, 0x0b, 0xeb, 0xbb, 0x16, 0xb5, 0xaf, 0xb3, 0x82, 0xcf,
	0x88, 0x51, 0x56, 0x89, 0x3f, 0xf9, 0xd1, 0xe2, 0xcf, 0x3f, 0x34, 0xb8, 0x3b, 0x84, 0x33, 0xbf,
	0x83, 0x7a, 0xac, 0xd9, 0x4e, 0x3e, 0xea, 0x53, 0x1c, 0xba, 0x8e, 0x0b, 0xa6, 0x04, 0x62, 0x41,
	0xa4, 0x76, 0x0d, 0xa5, 0x38, 0x60, 0xa4, 0xb8, 0x1a, 0x57, 0x69, 0x7e, 0x24, 0x95, 0xfe, 0x55,
	0x83, 0x99, 0x08, 0xd0, 0x8b, 0xab, 0x5a, 0x66, 0x5c, 0xcd, 0x27, 0xe2, 0x2a, 0x33, 0x54, 0x71,
	0x63, 0xc9, 0x92, 0x97, 0xb8, 0xaf, 0x10, 0x14, 0x98, 0x9f, 0xc8, 0x4c, 0x91, 0x7f, 0x33, 0x07,
	0xf3, 0x2f, 0xbe, 0xc1, 0x76, 0x94, 0x95, 0xc9, 0x11, 0xba, 0x9f, 0x70, 0x72, 0x91, 0xdd, 0xc4,
	0x5c, 0xfc, 0x01, 0xcc, 0xc7, 0xbc, 0x4d, 0x04, 0x02, 0xa3, 0xa8, 0x3a, 0x9b, 0xfe, 0xbd, 0xc6,
	0x5e, 0xe3, 0x04, 0x87, 0xf4, 0x8d, 0xae, 0xde, 0x37, 0x50, 0x1e, 0x7a, 0x04, 0x0b, 0xb6, 0xef,
	0x5d, 0xba, 0x2d, 0x9b, 0x9a, 0x81, 0xdf, 0x72, 0xed, 0xae, 0x4c, 0x63, 0x4b, 0xd1, 0xf4, 0x4b,
	0x3e, 0xcb, 0xee, 0xdc, 0x41, 0xd1, 0xc6, 0xbb, 0x73, 0xdf, 0x87, 0x02, 0x93, 0x97, 0x29, 0xfc,
	0x2a, 0xf4, 0x3b, 0x81, 0xcc, 0x47, 0xc4, 0x20, 0xf9, 0x92, 0xd2, 0x3f, 0x82, 0x49, 0xbe, 0xbb,
	0x0c, 0xf4, 0x55, 0x98, 0x76, 0x1d, 0xb3, 0xe5, 0x12, 0x2a, 0x93, 0xad, 0x29, 0xd7, 0x39, 0x74,
	0x09, 0xd5, 0xbf, 0x84, 0x69, 0xf6, 0x52, 0x3e, 0x22, 0x57, 0xe8, 0x6d, 0x28, 0xf1, 0xac, 0x5d,
	0x1c, 0x53, 0xff, 0x21, 0x51, 0x64, 0xb3, 0x27, 0x7c, 0x52, 0xb4, 0x75, 0xa2, 0xfc, 0xb6, 0x8f,
	0x28, 0xe4, 0x58, 0x90, 0x80, 0x08, 0x57, 0xff, 0x1a, 0x40, 0xe6, 0xbc, 0x3f, 0x0c, 0xfd, 0x53,
	0x1e, 0x1e, 0x0e, 0x30, 0x3d, 0xed, 0x5c, 0x24, 0x6a, 0x29, 0x1d, 0x4c, 0x28, 0xfa, 0x10, 0x56,
	0x06, 0x52, 0x72, 0xa1, 0x04, 0xf1, 0x20, 0x5e, 0x4c, 0xe4, 0xe5, 0x5c, 0x23, 0x7f, 0x13, 0xae,
	0x9f, 0x46, 0x95, 0x04, 0xbe, 0x47, 0x30, 0xba, 0xee, 0x8b, 0xc8, 0xcb, 0x17, 0x4a, 0x1a, 0xfa,
	0x2c, 0x16, 0x06, 0xb2, 0x69, 0xc4, 0x2a, 0x18, 0xbd, 0x9c, 0x74, 0xc1, 0x8e, 0xcf, 0xd6, 0x76,
	0x61, 0x29, 0x0d, 0x71, 0x9c, 0x62, 0xc4, 0xe6, 0x09, 0x2c, 0x24, 0x9e, 0x4e, 0xe8, 0x2e, 0x54,
	0x13, 0x53, 0xe6, 0xf9, 0xf1, 0x7e, 0xe3, 0x79, 0xf3, 0xb8, 0xb1, 0x5f, 0xce, 0xa5, 0x81, 0x5f,
	0x1e, 0xd6, 0x9b, 0xc7, 0x67, 0x8d, 0x2f, 0xce, 0xca, 0xda, 0xe6, 0xf7, 0x1a, 0xcc, 0xc7, 0x1e,
	0x50, 0x68, 0x0d, 0x56, 0x63, 0x13, 0x31, 0x6a, 0x03, 0xc0, 0xc6, 0x17, 0xf5, 0xa3, 0xe6, 0x71,
	0xf3, 0xf8, 0xa0, 0xac, 0xa1, 0x2a, 0x2c, 0xc7, 0x81, 0xcd, 0xc3, 0xc3, 0xc6, 0x41, 0xfd, 0xb0,
	0x9c, 0x47, 0x15, 0xb6, 0x77, 0x15, 0x74, 0x7c, 0x62, 0x1c, 0xd5, 0x0f, 0xcb, 0x13, 0x83, 0x8b,
	0xf6, 0x1b, 0x87, 0x8d, 0xb3, 0xc6, 0x7e, 0xb9, 0xb0, 0xf9, 0x35, 0x94, 0xf6, 0x7c, 0xef, 0xb2,
	0xe5, 0x46, 0x9e, 0xc8, 0xd8, 0x9f, 0xbf, 0xdc, 0xaf, 0x9f, 0x35, 0xcc, 0xd3, 0xbd, 0x13, 0xa3,
	0x61, 0xd6, 0x8f, 0xf7, 0xcd, 0xe7, 0xcd, 0xc3, 0xb3, 0x86, 0x51, 0xce, 0x21, 0x80, 0xa9, 0xe6,
	0xc1, 0xf1, 0x89, 0xd1, 0x28, 0x6b, 0xa8, 0x0c, 0x45, 0x15, 0xb1, 0x9c, 0x47, 0x77, 0x60, 0x5e,
	0xce, 0xc8, 0x05, 0x85, 0xcd, 0xdf, 0x6a, 0xb0, 0x92, 0x5e, 0xc1, 0x47, 0x6f, 0xc3, 0x46, 0x3a,
	0x24, 0xa6, 0x0d, 0x1d, 0xee, 0x65, 0x60, 0xed, 0x9d, 0x1c, 0x1d, 0x35, 0x8e, 0xcf, 0xca, 0x1a,
	0x7a, 0x0b, 0xd6, 0x32, 0x70, 0x0e, 0x9b, 0x9f, 0x37, 0xca, 0xf9, 0x9d, 0x3f, 0x97, 0xa0, 0xa4,
	0x9c, 0xd0, 0xe9, 0x4d, 0x88, 0x0e, 0xa0, 0xa8, 0x16, 0x91, 0x50, 0x35, 0x56, 0x4b, 0x53, 0x4b,
	0x51, 0xb5, 0x5a, 0x16, 0x88, 0x04, 0x7a, 0x0e, 0xbd, 0xe4, 0xb5, 0x2b, 0xb5, 0x30, 0x84, 0xd6,
	0x63, 0x0b, 0x12, 0xa5, 0xa6, 0xda, 0xdd, 0x21, 0x50, 0x4e, 0x71, 0x1f, 0xe6, 0x94, 0x02, 0x34,
	0xaa, 0xf4, 0xf1, 0xe3, 0x15, 0xed, 0x5a, 0x35, 0x03, 0xc2, 0xa9, 0x1c, 0xf5, 0x0a, 0xe0, 0x51,
	0xd5, 0x61, 0x6d, 0x00, 0xbd, 0x5f, 0xa5, 0xae, 0xad, 0x67, 0x03, 0x39, 0xb9, 0xcf, 0x60, 0x3e,
	0xd6, 0xb9, 0x45, 0x8a, 0x56, 0x92, 0x0d, 0xe3, 0xda, 0x5a, 0x26, 0x8c, 0xd3, 0x3a, 0x85, 0x72,
	0xb2, 0x1f, 0x8b, 0x14, 0xad, 0xa4, 0x34, 0x83, 0x6b, 0xf7, 0x86, 0x81, 0x39, 0xd1, 0xaf, 0xa2,
	0x2e, 0x72, 0xac, 0x75, 0x8a, 0x36, 0x92, 0x0b, 0x93, 0x3d, 0xdc, 0xda, 0xfd, 0x5b, 0x30, 0x38,
	0xf5, 0x0b, 0x58, 0x4e, 0x6d, 0x73, 0x22, 0x5d, 0xb9, 0x01, 0x33, 0xba, 0xad, 0xb5, 0x07, 0xb7,
	0xe2, 0x70, 0x1e, 0xaf, 0xa0, 0x92, 0xd5, 0xb0, 0x44, 0xef, 0x24, 0x35, 0x9a, 0xce, 0xe9, 0xe1,
	0x28, 0x68, 0xd1, 0x86, 0x52, 0x1b, 0x89, 0xea, 0x86, 0xb2, 0x5a, 0xa1, 0xea, 0x86, 0x32, 0xbb,
	0x91, 0xc2, 0x66, 0x62, 0x4d, 0x05, 0xd5, 0x66, 0x92, 0xad, 0x09, 0xd5, 0x66, 0x06, 0x3b, 0x11,
	0xd2, 0xfe, 0x94, 0xf6, 0x42, 0xdc, 0xfe, 0xe2, 0x4d, 0x8a, 0xb8, 0xfd, 0x25, 0x7b, 0x12, 0xdc,
	0xfe, 0x92, 0x5d, 0x06, 0xd5, 0xfe, 0x52, 0x3a, 0x16, 0xaa, 0xfd, 0xa5, 0x36, 0x28, 0x38, 0xd1,
	0x64, 0xcb, 0x41, 0x25, 0x9a, 0xd2, 0xbe, 0x50, 0x89, 0xa6, 0x76, 0x2b, 0x72, 0xe8, 0x35, 0xd4,
	0xb2, 0x2b, 0xfb, 0xe8, 0xd1, 0x68, 0xf5, 0xff, 0xd7, 0xb5, 0xc7, 0xa3, 0x36, 0x0a, 0x84, 0x15,
	0x66, 0xfd, 0xd1, 0xa2, 0x5a, 0xe1, 0x90, 0xdf, 0x68, 0x54, 0x2b, 0x1c, 0xf6, 0x73, 0x8c, 0x9e,
	0x43, 0x18, 0x56, 0xd2, 0x9b, 0xb7, 0x48, 0x31, 0xb1, 0xcc, 0x06, 0x74, 0xed, 0xed, 0xdb, 0x91,
	0x38, 0x9b, 0x03, 0x28, 0xaa, 0x8d, 0x5a, 0x35, 0xd8, 0x27, 0xba, 0xc3, 0x6a, 0xb0, 0x1f, 0xe8,
	0xed, 0xe6, 0xd0, 0xaf, 0xe1, 0xee, 0xd0, 0x06, 0x28, 0xda, 0x8c, 0xdb, 0xc9, 0xb0, 0x2e, 0x70,
	0xed, 0xbd, 0x91, 0x71, 0x39, 0xef, 0x6f, 0x60, 0x39, 0x35, 0x45, 0x42, 0x0f, 0x6f, 0xcd, 0xa1,
	0x78, 0x76, 0x57, 0x7b, 0x34, 0x62, 0xae, 0xa5, 0xe7, 0x76, 0xfe, 0x5d, 0x80, 0x79, 0x76, 0x61,
	0x9e, 0xd2, 0x10, 0x5b, 0x6d, 0x76, 0x5f, 0x72, 0x9f, 0x89, 0x27, 0xe8, 0x71, 0x9f, 0x19, 0x78,
	0x57, 0xc4, 0x7d, 0x66, 0x30, 0xb7, 0xd7, 0x73, 0xc8, 0x84, 0xa5, 0xb4, 0x77, 0x1f, 0xba, 0x1f,
	0x33, 0xa0, 0xb4, 0x97, 0x6c, 0x4d, 0xbf, 0x0d, 0x25, 0x72, 0xca, 0x64, 0x29, 0x4f, 0x95, 0x3a,
	0xa5, 0x10, 0xa9, 0x4a, 0x9d, 0x56, 0x05, 0xe4, 0xa1, 0x73, 0x25, 0xbd, 0x9e, 0xab, 0x1a, 0x6d,
	0x66, 0xc5, 0x57, 0xbd, 0x6f, 0x32, 0x2a, 0xaf, 0xc2, 0x0b, 0xb3, 0x6a, 0xca, 0xaa, 0x17, 0x0e,
	0x29, 0x5d, 0xab, 0x5e, 0x38, 0xb4, 0x3c, 0x9d, 0x43, 0x1e, 0x54, 0x33, 0xdf, 0xe0, 0x09, 0xeb,
	0xca, 0x2c, 0x2d, 0x24, 0xac, 0x2b, 0xfb, 0x41, 0xaf, 0xe7, 0x76, 0xdf, 0xff, 0xd5, 0xe6, 0x95,
	0xdf, 0xb2, 0xbc, 0xab, 0xad, 0x27, 0x3b, 0x94, 0x6e, 0xd9, 0x7e, 0x7b, 0x9b, 0xff, 0xa8, 0x67,
	0xfb, 0xad, 0x6d, 0x82, 0xc3, 0x1b, 0xd7, 0xc6, 0x64, 0x3b, 0xa2, 0x76, 0x31, 0xc5, 0x61, 0x1f,
	0xfe, 0x37, 0x00, 0x00, 0xff, 0xff, 0xd2, 0xe4, 0xf9, 0xef, 0xe1, 0x27, 0x00, 0x00,
}
