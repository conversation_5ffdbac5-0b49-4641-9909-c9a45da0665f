// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/guild-cooperation/guild-cooperation.proto

package guild_cooperation // import "golang.52tt.com/protocol/services/guild-cooperation"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type CooperationType int32

const (
	CooperationType_CTypeInvalid CooperationType = 0
	CooperationType_CTypeAmuse   CooperationType = 4
	CooperationType_CTypeYuyin   CooperationType = 7
	CooperationType_CTypeESports CooperationType = 8
)

var CooperationType_name = map[int32]string{
	0: "CTypeInvalid",
	4: "CTypeAmuse",
	7: "CTypeYuyin",
	8: "CTypeESports",
}
var CooperationType_value = map[string]int32{
	"CTypeInvalid": 0,
	"CTypeAmuse":   4,
	"CTypeYuyin":   7,
	"CTypeESports": 8,
}

func (x CooperationType) String() string {
	return proto.EnumName(CooperationType_name, int32(x))
}
func (CooperationType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{0}
}

// 申请步骤
type ApplicationStep int32

const (
	ApplicationStep_StepWaitSubmit  ApplicationStep = 0
	ApplicationStep_StepSubmit      ApplicationStep = 3
	ApplicationStep_StepCommunicate ApplicationStep = 5
	ApplicationStep_StepCooperation ApplicationStep = 7
)

var ApplicationStep_name = map[int32]string{
	0: "StepWaitSubmit",
	3: "StepSubmit",
	5: "StepCommunicate",
	7: "StepCooperation",
}
var ApplicationStep_value = map[string]int32{
	"StepWaitSubmit":  0,
	"StepSubmit":      3,
	"StepCommunicate": 5,
	"StepCooperation": 7,
}

func (x ApplicationStep) String() string {
	return proto.EnumName(ApplicationStep_name, int32(x))
}
func (ApplicationStep) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{1}
}

// 申请操作
type ApplicationOperation int32

const (
	ApplicationOperation_OperationInvalid  ApplicationOperation = 0
	ApplicationOperation_OperationApproval ApplicationOperation = 1
	ApplicationOperation_OperationReject   ApplicationOperation = 3
)

var ApplicationOperation_name = map[int32]string{
	0: "OperationInvalid",
	1: "OperationApproval",
	3: "OperationReject",
}
var ApplicationOperation_value = map[string]int32{
	"OperationInvalid":  0,
	"OperationApproval": 1,
	"OperationReject":   3,
}

func (x ApplicationOperation) String() string {
	return proto.EnumName(ApplicationOperation_name, int32(x))
}
func (ApplicationOperation) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{2}
}

// 请求查询状态
type ReqApplyStatus int32

const (
	ReqApplyStatus_Invalid       ReqApplyStatus = 0
	ReqApplyStatus_Submitted     ReqApplyStatus = 1
	ReqApplyStatus_Rejected      ReqApplyStatus = 3
	ReqApplyStatus_Communicating ReqApplyStatus = 5
	ReqApplyStatus_WaitApply     ReqApplyStatus = 6
	ReqApplyStatus_Applied       ReqApplyStatus = 7
	ReqApplyStatus_Cooperating   ReqApplyStatus = 10
)

var ReqApplyStatus_name = map[int32]string{
	0:  "Invalid",
	1:  "Submitted",
	3:  "Rejected",
	5:  "Communicating",
	6:  "WaitApply",
	7:  "Applied",
	10: "Cooperating",
}
var ReqApplyStatus_value = map[string]int32{
	"Invalid":       0,
	"Submitted":     1,
	"Rejected":      3,
	"Communicating": 5,
	"WaitApply":     6,
	"Applied":       7,
	"Cooperating":   10,
}

func (x ReqApplyStatus) String() string {
	return proto.EnumName(ReqApplyStatus_name, int32(x))
}
func (ReqApplyStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{3}
}

// 合作库操作类型
type CoopOptType int32

const (
	CoopOptType_InvalidType CoopOptType = 0
	CoopOptType_Add         CoopOptType = 1
	CoopOptType_Del         CoopOptType = 2
)

var CoopOptType_name = map[int32]string{
	0: "InvalidType",
	1: "Add",
	2: "Del",
}
var CoopOptType_value = map[string]int32{
	"InvalidType": 0,
	"Add":         1,
	"Del":         2,
}

func (x CoopOptType) String() string {
	return proto.EnumName(CoopOptType_name, int32(x))
}
func (CoopOptType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{4}
}

// 申请来源
type SourcePlatform int32

const (
	SourcePlatform_PLATFORM_INVALID  SourcePlatform = 0
	SourcePlatform_PLATFORM_APP      SourcePlatform = 1
	SourcePlatform_PLATFORM_WEB      SourcePlatform = 2
	SourcePlatform_PLATFORM_OPERATOR SourcePlatform = 3
)

var SourcePlatform_name = map[int32]string{
	0: "PLATFORM_INVALID",
	1: "PLATFORM_APP",
	2: "PLATFORM_WEB",
	3: "PLATFORM_OPERATOR",
}
var SourcePlatform_value = map[string]int32{
	"PLATFORM_INVALID":  0,
	"PLATFORM_APP":      1,
	"PLATFORM_WEB":      2,
	"PLATFORM_OPERATOR": 3,
}

func (x SourcePlatform) String() string {
	return proto.EnumName(SourcePlatform_name, int32(x))
}
func (SourcePlatform) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{5}
}

type MsgType int32

const (
	MsgType_MSG_TYPE_INVALID MsgType = 0
	MsgType_MSG_TYPE_PASS    MsgType = 1
	MsgType_MSG_TYPE_REJECT  MsgType = 2
)

var MsgType_name = map[int32]string{
	0: "MSG_TYPE_INVALID",
	1: "MSG_TYPE_PASS",
	2: "MSG_TYPE_REJECT",
}
var MsgType_value = map[string]int32{
	"MSG_TYPE_INVALID": 0,
	"MSG_TYPE_PASS":    1,
	"MSG_TYPE_REJECT":  2,
}

func (x MsgType) String() string {
	return proto.EnumName(MsgType_name, int32(x))
}
func (MsgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{6}
}

type CooperationInfo struct {
	CooperationType      CooperationType      `protobuf:"varint,1,opt,name=cooperation_type,json=cooperationType,proto3,enum=guild_cooperation.CooperationType" json:"cooperation_type,omitempty"`
	IsCooperation        bool                 `protobuf:"varint,2,opt,name=is_cooperation,json=isCooperation,proto3" json:"is_cooperation,omitempty"`
	ApplicationStep      ApplicationStep      `protobuf:"varint,3,opt,name=application_step,json=applicationStep,proto3,enum=guild_cooperation.ApplicationStep" json:"application_step,omitempty"`
	ApplicationOperation ApplicationOperation `protobuf:"varint,4,opt,name=application_operation,json=applicationOperation,proto3,enum=guild_cooperation.ApplicationOperation" json:"application_operation,omitempty"`
	ApplicationId        string               `protobuf:"bytes,5,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	ReapplyTime          uint32               `protobuf:"varint,6,opt,name=reapply_time,json=reapplyTime,proto3" json:"reapply_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *CooperationInfo) Reset()         { *m = CooperationInfo{} }
func (m *CooperationInfo) String() string { return proto.CompactTextString(m) }
func (*CooperationInfo) ProtoMessage()    {}
func (*CooperationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{0}
}
func (m *CooperationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CooperationInfo.Unmarshal(m, b)
}
func (m *CooperationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CooperationInfo.Marshal(b, m, deterministic)
}
func (dst *CooperationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CooperationInfo.Merge(dst, src)
}
func (m *CooperationInfo) XXX_Size() int {
	return xxx_messageInfo_CooperationInfo.Size(m)
}
func (m *CooperationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CooperationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CooperationInfo proto.InternalMessageInfo

func (m *CooperationInfo) GetCooperationType() CooperationType {
	if m != nil {
		return m.CooperationType
	}
	return CooperationType_CTypeInvalid
}

func (m *CooperationInfo) GetIsCooperation() bool {
	if m != nil {
		return m.IsCooperation
	}
	return false
}

func (m *CooperationInfo) GetApplicationStep() ApplicationStep {
	if m != nil {
		return m.ApplicationStep
	}
	return ApplicationStep_StepWaitSubmit
}

func (m *CooperationInfo) GetApplicationOperation() ApplicationOperation {
	if m != nil {
		return m.ApplicationOperation
	}
	return ApplicationOperation_OperationInvalid
}

func (m *CooperationInfo) GetApplicationId() string {
	if m != nil {
		return m.ApplicationId
	}
	return ""
}

func (m *CooperationInfo) GetReapplyTime() uint32 {
	if m != nil {
		return m.ReapplyTime
	}
	return 0
}

type GetCooperationInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCooperationInfoReq) Reset()         { *m = GetCooperationInfoReq{} }
func (m *GetCooperationInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetCooperationInfoReq) ProtoMessage()    {}
func (*GetCooperationInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{1}
}
func (m *GetCooperationInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCooperationInfoReq.Unmarshal(m, b)
}
func (m *GetCooperationInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCooperationInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetCooperationInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCooperationInfoReq.Merge(dst, src)
}
func (m *GetCooperationInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetCooperationInfoReq.Size(m)
}
func (m *GetCooperationInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCooperationInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCooperationInfoReq proto.InternalMessageInfo

func (m *GetCooperationInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetCooperationInfoResp struct {
	CooperationInfo      []*CooperationInfo `protobuf:"bytes,1,rep,name=cooperation_info,json=cooperationInfo,proto3" json:"cooperation_info,omitempty"`
	EsportEntranceSwitch bool               `protobuf:"varint,2,opt,name=esport_entrance_switch,json=esportEntranceSwitch,proto3" json:"esport_entrance_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetCooperationInfoResp) Reset()         { *m = GetCooperationInfoResp{} }
func (m *GetCooperationInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetCooperationInfoResp) ProtoMessage()    {}
func (*GetCooperationInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{2}
}
func (m *GetCooperationInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCooperationInfoResp.Unmarshal(m, b)
}
func (m *GetCooperationInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCooperationInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetCooperationInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCooperationInfoResp.Merge(dst, src)
}
func (m *GetCooperationInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetCooperationInfoResp.Size(m)
}
func (m *GetCooperationInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCooperationInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCooperationInfoResp proto.InternalMessageInfo

func (m *GetCooperationInfoResp) GetCooperationInfo() []*CooperationInfo {
	if m != nil {
		return m.CooperationInfo
	}
	return nil
}

func (m *GetCooperationInfoResp) GetEsportEntranceSwitch() bool {
	if m != nil {
		return m.EsportEntranceSwitch
	}
	return false
}

type GetCooperationInfoByPhoneReq struct {
	Phone                string   `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCooperationInfoByPhoneReq) Reset()         { *m = GetCooperationInfoByPhoneReq{} }
func (m *GetCooperationInfoByPhoneReq) String() string { return proto.CompactTextString(m) }
func (*GetCooperationInfoByPhoneReq) ProtoMessage()    {}
func (*GetCooperationInfoByPhoneReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{3}
}
func (m *GetCooperationInfoByPhoneReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCooperationInfoByPhoneReq.Unmarshal(m, b)
}
func (m *GetCooperationInfoByPhoneReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCooperationInfoByPhoneReq.Marshal(b, m, deterministic)
}
func (dst *GetCooperationInfoByPhoneReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCooperationInfoByPhoneReq.Merge(dst, src)
}
func (m *GetCooperationInfoByPhoneReq) XXX_Size() int {
	return xxx_messageInfo_GetCooperationInfoByPhoneReq.Size(m)
}
func (m *GetCooperationInfoByPhoneReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCooperationInfoByPhoneReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCooperationInfoByPhoneReq proto.InternalMessageInfo

func (m *GetCooperationInfoByPhoneReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

type GetCooperationInfoByPhoneResp struct {
	CooperationInfo      []*CooperationInfo `protobuf:"bytes,1,rep,name=cooperation_info,json=cooperationInfo,proto3" json:"cooperation_info,omitempty"`
	EsportEntranceSwitch bool               `protobuf:"varint,2,opt,name=esport_entrance_switch,json=esportEntranceSwitch,proto3" json:"esport_entrance_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetCooperationInfoByPhoneResp) Reset()         { *m = GetCooperationInfoByPhoneResp{} }
func (m *GetCooperationInfoByPhoneResp) String() string { return proto.CompactTextString(m) }
func (*GetCooperationInfoByPhoneResp) ProtoMessage()    {}
func (*GetCooperationInfoByPhoneResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{4}
}
func (m *GetCooperationInfoByPhoneResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCooperationInfoByPhoneResp.Unmarshal(m, b)
}
func (m *GetCooperationInfoByPhoneResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCooperationInfoByPhoneResp.Marshal(b, m, deterministic)
}
func (dst *GetCooperationInfoByPhoneResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCooperationInfoByPhoneResp.Merge(dst, src)
}
func (m *GetCooperationInfoByPhoneResp) XXX_Size() int {
	return xxx_messageInfo_GetCooperationInfoByPhoneResp.Size(m)
}
func (m *GetCooperationInfoByPhoneResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCooperationInfoByPhoneResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCooperationInfoByPhoneResp proto.InternalMessageInfo

func (m *GetCooperationInfoByPhoneResp) GetCooperationInfo() []*CooperationInfo {
	if m != nil {
		return m.CooperationInfo
	}
	return nil
}

func (m *GetCooperationInfoByPhoneResp) GetEsportEntranceSwitch() bool {
	if m != nil {
		return m.EsportEntranceSwitch
	}
	return false
}

// 提交申请资料
type SubmitApplicationReq struct {
	Uid                   uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ExternalPlatform      string            `protobuf:"bytes,2,opt,name=external_platform,json=externalPlatform,proto3" json:"external_platform,omitempty"`
	Wechat                string            `protobuf:"bytes,3,opt,name=wechat,proto3" json:"wechat,omitempty"`
	Corporation           string            `protobuf:"bytes,4,opt,name=corporation,proto3" json:"corporation,omitempty"`
	LegalPerson           string            `protobuf:"bytes,5,opt,name=legal_person,json=legalPerson,proto3" json:"legal_person,omitempty"`
	CorporationCode       string            `protobuf:"bytes,6,opt,name=corporation_code,json=corporationCode,proto3" json:"corporation_code,omitempty"`
	BankAccount           string            `protobuf:"bytes,7,opt,name=bank_account,json=bankAccount,proto3" json:"bank_account,omitempty"`
	BusinessLicensePhotos []string          `protobuf:"bytes,8,rep,name=business_license_photos,json=businessLicensePhotos,proto3" json:"business_license_photos,omitempty"`
	BillHistoryPhotos     []string          `protobuf:"bytes,9,rep,name=bill_history_photos,json=billHistoryPhotos,proto3" json:"bill_history_photos,omitempty"`
	CooperationType       CooperationType   `protobuf:"varint,10,opt,name=cooperation_type,json=cooperationType,proto3,enum=guild_cooperation.CooperationType" json:"cooperation_type,omitempty"`
	CooperationTypes      []CooperationType `protobuf:"varint,11,rep,packed,name=cooperation_types,json=cooperationTypes,proto3,enum=guild_cooperation.CooperationType" json:"cooperation_types,omitempty"`
	Phone                 string            `protobuf:"bytes,12,opt,name=phone,proto3" json:"phone,omitempty"`
	SourcePlatform        SourcePlatform    `protobuf:"varint,13,opt,name=source_platform,json=sourcePlatform,proto3,enum=guild_cooperation.SourcePlatform" json:"source_platform,omitempty"`
	CreateGuildName       string            `protobuf:"bytes,14,opt,name=create_guild_name,json=createGuildName,proto3" json:"create_guild_name,omitempty"`
	Operator              string            `protobuf:"bytes,15,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}          `json:"-"`
	XXX_unrecognized      []byte            `json:"-"`
	XXX_sizecache         int32             `json:"-"`
}

func (m *SubmitApplicationReq) Reset()         { *m = SubmitApplicationReq{} }
func (m *SubmitApplicationReq) String() string { return proto.CompactTextString(m) }
func (*SubmitApplicationReq) ProtoMessage()    {}
func (*SubmitApplicationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{5}
}
func (m *SubmitApplicationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitApplicationReq.Unmarshal(m, b)
}
func (m *SubmitApplicationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitApplicationReq.Marshal(b, m, deterministic)
}
func (dst *SubmitApplicationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitApplicationReq.Merge(dst, src)
}
func (m *SubmitApplicationReq) XXX_Size() int {
	return xxx_messageInfo_SubmitApplicationReq.Size(m)
}
func (m *SubmitApplicationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitApplicationReq.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitApplicationReq proto.InternalMessageInfo

func (m *SubmitApplicationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SubmitApplicationReq) GetExternalPlatform() string {
	if m != nil {
		return m.ExternalPlatform
	}
	return ""
}

func (m *SubmitApplicationReq) GetWechat() string {
	if m != nil {
		return m.Wechat
	}
	return ""
}

func (m *SubmitApplicationReq) GetCorporation() string {
	if m != nil {
		return m.Corporation
	}
	return ""
}

func (m *SubmitApplicationReq) GetLegalPerson() string {
	if m != nil {
		return m.LegalPerson
	}
	return ""
}

func (m *SubmitApplicationReq) GetCorporationCode() string {
	if m != nil {
		return m.CorporationCode
	}
	return ""
}

func (m *SubmitApplicationReq) GetBankAccount() string {
	if m != nil {
		return m.BankAccount
	}
	return ""
}

func (m *SubmitApplicationReq) GetBusinessLicensePhotos() []string {
	if m != nil {
		return m.BusinessLicensePhotos
	}
	return nil
}

func (m *SubmitApplicationReq) GetBillHistoryPhotos() []string {
	if m != nil {
		return m.BillHistoryPhotos
	}
	return nil
}

func (m *SubmitApplicationReq) GetCooperationType() CooperationType {
	if m != nil {
		return m.CooperationType
	}
	return CooperationType_CTypeInvalid
}

func (m *SubmitApplicationReq) GetCooperationTypes() []CooperationType {
	if m != nil {
		return m.CooperationTypes
	}
	return nil
}

func (m *SubmitApplicationReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *SubmitApplicationReq) GetSourcePlatform() SourcePlatform {
	if m != nil {
		return m.SourcePlatform
	}
	return SourcePlatform_PLATFORM_INVALID
}

func (m *SubmitApplicationReq) GetCreateGuildName() string {
	if m != nil {
		return m.CreateGuildName
	}
	return ""
}

func (m *SubmitApplicationReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type SubmitApplicationResp struct {
	ApplicationId        string   `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	ApplicationIds       []string `protobuf:"bytes,2,rep,name=application_ids,json=applicationIds,proto3" json:"application_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitApplicationResp) Reset()         { *m = SubmitApplicationResp{} }
func (m *SubmitApplicationResp) String() string { return proto.CompactTextString(m) }
func (*SubmitApplicationResp) ProtoMessage()    {}
func (*SubmitApplicationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{6}
}
func (m *SubmitApplicationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitApplicationResp.Unmarshal(m, b)
}
func (m *SubmitApplicationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitApplicationResp.Marshal(b, m, deterministic)
}
func (dst *SubmitApplicationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitApplicationResp.Merge(dst, src)
}
func (m *SubmitApplicationResp) XXX_Size() int {
	return xxx_messageInfo_SubmitApplicationResp.Size(m)
}
func (m *SubmitApplicationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitApplicationResp.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitApplicationResp proto.InternalMessageInfo

func (m *SubmitApplicationResp) GetApplicationId() string {
	if m != nil {
		return m.ApplicationId
	}
	return ""
}

func (m *SubmitApplicationResp) GetApplicationIds() []string {
	if m != nil {
		return m.ApplicationIds
	}
	return nil
}

// 创建公会
type CreateGuildReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildName            string   `protobuf:"bytes,2,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGuildReq) Reset()         { *m = CreateGuildReq{} }
func (m *CreateGuildReq) String() string { return proto.CompactTextString(m) }
func (*CreateGuildReq) ProtoMessage()    {}
func (*CreateGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{7}
}
func (m *CreateGuildReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGuildReq.Unmarshal(m, b)
}
func (m *CreateGuildReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGuildReq.Marshal(b, m, deterministic)
}
func (dst *CreateGuildReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGuildReq.Merge(dst, src)
}
func (m *CreateGuildReq) XXX_Size() int {
	return xxx_messageInfo_CreateGuildReq.Size(m)
}
func (m *CreateGuildReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGuildReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGuildReq proto.InternalMessageInfo

func (m *CreateGuildReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CreateGuildReq) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

type CreateGuildResp struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGuildResp) Reset()         { *m = CreateGuildResp{} }
func (m *CreateGuildResp) String() string { return proto.CompactTextString(m) }
func (*CreateGuildResp) ProtoMessage()    {}
func (*CreateGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{8}
}
func (m *CreateGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGuildResp.Unmarshal(m, b)
}
func (m *CreateGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGuildResp.Marshal(b, m, deterministic)
}
func (dst *CreateGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGuildResp.Merge(dst, src)
}
func (m *CreateGuildResp) XXX_Size() int {
	return xxx_messageInfo_CreateGuildResp.Size(m)
}
func (m *CreateGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGuildResp proto.InternalMessageInfo

func (m *CreateGuildResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type ApplyCooperationReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ApplicationId        string   `protobuf:"bytes,3,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyCooperationReq) Reset()         { *m = ApplyCooperationReq{} }
func (m *ApplyCooperationReq) String() string { return proto.CompactTextString(m) }
func (*ApplyCooperationReq) ProtoMessage()    {}
func (*ApplyCooperationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{9}
}
func (m *ApplyCooperationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyCooperationReq.Unmarshal(m, b)
}
func (m *ApplyCooperationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyCooperationReq.Marshal(b, m, deterministic)
}
func (dst *ApplyCooperationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyCooperationReq.Merge(dst, src)
}
func (m *ApplyCooperationReq) XXX_Size() int {
	return xxx_messageInfo_ApplyCooperationReq.Size(m)
}
func (m *ApplyCooperationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyCooperationReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyCooperationReq proto.InternalMessageInfo

func (m *ApplyCooperationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApplyCooperationReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ApplyCooperationReq) GetApplicationId() string {
	if m != nil {
		return m.ApplicationId
	}
	return ""
}

type ApplyCooperationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyCooperationResp) Reset()         { *m = ApplyCooperationResp{} }
func (m *ApplyCooperationResp) String() string { return proto.CompactTextString(m) }
func (*ApplyCooperationResp) ProtoMessage()    {}
func (*ApplyCooperationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{10}
}
func (m *ApplyCooperationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyCooperationResp.Unmarshal(m, b)
}
func (m *ApplyCooperationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyCooperationResp.Marshal(b, m, deterministic)
}
func (dst *ApplyCooperationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyCooperationResp.Merge(dst, src)
}
func (m *ApplyCooperationResp) XXX_Size() int {
	return xxx_messageInfo_ApplyCooperationResp.Size(m)
}
func (m *ApplyCooperationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyCooperationResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyCooperationResp proto.InternalMessageInfo

type CooperationApplication struct {
	ApplicationId         string               `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	Uid                   uint32               `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                  string               `protobuf:"bytes,3,opt,name=ttid,proto3" json:"ttid,omitempty"`
	CooperationType       CooperationType      `protobuf:"varint,4,opt,name=cooperation_type,json=cooperationType,proto3,enum=guild_cooperation.CooperationType" json:"cooperation_type,omitempty"`
	WechatId              string               `protobuf:"bytes,5,opt,name=wechat_id,json=wechatId,proto3" json:"wechat_id,omitempty"`
	CreateTime            uint32               `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Operator              string               `protobuf:"bytes,7,opt,name=operator,proto3" json:"operator,omitempty"`
	UpdateTime            uint32               `protobuf:"varint,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	LegalPerson           string               `protobuf:"bytes,9,opt,name=legal_person,json=legalPerson,proto3" json:"legal_person,omitempty"`
	Corporation           string               `protobuf:"bytes,10,opt,name=corporation,proto3" json:"corporation,omitempty"`
	CorporationCode       string               `protobuf:"bytes,11,opt,name=corporation_code,json=corporationCode,proto3" json:"corporation_code,omitempty"`
	BankAccount           string               `protobuf:"bytes,12,opt,name=bank_account,json=bankAccount,proto3" json:"bank_account,omitempty"`
	BusinessLicensePhotos []string             `protobuf:"bytes,13,rep,name=business_license_photos,json=businessLicensePhotos,proto3" json:"business_license_photos,omitempty"`
	BillHistoryPhotos     []string             `protobuf:"bytes,14,rep,name=bill_history_photos,json=billHistoryPhotos,proto3" json:"bill_history_photos,omitempty"`
	Reason                string               `protobuf:"bytes,16,opt,name=reason,proto3" json:"reason,omitempty"`
	ExternalPlatform      string               `protobuf:"bytes,17,opt,name=external_platform,json=externalPlatform,proto3" json:"external_platform,omitempty"`
	CurrentStatus         ReqApplyStatus       `protobuf:"varint,15,opt,name=current_status,json=currentStatus,proto3,enum=guild_cooperation.ReqApplyStatus" json:"current_status,omitempty"`
	Step                  ApplicationStep      `protobuf:"varint,18,opt,name=step,proto3,enum=guild_cooperation.ApplicationStep" json:"step,omitempty"`
	Operation             ApplicationOperation `protobuf:"varint,19,opt,name=operation,proto3,enum=guild_cooperation.ApplicationOperation" json:"operation,omitempty"`
	CurrentStatusDesc     string               `protobuf:"bytes,20,opt,name=current_status_desc,json=currentStatusDesc,proto3" json:"current_status_desc,omitempty"`
	ApproveDesc           string               `protobuf:"bytes,21,opt,name=approve_desc,json=approveDesc,proto3" json:"approve_desc,omitempty"`
	ReapplyTime           uint32               `protobuf:"varint,22,opt,name=reapply_time,json=reapplyTime,proto3" json:"reapply_time,omitempty"`
	Phone                 string               `protobuf:"bytes,23,opt,name=phone,proto3" json:"phone,omitempty"`
	SourcePlatform        SourcePlatform       `protobuf:"varint,24,opt,name=source_platform,json=sourcePlatform,proto3,enum=guild_cooperation.SourcePlatform" json:"source_platform,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}             `json:"-"`
	XXX_unrecognized      []byte               `json:"-"`
	XXX_sizecache         int32                `json:"-"`
}

func (m *CooperationApplication) Reset()         { *m = CooperationApplication{} }
func (m *CooperationApplication) String() string { return proto.CompactTextString(m) }
func (*CooperationApplication) ProtoMessage()    {}
func (*CooperationApplication) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{11}
}
func (m *CooperationApplication) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CooperationApplication.Unmarshal(m, b)
}
func (m *CooperationApplication) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CooperationApplication.Marshal(b, m, deterministic)
}
func (dst *CooperationApplication) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CooperationApplication.Merge(dst, src)
}
func (m *CooperationApplication) XXX_Size() int {
	return xxx_messageInfo_CooperationApplication.Size(m)
}
func (m *CooperationApplication) XXX_DiscardUnknown() {
	xxx_messageInfo_CooperationApplication.DiscardUnknown(m)
}

var xxx_messageInfo_CooperationApplication proto.InternalMessageInfo

func (m *CooperationApplication) GetApplicationId() string {
	if m != nil {
		return m.ApplicationId
	}
	return ""
}

func (m *CooperationApplication) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CooperationApplication) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *CooperationApplication) GetCooperationType() CooperationType {
	if m != nil {
		return m.CooperationType
	}
	return CooperationType_CTypeInvalid
}

func (m *CooperationApplication) GetWechatId() string {
	if m != nil {
		return m.WechatId
	}
	return ""
}

func (m *CooperationApplication) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *CooperationApplication) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *CooperationApplication) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *CooperationApplication) GetLegalPerson() string {
	if m != nil {
		return m.LegalPerson
	}
	return ""
}

func (m *CooperationApplication) GetCorporation() string {
	if m != nil {
		return m.Corporation
	}
	return ""
}

func (m *CooperationApplication) GetCorporationCode() string {
	if m != nil {
		return m.CorporationCode
	}
	return ""
}

func (m *CooperationApplication) GetBankAccount() string {
	if m != nil {
		return m.BankAccount
	}
	return ""
}

func (m *CooperationApplication) GetBusinessLicensePhotos() []string {
	if m != nil {
		return m.BusinessLicensePhotos
	}
	return nil
}

func (m *CooperationApplication) GetBillHistoryPhotos() []string {
	if m != nil {
		return m.BillHistoryPhotos
	}
	return nil
}

func (m *CooperationApplication) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *CooperationApplication) GetExternalPlatform() string {
	if m != nil {
		return m.ExternalPlatform
	}
	return ""
}

func (m *CooperationApplication) GetCurrentStatus() ReqApplyStatus {
	if m != nil {
		return m.CurrentStatus
	}
	return ReqApplyStatus_Invalid
}

func (m *CooperationApplication) GetStep() ApplicationStep {
	if m != nil {
		return m.Step
	}
	return ApplicationStep_StepWaitSubmit
}

func (m *CooperationApplication) GetOperation() ApplicationOperation {
	if m != nil {
		return m.Operation
	}
	return ApplicationOperation_OperationInvalid
}

func (m *CooperationApplication) GetCurrentStatusDesc() string {
	if m != nil {
		return m.CurrentStatusDesc
	}
	return ""
}

func (m *CooperationApplication) GetApproveDesc() string {
	if m != nil {
		return m.ApproveDesc
	}
	return ""
}

func (m *CooperationApplication) GetReapplyTime() uint32 {
	if m != nil {
		return m.ReapplyTime
	}
	return 0
}

func (m *CooperationApplication) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *CooperationApplication) GetSourcePlatform() SourcePlatform {
	if m != nil {
		return m.SourcePlatform
	}
	return SourcePlatform_PLATFORM_INVALID
}

type GetApplicationsReq struct {
	ReqApplyStatus       ReqApplyStatus  `protobuf:"varint,1,opt,name=req_apply_status,json=reqApplyStatus,proto3,enum=guild_cooperation.ReqApplyStatus" json:"req_apply_status,omitempty"`
	Offset               uint32          `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32          `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	Ttid                 string          `protobuf:"bytes,5,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Wechat               string          `protobuf:"bytes,6,opt,name=wechat,proto3" json:"wechat,omitempty"`
	CooperationType      CooperationType `protobuf:"varint,7,opt,name=cooperation_type,json=cooperationType,proto3,enum=guild_cooperation.CooperationType" json:"cooperation_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetApplicationsReq) Reset()         { *m = GetApplicationsReq{} }
func (m *GetApplicationsReq) String() string { return proto.CompactTextString(m) }
func (*GetApplicationsReq) ProtoMessage()    {}
func (*GetApplicationsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{12}
}
func (m *GetApplicationsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplicationsReq.Unmarshal(m, b)
}
func (m *GetApplicationsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplicationsReq.Marshal(b, m, deterministic)
}
func (dst *GetApplicationsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplicationsReq.Merge(dst, src)
}
func (m *GetApplicationsReq) XXX_Size() int {
	return xxx_messageInfo_GetApplicationsReq.Size(m)
}
func (m *GetApplicationsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplicationsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplicationsReq proto.InternalMessageInfo

func (m *GetApplicationsReq) GetReqApplyStatus() ReqApplyStatus {
	if m != nil {
		return m.ReqApplyStatus
	}
	return ReqApplyStatus_Invalid
}

func (m *GetApplicationsReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetApplicationsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetApplicationsReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *GetApplicationsReq) GetWechat() string {
	if m != nil {
		return m.Wechat
	}
	return ""
}

func (m *GetApplicationsReq) GetCooperationType() CooperationType {
	if m != nil {
		return m.CooperationType
	}
	return CooperationType_CTypeInvalid
}

type GetApplicationsResp struct {
	Applications         []*CooperationApplication `protobuf:"bytes,1,rep,name=applications,proto3" json:"applications,omitempty"`
	Total                uint32                    `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetApplicationsResp) Reset()         { *m = GetApplicationsResp{} }
func (m *GetApplicationsResp) String() string { return proto.CompactTextString(m) }
func (*GetApplicationsResp) ProtoMessage()    {}
func (*GetApplicationsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{13}
}
func (m *GetApplicationsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplicationsResp.Unmarshal(m, b)
}
func (m *GetApplicationsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplicationsResp.Marshal(b, m, deterministic)
}
func (dst *GetApplicationsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplicationsResp.Merge(dst, src)
}
func (m *GetApplicationsResp) XXX_Size() int {
	return xxx_messageInfo_GetApplicationsResp.Size(m)
}
func (m *GetApplicationsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplicationsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplicationsResp proto.InternalMessageInfo

func (m *GetApplicationsResp) GetApplications() []*CooperationApplication {
	if m != nil {
		return m.Applications
	}
	return nil
}

func (m *GetApplicationsResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetApplicationInfoReq struct {
	ApplicationId        string   `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetApplicationInfoReq) Reset()         { *m = GetApplicationInfoReq{} }
func (m *GetApplicationInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetApplicationInfoReq) ProtoMessage()    {}
func (*GetApplicationInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{14}
}
func (m *GetApplicationInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplicationInfoReq.Unmarshal(m, b)
}
func (m *GetApplicationInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplicationInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetApplicationInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplicationInfoReq.Merge(dst, src)
}
func (m *GetApplicationInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetApplicationInfoReq.Size(m)
}
func (m *GetApplicationInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplicationInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplicationInfoReq proto.InternalMessageInfo

func (m *GetApplicationInfoReq) GetApplicationId() string {
	if m != nil {
		return m.ApplicationId
	}
	return ""
}

func (m *GetApplicationInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetApplicationInfoResp struct {
	Application          *CooperationApplication `protobuf:"bytes,1,opt,name=application,proto3" json:"application,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetApplicationInfoResp) Reset()         { *m = GetApplicationInfoResp{} }
func (m *GetApplicationInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetApplicationInfoResp) ProtoMessage()    {}
func (*GetApplicationInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{15}
}
func (m *GetApplicationInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplicationInfoResp.Unmarshal(m, b)
}
func (m *GetApplicationInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplicationInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetApplicationInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplicationInfoResp.Merge(dst, src)
}
func (m *GetApplicationInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetApplicationInfoResp.Size(m)
}
func (m *GetApplicationInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplicationInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplicationInfoResp proto.InternalMessageInfo

func (m *GetApplicationInfoResp) GetApplication() *CooperationApplication {
	if m != nil {
		return m.Application
	}
	return nil
}

type ApproveApplicationReq struct {
	ApplicationId        string          `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	Operator             string          `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	Step                 ApplicationStep `protobuf:"varint,3,opt,name=step,proto3,enum=guild_cooperation.ApplicationStep" json:"step,omitempty"`
	NotificationId       uint32          `protobuf:"varint,4,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ApproveApplicationReq) Reset()         { *m = ApproveApplicationReq{} }
func (m *ApproveApplicationReq) String() string { return proto.CompactTextString(m) }
func (*ApproveApplicationReq) ProtoMessage()    {}
func (*ApproveApplicationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{16}
}
func (m *ApproveApplicationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApproveApplicationReq.Unmarshal(m, b)
}
func (m *ApproveApplicationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApproveApplicationReq.Marshal(b, m, deterministic)
}
func (dst *ApproveApplicationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApproveApplicationReq.Merge(dst, src)
}
func (m *ApproveApplicationReq) XXX_Size() int {
	return xxx_messageInfo_ApproveApplicationReq.Size(m)
}
func (m *ApproveApplicationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApproveApplicationReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApproveApplicationReq proto.InternalMessageInfo

func (m *ApproveApplicationReq) GetApplicationId() string {
	if m != nil {
		return m.ApplicationId
	}
	return ""
}

func (m *ApproveApplicationReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *ApproveApplicationReq) GetStep() ApplicationStep {
	if m != nil {
		return m.Step
	}
	return ApplicationStep_StepWaitSubmit
}

func (m *ApproveApplicationReq) GetNotificationId() uint32 {
	if m != nil {
		return m.NotificationId
	}
	return 0
}

type ApproveApplicationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApproveApplicationResp) Reset()         { *m = ApproveApplicationResp{} }
func (m *ApproveApplicationResp) String() string { return proto.CompactTextString(m) }
func (*ApproveApplicationResp) ProtoMessage()    {}
func (*ApproveApplicationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{17}
}
func (m *ApproveApplicationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApproveApplicationResp.Unmarshal(m, b)
}
func (m *ApproveApplicationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApproveApplicationResp.Marshal(b, m, deterministic)
}
func (dst *ApproveApplicationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApproveApplicationResp.Merge(dst, src)
}
func (m *ApproveApplicationResp) XXX_Size() int {
	return xxx_messageInfo_ApproveApplicationResp.Size(m)
}
func (m *ApproveApplicationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApproveApplicationResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApproveApplicationResp proto.InternalMessageInfo

type RejectApplicationReq struct {
	ApplicationId        string          `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	Operator             string          `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	Step                 ApplicationStep `protobuf:"varint,3,opt,name=step,proto3,enum=guild_cooperation.ApplicationStep" json:"step,omitempty"`
	NotificationId       uint32          `protobuf:"varint,4,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *RejectApplicationReq) Reset()         { *m = RejectApplicationReq{} }
func (m *RejectApplicationReq) String() string { return proto.CompactTextString(m) }
func (*RejectApplicationReq) ProtoMessage()    {}
func (*RejectApplicationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{18}
}
func (m *RejectApplicationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RejectApplicationReq.Unmarshal(m, b)
}
func (m *RejectApplicationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RejectApplicationReq.Marshal(b, m, deterministic)
}
func (dst *RejectApplicationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RejectApplicationReq.Merge(dst, src)
}
func (m *RejectApplicationReq) XXX_Size() int {
	return xxx_messageInfo_RejectApplicationReq.Size(m)
}
func (m *RejectApplicationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RejectApplicationReq.DiscardUnknown(m)
}

var xxx_messageInfo_RejectApplicationReq proto.InternalMessageInfo

func (m *RejectApplicationReq) GetApplicationId() string {
	if m != nil {
		return m.ApplicationId
	}
	return ""
}

func (m *RejectApplicationReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *RejectApplicationReq) GetStep() ApplicationStep {
	if m != nil {
		return m.Step
	}
	return ApplicationStep_StepWaitSubmit
}

func (m *RejectApplicationReq) GetNotificationId() uint32 {
	if m != nil {
		return m.NotificationId
	}
	return 0
}

type RejectApplicationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RejectApplicationResp) Reset()         { *m = RejectApplicationResp{} }
func (m *RejectApplicationResp) String() string { return proto.CompactTextString(m) }
func (*RejectApplicationResp) ProtoMessage()    {}
func (*RejectApplicationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{19}
}
func (m *RejectApplicationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RejectApplicationResp.Unmarshal(m, b)
}
func (m *RejectApplicationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RejectApplicationResp.Marshal(b, m, deterministic)
}
func (dst *RejectApplicationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RejectApplicationResp.Merge(dst, src)
}
func (m *RejectApplicationResp) XXX_Size() int {
	return xxx_messageInfo_RejectApplicationResp.Size(m)
}
func (m *RejectApplicationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RejectApplicationResp.DiscardUnknown(m)
}

var xxx_messageInfo_RejectApplicationResp proto.InternalMessageInfo

type EditNotificationReq struct {
	NotificationId       uint32   `protobuf:"varint,2,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Content              string   `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	Operator             string   `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator,omitempty"`
	Usage                string   `protobuf:"bytes,6,opt,name=usage,proto3" json:"usage,omitempty"`
	MsgType              MsgType  `protobuf:"varint,7,opt,name=msg_type,json=msgType,proto3,enum=guild_cooperation.MsgType" json:"msg_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EditNotificationReq) Reset()         { *m = EditNotificationReq{} }
func (m *EditNotificationReq) String() string { return proto.CompactTextString(m) }
func (*EditNotificationReq) ProtoMessage()    {}
func (*EditNotificationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{20}
}
func (m *EditNotificationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditNotificationReq.Unmarshal(m, b)
}
func (m *EditNotificationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditNotificationReq.Marshal(b, m, deterministic)
}
func (dst *EditNotificationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditNotificationReq.Merge(dst, src)
}
func (m *EditNotificationReq) XXX_Size() int {
	return xxx_messageInfo_EditNotificationReq.Size(m)
}
func (m *EditNotificationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EditNotificationReq.DiscardUnknown(m)
}

var xxx_messageInfo_EditNotificationReq proto.InternalMessageInfo

func (m *EditNotificationReq) GetNotificationId() uint32 {
	if m != nil {
		return m.NotificationId
	}
	return 0
}

func (m *EditNotificationReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *EditNotificationReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *EditNotificationReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *EditNotificationReq) GetUsage() string {
	if m != nil {
		return m.Usage
	}
	return ""
}

func (m *EditNotificationReq) GetMsgType() MsgType {
	if m != nil {
		return m.MsgType
	}
	return MsgType_MSG_TYPE_INVALID
}

type EditNotificationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EditNotificationResp) Reset()         { *m = EditNotificationResp{} }
func (m *EditNotificationResp) String() string { return proto.CompactTextString(m) }
func (*EditNotificationResp) ProtoMessage()    {}
func (*EditNotificationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{21}
}
func (m *EditNotificationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditNotificationResp.Unmarshal(m, b)
}
func (m *EditNotificationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditNotificationResp.Marshal(b, m, deterministic)
}
func (dst *EditNotificationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditNotificationResp.Merge(dst, src)
}
func (m *EditNotificationResp) XXX_Size() int {
	return xxx_messageInfo_EditNotificationResp.Size(m)
}
func (m *EditNotificationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EditNotificationResp.DiscardUnknown(m)
}

var xxx_messageInfo_EditNotificationResp proto.InternalMessageInfo

type Notification struct {
	NotificationId       uint32   `protobuf:"varint,1,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Content              string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	CreateTime           uint32   `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Operator             string   `protobuf:"bytes,6,opt,name=operator,proto3" json:"operator,omitempty"`
	Usage                string   `protobuf:"bytes,7,opt,name=usage,proto3" json:"usage,omitempty"`
	MsgType              MsgType  `protobuf:"varint,8,opt,name=msg_type,json=msgType,proto3,enum=guild_cooperation.MsgType" json:"msg_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Notification) Reset()         { *m = Notification{} }
func (m *Notification) String() string { return proto.CompactTextString(m) }
func (*Notification) ProtoMessage()    {}
func (*Notification) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{22}
}
func (m *Notification) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Notification.Unmarshal(m, b)
}
func (m *Notification) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Notification.Marshal(b, m, deterministic)
}
func (dst *Notification) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Notification.Merge(dst, src)
}
func (m *Notification) XXX_Size() int {
	return xxx_messageInfo_Notification.Size(m)
}
func (m *Notification) XXX_DiscardUnknown() {
	xxx_messageInfo_Notification.DiscardUnknown(m)
}

var xxx_messageInfo_Notification proto.InternalMessageInfo

func (m *Notification) GetNotificationId() uint32 {
	if m != nil {
		return m.NotificationId
	}
	return 0
}

func (m *Notification) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *Notification) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *Notification) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *Notification) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *Notification) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *Notification) GetUsage() string {
	if m != nil {
		return m.Usage
	}
	return ""
}

func (m *Notification) GetMsgType() MsgType {
	if m != nil {
		return m.MsgType
	}
	return MsgType_MSG_TYPE_INVALID
}

type GetNotificationListReq struct {
	MsgType              MsgType  `protobuf:"varint,1,opt,name=msg_type,json=msgType,proto3,enum=guild_cooperation.MsgType" json:"msg_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNotificationListReq) Reset()         { *m = GetNotificationListReq{} }
func (m *GetNotificationListReq) String() string { return proto.CompactTextString(m) }
func (*GetNotificationListReq) ProtoMessage()    {}
func (*GetNotificationListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{23}
}
func (m *GetNotificationListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNotificationListReq.Unmarshal(m, b)
}
func (m *GetNotificationListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNotificationListReq.Marshal(b, m, deterministic)
}
func (dst *GetNotificationListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNotificationListReq.Merge(dst, src)
}
func (m *GetNotificationListReq) XXX_Size() int {
	return xxx_messageInfo_GetNotificationListReq.Size(m)
}
func (m *GetNotificationListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNotificationListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNotificationListReq proto.InternalMessageInfo

func (m *GetNotificationListReq) GetMsgType() MsgType {
	if m != nil {
		return m.MsgType
	}
	return MsgType_MSG_TYPE_INVALID
}

type GetNotificationListResp struct {
	Notifications        []*Notification `protobuf:"bytes,1,rep,name=notifications,proto3" json:"notifications,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetNotificationListResp) Reset()         { *m = GetNotificationListResp{} }
func (m *GetNotificationListResp) String() string { return proto.CompactTextString(m) }
func (*GetNotificationListResp) ProtoMessage()    {}
func (*GetNotificationListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{24}
}
func (m *GetNotificationListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNotificationListResp.Unmarshal(m, b)
}
func (m *GetNotificationListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNotificationListResp.Marshal(b, m, deterministic)
}
func (dst *GetNotificationListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNotificationListResp.Merge(dst, src)
}
func (m *GetNotificationListResp) XXX_Size() int {
	return xxx_messageInfo_GetNotificationListResp.Size(m)
}
func (m *GetNotificationListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNotificationListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNotificationListResp proto.InternalMessageInfo

func (m *GetNotificationListResp) GetNotifications() []*Notification {
	if m != nil {
		return m.Notifications
	}
	return nil
}

type DeleteNotificationReq struct {
	NotificationId       uint32   `protobuf:"varint,2,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteNotificationReq) Reset()         { *m = DeleteNotificationReq{} }
func (m *DeleteNotificationReq) String() string { return proto.CompactTextString(m) }
func (*DeleteNotificationReq) ProtoMessage()    {}
func (*DeleteNotificationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{25}
}
func (m *DeleteNotificationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteNotificationReq.Unmarshal(m, b)
}
func (m *DeleteNotificationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteNotificationReq.Marshal(b, m, deterministic)
}
func (dst *DeleteNotificationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteNotificationReq.Merge(dst, src)
}
func (m *DeleteNotificationReq) XXX_Size() int {
	return xxx_messageInfo_DeleteNotificationReq.Size(m)
}
func (m *DeleteNotificationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteNotificationReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteNotificationReq proto.InternalMessageInfo

func (m *DeleteNotificationReq) GetNotificationId() uint32 {
	if m != nil {
		return m.NotificationId
	}
	return 0
}

type DeleteNotificationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteNotificationResp) Reset()         { *m = DeleteNotificationResp{} }
func (m *DeleteNotificationResp) String() string { return proto.CompactTextString(m) }
func (*DeleteNotificationResp) ProtoMessage()    {}
func (*DeleteNotificationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{26}
}
func (m *DeleteNotificationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteNotificationResp.Unmarshal(m, b)
}
func (m *DeleteNotificationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteNotificationResp.Marshal(b, m, deterministic)
}
func (dst *DeleteNotificationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteNotificationResp.Merge(dst, src)
}
func (m *DeleteNotificationResp) XXX_Size() int {
	return xxx_messageInfo_DeleteNotificationResp.Size(m)
}
func (m *DeleteNotificationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteNotificationResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteNotificationResp proto.InternalMessageInfo

type CreateApplyLimitReq struct {
	Ttid                 string   `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid,omitempty"`
	StartTime            uint32   `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Operator             string   `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator,omitempty"`
	Id                   uint32   `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateApplyLimitReq) Reset()         { *m = CreateApplyLimitReq{} }
func (m *CreateApplyLimitReq) String() string { return proto.CompactTextString(m) }
func (*CreateApplyLimitReq) ProtoMessage()    {}
func (*CreateApplyLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{27}
}
func (m *CreateApplyLimitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateApplyLimitReq.Unmarshal(m, b)
}
func (m *CreateApplyLimitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateApplyLimitReq.Marshal(b, m, deterministic)
}
func (dst *CreateApplyLimitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateApplyLimitReq.Merge(dst, src)
}
func (m *CreateApplyLimitReq) XXX_Size() int {
	return xxx_messageInfo_CreateApplyLimitReq.Size(m)
}
func (m *CreateApplyLimitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateApplyLimitReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateApplyLimitReq proto.InternalMessageInfo

func (m *CreateApplyLimitReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *CreateApplyLimitReq) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *CreateApplyLimitReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *CreateApplyLimitReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *CreateApplyLimitReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type CreateApplyLimitResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateApplyLimitResp) Reset()         { *m = CreateApplyLimitResp{} }
func (m *CreateApplyLimitResp) String() string { return proto.CompactTextString(m) }
func (*CreateApplyLimitResp) ProtoMessage()    {}
func (*CreateApplyLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{28}
}
func (m *CreateApplyLimitResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateApplyLimitResp.Unmarshal(m, b)
}
func (m *CreateApplyLimitResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateApplyLimitResp.Marshal(b, m, deterministic)
}
func (dst *CreateApplyLimitResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateApplyLimitResp.Merge(dst, src)
}
func (m *CreateApplyLimitResp) XXX_Size() int {
	return xxx_messageInfo_CreateApplyLimitResp.Size(m)
}
func (m *CreateApplyLimitResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateApplyLimitResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateApplyLimitResp proto.InternalMessageInfo

type ApplyLimit struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	StartTime            uint32   `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Operator             string   `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator,omitempty"`
	CreateTime           uint32   `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Id                   uint32   `protobuf:"varint,8,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyLimit) Reset()         { *m = ApplyLimit{} }
func (m *ApplyLimit) String() string { return proto.CompactTextString(m) }
func (*ApplyLimit) ProtoMessage()    {}
func (*ApplyLimit) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{29}
}
func (m *ApplyLimit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyLimit.Unmarshal(m, b)
}
func (m *ApplyLimit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyLimit.Marshal(b, m, deterministic)
}
func (dst *ApplyLimit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyLimit.Merge(dst, src)
}
func (m *ApplyLimit) XXX_Size() int {
	return xxx_messageInfo_ApplyLimit.Size(m)
}
func (m *ApplyLimit) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyLimit.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyLimit proto.InternalMessageInfo

func (m *ApplyLimit) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApplyLimit) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *ApplyLimit) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *ApplyLimit) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ApplyLimit) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *ApplyLimit) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *ApplyLimit) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *ApplyLimit) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetApplyLimitListReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Ttid                 string   `protobuf:"bytes,3,opt,name=ttid,proto3" json:"ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetApplyLimitListReq) Reset()         { *m = GetApplyLimitListReq{} }
func (m *GetApplyLimitListReq) String() string { return proto.CompactTextString(m) }
func (*GetApplyLimitListReq) ProtoMessage()    {}
func (*GetApplyLimitListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{30}
}
func (m *GetApplyLimitListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplyLimitListReq.Unmarshal(m, b)
}
func (m *GetApplyLimitListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplyLimitListReq.Marshal(b, m, deterministic)
}
func (dst *GetApplyLimitListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplyLimitListReq.Merge(dst, src)
}
func (m *GetApplyLimitListReq) XXX_Size() int {
	return xxx_messageInfo_GetApplyLimitListReq.Size(m)
}
func (m *GetApplyLimitListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplyLimitListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplyLimitListReq proto.InternalMessageInfo

func (m *GetApplyLimitListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetApplyLimitListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetApplyLimitListReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

type GetApplyLimitListResp struct {
	ApplyLimits          []*ApplyLimit `protobuf:"bytes,1,rep,name=apply_limits,json=applyLimits,proto3" json:"apply_limits,omitempty"`
	Total                uint32        `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetApplyLimitListResp) Reset()         { *m = GetApplyLimitListResp{} }
func (m *GetApplyLimitListResp) String() string { return proto.CompactTextString(m) }
func (*GetApplyLimitListResp) ProtoMessage()    {}
func (*GetApplyLimitListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{31}
}
func (m *GetApplyLimitListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplyLimitListResp.Unmarshal(m, b)
}
func (m *GetApplyLimitListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplyLimitListResp.Marshal(b, m, deterministic)
}
func (dst *GetApplyLimitListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplyLimitListResp.Merge(dst, src)
}
func (m *GetApplyLimitListResp) XXX_Size() int {
	return xxx_messageInfo_GetApplyLimitListResp.Size(m)
}
func (m *GetApplyLimitListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplyLimitListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplyLimitListResp proto.InternalMessageInfo

func (m *GetApplyLimitListResp) GetApplyLimits() []*ApplyLimit {
	if m != nil {
		return m.ApplyLimits
	}
	return nil
}

func (m *GetApplyLimitListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DeleteApplyLimitReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteApplyLimitReq) Reset()         { *m = DeleteApplyLimitReq{} }
func (m *DeleteApplyLimitReq) String() string { return proto.CompactTextString(m) }
func (*DeleteApplyLimitReq) ProtoMessage()    {}
func (*DeleteApplyLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{32}
}
func (m *DeleteApplyLimitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteApplyLimitReq.Unmarshal(m, b)
}
func (m *DeleteApplyLimitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteApplyLimitReq.Marshal(b, m, deterministic)
}
func (dst *DeleteApplyLimitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteApplyLimitReq.Merge(dst, src)
}
func (m *DeleteApplyLimitReq) XXX_Size() int {
	return xxx_messageInfo_DeleteApplyLimitReq.Size(m)
}
func (m *DeleteApplyLimitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteApplyLimitReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteApplyLimitReq proto.InternalMessageInfo

func (m *DeleteApplyLimitReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type DeleteApplyLimitResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteApplyLimitResp) Reset()         { *m = DeleteApplyLimitResp{} }
func (m *DeleteApplyLimitResp) String() string { return proto.CompactTextString(m) }
func (*DeleteApplyLimitResp) ProtoMessage()    {}
func (*DeleteApplyLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{33}
}
func (m *DeleteApplyLimitResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteApplyLimitResp.Unmarshal(m, b)
}
func (m *DeleteApplyLimitResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteApplyLimitResp.Marshal(b, m, deterministic)
}
func (dst *DeleteApplyLimitResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteApplyLimitResp.Merge(dst, src)
}
func (m *DeleteApplyLimitResp) XXX_Size() int {
	return xxx_messageInfo_DeleteApplyLimitResp.Size(m)
}
func (m *DeleteApplyLimitResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteApplyLimitResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteApplyLimitResp proto.InternalMessageInfo

// 获取公会信息
type GetGuildInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildInfoReq) Reset()         { *m = GetGuildInfoReq{} }
func (m *GetGuildInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildInfoReq) ProtoMessage()    {}
func (*GetGuildInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{34}
}
func (m *GetGuildInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildInfoReq.Unmarshal(m, b)
}
func (m *GetGuildInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildInfoReq.Merge(dst, src)
}
func (m *GetGuildInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildInfoReq.Size(m)
}
func (m *GetGuildInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildInfoReq proto.InternalMessageInfo

func (m *GetGuildInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetGuildInfoResp struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildOwner           uint32   `protobuf:"varint,2,opt,name=guild_owner,json=guildOwner,proto3" json:"guild_owner,omitempty"`
	GuildName            string   `protobuf:"bytes,3,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	IsRealName           bool     `protobuf:"varint,4,opt,name=is_real_name,json=isRealName,proto3" json:"is_real_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildInfoResp) Reset()         { *m = GetGuildInfoResp{} }
func (m *GetGuildInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildInfoResp) ProtoMessage()    {}
func (*GetGuildInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{35}
}
func (m *GetGuildInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildInfoResp.Unmarshal(m, b)
}
func (m *GetGuildInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildInfoResp.Merge(dst, src)
}
func (m *GetGuildInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildInfoResp.Size(m)
}
func (m *GetGuildInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildInfoResp proto.InternalMessageInfo

func (m *GetGuildInfoResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildInfoResp) GetGuildOwner() uint32 {
	if m != nil {
		return m.GuildOwner
	}
	return 0
}

func (m *GetGuildInfoResp) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *GetGuildInfoResp) GetIsRealName() bool {
	if m != nil {
		return m.IsRealName
	}
	return false
}

type OperateHistory struct {
	ApplicationId        string               `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	Operator             string               `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	CreateTime           uint32               `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	CooperationType      CooperationType      `protobuf:"varint,4,opt,name=cooperation_type,json=cooperationType,proto3,enum=guild_cooperation.CooperationType" json:"cooperation_type,omitempty"`
	Uid                  uint32               `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string               `protobuf:"bytes,6,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Step                 ApplicationStep      `protobuf:"varint,8,opt,name=step,proto3,enum=guild_cooperation.ApplicationStep" json:"step,omitempty"`
	Operation            ApplicationOperation `protobuf:"varint,9,opt,name=operation,proto3,enum=guild_cooperation.ApplicationOperation" json:"operation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *OperateHistory) Reset()         { *m = OperateHistory{} }
func (m *OperateHistory) String() string { return proto.CompactTextString(m) }
func (*OperateHistory) ProtoMessage()    {}
func (*OperateHistory) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{36}
}
func (m *OperateHistory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OperateHistory.Unmarshal(m, b)
}
func (m *OperateHistory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OperateHistory.Marshal(b, m, deterministic)
}
func (dst *OperateHistory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OperateHistory.Merge(dst, src)
}
func (m *OperateHistory) XXX_Size() int {
	return xxx_messageInfo_OperateHistory.Size(m)
}
func (m *OperateHistory) XXX_DiscardUnknown() {
	xxx_messageInfo_OperateHistory.DiscardUnknown(m)
}

var xxx_messageInfo_OperateHistory proto.InternalMessageInfo

func (m *OperateHistory) GetApplicationId() string {
	if m != nil {
		return m.ApplicationId
	}
	return ""
}

func (m *OperateHistory) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *OperateHistory) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *OperateHistory) GetCooperationType() CooperationType {
	if m != nil {
		return m.CooperationType
	}
	return CooperationType_CTypeInvalid
}

func (m *OperateHistory) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *OperateHistory) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *OperateHistory) GetStep() ApplicationStep {
	if m != nil {
		return m.Step
	}
	return ApplicationStep_StepWaitSubmit
}

func (m *OperateHistory) GetOperation() ApplicationOperation {
	if m != nil {
		return m.Operation
	}
	return ApplicationOperation_OperationInvalid
}

type GetOperationHistoryReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Ttid                 string   `protobuf:"bytes,3,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Operator             string   `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOperationHistoryReq) Reset()         { *m = GetOperationHistoryReq{} }
func (m *GetOperationHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetOperationHistoryReq) ProtoMessage()    {}
func (*GetOperationHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{37}
}
func (m *GetOperationHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOperationHistoryReq.Unmarshal(m, b)
}
func (m *GetOperationHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOperationHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetOperationHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOperationHistoryReq.Merge(dst, src)
}
func (m *GetOperationHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetOperationHistoryReq.Size(m)
}
func (m *GetOperationHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOperationHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOperationHistoryReq proto.InternalMessageInfo

func (m *GetOperationHistoryReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetOperationHistoryReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetOperationHistoryReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *GetOperationHistoryReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type GetOperationHistoryResp struct {
	OperateHistories     []*OperateHistory `protobuf:"bytes,1,rep,name=operate_histories,json=operateHistories,proto3" json:"operate_histories,omitempty"`
	Total                uint32            `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetOperationHistoryResp) Reset()         { *m = GetOperationHistoryResp{} }
func (m *GetOperationHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetOperationHistoryResp) ProtoMessage()    {}
func (*GetOperationHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{38}
}
func (m *GetOperationHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOperationHistoryResp.Unmarshal(m, b)
}
func (m *GetOperationHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOperationHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetOperationHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOperationHistoryResp.Merge(dst, src)
}
func (m *GetOperationHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetOperationHistoryResp.Size(m)
}
func (m *GetOperationHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOperationHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOperationHistoryResp proto.InternalMessageInfo

func (m *GetOperationHistoryResp) GetOperateHistories() []*OperateHistory {
	if m != nil {
		return m.OperateHistories
	}
	return nil
}

func (m *GetOperationHistoryResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetApplicationInfoByUidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetApplicationInfoByUidReq) Reset()         { *m = GetApplicationInfoByUidReq{} }
func (m *GetApplicationInfoByUidReq) String() string { return proto.CompactTextString(m) }
func (*GetApplicationInfoByUidReq) ProtoMessage()    {}
func (*GetApplicationInfoByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{39}
}
func (m *GetApplicationInfoByUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplicationInfoByUidReq.Unmarshal(m, b)
}
func (m *GetApplicationInfoByUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplicationInfoByUidReq.Marshal(b, m, deterministic)
}
func (dst *GetApplicationInfoByUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplicationInfoByUidReq.Merge(dst, src)
}
func (m *GetApplicationInfoByUidReq) XXX_Size() int {
	return xxx_messageInfo_GetApplicationInfoByUidReq.Size(m)
}
func (m *GetApplicationInfoByUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplicationInfoByUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplicationInfoByUidReq proto.InternalMessageInfo

func (m *GetApplicationInfoByUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetApplicationInfoByUidResp struct {
	Application          *CooperationApplication `protobuf:"bytes,1,opt,name=application,proto3" json:"application,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetApplicationInfoByUidResp) Reset()         { *m = GetApplicationInfoByUidResp{} }
func (m *GetApplicationInfoByUidResp) String() string { return proto.CompactTextString(m) }
func (*GetApplicationInfoByUidResp) ProtoMessage()    {}
func (*GetApplicationInfoByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{40}
}
func (m *GetApplicationInfoByUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplicationInfoByUidResp.Unmarshal(m, b)
}
func (m *GetApplicationInfoByUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplicationInfoByUidResp.Marshal(b, m, deterministic)
}
func (dst *GetApplicationInfoByUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplicationInfoByUidResp.Merge(dst, src)
}
func (m *GetApplicationInfoByUidResp) XXX_Size() int {
	return xxx_messageInfo_GetApplicationInfoByUidResp.Size(m)
}
func (m *GetApplicationInfoByUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplicationInfoByUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplicationInfoByUidResp proto.InternalMessageInfo

func (m *GetApplicationInfoByUidResp) GetApplication() *CooperationApplication {
	if m != nil {
		return m.Application
	}
	return nil
}

type CooperationGuildInfo struct {
	GuildId              uint32          `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildDisplayId       uint32          `protobuf:"varint,2,opt,name=guild_display_id,json=guildDisplayId,proto3" json:"guild_display_id,omitempty"`
	GuildName            string          `protobuf:"bytes,3,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	Uid                  uint32          `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string          `protobuf:"bytes,5,opt,name=ttid,proto3" json:"ttid,omitempty"`
	NickName             string          `protobuf:"bytes,6,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	Operator             string          `protobuf:"bytes,7,opt,name=operator,proto3" json:"operator,omitempty"`
	JoinTime             int64           `protobuf:"varint,8,opt,name=join_time,json=joinTime,proto3" json:"join_time,omitempty"`
	CooperationType      CooperationType `protobuf:"varint,9,opt,name=cooperation_type,json=cooperationType,proto3,enum=guild_cooperation.CooperationType" json:"cooperation_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CooperationGuildInfo) Reset()         { *m = CooperationGuildInfo{} }
func (m *CooperationGuildInfo) String() string { return proto.CompactTextString(m) }
func (*CooperationGuildInfo) ProtoMessage()    {}
func (*CooperationGuildInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{41}
}
func (m *CooperationGuildInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CooperationGuildInfo.Unmarshal(m, b)
}
func (m *CooperationGuildInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CooperationGuildInfo.Marshal(b, m, deterministic)
}
func (dst *CooperationGuildInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CooperationGuildInfo.Merge(dst, src)
}
func (m *CooperationGuildInfo) XXX_Size() int {
	return xxx_messageInfo_CooperationGuildInfo.Size(m)
}
func (m *CooperationGuildInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CooperationGuildInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CooperationGuildInfo proto.InternalMessageInfo

func (m *CooperationGuildInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CooperationGuildInfo) GetGuildDisplayId() uint32 {
	if m != nil {
		return m.GuildDisplayId
	}
	return 0
}

func (m *CooperationGuildInfo) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *CooperationGuildInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CooperationGuildInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *CooperationGuildInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *CooperationGuildInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *CooperationGuildInfo) GetJoinTime() int64 {
	if m != nil {
		return m.JoinTime
	}
	return 0
}

func (m *CooperationGuildInfo) GetCooperationType() CooperationType {
	if m != nil {
		return m.CooperationType
	}
	return CooperationType_CTypeInvalid
}

type GetCooperationGuildListReq struct {
	Offset               uint32          `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32          `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	GuildId              uint32          `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint32          `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	Operator             string          `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator,omitempty"`
	CoopType             CooperationType `protobuf:"varint,6,opt,name=coop_type,json=coopType,proto3,enum=guild_cooperation.CooperationType" json:"coop_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetCooperationGuildListReq) Reset()         { *m = GetCooperationGuildListReq{} }
func (m *GetCooperationGuildListReq) String() string { return proto.CompactTextString(m) }
func (*GetCooperationGuildListReq) ProtoMessage()    {}
func (*GetCooperationGuildListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{42}
}
func (m *GetCooperationGuildListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCooperationGuildListReq.Unmarshal(m, b)
}
func (m *GetCooperationGuildListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCooperationGuildListReq.Marshal(b, m, deterministic)
}
func (dst *GetCooperationGuildListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCooperationGuildListReq.Merge(dst, src)
}
func (m *GetCooperationGuildListReq) XXX_Size() int {
	return xxx_messageInfo_GetCooperationGuildListReq.Size(m)
}
func (m *GetCooperationGuildListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCooperationGuildListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCooperationGuildListReq proto.InternalMessageInfo

func (m *GetCooperationGuildListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetCooperationGuildListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetCooperationGuildListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetCooperationGuildListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetCooperationGuildListReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *GetCooperationGuildListReq) GetCoopType() CooperationType {
	if m != nil {
		return m.CoopType
	}
	return CooperationType_CTypeInvalid
}

type GetCooperationGuildListResp struct {
	InfoList             []*CooperationGuildInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	Total                uint32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetCooperationGuildListResp) Reset()         { *m = GetCooperationGuildListResp{} }
func (m *GetCooperationGuildListResp) String() string { return proto.CompactTextString(m) }
func (*GetCooperationGuildListResp) ProtoMessage()    {}
func (*GetCooperationGuildListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{43}
}
func (m *GetCooperationGuildListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCooperationGuildListResp.Unmarshal(m, b)
}
func (m *GetCooperationGuildListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCooperationGuildListResp.Marshal(b, m, deterministic)
}
func (dst *GetCooperationGuildListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCooperationGuildListResp.Merge(dst, src)
}
func (m *GetCooperationGuildListResp) XXX_Size() int {
	return xxx_messageInfo_GetCooperationGuildListResp.Size(m)
}
func (m *GetCooperationGuildListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCooperationGuildListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCooperationGuildListResp proto.InternalMessageInfo

func (m *GetCooperationGuildListResp) GetInfoList() []*CooperationGuildInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetCooperationGuildListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type AddCooperationGuildReq struct {
	GuildId              uint32          `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	CoopType             CooperationType `protobuf:"varint,2,opt,name=coop_type,json=coopType,proto3,enum=guild_cooperation.CooperationType" json:"coop_type,omitempty"`
	Operator             string          `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AddCooperationGuildReq) Reset()         { *m = AddCooperationGuildReq{} }
func (m *AddCooperationGuildReq) String() string { return proto.CompactTextString(m) }
func (*AddCooperationGuildReq) ProtoMessage()    {}
func (*AddCooperationGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{44}
}
func (m *AddCooperationGuildReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCooperationGuildReq.Unmarshal(m, b)
}
func (m *AddCooperationGuildReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCooperationGuildReq.Marshal(b, m, deterministic)
}
func (dst *AddCooperationGuildReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCooperationGuildReq.Merge(dst, src)
}
func (m *AddCooperationGuildReq) XXX_Size() int {
	return xxx_messageInfo_AddCooperationGuildReq.Size(m)
}
func (m *AddCooperationGuildReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCooperationGuildReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddCooperationGuildReq proto.InternalMessageInfo

func (m *AddCooperationGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AddCooperationGuildReq) GetCoopType() CooperationType {
	if m != nil {
		return m.CoopType
	}
	return CooperationType_CTypeInvalid
}

func (m *AddCooperationGuildReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type AddCooperationGuildResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCooperationGuildResp) Reset()         { *m = AddCooperationGuildResp{} }
func (m *AddCooperationGuildResp) String() string { return proto.CompactTextString(m) }
func (*AddCooperationGuildResp) ProtoMessage()    {}
func (*AddCooperationGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{45}
}
func (m *AddCooperationGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCooperationGuildResp.Unmarshal(m, b)
}
func (m *AddCooperationGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCooperationGuildResp.Marshal(b, m, deterministic)
}
func (dst *AddCooperationGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCooperationGuildResp.Merge(dst, src)
}
func (m *AddCooperationGuildResp) XXX_Size() int {
	return xxx_messageInfo_AddCooperationGuildResp.Size(m)
}
func (m *AddCooperationGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCooperationGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddCooperationGuildResp proto.InternalMessageInfo

type BatchAddCooperationGuildReq struct {
	GuildList            []uint32        `protobuf:"varint,1,rep,packed,name=guild_list,json=guildList,proto3" json:"guild_list,omitempty"`
	CoopType             CooperationType `protobuf:"varint,2,opt,name=coop_type,json=coopType,proto3,enum=guild_cooperation.CooperationType" json:"coop_type,omitempty"`
	Operator             string          `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchAddCooperationGuildReq) Reset()         { *m = BatchAddCooperationGuildReq{} }
func (m *BatchAddCooperationGuildReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddCooperationGuildReq) ProtoMessage()    {}
func (*BatchAddCooperationGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{46}
}
func (m *BatchAddCooperationGuildReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddCooperationGuildReq.Unmarshal(m, b)
}
func (m *BatchAddCooperationGuildReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddCooperationGuildReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddCooperationGuildReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddCooperationGuildReq.Merge(dst, src)
}
func (m *BatchAddCooperationGuildReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddCooperationGuildReq.Size(m)
}
func (m *BatchAddCooperationGuildReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddCooperationGuildReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddCooperationGuildReq proto.InternalMessageInfo

func (m *BatchAddCooperationGuildReq) GetGuildList() []uint32 {
	if m != nil {
		return m.GuildList
	}
	return nil
}

func (m *BatchAddCooperationGuildReq) GetCoopType() CooperationType {
	if m != nil {
		return m.CoopType
	}
	return CooperationType_CTypeInvalid
}

func (m *BatchAddCooperationGuildReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type BatchAddCooperationGuildResp struct {
	SucceedList          []uint32 `protobuf:"varint,1,rep,packed,name=succeed_list,json=succeedList,proto3" json:"succeed_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddCooperationGuildResp) Reset()         { *m = BatchAddCooperationGuildResp{} }
func (m *BatchAddCooperationGuildResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddCooperationGuildResp) ProtoMessage()    {}
func (*BatchAddCooperationGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{47}
}
func (m *BatchAddCooperationGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddCooperationGuildResp.Unmarshal(m, b)
}
func (m *BatchAddCooperationGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddCooperationGuildResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddCooperationGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddCooperationGuildResp.Merge(dst, src)
}
func (m *BatchAddCooperationGuildResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddCooperationGuildResp.Size(m)
}
func (m *BatchAddCooperationGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddCooperationGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddCooperationGuildResp proto.InternalMessageInfo

func (m *BatchAddCooperationGuildResp) GetSucceedList() []uint32 {
	if m != nil {
		return m.SucceedList
	}
	return nil
}

type DelCooperationGuildReq struct {
	GuildId              uint32          `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	CoopType             CooperationType `protobuf:"varint,2,opt,name=coop_type,json=coopType,proto3,enum=guild_cooperation.CooperationType" json:"coop_type,omitempty"`
	Operator             string          `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *DelCooperationGuildReq) Reset()         { *m = DelCooperationGuildReq{} }
func (m *DelCooperationGuildReq) String() string { return proto.CompactTextString(m) }
func (*DelCooperationGuildReq) ProtoMessage()    {}
func (*DelCooperationGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{48}
}
func (m *DelCooperationGuildReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCooperationGuildReq.Unmarshal(m, b)
}
func (m *DelCooperationGuildReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCooperationGuildReq.Marshal(b, m, deterministic)
}
func (dst *DelCooperationGuildReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCooperationGuildReq.Merge(dst, src)
}
func (m *DelCooperationGuildReq) XXX_Size() int {
	return xxx_messageInfo_DelCooperationGuildReq.Size(m)
}
func (m *DelCooperationGuildReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCooperationGuildReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelCooperationGuildReq proto.InternalMessageInfo

func (m *DelCooperationGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *DelCooperationGuildReq) GetCoopType() CooperationType {
	if m != nil {
		return m.CoopType
	}
	return CooperationType_CTypeInvalid
}

func (m *DelCooperationGuildReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type DelCooperationGuildResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCooperationGuildResp) Reset()         { *m = DelCooperationGuildResp{} }
func (m *DelCooperationGuildResp) String() string { return proto.CompactTextString(m) }
func (*DelCooperationGuildResp) ProtoMessage()    {}
func (*DelCooperationGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{49}
}
func (m *DelCooperationGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCooperationGuildResp.Unmarshal(m, b)
}
func (m *DelCooperationGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCooperationGuildResp.Marshal(b, m, deterministic)
}
func (dst *DelCooperationGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCooperationGuildResp.Merge(dst, src)
}
func (m *DelCooperationGuildResp) XXX_Size() int {
	return xxx_messageInfo_DelCooperationGuildResp.Size(m)
}
func (m *DelCooperationGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCooperationGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelCooperationGuildResp proto.InternalMessageInfo

type CooperationOptInfo struct {
	GuildId              uint32          `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildDisplayId       uint32          `protobuf:"varint,2,opt,name=guild_display_id,json=guildDisplayId,proto3" json:"guild_display_id,omitempty"`
	GuildName            string          `protobuf:"bytes,3,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	Uid                  uint32          `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string          `protobuf:"bytes,5,opt,name=ttid,proto3" json:"ttid,omitempty"`
	OptType              CoopOptType     `protobuf:"varint,6,opt,name=opt_type,json=optType,proto3,enum=guild_cooperation.CoopOptType" json:"opt_type,omitempty"`
	OptTime              int64           `protobuf:"varint,7,opt,name=opt_time,json=optTime,proto3" json:"opt_time,omitempty"`
	Operator             string          `protobuf:"bytes,8,opt,name=operator,proto3" json:"operator,omitempty"`
	CooperationType      CooperationType `protobuf:"varint,9,opt,name=cooperation_type,json=cooperationType,proto3,enum=guild_cooperation.CooperationType" json:"cooperation_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CooperationOptInfo) Reset()         { *m = CooperationOptInfo{} }
func (m *CooperationOptInfo) String() string { return proto.CompactTextString(m) }
func (*CooperationOptInfo) ProtoMessage()    {}
func (*CooperationOptInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{50}
}
func (m *CooperationOptInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CooperationOptInfo.Unmarshal(m, b)
}
func (m *CooperationOptInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CooperationOptInfo.Marshal(b, m, deterministic)
}
func (dst *CooperationOptInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CooperationOptInfo.Merge(dst, src)
}
func (m *CooperationOptInfo) XXX_Size() int {
	return xxx_messageInfo_CooperationOptInfo.Size(m)
}
func (m *CooperationOptInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CooperationOptInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CooperationOptInfo proto.InternalMessageInfo

func (m *CooperationOptInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CooperationOptInfo) GetGuildDisplayId() uint32 {
	if m != nil {
		return m.GuildDisplayId
	}
	return 0
}

func (m *CooperationOptInfo) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *CooperationOptInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CooperationOptInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *CooperationOptInfo) GetOptType() CoopOptType {
	if m != nil {
		return m.OptType
	}
	return CoopOptType_InvalidType
}

func (m *CooperationOptInfo) GetOptTime() int64 {
	if m != nil {
		return m.OptTime
	}
	return 0
}

func (m *CooperationOptInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *CooperationOptInfo) GetCooperationType() CooperationType {
	if m != nil {
		return m.CooperationType
	}
	return CooperationType_CTypeInvalid
}

type GetCooperationOptListReq struct {
	Offset               uint32          `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32          `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	GuildId              uint32          `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint32          `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	CoopType             CooperationType `protobuf:"varint,5,opt,name=coop_type,json=coopType,proto3,enum=guild_cooperation.CooperationType" json:"coop_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetCooperationOptListReq) Reset()         { *m = GetCooperationOptListReq{} }
func (m *GetCooperationOptListReq) String() string { return proto.CompactTextString(m) }
func (*GetCooperationOptListReq) ProtoMessage()    {}
func (*GetCooperationOptListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{51}
}
func (m *GetCooperationOptListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCooperationOptListReq.Unmarshal(m, b)
}
func (m *GetCooperationOptListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCooperationOptListReq.Marshal(b, m, deterministic)
}
func (dst *GetCooperationOptListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCooperationOptListReq.Merge(dst, src)
}
func (m *GetCooperationOptListReq) XXX_Size() int {
	return xxx_messageInfo_GetCooperationOptListReq.Size(m)
}
func (m *GetCooperationOptListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCooperationOptListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCooperationOptListReq proto.InternalMessageInfo

func (m *GetCooperationOptListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetCooperationOptListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetCooperationOptListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetCooperationOptListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetCooperationOptListReq) GetCoopType() CooperationType {
	if m != nil {
		return m.CoopType
	}
	return CooperationType_CTypeInvalid
}

type GetCooperationOptListResp struct {
	List                 []*CooperationOptInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32                `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetCooperationOptListResp) Reset()         { *m = GetCooperationOptListResp{} }
func (m *GetCooperationOptListResp) String() string { return proto.CompactTextString(m) }
func (*GetCooperationOptListResp) ProtoMessage()    {}
func (*GetCooperationOptListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{52}
}
func (m *GetCooperationOptListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCooperationOptListResp.Unmarshal(m, b)
}
func (m *GetCooperationOptListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCooperationOptListResp.Marshal(b, m, deterministic)
}
func (dst *GetCooperationOptListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCooperationOptListResp.Merge(dst, src)
}
func (m *GetCooperationOptListResp) XXX_Size() int {
	return xxx_messageInfo_GetCooperationOptListResp.Size(m)
}
func (m *GetCooperationOptListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCooperationOptListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCooperationOptListResp proto.InternalMessageInfo

func (m *GetCooperationOptListResp) GetList() []*CooperationOptInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetCooperationOptListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetCooperationGuildIdsReq struct {
	CoopType             CooperationType `protobuf:"varint,1,opt,name=coop_type,json=coopType,proto3,enum=guild_cooperation.CooperationType" json:"coop_type,omitempty"`
	Offset               uint32          `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32          `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	GuildList            []uint32        `protobuf:"varint,4,rep,packed,name=guild_list,json=guildList,proto3" json:"guild_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetCooperationGuildIdsReq) Reset()         { *m = GetCooperationGuildIdsReq{} }
func (m *GetCooperationGuildIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetCooperationGuildIdsReq) ProtoMessage()    {}
func (*GetCooperationGuildIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{53}
}
func (m *GetCooperationGuildIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCooperationGuildIdsReq.Unmarshal(m, b)
}
func (m *GetCooperationGuildIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCooperationGuildIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetCooperationGuildIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCooperationGuildIdsReq.Merge(dst, src)
}
func (m *GetCooperationGuildIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetCooperationGuildIdsReq.Size(m)
}
func (m *GetCooperationGuildIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCooperationGuildIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCooperationGuildIdsReq proto.InternalMessageInfo

func (m *GetCooperationGuildIdsReq) GetCoopType() CooperationType {
	if m != nil {
		return m.CoopType
	}
	return CooperationType_CTypeInvalid
}

func (m *GetCooperationGuildIdsReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetCooperationGuildIdsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetCooperationGuildIdsReq) GetGuildList() []uint32 {
	if m != nil {
		return m.GuildList
	}
	return nil
}

type GetCooperationGuildIdsResp struct {
	GuildList            []uint32 `protobuf:"varint,1,rep,packed,name=guild_list,json=guildList,proto3" json:"guild_list,omitempty"`
	Total                uint32   `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCooperationGuildIdsResp) Reset()         { *m = GetCooperationGuildIdsResp{} }
func (m *GetCooperationGuildIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetCooperationGuildIdsResp) ProtoMessage()    {}
func (*GetCooperationGuildIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{54}
}
func (m *GetCooperationGuildIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCooperationGuildIdsResp.Unmarshal(m, b)
}
func (m *GetCooperationGuildIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCooperationGuildIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetCooperationGuildIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCooperationGuildIdsResp.Merge(dst, src)
}
func (m *GetCooperationGuildIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetCooperationGuildIdsResp.Size(m)
}
func (m *GetCooperationGuildIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCooperationGuildIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCooperationGuildIdsResp proto.InternalMessageInfo

func (m *GetCooperationGuildIdsResp) GetGuildList() []uint32 {
	if m != nil {
		return m.GuildList
	}
	return nil
}

func (m *GetCooperationGuildIdsResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetGuildCooperationInfosReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildCooperationInfosReq) Reset()         { *m = GetGuildCooperationInfosReq{} }
func (m *GetGuildCooperationInfosReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildCooperationInfosReq) ProtoMessage()    {}
func (*GetGuildCooperationInfosReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{55}
}
func (m *GetGuildCooperationInfosReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildCooperationInfosReq.Unmarshal(m, b)
}
func (m *GetGuildCooperationInfosReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildCooperationInfosReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildCooperationInfosReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildCooperationInfosReq.Merge(dst, src)
}
func (m *GetGuildCooperationInfosReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildCooperationInfosReq.Size(m)
}
func (m *GetGuildCooperationInfosReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildCooperationInfosReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildCooperationInfosReq proto.InternalMessageInfo

func (m *GetGuildCooperationInfosReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildCooperationInfosResp struct {
	IsAmuseCoopGuild     bool     `protobuf:"varint,1,opt,name=is_amuse_coop_guild,json=isAmuseCoopGuild,proto3" json:"is_amuse_coop_guild,omitempty"`
	IsYuyinCoopGuild     bool     `protobuf:"varint,2,opt,name=is_yuyin_coop_guild,json=isYuyinCoopGuild,proto3" json:"is_yuyin_coop_guild,omitempty"`
	IsEsportCoopGuild    bool     `protobuf:"varint,5,opt,name=is_esport_coop_guild,json=isEsportCoopGuild,proto3" json:"is_esport_coop_guild,omitempty"`
	AmuseApplication     uint32   `protobuf:"varint,3,opt,name=amuse_application,json=amuseApplication,proto3" json:"amuse_application,omitempty"`
	YuyinApplication     uint32   `protobuf:"varint,4,opt,name=yuyin_application,json=yuyinApplication,proto3" json:"yuyin_application,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildCooperationInfosResp) Reset()         { *m = GetGuildCooperationInfosResp{} }
func (m *GetGuildCooperationInfosResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildCooperationInfosResp) ProtoMessage()    {}
func (*GetGuildCooperationInfosResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{56}
}
func (m *GetGuildCooperationInfosResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildCooperationInfosResp.Unmarshal(m, b)
}
func (m *GetGuildCooperationInfosResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildCooperationInfosResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildCooperationInfosResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildCooperationInfosResp.Merge(dst, src)
}
func (m *GetGuildCooperationInfosResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildCooperationInfosResp.Size(m)
}
func (m *GetGuildCooperationInfosResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildCooperationInfosResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildCooperationInfosResp proto.InternalMessageInfo

func (m *GetGuildCooperationInfosResp) GetIsAmuseCoopGuild() bool {
	if m != nil {
		return m.IsAmuseCoopGuild
	}
	return false
}

func (m *GetGuildCooperationInfosResp) GetIsYuyinCoopGuild() bool {
	if m != nil {
		return m.IsYuyinCoopGuild
	}
	return false
}

func (m *GetGuildCooperationInfosResp) GetIsEsportCoopGuild() bool {
	if m != nil {
		return m.IsEsportCoopGuild
	}
	return false
}

func (m *GetGuildCooperationInfosResp) GetAmuseApplication() uint32 {
	if m != nil {
		return m.AmuseApplication
	}
	return 0
}

func (m *GetGuildCooperationInfosResp) GetYuyinApplication() uint32 {
	if m != nil {
		return m.YuyinApplication
	}
	return 0
}

type CooperationHistory struct {
	GuildId              uint32          `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	OptType              CoopOptType     `protobuf:"varint,2,opt,name=opt_type,json=optType,proto3,enum=guild_cooperation.CoopOptType" json:"opt_type,omitempty"`
	OptTime              int64           `protobuf:"varint,3,opt,name=opt_time,json=optTime,proto3" json:"opt_time,omitempty"`
	Operator             string          `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator,omitempty"`
	CooperationType      CooperationType `protobuf:"varint,5,opt,name=cooperation_type,json=cooperationType,proto3,enum=guild_cooperation.CooperationType" json:"cooperation_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CooperationHistory) Reset()         { *m = CooperationHistory{} }
func (m *CooperationHistory) String() string { return proto.CompactTextString(m) }
func (*CooperationHistory) ProtoMessage()    {}
func (*CooperationHistory) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{57}
}
func (m *CooperationHistory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CooperationHistory.Unmarshal(m, b)
}
func (m *CooperationHistory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CooperationHistory.Marshal(b, m, deterministic)
}
func (dst *CooperationHistory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CooperationHistory.Merge(dst, src)
}
func (m *CooperationHistory) XXX_Size() int {
	return xxx_messageInfo_CooperationHistory.Size(m)
}
func (m *CooperationHistory) XXX_DiscardUnknown() {
	xxx_messageInfo_CooperationHistory.DiscardUnknown(m)
}

var xxx_messageInfo_CooperationHistory proto.InternalMessageInfo

func (m *CooperationHistory) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CooperationHistory) GetOptType() CoopOptType {
	if m != nil {
		return m.OptType
	}
	return CoopOptType_InvalidType
}

func (m *CooperationHistory) GetOptTime() int64 {
	if m != nil {
		return m.OptTime
	}
	return 0
}

func (m *CooperationHistory) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *CooperationHistory) GetCooperationType() CooperationType {
	if m != nil {
		return m.CooperationType
	}
	return CooperationType_CTypeInvalid
}

type GetGuildCooperationHistoryReq struct {
	GuildId              uint32          `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	CoopType             CooperationType `protobuf:"varint,2,opt,name=coop_type,json=coopType,proto3,enum=guild_cooperation.CooperationType" json:"coop_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetGuildCooperationHistoryReq) Reset()         { *m = GetGuildCooperationHistoryReq{} }
func (m *GetGuildCooperationHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildCooperationHistoryReq) ProtoMessage()    {}
func (*GetGuildCooperationHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{58}
}
func (m *GetGuildCooperationHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildCooperationHistoryReq.Unmarshal(m, b)
}
func (m *GetGuildCooperationHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildCooperationHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildCooperationHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildCooperationHistoryReq.Merge(dst, src)
}
func (m *GetGuildCooperationHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildCooperationHistoryReq.Size(m)
}
func (m *GetGuildCooperationHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildCooperationHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildCooperationHistoryReq proto.InternalMessageInfo

func (m *GetGuildCooperationHistoryReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildCooperationHistoryReq) GetCoopType() CooperationType {
	if m != nil {
		return m.CoopType
	}
	return CooperationType_CTypeInvalid
}

type GetGuildCooperationHistoryResp struct {
	Infos                []*CooperationHistory `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetGuildCooperationHistoryResp) Reset()         { *m = GetGuildCooperationHistoryResp{} }
func (m *GetGuildCooperationHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildCooperationHistoryResp) ProtoMessage()    {}
func (*GetGuildCooperationHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{59}
}
func (m *GetGuildCooperationHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildCooperationHistoryResp.Unmarshal(m, b)
}
func (m *GetGuildCooperationHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildCooperationHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildCooperationHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildCooperationHistoryResp.Merge(dst, src)
}
func (m *GetGuildCooperationHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildCooperationHistoryResp.Size(m)
}
func (m *GetGuildCooperationHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildCooperationHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildCooperationHistoryResp proto.InternalMessageInfo

func (m *GetGuildCooperationHistoryResp) GetInfos() []*CooperationHistory {
	if m != nil {
		return m.Infos
	}
	return nil
}

type GetGuildCooperationWithTimeReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	SnapshotTime         int64    `protobuf:"varint,2,opt,name=snapshot_time,json=snapshotTime,proto3" json:"snapshot_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildCooperationWithTimeReq) Reset()         { *m = GetGuildCooperationWithTimeReq{} }
func (m *GetGuildCooperationWithTimeReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildCooperationWithTimeReq) ProtoMessage()    {}
func (*GetGuildCooperationWithTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{60}
}
func (m *GetGuildCooperationWithTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildCooperationWithTimeReq.Unmarshal(m, b)
}
func (m *GetGuildCooperationWithTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildCooperationWithTimeReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildCooperationWithTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildCooperationWithTimeReq.Merge(dst, src)
}
func (m *GetGuildCooperationWithTimeReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildCooperationWithTimeReq.Size(m)
}
func (m *GetGuildCooperationWithTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildCooperationWithTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildCooperationWithTimeReq proto.InternalMessageInfo

func (m *GetGuildCooperationWithTimeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildCooperationWithTimeReq) GetSnapshotTime() int64 {
	if m != nil {
		return m.SnapshotTime
	}
	return 0
}

type GetGuildCooperationWithTimeResp struct {
	IsExist              bool     `protobuf:"varint,1,opt,name=is_exist,json=isExist,proto3" json:"is_exist,omitempty"`
	IsAmuseCoopGuild     bool     `protobuf:"varint,2,opt,name=is_amuse_coop_guild,json=isAmuseCoopGuild,proto3" json:"is_amuse_coop_guild,omitempty"`
	IsYuyinCoopGuild     bool     `protobuf:"varint,3,opt,name=is_yuyin_coop_guild,json=isYuyinCoopGuild,proto3" json:"is_yuyin_coop_guild,omitempty"`
	IsEsportCoopGuild    bool     `protobuf:"varint,6,opt,name=is_esport_coop_guild,json=isEsportCoopGuild,proto3" json:"is_esport_coop_guild,omitempty"`
	GuildType            uint32   `protobuf:"varint,4,opt,name=guild_type,json=guildType,proto3" json:"guild_type,omitempty"`
	LibraryType          uint32   `protobuf:"varint,5,opt,name=library_type,json=libraryType,proto3" json:"library_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildCooperationWithTimeResp) Reset()         { *m = GetGuildCooperationWithTimeResp{} }
func (m *GetGuildCooperationWithTimeResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildCooperationWithTimeResp) ProtoMessage()    {}
func (*GetGuildCooperationWithTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{61}
}
func (m *GetGuildCooperationWithTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildCooperationWithTimeResp.Unmarshal(m, b)
}
func (m *GetGuildCooperationWithTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildCooperationWithTimeResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildCooperationWithTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildCooperationWithTimeResp.Merge(dst, src)
}
func (m *GetGuildCooperationWithTimeResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildCooperationWithTimeResp.Size(m)
}
func (m *GetGuildCooperationWithTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildCooperationWithTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildCooperationWithTimeResp proto.InternalMessageInfo

func (m *GetGuildCooperationWithTimeResp) GetIsExist() bool {
	if m != nil {
		return m.IsExist
	}
	return false
}

func (m *GetGuildCooperationWithTimeResp) GetIsAmuseCoopGuild() bool {
	if m != nil {
		return m.IsAmuseCoopGuild
	}
	return false
}

func (m *GetGuildCooperationWithTimeResp) GetIsYuyinCoopGuild() bool {
	if m != nil {
		return m.IsYuyinCoopGuild
	}
	return false
}

func (m *GetGuildCooperationWithTimeResp) GetIsEsportCoopGuild() bool {
	if m != nil {
		return m.IsEsportCoopGuild
	}
	return false
}

func (m *GetGuildCooperationWithTimeResp) GetGuildType() uint32 {
	if m != nil {
		return m.GuildType
	}
	return 0
}

func (m *GetGuildCooperationWithTimeResp) GetLibraryType() uint32 {
	if m != nil {
		return m.LibraryType
	}
	return 0
}

// 公会历史合作时间段
type CoopHistoryPeriod struct {
	CoopType             CooperationType `protobuf:"varint,1,opt,name=coop_type,json=coopType,proto3,enum=guild_cooperation.CooperationType" json:"coop_type,omitempty"`
	BeginTime            int64           `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64           `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CoopHistoryPeriod) Reset()         { *m = CoopHistoryPeriod{} }
func (m *CoopHistoryPeriod) String() string { return proto.CompactTextString(m) }
func (*CoopHistoryPeriod) ProtoMessage()    {}
func (*CoopHistoryPeriod) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{62}
}
func (m *CoopHistoryPeriod) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoopHistoryPeriod.Unmarshal(m, b)
}
func (m *CoopHistoryPeriod) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoopHistoryPeriod.Marshal(b, m, deterministic)
}
func (dst *CoopHistoryPeriod) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoopHistoryPeriod.Merge(dst, src)
}
func (m *CoopHistoryPeriod) XXX_Size() int {
	return xxx_messageInfo_CoopHistoryPeriod.Size(m)
}
func (m *CoopHistoryPeriod) XXX_DiscardUnknown() {
	xxx_messageInfo_CoopHistoryPeriod.DiscardUnknown(m)
}

var xxx_messageInfo_CoopHistoryPeriod proto.InternalMessageInfo

func (m *CoopHistoryPeriod) GetCoopType() CooperationType {
	if m != nil {
		return m.CoopType
	}
	return CooperationType_CTypeInvalid
}

func (m *CoopHistoryPeriod) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *CoopHistoryPeriod) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetGuildCooperationHistoryPeriodReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildCooperationHistoryPeriodReq) Reset()         { *m = GetGuildCooperationHistoryPeriodReq{} }
func (m *GetGuildCooperationHistoryPeriodReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildCooperationHistoryPeriodReq) ProtoMessage()    {}
func (*GetGuildCooperationHistoryPeriodReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{63}
}
func (m *GetGuildCooperationHistoryPeriodReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildCooperationHistoryPeriodReq.Unmarshal(m, b)
}
func (m *GetGuildCooperationHistoryPeriodReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildCooperationHistoryPeriodReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildCooperationHistoryPeriodReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildCooperationHistoryPeriodReq.Merge(dst, src)
}
func (m *GetGuildCooperationHistoryPeriodReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildCooperationHistoryPeriodReq.Size(m)
}
func (m *GetGuildCooperationHistoryPeriodReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildCooperationHistoryPeriodReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildCooperationHistoryPeriodReq proto.InternalMessageInfo

func (m *GetGuildCooperationHistoryPeriodReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildCooperationHistoryPeriodResp struct {
	Period               []*CoopHistoryPeriod `protobuf:"bytes,1,rep,name=period,proto3" json:"period,omitempty"`
	SnapshotTime         int64                `protobuf:"varint,2,opt,name=snapshot_time,json=snapshotTime,proto3" json:"snapshot_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetGuildCooperationHistoryPeriodResp) Reset()         { *m = GetGuildCooperationHistoryPeriodResp{} }
func (m *GetGuildCooperationHistoryPeriodResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildCooperationHistoryPeriodResp) ProtoMessage()    {}
func (*GetGuildCooperationHistoryPeriodResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{64}
}
func (m *GetGuildCooperationHistoryPeriodResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildCooperationHistoryPeriodResp.Unmarshal(m, b)
}
func (m *GetGuildCooperationHistoryPeriodResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildCooperationHistoryPeriodResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildCooperationHistoryPeriodResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildCooperationHistoryPeriodResp.Merge(dst, src)
}
func (m *GetGuildCooperationHistoryPeriodResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildCooperationHistoryPeriodResp.Size(m)
}
func (m *GetGuildCooperationHistoryPeriodResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildCooperationHistoryPeriodResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildCooperationHistoryPeriodResp proto.InternalMessageInfo

func (m *GetGuildCooperationHistoryPeriodResp) GetPeriod() []*CoopHistoryPeriod {
	if m != nil {
		return m.Period
	}
	return nil
}

func (m *GetGuildCooperationHistoryPeriodResp) GetSnapshotTime() int64 {
	if m != nil {
		return m.SnapshotTime
	}
	return 0
}

type GuildNoBonus struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Remark               string   `protobuf:"bytes,2,opt,name=remark,proto3" json:"remark,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildNoBonus) Reset()         { *m = GuildNoBonus{} }
func (m *GuildNoBonus) String() string { return proto.CompactTextString(m) }
func (*GuildNoBonus) ProtoMessage()    {}
func (*GuildNoBonus) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{65}
}
func (m *GuildNoBonus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildNoBonus.Unmarshal(m, b)
}
func (m *GuildNoBonus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildNoBonus.Marshal(b, m, deterministic)
}
func (dst *GuildNoBonus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildNoBonus.Merge(dst, src)
}
func (m *GuildNoBonus) XXX_Size() int {
	return xxx_messageInfo_GuildNoBonus.Size(m)
}
func (m *GuildNoBonus) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildNoBonus.DiscardUnknown(m)
}

var xxx_messageInfo_GuildNoBonus proto.InternalMessageInfo

func (m *GuildNoBonus) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildNoBonus) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

type GetAllNoBonusGuildReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllNoBonusGuildReq) Reset()         { *m = GetAllNoBonusGuildReq{} }
func (m *GetAllNoBonusGuildReq) String() string { return proto.CompactTextString(m) }
func (*GetAllNoBonusGuildReq) ProtoMessage()    {}
func (*GetAllNoBonusGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{66}
}
func (m *GetAllNoBonusGuildReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllNoBonusGuildReq.Unmarshal(m, b)
}
func (m *GetAllNoBonusGuildReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllNoBonusGuildReq.Marshal(b, m, deterministic)
}
func (dst *GetAllNoBonusGuildReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllNoBonusGuildReq.Merge(dst, src)
}
func (m *GetAllNoBonusGuildReq) XXX_Size() int {
	return xxx_messageInfo_GetAllNoBonusGuildReq.Size(m)
}
func (m *GetAllNoBonusGuildReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllNoBonusGuildReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllNoBonusGuildReq proto.InternalMessageInfo

type GetAllNoBonusGuildResp struct {
	GuildList            []*GuildNoBonus `protobuf:"bytes,1,rep,name=guild_list,json=guildList,proto3" json:"guild_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetAllNoBonusGuildResp) Reset()         { *m = GetAllNoBonusGuildResp{} }
func (m *GetAllNoBonusGuildResp) String() string { return proto.CompactTextString(m) }
func (*GetAllNoBonusGuildResp) ProtoMessage()    {}
func (*GetAllNoBonusGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{67}
}
func (m *GetAllNoBonusGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllNoBonusGuildResp.Unmarshal(m, b)
}
func (m *GetAllNoBonusGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllNoBonusGuildResp.Marshal(b, m, deterministic)
}
func (dst *GetAllNoBonusGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllNoBonusGuildResp.Merge(dst, src)
}
func (m *GetAllNoBonusGuildResp) XXX_Size() int {
	return xxx_messageInfo_GetAllNoBonusGuildResp.Size(m)
}
func (m *GetAllNoBonusGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllNoBonusGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllNoBonusGuildResp proto.InternalMessageInfo

func (m *GetAllNoBonusGuildResp) GetGuildList() []*GuildNoBonus {
	if m != nil {
		return m.GuildList
	}
	return nil
}

type SetNoBonusGuildReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Remark               string   `protobuf:"bytes,2,opt,name=remark,proto3" json:"remark,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetNoBonusGuildReq) Reset()         { *m = SetNoBonusGuildReq{} }
func (m *SetNoBonusGuildReq) String() string { return proto.CompactTextString(m) }
func (*SetNoBonusGuildReq) ProtoMessage()    {}
func (*SetNoBonusGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{68}
}
func (m *SetNoBonusGuildReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetNoBonusGuildReq.Unmarshal(m, b)
}
func (m *SetNoBonusGuildReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetNoBonusGuildReq.Marshal(b, m, deterministic)
}
func (dst *SetNoBonusGuildReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetNoBonusGuildReq.Merge(dst, src)
}
func (m *SetNoBonusGuildReq) XXX_Size() int {
	return xxx_messageInfo_SetNoBonusGuildReq.Size(m)
}
func (m *SetNoBonusGuildReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetNoBonusGuildReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetNoBonusGuildReq proto.InternalMessageInfo

func (m *SetNoBonusGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SetNoBonusGuildReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

type SetNoBonusGuildResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetNoBonusGuildResp) Reset()         { *m = SetNoBonusGuildResp{} }
func (m *SetNoBonusGuildResp) String() string { return proto.CompactTextString(m) }
func (*SetNoBonusGuildResp) ProtoMessage()    {}
func (*SetNoBonusGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{69}
}
func (m *SetNoBonusGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetNoBonusGuildResp.Unmarshal(m, b)
}
func (m *SetNoBonusGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetNoBonusGuildResp.Marshal(b, m, deterministic)
}
func (dst *SetNoBonusGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetNoBonusGuildResp.Merge(dst, src)
}
func (m *SetNoBonusGuildResp) XXX_Size() int {
	return xxx_messageInfo_SetNoBonusGuildResp.Size(m)
}
func (m *SetNoBonusGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetNoBonusGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetNoBonusGuildResp proto.InternalMessageInfo

// 合作变更事件
type CooperationChangeEvent struct {
	GuildId              uint32          `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	CoopType             CooperationType `protobuf:"varint,2,opt,name=coop_type,json=coopType,proto3,enum=guild_cooperation.CooperationType" json:"coop_type,omitempty"`
	OptType              CoopOptType     `protobuf:"varint,3,opt,name=opt_type,json=optType,proto3,enum=guild_cooperation.CoopOptType" json:"opt_type,omitempty"`
	ChangeTime           int64           `protobuf:"varint,4,opt,name=change_time,json=changeTime,proto3" json:"change_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CooperationChangeEvent) Reset()         { *m = CooperationChangeEvent{} }
func (m *CooperationChangeEvent) String() string { return proto.CompactTextString(m) }
func (*CooperationChangeEvent) ProtoMessage()    {}
func (*CooperationChangeEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_cooperation_673a2604e98b98c6, []int{70}
}
func (m *CooperationChangeEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CooperationChangeEvent.Unmarshal(m, b)
}
func (m *CooperationChangeEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CooperationChangeEvent.Marshal(b, m, deterministic)
}
func (dst *CooperationChangeEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CooperationChangeEvent.Merge(dst, src)
}
func (m *CooperationChangeEvent) XXX_Size() int {
	return xxx_messageInfo_CooperationChangeEvent.Size(m)
}
func (m *CooperationChangeEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_CooperationChangeEvent.DiscardUnknown(m)
}

var xxx_messageInfo_CooperationChangeEvent proto.InternalMessageInfo

func (m *CooperationChangeEvent) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CooperationChangeEvent) GetCoopType() CooperationType {
	if m != nil {
		return m.CoopType
	}
	return CooperationType_CTypeInvalid
}

func (m *CooperationChangeEvent) GetOptType() CoopOptType {
	if m != nil {
		return m.OptType
	}
	return CoopOptType_InvalidType
}

func (m *CooperationChangeEvent) GetChangeTime() int64 {
	if m != nil {
		return m.ChangeTime
	}
	return 0
}

func init() {
	proto.RegisterType((*CooperationInfo)(nil), "guild_cooperation.CooperationInfo")
	proto.RegisterType((*GetCooperationInfoReq)(nil), "guild_cooperation.GetCooperationInfoReq")
	proto.RegisterType((*GetCooperationInfoResp)(nil), "guild_cooperation.GetCooperationInfoResp")
	proto.RegisterType((*GetCooperationInfoByPhoneReq)(nil), "guild_cooperation.GetCooperationInfoByPhoneReq")
	proto.RegisterType((*GetCooperationInfoByPhoneResp)(nil), "guild_cooperation.GetCooperationInfoByPhoneResp")
	proto.RegisterType((*SubmitApplicationReq)(nil), "guild_cooperation.SubmitApplicationReq")
	proto.RegisterType((*SubmitApplicationResp)(nil), "guild_cooperation.SubmitApplicationResp")
	proto.RegisterType((*CreateGuildReq)(nil), "guild_cooperation.CreateGuildReq")
	proto.RegisterType((*CreateGuildResp)(nil), "guild_cooperation.CreateGuildResp")
	proto.RegisterType((*ApplyCooperationReq)(nil), "guild_cooperation.ApplyCooperationReq")
	proto.RegisterType((*ApplyCooperationResp)(nil), "guild_cooperation.ApplyCooperationResp")
	proto.RegisterType((*CooperationApplication)(nil), "guild_cooperation.CooperationApplication")
	proto.RegisterType((*GetApplicationsReq)(nil), "guild_cooperation.GetApplicationsReq")
	proto.RegisterType((*GetApplicationsResp)(nil), "guild_cooperation.GetApplicationsResp")
	proto.RegisterType((*GetApplicationInfoReq)(nil), "guild_cooperation.GetApplicationInfoReq")
	proto.RegisterType((*GetApplicationInfoResp)(nil), "guild_cooperation.GetApplicationInfoResp")
	proto.RegisterType((*ApproveApplicationReq)(nil), "guild_cooperation.ApproveApplicationReq")
	proto.RegisterType((*ApproveApplicationResp)(nil), "guild_cooperation.ApproveApplicationResp")
	proto.RegisterType((*RejectApplicationReq)(nil), "guild_cooperation.RejectApplicationReq")
	proto.RegisterType((*RejectApplicationResp)(nil), "guild_cooperation.RejectApplicationResp")
	proto.RegisterType((*EditNotificationReq)(nil), "guild_cooperation.EditNotificationReq")
	proto.RegisterType((*EditNotificationResp)(nil), "guild_cooperation.EditNotificationResp")
	proto.RegisterType((*Notification)(nil), "guild_cooperation.Notification")
	proto.RegisterType((*GetNotificationListReq)(nil), "guild_cooperation.GetNotificationListReq")
	proto.RegisterType((*GetNotificationListResp)(nil), "guild_cooperation.GetNotificationListResp")
	proto.RegisterType((*DeleteNotificationReq)(nil), "guild_cooperation.DeleteNotificationReq")
	proto.RegisterType((*DeleteNotificationResp)(nil), "guild_cooperation.DeleteNotificationResp")
	proto.RegisterType((*CreateApplyLimitReq)(nil), "guild_cooperation.CreateApplyLimitReq")
	proto.RegisterType((*CreateApplyLimitResp)(nil), "guild_cooperation.CreateApplyLimitResp")
	proto.RegisterType((*ApplyLimit)(nil), "guild_cooperation.ApplyLimit")
	proto.RegisterType((*GetApplyLimitListReq)(nil), "guild_cooperation.GetApplyLimitListReq")
	proto.RegisterType((*GetApplyLimitListResp)(nil), "guild_cooperation.GetApplyLimitListResp")
	proto.RegisterType((*DeleteApplyLimitReq)(nil), "guild_cooperation.DeleteApplyLimitReq")
	proto.RegisterType((*DeleteApplyLimitResp)(nil), "guild_cooperation.DeleteApplyLimitResp")
	proto.RegisterType((*GetGuildInfoReq)(nil), "guild_cooperation.GetGuildInfoReq")
	proto.RegisterType((*GetGuildInfoResp)(nil), "guild_cooperation.GetGuildInfoResp")
	proto.RegisterType((*OperateHistory)(nil), "guild_cooperation.OperateHistory")
	proto.RegisterType((*GetOperationHistoryReq)(nil), "guild_cooperation.GetOperationHistoryReq")
	proto.RegisterType((*GetOperationHistoryResp)(nil), "guild_cooperation.GetOperationHistoryResp")
	proto.RegisterType((*GetApplicationInfoByUidReq)(nil), "guild_cooperation.GetApplicationInfoByUidReq")
	proto.RegisterType((*GetApplicationInfoByUidResp)(nil), "guild_cooperation.GetApplicationInfoByUidResp")
	proto.RegisterType((*CooperationGuildInfo)(nil), "guild_cooperation.CooperationGuildInfo")
	proto.RegisterType((*GetCooperationGuildListReq)(nil), "guild_cooperation.GetCooperationGuildListReq")
	proto.RegisterType((*GetCooperationGuildListResp)(nil), "guild_cooperation.GetCooperationGuildListResp")
	proto.RegisterType((*AddCooperationGuildReq)(nil), "guild_cooperation.AddCooperationGuildReq")
	proto.RegisterType((*AddCooperationGuildResp)(nil), "guild_cooperation.AddCooperationGuildResp")
	proto.RegisterType((*BatchAddCooperationGuildReq)(nil), "guild_cooperation.BatchAddCooperationGuildReq")
	proto.RegisterType((*BatchAddCooperationGuildResp)(nil), "guild_cooperation.BatchAddCooperationGuildResp")
	proto.RegisterType((*DelCooperationGuildReq)(nil), "guild_cooperation.DelCooperationGuildReq")
	proto.RegisterType((*DelCooperationGuildResp)(nil), "guild_cooperation.DelCooperationGuildResp")
	proto.RegisterType((*CooperationOptInfo)(nil), "guild_cooperation.CooperationOptInfo")
	proto.RegisterType((*GetCooperationOptListReq)(nil), "guild_cooperation.GetCooperationOptListReq")
	proto.RegisterType((*GetCooperationOptListResp)(nil), "guild_cooperation.GetCooperationOptListResp")
	proto.RegisterType((*GetCooperationGuildIdsReq)(nil), "guild_cooperation.GetCooperationGuildIdsReq")
	proto.RegisterType((*GetCooperationGuildIdsResp)(nil), "guild_cooperation.GetCooperationGuildIdsResp")
	proto.RegisterType((*GetGuildCooperationInfosReq)(nil), "guild_cooperation.GetGuildCooperationInfosReq")
	proto.RegisterType((*GetGuildCooperationInfosResp)(nil), "guild_cooperation.GetGuildCooperationInfosResp")
	proto.RegisterType((*CooperationHistory)(nil), "guild_cooperation.CooperationHistory")
	proto.RegisterType((*GetGuildCooperationHistoryReq)(nil), "guild_cooperation.GetGuildCooperationHistoryReq")
	proto.RegisterType((*GetGuildCooperationHistoryResp)(nil), "guild_cooperation.GetGuildCooperationHistoryResp")
	proto.RegisterType((*GetGuildCooperationWithTimeReq)(nil), "guild_cooperation.GetGuildCooperationWithTimeReq")
	proto.RegisterType((*GetGuildCooperationWithTimeResp)(nil), "guild_cooperation.GetGuildCooperationWithTimeResp")
	proto.RegisterType((*CoopHistoryPeriod)(nil), "guild_cooperation.CoopHistoryPeriod")
	proto.RegisterType((*GetGuildCooperationHistoryPeriodReq)(nil), "guild_cooperation.GetGuildCooperationHistoryPeriodReq")
	proto.RegisterType((*GetGuildCooperationHistoryPeriodResp)(nil), "guild_cooperation.GetGuildCooperationHistoryPeriodResp")
	proto.RegisterType((*GuildNoBonus)(nil), "guild_cooperation.GuildNoBonus")
	proto.RegisterType((*GetAllNoBonusGuildReq)(nil), "guild_cooperation.GetAllNoBonusGuildReq")
	proto.RegisterType((*GetAllNoBonusGuildResp)(nil), "guild_cooperation.GetAllNoBonusGuildResp")
	proto.RegisterType((*SetNoBonusGuildReq)(nil), "guild_cooperation.SetNoBonusGuildReq")
	proto.RegisterType((*SetNoBonusGuildResp)(nil), "guild_cooperation.SetNoBonusGuildResp")
	proto.RegisterType((*CooperationChangeEvent)(nil), "guild_cooperation.CooperationChangeEvent")
	proto.RegisterEnum("guild_cooperation.CooperationType", CooperationType_name, CooperationType_value)
	proto.RegisterEnum("guild_cooperation.ApplicationStep", ApplicationStep_name, ApplicationStep_value)
	proto.RegisterEnum("guild_cooperation.ApplicationOperation", ApplicationOperation_name, ApplicationOperation_value)
	proto.RegisterEnum("guild_cooperation.ReqApplyStatus", ReqApplyStatus_name, ReqApplyStatus_value)
	proto.RegisterEnum("guild_cooperation.CoopOptType", CoopOptType_name, CoopOptType_value)
	proto.RegisterEnum("guild_cooperation.SourcePlatform", SourcePlatform_name, SourcePlatform_value)
	proto.RegisterEnum("guild_cooperation.MsgType", MsgType_name, MsgType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GuildCooperationClient is the client API for GuildCooperation service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GuildCooperationClient interface {
	// 获取合作信息
	GetCooperationInfo(ctx context.Context, in *GetCooperationInfoReq, opts ...grpc.CallOption) (*GetCooperationInfoResp, error)
	// 通过手机号获取合作信息
	GetCooperationInfoByPhone(ctx context.Context, in *GetCooperationInfoByPhoneReq, opts ...grpc.CallOption) (*GetCooperationInfoByPhoneResp, error)
	// 提交申请资料
	SubmitApplication(ctx context.Context, in *SubmitApplicationReq, opts ...grpc.CallOption) (*SubmitApplicationResp, error)
	// 创建公会
	CreateGuild(ctx context.Context, in *CreateGuildReq, opts ...grpc.CallOption) (*CreateGuildResp, error)
	// 申请合作
	ApplyCooperation(ctx context.Context, in *ApplyCooperationReq, opts ...grpc.CallOption) (*ApplyCooperationResp, error)
	// 获取入驻申请列表
	GetApplications(ctx context.Context, in *GetApplicationsReq, opts ...grpc.CallOption) (*GetApplicationsResp, error)
	// 查看申请资料
	GetApplicationInfo(ctx context.Context, in *GetApplicationInfoReq, opts ...grpc.CallOption) (*GetApplicationInfoResp, error)
	// 通过申请
	ApproveApplication(ctx context.Context, in *ApproveApplicationReq, opts ...grpc.CallOption) (*ApproveApplicationResp, error)
	// 拒绝申请
	RejectApplication(ctx context.Context, in *RejectApplicationReq, opts ...grpc.CallOption) (*RejectApplicationResp, error)
	// 获取公会信息
	GetGuildInfo(ctx context.Context, in *GetGuildInfoReq, opts ...grpc.CallOption) (*GetGuildInfoResp, error)
	// 获取操作历史
	GetOperationHistory(ctx context.Context, in *GetOperationHistoryReq, opts ...grpc.CallOption) (*GetOperationHistoryResp, error)
	// 获取会长的合作库申请资料
	GetApplicationInfoByUid(ctx context.Context, in *GetApplicationInfoByUidReq, opts ...grpc.CallOption) (*GetApplicationInfoByUidResp, error)
	// 创建/编辑文案
	EditNotification(ctx context.Context, in *EditNotificationReq, opts ...grpc.CallOption) (*EditNotificationResp, error)
	// 获取通知文案列表
	GetNotificationList(ctx context.Context, in *GetNotificationListReq, opts ...grpc.CallOption) (*GetNotificationListResp, error)
	// 删除文案
	DeleteNotification(ctx context.Context, in *DeleteNotificationReq, opts ...grpc.CallOption) (*DeleteNotificationResp, error)
	// 创建申请限制
	CreateApplyLimit(ctx context.Context, in *CreateApplyLimitReq, opts ...grpc.CallOption) (*CreateApplyLimitResp, error)
	// 获取申请限制列表
	GetApplyLimitList(ctx context.Context, in *GetApplyLimitListReq, opts ...grpc.CallOption) (*GetApplyLimitListResp, error)
	// 解除申请限制
	DeleteApplyLimit(ctx context.Context, in *DeleteApplyLimitReq, opts ...grpc.CallOption) (*DeleteApplyLimitResp, error)
	// 获取合作库公会列表
	GetCooperationGuildList(ctx context.Context, in *GetCooperationGuildListReq, opts ...grpc.CallOption) (*GetCooperationGuildListResp, error)
	// 手动添加合作库公会（管理后台接口）
	AddCooperationGuild(ctx context.Context, in *AddCooperationGuildReq, opts ...grpc.CallOption) (*AddCooperationGuildResp, error)
	// 批量手动添加合作库公会（管理后台接口）
	BatchAddCooperationGuild(ctx context.Context, in *BatchAddCooperationGuildReq, opts ...grpc.CallOption) (*BatchAddCooperationGuildResp, error)
	// 移除合作库公会
	DelCooperationGuild(ctx context.Context, in *DelCooperationGuildReq, opts ...grpc.CallOption) (*DelCooperationGuildResp, error)
	// 获取合作库操作记录
	GetCooperationOptList(ctx context.Context, in *GetCooperationOptListReq, opts ...grpc.CallOption) (*GetCooperationOptListResp, error)
	// --------------------------------- 以下接口提供服务端使用 ---------------------------------------
	// 批量获取所有合作库公会ID
	GetCooperationGuildIds(ctx context.Context, in *GetCooperationGuildIdsReq, opts ...grpc.CallOption) (*GetCooperationGuildIdsResp, error)
	// 获取公会合作信息
	GetGuildCooperationInfos(ctx context.Context, in *GetGuildCooperationInfosReq, opts ...grpc.CallOption) (*GetGuildCooperationInfosResp, error)
	// 获取公会合作历史信息
	GetGuildCooperationHistory(ctx context.Context, in *GetGuildCooperationHistoryReq, opts ...grpc.CallOption) (*GetGuildCooperationHistoryResp, error)
	// 获取公会指定时间的合作状态
	GetGuildCooperationWithTime(ctx context.Context, in *GetGuildCooperationWithTimeReq, opts ...grpc.CallOption) (*GetGuildCooperationWithTimeResp, error)
	// 获取公会历史的合作时间段
	GetGuildCooperationHistoryPeriod(ctx context.Context, in *GetGuildCooperationHistoryPeriodReq, opts ...grpc.CallOption) (*GetGuildCooperationHistoryPeriodResp, error)
	// 获取所有无佣金公会信息
	GetAllNoBonusGuild(ctx context.Context, in *GetAllNoBonusGuildReq, opts ...grpc.CallOption) (*GetAllNoBonusGuildResp, error)
	// 设置无佣金公会
	SetNoBonusGuild(ctx context.Context, in *SetNoBonusGuildReq, opts ...grpc.CallOption) (*SetNoBonusGuildResp, error)
}

type guildCooperationClient struct {
	cc *grpc.ClientConn
}

func NewGuildCooperationClient(cc *grpc.ClientConn) GuildCooperationClient {
	return &guildCooperationClient{cc}
}

func (c *guildCooperationClient) GetCooperationInfo(ctx context.Context, in *GetCooperationInfoReq, opts ...grpc.CallOption) (*GetCooperationInfoResp, error) {
	out := new(GetCooperationInfoResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/GetCooperationInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) GetCooperationInfoByPhone(ctx context.Context, in *GetCooperationInfoByPhoneReq, opts ...grpc.CallOption) (*GetCooperationInfoByPhoneResp, error) {
	out := new(GetCooperationInfoByPhoneResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/GetCooperationInfoByPhone", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) SubmitApplication(ctx context.Context, in *SubmitApplicationReq, opts ...grpc.CallOption) (*SubmitApplicationResp, error) {
	out := new(SubmitApplicationResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/SubmitApplication", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) CreateGuild(ctx context.Context, in *CreateGuildReq, opts ...grpc.CallOption) (*CreateGuildResp, error) {
	out := new(CreateGuildResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/CreateGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) ApplyCooperation(ctx context.Context, in *ApplyCooperationReq, opts ...grpc.CallOption) (*ApplyCooperationResp, error) {
	out := new(ApplyCooperationResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/ApplyCooperation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) GetApplications(ctx context.Context, in *GetApplicationsReq, opts ...grpc.CallOption) (*GetApplicationsResp, error) {
	out := new(GetApplicationsResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/GetApplications", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) GetApplicationInfo(ctx context.Context, in *GetApplicationInfoReq, opts ...grpc.CallOption) (*GetApplicationInfoResp, error) {
	out := new(GetApplicationInfoResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/GetApplicationInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) ApproveApplication(ctx context.Context, in *ApproveApplicationReq, opts ...grpc.CallOption) (*ApproveApplicationResp, error) {
	out := new(ApproveApplicationResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/ApproveApplication", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) RejectApplication(ctx context.Context, in *RejectApplicationReq, opts ...grpc.CallOption) (*RejectApplicationResp, error) {
	out := new(RejectApplicationResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/RejectApplication", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) GetGuildInfo(ctx context.Context, in *GetGuildInfoReq, opts ...grpc.CallOption) (*GetGuildInfoResp, error) {
	out := new(GetGuildInfoResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/GetGuildInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) GetOperationHistory(ctx context.Context, in *GetOperationHistoryReq, opts ...grpc.CallOption) (*GetOperationHistoryResp, error) {
	out := new(GetOperationHistoryResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/GetOperationHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) GetApplicationInfoByUid(ctx context.Context, in *GetApplicationInfoByUidReq, opts ...grpc.CallOption) (*GetApplicationInfoByUidResp, error) {
	out := new(GetApplicationInfoByUidResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/GetApplicationInfoByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) EditNotification(ctx context.Context, in *EditNotificationReq, opts ...grpc.CallOption) (*EditNotificationResp, error) {
	out := new(EditNotificationResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/EditNotification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) GetNotificationList(ctx context.Context, in *GetNotificationListReq, opts ...grpc.CallOption) (*GetNotificationListResp, error) {
	out := new(GetNotificationListResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/GetNotificationList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) DeleteNotification(ctx context.Context, in *DeleteNotificationReq, opts ...grpc.CallOption) (*DeleteNotificationResp, error) {
	out := new(DeleteNotificationResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/DeleteNotification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) CreateApplyLimit(ctx context.Context, in *CreateApplyLimitReq, opts ...grpc.CallOption) (*CreateApplyLimitResp, error) {
	out := new(CreateApplyLimitResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/CreateApplyLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) GetApplyLimitList(ctx context.Context, in *GetApplyLimitListReq, opts ...grpc.CallOption) (*GetApplyLimitListResp, error) {
	out := new(GetApplyLimitListResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/GetApplyLimitList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) DeleteApplyLimit(ctx context.Context, in *DeleteApplyLimitReq, opts ...grpc.CallOption) (*DeleteApplyLimitResp, error) {
	out := new(DeleteApplyLimitResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/DeleteApplyLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) GetCooperationGuildList(ctx context.Context, in *GetCooperationGuildListReq, opts ...grpc.CallOption) (*GetCooperationGuildListResp, error) {
	out := new(GetCooperationGuildListResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/GetCooperationGuildList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) AddCooperationGuild(ctx context.Context, in *AddCooperationGuildReq, opts ...grpc.CallOption) (*AddCooperationGuildResp, error) {
	out := new(AddCooperationGuildResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/AddCooperationGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) BatchAddCooperationGuild(ctx context.Context, in *BatchAddCooperationGuildReq, opts ...grpc.CallOption) (*BatchAddCooperationGuildResp, error) {
	out := new(BatchAddCooperationGuildResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/BatchAddCooperationGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) DelCooperationGuild(ctx context.Context, in *DelCooperationGuildReq, opts ...grpc.CallOption) (*DelCooperationGuildResp, error) {
	out := new(DelCooperationGuildResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/DelCooperationGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) GetCooperationOptList(ctx context.Context, in *GetCooperationOptListReq, opts ...grpc.CallOption) (*GetCooperationOptListResp, error) {
	out := new(GetCooperationOptListResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/GetCooperationOptList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) GetCooperationGuildIds(ctx context.Context, in *GetCooperationGuildIdsReq, opts ...grpc.CallOption) (*GetCooperationGuildIdsResp, error) {
	out := new(GetCooperationGuildIdsResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/GetCooperationGuildIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) GetGuildCooperationInfos(ctx context.Context, in *GetGuildCooperationInfosReq, opts ...grpc.CallOption) (*GetGuildCooperationInfosResp, error) {
	out := new(GetGuildCooperationInfosResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/GetGuildCooperationInfos", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) GetGuildCooperationHistory(ctx context.Context, in *GetGuildCooperationHistoryReq, opts ...grpc.CallOption) (*GetGuildCooperationHistoryResp, error) {
	out := new(GetGuildCooperationHistoryResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/GetGuildCooperationHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) GetGuildCooperationWithTime(ctx context.Context, in *GetGuildCooperationWithTimeReq, opts ...grpc.CallOption) (*GetGuildCooperationWithTimeResp, error) {
	out := new(GetGuildCooperationWithTimeResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/GetGuildCooperationWithTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) GetGuildCooperationHistoryPeriod(ctx context.Context, in *GetGuildCooperationHistoryPeriodReq, opts ...grpc.CallOption) (*GetGuildCooperationHistoryPeriodResp, error) {
	out := new(GetGuildCooperationHistoryPeriodResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/GetGuildCooperationHistoryPeriod", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) GetAllNoBonusGuild(ctx context.Context, in *GetAllNoBonusGuildReq, opts ...grpc.CallOption) (*GetAllNoBonusGuildResp, error) {
	out := new(GetAllNoBonusGuildResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/GetAllNoBonusGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCooperationClient) SetNoBonusGuild(ctx context.Context, in *SetNoBonusGuildReq, opts ...grpc.CallOption) (*SetNoBonusGuildResp, error) {
	out := new(SetNoBonusGuildResp)
	err := c.cc.Invoke(ctx, "/guild_cooperation.GuildCooperation/SetNoBonusGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GuildCooperationServer is the server API for GuildCooperation service.
type GuildCooperationServer interface {
	// 获取合作信息
	GetCooperationInfo(context.Context, *GetCooperationInfoReq) (*GetCooperationInfoResp, error)
	// 通过手机号获取合作信息
	GetCooperationInfoByPhone(context.Context, *GetCooperationInfoByPhoneReq) (*GetCooperationInfoByPhoneResp, error)
	// 提交申请资料
	SubmitApplication(context.Context, *SubmitApplicationReq) (*SubmitApplicationResp, error)
	// 创建公会
	CreateGuild(context.Context, *CreateGuildReq) (*CreateGuildResp, error)
	// 申请合作
	ApplyCooperation(context.Context, *ApplyCooperationReq) (*ApplyCooperationResp, error)
	// 获取入驻申请列表
	GetApplications(context.Context, *GetApplicationsReq) (*GetApplicationsResp, error)
	// 查看申请资料
	GetApplicationInfo(context.Context, *GetApplicationInfoReq) (*GetApplicationInfoResp, error)
	// 通过申请
	ApproveApplication(context.Context, *ApproveApplicationReq) (*ApproveApplicationResp, error)
	// 拒绝申请
	RejectApplication(context.Context, *RejectApplicationReq) (*RejectApplicationResp, error)
	// 获取公会信息
	GetGuildInfo(context.Context, *GetGuildInfoReq) (*GetGuildInfoResp, error)
	// 获取操作历史
	GetOperationHistory(context.Context, *GetOperationHistoryReq) (*GetOperationHistoryResp, error)
	// 获取会长的合作库申请资料
	GetApplicationInfoByUid(context.Context, *GetApplicationInfoByUidReq) (*GetApplicationInfoByUidResp, error)
	// 创建/编辑文案
	EditNotification(context.Context, *EditNotificationReq) (*EditNotificationResp, error)
	// 获取通知文案列表
	GetNotificationList(context.Context, *GetNotificationListReq) (*GetNotificationListResp, error)
	// 删除文案
	DeleteNotification(context.Context, *DeleteNotificationReq) (*DeleteNotificationResp, error)
	// 创建申请限制
	CreateApplyLimit(context.Context, *CreateApplyLimitReq) (*CreateApplyLimitResp, error)
	// 获取申请限制列表
	GetApplyLimitList(context.Context, *GetApplyLimitListReq) (*GetApplyLimitListResp, error)
	// 解除申请限制
	DeleteApplyLimit(context.Context, *DeleteApplyLimitReq) (*DeleteApplyLimitResp, error)
	// 获取合作库公会列表
	GetCooperationGuildList(context.Context, *GetCooperationGuildListReq) (*GetCooperationGuildListResp, error)
	// 手动添加合作库公会（管理后台接口）
	AddCooperationGuild(context.Context, *AddCooperationGuildReq) (*AddCooperationGuildResp, error)
	// 批量手动添加合作库公会（管理后台接口）
	BatchAddCooperationGuild(context.Context, *BatchAddCooperationGuildReq) (*BatchAddCooperationGuildResp, error)
	// 移除合作库公会
	DelCooperationGuild(context.Context, *DelCooperationGuildReq) (*DelCooperationGuildResp, error)
	// 获取合作库操作记录
	GetCooperationOptList(context.Context, *GetCooperationOptListReq) (*GetCooperationOptListResp, error)
	// --------------------------------- 以下接口提供服务端使用 ---------------------------------------
	// 批量获取所有合作库公会ID
	GetCooperationGuildIds(context.Context, *GetCooperationGuildIdsReq) (*GetCooperationGuildIdsResp, error)
	// 获取公会合作信息
	GetGuildCooperationInfos(context.Context, *GetGuildCooperationInfosReq) (*GetGuildCooperationInfosResp, error)
	// 获取公会合作历史信息
	GetGuildCooperationHistory(context.Context, *GetGuildCooperationHistoryReq) (*GetGuildCooperationHistoryResp, error)
	// 获取公会指定时间的合作状态
	GetGuildCooperationWithTime(context.Context, *GetGuildCooperationWithTimeReq) (*GetGuildCooperationWithTimeResp, error)
	// 获取公会历史的合作时间段
	GetGuildCooperationHistoryPeriod(context.Context, *GetGuildCooperationHistoryPeriodReq) (*GetGuildCooperationHistoryPeriodResp, error)
	// 获取所有无佣金公会信息
	GetAllNoBonusGuild(context.Context, *GetAllNoBonusGuildReq) (*GetAllNoBonusGuildResp, error)
	// 设置无佣金公会
	SetNoBonusGuild(context.Context, *SetNoBonusGuildReq) (*SetNoBonusGuildResp, error)
}

func RegisterGuildCooperationServer(s *grpc.Server, srv GuildCooperationServer) {
	s.RegisterService(&_GuildCooperation_serviceDesc, srv)
}

func _GuildCooperation_GetCooperationInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCooperationInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).GetCooperationInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/GetCooperationInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).GetCooperationInfo(ctx, req.(*GetCooperationInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_GetCooperationInfoByPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCooperationInfoByPhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).GetCooperationInfoByPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/GetCooperationInfoByPhone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).GetCooperationInfoByPhone(ctx, req.(*GetCooperationInfoByPhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_SubmitApplication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitApplicationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).SubmitApplication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/SubmitApplication",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).SubmitApplication(ctx, req.(*SubmitApplicationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_CreateGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).CreateGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/CreateGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).CreateGuild(ctx, req.(*CreateGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_ApplyCooperation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyCooperationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).ApplyCooperation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/ApplyCooperation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).ApplyCooperation(ctx, req.(*ApplyCooperationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_GetApplications_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplicationsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).GetApplications(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/GetApplications",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).GetApplications(ctx, req.(*GetApplicationsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_GetApplicationInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplicationInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).GetApplicationInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/GetApplicationInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).GetApplicationInfo(ctx, req.(*GetApplicationInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_ApproveApplication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApproveApplicationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).ApproveApplication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/ApproveApplication",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).ApproveApplication(ctx, req.(*ApproveApplicationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_RejectApplication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RejectApplicationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).RejectApplication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/RejectApplication",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).RejectApplication(ctx, req.(*RejectApplicationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_GetGuildInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).GetGuildInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/GetGuildInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).GetGuildInfo(ctx, req.(*GetGuildInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_GetOperationHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOperationHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).GetOperationHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/GetOperationHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).GetOperationHistory(ctx, req.(*GetOperationHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_GetApplicationInfoByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplicationInfoByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).GetApplicationInfoByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/GetApplicationInfoByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).GetApplicationInfoByUid(ctx, req.(*GetApplicationInfoByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_EditNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditNotificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).EditNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/EditNotification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).EditNotification(ctx, req.(*EditNotificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_GetNotificationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNotificationListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).GetNotificationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/GetNotificationList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).GetNotificationList(ctx, req.(*GetNotificationListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_DeleteNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNotificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).DeleteNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/DeleteNotification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).DeleteNotification(ctx, req.(*DeleteNotificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_CreateApplyLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateApplyLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).CreateApplyLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/CreateApplyLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).CreateApplyLimit(ctx, req.(*CreateApplyLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_GetApplyLimitList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplyLimitListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).GetApplyLimitList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/GetApplyLimitList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).GetApplyLimitList(ctx, req.(*GetApplyLimitListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_DeleteApplyLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteApplyLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).DeleteApplyLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/DeleteApplyLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).DeleteApplyLimit(ctx, req.(*DeleteApplyLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_GetCooperationGuildList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCooperationGuildListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).GetCooperationGuildList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/GetCooperationGuildList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).GetCooperationGuildList(ctx, req.(*GetCooperationGuildListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_AddCooperationGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCooperationGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).AddCooperationGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/AddCooperationGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).AddCooperationGuild(ctx, req.(*AddCooperationGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_BatchAddCooperationGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddCooperationGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).BatchAddCooperationGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/BatchAddCooperationGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).BatchAddCooperationGuild(ctx, req.(*BatchAddCooperationGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_DelCooperationGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelCooperationGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).DelCooperationGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/DelCooperationGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).DelCooperationGuild(ctx, req.(*DelCooperationGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_GetCooperationOptList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCooperationOptListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).GetCooperationOptList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/GetCooperationOptList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).GetCooperationOptList(ctx, req.(*GetCooperationOptListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_GetCooperationGuildIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCooperationGuildIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).GetCooperationGuildIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/GetCooperationGuildIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).GetCooperationGuildIds(ctx, req.(*GetCooperationGuildIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_GetGuildCooperationInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildCooperationInfosReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).GetGuildCooperationInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/GetGuildCooperationInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).GetGuildCooperationInfos(ctx, req.(*GetGuildCooperationInfosReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_GetGuildCooperationHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildCooperationHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).GetGuildCooperationHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/GetGuildCooperationHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).GetGuildCooperationHistory(ctx, req.(*GetGuildCooperationHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_GetGuildCooperationWithTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildCooperationWithTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).GetGuildCooperationWithTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/GetGuildCooperationWithTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).GetGuildCooperationWithTime(ctx, req.(*GetGuildCooperationWithTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_GetGuildCooperationHistoryPeriod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildCooperationHistoryPeriodReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).GetGuildCooperationHistoryPeriod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/GetGuildCooperationHistoryPeriod",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).GetGuildCooperationHistoryPeriod(ctx, req.(*GetGuildCooperationHistoryPeriodReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_GetAllNoBonusGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllNoBonusGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).GetAllNoBonusGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/GetAllNoBonusGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).GetAllNoBonusGuild(ctx, req.(*GetAllNoBonusGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCooperation_SetNoBonusGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetNoBonusGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCooperationServer).SetNoBonusGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_cooperation.GuildCooperation/SetNoBonusGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCooperationServer).SetNoBonusGuild(ctx, req.(*SetNoBonusGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GuildCooperation_serviceDesc = grpc.ServiceDesc{
	ServiceName: "guild_cooperation.GuildCooperation",
	HandlerType: (*GuildCooperationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCooperationInfo",
			Handler:    _GuildCooperation_GetCooperationInfo_Handler,
		},
		{
			MethodName: "GetCooperationInfoByPhone",
			Handler:    _GuildCooperation_GetCooperationInfoByPhone_Handler,
		},
		{
			MethodName: "SubmitApplication",
			Handler:    _GuildCooperation_SubmitApplication_Handler,
		},
		{
			MethodName: "CreateGuild",
			Handler:    _GuildCooperation_CreateGuild_Handler,
		},
		{
			MethodName: "ApplyCooperation",
			Handler:    _GuildCooperation_ApplyCooperation_Handler,
		},
		{
			MethodName: "GetApplications",
			Handler:    _GuildCooperation_GetApplications_Handler,
		},
		{
			MethodName: "GetApplicationInfo",
			Handler:    _GuildCooperation_GetApplicationInfo_Handler,
		},
		{
			MethodName: "ApproveApplication",
			Handler:    _GuildCooperation_ApproveApplication_Handler,
		},
		{
			MethodName: "RejectApplication",
			Handler:    _GuildCooperation_RejectApplication_Handler,
		},
		{
			MethodName: "GetGuildInfo",
			Handler:    _GuildCooperation_GetGuildInfo_Handler,
		},
		{
			MethodName: "GetOperationHistory",
			Handler:    _GuildCooperation_GetOperationHistory_Handler,
		},
		{
			MethodName: "GetApplicationInfoByUid",
			Handler:    _GuildCooperation_GetApplicationInfoByUid_Handler,
		},
		{
			MethodName: "EditNotification",
			Handler:    _GuildCooperation_EditNotification_Handler,
		},
		{
			MethodName: "GetNotificationList",
			Handler:    _GuildCooperation_GetNotificationList_Handler,
		},
		{
			MethodName: "DeleteNotification",
			Handler:    _GuildCooperation_DeleteNotification_Handler,
		},
		{
			MethodName: "CreateApplyLimit",
			Handler:    _GuildCooperation_CreateApplyLimit_Handler,
		},
		{
			MethodName: "GetApplyLimitList",
			Handler:    _GuildCooperation_GetApplyLimitList_Handler,
		},
		{
			MethodName: "DeleteApplyLimit",
			Handler:    _GuildCooperation_DeleteApplyLimit_Handler,
		},
		{
			MethodName: "GetCooperationGuildList",
			Handler:    _GuildCooperation_GetCooperationGuildList_Handler,
		},
		{
			MethodName: "AddCooperationGuild",
			Handler:    _GuildCooperation_AddCooperationGuild_Handler,
		},
		{
			MethodName: "BatchAddCooperationGuild",
			Handler:    _GuildCooperation_BatchAddCooperationGuild_Handler,
		},
		{
			MethodName: "DelCooperationGuild",
			Handler:    _GuildCooperation_DelCooperationGuild_Handler,
		},
		{
			MethodName: "GetCooperationOptList",
			Handler:    _GuildCooperation_GetCooperationOptList_Handler,
		},
		{
			MethodName: "GetCooperationGuildIds",
			Handler:    _GuildCooperation_GetCooperationGuildIds_Handler,
		},
		{
			MethodName: "GetGuildCooperationInfos",
			Handler:    _GuildCooperation_GetGuildCooperationInfos_Handler,
		},
		{
			MethodName: "GetGuildCooperationHistory",
			Handler:    _GuildCooperation_GetGuildCooperationHistory_Handler,
		},
		{
			MethodName: "GetGuildCooperationWithTime",
			Handler:    _GuildCooperation_GetGuildCooperationWithTime_Handler,
		},
		{
			MethodName: "GetGuildCooperationHistoryPeriod",
			Handler:    _GuildCooperation_GetGuildCooperationHistoryPeriod_Handler,
		},
		{
			MethodName: "GetAllNoBonusGuild",
			Handler:    _GuildCooperation_GetAllNoBonusGuild_Handler,
		},
		{
			MethodName: "SetNoBonusGuild",
			Handler:    _GuildCooperation_SetNoBonusGuild_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/guild-cooperation/guild-cooperation.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/guild-cooperation/guild-cooperation.proto", fileDescriptor_guild_cooperation_673a2604e98b98c6)
}

var fileDescriptor_guild_cooperation_673a2604e98b98c6 = []byte{
	// 3355 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x3b, 0xcf, 0x6f, 0x1b, 0xc7,
	0xd5, 0x59, 0x52, 0x14, 0xc9, 0x27, 0x91, 0x5a, 0x8e, 0x7e, 0x98, 0xa6, 0xe3, 0x58, 0x5e, 0x27,
	0xb1, 0x2c, 0xc7, 0x52, 0x3e, 0xe7, 0xc7, 0x97, 0xe0, 0xfb, 0xd0, 0x46, 0xb6, 0x55, 0x47, 0x89,
	0x6d, 0xa9, 0x2b, 0xa7, 0x4e, 0x8a, 0x16, 0xf4, 0x7a, 0x39, 0x92, 0x36, 0x5e, 0xee, 0x8e, 0x77,
	0x96, 0x4a, 0x94, 0x00, 0x2d, 0x0a, 0x14, 0x28, 0x5a, 0xa0, 0x4d, 0xd0, 0x9e, 0x5a, 0xa0, 0xb7,
	0x5e, 0x7a, 0xed, 0xa1, 0x87, 0xf6, 0xdc, 0x7b, 0x0f, 0x05, 0x0a, 0xf4, 0xd2, 0x6b, 0x8f, 0xfd,
	0x0b, 0x8a, 0x62, 0x66, 0x76, 0xb9, 0xb3, 0xbb, 0xb3, 0x14, 0xc9, 0x38, 0x45, 0x7a, 0x12, 0xe7,
	0xcd, 0x7b, 0x6f, 0xdf, 0x7b, 0xf3, 0xe6, 0xcd, 0x9b, 0xf7, 0x46, 0xf0, 0x46, 0x18, 0x6e, 0x3e,
	0x19, 0x38, 0xf6, 0x63, 0xea, 0xb8, 0xc7, 0x38, 0xd8, 0x3c, 0x1c, 0x38, 0x6e, 0xef, 0x9a, 0xed,
	0xfb, 0x04, 0x07, 0x56, 0xe8, 0xf8, 0x5e, 0x1e, 0xb2, 0x41, 0x02, 0x3f, 0xf4, 0x51, 0x8b, 0x4f,
	0x74, 0xa5, 0x09, 0xe3, 0x5f, 0x25, 0x58, 0xb8, 0x99, 0x8c, 0x77, 0xbc, 0x03, 0x1f, 0xdd, 0x05,
	0x5d, 0x42, 0xe9, 0x86, 0x27, 0x04, 0xb7, 0xb5, 0x55, 0x6d, 0xad, 0x79, 0xdd, 0xd8, 0xc8, 0x71,
	0xd8, 0x90, 0xa8, 0xef, 0x9f, 0x10, 0x6c, 0x2e, 0xd8, 0x69, 0x00, 0x7a, 0x01, 0x9a, 0x0e, 0x95,
	0x49, 0xda, 0xa5, 0x55, 0x6d, 0xad, 0x66, 0x36, 0x1c, 0x2a, 0xd1, 0xb2, 0xaf, 0x5a, 0x84, 0xb8,
	0x8e, 0x2d, 0xbe, 0x4a, 0x43, 0x4c, 0xda, 0xe5, 0xc2, 0xaf, 0x6e, 0x25, 0xa8, 0xfb, 0x21, 0x26,
	0xe6, 0x82, 0x95, 0x06, 0xa0, 0xef, 0xc0, 0xb2, 0xcc, 0x2e, 0xf9, 0xf8, 0x0c, 0xe7, 0x79, 0x79,
	0x34, 0xcf, 0xdd, 0x18, 0x68, 0x2e, 0x59, 0x0a, 0x28, 0xd3, 0x49, 0xe6, 0xee, 0xf4, 0xda, 0x95,
	0x55, 0x6d, 0xad, 0x6e, 0x36, 0x24, 0xe8, 0x4e, 0x0f, 0x5d, 0x84, 0xf9, 0x00, 0x33, 0xd0, 0x49,
	0x37, 0x74, 0xfa, 0xb8, 0x3d, 0xbb, 0xaa, 0xad, 0x35, 0xcc, 0xb9, 0x08, 0x76, 0xdf, 0xe9, 0x63,
	0xe3, 0x0a, 0x2c, 0xdf, 0xc6, 0x61, 0x66, 0x09, 0x4c, 0xfc, 0x04, 0xe9, 0x50, 0x1e, 0x38, 0x3d,
	0x6e, 0xf8, 0x86, 0xc9, 0x7e, 0x1a, 0xbf, 0xd6, 0x60, 0x45, 0x85, 0x4b, 0x49, 0x76, 0xc9, 0x1c,
	0xef, 0xc0, 0x6f, 0x6b, 0xab, 0xe5, 0xb5, 0xb9, 0xd3, 0x96, 0x8c, 0x73, 0x90, 0x97, 0x8c, 0x7b,
	0xc0, 0xab, 0xb0, 0x82, 0x29, 0xf1, 0x83, 0xb0, 0x8b, 0xbd, 0x30, 0xb0, 0x3c, 0x1b, 0x77, 0xe9,
	0x47, 0x4e, 0x68, 0x1f, 0x45, 0x4b, 0xb7, 0x24, 0x66, 0xb7, 0xa3, 0xc9, 0x7d, 0x3e, 0x67, 0xbc,
	0x0a, 0xcf, 0xe6, 0xc5, 0xbb, 0x71, 0xb2, 0x77, 0xe4, 0x7b, 0x98, 0x69, 0xb4, 0x04, 0x15, 0xc2,
	0x7e, 0x73, 0x9d, 0xea, 0xa6, 0x18, 0x18, 0xbf, 0xd1, 0xe0, 0xfc, 0x08, 0xb2, 0xaf, 0x8a, 0x72,
	0xbf, 0xab, 0xc0, 0xd2, 0xfe, 0xe0, 0x51, 0xdf, 0x09, 0x25, 0x37, 0x51, 0xae, 0x13, 0xba, 0x0a,
	0x2d, 0xfc, 0x71, 0x88, 0x03, 0xcf, 0x72, 0xbb, 0xc4, 0xb5, 0xc2, 0x03, 0x3f, 0xe8, 0x73, 0xde,
	0x75, 0x53, 0x8f, 0x27, 0xf6, 0x22, 0x38, 0x5a, 0x81, 0xd9, 0x8f, 0xb0, 0x7d, 0x64, 0x85, 0xdc,
	0xd9, 0xeb, 0x66, 0x34, 0x42, 0xab, 0x30, 0x67, 0xfb, 0x01, 0xf1, 0x25, 0xaf, 0xad, 0x9b, 0x32,
	0x88, 0x39, 0x97, 0x8b, 0x0f, 0xd9, 0x37, 0x70, 0x40, 0x7d, 0x2f, 0xf2, 0xc0, 0x39, 0x0e, 0xdb,
	0xe3, 0x20, 0x74, 0x85, 0x59, 0x6e, 0x48, 0xd1, 0xb5, 0xfd, 0x9e, 0xf0, 0xc1, 0x3a, 0xb3, 0xca,
	0x10, 0x7e, 0xd3, 0xef, 0x61, 0xc6, 0xed, 0x91, 0xe5, 0x3d, 0xee, 0x5a, 0xb6, 0xed, 0x0f, 0xbc,
	0xb0, 0x5d, 0x15, 0xdc, 0x18, 0x6c, 0x4b, 0x80, 0xd0, 0xeb, 0x70, 0xe6, 0xd1, 0x80, 0x3a, 0x1e,
	0xa6, 0xb4, 0xeb, 0x3a, 0x36, 0xf6, 0x28, 0xee, 0x92, 0x23, 0x3f, 0xf4, 0x69, 0xbb, 0xb6, 0x5a,
	0x5e, 0xab, 0x9b, 0xcb, 0xf1, 0xf4, 0x1d, 0x31, 0xbb, 0xc7, 0x27, 0xd1, 0x06, 0x2c, 0x3e, 0x72,
	0x5c, 0xb7, 0x7b, 0xe4, 0xd0, 0xd0, 0x0f, 0x4e, 0x62, 0x9a, 0x3a, 0xa7, 0x69, 0xb1, 0xa9, 0xb7,
	0xc5, 0x4c, 0x84, 0xaf, 0x8a, 0x3f, 0x30, 0x7d, 0xfc, 0xd9, 0x85, 0x56, 0x96, 0x1d, 0x6d, 0xcf,
	0xad, 0x96, 0xc7, 0xe4, 0xa7, 0x67, 0xf8, 0xd1, 0xc4, 0x8f, 0xe7, 0x25, 0x3f, 0x46, 0xef, 0xc0,
	0x02, 0xf5, 0x07, 0x81, 0x8d, 0x93, 0x35, 0x6f, 0x70, 0xa1, 0x2f, 0x2a, 0x3e, 0xb2, 0xcf, 0x31,
	0x63, 0x27, 0x30, 0x9b, 0x34, 0x35, 0x46, 0xeb, 0xd0, 0xb2, 0x03, 0x6c, 0x85, 0xb8, 0x2b, 0x48,
	0x3d, 0xab, 0x8f, 0xdb, 0xcd, 0x68, 0xe1, 0xf8, 0xc4, 0x6d, 0x06, 0xbf, 0x67, 0xf5, 0x31, 0xea,
	0x40, 0x4d, 0xf0, 0xf5, 0x83, 0xf6, 0x02, 0x47, 0x19, 0x8e, 0x8d, 0x43, 0x58, 0x56, 0xf8, 0x2c,
	0x25, 0x8a, 0xf8, 0xa5, 0xa9, 0xe2, 0xd7, 0x65, 0x58, 0x48, 0xa3, 0xd1, 0x76, 0x89, 0xaf, 0x5a,
	0x33, 0x85, 0x47, 0x8d, 0x2d, 0x68, 0xde, 0x4c, 0xe4, 0x52, 0x6f, 0x8b, 0xf3, 0x00, 0x92, 0x36,
	0x62, 0x3f, 0xd4, 0x0f, 0x63, 0x3d, 0x8c, 0x97, 0x60, 0x21, 0xc5, 0x82, 0x12, 0x74, 0x16, 0x6a,
	0x82, 0x62, 0xc8, 0xa8, 0xca, 0xc7, 0x3b, 0x3d, 0xc3, 0x81, 0x45, 0xa6, 0xd3, 0x89, 0xb4, 0x5a,
	0xea, 0xaf, 0xca, 0x3c, 0x4a, 0x29, 0x1e, 0x0a, 0x23, 0x94, 0x15, 0x46, 0x30, 0x56, 0x60, 0x29,
	0xff, 0x29, 0x4a, 0x8c, 0xcf, 0x6b, 0xb0, 0x22, 0xc1, 0x24, 0x13, 0x8f, 0x6b, 0xde, 0x48, 0xda,
	0x52, 0x22, 0x2d, 0x82, 0x99, 0x30, 0x1c, 0x0a, 0xc2, 0x7f, 0x2b, 0xb7, 0xc3, 0xcc, 0xf4, 0xdb,
	0xe1, 0x1c, 0xd4, 0x45, 0x88, 0x49, 0x4e, 0xad, 0x9a, 0x00, 0xec, 0xf4, 0xd0, 0x05, 0x98, 0x8b,
	0x1c, 0x4f, 0x3a, 0xaf, 0x40, 0x80, 0xd8, 0x71, 0x95, 0xf2, 0xb6, 0x6a, 0xda, 0xdb, 0x18, 0xf1,
	0x80, 0xf4, 0x86, 0xc4, 0x35, 0x41, 0x2c, 0x40, 0x9c, 0x38, 0x1b, 0xb1, 0xea, 0xf9, 0x88, 0x95,
	0x09, 0x7b, 0x90, 0x0f, 0x7b, 0xaa, 0x98, 0x36, 0x37, 0x5e, 0x4c, 0x9b, 0x9f, 0x28, 0xa6, 0x35,
	0xa6, 0x88, 0x69, 0xcd, 0xa2, 0x98, 0xb6, 0x02, 0xb3, 0x01, 0xb6, 0x98, 0xd2, 0xba, 0x08, 0xf3,
	0x62, 0xa4, 0x3e, 0x2b, 0x5a, 0x05, 0x67, 0xc5, 0xdb, 0xd0, 0xb4, 0x07, 0x41, 0x80, 0xbd, 0xb0,
	0x4b, 0x43, 0x2b, 0x1c, 0x50, 0xbe, 0xe1, 0xd5, 0x11, 0xc6, 0xc4, 0x4f, 0xb8, 0xd7, 0xee, 0x73,
	0x44, 0xb3, 0x11, 0x11, 0x8a, 0x21, 0x7a, 0x1d, 0x66, 0x78, 0x82, 0x85, 0xc6, 0x4e, 0xb0, 0x38,
	0x3e, 0xda, 0x86, 0x7a, 0x92, 0x49, 0x2d, 0x4e, 0x96, 0x49, 0x25, 0x94, 0xcc, 0x7a, 0x69, 0x45,
	0xba, 0x3d, 0x4c, 0xed, 0xf6, 0x12, 0xd7, 0xbb, 0x95, 0x12, 0xf5, 0x16, 0xa6, 0x36, 0x5b, 0x48,
	0x8b, 0x90, 0xc0, 0x3f, 0xc6, 0x02, 0x71, 0x59, 0x2c, 0x64, 0x04, 0x8b, 0x51, 0x52, 0xa9, 0xd6,
	0x4a, 0x2e, 0xd5, 0x4a, 0xe2, 0xf6, 0x99, 0x53, 0xe2, 0x76, 0x7b, 0xca, 0xb8, 0x6d, 0xfc, 0xa4,
	0x04, 0xe8, 0x36, 0x96, 0xa3, 0x2d, 0x65, 0x51, 0xe9, 0x5d, 0xd0, 0x03, 0xfc, 0xa4, 0x2b, 0xa4,
	0x8b, 0x56, 0x4e, 0x1b, 0x77, 0xe5, 0x9a, 0x41, 0x6a, 0xcc, 0x3c, 0xc9, 0x3f, 0x38, 0xa0, 0x58,
	0x24, 0x0c, 0x0d, 0x33, 0x1a, 0x31, 0xed, 0x5c, 0xa7, 0xef, 0x84, 0x3c, 0x36, 0x34, 0x4c, 0x31,
	0x18, 0x06, 0x94, 0x8a, 0x14, 0x50, 0x92, 0x94, 0x63, 0x36, 0x95, 0x72, 0xa8, 0x02, 0x4d, 0x75,
	0xea, 0x40, 0x63, 0x7c, 0x02, 0x8b, 0x39, 0x5b, 0xf0, 0x6c, 0x6e, 0x5e, 0x8a, 0x82, 0x34, 0xca,
	0xe4, 0xae, 0x8c, 0xfe, 0x82, 0x7c, 0x7e, 0xa5, 0xc8, 0x99, 0xda, 0xa1, 0x1f, 0x5a, 0x6e, 0x14,
	0x45, 0xc5, 0xc0, 0xd8, 0xe3, 0x59, 0xb5, 0x44, 0x15, 0x67, 0xd5, 0xd3, 0x46, 0x66, 0x03, 0xf3,
	0xdc, 0x3b, 0xc7, 0x91, 0x12, 0xf4, 0x2e, 0xcc, 0x49, 0xc4, 0x9c, 0xdf, 0x44, 0xfa, 0xc8, 0xd4,
	0xc6, 0x1f, 0x35, 0x58, 0xde, 0x12, 0x6e, 0x9d, 0xc9, 0x33, 0xc7, 0x94, 0x5c, 0x0e, 0xd0, 0xa5,
	0x4c, 0x80, 0x8e, 0x77, 0x7d, 0x79, 0xc2, 0x5d, 0x7f, 0x19, 0x16, 0x3c, 0x3f, 0x74, 0x0e, 0xa4,
	0x6f, 0x0b, 0x27, 0x6b, 0xca, 0xe0, 0x9d, 0x9e, 0xd1, 0x86, 0x15, 0x95, 0xf0, 0x94, 0x18, 0x7f,
	0xd0, 0x60, 0xc9, 0xc4, 0x1f, 0x62, 0x3b, 0xfc, 0x2f, 0x54, 0xeb, 0x0c, 0x2c, 0x2b, 0x64, 0xa7,
	0xc4, 0xf8, 0xab, 0x06, 0x8b, 0xdb, 0x3d, 0x27, 0xbc, 0x27, 0xe1, 0x33, 0xa5, 0x14, 0x9c, 0x4b,
	0x2a, 0xce, 0xdc, 0x7b, 0x9d, 0xd0, 0xc5, 0xd1, 0x81, 0x2f, 0x06, 0xa8, 0x0d, 0x55, 0xdb, 0xf7,
	0x42, 0xec, 0x85, 0x51, 0xde, 0x1f, 0x0f, 0x53, 0x66, 0xa8, 0x64, 0xcc, 0xb0, 0x04, 0x95, 0x01,
	0xb5, 0x0e, 0xe3, 0x0c, 0x5f, 0x0c, 0xd0, 0x6b, 0x50, 0xeb, 0xd3, 0x43, 0x79, 0x33, 0x77, 0x14,
	0x06, 0xba, 0x4b, 0x0f, 0xf9, 0x26, 0xae, 0xf6, 0xc5, 0x0f, 0x96, 0xf4, 0xe4, 0x15, 0xa3, 0xc4,
	0xf8, 0x79, 0x09, 0xe6, 0x65, 0xa0, 0x4a, 0x55, 0x6d, 0xb4, 0xaa, 0xa5, 0x02, 0x55, 0xcb, 0x69,
	0x55, 0x33, 0xa9, 0xc8, 0x4c, 0x2e, 0x15, 0xc9, 0xa4, 0x1b, 0x95, 0x5c, 0xba, 0x21, 0x1b, 0x6b,
	0xb6, 0xc8, 0x58, 0xd5, 0x22, 0x63, 0xd5, 0xc6, 0x37, 0xd6, 0x2e, 0x8f, 0x0d, 0xb2, 0x59, 0xee,
	0x38, 0x34, 0x64, 0x8e, 0x20, 0x33, 0xd4, 0xc6, 0x67, 0xf8, 0x10, 0xce, 0x28, 0x19, 0x52, 0x76,
	0x02, 0x37, 0x64, 0xc3, 0xc6, 0xf1, 0xf3, 0x82, 0x82, 0x6d, 0x6a, 0xf1, 0xd2, 0x54, 0xc6, 0x5b,
	0xb0, 0x7c, 0x0b, 0xbb, 0x38, 0xc4, 0xd3, 0xba, 0x2e, 0xdb, 0xeb, 0x2a, 0x0e, 0x94, 0x18, 0x9f,
	0x69, 0xb0, 0x28, 0x52, 0x79, 0x7e, 0x6e, 0xdd, 0x61, 0x07, 0x11, 0x63, 0x1d, 0x9f, 0x45, 0x9a,
	0x74, 0x16, 0x9d, 0x07, 0xa0, 0xa1, 0x15, 0x84, 0x62, 0x0d, 0xc5, 0x97, 0xea, 0x1c, 0xc2, 0x97,
	0xf0, 0x2c, 0xd4, 0xb0, 0xd7, 0x13, 0x93, 0xe2, 0xb8, 0xab, 0x62, 0xaf, 0x97, 0x5b, 0xdd, 0x99,
	0xcc, 0xea, 0x36, 0xa1, 0x14, 0x9d, 0x79, 0x0d, 0xb3, 0xe4, 0xf0, 0x14, 0x3e, 0x2f, 0x10, 0x25,
	0xc6, 0xdf, 0x34, 0x80, 0x04, 0xa4, 0xb8, 0x3d, 0xc4, 0x22, 0x97, 0x0a, 0x45, 0x2e, 0x8f, 0x12,
	0x79, 0xa6, 0x58, 0xe4, 0x4a, 0x3e, 0x79, 0x1e, 0x9d, 0x79, 0x67, 0xdc, 0xbd, 0x9a, 0x73, 0x77,
	0xa1, 0x74, 0x6d, 0xa8, 0xf4, 0xfb, 0xb0, 0x14, 0x9d, 0x58, 0x42, 0xbd, 0xd8, 0x27, 0x93, 0x04,
	0x42, 0x53, 0x27, 0x10, 0x25, 0x55, 0x02, 0x21, 0xdd, 0x48, 0x0c, 0x7f, 0x78, 0xba, 0xca, 0x9c,
	0x29, 0x41, 0x6f, 0x89, 0xb3, 0xfd, 0xa4, 0xcb, 0x69, 0x63, 0xdf, 0x3c, 0x5f, 0x10, 0x91, 0xa3,
	0x85, 0xe0, 0xe7, 0x9f, 0xf8, 0x5d, 0x74, 0x9c, 0x5f, 0x86, 0x45, 0xe1, 0x6b, 0x69, 0x87, 0xca,
	0x97, 0xc8, 0x56, 0x60, 0x29, 0x8f, 0x48, 0x89, 0x71, 0x09, 0x16, 0x6e, 0xe3, 0x90, 0xdf, 0x2c,
	0x8b, 0xeb, 0x6b, 0x9f, 0x69, 0xa0, 0xa7, 0xb1, 0x46, 0xde, 0x41, 0xd9, 0x8a, 0x88, 0x29, 0xff,
	0x23, 0x0f, 0x07, 0x91, 0xc4, 0xe2, 0x8e, 0xbb, 0xcb, 0x20, 0x99, 0x1b, 0x6f, 0x39, 0x73, 0xe3,
	0x45, 0xab, 0x30, 0xef, 0xd0, 0x6e, 0x80, 0x2d, 0x57, 0x20, 0xcc, 0xf0, 0xf2, 0x13, 0x38, 0xd4,
	0xc4, 0x96, 0xcb, 0xef, 0xc4, 0xff, 0x28, 0x41, 0x53, 0x24, 0xd0, 0x38, 0xba, 0x4e, 0x3c, 0x8d,
	0xf3, 0x32, 0xe3, 0x6a, 0xe5, 0x9c, 0xab, 0x3d, 0xe5, 0x1b, 0x67, 0x64, 0xe9, 0x4a, 0x7e, 0x5b,
	0xcd, 0x4a, 0xdb, 0x2a, 0x3e, 0xc5, 0x6b, 0x5f, 0xe4, 0x4a, 0x52, 0x9f, 0xf6, 0x4a, 0x62, 0x1c,
	0xf3, 0x18, 0x3e, 0x9c, 0x8a, 0xcc, 0xfd, 0x54, 0xf6, 0xcb, 0xa8, 0x50, 0x65, 0x7c, 0x9f, 0x87,
	0xfa, 0xfc, 0x77, 0x29, 0x41, 0xf7, 0xa0, 0x25, 0xd0, 0x70, 0x74, 0xcd, 0x74, 0x70, 0xbc, 0xa5,
	0x54, 0xf7, 0x86, 0xb4, 0xa3, 0x98, 0xba, 0x2f, 0x8f, 0x1d, 0x5c, 0xb4, 0xb7, 0x36, 0xa0, 0x93,
	0x4f, 0x6c, 0x6f, 0x9c, 0xbc, 0xe7, 0xa8, 0xcb, 0x38, 0xc6, 0x87, 0x70, 0xae, 0x10, 0xff, 0x69,
	0x67, 0xc3, 0x7f, 0x2a, 0xc1, 0x92, 0x84, 0x37, 0xdc, 0x99, 0xa3, 0x76, 0xe5, 0x1a, 0xe8, 0x62,
	0xaa, 0xe7, 0x50, 0xe2, 0x5a, 0x27, 0xd2, 0x09, 0xc6, 0xe1, 0xb7, 0x04, 0x78, 0xa7, 0x77, 0xda,
	0xf6, 0x8c, 0x54, 0x9f, 0xc9, 0xbb, 0xad, 0x7c, 0x99, 0x3a, 0x07, 0x75, 0xcf, 0xb1, 0x1f, 0x0b,
	0x1e, 0x51, 0x96, 0xc1, 0x00, 0xb9, 0xda, 0x5c, 0xb6, 0x5a, 0x72, 0x0e, 0xea, 0x1f, 0xfa, 0x8e,
	0x97, 0xd4, 0x4a, 0xca, 0x66, 0x8d, 0x01, 0x0a, 0x77, 0x60, 0x7d, 0xfa, 0xab, 0xd8, 0x5f, 0x34,
	0xbe, 0xc8, 0x59, 0x53, 0x4e, 0x77, 0x22, 0xc8, 0xb6, 0x2f, 0xa7, 0x6d, 0x9f, 0x37, 0xd9, 0xa8,
	0x23, 0xef, 0xeb, 0x50, 0x67, 0x82, 0x0a, 0xed, 0x66, 0xc7, 0xd6, 0xae, 0xc6, 0x26, 0xb9, 0x5a,
	0x27, 0xdc, 0x15, 0xd5, 0x5a, 0x51, 0x82, 0x6e, 0x41, 0xdd, 0xf1, 0x0e, 0xfc, 0xae, 0xeb, 0xd0,
	0x30, 0xda, 0x37, 0x97, 0x47, 0xf3, 0x4f, 0x42, 0x7f, 0x8d, 0x51, 0x32, 0x4e, 0x05, 0xbb, 0xe6,
	0x73, 0x0d, 0x56, 0xb6, 0x7a, 0xbd, 0x2c, 0x2d, 0xb3, 0xe6, 0x08, 0xdf, 0x4c, 0x69, 0x5c, 0x9a,
	0x5c, 0xe3, 0x94, 0x39, 0xcb, 0x99, 0x48, 0x72, 0x16, 0xce, 0x28, 0x25, 0xa2, 0xc4, 0xf8, 0xa5,
	0x06, 0xe7, 0x6e, 0x58, 0xa1, 0x7d, 0x54, 0x20, 0xf2, 0x70, 0x27, 0x0c, 0x4d, 0xd5, 0x88, 0x76,
	0x02, 0x37, 0xc1, 0x97, 0x2a, 0xf6, 0x16, 0x3c, 0x5b, 0x2c, 0x1a, 0x25, 0xe8, 0x22, 0xcc, 0xd3,
	0x81, 0x6d, 0x63, 0x9c, 0x92, 0x6e, 0x2e, 0x82, 0x31, 0xf9, 0xf8, 0x62, 0xdc, 0xc2, 0xee, 0x57,
	0x6c, 0x31, 0x94, 0x12, 0x51, 0x62, 0xfc, 0xbd, 0x04, 0x48, 0x9a, 0xd8, 0x25, 0xe1, 0x57, 0x2c,
	0xa4, 0xbd, 0xc9, 0xf4, 0x0a, 0xe5, 0x6d, 0xf9, 0x5c, 0x81, 0x5d, 0x76, 0x49, 0x28, 0x2e, 0x2e,
	0xbe, 0xf8, 0xc1, 0x94, 0xe0, 0xa4, 0x71, 0x86, 0x5a, 0x16, 0x53, 0xd9, 0xe4, 0xb7, 0x96, 0x89,
	0x04, 0x4f, 0x39, 0xdc, 0xfd, 0x5e, 0x83, 0x76, 0x3a, 0x30, 0xec, 0x92, 0xf0, 0x3f, 0x10, 0xec,
	0x52, 0x1e, 0x55, 0x99, 0x22, 0xa0, 0xb9, 0x70, 0xb6, 0x40, 0x6e, 0x4a, 0xd0, 0x9b, 0x30, 0x23,
	0x45, 0xb2, 0x17, 0x46, 0x33, 0x8e, 0xbc, 0xca, 0xe4, 0x24, 0x05, 0x31, 0xec, 0xb7, 0x5a, 0xf6,
	0x73, 0x22, 0xfe, 0xf5, 0x78, 0xd1, 0x32, 0xa5, 0x8c, 0x36, 0xc5, 0xf6, 0x48, 0x0c, 0x5d, 0x52,
	0x1b, 0xba, 0x2c, 0x1b, 0x3a, 0x1d, 0x82, 0x66, 0x32, 0x21, 0xc8, 0xf8, 0xa6, 0xf2, 0x00, 0xe3,
	0xa2, 0x52, 0x72, 0x5a, 0xfc, 0x52, 0xab, 0xff, 0x06, 0x3f, 0x3d, 0x38, 0x9f, 0x4c, 0xcf, 0x98,
	0x8e, 0x8e, 0x1c, 0xc6, 0x8f, 0x4a, 0xbc, 0xd3, 0x5d, 0x40, 0x4a, 0x09, 0xba, 0x06, 0x8b, 0x0e,
	0xed, 0x5a, 0xfd, 0x01, 0xc5, 0xdc, 0x58, 0xa2, 0x8d, 0xc7, 0xd9, 0xd4, 0x4c, 0xdd, 0xa1, 0x5b,
	0x6c, 0x86, 0x51, 0x72, 0x16, 0x11, 0xfa, 0xc9, 0xe0, 0xc4, 0xf1, 0x64, 0xf4, 0x52, 0x8c, 0xfe,
	0x01, 0x9b, 0x49, 0xd0, 0x37, 0x61, 0xc9, 0xa1, 0xdd, 0xa8, 0x87, 0x2d, 0xe1, 0x57, 0x38, 0x7e,
	0xcb, 0xa1, 0xdb, 0x7c, 0x2a, 0x21, 0xb8, 0x0a, 0x2d, 0x21, 0x8b, 0x9c, 0x99, 0x09, 0xeb, 0xeb,
	0x7c, 0x42, 0xee, 0x5d, 0x5d, 0x85, 0x96, 0x90, 0x44, 0x46, 0x16, 0x4e, 0xae, 0xf3, 0x09, 0x09,
	0xd9, 0xf8, 0xa7, 0x96, 0x8a, 0x65, 0xf1, 0x25, 0x65, 0x44, 0x2c, 0x93, 0x83, 0x4b, 0x69, 0xfa,
	0xe0, 0x52, 0x2e, 0x0e, 0x2e, 0x33, 0x63, 0x04, 0x97, 0xca, 0xf4, 0xc1, 0xe5, 0x53, 0xfe, 0x5c,
	0x21, 0xbb, 0xf6, 0xd2, 0x7d, 0xe1, 0x4b, 0x3c, 0x72, 0x8c, 0xef, 0xc2, 0x73, 0xa3, 0x3e, 0x4e,
	0x09, 0xfa, 0x3f, 0xa8, 0xb0, 0xd4, 0x85, 0x8e, 0x17, 0x26, 0x62, 0x4a, 0x41, 0x63, 0x3c, 0x54,
	0xb2, 0x7f, 0xe0, 0x84, 0x47, 0xcc, 0xca, 0xa7, 0x28, 0x77, 0x09, 0x1a, 0xd4, 0xb3, 0x08, 0x3d,
	0xf2, 0xa5, 0x6a, 0x4e, 0xd9, 0x9c, 0x8f, 0x81, 0xfc, 0xb9, 0xcb, 0x4f, 0x4b, 0x70, 0x61, 0xe4,
	0x27, 0xc4, 0x95, 0x9b, 0xf9, 0xf7, 0xc7, 0x62, 0x2f, 0x33, 0x9f, 0xae, 0x3a, 0x74, 0x9b, 0x0d,
	0x8b, 0x36, 0x56, 0x69, 0xb2, 0x8d, 0x55, 0x9e, 0x70, 0x63, 0xcd, 0x16, 0x6d, 0xac, 0x61, 0xdc,
	0x19, 0x5e, 0x91, 0xe3, 0xb8, 0xc3, 0x1d, 0xf6, 0x22, 0xcc, 0xbb, 0xce, 0xa3, 0xc0, 0x0a, 0x4e,
	0x12, 0xaf, 0x6b, 0x98, 0x73, 0x11, 0x8c, 0x2f, 0xe8, 0xcf, 0x34, 0x68, 0x31, 0x7e, 0x71, 0xb7,
	0x10, 0x07, 0x8e, 0xdf, 0xfb, 0xe2, 0xb1, 0xf7, 0x3c, 0xc0, 0x23, 0x7c, 0x18, 0xdf, 0x2e, 0xc4,
	0x42, 0xd4, 0x39, 0x44, 0x59, 0x56, 0x2b, 0x0f, 0x6b, 0x54, 0xc6, 0x5b, 0x70, 0xa9, 0xd8, 0xc3,
	0x84, 0x78, 0xa7, 0x44, 0xc7, 0x1f, 0x6b, 0xf0, 0xfc, 0xe9, 0x2c, 0x28, 0x41, 0xff, 0x0f, 0xb3,
	0x84, 0x8f, 0x22, 0x5f, 0x7d, 0xbe, 0x40, 0xc5, 0x34, 0x65, 0x44, 0x33, 0x9e, 0xbb, 0x6d, 0xc1,
	0xbc, 0x78, 0x29, 0xe1, 0xdf, 0xf0, 0xbd, 0x01, 0x1d, 0xe5, 0xbe, 0xbc, 0x43, 0xdb, 0xb7, 0x82,
	0xc7, 0x51, 0xbd, 0x24, 0x1a, 0x19, 0x67, 0x44, 0xb1, 0xcb, 0x75, 0x23, 0x1e, 0x71, 0x6a, 0x69,
	0xbc, 0x2f, 0x3a, 0x42, 0xd9, 0x09, 0x4a, 0xd0, 0xd7, 0x72, 0xc7, 0x91, 0xba, 0x40, 0x2b, 0x8b,
	0x26, 0x1f, 0x76, 0xb7, 0x01, 0xed, 0xe3, 0x30, 0xf3, 0xbd, 0x69, 0x64, 0x5f, 0x86, 0xc5, 0x1c,
	0x23, 0x4a, 0x8c, 0x3f, 0x6b, 0xa9, 0x97, 0x0b, 0x37, 0x8f, 0x2c, 0xef, 0x10, 0x6f, 0x1f, 0x63,
	0x2f, 0xfc, 0x52, 0xf3, 0x65, 0x39, 0xf4, 0x97, 0x27, 0x0b, 0xfd, 0x17, 0x60, 0xce, 0xe6, 0x52,
	0x26, 0x75, 0xd5, 0xb2, 0x09, 0x02, 0xc4, 0x16, 0x7a, 0xfd, 0xbd, 0xd4, 0x33, 0xc6, 0xa8, 0xec,
	0x34, 0x7f, 0x93, 0xfd, 0xd8, 0xf1, 0x8e, 0x2d, 0xd7, 0xe9, 0xe9, 0xcf, 0xa0, 0x26, 0x00, 0x87,
	0xf0, 0x28, 0xa1, 0xcf, 0x0c, 0xc7, 0x3c, 0x0c, 0xe8, 0xd5, 0x21, 0xc5, 0xf6, 0x3e, 0xdb, 0xe6,
	0x54, 0xaf, 0xad, 0x77, 0x61, 0x21, 0x53, 0x75, 0x42, 0x08, 0x9a, 0xec, 0xef, 0x03, 0xcb, 0x09,
	0xc5, 0xdb, 0x1a, 0xc1, 0x98, 0xc1, 0xa2, 0x71, 0x19, 0x2d, 0xc2, 0x02, 0x1b, 0xdf, 0xf4, 0xfb,
	0xfd, 0x81, 0xc7, 0x88, 0xb1, 0x5e, 0x49, 0x80, 0x43, 0x31, 0xf5, 0xea, 0xfa, 0xfb, 0xe2, 0x71,
	0x49, 0xee, 0x81, 0xe1, 0x12, 0xe8, 0xbb, 0x49, 0x5e, 0x11, 0x2b, 0xb0, 0x0c, 0xad, 0x5d, 0xa9,
	0x68, 0x12, 0xf8, 0xc7, 0x96, 0xab, 0x6b, 0x8c, 0x73, 0x52, 0xd3, 0xe2, 0x8d, 0x2a, 0xbd, 0xbc,
	0xfe, 0x09, 0x34, 0xd3, 0x9d, 0x64, 0x34, 0x07, 0xd5, 0x84, 0x55, 0x03, 0xea, 0x42, 0xdc, 0x10,
	0xf7, 0x74, 0x0d, 0xcd, 0x43, 0x4d, 0x50, 0xe2, 0x9e, 0x5e, 0x46, 0x2d, 0x68, 0x24, 0xb2, 0x3b,
	0xde, 0xa1, 0x5e, 0x61, 0xf8, 0x4c, 0x65, 0xce, 0x4f, 0x9f, 0x65, 0xbc, 0xb8, 0xdc, 0xb8, 0xa7,
	0x57, 0xd1, 0x02, 0xcc, 0x0d, 0xb5, 0xf2, 0x0e, 0x75, 0x58, 0x7f, 0x59, 0x00, 0xa2, 0x65, 0x64,
	0xf3, 0xd1, 0x87, 0xd9, 0x50, 0x7f, 0x06, 0x55, 0xa1, 0xbc, 0xd5, 0x63, 0x9f, 0xad, 0x42, 0xf9,
	0x16, 0x76, 0xf5, 0xd2, 0xba, 0x05, 0xcd, 0x74, 0x6f, 0x9d, 0x59, 0x60, 0xef, 0xce, 0xd6, 0xfd,
	0x6f, 0xec, 0x9a, 0x77, 0xbb, 0x3b, 0xf7, 0xbe, 0xb5, 0x75, 0x67, 0xe7, 0x96, 0xfe, 0x0c, 0x5b,
	0xa2, 0x21, 0x74, 0x6b, 0x6f, 0x4f, 0xd7, 0x52, 0x90, 0x07, 0xdb, 0x37, 0xf4, 0x12, 0xb3, 0xd2,
	0x10, 0xb2, 0xbb, 0xb7, 0x6d, 0x6e, 0xdd, 0xdf, 0x35, 0xf5, 0xf2, 0xfa, 0x6d, 0xa8, 0x46, 0x8d,
	0x16, 0xc6, 0xfb, 0xee, 0xfe, 0xed, 0xee, 0xfd, 0x0f, 0xf6, 0xb6, 0x25, 0xde, 0x2d, 0x68, 0x0c,
	0xa1, 0x7b, 0x5b, 0xfb, 0xfb, 0xc2, 0xb2, 0x43, 0x90, 0xb9, 0xfd, 0xce, 0xf6, 0xcd, 0xfb, 0x7a,
	0xe9, 0xfa, 0xaf, 0x3a, 0xa0, 0x67, 0xa3, 0x1b, 0x72, 0x78, 0xe7, 0x3f, 0xfb, 0x94, 0x76, 0x4d,
	0xb5, 0xeb, 0x55, 0xcf, 0x3d, 0x3b, 0x57, 0xc6, 0xc4, 0xa4, 0x04, 0x7d, 0x2f, 0x9b, 0xb6, 0x4b,
	0x0f, 0x26, 0xd1, 0xe6, 0x58, 0x7c, 0x92, 0x57, 0x99, 0x9d, 0x97, 0x27, 0x23, 0xa0, 0x04, 0x1d,
	0x40, 0x2b, 0xf7, 0xaa, 0x0c, 0xa9, 0x2a, 0x2b, 0xaa, 0xf7, 0x92, 0x9d, 0xb5, 0xf1, 0x10, 0x29,
	0x41, 0xf7, 0x61, 0x4e, 0x7a, 0x11, 0x86, 0x54, 0x35, 0xcf, 0xf4, 0xa3, 0xb3, 0x8e, 0x71, 0x1a,
	0x0a, 0x25, 0xc8, 0x06, 0x3d, 0xfb, 0x9c, 0x0b, 0xbd, 0x58, 0xd4, 0xa1, 0x48, 0x3f, 0x2f, 0xeb,
	0x5c, 0x1e, 0x0b, 0x8f, 0x12, 0xf4, 0x90, 0xf7, 0x1b, 0xe4, 0xb7, 0x0f, 0xe8, 0x05, 0xb5, 0x9d,
	0x33, 0x6f, 0x45, 0x3a, 0x2f, 0x8e, 0x83, 0x46, 0x49, 0xe4, 0x6f, 0x99, 0x32, 0x6c, 0x91, 0xbf,
	0xe5, 0x1f, 0x42, 0x14, 0xf9, 0x9b, 0xea, 0x81, 0x83, 0x03, 0x28, 0xdf, 0xd5, 0x57, 0x7e, 0x4a,
	0xf9, 0x72, 0x41, 0xf9, 0x29, 0xf5, 0x33, 0x01, 0xe6, 0x5a, 0xb9, 0x4e, 0xbb, 0xd2, 0xb5, 0x54,
	0x6f, 0x09, 0x94, 0xae, 0xa5, 0x6c, 0xdc, 0xa3, 0x07, 0x30, 0x2f, 0x77, 0x7a, 0x90, 0xa1, 0xb6,
	0x86, 0xdc, 0x30, 0xea, 0x5c, 0x3a, 0x15, 0x87, 0x12, 0xe4, 0xf2, 0x47, 0x2f, 0xd9, 0x72, 0x3e,
	0x2a, 0xb0, 0xb6, 0xa2, 0xdd, 0xd0, 0x59, 0x1f, 0x17, 0x95, 0x12, 0xf4, 0x31, 0x6f, 0x1e, 0xa8,
	0x6a, 0xf1, 0xe8, 0xda, 0x58, 0xeb, 0x1b, 0xd7, 0xf9, 0x3b, 0x1b, 0x93, 0xa0, 0x8b, 0x5d, 0x94,
	0x7d, 0x1f, 0xa0, 0xdc, 0x45, 0x8a, 0xd7, 0x11, 0xca, 0x5d, 0xa4, 0x7a, 0x6c, 0x10, 0x19, 0x33,
	0xdb, 0x06, 0x2f, 0x32, 0xa6, 0xa2, 0xff, 0x5e, 0x64, 0x4c, 0x65, 0x67, 0xdd, 0x01, 0x94, 0x6f,
	0x68, 0x2b, 0xdd, 0x5c, 0xd9, 0x39, 0x57, 0xba, 0xb9, 0xba, 0x43, 0xce, 0xac, 0x97, 0xed, 0x47,
	0x2b, 0xad, 0xa7, 0xe8, 0xa2, 0x2b, 0xad, 0xa7, 0x6a, 0x6e, 0xb3, 0xbd, 0x94, 0xeb, 0xd2, 0x2a,
	0xf7, 0x92, 0xaa, 0x4b, 0xdc, 0x59, 0x1b, 0x0f, 0x51, 0x28, 0x93, 0xed, 0xb9, 0x2a, 0x95, 0x51,
	0x74, 0x70, 0x95, 0xca, 0xa8, 0x1a, 0xb8, 0x91, 0xa7, 0xab, 0x4a, 0xfd, 0x45, 0x9e, 0x5e, 0xd0,
	0xec, 0x28, 0xf2, 0xf4, 0xc2, 0x2e, 0x82, 0x0b, 0x8b, 0x8a, 0xd2, 0xb4, 0xd2, 0x09, 0xd5, 0xd5,
	0x75, 0xa5, 0x13, 0x16, 0x55, 0xbb, 0x3f, 0x85, 0x76, 0x51, 0x35, 0x1c, 0xa9, 0x24, 0x1f, 0x51,
	0xd5, 0xef, 0x6c, 0x4e, 0x84, 0x2f, 0x54, 0x55, 0x14, 0xad, 0x51, 0x81, 0x63, 0x8f, 0xab, 0x6a,
	0x41, 0x1d, 0x1c, 0x05, 0xd9, 0xff, 0x7c, 0x89, 0x8a, 0x9d, 0xe8, 0xea, 0xa9, 0x2b, 0x94, 0x94,
	0x73, 0x3b, 0x2f, 0x8d, 0x8f, 0x4c, 0x09, 0x1a, 0x64, 0xff, 0x83, 0x26, 0x2e, 0x23, 0xa2, 0x97,
	0xc6, 0x73, 0x0b, 0x51, 0x1c, 0xed, 0x5c, 0x9b, 0x00, 0x5b, 0xac, 0x6a, 0x51, 0xbd, 0x10, 0x6d,
	0x8c, 0x38, 0x56, 0x14, 0x75, 0xc9, 0xce, 0xe6, 0x44, 0xf8, 0x94, 0xa0, 0x1f, 0x88, 0xe6, 0x5f,
	0xc1, 0x7d, 0x1c, 0xbd, 0x3c, 0x1e, 0x3f, 0xe9, 0x84, 0xfa, 0x9f, 0x09, 0x29, 0x28, 0x41, 0x3f,
	0xd4, 0x94, 0xc5, 0xd6, 0xb8, 0xec, 0x83, 0xc6, 0x64, 0x29, 0x55, 0xa2, 0x3a, 0xd7, 0x27, 0x25,
	0xa1, 0x04, 0xfd, 0x42, 0x83, 0xd5, 0xd3, 0x4a, 0x13, 0xe8, 0xf5, 0x89, 0xd4, 0x1b, 0x96, 0x44,
	0x3a, 0xff, 0x3b, 0x15, 0x5d, 0x92, 0xca, 0xa5, 0x0b, 0x09, 0x85, 0xa9, 0x5c, 0xae, 0x10, 0x51,
	0x98, 0xca, 0x29, 0x2a, 0x13, 0x0f, 0x61, 0x21, 0x53, 0x10, 0x50, 0xe6, 0xa5, 0xf9, 0xea, 0x83,
	0x32, 0x2f, 0x55, 0xd4, 0x16, 0x6e, 0xbc, 0xf6, 0xed, 0x57, 0x0e, 0x7d, 0xd7, 0xf2, 0x0e, 0x37,
	0x5e, 0xbb, 0x1e, 0x86, 0x1b, 0xb6, 0xdf, 0xdf, 0xe4, 0xff, 0x7c, 0x68, 0xfb, 0xee, 0x26, 0xc5,
	0xc1, 0xb1, 0x63, 0x63, 0x9a, 0xff, 0x07, 0xc5, 0x47, 0xb3, 0x1c, 0xe9, 0x95, 0x7f, 0x07, 0x00,
	0x00, 0xff, 0xff, 0x0d, 0x34, 0x7e, 0x99, 0xdd, 0x38, 0x00, 0x00,
}
