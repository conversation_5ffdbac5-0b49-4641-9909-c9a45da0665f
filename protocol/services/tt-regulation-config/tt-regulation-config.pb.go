// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt-regulation-config/tt-regulation-config.proto

package tt_regulation_config // import "golang.52tt.com/protocol/services/tt-regulation-config"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SearchType int32

const (
	SearchType_UNKNOWN SearchType = 0
	SearchType_USER    SearchType = 1
	SearchType_CHANNEL SearchType = 2
	SearchType_GUILD   SearchType = 3
)

var SearchType_name = map[int32]string{
	0: "UNKNOWN",
	1: "USER",
	2: "CHANNEL",
	3: "GUILD",
}
var SearchType_value = map[string]int32{
	"UNKNOWN": 0,
	"USER":    1,
	"CHANNEL": 2,
	"GUILD":   3,
}

func (x SearchType) String() string {
	return proto.EnumName(SearchType_name, int32(x))
}
func (SearchType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{0}
}

// 枚举定义, 按位递增 0,1,2,4,...
type ControlType int32

const (
	ControlType_ENUM_NO_CONTROL                     ControlType = 0
	ControlType_ENUM_CONTROL_BY_REGISTER_TS         ControlType = 1
	ControlType_ENUM_CONTROL_BY_UNUSAL_DEVICE_LOGIN ControlType = 2
	ControlType_ENUM_CONTROL_BY_UNBIND_PHONE        ControlType = 4
	ControlType_ENUM_CONTROL_BY_GAMESDK_FIRST_LOGIN ControlType = 8
)

var ControlType_name = map[int32]string{
	0: "ENUM_NO_CONTROL",
	1: "ENUM_CONTROL_BY_REGISTER_TS",
	2: "ENUM_CONTROL_BY_UNUSAL_DEVICE_LOGIN",
	4: "ENUM_CONTROL_BY_UNBIND_PHONE",
	8: "ENUM_CONTROL_BY_GAMESDK_FIRST_LOGIN",
}
var ControlType_value = map[string]int32{
	"ENUM_NO_CONTROL":                     0,
	"ENUM_CONTROL_BY_REGISTER_TS":         1,
	"ENUM_CONTROL_BY_UNUSAL_DEVICE_LOGIN": 2,
	"ENUM_CONTROL_BY_UNBIND_PHONE":        4,
	"ENUM_CONTROL_BY_GAMESDK_FIRST_LOGIN": 8,
}

func (x ControlType) String() string {
	return proto.EnumName(ControlType_name, int32(x))
}
func (ControlType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{1}
}

// 白名单开关，true 开 false 关
type SwitchSearchWhiteListReq struct {
	WhiteListStatus      bool     `protobuf:"varint,1,opt,name=white_list_status,json=whiteListStatus,proto3" json:"white_list_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchSearchWhiteListReq) Reset()         { *m = SwitchSearchWhiteListReq{} }
func (m *SwitchSearchWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*SwitchSearchWhiteListReq) ProtoMessage()    {}
func (*SwitchSearchWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{0}
}
func (m *SwitchSearchWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchSearchWhiteListReq.Unmarshal(m, b)
}
func (m *SwitchSearchWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchSearchWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *SwitchSearchWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchSearchWhiteListReq.Merge(dst, src)
}
func (m *SwitchSearchWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_SwitchSearchWhiteListReq.Size(m)
}
func (m *SwitchSearchWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchSearchWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchSearchWhiteListReq proto.InternalMessageInfo

func (m *SwitchSearchWhiteListReq) GetWhiteListStatus() bool {
	if m != nil {
		return m.WhiteListStatus
	}
	return false
}

// 白名单开关，true 开 false 关
type SwitchSearchWhiteListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchSearchWhiteListResp) Reset()         { *m = SwitchSearchWhiteListResp{} }
func (m *SwitchSearchWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*SwitchSearchWhiteListResp) ProtoMessage()    {}
func (*SwitchSearchWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{1}
}
func (m *SwitchSearchWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchSearchWhiteListResp.Unmarshal(m, b)
}
func (m *SwitchSearchWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchSearchWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *SwitchSearchWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchSearchWhiteListResp.Merge(dst, src)
}
func (m *SwitchSearchWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_SwitchSearchWhiteListResp.Size(m)
}
func (m *SwitchSearchWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchSearchWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchSearchWhiteListResp proto.InternalMessageInfo

// 获取白名单开关状态
type GetSearchWhiteListStatusReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSearchWhiteListStatusReq) Reset()         { *m = GetSearchWhiteListStatusReq{} }
func (m *GetSearchWhiteListStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetSearchWhiteListStatusReq) ProtoMessage()    {}
func (*GetSearchWhiteListStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{2}
}
func (m *GetSearchWhiteListStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSearchWhiteListStatusReq.Unmarshal(m, b)
}
func (m *GetSearchWhiteListStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSearchWhiteListStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetSearchWhiteListStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSearchWhiteListStatusReq.Merge(dst, src)
}
func (m *GetSearchWhiteListStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetSearchWhiteListStatusReq.Size(m)
}
func (m *GetSearchWhiteListStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSearchWhiteListStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSearchWhiteListStatusReq proto.InternalMessageInfo

type GetSearchWhiteListStatusResp struct {
	WhiteListStatus      bool     `protobuf:"varint,1,opt,name=white_list_status,json=whiteListStatus,proto3" json:"white_list_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSearchWhiteListStatusResp) Reset()         { *m = GetSearchWhiteListStatusResp{} }
func (m *GetSearchWhiteListStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetSearchWhiteListStatusResp) ProtoMessage()    {}
func (*GetSearchWhiteListStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{3}
}
func (m *GetSearchWhiteListStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSearchWhiteListStatusResp.Unmarshal(m, b)
}
func (m *GetSearchWhiteListStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSearchWhiteListStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetSearchWhiteListStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSearchWhiteListStatusResp.Merge(dst, src)
}
func (m *GetSearchWhiteListStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetSearchWhiteListStatusResp.Size(m)
}
func (m *GetSearchWhiteListStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSearchWhiteListStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSearchWhiteListStatusResp proto.InternalMessageInfo

func (m *GetSearchWhiteListStatusResp) GetWhiteListStatus() bool {
	if m != nil {
		return m.WhiteListStatus
	}
	return false
}

// 添加白名单
// 不用填create_time，begin_time、end_time都为0则为永久白名单
type BatchAddSearchWhiteListReq struct {
	ItemList             []*SearchWhiteListItem `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BatchAddSearchWhiteListReq) Reset()         { *m = BatchAddSearchWhiteListReq{} }
func (m *BatchAddSearchWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddSearchWhiteListReq) ProtoMessage()    {}
func (*BatchAddSearchWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{4}
}
func (m *BatchAddSearchWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddSearchWhiteListReq.Unmarshal(m, b)
}
func (m *BatchAddSearchWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddSearchWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddSearchWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddSearchWhiteListReq.Merge(dst, src)
}
func (m *BatchAddSearchWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddSearchWhiteListReq.Size(m)
}
func (m *BatchAddSearchWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddSearchWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddSearchWhiteListReq proto.InternalMessageInfo

func (m *BatchAddSearchWhiteListReq) GetItemList() []*SearchWhiteListItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type BatchAddSearchWhiteListResp struct {
	FailList             []*SearchWhiteListFailResult `protobuf:"bytes,1,rep,name=fail_list,json=failList,proto3" json:"fail_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *BatchAddSearchWhiteListResp) Reset()         { *m = BatchAddSearchWhiteListResp{} }
func (m *BatchAddSearchWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddSearchWhiteListResp) ProtoMessage()    {}
func (*BatchAddSearchWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{5}
}
func (m *BatchAddSearchWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddSearchWhiteListResp.Unmarshal(m, b)
}
func (m *BatchAddSearchWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddSearchWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddSearchWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddSearchWhiteListResp.Merge(dst, src)
}
func (m *BatchAddSearchWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddSearchWhiteListResp.Size(m)
}
func (m *BatchAddSearchWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddSearchWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddSearchWhiteListResp proto.InternalMessageInfo

func (m *BatchAddSearchWhiteListResp) GetFailList() []*SearchWhiteListFailResult {
	if m != nil {
		return m.FailList
	}
	return nil
}

type SearchWhiteListItem struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemContent          string   `protobuf:"bytes,2,opt,name=item_content,json=itemContent,proto3" json:"item_content,omitempty"`
	OperaterId           string   `protobuf:"bytes,3,opt,name=operater_id,json=operaterId,proto3" json:"operater_id,omitempty"`
	CreateTime           uint32   `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	BeginTime            uint32   `protobuf:"varint,5,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchWhiteListItem) Reset()         { *m = SearchWhiteListItem{} }
func (m *SearchWhiteListItem) String() string { return proto.CompactTextString(m) }
func (*SearchWhiteListItem) ProtoMessage()    {}
func (*SearchWhiteListItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{6}
}
func (m *SearchWhiteListItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchWhiteListItem.Unmarshal(m, b)
}
func (m *SearchWhiteListItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchWhiteListItem.Marshal(b, m, deterministic)
}
func (dst *SearchWhiteListItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchWhiteListItem.Merge(dst, src)
}
func (m *SearchWhiteListItem) XXX_Size() int {
	return xxx_messageInfo_SearchWhiteListItem.Size(m)
}
func (m *SearchWhiteListItem) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchWhiteListItem.DiscardUnknown(m)
}

var xxx_messageInfo_SearchWhiteListItem proto.InternalMessageInfo

func (m *SearchWhiteListItem) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *SearchWhiteListItem) GetItemContent() string {
	if m != nil {
		return m.ItemContent
	}
	return ""
}

func (m *SearchWhiteListItem) GetOperaterId() string {
	if m != nil {
		return m.OperaterId
	}
	return ""
}

func (m *SearchWhiteListItem) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *SearchWhiteListItem) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *SearchWhiteListItem) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type SearchWhiteListFailResult struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemContent          string   `protobuf:"bytes,2,opt,name=item_content,json=itemContent,proto3" json:"item_content,omitempty"`
	FailReason           string   `protobuf:"bytes,3,opt,name=fail_reason,json=failReason,proto3" json:"fail_reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchWhiteListFailResult) Reset()         { *m = SearchWhiteListFailResult{} }
func (m *SearchWhiteListFailResult) String() string { return proto.CompactTextString(m) }
func (*SearchWhiteListFailResult) ProtoMessage()    {}
func (*SearchWhiteListFailResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{7}
}
func (m *SearchWhiteListFailResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchWhiteListFailResult.Unmarshal(m, b)
}
func (m *SearchWhiteListFailResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchWhiteListFailResult.Marshal(b, m, deterministic)
}
func (dst *SearchWhiteListFailResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchWhiteListFailResult.Merge(dst, src)
}
func (m *SearchWhiteListFailResult) XXX_Size() int {
	return xxx_messageInfo_SearchWhiteListFailResult.Size(m)
}
func (m *SearchWhiteListFailResult) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchWhiteListFailResult.DiscardUnknown(m)
}

var xxx_messageInfo_SearchWhiteListFailResult proto.InternalMessageInfo

func (m *SearchWhiteListFailResult) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *SearchWhiteListFailResult) GetItemContent() string {
	if m != nil {
		return m.ItemContent
	}
	return ""
}

func (m *SearchWhiteListFailResult) GetFailReason() string {
	if m != nil {
		return m.FailReason
	}
	return ""
}

// 删除白名单
// 只填id即可
type BatchDelSearchWhiteListReq struct {
	ItemList             []*SearchWhiteListItem `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BatchDelSearchWhiteListReq) Reset()         { *m = BatchDelSearchWhiteListReq{} }
func (m *BatchDelSearchWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*BatchDelSearchWhiteListReq) ProtoMessage()    {}
func (*BatchDelSearchWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{8}
}
func (m *BatchDelSearchWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelSearchWhiteListReq.Unmarshal(m, b)
}
func (m *BatchDelSearchWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelSearchWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *BatchDelSearchWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelSearchWhiteListReq.Merge(dst, src)
}
func (m *BatchDelSearchWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_BatchDelSearchWhiteListReq.Size(m)
}
func (m *BatchDelSearchWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelSearchWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelSearchWhiteListReq proto.InternalMessageInfo

func (m *BatchDelSearchWhiteListReq) GetItemList() []*SearchWhiteListItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type BatchDelSearchWhiteListResp struct {
	FailList             []*SearchWhiteListFailResult `protobuf:"bytes,1,rep,name=fail_list,json=failList,proto3" json:"fail_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *BatchDelSearchWhiteListResp) Reset()         { *m = BatchDelSearchWhiteListResp{} }
func (m *BatchDelSearchWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*BatchDelSearchWhiteListResp) ProtoMessage()    {}
func (*BatchDelSearchWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{9}
}
func (m *BatchDelSearchWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelSearchWhiteListResp.Unmarshal(m, b)
}
func (m *BatchDelSearchWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelSearchWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *BatchDelSearchWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelSearchWhiteListResp.Merge(dst, src)
}
func (m *BatchDelSearchWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_BatchDelSearchWhiteListResp.Size(m)
}
func (m *BatchDelSearchWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelSearchWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelSearchWhiteListResp proto.InternalMessageInfo

func (m *BatchDelSearchWhiteListResp) GetFailList() []*SearchWhiteListFailResult {
	if m != nil {
		return m.FailList
	}
	return nil
}

type GetSearchWhiteListReq struct {
	Content              string   `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	BeginTime            uint32   `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Page                 uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSearchWhiteListReq) Reset()         { *m = GetSearchWhiteListReq{} }
func (m *GetSearchWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*GetSearchWhiteListReq) ProtoMessage()    {}
func (*GetSearchWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{10}
}
func (m *GetSearchWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSearchWhiteListReq.Unmarshal(m, b)
}
func (m *GetSearchWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSearchWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *GetSearchWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSearchWhiteListReq.Merge(dst, src)
}
func (m *GetSearchWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_GetSearchWhiteListReq.Size(m)
}
func (m *GetSearchWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSearchWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSearchWhiteListReq proto.InternalMessageInfo

func (m *GetSearchWhiteListReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GetSearchWhiteListReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetSearchWhiteListReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetSearchWhiteListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetSearchWhiteListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetSearchWhiteListResp struct {
	ItemList             []*SearchWhiteListItem `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	TotalCount           uint32                 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetSearchWhiteListResp) Reset()         { *m = GetSearchWhiteListResp{} }
func (m *GetSearchWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*GetSearchWhiteListResp) ProtoMessage()    {}
func (*GetSearchWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{11}
}
func (m *GetSearchWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSearchWhiteListResp.Unmarshal(m, b)
}
func (m *GetSearchWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSearchWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *GetSearchWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSearchWhiteListResp.Merge(dst, src)
}
func (m *GetSearchWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_GetSearchWhiteListResp.Size(m)
}
func (m *GetSearchWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSearchWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSearchWhiteListResp proto.InternalMessageInfo

func (m *GetSearchWhiteListResp) GetItemList() []*SearchWhiteListItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *GetSearchWhiteListResp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

// 导出热词
type ExportHotWordReq struct {
	BeginTime            uint32   `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExportHotWordReq) Reset()         { *m = ExportHotWordReq{} }
func (m *ExportHotWordReq) String() string { return proto.CompactTextString(m) }
func (*ExportHotWordReq) ProtoMessage()    {}
func (*ExportHotWordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{12}
}
func (m *ExportHotWordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExportHotWordReq.Unmarshal(m, b)
}
func (m *ExportHotWordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExportHotWordReq.Marshal(b, m, deterministic)
}
func (dst *ExportHotWordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExportHotWordReq.Merge(dst, src)
}
func (m *ExportHotWordReq) XXX_Size() int {
	return xxx_messageInfo_ExportHotWordReq.Size(m)
}
func (m *ExportHotWordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExportHotWordReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExportHotWordReq proto.InternalMessageInfo

func (m *ExportHotWordReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ExportHotWordReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type ExportHotWordResp struct {
	WordList             []string `protobuf:"bytes,1,rep,name=word_list,json=wordList,proto3" json:"word_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExportHotWordResp) Reset()         { *m = ExportHotWordResp{} }
func (m *ExportHotWordResp) String() string { return proto.CompactTextString(m) }
func (*ExportHotWordResp) ProtoMessage()    {}
func (*ExportHotWordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{13}
}
func (m *ExportHotWordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExportHotWordResp.Unmarshal(m, b)
}
func (m *ExportHotWordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExportHotWordResp.Marshal(b, m, deterministic)
}
func (dst *ExportHotWordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExportHotWordResp.Merge(dst, src)
}
func (m *ExportHotWordResp) XXX_Size() int {
	return xxx_messageInfo_ExportHotWordResp.Size(m)
}
func (m *ExportHotWordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExportHotWordResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExportHotWordResp proto.InternalMessageInfo

func (m *ExportHotWordResp) GetWordList() []string {
	if m != nil {
		return m.WordList
	}
	return nil
}

// 增加搜索次数计数
type AddSearchCountReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SearchType           uint32   `protobuf:"varint,2,opt,name=search_type,json=searchType,proto3" json:"search_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddSearchCountReq) Reset()         { *m = AddSearchCountReq{} }
func (m *AddSearchCountReq) String() string { return proto.CompactTextString(m) }
func (*AddSearchCountReq) ProtoMessage()    {}
func (*AddSearchCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{14}
}
func (m *AddSearchCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSearchCountReq.Unmarshal(m, b)
}
func (m *AddSearchCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSearchCountReq.Marshal(b, m, deterministic)
}
func (dst *AddSearchCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSearchCountReq.Merge(dst, src)
}
func (m *AddSearchCountReq) XXX_Size() int {
	return xxx_messageInfo_AddSearchCountReq.Size(m)
}
func (m *AddSearchCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSearchCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddSearchCountReq proto.InternalMessageInfo

func (m *AddSearchCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddSearchCountReq) GetSearchType() uint32 {
	if m != nil {
		return m.SearchType
	}
	return 0
}

type AddSearchCountResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddSearchCountResp) Reset()         { *m = AddSearchCountResp{} }
func (m *AddSearchCountResp) String() string { return proto.CompactTextString(m) }
func (*AddSearchCountResp) ProtoMessage()    {}
func (*AddSearchCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{15}
}
func (m *AddSearchCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSearchCountResp.Unmarshal(m, b)
}
func (m *AddSearchCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSearchCountResp.Marshal(b, m, deterministic)
}
func (dst *AddSearchCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSearchCountResp.Merge(dst, src)
}
func (m *AddSearchCountResp) XXX_Size() int {
	return xxx_messageInfo_AddSearchCountResp.Size(m)
}
func (m *AddSearchCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSearchCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddSearchCountResp proto.InternalMessageInfo

// 获取次数计数
type GetSearchCountReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SearchType           uint32   `protobuf:"varint,2,opt,name=search_type,json=searchType,proto3" json:"search_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSearchCountReq) Reset()         { *m = GetSearchCountReq{} }
func (m *GetSearchCountReq) String() string { return proto.CompactTextString(m) }
func (*GetSearchCountReq) ProtoMessage()    {}
func (*GetSearchCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{16}
}
func (m *GetSearchCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSearchCountReq.Unmarshal(m, b)
}
func (m *GetSearchCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSearchCountReq.Marshal(b, m, deterministic)
}
func (dst *GetSearchCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSearchCountReq.Merge(dst, src)
}
func (m *GetSearchCountReq) XXX_Size() int {
	return xxx_messageInfo_GetSearchCountReq.Size(m)
}
func (m *GetSearchCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSearchCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSearchCountReq proto.InternalMessageInfo

func (m *GetSearchCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetSearchCountReq) GetSearchType() uint32 {
	if m != nil {
		return m.SearchType
	}
	return 0
}

type GetSearchCountResp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	CanSearch            bool     `protobuf:"varint,2,opt,name=can_search,json=canSearch,proto3" json:"can_search,omitempty"`
	ResultLimit          uint32   `protobuf:"varint,3,opt,name=result_limit,json=resultLimit,proto3" json:"result_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSearchCountResp) Reset()         { *m = GetSearchCountResp{} }
func (m *GetSearchCountResp) String() string { return proto.CompactTextString(m) }
func (*GetSearchCountResp) ProtoMessage()    {}
func (*GetSearchCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{17}
}
func (m *GetSearchCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSearchCountResp.Unmarshal(m, b)
}
func (m *GetSearchCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSearchCountResp.Marshal(b, m, deterministic)
}
func (dst *GetSearchCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSearchCountResp.Merge(dst, src)
}
func (m *GetSearchCountResp) XXX_Size() int {
	return xxx_messageInfo_GetSearchCountResp.Size(m)
}
func (m *GetSearchCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSearchCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSearchCountResp proto.InternalMessageInfo

func (m *GetSearchCountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetSearchCountResp) GetCanSearch() bool {
	if m != nil {
		return m.CanSearch
	}
	return false
}

func (m *GetSearchCountResp) GetResultLimit() uint32 {
	if m != nil {
		return m.ResultLimit
	}
	return 0
}

// 某词是否命中白名单
type IsWordInWhiteListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Word                 string   `protobuf:"bytes,2,opt,name=word,proto3" json:"word,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsWordInWhiteListReq) Reset()         { *m = IsWordInWhiteListReq{} }
func (m *IsWordInWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*IsWordInWhiteListReq) ProtoMessage()    {}
func (*IsWordInWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{18}
}
func (m *IsWordInWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsWordInWhiteListReq.Unmarshal(m, b)
}
func (m *IsWordInWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsWordInWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *IsWordInWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsWordInWhiteListReq.Merge(dst, src)
}
func (m *IsWordInWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_IsWordInWhiteListReq.Size(m)
}
func (m *IsWordInWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsWordInWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsWordInWhiteListReq proto.InternalMessageInfo

func (m *IsWordInWhiteListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *IsWordInWhiteListReq) GetWord() string {
	if m != nil {
		return m.Word
	}
	return ""
}

type IsWordInWhiteListResp struct {
	InWhiteList          bool     `protobuf:"varint,1,opt,name=in_white_list,json=inWhiteList,proto3" json:"in_white_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsWordInWhiteListResp) Reset()         { *m = IsWordInWhiteListResp{} }
func (m *IsWordInWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*IsWordInWhiteListResp) ProtoMessage()    {}
func (*IsWordInWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{19}
}
func (m *IsWordInWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsWordInWhiteListResp.Unmarshal(m, b)
}
func (m *IsWordInWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsWordInWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *IsWordInWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsWordInWhiteListResp.Merge(dst, src)
}
func (m *IsWordInWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_IsWordInWhiteListResp.Size(m)
}
func (m *IsWordInWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsWordInWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsWordInWhiteListResp proto.InternalMessageInfo

func (m *IsWordInWhiteListResp) GetInWhiteList() bool {
	if m != nil {
		return m.InWhiteList
	}
	return false
}

// 某词是否命中白名单
type AuthSearchEventReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Word                 string   `protobuf:"bytes,2,opt,name=word,proto3" json:"word,omitempty"`
	SearchType           uint32   `protobuf:"varint,3,opt,name=search_type,json=searchType,proto3" json:"search_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AuthSearchEventReq) Reset()         { *m = AuthSearchEventReq{} }
func (m *AuthSearchEventReq) String() string { return proto.CompactTextString(m) }
func (*AuthSearchEventReq) ProtoMessage()    {}
func (*AuthSearchEventReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{20}
}
func (m *AuthSearchEventReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuthSearchEventReq.Unmarshal(m, b)
}
func (m *AuthSearchEventReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuthSearchEventReq.Marshal(b, m, deterministic)
}
func (dst *AuthSearchEventReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuthSearchEventReq.Merge(dst, src)
}
func (m *AuthSearchEventReq) XXX_Size() int {
	return xxx_messageInfo_AuthSearchEventReq.Size(m)
}
func (m *AuthSearchEventReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AuthSearchEventReq.DiscardUnknown(m)
}

var xxx_messageInfo_AuthSearchEventReq proto.InternalMessageInfo

func (m *AuthSearchEventReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AuthSearchEventReq) GetWord() string {
	if m != nil {
		return m.Word
	}
	return ""
}

func (m *AuthSearchEventReq) GetSearchType() uint32 {
	if m != nil {
		return m.SearchType
	}
	return 0
}

type AuthSearchEventResp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	CanSearch            bool     `protobuf:"varint,2,opt,name=can_search,json=canSearch,proto3" json:"can_search,omitempty"`
	ResultLimit          uint32   `protobuf:"varint,3,opt,name=result_limit,json=resultLimit,proto3" json:"result_limit,omitempty"`
	InWhiteList          bool     `protobuf:"varint,4,opt,name=in_white_list,json=inWhiteList,proto3" json:"in_white_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AuthSearchEventResp) Reset()         { *m = AuthSearchEventResp{} }
func (m *AuthSearchEventResp) String() string { return proto.CompactTextString(m) }
func (*AuthSearchEventResp) ProtoMessage()    {}
func (*AuthSearchEventResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{21}
}
func (m *AuthSearchEventResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuthSearchEventResp.Unmarshal(m, b)
}
func (m *AuthSearchEventResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuthSearchEventResp.Marshal(b, m, deterministic)
}
func (dst *AuthSearchEventResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuthSearchEventResp.Merge(dst, src)
}
func (m *AuthSearchEventResp) XXX_Size() int {
	return xxx_messageInfo_AuthSearchEventResp.Size(m)
}
func (m *AuthSearchEventResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AuthSearchEventResp.DiscardUnknown(m)
}

var xxx_messageInfo_AuthSearchEventResp proto.InternalMessageInfo

func (m *AuthSearchEventResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *AuthSearchEventResp) GetCanSearch() bool {
	if m != nil {
		return m.CanSearch
	}
	return false
}

func (m *AuthSearchEventResp) GetResultLimit() uint32 {
	if m != nil {
		return m.ResultLimit
	}
	return 0
}

func (m *AuthSearchEventResp) GetInWhiteList() bool {
	if m != nil {
		return m.InWhiteList
	}
	return false
}

type TriggerCondition struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	TriggerCnt           uint32   `protobuf:"varint,2,opt,name=trigger_cnt,json=triggerCnt,proto3" json:"trigger_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TriggerCondition) Reset()         { *m = TriggerCondition{} }
func (m *TriggerCondition) String() string { return proto.CompactTextString(m) }
func (*TriggerCondition) ProtoMessage()    {}
func (*TriggerCondition) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{22}
}
func (m *TriggerCondition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TriggerCondition.Unmarshal(m, b)
}
func (m *TriggerCondition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TriggerCondition.Marshal(b, m, deterministic)
}
func (dst *TriggerCondition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TriggerCondition.Merge(dst, src)
}
func (m *TriggerCondition) XXX_Size() int {
	return xxx_messageInfo_TriggerCondition.Size(m)
}
func (m *TriggerCondition) XXX_DiscardUnknown() {
	xxx_messageInfo_TriggerCondition.DiscardUnknown(m)
}

var xxx_messageInfo_TriggerCondition proto.InternalMessageInfo

func (m *TriggerCondition) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *TriggerCondition) GetTriggerCnt() uint32 {
	if m != nil {
		return m.TriggerCnt
	}
	return 0
}

// 触发条件信息
type TriggerInfo struct {
	CondList             []*TriggerCondition `protobuf:"bytes,1,rep,name=cond_list,json=condList,proto3" json:"cond_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *TriggerInfo) Reset()         { *m = TriggerInfo{} }
func (m *TriggerInfo) String() string { return proto.CompactTextString(m) }
func (*TriggerInfo) ProtoMessage()    {}
func (*TriggerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{23}
}
func (m *TriggerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TriggerInfo.Unmarshal(m, b)
}
func (m *TriggerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TriggerInfo.Marshal(b, m, deterministic)
}
func (dst *TriggerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TriggerInfo.Merge(dst, src)
}
func (m *TriggerInfo) XXX_Size() int {
	return xxx_messageInfo_TriggerInfo.Size(m)
}
func (m *TriggerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TriggerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TriggerInfo proto.InternalMessageInfo

func (m *TriggerInfo) GetCondList() []*TriggerCondition {
	if m != nil {
		return m.CondList
	}
	return nil
}

// 用户受控状态信息
type UserControlInfo struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IsControlled         bool         `protobuf:"varint,2,opt,name=is_controlled,json=isControlled,proto3" json:"is_controlled,omitempty"`
	TriggerInfo          *TriggerInfo `protobuf:"bytes,3,opt,name=trigger_info,json=triggerInfo,proto3" json:"trigger_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UserControlInfo) Reset()         { *m = UserControlInfo{} }
func (m *UserControlInfo) String() string { return proto.CompactTextString(m) }
func (*UserControlInfo) ProtoMessage()    {}
func (*UserControlInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{24}
}
func (m *UserControlInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserControlInfo.Unmarshal(m, b)
}
func (m *UserControlInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserControlInfo.Marshal(b, m, deterministic)
}
func (dst *UserControlInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserControlInfo.Merge(dst, src)
}
func (m *UserControlInfo) XXX_Size() int {
	return xxx_messageInfo_UserControlInfo.Size(m)
}
func (m *UserControlInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserControlInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserControlInfo proto.InternalMessageInfo

func (m *UserControlInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserControlInfo) GetIsControlled() bool {
	if m != nil {
		return m.IsControlled
	}
	return false
}

func (m *UserControlInfo) GetTriggerInfo() *TriggerInfo {
	if m != nil {
		return m.TriggerInfo
	}
	return nil
}

// 获取用户受控信息
type GetUserControlInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserControlInfoReq) Reset()         { *m = GetUserControlInfoReq{} }
func (m *GetUserControlInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserControlInfoReq) ProtoMessage()    {}
func (*GetUserControlInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{25}
}
func (m *GetUserControlInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserControlInfoReq.Unmarshal(m, b)
}
func (m *GetUserControlInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserControlInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserControlInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserControlInfoReq.Merge(dst, src)
}
func (m *GetUserControlInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserControlInfoReq.Size(m)
}
func (m *GetUserControlInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserControlInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserControlInfoReq proto.InternalMessageInfo

func (m *GetUserControlInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserControlInfoResp struct {
	Info                 *UserControlInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserControlInfoResp) Reset()         { *m = GetUserControlInfoResp{} }
func (m *GetUserControlInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserControlInfoResp) ProtoMessage()    {}
func (*GetUserControlInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{26}
}
func (m *GetUserControlInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserControlInfoResp.Unmarshal(m, b)
}
func (m *GetUserControlInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserControlInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserControlInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserControlInfoResp.Merge(dst, src)
}
func (m *GetUserControlInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserControlInfoResp.Size(m)
}
func (m *GetUserControlInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserControlInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserControlInfoResp proto.InternalMessageInfo

func (m *GetUserControlInfoResp) GetInfo() *UserControlInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 批量获取用户受控信息
type BatGetUserControlInfoReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetUserControlInfoReq) Reset()         { *m = BatGetUserControlInfoReq{} }
func (m *BatGetUserControlInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatGetUserControlInfoReq) ProtoMessage()    {}
func (*BatGetUserControlInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{27}
}
func (m *BatGetUserControlInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetUserControlInfoReq.Unmarshal(m, b)
}
func (m *BatGetUserControlInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetUserControlInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatGetUserControlInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetUserControlInfoReq.Merge(dst, src)
}
func (m *BatGetUserControlInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatGetUserControlInfoReq.Size(m)
}
func (m *BatGetUserControlInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetUserControlInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetUserControlInfoReq proto.InternalMessageInfo

func (m *BatGetUserControlInfoReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatGetUserControlInfoResp struct {
	InfoList             []*UserControlInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatGetUserControlInfoResp) Reset()         { *m = BatGetUserControlInfoResp{} }
func (m *BatGetUserControlInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatGetUserControlInfoResp) ProtoMessage()    {}
func (*BatGetUserControlInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{28}
}
func (m *BatGetUserControlInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetUserControlInfoResp.Unmarshal(m, b)
}
func (m *BatGetUserControlInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetUserControlInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatGetUserControlInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetUserControlInfoResp.Merge(dst, src)
}
func (m *BatGetUserControlInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatGetUserControlInfoResp.Size(m)
}
func (m *BatGetUserControlInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetUserControlInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetUserControlInfoResp proto.InternalMessageInfo

func (m *BatGetUserControlInfoResp) GetInfoList() []*UserControlInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 解除受控用户的受控状态
type BatRemoveUserControlledReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatRemoveUserControlledReq) Reset()         { *m = BatRemoveUserControlledReq{} }
func (m *BatRemoveUserControlledReq) String() string { return proto.CompactTextString(m) }
func (*BatRemoveUserControlledReq) ProtoMessage()    {}
func (*BatRemoveUserControlledReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{29}
}
func (m *BatRemoveUserControlledReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatRemoveUserControlledReq.Unmarshal(m, b)
}
func (m *BatRemoveUserControlledReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatRemoveUserControlledReq.Marshal(b, m, deterministic)
}
func (dst *BatRemoveUserControlledReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatRemoveUserControlledReq.Merge(dst, src)
}
func (m *BatRemoveUserControlledReq) XXX_Size() int {
	return xxx_messageInfo_BatRemoveUserControlledReq.Size(m)
}
func (m *BatRemoveUserControlledReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatRemoveUserControlledReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatRemoveUserControlledReq proto.InternalMessageInfo

func (m *BatRemoveUserControlledReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatRemoveUserControlledResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatRemoveUserControlledResp) Reset()         { *m = BatRemoveUserControlledResp{} }
func (m *BatRemoveUserControlledResp) String() string { return proto.CompactTextString(m) }
func (*BatRemoveUserControlledResp) ProtoMessage()    {}
func (*BatRemoveUserControlledResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{30}
}
func (m *BatRemoveUserControlledResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatRemoveUserControlledResp.Unmarshal(m, b)
}
func (m *BatRemoveUserControlledResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatRemoveUserControlledResp.Marshal(b, m, deterministic)
}
func (dst *BatRemoveUserControlledResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatRemoveUserControlledResp.Merge(dst, src)
}
func (m *BatRemoveUserControlledResp) XXX_Size() int {
	return xxx_messageInfo_BatRemoveUserControlledResp.Size(m)
}
func (m *BatRemoveUserControlledResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatRemoveUserControlledResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatRemoveUserControlledResp proto.InternalMessageInfo

// 设置用户受控信息
type SetUserControlInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ControlType          uint32   `protobuf:"varint,2,opt,name=control_type,json=controlType,proto3" json:"control_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserControlInfoReq) Reset()         { *m = SetUserControlInfoReq{} }
func (m *SetUserControlInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetUserControlInfoReq) ProtoMessage()    {}
func (*SetUserControlInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{31}
}
func (m *SetUserControlInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserControlInfoReq.Unmarshal(m, b)
}
func (m *SetUserControlInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserControlInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetUserControlInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserControlInfoReq.Merge(dst, src)
}
func (m *SetUserControlInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetUserControlInfoReq.Size(m)
}
func (m *SetUserControlInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserControlInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserControlInfoReq proto.InternalMessageInfo

func (m *SetUserControlInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserControlInfoReq) GetControlType() uint32 {
	if m != nil {
		return m.ControlType
	}
	return 0
}

type SetUserControlInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserControlInfoResp) Reset()         { *m = SetUserControlInfoResp{} }
func (m *SetUserControlInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetUserControlInfoResp) ProtoMessage()    {}
func (*SetUserControlInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_regulation_config_d5562562b28352be, []int{32}
}
func (m *SetUserControlInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserControlInfoResp.Unmarshal(m, b)
}
func (m *SetUserControlInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserControlInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetUserControlInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserControlInfoResp.Merge(dst, src)
}
func (m *SetUserControlInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetUserControlInfoResp.Size(m)
}
func (m *SetUserControlInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserControlInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserControlInfoResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*SwitchSearchWhiteListReq)(nil), "tt_regulation_config.SwitchSearchWhiteListReq")
	proto.RegisterType((*SwitchSearchWhiteListResp)(nil), "tt_regulation_config.SwitchSearchWhiteListResp")
	proto.RegisterType((*GetSearchWhiteListStatusReq)(nil), "tt_regulation_config.GetSearchWhiteListStatusReq")
	proto.RegisterType((*GetSearchWhiteListStatusResp)(nil), "tt_regulation_config.GetSearchWhiteListStatusResp")
	proto.RegisterType((*BatchAddSearchWhiteListReq)(nil), "tt_regulation_config.BatchAddSearchWhiteListReq")
	proto.RegisterType((*BatchAddSearchWhiteListResp)(nil), "tt_regulation_config.BatchAddSearchWhiteListResp")
	proto.RegisterType((*SearchWhiteListItem)(nil), "tt_regulation_config.SearchWhiteListItem")
	proto.RegisterType((*SearchWhiteListFailResult)(nil), "tt_regulation_config.SearchWhiteListFailResult")
	proto.RegisterType((*BatchDelSearchWhiteListReq)(nil), "tt_regulation_config.BatchDelSearchWhiteListReq")
	proto.RegisterType((*BatchDelSearchWhiteListResp)(nil), "tt_regulation_config.BatchDelSearchWhiteListResp")
	proto.RegisterType((*GetSearchWhiteListReq)(nil), "tt_regulation_config.GetSearchWhiteListReq")
	proto.RegisterType((*GetSearchWhiteListResp)(nil), "tt_regulation_config.GetSearchWhiteListResp")
	proto.RegisterType((*ExportHotWordReq)(nil), "tt_regulation_config.ExportHotWordReq")
	proto.RegisterType((*ExportHotWordResp)(nil), "tt_regulation_config.ExportHotWordResp")
	proto.RegisterType((*AddSearchCountReq)(nil), "tt_regulation_config.AddSearchCountReq")
	proto.RegisterType((*AddSearchCountResp)(nil), "tt_regulation_config.AddSearchCountResp")
	proto.RegisterType((*GetSearchCountReq)(nil), "tt_regulation_config.GetSearchCountReq")
	proto.RegisterType((*GetSearchCountResp)(nil), "tt_regulation_config.GetSearchCountResp")
	proto.RegisterType((*IsWordInWhiteListReq)(nil), "tt_regulation_config.IsWordInWhiteListReq")
	proto.RegisterType((*IsWordInWhiteListResp)(nil), "tt_regulation_config.IsWordInWhiteListResp")
	proto.RegisterType((*AuthSearchEventReq)(nil), "tt_regulation_config.AuthSearchEventReq")
	proto.RegisterType((*AuthSearchEventResp)(nil), "tt_regulation_config.AuthSearchEventResp")
	proto.RegisterType((*TriggerCondition)(nil), "tt_regulation_config.TriggerCondition")
	proto.RegisterType((*TriggerInfo)(nil), "tt_regulation_config.TriggerInfo")
	proto.RegisterType((*UserControlInfo)(nil), "tt_regulation_config.UserControlInfo")
	proto.RegisterType((*GetUserControlInfoReq)(nil), "tt_regulation_config.GetUserControlInfoReq")
	proto.RegisterType((*GetUserControlInfoResp)(nil), "tt_regulation_config.GetUserControlInfoResp")
	proto.RegisterType((*BatGetUserControlInfoReq)(nil), "tt_regulation_config.BatGetUserControlInfoReq")
	proto.RegisterType((*BatGetUserControlInfoResp)(nil), "tt_regulation_config.BatGetUserControlInfoResp")
	proto.RegisterType((*BatRemoveUserControlledReq)(nil), "tt_regulation_config.BatRemoveUserControlledReq")
	proto.RegisterType((*BatRemoveUserControlledResp)(nil), "tt_regulation_config.BatRemoveUserControlledResp")
	proto.RegisterType((*SetUserControlInfoReq)(nil), "tt_regulation_config.SetUserControlInfoReq")
	proto.RegisterType((*SetUserControlInfoResp)(nil), "tt_regulation_config.SetUserControlInfoResp")
	proto.RegisterEnum("tt_regulation_config.SearchType", SearchType_name, SearchType_value)
	proto.RegisterEnum("tt_regulation_config.ControlType", ControlType_name, ControlType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// TtRegulationConfigClient is the client API for TtRegulationConfig service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TtRegulationConfigClient interface {
	// 搜索白名单后台相关
	SwitchSearchWhiteList(ctx context.Context, in *SwitchSearchWhiteListReq, opts ...grpc.CallOption) (*SwitchSearchWhiteListResp, error)
	GetSearchWhiteListStatus(ctx context.Context, in *GetSearchWhiteListStatusReq, opts ...grpc.CallOption) (*GetSearchWhiteListStatusResp, error)
	BatchAddSearchWhiteList(ctx context.Context, in *BatchAddSearchWhiteListReq, opts ...grpc.CallOption) (*BatchAddSearchWhiteListResp, error)
	BatchDelSearchWhiteList(ctx context.Context, in *BatchDelSearchWhiteListReq, opts ...grpc.CallOption) (*BatchDelSearchWhiteListResp, error)
	GetSearchWhiteList(ctx context.Context, in *GetSearchWhiteListReq, opts ...grpc.CallOption) (*GetSearchWhiteListResp, error)
	ExportHotWord(ctx context.Context, in *ExportHotWordReq, opts ...grpc.CallOption) (*ExportHotWordResp, error)
	AddSearchCount(ctx context.Context, in *AddSearchCountReq, opts ...grpc.CallOption) (*AddSearchCountResp, error)
	GetSearchCount(ctx context.Context, in *GetSearchCountReq, opts ...grpc.CallOption) (*GetSearchCountResp, error)
	IsWordInWhiteList(ctx context.Context, in *IsWordInWhiteListReq, opts ...grpc.CallOption) (*IsWordInWhiteListResp, error)
	// 给搜索调用的接口，一次性完成增加搜索次数、返回搜索次数、返回白名单情况的功能
	AuthSearchEvent(ctx context.Context, in *AuthSearchEventReq, opts ...grpc.CallOption) (*AuthSearchEventResp, error)
	// 获取用户受控信息
	GetUserControlInfo(ctx context.Context, in *GetUserControlInfoReq, opts ...grpc.CallOption) (*GetUserControlInfoResp, error)
	// 解除受控用户的受控状态
	BatRemoveUserControlled(ctx context.Context, in *BatRemoveUserControlledReq, opts ...grpc.CallOption) (*BatRemoveUserControlledResp, error)
	// 批量获取用户受控信息
	BatGetUserControlInfo(ctx context.Context, in *BatGetUserControlInfoReq, opts ...grpc.CallOption) (*BatGetUserControlInfoResp, error)
	// 设置用户受控信息
	SetUserControlInfo(ctx context.Context, in *SetUserControlInfoReq, opts ...grpc.CallOption) (*SetUserControlInfoResp, error)
}

type ttRegulationConfigClient struct {
	cc *grpc.ClientConn
}

func NewTtRegulationConfigClient(cc *grpc.ClientConn) TtRegulationConfigClient {
	return &ttRegulationConfigClient{cc}
}

func (c *ttRegulationConfigClient) SwitchSearchWhiteList(ctx context.Context, in *SwitchSearchWhiteListReq, opts ...grpc.CallOption) (*SwitchSearchWhiteListResp, error) {
	out := new(SwitchSearchWhiteListResp)
	err := c.cc.Invoke(ctx, "/tt_regulation_config.TtRegulationConfig/SwitchSearchWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ttRegulationConfigClient) GetSearchWhiteListStatus(ctx context.Context, in *GetSearchWhiteListStatusReq, opts ...grpc.CallOption) (*GetSearchWhiteListStatusResp, error) {
	out := new(GetSearchWhiteListStatusResp)
	err := c.cc.Invoke(ctx, "/tt_regulation_config.TtRegulationConfig/GetSearchWhiteListStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ttRegulationConfigClient) BatchAddSearchWhiteList(ctx context.Context, in *BatchAddSearchWhiteListReq, opts ...grpc.CallOption) (*BatchAddSearchWhiteListResp, error) {
	out := new(BatchAddSearchWhiteListResp)
	err := c.cc.Invoke(ctx, "/tt_regulation_config.TtRegulationConfig/BatchAddSearchWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ttRegulationConfigClient) BatchDelSearchWhiteList(ctx context.Context, in *BatchDelSearchWhiteListReq, opts ...grpc.CallOption) (*BatchDelSearchWhiteListResp, error) {
	out := new(BatchDelSearchWhiteListResp)
	err := c.cc.Invoke(ctx, "/tt_regulation_config.TtRegulationConfig/BatchDelSearchWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ttRegulationConfigClient) GetSearchWhiteList(ctx context.Context, in *GetSearchWhiteListReq, opts ...grpc.CallOption) (*GetSearchWhiteListResp, error) {
	out := new(GetSearchWhiteListResp)
	err := c.cc.Invoke(ctx, "/tt_regulation_config.TtRegulationConfig/GetSearchWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ttRegulationConfigClient) ExportHotWord(ctx context.Context, in *ExportHotWordReq, opts ...grpc.CallOption) (*ExportHotWordResp, error) {
	out := new(ExportHotWordResp)
	err := c.cc.Invoke(ctx, "/tt_regulation_config.TtRegulationConfig/ExportHotWord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ttRegulationConfigClient) AddSearchCount(ctx context.Context, in *AddSearchCountReq, opts ...grpc.CallOption) (*AddSearchCountResp, error) {
	out := new(AddSearchCountResp)
	err := c.cc.Invoke(ctx, "/tt_regulation_config.TtRegulationConfig/AddSearchCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ttRegulationConfigClient) GetSearchCount(ctx context.Context, in *GetSearchCountReq, opts ...grpc.CallOption) (*GetSearchCountResp, error) {
	out := new(GetSearchCountResp)
	err := c.cc.Invoke(ctx, "/tt_regulation_config.TtRegulationConfig/GetSearchCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ttRegulationConfigClient) IsWordInWhiteList(ctx context.Context, in *IsWordInWhiteListReq, opts ...grpc.CallOption) (*IsWordInWhiteListResp, error) {
	out := new(IsWordInWhiteListResp)
	err := c.cc.Invoke(ctx, "/tt_regulation_config.TtRegulationConfig/IsWordInWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ttRegulationConfigClient) AuthSearchEvent(ctx context.Context, in *AuthSearchEventReq, opts ...grpc.CallOption) (*AuthSearchEventResp, error) {
	out := new(AuthSearchEventResp)
	err := c.cc.Invoke(ctx, "/tt_regulation_config.TtRegulationConfig/AuthSearchEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ttRegulationConfigClient) GetUserControlInfo(ctx context.Context, in *GetUserControlInfoReq, opts ...grpc.CallOption) (*GetUserControlInfoResp, error) {
	out := new(GetUserControlInfoResp)
	err := c.cc.Invoke(ctx, "/tt_regulation_config.TtRegulationConfig/GetUserControlInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ttRegulationConfigClient) BatRemoveUserControlled(ctx context.Context, in *BatRemoveUserControlledReq, opts ...grpc.CallOption) (*BatRemoveUserControlledResp, error) {
	out := new(BatRemoveUserControlledResp)
	err := c.cc.Invoke(ctx, "/tt_regulation_config.TtRegulationConfig/BatRemoveUserControlled", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ttRegulationConfigClient) BatGetUserControlInfo(ctx context.Context, in *BatGetUserControlInfoReq, opts ...grpc.CallOption) (*BatGetUserControlInfoResp, error) {
	out := new(BatGetUserControlInfoResp)
	err := c.cc.Invoke(ctx, "/tt_regulation_config.TtRegulationConfig/BatGetUserControlInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ttRegulationConfigClient) SetUserControlInfo(ctx context.Context, in *SetUserControlInfoReq, opts ...grpc.CallOption) (*SetUserControlInfoResp, error) {
	out := new(SetUserControlInfoResp)
	err := c.cc.Invoke(ctx, "/tt_regulation_config.TtRegulationConfig/SetUserControlInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TtRegulationConfigServer is the server API for TtRegulationConfig service.
type TtRegulationConfigServer interface {
	// 搜索白名单后台相关
	SwitchSearchWhiteList(context.Context, *SwitchSearchWhiteListReq) (*SwitchSearchWhiteListResp, error)
	GetSearchWhiteListStatus(context.Context, *GetSearchWhiteListStatusReq) (*GetSearchWhiteListStatusResp, error)
	BatchAddSearchWhiteList(context.Context, *BatchAddSearchWhiteListReq) (*BatchAddSearchWhiteListResp, error)
	BatchDelSearchWhiteList(context.Context, *BatchDelSearchWhiteListReq) (*BatchDelSearchWhiteListResp, error)
	GetSearchWhiteList(context.Context, *GetSearchWhiteListReq) (*GetSearchWhiteListResp, error)
	ExportHotWord(context.Context, *ExportHotWordReq) (*ExportHotWordResp, error)
	AddSearchCount(context.Context, *AddSearchCountReq) (*AddSearchCountResp, error)
	GetSearchCount(context.Context, *GetSearchCountReq) (*GetSearchCountResp, error)
	IsWordInWhiteList(context.Context, *IsWordInWhiteListReq) (*IsWordInWhiteListResp, error)
	// 给搜索调用的接口，一次性完成增加搜索次数、返回搜索次数、返回白名单情况的功能
	AuthSearchEvent(context.Context, *AuthSearchEventReq) (*AuthSearchEventResp, error)
	// 获取用户受控信息
	GetUserControlInfo(context.Context, *GetUserControlInfoReq) (*GetUserControlInfoResp, error)
	// 解除受控用户的受控状态
	BatRemoveUserControlled(context.Context, *BatRemoveUserControlledReq) (*BatRemoveUserControlledResp, error)
	// 批量获取用户受控信息
	BatGetUserControlInfo(context.Context, *BatGetUserControlInfoReq) (*BatGetUserControlInfoResp, error)
	// 设置用户受控信息
	SetUserControlInfo(context.Context, *SetUserControlInfoReq) (*SetUserControlInfoResp, error)
}

func RegisterTtRegulationConfigServer(s *grpc.Server, srv TtRegulationConfigServer) {
	s.RegisterService(&_TtRegulationConfig_serviceDesc, srv)
}

func _TtRegulationConfig_SwitchSearchWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchSearchWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TtRegulationConfigServer).SwitchSearchWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_regulation_config.TtRegulationConfig/SwitchSearchWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TtRegulationConfigServer).SwitchSearchWhiteList(ctx, req.(*SwitchSearchWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TtRegulationConfig_GetSearchWhiteListStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSearchWhiteListStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TtRegulationConfigServer).GetSearchWhiteListStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_regulation_config.TtRegulationConfig/GetSearchWhiteListStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TtRegulationConfigServer).GetSearchWhiteListStatus(ctx, req.(*GetSearchWhiteListStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TtRegulationConfig_BatchAddSearchWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddSearchWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TtRegulationConfigServer).BatchAddSearchWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_regulation_config.TtRegulationConfig/BatchAddSearchWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TtRegulationConfigServer).BatchAddSearchWhiteList(ctx, req.(*BatchAddSearchWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TtRegulationConfig_BatchDelSearchWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelSearchWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TtRegulationConfigServer).BatchDelSearchWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_regulation_config.TtRegulationConfig/BatchDelSearchWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TtRegulationConfigServer).BatchDelSearchWhiteList(ctx, req.(*BatchDelSearchWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TtRegulationConfig_GetSearchWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSearchWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TtRegulationConfigServer).GetSearchWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_regulation_config.TtRegulationConfig/GetSearchWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TtRegulationConfigServer).GetSearchWhiteList(ctx, req.(*GetSearchWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TtRegulationConfig_ExportHotWord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportHotWordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TtRegulationConfigServer).ExportHotWord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_regulation_config.TtRegulationConfig/ExportHotWord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TtRegulationConfigServer).ExportHotWord(ctx, req.(*ExportHotWordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TtRegulationConfig_AddSearchCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSearchCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TtRegulationConfigServer).AddSearchCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_regulation_config.TtRegulationConfig/AddSearchCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TtRegulationConfigServer).AddSearchCount(ctx, req.(*AddSearchCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TtRegulationConfig_GetSearchCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSearchCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TtRegulationConfigServer).GetSearchCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_regulation_config.TtRegulationConfig/GetSearchCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TtRegulationConfigServer).GetSearchCount(ctx, req.(*GetSearchCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TtRegulationConfig_IsWordInWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsWordInWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TtRegulationConfigServer).IsWordInWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_regulation_config.TtRegulationConfig/IsWordInWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TtRegulationConfigServer).IsWordInWhiteList(ctx, req.(*IsWordInWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TtRegulationConfig_AuthSearchEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthSearchEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TtRegulationConfigServer).AuthSearchEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_regulation_config.TtRegulationConfig/AuthSearchEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TtRegulationConfigServer).AuthSearchEvent(ctx, req.(*AuthSearchEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TtRegulationConfig_GetUserControlInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserControlInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TtRegulationConfigServer).GetUserControlInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_regulation_config.TtRegulationConfig/GetUserControlInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TtRegulationConfigServer).GetUserControlInfo(ctx, req.(*GetUserControlInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TtRegulationConfig_BatRemoveUserControlled_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatRemoveUserControlledReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TtRegulationConfigServer).BatRemoveUserControlled(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_regulation_config.TtRegulationConfig/BatRemoveUserControlled",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TtRegulationConfigServer).BatRemoveUserControlled(ctx, req.(*BatRemoveUserControlledReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TtRegulationConfig_BatGetUserControlInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetUserControlInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TtRegulationConfigServer).BatGetUserControlInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_regulation_config.TtRegulationConfig/BatGetUserControlInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TtRegulationConfigServer).BatGetUserControlInfo(ctx, req.(*BatGetUserControlInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TtRegulationConfig_SetUserControlInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserControlInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TtRegulationConfigServer).SetUserControlInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_regulation_config.TtRegulationConfig/SetUserControlInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TtRegulationConfigServer).SetUserControlInfo(ctx, req.(*SetUserControlInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _TtRegulationConfig_serviceDesc = grpc.ServiceDesc{
	ServiceName: "tt_regulation_config.TtRegulationConfig",
	HandlerType: (*TtRegulationConfigServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SwitchSearchWhiteList",
			Handler:    _TtRegulationConfig_SwitchSearchWhiteList_Handler,
		},
		{
			MethodName: "GetSearchWhiteListStatus",
			Handler:    _TtRegulationConfig_GetSearchWhiteListStatus_Handler,
		},
		{
			MethodName: "BatchAddSearchWhiteList",
			Handler:    _TtRegulationConfig_BatchAddSearchWhiteList_Handler,
		},
		{
			MethodName: "BatchDelSearchWhiteList",
			Handler:    _TtRegulationConfig_BatchDelSearchWhiteList_Handler,
		},
		{
			MethodName: "GetSearchWhiteList",
			Handler:    _TtRegulationConfig_GetSearchWhiteList_Handler,
		},
		{
			MethodName: "ExportHotWord",
			Handler:    _TtRegulationConfig_ExportHotWord_Handler,
		},
		{
			MethodName: "AddSearchCount",
			Handler:    _TtRegulationConfig_AddSearchCount_Handler,
		},
		{
			MethodName: "GetSearchCount",
			Handler:    _TtRegulationConfig_GetSearchCount_Handler,
		},
		{
			MethodName: "IsWordInWhiteList",
			Handler:    _TtRegulationConfig_IsWordInWhiteList_Handler,
		},
		{
			MethodName: "AuthSearchEvent",
			Handler:    _TtRegulationConfig_AuthSearchEvent_Handler,
		},
		{
			MethodName: "GetUserControlInfo",
			Handler:    _TtRegulationConfig_GetUserControlInfo_Handler,
		},
		{
			MethodName: "BatRemoveUserControlled",
			Handler:    _TtRegulationConfig_BatRemoveUserControlled_Handler,
		},
		{
			MethodName: "BatGetUserControlInfo",
			Handler:    _TtRegulationConfig_BatGetUserControlInfo_Handler,
		},
		{
			MethodName: "SetUserControlInfo",
			Handler:    _TtRegulationConfig_SetUserControlInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt-regulation-config/tt-regulation-config.proto",
}

func init() {
	proto.RegisterFile("tt-regulation-config/tt-regulation-config.proto", fileDescriptor_tt_regulation_config_d5562562b28352be)
}

var fileDescriptor_tt_regulation_config_d5562562b28352be = []byte{
	// 1350 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x58, 0xdd, 0x72, 0xd3, 0xc6,
	0x17, 0x8f, 0x12, 0x43, 0xec, 0x63, 0xf2, 0x8f, 0xb3, 0x24, 0xe0, 0x38, 0x30, 0x04, 0x31, 0xff,
	0x12, 0x42, 0x49, 0x20, 0x1d, 0xfa, 0x31, 0xf4, 0x26, 0x71, 0x9c, 0xe0, 0x62, 0x9c, 0x8e, 0x64,
	0x97, 0x69, 0x7b, 0xa1, 0x11, 0xd2, 0xc6, 0xd9, 0xa9, 0x2c, 0x09, 0xed, 0x3a, 0x81, 0x5e, 0xd1,
	0xe9, 0x7d, 0x67, 0x7a, 0xd1, 0x67, 0xe9, 0x53, 0xf4, 0x9d, 0x3a, 0x7b, 0xe4, 0x4f, 0x69, 0xe5,
	0x3a, 0x03, 0xed, 0x9d, 0xf6, 0x7c, 0x9f, 0xdf, 0x9e, 0xb3, 0xe7, 0xd8, 0xb0, 0x2b, 0xc4, 0xa3,
	0x88, 0x76, 0x7a, 0x9e, 0x2d, 0x58, 0xe0, 0x3f, 0x72, 0x02, 0xff, 0x94, 0x75, 0x94, 0xc4, 0x9d,
	0x30, 0x0a, 0x44, 0x40, 0x56, 0x85, 0xb0, 0x46, 0x3c, 0x2b, 0xe6, 0xe9, 0x47, 0x50, 0x36, 0x2f,
	0x98, 0x70, 0xce, 0x4c, 0x6a, 0x47, 0xce, 0xd9, 0xab, 0x33, 0x26, 0x68, 0x83, 0x71, 0x61, 0xd0,
	0x37, 0x64, 0x1b, 0x56, 0x2e, 0xe4, 0xd9, 0xf2, 0x18, 0x17, 0x16, 0x17, 0xb6, 0xe8, 0xf1, 0xb2,
	0xb6, 0xa9, 0x6d, 0xe5, 0x8d, 0xe5, 0x8b, 0x81, 0xa0, 0x89, 0x64, 0x7d, 0x03, 0xd6, 0x33, 0xec,
	0xf0, 0x50, 0xbf, 0x0d, 0x1b, 0xc7, 0x54, 0x24, 0x38, 0xb1, 0xa2, 0x41, 0xdf, 0xe8, 0xdf, 0xc0,
	0xad, 0x6c, 0x36, 0x0f, 0x2f, 0x15, 0x87, 0x0b, 0x95, 0x03, 0x5b, 0x38, 0x67, 0xfb, 0xae, 0xab,
	0xc8, 0xe8, 0x08, 0x0a, 0x4c, 0xd0, 0x2e, 0x1a, 0x2a, 0x6b, 0x9b, 0x0b, 0x5b, 0xc5, 0xbd, 0x07,
	0x3b, 0x2a, 0x5c, 0x76, 0x12, 0xca, 0x75, 0x41, 0xbb, 0x46, 0x5e, 0xea, 0xca, 0x93, 0xfe, 0x13,
	0x6c, 0x64, 0x7a, 0xe1, 0x21, 0x69, 0x40, 0xe1, 0xd4, 0x66, 0xde, 0xb8, 0x9b, 0xdd, 0x99, 0xdc,
	0x1c, 0xd9, 0xcc, 0x33, 0x28, 0xef, 0x79, 0xc2, 0xc8, 0x4b, 0x0b, 0xe8, 0xec, 0x2f, 0x0d, 0xae,
	0x2b, 0xc2, 0x21, 0x37, 0x61, 0x11, 0x93, 0x61, 0x2e, 0x82, 0xb1, 0x64, 0x5c, 0x95, 0xc7, 0xba,
	0x4b, 0xee, 0xc2, 0x35, 0x64, 0x38, 0x81, 0x2f, 0xa8, 0x2f, 0xca, 0xf3, 0x9b, 0xda, 0x56, 0xc1,
	0x28, 0x4a, 0x5a, 0x35, 0x26, 0x91, 0x3b, 0x50, 0x0c, 0x42, 0x1a, 0xd9, 0x82, 0x46, 0x52, 0x7f,
	0x01, 0x25, 0x60, 0x40, 0xaa, 0xbb, 0x52, 0xc0, 0x89, 0xa8, 0x2d, 0xa8, 0x25, 0x58, 0x97, 0x96,
	0x73, 0xe8, 0x00, 0x62, 0x52, 0x8b, 0x75, 0x29, 0xb9, 0x0d, 0xf0, 0x9a, 0x76, 0x98, 0x1f, 0xf3,
	0xaf, 0x20, 0xbf, 0x80, 0x14, 0x64, 0xaf, 0x43, 0x9e, 0xfa, 0x6e, 0xcc, 0xbc, 0x8a, 0xcc, 0x45,
	0xea, 0xbb, 0x92, 0xa5, 0xbf, 0x85, 0xf5, 0xcc, 0xb4, 0x3f, 0x34, 0x29, 0x84, 0x3d, 0xa2, 0x36,
	0x0f, 0xfc, 0x41, 0x52, 0xa7, 0x68, 0x5c, 0x52, 0x86, 0xc5, 0x71, 0x48, 0xbd, 0xff, 0xa0, 0x38,
	0x54, 0x5e, 0x3e, 0x7a, 0x71, 0xfc, 0xa1, 0xc1, 0x5a, 0xba, 0x79, 0x64, 0x3a, 0x65, 0x58, 0x1c,
	0x60, 0xa5, 0x21, 0x12, 0x83, 0x63, 0xe2, 0xea, 0xe6, 0xa7, 0x5d, 0xdd, 0xc2, 0xc4, 0xd5, 0x11,
	0x02, 0xb9, 0xd0, 0xee, 0x0c, 0xca, 0x01, 0xbf, 0xc9, 0x2a, 0x5c, 0x71, 0x82, 0x9e, 0x2f, 0xfa,
	0x35, 0x10, 0x1f, 0xf4, 0x5f, 0x34, 0xb8, 0xa1, 0x8a, 0x8b, 0x87, 0x1f, 0x0b, 0x67, 0x79, 0xdd,
	0x22, 0x10, 0xb6, 0x67, 0xc5, 0xee, 0xe3, 0x3c, 0x00, 0x49, 0x55, 0x8c, 0xa1, 0x01, 0xa5, 0xda,
	0xdb, 0x30, 0x88, 0xc4, 0xf3, 0x40, 0xbc, 0x0a, 0x22, 0x57, 0xa2, 0x32, 0x99, 0xbb, 0x36, 0x2d,
	0xf7, 0xf9, 0xc9, 0xb2, 0x7d, 0x0c, 0x2b, 0x09, 0x6b, 0x3c, 0x24, 0x1b, 0x50, 0xb8, 0x08, 0x22,
	0x77, 0x94, 0x4b, 0xc1, 0xc8, 0x4b, 0x02, 0xde, 0xcd, 0x11, 0xac, 0x0c, 0x1f, 0x08, 0x8c, 0x48,
	0x06, 0x50, 0x82, 0x85, 0xde, 0xb0, 0xb8, 0xe5, 0xa7, 0xcc, 0x83, 0xa3, 0x8c, 0x25, 0xde, 0x85,
	0x03, 0xb7, 0x10, 0x93, 0x5a, 0xef, 0x42, 0xaa, 0xaf, 0x02, 0x49, 0xda, 0xe1, 0xa1, 0xb4, 0x3e,
	0x04, 0xf8, 0x43, 0xac, 0x7b, 0x40, 0x92, 0x76, 0x78, 0x38, 0xba, 0x55, 0x6d, 0xec, 0x56, 0x25,
	0x7a, 0x8e, 0xed, 0x5b, 0xb1, 0x36, 0xda, 0xca, 0x1b, 0x05, 0xc7, 0xf6, 0x63, 0x6d, 0xd9, 0xa3,
	0x11, 0x16, 0xa8, 0xe5, 0xb1, 0x2e, 0x13, 0xfd, 0xea, 0x29, 0xc6, 0xb4, 0x86, 0x24, 0xe9, 0x5f,
	0xc3, 0x6a, 0x9d, 0x4b, 0xf8, 0xea, 0xfe, 0x44, 0xb5, 0xa6, 0x03, 0x27, 0x90, 0x93, 0x48, 0xf6,
	0x1b, 0x1d, 0xbf, 0xf5, 0x67, 0xb0, 0xa6, 0xd0, 0xe6, 0x21, 0xd1, 0x61, 0x89, 0xf9, 0xd6, 0x68,
	0x4a, 0xf4, 0xc7, 0x43, 0x91, 0x8d, 0xe4, 0xf4, 0x1f, 0x81, 0xec, 0xf7, 0x44, 0x7f, 0x40, 0xd5,
	0xce, 0xa9, 0x3f, 0xbb, 0xe3, 0x24, 0x8a, 0x0b, 0x29, 0x14, 0x7f, 0xd7, 0xe0, 0x7a, 0xca, 0xfa,
	0xbf, 0x87, 0x63, 0x3a, 0xe1, 0x5c, 0x3a, 0xe1, 0x63, 0x28, 0xb5, 0x22, 0xd6, 0xe9, 0xd0, 0xa8,
	0x1a, 0xf8, 0x2e, 0x93, 0x9d, 0x25, 0x93, 0xc3, 0x0c, 0xe2, 0x70, 0xf0, 0x1b, 0x1b, 0x29, 0x96,
	0xb3, 0x9c, 0xb1, 0x46, 0xea, 0xab, 0xfa, 0x42, 0x37, 0xa0, 0xd8, 0x37, 0x54, 0xf7, 0x4f, 0x03,
	0x52, 0x85, 0x82, 0x13, 0xf8, 0xee, 0x78, 0x03, 0x7f, 0xa2, 0x6e, 0xe0, 0xa4, 0x7b, 0x23, 0x2f,
	0x15, 0x31, 0xb8, 0xdf, 0x34, 0x58, 0x6e, 0x73, 0xe4, 0x89, 0x28, 0xf0, 0xd0, 0x70, 0xfa, 0x2e,
	0xee, 0xc1, 0x12, 0xe3, 0xf8, 0xe6, 0x47, 0x81, 0xe7, 0x51, 0xb7, 0x8f, 0xd5, 0x35, 0xc6, 0xab,
	0x43, 0x1a, 0x39, 0x84, 0x6b, 0x83, 0xf8, 0x99, 0x7f, 0x1a, 0x20, 0x5c, 0xc5, 0xbd, 0xbb, 0x53,
	0x43, 0x92, 0xfe, 0x8c, 0x41, 0xda, 0xf2, 0xa0, 0x3f, 0xc0, 0x87, 0x34, 0x11, 0x92, 0xb2, 0x42,
	0x74, 0x13, 0xdf, 0xb6, 0x94, 0x28, 0x0f, 0xc9, 0x57, 0x90, 0xc3, 0x10, 0x34, 0x0c, 0xe1, 0xff,
	0xea, 0x10, 0x92, 0x8a, 0xa8, 0xa2, 0x3f, 0x85, 0xf2, 0x81, 0x2d, 0xd4, 0x21, 0xac, 0x43, 0xbe,
	0xc7, 0xc6, 0x00, 0x5f, 0x32, 0x16, 0x7b, 0x2c, 0xc6, 0xd1, 0x82, 0xf5, 0x0c, 0x35, 0x1e, 0x92,
	0x03, 0x28, 0x48, 0xdb, 0xe3, 0x37, 0x35, 0x63, 0x4c, 0x79, 0xa9, 0x87, 0x0e, 0xbe, 0xc0, 0xa1,
	0x69, 0xd0, 0x6e, 0x70, 0x4e, 0xc7, 0xa4, 0x3c, 0xea, 0xfe, 0x43, 0x64, 0xb7, 0x71, 0x0e, 0xaa,
	0x15, 0x79, 0xa8, 0x37, 0x60, 0xcd, 0x9c, 0x0d, 0x6f, 0xd9, 0x0f, 0xfd, 0x12, 0x18, 0x7f, 0xc4,
	0x8a, 0x7d, 0x1a, 0xf6, 0x5f, 0x19, 0x6e, 0x98, 0x4a, 0x0c, 0xb6, 0x9f, 0x01, 0x98, 0xc3, 0x3e,
	0x25, 0x45, 0x58, 0x6c, 0x37, 0x5f, 0x34, 0x4f, 0x5e, 0x35, 0x4b, 0x73, 0x24, 0x0f, 0xb9, 0xb6,
	0x59, 0x33, 0x4a, 0x9a, 0x24, 0x57, 0x9f, 0xef, 0x37, 0x9b, 0xb5, 0x46, 0x69, 0x9e, 0x14, 0xe0,
	0xca, 0x71, 0xbb, 0xde, 0x38, 0x2c, 0x2d, 0x6c, 0xff, 0xa9, 0x41, 0xb1, 0x3a, 0x72, 0x43, 0xae,
	0xc3, 0x72, 0xad, 0xd9, 0x7e, 0x69, 0x35, 0x4f, 0xac, 0xea, 0x49, 0xb3, 0x65, 0x9c, 0x34, 0x4a,
	0x73, 0xe4, 0x0e, 0x6c, 0x20, 0xb1, 0x4f, 0xb1, 0x0e, 0xbe, 0xb7, 0x8c, 0xda, 0x71, 0xdd, 0x6c,
	0xd5, 0x0c, 0xab, 0x65, 0x96, 0x34, 0x72, 0x1f, 0xee, 0x25, 0x05, 0xda, 0xcd, 0xb6, 0xb9, 0xdf,
	0xb0, 0x0e, 0x6b, 0xdf, 0xd5, 0xab, 0x35, 0xab, 0x71, 0x72, 0x5c, 0x6f, 0x96, 0xe6, 0xc9, 0x26,
	0xdc, 0x4a, 0x0b, 0x1e, 0xd4, 0x9b, 0x87, 0xd6, 0xb7, 0xcf, 0x4f, 0x9a, 0xb5, 0x52, 0x4e, 0x65,
	0xea, 0x78, 0xff, 0x65, 0xcd, 0x3c, 0x7c, 0x61, 0x1d, 0xd5, 0x0d, 0xb3, 0xd5, 0x37, 0x95, 0xdf,
	0x7b, 0xbf, 0x04, 0xa4, 0x25, 0x8c, 0xe1, 0x45, 0x57, 0xf1, 0x9e, 0xc9, 0xcf, 0xb0, 0xa6, 0xdc,
	0xd3, 0xc9, 0x4e, 0xc6, 0x08, 0xce, 0xf8, 0x71, 0x50, 0xd9, 0xbd, 0x94, 0x3c, 0x0f, 0xf5, 0x39,
	0xf2, 0xab, 0x06, 0xe5, 0xac, 0x45, 0x9f, 0x3c, 0x51, 0xdb, 0x9b, 0xf2, 0xbb, 0xa1, 0xb2, 0x77,
	0x59, 0x15, 0x8c, 0xe2, 0xbd, 0x06, 0x37, 0x33, 0x96, 0x77, 0xf2, 0x58, 0x6d, 0x31, 0xfb, 0x17,
	0x45, 0xe5, 0xc9, 0x25, 0x35, 0x26, 0x43, 0x48, 0xaf, 0x88, 0x53, 0x43, 0x50, 0xee, 0xad, 0x53,
	0x43, 0x50, 0xef, 0xa0, 0xfa, 0x1c, 0x79, 0x33, 0x36, 0xf5, 0x47, 0xce, 0x1f, 0xce, 0x8a, 0xa8,
	0xf4, 0xfb, 0xe9, 0xec, 0xc2, 0xe8, 0xf2, 0x35, 0x2c, 0x4d, 0x2c, 0x50, 0x24, 0x63, 0x68, 0x24,
	0x77, 0xb6, 0xca, 0xfd, 0x99, 0xe4, 0xd0, 0x07, 0x85, 0xff, 0x4d, 0xae, 0x4a, 0x24, 0x43, 0x39,
	0xb5, 0x98, 0x55, 0xb6, 0x66, 0x13, 0x1c, 0xb8, 0x99, 0xdc, 0x99, 0xb2, 0xdc, 0xa4, 0x36, 0xb4,
	0x2c, 0x37, 0xe9, 0x15, 0x4c, 0x9f, 0x23, 0x3e, 0xac, 0xa4, 0xd6, 0x1d, 0xb2, 0xad, 0x36, 0xa0,
	0xda, 0xaa, 0x2a, 0x0f, 0x67, 0x96, 0x45, 0x7f, 0x67, 0xb0, 0x9c, 0xd8, 0x61, 0x48, 0x16, 0x2a,
	0xa9, 0x45, 0xaa, 0xf2, 0x60, 0x46, 0xc9, 0xb1, 0xf2, 0x4b, 0xce, 0xff, 0xec, 0xf2, 0x4b, 0x8f,
	0x89, 0x29, 0xe5, 0xa7, 0x98, 0x02, 0xa3, 0xa6, 0x53, 0xcd, 0xa3, 0x29, 0x4d, 0x97, 0x31, 0xf7,
	0xa6, 0x34, 0x5d, 0xe6, 0xc0, 0x9b, 0x93, 0x8f, 0xaf, 0x72, 0x56, 0x67, 0x3d, 0xbe, 0x59, 0xfb,
	0x40, 0xd6, 0xe3, 0x9b, 0xb9, 0x08, 0xc4, 0x88, 0x9b, 0x33, 0x23, 0x6e, 0x5e, 0x06, 0x71, 0x33,
	0xc3, 0xe5, 0xc1, 0x97, 0x3f, 0x7c, 0xde, 0x09, 0x3c, 0xdb, 0xef, 0xec, 0x3c, 0xdd, 0x13, 0x62,
	0xc7, 0x09, 0xba, 0xbb, 0xf8, 0x57, 0x94, 0x13, 0x78, 0xbb, 0x9c, 0x46, 0xe7, 0xcc, 0xa1, 0x5c,
	0xf9, 0x8f, 0xd5, 0xeb, 0xab, 0x28, 0xf7, 0xd9, 0xdf, 0x01, 0x00, 0x00, 0xff, 0xff, 0x22, 0x17,
	0xf5, 0xc1, 0xe5, 0x12, 0x00, 0x00,
}
