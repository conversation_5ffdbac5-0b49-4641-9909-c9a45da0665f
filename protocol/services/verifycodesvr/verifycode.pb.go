// Code generated by protoc-gen-gogo.
// source: src/verifycodesvr/verifycode.proto
// DO NOT EDIT!

/*
	Package verifycode is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/verifycodesvr/verifycode.proto

	It has these top-level messages:
		CreateVerifyCodeReq
		CreateVerifyCodeResp
		CheckValidReq
		CheckValidResp
		CheckVerifyCodeGlobalSwitchReq
		CheckVerifyCodeGlobalSwitchResp
		CheckVerifyCodeStatusReq
		CheckVerifyCodeStatusResp
		PassVerifyCodeCheckReq
		PassVerifyCodeCheckResp
		CreateVerifyCodeByKeyReq
		CreateVerifyCodeByKeyResp
		ValidateVerifyCodeReq
*/
package verifycode

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type CheckResult int32

const (
	CheckResult_ok              CheckResult = 0
	CheckResult_not_exists      CheckResult = 1
	CheckResult_session_changed CheckResult = 2
	CheckResult_incorrect_code  CheckResult = 3
	CheckResult_not_verified    CheckResult = 4
	CheckResult_pass            CheckResult = 5
)

var CheckResult_name = map[int32]string{
	0: "ok",
	1: "not_exists",
	2: "session_changed",
	3: "incorrect_code",
	4: "not_verified",
	5: "pass",
}
var CheckResult_value = map[string]int32{
	"ok":              0,
	"not_exists":      1,
	"session_changed": 2,
	"incorrect_code":  3,
	"not_verified":    4,
	"pass":            5,
}

func (x CheckResult) Enum() *CheckResult {
	p := new(CheckResult)
	*p = x
	return p
}
func (x CheckResult) String() string {
	return proto.EnumName(CheckResult_name, int32(x))
}
func (x *CheckResult) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CheckResult_value, data, "CheckResult")
	if err != nil {
		return err
	}
	*x = CheckResult(value)
	return nil
}
func (CheckResult) EnumDescriptor() ([]byte, []int) { return fileDescriptorVerifycode, []int{0} }

type PassVerifyCodeCheckReq_EPASS_OP_SOURCE_TYPE int32

const (
	PassVerifyCodeCheckReq_ENUM_SMS_CHECKER              PassVerifyCodeCheckReq_EPASS_OP_SOURCE_TYPE = 1
	PassVerifyCodeCheckReq_ENUM_THIRDPARTY_TOKEN_CHECKER PassVerifyCodeCheckReq_EPASS_OP_SOURCE_TYPE = 2
)

var PassVerifyCodeCheckReq_EPASS_OP_SOURCE_TYPE_name = map[int32]string{
	1: "ENUM_SMS_CHECKER",
	2: "ENUM_THIRDPARTY_TOKEN_CHECKER",
}
var PassVerifyCodeCheckReq_EPASS_OP_SOURCE_TYPE_value = map[string]int32{
	"ENUM_SMS_CHECKER":              1,
	"ENUM_THIRDPARTY_TOKEN_CHECKER": 2,
}

func (x PassVerifyCodeCheckReq_EPASS_OP_SOURCE_TYPE) Enum() *PassVerifyCodeCheckReq_EPASS_OP_SOURCE_TYPE {
	p := new(PassVerifyCodeCheckReq_EPASS_OP_SOURCE_TYPE)
	*p = x
	return p
}
func (x PassVerifyCodeCheckReq_EPASS_OP_SOURCE_TYPE) String() string {
	return proto.EnumName(PassVerifyCodeCheckReq_EPASS_OP_SOURCE_TYPE_name, int32(x))
}
func (x *PassVerifyCodeCheckReq_EPASS_OP_SOURCE_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PassVerifyCodeCheckReq_EPASS_OP_SOURCE_TYPE_value, data, "PassVerifyCodeCheckReq_EPASS_OP_SOURCE_TYPE")
	if err != nil {
		return err
	}
	*x = PassVerifyCodeCheckReq_EPASS_OP_SOURCE_TYPE(value)
	return nil
}
func (PassVerifyCodeCheckReq_EPASS_OP_SOURCE_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorVerifycode, []int{8, 0}
}

type CreateVerifyCodeReq struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Type    uint32 `protobuf:"varint,2,req,name=type" json:"type"`
	Session string `protobuf:"bytes,3,req,name=session" json:"session"`
	CodeLen uint32 `protobuf:"varint,4,req,name=code_len,json=codeLen" json:"code_len"`
	Ttl     uint32 `protobuf:"varint,5,opt,name=ttl" json:"ttl"`
}

func (m *CreateVerifyCodeReq) Reset()                    { *m = CreateVerifyCodeReq{} }
func (m *CreateVerifyCodeReq) String() string            { return proto.CompactTextString(m) }
func (*CreateVerifyCodeReq) ProtoMessage()               {}
func (*CreateVerifyCodeReq) Descriptor() ([]byte, []int) { return fileDescriptorVerifycode, []int{0} }

func (m *CreateVerifyCodeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CreateVerifyCodeReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *CreateVerifyCodeReq) GetSession() string {
	if m != nil {
		return m.Session
	}
	return ""
}

func (m *CreateVerifyCodeReq) GetCodeLen() uint32 {
	if m != nil {
		return m.CodeLen
	}
	return 0
}

func (m *CreateVerifyCodeReq) GetTtl() uint32 {
	if m != nil {
		return m.Ttl
	}
	return 0
}

type CreateVerifyCodeResp struct {
	Code string `protobuf:"bytes,1,req,name=code" json:"code"`
}

func (m *CreateVerifyCodeResp) Reset()                    { *m = CreateVerifyCodeResp{} }
func (m *CreateVerifyCodeResp) String() string            { return proto.CompactTextString(m) }
func (*CreateVerifyCodeResp) ProtoMessage()               {}
func (*CreateVerifyCodeResp) Descriptor() ([]byte, []int) { return fileDescriptorVerifycode, []int{1} }

func (m *CreateVerifyCodeResp) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

// 检查验证码的有效性
type CheckValidReq struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Type    uint32 `protobuf:"varint,2,req,name=type" json:"type"`
	Session string `protobuf:"bytes,3,req,name=session" json:"session"`
	Code    string `protobuf:"bytes,4,opt,name=code" json:"code"`
}

func (m *CheckValidReq) Reset()                    { *m = CheckValidReq{} }
func (m *CheckValidReq) String() string            { return proto.CompactTextString(m) }
func (*CheckValidReq) ProtoMessage()               {}
func (*CheckValidReq) Descriptor() ([]byte, []int) { return fileDescriptorVerifycode, []int{2} }

func (m *CheckValidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckValidReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *CheckValidReq) GetSession() string {
	if m != nil {
		return m.Session
	}
	return ""
}

func (m *CheckValidReq) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

type CheckValidResp struct {
	Result    uint32 `protobuf:"varint,1,req,name=result" json:"result"`
	Timestamp uint32 `protobuf:"varint,2,req,name=timestamp" json:"timestamp"`
	Cooldown  uint32 `protobuf:"varint,3,opt,name=cooldown" json:"cooldown"`
}

func (m *CheckValidResp) Reset()                    { *m = CheckValidResp{} }
func (m *CheckValidResp) String() string            { return proto.CompactTextString(m) }
func (*CheckValidResp) ProtoMessage()               {}
func (*CheckValidResp) Descriptor() ([]byte, []int) { return fileDescriptorVerifycode, []int{3} }

func (m *CheckValidResp) GetResult() uint32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *CheckValidResp) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *CheckValidResp) GetCooldown() uint32 {
	if m != nil {
		return m.Cooldown
	}
	return 0
}

// 获取是否开启验证码功能 全局开关
type CheckVerifyCodeGlobalSwitchReq struct {
}

func (m *CheckVerifyCodeGlobalSwitchReq) Reset()         { *m = CheckVerifyCodeGlobalSwitchReq{} }
func (m *CheckVerifyCodeGlobalSwitchReq) String() string { return proto.CompactTextString(m) }
func (*CheckVerifyCodeGlobalSwitchReq) ProtoMessage()    {}
func (*CheckVerifyCodeGlobalSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptorVerifycode, []int{4}
}

type CheckVerifyCodeGlobalSwitchResp struct {
	IsOpen bool `protobuf:"varint,1,req,name=is_open,json=isOpen" json:"is_open"`
}

func (m *CheckVerifyCodeGlobalSwitchResp) Reset()         { *m = CheckVerifyCodeGlobalSwitchResp{} }
func (m *CheckVerifyCodeGlobalSwitchResp) String() string { return proto.CompactTextString(m) }
func (*CheckVerifyCodeGlobalSwitchResp) ProtoMessage()    {}
func (*CheckVerifyCodeGlobalSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptorVerifycode, []int{5}
}

func (m *CheckVerifyCodeGlobalSwitchResp) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

// 检查是否能需要进行验证操作（比如 每种TYPE 每次session 只需要验证一次）
type CheckVerifyCodeStatusReq struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Type    uint32 `protobuf:"varint,2,req,name=type" json:"type"`
	Session string `protobuf:"bytes,3,req,name=session" json:"session"`
}

func (m *CheckVerifyCodeStatusReq) Reset()         { *m = CheckVerifyCodeStatusReq{} }
func (m *CheckVerifyCodeStatusReq) String() string { return proto.CompactTextString(m) }
func (*CheckVerifyCodeStatusReq) ProtoMessage()    {}
func (*CheckVerifyCodeStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorVerifycode, []int{6}
}

func (m *CheckVerifyCodeStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckVerifyCodeStatusReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *CheckVerifyCodeStatusReq) GetSession() string {
	if m != nil {
		return m.Session
	}
	return ""
}

type CheckVerifyCodeStatusResp struct {
	Result    uint32 `protobuf:"varint,1,req,name=result" json:"result"`
	Timestamp uint32 `protobuf:"varint,2,opt,name=timestamp" json:"timestamp"`
	Cooldown  uint32 `protobuf:"varint,3,opt,name=cooldown" json:"cooldown"`
}

func (m *CheckVerifyCodeStatusResp) Reset()         { *m = CheckVerifyCodeStatusResp{} }
func (m *CheckVerifyCodeStatusResp) String() string { return proto.CompactTextString(m) }
func (*CheckVerifyCodeStatusResp) ProtoMessage()    {}
func (*CheckVerifyCodeStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorVerifycode, []int{7}
}

func (m *CheckVerifyCodeStatusResp) GetResult() uint32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *CheckVerifyCodeStatusResp) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *CheckVerifyCodeStatusResp) GetCooldown() uint32 {
	if m != nil {
		return m.Cooldown
	}
	return 0
}

// 直接设置通过验证
type PassVerifyCodeCheckReq struct {
	Uid            uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Type           uint32 `protobuf:"varint,2,req,name=type" json:"type"`
	Session        string `protobuf:"bytes,3,req,name=session" json:"session"`
	Ttl            uint32 `protobuf:"varint,5,req,name=ttl" json:"ttl"`
	PassSourceType uint32 `protobuf:"varint,6,req,name=pass_source_type,json=passSourceType" json:"pass_source_type"`
}

func (m *PassVerifyCodeCheckReq) Reset()                    { *m = PassVerifyCodeCheckReq{} }
func (m *PassVerifyCodeCheckReq) String() string            { return proto.CompactTextString(m) }
func (*PassVerifyCodeCheckReq) ProtoMessage()               {}
func (*PassVerifyCodeCheckReq) Descriptor() ([]byte, []int) { return fileDescriptorVerifycode, []int{8} }

func (m *PassVerifyCodeCheckReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PassVerifyCodeCheckReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *PassVerifyCodeCheckReq) GetSession() string {
	if m != nil {
		return m.Session
	}
	return ""
}

func (m *PassVerifyCodeCheckReq) GetTtl() uint32 {
	if m != nil {
		return m.Ttl
	}
	return 0
}

func (m *PassVerifyCodeCheckReq) GetPassSourceType() uint32 {
	if m != nil {
		return m.PassSourceType
	}
	return 0
}

type PassVerifyCodeCheckResp struct {
}

func (m *PassVerifyCodeCheckResp) Reset()         { *m = PassVerifyCodeCheckResp{} }
func (m *PassVerifyCodeCheckResp) String() string { return proto.CompactTextString(m) }
func (*PassVerifyCodeCheckResp) ProtoMessage()    {}
func (*PassVerifyCodeCheckResp) Descriptor() ([]byte, []int) {
	return fileDescriptorVerifycode, []int{9}
}

// 没有UID参数情况下使用，原来smsvr服务的接口CreateVerifyCode
type CreateVerifyCodeByKeyReq struct {
	Key string `protobuf:"bytes,1,req,name=key" json:"key"`
	Len uint32 `protobuf:"varint,2,req,name=len" json:"len"`
	Ttl uint32 `protobuf:"varint,3,opt,name=ttl" json:"ttl"`
}

func (m *CreateVerifyCodeByKeyReq) Reset()         { *m = CreateVerifyCodeByKeyReq{} }
func (m *CreateVerifyCodeByKeyReq) String() string { return proto.CompactTextString(m) }
func (*CreateVerifyCodeByKeyReq) ProtoMessage()    {}
func (*CreateVerifyCodeByKeyReq) Descriptor() ([]byte, []int) {
	return fileDescriptorVerifycode, []int{10}
}

func (m *CreateVerifyCodeByKeyReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *CreateVerifyCodeByKeyReq) GetLen() uint32 {
	if m != nil {
		return m.Len
	}
	return 0
}

func (m *CreateVerifyCodeByKeyReq) GetTtl() uint32 {
	if m != nil {
		return m.Ttl
	}
	return 0
}

type CreateVerifyCodeByKeyResp struct {
	VerifyCode string `protobuf:"bytes,2,req,name=verify_code,json=verifyCode" json:"verify_code"`
}

func (m *CreateVerifyCodeByKeyResp) Reset()         { *m = CreateVerifyCodeByKeyResp{} }
func (m *CreateVerifyCodeByKeyResp) String() string { return proto.CompactTextString(m) }
func (*CreateVerifyCodeByKeyResp) ProtoMessage()    {}
func (*CreateVerifyCodeByKeyResp) Descriptor() ([]byte, []int) {
	return fileDescriptorVerifycode, []int{11}
}

func (m *CreateVerifyCodeByKeyResp) GetVerifyCode() string {
	if m != nil {
		return m.VerifyCode
	}
	return ""
}

// 验证CreateVerifyCodeByKey产生的接口, 员smsvr的ValidateVerifyCode
type ValidateVerifyCodeReq struct {
	Key          string `protobuf:"bytes,1,req,name=key" json:"key"`
	VerifyCode   string `protobuf:"bytes,2,req,name=verify_code,json=verifyCode" json:"verify_code"`
	DelIfSuccess bool   `protobuf:"varint,3,opt,name=del_if_success,json=delIfSuccess" json:"del_if_success"`
}

func (m *ValidateVerifyCodeReq) Reset()                    { *m = ValidateVerifyCodeReq{} }
func (m *ValidateVerifyCodeReq) String() string            { return proto.CompactTextString(m) }
func (*ValidateVerifyCodeReq) ProtoMessage()               {}
func (*ValidateVerifyCodeReq) Descriptor() ([]byte, []int) { return fileDescriptorVerifycode, []int{12} }

func (m *ValidateVerifyCodeReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *ValidateVerifyCodeReq) GetVerifyCode() string {
	if m != nil {
		return m.VerifyCode
	}
	return ""
}

func (m *ValidateVerifyCodeReq) GetDelIfSuccess() bool {
	if m != nil {
		return m.DelIfSuccess
	}
	return false
}

func init() {
	proto.RegisterType((*CreateVerifyCodeReq)(nil), "verifycode.CreateVerifyCodeReq")
	proto.RegisterType((*CreateVerifyCodeResp)(nil), "verifycode.CreateVerifyCodeResp")
	proto.RegisterType((*CheckValidReq)(nil), "verifycode.CheckValidReq")
	proto.RegisterType((*CheckValidResp)(nil), "verifycode.CheckValidResp")
	proto.RegisterType((*CheckVerifyCodeGlobalSwitchReq)(nil), "verifycode.CheckVerifyCodeGlobalSwitchReq")
	proto.RegisterType((*CheckVerifyCodeGlobalSwitchResp)(nil), "verifycode.CheckVerifyCodeGlobalSwitchResp")
	proto.RegisterType((*CheckVerifyCodeStatusReq)(nil), "verifycode.CheckVerifyCodeStatusReq")
	proto.RegisterType((*CheckVerifyCodeStatusResp)(nil), "verifycode.CheckVerifyCodeStatusResp")
	proto.RegisterType((*PassVerifyCodeCheckReq)(nil), "verifycode.PassVerifyCodeCheckReq")
	proto.RegisterType((*PassVerifyCodeCheckResp)(nil), "verifycode.PassVerifyCodeCheckResp")
	proto.RegisterType((*CreateVerifyCodeByKeyReq)(nil), "verifycode.CreateVerifyCodeByKeyReq")
	proto.RegisterType((*CreateVerifyCodeByKeyResp)(nil), "verifycode.CreateVerifyCodeByKeyResp")
	proto.RegisterType((*ValidateVerifyCodeReq)(nil), "verifycode.ValidateVerifyCodeReq")
	proto.RegisterEnum("verifycode.CheckResult", CheckResult_name, CheckResult_value)
	proto.RegisterEnum("verifycode.PassVerifyCodeCheckReq_EPASS_OP_SOURCE_TYPE", PassVerifyCodeCheckReq_EPASS_OP_SOURCE_TYPE_name, PassVerifyCodeCheckReq_EPASS_OP_SOURCE_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for VerifyCode service

type VerifyCodeClient interface {
	// 创建验证码
	CreateVerifyCode(ctx context.Context, in *CreateVerifyCodeReq, opts ...grpc.CallOption) (*CreateVerifyCodeResp, error)
	// 验证验证码
	CheckValid(ctx context.Context, in *CheckValidReq, opts ...grpc.CallOption) (*CheckValidResp, error)
	// 获取是否开启验证码功能 全局开关
	CheckVerifyCodeGlobalSwitch(ctx context.Context, in *CheckVerifyCodeGlobalSwitchReq, opts ...grpc.CallOption) (*CheckVerifyCodeGlobalSwitchResp, error)
	// 检查是否能需要进行验证操作（比如 每种TYPE 每次session 只需要验证一次）
	CheckVerifyCodeStatus(ctx context.Context, in *CheckVerifyCodeStatusReq, opts ...grpc.CallOption) (*CheckVerifyCodeStatusResp, error)
	// 直接设置通过验证
	PassVerifyCodeCheck(ctx context.Context, in *PassVerifyCodeCheckReq, opts ...grpc.CallOption) (*PassVerifyCodeCheckResp, error)
	// 没有UID的情况下使用的产生验证码接口，原smsvr服务的CreateVerifyCode接口
	CreateVerifyCodeByKey(ctx context.Context, in *CreateVerifyCodeByKeyReq, opts ...grpc.CallOption) (*CreateVerifyCodeByKeyResp, error)
	// 没有UID的情况下使用的验证接口，原来smsvr服的ValidateVerifyCode接口
	ValidateVerifyCode(ctx context.Context, in *ValidateVerifyCodeReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
}

type verifyCodeClient struct {
	cc *grpc.ClientConn
}

func NewVerifyCodeClient(cc *grpc.ClientConn) VerifyCodeClient {
	return &verifyCodeClient{cc}
}

func (c *verifyCodeClient) CreateVerifyCode(ctx context.Context, in *CreateVerifyCodeReq, opts ...grpc.CallOption) (*CreateVerifyCodeResp, error) {
	out := new(CreateVerifyCodeResp)
	err := grpc.Invoke(ctx, "/verifycode.VerifyCode/CreateVerifyCode", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *verifyCodeClient) CheckValid(ctx context.Context, in *CheckValidReq, opts ...grpc.CallOption) (*CheckValidResp, error) {
	out := new(CheckValidResp)
	err := grpc.Invoke(ctx, "/verifycode.VerifyCode/CheckValid", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *verifyCodeClient) CheckVerifyCodeGlobalSwitch(ctx context.Context, in *CheckVerifyCodeGlobalSwitchReq, opts ...grpc.CallOption) (*CheckVerifyCodeGlobalSwitchResp, error) {
	out := new(CheckVerifyCodeGlobalSwitchResp)
	err := grpc.Invoke(ctx, "/verifycode.VerifyCode/CheckVerifyCodeGlobalSwitch", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *verifyCodeClient) CheckVerifyCodeStatus(ctx context.Context, in *CheckVerifyCodeStatusReq, opts ...grpc.CallOption) (*CheckVerifyCodeStatusResp, error) {
	out := new(CheckVerifyCodeStatusResp)
	err := grpc.Invoke(ctx, "/verifycode.VerifyCode/CheckVerifyCodeStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *verifyCodeClient) PassVerifyCodeCheck(ctx context.Context, in *PassVerifyCodeCheckReq, opts ...grpc.CallOption) (*PassVerifyCodeCheckResp, error) {
	out := new(PassVerifyCodeCheckResp)
	err := grpc.Invoke(ctx, "/verifycode.VerifyCode/PassVerifyCodeCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *verifyCodeClient) CreateVerifyCodeByKey(ctx context.Context, in *CreateVerifyCodeByKeyReq, opts ...grpc.CallOption) (*CreateVerifyCodeByKeyResp, error) {
	out := new(CreateVerifyCodeByKeyResp)
	err := grpc.Invoke(ctx, "/verifycode.VerifyCode/CreateVerifyCodeByKey", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *verifyCodeClient) ValidateVerifyCode(ctx context.Context, in *ValidateVerifyCodeReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/verifycode.VerifyCode/ValidateVerifyCode", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for VerifyCode service

type VerifyCodeServer interface {
	// 创建验证码
	CreateVerifyCode(context.Context, *CreateVerifyCodeReq) (*CreateVerifyCodeResp, error)
	// 验证验证码
	CheckValid(context.Context, *CheckValidReq) (*CheckValidResp, error)
	// 获取是否开启验证码功能 全局开关
	CheckVerifyCodeGlobalSwitch(context.Context, *CheckVerifyCodeGlobalSwitchReq) (*CheckVerifyCodeGlobalSwitchResp, error)
	// 检查是否能需要进行验证操作（比如 每种TYPE 每次session 只需要验证一次）
	CheckVerifyCodeStatus(context.Context, *CheckVerifyCodeStatusReq) (*CheckVerifyCodeStatusResp, error)
	// 直接设置通过验证
	PassVerifyCodeCheck(context.Context, *PassVerifyCodeCheckReq) (*PassVerifyCodeCheckResp, error)
	// 没有UID的情况下使用的产生验证码接口，原smsvr服务的CreateVerifyCode接口
	CreateVerifyCodeByKey(context.Context, *CreateVerifyCodeByKeyReq) (*CreateVerifyCodeByKeyResp, error)
	// 没有UID的情况下使用的验证接口，原来smsvr服的ValidateVerifyCode接口
	ValidateVerifyCode(context.Context, *ValidateVerifyCodeReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
}

func RegisterVerifyCodeServer(s *grpc.Server, srv VerifyCodeServer) {
	s.RegisterService(&_VerifyCode_serviceDesc, srv)
}

func _VerifyCode_CreateVerifyCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVerifyCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VerifyCodeServer).CreateVerifyCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/verifycode.VerifyCode/CreateVerifyCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VerifyCodeServer).CreateVerifyCode(ctx, req.(*CreateVerifyCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VerifyCode_CheckValid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckValidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VerifyCodeServer).CheckValid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/verifycode.VerifyCode/CheckValid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VerifyCodeServer).CheckValid(ctx, req.(*CheckValidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VerifyCode_CheckVerifyCodeGlobalSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckVerifyCodeGlobalSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VerifyCodeServer).CheckVerifyCodeGlobalSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/verifycode.VerifyCode/CheckVerifyCodeGlobalSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VerifyCodeServer).CheckVerifyCodeGlobalSwitch(ctx, req.(*CheckVerifyCodeGlobalSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VerifyCode_CheckVerifyCodeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckVerifyCodeStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VerifyCodeServer).CheckVerifyCodeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/verifycode.VerifyCode/CheckVerifyCodeStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VerifyCodeServer).CheckVerifyCodeStatus(ctx, req.(*CheckVerifyCodeStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VerifyCode_PassVerifyCodeCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PassVerifyCodeCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VerifyCodeServer).PassVerifyCodeCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/verifycode.VerifyCode/PassVerifyCodeCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VerifyCodeServer).PassVerifyCodeCheck(ctx, req.(*PassVerifyCodeCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VerifyCode_CreateVerifyCodeByKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVerifyCodeByKeyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VerifyCodeServer).CreateVerifyCodeByKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/verifycode.VerifyCode/CreateVerifyCodeByKey",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VerifyCodeServer).CreateVerifyCodeByKey(ctx, req.(*CreateVerifyCodeByKeyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VerifyCode_ValidateVerifyCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateVerifyCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VerifyCodeServer).ValidateVerifyCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/verifycode.VerifyCode/ValidateVerifyCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VerifyCodeServer).ValidateVerifyCode(ctx, req.(*ValidateVerifyCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _VerifyCode_serviceDesc = grpc.ServiceDesc{
	ServiceName: "verifycode.VerifyCode",
	HandlerType: (*VerifyCodeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateVerifyCode",
			Handler:    _VerifyCode_CreateVerifyCode_Handler,
		},
		{
			MethodName: "CheckValid",
			Handler:    _VerifyCode_CheckValid_Handler,
		},
		{
			MethodName: "CheckVerifyCodeGlobalSwitch",
			Handler:    _VerifyCode_CheckVerifyCodeGlobalSwitch_Handler,
		},
		{
			MethodName: "CheckVerifyCodeStatus",
			Handler:    _VerifyCode_CheckVerifyCodeStatus_Handler,
		},
		{
			MethodName: "PassVerifyCodeCheck",
			Handler:    _VerifyCode_PassVerifyCodeCheck_Handler,
		},
		{
			MethodName: "CreateVerifyCodeByKey",
			Handler:    _VerifyCode_CreateVerifyCodeByKey_Handler,
		},
		{
			MethodName: "ValidateVerifyCode",
			Handler:    _VerifyCode_ValidateVerifyCode_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/verifycodesvr/verifycode.proto",
}

func (m *CreateVerifyCodeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateVerifyCodeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(len(m.Session)))
	i += copy(dAtA[i:], m.Session)
	dAtA[i] = 0x20
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.CodeLen))
	dAtA[i] = 0x28
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.Ttl))
	return i, nil
}

func (m *CreateVerifyCodeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateVerifyCodeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(len(m.Code)))
	i += copy(dAtA[i:], m.Code)
	return i, nil
}

func (m *CheckValidReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckValidReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(len(m.Session)))
	i += copy(dAtA[i:], m.Session)
	dAtA[i] = 0x22
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(len(m.Code)))
	i += copy(dAtA[i:], m.Code)
	return i, nil
}

func (m *CheckValidResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckValidResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.Result))
	dAtA[i] = 0x10
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.Timestamp))
	dAtA[i] = 0x18
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.Cooldown))
	return i, nil
}

func (m *CheckVerifyCodeGlobalSwitchReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckVerifyCodeGlobalSwitchReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *CheckVerifyCodeGlobalSwitchResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckVerifyCodeGlobalSwitchResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsOpen {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *CheckVerifyCodeStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckVerifyCodeStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(len(m.Session)))
	i += copy(dAtA[i:], m.Session)
	return i, nil
}

func (m *CheckVerifyCodeStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckVerifyCodeStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.Result))
	dAtA[i] = 0x10
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.Timestamp))
	dAtA[i] = 0x18
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.Cooldown))
	return i, nil
}

func (m *PassVerifyCodeCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PassVerifyCodeCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(len(m.Session)))
	i += copy(dAtA[i:], m.Session)
	dAtA[i] = 0x28
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.Ttl))
	dAtA[i] = 0x30
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.PassSourceType))
	return i, nil
}

func (m *PassVerifyCodeCheckResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PassVerifyCodeCheckResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *CreateVerifyCodeByKeyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateVerifyCodeByKeyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	dAtA[i] = 0x10
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.Len))
	dAtA[i] = 0x18
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(m.Ttl))
	return i, nil
}

func (m *CreateVerifyCodeByKeyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateVerifyCodeByKeyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x12
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(len(m.VerifyCode)))
	i += copy(dAtA[i:], m.VerifyCode)
	return i, nil
}

func (m *ValidateVerifyCodeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ValidateVerifyCodeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	dAtA[i] = 0x12
	i++
	i = encodeVarintVerifycode(dAtA, i, uint64(len(m.VerifyCode)))
	i += copy(dAtA[i:], m.VerifyCode)
	dAtA[i] = 0x18
	i++
	if m.DelIfSuccess {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func encodeFixed64Verifycode(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Verifycode(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintVerifycode(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *CreateVerifyCodeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovVerifycode(uint64(m.Uid))
	n += 1 + sovVerifycode(uint64(m.Type))
	l = len(m.Session)
	n += 1 + l + sovVerifycode(uint64(l))
	n += 1 + sovVerifycode(uint64(m.CodeLen))
	n += 1 + sovVerifycode(uint64(m.Ttl))
	return n
}

func (m *CreateVerifyCodeResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.Code)
	n += 1 + l + sovVerifycode(uint64(l))
	return n
}

func (m *CheckValidReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovVerifycode(uint64(m.Uid))
	n += 1 + sovVerifycode(uint64(m.Type))
	l = len(m.Session)
	n += 1 + l + sovVerifycode(uint64(l))
	l = len(m.Code)
	n += 1 + l + sovVerifycode(uint64(l))
	return n
}

func (m *CheckValidResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovVerifycode(uint64(m.Result))
	n += 1 + sovVerifycode(uint64(m.Timestamp))
	n += 1 + sovVerifycode(uint64(m.Cooldown))
	return n
}

func (m *CheckVerifyCodeGlobalSwitchReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *CheckVerifyCodeGlobalSwitchResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *CheckVerifyCodeStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovVerifycode(uint64(m.Uid))
	n += 1 + sovVerifycode(uint64(m.Type))
	l = len(m.Session)
	n += 1 + l + sovVerifycode(uint64(l))
	return n
}

func (m *CheckVerifyCodeStatusResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovVerifycode(uint64(m.Result))
	n += 1 + sovVerifycode(uint64(m.Timestamp))
	n += 1 + sovVerifycode(uint64(m.Cooldown))
	return n
}

func (m *PassVerifyCodeCheckReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovVerifycode(uint64(m.Uid))
	n += 1 + sovVerifycode(uint64(m.Type))
	l = len(m.Session)
	n += 1 + l + sovVerifycode(uint64(l))
	n += 1 + sovVerifycode(uint64(m.Ttl))
	n += 1 + sovVerifycode(uint64(m.PassSourceType))
	return n
}

func (m *PassVerifyCodeCheckResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *CreateVerifyCodeByKeyReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovVerifycode(uint64(l))
	n += 1 + sovVerifycode(uint64(m.Len))
	n += 1 + sovVerifycode(uint64(m.Ttl))
	return n
}

func (m *CreateVerifyCodeByKeyResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.VerifyCode)
	n += 1 + l + sovVerifycode(uint64(l))
	return n
}

func (m *ValidateVerifyCodeReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovVerifycode(uint64(l))
	l = len(m.VerifyCode)
	n += 1 + l + sovVerifycode(uint64(l))
	n += 2
	return n
}

func sovVerifycode(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozVerifycode(x uint64) (n int) {
	return sovVerifycode(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *CreateVerifyCodeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowVerifycode
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateVerifyCodeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateVerifyCodeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Session", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthVerifycode
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Session = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CodeLen", wireType)
			}
			m.CodeLen = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CodeLen |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ttl", wireType)
			}
			m.Ttl = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ttl |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipVerifycode(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthVerifycode
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("session")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("code_len")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateVerifyCodeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowVerifycode
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateVerifyCodeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateVerifyCodeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthVerifycode
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Code = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipVerifycode(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthVerifycode
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("code")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckValidReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowVerifycode
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckValidReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckValidReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Session", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthVerifycode
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Session = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthVerifycode
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Code = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipVerifycode(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthVerifycode
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("session")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckValidResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowVerifycode
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckValidResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckValidResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cooldown", wireType)
			}
			m.Cooldown = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cooldown |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipVerifycode(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthVerifycode
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("result")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("timestamp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckVerifyCodeGlobalSwitchReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowVerifycode
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckVerifyCodeGlobalSwitchReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckVerifyCodeGlobalSwitchReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipVerifycode(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthVerifycode
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckVerifyCodeGlobalSwitchResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowVerifycode
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckVerifyCodeGlobalSwitchResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckVerifyCodeGlobalSwitchResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsOpen", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsOpen = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipVerifycode(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthVerifycode
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_open")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckVerifyCodeStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowVerifycode
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckVerifyCodeStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckVerifyCodeStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Session", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthVerifycode
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Session = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipVerifycode(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthVerifycode
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("session")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckVerifyCodeStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowVerifycode
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckVerifyCodeStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckVerifyCodeStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cooldown", wireType)
			}
			m.Cooldown = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cooldown |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipVerifycode(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthVerifycode
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("result")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PassVerifyCodeCheckReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowVerifycode
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PassVerifyCodeCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PassVerifyCodeCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Session", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthVerifycode
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Session = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ttl", wireType)
			}
			m.Ttl = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ttl |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PassSourceType", wireType)
			}
			m.PassSourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PassSourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipVerifycode(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthVerifycode
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("session")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("ttl")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("pass_source_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PassVerifyCodeCheckResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowVerifycode
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PassVerifyCodeCheckResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PassVerifyCodeCheckResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipVerifycode(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthVerifycode
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateVerifyCodeByKeyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowVerifycode
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateVerifyCodeByKeyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateVerifyCodeByKeyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthVerifycode
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Len", wireType)
			}
			m.Len = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Len |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ttl", wireType)
			}
			m.Ttl = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ttl |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipVerifycode(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthVerifycode
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("key")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("len")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateVerifyCodeByKeyResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowVerifycode
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateVerifyCodeByKeyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateVerifyCodeByKeyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthVerifycode
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VerifyCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipVerifycode(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthVerifycode
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("verify_code")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ValidateVerifyCodeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowVerifycode
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ValidateVerifyCodeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ValidateVerifyCodeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthVerifycode
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthVerifycode
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VerifyCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DelIfSuccess", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.DelIfSuccess = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipVerifycode(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthVerifycode
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("key")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("verify_code")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipVerifycode(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowVerifycode
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowVerifycode
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthVerifycode
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowVerifycode
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipVerifycode(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthVerifycode = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowVerifycode   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/verifycodesvr/verifycode.proto", fileDescriptorVerifycode) }

var fileDescriptorVerifycode = []byte{
	// 989 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x96, 0x4f, 0x6f, 0x1a, 0x47,
	0x18, 0xc6, 0xbd, 0x0b, 0xc6, 0xe4, 0x4d, 0xe2, 0xac, 0xc6, 0x4e, 0x0a, 0xd4, 0xc1, 0x9b, 0x49,
	0x2c, 0x45, 0xae, 0xc0, 0x6d, 0x8f, 0x16, 0x42, 0x35, 0x94, 0x36, 0x91, 0x9b, 0x80, 0x58, 0x12,
	0x29, 0xa7, 0x15, 0xde, 0x1d, 0xd7, 0x5b, 0x96, 0x9d, 0x09, 0x33, 0x6b, 0x97, 0xf6, 0xd0, 0x7f,
	0x97, 0xa8, 0xa7, 0xa6, 0x97, 0xf6, 0xd8, 0x83, 0xd5, 0xcf, 0x92, 0x4b, 0xa5, 0x7e, 0x82, 0xaa,
	0x72, 0x2f, 0xfe, 0x18, 0xd5, 0x0c, 0x0b, 0x2c, 0x18, 0xa8, 0x1b, 0xc9, 0x47, 0xde, 0x79, 0x67,
	0x9e, 0xdf, 0x3c, 0xbc, 0xf3, 0x00, 0x60, 0xde, 0x73, 0x76, 0x8e, 0x49, 0xcf, 0x3b, 0xec, 0x3b,
	0xd4, 0x25, 0xfc, 0xb8, 0x17, 0xfb, 0x54, 0x64, 0x3d, 0x2a, 0x28, 0x82, 0x71, 0x25, 0xf7, 0xc0,
	0xa1, 0xdd, 0x2e, 0x0d, 0x76, 0x84, 0x7f, 0xcc, 0x3c, 0xa7, 0xe3, 0x93, 0x1d, 0xde, 0x39, 0x08,
	0x3d, 0x5f, 0x78, 0x81, 0xe8, 0xb3, 0x68, 0x07, 0xfe, 0x4d, 0x83, 0xb5, 0x6a, 0x8f, 0xb4, 0x05,
	0x79, 0xae, 0xb6, 0x56, 0xa9, 0x4b, 0x9a, 0xe4, 0x25, 0xba, 0x03, 0x89, 0xd0, 0x73, 0x33, 0x9a,
	0xa9, 0x3f, 0xbc, 0x59, 0x49, 0xbe, 0xf9, 0x6b, 0x73, 0xa9, 0x29, 0x0b, 0x28, 0x03, 0x49, 0xb9,
	0x3b, 0xa3, 0xc7, 0x16, 0x54, 0x05, 0xe5, 0x61, 0x85, 0x13, 0xce, 0x3d, 0x1a, 0x64, 0x12, 0xa6,
	0xfe, 0xf0, 0x5a, 0xb4, 0x38, 0x2c, 0xa2, 0x4d, 0x48, 0x4b, 0x2e, 0xdb, 0x27, 0x41, 0x26, 0x19,
	0xdb, 0xbd, 0x22, 0xab, 0x9f, 0x91, 0x40, 0x4a, 0x0a, 0xe1, 0x67, 0x96, 0x4d, 0x6d, 0x2c, 0x29,
	0x84, 0x8f, 0xdf, 0x87, 0xf5, 0x8b, 0x84, 0x9c, 0x49, 0x14, 0xb9, 0x55, 0x31, 0x0e, 0xd5, 0x54,
	0x05, 0x7f, 0x0d, 0x37, 0xab, 0x47, 0xc4, 0xe9, 0x3c, 0x6f, 0xfb, 0x9e, 0x7b, 0x35, 0xb7, 0x19,
	0x8a, 0x27, 0x4d, 0x6d, 0x4a, 0x5c, 0xc0, 0x6a, 0x5c, 0x9c, 0x33, 0xb4, 0x01, 0xa9, 0x1e, 0xe1,
	0xa1, 0x2f, 0x26, 0x00, 0xa2, 0x1a, 0xc2, 0x70, 0x4d, 0x78, 0x5d, 0xc2, 0x45, 0xbb, 0xcb, 0x26,
	0x40, 0xc6, 0x65, 0x64, 0x4a, 0xef, 0xa8, 0xef, 0xd2, 0x13, 0x89, 0x33, 0xf6, 0x67, 0x54, 0xc5,
	0x26, 0xe4, 0x07, 0xaa, 0x23, 0x8f, 0x3e, 0xf5, 0xe9, 0x41, 0xdb, 0xb7, 0x4e, 0x3c, 0xe1, 0x1c,
	0x35, 0xc9, 0x4b, 0xfc, 0x11, 0x6c, 0x2e, 0xec, 0xe0, 0x0c, 0xdd, 0x85, 0x15, 0x8f, 0xdb, 0x94,
	0x91, 0x40, 0x91, 0xa6, 0x87, 0xa4, 0x1e, 0xaf, 0x33, 0x12, 0x60, 0x1f, 0x32, 0x53, 0x27, 0x58,
	0xa2, 0x2d, 0x42, 0x7e, 0x25, 0x0e, 0xe3, 0x6f, 0x20, 0x3b, 0x47, 0xed, 0xff, 0x5a, 0xaa, 0xbd,
	0x9d, 0xa5, 0x3f, 0xe8, 0x70, 0xa7, 0xd1, 0xe6, 0x7c, 0x0c, 0xa0, 0x78, 0xae, 0x66, 0x9e, 0x46,
	0xc3, 0xaf, 0x4f, 0x0c, 0x3f, 0x2a, 0x82, 0xc1, 0xda, 0x9c, 0xdb, 0x9c, 0x86, 0x3d, 0x87, 0xd8,
	0xea, 0xf4, 0x54, 0xac, 0x69, 0x55, 0xae, 0x5a, 0x6a, 0xb1, 0xd5, 0x67, 0x04, 0xd7, 0x61, 0xbd,
	0xd6, 0xd8, 0xb3, 0x2c, 0xbb, 0xde, 0xb0, 0xad, 0xfa, 0xb3, 0x66, 0xb5, 0x66, 0xb7, 0x5e, 0x34,
	0x6a, 0x68, 0x1d, 0x8c, 0xda, 0xd3, 0x67, 0x4f, 0x6c, 0xeb, 0x89, 0x65, 0x57, 0x1f, 0xd5, 0xaa,
	0xfb, 0xb5, 0xa6, 0xa1, 0xa1, 0x7b, 0x70, 0x57, 0x55, 0x5b, 0x8f, 0x1e, 0x37, 0x3f, 0x6e, 0xec,
	0x35, 0x5b, 0x2f, 0xec, 0x56, 0x7d, 0xbf, 0xf6, 0x74, 0xd4, 0xa2, 0xe3, 0x2c, 0xbc, 0x33, 0xd3,
	0x04, 0xce, 0xf0, 0x01, 0x64, 0xa6, 0x1f, 0x66, 0xa5, 0xbf, 0x4f, 0xfa, 0x91, 0x43, 0x1d, 0xd2,
	0x9f, 0x78, 0x9b, 0xb2, 0x20, 0xeb, 0x32, 0x00, 0xe2, 0x06, 0xc9, 0xc2, 0xf0, 0xfe, 0x89, 0xe9,
	0xc7, 0x5f, 0x81, 0xec, 0x1c, 0x0d, 0xce, 0xd0, 0x16, 0x5c, 0x1f, 0x04, 0x9e, 0xad, 0xde, 0xa2,
	0x1e, 0x13, 0x8b, 0x92, 0x50, 0xf6, 0xe3, 0xef, 0x35, 0xb8, 0xad, 0x5e, 0xe3, 0xac, 0x94, 0x9b,
	0x49, 0x79, 0xb9, 0x83, 0xd1, 0x36, 0xac, 0xba, 0xc4, 0xb7, 0xbd, 0x43, 0x9b, 0x87, 0x8e, 0x43,
	0x38, 0x57, 0xfc, 0xc3, 0x67, 0x73, 0xc3, 0x25, 0xfe, 0xe3, 0x43, 0x6b, 0xb0, 0xb2, 0xfd, 0x05,
	0x5c, 0x1f, 0x3a, 0x27, 0x47, 0x34, 0x05, 0x3a, 0xed, 0x18, 0x4b, 0x68, 0x15, 0x20, 0xa0, 0xc2,
	0x26, 0x5f, 0x7a, 0x5c, 0x70, 0x43, 0x43, 0x6b, 0x70, 0x2b, 0x1a, 0x09, 0xdb, 0x39, 0x6a, 0x07,
	0x9f, 0x13, 0xd7, 0xd0, 0x11, 0x82, 0x55, 0x2f, 0x70, 0x68, 0xaf, 0x47, 0x1c, 0xa1, 0x88, 0x8c,
	0x04, 0x32, 0xe0, 0x86, 0xdc, 0xa8, 0x68, 0x3c, 0xe2, 0x1a, 0x49, 0x94, 0x86, 0xa4, 0x1c, 0x06,
	0x63, 0xf9, 0xc3, 0xd7, 0x69, 0x80, 0xf1, 0x45, 0xd1, 0xaf, 0x1a, 0x18, 0xd3, 0x26, 0xa2, 0xcd,
	0x62, 0xec, 0xd7, 0x63, 0xc6, 0x2f, 0x40, 0xce, 0x5c, 0xdc, 0xc0, 0x19, 0xde, 0xfb, 0xf6, 0xf4,
	0x3c, 0xa1, 0xfd, 0x78, 0x7a, 0x9e, 0x48, 0x87, 0xbb, 0x62, 0x97, 0xef, 0xfa, 0xbb, 0x3f, 0x9f,
	0x9e, 0x27, 0xb6, 0x0b, 0xa1, 0x59, 0x0a, 0x3d, 0xb7, 0x6c, 0x16, 0x44, 0x49, 0x0e, 0x6c, 0xd9,
	0x2c, 0xf0, 0x52, 0x74, 0xab, 0xb2, 0x59, 0xf0, 0x4b, 0xf2, 0xc8, 0x82, 0x4f, 0x82, 0x32, 0xfa,
	0x4e, 0x03, 0x18, 0xa7, 0x25, 0xca, 0x4e, 0x68, 0xc6, 0x23, 0x3c, 0x97, 0x9b, 0xb7, 0xc4, 0x19,
	0x2e, 0x49, 0x10, 0x3d, 0x06, 0xe2, 0x28, 0x90, 0xad, 0x42, 0xb8, 0x88, 0xc3, 0x51, 0x1c, 0x65,
	0xf4, 0x5a, 0x83, 0x77, 0x17, 0x24, 0x23, 0xda, 0xbe, 0xa8, 0x3c, 0x2f, 0x64, 0x73, 0xef, 0x5d,
	0xba, 0x97, 0x33, 0x9c, 0x95, 0xd8, 0x09, 0x89, 0xad, 0x87, 0x0a, 0x38, 0x3d, 0x04, 0x46, 0xbf,
	0x68, 0x70, 0x7b, 0x66, 0xfa, 0xa1, 0x07, 0x0b, 0x14, 0x46, 0x71, 0x9c, 0xdb, 0xba, 0x44, 0x17,
	0x67, 0xf8, 0x03, 0x49, 0x90, 0x94, 0x04, 0xa9, 0x81, 0x71, 0x92, 0x62, 0x63, 0x91, 0x6d, 0xe8,
	0x77, 0x0d, 0xd6, 0x66, 0x04, 0x02, 0xc2, 0x71, 0xc5, 0xd9, 0xb1, 0x99, 0xbb, 0xff, 0x9f, 0x3d,
	0x9c, 0xe1, 0x4f, 0x24, 0xd3, 0x72, 0xec, 0xcb, 0xa4, 0x8a, 0x6a, 0x67, 0xf1, 0x97, 0x49, 0x4b,
	0x83, 0x84, 0x34, 0x29, 0x33, 0xd5, 0x32, 0x7a, 0x25, 0x2d, 0x9c, 0x15, 0x1d, 0x53, 0x16, 0xce,
	0x49, 0xb0, 0x29, 0x0b, 0xe7, 0x65, 0x10, 0xc6, 0x12, 0x37, 0x25, 0x71, 0x93, 0x9d, 0xe8, 0x01,
	0xdc, 0x2a, 0x74, 0x4a, 0x1d, 0xd2, 0x57, 0xa3, 0xae, 0xa6, 0xfc, 0x2b, 0x40, 0x17, 0xf3, 0x07,
	0xdd, 0x8b, 0x0b, 0xcc, 0xcc, 0xa7, 0xdc, 0x46, 0x71, 0xf4, 0xef, 0xad, 0x68, 0xed, 0x57, 0x06,
	0xff, 0xde, 0x6a, 0x5d, 0x26, 0xfa, 0x76, 0xa3, 0x82, 0xef, 0x4b, 0xe9, 0x95, 0x48, 0x7a, 0x30,
	0xf2, 0xc6, 0x48, 0x3a, 0x9a, 0xee, 0x5c, 0xea, 0xd5, 0xe9, 0x79, 0xe2, 0x8f, 0x93, 0x8a, 0xf1,
	0xe6, 0x2c, 0xaf, 0xfd, 0x79, 0x96, 0xd7, 0xfe, 0x3e, 0xcb, 0x6b, 0x3f, 0xfd, 0x93, 0x5f, 0xfa,
	0x37, 0x00, 0x00, 0xff, 0xff, 0x51, 0xc1, 0xae, 0xc4, 0x51, 0x0a, 0x00, 0x00,
}
