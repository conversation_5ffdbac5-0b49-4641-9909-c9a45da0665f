// Code generated by protoc-gen-go. DO NOT EDIT.
// source: game-pal/game-pal.proto

package game_pal // import "golang.52tt.com/protocol/services/game-pal"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 游戏搭子卡审核状态
type GamePalCardAuditState int32

const (
	GamePalCardAuditState_GamePalCardAuditStateNone GamePalCardAuditState = 0
	// 审核中
	GamePalCardAuditState_GamePalCardAuditStateReview GamePalCardAuditState = 1
	// 审核通过
	GamePalCardAuditState_GamePalCardAuditStatePass GamePalCardAuditState = 2
	// 审核拒绝
	GamePalCardAuditState_GamePalCardAuditStateReject GamePalCardAuditState = 3
)

var GamePalCardAuditState_name = map[int32]string{
	0: "GamePalCardAuditStateNone",
	1: "GamePalCardAuditStateReview",
	2: "GamePalCardAuditStatePass",
	3: "GamePalCardAuditStateReject",
}
var GamePalCardAuditState_value = map[string]int32{
	"GamePalCardAuditStateNone":   0,
	"GamePalCardAuditStateReview": 1,
	"GamePalCardAuditStatePass":   2,
	"GamePalCardAuditStateReject": 3,
}

func (x GamePalCardAuditState) String() string {
	return proto.EnumName(GamePalCardAuditState_name, int32(x))
}
func (GamePalCardAuditState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{0}
}

// 游戏搭子卡状态
type GamePalCardState int32

const (
	GamePalCardState_GamePalCardStateNone GamePalCardState = 0
	// 点亮
	GamePalCardState_GamePalCardStateLit GamePalCardState = 1
	// 熄灭
	GamePalCardState_GamePalCardStateExtinct GamePalCardState = 2
)

var GamePalCardState_name = map[int32]string{
	0: "GamePalCardStateNone",
	1: "GamePalCardStateLit",
	2: "GamePalCardStateExtinct",
}
var GamePalCardState_value = map[string]int32{
	"GamePalCardStateNone":    0,
	"GamePalCardStateLit":     1,
	"GamePalCardStateExtinct": 2,
}

func (x GamePalCardState) String() string {
	return proto.EnumName(GamePalCardState_name, int32(x))
}
func (GamePalCardState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{1}
}

type EventType int32

const (
	EventType_TYPE_INVALID_EVENT EventType = 0
	EventType_TYPE_LIGHT         EventType = 1
	EventType_TYPE_PUBLISH       EventType = 2
	EventType_TYPE_EXTINCT       EventType = 3
	EventType_TYPE_DELETE_CARD   EventType = 4
	EventType_TYPE_EDIT_CARD     EventType = 5
)

var EventType_name = map[int32]string{
	0: "TYPE_INVALID_EVENT",
	1: "TYPE_LIGHT",
	2: "TYPE_PUBLISH",
	3: "TYPE_EXTINCT",
	4: "TYPE_DELETE_CARD",
	5: "TYPE_EDIT_CARD",
}
var EventType_value = map[string]int32{
	"TYPE_INVALID_EVENT": 0,
	"TYPE_LIGHT":         1,
	"TYPE_PUBLISH":       2,
	"TYPE_EXTINCT":       3,
	"TYPE_DELETE_CARD":   4,
	"TYPE_EDIT_CARD":     5,
}

func (x EventType) String() string {
	return proto.EnumName(EventType_name, int32(x))
}
func (EventType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{2}
}

type DeclarationType int32

const (
	DeclarationType_TYPE_INVALID_DECLARATION_TYPE DeclarationType = 0
	DeclarationType_TYPE_OFFICIAL                 DeclarationType = 1
	DeclarationType_TYPE_CUSTOM                   DeclarationType = 2
)

var DeclarationType_name = map[int32]string{
	0: "TYPE_INVALID_DECLARATION_TYPE",
	1: "TYPE_OFFICIAL",
	2: "TYPE_CUSTOM",
}
var DeclarationType_value = map[string]int32{
	"TYPE_INVALID_DECLARATION_TYPE": 0,
	"TYPE_OFFICIAL":                 1,
	"TYPE_CUSTOM":                   2,
}

func (x DeclarationType) String() string {
	return proto.EnumName(DeclarationType_name, int32(x))
}
func (DeclarationType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{3}
}

// 批量擦亮接口
type PolishType int32

const (
	PolishType_INVALID_POLISH_TYPE PolishType = 0
	PolishType_LIGHT               PolishType = 1
	PolishType_PUBLISH             PolishType = 2
)

var PolishType_name = map[int32]string{
	0: "INVALID_POLISH_TYPE",
	1: "LIGHT",
	2: "PUBLISH",
}
var PolishType_value = map[string]int32{
	"INVALID_POLISH_TYPE": 0,
	"LIGHT":               1,
	"PUBLISH":             2,
}

func (x PolishType) String() string {
	return proto.EnumName(PolishType_name, int32(x))
}
func (PolishType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{4}
}

type GamePalBlock_Type int32

const (
	GamePalBlock_TypeDefault GamePalBlock_Type = 0
	GamePalBlock_Purpose     GamePalBlock_Type = 1
	GamePalBlock_PlayTime    GamePalBlock_Type = 2
)

var GamePalBlock_Type_name = map[int32]string{
	0: "TypeDefault",
	1: "Purpose",
	2: "PlayTime",
}
var GamePalBlock_Type_value = map[string]int32{
	"TypeDefault": 0,
	"Purpose":     1,
	"PlayTime":    2,
}

func (x GamePalBlock_Type) String() string {
	return proto.EnumName(GamePalBlock_Type_name, int32(x))
}
func (GamePalBlock_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{0, 0}
}

type GamePalBlock_Mode int32

const (
	GamePalBlock_SINGLE       GamePalBlock_Mode = 0
	GamePalBlock_MULTI        GamePalBlock_Mode = 1
	GamePalBlock_SETUP_NUMBER GamePalBlock_Mode = 2
)

var GamePalBlock_Mode_name = map[int32]string{
	0: "SINGLE",
	1: "MULTI",
	2: "SETUP_NUMBER",
}
var GamePalBlock_Mode_value = map[string]int32{
	"SINGLE":       0,
	"MULTI":        1,
	"SETUP_NUMBER": 2,
}

func (x GamePalBlock_Mode) String() string {
	return proto.EnumName(GamePalBlock_Mode_name, int32(x))
}
func (GamePalBlock_Mode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{0, 1}
}

type GamePalBlock struct {
	Id   uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// block类型
	Type GamePalBlock_Type `protobuf:"varint,3,opt,name=type,proto3,enum=game_pal.GamePalBlock_Type" json:"type,omitempty"`
	// 最多可选
	MostSelectNum        uint32         `protobuf:"varint,4,opt,name=most_select_num,json=mostSelectNum,proto3" json:"most_select_num,omitempty"`
	Elems                []*GamePalElem `protobuf:"bytes,5,rep,name=elems,proto3" json:"elems,omitempty"`
	Mode                 uint32         `protobuf:"varint,6,opt,name=mode,proto3" json:"mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GamePalBlock) Reset()         { *m = GamePalBlock{} }
func (m *GamePalBlock) String() string { return proto.CompactTextString(m) }
func (*GamePalBlock) ProtoMessage()    {}
func (*GamePalBlock) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{0}
}
func (m *GamePalBlock) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GamePalBlock.Unmarshal(m, b)
}
func (m *GamePalBlock) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GamePalBlock.Marshal(b, m, deterministic)
}
func (dst *GamePalBlock) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GamePalBlock.Merge(dst, src)
}
func (m *GamePalBlock) XXX_Size() int {
	return xxx_messageInfo_GamePalBlock.Size(m)
}
func (m *GamePalBlock) XXX_DiscardUnknown() {
	xxx_messageInfo_GamePalBlock.DiscardUnknown(m)
}

var xxx_messageInfo_GamePalBlock proto.InternalMessageInfo

func (m *GamePalBlock) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GamePalBlock) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GamePalBlock) GetType() GamePalBlock_Type {
	if m != nil {
		return m.Type
	}
	return GamePalBlock_TypeDefault
}

func (m *GamePalBlock) GetMostSelectNum() uint32 {
	if m != nil {
		return m.MostSelectNum
	}
	return 0
}

func (m *GamePalBlock) GetElems() []*GamePalElem {
	if m != nil {
		return m.Elems
	}
	return nil
}

func (m *GamePalBlock) GetMode() uint32 {
	if m != nil {
		return m.Mode
	}
	return 0
}

type GamePalElem struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GamePalElem) Reset()         { *m = GamePalElem{} }
func (m *GamePalElem) String() string { return proto.CompactTextString(m) }
func (*GamePalElem) ProtoMessage()    {}
func (*GamePalElem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{1}
}
func (m *GamePalElem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GamePalElem.Unmarshal(m, b)
}
func (m *GamePalElem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GamePalElem.Marshal(b, m, deterministic)
}
func (dst *GamePalElem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GamePalElem.Merge(dst, src)
}
func (m *GamePalElem) XXX_Size() int {
	return xxx_messageInfo_GamePalElem.Size(m)
}
func (m *GamePalElem) XXX_DiscardUnknown() {
	xxx_messageInfo_GamePalElem.DiscardUnknown(m)
}

var xxx_messageInfo_GamePalElem proto.InternalMessageInfo

func (m *GamePalElem) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GamePalElem) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// 游戏搭子卡信息
type GamePalCard struct {
	// 游戏搭子卡ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 游戏搭子卡所属用户
	Uid uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// 游戏玩法ID
	TabId uint32 `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	// 交友宣言
	SocialDecl string `protobuf:"bytes,4,opt,name=social_decl,json=socialDecl,proto3" json:"social_decl,omitempty"`
	// 图片 obs key
	ImageKeys []string `protobuf:"bytes,5,rep,name=image_keys,json=imageKeys,proto3" json:"image_keys,omitempty"`
	// 游戏搭子卡属性字段
	Props []*GamePalBlock `protobuf:"bytes,6,rep,name=props,proto3" json:"props,omitempty"`
	// 游戏搭子卡审核状态
	AuditState GamePalCardAuditState `protobuf:"varint,7,opt,name=audit_state,json=auditState,proto3,enum=game_pal.GamePalCardAuditState" json:"audit_state,omitempty"`
	// 游戏搭子卡状态
	State GamePalCardState `protobuf:"varint,8,opt,name=state,proto3,enum=game_pal.GamePalCardState" json:"state,omitempty"`
	// 游戏搭子卡当天已点亮次数
	LightenCount uint32 `protobuf:"varint,9,opt,name=lighten_count,json=lightenCount,proto3" json:"lighten_count,omitempty"`
	// 游戏搭子卡点亮时间
	LightenAt int64    `protobuf:"varint,10,opt,name=lighten_at,json=lightenAt,proto3" json:"lighten_at,omitempty"`
	ImageUrls []string `protobuf:"bytes,11,rep,name=image_urls,json=imageUrls,proto3" json:"image_urls,omitempty"`
	// 游戏搭子卡更新时间
	UpdatedAt int64 `protobuf:"varint,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// 游戏搭子卡送审时间
	AuditAt              int64    `protobuf:"varint,13,opt,name=audit_at,json=auditAt,proto3" json:"audit_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GamePalCard) Reset()         { *m = GamePalCard{} }
func (m *GamePalCard) String() string { return proto.CompactTextString(m) }
func (*GamePalCard) ProtoMessage()    {}
func (*GamePalCard) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{2}
}
func (m *GamePalCard) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GamePalCard.Unmarshal(m, b)
}
func (m *GamePalCard) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GamePalCard.Marshal(b, m, deterministic)
}
func (dst *GamePalCard) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GamePalCard.Merge(dst, src)
}
func (m *GamePalCard) XXX_Size() int {
	return xxx_messageInfo_GamePalCard.Size(m)
}
func (m *GamePalCard) XXX_DiscardUnknown() {
	xxx_messageInfo_GamePalCard.DiscardUnknown(m)
}

var xxx_messageInfo_GamePalCard proto.InternalMessageInfo

func (m *GamePalCard) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GamePalCard) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GamePalCard) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GamePalCard) GetSocialDecl() string {
	if m != nil {
		return m.SocialDecl
	}
	return ""
}

func (m *GamePalCard) GetImageKeys() []string {
	if m != nil {
		return m.ImageKeys
	}
	return nil
}

func (m *GamePalCard) GetProps() []*GamePalBlock {
	if m != nil {
		return m.Props
	}
	return nil
}

func (m *GamePalCard) GetAuditState() GamePalCardAuditState {
	if m != nil {
		return m.AuditState
	}
	return GamePalCardAuditState_GamePalCardAuditStateNone
}

func (m *GamePalCard) GetState() GamePalCardState {
	if m != nil {
		return m.State
	}
	return GamePalCardState_GamePalCardStateNone
}

func (m *GamePalCard) GetLightenCount() uint32 {
	if m != nil {
		return m.LightenCount
	}
	return 0
}

func (m *GamePalCard) GetLightenAt() int64 {
	if m != nil {
		return m.LightenAt
	}
	return 0
}

func (m *GamePalCard) GetImageUrls() []string {
	if m != nil {
		return m.ImageUrls
	}
	return nil
}

func (m *GamePalCard) GetUpdatedAt() int64 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

func (m *GamePalCard) GetAuditAt() int64 {
	if m != nil {
		return m.AuditAt
	}
	return 0
}

type UpsertGamePalCardReq struct {
	Card                 *GamePalCard `protobuf:"bytes,1,opt,name=card,proto3" json:"card,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpsertGamePalCardReq) Reset()         { *m = UpsertGamePalCardReq{} }
func (m *UpsertGamePalCardReq) String() string { return proto.CompactTextString(m) }
func (*UpsertGamePalCardReq) ProtoMessage()    {}
func (*UpsertGamePalCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{3}
}
func (m *UpsertGamePalCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertGamePalCardReq.Unmarshal(m, b)
}
func (m *UpsertGamePalCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertGamePalCardReq.Marshal(b, m, deterministic)
}
func (dst *UpsertGamePalCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertGamePalCardReq.Merge(dst, src)
}
func (m *UpsertGamePalCardReq) XXX_Size() int {
	return xxx_messageInfo_UpsertGamePalCardReq.Size(m)
}
func (m *UpsertGamePalCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertGamePalCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertGamePalCardReq proto.InternalMessageInfo

func (m *UpsertGamePalCardReq) GetCard() *GamePalCard {
	if m != nil {
		return m.Card
	}
	return nil
}

type UpsertGamePalCardResp struct {
	Card                 *GamePalCard `protobuf:"bytes,1,opt,name=card,proto3" json:"card,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpsertGamePalCardResp) Reset()         { *m = UpsertGamePalCardResp{} }
func (m *UpsertGamePalCardResp) String() string { return proto.CompactTextString(m) }
func (*UpsertGamePalCardResp) ProtoMessage()    {}
func (*UpsertGamePalCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{4}
}
func (m *UpsertGamePalCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertGamePalCardResp.Unmarshal(m, b)
}
func (m *UpsertGamePalCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertGamePalCardResp.Marshal(b, m, deterministic)
}
func (dst *UpsertGamePalCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertGamePalCardResp.Merge(dst, src)
}
func (m *UpsertGamePalCardResp) XXX_Size() int {
	return xxx_messageInfo_UpsertGamePalCardResp.Size(m)
}
func (m *UpsertGamePalCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertGamePalCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertGamePalCardResp proto.InternalMessageInfo

func (m *UpsertGamePalCardResp) GetCard() *GamePalCard {
	if m != nil {
		return m.Card
	}
	return nil
}

type DeleteGamePalCardReq struct {
	Card                 *GamePalCard `protobuf:"bytes,1,opt,name=card,proto3" json:"card,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DeleteGamePalCardReq) Reset()         { *m = DeleteGamePalCardReq{} }
func (m *DeleteGamePalCardReq) String() string { return proto.CompactTextString(m) }
func (*DeleteGamePalCardReq) ProtoMessage()    {}
func (*DeleteGamePalCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{5}
}
func (m *DeleteGamePalCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteGamePalCardReq.Unmarshal(m, b)
}
func (m *DeleteGamePalCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteGamePalCardReq.Marshal(b, m, deterministic)
}
func (dst *DeleteGamePalCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteGamePalCardReq.Merge(dst, src)
}
func (m *DeleteGamePalCardReq) XXX_Size() int {
	return xxx_messageInfo_DeleteGamePalCardReq.Size(m)
}
func (m *DeleteGamePalCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteGamePalCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteGamePalCardReq proto.InternalMessageInfo

func (m *DeleteGamePalCardReq) GetCard() *GamePalCard {
	if m != nil {
		return m.Card
	}
	return nil
}

type DeleteGamePalCardResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteGamePalCardResp) Reset()         { *m = DeleteGamePalCardResp{} }
func (m *DeleteGamePalCardResp) String() string { return proto.CompactTextString(m) }
func (*DeleteGamePalCardResp) ProtoMessage()    {}
func (*DeleteGamePalCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{6}
}
func (m *DeleteGamePalCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteGamePalCardResp.Unmarshal(m, b)
}
func (m *DeleteGamePalCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteGamePalCardResp.Marshal(b, m, deterministic)
}
func (dst *DeleteGamePalCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteGamePalCardResp.Merge(dst, src)
}
func (m *DeleteGamePalCardResp) XXX_Size() int {
	return xxx_messageInfo_DeleteGamePalCardResp.Size(m)
}
func (m *DeleteGamePalCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteGamePalCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteGamePalCardResp proto.InternalMessageInfo

type GetGamePalCardReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGamePalCardReq) Reset()         { *m = GetGamePalCardReq{} }
func (m *GetGamePalCardReq) String() string { return proto.CompactTextString(m) }
func (*GetGamePalCardReq) ProtoMessage()    {}
func (*GetGamePalCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{7}
}
func (m *GetGamePalCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGamePalCardReq.Unmarshal(m, b)
}
func (m *GetGamePalCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGamePalCardReq.Marshal(b, m, deterministic)
}
func (dst *GetGamePalCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGamePalCardReq.Merge(dst, src)
}
func (m *GetGamePalCardReq) XXX_Size() int {
	return xxx_messageInfo_GetGamePalCardReq.Size(m)
}
func (m *GetGamePalCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGamePalCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGamePalCardReq proto.InternalMessageInfo

func (m *GetGamePalCardReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type GetGamePalCardResp struct {
	Card                 *GamePalCard `protobuf:"bytes,1,opt,name=card,proto3" json:"card,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGamePalCardResp) Reset()         { *m = GetGamePalCardResp{} }
func (m *GetGamePalCardResp) String() string { return proto.CompactTextString(m) }
func (*GetGamePalCardResp) ProtoMessage()    {}
func (*GetGamePalCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{8}
}
func (m *GetGamePalCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGamePalCardResp.Unmarshal(m, b)
}
func (m *GetGamePalCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGamePalCardResp.Marshal(b, m, deterministic)
}
func (dst *GetGamePalCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGamePalCardResp.Merge(dst, src)
}
func (m *GetGamePalCardResp) XXX_Size() int {
	return xxx_messageInfo_GetGamePalCardResp.Size(m)
}
func (m *GetGamePalCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGamePalCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGamePalCardResp proto.InternalMessageInfo

func (m *GetGamePalCardResp) GetCard() *GamePalCard {
	if m != nil {
		return m.Card
	}
	return nil
}

type GetGamePalCardListReq struct {
	IdList               []string `protobuf:"bytes,1,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGamePalCardListReq) Reset()         { *m = GetGamePalCardListReq{} }
func (m *GetGamePalCardListReq) String() string { return proto.CompactTextString(m) }
func (*GetGamePalCardListReq) ProtoMessage()    {}
func (*GetGamePalCardListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{9}
}
func (m *GetGamePalCardListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGamePalCardListReq.Unmarshal(m, b)
}
func (m *GetGamePalCardListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGamePalCardListReq.Marshal(b, m, deterministic)
}
func (dst *GetGamePalCardListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGamePalCardListReq.Merge(dst, src)
}
func (m *GetGamePalCardListReq) XXX_Size() int {
	return xxx_messageInfo_GetGamePalCardListReq.Size(m)
}
func (m *GetGamePalCardListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGamePalCardListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGamePalCardListReq proto.InternalMessageInfo

func (m *GetGamePalCardListReq) GetIdList() []string {
	if m != nil {
		return m.IdList
	}
	return nil
}

type GetGamePalCardListResp struct {
	Cards                []*GamePalCard `protobuf:"bytes,1,rep,name=cards,proto3" json:"cards,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetGamePalCardListResp) Reset()         { *m = GetGamePalCardListResp{} }
func (m *GetGamePalCardListResp) String() string { return proto.CompactTextString(m) }
func (*GetGamePalCardListResp) ProtoMessage()    {}
func (*GetGamePalCardListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{10}
}
func (m *GetGamePalCardListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGamePalCardListResp.Unmarshal(m, b)
}
func (m *GetGamePalCardListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGamePalCardListResp.Marshal(b, m, deterministic)
}
func (dst *GetGamePalCardListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGamePalCardListResp.Merge(dst, src)
}
func (m *GetGamePalCardListResp) XXX_Size() int {
	return xxx_messageInfo_GetGamePalCardListResp.Size(m)
}
func (m *GetGamePalCardListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGamePalCardListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGamePalCardListResp proto.InternalMessageInfo

func (m *GetGamePalCardListResp) GetCards() []*GamePalCard {
	if m != nil {
		return m.Cards
	}
	return nil
}

type GetUserGamePalCardListReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 是否做限流
	Limiting             bool     `protobuf:"varint,2,opt,name=limiting,proto3" json:"limiting,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserGamePalCardListReq) Reset()         { *m = GetUserGamePalCardListReq{} }
func (m *GetUserGamePalCardListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserGamePalCardListReq) ProtoMessage()    {}
func (*GetUserGamePalCardListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{11}
}
func (m *GetUserGamePalCardListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGamePalCardListReq.Unmarshal(m, b)
}
func (m *GetUserGamePalCardListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGamePalCardListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserGamePalCardListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGamePalCardListReq.Merge(dst, src)
}
func (m *GetUserGamePalCardListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserGamePalCardListReq.Size(m)
}
func (m *GetUserGamePalCardListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGamePalCardListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGamePalCardListReq proto.InternalMessageInfo

func (m *GetUserGamePalCardListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserGamePalCardListReq) GetLimiting() bool {
	if m != nil {
		return m.Limiting
	}
	return false
}

type GetUserGamePalCardListResp struct {
	Cards                []*GamePalCard `protobuf:"bytes,1,rep,name=cards,proto3" json:"cards,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetUserGamePalCardListResp) Reset()         { *m = GetUserGamePalCardListResp{} }
func (m *GetUserGamePalCardListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserGamePalCardListResp) ProtoMessage()    {}
func (*GetUserGamePalCardListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{12}
}
func (m *GetUserGamePalCardListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGamePalCardListResp.Unmarshal(m, b)
}
func (m *GetUserGamePalCardListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGamePalCardListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserGamePalCardListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGamePalCardListResp.Merge(dst, src)
}
func (m *GetUserGamePalCardListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserGamePalCardListResp.Size(m)
}
func (m *GetUserGamePalCardListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGamePalCardListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGamePalCardListResp proto.InternalMessageInfo

func (m *GetUserGamePalCardListResp) GetCards() []*GamePalCard {
	if m != nil {
		return m.Cards
	}
	return nil
}

type GetUserGamePalCardReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserGamePalCardReq) Reset()         { *m = GetUserGamePalCardReq{} }
func (m *GetUserGamePalCardReq) String() string { return proto.CompactTextString(m) }
func (*GetUserGamePalCardReq) ProtoMessage()    {}
func (*GetUserGamePalCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{13}
}
func (m *GetUserGamePalCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGamePalCardReq.Unmarshal(m, b)
}
func (m *GetUserGamePalCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGamePalCardReq.Marshal(b, m, deterministic)
}
func (dst *GetUserGamePalCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGamePalCardReq.Merge(dst, src)
}
func (m *GetUserGamePalCardReq) XXX_Size() int {
	return xxx_messageInfo_GetUserGamePalCardReq.Size(m)
}
func (m *GetUserGamePalCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGamePalCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGamePalCardReq proto.InternalMessageInfo

func (m *GetUserGamePalCardReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserGamePalCardReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetUserGamePalCardResp struct {
	Card                 *GamePalCard `protobuf:"bytes,1,opt,name=card,proto3" json:"card,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserGamePalCardResp) Reset()         { *m = GetUserGamePalCardResp{} }
func (m *GetUserGamePalCardResp) String() string { return proto.CompactTextString(m) }
func (*GetUserGamePalCardResp) ProtoMessage()    {}
func (*GetUserGamePalCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{14}
}
func (m *GetUserGamePalCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGamePalCardResp.Unmarshal(m, b)
}
func (m *GetUserGamePalCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGamePalCardResp.Marshal(b, m, deterministic)
}
func (dst *GetUserGamePalCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGamePalCardResp.Merge(dst, src)
}
func (m *GetUserGamePalCardResp) XXX_Size() int {
	return xxx_messageInfo_GetUserGamePalCardResp.Size(m)
}
func (m *GetUserGamePalCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGamePalCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGamePalCardResp proto.InternalMessageInfo

func (m *GetUserGamePalCardResp) GetCard() *GamePalCard {
	if m != nil {
		return m.Card
	}
	return nil
}

type GetGamePalCardTipOffInfoReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGamePalCardTipOffInfoReq) Reset()         { *m = GetGamePalCardTipOffInfoReq{} }
func (m *GetGamePalCardTipOffInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetGamePalCardTipOffInfoReq) ProtoMessage()    {}
func (*GetGamePalCardTipOffInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{15}
}
func (m *GetGamePalCardTipOffInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGamePalCardTipOffInfoReq.Unmarshal(m, b)
}
func (m *GetGamePalCardTipOffInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGamePalCardTipOffInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetGamePalCardTipOffInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGamePalCardTipOffInfoReq.Merge(dst, src)
}
func (m *GetGamePalCardTipOffInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetGamePalCardTipOffInfoReq.Size(m)
}
func (m *GetGamePalCardTipOffInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGamePalCardTipOffInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGamePalCardTipOffInfoReq proto.InternalMessageInfo

func (m *GetGamePalCardTipOffInfoReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type GetGamePalCardTipOffInfoResp struct {
	SocialDecl           string   `protobuf:"bytes,1,opt,name=social_decl,json=socialDecl,proto3" json:"social_decl,omitempty"`
	ImageUrls            []string `protobuf:"bytes,2,rep,name=image_urls,json=imageUrls,proto3" json:"image_urls,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGamePalCardTipOffInfoResp) Reset()         { *m = GetGamePalCardTipOffInfoResp{} }
func (m *GetGamePalCardTipOffInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetGamePalCardTipOffInfoResp) ProtoMessage()    {}
func (*GetGamePalCardTipOffInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{16}
}
func (m *GetGamePalCardTipOffInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGamePalCardTipOffInfoResp.Unmarshal(m, b)
}
func (m *GetGamePalCardTipOffInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGamePalCardTipOffInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetGamePalCardTipOffInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGamePalCardTipOffInfoResp.Merge(dst, src)
}
func (m *GetGamePalCardTipOffInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetGamePalCardTipOffInfoResp.Size(m)
}
func (m *GetGamePalCardTipOffInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGamePalCardTipOffInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGamePalCardTipOffInfoResp proto.InternalMessageInfo

func (m *GetGamePalCardTipOffInfoResp) GetSocialDecl() string {
	if m != nil {
		return m.SocialDecl
	}
	return ""
}

func (m *GetGamePalCardTipOffInfoResp) GetImageUrls() []string {
	if m != nil {
		return m.ImageUrls
	}
	return nil
}

type BanGamePalCardReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BanGamePalCardReq) Reset()         { *m = BanGamePalCardReq{} }
func (m *BanGamePalCardReq) String() string { return proto.CompactTextString(m) }
func (*BanGamePalCardReq) ProtoMessage()    {}
func (*BanGamePalCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{17}
}
func (m *BanGamePalCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanGamePalCardReq.Unmarshal(m, b)
}
func (m *BanGamePalCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanGamePalCardReq.Marshal(b, m, deterministic)
}
func (dst *BanGamePalCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanGamePalCardReq.Merge(dst, src)
}
func (m *BanGamePalCardReq) XXX_Size() int {
	return xxx_messageInfo_BanGamePalCardReq.Size(m)
}
func (m *BanGamePalCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BanGamePalCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_BanGamePalCardReq proto.InternalMessageInfo

func (m *BanGamePalCardReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type BanGamePalCardResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BanGamePalCardResp) Reset()         { *m = BanGamePalCardResp{} }
func (m *BanGamePalCardResp) String() string { return proto.CompactTextString(m) }
func (*BanGamePalCardResp) ProtoMessage()    {}
func (*BanGamePalCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{18}
}
func (m *BanGamePalCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanGamePalCardResp.Unmarshal(m, b)
}
func (m *BanGamePalCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanGamePalCardResp.Marshal(b, m, deterministic)
}
func (dst *BanGamePalCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanGamePalCardResp.Merge(dst, src)
}
func (m *BanGamePalCardResp) XXX_Size() int {
	return xxx_messageInfo_BanGamePalCardResp.Size(m)
}
func (m *BanGamePalCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BanGamePalCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_BanGamePalCardResp proto.InternalMessageInfo

type GamePalCardOption struct {
	GamePalBlockId       uint32   `protobuf:"varint,1,opt,name=game_pal_block_id,json=gamePalBlockId,proto3" json:"game_pal_block_id,omitempty"`
	GamePalElemId        uint32   `protobuf:"varint,2,opt,name=game_pal_elem_id,json=gamePalElemId,proto3" json:"game_pal_elem_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GamePalCardOption) Reset()         { *m = GamePalCardOption{} }
func (m *GamePalCardOption) String() string { return proto.CompactTextString(m) }
func (*GamePalCardOption) ProtoMessage()    {}
func (*GamePalCardOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{19}
}
func (m *GamePalCardOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GamePalCardOption.Unmarshal(m, b)
}
func (m *GamePalCardOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GamePalCardOption.Marshal(b, m, deterministic)
}
func (dst *GamePalCardOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GamePalCardOption.Merge(dst, src)
}
func (m *GamePalCardOption) XXX_Size() int {
	return xxx_messageInfo_GamePalCardOption.Size(m)
}
func (m *GamePalCardOption) XXX_DiscardUnknown() {
	xxx_messageInfo_GamePalCardOption.DiscardUnknown(m)
}

var xxx_messageInfo_GamePalCardOption proto.InternalMessageInfo

func (m *GamePalCardOption) GetGamePalBlockId() uint32 {
	if m != nil {
		return m.GamePalBlockId
	}
	return 0
}

func (m *GamePalCardOption) GetGamePalElemId() uint32 {
	if m != nil {
		return m.GamePalElemId
	}
	return 0
}

// 搭子卡事件
type GamePalCardEvent struct {
	EventType            EventType                    `protobuf:"varint,1,opt,name=event_type,json=eventType,proto3,enum=game_pal.EventType" json:"event_type,omitempty"`
	CardInfos            []*GamePalCardEvent_CardInfo `protobuf:"bytes,3,rep,name=card_infos,json=cardInfos,proto3" json:"card_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GamePalCardEvent) Reset()         { *m = GamePalCardEvent{} }
func (m *GamePalCardEvent) String() string { return proto.CompactTextString(m) }
func (*GamePalCardEvent) ProtoMessage()    {}
func (*GamePalCardEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{20}
}
func (m *GamePalCardEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GamePalCardEvent.Unmarshal(m, b)
}
func (m *GamePalCardEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GamePalCardEvent.Marshal(b, m, deterministic)
}
func (dst *GamePalCardEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GamePalCardEvent.Merge(dst, src)
}
func (m *GamePalCardEvent) XXX_Size() int {
	return xxx_messageInfo_GamePalCardEvent.Size(m)
}
func (m *GamePalCardEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_GamePalCardEvent.DiscardUnknown(m)
}

var xxx_messageInfo_GamePalCardEvent proto.InternalMessageInfo

func (m *GamePalCardEvent) GetEventType() EventType {
	if m != nil {
		return m.EventType
	}
	return EventType_TYPE_INVALID_EVENT
}

func (m *GamePalCardEvent) GetCardInfos() []*GamePalCardEvent_CardInfo {
	if m != nil {
		return m.CardInfos
	}
	return nil
}

type GamePalCardEvent_CardInfo struct {
	PalCardId            string               `protobuf:"bytes,1,opt,name=pal_card_id,json=palCardId,proto3" json:"pal_card_id,omitempty"`
	LightDuration        int64                `protobuf:"varint,2,opt,name=light_duration,json=lightDuration,proto3" json:"light_duration,omitempty"`
	Options              []*GamePalCardOption `protobuf:"bytes,3,rep,name=options,proto3" json:"options,omitempty"`
	Declaration          string               `protobuf:"bytes,4,opt,name=declaration,proto3" json:"declaration,omitempty"`
	UpdateTime           int64                `protobuf:"varint,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	TabId                uint32               `protobuf:"varint,6,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Uid                  uint32               `protobuf:"varint,7,opt,name=uid,proto3" json:"uid,omitempty"`
	NoLimitBlockIds      []uint32             `protobuf:"varint,8,rep,packed,name=no_limit_block_ids,json=noLimitBlockIds,proto3" json:"no_limit_block_ids,omitempty"`
	ImageKeys            []string             `protobuf:"bytes,9,rep,name=image_keys,json=imageKeys,proto3" json:"image_keys,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GamePalCardEvent_CardInfo) Reset()         { *m = GamePalCardEvent_CardInfo{} }
func (m *GamePalCardEvent_CardInfo) String() string { return proto.CompactTextString(m) }
func (*GamePalCardEvent_CardInfo) ProtoMessage()    {}
func (*GamePalCardEvent_CardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{20, 0}
}
func (m *GamePalCardEvent_CardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GamePalCardEvent_CardInfo.Unmarshal(m, b)
}
func (m *GamePalCardEvent_CardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GamePalCardEvent_CardInfo.Marshal(b, m, deterministic)
}
func (dst *GamePalCardEvent_CardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GamePalCardEvent_CardInfo.Merge(dst, src)
}
func (m *GamePalCardEvent_CardInfo) XXX_Size() int {
	return xxx_messageInfo_GamePalCardEvent_CardInfo.Size(m)
}
func (m *GamePalCardEvent_CardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GamePalCardEvent_CardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GamePalCardEvent_CardInfo proto.InternalMessageInfo

func (m *GamePalCardEvent_CardInfo) GetPalCardId() string {
	if m != nil {
		return m.PalCardId
	}
	return ""
}

func (m *GamePalCardEvent_CardInfo) GetLightDuration() int64 {
	if m != nil {
		return m.LightDuration
	}
	return 0
}

func (m *GamePalCardEvent_CardInfo) GetOptions() []*GamePalCardOption {
	if m != nil {
		return m.Options
	}
	return nil
}

func (m *GamePalCardEvent_CardInfo) GetDeclaration() string {
	if m != nil {
		return m.Declaration
	}
	return ""
}

func (m *GamePalCardEvent_CardInfo) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *GamePalCardEvent_CardInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GamePalCardEvent_CardInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GamePalCardEvent_CardInfo) GetNoLimitBlockIds() []uint32 {
	if m != nil {
		return m.NoLimitBlockIds
	}
	return nil
}

func (m *GamePalCardEvent_CardInfo) GetImageKeys() []string {
	if m != nil {
		return m.ImageKeys
	}
	return nil
}

type BatchPolishGamePalCardReq struct {
	GamePalCards         []*GamePalCard `protobuf:"bytes,1,rep,name=game_pal_cards,json=gamePalCards,proto3" json:"game_pal_cards,omitempty"`
	PolishType           PolishType     `protobuf:"varint,2,opt,name=polish_type,json=polishType,proto3,enum=game_pal.PolishType" json:"polish_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatchPolishGamePalCardReq) Reset()         { *m = BatchPolishGamePalCardReq{} }
func (m *BatchPolishGamePalCardReq) String() string { return proto.CompactTextString(m) }
func (*BatchPolishGamePalCardReq) ProtoMessage()    {}
func (*BatchPolishGamePalCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{21}
}
func (m *BatchPolishGamePalCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchPolishGamePalCardReq.Unmarshal(m, b)
}
func (m *BatchPolishGamePalCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchPolishGamePalCardReq.Marshal(b, m, deterministic)
}
func (dst *BatchPolishGamePalCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchPolishGamePalCardReq.Merge(dst, src)
}
func (m *BatchPolishGamePalCardReq) XXX_Size() int {
	return xxx_messageInfo_BatchPolishGamePalCardReq.Size(m)
}
func (m *BatchPolishGamePalCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchPolishGamePalCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchPolishGamePalCardReq proto.InternalMessageInfo

func (m *BatchPolishGamePalCardReq) GetGamePalCards() []*GamePalCard {
	if m != nil {
		return m.GamePalCards
	}
	return nil
}

func (m *BatchPolishGamePalCardReq) GetPolishType() PolishType {
	if m != nil {
		return m.PolishType
	}
	return PolishType_INVALID_POLISH_TYPE
}

type BatchPolishGamePalCardResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchPolishGamePalCardResp) Reset()         { *m = BatchPolishGamePalCardResp{} }
func (m *BatchPolishGamePalCardResp) String() string { return proto.CompactTextString(m) }
func (*BatchPolishGamePalCardResp) ProtoMessage()    {}
func (*BatchPolishGamePalCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{22}
}
func (m *BatchPolishGamePalCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchPolishGamePalCardResp.Unmarshal(m, b)
}
func (m *BatchPolishGamePalCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchPolishGamePalCardResp.Marshal(b, m, deterministic)
}
func (dst *BatchPolishGamePalCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchPolishGamePalCardResp.Merge(dst, src)
}
func (m *BatchPolishGamePalCardResp) XXX_Size() int {
	return xxx_messageInfo_BatchPolishGamePalCardResp.Size(m)
}
func (m *BatchPolishGamePalCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchPolishGamePalCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchPolishGamePalCardResp proto.InternalMessageInfo

// 运营后台接口
// 插入、更新筛选项/发布项
type UpsertGamePalReleaseConditionReq struct {
	TabId                uint32          `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	GamePalBlocks        []*GamePalBlock `protobuf:"bytes,2,rep,name=game_pal_blocks,json=gamePalBlocks,proto3" json:"game_pal_blocks,omitempty"`
	LightEffectiveTime   int64           `protobuf:"varint,3,opt,name=light_effective_time,json=lightEffectiveTime,proto3" json:"light_effective_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UpsertGamePalReleaseConditionReq) Reset()         { *m = UpsertGamePalReleaseConditionReq{} }
func (m *UpsertGamePalReleaseConditionReq) String() string { return proto.CompactTextString(m) }
func (*UpsertGamePalReleaseConditionReq) ProtoMessage()    {}
func (*UpsertGamePalReleaseConditionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{23}
}
func (m *UpsertGamePalReleaseConditionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertGamePalReleaseConditionReq.Unmarshal(m, b)
}
func (m *UpsertGamePalReleaseConditionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertGamePalReleaseConditionReq.Marshal(b, m, deterministic)
}
func (dst *UpsertGamePalReleaseConditionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertGamePalReleaseConditionReq.Merge(dst, src)
}
func (m *UpsertGamePalReleaseConditionReq) XXX_Size() int {
	return xxx_messageInfo_UpsertGamePalReleaseConditionReq.Size(m)
}
func (m *UpsertGamePalReleaseConditionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertGamePalReleaseConditionReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertGamePalReleaseConditionReq proto.InternalMessageInfo

func (m *UpsertGamePalReleaseConditionReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *UpsertGamePalReleaseConditionReq) GetGamePalBlocks() []*GamePalBlock {
	if m != nil {
		return m.GamePalBlocks
	}
	return nil
}

func (m *UpsertGamePalReleaseConditionReq) GetLightEffectiveTime() int64 {
	if m != nil {
		return m.LightEffectiveTime
	}
	return 0
}

type UpsertGamePalReleaseConditionResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertGamePalReleaseConditionResp) Reset()         { *m = UpsertGamePalReleaseConditionResp{} }
func (m *UpsertGamePalReleaseConditionResp) String() string { return proto.CompactTextString(m) }
func (*UpsertGamePalReleaseConditionResp) ProtoMessage()    {}
func (*UpsertGamePalReleaseConditionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{24}
}
func (m *UpsertGamePalReleaseConditionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertGamePalReleaseConditionResp.Unmarshal(m, b)
}
func (m *UpsertGamePalReleaseConditionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertGamePalReleaseConditionResp.Marshal(b, m, deterministic)
}
func (dst *UpsertGamePalReleaseConditionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertGamePalReleaseConditionResp.Merge(dst, src)
}
func (m *UpsertGamePalReleaseConditionResp) XXX_Size() int {
	return xxx_messageInfo_UpsertGamePalReleaseConditionResp.Size(m)
}
func (m *UpsertGamePalReleaseConditionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertGamePalReleaseConditionResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertGamePalReleaseConditionResp proto.InternalMessageInfo

// 获取指定玩法id的搭子卡筛选项/发布项
type BatchGetGamePalReleaseConditionReq struct {
	TabIds               []uint32 `protobuf:"varint,1,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	Source               string   `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	NoUseCache           bool     `protobuf:"varint,3,opt,name=no_use_cache,json=noUseCache,proto3" json:"no_use_cache,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetGamePalReleaseConditionReq) Reset()         { *m = BatchGetGamePalReleaseConditionReq{} }
func (m *BatchGetGamePalReleaseConditionReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetGamePalReleaseConditionReq) ProtoMessage()    {}
func (*BatchGetGamePalReleaseConditionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{25}
}
func (m *BatchGetGamePalReleaseConditionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGamePalReleaseConditionReq.Unmarshal(m, b)
}
func (m *BatchGetGamePalReleaseConditionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGamePalReleaseConditionReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetGamePalReleaseConditionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGamePalReleaseConditionReq.Merge(dst, src)
}
func (m *BatchGetGamePalReleaseConditionReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetGamePalReleaseConditionReq.Size(m)
}
func (m *BatchGetGamePalReleaseConditionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGamePalReleaseConditionReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGamePalReleaseConditionReq proto.InternalMessageInfo

func (m *BatchGetGamePalReleaseConditionReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *BatchGetGamePalReleaseConditionReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *BatchGetGamePalReleaseConditionReq) GetNoUseCache() bool {
	if m != nil {
		return m.NoUseCache
	}
	return false
}

type BatchGetGamePalReleaseConditionResp struct {
	// key 玩法tab_id value 玩法绑定的搭子卡筛选项
	FilterItemMap        map[uint32]*BatchGetGamePalReleaseConditionResp_TabGamePalBlocks `protobuf:"bytes,1,rep,name=filter_item_map,json=filterItemMap,proto3" json:"filter_item_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                                         `json:"-"`
	XXX_unrecognized     []byte                                                           `json:"-"`
	XXX_sizecache        int32                                                            `json:"-"`
}

func (m *BatchGetGamePalReleaseConditionResp) Reset()         { *m = BatchGetGamePalReleaseConditionResp{} }
func (m *BatchGetGamePalReleaseConditionResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetGamePalReleaseConditionResp) ProtoMessage()    {}
func (*BatchGetGamePalReleaseConditionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{26}
}
func (m *BatchGetGamePalReleaseConditionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGamePalReleaseConditionResp.Unmarshal(m, b)
}
func (m *BatchGetGamePalReleaseConditionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGamePalReleaseConditionResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetGamePalReleaseConditionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGamePalReleaseConditionResp.Merge(dst, src)
}
func (m *BatchGetGamePalReleaseConditionResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetGamePalReleaseConditionResp.Size(m)
}
func (m *BatchGetGamePalReleaseConditionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGamePalReleaseConditionResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGamePalReleaseConditionResp proto.InternalMessageInfo

func (m *BatchGetGamePalReleaseConditionResp) GetFilterItemMap() map[uint32]*BatchGetGamePalReleaseConditionResp_TabGamePalBlocks {
	if m != nil {
		return m.FilterItemMap
	}
	return nil
}

type BatchGetGamePalReleaseConditionResp_TabGamePalBlocks struct {
	GamePalBlocks        []*GamePalBlock `protobuf:"bytes,1,rep,name=game_pal_blocks,json=gamePalBlocks,proto3" json:"game_pal_blocks,omitempty"`
	LightEffectiveTime   int64           `protobuf:"varint,2,opt,name=light_effective_time,json=lightEffectiveTime,proto3" json:"light_effective_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchGetGamePalReleaseConditionResp_TabGamePalBlocks) Reset() {
	*m = BatchGetGamePalReleaseConditionResp_TabGamePalBlocks{}
}
func (m *BatchGetGamePalReleaseConditionResp_TabGamePalBlocks) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetGamePalReleaseConditionResp_TabGamePalBlocks) ProtoMessage() {}
func (*BatchGetGamePalReleaseConditionResp_TabGamePalBlocks) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{26, 0}
}
func (m *BatchGetGamePalReleaseConditionResp_TabGamePalBlocks) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGamePalReleaseConditionResp_TabGamePalBlocks.Unmarshal(m, b)
}
func (m *BatchGetGamePalReleaseConditionResp_TabGamePalBlocks) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGamePalReleaseConditionResp_TabGamePalBlocks.Marshal(b, m, deterministic)
}
func (dst *BatchGetGamePalReleaseConditionResp_TabGamePalBlocks) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGamePalReleaseConditionResp_TabGamePalBlocks.Merge(dst, src)
}
func (m *BatchGetGamePalReleaseConditionResp_TabGamePalBlocks) XXX_Size() int {
	return xxx_messageInfo_BatchGetGamePalReleaseConditionResp_TabGamePalBlocks.Size(m)
}
func (m *BatchGetGamePalReleaseConditionResp_TabGamePalBlocks) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGamePalReleaseConditionResp_TabGamePalBlocks.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGamePalReleaseConditionResp_TabGamePalBlocks proto.InternalMessageInfo

func (m *BatchGetGamePalReleaseConditionResp_TabGamePalBlocks) GetGamePalBlocks() []*GamePalBlock {
	if m != nil {
		return m.GamePalBlocks
	}
	return nil
}

func (m *BatchGetGamePalReleaseConditionResp_TabGamePalBlocks) GetLightEffectiveTime() int64 {
	if m != nil {
		return m.LightEffectiveTime
	}
	return 0
}

// 交友宣言文案库
type SocialDeclConfig struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TypeName             string   `protobuf:"bytes,2,opt,name=type_name,json=typeName,proto3" json:"type_name,omitempty"`
	SocialDecls          []string `protobuf:"bytes,3,rep,name=social_decls,json=socialDecls,proto3" json:"social_decls,omitempty"`
	TabIds               []uint32 `protobuf:"varint,4,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	UpdateTs             int64    `protobuf:"varint,5,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialDeclConfig) Reset()         { *m = SocialDeclConfig{} }
func (m *SocialDeclConfig) String() string { return proto.CompactTextString(m) }
func (*SocialDeclConfig) ProtoMessage()    {}
func (*SocialDeclConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{27}
}
func (m *SocialDeclConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialDeclConfig.Unmarshal(m, b)
}
func (m *SocialDeclConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialDeclConfig.Marshal(b, m, deterministic)
}
func (dst *SocialDeclConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialDeclConfig.Merge(dst, src)
}
func (m *SocialDeclConfig) XXX_Size() int {
	return xxx_messageInfo_SocialDeclConfig.Size(m)
}
func (m *SocialDeclConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialDeclConfig.DiscardUnknown(m)
}

var xxx_messageInfo_SocialDeclConfig proto.InternalMessageInfo

func (m *SocialDeclConfig) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SocialDeclConfig) GetTypeName() string {
	if m != nil {
		return m.TypeName
	}
	return ""
}

func (m *SocialDeclConfig) GetSocialDecls() []string {
	if m != nil {
		return m.SocialDecls
	}
	return nil
}

func (m *SocialDeclConfig) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *SocialDeclConfig) GetUpdateTs() int64 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

type UpsertSocialDeclConfigReq struct {
	Config               *SocialDeclConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UpsertSocialDeclConfigReq) Reset()         { *m = UpsertSocialDeclConfigReq{} }
func (m *UpsertSocialDeclConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpsertSocialDeclConfigReq) ProtoMessage()    {}
func (*UpsertSocialDeclConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{28}
}
func (m *UpsertSocialDeclConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertSocialDeclConfigReq.Unmarshal(m, b)
}
func (m *UpsertSocialDeclConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertSocialDeclConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpsertSocialDeclConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertSocialDeclConfigReq.Merge(dst, src)
}
func (m *UpsertSocialDeclConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpsertSocialDeclConfigReq.Size(m)
}
func (m *UpsertSocialDeclConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertSocialDeclConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertSocialDeclConfigReq proto.InternalMessageInfo

func (m *UpsertSocialDeclConfigReq) GetConfig() *SocialDeclConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type UpsertSocialDeclConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertSocialDeclConfigResp) Reset()         { *m = UpsertSocialDeclConfigResp{} }
func (m *UpsertSocialDeclConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpsertSocialDeclConfigResp) ProtoMessage()    {}
func (*UpsertSocialDeclConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{29}
}
func (m *UpsertSocialDeclConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertSocialDeclConfigResp.Unmarshal(m, b)
}
func (m *UpsertSocialDeclConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertSocialDeclConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpsertSocialDeclConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertSocialDeclConfigResp.Merge(dst, src)
}
func (m *UpsertSocialDeclConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpsertSocialDeclConfigResp.Size(m)
}
func (m *UpsertSocialDeclConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertSocialDeclConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertSocialDeclConfigResp proto.InternalMessageInfo

type GetSocialDeclConfigsReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size                 uint32   `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	NeedCount            bool     `protobuf:"varint,3,opt,name=need_count,json=needCount,proto3" json:"need_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSocialDeclConfigsReq) Reset()         { *m = GetSocialDeclConfigsReq{} }
func (m *GetSocialDeclConfigsReq) String() string { return proto.CompactTextString(m) }
func (*GetSocialDeclConfigsReq) ProtoMessage()    {}
func (*GetSocialDeclConfigsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{30}
}
func (m *GetSocialDeclConfigsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialDeclConfigsReq.Unmarshal(m, b)
}
func (m *GetSocialDeclConfigsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialDeclConfigsReq.Marshal(b, m, deterministic)
}
func (dst *GetSocialDeclConfigsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialDeclConfigsReq.Merge(dst, src)
}
func (m *GetSocialDeclConfigsReq) XXX_Size() int {
	return xxx_messageInfo_GetSocialDeclConfigsReq.Size(m)
}
func (m *GetSocialDeclConfigsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialDeclConfigsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialDeclConfigsReq proto.InternalMessageInfo

func (m *GetSocialDeclConfigsReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetSocialDeclConfigsReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetSocialDeclConfigsReq) GetNeedCount() bool {
	if m != nil {
		return m.NeedCount
	}
	return false
}

type GetSocialDeclConfigsResp struct {
	Configs              []*SocialDeclConfig `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	Total                uint32              `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetSocialDeclConfigsResp) Reset()         { *m = GetSocialDeclConfigsResp{} }
func (m *GetSocialDeclConfigsResp) String() string { return proto.CompactTextString(m) }
func (*GetSocialDeclConfigsResp) ProtoMessage()    {}
func (*GetSocialDeclConfigsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{31}
}
func (m *GetSocialDeclConfigsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialDeclConfigsResp.Unmarshal(m, b)
}
func (m *GetSocialDeclConfigsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialDeclConfigsResp.Marshal(b, m, deterministic)
}
func (dst *GetSocialDeclConfigsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialDeclConfigsResp.Merge(dst, src)
}
func (m *GetSocialDeclConfigsResp) XXX_Size() int {
	return xxx_messageInfo_GetSocialDeclConfigsResp.Size(m)
}
func (m *GetSocialDeclConfigsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialDeclConfigsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialDeclConfigsResp proto.InternalMessageInfo

func (m *GetSocialDeclConfigsResp) GetConfigs() []*SocialDeclConfig {
	if m != nil {
		return m.Configs
	}
	return nil
}

func (m *GetSocialDeclConfigsResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DelSocialDeclConfigReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSocialDeclConfigReq) Reset()         { *m = DelSocialDeclConfigReq{} }
func (m *DelSocialDeclConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelSocialDeclConfigReq) ProtoMessage()    {}
func (*DelSocialDeclConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{32}
}
func (m *DelSocialDeclConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSocialDeclConfigReq.Unmarshal(m, b)
}
func (m *DelSocialDeclConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSocialDeclConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelSocialDeclConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSocialDeclConfigReq.Merge(dst, src)
}
func (m *DelSocialDeclConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelSocialDeclConfigReq.Size(m)
}
func (m *DelSocialDeclConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSocialDeclConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelSocialDeclConfigReq proto.InternalMessageInfo

func (m *DelSocialDeclConfigReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DelSocialDeclConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSocialDeclConfigResp) Reset()         { *m = DelSocialDeclConfigResp{} }
func (m *DelSocialDeclConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelSocialDeclConfigResp) ProtoMessage()    {}
func (*DelSocialDeclConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{33}
}
func (m *DelSocialDeclConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSocialDeclConfigResp.Unmarshal(m, b)
}
func (m *DelSocialDeclConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSocialDeclConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelSocialDeclConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSocialDeclConfigResp.Merge(dst, src)
}
func (m *DelSocialDeclConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelSocialDeclConfigResp.Size(m)
}
func (m *DelSocialDeclConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSocialDeclConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelSocialDeclConfigResp proto.InternalMessageInfo

type DeleteGamePalReleaseConditionReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteGamePalReleaseConditionReq) Reset()         { *m = DeleteGamePalReleaseConditionReq{} }
func (m *DeleteGamePalReleaseConditionReq) String() string { return proto.CompactTextString(m) }
func (*DeleteGamePalReleaseConditionReq) ProtoMessage()    {}
func (*DeleteGamePalReleaseConditionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{34}
}
func (m *DeleteGamePalReleaseConditionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteGamePalReleaseConditionReq.Unmarshal(m, b)
}
func (m *DeleteGamePalReleaseConditionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteGamePalReleaseConditionReq.Marshal(b, m, deterministic)
}
func (dst *DeleteGamePalReleaseConditionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteGamePalReleaseConditionReq.Merge(dst, src)
}
func (m *DeleteGamePalReleaseConditionReq) XXX_Size() int {
	return xxx_messageInfo_DeleteGamePalReleaseConditionReq.Size(m)
}
func (m *DeleteGamePalReleaseConditionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteGamePalReleaseConditionReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteGamePalReleaseConditionReq proto.InternalMessageInfo

func (m *DeleteGamePalReleaseConditionReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type DeleteGamePalReleaseConditionResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteGamePalReleaseConditionResp) Reset()         { *m = DeleteGamePalReleaseConditionResp{} }
func (m *DeleteGamePalReleaseConditionResp) String() string { return proto.CompactTextString(m) }
func (*DeleteGamePalReleaseConditionResp) ProtoMessage()    {}
func (*DeleteGamePalReleaseConditionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{35}
}
func (m *DeleteGamePalReleaseConditionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteGamePalReleaseConditionResp.Unmarshal(m, b)
}
func (m *DeleteGamePalReleaseConditionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteGamePalReleaseConditionResp.Marshal(b, m, deterministic)
}
func (dst *DeleteGamePalReleaseConditionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteGamePalReleaseConditionResp.Merge(dst, src)
}
func (m *DeleteGamePalReleaseConditionResp) XXX_Size() int {
	return xxx_messageInfo_DeleteGamePalReleaseConditionResp.Size(m)
}
func (m *DeleteGamePalReleaseConditionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteGamePalReleaseConditionResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteGamePalReleaseConditionResp proto.InternalMessageInfo

type BatchExtinctGamePalCardReq struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PalCardInfos         []*GamePalCard `protobuf:"bytes,2,rep,name=pal_card_infos,json=palCardInfos,proto3" json:"pal_card_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatchExtinctGamePalCardReq) Reset()         { *m = BatchExtinctGamePalCardReq{} }
func (m *BatchExtinctGamePalCardReq) String() string { return proto.CompactTextString(m) }
func (*BatchExtinctGamePalCardReq) ProtoMessage()    {}
func (*BatchExtinctGamePalCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{36}
}
func (m *BatchExtinctGamePalCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchExtinctGamePalCardReq.Unmarshal(m, b)
}
func (m *BatchExtinctGamePalCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchExtinctGamePalCardReq.Marshal(b, m, deterministic)
}
func (dst *BatchExtinctGamePalCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchExtinctGamePalCardReq.Merge(dst, src)
}
func (m *BatchExtinctGamePalCardReq) XXX_Size() int {
	return xxx_messageInfo_BatchExtinctGamePalCardReq.Size(m)
}
func (m *BatchExtinctGamePalCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchExtinctGamePalCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchExtinctGamePalCardReq proto.InternalMessageInfo

func (m *BatchExtinctGamePalCardReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchExtinctGamePalCardReq) GetPalCardInfos() []*GamePalCard {
	if m != nil {
		return m.PalCardInfos
	}
	return nil
}

type BatchExtinctGamePalCardResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchExtinctGamePalCardResp) Reset()         { *m = BatchExtinctGamePalCardResp{} }
func (m *BatchExtinctGamePalCardResp) String() string { return proto.CompactTextString(m) }
func (*BatchExtinctGamePalCardResp) ProtoMessage()    {}
func (*BatchExtinctGamePalCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{37}
}
func (m *BatchExtinctGamePalCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchExtinctGamePalCardResp.Unmarshal(m, b)
}
func (m *BatchExtinctGamePalCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchExtinctGamePalCardResp.Marshal(b, m, deterministic)
}
func (dst *BatchExtinctGamePalCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchExtinctGamePalCardResp.Merge(dst, src)
}
func (m *BatchExtinctGamePalCardResp) XXX_Size() int {
	return xxx_messageInfo_BatchExtinctGamePalCardResp.Size(m)
}
func (m *BatchExtinctGamePalCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchExtinctGamePalCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchExtinctGamePalCardResp proto.InternalMessageInfo

// 卡片编辑、送审事件
type SendEditCardEventReq struct {
	Card                 *GamePalCard `protobuf:"bytes,1,opt,name=card,proto3" json:"card,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SendEditCardEventReq) Reset()         { *m = SendEditCardEventReq{} }
func (m *SendEditCardEventReq) String() string { return proto.CompactTextString(m) }
func (*SendEditCardEventReq) ProtoMessage()    {}
func (*SendEditCardEventReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{38}
}
func (m *SendEditCardEventReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendEditCardEventReq.Unmarshal(m, b)
}
func (m *SendEditCardEventReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendEditCardEventReq.Marshal(b, m, deterministic)
}
func (dst *SendEditCardEventReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendEditCardEventReq.Merge(dst, src)
}
func (m *SendEditCardEventReq) XXX_Size() int {
	return xxx_messageInfo_SendEditCardEventReq.Size(m)
}
func (m *SendEditCardEventReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendEditCardEventReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendEditCardEventReq proto.InternalMessageInfo

func (m *SendEditCardEventReq) GetCard() *GamePalCard {
	if m != nil {
		return m.Card
	}
	return nil
}

type SendEditCardEventResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendEditCardEventResp) Reset()         { *m = SendEditCardEventResp{} }
func (m *SendEditCardEventResp) String() string { return proto.CompactTextString(m) }
func (*SendEditCardEventResp) ProtoMessage()    {}
func (*SendEditCardEventResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{39}
}
func (m *SendEditCardEventResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendEditCardEventResp.Unmarshal(m, b)
}
func (m *SendEditCardEventResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendEditCardEventResp.Marshal(b, m, deterministic)
}
func (dst *SendEditCardEventResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendEditCardEventResp.Merge(dst, src)
}
func (m *SendEditCardEventResp) XXX_Size() int {
	return xxx_messageInfo_SendEditCardEventResp.Size(m)
}
func (m *SendEditCardEventResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendEditCardEventResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendEditCardEventResp proto.InternalMessageInfo

type GetTabPolishedCardCountMapReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTabPolishedCardCountMapReq) Reset()         { *m = GetTabPolishedCardCountMapReq{} }
func (m *GetTabPolishedCardCountMapReq) String() string { return proto.CompactTextString(m) }
func (*GetTabPolishedCardCountMapReq) ProtoMessage()    {}
func (*GetTabPolishedCardCountMapReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{40}
}
func (m *GetTabPolishedCardCountMapReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabPolishedCardCountMapReq.Unmarshal(m, b)
}
func (m *GetTabPolishedCardCountMapReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabPolishedCardCountMapReq.Marshal(b, m, deterministic)
}
func (dst *GetTabPolishedCardCountMapReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabPolishedCardCountMapReq.Merge(dst, src)
}
func (m *GetTabPolishedCardCountMapReq) XXX_Size() int {
	return xxx_messageInfo_GetTabPolishedCardCountMapReq.Size(m)
}
func (m *GetTabPolishedCardCountMapReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabPolishedCardCountMapReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabPolishedCardCountMapReq proto.InternalMessageInfo

type GetTabPolishedCardCountMapResp struct {
	TabPolishedCardCountMap map[uint32]uint32 `protobuf:"bytes,1,rep,name=tab_polished_card_count_map,json=tabPolishedCardCountMap,proto3" json:"tab_polished_card_count_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral    struct{}          `json:"-"`
	XXX_unrecognized        []byte            `json:"-"`
	XXX_sizecache           int32             `json:"-"`
}

func (m *GetTabPolishedCardCountMapResp) Reset()         { *m = GetTabPolishedCardCountMapResp{} }
func (m *GetTabPolishedCardCountMapResp) String() string { return proto.CompactTextString(m) }
func (*GetTabPolishedCardCountMapResp) ProtoMessage()    {}
func (*GetTabPolishedCardCountMapResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_pal_b4893208fe8ffc77, []int{41}
}
func (m *GetTabPolishedCardCountMapResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabPolishedCardCountMapResp.Unmarshal(m, b)
}
func (m *GetTabPolishedCardCountMapResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabPolishedCardCountMapResp.Marshal(b, m, deterministic)
}
func (dst *GetTabPolishedCardCountMapResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabPolishedCardCountMapResp.Merge(dst, src)
}
func (m *GetTabPolishedCardCountMapResp) XXX_Size() int {
	return xxx_messageInfo_GetTabPolishedCardCountMapResp.Size(m)
}
func (m *GetTabPolishedCardCountMapResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabPolishedCardCountMapResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabPolishedCardCountMapResp proto.InternalMessageInfo

func (m *GetTabPolishedCardCountMapResp) GetTabPolishedCardCountMap() map[uint32]uint32 {
	if m != nil {
		return m.TabPolishedCardCountMap
	}
	return nil
}

func init() {
	proto.RegisterType((*GamePalBlock)(nil), "game_pal.GamePalBlock")
	proto.RegisterType((*GamePalElem)(nil), "game_pal.GamePalElem")
	proto.RegisterType((*GamePalCard)(nil), "game_pal.GamePalCard")
	proto.RegisterType((*UpsertGamePalCardReq)(nil), "game_pal.UpsertGamePalCardReq")
	proto.RegisterType((*UpsertGamePalCardResp)(nil), "game_pal.UpsertGamePalCardResp")
	proto.RegisterType((*DeleteGamePalCardReq)(nil), "game_pal.DeleteGamePalCardReq")
	proto.RegisterType((*DeleteGamePalCardResp)(nil), "game_pal.DeleteGamePalCardResp")
	proto.RegisterType((*GetGamePalCardReq)(nil), "game_pal.GetGamePalCardReq")
	proto.RegisterType((*GetGamePalCardResp)(nil), "game_pal.GetGamePalCardResp")
	proto.RegisterType((*GetGamePalCardListReq)(nil), "game_pal.GetGamePalCardListReq")
	proto.RegisterType((*GetGamePalCardListResp)(nil), "game_pal.GetGamePalCardListResp")
	proto.RegisterType((*GetUserGamePalCardListReq)(nil), "game_pal.GetUserGamePalCardListReq")
	proto.RegisterType((*GetUserGamePalCardListResp)(nil), "game_pal.GetUserGamePalCardListResp")
	proto.RegisterType((*GetUserGamePalCardReq)(nil), "game_pal.GetUserGamePalCardReq")
	proto.RegisterType((*GetUserGamePalCardResp)(nil), "game_pal.GetUserGamePalCardResp")
	proto.RegisterType((*GetGamePalCardTipOffInfoReq)(nil), "game_pal.GetGamePalCardTipOffInfoReq")
	proto.RegisterType((*GetGamePalCardTipOffInfoResp)(nil), "game_pal.GetGamePalCardTipOffInfoResp")
	proto.RegisterType((*BanGamePalCardReq)(nil), "game_pal.BanGamePalCardReq")
	proto.RegisterType((*BanGamePalCardResp)(nil), "game_pal.BanGamePalCardResp")
	proto.RegisterType((*GamePalCardOption)(nil), "game_pal.GamePalCardOption")
	proto.RegisterType((*GamePalCardEvent)(nil), "game_pal.GamePalCardEvent")
	proto.RegisterType((*GamePalCardEvent_CardInfo)(nil), "game_pal.GamePalCardEvent.CardInfo")
	proto.RegisterType((*BatchPolishGamePalCardReq)(nil), "game_pal.BatchPolishGamePalCardReq")
	proto.RegisterType((*BatchPolishGamePalCardResp)(nil), "game_pal.BatchPolishGamePalCardResp")
	proto.RegisterType((*UpsertGamePalReleaseConditionReq)(nil), "game_pal.UpsertGamePalReleaseConditionReq")
	proto.RegisterType((*UpsertGamePalReleaseConditionResp)(nil), "game_pal.UpsertGamePalReleaseConditionResp")
	proto.RegisterType((*BatchGetGamePalReleaseConditionReq)(nil), "game_pal.BatchGetGamePalReleaseConditionReq")
	proto.RegisterType((*BatchGetGamePalReleaseConditionResp)(nil), "game_pal.BatchGetGamePalReleaseConditionResp")
	proto.RegisterMapType((map[uint32]*BatchGetGamePalReleaseConditionResp_TabGamePalBlocks)(nil), "game_pal.BatchGetGamePalReleaseConditionResp.FilterItemMapEntry")
	proto.RegisterType((*BatchGetGamePalReleaseConditionResp_TabGamePalBlocks)(nil), "game_pal.BatchGetGamePalReleaseConditionResp.TabGamePalBlocks")
	proto.RegisterType((*SocialDeclConfig)(nil), "game_pal.SocialDeclConfig")
	proto.RegisterType((*UpsertSocialDeclConfigReq)(nil), "game_pal.UpsertSocialDeclConfigReq")
	proto.RegisterType((*UpsertSocialDeclConfigResp)(nil), "game_pal.UpsertSocialDeclConfigResp")
	proto.RegisterType((*GetSocialDeclConfigsReq)(nil), "game_pal.GetSocialDeclConfigsReq")
	proto.RegisterType((*GetSocialDeclConfigsResp)(nil), "game_pal.GetSocialDeclConfigsResp")
	proto.RegisterType((*DelSocialDeclConfigReq)(nil), "game_pal.DelSocialDeclConfigReq")
	proto.RegisterType((*DelSocialDeclConfigResp)(nil), "game_pal.DelSocialDeclConfigResp")
	proto.RegisterType((*DeleteGamePalReleaseConditionReq)(nil), "game_pal.DeleteGamePalReleaseConditionReq")
	proto.RegisterType((*DeleteGamePalReleaseConditionResp)(nil), "game_pal.DeleteGamePalReleaseConditionResp")
	proto.RegisterType((*BatchExtinctGamePalCardReq)(nil), "game_pal.BatchExtinctGamePalCardReq")
	proto.RegisterType((*BatchExtinctGamePalCardResp)(nil), "game_pal.BatchExtinctGamePalCardResp")
	proto.RegisterType((*SendEditCardEventReq)(nil), "game_pal.SendEditCardEventReq")
	proto.RegisterType((*SendEditCardEventResp)(nil), "game_pal.SendEditCardEventResp")
	proto.RegisterType((*GetTabPolishedCardCountMapReq)(nil), "game_pal.GetTabPolishedCardCountMapReq")
	proto.RegisterType((*GetTabPolishedCardCountMapResp)(nil), "game_pal.GetTabPolishedCardCountMapResp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "game_pal.GetTabPolishedCardCountMapResp.TabPolishedCardCountMapEntry")
	proto.RegisterEnum("game_pal.GamePalCardAuditState", GamePalCardAuditState_name, GamePalCardAuditState_value)
	proto.RegisterEnum("game_pal.GamePalCardState", GamePalCardState_name, GamePalCardState_value)
	proto.RegisterEnum("game_pal.EventType", EventType_name, EventType_value)
	proto.RegisterEnum("game_pal.DeclarationType", DeclarationType_name, DeclarationType_value)
	proto.RegisterEnum("game_pal.PolishType", PolishType_name, PolishType_value)
	proto.RegisterEnum("game_pal.GamePalBlock_Type", GamePalBlock_Type_name, GamePalBlock_Type_value)
	proto.RegisterEnum("game_pal.GamePalBlock_Mode", GamePalBlock_Mode_name, GamePalBlock_Mode_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GamePalClient is the client API for GamePal service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GamePalClient interface {
	// 游戏搭子卡管理
	UpsertGamePalCard(ctx context.Context, in *UpsertGamePalCardReq, opts ...grpc.CallOption) (*UpsertGamePalCardResp, error)
	DeleteGamePalCard(ctx context.Context, in *DeleteGamePalCardReq, opts ...grpc.CallOption) (*DeleteGamePalCardResp, error)
	GetGamePalCard(ctx context.Context, in *GetGamePalCardReq, opts ...grpc.CallOption) (*GetGamePalCardResp, error)
	GetGamePalCardList(ctx context.Context, in *GetGamePalCardListReq, opts ...grpc.CallOption) (*GetGamePalCardListResp, error)
	GetUserGamePalCardList(ctx context.Context, in *GetUserGamePalCardListReq, opts ...grpc.CallOption) (*GetUserGamePalCardListResp, error)
	GetUserGamePalCard(ctx context.Context, in *GetUserGamePalCardReq, opts ...grpc.CallOption) (*GetUserGamePalCardResp, error)
	// 游戏搭子卡举报
	BanGamePalCard(ctx context.Context, in *BanGamePalCardReq, opts ...grpc.CallOption) (*BanGamePalCardResp, error)
	// 搭子卡擦亮
	BatchPolishGamePalCard(ctx context.Context, in *BatchPolishGamePalCardReq, opts ...grpc.CallOption) (*BatchPolishGamePalCardResp, error)
	// 搭子卡熄灭,展示不需要
	BatchExtinctGamePalCard(ctx context.Context, in *BatchExtinctGamePalCardReq, opts ...grpc.CallOption) (*BatchExtinctGamePalCardResp, error)
	// 运营后台接口
	// 插入、更新发布条件
	UpsertGamePalReleaseCondition(ctx context.Context, in *UpsertGamePalReleaseConditionReq, opts ...grpc.CallOption) (*UpsertGamePalReleaseConditionResp, error)
	// 获取指定玩法id的搭子发布条件
	BatchGetGamePalReleaseCondition(ctx context.Context, in *BatchGetGamePalReleaseConditionReq, opts ...grpc.CallOption) (*BatchGetGamePalReleaseConditionResp, error)
	// 删除指定玩法搭子卡发布条件，删除tab时调用
	DeleteGamePalReleaseCondition(ctx context.Context, in *DeleteGamePalReleaseConditionReq, opts ...grpc.CallOption) (*DeleteGamePalReleaseConditionResp, error)
	// 创建/更新交友宣言配置
	UpsertSocialDeclConfig(ctx context.Context, in *UpsertSocialDeclConfigReq, opts ...grpc.CallOption) (*UpsertSocialDeclConfigResp, error)
	// 删除交友宣言配置
	DelSocialDeclConfig(ctx context.Context, in *DelSocialDeclConfigReq, opts ...grpc.CallOption) (*DelSocialDeclConfigResp, error)
	// 获取交友宣言配置列表
	GetSocialDeclConfigs(ctx context.Context, in *GetSocialDeclConfigsReq, opts ...grpc.CallOption) (*GetSocialDeclConfigsResp, error)
	// 获取交友宣言配置列表
	SendEditCardEvent(ctx context.Context, in *SendEditCardEventReq, opts ...grpc.CallOption) (*SendEditCardEventResp, error)
	// 机器人接口
	// 获取玩法当前擦亮中的搭子卡数量
	GetTabPolishedCardCountMap(ctx context.Context, in *GetTabPolishedCardCountMapReq, opts ...grpc.CallOption) (*GetTabPolishedCardCountMapResp, error)
}

type gamePalClient struct {
	cc *grpc.ClientConn
}

func NewGamePalClient(cc *grpc.ClientConn) GamePalClient {
	return &gamePalClient{cc}
}

func (c *gamePalClient) UpsertGamePalCard(ctx context.Context, in *UpsertGamePalCardReq, opts ...grpc.CallOption) (*UpsertGamePalCardResp, error) {
	out := new(UpsertGamePalCardResp)
	err := c.cc.Invoke(ctx, "/game_pal.GamePal/UpsertGamePalCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalClient) DeleteGamePalCard(ctx context.Context, in *DeleteGamePalCardReq, opts ...grpc.CallOption) (*DeleteGamePalCardResp, error) {
	out := new(DeleteGamePalCardResp)
	err := c.cc.Invoke(ctx, "/game_pal.GamePal/DeleteGamePalCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalClient) GetGamePalCard(ctx context.Context, in *GetGamePalCardReq, opts ...grpc.CallOption) (*GetGamePalCardResp, error) {
	out := new(GetGamePalCardResp)
	err := c.cc.Invoke(ctx, "/game_pal.GamePal/GetGamePalCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalClient) GetGamePalCardList(ctx context.Context, in *GetGamePalCardListReq, opts ...grpc.CallOption) (*GetGamePalCardListResp, error) {
	out := new(GetGamePalCardListResp)
	err := c.cc.Invoke(ctx, "/game_pal.GamePal/GetGamePalCardList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalClient) GetUserGamePalCardList(ctx context.Context, in *GetUserGamePalCardListReq, opts ...grpc.CallOption) (*GetUserGamePalCardListResp, error) {
	out := new(GetUserGamePalCardListResp)
	err := c.cc.Invoke(ctx, "/game_pal.GamePal/GetUserGamePalCardList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalClient) GetUserGamePalCard(ctx context.Context, in *GetUserGamePalCardReq, opts ...grpc.CallOption) (*GetUserGamePalCardResp, error) {
	out := new(GetUserGamePalCardResp)
	err := c.cc.Invoke(ctx, "/game_pal.GamePal/GetUserGamePalCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalClient) BanGamePalCard(ctx context.Context, in *BanGamePalCardReq, opts ...grpc.CallOption) (*BanGamePalCardResp, error) {
	out := new(BanGamePalCardResp)
	err := c.cc.Invoke(ctx, "/game_pal.GamePal/BanGamePalCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalClient) BatchPolishGamePalCard(ctx context.Context, in *BatchPolishGamePalCardReq, opts ...grpc.CallOption) (*BatchPolishGamePalCardResp, error) {
	out := new(BatchPolishGamePalCardResp)
	err := c.cc.Invoke(ctx, "/game_pal.GamePal/BatchPolishGamePalCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalClient) BatchExtinctGamePalCard(ctx context.Context, in *BatchExtinctGamePalCardReq, opts ...grpc.CallOption) (*BatchExtinctGamePalCardResp, error) {
	out := new(BatchExtinctGamePalCardResp)
	err := c.cc.Invoke(ctx, "/game_pal.GamePal/BatchExtinctGamePalCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalClient) UpsertGamePalReleaseCondition(ctx context.Context, in *UpsertGamePalReleaseConditionReq, opts ...grpc.CallOption) (*UpsertGamePalReleaseConditionResp, error) {
	out := new(UpsertGamePalReleaseConditionResp)
	err := c.cc.Invoke(ctx, "/game_pal.GamePal/UpsertGamePalReleaseCondition", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalClient) BatchGetGamePalReleaseCondition(ctx context.Context, in *BatchGetGamePalReleaseConditionReq, opts ...grpc.CallOption) (*BatchGetGamePalReleaseConditionResp, error) {
	out := new(BatchGetGamePalReleaseConditionResp)
	err := c.cc.Invoke(ctx, "/game_pal.GamePal/BatchGetGamePalReleaseCondition", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalClient) DeleteGamePalReleaseCondition(ctx context.Context, in *DeleteGamePalReleaseConditionReq, opts ...grpc.CallOption) (*DeleteGamePalReleaseConditionResp, error) {
	out := new(DeleteGamePalReleaseConditionResp)
	err := c.cc.Invoke(ctx, "/game_pal.GamePal/DeleteGamePalReleaseCondition", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalClient) UpsertSocialDeclConfig(ctx context.Context, in *UpsertSocialDeclConfigReq, opts ...grpc.CallOption) (*UpsertSocialDeclConfigResp, error) {
	out := new(UpsertSocialDeclConfigResp)
	err := c.cc.Invoke(ctx, "/game_pal.GamePal/UpsertSocialDeclConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalClient) DelSocialDeclConfig(ctx context.Context, in *DelSocialDeclConfigReq, opts ...grpc.CallOption) (*DelSocialDeclConfigResp, error) {
	out := new(DelSocialDeclConfigResp)
	err := c.cc.Invoke(ctx, "/game_pal.GamePal/DelSocialDeclConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalClient) GetSocialDeclConfigs(ctx context.Context, in *GetSocialDeclConfigsReq, opts ...grpc.CallOption) (*GetSocialDeclConfigsResp, error) {
	out := new(GetSocialDeclConfigsResp)
	err := c.cc.Invoke(ctx, "/game_pal.GamePal/GetSocialDeclConfigs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalClient) SendEditCardEvent(ctx context.Context, in *SendEditCardEventReq, opts ...grpc.CallOption) (*SendEditCardEventResp, error) {
	out := new(SendEditCardEventResp)
	err := c.cc.Invoke(ctx, "/game_pal.GamePal/SendEditCardEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalClient) GetTabPolishedCardCountMap(ctx context.Context, in *GetTabPolishedCardCountMapReq, opts ...grpc.CallOption) (*GetTabPolishedCardCountMapResp, error) {
	out := new(GetTabPolishedCardCountMapResp)
	err := c.cc.Invoke(ctx, "/game_pal.GamePal/GetTabPolishedCardCountMap", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GamePalServer is the server API for GamePal service.
type GamePalServer interface {
	// 游戏搭子卡管理
	UpsertGamePalCard(context.Context, *UpsertGamePalCardReq) (*UpsertGamePalCardResp, error)
	DeleteGamePalCard(context.Context, *DeleteGamePalCardReq) (*DeleteGamePalCardResp, error)
	GetGamePalCard(context.Context, *GetGamePalCardReq) (*GetGamePalCardResp, error)
	GetGamePalCardList(context.Context, *GetGamePalCardListReq) (*GetGamePalCardListResp, error)
	GetUserGamePalCardList(context.Context, *GetUserGamePalCardListReq) (*GetUserGamePalCardListResp, error)
	GetUserGamePalCard(context.Context, *GetUserGamePalCardReq) (*GetUserGamePalCardResp, error)
	// 游戏搭子卡举报
	BanGamePalCard(context.Context, *BanGamePalCardReq) (*BanGamePalCardResp, error)
	// 搭子卡擦亮
	BatchPolishGamePalCard(context.Context, *BatchPolishGamePalCardReq) (*BatchPolishGamePalCardResp, error)
	// 搭子卡熄灭,展示不需要
	BatchExtinctGamePalCard(context.Context, *BatchExtinctGamePalCardReq) (*BatchExtinctGamePalCardResp, error)
	// 运营后台接口
	// 插入、更新发布条件
	UpsertGamePalReleaseCondition(context.Context, *UpsertGamePalReleaseConditionReq) (*UpsertGamePalReleaseConditionResp, error)
	// 获取指定玩法id的搭子发布条件
	BatchGetGamePalReleaseCondition(context.Context, *BatchGetGamePalReleaseConditionReq) (*BatchGetGamePalReleaseConditionResp, error)
	// 删除指定玩法搭子卡发布条件，删除tab时调用
	DeleteGamePalReleaseCondition(context.Context, *DeleteGamePalReleaseConditionReq) (*DeleteGamePalReleaseConditionResp, error)
	// 创建/更新交友宣言配置
	UpsertSocialDeclConfig(context.Context, *UpsertSocialDeclConfigReq) (*UpsertSocialDeclConfigResp, error)
	// 删除交友宣言配置
	DelSocialDeclConfig(context.Context, *DelSocialDeclConfigReq) (*DelSocialDeclConfigResp, error)
	// 获取交友宣言配置列表
	GetSocialDeclConfigs(context.Context, *GetSocialDeclConfigsReq) (*GetSocialDeclConfigsResp, error)
	// 获取交友宣言配置列表
	SendEditCardEvent(context.Context, *SendEditCardEventReq) (*SendEditCardEventResp, error)
	// 机器人接口
	// 获取玩法当前擦亮中的搭子卡数量
	GetTabPolishedCardCountMap(context.Context, *GetTabPolishedCardCountMapReq) (*GetTabPolishedCardCountMapResp, error)
}

func RegisterGamePalServer(s *grpc.Server, srv GamePalServer) {
	s.RegisterService(&_GamePal_serviceDesc, srv)
}

func _GamePal_UpsertGamePalCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertGamePalCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalServer).UpsertGamePalCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_pal.GamePal/UpsertGamePalCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalServer).UpsertGamePalCard(ctx, req.(*UpsertGamePalCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePal_DeleteGamePalCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGamePalCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalServer).DeleteGamePalCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_pal.GamePal/DeleteGamePalCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalServer).DeleteGamePalCard(ctx, req.(*DeleteGamePalCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePal_GetGamePalCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGamePalCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalServer).GetGamePalCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_pal.GamePal/GetGamePalCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalServer).GetGamePalCard(ctx, req.(*GetGamePalCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePal_GetGamePalCardList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGamePalCardListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalServer).GetGamePalCardList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_pal.GamePal/GetGamePalCardList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalServer).GetGamePalCardList(ctx, req.(*GetGamePalCardListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePal_GetUserGamePalCardList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserGamePalCardListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalServer).GetUserGamePalCardList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_pal.GamePal/GetUserGamePalCardList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalServer).GetUserGamePalCardList(ctx, req.(*GetUserGamePalCardListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePal_GetUserGamePalCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserGamePalCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalServer).GetUserGamePalCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_pal.GamePal/GetUserGamePalCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalServer).GetUserGamePalCard(ctx, req.(*GetUserGamePalCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePal_BanGamePalCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BanGamePalCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalServer).BanGamePalCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_pal.GamePal/BanGamePalCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalServer).BanGamePalCard(ctx, req.(*BanGamePalCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePal_BatchPolishGamePalCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchPolishGamePalCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalServer).BatchPolishGamePalCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_pal.GamePal/BatchPolishGamePalCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalServer).BatchPolishGamePalCard(ctx, req.(*BatchPolishGamePalCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePal_BatchExtinctGamePalCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchExtinctGamePalCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalServer).BatchExtinctGamePalCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_pal.GamePal/BatchExtinctGamePalCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalServer).BatchExtinctGamePalCard(ctx, req.(*BatchExtinctGamePalCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePal_UpsertGamePalReleaseCondition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertGamePalReleaseConditionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalServer).UpsertGamePalReleaseCondition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_pal.GamePal/UpsertGamePalReleaseCondition",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalServer).UpsertGamePalReleaseCondition(ctx, req.(*UpsertGamePalReleaseConditionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePal_BatchGetGamePalReleaseCondition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGamePalReleaseConditionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalServer).BatchGetGamePalReleaseCondition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_pal.GamePal/BatchGetGamePalReleaseCondition",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalServer).BatchGetGamePalReleaseCondition(ctx, req.(*BatchGetGamePalReleaseConditionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePal_DeleteGamePalReleaseCondition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGamePalReleaseConditionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalServer).DeleteGamePalReleaseCondition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_pal.GamePal/DeleteGamePalReleaseCondition",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalServer).DeleteGamePalReleaseCondition(ctx, req.(*DeleteGamePalReleaseConditionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePal_UpsertSocialDeclConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertSocialDeclConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalServer).UpsertSocialDeclConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_pal.GamePal/UpsertSocialDeclConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalServer).UpsertSocialDeclConfig(ctx, req.(*UpsertSocialDeclConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePal_DelSocialDeclConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelSocialDeclConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalServer).DelSocialDeclConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_pal.GamePal/DelSocialDeclConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalServer).DelSocialDeclConfig(ctx, req.(*DelSocialDeclConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePal_GetSocialDeclConfigs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSocialDeclConfigsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalServer).GetSocialDeclConfigs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_pal.GamePal/GetSocialDeclConfigs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalServer).GetSocialDeclConfigs(ctx, req.(*GetSocialDeclConfigsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePal_SendEditCardEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendEditCardEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalServer).SendEditCardEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_pal.GamePal/SendEditCardEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalServer).SendEditCardEvent(ctx, req.(*SendEditCardEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePal_GetTabPolishedCardCountMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTabPolishedCardCountMapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalServer).GetTabPolishedCardCountMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_pal.GamePal/GetTabPolishedCardCountMap",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalServer).GetTabPolishedCardCountMap(ctx, req.(*GetTabPolishedCardCountMapReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GamePal_serviceDesc = grpc.ServiceDesc{
	ServiceName: "game_pal.GamePal",
	HandlerType: (*GamePalServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpsertGamePalCard",
			Handler:    _GamePal_UpsertGamePalCard_Handler,
		},
		{
			MethodName: "DeleteGamePalCard",
			Handler:    _GamePal_DeleteGamePalCard_Handler,
		},
		{
			MethodName: "GetGamePalCard",
			Handler:    _GamePal_GetGamePalCard_Handler,
		},
		{
			MethodName: "GetGamePalCardList",
			Handler:    _GamePal_GetGamePalCardList_Handler,
		},
		{
			MethodName: "GetUserGamePalCardList",
			Handler:    _GamePal_GetUserGamePalCardList_Handler,
		},
		{
			MethodName: "GetUserGamePalCard",
			Handler:    _GamePal_GetUserGamePalCard_Handler,
		},
		{
			MethodName: "BanGamePalCard",
			Handler:    _GamePal_BanGamePalCard_Handler,
		},
		{
			MethodName: "BatchPolishGamePalCard",
			Handler:    _GamePal_BatchPolishGamePalCard_Handler,
		},
		{
			MethodName: "BatchExtinctGamePalCard",
			Handler:    _GamePal_BatchExtinctGamePalCard_Handler,
		},
		{
			MethodName: "UpsertGamePalReleaseCondition",
			Handler:    _GamePal_UpsertGamePalReleaseCondition_Handler,
		},
		{
			MethodName: "BatchGetGamePalReleaseCondition",
			Handler:    _GamePal_BatchGetGamePalReleaseCondition_Handler,
		},
		{
			MethodName: "DeleteGamePalReleaseCondition",
			Handler:    _GamePal_DeleteGamePalReleaseCondition_Handler,
		},
		{
			MethodName: "UpsertSocialDeclConfig",
			Handler:    _GamePal_UpsertSocialDeclConfig_Handler,
		},
		{
			MethodName: "DelSocialDeclConfig",
			Handler:    _GamePal_DelSocialDeclConfig_Handler,
		},
		{
			MethodName: "GetSocialDeclConfigs",
			Handler:    _GamePal_GetSocialDeclConfigs_Handler,
		},
		{
			MethodName: "SendEditCardEvent",
			Handler:    _GamePal_SendEditCardEvent_Handler,
		},
		{
			MethodName: "GetTabPolishedCardCountMap",
			Handler:    _GamePal_GetTabPolishedCardCountMap_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "game-pal/game-pal.proto",
}

func init() { proto.RegisterFile("game-pal/game-pal.proto", fileDescriptor_game_pal_b4893208fe8ffc77) }

var fileDescriptor_game_pal_b4893208fe8ffc77 = []byte{
	// 2164 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x39, 0xdd, 0x72, 0xe2, 0xc8,
	0xd5, 0x08, 0xcc, 0xdf, 0xc1, 0xd8, 0x9a, 0x1e, 0x3c, 0x60, 0x18, 0x8f, 0x19, 0xf9, 0xdb, 0x6f,
	0xbd, 0x9e, 0x19, 0x7b, 0xe2, 0x64, 0xaa, 0x92, 0x6c, 0x65, 0x33, 0x18, 0x18, 0x2f, 0x09, 0xfe,
	0x29, 0x19, 0x4f, 0xed, 0xa6, 0x52, 0x51, 0x64, 0xd4, 0x60, 0xc5, 0x42, 0x92, 0xe9, 0xc6, 0x1b,
	0xef, 0xd5, 0xa6, 0x92, 0xfb, 0x5c, 0x26, 0x79, 0x86, 0x5c, 0xed, 0x1b, 0xe4, 0x26, 0x97, 0x79,
	0x99, 0x3c, 0x41, 0xaa, 0x5b, 0x12, 0x08, 0xd1, 0x80, 0x9d, 0xca, 0x95, 0x5b, 0xa7, 0xcf, 0x39,
	0x7d, 0xfe, 0x7f, 0x30, 0x14, 0xfb, 0xfa, 0x00, 0xbf, 0x71, 0x75, 0xeb, 0x20, 0x38, 0xec, 0xbb,
	0x43, 0x87, 0x3a, 0x28, 0xc3, 0xbe, 0x35, 0x57, 0xb7, 0x94, 0xef, 0xe3, 0xb0, 0x7a, 0xac, 0x0f,
	0xf0, 0xb9, 0x6e, 0x1d, 0x59, 0x4e, 0xf7, 0x06, 0xad, 0x41, 0xdc, 0x34, 0x4a, 0x52, 0x55, 0xda,
	0xcd, 0xab, 0x71, 0xd3, 0x40, 0x08, 0x56, 0x6c, 0x7d, 0x80, 0x4b, 0xf1, 0xaa, 0xb4, 0x9b, 0x55,
	0xf9, 0x19, 0x1d, 0xc0, 0x0a, 0xbd, 0x77, 0x71, 0x29, 0x51, 0x95, 0x76, 0xd7, 0x0e, 0x2b, 0xfb,
	0x01, 0xb7, 0xfd, 0x30, 0xa7, 0xfd, 0xce, 0xbd, 0x8b, 0x55, 0x8e, 0x88, 0xfe, 0x1f, 0xd6, 0x07,
	0x0e, 0xa1, 0x1a, 0xc1, 0x16, 0xee, 0x52, 0xcd, 0x1e, 0x0d, 0x4a, 0x2b, 0xfc, 0x85, 0x3c, 0x03,
	0x5f, 0x70, 0xe8, 0xe9, 0x68, 0x80, 0x5e, 0x41, 0x12, 0x5b, 0x78, 0x40, 0x4a, 0xc9, 0x6a, 0x62,
	0x37, 0x77, 0xb8, 0x31, 0xc3, 0xb9, 0x69, 0xe1, 0x81, 0xea, 0xe1, 0x30, 0xc9, 0x06, 0x8e, 0x81,
	0x4b, 0x29, 0xce, 0x89, 0x9f, 0x95, 0x43, 0x58, 0x61, 0xcf, 0xa2, 0x75, 0xc8, 0xb1, 0xbf, 0x0d,
	0xdc, 0xd3, 0x47, 0x16, 0x95, 0x63, 0x28, 0x07, 0xe9, 0xf3, 0xd1, 0xd0, 0x75, 0x08, 0x96, 0x25,
	0xb4, 0x0a, 0x99, 0x73, 0x4b, 0xbf, 0xef, 0x98, 0x03, 0x2c, 0xc7, 0x95, 0x03, 0x58, 0x39, 0x71,
	0x0c, 0x8c, 0x00, 0x52, 0x17, 0xad, 0xd3, 0xe3, 0x76, 0x53, 0x8e, 0xa1, 0x2c, 0x24, 0x4f, 0x2e,
	0xdb, 0x9d, 0x96, 0x2c, 0x21, 0x19, 0x56, 0x2f, 0x9a, 0x9d, 0xcb, 0x73, 0xed, 0xf4, 0xf2, 0xe4,
	0xa8, 0xa9, 0xca, 0x71, 0xe5, 0x07, 0x90, 0x0b, 0x89, 0xf3, 0x10, 0x8b, 0x29, 0xff, 0x4c, 0x8c,
	0x69, 0xea, 0xfa, 0xd0, 0x08, 0xd1, 0x64, 0x39, 0x8d, 0x0c, 0x89, 0x91, 0x69, 0x70, 0x92, 0xbc,
	0xca, 0x8e, 0x68, 0x03, 0x52, 0x54, 0xbf, 0xd2, 0x4c, 0x83, 0x5b, 0x39, 0xaf, 0x26, 0xa9, 0x7e,
	0xd5, 0x32, 0xd0, 0x36, 0xe4, 0x88, 0xd3, 0x35, 0x75, 0x4b, 0x33, 0x70, 0xd7, 0xe2, 0x56, 0xcc,
	0xaa, 0xe0, 0x81, 0x1a, 0xb8, 0x6b, 0xa1, 0x2d, 0x00, 0x73, 0xa0, 0xf7, 0xb1, 0x76, 0x83, 0xef,
	0x3d, 0x3b, 0x66, 0xd5, 0x2c, 0x87, 0xfc, 0x12, 0xdf, 0x13, 0xf4, 0x1a, 0x92, 0xee, 0xd0, 0x71,
	0x49, 0x29, 0xc5, 0x2d, 0xfc, 0x4c, 0xec, 0x3b, 0xd5, 0x43, 0x42, 0xef, 0x21, 0xa7, 0x8f, 0x0c,
	0x93, 0x6a, 0x84, 0xea, 0x14, 0x97, 0xd2, 0xdc, 0xdf, 0xdb, 0x33, 0x34, 0x4c, 0xa5, 0x1a, 0xc3,
	0xbb, 0x60, 0x68, 0x2a, 0xe8, 0xe3, 0x33, 0x7a, 0x0b, 0x49, 0x8f, 0x36, 0xc3, 0x69, 0xcb, 0x42,
	0x5a, 0x8f, 0xcc, 0x43, 0x44, 0x3b, 0x90, 0xb7, 0xcc, 0xfe, 0x35, 0xc5, 0xb6, 0xd6, 0x75, 0x46,
	0x36, 0x2d, 0x65, 0xb9, 0xfe, 0xab, 0x3e, 0xb0, 0xce, 0x60, 0x4c, 0xcb, 0x00, 0x49, 0xa7, 0x25,
	0xa8, 0x4a, 0xbb, 0x09, 0x35, 0xeb, 0x43, 0x6a, 0x74, 0x62, 0x84, 0xd1, 0xd0, 0x22, 0xa5, 0x5c,
	0xc8, 0x08, 0x97, 0x43, 0x8b, 0xb0, 0xeb, 0x91, 0x6b, 0xe8, 0x14, 0x1b, 0x8c, 0x7a, 0xd5, 0xa3,
	0xf6, 0x21, 0x35, 0x8a, 0x36, 0x21, 0xe3, 0x69, 0xad, 0xd3, 0x52, 0x9e, 0x5f, 0xa6, 0xf9, 0x77,
	0x8d, 0x2a, 0x35, 0x28, 0x5c, 0xba, 0x04, 0x0f, 0x69, 0x48, 0x7a, 0x15, 0xdf, 0xa2, 0xcf, 0x60,
	0xa5, 0xab, 0x0f, 0x3d, 0x8f, 0x8a, 0xe2, 0x96, 0xe3, 0x71, 0x14, 0xe5, 0x08, 0x36, 0x04, 0x2c,
	0x88, 0xfb, 0x18, 0x1e, 0x35, 0x28, 0x34, 0xb0, 0x85, 0x29, 0xfe, 0xef, 0xc5, 0x28, 0xc2, 0x86,
	0x80, 0x05, 0x71, 0x95, 0x1d, 0x78, 0x72, 0x8c, 0xa3, 0xfa, 0x45, 0xe2, 0x55, 0xf9, 0x39, 0xa0,
	0x28, 0xd2, 0xe3, 0x34, 0x78, 0x0b, 0x1b, 0xd3, 0x0c, 0xda, 0x26, 0xa1, 0xec, 0xa5, 0x22, 0xa4,
	0x4d, 0x43, 0xb3, 0x4c, 0x42, 0x4b, 0x12, 0xf7, 0x5b, 0xca, 0xe4, 0x77, 0x4a, 0x13, 0x9e, 0x89,
	0x28, 0x88, 0xcb, 0xaa, 0x06, 0xe3, 0x49, 0x38, 0xc1, 0xdc, 0x77, 0x3d, 0x1c, 0xa5, 0x05, 0x9b,
	0xc7, 0x98, 0x5e, 0x12, 0x3c, 0x14, 0x3c, 0xee, 0xa7, 0xa1, 0x34, 0x49, 0xc3, 0x32, 0x64, 0x2c,
	0x73, 0x60, 0x52, 0xd3, 0xee, 0xf3, 0xec, 0xcc, 0xa8, 0xe3, 0x6f, 0xa5, 0x05, 0xe5, 0x79, 0xac,
	0x1e, 0x2b, 0xd5, 0x7b, 0x6e, 0x8e, 0x08, 0x2b, 0xb1, 0x44, 0x93, 0xc2, 0x10, 0x0f, 0x15, 0x06,
	0xa5, 0xce, 0xcd, 0x33, 0xc3, 0xe1, 0x71, 0x5e, 0x79, 0x03, 0x95, 0x69, 0x1b, 0x77, 0x4c, 0xf7,
	0xac, 0xd7, 0x6b, 0xd9, 0x3d, 0x47, 0x14, 0x05, 0xbf, 0x81, 0xe7, 0xf3, 0xd1, 0x89, 0x1b, 0x2d,
	0x56, 0xd2, 0xfc, 0x62, 0xc5, 0xf3, 0x34, 0x1e, 0xc9, 0x53, 0x16, 0x8a, 0x47, 0xba, 0xbd, 0x24,
	0x14, 0x0b, 0x80, 0xa2, 0x48, 0xc4, 0x55, 0xfa, 0xf0, 0x24, 0x04, 0x3a, 0x73, 0xa9, 0xe9, 0xd8,
	0xe8, 0x33, 0x78, 0x12, 0x28, 0xaf, 0x5d, 0xb1, 0x3a, 0xa7, 0x8d, 0x4d, 0xbb, 0xd6, 0x0f, 0x95,
	0xbf, 0x96, 0x81, 0x3e, 0x05, 0x79, 0x8c, 0xca, 0xda, 0xcd, 0xc4, 0xde, 0xf9, 0xfe, 0xa4, 0xf6,
	0xb7, 0x0c, 0xe5, 0x5f, 0x09, 0x90, 0x43, 0x2f, 0x35, 0xef, 0xb0, 0x4d, 0xd1, 0x21, 0x00, 0x66,
	0x07, 0x8d, 0xb7, 0x49, 0x89, 0x97, 0xbe, 0xa7, 0x13, 0xc3, 0x73, 0x24, 0xde, 0x1e, 0xb3, 0x38,
	0x38, 0xa2, 0x23, 0x00, 0xe6, 0x03, 0xcd, 0xb4, 0x7b, 0x0e, 0x29, 0x25, 0x78, 0xd0, 0xec, 0x08,
	0x9d, 0xc5, 0xc9, 0xf7, 0xd9, 0x89, 0x5b, 0x3a, 0xdb, 0xf5, 0x4f, 0xa4, 0xfc, 0x8f, 0x38, 0x64,
	0x02, 0x38, 0x7a, 0x01, 0x39, 0x26, 0xbd, 0xc7, 0x34, 0xb0, 0x58, 0xd6, 0xf5, 0x78, 0xb4, 0x0c,
	0xf4, 0x09, 0xac, 0xf1, 0x8a, 0xa9, 0x19, 0xa3, 0xa1, 0xce, 0xec, 0xc3, 0x15, 0x4c, 0xa8, 0x5e,
	0xf9, 0x6d, 0xf8, 0x40, 0xf4, 0x0e, 0xd2, 0x0e, 0x37, 0x5f, 0x20, 0x54, 0x45, 0x28, 0x94, 0x67,
	0x62, 0x35, 0xc0, 0x45, 0x55, 0xc8, 0x31, 0xa7, 0xeb, 0x3e, 0x6b, 0xaf, 0x51, 0x85, 0x41, 0x2c,
	0x3a, 0xbc, 0x9a, 0xab, 0x51, 0x73, 0x80, 0x4b, 0x49, 0xfe, 0xb8, 0x5f, 0x98, 0x59, 0x6b, 0x0e,
	0x45, 0x7a, 0x2a, 0xdc, 0x02, 0xfd, 0x94, 0x48, 0x4f, 0x52, 0xe2, 0x15, 0x20, 0xdb, 0xd1, 0x78,
	0x5e, 0x8e, 0xfd, 0x4a, 0x4a, 0x99, 0x6a, 0x62, 0x37, 0xaf, 0xae, 0xdb, 0x4e, 0x9b, 0x5d, 0xf8,
	0x8e, 0x25, 0x91, 0x06, 0x99, 0x8d, 0x34, 0x48, 0xe5, 0xcf, 0x12, 0x6c, 0x1e, 0xe9, 0xb4, 0x7b,
	0x7d, 0xee, 0x58, 0x26, 0xb9, 0x8e, 0x04, 0xdf, 0xe7, 0xb0, 0x36, 0x0e, 0x8b, 0x07, 0x64, 0xf7,
	0x6a, 0x7f, 0xf2, 0x41, 0xd0, 0x3b, 0xc8, 0xb9, 0x9c, 0xa9, 0x17, 0x16, 0x71, 0x1e, 0x16, 0x85,
	0x09, 0xa5, 0xf7, 0x22, 0x8f, 0x0b, 0x70, 0xc7, 0x67, 0xe5, 0x39, 0x94, 0xe7, 0x09, 0x44, 0x5c,
	0xe5, 0xef, 0x12, 0x54, 0xa7, 0xfa, 0x89, 0x8a, 0x2d, 0xac, 0x13, 0x5c, 0x77, 0x6c, 0xc3, 0xe4,
	0x2e, 0xc1, 0xb7, 0x21, 0x4b, 0x4a, 0x61, 0x4b, 0x7e, 0x01, 0xeb, 0xd3, 0xf9, 0xe0, 0xe5, 0xe0,
	0xfc, 0xb1, 0x20, 0x1f, 0xce, 0x12, 0x82, 0xde, 0x42, 0xc1, 0x8b, 0x20, 0xdc, 0xeb, 0xe1, 0x2e,
	0x35, 0xef, 0x7c, 0x57, 0x26, 0xb8, 0x2b, 0x11, 0xbf, 0x6b, 0x06, 0x57, 0xcc, 0xa5, 0xca, 0x0e,
	0xbc, 0x5c, 0x22, 0x2c, 0x71, 0x95, 0x6f, 0x40, 0xe1, 0x0a, 0x4f, 0x6a, 0x8b, 0x48, 0xa7, 0x22,
	0xa4, 0x3d, 0x9d, 0x3c, 0x1f, 0xe4, 0xd5, 0x14, 0x57, 0x8a, 0xa0, 0x67, 0x90, 0x22, 0xce, 0x68,
	0xd8, 0x0d, 0x26, 0x30, 0xff, 0x0b, 0x55, 0x61, 0xd5, 0x76, 0xb4, 0x11, 0xc1, 0x5a, 0x57, 0xef,
	0x5e, 0x7b, 0x52, 0x66, 0x54, 0xb0, 0x9d, 0x4b, 0x82, 0xeb, 0x0c, 0xa2, 0xfc, 0x2d, 0x01, 0x3b,
	0x4b, 0x5f, 0x26, 0x2e, 0xba, 0x86, 0xf5, 0x9e, 0x69, 0x51, 0x3c, 0xd4, 0x4c, 0x8a, 0x07, 0xda,
	0x40, 0x77, 0xfd, 0x30, 0x78, 0x3f, 0xb1, 0xdb, 0x03, 0xf8, 0xec, 0x7f, 0xe0, 0x4c, 0x5a, 0x14,
	0x0f, 0x4e, 0x74, 0xb7, 0x69, 0xd3, 0xe1, 0xbd, 0x9a, 0xef, 0x85, 0x61, 0xe5, 0x3f, 0x49, 0x20,
	0x77, 0xf4, 0xab, 0xe3, 0x29, 0xb3, 0x0b, 0xdc, 0x26, 0xfd, 0x2f, 0xdc, 0x16, 0x9f, 0xe7, 0xb6,
	0xf2, 0x77, 0x12, 0xa0, 0x59, 0x61, 0x59, 0x26, 0xde, 0xe0, 0xfb, 0xa0, 0x39, 0xdd, 0xe0, 0x7b,
	0xd4, 0x81, 0xe4, 0x9d, 0x6e, 0x8d, 0x3c, 0x5e, 0xb9, 0xc3, 0x2f, 0x1e, 0x67, 0x8f, 0xa8, 0xa6,
	0xaa, 0xc7, 0xec, 0xa7, 0xf1, 0x1f, 0x4b, 0xca, 0x5f, 0x25, 0x90, 0x2f, 0xc6, 0x9d, 0xa3, 0xee,
	0xd8, 0x3d, 0xb3, 0x3f, 0x33, 0x46, 0x57, 0x20, 0xcb, 0x52, 0x4b, 0x0b, 0xcd, 0xdf, 0x19, 0x06,
	0x38, 0x65, 0x5b, 0xcb, 0x4b, 0x58, 0x0d, 0x75, 0x23, 0xaf, 0x9a, 0x65, 0xd5, 0xdc, 0xa4, 0x1d,
	0x91, 0x70, 0x4c, 0xad, 0x4c, 0xc5, 0x54, 0x05, 0xb2, 0x41, 0xad, 0x22, 0x7e, 0xa5, 0xca, 0xf8,
	0x95, 0x8a, 0x28, 0x67, 0xb0, 0xe9, 0x05, 0x75, 0x54, 0x3e, 0x16, 0xa6, 0x87, 0x90, 0xea, 0xf2,
	0x0f, 0xbf, 0xff, 0x86, 0x26, 0xe0, 0x19, 0x74, 0x1f, 0x93, 0x65, 0xfc, 0x3c, 0x86, 0xc4, 0x55,
	0x7e, 0x0b, 0xc5, 0x63, 0x3c, 0x73, 0x45, 0xd8, 0x63, 0x08, 0x56, 0x5c, 0xbd, 0x8f, 0x7d, 0x8f,
	0xf0, 0x33, 0x83, 0x11, 0xf3, 0x5b, 0xec, 0x77, 0x2f, 0x7e, 0x66, 0x35, 0xd0, 0xc6, 0xd8, 0xf0,
	0x07, 0x6c, 0x2f, 0x11, 0xb2, 0x0c, 0xc2, 0xa7, 0x6b, 0xa5, 0x07, 0x25, 0xf1, 0x0b, 0xc4, 0x45,
	0x3f, 0x82, 0xb4, 0x27, 0x65, 0x10, 0x74, 0x8b, 0x14, 0x0a, 0x50, 0x51, 0x01, 0x92, 0xd4, 0xa1,
	0xba, 0x35, 0x9e, 0x59, 0xd8, 0x87, 0xb2, 0x0b, 0xcf, 0x1a, 0xd8, 0x12, 0x59, 0x2d, 0xda, 0xe4,
	0x37, 0xa1, 0x28, 0xc4, 0x24, 0xae, 0xf2, 0x13, 0xa8, 0x4e, 0x0d, 0xb2, 0x0f, 0xaf, 0x7f, 0xac,
	0x1a, 0x2d, 0x21, 0x25, 0xae, 0x72, 0xe3, 0x97, 0xdf, 0xe6, 0xef, 0xa9, 0x69, 0x77, 0xe9, 0xd2,
	0xf9, 0xec, 0x73, 0x58, 0x9b, 0xb4, 0x5d, 0xde, 0xcb, 0xe3, 0x0b, 0x5b, 0x44, 0xd0, 0x90, 0x19,
	0xaa, 0xb2, 0x05, 0x95, 0xb9, 0x8f, 0x11, 0x97, 0xcd, 0xfd, 0x17, 0xd8, 0x36, 0x9a, 0x86, 0x49,
	0xc7, 0x83, 0xc0, 0xe3, 0xe7, 0x7e, 0x01, 0x0b, 0xe2, 0x2a, 0xdb, 0xb0, 0x75, 0x8c, 0x69, 0x47,
	0xbf, 0xf2, 0xfa, 0x0c, 0x36, 0xd8, 0x35, 0x8f, 0x87, 0x13, 0xdd, 0x55, 0xf1, 0xad, 0xf2, 0x6f,
	0x09, 0x5e, 0x2c, 0xc2, 0x20, 0x2e, 0xfa, 0xa3, 0x04, 0x15, 0x66, 0x68, 0xd7, 0x47, 0xf0, 0xac,
	0xc0, 0xa3, 0x2c, 0x54, 0x25, 0x9b, 0x21, 0xf9, 0x16, 0xf2, 0xdb, 0x9f, 0x73, 0xe7, 0x95, 0xca,
	0x22, 0x15, 0xdf, 0x96, 0x7f, 0x01, 0xcf, 0x17, 0x11, 0x0a, 0xca, 0x56, 0x21, 0x5c, 0xb6, 0xf2,
	0xa1, 0xb2, 0xb3, 0xf7, 0x17, 0x09, 0x36, 0x84, 0x5b, 0x2e, 0xda, 0x82, 0x4d, 0xe1, 0xc5, 0xa9,
	0x63, 0x63, 0x39, 0x86, 0xb6, 0xa1, 0x22, 0xde, 0x8e, 0xf1, 0x9d, 0x89, 0xbf, 0x91, 0xa5, 0xb9,
	0xf4, 0xe7, 0x3a, 0x21, 0x72, 0x7c, 0x01, 0xfd, 0xef, 0x70, 0x97, 0xca, 0x89, 0xbd, 0xab, 0xa9,
	0xb9, 0xd3, 0x93, 0xa9, 0x04, 0x85, 0x28, 0xcc, 0x17, 0xa7, 0x08, 0x4f, 0xa3, 0x37, 0x6d, 0x93,
	0xca, 0x12, 0xaa, 0x40, 0x31, 0x7a, 0xe1, 0x07, 0x9f, 0x1c, 0xdf, 0xfb, 0x83, 0x04, 0xd9, 0xf1,
	0xb0, 0x8a, 0x9e, 0x01, 0xea, 0x7c, 0x7d, 0xde, 0xd4, 0x5a, 0xa7, 0x1f, 0x6b, 0xed, 0x56, 0x43,
	0x6b, 0x7e, 0x6c, 0x9e, 0x76, 0xe4, 0x18, 0x5a, 0x03, 0xe0, 0xf0, 0x76, 0xeb, 0xf8, 0xcb, 0x8e,
	0xf7, 0x8b, 0x09, 0xff, 0x3e, 0xbf, 0x3c, 0x6a, 0xb7, 0x2e, 0xbe, 0x94, 0xe3, 0x63, 0x48, 0xf3,
	0xab, 0x4e, 0xeb, 0xb4, 0xde, 0x91, 0x13, 0xa8, 0x00, 0x32, 0x87, 0x34, 0x9a, 0xed, 0x66, 0xa7,
	0xa9, 0xd5, 0x6b, 0x6a, 0x43, 0x5e, 0x41, 0x08, 0xd6, 0x3c, 0xbc, 0x46, 0xab, 0xe3, 0xc1, 0x92,
	0x7b, 0x5f, 0xc1, 0x7a, 0x63, 0x32, 0x35, 0x72, 0x41, 0x5e, 0xc2, 0xd6, 0x94, 0x20, 0x8d, 0x66,
	0xbd, 0x5d, 0x53, 0x6b, 0x9d, 0xd6, 0xd9, 0xa9, 0xc6, 0x2e, 0xe4, 0x18, 0x7a, 0x02, 0x79, 0x8e,
	0x72, 0xf6, 0xe1, 0x43, 0xab, 0xde, 0xaa, 0xb5, 0x65, 0x89, 0xff, 0x26, 0xc4, 0x40, 0xf5, 0xcb,
	0x8b, 0xce, 0xd9, 0x89, 0x1c, 0xdf, 0xfb, 0x19, 0xc0, 0x64, 0xe4, 0x62, 0x16, 0x0a, 0xf8, 0x9d,
	0x9f, 0x31, 0xb9, 0x03, 0x56, 0x59, 0x48, 0x06, 0x9a, 0xe5, 0x20, 0x3d, 0x56, 0xea, 0xf0, 0xfb,
	0x3c, 0xa4, 0x7d, 0xd3, 0xa1, 0x8f, 0xf0, 0x64, 0x66, 0xa9, 0x47, 0x2f, 0x26, 0x71, 0x2e, 0xfa,
	0xd1, 0xa0, 0xbc, 0xbd, 0xf0, 0x9e, 0xb8, 0x4a, 0x8c, 0xf1, 0x9d, 0xd9, 0xd2, 0xc3, 0x7c, 0x45,
	0xbf, 0x02, 0x84, 0xf9, 0x8a, 0x57, 0xfc, 0x18, 0x3a, 0x81, 0xb5, 0xe9, 0xcd, 0x0d, 0x55, 0xa6,
	0x92, 0x32, 0xc2, 0xf1, 0xf9, 0xfc, 0x4b, 0xce, 0xee, 0xeb, 0xe8, 0xcf, 0x01, 0x6c, 0x0b, 0x46,
	0xdb, 0xf3, 0xa8, 0xfc, 0x75, 0xbb, 0x5c, 0x5d, 0x8c, 0xc0, 0x59, 0x63, 0xd1, 0x5e, 0xcb, 0xd9,
	0xef, 0x4c, 0x51, 0x8b, 0x37, 0xfa, 0xf2, 0xff, 0x2d, 0x47, 0x0a, 0x69, 0x10, 0xb9, 0x8f, 0x68,
	0x30, 0xbb, 0x9e, 0x47, 0x34, 0x10, 0x6c, 0xdf, 0x9e, 0xad, 0xa7, 0x17, 0xd4, 0xb0, 0xad, 0x67,
	0xf6, 0xdb, 0xb0, 0xad, 0x05, 0x7b, 0x2d, 0x37, 0x88, 0x78, 0x1d, 0x08, 0x1b, 0x64, 0xee, 0x06,
	0x13, 0x36, 0xc8, 0x82, 0xad, 0x22, 0x86, 0xae, 0xa1, 0x38, 0xa7, 0x13, 0xa1, 0x28, 0x0b, 0x61,
	0x67, 0x2c, 0x7f, 0xf2, 0x00, 0x2c, 0xfe, 0xd2, 0xb7, 0xb0, 0xb5, 0x70, 0x27, 0x40, 0x7b, 0x73,
	0xf2, 0x44, 0xd0, 0xe9, 0xcb, 0xaf, 0x1e, 0x8c, 0xcb, 0xdf, 0xfe, 0x4e, 0x82, 0xed, 0x25, 0x93,
	0x29, 0x7a, 0xfd, 0x88, 0x21, 0xf6, 0xb6, 0xfc, 0xe6, 0x51, 0x23, 0xaf, 0xa7, 0xfe, 0xc2, 0x21,
	0x24, 0xac, 0xfe, 0xb2, 0x41, 0x27, 0xac, 0xfe, 0xf2, 0xc9, 0x86, 0xc7, 0x92, 0x78, 0xd0, 0x0c,
	0xc7, 0xd2, 0xdc, 0xd9, 0x36, 0x1c, 0x4b, 0x0b, 0xe6, 0xd5, 0x18, 0xfa, 0x35, 0x3c, 0x15, 0x4c,
	0x6f, 0xa8, 0x3a, 0x25, 0xac, 0xe8, 0x81, 0x97, 0x4b, 0x30, 0x38, 0x77, 0x0d, 0x0a, 0xa2, 0x69,
	0x15, 0xbd, 0x9c, 0xca, 0x4d, 0xd1, 0xbc, 0x5c, 0x56, 0x96, 0xa1, 0x04, 0x45, 0x78, 0x66, 0x64,
	0x0a, 0x17, 0x61, 0xd1, 0x48, 0x16, 0x2e, 0xc2, 0xe2, 0x79, 0x2b, 0x86, 0x6e, 0xf9, 0xef, 0x87,
	0x73, 0x46, 0x15, 0xf4, 0xe9, 0xc3, 0xa6, 0xa4, 0xdb, 0xf2, 0xee, 0x43, 0xc7, 0x29, 0x25, 0x76,
	0xf4, 0xfa, 0x57, 0x7b, 0x7d, 0xc7, 0xd2, 0xed, 0xfe, 0xfe, 0xbb, 0x43, 0x4a, 0xf7, 0xbb, 0xce,
	0xe0, 0x80, 0xff, 0x47, 0xa8, 0xeb, 0x58, 0x07, 0x04, 0x0f, 0xef, 0xcc, 0x2e, 0x26, 0xe3, 0x7f,
	0x16, 0x5d, 0xa5, 0xf8, 0xdd, 0x0f, 0xff, 0x13, 0x00, 0x00, 0xff, 0xff, 0xab, 0x7f, 0x28, 0x68,
	0x48, 0x1a, 0x00, 0x00,
}
