// Code generated by protoc-gen-go. DO NOT EDIT.
// source: push-notification/v3/push-stats.proto

package push_server // import "golang.52tt.com/protocol/services/push-notification/v3"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type BaseStatics struct {
	Total                *BaseStatics_Total    `protobuf:"bytes,1,opt,name=total,proto3" json:"total,omitempty"`
	DetailList           []*BaseStatics_Detail `protobuf:"bytes,2,rep,name=detail_list,json=detailList,proto3" json:"detail_list,omitempty"`
	ActionList           []*BaseStatics_Action `protobuf:"bytes,3,rep,name=action_list,json=actionList,proto3" json:"action_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *BaseStatics) Reset()         { *m = BaseStatics{} }
func (m *BaseStatics) String() string { return proto.CompactTextString(m) }
func (*BaseStatics) ProtoMessage()    {}
func (*BaseStatics) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{0}
}
func (m *BaseStatics) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseStatics.Unmarshal(m, b)
}
func (m *BaseStatics) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseStatics.Marshal(b, m, deterministic)
}
func (dst *BaseStatics) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseStatics.Merge(dst, src)
}
func (m *BaseStatics) XXX_Size() int {
	return xxx_messageInfo_BaseStatics.Size(m)
}
func (m *BaseStatics) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseStatics.DiscardUnknown(m)
}

var xxx_messageInfo_BaseStatics proto.InternalMessageInfo

func (m *BaseStatics) GetTotal() *BaseStatics_Total {
	if m != nil {
		return m.Total
	}
	return nil
}

func (m *BaseStatics) GetDetailList() []*BaseStatics_Detail {
	if m != nil {
		return m.DetailList
	}
	return nil
}

func (m *BaseStatics) GetActionList() []*BaseStatics_Action {
	if m != nil {
		return m.ActionList
	}
	return nil
}

type BaseStatics_Total struct {
	Target               int32    `protobuf:"varint,1,opt,name=target,proto3" json:"target,omitempty"`
	Receive              int32    `protobuf:"varint,2,opt,name=receive,proto3" json:"receive,omitempty"`
	Display              int32    `protobuf:"varint,3,opt,name=display,proto3" json:"display,omitempty"`
	Click                int32    `protobuf:"varint,4,opt,name=click,proto3" json:"click,omitempty"`
	Msg                  int32    `protobuf:"varint,5,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseStatics_Total) Reset()         { *m = BaseStatics_Total{} }
func (m *BaseStatics_Total) String() string { return proto.CompactTextString(m) }
func (*BaseStatics_Total) ProtoMessage()    {}
func (*BaseStatics_Total) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{0, 0}
}
func (m *BaseStatics_Total) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseStatics_Total.Unmarshal(m, b)
}
func (m *BaseStatics_Total) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseStatics_Total.Marshal(b, m, deterministic)
}
func (dst *BaseStatics_Total) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseStatics_Total.Merge(dst, src)
}
func (m *BaseStatics_Total) XXX_Size() int {
	return xxx_messageInfo_BaseStatics_Total.Size(m)
}
func (m *BaseStatics_Total) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseStatics_Total.DiscardUnknown(m)
}

var xxx_messageInfo_BaseStatics_Total proto.InternalMessageInfo

func (m *BaseStatics_Total) GetTarget() int32 {
	if m != nil {
		return m.Target
	}
	return 0
}

func (m *BaseStatics_Total) GetReceive() int32 {
	if m != nil {
		return m.Receive
	}
	return 0
}

func (m *BaseStatics_Total) GetDisplay() int32 {
	if m != nil {
		return m.Display
	}
	return 0
}

func (m *BaseStatics_Total) GetClick() int32 {
	if m != nil {
		return m.Click
	}
	return 0
}

func (m *BaseStatics_Total) GetMsg() int32 {
	if m != nil {
		return m.Msg
	}
	return 0
}

type BaseStatics_Detail struct {
	Target               int32    `protobuf:"varint,1,opt,name=target,proto3" json:"target,omitempty"`
	Receive              int32    `protobuf:"varint,2,opt,name=receive,proto3" json:"receive,omitempty"`
	Display              int32    `protobuf:"varint,3,opt,name=display,proto3" json:"display,omitempty"`
	Click                int32    `protobuf:"varint,4,opt,name=click,proto3" json:"click,omitempty"`
	Manufacturer         string   `protobuf:"bytes,5,opt,name=manufacturer,proto3" json:"manufacturer,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseStatics_Detail) Reset()         { *m = BaseStatics_Detail{} }
func (m *BaseStatics_Detail) String() string { return proto.CompactTextString(m) }
func (*BaseStatics_Detail) ProtoMessage()    {}
func (*BaseStatics_Detail) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{0, 1}
}
func (m *BaseStatics_Detail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseStatics_Detail.Unmarshal(m, b)
}
func (m *BaseStatics_Detail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseStatics_Detail.Marshal(b, m, deterministic)
}
func (dst *BaseStatics_Detail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseStatics_Detail.Merge(dst, src)
}
func (m *BaseStatics_Detail) XXX_Size() int {
	return xxx_messageInfo_BaseStatics_Detail.Size(m)
}
func (m *BaseStatics_Detail) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseStatics_Detail.DiscardUnknown(m)
}

var xxx_messageInfo_BaseStatics_Detail proto.InternalMessageInfo

func (m *BaseStatics_Detail) GetTarget() int32 {
	if m != nil {
		return m.Target
	}
	return 0
}

func (m *BaseStatics_Detail) GetReceive() int32 {
	if m != nil {
		return m.Receive
	}
	return 0
}

func (m *BaseStatics_Detail) GetDisplay() int32 {
	if m != nil {
		return m.Display
	}
	return 0
}

func (m *BaseStatics_Detail) GetClick() int32 {
	if m != nil {
		return m.Click
	}
	return 0
}

func (m *BaseStatics_Detail) GetManufacturer() string {
	if m != nil {
		return m.Manufacturer
	}
	return ""
}

type BaseStatics_Action struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value                int32    `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseStatics_Action) Reset()         { *m = BaseStatics_Action{} }
func (m *BaseStatics_Action) String() string { return proto.CompactTextString(m) }
func (*BaseStatics_Action) ProtoMessage()    {}
func (*BaseStatics_Action) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{0, 2}
}
func (m *BaseStatics_Action) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseStatics_Action.Unmarshal(m, b)
}
func (m *BaseStatics_Action) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseStatics_Action.Marshal(b, m, deterministic)
}
func (dst *BaseStatics_Action) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseStatics_Action.Merge(dst, src)
}
func (m *BaseStatics_Action) XXX_Size() int {
	return xxx_messageInfo_BaseStatics_Action.Size(m)
}
func (m *BaseStatics_Action) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseStatics_Action.DiscardUnknown(m)
}

var xxx_messageInfo_BaseStatics_Action proto.InternalMessageInfo

func (m *BaseStatics_Action) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *BaseStatics_Action) GetValue() int32 {
	if m != nil {
		return m.Value
	}
	return 0
}

type BasePushCount struct {
	Manufacturer         string   `protobuf:"bytes,1,opt,name=manufacturer,proto3" json:"manufacturer,omitempty"`
	DescribeTag          string   `protobuf:"bytes,2,opt,name=describe_tag,json=describeTag,proto3" json:"describe_tag,omitempty"`
	TotalNum             int32    `protobuf:"varint,3,opt,name=total_num,json=totalNum,proto3" json:"total_num,omitempty"`
	PushNum              int32    `protobuf:"varint,4,opt,name=push_num,json=pushNum,proto3" json:"push_num,omitempty"`
	RemainNum            int32    `protobuf:"varint,5,opt,name=remain_num,json=remainNum,proto3" json:"remain_num,omitempty"`
	HasLimit             bool     `protobuf:"varint,6,opt,name=has_limit,json=hasLimit,proto3" json:"has_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BasePushCount) Reset()         { *m = BasePushCount{} }
func (m *BasePushCount) String() string { return proto.CompactTextString(m) }
func (*BasePushCount) ProtoMessage()    {}
func (*BasePushCount) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{1}
}
func (m *BasePushCount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BasePushCount.Unmarshal(m, b)
}
func (m *BasePushCount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BasePushCount.Marshal(b, m, deterministic)
}
func (dst *BasePushCount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BasePushCount.Merge(dst, src)
}
func (m *BasePushCount) XXX_Size() int {
	return xxx_messageInfo_BasePushCount.Size(m)
}
func (m *BasePushCount) XXX_DiscardUnknown() {
	xxx_messageInfo_BasePushCount.DiscardUnknown(m)
}

var xxx_messageInfo_BasePushCount proto.InternalMessageInfo

func (m *BasePushCount) GetManufacturer() string {
	if m != nil {
		return m.Manufacturer
	}
	return ""
}

func (m *BasePushCount) GetDescribeTag() string {
	if m != nil {
		return m.DescribeTag
	}
	return ""
}

func (m *BasePushCount) GetTotalNum() int32 {
	if m != nil {
		return m.TotalNum
	}
	return 0
}

func (m *BasePushCount) GetPushNum() int32 {
	if m != nil {
		return m.PushNum
	}
	return 0
}

func (m *BasePushCount) GetRemainNum() int32 {
	if m != nil {
		return m.RemainNum
	}
	return 0
}

func (m *BasePushCount) GetHasLimit() bool {
	if m != nil {
		return m.HasLimit
	}
	return false
}

type QueryTasksReq struct {
	PushAgent            string   `protobuf:"bytes,1,opt,name=push_agent,json=pushAgent,proto3" json:"push_agent,omitempty"`
	BundleId             string   `protobuf:"bytes,2,opt,name=bundle_id,json=bundleId,proto3" json:"bundle_id,omitempty"`
	TaskList             []string `protobuf:"bytes,3,rep,name=task_list,json=taskList,proto3" json:"task_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryTasksReq) Reset()         { *m = QueryTasksReq{} }
func (m *QueryTasksReq) String() string { return proto.CompactTextString(m) }
func (*QueryTasksReq) ProtoMessage()    {}
func (*QueryTasksReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{2}
}
func (m *QueryTasksReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryTasksReq.Unmarshal(m, b)
}
func (m *QueryTasksReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryTasksReq.Marshal(b, m, deterministic)
}
func (dst *QueryTasksReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryTasksReq.Merge(dst, src)
}
func (m *QueryTasksReq) XXX_Size() int {
	return xxx_messageInfo_QueryTasksReq.Size(m)
}
func (m *QueryTasksReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryTasksReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryTasksReq proto.InternalMessageInfo

func (m *QueryTasksReq) GetPushAgent() string {
	if m != nil {
		return m.PushAgent
	}
	return ""
}

func (m *QueryTasksReq) GetBundleId() string {
	if m != nil {
		return m.BundleId
	}
	return ""
}

func (m *QueryTasksReq) GetTaskList() []string {
	if m != nil {
		return m.TaskList
	}
	return nil
}

type QueryTasksResp struct {
	TaskList             []string       `protobuf:"bytes,1,rep,name=task_list,json=taskList,proto3" json:"task_list,omitempty"`
	StaticsList          []*BaseStatics `protobuf:"bytes,2,rep,name=statics_list,json=staticsList,proto3" json:"statics_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *QueryTasksResp) Reset()         { *m = QueryTasksResp{} }
func (m *QueryTasksResp) String() string { return proto.CompactTextString(m) }
func (*QueryTasksResp) ProtoMessage()    {}
func (*QueryTasksResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{3}
}
func (m *QueryTasksResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryTasksResp.Unmarshal(m, b)
}
func (m *QueryTasksResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryTasksResp.Marshal(b, m, deterministic)
}
func (dst *QueryTasksResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryTasksResp.Merge(dst, src)
}
func (m *QueryTasksResp) XXX_Size() int {
	return xxx_messageInfo_QueryTasksResp.Size(m)
}
func (m *QueryTasksResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryTasksResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryTasksResp proto.InternalMessageInfo

func (m *QueryTasksResp) GetTaskList() []string {
	if m != nil {
		return m.TaskList
	}
	return nil
}

func (m *QueryTasksResp) GetStaticsList() []*BaseStatics {
	if m != nil {
		return m.StaticsList
	}
	return nil
}

type QueryTaskGroupReq struct {
	PushAgent            string   `protobuf:"bytes,1,opt,name=push_agent,json=pushAgent,proto3" json:"push_agent,omitempty"`
	BundleId             string   `protobuf:"bytes,2,opt,name=bundle_id,json=bundleId,proto3" json:"bundle_id,omitempty"`
	GroupId              string   `protobuf:"bytes,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryTaskGroupReq) Reset()         { *m = QueryTaskGroupReq{} }
func (m *QueryTaskGroupReq) String() string { return proto.CompactTextString(m) }
func (*QueryTaskGroupReq) ProtoMessage()    {}
func (*QueryTaskGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{4}
}
func (m *QueryTaskGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryTaskGroupReq.Unmarshal(m, b)
}
func (m *QueryTaskGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryTaskGroupReq.Marshal(b, m, deterministic)
}
func (dst *QueryTaskGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryTaskGroupReq.Merge(dst, src)
}
func (m *QueryTaskGroupReq) XXX_Size() int {
	return xxx_messageInfo_QueryTaskGroupReq.Size(m)
}
func (m *QueryTaskGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryTaskGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryTaskGroupReq proto.InternalMessageInfo

func (m *QueryTaskGroupReq) GetPushAgent() string {
	if m != nil {
		return m.PushAgent
	}
	return ""
}

func (m *QueryTaskGroupReq) GetBundleId() string {
	if m != nil {
		return m.BundleId
	}
	return ""
}

func (m *QueryTaskGroupReq) GetGroupId() string {
	if m != nil {
		return m.GroupId
	}
	return ""
}

type QueryTaskGroupResp struct {
	Statics              *BaseStatics `protobuf:"bytes,1,opt,name=statics,proto3" json:"statics,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *QueryTaskGroupResp) Reset()         { *m = QueryTaskGroupResp{} }
func (m *QueryTaskGroupResp) String() string { return proto.CompactTextString(m) }
func (*QueryTaskGroupResp) ProtoMessage()    {}
func (*QueryTaskGroupResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{5}
}
func (m *QueryTaskGroupResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryTaskGroupResp.Unmarshal(m, b)
}
func (m *QueryTaskGroupResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryTaskGroupResp.Marshal(b, m, deterministic)
}
func (dst *QueryTaskGroupResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryTaskGroupResp.Merge(dst, src)
}
func (m *QueryTaskGroupResp) XXX_Size() int {
	return xxx_messageInfo_QueryTaskGroupResp.Size(m)
}
func (m *QueryTaskGroupResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryTaskGroupResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryTaskGroupResp proto.InternalMessageInfo

func (m *QueryTaskGroupResp) GetStatics() *BaseStatics {
	if m != nil {
		return m.Statics
	}
	return nil
}

type QueryPushCountReq struct {
	PushAgent            string   `protobuf:"bytes,1,opt,name=push_agent,json=pushAgent,proto3" json:"push_agent,omitempty"`
	BundleId             string   `protobuf:"bytes,2,opt,name=bundle_id,json=bundleId,proto3" json:"bundle_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryPushCountReq) Reset()         { *m = QueryPushCountReq{} }
func (m *QueryPushCountReq) String() string { return proto.CompactTextString(m) }
func (*QueryPushCountReq) ProtoMessage()    {}
func (*QueryPushCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{6}
}
func (m *QueryPushCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryPushCountReq.Unmarshal(m, b)
}
func (m *QueryPushCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryPushCountReq.Marshal(b, m, deterministic)
}
func (dst *QueryPushCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryPushCountReq.Merge(dst, src)
}
func (m *QueryPushCountReq) XXX_Size() int {
	return xxx_messageInfo_QueryPushCountReq.Size(m)
}
func (m *QueryPushCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryPushCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryPushCountReq proto.InternalMessageInfo

func (m *QueryPushCountReq) GetPushAgent() string {
	if m != nil {
		return m.PushAgent
	}
	return ""
}

func (m *QueryPushCountReq) GetBundleId() string {
	if m != nil {
		return m.BundleId
	}
	return ""
}

type QueryPushCountResp struct {
	CountList            []*BasePushCount `protobuf:"bytes,1,rep,name=count_list,json=countList,proto3" json:"count_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *QueryPushCountResp) Reset()         { *m = QueryPushCountResp{} }
func (m *QueryPushCountResp) String() string { return proto.CompactTextString(m) }
func (*QueryPushCountResp) ProtoMessage()    {}
func (*QueryPushCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{7}
}
func (m *QueryPushCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryPushCountResp.Unmarshal(m, b)
}
func (m *QueryPushCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryPushCountResp.Marshal(b, m, deterministic)
}
func (dst *QueryPushCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryPushCountResp.Merge(dst, src)
}
func (m *QueryPushCountResp) XXX_Size() int {
	return xxx_messageInfo_QueryPushCountResp.Size(m)
}
func (m *QueryPushCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryPushCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryPushCountResp proto.InternalMessageInfo

func (m *QueryPushCountResp) GetCountList() []*BasePushCount {
	if m != nil {
		return m.CountList
	}
	return nil
}

type QueryPushDataByDayReq struct {
	PushAgent            string   `protobuf:"bytes,1,opt,name=push_agent,json=pushAgent,proto3" json:"push_agent,omitempty"`
	BundleId             string   `protobuf:"bytes,2,opt,name=bundle_id,json=bundleId,proto3" json:"bundle_id,omitempty"`
	UnixSecond           int64    `protobuf:"varint,3,opt,name=unix_second,json=unixSecond,proto3" json:"unix_second,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryPushDataByDayReq) Reset()         { *m = QueryPushDataByDayReq{} }
func (m *QueryPushDataByDayReq) String() string { return proto.CompactTextString(m) }
func (*QueryPushDataByDayReq) ProtoMessage()    {}
func (*QueryPushDataByDayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{8}
}
func (m *QueryPushDataByDayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryPushDataByDayReq.Unmarshal(m, b)
}
func (m *QueryPushDataByDayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryPushDataByDayReq.Marshal(b, m, deterministic)
}
func (dst *QueryPushDataByDayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryPushDataByDayReq.Merge(dst, src)
}
func (m *QueryPushDataByDayReq) XXX_Size() int {
	return xxx_messageInfo_QueryPushDataByDayReq.Size(m)
}
func (m *QueryPushDataByDayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryPushDataByDayReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryPushDataByDayReq proto.InternalMessageInfo

func (m *QueryPushDataByDayReq) GetPushAgent() string {
	if m != nil {
		return m.PushAgent
	}
	return ""
}

func (m *QueryPushDataByDayReq) GetBundleId() string {
	if m != nil {
		return m.BundleId
	}
	return ""
}

func (m *QueryPushDataByDayReq) GetUnixSecond() int64 {
	if m != nil {
		return m.UnixSecond
	}
	return 0
}

type QueryPushDataByDayResp struct {
	Date                 string       `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	Statics              *BaseStatics `protobuf:"bytes,2,opt,name=statics,proto3" json:"statics,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *QueryPushDataByDayResp) Reset()         { *m = QueryPushDataByDayResp{} }
func (m *QueryPushDataByDayResp) String() string { return proto.CompactTextString(m) }
func (*QueryPushDataByDayResp) ProtoMessage()    {}
func (*QueryPushDataByDayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{9}
}
func (m *QueryPushDataByDayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryPushDataByDayResp.Unmarshal(m, b)
}
func (m *QueryPushDataByDayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryPushDataByDayResp.Marshal(b, m, deterministic)
}
func (dst *QueryPushDataByDayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryPushDataByDayResp.Merge(dst, src)
}
func (m *QueryPushDataByDayResp) XXX_Size() int {
	return xxx_messageInfo_QueryPushDataByDayResp.Size(m)
}
func (m *QueryPushDataByDayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryPushDataByDayResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryPushDataByDayResp proto.InternalMessageInfo

func (m *QueryPushDataByDayResp) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *QueryPushDataByDayResp) GetStatics() *BaseStatics {
	if m != nil {
		return m.Statics
	}
	return nil
}

type QueryUserDataByDayReq struct {
	PushAgent            string   `protobuf:"bytes,1,opt,name=push_agent,json=pushAgent,proto3" json:"push_agent,omitempty"`
	BundleId             string   `protobuf:"bytes,2,opt,name=bundle_id,json=bundleId,proto3" json:"bundle_id,omitempty"`
	UnixSecond           int64    `protobuf:"varint,3,opt,name=unix_second,json=unixSecond,proto3" json:"unix_second,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryUserDataByDayReq) Reset()         { *m = QueryUserDataByDayReq{} }
func (m *QueryUserDataByDayReq) String() string { return proto.CompactTextString(m) }
func (*QueryUserDataByDayReq) ProtoMessage()    {}
func (*QueryUserDataByDayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{10}
}
func (m *QueryUserDataByDayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryUserDataByDayReq.Unmarshal(m, b)
}
func (m *QueryUserDataByDayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryUserDataByDayReq.Marshal(b, m, deterministic)
}
func (dst *QueryUserDataByDayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryUserDataByDayReq.Merge(dst, src)
}
func (m *QueryUserDataByDayReq) XXX_Size() int {
	return xxx_messageInfo_QueryUserDataByDayReq.Size(m)
}
func (m *QueryUserDataByDayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryUserDataByDayReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryUserDataByDayReq proto.InternalMessageInfo

func (m *QueryUserDataByDayReq) GetPushAgent() string {
	if m != nil {
		return m.PushAgent
	}
	return ""
}

func (m *QueryUserDataByDayReq) GetBundleId() string {
	if m != nil {
		return m.BundleId
	}
	return ""
}

func (m *QueryUserDataByDayReq) GetUnixSecond() int64 {
	if m != nil {
		return m.UnixSecond
	}
	return 0
}

type QueryUserDataByDayResp struct {
	Date                 string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	Accumulative         int32    `protobuf:"varint,2,opt,name=accumulative,proto3" json:"accumulative,omitempty"`
	Register             int32    `protobuf:"varint,3,opt,name=register,proto3" json:"register,omitempty"`
	Active               int32    `protobuf:"varint,4,opt,name=active,proto3" json:"active,omitempty"`
	Online               int32    `protobuf:"varint,5,opt,name=online,proto3" json:"online,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryUserDataByDayResp) Reset()         { *m = QueryUserDataByDayResp{} }
func (m *QueryUserDataByDayResp) String() string { return proto.CompactTextString(m) }
func (*QueryUserDataByDayResp) ProtoMessage()    {}
func (*QueryUserDataByDayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{11}
}
func (m *QueryUserDataByDayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryUserDataByDayResp.Unmarshal(m, b)
}
func (m *QueryUserDataByDayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryUserDataByDayResp.Marshal(b, m, deterministic)
}
func (dst *QueryUserDataByDayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryUserDataByDayResp.Merge(dst, src)
}
func (m *QueryUserDataByDayResp) XXX_Size() int {
	return xxx_messageInfo_QueryUserDataByDayResp.Size(m)
}
func (m *QueryUserDataByDayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryUserDataByDayResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryUserDataByDayResp proto.InternalMessageInfo

func (m *QueryUserDataByDayResp) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *QueryUserDataByDayResp) GetAccumulative() int32 {
	if m != nil {
		return m.Accumulative
	}
	return 0
}

func (m *QueryUserDataByDayResp) GetRegister() int32 {
	if m != nil {
		return m.Register
	}
	return 0
}

func (m *QueryUserDataByDayResp) GetActive() int32 {
	if m != nil {
		return m.Active
	}
	return 0
}

func (m *QueryUserDataByDayResp) GetOnline() int32 {
	if m != nil {
		return m.Online
	}
	return 0
}

type QueryOnlineUserBy24HReq struct {
	PushAgent            string   `protobuf:"bytes,1,opt,name=push_agent,json=pushAgent,proto3" json:"push_agent,omitempty"`
	BundleId             string   `protobuf:"bytes,2,opt,name=bundle_id,json=bundleId,proto3" json:"bundle_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryOnlineUserBy24HReq) Reset()         { *m = QueryOnlineUserBy24HReq{} }
func (m *QueryOnlineUserBy24HReq) String() string { return proto.CompactTextString(m) }
func (*QueryOnlineUserBy24HReq) ProtoMessage()    {}
func (*QueryOnlineUserBy24HReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{12}
}
func (m *QueryOnlineUserBy24HReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryOnlineUserBy24HReq.Unmarshal(m, b)
}
func (m *QueryOnlineUserBy24HReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryOnlineUserBy24HReq.Marshal(b, m, deterministic)
}
func (dst *QueryOnlineUserBy24HReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryOnlineUserBy24HReq.Merge(dst, src)
}
func (m *QueryOnlineUserBy24HReq) XXX_Size() int {
	return xxx_messageInfo_QueryOnlineUserBy24HReq.Size(m)
}
func (m *QueryOnlineUserBy24HReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryOnlineUserBy24HReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryOnlineUserBy24HReq proto.InternalMessageInfo

func (m *QueryOnlineUserBy24HReq) GetPushAgent() string {
	if m != nil {
		return m.PushAgent
	}
	return ""
}

func (m *QueryOnlineUserBy24HReq) GetBundleId() string {
	if m != nil {
		return m.BundleId
	}
	return ""
}

type QueryOnlineUserBy24HResp struct {
	Date                 string                                 `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	OnlineList           []*QueryOnlineUserBy24HResp_OnlineInfo `protobuf:"bytes,2,rep,name=online_list,json=onlineList,proto3" json:"online_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *QueryOnlineUserBy24HResp) Reset()         { *m = QueryOnlineUserBy24HResp{} }
func (m *QueryOnlineUserBy24HResp) String() string { return proto.CompactTextString(m) }
func (*QueryOnlineUserBy24HResp) ProtoMessage()    {}
func (*QueryOnlineUserBy24HResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{13}
}
func (m *QueryOnlineUserBy24HResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryOnlineUserBy24HResp.Unmarshal(m, b)
}
func (m *QueryOnlineUserBy24HResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryOnlineUserBy24HResp.Marshal(b, m, deterministic)
}
func (dst *QueryOnlineUserBy24HResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryOnlineUserBy24HResp.Merge(dst, src)
}
func (m *QueryOnlineUserBy24HResp) XXX_Size() int {
	return xxx_messageInfo_QueryOnlineUserBy24HResp.Size(m)
}
func (m *QueryOnlineUserBy24HResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryOnlineUserBy24HResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryOnlineUserBy24HResp proto.InternalMessageInfo

func (m *QueryOnlineUserBy24HResp) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *QueryOnlineUserBy24HResp) GetOnlineList() []*QueryOnlineUserBy24HResp_OnlineInfo {
	if m != nil {
		return m.OnlineList
	}
	return nil
}

type QueryOnlineUserBy24HResp_OnlineInfo struct {
	UnixMillisecond      int64    `protobuf:"varint,1,opt,name=unix_millisecond,json=unixMillisecond,proto3" json:"unix_millisecond,omitempty"`
	Online               int32    `protobuf:"varint,2,opt,name=online,proto3" json:"online,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryOnlineUserBy24HResp_OnlineInfo) Reset()         { *m = QueryOnlineUserBy24HResp_OnlineInfo{} }
func (m *QueryOnlineUserBy24HResp_OnlineInfo) String() string { return proto.CompactTextString(m) }
func (*QueryOnlineUserBy24HResp_OnlineInfo) ProtoMessage()    {}
func (*QueryOnlineUserBy24HResp_OnlineInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{13, 0}
}
func (m *QueryOnlineUserBy24HResp_OnlineInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryOnlineUserBy24HResp_OnlineInfo.Unmarshal(m, b)
}
func (m *QueryOnlineUserBy24HResp_OnlineInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryOnlineUserBy24HResp_OnlineInfo.Marshal(b, m, deterministic)
}
func (dst *QueryOnlineUserBy24HResp_OnlineInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryOnlineUserBy24HResp_OnlineInfo.Merge(dst, src)
}
func (m *QueryOnlineUserBy24HResp_OnlineInfo) XXX_Size() int {
	return xxx_messageInfo_QueryOnlineUserBy24HResp_OnlineInfo.Size(m)
}
func (m *QueryOnlineUserBy24HResp_OnlineInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryOnlineUserBy24HResp_OnlineInfo.DiscardUnknown(m)
}

var xxx_messageInfo_QueryOnlineUserBy24HResp_OnlineInfo proto.InternalMessageInfo

func (m *QueryOnlineUserBy24HResp_OnlineInfo) GetUnixMillisecond() int64 {
	if m != nil {
		return m.UnixMillisecond
	}
	return 0
}

func (m *QueryOnlineUserBy24HResp_OnlineInfo) GetOnline() int32 {
	if m != nil {
		return m.Online
	}
	return 0
}

type QueryTaskScheduleReq struct {
	PushAgent            string   `protobuf:"bytes,1,opt,name=push_agent,json=pushAgent,proto3" json:"push_agent,omitempty"`
	BundleId             string   `protobuf:"bytes,2,opt,name=bundle_id,json=bundleId,proto3" json:"bundle_id,omitempty"`
	TaskId               string   `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryTaskScheduleReq) Reset()         { *m = QueryTaskScheduleReq{} }
func (m *QueryTaskScheduleReq) String() string { return proto.CompactTextString(m) }
func (*QueryTaskScheduleReq) ProtoMessage()    {}
func (*QueryTaskScheduleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{14}
}
func (m *QueryTaskScheduleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryTaskScheduleReq.Unmarshal(m, b)
}
func (m *QueryTaskScheduleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryTaskScheduleReq.Marshal(b, m, deterministic)
}
func (dst *QueryTaskScheduleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryTaskScheduleReq.Merge(dst, src)
}
func (m *QueryTaskScheduleReq) XXX_Size() int {
	return xxx_messageInfo_QueryTaskScheduleReq.Size(m)
}
func (m *QueryTaskScheduleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryTaskScheduleReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryTaskScheduleReq proto.InternalMessageInfo

func (m *QueryTaskScheduleReq) GetPushAgent() string {
	if m != nil {
		return m.PushAgent
	}
	return ""
}

func (m *QueryTaskScheduleReq) GetBundleId() string {
	if m != nil {
		return m.BundleId
	}
	return ""
}

func (m *QueryTaskScheduleReq) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

type QueryTaskScheduleResp struct {
	ScheduleList         []*QueryTaskScheduleResp_ScheduleInfo `protobuf:"bytes,1,rep,name=schedule_list,json=scheduleList,proto3" json:"schedule_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *QueryTaskScheduleResp) Reset()         { *m = QueryTaskScheduleResp{} }
func (m *QueryTaskScheduleResp) String() string { return proto.CompactTextString(m) }
func (*QueryTaskScheduleResp) ProtoMessage()    {}
func (*QueryTaskScheduleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{15}
}
func (m *QueryTaskScheduleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryTaskScheduleResp.Unmarshal(m, b)
}
func (m *QueryTaskScheduleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryTaskScheduleResp.Marshal(b, m, deterministic)
}
func (dst *QueryTaskScheduleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryTaskScheduleResp.Merge(dst, src)
}
func (m *QueryTaskScheduleResp) XXX_Size() int {
	return xxx_messageInfo_QueryTaskScheduleResp.Size(m)
}
func (m *QueryTaskScheduleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryTaskScheduleResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryTaskScheduleResp proto.InternalMessageInfo

func (m *QueryTaskScheduleResp) GetScheduleList() []*QueryTaskScheduleResp_ScheduleInfo {
	if m != nil {
		return m.ScheduleList
	}
	return nil
}

type QueryTaskScheduleResp_ScheduleInfo struct {
	Item                 string   `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"`
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryTaskScheduleResp_ScheduleInfo) Reset()         { *m = QueryTaskScheduleResp_ScheduleInfo{} }
func (m *QueryTaskScheduleResp_ScheduleInfo) String() string { return proto.CompactTextString(m) }
func (*QueryTaskScheduleResp_ScheduleInfo) ProtoMessage()    {}
func (*QueryTaskScheduleResp_ScheduleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{15, 0}
}
func (m *QueryTaskScheduleResp_ScheduleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryTaskScheduleResp_ScheduleInfo.Unmarshal(m, b)
}
func (m *QueryTaskScheduleResp_ScheduleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryTaskScheduleResp_ScheduleInfo.Marshal(b, m, deterministic)
}
func (dst *QueryTaskScheduleResp_ScheduleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryTaskScheduleResp_ScheduleInfo.Merge(dst, src)
}
func (m *QueryTaskScheduleResp_ScheduleInfo) XXX_Size() int {
	return xxx_messageInfo_QueryTaskScheduleResp_ScheduleInfo.Size(m)
}
func (m *QueryTaskScheduleResp_ScheduleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryTaskScheduleResp_ScheduleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_QueryTaskScheduleResp_ScheduleInfo proto.InternalMessageInfo

func (m *QueryTaskScheduleResp_ScheduleInfo) GetItem() string {
	if m != nil {
		return m.Item
	}
	return ""
}

func (m *QueryTaskScheduleResp_ScheduleInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type QueryTaskDetailReq struct {
	PushAgent            string   `protobuf:"bytes,1,opt,name=push_agent,json=pushAgent,proto3" json:"push_agent,omitempty"`
	BundleId             string   `protobuf:"bytes,2,opt,name=bundle_id,json=bundleId,proto3" json:"bundle_id,omitempty"`
	TaskId               string   `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	CId                  string   `protobuf:"bytes,4,opt,name=c_id,json=cId,proto3" json:"c_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryTaskDetailReq) Reset()         { *m = QueryTaskDetailReq{} }
func (m *QueryTaskDetailReq) String() string { return proto.CompactTextString(m) }
func (*QueryTaskDetailReq) ProtoMessage()    {}
func (*QueryTaskDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{16}
}
func (m *QueryTaskDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryTaskDetailReq.Unmarshal(m, b)
}
func (m *QueryTaskDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryTaskDetailReq.Marshal(b, m, deterministic)
}
func (dst *QueryTaskDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryTaskDetailReq.Merge(dst, src)
}
func (m *QueryTaskDetailReq) XXX_Size() int {
	return xxx_messageInfo_QueryTaskDetailReq.Size(m)
}
func (m *QueryTaskDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryTaskDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryTaskDetailReq proto.InternalMessageInfo

func (m *QueryTaskDetailReq) GetPushAgent() string {
	if m != nil {
		return m.PushAgent
	}
	return ""
}

func (m *QueryTaskDetailReq) GetBundleId() string {
	if m != nil {
		return m.BundleId
	}
	return ""
}

func (m *QueryTaskDetailReq) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *QueryTaskDetailReq) GetCId() string {
	if m != nil {
		return m.CId
	}
	return ""
}

type QueryTaskDetailResp struct {
	EventList            []*QueryTaskDetailResp_EventInfo `protobuf:"bytes,1,rep,name=event_list,json=eventList,proto3" json:"event_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *QueryTaskDetailResp) Reset()         { *m = QueryTaskDetailResp{} }
func (m *QueryTaskDetailResp) String() string { return proto.CompactTextString(m) }
func (*QueryTaskDetailResp) ProtoMessage()    {}
func (*QueryTaskDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{17}
}
func (m *QueryTaskDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryTaskDetailResp.Unmarshal(m, b)
}
func (m *QueryTaskDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryTaskDetailResp.Marshal(b, m, deterministic)
}
func (dst *QueryTaskDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryTaskDetailResp.Merge(dst, src)
}
func (m *QueryTaskDetailResp) XXX_Size() int {
	return xxx_messageInfo_QueryTaskDetailResp.Size(m)
}
func (m *QueryTaskDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryTaskDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryTaskDetailResp proto.InternalMessageInfo

func (m *QueryTaskDetailResp) GetEventList() []*QueryTaskDetailResp_EventInfo {
	if m != nil {
		return m.EventList
	}
	return nil
}

type QueryTaskDetailResp_EventInfo struct {
	Time                 string   `protobuf:"bytes,1,opt,name=time,proto3" json:"time,omitempty"`
	Event                string   `protobuf:"bytes,2,opt,name=event,proto3" json:"event,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryTaskDetailResp_EventInfo) Reset()         { *m = QueryTaskDetailResp_EventInfo{} }
func (m *QueryTaskDetailResp_EventInfo) String() string { return proto.CompactTextString(m) }
func (*QueryTaskDetailResp_EventInfo) ProtoMessage()    {}
func (*QueryTaskDetailResp_EventInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{17, 0}
}
func (m *QueryTaskDetailResp_EventInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryTaskDetailResp_EventInfo.Unmarshal(m, b)
}
func (m *QueryTaskDetailResp_EventInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryTaskDetailResp_EventInfo.Marshal(b, m, deterministic)
}
func (dst *QueryTaskDetailResp_EventInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryTaskDetailResp_EventInfo.Merge(dst, src)
}
func (m *QueryTaskDetailResp_EventInfo) XXX_Size() int {
	return xxx_messageInfo_QueryTaskDetailResp_EventInfo.Size(m)
}
func (m *QueryTaskDetailResp_EventInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryTaskDetailResp_EventInfo.DiscardUnknown(m)
}

var xxx_messageInfo_QueryTaskDetailResp_EventInfo proto.InternalMessageInfo

func (m *QueryTaskDetailResp_EventInfo) GetTime() string {
	if m != nil {
		return m.Time
	}
	return ""
}

func (m *QueryTaskDetailResp_EventInfo) GetEvent() string {
	if m != nil {
		return m.Event
	}
	return ""
}

type PushMsgInfo struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	TId                  string   `protobuf:"bytes,2,opt,name=t_id,json=tId,proto3" json:"t_id,omitempty"`
	CId                  string   `protobuf:"bytes,3,opt,name=c_id,json=cId,proto3" json:"c_id,omitempty"`
	UId                  uint32   `protobuf:"varint,4,opt,name=u_id,json=uId,proto3" json:"u_id,omitempty"`
	PushType             PushType `protobuf:"varint,5,opt,name=push_type,json=pushType,proto3,enum=PushNotification.PushType" json:"push_type,omitempty"`
	AppId                string   `protobuf:"bytes,6,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	UserIp               string   `protobuf:"bytes,7,opt,name=user_ip,json=userIp,proto3" json:"user_ip,omitempty"`
	UserAgent            string   `protobuf:"bytes,8,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushMsgInfo) Reset()         { *m = PushMsgInfo{} }
func (m *PushMsgInfo) String() string { return proto.CompactTextString(m) }
func (*PushMsgInfo) ProtoMessage()    {}
func (*PushMsgInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{18}
}
func (m *PushMsgInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushMsgInfo.Unmarshal(m, b)
}
func (m *PushMsgInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushMsgInfo.Marshal(b, m, deterministic)
}
func (dst *PushMsgInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushMsgInfo.Merge(dst, src)
}
func (m *PushMsgInfo) XXX_Size() int {
	return xxx_messageInfo_PushMsgInfo.Size(m)
}
func (m *PushMsgInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PushMsgInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PushMsgInfo proto.InternalMessageInfo

func (m *PushMsgInfo) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *PushMsgInfo) GetTId() string {
	if m != nil {
		return m.TId
	}
	return ""
}

func (m *PushMsgInfo) GetCId() string {
	if m != nil {
		return m.CId
	}
	return ""
}

func (m *PushMsgInfo) GetUId() uint32 {
	if m != nil {
		return m.UId
	}
	return 0
}

func (m *PushMsgInfo) GetPushType() PushType {
	if m != nil {
		return m.PushType
	}
	return PushType_Normal
}

func (m *PushMsgInfo) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *PushMsgInfo) GetUserIp() string {
	if m != nil {
		return m.UserIp
	}
	return ""
}

func (m *PushMsgInfo) GetUserAgent() string {
	if m != nil {
		return m.UserAgent
	}
	return ""
}

type SaveTaskIdReq struct {
	TaskId               string         `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	InfoList             []*PushMsgInfo `protobuf:"bytes,2,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SaveTaskIdReq) Reset()         { *m = SaveTaskIdReq{} }
func (m *SaveTaskIdReq) String() string { return proto.CompactTextString(m) }
func (*SaveTaskIdReq) ProtoMessage()    {}
func (*SaveTaskIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{19}
}
func (m *SaveTaskIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveTaskIdReq.Unmarshal(m, b)
}
func (m *SaveTaskIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveTaskIdReq.Marshal(b, m, deterministic)
}
func (dst *SaveTaskIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveTaskIdReq.Merge(dst, src)
}
func (m *SaveTaskIdReq) XXX_Size() int {
	return xxx_messageInfo_SaveTaskIdReq.Size(m)
}
func (m *SaveTaskIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveTaskIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_SaveTaskIdReq proto.InternalMessageInfo

func (m *SaveTaskIdReq) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *SaveTaskIdReq) GetInfoList() []*PushMsgInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type SaveTaskIdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveTaskIdResp) Reset()         { *m = SaveTaskIdResp{} }
func (m *SaveTaskIdResp) String() string { return proto.CompactTextString(m) }
func (*SaveTaskIdResp) ProtoMessage()    {}
func (*SaveTaskIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{20}
}
func (m *SaveTaskIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveTaskIdResp.Unmarshal(m, b)
}
func (m *SaveTaskIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveTaskIdResp.Marshal(b, m, deterministic)
}
func (dst *SaveTaskIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveTaskIdResp.Merge(dst, src)
}
func (m *SaveTaskIdResp) XXX_Size() int {
	return xxx_messageInfo_SaveTaskIdResp.Size(m)
}
func (m *SaveTaskIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveTaskIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_SaveTaskIdResp proto.InternalMessageInfo

type PushTaskStat struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	TokenType            string   `protobuf:"bytes,2,opt,name=token_type,json=tokenType,proto3" json:"token_type,omitempty"`
	CreateTime           int64    `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Select               int64    `protobuf:"varint,4,opt,name=select,proto3" json:"select,omitempty"`
	Stored               int64    `protobuf:"varint,5,opt,name=stored,proto3" json:"stored,omitempty"`
	Queued               int64    `protobuf:"varint,6,opt,name=queued,proto3" json:"queued,omitempty"`
	Target               int64    `protobuf:"varint,7,opt,name=target,proto3" json:"target,omitempty"`
	Submit               int64    `protobuf:"varint,8,opt,name=submit,proto3" json:"submit,omitempty"`
	Arrive               int64    `protobuf:"varint,9,opt,name=arrive,proto3" json:"arrive,omitempty"`
	Click                int64    `protobuf:"varint,10,opt,name=click,proto3" json:"click,omitempty"`
	LastUpdate           int64    `protobuf:"varint,11,opt,name=last_update,json=lastUpdate,proto3" json:"last_update,omitempty"`
	PickupTime           int64    `protobuf:"varint,12,opt,name=pickup_time,json=pickupTime,proto3" json:"pickup_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushTaskStat) Reset()         { *m = PushTaskStat{} }
func (m *PushTaskStat) String() string { return proto.CompactTextString(m) }
func (*PushTaskStat) ProtoMessage()    {}
func (*PushTaskStat) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{21}
}
func (m *PushTaskStat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushTaskStat.Unmarshal(m, b)
}
func (m *PushTaskStat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushTaskStat.Marshal(b, m, deterministic)
}
func (dst *PushTaskStat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushTaskStat.Merge(dst, src)
}
func (m *PushTaskStat) XXX_Size() int {
	return xxx_messageInfo_PushTaskStat.Size(m)
}
func (m *PushTaskStat) XXX_DiscardUnknown() {
	xxx_messageInfo_PushTaskStat.DiscardUnknown(m)
}

var xxx_messageInfo_PushTaskStat proto.InternalMessageInfo

func (m *PushTaskStat) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *PushTaskStat) GetTokenType() string {
	if m != nil {
		return m.TokenType
	}
	return ""
}

func (m *PushTaskStat) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *PushTaskStat) GetSelect() int64 {
	if m != nil {
		return m.Select
	}
	return 0
}

func (m *PushTaskStat) GetStored() int64 {
	if m != nil {
		return m.Stored
	}
	return 0
}

func (m *PushTaskStat) GetQueued() int64 {
	if m != nil {
		return m.Queued
	}
	return 0
}

func (m *PushTaskStat) GetTarget() int64 {
	if m != nil {
		return m.Target
	}
	return 0
}

func (m *PushTaskStat) GetSubmit() int64 {
	if m != nil {
		return m.Submit
	}
	return 0
}

func (m *PushTaskStat) GetArrive() int64 {
	if m != nil {
		return m.Arrive
	}
	return 0
}

func (m *PushTaskStat) GetClick() int64 {
	if m != nil {
		return m.Click
	}
	return 0
}

func (m *PushTaskStat) GetLastUpdate() int64 {
	if m != nil {
		return m.LastUpdate
	}
	return 0
}

func (m *PushTaskStat) GetPickupTime() int64 {
	if m != nil {
		return m.PickupTime
	}
	return 0
}

type SaveTaskStatReq struct {
	StatList             []*PushTaskStat `protobuf:"bytes,1,rep,name=stat_list,json=statList,proto3" json:"stat_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SaveTaskStatReq) Reset()         { *m = SaveTaskStatReq{} }
func (m *SaveTaskStatReq) String() string { return proto.CompactTextString(m) }
func (*SaveTaskStatReq) ProtoMessage()    {}
func (*SaveTaskStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{22}
}
func (m *SaveTaskStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveTaskStatReq.Unmarshal(m, b)
}
func (m *SaveTaskStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveTaskStatReq.Marshal(b, m, deterministic)
}
func (dst *SaveTaskStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveTaskStatReq.Merge(dst, src)
}
func (m *SaveTaskStatReq) XXX_Size() int {
	return xxx_messageInfo_SaveTaskStatReq.Size(m)
}
func (m *SaveTaskStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveTaskStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_SaveTaskStatReq proto.InternalMessageInfo

func (m *SaveTaskStatReq) GetStatList() []*PushTaskStat {
	if m != nil {
		return m.StatList
	}
	return nil
}

type SaveTaskStatResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveTaskStatResp) Reset()         { *m = SaveTaskStatResp{} }
func (m *SaveTaskStatResp) String() string { return proto.CompactTextString(m) }
func (*SaveTaskStatResp) ProtoMessage()    {}
func (*SaveTaskStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{23}
}
func (m *SaveTaskStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveTaskStatResp.Unmarshal(m, b)
}
func (m *SaveTaskStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveTaskStatResp.Marshal(b, m, deterministic)
}
func (dst *SaveTaskStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveTaskStatResp.Merge(dst, src)
}
func (m *SaveTaskStatResp) XXX_Size() int {
	return xxx_messageInfo_SaveTaskStatResp.Size(m)
}
func (m *SaveTaskStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveTaskStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_SaveTaskStatResp proto.InternalMessageInfo

type PushStatValue struct {
	Day                  string   `protobuf:"bytes,1,opt,name=day,proto3" json:"day,omitempty"`
	PushType             int32    `protobuf:"varint,2,opt,name=push_type,json=pushType,proto3" json:"push_type,omitempty"`
	CreateTime           int64    `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Received             int64    `protobuf:"varint,4,opt,name=received,proto3" json:"received,omitempty"`
	Queued               int64    `protobuf:"varint,5,opt,name=queued,proto3" json:"queued,omitempty"`
	Target               int64    `protobuf:"varint,6,opt,name=target,proto3" json:"target,omitempty"`
	Submit               int64    `protobuf:"varint,7,opt,name=submit,proto3" json:"submit,omitempty"`
	Arrive               int64    `protobuf:"varint,8,opt,name=arrive,proto3" json:"arrive,omitempty"`
	Click                int64    `protobuf:"varint,9,opt,name=click,proto3" json:"click,omitempty"`
	Online               int64    `protobuf:"varint,10,opt,name=online,proto3" json:"online,omitempty"`
	Hw                   int64    `protobuf:"varint,11,opt,name=hw,proto3" json:"hw,omitempty"`
	Xm                   int64    `protobuf:"varint,12,opt,name=xm,proto3" json:"xm,omitempty"`
	Op                   int64    `protobuf:"varint,13,opt,name=op,proto3" json:"op,omitempty"`
	Vv                   int64    `protobuf:"varint,14,opt,name=vv,proto3" json:"vv,omitempty"`
	Apns                 int64    `protobuf:"varint,15,opt,name=apns,proto3" json:"apns,omitempty"`
	Other                int64    `protobuf:"varint,16,opt,name=other,proto3" json:"other,omitempty"`
	OnlineAr             int64    `protobuf:"varint,17,opt,name=online_ar,json=onlineAr,proto3" json:"online_ar,omitempty"`
	GtAr                 int64    `protobuf:"varint,18,opt,name=gt_ar,json=gtAr,proto3" json:"gt_ar,omitempty"`
	ApnsAr               int64    `protobuf:"varint,19,opt,name=apns_ar,json=apnsAr,proto3" json:"apns_ar,omitempty"`
	HwAr                 int64    `protobuf:"varint,20,opt,name=hw_ar,json=hwAr,proto3" json:"hw_ar,omitempty"`
	XmAr                 int64    `protobuf:"varint,21,opt,name=xm_ar,json=xmAr,proto3" json:"xm_ar,omitempty"`
	OpAr                 int64    `protobuf:"varint,22,opt,name=op_ar,json=opAr,proto3" json:"op_ar,omitempty"`
	VvAr                 int64    `protobuf:"varint,23,opt,name=vv_ar,json=vvAr,proto3" json:"vv_ar,omitempty"`
	OtherAr              int64    `protobuf:"varint,24,opt,name=other_ar,json=otherAr,proto3" json:"other_ar,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushStatValue) Reset()         { *m = PushStatValue{} }
func (m *PushStatValue) String() string { return proto.CompactTextString(m) }
func (*PushStatValue) ProtoMessage()    {}
func (*PushStatValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{24}
}
func (m *PushStatValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushStatValue.Unmarshal(m, b)
}
func (m *PushStatValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushStatValue.Marshal(b, m, deterministic)
}
func (dst *PushStatValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushStatValue.Merge(dst, src)
}
func (m *PushStatValue) XXX_Size() int {
	return xxx_messageInfo_PushStatValue.Size(m)
}
func (m *PushStatValue) XXX_DiscardUnknown() {
	xxx_messageInfo_PushStatValue.DiscardUnknown(m)
}

var xxx_messageInfo_PushStatValue proto.InternalMessageInfo

func (m *PushStatValue) GetDay() string {
	if m != nil {
		return m.Day
	}
	return ""
}

func (m *PushStatValue) GetPushType() int32 {
	if m != nil {
		return m.PushType
	}
	return 0
}

func (m *PushStatValue) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *PushStatValue) GetReceived() int64 {
	if m != nil {
		return m.Received
	}
	return 0
}

func (m *PushStatValue) GetQueued() int64 {
	if m != nil {
		return m.Queued
	}
	return 0
}

func (m *PushStatValue) GetTarget() int64 {
	if m != nil {
		return m.Target
	}
	return 0
}

func (m *PushStatValue) GetSubmit() int64 {
	if m != nil {
		return m.Submit
	}
	return 0
}

func (m *PushStatValue) GetArrive() int64 {
	if m != nil {
		return m.Arrive
	}
	return 0
}

func (m *PushStatValue) GetClick() int64 {
	if m != nil {
		return m.Click
	}
	return 0
}

func (m *PushStatValue) GetOnline() int64 {
	if m != nil {
		return m.Online
	}
	return 0
}

func (m *PushStatValue) GetHw() int64 {
	if m != nil {
		return m.Hw
	}
	return 0
}

func (m *PushStatValue) GetXm() int64 {
	if m != nil {
		return m.Xm
	}
	return 0
}

func (m *PushStatValue) GetOp() int64 {
	if m != nil {
		return m.Op
	}
	return 0
}

func (m *PushStatValue) GetVv() int64 {
	if m != nil {
		return m.Vv
	}
	return 0
}

func (m *PushStatValue) GetApns() int64 {
	if m != nil {
		return m.Apns
	}
	return 0
}

func (m *PushStatValue) GetOther() int64 {
	if m != nil {
		return m.Other
	}
	return 0
}

func (m *PushStatValue) GetOnlineAr() int64 {
	if m != nil {
		return m.OnlineAr
	}
	return 0
}

func (m *PushStatValue) GetGtAr() int64 {
	if m != nil {
		return m.GtAr
	}
	return 0
}

func (m *PushStatValue) GetApnsAr() int64 {
	if m != nil {
		return m.ApnsAr
	}
	return 0
}

func (m *PushStatValue) GetHwAr() int64 {
	if m != nil {
		return m.HwAr
	}
	return 0
}

func (m *PushStatValue) GetXmAr() int64 {
	if m != nil {
		return m.XmAr
	}
	return 0
}

func (m *PushStatValue) GetOpAr() int64 {
	if m != nil {
		return m.OpAr
	}
	return 0
}

func (m *PushStatValue) GetVvAr() int64 {
	if m != nil {
		return m.VvAr
	}
	return 0
}

func (m *PushStatValue) GetOtherAr() int64 {
	if m != nil {
		return m.OtherAr
	}
	return 0
}

type UpdatePushTotalStatReq struct {
	TotalStat            map[string]*PushStatValue `protobuf:"bytes,1,rep,name=total_stat,json=totalStat,proto3" json:"total_stat,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *UpdatePushTotalStatReq) Reset()         { *m = UpdatePushTotalStatReq{} }
func (m *UpdatePushTotalStatReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePushTotalStatReq) ProtoMessage()    {}
func (*UpdatePushTotalStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{25}
}
func (m *UpdatePushTotalStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePushTotalStatReq.Unmarshal(m, b)
}
func (m *UpdatePushTotalStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePushTotalStatReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePushTotalStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePushTotalStatReq.Merge(dst, src)
}
func (m *UpdatePushTotalStatReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePushTotalStatReq.Size(m)
}
func (m *UpdatePushTotalStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePushTotalStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePushTotalStatReq proto.InternalMessageInfo

func (m *UpdatePushTotalStatReq) GetTotalStat() map[string]*PushStatValue {
	if m != nil {
		return m.TotalStat
	}
	return nil
}

type PushStatValueV2 struct {
	Day        string `protobuf:"bytes,1,opt,name=day,proto3" json:"day,omitempty"`
	PushType   int32  `protobuf:"varint,2,opt,name=push_type,json=pushType,proto3" json:"push_type,omitempty"`
	CreateTime int64  `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 各环节发送数量，key值: pushCore.SectionType
	SectionCount map[int32]int64 `protobuf:"bytes,7,rep,name=section_count,json=sectionCount,proto3" json:"section_count,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// 各手机厂家或在线通道下发数量，key值: pushCore.FunnelChannelType
	ChannelSendCount map[int32]int64 `protobuf:"bytes,8,rep,name=channel_send_count,json=channelSendCount,proto3" json:"channel_send_count,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// 各手机厂家或在线通道到达数量，依赖客户端ack上报，key值: pushCore.FunnelChannelType
	ChannelArrivedCount  map[int32]int64 `protobuf:"bytes,9,rep,name=channel_arrived_count,json=channelArrivedCount,proto3" json:"channel_arrived_count,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *PushStatValueV2) Reset()         { *m = PushStatValueV2{} }
func (m *PushStatValueV2) String() string { return proto.CompactTextString(m) }
func (*PushStatValueV2) ProtoMessage()    {}
func (*PushStatValueV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{26}
}
func (m *PushStatValueV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushStatValueV2.Unmarshal(m, b)
}
func (m *PushStatValueV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushStatValueV2.Marshal(b, m, deterministic)
}
func (dst *PushStatValueV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushStatValueV2.Merge(dst, src)
}
func (m *PushStatValueV2) XXX_Size() int {
	return xxx_messageInfo_PushStatValueV2.Size(m)
}
func (m *PushStatValueV2) XXX_DiscardUnknown() {
	xxx_messageInfo_PushStatValueV2.DiscardUnknown(m)
}

var xxx_messageInfo_PushStatValueV2 proto.InternalMessageInfo

func (m *PushStatValueV2) GetDay() string {
	if m != nil {
		return m.Day
	}
	return ""
}

func (m *PushStatValueV2) GetPushType() int32 {
	if m != nil {
		return m.PushType
	}
	return 0
}

func (m *PushStatValueV2) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *PushStatValueV2) GetSectionCount() map[int32]int64 {
	if m != nil {
		return m.SectionCount
	}
	return nil
}

func (m *PushStatValueV2) GetChannelSendCount() map[int32]int64 {
	if m != nil {
		return m.ChannelSendCount
	}
	return nil
}

func (m *PushStatValueV2) GetChannelArrivedCount() map[int32]int64 {
	if m != nil {
		return m.ChannelArrivedCount
	}
	return nil
}

type UpdatePushTotalStatReqV2 struct {
	TotalStat            map[string]*PushStatValueV2 `protobuf:"bytes,1,rep,name=total_stat,json=totalStat,proto3" json:"total_stat,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *UpdatePushTotalStatReqV2) Reset()         { *m = UpdatePushTotalStatReqV2{} }
func (m *UpdatePushTotalStatReqV2) String() string { return proto.CompactTextString(m) }
func (*UpdatePushTotalStatReqV2) ProtoMessage()    {}
func (*UpdatePushTotalStatReqV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{27}
}
func (m *UpdatePushTotalStatReqV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePushTotalStatReqV2.Unmarshal(m, b)
}
func (m *UpdatePushTotalStatReqV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePushTotalStatReqV2.Marshal(b, m, deterministic)
}
func (dst *UpdatePushTotalStatReqV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePushTotalStatReqV2.Merge(dst, src)
}
func (m *UpdatePushTotalStatReqV2) XXX_Size() int {
	return xxx_messageInfo_UpdatePushTotalStatReqV2.Size(m)
}
func (m *UpdatePushTotalStatReqV2) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePushTotalStatReqV2.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePushTotalStatReqV2 proto.InternalMessageInfo

func (m *UpdatePushTotalStatReqV2) GetTotalStat() map[string]*PushStatValueV2 {
	if m != nil {
		return m.TotalStat
	}
	return nil
}

type UpdatePushTotalStatResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePushTotalStatResp) Reset()         { *m = UpdatePushTotalStatResp{} }
func (m *UpdatePushTotalStatResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePushTotalStatResp) ProtoMessage()    {}
func (*UpdatePushTotalStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{28}
}
func (m *UpdatePushTotalStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePushTotalStatResp.Unmarshal(m, b)
}
func (m *UpdatePushTotalStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePushTotalStatResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePushTotalStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePushTotalStatResp.Merge(dst, src)
}
func (m *UpdatePushTotalStatResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePushTotalStatResp.Size(m)
}
func (m *UpdatePushTotalStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePushTotalStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePushTotalStatResp proto.InternalMessageInfo

type TaskStatValue struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	CreateTime           int64    `protobuf:"varint,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	StartTime            int64    `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	UpdateTime           int64    `protobuf:"varint,4,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Selected             int64    `protobuf:"varint,5,opt,name=selected,proto3" json:"selected,omitempty"`
	Received             int64    `protobuf:"varint,6,opt,name=received,proto3" json:"received,omitempty"`
	Queued               int64    `protobuf:"varint,7,opt,name=queued,proto3" json:"queued,omitempty"`
	Target               int64    `protobuf:"varint,8,opt,name=target,proto3" json:"target,omitempty"`
	Submit               int64    `protobuf:"varint,9,opt,name=submit,proto3" json:"submit,omitempty"`
	Arrive               int64    `protobuf:"varint,10,opt,name=arrive,proto3" json:"arrive,omitempty"`
	Click                int64    `protobuf:"varint,11,opt,name=click,proto3" json:"click,omitempty"`
	Online               int64    `protobuf:"varint,12,opt,name=online,proto3" json:"online,omitempty"`
	Hw                   int64    `protobuf:"varint,13,opt,name=hw,proto3" json:"hw,omitempty"`
	Xm                   int64    `protobuf:"varint,14,opt,name=xm,proto3" json:"xm,omitempty"`
	Op                   int64    `protobuf:"varint,15,opt,name=op,proto3" json:"op,omitempty"`
	Vv                   int64    `protobuf:"varint,16,opt,name=vv,proto3" json:"vv,omitempty"`
	Apns                 int64    `protobuf:"varint,17,opt,name=apns,proto3" json:"apns,omitempty"`
	Other                int64    `protobuf:"varint,18,opt,name=other,proto3" json:"other,omitempty"`
	EndTime              int64    `protobuf:"varint,19,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskStatValue) Reset()         { *m = TaskStatValue{} }
func (m *TaskStatValue) String() string { return proto.CompactTextString(m) }
func (*TaskStatValue) ProtoMessage()    {}
func (*TaskStatValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{29}
}
func (m *TaskStatValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskStatValue.Unmarshal(m, b)
}
func (m *TaskStatValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskStatValue.Marshal(b, m, deterministic)
}
func (dst *TaskStatValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskStatValue.Merge(dst, src)
}
func (m *TaskStatValue) XXX_Size() int {
	return xxx_messageInfo_TaskStatValue.Size(m)
}
func (m *TaskStatValue) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskStatValue.DiscardUnknown(m)
}

var xxx_messageInfo_TaskStatValue proto.InternalMessageInfo

func (m *TaskStatValue) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *TaskStatValue) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *TaskStatValue) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *TaskStatValue) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *TaskStatValue) GetSelected() int64 {
	if m != nil {
		return m.Selected
	}
	return 0
}

func (m *TaskStatValue) GetReceived() int64 {
	if m != nil {
		return m.Received
	}
	return 0
}

func (m *TaskStatValue) GetQueued() int64 {
	if m != nil {
		return m.Queued
	}
	return 0
}

func (m *TaskStatValue) GetTarget() int64 {
	if m != nil {
		return m.Target
	}
	return 0
}

func (m *TaskStatValue) GetSubmit() int64 {
	if m != nil {
		return m.Submit
	}
	return 0
}

func (m *TaskStatValue) GetArrive() int64 {
	if m != nil {
		return m.Arrive
	}
	return 0
}

func (m *TaskStatValue) GetClick() int64 {
	if m != nil {
		return m.Click
	}
	return 0
}

func (m *TaskStatValue) GetOnline() int64 {
	if m != nil {
		return m.Online
	}
	return 0
}

func (m *TaskStatValue) GetHw() int64 {
	if m != nil {
		return m.Hw
	}
	return 0
}

func (m *TaskStatValue) GetXm() int64 {
	if m != nil {
		return m.Xm
	}
	return 0
}

func (m *TaskStatValue) GetOp() int64 {
	if m != nil {
		return m.Op
	}
	return 0
}

func (m *TaskStatValue) GetVv() int64 {
	if m != nil {
		return m.Vv
	}
	return 0
}

func (m *TaskStatValue) GetApns() int64 {
	if m != nil {
		return m.Apns
	}
	return 0
}

func (m *TaskStatValue) GetOther() int64 {
	if m != nil {
		return m.Other
	}
	return 0
}

func (m *TaskStatValue) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type UpdateTaskStatReq struct {
	TaskStat             map[string]*TaskStatValue `protobuf:"bytes,1,rep,name=task_stat,json=taskStat,proto3" json:"task_stat,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *UpdateTaskStatReq) Reset()         { *m = UpdateTaskStatReq{} }
func (m *UpdateTaskStatReq) String() string { return proto.CompactTextString(m) }
func (*UpdateTaskStatReq) ProtoMessage()    {}
func (*UpdateTaskStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{30}
}
func (m *UpdateTaskStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTaskStatReq.Unmarshal(m, b)
}
func (m *UpdateTaskStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTaskStatReq.Marshal(b, m, deterministic)
}
func (dst *UpdateTaskStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTaskStatReq.Merge(dst, src)
}
func (m *UpdateTaskStatReq) XXX_Size() int {
	return xxx_messageInfo_UpdateTaskStatReq.Size(m)
}
func (m *UpdateTaskStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTaskStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTaskStatReq proto.InternalMessageInfo

func (m *UpdateTaskStatReq) GetTaskStat() map[string]*TaskStatValue {
	if m != nil {
		return m.TaskStat
	}
	return nil
}

type TaskStatValueV2 struct {
	TaskId     string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	CreateTime int64  `protobuf:"varint,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime int64  `protobuf:"varint,3,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	StartTime  int64  `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime    int64  `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 各环节发送数量，key值: pushCore.SectionType
	SectionCount map[int32]int64 `protobuf:"bytes,6,rep,name=section_count,json=sectionCount,proto3" json:"section_count,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// 各手机厂家或在线通道下发数量，key值: pushCore.FunnelChannelType
	ChannelSendCount     map[int32]int64 `protobuf:"bytes,7,rep,name=channel_send_count,json=channelSendCount,proto3" json:"channel_send_count,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *TaskStatValueV2) Reset()         { *m = TaskStatValueV2{} }
func (m *TaskStatValueV2) String() string { return proto.CompactTextString(m) }
func (*TaskStatValueV2) ProtoMessage()    {}
func (*TaskStatValueV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{31}
}
func (m *TaskStatValueV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskStatValueV2.Unmarshal(m, b)
}
func (m *TaskStatValueV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskStatValueV2.Marshal(b, m, deterministic)
}
func (dst *TaskStatValueV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskStatValueV2.Merge(dst, src)
}
func (m *TaskStatValueV2) XXX_Size() int {
	return xxx_messageInfo_TaskStatValueV2.Size(m)
}
func (m *TaskStatValueV2) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskStatValueV2.DiscardUnknown(m)
}

var xxx_messageInfo_TaskStatValueV2 proto.InternalMessageInfo

func (m *TaskStatValueV2) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *TaskStatValueV2) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *TaskStatValueV2) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *TaskStatValueV2) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *TaskStatValueV2) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *TaskStatValueV2) GetSectionCount() map[int32]int64 {
	if m != nil {
		return m.SectionCount
	}
	return nil
}

func (m *TaskStatValueV2) GetChannelSendCount() map[int32]int64 {
	if m != nil {
		return m.ChannelSendCount
	}
	return nil
}

type UpdateTaskStatReqV2 struct {
	TaskStat             map[string]*TaskStatValueV2 `protobuf:"bytes,1,rep,name=task_stat,json=taskStat,proto3" json:"task_stat,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *UpdateTaskStatReqV2) Reset()         { *m = UpdateTaskStatReqV2{} }
func (m *UpdateTaskStatReqV2) String() string { return proto.CompactTextString(m) }
func (*UpdateTaskStatReqV2) ProtoMessage()    {}
func (*UpdateTaskStatReqV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{32}
}
func (m *UpdateTaskStatReqV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTaskStatReqV2.Unmarshal(m, b)
}
func (m *UpdateTaskStatReqV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTaskStatReqV2.Marshal(b, m, deterministic)
}
func (dst *UpdateTaskStatReqV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTaskStatReqV2.Merge(dst, src)
}
func (m *UpdateTaskStatReqV2) XXX_Size() int {
	return xxx_messageInfo_UpdateTaskStatReqV2.Size(m)
}
func (m *UpdateTaskStatReqV2) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTaskStatReqV2.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTaskStatReqV2 proto.InternalMessageInfo

func (m *UpdateTaskStatReqV2) GetTaskStat() map[string]*TaskStatValueV2 {
	if m != nil {
		return m.TaskStat
	}
	return nil
}

type UpdateTaskStatResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTaskStatResp) Reset()         { *m = UpdateTaskStatResp{} }
func (m *UpdateTaskStatResp) String() string { return proto.CompactTextString(m) }
func (*UpdateTaskStatResp) ProtoMessage()    {}
func (*UpdateTaskStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{33}
}
func (m *UpdateTaskStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTaskStatResp.Unmarshal(m, b)
}
func (m *UpdateTaskStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTaskStatResp.Marshal(b, m, deterministic)
}
func (dst *UpdateTaskStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTaskStatResp.Merge(dst, src)
}
func (m *UpdateTaskStatResp) XXX_Size() int {
	return xxx_messageInfo_UpdateTaskStatResp.Size(m)
}
func (m *UpdateTaskStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTaskStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTaskStatResp proto.InternalMessageInfo

type UpdateTaskTimeReq struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	StartTime            int64    `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTaskTimeReq) Reset()         { *m = UpdateTaskTimeReq{} }
func (m *UpdateTaskTimeReq) String() string { return proto.CompactTextString(m) }
func (*UpdateTaskTimeReq) ProtoMessage()    {}
func (*UpdateTaskTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{34}
}
func (m *UpdateTaskTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTaskTimeReq.Unmarshal(m, b)
}
func (m *UpdateTaskTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTaskTimeReq.Marshal(b, m, deterministic)
}
func (dst *UpdateTaskTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTaskTimeReq.Merge(dst, src)
}
func (m *UpdateTaskTimeReq) XXX_Size() int {
	return xxx_messageInfo_UpdateTaskTimeReq.Size(m)
}
func (m *UpdateTaskTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTaskTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTaskTimeReq proto.InternalMessageInfo

func (m *UpdateTaskTimeReq) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *UpdateTaskTimeReq) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

type UpdateTaskTimeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTaskTimeResp) Reset()         { *m = UpdateTaskTimeResp{} }
func (m *UpdateTaskTimeResp) String() string { return proto.CompactTextString(m) }
func (*UpdateTaskTimeResp) ProtoMessage()    {}
func (*UpdateTaskTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{35}
}
func (m *UpdateTaskTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTaskTimeResp.Unmarshal(m, b)
}
func (m *UpdateTaskTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTaskTimeResp.Marshal(b, m, deterministic)
}
func (dst *UpdateTaskTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTaskTimeResp.Merge(dst, src)
}
func (m *UpdateTaskTimeResp) XXX_Size() int {
	return xxx_messageInfo_UpdateTaskTimeResp.Size(m)
}
func (m *UpdateTaskTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTaskTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTaskTimeResp proto.InternalMessageInfo

type TaskTime struct {
	StartTime            int64    `protobuf:"varint,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64    `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskTime) Reset()         { *m = TaskTime{} }
func (m *TaskTime) String() string { return proto.CompactTextString(m) }
func (*TaskTime) ProtoMessage()    {}
func (*TaskTime) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{36}
}
func (m *TaskTime) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskTime.Unmarshal(m, b)
}
func (m *TaskTime) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskTime.Marshal(b, m, deterministic)
}
func (dst *TaskTime) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskTime.Merge(dst, src)
}
func (m *TaskTime) XXX_Size() int {
	return xxx_messageInfo_TaskTime.Size(m)
}
func (m *TaskTime) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskTime.DiscardUnknown(m)
}

var xxx_messageInfo_TaskTime proto.InternalMessageInfo

func (m *TaskTime) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *TaskTime) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type UpdateFunnelTaskTimeReq struct {
	TaskTime             map[string]*TaskTime `protobuf:"bytes,1,rep,name=task_time,json=taskTime,proto3" json:"task_time,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UpdateFunnelTaskTimeReq) Reset()         { *m = UpdateFunnelTaskTimeReq{} }
func (m *UpdateFunnelTaskTimeReq) String() string { return proto.CompactTextString(m) }
func (*UpdateFunnelTaskTimeReq) ProtoMessage()    {}
func (*UpdateFunnelTaskTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{37}
}
func (m *UpdateFunnelTaskTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFunnelTaskTimeReq.Unmarshal(m, b)
}
func (m *UpdateFunnelTaskTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFunnelTaskTimeReq.Marshal(b, m, deterministic)
}
func (dst *UpdateFunnelTaskTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFunnelTaskTimeReq.Merge(dst, src)
}
func (m *UpdateFunnelTaskTimeReq) XXX_Size() int {
	return xxx_messageInfo_UpdateFunnelTaskTimeReq.Size(m)
}
func (m *UpdateFunnelTaskTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFunnelTaskTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFunnelTaskTimeReq proto.InternalMessageInfo

func (m *UpdateFunnelTaskTimeReq) GetTaskTime() map[string]*TaskTime {
	if m != nil {
		return m.TaskTime
	}
	return nil
}

type UpdateFunnelTaskTimeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateFunnelTaskTimeResp) Reset()         { *m = UpdateFunnelTaskTimeResp{} }
func (m *UpdateFunnelTaskTimeResp) String() string { return proto.CompactTextString(m) }
func (*UpdateFunnelTaskTimeResp) ProtoMessage()    {}
func (*UpdateFunnelTaskTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{38}
}
func (m *UpdateFunnelTaskTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFunnelTaskTimeResp.Unmarshal(m, b)
}
func (m *UpdateFunnelTaskTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFunnelTaskTimeResp.Marshal(b, m, deterministic)
}
func (dst *UpdateFunnelTaskTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFunnelTaskTimeResp.Merge(dst, src)
}
func (m *UpdateFunnelTaskTimeResp) XXX_Size() int {
	return xxx_messageInfo_UpdateFunnelTaskTimeResp.Size(m)
}
func (m *UpdateFunnelTaskTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFunnelTaskTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFunnelTaskTimeResp proto.InternalMessageInfo

type CheckTotalStatReq struct {
	Days                 int32    `protobuf:"varint,1,opt,name=days,proto3" json:"days,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckTotalStatReq) Reset()         { *m = CheckTotalStatReq{} }
func (m *CheckTotalStatReq) String() string { return proto.CompactTextString(m) }
func (*CheckTotalStatReq) ProtoMessage()    {}
func (*CheckTotalStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{39}
}
func (m *CheckTotalStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTotalStatReq.Unmarshal(m, b)
}
func (m *CheckTotalStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTotalStatReq.Marshal(b, m, deterministic)
}
func (dst *CheckTotalStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTotalStatReq.Merge(dst, src)
}
func (m *CheckTotalStatReq) XXX_Size() int {
	return xxx_messageInfo_CheckTotalStatReq.Size(m)
}
func (m *CheckTotalStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTotalStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTotalStatReq proto.InternalMessageInfo

func (m *CheckTotalStatReq) GetDays() int32 {
	if m != nil {
		return m.Days
	}
	return 0
}

type CheckTotalStatResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckTotalStatResp) Reset()         { *m = CheckTotalStatResp{} }
func (m *CheckTotalStatResp) String() string { return proto.CompactTextString(m) }
func (*CheckTotalStatResp) ProtoMessage()    {}
func (*CheckTotalStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_stats_87643f9611804350, []int{40}
}
func (m *CheckTotalStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTotalStatResp.Unmarshal(m, b)
}
func (m *CheckTotalStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTotalStatResp.Marshal(b, m, deterministic)
}
func (dst *CheckTotalStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTotalStatResp.Merge(dst, src)
}
func (m *CheckTotalStatResp) XXX_Size() int {
	return xxx_messageInfo_CheckTotalStatResp.Size(m)
}
func (m *CheckTotalStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTotalStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTotalStatResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*BaseStatics)(nil), "PushNotification.stats.BaseStatics")
	proto.RegisterType((*BaseStatics_Total)(nil), "PushNotification.stats.BaseStatics.Total")
	proto.RegisterType((*BaseStatics_Detail)(nil), "PushNotification.stats.BaseStatics.Detail")
	proto.RegisterType((*BaseStatics_Action)(nil), "PushNotification.stats.BaseStatics.Action")
	proto.RegisterType((*BasePushCount)(nil), "PushNotification.stats.BasePushCount")
	proto.RegisterType((*QueryTasksReq)(nil), "PushNotification.stats.QueryTasksReq")
	proto.RegisterType((*QueryTasksResp)(nil), "PushNotification.stats.QueryTasksResp")
	proto.RegisterType((*QueryTaskGroupReq)(nil), "PushNotification.stats.QueryTaskGroupReq")
	proto.RegisterType((*QueryTaskGroupResp)(nil), "PushNotification.stats.QueryTaskGroupResp")
	proto.RegisterType((*QueryPushCountReq)(nil), "PushNotification.stats.QueryPushCountReq")
	proto.RegisterType((*QueryPushCountResp)(nil), "PushNotification.stats.QueryPushCountResp")
	proto.RegisterType((*QueryPushDataByDayReq)(nil), "PushNotification.stats.QueryPushDataByDayReq")
	proto.RegisterType((*QueryPushDataByDayResp)(nil), "PushNotification.stats.QueryPushDataByDayResp")
	proto.RegisterType((*QueryUserDataByDayReq)(nil), "PushNotification.stats.QueryUserDataByDayReq")
	proto.RegisterType((*QueryUserDataByDayResp)(nil), "PushNotification.stats.QueryUserDataByDayResp")
	proto.RegisterType((*QueryOnlineUserBy24HReq)(nil), "PushNotification.stats.QueryOnlineUserBy24HReq")
	proto.RegisterType((*QueryOnlineUserBy24HResp)(nil), "PushNotification.stats.QueryOnlineUserBy24HResp")
	proto.RegisterType((*QueryOnlineUserBy24HResp_OnlineInfo)(nil), "PushNotification.stats.QueryOnlineUserBy24HResp.OnlineInfo")
	proto.RegisterType((*QueryTaskScheduleReq)(nil), "PushNotification.stats.QueryTaskScheduleReq")
	proto.RegisterType((*QueryTaskScheduleResp)(nil), "PushNotification.stats.QueryTaskScheduleResp")
	proto.RegisterType((*QueryTaskScheduleResp_ScheduleInfo)(nil), "PushNotification.stats.QueryTaskScheduleResp.ScheduleInfo")
	proto.RegisterType((*QueryTaskDetailReq)(nil), "PushNotification.stats.QueryTaskDetailReq")
	proto.RegisterType((*QueryTaskDetailResp)(nil), "PushNotification.stats.QueryTaskDetailResp")
	proto.RegisterType((*QueryTaskDetailResp_EventInfo)(nil), "PushNotification.stats.QueryTaskDetailResp.EventInfo")
	proto.RegisterType((*PushMsgInfo)(nil), "PushNotification.stats.PushMsgInfo")
	proto.RegisterType((*SaveTaskIdReq)(nil), "PushNotification.stats.SaveTaskIdReq")
	proto.RegisterType((*SaveTaskIdResp)(nil), "PushNotification.stats.SaveTaskIdResp")
	proto.RegisterType((*PushTaskStat)(nil), "PushNotification.stats.PushTaskStat")
	proto.RegisterType((*SaveTaskStatReq)(nil), "PushNotification.stats.SaveTaskStatReq")
	proto.RegisterType((*SaveTaskStatResp)(nil), "PushNotification.stats.SaveTaskStatResp")
	proto.RegisterType((*PushStatValue)(nil), "PushNotification.stats.PushStatValue")
	proto.RegisterType((*UpdatePushTotalStatReq)(nil), "PushNotification.stats.UpdatePushTotalStatReq")
	proto.RegisterMapType((map[string]*PushStatValue)(nil), "PushNotification.stats.UpdatePushTotalStatReq.TotalStatEntry")
	proto.RegisterType((*PushStatValueV2)(nil), "PushNotification.stats.PushStatValueV2")
	proto.RegisterMapType((map[int32]int64)(nil), "PushNotification.stats.PushStatValueV2.ChannelArrivedCountEntry")
	proto.RegisterMapType((map[int32]int64)(nil), "PushNotification.stats.PushStatValueV2.ChannelSendCountEntry")
	proto.RegisterMapType((map[int32]int64)(nil), "PushNotification.stats.PushStatValueV2.SectionCountEntry")
	proto.RegisterType((*UpdatePushTotalStatReqV2)(nil), "PushNotification.stats.UpdatePushTotalStatReqV2")
	proto.RegisterMapType((map[string]*PushStatValueV2)(nil), "PushNotification.stats.UpdatePushTotalStatReqV2.TotalStatEntry")
	proto.RegisterType((*UpdatePushTotalStatResp)(nil), "PushNotification.stats.UpdatePushTotalStatResp")
	proto.RegisterType((*TaskStatValue)(nil), "PushNotification.stats.TaskStatValue")
	proto.RegisterType((*UpdateTaskStatReq)(nil), "PushNotification.stats.UpdateTaskStatReq")
	proto.RegisterMapType((map[string]*TaskStatValue)(nil), "PushNotification.stats.UpdateTaskStatReq.TaskStatEntry")
	proto.RegisterType((*TaskStatValueV2)(nil), "PushNotification.stats.TaskStatValueV2")
	proto.RegisterMapType((map[int32]int64)(nil), "PushNotification.stats.TaskStatValueV2.ChannelSendCountEntry")
	proto.RegisterMapType((map[int32]int64)(nil), "PushNotification.stats.TaskStatValueV2.SectionCountEntry")
	proto.RegisterType((*UpdateTaskStatReqV2)(nil), "PushNotification.stats.UpdateTaskStatReqV2")
	proto.RegisterMapType((map[string]*TaskStatValueV2)(nil), "PushNotification.stats.UpdateTaskStatReqV2.TaskStatEntry")
	proto.RegisterType((*UpdateTaskStatResp)(nil), "PushNotification.stats.UpdateTaskStatResp")
	proto.RegisterType((*UpdateTaskTimeReq)(nil), "PushNotification.stats.UpdateTaskTimeReq")
	proto.RegisterType((*UpdateTaskTimeResp)(nil), "PushNotification.stats.UpdateTaskTimeResp")
	proto.RegisterType((*TaskTime)(nil), "PushNotification.stats.TaskTime")
	proto.RegisterType((*UpdateFunnelTaskTimeReq)(nil), "PushNotification.stats.UpdateFunnelTaskTimeReq")
	proto.RegisterMapType((map[string]*TaskTime)(nil), "PushNotification.stats.UpdateFunnelTaskTimeReq.TaskTimeEntry")
	proto.RegisterType((*UpdateFunnelTaskTimeResp)(nil), "PushNotification.stats.UpdateFunnelTaskTimeResp")
	proto.RegisterType((*CheckTotalStatReq)(nil), "PushNotification.stats.CheckTotalStatReq")
	proto.RegisterType((*CheckTotalStatResp)(nil), "PushNotification.stats.CheckTotalStatResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PushStatsClient is the client API for PushStats service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PushStatsClient interface {
	QueryTasks(ctx context.Context, in *QueryTasksReq, opts ...grpc.CallOption) (*QueryTasksResp, error)
	QueryTaskGroup(ctx context.Context, in *QueryTaskGroupReq, opts ...grpc.CallOption) (*QueryTaskGroupResp, error)
	QueryPushCount(ctx context.Context, in *QueryPushCountReq, opts ...grpc.CallOption) (*QueryPushCountResp, error)
	QueryPushDataByDay(ctx context.Context, in *QueryPushDataByDayReq, opts ...grpc.CallOption) (*QueryPushDataByDayResp, error)
	QueryUserDataByDay(ctx context.Context, in *QueryUserDataByDayReq, opts ...grpc.CallOption) (*QueryUserDataByDayResp, error)
	QueryOnlineUserBy24H(ctx context.Context, in *QueryOnlineUserBy24HReq, opts ...grpc.CallOption) (*QueryOnlineUserBy24HResp, error)
	QueryTaskSchedule(ctx context.Context, in *QueryTaskScheduleReq, opts ...grpc.CallOption) (*QueryTaskScheduleResp, error)
	QueryTaskDetail(ctx context.Context, in *QueryTaskDetailReq, opts ...grpc.CallOption) (*QueryTaskDetailResp, error)
	SaveTaskId(ctx context.Context, in *SaveTaskIdReq, opts ...grpc.CallOption) (*SaveTaskIdResp, error)
	// 弃用，改用UpdatePushTotalStatV2
	UpdatePushTotalStat(ctx context.Context, in *UpdatePushTotalStatReq, opts ...grpc.CallOption) (*UpdatePushTotalStatResp, error)
	// 弃用，改用UpdateTaskStatV2
	UpdateTaskStat(ctx context.Context, in *UpdateTaskStatReq, opts ...grpc.CallOption) (*UpdateTaskStatResp, error)
	UpdatePushTotalStatV2(ctx context.Context, in *UpdatePushTotalStatReqV2, opts ...grpc.CallOption) (*UpdatePushTotalStatResp, error)
	UpdateTaskStatV2(ctx context.Context, in *UpdateTaskStatReqV2, opts ...grpc.CallOption) (*UpdateTaskStatResp, error)
	UpdateFunnelTaskTime(ctx context.Context, in *UpdateFunnelTaskTimeReq, opts ...grpc.CallOption) (*UpdateFunnelTaskTimeResp, error)
	CheckTotalStat(ctx context.Context, in *CheckTotalStatReq, opts ...grpc.CallOption) (*CheckTotalStatResp, error)
}

type pushStatsClient struct {
	cc *grpc.ClientConn
}

func NewPushStatsClient(cc *grpc.ClientConn) PushStatsClient {
	return &pushStatsClient{cc}
}

func (c *pushStatsClient) QueryTasks(ctx context.Context, in *QueryTasksReq, opts ...grpc.CallOption) (*QueryTasksResp, error) {
	out := new(QueryTasksResp)
	err := c.cc.Invoke(ctx, "/PushNotification.stats.PushStats/QueryTasks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushStatsClient) QueryTaskGroup(ctx context.Context, in *QueryTaskGroupReq, opts ...grpc.CallOption) (*QueryTaskGroupResp, error) {
	out := new(QueryTaskGroupResp)
	err := c.cc.Invoke(ctx, "/PushNotification.stats.PushStats/QueryTaskGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushStatsClient) QueryPushCount(ctx context.Context, in *QueryPushCountReq, opts ...grpc.CallOption) (*QueryPushCountResp, error) {
	out := new(QueryPushCountResp)
	err := c.cc.Invoke(ctx, "/PushNotification.stats.PushStats/QueryPushCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushStatsClient) QueryPushDataByDay(ctx context.Context, in *QueryPushDataByDayReq, opts ...grpc.CallOption) (*QueryPushDataByDayResp, error) {
	out := new(QueryPushDataByDayResp)
	err := c.cc.Invoke(ctx, "/PushNotification.stats.PushStats/QueryPushDataByDay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushStatsClient) QueryUserDataByDay(ctx context.Context, in *QueryUserDataByDayReq, opts ...grpc.CallOption) (*QueryUserDataByDayResp, error) {
	out := new(QueryUserDataByDayResp)
	err := c.cc.Invoke(ctx, "/PushNotification.stats.PushStats/QueryUserDataByDay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushStatsClient) QueryOnlineUserBy24H(ctx context.Context, in *QueryOnlineUserBy24HReq, opts ...grpc.CallOption) (*QueryOnlineUserBy24HResp, error) {
	out := new(QueryOnlineUserBy24HResp)
	err := c.cc.Invoke(ctx, "/PushNotification.stats.PushStats/QueryOnlineUserBy24H", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushStatsClient) QueryTaskSchedule(ctx context.Context, in *QueryTaskScheduleReq, opts ...grpc.CallOption) (*QueryTaskScheduleResp, error) {
	out := new(QueryTaskScheduleResp)
	err := c.cc.Invoke(ctx, "/PushNotification.stats.PushStats/QueryTaskSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushStatsClient) QueryTaskDetail(ctx context.Context, in *QueryTaskDetailReq, opts ...grpc.CallOption) (*QueryTaskDetailResp, error) {
	out := new(QueryTaskDetailResp)
	err := c.cc.Invoke(ctx, "/PushNotification.stats.PushStats/QueryTaskDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushStatsClient) SaveTaskId(ctx context.Context, in *SaveTaskIdReq, opts ...grpc.CallOption) (*SaveTaskIdResp, error) {
	out := new(SaveTaskIdResp)
	err := c.cc.Invoke(ctx, "/PushNotification.stats.PushStats/SaveTaskId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushStatsClient) UpdatePushTotalStat(ctx context.Context, in *UpdatePushTotalStatReq, opts ...grpc.CallOption) (*UpdatePushTotalStatResp, error) {
	out := new(UpdatePushTotalStatResp)
	err := c.cc.Invoke(ctx, "/PushNotification.stats.PushStats/UpdatePushTotalStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushStatsClient) UpdateTaskStat(ctx context.Context, in *UpdateTaskStatReq, opts ...grpc.CallOption) (*UpdateTaskStatResp, error) {
	out := new(UpdateTaskStatResp)
	err := c.cc.Invoke(ctx, "/PushNotification.stats.PushStats/UpdateTaskStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushStatsClient) UpdatePushTotalStatV2(ctx context.Context, in *UpdatePushTotalStatReqV2, opts ...grpc.CallOption) (*UpdatePushTotalStatResp, error) {
	out := new(UpdatePushTotalStatResp)
	err := c.cc.Invoke(ctx, "/PushNotification.stats.PushStats/UpdatePushTotalStatV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushStatsClient) UpdateTaskStatV2(ctx context.Context, in *UpdateTaskStatReqV2, opts ...grpc.CallOption) (*UpdateTaskStatResp, error) {
	out := new(UpdateTaskStatResp)
	err := c.cc.Invoke(ctx, "/PushNotification.stats.PushStats/UpdateTaskStatV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushStatsClient) UpdateFunnelTaskTime(ctx context.Context, in *UpdateFunnelTaskTimeReq, opts ...grpc.CallOption) (*UpdateFunnelTaskTimeResp, error) {
	out := new(UpdateFunnelTaskTimeResp)
	err := c.cc.Invoke(ctx, "/PushNotification.stats.PushStats/UpdateFunnelTaskTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushStatsClient) CheckTotalStat(ctx context.Context, in *CheckTotalStatReq, opts ...grpc.CallOption) (*CheckTotalStatResp, error) {
	out := new(CheckTotalStatResp)
	err := c.cc.Invoke(ctx, "/PushNotification.stats.PushStats/CheckTotalStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PushStatsServer is the server API for PushStats service.
type PushStatsServer interface {
	QueryTasks(context.Context, *QueryTasksReq) (*QueryTasksResp, error)
	QueryTaskGroup(context.Context, *QueryTaskGroupReq) (*QueryTaskGroupResp, error)
	QueryPushCount(context.Context, *QueryPushCountReq) (*QueryPushCountResp, error)
	QueryPushDataByDay(context.Context, *QueryPushDataByDayReq) (*QueryPushDataByDayResp, error)
	QueryUserDataByDay(context.Context, *QueryUserDataByDayReq) (*QueryUserDataByDayResp, error)
	QueryOnlineUserBy24H(context.Context, *QueryOnlineUserBy24HReq) (*QueryOnlineUserBy24HResp, error)
	QueryTaskSchedule(context.Context, *QueryTaskScheduleReq) (*QueryTaskScheduleResp, error)
	QueryTaskDetail(context.Context, *QueryTaskDetailReq) (*QueryTaskDetailResp, error)
	SaveTaskId(context.Context, *SaveTaskIdReq) (*SaveTaskIdResp, error)
	// 弃用，改用UpdatePushTotalStatV2
	UpdatePushTotalStat(context.Context, *UpdatePushTotalStatReq) (*UpdatePushTotalStatResp, error)
	// 弃用，改用UpdateTaskStatV2
	UpdateTaskStat(context.Context, *UpdateTaskStatReq) (*UpdateTaskStatResp, error)
	UpdatePushTotalStatV2(context.Context, *UpdatePushTotalStatReqV2) (*UpdatePushTotalStatResp, error)
	UpdateTaskStatV2(context.Context, *UpdateTaskStatReqV2) (*UpdateTaskStatResp, error)
	UpdateFunnelTaskTime(context.Context, *UpdateFunnelTaskTimeReq) (*UpdateFunnelTaskTimeResp, error)
	CheckTotalStat(context.Context, *CheckTotalStatReq) (*CheckTotalStatResp, error)
}

func RegisterPushStatsServer(s *grpc.Server, srv PushStatsServer) {
	s.RegisterService(&_PushStats_serviceDesc, srv)
}

func _PushStats_QueryTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryTasksReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushStatsServer).QueryTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.stats.PushStats/QueryTasks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushStatsServer).QueryTasks(ctx, req.(*QueryTasksReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushStats_QueryTaskGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryTaskGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushStatsServer).QueryTaskGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.stats.PushStats/QueryTaskGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushStatsServer).QueryTaskGroup(ctx, req.(*QueryTaskGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushStats_QueryPushCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryPushCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushStatsServer).QueryPushCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.stats.PushStats/QueryPushCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushStatsServer).QueryPushCount(ctx, req.(*QueryPushCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushStats_QueryPushDataByDay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryPushDataByDayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushStatsServer).QueryPushDataByDay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.stats.PushStats/QueryPushDataByDay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushStatsServer).QueryPushDataByDay(ctx, req.(*QueryPushDataByDayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushStats_QueryUserDataByDay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryUserDataByDayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushStatsServer).QueryUserDataByDay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.stats.PushStats/QueryUserDataByDay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushStatsServer).QueryUserDataByDay(ctx, req.(*QueryUserDataByDayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushStats_QueryOnlineUserBy24H_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryOnlineUserBy24HReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushStatsServer).QueryOnlineUserBy24H(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.stats.PushStats/QueryOnlineUserBy24H",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushStatsServer).QueryOnlineUserBy24H(ctx, req.(*QueryOnlineUserBy24HReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushStats_QueryTaskSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryTaskScheduleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushStatsServer).QueryTaskSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.stats.PushStats/QueryTaskSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushStatsServer).QueryTaskSchedule(ctx, req.(*QueryTaskScheduleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushStats_QueryTaskDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryTaskDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushStatsServer).QueryTaskDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.stats.PushStats/QueryTaskDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushStatsServer).QueryTaskDetail(ctx, req.(*QueryTaskDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushStats_SaveTaskId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveTaskIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushStatsServer).SaveTaskId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.stats.PushStats/SaveTaskId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushStatsServer).SaveTaskId(ctx, req.(*SaveTaskIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushStats_UpdatePushTotalStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePushTotalStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushStatsServer).UpdatePushTotalStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.stats.PushStats/UpdatePushTotalStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushStatsServer).UpdatePushTotalStat(ctx, req.(*UpdatePushTotalStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushStats_UpdateTaskStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTaskStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushStatsServer).UpdateTaskStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.stats.PushStats/UpdateTaskStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushStatsServer).UpdateTaskStat(ctx, req.(*UpdateTaskStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushStats_UpdatePushTotalStatV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePushTotalStatReqV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushStatsServer).UpdatePushTotalStatV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.stats.PushStats/UpdatePushTotalStatV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushStatsServer).UpdatePushTotalStatV2(ctx, req.(*UpdatePushTotalStatReqV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushStats_UpdateTaskStatV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTaskStatReqV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushStatsServer).UpdateTaskStatV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.stats.PushStats/UpdateTaskStatV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushStatsServer).UpdateTaskStatV2(ctx, req.(*UpdateTaskStatReqV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushStats_UpdateFunnelTaskTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFunnelTaskTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushStatsServer).UpdateFunnelTaskTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.stats.PushStats/UpdateFunnelTaskTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushStatsServer).UpdateFunnelTaskTime(ctx, req.(*UpdateFunnelTaskTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushStats_CheckTotalStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckTotalStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushStatsServer).CheckTotalStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.stats.PushStats/CheckTotalStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushStatsServer).CheckTotalStat(ctx, req.(*CheckTotalStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PushStats_serviceDesc = grpc.ServiceDesc{
	ServiceName: "PushNotification.stats.PushStats",
	HandlerType: (*PushStatsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryTasks",
			Handler:    _PushStats_QueryTasks_Handler,
		},
		{
			MethodName: "QueryTaskGroup",
			Handler:    _PushStats_QueryTaskGroup_Handler,
		},
		{
			MethodName: "QueryPushCount",
			Handler:    _PushStats_QueryPushCount_Handler,
		},
		{
			MethodName: "QueryPushDataByDay",
			Handler:    _PushStats_QueryPushDataByDay_Handler,
		},
		{
			MethodName: "QueryUserDataByDay",
			Handler:    _PushStats_QueryUserDataByDay_Handler,
		},
		{
			MethodName: "QueryOnlineUserBy24H",
			Handler:    _PushStats_QueryOnlineUserBy24H_Handler,
		},
		{
			MethodName: "QueryTaskSchedule",
			Handler:    _PushStats_QueryTaskSchedule_Handler,
		},
		{
			MethodName: "QueryTaskDetail",
			Handler:    _PushStats_QueryTaskDetail_Handler,
		},
		{
			MethodName: "SaveTaskId",
			Handler:    _PushStats_SaveTaskId_Handler,
		},
		{
			MethodName: "UpdatePushTotalStat",
			Handler:    _PushStats_UpdatePushTotalStat_Handler,
		},
		{
			MethodName: "UpdateTaskStat",
			Handler:    _PushStats_UpdateTaskStat_Handler,
		},
		{
			MethodName: "UpdatePushTotalStatV2",
			Handler:    _PushStats_UpdatePushTotalStatV2_Handler,
		},
		{
			MethodName: "UpdateTaskStatV2",
			Handler:    _PushStats_UpdateTaskStatV2_Handler,
		},
		{
			MethodName: "UpdateFunnelTaskTime",
			Handler:    _PushStats_UpdateFunnelTaskTime_Handler,
		},
		{
			MethodName: "CheckTotalStat",
			Handler:    _PushStats_CheckTotalStat_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "push-notification/v3/push-stats.proto",
}

func init() {
	proto.RegisterFile("push-notification/v3/push-stats.proto", fileDescriptor_push_stats_87643f9611804350)
}

var fileDescriptor_push_stats_87643f9611804350 = []byte{
	// 2376 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x5a, 0xcf, 0x6f, 0x1b, 0xc7,
	0xf5, 0x17, 0x49, 0x89, 0x3f, 0x1e, 0xf5, 0x73, 0x6c, 0xc9, 0x34, 0x83, 0xc0, 0xfe, 0xee, 0xb7,
	0x4e, 0x6c, 0xa7, 0xa6, 0x0c, 0xa6, 0x4e, 0x9a, 0x18, 0x86, 0x23, 0x5b, 0x71, 0x2b, 0xe4, 0x87,
	0x5b, 0x4a, 0xd6, 0xc1, 0x48, 0x42, 0xac, 0x97, 0x63, 0x72, 0x23, 0x72, 0xb9, 0xda, 0x99, 0xa5,
	0x45, 0x38, 0x7f, 0x43, 0xff, 0x82, 0x9e, 0x7a, 0x28, 0x7a, 0x29, 0xd0, 0xbf, 0xa3, 0x40, 0xd1,
	0x43, 0x73, 0x2b, 0x7a, 0xe8, 0xa9, 0xf7, 0xde, 0x7a, 0x2b, 0xde, 0x9b, 0x59, 0x72, 0x76, 0xc9,
	0xa5, 0x48, 0xab, 0x0d, 0x7a, 0xdb, 0xf7, 0x99, 0x99, 0xf7, 0x7b, 0xe6, 0xcd, 0x3c, 0x12, 0x6e,
	0xf8, 0xa1, 0xe8, 0xdc, 0xf1, 0xfa, 0xd2, 0x7d, 0xe9, 0x3a, 0xb6, 0x74, 0xfb, 0xde, 0xee, 0xe0,
	0xfd, 0x5d, 0x02, 0x85, 0xb4, 0xa5, 0xa8, 0xf9, 0x41, 0x5f, 0xf6, 0xd9, 0xce, 0x2f, 0x42, 0xd1,
	0xf9, 0xd2, 0x98, 0x55, 0xa3, 0xd1, 0xea, 0x3b, 0x33, 0x96, 0xf3, 0x60, 0xc0, 0x03, 0xb5, 0xde,
	0xfa, 0xdd, 0x32, 0x94, 0x1f, 0xd9, 0x82, 0x1f, 0x4a, 0x5b, 0xba, 0x8e, 0x60, 0x0f, 0x61, 0x45,
	0xf6, 0xa5, 0xdd, 0xad, 0x64, 0xae, 0x67, 0x6e, 0x96, 0xeb, 0xb7, 0x6a, 0xd3, 0xf9, 0xd7, 0x8c,
	0x35, 0xb5, 0x23, 0x5c, 0xd0, 0x50, 0xeb, 0xd8, 0x67, 0x50, 0x6e, 0x71, 0x69, 0xbb, 0xdd, 0x66,
	0xd7, 0x15, 0xb2, 0x92, 0xbd, 0x9e, 0xbb, 0x59, 0xae, 0xdf, 0x9e, 0x87, 0xcd, 0x3e, 0x2d, 0x6b,
	0x80, 0x5a, 0xfe, 0xb9, 0x2b, 0x24, 0x32, 0xb3, 0x1d, 0x9c, 0xae, 0x98, 0xe5, 0xe6, 0x67, 0xb6,
	0x47, 0xcb, 0x1a, 0xa0, 0x96, 0x23, 0xb3, 0xea, 0x6b, 0x58, 0x21, 0x4d, 0xd9, 0x0e, 0xe4, 0xa5,
	0x1d, 0xb4, 0xb9, 0x24, 0x23, 0x57, 0x1a, 0x9a, 0x62, 0x15, 0x28, 0x04, 0xdc, 0xe1, 0xee, 0x80,
	0x57, 0xb2, 0x34, 0x10, 0x91, 0x38, 0xd2, 0x72, 0x85, 0xdf, 0xb5, 0x87, 0x95, 0x9c, 0x1a, 0xd1,
	0x24, 0xbb, 0x0c, 0x2b, 0x4e, 0xd7, 0x75, 0x4e, 0x2a, 0xcb, 0x84, 0x2b, 0x82, 0x6d, 0x42, 0xae,
	0x27, 0xda, 0x95, 0x15, 0xc2, 0xf0, 0xb3, 0xfa, 0xab, 0x0c, 0xe4, 0x95, 0x81, 0x3f, 0x80, 0x78,
	0x0b, 0x56, 0x7b, 0xb6, 0x17, 0xbe, 0xb4, 0x1d, 0x19, 0x06, 0x3c, 0x20, 0x3d, 0x4a, 0x8d, 0x18,
	0x56, 0xbd, 0x0b, 0x79, 0xe5, 0x23, 0x54, 0xf6, 0x84, 0x0f, 0x49, 0x99, 0x52, 0x03, 0x3f, 0x91,
	0xeb, 0xc0, 0xee, 0x86, 0x91, 0x1e, 0x8a, 0xb0, 0xfe, 0x98, 0x81, 0x35, 0x74, 0x31, 0x7a, 0xff,
	0x71, 0x3f, 0xf4, 0xe4, 0x84, 0x9c, 0xcc, 0xa4, 0x1c, 0xf6, 0x7f, 0xb0, 0xda, 0xe2, 0xc2, 0x09,
	0xdc, 0x17, 0xbc, 0x29, 0xed, 0x36, 0xb1, 0x2c, 0x35, 0xca, 0x11, 0x76, 0x64, 0xb7, 0xd9, 0x5b,
	0x50, 0xa2, 0xdc, 0x69, 0x7a, 0x61, 0x4f, 0x1b, 0x58, 0x24, 0xe0, 0xcb, 0xb0, 0xc7, 0xae, 0x42,
	0x11, 0xb3, 0x96, 0xc6, 0x94, 0x91, 0x05, 0xa4, 0x71, 0xe8, 0x6d, 0x80, 0x80, 0xf7, 0x6c, 0xd7,
	0xa3, 0x41, 0xe5, 0xec, 0x92, 0x42, 0x70, 0xf8, 0x2d, 0x28, 0x75, 0x6c, 0xd1, 0xec, 0xba, 0x3d,
	0x57, 0x56, 0xf2, 0xd7, 0x33, 0x37, 0x8b, 0x8d, 0x62, 0xc7, 0x16, 0x9f, 0x23, 0x6d, 0x75, 0x60,
	0xed, 0x97, 0x21, 0x0f, 0x86, 0x47, 0xb6, 0x38, 0x11, 0x0d, 0x7e, 0x8a, 0xcc, 0x48, 0x8e, 0xdd,
	0xe6, 0x9e, 0xd4, 0x96, 0x94, 0x10, 0xd9, 0x43, 0x00, 0x99, 0xbd, 0x08, 0xbd, 0x56, 0x97, 0x37,
	0xdd, 0x96, 0xb6, 0xa1, 0xa8, 0x80, 0x83, 0x16, 0x19, 0x60, 0x8b, 0x93, 0x71, 0x92, 0x96, 0x1a,
	0x45, 0x04, 0x30, 0xed, 0xac, 0x10, 0xd6, 0x4d, 0x49, 0xc2, 0x8f, 0x4f, 0xcf, 0xc4, 0xa7, 0xb3,
	0x27, 0xb0, 0x2a, 0x54, 0x0e, 0x9b, 0x1b, 0xe8, 0xff, 0xe7, 0xc8, 0xf9, 0x46, 0x59, 0x2f, 0x24,
	0xb1, 0xdf, 0xc2, 0xd6, 0x48, 0xec, 0xcf, 0x82, 0x7e, 0xe8, 0x5f, 0xd4, 0xc8, 0xab, 0x50, 0x6c,
	0x23, 0x1f, 0x1c, 0xcb, 0xd1, 0x58, 0x81, 0xe8, 0x83, 0x96, 0x75, 0x08, 0x2c, 0x29, 0x4b, 0xf8,
	0xec, 0x01, 0x14, 0xb4, 0x42, 0xfa, 0x30, 0x99, 0xcb, 0x88, 0x68, 0x8d, 0xf5, 0x54, 0x1b, 0x30,
	0x4a, 0xb7, 0x0b, 0x1a, 0x60, 0x3d, 0xd7, 0x5a, 0x1a, 0x0c, 0x85, 0xcf, 0xf6, 0x01, 0x1c, 0x24,
	0xc6, 0xd1, 0x28, 0xd7, 0x6f, 0xcc, 0x52, 0x74, 0xbc, 0xbc, 0x44, 0x0b, 0xc9, 0xdb, 0x12, 0xb6,
	0x47, 0xbc, 0xf7, 0x6d, 0x69, 0x3f, 0x1a, 0xee, 0xdb, 0xc3, 0x8b, 0x7a, 0xfc, 0x1a, 0x94, 0x43,
	0xcf, 0x3d, 0x6b, 0x0a, 0xee, 0xf4, 0x3d, 0xe5, 0xf4, 0x5c, 0x03, 0x10, 0x3a, 0x24, 0xc4, 0x3a,
	0x81, 0x9d, 0x69, 0x52, 0x85, 0xcf, 0x18, 0x2c, 0xb7, 0x6c, 0xc9, 0xb5, 0x40, 0xfa, 0x36, 0xe3,
	0x91, 0x7d, 0x83, 0x78, 0x44, 0x26, 0x3e, 0x13, 0x3c, 0xf8, 0xe1, 0x4c, 0xfc, 0x75, 0x46, 0xdb,
	0x98, 0x10, 0x9b, 0x62, 0xa3, 0x05, 0xab, 0xb6, 0xe3, 0x84, 0xbd, 0xb0, 0x6b, 0xcb, 0xf1, 0x41,
	0x1a, 0xc3, 0x58, 0x15, 0x8a, 0x01, 0x6f, 0xbb, 0x42, 0xf2, 0x20, 0x3a, 0x6d, 0x22, 0x1a, 0xcf,
	0x66, 0xac, 0x18, 0x03, 0xae, 0xcf, 0x1a, 0x4d, 0x21, 0xde, 0xf7, 0xba, 0xae, 0xc7, 0xf5, 0x31,
	0xa3, 0x29, 0xeb, 0x19, 0x5c, 0x21, 0xed, 0x9e, 0x12, 0x89, 0x3a, 0x3e, 0x1a, 0xd6, 0x7f, 0xf2,
	0xf3, 0x8b, 0xa6, 0xea, 0xdf, 0x32, 0x50, 0x99, 0xce, 0x37, 0xc5, 0xee, 0xaf, 0xa0, 0xac, 0x34,
	0x32, 0x0f, 0x8d, 0xfb, 0x69, 0xf1, 0x4d, 0x63, 0x5d, 0x53, 0xd8, 0x81, 0xf7, 0xb2, 0xdf, 0x00,
	0xc5, 0x8f, 0x2a, 0xe7, 0x53, 0x80, 0xf1, 0x08, 0xbb, 0x05, 0x9b, 0x14, 0xb3, 0x9e, 0xdb, 0xed,
	0xba, 0x3a, 0x70, 0x19, 0x0a, 0xdc, 0x06, 0xe2, 0x5f, 0x8c, 0x61, 0xc3, 0x6d, 0xd9, 0x98, 0xdb,
	0x4e, 0xe0, 0xf2, 0xe8, 0xc0, 0x38, 0x74, 0x3a, 0xbc, 0x15, 0x76, 0xf9, 0x45, 0x53, 0xe9, 0x0a,
	0x14, 0xe8, 0x54, 0x1d, 0x1d, 0x4f, 0x79, 0x24, 0x0f, 0x5a, 0xd6, 0x1f, 0x32, 0x3a, 0x73, 0xe3,
	0xd2, 0x84, 0xcf, 0x9a, 0xb0, 0x26, 0x34, 0x6d, 0x6e, 0xff, 0x8f, 0x67, 0xfa, 0x2d, 0xc9, 0xa5,
	0x16, 0x11, 0xe4, 0xb6, 0xd5, 0x88, 0x21, 0x39, 0xee, 0x03, 0x58, 0x35, 0x47, 0x31, 0x74, 0xae,
	0xe4, 0xbd, 0x28, 0x74, 0xf8, 0x4d, 0xe1, 0xe4, 0xc2, 0xd1, 0xf6, 0xd0, 0xb7, 0xf5, 0x9d, 0x71,
	0xa0, 0xea, 0x6b, 0xd1, 0x7f, 0xc9, 0x3b, 0x6c, 0x0b, 0x96, 0x1d, 0x44, 0x97, 0x55, 0xf9, 0x77,
	0x0e, 0x5a, 0xd6, 0x6f, 0x32, 0x70, 0x69, 0x42, 0xbc, 0xf0, 0xd9, 0x11, 0x00, 0x1f, 0xf0, 0xf8,
	0x51, 0x79, 0xef, 0x5c, 0x5f, 0x8d, 0x19, 0xd4, 0x3e, 0xc5, 0xd5, 0xe4, 0xa6, 0x12, 0x31, 0x22,
	0x1f, 0xdd, 0x83, 0xd2, 0x08, 0x47, 0x67, 0x48, 0xb7, 0x37, 0xca, 0x6d, 0xfc, 0xc6, 0xdb, 0x08,
	0xcd, 0xd6, 0x36, 0x29, 0xc2, 0xfa, 0x47, 0x06, 0xca, 0x28, 0xfa, 0x0b, 0xd1, 0xa6, 0x95, 0x86,
	0x81, 0x99, 0xa4, 0x81, 0x72, 0xec, 0x91, 0x9c, 0x34, 0x6c, 0xce, 0x8d, 0x6c, 0x46, 0x28, 0x8c,
	0xdc, 0xb0, 0xd6, 0xc8, 0x85, 0x07, 0x2d, 0xf6, 0x21, 0x90, 0x73, 0x9b, 0x72, 0xe8, 0xab, 0x6d,
	0xbf, 0x5e, 0xaf, 0x4e, 0x5a, 0x8b, 0xc0, 0xd1, 0xd0, 0xe7, 0x0d, 0xba, 0xa6, 0xe0, 0x17, 0xdb,
	0x86, 0xbc, 0xed, 0x53, 0x9d, 0xcc, 0x2b, 0x8d, 0x6d, 0xdf, 0x57, 0x21, 0x08, 0x05, 0x0f, 0x9a,
	0xae, 0x5f, 0x29, 0x28, 0x0d, 0x91, 0x3c, 0xf0, 0x31, 0xae, 0x34, 0xa0, 0xe2, 0x5a, 0x54, 0x71,
	0x45, 0x84, 0xe2, 0x6a, 0x7d, 0x0b, 0x6b, 0x87, 0xf6, 0x80, 0x1f, 0x91, 0x39, 0x98, 0x07, 0xa9,
	0xa6, 0x7e, 0x02, 0x25, 0xd7, 0x7b, 0xd9, 0x9f, 0xeb, 0xe2, 0x60, 0xf8, 0xae, 0x51, 0xc4, 0x55,
	0x54, 0xc7, 0x36, 0x61, 0xdd, 0x94, 0x25, 0x7c, 0xeb, 0xcf, 0x59, 0x58, 0x25, 0x1b, 0x31, 0xed,
	0xa5, 0x2d, 0xd3, 0xa5, 0xbf, 0x0d, 0x20, 0xfb, 0x27, 0xdc, 0x53, 0x0e, 0x53, 0xee, 0x2e, 0x11,
	0x42, 0x5e, 0xb9, 0x06, 0x65, 0x27, 0xe0, 0xb6, 0xe4, 0x4d, 0x8a, 0xb0, 0x3e, 0xea, 0x15, 0x74,
	0x84, 0x71, 0xde, 0x81, 0xbc, 0xe0, 0x5d, 0xee, 0x48, 0x0a, 0x42, 0xae, 0xa1, 0x29, 0xc2, 0x65,
	0x3f, 0xe0, 0x2d, 0x0a, 0x02, 0xe2, 0x44, 0x21, 0x7e, 0x1a, 0xf2, 0x90, 0x2b, 0x37, 0xe7, 0x1a,
	0x9a, 0x32, 0xee, 0xd7, 0x05, 0x85, 0xeb, 0xfb, 0x35, 0xf2, 0x09, 0x5f, 0xe0, 0x65, 0xb0, 0xa8,
	0xf9, 0x10, 0x45, 0x67, 0x7e, 0x10, 0xe0, 0x99, 0x5f, 0x52, 0xb8, 0xa2, 0xc6, 0x77, 0x6b, 0x20,
	0x58, 0xdf, 0xad, 0xaf, 0x41, 0xb9, 0x6b, 0x0b, 0xd9, 0x0c, 0x7d, 0x3a, 0x84, 0xcb, 0xca, 0x0c,
	0x84, 0x9e, 0x11, 0x82, 0x13, 0x7c, 0xd7, 0x39, 0x09, 0x7d, 0x65, 0xe7, 0xaa, 0x9a, 0xa0, 0x20,
	0xb4, 0xd3, 0x3a, 0x82, 0x8d, 0xc8, 0xc7, 0xe8, 0x50, 0x8c, 0xe8, 0x1e, 0x94, 0x30, 0x2a, 0xe6,
	0xc6, 0xfa, 0xd1, 0xac, 0xc0, 0x8d, 0xd6, 0x16, 0x11, 0xa3, 0xc8, 0x31, 0xd8, 0x8c, 0x73, 0x15,
	0xbe, 0xf5, 0xfb, 0x65, 0x58, 0xc3, 0xe9, 0x08, 0x1c, 0xe3, 0x1d, 0x1e, 0xef, 0xfa, 0x2d, 0x7b,
	0x74, 0xd7, 0x6f, 0xd9, 0x43, 0x3c, 0x35, 0xc6, 0x59, 0xae, 0x4e, 0xe9, 0x71, 0x26, 0x9f, 0x1b,
	0x33, 0xaa, 0xa5, 0xf4, 0x48, 0x69, 0xe9, 0xa8, 0x8d, 0x68, 0x23, 0x3e, 0x2b, 0x29, 0xf1, 0xc9,
	0xa7, 0xc4, 0xa7, 0x90, 0x12, 0x9f, 0xe2, 0xf4, 0xf8, 0x94, 0xcc, 0xf8, 0x8c, 0x4b, 0x8e, 0x0a,
	0x9b, 0xa6, 0xd8, 0x3a, 0x64, 0x3b, 0xaf, 0x74, 0xb8, 0xb2, 0x9d, 0x57, 0x48, 0x9f, 0xf5, 0x74,
	0x74, 0xb2, 0x67, 0x3d, 0xa4, 0xfb, 0x7e, 0x65, 0x4d, 0xd1, 0x7d, 0x1f, 0xe9, 0xc1, 0xa0, 0xb2,
	0xae, 0xe8, 0xc1, 0x00, 0x4f, 0x26, 0xdb, 0xf7, 0x44, 0x65, 0x83, 0x10, 0xfa, 0x46, 0x0d, 0xfa,
	0xb2, 0xc3, 0x83, 0xca, 0xa6, 0xd2, 0x80, 0x08, 0xf4, 0xa8, 0xae, 0xc5, 0x76, 0x50, 0xd9, 0x52,
	0x4e, 0x51, 0xc0, 0x5e, 0xc0, 0x2e, 0xc1, 0x4a, 0x5b, 0xe2, 0x00, 0x53, 0x7c, 0xda, 0x72, 0x2f,
	0xc0, 0x2d, 0x85, 0xfc, 0x10, 0xbe, 0xa4, 0x4d, 0xf4, 0x3d, 0xa1, 0x66, 0x77, 0x5e, 0x21, 0x7c,
	0x59, 0xcd, 0xee, 0xbc, 0x52, 0xe0, 0x59, 0x0f, 0xc1, 0x6d, 0x05, 0x9e, 0xf5, 0x14, 0xd8, 0xf7,
	0x11, 0xdc, 0x51, 0x60, 0xdf, 0x57, 0xe0, 0x60, 0x80, 0xe0, 0x15, 0x05, 0x0e, 0x06, 0x7b, 0x01,
	0xde, 0xe3, 0x49, 0x4f, 0xc4, 0x2b, 0x84, 0x17, 0x88, 0xde, 0x0b, 0xac, 0xbf, 0x66, 0x60, 0x47,
	0x65, 0x31, 0x25, 0x19, 0x3e, 0xc1, 0xa2, 0x0c, 0xfd, 0x0a, 0x37, 0x37, 0xbe, 0xd1, 0x30, 0xe1,
	0x74, 0x8a, 0x3e, 0x48, 0x4b, 0xd1, 0xe9, 0x3c, 0x6a, 0x23, 0xe2, 0x53, 0x4f, 0x06, 0xc3, 0x86,
	0x7a, 0xf4, 0x21, 0x5d, 0x75, 0x60, 0x3d, 0x3e, 0x38, 0xe5, 0x51, 0x7a, 0xdf, 0x7c, 0x94, 0xce,
	0xb8, 0xa3, 0xc7, 0x12, 0x5e, 0xbf, 0x5d, 0x3f, 0xce, 0xfe, 0x34, 0x63, 0xfd, 0x73, 0x19, 0x36,
	0x62, 0x83, 0xc7, 0xf5, 0xff, 0xf8, 0x7e, 0xf8, 0x06, 0xd6, 0x04, 0x57, 0x1d, 0x0b, 0x7a, 0x1c,
	0x54, 0x0a, 0xe4, 0xa9, 0x8f, 0xe6, 0x52, 0xf6, 0xb8, 0x5e, 0x3b, 0x54, 0x8b, 0xe9, 0x7d, 0xa1,
	0xbc, 0xb4, 0x2a, 0x0c, 0x88, 0x9d, 0x00, 0x73, 0x3a, 0xb6, 0xe7, 0xf1, 0x6e, 0x53, 0x70, 0xaf,
	0xa5, 0x85, 0x14, 0x67, 0x87, 0x23, 0x29, 0xe4, 0xb1, 0xe2, 0x70, 0xc8, 0xbd, 0x96, 0x21, 0x68,
	0xd3, 0x49, 0xc0, 0x4c, 0xc2, 0x76, 0x24, 0x4c, 0x6d, 0xb9, 0x48, 0x5e, 0x89, 0xe4, 0x7d, 0xb2,
	0xa0, 0xbc, 0x3d, 0xc5, 0xc3, 0x10, 0x79, 0xc9, 0x99, 0x1c, 0xa9, 0x3e, 0x84, 0xad, 0x09, 0x2f,
	0x98, 0xe9, 0xb0, 0x32, 0xa5, 0x47, 0x91, 0x33, 0xe2, 0x5c, 0x7d, 0x0c, 0xdb, 0x53, 0x2d, 0x5c,
	0x88, 0xc9, 0x13, 0xa8, 0xa4, 0xa9, 0xbd, 0x08, 0x1f, 0xeb, 0xef, 0x19, 0xa8, 0x4c, 0xdf, 0x0e,
	0xc7, 0x75, 0xf6, 0xcd, 0x94, 0x4d, 0xf5, 0x70, 0xb1, 0x4d, 0x75, 0x5c, 0x9f, 0xb1, 0xad, 0xf8,
	0x1c, 0xdb, 0xea, 0x41, 0x7c, 0x5b, 0xbd, 0x3b, 0x67, 0x50, 0x4d, 0x1b, 0xaf, 0xc2, 0x95, 0xa9,
	0xca, 0x09, 0xdf, 0xfa, 0x3e, 0x07, 0x6b, 0x51, 0x49, 0x52, 0x15, 0x28, 0xf5, 0xfa, 0x90, 0xd8,
	0x5b, 0xd9, 0x89, 0xbd, 0xf5, 0x36, 0x80, 0x90, 0x76, 0x20, 0xcd, 0xbd, 0x57, 0x22, 0x84, 0x86,
	0xf1, 0x29, 0x49, 0x5a, 0xa8, 0xf1, 0x65, 0xfd, 0x94, 0x24, 0x28, 0xaa, 0x55, 0xea, 0x46, 0x31,
	0xaa, 0x48, 0x23, 0x3a, 0x56, 0xc7, 0xf2, 0xa9, 0x75, 0xac, 0x90, 0x52, 0xc7, 0x8a, 0x29, 0x75,
	0xac, 0x94, 0x52, 0xc7, 0x60, 0x7a, 0x1d, 0x2b, 0x4f, 0xaf, 0x63, 0xab, 0x53, 0xea, 0xd8, 0x5a,
	0xa2, 0x8e, 0xad, 0x27, 0xea, 0xd8, 0x46, 0xa2, 0x8e, 0x6d, 0x4e, 0xd4, 0xb1, 0xad, 0x69, 0x75,
	0x8c, 0x99, 0x75, 0xec, 0x2a, 0x14, 0xf1, 0x88, 0x21, 0x6f, 0xaa, 0xb2, 0x54, 0xe0, 0x5e, 0x8b,
	0xae, 0x30, 0x7f, 0xca, 0xc0, 0x96, 0x0a, 0xb9, 0x79, 0x8b, 0x39, 0xd2, 0x7d, 0x2d, 0x23, 0x9b,
	0x3f, 0x9c, 0x9d, 0xcd, 0xc6, 0xea, 0x5a, 0xf4, 0xad, 0xb2, 0x98, 0x1a, 0x62, 0x94, 0xc4, 0x2f,
	0xc6, 0x19, 0x74, 0xd1, 0xd2, 0x10, 0xcb, 0x44, 0x33, 0x83, 0xff, 0x95, 0x83, 0x8d, 0xd8, 0xe0,
	0x71, 0xfd, 0x02, 0x89, 0x9a, 0xc8, 0xc4, 0xdc, 0x44, 0x26, 0xc6, 0x33, 0x79, 0x39, 0x99, 0xc9,
	0xa6, 0xe3, 0x57, 0x62, 0x8e, 0x9f, 0xac, 0x2f, 0xf9, 0xd9, 0xf5, 0x25, 0x61, 0xd4, 0x1b, 0xd6,
	0x97, 0xc2, 0xec, 0xfa, 0x92, 0x14, 0x32, 0x67, 0x7d, 0xf9, 0xdf, 0x38, 0xe9, 0xad, 0xbf, 0x64,
	0xe0, 0xd2, 0x44, 0x36, 0x1e, 0xd7, 0xd9, 0xf1, 0x64, 0x36, 0x7f, 0x34, 0x77, 0x36, 0xe3, 0xb1,
	0x9c, 0x92, 0xcf, 0xad, 0xf3, 0xf3, 0x79, 0xde, 0x33, 0x39, 0xe1, 0x78, 0xd3, 0xaa, 0xcb, 0xc0,
	0x92, 0x4a, 0x09, 0xdf, 0xfa, 0xcc, 0xdc, 0xb6, 0x98, 0x50, 0x33, 0x9f, 0x93, 0xf1, 0x34, 0xcd,
	0x26, 0xd2, 0x34, 0x2e, 0x42, 0x31, 0x13, 0xbe, 0xb5, 0x0f, 0xc5, 0x88, 0x4e, 0x30, 0xc8, 0xcc,
	0xca, 0xf3, 0x6c, 0xfc, 0x80, 0xf9, 0x3e, 0x13, 0xd5, 0x94, 0x27, 0x21, 0xc6, 0xd7, 0xd4, 0xf7,
	0xb9, 0x0e, 0x8c, 0x66, 0x3a, 0xc7, 0x4d, 0x74, 0x82, 0x47, 0x2d, 0xfa, 0x36, 0x82, 0x83, 0x64,
	0xf5, 0x6b, 0x15, 0x9c, 0xd1, 0xd0, 0x94, 0xe0, 0x7c, 0x10, 0x0f, 0xce, 0xf5, 0x59, 0xc1, 0x21,
	0x71, 0x46, 0x54, 0xaa, 0xd1, 0x65, 0x20, 0xa9, 0x91, 0xf0, 0xad, 0x77, 0x61, 0xeb, 0x71, 0x87,
	0x3b, 0x27, 0xb1, 0x6b, 0x37, 0xf5, 0xfa, 0x86, 0x42, 0x67, 0x32, 0x7d, 0xa3, 0xdf, 0x93, 0x13,
	0x85, 0x5f, 0xff, 0xed, 0x1a, 0x94, 0xa2, 0x1a, 0x2d, 0xd8, 0xd7, 0x00, 0xe3, 0x1f, 0x1d, 0xd8,
	0x8d, 0x73, 0x9b, 0x34, 0xa2, 0xc1, 0x4f, 0xab, 0xef, 0xcc, 0x33, 0x4d, 0xf8, 0xd6, 0x12, 0x73,
	0x8d, 0xdf, 0x34, 0xa8, 0xe1, 0xcf, 0x6e, 0x9d, 0xbb, 0x36, 0xfa, 0x11, 0xa2, 0x7a, 0x7b, 0xde,
	0xa9, 0x31, 0x51, 0xe3, 0x5f, 0x9d, 0x66, 0x8b, 0x32, 0x7f, 0x2e, 0x38, 0x47, 0x54, 0xec, 0x87,
	0x00, 0x6b, 0x89, 0x85, 0xc6, 0x0f, 0x04, 0xa3, 0x56, 0x33, 0xbb, 0x73, 0x2e, 0x0f, 0xb3, 0x1b,
	0x5e, 0xad, 0x2d, 0x32, 0x3d, 0x26, 0x36, 0xd6, 0xe1, 0x3e, 0x47, 0x6c, 0xb2, 0x09, 0x7f, 0x8e,
	0xd8, 0x89, 0xe6, 0xb9, 0xb5, 0xc4, 0x5e, 0xeb, 0x1e, 0x6c, 0xa2, 0x0f, 0xcc, 0x76, 0x17, 0xeb,
	0x1a, 0x9f, 0x56, 0xef, 0x2e, 0xda, 0x66, 0xb6, 0x96, 0x58, 0x60, 0xfc, 0x3a, 0x15, 0x75, 0x48,
	0xd9, 0x8f, 0x17, 0xe8, 0xbb, 0x9e, 0x56, 0xef, 0x2c, 0xd4, 0xa5, 0xb5, 0x96, 0x58, 0x17, 0x36,
	0x12, 0x4d, 0x49, 0x76, 0x7b, 0xee, 0xee, 0xe5, 0x69, 0xf5, 0xbd, 0x05, 0x3a, 0x9d, 0xd6, 0x12,
	0xee, 0xc0, 0x71, 0x27, 0x2d, 0x7d, 0x07, 0xc6, 0x3a, 0x7b, 0xe9, 0x3b, 0x30, 0xd1, 0x94, 0x5b,
	0x62, 0x67, 0x51, 0xd1, 0x8a, 0xdd, 0xb9, 0x59, 0x6d, 0xb1, 0xd7, 0x43, 0x75, 0x77, 0xa1, 0xf9,
	0xd1, 0x86, 0x8c, 0x57, 0x96, 0xf4, 0x0d, 0x39, 0x51, 0x16, 0xd3, 0x37, 0xe4, 0x94, 0x62, 0xb5,
	0xc4, 0xbe, 0x83, 0xed, 0x29, 0x7a, 0x1c, 0xd7, 0xd9, 0xdd, 0x45, 0x1f, 0x49, 0x6f, 0x62, 0x68,
	0x0f, 0x36, 0xe3, 0x5a, 0x1d, 0xd7, 0xd9, 0x7b, 0x0b, 0xdc, 0x00, 0x16, 0x34, 0xf6, 0x35, 0x5c,
	0x9e, 0x56, 0x1b, 0xd8, 0xee, 0x82, 0xb5, 0xad, 0x7a, 0x77, 0xb1, 0x05, 0x51, 0x50, 0xe3, 0x35,
	0x25, 0x3d, 0xa8, 0x13, 0x45, 0x2a, 0xdd, 0xce, 0xc9, 0x32, 0x65, 0x2d, 0x3d, 0xda, 0x7f, 0xfe,
	0xa8, 0xdd, 0xef, 0xda, 0x5e, 0xbb, 0x76, 0xaf, 0x2e, 0x65, 0xcd, 0xe9, 0xf7, 0x76, 0xe9, 0xaf,
	0x28, 0x4e, 0xbf, 0xbb, 0x2b, 0x78, 0x30, 0x70, 0x1d, 0x2e, 0x76, 0xa7, 0xfd, 0x7b, 0xe5, 0x3e,
	0xf5, 0x65, 0xd4, 0xbf, 0x57, 0x5e, 0xe4, 0x69, 0xcd, 0xfb, 0xff, 0x0e, 0x00, 0x00, 0xff, 0xff,
	0x0b, 0xed, 0xd7, 0x9f, 0x27, 0x23, 0x00, 0x00,
}
