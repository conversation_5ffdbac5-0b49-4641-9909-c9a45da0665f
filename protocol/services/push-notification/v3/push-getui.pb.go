// Code generated by protoc-gen-go. DO NOT EDIT.
// source: push-notification/v3/push-getui.proto

package push_server // import "golang.52tt.com/protocol/services/push-notification/v3"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type Audience struct {
	Cid                  []string        `protobuf:"bytes,1,rep,name=cid,proto3" json:"cid,omitempty"`
	Alias                []string        `protobuf:"bytes,2,rep,name=alias,proto3" json:"alias,omitempty"`
	Tag                  []*Audience_Tag `protobuf:"bytes,3,rep,name=tag,proto3" json:"tag,omitempty"`
	FastCustomTag        string          `protobuf:"bytes,4,opt,name=fast_custom_tag,json=fastCustomTag,proto3" json:"fast_custom_tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *Audience) Reset()         { *m = Audience{} }
func (m *Audience) String() string { return proto.CompactTextString(m) }
func (*Audience) ProtoMessage()    {}
func (*Audience) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{0}
}
func (m *Audience) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Audience.Unmarshal(m, b)
}
func (m *Audience) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Audience.Marshal(b, m, deterministic)
}
func (dst *Audience) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Audience.Merge(dst, src)
}
func (m *Audience) XXX_Size() int {
	return xxx_messageInfo_Audience.Size(m)
}
func (m *Audience) XXX_DiscardUnknown() {
	xxx_messageInfo_Audience.DiscardUnknown(m)
}

var xxx_messageInfo_Audience proto.InternalMessageInfo

func (m *Audience) GetCid() []string {
	if m != nil {
		return m.Cid
	}
	return nil
}

func (m *Audience) GetAlias() []string {
	if m != nil {
		return m.Alias
	}
	return nil
}

func (m *Audience) GetTag() []*Audience_Tag {
	if m != nil {
		return m.Tag
	}
	return nil
}

func (m *Audience) GetFastCustomTag() string {
	if m != nil {
		return m.FastCustomTag
	}
	return ""
}

type Audience_Tag struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Values               []string `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"`
	OptType              string   `protobuf:"bytes,3,opt,name=opt_type,json=optType,proto3" json:"opt_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Audience_Tag) Reset()         { *m = Audience_Tag{} }
func (m *Audience_Tag) String() string { return proto.CompactTextString(m) }
func (*Audience_Tag) ProtoMessage()    {}
func (*Audience_Tag) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{0, 0}
}
func (m *Audience_Tag) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Audience_Tag.Unmarshal(m, b)
}
func (m *Audience_Tag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Audience_Tag.Marshal(b, m, deterministic)
}
func (dst *Audience_Tag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Audience_Tag.Merge(dst, src)
}
func (m *Audience_Tag) XXX_Size() int {
	return xxx_messageInfo_Audience_Tag.Size(m)
}
func (m *Audience_Tag) XXX_DiscardUnknown() {
	xxx_messageInfo_Audience_Tag.DiscardUnknown(m)
}

var xxx_messageInfo_Audience_Tag proto.InternalMessageInfo

func (m *Audience_Tag) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *Audience_Tag) GetValues() []string {
	if m != nil {
		return m.Values
	}
	return nil
}

func (m *Audience_Tag) GetOptType() string {
	if m != nil {
		return m.OptType
	}
	return ""
}

type Settings struct {
	Ttl                  int64              `protobuf:"varint,1,opt,name=ttl,proto3" json:"ttl,omitempty"`
	Strategy             *Settings_Strategy `protobuf:"bytes,2,opt,name=strategy,proto3" json:"strategy,omitempty"`
	Speed                int32              `protobuf:"varint,3,opt,name=speed,proto3" json:"speed,omitempty"`
	ScheduleTime         int64              `protobuf:"varint,4,opt,name=schedule_time,json=scheduleTime,proto3" json:"schedule_time,omitempty"`
	CustomCallback       string             `protobuf:"bytes,5,opt,name=custom_callback,json=customCallback,proto3" json:"custom_callback,omitempty"`
	FilterNotifyOff      bool               `protobuf:"varint,6,opt,name=filter_notify_off,json=filterNotifyOff,proto3" json:"filter_notify_off,omitempty"`
	ActiveDays           int32              `protobuf:"varint,7,opt,name=active_days,json=activeDays,proto3" json:"active_days,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *Settings) Reset()         { *m = Settings{} }
func (m *Settings) String() string { return proto.CompactTextString(m) }
func (*Settings) ProtoMessage()    {}
func (*Settings) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{1}
}
func (m *Settings) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Settings.Unmarshal(m, b)
}
func (m *Settings) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Settings.Marshal(b, m, deterministic)
}
func (dst *Settings) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Settings.Merge(dst, src)
}
func (m *Settings) XXX_Size() int {
	return xxx_messageInfo_Settings.Size(m)
}
func (m *Settings) XXX_DiscardUnknown() {
	xxx_messageInfo_Settings.DiscardUnknown(m)
}

var xxx_messageInfo_Settings proto.InternalMessageInfo

func (m *Settings) GetTtl() int64 {
	if m != nil {
		return m.Ttl
	}
	return 0
}

func (m *Settings) GetStrategy() *Settings_Strategy {
	if m != nil {
		return m.Strategy
	}
	return nil
}

func (m *Settings) GetSpeed() int32 {
	if m != nil {
		return m.Speed
	}
	return 0
}

func (m *Settings) GetScheduleTime() int64 {
	if m != nil {
		return m.ScheduleTime
	}
	return 0
}

func (m *Settings) GetCustomCallback() string {
	if m != nil {
		return m.CustomCallback
	}
	return ""
}

func (m *Settings) GetFilterNotifyOff() bool {
	if m != nil {
		return m.FilterNotifyOff
	}
	return false
}

func (m *Settings) GetActiveDays() int32 {
	if m != nil {
		return m.ActiveDays
	}
	return 0
}

type Settings_Strategy struct {
	Default              int32    `protobuf:"varint,1,opt,name=default,proto3" json:"default,omitempty"`
	Ios                  int32    `protobuf:"varint,2,opt,name=ios,proto3" json:"ios,omitempty"`
	Hw                   int32    `protobuf:"varint,3,opt,name=hw,proto3" json:"hw,omitempty"`
	Ho                   int32    `protobuf:"varint,4,opt,name=ho,proto3" json:"ho,omitempty"`
	Xm                   int32    `protobuf:"varint,5,opt,name=xm,proto3" json:"xm,omitempty"`
	Xmg                  int32    `protobuf:"varint,6,opt,name=xmg,proto3" json:"xmg,omitempty"`
	Vv                   int32    `protobuf:"varint,7,opt,name=vv,proto3" json:"vv,omitempty"`
	Op                   int32    `protobuf:"varint,8,opt,name=op,proto3" json:"op,omitempty"`
	Opg                  int32    `protobuf:"varint,9,opt,name=opg,proto3" json:"opg,omitempty"`
	Mz                   int32    `protobuf:"varint,10,opt,name=mz,proto3" json:"mz,omitempty"`
	St                   int32    `protobuf:"varint,11,opt,name=st,proto3" json:"st,omitempty"`
	Wx                   int32    `protobuf:"varint,12,opt,name=wx,proto3" json:"wx,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Settings_Strategy) Reset()         { *m = Settings_Strategy{} }
func (m *Settings_Strategy) String() string { return proto.CompactTextString(m) }
func (*Settings_Strategy) ProtoMessage()    {}
func (*Settings_Strategy) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{1, 0}
}
func (m *Settings_Strategy) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Settings_Strategy.Unmarshal(m, b)
}
func (m *Settings_Strategy) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Settings_Strategy.Marshal(b, m, deterministic)
}
func (dst *Settings_Strategy) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Settings_Strategy.Merge(dst, src)
}
func (m *Settings_Strategy) XXX_Size() int {
	return xxx_messageInfo_Settings_Strategy.Size(m)
}
func (m *Settings_Strategy) XXX_DiscardUnknown() {
	xxx_messageInfo_Settings_Strategy.DiscardUnknown(m)
}

var xxx_messageInfo_Settings_Strategy proto.InternalMessageInfo

func (m *Settings_Strategy) GetDefault() int32 {
	if m != nil {
		return m.Default
	}
	return 0
}

func (m *Settings_Strategy) GetIos() int32 {
	if m != nil {
		return m.Ios
	}
	return 0
}

func (m *Settings_Strategy) GetHw() int32 {
	if m != nil {
		return m.Hw
	}
	return 0
}

func (m *Settings_Strategy) GetHo() int32 {
	if m != nil {
		return m.Ho
	}
	return 0
}

func (m *Settings_Strategy) GetXm() int32 {
	if m != nil {
		return m.Xm
	}
	return 0
}

func (m *Settings_Strategy) GetXmg() int32 {
	if m != nil {
		return m.Xmg
	}
	return 0
}

func (m *Settings_Strategy) GetVv() int32 {
	if m != nil {
		return m.Vv
	}
	return 0
}

func (m *Settings_Strategy) GetOp() int32 {
	if m != nil {
		return m.Op
	}
	return 0
}

func (m *Settings_Strategy) GetOpg() int32 {
	if m != nil {
		return m.Opg
	}
	return 0
}

func (m *Settings_Strategy) GetMz() int32 {
	if m != nil {
		return m.Mz
	}
	return 0
}

func (m *Settings_Strategy) GetSt() int32 {
	if m != nil {
		return m.St
	}
	return 0
}

func (m *Settings_Strategy) GetWx() int32 {
	if m != nil {
		return m.Wx
	}
	return 0
}

type PushMessage struct {
	Duration             string                    `protobuf:"bytes,1,opt,name=duration,proto3" json:"duration,omitempty"`
	Notification         *PushMessage_Notification `protobuf:"bytes,2,opt,name=notification,proto3" json:"notification,omitempty"`
	Transmission         string                    `protobuf:"bytes,3,opt,name=transmission,proto3" json:"transmission,omitempty"`
	Revoke               *PushMessage_Revoke       `protobuf:"bytes,4,opt,name=revoke,proto3" json:"revoke,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *PushMessage) Reset()         { *m = PushMessage{} }
func (m *PushMessage) String() string { return proto.CompactTextString(m) }
func (*PushMessage) ProtoMessage()    {}
func (*PushMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{2}
}
func (m *PushMessage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushMessage.Unmarshal(m, b)
}
func (m *PushMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushMessage.Marshal(b, m, deterministic)
}
func (dst *PushMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushMessage.Merge(dst, src)
}
func (m *PushMessage) XXX_Size() int {
	return xxx_messageInfo_PushMessage.Size(m)
}
func (m *PushMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_PushMessage.DiscardUnknown(m)
}

var xxx_messageInfo_PushMessage proto.InternalMessageInfo

func (m *PushMessage) GetDuration() string {
	if m != nil {
		return m.Duration
	}
	return ""
}

func (m *PushMessage) GetNotification() *PushMessage_Notification {
	if m != nil {
		return m.Notification
	}
	return nil
}

func (m *PushMessage) GetTransmission() string {
	if m != nil {
		return m.Transmission
	}
	return ""
}

func (m *PushMessage) GetRevoke() *PushMessage_Revoke {
	if m != nil {
		return m.Revoke
	}
	return nil
}

type PushMessage_Notification struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Body                 string   `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	BigText              string   `protobuf:"bytes,3,opt,name=big_text,json=bigText,proto3" json:"big_text,omitempty"`
	BigImage             string   `protobuf:"bytes,4,opt,name=big_image,json=bigImage,proto3" json:"big_image,omitempty"`
	Logo                 string   `protobuf:"bytes,5,opt,name=logo,proto3" json:"logo,omitempty"`
	LogoUrl              string   `protobuf:"bytes,6,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	ChannelId            string   `protobuf:"bytes,7,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName          string   `protobuf:"bytes,8,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelLevel         string   `protobuf:"bytes,9,opt,name=channel_level,json=channelLevel,proto3" json:"channel_level,omitempty"`
	ClickType            string   `protobuf:"bytes,10,opt,name=click_type,json=clickType,proto3" json:"click_type,omitempty"`
	Intent               string   `protobuf:"bytes,11,opt,name=intent,proto3" json:"intent,omitempty"`
	Url                  string   `protobuf:"bytes,12,opt,name=url,proto3" json:"url,omitempty"`
	Payload              string   `protobuf:"bytes,13,opt,name=payload,proto3" json:"payload,omitempty"`
	NotifyId             int64    `protobuf:"varint,14,opt,name=notify_id,json=notifyId,proto3" json:"notify_id,omitempty"`
	RingName             string   `protobuf:"bytes,15,opt,name=ring_name,json=ringName,proto3" json:"ring_name,omitempty"`
	BadgeAddNum          int32    `protobuf:"varint,16,opt,name=badge_add_num,json=badgeAddNum,proto3" json:"badge_add_num,omitempty"`
	ThreadId             string   `protobuf:"bytes,17,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushMessage_Notification) Reset()         { *m = PushMessage_Notification{} }
func (m *PushMessage_Notification) String() string { return proto.CompactTextString(m) }
func (*PushMessage_Notification) ProtoMessage()    {}
func (*PushMessage_Notification) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{2, 0}
}
func (m *PushMessage_Notification) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushMessage_Notification.Unmarshal(m, b)
}
func (m *PushMessage_Notification) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushMessage_Notification.Marshal(b, m, deterministic)
}
func (dst *PushMessage_Notification) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushMessage_Notification.Merge(dst, src)
}
func (m *PushMessage_Notification) XXX_Size() int {
	return xxx_messageInfo_PushMessage_Notification.Size(m)
}
func (m *PushMessage_Notification) XXX_DiscardUnknown() {
	xxx_messageInfo_PushMessage_Notification.DiscardUnknown(m)
}

var xxx_messageInfo_PushMessage_Notification proto.InternalMessageInfo

func (m *PushMessage_Notification) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PushMessage_Notification) GetBody() string {
	if m != nil {
		return m.Body
	}
	return ""
}

func (m *PushMessage_Notification) GetBigText() string {
	if m != nil {
		return m.BigText
	}
	return ""
}

func (m *PushMessage_Notification) GetBigImage() string {
	if m != nil {
		return m.BigImage
	}
	return ""
}

func (m *PushMessage_Notification) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *PushMessage_Notification) GetLogoUrl() string {
	if m != nil {
		return m.LogoUrl
	}
	return ""
}

func (m *PushMessage_Notification) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *PushMessage_Notification) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *PushMessage_Notification) GetChannelLevel() string {
	if m != nil {
		return m.ChannelLevel
	}
	return ""
}

func (m *PushMessage_Notification) GetClickType() string {
	if m != nil {
		return m.ClickType
	}
	return ""
}

func (m *PushMessage_Notification) GetIntent() string {
	if m != nil {
		return m.Intent
	}
	return ""
}

func (m *PushMessage_Notification) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *PushMessage_Notification) GetPayload() string {
	if m != nil {
		return m.Payload
	}
	return ""
}

func (m *PushMessage_Notification) GetNotifyId() int64 {
	if m != nil {
		return m.NotifyId
	}
	return 0
}

func (m *PushMessage_Notification) GetRingName() string {
	if m != nil {
		return m.RingName
	}
	return ""
}

func (m *PushMessage_Notification) GetBadgeAddNum() int32 {
	if m != nil {
		return m.BadgeAddNum
	}
	return 0
}

func (m *PushMessage_Notification) GetThreadId() string {
	if m != nil {
		return m.ThreadId
	}
	return ""
}

type PushMessage_Revoke struct {
	OldTaskId            string   `protobuf:"bytes,1,opt,name=old_task_id,json=oldTaskId,proto3" json:"old_task_id,omitempty"`
	Force                bool     `protobuf:"varint,2,opt,name=force,proto3" json:"force,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushMessage_Revoke) Reset()         { *m = PushMessage_Revoke{} }
func (m *PushMessage_Revoke) String() string { return proto.CompactTextString(m) }
func (*PushMessage_Revoke) ProtoMessage()    {}
func (*PushMessage_Revoke) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{2, 1}
}
func (m *PushMessage_Revoke) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushMessage_Revoke.Unmarshal(m, b)
}
func (m *PushMessage_Revoke) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushMessage_Revoke.Marshal(b, m, deterministic)
}
func (dst *PushMessage_Revoke) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushMessage_Revoke.Merge(dst, src)
}
func (m *PushMessage_Revoke) XXX_Size() int {
	return xxx_messageInfo_PushMessage_Revoke.Size(m)
}
func (m *PushMessage_Revoke) XXX_DiscardUnknown() {
	xxx_messageInfo_PushMessage_Revoke.DiscardUnknown(m)
}

var xxx_messageInfo_PushMessage_Revoke proto.InternalMessageInfo

func (m *PushMessage_Revoke) GetOldTaskId() string {
	if m != nil {
		return m.OldTaskId
	}
	return ""
}

func (m *PushMessage_Revoke) GetForce() bool {
	if m != nil {
		return m.Force
	}
	return false
}

type IosChannel struct {
	Type       string                   `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Aps        *IosChannel_Aps          `protobuf:"bytes,2,opt,name=aps,proto3" json:"aps,omitempty"`
	AutoBadge  string                   `protobuf:"bytes,3,opt,name=auto_badge,json=autoBadge,proto3" json:"auto_badge,omitempty"`
	Payload    string                   `protobuf:"bytes,4,opt,name=payload,proto3" json:"payload,omitempty"`
	Multimedia []*IosChannel_Multimedia `protobuf:"bytes,5,rep,name=multimedia,proto3" json:"multimedia,omitempty"`
	// @gotags: json:"apns-collapse-id,omitempty"
	ApnsCollapseId       string   `protobuf:"bytes,6,opt,name=apns_collapse_id,json=apns-collapse-id,omitempty,proto3" json:"apns-collapse-id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IosChannel) Reset()         { *m = IosChannel{} }
func (m *IosChannel) String() string { return proto.CompactTextString(m) }
func (*IosChannel) ProtoMessage()    {}
func (*IosChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{3}
}
func (m *IosChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IosChannel.Unmarshal(m, b)
}
func (m *IosChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IosChannel.Marshal(b, m, deterministic)
}
func (dst *IosChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IosChannel.Merge(dst, src)
}
func (m *IosChannel) XXX_Size() int {
	return xxx_messageInfo_IosChannel.Size(m)
}
func (m *IosChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_IosChannel.DiscardUnknown(m)
}

var xxx_messageInfo_IosChannel proto.InternalMessageInfo

func (m *IosChannel) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *IosChannel) GetAps() *IosChannel_Aps {
	if m != nil {
		return m.Aps
	}
	return nil
}

func (m *IosChannel) GetAutoBadge() string {
	if m != nil {
		return m.AutoBadge
	}
	return ""
}

func (m *IosChannel) GetPayload() string {
	if m != nil {
		return m.Payload
	}
	return ""
}

func (m *IosChannel) GetMultimedia() []*IosChannel_Multimedia {
	if m != nil {
		return m.Multimedia
	}
	return nil
}

func (m *IosChannel) GetApnsCollapseId() string {
	if m != nil {
		return m.ApnsCollapseId
	}
	return ""
}

type IosChannel_Aps struct {
	Alert *IosChannel_Aps_Alert `protobuf:"bytes,1,opt,name=alert,proto3" json:"alert,omitempty"`
	// @gotags: json:"content-available,omitempty"
	ContentAvailable int32  `protobuf:"varint,2,opt,name=content_available,json=content-available,proto3" json:"content-available,omitempty"`
	Sound            string `protobuf:"bytes,3,opt,name=sound,proto3" json:"sound,omitempty"`
	Category         string `protobuf:"bytes,4,opt,name=category,proto3" json:"category,omitempty"`
	// @gotags: json:"thread-id,omitempty"
	ThreadId  string `protobuf:"bytes,5,opt,name=thread_id,json=thread-id,proto3" json:"thread-id,omitempty"`
	Timestamp string `protobuf:"bytes,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Event     string `protobuf:"bytes,7,opt,name=event,proto3" json:"event,omitempty"`
	// @gotags: json:"content-state,omitempty"
	ContentState         map[string]string `protobuf:"bytes,8,rep,name=content_state,json=content-state,proto3" json:"content-state,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *IosChannel_Aps) Reset()         { *m = IosChannel_Aps{} }
func (m *IosChannel_Aps) String() string { return proto.CompactTextString(m) }
func (*IosChannel_Aps) ProtoMessage()    {}
func (*IosChannel_Aps) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{3, 0}
}
func (m *IosChannel_Aps) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IosChannel_Aps.Unmarshal(m, b)
}
func (m *IosChannel_Aps) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IosChannel_Aps.Marshal(b, m, deterministic)
}
func (dst *IosChannel_Aps) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IosChannel_Aps.Merge(dst, src)
}
func (m *IosChannel_Aps) XXX_Size() int {
	return xxx_messageInfo_IosChannel_Aps.Size(m)
}
func (m *IosChannel_Aps) XXX_DiscardUnknown() {
	xxx_messageInfo_IosChannel_Aps.DiscardUnknown(m)
}

var xxx_messageInfo_IosChannel_Aps proto.InternalMessageInfo

func (m *IosChannel_Aps) GetAlert() *IosChannel_Aps_Alert {
	if m != nil {
		return m.Alert
	}
	return nil
}

func (m *IosChannel_Aps) GetContentAvailable() int32 {
	if m != nil {
		return m.ContentAvailable
	}
	return 0
}

func (m *IosChannel_Aps) GetSound() string {
	if m != nil {
		return m.Sound
	}
	return ""
}

func (m *IosChannel_Aps) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *IosChannel_Aps) GetThreadId() string {
	if m != nil {
		return m.ThreadId
	}
	return ""
}

func (m *IosChannel_Aps) GetTimestamp() string {
	if m != nil {
		return m.Timestamp
	}
	return ""
}

func (m *IosChannel_Aps) GetEvent() string {
	if m != nil {
		return m.Event
	}
	return ""
}

func (m *IosChannel_Aps) GetContentState() map[string]string {
	if m != nil {
		return m.ContentState
	}
	return nil
}

type IosChannel_Aps_Alert struct {
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Body  string `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	// @gotags: json:"action-loc-key,omitempty"
	ActionLocKey string `protobuf:"bytes,3,opt,name=action_loc_key,json=action-loc-key,proto3" json:"action-loc-key,omitempty"`
	// @gotags: json:"loc-key,omitempty"
	LocKey string `protobuf:"bytes,4,opt,name=loc_key,json=loc-key,proto3" json:"loc-key,omitempty"`
	// @gotags: json:"loc-args,omitempty"
	LocArgs []string `protobuf:"bytes,5,rep,name=loc_args,json=loc-args,proto3" json:"loc-args,omitempty"`
	// @gotags: json:"launch-image,omitempty"
	LaunchImage string `protobuf:"bytes,6,opt,name=launch_image,json=launch-image,proto3" json:"launch-image,omitempty"`
	// @gotags: json:"title-loc-key,omitempty"
	TitleLocKey string `protobuf:"bytes,7,opt,name=title_loc_key,json=title-loc-key,proto3" json:"title-loc-key,omitempty"`
	// @gotags: json:"title-loc-args,omitempty"
	TitleLocArgs []string `protobuf:"bytes,8,rep,name=title_loc_args,json=title-loc-args,proto3" json:"title-loc-args,omitempty"`
	Subtitle     string   `protobuf:"bytes,9,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	// @gotags: json:"subtitle-loc-key,omitempty"
	SubTitleLocKey string `protobuf:"bytes,10,opt,name=sub_title_loc_key,json=subtitle-loc-key,proto3" json:"subtitle-loc-key,omitempty"`
	// @gotags: json:"subtitle-loc-args,omitempty"
	SubTitleLocArgs      []string `protobuf:"bytes,11,rep,name=sub_title_loc_args,json=subtitle-loc-args,proto3" json:"subtitle-loc-args,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IosChannel_Aps_Alert) Reset()         { *m = IosChannel_Aps_Alert{} }
func (m *IosChannel_Aps_Alert) String() string { return proto.CompactTextString(m) }
func (*IosChannel_Aps_Alert) ProtoMessage()    {}
func (*IosChannel_Aps_Alert) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{3, 0, 0}
}
func (m *IosChannel_Aps_Alert) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IosChannel_Aps_Alert.Unmarshal(m, b)
}
func (m *IosChannel_Aps_Alert) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IosChannel_Aps_Alert.Marshal(b, m, deterministic)
}
func (dst *IosChannel_Aps_Alert) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IosChannel_Aps_Alert.Merge(dst, src)
}
func (m *IosChannel_Aps_Alert) XXX_Size() int {
	return xxx_messageInfo_IosChannel_Aps_Alert.Size(m)
}
func (m *IosChannel_Aps_Alert) XXX_DiscardUnknown() {
	xxx_messageInfo_IosChannel_Aps_Alert.DiscardUnknown(m)
}

var xxx_messageInfo_IosChannel_Aps_Alert proto.InternalMessageInfo

func (m *IosChannel_Aps_Alert) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *IosChannel_Aps_Alert) GetBody() string {
	if m != nil {
		return m.Body
	}
	return ""
}

func (m *IosChannel_Aps_Alert) GetActionLocKey() string {
	if m != nil {
		return m.ActionLocKey
	}
	return ""
}

func (m *IosChannel_Aps_Alert) GetLocKey() string {
	if m != nil {
		return m.LocKey
	}
	return ""
}

func (m *IosChannel_Aps_Alert) GetLocArgs() []string {
	if m != nil {
		return m.LocArgs
	}
	return nil
}

func (m *IosChannel_Aps_Alert) GetLaunchImage() string {
	if m != nil {
		return m.LaunchImage
	}
	return ""
}

func (m *IosChannel_Aps_Alert) GetTitleLocKey() string {
	if m != nil {
		return m.TitleLocKey
	}
	return ""
}

func (m *IosChannel_Aps_Alert) GetTitleLocArgs() []string {
	if m != nil {
		return m.TitleLocArgs
	}
	return nil
}

func (m *IosChannel_Aps_Alert) GetSubtitle() string {
	if m != nil {
		return m.Subtitle
	}
	return ""
}

func (m *IosChannel_Aps_Alert) GetSubTitleLocKey() string {
	if m != nil {
		return m.SubTitleLocKey
	}
	return ""
}

func (m *IosChannel_Aps_Alert) GetSubTitleLocArgs() []string {
	if m != nil {
		return m.SubTitleLocArgs
	}
	return nil
}

type IosChannel_Multimedia struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Type                 int32    `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	OnlyWifi             bool     `protobuf:"varint,3,opt,name=only_wifi,json=onlyWifi,proto3" json:"only_wifi,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IosChannel_Multimedia) Reset()         { *m = IosChannel_Multimedia{} }
func (m *IosChannel_Multimedia) String() string { return proto.CompactTextString(m) }
func (*IosChannel_Multimedia) ProtoMessage()    {}
func (*IosChannel_Multimedia) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{3, 1}
}
func (m *IosChannel_Multimedia) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IosChannel_Multimedia.Unmarshal(m, b)
}
func (m *IosChannel_Multimedia) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IosChannel_Multimedia.Marshal(b, m, deterministic)
}
func (dst *IosChannel_Multimedia) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IosChannel_Multimedia.Merge(dst, src)
}
func (m *IosChannel_Multimedia) XXX_Size() int {
	return xxx_messageInfo_IosChannel_Multimedia.Size(m)
}
func (m *IosChannel_Multimedia) XXX_DiscardUnknown() {
	xxx_messageInfo_IosChannel_Multimedia.DiscardUnknown(m)
}

var xxx_messageInfo_IosChannel_Multimedia proto.InternalMessageInfo

func (m *IosChannel_Multimedia) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *IosChannel_Multimedia) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *IosChannel_Multimedia) GetOnlyWifi() bool {
	if m != nil {
		return m.OnlyWifi
	}
	return false
}

type AndroidChannel struct {
	Ups                  *AndroidChannel_Ups `protobuf:"bytes,1,opt,name=ups,proto3" json:"ups,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *AndroidChannel) Reset()         { *m = AndroidChannel{} }
func (m *AndroidChannel) String() string { return proto.CompactTextString(m) }
func (*AndroidChannel) ProtoMessage()    {}
func (*AndroidChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{4}
}
func (m *AndroidChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AndroidChannel.Unmarshal(m, b)
}
func (m *AndroidChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AndroidChannel.Marshal(b, m, deterministic)
}
func (dst *AndroidChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AndroidChannel.Merge(dst, src)
}
func (m *AndroidChannel) XXX_Size() int {
	return xxx_messageInfo_AndroidChannel.Size(m)
}
func (m *AndroidChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_AndroidChannel.DiscardUnknown(m)
}

var xxx_messageInfo_AndroidChannel proto.InternalMessageInfo

func (m *AndroidChannel) GetUps() *AndroidChannel_Ups {
	if m != nil {
		return m.Ups
	}
	return nil
}

type AndroidChannel_Ups struct {
	Notification         *AndroidChannel_Ups_Notification `protobuf:"bytes,1,opt,name=notification,proto3" json:"notification,omitempty"`
	Transmission         string                           `protobuf:"bytes,2,opt,name=transmission,proto3" json:"transmission,omitempty"`
	Revoke               *AndroidChannel_Ups_Revoke       `protobuf:"bytes,3,opt,name=revoke,proto3" json:"revoke,omitempty"`
	Options              *AndroidChannel_Ups_Options      `protobuf:"bytes,4,opt,name=options,proto3" json:"options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *AndroidChannel_Ups) Reset()         { *m = AndroidChannel_Ups{} }
func (m *AndroidChannel_Ups) String() string { return proto.CompactTextString(m) }
func (*AndroidChannel_Ups) ProtoMessage()    {}
func (*AndroidChannel_Ups) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{4, 0}
}
func (m *AndroidChannel_Ups) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AndroidChannel_Ups.Unmarshal(m, b)
}
func (m *AndroidChannel_Ups) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AndroidChannel_Ups.Marshal(b, m, deterministic)
}
func (dst *AndroidChannel_Ups) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AndroidChannel_Ups.Merge(dst, src)
}
func (m *AndroidChannel_Ups) XXX_Size() int {
	return xxx_messageInfo_AndroidChannel_Ups.Size(m)
}
func (m *AndroidChannel_Ups) XXX_DiscardUnknown() {
	xxx_messageInfo_AndroidChannel_Ups.DiscardUnknown(m)
}

var xxx_messageInfo_AndroidChannel_Ups proto.InternalMessageInfo

func (m *AndroidChannel_Ups) GetNotification() *AndroidChannel_Ups_Notification {
	if m != nil {
		return m.Notification
	}
	return nil
}

func (m *AndroidChannel_Ups) GetTransmission() string {
	if m != nil {
		return m.Transmission
	}
	return ""
}

func (m *AndroidChannel_Ups) GetRevoke() *AndroidChannel_Ups_Revoke {
	if m != nil {
		return m.Revoke
	}
	return nil
}

func (m *AndroidChannel_Ups) GetOptions() *AndroidChannel_Ups_Options {
	if m != nil {
		return m.Options
	}
	return nil
}

type AndroidChannel_Ups_Notification struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Body                 string   `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	ClickType            string   `protobuf:"bytes,3,opt,name=click_type,json=clickType,proto3" json:"click_type,omitempty"`
	Intent               string   `protobuf:"bytes,4,opt,name=intent,proto3" json:"intent,omitempty"`
	Url                  string   `protobuf:"bytes,5,opt,name=url,proto3" json:"url,omitempty"`
	NotifyId             int64    `protobuf:"varint,6,opt,name=notify_id,json=notifyId,proto3" json:"notify_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AndroidChannel_Ups_Notification) Reset()         { *m = AndroidChannel_Ups_Notification{} }
func (m *AndroidChannel_Ups_Notification) String() string { return proto.CompactTextString(m) }
func (*AndroidChannel_Ups_Notification) ProtoMessage()    {}
func (*AndroidChannel_Ups_Notification) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{4, 0, 0}
}
func (m *AndroidChannel_Ups_Notification) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AndroidChannel_Ups_Notification.Unmarshal(m, b)
}
func (m *AndroidChannel_Ups_Notification) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AndroidChannel_Ups_Notification.Marshal(b, m, deterministic)
}
func (dst *AndroidChannel_Ups_Notification) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AndroidChannel_Ups_Notification.Merge(dst, src)
}
func (m *AndroidChannel_Ups_Notification) XXX_Size() int {
	return xxx_messageInfo_AndroidChannel_Ups_Notification.Size(m)
}
func (m *AndroidChannel_Ups_Notification) XXX_DiscardUnknown() {
	xxx_messageInfo_AndroidChannel_Ups_Notification.DiscardUnknown(m)
}

var xxx_messageInfo_AndroidChannel_Ups_Notification proto.InternalMessageInfo

func (m *AndroidChannel_Ups_Notification) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *AndroidChannel_Ups_Notification) GetBody() string {
	if m != nil {
		return m.Body
	}
	return ""
}

func (m *AndroidChannel_Ups_Notification) GetClickType() string {
	if m != nil {
		return m.ClickType
	}
	return ""
}

func (m *AndroidChannel_Ups_Notification) GetIntent() string {
	if m != nil {
		return m.Intent
	}
	return ""
}

func (m *AndroidChannel_Ups_Notification) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *AndroidChannel_Ups_Notification) GetNotifyId() int64 {
	if m != nil {
		return m.NotifyId
	}
	return 0
}

type AndroidChannel_Ups_Revoke struct {
	OldTaskId            string   `protobuf:"bytes,1,opt,name=old_task_id,json=oldTaskId,proto3" json:"old_task_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AndroidChannel_Ups_Revoke) Reset()         { *m = AndroidChannel_Ups_Revoke{} }
func (m *AndroidChannel_Ups_Revoke) String() string { return proto.CompactTextString(m) }
func (*AndroidChannel_Ups_Revoke) ProtoMessage()    {}
func (*AndroidChannel_Ups_Revoke) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{4, 0, 1}
}
func (m *AndroidChannel_Ups_Revoke) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AndroidChannel_Ups_Revoke.Unmarshal(m, b)
}
func (m *AndroidChannel_Ups_Revoke) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AndroidChannel_Ups_Revoke.Marshal(b, m, deterministic)
}
func (dst *AndroidChannel_Ups_Revoke) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AndroidChannel_Ups_Revoke.Merge(dst, src)
}
func (m *AndroidChannel_Ups_Revoke) XXX_Size() int {
	return xxx_messageInfo_AndroidChannel_Ups_Revoke.Size(m)
}
func (m *AndroidChannel_Ups_Revoke) XXX_DiscardUnknown() {
	xxx_messageInfo_AndroidChannel_Ups_Revoke.DiscardUnknown(m)
}

var xxx_messageInfo_AndroidChannel_Ups_Revoke proto.InternalMessageInfo

func (m *AndroidChannel_Ups_Revoke) GetOldTaskId() string {
	if m != nil {
		return m.OldTaskId
	}
	return ""
}

type AndroidChannel_Ups_Options struct {
	// @gotags: json:"HW,omitempty"
	Hw *AndroidChannel_Ups_Options_HW `protobuf:"bytes,1,opt,name=hw,json=HW,omitempty,proto3" json:"HW,omitempty"`
	// @gotags: json:"XM,omitempty"
	Xm *AndroidChannel_Ups_Options_XM `protobuf:"bytes,2,opt,name=xm,json=XM,omitempty,proto3" json:"XM,omitempty"`
	// @gotags: json:"OP,omitempty"
	Op *AndroidChannel_Ups_Options_OP `protobuf:"bytes,3,opt,name=op,json=OP,omitempty,proto3" json:"OP,omitempty"`
	// @gotags: json:"VV,omitempty"
	Vv *AndroidChannel_Ups_Options_VV `protobuf:"bytes,4,opt,name=vv,json=VV,omitempty,proto3" json:"VV,omitempty"`
	// @gotags: json:"HO,omitempty"
	Ho *AndroidChannel_Ups_Options_HO `protobuf:"bytes,5,opt,name=ho,json=HO,omitempty,proto3" json:"HO,omitempty"`
	// @gotags: json:"XMG,omitempty"
	Xmg *AndroidChannel_Ups_Options_XM `protobuf:"bytes,6,opt,name=xmg,json=XMG,omitempty,proto3" json:"XMG,omitempty"`
	// @gotags: json:"OPG,omitempty"
	Opg *AndroidChannel_Ups_Options_OP `protobuf:"bytes,7,opt,name=opg,json=OPG,omitempty,proto3" json:"OPG,omitempty"`
	// @gotags: json:"UPS,omitempty"
	Ups                  *AndroidChannel_Ups_Options_UPS `protobuf:"bytes,20,opt,name=ups,json=UPS,omitempty,proto3" json:"UPS,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *AndroidChannel_Ups_Options) Reset()         { *m = AndroidChannel_Ups_Options{} }
func (m *AndroidChannel_Ups_Options) String() string { return proto.CompactTextString(m) }
func (*AndroidChannel_Ups_Options) ProtoMessage()    {}
func (*AndroidChannel_Ups_Options) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{4, 0, 2}
}
func (m *AndroidChannel_Ups_Options) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AndroidChannel_Ups_Options.Unmarshal(m, b)
}
func (m *AndroidChannel_Ups_Options) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AndroidChannel_Ups_Options.Marshal(b, m, deterministic)
}
func (dst *AndroidChannel_Ups_Options) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AndroidChannel_Ups_Options.Merge(dst, src)
}
func (m *AndroidChannel_Ups_Options) XXX_Size() int {
	return xxx_messageInfo_AndroidChannel_Ups_Options.Size(m)
}
func (m *AndroidChannel_Ups_Options) XXX_DiscardUnknown() {
	xxx_messageInfo_AndroidChannel_Ups_Options.DiscardUnknown(m)
}

var xxx_messageInfo_AndroidChannel_Ups_Options proto.InternalMessageInfo

func (m *AndroidChannel_Ups_Options) GetHw() *AndroidChannel_Ups_Options_HW {
	if m != nil {
		return m.Hw
	}
	return nil
}

func (m *AndroidChannel_Ups_Options) GetXm() *AndroidChannel_Ups_Options_XM {
	if m != nil {
		return m.Xm
	}
	return nil
}

func (m *AndroidChannel_Ups_Options) GetOp() *AndroidChannel_Ups_Options_OP {
	if m != nil {
		return m.Op
	}
	return nil
}

func (m *AndroidChannel_Ups_Options) GetVv() *AndroidChannel_Ups_Options_VV {
	if m != nil {
		return m.Vv
	}
	return nil
}

func (m *AndroidChannel_Ups_Options) GetHo() *AndroidChannel_Ups_Options_HO {
	if m != nil {
		return m.Ho
	}
	return nil
}

func (m *AndroidChannel_Ups_Options) GetXmg() *AndroidChannel_Ups_Options_XM {
	if m != nil {
		return m.Xmg
	}
	return nil
}

func (m *AndroidChannel_Ups_Options) GetOpg() *AndroidChannel_Ups_Options_OP {
	if m != nil {
		return m.Opg
	}
	return nil
}

func (m *AndroidChannel_Ups_Options) GetUps() *AndroidChannel_Ups_Options_UPS {
	if m != nil {
		return m.Ups
	}
	return nil
}

type AndroidChannel_Ups_Options_HW struct {
	// @gotags: json:"/message/android/notification/badge/class,omitempty"
	BadgeClass string `protobuf:"bytes,1,opt,name=badge_class,json=/message/android/notification/badge/class,omitempty,proto3" json:"/message/android/notification/badge/class,omitempty"`
	// @gotags: json:"/message/android/notification/badge/add_num,omitempty"
	BadgeAddNum int32 `protobuf:"varint,2,opt,name=badge_add_num,json=/message/android/notification/badge/add_num,omitempty,proto3" json:"/message/android/notification/badge/add_num,omitempty"`
	// @gotags: json:"/message/android/notification/badge/set_num,omitempty"
	BadgeSetNum int32 `protobuf:"varint,3,opt,name=badge_set_num,json=/message/android/notification/badge/set_num,omitempty,proto3" json:"/message/android/notification/badge/set_num,omitempty"`
	// @gotags: json:"/message/android/notification/image,omitempty"
	Image string `protobuf:"bytes,4,opt,name=image,json=/message/android/notification/image,omitempty,proto3" json:"/message/android/notification/image,omitempty"`
	// @gotags: json:"/message/android/notification/style,omitempty"
	Style int32 `protobuf:"varint,5,opt,name=style,json=/message/android/notification/style,omitempty,proto3" json:"/message/android/notification/style,omitempty"`
	// @gotags: json:"/message/android/notification/big_title,omitempty"
	BigTitle string `protobuf:"bytes,6,opt,name=big_title,json=/message/android/notification/big_title,omitempty,proto3" json:"/message/android/notification/big_title,omitempty"`
	// @gotags: json:"/message/android/notification/big_body,omitempty"
	BigBody string `protobuf:"bytes,7,opt,name=big_body,json=/message/android/notification/big_body,omitempty,proto3" json:"/message/android/notification/big_body,omitempty"`
	// @gotags: json:"/message/android/notification/importance,omitempty"
	Importance string `protobuf:"bytes,8,opt,name=importance,json=/message/android/notification/importance,omitempty,proto3" json:"/message/android/notification/importance,omitempty"`
	// @gotags: json:"/message/android/notification/default_sound,omitempty"
	DefaultSound bool `protobuf:"varint,9,opt,name=default_sound,json=/message/android/notification/default_sound,omitempty,proto3" json:"/message/android/notification/default_sound,omitempty"`
	// @gotags: json:"/message/android/notification/channel_id,omitempty"
	ChannelId string `protobuf:"bytes,10,opt,name=channel_id,json=/message/android/notification/channel_id,omitempty,proto3" json:"/message/android/notification/channel_id,omitempty"`
	// @gotags: json:"/message/android/notification/sound,omitempty"
	Sound string `protobuf:"bytes,11,opt,name=sound,json=/message/android/notification/sound,omitempty,proto3" json:"/message/android/notification/sound,omitempty"`
	// @gotags: json:"/message/android/category,omitempty"
	Category string `protobuf:"bytes,12,opt,name=category,json=/message/android/category,omitempty,proto3" json:"/message/android/category,omitempty"`
	// @gotags: json:"/message/android/target_user_type,omitempty"
	TargetUserType       int32    `protobuf:"varint,13,opt,name=target_user_type,json=/message/android/target_user_type,omitempty,proto3" json:"/message/android/target_user_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AndroidChannel_Ups_Options_HW) Reset()         { *m = AndroidChannel_Ups_Options_HW{} }
func (m *AndroidChannel_Ups_Options_HW) String() string { return proto.CompactTextString(m) }
func (*AndroidChannel_Ups_Options_HW) ProtoMessage()    {}
func (*AndroidChannel_Ups_Options_HW) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{4, 0, 2, 0}
}
func (m *AndroidChannel_Ups_Options_HW) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AndroidChannel_Ups_Options_HW.Unmarshal(m, b)
}
func (m *AndroidChannel_Ups_Options_HW) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AndroidChannel_Ups_Options_HW.Marshal(b, m, deterministic)
}
func (dst *AndroidChannel_Ups_Options_HW) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AndroidChannel_Ups_Options_HW.Merge(dst, src)
}
func (m *AndroidChannel_Ups_Options_HW) XXX_Size() int {
	return xxx_messageInfo_AndroidChannel_Ups_Options_HW.Size(m)
}
func (m *AndroidChannel_Ups_Options_HW) XXX_DiscardUnknown() {
	xxx_messageInfo_AndroidChannel_Ups_Options_HW.DiscardUnknown(m)
}

var xxx_messageInfo_AndroidChannel_Ups_Options_HW proto.InternalMessageInfo

func (m *AndroidChannel_Ups_Options_HW) GetBadgeClass() string {
	if m != nil {
		return m.BadgeClass
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_HW) GetBadgeAddNum() int32 {
	if m != nil {
		return m.BadgeAddNum
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_HW) GetBadgeSetNum() int32 {
	if m != nil {
		return m.BadgeSetNum
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_HW) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_HW) GetStyle() int32 {
	if m != nil {
		return m.Style
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_HW) GetBigTitle() string {
	if m != nil {
		return m.BigTitle
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_HW) GetBigBody() string {
	if m != nil {
		return m.BigBody
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_HW) GetImportance() string {
	if m != nil {
		return m.Importance
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_HW) GetDefaultSound() bool {
	if m != nil {
		return m.DefaultSound
	}
	return false
}

func (m *AndroidChannel_Ups_Options_HW) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_HW) GetSound() string {
	if m != nil {
		return m.Sound
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_HW) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_HW) GetTargetUserType() int32 {
	if m != nil {
		return m.TargetUserType
	}
	return 0
}

type AndroidChannel_Ups_Options_HO struct {
	// @gotags: json:"/android/notification/badge/badgeClass,omitempty"
	BadgeClass string `protobuf:"bytes,1,opt,name=badge_class,json=/android/notification/badge/badgeClass,omitempty,proto3" json:"/android/notification/badge/badgeClass,omitempty"`
	// @gotags: json:"/android/notification/badge/addNum,omitempty"
	BadgeAddNum int32 `protobuf:"varint,2,opt,name=badge_add_num,json=/android/notification/badge/addNum,omitempty,proto3" json:"/android/notification/badge/addNum,omitempty"`
	// @gotags: json:"/android/notification/badge/setNum,omitempty"
	BadgeSetNum int32 `protobuf:"varint,3,opt,name=badge_set_num,json=/android/notification/badge/setNum,omitempty,proto3" json:"/android/notification/badge/setNum,omitempty"`
	// @gotags: json:"/android/notification/icon,omitempty"
	Icon string `protobuf:"bytes,4,opt,name=icon,json=/android/notification/icon,omitempty,proto3" json:"/android/notification/icon,omitempty"`
	// @gotags: json:"/android/notification/image,omitempty"
	Image string `protobuf:"bytes,5,opt,name=image,json=/android/notification/image,omitempty,proto3" json:"/android/notification/image,omitempty"`
	// @gotags: json:"/android/notification/style,omitempty"
	Style int32 `protobuf:"varint,6,opt,name=style,json=/android/notification/style,omitempty,proto3" json:"/android/notification/style,omitempty"`
	// @gotags: json:"/android/notification/bigTitle,omitempty"
	BigTitle string `protobuf:"bytes,7,opt,name=big_title,json=/android/notification/bigTitle,omitempty,proto3" json:"/android/notification/bigTitle,omitempty"`
	// @gotags: json:"/android/notification/bigBody,omitempty"
	BigBody string `protobuf:"bytes,8,opt,name=big_body,json=/android/notification/bigBody,omitempty,proto3" json:"/android/notification/bigBody,omitempty"`
	// @gotags: json:"/android/notification/notifySummary,omitempty"
	NotifiySummary string `protobuf:"bytes,9,opt,name=notifiy_summary,json=/android/notification/notifySummary,omitempty,proto3" json:"/android/notification/notifySummary,omitempty"`
	// @gotags: json:"/android/notification/importance,omitempty"
	Importance string `protobuf:"bytes,10,opt,name=importance,json=/android/notification/importance,omitempty,proto3" json:"/android/notification/importance,omitempty"`
	// @gotags: json:"/android/notification/buttons,omitempty"
	Buttons []*AndroidChannel_Ups_Options_HO_Button `protobuf:"bytes,11,rep,name=buttons,json=/android/notification/buttons,omitempty,proto3" json:"/android/notification/buttons,omitempty"`
	// @gotags: json:"/android/notification/when,omitempty"
	When                 string   `protobuf:"bytes,12,opt,name=when,json=/android/notification/when,omitempty,proto3" json:"/android/notification/when,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AndroidChannel_Ups_Options_HO) Reset()         { *m = AndroidChannel_Ups_Options_HO{} }
func (m *AndroidChannel_Ups_Options_HO) String() string { return proto.CompactTextString(m) }
func (*AndroidChannel_Ups_Options_HO) ProtoMessage()    {}
func (*AndroidChannel_Ups_Options_HO) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{4, 0, 2, 1}
}
func (m *AndroidChannel_Ups_Options_HO) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AndroidChannel_Ups_Options_HO.Unmarshal(m, b)
}
func (m *AndroidChannel_Ups_Options_HO) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AndroidChannel_Ups_Options_HO.Marshal(b, m, deterministic)
}
func (dst *AndroidChannel_Ups_Options_HO) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AndroidChannel_Ups_Options_HO.Merge(dst, src)
}
func (m *AndroidChannel_Ups_Options_HO) XXX_Size() int {
	return xxx_messageInfo_AndroidChannel_Ups_Options_HO.Size(m)
}
func (m *AndroidChannel_Ups_Options_HO) XXX_DiscardUnknown() {
	xxx_messageInfo_AndroidChannel_Ups_Options_HO.DiscardUnknown(m)
}

var xxx_messageInfo_AndroidChannel_Ups_Options_HO proto.InternalMessageInfo

func (m *AndroidChannel_Ups_Options_HO) GetBadgeClass() string {
	if m != nil {
		return m.BadgeClass
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_HO) GetBadgeAddNum() int32 {
	if m != nil {
		return m.BadgeAddNum
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_HO) GetBadgeSetNum() int32 {
	if m != nil {
		return m.BadgeSetNum
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_HO) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_HO) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_HO) GetStyle() int32 {
	if m != nil {
		return m.Style
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_HO) GetBigTitle() string {
	if m != nil {
		return m.BigTitle
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_HO) GetBigBody() string {
	if m != nil {
		return m.BigBody
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_HO) GetNotifiySummary() string {
	if m != nil {
		return m.NotifiySummary
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_HO) GetImportance() string {
	if m != nil {
		return m.Importance
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_HO) GetButtons() []*AndroidChannel_Ups_Options_HO_Button {
	if m != nil {
		return m.Buttons
	}
	return nil
}

func (m *AndroidChannel_Ups_Options_HO) GetWhen() string {
	if m != nil {
		return m.When
	}
	return ""
}

type AndroidChannel_Ups_Options_HO_Button struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	ActionType           int32    `protobuf:"varint,2,opt,name=actionType,proto3" json:"actionType,omitempty"`
	IntentType           int32    `protobuf:"varint,3,opt,name=intentType,proto3" json:"intentType,omitempty"`
	Intent               string   `protobuf:"bytes,4,opt,name=intent,proto3" json:"intent,omitempty"`
	Data                 string   `protobuf:"bytes,5,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AndroidChannel_Ups_Options_HO_Button) Reset()         { *m = AndroidChannel_Ups_Options_HO_Button{} }
func (m *AndroidChannel_Ups_Options_HO_Button) String() string { return proto.CompactTextString(m) }
func (*AndroidChannel_Ups_Options_HO_Button) ProtoMessage()    {}
func (*AndroidChannel_Ups_Options_HO_Button) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{4, 0, 2, 1, 0}
}
func (m *AndroidChannel_Ups_Options_HO_Button) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AndroidChannel_Ups_Options_HO_Button.Unmarshal(m, b)
}
func (m *AndroidChannel_Ups_Options_HO_Button) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AndroidChannel_Ups_Options_HO_Button.Marshal(b, m, deterministic)
}
func (dst *AndroidChannel_Ups_Options_HO_Button) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AndroidChannel_Ups_Options_HO_Button.Merge(dst, src)
}
func (m *AndroidChannel_Ups_Options_HO_Button) XXX_Size() int {
	return xxx_messageInfo_AndroidChannel_Ups_Options_HO_Button.Size(m)
}
func (m *AndroidChannel_Ups_Options_HO_Button) XXX_DiscardUnknown() {
	xxx_messageInfo_AndroidChannel_Ups_Options_HO_Button.DiscardUnknown(m)
}

var xxx_messageInfo_AndroidChannel_Ups_Options_HO_Button proto.InternalMessageInfo

func (m *AndroidChannel_Ups_Options_HO_Button) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_HO_Button) GetActionType() int32 {
	if m != nil {
		return m.ActionType
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_HO_Button) GetIntentType() int32 {
	if m != nil {
		return m.IntentType
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_HO_Button) GetIntent() string {
	if m != nil {
		return m.Intent
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_HO_Button) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

type AndroidChannel_Ups_Options_XM struct {
	// @gotags: json:"/extra.channel_id,omitempty"
	ChannelId string `protobuf:"bytes,1,opt,name=channel_id,json=/extra.channel_id,omitempty,proto3" json:"/extra.channel_id,omitempty"`
	// @gotags: json:"/extra.notification_style_type,omitempty"
	NotificationStyleType string `protobuf:"bytes,2,opt,name=notification_style_type,json=/extra.notification_style_type,omitempty,proto3" json:"/extra.notification_style_type,omitempty"`
	// @gotags: json "/extra.notification_bigPic_uri,omitempty"
	NotificationBigPicUri string `protobuf:"bytes,3,opt,name=notification_bigPic_uri,json=/extra.notification_bigPic_uri,omitempty,proto3" json:"notification_bigPic_uri,omitempty"`
	// @gotags: json:"/extra.notification_large_icon_uri,omitempty"
	NotificationLargeIconUri string `protobuf:"bytes,4,opt,name=notification_large_icon_uri,json=/extra.notification_large_icon_uri,omitempty,proto3" json:"/extra.notification_large_icon_uri,omitempty"`
	// @gotags: json:"/extra.sound_uri,omitempty"
	SoundUri string `protobuf:"bytes,6,opt,name=sound_uri,json=/extra.sound_uri,omitempty,proto3" json:"/extra.sound_uri,omitempty"`
	// @gotags: json:"/extra.locale,omitempty"
	Locale string `protobuf:"bytes,7,opt,name=locale,json=/extra.locale,omitempty,proto3" json:"/extra.locale,omitempty"`
	// @gotags: json:"/extra.locale_not_in,omitempty"
	LocaleNotIn string `protobuf:"bytes,8,opt,name=locale_not_in,json=/extra.locale_not_in,omitempty,proto3" json:"/extra.locale_not_in,omitempty"`
	// @gotags: json:"/extra.model,omitempty"
	Model string `protobuf:"bytes,9,opt,name=model,json=/extra.model,omitempty,proto3" json:"/extra.model,omitempty"`
	// @gotags: json:"/extra.model_not_in,omitempty"
	ModelNotIn string `protobuf:"bytes,10,opt,name=model_not_in,json=/extra.model_not_in,omitempty,proto3" json:"/extra.model_not_in,omitempty"`
	// @gotags: json:"/extra.app_version,omitempty"
	AppVersion string `protobuf:"bytes,11,opt,name=app_version,json=/extra.app_version,omitempty,proto3" json:"/extra.app_version,omitempty"`
	// @gotags: json:"/extra.app_version_not_in,omitempty"
	AppVersionNotIn string `protobuf:"bytes,12,opt,name=app_version_not_in,json=/extra.app_version_not_in,omitempty,proto3" json:"/extra.app_version_not_in,omitempty"`
	// @gotags: json:"/extra.notification_style_button_left_notify_effect,omitempty"
	StyleButtonLeftNotifyEffect string `protobuf:"bytes,13,opt,name=style_button_left_notify_effect,json=/extra.notification_style_button_left_notify_effect,omitempty,proto3" json:"/extra.notification_style_button_left_notify_effect,omitempty"`
	// @gotags: json:"/extra.notification_style_button_left_name,omitempty"
	StyleButtonLeftName string `protobuf:"bytes,14,opt,name=style_button_left_name,json=/extra.notification_style_button_left_name,omitempty,proto3" json:"/extra.notification_style_button_left_name,omitempty"`
	// @gotags: json:"/extra.notification_style_button_left_intent_uri,omitempty"
	StyleButtonLeftIntentUri string `protobuf:"bytes,15,opt,name=style_button_left_intent_uri,json=/extra.notification_style_button_left_intent_uri,omitempty,proto3" json:"/extra.notification_style_button_left_intent_uri,omitempty"`
	// @gotags: json:"/extra.notification_style_button_left_web_uri,omitempty"
	StyleButtonLeftWebUri string `protobuf:"bytes,16,opt,name=style_button_left_web_uri,json=/extra.notification_style_button_left_web_uri,omitempty,proto3" json:"/extra.notification_style_button_left_web_uri,omitempty"`
	// @gotags: json:"/extra.notification_style_button_left_intent_class,omitempty"
	StyleButtonLeftIntentClass string `protobuf:"bytes,17,opt,name=style_button_left_intent_class,json=/extra.notification_style_button_left_intent_class,omitempty,proto3" json:"/extra.notification_style_button_left_intent_class,omitempty"`
	// @gotags: json:"/extra.notification_style_button_right_notify_effect,omitempty"
	StyleButtonRightNotifyEffect string `protobuf:"bytes,18,opt,name=style_button_right_notify_effect,json=/extra.notification_style_button_right_notify_effect,omitempty,proto3" json:"/extra.notification_style_button_right_notify_effect,omitempty"`
	// @gotags: json:"/extra.notification_style_button_right_name,omitempty"
	StyleButtonRightName string `protobuf:"bytes,19,opt,name=style_button_right_name,json=/extra.notification_style_button_right_name,omitempty,proto3" json:"/extra.notification_style_button_right_name,omitempty"`
	// @gotags: json:"/extra.notification_style_button_right_intent_uri,omitempty"
	StyleButtonRightIntentUri string `protobuf:"bytes,20,opt,name=style_button_right_intent_uri,json=/extra.notification_style_button_right_intent_uri,omitempty,proto3" json:"/extra.notification_style_button_right_intent_uri,omitempty"`
	// @gotags: json:"/extra.notification_style_button_right_web_uri,omitempty"
	StyleButtonRightWebUri string `protobuf:"bytes,21,opt,name=style_button_right_web_uri,json=/extra.notification_style_button_right_web_uri,omitempty,proto3" json:"/extra.notification_style_button_right_web_uri,omitempty"`
	// @gotags: json:"/extra.notification_style_button_right_intent_class,omitempty"
	StyleButtonRightIntentClass string `protobuf:"bytes,22,opt,name=style_button_right_intent_class,json=/extra.notification_style_button_right_intent_class,omitempty,proto3" json:"/extra.notification_style_button_right_intent_class,omitempty"`
	// @gotags: json:"/extra.notification_style_button_mid_notify_effect,omitempty"
	StyleButtonMidNotifyEffect string `protobuf:"bytes,23,opt,name=style_button_mid_notify_effect,json=/extra.notification_style_button_mid_notify_effect,omitempty,proto3" json:"/extra.notification_style_button_mid_notify_effect,omitempty"`
	// @gotags: json:"/extra.notification_style_button_mid_name,omitempty"
	StyleButtonMidName string `protobuf:"bytes,24,opt,name=style_button_mid_name,json=/extra.notification_style_button_mid_name,omitempty,proto3" json:"/extra.notification_style_button_mid_name,omitempty"`
	// @gotags: json:"/extra.notification_style_button_mid_intent_uri,omitempty"
	StyleButtonMidIntentUri string `protobuf:"bytes,25,opt,name=style_button_mid_intent_uri,json=/extra.notification_style_button_mid_intent_uri,omitempty,proto3" json:"/extra.notification_style_button_mid_intent_uri,omitempty"`
	// @gotags: json:"/extra.notification_style_button_mid_web_uri,omitempty"
	StyleButtonMidWebUri string `protobuf:"bytes,26,opt,name=style_button_mid_web_uri,json=/extra.notification_style_button_mid_web_uri,omitempty,proto3" json:"/extra.notification_style_button_mid_web_uri,omitempty"`
	// @gotags: json:"/extra.notification_style_button_mid_intent_class,omitempty"
	StyleButtonMidIntentClass string `protobuf:"bytes,27,opt,name=style_button_mid_intent_class,json=/extra.notification_style_button_mid_intent_class,omitempty,proto3" json:"/extra.notification_style_button_mid_intent_class,omitempty"`
	// @gotags: json:"/time_to_live,omitempty"
	TimeToLive int64 `protobuf:"varint,28,opt,name=time_to_live,json=/time_to_live,omitempty,proto3" json:"/time_to_live,omitempty"`
	// @gotags: json:"/extra.only_send_once,omitempty"
	OnlySendOnce string `protobuf:"bytes,29,opt,name=only_send_once,json=/extra.only_send_once,omitempty,proto3" json:"/extra.only_send_once,omitempty"`
	// @gotags: json:"/time_to_send,omitempty"
	TimeToSend           int64    `protobuf:"varint,30,opt,name=time_to_send,json=/time_to_send,omitempty,proto3" json:"/time_to_send,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AndroidChannel_Ups_Options_XM) Reset()         { *m = AndroidChannel_Ups_Options_XM{} }
func (m *AndroidChannel_Ups_Options_XM) String() string { return proto.CompactTextString(m) }
func (*AndroidChannel_Ups_Options_XM) ProtoMessage()    {}
func (*AndroidChannel_Ups_Options_XM) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{4, 0, 2, 2}
}
func (m *AndroidChannel_Ups_Options_XM) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AndroidChannel_Ups_Options_XM.Unmarshal(m, b)
}
func (m *AndroidChannel_Ups_Options_XM) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AndroidChannel_Ups_Options_XM.Marshal(b, m, deterministic)
}
func (dst *AndroidChannel_Ups_Options_XM) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AndroidChannel_Ups_Options_XM.Merge(dst, src)
}
func (m *AndroidChannel_Ups_Options_XM) XXX_Size() int {
	return xxx_messageInfo_AndroidChannel_Ups_Options_XM.Size(m)
}
func (m *AndroidChannel_Ups_Options_XM) XXX_DiscardUnknown() {
	xxx_messageInfo_AndroidChannel_Ups_Options_XM.DiscardUnknown(m)
}

var xxx_messageInfo_AndroidChannel_Ups_Options_XM proto.InternalMessageInfo

func (m *AndroidChannel_Ups_Options_XM) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetNotificationStyleType() string {
	if m != nil {
		return m.NotificationStyleType
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetNotificationBigPicUri() string {
	if m != nil {
		return m.NotificationBigPicUri
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetNotificationLargeIconUri() string {
	if m != nil {
		return m.NotificationLargeIconUri
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetSoundUri() string {
	if m != nil {
		return m.SoundUri
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetLocale() string {
	if m != nil {
		return m.Locale
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetLocaleNotIn() string {
	if m != nil {
		return m.LocaleNotIn
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetModel() string {
	if m != nil {
		return m.Model
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetModelNotIn() string {
	if m != nil {
		return m.ModelNotIn
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetAppVersion() string {
	if m != nil {
		return m.AppVersion
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetAppVersionNotIn() string {
	if m != nil {
		return m.AppVersionNotIn
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetStyleButtonLeftNotifyEffect() string {
	if m != nil {
		return m.StyleButtonLeftNotifyEffect
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetStyleButtonLeftName() string {
	if m != nil {
		return m.StyleButtonLeftName
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetStyleButtonLeftIntentUri() string {
	if m != nil {
		return m.StyleButtonLeftIntentUri
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetStyleButtonLeftWebUri() string {
	if m != nil {
		return m.StyleButtonLeftWebUri
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetStyleButtonLeftIntentClass() string {
	if m != nil {
		return m.StyleButtonLeftIntentClass
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetStyleButtonRightNotifyEffect() string {
	if m != nil {
		return m.StyleButtonRightNotifyEffect
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetStyleButtonRightName() string {
	if m != nil {
		return m.StyleButtonRightName
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetStyleButtonRightIntentUri() string {
	if m != nil {
		return m.StyleButtonRightIntentUri
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetStyleButtonRightWebUri() string {
	if m != nil {
		return m.StyleButtonRightWebUri
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetStyleButtonRightIntentClass() string {
	if m != nil {
		return m.StyleButtonRightIntentClass
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetStyleButtonMidNotifyEffect() string {
	if m != nil {
		return m.StyleButtonMidNotifyEffect
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetStyleButtonMidName() string {
	if m != nil {
		return m.StyleButtonMidName
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetStyleButtonMidIntentUri() string {
	if m != nil {
		return m.StyleButtonMidIntentUri
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetStyleButtonMidWebUri() string {
	if m != nil {
		return m.StyleButtonMidWebUri
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetStyleButtonMidIntentClass() string {
	if m != nil {
		return m.StyleButtonMidIntentClass
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetTimeToLive() int64 {
	if m != nil {
		return m.TimeToLive
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_XM) GetOnlySendOnce() string {
	if m != nil {
		return m.OnlySendOnce
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_XM) GetTimeToSend() int64 {
	if m != nil {
		return m.TimeToSend
	}
	return 0
}

type AndroidChannel_Ups_Options_OP struct {
	// @gotags: json:"/channel_id,omitempty"
	ChannelId string `protobuf:"bytes,1,opt,name=channel_id,json=/channel_id,omitempty,proto3" json:"/channel_id,omitempty"`
	// @gotags: json:"/style,omitempty"
	Style int32 `protobuf:"varint,2,opt,name=style,json=/style,omitempty,proto3" json:"/style,omitempty"`
	// @gotags: json:"/small_picture_id,omitempty"
	SmallPictureId string `protobuf:"bytes,3,opt,name=small_picture_id,json=/small_picture_id,omitempty,proto3" json:"/small_picture_id,omitempty"`
	// @gotags: json:"/big_picture_id,omitempty"
	BigPictureId string `protobuf:"bytes,4,opt,name=big_picture_id,json=/big_picture_id,omitempty,proto3" json:"/big_picture_id,omitempty"`
	// @gotags: json:"/app_message_id,omitempty"
	AppMessageId string `protobuf:"bytes,5,opt,name=app_message_id,json=/app_message_id,omitempty,proto3" json:"/app_message_id,omitempty"`
	// @gotags: json:"/show_time_type,omitempty"
	ShowTimeType int32 `protobuf:"varint,6,opt,name=show_time_type,json=/show_time_type,omitempty,proto3" json:"/show_time_type,omitempty"`
	// @gotags: json:"/show_start_time,omitempty"
	ShowStartTime int64 `protobuf:"varint,7,opt,name=show_start_time,json=/show_start_time,omitempty,proto3" json:"/show_start_time,omitempty"`
	// @gotags: json:"/show_end_time,omitempty"
	ShowEndTime int64 `protobuf:"varint,8,opt,name=show_end_time,json=/show_end_time,omitempty,proto3" json:"/show_end_time,omitempty"`
	// @gotags: json:"/off_line,omitempty"
	OffLine bool `protobuf:"varint,9,opt,name=off_line,json=/off_line,omitempty,proto3" json:"/off_line,omitempty"`
	// @gotags: json:"/off_line_ttl,omitempty"
	OffLineTtl int32 `protobuf:"varint,10,opt,name=off_line_ttl,json=/off_line_ttl,omitempty,proto3" json:"/off_line_ttl,omitempty"`
	// @gotags: json:"/show_ttl,omitempty"
	ShowTtl int32 `protobuf:"varint,11,opt,name=show_ttl,json=/show_ttl,omitempty,proto3" json:"/show_ttl,omitempty"`
	// @gotags: json:"/small_picture_url,omitempty"
	SmallPictureUrl string `protobuf:"bytes,12,opt,name=small_picture_url,json=/small_picture_url,omitempty,proto3" json:"/small_picture_url,omitempty"`
	// @gotags: json:"/medium_picture_url,omitempty"
	MediumPictureUrl string `protobuf:"bytes,13,opt,name=medium_picture_url,json=/medium_picture_url,omitempty,proto3" json:"/medium_picture_url,omitempty"`
	// @gotags: json:"/big_picture_url,omitempty"
	BigPictureUrl string `protobuf:"bytes,14,opt,name=big_picture_url,json=/big_picture_url,omitempty,proto3" json:"/big_picture_url,omitempty"`
	// @gotags: json:"/buttons,omitempty"
	Buttons []*AndroidChannel_Ups_Options_OP_BUTTON `protobuf:"bytes,15,rep,name=buttons,json=/buttons,omitempty,proto3" json:"/buttons,omitempty"`
	// @gotags: json:"/option,omitempty"
	Option               *AndroidChannel_Ups_Options_OP_OPTION `protobuf:"bytes,16,opt,name=option,json=/option,omitempty,proto3" json:"/option,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *AndroidChannel_Ups_Options_OP) Reset()         { *m = AndroidChannel_Ups_Options_OP{} }
func (m *AndroidChannel_Ups_Options_OP) String() string { return proto.CompactTextString(m) }
func (*AndroidChannel_Ups_Options_OP) ProtoMessage()    {}
func (*AndroidChannel_Ups_Options_OP) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{4, 0, 2, 3}
}
func (m *AndroidChannel_Ups_Options_OP) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AndroidChannel_Ups_Options_OP.Unmarshal(m, b)
}
func (m *AndroidChannel_Ups_Options_OP) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AndroidChannel_Ups_Options_OP.Marshal(b, m, deterministic)
}
func (dst *AndroidChannel_Ups_Options_OP) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AndroidChannel_Ups_Options_OP.Merge(dst, src)
}
func (m *AndroidChannel_Ups_Options_OP) XXX_Size() int {
	return xxx_messageInfo_AndroidChannel_Ups_Options_OP.Size(m)
}
func (m *AndroidChannel_Ups_Options_OP) XXX_DiscardUnknown() {
	xxx_messageInfo_AndroidChannel_Ups_Options_OP.DiscardUnknown(m)
}

var xxx_messageInfo_AndroidChannel_Ups_Options_OP proto.InternalMessageInfo

func (m *AndroidChannel_Ups_Options_OP) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_OP) GetStyle() int32 {
	if m != nil {
		return m.Style
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_OP) GetSmallPictureId() string {
	if m != nil {
		return m.SmallPictureId
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_OP) GetBigPictureId() string {
	if m != nil {
		return m.BigPictureId
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_OP) GetAppMessageId() string {
	if m != nil {
		return m.AppMessageId
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_OP) GetShowTimeType() int32 {
	if m != nil {
		return m.ShowTimeType
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_OP) GetShowStartTime() int64 {
	if m != nil {
		return m.ShowStartTime
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_OP) GetShowEndTime() int64 {
	if m != nil {
		return m.ShowEndTime
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_OP) GetOffLine() bool {
	if m != nil {
		return m.OffLine
	}
	return false
}

func (m *AndroidChannel_Ups_Options_OP) GetOffLineTtl() int32 {
	if m != nil {
		return m.OffLineTtl
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_OP) GetShowTtl() int32 {
	if m != nil {
		return m.ShowTtl
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_OP) GetSmallPictureUrl() string {
	if m != nil {
		return m.SmallPictureUrl
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_OP) GetMediumPictureUrl() string {
	if m != nil {
		return m.MediumPictureUrl
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_OP) GetBigPictureUrl() string {
	if m != nil {
		return m.BigPictureUrl
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_OP) GetButtons() []*AndroidChannel_Ups_Options_OP_BUTTON {
	if m != nil {
		return m.Buttons
	}
	return nil
}

func (m *AndroidChannel_Ups_Options_OP) GetOption() *AndroidChannel_Ups_Options_OP_OPTION {
	if m != nil {
		return m.Option
	}
	return nil
}

type AndroidChannel_Ups_Options_OP_BUTTON struct {
	// @gotags: json:"name,omitempty"
	Name string `protobuf:"bytes,1,opt,name=name,json=name,omitempty,proto3" json:"name,omitempty"`
	// @gotags: json:"clickActionType,omitempty"
	ClickActionType int32 `protobuf:"varint,2,opt,name=clickActionType,json=clickActionType,omitempty,proto3" json:"clickActionType,omitempty"`
	// @gotags: json:"clickActionUrl,omitempty"
	ClickActionUrl string `protobuf:"bytes,3,opt,name=clickActionUrl,json=clickActionUrl,omitempty,proto3" json:"clickActionUrl,omitempty"`
	// @gotags: json:"clickActionActivity,omitempty"
	ClickActionActivity string `protobuf:"bytes,4,opt,name=clickActionActivity,json=clickActionActivity,omitempty,proto3" json:"clickActionActivity,omitempty"`
	// @gotags: json:"actionParameters,omitempty"
	ActionParameters     map[string]string `protobuf:"bytes,5,rep,name=actionParameters,json=actionParameters,omitempty,proto3" json:"actionParameters,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AndroidChannel_Ups_Options_OP_BUTTON) Reset()         { *m = AndroidChannel_Ups_Options_OP_BUTTON{} }
func (m *AndroidChannel_Ups_Options_OP_BUTTON) String() string { return proto.CompactTextString(m) }
func (*AndroidChannel_Ups_Options_OP_BUTTON) ProtoMessage()    {}
func (*AndroidChannel_Ups_Options_OP_BUTTON) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{4, 0, 2, 3, 0}
}
func (m *AndroidChannel_Ups_Options_OP_BUTTON) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AndroidChannel_Ups_Options_OP_BUTTON.Unmarshal(m, b)
}
func (m *AndroidChannel_Ups_Options_OP_BUTTON) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AndroidChannel_Ups_Options_OP_BUTTON.Marshal(b, m, deterministic)
}
func (dst *AndroidChannel_Ups_Options_OP_BUTTON) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AndroidChannel_Ups_Options_OP_BUTTON.Merge(dst, src)
}
func (m *AndroidChannel_Ups_Options_OP_BUTTON) XXX_Size() int {
	return xxx_messageInfo_AndroidChannel_Ups_Options_OP_BUTTON.Size(m)
}
func (m *AndroidChannel_Ups_Options_OP_BUTTON) XXX_DiscardUnknown() {
	xxx_messageInfo_AndroidChannel_Ups_Options_OP_BUTTON.DiscardUnknown(m)
}

var xxx_messageInfo_AndroidChannel_Ups_Options_OP_BUTTON proto.InternalMessageInfo

func (m *AndroidChannel_Ups_Options_OP_BUTTON) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_OP_BUTTON) GetClickActionType() int32 {
	if m != nil {
		return m.ClickActionType
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_OP_BUTTON) GetClickActionUrl() string {
	if m != nil {
		return m.ClickActionUrl
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_OP_BUTTON) GetClickActionActivity() string {
	if m != nil {
		return m.ClickActionActivity
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_OP_BUTTON) GetActionParameters() map[string]string {
	if m != nil {
		return m.ActionParameters
	}
	return nil
}

type AndroidChannel_Ups_Options_OP_OPTION struct {
	// @gotags: json:"notification_group,omitempty"
	NotificationGroup string `protobuf:"bytes,1,opt,name=notification_group,json=notification_group,omitempty,proto3" json:"notification_group,omitempty"`
	// @gotags: json:"m_url,omitempty"
	MUrl string `protobuf:"bytes,2,opt,name=m_url,json=m_url,omitempty,proto3" json:"m_url,omitempty"`
	// @gotags: json:"senior_push,omitempty"
	SeniorPush bool `protobuf:"varint,3,opt,name=senior_push,json=senior_push,omitempty,proto3" json:"senior_push,omitempty"`
	// @gotags: json:"unfold,omitempty"
	Unfold bool `protobuf:"varint,4,opt,name=unfold,json=unfold,omitempty,proto3" json:"unfold,omitempty"`
	// @gotags: json:"top_notification_bar_show,omitempty"
	TopNotificationBarShow bool `protobuf:"varint,5,opt,name=top_notification_bar_show,json=top_notification_bar_show,omitempty,proto3" json:"top_notification_bar_show,omitempty"`
	// @gotags: json:"material_id,omitempty"
	MaterialId string `protobuf:"bytes,6,opt,name=material_id,json=material_id,omitempty,proto3" json:"material_id,omitempty"`
	// @gotags: json:"label_id,omitempty"
	LabelId int32 `protobuf:"varint,7,opt,name=label_id,json=label_id,omitempty,proto3" json:"label_id,omitempty"`
	// @gotags: json:"filter,omitempty"
	Filter *AndroidChannel_Ups_Options_OP_OPTION_FILTER `protobuf:"bytes,8,opt,name=filter,json=filter,omitempty,proto3" json:"filter,omitempty"`
	// @gotags: json:"advertiser_id,omitempty"
	AdvertiserId         string   `protobuf:"bytes,9,opt,name=advertiser_id,json=advertiser_id,omitempty,proto3" json:"advertiser_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AndroidChannel_Ups_Options_OP_OPTION) Reset()         { *m = AndroidChannel_Ups_Options_OP_OPTION{} }
func (m *AndroidChannel_Ups_Options_OP_OPTION) String() string { return proto.CompactTextString(m) }
func (*AndroidChannel_Ups_Options_OP_OPTION) ProtoMessage()    {}
func (*AndroidChannel_Ups_Options_OP_OPTION) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{4, 0, 2, 3, 1}
}
func (m *AndroidChannel_Ups_Options_OP_OPTION) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AndroidChannel_Ups_Options_OP_OPTION.Unmarshal(m, b)
}
func (m *AndroidChannel_Ups_Options_OP_OPTION) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AndroidChannel_Ups_Options_OP_OPTION.Marshal(b, m, deterministic)
}
func (dst *AndroidChannel_Ups_Options_OP_OPTION) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AndroidChannel_Ups_Options_OP_OPTION.Merge(dst, src)
}
func (m *AndroidChannel_Ups_Options_OP_OPTION) XXX_Size() int {
	return xxx_messageInfo_AndroidChannel_Ups_Options_OP_OPTION.Size(m)
}
func (m *AndroidChannel_Ups_Options_OP_OPTION) XXX_DiscardUnknown() {
	xxx_messageInfo_AndroidChannel_Ups_Options_OP_OPTION.DiscardUnknown(m)
}

var xxx_messageInfo_AndroidChannel_Ups_Options_OP_OPTION proto.InternalMessageInfo

func (m *AndroidChannel_Ups_Options_OP_OPTION) GetNotificationGroup() string {
	if m != nil {
		return m.NotificationGroup
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_OP_OPTION) GetMUrl() string {
	if m != nil {
		return m.MUrl
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_OP_OPTION) GetSeniorPush() bool {
	if m != nil {
		return m.SeniorPush
	}
	return false
}

func (m *AndroidChannel_Ups_Options_OP_OPTION) GetUnfold() bool {
	if m != nil {
		return m.Unfold
	}
	return false
}

func (m *AndroidChannel_Ups_Options_OP_OPTION) GetTopNotificationBarShow() bool {
	if m != nil {
		return m.TopNotificationBarShow
	}
	return false
}

func (m *AndroidChannel_Ups_Options_OP_OPTION) GetMaterialId() string {
	if m != nil {
		return m.MaterialId
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_OP_OPTION) GetLabelId() int32 {
	if m != nil {
		return m.LabelId
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_OP_OPTION) GetFilter() *AndroidChannel_Ups_Options_OP_OPTION_FILTER {
	if m != nil {
		return m.Filter
	}
	return nil
}

func (m *AndroidChannel_Ups_Options_OP_OPTION) GetAdvertiserId() string {
	if m != nil {
		return m.AdvertiserId
	}
	return ""
}

type AndroidChannel_Ups_Options_OP_OPTION_FILTER struct {
	// @gotags: json:"unsupport_top_show,omitempty"
	UnsupportTopShow int32 `protobuf:"varint,1,opt,name=unsupport_top_show,json=unsupport_top_show,omitempty,proto3" json:"unsupport_top_show,omitempty"`
	// @gotags: json:"unsupport_unfold,omitempty"
	UnsupportUnfold int32 `protobuf:"varint,2,opt,name=unsupport_unfold,json=unsupport_unfold,omitempty,proto3" json:"unsupport_unfold,omitempty"`
	// @gotags: json:"unsupport_label,omitempty"
	UnsupportLabel int32 `protobuf:"varint,3,opt,name=unsupport_label,json=unsupport_label,omitempty,proto3" json:"unsupport_label,omitempty"`
	// @gotags: json:"unsupport_skin,omitempty"
	UnsupportSkin        int32    `protobuf:"varint,4,opt,name=unsupport_skin,json=unsupport_skin,omitempty,proto3" json:"unsupport_skin,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AndroidChannel_Ups_Options_OP_OPTION_FILTER) Reset() {
	*m = AndroidChannel_Ups_Options_OP_OPTION_FILTER{}
}
func (m *AndroidChannel_Ups_Options_OP_OPTION_FILTER) String() string {
	return proto.CompactTextString(m)
}
func (*AndroidChannel_Ups_Options_OP_OPTION_FILTER) ProtoMessage() {}
func (*AndroidChannel_Ups_Options_OP_OPTION_FILTER) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{4, 0, 2, 3, 1, 0}
}
func (m *AndroidChannel_Ups_Options_OP_OPTION_FILTER) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AndroidChannel_Ups_Options_OP_OPTION_FILTER.Unmarshal(m, b)
}
func (m *AndroidChannel_Ups_Options_OP_OPTION_FILTER) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AndroidChannel_Ups_Options_OP_OPTION_FILTER.Marshal(b, m, deterministic)
}
func (dst *AndroidChannel_Ups_Options_OP_OPTION_FILTER) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AndroidChannel_Ups_Options_OP_OPTION_FILTER.Merge(dst, src)
}
func (m *AndroidChannel_Ups_Options_OP_OPTION_FILTER) XXX_Size() int {
	return xxx_messageInfo_AndroidChannel_Ups_Options_OP_OPTION_FILTER.Size(m)
}
func (m *AndroidChannel_Ups_Options_OP_OPTION_FILTER) XXX_DiscardUnknown() {
	xxx_messageInfo_AndroidChannel_Ups_Options_OP_OPTION_FILTER.DiscardUnknown(m)
}

var xxx_messageInfo_AndroidChannel_Ups_Options_OP_OPTION_FILTER proto.InternalMessageInfo

func (m *AndroidChannel_Ups_Options_OP_OPTION_FILTER) GetUnsupportTopShow() int32 {
	if m != nil {
		return m.UnsupportTopShow
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_OP_OPTION_FILTER) GetUnsupportUnfold() int32 {
	if m != nil {
		return m.UnsupportUnfold
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_OP_OPTION_FILTER) GetUnsupportLabel() int32 {
	if m != nil {
		return m.UnsupportLabel
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_OP_OPTION_FILTER) GetUnsupportSkin() int32 {
	if m != nil {
		return m.UnsupportSkin
	}
	return 0
}

type AndroidChannel_Ups_Options_VV struct {
	// @gotags: json:"/classification,omitempty"
	Classification int32 `protobuf:"varint,1,opt,name=classification,json=/classification,omitempty,proto3" json:"/classification,omitempty"`
	// @gotags: json:"/notifyType,omitempty"
	NotifyType int32 `protobuf:"varint,2,opt,name=notify_type,json=/notifyType,omitempty,proto3" json:"/notifyType,omitempty"`
	// @gotags: json:"/networkType,omitempty"
	NetworkType int32 `protobuf:"varint,3,opt,name=network_type,json=/networkType,omitempty,proto3" json:"/networkType,omitempty"`
	// @gotags: json:"/timeToLive,omitempty"
	TimeToLive int32 `protobuf:"varint,4,opt,name=time_to_live,json=/timeToLive,omitempty,proto3" json:"/timeToLive,omitempty"`
	// @gotags: json:"/category,omitempty"
	Category string `protobuf:"bytes,5,opt,name=category,json=/category,omitempty,proto3" json:"/category,omitempty"`
	// @gotags: json:"/pushMode,omitempty"
	PushMode             int32    `protobuf:"varint,6,opt,name=push_mode,json=/pushMode,omitempty,proto3" json:"/pushMode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AndroidChannel_Ups_Options_VV) Reset()         { *m = AndroidChannel_Ups_Options_VV{} }
func (m *AndroidChannel_Ups_Options_VV) String() string { return proto.CompactTextString(m) }
func (*AndroidChannel_Ups_Options_VV) ProtoMessage()    {}
func (*AndroidChannel_Ups_Options_VV) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{4, 0, 2, 4}
}
func (m *AndroidChannel_Ups_Options_VV) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AndroidChannel_Ups_Options_VV.Unmarshal(m, b)
}
func (m *AndroidChannel_Ups_Options_VV) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AndroidChannel_Ups_Options_VV.Marshal(b, m, deterministic)
}
func (dst *AndroidChannel_Ups_Options_VV) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AndroidChannel_Ups_Options_VV.Merge(dst, src)
}
func (m *AndroidChannel_Ups_Options_VV) XXX_Size() int {
	return xxx_messageInfo_AndroidChannel_Ups_Options_VV.Size(m)
}
func (m *AndroidChannel_Ups_Options_VV) XXX_DiscardUnknown() {
	xxx_messageInfo_AndroidChannel_Ups_Options_VV.DiscardUnknown(m)
}

var xxx_messageInfo_AndroidChannel_Ups_Options_VV proto.InternalMessageInfo

func (m *AndroidChannel_Ups_Options_VV) GetClassification() int32 {
	if m != nil {
		return m.Classification
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_VV) GetNotifyType() int32 {
	if m != nil {
		return m.NotifyType
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_VV) GetNetworkType() int32 {
	if m != nil {
		return m.NetworkType
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_VV) GetTimeToLive() int32 {
	if m != nil {
		return m.TimeToLive
	}
	return 0
}

func (m *AndroidChannel_Ups_Options_VV) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_VV) GetPushMode() int32 {
	if m != nil {
		return m.PushMode
	}
	return 0
}

type AndroidChannel_Ups_Options_UPS struct {
	// @gotags: json:"bigText,omitempty"
	BigText string `protobuf:"bytes,1,opt,name=big_text,json=bigText,omitempty,proto3" json:"bigText,omitempty"`
	// @gotags: json:"bigImageUrl,omitempty"
	BigImageUrl          string   `protobuf:"bytes,2,opt,name=big_image_url,json=bigImageUrl,omitempty,proto3" json:"bigImageUrl,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AndroidChannel_Ups_Options_UPS) Reset()         { *m = AndroidChannel_Ups_Options_UPS{} }
func (m *AndroidChannel_Ups_Options_UPS) String() string { return proto.CompactTextString(m) }
func (*AndroidChannel_Ups_Options_UPS) ProtoMessage()    {}
func (*AndroidChannel_Ups_Options_UPS) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{4, 0, 2, 5}
}
func (m *AndroidChannel_Ups_Options_UPS) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AndroidChannel_Ups_Options_UPS.Unmarshal(m, b)
}
func (m *AndroidChannel_Ups_Options_UPS) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AndroidChannel_Ups_Options_UPS.Marshal(b, m, deterministic)
}
func (dst *AndroidChannel_Ups_Options_UPS) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AndroidChannel_Ups_Options_UPS.Merge(dst, src)
}
func (m *AndroidChannel_Ups_Options_UPS) XXX_Size() int {
	return xxx_messageInfo_AndroidChannel_Ups_Options_UPS.Size(m)
}
func (m *AndroidChannel_Ups_Options_UPS) XXX_DiscardUnknown() {
	xxx_messageInfo_AndroidChannel_Ups_Options_UPS.DiscardUnknown(m)
}

var xxx_messageInfo_AndroidChannel_Ups_Options_UPS proto.InternalMessageInfo

func (m *AndroidChannel_Ups_Options_UPS) GetBigText() string {
	if m != nil {
		return m.BigText
	}
	return ""
}

func (m *AndroidChannel_Ups_Options_UPS) GetBigImageUrl() string {
	if m != nil {
		return m.BigImageUrl
	}
	return ""
}

type MiniProgram struct {
	Wx                   *MiniProgram_WX `protobuf:"bytes,1,opt,name=wx,proto3" json:"wx,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *MiniProgram) Reset()         { *m = MiniProgram{} }
func (m *MiniProgram) String() string { return proto.CompactTextString(m) }
func (*MiniProgram) ProtoMessage()    {}
func (*MiniProgram) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{5}
}
func (m *MiniProgram) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MiniProgram.Unmarshal(m, b)
}
func (m *MiniProgram) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MiniProgram.Marshal(b, m, deterministic)
}
func (dst *MiniProgram) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MiniProgram.Merge(dst, src)
}
func (m *MiniProgram) XXX_Size() int {
	return xxx_messageInfo_MiniProgram.Size(m)
}
func (m *MiniProgram) XXX_DiscardUnknown() {
	xxx_messageInfo_MiniProgram.DiscardUnknown(m)
}

var xxx_messageInfo_MiniProgram proto.InternalMessageInfo

func (m *MiniProgram) GetWx() *MiniProgram_WX {
	if m != nil {
		return m.Wx
	}
	return nil
}

type MiniProgram_WX struct {
	TemplatedId          string            `protobuf:"bytes,1,opt,name=templated_id,json=templatedId,proto3" json:"templated_id,omitempty"`
	Page                 string            `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
	MiniprogramState     string            `protobuf:"bytes,3,opt,name=miniprogram_state,json=miniprogramState,proto3" json:"miniprogram_state,omitempty"`
	Lang                 string            `protobuf:"bytes,4,opt,name=lang,proto3" json:"lang,omitempty"`
	Data                 map[string]string `protobuf:"bytes,5,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *MiniProgram_WX) Reset()         { *m = MiniProgram_WX{} }
func (m *MiniProgram_WX) String() string { return proto.CompactTextString(m) }
func (*MiniProgram_WX) ProtoMessage()    {}
func (*MiniProgram_WX) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{5, 0}
}
func (m *MiniProgram_WX) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MiniProgram_WX.Unmarshal(m, b)
}
func (m *MiniProgram_WX) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MiniProgram_WX.Marshal(b, m, deterministic)
}
func (dst *MiniProgram_WX) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MiniProgram_WX.Merge(dst, src)
}
func (m *MiniProgram_WX) XXX_Size() int {
	return xxx_messageInfo_MiniProgram_WX.Size(m)
}
func (m *MiniProgram_WX) XXX_DiscardUnknown() {
	xxx_messageInfo_MiniProgram_WX.DiscardUnknown(m)
}

var xxx_messageInfo_MiniProgram_WX proto.InternalMessageInfo

func (m *MiniProgram_WX) GetTemplatedId() string {
	if m != nil {
		return m.TemplatedId
	}
	return ""
}

func (m *MiniProgram_WX) GetPage() string {
	if m != nil {
		return m.Page
	}
	return ""
}

func (m *MiniProgram_WX) GetMiniprogramState() string {
	if m != nil {
		return m.MiniprogramState
	}
	return ""
}

func (m *MiniProgram_WX) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *MiniProgram_WX) GetData() map[string]string {
	if m != nil {
		return m.Data
	}
	return nil
}

type PushChannel struct {
	Ios                  *IosChannel     `protobuf:"bytes,1,opt,name=ios,proto3" json:"ios,omitempty"`
	Android              *AndroidChannel `protobuf:"bytes,2,opt,name=android,proto3" json:"android,omitempty"`
	Mp                   *MiniProgram    `protobuf:"bytes,3,opt,name=mp,proto3" json:"mp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *PushChannel) Reset()         { *m = PushChannel{} }
func (m *PushChannel) String() string { return proto.CompactTextString(m) }
func (*PushChannel) ProtoMessage()    {}
func (*PushChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_getui_36d3567a420c66eb, []int{6}
}
func (m *PushChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushChannel.Unmarshal(m, b)
}
func (m *PushChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushChannel.Marshal(b, m, deterministic)
}
func (dst *PushChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushChannel.Merge(dst, src)
}
func (m *PushChannel) XXX_Size() int {
	return xxx_messageInfo_PushChannel.Size(m)
}
func (m *PushChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_PushChannel.DiscardUnknown(m)
}

var xxx_messageInfo_PushChannel proto.InternalMessageInfo

func (m *PushChannel) GetIos() *IosChannel {
	if m != nil {
		return m.Ios
	}
	return nil
}

func (m *PushChannel) GetAndroid() *AndroidChannel {
	if m != nil {
		return m.Android
	}
	return nil
}

func (m *PushChannel) GetMp() *MiniProgram {
	if m != nil {
		return m.Mp
	}
	return nil
}

func init() {
	proto.RegisterType((*Audience)(nil), "PushNotification.getui.Audience")
	proto.RegisterType((*Audience_Tag)(nil), "PushNotification.getui.Audience.Tag")
	proto.RegisterType((*Settings)(nil), "PushNotification.getui.Settings")
	proto.RegisterType((*Settings_Strategy)(nil), "PushNotification.getui.Settings.Strategy")
	proto.RegisterType((*PushMessage)(nil), "PushNotification.getui.PushMessage")
	proto.RegisterType((*PushMessage_Notification)(nil), "PushNotification.getui.PushMessage.Notification")
	proto.RegisterType((*PushMessage_Revoke)(nil), "PushNotification.getui.PushMessage.Revoke")
	proto.RegisterType((*IosChannel)(nil), "PushNotification.getui.IosChannel")
	proto.RegisterType((*IosChannel_Aps)(nil), "PushNotification.getui.IosChannel.Aps")
	proto.RegisterMapType((map[string]string)(nil), "PushNotification.getui.IosChannel.Aps.ContentStateEntry")
	proto.RegisterType((*IosChannel_Aps_Alert)(nil), "PushNotification.getui.IosChannel.Aps.Alert")
	proto.RegisterType((*IosChannel_Multimedia)(nil), "PushNotification.getui.IosChannel.Multimedia")
	proto.RegisterType((*AndroidChannel)(nil), "PushNotification.getui.AndroidChannel")
	proto.RegisterType((*AndroidChannel_Ups)(nil), "PushNotification.getui.AndroidChannel.Ups")
	proto.RegisterType((*AndroidChannel_Ups_Notification)(nil), "PushNotification.getui.AndroidChannel.Ups.Notification")
	proto.RegisterType((*AndroidChannel_Ups_Revoke)(nil), "PushNotification.getui.AndroidChannel.Ups.Revoke")
	proto.RegisterType((*AndroidChannel_Ups_Options)(nil), "PushNotification.getui.AndroidChannel.Ups.Options")
	proto.RegisterType((*AndroidChannel_Ups_Options_HW)(nil), "PushNotification.getui.AndroidChannel.Ups.Options.HW")
	proto.RegisterType((*AndroidChannel_Ups_Options_HO)(nil), "PushNotification.getui.AndroidChannel.Ups.Options.HO")
	proto.RegisterType((*AndroidChannel_Ups_Options_HO_Button)(nil), "PushNotification.getui.AndroidChannel.Ups.Options.HO.Button")
	proto.RegisterType((*AndroidChannel_Ups_Options_XM)(nil), "PushNotification.getui.AndroidChannel.Ups.Options.XM")
	proto.RegisterType((*AndroidChannel_Ups_Options_OP)(nil), "PushNotification.getui.AndroidChannel.Ups.Options.OP")
	proto.RegisterType((*AndroidChannel_Ups_Options_OP_BUTTON)(nil), "PushNotification.getui.AndroidChannel.Ups.Options.OP.BUTTON")
	proto.RegisterMapType((map[string]string)(nil), "PushNotification.getui.AndroidChannel.Ups.Options.OP.BUTTON.ActionParametersEntry")
	proto.RegisterType((*AndroidChannel_Ups_Options_OP_OPTION)(nil), "PushNotification.getui.AndroidChannel.Ups.Options.OP.OPTION")
	proto.RegisterType((*AndroidChannel_Ups_Options_OP_OPTION_FILTER)(nil), "PushNotification.getui.AndroidChannel.Ups.Options.OP.OPTION.FILTER")
	proto.RegisterType((*AndroidChannel_Ups_Options_VV)(nil), "PushNotification.getui.AndroidChannel.Ups.Options.VV")
	proto.RegisterType((*AndroidChannel_Ups_Options_UPS)(nil), "PushNotification.getui.AndroidChannel.Ups.Options.UPS")
	proto.RegisterType((*MiniProgram)(nil), "PushNotification.getui.MiniProgram")
	proto.RegisterType((*MiniProgram_WX)(nil), "PushNotification.getui.MiniProgram.WX")
	proto.RegisterMapType((map[string]string)(nil), "PushNotification.getui.MiniProgram.WX.DataEntry")
	proto.RegisterType((*PushChannel)(nil), "PushNotification.getui.PushChannel")
}

func init() {
	proto.RegisterFile("push-notification/v3/push-getui.proto", fileDescriptor_push_getui_36d3567a420c66eb)
}

var fileDescriptor_push_getui_36d3567a420c66eb = []byte{
	// 3376 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x5a, 0x4b, 0x73, 0x1b, 0xb9,
	0x76, 0x2e, 0x92, 0x22, 0x45, 0x1e, 0x52, 0xaf, 0xb6, 0x65, 0x53, 0xb4, 0xfc, 0xb8, 0xf2, 0x63,
	0x7c, 0x6d, 0x8b, 0xf4, 0xc8, 0xcf, 0x3b, 0x76, 0xe6, 0x46, 0x92, 0x7d, 0x63, 0xa5, 0x2c, 0x4b,
	0x69, 0x53, 0xb6, 0xea, 0x26, 0x35, 0x1d, 0x88, 0x0d, 0x52, 0x5d, 0xee, 0x57, 0x75, 0xa3, 0x29,
	0x71, 0xd6, 0x59, 0x4c, 0xa5, 0x92, 0xac, 0xb3, 0xca, 0x2f, 0xc8, 0x2f, 0x98, 0xec, 0x92, 0x7d,
	0x2a, 0xeb, 0x2c, 0x66, 0x95, 0x3f, 0x90, 0x65, 0x2a, 0x9b, 0x5b, 0xc0, 0x41, 0x93, 0x00, 0x1f,
	0x16, 0xa5, 0xd9, 0x48, 0xc4, 0xc1, 0x39, 0x1f, 0x80, 0xf3, 0xc2, 0x01, 0x1a, 0x70, 0x37, 0x4c,
	0xe2, 0xe3, 0x75, 0x3f, 0x60, 0x4e, 0xdb, 0x69, 0x11, 0xe6, 0x04, 0x7e, 0xa3, 0xfb, 0xa4, 0x21,
	0x88, 0x1d, 0xca, 0x12, 0xa7, 0x1e, 0x46, 0x01, 0x0b, 0x8c, 0x2b, 0xfb, 0x49, 0x7c, 0xfc, 0x41,
	0xe1, 0xaa, 0x8b, 0xde, 0xb5, 0x5f, 0x32, 0x50, 0xdc, 0x4c, 0x6c, 0x87, 0xfa, 0x2d, 0x6a, 0x2c,
	0x42, 0xae, 0xe5, 0xd8, 0xd5, 0xcc, 0xad, 0xdc, 0xfd, 0x92, 0xc9, 0x7f, 0x1a, 0x97, 0x21, 0x4f,
	0x5c, 0x87, 0xc4, 0xd5, 0xac, 0xa0, 0x61, 0xc3, 0x78, 0x0e, 0x39, 0x46, 0x3a, 0xd5, 0xdc, 0xad,
	0xdc, 0xfd, 0xf2, 0xc6, 0x9d, 0xfa, 0x78, 0xe8, 0x7a, 0x0a, 0x5b, 0x6f, 0x92, 0x8e, 0xc9, 0x05,
	0x8c, 0x7b, 0xb0, 0xd0, 0x26, 0x31, 0xb3, 0x5a, 0x49, 0xcc, 0x02, 0xcf, 0xe2, 0x18, 0x33, 0xb7,
	0x32, 0xf7, 0x4b, 0xe6, 0x1c, 0x27, 0x6f, 0x0b, 0x6a, 0x93, 0x74, 0x6a, 0x7f, 0x09, 0xb9, 0x26,
	0xe9, 0xf0, 0xe9, 0x7c, 0xa1, 0xbd, 0x6a, 0x46, 0xb0, 0xf0, 0x9f, 0xc6, 0x15, 0x28, 0x74, 0x89,
	0x9b, 0xd0, 0x74, 0x3e, 0xb2, 0x65, 0xac, 0x40, 0x31, 0x08, 0x99, 0xc5, 0x7a, 0x21, 0xad, 0xe6,
	0x04, 0xfb, 0x6c, 0x10, 0xb2, 0x66, 0x2f, 0xa4, 0x6b, 0xff, 0x9b, 0x83, 0xe2, 0x47, 0xca, 0x98,
	0xe3, 0x77, 0x62, 0x8e, 0xc8, 0x98, 0x2b, 0x10, 0x73, 0x26, 0xff, 0x69, 0xbc, 0x85, 0x62, 0xcc,
	0x22, 0xc2, 0x68, 0xa7, 0x57, 0xcd, 0xde, 0xca, 0xdc, 0x2f, 0x6f, 0xfc, 0x76, 0xd2, 0x7a, 0x52,
	0x94, 0xfa, 0x47, 0x29, 0x60, 0xf6, 0x45, 0xb9, 0x9e, 0xe2, 0x90, 0x52, 0x5b, 0x8c, 0x9e, 0x37,
	0xb1, 0x61, 0xdc, 0x86, 0xb9, 0xb8, 0x75, 0x4c, 0xed, 0xc4, 0xa5, 0x16, 0x73, 0x3c, 0x2a, 0x56,
	0x9b, 0x33, 0x2b, 0x29, 0xb1, 0xe9, 0x78, 0xd4, 0xf8, 0x06, 0x16, 0xa4, 0x3e, 0x5a, 0xc4, 0x75,
	0x8f, 0x48, 0xeb, 0x4b, 0x35, 0x2f, 0x96, 0x30, 0x8f, 0xe4, 0x6d, 0x49, 0x35, 0x1e, 0xc0, 0x52,
	0xdb, 0x71, 0x19, 0x8d, 0x2c, 0x61, 0xed, 0x9e, 0x15, 0xb4, 0xdb, 0xd5, 0xc2, 0xad, 0xcc, 0xfd,
	0xa2, 0xb9, 0x80, 0x1d, 0x62, 0xd2, 0xbd, 0xbd, 0x76, 0xdb, 0xb8, 0x09, 0x65, 0xd2, 0x62, 0x4e,
	0x97, 0x5a, 0x36, 0xe9, 0xc5, 0xd5, 0x59, 0x31, 0x2b, 0x40, 0xd2, 0x1b, 0xd2, 0x8b, 0x6b, 0xff,
	0x9d, 0x81, 0x62, 0xba, 0x0e, 0xa3, 0x0a, 0xb3, 0x36, 0x6d, 0x93, 0xc4, 0x65, 0x42, 0x35, 0x79,
	0x33, 0x6d, 0x72, 0x85, 0x39, 0x41, 0x2c, 0x34, 0x93, 0x37, 0xf9, 0x4f, 0x63, 0x1e, 0xb2, 0xc7,
	0x27, 0x72, 0x99, 0xd9, 0xe3, 0x13, 0xd1, 0x0e, 0xc4, 0xc2, 0x78, 0x3b, 0xe0, 0xed, 0x53, 0x4f,
	0xac, 0x20, 0x6f, 0x66, 0x4f, 0x3d, 0x8e, 0x70, 0xea, 0x75, 0xc4, 0x3c, 0xf3, 0x26, 0xff, 0xc9,
	0x39, 0xba, 0x5d, 0x39, 0xa5, 0x6c, 0xb7, 0xcb, 0xdb, 0x41, 0x58, 0x2d, 0x62, 0x3b, 0x08, 0xb9,
	0x44, 0x10, 0x76, 0xaa, 0x25, 0x94, 0x08, 0x42, 0x21, 0xe1, 0xfd, 0x58, 0x05, 0xe4, 0xf0, 0x7e,
	0xe4, 0xed, 0x98, 0x55, 0xcb, 0xd8, 0x8e, 0x19, 0x6f, 0x9f, 0x9c, 0x56, 0x2b, 0xd8, 0x3e, 0x39,
	0x5d, 0xfb, 0xa7, 0x02, 0x94, 0xb9, 0x11, 0x77, 0x69, 0x1c, 0x93, 0x0e, 0x35, 0x6a, 0x50, 0xb4,
	0x93, 0x48, 0xd8, 0x52, 0x7a, 0x53, 0xbf, 0x6d, 0x34, 0xa1, 0xa2, 0x06, 0x8f, 0x74, 0x82, 0xc7,
	0x93, 0x9c, 0x40, 0x81, 0xad, 0xab, 0xdd, 0xa6, 0x86, 0x62, 0xac, 0x41, 0x85, 0x45, 0xc4, 0x8f,
	0x3d, 0x27, 0x8e, 0x39, 0x2a, 0x3a, 0xa5, 0x46, 0x33, 0xb6, 0xa0, 0x10, 0xd1, 0x6e, 0xf0, 0x05,
	0xdd, 0xa2, 0xbc, 0xf1, 0x60, 0x9a, 0x31, 0x4d, 0x21, 0x61, 0x4a, 0xc9, 0xda, 0xff, 0xe4, 0xa0,
	0xa2, 0x4a, 0x70, 0x47, 0x64, 0x0e, 0x73, 0xa9, 0x5c, 0x27, 0x36, 0x0c, 0x03, 0x66, 0x8e, 0x02,
	0x1b, 0x3d, 0xbc, 0x64, 0x8a, 0xdf, 0x3c, 0x66, 0x8e, 0x9c, 0x8e, 0xc5, 0xe8, 0x29, 0x4b, 0x63,
	0xe6, 0xc8, 0xe9, 0x34, 0xe9, 0x29, 0x33, 0xae, 0x41, 0x89, 0x77, 0x39, 0x1e, 0xe9, 0x50, 0x19,
	0xa1, 0x9c, 0x77, 0x87, 0xb7, 0x39, 0x96, 0x1b, 0x74, 0x02, 0xe9, 0xa4, 0xe2, 0x37, 0xc7, 0xe2,
	0xff, 0xad, 0x24, 0x72, 0x85, 0xa5, 0x4b, 0xe6, 0x2c, 0x6f, 0x1f, 0x44, 0xae, 0x71, 0x1d, 0xa0,
	0x75, 0x4c, 0x7c, 0x9f, 0xba, 0x96, 0x63, 0x0b, 0xab, 0x97, 0xcc, 0x92, 0xa4, 0xec, 0xd8, 0xc6,
	0x6f, 0xa0, 0x92, 0x76, 0xfb, 0xc4, 0xa3, 0xc2, 0x0d, 0x4a, 0x66, 0x59, 0xd2, 0x3e, 0x10, 0x8f,
	0xf2, 0x28, 0x4a, 0x59, 0x5c, 0xda, 0xa5, 0xae, 0xf0, 0x8c, 0x92, 0x99, 0xca, 0xbd, 0xe7, 0x34,
	0x31, 0x8c, 0xeb, 0xb4, 0xbe, 0x60, 0x0e, 0x00, 0x39, 0x0c, 0xa7, 0xf0, 0x2c, 0xc0, 0x13, 0x87,
	0xe3, 0x33, 0xea, 0xa3, 0xd7, 0x94, 0x4c, 0xd9, 0xe2, 0xbe, 0xc6, 0xe7, 0x5c, 0xc1, 0x14, 0x93,
	0x44, 0x2e, 0x8f, 0x85, 0x90, 0xf4, 0xdc, 0x80, 0xd8, 0xd5, 0x39, 0x5c, 0x89, 0x6c, 0x72, 0xad,
	0xc8, 0xc0, 0x73, 0xec, 0xea, 0xbc, 0x88, 0xe4, 0x22, 0x12, 0x76, 0x44, 0x67, 0xe4, 0xf8, 0x1d,
	0x5c, 0xc4, 0x02, 0xaa, 0x8c, 0x13, 0xc4, 0x0a, 0xd6, 0x60, 0xee, 0x88, 0xd8, 0x1d, 0x6a, 0x11,
	0xdb, 0xb6, 0xfc, 0xc4, 0xab, 0x2e, 0x0a, 0x57, 0x2d, 0x0b, 0xe2, 0xa6, 0x6d, 0x7f, 0x48, 0x3c,
	0x0e, 0xc0, 0x8e, 0x23, 0x4a, 0x6c, 0x8e, 0xbe, 0x84, 0x00, 0x48, 0xd8, 0xb1, 0x6b, 0xdf, 0x43,
	0x01, 0x0d, 0x6f, 0xdc, 0x80, 0x72, 0xe0, 0xda, 0x16, 0x23, 0xf1, 0x17, 0x4b, 0xa4, 0x6a, 0xb1,
	0xd0, 0xc0, 0xb5, 0x9b, 0x24, 0xfe, 0xb2, 0x23, 0x12, 0x76, 0x3b, 0x88, 0x5a, 0x54, 0x98, 0xba,
	0x68, 0x62, 0x63, 0xed, 0xef, 0x4a, 0x00, 0x3b, 0x41, 0xbc, 0x8d, 0x1a, 0xe3, 0x26, 0x14, 0x6a,
	0x42, 0x69, 0xf1, 0xdb, 0x78, 0x09, 0x39, 0x12, 0xc6, 0xd2, 0xfd, 0xef, 0x4d, 0x72, 0xc5, 0x01,
	0x48, 0x7d, 0x33, 0x8c, 0x4d, 0x2e, 0xc2, 0x55, 0x4f, 0x12, 0x16, 0x58, 0x62, 0x35, 0xd2, 0x95,
	0x4a, 0x9c, 0xb2, 0xc5, 0x09, 0xaa, 0x42, 0x67, 0x74, 0x85, 0xee, 0x02, 0x78, 0x89, 0xcb, 0x13,
	0xa3, 0xed, 0x90, 0x6a, 0x5e, 0xec, 0x26, 0xeb, 0x53, 0x8c, 0xbc, 0xdb, 0x17, 0x32, 0x15, 0x00,
	0xe3, 0x29, 0x2c, 0x92, 0xd0, 0x8f, 0xad, 0x56, 0xe0, 0xba, 0x24, 0x8c, 0x29, 0xd7, 0x0f, 0x3a,
	0x63, 0x8d, 0xd3, 0xd7, 0x53, 0xfa, 0xba, 0x63, 0x3f, 0x0a, 0x3c, 0x87, 0x51, 0x2f, 0x64, 0xbd,
	0xda, 0x3f, 0x17, 0x20, 0xb7, 0x19, 0xc6, 0xc6, 0x16, 0xdf, 0xe9, 0x68, 0x84, 0x19, 0xb0, 0xbc,
	0xf1, 0x68, 0x3a, 0x0d, 0xd4, 0x37, 0xb9, 0x8c, 0x89, 0xa2, 0xc6, 0x23, 0x58, 0x6a, 0x05, 0xc2,
	0xb1, 0x2c, 0xd2, 0x25, 0x8e, 0x4b, 0x8e, 0x5c, 0x2a, 0x73, 0x67, 0xda, 0xb1, 0xde, 0xef, 0x10,
	0x7b, 0x46, 0x90, 0xf8, 0xb6, 0x54, 0x19, 0x36, 0x78, 0xae, 0x6a, 0xf1, 0xac, 0x1c, 0x44, 0xbd,
	0x34, 0xf4, 0xd2, 0xb6, 0xb1, 0xaa, 0xfa, 0x08, 0xc6, 0x9f, 0x24, 0xac, 0x3b, 0xb6, 0xe8, 0x75,
	0x3c, 0x1a, 0x33, 0xe2, 0x85, 0x72, 0xe1, 0x03, 0x02, 0x1f, 0x8d, 0x76, 0x79, 0x00, 0x60, 0x08,
	0x62, 0xc3, 0xf8, 0x01, 0xe6, 0xd2, 0x19, 0xc7, 0x8c, 0x30, 0x1e, 0x7f, 0xdc, 0x0a, 0x2f, 0xa7,
	0x5c, 0xfd, 0x36, 0xca, 0x7e, 0xe4, 0xa2, 0x6f, 0x7d, 0x16, 0xf5, 0xcc, 0x14, 0x6e, 0x5d, 0xc0,
	0xd5, 0xfe, 0x2f, 0x0b, 0x79, 0xa1, 0xa2, 0x73, 0x24, 0xa6, 0x7b, 0x30, 0xcf, 0x37, 0xaa, 0xc0,
	0xb7, 0xdc, 0xa0, 0x65, 0xf1, 0x0a, 0x00, 0x15, 0x24, 0xa9, 0xeb, 0x6e, 0xd0, 0x5a, 0xe7, 0xc5,
	0x40, 0x15, 0x66, 0x53, 0x86, 0x99, 0x34, 0xe7, 0x60, 0x4f, 0x8d, 0xa7, 0xa3, 0x96, 0x45, 0xa2,
	0x4e, 0x2c, 0xdc, 0xaa, 0x64, 0xf2, 0xf6, 0x3a, 0x6f, 0xf3, 0xcc, 0xec, 0x92, 0xc4, 0x6f, 0x1d,
	0xcb, 0xf4, 0x86, 0x8a, 0x92, 0xb4, 0x75, 0x41, 0x33, 0xee, 0xc0, 0x9c, 0x98, 0x5e, 0x7f, 0x02,
	0xa8, 0x33, 0x24, 0xf6, 0xc7, 0xbf, 0x07, 0xf3, 0x03, 0x2e, 0x31, 0x56, 0x51, 0x8c, 0x35, 0x3f,
	0x60, 0x13, 0x23, 0xd6, 0xa0, 0x18, 0x27, 0x47, 0xb8, 0x78, 0x4c, 0x5d, 0xfd, 0xb6, 0xf1, 0x10,
	0x96, 0xe2, 0xe4, 0xc8, 0xd2, 0x47, 0xc3, 0xec, 0xb5, 0x98, 0x32, 0xf5, 0x07, 0x5c, 0x07, 0x43,
	0x67, 0x16, 0x83, 0x96, 0xc5, 0xa0, 0x4b, 0x1a, 0x37, 0xef, 0xa8, 0xfd, 0x1e, 0x96, 0x46, 0xec,
	0x33, 0xa6, 0xa6, 0xba, 0x0c, 0x79, 0x51, 0x45, 0x49, 0x1b, 0x60, 0xe3, 0xbb, 0xec, 0xcb, 0x4c,
	0x6d, 0x0f, 0x60, 0x10, 0x6a, 0x69, 0xaa, 0xcc, 0x0c, 0x52, 0x65, 0x9a, 0x46, 0xd0, 0xc3, 0x31,
	0x8d, 0x5c, 0x83, 0x52, 0xe0, 0xbb, 0x3d, 0xeb, 0xc4, 0x69, 0x3b, 0xc2, 0x6e, 0x45, 0xb3, 0xc8,
	0x09, 0x9f, 0x9d, 0xb6, 0xb3, 0xf6, 0x0f, 0xaf, 0x61, 0x7e, 0xd3, 0xb7, 0xa3, 0xc0, 0xb1, 0xd3,
	0x54, 0xf4, 0x1a, 0x72, 0x49, 0x18, 0xcb, 0xa0, 0x9b, 0xb8, 0x03, 0xea, 0x42, 0xf5, 0x03, 0x9e,
	0x7a, 0x92, 0x30, 0xae, 0xfd, 0xdb, 0x2b, 0xc8, 0x1d, 0x84, 0xb1, 0xf1, 0xd7, 0x43, 0x9b, 0x38,
	0xc2, 0xbd, 0x98, 0x1e, 0xee, 0x3c, 0x7b, 0x79, 0x76, 0xcc, 0x5e, 0xbe, 0xd3, 0xdf, 0xcb, 0x73,
	0x62, 0xe8, 0x6f, 0xcf, 0x31, 0xb4, 0xbe, 0xa5, 0x1b, 0xef, 0x81, 0xd7, 0xae, 0x4e, 0xe0, 0xc7,
	0xb2, 0x2e, 0xd8, 0x38, 0x07, 0xd6, 0x1e, 0x4a, 0x9a, 0x29, 0x44, 0xed, 0x5f, 0x32, 0x17, 0x2e,
	0x10, 0xf4, 0x2d, 0x35, 0x37, 0x79, 0x4b, 0x9d, 0x19, 0xb7, 0xa5, 0xe6, 0x07, 0x7e, 0xa2, 0x6d,
	0x9c, 0x05, 0x7d, 0xe3, 0xac, 0xdd, 0x9f, 0x76, 0x6b, 0xab, 0xfd, 0xe7, 0x0b, 0x98, 0x95, 0xeb,
	0x33, 0xf6, 0x44, 0x15, 0x8a, 0x66, 0x7e, 0x76, 0x7e, 0xfd, 0xd4, 0xdf, 0x7d, 0x36, 0x2b, 0xef,
	0x3e, 0x0f, 0xb6, 0x01, 0x0e, 0x78, 0xea, 0xc9, 0xdd, 0xef, 0x22, 0x80, 0x87, 0xbb, 0x66, 0xe5,
	0x70, 0x57, 0x07, 0x0c, 0x42, 0xe9, 0x0d, 0x17, 0x01, 0xdc, 0xdb, 0x37, 0x2b, 0x7b, 0xfb, 0x3a,
	0x60, 0xb7, 0x2b, 0x5d, 0xe2, 0x22, 0x80, 0x9f, 0x3e, 0x99, 0x95, 0x4f, 0x9f, 0x74, 0xc0, 0x63,
	0x2c, 0xe3, 0x2e, 0xa8, 0xc3, 0x3d, 0xb3, 0xf2, 0x6e, 0x4f, 0x01, 0xfc, 0xab, 0x41, 0xa9, 0x7f,
	0x61, 0x25, 0xce, 0x1d, 0xee, 0xfe, 0x85, 0x0e, 0xc9, 0xcf, 0x02, 0xb3, 0xbf, 0x46, 0x8d, 0x73,
	0x7b, 0xfb, 0x2a, 0xa4, 0x89, 0x19, 0xe7, 0xb2, 0x80, 0x7c, 0x7e, 0x01, 0xc8, 0x83, 0xfd, 0x8f,
	0xe6, 0xdc, 0xc1, 0xfe, 0x47, 0xa5, 0x88, 0xf8, 0xfb, 0x59, 0xc8, 0xbe, 0xfb, 0x6c, 0xbc, 0x03,
	0x2c, 0xe9, 0xac, 0x96, 0x4b, 0xe2, 0x58, 0x7a, 0xf0, 0x93, 0x86, 0x87, 0xb5, 0x7b, 0x83, 0x20,
	0x66, 0x43, 0x3b, 0xac, 0x0b, 0x81, 0x86, 0x10, 0x50, 0x26, 0xf9, 0x7e, 0xb8, 0x62, 0xc4, 0x1c,
	0xfb, 0x6c, 0x1a, 0x2c, 0x29, 0x32, 0x0e, 0x2d, 0xa6, 0x4c, 0xa0, 0xe5, 0xa6, 0x47, 0x93, 0x22,
	0x0a, 0xda, 0x6b, 0xc8, 0xab, 0x27, 0x83, 0xf5, 0xaf, 0xa3, 0x08, 0x56, 0x5d, 0x3a, 0x66, 0x3d,
	0x97, 0xca, 0x23, 0xe2, 0x19, 0xd2, 0x82, 0x55, 0x91, 0x7e, 0x83, 0x27, 0x13, 0xcc, 0x60, 0xb8,
	0x75, 0x7f, 0x7b, 0xc6, 0x2a, 0x52, 0x76, 0x05, 0x65, 0x0b, 0x8f, 0x3e, 0x22, 0xe3, 0xe1, 0xd6,
	0xfe, 0xf8, 0x6c, 0x10, 0xce, 0xad, 0x60, 0xfc, 0x01, 0xc0, 0xf1, 0xc2, 0x20, 0x62, 0xc4, 0x6f,
	0xa5, 0xc7, 0x96, 0x8d, 0xb3, 0x54, 0x91, 0xf2, 0xeb, 0xb6, 0x91, 0x87, 0x6d, 0x0b, 0xab, 0xc1,
	0x92, 0xd8, 0x34, 0xcf, 0xb0, 0x8d, 0x26, 0xa2, 0xcf, 0x4a, 0x39, 0x6d, 0xc1, 0x34, 0xb3, 0x1a,
	0xf0, 0x0f, 0x59, 0x49, 0xcc, 0xa6, 0x3c, 0x8d, 0x8d, 0x87, 0x67, 0xf1, 0x4c, 0xa9, 0x61, 0xf1,
	0x68, 0x75, 0x7b, 0x04, 0x20, 0x65, 0x50, 0xc4, 0xde, 0xc2, 0x22, 0x23, 0x51, 0x87, 0x32, 0x2b,
	0x89, 0x69, 0x84, 0xdb, 0xce, 0x9c, 0xf0, 0x92, 0x87, 0x23, 0xe2, 0xc3, 0x8c, 0x4a, 0x30, 0xfe,
	0x2b, 0x0f, 0xc6, 0x3d, 0xe3, 0xed, 0xb8, 0x60, 0x7c, 0xfc, 0x35, 0x57, 0x17, 0x7f, 0xb7, 0x87,
	0x22, 0x71, 0x7b, 0x7c, 0x24, 0x3e, 0x3a, 0x23, 0x02, 0x3f, 0x68, 0x21, 0xb3, 0x3d, 0x3e, 0x00,
	0x1f, 0x9d, 0x11, 0x78, 0x3a, 0xc8, 0x06, 0xcc, 0x38, 0xad, 0xc0, 0x97, 0x61, 0x77, 0x67, 0x82,
	0x8f, 0xb5, 0x02, 0x5f, 0x91, 0x79, 0x9a, 0xc6, 0x2a, 0x6e, 0xc7, 0x77, 0xa7, 0x8b, 0xd1, 0xa7,
	0x69, 0x8c, 0xe2, 0xad, 0xcd, 0xdd, 0xe9, 0x62, 0xf3, 0x95, 0x1a, 0x9b, 0x18, 0x56, 0xf7, 0x27,
	0x86, 0x53, 0x73, 0x28, 0x24, 0x7f, 0xa7, 0x84, 0x24, 0x06, 0xd3, 0x37, 0x13, 0x65, 0xb7, 0x86,
	0x23, 0x71, 0x01, 0x39, 0x7a, 0x56, 0x9c, 0x78, 0x1e, 0x89, 0x7a, 0xb2, 0xcc, 0x5e, 0x1f, 0x8f,
	0x80, 0xa5, 0xc7, 0x47, 0x64, 0x55, 0x70, 0xbe, 0xd7, 0x22, 0x1a, 0x63, 0xe7, 0xc1, 0x39, 0x22,
	0xb9, 0x0b, 0xb3, 0x47, 0x09, 0x63, 0xbc, 0x70, 0x2b, 0x8b, 0x53, 0xd4, 0xeb, 0x0b, 0x6d, 0xaa,
	0xf5, 0x2d, 0x01, 0x32, 0x71, 0xfd, 0x38, 0x84, 0xee, 0x17, 0x27, 0xc7, 0xd4, 0x97, 0x91, 0x36,
	0xc1, 0x2f, 0x38, 0x87, 0x12, 0x23, 0x3f, 0x65, 0xa0, 0x80, 0xe3, 0xf0, 0xd2, 0x4f, 0x5c, 0x5a,
	0xc8, 0xcb, 0x00, 0xfe, 0xdb, 0xb8, 0x01, 0x80, 0x87, 0xad, 0xe6, 0xa0, 0xbe, 0x57, 0x28, 0xbc,
	0x1f, 0xab, 0xbd, 0x66, 0x5a, 0x1a, 0xe6, 0x4d, 0x85, 0x32, 0xb1, 0x36, 0x34, 0x60, 0xc6, 0x26,
	0x8c, 0xa4, 0x77, 0x47, 0xfc, 0x77, 0xed, 0xe7, 0x25, 0xc8, 0x1e, 0xee, 0x1a, 0x0d, 0x2d, 0x73,
	0xe1, 0x64, 0xae, 0x35, 0xe8, 0x29, 0x8b, 0x48, 0x7d, 0x6c, 0x8a, 0xda, 0x81, 0xab, 0xea, 0x0a,
	0x2d, 0xe1, 0x8e, 0x56, 0xff, 0x40, 0xc2, 0x9d, 0x0f, 0xa5, 0x27, 0x70, 0x7d, 0x05, 0xea, 0xc8,
	0xe9, 0xec, 0x3b, 0x2d, 0x2b, 0x89, 0x1c, 0x59, 0xf6, 0x8e, 0x85, 0x1a, 0x70, 0x69, 0x05, 0xcb,
	0x35, 0x8d, 0xc9, 0xe5, 0x79, 0xca, 0xe2, 0x51, 0x29, 0xe0, 0x50, 0x1d, 0x8f, 0xc6, 0xc1, 0xe9,
	0x9c, 0x0a, 0xe4, 0x3a, 0x94, 0x44, 0x82, 0x15, 0x00, 0xf2, 0x42, 0x43, 0x02, 0xf4, 0xe9, 0x0a,
	0xfb, 0x37, 0x50, 0x70, 0x83, 0x16, 0xe9, 0xc7, 0xe0, 0x55, 0xc9, 0x8b, 0x44, 0x2d, 0x4b, 0xcf,
	0x21, 0xcd, 0xf2, 0x03, 0x66, 0x39, 0xbe, 0x8c, 0xbb, 0x1b, 0x1a, 0xbf, 0xec, 0x53, 0xc4, 0xee,
	0x42, 0xde, 0x0b, 0xec, 0xfe, 0x35, 0xdc, 0x15, 0xc9, 0x2e, 0x68, 0x0a, 0xdb, 0x13, 0xa8, 0x08,
	0x52, 0x0a, 0x8e, 0xf1, 0x74, 0x5d, 0xe5, 0x1e, 0xc5, 0xfe, 0x16, 0xca, 0x24, 0x0c, 0xad, 0x2e,
	0x8d, 0xc4, 0x49, 0x0b, 0x37, 0x9f, 0x55, 0x29, 0xa3, 0xf4, 0x28, 0x22, 0xbf, 0x07, 0x43, 0xe9,
	0x48, 0x47, 0x4b, 0x77, 0x9d, 0x11, 0xc9, 0xd1, 0x31, 0xdb, 0x70, 0x13, 0x9d, 0x02, 0x23, 0xcb,
	0x72, 0x69, 0x9b, 0xa5, 0x37, 0xec, 0xb4, 0xdd, 0xa6, 0x2d, 0x26, 0x2f, 0x02, 0xff, 0x6c, 0xb2,
	0x3f, 0x4d, 0x14, 0x55, 0xc6, 0x69, 0xc2, 0x95, 0x31, 0xcc, 0x3c, 0xf2, 0xe6, 0x05, 0xfc, 0xd3,
	0x29, 0xe1, 0x89, 0xa7, 0x1a, 0xf1, 0x6f, 0x61, 0x75, 0x94, 0x07, 0xa3, 0x4d, 0xf8, 0x0b, 0x5e,
	0x45, 0x7e, 0x37, 0x1d, 0xf6, 0x40, 0x4e, 0x19, 0xe1, 0x8f, 0xb0, 0x32, 0xca, 0x79, 0x42, 0x8f,
	0x04, 0xfc, 0xa2, 0x80, 0x7f, 0x31, 0x1d, 0xbc, 0x14, 0x52, 0xb0, 0x6d, 0xb8, 0x31, 0x71, 0x16,
	0xb8, 0x6d, 0xe3, 0x4d, 0xe8, 0xeb, 0x73, 0xcd, 0x7f, 0xb8, 0x98, 0x3e, 0x86, 0x5b, 0x1a, 0x6f,
	0xe4, 0x74, 0x8e, 0x87, 0x4d, 0x6c, 0x88, 0x71, 0xbe, 0x3f, 0x73, 0x9c, 0x31, 0xb2, 0xca, 0x48,
	0x9f, 0xe0, 0xea, 0x38, 0x6e, 0x6e, 0xe4, 0x4b, 0x62, 0x80, 0x67, 0xd3, 0x0e, 0xa0, 0x5b, 0xf9,
	0x08, 0xae, 0x8f, 0x61, 0x52, 0xcc, 0x7c, 0x59, 0xa0, 0xbf, 0x9a, 0x12, 0x7d, 0xac, 0x9d, 0xff,
	0x06, 0x6a, 0x63, 0x58, 0x53, 0x43, 0x2f, 0x8b, 0x01, 0x5e, 0x4e, 0x39, 0xc0, 0xa8, 0xa5, 0x87,
	0xa3, 0x4c, 0x9b, 0x08, 0x9a, 0xfa, 0xca, 0x94, 0x51, 0x36, 0x2a, 0xfa, 0x15, 0x8f, 0xf2, 0x1c,
	0x7b, 0xc8, 0xd2, 0x57, 0xa7, 0xf4, 0xa8, 0x11, 0x49, 0xed, 0x0c, 0xb9, 0x3c, 0xca, 0xcb, 0xad,
	0x5c, 0x95, 0x47, 0xbe, 0xa9, 0xc0, 0x75, 0x1b, 0xff, 0x00, 0xd7, 0x46, 0x58, 0x14, 0x0b, 0xaf,
	0x08, 0xe4, 0xdf, 0x4d, 0x85, 0x3c, 0xd6, 0xbe, 0x87, 0x50, 0x1d, 0x61, 0x4c, 0xad, 0x5b, 0x13,
	0xe0, 0xcf, 0xa7, 0x02, 0x1f, 0xb5, 0xed, 0xb0, 0x77, 0x2a, 0x53, 0x40, 0xcb, 0x5e, 0x9b, 0xd2,
	0x3b, 0x87, 0x05, 0xb5, 0x4d, 0xb0, 0xc2, 0x1c, 0x8f, 0x5a, 0x2c, 0xb0, 0x5c, 0xa7, 0x4b, 0xab,
	0xab, 0xe2, 0x1a, 0xe9, 0x6a, 0x43, 0x25, 0x2a, 0xec, 0x2f, 0x60, 0x5e, 0x5c, 0x43, 0xc6, 0xd4,
	0xb7, 0xad, 0x80, 0xd7, 0x73, 0xd7, 0xc5, 0x1c, 0x6e, 0xca, 0x39, 0xe8, 0x9d, 0xe3, 0xc7, 0xe1,
	0xdd, 0xd5, 0x1b, 0x43, 0xe3, 0x70, 0xa2, 0x52, 0x47, 0xfd, 0xd7, 0x02, 0x64, 0xf7, 0xf6, 0x8d,
	0xdf, 0x8e, 0x29, 0x5e, 0x96, 0xc7, 0x9f, 0xac, 0x6e, 0xa6, 0xb5, 0x35, 0x56, 0x55, 0x8b, 0x23,
	0x65, 0xf4, 0x33, 0x58, 0x8c, 0x3d, 0xe2, 0xba, 0x56, 0xe8, 0xb4, 0x58, 0x12, 0x89, 0xcf, 0x18,
	0x39, 0x59, 0x0e, 0x0d, 0x77, 0x68, 0x5b, 0xe7, 0x3c, 0x2f, 0xa0, 0x15, 0x21, 0xac, 0x35, 0x56,
	0x1a, 0x3a, 0x59, 0x17, 0xe1, 0x3b, 0xa3, 0x3c, 0x5a, 0x0d, 0xbe, 0x29, 0xac, 0x34, 0x74, 0xb2,
	0x2e, 0x12, 0x1f, 0x07, 0x27, 0x16, 0xaa, 0x83, 0xd7, 0x5a, 0x78, 0x44, 0x58, 0x69, 0xe8, 0x64,
	0xad, 0x10, 0x58, 0x10, 0x7d, 0x31, 0x23, 0x11, 0xc3, 0xcf, 0xe0, 0xb3, 0x42, 0xa9, 0xb5, 0xc6,
	0x10, 0x5d, 0x11, 0x6a, 0xc0, 0x9c, 0xe8, 0xe4, 0x16, 0x12, 0x22, 0x45, 0x21, 0x52, 0x6d, 0x68,
	0x54, 0xad, 0x2a, 0x29, 0x06, 0xed, 0xb6, 0xe5, 0x3a, 0x3e, 0x95, 0x27, 0xe8, 0x4b, 0x8d, 0x94,
	0xa0, 0x9b, 0x37, 0xa5, 0x5a, 0x8c, 0xb9, 0xf2, 0x9b, 0xf2, 0xd5, 0x86, 0x4a, 0xd4, 0x51, 0x71,
	0x5d, 0xcc, 0x95, 0x9f, 0x9b, 0x2f, 0x35, 0x52, 0x82, 0xe6, 0x6d, 0x4b, 0xba, 0x65, 0x06, 0xdf,
	0x14, 0x57, 0x1b, 0x23, 0x3d, 0xda, 0xa9, 0xc7, 0xf0, 0xa8, 0xed, 0x24, 0x9e, 0x26, 0x39, 0x27,
	0x4b, 0xa5, 0xd1, 0x2e, 0x5d, 0xad, 0xaa, 0x61, 0xb9, 0xdc, 0xbc, 0xac, 0x0d, 0x87, 0xe8, 0x8a,
	0x10, 0x19, 0x1c, 0x51, 0x16, 0x2e, 0x7c, 0x44, 0xd9, 0xdb, 0xaf, 0x6f, 0x1d, 0x34, 0x9b, 0x7b,
	0x1f, 0x4c, 0x63, 0xcc, 0x69, 0xc4, 0x82, 0x02, 0xde, 0x3d, 0x8b, 0xda, 0xe0, 0xc2, 0x23, 0xec,
	0xed, 0x37, 0x77, 0xf6, 0x3e, 0x98, 0x4b, 0x0d, 0x04, 0x53, 0x3f, 0xd8, 0xe5, 0xa0, 0x80, 0xe3,
	0x1b, 0xab, 0xda, 0xd1, 0x65, 0x7e, 0x28, 0xa1, 0x6e, 0xc0, 0x82, 0xb8, 0xad, 0xde, 0x1c, 0x3e,
	0xc9, 0xac, 0x0c, 0x91, 0x15, 0x99, 0xc7, 0x30, 0xaf, 0x74, 0x1e, 0x44, 0xae, 0x0c, 0xbd, 0xaa,
	0x4e, 0x55, 0x24, 0xbe, 0x83, 0x4b, 0x4a, 0x1f, 0xff, 0xdb, 0x75, 0x58, 0xfa, 0x45, 0xea, 0xfa,
	0x98, 0x2e, 0x45, 0xf6, 0xa7, 0x0c, 0x2c, 0xe2, 0xa9, 0x6a, 0x9f, 0x44, 0xc4, 0xa3, 0x8c, 0x46,
	0xb1, 0xfc, 0x0e, 0x6a, 0xfe, 0x1a, 0xc3, 0xd4, 0x37, 0x87, 0x40, 0xf1, 0xdb, 0x5c, 0x6d, 0x78,
	0x2c, 0x45, 0xab, 0xdb, 0xb0, 0x3c, 0x56, 0xe8, 0x5c, 0x1f, 0x8c, 0xfe, 0x3d, 0x0f, 0x05, 0x34,
	0x9c, 0xf1, 0x12, 0x0c, 0x2d, 0xcf, 0x77, 0xa2, 0x20, 0x09, 0x25, 0xca, 0xea, 0x68, 0x8f, 0xa2,
	0x94, 0x1b, 0x90, 0xf7, 0x84, 0x3b, 0x23, 0xfc, 0x82, 0x37, 0xe4, 0xc3, 0x0f, 0xa0, 0x1c, 0x53,
	0xdf, 0x09, 0x22, 0x2b, 0x4c, 0xe2, 0x63, 0xf9, 0x8d, 0x69, 0x59, 0x21, 0x29, 0xbc, 0xb7, 0xa0,
	0x90, 0xf8, 0xed, 0xc0, 0xc5, 0x64, 0x58, 0x34, 0x17, 0xb1, 0xa5, 0x5d, 0x1e, 0xac, 0xb0, 0x20,
	0xb4, 0xf4, 0x83, 0x1d, 0x89, 0x2c, 0x1e, 0xe5, 0x22, 0x1d, 0x16, 0xcd, 0xdb, 0x13, 0x19, 0xf4,
	0x59, 0x79, 0x84, 0xd1, 0xc8, 0x21, 0xee, 0xe0, 0xbb, 0xf3, 0xb2, 0x42, 0x52, 0x78, 0xef, 0x40,
	0xd1, 0x25, 0x47, 0x83, 0x07, 0x11, 0x79, 0xd3, 0x48, 0xdb, 0x0a, 0x17, 0x85, 0x02, 0xbe, 0xea,
	0x11, 0xb9, 0xaf, 0xbc, 0xb1, 0xfd, 0x6b, 0x02, 0xa9, 0xfe, 0x87, 0x9d, 0xf7, 0xcd, 0xb7, 0xa6,
	0xb9, 0x88, 0x90, 0xca, 0x30, 0x75, 0x98, 0x23, 0x76, 0x97, 0x46, 0xcc, 0x89, 0x69, 0xc4, 0x67,
	0x84, 0xc7, 0xba, 0xab, 0x1a, 0x51, 0x71, 0x94, 0x5f, 0x32, 0x50, 0x40, 0x30, 0x6e, 0xe3, 0xc4,
	0x8f, 0x93, 0x30, 0x0c, 0x78, 0x02, 0x0f, 0x42, 0x54, 0x1a, 0xbe, 0x20, 0x5a, 0x1d, 0xed, 0xd1,
	0x2e, 0x98, 0x16, 0x07, 0xfd, 0xd2, 0x42, 0x18, 0x9b, 0xb5, 0x61, 0xba, 0x1e, 0xd0, 0x83, 0x5e,
	0xa1, 0x31, 0x79, 0xf5, 0xb0, 0x32, 0x44, 0xd6, 0x03, 0x7a, 0xd0, 0x19, 0x7f, 0x71, 0x7c, 0xf9,
	0x54, 0xa9, 0xaa, 0x53, 0x95, 0x05, 0xfe, 0x63, 0x16, 0xb2, 0x9f, 0x3e, 0xf1, 0x9d, 0x4e, 0xd4,
	0x20, 0xfa, 0x47, 0x45, 0xbe, 0xd3, 0xe9, 0x64, 0xdd, 0x07, 0x64, 0xc1, 0xa8, 0x7c, 0x16, 0x5d,
	0x96, 0xf7, 0x4d, 0x43, 0x89, 0xe6, 0x11, 0x54, 0x7c, 0xca, 0x4e, 0x82, 0x48, 0xf9, 0xbc, 0x96,
	0x37, 0xaf, 0x34, 0x24, 0x71, 0x88, 0xfb, 0xe1, 0x50, 0xf5, 0x33, 0x23, 0xa1, 0x39, 0xb1, 0x19,
	0xbc, 0xd7, 0x6b, 0x9f, 0xbb, 0xca, 0xed, 0x2b, 0x6e, 0xe8, 0x97, 0xc6, 0xdd, 0xb6, 0xde, 0x83,
	0x12, 0x8f, 0x16, 0x8b, 0x1f, 0xc5, 0xe5, 0x2e, 0x7e, 0x49, 0xbc, 0x1b, 0xdc, 0x0d, 0x6c, 0xf5,
	0x3a, 0xf5, 0x10, 0x72, 0x07, 0xfb, 0x1f, 0x8d, 0xdb, 0xca, 0x73, 0x21, 0x0c, 0xe3, 0x25, 0xf9,
	0x5c, 0x48, 0x5b, 0xd5, 0x5c, 0xff, 0xe1, 0x90, 0x12, 0xc3, 0xcb, 0xe9, 0xe3, 0x21, 0x2d, 0x75,
	0xae, 0xfd, 0x47, 0x16, 0xca, 0xbb, 0x8e, 0xef, 0xec, 0x47, 0x41, 0x27, 0x22, 0x9e, 0xf1, 0x5c,
	0x3c, 0xe3, 0xca, 0x7c, 0xfd, 0x05, 0x8a, 0x22, 0x50, 0xff, 0x7c, 0x68, 0x66, 0x4f, 0x4e, 0x6b,
	0xff, 0x9f, 0x81, 0xec, 0xe7, 0x43, 0xe3, 0x37, 0x50, 0xe1, 0xc0, 0x2e, 0x61, 0xd4, 0x1e, 0x94,
	0x61, 0xe5, 0x3e, 0x6d, 0xc7, 0x36, 0x0c, 0x98, 0x09, 0x49, 0x27, 0xcd, 0x5c, 0xe2, 0xb7, 0xf1,
	0x10, 0x96, 0x3c, 0xc7, 0x77, 0x42, 0xc4, 0x94, 0xcf, 0x20, 0x30, 0xeb, 0x2f, 0x2a, 0x1d, 0xe2,
	0x1b, 0xba, 0x78, 0xfc, 0x44, 0xfc, 0xf4, 0xd9, 0xa2, 0xf8, 0x6d, 0xbc, 0xe9, 0x5f, 0x6a, 0xe5,
	0xbe, 0xf6, 0x72, 0x4c, 0x9f, 0x78, 0xfd, 0x0d, 0x61, 0x04, 0xd3, 0x32, 0x5e, 0x83, 0xbd, 0x80,
	0x52, 0x9f, 0x74, 0x9e, 0xa4, 0xbb, 0xf6, 0x73, 0x06, 0x1f, 0xbb, 0xa5, 0x5f, 0xd4, 0x9f, 0xe2,
	0x93, 0x3d, 0x54, 0xe3, 0xda, 0xd9, 0x0f, 0x39, 0xf0, 0x59, 0xdf, 0x9f, 0xc3, 0xac, 0xbc, 0x37,
	0x3c, 0xeb, 0x09, 0x90, 0x9e, 0x6e, 0xcc, 0x54, 0xcc, 0x78, 0x02, 0x59, 0x2f, 0xfd, 0xe0, 0x79,
	0x7b, 0x0a, 0x25, 0x98, 0x59, 0x2f, 0xdc, 0x7a, 0xf3, 0xc7, 0xad, 0x4e, 0xc0, 0xb5, 0x58, 0x7f,
	0xb6, 0xc1, 0x58, 0xbd, 0x15, 0x78, 0x0d, 0xf1, 0x5e, 0xb5, 0x15, 0xb8, 0x8d, 0x98, 0x46, 0x5d,
	0xa7, 0x45, 0xe3, 0xc6, 0xb8, 0x17, 0xae, 0xaf, 0x84, 0xef, 0x72, 0x16, 0x1a, 0x1d, 0x15, 0x84,
	0xcc, 0x93, 0x3f, 0x05, 0x00, 0x00, 0xff, 0xff, 0x82, 0xd2, 0xb3, 0x84, 0x0c, 0x2b, 0x00, 0x00,
}
