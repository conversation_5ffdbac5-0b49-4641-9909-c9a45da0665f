// Code generated by protoc-gen-go. DO NOT EDIT.
// source: push-notification/v2/pushnotification_v2.proto

package push_notification // import "golang.52tt.com/protocol/services/push-notification/v2"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import APNs "golang.52tt.com/protocol/services/push-notification/v2/APNs"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RelationRegisterEventType int32

const (
	RelationRegisterEventType_ENUM_CHANNEL_RELATION_REGENENT_REGIST   RelationRegisterEventType = 0
	RelationRegisterEventType_ENUM_CHANNEL_RELATION_REGENENT_UNREGIST RelationRegisterEventType = 1
	RelationRegisterEventType_ENUM_GROUP_RELATION_REGENENT_REGIST     RelationRegisterEventType = 2
	RelationRegisterEventType_ENUM_GROUP_RELATION_REGENENT_UNREGIST   RelationRegisterEventType = 3
)

var RelationRegisterEventType_name = map[int32]string{
	0: "ENUM_CHANNEL_RELATION_REGENENT_REGIST",
	1: "ENUM_CHANNEL_RELATION_REGENENT_UNREGIST",
	2: "ENUM_GROUP_RELATION_REGENENT_REGIST",
	3: "ENUM_GROUP_RELATION_REGENENT_UNREGIST",
}
var RelationRegisterEventType_value = map[string]int32{
	"ENUM_CHANNEL_RELATION_REGENENT_REGIST":   0,
	"ENUM_CHANNEL_RELATION_REGENENT_UNREGIST": 1,
	"ENUM_GROUP_RELATION_REGENENT_REGIST":     2,
	"ENUM_GROUP_RELATION_REGENENT_UNREGIST":   3,
}

func (x RelationRegisterEventType) String() string {
	return proto.EnumName(RelationRegisterEventType_name, int32(x))
}
func (RelationRegisterEventType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{0}
}

type ProxyNotification_Type int32

const (
	ProxyNotification_INVALID           ProxyNotification_Type = 0
	ProxyNotification_NOTIFY            ProxyNotification_Type = 1
	ProxyNotification_PUSH              ProxyNotification_Type = 2
	ProxyNotification_TRANSMISSION_PUSH ProxyNotification_Type = 3
	ProxyNotification_KICKOUT           ProxyNotification_Type = 4
)

var ProxyNotification_Type_name = map[int32]string{
	0: "INVALID",
	1: "NOTIFY",
	2: "PUSH",
	3: "TRANSMISSION_PUSH",
	4: "KICKOUT",
}
var ProxyNotification_Type_value = map[string]int32{
	"INVALID":           0,
	"NOTIFY":            1,
	"PUSH":              2,
	"TRANSMISSION_PUSH": 3,
	"KICKOUT":           4,
}

func (x ProxyNotification_Type) String() string {
	return proto.EnumName(ProxyNotification_Type_name, int32(x))
}
func (ProxyNotification_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{0, 0}
}

type ProxyNotification_Policy int32

const (
	ProxyNotification_DEFAULT  ProxyNotification_Policy = 0
	ProxyNotification_RELIABLE ProxyNotification_Policy = 1
)

var ProxyNotification_Policy_name = map[int32]string{
	0: "DEFAULT",
	1: "RELIABLE",
}
var ProxyNotification_Policy_value = map[string]int32{
	"DEFAULT":  0,
	"RELIABLE": 1,
}

func (x ProxyNotification_Policy) String() string {
	return proto.EnumName(ProxyNotification_Policy_name, int32(x))
}
func (ProxyNotification_Policy) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{0, 1}
}

type ProxyNotification struct {
	Type                 uint32                   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Payload              []byte                   `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	Policy               ProxyNotification_Policy `protobuf:"varint,3,opt,name=policy,proto3,enum=PushNotification.ProxyNotification_Policy" json:"policy,omitempty"`
	ExpireTime           uint32                   `protobuf:"varint,4,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	Sync                 bool                     `protobuf:"varint,5,opt,name=sync,proto3" json:"sync,omitempty"`
	Priority             uint32                   `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"`
	PushType             uint32                   `protobuf:"varint,9,opt,name=push_type,json=pushType,proto3" json:"push_type,omitempty"`
	PushLabel            string                   `protobuf:"bytes,10,opt,name=push_label,json=pushLabel,proto3" json:"push_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *ProxyNotification) Reset()         { *m = ProxyNotification{} }
func (m *ProxyNotification) String() string { return proto.CompactTextString(m) }
func (*ProxyNotification) ProtoMessage()    {}
func (*ProxyNotification) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{0}
}
func (m *ProxyNotification) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProxyNotification.Unmarshal(m, b)
}
func (m *ProxyNotification) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProxyNotification.Marshal(b, m, deterministic)
}
func (dst *ProxyNotification) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProxyNotification.Merge(dst, src)
}
func (m *ProxyNotification) XXX_Size() int {
	return xxx_messageInfo_ProxyNotification.Size(m)
}
func (m *ProxyNotification) XXX_DiscardUnknown() {
	xxx_messageInfo_ProxyNotification.DiscardUnknown(m)
}

var xxx_messageInfo_ProxyNotification proto.InternalMessageInfo

func (m *ProxyNotification) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ProxyNotification) GetPayload() []byte {
	if m != nil {
		return m.Payload
	}
	return nil
}

func (m *ProxyNotification) GetPolicy() ProxyNotification_Policy {
	if m != nil {
		return m.Policy
	}
	return ProxyNotification_DEFAULT
}

func (m *ProxyNotification) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *ProxyNotification) GetSync() bool {
	if m != nil {
		return m.Sync
	}
	return false
}

func (m *ProxyNotification) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func (m *ProxyNotification) GetPushType() uint32 {
	if m != nil {
		return m.PushType
	}
	return 0
}

func (m *ProxyNotification) GetPushLabel() string {
	if m != nil {
		return m.PushLabel
	}
	return ""
}

type CompositiveNotification struct {
	Sequence             uint32              `protobuf:"varint,1,opt,name=sequence,proto3" json:"sequence,omitempty"`
	TerminalTypeList     []uint32            `protobuf:"varint,2,rep,packed,name=terminal_type_list,json=terminalTypeList,proto3" json:"terminal_type_list,omitempty"`
	AppId                uint32              `protobuf:"varint,3,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	ProxyNotification    *ProxyNotification  `protobuf:"bytes,4,opt,name=proxy_notification,json=proxyNotification,proto3" json:"proxy_notification,omitempty"`
	ApnsNotification     *APNs.Notification  `protobuf:"bytes,5,opt,name=apns_notification,json=apnsNotification,proto3" json:"apns_notification,omitempty"`
	ApnsFrequence        uint32              `protobuf:"varint,6,opt,name=apns_frequence,json=apnsFrequence,proto3" json:"apns_frequence,omitempty"`
	ClientTimestamp      uint32              `protobuf:"varint,7,opt,name=client_timestamp,json=clientTimestamp,proto3" json:"client_timestamp,omitempty"`
	TerminalTypePolicy   *TerminalTypePolicy `protobuf:"bytes,8,opt,name=terminal_type_policy,json=terminalTypePolicy,proto3" json:"terminal_type_policy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *CompositiveNotification) Reset()         { *m = CompositiveNotification{} }
func (m *CompositiveNotification) String() string { return proto.CompactTextString(m) }
func (*CompositiveNotification) ProtoMessage()    {}
func (*CompositiveNotification) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{1}
}
func (m *CompositiveNotification) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompositiveNotification.Unmarshal(m, b)
}
func (m *CompositiveNotification) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompositiveNotification.Marshal(b, m, deterministic)
}
func (dst *CompositiveNotification) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompositiveNotification.Merge(dst, src)
}
func (m *CompositiveNotification) XXX_Size() int {
	return xxx_messageInfo_CompositiveNotification.Size(m)
}
func (m *CompositiveNotification) XXX_DiscardUnknown() {
	xxx_messageInfo_CompositiveNotification.DiscardUnknown(m)
}

var xxx_messageInfo_CompositiveNotification proto.InternalMessageInfo

func (m *CompositiveNotification) GetSequence() uint32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func (m *CompositiveNotification) GetTerminalTypeList() []uint32 {
	if m != nil {
		return m.TerminalTypeList
	}
	return nil
}

func (m *CompositiveNotification) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *CompositiveNotification) GetProxyNotification() *ProxyNotification {
	if m != nil {
		return m.ProxyNotification
	}
	return nil
}

func (m *CompositiveNotification) GetApnsNotification() *APNs.Notification {
	if m != nil {
		return m.ApnsNotification
	}
	return nil
}

func (m *CompositiveNotification) GetApnsFrequence() uint32 {
	if m != nil {
		return m.ApnsFrequence
	}
	return 0
}

func (m *CompositiveNotification) GetClientTimestamp() uint32 {
	if m != nil {
		return m.ClientTimestamp
	}
	return 0
}

func (m *CompositiveNotification) GetTerminalTypePolicy() *TerminalTypePolicy {
	if m != nil {
		return m.TerminalTypePolicy
	}
	return nil
}

type TerminalTypePolicy struct {
	PolicyGroup          string   `protobuf:"bytes,1,opt,name=policy_group,json=policyGroup,proto3" json:"policy_group,omitempty"`
	PolicyName           string   `protobuf:"bytes,2,opt,name=policy_name,json=policyName,proto3" json:"policy_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TerminalTypePolicy) Reset()         { *m = TerminalTypePolicy{} }
func (m *TerminalTypePolicy) String() string { return proto.CompactTextString(m) }
func (*TerminalTypePolicy) ProtoMessage()    {}
func (*TerminalTypePolicy) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{2}
}
func (m *TerminalTypePolicy) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TerminalTypePolicy.Unmarshal(m, b)
}
func (m *TerminalTypePolicy) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TerminalTypePolicy.Marshal(b, m, deterministic)
}
func (dst *TerminalTypePolicy) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TerminalTypePolicy.Merge(dst, src)
}
func (m *TerminalTypePolicy) XXX_Size() int {
	return xxx_messageInfo_TerminalTypePolicy.Size(m)
}
func (m *TerminalTypePolicy) XXX_DiscardUnknown() {
	xxx_messageInfo_TerminalTypePolicy.DiscardUnknown(m)
}

var xxx_messageInfo_TerminalTypePolicy proto.InternalMessageInfo

func (m *TerminalTypePolicy) GetPolicyGroup() string {
	if m != nil {
		return m.PolicyGroup
	}
	return ""
}

func (m *TerminalTypePolicy) GetPolicyName() string {
	if m != nil {
		return m.PolicyName
	}
	return ""
}

type GetTerminalTypePolicyResp struct {
	TerminalTypePolicyInfoList []*TerminalTypePolicyInfo `protobuf:"bytes,1,rep,name=terminal_type_policy_info_list,json=terminalTypePolicyInfoList,proto3" json:"terminal_type_policy_info_list,omitempty"`
	XXX_NoUnkeyedLiteral       struct{}                  `json:"-"`
	XXX_unrecognized           []byte                    `json:"-"`
	XXX_sizecache              int32                     `json:"-"`
}

func (m *GetTerminalTypePolicyResp) Reset()         { *m = GetTerminalTypePolicyResp{} }
func (m *GetTerminalTypePolicyResp) String() string { return proto.CompactTextString(m) }
func (*GetTerminalTypePolicyResp) ProtoMessage()    {}
func (*GetTerminalTypePolicyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{3}
}
func (m *GetTerminalTypePolicyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTerminalTypePolicyResp.Unmarshal(m, b)
}
func (m *GetTerminalTypePolicyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTerminalTypePolicyResp.Marshal(b, m, deterministic)
}
func (dst *GetTerminalTypePolicyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTerminalTypePolicyResp.Merge(dst, src)
}
func (m *GetTerminalTypePolicyResp) XXX_Size() int {
	return xxx_messageInfo_GetTerminalTypePolicyResp.Size(m)
}
func (m *GetTerminalTypePolicyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTerminalTypePolicyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTerminalTypePolicyResp proto.InternalMessageInfo

func (m *GetTerminalTypePolicyResp) GetTerminalTypePolicyInfoList() []*TerminalTypePolicyInfo {
	if m != nil {
		return m.TerminalTypePolicyInfoList
	}
	return nil
}

type SetTerminalTypePolicyResp struct {
	Success              bool     `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetTerminalTypePolicyResp) Reset()         { *m = SetTerminalTypePolicyResp{} }
func (m *SetTerminalTypePolicyResp) String() string { return proto.CompactTextString(m) }
func (*SetTerminalTypePolicyResp) ProtoMessage()    {}
func (*SetTerminalTypePolicyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{4}
}
func (m *SetTerminalTypePolicyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTerminalTypePolicyResp.Unmarshal(m, b)
}
func (m *SetTerminalTypePolicyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTerminalTypePolicyResp.Marshal(b, m, deterministic)
}
func (dst *SetTerminalTypePolicyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTerminalTypePolicyResp.Merge(dst, src)
}
func (m *SetTerminalTypePolicyResp) XXX_Size() int {
	return xxx_messageInfo_SetTerminalTypePolicyResp.Size(m)
}
func (m *SetTerminalTypePolicyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTerminalTypePolicyResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetTerminalTypePolicyResp proto.InternalMessageInfo

func (m *SetTerminalTypePolicyResp) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

type TerminalTypePolicyInfo struct {
	TerminalTypePolicy   *TerminalTypePolicy `protobuf:"bytes,1,opt,name=terminal_type_policy,json=terminalTypePolicy,proto3" json:"terminal_type_policy,omitempty"`
	TerminalTypeList     []uint32            `protobuf:"varint,2,rep,packed,name=terminal_type_list,json=terminalTypeList,proto3" json:"terminal_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *TerminalTypePolicyInfo) Reset()         { *m = TerminalTypePolicyInfo{} }
func (m *TerminalTypePolicyInfo) String() string { return proto.CompactTextString(m) }
func (*TerminalTypePolicyInfo) ProtoMessage()    {}
func (*TerminalTypePolicyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{5}
}
func (m *TerminalTypePolicyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TerminalTypePolicyInfo.Unmarshal(m, b)
}
func (m *TerminalTypePolicyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TerminalTypePolicyInfo.Marshal(b, m, deterministic)
}
func (dst *TerminalTypePolicyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TerminalTypePolicyInfo.Merge(dst, src)
}
func (m *TerminalTypePolicyInfo) XXX_Size() int {
	return xxx_messageInfo_TerminalTypePolicyInfo.Size(m)
}
func (m *TerminalTypePolicyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TerminalTypePolicyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TerminalTypePolicyInfo proto.InternalMessageInfo

func (m *TerminalTypePolicyInfo) GetTerminalTypePolicy() *TerminalTypePolicy {
	if m != nil {
		return m.TerminalTypePolicy
	}
	return nil
}

func (m *TerminalTypePolicyInfo) GetTerminalTypeList() []uint32 {
	if m != nil {
		return m.TerminalTypeList
	}
	return nil
}

type MulticastAccount struct {
	Id                   uint64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MulticastAccount) Reset()         { *m = MulticastAccount{} }
func (m *MulticastAccount) String() string { return proto.CompactTextString(m) }
func (*MulticastAccount) ProtoMessage()    {}
func (*MulticastAccount) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{6}
}
func (m *MulticastAccount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MulticastAccount.Unmarshal(m, b)
}
func (m *MulticastAccount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MulticastAccount.Marshal(b, m, deterministic)
}
func (dst *MulticastAccount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MulticastAccount.Merge(dst, src)
}
func (m *MulticastAccount) XXX_Size() int {
	return xxx_messageInfo_MulticastAccount.Size(m)
}
func (m *MulticastAccount) XXX_DiscardUnknown() {
	xxx_messageInfo_MulticastAccount.DiscardUnknown(m)
}

var xxx_messageInfo_MulticastAccount proto.InternalMessageInfo

func (m *MulticastAccount) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MulticastAccount) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type ProxyPushResult struct {
	Result               int32             `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	UserNotifyCountsMap  map[uint32]uint32 `protobuf:"bytes,2,rep,name=user_notify_counts_map,json=userNotifyCountsMap,proto3" json:"user_notify_counts_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ProxyPushResult) Reset()         { *m = ProxyPushResult{} }
func (m *ProxyPushResult) String() string { return proto.CompactTextString(m) }
func (*ProxyPushResult) ProtoMessage()    {}
func (*ProxyPushResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{7}
}
func (m *ProxyPushResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProxyPushResult.Unmarshal(m, b)
}
func (m *ProxyPushResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProxyPushResult.Marshal(b, m, deterministic)
}
func (dst *ProxyPushResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProxyPushResult.Merge(dst, src)
}
func (m *ProxyPushResult) XXX_Size() int {
	return xxx_messageInfo_ProxyPushResult.Size(m)
}
func (m *ProxyPushResult) XXX_DiscardUnknown() {
	xxx_messageInfo_ProxyPushResult.DiscardUnknown(m)
}

var xxx_messageInfo_ProxyPushResult proto.InternalMessageInfo

func (m *ProxyPushResult) GetResult() int32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *ProxyPushResult) GetUserNotifyCountsMap() map[uint32]uint32 {
	if m != nil {
		return m.UserNotifyCountsMap
	}
	return nil
}

type PushToUserMapReq struct {
	UidMap               map[uint32]*CompositiveNotification `protobuf:"bytes,1,rep,name=uid_map,json=uidMap,proto3" json:"uid_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Sync                 bool                                `protobuf:"varint,2,opt,name=sync,proto3" json:"sync,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *PushToUserMapReq) Reset()         { *m = PushToUserMapReq{} }
func (m *PushToUserMapReq) String() string { return proto.CompactTextString(m) }
func (*PushToUserMapReq) ProtoMessage()    {}
func (*PushToUserMapReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{8}
}
func (m *PushToUserMapReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushToUserMapReq.Unmarshal(m, b)
}
func (m *PushToUserMapReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushToUserMapReq.Marshal(b, m, deterministic)
}
func (dst *PushToUserMapReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushToUserMapReq.Merge(dst, src)
}
func (m *PushToUserMapReq) XXX_Size() int {
	return xxx_messageInfo_PushToUserMapReq.Size(m)
}
func (m *PushToUserMapReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushToUserMapReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushToUserMapReq proto.InternalMessageInfo

func (m *PushToUserMapReq) GetUidMap() map[uint32]*CompositiveNotification {
	if m != nil {
		return m.UidMap
	}
	return nil
}

func (m *PushToUserMapReq) GetSync() bool {
	if m != nil {
		return m.Sync
	}
	return false
}

type PushToUserMapResp struct {
	Sequence             map[uint32]uint32 `protobuf:"bytes,1,rep,name=sequence,proto3" json:"sequence,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ProxyPushResult      *ProxyPushResult  `protobuf:"bytes,2,opt,name=proxy_push_result,json=proxyPushResult,proto3" json:"proxy_push_result,omitempty"`
	RequestId            string            `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PushToUserMapResp) Reset()         { *m = PushToUserMapResp{} }
func (m *PushToUserMapResp) String() string { return proto.CompactTextString(m) }
func (*PushToUserMapResp) ProtoMessage()    {}
func (*PushToUserMapResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{9}
}
func (m *PushToUserMapResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushToUserMapResp.Unmarshal(m, b)
}
func (m *PushToUserMapResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushToUserMapResp.Marshal(b, m, deterministic)
}
func (dst *PushToUserMapResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushToUserMapResp.Merge(dst, src)
}
func (m *PushToUserMapResp) XXX_Size() int {
	return xxx_messageInfo_PushToUserMapResp.Size(m)
}
func (m *PushToUserMapResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushToUserMapResp.DiscardUnknown(m)
}

var xxx_messageInfo_PushToUserMapResp proto.InternalMessageInfo

func (m *PushToUserMapResp) GetSequence() map[uint32]uint32 {
	if m != nil {
		return m.Sequence
	}
	return nil
}

func (m *PushToUserMapResp) GetProxyPushResult() *ProxyPushResult {
	if m != nil {
		return m.ProxyPushResult
	}
	return nil
}

func (m *PushToUserMapResp) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

type PushToUsersReq struct {
	UidList              []uint32                 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Notification         *CompositiveNotification `protobuf:"bytes,2,opt,name=notification,proto3" json:"notification,omitempty"`
	Sync                 bool                     `protobuf:"varint,3,opt,name=sync,proto3" json:"sync,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *PushToUsersReq) Reset()         { *m = PushToUsersReq{} }
func (m *PushToUsersReq) String() string { return proto.CompactTextString(m) }
func (*PushToUsersReq) ProtoMessage()    {}
func (*PushToUsersReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{10}
}
func (m *PushToUsersReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushToUsersReq.Unmarshal(m, b)
}
func (m *PushToUsersReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushToUsersReq.Marshal(b, m, deterministic)
}
func (dst *PushToUsersReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushToUsersReq.Merge(dst, src)
}
func (m *PushToUsersReq) XXX_Size() int {
	return xxx_messageInfo_PushToUsersReq.Size(m)
}
func (m *PushToUsersReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushToUsersReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushToUsersReq proto.InternalMessageInfo

func (m *PushToUsersReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *PushToUsersReq) GetNotification() *CompositiveNotification {
	if m != nil {
		return m.Notification
	}
	return nil
}

func (m *PushToUsersReq) GetSync() bool {
	if m != nil {
		return m.Sync
	}
	return false
}

type PushMulticastReq struct {
	MulticastAccount     *MulticastAccount        `protobuf:"bytes,1,opt,name=multicast_account,json=multicastAccount,proto3" json:"multicast_account,omitempty"`
	Notification         *CompositiveNotification `protobuf:"bytes,2,opt,name=notification,proto3" json:"notification,omitempty"`
	SkipUids             []uint32                 `protobuf:"varint,3,rep,packed,name=skip_uids,json=skipUids,proto3" json:"skip_uids,omitempty"`
	MulticastAccounts    []*MulticastAccount      `protobuf:"bytes,4,rep,name=multicast_accounts,json=multicastAccounts,proto3" json:"multicast_accounts,omitempty"`
	Sync                 bool                     `protobuf:"varint,5,opt,name=sync,proto3" json:"sync,omitempty"`
	OnlyUids             []uint32                 `protobuf:"varint,6,rep,packed,name=only_uids,json=onlyUids,proto3" json:"only_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *PushMulticastReq) Reset()         { *m = PushMulticastReq{} }
func (m *PushMulticastReq) String() string { return proto.CompactTextString(m) }
func (*PushMulticastReq) ProtoMessage()    {}
func (*PushMulticastReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{11}
}
func (m *PushMulticastReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushMulticastReq.Unmarshal(m, b)
}
func (m *PushMulticastReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushMulticastReq.Marshal(b, m, deterministic)
}
func (dst *PushMulticastReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushMulticastReq.Merge(dst, src)
}
func (m *PushMulticastReq) XXX_Size() int {
	return xxx_messageInfo_PushMulticastReq.Size(m)
}
func (m *PushMulticastReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushMulticastReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushMulticastReq proto.InternalMessageInfo

func (m *PushMulticastReq) GetMulticastAccount() *MulticastAccount {
	if m != nil {
		return m.MulticastAccount
	}
	return nil
}

func (m *PushMulticastReq) GetNotification() *CompositiveNotification {
	if m != nil {
		return m.Notification
	}
	return nil
}

func (m *PushMulticastReq) GetSkipUids() []uint32 {
	if m != nil {
		return m.SkipUids
	}
	return nil
}

func (m *PushMulticastReq) GetMulticastAccounts() []*MulticastAccount {
	if m != nil {
		return m.MulticastAccounts
	}
	return nil
}

func (m *PushMulticastReq) GetSync() bool {
	if m != nil {
		return m.Sync
	}
	return false
}

func (m *PushMulticastReq) GetOnlyUids() []uint32 {
	if m != nil {
		return m.OnlyUids
	}
	return nil
}

type PushToUsersResp struct {
	Sequence             uint32           `protobuf:"varint,1,opt,name=sequence,proto3" json:"sequence,omitempty"`
	ProxyPushResult      *ProxyPushResult `protobuf:"bytes,2,opt,name=proxy_push_result,json=proxyPushResult,proto3" json:"proxy_push_result,omitempty"`
	RequestId            string           `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PushToUsersResp) Reset()         { *m = PushToUsersResp{} }
func (m *PushToUsersResp) String() string { return proto.CompactTextString(m) }
func (*PushToUsersResp) ProtoMessage()    {}
func (*PushToUsersResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{12}
}
func (m *PushToUsersResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushToUsersResp.Unmarshal(m, b)
}
func (m *PushToUsersResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushToUsersResp.Marshal(b, m, deterministic)
}
func (dst *PushToUsersResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushToUsersResp.Merge(dst, src)
}
func (m *PushToUsersResp) XXX_Size() int {
	return xxx_messageInfo_PushToUsersResp.Size(m)
}
func (m *PushToUsersResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushToUsersResp.DiscardUnknown(m)
}

var xxx_messageInfo_PushToUsersResp proto.InternalMessageInfo

func (m *PushToUsersResp) GetSequence() uint32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func (m *PushToUsersResp) GetProxyPushResult() *ProxyPushResult {
	if m != nil {
		return m.ProxyPushResult
	}
	return nil
}

func (m *PushToUsersResp) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

type PushMulticastResp struct {
	Sequence             uint32   `protobuf:"varint,1,opt,name=sequence,proto3" json:"sequence,omitempty"`
	RequestId            string   `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushMulticastResp) Reset()         { *m = PushMulticastResp{} }
func (m *PushMulticastResp) String() string { return proto.CompactTextString(m) }
func (*PushMulticastResp) ProtoMessage()    {}
func (*PushMulticastResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{13}
}
func (m *PushMulticastResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushMulticastResp.Unmarshal(m, b)
}
func (m *PushMulticastResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushMulticastResp.Marshal(b, m, deterministic)
}
func (dst *PushMulticastResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushMulticastResp.Merge(dst, src)
}
func (m *PushMulticastResp) XXX_Size() int {
	return xxx_messageInfo_PushMulticastResp.Size(m)
}
func (m *PushMulticastResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushMulticastResp.DiscardUnknown(m)
}

var xxx_messageInfo_PushMulticastResp proto.InternalMessageInfo

func (m *PushMulticastResp) GetSequence() uint32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func (m *PushMulticastResp) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

type Presence struct {
	ProxyIp              uint32   `protobuf:"varint,1,opt,name=proxy_ip,json=proxyIp,proto3" json:"proxy_ip,omitempty"`
	ProxyPort            uint32   `protobuf:"varint,2,opt,name=proxy_port,json=proxyPort,proto3" json:"proxy_port,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	ClientId             uint32   `protobuf:"varint,4,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	TerminalType         uint32   `protobuf:"varint,5,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Presence) Reset()         { *m = Presence{} }
func (m *Presence) String() string { return proto.CompactTextString(m) }
func (*Presence) ProtoMessage()    {}
func (*Presence) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{14}
}
func (m *Presence) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Presence.Unmarshal(m, b)
}
func (m *Presence) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Presence.Marshal(b, m, deterministic)
}
func (dst *Presence) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Presence.Merge(dst, src)
}
func (m *Presence) XXX_Size() int {
	return xxx_messageInfo_Presence.Size(m)
}
func (m *Presence) XXX_DiscardUnknown() {
	xxx_messageInfo_Presence.DiscardUnknown(m)
}

var xxx_messageInfo_Presence proto.InternalMessageInfo

func (m *Presence) GetProxyIp() uint32 {
	if m != nil {
		return m.ProxyIp
	}
	return 0
}

func (m *Presence) GetProxyPort() uint32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

func (m *Presence) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Presence) GetClientId() uint32 {
	if m != nil {
		return m.ClientId
	}
	return 0
}

func (m *Presence) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

type Presences struct {
	Ps                   []*Presence `protobuf:"bytes,1,rep,name=ps,proto3" json:"ps,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *Presences) Reset()         { *m = Presences{} }
func (m *Presences) String() string { return proto.CompactTextString(m) }
func (*Presences) ProtoMessage()    {}
func (*Presences) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{15}
}
func (m *Presences) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Presences.Unmarshal(m, b)
}
func (m *Presences) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Presences.Marshal(b, m, deterministic)
}
func (dst *Presences) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Presences.Merge(dst, src)
}
func (m *Presences) XXX_Size() int {
	return xxx_messageInfo_Presences.Size(m)
}
func (m *Presences) XXX_DiscardUnknown() {
	xxx_messageInfo_Presences.DiscardUnknown(m)
}

var xxx_messageInfo_Presences proto.InternalMessageInfo

func (m *Presences) GetPs() []*Presence {
	if m != nil {
		return m.Ps
	}
	return nil
}

type PushToPresenceListReq struct {
	PresenceList         []*Presence              `protobuf:"bytes,1,rep,name=presence_list,json=presenceList,proto3" json:"presence_list,omitempty"`
	Notification         *CompositiveNotification `protobuf:"bytes,2,opt,name=notification,proto3" json:"notification,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *PushToPresenceListReq) Reset()         { *m = PushToPresenceListReq{} }
func (m *PushToPresenceListReq) String() string { return proto.CompactTextString(m) }
func (*PushToPresenceListReq) ProtoMessage()    {}
func (*PushToPresenceListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{16}
}
func (m *PushToPresenceListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushToPresenceListReq.Unmarshal(m, b)
}
func (m *PushToPresenceListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushToPresenceListReq.Marshal(b, m, deterministic)
}
func (dst *PushToPresenceListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushToPresenceListReq.Merge(dst, src)
}
func (m *PushToPresenceListReq) XXX_Size() int {
	return xxx_messageInfo_PushToPresenceListReq.Size(m)
}
func (m *PushToPresenceListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushToPresenceListReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushToPresenceListReq proto.InternalMessageInfo

func (m *PushToPresenceListReq) GetPresenceList() []*Presence {
	if m != nil {
		return m.PresenceList
	}
	return nil
}

func (m *PushToPresenceListReq) GetNotification() *CompositiveNotification {
	if m != nil {
		return m.Notification
	}
	return nil
}

type PushRelationEventResult struct {
	// Types that are valid to be assigned to Type:
	//	*PushRelationEventResult_Succeed_
	//	*PushRelationEventResult_Error
	Type                 isPushRelationEventResult_Type `protobuf_oneof:"type"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *PushRelationEventResult) Reset()         { *m = PushRelationEventResult{} }
func (m *PushRelationEventResult) String() string { return proto.CompactTextString(m) }
func (*PushRelationEventResult) ProtoMessage()    {}
func (*PushRelationEventResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{17}
}
func (m *PushRelationEventResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushRelationEventResult.Unmarshal(m, b)
}
func (m *PushRelationEventResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushRelationEventResult.Marshal(b, m, deterministic)
}
func (dst *PushRelationEventResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushRelationEventResult.Merge(dst, src)
}
func (m *PushRelationEventResult) XXX_Size() int {
	return xxx_messageInfo_PushRelationEventResult.Size(m)
}
func (m *PushRelationEventResult) XXX_DiscardUnknown() {
	xxx_messageInfo_PushRelationEventResult.DiscardUnknown(m)
}

var xxx_messageInfo_PushRelationEventResult proto.InternalMessageInfo

type isPushRelationEventResult_Type interface {
	isPushRelationEventResult_Type()
}

type PushRelationEventResult_Succeed_ struct {
	Succeed *PushRelationEventResult_Succeed `protobuf:"bytes,1,opt,name=succeed,proto3,oneof"`
}

type PushRelationEventResult_Error struct {
	Error string `protobuf:"bytes,2,opt,name=error,proto3,oneof"`
}

func (*PushRelationEventResult_Succeed_) isPushRelationEventResult_Type() {}

func (*PushRelationEventResult_Error) isPushRelationEventResult_Type() {}

func (m *PushRelationEventResult) GetType() isPushRelationEventResult_Type {
	if m != nil {
		return m.Type
	}
	return nil
}

func (m *PushRelationEventResult) GetSucceed() *PushRelationEventResult_Succeed {
	if x, ok := m.GetType().(*PushRelationEventResult_Succeed_); ok {
		return x.Succeed
	}
	return nil
}

func (m *PushRelationEventResult) GetError() string {
	if x, ok := m.GetType().(*PushRelationEventResult_Error); ok {
		return x.Error
	}
	return ""
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*PushRelationEventResult) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _PushRelationEventResult_OneofMarshaler, _PushRelationEventResult_OneofUnmarshaler, _PushRelationEventResult_OneofSizer, []interface{}{
		(*PushRelationEventResult_Succeed_)(nil),
		(*PushRelationEventResult_Error)(nil),
	}
}

func _PushRelationEventResult_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*PushRelationEventResult)
	// type
	switch x := m.Type.(type) {
	case *PushRelationEventResult_Succeed_:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Succeed); err != nil {
			return err
		}
	case *PushRelationEventResult_Error:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.Error)
	case nil:
	default:
		return fmt.Errorf("PushRelationEventResult.Type has unexpected type %T", x)
	}
	return nil
}

func _PushRelationEventResult_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*PushRelationEventResult)
	switch tag {
	case 1: // type.succeed
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(PushRelationEventResult_Succeed)
		err := b.DecodeMessage(msg)
		m.Type = &PushRelationEventResult_Succeed_{msg}
		return true, err
	case 2: // type.error
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.Type = &PushRelationEventResult_Error{x}
		return true, err
	default:
		return false, nil
	}
}

func _PushRelationEventResult_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*PushRelationEventResult)
	// type
	switch x := m.Type.(type) {
	case *PushRelationEventResult_Succeed_:
		s := proto.Size(x.Succeed)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *PushRelationEventResult_Error:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(len(x.Error)))
		n += len(x.Error)
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type PushRelationEventResult_Succeed struct {
	DeliverAddrs         []string `protobuf:"bytes,1,rep,name=deliver_addrs,json=deliverAddrs,proto3" json:"deliver_addrs,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushRelationEventResult_Succeed) Reset()         { *m = PushRelationEventResult_Succeed{} }
func (m *PushRelationEventResult_Succeed) String() string { return proto.CompactTextString(m) }
func (*PushRelationEventResult_Succeed) ProtoMessage()    {}
func (*PushRelationEventResult_Succeed) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{17, 0}
}
func (m *PushRelationEventResult_Succeed) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushRelationEventResult_Succeed.Unmarshal(m, b)
}
func (m *PushRelationEventResult_Succeed) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushRelationEventResult_Succeed.Marshal(b, m, deterministic)
}
func (dst *PushRelationEventResult_Succeed) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushRelationEventResult_Succeed.Merge(dst, src)
}
func (m *PushRelationEventResult_Succeed) XXX_Size() int {
	return xxx_messageInfo_PushRelationEventResult_Succeed.Size(m)
}
func (m *PushRelationEventResult_Succeed) XXX_DiscardUnknown() {
	xxx_messageInfo_PushRelationEventResult_Succeed.DiscardUnknown(m)
}

var xxx_messageInfo_PushRelationEventResult_Succeed proto.InternalMessageInfo

func (m *PushRelationEventResult_Succeed) GetDeliverAddrs() []string {
	if m != nil {
		return m.DeliverAddrs
	}
	return nil
}

type RelationRegisterEvent struct {
	RegEventType RelationRegisterEventType `protobuf:"varint,1,opt,name=reg_event_type,json=regEventType,proto3,enum=PushNotification.RelationRegisterEventType" json:"reg_event_type,omitempty"`
	Uid          uint32                    `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Channels     []uint32                  `protobuf:"varint,3,rep,packed,name=channels,proto3" json:"channels,omitempty"`
	TerminalType uint32                    `protobuf:"varint,4,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	// 以下为可选参数
	ClientId             uint32   `protobuf:"varint,5,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	EventSeq             uint64   `protobuf:"varint,6,opt,name=event_seq,json=eventSeq,proto3" json:"event_seq,omitempty"`
	DelayMs              uint32   `protobuf:"varint,7,opt,name=delay_ms,json=delayMs,proto3" json:"delay_ms,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RelationRegisterEvent) Reset()         { *m = RelationRegisterEvent{} }
func (m *RelationRegisterEvent) String() string { return proto.CompactTextString(m) }
func (*RelationRegisterEvent) ProtoMessage()    {}
func (*RelationRegisterEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{18}
}
func (m *RelationRegisterEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelationRegisterEvent.Unmarshal(m, b)
}
func (m *RelationRegisterEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelationRegisterEvent.Marshal(b, m, deterministic)
}
func (dst *RelationRegisterEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelationRegisterEvent.Merge(dst, src)
}
func (m *RelationRegisterEvent) XXX_Size() int {
	return xxx_messageInfo_RelationRegisterEvent.Size(m)
}
func (m *RelationRegisterEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_RelationRegisterEvent.DiscardUnknown(m)
}

var xxx_messageInfo_RelationRegisterEvent proto.InternalMessageInfo

func (m *RelationRegisterEvent) GetRegEventType() RelationRegisterEventType {
	if m != nil {
		return m.RegEventType
	}
	return RelationRegisterEventType_ENUM_CHANNEL_RELATION_REGENENT_REGIST
}

func (m *RelationRegisterEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RelationRegisterEvent) GetChannels() []uint32 {
	if m != nil {
		return m.Channels
	}
	return nil
}

func (m *RelationRegisterEvent) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *RelationRegisterEvent) GetClientId() uint32 {
	if m != nil {
		return m.ClientId
	}
	return 0
}

func (m *RelationRegisterEvent) GetEventSeq() uint64 {
	if m != nil {
		return m.EventSeq
	}
	return 0
}

func (m *RelationRegisterEvent) GetDelayMs() uint32 {
	if m != nil {
		return m.DelayMs
	}
	return 0
}

// push the regist event to proxynotify
type PushRelationRegisterEventReq struct {
	Event                *RelationRegisterEvent `protobuf:"bytes,1,opt,name=event,proto3" json:"event,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *PushRelationRegisterEventReq) Reset()         { *m = PushRelationRegisterEventReq{} }
func (m *PushRelationRegisterEventReq) String() string { return proto.CompactTextString(m) }
func (*PushRelationRegisterEventReq) ProtoMessage()    {}
func (*PushRelationRegisterEventReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{19}
}
func (m *PushRelationRegisterEventReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushRelationRegisterEventReq.Unmarshal(m, b)
}
func (m *PushRelationRegisterEventReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushRelationRegisterEventReq.Marshal(b, m, deterministic)
}
func (dst *PushRelationRegisterEventReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushRelationRegisterEventReq.Merge(dst, src)
}
func (m *PushRelationRegisterEventReq) XXX_Size() int {
	return xxx_messageInfo_PushRelationRegisterEventReq.Size(m)
}
func (m *PushRelationRegisterEventReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushRelationRegisterEventReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushRelationRegisterEventReq proto.InternalMessageInfo

func (m *PushRelationRegisterEventReq) GetEvent() *RelationRegisterEvent {
	if m != nil {
		return m.Event
	}
	return nil
}

// return the address of proxy_notify or error message;
type PushRelationRegisterEventResp struct {
	Result               *PushRelationEventResult `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *PushRelationRegisterEventResp) Reset()         { *m = PushRelationRegisterEventResp{} }
func (m *PushRelationRegisterEventResp) String() string { return proto.CompactTextString(m) }
func (*PushRelationRegisterEventResp) ProtoMessage()    {}
func (*PushRelationRegisterEventResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{20}
}
func (m *PushRelationRegisterEventResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushRelationRegisterEventResp.Unmarshal(m, b)
}
func (m *PushRelationRegisterEventResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushRelationRegisterEventResp.Marshal(b, m, deterministic)
}
func (dst *PushRelationRegisterEventResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushRelationRegisterEventResp.Merge(dst, src)
}
func (m *PushRelationRegisterEventResp) XXX_Size() int {
	return xxx_messageInfo_PushRelationRegisterEventResp.Size(m)
}
func (m *PushRelationRegisterEventResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushRelationRegisterEventResp.DiscardUnknown(m)
}

var xxx_messageInfo_PushRelationRegisterEventResp proto.InternalMessageInfo

func (m *PushRelationRegisterEventResp) GetResult() *PushRelationEventResult {
	if m != nil {
		return m.Result
	}
	return nil
}

type BatchPushRelationRegisterEventReq struct {
	Events               []*RelationRegisterEvent `protobuf:"bytes,1,rep,name=events,proto3" json:"events,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *BatchPushRelationRegisterEventReq) Reset()         { *m = BatchPushRelationRegisterEventReq{} }
func (m *BatchPushRelationRegisterEventReq) String() string { return proto.CompactTextString(m) }
func (*BatchPushRelationRegisterEventReq) ProtoMessage()    {}
func (*BatchPushRelationRegisterEventReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{21}
}
func (m *BatchPushRelationRegisterEventReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchPushRelationRegisterEventReq.Unmarshal(m, b)
}
func (m *BatchPushRelationRegisterEventReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchPushRelationRegisterEventReq.Marshal(b, m, deterministic)
}
func (dst *BatchPushRelationRegisterEventReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchPushRelationRegisterEventReq.Merge(dst, src)
}
func (m *BatchPushRelationRegisterEventReq) XXX_Size() int {
	return xxx_messageInfo_BatchPushRelationRegisterEventReq.Size(m)
}
func (m *BatchPushRelationRegisterEventReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchPushRelationRegisterEventReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchPushRelationRegisterEventReq proto.InternalMessageInfo

func (m *BatchPushRelationRegisterEventReq) GetEvents() []*RelationRegisterEvent {
	if m != nil {
		return m.Events
	}
	return nil
}

type BatchPushRelationRegisterEventResp struct {
	// <uid,result>
	Results              map[uint32]*PushRelationEventResult `protobuf:"bytes,2,rep,name=results,proto3" json:"results,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *BatchPushRelationRegisterEventResp) Reset()         { *m = BatchPushRelationRegisterEventResp{} }
func (m *BatchPushRelationRegisterEventResp) String() string { return proto.CompactTextString(m) }
func (*BatchPushRelationRegisterEventResp) ProtoMessage()    {}
func (*BatchPushRelationRegisterEventResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{22}
}
func (m *BatchPushRelationRegisterEventResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchPushRelationRegisterEventResp.Unmarshal(m, b)
}
func (m *BatchPushRelationRegisterEventResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchPushRelationRegisterEventResp.Marshal(b, m, deterministic)
}
func (dst *BatchPushRelationRegisterEventResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchPushRelationRegisterEventResp.Merge(dst, src)
}
func (m *BatchPushRelationRegisterEventResp) XXX_Size() int {
	return xxx_messageInfo_BatchPushRelationRegisterEventResp.Size(m)
}
func (m *BatchPushRelationRegisterEventResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchPushRelationRegisterEventResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchPushRelationRegisterEventResp proto.InternalMessageInfo

func (m *BatchPushRelationRegisterEventResp) GetResults() map[uint32]*PushRelationEventResult {
	if m != nil {
		return m.Results
	}
	return nil
}

type GetReliableProxyNotificationsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SequenceBegin        uint32   `protobuf:"varint,2,opt,name=sequence_begin,json=sequenceBegin,proto3" json:"sequence_begin,omitempty"`
	SequenceEnd          uint32   `protobuf:"varint,3,opt,name=sequence_end,json=sequenceEnd,proto3" json:"sequence_end,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReliableProxyNotificationsReq) Reset()         { *m = GetReliableProxyNotificationsReq{} }
func (m *GetReliableProxyNotificationsReq) String() string { return proto.CompactTextString(m) }
func (*GetReliableProxyNotificationsReq) ProtoMessage()    {}
func (*GetReliableProxyNotificationsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{23}
}
func (m *GetReliableProxyNotificationsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReliableProxyNotificationsReq.Unmarshal(m, b)
}
func (m *GetReliableProxyNotificationsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReliableProxyNotificationsReq.Marshal(b, m, deterministic)
}
func (dst *GetReliableProxyNotificationsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReliableProxyNotificationsReq.Merge(dst, src)
}
func (m *GetReliableProxyNotificationsReq) XXX_Size() int {
	return xxx_messageInfo_GetReliableProxyNotificationsReq.Size(m)
}
func (m *GetReliableProxyNotificationsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReliableProxyNotificationsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetReliableProxyNotificationsReq proto.InternalMessageInfo

func (m *GetReliableProxyNotificationsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetReliableProxyNotificationsReq) GetSequenceBegin() uint32 {
	if m != nil {
		return m.SequenceBegin
	}
	return 0
}

func (m *GetReliableProxyNotificationsReq) GetSequenceEnd() uint32 {
	if m != nil {
		return m.SequenceEnd
	}
	return 0
}

func (m *GetReliableProxyNotificationsReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type ReliableProxyNotification struct {
	Sequence             uint32             `protobuf:"varint,1,opt,name=sequence,proto3" json:"sequence,omitempty"`
	TerminalTypeList     []uint32           `protobuf:"varint,2,rep,packed,name=terminal_type_list,json=terminalTypeList,proto3" json:"terminal_type_list,omitempty"`
	AppId                uint32             `protobuf:"varint,3,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	ProxyNotification    *ProxyNotification `protobuf:"bytes,4,opt,name=proxy_notification,json=proxyNotification,proto3" json:"proxy_notification,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ReliableProxyNotification) Reset()         { *m = ReliableProxyNotification{} }
func (m *ReliableProxyNotification) String() string { return proto.CompactTextString(m) }
func (*ReliableProxyNotification) ProtoMessage()    {}
func (*ReliableProxyNotification) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{24}
}
func (m *ReliableProxyNotification) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReliableProxyNotification.Unmarshal(m, b)
}
func (m *ReliableProxyNotification) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReliableProxyNotification.Marshal(b, m, deterministic)
}
func (dst *ReliableProxyNotification) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReliableProxyNotification.Merge(dst, src)
}
func (m *ReliableProxyNotification) XXX_Size() int {
	return xxx_messageInfo_ReliableProxyNotification.Size(m)
}
func (m *ReliableProxyNotification) XXX_DiscardUnknown() {
	xxx_messageInfo_ReliableProxyNotification.DiscardUnknown(m)
}

var xxx_messageInfo_ReliableProxyNotification proto.InternalMessageInfo

func (m *ReliableProxyNotification) GetSequence() uint32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func (m *ReliableProxyNotification) GetTerminalTypeList() []uint32 {
	if m != nil {
		return m.TerminalTypeList
	}
	return nil
}

func (m *ReliableProxyNotification) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *ReliableProxyNotification) GetProxyNotification() *ProxyNotification {
	if m != nil {
		return m.ProxyNotification
	}
	return nil
}

// Clients are responsible for filtering the notifications with terminal types or app ids
type GetReliableProxyNotificationsResp struct {
	Notifications        []*ReliableProxyNotification `protobuf:"bytes,1,rep,name=notifications,proto3" json:"notifications,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetReliableProxyNotificationsResp) Reset()         { *m = GetReliableProxyNotificationsResp{} }
func (m *GetReliableProxyNotificationsResp) String() string { return proto.CompactTextString(m) }
func (*GetReliableProxyNotificationsResp) ProtoMessage()    {}
func (*GetReliableProxyNotificationsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{25}
}
func (m *GetReliableProxyNotificationsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReliableProxyNotificationsResp.Unmarshal(m, b)
}
func (m *GetReliableProxyNotificationsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReliableProxyNotificationsResp.Marshal(b, m, deterministic)
}
func (dst *GetReliableProxyNotificationsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReliableProxyNotificationsResp.Merge(dst, src)
}
func (m *GetReliableProxyNotificationsResp) XXX_Size() int {
	return xxx_messageInfo_GetReliableProxyNotificationsResp.Size(m)
}
func (m *GetReliableProxyNotificationsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReliableProxyNotificationsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetReliableProxyNotificationsResp proto.InternalMessageInfo

func (m *GetReliableProxyNotificationsResp) GetNotifications() []*ReliableProxyNotification {
	if m != nil {
		return m.Notifications
	}
	return nil
}

type MessageReceivedAckReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SequenceList         []uint32 `protobuf:"varint,2,rep,packed,name=sequence_list,json=sequenceList,proto3" json:"sequence_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MessageReceivedAckReq) Reset()         { *m = MessageReceivedAckReq{} }
func (m *MessageReceivedAckReq) String() string { return proto.CompactTextString(m) }
func (*MessageReceivedAckReq) ProtoMessage()    {}
func (*MessageReceivedAckReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{26}
}
func (m *MessageReceivedAckReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MessageReceivedAckReq.Unmarshal(m, b)
}
func (m *MessageReceivedAckReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MessageReceivedAckReq.Marshal(b, m, deterministic)
}
func (dst *MessageReceivedAckReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MessageReceivedAckReq.Merge(dst, src)
}
func (m *MessageReceivedAckReq) XXX_Size() int {
	return xxx_messageInfo_MessageReceivedAckReq.Size(m)
}
func (m *MessageReceivedAckReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MessageReceivedAckReq.DiscardUnknown(m)
}

var xxx_messageInfo_MessageReceivedAckReq proto.InternalMessageInfo

func (m *MessageReceivedAckReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MessageReceivedAckReq) GetSequenceList() []uint32 {
	if m != nil {
		return m.SequenceList
	}
	return nil
}

type MessageReceivedAckResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MessageReceivedAckResp) Reset()         { *m = MessageReceivedAckResp{} }
func (m *MessageReceivedAckResp) String() string { return proto.CompactTextString(m) }
func (*MessageReceivedAckResp) ProtoMessage()    {}
func (*MessageReceivedAckResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{27}
}
func (m *MessageReceivedAckResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MessageReceivedAckResp.Unmarshal(m, b)
}
func (m *MessageReceivedAckResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MessageReceivedAckResp.Marshal(b, m, deterministic)
}
func (dst *MessageReceivedAckResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MessageReceivedAckResp.Merge(dst, src)
}
func (m *MessageReceivedAckResp) XXX_Size() int {
	return xxx_messageInfo_MessageReceivedAckResp.Size(m)
}
func (m *MessageReceivedAckResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MessageReceivedAckResp.DiscardUnknown(m)
}

var xxx_messageInfo_MessageReceivedAckResp proto.InternalMessageInfo

type ExpireKey struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Seq                  uint32   `protobuf:"varint,2,opt,name=seq,proto3" json:"seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExpireKey) Reset()         { *m = ExpireKey{} }
func (m *ExpireKey) String() string { return proto.CompactTextString(m) }
func (*ExpireKey) ProtoMessage()    {}
func (*ExpireKey) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{28}
}
func (m *ExpireKey) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpireKey.Unmarshal(m, b)
}
func (m *ExpireKey) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpireKey.Marshal(b, m, deterministic)
}
func (dst *ExpireKey) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpireKey.Merge(dst, src)
}
func (m *ExpireKey) XXX_Size() int {
	return xxx_messageInfo_ExpireKey.Size(m)
}
func (m *ExpireKey) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpireKey.DiscardUnknown(m)
}

var xxx_messageInfo_ExpireKey proto.InternalMessageInfo

func (m *ExpireKey) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ExpireKey) GetSeq() uint32 {
	if m != nil {
		return m.Seq
	}
	return 0
}

type KickoutPushdChannelUsersReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KickoutPushdChannelUsersReq) Reset()         { *m = KickoutPushdChannelUsersReq{} }
func (m *KickoutPushdChannelUsersReq) String() string { return proto.CompactTextString(m) }
func (*KickoutPushdChannelUsersReq) ProtoMessage()    {}
func (*KickoutPushdChannelUsersReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{29}
}
func (m *KickoutPushdChannelUsersReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KickoutPushdChannelUsersReq.Unmarshal(m, b)
}
func (m *KickoutPushdChannelUsersReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KickoutPushdChannelUsersReq.Marshal(b, m, deterministic)
}
func (dst *KickoutPushdChannelUsersReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KickoutPushdChannelUsersReq.Merge(dst, src)
}
func (m *KickoutPushdChannelUsersReq) XXX_Size() int {
	return xxx_messageInfo_KickoutPushdChannelUsersReq.Size(m)
}
func (m *KickoutPushdChannelUsersReq) XXX_DiscardUnknown() {
	xxx_messageInfo_KickoutPushdChannelUsersReq.DiscardUnknown(m)
}

var xxx_messageInfo_KickoutPushdChannelUsersReq proto.InternalMessageInfo

func (m *KickoutPushdChannelUsersReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *KickoutPushdChannelUsersReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type KickoutPushdChannelUsersResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KickoutPushdChannelUsersResp) Reset()         { *m = KickoutPushdChannelUsersResp{} }
func (m *KickoutPushdChannelUsersResp) String() string { return proto.CompactTextString(m) }
func (*KickoutPushdChannelUsersResp) ProtoMessage()    {}
func (*KickoutPushdChannelUsersResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushnotification_v2_c551c54aa2ec0695, []int{30}
}
func (m *KickoutPushdChannelUsersResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KickoutPushdChannelUsersResp.Unmarshal(m, b)
}
func (m *KickoutPushdChannelUsersResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KickoutPushdChannelUsersResp.Marshal(b, m, deterministic)
}
func (dst *KickoutPushdChannelUsersResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KickoutPushdChannelUsersResp.Merge(dst, src)
}
func (m *KickoutPushdChannelUsersResp) XXX_Size() int {
	return xxx_messageInfo_KickoutPushdChannelUsersResp.Size(m)
}
func (m *KickoutPushdChannelUsersResp) XXX_DiscardUnknown() {
	xxx_messageInfo_KickoutPushdChannelUsersResp.DiscardUnknown(m)
}

var xxx_messageInfo_KickoutPushdChannelUsersResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*ProxyNotification)(nil), "PushNotification.ProxyNotification")
	proto.RegisterType((*CompositiveNotification)(nil), "PushNotification.CompositiveNotification")
	proto.RegisterType((*TerminalTypePolicy)(nil), "PushNotification.TerminalTypePolicy")
	proto.RegisterType((*GetTerminalTypePolicyResp)(nil), "PushNotification.GetTerminalTypePolicyResp")
	proto.RegisterType((*SetTerminalTypePolicyResp)(nil), "PushNotification.SetTerminalTypePolicyResp")
	proto.RegisterType((*TerminalTypePolicyInfo)(nil), "PushNotification.TerminalTypePolicyInfo")
	proto.RegisterType((*MulticastAccount)(nil), "PushNotification.MulticastAccount")
	proto.RegisterType((*ProxyPushResult)(nil), "PushNotification.ProxyPushResult")
	proto.RegisterMapType((map[uint32]uint32)(nil), "PushNotification.ProxyPushResult.UserNotifyCountsMapEntry")
	proto.RegisterType((*PushToUserMapReq)(nil), "PushNotification.PushToUserMapReq")
	proto.RegisterMapType((map[uint32]*CompositiveNotification)(nil), "PushNotification.PushToUserMapReq.UidMapEntry")
	proto.RegisterType((*PushToUserMapResp)(nil), "PushNotification.PushToUserMapResp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "PushNotification.PushToUserMapResp.SequenceEntry")
	proto.RegisterType((*PushToUsersReq)(nil), "PushNotification.PushToUsersReq")
	proto.RegisterType((*PushMulticastReq)(nil), "PushNotification.PushMulticastReq")
	proto.RegisterType((*PushToUsersResp)(nil), "PushNotification.PushToUsersResp")
	proto.RegisterType((*PushMulticastResp)(nil), "PushNotification.PushMulticastResp")
	proto.RegisterType((*Presence)(nil), "PushNotification.Presence")
	proto.RegisterType((*Presences)(nil), "PushNotification.Presences")
	proto.RegisterType((*PushToPresenceListReq)(nil), "PushNotification.PushToPresenceListReq")
	proto.RegisterType((*PushRelationEventResult)(nil), "PushNotification.PushRelationEventResult")
	proto.RegisterType((*PushRelationEventResult_Succeed)(nil), "PushNotification.PushRelationEventResult.Succeed")
	proto.RegisterType((*RelationRegisterEvent)(nil), "PushNotification.RelationRegisterEvent")
	proto.RegisterType((*PushRelationRegisterEventReq)(nil), "PushNotification.PushRelationRegisterEventReq")
	proto.RegisterType((*PushRelationRegisterEventResp)(nil), "PushNotification.PushRelationRegisterEventResp")
	proto.RegisterType((*BatchPushRelationRegisterEventReq)(nil), "PushNotification.BatchPushRelationRegisterEventReq")
	proto.RegisterType((*BatchPushRelationRegisterEventResp)(nil), "PushNotification.BatchPushRelationRegisterEventResp")
	proto.RegisterMapType((map[uint32]*PushRelationEventResult)(nil), "PushNotification.BatchPushRelationRegisterEventResp.ResultsEntry")
	proto.RegisterType((*GetReliableProxyNotificationsReq)(nil), "PushNotification.GetReliableProxyNotificationsReq")
	proto.RegisterType((*ReliableProxyNotification)(nil), "PushNotification.ReliableProxyNotification")
	proto.RegisterType((*GetReliableProxyNotificationsResp)(nil), "PushNotification.GetReliableProxyNotificationsResp")
	proto.RegisterType((*MessageReceivedAckReq)(nil), "PushNotification.MessageReceivedAckReq")
	proto.RegisterType((*MessageReceivedAckResp)(nil), "PushNotification.MessageReceivedAckResp")
	proto.RegisterType((*ExpireKey)(nil), "PushNotification.ExpireKey")
	proto.RegisterType((*KickoutPushdChannelUsersReq)(nil), "PushNotification.KickoutPushdChannelUsersReq")
	proto.RegisterType((*KickoutPushdChannelUsersResp)(nil), "PushNotification.KickoutPushdChannelUsersResp")
	proto.RegisterEnum("PushNotification.RelationRegisterEventType", RelationRegisterEventType_name, RelationRegisterEventType_value)
	proto.RegisterEnum("PushNotification.ProxyNotification_Type", ProxyNotification_Type_name, ProxyNotification_Type_value)
	proto.RegisterEnum("PushNotification.ProxyNotification_Policy", ProxyNotification_Policy_name, ProxyNotification_Policy_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PushNotificationClient is the client API for PushNotification service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PushNotificationClient interface {
	PushToUsers(ctx context.Context, opts ...grpc.CallOption) (PushNotification_PushToUsersClient, error)
	PushMulticast(ctx context.Context, opts ...grpc.CallOption) (PushNotification_PushMulticastClient, error)
	UnaryPushToUsers(ctx context.Context, in *PushToUsersReq, opts ...grpc.CallOption) (*PushToUsersResp, error)
	UnaryPushToUserMap(ctx context.Context, in *PushToUserMapReq, opts ...grpc.CallOption) (*PushToUserMapResp, error)
	UnaryPushMulticast(ctx context.Context, in *PushMulticastReq, opts ...grpc.CallOption) (*PushMulticastResp, error)
	UnaryPushToPresenceList(ctx context.Context, in *PushToPresenceListReq, opts ...grpc.CallOption) (*PushToUsersResp, error)
	PushRelationRegisterEvent(ctx context.Context, in *PushRelationRegisterEventReq, opts ...grpc.CallOption) (*PushRelationRegisterEventResp, error)
	BatchPushRelationRegisterEvents(ctx context.Context, in *BatchPushRelationRegisterEventReq, opts ...grpc.CallOption) (*BatchPushRelationRegisterEventResp, error)
	GetReliableProxyNotifications(ctx context.Context, in *GetReliableProxyNotificationsReq, opts ...grpc.CallOption) (*GetReliableProxyNotificationsResp, error)
	MessageReceivedAck(ctx context.Context, in *MessageReceivedAckReq, opts ...grpc.CallOption) (*MessageReceivedAckResp, error)
	KickoutPushdChannelUsers(ctx context.Context, in *KickoutPushdChannelUsersReq, opts ...grpc.CallOption) (*KickoutPushdChannelUsersResp, error)
}

type pushNotificationClient struct {
	cc *grpc.ClientConn
}

func NewPushNotificationClient(cc *grpc.ClientConn) PushNotificationClient {
	return &pushNotificationClient{cc}
}

func (c *pushNotificationClient) PushToUsers(ctx context.Context, opts ...grpc.CallOption) (PushNotification_PushToUsersClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PushNotification_serviceDesc.Streams[0], "/PushNotification.PushNotification/PushToUsers", opts...)
	if err != nil {
		return nil, err
	}
	x := &pushNotificationPushToUsersClient{stream}
	return x, nil
}

type PushNotification_PushToUsersClient interface {
	Send(*PushToUsersReq) error
	Recv() (*PushToUsersResp, error)
	grpc.ClientStream
}

type pushNotificationPushToUsersClient struct {
	grpc.ClientStream
}

func (x *pushNotificationPushToUsersClient) Send(m *PushToUsersReq) error {
	return x.ClientStream.SendMsg(m)
}

func (x *pushNotificationPushToUsersClient) Recv() (*PushToUsersResp, error) {
	m := new(PushToUsersResp)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *pushNotificationClient) PushMulticast(ctx context.Context, opts ...grpc.CallOption) (PushNotification_PushMulticastClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PushNotification_serviceDesc.Streams[1], "/PushNotification.PushNotification/PushMulticast", opts...)
	if err != nil {
		return nil, err
	}
	x := &pushNotificationPushMulticastClient{stream}
	return x, nil
}

type PushNotification_PushMulticastClient interface {
	Send(*PushMulticastReq) error
	Recv() (*PushMulticastResp, error)
	grpc.ClientStream
}

type pushNotificationPushMulticastClient struct {
	grpc.ClientStream
}

func (x *pushNotificationPushMulticastClient) Send(m *PushMulticastReq) error {
	return x.ClientStream.SendMsg(m)
}

func (x *pushNotificationPushMulticastClient) Recv() (*PushMulticastResp, error) {
	m := new(PushMulticastResp)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *pushNotificationClient) UnaryPushToUsers(ctx context.Context, in *PushToUsersReq, opts ...grpc.CallOption) (*PushToUsersResp, error) {
	out := new(PushToUsersResp)
	err := c.cc.Invoke(ctx, "/PushNotification.PushNotification/UnaryPushToUsers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationClient) UnaryPushToUserMap(ctx context.Context, in *PushToUserMapReq, opts ...grpc.CallOption) (*PushToUserMapResp, error) {
	out := new(PushToUserMapResp)
	err := c.cc.Invoke(ctx, "/PushNotification.PushNotification/UnaryPushToUserMap", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationClient) UnaryPushMulticast(ctx context.Context, in *PushMulticastReq, opts ...grpc.CallOption) (*PushMulticastResp, error) {
	out := new(PushMulticastResp)
	err := c.cc.Invoke(ctx, "/PushNotification.PushNotification/UnaryPushMulticast", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationClient) UnaryPushToPresenceList(ctx context.Context, in *PushToPresenceListReq, opts ...grpc.CallOption) (*PushToUsersResp, error) {
	out := new(PushToUsersResp)
	err := c.cc.Invoke(ctx, "/PushNotification.PushNotification/UnaryPushToPresenceList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationClient) PushRelationRegisterEvent(ctx context.Context, in *PushRelationRegisterEventReq, opts ...grpc.CallOption) (*PushRelationRegisterEventResp, error) {
	out := new(PushRelationRegisterEventResp)
	err := c.cc.Invoke(ctx, "/PushNotification.PushNotification/PushRelationRegisterEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationClient) BatchPushRelationRegisterEvents(ctx context.Context, in *BatchPushRelationRegisterEventReq, opts ...grpc.CallOption) (*BatchPushRelationRegisterEventResp, error) {
	out := new(BatchPushRelationRegisterEventResp)
	err := c.cc.Invoke(ctx, "/PushNotification.PushNotification/BatchPushRelationRegisterEvents", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationClient) GetReliableProxyNotifications(ctx context.Context, in *GetReliableProxyNotificationsReq, opts ...grpc.CallOption) (*GetReliableProxyNotificationsResp, error) {
	out := new(GetReliableProxyNotificationsResp)
	err := c.cc.Invoke(ctx, "/PushNotification.PushNotification/GetReliableProxyNotifications", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationClient) MessageReceivedAck(ctx context.Context, in *MessageReceivedAckReq, opts ...grpc.CallOption) (*MessageReceivedAckResp, error) {
	out := new(MessageReceivedAckResp)
	err := c.cc.Invoke(ctx, "/PushNotification.PushNotification/MessageReceivedAck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationClient) KickoutPushdChannelUsers(ctx context.Context, in *KickoutPushdChannelUsersReq, opts ...grpc.CallOption) (*KickoutPushdChannelUsersResp, error) {
	out := new(KickoutPushdChannelUsersResp)
	err := c.cc.Invoke(ctx, "/PushNotification.PushNotification/KickoutPushdChannelUsers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PushNotificationServer is the server API for PushNotification service.
type PushNotificationServer interface {
	PushToUsers(PushNotification_PushToUsersServer) error
	PushMulticast(PushNotification_PushMulticastServer) error
	UnaryPushToUsers(context.Context, *PushToUsersReq) (*PushToUsersResp, error)
	UnaryPushToUserMap(context.Context, *PushToUserMapReq) (*PushToUserMapResp, error)
	UnaryPushMulticast(context.Context, *PushMulticastReq) (*PushMulticastResp, error)
	UnaryPushToPresenceList(context.Context, *PushToPresenceListReq) (*PushToUsersResp, error)
	PushRelationRegisterEvent(context.Context, *PushRelationRegisterEventReq) (*PushRelationRegisterEventResp, error)
	BatchPushRelationRegisterEvents(context.Context, *BatchPushRelationRegisterEventReq) (*BatchPushRelationRegisterEventResp, error)
	GetReliableProxyNotifications(context.Context, *GetReliableProxyNotificationsReq) (*GetReliableProxyNotificationsResp, error)
	MessageReceivedAck(context.Context, *MessageReceivedAckReq) (*MessageReceivedAckResp, error)
	KickoutPushdChannelUsers(context.Context, *KickoutPushdChannelUsersReq) (*KickoutPushdChannelUsersResp, error)
}

func RegisterPushNotificationServer(s *grpc.Server, srv PushNotificationServer) {
	s.RegisterService(&_PushNotification_serviceDesc, srv)
}

func _PushNotification_PushToUsers_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(PushNotificationServer).PushToUsers(&pushNotificationPushToUsersServer{stream})
}

type PushNotification_PushToUsersServer interface {
	Send(*PushToUsersResp) error
	Recv() (*PushToUsersReq, error)
	grpc.ServerStream
}

type pushNotificationPushToUsersServer struct {
	grpc.ServerStream
}

func (x *pushNotificationPushToUsersServer) Send(m *PushToUsersResp) error {
	return x.ServerStream.SendMsg(m)
}

func (x *pushNotificationPushToUsersServer) Recv() (*PushToUsersReq, error) {
	m := new(PushToUsersReq)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _PushNotification_PushMulticast_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(PushNotificationServer).PushMulticast(&pushNotificationPushMulticastServer{stream})
}

type PushNotification_PushMulticastServer interface {
	Send(*PushMulticastResp) error
	Recv() (*PushMulticastReq, error)
	grpc.ServerStream
}

type pushNotificationPushMulticastServer struct {
	grpc.ServerStream
}

func (x *pushNotificationPushMulticastServer) Send(m *PushMulticastResp) error {
	return x.ServerStream.SendMsg(m)
}

func (x *pushNotificationPushMulticastServer) Recv() (*PushMulticastReq, error) {
	m := new(PushMulticastReq)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _PushNotification_UnaryPushToUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushToUsersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationServer).UnaryPushToUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.PushNotification/UnaryPushToUsers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationServer).UnaryPushToUsers(ctx, req.(*PushToUsersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotification_UnaryPushToUserMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushToUserMapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationServer).UnaryPushToUserMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.PushNotification/UnaryPushToUserMap",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationServer).UnaryPushToUserMap(ctx, req.(*PushToUserMapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotification_UnaryPushMulticast_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushMulticastReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationServer).UnaryPushMulticast(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.PushNotification/UnaryPushMulticast",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationServer).UnaryPushMulticast(ctx, req.(*PushMulticastReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotification_UnaryPushToPresenceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushToPresenceListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationServer).UnaryPushToPresenceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.PushNotification/UnaryPushToPresenceList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationServer).UnaryPushToPresenceList(ctx, req.(*PushToPresenceListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotification_PushRelationRegisterEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushRelationRegisterEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationServer).PushRelationRegisterEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.PushNotification/PushRelationRegisterEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationServer).PushRelationRegisterEvent(ctx, req.(*PushRelationRegisterEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotification_BatchPushRelationRegisterEvents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchPushRelationRegisterEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationServer).BatchPushRelationRegisterEvents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.PushNotification/BatchPushRelationRegisterEvents",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationServer).BatchPushRelationRegisterEvents(ctx, req.(*BatchPushRelationRegisterEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotification_GetReliableProxyNotifications_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReliableProxyNotificationsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationServer).GetReliableProxyNotifications(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.PushNotification/GetReliableProxyNotifications",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationServer).GetReliableProxyNotifications(ctx, req.(*GetReliableProxyNotificationsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotification_MessageReceivedAck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MessageReceivedAckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationServer).MessageReceivedAck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.PushNotification/MessageReceivedAck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationServer).MessageReceivedAck(ctx, req.(*MessageReceivedAckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotification_KickoutPushdChannelUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KickoutPushdChannelUsersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationServer).KickoutPushdChannelUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.PushNotification/KickoutPushdChannelUsers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationServer).KickoutPushdChannelUsers(ctx, req.(*KickoutPushdChannelUsersReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PushNotification_serviceDesc = grpc.ServiceDesc{
	ServiceName: "PushNotification.PushNotification",
	HandlerType: (*PushNotificationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UnaryPushToUsers",
			Handler:    _PushNotification_UnaryPushToUsers_Handler,
		},
		{
			MethodName: "UnaryPushToUserMap",
			Handler:    _PushNotification_UnaryPushToUserMap_Handler,
		},
		{
			MethodName: "UnaryPushMulticast",
			Handler:    _PushNotification_UnaryPushMulticast_Handler,
		},
		{
			MethodName: "UnaryPushToPresenceList",
			Handler:    _PushNotification_UnaryPushToPresenceList_Handler,
		},
		{
			MethodName: "PushRelationRegisterEvent",
			Handler:    _PushNotification_PushRelationRegisterEvent_Handler,
		},
		{
			MethodName: "BatchPushRelationRegisterEvents",
			Handler:    _PushNotification_BatchPushRelationRegisterEvents_Handler,
		},
		{
			MethodName: "GetReliableProxyNotifications",
			Handler:    _PushNotification_GetReliableProxyNotifications_Handler,
		},
		{
			MethodName: "MessageReceivedAck",
			Handler:    _PushNotification_MessageReceivedAck_Handler,
		},
		{
			MethodName: "KickoutPushdChannelUsers",
			Handler:    _PushNotification_KickoutPushdChannelUsers_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "PushToUsers",
			Handler:       _PushNotification_PushToUsers_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "PushMulticast",
			Handler:       _PushNotification_PushMulticast_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "push-notification/v2/pushnotification_v2.proto",
}

// TerminalTypePolicyServiceClient is the client API for TerminalTypePolicyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TerminalTypePolicyServiceClient interface {
	SetTerminalTypePolicy(ctx context.Context, in *TerminalTypePolicyInfo, opts ...grpc.CallOption) (*SetTerminalTypePolicyResp, error)
	GetTerminalTypePolicy(ctx context.Context, in *TerminalTypePolicy, opts ...grpc.CallOption) (*GetTerminalTypePolicyResp, error)
}

type terminalTypePolicyServiceClient struct {
	cc *grpc.ClientConn
}

func NewTerminalTypePolicyServiceClient(cc *grpc.ClientConn) TerminalTypePolicyServiceClient {
	return &terminalTypePolicyServiceClient{cc}
}

func (c *terminalTypePolicyServiceClient) SetTerminalTypePolicy(ctx context.Context, in *TerminalTypePolicyInfo, opts ...grpc.CallOption) (*SetTerminalTypePolicyResp, error) {
	out := new(SetTerminalTypePolicyResp)
	err := c.cc.Invoke(ctx, "/PushNotification.TerminalTypePolicyService/SetTerminalTypePolicy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *terminalTypePolicyServiceClient) GetTerminalTypePolicy(ctx context.Context, in *TerminalTypePolicy, opts ...grpc.CallOption) (*GetTerminalTypePolicyResp, error) {
	out := new(GetTerminalTypePolicyResp)
	err := c.cc.Invoke(ctx, "/PushNotification.TerminalTypePolicyService/GetTerminalTypePolicy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TerminalTypePolicyServiceServer is the server API for TerminalTypePolicyService service.
type TerminalTypePolicyServiceServer interface {
	SetTerminalTypePolicy(context.Context, *TerminalTypePolicyInfo) (*SetTerminalTypePolicyResp, error)
	GetTerminalTypePolicy(context.Context, *TerminalTypePolicy) (*GetTerminalTypePolicyResp, error)
}

func RegisterTerminalTypePolicyServiceServer(s *grpc.Server, srv TerminalTypePolicyServiceServer) {
	s.RegisterService(&_TerminalTypePolicyService_serviceDesc, srv)
}

func _TerminalTypePolicyService_SetTerminalTypePolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TerminalTypePolicyInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerminalTypePolicyServiceServer).SetTerminalTypePolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.TerminalTypePolicyService/SetTerminalTypePolicy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerminalTypePolicyServiceServer).SetTerminalTypePolicy(ctx, req.(*TerminalTypePolicyInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _TerminalTypePolicyService_GetTerminalTypePolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TerminalTypePolicy)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerminalTypePolicyServiceServer).GetTerminalTypePolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.TerminalTypePolicyService/GetTerminalTypePolicy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerminalTypePolicyServiceServer).GetTerminalTypePolicy(ctx, req.(*TerminalTypePolicy))
	}
	return interceptor(ctx, in, info, handler)
}

var _TerminalTypePolicyService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "PushNotification.TerminalTypePolicyService",
	HandlerType: (*TerminalTypePolicyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetTerminalTypePolicy",
			Handler:    _TerminalTypePolicyService_SetTerminalTypePolicy_Handler,
		},
		{
			MethodName: "GetTerminalTypePolicy",
			Handler:    _TerminalTypePolicyService_GetTerminalTypePolicy_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "push-notification/v2/pushnotification_v2.proto",
}

func init() {
	proto.RegisterFile("push-notification/v2/pushnotification_v2.proto", fileDescriptor_pushnotification_v2_c551c54aa2ec0695)
}

var fileDescriptor_pushnotification_v2_c551c54aa2ec0695 = []byte{
	// 2050 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x59, 0x51, 0x6f, 0x23, 0x49,
	0x11, 0xce, 0xd8, 0x89, 0x63, 0x97, 0xe3, 0xac, 0xd3, 0x5c, 0x76, 0x1d, 0xe7, 0x72, 0x97, 0x4c,
	0x58, 0xc5, 0xbb, 0xc7, 0x39, 0xe0, 0xe5, 0x04, 0xba, 0x03, 0xad, 0x9c, 0x9c, 0x37, 0x31, 0x1b,
	0x3b, 0xd9, 0xb6, 0x7d, 0xdc, 0x81, 0x60, 0x34, 0xf1, 0x74, 0x92, 0xd1, 0x8e, 0x67, 0x3a, 0xd3,
	0x63, 0xeb, 0x2c, 0x24, 0x1e, 0x91, 0x40, 0xe2, 0x1e, 0x10, 0x0f, 0x48, 0x48, 0xbc, 0xf2, 0x17,
	0xf8, 0x07, 0x48, 0x3c, 0x21, 0x21, 0x24, 0x9e, 0x79, 0xe2, 0x3f, 0xf0, 0x82, 0xba, 0x7b, 0xc6,
	0x19, 0x7b, 0x66, 0xe2, 0xe4, 0x58, 0x90, 0xee, 0xcd, 0x5d, 0x53, 0x55, 0xfd, 0x55, 0xd7, 0x57,
	0xd5, 0x35, 0x63, 0xa8, 0xd2, 0x21, 0xbb, 0x7a, 0xdf, 0x76, 0x3c, 0xf3, 0xc2, 0xec, 0xeb, 0x9e,
	0xe9, 0xd8, 0xfb, 0xa3, 0xda, 0x3e, 0x17, 0x86, 0x65, 0xda, 0xa8, 0x56, 0xa5, 0xae, 0xe3, 0x39,
	0xa8, 0x78, 0x36, 0x64, 0x57, 0xed, 0xd0, 0xa3, 0xf2, 0x5e, 0xac, 0x87, 0xfa, 0x59, 0x9b, 0xed,
	0xeb, 0xd4, 0x66, 0x06, 0xb9, 0x90, 0xa6, 0xea, 0x2f, 0xd2, 0xb0, 0x76, 0xe6, 0x3a, 0x9f, 0x8f,
	0xc3, 0xe6, 0x08, 0xc1, 0xa2, 0x37, 0xa6, 0xa4, 0xa4, 0x6c, 0x2b, 0x95, 0x02, 0x16, 0xbf, 0x51,
	0x09, 0x96, 0xa9, 0x3e, 0xb6, 0x1c, 0xdd, 0x28, 0xa5, 0xb6, 0x95, 0xca, 0x0a, 0x0e, 0x96, 0xe8,
	0x00, 0x32, 0xd4, 0xb1, 0xcc, 0xfe, 0xb8, 0x94, 0xde, 0x56, 0x2a, 0xab, 0xb5, 0xa7, 0xd5, 0x59,
	0x3c, 0xd5, 0xc8, 0x16, 0xd5, 0x33, 0x61, 0x81, 0x7d, 0x4b, 0xf4, 0x2e, 0xe4, 0xc9, 0xe7, 0xd4,
	0x74, 0x89, 0xe6, 0x99, 0x03, 0x52, 0x5a, 0x14, 0x1b, 0x83, 0x14, 0x75, 0xcd, 0x01, 0xe1, 0x90,
	0xd8, 0xd8, 0xee, 0x97, 0x96, 0xb6, 0x95, 0x4a, 0x16, 0x8b, 0xdf, 0xa8, 0x0c, 0x59, 0xea, 0x9a,
	0x8e, 0x6b, 0x7a, 0xe3, 0x52, 0x46, 0x58, 0x4c, 0xd6, 0x68, 0x13, 0x72, 0xfc, 0x0c, 0x34, 0x11,
	0x47, 0xce, 0x7f, 0x38, 0x64, 0x57, 0x5d, 0x1e, 0xcb, 0x16, 0x80, 0x78, 0x68, 0xe9, 0xe7, 0xc4,
	0x2a, 0xc1, 0xb6, 0x52, 0xc9, 0x61, 0xa1, 0x7e, 0xc2, 0x05, 0x6a, 0x0b, 0x16, 0x85, 0x5a, 0x1e,
	0x96, 0x9b, 0xed, 0x4f, 0xea, 0x27, 0xcd, 0x8f, 0x8b, 0x0b, 0x08, 0x20, 0xd3, 0x3e, 0xed, 0x36,
	0x5f, 0x7c, 0x56, 0x54, 0x50, 0x16, 0x16, 0xcf, 0x7a, 0x9d, 0xe3, 0x62, 0x0a, 0xad, 0xc3, 0x5a,
	0x17, 0xd7, 0xdb, 0x9d, 0x56, 0xb3, 0xd3, 0x69, 0x9e, 0xb6, 0x35, 0x21, 0x4e, 0x73, 0xcb, 0x97,
	0xcd, 0xc3, 0x97, 0xa7, 0xbd, 0x6e, 0x71, 0x51, 0xdd, 0x85, 0x8c, 0x8c, 0x96, 0x8b, 0x3f, 0x6e,
	0xbc, 0xa8, 0xf7, 0x4e, 0xba, 0xc5, 0x05, 0xb4, 0x02, 0x59, 0xdc, 0x38, 0x69, 0xd6, 0x0f, 0x4e,
	0x1a, 0x45, 0x45, 0xfd, 0x4b, 0x1a, 0x1e, 0x1d, 0x3a, 0x03, 0xea, 0x30, 0xd3, 0x33, 0x47, 0x64,
	0x2a, 0x1d, 0x65, 0xc8, 0x32, 0x72, 0x3d, 0x24, 0x76, 0x3f, 0x48, 0xc9, 0x64, 0x8d, 0xbe, 0x01,
	0xc8, 0x23, 0xee, 0xc0, 0xb4, 0x75, 0x4b, 0xc4, 0xaa, 0x59, 0x26, 0xf3, 0x4a, 0xa9, 0xed, 0x74,
	0xa5, 0x80, 0x8b, 0xc1, 0x13, 0x1e, 0xcd, 0x89, 0xc9, 0x3c, 0xb4, 0x0e, 0x19, 0x9d, 0x52, 0xcd,
	0x34, 0x44, 0xaa, 0x0a, 0x78, 0x49, 0xa7, 0xb4, 0x69, 0x20, 0x0c, 0x88, 0xf2, 0x0c, 0x69, 0x61,
	0xc6, 0x88, 0x24, 0xe4, 0x6b, 0xbb, 0x77, 0xc8, 0x26, 0x5e, 0xa3, 0x11, 0x0e, 0x9d, 0xc1, 0x1a,
	0xa7, 0xda, 0xb4, 0xcb, 0xa5, 0x24, 0x97, 0x9c, 0x9a, 0xd5, 0x29, 0x97, 0x45, 0x6e, 0x3d, 0xe5,
	0xf1, 0x31, 0xac, 0x0a, 0x8f, 0x17, 0x6e, 0x70, 0x18, 0x32, 0xe9, 0x05, 0x2e, 0x7d, 0x11, 0x08,
	0xd1, 0x13, 0x28, 0xf6, 0x2d, 0x93, 0xd8, 0x9e, 0xa0, 0x12, 0xf3, 0xf4, 0x01, 0x2d, 0x2d, 0x0b,
	0xc5, 0x07, 0x52, 0xde, 0x0d, 0xc4, 0xe8, 0x13, 0x78, 0x6b, 0xfa, 0xf0, 0x7c, 0x1e, 0x67, 0x05,
	0xcc, 0xaf, 0x47, 0x61, 0x76, 0x43, 0x07, 0xea, 0x33, 0x18, 0x79, 0x11, 0x99, 0xfa, 0x29, 0xa0,
	0xa8, 0x26, 0xda, 0x81, 0x15, 0xe9, 0x5f, 0xbb, 0x74, 0x9d, 0x21, 0x15, 0xa9, 0xcc, 0xe1, 0xbc,
	0x94, 0x1d, 0x71, 0x11, 0x2f, 0x03, 0x5f, 0xc5, 0xd6, 0x07, 0x44, 0x14, 0x5a, 0x0e, 0x83, 0x14,
	0xb5, 0xf5, 0x01, 0x51, 0x7f, 0xa9, 0xc0, 0xc6, 0x11, 0xf1, 0x62, 0x70, 0x10, 0x46, 0x91, 0x05,
	0xef, 0xc4, 0xc5, 0xa3, 0x99, 0xf6, 0x85, 0x23, 0x89, 0xa1, 0x6c, 0xa7, 0x2b, 0xf9, 0x5a, 0xe5,
	0x2e, 0x91, 0x35, 0xed, 0x0b, 0x07, 0x97, 0xbd, 0x58, 0x39, 0x27, 0x93, 0xfa, 0x01, 0x6c, 0x74,
	0x12, 0xa1, 0x94, 0x60, 0x99, 0x0d, 0xfb, 0x7d, 0xc2, 0x98, 0x88, 0x33, 0x8b, 0x83, 0xa5, 0xfa,
	0x07, 0x05, 0x1e, 0xc6, 0xef, 0x96, 0x98, 0x0f, 0xe5, 0xbf, 0xcb, 0xc7, 0xfd, 0x8a, 0x44, 0xfd,
	0x1e, 0x14, 0x5b, 0x43, 0xcb, 0x33, 0xfb, 0x3a, 0xf3, 0xea, 0xfd, 0xbe, 0x33, 0xb4, 0x3d, 0xb4,
	0x0a, 0x29, 0xd3, 0x10, 0x38, 0x16, 0x71, 0xca, 0x34, 0x78, 0x78, 0xba, 0x7c, 0xe4, 0x27, 0x29,
	0x58, 0xaa, 0xff, 0x54, 0xe0, 0x81, 0x28, 0x10, 0x0e, 0x16, 0x13, 0x36, 0xb4, 0x3c, 0xf4, 0x10,
	0x32, 0xae, 0xf8, 0x25, 0x3c, 0x2c, 0x61, 0x7f, 0x85, 0x1c, 0x78, 0x38, 0x64, 0xc4, 0x95, 0x35,
	0x32, 0xd6, 0x84, 0x03, 0xa6, 0x0d, 0x74, 0x2a, 0xb0, 0xe5, 0x6b, 0x1f, 0x26, 0xd4, 0xde, 0x8d,
	0xeb, 0x6a, 0x8f, 0x11, 0x57, 0x28, 0x8c, 0x0f, 0x85, 0x75, 0x4b, 0xa7, 0x0d, 0xdb, 0x73, 0xc7,
	0xf8, 0x6b, 0xc3, 0xe8, 0x93, 0xf2, 0x0b, 0x28, 0x25, 0x19, 0xa0, 0x22, 0xa4, 0x5f, 0x93, 0xb1,
	0xdf, 0x60, 0xf8, 0x4f, 0xf4, 0x16, 0x2c, 0x8d, 0x74, 0x6b, 0x28, 0x79, 0x58, 0xc0, 0x72, 0xf1,
	0x61, 0xea, 0xbb, 0x8a, 0xfa, 0x77, 0x05, 0xc4, 0xa5, 0xd3, 0x75, 0xb8, 0xbb, 0x96, 0x4e, 0x31,
	0xb9, 0x46, 0x47, 0xb0, 0x3c, 0x34, 0x0d, 0x01, 0x5f, 0xd2, 0xac, 0x1a, 0x03, 0x7f, 0xc6, 0xa8,
	0xda, 0x33, 0x8d, 0x09, 0xe4, 0xcc, 0x50, 0x2c, 0x26, 0xbd, 0x3e, 0x75, 0xd3, 0xeb, 0xcb, 0x06,
	0xe4, 0x43, 0xaa, 0x31, 0x60, 0x9f, 0x87, 0xc1, 0xe6, 0x6b, 0x4f, 0xa2, 0x7b, 0x27, 0xb4, 0xd7,
	0x70, 0x5c, 0xbf, 0x4e, 0xc1, 0xda, 0x0c, 0x44, 0x46, 0x51, 0x6b, 0xaa, 0xff, 0xf2, 0xc8, 0xbe,
	0x35, 0x37, 0x32, 0x46, 0xab, 0x1d, 0xdf, 0x46, 0x06, 0x77, 0xd3, 0xb2, 0x5b, 0x20, 0xdb, 0xa5,
	0x26, 0xee, 0x20, 0x9f, 0x18, 0x12, 0xf5, 0xce, 0xdc, 0x84, 0xe3, 0x07, 0x74, 0x86, 0x5c, 0x5b,
	0x00, 0xa2, 0xf7, 0x31, 0x2f, 0xe8, 0xeb, 0x39, 0x9c, 0xf3, 0x25, 0x4d, 0xa3, 0xfc, 0x11, 0x14,
	0xa6, 0x80, 0xdc, 0x2b, 0xcf, 0x5f, 0x28, 0xb0, 0x7a, 0x13, 0x18, 0xe3, 0x59, 0xde, 0x80, 0x2c,
	0xcf, 0xf2, 0xa4, 0x9b, 0x14, 0x30, 0xcf, 0xba, 0xb8, 0x5d, 0x5a, 0xb0, 0x32, 0xd5, 0xed, 0xef,
	0x9d, 0x89, 0x29, 0xf3, 0x09, 0x0d, 0xd2, 0x37, 0x34, 0x50, 0xff, 0x96, 0x92, 0xc4, 0x9b, 0x14,
	0x28, 0x87, 0x74, 0x0a, 0x6b, 0x83, 0x60, 0xad, 0x05, 0x65, 0x29, 0x7b, 0x86, 0x1a, 0xdd, 0x7c,
	0xb6, 0xb6, 0x71, 0x71, 0x30, 0x5b, 0xed, 0x6f, 0x38, 0x90, 0x4d, 0xc8, 0xb1, 0xd7, 0x26, 0xd5,
	0x86, 0xa6, 0xc1, 0x4a, 0x69, 0x71, 0x66, 0x59, 0x2e, 0xe8, 0x99, 0x06, 0x43, 0xaf, 0x00, 0x45,
	0xc0, 0xb3, 0xd2, 0xa2, 0xa0, 0xd9, 0x5d, 0xd0, 0xaf, 0xcd, 0xa2, 0x67, 0xb1, 0xb3, 0xd2, 0x26,
	0xe4, 0x1c, 0xdb, 0x1a, 0x4b, 0x0c, 0x19, 0x89, 0x81, 0x0b, 0x38, 0x06, 0xf5, 0xf7, 0xbc, 0x67,
	0x85, 0xd3, 0xcc, 0xe8, 0xad, 0x43, 0xc7, 0xff, 0x95, 0xc1, 0x6a, 0x5b, 0xd6, 0x64, 0x28, 0xe5,
	0x73, 0xe0, 0x4d, 0xfb, 0x4b, 0xcd, 0xfa, 0xfb, 0x9d, 0x02, 0xd9, 0x33, 0x97, 0x30, 0xa1, 0xbb,
	0xc1, 0x67, 0x48, 0x1e, 0x8a, 0x49, 0x7d, 0x3f, 0xcb, 0x62, 0xdd, 0xa4, 0x62, 0x4a, 0x94, 0x51,
	0x3a, 0xae, 0xe7, 0xd7, 0x46, 0x4e, 0x62, 0x77, 0x5c, 0x8f, 0xd7, 0xd1, 0x70, 0x32, 0x48, 0xf1,
	0x9f, 0xfc, 0x8c, 0xfd, 0xc9, 0xc3, 0x34, 0xfc, 0x11, 0x36, 0x2b, 0x05, 0x4d, 0x03, 0xed, 0x42,
	0x61, 0xea, 0x0e, 0x12, 0xd9, 0x29, 0xe0, 0x95, 0xf0, 0xf5, 0xa3, 0x7e, 0x07, 0x72, 0x01, 0x32,
	0x86, 0x9e, 0x42, 0x8a, 0x32, 0xbf, 0xe1, 0x94, 0xe3, 0x8e, 0x55, 0x2a, 0xe2, 0x14, 0x65, 0xea,
	0x1f, 0x15, 0x58, 0x97, 0x19, 0x0c, 0xc4, 0xbc, 0x22, 0x79, 0x71, 0x3c, 0x87, 0x02, 0xf5, 0x45,
	0xe1, 0x11, 0xe0, 0x36, 0x87, 0x2b, 0x34, 0xe4, 0xe3, 0x0d, 0x17, 0x83, 0xfa, 0x27, 0x05, 0x1e,
	0xc9, 0xdc, 0x5b, 0x42, 0xd0, 0x18, 0x11, 0xdb, 0xf3, 0x89, 0xd0, 0xf2, 0x87, 0x06, 0x62, 0xf8,
	0xe5, 0x9b, 0xd0, 0x67, 0x63, 0x6c, 0xab, 0x1d, 0x69, 0x78, 0xbc, 0x80, 0x03, 0x1f, 0xe8, 0x21,
	0x2c, 0x11, 0xd7, 0x75, 0x5c, 0x49, 0x81, 0xe3, 0x05, 0x2c, 0x97, 0xe5, 0x2a, 0x2c, 0xfb, 0xda,
	0x3c, 0x2b, 0x06, 0xb1, 0xcc, 0x11, 0x71, 0x35, 0xdd, 0x30, 0x5c, 0x79, 0xdc, 0x39, 0xbc, 0xe2,
	0x0b, 0xeb, 0x5c, 0x76, 0x90, 0x91, 0xaf, 0x43, 0xea, 0x6f, 0x52, 0xb0, 0x1e, 0x6c, 0x8d, 0xc9,
	0xa5, 0xc9, 0x3c, 0xe2, 0x0a, 0x08, 0xe8, 0x15, 0xac, 0xba, 0xe4, 0x52, 0x23, 0x23, 0x31, 0x76,
	0x06, 0xaf, 0x4e, 0xab, 0xb5, 0xf7, 0xa2, 0xf8, 0x63, 0x1d, 0xf0, 0xe4, 0xe3, 0x15, 0x97, 0x5c,
	0x4e, 0x56, 0x01, 0xbd, 0x52, 0x37, 0xf4, 0x2a, 0x43, 0xb6, 0x7f, 0xa5, 0xdb, 0x36, 0xb1, 0x26,
	0x5d, 0x24, 0x58, 0x47, 0xd9, 0xb5, 0x18, 0x65, 0xd7, 0x34, 0x3f, 0x97, 0x66, 0xf8, 0xb9, 0x09,
	0x39, 0x09, 0x9f, 0x91, 0x6b, 0x31, 0x58, 0x2f, 0xe2, 0xac, 0x10, 0x74, 0x64, 0xd3, 0x37, 0x88,
	0xa5, 0x8f, 0xb5, 0x01, 0xf3, 0x67, 0xe9, 0x65, 0xb1, 0x6e, 0x31, 0xf5, 0x27, 0xf0, 0x76, 0x38,
	0x25, 0x53, 0x61, 0x71, 0xfe, 0x7d, 0x1f, 0x96, 0x84, 0x1b, 0x3f, 0xa3, 0x7b, 0x77, 0x3c, 0x11,
	0x2c, 0xad, 0xd4, 0x73, 0xd8, 0xba, 0xc5, 0x3d, 0xa3, 0xa8, 0x3e, 0x35, 0x5b, 0xc5, 0x12, 0x33,
	0x81, 0x32, 0xc1, 0x18, 0xa6, 0x1a, 0xb0, 0x73, 0xa0, 0x7b, 0xfd, 0xab, 0x5b, 0xe3, 0x78, 0x0e,
	0x19, 0x81, 0x28, 0xa8, 0xc8, 0x3b, 0x07, 0xe2, 0x9b, 0xa9, 0xff, 0x52, 0x40, 0x9d, 0xb7, 0x0d,
	0xa3, 0xe8, 0xc7, 0xb0, 0x2c, 0x61, 0x31, 0x7f, 0x08, 0xac, 0x47, 0x37, 0x9a, 0xef, 0xa6, 0x2a,
	0x43, 0x64, 0x72, 0xf6, 0x08, 0x3c, 0x96, 0x09, 0xac, 0x84, 0x1f, 0x7c, 0xa9, 0x31, 0x2a, 0xe9,
	0x34, 0x43, 0x63, 0xc3, 0x6f, 0x15, 0xd8, 0x3e, 0x22, 0x1e, 0x26, 0x96, 0xa9, 0x9f, 0x5b, 0x24,
	0xf2, 0xbe, 0x28, 0x06, 0x09, 0x9f, 0xe0, 0xca, 0x0d, 0xc1, 0x1f, 0xc3, 0x6a, 0xd0, 0xc3, 0xb5,
	0x73, 0x72, 0x69, 0xda, 0x3e, 0xfb, 0x0b, 0x81, 0xf4, 0x80, 0x0b, 0xf9, 0x7b, 0xd4, 0x44, 0x8d,
	0xd8, 0x41, 0x07, 0xce, 0xb3, 0xc9, 0x94, 0x63, 0xf0, 0x89, 0x46, 0x4e, 0x01, 0xb2, 0x0c, 0xe4,
	0x42, 0xfd, 0xab, 0x02, 0x1b, 0x89, 0x98, 0xbe, 0x92, 0x6f, 0xd9, 0xea, 0x08, 0x76, 0xe6, 0x1c,
	0x34, 0xa3, 0xe8, 0x15, 0x14, 0xc2, 0x5b, 0x06, 0x0c, 0x8e, 0x6f, 0x4e, 0xf1, 0x8e, 0xf0, 0xb4,
	0x07, 0xb5, 0x0d, 0xeb, 0x2d, 0xc2, 0x98, 0x7e, 0x49, 0x30, 0xe9, 0x13, 0x73, 0x44, 0x8c, 0x7a,
	0xff, 0x75, 0x7c, 0x56, 0x77, 0x61, 0x92, 0xbf, 0xf0, 0xb1, 0x4d, 0x72, 0x28, 0xde, 0xb9, 0x4a,
	0xf0, 0x30, 0xce, 0x1f, 0xa3, 0xea, 0x3e, 0xe4, 0x1a, 0xe2, 0x33, 0xd0, 0x4b, 0x32, 0x8e, 0xf1,
	0x5e, 0x84, 0x34, 0x6f, 0x58, 0x7e, 0x9b, 0x64, 0xe4, 0x5a, 0xfd, 0x21, 0x6c, 0xbe, 0x34, 0xfb,
	0xaf, 0x9d, 0xa1, 0xc7, 0xc3, 0x33, 0x0e, 0x65, 0x8b, 0x9c, 0xcc, 0xaf, 0x5b, 0x00, 0x7e, 0xd7,
	0xd4, 0x26, 0x9e, 0x72, 0xbe, 0xa4, 0x69, 0x4c, 0x8d, 0xb7, 0xa9, 0xa9, 0xf1, 0x56, 0x7d, 0x07,
	0xde, 0x4e, 0x76, 0xcc, 0xe8, 0xd3, 0x3f, 0x4b, 0x7a, 0xc5, 0x77, 0x77, 0xf4, 0x04, 0x1e, 0x37,
	0xda, 0xbd, 0x96, 0x76, 0x78, 0x5c, 0x6f, 0xb7, 0x1b, 0x27, 0x1a, 0x6e, 0x9c, 0xd4, 0xbb, 0xcd,
	0xd3, 0xb6, 0x86, 0x1b, 0x47, 0x8d, 0x76, 0xa3, 0xdd, 0xe5, 0x3f, 0x9a, 0x9d, 0x6e, 0x71, 0x01,
	0xbd, 0x07, 0x7b, 0x73, 0x54, 0x7b, 0x6d, 0x5f, 0x59, 0x41, 0x7b, 0xb0, 0x2b, 0x94, 0x8f, 0xf0,
	0x69, 0xef, 0x2c, 0xd9, 0x6b, 0x6a, 0x02, 0x20, 0x49, 0x71, 0xe2, 0x33, 0x5d, 0xfb, 0x47, 0x0e,
	0x22, 0xdf, 0x14, 0xd1, 0xa7, 0x90, 0x0f, 0xcd, 0x88, 0x68, 0xfb, 0xb6, 0x57, 0x20, 0x7e, 0xd2,
	0xe5, 0x9d, 0x39, 0x1a, 0x8c, 0xaa, 0x0b, 0x15, 0xe5, 0x9b, 0x0a, 0xfa, 0x29, 0x14, 0xa6, 0x06,
	0x3c, 0xa4, 0xc6, 0x5b, 0x86, 0x87, 0xfe, 0xf2, 0xee, 0x5c, 0x9d, 0x89, 0xff, 0xcf, 0xa0, 0xd8,
	0xb3, 0x75, 0x77, 0xfc, 0xe6, 0xe1, 0x23, 0x0d, 0xd0, 0x8c, 0x6b, 0xfe, 0x02, 0xab, 0xce, 0x7f,
	0xf1, 0x4d, 0xc2, 0x3f, 0xf5, 0x0a, 0x39, 0xb3, 0xc1, 0xff, 0xe2, 0x80, 0xd0, 0x25, 0x3c, 0x0a,
	0x45, 0x10, 0x9e, 0x1e, 0xd1, 0x5e, 0x12, 0xc4, 0x99, 0x19, 0xf3, 0x6e, 0x47, 0xf5, 0x73, 0xd8,
	0x48, 0xbc, 0xb2, 0x50, 0xf5, 0xf6, 0x7b, 0x66, 0xf6, 0x36, 0x2e, 0xef, 0xdf, 0x4b, 0x5f, 0xec,
	0xff, 0x85, 0x02, 0xef, 0xde, 0x7e, 0x71, 0x32, 0xf4, 0xec, 0xfe, 0x77, 0xed, 0x75, 0xf9, 0xdb,
	0x5f, 0xe6, 0x82, 0x56, 0x17, 0xd0, 0xaf, 0x14, 0xd8, 0xba, 0xb5, 0x79, 0xa3, 0x5a, 0xd4, 0xf3,
	0xbc, 0x6b, 0xb5, 0xfc, 0xec, 0xde, 0x36, 0x02, 0x8c, 0x09, 0x28, 0xda, 0x80, 0xe3, 0x18, 0x10,
	0xdb, 0xf6, 0xcb, 0x95, 0xbb, 0x29, 0x8a, 0xad, 0x7e, 0x06, 0xa5, 0xa4, 0x3e, 0x8a, 0xde, 0x8f,
	0xfa, 0xb9, 0xa5, 0x99, 0x97, 0xab, 0xf7, 0x51, 0xe7, 0x9b, 0xd7, 0xfe, 0xad, 0xc0, 0x46, 0xf4,
	0xab, 0x61, 0x87, 0xb8, 0x23, 0xb3, 0x4f, 0x10, 0x85, 0xf5, 0xd8, 0x4f, 0x9a, 0xe8, 0xce, 0x5f,
	0x4c, 0xcb, 0x31, 0xb7, 0x6a, 0xe2, 0x57, 0x52, 0x75, 0x01, 0x59, 0xb0, 0x1e, 0xfb, 0x3d, 0x17,
	0xdd, 0xe9, 0x6b, 0x67, 0xdc, 0x6e, 0x47, 0xc9, 0xbb, 0x1d, 0xfc, 0xe0, 0x47, 0xc7, 0x97, 0x8e,
	0xa5, 0xdb, 0x97, 0xd5, 0x0f, 0x6a, 0x9e, 0x57, 0xed, 0x3b, 0x83, 0x7d, 0xf1, 0x3f, 0x50, 0xdf,
	0xb1, 0xf6, 0x99, 0x3c, 0x03, 0xb6, 0x1f, 0xf7, 0xdf, 0xd1, 0x47, 0xe2, 0x4d, 0x3f, 0x2c, 0x3c,
	0xcf, 0x08, 0xcb, 0x67, 0xff, 0x09, 0x00, 0x00, 0xff, 0xff, 0x2d, 0x60, 0xbb, 0xcb, 0xae, 0x1a,
	0x00, 0x00,
}
