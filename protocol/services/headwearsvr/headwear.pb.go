// Code generated by protoc-gen-gogo.
// source: src/headwearsvr/headwear.proto
// DO NOT EDIT!

/*
	Package headwear is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/headwearsvr/headwear.proto

	It has these top-level messages:
		HeadwearConfig
		UserHeadwearInfo
		GetUserHeadwearReq
		GetUserHeadwearResp
		GetUserHeadwearBySuiteReq
		GetHeadwearConfigReq
		GetHeadwearConfigResp
		SetUserHeadwearInUseReq
		GetUserHeadwearInUseReq
		RemoveUserHeadwearInUseReq
		GetUserHeadwearInUseResp
		GetUserHeadwearInUseListReq
		GetUserHeadwearInUseListResp
		GiveHeadweartoUserReq
		HeadwearConfigReq
		GetHeadwearConfigAllResp
		AddUserHeadwearExtraTimeReq
*/
package headwear

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type HEADWEAR_TYPE int32

const (
	HEADWEAR_TYPE_HEADWEAR_TYPE_COMMON HEADWEAR_TYPE = 0
	HEADWEAR_TYPE_HEADWEAR_TYPE_CP     HEADWEAR_TYPE = 1
)

var HEADWEAR_TYPE_name = map[int32]string{
	0: "HEADWEAR_TYPE_COMMON",
	1: "HEADWEAR_TYPE_CP",
}
var HEADWEAR_TYPE_value = map[string]int32{
	"HEADWEAR_TYPE_COMMON": 0,
	"HEADWEAR_TYPE_CP":     1,
}

func (x HEADWEAR_TYPE) String() string {
	return proto.EnumName(HEADWEAR_TYPE_name, int32(x))
}
func (HEADWEAR_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorHeadwear, []int{0} }

type GIVE_TYPE int32

const (
	GIVE_TYPE_GIVE_TYPE_NONE   GIVE_TYPE = 0
	GIVE_TYPE_GIVE_TYPE_NEW    GIVE_TYPE = 1
	GIVE_TYPE_GIVE_TYPE_APPEND GIVE_TYPE = 2
)

var GIVE_TYPE_name = map[int32]string{
	0: "GIVE_TYPE_NONE",
	1: "GIVE_TYPE_NEW",
	2: "GIVE_TYPE_APPEND",
}
var GIVE_TYPE_value = map[string]int32{
	"GIVE_TYPE_NONE":   0,
	"GIVE_TYPE_NEW":    1,
	"GIVE_TYPE_APPEND": 2,
}

func (x GIVE_TYPE) String() string {
	return proto.EnumName(GIVE_TYPE_name, int32(x))
}
func (GIVE_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorHeadwear, []int{1} }

type HeadwearConfig struct {
	HeadwearId    uint32 `protobuf:"varint,1,opt,name=headwear_id,json=headwearId,proto3" json:"headwear_id,omitempty"`
	SuiteId       uint32 `protobuf:"varint,2,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Img           string `protobuf:"bytes,4,opt,name=img,proto3" json:"img,omitempty"`
	Level         uint32 `protobuf:"varint,5,opt,name=level,proto3" json:"level,omitempty"`
	HoldingTime   uint32 `protobuf:"varint,6,opt,name=holding_time,json=holdingTime,proto3" json:"holding_time,omitempty"`
	StaticImg     string `protobuf:"bytes,7,opt,name=static_img,json=staticImg,proto3" json:"static_img,omitempty"`
	GifImg        string `protobuf:"bytes,8,opt,name=gif_img,json=gifImg,proto3" json:"gif_img,omitempty"`
	HeadwearType  uint32 `protobuf:"varint,9,opt,name=headwear_type,json=headwearType,proto3" json:"headwear_type,omitempty"`
	CpHeadwearImg string `protobuf:"bytes,10,opt,name=cp_headwear_img,json=cpHeadwearImg,proto3" json:"cp_headwear_img,omitempty"`
}

func (m *HeadwearConfig) Reset()                    { *m = HeadwearConfig{} }
func (m *HeadwearConfig) String() string            { return proto.CompactTextString(m) }
func (*HeadwearConfig) ProtoMessage()               {}
func (*HeadwearConfig) Descriptor() ([]byte, []int) { return fileDescriptorHeadwear, []int{0} }

func (m *HeadwearConfig) GetHeadwearId() uint32 {
	if m != nil {
		return m.HeadwearId
	}
	return 0
}

func (m *HeadwearConfig) GetSuiteId() uint32 {
	if m != nil {
		return m.SuiteId
	}
	return 0
}

func (m *HeadwearConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *HeadwearConfig) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *HeadwearConfig) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *HeadwearConfig) GetHoldingTime() uint32 {
	if m != nil {
		return m.HoldingTime
	}
	return 0
}

func (m *HeadwearConfig) GetStaticImg() string {
	if m != nil {
		return m.StaticImg
	}
	return ""
}

func (m *HeadwearConfig) GetGifImg() string {
	if m != nil {
		return m.GifImg
	}
	return ""
}

func (m *HeadwearConfig) GetHeadwearType() uint32 {
	if m != nil {
		return m.HeadwearType
	}
	return 0
}

func (m *HeadwearConfig) GetCpHeadwearImg() string {
	if m != nil {
		return m.CpHeadwearImg
	}
	return ""
}

type UserHeadwearInfo struct {
	Uid            uint32          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	HeadwearId     uint32          `protobuf:"varint,2,opt,name=headwear_id,json=headwearId,proto3" json:"headwear_id,omitempty"`
	ExpireTime     uint32          `protobuf:"varint,3,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	ExtraTime      uint32          `protobuf:"varint,4,opt,name=extra_time,json=extraTime,proto3" json:"extra_time,omitempty"`
	HeadwearConfig *HeadwearConfig `protobuf:"bytes,5,opt,name=headwear_config,json=headwearConfig" json:"headwear_config,omitempty"`
	CpUid          uint32          `protobuf:"varint,6,opt,name=cp_uid,json=cpUid,proto3" json:"cp_uid,omitempty"`
}

func (m *UserHeadwearInfo) Reset()                    { *m = UserHeadwearInfo{} }
func (m *UserHeadwearInfo) String() string            { return proto.CompactTextString(m) }
func (*UserHeadwearInfo) ProtoMessage()               {}
func (*UserHeadwearInfo) Descriptor() ([]byte, []int) { return fileDescriptorHeadwear, []int{1} }

func (m *UserHeadwearInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserHeadwearInfo) GetHeadwearId() uint32 {
	if m != nil {
		return m.HeadwearId
	}
	return 0
}

func (m *UserHeadwearInfo) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *UserHeadwearInfo) GetExtraTime() uint32 {
	if m != nil {
		return m.ExtraTime
	}
	return 0
}

func (m *UserHeadwearInfo) GetHeadwearConfig() *HeadwearConfig {
	if m != nil {
		return m.HeadwearConfig
	}
	return nil
}

func (m *UserHeadwearInfo) GetCpUid() uint32 {
	if m != nil {
		return m.CpUid
	}
	return 0
}

type GetUserHeadwearReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (m *GetUserHeadwearReq) Reset()                    { *m = GetUserHeadwearReq{} }
func (m *GetUserHeadwearReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserHeadwearReq) ProtoMessage()               {}
func (*GetUserHeadwearReq) Descriptor() ([]byte, []int) { return fileDescriptorHeadwear, []int{2} }

func (m *GetUserHeadwearReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserHeadwearResp struct {
	HeadwearList []*UserHeadwearInfo `protobuf:"bytes,1,rep,name=headwear_list,json=headwearList" json:"headwear_list,omitempty"`
}

func (m *GetUserHeadwearResp) Reset()                    { *m = GetUserHeadwearResp{} }
func (m *GetUserHeadwearResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserHeadwearResp) ProtoMessage()               {}
func (*GetUserHeadwearResp) Descriptor() ([]byte, []int) { return fileDescriptorHeadwear, []int{3} }

func (m *GetUserHeadwearResp) GetHeadwearList() []*UserHeadwearInfo {
	if m != nil {
		return m.HeadwearList
	}
	return nil
}

type GetUserHeadwearBySuiteReq struct {
	Uid     uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SuiteId uint32 `protobuf:"varint,2,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`
	CpUid   uint32 `protobuf:"varint,3,opt,name=cp_uid,json=cpUid,proto3" json:"cp_uid,omitempty"`
}

func (m *GetUserHeadwearBySuiteReq) Reset()         { *m = GetUserHeadwearBySuiteReq{} }
func (m *GetUserHeadwearBySuiteReq) String() string { return proto.CompactTextString(m) }
func (*GetUserHeadwearBySuiteReq) ProtoMessage()    {}
func (*GetUserHeadwearBySuiteReq) Descriptor() ([]byte, []int) {
	return fileDescriptorHeadwear, []int{4}
}

func (m *GetUserHeadwearBySuiteReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserHeadwearBySuiteReq) GetSuiteId() uint32 {
	if m != nil {
		return m.SuiteId
	}
	return 0
}

func (m *GetUserHeadwearBySuiteReq) GetCpUid() uint32 {
	if m != nil {
		return m.CpUid
	}
	return 0
}

type GetHeadwearConfigReq struct {
	IdList []uint32 `protobuf:"varint,1,rep,packed,name=id_list,json=idList" json:"id_list,omitempty"`
}

func (m *GetHeadwearConfigReq) Reset()                    { *m = GetHeadwearConfigReq{} }
func (m *GetHeadwearConfigReq) String() string            { return proto.CompactTextString(m) }
func (*GetHeadwearConfigReq) ProtoMessage()               {}
func (*GetHeadwearConfigReq) Descriptor() ([]byte, []int) { return fileDescriptorHeadwear, []int{5} }

func (m *GetHeadwearConfigReq) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

type GetHeadwearConfigResp struct {
	HeadwearList []*HeadwearConfig `protobuf:"bytes,1,rep,name=headwear_list,json=headwearList" json:"headwear_list,omitempty"`
}

func (m *GetHeadwearConfigResp) Reset()                    { *m = GetHeadwearConfigResp{} }
func (m *GetHeadwearConfigResp) String() string            { return proto.CompactTextString(m) }
func (*GetHeadwearConfigResp) ProtoMessage()               {}
func (*GetHeadwearConfigResp) Descriptor() ([]byte, []int) { return fileDescriptorHeadwear, []int{6} }

func (m *GetHeadwearConfigResp) GetHeadwearList() []*HeadwearConfig {
	if m != nil {
		return m.HeadwearList
	}
	return nil
}

type SetUserHeadwearInUseReq struct {
	Uid        uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	HeadwearId uint32 `protobuf:"varint,2,opt,name=headwear_id,json=headwearId,proto3" json:"headwear_id,omitempty"`
	CpUid      uint32 `protobuf:"varint,3,opt,name=cp_uid,json=cpUid,proto3" json:"cp_uid,omitempty"`
}

func (m *SetUserHeadwearInUseReq) Reset()                    { *m = SetUserHeadwearInUseReq{} }
func (m *SetUserHeadwearInUseReq) String() string            { return proto.CompactTextString(m) }
func (*SetUserHeadwearInUseReq) ProtoMessage()               {}
func (*SetUserHeadwearInUseReq) Descriptor() ([]byte, []int) { return fileDescriptorHeadwear, []int{7} }

func (m *SetUserHeadwearInUseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserHeadwearInUseReq) GetHeadwearId() uint32 {
	if m != nil {
		return m.HeadwearId
	}
	return 0
}

func (m *SetUserHeadwearInUseReq) GetCpUid() uint32 {
	if m != nil {
		return m.CpUid
	}
	return 0
}

type GetUserHeadwearInUseReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (m *GetUserHeadwearInUseReq) Reset()                    { *m = GetUserHeadwearInUseReq{} }
func (m *GetUserHeadwearInUseReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserHeadwearInUseReq) ProtoMessage()               {}
func (*GetUserHeadwearInUseReq) Descriptor() ([]byte, []int) { return fileDescriptorHeadwear, []int{8} }

func (m *GetUserHeadwearInUseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type RemoveUserHeadwearInUseReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (m *RemoveUserHeadwearInUseReq) Reset()         { *m = RemoveUserHeadwearInUseReq{} }
func (m *RemoveUserHeadwearInUseReq) String() string { return proto.CompactTextString(m) }
func (*RemoveUserHeadwearInUseReq) ProtoMessage()    {}
func (*RemoveUserHeadwearInUseReq) Descriptor() ([]byte, []int) {
	return fileDescriptorHeadwear, []int{9}
}

func (m *RemoveUserHeadwearInUseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserHeadwearInUseResp struct {
	HeadwearId uint32 `protobuf:"varint,1,opt,name=headwear_id,json=headwearId,proto3" json:"headwear_id,omitempty"`
	CpUid      uint32 `protobuf:"varint,2,opt,name=cp_uid,json=cpUid,proto3" json:"cp_uid,omitempty"`
}

func (m *GetUserHeadwearInUseResp) Reset()         { *m = GetUserHeadwearInUseResp{} }
func (m *GetUserHeadwearInUseResp) String() string { return proto.CompactTextString(m) }
func (*GetUserHeadwearInUseResp) ProtoMessage()    {}
func (*GetUserHeadwearInUseResp) Descriptor() ([]byte, []int) {
	return fileDescriptorHeadwear, []int{10}
}

func (m *GetUserHeadwearInUseResp) GetHeadwearId() uint32 {
	if m != nil {
		return m.HeadwearId
	}
	return 0
}

func (m *GetUserHeadwearInUseResp) GetCpUid() uint32 {
	if m != nil {
		return m.CpUid
	}
	return 0
}

type GetUserHeadwearInUseListReq struct {
	Uids []uint32 `protobuf:"varint,1,rep,packed,name=uids" json:"uids,omitempty"`
}

func (m *GetUserHeadwearInUseListReq) Reset()         { *m = GetUserHeadwearInUseListReq{} }
func (m *GetUserHeadwearInUseListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserHeadwearInUseListReq) ProtoMessage()    {}
func (*GetUserHeadwearInUseListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorHeadwear, []int{11}
}

func (m *GetUserHeadwearInUseListReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type GetUserHeadwearInUseListResp struct {
	HeadwearList []*UserHeadwearInfo `protobuf:"bytes,1,rep,name=headwear_list,json=headwearList" json:"headwear_list,omitempty"`
}

func (m *GetUserHeadwearInUseListResp) Reset()         { *m = GetUserHeadwearInUseListResp{} }
func (m *GetUserHeadwearInUseListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserHeadwearInUseListResp) ProtoMessage()    {}
func (*GetUserHeadwearInUseListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorHeadwear, []int{12}
}

func (m *GetUserHeadwearInUseListResp) GetHeadwearList() []*UserHeadwearInfo {
	if m != nil {
		return m.HeadwearList
	}
	return nil
}

type GiveHeadweartoUserReq struct {
	Uid               uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SuiteId           uint32 `protobuf:"varint,2,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`
	Level             uint32 `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	GiveType          uint32 `protobuf:"varint,4,opt,name=give_type,json=giveType,proto3" json:"give_type,omitempty"`
	ExpireTime        uint32 `protobuf:"varint,5,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	ExpireTimeRel     uint32 `protobuf:"varint,6,opt,name=expire_time_rel,json=expireTimeRel,proto3" json:"expire_time_rel,omitempty"`
	CpUid             uint32 `protobuf:"varint,7,opt,name=cp_uid,json=cpUid,proto3" json:"cp_uid,omitempty"`
	OrderId           string `protobuf:"bytes,8,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	PushmsgTextPrefix string `protobuf:"bytes,9,opt,name=pushmsg_text_prefix,json=pushmsgTextPrefix,proto3" json:"pushmsg_text_prefix,omitempty"`
}

func (m *GiveHeadweartoUserReq) Reset()                    { *m = GiveHeadweartoUserReq{} }
func (m *GiveHeadweartoUserReq) String() string            { return proto.CompactTextString(m) }
func (*GiveHeadweartoUserReq) ProtoMessage()               {}
func (*GiveHeadweartoUserReq) Descriptor() ([]byte, []int) { return fileDescriptorHeadwear, []int{13} }

func (m *GiveHeadweartoUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GiveHeadweartoUserReq) GetSuiteId() uint32 {
	if m != nil {
		return m.SuiteId
	}
	return 0
}

func (m *GiveHeadweartoUserReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GiveHeadweartoUserReq) GetGiveType() uint32 {
	if m != nil {
		return m.GiveType
	}
	return 0
}

func (m *GiveHeadweartoUserReq) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *GiveHeadweartoUserReq) GetExpireTimeRel() uint32 {
	if m != nil {
		return m.ExpireTimeRel
	}
	return 0
}

func (m *GiveHeadweartoUserReq) GetCpUid() uint32 {
	if m != nil {
		return m.CpUid
	}
	return 0
}

func (m *GiveHeadweartoUserReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *GiveHeadweartoUserReq) GetPushmsgTextPrefix() string {
	if m != nil {
		return m.PushmsgTextPrefix
	}
	return ""
}

type HeadwearConfigReq struct {
	HeadwearId uint32 `protobuf:"varint,1,opt,name=headwear_id,json=headwearId,proto3" json:"headwear_id,omitempty"`
}

func (m *HeadwearConfigReq) Reset()                    { *m = HeadwearConfigReq{} }
func (m *HeadwearConfigReq) String() string            { return proto.CompactTextString(m) }
func (*HeadwearConfigReq) ProtoMessage()               {}
func (*HeadwearConfigReq) Descriptor() ([]byte, []int) { return fileDescriptorHeadwear, []int{14} }

func (m *HeadwearConfigReq) GetHeadwearId() uint32 {
	if m != nil {
		return m.HeadwearId
	}
	return 0
}

type GetHeadwearConfigAllResp struct {
	ConfigList []*HeadwearConfig `protobuf:"bytes,1,rep,name=config_list,json=configList" json:"config_list,omitempty"`
}

func (m *GetHeadwearConfigAllResp) Reset()         { *m = GetHeadwearConfigAllResp{} }
func (m *GetHeadwearConfigAllResp) String() string { return proto.CompactTextString(m) }
func (*GetHeadwearConfigAllResp) ProtoMessage()    {}
func (*GetHeadwearConfigAllResp) Descriptor() ([]byte, []int) {
	return fileDescriptorHeadwear, []int{15}
}

func (m *GetHeadwearConfigAllResp) GetConfigList() []*HeadwearConfig {
	if m != nil {
		return m.ConfigList
	}
	return nil
}

type AddUserHeadwearExtraTimeReq struct {
	Uid       uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ExtraTime uint32   `protobuf:"varint,2,opt,name=extra_time,json=extraTime,proto3" json:"extra_time,omitempty"`
	SuiteIds  []uint32 `protobuf:"varint,3,rep,packed,name=suite_ids,json=suiteIds" json:"suite_ids,omitempty"`
}

func (m *AddUserHeadwearExtraTimeReq) Reset()         { *m = AddUserHeadwearExtraTimeReq{} }
func (m *AddUserHeadwearExtraTimeReq) String() string { return proto.CompactTextString(m) }
func (*AddUserHeadwearExtraTimeReq) ProtoMessage()    {}
func (*AddUserHeadwearExtraTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorHeadwear, []int{16}
}

func (m *AddUserHeadwearExtraTimeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserHeadwearExtraTimeReq) GetExtraTime() uint32 {
	if m != nil {
		return m.ExtraTime
	}
	return 0
}

func (m *AddUserHeadwearExtraTimeReq) GetSuiteIds() []uint32 {
	if m != nil {
		return m.SuiteIds
	}
	return nil
}

func init() {
	proto.RegisterType((*HeadwearConfig)(nil), "headwear.HeadwearConfig")
	proto.RegisterType((*UserHeadwearInfo)(nil), "headwear.UserHeadwearInfo")
	proto.RegisterType((*GetUserHeadwearReq)(nil), "headwear.GetUserHeadwearReq")
	proto.RegisterType((*GetUserHeadwearResp)(nil), "headwear.GetUserHeadwearResp")
	proto.RegisterType((*GetUserHeadwearBySuiteReq)(nil), "headwear.GetUserHeadwearBySuiteReq")
	proto.RegisterType((*GetHeadwearConfigReq)(nil), "headwear.GetHeadwearConfigReq")
	proto.RegisterType((*GetHeadwearConfigResp)(nil), "headwear.GetHeadwearConfigResp")
	proto.RegisterType((*SetUserHeadwearInUseReq)(nil), "headwear.SetUserHeadwearInUseReq")
	proto.RegisterType((*GetUserHeadwearInUseReq)(nil), "headwear.GetUserHeadwearInUseReq")
	proto.RegisterType((*RemoveUserHeadwearInUseReq)(nil), "headwear.RemoveUserHeadwearInUseReq")
	proto.RegisterType((*GetUserHeadwearInUseResp)(nil), "headwear.GetUserHeadwearInUseResp")
	proto.RegisterType((*GetUserHeadwearInUseListReq)(nil), "headwear.GetUserHeadwearInUseListReq")
	proto.RegisterType((*GetUserHeadwearInUseListResp)(nil), "headwear.GetUserHeadwearInUseListResp")
	proto.RegisterType((*GiveHeadweartoUserReq)(nil), "headwear.GiveHeadweartoUserReq")
	proto.RegisterType((*HeadwearConfigReq)(nil), "headwear.HeadwearConfigReq")
	proto.RegisterType((*GetHeadwearConfigAllResp)(nil), "headwear.GetHeadwearConfigAllResp")
	proto.RegisterType((*AddUserHeadwearExtraTimeReq)(nil), "headwear.AddUserHeadwearExtraTimeReq")
	proto.RegisterEnum("headwear.HEADWEAR_TYPE", HEADWEAR_TYPE_name, HEADWEAR_TYPE_value)
	proto.RegisterEnum("headwear.GIVE_TYPE", GIVE_TYPE_name, GIVE_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for HeadWear service

type HeadWearClient interface {
	GetUserHeadwear(ctx context.Context, in *GetUserHeadwearReq, opts ...grpc.CallOption) (*GetUserHeadwearResp, error)
	SetUserHeadwearInUse(ctx context.Context, in *SetUserHeadwearInUseReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetUserHeadwearInUse(ctx context.Context, in *GetUserHeadwearInUseReq, opts ...grpc.CallOption) (*GetUserHeadwearInUseResp, error)
	GiveHeadweartoUser(ctx context.Context, in *GiveHeadweartoUserReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	AddHeadwearConfig(ctx context.Context, in *HeadwearConfig, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetHeadwearConfig(ctx context.Context, in *HeadwearConfigReq, opts ...grpc.CallOption) (*HeadwearConfig, error)
	DelHeadwearConfig(ctx context.Context, in *HeadwearConfigReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetHeadwearConfigAll(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetHeadwearConfigAllResp, error)
	GetUserHeadwearInUseList(ctx context.Context, in *GetUserHeadwearInUseListReq, opts ...grpc.CallOption) (*GetUserHeadwearInUseListResp, error)
	RemoveUserHeadwearInUse(ctx context.Context, in *RemoveUserHeadwearInUseReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetUserHeadwearBySuite(ctx context.Context, in *GetUserHeadwearBySuiteReq, opts ...grpc.CallOption) (*UserHeadwearInfo, error)
	AddUserHeadwearExtraTime(ctx context.Context, in *AddUserHeadwearExtraTimeReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GiveHeadweartoUserWithExpireTime(ctx context.Context, in *GiveHeadweartoUserReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	UpdateHeadwearConfig(ctx context.Context, in *HeadwearConfig, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GiveHeadweartoUserWithExpireTimeRel(ctx context.Context, in *GiveHeadweartoUserReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
}

type headWearClient struct {
	cc *grpc.ClientConn
}

func NewHeadWearClient(cc *grpc.ClientConn) HeadWearClient {
	return &headWearClient{cc}
}

func (c *headWearClient) GetUserHeadwear(ctx context.Context, in *GetUserHeadwearReq, opts ...grpc.CallOption) (*GetUserHeadwearResp, error) {
	out := new(GetUserHeadwearResp)
	err := grpc.Invoke(ctx, "/headwear.HeadWear/GetUserHeadwear", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *headWearClient) SetUserHeadwearInUse(ctx context.Context, in *SetUserHeadwearInUseReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/headwear.HeadWear/SetUserHeadwearInUse", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *headWearClient) GetUserHeadwearInUse(ctx context.Context, in *GetUserHeadwearInUseReq, opts ...grpc.CallOption) (*GetUserHeadwearInUseResp, error) {
	out := new(GetUserHeadwearInUseResp)
	err := grpc.Invoke(ctx, "/headwear.HeadWear/GetUserHeadwearInUse", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *headWearClient) GiveHeadweartoUser(ctx context.Context, in *GiveHeadweartoUserReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/headwear.HeadWear/GiveHeadweartoUser", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *headWearClient) AddHeadwearConfig(ctx context.Context, in *HeadwearConfig, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/headwear.HeadWear/AddHeadwearConfig", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *headWearClient) GetHeadwearConfig(ctx context.Context, in *HeadwearConfigReq, opts ...grpc.CallOption) (*HeadwearConfig, error) {
	out := new(HeadwearConfig)
	err := grpc.Invoke(ctx, "/headwear.HeadWear/GetHeadwearConfig", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *headWearClient) DelHeadwearConfig(ctx context.Context, in *HeadwearConfigReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/headwear.HeadWear/DelHeadwearConfig", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *headWearClient) GetHeadwearConfigAll(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetHeadwearConfigAllResp, error) {
	out := new(GetHeadwearConfigAllResp)
	err := grpc.Invoke(ctx, "/headwear.HeadWear/GetHeadwearConfigAll", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *headWearClient) GetUserHeadwearInUseList(ctx context.Context, in *GetUserHeadwearInUseListReq, opts ...grpc.CallOption) (*GetUserHeadwearInUseListResp, error) {
	out := new(GetUserHeadwearInUseListResp)
	err := grpc.Invoke(ctx, "/headwear.HeadWear/GetUserHeadwearInUseList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *headWearClient) RemoveUserHeadwearInUse(ctx context.Context, in *RemoveUserHeadwearInUseReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/headwear.HeadWear/RemoveUserHeadwearInUse", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *headWearClient) GetUserHeadwearBySuite(ctx context.Context, in *GetUserHeadwearBySuiteReq, opts ...grpc.CallOption) (*UserHeadwearInfo, error) {
	out := new(UserHeadwearInfo)
	err := grpc.Invoke(ctx, "/headwear.HeadWear/GetUserHeadwearBySuite", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *headWearClient) AddUserHeadwearExtraTime(ctx context.Context, in *AddUserHeadwearExtraTimeReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/headwear.HeadWear/AddUserHeadwearExtraTime", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *headWearClient) GiveHeadweartoUserWithExpireTime(ctx context.Context, in *GiveHeadweartoUserReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/headwear.HeadWear/GiveHeadweartoUserWithExpireTime", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *headWearClient) UpdateHeadwearConfig(ctx context.Context, in *HeadwearConfig, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/headwear.HeadWear/UpdateHeadwearConfig", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *headWearClient) GiveHeadweartoUserWithExpireTimeRel(ctx context.Context, in *GiveHeadweartoUserReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/headwear.HeadWear/GiveHeadweartoUserWithExpireTimeRel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for HeadWear service

type HeadWearServer interface {
	GetUserHeadwear(context.Context, *GetUserHeadwearReq) (*GetUserHeadwearResp, error)
	SetUserHeadwearInUse(context.Context, *SetUserHeadwearInUseReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetUserHeadwearInUse(context.Context, *GetUserHeadwearInUseReq) (*GetUserHeadwearInUseResp, error)
	GiveHeadweartoUser(context.Context, *GiveHeadweartoUserReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	AddHeadwearConfig(context.Context, *HeadwearConfig) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetHeadwearConfig(context.Context, *HeadwearConfigReq) (*HeadwearConfig, error)
	DelHeadwearConfig(context.Context, *HeadwearConfigReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetHeadwearConfigAll(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*GetHeadwearConfigAllResp, error)
	GetUserHeadwearInUseList(context.Context, *GetUserHeadwearInUseListReq) (*GetUserHeadwearInUseListResp, error)
	RemoveUserHeadwearInUse(context.Context, *RemoveUserHeadwearInUseReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetUserHeadwearBySuite(context.Context, *GetUserHeadwearBySuiteReq) (*UserHeadwearInfo, error)
	AddUserHeadwearExtraTime(context.Context, *AddUserHeadwearExtraTimeReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GiveHeadweartoUserWithExpireTime(context.Context, *GiveHeadweartoUserReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	UpdateHeadwearConfig(context.Context, *HeadwearConfig) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GiveHeadweartoUserWithExpireTimeRel(context.Context, *GiveHeadweartoUserReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
}

func RegisterHeadWearServer(s *grpc.Server, srv HeadWearServer) {
	s.RegisterService(&_HeadWear_serviceDesc, srv)
}

func _HeadWear_GetUserHeadwear_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserHeadwearReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HeadWearServer).GetUserHeadwear(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/headwear.HeadWear/GetUserHeadwear",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HeadWearServer).GetUserHeadwear(ctx, req.(*GetUserHeadwearReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HeadWear_SetUserHeadwearInUse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserHeadwearInUseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HeadWearServer).SetUserHeadwearInUse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/headwear.HeadWear/SetUserHeadwearInUse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HeadWearServer).SetUserHeadwearInUse(ctx, req.(*SetUserHeadwearInUseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HeadWear_GetUserHeadwearInUse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserHeadwearInUseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HeadWearServer).GetUserHeadwearInUse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/headwear.HeadWear/GetUserHeadwearInUse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HeadWearServer).GetUserHeadwearInUse(ctx, req.(*GetUserHeadwearInUseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HeadWear_GiveHeadweartoUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiveHeadweartoUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HeadWearServer).GiveHeadweartoUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/headwear.HeadWear/GiveHeadweartoUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HeadWearServer).GiveHeadweartoUser(ctx, req.(*GiveHeadweartoUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HeadWear_AddHeadwearConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HeadwearConfig)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HeadWearServer).AddHeadwearConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/headwear.HeadWear/AddHeadwearConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HeadWearServer).AddHeadwearConfig(ctx, req.(*HeadwearConfig))
	}
	return interceptor(ctx, in, info, handler)
}

func _HeadWear_GetHeadwearConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HeadwearConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HeadWearServer).GetHeadwearConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/headwear.HeadWear/GetHeadwearConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HeadWearServer).GetHeadwearConfig(ctx, req.(*HeadwearConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HeadWear_DelHeadwearConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HeadwearConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HeadWearServer).DelHeadwearConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/headwear.HeadWear/DelHeadwearConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HeadWearServer).DelHeadwearConfig(ctx, req.(*HeadwearConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HeadWear_GetHeadwearConfigAll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HeadWearServer).GetHeadwearConfigAll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/headwear.HeadWear/GetHeadwearConfigAll",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HeadWearServer).GetHeadwearConfigAll(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _HeadWear_GetUserHeadwearInUseList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserHeadwearInUseListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HeadWearServer).GetUserHeadwearInUseList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/headwear.HeadWear/GetUserHeadwearInUseList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HeadWearServer).GetUserHeadwearInUseList(ctx, req.(*GetUserHeadwearInUseListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HeadWear_RemoveUserHeadwearInUse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveUserHeadwearInUseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HeadWearServer).RemoveUserHeadwearInUse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/headwear.HeadWear/RemoveUserHeadwearInUse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HeadWearServer).RemoveUserHeadwearInUse(ctx, req.(*RemoveUserHeadwearInUseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HeadWear_GetUserHeadwearBySuite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserHeadwearBySuiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HeadWearServer).GetUserHeadwearBySuite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/headwear.HeadWear/GetUserHeadwearBySuite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HeadWearServer).GetUserHeadwearBySuite(ctx, req.(*GetUserHeadwearBySuiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HeadWear_AddUserHeadwearExtraTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserHeadwearExtraTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HeadWearServer).AddUserHeadwearExtraTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/headwear.HeadWear/AddUserHeadwearExtraTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HeadWearServer).AddUserHeadwearExtraTime(ctx, req.(*AddUserHeadwearExtraTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HeadWear_GiveHeadweartoUserWithExpireTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiveHeadweartoUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HeadWearServer).GiveHeadweartoUserWithExpireTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/headwear.HeadWear/GiveHeadweartoUserWithExpireTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HeadWearServer).GiveHeadweartoUserWithExpireTime(ctx, req.(*GiveHeadweartoUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HeadWear_UpdateHeadwearConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HeadwearConfig)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HeadWearServer).UpdateHeadwearConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/headwear.HeadWear/UpdateHeadwearConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HeadWearServer).UpdateHeadwearConfig(ctx, req.(*HeadwearConfig))
	}
	return interceptor(ctx, in, info, handler)
}

func _HeadWear_GiveHeadweartoUserWithExpireTimeRel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiveHeadweartoUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HeadWearServer).GiveHeadweartoUserWithExpireTimeRel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/headwear.HeadWear/GiveHeadweartoUserWithExpireTimeRel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HeadWearServer).GiveHeadweartoUserWithExpireTimeRel(ctx, req.(*GiveHeadweartoUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _HeadWear_serviceDesc = grpc.ServiceDesc{
	ServiceName: "headwear.HeadWear",
	HandlerType: (*HeadWearServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserHeadwear",
			Handler:    _HeadWear_GetUserHeadwear_Handler,
		},
		{
			MethodName: "SetUserHeadwearInUse",
			Handler:    _HeadWear_SetUserHeadwearInUse_Handler,
		},
		{
			MethodName: "GetUserHeadwearInUse",
			Handler:    _HeadWear_GetUserHeadwearInUse_Handler,
		},
		{
			MethodName: "GiveHeadweartoUser",
			Handler:    _HeadWear_GiveHeadweartoUser_Handler,
		},
		{
			MethodName: "AddHeadwearConfig",
			Handler:    _HeadWear_AddHeadwearConfig_Handler,
		},
		{
			MethodName: "GetHeadwearConfig",
			Handler:    _HeadWear_GetHeadwearConfig_Handler,
		},
		{
			MethodName: "DelHeadwearConfig",
			Handler:    _HeadWear_DelHeadwearConfig_Handler,
		},
		{
			MethodName: "GetHeadwearConfigAll",
			Handler:    _HeadWear_GetHeadwearConfigAll_Handler,
		},
		{
			MethodName: "GetUserHeadwearInUseList",
			Handler:    _HeadWear_GetUserHeadwearInUseList_Handler,
		},
		{
			MethodName: "RemoveUserHeadwearInUse",
			Handler:    _HeadWear_RemoveUserHeadwearInUse_Handler,
		},
		{
			MethodName: "GetUserHeadwearBySuite",
			Handler:    _HeadWear_GetUserHeadwearBySuite_Handler,
		},
		{
			MethodName: "AddUserHeadwearExtraTime",
			Handler:    _HeadWear_AddUserHeadwearExtraTime_Handler,
		},
		{
			MethodName: "GiveHeadweartoUserWithExpireTime",
			Handler:    _HeadWear_GiveHeadweartoUserWithExpireTime_Handler,
		},
		{
			MethodName: "UpdateHeadwearConfig",
			Handler:    _HeadWear_UpdateHeadwearConfig_Handler,
		},
		{
			MethodName: "GiveHeadweartoUserWithExpireTimeRel",
			Handler:    _HeadWear_GiveHeadweartoUserWithExpireTimeRel_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/headwearsvr/headwear.proto",
}

func (m *HeadwearConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HeadwearConfig) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.HeadwearId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.HeadwearId))
	}
	if m.SuiteId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.SuiteId))
	}
	if len(m.Name) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(len(m.Name)))
		i += copy(dAtA[i:], m.Name)
	}
	if len(m.Img) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(len(m.Img)))
		i += copy(dAtA[i:], m.Img)
	}
	if m.Level != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.Level))
	}
	if m.HoldingTime != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.HoldingTime))
	}
	if len(m.StaticImg) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(len(m.StaticImg)))
		i += copy(dAtA[i:], m.StaticImg)
	}
	if len(m.GifImg) > 0 {
		dAtA[i] = 0x42
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(len(m.GifImg)))
		i += copy(dAtA[i:], m.GifImg)
	}
	if m.HeadwearType != 0 {
		dAtA[i] = 0x48
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.HeadwearType))
	}
	if len(m.CpHeadwearImg) > 0 {
		dAtA[i] = 0x52
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(len(m.CpHeadwearImg)))
		i += copy(dAtA[i:], m.CpHeadwearImg)
	}
	return i, nil
}

func (m *UserHeadwearInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserHeadwearInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.Uid))
	}
	if m.HeadwearId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.HeadwearId))
	}
	if m.ExpireTime != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.ExpireTime))
	}
	if m.ExtraTime != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.ExtraTime))
	}
	if m.HeadwearConfig != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.HeadwearConfig.Size()))
		n1, err := m.HeadwearConfig.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if m.CpUid != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.CpUid))
	}
	return i, nil
}

func (m *GetUserHeadwearReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserHeadwearReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.Uid))
	}
	return i, nil
}

func (m *GetUserHeadwearResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserHeadwearResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.HeadwearList) > 0 {
		for _, msg := range m.HeadwearList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintHeadwear(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserHeadwearBySuiteReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserHeadwearBySuiteReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.Uid))
	}
	if m.SuiteId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.SuiteId))
	}
	if m.CpUid != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.CpUid))
	}
	return i, nil
}

func (m *GetHeadwearConfigReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetHeadwearConfigReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.IdList) > 0 {
		dAtA3 := make([]byte, len(m.IdList)*10)
		var j2 int
		for _, num := range m.IdList {
			for num >= 1<<7 {
				dAtA3[j2] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j2++
			}
			dAtA3[j2] = uint8(num)
			j2++
		}
		dAtA[i] = 0xa
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(j2))
		i += copy(dAtA[i:], dAtA3[:j2])
	}
	return i, nil
}

func (m *GetHeadwearConfigResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetHeadwearConfigResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.HeadwearList) > 0 {
		for _, msg := range m.HeadwearList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintHeadwear(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetUserHeadwearInUseReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserHeadwearInUseReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.Uid))
	}
	if m.HeadwearId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.HeadwearId))
	}
	if m.CpUid != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.CpUid))
	}
	return i, nil
}

func (m *GetUserHeadwearInUseReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserHeadwearInUseReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.Uid))
	}
	return i, nil
}

func (m *RemoveUserHeadwearInUseReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveUserHeadwearInUseReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.Uid))
	}
	return i, nil
}

func (m *GetUserHeadwearInUseResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserHeadwearInUseResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.HeadwearId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.HeadwearId))
	}
	if m.CpUid != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.CpUid))
	}
	return i, nil
}

func (m *GetUserHeadwearInUseListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserHeadwearInUseListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Uids) > 0 {
		dAtA5 := make([]byte, len(m.Uids)*10)
		var j4 int
		for _, num := range m.Uids {
			for num >= 1<<7 {
				dAtA5[j4] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j4++
			}
			dAtA5[j4] = uint8(num)
			j4++
		}
		dAtA[i] = 0xa
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(j4))
		i += copy(dAtA[i:], dAtA5[:j4])
	}
	return i, nil
}

func (m *GetUserHeadwearInUseListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserHeadwearInUseListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.HeadwearList) > 0 {
		for _, msg := range m.HeadwearList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintHeadwear(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GiveHeadweartoUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GiveHeadweartoUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.Uid))
	}
	if m.SuiteId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.SuiteId))
	}
	if m.Level != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.Level))
	}
	if m.GiveType != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.GiveType))
	}
	if m.ExpireTime != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.ExpireTime))
	}
	if m.ExpireTimeRel != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.ExpireTimeRel))
	}
	if m.CpUid != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.CpUid))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x42
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if len(m.PushmsgTextPrefix) > 0 {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(len(m.PushmsgTextPrefix)))
		i += copy(dAtA[i:], m.PushmsgTextPrefix)
	}
	return i, nil
}

func (m *HeadwearConfigReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HeadwearConfigReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.HeadwearId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.HeadwearId))
	}
	return i, nil
}

func (m *GetHeadwearConfigAllResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetHeadwearConfigAllResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ConfigList) > 0 {
		for _, msg := range m.ConfigList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintHeadwear(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddUserHeadwearExtraTimeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserHeadwearExtraTimeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.Uid))
	}
	if m.ExtraTime != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(m.ExtraTime))
	}
	if len(m.SuiteIds) > 0 {
		dAtA7 := make([]byte, len(m.SuiteIds)*10)
		var j6 int
		for _, num := range m.SuiteIds {
			for num >= 1<<7 {
				dAtA7[j6] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j6++
			}
			dAtA7[j6] = uint8(num)
			j6++
		}
		dAtA[i] = 0x1a
		i++
		i = encodeVarintHeadwear(dAtA, i, uint64(j6))
		i += copy(dAtA[i:], dAtA7[:j6])
	}
	return i, nil
}

func encodeFixed64Headwear(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Headwear(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintHeadwear(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *HeadwearConfig) Size() (n int) {
	var l int
	_ = l
	if m.HeadwearId != 0 {
		n += 1 + sovHeadwear(uint64(m.HeadwearId))
	}
	if m.SuiteId != 0 {
		n += 1 + sovHeadwear(uint64(m.SuiteId))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovHeadwear(uint64(l))
	}
	l = len(m.Img)
	if l > 0 {
		n += 1 + l + sovHeadwear(uint64(l))
	}
	if m.Level != 0 {
		n += 1 + sovHeadwear(uint64(m.Level))
	}
	if m.HoldingTime != 0 {
		n += 1 + sovHeadwear(uint64(m.HoldingTime))
	}
	l = len(m.StaticImg)
	if l > 0 {
		n += 1 + l + sovHeadwear(uint64(l))
	}
	l = len(m.GifImg)
	if l > 0 {
		n += 1 + l + sovHeadwear(uint64(l))
	}
	if m.HeadwearType != 0 {
		n += 1 + sovHeadwear(uint64(m.HeadwearType))
	}
	l = len(m.CpHeadwearImg)
	if l > 0 {
		n += 1 + l + sovHeadwear(uint64(l))
	}
	return n
}

func (m *UserHeadwearInfo) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovHeadwear(uint64(m.Uid))
	}
	if m.HeadwearId != 0 {
		n += 1 + sovHeadwear(uint64(m.HeadwearId))
	}
	if m.ExpireTime != 0 {
		n += 1 + sovHeadwear(uint64(m.ExpireTime))
	}
	if m.ExtraTime != 0 {
		n += 1 + sovHeadwear(uint64(m.ExtraTime))
	}
	if m.HeadwearConfig != nil {
		l = m.HeadwearConfig.Size()
		n += 1 + l + sovHeadwear(uint64(l))
	}
	if m.CpUid != 0 {
		n += 1 + sovHeadwear(uint64(m.CpUid))
	}
	return n
}

func (m *GetUserHeadwearReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovHeadwear(uint64(m.Uid))
	}
	return n
}

func (m *GetUserHeadwearResp) Size() (n int) {
	var l int
	_ = l
	if len(m.HeadwearList) > 0 {
		for _, e := range m.HeadwearList {
			l = e.Size()
			n += 1 + l + sovHeadwear(uint64(l))
		}
	}
	return n
}

func (m *GetUserHeadwearBySuiteReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovHeadwear(uint64(m.Uid))
	}
	if m.SuiteId != 0 {
		n += 1 + sovHeadwear(uint64(m.SuiteId))
	}
	if m.CpUid != 0 {
		n += 1 + sovHeadwear(uint64(m.CpUid))
	}
	return n
}

func (m *GetHeadwearConfigReq) Size() (n int) {
	var l int
	_ = l
	if len(m.IdList) > 0 {
		l = 0
		for _, e := range m.IdList {
			l += sovHeadwear(uint64(e))
		}
		n += 1 + sovHeadwear(uint64(l)) + l
	}
	return n
}

func (m *GetHeadwearConfigResp) Size() (n int) {
	var l int
	_ = l
	if len(m.HeadwearList) > 0 {
		for _, e := range m.HeadwearList {
			l = e.Size()
			n += 1 + l + sovHeadwear(uint64(l))
		}
	}
	return n
}

func (m *SetUserHeadwearInUseReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovHeadwear(uint64(m.Uid))
	}
	if m.HeadwearId != 0 {
		n += 1 + sovHeadwear(uint64(m.HeadwearId))
	}
	if m.CpUid != 0 {
		n += 1 + sovHeadwear(uint64(m.CpUid))
	}
	return n
}

func (m *GetUserHeadwearInUseReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovHeadwear(uint64(m.Uid))
	}
	return n
}

func (m *RemoveUserHeadwearInUseReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovHeadwear(uint64(m.Uid))
	}
	return n
}

func (m *GetUserHeadwearInUseResp) Size() (n int) {
	var l int
	_ = l
	if m.HeadwearId != 0 {
		n += 1 + sovHeadwear(uint64(m.HeadwearId))
	}
	if m.CpUid != 0 {
		n += 1 + sovHeadwear(uint64(m.CpUid))
	}
	return n
}

func (m *GetUserHeadwearInUseListReq) Size() (n int) {
	var l int
	_ = l
	if len(m.Uids) > 0 {
		l = 0
		for _, e := range m.Uids {
			l += sovHeadwear(uint64(e))
		}
		n += 1 + sovHeadwear(uint64(l)) + l
	}
	return n
}

func (m *GetUserHeadwearInUseListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.HeadwearList) > 0 {
		for _, e := range m.HeadwearList {
			l = e.Size()
			n += 1 + l + sovHeadwear(uint64(l))
		}
	}
	return n
}

func (m *GiveHeadweartoUserReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovHeadwear(uint64(m.Uid))
	}
	if m.SuiteId != 0 {
		n += 1 + sovHeadwear(uint64(m.SuiteId))
	}
	if m.Level != 0 {
		n += 1 + sovHeadwear(uint64(m.Level))
	}
	if m.GiveType != 0 {
		n += 1 + sovHeadwear(uint64(m.GiveType))
	}
	if m.ExpireTime != 0 {
		n += 1 + sovHeadwear(uint64(m.ExpireTime))
	}
	if m.ExpireTimeRel != 0 {
		n += 1 + sovHeadwear(uint64(m.ExpireTimeRel))
	}
	if m.CpUid != 0 {
		n += 1 + sovHeadwear(uint64(m.CpUid))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovHeadwear(uint64(l))
	}
	l = len(m.PushmsgTextPrefix)
	if l > 0 {
		n += 1 + l + sovHeadwear(uint64(l))
	}
	return n
}

func (m *HeadwearConfigReq) Size() (n int) {
	var l int
	_ = l
	if m.HeadwearId != 0 {
		n += 1 + sovHeadwear(uint64(m.HeadwearId))
	}
	return n
}

func (m *GetHeadwearConfigAllResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ConfigList) > 0 {
		for _, e := range m.ConfigList {
			l = e.Size()
			n += 1 + l + sovHeadwear(uint64(l))
		}
	}
	return n
}

func (m *AddUserHeadwearExtraTimeReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovHeadwear(uint64(m.Uid))
	}
	if m.ExtraTime != 0 {
		n += 1 + sovHeadwear(uint64(m.ExtraTime))
	}
	if len(m.SuiteIds) > 0 {
		l = 0
		for _, e := range m.SuiteIds {
			l += sovHeadwear(uint64(e))
		}
		n += 1 + sovHeadwear(uint64(l)) + l
	}
	return n
}

func sovHeadwear(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozHeadwear(x uint64) (n int) {
	return sovHeadwear(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *HeadwearConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHeadwear
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HeadwearConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HeadwearConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearId", wireType)
			}
			m.HeadwearId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HeadwearId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SuiteId", wireType)
			}
			m.SuiteId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SuiteId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthHeadwear
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Img", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthHeadwear
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Img = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HoldingTime", wireType)
			}
			m.HoldingTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HoldingTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StaticImg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthHeadwear
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StaticImg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GifImg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthHeadwear
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GifImg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearType", wireType)
			}
			m.HeadwearType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HeadwearType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CpHeadwearImg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthHeadwear
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CpHeadwearImg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipHeadwear(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHeadwear
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserHeadwearInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHeadwear
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserHeadwearInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserHeadwearInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearId", wireType)
			}
			m.HeadwearId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HeadwearId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtraTime", wireType)
			}
			m.ExtraTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtraTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearConfig", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthHeadwear
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.HeadwearConfig == nil {
				m.HeadwearConfig = &HeadwearConfig{}
			}
			if err := m.HeadwearConfig.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CpUid", wireType)
			}
			m.CpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipHeadwear(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHeadwear
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserHeadwearReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHeadwear
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserHeadwearReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserHeadwearReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipHeadwear(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHeadwear
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserHeadwearResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHeadwear
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserHeadwearResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserHeadwearResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthHeadwear
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HeadwearList = append(m.HeadwearList, &UserHeadwearInfo{})
			if err := m.HeadwearList[len(m.HeadwearList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipHeadwear(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHeadwear
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserHeadwearBySuiteReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHeadwear
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserHeadwearBySuiteReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserHeadwearBySuiteReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SuiteId", wireType)
			}
			m.SuiteId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SuiteId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CpUid", wireType)
			}
			m.CpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipHeadwear(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHeadwear
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetHeadwearConfigReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHeadwear
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetHeadwearConfigReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetHeadwearConfigReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowHeadwear
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.IdList = append(m.IdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowHeadwear
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthHeadwear
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowHeadwear
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.IdList = append(m.IdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipHeadwear(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHeadwear
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetHeadwearConfigResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHeadwear
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetHeadwearConfigResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetHeadwearConfigResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthHeadwear
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HeadwearList = append(m.HeadwearList, &HeadwearConfig{})
			if err := m.HeadwearList[len(m.HeadwearList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipHeadwear(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHeadwear
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserHeadwearInUseReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHeadwear
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserHeadwearInUseReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserHeadwearInUseReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearId", wireType)
			}
			m.HeadwearId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HeadwearId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CpUid", wireType)
			}
			m.CpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipHeadwear(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHeadwear
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserHeadwearInUseReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHeadwear
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserHeadwearInUseReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserHeadwearInUseReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipHeadwear(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHeadwear
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveUserHeadwearInUseReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHeadwear
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveUserHeadwearInUseReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveUserHeadwearInUseReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipHeadwear(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHeadwear
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserHeadwearInUseResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHeadwear
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserHeadwearInUseResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserHeadwearInUseResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearId", wireType)
			}
			m.HeadwearId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HeadwearId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CpUid", wireType)
			}
			m.CpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipHeadwear(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHeadwear
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserHeadwearInUseListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHeadwear
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserHeadwearInUseListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserHeadwearInUseListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowHeadwear
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Uids = append(m.Uids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowHeadwear
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthHeadwear
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowHeadwear
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Uids = append(m.Uids, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uids", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipHeadwear(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHeadwear
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserHeadwearInUseListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHeadwear
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserHeadwearInUseListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserHeadwearInUseListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthHeadwear
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HeadwearList = append(m.HeadwearList, &UserHeadwearInfo{})
			if err := m.HeadwearList[len(m.HeadwearList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipHeadwear(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHeadwear
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GiveHeadweartoUserReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHeadwear
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GiveHeadweartoUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GiveHeadweartoUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SuiteId", wireType)
			}
			m.SuiteId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SuiteId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiveType", wireType)
			}
			m.GiveType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiveType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTimeRel", wireType)
			}
			m.ExpireTimeRel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTimeRel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CpUid", wireType)
			}
			m.CpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthHeadwear
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushmsgTextPrefix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthHeadwear
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PushmsgTextPrefix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipHeadwear(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHeadwear
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HeadwearConfigReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHeadwear
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HeadwearConfigReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HeadwearConfigReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearId", wireType)
			}
			m.HeadwearId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HeadwearId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipHeadwear(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHeadwear
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetHeadwearConfigAllResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHeadwear
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetHeadwearConfigAllResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetHeadwearConfigAllResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConfigList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthHeadwear
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ConfigList = append(m.ConfigList, &HeadwearConfig{})
			if err := m.ConfigList[len(m.ConfigList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipHeadwear(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHeadwear
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserHeadwearExtraTimeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHeadwear
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserHeadwearExtraTimeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserHeadwearExtraTimeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtraTime", wireType)
			}
			m.ExtraTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtraTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowHeadwear
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.SuiteIds = append(m.SuiteIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowHeadwear
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthHeadwear
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowHeadwear
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.SuiteIds = append(m.SuiteIds, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field SuiteIds", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipHeadwear(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHeadwear
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipHeadwear(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowHeadwear
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowHeadwear
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthHeadwear
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowHeadwear
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipHeadwear(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthHeadwear = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowHeadwear   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/headwearsvr/headwear.proto", fileDescriptorHeadwear) }

var fileDescriptorHeadwear = []byte{
	// 1430 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x58, 0xcd, 0x6b, 0x1b, 0xd7,
	0x16, 0xf7, 0x48, 0xb6, 0x3e, 0x8e, 0x2c, 0x4b, 0xbe, 0x51, 0x9e, 0x27, 0x72, 0x62, 0x4f, 0xc6,
	0x79, 0x26, 0xe4, 0x21, 0x9b, 0xe4, 0xe5, 0xc1, 0xeb, 0xa0, 0x2a, 0xc8, 0x89, 0x70, 0x44, 0x1b,
	0xc7, 0x28, 0x76, 0x4c, 0x09, 0x65, 0x50, 0x34, 0xd7, 0xa3, 0x4b, 0x66, 0xa4, 0xb1, 0xe6, 0x4a,
	0xb1, 0x77, 0x85, 0x42, 0x09, 0xd9, 0xf4, 0x63, 0xd1, 0x55, 0x57, 0xc5, 0x7f, 0x40, 0xff, 0x8c,
	0x42, 0x37, 0x5d, 0x75, 0xdb, 0x92, 0x52, 0x30, 0x85, 0x2e, 0xba, 0xe9, 0xba, 0xdc, 0x3b, 0x1a,
	0xcd, 0x48, 0x9a, 0x91, 0x6c, 0x12, 0xba, 0x9b, 0x7b, 0xce, 0xbd, 0xe7, 0x9c, 0xdf, 0xf9, 0xf8,
	0xe9, 0x20, 0x58, 0xb1, 0x3b, 0x8d, 0xcd, 0x26, 0xae, 0x6b, 0x2f, 0x71, 0xbd, 0x63, 0xf7, 0x3a,
	0x83, 0xef, 0x0d, 0xab, 0xd3, 0xa6, 0x6d, 0x94, 0x70, 0xcf, 0xf9, 0x1b, 0x8d, 0xb6, 0x69, 0xb6,
	0x5b, 0x9b, 0xd4, 0xe8, 0x59, 0xa4, 0xf1, 0xc2, 0xc0, 0x9b, 0xf6, 0x8b, 0xe7, 0x5d, 0x62, 0x50,
	0xd2, 0xa2, 0x27, 0x16, 0x76, 0xee, 0xcb, 0xdf, 0x45, 0x60, 0xe1, 0x61, 0xff, 0xc9, 0xfd, 0x76,
	0xeb, 0x90, 0xe8, 0x68, 0x15, 0x52, 0xae, 0x11, 0x95, 0x68, 0xa2, 0x20, 0x09, 0x37, 0xd3, 0x35,
	0x70, 0x45, 0x55, 0x0d, 0x5d, 0x81, 0x84, 0xdd, 0x25, 0x14, 0x33, 0x6d, 0x84, 0x6b, 0xe3, 0xfc,
	0x5c, 0xd5, 0x10, 0x82, 0xd9, 0x56, 0xdd, 0xc4, 0x62, 0x54, 0x12, 0x6e, 0x26, 0x6b, 0xfc, 0x1b,
	0x65, 0x21, 0x4a, 0x4c, 0x5d, 0x9c, 0xe5, 0x22, 0xf6, 0x89, 0x72, 0x30, 0x67, 0xe0, 0x1e, 0x36,
	0xc4, 0x39, 0xfe, 0xda, 0x39, 0xa0, 0xeb, 0x30, 0xdf, 0x6c, 0x1b, 0x1a, 0x69, 0xe9, 0x2a, 0x25,
	0x26, 0x16, 0x63, 0x5c, 0x99, 0xea, 0xcb, 0xf6, 0x88, 0x89, 0xd1, 0x35, 0x00, 0x9b, 0xd6, 0x29,
	0x69, 0xa8, 0xcc, 0x62, 0x9c, 0x5b, 0x4c, 0x3a, 0x92, 0xaa, 0xa9, 0xa3, 0x25, 0x88, 0xeb, 0xe4,
	0x90, 0xeb, 0x12, 0x5c, 0x17, 0xd3, 0xc9, 0x21, 0x53, 0xac, 0x41, 0x7a, 0x00, 0x89, 0x81, 0x17,
	0x93, 0xdc, 0xf6, 0xbc, 0x2b, 0xdc, 0x3b, 0xb1, 0x30, 0x5a, 0x87, 0x4c, 0xc3, 0x52, 0x3d, 0xe8,
	0xa6, 0x2e, 0x02, 0xb7, 0x92, 0x6e, 0x58, 0x6e, 0x8a, 0xaa, 0xa6, 0x2e, 0xff, 0x2c, 0x40, 0x76,
	0xdf, 0xc6, 0x9d, 0x81, 0xac, 0x75, 0xd8, 0x66, 0x20, 0xbb, 0x83, 0x64, 0xb1, 0xcf, 0xd1, 0x34,
	0x46, 0xc6, 0xd2, 0xb8, 0x0a, 0x29, 0x7c, 0x6c, 0x91, 0x0e, 0x76, 0xe0, 0x46, 0x9d, 0x0b, 0x8e,
	0xc8, 0x45, 0x8b, 0x8f, 0x69, 0xa7, 0xee, 0xe8, 0x67, 0xb9, 0x3e, 0xc9, 0x25, 0x5c, 0x5d, 0x86,
	0xcc, 0xc0, 0x41, 0x83, 0x97, 0x8e, 0xe7, 0x33, 0x75, 0x47, 0xdc, 0x18, 0x34, 0xc5, 0x70, 0x69,
	0x6b, 0x0b, 0xcd, 0xe1, 0x52, 0x5f, 0x86, 0x58, 0xc3, 0x52, 0x59, 0xe0, 0x4e, 0xb2, 0xe7, 0x1a,
	0xd6, 0x3e, 0xd1, 0xe4, 0x75, 0x40, 0xdb, 0x98, 0xfa, 0x31, 0xd6, 0xf0, 0xd1, 0x38, 0x44, 0xf9,
	0x29, 0x5c, 0x1a, 0xbb, 0x67, 0x5b, 0xe8, 0x9e, 0x2f, 0xdb, 0x06, 0xb1, 0xa9, 0x28, 0x48, 0xd1,
	0x9b, 0xa9, 0x3b, 0x79, 0x2f, 0xac, 0xd1, 0xf4, 0x79, 0x95, 0xf8, 0x90, 0xd8, 0x54, 0x56, 0xe1,
	0xca, 0x88, 0xdd, 0xad, 0x93, 0x27, 0xac, 0xc3, 0x02, 0xc3, 0x98, 0xd4, 0x8f, 0x1e, 0xc0, 0xa8,
	0x1f, 0xe0, 0x26, 0xe4, 0xb6, 0x31, 0x1d, 0x49, 0x0e, 0x3e, 0x62, 0x0d, 0x44, 0x34, 0x2f, 0xe6,
	0x74, 0x2d, 0x46, 0x34, 0x1e, 0xd1, 0x53, 0xb8, 0x1c, 0xf0, 0xc0, 0xb6, 0xd0, 0xfb, 0xc1, 0x58,
	0xc3, 0x4b, 0x30, 0x8c, 0xb4, 0x01, 0x4b, 0x4f, 0x86, 0x91, 0x56, 0x5b, 0xfb, 0x76, 0x08, 0xce,
	0xa9, 0x1d, 0x15, 0x82, 0xf6, 0x3f, 0xb0, 0xb4, 0x7d, 0x5e, 0x27, 0xf2, 0x06, 0xe4, 0x6b, 0xd8,
	0x6c, 0xf7, 0xf0, 0x39, 0xef, 0xd7, 0x40, 0x0c, 0x36, 0x6e, 0x5b, 0xd3, 0x99, 0xc4, 0x0b, 0x38,
	0xe2, 0x0f, 0xf8, 0x36, 0x2c, 0x07, 0xd9, 0x64, 0x19, 0x63, 0x41, 0x20, 0x98, 0xed, 0x12, 0xcd,
	0xee, 0x97, 0x88, 0x7f, 0xcb, 0x2a, 0x5c, 0x0d, 0x7f, 0xf2, 0x2e, 0x7a, 0xf2, 0xdb, 0x08, 0x5c,
	0xde, 0x26, 0x3d, 0xec, 0x5e, 0xa1, 0x6d, 0xf6, 0xe0, 0xc2, 0x0d, 0x39, 0xa0, 0xbe, 0xa8, 0x9f,
	0xfa, 0x96, 0x21, 0xa9, 0x93, 0x1e, 0x76, 0xb8, 0xc9, 0x19, 0xf4, 0x04, 0x13, 0x70, 0x5e, 0x1a,
	0xe1, 0x89, 0xb9, 0x31, 0x9e, 0x58, 0x87, 0x8c, 0xef, 0x82, 0xda, 0xc1, 0x46, 0x7f, 0x9c, 0xd3,
	0xde, 0xa5, 0x1a, 0x36, 0x7c, 0xd9, 0x8e, 0xfb, 0xb2, 0xcd, 0xa2, 0x6d, 0x77, 0x34, 0xcc, 0x4b,
	0xe4, 0xd0, 0x66, 0x9c, 0x9f, 0xab, 0x1a, 0xda, 0x80, 0x4b, 0x56, 0xd7, 0x6e, 0x9a, 0xb6, 0xae,
	0x52, 0x7c, 0x4c, 0x55, 0xab, 0x83, 0x0f, 0xc9, 0x31, 0x67, 0xcf, 0x64, 0x6d, 0xb1, 0xaf, 0xda,
	0xc3, 0xc7, 0x74, 0x97, 0x2b, 0xe4, 0xbb, 0xb0, 0x38, 0x3e, 0x54, 0xd3, 0xba, 0x40, 0xde, 0xe7,
	0x2d, 0x34, 0xfc, 0xb0, 0x6c, 0x18, 0xbc, 0x6e, 0xef, 0x41, 0xca, 0xe1, 0xb6, 0xf3, 0x4d, 0x17,
	0x38, 0x97, 0x79, 0xc5, 0x5e, 0xc0, 0x72, 0x59, 0xd3, 0xfc, 0x65, 0xad, 0xb8, 0xdc, 0x19, 0x5c,
	0xb6, 0x61, 0xbe, 0x8d, 0x8c, 0xf2, 0xed, 0x32, 0x24, 0xdd, 0xaa, 0xda, 0x62, 0x94, 0xf7, 0x5e,
	0xa2, 0x5f, 0x56, 0xfb, 0xd6, 0x3d, 0x48, 0x3f, 0xac, 0x94, 0x1f, 0x1c, 0x54, 0xca, 0x35, 0x75,
	0xef, 0xa3, 0xdd, 0x0a, 0x12, 0x21, 0x37, 0x24, 0x50, 0xef, 0x3f, 0x7e, 0xf4, 0xe8, 0xf1, 0x4e,
	0x76, 0x06, 0xe5, 0x20, 0x3b, 0xa2, 0xd9, 0xcd, 0x0a, 0xb7, 0x1e, 0x42, 0x72, 0xbb, 0xfa, 0xb4,
	0xe2, 0x3c, 0x46, 0xb0, 0x30, 0x38, 0xa8, 0x3b, 0x8f, 0x77, 0x2a, 0xd9, 0x19, 0xb4, 0x08, 0x69,
	0x9f, 0xac, 0x72, 0x90, 0x15, 0x98, 0x25, 0x4f, 0x54, 0xde, 0xdd, 0xad, 0xec, 0x3c, 0xc8, 0x46,
	0xee, 0xfc, 0x85, 0x20, 0xc1, 0x10, 0x1f, 0xe0, 0x7a, 0x07, 0x35, 0x21, 0x33, 0x32, 0x17, 0xe8,
	0xaa, 0x97, 0xbd, 0x71, 0x96, 0xcf, 0x5f, 0x9b, 0xa0, 0xb5, 0x2d, 0x59, 0xfc, 0xe4, 0xf4, 0x2c,
	0x2a, 0xbc, 0x3e, 0x3d, 0x8b, 0x46, 0xba, 0xca, 0x57, 0xa7, 0x67, 0xd1, 0x78, 0xa1, 0x5b, 0xec,
	0x12, 0xad, 0x84, 0xbe, 0x14, 0x20, 0x17, 0xc4, 0x65, 0xe8, 0xba, 0x67, 0x31, 0x84, 0xeb, 0xf2,
	0x57, 0x37, 0x06, 0x5b, 0xca, 0xc6, 0x93, 0x0f, 0xb6, 0x9c, 0x2d, 0xa5, 0x62, 0x5a, 0xf4, 0x44,
	0xdd, 0xdd, 0x92, 0xff, 0xcf, 0x7c, 0x46, 0x98, 0xcf, 0x58, 0x57, 0x69, 0x2a, 0x47, 0xdc, 0xef,
	0x5a, 0xdf, 0xaf, 0x54, 0x68, 0x16, 0x7d, 0xed, 0x55, 0x92, 0x0a, 0x47, 0x52, 0xd1, 0x69, 0xf3,
	0x12, 0x7a, 0xc9, 0x79, 0x7e, 0x62, 0x48, 0x21, 0xcc, 0x98, 0x97, 0xa7, 0x5d, 0x71, 0x93, 0x11,
	0x0d, 0x4a, 0xc6, 0x0f, 0x02, 0xa0, 0x71, 0xb6, 0x40, 0xab, 0x3e, 0xa3, 0x41, 0x5c, 0x32, 0x25,
	0x11, 0x3d, 0xe6, 0x6f, 0x96, 0xf9, 0x9b, 0xef, 0x2a, 0xb6, 0x62, 0x28, 0x47, 0x4a, 0x5b, 0xb1,
	0xb8, 0xe7, 0x67, 0x83, 0x74, 0xd8, 0x5e, 0x3a, 0xdc, 0x76, 0x2d, 0x49, 0x05, 0xc3, 0x93, 0x72,
	0xca, 0x61, 0x79, 0x72, 0xd3, 0x24, 0x15, 0xda, 0x45, 0x97, 0x01, 0x4a, 0x52, 0xc1, 0x2a, 0xba,
	0x33, 0xef, 0x8c, 0x7b, 0x09, 0xfd, 0x24, 0xc0, 0x62, 0x59, 0xd3, 0x46, 0xf6, 0xc4, 0xd0, 0x29,
	0x9c, 0x82, 0xe2, 0x95, 0xc0, 0x60, 0xcc, 0x31, 0x18, 0xc8, 0x56, 0x5a, 0x0a, 0x51, 0x0c, 0x85,
	0x2a, 0xa6, 0xa2, 0x2b, 0x56, 0xbf, 0xb6, 0x7a, 0xc1, 0x2e, 0xfa, 0x62, 0x6f, 0x15, 0xd9, 0x1e,
	0x59, 0x92, 0x0a, 0xa4, 0x48, 0x4c, 0x9d, 0x83, 0x71, 0x31, 0xd0, 0xa2, 0x7f, 0x65, 0x2c, 0x49,
	0x05, 0xb3, 0xe8, 0x2d, 0x88, 0x25, 0xa9, 0xa0, 0x17, 0xfb, 0x0b, 0x21, 0xc7, 0xc5, 0xe8, 0xd5,
	0x05, 0xce, 0x64, 0xc8, 0x80, 0xc5, 0x31, 0xe6, 0x41, 0xcb, 0xa1, 0xec, 0x82, 0x8f, 0xf2, 0xa1,
	0xa0, 0x65, 0x89, 0xa1, 0x8a, 0xf1, 0x66, 0x68, 0x72, 0x14, 0x99, 0x91, 0xce, 0x44, 0x16, 0x2c,
	0x3e, 0xc0, 0xc6, 0x45, 0xbc, 0x4d, 0x4e, 0x24, 0xf7, 0x18, 0x9f, 0xe4, 0x91, 0x04, 0xec, 0x39,
	0x65, 0xc3, 0x40, 0x13, 0xed, 0x8e, 0xb4, 0x7e, 0x20, 0x2f, 0xcb, 0x19, 0xe6, 0x3b, 0xc1, 0x7c,
	0xcf, 0x30, 0xcf, 0x33, 0xe8, 0x53, 0x21, 0x78, 0x11, 0x60, 0x54, 0x8c, 0xfe, 0x3d, 0x79, 0x98,
	0xfa, 0x3f, 0xec, 0xf9, 0xf5, 0xf3, 0x5c, 0x73, 0xe7, 0x2e, 0x19, 0x34, 0x77, 0x2f, 0x61, 0x29,
	0x64, 0x7b, 0x41, 0x37, 0x3c, 0xe3, 0xe1, 0x0b, 0xce, 0x94, 0x8c, 0x73, 0xc7, 0x10, 0xe4, 0xf8,
	0x73, 0x01, 0xfe, 0x15, 0xbc, 0xb3, 0xa2, 0xb5, 0x50, 0x54, 0xde, 0x56, 0x9b, 0x9f, 0xb0, 0x88,
	0xc8, 0x77, 0x99, 0xd7, 0x54, 0x9f, 0xff, 0xec, 0xfe, 0x8c, 0xac, 0xfa, 0x06, 0xde, 0x37, 0x2b,
	0x83, 0xa1, 0x46, 0x5f, 0x0b, 0x20, 0x86, 0xfd, 0xfe, 0xf9, 0x0b, 0x32, 0xe1, 0x37, 0x72, 0x4a,
	0x36, 0xfe, 0xc7, 0xe2, 0x9a, 0x1f, 0xc4, 0x45, 0x79, 0x5c, 0x52, 0x70, 0x5c, 0xb4, 0xc8, 0x06,
	0x54, 0xb9, 0x6d, 0x97, 0xd0, 0xef, 0x02, 0x48, 0xe3, 0xec, 0x77, 0x40, 0x68, 0xb3, 0xe2, 0xed,
	0x34, 0x6f, 0xc9, 0x94, 0x9f, 0x71, 0x8e, 0x49, 0xb3, 0xd8, 0x16, 0x1c, 0xaa, 0xa4, 0x3e, 0xb2,
	0x24, 0x17, 0x27, 0x4b, 0x27, 0x7e, 0x9b, 0xd6, 0x4d, 0xeb, 0x42, 0xd4, 0xf9, 0x87, 0x00, 0xb9,
	0x7d, 0x4b, 0xab, 0x53, 0xfc, 0x8e, 0xd8, 0xf3, 0x1b, 0x8e, 0x6c, 0x81, 0x21, 0xcb, 0x35, 0x95,
	0x60, 0xfe, 0x3c, 0x19, 0xfd, 0x4d, 0x7c, 0xf6, 0x0f, 0x11, 0xea, 0xc7, 0xe8, 0x4f, 0x01, 0xd6,
	0xa6, 0x15, 0x97, 0xed, 0xa2, 0x6f, 0x59, 0xdf, 0xd7, 0x3c, 0x0b, 0x99, 0x90, 0xfa, 0x9a, 0x6f,
	0x55, 0x5f, 0xb6, 0x40, 0x5f, 0xa4, 0xc6, 0xf9, 0xd8, 0xab, 0xd3, 0xb3, 0xe8, 0x6f, 0x27, 0x5b,
	0xd9, 0xef, 0xdf, 0xac, 0x08, 0x3f, 0xbe, 0x59, 0x11, 0x7e, 0x79, 0xb3, 0x22, 0x7c, 0xf1, 0xeb,
	0xca, 0xcc, 0xf3, 0x18, 0xff, 0x93, 0xe5, 0xbf, 0x7f, 0x07, 0x00, 0x00, 0xff, 0xff, 0x14, 0x31,
	0x17, 0x14, 0xb6, 0x11, 0x00, 0x00,
}
