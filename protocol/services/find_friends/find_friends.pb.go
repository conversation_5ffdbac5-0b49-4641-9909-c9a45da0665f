// Code generated by protoc-gen-go. DO NOT EDIT.
// source: find_friends/find_friends.proto

package FindFriends

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type Gender int32

const (
	Gender_Gender_UNKNOWN Gender = 0
	Gender_Gender_FEMALE  Gender = 1
	Gender_Gender_MALE    Gender = 2
)

var Gender_name = map[int32]string{
	0: "Gender_UNKNOWN",
	1: "Gender_FEMALE",
	2: "Gender_MALE",
}
var Gender_value = map[string]int32{
	"Gender_UNKNOWN": 0,
	"Gender_FEMALE":  1,
	"Gender_MALE":    2,
}

func (x Gender) String() string {
	return proto.EnumName(Gender_name, int32(x))
}
func (Gender) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{0}
}

type GenderFilter int32

const (
	GenderFilter_GenderFilter_ZERO         GenderFilter = 0
	GenderFilter_GenderFilter_MALE_ONLY    GenderFilter = 1
	GenderFilter_GenderFilter_FEMALE_ONLY  GenderFilter = 2
	GenderFilter_GenderFilter_UNRESTRICTED GenderFilter = 3
)

var GenderFilter_name = map[int32]string{
	0: "GenderFilter_ZERO",
	1: "GenderFilter_MALE_ONLY",
	2: "GenderFilter_FEMALE_ONLY",
	3: "GenderFilter_UNRESTRICTED",
}
var GenderFilter_value = map[string]int32{
	"GenderFilter_ZERO":         0,
	"GenderFilter_MALE_ONLY":    1,
	"GenderFilter_FEMALE_ONLY":  2,
	"GenderFilter_UNRESTRICTED": 3,
}

func (x GenderFilter) String() string {
	return proto.EnumName(GenderFilter_name, int32(x))
}
func (GenderFilter) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{1}
}

type UserInfo_AutoPlayVoice int32

const (
	UserInfo_AutoPlayVoice_UNSPECIFIC UserInfo_AutoPlayVoice = 0
	UserInfo_AutoPlayVoice_ENABLED    UserInfo_AutoPlayVoice = 1
	UserInfo_AutoPlayVoice_DISABLED   UserInfo_AutoPlayVoice = 2
)

var UserInfo_AutoPlayVoice_name = map[int32]string{
	0: "AutoPlayVoice_UNSPECIFIC",
	1: "AutoPlayVoice_ENABLED",
	2: "AutoPlayVoice_DISABLED",
}
var UserInfo_AutoPlayVoice_value = map[string]int32{
	"AutoPlayVoice_UNSPECIFIC": 0,
	"AutoPlayVoice_ENABLED":    1,
	"AutoPlayVoice_DISABLED":   2,
}

func (x UserInfo_AutoPlayVoice) String() string {
	return proto.EnumName(UserInfo_AutoPlayVoice_name, int32(x))
}
func (UserInfo_AutoPlayVoice) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{1, 0}
}

type Location struct {
	AdCode               uint32   `protobuf:"varint,1,opt,name=ad_code,json=adCode,proto3" json:"ad_code,omitempty"`
	Country              string   `protobuf:"bytes,2,opt,name=country,proto3" json:"country,omitempty"`
	Province             string   `protobuf:"bytes,3,opt,name=province,proto3" json:"province,omitempty"`
	City                 string   `protobuf:"bytes,4,opt,name=city,proto3" json:"city,omitempty"`
	District             string   `protobuf:"bytes,5,opt,name=district,proto3" json:"district,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Location) Reset()         { *m = Location{} }
func (m *Location) String() string { return proto.CompactTextString(m) }
func (*Location) ProtoMessage()    {}
func (*Location) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{0}
}
func (m *Location) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Location.Unmarshal(m, b)
}
func (m *Location) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Location.Marshal(b, m, deterministic)
}
func (dst *Location) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Location.Merge(dst, src)
}
func (m *Location) XXX_Size() int {
	return xxx_messageInfo_Location.Size(m)
}
func (m *Location) XXX_DiscardUnknown() {
	xxx_messageInfo_Location.DiscardUnknown(m)
}

var xxx_messageInfo_Location proto.InternalMessageInfo

func (m *Location) GetAdCode() uint32 {
	if m != nil {
		return m.AdCode
	}
	return 0
}

func (m *Location) GetCountry() string {
	if m != nil {
		return m.Country
	}
	return ""
}

func (m *Location) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *Location) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *Location) GetDistrict() string {
	if m != nil {
		return m.District
	}
	return ""
}

type UserInfo struct {
	UserId               uint32                 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PhotoUrls            []string               `protobuf:"bytes,2,rep,name=photo_urls,json=photoUrls,proto3" json:"photo_urls,omitempty"`
	VoiceUrl             string                 `protobuf:"bytes,3,opt,name=voice_url,json=voiceUrl,proto3" json:"voice_url,omitempty"`
	Location             *Location              `protobuf:"bytes,4,opt,name=location,proto3" json:"location,omitempty"`
	GenderFilter         GenderFilter           `protobuf:"varint,5,opt,name=gender_filter,json=genderFilter,proto3,enum=FindFriends.GenderFilter" json:"gender_filter,omitempty"`
	PlayingGames         []string               `protobuf:"bytes,6,rep,name=playing_games,json=playingGames,proto3" json:"playing_games,omitempty"`
	AutoPlayVoice        UserInfo_AutoPlayVoice `protobuf:"varint,7,opt,name=auto_play_voice,json=autoPlayVoice,proto3,enum=FindFriends.UserInfo_AutoPlayVoice" json:"auto_play_voice,omitempty"`
	Gender               Gender                 `protobuf:"varint,8,opt,name=gender,proto3,enum=FindFriends.Gender" json:"gender,omitempty"`
	VoiceDuration        uint32                 `protobuf:"varint,9,opt,name=voice_duration,json=voiceDuration,proto3" json:"voice_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *UserInfo) Reset()         { *m = UserInfo{} }
func (m *UserInfo) String() string { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()    {}
func (*UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{1}
}
func (m *UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo.Unmarshal(m, b)
}
func (m *UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo.Marshal(b, m, deterministic)
}
func (dst *UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo.Merge(dst, src)
}
func (m *UserInfo) XXX_Size() int {
	return xxx_messageInfo_UserInfo.Size(m)
}
func (m *UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo proto.InternalMessageInfo

func (m *UserInfo) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *UserInfo) GetPhotoUrls() []string {
	if m != nil {
		return m.PhotoUrls
	}
	return nil
}

func (m *UserInfo) GetVoiceUrl() string {
	if m != nil {
		return m.VoiceUrl
	}
	return ""
}

func (m *UserInfo) GetLocation() *Location {
	if m != nil {
		return m.Location
	}
	return nil
}

func (m *UserInfo) GetGenderFilter() GenderFilter {
	if m != nil {
		return m.GenderFilter
	}
	return GenderFilter_GenderFilter_ZERO
}

func (m *UserInfo) GetPlayingGames() []string {
	if m != nil {
		return m.PlayingGames
	}
	return nil
}

func (m *UserInfo) GetAutoPlayVoice() UserInfo_AutoPlayVoice {
	if m != nil {
		return m.AutoPlayVoice
	}
	return UserInfo_AutoPlayVoice_UNSPECIFIC
}

func (m *UserInfo) GetGender() Gender {
	if m != nil {
		return m.Gender
	}
	return Gender_Gender_UNKNOWN
}

func (m *UserInfo) GetVoiceDuration() uint32 {
	if m != nil {
		return m.VoiceDuration
	}
	return 0
}

type GetUserRequest struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRequest) Reset()         { *m = GetUserRequest{} }
func (m *GetUserRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserRequest) ProtoMessage()    {}
func (*GetUserRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{2}
}
func (m *GetUserRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRequest.Unmarshal(m, b)
}
func (m *GetUserRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRequest.Merge(dst, src)
}
func (m *GetUserRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserRequest.Size(m)
}
func (m *GetUserRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRequest proto.InternalMessageInfo

func (m *GetUserRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type GetUserResponse struct {
	UserInfo             *UserInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetUserResponse) Reset()         { *m = GetUserResponse{} }
func (m *GetUserResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserResponse) ProtoMessage()    {}
func (*GetUserResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{3}
}
func (m *GetUserResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserResponse.Unmarshal(m, b)
}
func (m *GetUserResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserResponse.Merge(dst, src)
}
func (m *GetUserResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserResponse.Size(m)
}
func (m *GetUserResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserResponse proto.InternalMessageInfo

func (m *GetUserResponse) GetUserInfo() *UserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

type CreateOrUpdateUserRequest struct {
	UserInfo             *UserInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	ClearPlayingGames    bool      `protobuf:"varint,2,opt,name=clear_playing_games,json=clearPlayingGames,proto3" json:"clear_playing_games,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *CreateOrUpdateUserRequest) Reset()         { *m = CreateOrUpdateUserRequest{} }
func (m *CreateOrUpdateUserRequest) String() string { return proto.CompactTextString(m) }
func (*CreateOrUpdateUserRequest) ProtoMessage()    {}
func (*CreateOrUpdateUserRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{4}
}
func (m *CreateOrUpdateUserRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateOrUpdateUserRequest.Unmarshal(m, b)
}
func (m *CreateOrUpdateUserRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateOrUpdateUserRequest.Marshal(b, m, deterministic)
}
func (dst *CreateOrUpdateUserRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateOrUpdateUserRequest.Merge(dst, src)
}
func (m *CreateOrUpdateUserRequest) XXX_Size() int {
	return xxx_messageInfo_CreateOrUpdateUserRequest.Size(m)
}
func (m *CreateOrUpdateUserRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateOrUpdateUserRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateOrUpdateUserRequest proto.InternalMessageInfo

func (m *CreateOrUpdateUserRequest) GetUserInfo() *UserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *CreateOrUpdateUserRequest) GetClearPlayingGames() bool {
	if m != nil {
		return m.ClearPlayingGames
	}
	return false
}

type CreateOrUpdateUserResponse struct {
	UserInfo             *UserInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *CreateOrUpdateUserResponse) Reset()         { *m = CreateOrUpdateUserResponse{} }
func (m *CreateOrUpdateUserResponse) String() string { return proto.CompactTextString(m) }
func (*CreateOrUpdateUserResponse) ProtoMessage()    {}
func (*CreateOrUpdateUserResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{5}
}
func (m *CreateOrUpdateUserResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateOrUpdateUserResponse.Unmarshal(m, b)
}
func (m *CreateOrUpdateUserResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateOrUpdateUserResponse.Marshal(b, m, deterministic)
}
func (dst *CreateOrUpdateUserResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateOrUpdateUserResponse.Merge(dst, src)
}
func (m *CreateOrUpdateUserResponse) XXX_Size() int {
	return xxx_messageInfo_CreateOrUpdateUserResponse.Size(m)
}
func (m *CreateOrUpdateUserResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateOrUpdateUserResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateOrUpdateUserResponse proto.InternalMessageInfo

func (m *CreateOrUpdateUserResponse) GetUserInfo() *UserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

type UpdatePhotoRequest struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Index                uint32   `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	PhotoUrl             string   `protobuf:"bytes,3,opt,name=photo_url,json=photoUrl,proto3" json:"photo_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePhotoRequest) Reset()         { *m = UpdatePhotoRequest{} }
func (m *UpdatePhotoRequest) String() string { return proto.CompactTextString(m) }
func (*UpdatePhotoRequest) ProtoMessage()    {}
func (*UpdatePhotoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{6}
}
func (m *UpdatePhotoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePhotoRequest.Unmarshal(m, b)
}
func (m *UpdatePhotoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePhotoRequest.Marshal(b, m, deterministic)
}
func (dst *UpdatePhotoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePhotoRequest.Merge(dst, src)
}
func (m *UpdatePhotoRequest) XXX_Size() int {
	return xxx_messageInfo_UpdatePhotoRequest.Size(m)
}
func (m *UpdatePhotoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePhotoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePhotoRequest proto.InternalMessageInfo

func (m *UpdatePhotoRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *UpdatePhotoRequest) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *UpdatePhotoRequest) GetPhotoUrl() string {
	if m != nil {
		return m.PhotoUrl
	}
	return ""
}

type UpdatePhotoResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePhotoResponse) Reset()         { *m = UpdatePhotoResponse{} }
func (m *UpdatePhotoResponse) String() string { return proto.CompactTextString(m) }
func (*UpdatePhotoResponse) ProtoMessage()    {}
func (*UpdatePhotoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{7}
}
func (m *UpdatePhotoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePhotoResponse.Unmarshal(m, b)
}
func (m *UpdatePhotoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePhotoResponse.Marshal(b, m, deterministic)
}
func (dst *UpdatePhotoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePhotoResponse.Merge(dst, src)
}
func (m *UpdatePhotoResponse) XXX_Size() int {
	return xxx_messageInfo_UpdatePhotoResponse.Size(m)
}
func (m *UpdatePhotoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePhotoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePhotoResponse proto.InternalMessageInfo

type GetRecommendUsersRequest struct {
	UserId               uint32       `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	FilterUids           []uint32     `protobuf:"varint,2,rep,packed,name=filter_uids,json=filterUids,proto3" json:"filter_uids,omitempty"`
	Count                uint32       `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	GenderFilter         GenderFilter `protobuf:"varint,4,opt,name=gender_filter,json=genderFilter,proto3,enum=FindFriends.GenderFilter" json:"gender_filter,omitempty"`
	Location             *Location    `protobuf:"bytes,5,opt,name=location,proto3" json:"location,omitempty"`
	SessionId            string       `protobuf:"bytes,6,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRecommendUsersRequest) Reset()         { *m = GetRecommendUsersRequest{} }
func (m *GetRecommendUsersRequest) String() string { return proto.CompactTextString(m) }
func (*GetRecommendUsersRequest) ProtoMessage()    {}
func (*GetRecommendUsersRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{8}
}
func (m *GetRecommendUsersRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendUsersRequest.Unmarshal(m, b)
}
func (m *GetRecommendUsersRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendUsersRequest.Marshal(b, m, deterministic)
}
func (dst *GetRecommendUsersRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendUsersRequest.Merge(dst, src)
}
func (m *GetRecommendUsersRequest) XXX_Size() int {
	return xxx_messageInfo_GetRecommendUsersRequest.Size(m)
}
func (m *GetRecommendUsersRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendUsersRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendUsersRequest proto.InternalMessageInfo

func (m *GetRecommendUsersRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetRecommendUsersRequest) GetFilterUids() []uint32 {
	if m != nil {
		return m.FilterUids
	}
	return nil
}

func (m *GetRecommendUsersRequest) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetRecommendUsersRequest) GetGenderFilter() GenderFilter {
	if m != nil {
		return m.GenderFilter
	}
	return GenderFilter_GenderFilter_ZERO
}

func (m *GetRecommendUsersRequest) GetLocation() *Location {
	if m != nil {
		return m.Location
	}
	return nil
}

func (m *GetRecommendUsersRequest) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

type GetRecommendUsersResponse struct {
	Users                []*UserInfo `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetRecommendUsersResponse) Reset()         { *m = GetRecommendUsersResponse{} }
func (m *GetRecommendUsersResponse) String() string { return proto.CompactTextString(m) }
func (*GetRecommendUsersResponse) ProtoMessage()    {}
func (*GetRecommendUsersResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{9}
}
func (m *GetRecommendUsersResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendUsersResponse.Unmarshal(m, b)
}
func (m *GetRecommendUsersResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendUsersResponse.Marshal(b, m, deterministic)
}
func (dst *GetRecommendUsersResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendUsersResponse.Merge(dst, src)
}
func (m *GetRecommendUsersResponse) XXX_Size() int {
	return xxx_messageInfo_GetRecommendUsersResponse.Size(m)
}
func (m *GetRecommendUsersResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendUsersResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendUsersResponse proto.InternalMessageInfo

func (m *GetRecommendUsersResponse) GetUsers() []*UserInfo {
	if m != nil {
		return m.Users
	}
	return nil
}

type LikeRequest struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ThrowUids            []uint32 `protobuf:"varint,2,rep,packed,name=throw_uids,json=throwUids,proto3" json:"throw_uids,omitempty"`
	LikeUid              uint32   `protobuf:"varint,3,opt,name=like_uid,json=likeUid,proto3" json:"like_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LikeRequest) Reset()         { *m = LikeRequest{} }
func (m *LikeRequest) String() string { return proto.CompactTextString(m) }
func (*LikeRequest) ProtoMessage()    {}
func (*LikeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{10}
}
func (m *LikeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LikeRequest.Unmarshal(m, b)
}
func (m *LikeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LikeRequest.Marshal(b, m, deterministic)
}
func (dst *LikeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LikeRequest.Merge(dst, src)
}
func (m *LikeRequest) XXX_Size() int {
	return xxx_messageInfo_LikeRequest.Size(m)
}
func (m *LikeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LikeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LikeRequest proto.InternalMessageInfo

func (m *LikeRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *LikeRequest) GetThrowUids() []uint32 {
	if m != nil {
		return m.ThrowUids
	}
	return nil
}

func (m *LikeRequest) GetLikeUid() uint32 {
	if m != nil {
		return m.LikeUid
	}
	return 0
}

type LikeResponse struct {
	LikeIsEffectual      bool     `protobuf:"varint,1,opt,name=like_is_effectual,json=likeIsEffectual,proto3" json:"like_is_effectual,omitempty"`
	IsFree               bool     `protobuf:"varint,2,opt,name=is_free,json=isFree,proto3" json:"is_free,omitempty"`
	FreeLikeQuota        uint32   `protobuf:"varint,3,opt,name=free_like_quota,json=freeLikeQuota,proto3" json:"free_like_quota,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LikeResponse) Reset()         { *m = LikeResponse{} }
func (m *LikeResponse) String() string { return proto.CompactTextString(m) }
func (*LikeResponse) ProtoMessage()    {}
func (*LikeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{11}
}
func (m *LikeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LikeResponse.Unmarshal(m, b)
}
func (m *LikeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LikeResponse.Marshal(b, m, deterministic)
}
func (dst *LikeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LikeResponse.Merge(dst, src)
}
func (m *LikeResponse) XXX_Size() int {
	return xxx_messageInfo_LikeResponse.Size(m)
}
func (m *LikeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LikeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LikeResponse proto.InternalMessageInfo

func (m *LikeResponse) GetLikeIsEffectual() bool {
	if m != nil {
		return m.LikeIsEffectual
	}
	return false
}

func (m *LikeResponse) GetIsFree() bool {
	if m != nil {
		return m.IsFree
	}
	return false
}

func (m *LikeResponse) GetFreeLikeQuota() uint32 {
	if m != nil {
		return m.FreeLikeQuota
	}
	return 0
}

type CheckLikedRequest struct {
	FromUserId           uint32   `protobuf:"varint,1,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id,omitempty"`
	TargetUserId         uint32   `protobuf:"varint,2,opt,name=target_user_id,json=targetUserId,proto3" json:"target_user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckLikedRequest) Reset()         { *m = CheckLikedRequest{} }
func (m *CheckLikedRequest) String() string { return proto.CompactTextString(m) }
func (*CheckLikedRequest) ProtoMessage()    {}
func (*CheckLikedRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{12}
}
func (m *CheckLikedRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckLikedRequest.Unmarshal(m, b)
}
func (m *CheckLikedRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckLikedRequest.Marshal(b, m, deterministic)
}
func (dst *CheckLikedRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckLikedRequest.Merge(dst, src)
}
func (m *CheckLikedRequest) XXX_Size() int {
	return xxx_messageInfo_CheckLikedRequest.Size(m)
}
func (m *CheckLikedRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckLikedRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckLikedRequest proto.InternalMessageInfo

func (m *CheckLikedRequest) GetFromUserId() uint32 {
	if m != nil {
		return m.FromUserId
	}
	return 0
}

func (m *CheckLikedRequest) GetTargetUserId() uint32 {
	if m != nil {
		return m.TargetUserId
	}
	return 0
}

type CheckLikedResponse struct {
	Liked                bool     `protobuf:"varint,1,opt,name=liked,proto3" json:"liked,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckLikedResponse) Reset()         { *m = CheckLikedResponse{} }
func (m *CheckLikedResponse) String() string { return proto.CompactTextString(m) }
func (*CheckLikedResponse) ProtoMessage()    {}
func (*CheckLikedResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{13}
}
func (m *CheckLikedResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckLikedResponse.Unmarshal(m, b)
}
func (m *CheckLikedResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckLikedResponse.Marshal(b, m, deterministic)
}
func (dst *CheckLikedResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckLikedResponse.Merge(dst, src)
}
func (m *CheckLikedResponse) XXX_Size() int {
	return xxx_messageInfo_CheckLikedResponse.Size(m)
}
func (m *CheckLikedResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckLikedResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckLikedResponse proto.InternalMessageInfo

func (m *CheckLikedResponse) GetLiked() bool {
	if m != nil {
		return m.Liked
	}
	return false
}

type CheckBeenLikedRequest struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TestUidSet           []uint32 `protobuf:"varint,2,rep,packed,name=test_uid_set,json=testUidSet,proto3" json:"test_uid_set,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckBeenLikedRequest) Reset()         { *m = CheckBeenLikedRequest{} }
func (m *CheckBeenLikedRequest) String() string { return proto.CompactTextString(m) }
func (*CheckBeenLikedRequest) ProtoMessage()    {}
func (*CheckBeenLikedRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{14}
}
func (m *CheckBeenLikedRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckBeenLikedRequest.Unmarshal(m, b)
}
func (m *CheckBeenLikedRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckBeenLikedRequest.Marshal(b, m, deterministic)
}
func (dst *CheckBeenLikedRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckBeenLikedRequest.Merge(dst, src)
}
func (m *CheckBeenLikedRequest) XXX_Size() int {
	return xxx_messageInfo_CheckBeenLikedRequest.Size(m)
}
func (m *CheckBeenLikedRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckBeenLikedRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckBeenLikedRequest proto.InternalMessageInfo

func (m *CheckBeenLikedRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *CheckBeenLikedRequest) GetTestUidSet() []uint32 {
	if m != nil {
		return m.TestUidSet
	}
	return nil
}

type CheckBeenLikedResponse struct {
	LikedByUidSet        []uint32 `protobuf:"varint,1,rep,packed,name=liked_by_uid_set,json=likedByUidSet,proto3" json:"liked_by_uid_set,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckBeenLikedResponse) Reset()         { *m = CheckBeenLikedResponse{} }
func (m *CheckBeenLikedResponse) String() string { return proto.CompactTextString(m) }
func (*CheckBeenLikedResponse) ProtoMessage()    {}
func (*CheckBeenLikedResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{15}
}
func (m *CheckBeenLikedResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckBeenLikedResponse.Unmarshal(m, b)
}
func (m *CheckBeenLikedResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckBeenLikedResponse.Marshal(b, m, deterministic)
}
func (dst *CheckBeenLikedResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckBeenLikedResponse.Merge(dst, src)
}
func (m *CheckBeenLikedResponse) XXX_Size() int {
	return xxx_messageInfo_CheckBeenLikedResponse.Size(m)
}
func (m *CheckBeenLikedResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckBeenLikedResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckBeenLikedResponse proto.InternalMessageInfo

func (m *CheckBeenLikedResponse) GetLikedByUidSet() []uint32 {
	if m != nil {
		return m.LikedByUidSet
	}
	return nil
}

// 清理相互的被喜欢状态
type ClearMutualLikesMeRequest struct {
	UserId_A             uint32   `protobuf:"varint,1,opt,name=user_id_A,json=userIdA,proto3" json:"user_id_A,omitempty"`
	UserId_B             uint32   `protobuf:"varint,2,opt,name=user_id_B,json=userIdB,proto3" json:"user_id_B,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearMutualLikesMeRequest) Reset()         { *m = ClearMutualLikesMeRequest{} }
func (m *ClearMutualLikesMeRequest) String() string { return proto.CompactTextString(m) }
func (*ClearMutualLikesMeRequest) ProtoMessage()    {}
func (*ClearMutualLikesMeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{16}
}
func (m *ClearMutualLikesMeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearMutualLikesMeRequest.Unmarshal(m, b)
}
func (m *ClearMutualLikesMeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearMutualLikesMeRequest.Marshal(b, m, deterministic)
}
func (dst *ClearMutualLikesMeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearMutualLikesMeRequest.Merge(dst, src)
}
func (m *ClearMutualLikesMeRequest) XXX_Size() int {
	return xxx_messageInfo_ClearMutualLikesMeRequest.Size(m)
}
func (m *ClearMutualLikesMeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearMutualLikesMeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ClearMutualLikesMeRequest proto.InternalMessageInfo

func (m *ClearMutualLikesMeRequest) GetUserId_A() uint32 {
	if m != nil {
		return m.UserId_A
	}
	return 0
}

func (m *ClearMutualLikesMeRequest) GetUserId_B() uint32 {
	if m != nil {
		return m.UserId_B
	}
	return 0
}

type ClearMutualLikesMeResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearMutualLikesMeResponse) Reset()         { *m = ClearMutualLikesMeResponse{} }
func (m *ClearMutualLikesMeResponse) String() string { return proto.CompactTextString(m) }
func (*ClearMutualLikesMeResponse) ProtoMessage()    {}
func (*ClearMutualLikesMeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{17}
}
func (m *ClearMutualLikesMeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearMutualLikesMeResponse.Unmarshal(m, b)
}
func (m *ClearMutualLikesMeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearMutualLikesMeResponse.Marshal(b, m, deterministic)
}
func (dst *ClearMutualLikesMeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearMutualLikesMeResponse.Merge(dst, src)
}
func (m *ClearMutualLikesMeResponse) XXX_Size() int {
	return xxx_messageInfo_ClearMutualLikesMeResponse.Size(m)
}
func (m *ClearMutualLikesMeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearMutualLikesMeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ClearMutualLikesMeResponse proto.InternalMessageInfo

type GetTodayLikedCountRequest struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Timestamp            uint32   `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTodayLikedCountRequest) Reset()         { *m = GetTodayLikedCountRequest{} }
func (m *GetTodayLikedCountRequest) String() string { return proto.CompactTextString(m) }
func (*GetTodayLikedCountRequest) ProtoMessage()    {}
func (*GetTodayLikedCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{18}
}
func (m *GetTodayLikedCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTodayLikedCountRequest.Unmarshal(m, b)
}
func (m *GetTodayLikedCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTodayLikedCountRequest.Marshal(b, m, deterministic)
}
func (dst *GetTodayLikedCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTodayLikedCountRequest.Merge(dst, src)
}
func (m *GetTodayLikedCountRequest) XXX_Size() int {
	return xxx_messageInfo_GetTodayLikedCountRequest.Size(m)
}
func (m *GetTodayLikedCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTodayLikedCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTodayLikedCountRequest proto.InternalMessageInfo

func (m *GetTodayLikedCountRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetTodayLikedCountRequest) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

type GetTodayLikedCountResponse struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTodayLikedCountResponse) Reset()         { *m = GetTodayLikedCountResponse{} }
func (m *GetTodayLikedCountResponse) String() string { return proto.CompactTextString(m) }
func (*GetTodayLikedCountResponse) ProtoMessage()    {}
func (*GetTodayLikedCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{19}
}
func (m *GetTodayLikedCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTodayLikedCountResponse.Unmarshal(m, b)
}
func (m *GetTodayLikedCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTodayLikedCountResponse.Marshal(b, m, deterministic)
}
func (dst *GetTodayLikedCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTodayLikedCountResponse.Merge(dst, src)
}
func (m *GetTodayLikedCountResponse) XXX_Size() int {
	return xxx_messageInfo_GetTodayLikedCountResponse.Size(m)
}
func (m *GetTodayLikedCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTodayLikedCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetTodayLikedCountResponse proto.InternalMessageInfo

func (m *GetTodayLikedCountResponse) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetFreeLikeQuotaRequest struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Timestamp            uint32   `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFreeLikeQuotaRequest) Reset()         { *m = GetFreeLikeQuotaRequest{} }
func (m *GetFreeLikeQuotaRequest) String() string { return proto.CompactTextString(m) }
func (*GetFreeLikeQuotaRequest) ProtoMessage()    {}
func (*GetFreeLikeQuotaRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{20}
}
func (m *GetFreeLikeQuotaRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFreeLikeQuotaRequest.Unmarshal(m, b)
}
func (m *GetFreeLikeQuotaRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFreeLikeQuotaRequest.Marshal(b, m, deterministic)
}
func (dst *GetFreeLikeQuotaRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFreeLikeQuotaRequest.Merge(dst, src)
}
func (m *GetFreeLikeQuotaRequest) XXX_Size() int {
	return xxx_messageInfo_GetFreeLikeQuotaRequest.Size(m)
}
func (m *GetFreeLikeQuotaRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFreeLikeQuotaRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetFreeLikeQuotaRequest proto.InternalMessageInfo

func (m *GetFreeLikeQuotaRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetFreeLikeQuotaRequest) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

type GetFreeLikeQuotaResponse struct {
	Quota                uint32   `protobuf:"varint,1,opt,name=quota,proto3" json:"quota,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFreeLikeQuotaResponse) Reset()         { *m = GetFreeLikeQuotaResponse{} }
func (m *GetFreeLikeQuotaResponse) String() string { return proto.CompactTextString(m) }
func (*GetFreeLikeQuotaResponse) ProtoMessage()    {}
func (*GetFreeLikeQuotaResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{21}
}
func (m *GetFreeLikeQuotaResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFreeLikeQuotaResponse.Unmarshal(m, b)
}
func (m *GetFreeLikeQuotaResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFreeLikeQuotaResponse.Marshal(b, m, deterministic)
}
func (dst *GetFreeLikeQuotaResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFreeLikeQuotaResponse.Merge(dst, src)
}
func (m *GetFreeLikeQuotaResponse) XXX_Size() int {
	return xxx_messageInfo_GetFreeLikeQuotaResponse.Size(m)
}
func (m *GetFreeLikeQuotaResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFreeLikeQuotaResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetFreeLikeQuotaResponse proto.InternalMessageInfo

func (m *GetFreeLikeQuotaResponse) GetQuota() uint32 {
	if m != nil {
		return m.Quota
	}
	return 0
}

type GrantFreeLikeQuotaRequest struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Timestamp            uint32   `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Quota                uint32   `protobuf:"varint,3,opt,name=quota,proto3" json:"quota,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GrantFreeLikeQuotaRequest) Reset()         { *m = GrantFreeLikeQuotaRequest{} }
func (m *GrantFreeLikeQuotaRequest) String() string { return proto.CompactTextString(m) }
func (*GrantFreeLikeQuotaRequest) ProtoMessage()    {}
func (*GrantFreeLikeQuotaRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{22}
}
func (m *GrantFreeLikeQuotaRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantFreeLikeQuotaRequest.Unmarshal(m, b)
}
func (m *GrantFreeLikeQuotaRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantFreeLikeQuotaRequest.Marshal(b, m, deterministic)
}
func (dst *GrantFreeLikeQuotaRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantFreeLikeQuotaRequest.Merge(dst, src)
}
func (m *GrantFreeLikeQuotaRequest) XXX_Size() int {
	return xxx_messageInfo_GrantFreeLikeQuotaRequest.Size(m)
}
func (m *GrantFreeLikeQuotaRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantFreeLikeQuotaRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GrantFreeLikeQuotaRequest proto.InternalMessageInfo

func (m *GrantFreeLikeQuotaRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GrantFreeLikeQuotaRequest) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *GrantFreeLikeQuotaRequest) GetQuota() uint32 {
	if m != nil {
		return m.Quota
	}
	return 0
}

type GrantFreeLikeQuotaResponse struct {
	Granted              bool     `protobuf:"varint,1,opt,name=granted,proto3" json:"granted,omitempty"`
	Quota                uint32   `protobuf:"varint,2,opt,name=quota,proto3" json:"quota,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GrantFreeLikeQuotaResponse) Reset()         { *m = GrantFreeLikeQuotaResponse{} }
func (m *GrantFreeLikeQuotaResponse) String() string { return proto.CompactTextString(m) }
func (*GrantFreeLikeQuotaResponse) ProtoMessage()    {}
func (*GrantFreeLikeQuotaResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{23}
}
func (m *GrantFreeLikeQuotaResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantFreeLikeQuotaResponse.Unmarshal(m, b)
}
func (m *GrantFreeLikeQuotaResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantFreeLikeQuotaResponse.Marshal(b, m, deterministic)
}
func (dst *GrantFreeLikeQuotaResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantFreeLikeQuotaResponse.Merge(dst, src)
}
func (m *GrantFreeLikeQuotaResponse) XXX_Size() int {
	return xxx_messageInfo_GrantFreeLikeQuotaResponse.Size(m)
}
func (m *GrantFreeLikeQuotaResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantFreeLikeQuotaResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GrantFreeLikeQuotaResponse proto.InternalMessageInfo

func (m *GrantFreeLikeQuotaResponse) GetGranted() bool {
	if m != nil {
		return m.Granted
	}
	return false
}

func (m *GrantFreeLikeQuotaResponse) GetQuota() uint32 {
	if m != nil {
		return m.Quota
	}
	return 0
}

type AddAFKLikedRequest struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAFKLikedRequest) Reset()         { *m = AddAFKLikedRequest{} }
func (m *AddAFKLikedRequest) String() string { return proto.CompactTextString(m) }
func (*AddAFKLikedRequest) ProtoMessage()    {}
func (*AddAFKLikedRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{24}
}
func (m *AddAFKLikedRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAFKLikedRequest.Unmarshal(m, b)
}
func (m *AddAFKLikedRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAFKLikedRequest.Marshal(b, m, deterministic)
}
func (dst *AddAFKLikedRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAFKLikedRequest.Merge(dst, src)
}
func (m *AddAFKLikedRequest) XXX_Size() int {
	return xxx_messageInfo_AddAFKLikedRequest.Size(m)
}
func (m *AddAFKLikedRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAFKLikedRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddAFKLikedRequest proto.InternalMessageInfo

func (m *AddAFKLikedRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type AddAFKLikedResponse struct {
	LikedCount           uint32   `protobuf:"varint,1,opt,name=liked_count,json=likedCount,proto3" json:"liked_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAFKLikedResponse) Reset()         { *m = AddAFKLikedResponse{} }
func (m *AddAFKLikedResponse) String() string { return proto.CompactTextString(m) }
func (*AddAFKLikedResponse) ProtoMessage()    {}
func (*AddAFKLikedResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{25}
}
func (m *AddAFKLikedResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAFKLikedResponse.Unmarshal(m, b)
}
func (m *AddAFKLikedResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAFKLikedResponse.Marshal(b, m, deterministic)
}
func (dst *AddAFKLikedResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAFKLikedResponse.Merge(dst, src)
}
func (m *AddAFKLikedResponse) XXX_Size() int {
	return xxx_messageInfo_AddAFKLikedResponse.Size(m)
}
func (m *AddAFKLikedResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAFKLikedResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddAFKLikedResponse proto.InternalMessageInfo

func (m *AddAFKLikedResponse) GetLikedCount() uint32 {
	if m != nil {
		return m.LikedCount
	}
	return 0
}

type ClearAFKLikedRequest struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearAFKLikedRequest) Reset()         { *m = ClearAFKLikedRequest{} }
func (m *ClearAFKLikedRequest) String() string { return proto.CompactTextString(m) }
func (*ClearAFKLikedRequest) ProtoMessage()    {}
func (*ClearAFKLikedRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{26}
}
func (m *ClearAFKLikedRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearAFKLikedRequest.Unmarshal(m, b)
}
func (m *ClearAFKLikedRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearAFKLikedRequest.Marshal(b, m, deterministic)
}
func (dst *ClearAFKLikedRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearAFKLikedRequest.Merge(dst, src)
}
func (m *ClearAFKLikedRequest) XXX_Size() int {
	return xxx_messageInfo_ClearAFKLikedRequest.Size(m)
}
func (m *ClearAFKLikedRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearAFKLikedRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ClearAFKLikedRequest proto.InternalMessageInfo

func (m *ClearAFKLikedRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type ClearAFKLikedResponse struct {
	Changed              bool     `protobuf:"varint,1,opt,name=changed,proto3" json:"changed,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearAFKLikedResponse) Reset()         { *m = ClearAFKLikedResponse{} }
func (m *ClearAFKLikedResponse) String() string { return proto.CompactTextString(m) }
func (*ClearAFKLikedResponse) ProtoMessage()    {}
func (*ClearAFKLikedResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{27}
}
func (m *ClearAFKLikedResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearAFKLikedResponse.Unmarshal(m, b)
}
func (m *ClearAFKLikedResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearAFKLikedResponse.Marshal(b, m, deterministic)
}
func (dst *ClearAFKLikedResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearAFKLikedResponse.Merge(dst, src)
}
func (m *ClearAFKLikedResponse) XXX_Size() int {
	return xxx_messageInfo_ClearAFKLikedResponse.Size(m)
}
func (m *ClearAFKLikedResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearAFKLikedResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ClearAFKLikedResponse proto.InternalMessageInfo

func (m *ClearAFKLikedResponse) GetChanged() bool {
	if m != nil {
		return m.Changed
	}
	return false
}

type ReportActiveRequest struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportActiveRequest) Reset()         { *m = ReportActiveRequest{} }
func (m *ReportActiveRequest) String() string { return proto.CompactTextString(m) }
func (*ReportActiveRequest) ProtoMessage()    {}
func (*ReportActiveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{28}
}
func (m *ReportActiveRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportActiveRequest.Unmarshal(m, b)
}
func (m *ReportActiveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportActiveRequest.Marshal(b, m, deterministic)
}
func (dst *ReportActiveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportActiveRequest.Merge(dst, src)
}
func (m *ReportActiveRequest) XXX_Size() int {
	return xxx_messageInfo_ReportActiveRequest.Size(m)
}
func (m *ReportActiveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportActiveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReportActiveRequest proto.InternalMessageInfo

func (m *ReportActiveRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type ReportActiveResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportActiveResponse) Reset()         { *m = ReportActiveResponse{} }
func (m *ReportActiveResponse) String() string { return proto.CompactTextString(m) }
func (*ReportActiveResponse) ProtoMessage()    {}
func (*ReportActiveResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{29}
}
func (m *ReportActiveResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportActiveResponse.Unmarshal(m, b)
}
func (m *ReportActiveResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportActiveResponse.Marshal(b, m, deterministic)
}
func (dst *ReportActiveResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportActiveResponse.Merge(dst, src)
}
func (m *ReportActiveResponse) XXX_Size() int {
	return xxx_messageInfo_ReportActiveResponse.Size(m)
}
func (m *ReportActiveResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportActiveResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReportActiveResponse proto.InternalMessageInfo

type GetRecentLikedMeRequest struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecentLikedMeRequest) Reset()         { *m = GetRecentLikedMeRequest{} }
func (m *GetRecentLikedMeRequest) String() string { return proto.CompactTextString(m) }
func (*GetRecentLikedMeRequest) ProtoMessage()    {}
func (*GetRecentLikedMeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{30}
}
func (m *GetRecentLikedMeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecentLikedMeRequest.Unmarshal(m, b)
}
func (m *GetRecentLikedMeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecentLikedMeRequest.Marshal(b, m, deterministic)
}
func (dst *GetRecentLikedMeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecentLikedMeRequest.Merge(dst, src)
}
func (m *GetRecentLikedMeRequest) XXX_Size() int {
	return xxx_messageInfo_GetRecentLikedMeRequest.Size(m)
}
func (m *GetRecentLikedMeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecentLikedMeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecentLikedMeRequest proto.InternalMessageInfo

func (m *GetRecentLikedMeRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetRecentLikedMeRequest) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetRecentLikedMeResponse struct {
	UserIdList           []uint32 `protobuf:"varint,1,rep,packed,name=user_id_list,json=userIdList,proto3" json:"user_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecentLikedMeResponse) Reset()         { *m = GetRecentLikedMeResponse{} }
func (m *GetRecentLikedMeResponse) String() string { return proto.CompactTextString(m) }
func (*GetRecentLikedMeResponse) ProtoMessage()    {}
func (*GetRecentLikedMeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{31}
}
func (m *GetRecentLikedMeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecentLikedMeResponse.Unmarshal(m, b)
}
func (m *GetRecentLikedMeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecentLikedMeResponse.Marshal(b, m, deterministic)
}
func (dst *GetRecentLikedMeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecentLikedMeResponse.Merge(dst, src)
}
func (m *GetRecentLikedMeResponse) XXX_Size() int {
	return xxx_messageInfo_GetRecentLikedMeResponse.Size(m)
}
func (m *GetRecentLikedMeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecentLikedMeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecentLikedMeResponse proto.InternalMessageInfo

func (m *GetRecentLikedMeResponse) GetUserIdList() []uint32 {
	if m != nil {
		return m.UserIdList
	}
	return nil
}

type QuickMatchGame struct {
	GameName             string            `protobuf:"bytes,1,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	Options              map[string]uint32 `protobuf:"bytes,2,rep,name=options,proto3" json:"options,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	GameId               uint32            `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	MatchMessage         string            `protobuf:"bytes,9,opt,name=match_message,json=matchMessage,proto3" json:"match_message,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *QuickMatchGame) Reset()         { *m = QuickMatchGame{} }
func (m *QuickMatchGame) String() string { return proto.CompactTextString(m) }
func (*QuickMatchGame) ProtoMessage()    {}
func (*QuickMatchGame) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{32}
}
func (m *QuickMatchGame) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuickMatchGame.Unmarshal(m, b)
}
func (m *QuickMatchGame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuickMatchGame.Marshal(b, m, deterministic)
}
func (dst *QuickMatchGame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuickMatchGame.Merge(dst, src)
}
func (m *QuickMatchGame) XXX_Size() int {
	return xxx_messageInfo_QuickMatchGame.Size(m)
}
func (m *QuickMatchGame) XXX_DiscardUnknown() {
	xxx_messageInfo_QuickMatchGame.DiscardUnknown(m)
}

var xxx_messageInfo_QuickMatchGame proto.InternalMessageInfo

func (m *QuickMatchGame) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *QuickMatchGame) GetOptions() map[string]uint32 {
	if m != nil {
		return m.Options
	}
	return nil
}

func (m *QuickMatchGame) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *QuickMatchGame) GetMatchMessage() string {
	if m != nil {
		return m.MatchMessage
	}
	return ""
}

type MatchedPeerInfo struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	UserGender           uint32   `protobuf:"varint,2,opt,name=user_gender,json=userGender,proto3" json:"user_gender,omitempty"`
	SessionId            string   `protobuf:"bytes,3,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MatchedPeerInfo) Reset()         { *m = MatchedPeerInfo{} }
func (m *MatchedPeerInfo) String() string { return proto.CompactTextString(m) }
func (*MatchedPeerInfo) ProtoMessage()    {}
func (*MatchedPeerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{33}
}
func (m *MatchedPeerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MatchedPeerInfo.Unmarshal(m, b)
}
func (m *MatchedPeerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MatchedPeerInfo.Marshal(b, m, deterministic)
}
func (dst *MatchedPeerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MatchedPeerInfo.Merge(dst, src)
}
func (m *MatchedPeerInfo) XXX_Size() int {
	return xxx_messageInfo_MatchedPeerInfo.Size(m)
}
func (m *MatchedPeerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MatchedPeerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MatchedPeerInfo proto.InternalMessageInfo

func (m *MatchedPeerInfo) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *MatchedPeerInfo) GetUserGender() uint32 {
	if m != nil {
		return m.UserGender
	}
	return 0
}

func (m *MatchedPeerInfo) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

type StartQuickMatchRequest struct {
	UserId               uint32            `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	GameName             string            `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	Options              map[string]uint32 `protobuf:"bytes,3,rep,name=options,proto3" json:"options,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	Duration             uint32            `protobuf:"varint,4,opt,name=duration,proto3" json:"duration,omitempty"`
	UserGender           uint32            `protobuf:"varint,5,opt,name=user_gender,json=userGender,proto3" json:"user_gender,omitempty"`
	GameList             []*QuickMatchGame `protobuf:"bytes,6,rep,name=game_list,json=gameList,proto3" json:"game_list,omitempty"`
	SupplementChannelId  uint32            `protobuf:"varint,7,opt,name=supplement_channel_id,json=supplementChannelId,proto3" json:"supplement_channel_id,omitempty"`
	SupplementNum        uint32            `protobuf:"varint,8,opt,name=supplement_num,json=supplementNum,proto3" json:"supplement_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *StartQuickMatchRequest) Reset()         { *m = StartQuickMatchRequest{} }
func (m *StartQuickMatchRequest) String() string { return proto.CompactTextString(m) }
func (*StartQuickMatchRequest) ProtoMessage()    {}
func (*StartQuickMatchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{34}
}
func (m *StartQuickMatchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartQuickMatchRequest.Unmarshal(m, b)
}
func (m *StartQuickMatchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartQuickMatchRequest.Marshal(b, m, deterministic)
}
func (dst *StartQuickMatchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartQuickMatchRequest.Merge(dst, src)
}
func (m *StartQuickMatchRequest) XXX_Size() int {
	return xxx_messageInfo_StartQuickMatchRequest.Size(m)
}
func (m *StartQuickMatchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StartQuickMatchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StartQuickMatchRequest proto.InternalMessageInfo

func (m *StartQuickMatchRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *StartQuickMatchRequest) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *StartQuickMatchRequest) GetOptions() map[string]uint32 {
	if m != nil {
		return m.Options
	}
	return nil
}

func (m *StartQuickMatchRequest) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *StartQuickMatchRequest) GetUserGender() uint32 {
	if m != nil {
		return m.UserGender
	}
	return 0
}

func (m *StartQuickMatchRequest) GetGameList() []*QuickMatchGame {
	if m != nil {
		return m.GameList
	}
	return nil
}

func (m *StartQuickMatchRequest) GetSupplementChannelId() uint32 {
	if m != nil {
		return m.SupplementChannelId
	}
	return 0
}

func (m *StartQuickMatchRequest) GetSupplementNum() uint32 {
	if m != nil {
		return m.SupplementNum
	}
	return 0
}

type StartQuickMatchResponse struct {
	SessionId            string           `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	MatchedPeerInfo      *MatchedPeerInfo `protobuf:"bytes,2,opt,name=matched_peer_info,json=matchedPeerInfo,proto3" json:"matched_peer_info,omitempty"`
	SequenceInQueue      uint32           `protobuf:"varint,3,opt,name=sequence_in_queue,json=sequenceInQueue,proto3" json:"sequence_in_queue,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *StartQuickMatchResponse) Reset()         { *m = StartQuickMatchResponse{} }
func (m *StartQuickMatchResponse) String() string { return proto.CompactTextString(m) }
func (*StartQuickMatchResponse) ProtoMessage()    {}
func (*StartQuickMatchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{35}
}
func (m *StartQuickMatchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartQuickMatchResponse.Unmarshal(m, b)
}
func (m *StartQuickMatchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartQuickMatchResponse.Marshal(b, m, deterministic)
}
func (dst *StartQuickMatchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartQuickMatchResponse.Merge(dst, src)
}
func (m *StartQuickMatchResponse) XXX_Size() int {
	return xxx_messageInfo_StartQuickMatchResponse.Size(m)
}
func (m *StartQuickMatchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StartQuickMatchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StartQuickMatchResponse proto.InternalMessageInfo

func (m *StartQuickMatchResponse) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

func (m *StartQuickMatchResponse) GetMatchedPeerInfo() *MatchedPeerInfo {
	if m != nil {
		return m.MatchedPeerInfo
	}
	return nil
}

func (m *StartQuickMatchResponse) GetSequenceInQueue() uint32 {
	if m != nil {
		return m.SequenceInQueue
	}
	return 0
}

type CancelQuickMatchRequest struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	SessionId            string   `protobuf:"bytes,2,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelQuickMatchRequest) Reset()         { *m = CancelQuickMatchRequest{} }
func (m *CancelQuickMatchRequest) String() string { return proto.CompactTextString(m) }
func (*CancelQuickMatchRequest) ProtoMessage()    {}
func (*CancelQuickMatchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{36}
}
func (m *CancelQuickMatchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelQuickMatchRequest.Unmarshal(m, b)
}
func (m *CancelQuickMatchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelQuickMatchRequest.Marshal(b, m, deterministic)
}
func (dst *CancelQuickMatchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelQuickMatchRequest.Merge(dst, src)
}
func (m *CancelQuickMatchRequest) XXX_Size() int {
	return xxx_messageInfo_CancelQuickMatchRequest.Size(m)
}
func (m *CancelQuickMatchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelQuickMatchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CancelQuickMatchRequest proto.InternalMessageInfo

func (m *CancelQuickMatchRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *CancelQuickMatchRequest) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

type CancelQuickMatchResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelQuickMatchResponse) Reset()         { *m = CancelQuickMatchResponse{} }
func (m *CancelQuickMatchResponse) String() string { return proto.CompactTextString(m) }
func (*CancelQuickMatchResponse) ProtoMessage()    {}
func (*CancelQuickMatchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{37}
}
func (m *CancelQuickMatchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelQuickMatchResponse.Unmarshal(m, b)
}
func (m *CancelQuickMatchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelQuickMatchResponse.Marshal(b, m, deterministic)
}
func (dst *CancelQuickMatchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelQuickMatchResponse.Merge(dst, src)
}
func (m *CancelQuickMatchResponse) XXX_Size() int {
	return xxx_messageInfo_CancelQuickMatchResponse.Size(m)
}
func (m *CancelQuickMatchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelQuickMatchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CancelQuickMatchResponse proto.InternalMessageInfo

type GetQuickMatchStatisticsRequest struct {
	BeginTime            uint64   `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint64   `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuickMatchStatisticsRequest) Reset()         { *m = GetQuickMatchStatisticsRequest{} }
func (m *GetQuickMatchStatisticsRequest) String() string { return proto.CompactTextString(m) }
func (*GetQuickMatchStatisticsRequest) ProtoMessage()    {}
func (*GetQuickMatchStatisticsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{38}
}
func (m *GetQuickMatchStatisticsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickMatchStatisticsRequest.Unmarshal(m, b)
}
func (m *GetQuickMatchStatisticsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickMatchStatisticsRequest.Marshal(b, m, deterministic)
}
func (dst *GetQuickMatchStatisticsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickMatchStatisticsRequest.Merge(dst, src)
}
func (m *GetQuickMatchStatisticsRequest) XXX_Size() int {
	return xxx_messageInfo_GetQuickMatchStatisticsRequest.Size(m)
}
func (m *GetQuickMatchStatisticsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickMatchStatisticsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickMatchStatisticsRequest proto.InternalMessageInfo

func (m *GetQuickMatchStatisticsRequest) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetQuickMatchStatisticsRequest) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetQuickMatchStatisticsResponse struct {
	CurrentSessionCount  uint32   `protobuf:"varint,1,opt,name=current_session_count,json=currentSessionCount,proto3" json:"current_session_count,omitempty"`
	TotalStartedMatch    uint32   `protobuf:"varint,2,opt,name=total_started_match,json=totalStartedMatch,proto3" json:"total_started_match,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuickMatchStatisticsResponse) Reset()         { *m = GetQuickMatchStatisticsResponse{} }
func (m *GetQuickMatchStatisticsResponse) String() string { return proto.CompactTextString(m) }
func (*GetQuickMatchStatisticsResponse) ProtoMessage()    {}
func (*GetQuickMatchStatisticsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{39}
}
func (m *GetQuickMatchStatisticsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickMatchStatisticsResponse.Unmarshal(m, b)
}
func (m *GetQuickMatchStatisticsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickMatchStatisticsResponse.Marshal(b, m, deterministic)
}
func (dst *GetQuickMatchStatisticsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickMatchStatisticsResponse.Merge(dst, src)
}
func (m *GetQuickMatchStatisticsResponse) XXX_Size() int {
	return xxx_messageInfo_GetQuickMatchStatisticsResponse.Size(m)
}
func (m *GetQuickMatchStatisticsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickMatchStatisticsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickMatchStatisticsResponse proto.InternalMessageInfo

func (m *GetQuickMatchStatisticsResponse) GetCurrentSessionCount() uint32 {
	if m != nil {
		return m.CurrentSessionCount
	}
	return 0
}

func (m *GetQuickMatchStatisticsResponse) GetTotalStartedMatch() uint32 {
	if m != nil {
		return m.TotalStartedMatch
	}
	return 0
}

type QuickMatchKeepAliveRequest struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	SessionId            string   `protobuf:"bytes,2,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QuickMatchKeepAliveRequest) Reset()         { *m = QuickMatchKeepAliveRequest{} }
func (m *QuickMatchKeepAliveRequest) String() string { return proto.CompactTextString(m) }
func (*QuickMatchKeepAliveRequest) ProtoMessage()    {}
func (*QuickMatchKeepAliveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{40}
}
func (m *QuickMatchKeepAliveRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuickMatchKeepAliveRequest.Unmarshal(m, b)
}
func (m *QuickMatchKeepAliveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuickMatchKeepAliveRequest.Marshal(b, m, deterministic)
}
func (dst *QuickMatchKeepAliveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuickMatchKeepAliveRequest.Merge(dst, src)
}
func (m *QuickMatchKeepAliveRequest) XXX_Size() int {
	return xxx_messageInfo_QuickMatchKeepAliveRequest.Size(m)
}
func (m *QuickMatchKeepAliveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QuickMatchKeepAliveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QuickMatchKeepAliveRequest proto.InternalMessageInfo

func (m *QuickMatchKeepAliveRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *QuickMatchKeepAliveRequest) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

type QuickMatchKeepAliveResponse struct {
	IsAlive              bool     `protobuf:"varint,1,opt,name=is_alive,json=isAlive,proto3" json:"is_alive,omitempty"`
	Rank                 uint32   `protobuf:"varint,2,opt,name=rank,proto3" json:"rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QuickMatchKeepAliveResponse) Reset()         { *m = QuickMatchKeepAliveResponse{} }
func (m *QuickMatchKeepAliveResponse) String() string { return proto.CompactTextString(m) }
func (*QuickMatchKeepAliveResponse) ProtoMessage()    {}
func (*QuickMatchKeepAliveResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{41}
}
func (m *QuickMatchKeepAliveResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuickMatchKeepAliveResponse.Unmarshal(m, b)
}
func (m *QuickMatchKeepAliveResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuickMatchKeepAliveResponse.Marshal(b, m, deterministic)
}
func (dst *QuickMatchKeepAliveResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuickMatchKeepAliveResponse.Merge(dst, src)
}
func (m *QuickMatchKeepAliveResponse) XXX_Size() int {
	return xxx_messageInfo_QuickMatchKeepAliveResponse.Size(m)
}
func (m *QuickMatchKeepAliveResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QuickMatchKeepAliveResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QuickMatchKeepAliveResponse proto.InternalMessageInfo

func (m *QuickMatchKeepAliveResponse) GetIsAlive() bool {
	if m != nil {
		return m.IsAlive
	}
	return false
}

func (m *QuickMatchKeepAliveResponse) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

type PunishQuickMatchDeserterRequest struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OnlineSeconds        uint32   `protobuf:"varint,3,opt,name=online_seconds,json=onlineSeconds,proto3" json:"online_seconds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PunishQuickMatchDeserterRequest) Reset()         { *m = PunishQuickMatchDeserterRequest{} }
func (m *PunishQuickMatchDeserterRequest) String() string { return proto.CompactTextString(m) }
func (*PunishQuickMatchDeserterRequest) ProtoMessage()    {}
func (*PunishQuickMatchDeserterRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{42}
}
func (m *PunishQuickMatchDeserterRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PunishQuickMatchDeserterRequest.Unmarshal(m, b)
}
func (m *PunishQuickMatchDeserterRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PunishQuickMatchDeserterRequest.Marshal(b, m, deterministic)
}
func (dst *PunishQuickMatchDeserterRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PunishQuickMatchDeserterRequest.Merge(dst, src)
}
func (m *PunishQuickMatchDeserterRequest) XXX_Size() int {
	return xxx_messageInfo_PunishQuickMatchDeserterRequest.Size(m)
}
func (m *PunishQuickMatchDeserterRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PunishQuickMatchDeserterRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PunishQuickMatchDeserterRequest proto.InternalMessageInfo

func (m *PunishQuickMatchDeserterRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *PunishQuickMatchDeserterRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PunishQuickMatchDeserterRequest) GetOnlineSeconds() uint32 {
	if m != nil {
		return m.OnlineSeconds
	}
	return 0
}

type PunishQuickMatchDeserterResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PunishQuickMatchDeserterResponse) Reset()         { *m = PunishQuickMatchDeserterResponse{} }
func (m *PunishQuickMatchDeserterResponse) String() string { return proto.CompactTextString(m) }
func (*PunishQuickMatchDeserterResponse) ProtoMessage()    {}
func (*PunishQuickMatchDeserterResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_find_friends_29a4061c5be26f06, []int{43}
}
func (m *PunishQuickMatchDeserterResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PunishQuickMatchDeserterResponse.Unmarshal(m, b)
}
func (m *PunishQuickMatchDeserterResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PunishQuickMatchDeserterResponse.Marshal(b, m, deterministic)
}
func (dst *PunishQuickMatchDeserterResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PunishQuickMatchDeserterResponse.Merge(dst, src)
}
func (m *PunishQuickMatchDeserterResponse) XXX_Size() int {
	return xxx_messageInfo_PunishQuickMatchDeserterResponse.Size(m)
}
func (m *PunishQuickMatchDeserterResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PunishQuickMatchDeserterResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PunishQuickMatchDeserterResponse proto.InternalMessageInfo

func init() {
	proto.RegisterType((*Location)(nil), "FindFriends.Location")
	proto.RegisterType((*UserInfo)(nil), "FindFriends.UserInfo")
	proto.RegisterType((*GetUserRequest)(nil), "FindFriends.GetUserRequest")
	proto.RegisterType((*GetUserResponse)(nil), "FindFriends.GetUserResponse")
	proto.RegisterType((*CreateOrUpdateUserRequest)(nil), "FindFriends.CreateOrUpdateUserRequest")
	proto.RegisterType((*CreateOrUpdateUserResponse)(nil), "FindFriends.CreateOrUpdateUserResponse")
	proto.RegisterType((*UpdatePhotoRequest)(nil), "FindFriends.UpdatePhotoRequest")
	proto.RegisterType((*UpdatePhotoResponse)(nil), "FindFriends.UpdatePhotoResponse")
	proto.RegisterType((*GetRecommendUsersRequest)(nil), "FindFriends.GetRecommendUsersRequest")
	proto.RegisterType((*GetRecommendUsersResponse)(nil), "FindFriends.GetRecommendUsersResponse")
	proto.RegisterType((*LikeRequest)(nil), "FindFriends.LikeRequest")
	proto.RegisterType((*LikeResponse)(nil), "FindFriends.LikeResponse")
	proto.RegisterType((*CheckLikedRequest)(nil), "FindFriends.CheckLikedRequest")
	proto.RegisterType((*CheckLikedResponse)(nil), "FindFriends.CheckLikedResponse")
	proto.RegisterType((*CheckBeenLikedRequest)(nil), "FindFriends.CheckBeenLikedRequest")
	proto.RegisterType((*CheckBeenLikedResponse)(nil), "FindFriends.CheckBeenLikedResponse")
	proto.RegisterType((*ClearMutualLikesMeRequest)(nil), "FindFriends.ClearMutualLikesMeRequest")
	proto.RegisterType((*ClearMutualLikesMeResponse)(nil), "FindFriends.ClearMutualLikesMeResponse")
	proto.RegisterType((*GetTodayLikedCountRequest)(nil), "FindFriends.GetTodayLikedCountRequest")
	proto.RegisterType((*GetTodayLikedCountResponse)(nil), "FindFriends.GetTodayLikedCountResponse")
	proto.RegisterType((*GetFreeLikeQuotaRequest)(nil), "FindFriends.GetFreeLikeQuotaRequest")
	proto.RegisterType((*GetFreeLikeQuotaResponse)(nil), "FindFriends.GetFreeLikeQuotaResponse")
	proto.RegisterType((*GrantFreeLikeQuotaRequest)(nil), "FindFriends.GrantFreeLikeQuotaRequest")
	proto.RegisterType((*GrantFreeLikeQuotaResponse)(nil), "FindFriends.GrantFreeLikeQuotaResponse")
	proto.RegisterType((*AddAFKLikedRequest)(nil), "FindFriends.AddAFKLikedRequest")
	proto.RegisterType((*AddAFKLikedResponse)(nil), "FindFriends.AddAFKLikedResponse")
	proto.RegisterType((*ClearAFKLikedRequest)(nil), "FindFriends.ClearAFKLikedRequest")
	proto.RegisterType((*ClearAFKLikedResponse)(nil), "FindFriends.ClearAFKLikedResponse")
	proto.RegisterType((*ReportActiveRequest)(nil), "FindFriends.ReportActiveRequest")
	proto.RegisterType((*ReportActiveResponse)(nil), "FindFriends.ReportActiveResponse")
	proto.RegisterType((*GetRecentLikedMeRequest)(nil), "FindFriends.GetRecentLikedMeRequest")
	proto.RegisterType((*GetRecentLikedMeResponse)(nil), "FindFriends.GetRecentLikedMeResponse")
	proto.RegisterType((*QuickMatchGame)(nil), "FindFriends.QuickMatchGame")
	proto.RegisterMapType((map[string]uint32)(nil), "FindFriends.QuickMatchGame.OptionsEntry")
	proto.RegisterType((*MatchedPeerInfo)(nil), "FindFriends.MatchedPeerInfo")
	proto.RegisterType((*StartQuickMatchRequest)(nil), "FindFriends.StartQuickMatchRequest")
	proto.RegisterMapType((map[string]uint32)(nil), "FindFriends.StartQuickMatchRequest.OptionsEntry")
	proto.RegisterType((*StartQuickMatchResponse)(nil), "FindFriends.StartQuickMatchResponse")
	proto.RegisterType((*CancelQuickMatchRequest)(nil), "FindFriends.CancelQuickMatchRequest")
	proto.RegisterType((*CancelQuickMatchResponse)(nil), "FindFriends.CancelQuickMatchResponse")
	proto.RegisterType((*GetQuickMatchStatisticsRequest)(nil), "FindFriends.GetQuickMatchStatisticsRequest")
	proto.RegisterType((*GetQuickMatchStatisticsResponse)(nil), "FindFriends.GetQuickMatchStatisticsResponse")
	proto.RegisterType((*QuickMatchKeepAliveRequest)(nil), "FindFriends.QuickMatchKeepAliveRequest")
	proto.RegisterType((*QuickMatchKeepAliveResponse)(nil), "FindFriends.QuickMatchKeepAliveResponse")
	proto.RegisterType((*PunishQuickMatchDeserterRequest)(nil), "FindFriends.PunishQuickMatchDeserterRequest")
	proto.RegisterType((*PunishQuickMatchDeserterResponse)(nil), "FindFriends.PunishQuickMatchDeserterResponse")
	proto.RegisterEnum("FindFriends.Gender", Gender_name, Gender_value)
	proto.RegisterEnum("FindFriends.GenderFilter", GenderFilter_name, GenderFilter_value)
	proto.RegisterEnum("FindFriends.UserInfo_AutoPlayVoice", UserInfo_AutoPlayVoice_name, UserInfo_AutoPlayVoice_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// FindFriendsClient is the client API for FindFriends service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type FindFriendsClient interface {
	// 扩圈相关
	GetUser(ctx context.Context, in *GetUserRequest, opts ...grpc.CallOption) (*GetUserResponse, error)
	CreateOrUpdateUser(ctx context.Context, in *CreateOrUpdateUserRequest, opts ...grpc.CallOption) (*CreateOrUpdateUserResponse, error)
	UpdatePhoto(ctx context.Context, in *UpdatePhotoRequest, opts ...grpc.CallOption) (*UpdatePhotoResponse, error)
	ReportActive(ctx context.Context, in *ReportActiveRequest, opts ...grpc.CallOption) (*ReportActiveResponse, error)
	GetRecommendUsers(ctx context.Context, in *GetRecommendUsersRequest, opts ...grpc.CallOption) (*GetRecommendUsersResponse, error)
	Like(ctx context.Context, in *LikeRequest, opts ...grpc.CallOption) (*LikeResponse, error)
	CheckLiked(ctx context.Context, in *CheckLikedRequest, opts ...grpc.CallOption) (*CheckLikedResponse, error)
	CheckBeenLiked(ctx context.Context, in *CheckBeenLikedRequest, opts ...grpc.CallOption) (*CheckBeenLikedResponse, error)
	GetRecentLikedMe(ctx context.Context, in *GetRecentLikedMeRequest, opts ...grpc.CallOption) (*GetRecentLikedMeResponse, error)
	ClearMutualLikesMe(ctx context.Context, in *ClearMutualLikesMeRequest, opts ...grpc.CallOption) (*ClearMutualLikesMeResponse, error)
	GetTodayLikedCount(ctx context.Context, in *GetTodayLikedCountRequest, opts ...grpc.CallOption) (*GetTodayLikedCountResponse, error)
	AddAFKLiked(ctx context.Context, in *AddAFKLikedRequest, opts ...grpc.CallOption) (*AddAFKLikedResponse, error)
	ClearAFKLiked(ctx context.Context, in *ClearAFKLikedRequest, opts ...grpc.CallOption) (*ClearAFKLikedResponse, error)
	GetFreeLikeQuota(ctx context.Context, in *GetFreeLikeQuotaRequest, opts ...grpc.CallOption) (*GetFreeLikeQuotaResponse, error)
	GrantFreeLikeQuota(ctx context.Context, in *GrantFreeLikeQuotaRequest, opts ...grpc.CallOption) (*GrantFreeLikeQuotaResponse, error)
	// 快速匹配
	StartQuickMatch(ctx context.Context, in *StartQuickMatchRequest, opts ...grpc.CallOption) (*StartQuickMatchResponse, error)
	CancelQuickMatch(ctx context.Context, in *CancelQuickMatchRequest, opts ...grpc.CallOption) (*CancelQuickMatchResponse, error)
	GetQuickMatchStatistics(ctx context.Context, in *GetQuickMatchStatisticsRequest, opts ...grpc.CallOption) (*GetQuickMatchStatisticsResponse, error)
	QuickMatchKeepAlive(ctx context.Context, in *QuickMatchKeepAliveRequest, opts ...grpc.CallOption) (*QuickMatchKeepAliveResponse, error)
	PunishQuickMatchDeserter(ctx context.Context, in *PunishQuickMatchDeserterRequest, opts ...grpc.CallOption) (*PunishQuickMatchDeserterResponse, error)
}

type findFriendsClient struct {
	cc *grpc.ClientConn
}

func NewFindFriendsClient(cc *grpc.ClientConn) FindFriendsClient {
	return &findFriendsClient{cc}
}

func (c *findFriendsClient) GetUser(ctx context.Context, in *GetUserRequest, opts ...grpc.CallOption) (*GetUserResponse, error) {
	out := new(GetUserResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/GetUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendsClient) CreateOrUpdateUser(ctx context.Context, in *CreateOrUpdateUserRequest, opts ...grpc.CallOption) (*CreateOrUpdateUserResponse, error) {
	out := new(CreateOrUpdateUserResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/CreateOrUpdateUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendsClient) UpdatePhoto(ctx context.Context, in *UpdatePhotoRequest, opts ...grpc.CallOption) (*UpdatePhotoResponse, error) {
	out := new(UpdatePhotoResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/UpdatePhoto", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendsClient) ReportActive(ctx context.Context, in *ReportActiveRequest, opts ...grpc.CallOption) (*ReportActiveResponse, error) {
	out := new(ReportActiveResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/ReportActive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendsClient) GetRecommendUsers(ctx context.Context, in *GetRecommendUsersRequest, opts ...grpc.CallOption) (*GetRecommendUsersResponse, error) {
	out := new(GetRecommendUsersResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/GetRecommendUsers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendsClient) Like(ctx context.Context, in *LikeRequest, opts ...grpc.CallOption) (*LikeResponse, error) {
	out := new(LikeResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/Like", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendsClient) CheckLiked(ctx context.Context, in *CheckLikedRequest, opts ...grpc.CallOption) (*CheckLikedResponse, error) {
	out := new(CheckLikedResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/CheckLiked", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendsClient) CheckBeenLiked(ctx context.Context, in *CheckBeenLikedRequest, opts ...grpc.CallOption) (*CheckBeenLikedResponse, error) {
	out := new(CheckBeenLikedResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/CheckBeenLiked", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendsClient) GetRecentLikedMe(ctx context.Context, in *GetRecentLikedMeRequest, opts ...grpc.CallOption) (*GetRecentLikedMeResponse, error) {
	out := new(GetRecentLikedMeResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/GetRecentLikedMe", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendsClient) ClearMutualLikesMe(ctx context.Context, in *ClearMutualLikesMeRequest, opts ...grpc.CallOption) (*ClearMutualLikesMeResponse, error) {
	out := new(ClearMutualLikesMeResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/ClearMutualLikesMe", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendsClient) GetTodayLikedCount(ctx context.Context, in *GetTodayLikedCountRequest, opts ...grpc.CallOption) (*GetTodayLikedCountResponse, error) {
	out := new(GetTodayLikedCountResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/GetTodayLikedCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendsClient) AddAFKLiked(ctx context.Context, in *AddAFKLikedRequest, opts ...grpc.CallOption) (*AddAFKLikedResponse, error) {
	out := new(AddAFKLikedResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/AddAFKLiked", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendsClient) ClearAFKLiked(ctx context.Context, in *ClearAFKLikedRequest, opts ...grpc.CallOption) (*ClearAFKLikedResponse, error) {
	out := new(ClearAFKLikedResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/ClearAFKLiked", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendsClient) GetFreeLikeQuota(ctx context.Context, in *GetFreeLikeQuotaRequest, opts ...grpc.CallOption) (*GetFreeLikeQuotaResponse, error) {
	out := new(GetFreeLikeQuotaResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/GetFreeLikeQuota", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendsClient) GrantFreeLikeQuota(ctx context.Context, in *GrantFreeLikeQuotaRequest, opts ...grpc.CallOption) (*GrantFreeLikeQuotaResponse, error) {
	out := new(GrantFreeLikeQuotaResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/GrantFreeLikeQuota", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendsClient) StartQuickMatch(ctx context.Context, in *StartQuickMatchRequest, opts ...grpc.CallOption) (*StartQuickMatchResponse, error) {
	out := new(StartQuickMatchResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/StartQuickMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendsClient) CancelQuickMatch(ctx context.Context, in *CancelQuickMatchRequest, opts ...grpc.CallOption) (*CancelQuickMatchResponse, error) {
	out := new(CancelQuickMatchResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/CancelQuickMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendsClient) GetQuickMatchStatistics(ctx context.Context, in *GetQuickMatchStatisticsRequest, opts ...grpc.CallOption) (*GetQuickMatchStatisticsResponse, error) {
	out := new(GetQuickMatchStatisticsResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/GetQuickMatchStatistics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendsClient) QuickMatchKeepAlive(ctx context.Context, in *QuickMatchKeepAliveRequest, opts ...grpc.CallOption) (*QuickMatchKeepAliveResponse, error) {
	out := new(QuickMatchKeepAliveResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/QuickMatchKeepAlive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendsClient) PunishQuickMatchDeserter(ctx context.Context, in *PunishQuickMatchDeserterRequest, opts ...grpc.CallOption) (*PunishQuickMatchDeserterResponse, error) {
	out := new(PunishQuickMatchDeserterResponse)
	err := c.cc.Invoke(ctx, "/FindFriends.FindFriends/PunishQuickMatchDeserter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FindFriendsServer is the server API for FindFriends service.
type FindFriendsServer interface {
	// 扩圈相关
	GetUser(context.Context, *GetUserRequest) (*GetUserResponse, error)
	CreateOrUpdateUser(context.Context, *CreateOrUpdateUserRequest) (*CreateOrUpdateUserResponse, error)
	UpdatePhoto(context.Context, *UpdatePhotoRequest) (*UpdatePhotoResponse, error)
	ReportActive(context.Context, *ReportActiveRequest) (*ReportActiveResponse, error)
	GetRecommendUsers(context.Context, *GetRecommendUsersRequest) (*GetRecommendUsersResponse, error)
	Like(context.Context, *LikeRequest) (*LikeResponse, error)
	CheckLiked(context.Context, *CheckLikedRequest) (*CheckLikedResponse, error)
	CheckBeenLiked(context.Context, *CheckBeenLikedRequest) (*CheckBeenLikedResponse, error)
	GetRecentLikedMe(context.Context, *GetRecentLikedMeRequest) (*GetRecentLikedMeResponse, error)
	ClearMutualLikesMe(context.Context, *ClearMutualLikesMeRequest) (*ClearMutualLikesMeResponse, error)
	GetTodayLikedCount(context.Context, *GetTodayLikedCountRequest) (*GetTodayLikedCountResponse, error)
	AddAFKLiked(context.Context, *AddAFKLikedRequest) (*AddAFKLikedResponse, error)
	ClearAFKLiked(context.Context, *ClearAFKLikedRequest) (*ClearAFKLikedResponse, error)
	GetFreeLikeQuota(context.Context, *GetFreeLikeQuotaRequest) (*GetFreeLikeQuotaResponse, error)
	GrantFreeLikeQuota(context.Context, *GrantFreeLikeQuotaRequest) (*GrantFreeLikeQuotaResponse, error)
	// 快速匹配
	StartQuickMatch(context.Context, *StartQuickMatchRequest) (*StartQuickMatchResponse, error)
	CancelQuickMatch(context.Context, *CancelQuickMatchRequest) (*CancelQuickMatchResponse, error)
	GetQuickMatchStatistics(context.Context, *GetQuickMatchStatisticsRequest) (*GetQuickMatchStatisticsResponse, error)
	QuickMatchKeepAlive(context.Context, *QuickMatchKeepAliveRequest) (*QuickMatchKeepAliveResponse, error)
	PunishQuickMatchDeserter(context.Context, *PunishQuickMatchDeserterRequest) (*PunishQuickMatchDeserterResponse, error)
}

func RegisterFindFriendsServer(s *grpc.Server, srv FindFriendsServer) {
	s.RegisterService(&_FindFriends_serviceDesc, srv)
}

func _FindFriends_GetUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).GetUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/GetUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).GetUser(ctx, req.(*GetUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriends_CreateOrUpdateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).CreateOrUpdateUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/CreateOrUpdateUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).CreateOrUpdateUser(ctx, req.(*CreateOrUpdateUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriends_UpdatePhoto_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePhotoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).UpdatePhoto(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/UpdatePhoto",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).UpdatePhoto(ctx, req.(*UpdatePhotoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriends_ReportActive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportActiveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).ReportActive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/ReportActive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).ReportActive(ctx, req.(*ReportActiveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriends_GetRecommendUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).GetRecommendUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/GetRecommendUsers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).GetRecommendUsers(ctx, req.(*GetRecommendUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriends_Like_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LikeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).Like(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/Like",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).Like(ctx, req.(*LikeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriends_CheckLiked_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckLikedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).CheckLiked(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/CheckLiked",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).CheckLiked(ctx, req.(*CheckLikedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriends_CheckBeenLiked_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckBeenLikedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).CheckBeenLiked(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/CheckBeenLiked",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).CheckBeenLiked(ctx, req.(*CheckBeenLikedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriends_GetRecentLikedMe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecentLikedMeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).GetRecentLikedMe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/GetRecentLikedMe",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).GetRecentLikedMe(ctx, req.(*GetRecentLikedMeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriends_ClearMutualLikesMe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearMutualLikesMeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).ClearMutualLikesMe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/ClearMutualLikesMe",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).ClearMutualLikesMe(ctx, req.(*ClearMutualLikesMeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriends_GetTodayLikedCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTodayLikedCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).GetTodayLikedCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/GetTodayLikedCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).GetTodayLikedCount(ctx, req.(*GetTodayLikedCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriends_AddAFKLiked_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAFKLikedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).AddAFKLiked(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/AddAFKLiked",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).AddAFKLiked(ctx, req.(*AddAFKLikedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriends_ClearAFKLiked_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearAFKLikedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).ClearAFKLiked(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/ClearAFKLiked",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).ClearAFKLiked(ctx, req.(*ClearAFKLikedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriends_GetFreeLikeQuota_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFreeLikeQuotaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).GetFreeLikeQuota(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/GetFreeLikeQuota",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).GetFreeLikeQuota(ctx, req.(*GetFreeLikeQuotaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriends_GrantFreeLikeQuota_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrantFreeLikeQuotaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).GrantFreeLikeQuota(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/GrantFreeLikeQuota",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).GrantFreeLikeQuota(ctx, req.(*GrantFreeLikeQuotaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriends_StartQuickMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartQuickMatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).StartQuickMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/StartQuickMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).StartQuickMatch(ctx, req.(*StartQuickMatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriends_CancelQuickMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelQuickMatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).CancelQuickMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/CancelQuickMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).CancelQuickMatch(ctx, req.(*CancelQuickMatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriends_GetQuickMatchStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQuickMatchStatisticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).GetQuickMatchStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/GetQuickMatchStatistics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).GetQuickMatchStatistics(ctx, req.(*GetQuickMatchStatisticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriends_QuickMatchKeepAlive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuickMatchKeepAliveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).QuickMatchKeepAlive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/QuickMatchKeepAlive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).QuickMatchKeepAlive(ctx, req.(*QuickMatchKeepAliveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriends_PunishQuickMatchDeserter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PunishQuickMatchDeserterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendsServer).PunishQuickMatchDeserter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/FindFriends.FindFriends/PunishQuickMatchDeserter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendsServer).PunishQuickMatchDeserter(ctx, req.(*PunishQuickMatchDeserterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _FindFriends_serviceDesc = grpc.ServiceDesc{
	ServiceName: "FindFriends.FindFriends",
	HandlerType: (*FindFriendsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUser",
			Handler:    _FindFriends_GetUser_Handler,
		},
		{
			MethodName: "CreateOrUpdateUser",
			Handler:    _FindFriends_CreateOrUpdateUser_Handler,
		},
		{
			MethodName: "UpdatePhoto",
			Handler:    _FindFriends_UpdatePhoto_Handler,
		},
		{
			MethodName: "ReportActive",
			Handler:    _FindFriends_ReportActive_Handler,
		},
		{
			MethodName: "GetRecommendUsers",
			Handler:    _FindFriends_GetRecommendUsers_Handler,
		},
		{
			MethodName: "Like",
			Handler:    _FindFriends_Like_Handler,
		},
		{
			MethodName: "CheckLiked",
			Handler:    _FindFriends_CheckLiked_Handler,
		},
		{
			MethodName: "CheckBeenLiked",
			Handler:    _FindFriends_CheckBeenLiked_Handler,
		},
		{
			MethodName: "GetRecentLikedMe",
			Handler:    _FindFriends_GetRecentLikedMe_Handler,
		},
		{
			MethodName: "ClearMutualLikesMe",
			Handler:    _FindFriends_ClearMutualLikesMe_Handler,
		},
		{
			MethodName: "GetTodayLikedCount",
			Handler:    _FindFriends_GetTodayLikedCount_Handler,
		},
		{
			MethodName: "AddAFKLiked",
			Handler:    _FindFriends_AddAFKLiked_Handler,
		},
		{
			MethodName: "ClearAFKLiked",
			Handler:    _FindFriends_ClearAFKLiked_Handler,
		},
		{
			MethodName: "GetFreeLikeQuota",
			Handler:    _FindFriends_GetFreeLikeQuota_Handler,
		},
		{
			MethodName: "GrantFreeLikeQuota",
			Handler:    _FindFriends_GrantFreeLikeQuota_Handler,
		},
		{
			MethodName: "StartQuickMatch",
			Handler:    _FindFriends_StartQuickMatch_Handler,
		},
		{
			MethodName: "CancelQuickMatch",
			Handler:    _FindFriends_CancelQuickMatch_Handler,
		},
		{
			MethodName: "GetQuickMatchStatistics",
			Handler:    _FindFriends_GetQuickMatchStatistics_Handler,
		},
		{
			MethodName: "QuickMatchKeepAlive",
			Handler:    _FindFriends_QuickMatchKeepAlive_Handler,
		},
		{
			MethodName: "PunishQuickMatchDeserter",
			Handler:    _FindFriends_PunishQuickMatchDeserter_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "find_friends/find_friends.proto",
}

func init() {
	proto.RegisterFile("find_friends/find_friends.proto", fileDescriptor_find_friends_29a4061c5be26f06)
}

var fileDescriptor_find_friends_29a4061c5be26f06 = []byte{
	// 2121 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x59, 0x5b, 0x53, 0xe3, 0xc8,
	0xf5, 0xc7, 0x36, 0x60, 0xfb, 0x60, 0x63, 0xd3, 0x0c, 0x33, 0x46, 0x03, 0x0b, 0xab, 0xb9, 0xf1,
	0x67, 0xfe, 0xcb, 0xee, 0x92, 0xaa, 0xd4, 0xd6, 0x56, 0x92, 0x8a, 0x31, 0x86, 0x71, 0x86, 0xdb,
	0xc8, 0x38, 0x97, 0xdd, 0xaa, 0x68, 0x35, 0x52, 0x63, 0x7a, 0xb1, 0x25, 0x8f, 0xba, 0x45, 0x42,
	0xa5, 0x2a, 0xc9, 0x4b, 0x1e, 0xf2, 0x90, 0xef, 0x91, 0xa7, 0x7c, 0xb1, 0x7c, 0x85, 0x54, 0x2a,
	0xd5, 0x17, 0xc9, 0x92, 0x2c, 0x63, 0x36, 0x93, 0x37, 0xf7, 0x39, 0x47, 0xe7, 0x7e, 0xba, 0xcf,
	0x0f, 0x60, 0xeb, 0x8a, 0xb8, 0x8e, 0x79, 0xe5, 0x13, 0xec, 0x3a, 0xf4, 0xf3, 0xf8, 0x61, 0x6f,
	0xe4, 0x7b, 0xcc, 0x43, 0x4b, 0x47, 0xc4, 0x75, 0x8e, 0x24, 0x49, 0xff, 0x6b, 0x0e, 0x4a, 0x27,
	0x9e, 0x6d, 0x31, 0xe2, 0xb9, 0xe8, 0x09, 0x14, 0x2d, 0xc7, 0xb4, 0x3d, 0x07, 0x37, 0x72, 0xdb,
	0xb9, 0x9d, 0xaa, 0xb1, 0x68, 0x39, 0x2d, 0xcf, 0xc1, 0xa8, 0x01, 0x45, 0xdb, 0x0b, 0x5c, 0xe6,
	0xdf, 0x35, 0xf2, 0xdb, 0xb9, 0x9d, 0xb2, 0x11, 0x1e, 0x91, 0x06, 0xa5, 0x91, 0xef, 0xdd, 0x12,
	0xd7, 0xc6, 0x8d, 0x82, 0x60, 0x45, 0x67, 0x84, 0x60, 0xde, 0x26, 0xec, 0xae, 0x31, 0x2f, 0xe8,
	0xe2, 0x37, 0x97, 0x77, 0x08, 0x65, 0x3e, 0xb1, 0x59, 0x63, 0x41, 0xca, 0x87, 0x67, 0xfd, 0xdf,
	0x05, 0x28, 0xf5, 0x28, 0xf6, 0x3b, 0xee, 0x95, 0xc7, 0x7d, 0x09, 0x28, 0xf6, 0x4d, 0xe2, 0x84,
	0xbe, 0xf0, 0x63, 0xc7, 0x41, 0x9b, 0x00, 0xa3, 0x6b, 0x8f, 0x79, 0x66, 0xe0, 0x0f, 0x68, 0x23,
	0xbf, 0x5d, 0xd8, 0x29, 0x1b, 0x65, 0x41, 0xe9, 0xf9, 0x03, 0x8a, 0x9e, 0x42, 0xf9, 0xd6, 0x23,
	0x36, 0xe6, 0xec, 0xd0, 0x23, 0x41, 0xe8, 0xf9, 0x03, 0xf4, 0x25, 0x94, 0x06, 0x2a, 0x58, 0xe1,
	0xd5, 0xd2, 0xfe, 0xda, 0x5e, 0x2c, 0x1b, 0x7b, 0x61, 0x26, 0x8c, 0x48, 0x0c, 0xfd, 0x0c, 0xaa,
	0x7d, 0xec, 0x3a, 0xd8, 0x37, 0xaf, 0xc8, 0x80, 0x61, 0x5f, 0x78, 0xbd, 0xbc, 0xbf, 0x9e, 0xf8,
	0xee, 0x58, 0x48, 0x1c, 0x09, 0x01, 0xa3, 0xd2, 0x8f, 0x9d, 0xd0, 0x33, 0xa8, 0x8e, 0x06, 0xd6,
	0x1d, 0x71, 0xfb, 0x66, 0xdf, 0x1a, 0x62, 0xda, 0x58, 0x14, 0x1e, 0x57, 0x14, 0xf1, 0x98, 0xd3,
	0xd0, 0x5b, 0xa8, 0x59, 0x01, 0xf3, 0x4c, 0x4e, 0x34, 0x85, 0xb7, 0x8d, 0xa2, 0x30, 0xf3, 0x2c,
	0x61, 0x26, 0x4c, 0xce, 0x5e, 0x33, 0x60, 0xde, 0xc5, 0xc0, 0xba, 0xfb, 0x25, 0x17, 0x35, 0xaa,
	0x56, 0xfc, 0x88, 0x5e, 0xc3, 0xa2, 0xf4, 0xa0, 0x51, 0x12, 0x3a, 0x56, 0x33, 0x5c, 0x35, 0x94,
	0x08, 0x7a, 0x01, 0xcb, 0x32, 0x5d, 0x4e, 0xe0, 0xcb, 0xbc, 0x94, 0x45, 0xb6, 0xab, 0x82, 0x7a,
	0xa8, 0x88, 0xba, 0x03, 0xd5, 0x84, 0x4d, 0xb4, 0x01, 0x8d, 0x04, 0xc1, 0xec, 0x9d, 0x75, 0x2f,
	0xda, 0xad, 0xce, 0x51, 0xa7, 0x55, 0x9f, 0x43, 0xeb, 0xb0, 0x96, 0xe4, 0xb6, 0xcf, 0x9a, 0x07,
	0x27, 0xed, 0xc3, 0x7a, 0x0e, 0x69, 0xf0, 0x38, 0xc9, 0x3a, 0xec, 0x74, 0x25, 0x2f, 0xaf, 0xff,
	0x1f, 0x2c, 0x1f, 0x63, 0xc6, 0xa3, 0x34, 0xf0, 0x87, 0x00, 0x53, 0x36, 0xb5, 0x0b, 0xf4, 0x36,
	0xd4, 0x22, 0x51, 0x3a, 0xf2, 0x5c, 0x8a, 0xd1, 0x3e, 0x94, 0xa5, 0xac, 0x7b, 0xe5, 0x09, 0xe9,
	0x74, 0x75, 0xc3, 0xf4, 0x19, 0xa5, 0x40, 0xfd, 0xd2, 0xff, 0x04, 0xeb, 0x2d, 0x1f, 0x5b, 0x0c,
	0x9f, 0xfb, 0xbd, 0x91, 0x63, 0x31, 0x1c, 0x37, 0xfe, 0x5f, 0x28, 0x44, 0x7b, 0xb0, 0x6a, 0x0f,
	0xb0, 0xe5, 0x9b, 0xc9, 0xa2, 0xf3, 0xa9, 0x29, 0x19, 0x2b, 0x82, 0x75, 0x11, 0xab, 0xbc, 0x7e,
	0x01, 0x5a, 0x96, 0x03, 0x1f, 0x11, 0xd2, 0x77, 0x80, 0xa4, 0xa6, 0x0b, 0x3e, 0x13, 0xb3, 0x12,
	0x89, 0x1e, 0xc1, 0x02, 0x71, 0x1d, 0xfc, 0x7b, 0xe1, 0x62, 0xd5, 0x90, 0x07, 0x3e, 0x45, 0xd1,
	0x90, 0x45, 0x73, 0xad, 0x66, 0x4c, 0x5f, 0x83, 0xd5, 0x84, 0x05, 0xe9, 0xac, 0xfe, 0xaf, 0x1c,
	0x34, 0x8e, 0x31, 0x33, 0xb0, 0xed, 0x0d, 0x87, 0xd8, 0x75, 0xb8, 0x6f, 0x74, 0xa6, 0xfd, 0x2d,
	0x58, 0x92, 0x83, 0x65, 0x06, 0xc4, 0x91, 0xf3, 0x5c, 0x35, 0x40, 0x92, 0x7a, 0xc4, 0xa1, 0xdc,
	0x41, 0x71, 0xd9, 0x08, 0x37, 0xaa, 0x86, 0x3c, 0x4c, 0x8e, 0xe5, 0xfc, 0x0f, 0x1b, 0xcb, 0xf8,
	0x4d, 0xb0, 0xf0, 0xb0, 0x9b, 0x60, 0x13, 0x80, 0x62, 0x4a, 0x89, 0xe7, 0xf2, 0x28, 0x16, 0x45,
	0x52, 0xca, 0x8a, 0xd2, 0x71, 0xf4, 0x37, 0xb0, 0x9e, 0x11, 0xbd, 0x2a, 0xe4, 0x6b, 0x58, 0xe0,
	0xf1, 0xd2, 0x46, 0x6e, 0xbb, 0x30, 0xbd, 0x88, 0x52, 0x46, 0xff, 0x0e, 0x96, 0x4e, 0xc8, 0x0d,
	0x9e, 0x99, 0xba, 0x4d, 0x00, 0x76, 0xed, 0x7b, 0xbf, 0x8b, 0x67, 0xae, 0x2c, 0x28, 0x22, 0x71,
	0xeb, 0x50, 0x1a, 0x90, 0x1b, 0xcc, 0xb9, 0x2a, 0x77, 0x45, 0x7e, 0xee, 0x11, 0x47, 0xff, 0x03,
	0x54, 0xa4, 0x05, 0xe5, 0xde, 0x2e, 0xac, 0x08, 0x51, 0x42, 0x4d, 0x7c, 0x75, 0x85, 0x6d, 0x16,
	0x58, 0x03, 0x61, 0xac, 0x64, 0xd4, 0x38, 0xa3, 0x43, 0xdb, 0x21, 0x99, 0xbb, 0x43, 0xa8, 0x79,
	0xe5, 0x63, 0xac, 0xba, 0x7a, 0x91, 0xd0, 0x23, 0x1f, 0x63, 0xf4, 0x12, 0x6a, 0x9c, 0x6a, 0x0a,
	0x4d, 0x1f, 0x02, 0x8f, 0x59, 0xca, 0x6c, 0x95, 0x93, 0xb9, 0xbd, 0x77, 0x9c, 0xa8, 0x7f, 0x0b,
	0x2b, 0xad, 0x6b, 0x6c, 0xdf, 0x70, 0x8a, 0x13, 0x06, 0xb9, 0x0d, 0x95, 0x2b, 0xdf, 0x1b, 0x9a,
	0xc9, 0x48, 0x81, 0xd3, 0x7a, 0x32, 0xda, 0xe7, 0xb0, 0xcc, 0x2c, 0xbf, 0x8f, 0x59, 0x24, 0x23,
	0x3b, 0xb6, 0x22, 0xa9, 0x52, 0x4a, 0xdf, 0x05, 0x14, 0x57, 0xae, 0xe2, 0x7b, 0x04, 0x0b, 0xdc,
	0x2b, 0x47, 0xc5, 0x24, 0x0f, 0xba, 0x01, 0x6b, 0x42, 0xf6, 0x00, 0x63, 0x37, 0xe1, 0xcc, 0xd4,
	0x8c, 0x6f, 0x43, 0x85, 0x61, 0xca, 0x78, 0x4a, 0x4d, 0x8a, 0x59, 0xd8, 0xad, 0x9c, 0xd6, 0x23,
	0x4e, 0x17, 0x33, 0xbd, 0x09, 0x8f, 0xd3, 0x3a, 0x95, 0x0f, 0xaf, 0xa0, 0x2e, 0xcc, 0x9a, 0xef,
	0xef, 0xa2, 0xef, 0x73, 0xe2, 0xfb, 0xaa, 0xa0, 0x1f, 0xdc, 0x29, 0x15, 0x5d, 0x58, 0x6f, 0xf1,
	0x7b, 0xe2, 0x34, 0xe0, 0xf9, 0xe6, 0x4a, 0xe8, 0x69, 0xd4, 0x0c, 0x5a, 0x78, 0x23, 0x38, 0x66,
	0x53, 0x39, 0x57, 0x94, 0xce, 0x35, 0xe3, 0xbc, 0x03, 0x95, 0x1c, 0xc5, 0x3b, 0xd0, 0x37, 0x40,
	0xcb, 0x52, 0xaa, 0x46, 0xd7, 0x10, 0xbd, 0x7b, 0xe9, 0x39, 0xd6, 0x9d, 0x70, 0xba, 0xc5, 0x67,
	0x6c, 0x66, 0x36, 0x36, 0xa0, 0xcc, 0xc8, 0x10, 0x53, 0x66, 0x0d, 0x47, 0xca, 0xde, 0x98, 0xa0,
	0xef, 0x83, 0x96, 0xa5, 0x73, 0x5c, 0x11, 0x39, 0xd5, 0xb9, 0xd8, 0x54, 0xeb, 0x17, 0xf0, 0xe4,
	0x18, 0xb3, 0xa3, 0x78, 0xbb, 0x7c, 0xa4, 0x17, 0x5f, 0x88, 0x3b, 0x29, 0xa5, 0x71, 0xec, 0x83,
	0x6c, 0x53, 0xe5, 0x83, 0x38, 0xe8, 0xd7, 0xb0, 0x7e, 0xec, 0x5b, 0xee, 0xff, 0xd2, 0x8b, 0xb1,
	0xa5, 0x42, 0xdc, 0xd2, 0x09, 0x68, 0x59, 0x96, 0x94, 0x77, 0x0d, 0x28, 0xf6, 0x39, 0x37, 0xea,
	0xda, 0xf0, 0x38, 0xd6, 0x96, 0x8f, 0x6b, 0xfb, 0x0c, 0x50, 0xd3, 0x71, 0x9a, 0x47, 0x6f, 0x1f,
	0xd4, 0xca, 0xfa, 0x8f, 0x61, 0x35, 0x21, 0xae, 0xac, 0x6e, 0xc1, 0x92, 0xec, 0xd2, 0x78, 0x75,
	0x60, 0x10, 0x15, 0x50, 0xff, 0x1c, 0x1e, 0x89, 0x46, 0x7a, 0xb0, 0xa1, 0x2f, 0x61, 0x2d, 0xf5,
	0xc1, 0x38, 0x40, 0xfb, 0xda, 0x72, 0xfb, 0xe3, 0x00, 0xd5, 0x51, 0xdf, 0x83, 0x55, 0x03, 0x8f,
	0x3c, 0x9f, 0x35, 0x6d, 0x46, 0x6e, 0x67, 0x5e, 0x84, 0xfa, 0x63, 0x78, 0x94, 0x94, 0x57, 0x6d,
	0xfd, 0x46, 0xb4, 0x93, 0x81, 0x6d, 0xec, 0x32, 0x61, 0xfb, 0x14, 0x3f, 0xe4, 0x3d, 0x94, 0xa1,
	0xe7, 0xe3, 0x8d, 0xf9, 0x93, 0xf0, 0x69, 0x8b, 0x6b, 0x52, 0x71, 0x6c, 0x43, 0x25, 0x1c, 0xbb,
	0x01, 0xa1, 0xe1, 0x50, 0x83, 0xd4, 0x77, 0x42, 0x28, 0xd3, 0xff, 0x99, 0x83, 0xe5, 0x77, 0x01,
	0xb1, 0x6f, 0x4e, 0x2d, 0x66, 0x5f, 0xf3, 0x87, 0x9f, 0x3f, 0xb0, 0x7c, 0x33, 0x30, 0x5d, 0x6b,
	0x28, 0x97, 0xed, 0xb2, 0x51, 0xe2, 0x84, 0x33, 0xce, 0x3c, 0x80, 0xa2, 0x37, 0xe2, 0x6f, 0x8e,
	0xbc, 0xd5, 0x97, 0xf6, 0x77, 0x12, 0xef, 0x45, 0x52, 0xd5, 0xde, 0xb9, 0x14, 0x6d, 0xf3, 0x7d,
	0xdc, 0x08, 0x3f, 0xe4, 0x01, 0x0a, 0x03, 0xd1, 0xe5, 0xbf, 0xc8, 0x8f, 0x1d, 0x87, 0x2f, 0xa4,
	0x43, 0xfe, 0xad, 0x39, 0xc4, 0x94, 0x5a, 0x7d, 0x2c, 0x16, 0xbe, 0xb2, 0x51, 0x11, 0xc4, 0x53,
	0x49, 0xd3, 0xbe, 0x86, 0x4a, 0x5c, 0x2d, 0xaa, 0x43, 0xe1, 0x06, 0xdf, 0x29, 0x47, 0xf9, 0x4f,
	0x9e, 0xa7, 0x5b, 0x6b, 0x10, 0xe0, 0x30, 0x4f, 0xe2, 0xf0, 0x75, 0xfe, 0xab, 0x9c, 0xfe, 0x3d,
	0xd4, 0x84, 0x73, 0xd8, 0xb9, 0xc0, 0xb3, 0x96, 0xf9, 0x2d, 0x58, 0x12, 0x0c, 0xb5, 0xb0, 0x4a,
	0x5d, 0x22, 0x75, 0xf2, 0xed, 0x4e, 0x3d, 0xba, 0x85, 0xf4, 0xa3, 0xfb, 0xf7, 0x02, 0x3c, 0xee,
	0x32, 0xcb, 0x67, 0xe3, 0x9c, 0xcc, 0xac, 0x70, 0x22, 0xf5, 0xf9, 0x54, 0xea, 0x7f, 0x31, 0x4e,
	0x7d, 0x41, 0xa4, 0xfe, 0x8b, 0x44, 0xea, 0xb3, 0x6d, 0x4d, 0x29, 0x01, 0xc7, 0x3a, 0xe1, 0x56,
	0x3d, 0x2f, 0x5c, 0x88, 0xce, 0xe9, 0xc0, 0x17, 0x26, 0x02, 0xff, 0x4a, 0x79, 0x29, 0x5a, 0x6a,
	0x51, 0xb8, 0xf2, 0xf4, 0x9e, 0x2e, 0x90, 0x21, 0xf0, 0x6e, 0x43, 0xfb, 0xb0, 0x46, 0x83, 0xd1,
	0x68, 0x80, 0x87, 0xd8, 0x65, 0x26, 0x9f, 0x29, 0x17, 0x0f, 0x78, 0x1a, 0x8a, 0xc2, 0xc8, 0xea,
	0x98, 0xd9, 0x92, 0xbc, 0x8e, 0xc3, 0x61, 0x40, 0xec, 0x1b, 0x37, 0x18, 0x0a, 0xec, 0x50, 0x35,
	0xaa, 0x63, 0xea, 0x59, 0x30, 0xfc, 0xa8, 0xb6, 0xf8, 0x47, 0x0e, 0x9e, 0x4c, 0xa4, 0x4f, 0x8d,
	0x50, 0xb2, 0xca, 0xb9, 0x54, 0x95, 0xd1, 0x1b, 0x58, 0x19, 0xca, 0x8e, 0x32, 0x47, 0x38, 0x5c,
	0x87, 0xf3, 0x62, 0x6b, 0xdb, 0x48, 0xe4, 0x24, 0xd5, 0x77, 0x46, 0x6d, 0x98, 0x6a, 0xc4, 0x5d,
	0x58, 0xa1, 0xbc, 0x66, 0xae, 0x8d, 0x4d, 0xe2, 0x9a, 0x1f, 0x02, 0x1c, 0x60, 0x35, 0x1f, 0xb5,
	0x90, 0xd1, 0x71, 0xdf, 0x71, 0xb2, 0xfe, 0x0e, 0x9e, 0xb4, 0x2c, 0xd7, 0xc6, 0x83, 0x1f, 0xd0,
	0x5b, 0xc9, 0x40, 0xf2, 0xe9, 0x76, 0xd5, 0xa0, 0x31, 0xa9, 0x52, 0x5d, 0x56, 0xdf, 0xc0, 0x27,
	0xc7, 0x38, 0x96, 0x9c, 0x2e, 0xb3, 0x18, 0xa1, 0x8c, 0xd8, 0xd1, 0x0e, 0xbd, 0x09, 0xf0, 0x1e,
	0xf7, 0x89, 0x6b, 0xf2, 0x87, 0x45, 0x18, 0x9e, 0x37, 0xca, 0x82, 0x72, 0x49, 0x86, 0x98, 0xef,
	0x7b, 0xd8, 0x75, 0x24, 0x33, 0x2f, 0x98, 0x45, 0xec, 0x3a, 0x9c, 0xa5, 0xff, 0x25, 0x07, 0x5b,
	0x53, 0x95, 0x47, 0x58, 0x63, 0xcd, 0x0e, 0x7c, 0x9f, 0xd7, 0x3f, 0x0c, 0x21, 0xfe, 0x06, 0xac,
	0x2a, 0x66, 0x57, 0xf2, 0xc4, 0x63, 0xc0, 0xd1, 0x0e, 0xf3, 0x98, 0x35, 0x30, 0x29, 0x2f, 0x2c,
	0x76, 0x4c, 0x91, 0x6f, 0x55, 0xfb, 0x15, 0xc1, 0xea, 0x4a, 0x8e, 0xb0, 0xaa, 0x5f, 0x82, 0x36,
	0xf6, 0xe1, 0x2d, 0xc6, 0xa3, 0xe6, 0xe0, 0x01, 0xf7, 0xfb, 0xac, 0xac, 0x9e, 0xc0, 0xd3, 0x4c,
	0xad, 0x2a, 0xb0, 0x75, 0x28, 0x11, 0x6a, 0x5a, 0x9c, 0x16, 0x3e, 0x34, 0x84, 0x0a, 0x11, 0x84,
	0x60, 0xde, 0xb7, 0xdc, 0x1b, 0xe5, 0xb0, 0xf8, 0xad, 0xff, 0x39, 0x07, 0x5b, 0x17, 0x81, 0x4b,
	0xe8, 0xf5, 0x58, 0xe9, 0x21, 0xa6, 0xd8, 0x67, 0xb3, 0x61, 0x29, 0xf7, 0x34, 0x36, 0x70, 0x6a,
	0x0f, 0xb0, 0xe3, 0x63, 0xe6, 0xb9, 0x03, 0xe2, 0x62, 0x93, 0x62, 0xdb, 0x73, 0x1d, 0x1a, 0x6e,
	0xc8, 0x92, 0xda, 0x95, 0x44, 0x5d, 0x87, 0xed, 0xe9, 0x1e, 0xc8, 0xa8, 0x76, 0x7f, 0x0e, 0x8b,
	0xea, 0xa6, 0x40, 0x1c, 0x35, 0x0b, 0x28, 0xd4, 0x3b, 0x7b, 0x7b, 0x76, 0xfe, 0xab, 0xb3, 0xfa,
	0x1c, 0x5a, 0x81, 0xaa, 0xa2, 0x1d, 0xb5, 0x4f, 0x9b, 0x27, 0xed, 0x7a, 0x0e, 0xd5, 0x60, 0x49,
	0x91, 0x04, 0x21, 0xbf, 0xfb, 0x47, 0xa8, 0xc4, 0x01, 0x12, 0x5a, 0x83, 0x95, 0xf8, 0xd9, 0xfc,
	0xa6, 0x6d, 0x9c, 0xd7, 0xe7, 0x38, 0x60, 0x4f, 0x90, 0xf9, 0xd7, 0xe6, 0xf9, 0xd9, 0xc9, 0x6f,
	0xea, 0x39, 0xb4, 0xc1, 0x9f, 0xc5, 0x18, 0x4f, 0x1a, 0x93, 0xdc, 0x3c, 0xda, 0xe4, 0x5b, 0x65,
	0x8c, 0xdb, 0x3b, 0x33, 0xda, 0xdd, 0x4b, 0xa3, 0xd3, 0xba, 0x6c, 0x1f, 0xd6, 0x0b, 0xfb, 0x7f,
	0xab, 0x41, 0xfc, 0x4f, 0x51, 0xe8, 0x08, 0x8a, 0x0a, 0xd2, 0xa3, 0xa7, 0x29, 0x18, 0x17, 0xff,
	0x9b, 0x80, 0xb6, 0x91, 0xcd, 0x54, 0x63, 0x34, 0x87, 0xfa, 0x80, 0x26, 0x21, 0x35, 0x7a, 0x99,
	0xf8, 0x6a, 0x2a, 0xe8, 0xd7, 0x5e, 0xcd, 0x94, 0x8b, 0x0c, 0x19, 0xb0, 0x14, 0xc3, 0xc1, 0x68,
	0x2b, 0x09, 0xea, 0x26, 0x30, 0xb8, 0xb6, 0x3d, 0x5d, 0x20, 0xd2, 0xd9, 0x83, 0x4a, 0x7c, 0x95,
	0x41, 0xc9, 0x6f, 0x32, 0xb6, 0x22, 0xed, 0xd3, 0x7b, 0x24, 0x22, 0xb5, 0x0e, 0xaf, 0x6d, 0x0a,
	0x9c, 0xa2, 0x17, 0xe9, 0x44, 0x66, 0x42, 0x77, 0xed, 0xe5, 0x2c, 0xb1, 0xc8, 0xca, 0x4f, 0x61,
	0x9e, 0x2f, 0x47, 0xa8, 0x91, 0x84, 0xd2, 0x63, 0x2c, 0xab, 0xad, 0x67, 0x70, 0xa2, 0xcf, 0xcf,
	0x01, 0xc6, 0xd8, 0x0d, 0x7d, 0x92, 0x2c, 0x44, 0x1a, 0x31, 0x6a, 0x5b, 0x53, 0xf9, 0x91, 0xc2,
	0x6f, 0x61, 0x39, 0x09, 0xc6, 0x90, 0x3e, 0xf9, 0x51, 0x1a, 0xfd, 0x69, 0xcf, 0xee, 0x95, 0x89,
	0x94, 0x5b, 0x50, 0x4f, 0xaf, 0x84, 0xe8, 0x79, 0x46, 0xaa, 0x26, 0x76, 0x4f, 0xed, 0xc5, 0x0c,
	0xa9, 0x44, 0x27, 0x4f, 0x80, 0xb6, 0x74, 0x27, 0x4f, 0x83, 0x8a, 0xe9, 0x4e, 0x9e, 0x8e, 0xfe,
	0x84, 0xa1, 0x49, 0xac, 0x86, 0x26, 0x0a, 0x9f, 0x0d, 0x10, 0x53, 0x86, 0xa6, 0x83, 0x3e, 0x39,
	0x32, 0x31, 0xd4, 0x91, 0x1a, 0x99, 0x49, 0xf8, 0x92, 0x1a, 0x99, 0x0c, 0xc0, 0xa2, 0xcf, 0xa1,
	0x5f, 0x43, 0x35, 0x01, 0x30, 0xd0, 0xa7, 0x93, 0x81, 0xa7, 0xf5, 0xea, 0xf7, 0x89, 0xa4, 0x4a,
	0x9c, 0x80, 0x67, 0x93, 0x25, 0xce, 0xc2, 0x89, 0x93, 0x25, 0xce, 0xc4, 0x78, 0x2a, 0xf3, 0x13,
	0x18, 0x30, 0x9d, 0xf9, 0x69, 0x70, 0x34, 0x9d, 0xf9, 0xa9, 0x60, 0x52, 0x9f, 0x43, 0xbf, 0x85,
	0x5a, 0x6a, 0xfb, 0x42, 0xcf, 0x1e, 0xb0, 0xda, 0x6a, 0xcf, 0xef, 0x17, 0x8a, 0xe7, 0x2a, 0xbd,
	0xda, 0xa4, 0x72, 0x35, 0x65, 0x99, 0x4a, 0xe5, 0x6a, 0xea, 0x7e, 0x34, 0x87, 0x6e, 0x05, 0x9c,
	0xcb, 0x5a, 0x62, 0xd0, 0xeb, 0x74, 0xbe, 0xef, 0xd9, 0xa3, 0xb4, 0xff, 0x7f, 0x98, 0x70, 0x64,
	0xf7, 0x7b, 0x58, 0xcd, 0xd8, 0x2f, 0xd0, 0xab, 0x29, 0xeb, 0x78, 0x7a, 0xaf, 0xd1, 0x76, 0x66,
	0x0b, 0x46, 0xb6, 0xee, 0xa0, 0x31, 0xed, 0xe9, 0x47, 0x49, 0xbf, 0x67, 0xec, 0x28, 0xda, 0x67,
	0x0f, 0x94, 0x0e, 0x4d, 0xbf, 0x5f, 0x14, 0xff, 0x1e, 0xfa, 0xd1, 0x7f, 0x02, 0x00, 0x00, 0xff,
	0xff, 0x0b, 0xe3, 0x89, 0xe5, 0x41, 0x1a, 0x00, 0x00,
}
