// Code generated by protoc-gen-gogo.
// source: src/currencysvr/currency.proto
// DO NOT EDIT!

/*
	Package Currency is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/currencysvr/currency.proto

	It has these top-level messages:
		GetUserCurrencyReq
		GetUserCurrencyResp
		UserCurrency
		BatGetUserCurrencyReq
		BatGetUserCurrencyResp
		GetUserCurrencyLogReq
		UserCurrencyLogInfo
		GetUserCurrencyLogResp
		AddUserCurrencyReq
		AddUserCurrencyResp
		HasAddedUserCurrencyReq
		HasAddedUserCurrencyResp
		GetAppCurrencyReq
		GetAppCurrencyResp
		AppCurrencyUpdate
		UpdateAppCurrencyReq
		UpdateAppCurrencyResp
		GetAppCurrencyUpdateLogReq
		GetAppCurrencyUpdateLogResp
		FreezeUserCurrencyReq
		FreezeUserCurrencyResp
		ConfirmFrozenOrderReq
		ConfirmFrozenOrderResp
*/
package Currency

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type ADD_CURRENCY_REASON int32

const (
	ADD_CURRENCY_REASON_UNKNOWN                               ADD_CURRENCY_REASON = 0
	ADD_CURRENCY_REASON_LOGIN_REWARD                          ADD_CURRENCY_REASON = 1
	ADD_CURRENCY_REASON_USER_MISSION                          ADD_CURRENCY_REASON = 2
	ADD_CURRENCY_REASON_SEND_PRESENT                          ADD_CURRENCY_REASON = 3
	ADD_CURRENCY_REASON_INTERACTION_INTIMACY                  ADD_CURRENCY_REASON = 4
	ADD_CURRENCY_REASON_USER_TAG_GAME_CARD                    ADD_CURRENCY_REASON = 5
	ADD_CURRENCY_REASON_TT_ZHUANGYUAN                         ADD_CURRENCY_REASON = 6
	ADD_CURRENCY_REASON_CHANNEL_LOTTERY                       ADD_CURRENCY_REASON = 7
	ADD_CURRENCY_REASON_CHANNEL_INVITE_NON_ENTER_CHANNEL_USER ADD_CURRENCY_REASON = 8
	ADD_CURRENCY_REASON_SOCIAL_COMMUNITY_LIKE_ACTIVITY_PUSH   ADD_CURRENCY_REASON = 9
	ADD_CURRENCY_REASON_BUY_CHANNEL_WEDDING                   ADD_CURRENCY_REASON = 10
)

var ADD_CURRENCY_REASON_name = map[int32]string{
	0:  "UNKNOWN",
	1:  "LOGIN_REWARD",
	2:  "USER_MISSION",
	3:  "SEND_PRESENT",
	4:  "INTERACTION_INTIMACY",
	5:  "USER_TAG_GAME_CARD",
	6:  "TT_ZHUANGYUAN",
	7:  "CHANNEL_LOTTERY",
	8:  "CHANNEL_INVITE_NON_ENTER_CHANNEL_USER",
	9:  "SOCIAL_COMMUNITY_LIKE_ACTIVITY_PUSH",
	10: "BUY_CHANNEL_WEDDING",
}
var ADD_CURRENCY_REASON_value = map[string]int32{
	"UNKNOWN":                               0,
	"LOGIN_REWARD":                          1,
	"USER_MISSION":                          2,
	"SEND_PRESENT":                          3,
	"INTERACTION_INTIMACY":                  4,
	"USER_TAG_GAME_CARD":                    5,
	"TT_ZHUANGYUAN":                         6,
	"CHANNEL_LOTTERY":                       7,
	"CHANNEL_INVITE_NON_ENTER_CHANNEL_USER": 8,
	"SOCIAL_COMMUNITY_LIKE_ACTIVITY_PUSH":   9,
	"BUY_CHANNEL_WEDDING":                   10,
}

func (x ADD_CURRENCY_REASON) Enum() *ADD_CURRENCY_REASON {
	p := new(ADD_CURRENCY_REASON)
	*p = x
	return p
}
func (x ADD_CURRENCY_REASON) String() string {
	return proto.EnumName(ADD_CURRENCY_REASON_name, int32(x))
}
func (x *ADD_CURRENCY_REASON) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ADD_CURRENCY_REASON_value, data, "ADD_CURRENCY_REASON")
	if err != nil {
		return err
	}
	*x = ADD_CURRENCY_REASON(value)
	return nil
}
func (ADD_CURRENCY_REASON) EnumDescriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{0} }

type AppCurrencyUpdate_OP int32

const (
	AppCurrencyUpdate_NOP AppCurrencyUpdate_OP = 0
	AppCurrencyUpdate_SET AppCurrencyUpdate_OP = 1
	AppCurrencyUpdate_ADD AppCurrencyUpdate_OP = 2
	AppCurrencyUpdate_SUB AppCurrencyUpdate_OP = 3
)

var AppCurrencyUpdate_OP_name = map[int32]string{
	0: "NOP",
	1: "SET",
	2: "ADD",
	3: "SUB",
}
var AppCurrencyUpdate_OP_value = map[string]int32{
	"NOP": 0,
	"SET": 1,
	"ADD": 2,
	"SUB": 3,
}

func (x AppCurrencyUpdate_OP) Enum() *AppCurrencyUpdate_OP {
	p := new(AppCurrencyUpdate_OP)
	*p = x
	return p
}
func (x AppCurrencyUpdate_OP) String() string {
	return proto.EnumName(AppCurrencyUpdate_OP_name, int32(x))
}
func (x *AppCurrencyUpdate_OP) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(AppCurrencyUpdate_OP_value, data, "AppCurrencyUpdate_OP")
	if err != nil {
		return err
	}
	*x = AppCurrencyUpdate_OP(value)
	return nil
}
func (AppCurrencyUpdate_OP) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorCurrency, []int{14, 0}
}

type ConfirmFrozenOrderReq_OP int32

const (
	ConfirmFrozenOrderReq_NOP      ConfirmFrozenOrderReq_OP = 0
	ConfirmFrozenOrderReq_COMMIT   ConfirmFrozenOrderReq_OP = 1
	ConfirmFrozenOrderReq_ROLLBACK ConfirmFrozenOrderReq_OP = 2
)

var ConfirmFrozenOrderReq_OP_name = map[int32]string{
	0: "NOP",
	1: "COMMIT",
	2: "ROLLBACK",
}
var ConfirmFrozenOrderReq_OP_value = map[string]int32{
	"NOP":      0,
	"COMMIT":   1,
	"ROLLBACK": 2,
}

func (x ConfirmFrozenOrderReq_OP) Enum() *ConfirmFrozenOrderReq_OP {
	p := new(ConfirmFrozenOrderReq_OP)
	*p = x
	return p
}
func (x ConfirmFrozenOrderReq_OP) String() string {
	return proto.EnumName(ConfirmFrozenOrderReq_OP_name, int32(x))
}
func (x *ConfirmFrozenOrderReq_OP) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ConfirmFrozenOrderReq_OP_value, data, "ConfirmFrozenOrderReq_OP")
	if err != nil {
		return err
	}
	*x = ConfirmFrozenOrderReq_OP(value)
	return nil
}
func (ConfirmFrozenOrderReq_OP) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorCurrency, []int{21, 0}
}

// =================================================================
//
// 获取用户红钻
//
// =================================================================
type GetUserCurrencyReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserCurrencyReq) Reset()                    { *m = GetUserCurrencyReq{} }
func (m *GetUserCurrencyReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserCurrencyReq) ProtoMessage()               {}
func (*GetUserCurrencyReq) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{0} }

func (m *GetUserCurrencyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserCurrencyResp struct {
	Currency          int32 `protobuf:"varint,1,req,name=currency" json:"currency"`
	ValuableCurrency  int32 `protobuf:"varint,2,opt,name=valuable_currency,json=valuableCurrency" json:"valuable_currency"`
	ValuelessCurrency int32 `protobuf:"varint,3,opt,name=valueless_currency,json=valuelessCurrency" json:"valueless_currency"`
}

func (m *GetUserCurrencyResp) Reset()                    { *m = GetUserCurrencyResp{} }
func (m *GetUserCurrencyResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserCurrencyResp) ProtoMessage()               {}
func (*GetUserCurrencyResp) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{1} }

func (m *GetUserCurrencyResp) GetCurrency() int32 {
	if m != nil {
		return m.Currency
	}
	return 0
}

func (m *GetUserCurrencyResp) GetValuableCurrency() int32 {
	if m != nil {
		return m.ValuableCurrency
	}
	return 0
}

func (m *GetUserCurrencyResp) GetValuelessCurrency() int32 {
	if m != nil {
		return m.ValuelessCurrency
	}
	return 0
}

type UserCurrency struct {
	Uid               uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Currency          int32  `protobuf:"varint,2,req,name=currency" json:"currency"`
	ValuableCurrency  int32  `protobuf:"varint,3,opt,name=valuable_currency,json=valuableCurrency" json:"valuable_currency"`
	ValuelessCurrency int32  `protobuf:"varint,4,opt,name=valueless_currency,json=valuelessCurrency" json:"valueless_currency"`
}

func (m *UserCurrency) Reset()                    { *m = UserCurrency{} }
func (m *UserCurrency) String() string            { return proto.CompactTextString(m) }
func (*UserCurrency) ProtoMessage()               {}
func (*UserCurrency) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{2} }

func (m *UserCurrency) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserCurrency) GetCurrency() int32 {
	if m != nil {
		return m.Currency
	}
	return 0
}

func (m *UserCurrency) GetValuableCurrency() int32 {
	if m != nil {
		return m.ValuableCurrency
	}
	return 0
}

func (m *UserCurrency) GetValuelessCurrency() int32 {
	if m != nil {
		return m.ValuelessCurrency
	}
	return 0
}

type BatGetUserCurrencyReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatGetUserCurrencyReq) Reset()                    { *m = BatGetUserCurrencyReq{} }
func (m *BatGetUserCurrencyReq) String() string            { return proto.CompactTextString(m) }
func (*BatGetUserCurrencyReq) ProtoMessage()               {}
func (*BatGetUserCurrencyReq) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{3} }

func (m *BatGetUserCurrencyReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatGetUserCurrencyResp struct {
	UserCurrencyList []*UserCurrency `protobuf:"bytes,1,rep,name=user_currency_list,json=userCurrencyList" json:"user_currency_list,omitempty"`
}

func (m *BatGetUserCurrencyResp) Reset()                    { *m = BatGetUserCurrencyResp{} }
func (m *BatGetUserCurrencyResp) String() string            { return proto.CompactTextString(m) }
func (*BatGetUserCurrencyResp) ProtoMessage()               {}
func (*BatGetUserCurrencyResp) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{4} }

func (m *BatGetUserCurrencyResp) GetUserCurrencyList() []*UserCurrency {
	if m != nil {
		return m.UserCurrencyList
	}
	return nil
}

type GetUserCurrencyLogReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	BeginTime uint32 `protobuf:"varint,2,opt,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime   uint32 `protobuf:"varint,3,opt,name=end_time,json=endTime" json:"end_time"`
	DescLike  string `protobuf:"bytes,4,opt,name=desc_like,json=descLike" json:"desc_like"`
}

func (m *GetUserCurrencyLogReq) Reset()                    { *m = GetUserCurrencyLogReq{} }
func (m *GetUserCurrencyLogReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserCurrencyLogReq) ProtoMessage()               {}
func (*GetUserCurrencyLogReq) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{5} }

func (m *GetUserCurrencyLogReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserCurrencyLogReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetUserCurrencyLogReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetUserCurrencyLogReq) GetDescLike() string {
	if m != nil {
		return m.DescLike
	}
	return ""
}

type UserCurrencyLogInfo struct {
	CValue uint32 `protobuf:"varint,1,req,name=c_value,json=cValue" json:"c_value"`
	OpTime string `protobuf:"bytes,2,req,name=op_time,json=opTime" json:"op_time"`
	Desc   string `protobuf:"bytes,3,req,name=desc" json:"desc"`
}

func (m *UserCurrencyLogInfo) Reset()                    { *m = UserCurrencyLogInfo{} }
func (m *UserCurrencyLogInfo) String() string            { return proto.CompactTextString(m) }
func (*UserCurrencyLogInfo) ProtoMessage()               {}
func (*UserCurrencyLogInfo) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{6} }

func (m *UserCurrencyLogInfo) GetCValue() uint32 {
	if m != nil {
		return m.CValue
	}
	return 0
}

func (m *UserCurrencyLogInfo) GetOpTime() string {
	if m != nil {
		return m.OpTime
	}
	return ""
}

func (m *UserCurrencyLogInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type GetUserCurrencyLogResp struct {
	LogInfos []*UserCurrencyLogInfo `protobuf:"bytes,1,rep,name=log_infos,json=logInfos" json:"log_infos,omitempty"`
}

func (m *GetUserCurrencyLogResp) Reset()                    { *m = GetUserCurrencyLogResp{} }
func (m *GetUserCurrencyLogResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserCurrencyLogResp) ProtoMessage()               {}
func (*GetUserCurrencyLogResp) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{7} }

func (m *GetUserCurrencyLogResp) GetLogInfos() []*UserCurrencyLogInfo {
	if m != nil {
		return m.LogInfos
	}
	return nil
}

type AddUserCurrencyReq struct {
	Uid         uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Currency    int32  `protobuf:"varint,2,req,name=currency" json:"currency"`
	MissionKey  string `protobuf:"bytes,3,req,name=mission_key,json=missionKey" json:"mission_key"`
	MissionDesc string `protobuf:"bytes,4,req,name=mission_desc,json=missionDesc" json:"mission_desc"`
	OpUid       uint32 `protobuf:"varint,5,req,name=op_uid,json=opUid" json:"op_uid"`
	WithStock   bool   `protobuf:"varint,6,opt,name=with_stock,json=withStock" json:"with_stock"`
	Appid       uint32 `protobuf:"varint,7,opt,name=appid" json:"appid"`
	IsValuable  bool   `protobuf:"varint,8,opt,name=is_valuable,json=isValuable" json:"is_valuable"`
	OrderId     string `protobuf:"bytes,9,opt,name=order_id,json=orderId" json:"order_id"`
	Reason      uint32 `protobuf:"varint,10,opt,name=reason" json:"reason"`
}

func (m *AddUserCurrencyReq) Reset()                    { *m = AddUserCurrencyReq{} }
func (m *AddUserCurrencyReq) String() string            { return proto.CompactTextString(m) }
func (*AddUserCurrencyReq) ProtoMessage()               {}
func (*AddUserCurrencyReq) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{8} }

func (m *AddUserCurrencyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserCurrencyReq) GetCurrency() int32 {
	if m != nil {
		return m.Currency
	}
	return 0
}

func (m *AddUserCurrencyReq) GetMissionKey() string {
	if m != nil {
		return m.MissionKey
	}
	return ""
}

func (m *AddUserCurrencyReq) GetMissionDesc() string {
	if m != nil {
		return m.MissionDesc
	}
	return ""
}

func (m *AddUserCurrencyReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *AddUserCurrencyReq) GetWithStock() bool {
	if m != nil {
		return m.WithStock
	}
	return false
}

func (m *AddUserCurrencyReq) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

func (m *AddUserCurrencyReq) GetIsValuable() bool {
	if m != nil {
		return m.IsValuable
	}
	return false
}

func (m *AddUserCurrencyReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AddUserCurrencyReq) GetReason() uint32 {
	if m != nil {
		return m.Reason
	}
	return 0
}

type AddUserCurrencyResp struct {
}

func (m *AddUserCurrencyResp) Reset()                    { *m = AddUserCurrencyResp{} }
func (m *AddUserCurrencyResp) String() string            { return proto.CompactTextString(m) }
func (*AddUserCurrencyResp) ProtoMessage()               {}
func (*AddUserCurrencyResp) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{9} }

type HasAddedUserCurrencyReq struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	MissionKey string `protobuf:"bytes,2,req,name=mission_key,json=missionKey" json:"mission_key"`
}

func (m *HasAddedUserCurrencyReq) Reset()                    { *m = HasAddedUserCurrencyReq{} }
func (m *HasAddedUserCurrencyReq) String() string            { return proto.CompactTextString(m) }
func (*HasAddedUserCurrencyReq) ProtoMessage()               {}
func (*HasAddedUserCurrencyReq) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{10} }

func (m *HasAddedUserCurrencyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HasAddedUserCurrencyReq) GetMissionKey() string {
	if m != nil {
		return m.MissionKey
	}
	return ""
}

type HasAddedUserCurrencyResp struct {
	Added bool `protobuf:"varint,1,req,name=added" json:"added"`
}

func (m *HasAddedUserCurrencyResp) Reset()         { *m = HasAddedUserCurrencyResp{} }
func (m *HasAddedUserCurrencyResp) String() string { return proto.CompactTextString(m) }
func (*HasAddedUserCurrencyResp) ProtoMessage()    {}
func (*HasAddedUserCurrencyResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCurrency, []int{11}
}

func (m *HasAddedUserCurrencyResp) GetAdded() bool {
	if m != nil {
		return m.Added
	}
	return false
}

type GetAppCurrencyReq struct {
	Appid uint32 `protobuf:"varint,1,req,name=appid" json:"appid"`
}

func (m *GetAppCurrencyReq) Reset()                    { *m = GetAppCurrencyReq{} }
func (m *GetAppCurrencyReq) String() string            { return proto.CompactTextString(m) }
func (*GetAppCurrencyReq) ProtoMessage()               {}
func (*GetAppCurrencyReq) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{12} }

func (m *GetAppCurrencyReq) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

type GetAppCurrencyResp struct {
	Amount uint64 `protobuf:"varint,1,opt,name=amount" json:"amount"`
}

func (m *GetAppCurrencyResp) Reset()                    { *m = GetAppCurrencyResp{} }
func (m *GetAppCurrencyResp) String() string            { return proto.CompactTextString(m) }
func (*GetAppCurrencyResp) ProtoMessage()               {}
func (*GetAppCurrencyResp) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{13} }

func (m *GetAppCurrencyResp) GetAmount() uint64 {
	if m != nil {
		return m.Amount
	}
	return 0
}

type AppCurrencyUpdate struct {
	Appid  uint32 `protobuf:"varint,1,req,name=appid" json:"appid"`
	Op     uint32 `protobuf:"varint,2,req,name=op" json:"op"`
	Amount uint64 `protobuf:"varint,3,req,name=amount" json:"amount"`
	OpUid  uint32 `protobuf:"varint,4,opt,name=op_uid,json=opUid" json:"op_uid"`
	OpDesc string `protobuf:"bytes,5,opt,name=op_desc,json=opDesc" json:"op_desc"`
	OpAt   uint32 `protobuf:"varint,6,opt,name=op_at,json=opAt" json:"op_at"`
}

func (m *AppCurrencyUpdate) Reset()                    { *m = AppCurrencyUpdate{} }
func (m *AppCurrencyUpdate) String() string            { return proto.CompactTextString(m) }
func (*AppCurrencyUpdate) ProtoMessage()               {}
func (*AppCurrencyUpdate) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{14} }

func (m *AppCurrencyUpdate) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

func (m *AppCurrencyUpdate) GetOp() uint32 {
	if m != nil {
		return m.Op
	}
	return 0
}

func (m *AppCurrencyUpdate) GetAmount() uint64 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *AppCurrencyUpdate) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *AppCurrencyUpdate) GetOpDesc() string {
	if m != nil {
		return m.OpDesc
	}
	return ""
}

func (m *AppCurrencyUpdate) GetOpAt() uint32 {
	if m != nil {
		return m.OpAt
	}
	return 0
}

type UpdateAppCurrencyReq struct {
	Update *AppCurrencyUpdate `protobuf:"bytes,1,req,name=update" json:"update,omitempty"`
}

func (m *UpdateAppCurrencyReq) Reset()                    { *m = UpdateAppCurrencyReq{} }
func (m *UpdateAppCurrencyReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateAppCurrencyReq) ProtoMessage()               {}
func (*UpdateAppCurrencyReq) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{15} }

func (m *UpdateAppCurrencyReq) GetUpdate() *AppCurrencyUpdate {
	if m != nil {
		return m.Update
	}
	return nil
}

type UpdateAppCurrencyResp struct {
}

func (m *UpdateAppCurrencyResp) Reset()                    { *m = UpdateAppCurrencyResp{} }
func (m *UpdateAppCurrencyResp) String() string            { return proto.CompactTextString(m) }
func (*UpdateAppCurrencyResp) ProtoMessage()               {}
func (*UpdateAppCurrencyResp) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{16} }

type GetAppCurrencyUpdateLogReq struct {
	Appid     uint32 `protobuf:"varint,1,req,name=appid" json:"appid"`
	BeginTime uint32 `protobuf:"varint,2,opt,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime   uint32 `protobuf:"varint,3,opt,name=end_time,json=endTime" json:"end_time"`
}

func (m *GetAppCurrencyUpdateLogReq) Reset()         { *m = GetAppCurrencyUpdateLogReq{} }
func (m *GetAppCurrencyUpdateLogReq) String() string { return proto.CompactTextString(m) }
func (*GetAppCurrencyUpdateLogReq) ProtoMessage()    {}
func (*GetAppCurrencyUpdateLogReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCurrency, []int{17}
}

func (m *GetAppCurrencyUpdateLogReq) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

func (m *GetAppCurrencyUpdateLogReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetAppCurrencyUpdateLogReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetAppCurrencyUpdateLogResp struct {
	UpdateList []*AppCurrencyUpdate `protobuf:"bytes,1,rep,name=update_list,json=updateList" json:"update_list,omitempty"`
}

func (m *GetAppCurrencyUpdateLogResp) Reset()         { *m = GetAppCurrencyUpdateLogResp{} }
func (m *GetAppCurrencyUpdateLogResp) String() string { return proto.CompactTextString(m) }
func (*GetAppCurrencyUpdateLogResp) ProtoMessage()    {}
func (*GetAppCurrencyUpdateLogResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCurrency, []int{18}
}

func (m *GetAppCurrencyUpdateLogResp) GetUpdateList() []*AppCurrencyUpdate {
	if m != nil {
		return m.UpdateList
	}
	return nil
}

type FreezeUserCurrencyReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Amount    uint32 `protobuf:"varint,2,req,name=amount" json:"amount"`
	AppId     string `protobuf:"bytes,3,req,name=app_id,json=appId" json:"app_id"`
	OrderId   string `protobuf:"bytes,4,req,name=order_id,json=orderId" json:"order_id"`
	OrderDesc string `protobuf:"bytes,5,req,name=order_desc,json=orderDesc" json:"order_desc"`
	OpUid     uint32 `protobuf:"varint,6,req,name=op_uid,json=opUid" json:"op_uid"`
}

func (m *FreezeUserCurrencyReq) Reset()                    { *m = FreezeUserCurrencyReq{} }
func (m *FreezeUserCurrencyReq) String() string            { return proto.CompactTextString(m) }
func (*FreezeUserCurrencyReq) ProtoMessage()               {}
func (*FreezeUserCurrencyReq) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{19} }

func (m *FreezeUserCurrencyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FreezeUserCurrencyReq) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *FreezeUserCurrencyReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *FreezeUserCurrencyReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *FreezeUserCurrencyReq) GetOrderDesc() string {
	if m != nil {
		return m.OrderDesc
	}
	return ""
}

func (m *FreezeUserCurrencyReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

type FreezeUserCurrencyResp struct {
}

func (m *FreezeUserCurrencyResp) Reset()                    { *m = FreezeUserCurrencyResp{} }
func (m *FreezeUserCurrencyResp) String() string            { return proto.CompactTextString(m) }
func (*FreezeUserCurrencyResp) ProtoMessage()               {}
func (*FreezeUserCurrencyResp) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{20} }

type ConfirmFrozenOrderReq struct {
	Uid     uint32                   `protobuf:"varint,1,req,name=uid" json:"uid"`
	Amount  uint32                   `protobuf:"varint,2,req,name=amount" json:"amount"`
	AppId   string                   `protobuf:"bytes,3,req,name=app_id,json=appId" json:"app_id"`
	OrderId string                   `protobuf:"bytes,4,req,name=order_id,json=orderId" json:"order_id"`
	Op      ConfirmFrozenOrderReq_OP `protobuf:"varint,5,req,name=op,enum=Currency.ConfirmFrozenOrderReq_OP" json:"op"`
}

func (m *ConfirmFrozenOrderReq) Reset()                    { *m = ConfirmFrozenOrderReq{} }
func (m *ConfirmFrozenOrderReq) String() string            { return proto.CompactTextString(m) }
func (*ConfirmFrozenOrderReq) ProtoMessage()               {}
func (*ConfirmFrozenOrderReq) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{21} }

func (m *ConfirmFrozenOrderReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConfirmFrozenOrderReq) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *ConfirmFrozenOrderReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *ConfirmFrozenOrderReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *ConfirmFrozenOrderReq) GetOp() ConfirmFrozenOrderReq_OP {
	if m != nil {
		return m.Op
	}
	return ConfirmFrozenOrderReq_NOP
}

type ConfirmFrozenOrderResp struct {
}

func (m *ConfirmFrozenOrderResp) Reset()                    { *m = ConfirmFrozenOrderResp{} }
func (m *ConfirmFrozenOrderResp) String() string            { return proto.CompactTextString(m) }
func (*ConfirmFrozenOrderResp) ProtoMessage()               {}
func (*ConfirmFrozenOrderResp) Descriptor() ([]byte, []int) { return fileDescriptorCurrency, []int{22} }

func init() {
	proto.RegisterType((*GetUserCurrencyReq)(nil), "Currency.GetUserCurrencyReq")
	proto.RegisterType((*GetUserCurrencyResp)(nil), "Currency.GetUserCurrencyResp")
	proto.RegisterType((*UserCurrency)(nil), "Currency.UserCurrency")
	proto.RegisterType((*BatGetUserCurrencyReq)(nil), "Currency.BatGetUserCurrencyReq")
	proto.RegisterType((*BatGetUserCurrencyResp)(nil), "Currency.BatGetUserCurrencyResp")
	proto.RegisterType((*GetUserCurrencyLogReq)(nil), "Currency.GetUserCurrencyLogReq")
	proto.RegisterType((*UserCurrencyLogInfo)(nil), "Currency.UserCurrencyLogInfo")
	proto.RegisterType((*GetUserCurrencyLogResp)(nil), "Currency.GetUserCurrencyLogResp")
	proto.RegisterType((*AddUserCurrencyReq)(nil), "Currency.AddUserCurrencyReq")
	proto.RegisterType((*AddUserCurrencyResp)(nil), "Currency.AddUserCurrencyResp")
	proto.RegisterType((*HasAddedUserCurrencyReq)(nil), "Currency.HasAddedUserCurrencyReq")
	proto.RegisterType((*HasAddedUserCurrencyResp)(nil), "Currency.HasAddedUserCurrencyResp")
	proto.RegisterType((*GetAppCurrencyReq)(nil), "Currency.GetAppCurrencyReq")
	proto.RegisterType((*GetAppCurrencyResp)(nil), "Currency.GetAppCurrencyResp")
	proto.RegisterType((*AppCurrencyUpdate)(nil), "Currency.AppCurrencyUpdate")
	proto.RegisterType((*UpdateAppCurrencyReq)(nil), "Currency.UpdateAppCurrencyReq")
	proto.RegisterType((*UpdateAppCurrencyResp)(nil), "Currency.UpdateAppCurrencyResp")
	proto.RegisterType((*GetAppCurrencyUpdateLogReq)(nil), "Currency.GetAppCurrencyUpdateLogReq")
	proto.RegisterType((*GetAppCurrencyUpdateLogResp)(nil), "Currency.GetAppCurrencyUpdateLogResp")
	proto.RegisterType((*FreezeUserCurrencyReq)(nil), "Currency.FreezeUserCurrencyReq")
	proto.RegisterType((*FreezeUserCurrencyResp)(nil), "Currency.FreezeUserCurrencyResp")
	proto.RegisterType((*ConfirmFrozenOrderReq)(nil), "Currency.ConfirmFrozenOrderReq")
	proto.RegisterType((*ConfirmFrozenOrderResp)(nil), "Currency.ConfirmFrozenOrderResp")
	proto.RegisterEnum("Currency.ADD_CURRENCY_REASON", ADD_CURRENCY_REASON_name, ADD_CURRENCY_REASON_value)
	proto.RegisterEnum("Currency.AppCurrencyUpdate_OP", AppCurrencyUpdate_OP_name, AppCurrencyUpdate_OP_value)
	proto.RegisterEnum("Currency.ConfirmFrozenOrderReq_OP", ConfirmFrozenOrderReq_OP_name, ConfirmFrozenOrderReq_OP_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Currency service

type CurrencyClient interface {
	GetUserCurrency(ctx context.Context, in *GetUserCurrencyReq, opts ...grpc.CallOption) (*GetUserCurrencyResp, error)
	AddUserCurrency(ctx context.Context, in *AddUserCurrencyReq, opts ...grpc.CallOption) (*AddUserCurrencyResp, error)
	HasAddedUserCurrency(ctx context.Context, in *HasAddedUserCurrencyReq, opts ...grpc.CallOption) (*HasAddedUserCurrencyResp, error)
	BatGetUserCurrency(ctx context.Context, in *BatGetUserCurrencyReq, opts ...grpc.CallOption) (*BatGetUserCurrencyResp, error)
	// 红钻池
	GetAppCurrency(ctx context.Context, in *GetAppCurrencyReq, opts ...grpc.CallOption) (*GetAppCurrencyResp, error)
	UpdateAppCurrency(ctx context.Context, in *UpdateAppCurrencyReq, opts ...grpc.CallOption) (*UpdateAppCurrencyResp, error)
	GetAppCurrencyUpdateLog(ctx context.Context, in *GetAppCurrencyUpdateLogReq, opts ...grpc.CallOption) (*GetAppCurrencyUpdateLogResp, error)
	// 红钻冻结消费
	FreezeUserCurrency(ctx context.Context, in *FreezeUserCurrencyReq, opts ...grpc.CallOption) (*FreezeUserCurrencyResp, error)
	ConfirmFrozenOrder(ctx context.Context, in *ConfirmFrozenOrderReq, opts ...grpc.CallOption) (*ConfirmFrozenOrderResp, error)
	// 红钻记录
	GetUserCurrencyLog(ctx context.Context, in *GetUserCurrencyLogReq, opts ...grpc.CallOption) (*GetUserCurrencyLogResp, error)
}

type currencyClient struct {
	cc *grpc.ClientConn
}

func NewCurrencyClient(cc *grpc.ClientConn) CurrencyClient {
	return &currencyClient{cc}
}

func (c *currencyClient) GetUserCurrency(ctx context.Context, in *GetUserCurrencyReq, opts ...grpc.CallOption) (*GetUserCurrencyResp, error) {
	out := new(GetUserCurrencyResp)
	err := grpc.Invoke(ctx, "/Currency.Currency/GetUserCurrency", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *currencyClient) AddUserCurrency(ctx context.Context, in *AddUserCurrencyReq, opts ...grpc.CallOption) (*AddUserCurrencyResp, error) {
	out := new(AddUserCurrencyResp)
	err := grpc.Invoke(ctx, "/Currency.Currency/AddUserCurrency", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *currencyClient) HasAddedUserCurrency(ctx context.Context, in *HasAddedUserCurrencyReq, opts ...grpc.CallOption) (*HasAddedUserCurrencyResp, error) {
	out := new(HasAddedUserCurrencyResp)
	err := grpc.Invoke(ctx, "/Currency.Currency/HasAddedUserCurrency", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *currencyClient) BatGetUserCurrency(ctx context.Context, in *BatGetUserCurrencyReq, opts ...grpc.CallOption) (*BatGetUserCurrencyResp, error) {
	out := new(BatGetUserCurrencyResp)
	err := grpc.Invoke(ctx, "/Currency.Currency/BatGetUserCurrency", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *currencyClient) GetAppCurrency(ctx context.Context, in *GetAppCurrencyReq, opts ...grpc.CallOption) (*GetAppCurrencyResp, error) {
	out := new(GetAppCurrencyResp)
	err := grpc.Invoke(ctx, "/Currency.Currency/GetAppCurrency", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *currencyClient) UpdateAppCurrency(ctx context.Context, in *UpdateAppCurrencyReq, opts ...grpc.CallOption) (*UpdateAppCurrencyResp, error) {
	out := new(UpdateAppCurrencyResp)
	err := grpc.Invoke(ctx, "/Currency.Currency/UpdateAppCurrency", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *currencyClient) GetAppCurrencyUpdateLog(ctx context.Context, in *GetAppCurrencyUpdateLogReq, opts ...grpc.CallOption) (*GetAppCurrencyUpdateLogResp, error) {
	out := new(GetAppCurrencyUpdateLogResp)
	err := grpc.Invoke(ctx, "/Currency.Currency/GetAppCurrencyUpdateLog", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *currencyClient) FreezeUserCurrency(ctx context.Context, in *FreezeUserCurrencyReq, opts ...grpc.CallOption) (*FreezeUserCurrencyResp, error) {
	out := new(FreezeUserCurrencyResp)
	err := grpc.Invoke(ctx, "/Currency.Currency/FreezeUserCurrency", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *currencyClient) ConfirmFrozenOrder(ctx context.Context, in *ConfirmFrozenOrderReq, opts ...grpc.CallOption) (*ConfirmFrozenOrderResp, error) {
	out := new(ConfirmFrozenOrderResp)
	err := grpc.Invoke(ctx, "/Currency.Currency/ConfirmFrozenOrder", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *currencyClient) GetUserCurrencyLog(ctx context.Context, in *GetUserCurrencyLogReq, opts ...grpc.CallOption) (*GetUserCurrencyLogResp, error) {
	out := new(GetUserCurrencyLogResp)
	err := grpc.Invoke(ctx, "/Currency.Currency/GetUserCurrencyLog", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Currency service

type CurrencyServer interface {
	GetUserCurrency(context.Context, *GetUserCurrencyReq) (*GetUserCurrencyResp, error)
	AddUserCurrency(context.Context, *AddUserCurrencyReq) (*AddUserCurrencyResp, error)
	HasAddedUserCurrency(context.Context, *HasAddedUserCurrencyReq) (*HasAddedUserCurrencyResp, error)
	BatGetUserCurrency(context.Context, *BatGetUserCurrencyReq) (*BatGetUserCurrencyResp, error)
	// 红钻池
	GetAppCurrency(context.Context, *GetAppCurrencyReq) (*GetAppCurrencyResp, error)
	UpdateAppCurrency(context.Context, *UpdateAppCurrencyReq) (*UpdateAppCurrencyResp, error)
	GetAppCurrencyUpdateLog(context.Context, *GetAppCurrencyUpdateLogReq) (*GetAppCurrencyUpdateLogResp, error)
	// 红钻冻结消费
	FreezeUserCurrency(context.Context, *FreezeUserCurrencyReq) (*FreezeUserCurrencyResp, error)
	ConfirmFrozenOrder(context.Context, *ConfirmFrozenOrderReq) (*ConfirmFrozenOrderResp, error)
	// 红钻记录
	GetUserCurrencyLog(context.Context, *GetUserCurrencyLogReq) (*GetUserCurrencyLogResp, error)
}

func RegisterCurrencyServer(s *grpc.Server, srv CurrencyServer) {
	s.RegisterService(&_Currency_serviceDesc, srv)
}

func _Currency_GetUserCurrency_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCurrencyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CurrencyServer).GetUserCurrency(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Currency.Currency/GetUserCurrency",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CurrencyServer).GetUserCurrency(ctx, req.(*GetUserCurrencyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Currency_AddUserCurrency_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserCurrencyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CurrencyServer).AddUserCurrency(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Currency.Currency/AddUserCurrency",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CurrencyServer).AddUserCurrency(ctx, req.(*AddUserCurrencyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Currency_HasAddedUserCurrency_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HasAddedUserCurrencyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CurrencyServer).HasAddedUserCurrency(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Currency.Currency/HasAddedUserCurrency",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CurrencyServer).HasAddedUserCurrency(ctx, req.(*HasAddedUserCurrencyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Currency_BatGetUserCurrency_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetUserCurrencyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CurrencyServer).BatGetUserCurrency(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Currency.Currency/BatGetUserCurrency",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CurrencyServer).BatGetUserCurrency(ctx, req.(*BatGetUserCurrencyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Currency_GetAppCurrency_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppCurrencyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CurrencyServer).GetAppCurrency(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Currency.Currency/GetAppCurrency",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CurrencyServer).GetAppCurrency(ctx, req.(*GetAppCurrencyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Currency_UpdateAppCurrency_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAppCurrencyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CurrencyServer).UpdateAppCurrency(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Currency.Currency/UpdateAppCurrency",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CurrencyServer).UpdateAppCurrency(ctx, req.(*UpdateAppCurrencyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Currency_GetAppCurrencyUpdateLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppCurrencyUpdateLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CurrencyServer).GetAppCurrencyUpdateLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Currency.Currency/GetAppCurrencyUpdateLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CurrencyServer).GetAppCurrencyUpdateLog(ctx, req.(*GetAppCurrencyUpdateLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Currency_FreezeUserCurrency_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreezeUserCurrencyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CurrencyServer).FreezeUserCurrency(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Currency.Currency/FreezeUserCurrency",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CurrencyServer).FreezeUserCurrency(ctx, req.(*FreezeUserCurrencyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Currency_ConfirmFrozenOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmFrozenOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CurrencyServer).ConfirmFrozenOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Currency.Currency/ConfirmFrozenOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CurrencyServer).ConfirmFrozenOrder(ctx, req.(*ConfirmFrozenOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Currency_GetUserCurrencyLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCurrencyLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CurrencyServer).GetUserCurrencyLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Currency.Currency/GetUserCurrencyLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CurrencyServer).GetUserCurrencyLog(ctx, req.(*GetUserCurrencyLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Currency_serviceDesc = grpc.ServiceDesc{
	ServiceName: "Currency.Currency",
	HandlerType: (*CurrencyServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserCurrency",
			Handler:    _Currency_GetUserCurrency_Handler,
		},
		{
			MethodName: "AddUserCurrency",
			Handler:    _Currency_AddUserCurrency_Handler,
		},
		{
			MethodName: "HasAddedUserCurrency",
			Handler:    _Currency_HasAddedUserCurrency_Handler,
		},
		{
			MethodName: "BatGetUserCurrency",
			Handler:    _Currency_BatGetUserCurrency_Handler,
		},
		{
			MethodName: "GetAppCurrency",
			Handler:    _Currency_GetAppCurrency_Handler,
		},
		{
			MethodName: "UpdateAppCurrency",
			Handler:    _Currency_UpdateAppCurrency_Handler,
		},
		{
			MethodName: "GetAppCurrencyUpdateLog",
			Handler:    _Currency_GetAppCurrencyUpdateLog_Handler,
		},
		{
			MethodName: "FreezeUserCurrency",
			Handler:    _Currency_FreezeUserCurrency_Handler,
		},
		{
			MethodName: "ConfirmFrozenOrder",
			Handler:    _Currency_ConfirmFrozenOrder_Handler,
		},
		{
			MethodName: "GetUserCurrencyLog",
			Handler:    _Currency_GetUserCurrencyLog_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/currencysvr/currency.proto",
}

func (m *GetUserCurrencyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserCurrencyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetUserCurrencyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserCurrencyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Currency))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.ValuableCurrency))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.ValuelessCurrency))
	return i, nil
}

func (m *UserCurrency) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserCurrency) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Currency))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.ValuableCurrency))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.ValuelessCurrency))
	return i, nil
}

func (m *BatGetUserCurrencyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetUserCurrencyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintCurrency(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatGetUserCurrencyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetUserCurrencyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UserCurrencyList) > 0 {
		for _, msg := range m.UserCurrencyList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintCurrency(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserCurrencyLogReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserCurrencyLogReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x22
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(len(m.DescLike)))
	i += copy(dAtA[i:], m.DescLike)
	return i, nil
}

func (m *UserCurrencyLogInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserCurrencyLogInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.CValue))
	dAtA[i] = 0x12
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(len(m.OpTime)))
	i += copy(dAtA[i:], m.OpTime)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(len(m.Desc)))
	i += copy(dAtA[i:], m.Desc)
	return i, nil
}

func (m *GetUserCurrencyLogResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserCurrencyLogResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LogInfos) > 0 {
		for _, msg := range m.LogInfos {
			dAtA[i] = 0xa
			i++
			i = encodeVarintCurrency(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddUserCurrencyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserCurrencyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Currency))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(len(m.MissionKey)))
	i += copy(dAtA[i:], m.MissionKey)
	dAtA[i] = 0x22
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(len(m.MissionDesc)))
	i += copy(dAtA[i:], m.MissionDesc)
	dAtA[i] = 0x28
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.OpUid))
	dAtA[i] = 0x30
	i++
	if m.WithStock {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x38
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Appid))
	dAtA[i] = 0x40
	i++
	if m.IsValuable {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x4a
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x50
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Reason))
	return i, nil
}

func (m *AddUserCurrencyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserCurrencyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *HasAddedUserCurrencyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HasAddedUserCurrencyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(len(m.MissionKey)))
	i += copy(dAtA[i:], m.MissionKey)
	return i, nil
}

func (m *HasAddedUserCurrencyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HasAddedUserCurrencyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.Added {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetAppCurrencyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAppCurrencyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Appid))
	return i, nil
}

func (m *GetAppCurrencyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAppCurrencyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Amount))
	return i, nil
}

func (m *AppCurrencyUpdate) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AppCurrencyUpdate) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Appid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Op))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Amount))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.OpUid))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(len(m.OpDesc)))
	i += copy(dAtA[i:], m.OpDesc)
	dAtA[i] = 0x30
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.OpAt))
	return i, nil
}

func (m *UpdateAppCurrencyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateAppCurrencyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Update == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("update")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCurrency(dAtA, i, uint64(m.Update.Size()))
		n1, err := m.Update.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *UpdateAppCurrencyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateAppCurrencyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetAppCurrencyUpdateLogReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAppCurrencyUpdateLogReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Appid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.EndTime))
	return i, nil
}

func (m *GetAppCurrencyUpdateLogResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAppCurrencyUpdateLogResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UpdateList) > 0 {
		for _, msg := range m.UpdateList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintCurrency(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *FreezeUserCurrencyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FreezeUserCurrencyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Amount))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(len(m.AppId)))
	i += copy(dAtA[i:], m.AppId)
	dAtA[i] = 0x22
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(len(m.OrderDesc)))
	i += copy(dAtA[i:], m.OrderDesc)
	dAtA[i] = 0x30
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.OpUid))
	return i, nil
}

func (m *FreezeUserCurrencyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FreezeUserCurrencyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *ConfirmFrozenOrderReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConfirmFrozenOrderReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Amount))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(len(m.AppId)))
	i += copy(dAtA[i:], m.AppId)
	dAtA[i] = 0x22
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x28
	i++
	i = encodeVarintCurrency(dAtA, i, uint64(m.Op))
	return i, nil
}

func (m *ConfirmFrozenOrderResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConfirmFrozenOrderResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Currency(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Currency(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintCurrency(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GetUserCurrencyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCurrency(uint64(m.Uid))
	return n
}

func (m *GetUserCurrencyResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCurrency(uint64(m.Currency))
	n += 1 + sovCurrency(uint64(m.ValuableCurrency))
	n += 1 + sovCurrency(uint64(m.ValuelessCurrency))
	return n
}

func (m *UserCurrency) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCurrency(uint64(m.Uid))
	n += 1 + sovCurrency(uint64(m.Currency))
	n += 1 + sovCurrency(uint64(m.ValuableCurrency))
	n += 1 + sovCurrency(uint64(m.ValuelessCurrency))
	return n
}

func (m *BatGetUserCurrencyReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovCurrency(uint64(e))
		}
	}
	return n
}

func (m *BatGetUserCurrencyResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UserCurrencyList) > 0 {
		for _, e := range m.UserCurrencyList {
			l = e.Size()
			n += 1 + l + sovCurrency(uint64(l))
		}
	}
	return n
}

func (m *GetUserCurrencyLogReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCurrency(uint64(m.Uid))
	n += 1 + sovCurrency(uint64(m.BeginTime))
	n += 1 + sovCurrency(uint64(m.EndTime))
	l = len(m.DescLike)
	n += 1 + l + sovCurrency(uint64(l))
	return n
}

func (m *UserCurrencyLogInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCurrency(uint64(m.CValue))
	l = len(m.OpTime)
	n += 1 + l + sovCurrency(uint64(l))
	l = len(m.Desc)
	n += 1 + l + sovCurrency(uint64(l))
	return n
}

func (m *GetUserCurrencyLogResp) Size() (n int) {
	var l int
	_ = l
	if len(m.LogInfos) > 0 {
		for _, e := range m.LogInfos {
			l = e.Size()
			n += 1 + l + sovCurrency(uint64(l))
		}
	}
	return n
}

func (m *AddUserCurrencyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCurrency(uint64(m.Uid))
	n += 1 + sovCurrency(uint64(m.Currency))
	l = len(m.MissionKey)
	n += 1 + l + sovCurrency(uint64(l))
	l = len(m.MissionDesc)
	n += 1 + l + sovCurrency(uint64(l))
	n += 1 + sovCurrency(uint64(m.OpUid))
	n += 2
	n += 1 + sovCurrency(uint64(m.Appid))
	n += 2
	l = len(m.OrderId)
	n += 1 + l + sovCurrency(uint64(l))
	n += 1 + sovCurrency(uint64(m.Reason))
	return n
}

func (m *AddUserCurrencyResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *HasAddedUserCurrencyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCurrency(uint64(m.Uid))
	l = len(m.MissionKey)
	n += 1 + l + sovCurrency(uint64(l))
	return n
}

func (m *HasAddedUserCurrencyResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *GetAppCurrencyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCurrency(uint64(m.Appid))
	return n
}

func (m *GetAppCurrencyResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCurrency(uint64(m.Amount))
	return n
}

func (m *AppCurrencyUpdate) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCurrency(uint64(m.Appid))
	n += 1 + sovCurrency(uint64(m.Op))
	n += 1 + sovCurrency(uint64(m.Amount))
	n += 1 + sovCurrency(uint64(m.OpUid))
	l = len(m.OpDesc)
	n += 1 + l + sovCurrency(uint64(l))
	n += 1 + sovCurrency(uint64(m.OpAt))
	return n
}

func (m *UpdateAppCurrencyReq) Size() (n int) {
	var l int
	_ = l
	if m.Update != nil {
		l = m.Update.Size()
		n += 1 + l + sovCurrency(uint64(l))
	}
	return n
}

func (m *UpdateAppCurrencyResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetAppCurrencyUpdateLogReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCurrency(uint64(m.Appid))
	n += 1 + sovCurrency(uint64(m.BeginTime))
	n += 1 + sovCurrency(uint64(m.EndTime))
	return n
}

func (m *GetAppCurrencyUpdateLogResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UpdateList) > 0 {
		for _, e := range m.UpdateList {
			l = e.Size()
			n += 1 + l + sovCurrency(uint64(l))
		}
	}
	return n
}

func (m *FreezeUserCurrencyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCurrency(uint64(m.Uid))
	n += 1 + sovCurrency(uint64(m.Amount))
	l = len(m.AppId)
	n += 1 + l + sovCurrency(uint64(l))
	l = len(m.OrderId)
	n += 1 + l + sovCurrency(uint64(l))
	l = len(m.OrderDesc)
	n += 1 + l + sovCurrency(uint64(l))
	n += 1 + sovCurrency(uint64(m.OpUid))
	return n
}

func (m *FreezeUserCurrencyResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *ConfirmFrozenOrderReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCurrency(uint64(m.Uid))
	n += 1 + sovCurrency(uint64(m.Amount))
	l = len(m.AppId)
	n += 1 + l + sovCurrency(uint64(l))
	l = len(m.OrderId)
	n += 1 + l + sovCurrency(uint64(l))
	n += 1 + sovCurrency(uint64(m.Op))
	return n
}

func (m *ConfirmFrozenOrderResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovCurrency(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozCurrency(x uint64) (n int) {
	return sovCurrency(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *GetUserCurrencyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserCurrencyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserCurrencyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserCurrencyResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserCurrencyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserCurrencyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Currency", wireType)
			}
			m.Currency = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Currency |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValuableCurrency", wireType)
			}
			m.ValuableCurrency = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ValuableCurrency |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValuelessCurrency", wireType)
			}
			m.ValuelessCurrency = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ValuelessCurrency |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("currency")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserCurrency) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserCurrency: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserCurrency: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Currency", wireType)
			}
			m.Currency = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Currency |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValuableCurrency", wireType)
			}
			m.ValuableCurrency = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ValuableCurrency |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValuelessCurrency", wireType)
			}
			m.ValuelessCurrency = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ValuelessCurrency |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("currency")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetUserCurrencyReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetUserCurrencyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetUserCurrencyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCurrency
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCurrency
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthCurrency
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCurrency
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetUserCurrencyResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetUserCurrencyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetUserCurrencyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserCurrencyList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCurrency
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserCurrencyList = append(m.UserCurrencyList, &UserCurrency{})
			if err := m.UserCurrencyList[len(m.UserCurrencyList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserCurrencyLogReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserCurrencyLogReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserCurrencyLogReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DescLike", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DescLike = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserCurrencyLogInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserCurrencyLogInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserCurrencyLogInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CValue", wireType)
			}
			m.CValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Desc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("c_value")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("desc")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserCurrencyLogResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserCurrencyLogResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserCurrencyLogResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LogInfos", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCurrency
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LogInfos = append(m.LogInfos, &UserCurrencyLogInfo{})
			if err := m.LogInfos[len(m.LogInfos)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserCurrencyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserCurrencyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserCurrencyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Currency", wireType)
			}
			m.Currency = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Currency |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MissionKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MissionDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WithStock", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithStock = bool(v != 0)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			m.Appid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Appid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsValuable", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsValuable = bool(v != 0)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			m.Reason = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Reason |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("currency")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mission_key")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mission_desc")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserCurrencyResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserCurrencyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserCurrencyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HasAddedUserCurrencyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HasAddedUserCurrencyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HasAddedUserCurrencyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MissionKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mission_key")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HasAddedUserCurrencyResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HasAddedUserCurrencyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HasAddedUserCurrencyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Added", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Added = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("added")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAppCurrencyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAppCurrencyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAppCurrencyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			m.Appid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Appid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("appid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAppCurrencyResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAppCurrencyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAppCurrencyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AppCurrencyUpdate) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AppCurrencyUpdate: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AppCurrencyUpdate: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			m.Appid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Appid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Op", wireType)
			}
			m.Op = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Op |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpAt", wireType)
			}
			m.OpAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("appid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("amount")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateAppCurrencyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateAppCurrencyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateAppCurrencyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Update", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCurrency
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Update == nil {
				m.Update = &AppCurrencyUpdate{}
			}
			if err := m.Update.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("update")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateAppCurrencyResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateAppCurrencyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateAppCurrencyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAppCurrencyUpdateLogReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAppCurrencyUpdateLogReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAppCurrencyUpdateLogReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			m.Appid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Appid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("appid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAppCurrencyUpdateLogResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAppCurrencyUpdateLogResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAppCurrencyUpdateLogResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCurrency
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UpdateList = append(m.UpdateList, &AppCurrencyUpdate{})
			if err := m.UpdateList[len(m.UpdateList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FreezeUserCurrencyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FreezeUserCurrencyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FreezeUserCurrencyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("amount")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("app_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_desc")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FreezeUserCurrencyResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FreezeUserCurrencyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FreezeUserCurrencyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConfirmFrozenOrderReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConfirmFrozenOrderReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConfirmFrozenOrderReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Op", wireType)
			}
			m.Op = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Op |= (ConfirmFrozenOrderReq_OP(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("amount")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("app_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConfirmFrozenOrderResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConfirmFrozenOrderResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConfirmFrozenOrderResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipCurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipCurrency(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowCurrency
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCurrency
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthCurrency
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowCurrency
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipCurrency(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthCurrency = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowCurrency   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/currencysvr/currency.proto", fileDescriptorCurrency) }

var fileDescriptorCurrency = []byte{
	// 1708 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x58, 0xcb, 0x6f, 0xdb, 0xc8,
	0x19, 0x0f, 0x25, 0x59, 0x8f, 0xcf, 0xf6, 0x2e, 0x3d, 0x8e, 0x1d, 0xad, 0x9c, 0xd8, 0x5c, 0x66,
	0x83, 0xb8, 0x85, 0x9d, 0x60, 0x1d, 0xb4, 0x5d, 0x10, 0x8a, 0x00, 0x5a, 0xd2, 0x3a, 0x84, 0x65,
	0xca, 0xd0, 0x23, 0xa9, 0x6b, 0xa4, 0x2c, 0x2d, 0x4e, 0xb2, 0xac, 0x64, 0x71, 0xca, 0x21, 0xb3,
	0xc8, 0xa2, 0x2d, 0x7a, 0x2c, 0x7a, 0x5a, 0xf4, 0xd2, 0xa2, 0x97, 0xbd, 0xa4, 0x87, 0xde, 0x7a,
	0xea, 0xdf, 0xb0, 0x87, 0x16, 0x28, 0xd0, 0x7b, 0x51, 0xa4, 0x3d, 0xe4, 0xd4, 0xbf, 0xa1, 0x98,
	0xa1, 0x28, 0x8d, 0x24, 0x5a, 0x35, 0x16, 0x05, 0x7a, 0xe3, 0x7c, 0x8f, 0xf9, 0x7e, 0xdf, 0x7b,
	0x24, 0xd8, 0xa6, 0x7e, 0xef, 0x61, 0x2f, 0xf4, 0x7d, 0x3c, 0xec, 0xbd, 0xa6, 0xaf, 0xfc, 0xf1,
	0xf7, 0x03, 0xe2, 0x7b, 0x81, 0x87, 0xf2, 0xd5, 0xd1, 0xb9, 0xf4, 0x51, 0xcf, 0xbb, 0xbc, 0xf4,
	0x86, 0x0f, 0x83, 0xc1, 0x2b, 0xe2, 0xf6, 0xfa, 0x03, 0xfc, 0x90, 0xf6, 0x2f, 0x42, 0x77, 0x10,
	0xb8, 0xc3, 0xe0, 0x35, 0xc1, 0x91, 0xbc, 0xba, 0x07, 0xe8, 0x08, 0x07, 0x5d, 0x8a, 0xfd, 0x58,
	0xb1, 0x85, 0x7f, 0x82, 0x36, 0x21, 0x1d, 0xba, 0x4e, 0x51, 0x52, 0x52, 0xbb, 0xab, 0x87, 0x99,
	0xaf, 0xff, 0xbe, 0x73, 0xa3, 0xc5, 0x08, 0xea, 0x57, 0x12, 0xac, 0xcf, 0x89, 0x53, 0x82, 0x14,
	0xc8, 0xc7, 0x38, 0xb8, 0xd2, 0xd2, 0x48, 0x69, 0x4c, 0x45, 0x1f, 0xc3, 0xda, 0x2b, 0x7b, 0x10,
	0xda, 0x17, 0x03, 0x6c, 0x8d, 0x45, 0x53, 0x8a, 0x34, 0x16, 0x95, 0x63, 0x76, 0x7c, 0x31, 0x7a,
	0x04, 0x88, 0xd1, 0xf0, 0x00, 0x53, 0x3a, 0xd1, 0x49, 0x0b, 0x3a, 0x6b, 0x63, 0x7e, 0xac, 0xa4,
	0xfe, 0x51, 0x82, 0x15, 0x11, 0xde, 0x55, 0xae, 0x4c, 0x41, 0x4e, 0x5d, 0x1f, 0x72, 0xfa, 0x1b,
	0x40, 0xce, 0x2c, 0x86, 0x7c, 0x00, 0x1b, 0x87, 0x76, 0x90, 0x90, 0x85, 0x0f, 0x20, 0x1f, 0xba,
	0x8e, 0x35, 0x70, 0x69, 0x50, 0x94, 0x94, 0xf4, 0xee, 0x6a, 0x2b, 0x17, 0xba, 0x4e, 0xc3, 0xa5,
	0x81, 0xfa, 0x43, 0xd8, 0x4c, 0xd2, 0xa1, 0x04, 0xd5, 0x00, 0x85, 0x14, 0xfb, 0x63, 0xeb, 0x13,
	0xf5, 0xe5, 0x83, 0xcd, 0x07, 0xb1, 0xe8, 0x83, 0x29, 0x3d, 0x39, 0x14, 0x4e, 0xfc, 0xfe, 0xdf,
	0x4a, 0xb0, 0x31, 0x73, 0x7b, 0xc3, 0x7b, 0xb9, 0xa0, 0x34, 0xd0, 0x5d, 0x80, 0x0b, 0xfc, 0xd2,
	0x1d, 0x5a, 0x81, 0x7b, 0x89, 0x79, 0x66, 0x63, 0x76, 0x81, 0xd3, 0x3b, 0xee, 0x25, 0x46, 0x3b,
	0x90, 0xc7, 0x43, 0x27, 0x12, 0x49, 0x0b, 0x22, 0x39, 0x3c, 0x74, 0xb8, 0xc0, 0x87, 0x50, 0x70,
	0x30, 0xed, 0x59, 0x03, 0xb7, 0x8f, 0x79, 0xdc, 0x0a, 0x71, 0x5a, 0x18, 0xb9, 0xe1, 0xf6, 0xb1,
	0x7a, 0x09, 0xeb, 0x33, 0xb0, 0x8c, 0xe1, 0x0b, 0x0f, 0xdd, 0x81, 0x5c, 0xcf, 0xe2, 0xc1, 0x9d,
	0xc2, 0x96, 0xed, 0x3d, 0x65, 0x34, 0xc6, 0xf6, 0x48, 0x8c, 0x2d, 0x35, 0xbe, 0x36, 0xeb, 0x11,
	0x6e, 0xb7, 0x08, 0x19, 0x66, 0xa0, 0x98, 0x16, 0x78, 0x9c, 0xa2, 0x76, 0x60, 0x33, 0x29, 0x10,
	0x94, 0x20, 0x0d, 0x0a, 0x03, 0xef, 0xa5, 0xe5, 0x0e, 0x5f, 0x78, 0x74, 0x14, 0xe0, 0x3b, 0xc9,
	0x01, 0x1e, 0x61, 0x6c, 0xe5, 0x07, 0xd1, 0x07, 0x55, 0xdf, 0xa6, 0x00, 0xe9, 0x8e, 0x73, 0xcd,
	0xbe, 0xbb, 0x46, 0xb1, 0xde, 0x83, 0xe5, 0x4b, 0x97, 0x52, 0xd7, 0x1b, 0x5a, 0x7d, 0xfc, 0x7a,
	0xca, 0x0f, 0x18, 0x31, 0x8e, 0xf1, 0x6b, 0x74, 0x1f, 0x56, 0x62, 0x31, 0xee, 0x6f, 0x46, 0x90,
	0x8b, 0x2f, 0xa8, 0x61, 0xda, 0x43, 0x5b, 0x90, 0xf5, 0x88, 0xc5, 0xc0, 0x2c, 0x09, 0x60, 0x96,
	0x3c, 0xd2, 0x8d, 0x72, 0xfd, 0xb9, 0x1b, 0x7c, 0x66, 0xd1, 0xc0, 0xeb, 0xf5, 0x8b, 0x59, 0x45,
	0xda, 0xcd, 0xc7, 0xb9, 0x66, 0xf4, 0x36, 0x23, 0xa3, 0x12, 0x2c, 0xd9, 0x84, 0xb8, 0x4e, 0x31,
	0x27, 0x24, 0x3a, 0x22, 0x31, 0xb4, 0x2e, 0xb5, 0xe2, 0xf6, 0x29, 0xe6, 0x85, 0x1b, 0xc0, 0xa5,
	0x4f, 0x47, 0x74, 0x56, 0x2e, 0x9e, 0xef, 0x60, 0xdf, 0x72, 0x9d, 0x62, 0x41, 0x28, 0x86, 0x1c,
	0xa7, 0x1a, 0x0e, 0xba, 0x0d, 0x59, 0x1f, 0xdb, 0xd4, 0x1b, 0x16, 0x41, 0x30, 0x32, 0xa2, 0xa9,
	0x1b, 0xb0, 0x3e, 0x17, 0x63, 0x4a, 0xd4, 0xef, 0xc3, 0xad, 0x27, 0x36, 0xd5, 0x1d, 0x07, 0x5f,
	0x3b, 0xfe, 0x33, 0xd1, 0x4d, 0x25, 0x47, 0x57, 0xfd, 0x2e, 0x14, 0x93, 0x6f, 0xa6, 0x84, 0x87,
	0x83, 0x31, 0xf8, 0xe5, 0xf9, 0x71, 0x38, 0x18, 0x49, 0x7d, 0x08, 0x6b, 0x47, 0x38, 0xd0, 0x09,
	0x11, 0xb1, 0x8c, 0xe3, 0x27, 0xa2, 0x89, 0x48, 0xea, 0x01, 0x9f, 0xda, 0x53, 0x0a, 0x94, 0xb0,
	0x68, 0xd8, 0x97, 0x5e, 0x38, 0x64, 0xed, 0x2e, 0xed, 0x66, 0xe2, 0x68, 0x44, 0x34, 0xf5, 0x5f,
	0x12, 0xac, 0x09, 0x1a, 0x5d, 0xe2, 0xd8, 0x01, 0x5e, 0x64, 0x05, 0xdd, 0x84, 0x94, 0x47, 0xb8,
	0xb3, 0x31, 0x23, 0xe5, 0x89, 0x56, 0x58, 0x91, 0xcd, 0x58, 0x11, 0xea, 0x26, 0x23, 0xa6, 0x3d,
	0xaa, 0x9b, 0xa8, 0x09, 0x79, 0xe1, 0x2d, 0x09, 0xe9, 0xcc, 0x7a, 0x84, 0xd7, 0xdc, 0x07, 0xb0,
	0xe4, 0x11, 0xcb, 0x0e, 0x78, 0x45, 0xc5, 0xaa, 0x19, 0x8f, 0xe8, 0x81, 0xba, 0x0b, 0xa9, 0xe6,
	0x29, 0xca, 0x41, 0xda, 0x6c, 0x9e, 0xca, 0x37, 0xd8, 0x47, 0xbb, 0xde, 0x91, 0x25, 0xf6, 0xa1,
	0xd7, 0x6a, 0x72, 0x8a, 0x53, 0xba, 0x87, 0x72, 0x5a, 0x3d, 0x86, 0x9b, 0x91, 0x6b, 0x33, 0xe1,
	0x7c, 0x04, 0xd9, 0x90, 0xd3, 0xb9, 0xa7, 0xcb, 0x07, 0x5b, 0x93, 0x56, 0x9d, 0x8b, 0x4a, 0x6b,
	0x24, 0xaa, 0xde, 0x82, 0x8d, 0x84, 0xcb, 0x28, 0x51, 0x7f, 0x0e, 0xa5, 0xe9, 0x04, 0x44, 0x62,
	0xa3, 0x19, 0xb9, 0x28, 0xa8, 0xff, 0x93, 0x39, 0xa9, 0x9e, 0xc3, 0xd6, 0x95, 0xf6, 0x29, 0x41,
	0x65, 0x58, 0x8e, 0x3c, 0x10, 0xa7, 0xff, 0x42, 0x8f, 0x21, 0x92, 0xe7, 0xc3, 0xff, 0x2f, 0x12,
	0x6c, 0x7c, 0xea, 0x63, 0xfc, 0x05, 0xbe, 0x6e, 0x7f, 0x4c, 0x6a, 0x42, 0xac, 0x16, 0xa1, 0x26,
	0x6c, 0x42, 0x58, 0x13, 0x8b, 0x63, 0x89, 0xc5, 0xc3, 0x70, 0xa6, 0x7a, 0x5c, 0x9c, 0x46, 0xe3,
	0x1e, 0xbf, 0x0b, 0x10, 0x09, 0x8c, 0xea, 0x66, 0x22, 0x52, 0xe0, 0xf4, 0x99, 0x71, 0x95, 0x9d,
	0x1b, 0x57, 0x6a, 0x11, 0x36, 0x93, 0xdc, 0xa1, 0x44, 0xfd, 0xb7, 0x04, 0x1b, 0x55, 0x6f, 0xf8,
	0xc2, 0xf5, 0x2f, 0x3f, 0xf5, 0xbd, 0x2f, 0xf0, 0xb0, 0xc9, 0x6e, 0xfc, 0x7f, 0x79, 0xfa, 0x09,
	0xef, 0x37, 0xe6, 0xe1, 0x7b, 0x07, 0xea, 0x24, 0x59, 0x89, 0x00, 0x1f, 0x34, 0x4f, 0x27, 0x3d,
	0xa9, 0xde, 0x9f, 0x6e, 0x0f, 0x80, 0x6c, 0xb5, 0x79, 0x72, 0x62, 0xb0, 0x0e, 0x59, 0x81, 0x7c,
	0xab, 0xd9, 0x68, 0x1c, 0xea, 0xd5, 0x63, 0x39, 0xc5, 0x42, 0x91, 0x74, 0x1d, 0x25, 0xdf, 0xfe,
	0x2a, 0x05, 0xeb, 0x7a, 0xad, 0x66, 0x55, 0xbb, 0xad, 0x56, 0xdd, 0xac, 0x9e, 0x59, 0xad, 0xba,
	0xde, 0x6e, 0x9a, 0x68, 0x19, 0x72, 0x5d, 0xf3, 0xd8, 0x6c, 0x3e, 0x33, 0xe5, 0x1b, 0x48, 0x86,
	0x95, 0x46, 0xf3, 0xc8, 0x30, 0xad, 0x56, 0xfd, 0x99, 0xde, 0xaa, 0xc9, 0x12, 0xa3, 0x74, 0xdb,
	0xf5, 0x96, 0x75, 0x62, 0xb4, 0xdb, 0x46, 0xd3, 0x94, 0x53, 0x8c, 0xd2, 0xae, 0x9b, 0x35, 0xeb,
	0xb4, 0x55, 0x6f, 0xd7, 0xcd, 0x8e, 0x9c, 0x46, 0x45, 0xb8, 0x69, 0x98, 0x9d, 0x7a, 0x4b, 0xaf,
	0x76, 0x8c, 0xa6, 0x69, 0x19, 0x66, 0xc7, 0x38, 0xd1, 0xab, 0x67, 0x72, 0x06, 0x6d, 0x02, 0xe2,
	0xda, 0x1d, 0xfd, 0xc8, 0x3a, 0xd2, 0x4f, 0xea, 0x56, 0x95, 0xdd, 0xba, 0x84, 0xd6, 0x60, 0xb5,
	0xd3, 0xb1, 0x7e, 0xf0, 0xa4, 0xab, 0x9b, 0x47, 0x67, 0x5d, 0xdd, 0x94, 0xb3, 0x68, 0x1d, 0xde,
	0xaf, 0x3e, 0xd1, 0x4d, 0xb3, 0xde, 0xb0, 0x1a, 0xcd, 0x4e, 0xa7, 0xde, 0x3a, 0x93, 0x73, 0xe8,
	0x5b, 0x70, 0x2f, 0x26, 0x1a, 0xe6, 0x53, 0xa3, 0x53, 0xb7, 0xcc, 0xa6, 0x69, 0xd5, 0x99, 0x31,
	0x2b, 0x66, 0x30, 0x03, 0x72, 0x1e, 0xdd, 0x87, 0xbb, 0xed, 0x66, 0xd5, 0xd0, 0x1b, 0x16, 0x0b,
	0x4d, 0xd7, 0x34, 0x3a, 0x67, 0x56, 0xc3, 0x38, 0xae, 0x5b, 0x0c, 0xd5, 0x53, 0x76, 0x3a, 0xed,
	0xb6, 0x9f, 0xc8, 0x05, 0x74, 0x0b, 0xd6, 0x0f, 0xbb, 0x67, 0x63, 0xf5, 0x67, 0xf5, 0x5a, 0xcd,
	0x30, 0x8f, 0x64, 0x38, 0xf8, 0xdd, 0x0a, 0x8c, 0x5f, 0xd7, 0xe8, 0xc7, 0xf0, 0xfe, 0xcc, 0xb3,
	0x00, 0xdd, 0x9e, 0xa4, 0x6c, 0xfe, 0x31, 0x57, 0xba, 0xb3, 0x80, 0x4b, 0x89, 0x5a, 0xfa, 0xc5,
	0x9b, 0x77, 0x69, 0xe9, 0x57, 0x6f, 0xde, 0xa5, 0x53, 0xa1, 0xf6, 0xeb, 0x37, 0xef, 0xd2, 0x85,
	0xfd, 0x50, 0x29, 0x87, 0xae, 0x53, 0x29, 0xa0, 0x3f, 0x4b, 0xf0, 0xfe, 0xcc, 0x22, 0x13, 0x8d,
	0xcd, 0xbf, 0x23, 0x44, 0x63, 0x49, 0x1b, 0xf0, 0xa7, 0xcc, 0x58, 0x8a, 0x19, 0x5b, 0x0d, 0x35,
	0x5f, 0xeb, 0x6b, 0x8e, 0xe6, 0x69, 0xb6, 0x46, 0x99, 0xdd, 0x1f, 0xc5, 0x76, 0x95, 0x7d, 0x5f,
	0x29, 0xc7, 0xef, 0x8b, 0x8a, 0xb2, 0xdf, 0x57, 0xca, 0xbc, 0x50, 0x15, 0xce, 0x73, 0xe2, 0x13,
	0xeb, 0xca, 0x8a, 0x72, 0xbe, 0xef, 0x29, 0xe5, 0xa8, 0x03, 0x2b, 0xcf, 0x95, 0xf3, 0x7d, 0x5b,
	0x29, 0xf3, 0x71, 0xc7, 0x0f, 0x74, 0x4f, 0x99, 0x3c, 0x17, 0x9e, 0xa3, 0xdf, 0x48, 0x70, 0x33,
	0x69, 0x4d, 0xa2, 0x0f, 0x27, 0xa8, 0xaf, 0x58, 0xd0, 0x25, 0xf5, 0xbf, 0x89, 0x50, 0xa2, 0x7e,
	0xc2, 0xbc, 0x4b, 0x33, 0xef, 0xb2, 0xa1, 0xd6, 0xd7, 0x6c, 0x1e, 0xce, 0xbb, 0x13, 0xb7, 0xa6,
	0x1c, 0x11, 0xa1, 0xa2, 0xcf, 0x01, 0xcd, 0xbf, 0xaa, 0xd1, 0xce, 0xc4, 0x66, 0xe2, 0x3b, 0xbd,
	0xa4, 0x2c, 0x16, 0xa0, 0x44, 0xdd, 0x66, 0x90, 0x32, 0x42, 0x76, 0x57, 0x47, 0x70, 0x14, 0x36,
	0x9f, 0x2b, 0xe8, 0x33, 0x78, 0x6f, 0x7a, 0x9c, 0xa3, 0xad, 0xa9, 0x72, 0x99, 0xde, 0x65, 0xa5,
	0xdb, 0x57, 0x33, 0x29, 0x51, 0xb7, 0x98, 0xb1, 0x2c, 0x37, 0x16, 0xf9, 0x0e, 0x13, 0x1f, 0xd1,
	0xdf, 0x24, 0x58, 0x9b, 0x5b, 0x69, 0x68, 0x5b, 0x78, 0xb7, 0x26, 0x2c, 0xcf, 0xd2, 0xce, 0x42,
	0x3e, 0x25, 0xea, 0xcf, 0x98, 0xcd, 0x1c, 0xb3, 0xb9, 0x62, 0x6b, 0x9e, 0xe6, 0x6b, 0xa1, 0xe6,
	0x68, 0x01, 0xb7, 0x7e, 0x31, 0xb1, 0xae, 0x44, 0x55, 0x82, 0x7d, 0x3b, 0xf0, 0x7c, 0xed, 0xe3,
	0xc7, 0x14, 0x07, 0x7b, 0x07, 0x8f, 0x6d, 0xc7, 0xd9, 0x7b, 0xf4, 0x98, 0x86, 0x17, 0xb3, 0x25,
	0x77, 0xce, 0x82, 0xe4, 0x11, 0x25, 0x2e, 0x2a, 0x87, 0x9f, 0x78, 0xc1, 0xb1, 0x63, 0xc0, 0x8f,
	0x76, 0x50, 0x79, 0x8e, 0xfe, 0x20, 0xc1, 0xad, 0x2b, 0xf6, 0x21, 0xfa, 0xe8, 0xaa, 0x60, 0x89,
	0x2b, 0xbb, 0x74, 0xef, 0x1a, 0x52, 0x94, 0xa8, 0x3a, 0xf3, 0x33, 0xcf, 0x6b, 0xcb, 0xd6, 0x2e,
	0x34, 0xcc, 0x3d, 0xdc, 0x13, 0x3c, 0x3c, 0xdf, 0xbf, 0x50, 0xca, 0x7c, 0x91, 0x2b, 0x6c, 0x7b,
	0x73, 0xa0, 0x58, 0x29, 0xe3, 0xa1, 0x33, 0x3a, 0xa3, 0x2f, 0x25, 0x40, 0xf3, 0xeb, 0x48, 0xac,
	0xb2, 0xc4, 0xdd, 0x2b, 0x56, 0xd9, 0x15, 0xdb, 0xec, 0x7b, 0x0c, 0x1c, 0x8c, 0x0a, 0x9f, 0xa5,
	0x81, 0x81, 0x53, 0x27, 0x85, 0x1f, 0xa1, 0xb4, 0xc6, 0x89, 0x18, 0x2d, 0xa3, 0x0a, 0x87, 0x34,
	0xbf, 0x16, 0x44, 0x48, 0x89, 0x3b, 0x48, 0x84, 0x94, 0xbc, 0x55, 0x22, 0x48, 0xcb, 0xdf, 0x00,
	0xd2, 0xef, 0xa5, 0xb9, 0x3f, 0x26, 0x58, 0x32, 0x77, 0xae, 0x9c, 0xa2, 0xa3, 0x3c, 0x2a, 0x8b,
	0x05, 0x28, 0x51, 0x1b, 0x0c, 0xd2, 0x0a, 0x83, 0x94, 0x0f, 0x79, 0x0a, 0x1d, 0x0e, 0xea, 0x3b,
	0x13, 0x50, 0x71, 0x06, 0xf9, 0xfb, 0xab, 0xa2, 0x8c, 0xf2, 0x17, 0x9f, 0x1c, 0xa5, 0x3c, 0xfe,
	0x89, 0x5a, 0x29, 0x65, 0x7f, 0xf9, 0xe6, 0x5d, 0xfa, 0x4f, 0xe1, 0xa1, 0xfc, 0xf5, 0xdb, 0x6d,
	0xe9, 0xaf, 0x6f, 0xb7, 0xa5, 0x7f, 0xbc, 0xdd, 0x96, 0xbe, 0xfc, 0xe7, 0xf6, 0x8d, 0xff, 0x04,
	0x00, 0x00, 0xff, 0xff, 0x4a, 0x20, 0xf6, 0x3b, 0xaa, 0x11, 0x00, 0x00,
}
