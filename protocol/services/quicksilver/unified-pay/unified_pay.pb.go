// Code generated by protoc-gen-go. DO NOT EDIT.
// source: src/quicksilver/unified-pay/unified_pay.proto

package unified_pay

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type FeeType int32

const (
	FeeType_UNKNOWN     FeeType = 0
	FeeType_RED_DIAMOND FeeType = 1
	FeeType_TBEAN       FeeType = 2
)

var FeeType_name = map[int32]string{
	0: "UNKNOWN",
	1: "RED_DIAMOND",
	2: "TBEAN",
}
var FeeType_value = map[string]int32{
	"UNKNOWN":     0,
	"RED_DIAMOND": 1,
	"TBEAN":       2,
}

func (x FeeType) String() string {
	return proto.EnumName(FeeType_name, int32(x))
}
func (FeeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{0}
}

type PayOrderStatus int32

const (
	PayOrderStatus_INIT      PayOrderStatus = 0
	PayOrderStatus_FREEZED   PayOrderStatus = 1
	PayOrderStatus_COMMITTED PayOrderStatus = 2
	PayOrderStatus_ROLLBACK  PayOrderStatus = 3
	PayOrderStatus_ERROR     PayOrderStatus = 99
)

var PayOrderStatus_name = map[int32]string{
	0:  "INIT",
	1:  "FREEZED",
	2:  "COMMITTED",
	3:  "ROLLBACK",
	99: "ERROR",
}
var PayOrderStatus_value = map[string]int32{
	"INIT":      0,
	"FREEZED":   1,
	"COMMITTED": 2,
	"ROLLBACK":  3,
	"ERROR":     99,
}

func (x PayOrderStatus) String() string {
	return proto.EnumName(PayOrderStatus_name, int32(x))
}
func (PayOrderStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{1}
}

type Callback_Type int32

const (
	Callback_UNDEFINED Callback_Type = 0
	Callback_GRPC      Callback_Type = 1
	Callback_HTTP      Callback_Type = 2
)

var Callback_Type_name = map[int32]string{
	0: "UNDEFINED",
	1: "GRPC",
	2: "HTTP",
}
var Callback_Type_value = map[string]int32{
	"UNDEFINED": 0,
	"GRPC":      1,
	"HTTP":      2,
}

func (x Callback_Type) String() string {
	return proto.EnumName(Callback_Type_name, int32(x))
}
func (Callback_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{1, 0}
}

type ConfirmReq_OP int32

const (
	ConfirmReq_COMMIT   ConfirmReq_OP = 0
	ConfirmReq_ROLLBACK ConfirmReq_OP = 1
)

var ConfirmReq_OP_name = map[int32]string{
	0: "COMMIT",
	1: "ROLLBACK",
}
var ConfirmReq_OP_value = map[string]int32{
	"COMMIT":   0,
	"ROLLBACK": 1,
}

func (x ConfirmReq_OP) String() string {
	return proto.EnumName(ConfirmReq_OP_name, int32(x))
}
func (ConfirmReq_OP) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{5, 0}
}

type ClientInfo struct {
	ClientIp             string   `protobuf:"bytes,1,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	ClientDeviceId       []byte   `protobuf:"bytes,2,opt,name=client_device_id,json=clientDeviceId,proto3" json:"client_device_id,omitempty"`
	ClientDeviceInfo     string   `protobuf:"bytes,3,opt,name=client_device_info,json=clientDeviceInfo,proto3" json:"client_device_info,omitempty"`
	TerminalType         uint32   `protobuf:"varint,4,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClientInfo) Reset()         { *m = ClientInfo{} }
func (m *ClientInfo) String() string { return proto.CompactTextString(m) }
func (*ClientInfo) ProtoMessage()    {}
func (*ClientInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{0}
}
func (m *ClientInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClientInfo.Unmarshal(m, b)
}
func (m *ClientInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClientInfo.Marshal(b, m, deterministic)
}
func (dst *ClientInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClientInfo.Merge(dst, src)
}
func (m *ClientInfo) XXX_Size() int {
	return xxx_messageInfo_ClientInfo.Size(m)
}
func (m *ClientInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ClientInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ClientInfo proto.InternalMessageInfo

func (m *ClientInfo) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *ClientInfo) GetClientDeviceId() []byte {
	if m != nil {
		return m.ClientDeviceId
	}
	return nil
}

func (m *ClientInfo) GetClientDeviceInfo() string {
	if m != nil {
		return m.ClientDeviceInfo
	}
	return ""
}

func (m *ClientInfo) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

type Callback struct {
	// Type, only GRPC is supported by now.
	Type Callback_Type `protobuf:"varint,25,opt,name=type,proto3,enum=unified_pay.Callback_Type" json:"type,omitempty"`
	// A url which should be registered to name server
	// e.g.: pay_callback.userpresent.52tt.local
	Url string `protobuf:"bytes,26,opt,name=url,proto3" json:"url,omitempty"`
	// Duration after pay order is created (in seconds).
	// default: 5 minutes
	// max: 1 hour
	FirstTime            uint32   `protobuf:"varint,27,opt,name=first_time,json=firstTime,proto3" json:"first_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Callback) Reset()         { *m = Callback{} }
func (m *Callback) String() string { return proto.CompactTextString(m) }
func (*Callback) ProtoMessage()    {}
func (*Callback) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{1}
}
func (m *Callback) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Callback.Unmarshal(m, b)
}
func (m *Callback) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Callback.Marshal(b, m, deterministic)
}
func (dst *Callback) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Callback.Merge(dst, src)
}
func (m *Callback) XXX_Size() int {
	return xxx_messageInfo_Callback.Size(m)
}
func (m *Callback) XXX_DiscardUnknown() {
	xxx_messageInfo_Callback.DiscardUnknown(m)
}

var xxx_messageInfo_Callback proto.InternalMessageInfo

func (m *Callback) GetType() Callback_Type {
	if m != nil {
		return m.Type
	}
	return Callback_UNDEFINED
}

func (m *Callback) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *Callback) GetFirstTime() uint32 {
	if m != nil {
		return m.FirstTime
	}
	return 0
}

type PayOrder struct {
	AppId                string         `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	OutTradeNo           string         `protobuf:"bytes,2,opt,name=out_trade_no,json=outTradeNo,proto3" json:"out_trade_no,omitempty"`
	Uid                  uint32         `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	UserName             string         `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	FeeType              FeeType        `protobuf:"varint,5,opt,name=fee_type,json=feeType,proto3,enum=unified_pay.FeeType" json:"fee_type,omitempty"`
	TotalFee             uint32         `protobuf:"varint,6,opt,name=total_fee,json=totalFee,proto3" json:"total_fee,omitempty"`
	Body                 string         `protobuf:"bytes,7,opt,name=body,proto3" json:"body,omitempty"`
	Detail               string         `protobuf:"bytes,8,opt,name=detail,proto3" json:"detail,omitempty"`
	CreateAt             uint32         `protobuf:"varint,9,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	Status               PayOrderStatus `protobuf:"varint,10,opt,name=status,proto3,enum=unified_pay.PayOrderStatus" json:"status,omitempty"`
	Confirmed            bool           `protobuf:"varint,11,opt,name=confirmed,proto3" json:"confirmed,omitempty"`
	ItemId               string         `protobuf:"bytes,12,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ClientInfo           *ClientInfo    `protobuf:"bytes,20,opt,name=client_info,json=clientInfo,proto3" json:"client_info,omitempty"`
	Callback             *Callback      `protobuf:"bytes,21,opt,name=callback,proto3" json:"callback,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *PayOrder) Reset()         { *m = PayOrder{} }
func (m *PayOrder) String() string { return proto.CompactTextString(m) }
func (*PayOrder) ProtoMessage()    {}
func (*PayOrder) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{2}
}
func (m *PayOrder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayOrder.Unmarshal(m, b)
}
func (m *PayOrder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayOrder.Marshal(b, m, deterministic)
}
func (dst *PayOrder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayOrder.Merge(dst, src)
}
func (m *PayOrder) XXX_Size() int {
	return xxx_messageInfo_PayOrder.Size(m)
}
func (m *PayOrder) XXX_DiscardUnknown() {
	xxx_messageInfo_PayOrder.DiscardUnknown(m)
}

var xxx_messageInfo_PayOrder proto.InternalMessageInfo

func (m *PayOrder) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *PayOrder) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *PayOrder) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PayOrder) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *PayOrder) GetFeeType() FeeType {
	if m != nil {
		return m.FeeType
	}
	return FeeType_UNKNOWN
}

func (m *PayOrder) GetTotalFee() uint32 {
	if m != nil {
		return m.TotalFee
	}
	return 0
}

func (m *PayOrder) GetBody() string {
	if m != nil {
		return m.Body
	}
	return ""
}

func (m *PayOrder) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

func (m *PayOrder) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *PayOrder) GetStatus() PayOrderStatus {
	if m != nil {
		return m.Status
	}
	return PayOrderStatus_INIT
}

func (m *PayOrder) GetConfirmed() bool {
	if m != nil {
		return m.Confirmed
	}
	return false
}

func (m *PayOrder) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

func (m *PayOrder) GetClientInfo() *ClientInfo {
	if m != nil {
		return m.ClientInfo
	}
	return nil
}

func (m *PayOrder) GetCallback() *Callback {
	if m != nil {
		return m.Callback
	}
	return nil
}

type FreezeReq struct {
	PayOrder             *PayOrder `protobuf:"bytes,1,opt,name=pay_order,json=payOrder,proto3" json:"pay_order,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *FreezeReq) Reset()         { *m = FreezeReq{} }
func (m *FreezeReq) String() string { return proto.CompactTextString(m) }
func (*FreezeReq) ProtoMessage()    {}
func (*FreezeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{3}
}
func (m *FreezeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeReq.Unmarshal(m, b)
}
func (m *FreezeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeReq.Marshal(b, m, deterministic)
}
func (dst *FreezeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeReq.Merge(dst, src)
}
func (m *FreezeReq) XXX_Size() int {
	return xxx_messageInfo_FreezeReq.Size(m)
}
func (m *FreezeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeReq.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeReq proto.InternalMessageInfo

func (m *FreezeReq) GetPayOrder() *PayOrder {
	if m != nil {
		return m.PayOrder
	}
	return nil
}

type FreezeResp struct {
	PayOrderId           string   `protobuf:"bytes,1,opt,name=pay_order_id,json=payOrderId,proto3" json:"pay_order_id,omitempty"`
	Balance              int64    `protobuf:"varint,2,opt,name=balance,proto3" json:"balance,omitempty"`
	TbeanOrderTime       string   `protobuf:"bytes,3,opt,name=tbean_order_time,json=tbeanOrderTime,proto3" json:"tbean_order_time,omitempty"`
	DealToken            string   `protobuf:"bytes,4,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreezeResp) Reset()         { *m = FreezeResp{} }
func (m *FreezeResp) String() string { return proto.CompactTextString(m) }
func (*FreezeResp) ProtoMessage()    {}
func (*FreezeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{4}
}
func (m *FreezeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeResp.Unmarshal(m, b)
}
func (m *FreezeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeResp.Marshal(b, m, deterministic)
}
func (dst *FreezeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeResp.Merge(dst, src)
}
func (m *FreezeResp) XXX_Size() int {
	return xxx_messageInfo_FreezeResp.Size(m)
}
func (m *FreezeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeResp.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeResp proto.InternalMessageInfo

func (m *FreezeResp) GetPayOrderId() string {
	if m != nil {
		return m.PayOrderId
	}
	return ""
}

func (m *FreezeResp) GetBalance() int64 {
	if m != nil {
		return m.Balance
	}
	return 0
}

func (m *FreezeResp) GetTbeanOrderTime() string {
	if m != nil {
		return m.TbeanOrderTime
	}
	return ""
}

func (m *FreezeResp) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

type ConfirmReq struct {
	AppId                string        `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	PayOrderId           string        `protobuf:"bytes,2,opt,name=pay_order_id,json=payOrderId,proto3" json:"pay_order_id,omitempty"`
	OutTradeNo           string        `protobuf:"bytes,3,opt,name=out_trade_no,json=outTradeNo,proto3" json:"out_trade_no,omitempty"`
	Op                   ConfirmReq_OP `protobuf:"varint,4,opt,name=op,proto3,enum=unified_pay.ConfirmReq_OP" json:"op,omitempty"`
	Oper                 string        `protobuf:"bytes,5,opt,name=oper,proto3" json:"oper,omitempty"`
	Synchronized         bool          `protobuf:"varint,6,opt,name=synchronized,proto3" json:"synchronized,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ConfirmReq) Reset()         { *m = ConfirmReq{} }
func (m *ConfirmReq) String() string { return proto.CompactTextString(m) }
func (*ConfirmReq) ProtoMessage()    {}
func (*ConfirmReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{5}
}
func (m *ConfirmReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmReq.Unmarshal(m, b)
}
func (m *ConfirmReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmReq.Marshal(b, m, deterministic)
}
func (dst *ConfirmReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmReq.Merge(dst, src)
}
func (m *ConfirmReq) XXX_Size() int {
	return xxx_messageInfo_ConfirmReq.Size(m)
}
func (m *ConfirmReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmReq proto.InternalMessageInfo

func (m *ConfirmReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *ConfirmReq) GetPayOrderId() string {
	if m != nil {
		return m.PayOrderId
	}
	return ""
}

func (m *ConfirmReq) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *ConfirmReq) GetOp() ConfirmReq_OP {
	if m != nil {
		return m.Op
	}
	return ConfirmReq_COMMIT
}

func (m *ConfirmReq) GetOper() string {
	if m != nil {
		return m.Oper
	}
	return ""
}

func (m *ConfirmReq) GetSynchronized() bool {
	if m != nil {
		return m.Synchronized
	}
	return false
}

type ConfirmResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmResp) Reset()         { *m = ConfirmResp{} }
func (m *ConfirmResp) String() string { return proto.CompactTextString(m) }
func (*ConfirmResp) ProtoMessage()    {}
func (*ConfirmResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{6}
}
func (m *ConfirmResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmResp.Unmarshal(m, b)
}
func (m *ConfirmResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmResp.Marshal(b, m, deterministic)
}
func (dst *ConfirmResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmResp.Merge(dst, src)
}
func (m *ConfirmResp) XXX_Size() int {
	return xxx_messageInfo_ConfirmResp.Size(m)
}
func (m *ConfirmResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmResp proto.InternalMessageInfo

type DirectPayReq struct {
	PayOrder             *PayOrder `protobuf:"bytes,1,opt,name=pay_order,json=payOrder,proto3" json:"pay_order,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *DirectPayReq) Reset()         { *m = DirectPayReq{} }
func (m *DirectPayReq) String() string { return proto.CompactTextString(m) }
func (*DirectPayReq) ProtoMessage()    {}
func (*DirectPayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{7}
}
func (m *DirectPayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DirectPayReq.Unmarshal(m, b)
}
func (m *DirectPayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DirectPayReq.Marshal(b, m, deterministic)
}
func (dst *DirectPayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DirectPayReq.Merge(dst, src)
}
func (m *DirectPayReq) XXX_Size() int {
	return xxx_messageInfo_DirectPayReq.Size(m)
}
func (m *DirectPayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DirectPayReq.DiscardUnknown(m)
}

var xxx_messageInfo_DirectPayReq proto.InternalMessageInfo

func (m *DirectPayReq) GetPayOrder() *PayOrder {
	if m != nil {
		return m.PayOrder
	}
	return nil
}

type DirectPayResp struct {
	Balance              int64    `protobuf:"varint,1,opt,name=balance,proto3" json:"balance,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DirectPayResp) Reset()         { *m = DirectPayResp{} }
func (m *DirectPayResp) String() string { return proto.CompactTextString(m) }
func (*DirectPayResp) ProtoMessage()    {}
func (*DirectPayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{8}
}
func (m *DirectPayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DirectPayResp.Unmarshal(m, b)
}
func (m *DirectPayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DirectPayResp.Marshal(b, m, deterministic)
}
func (dst *DirectPayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DirectPayResp.Merge(dst, src)
}
func (m *DirectPayResp) XXX_Size() int {
	return xxx_messageInfo_DirectPayResp.Size(m)
}
func (m *DirectPayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DirectPayResp.DiscardUnknown(m)
}

var xxx_messageInfo_DirectPayResp proto.InternalMessageInfo

func (m *DirectPayResp) GetBalance() int64 {
	if m != nil {
		return m.Balance
	}
	return 0
}

//
type TransOrder struct {
	AppId                string         `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	OutTradeNo           string         `protobuf:"bytes,2,opt,name=out_trade_no,json=outTradeNo,proto3" json:"out_trade_no,omitempty"`
	FromUid              uint32         `protobuf:"varint,3,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	FromUserName         string         `protobuf:"bytes,4,opt,name=from_user_name,json=fromUserName,proto3" json:"from_user_name,omitempty"`
	TargetUid            uint32         `protobuf:"varint,5,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	TargetUserName       string         `protobuf:"bytes,6,opt,name=target_user_name,json=targetUserName,proto3" json:"target_user_name,omitempty"`
	FeeType              FeeType        `protobuf:"varint,7,opt,name=fee_type,json=feeType,proto3,enum=unified_pay.FeeType" json:"fee_type,omitempty"`
	TotalFee             uint32         `protobuf:"varint,8,opt,name=total_fee,json=totalFee,proto3" json:"total_fee,omitempty"`
	CreateAt             uint32         `protobuf:"varint,9,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	Status               PayOrderStatus `protobuf:"varint,10,opt,name=status,proto3,enum=unified_pay.PayOrderStatus" json:"status,omitempty"`
	SubId                string         `protobuf:"bytes,11,opt,name=sub_id,json=subId,proto3" json:"sub_id,omitempty"`
	ClientInfo           *ClientInfo    `protobuf:"bytes,20,opt,name=client_info,json=clientInfo,proto3" json:"client_info,omitempty"`
	Callback             *Callback      `protobuf:"bytes,21,opt,name=callback,proto3" json:"callback,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *TransOrder) Reset()         { *m = TransOrder{} }
func (m *TransOrder) String() string { return proto.CompactTextString(m) }
func (*TransOrder) ProtoMessage()    {}
func (*TransOrder) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{9}
}
func (m *TransOrder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TransOrder.Unmarshal(m, b)
}
func (m *TransOrder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TransOrder.Marshal(b, m, deterministic)
}
func (dst *TransOrder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransOrder.Merge(dst, src)
}
func (m *TransOrder) XXX_Size() int {
	return xxx_messageInfo_TransOrder.Size(m)
}
func (m *TransOrder) XXX_DiscardUnknown() {
	xxx_messageInfo_TransOrder.DiscardUnknown(m)
}

var xxx_messageInfo_TransOrder proto.InternalMessageInfo

func (m *TransOrder) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *TransOrder) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *TransOrder) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *TransOrder) GetFromUserName() string {
	if m != nil {
		return m.FromUserName
	}
	return ""
}

func (m *TransOrder) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *TransOrder) GetTargetUserName() string {
	if m != nil {
		return m.TargetUserName
	}
	return ""
}

func (m *TransOrder) GetFeeType() FeeType {
	if m != nil {
		return m.FeeType
	}
	return FeeType_UNKNOWN
}

func (m *TransOrder) GetTotalFee() uint32 {
	if m != nil {
		return m.TotalFee
	}
	return 0
}

func (m *TransOrder) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *TransOrder) GetStatus() PayOrderStatus {
	if m != nil {
		return m.Status
	}
	return PayOrderStatus_INIT
}

func (m *TransOrder) GetSubId() string {
	if m != nil {
		return m.SubId
	}
	return ""
}

func (m *TransOrder) GetClientInfo() *ClientInfo {
	if m != nil {
		return m.ClientInfo
	}
	return nil
}

func (m *TransOrder) GetCallback() *Callback {
	if m != nil {
		return m.Callback
	}
	return nil
}

type DirectTransReq struct {
	TransOrder           *TransOrder `protobuf:"bytes,1,opt,name=trans_order,json=transOrder,proto3" json:"trans_order,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *DirectTransReq) Reset()         { *m = DirectTransReq{} }
func (m *DirectTransReq) String() string { return proto.CompactTextString(m) }
func (*DirectTransReq) ProtoMessage()    {}
func (*DirectTransReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{10}
}
func (m *DirectTransReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DirectTransReq.Unmarshal(m, b)
}
func (m *DirectTransReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DirectTransReq.Marshal(b, m, deterministic)
}
func (dst *DirectTransReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DirectTransReq.Merge(dst, src)
}
func (m *DirectTransReq) XXX_Size() int {
	return xxx_messageInfo_DirectTransReq.Size(m)
}
func (m *DirectTransReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DirectTransReq.DiscardUnknown(m)
}

var xxx_messageInfo_DirectTransReq proto.InternalMessageInfo

func (m *DirectTransReq) GetTransOrder() *TransOrder {
	if m != nil {
		return m.TransOrder
	}
	return nil
}

type DirectTransResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DirectTransResp) Reset()         { *m = DirectTransResp{} }
func (m *DirectTransResp) String() string { return proto.CompactTextString(m) }
func (*DirectTransResp) ProtoMessage()    {}
func (*DirectTransResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{11}
}
func (m *DirectTransResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DirectTransResp.Unmarshal(m, b)
}
func (m *DirectTransResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DirectTransResp.Marshal(b, m, deterministic)
}
func (dst *DirectTransResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DirectTransResp.Merge(dst, src)
}
func (m *DirectTransResp) XXX_Size() int {
	return xxx_messageInfo_DirectTransResp.Size(m)
}
func (m *DirectTransResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DirectTransResp.DiscardUnknown(m)
}

var xxx_messageInfo_DirectTransResp proto.InternalMessageInfo

// PresetFreezeReq
type PresetFreezeReq struct {
	AppId                string   `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	FreezeBalance        uint32   `protobuf:"varint,3,opt,name=freeze_balance,json=freezeBalance,proto3" json:"freeze_balance,omitempty"`
	OutTradeNo           string   `protobuf:"bytes,4,opt,name=out_trade_no,json=outTradeNo,proto3" json:"out_trade_no,omitempty"`
	FreezeReason         string   `protobuf:"bytes,5,opt,name=freeze_reason,json=freezeReason,proto3" json:"freeze_reason,omitempty"`
	OutOrderTime         string   `protobuf:"bytes,6,opt,name=out_order_time,json=outOrderTime,proto3" json:"out_order_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresetFreezeReq) Reset()         { *m = PresetFreezeReq{} }
func (m *PresetFreezeReq) String() string { return proto.CompactTextString(m) }
func (*PresetFreezeReq) ProtoMessage()    {}
func (*PresetFreezeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{12}
}
func (m *PresetFreezeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresetFreezeReq.Unmarshal(m, b)
}
func (m *PresetFreezeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresetFreezeReq.Marshal(b, m, deterministic)
}
func (dst *PresetFreezeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresetFreezeReq.Merge(dst, src)
}
func (m *PresetFreezeReq) XXX_Size() int {
	return xxx_messageInfo_PresetFreezeReq.Size(m)
}
func (m *PresetFreezeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PresetFreezeReq.DiscardUnknown(m)
}

var xxx_messageInfo_PresetFreezeReq proto.InternalMessageInfo

func (m *PresetFreezeReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *PresetFreezeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PresetFreezeReq) GetFreezeBalance() uint32 {
	if m != nil {
		return m.FreezeBalance
	}
	return 0
}

func (m *PresetFreezeReq) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *PresetFreezeReq) GetFreezeReason() string {
	if m != nil {
		return m.FreezeReason
	}
	return ""
}

func (m *PresetFreezeReq) GetOutOrderTime() string {
	if m != nil {
		return m.OutOrderTime
	}
	return ""
}

type PresetFreezeResp struct {
	RemainBalance        uint32   `protobuf:"varint,1,opt,name=remain_balance,json=remainBalance,proto3" json:"remain_balance,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresetFreezeResp) Reset()         { *m = PresetFreezeResp{} }
func (m *PresetFreezeResp) String() string { return proto.CompactTextString(m) }
func (*PresetFreezeResp) ProtoMessage()    {}
func (*PresetFreezeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{13}
}
func (m *PresetFreezeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresetFreezeResp.Unmarshal(m, b)
}
func (m *PresetFreezeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresetFreezeResp.Marshal(b, m, deterministic)
}
func (dst *PresetFreezeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresetFreezeResp.Merge(dst, src)
}
func (m *PresetFreezeResp) XXX_Size() int {
	return xxx_messageInfo_PresetFreezeResp.Size(m)
}
func (m *PresetFreezeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PresetFreezeResp.DiscardUnknown(m)
}

var xxx_messageInfo_PresetFreezeResp proto.InternalMessageInfo

func (m *PresetFreezeResp) GetRemainBalance() uint32 {
	if m != nil {
		return m.RemainBalance
	}
	return 0
}

type UnFreezeAndRefundReq struct {
	AppId                string   `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	OutTradeNo           string   `protobuf:"bytes,3,opt,name=out_trade_no,json=outTradeNo,proto3" json:"out_trade_no,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnFreezeAndRefundReq) Reset()         { *m = UnFreezeAndRefundReq{} }
func (m *UnFreezeAndRefundReq) String() string { return proto.CompactTextString(m) }
func (*UnFreezeAndRefundReq) ProtoMessage()    {}
func (*UnFreezeAndRefundReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{14}
}
func (m *UnFreezeAndRefundReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnFreezeAndRefundReq.Unmarshal(m, b)
}
func (m *UnFreezeAndRefundReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnFreezeAndRefundReq.Marshal(b, m, deterministic)
}
func (dst *UnFreezeAndRefundReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnFreezeAndRefundReq.Merge(dst, src)
}
func (m *UnFreezeAndRefundReq) XXX_Size() int {
	return xxx_messageInfo_UnFreezeAndRefundReq.Size(m)
}
func (m *UnFreezeAndRefundReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnFreezeAndRefundReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnFreezeAndRefundReq proto.InternalMessageInfo

func (m *UnFreezeAndRefundReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *UnFreezeAndRefundReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UnFreezeAndRefundReq) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

type UnFreezeAndRefundResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnFreezeAndRefundResp) Reset()         { *m = UnFreezeAndRefundResp{} }
func (m *UnFreezeAndRefundResp) String() string { return proto.CompactTextString(m) }
func (*UnFreezeAndRefundResp) ProtoMessage()    {}
func (*UnFreezeAndRefundResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{15}
}
func (m *UnFreezeAndRefundResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnFreezeAndRefundResp.Unmarshal(m, b)
}
func (m *UnFreezeAndRefundResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnFreezeAndRefundResp.Marshal(b, m, deterministic)
}
func (dst *UnFreezeAndRefundResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnFreezeAndRefundResp.Merge(dst, src)
}
func (m *UnFreezeAndRefundResp) XXX_Size() int {
	return xxx_messageInfo_UnFreezeAndRefundResp.Size(m)
}
func (m *UnFreezeAndRefundResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnFreezeAndRefundResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnFreezeAndRefundResp proto.InternalMessageInfo

type UnfreezeAndConsumeReq struct {
	AppId                string   `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	UserName             string   `protobuf:"bytes,3,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	ItemId               uint32   `protobuf:"varint,4,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemName             string   `protobuf:"bytes,5,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	ItemNum              uint32   `protobuf:"varint,6,opt,name=item_num,json=itemNum,proto3" json:"item_num,omitempty"`
	ItemPrice            uint32   `protobuf:"varint,7,opt,name=item_price,json=itemPrice,proto3" json:"item_price,omitempty"`
	TotalPrice           uint32   `protobuf:"varint,8,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	Platform             string   `protobuf:"bytes,9,opt,name=platform,proto3" json:"platform,omitempty"`
	OutTradeNo           string   `protobuf:"bytes,10,opt,name=out_trade_no,json=outTradeNo,proto3" json:"out_trade_no,omitempty"`
	Notes                string   `protobuf:"bytes,11,opt,name=notes,proto3" json:"notes,omitempty"`
	OutOrderTime         string   `protobuf:"bytes,12,opt,name=out_order_time,json=outOrderTime,proto3" json:"out_order_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnfreezeAndConsumeReq) Reset()         { *m = UnfreezeAndConsumeReq{} }
func (m *UnfreezeAndConsumeReq) String() string { return proto.CompactTextString(m) }
func (*UnfreezeAndConsumeReq) ProtoMessage()    {}
func (*UnfreezeAndConsumeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{16}
}
func (m *UnfreezeAndConsumeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnfreezeAndConsumeReq.Unmarshal(m, b)
}
func (m *UnfreezeAndConsumeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnfreezeAndConsumeReq.Marshal(b, m, deterministic)
}
func (dst *UnfreezeAndConsumeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnfreezeAndConsumeReq.Merge(dst, src)
}
func (m *UnfreezeAndConsumeReq) XXX_Size() int {
	return xxx_messageInfo_UnfreezeAndConsumeReq.Size(m)
}
func (m *UnfreezeAndConsumeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnfreezeAndConsumeReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnfreezeAndConsumeReq proto.InternalMessageInfo

func (m *UnfreezeAndConsumeReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *UnfreezeAndConsumeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UnfreezeAndConsumeReq) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *UnfreezeAndConsumeReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *UnfreezeAndConsumeReq) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *UnfreezeAndConsumeReq) GetItemNum() uint32 {
	if m != nil {
		return m.ItemNum
	}
	return 0
}

func (m *UnfreezeAndConsumeReq) GetItemPrice() uint32 {
	if m != nil {
		return m.ItemPrice
	}
	return 0
}

func (m *UnfreezeAndConsumeReq) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *UnfreezeAndConsumeReq) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

func (m *UnfreezeAndConsumeReq) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *UnfreezeAndConsumeReq) GetNotes() string {
	if m != nil {
		return m.Notes
	}
	return ""
}

func (m *UnfreezeAndConsumeReq) GetOutOrderTime() string {
	if m != nil {
		return m.OutOrderTime
	}
	return ""
}

type UnfreezeAndConsumeResp struct {
	Ctime                string   `protobuf:"bytes,1,opt,name=ctime,proto3" json:"ctime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnfreezeAndConsumeResp) Reset()         { *m = UnfreezeAndConsumeResp{} }
func (m *UnfreezeAndConsumeResp) String() string { return proto.CompactTextString(m) }
func (*UnfreezeAndConsumeResp) ProtoMessage()    {}
func (*UnfreezeAndConsumeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_0e393e7a4ecb21ff, []int{17}
}
func (m *UnfreezeAndConsumeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnfreezeAndConsumeResp.Unmarshal(m, b)
}
func (m *UnfreezeAndConsumeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnfreezeAndConsumeResp.Marshal(b, m, deterministic)
}
func (dst *UnfreezeAndConsumeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnfreezeAndConsumeResp.Merge(dst, src)
}
func (m *UnfreezeAndConsumeResp) XXX_Size() int {
	return xxx_messageInfo_UnfreezeAndConsumeResp.Size(m)
}
func (m *UnfreezeAndConsumeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnfreezeAndConsumeResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnfreezeAndConsumeResp proto.InternalMessageInfo

func (m *UnfreezeAndConsumeResp) GetCtime() string {
	if m != nil {
		return m.Ctime
	}
	return ""
}

func init() {
	proto.RegisterType((*ClientInfo)(nil), "unified_pay.ClientInfo")
	proto.RegisterType((*Callback)(nil), "unified_pay.Callback")
	proto.RegisterType((*PayOrder)(nil), "unified_pay.PayOrder")
	proto.RegisterType((*FreezeReq)(nil), "unified_pay.FreezeReq")
	proto.RegisterType((*FreezeResp)(nil), "unified_pay.FreezeResp")
	proto.RegisterType((*ConfirmReq)(nil), "unified_pay.ConfirmReq")
	proto.RegisterType((*ConfirmResp)(nil), "unified_pay.ConfirmResp")
	proto.RegisterType((*DirectPayReq)(nil), "unified_pay.DirectPayReq")
	proto.RegisterType((*DirectPayResp)(nil), "unified_pay.DirectPayResp")
	proto.RegisterType((*TransOrder)(nil), "unified_pay.TransOrder")
	proto.RegisterType((*DirectTransReq)(nil), "unified_pay.DirectTransReq")
	proto.RegisterType((*DirectTransResp)(nil), "unified_pay.DirectTransResp")
	proto.RegisterType((*PresetFreezeReq)(nil), "unified_pay.PresetFreezeReq")
	proto.RegisterType((*PresetFreezeResp)(nil), "unified_pay.PresetFreezeResp")
	proto.RegisterType((*UnFreezeAndRefundReq)(nil), "unified_pay.UnFreezeAndRefundReq")
	proto.RegisterType((*UnFreezeAndRefundResp)(nil), "unified_pay.UnFreezeAndRefundResp")
	proto.RegisterType((*UnfreezeAndConsumeReq)(nil), "unified_pay.UnfreezeAndConsumeReq")
	proto.RegisterType((*UnfreezeAndConsumeResp)(nil), "unified_pay.UnfreezeAndConsumeResp")
	proto.RegisterEnum("unified_pay.FeeType", FeeType_name, FeeType_value)
	proto.RegisterEnum("unified_pay.PayOrderStatus", PayOrderStatus_name, PayOrderStatus_value)
	proto.RegisterEnum("unified_pay.Callback_Type", Callback_Type_name, Callback_Type_value)
	proto.RegisterEnum("unified_pay.ConfirmReq_OP", ConfirmReq_OP_name, ConfirmReq_OP_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UnifiedPayClient is the client API for UnifiedPay service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UnifiedPayClient interface {
	Freeze(ctx context.Context, in *FreezeReq, opts ...grpc.CallOption) (*FreezeResp, error)
	Confirm(ctx context.Context, in *ConfirmReq, opts ...grpc.CallOption) (*ConfirmResp, error)
	DirectPay(ctx context.Context, in *DirectPayReq, opts ...grpc.CallOption) (*DirectPayResp, error)
	DirectTrans(ctx context.Context, in *DirectTransReq, opts ...grpc.CallOption) (*DirectTransResp, error)
	// T豆冻结接口 V2 支持长时间冻结
	PresetFreeze(ctx context.Context, in *PresetFreezeReq, opts ...grpc.CallOption) (*PresetFreezeResp, error)
	// T豆解冻并回退接口
	UnFreezeAndRefund(ctx context.Context, in *UnFreezeAndRefundReq, opts ...grpc.CallOption) (*UnFreezeAndRefundResp, error)
	// T豆解冻并消费
	UnfreezeAndConsume(ctx context.Context, in *UnfreezeAndConsumeReq, opts ...grpc.CallOption) (*UnfreezeAndConsumeResp, error)
}

type unifiedPayClient struct {
	cc *grpc.ClientConn
}

func NewUnifiedPayClient(cc *grpc.ClientConn) UnifiedPayClient {
	return &unifiedPayClient{cc}
}

func (c *unifiedPayClient) Freeze(ctx context.Context, in *FreezeReq, opts ...grpc.CallOption) (*FreezeResp, error) {
	out := new(FreezeResp)
	err := c.cc.Invoke(ctx, "/unified_pay.UnifiedPay/Freeze", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *unifiedPayClient) Confirm(ctx context.Context, in *ConfirmReq, opts ...grpc.CallOption) (*ConfirmResp, error) {
	out := new(ConfirmResp)
	err := c.cc.Invoke(ctx, "/unified_pay.UnifiedPay/Confirm", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *unifiedPayClient) DirectPay(ctx context.Context, in *DirectPayReq, opts ...grpc.CallOption) (*DirectPayResp, error) {
	out := new(DirectPayResp)
	err := c.cc.Invoke(ctx, "/unified_pay.UnifiedPay/DirectPay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *unifiedPayClient) DirectTrans(ctx context.Context, in *DirectTransReq, opts ...grpc.CallOption) (*DirectTransResp, error) {
	out := new(DirectTransResp)
	err := c.cc.Invoke(ctx, "/unified_pay.UnifiedPay/DirectTrans", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *unifiedPayClient) PresetFreeze(ctx context.Context, in *PresetFreezeReq, opts ...grpc.CallOption) (*PresetFreezeResp, error) {
	out := new(PresetFreezeResp)
	err := c.cc.Invoke(ctx, "/unified_pay.UnifiedPay/PresetFreeze", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *unifiedPayClient) UnFreezeAndRefund(ctx context.Context, in *UnFreezeAndRefundReq, opts ...grpc.CallOption) (*UnFreezeAndRefundResp, error) {
	out := new(UnFreezeAndRefundResp)
	err := c.cc.Invoke(ctx, "/unified_pay.UnifiedPay/UnFreezeAndRefund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *unifiedPayClient) UnfreezeAndConsume(ctx context.Context, in *UnfreezeAndConsumeReq, opts ...grpc.CallOption) (*UnfreezeAndConsumeResp, error) {
	out := new(UnfreezeAndConsumeResp)
	err := c.cc.Invoke(ctx, "/unified_pay.UnifiedPay/UnfreezeAndConsume", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UnifiedPayServer is the server API for UnifiedPay service.
type UnifiedPayServer interface {
	Freeze(context.Context, *FreezeReq) (*FreezeResp, error)
	Confirm(context.Context, *ConfirmReq) (*ConfirmResp, error)
	DirectPay(context.Context, *DirectPayReq) (*DirectPayResp, error)
	DirectTrans(context.Context, *DirectTransReq) (*DirectTransResp, error)
	// T豆冻结接口 V2 支持长时间冻结
	PresetFreeze(context.Context, *PresetFreezeReq) (*PresetFreezeResp, error)
	// T豆解冻并回退接口
	UnFreezeAndRefund(context.Context, *UnFreezeAndRefundReq) (*UnFreezeAndRefundResp, error)
	// T豆解冻并消费
	UnfreezeAndConsume(context.Context, *UnfreezeAndConsumeReq) (*UnfreezeAndConsumeResp, error)
}

func RegisterUnifiedPayServer(s *grpc.Server, srv UnifiedPayServer) {
	s.RegisterService(&_UnifiedPay_serviceDesc, srv)
}

func _UnifiedPay_Freeze_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreezeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UnifiedPayServer).Freeze(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/unified_pay.UnifiedPay/Freeze",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UnifiedPayServer).Freeze(ctx, req.(*FreezeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UnifiedPay_Confirm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UnifiedPayServer).Confirm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/unified_pay.UnifiedPay/Confirm",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UnifiedPayServer).Confirm(ctx, req.(*ConfirmReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UnifiedPay_DirectPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DirectPayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UnifiedPayServer).DirectPay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/unified_pay.UnifiedPay/DirectPay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UnifiedPayServer).DirectPay(ctx, req.(*DirectPayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UnifiedPay_DirectTrans_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DirectTransReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UnifiedPayServer).DirectTrans(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/unified_pay.UnifiedPay/DirectTrans",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UnifiedPayServer).DirectTrans(ctx, req.(*DirectTransReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UnifiedPay_PresetFreeze_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PresetFreezeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UnifiedPayServer).PresetFreeze(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/unified_pay.UnifiedPay/PresetFreeze",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UnifiedPayServer).PresetFreeze(ctx, req.(*PresetFreezeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UnifiedPay_UnFreezeAndRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnFreezeAndRefundReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UnifiedPayServer).UnFreezeAndRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/unified_pay.UnifiedPay/UnFreezeAndRefund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UnifiedPayServer).UnFreezeAndRefund(ctx, req.(*UnFreezeAndRefundReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UnifiedPay_UnfreezeAndConsume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnfreezeAndConsumeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UnifiedPayServer).UnfreezeAndConsume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/unified_pay.UnifiedPay/UnfreezeAndConsume",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UnifiedPayServer).UnfreezeAndConsume(ctx, req.(*UnfreezeAndConsumeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UnifiedPay_serviceDesc = grpc.ServiceDesc{
	ServiceName: "unified_pay.UnifiedPay",
	HandlerType: (*UnifiedPayServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Freeze",
			Handler:    _UnifiedPay_Freeze_Handler,
		},
		{
			MethodName: "Confirm",
			Handler:    _UnifiedPay_Confirm_Handler,
		},
		{
			MethodName: "DirectPay",
			Handler:    _UnifiedPay_DirectPay_Handler,
		},
		{
			MethodName: "DirectTrans",
			Handler:    _UnifiedPay_DirectTrans_Handler,
		},
		{
			MethodName: "PresetFreeze",
			Handler:    _UnifiedPay_PresetFreeze_Handler,
		},
		{
			MethodName: "UnFreezeAndRefund",
			Handler:    _UnifiedPay_UnFreezeAndRefund_Handler,
		},
		{
			MethodName: "UnfreezeAndConsume",
			Handler:    _UnifiedPay_UnfreezeAndConsume_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/quicksilver/unified-pay/unified_pay.proto",
}

func init() {
	proto.RegisterFile("src/quicksilver/unified-pay/unified_pay.proto", fileDescriptor_unified_pay_0e393e7a4ecb21ff)
}

var fileDescriptor_unified_pay_0e393e7a4ecb21ff = []byte{
	// 1354 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x57, 0xdd, 0x6e, 0x1b, 0x45,
	0x14, 0xce, 0xda, 0x8e, 0xbd, 0x7b, 0x6c, 0xa7, 0xdb, 0x51, 0xd2, 0x6c, 0x9c, 0x16, 0xcc, 0x16,
	0x24, 0xb7, 0xa2, 0xa9, 0x48, 0x6f, 0x8a, 0x90, 0x40, 0x49, 0xec, 0x80, 0xdb, 0xc6, 0xb6, 0x06,
	0x47, 0x48, 0x08, 0x69, 0x35, 0xde, 0x1d, 0xc3, 0xaa, 0xf6, 0xee, 0x76, 0x77, 0x5c, 0xc9, 0x7d,
	0x0b, 0x6e, 0x78, 0x04, 0x24, 0x5e, 0x86, 0x87, 0xe0, 0x0a, 0x89, 0x4b, 0x5e, 0x00, 0xcd, 0x99,
	0x5d, 0xdb, 0x6b, 0x3b, 0x54, 0x05, 0xc4, 0x4d, 0x34, 0xf3, 0x9d, 0x9f, 0x99, 0x3d, 0xf3, 0x9d,
	0xef, 0x38, 0xf0, 0x28, 0x89, 0xdd, 0xc7, 0xaf, 0x66, 0xbe, 0xfb, 0x32, 0xf1, 0x27, 0xaf, 0x79,
	0xfc, 0x78, 0x16, 0xf8, 0x63, 0x9f, 0x7b, 0x8f, 0x22, 0x36, 0xcf, 0xd6, 0x4e, 0xc4, 0xe6, 0x27,
	0x51, 0x1c, 0x8a, 0x90, 0x54, 0x57, 0x20, 0xfb, 0x67, 0x0d, 0xe0, 0x62, 0xe2, 0xf3, 0x40, 0x74,
	0x83, 0x71, 0x48, 0x8e, 0xc1, 0x70, 0x71, 0xe7, 0xf8, 0x91, 0xa5, 0x35, 0xb5, 0x96, 0x41, 0x75,
	0x05, 0x74, 0x23, 0xd2, 0x02, 0x33, 0x35, 0x7a, 0xfc, 0xb5, 0xef, 0x72, 0xc7, 0xf7, 0xac, 0x42,
	0x53, 0x6b, 0xd5, 0xe8, 0x9e, 0xc2, 0xdb, 0x08, 0x77, 0x3d, 0xf2, 0x31, 0x90, 0x35, 0xcf, 0x60,
	0x1c, 0x5a, 0x45, 0xcc, 0x67, 0xe6, 0x7c, 0xe5, 0xa1, 0xf7, 0xa1, 0x2e, 0x78, 0x3c, 0xf5, 0x03,
	0x36, 0x71, 0xc4, 0x3c, 0xe2, 0x56, 0xa9, 0xa9, 0xb5, 0xea, 0xb4, 0x96, 0x81, 0xc3, 0x79, 0xc4,
	0xed, 0x9f, 0x34, 0xd0, 0x2f, 0xd8, 0x64, 0x32, 0x62, 0xee, 0x4b, 0x72, 0x02, 0x25, 0x74, 0x3c,
	0x6a, 0x6a, 0xad, 0xbd, 0xd3, 0xc6, 0xc9, 0xea, 0x47, 0x66, 0x4e, 0x27, 0x32, 0x8c, 0xa2, 0x1f,
	0x31, 0xa1, 0x38, 0x8b, 0x27, 0x56, 0x03, 0x2f, 0x20, 0x97, 0xe4, 0x1e, 0xc0, 0xd8, 0x8f, 0x13,
	0xe1, 0x08, 0x7f, 0xca, 0xad, 0x63, 0x3c, 0xd0, 0x40, 0x64, 0xe8, 0x4f, 0xb9, 0xfd, 0x00, 0x4a,
	0x32, 0x9c, 0xd4, 0xc1, 0xb8, 0xee, 0xb5, 0x3b, 0x97, 0xdd, 0x5e, 0xa7, 0x6d, 0xee, 0x10, 0x1d,
	0x4a, 0x5f, 0xd2, 0xc1, 0x85, 0xa9, 0xc9, 0xd5, 0x57, 0xc3, 0xe1, 0xc0, 0x2c, 0xd8, 0xbf, 0x15,
	0x41, 0x1f, 0xb0, 0x79, 0x3f, 0xf6, 0x78, 0x4c, 0x0e, 0xa0, 0xcc, 0xa2, 0x48, 0x16, 0x46, 0x15,
	0x6f, 0x97, 0x45, 0x51, 0xd7, 0x23, 0x4d, 0xa8, 0x85, 0x33, 0xe1, 0x88, 0x98, 0x79, 0xdc, 0x09,
	0x42, 0xac, 0x9a, 0x41, 0x21, 0x9c, 0x89, 0xa1, 0x84, 0x7a, 0x21, 0xde, 0xd0, 0xf7, 0xb0, 0x44,
	0x75, 0x2a, 0x97, 0xf2, 0x29, 0x66, 0x09, 0x8f, 0x9d, 0x80, 0x4d, 0x55, 0x45, 0x0c, 0xaa, 0x4b,
	0xa0, 0xc7, 0xa6, 0x9c, 0x3c, 0x06, 0x7d, 0xcc, 0xb9, 0xaa, 0xd6, 0x2e, 0x16, 0x61, 0x3f, 0x57,
	0x84, 0x4b, 0xce, 0xf1, 0xf3, 0x2b, 0x63, 0xb5, 0x90, 0xd9, 0x44, 0x28, 0xd8, 0xc4, 0x19, 0x73,
	0x6e, 0x95, 0xf1, 0x14, 0x1d, 0x81, 0x4b, 0xce, 0x09, 0x81, 0xd2, 0x28, 0xf4, 0xe6, 0x56, 0x05,
	0x4f, 0xc1, 0x35, 0xb9, 0x03, 0x65, 0x8f, 0x0b, 0xe6, 0x4f, 0x2c, 0x1d, 0xd1, 0x74, 0x87, 0x0c,
	0x89, 0x39, 0x13, 0xdc, 0x61, 0xc2, 0x32, 0x54, 0x22, 0x05, 0x9c, 0x09, 0xf2, 0x04, 0xca, 0x89,
	0x60, 0x62, 0x96, 0x58, 0x80, 0x97, 0x3a, 0xce, 0x5d, 0x2a, 0xab, 0xd2, 0xd7, 0xe8, 0x42, 0x53,
	0x57, 0x72, 0x17, 0x0c, 0x37, 0x0c, 0xc6, 0x7e, 0x3c, 0xe5, 0x9e, 0x55, 0x6d, 0x6a, 0x2d, 0x9d,
	0x2e, 0x01, 0x72, 0x08, 0x15, 0x5f, 0xf0, 0xa9, 0x2c, 0x69, 0x4d, 0x5d, 0x44, 0x6e, 0xbb, 0x1e,
	0x79, 0x0a, 0xd5, 0x8c, 0xaa, 0x92, 0x5c, 0xfb, 0x4d, 0xad, 0x55, 0x3d, 0x3d, 0xcc, 0x53, 0x61,
	0x41, 0x6c, 0x0a, 0xee, 0x92, 0xe4, 0x9f, 0x80, 0xee, 0xa6, 0x24, 0xb1, 0x0e, 0x30, 0xec, 0x60,
	0x2b, 0x83, 0xe8, 0xc2, 0xcd, 0xfe, 0x02, 0x8c, 0xcb, 0x98, 0xf3, 0x37, 0x9c, 0xf2, 0x57, 0xe4,
	0x14, 0x8c, 0x88, 0xcd, 0x9d, 0x50, 0x7e, 0x0b, 0xbe, 0xf3, 0x7a, 0x82, 0xec, 0x43, 0xa9, 0x1e,
	0xa5, 0x2b, 0xfb, 0x47, 0x0d, 0x20, 0xcb, 0x90, 0x44, 0x92, 0x10, 0x8b, 0x14, 0x4b, 0xb6, 0x40,
	0xe6, 0xde, 0xf5, 0x88, 0x05, 0x95, 0x11, 0x9b, 0xb0, 0xc0, 0xe5, 0xc8, 0x96, 0x22, 0xcd, 0xb6,
	0xb2, 0x0d, 0xc5, 0x88, 0xb3, 0x20, 0x8d, 0x46, 0x02, 0xab, 0xd6, 0xda, 0x43, 0x1c, 0x33, 0x48,
	0x16, 0x4b, 0x92, 0x7b, 0x5c, 0x36, 0x55, 0xf8, 0x92, 0x07, 0x29, 0x87, 0x0c, 0x89, 0x0c, 0x25,
	0x60, 0xff, 0x21, 0x7b, 0x5f, 0x15, 0x5a, 0x7e, 0xd6, 0xcd, 0xdc, 0xcd, 0x5d, 0xb5, 0xb0, 0x71,
	0xd5, 0x75, 0x76, 0x17, 0x37, 0xd8, 0xfd, 0x10, 0x0a, 0x61, 0x84, 0x17, 0xd8, 0xe8, 0xd6, 0xc5,
	0xf9, 0x27, 0xfd, 0x01, 0x2d, 0x84, 0x91, 0x24, 0x63, 0x18, 0xf1, 0x18, 0x69, 0x6d, 0x50, 0x5c,
	0x13, 0x1b, 0x6a, 0xc9, 0x3c, 0x70, 0x7f, 0x88, 0xc3, 0xc0, 0x7f, 0xc3, 0x3d, 0x24, 0xb0, 0x4e,
	0x73, 0x98, 0xfd, 0x1e, 0x14, 0xfa, 0x03, 0x02, 0x50, 0xbe, 0xe8, 0x5f, 0x5d, 0x75, 0x87, 0xe6,
	0x0e, 0xa9, 0x81, 0x4e, 0xfb, 0x2f, 0x5e, 0x9c, 0x9f, 0x5d, 0x3c, 0x37, 0x35, 0xbb, 0x0e, 0xd5,
	0xc5, 0x61, 0x49, 0x64, 0x9f, 0x43, 0xad, 0xed, 0xc7, 0xdc, 0x15, 0x03, 0x36, 0xff, 0xa7, 0x8f,
	0xfa, 0x00, 0xea, 0x2b, 0x39, 0x92, 0x68, 0xf5, 0xd1, 0xb4, 0xdc, 0xa3, 0xd9, 0x7f, 0x16, 0x01,
	0x86, 0x31, 0x0b, 0x92, 0x7f, 0xa9, 0x13, 0x47, 0xa0, 0x8f, 0xe3, 0x70, 0xea, 0x2c, 0xc5, 0xa2,
	0x22, 0xf7, 0xd7, 0xbe, 0x47, 0x3e, 0x84, 0x3d, 0x65, 0x5a, 0x53, 0x8d, 0x1a, 0x3a, 0x64, 0xca,
	0x71, 0x0f, 0x40, 0xb0, 0xf8, 0x7b, 0x2e, 0x30, 0xc5, 0xae, 0x12, 0x3e, 0x85, 0xc8, 0x24, 0x92,
	0x5c, 0xa9, 0x79, 0x91, 0xa6, 0x9c, 0x92, 0x4b, 0x39, 0x6d, 0x93, 0xa0, 0xca, 0x3b, 0x4b, 0x90,
	0xbe, 0x26, 0x41, 0xff, 0xbd, 0xac, 0x1c, 0x40, 0x39, 0x99, 0x8d, 0x64, 0x89, 0xab, 0xaa, 0xc4,
	0xc9, 0x6c, 0xf4, 0x7f, 0xcb, 0xc6, 0x33, 0xd8, 0x53, 0x04, 0xc1, 0xa7, 0x97, 0x34, 0x7b, 0x0a,
	0x55, 0x21, 0xd7, 0x39, 0xa2, 0xe5, 0x8f, 0x5f, 0xd2, 0x84, 0x82, 0x58, 0xac, 0xed, 0xdb, 0x70,
	0x2b, 0x97, 0x2b, 0x89, 0xec, 0x5f, 0x35, 0xb8, 0x35, 0x88, 0x79, 0xc2, 0xc5, 0x52, 0x9c, 0x6e,
	0x60, 0x56, 0x3a, 0x5f, 0x0a, 0xcb, 0xf9, 0xf2, 0x91, 0xa4, 0x8b, 0x8c, 0x72, 0x32, 0xca, 0x2a,
	0x3e, 0xd5, 0x15, 0x7a, 0x9e, 0xaa, 0xcd, 0x3a, 0x25, 0x4b, 0x1b, 0x94, 0xbc, 0x0f, 0x69, 0x88,
	0x13, 0x73, 0x96, 0x84, 0x41, 0xda, 0xb9, 0xb5, 0x71, 0x7a, 0x27, 0x89, 0x49, 0x72, 0xca, 0x34,
	0x2b, 0x92, 0xa5, 0x58, 0x25, 0x93, 0x2f, 0x04, 0xcb, 0xfe, 0x14, 0xcc, 0xfc, 0xf7, 0x24, 0x91,
	0xbc, 0x67, 0xcc, 0xa7, 0xcc, 0x0f, 0x9c, 0xd5, 0xd6, 0xaa, 0xd3, 0xba, 0x42, 0xd3, 0x7b, 0xda,
	0x0c, 0xf6, 0xaf, 0x03, 0x15, 0x76, 0x16, 0x78, 0x94, 0x8f, 0x67, 0xf2, 0xef, 0x3b, 0xd4, 0xe3,
	0xad, 0x2a, 0x66, 0x1f, 0xc2, 0xc1, 0x96, 0x23, 0x92, 0xc8, 0xfe, 0xbd, 0x20, 0x2d, 0xe3, 0xcc,
	0x72, 0x11, 0x06, 0xc9, 0x6c, 0xfa, 0x6e, 0xaf, 0x91, 0x9b, 0xf6, 0xc5, 0xb5, 0x69, 0xbf, 0x32,
	0x03, 0xd5, 0x4f, 0xa3, 0x6c, 0x06, 0x1e, 0x83, 0x81, 0x06, 0x8c, 0x52, 0x65, 0xd7, 0x25, 0x80,
	0x51, 0x47, 0xa0, 0x2b, 0xe3, 0x6c, 0x9a, 0x4e, 0x7c, 0xcc, 0xd2, 0x9b, 0x4d, 0xa5, 0x08, 0xa0,
	0x29, 0x8a, 0x7d, 0x57, 0x75, 0x6f, 0x9d, 0x62, 0xa6, 0x81, 0x04, 0xc8, 0xfb, 0x50, 0x55, 0x9d,
	0xaa, 0xec, 0xaa, 0x57, 0x01, 0x21, 0xe5, 0xd0, 0x00, 0x3d, 0x9a, 0x30, 0x31, 0x0e, 0xe3, 0x29,
	0x36, 0xab, 0x41, 0x17, 0xfb, 0x8d, 0x3a, 0xc2, 0x06, 0x61, 0xf6, 0x61, 0x37, 0x08, 0x05, 0x4f,
	0xb2, 0xc6, 0xc4, 0xcd, 0x16, 0x86, 0xd4, 0xb6, 0x30, 0xe4, 0x04, 0xee, 0x6c, 0xab, 0x74, 0x12,
	0xc9, 0xac, 0x2e, 0x86, 0xa5, 0x95, 0xc6, 0xcd, 0xc3, 0x53, 0xa8, 0xa4, 0x42, 0x44, 0xaa, 0x50,
	0xb9, 0xee, 0x3d, 0xef, 0xf5, 0xbf, 0xe9, 0x99, 0x3b, 0xe4, 0x16, 0x54, 0x69, 0xa7, 0xed, 0xb4,
	0xbb, 0x67, 0x57, 0xfd, 0x5e, 0xdb, 0xd4, 0x88, 0x01, 0xbb, 0xc3, 0xf3, 0xce, 0x59, 0xcf, 0x2c,
	0x3c, 0xec, 0xc3, 0x5e, 0x5e, 0x53, 0xe4, 0xaf, 0xbd, 0x6e, 0x0f, 0x67, 0x4a, 0x15, 0x2a, 0x97,
	0xb4, 0xd3, 0xf9, 0xb6, 0x23, 0x63, 0xea, 0x60, 0xa8, 0x61, 0x33, 0xec, 0xb4, 0xcd, 0x42, 0x6e,
	0xde, 0x14, 0x65, 0xc2, 0x0e, 0xa5, 0x7d, 0x6a, 0xba, 0xa7, 0xbf, 0x94, 0x00, 0xae, 0x55, 0x87,
	0x0f, 0xd8, 0x9c, 0x7c, 0x06, 0x65, 0xc5, 0x22, 0x72, 0x27, 0xaf, 0x98, 0x59, 0x13, 0x37, 0x0e,
	0xb7, 0xe2, 0x49, 0x64, 0xef, 0x90, 0xcf, 0xa1, 0x92, 0x8e, 0x31, 0x72, 0x78, 0xc3, 0x24, 0x6d,
	0x58, 0xdb, 0x0d, 0x18, 0xdf, 0x06, 0x63, 0x31, 0xb3, 0xc8, 0x51, 0xce, 0x71, 0x75, 0x1e, 0x36,
	0x1a, 0x37, 0x99, 0x30, 0xcb, 0x33, 0xa8, 0xae, 0x88, 0x11, 0x39, 0xde, 0xe2, 0x9c, 0x49, 0x5e,
	0xe3, 0xee, 0xcd, 0x46, 0xcc, 0x75, 0x05, 0xb5, 0xd5, 0xa6, 0x27, 0x79, 0xff, 0x35, 0x7d, 0x6b,
	0xdc, 0xfb, 0x1b, 0x2b, 0xa6, 0xfb, 0x0e, 0x6e, 0x6f, 0x74, 0x29, 0xf9, 0x20, 0x17, 0xb5, 0x4d,
	0x28, 0x1a, 0xf6, 0xdb, 0x5c, 0x30, 0xbb, 0x03, 0x64, 0x93, 0x7f, 0x64, 0x3d, 0x76, 0x8b, 0x14,
	0x34, 0xee, 0xbf, 0xd5, 0x47, 0x1e, 0x30, 0x2a, 0xe3, 0x3f, 0x69, 0x4f, 0xfe, 0x0a, 0x00, 0x00,
	0xff, 0xff, 0x2c, 0xc4, 0x42, 0x3e, 0xd5, 0x0d, 0x00, 0x00,
}
