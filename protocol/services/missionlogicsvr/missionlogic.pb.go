// Code generated by protoc-gen-gogo.
// source: src/missionlogicsvr/missionlogic.proto
// DO NOT EDIT!

/*
	Package MissionLogic is a generated protocol buffer package.

	It is generated from these files:
		src/missionlogicsvr/missionlogic.proto

	It has these top-level messages:
		MissionBonus
		HandleMissionReq
		HandleMissionResp
		HandleGuildJoinMissionReq
		HandleGuildJoinMissionResp
		HandleBindPhoneMissionReq
		HandleChannelOnlineMissionReq
		HandleChannelOnlineMissionResp
		MissionGroup
		Mission
		MissionGroupDetail
		Platform
		GetUserMissionsReq
		GetUserMissionsResp
		GetLatestUserMissionReq
		GetLatestUserMissionResp
		CollectMissionBonusReq
		CollectMissionBonusResp
		GrowInfo
		GetUserMissionGuideReq
		GetUserMissionGuideResp
		BuffRatio
		Buff
		MedalBuffRatio
		MedalBuff
		StaticMission
		StaticMissionGroup
		StaticMissionConfig
		GetStaticMissionReq
		GetStaticMissionResp
		IncreaseMissionFinishCountReq
		ResetInProgressMissionReq
		HandleTimeLimitMissionReq
		HandleTimeLimitMissionResp
		AcceptMissionReq
		GetStaticTimeLimitGameMissionReq
		GetStaticTimeLimitGameMissionResp
		HandleEventReq
		HandleEnterChannelFromRankMissionReq
		HandleEnterChannelFromRankMissionResp
*/
package MissionLogic

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 任务类型
type MISSION_TYPE int32

const (
	MISSION_TYPE_GREENER    MISSION_TYPE = 1
	MISSION_TYPE_DAILY      MISSION_TYPE = 2
	MISSION_TYPE_ELITE      MISSION_TYPE = 3
	MISSION_TYPE_TIME_LIMIT MISSION_TYPE = 4
)

var MISSION_TYPE_name = map[int32]string{
	1: "GREENER",
	2: "DAILY",
	3: "ELITE",
	4: "TIME_LIMIT",
}
var MISSION_TYPE_value = map[string]int32{
	"GREENER":    1,
	"DAILY":      2,
	"ELITE":      3,
	"TIME_LIMIT": 4,
}

func (x MISSION_TYPE) Enum() *MISSION_TYPE {
	p := new(MISSION_TYPE)
	*p = x
	return p
}
func (x MISSION_TYPE) String() string {
	return proto.EnumName(MISSION_TYPE_name, int32(x))
}
func (x *MISSION_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(MISSION_TYPE_value, data, "MISSION_TYPE")
	if err != nil {
		return err
	}
	*x = MISSION_TYPE(value)
	return nil
}
func (MISSION_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{0} }

// 限时任务子类型
type TIME_LIMIT_MISSION_SUB_TYPE int32

const (
	TIME_LIMIT_MISSION_SUB_TYPE_TIME_LIMIT_GAME_CONTINUOUS_LOGIN TIME_LIMIT_MISSION_SUB_TYPE = 1
	TIME_LIMIT_MISSION_SUB_TYPE_TIME_LIMIT_GAME_ACCUM_LOGIN      TIME_LIMIT_MISSION_SUB_TYPE = 2
	TIME_LIMIT_MISSION_SUB_TYPE_TIME_LIMIT_GAME_ACCUM_RECHARGE   TIME_LIMIT_MISSION_SUB_TYPE = 3
	TIME_LIMIT_MISSION_SUB_TYPE_TIME_LIMIT_CONTINOUS_CHECK_IN    TIME_LIMIT_MISSION_SUB_TYPE = 4
	TIME_LIMIT_MISSION_SUB_TYPE_TIME_LIMIT_HAPPY_CENTER_LEVEL    TIME_LIMIT_MISSION_SUB_TYPE = 5
	TIME_LIMIT_MISSION_SUB_TYPE_TIME_LIMIT_TT_CONTINUOUS_LOGIN   TIME_LIMIT_MISSION_SUB_TYPE = 6
	TIME_LIMIT_MISSION_SUB_TYPE_TIME_LIMIT_TT_ACCUM_LOGIN        TIME_LIMIT_MISSION_SUB_TYPE = 7
	TIME_LIMIT_MISSION_SUB_TYPE_TIME_LIMIT_ACCUM_CHECK_IN        TIME_LIMIT_MISSION_SUB_TYPE = 8
	TIME_LIMIT_MISSION_SUB_TYPE_TIME_LIMIT_GAME_RECHARGE         TIME_LIMIT_MISSION_SUB_TYPE = 9
)

var TIME_LIMIT_MISSION_SUB_TYPE_name = map[int32]string{
	1: "TIME_LIMIT_GAME_CONTINUOUS_LOGIN",
	2: "TIME_LIMIT_GAME_ACCUM_LOGIN",
	3: "TIME_LIMIT_GAME_ACCUM_RECHARGE",
	4: "TIME_LIMIT_CONTINOUS_CHECK_IN",
	5: "TIME_LIMIT_HAPPY_CENTER_LEVEL",
	6: "TIME_LIMIT_TT_CONTINUOUS_LOGIN",
	7: "TIME_LIMIT_TT_ACCUM_LOGIN",
	8: "TIME_LIMIT_ACCUM_CHECK_IN",
	9: "TIME_LIMIT_GAME_RECHARGE",
}
var TIME_LIMIT_MISSION_SUB_TYPE_value = map[string]int32{
	"TIME_LIMIT_GAME_CONTINUOUS_LOGIN": 1,
	"TIME_LIMIT_GAME_ACCUM_LOGIN":      2,
	"TIME_LIMIT_GAME_ACCUM_RECHARGE":   3,
	"TIME_LIMIT_CONTINOUS_CHECK_IN":    4,
	"TIME_LIMIT_HAPPY_CENTER_LEVEL":    5,
	"TIME_LIMIT_TT_CONTINUOUS_LOGIN":   6,
	"TIME_LIMIT_TT_ACCUM_LOGIN":        7,
	"TIME_LIMIT_ACCUM_CHECK_IN":        8,
	"TIME_LIMIT_GAME_RECHARGE":         9,
}

func (x TIME_LIMIT_MISSION_SUB_TYPE) Enum() *TIME_LIMIT_MISSION_SUB_TYPE {
	p := new(TIME_LIMIT_MISSION_SUB_TYPE)
	*p = x
	return p
}
func (x TIME_LIMIT_MISSION_SUB_TYPE) String() string {
	return proto.EnumName(TIME_LIMIT_MISSION_SUB_TYPE_name, int32(x))
}
func (x *TIME_LIMIT_MISSION_SUB_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(TIME_LIMIT_MISSION_SUB_TYPE_value, data, "TIME_LIMIT_MISSION_SUB_TYPE")
	if err != nil {
		return err
	}
	*x = TIME_LIMIT_MISSION_SUB_TYPE(value)
	return nil
}
func (TIME_LIMIT_MISSION_SUB_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{1}
}

// 事件类型
type EVENT_TYPE int32

const (
	EVENT_TYPE_GAME_LOGIN         EVENT_TYPE = 1
	EVENT_TYPE_GAME_RECHARGE      EVENT_TYPE = 2
	EVENT_TYPE_GUILD_CHECK_IN     EVENT_TYPE = 3
	EVENT_TYPE_HAPPY_CENTER_LEVEL EVENT_TYPE = 4
	EVENT_TYPE_TT_LOGIN           EVENT_TYPE = 5
)

var EVENT_TYPE_name = map[int32]string{
	1: "GAME_LOGIN",
	2: "GAME_RECHARGE",
	3: "GUILD_CHECK_IN",
	4: "HAPPY_CENTER_LEVEL",
	5: "TT_LOGIN",
}
var EVENT_TYPE_value = map[string]int32{
	"GAME_LOGIN":         1,
	"GAME_RECHARGE":      2,
	"GUILD_CHECK_IN":     3,
	"HAPPY_CENTER_LEVEL": 4,
	"TT_LOGIN":           5,
}

func (x EVENT_TYPE) Enum() *EVENT_TYPE {
	p := new(EVENT_TYPE)
	*p = x
	return p
}
func (x EVENT_TYPE) String() string {
	return proto.EnumName(EVENT_TYPE_name, int32(x))
}
func (x *EVENT_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(EVENT_TYPE_value, data, "EVENT_TYPE")
	if err != nil {
		return err
	}
	*x = EVENT_TYPE(value)
	return nil
}
func (EVENT_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{2} }

// 任务状态
type MISSION_STATUS int32

const (
	MISSION_STATUS_IN_PROGRESS MISSION_STATUS = 1
	MISSION_STATUS_FINISHED    MISSION_STATUS = 2
	MISSION_STATUS_COLLECTED   MISSION_STATUS = 3
)

var MISSION_STATUS_name = map[int32]string{
	1: "IN_PROGRESS",
	2: "FINISHED",
	3: "COLLECTED",
}
var MISSION_STATUS_value = map[string]int32{
	"IN_PROGRESS": 1,
	"FINISHED":    2,
	"COLLECTED":   3,
}

func (x MISSION_STATUS) Enum() *MISSION_STATUS {
	p := new(MISSION_STATUS)
	*p = x
	return p
}
func (x MISSION_STATUS) String() string {
	return proto.EnumName(MISSION_STATUS_name, int32(x))
}
func (x *MISSION_STATUS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(MISSION_STATUS_value, data, "MISSION_STATUS")
	if err != nil {
		return err
	}
	*x = MISSION_STATUS(value)
	return nil
}
func (MISSION_STATUS) EnumDescriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{3} }

// 通知类型(MASK, 各枚举字段需要用位移计算)
type NOTIFY_MASK int32

const (
	NOTIFY_MASK_NONE      NOTIFY_MASK = 0
	NOTIFY_MASK_RED_POINT NOTIFY_MASK = 1
	NOTIFY_MASK_ALTER     NOTIFY_MASK = 2
)

var NOTIFY_MASK_name = map[int32]string{
	0: "NONE",
	1: "RED_POINT",
	2: "ALTER",
}
var NOTIFY_MASK_value = map[string]int32{
	"NONE":      0,
	"RED_POINT": 1,
	"ALTER":     2,
}

func (x NOTIFY_MASK) Enum() *NOTIFY_MASK {
	p := new(NOTIFY_MASK)
	*p = x
	return p
}
func (x NOTIFY_MASK) String() string {
	return proto.EnumName(NOTIFY_MASK_name, int32(x))
}
func (x *NOTIFY_MASK) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(NOTIFY_MASK_value, data, "NOTIFY_MASK")
	if err != nil {
		return err
	}
	*x = NOTIFY_MASK(value)
	return nil
}
func (NOTIFY_MASK) EnumDescriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{4} }

// 重置进行中的任务的进度的原因
type RESET_MISSION_REASON int32

const (
	RESET_MISSION_REASON_GUILD_QUIT RESET_MISSION_REASON = 1
)

var RESET_MISSION_REASON_name = map[int32]string{
	1: "GUILD_QUIT",
}
var RESET_MISSION_REASON_value = map[string]int32{
	"GUILD_QUIT": 1,
}

func (x RESET_MISSION_REASON) Enum() *RESET_MISSION_REASON {
	p := new(RESET_MISSION_REASON)
	*p = x
	return p
}
func (x RESET_MISSION_REASON) String() string {
	return proto.EnumName(RESET_MISSION_REASON_name, int32(x))
}
func (x *RESET_MISSION_REASON) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RESET_MISSION_REASON_value, data, "RESET_MISSION_REASON")
	if err != nil {
		return err
	}
	*x = RESET_MISSION_REASON(value)
	return nil
}
func (RESET_MISSION_REASON) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{5}
}

type Platform_Values int32

const (
	Platform_Android Platform_Values = 1
	Platform_iOS     Platform_Values = 2
)

var Platform_Values_name = map[int32]string{
	1: "Android",
	2: "iOS",
}
var Platform_Values_value = map[string]int32{
	"Android": 1,
	"iOS":     2,
}

func (x Platform_Values) Enum() *Platform_Values {
	p := new(Platform_Values)
	*p = x
	return p
}
func (x Platform_Values) String() string {
	return proto.EnumName(Platform_Values_name, int32(x))
}
func (x *Platform_Values) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Platform_Values_value, data, "Platform_Values")
	if err != nil {
		return err
	}
	*x = Platform_Values(value)
	return nil
}
func (Platform_Values) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{11, 0}
}

// 任务奖励
type MissionBonus struct {
	Experience  uint32 `protobuf:"varint,1,opt,name=experience" json:"experience"`
	RedDiamonds uint32 `protobuf:"varint,2,opt,name=red_diamonds,json=redDiamonds" json:"red_diamonds"`
	Medal       uint32 `protobuf:"varint,3,opt,name=medal" json:"medal"`
}

func (m *MissionBonus) Reset()                    { *m = MissionBonus{} }
func (m *MissionBonus) String() string            { return proto.CompactTextString(m) }
func (*MissionBonus) ProtoMessage()               {}
func (*MissionBonus) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{0} }

func (m *MissionBonus) GetExperience() uint32 {
	if m != nil {
		return m.Experience
	}
	return 0
}

func (m *MissionBonus) GetRedDiamonds() uint32 {
	if m != nil {
		return m.RedDiamonds
	}
	return 0
}

func (m *MissionBonus) GetMedal() uint32 {
	if m != nil {
		return m.Medal
	}
	return 0
}

type HandleMissionReq struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	CmdId      uint32 `protobuf:"varint,2,req,name=cmd_id,json=cmdId" json:"cmd_id"`
	CmdBody    []byte `protobuf:"bytes,3,req,name=cmd_body,json=cmdBody" json:"cmd_body"`
	ClientType uint32 `protobuf:"varint,4,opt,name=client_type,json=clientType" json:"client_type"`
	ClientVer  uint32 `protobuf:"varint,5,opt,name=client_ver,json=clientVer" json:"client_ver"`
}

func (m *HandleMissionReq) Reset()                    { *m = HandleMissionReq{} }
func (m *HandleMissionReq) String() string            { return proto.CompactTextString(m) }
func (*HandleMissionReq) ProtoMessage()               {}
func (*HandleMissionReq) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{1} }

func (m *HandleMissionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HandleMissionReq) GetCmdId() uint32 {
	if m != nil {
		return m.CmdId
	}
	return 0
}

func (m *HandleMissionReq) GetCmdBody() []byte {
	if m != nil {
		return m.CmdBody
	}
	return nil
}

func (m *HandleMissionReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *HandleMissionReq) GetClientVer() uint32 {
	if m != nil {
		return m.ClientVer
	}
	return 0
}

type HandleMissionResp struct {
}

func (m *HandleMissionResp) Reset()                    { *m = HandleMissionResp{} }
func (m *HandleMissionResp) String() string            { return proto.CompactTextString(m) }
func (*HandleMissionResp) ProtoMessage()               {}
func (*HandleMissionResp) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{2} }

type HandleGuildJoinMissionReq struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *HandleGuildJoinMissionReq) Reset()         { *m = HandleGuildJoinMissionReq{} }
func (m *HandleGuildJoinMissionReq) String() string { return proto.CompactTextString(m) }
func (*HandleGuildJoinMissionReq) ProtoMessage()    {}
func (*HandleGuildJoinMissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{3}
}

func (m *HandleGuildJoinMissionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HandleGuildJoinMissionReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type HandleGuildJoinMissionResp struct {
}

func (m *HandleGuildJoinMissionResp) Reset()         { *m = HandleGuildJoinMissionResp{} }
func (m *HandleGuildJoinMissionResp) String() string { return proto.CompactTextString(m) }
func (*HandleGuildJoinMissionResp) ProtoMessage()    {}
func (*HandleGuildJoinMissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{4}
}

type HandleBindPhoneMissionReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *HandleBindPhoneMissionReq) Reset()         { *m = HandleBindPhoneMissionReq{} }
func (m *HandleBindPhoneMissionReq) String() string { return proto.CompactTextString(m) }
func (*HandleBindPhoneMissionReq) ProtoMessage()    {}
func (*HandleBindPhoneMissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{5}
}

func (m *HandleBindPhoneMissionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type HandleChannelOnlineMissionReq struct {
	Uid         uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId   uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	ChannelType uint32 `protobuf:"varint,3,opt,name=channel_type,json=channelType" json:"channel_type"`
}

func (m *HandleChannelOnlineMissionReq) Reset()         { *m = HandleChannelOnlineMissionReq{} }
func (m *HandleChannelOnlineMissionReq) String() string { return proto.CompactTextString(m) }
func (*HandleChannelOnlineMissionReq) ProtoMessage()    {}
func (*HandleChannelOnlineMissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{6}
}

func (m *HandleChannelOnlineMissionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HandleChannelOnlineMissionReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *HandleChannelOnlineMissionReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type HandleChannelOnlineMissionResp struct {
}

func (m *HandleChannelOnlineMissionResp) Reset()         { *m = HandleChannelOnlineMissionResp{} }
func (m *HandleChannelOnlineMissionResp) String() string { return proto.CompactTextString(m) }
func (*HandleChannelOnlineMissionResp) ProtoMessage()    {}
func (*HandleChannelOnlineMissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{7}
}

type MissionGroup struct {
	GroupId  uint32 `protobuf:"varint,1,req,name=group_id,json=groupId" json:"group_id"`
	Name     string `protobuf:"bytes,2,req,name=name" json:"name"`
	Type     uint32 `protobuf:"varint,3,req,name=type" json:"type"`
	Visible  bool   `protobuf:"varint,4,req,name=visible" json:"visible"`
	Unlocked bool   `protobuf:"varint,5,req,name=unlocked" json:"unlocked"`
}

func (m *MissionGroup) Reset()                    { *m = MissionGroup{} }
func (m *MissionGroup) String() string            { return proto.CompactTextString(m) }
func (*MissionGroup) ProtoMessage()               {}
func (*MissionGroup) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{8} }

func (m *MissionGroup) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *MissionGroup) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MissionGroup) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *MissionGroup) GetVisible() bool {
	if m != nil {
		return m.Visible
	}
	return false
}

func (m *MissionGroup) GetUnlocked() bool {
	if m != nil {
		return m.Unlocked
	}
	return false
}

type Mission struct {
	MissionId        uint32        `protobuf:"varint,1,req,name=mission_id,json=missionId" json:"mission_id"`
	Name             string        `protobuf:"bytes,2,req,name=name" json:"name"`
	StartTime        uint32        `protobuf:"varint,3,req,name=start_time,json=startTime" json:"start_time"`
	ExpireTime       uint32        `protobuf:"varint,4,req,name=expire_time,json=expireTime" json:"expire_time"`
	Bonus            *MissionBonus `protobuf:"bytes,5,req,name=bonus" json:"bonus,omitempty"`
	Status           uint32        `protobuf:"varint,6,req,name=status" json:"status"`
	FinishCount      uint32        `protobuf:"varint,7,req,name=finish_count,json=finishCount" json:"finish_count"`
	RequiredCount    uint32        `protobuf:"varint,8,req,name=required_count,json=requiredCount" json:"required_count"`
	Description      string        `protobuf:"bytes,9,req,name=description" json:"description"`
	Strategy         string        `protobuf:"bytes,10,req,name=strategy" json:"strategy"`
	Identifier       string        `protobuf:"bytes,11,req,name=identifier" json:"identifier"`
	ClientUrl        string        `protobuf:"bytes,12,opt,name=client_url,json=clientUrl" json:"client_url"`
	RewardCount      uint32        `protobuf:"varint,13,opt,name=reward_count,json=rewardCount" json:"reward_count"`
	EffectBegin      uint32        `protobuf:"varint,14,opt,name=effect_begin,json=effectBegin" json:"effect_begin"`
	EffectEnd        uint32        `protobuf:"varint,15,opt,name=effect_end,json=effectEnd" json:"effect_end"`
	IsAccept         uint32        `protobuf:"varint,16,opt,name=is_accept,json=isAccept" json:"is_accept"`
	SubName          string        `protobuf:"bytes,17,opt,name=sub_name,json=subName" json:"sub_name"`
	SpecialBonusName string        `protobuf:"bytes,18,opt,name=special_bonus_name,json=specialBonusName" json:"special_bonus_name"`
	SpecialBonus     *MissionBonus `protobuf:"bytes,19,opt,name=special_bonus,json=specialBonus" json:"special_bonus,omitempty"`
	BaseRewardCount  uint32        `protobuf:"varint,20,opt,name=base_reward_count,json=baseRewardCount" json:"base_reward_count"`
	SubNameType      uint32        `protobuf:"varint,21,opt,name=sub_name_type,json=subNameType" json:"sub_name_type"`
	MaxCollectCount  uint32        `protobuf:"varint,22,opt,name=max_collect_count,json=maxCollectCount" json:"max_collect_count"`
}

func (m *Mission) Reset()                    { *m = Mission{} }
func (m *Mission) String() string            { return proto.CompactTextString(m) }
func (*Mission) ProtoMessage()               {}
func (*Mission) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{9} }

func (m *Mission) GetMissionId() uint32 {
	if m != nil {
		return m.MissionId
	}
	return 0
}

func (m *Mission) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Mission) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *Mission) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *Mission) GetBonus() *MissionBonus {
	if m != nil {
		return m.Bonus
	}
	return nil
}

func (m *Mission) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *Mission) GetFinishCount() uint32 {
	if m != nil {
		return m.FinishCount
	}
	return 0
}

func (m *Mission) GetRequiredCount() uint32 {
	if m != nil {
		return m.RequiredCount
	}
	return 0
}

func (m *Mission) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *Mission) GetStrategy() string {
	if m != nil {
		return m.Strategy
	}
	return ""
}

func (m *Mission) GetIdentifier() string {
	if m != nil {
		return m.Identifier
	}
	return ""
}

func (m *Mission) GetClientUrl() string {
	if m != nil {
		return m.ClientUrl
	}
	return ""
}

func (m *Mission) GetRewardCount() uint32 {
	if m != nil {
		return m.RewardCount
	}
	return 0
}

func (m *Mission) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *Mission) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *Mission) GetIsAccept() uint32 {
	if m != nil {
		return m.IsAccept
	}
	return 0
}

func (m *Mission) GetSubName() string {
	if m != nil {
		return m.SubName
	}
	return ""
}

func (m *Mission) GetSpecialBonusName() string {
	if m != nil {
		return m.SpecialBonusName
	}
	return ""
}

func (m *Mission) GetSpecialBonus() *MissionBonus {
	if m != nil {
		return m.SpecialBonus
	}
	return nil
}

func (m *Mission) GetBaseRewardCount() uint32 {
	if m != nil {
		return m.BaseRewardCount
	}
	return 0
}

func (m *Mission) GetSubNameType() uint32 {
	if m != nil {
		return m.SubNameType
	}
	return 0
}

func (m *Mission) GetMaxCollectCount() uint32 {
	if m != nil {
		return m.MaxCollectCount
	}
	return 0
}

type MissionGroupDetail struct {
	Group       *MissionGroup `protobuf:"bytes,1,req,name=group" json:"group,omitempty"`
	MissionList []*Mission    `protobuf:"bytes,2,rep,name=mission_list,json=missionList" json:"mission_list,omitempty"`
}

func (m *MissionGroupDetail) Reset()                    { *m = MissionGroupDetail{} }
func (m *MissionGroupDetail) String() string            { return proto.CompactTextString(m) }
func (*MissionGroupDetail) ProtoMessage()               {}
func (*MissionGroupDetail) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{10} }

func (m *MissionGroupDetail) GetGroup() *MissionGroup {
	if m != nil {
		return m.Group
	}
	return nil
}

func (m *MissionGroupDetail) GetMissionList() []*Mission {
	if m != nil {
		return m.MissionList
	}
	return nil
}

type Platform struct {
}

func (m *Platform) Reset()                    { *m = Platform{} }
func (m *Platform) String() string            { return proto.CompactTextString(m) }
func (*Platform) ProtoMessage()               {}
func (*Platform) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{11} }

type GetUserMissionsReq struct {
	Uid            uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Platform       uint32 `protobuf:"varint,2,opt,name=platform" json:"platform"`
	ClientVersion  uint32 `protobuf:"varint,3,opt,name=client_version,json=clientVersion" json:"client_version"`
	ShowGameCenter bool   `protobuf:"varint,4,opt,name=show_game_center,json=showGameCenter" json:"show_game_center"`
}

func (m *GetUserMissionsReq) Reset()                    { *m = GetUserMissionsReq{} }
func (m *GetUserMissionsReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserMissionsReq) ProtoMessage()               {}
func (*GetUserMissionsReq) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{12} }

func (m *GetUserMissionsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserMissionsReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *GetUserMissionsReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *GetUserMissionsReq) GetShowGameCenter() bool {
	if m != nil {
		return m.ShowGameCenter
	}
	return false
}

type GetUserMissionsResp struct {
	MissionGroupList []*MissionGroupDetail `protobuf:"bytes,1,rep,name=mission_group_list,json=missionGroupList" json:"mission_group_list,omitempty"`
}

func (m *GetUserMissionsResp) Reset()                    { *m = GetUserMissionsResp{} }
func (m *GetUserMissionsResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserMissionsResp) ProtoMessage()               {}
func (*GetUserMissionsResp) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{13} }

func (m *GetUserMissionsResp) GetMissionGroupList() []*MissionGroupDetail {
	if m != nil {
		return m.MissionGroupList
	}
	return nil
}

type GetLatestUserMissionReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	MissionId uint32 `protobuf:"varint,2,req,name=mission_id,json=missionId" json:"mission_id"`
	Platform  uint32 `protobuf:"varint,3,opt,name=platform" json:"platform"`
}

func (m *GetLatestUserMissionReq) Reset()         { *m = GetLatestUserMissionReq{} }
func (m *GetLatestUserMissionReq) String() string { return proto.CompactTextString(m) }
func (*GetLatestUserMissionReq) ProtoMessage()    {}
func (*GetLatestUserMissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{14}
}

func (m *GetLatestUserMissionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLatestUserMissionReq) GetMissionId() uint32 {
	if m != nil {
		return m.MissionId
	}
	return 0
}

func (m *GetLatestUserMissionReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

type GetLatestUserMissionResp struct {
	MissionGroup *MissionGroupDetail `protobuf:"bytes,1,opt,name=mission_group,json=missionGroup" json:"mission_group,omitempty"`
}

func (m *GetLatestUserMissionResp) Reset()         { *m = GetLatestUserMissionResp{} }
func (m *GetLatestUserMissionResp) String() string { return proto.CompactTextString(m) }
func (*GetLatestUserMissionResp) ProtoMessage()    {}
func (*GetLatestUserMissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{15}
}

func (m *GetLatestUserMissionResp) GetMissionGroup() *MissionGroupDetail {
	if m != nil {
		return m.MissionGroup
	}
	return nil
}

type CollectMissionBonusReq struct {
	Uid               uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	MissionIdentifier string `protobuf:"bytes,2,req,name=mission_identifier,json=missionIdentifier" json:"mission_identifier"`
	Platform          uint32 `protobuf:"varint,3,opt,name=platform" json:"platform"`
	ShowGameCenter    bool   `protobuf:"varint,4,opt,name=show_game_center,json=showGameCenter" json:"show_game_center"`
}

func (m *CollectMissionBonusReq) Reset()         { *m = CollectMissionBonusReq{} }
func (m *CollectMissionBonusReq) String() string { return proto.CompactTextString(m) }
func (*CollectMissionBonusReq) ProtoMessage()    {}
func (*CollectMissionBonusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{16}
}

func (m *CollectMissionBonusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CollectMissionBonusReq) GetMissionIdentifier() string {
	if m != nil {
		return m.MissionIdentifier
	}
	return ""
}

func (m *CollectMissionBonusReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *CollectMissionBonusReq) GetShowGameCenter() bool {
	if m != nil {
		return m.ShowGameCenter
	}
	return false
}

type CollectMissionBonusResp struct {
	GrowInfo      *GrowInfo     `protobuf:"bytes,1,req,name=grow_info,json=growInfo" json:"grow_info,omitempty"`
	BaseBonus     *MissionBonus `protobuf:"bytes,2,req,name=base_bonus,json=baseBonus" json:"base_bonus,omitempty"`
	MedalBuffList []*MedalBuff  `protobuf:"bytes,3,rep,name=medal_buff_list,json=medalBuffList" json:"medal_buff_list,omitempty"`
	NewMedalId    uint32        `protobuf:"varint,4,opt,name=new_medal_id,json=newMedalId" json:"new_medal_id"`
	MissionId     uint32        `protobuf:"varint,5,opt,name=mission_id,json=missionId" json:"mission_id"`
	FinalBonus    *MissionBonus `protobuf:"bytes,6,opt,name=final_bonus,json=finalBonus" json:"final_bonus,omitempty"`
	NoBuffBonus   *MissionBonus `protobuf:"bytes,7,opt,name=no_buff_bonus,json=noBuffBonus" json:"no_buff_bonus,omitempty"`
}

func (m *CollectMissionBonusResp) Reset()         { *m = CollectMissionBonusResp{} }
func (m *CollectMissionBonusResp) String() string { return proto.CompactTextString(m) }
func (*CollectMissionBonusResp) ProtoMessage()    {}
func (*CollectMissionBonusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{17}
}

func (m *CollectMissionBonusResp) GetGrowInfo() *GrowInfo {
	if m != nil {
		return m.GrowInfo
	}
	return nil
}

func (m *CollectMissionBonusResp) GetBaseBonus() *MissionBonus {
	if m != nil {
		return m.BaseBonus
	}
	return nil
}

func (m *CollectMissionBonusResp) GetMedalBuffList() []*MedalBuff {
	if m != nil {
		return m.MedalBuffList
	}
	return nil
}

func (m *CollectMissionBonusResp) GetNewMedalId() uint32 {
	if m != nil {
		return m.NewMedalId
	}
	return 0
}

func (m *CollectMissionBonusResp) GetMissionId() uint32 {
	if m != nil {
		return m.MissionId
	}
	return 0
}

func (m *CollectMissionBonusResp) GetFinalBonus() *MissionBonus {
	if m != nil {
		return m.FinalBonus
	}
	return nil
}

func (m *CollectMissionBonusResp) GetNoBuffBonus() *MissionBonus {
	if m != nil {
		return m.NoBuffBonus
	}
	return nil
}

// 成长信息
type GrowInfo struct {
	Exp                int32  `protobuf:"varint,1,req,name=exp" json:"exp"`
	Level              uint32 `protobuf:"varint,2,req,name=level" json:"level"`
	CurrentLevelExpMin uint32 `protobuf:"varint,3,req,name=current_level_exp_min,json=currentLevelExpMin" json:"current_level_exp_min"`
	CurrentLevelExpMax uint32 `protobuf:"varint,4,req,name=current_level_exp_max,json=currentLevelExpMax" json:"current_level_exp_max"`
	Currency           int32  `protobuf:"varint,5,req,name=currency" json:"currency"`
}

func (m *GrowInfo) Reset()                    { *m = GrowInfo{} }
func (m *GrowInfo) String() string            { return proto.CompactTextString(m) }
func (*GrowInfo) ProtoMessage()               {}
func (*GrowInfo) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{18} }

func (m *GrowInfo) GetExp() int32 {
	if m != nil {
		return m.Exp
	}
	return 0
}

func (m *GrowInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GrowInfo) GetCurrentLevelExpMin() uint32 {
	if m != nil {
		return m.CurrentLevelExpMin
	}
	return 0
}

func (m *GrowInfo) GetCurrentLevelExpMax() uint32 {
	if m != nil {
		return m.CurrentLevelExpMax
	}
	return 0
}

func (m *GrowInfo) GetCurrency() int32 {
	if m != nil {
		return m.Currency
	}
	return 0
}

type GetUserMissionGuideReq struct {
	Uid            uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Platform       uint32 `protobuf:"varint,2,opt,name=platform" json:"platform"`
	ShowGameCenter bool   `protobuf:"varint,3,opt,name=show_game_center,json=showGameCenter" json:"show_game_center"`
}

func (m *GetUserMissionGuideReq) Reset()         { *m = GetUserMissionGuideReq{} }
func (m *GetUserMissionGuideReq) String() string { return proto.CompactTextString(m) }
func (*GetUserMissionGuideReq) ProtoMessage()    {}
func (*GetUserMissionGuideReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{19}
}

func (m *GetUserMissionGuideReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserMissionGuideReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *GetUserMissionGuideReq) GetShowGameCenter() bool {
	if m != nil {
		return m.ShowGameCenter
	}
	return false
}

type GetUserMissionGuideResp struct {
	Guide string `protobuf:"bytes,1,req,name=guide" json:"guide"`
}

func (m *GetUserMissionGuideResp) Reset()         { *m = GetUserMissionGuideResp{} }
func (m *GetUserMissionGuideResp) String() string { return proto.CompactTextString(m) }
func (*GetUserMissionGuideResp) ProtoMessage()    {}
func (*GetUserMissionGuideResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{20}
}

func (m *GetUserMissionGuideResp) GetGuide() string {
	if m != nil {
		return m.Guide
	}
	return ""
}

// 加成比例, 需要除10000
type BuffRatio struct {
	Experience  uint32 `protobuf:"varint,1,req,name=experience" json:"experience"`
	RedDiamonds uint32 `protobuf:"varint,2,req,name=red_diamonds,json=redDiamonds" json:"red_diamonds"`
}

func (m *BuffRatio) Reset()                    { *m = BuffRatio{} }
func (m *BuffRatio) String() string            { return proto.CompactTextString(m) }
func (*BuffRatio) ProtoMessage()               {}
func (*BuffRatio) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{21} }

func (m *BuffRatio) GetExperience() uint32 {
	if m != nil {
		return m.Experience
	}
	return 0
}

func (m *BuffRatio) GetRedDiamonds() uint32 {
	if m != nil {
		return m.RedDiamonds
	}
	return 0
}

// 加成数量
type Buff struct {
	Experience  uint32 `protobuf:"varint,1,req,name=experience" json:"experience"`
	RedDiamonds uint32 `protobuf:"varint,2,req,name=red_diamonds,json=redDiamonds" json:"red_diamonds"`
}

func (m *Buff) Reset()                    { *m = Buff{} }
func (m *Buff) String() string            { return proto.CompactTextString(m) }
func (*Buff) ProtoMessage()               {}
func (*Buff) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{22} }

func (m *Buff) GetExperience() uint32 {
	if m != nil {
		return m.Experience
	}
	return 0
}

func (m *Buff) GetRedDiamonds() uint32 {
	if m != nil {
		return m.RedDiamonds
	}
	return 0
}

type MedalBuffRatio struct {
	MedalId   uint32     `protobuf:"varint,1,req,name=medal_id,json=medalId" json:"medal_id"`
	BuffRatio *BuffRatio `protobuf:"bytes,2,req,name=buff_ratio,json=buffRatio" json:"buff_ratio,omitempty"`
}

func (m *MedalBuffRatio) Reset()                    { *m = MedalBuffRatio{} }
func (m *MedalBuffRatio) String() string            { return proto.CompactTextString(m) }
func (*MedalBuffRatio) ProtoMessage()               {}
func (*MedalBuffRatio) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{23} }

func (m *MedalBuffRatio) GetMedalId() uint32 {
	if m != nil {
		return m.MedalId
	}
	return 0
}

func (m *MedalBuffRatio) GetBuffRatio() *BuffRatio {
	if m != nil {
		return m.BuffRatio
	}
	return nil
}

type MedalBuff struct {
	MedalId uint32 `protobuf:"varint,1,req,name=medal_id,json=medalId" json:"medal_id"`
	Buff    *Buff  `protobuf:"bytes,2,req,name=buff" json:"buff,omitempty"`
}

func (m *MedalBuff) Reset()                    { *m = MedalBuff{} }
func (m *MedalBuff) String() string            { return proto.CompactTextString(m) }
func (*MedalBuff) ProtoMessage()               {}
func (*MedalBuff) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{24} }

func (m *MedalBuff) GetMedalId() uint32 {
	if m != nil {
		return m.MedalId
	}
	return 0
}

func (m *MedalBuff) GetBuff() *Buff {
	if m != nil {
		return m.Buff
	}
	return nil
}

type StaticMission struct {
	MissionId        uint32        `protobuf:"varint,1,req,name=mission_id,json=missionId" json:"mission_id"`
	Name             string        `protobuf:"bytes,2,req,name=name" json:"name"`
	Description      string        `protobuf:"bytes,3,req,name=description" json:"description"`
	EffectBegin      uint32        `protobuf:"varint,4,req,name=effect_begin,json=effectBegin" json:"effect_begin"`
	EffectEnd        uint32        `protobuf:"varint,5,req,name=effect_end,json=effectEnd" json:"effect_end"`
	MinClientVersion uint32        `protobuf:"varint,6,req,name=min_client_version,json=minClientVersion" json:"min_client_version"`
	AddTime          uint32        `protobuf:"varint,7,req,name=add_time,json=addTime" json:"add_time"`
	Bonus            *MissionBonus `protobuf:"bytes,8,req,name=bonus" json:"bonus,omitempty"`
	ResetTime        uint32        `protobuf:"varint,9,opt,name=reset_time,json=resetTime" json:"reset_time"`
	EventType        uint32        `protobuf:"varint,10,opt,name=event_type,json=eventType" json:"event_type"`
	SubType          uint32        `protobuf:"varint,11,opt,name=sub_type,json=subType" json:"sub_type"`
	LyGameIdList     []uint64      `protobuf:"varint,12,rep,name=ly_game_id_list,json=lyGameIdList" json:"ly_game_id_list,omitempty"`
	SubName          string        `protobuf:"bytes,13,opt,name=sub_name,json=subName" json:"sub_name"`
	RechargeCount    uint32        `protobuf:"varint,14,opt,name=recharge_count,json=rechargeCount" json:"recharge_count"`
	SpecialBonusName string        `protobuf:"bytes,15,opt,name=special_bonus_name,json=specialBonusName" json:"special_bonus_name"`
	SpecialBonus     *MissionBonus `protobuf:"bytes,16,opt,name=special_bonus,json=specialBonus" json:"special_bonus,omitempty"`
	SubNameType      uint32        `protobuf:"varint,17,opt,name=sub_name_type,json=subNameType" json:"sub_name_type"`
	Platform         uint32        `protobuf:"varint,18,opt,name=platform" json:"platform"`
}

func (m *StaticMission) Reset()                    { *m = StaticMission{} }
func (m *StaticMission) String() string            { return proto.CompactTextString(m) }
func (*StaticMission) ProtoMessage()               {}
func (*StaticMission) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{25} }

func (m *StaticMission) GetMissionId() uint32 {
	if m != nil {
		return m.MissionId
	}
	return 0
}

func (m *StaticMission) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StaticMission) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *StaticMission) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *StaticMission) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *StaticMission) GetMinClientVersion() uint32 {
	if m != nil {
		return m.MinClientVersion
	}
	return 0
}

func (m *StaticMission) GetAddTime() uint32 {
	if m != nil {
		return m.AddTime
	}
	return 0
}

func (m *StaticMission) GetBonus() *MissionBonus {
	if m != nil {
		return m.Bonus
	}
	return nil
}

func (m *StaticMission) GetResetTime() uint32 {
	if m != nil {
		return m.ResetTime
	}
	return 0
}

func (m *StaticMission) GetEventType() uint32 {
	if m != nil {
		return m.EventType
	}
	return 0
}

func (m *StaticMission) GetSubType() uint32 {
	if m != nil {
		return m.SubType
	}
	return 0
}

func (m *StaticMission) GetLyGameIdList() []uint64 {
	if m != nil {
		return m.LyGameIdList
	}
	return nil
}

func (m *StaticMission) GetSubName() string {
	if m != nil {
		return m.SubName
	}
	return ""
}

func (m *StaticMission) GetRechargeCount() uint32 {
	if m != nil {
		return m.RechargeCount
	}
	return 0
}

func (m *StaticMission) GetSpecialBonusName() string {
	if m != nil {
		return m.SpecialBonusName
	}
	return ""
}

func (m *StaticMission) GetSpecialBonus() *MissionBonus {
	if m != nil {
		return m.SpecialBonus
	}
	return nil
}

func (m *StaticMission) GetSubNameType() uint32 {
	if m != nil {
		return m.SubNameType
	}
	return 0
}

func (m *StaticMission) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

type StaticMissionGroup struct {
	GroupId     uint32           `protobuf:"varint,1,req,name=group_id,json=groupId" json:"group_id"`
	Name        string           `protobuf:"bytes,2,req,name=name" json:"name"`
	Type        uint32           `protobuf:"varint,3,req,name=type" json:"type"`
	MissionList []*StaticMission `protobuf:"bytes,4,rep,name=mission_list,json=missionList" json:"mission_list,omitempty"`
}

func (m *StaticMissionGroup) Reset()                    { *m = StaticMissionGroup{} }
func (m *StaticMissionGroup) String() string            { return proto.CompactTextString(m) }
func (*StaticMissionGroup) ProtoMessage()               {}
func (*StaticMissionGroup) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{26} }

func (m *StaticMissionGroup) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *StaticMissionGroup) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StaticMissionGroup) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *StaticMissionGroup) GetMissionList() []*StaticMission {
	if m != nil {
		return m.MissionList
	}
	return nil
}

type StaticMissionConfig struct {
	UpdateTime       uint32                `protobuf:"varint,1,req,name=update_time,json=updateTime" json:"update_time"`
	MissionGroupList []*StaticMissionGroup `protobuf:"bytes,2,rep,name=mission_group_list,json=missionGroupList" json:"mission_group_list,omitempty"`
}

func (m *StaticMissionConfig) Reset()                    { *m = StaticMissionConfig{} }
func (m *StaticMissionConfig) String() string            { return proto.CompactTextString(m) }
func (*StaticMissionConfig) ProtoMessage()               {}
func (*StaticMissionConfig) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{27} }

func (m *StaticMissionConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *StaticMissionConfig) GetMissionGroupList() []*StaticMissionGroup {
	if m != nil {
		return m.MissionGroupList
	}
	return nil
}

type GetStaticMissionReq struct {
	MissionId uint32 `protobuf:"varint,1,req,name=mission_id,json=missionId" json:"mission_id"`
}

func (m *GetStaticMissionReq) Reset()                    { *m = GetStaticMissionReq{} }
func (m *GetStaticMissionReq) String() string            { return proto.CompactTextString(m) }
func (*GetStaticMissionReq) ProtoMessage()               {}
func (*GetStaticMissionReq) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{28} }

func (m *GetStaticMissionReq) GetMissionId() uint32 {
	if m != nil {
		return m.MissionId
	}
	return 0
}

type GetStaticMissionResp struct {
	Mission *StaticMission `protobuf:"bytes,1,req,name=mission" json:"mission,omitempty"`
}

func (m *GetStaticMissionResp) Reset()         { *m = GetStaticMissionResp{} }
func (m *GetStaticMissionResp) String() string { return proto.CompactTextString(m) }
func (*GetStaticMissionResp) ProtoMessage()    {}
func (*GetStaticMissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{29}
}

func (m *GetStaticMissionResp) GetMission() *StaticMission {
	if m != nil {
		return m.Mission
	}
	return nil
}

type IncreaseMissionFinishCountReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	MissionId uint32 `protobuf:"varint,2,req,name=mission_id,json=missionId" json:"mission_id"`
	Count     uint32 `protobuf:"varint,3,req,name=count" json:"count"`
}

func (m *IncreaseMissionFinishCountReq) Reset()         { *m = IncreaseMissionFinishCountReq{} }
func (m *IncreaseMissionFinishCountReq) String() string { return proto.CompactTextString(m) }
func (*IncreaseMissionFinishCountReq) ProtoMessage()    {}
func (*IncreaseMissionFinishCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{30}
}

func (m *IncreaseMissionFinishCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *IncreaseMissionFinishCountReq) GetMissionId() uint32 {
	if m != nil {
		return m.MissionId
	}
	return 0
}

func (m *IncreaseMissionFinishCountReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type ResetInProgressMissionReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	MissionId uint32 `protobuf:"varint,2,opt,name=mission_id,json=missionId" json:"mission_id"`
	Type      uint32 `protobuf:"varint,3,opt,name=type" json:"type"`
}

func (m *ResetInProgressMissionReq) Reset()         { *m = ResetInProgressMissionReq{} }
func (m *ResetInProgressMissionReq) String() string { return proto.CompactTextString(m) }
func (*ResetInProgressMissionReq) ProtoMessage()    {}
func (*ResetInProgressMissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{31}
}

func (m *ResetInProgressMissionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ResetInProgressMissionReq) GetMissionId() uint32 {
	if m != nil {
		return m.MissionId
	}
	return 0
}

func (m *ResetInProgressMissionReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

// 限时任务相关的东东，已废弃
type HandleTimeLimitMissionReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	MissionId uint32 `protobuf:"varint,2,req,name=mission_id,json=missionId" json:"mission_id"`
	Extend    string `protobuf:"bytes,3,opt,name=extend" json:"extend"`
}

func (m *HandleTimeLimitMissionReq) Reset()         { *m = HandleTimeLimitMissionReq{} }
func (m *HandleTimeLimitMissionReq) String() string { return proto.CompactTextString(m) }
func (*HandleTimeLimitMissionReq) ProtoMessage()    {}
func (*HandleTimeLimitMissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{32}
}

func (m *HandleTimeLimitMissionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HandleTimeLimitMissionReq) GetMissionId() uint32 {
	if m != nil {
		return m.MissionId
	}
	return 0
}

func (m *HandleTimeLimitMissionReq) GetExtend() string {
	if m != nil {
		return m.Extend
	}
	return ""
}

type HandleTimeLimitMissionResp struct {
}

func (m *HandleTimeLimitMissionResp) Reset()         { *m = HandleTimeLimitMissionResp{} }
func (m *HandleTimeLimitMissionResp) String() string { return proto.CompactTextString(m) }
func (*HandleTimeLimitMissionResp) ProtoMessage()    {}
func (*HandleTimeLimitMissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{33}
}

type AcceptMissionReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	MissionId uint32 `protobuf:"varint,2,req,name=mission_id,json=missionId" json:"mission_id"`
	Platform  uint32 `protobuf:"varint,3,opt,name=platform" json:"platform"`
}

func (m *AcceptMissionReq) Reset()                    { *m = AcceptMissionReq{} }
func (m *AcceptMissionReq) String() string            { return proto.CompactTextString(m) }
func (*AcceptMissionReq) ProtoMessage()               {}
func (*AcceptMissionReq) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{34} }

func (m *AcceptMissionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AcceptMissionReq) GetMissionId() uint32 {
	if m != nil {
		return m.MissionId
	}
	return 0
}

func (m *AcceptMissionReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

type GetStaticTimeLimitGameMissionReq struct {
	LastUpdateTime uint32 `protobuf:"varint,1,opt,name=last_update_time,json=lastUpdateTime" json:"last_update_time"`
	EventType      uint32 `protobuf:"varint,2,opt,name=event_type,json=eventType" json:"event_type"`
}

func (m *GetStaticTimeLimitGameMissionReq) Reset()         { *m = GetStaticTimeLimitGameMissionReq{} }
func (m *GetStaticTimeLimitGameMissionReq) String() string { return proto.CompactTextString(m) }
func (*GetStaticTimeLimitGameMissionReq) ProtoMessage()    {}
func (*GetStaticTimeLimitGameMissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{35}
}

func (m *GetStaticTimeLimitGameMissionReq) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func (m *GetStaticTimeLimitGameMissionReq) GetEventType() uint32 {
	if m != nil {
		return m.EventType
	}
	return 0
}

type GetStaticTimeLimitGameMissionResp struct {
	UpdateTime  uint32           `protobuf:"varint,1,req,name=update_time,json=updateTime" json:"update_time"`
	MissionList []*StaticMission `protobuf:"bytes,2,rep,name=mission_list,json=missionList" json:"mission_list,omitempty"`
}

func (m *GetStaticTimeLimitGameMissionResp) Reset()         { *m = GetStaticTimeLimitGameMissionResp{} }
func (m *GetStaticTimeLimitGameMissionResp) String() string { return proto.CompactTextString(m) }
func (*GetStaticTimeLimitGameMissionResp) ProtoMessage()    {}
func (*GetStaticTimeLimitGameMissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{36}
}

func (m *GetStaticTimeLimitGameMissionResp) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *GetStaticTimeLimitGameMissionResp) GetMissionList() []*StaticMission {
	if m != nil {
		return m.MissionList
	}
	return nil
}

type HandleEventReq struct {
	Uid               uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	EventType         uint32 `protobuf:"varint,2,req,name=event_type,json=eventType" json:"event_type"`
	LyGameId          uint64 `protobuf:"varint,3,opt,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	RechargePenny     uint32 `protobuf:"varint,4,opt,name=recharge_penny,json=rechargePenny" json:"recharge_penny"`
	Level             uint32 `protobuf:"varint,5,opt,name=level" json:"level"`
	CashRechargePenny uint32 `protobuf:"varint,6,opt,name=cash_recharge_penny,json=cashRechargePenny" json:"cash_recharge_penny"`
}

func (m *HandleEventReq) Reset()                    { *m = HandleEventReq{} }
func (m *HandleEventReq) String() string            { return proto.CompactTextString(m) }
func (*HandleEventReq) ProtoMessage()               {}
func (*HandleEventReq) Descriptor() ([]byte, []int) { return fileDescriptorMissionlogic, []int{37} }

func (m *HandleEventReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HandleEventReq) GetEventType() uint32 {
	if m != nil {
		return m.EventType
	}
	return 0
}

func (m *HandleEventReq) GetLyGameId() uint64 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *HandleEventReq) GetRechargePenny() uint32 {
	if m != nil {
		return m.RechargePenny
	}
	return 0
}

func (m *HandleEventReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *HandleEventReq) GetCashRechargePenny() uint32 {
	if m != nil {
		return m.CashRechargePenny
	}
	return 0
}

type HandleEnterChannelFromRankMissionReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *HandleEnterChannelFromRankMissionReq) Reset()         { *m = HandleEnterChannelFromRankMissionReq{} }
func (m *HandleEnterChannelFromRankMissionReq) String() string { return proto.CompactTextString(m) }
func (*HandleEnterChannelFromRankMissionReq) ProtoMessage()    {}
func (*HandleEnterChannelFromRankMissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{38}
}

func (m *HandleEnterChannelFromRankMissionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type HandleEnterChannelFromRankMissionResp struct {
}

func (m *HandleEnterChannelFromRankMissionResp) Reset()         { *m = HandleEnterChannelFromRankMissionResp{} }
func (m *HandleEnterChannelFromRankMissionResp) String() string { return proto.CompactTextString(m) }
func (*HandleEnterChannelFromRankMissionResp) ProtoMessage()    {}
func (*HandleEnterChannelFromRankMissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMissionlogic, []int{39}
}

func init() {
	proto.RegisterType((*MissionBonus)(nil), "MissionLogic.MissionBonus")
	proto.RegisterType((*HandleMissionReq)(nil), "MissionLogic.HandleMissionReq")
	proto.RegisterType((*HandleMissionResp)(nil), "MissionLogic.HandleMissionResp")
	proto.RegisterType((*HandleGuildJoinMissionReq)(nil), "MissionLogic.HandleGuildJoinMissionReq")
	proto.RegisterType((*HandleGuildJoinMissionResp)(nil), "MissionLogic.HandleGuildJoinMissionResp")
	proto.RegisterType((*HandleBindPhoneMissionReq)(nil), "MissionLogic.HandleBindPhoneMissionReq")
	proto.RegisterType((*HandleChannelOnlineMissionReq)(nil), "MissionLogic.HandleChannelOnlineMissionReq")
	proto.RegisterType((*HandleChannelOnlineMissionResp)(nil), "MissionLogic.HandleChannelOnlineMissionResp")
	proto.RegisterType((*MissionGroup)(nil), "MissionLogic.MissionGroup")
	proto.RegisterType((*Mission)(nil), "MissionLogic.Mission")
	proto.RegisterType((*MissionGroupDetail)(nil), "MissionLogic.MissionGroupDetail")
	proto.RegisterType((*Platform)(nil), "MissionLogic.Platform")
	proto.RegisterType((*GetUserMissionsReq)(nil), "MissionLogic.GetUserMissionsReq")
	proto.RegisterType((*GetUserMissionsResp)(nil), "MissionLogic.GetUserMissionsResp")
	proto.RegisterType((*GetLatestUserMissionReq)(nil), "MissionLogic.GetLatestUserMissionReq")
	proto.RegisterType((*GetLatestUserMissionResp)(nil), "MissionLogic.GetLatestUserMissionResp")
	proto.RegisterType((*CollectMissionBonusReq)(nil), "MissionLogic.CollectMissionBonusReq")
	proto.RegisterType((*CollectMissionBonusResp)(nil), "MissionLogic.CollectMissionBonusResp")
	proto.RegisterType((*GrowInfo)(nil), "MissionLogic.GrowInfo")
	proto.RegisterType((*GetUserMissionGuideReq)(nil), "MissionLogic.GetUserMissionGuideReq")
	proto.RegisterType((*GetUserMissionGuideResp)(nil), "MissionLogic.GetUserMissionGuideResp")
	proto.RegisterType((*BuffRatio)(nil), "MissionLogic.BuffRatio")
	proto.RegisterType((*Buff)(nil), "MissionLogic.Buff")
	proto.RegisterType((*MedalBuffRatio)(nil), "MissionLogic.MedalBuffRatio")
	proto.RegisterType((*MedalBuff)(nil), "MissionLogic.MedalBuff")
	proto.RegisterType((*StaticMission)(nil), "MissionLogic.StaticMission")
	proto.RegisterType((*StaticMissionGroup)(nil), "MissionLogic.StaticMissionGroup")
	proto.RegisterType((*StaticMissionConfig)(nil), "MissionLogic.StaticMissionConfig")
	proto.RegisterType((*GetStaticMissionReq)(nil), "MissionLogic.GetStaticMissionReq")
	proto.RegisterType((*GetStaticMissionResp)(nil), "MissionLogic.GetStaticMissionResp")
	proto.RegisterType((*IncreaseMissionFinishCountReq)(nil), "MissionLogic.IncreaseMissionFinishCountReq")
	proto.RegisterType((*ResetInProgressMissionReq)(nil), "MissionLogic.ResetInProgressMissionReq")
	proto.RegisterType((*HandleTimeLimitMissionReq)(nil), "MissionLogic.HandleTimeLimitMissionReq")
	proto.RegisterType((*HandleTimeLimitMissionResp)(nil), "MissionLogic.HandleTimeLimitMissionResp")
	proto.RegisterType((*AcceptMissionReq)(nil), "MissionLogic.AcceptMissionReq")
	proto.RegisterType((*GetStaticTimeLimitGameMissionReq)(nil), "MissionLogic.GetStaticTimeLimitGameMissionReq")
	proto.RegisterType((*GetStaticTimeLimitGameMissionResp)(nil), "MissionLogic.GetStaticTimeLimitGameMissionResp")
	proto.RegisterType((*HandleEventReq)(nil), "MissionLogic.HandleEventReq")
	proto.RegisterType((*HandleEnterChannelFromRankMissionReq)(nil), "MissionLogic.HandleEnterChannelFromRankMissionReq")
	proto.RegisterType((*HandleEnterChannelFromRankMissionResp)(nil), "MissionLogic.HandleEnterChannelFromRankMissionResp")
	proto.RegisterEnum("MissionLogic.MISSION_TYPE", MISSION_TYPE_name, MISSION_TYPE_value)
	proto.RegisterEnum("MissionLogic.TIME_LIMIT_MISSION_SUB_TYPE", TIME_LIMIT_MISSION_SUB_TYPE_name, TIME_LIMIT_MISSION_SUB_TYPE_value)
	proto.RegisterEnum("MissionLogic.EVENT_TYPE", EVENT_TYPE_name, EVENT_TYPE_value)
	proto.RegisterEnum("MissionLogic.MISSION_STATUS", MISSION_STATUS_name, MISSION_STATUS_value)
	proto.RegisterEnum("MissionLogic.NOTIFY_MASK", NOTIFY_MASK_name, NOTIFY_MASK_value)
	proto.RegisterEnum("MissionLogic.RESET_MISSION_REASON", RESET_MISSION_REASON_name, RESET_MISSION_REASON_value)
	proto.RegisterEnum("MissionLogic.Platform_Values", Platform_Values_name, Platform_Values_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for MissionLogic service

type MissionLogicClient interface {
	HandleMission(ctx context.Context, in *HandleMissionReq, opts ...grpc.CallOption) (*HandleMissionResp, error)
	GetUserMissions(ctx context.Context, in *GetUserMissionsReq, opts ...grpc.CallOption) (*GetUserMissionsResp, error)
	CollectMissionBonus(ctx context.Context, in *CollectMissionBonusReq, opts ...grpc.CallOption) (*CollectMissionBonusResp, error)
	GetUserMissionGuide(ctx context.Context, in *GetUserMissionGuideReq, opts ...grpc.CallOption) (*GetUserMissionGuideResp, error)
	GetStaticMissionConfig(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*StaticMissionConfig, error)
	GetStaticMission(ctx context.Context, in *GetStaticMissionReq, opts ...grpc.CallOption) (*GetStaticMissionResp, error)
	HandleGuildJoinMission(ctx context.Context, in *HandleGuildJoinMissionReq, opts ...grpc.CallOption) (*HandleGuildJoinMissionResp, error)
	IncreaseMissionFinishCount(ctx context.Context, in *IncreaseMissionFinishCountReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	HandleBindPhoneMission(ctx context.Context, in *HandleBindPhoneMissionReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetLatestUserMission(ctx context.Context, in *GetLatestUserMissionReq, opts ...grpc.CallOption) (*GetLatestUserMissionResp, error)
	AcceptMission(ctx context.Context, in *AcceptMissionReq, opts ...grpc.CallOption) (*GetLatestUserMissionResp, error)
	HandleTimeLimitMission(ctx context.Context, in *HandleTimeLimitMissionReq, opts ...grpc.CallOption) (*HandleTimeLimitMissionResp, error)
	GetStaticTimeLimitGameMission(ctx context.Context, in *GetStaticTimeLimitGameMissionReq, opts ...grpc.CallOption) (*GetStaticTimeLimitGameMissionResp, error)
	HandleEvent(ctx context.Context, in *HandleEventReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	ResetInProgressMission(ctx context.Context, in *ResetInProgressMissionReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	HandleChannelOnlineMission(ctx context.Context, in *HandleChannelOnlineMissionReq, opts ...grpc.CallOption) (*HandleChannelOnlineMissionResp, error)
	HandleEnterChannelFromRankMission(ctx context.Context, in *HandleEnterChannelFromRankMissionReq, opts ...grpc.CallOption) (*HandleEnterChannelFromRankMissionResp, error)
}

type missionLogicClient struct {
	cc *grpc.ClientConn
}

func NewMissionLogicClient(cc *grpc.ClientConn) MissionLogicClient {
	return &missionLogicClient{cc}
}

func (c *missionLogicClient) HandleMission(ctx context.Context, in *HandleMissionReq, opts ...grpc.CallOption) (*HandleMissionResp, error) {
	out := new(HandleMissionResp)
	err := grpc.Invoke(ctx, "/MissionLogic.MissionLogic/HandleMission", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionLogicClient) GetUserMissions(ctx context.Context, in *GetUserMissionsReq, opts ...grpc.CallOption) (*GetUserMissionsResp, error) {
	out := new(GetUserMissionsResp)
	err := grpc.Invoke(ctx, "/MissionLogic.MissionLogic/GetUserMissions", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionLogicClient) CollectMissionBonus(ctx context.Context, in *CollectMissionBonusReq, opts ...grpc.CallOption) (*CollectMissionBonusResp, error) {
	out := new(CollectMissionBonusResp)
	err := grpc.Invoke(ctx, "/MissionLogic.MissionLogic/CollectMissionBonus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionLogicClient) GetUserMissionGuide(ctx context.Context, in *GetUserMissionGuideReq, opts ...grpc.CallOption) (*GetUserMissionGuideResp, error) {
	out := new(GetUserMissionGuideResp)
	err := grpc.Invoke(ctx, "/MissionLogic.MissionLogic/GetUserMissionGuide", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionLogicClient) GetStaticMissionConfig(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*StaticMissionConfig, error) {
	out := new(StaticMissionConfig)
	err := grpc.Invoke(ctx, "/MissionLogic.MissionLogic/GetStaticMissionConfig", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionLogicClient) GetStaticMission(ctx context.Context, in *GetStaticMissionReq, opts ...grpc.CallOption) (*GetStaticMissionResp, error) {
	out := new(GetStaticMissionResp)
	err := grpc.Invoke(ctx, "/MissionLogic.MissionLogic/GetStaticMission", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionLogicClient) HandleGuildJoinMission(ctx context.Context, in *HandleGuildJoinMissionReq, opts ...grpc.CallOption) (*HandleGuildJoinMissionResp, error) {
	out := new(HandleGuildJoinMissionResp)
	err := grpc.Invoke(ctx, "/MissionLogic.MissionLogic/HandleGuildJoinMission", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionLogicClient) IncreaseMissionFinishCount(ctx context.Context, in *IncreaseMissionFinishCountReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/MissionLogic.MissionLogic/IncreaseMissionFinishCount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionLogicClient) HandleBindPhoneMission(ctx context.Context, in *HandleBindPhoneMissionReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/MissionLogic.MissionLogic/HandleBindPhoneMission", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionLogicClient) GetLatestUserMission(ctx context.Context, in *GetLatestUserMissionReq, opts ...grpc.CallOption) (*GetLatestUserMissionResp, error) {
	out := new(GetLatestUserMissionResp)
	err := grpc.Invoke(ctx, "/MissionLogic.MissionLogic/GetLatestUserMission", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionLogicClient) AcceptMission(ctx context.Context, in *AcceptMissionReq, opts ...grpc.CallOption) (*GetLatestUserMissionResp, error) {
	out := new(GetLatestUserMissionResp)
	err := grpc.Invoke(ctx, "/MissionLogic.MissionLogic/AcceptMission", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionLogicClient) HandleTimeLimitMission(ctx context.Context, in *HandleTimeLimitMissionReq, opts ...grpc.CallOption) (*HandleTimeLimitMissionResp, error) {
	out := new(HandleTimeLimitMissionResp)
	err := grpc.Invoke(ctx, "/MissionLogic.MissionLogic/HandleTimeLimitMission", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionLogicClient) GetStaticTimeLimitGameMission(ctx context.Context, in *GetStaticTimeLimitGameMissionReq, opts ...grpc.CallOption) (*GetStaticTimeLimitGameMissionResp, error) {
	out := new(GetStaticTimeLimitGameMissionResp)
	err := grpc.Invoke(ctx, "/MissionLogic.MissionLogic/GetStaticTimeLimitGameMission", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionLogicClient) HandleEvent(ctx context.Context, in *HandleEventReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/MissionLogic.MissionLogic/HandleEvent", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionLogicClient) ResetInProgressMission(ctx context.Context, in *ResetInProgressMissionReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/MissionLogic.MissionLogic/ResetInProgressMission", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionLogicClient) HandleChannelOnlineMission(ctx context.Context, in *HandleChannelOnlineMissionReq, opts ...grpc.CallOption) (*HandleChannelOnlineMissionResp, error) {
	out := new(HandleChannelOnlineMissionResp)
	err := grpc.Invoke(ctx, "/MissionLogic.MissionLogic/HandleChannelOnlineMission", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionLogicClient) HandleEnterChannelFromRankMission(ctx context.Context, in *HandleEnterChannelFromRankMissionReq, opts ...grpc.CallOption) (*HandleEnterChannelFromRankMissionResp, error) {
	out := new(HandleEnterChannelFromRankMissionResp)
	err := grpc.Invoke(ctx, "/MissionLogic.MissionLogic/HandleEnterChannelFromRankMission", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for MissionLogic service

type MissionLogicServer interface {
	HandleMission(context.Context, *HandleMissionReq) (*HandleMissionResp, error)
	GetUserMissions(context.Context, *GetUserMissionsReq) (*GetUserMissionsResp, error)
	CollectMissionBonus(context.Context, *CollectMissionBonusReq) (*CollectMissionBonusResp, error)
	GetUserMissionGuide(context.Context, *GetUserMissionGuideReq) (*GetUserMissionGuideResp, error)
	GetStaticMissionConfig(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*StaticMissionConfig, error)
	GetStaticMission(context.Context, *GetStaticMissionReq) (*GetStaticMissionResp, error)
	HandleGuildJoinMission(context.Context, *HandleGuildJoinMissionReq) (*HandleGuildJoinMissionResp, error)
	IncreaseMissionFinishCount(context.Context, *IncreaseMissionFinishCountReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	HandleBindPhoneMission(context.Context, *HandleBindPhoneMissionReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetLatestUserMission(context.Context, *GetLatestUserMissionReq) (*GetLatestUserMissionResp, error)
	AcceptMission(context.Context, *AcceptMissionReq) (*GetLatestUserMissionResp, error)
	HandleTimeLimitMission(context.Context, *HandleTimeLimitMissionReq) (*HandleTimeLimitMissionResp, error)
	GetStaticTimeLimitGameMission(context.Context, *GetStaticTimeLimitGameMissionReq) (*GetStaticTimeLimitGameMissionResp, error)
	HandleEvent(context.Context, *HandleEventReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	ResetInProgressMission(context.Context, *ResetInProgressMissionReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	HandleChannelOnlineMission(context.Context, *HandleChannelOnlineMissionReq) (*HandleChannelOnlineMissionResp, error)
	HandleEnterChannelFromRankMission(context.Context, *HandleEnterChannelFromRankMissionReq) (*HandleEnterChannelFromRankMissionResp, error)
}

func RegisterMissionLogicServer(s *grpc.Server, srv MissionLogicServer) {
	s.RegisterService(&_MissionLogic_serviceDesc, srv)
}

func _MissionLogic_HandleMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionLogicServer).HandleMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MissionLogic.MissionLogic/HandleMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionLogicServer).HandleMission(ctx, req.(*HandleMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionLogic_GetUserMissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserMissionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionLogicServer).GetUserMissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MissionLogic.MissionLogic/GetUserMissions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionLogicServer).GetUserMissions(ctx, req.(*GetUserMissionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionLogic_CollectMissionBonus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CollectMissionBonusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionLogicServer).CollectMissionBonus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MissionLogic.MissionLogic/CollectMissionBonus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionLogicServer).CollectMissionBonus(ctx, req.(*CollectMissionBonusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionLogic_GetUserMissionGuide_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserMissionGuideReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionLogicServer).GetUserMissionGuide(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MissionLogic.MissionLogic/GetUserMissionGuide",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionLogicServer).GetUserMissionGuide(ctx, req.(*GetUserMissionGuideReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionLogic_GetStaticMissionConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionLogicServer).GetStaticMissionConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MissionLogic.MissionLogic/GetStaticMissionConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionLogicServer).GetStaticMissionConfig(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionLogic_GetStaticMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaticMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionLogicServer).GetStaticMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MissionLogic.MissionLogic/GetStaticMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionLogicServer).GetStaticMission(ctx, req.(*GetStaticMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionLogic_HandleGuildJoinMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleGuildJoinMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionLogicServer).HandleGuildJoinMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MissionLogic.MissionLogic/HandleGuildJoinMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionLogicServer).HandleGuildJoinMission(ctx, req.(*HandleGuildJoinMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionLogic_IncreaseMissionFinishCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IncreaseMissionFinishCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionLogicServer).IncreaseMissionFinishCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MissionLogic.MissionLogic/IncreaseMissionFinishCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionLogicServer).IncreaseMissionFinishCount(ctx, req.(*IncreaseMissionFinishCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionLogic_HandleBindPhoneMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleBindPhoneMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionLogicServer).HandleBindPhoneMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MissionLogic.MissionLogic/HandleBindPhoneMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionLogicServer).HandleBindPhoneMission(ctx, req.(*HandleBindPhoneMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionLogic_GetLatestUserMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLatestUserMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionLogicServer).GetLatestUserMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MissionLogic.MissionLogic/GetLatestUserMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionLogicServer).GetLatestUserMission(ctx, req.(*GetLatestUserMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionLogic_AcceptMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcceptMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionLogicServer).AcceptMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MissionLogic.MissionLogic/AcceptMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionLogicServer).AcceptMission(ctx, req.(*AcceptMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionLogic_HandleTimeLimitMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleTimeLimitMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionLogicServer).HandleTimeLimitMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MissionLogic.MissionLogic/HandleTimeLimitMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionLogicServer).HandleTimeLimitMission(ctx, req.(*HandleTimeLimitMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionLogic_GetStaticTimeLimitGameMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaticTimeLimitGameMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionLogicServer).GetStaticTimeLimitGameMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MissionLogic.MissionLogic/GetStaticTimeLimitGameMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionLogicServer).GetStaticTimeLimitGameMission(ctx, req.(*GetStaticTimeLimitGameMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionLogic_HandleEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionLogicServer).HandleEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MissionLogic.MissionLogic/HandleEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionLogicServer).HandleEvent(ctx, req.(*HandleEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionLogic_ResetInProgressMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetInProgressMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionLogicServer).ResetInProgressMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MissionLogic.MissionLogic/ResetInProgressMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionLogicServer).ResetInProgressMission(ctx, req.(*ResetInProgressMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionLogic_HandleChannelOnlineMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleChannelOnlineMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionLogicServer).HandleChannelOnlineMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MissionLogic.MissionLogic/HandleChannelOnlineMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionLogicServer).HandleChannelOnlineMission(ctx, req.(*HandleChannelOnlineMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionLogic_HandleEnterChannelFromRankMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleEnterChannelFromRankMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionLogicServer).HandleEnterChannelFromRankMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MissionLogic.MissionLogic/HandleEnterChannelFromRankMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionLogicServer).HandleEnterChannelFromRankMission(ctx, req.(*HandleEnterChannelFromRankMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MissionLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "MissionLogic.MissionLogic",
	HandlerType: (*MissionLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HandleMission",
			Handler:    _MissionLogic_HandleMission_Handler,
		},
		{
			MethodName: "GetUserMissions",
			Handler:    _MissionLogic_GetUserMissions_Handler,
		},
		{
			MethodName: "CollectMissionBonus",
			Handler:    _MissionLogic_CollectMissionBonus_Handler,
		},
		{
			MethodName: "GetUserMissionGuide",
			Handler:    _MissionLogic_GetUserMissionGuide_Handler,
		},
		{
			MethodName: "GetStaticMissionConfig",
			Handler:    _MissionLogic_GetStaticMissionConfig_Handler,
		},
		{
			MethodName: "GetStaticMission",
			Handler:    _MissionLogic_GetStaticMission_Handler,
		},
		{
			MethodName: "HandleGuildJoinMission",
			Handler:    _MissionLogic_HandleGuildJoinMission_Handler,
		},
		{
			MethodName: "IncreaseMissionFinishCount",
			Handler:    _MissionLogic_IncreaseMissionFinishCount_Handler,
		},
		{
			MethodName: "HandleBindPhoneMission",
			Handler:    _MissionLogic_HandleBindPhoneMission_Handler,
		},
		{
			MethodName: "GetLatestUserMission",
			Handler:    _MissionLogic_GetLatestUserMission_Handler,
		},
		{
			MethodName: "AcceptMission",
			Handler:    _MissionLogic_AcceptMission_Handler,
		},
		{
			MethodName: "HandleTimeLimitMission",
			Handler:    _MissionLogic_HandleTimeLimitMission_Handler,
		},
		{
			MethodName: "GetStaticTimeLimitGameMission",
			Handler:    _MissionLogic_GetStaticTimeLimitGameMission_Handler,
		},
		{
			MethodName: "HandleEvent",
			Handler:    _MissionLogic_HandleEvent_Handler,
		},
		{
			MethodName: "ResetInProgressMission",
			Handler:    _MissionLogic_ResetInProgressMission_Handler,
		},
		{
			MethodName: "HandleChannelOnlineMission",
			Handler:    _MissionLogic_HandleChannelOnlineMission_Handler,
		},
		{
			MethodName: "HandleEnterChannelFromRankMission",
			Handler:    _MissionLogic_HandleEnterChannelFromRankMission_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/missionlogicsvr/missionlogic.proto",
}

func (m *MissionBonus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MissionBonus) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Experience))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.RedDiamonds))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Medal))
	return i, nil
}

func (m *HandleMissionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HandleMissionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.CmdId))
	if m.CmdBody != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintMissionlogic(dAtA, i, uint64(len(m.CmdBody)))
		i += copy(dAtA[i:], m.CmdBody)
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.ClientType))
	dAtA[i] = 0x28
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.ClientVer))
	return i, nil
}

func (m *HandleMissionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HandleMissionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *HandleGuildJoinMissionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HandleGuildJoinMissionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *HandleGuildJoinMissionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HandleGuildJoinMissionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *HandleBindPhoneMissionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HandleBindPhoneMissionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *HandleChannelOnlineMissionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HandleChannelOnlineMissionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.ChannelType))
	return i, nil
}

func (m *HandleChannelOnlineMissionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HandleChannelOnlineMissionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *MissionGroup) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MissionGroup) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.GroupId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x18
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x20
	i++
	if m.Visible {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	if m.Unlocked {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *Mission) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Mission) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.MissionId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x18
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.StartTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.ExpireTime))
	if m.Bonus == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("bonus")
	} else {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintMissionlogic(dAtA, i, uint64(m.Bonus.Size()))
		n1, err := m.Bonus.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x30
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x38
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.FinishCount))
	dAtA[i] = 0x40
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.RequiredCount))
	dAtA[i] = 0x4a
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(len(m.Description)))
	i += copy(dAtA[i:], m.Description)
	dAtA[i] = 0x52
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(len(m.Strategy)))
	i += copy(dAtA[i:], m.Strategy)
	dAtA[i] = 0x5a
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(len(m.Identifier)))
	i += copy(dAtA[i:], m.Identifier)
	dAtA[i] = 0x62
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(len(m.ClientUrl)))
	i += copy(dAtA[i:], m.ClientUrl)
	dAtA[i] = 0x68
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.RewardCount))
	dAtA[i] = 0x70
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.EffectBegin))
	dAtA[i] = 0x78
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.EffectEnd))
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.IsAccept))
	dAtA[i] = 0x8a
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(len(m.SubName)))
	i += copy(dAtA[i:], m.SubName)
	dAtA[i] = 0x92
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(len(m.SpecialBonusName)))
	i += copy(dAtA[i:], m.SpecialBonusName)
	if m.SpecialBonus != nil {
		dAtA[i] = 0x9a
		i++
		dAtA[i] = 0x1
		i++
		i = encodeVarintMissionlogic(dAtA, i, uint64(m.SpecialBonus.Size()))
		n2, err := m.SpecialBonus.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0xa0
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.BaseRewardCount))
	dAtA[i] = 0xa8
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.SubNameType))
	dAtA[i] = 0xb0
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.MaxCollectCount))
	return i, nil
}

func (m *MissionGroupDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MissionGroupDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Group == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("group")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMissionlogic(dAtA, i, uint64(m.Group.Size()))
		n3, err := m.Group.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	if len(m.MissionList) > 0 {
		for _, msg := range m.MissionList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintMissionlogic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *Platform) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Platform) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetUserMissionsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserMissionsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Platform))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.ClientVersion))
	dAtA[i] = 0x20
	i++
	if m.ShowGameCenter {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetUserMissionsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserMissionsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MissionGroupList) > 0 {
		for _, msg := range m.MissionGroupList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMissionlogic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetLatestUserMissionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLatestUserMissionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.MissionId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Platform))
	return i, nil
}

func (m *GetLatestUserMissionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLatestUserMissionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.MissionGroup != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMissionlogic(dAtA, i, uint64(m.MissionGroup.Size()))
		n4, err := m.MissionGroup.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *CollectMissionBonusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CollectMissionBonusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(len(m.MissionIdentifier)))
	i += copy(dAtA[i:], m.MissionIdentifier)
	dAtA[i] = 0x18
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Platform))
	dAtA[i] = 0x20
	i++
	if m.ShowGameCenter {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *CollectMissionBonusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CollectMissionBonusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GrowInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("grow_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMissionlogic(dAtA, i, uint64(m.GrowInfo.Size()))
		n5, err := m.GrowInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if m.BaseBonus == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("base_bonus")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintMissionlogic(dAtA, i, uint64(m.BaseBonus.Size()))
		n6, err := m.BaseBonus.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if len(m.MedalBuffList) > 0 {
		for _, msg := range m.MedalBuffList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintMissionlogic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.NewMedalId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.MissionId))
	if m.FinalBonus != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintMissionlogic(dAtA, i, uint64(m.FinalBonus.Size()))
		n7, err := m.FinalBonus.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if m.NoBuffBonus != nil {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintMissionlogic(dAtA, i, uint64(m.NoBuffBonus.Size()))
		n8, err := m.NoBuffBonus.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *GrowInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GrowInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Exp))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Level))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.CurrentLevelExpMin))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.CurrentLevelExpMax))
	dAtA[i] = 0x28
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Currency))
	return i, nil
}

func (m *GetUserMissionGuideReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserMissionGuideReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Platform))
	dAtA[i] = 0x18
	i++
	if m.ShowGameCenter {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetUserMissionGuideResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserMissionGuideResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(len(m.Guide)))
	i += copy(dAtA[i:], m.Guide)
	return i, nil
}

func (m *BuffRatio) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BuffRatio) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Experience))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.RedDiamonds))
	return i, nil
}

func (m *Buff) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Buff) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Experience))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.RedDiamonds))
	return i, nil
}

func (m *MedalBuffRatio) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MedalBuffRatio) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.MedalId))
	if m.BuffRatio == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("buff_ratio")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintMissionlogic(dAtA, i, uint64(m.BuffRatio.Size()))
		n9, err := m.BuffRatio.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	return i, nil
}

func (m *MedalBuff) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MedalBuff) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.MedalId))
	if m.Buff == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("buff")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintMissionlogic(dAtA, i, uint64(m.Buff.Size()))
		n10, err := m.Buff.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	return i, nil
}

func (m *StaticMission) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StaticMission) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.MissionId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(len(m.Description)))
	i += copy(dAtA[i:], m.Description)
	dAtA[i] = 0x20
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.EffectBegin))
	dAtA[i] = 0x28
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.EffectEnd))
	dAtA[i] = 0x30
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.MinClientVersion))
	dAtA[i] = 0x38
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.AddTime))
	if m.Bonus == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("bonus")
	} else {
		dAtA[i] = 0x42
		i++
		i = encodeVarintMissionlogic(dAtA, i, uint64(m.Bonus.Size()))
		n11, err := m.Bonus.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	dAtA[i] = 0x48
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.ResetTime))
	dAtA[i] = 0x50
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.EventType))
	dAtA[i] = 0x58
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.SubType))
	if len(m.LyGameIdList) > 0 {
		for _, num := range m.LyGameIdList {
			dAtA[i] = 0x60
			i++
			i = encodeVarintMissionlogic(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x6a
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(len(m.SubName)))
	i += copy(dAtA[i:], m.SubName)
	dAtA[i] = 0x70
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.RechargeCount))
	dAtA[i] = 0x7a
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(len(m.SpecialBonusName)))
	i += copy(dAtA[i:], m.SpecialBonusName)
	if m.SpecialBonus != nil {
		dAtA[i] = 0x82
		i++
		dAtA[i] = 0x1
		i++
		i = encodeVarintMissionlogic(dAtA, i, uint64(m.SpecialBonus.Size()))
		n12, err := m.SpecialBonus.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	dAtA[i] = 0x88
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.SubNameType))
	dAtA[i] = 0x90
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Platform))
	return i, nil
}

func (m *StaticMissionGroup) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StaticMissionGroup) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.GroupId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x18
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Type))
	if len(m.MissionList) > 0 {
		for _, msg := range m.MissionList {
			dAtA[i] = 0x22
			i++
			i = encodeVarintMissionlogic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *StaticMissionConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StaticMissionConfig) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.UpdateTime))
	if len(m.MissionGroupList) > 0 {
		for _, msg := range m.MissionGroupList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintMissionlogic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetStaticMissionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetStaticMissionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.MissionId))
	return i, nil
}

func (m *GetStaticMissionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetStaticMissionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Mission == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("mission")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMissionlogic(dAtA, i, uint64(m.Mission.Size()))
		n13, err := m.Mission.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	return i, nil
}

func (m *IncreaseMissionFinishCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IncreaseMissionFinishCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.MissionId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *ResetInProgressMissionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResetInProgressMissionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.MissionId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Type))
	return i, nil
}

func (m *HandleTimeLimitMissionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HandleTimeLimitMissionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.MissionId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(len(m.Extend)))
	i += copy(dAtA[i:], m.Extend)
	return i, nil
}

func (m *HandleTimeLimitMissionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HandleTimeLimitMissionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *AcceptMissionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AcceptMissionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.MissionId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Platform))
	return i, nil
}

func (m *GetStaticTimeLimitGameMissionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetStaticTimeLimitGameMissionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.LastUpdateTime))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.EventType))
	return i, nil
}

func (m *GetStaticTimeLimitGameMissionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetStaticTimeLimitGameMissionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.UpdateTime))
	if len(m.MissionList) > 0 {
		for _, msg := range m.MissionList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintMissionlogic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *HandleEventReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HandleEventReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.EventType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.RechargePenny))
	dAtA[i] = 0x28
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Level))
	dAtA[i] = 0x30
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.CashRechargePenny))
	return i, nil
}

func (m *HandleEnterChannelFromRankMissionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HandleEnterChannelFromRankMissionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissionlogic(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *HandleEnterChannelFromRankMissionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HandleEnterChannelFromRankMissionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Missionlogic(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Missionlogic(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintMissionlogic(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *MissionBonus) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.Experience))
	n += 1 + sovMissionlogic(uint64(m.RedDiamonds))
	n += 1 + sovMissionlogic(uint64(m.Medal))
	return n
}

func (m *HandleMissionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.Uid))
	n += 1 + sovMissionlogic(uint64(m.CmdId))
	if m.CmdBody != nil {
		l = len(m.CmdBody)
		n += 1 + l + sovMissionlogic(uint64(l))
	}
	n += 1 + sovMissionlogic(uint64(m.ClientType))
	n += 1 + sovMissionlogic(uint64(m.ClientVer))
	return n
}

func (m *HandleMissionResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *HandleGuildJoinMissionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.Uid))
	n += 1 + sovMissionlogic(uint64(m.GuildId))
	return n
}

func (m *HandleGuildJoinMissionResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *HandleBindPhoneMissionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.Uid))
	return n
}

func (m *HandleChannelOnlineMissionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.Uid))
	n += 1 + sovMissionlogic(uint64(m.ChannelId))
	n += 1 + sovMissionlogic(uint64(m.ChannelType))
	return n
}

func (m *HandleChannelOnlineMissionResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *MissionGroup) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.GroupId))
	l = len(m.Name)
	n += 1 + l + sovMissionlogic(uint64(l))
	n += 1 + sovMissionlogic(uint64(m.Type))
	n += 2
	n += 2
	return n
}

func (m *Mission) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.MissionId))
	l = len(m.Name)
	n += 1 + l + sovMissionlogic(uint64(l))
	n += 1 + sovMissionlogic(uint64(m.StartTime))
	n += 1 + sovMissionlogic(uint64(m.ExpireTime))
	if m.Bonus != nil {
		l = m.Bonus.Size()
		n += 1 + l + sovMissionlogic(uint64(l))
	}
	n += 1 + sovMissionlogic(uint64(m.Status))
	n += 1 + sovMissionlogic(uint64(m.FinishCount))
	n += 1 + sovMissionlogic(uint64(m.RequiredCount))
	l = len(m.Description)
	n += 1 + l + sovMissionlogic(uint64(l))
	l = len(m.Strategy)
	n += 1 + l + sovMissionlogic(uint64(l))
	l = len(m.Identifier)
	n += 1 + l + sovMissionlogic(uint64(l))
	l = len(m.ClientUrl)
	n += 1 + l + sovMissionlogic(uint64(l))
	n += 1 + sovMissionlogic(uint64(m.RewardCount))
	n += 1 + sovMissionlogic(uint64(m.EffectBegin))
	n += 1 + sovMissionlogic(uint64(m.EffectEnd))
	n += 2 + sovMissionlogic(uint64(m.IsAccept))
	l = len(m.SubName)
	n += 2 + l + sovMissionlogic(uint64(l))
	l = len(m.SpecialBonusName)
	n += 2 + l + sovMissionlogic(uint64(l))
	if m.SpecialBonus != nil {
		l = m.SpecialBonus.Size()
		n += 2 + l + sovMissionlogic(uint64(l))
	}
	n += 2 + sovMissionlogic(uint64(m.BaseRewardCount))
	n += 2 + sovMissionlogic(uint64(m.SubNameType))
	n += 2 + sovMissionlogic(uint64(m.MaxCollectCount))
	return n
}

func (m *MissionGroupDetail) Size() (n int) {
	var l int
	_ = l
	if m.Group != nil {
		l = m.Group.Size()
		n += 1 + l + sovMissionlogic(uint64(l))
	}
	if len(m.MissionList) > 0 {
		for _, e := range m.MissionList {
			l = e.Size()
			n += 1 + l + sovMissionlogic(uint64(l))
		}
	}
	return n
}

func (m *Platform) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetUserMissionsReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.Uid))
	n += 1 + sovMissionlogic(uint64(m.Platform))
	n += 1 + sovMissionlogic(uint64(m.ClientVersion))
	n += 2
	return n
}

func (m *GetUserMissionsResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MissionGroupList) > 0 {
		for _, e := range m.MissionGroupList {
			l = e.Size()
			n += 1 + l + sovMissionlogic(uint64(l))
		}
	}
	return n
}

func (m *GetLatestUserMissionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.Uid))
	n += 1 + sovMissionlogic(uint64(m.MissionId))
	n += 1 + sovMissionlogic(uint64(m.Platform))
	return n
}

func (m *GetLatestUserMissionResp) Size() (n int) {
	var l int
	_ = l
	if m.MissionGroup != nil {
		l = m.MissionGroup.Size()
		n += 1 + l + sovMissionlogic(uint64(l))
	}
	return n
}

func (m *CollectMissionBonusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.Uid))
	l = len(m.MissionIdentifier)
	n += 1 + l + sovMissionlogic(uint64(l))
	n += 1 + sovMissionlogic(uint64(m.Platform))
	n += 2
	return n
}

func (m *CollectMissionBonusResp) Size() (n int) {
	var l int
	_ = l
	if m.GrowInfo != nil {
		l = m.GrowInfo.Size()
		n += 1 + l + sovMissionlogic(uint64(l))
	}
	if m.BaseBonus != nil {
		l = m.BaseBonus.Size()
		n += 1 + l + sovMissionlogic(uint64(l))
	}
	if len(m.MedalBuffList) > 0 {
		for _, e := range m.MedalBuffList {
			l = e.Size()
			n += 1 + l + sovMissionlogic(uint64(l))
		}
	}
	n += 1 + sovMissionlogic(uint64(m.NewMedalId))
	n += 1 + sovMissionlogic(uint64(m.MissionId))
	if m.FinalBonus != nil {
		l = m.FinalBonus.Size()
		n += 1 + l + sovMissionlogic(uint64(l))
	}
	if m.NoBuffBonus != nil {
		l = m.NoBuffBonus.Size()
		n += 1 + l + sovMissionlogic(uint64(l))
	}
	return n
}

func (m *GrowInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.Exp))
	n += 1 + sovMissionlogic(uint64(m.Level))
	n += 1 + sovMissionlogic(uint64(m.CurrentLevelExpMin))
	n += 1 + sovMissionlogic(uint64(m.CurrentLevelExpMax))
	n += 1 + sovMissionlogic(uint64(m.Currency))
	return n
}

func (m *GetUserMissionGuideReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.Uid))
	n += 1 + sovMissionlogic(uint64(m.Platform))
	n += 2
	return n
}

func (m *GetUserMissionGuideResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.Guide)
	n += 1 + l + sovMissionlogic(uint64(l))
	return n
}

func (m *BuffRatio) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.Experience))
	n += 1 + sovMissionlogic(uint64(m.RedDiamonds))
	return n
}

func (m *Buff) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.Experience))
	n += 1 + sovMissionlogic(uint64(m.RedDiamonds))
	return n
}

func (m *MedalBuffRatio) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.MedalId))
	if m.BuffRatio != nil {
		l = m.BuffRatio.Size()
		n += 1 + l + sovMissionlogic(uint64(l))
	}
	return n
}

func (m *MedalBuff) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.MedalId))
	if m.Buff != nil {
		l = m.Buff.Size()
		n += 1 + l + sovMissionlogic(uint64(l))
	}
	return n
}

func (m *StaticMission) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.MissionId))
	l = len(m.Name)
	n += 1 + l + sovMissionlogic(uint64(l))
	l = len(m.Description)
	n += 1 + l + sovMissionlogic(uint64(l))
	n += 1 + sovMissionlogic(uint64(m.EffectBegin))
	n += 1 + sovMissionlogic(uint64(m.EffectEnd))
	n += 1 + sovMissionlogic(uint64(m.MinClientVersion))
	n += 1 + sovMissionlogic(uint64(m.AddTime))
	if m.Bonus != nil {
		l = m.Bonus.Size()
		n += 1 + l + sovMissionlogic(uint64(l))
	}
	n += 1 + sovMissionlogic(uint64(m.ResetTime))
	n += 1 + sovMissionlogic(uint64(m.EventType))
	n += 1 + sovMissionlogic(uint64(m.SubType))
	if len(m.LyGameIdList) > 0 {
		for _, e := range m.LyGameIdList {
			n += 1 + sovMissionlogic(uint64(e))
		}
	}
	l = len(m.SubName)
	n += 1 + l + sovMissionlogic(uint64(l))
	n += 1 + sovMissionlogic(uint64(m.RechargeCount))
	l = len(m.SpecialBonusName)
	n += 1 + l + sovMissionlogic(uint64(l))
	if m.SpecialBonus != nil {
		l = m.SpecialBonus.Size()
		n += 2 + l + sovMissionlogic(uint64(l))
	}
	n += 2 + sovMissionlogic(uint64(m.SubNameType))
	n += 2 + sovMissionlogic(uint64(m.Platform))
	return n
}

func (m *StaticMissionGroup) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.GroupId))
	l = len(m.Name)
	n += 1 + l + sovMissionlogic(uint64(l))
	n += 1 + sovMissionlogic(uint64(m.Type))
	if len(m.MissionList) > 0 {
		for _, e := range m.MissionList {
			l = e.Size()
			n += 1 + l + sovMissionlogic(uint64(l))
		}
	}
	return n
}

func (m *StaticMissionConfig) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.UpdateTime))
	if len(m.MissionGroupList) > 0 {
		for _, e := range m.MissionGroupList {
			l = e.Size()
			n += 1 + l + sovMissionlogic(uint64(l))
		}
	}
	return n
}

func (m *GetStaticMissionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.MissionId))
	return n
}

func (m *GetStaticMissionResp) Size() (n int) {
	var l int
	_ = l
	if m.Mission != nil {
		l = m.Mission.Size()
		n += 1 + l + sovMissionlogic(uint64(l))
	}
	return n
}

func (m *IncreaseMissionFinishCountReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.Uid))
	n += 1 + sovMissionlogic(uint64(m.MissionId))
	n += 1 + sovMissionlogic(uint64(m.Count))
	return n
}

func (m *ResetInProgressMissionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.Uid))
	n += 1 + sovMissionlogic(uint64(m.MissionId))
	n += 1 + sovMissionlogic(uint64(m.Type))
	return n
}

func (m *HandleTimeLimitMissionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.Uid))
	n += 1 + sovMissionlogic(uint64(m.MissionId))
	l = len(m.Extend)
	n += 1 + l + sovMissionlogic(uint64(l))
	return n
}

func (m *HandleTimeLimitMissionResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *AcceptMissionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.Uid))
	n += 1 + sovMissionlogic(uint64(m.MissionId))
	n += 1 + sovMissionlogic(uint64(m.Platform))
	return n
}

func (m *GetStaticTimeLimitGameMissionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.LastUpdateTime))
	n += 1 + sovMissionlogic(uint64(m.EventType))
	return n
}

func (m *GetStaticTimeLimitGameMissionResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.UpdateTime))
	if len(m.MissionList) > 0 {
		for _, e := range m.MissionList {
			l = e.Size()
			n += 1 + l + sovMissionlogic(uint64(l))
		}
	}
	return n
}

func (m *HandleEventReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.Uid))
	n += 1 + sovMissionlogic(uint64(m.EventType))
	n += 1 + sovMissionlogic(uint64(m.LyGameId))
	n += 1 + sovMissionlogic(uint64(m.RechargePenny))
	n += 1 + sovMissionlogic(uint64(m.Level))
	n += 1 + sovMissionlogic(uint64(m.CashRechargePenny))
	return n
}

func (m *HandleEnterChannelFromRankMissionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissionlogic(uint64(m.Uid))
	return n
}

func (m *HandleEnterChannelFromRankMissionResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovMissionlogic(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozMissionlogic(x uint64) (n int) {
	return sovMissionlogic(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *MissionBonus) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MissionBonus: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MissionBonus: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Experience", wireType)
			}
			m.Experience = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Experience |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedDiamonds", wireType)
			}
			m.RedDiamonds = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedDiamonds |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Medal", wireType)
			}
			m.Medal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Medal |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HandleMissionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HandleMissionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HandleMissionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CmdId", wireType)
			}
			m.CmdId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CmdId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CmdBody", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CmdBody = append(m.CmdBody[:0], dAtA[iNdEx:postIndex]...)
			if m.CmdBody == nil {
				m.CmdBody = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientType", wireType)
			}
			m.ClientType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientVer", wireType)
			}
			m.ClientVer = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientVer |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cmd_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cmd_body")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HandleMissionResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HandleMissionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HandleMissionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HandleGuildJoinMissionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HandleGuildJoinMissionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HandleGuildJoinMissionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HandleGuildJoinMissionResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HandleGuildJoinMissionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HandleGuildJoinMissionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HandleBindPhoneMissionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HandleBindPhoneMissionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HandleBindPhoneMissionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HandleChannelOnlineMissionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HandleChannelOnlineMissionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HandleChannelOnlineMissionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HandleChannelOnlineMissionResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HandleChannelOnlineMissionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HandleChannelOnlineMissionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MissionGroup) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MissionGroup: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MissionGroup: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Visible", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Visible = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Unlocked", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Unlocked = bool(v != 0)
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("group_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("visible")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("unlocked")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Mission) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Mission: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Mission: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionId", wireType)
			}
			m.MissionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MissionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			m.StartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Bonus", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Bonus == nil {
				m.Bonus = &MissionBonus{}
			}
			if err := m.Bonus.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinishCount", wireType)
			}
			m.FinishCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinishCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequiredCount", wireType)
			}
			m.RequiredCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RequiredCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Strategy", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Strategy = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Identifier", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Identifier = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000400)
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RewardCount", wireType)
			}
			m.RewardCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RewardCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EffectBegin", wireType)
			}
			m.EffectBegin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EffectBegin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EffectEnd", wireType)
			}
			m.EffectEnd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EffectEnd |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 16:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAccept", wireType)
			}
			m.IsAccept = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IsAccept |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 17:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SubName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 18:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SpecialBonusName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SpecialBonusName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 19:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SpecialBonus", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.SpecialBonus == nil {
				m.SpecialBonus = &MissionBonus{}
			}
			if err := m.SpecialBonus.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 20:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseRewardCount", wireType)
			}
			m.BaseRewardCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BaseRewardCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 21:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubNameType", wireType)
			}
			m.SubNameType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SubNameType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 22:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaxCollectCount", wireType)
			}
			m.MaxCollectCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxCollectCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mission_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("expire_time")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("bonus")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("finish_count")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("required_count")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("description")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("strategy")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("identifier")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MissionGroupDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MissionGroupDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MissionGroupDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Group", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Group == nil {
				m.Group = &MissionGroup{}
			}
			if err := m.Group.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MissionList = append(m.MissionList, &Mission{})
			if err := m.MissionList[len(m.MissionList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("group")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Platform) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Platform: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Platform: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserMissionsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserMissionsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserMissionsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientVersion", wireType)
			}
			m.ClientVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ShowGameCenter", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShowGameCenter = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserMissionsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserMissionsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserMissionsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionGroupList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MissionGroupList = append(m.MissionGroupList, &MissionGroupDetail{})
			if err := m.MissionGroupList[len(m.MissionGroupList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLatestUserMissionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLatestUserMissionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLatestUserMissionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionId", wireType)
			}
			m.MissionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MissionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mission_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLatestUserMissionResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLatestUserMissionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLatestUserMissionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionGroup", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MissionGroup == nil {
				m.MissionGroup = &MissionGroupDetail{}
			}
			if err := m.MissionGroup.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CollectMissionBonusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CollectMissionBonusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CollectMissionBonusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionIdentifier", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MissionIdentifier = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ShowGameCenter", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShowGameCenter = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mission_identifier")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CollectMissionBonusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CollectMissionBonusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CollectMissionBonusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GrowInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GrowInfo == nil {
				m.GrowInfo = &GrowInfo{}
			}
			if err := m.GrowInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseBonus", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseBonus == nil {
				m.BaseBonus = &MissionBonus{}
			}
			if err := m.BaseBonus.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MedalBuffList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MedalBuffList = append(m.MedalBuffList, &MedalBuff{})
			if err := m.MedalBuffList[len(m.MedalBuffList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewMedalId", wireType)
			}
			m.NewMedalId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewMedalId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionId", wireType)
			}
			m.MissionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MissionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinalBonus", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.FinalBonus == nil {
				m.FinalBonus = &MissionBonus{}
			}
			if err := m.FinalBonus.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NoBuffBonus", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.NoBuffBonus == nil {
				m.NoBuffBonus = &MissionBonus{}
			}
			if err := m.NoBuffBonus.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("grow_info")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_bonus")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GrowInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GrowInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GrowInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Exp", wireType)
			}
			m.Exp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Exp |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrentLevelExpMin", wireType)
			}
			m.CurrentLevelExpMin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrentLevelExpMin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrentLevelExpMax", wireType)
			}
			m.CurrentLevelExpMax = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrentLevelExpMax |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Currency", wireType)
			}
			m.Currency = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Currency |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("exp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("current_level_exp_min")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("current_level_exp_max")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("currency")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserMissionGuideReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserMissionGuideReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserMissionGuideReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ShowGameCenter", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShowGameCenter = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserMissionGuideResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserMissionGuideResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserMissionGuideResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Guide", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Guide = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guide")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BuffRatio) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BuffRatio: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BuffRatio: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Experience", wireType)
			}
			m.Experience = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Experience |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedDiamonds", wireType)
			}
			m.RedDiamonds = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedDiamonds |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("experience")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("red_diamonds")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Buff) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Buff: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Buff: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Experience", wireType)
			}
			m.Experience = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Experience |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedDiamonds", wireType)
			}
			m.RedDiamonds = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedDiamonds |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("experience")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("red_diamonds")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MedalBuffRatio) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MedalBuffRatio: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MedalBuffRatio: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MedalId", wireType)
			}
			m.MedalId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MedalId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BuffRatio", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BuffRatio == nil {
				m.BuffRatio = &BuffRatio{}
			}
			if err := m.BuffRatio.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("medal_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("buff_ratio")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MedalBuff) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MedalBuff: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MedalBuff: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MedalId", wireType)
			}
			m.MedalId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MedalId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Buff", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Buff == nil {
				m.Buff = &Buff{}
			}
			if err := m.Buff.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("medal_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("buff")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StaticMission) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StaticMission: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StaticMission: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionId", wireType)
			}
			m.MissionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MissionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EffectBegin", wireType)
			}
			m.EffectBegin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EffectBegin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EffectEnd", wireType)
			}
			m.EffectEnd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EffectEnd |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MinClientVersion", wireType)
			}
			m.MinClientVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MinClientVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AddTime", wireType)
			}
			m.AddTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AddTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Bonus", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Bonus == nil {
				m.Bonus = &MissionBonus{}
			}
			if err := m.Bonus.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResetTime", wireType)
			}
			m.ResetTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResetTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EventType", wireType)
			}
			m.EventType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EventType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubType", wireType)
			}
			m.SubType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SubType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMissionlogic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.LyGameIdList = append(m.LyGameIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMissionlogic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMissionlogic
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMissionlogic
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint64(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.LyGameIdList = append(m.LyGameIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameIdList", wireType)
			}
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SubName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeCount", wireType)
			}
			m.RechargeCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RechargeCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SpecialBonusName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SpecialBonusName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SpecialBonus", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.SpecialBonus == nil {
				m.SpecialBonus = &MissionBonus{}
			}
			if err := m.SpecialBonus.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 17:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubNameType", wireType)
			}
			m.SubNameType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SubNameType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 18:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mission_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("description")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("effect_begin")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("effect_end")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("min_client_version")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("add_time")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("bonus")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StaticMissionGroup) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StaticMissionGroup: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StaticMissionGroup: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MissionList = append(m.MissionList, &StaticMission{})
			if err := m.MissionList[len(m.MissionList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("group_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StaticMissionConfig) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StaticMissionConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StaticMissionConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionGroupList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MissionGroupList = append(m.MissionGroupList, &StaticMissionGroup{})
			if err := m.MissionGroupList[len(m.MissionGroupList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("update_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetStaticMissionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetStaticMissionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetStaticMissionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionId", wireType)
			}
			m.MissionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MissionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mission_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetStaticMissionResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetStaticMissionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetStaticMissionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Mission", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Mission == nil {
				m.Mission = &StaticMission{}
			}
			if err := m.Mission.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mission")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *IncreaseMissionFinishCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IncreaseMissionFinishCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IncreaseMissionFinishCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionId", wireType)
			}
			m.MissionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MissionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mission_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ResetInProgressMissionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ResetInProgressMissionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ResetInProgressMissionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionId", wireType)
			}
			m.MissionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MissionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HandleTimeLimitMissionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HandleTimeLimitMissionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HandleTimeLimitMissionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionId", wireType)
			}
			m.MissionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MissionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Extend", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Extend = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mission_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HandleTimeLimitMissionResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HandleTimeLimitMissionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HandleTimeLimitMissionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AcceptMissionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AcceptMissionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AcceptMissionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionId", wireType)
			}
			m.MissionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MissionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mission_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetStaticTimeLimitGameMissionReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetStaticTimeLimitGameMissionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetStaticTimeLimitGameMissionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTime", wireType)
			}
			m.LastUpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EventType", wireType)
			}
			m.EventType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EventType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetStaticTimeLimitGameMissionResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetStaticTimeLimitGameMissionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetStaticTimeLimitGameMissionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissionlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MissionList = append(m.MissionList, &StaticMission{})
			if err := m.MissionList[len(m.MissionList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("update_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HandleEventReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HandleEventReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HandleEventReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EventType", wireType)
			}
			m.EventType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EventType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargePenny", wireType)
			}
			m.RechargePenny = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RechargePenny |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CashRechargePenny", wireType)
			}
			m.CashRechargePenny = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CashRechargePenny |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("event_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HandleEnterChannelFromRankMissionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HandleEnterChannelFromRankMissionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HandleEnterChannelFromRankMissionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HandleEnterChannelFromRankMissionResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HandleEnterChannelFromRankMissionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HandleEnterChannelFromRankMissionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMissionlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissionlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipMissionlogic(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowMissionlogic
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMissionlogic
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthMissionlogic
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowMissionlogic
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipMissionlogic(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthMissionlogic = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowMissionlogic   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/missionlogicsvr/missionlogic.proto", fileDescriptorMissionlogic) }

var fileDescriptorMissionlogic = []byte{
	// 2934 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x5a, 0xdf, 0x6f, 0xe3, 0x58,
	0xf5, 0x1f, 0x27, 0x69, 0x93, 0x9c, 0x24, 0xad, 0x7b, 0x67, 0xb7, 0x9b, 0xc9, 0xce, 0xb4, 0x19,
	0xcf, 0xaf, 0x7e, 0xbb, 0xdf, 0xe9, 0xec, 0xce, 0xb0, 0xb0, 0x94, 0x2a, 0xab, 0x36, 0xf5, 0x74,
	0xc2, 0xa6, 0x69, 0x49, 0xd2, 0x91, 0x86, 0xd5, 0xca, 0x72, 0xe3, 0xdb, 0x8e, 0x19, 0xc7, 0xf6,
	0xd8, 0x4e, 0x9b, 0xae, 0x84, 0xb4, 0x42, 0x20, 0xa1, 0x15, 0x48, 0x68, 0x85, 0x10, 0xaf, 0xc0,
	0x22, 0x81, 0x78, 0xe0, 0x05, 0x09, 0xde, 0x79, 0x59, 0xf1, 0x80, 0x10, 0x7f, 0x00, 0x42, 0xcb,
	0xcb, 0x3e, 0xf0, 0xc0, 0x9f, 0x80, 0xee, 0xbd, 0xb6, 0x73, 0xed, 0xb8, 0x4d, 0x86, 0x5d, 0x78,
	0xa9, 0xe2, 0x73, 0x3e, 0xf7, 0xde, 0x73, 0xce, 0x3d, 0xe7, 0xdc, 0xcf, 0xb5, 0x0b, 0xb7, 0x5d,
	0xa7, 0x77, 0xaf, 0xaf, 0xbb, 0xae, 0x6e, 0x99, 0x86, 0x75, 0xac, 0xf7, 0xdc, 0x13, 0x27, 0xf2,
	0xbc, 0x66, 0x3b, 0x96, 0x67, 0xa1, 0xe2, 0x2e, 0x93, 0x35, 0x89, 0xac, 0x72, 0xb3, 0x67, 0xf5,
	0xfb, 0x96, 0x79, 0xcf, 0x33, 0x4e, 0x6c, 0xbd, 0xf7, 0xcc, 0xc0, 0xf7, 0xdc, 0x67, 0x87, 0x03,
	0xdd, 0xf0, 0x74, 0xd3, 0x3b, 0xb3, 0x31, 0x1b, 0x23, 0x9d, 0x41, 0x30, 0x6a, 0xcb, 0x32, 0x07,
	0x2e, 0xba, 0x09, 0x80, 0x87, 0x36, 0x76, 0x74, 0x6c, 0xf6, 0x70, 0x59, 0xa8, 0x0a, 0x2b, 0xa5,
	0xad, 0xcc, 0x27, 0x7f, 0x5b, 0xbe, 0xd4, 0xe6, 0xe4, 0xe8, 0x0e, 0x14, 0x1d, 0xac, 0x29, 0x9a,
	0xae, 0xf6, 0x2d, 0x53, 0x73, 0xcb, 0x29, 0x0e, 0x57, 0x70, 0xb0, 0xb6, 0xed, 0x2b, 0x50, 0x05,
	0x66, 0xfa, 0x58, 0x53, 0x8d, 0x72, 0x9a, 0x43, 0x30, 0x91, 0xf4, 0x3b, 0x01, 0xc4, 0x47, 0xaa,
	0xa9, 0x19, 0xd8, 0xb7, 0xa0, 0x8d, 0x9f, 0xa3, 0x45, 0x48, 0x0f, 0x74, 0xad, 0x2c, 0x54, 0x53,
	0x21, 0x9c, 0x08, 0xd0, 0xab, 0x30, 0xdb, 0xeb, 0x6b, 0x8a, 0xae, 0x95, 0x53, 0x9c, 0x6a, 0xa6,
	0xd7, 0xd7, 0x1a, 0x1a, 0x5a, 0x86, 0x1c, 0x51, 0x1e, 0x5a, 0xda, 0x59, 0x39, 0x5d, 0x4d, 0xad,
	0x14, 0x7d, 0x75, 0xb6, 0xd7, 0xd7, 0xb6, 0x2c, 0xed, 0x0c, 0xdd, 0x82, 0x42, 0xcf, 0xd0, 0xb1,
	0xe9, 0x29, 0xc4, 0xf5, 0x72, 0x86, 0x77, 0x8b, 0x29, 0xba, 0x67, 0x36, 0x46, 0x37, 0xc0, 0x7f,
	0x52, 0x4e, 0xb0, 0x53, 0x9e, 0xe1, 0x50, 0x79, 0x26, 0x7f, 0x8c, 0x1d, 0xe9, 0x32, 0x2c, 0xc4,
	0xac, 0x76, 0x6d, 0xa9, 0x0b, 0x57, 0x98, 0x70, 0x67, 0xa0, 0x1b, 0xda, 0xd7, 0x2d, 0xdd, 0x9c,
	0xc2, 0xa7, 0x65, 0xc8, 0x1d, 0x13, 0x78, 0xdc, 0xab, 0x2c, 0x95, 0x36, 0x34, 0xe9, 0x2a, 0x54,
	0xce, 0x9b, 0xd5, 0xb5, 0xa5, 0x07, 0xc1, 0x9a, 0x5b, 0xba, 0xa9, 0xed, 0x3f, 0xb5, 0xcc, 0x29,
	0xe2, 0x28, 0x7d, 0x4f, 0x80, 0x6b, 0x6c, 0x54, 0xfd, 0xa9, 0x6a, 0x9a, 0xd8, 0xd8, 0x33, 0x0d,
	0x7d, 0x9a, 0x91, 0x34, 0x38, 0x6c, 0x48, 0xdc, 0xde, 0xbc, 0x2f, 0x6f, 0x68, 0x24, 0x31, 0x02,
	0x10, 0x8d, 0x34, 0xbf, 0xed, 0x05, 0x5f, 0x43, 0x42, 0x2d, 0x55, 0x61, 0xe9, 0x22, 0x33, 0x5c,
	0x5b, 0xfa, 0xa5, 0x10, 0xa6, 0xe6, 0x8e, 0x63, 0x0d, 0x6c, 0x1a, 0x2e, 0xf2, 0x43, 0x89, 0x59,
	0x97, 0xa5, 0xd2, 0x86, 0x86, 0xca, 0x90, 0x31, 0xd5, 0x3e, 0xa6, 0xb6, 0xe5, 0x7d, 0x25, 0x95,
	0x10, 0x8d, 0x6f, 0xce, 0x68, 0x18, 0x95, 0xa0, 0x25, 0xc8, 0x9e, 0xe8, 0xae, 0x7e, 0x68, 0x90,
	0xac, 0x48, 0xad, 0xe4, 0x82, 0x39, 0x7d, 0x21, 0xaa, 0x42, 0x6e, 0x60, 0x1a, 0x56, 0xef, 0x19,
	0xd6, 0xca, 0x33, 0x1c, 0x20, 0x94, 0x4a, 0x3f, 0xcc, 0x42, 0xd6, 0xb7, 0x93, 0xc4, 0xc8, 0xaf,
	0xcb, 0xb8, 0x91, 0x79, 0x5f, 0x7e, 0xa1, 0x99, 0x37, 0x00, 0x5c, 0x4f, 0x75, 0x3c, 0xc5, 0xd3,
	0xfb, 0x51, 0x63, 0xf3, 0x54, 0xde, 0xd5, 0xfb, 0x98, 0xe4, 0x32, 0x1e, 0xda, 0xba, 0x83, 0x19,
	0x2a, 0xc3, 0xa1, 0x80, 0x29, 0x28, 0xec, 0x75, 0x98, 0x39, 0x24, 0x15, 0x4d, 0xad, 0x2e, 0xdc,
	0xaf, 0xac, 0xf1, 0xcd, 0x61, 0x8d, 0xaf, 0xf9, 0x36, 0x03, 0xa2, 0xab, 0x30, 0xeb, 0x7a, 0xaa,
	0x37, 0x70, 0xcb, 0xb3, 0xdc, 0x9c, 0xbe, 0x8c, 0xec, 0xec, 0x91, 0x6e, 0xea, 0xee, 0x53, 0xa5,
	0x67, 0x0d, 0x4c, 0xaf, 0x9c, 0xe5, 0x30, 0x05, 0xa6, 0xa9, 0x13, 0x05, 0x7a, 0x0d, 0xe6, 0x1c,
	0xfc, 0x7c, 0xa0, 0x93, 0x06, 0xc1, 0xa0, 0x39, 0x0e, 0x5a, 0x0a, 0x74, 0x0c, 0x7c, 0x1b, 0x0a,
	0x1a, 0x76, 0x7b, 0x8e, 0x6e, 0x7b, 0xba, 0x65, 0x96, 0xf3, 0x5c, 0x48, 0x78, 0x05, 0xd9, 0x06,
	0xd7, 0x73, 0x54, 0x0f, 0x1f, 0x9f, 0x95, 0x81, 0x03, 0x85, 0x52, 0xd2, 0xb8, 0x74, 0x0d, 0x9b,
	0x9e, 0x7e, 0xa4, 0x63, 0xa7, 0x5c, 0xe0, 0x30, 0x9c, 0x9c, 0xab, 0xf0, 0x81, 0x63, 0x94, 0x8b,
	0x55, 0x21, 0x44, 0xf9, 0x15, 0x7e, 0xe0, 0x18, 0xac, 0xbb, 0x9d, 0xaa, 0x4e, 0x60, 0x7f, 0x29,
	0xda, 0xdd, 0x88, 0x86, 0x59, 0x7f, 0x07, 0x8a, 0xf8, 0xe8, 0x08, 0xf7, 0x3c, 0xe5, 0x10, 0x1f,
	0xeb, 0x66, 0x79, 0x8e, 0x07, 0x32, 0xcd, 0x16, 0x51, 0x90, 0x65, 0x7d, 0x20, 0x36, 0xb5, 0xf2,
	0x3c, 0xdf, 0x58, 0x98, 0x5c, 0x36, 0x35, 0x74, 0x1d, 0xf2, 0xba, 0xab, 0xa8, 0xbd, 0x1e, 0xb6,
	0xbd, 0xb2, 0xc8, 0x61, 0x72, 0xba, 0xbb, 0x49, 0xa5, 0xa4, 0x04, 0xdc, 0xc1, 0xa1, 0x42, 0xd3,
	0x67, 0x81, 0x33, 0x3e, 0xeb, 0x0e, 0x0e, 0x5b, 0x24, 0x83, 0xee, 0x03, 0x72, 0x6d, 0xdc, 0xd3,
	0x55, 0x43, 0xa1, 0x9b, 0xca, 0xa0, 0x88, 0x83, 0x8a, 0xbe, 0x9e, 0x6e, 0x3d, 0x1d, 0xf3, 0x36,
	0x94, 0x22, 0x63, 0xca, 0x97, 0xab, 0xc2, 0x84, 0x8c, 0x29, 0xf2, 0x93, 0xa0, 0xd7, 0x61, 0xe1,
	0x50, 0x75, 0xb1, 0x12, 0x09, 0xda, 0x4b, 0x9c, 0x03, 0xf3, 0x44, 0xdd, 0xe6, 0x02, 0xb7, 0x02,
	0xa5, 0xc0, 0x0f, 0xd6, 0x27, 0x5e, 0xe6, 0x23, 0xe7, 0x3b, 0x43, 0x5b, 0xf2, 0xeb, 0xb0, 0xd0,
	0x57, 0x87, 0x4a, 0xcf, 0x32, 0x0c, 0x12, 0x3e, 0x36, 0xf7, 0x22, 0x3f, 0x77, 0x5f, 0x1d, 0xd6,
	0x99, 0x96, 0xce, 0x2d, 0x7d, 0x20, 0x00, 0xe2, 0xfb, 0xc6, 0x36, 0xf6, 0x54, 0xdd, 0x20, 0xf5,
	0x40, 0xfb, 0x04, 0xad, 0xca, 0xf3, 0xbc, 0xa3, 0x03, 0xda, 0x0c, 0x88, 0xde, 0x82, 0x62, 0x50,
	0xcc, 0x86, 0xee, 0x7a, 0xe5, 0x54, 0x35, 0xbd, 0x52, 0xb8, 0xff, 0x72, 0xe2, 0xc0, 0x76, 0xc1,
	0x87, 0x36, 0x75, 0xd7, 0x93, 0x56, 0x21, 0xb7, 0x6f, 0xa8, 0xde, 0x91, 0xe5, 0xf4, 0xa5, 0x25,
	0x98, 0x7d, 0xac, 0x1a, 0x03, 0xec, 0xa2, 0x02, 0x64, 0x37, 0x4d, 0xcd, 0xb1, 0x74, 0x4d, 0x14,
	0x50, 0x16, 0xd2, 0xfa, 0x5e, 0x47, 0x4c, 0x49, 0xbf, 0x11, 0x00, 0xed, 0x60, 0xef, 0xc0, 0xc5,
	0x8e, 0x3f, 0x97, 0x7b, 0x51, 0x17, 0xae, 0x42, 0xce, 0xf6, 0xa7, 0x8e, 0x9c, 0xba, 0xa1, 0x94,
	0xd4, 0xdf, 0xe8, 0x10, 0x23, 0xf3, 0x45, 0x9a, 0x70, 0x29, 0x3c, 0xc8, 0x68, 0xc3, 0x5a, 0x03,
	0xd1, 0x7d, 0x6a, 0x9d, 0x2a, 0xc7, 0x64, 0x27, 0x7a, 0xd8, 0xf4, 0xb0, 0x43, 0x4f, 0xc7, 0xa0,
	0xcd, 0xcd, 0x11, 0xed, 0x8e, 0xda, 0xc7, 0x75, 0xaa, 0x93, 0x30, 0x5c, 0x1e, 0x33, 0xd6, 0xb5,
	0x51, 0x0b, 0x50, 0x10, 0x2a, 0xd6, 0xa2, 0x69, 0xc0, 0x04, 0x1a, 0xb0, 0xea, 0xf9, 0x91, 0x66,
	0x5b, 0xd3, 0x16, 0xfb, 0x9c, 0x8c, 0x06, 0x70, 0x08, 0xaf, 0xec, 0x60, 0xaf, 0xa9, 0x7a, 0xd8,
	0xe5, 0x17, 0x9b, 0x70, 0x3c, 0x71, 0xad, 0x37, 0x95, 0xdc, 0x7a, 0xf9, 0xe8, 0xa5, 0x93, 0xa2,
	0x27, 0xa9, 0x50, 0x4e, 0x5e, 0xd9, 0xb5, 0x91, 0x0c, 0xa5, 0x88, 0x97, 0x94, 0x1e, 0x4d, 0xe3,
	0x60, 0x91, 0x77, 0x50, 0xfa, 0xbd, 0x00, 0x8b, 0x7e, 0xc6, 0x46, 0x8a, 0xea, 0x02, 0xe7, 0x1e,
	0x8c, 0xe2, 0xcb, 0x35, 0x39, 0xfe, 0x00, 0x59, 0x08, 0x9d, 0x0c, 0x7b, 0xdd, 0x44, 0x67, 0x5f,
	0x78, 0xf7, 0x7f, 0x9c, 0x86, 0x57, 0x12, 0x2d, 0x77, 0x6d, 0xf4, 0x00, 0xf2, 0xc7, 0x8e, 0x75,
	0xaa, 0xe8, 0xe6, 0x91, 0xe5, 0xd7, 0xd8, 0x62, 0x34, 0x30, 0x3b, 0x8e, 0x75, 0xda, 0x30, 0x8f,
	0xac, 0x36, 0x39, 0xc6, 0xe9, 0x2f, 0xf4, 0x55, 0x00, 0xda, 0x39, 0x58, 0xdf, 0x49, 0x4d, 0x3c,
	0xa9, 0xf2, 0x04, 0xcd, 0x9a, 0xce, 0xdb, 0x30, 0x4f, 0x69, 0xa4, 0x72, 0x38, 0x38, 0x3a, 0x62,
	0xf9, 0x96, 0xa6, 0xf9, 0xf6, 0x4a, 0x6c, 0x3c, 0x01, 0x6d, 0x0d, 0x8e, 0x8e, 0xda, 0xa5, 0x7e,
	0xf0, 0x93, 0xe4, 0x18, 0xba, 0x0d, 0x45, 0x13, 0x9f, 0x2a, 0x6c, 0x12, 0x5d, 0x8b, 0x92, 0x42,
	0x13, 0x9f, 0xd2, 0x81, 0x8d, 0x78, 0x62, 0x45, 0x48, 0xe1, 0x28, 0xb1, 0xbe, 0x06, 0xe4, 0x0c,
	0x0c, 0x3b, 0xe8, 0xec, 0xc4, 0x0e, 0x0a, 0x14, 0xce, 0x5c, 0xa9, 0x41, 0xc9, 0xb4, 0x98, 0x1f,
	0x6c, 0x78, 0x76, 0xe2, 0xf0, 0x82, 0x69, 0x11, 0x47, 0xe8, 0x83, 0xf4, 0x57, 0x01, 0x72, 0x41,
	0x70, 0x49, 0x0a, 0xe1, 0x21, 0xeb, 0x72, 0x33, 0x41, 0x0a, 0xe1, 0xa1, 0x4d, 0x98, 0xb8, 0x81,
	0x4f, 0xb0, 0x11, 0xe5, 0xcf, 0x54, 0x84, 0xbe, 0x02, 0x2f, 0xf7, 0x06, 0x8e, 0x43, 0x7a, 0x06,
	0x15, 0x28, 0x78, 0x68, 0x2b, 0x7d, 0xdd, 0x8c, 0x50, 0x10, 0xe4, 0x43, 0x9a, 0x04, 0x21, 0x0f,
	0xed, 0x5d, 0xdd, 0x3c, 0x67, 0xa0, 0x3a, 0x8c, 0xb0, 0x92, 0xb1, 0x81, 0xea, 0x90, 0xe4, 0x26,
	0x93, 0xf6, 0xce, 0x28, 0x41, 0x09, 0x4c, 0x0d, 0xa5, 0xd2, 0x77, 0x04, 0x58, 0x8c, 0xb6, 0x9a,
	0x9d, 0x81, 0xae, 0xe1, 0xcf, 0xd7, 0x1b, 0x93, 0x12, 0x3e, 0x7d, 0x41, 0xc2, 0xbf, 0x49, 0xfb,
	0xd0, 0xb8, 0x0d, 0x2e, 0x8d, 0xe7, 0x31, 0x79, 0xa0, 0x66, 0x04, 0x55, 0xc8, 0x44, 0xd2, 0x37,
	0x21, 0x4f, 0x33, 0x4e, 0xf5, 0x74, 0x6b, 0xec, 0x46, 0x95, 0x9a, 0xf2, 0x46, 0x95, 0x4a, 0xbc,
	0x51, 0x49, 0x07, 0x90, 0x21, 0x73, 0x7f, 0xd1, 0xd3, 0xea, 0x30, 0x37, 0xaa, 0x14, 0x6a, 0xf7,
	0x32, 0xe4, 0xc2, 0xda, 0x88, 0xd0, 0xed, 0xbe, 0x5f, 0x18, 0x5f, 0x06, 0xa0, 0x39, 0xeb, 0x10,
	0xb8, 0x5f, 0xbc, 0xb1, 0xe2, 0x0b, 0x67, 0x6b, 0xe7, 0x0f, 0x83, 0x9f, 0x52, 0x17, 0xf2, 0xe1,
	0x52, 0x93, 0x57, 0xb9, 0x0d, 0x19, 0x32, 0xd4, 0x9f, 0x1f, 0x25, 0xcc, 0x4f, 0xf5, 0xd2, 0x3f,
	0x67, 0xa0, 0xd4, 0xf1, 0x54, 0x4f, 0xef, 0x7d, 0x41, 0x64, 0x3c, 0x46, 0x4d, 0xd3, 0xe7, 0x51,
	0xd3, 0x38, 0x09, 0xe4, 0x53, 0xff, 0x02, 0x12, 0x38, 0xc3, 0xdb, 0x33, 0x22, 0x81, 0xf7, 0x49,
	0xa7, 0x37, 0x95, 0xd8, 0x09, 0xce, 0x13, 0x72, 0xb1, 0xaf, 0x9b, 0xf5, 0xc8, 0x21, 0xbe, 0x0c,
	0x39, 0x55, 0xd3, 0xd8, 0x75, 0x80, 0xa7, 0xe5, 0x59, 0x55, 0xd3, 0xa2, 0x77, 0x81, 0xdc, 0xb4,
	0x77, 0x81, 0x1b, 0x00, 0x0e, 0x76, 0xb1, 0x7f, 0x13, 0xc9, 0xf3, 0x4d, 0x8f, 0xca, 0xe9, 0xb4,
	0xc4, 0xa1, 0x93, 0xf0, 0x52, 0x0d, 0x11, 0x56, 0x7b, 0x12, 0xdc, 0xa9, 0x7d, 0xca, 0x4a, 0x21,
	0x05, 0x0e, 0x42, 0x28, 0x2b, 0x05, 0xdc, 0x82, 0x79, 0xe3, 0x8c, 0x55, 0xa4, 0xae, 0xb1, 0x46,
	0x5e, 0xac, 0xa6, 0x57, 0x32, 0xed, 0xa2, 0x71, 0x46, 0x4a, 0xb1, 0xa1, 0xd1, 0x76, 0xcd, 0x53,
	0xdf, 0x52, 0x12, 0xf5, 0xa5, 0xf7, 0x8e, 0xde, 0x53, 0xd5, 0x39, 0xc6, 0x3e, 0x4d, 0xe4, 0xe9,
	0x78, 0x29, 0xd0, 0x31, 0x02, 0x9a, 0xcc, 0x93, 0xe7, 0x5f, 0x8c, 0x27, 0x8b, 0x2f, 0xc8, 0x93,
	0xc7, 0x58, 0xef, 0xc2, 0x79, 0xac, 0x97, 0xef, 0x64, 0x28, 0x91, 0xa7, 0xfc, 0x5a, 0x00, 0x14,
	0x49, 0xf7, 0xff, 0xe2, 0x1d, 0xb9, 0x16, 0x23, 0xc2, 0x19, 0x7a, 0xce, 0xbe, 0x1a, 0xf5, 0x3b,
	0x62, 0x4c, 0x94, 0x0e, 0xff, 0x40, 0x80, 0xcb, 0x11, 0x75, 0xdd, 0x32, 0x8f, 0xf4, 0x63, 0x72,
	0x93, 0x1d, 0xd8, 0x9a, 0xea, 0xf9, 0x37, 0xd9, 0x48, 0x0f, 0x63, 0x0a, 0x9a, 0x66, 0xc9, 0xe4,
	0x32, 0x95, 0x44, 0x2e, 0xc7, 0x23, 0x92, 0x40, 0x2e, 0xd7, 0x29, 0x87, 0x8d, 0xda, 0x8b, 0x9f,
	0x4f, 0xd5, 0x2e, 0xa4, 0x5d, 0x78, 0x69, 0x7c, 0xac, 0x6b, 0xa3, 0x37, 0x21, 0xeb, 0x83, 0x7c,
	0xee, 0x73, 0x61, 0x74, 0x02, 0xac, 0x34, 0x84, 0x6b, 0x0d, 0xb3, 0xe7, 0x60, 0xd5, 0x0d, 0x5e,
	0x7d, 0x3c, 0x1c, 0xdd, 0xa4, 0x3f, 0x37, 0xdb, 0xad, 0xc0, 0x0c, 0x2b, 0x84, 0x74, 0xe4, 0x95,
	0x19, 0xbd, 0x25, 0x39, 0x70, 0xa5, 0x4d, 0x0a, 0xb9, 0x61, 0xee, 0x3b, 0xd6, 0xb1, 0x83, 0x5d,
	0xf7, 0x3f, 0xe0, 0xd8, 0xc2, 0x39, 0x1d, 0x75, 0xec, 0xd5, 0x0f, 0x95, 0x48, 0x27, 0xc1, 0x0b,
	0x2b, 0xb2, 0xad, 0x4d, 0xbd, 0xaf, 0x7b, 0x5f, 0x14, 0xaf, 0xbf, 0x0a, 0xb3, 0x78, 0xe8, 0x91,
	0xb6, 0x9a, 0xe6, 0x4a, 0xd8, 0x97, 0x8d, 0x5e, 0xa3, 0x8d, 0xaf, 0xeb, 0xda, 0xd2, 0x73, 0x10,
	0xd9, 0xed, 0xfa, 0x7f, 0x77, 0xc9, 0x38, 0x85, 0x6a, 0x98, 0x45, 0xa1, 0x4d, 0xa4, 0xd7, 0x71,
	0x26, 0xac, 0x81, 0x68, 0xa8, 0xae, 0xa7, 0x44, 0x2b, 0x64, 0x34, 0xdb, 0x1c, 0xd1, 0x1e, 0x8c,
	0xaa, 0x24, 0xda, 0x8c, 0x53, 0x89, 0xcd, 0x58, 0xfa, 0x50, 0x80, 0xeb, 0x13, 0x56, 0x76, 0xed,
	0x69, 0xeb, 0xb2, 0x96, 0x78, 0x3f, 0x9e, 0xbe, 0x2d, 0xfc, 0x4b, 0x80, 0x39, 0xb6, 0x2f, 0x32,
	0x31, 0x70, 0x42, 0xdc, 0x23, 0xce, 0xa5, 0x92, 0x4e, 0x1a, 0x09, 0x60, 0x74, 0x90, 0xd0, 0xc8,
	0x67, 0x82, 0xc8, 0x07, 0x27, 0x49, 0xe4, 0x90, 0xb0, 0xb1, 0x69, 0x9e, 0x45, 0x68, 0x7f, 0x78,
	0x48, 0xec, 0x13, 0xd5, 0x88, 0x32, 0xf3, 0xa4, 0xdf, 0xa7, 0xcc, 0x5f, 0x82, 0xcb, 0x3d, 0xd5,
	0x7d, 0xaa, 0xc4, 0x66, 0x9b, 0xe5, 0x90, 0x0b, 0x04, 0xd0, 0xe6, 0x67, 0x94, 0x6a, 0x70, 0xd3,
	0xf7, 0x98, 0xd0, 0x4b, 0xff, 0xd5, 0xe7, 0x43, 0xc7, 0xea, 0xb7, 0x55, 0xf3, 0xd9, 0x14, 0x6f,
	0x6f, 0xef, 0xc0, 0xad, 0x29, 0xc6, 0xbb, 0xf6, 0xea, 0x26, 0x14, 0x77, 0x1b, 0x9d, 0x4e, 0x63,
	0xaf, 0xa5, 0x74, 0x9f, 0xec, 0xcb, 0xa8, 0x00, 0xd9, 0x9d, 0xb6, 0x2c, 0xb7, 0xe4, 0xb6, 0x28,
	0xa0, 0x3c, 0xcc, 0x6c, 0x6f, 0x36, 0x9a, 0x4f, 0xc4, 0x14, 0xf9, 0x29, 0x37, 0x1b, 0x5d, 0x59,
	0x4c, 0xa3, 0x39, 0x80, 0x6e, 0x63, 0x57, 0x56, 0x9a, 0x8d, 0xdd, 0x46, 0x57, 0xcc, 0xac, 0xfe,
	0x39, 0x05, 0xaf, 0x8e, 0x04, 0x4a, 0x30, 0x5d, 0xe7, 0x60, 0x8b, 0x4d, 0x79, 0x13, 0xaa, 0x9c,
	0x7a, 0x67, 0x73, 0x57, 0x56, 0xea, 0x7b, 0xad, 0x6e, 0xa3, 0x75, 0xb0, 0x77, 0xd0, 0x51, 0x9a,
	0x7b, 0x3b, 0x8d, 0x96, 0x28, 0xa0, 0xe5, 0xc8, 0x24, 0x14, 0xb5, 0x59, 0xaf, 0x1f, 0xec, 0xfa,
	0x80, 0x14, 0x92, 0x60, 0x29, 0x19, 0xd0, 0x96, 0xeb, 0x8f, 0x36, 0xdb, 0x3b, 0xc4, 0xb4, 0xeb,
	0x70, 0x8d, 0xc3, 0xb0, 0x55, 0xc8, 0x22, 0xf5, 0x47, 0x72, 0xfd, 0x1d, 0xa5, 0xd1, 0x12, 0x33,
	0x31, 0xc8, 0xa3, 0xcd, 0xfd, 0xfd, 0x27, 0x4a, 0x5d, 0x6e, 0x75, 0xe5, 0xb6, 0xd2, 0x94, 0x1f,
	0xcb, 0x4d, 0x71, 0x26, 0xb6, 0x52, 0xb7, 0x3b, 0x6e, 0xee, 0x2c, 0xba, 0x06, 0x57, 0xa2, 0x18,
	0xde, 0xd8, 0x6c, 0x4c, 0xcd, 0x74, 0xa1, 0x11, 0x39, 0x74, 0x15, 0xca, 0x71, 0x5f, 0x42, 0x2f,
	0xf2, 0xab, 0x3a, 0x80, 0xfc, 0x58, 0x6e, 0x75, 0x59, 0xf8, 0xe6, 0x00, 0x28, 0x20, 0x08, 0xd4,
	0x02, 0x94, 0xa2, 0x03, 0x52, 0x08, 0xc1, 0xdc, 0xce, 0x41, 0xa3, 0xb9, 0x3d, 0x5a, 0x22, 0x8d,
	0x16, 0x01, 0x25, 0x38, 0x97, 0x41, 0x45, 0xc8, 0x75, 0xbb, 0xfe, 0x64, 0x33, 0xab, 0x35, 0x98,
	0x0b, 0xf7, 0xab, 0xbb, 0xd9, 0x3d, 0xe8, 0xa0, 0x79, 0x28, 0x34, 0x5a, 0xca, 0x7e, 0x7b, 0x6f,
	0xa7, 0x2d, 0x77, 0x3a, 0xa2, 0x40, 0x06, 0x3c, 0x6c, 0xb4, 0x1a, 0x9d, 0x47, 0xf2, 0xb6, 0x98,
	0x42, 0x25, 0xc8, 0xd7, 0xf7, 0x9a, 0x4d, 0xb9, 0xde, 0x95, 0xb7, 0xc5, 0xf4, 0xea, 0x1b, 0x50,
	0x68, 0xed, 0x75, 0x1b, 0x0f, 0x9f, 0x28, 0xbb, 0x9b, 0x9d, 0x77, 0x50, 0x0e, 0x32, 0xad, 0xbd,
	0x96, 0x2c, 0x5e, 0x22, 0xb8, 0xb6, 0xbc, 0xad, 0xec, 0xef, 0x35, 0x5a, 0x5d, 0x96, 0x49, 0x9b,
	0xcd, 0xae, 0xdc, 0x16, 0x53, 0xab, 0xb7, 0xe1, 0xa5, 0xb6, 0xdc, 0x91, 0x47, 0x89, 0xd2, 0x96,
	0x37, 0x3b, 0x7b, 0x2d, 0xea, 0x27, 0x75, 0xe2, 0x1b, 0x07, 0x8d, 0xae, 0x28, 0xdc, 0xff, 0xed,
	0x65, 0x88, 0x7c, 0xa7, 0x42, 0x2a, 0x94, 0x22, 0xdf, 0x53, 0xd0, 0x52, 0xb4, 0x83, 0xc4, 0x3f,
	0x11, 0x55, 0x96, 0x2f, 0xd4, 0xbb, 0xb6, 0x34, 0xff, 0xc1, 0xc7, 0x9f, 0xa5, 0x85, 0x0f, 0x3f,
	0xfe, 0x2c, 0x7d, 0xe9, 0x23, 0xf2, 0x07, 0x3d, 0x87, 0xf9, 0xd8, 0x5b, 0x2b, 0x14, 0x23, 0x0e,
	0xe3, 0x6f, 0xe0, 0x2a, 0xd7, 0x27, 0x20, 0x5c, 0x5b, 0xba, 0x42, 0x16, 0x4a, 0x91, 0x85, 0x52,
	0x83, 0x75, 0xb2, 0x54, 0xee, 0xee, 0xa0, 0xba, 0x31, 0xd0, 0xb5, 0x1a, 0x72, 0xe1, 0x72, 0xc2,
	0x9b, 0x12, 0x74, 0x33, 0x3a, 0x69, 0xf2, 0x6b, 0xa0, 0xca, 0xad, 0x29, 0x50, 0x81, 0x9f, 0x69,
	0xce, 0xcf, 0x6f, 0xc7, 0xdf, 0xce, 0xd1, 0xeb, 0x6a, 0x7c, 0xd1, 0xe4, 0x5b, 0x75, 0x7c, 0xd1,
	0x73, 0xee, 0xbd, 0xcc, 0xe7, 0x4c, 0xa2, 0xcf, 0xdf, 0xa2, 0x37, 0xf6, 0x24, 0xa6, 0x77, 0x75,
	0x2d, 0xfc, 0x0a, 0xb9, 0xd6, 0x79, 0x67, 0x8b, 0x7d, 0x85, 0x94, 0xfb, 0xb6, 0x77, 0xa6, 0xec,
	0x6f, 0xc5, 0x23, 0x9d, 0x30, 0x01, 0x73, 0x75, 0x86, 0x73, 0xf5, 0x7d, 0x10, 0xe3, 0x6b, 0xa1,
	0xf1, 0x1d, 0x8b, 0x93, 0xbc, 0x8a, 0x34, 0x09, 0xe2, 0xda, 0xd2, 0x32, 0x59, 0x6b, 0x96, 0x7a,
	0xd8, 0xa7, 0x1e, 0xce, 0xdd, 0xed, 0x57, 0x37, 0xc2, 0xf3, 0xbd, 0x86, 0x7e, 0x22, 0xc0, 0x62,
	0xf2, 0x77, 0x39, 0x74, 0x27, 0x29, 0x37, 0x13, 0xbe, 0x09, 0x56, 0x56, 0xa6, 0x03, 0xba, 0xb6,
	0xf4, 0x7f, 0xc4, 0x9c, 0x2c, 0x31, 0x27, 0x33, 0x58, 0x3f, 0xa6, 0x06, 0x2d, 0x06, 0x21, 0xaf,
	0xde, 0x3d, 0xae, 0x6e, 0x04, 0xdf, 0x10, 0x6b, 0xe8, 0x67, 0x02, 0x54, 0xce, 0xe7, 0x93, 0xe8,
	0xb5, 0xe8, 0x9a, 0x17, 0x32, 0xcf, 0xca, 0x85, 0x5b, 0x26, 0xbd, 0x45, 0x8c, 0xca, 0x11, 0xa3,
	0x66, 0x07, 0xeb, 0xfd, 0x75, 0x93, 0x9a, 0x75, 0x63, 0x64, 0xd6, 0x28, 0x60, 0x0a, 0x7d, 0x36,
	0xab, 0x1b, 0x94, 0x77, 0xd6, 0xd0, 0xfb, 0x41, 0xec, 0xe2, 0x5f, 0x2d, 0x93, 0x63, 0x97, 0xf0,
	0x6d, 0x73, 0x82, 0x69, 0x34, 0x41, 0xf3, 0x89, 0x09, 0xfa, 0x91, 0x40, 0xe9, 0xfb, 0xd8, 0xdb,
	0x5d, 0x34, 0x9e, 0xfb, 0x49, 0xef, 0x9e, 0x2b, 0xb7, 0xa7, 0x81, 0xb9, 0xb6, 0xf4, 0x1a, 0x31,
	0x01, 0xfc, 0x2d, 0x63, 0x39, 0x54, 0x3e, 0x2f, 0x36, 0xe8, 0xbb, 0x02, 0x94, 0x22, 0x04, 0x34,
	0xde, 0x00, 0xe3, 0xec, 0xf4, 0xc5, 0xcc, 0x28, 0x4c, 0x69, 0xc6, 0x4f, 0xc3, 0xa4, 0x8e, 0xb3,
	0xe4, 0xe4, 0x8d, 0x49, 0xe0, 0xf0, 0xc9, 0x49, 0x9d, 0x48, 0xba, 0xa9, 0x69, 0xc5, 0x29, 0x4d,
	0xfb, 0xb9, 0x00, 0xd7, 0x2e, 0x64, 0xad, 0x68, 0xed, 0x9c, 0xb2, 0x3e, 0x87, 0x5c, 0x57, 0xee,
	0xbd, 0x10, 0xde, 0xb5, 0xa5, 0x2a, 0xb1, 0xb7, 0x44, 0x93, 0xca, 0xa3, 0xd6, 0xce, 0xdf, 0xf5,
	0xaa, 0x1b, 0x23, 0x4e, 0x5a, 0x43, 0x7f, 0x14, 0xa0, 0xc0, 0xb1, 0x59, 0x74, 0x35, 0x29, 0x16,
	0x01, 0xd1, 0x9d, 0x90, 0xc2, 0x43, 0xb2, 0xda, 0x1c, 0x59, 0x0d, 0x06, 0xeb, 0xde, 0xfa, 0xf1,
	0xba, 0xb9, 0x6e, 0xd0, 0x55, 0x95, 0x51, 0x8c, 0xa2, 0xcb, 0x57, 0xdf, 0xa5, 0x9d, 0x80, 0x71,
	0x5f, 0x56, 0x6f, 0x51, 0x66, 0xca, 0xa2, 0x9a, 0x40, 0x59, 0x6b, 0xd5, 0xbb, 0x46, 0x75, 0x83,
	0x92, 0xda, 0xda, 0x7b, 0xe8, 0x17, 0x02, 0x2c, 0x26, 0xdf, 0x0b, 0xe3, 0x59, 0x70, 0xee, 0xed,
	0x71, 0x82, 0x6f, 0x5b, 0xc4, 0xb7, 0xf9, 0xb0, 0x73, 0xb0, 0x68, 0xde, 0x0d, 0xfd, 0x7a, 0x97,
	0x38, 0x46, 0x5c, 0xaa, 0xbe, 0xb1, 0x46, 0x1b, 0x5b, 0xad, 0x1a, 0xcf, 0x87, 0xf7, 0xd0, 0x1f,
	0x84, 0xe0, 0x46, 0x97, 0xf4, 0xdf, 0x03, 0xf1, 0x3e, 0x77, 0xe1, 0xbf, 0x3b, 0x54, 0xfe, 0x7f,
	0x7a, 0xb0, 0x6b, 0x4b, 0x6f, 0x13, 0xeb, 0x45, 0xdf, 0xfa, 0xa1, 0x6f, 0xfd, 0xea, 0x68, 0x57,
	0x86, 0xd5, 0x8d, 0xd1, 0x3f, 0x49, 0xb0, 0x5d, 0xe2, 0xff, 0x1f, 0xa2, 0x86, 0x7e, 0x25, 0xc0,
	0xf5, 0x89, 0x14, 0x1e, 0xdd, 0x4f, 0x4c, 0x9e, 0x0b, 0xef, 0x0c, 0x95, 0x07, 0x2f, 0x3c, 0xc6,
	0xb5, 0xa5, 0x0a, 0xf1, 0x67, 0x81, 0x6b, 0x96, 0xf9, 0xd0, 0x97, 0xca, 0xec, 0xf7, 0x3f, 0xfe,
	0x2c, 0xfd, 0xa7, 0xc1, 0x96, 0xf8, 0xc9, 0xa7, 0x4b, 0xc2, 0x5f, 0x3e, 0x5d, 0x12, 0xfe, 0xfe,
	0xe9, 0x92, 0xf0, 0xa3, 0x7f, 0x2c, 0x5d, 0xfa, 0x77, 0x00, 0x00, 0x00, 0xff, 0xff, 0xb4, 0x6f,
	0x5d, 0x2e, 0x91, 0x24, 0x00, 0x00,
}
