// Code generated by protoc-gen-gogo.
// source: services/channeldegrader/channeldegrader.proto
// DO NOT EDIT!

/*
	Package channeldegrader is a generated protocol buffer package.

	namespace

	It is generated from these files:
		services/channeldegrader/channeldegrader.proto

	It has these top-level messages:
		CheckCmdDegradationReq
		CheckCmdDegradationResp
		UserChannel
*/
package channeldegrader

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type CheckCmdDegradationReq struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Cmd        uint32 `protobuf:"varint,2,opt,name=cmd" json:"cmd"`
	ChannelId  uint32 `protobuf:"varint,3,opt,name=channel_id,json=channelId" json:"channel_id"`
	MemberSize uint32 `protobuf:"varint,4,opt,name=member_size,json=memberSize" json:"member_size"`
}

func (m *CheckCmdDegradationReq) Reset()         { *m = CheckCmdDegradationReq{} }
func (m *CheckCmdDegradationReq) String() string { return proto.CompactTextString(m) }
func (*CheckCmdDegradationReq) ProtoMessage()    {}
func (*CheckCmdDegradationReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldegrader, []int{0}
}

func (m *CheckCmdDegradationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckCmdDegradationReq) GetCmd() uint32 {
	if m != nil {
		return m.Cmd
	}
	return 0
}

func (m *CheckCmdDegradationReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CheckCmdDegradationReq) GetMemberSize() uint32 {
	if m != nil {
		return m.MemberSize
	}
	return 0
}

type CheckCmdDegradationResp struct {
	Degradation bool `protobuf:"varint,1,opt,name=degradation" json:"degradation"`
}

func (m *CheckCmdDegradationResp) Reset()         { *m = CheckCmdDegradationResp{} }
func (m *CheckCmdDegradationResp) String() string { return proto.CompactTextString(m) }
func (*CheckCmdDegradationResp) ProtoMessage()    {}
func (*CheckCmdDegradationResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldegrader, []int{1}
}

func (m *CheckCmdDegradationResp) GetDegradation() bool {
	if m != nil {
		return m.Degradation
	}
	return false
}

type UserChannel struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	EnterAt   uint32 `protobuf:"varint,2,req,name=enter_at,json=enterAt" json:"enter_at"`
}

func (m *UserChannel) Reset()                    { *m = UserChannel{} }
func (m *UserChannel) String() string            { return proto.CompactTextString(m) }
func (*UserChannel) ProtoMessage()               {}
func (*UserChannel) Descriptor() ([]byte, []int) { return fileDescriptorChanneldegrader, []int{2} }

func (m *UserChannel) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserChannel) GetEnterAt() uint32 {
	if m != nil {
		return m.EnterAt
	}
	return 0
}

func init() {
	proto.RegisterType((*CheckCmdDegradationReq)(nil), "channeldegrader.CheckCmdDegradationReq")
	proto.RegisterType((*CheckCmdDegradationResp)(nil), "channeldegrader.CheckCmdDegradationResp")
	proto.RegisterType((*UserChannel)(nil), "channeldegrader.UserChannel")
}
func (m *CheckCmdDegradationReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckCmdDegradationReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldegrader(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldegrader(dAtA, i, uint64(m.Cmd))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChanneldegrader(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChanneldegrader(dAtA, i, uint64(m.MemberSize))
	return i, nil
}

func (m *CheckCmdDegradationResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckCmdDegradationResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.Degradation {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *UserChannel) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserChannel) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldegrader(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldegrader(dAtA, i, uint64(m.EnterAt))
	return i, nil
}

func encodeFixed64Channeldegrader(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Channeldegrader(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintChanneldegrader(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *CheckCmdDegradationReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldegrader(uint64(m.Uid))
	n += 1 + sovChanneldegrader(uint64(m.Cmd))
	n += 1 + sovChanneldegrader(uint64(m.ChannelId))
	n += 1 + sovChanneldegrader(uint64(m.MemberSize))
	return n
}

func (m *CheckCmdDegradationResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *UserChannel) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldegrader(uint64(m.ChannelId))
	n += 1 + sovChanneldegrader(uint64(m.EnterAt))
	return n
}

func sovChanneldegrader(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozChanneldegrader(x uint64) (n int) {
	return sovChanneldegrader(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *CheckCmdDegradationReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldegrader
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckCmdDegradationReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckCmdDegradationReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldegrader
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cmd", wireType)
			}
			m.Cmd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldegrader
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cmd |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldegrader
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberSize", wireType)
			}
			m.MemberSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldegrader
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemberSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldegrader(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldegrader
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckCmdDegradationResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldegrader
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckCmdDegradationResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckCmdDegradationResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Degradation", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldegrader
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Degradation = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldegrader(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldegrader
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserChannel) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldegrader
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserChannel: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserChannel: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldegrader
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EnterAt", wireType)
			}
			m.EnterAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldegrader
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EnterAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldegrader(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldegrader
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("enter_at")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipChanneldegrader(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowChanneldegrader
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChanneldegrader
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChanneldegrader
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthChanneldegrader
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowChanneldegrader
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipChanneldegrader(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthChanneldegrader = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowChanneldegrader   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("services/channeldegrader/channeldegrader.proto", fileDescriptorChanneldegrader)
}

var fileDescriptorChanneldegrader = []byte{
	// 361 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x91, 0xcf, 0x6a, 0xea, 0x40,
	0x14, 0xc6, 0x1d, 0x23, 0xf7, 0x7a, 0x47, 0x2e, 0xf7, 0x12, 0xc1, 0x06, 0x17, 0x31, 0xa4, 0xad,
	0x75, 0xa3, 0x42, 0x97, 0x22, 0x82, 0xda, 0x4d, 0xb7, 0x4a, 0x57, 0xa5, 0x48, 0x9c, 0x39, 0xd4,
	0xc1, 0x4c, 0x92, 0xce, 0x4c, 0x84, 0xba, 0xea, 0xb2, 0x74, 0x25, 0xa5, 0x4f, 0x50, 0xb2, 0xec,
	0x83, 0xb8, 0xec, 0x13, 0x94, 0x62, 0x37, 0x79, 0x8c, 0xe2, 0x1f, 0x30, 0x04, 0x17, 0x5d, 0xe6,
	0xf7, 0x7d, 0x27, 0xf3, 0x9b, 0x33, 0xb8, 0x21, 0x41, 0xcc, 0x18, 0x01, 0xd9, 0x24, 0x13, 0xc7,
	0xf3, 0xc0, 0xa5, 0x70, 0x2b, 0x1c, 0x0a, 0x22, 0xfd, 0xdd, 0x08, 0x84, 0xaf, 0x7c, 0xfd, 0x5f,
	0x0a, 0x97, 0x4f, 0x88, 0xcf, 0xb9, 0xef, 0x35, 0x95, 0x3b, 0x0b, 0x18, 0x99, 0xba, 0xd0, 0x94,
	0xd3, 0x71, 0xc8, 0x5c, 0xc5, 0x3c, 0x75, 0x1f, 0xc0, 0x76, 0xcc, 0x7e, 0x41, 0xb8, 0xd4, 0x9f,
	0x00, 0x99, 0xf6, 0x39, 0xbd, 0xd8, 0x8c, 0x3a, 0x8a, 0xf9, 0xde, 0x00, 0xee, 0xf4, 0x12, 0xd6,
	0x42, 0x46, 0x0d, 0x64, 0x65, 0x6b, 0x7f, 0x7b, 0xb9, 0xe5, 0x47, 0x25, 0x33, 0x58, 0x83, 0x35,
	0x27, 0x9c, 0x1a, 0x59, 0x0b, 0xed, 0x39, 0xe1, 0x54, 0x3f, 0xc6, 0x78, 0xe7, 0x30, 0x62, 0xd4,
	0xd0, 0x12, 0xf1, 0x9f, 0x1d, 0xbf, 0xa4, 0xfa, 0x29, 0x2e, 0x70, 0xe0, 0x63, 0x10, 0x23, 0xc9,
	0xe6, 0x60, 0xe4, 0x12, 0x2d, 0xbc, 0x0d, 0x86, 0x6c, 0x0e, 0x76, 0x17, 0x1f, 0x1d, 0xb4, 0x92,
	0x81, 0x5e, 0xc5, 0x05, 0xba, 0x47, 0x06, 0xb2, 0x50, 0x2d, 0xbf, 0xfb, 0x43, 0x32, 0xb0, 0x87,
	0xb8, 0x70, 0x25, 0x41, 0xf4, 0xb7, 0x47, 0xa7, 0xec, 0x92, 0x97, 0x4a, 0xd8, 0x55, 0x70, 0x1e,
	0x3c, 0x05, 0x62, 0xe4, 0x28, 0x23, 0x9b, 0xa8, 0xfc, 0xde, 0xd0, 0xae, 0x3a, 0x7f, 0x43, 0x38,
	0xbd, 0x68, 0x7d, 0x81, 0x70, 0xf1, 0x80, 0xac, 0x7e, 0xd6, 0x48, 0xbf, 0xd4, 0xe1, 0x45, 0x97,
	0x6b, 0x3f, 0x2b, 0xca, 0xc0, 0xae, 0x3e, 0x44, 0xb1, 0x86, 0x9e, 0xa2, 0x58, 0xcb, 0x85, 0xad,
	0xa0, 0xf5, 0x1c, 0xc5, 0x5a, 0xb1, 0x1e, 0x5a, 0xed, 0x90, 0xd1, 0x8e, 0x75, 0x5d, 0x0f, 0xac,
	0x36, 0xe1, 0xb4, 0x73, 0x53, 0xfe, 0xf5, 0x18, 0xc5, 0xda, 0xeb, 0xbc, 0xf7, 0x7f, 0xb9, 0x32,
	0xd1, 0xfb, 0xca, 0x44, 0x9f, 0x2b, 0x13, 0x2d, 0xbe, 0xcc, 0xcc, 0x77, 0x00, 0x00, 0x00, 0xff,
	0xff, 0x8e, 0x90, 0xb2, 0x70, 0x57, 0x02, 0x00, 0x00,
}
