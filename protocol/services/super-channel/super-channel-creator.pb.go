// Code generated by protoc-gen-go. DO NOT EDIT.
// source: super-channel/super-channel-creator.proto

package super_channel // import "golang.52tt.com/protocol/services/super-channel"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ChannelAdminRole int32

const (
	ChannelAdminRole_CHANNEL_INVALID_ROLE ChannelAdminRole = 0
	ChannelAdminRole_CHANNEL_OWNER        ChannelAdminRole = 1
	ChannelAdminRole_CHANNEL_ADMIN        ChannelAdminRole = 2
	ChannelAdminRole_CHANNEL_NORMAL       ChannelAdminRole = 3
	ChannelAdminRole_CHANNEL_ADMIN_SUPER  ChannelAdminRole = 4
)

var ChannelAdminRole_name = map[int32]string{
	0: "CHANNEL_INVALID_ROLE",
	1: "CHANNEL_OWNER",
	2: "CHANNEL_ADMIN",
	3: "CHANNEL_NORMAL",
	4: "CHANNEL_ADMIN_SUPER",
}
var ChannelAdminRole_value = map[string]int32{
	"CHANNEL_INVALID_ROLE": 0,
	"CHANNEL_OWNER":        1,
	"CHANNEL_ADMIN":        2,
	"CHANNEL_NORMAL":       3,
	"CHANNEL_ADMIN_SUPER":  4,
}

func (x ChannelAdminRole) String() string {
	return proto.EnumName(ChannelAdminRole_name, int32(x))
}
func (ChannelAdminRole) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{0}
}

type CreateChannelReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	ChannelType          uint32   `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateChannelReq) Reset()         { *m = CreateChannelReq{} }
func (m *CreateChannelReq) String() string { return proto.CompactTextString(m) }
func (*CreateChannelReq) ProtoMessage()    {}
func (*CreateChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{0}
}
func (m *CreateChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateChannelReq.Unmarshal(m, b)
}
func (m *CreateChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateChannelReq.Marshal(b, m, deterministic)
}
func (dst *CreateChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateChannelReq.Merge(dst, src)
}
func (m *CreateChannelReq) XXX_Size() int {
	return xxx_messageInfo_CreateChannelReq.Size(m)
}
func (m *CreateChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateChannelReq proto.InternalMessageInfo

func (m *CreateChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CreateChannelReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreateChannelReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type CreateChannelResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DisplayId            uint32   `protobuf:"varint,2,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateChannelResp) Reset()         { *m = CreateChannelResp{} }
func (m *CreateChannelResp) String() string { return proto.CompactTextString(m) }
func (*CreateChannelResp) ProtoMessage()    {}
func (*CreateChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{1}
}
func (m *CreateChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateChannelResp.Unmarshal(m, b)
}
func (m *CreateChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateChannelResp.Marshal(b, m, deterministic)
}
func (dst *CreateChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateChannelResp.Merge(dst, src)
}
func (m *CreateChannelResp) XXX_Size() int {
	return xxx_messageInfo_CreateChannelResp.Size(m)
}
func (m *CreateChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateChannelResp proto.InternalMessageInfo

func (m *CreateChannelResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CreateChannelResp) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

type RevokeChannelReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RevokeChannelReq) Reset()         { *m = RevokeChannelReq{} }
func (m *RevokeChannelReq) String() string { return proto.CompactTextString(m) }
func (*RevokeChannelReq) ProtoMessage()    {}
func (*RevokeChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{2}
}
func (m *RevokeChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevokeChannelReq.Unmarshal(m, b)
}
func (m *RevokeChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevokeChannelReq.Marshal(b, m, deterministic)
}
func (dst *RevokeChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevokeChannelReq.Merge(dst, src)
}
func (m *RevokeChannelReq) XXX_Size() int {
	return xxx_messageInfo_RevokeChannelReq.Size(m)
}
func (m *RevokeChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RevokeChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_RevokeChannelReq proto.InternalMessageInfo

func (m *RevokeChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type RevokeChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RevokeChannelResp) Reset()         { *m = RevokeChannelResp{} }
func (m *RevokeChannelResp) String() string { return proto.CompactTextString(m) }
func (*RevokeChannelResp) ProtoMessage()    {}
func (*RevokeChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{3}
}
func (m *RevokeChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevokeChannelResp.Unmarshal(m, b)
}
func (m *RevokeChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevokeChannelResp.Marshal(b, m, deterministic)
}
func (dst *RevokeChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevokeChannelResp.Merge(dst, src)
}
func (m *RevokeChannelResp) XXX_Size() int {
	return xxx_messageInfo_RevokeChannelResp.Size(m)
}
func (m *RevokeChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RevokeChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_RevokeChannelResp proto.InternalMessageInfo

type ChannelInfo struct {
	ChannelId                        uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelCreatorUid                uint32   `protobuf:"varint,2,opt,name=channel_creator_uid,json=channelCreatorUid,proto3" json:"channel_creator_uid,omitempty"`
	ChannelName                      string   `protobuf:"bytes,3,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelType                      uint32   `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	ChannelIcon                      string   `protobuf:"bytes,5,opt,name=channel_icon,json=channelIcon,proto3" json:"channel_icon,omitempty"`
	ChannelDesc                      string   `protobuf:"bytes,6,opt,name=channel_desc,json=channelDesc,proto3" json:"channel_desc,omitempty"`
	ChannelShowId                    uint32   `protobuf:"varint,7,opt,name=channel_show_id,json=channelShowId,proto3" json:"channel_show_id,omitempty"`
	IsOpenLockScreenSwitch           bool     `protobuf:"varint,8,opt,name=is_open_lock_screen_switch,json=isOpenLockScreenSwitch,proto3" json:"is_open_lock_screen_switch,omitempty"`
	IsOpenDisableAttachmentMsgSwitch bool     `protobuf:"varint,9,opt,name=is_open_disable_attachment_msg_switch,json=isOpenDisableAttachmentMsgSwitch,proto3" json:"is_open_disable_attachment_msg_switch,omitempty"`
	IsOpenDisableLevelLmtSwitch      bool     `protobuf:"varint,10,opt,name=is_open_disable_level_lmt_switch,json=isOpenDisableLevelLmtSwitch,proto3" json:"is_open_disable_level_lmt_switch,omitempty"`
	IsOpenNormalQueueUpMicSwitch     bool     `protobuf:"varint,11,opt,name=is_open_normal_queue_up_mic_switch,json=isOpenNormalQueueUpMicSwitch,proto3" json:"is_open_normal_queue_up_mic_switch,omitempty"`
	UpdateTs                         int64    `protobuf:"varint,12,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	IsLocked                         bool     `protobuf:"varint,13,opt,name=is_locked,json=isLocked,proto3" json:"is_locked,omitempty"`
	ChannelViewId                    string   `protobuf:"bytes,14,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral             struct{} `json:"-"`
	XXX_unrecognized                 []byte   `json:"-"`
	XXX_sizecache                    int32    `json:"-"`
}

func (m *ChannelInfo) Reset()         { *m = ChannelInfo{} }
func (m *ChannelInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelInfo) ProtoMessage()    {}
func (*ChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{4}
}
func (m *ChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelInfo.Unmarshal(m, b)
}
func (m *ChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelInfo.Merge(dst, src)
}
func (m *ChannelInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelInfo.Size(m)
}
func (m *ChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelInfo proto.InternalMessageInfo

func (m *ChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelInfo) GetChannelCreatorUid() uint32 {
	if m != nil {
		return m.ChannelCreatorUid
	}
	return 0
}

func (m *ChannelInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *ChannelInfo) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *ChannelInfo) GetChannelIcon() string {
	if m != nil {
		return m.ChannelIcon
	}
	return ""
}

func (m *ChannelInfo) GetChannelDesc() string {
	if m != nil {
		return m.ChannelDesc
	}
	return ""
}

func (m *ChannelInfo) GetChannelShowId() uint32 {
	if m != nil {
		return m.ChannelShowId
	}
	return 0
}

func (m *ChannelInfo) GetIsOpenLockScreenSwitch() bool {
	if m != nil {
		return m.IsOpenLockScreenSwitch
	}
	return false
}

func (m *ChannelInfo) GetIsOpenDisableAttachmentMsgSwitch() bool {
	if m != nil {
		return m.IsOpenDisableAttachmentMsgSwitch
	}
	return false
}

func (m *ChannelInfo) GetIsOpenDisableLevelLmtSwitch() bool {
	if m != nil {
		return m.IsOpenDisableLevelLmtSwitch
	}
	return false
}

func (m *ChannelInfo) GetIsOpenNormalQueueUpMicSwitch() bool {
	if m != nil {
		return m.IsOpenNormalQueueUpMicSwitch
	}
	return false
}

func (m *ChannelInfo) GetUpdateTs() int64 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *ChannelInfo) GetIsLocked() bool {
	if m != nil {
		return m.IsLocked
	}
	return false
}

func (m *ChannelInfo) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type GetChannelInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelInfoReq) Reset()         { *m = GetChannelInfoReq{} }
func (m *GetChannelInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelInfoReq) ProtoMessage()    {}
func (*GetChannelInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{5}
}
func (m *GetChannelInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelInfoReq.Unmarshal(m, b)
}
func (m *GetChannelInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelInfoReq.Merge(dst, src)
}
func (m *GetChannelInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelInfoReq.Size(m)
}
func (m *GetChannelInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelInfoReq proto.InternalMessageInfo

func (m *GetChannelInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelInfoResp struct {
	Info                 *ChannelInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelInfoResp) Reset()         { *m = GetChannelInfoResp{} }
func (m *GetChannelInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelInfoResp) ProtoMessage()    {}
func (*GetChannelInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{6}
}
func (m *GetChannelInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelInfoResp.Unmarshal(m, b)
}
func (m *GetChannelInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelInfoResp.Merge(dst, src)
}
func (m *GetChannelInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelInfoResp.Size(m)
}
func (m *GetChannelInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelInfoResp proto.InternalMessageInfo

func (m *GetChannelInfoResp) GetInfo() *ChannelInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type ListChannelInfoReq struct {
	ChannelType          uint32   `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListChannelInfoReq) Reset()         { *m = ListChannelInfoReq{} }
func (m *ListChannelInfoReq) String() string { return proto.CompactTextString(m) }
func (*ListChannelInfoReq) ProtoMessage()    {}
func (*ListChannelInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{7}
}
func (m *ListChannelInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListChannelInfoReq.Unmarshal(m, b)
}
func (m *ListChannelInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListChannelInfoReq.Marshal(b, m, deterministic)
}
func (dst *ListChannelInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListChannelInfoReq.Merge(dst, src)
}
func (m *ListChannelInfoReq) XXX_Size() int {
	return xxx_messageInfo_ListChannelInfoReq.Size(m)
}
func (m *ListChannelInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListChannelInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListChannelInfoReq proto.InternalMessageInfo

func (m *ListChannelInfoReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type ListChannelInfoResp struct {
	ChannelList          []*ChannelInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ListChannelInfoResp) Reset()         { *m = ListChannelInfoResp{} }
func (m *ListChannelInfoResp) String() string { return proto.CompactTextString(m) }
func (*ListChannelInfoResp) ProtoMessage()    {}
func (*ListChannelInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{8}
}
func (m *ListChannelInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListChannelInfoResp.Unmarshal(m, b)
}
func (m *ListChannelInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListChannelInfoResp.Marshal(b, m, deterministic)
}
func (dst *ListChannelInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListChannelInfoResp.Merge(dst, src)
}
func (m *ListChannelInfoResp) XXX_Size() int {
	return xxx_messageInfo_ListChannelInfoResp.Size(m)
}
func (m *ListChannelInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListChannelInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListChannelInfoResp proto.InternalMessageInfo

func (m *ListChannelInfoResp) GetChannelList() []*ChannelInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type ChannelBindInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          uint32   `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelBindInfo) Reset()         { *m = ChannelBindInfo{} }
func (m *ChannelBindInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelBindInfo) ProtoMessage()    {}
func (*ChannelBindInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{9}
}
func (m *ChannelBindInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelBindInfo.Unmarshal(m, b)
}
func (m *ChannelBindInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelBindInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelBindInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelBindInfo.Merge(dst, src)
}
func (m *ChannelBindInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelBindInfo.Size(m)
}
func (m *ChannelBindInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelBindInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelBindInfo proto.InternalMessageInfo

func (m *ChannelBindInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelBindInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelBindInfo) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type GetChannelBindInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelType          uint32   `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelBindInfoReq) Reset()         { *m = GetChannelBindInfoReq{} }
func (m *GetChannelBindInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelBindInfoReq) ProtoMessage()    {}
func (*GetChannelBindInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{10}
}
func (m *GetChannelBindInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelBindInfoReq.Unmarshal(m, b)
}
func (m *GetChannelBindInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelBindInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelBindInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelBindInfoReq.Merge(dst, src)
}
func (m *GetChannelBindInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelBindInfoReq.Size(m)
}
func (m *GetChannelBindInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelBindInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelBindInfoReq proto.InternalMessageInfo

func (m *GetChannelBindInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelBindInfoReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type GetChannelBindInfoResp struct {
	ChannelList          []*ChannelBindInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetChannelBindInfoResp) Reset()         { *m = GetChannelBindInfoResp{} }
func (m *GetChannelBindInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelBindInfoResp) ProtoMessage()    {}
func (*GetChannelBindInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{11}
}
func (m *GetChannelBindInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelBindInfoResp.Unmarshal(m, b)
}
func (m *GetChannelBindInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelBindInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelBindInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelBindInfoResp.Merge(dst, src)
}
func (m *GetChannelBindInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelBindInfoResp.Size(m)
}
func (m *GetChannelBindInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelBindInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelBindInfoResp proto.InternalMessageInfo

func (m *GetChannelBindInfoResp) GetChannelList() []*ChannelBindInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type ListChannelBindInfoReq struct {
	ChannelType          uint32   `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListChannelBindInfoReq) Reset()         { *m = ListChannelBindInfoReq{} }
func (m *ListChannelBindInfoReq) String() string { return proto.CompactTextString(m) }
func (*ListChannelBindInfoReq) ProtoMessage()    {}
func (*ListChannelBindInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{12}
}
func (m *ListChannelBindInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListChannelBindInfoReq.Unmarshal(m, b)
}
func (m *ListChannelBindInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListChannelBindInfoReq.Marshal(b, m, deterministic)
}
func (dst *ListChannelBindInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListChannelBindInfoReq.Merge(dst, src)
}
func (m *ListChannelBindInfoReq) XXX_Size() int {
	return xxx_messageInfo_ListChannelBindInfoReq.Size(m)
}
func (m *ListChannelBindInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListChannelBindInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListChannelBindInfoReq proto.InternalMessageInfo

func (m *ListChannelBindInfoReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type ListChannelBindInfoResp struct {
	ChannelBindList      []*ChannelBindInfo `protobuf:"bytes,1,rep,name=channel_bind_list,json=channelBindList,proto3" json:"channel_bind_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ListChannelBindInfoResp) Reset()         { *m = ListChannelBindInfoResp{} }
func (m *ListChannelBindInfoResp) String() string { return proto.CompactTextString(m) }
func (*ListChannelBindInfoResp) ProtoMessage()    {}
func (*ListChannelBindInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{13}
}
func (m *ListChannelBindInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListChannelBindInfoResp.Unmarshal(m, b)
}
func (m *ListChannelBindInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListChannelBindInfoResp.Marshal(b, m, deterministic)
}
func (dst *ListChannelBindInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListChannelBindInfoResp.Merge(dst, src)
}
func (m *ListChannelBindInfoResp) XXX_Size() int {
	return xxx_messageInfo_ListChannelBindInfoResp.Size(m)
}
func (m *ListChannelBindInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListChannelBindInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListChannelBindInfoResp proto.InternalMessageInfo

func (m *ListChannelBindInfoResp) GetChannelBindList() []*ChannelBindInfo {
	if m != nil {
		return m.ChannelBindList
	}
	return nil
}

type GetChannelAdminReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelAdminReq) Reset()         { *m = GetChannelAdminReq{} }
func (m *GetChannelAdminReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelAdminReq) ProtoMessage()    {}
func (*GetChannelAdminReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{14}
}
func (m *GetChannelAdminReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelAdminReq.Unmarshal(m, b)
}
func (m *GetChannelAdminReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelAdminReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelAdminReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelAdminReq.Merge(dst, src)
}
func (m *GetChannelAdminReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelAdminReq.Size(m)
}
func (m *GetChannelAdminReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelAdminReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelAdminReq proto.InternalMessageInfo

func (m *GetChannelAdminReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelAdminResp struct {
	Admins               map[uint32]ChannelAdminRole `protobuf:"bytes,1,rep,name=admins,proto3" json:"admins,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=super_channel.ChannelAdminRole"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetChannelAdminResp) Reset()         { *m = GetChannelAdminResp{} }
func (m *GetChannelAdminResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelAdminResp) ProtoMessage()    {}
func (*GetChannelAdminResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{15}
}
func (m *GetChannelAdminResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelAdminResp.Unmarshal(m, b)
}
func (m *GetChannelAdminResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelAdminResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelAdminResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelAdminResp.Merge(dst, src)
}
func (m *GetChannelAdminResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelAdminResp.Size(m)
}
func (m *GetChannelAdminResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelAdminResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelAdminResp proto.InternalMessageInfo

func (m *GetChannelAdminResp) GetAdmins() map[uint32]ChannelAdminRole {
	if m != nil {
		return m.Admins
	}
	return nil
}

type SetChannelAdminReq struct {
	ChannelId            uint32           `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32           `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Role                 ChannelAdminRole `protobuf:"varint,3,opt,name=role,proto3,enum=super_channel.ChannelAdminRole" json:"role,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SetChannelAdminReq) Reset()         { *m = SetChannelAdminReq{} }
func (m *SetChannelAdminReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelAdminReq) ProtoMessage()    {}
func (*SetChannelAdminReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{16}
}
func (m *SetChannelAdminReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelAdminReq.Unmarshal(m, b)
}
func (m *SetChannelAdminReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelAdminReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelAdminReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelAdminReq.Merge(dst, src)
}
func (m *SetChannelAdminReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelAdminReq.Size(m)
}
func (m *SetChannelAdminReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelAdminReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelAdminReq proto.InternalMessageInfo

func (m *SetChannelAdminReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelAdminReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelAdminReq) GetRole() ChannelAdminRole {
	if m != nil {
		return m.Role
	}
	return ChannelAdminRole_CHANNEL_INVALID_ROLE
}

type SetChannelAdminResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelAdminResp) Reset()         { *m = SetChannelAdminResp{} }
func (m *SetChannelAdminResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelAdminResp) ProtoMessage()    {}
func (*SetChannelAdminResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{17}
}
func (m *SetChannelAdminResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelAdminResp.Unmarshal(m, b)
}
func (m *SetChannelAdminResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelAdminResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelAdminResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelAdminResp.Merge(dst, src)
}
func (m *SetChannelAdminResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelAdminResp.Size(m)
}
func (m *SetChannelAdminResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelAdminResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelAdminResp proto.InternalMessageInfo

type GetActiveChannelListReq struct {
	ChannelType          uint32   `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	ActiveAfter          uint32   `protobuf:"varint,2,opt,name=active_after,json=activeAfter,proto3" json:"active_after,omitempty"`
	ActiveEnd            uint32   `protobuf:"varint,3,opt,name=active_end,json=activeEnd,proto3" json:"active_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetActiveChannelListReq) Reset()         { *m = GetActiveChannelListReq{} }
func (m *GetActiveChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetActiveChannelListReq) ProtoMessage()    {}
func (*GetActiveChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{18}
}
func (m *GetActiveChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActiveChannelListReq.Unmarshal(m, b)
}
func (m *GetActiveChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActiveChannelListReq.Marshal(b, m, deterministic)
}
func (dst *GetActiveChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActiveChannelListReq.Merge(dst, src)
}
func (m *GetActiveChannelListReq) XXX_Size() int {
	return xxx_messageInfo_GetActiveChannelListReq.Size(m)
}
func (m *GetActiveChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActiveChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetActiveChannelListReq proto.InternalMessageInfo

func (m *GetActiveChannelListReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *GetActiveChannelListReq) GetActiveAfter() uint32 {
	if m != nil {
		return m.ActiveAfter
	}
	return 0
}

func (m *GetActiveChannelListReq) GetActiveEnd() uint32 {
	if m != nil {
		return m.ActiveEnd
	}
	return 0
}

type GetActiveChannelListResp struct {
	ChannelList          []uint32 `protobuf:"varint,2,rep,packed,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetActiveChannelListResp) Reset()         { *m = GetActiveChannelListResp{} }
func (m *GetActiveChannelListResp) String() string { return proto.CompactTextString(m) }
func (*GetActiveChannelListResp) ProtoMessage()    {}
func (*GetActiveChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{19}
}
func (m *GetActiveChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActiveChannelListResp.Unmarshal(m, b)
}
func (m *GetActiveChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActiveChannelListResp.Marshal(b, m, deterministic)
}
func (dst *GetActiveChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActiveChannelListResp.Merge(dst, src)
}
func (m *GetActiveChannelListResp) XXX_Size() int {
	return xxx_messageInfo_GetActiveChannelListResp.Size(m)
}
func (m *GetActiveChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActiveChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetActiveChannelListResp proto.InternalMessageInfo

func (m *GetActiveChannelListResp) GetChannelList() []uint32 {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type GetAllActiveChannelListReq struct {
	ActiveAfter          uint32   `protobuf:"varint,1,opt,name=active_after,json=activeAfter,proto3" json:"active_after,omitempty"`
	ActiveEnd            uint32   `protobuf:"varint,2,opt,name=active_end,json=activeEnd,proto3" json:"active_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllActiveChannelListReq) Reset()         { *m = GetAllActiveChannelListReq{} }
func (m *GetAllActiveChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetAllActiveChannelListReq) ProtoMessage()    {}
func (*GetAllActiveChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{20}
}
func (m *GetAllActiveChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllActiveChannelListReq.Unmarshal(m, b)
}
func (m *GetAllActiveChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllActiveChannelListReq.Marshal(b, m, deterministic)
}
func (dst *GetAllActiveChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllActiveChannelListReq.Merge(dst, src)
}
func (m *GetAllActiveChannelListReq) XXX_Size() int {
	return xxx_messageInfo_GetAllActiveChannelListReq.Size(m)
}
func (m *GetAllActiveChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllActiveChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllActiveChannelListReq proto.InternalMessageInfo

func (m *GetAllActiveChannelListReq) GetActiveAfter() uint32 {
	if m != nil {
		return m.ActiveAfter
	}
	return 0
}

func (m *GetAllActiveChannelListReq) GetActiveEnd() uint32 {
	if m != nil {
		return m.ActiveEnd
	}
	return 0
}

type GetAllActiveChannelMapResp struct {
	AllChannelList       map[uint32]*GetActiveChannelListResp `protobuf:"bytes,1,rep,name=all_channel_list,json=allChannelList,proto3" json:"all_channel_list,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *GetAllActiveChannelMapResp) Reset()         { *m = GetAllActiveChannelMapResp{} }
func (m *GetAllActiveChannelMapResp) String() string { return proto.CompactTextString(m) }
func (*GetAllActiveChannelMapResp) ProtoMessage()    {}
func (*GetAllActiveChannelMapResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_creator_8891a93ea1631446, []int{21}
}
func (m *GetAllActiveChannelMapResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllActiveChannelMapResp.Unmarshal(m, b)
}
func (m *GetAllActiveChannelMapResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllActiveChannelMapResp.Marshal(b, m, deterministic)
}
func (dst *GetAllActiveChannelMapResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllActiveChannelMapResp.Merge(dst, src)
}
func (m *GetAllActiveChannelMapResp) XXX_Size() int {
	return xxx_messageInfo_GetAllActiveChannelMapResp.Size(m)
}
func (m *GetAllActiveChannelMapResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllActiveChannelMapResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllActiveChannelMapResp proto.InternalMessageInfo

func (m *GetAllActiveChannelMapResp) GetAllChannelList() map[uint32]*GetActiveChannelListResp {
	if m != nil {
		return m.AllChannelList
	}
	return nil
}

func init() {
	proto.RegisterType((*CreateChannelReq)(nil), "super_channel.CreateChannelReq")
	proto.RegisterType((*CreateChannelResp)(nil), "super_channel.CreateChannelResp")
	proto.RegisterType((*RevokeChannelReq)(nil), "super_channel.RevokeChannelReq")
	proto.RegisterType((*RevokeChannelResp)(nil), "super_channel.RevokeChannelResp")
	proto.RegisterType((*ChannelInfo)(nil), "super_channel.ChannelInfo")
	proto.RegisterType((*GetChannelInfoReq)(nil), "super_channel.GetChannelInfoReq")
	proto.RegisterType((*GetChannelInfoResp)(nil), "super_channel.GetChannelInfoResp")
	proto.RegisterType((*ListChannelInfoReq)(nil), "super_channel.ListChannelInfoReq")
	proto.RegisterType((*ListChannelInfoResp)(nil), "super_channel.ListChannelInfoResp")
	proto.RegisterType((*ChannelBindInfo)(nil), "super_channel.ChannelBindInfo")
	proto.RegisterType((*GetChannelBindInfoReq)(nil), "super_channel.GetChannelBindInfoReq")
	proto.RegisterType((*GetChannelBindInfoResp)(nil), "super_channel.GetChannelBindInfoResp")
	proto.RegisterType((*ListChannelBindInfoReq)(nil), "super_channel.ListChannelBindInfoReq")
	proto.RegisterType((*ListChannelBindInfoResp)(nil), "super_channel.ListChannelBindInfoResp")
	proto.RegisterType((*GetChannelAdminReq)(nil), "super_channel.GetChannelAdminReq")
	proto.RegisterType((*GetChannelAdminResp)(nil), "super_channel.GetChannelAdminResp")
	proto.RegisterMapType((map[uint32]ChannelAdminRole)(nil), "super_channel.GetChannelAdminResp.AdminsEntry")
	proto.RegisterType((*SetChannelAdminReq)(nil), "super_channel.SetChannelAdminReq")
	proto.RegisterType((*SetChannelAdminResp)(nil), "super_channel.SetChannelAdminResp")
	proto.RegisterType((*GetActiveChannelListReq)(nil), "super_channel.GetActiveChannelListReq")
	proto.RegisterType((*GetActiveChannelListResp)(nil), "super_channel.GetActiveChannelListResp")
	proto.RegisterType((*GetAllActiveChannelListReq)(nil), "super_channel.GetAllActiveChannelListReq")
	proto.RegisterType((*GetAllActiveChannelMapResp)(nil), "super_channel.GetAllActiveChannelMapResp")
	proto.RegisterMapType((map[uint32]*GetActiveChannelListResp)(nil), "super_channel.GetAllActiveChannelMapResp.AllChannelListEntry")
	proto.RegisterEnum("super_channel.ChannelAdminRole", ChannelAdminRole_name, ChannelAdminRole_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SuperChannelCreatorClient is the client API for SuperChannelCreator service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SuperChannelCreatorClient interface {
	// 创建
	CreateChannel(ctx context.Context, in *CreateChannelReq, opts ...grpc.CallOption) (*CreateChannelResp, error)
	// 收回
	RevokeChannel(ctx context.Context, in *RevokeChannelReq, opts ...grpc.CallOption) (*RevokeChannelResp, error)
	// 查询
	GetChannelInfo(ctx context.Context, in *GetChannelInfoReq, opts ...grpc.CallOption) (*GetChannelInfoResp, error)
	// 获取列表
	ListChannelInfo(ctx context.Context, in *ListChannelInfoReq, opts ...grpc.CallOption) (*ListChannelInfoResp, error)
	// 查询
	GetChannelBindInfo(ctx context.Context, in *GetChannelBindInfoReq, opts ...grpc.CallOption) (*GetChannelBindInfoResp, error)
	// 获取列表
	ListChannelBindInfo(ctx context.Context, in *ListChannelBindInfoReq, opts ...grpc.CallOption) (*ListChannelBindInfoResp, error)
	// 获取管理员列表
	GetChannelAdmin(ctx context.Context, in *GetChannelAdminReq, opts ...grpc.CallOption) (*GetChannelAdminResp, error)
	// 设置管理员
	SetChannelAdmin(ctx context.Context, in *SetChannelAdminReq, opts ...grpc.CallOption) (*SetChannelAdminResp, error)
	// 获取活跃房间列表
	GetActiveChannelList(ctx context.Context, in *GetActiveChannelListReq, opts ...grpc.CallOption) (*GetActiveChannelListResp, error)
	// 获取所有类型活跃房间列表
	GetAllActiveChannelList(ctx context.Context, in *GetAllActiveChannelListReq, opts ...grpc.CallOption) (*GetActiveChannelListResp, error)
	GetAllActiveChannelMap(ctx context.Context, in *GetAllActiveChannelListReq, opts ...grpc.CallOption) (*GetAllActiveChannelMapResp, error)
}

type superChannelCreatorClient struct {
	cc *grpc.ClientConn
}

func NewSuperChannelCreatorClient(cc *grpc.ClientConn) SuperChannelCreatorClient {
	return &superChannelCreatorClient{cc}
}

func (c *superChannelCreatorClient) CreateChannel(ctx context.Context, in *CreateChannelReq, opts ...grpc.CallOption) (*CreateChannelResp, error) {
	out := new(CreateChannelResp)
	err := c.cc.Invoke(ctx, "/super_channel.SuperChannelCreator/CreateChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelCreatorClient) RevokeChannel(ctx context.Context, in *RevokeChannelReq, opts ...grpc.CallOption) (*RevokeChannelResp, error) {
	out := new(RevokeChannelResp)
	err := c.cc.Invoke(ctx, "/super_channel.SuperChannelCreator/RevokeChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelCreatorClient) GetChannelInfo(ctx context.Context, in *GetChannelInfoReq, opts ...grpc.CallOption) (*GetChannelInfoResp, error) {
	out := new(GetChannelInfoResp)
	err := c.cc.Invoke(ctx, "/super_channel.SuperChannelCreator/GetChannelInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelCreatorClient) ListChannelInfo(ctx context.Context, in *ListChannelInfoReq, opts ...grpc.CallOption) (*ListChannelInfoResp, error) {
	out := new(ListChannelInfoResp)
	err := c.cc.Invoke(ctx, "/super_channel.SuperChannelCreator/ListChannelInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelCreatorClient) GetChannelBindInfo(ctx context.Context, in *GetChannelBindInfoReq, opts ...grpc.CallOption) (*GetChannelBindInfoResp, error) {
	out := new(GetChannelBindInfoResp)
	err := c.cc.Invoke(ctx, "/super_channel.SuperChannelCreator/GetChannelBindInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelCreatorClient) ListChannelBindInfo(ctx context.Context, in *ListChannelBindInfoReq, opts ...grpc.CallOption) (*ListChannelBindInfoResp, error) {
	out := new(ListChannelBindInfoResp)
	err := c.cc.Invoke(ctx, "/super_channel.SuperChannelCreator/ListChannelBindInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelCreatorClient) GetChannelAdmin(ctx context.Context, in *GetChannelAdminReq, opts ...grpc.CallOption) (*GetChannelAdminResp, error) {
	out := new(GetChannelAdminResp)
	err := c.cc.Invoke(ctx, "/super_channel.SuperChannelCreator/GetChannelAdmin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelCreatorClient) SetChannelAdmin(ctx context.Context, in *SetChannelAdminReq, opts ...grpc.CallOption) (*SetChannelAdminResp, error) {
	out := new(SetChannelAdminResp)
	err := c.cc.Invoke(ctx, "/super_channel.SuperChannelCreator/SetChannelAdmin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelCreatorClient) GetActiveChannelList(ctx context.Context, in *GetActiveChannelListReq, opts ...grpc.CallOption) (*GetActiveChannelListResp, error) {
	out := new(GetActiveChannelListResp)
	err := c.cc.Invoke(ctx, "/super_channel.SuperChannelCreator/GetActiveChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelCreatorClient) GetAllActiveChannelList(ctx context.Context, in *GetAllActiveChannelListReq, opts ...grpc.CallOption) (*GetActiveChannelListResp, error) {
	out := new(GetActiveChannelListResp)
	err := c.cc.Invoke(ctx, "/super_channel.SuperChannelCreator/GetAllActiveChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelCreatorClient) GetAllActiveChannelMap(ctx context.Context, in *GetAllActiveChannelListReq, opts ...grpc.CallOption) (*GetAllActiveChannelMapResp, error) {
	out := new(GetAllActiveChannelMapResp)
	err := c.cc.Invoke(ctx, "/super_channel.SuperChannelCreator/GetAllActiveChannelMap", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SuperChannelCreatorServer is the server API for SuperChannelCreator service.
type SuperChannelCreatorServer interface {
	// 创建
	CreateChannel(context.Context, *CreateChannelReq) (*CreateChannelResp, error)
	// 收回
	RevokeChannel(context.Context, *RevokeChannelReq) (*RevokeChannelResp, error)
	// 查询
	GetChannelInfo(context.Context, *GetChannelInfoReq) (*GetChannelInfoResp, error)
	// 获取列表
	ListChannelInfo(context.Context, *ListChannelInfoReq) (*ListChannelInfoResp, error)
	// 查询
	GetChannelBindInfo(context.Context, *GetChannelBindInfoReq) (*GetChannelBindInfoResp, error)
	// 获取列表
	ListChannelBindInfo(context.Context, *ListChannelBindInfoReq) (*ListChannelBindInfoResp, error)
	// 获取管理员列表
	GetChannelAdmin(context.Context, *GetChannelAdminReq) (*GetChannelAdminResp, error)
	// 设置管理员
	SetChannelAdmin(context.Context, *SetChannelAdminReq) (*SetChannelAdminResp, error)
	// 获取活跃房间列表
	GetActiveChannelList(context.Context, *GetActiveChannelListReq) (*GetActiveChannelListResp, error)
	// 获取所有类型活跃房间列表
	GetAllActiveChannelList(context.Context, *GetAllActiveChannelListReq) (*GetActiveChannelListResp, error)
	GetAllActiveChannelMap(context.Context, *GetAllActiveChannelListReq) (*GetAllActiveChannelMapResp, error)
}

func RegisterSuperChannelCreatorServer(s *grpc.Server, srv SuperChannelCreatorServer) {
	s.RegisterService(&_SuperChannelCreator_serviceDesc, srv)
}

func _SuperChannelCreator_CreateChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelCreatorServer).CreateChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/super_channel.SuperChannelCreator/CreateChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelCreatorServer).CreateChannel(ctx, req.(*CreateChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelCreator_RevokeChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RevokeChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelCreatorServer).RevokeChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/super_channel.SuperChannelCreator/RevokeChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelCreatorServer).RevokeChannel(ctx, req.(*RevokeChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelCreator_GetChannelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelCreatorServer).GetChannelInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/super_channel.SuperChannelCreator/GetChannelInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelCreatorServer).GetChannelInfo(ctx, req.(*GetChannelInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelCreator_ListChannelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListChannelInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelCreatorServer).ListChannelInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/super_channel.SuperChannelCreator/ListChannelInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelCreatorServer).ListChannelInfo(ctx, req.(*ListChannelInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelCreator_GetChannelBindInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelBindInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelCreatorServer).GetChannelBindInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/super_channel.SuperChannelCreator/GetChannelBindInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelCreatorServer).GetChannelBindInfo(ctx, req.(*GetChannelBindInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelCreator_ListChannelBindInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListChannelBindInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelCreatorServer).ListChannelBindInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/super_channel.SuperChannelCreator/ListChannelBindInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelCreatorServer).ListChannelBindInfo(ctx, req.(*ListChannelBindInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelCreator_GetChannelAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelAdminReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelCreatorServer).GetChannelAdmin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/super_channel.SuperChannelCreator/GetChannelAdmin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelCreatorServer).GetChannelAdmin(ctx, req.(*GetChannelAdminReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelCreator_SetChannelAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelAdminReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelCreatorServer).SetChannelAdmin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/super_channel.SuperChannelCreator/SetChannelAdmin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelCreatorServer).SetChannelAdmin(ctx, req.(*SetChannelAdminReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelCreator_GetActiveChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActiveChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelCreatorServer).GetActiveChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/super_channel.SuperChannelCreator/GetActiveChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelCreatorServer).GetActiveChannelList(ctx, req.(*GetActiveChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelCreator_GetAllActiveChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllActiveChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelCreatorServer).GetAllActiveChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/super_channel.SuperChannelCreator/GetAllActiveChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelCreatorServer).GetAllActiveChannelList(ctx, req.(*GetAllActiveChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelCreator_GetAllActiveChannelMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllActiveChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelCreatorServer).GetAllActiveChannelMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/super_channel.SuperChannelCreator/GetAllActiveChannelMap",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelCreatorServer).GetAllActiveChannelMap(ctx, req.(*GetAllActiveChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SuperChannelCreator_serviceDesc = grpc.ServiceDesc{
	ServiceName: "super_channel.SuperChannelCreator",
	HandlerType: (*SuperChannelCreatorServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateChannel",
			Handler:    _SuperChannelCreator_CreateChannel_Handler,
		},
		{
			MethodName: "RevokeChannel",
			Handler:    _SuperChannelCreator_RevokeChannel_Handler,
		},
		{
			MethodName: "GetChannelInfo",
			Handler:    _SuperChannelCreator_GetChannelInfo_Handler,
		},
		{
			MethodName: "ListChannelInfo",
			Handler:    _SuperChannelCreator_ListChannelInfo_Handler,
		},
		{
			MethodName: "GetChannelBindInfo",
			Handler:    _SuperChannelCreator_GetChannelBindInfo_Handler,
		},
		{
			MethodName: "ListChannelBindInfo",
			Handler:    _SuperChannelCreator_ListChannelBindInfo_Handler,
		},
		{
			MethodName: "GetChannelAdmin",
			Handler:    _SuperChannelCreator_GetChannelAdmin_Handler,
		},
		{
			MethodName: "SetChannelAdmin",
			Handler:    _SuperChannelCreator_SetChannelAdmin_Handler,
		},
		{
			MethodName: "GetActiveChannelList",
			Handler:    _SuperChannelCreator_GetActiveChannelList_Handler,
		},
		{
			MethodName: "GetAllActiveChannelList",
			Handler:    _SuperChannelCreator_GetAllActiveChannelList_Handler,
		},
		{
			MethodName: "GetAllActiveChannelMap",
			Handler:    _SuperChannelCreator_GetAllActiveChannelMap_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "super-channel/super-channel-creator.proto",
}

func init() {
	proto.RegisterFile("super-channel/super-channel-creator.proto", fileDescriptor_super_channel_creator_8891a93ea1631446)
}

var fileDescriptor_super_channel_creator_8891a93ea1631446 = []byte{
	// 1197 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x57, 0x6f, 0x4f, 0xe3, 0xc6,
	0x13, 0xc6, 0x09, 0xf0, 0x83, 0xc9, 0x05, 0xc2, 0xe6, 0x0e, 0xac, 0xdc, 0xaf, 0x6d, 0xb0, 0x0a,
	0x85, 0x4a, 0x17, 0x74, 0x41, 0xa7, 0x56, 0x57, 0xf1, 0x22, 0x07, 0xe9, 0x5d, 0xaa, 0x10, 0x7a,
	0x0e, 0xdc, 0x49, 0x9c, 0x54, 0xcb, 0xd8, 0x0b, 0x6c, 0x71, 0xec, 0x25, 0xeb, 0x04, 0xa5, 0x2f,
	0xda, 0xaf, 0xd0, 0xaf, 0xd3, 0x8f, 0xd0, 0x8f, 0xd3, 0x6f, 0x50, 0x79, 0xed, 0x0d, 0xfe, 0xb3,
	0x5c, 0x4c, 0xdf, 0xc5, 0x33, 0xcf, 0x3c, 0x33, 0xfb, 0xec, 0xec, 0xec, 0x06, 0x76, 0xd9, 0x88,
	0xe2, 0xe1, 0x0b, 0xeb, 0xda, 0x74, 0x5d, 0xec, 0xec, 0x25, 0xbe, 0x5e, 0x58, 0x43, 0x6c, 0xfa,
	0xde, 0xb0, 0x41, 0x87, 0x9e, 0xef, 0xa1, 0x32, 0x77, 0x1a, 0x91, 0x53, 0xfb, 0x04, 0x95, 0xc3,
	0xc0, 0x8f, 0x0f, 0x43, 0x83, 0x8e, 0x6f, 0x51, 0x05, 0x8a, 0x23, 0x62, 0xab, 0x4a, 0x5d, 0xd9,
	0x29, 0xeb, 0xc1, 0x4f, 0x84, 0x60, 0xde, 0x35, 0x07, 0x58, 0x2d, 0xd4, 0x95, 0x9d, 0x65, 0x9d,
	0xff, 0x46, 0x9b, 0xf0, 0x24, 0x22, 0x31, 0xfc, 0x09, 0xc5, 0x6a, 0x91, 0xc3, 0x4b, 0x91, 0xed,
	0x74, 0x42, 0xb1, 0xf6, 0x1e, 0xd6, 0x52, 0xe4, 0x8c, 0xa2, 0x2f, 0x00, 0x44, 0xdc, 0x34, 0xc9,
	0x72, 0x64, 0xe9, 0xd8, 0x81, 0xdb, 0x26, 0x8c, 0x3a, 0xe6, 0x24, 0x70, 0x17, 0x42, 0x77, 0x64,
	0xe9, 0xd8, 0xda, 0x4b, 0xa8, 0xe8, 0x78, 0xec, 0xdd, 0xc4, 0xeb, 0xfd, 0x3c, 0xa3, 0x56, 0x85,
	0xb5, 0x54, 0x08, 0xa3, 0xda, 0x9f, 0x0b, 0x50, 0x8a, 0xbe, 0x3b, 0xee, 0xa5, 0x37, 0xab, 0xaa,
	0x06, 0x54, 0x85, 0x3b, 0x92, 0xd3, 0x18, 0x4d, 0xcb, 0x5b, 0x8b, 0x5c, 0x87, 0xa1, 0xe7, 0x8c,
	0xd8, 0x71, 0x71, 0xb8, 0x70, 0x45, 0x2e, 0x9c, 0x10, 0xa7, 0x27, 0xd3, 0x6f, 0x3e, 0xa3, 0x5f,
	0x1c, 0x42, 0x2c, 0xcf, 0x55, 0x17, 0x12, 0x2c, 0x1d, 0xcb, 0x73, 0xe3, 0x10, 0x1b, 0x33, 0x4b,
	0x5d, 0x4c, 0x40, 0x8e, 0x30, 0xb3, 0xd0, 0x36, 0xac, 0x0a, 0x08, 0xbb, 0xf6, 0xee, 0x82, 0xf5,
	0xfd, 0x8f, 0xe7, 0x2a, 0x47, 0xe6, 0xfe, 0xb5, 0x77, 0xd7, 0xb1, 0xd1, 0x6b, 0xa8, 0x11, 0x66,
	0x78, 0x14, 0xbb, 0x86, 0xe3, 0x59, 0x37, 0x06, 0xb3, 0x86, 0x18, 0xbb, 0x06, 0xbb, 0x23, 0xbe,
	0x75, 0xad, 0x2e, 0xd5, 0x95, 0x9d, 0x25, 0x7d, 0x9d, 0xb0, 0x13, 0x8a, 0xdd, 0xae, 0x67, 0xdd,
	0xf4, 0xb9, 0xbb, 0xcf, 0xbd, 0xe8, 0x04, 0xb6, 0x44, 0xac, 0x4d, 0x98, 0x79, 0xe1, 0x60, 0xc3,
	0xf4, 0x7d, 0xd3, 0xba, 0x1e, 0x60, 0xd7, 0x37, 0x06, 0xec, 0x4a, 0xd0, 0x2c, 0x73, 0x9a, 0x7a,
	0x48, 0x73, 0x14, 0x42, 0x5b, 0x53, 0xe4, 0x31, 0xbb, 0x8a, 0x08, 0xdb, 0x50, 0x4f, 0x13, 0x3a,
	0x78, 0x8c, 0x1d, 0xc3, 0x19, 0xf8, 0x82, 0x0b, 0x38, 0xd7, 0xf3, 0x04, 0x57, 0x37, 0x00, 0x75,
	0x07, 0x7e, 0x44, 0xf3, 0x0e, 0x34, 0x41, 0xe3, 0x7a, 0xc3, 0x81, 0xe9, 0x18, 0xb7, 0x23, 0x3c,
	0xc2, 0xc6, 0x88, 0x1a, 0x03, 0x62, 0x09, 0xa2, 0x12, 0x27, 0xfa, 0x7f, 0x48, 0xd4, 0xe3, 0xb8,
	0xf7, 0x01, 0xec, 0x8c, 0x1e, 0x13, 0x2b, 0x62, 0x7a, 0x0e, 0xcb, 0x23, 0x6a, 0x9b, 0x3e, 0x36,
	0x7c, 0xa6, 0x3e, 0xa9, 0x2b, 0x3b, 0x45, 0x7d, 0x29, 0x34, 0x9c, 0xb2, 0xc0, 0x49, 0x18, 0x57,
	0x0d, 0xdb, 0x6a, 0x99, 0xb3, 0x2d, 0x11, 0xd6, 0xe5, 0xdf, 0x71, 0xfd, 0xc7, 0x04, 0x73, 0xfd,
	0x57, 0xf8, 0x2e, 0x09, 0xfd, 0x3f, 0x10, 0x7c, 0xd7, 0xb1, 0xb5, 0x26, 0xac, 0xbd, 0xc5, 0x7e,
	0xac, 0x29, 0x73, 0xf4, 0xf6, 0x11, 0xa0, 0x74, 0x0c, 0xa3, 0xa8, 0x01, 0xf3, 0xc4, 0xbd, 0xf4,
	0x38, 0xbc, 0xd4, 0xac, 0x35, 0x12, 0x47, 0xbe, 0x11, 0x47, 0x73, 0x9c, 0xf6, 0x1d, 0xa0, 0x2e,
	0x61, 0xe9, 0xd4, 0xe9, 0x06, 0x55, 0xb2, 0x07, 0xfc, 0x14, 0xaa, 0x99, 0x40, 0x46, 0xd1, 0xc1,
	0x7d, 0xa4, 0x43, 0x98, 0xaf, 0x2a, 0xf5, 0xe2, 0x8c, 0x3a, 0x04, 0x6b, 0xc0, 0xa6, 0x61, 0x58,
	0x8d, 0x7c, 0x6f, 0x88, 0x6b, 0xf3, 0xe3, 0x99, 0x1d, 0x49, 0x49, 0x61, 0x0a, 0xe9, 0x03, 0x9b,
	0x63, 0x3a, 0x75, 0xe1, 0xd9, 0xbd, 0x76, 0x22, 0x93, 0x7c, 0xfe, 0xa5, 0xd9, 0x0a, 0x59, 0xb6,
	0x4f, 0xb0, 0x2e, 0x63, 0x63, 0x14, 0xb5, 0xa4, 0x6a, 0x7c, 0x29, 0x57, 0x63, 0x1a, 0x99, 0x50,
	0xe4, 0x07, 0x58, 0x8f, 0xe9, 0x1c, 0xaf, 0x35, 0xc7, 0x26, 0x61, 0xd8, 0x90, 0x06, 0x33, 0x8a,
	0x7e, 0x02, 0x31, 0xbb, 0x8c, 0x0b, 0xe2, 0xda, 0x8f, 0xa9, 0x4f, 0xf4, 0x74, 0x60, 0xe0, 0x35,
	0xee, 0xc7, 0x5b, 0xb1, 0x65, 0x0f, 0x88, 0x9b, 0xa3, 0x7f, 0xff, 0x52, 0xa0, 0x9a, 0x89, 0x62,
	0x14, 0xfd, 0x08, 0x8b, 0x66, 0xf0, 0xc1, 0xa2, 0x6a, 0x1a, 0xa9, 0x6a, 0x24, 0x31, 0x0d, 0xfe,
	0x8b, 0xb5, 0x5d, 0x7f, 0x38, 0xd1, 0xa3, 0xe8, 0xda, 0x39, 0x94, 0x62, 0xe6, 0x60, 0x67, 0x6f,
	0xf0, 0x44, 0xec, 0xec, 0x0d, 0x9e, 0xa0, 0x57, 0xb0, 0x30, 0x36, 0x9d, 0x51, 0xb8, 0xa5, 0x2b,
	0xcd, 0xaf, 0xe4, 0xab, 0x0e, 0x93, 0x78, 0x0e, 0xd6, 0x43, 0xf4, 0xeb, 0xc2, 0xf7, 0x8a, 0xf6,
	0x1b, 0xa0, 0xfe, 0x63, 0x17, 0x2c, 0x7a, 0xab, 0x70, 0xdf, 0x5b, 0xfb, 0x30, 0x3f, 0xf4, 0x9c,
	0xb0, 0x43, 0x73, 0x14, 0xc0, 0xc1, 0xda, 0x33, 0xa8, 0xf6, 0xb3, 0x12, 0x68, 0xbf, 0xc3, 0xc6,
	0x5b, 0xec, 0xb7, 0x2c, 0x9f, 0x8c, 0xc5, 0x6d, 0x17, 0xec, 0x4d, 0xbe, 0x46, 0x09, 0x20, 0x26,
	0x0f, 0x35, 0xcc, 0x4b, 0x1f, 0x0f, 0x45, 0x97, 0x87, 0xb6, 0x56, 0x60, 0x0a, 0x56, 0x17, 0x41,
	0xb0, 0x6b, 0x47, 0x87, 0x6a, 0x39, 0xb4, 0xb4, 0x5d, 0x5b, 0x3b, 0x00, 0x55, 0x9e, 0x9f, 0xd1,
	0x78, 0x01, 0xbc, 0xcd, 0x0a, 0xf5, 0x62, 0xac, 0x00, 0xde, 0x42, 0xbf, 0x40, 0x2d, 0x08, 0x77,
	0x9c, 0x87, 0x56, 0x90, 0x28, 0x4f, 0x99, 0x55, 0x5e, 0x21, 0x5d, 0xde, 0x3f, 0x8a, 0x34, 0xc1,
	0xb1, 0x49, 0x79, 0x85, 0x57, 0x50, 0x31, 0x1d, 0xc7, 0x90, 0x1c, 0xd6, 0x83, 0x6c, 0xfb, 0x3d,
	0x40, 0xd2, 0x68, 0x39, 0x4e, 0xac, 0xee, 0xb0, 0x1b, 0x57, 0xcc, 0x84, 0xb1, 0xf6, 0x2b, 0x54,
	0x25, 0x30, 0x49, 0x77, 0x1e, 0xc4, 0xbb, 0xb3, 0xd4, 0xfc, 0x46, 0x52, 0x86, 0x4c, 0xeb, 0x58,
	0x97, 0x7e, 0xfb, 0x07, 0x54, 0xd2, 0x3d, 0x84, 0x54, 0x78, 0x7a, 0xf8, 0xae, 0xd5, 0xeb, 0xb5,
	0xbb, 0x46, 0xa7, 0xf7, 0xa1, 0xd5, 0xed, 0x1c, 0x19, 0xfa, 0x49, 0xb7, 0x5d, 0x99, 0x43, 0x6b,
	0x50, 0x16, 0x9e, 0x93, 0x8f, 0xbd, 0xb6, 0x5e, 0x51, 0xe2, 0xa6, 0xd6, 0xd1, 0x71, 0xa7, 0x57,
	0x29, 0x20, 0x04, 0x2b, 0xc2, 0xd4, 0x3b, 0xd1, 0x8f, 0x5b, 0xdd, 0x4a, 0x11, 0x6d, 0x40, 0x35,
	0x01, 0x33, 0xfa, 0x67, 0x3f, 0xb7, 0xf5, 0xca, 0x7c, 0xf3, 0xef, 0x25, 0xa8, 0xf6, 0x83, 0xb2,
	0x0f, 0x13, 0xaf, 0x24, 0x74, 0x0a, 0xe5, 0xc4, 0xe3, 0x10, 0x65, 0x5a, 0x3f, 0xf5, 0x2e, 0xad,
	0xd5, 0x3f, 0x0f, 0x60, 0x54, 0x9b, 0x0b, 0x58, 0x13, 0x8f, 0xbd, 0x0c, 0x6b, 0xfa, 0xf5, 0x98,
	0x61, 0xcd, 0xbe, 0x15, 0xe7, 0xd0, 0x47, 0x58, 0x49, 0x5e, 0xb3, 0xa8, 0xfe, 0xe0, 0x40, 0x8a,
	0x26, 0x73, 0x6d, 0x73, 0x06, 0x82, 0x13, 0x9f, 0xc3, 0x6a, 0xea, 0x02, 0x45, 0xe9, 0xb8, 0xec,
	0xcd, 0x5c, 0xd3, 0x66, 0x41, 0x38, 0xb7, 0x15, 0x1f, 0xc8, 0xd3, 0x9b, 0xf4, 0xeb, 0x07, 0xcb,
	0x8a, 0x5d, 0x2b, 0xb5, 0xad, 0x1c, 0x28, 0x9e, 0xe4, 0x32, 0xf1, 0x02, 0x98, 0x66, 0xd9, 0x7a,
	0xb8, 0xc2, 0x78, 0x9a, 0xed, 0x3c, 0x30, 0x21, 0x54, 0x6a, 0xe6, 0xa3, 0xcd, 0x59, 0x77, 0x42,
	0x56, 0x28, 0xc9, 0xb5, 0x11, 0x72, 0xf7, 0x67, 0x70, 0xf7, 0x67, 0x73, 0xf7, 0xa5, 0xdc, 0x04,
	0x9e, 0xca, 0x4e, 0x29, 0xda, 0xce, 0x75, 0x94, 0x6f, 0x6b, 0x79, 0x8f, 0xbc, 0x36, 0x87, 0xbc,
	0x70, 0xf8, 0x4b, 0xa6, 0x27, 0xda, 0x9d, 0x3d, 0xbf, 0xfe, 0x43, 0x42, 0xca, 0x9f, 0x3c, 0x92,
	0x41, 0xf8, 0x98, 0x7c, 0xbb, 0xb9, 0x47, 0xab, 0x36, 0xf7, 0xe6, 0xe5, 0xf9, 0xde, 0x95, 0xe7,
	0x98, 0xee, 0x55, 0xe3, 0x55, 0xd3, 0xf7, 0x1b, 0x96, 0x37, 0xd8, 0xe3, 0xff, 0x6a, 0x2d, 0xcf,
	0xd9, 0x63, 0x78, 0x38, 0x26, 0x16, 0x66, 0xc9, 0x7f, 0xbf, 0x17, 0x8b, 0x1c, 0xb0, 0xff, 0x6f,
	0x00, 0x00, 0x00, 0xff, 0xff, 0xa2, 0x31, 0xc1, 0xea, 0x23, 0x0f, 0x00, 0x00,
}
