// Code generated by protoc-gen-gogo.
// source: services/guildstorage/guildstorage.proto
// DO NOT EDIT!

/*
	Package GuildStorage is a generated protocol buffer package.

	It is generated from these files:
		services/guildstorage/guildstorage.proto

	It has these top-level messages:
		MinGuildProduct
		GuildProduct
		ProductItem
		AddProductReq
		AddProductResp
		GetProductReq
		GetProductResp
		BatchGetProductByIdReq
		BatchGetProductByIdResp
		SearchProductReq
		SearchProductResp
		ExamineRecord
		GetExamineRecordLstReq
		GetExamineRecordLstResp
		DealExamineReq
		DealExamineResp
		ModifyExamineApplyReq
		ModifyExamineApplyResp
		StorageOperRecord
		StorageGetOperRecordReq
		StorageGetOperRecordResp
		GainProductReq
		GainProductResp
		AllotProductReq
		AllotProductResp
		ModifyProductStatusReq
		ModifyProductStatusResp
		GetProductBySourceIdReq
		GetProductBySourceIdResp
		GetGameProductReq
		GameProduct
		GetGameProductResp
		DeleteProductReq
		DeleteProductResp
		GetFetchRecordReq
		UserFetchRecord
		GetFetchRecordResp
		BatchGetUserFetchRecordReq
		BatchGetUserFetchRecordResp
		CalculateProductReq
		CalculateProductItem
		CalculateProductResp
		GetGuildBySourceIdReq
		GetGuildBySourceIdResp
		UserStorageInfo
		GetUserStorageInfoReq
		GetUserStorageInfoResp
		UserProductItem
		GetUserProductItemReq
		GetUserProductItemResp
		BatGetGameProductCountReq
		GuildProductCount
		BatGetGameProductCountResp
		GuildGiftCardInfo
		GetGuildGiftCardReq
		GetGuildGiftCardResp
		AddGuildGiftCardReq
		AddGuildGiftCardResp
		SplitGuildGiftCardReq
		SplitGuildGiftCardResp
		GrantVoucherData
		GetGiftCardLogInfoReq
		GetGiftCardLogInfo
		GetGiftCardLogInfoResp
		SetGiftCardLogStatusReq
		SetGiftCardLogStatusResp
		DeleteGiftCardReq
		DeleteGiftCardResp
*/
package GuildStorage

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 代金券异步任务
type EGiftCardAsyncType int32

const (
	EGiftCardAsyncType_EGiftCardAynsc_GetGiftCard  EGiftCardAsyncType = 1
	EGiftCardAsyncType_EGiftCardAynsc_GrantVoucher EGiftCardAsyncType = 2
)

var EGiftCardAsyncType_name = map[int32]string{
	1: "EGiftCardAynsc_GetGiftCard",
	2: "EGiftCardAynsc_GrantVoucher",
}
var EGiftCardAsyncType_value = map[string]int32{
	"EGiftCardAynsc_GetGiftCard":  1,
	"EGiftCardAynsc_GrantVoucher": 2,
}

func (x EGiftCardAsyncType) Enum() *EGiftCardAsyncType {
	p := new(EGiftCardAsyncType)
	*p = x
	return p
}
func (x EGiftCardAsyncType) String() string {
	return proto.EnumName(EGiftCardAsyncType_name, int32(x))
}
func (x *EGiftCardAsyncType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(EGiftCardAsyncType_value, data, "EGiftCardAsyncType")
	if err != nil {
		return err
	}
	*x = EGiftCardAsyncType(value)
	return nil
}
func (EGiftCardAsyncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{0}
}

// 礼品卡消费记录
type EGrantVoucherStatus int32

const (
	EGrantVoucherStatus_EGrantVoucherStatus_None    EGrantVoucherStatus = 1
	EGrantVoucherStatus_EGrantVoucherStatus_Success EGrantVoucherStatus = 2
	EGrantVoucherStatus_EGrantVoucherStatus_Fail    EGrantVoucherStatus = 3
)

var EGrantVoucherStatus_name = map[int32]string{
	1: "EGrantVoucherStatus_None",
	2: "EGrantVoucherStatus_Success",
	3: "EGrantVoucherStatus_Fail",
}
var EGrantVoucherStatus_value = map[string]int32{
	"EGrantVoucherStatus_None":    1,
	"EGrantVoucherStatus_Success": 2,
	"EGrantVoucherStatus_Fail":    3,
}

func (x EGrantVoucherStatus) Enum() *EGrantVoucherStatus {
	p := new(EGrantVoucherStatus)
	*p = x
	return p
}
func (x EGrantVoucherStatus) String() string {
	return proto.EnumName(EGrantVoucherStatus_name, int32(x))
}
func (x *EGrantVoucherStatus) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(EGrantVoucherStatus_value, data, "EGrantVoucherStatus")
	if err != nil {
		return err
	}
	*x = EGrantVoucherStatus(value)
	return nil
}
func (EGrantVoucherStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{1}
}

type StorageOperRecord_CATEGORY_TYPE int32

const (
	StorageOperRecord_CATEGORY_GUILD_OWNER  StorageOperRecord_CATEGORY_TYPE = 1
	StorageOperRecord_CATEGORY_GUILD_MEMBER StorageOperRecord_CATEGORY_TYPE = 2
)

var StorageOperRecord_CATEGORY_TYPE_name = map[int32]string{
	1: "CATEGORY_GUILD_OWNER",
	2: "CATEGORY_GUILD_MEMBER",
}
var StorageOperRecord_CATEGORY_TYPE_value = map[string]int32{
	"CATEGORY_GUILD_OWNER":  1,
	"CATEGORY_GUILD_MEMBER": 2,
}

func (x StorageOperRecord_CATEGORY_TYPE) Enum() *StorageOperRecord_CATEGORY_TYPE {
	p := new(StorageOperRecord_CATEGORY_TYPE)
	*p = x
	return p
}
func (x StorageOperRecord_CATEGORY_TYPE) String() string {
	return proto.EnumName(StorageOperRecord_CATEGORY_TYPE_name, int32(x))
}
func (x *StorageOperRecord_CATEGORY_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(StorageOperRecord_CATEGORY_TYPE_value, data, "StorageOperRecord_CATEGORY_TYPE")
	if err != nil {
		return err
	}
	*x = StorageOperRecord_CATEGORY_TYPE(value)
	return nil
}
func (StorageOperRecord_CATEGORY_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{18, 0}
}

// 会员操作
type StorageOperRecord_GUILD_MEM_OPER int32

const (
	StorageOperRecord_OPER_FETCH_P StorageOperRecord_GUILD_MEM_OPER = 1
	StorageOperRecord_OPER_ALLOT_P StorageOperRecord_GUILD_MEM_OPER = 2
)

var StorageOperRecord_GUILD_MEM_OPER_name = map[int32]string{
	1: "OPER_FETCH_P",
	2: "OPER_ALLOT_P",
}
var StorageOperRecord_GUILD_MEM_OPER_value = map[string]int32{
	"OPER_FETCH_P": 1,
	"OPER_ALLOT_P": 2,
}

func (x StorageOperRecord_GUILD_MEM_OPER) Enum() *StorageOperRecord_GUILD_MEM_OPER {
	p := new(StorageOperRecord_GUILD_MEM_OPER)
	*p = x
	return p
}
func (x StorageOperRecord_GUILD_MEM_OPER) String() string {
	return proto.EnumName(StorageOperRecord_GUILD_MEM_OPER_name, int32(x))
}
func (x *StorageOperRecord_GUILD_MEM_OPER) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(StorageOperRecord_GUILD_MEM_OPER_value, data, "StorageOperRecord_GUILD_MEM_OPER")
	if err != nil {
		return err
	}
	*x = StorageOperRecord_GUILD_MEM_OPER(value)
	return nil
}
func (StorageOperRecord_GUILD_MEM_OPER) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{18, 1}
}

type ModifyProductStatusReq_ModifyProductOperType int32

const (
	ModifyProductStatusReq_ModifyProductOperType_Shelve   ModifyProductStatusReq_ModifyProductOperType = 1
	ModifyProductStatusReq_ModifyProductOperType_UnShelve ModifyProductStatusReq_ModifyProductOperType = 2
	ModifyProductStatusReq_ModifyProductOperType_Modify   ModifyProductStatusReq_ModifyProductOperType = 3
)

var ModifyProductStatusReq_ModifyProductOperType_name = map[int32]string{
	1: "ModifyProductOperType_Shelve",
	2: "ModifyProductOperType_UnShelve",
	3: "ModifyProductOperType_Modify",
}
var ModifyProductStatusReq_ModifyProductOperType_value = map[string]int32{
	"ModifyProductOperType_Shelve":   1,
	"ModifyProductOperType_UnShelve": 2,
	"ModifyProductOperType_Modify":   3,
}

func (x ModifyProductStatusReq_ModifyProductOperType) Enum() *ModifyProductStatusReq_ModifyProductOperType {
	p := new(ModifyProductStatusReq_ModifyProductOperType)
	*p = x
	return p
}
func (x ModifyProductStatusReq_ModifyProductOperType) String() string {
	return proto.EnumName(ModifyProductStatusReq_ModifyProductOperType_name, int32(x))
}
func (x *ModifyProductStatusReq_ModifyProductOperType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ModifyProductStatusReq_ModifyProductOperType_value, data, "ModifyProductStatusReq_ModifyProductOperType")
	if err != nil {
		return err
	}
	*x = ModifyProductStatusReq_ModifyProductOperType(value)
	return nil
}
func (ModifyProductStatusReq_ModifyProductOperType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{25, 0}
}

// 商品基础类
type MinGuildProduct struct {
	ProductId    uint32 `protobuf:"varint,1,opt,name=product_id,json=productId" json:"product_id"`
	Name         string `protobuf:"bytes,2,req,name=name" json:"name"`
	SourceType   uint32 `protobuf:"varint,3,req,name=source_type,json=sourceType" json:"source_type"`
	SourceId     uint64 `protobuf:"varint,4,req,name=source_id,json=sourceId" json:"source_id"`
	ItemType     uint32 `protobuf:"varint,5,req,name=item_type,json=itemType" json:"item_type"`
	Usage        string `protobuf:"bytes,6,req,name=usage" json:"usage"`
	Description  string `protobuf:"bytes,7,req,name=description" json:"description"`
	Status       uint32 `protobuf:"varint,8,req,name=status" json:"status"`
	GameId       uint32 `protobuf:"varint,9,req,name=game_id,json=gameId" json:"game_id"`
	IconUrl      string `protobuf:"bytes,10,req,name=icon_url,json=iconUrl" json:"icon_url"`
	GuildId      uint32 `protobuf:"varint,11,req,name=guild_id,json=guildId" json:"guild_id"`
	ExchangeType uint32 `protobuf:"varint,12,opt,name=exchange_type,json=exchangeType" json:"exchange_type"`
	Platform     uint32 `protobuf:"varint,13,opt,name=platform" json:"platform"`
}

func (m *MinGuildProduct) Reset()                    { *m = MinGuildProduct{} }
func (m *MinGuildProduct) String() string            { return proto.CompactTextString(m) }
func (*MinGuildProduct) ProtoMessage()               {}
func (*MinGuildProduct) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{0} }

func (m *MinGuildProduct) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *MinGuildProduct) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MinGuildProduct) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *MinGuildProduct) GetSourceId() uint64 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *MinGuildProduct) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *MinGuildProduct) GetUsage() string {
	if m != nil {
		return m.Usage
	}
	return ""
}

func (m *MinGuildProduct) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *MinGuildProduct) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *MinGuildProduct) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *MinGuildProduct) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *MinGuildProduct) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *MinGuildProduct) GetExchangeType() uint32 {
	if m != nil {
		return m.ExchangeType
	}
	return 0
}

func (m *MinGuildProduct) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

// 商品
type GuildProduct struct {
	MinProduct   *MinGuildProduct `protobuf:"bytes,1,req,name=min_product,json=minProduct" json:"min_product,omitempty"`
	CurrencyType uint32           `protobuf:"varint,2,req,name=currency_type,json=currencyType" json:"currency_type"`
	Price        uint32           `protobuf:"varint,3,req,name=price" json:"price"`
	Remain       uint32           `protobuf:"varint,4,req,name=remain" json:"remain"`
	Total        uint32           `protobuf:"varint,5,req,name=total" json:"total"`
	ValidSDate   uint32           `protobuf:"varint,6,opt,name=valid_s_date,json=validSDate" json:"valid_s_date"`
	ValidEDate   uint32           `protobuf:"varint,7,opt,name=valid_e_date,json=validEDate" json:"valid_e_date"`
	Examine      uint32           `protobuf:"varint,8,opt,name=examine" json:"examine"`
	FetchLimit   uint32           `protobuf:"varint,9,opt,name=fetch_limit,json=fetchLimit" json:"fetch_limit"`
	FetchTimes   uint32           `protobuf:"varint,10,opt,name=fetch_times,json=fetchTimes" json:"fetch_times"`
	UpdateTs     uint32           `protobuf:"varint,11,opt,name=update_ts,json=updateTs" json:"update_ts"`
	ExtendInfo   string           `protobuf:"bytes,12,opt,name=extend_info,json=extendInfo" json:"extend_info"`
	DayLimit     uint32           `protobuf:"varint,13,opt,name=day_limit,json=dayLimit" json:"day_limit"`
}

func (m *GuildProduct) Reset()                    { *m = GuildProduct{} }
func (m *GuildProduct) String() string            { return proto.CompactTextString(m) }
func (*GuildProduct) ProtoMessage()               {}
func (*GuildProduct) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{1} }

func (m *GuildProduct) GetMinProduct() *MinGuildProduct {
	if m != nil {
		return m.MinProduct
	}
	return nil
}

func (m *GuildProduct) GetCurrencyType() uint32 {
	if m != nil {
		return m.CurrencyType
	}
	return 0
}

func (m *GuildProduct) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *GuildProduct) GetRemain() uint32 {
	if m != nil {
		return m.Remain
	}
	return 0
}

func (m *GuildProduct) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GuildProduct) GetValidSDate() uint32 {
	if m != nil {
		return m.ValidSDate
	}
	return 0
}

func (m *GuildProduct) GetValidEDate() uint32 {
	if m != nil {
		return m.ValidEDate
	}
	return 0
}

func (m *GuildProduct) GetExamine() uint32 {
	if m != nil {
		return m.Examine
	}
	return 0
}

func (m *GuildProduct) GetFetchLimit() uint32 {
	if m != nil {
		return m.FetchLimit
	}
	return 0
}

func (m *GuildProduct) GetFetchTimes() uint32 {
	if m != nil {
		return m.FetchTimes
	}
	return 0
}

func (m *GuildProduct) GetUpdateTs() uint32 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *GuildProduct) GetExtendInfo() string {
	if m != nil {
		return m.ExtendInfo
	}
	return ""
}

func (m *GuildProduct) GetDayLimit() uint32 {
	if m != nil {
		return m.DayLimit
	}
	return 0
}

// 物品信息
type ProductItem struct {
	Id           uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Uid          uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	GuildId      uint32 `protobuf:"varint,3,req,name=guild_id,json=guildId" json:"guild_id"`
	ProductId    uint32 `protobuf:"varint,4,req,name=product_id,json=productId" json:"product_id"`
	ExchangeType uint32 `protobuf:"varint,5,opt,name=exchange_type,json=exchangeType" json:"exchange_type"`
	ExchangeInfo []byte `protobuf:"bytes,6,opt,name=exchange_info,json=exchangeInfo" json:"exchange_info"`
	Date         uint32 `protobuf:"varint,7,opt,name=date" json:"date"`
}

func (m *ProductItem) Reset()                    { *m = ProductItem{} }
func (m *ProductItem) String() string            { return proto.CompactTextString(m) }
func (*ProductItem) ProtoMessage()               {}
func (*ProductItem) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{2} }

func (m *ProductItem) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ProductItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ProductItem) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ProductItem) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *ProductItem) GetExchangeType() uint32 {
	if m != nil {
		return m.ExchangeType
	}
	return 0
}

func (m *ProductItem) GetExchangeInfo() []byte {
	if m != nil {
		return m.ExchangeInfo
	}
	return nil
}

func (m *ProductItem) GetDate() uint32 {
	if m != nil {
		return m.Date
	}
	return 0
}

type AddProductReq struct {
	Uid        uint32        `protobuf:"varint,1,req,name=uid" json:"uid"`
	Product    *GuildProduct `protobuf:"bytes,2,req,name=product" json:"product,omitempty"`
	ItemBinary [][]byte      `protobuf:"bytes,3,rep,name=item_binary,json=itemBinary" json:"item_binary,omitempty"`
	Payment    uint32        `protobuf:"varint,4,opt,name=payment" json:"payment"`
}

func (m *AddProductReq) Reset()                    { *m = AddProductReq{} }
func (m *AddProductReq) String() string            { return proto.CompactTextString(m) }
func (*AddProductReq) ProtoMessage()               {}
func (*AddProductReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{3} }

func (m *AddProductReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddProductReq) GetProduct() *GuildProduct {
	if m != nil {
		return m.Product
	}
	return nil
}

func (m *AddProductReq) GetItemBinary() [][]byte {
	if m != nil {
		return m.ItemBinary
	}
	return nil
}

func (m *AddProductReq) GetPayment() uint32 {
	if m != nil {
		return m.Payment
	}
	return 0
}

type AddProductResp struct {
	ProductId uint32 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
}

func (m *AddProductResp) Reset()                    { *m = AddProductResp{} }
func (m *AddProductResp) String() string            { return proto.CompactTextString(m) }
func (*AddProductResp) ProtoMessage()               {}
func (*AddProductResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{4} }

func (m *AddProductResp) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

// 查找礼包
type GetProductReq struct {
	GuildId       uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	ProductId     uint32 `protobuf:"varint,2,opt,name=product_id,json=productId" json:"product_id"`
	SourceType    uint32 `protobuf:"varint,3,opt,name=source_type,json=sourceType" json:"source_type"`
	ItemType      uint32 `protobuf:"varint,4,opt,name=item_type,json=itemType" json:"item_type"`
	ProductStatus uint32 `protobuf:"varint,5,opt,name=product_status,json=productStatus" json:"product_status"`
	NeedUnshelve  uint32 `protobuf:"varint,6,opt,name=need_unshelve,json=needUnshelve" json:"need_unshelve"`
	Index         uint32 `protobuf:"varint,7,opt,name=index" json:"index"`
	Cnt           uint32 `protobuf:"varint,8,opt,name=cnt" json:"cnt"`
	Platform      uint32 `protobuf:"varint,9,opt,name=platform" json:"platform"`
}

func (m *GetProductReq) Reset()                    { *m = GetProductReq{} }
func (m *GetProductReq) String() string            { return proto.CompactTextString(m) }
func (*GetProductReq) ProtoMessage()               {}
func (*GetProductReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{5} }

func (m *GetProductReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetProductReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GetProductReq) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *GetProductReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *GetProductReq) GetProductStatus() uint32 {
	if m != nil {
		return m.ProductStatus
	}
	return 0
}

func (m *GetProductReq) GetNeedUnshelve() uint32 {
	if m != nil {
		return m.NeedUnshelve
	}
	return 0
}

func (m *GetProductReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *GetProductReq) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

func (m *GetProductReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

type GetProductResp struct {
	ProductLst []*GuildProduct `protobuf:"bytes,1,rep,name=product_lst,json=productLst" json:"product_lst,omitempty"`
	Total      uint32          `protobuf:"varint,2,opt,name=total" json:"total"`
}

func (m *GetProductResp) Reset()                    { *m = GetProductResp{} }
func (m *GetProductResp) String() string            { return proto.CompactTextString(m) }
func (*GetProductResp) ProtoMessage()               {}
func (*GetProductResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{6} }

func (m *GetProductResp) GetProductLst() []*GuildProduct {
	if m != nil {
		return m.ProductLst
	}
	return nil
}

func (m *GetProductResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type BatchGetProductByIdReq struct {
	GuildId      uint32   `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	ProductIdLst []uint32 `protobuf:"varint,2,rep,name=product_id_lst,json=productIdLst" json:"product_id_lst,omitempty"`
	Platform     uint32   `protobuf:"varint,3,opt,name=platform" json:"platform"`
}

func (m *BatchGetProductByIdReq) Reset()         { *m = BatchGetProductByIdReq{} }
func (m *BatchGetProductByIdReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetProductByIdReq) ProtoMessage()    {}
func (*BatchGetProductByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{7}
}

func (m *BatchGetProductByIdReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetProductByIdReq) GetProductIdLst() []uint32 {
	if m != nil {
		return m.ProductIdLst
	}
	return nil
}

func (m *BatchGetProductByIdReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

type BatchGetProductByIdResp struct {
	ProductLst []*GuildProduct `protobuf:"bytes,1,rep,name=product_lst,json=productLst" json:"product_lst,omitempty"`
}

func (m *BatchGetProductByIdResp) Reset()         { *m = BatchGetProductByIdResp{} }
func (m *BatchGetProductByIdResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetProductByIdResp) ProtoMessage()    {}
func (*BatchGetProductByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{8}
}

func (m *BatchGetProductByIdResp) GetProductLst() []*GuildProduct {
	if m != nil {
		return m.ProductLst
	}
	return nil
}

// 搜索
type SearchProductReq struct {
	GuildId      uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	KeyWord      string `protobuf:"bytes,2,req,name=key_word,json=keyWord" json:"key_word"`
	Index        uint32 `protobuf:"varint,3,opt,name=index" json:"index"`
	Cnt          uint32 `protobuf:"varint,4,opt,name=cnt" json:"cnt"`
	NeedUnshelve uint32 `protobuf:"varint,5,opt,name=need_unshelve,json=needUnshelve" json:"need_unshelve"`
	Platform     uint32 `protobuf:"varint,6,opt,name=platform" json:"platform"`
}

func (m *SearchProductReq) Reset()                    { *m = SearchProductReq{} }
func (m *SearchProductReq) String() string            { return proto.CompactTextString(m) }
func (*SearchProductReq) ProtoMessage()               {}
func (*SearchProductReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{9} }

func (m *SearchProductReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SearchProductReq) GetKeyWord() string {
	if m != nil {
		return m.KeyWord
	}
	return ""
}

func (m *SearchProductReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *SearchProductReq) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

func (m *SearchProductReq) GetNeedUnshelve() uint32 {
	if m != nil {
		return m.NeedUnshelve
	}
	return 0
}

func (m *SearchProductReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

type SearchProductResp struct {
	Total      uint32          `protobuf:"varint,1,req,name=total" json:"total"`
	ProductLst []*GuildProduct `protobuf:"bytes,2,rep,name=product_lst,json=productLst" json:"product_lst,omitempty"`
}

func (m *SearchProductResp) Reset()                    { *m = SearchProductResp{} }
func (m *SearchProductResp) String() string            { return proto.CompactTextString(m) }
func (*SearchProductResp) ProtoMessage()               {}
func (*SearchProductResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{10} }

func (m *SearchProductResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *SearchProductResp) GetProductLst() []*GuildProduct {
	if m != nil {
		return m.ProductLst
	}
	return nil
}

// 审查相关
type ExamineRecord struct {
	ExamineId  uint32        `protobuf:"varint,1,req,name=examine_id,json=examineId" json:"examine_id"`
	GuildId    uint32        `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	Uid        uint32        `protobuf:"varint,3,req,name=uid" json:"uid"`
	Date       uint32        `protobuf:"varint,4,req,name=date" json:"date"`
	Status     uint32        `protobuf:"varint,5,req,name=status" json:"status"`
	Num        uint32        `protobuf:"varint,6,req,name=num" json:"num"`
	Product    *GuildProduct `protobuf:"bytes,7,req,name=product" json:"product,omitempty"`
	GuildMsgId uint32        `protobuf:"varint,8,opt,name=guild_msg_id,json=guildMsgId" json:"guild_msg_id"`
}

func (m *ExamineRecord) Reset()                    { *m = ExamineRecord{} }
func (m *ExamineRecord) String() string            { return proto.CompactTextString(m) }
func (*ExamineRecord) ProtoMessage()               {}
func (*ExamineRecord) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{11} }

func (m *ExamineRecord) GetExamineId() uint32 {
	if m != nil {
		return m.ExamineId
	}
	return 0
}

func (m *ExamineRecord) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ExamineRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ExamineRecord) GetDate() uint32 {
	if m != nil {
		return m.Date
	}
	return 0
}

func (m *ExamineRecord) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ExamineRecord) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *ExamineRecord) GetProduct() *GuildProduct {
	if m != nil {
		return m.Product
	}
	return nil
}

func (m *ExamineRecord) GetGuildMsgId() uint32 {
	if m != nil {
		return m.GuildMsgId
	}
	return 0
}

// 拉审核列表
type GetExamineRecordLstReq struct {
	GuildId       uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	ProductId     uint32 `protobuf:"varint,2,opt,name=product_id,json=productId" json:"product_id"`
	ToUid         uint32 `protobuf:"varint,3,opt,name=to_uid,json=toUid" json:"to_uid"`
	ExamineId     uint32 `protobuf:"varint,4,opt,name=examine_id,json=examineId" json:"examine_id"`
	ExamineStatus uint32 `protobuf:"varint,5,opt,name=examine_status,json=examineStatus" json:"examine_status"`
}

func (m *GetExamineRecordLstReq) Reset()         { *m = GetExamineRecordLstReq{} }
func (m *GetExamineRecordLstReq) String() string { return proto.CompactTextString(m) }
func (*GetExamineRecordLstReq) ProtoMessage()    {}
func (*GetExamineRecordLstReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{12}
}

func (m *GetExamineRecordLstReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetExamineRecordLstReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GetExamineRecordLstReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *GetExamineRecordLstReq) GetExamineId() uint32 {
	if m != nil {
		return m.ExamineId
	}
	return 0
}

func (m *GetExamineRecordLstReq) GetExamineStatus() uint32 {
	if m != nil {
		return m.ExamineStatus
	}
	return 0
}

type GetExamineRecordLstResp struct {
	RecordLst []*ExamineRecord `protobuf:"bytes,1,rep,name=record_lst,json=recordLst" json:"record_lst,omitempty"`
}

func (m *GetExamineRecordLstResp) Reset()         { *m = GetExamineRecordLstResp{} }
func (m *GetExamineRecordLstResp) String() string { return proto.CompactTextString(m) }
func (*GetExamineRecordLstResp) ProtoMessage()    {}
func (*GetExamineRecordLstResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{13}
}

func (m *GetExamineRecordLstResp) GetRecordLst() []*ExamineRecord {
	if m != nil {
		return m.RecordLst
	}
	return nil
}

// 处理审核
type DealExamineReq struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	ExamineId uint32 `protobuf:"varint,2,req,name=examine_id,json=examineId" json:"examine_id"`
	Status    uint32 `protobuf:"varint,3,req,name=status" json:"status"`
}

func (m *DealExamineReq) Reset()                    { *m = DealExamineReq{} }
func (m *DealExamineReq) String() string            { return proto.CompactTextString(m) }
func (*DealExamineReq) ProtoMessage()               {}
func (*DealExamineReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{14} }

func (m *DealExamineReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *DealExamineReq) GetExamineId() uint32 {
	if m != nil {
		return m.ExamineId
	}
	return 0
}

func (m *DealExamineReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type DealExamineResp struct {
	ToUid      uint32        `protobuf:"varint,1,req,name=to_uid,json=toUid" json:"to_uid"`
	Product    *GuildProduct `protobuf:"bytes,2,req,name=product" json:"product,omitempty"`
	Item       *ProductItem  `protobuf:"bytes,3,opt,name=item" json:"item,omitempty"`
	GuildMsgId uint32        `protobuf:"varint,4,opt,name=guild_msg_id,json=guildMsgId" json:"guild_msg_id"`
}

func (m *DealExamineResp) Reset()                    { *m = DealExamineResp{} }
func (m *DealExamineResp) String() string            { return proto.CompactTextString(m) }
func (*DealExamineResp) ProtoMessage()               {}
func (*DealExamineResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{15} }

func (m *DealExamineResp) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *DealExamineResp) GetProduct() *GuildProduct {
	if m != nil {
		return m.Product
	}
	return nil
}

func (m *DealExamineResp) GetItem() *ProductItem {
	if m != nil {
		return m.Item
	}
	return nil
}

func (m *DealExamineResp) GetGuildMsgId() uint32 {
	if m != nil {
		return m.GuildMsgId
	}
	return 0
}

// examine写入msg_id
type ModifyExamineApplyReq struct {
	GuildId    uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	ExamineId  uint32 `protobuf:"varint,2,req,name=examine_id,json=examineId" json:"examine_id"`
	GuildMsgId uint32 `protobuf:"varint,3,req,name=guild_msg_id,json=guildMsgId" json:"guild_msg_id"`
}

func (m *ModifyExamineApplyReq) Reset()         { *m = ModifyExamineApplyReq{} }
func (m *ModifyExamineApplyReq) String() string { return proto.CompactTextString(m) }
func (*ModifyExamineApplyReq) ProtoMessage()    {}
func (*ModifyExamineApplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{16}
}

func (m *ModifyExamineApplyReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ModifyExamineApplyReq) GetExamineId() uint32 {
	if m != nil {
		return m.ExamineId
	}
	return 0
}

func (m *ModifyExamineApplyReq) GetGuildMsgId() uint32 {
	if m != nil {
		return m.GuildMsgId
	}
	return 0
}

type ModifyExamineApplyResp struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	ExamineId uint32 `protobuf:"varint,2,req,name=examine_id,json=examineId" json:"examine_id"`
}

func (m *ModifyExamineApplyResp) Reset()         { *m = ModifyExamineApplyResp{} }
func (m *ModifyExamineApplyResp) String() string { return proto.CompactTextString(m) }
func (*ModifyExamineApplyResp) ProtoMessage()    {}
func (*ModifyExamineApplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{17}
}

func (m *ModifyExamineApplyResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ModifyExamineApplyResp) GetExamineId() uint32 {
	if m != nil {
		return m.ExamineId
	}
	return 0
}

// 仓库操作记录
type StorageOperRecord struct {
	RecordId   uint32        `protobuf:"varint,1,req,name=record_id,json=recordId" json:"record_id"`
	Uid        uint32        `protobuf:"varint,2,req,name=uid" json:"uid"`
	GuildId    uint32        `protobuf:"varint,3,req,name=guild_id,json=guildId" json:"guild_id"`
	OperType   uint32        `protobuf:"varint,4,req,name=oper_type,json=operType" json:"oper_type"`
	Date       uint32        `protobuf:"varint,5,opt,name=date" json:"date"`
	Product    *GuildProduct `protobuf:"bytes,6,opt,name=product" json:"product,omitempty"`
	Payment    uint32        `protobuf:"varint,7,opt,name=payment" json:"payment"`
	ToUid      uint32        `protobuf:"varint,8,opt,name=to_uid,json=toUid" json:"to_uid"`
	ExtendInfo string        `protobuf:"bytes,9,opt,name=extend_info,json=extendInfo" json:"extend_info"`
	Category   uint32        `protobuf:"varint,10,opt,name=category" json:"category"`
}

func (m *StorageOperRecord) Reset()                    { *m = StorageOperRecord{} }
func (m *StorageOperRecord) String() string            { return proto.CompactTextString(m) }
func (*StorageOperRecord) ProtoMessage()               {}
func (*StorageOperRecord) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{18} }

func (m *StorageOperRecord) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *StorageOperRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StorageOperRecord) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *StorageOperRecord) GetOperType() uint32 {
	if m != nil {
		return m.OperType
	}
	return 0
}

func (m *StorageOperRecord) GetDate() uint32 {
	if m != nil {
		return m.Date
	}
	return 0
}

func (m *StorageOperRecord) GetProduct() *GuildProduct {
	if m != nil {
		return m.Product
	}
	return nil
}

func (m *StorageOperRecord) GetPayment() uint32 {
	if m != nil {
		return m.Payment
	}
	return 0
}

func (m *StorageOperRecord) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *StorageOperRecord) GetExtendInfo() string {
	if m != nil {
		return m.ExtendInfo
	}
	return ""
}

func (m *StorageOperRecord) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

type StorageGetOperRecordReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	Index   uint32 `protobuf:"varint,2,opt,name=index" json:"index"`
	Cnt     uint32 `protobuf:"varint,3,opt,name=cnt" json:"cnt"`
}

func (m *StorageGetOperRecordReq) Reset()         { *m = StorageGetOperRecordReq{} }
func (m *StorageGetOperRecordReq) String() string { return proto.CompactTextString(m) }
func (*StorageGetOperRecordReq) ProtoMessage()    {}
func (*StorageGetOperRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{19}
}

func (m *StorageGetOperRecordReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *StorageGetOperRecordReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *StorageGetOperRecordReq) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type StorageGetOperRecordResp struct {
	TotalRecord uint32               `protobuf:"varint,1,req,name=total_record,json=totalRecord" json:"total_record"`
	RecordLst   []*StorageOperRecord `protobuf:"bytes,2,rep,name=record_lst,json=recordLst" json:"record_lst,omitempty"`
}

func (m *StorageGetOperRecordResp) Reset()         { *m = StorageGetOperRecordResp{} }
func (m *StorageGetOperRecordResp) String() string { return proto.CompactTextString(m) }
func (*StorageGetOperRecordResp) ProtoMessage()    {}
func (*StorageGetOperRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{20}
}

func (m *StorageGetOperRecordResp) GetTotalRecord() uint32 {
	if m != nil {
		return m.TotalRecord
	}
	return 0
}

func (m *StorageGetOperRecordResp) GetRecordLst() []*StorageOperRecord {
	if m != nil {
		return m.RecordLst
	}
	return nil
}

// 申请商品
type GainProductReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId   uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	ProductId uint32 `protobuf:"varint,3,req,name=product_id,json=productId" json:"product_id"`
	// 价格核对用
	CurrencyType uint32 `protobuf:"varint,4,opt,name=currency_type,json=currencyType" json:"currency_type"`
	Price        uint32 `protobuf:"varint,5,opt,name=price" json:"price"`
}

func (m *GainProductReq) Reset()                    { *m = GainProductReq{} }
func (m *GainProductReq) String() string            { return proto.CompactTextString(m) }
func (*GainProductReq) ProtoMessage()               {}
func (*GainProductReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{21} }

func (m *GainProductReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GainProductReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GainProductReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GainProductReq) GetCurrencyType() uint32 {
	if m != nil {
		return m.CurrencyType
	}
	return 0
}

func (m *GainProductReq) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

type GainProductResp struct {
	Status    uint32        `protobuf:"varint,1,req,name=status" json:"status"`
	Item      *ProductItem  `protobuf:"bytes,2,opt,name=item" json:"item,omitempty"`
	Product   *GuildProduct `protobuf:"bytes,3,opt,name=product" json:"product,omitempty"`
	ExamineId uint32        `protobuf:"varint,4,opt,name=examine_id,json=examineId" json:"examine_id"`
}

func (m *GainProductResp) Reset()                    { *m = GainProductResp{} }
func (m *GainProductResp) String() string            { return proto.CompactTextString(m) }
func (*GainProductResp) ProtoMessage()               {}
func (*GainProductResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{22} }

func (m *GainProductResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GainProductResp) GetItem() *ProductItem {
	if m != nil {
		return m.Item
	}
	return nil
}

func (m *GainProductResp) GetProduct() *GuildProduct {
	if m != nil {
		return m.Product
	}
	return nil
}

func (m *GainProductResp) GetExamineId() uint32 {
	if m != nil {
		return m.ExamineId
	}
	return 0
}

// 分配商品
type AllotProductReq struct {
	ToUid     uint32 `protobuf:"varint,1,req,name=to_uid,json=toUid" json:"to_uid"`
	GuildId   uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	ProductId uint32 `protobuf:"varint,3,req,name=product_id,json=productId" json:"product_id"`
	Num       uint32 `protobuf:"varint,4,req,name=num" json:"num"`
}

func (m *AllotProductReq) Reset()                    { *m = AllotProductReq{} }
func (m *AllotProductReq) String() string            { return proto.CompactTextString(m) }
func (*AllotProductReq) ProtoMessage()               {}
func (*AllotProductReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{23} }

func (m *AllotProductReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *AllotProductReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AllotProductReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *AllotProductReq) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

type AllotProductResp struct {
	ToUid   uint32         `protobuf:"varint,1,req,name=to_uid,json=toUid" json:"to_uid"`
	Product *GuildProduct  `protobuf:"bytes,2,opt,name=product" json:"product,omitempty"`
	ItemLst []*ProductItem `protobuf:"bytes,3,rep,name=item_lst,json=itemLst" json:"item_lst,omitempty"`
	Total   uint32         `protobuf:"varint,4,opt,name=total" json:"total"`
}

func (m *AllotProductResp) Reset()                    { *m = AllotProductResp{} }
func (m *AllotProductResp) String() string            { return proto.CompactTextString(m) }
func (*AllotProductResp) ProtoMessage()               {}
func (*AllotProductResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{24} }

func (m *AllotProductResp) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *AllotProductResp) GetProduct() *GuildProduct {
	if m != nil {
		return m.Product
	}
	return nil
}

func (m *AllotProductResp) GetItemLst() []*ProductItem {
	if m != nil {
		return m.ItemLst
	}
	return nil
}

func (m *AllotProductResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 修改商品
type ModifyProductStatusReq struct {
	GuildId    uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	ProductId  uint32 `protobuf:"varint,2,req,name=product_id,json=productId" json:"product_id"`
	OperType   uint32 `protobuf:"varint,3,req,name=oper_type,json=operType" json:"oper_type"`
	Num        uint32 `protobuf:"varint,4,opt,name=num" json:"num"`
	Price      uint32 `protobuf:"varint,5,opt,name=price" json:"price"`
	Examine    uint32 `protobuf:"varint,6,opt,name=examine" json:"examine"`
	FetchLimit uint32 `protobuf:"varint,7,opt,name=fetch_limit,json=fetchLimit" json:"fetch_limit"`
	DayLimit   uint32 `protobuf:"varint,8,opt,name=day_limit,json=dayLimit" json:"day_limit"`
}

func (m *ModifyProductStatusReq) Reset()         { *m = ModifyProductStatusReq{} }
func (m *ModifyProductStatusReq) String() string { return proto.CompactTextString(m) }
func (*ModifyProductStatusReq) ProtoMessage()    {}
func (*ModifyProductStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{25}
}

func (m *ModifyProductStatusReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ModifyProductStatusReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *ModifyProductStatusReq) GetOperType() uint32 {
	if m != nil {
		return m.OperType
	}
	return 0
}

func (m *ModifyProductStatusReq) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *ModifyProductStatusReq) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *ModifyProductStatusReq) GetExamine() uint32 {
	if m != nil {
		return m.Examine
	}
	return 0
}

func (m *ModifyProductStatusReq) GetFetchLimit() uint32 {
	if m != nil {
		return m.FetchLimit
	}
	return 0
}

func (m *ModifyProductStatusReq) GetDayLimit() uint32 {
	if m != nil {
		return m.DayLimit
	}
	return 0
}

type ModifyProductStatusResp struct {
}

func (m *ModifyProductStatusResp) Reset()         { *m = ModifyProductStatusResp{} }
func (m *ModifyProductStatusResp) String() string { return proto.CompactTextString(m) }
func (*ModifyProductStatusResp) ProtoMessage()    {}
func (*ModifyProductStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{26}
}

type GetProductBySourceIdReq struct {
	GuildId     uint32   `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	SourceType  uint32   `protobuf:"varint,2,opt,name=source_type,json=sourceType" json:"source_type"`
	SourceIdLst []uint64 `protobuf:"varint,3,rep,name=source_id_lst,json=sourceIdLst" json:"source_id_lst,omitempty"`
}

func (m *GetProductBySourceIdReq) Reset()         { *m = GetProductBySourceIdReq{} }
func (m *GetProductBySourceIdReq) String() string { return proto.CompactTextString(m) }
func (*GetProductBySourceIdReq) ProtoMessage()    {}
func (*GetProductBySourceIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{27}
}

func (m *GetProductBySourceIdReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetProductBySourceIdReq) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *GetProductBySourceIdReq) GetSourceIdLst() []uint64 {
	if m != nil {
		return m.SourceIdLst
	}
	return nil
}

type GetProductBySourceIdResp struct {
	ProductLst []*GuildProduct `protobuf:"bytes,1,rep,name=product_lst,json=productLst" json:"product_lst,omitempty"`
}

func (m *GetProductBySourceIdResp) Reset()         { *m = GetProductBySourceIdResp{} }
func (m *GetProductBySourceIdResp) String() string { return proto.CompactTextString(m) }
func (*GetProductBySourceIdResp) ProtoMessage()    {}
func (*GetProductBySourceIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{28}
}

func (m *GetProductBySourceIdResp) GetProductLst() []*GuildProduct {
	if m != nil {
		return m.ProductLst
	}
	return nil
}

type GetGameProductReq struct {
	GuildId          uint32   `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	GameId           uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId" json:"game_id"`
	SourceType       uint32   `protobuf:"varint,3,opt,name=source_type,json=sourceType" json:"source_type"`
	ItemType         uint32   `protobuf:"varint,4,opt,name=item_type,json=itemType" json:"item_type"`
	NeedUnshelve     uint32   `protobuf:"varint,5,opt,name=need_unshelve,json=needUnshelve" json:"need_unshelve"`
	Platform         uint32   `protobuf:"varint,6,opt,name=platform" json:"platform"`
	InstallGameIdLst []uint32 `protobuf:"varint,7,rep,name=install_game_id_lst,json=installGameIdLst" json:"install_game_id_lst,omitempty"`
}

func (m *GetGameProductReq) Reset()                    { *m = GetGameProductReq{} }
func (m *GetGameProductReq) String() string            { return proto.CompactTextString(m) }
func (*GetGameProductReq) ProtoMessage()               {}
func (*GetGameProductReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{29} }

func (m *GetGameProductReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGameProductReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetGameProductReq) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *GetGameProductReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *GetGameProductReq) GetNeedUnshelve() uint32 {
	if m != nil {
		return m.NeedUnshelve
	}
	return 0
}

func (m *GetGameProductReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *GetGameProductReq) GetInstallGameIdLst() []uint32 {
	if m != nil {
		return m.InstallGameIdLst
	}
	return nil
}

type GameProduct struct {
	GameId     uint32          `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	ProductLst []*GuildProduct `protobuf:"bytes,2,rep,name=product_lst,json=productLst" json:"product_lst,omitempty"`
}

func (m *GameProduct) Reset()                    { *m = GameProduct{} }
func (m *GameProduct) String() string            { return proto.CompactTextString(m) }
func (*GameProduct) ProtoMessage()               {}
func (*GameProduct) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{30} }

func (m *GameProduct) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GameProduct) GetProductLst() []*GuildProduct {
	if m != nil {
		return m.ProductLst
	}
	return nil
}

// 结果以gameId分类
type GetGameProductResp struct {
	GameProductLst []*GameProduct `protobuf:"bytes,1,rep,name=game_product_lst,json=gameProductLst" json:"game_product_lst,omitempty"`
	Total          uint32         `protobuf:"varint,2,opt,name=total" json:"total"`
}

func (m *GetGameProductResp) Reset()                    { *m = GetGameProductResp{} }
func (m *GetGameProductResp) String() string            { return proto.CompactTextString(m) }
func (*GetGameProductResp) ProtoMessage()               {}
func (*GetGameProductResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{31} }

func (m *GetGameProductResp) GetGameProductLst() []*GameProduct {
	if m != nil {
		return m.GameProductLst
	}
	return nil
}

func (m *GetGameProductResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 移除商品
type DeleteProductReq struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	ProductId uint32 `protobuf:"varint,2,req,name=product_id,json=productId" json:"product_id"`
	Num       uint32 `protobuf:"varint,3,opt,name=num" json:"num"`
}

func (m *DeleteProductReq) Reset()                    { *m = DeleteProductReq{} }
func (m *DeleteProductReq) String() string            { return proto.CompactTextString(m) }
func (*DeleteProductReq) ProtoMessage()               {}
func (*DeleteProductReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{32} }

func (m *DeleteProductReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *DeleteProductReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *DeleteProductReq) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

type DeleteProductResp struct {
}

func (m *DeleteProductResp) Reset()                    { *m = DeleteProductResp{} }
func (m *DeleteProductResp) String() string            { return proto.CompactTextString(m) }
func (*DeleteProductResp) ProtoMessage()               {}
func (*DeleteProductResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{33} }

// 领取记录
type GetFetchRecordReq struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	ProductId uint32 `protobuf:"varint,2,req,name=product_id,json=productId" json:"product_id"`
	Index     uint32 `protobuf:"varint,3,opt,name=index" json:"index"`
	Cnt       uint32 `protobuf:"varint,4,opt,name=cnt" json:"cnt"`
}

func (m *GetFetchRecordReq) Reset()                    { *m = GetFetchRecordReq{} }
func (m *GetFetchRecordReq) String() string            { return proto.CompactTextString(m) }
func (*GetFetchRecordReq) ProtoMessage()               {}
func (*GetFetchRecordReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{34} }

func (m *GetFetchRecordReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetFetchRecordReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GetFetchRecordReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *GetFetchRecordReq) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type UserFetchRecord struct {
	RecordId uint32 `protobuf:"varint,1,req,name=record_id,json=recordId" json:"record_id"`
	Uid      uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	Date     uint32 `protobuf:"varint,3,req,name=date" json:"date"`
}

func (m *UserFetchRecord) Reset()                    { *m = UserFetchRecord{} }
func (m *UserFetchRecord) String() string            { return proto.CompactTextString(m) }
func (*UserFetchRecord) ProtoMessage()               {}
func (*UserFetchRecord) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{35} }

func (m *UserFetchRecord) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *UserFetchRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserFetchRecord) GetDate() uint32 {
	if m != nil {
		return m.Date
	}
	return 0
}

type GetFetchRecordResp struct {
	GuildId   uint32             `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	ProductId uint32             `protobuf:"varint,2,req,name=product_id,json=productId" json:"product_id"`
	Total     uint32             `protobuf:"varint,3,req,name=total" json:"total"`
	RecordLst []*UserFetchRecord `protobuf:"bytes,4,rep,name=record_lst,json=recordLst" json:"record_lst,omitempty"`
}

func (m *GetFetchRecordResp) Reset()                    { *m = GetFetchRecordResp{} }
func (m *GetFetchRecordResp) String() string            { return proto.CompactTextString(m) }
func (*GetFetchRecordResp) ProtoMessage()               {}
func (*GetFetchRecordResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{36} }

func (m *GetFetchRecordResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetFetchRecordResp) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GetFetchRecordResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetFetchRecordResp) GetRecordLst() []*UserFetchRecord {
	if m != nil {
		return m.RecordLst
	}
	return nil
}

type BatchGetUserFetchRecordReq struct {
	Uid          uint32   `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId      uint32   `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	ProductIdLst []uint32 `protobuf:"varint,3,rep,name=product_id_lst,json=productIdLst" json:"product_id_lst,omitempty"`
	OnlyTotal    bool     `protobuf:"varint,4,opt,name=only_total,json=onlyTotal" json:"only_total"`
}

func (m *BatchGetUserFetchRecordReq) Reset()         { *m = BatchGetUserFetchRecordReq{} }
func (m *BatchGetUserFetchRecordReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserFetchRecordReq) ProtoMessage()    {}
func (*BatchGetUserFetchRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{37}
}

func (m *BatchGetUserFetchRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchGetUserFetchRecordReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetUserFetchRecordReq) GetProductIdLst() []uint32 {
	if m != nil {
		return m.ProductIdLst
	}
	return nil
}

func (m *BatchGetUserFetchRecordReq) GetOnlyTotal() bool {
	if m != nil {
		return m.OnlyTotal
	}
	return false
}

type BatchGetUserFetchRecordResp struct {
	RecordLst []*GetFetchRecordResp `protobuf:"bytes,1,rep,name=record_lst,json=recordLst" json:"record_lst,omitempty"`
}

func (m *BatchGetUserFetchRecordResp) Reset()         { *m = BatchGetUserFetchRecordResp{} }
func (m *BatchGetUserFetchRecordResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserFetchRecordResp) ProtoMessage()    {}
func (*BatchGetUserFetchRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{38}
}

func (m *BatchGetUserFetchRecordResp) GetRecordLst() []*GetFetchRecordResp {
	if m != nil {
		return m.RecordLst
	}
	return nil
}

// 礼包统计
type CalculateProductReq struct {
	GuildId      uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	SourceType   uint32 `protobuf:"varint,2,req,name=source_type,json=sourceType" json:"source_type"`
	ItemType     uint32 `protobuf:"varint,3,opt,name=item_type,json=itemType" json:"item_type"`
	NeedUnshelve uint32 `protobuf:"varint,4,opt,name=need_unshelve,json=needUnshelve" json:"need_unshelve"`
	Platform     uint32 `protobuf:"varint,5,opt,name=platform" json:"platform"`
}

func (m *CalculateProductReq) Reset()                    { *m = CalculateProductReq{} }
func (m *CalculateProductReq) String() string            { return proto.CompactTextString(m) }
func (*CalculateProductReq) ProtoMessage()               {}
func (*CalculateProductReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{39} }

func (m *CalculateProductReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CalculateProductReq) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *CalculateProductReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *CalculateProductReq) GetNeedUnshelve() uint32 {
	if m != nil {
		return m.NeedUnshelve
	}
	return 0
}

func (m *CalculateProductReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

type CalculateProductItem struct {
	SourceType uint32 `protobuf:"varint,1,req,name=source_type,json=sourceType" json:"source_type"`
	ItemType   uint32 `protobuf:"varint,2,req,name=item_type,json=itemType" json:"item_type"`
	Total      uint32 `protobuf:"varint,3,req,name=total" json:"total"`
}

func (m *CalculateProductItem) Reset()         { *m = CalculateProductItem{} }
func (m *CalculateProductItem) String() string { return proto.CompactTextString(m) }
func (*CalculateProductItem) ProtoMessage()    {}
func (*CalculateProductItem) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{40}
}

func (m *CalculateProductItem) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *CalculateProductItem) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *CalculateProductItem) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type CalculateProductResp struct {
	GuildId uint32                  `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	ItemLst []*CalculateProductItem `protobuf:"bytes,2,rep,name=item_lst,json=itemLst" json:"item_lst,omitempty"`
}

func (m *CalculateProductResp) Reset()         { *m = CalculateProductResp{} }
func (m *CalculateProductResp) String() string { return proto.CompactTextString(m) }
func (*CalculateProductResp) ProtoMessage()    {}
func (*CalculateProductResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{41}
}

func (m *CalculateProductResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CalculateProductResp) GetItemLst() []*CalculateProductItem {
	if m != nil {
		return m.ItemLst
	}
	return nil
}

// 通过sourceId获得物品
type GetGuildBySourceIdReq struct {
	SourceId   uint32 `protobuf:"varint,1,req,name=source_id,json=sourceId" json:"source_id"`
	SourceType uint32 `protobuf:"varint,2,opt,name=source_type,json=sourceType" json:"source_type"`
	ItemStatus uint32 `protobuf:"varint,3,opt,name=item_status,json=itemStatus" json:"item_status"`
}

func (m *GetGuildBySourceIdReq) Reset()         { *m = GetGuildBySourceIdReq{} }
func (m *GetGuildBySourceIdReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildBySourceIdReq) ProtoMessage()    {}
func (*GetGuildBySourceIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{42}
}

func (m *GetGuildBySourceIdReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *GetGuildBySourceIdReq) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *GetGuildBySourceIdReq) GetItemStatus() uint32 {
	if m != nil {
		return m.ItemStatus
	}
	return 0
}

type GetGuildBySourceIdResp struct {
	GuildIdLst []uint32 `protobuf:"varint,1,rep,name=guild_id_lst,json=guildIdLst" json:"guild_id_lst,omitempty"`
}

func (m *GetGuildBySourceIdResp) Reset()         { *m = GetGuildBySourceIdResp{} }
func (m *GetGuildBySourceIdResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildBySourceIdResp) ProtoMessage()    {}
func (*GetGuildBySourceIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{43}
}

func (m *GetGuildBySourceIdResp) GetGuildIdLst() []uint32 {
	if m != nil {
		return m.GuildIdLst
	}
	return nil
}

// 获取用户状态
type UserStorageInfo struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId    uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	ProductId  uint32 `protobuf:"varint,3,req,name=product_id,json=productId" json:"product_id"`
	ExamineId  uint32 `protobuf:"varint,4,opt,name=examine_id,json=examineId" json:"examine_id"`
	FetchTimes uint32 `protobuf:"varint,5,opt,name=fetch_times,json=fetchTimes" json:"fetch_times"`
}

func (m *UserStorageInfo) Reset()                    { *m = UserStorageInfo{} }
func (m *UserStorageInfo) String() string            { return proto.CompactTextString(m) }
func (*UserStorageInfo) ProtoMessage()               {}
func (*UserStorageInfo) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{44} }

func (m *UserStorageInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserStorageInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UserStorageInfo) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *UserStorageInfo) GetExamineId() uint32 {
	if m != nil {
		return m.ExamineId
	}
	return 0
}

func (m *UserStorageInfo) GetFetchTimes() uint32 {
	if m != nil {
		return m.FetchTimes
	}
	return 0
}

type GetUserStorageInfoReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId   uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	ProductId uint32 `protobuf:"varint,3,req,name=product_id,json=productId" json:"product_id"`
}

func (m *GetUserStorageInfoReq) Reset()         { *m = GetUserStorageInfoReq{} }
func (m *GetUserStorageInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserStorageInfoReq) ProtoMessage()    {}
func (*GetUserStorageInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{45}
}

func (m *GetUserStorageInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserStorageInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetUserStorageInfoReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

type GetUserStorageInfoResp struct {
}

func (m *GetUserStorageInfoResp) Reset()         { *m = GetUserStorageInfoResp{} }
func (m *GetUserStorageInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserStorageInfoResp) ProtoMessage()    {}
func (*GetUserStorageInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{46}
}

// 获取商品账号密码
type UserProductItem struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId      uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	ProductId    uint32 `protobuf:"varint,3,req,name=product_id,json=productId" json:"product_id"`
	ExchangeType uint32 `protobuf:"varint,4,opt,name=exchange_type,json=exchangeType" json:"exchange_type"`
	ExchangeInfo string `protobuf:"bytes,5,opt,name=exchange_info,json=exchangeInfo" json:"exchange_info"`
	FetchDate    uint32 `protobuf:"varint,6,opt,name=fetch_date,json=fetchDate" json:"fetch_date"`
	ItemId       uint32 `protobuf:"varint,7,opt,name=item_id,json=itemId" json:"item_id"`
}

func (m *UserProductItem) Reset()                    { *m = UserProductItem{} }
func (m *UserProductItem) String() string            { return proto.CompactTextString(m) }
func (*UserProductItem) ProtoMessage()               {}
func (*UserProductItem) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{47} }

func (m *UserProductItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserProductItem) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UserProductItem) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *UserProductItem) GetExchangeType() uint32 {
	if m != nil {
		return m.ExchangeType
	}
	return 0
}

func (m *UserProductItem) GetExchangeInfo() string {
	if m != nil {
		return m.ExchangeInfo
	}
	return ""
}

func (m *UserProductItem) GetFetchDate() uint32 {
	if m != nil {
		return m.FetchDate
	}
	return 0
}

func (m *UserProductItem) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type GetUserProductItemReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId   uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	ProductId uint32 `protobuf:"varint,3,req,name=product_id,json=productId" json:"product_id"`
}

func (m *GetUserProductItemReq) Reset()         { *m = GetUserProductItemReq{} }
func (m *GetUserProductItemReq) String() string { return proto.CompactTextString(m) }
func (*GetUserProductItemReq) ProtoMessage()    {}
func (*GetUserProductItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{48}
}

func (m *GetUserProductItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserProductItemReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetUserProductItemReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

type GetUserProductItemResp struct {
	ProductItemLst []*UserProductItem `protobuf:"bytes,1,rep,name=product_item_lst,json=productItemLst" json:"product_item_lst,omitempty"`
}

func (m *GetUserProductItemResp) Reset()         { *m = GetUserProductItemResp{} }
func (m *GetUserProductItemResp) String() string { return proto.CompactTextString(m) }
func (*GetUserProductItemResp) ProtoMessage()    {}
func (*GetUserProductItemResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{49}
}

func (m *GetUserProductItemResp) GetProductItemLst() []*UserProductItem {
	if m != nil {
		return m.ProductItemLst
	}
	return nil
}

type BatGetGameProductCountReq struct {
	GuildIdList []uint32 `protobuf:"varint,1,rep,name=guild_id_list,json=guildIdList" json:"guild_id_list,omitempty"`
}

func (m *BatGetGameProductCountReq) Reset()         { *m = BatGetGameProductCountReq{} }
func (m *BatGetGameProductCountReq) String() string { return proto.CompactTextString(m) }
func (*BatGetGameProductCountReq) ProtoMessage()    {}
func (*BatGetGameProductCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{50}
}

func (m *BatGetGameProductCountReq) GetGuildIdList() []uint32 {
	if m != nil {
		return m.GuildIdList
	}
	return nil
}

type GuildProductCount struct {
	GuildId      uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	ProductCount uint32 `protobuf:"varint,2,req,name=product_count,json=productCount" json:"product_count"`
}

func (m *GuildProductCount) Reset()                    { *m = GuildProductCount{} }
func (m *GuildProductCount) String() string            { return proto.CompactTextString(m) }
func (*GuildProductCount) ProtoMessage()               {}
func (*GuildProductCount) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{51} }

func (m *GuildProductCount) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildProductCount) GetProductCount() uint32 {
	if m != nil {
		return m.ProductCount
	}
	return 0
}

type BatGetGameProductCountResp struct {
	ProductCountList []*GuildProductCount `protobuf:"bytes,1,rep,name=product_count_list,json=productCountList" json:"product_count_list,omitempty"`
}

func (m *BatGetGameProductCountResp) Reset()         { *m = BatGetGameProductCountResp{} }
func (m *BatGetGameProductCountResp) String() string { return proto.CompactTextString(m) }
func (*BatGetGameProductCountResp) ProtoMessage()    {}
func (*BatGetGameProductCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{52}
}

func (m *BatGetGameProductCountResp) GetProductCountList() []*GuildProductCount {
	if m != nil {
		return m.ProductCountList
	}
	return nil
}

// //////////////
// 公会金券相关//
// //////////////
type GuildGiftCardInfo struct {
	LyTicketId  uint32 `protobuf:"varint,1,req,name=ly_ticket_id,json=lyTicketId" json:"ly_ticket_id"`
	GuildId     uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	GameId      uint32 `protobuf:"varint,3,req,name=game_id,json=gameId" json:"game_id"`
	TotalPrice  uint32 `protobuf:"varint,4,req,name=total_price,json=totalPrice" json:"total_price"`
	UsedPrice   uint32 `protobuf:"varint,5,opt,name=used_price,json=usedPrice" json:"used_price"`
	ExpiryDate  uint32 `protobuf:"varint,6,opt,name=expiry_date,json=expiryDate" json:"expiry_date"`
	ActType     uint32 `protobuf:"varint,7,opt,name=act_type,json=actType" json:"act_type"`
	ActId       uint32 `protobuf:"varint,8,opt,name=act_id,json=actId" json:"act_id"`
	LowerBound  uint32 `protobuf:"varint,9,opt,name=lower_bound,json=lowerBound" json:"lower_bound"`
	UpperBound  uint32 `protobuf:"varint,10,opt,name=upper_bound,json=upperBound" json:"upper_bound"`
	CreateDate  uint32 `protobuf:"varint,11,opt,name=create_date,json=createDate" json:"create_date"`
	Balance     uint32 `protobuf:"varint,12,opt,name=balance" json:"balance"`
	MinRecharge uint32 `protobuf:"varint,13,opt,name=min_recharge,json=minRecharge" json:"min_recharge"`
	LyGameId    uint32 `protobuf:"varint,14,opt,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	Status      uint32 `protobuf:"varint,15,opt,name=status" json:"status"`
	Repeat      uint32 `protobuf:"varint,16,opt,name=repeat" json:"repeat"`
	Platform    uint32 `protobuf:"varint,17,opt,name=platform" json:"platform"`
}

func (m *GuildGiftCardInfo) Reset()                    { *m = GuildGiftCardInfo{} }
func (m *GuildGiftCardInfo) String() string            { return proto.CompactTextString(m) }
func (*GuildGiftCardInfo) ProtoMessage()               {}
func (*GuildGiftCardInfo) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{53} }

func (m *GuildGiftCardInfo) GetLyTicketId() uint32 {
	if m != nil {
		return m.LyTicketId
	}
	return 0
}

func (m *GuildGiftCardInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildGiftCardInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GuildGiftCardInfo) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *GuildGiftCardInfo) GetUsedPrice() uint32 {
	if m != nil {
		return m.UsedPrice
	}
	return 0
}

func (m *GuildGiftCardInfo) GetExpiryDate() uint32 {
	if m != nil {
		return m.ExpiryDate
	}
	return 0
}

func (m *GuildGiftCardInfo) GetActType() uint32 {
	if m != nil {
		return m.ActType
	}
	return 0
}

func (m *GuildGiftCardInfo) GetActId() uint32 {
	if m != nil {
		return m.ActId
	}
	return 0
}

func (m *GuildGiftCardInfo) GetLowerBound() uint32 {
	if m != nil {
		return m.LowerBound
	}
	return 0
}

func (m *GuildGiftCardInfo) GetUpperBound() uint32 {
	if m != nil {
		return m.UpperBound
	}
	return 0
}

func (m *GuildGiftCardInfo) GetCreateDate() uint32 {
	if m != nil {
		return m.CreateDate
	}
	return 0
}

func (m *GuildGiftCardInfo) GetBalance() uint32 {
	if m != nil {
		return m.Balance
	}
	return 0
}

func (m *GuildGiftCardInfo) GetMinRecharge() uint32 {
	if m != nil {
		return m.MinRecharge
	}
	return 0
}

func (m *GuildGiftCardInfo) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *GuildGiftCardInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GuildGiftCardInfo) GetRepeat() uint32 {
	if m != nil {
		return m.Repeat
	}
	return 0
}

func (m *GuildGiftCardInfo) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

// 获取公会金券
type GetGuildGiftCardReq struct {
	GuildId    uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	LyTicketId uint32 `protobuf:"varint,2,opt,name=ly_ticket_id,json=lyTicketId" json:"ly_ticket_id"`
	NeedDelete uint32 `protobuf:"varint,3,opt,name=need_delete,json=needDelete" json:"need_delete"`
}

func (m *GetGuildGiftCardReq) Reset()                    { *m = GetGuildGiftCardReq{} }
func (m *GetGuildGiftCardReq) String() string            { return proto.CompactTextString(m) }
func (*GetGuildGiftCardReq) ProtoMessage()               {}
func (*GetGuildGiftCardReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{54} }

func (m *GetGuildGiftCardReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildGiftCardReq) GetLyTicketId() uint32 {
	if m != nil {
		return m.LyTicketId
	}
	return 0
}

func (m *GetGuildGiftCardReq) GetNeedDelete() uint32 {
	if m != nil {
		return m.NeedDelete
	}
	return 0
}

type GetGuildGiftCardResp struct {
	TicketLst []*GuildGiftCardInfo `protobuf:"bytes,1,rep,name=ticket_lst,json=ticketLst" json:"ticket_lst,omitempty"`
}

func (m *GetGuildGiftCardResp) Reset()         { *m = GetGuildGiftCardResp{} }
func (m *GetGuildGiftCardResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildGiftCardResp) ProtoMessage()    {}
func (*GetGuildGiftCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{55}
}

func (m *GetGuildGiftCardResp) GetTicketLst() []*GuildGiftCardInfo {
	if m != nil {
		return m.TicketLst
	}
	return nil
}

// 增加公会金券
type AddGuildGiftCardReq struct {
	Ticket *GuildGiftCardInfo `protobuf:"bytes,1,req,name=ticket" json:"ticket,omitempty"`
}

func (m *AddGuildGiftCardReq) Reset()                    { *m = AddGuildGiftCardReq{} }
func (m *AddGuildGiftCardReq) String() string            { return proto.CompactTextString(m) }
func (*AddGuildGiftCardReq) ProtoMessage()               {}
func (*AddGuildGiftCardReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{56} }

func (m *AddGuildGiftCardReq) GetTicket() *GuildGiftCardInfo {
	if m != nil {
		return m.Ticket
	}
	return nil
}

type AddGuildGiftCardResp struct {
}

func (m *AddGuildGiftCardResp) Reset()         { *m = AddGuildGiftCardResp{} }
func (m *AddGuildGiftCardResp) String() string { return proto.CompactTextString(m) }
func (*AddGuildGiftCardResp) ProtoMessage()    {}
func (*AddGuildGiftCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{57}
}

// 公会金券拆分成商品
type SplitGuildGiftCardReq struct {
	GuildId    uint32        `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	LyTicketId uint32        `protobuf:"varint,2,req,name=ly_ticket_id,json=lyTicketId" json:"ly_ticket_id"`
	Value      uint32        `protobuf:"varint,3,req,name=value" json:"value"`
	GameId     uint32        `protobuf:"varint,4,opt,name=game_id,json=gameId" json:"game_id"`
	Num        uint32        `protobuf:"varint,5,req,name=num" json:"num"`
	Price      uint32        `protobuf:"varint,6,req,name=price" json:"price"`
	Examine    uint32        `protobuf:"varint,7,req,name=examine" json:"examine"`
	Product    *GuildProduct `protobuf:"bytes,8,opt,name=product" json:"product,omitempty"`
}

func (m *SplitGuildGiftCardReq) Reset()         { *m = SplitGuildGiftCardReq{} }
func (m *SplitGuildGiftCardReq) String() string { return proto.CompactTextString(m) }
func (*SplitGuildGiftCardReq) ProtoMessage()    {}
func (*SplitGuildGiftCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{58}
}

func (m *SplitGuildGiftCardReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SplitGuildGiftCardReq) GetLyTicketId() uint32 {
	if m != nil {
		return m.LyTicketId
	}
	return 0
}

func (m *SplitGuildGiftCardReq) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *SplitGuildGiftCardReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SplitGuildGiftCardReq) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *SplitGuildGiftCardReq) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *SplitGuildGiftCardReq) GetExamine() uint32 {
	if m != nil {
		return m.Examine
	}
	return 0
}

func (m *SplitGuildGiftCardReq) GetProduct() *GuildProduct {
	if m != nil {
		return m.Product
	}
	return nil
}

type SplitGuildGiftCardResp struct {
	Product *GuildProduct `protobuf:"bytes,1,req,name=product" json:"product,omitempty"`
}

func (m *SplitGuildGiftCardResp) Reset()         { *m = SplitGuildGiftCardResp{} }
func (m *SplitGuildGiftCardResp) String() string { return proto.CompactTextString(m) }
func (*SplitGuildGiftCardResp) ProtoMessage()    {}
func (*SplitGuildGiftCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{59}
}

func (m *SplitGuildGiftCardResp) GetProduct() *GuildProduct {
	if m != nil {
		return m.Product
	}
	return nil
}

// 异步发放代金券
type GrantVoucherData struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId    uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	LyTicketId uint32 `protobuf:"varint,3,req,name=ly_ticket_id,json=lyTicketId" json:"ly_ticket_id"`
	Value      uint32 `protobuf:"varint,4,req,name=value" json:"value"`
	LyGameId   uint32 `protobuf:"varint,5,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	OrderId    string `protobuf:"bytes,6,req,name=order_id,json=orderId" json:"order_id"`
	Msg        string `protobuf:"bytes,7,opt,name=msg" json:"msg"`
}

func (m *GrantVoucherData) Reset()                    { *m = GrantVoucherData{} }
func (m *GrantVoucherData) String() string            { return proto.CompactTextString(m) }
func (*GrantVoucherData) ProtoMessage()               {}
func (*GrantVoucherData) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{60} }

func (m *GrantVoucherData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GrantVoucherData) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GrantVoucherData) GetLyTicketId() uint32 {
	if m != nil {
		return m.LyTicketId
	}
	return 0
}

func (m *GrantVoucherData) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *GrantVoucherData) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *GrantVoucherData) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *GrantVoucherData) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

// 获取giftCard消费记录
type GetGiftCardLogInfoReq struct {
	GuildId    uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	LyTicketId uint32 `protobuf:"varint,2,req,name=ly_ticket_id,json=lyTicketId" json:"ly_ticket_id"`
	OrderId    string `protobuf:"bytes,3,opt,name=order_id,json=orderId" json:"order_id"`
	Status     uint32 `protobuf:"varint,4,opt,name=status" json:"status"`
	Uid        uint32 `protobuf:"varint,5,opt,name=uid" json:"uid"`
	Index      uint32 `protobuf:"varint,6,opt,name=index" json:"index"`
	Cnt        uint32 `protobuf:"varint,7,opt,name=cnt" json:"cnt"`
}

func (m *GetGiftCardLogInfoReq) Reset()         { *m = GetGiftCardLogInfoReq{} }
func (m *GetGiftCardLogInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetGiftCardLogInfoReq) ProtoMessage()    {}
func (*GetGiftCardLogInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{61}
}

func (m *GetGiftCardLogInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGiftCardLogInfoReq) GetLyTicketId() uint32 {
	if m != nil {
		return m.LyTicketId
	}
	return 0
}

func (m *GetGiftCardLogInfoReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *GetGiftCardLogInfoReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetGiftCardLogInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGiftCardLogInfoReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *GetGiftCardLogInfoReq) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type GetGiftCardLogInfo struct {
	OrderInfo  *GrantVoucherData `protobuf:"bytes,1,req,name=order_info,json=orderInfo" json:"order_info,omitempty"`
	Status     uint32            `protobuf:"varint,2,req,name=status" json:"status"`
	CreateDate uint32            `protobuf:"varint,3,opt,name=create_date,json=createDate" json:"create_date"`
}

func (m *GetGiftCardLogInfo) Reset()                    { *m = GetGiftCardLogInfo{} }
func (m *GetGiftCardLogInfo) String() string            { return proto.CompactTextString(m) }
func (*GetGiftCardLogInfo) ProtoMessage()               {}
func (*GetGiftCardLogInfo) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{62} }

func (m *GetGiftCardLogInfo) GetOrderInfo() *GrantVoucherData {
	if m != nil {
		return m.OrderInfo
	}
	return nil
}

func (m *GetGiftCardLogInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetGiftCardLogInfo) GetCreateDate() uint32 {
	if m != nil {
		return m.CreateDate
	}
	return 0
}

type GetGiftCardLogInfoResp struct {
	LogTotal uint32                `protobuf:"varint,1,req,name=log_total,json=logTotal" json:"log_total"`
	LogLst   []*GetGiftCardLogInfo `protobuf:"bytes,2,rep,name=log_lst,json=logLst" json:"log_lst,omitempty"`
}

func (m *GetGiftCardLogInfoResp) Reset()         { *m = GetGiftCardLogInfoResp{} }
func (m *GetGiftCardLogInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetGiftCardLogInfoResp) ProtoMessage()    {}
func (*GetGiftCardLogInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{63}
}

func (m *GetGiftCardLogInfoResp) GetLogTotal() uint32 {
	if m != nil {
		return m.LogTotal
	}
	return 0
}

func (m *GetGiftCardLogInfoResp) GetLogLst() []*GetGiftCardLogInfo {
	if m != nil {
		return m.LogLst
	}
	return nil
}

// 修改giftCardLog
type SetGiftCardLogStatusReq struct {
	OrderId string `protobuf:"bytes,1,req,name=order_id,json=orderId" json:"order_id"`
	GuildId uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	Status  uint32 `protobuf:"varint,3,req,name=status" json:"status"`
	RespMsg string `protobuf:"bytes,4,opt,name=resp_msg,json=respMsg" json:"resp_msg"`
}

func (m *SetGiftCardLogStatusReq) Reset()         { *m = SetGiftCardLogStatusReq{} }
func (m *SetGiftCardLogStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetGiftCardLogStatusReq) ProtoMessage()    {}
func (*SetGiftCardLogStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{64}
}

func (m *SetGiftCardLogStatusReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *SetGiftCardLogStatusReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SetGiftCardLogStatusReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *SetGiftCardLogStatusReq) GetRespMsg() string {
	if m != nil {
		return m.RespMsg
	}
	return ""
}

type SetGiftCardLogStatusResp struct {
}

func (m *SetGiftCardLogStatusResp) Reset()         { *m = SetGiftCardLogStatusResp{} }
func (m *SetGiftCardLogStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetGiftCardLogStatusResp) ProtoMessage()    {}
func (*SetGiftCardLogStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildstorage, []int{65}
}

// 删除代金券
type DeleteGiftCardReq struct {
	GuildId    uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	LyTicketId uint32 `protobuf:"varint,2,req,name=ly_ticket_id,json=lyTicketId" json:"ly_ticket_id"`
}

func (m *DeleteGiftCardReq) Reset()                    { *m = DeleteGiftCardReq{} }
func (m *DeleteGiftCardReq) String() string            { return proto.CompactTextString(m) }
func (*DeleteGiftCardReq) ProtoMessage()               {}
func (*DeleteGiftCardReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{66} }

func (m *DeleteGiftCardReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *DeleteGiftCardReq) GetLyTicketId() uint32 {
	if m != nil {
		return m.LyTicketId
	}
	return 0
}

type DeleteGiftCardResp struct {
}

func (m *DeleteGiftCardResp) Reset()                    { *m = DeleteGiftCardResp{} }
func (m *DeleteGiftCardResp) String() string            { return proto.CompactTextString(m) }
func (*DeleteGiftCardResp) ProtoMessage()               {}
func (*DeleteGiftCardResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildstorage, []int{67} }

func init() {
	proto.RegisterType((*MinGuildProduct)(nil), "GuildStorage.MinGuildProduct")
	proto.RegisterType((*GuildProduct)(nil), "GuildStorage.GuildProduct")
	proto.RegisterType((*ProductItem)(nil), "GuildStorage.ProductItem")
	proto.RegisterType((*AddProductReq)(nil), "GuildStorage.AddProductReq")
	proto.RegisterType((*AddProductResp)(nil), "GuildStorage.AddProductResp")
	proto.RegisterType((*GetProductReq)(nil), "GuildStorage.GetProductReq")
	proto.RegisterType((*GetProductResp)(nil), "GuildStorage.GetProductResp")
	proto.RegisterType((*BatchGetProductByIdReq)(nil), "GuildStorage.BatchGetProductByIdReq")
	proto.RegisterType((*BatchGetProductByIdResp)(nil), "GuildStorage.BatchGetProductByIdResp")
	proto.RegisterType((*SearchProductReq)(nil), "GuildStorage.SearchProductReq")
	proto.RegisterType((*SearchProductResp)(nil), "GuildStorage.SearchProductResp")
	proto.RegisterType((*ExamineRecord)(nil), "GuildStorage.ExamineRecord")
	proto.RegisterType((*GetExamineRecordLstReq)(nil), "GuildStorage.GetExamineRecordLstReq")
	proto.RegisterType((*GetExamineRecordLstResp)(nil), "GuildStorage.GetExamineRecordLstResp")
	proto.RegisterType((*DealExamineReq)(nil), "GuildStorage.DealExamineReq")
	proto.RegisterType((*DealExamineResp)(nil), "GuildStorage.DealExamineResp")
	proto.RegisterType((*ModifyExamineApplyReq)(nil), "GuildStorage.ModifyExamineApplyReq")
	proto.RegisterType((*ModifyExamineApplyResp)(nil), "GuildStorage.ModifyExamineApplyResp")
	proto.RegisterType((*StorageOperRecord)(nil), "GuildStorage.StorageOperRecord")
	proto.RegisterType((*StorageGetOperRecordReq)(nil), "GuildStorage.StorageGetOperRecordReq")
	proto.RegisterType((*StorageGetOperRecordResp)(nil), "GuildStorage.StorageGetOperRecordResp")
	proto.RegisterType((*GainProductReq)(nil), "GuildStorage.GainProductReq")
	proto.RegisterType((*GainProductResp)(nil), "GuildStorage.GainProductResp")
	proto.RegisterType((*AllotProductReq)(nil), "GuildStorage.AllotProductReq")
	proto.RegisterType((*AllotProductResp)(nil), "GuildStorage.AllotProductResp")
	proto.RegisterType((*ModifyProductStatusReq)(nil), "GuildStorage.ModifyProductStatusReq")
	proto.RegisterType((*ModifyProductStatusResp)(nil), "GuildStorage.ModifyProductStatusResp")
	proto.RegisterType((*GetProductBySourceIdReq)(nil), "GuildStorage.GetProductBySourceIdReq")
	proto.RegisterType((*GetProductBySourceIdResp)(nil), "GuildStorage.GetProductBySourceIdResp")
	proto.RegisterType((*GetGameProductReq)(nil), "GuildStorage.GetGameProductReq")
	proto.RegisterType((*GameProduct)(nil), "GuildStorage.GameProduct")
	proto.RegisterType((*GetGameProductResp)(nil), "GuildStorage.GetGameProductResp")
	proto.RegisterType((*DeleteProductReq)(nil), "GuildStorage.DeleteProductReq")
	proto.RegisterType((*DeleteProductResp)(nil), "GuildStorage.DeleteProductResp")
	proto.RegisterType((*GetFetchRecordReq)(nil), "GuildStorage.GetFetchRecordReq")
	proto.RegisterType((*UserFetchRecord)(nil), "GuildStorage.UserFetchRecord")
	proto.RegisterType((*GetFetchRecordResp)(nil), "GuildStorage.GetFetchRecordResp")
	proto.RegisterType((*BatchGetUserFetchRecordReq)(nil), "GuildStorage.BatchGetUserFetchRecordReq")
	proto.RegisterType((*BatchGetUserFetchRecordResp)(nil), "GuildStorage.BatchGetUserFetchRecordResp")
	proto.RegisterType((*CalculateProductReq)(nil), "GuildStorage.CalculateProductReq")
	proto.RegisterType((*CalculateProductItem)(nil), "GuildStorage.CalculateProductItem")
	proto.RegisterType((*CalculateProductResp)(nil), "GuildStorage.CalculateProductResp")
	proto.RegisterType((*GetGuildBySourceIdReq)(nil), "GuildStorage.GetGuildBySourceIdReq")
	proto.RegisterType((*GetGuildBySourceIdResp)(nil), "GuildStorage.GetGuildBySourceIdResp")
	proto.RegisterType((*UserStorageInfo)(nil), "GuildStorage.UserStorageInfo")
	proto.RegisterType((*GetUserStorageInfoReq)(nil), "GuildStorage.GetUserStorageInfoReq")
	proto.RegisterType((*GetUserStorageInfoResp)(nil), "GuildStorage.GetUserStorageInfoResp")
	proto.RegisterType((*UserProductItem)(nil), "GuildStorage.UserProductItem")
	proto.RegisterType((*GetUserProductItemReq)(nil), "GuildStorage.GetUserProductItemReq")
	proto.RegisterType((*GetUserProductItemResp)(nil), "GuildStorage.GetUserProductItemResp")
	proto.RegisterType((*BatGetGameProductCountReq)(nil), "GuildStorage.BatGetGameProductCountReq")
	proto.RegisterType((*GuildProductCount)(nil), "GuildStorage.GuildProductCount")
	proto.RegisterType((*BatGetGameProductCountResp)(nil), "GuildStorage.BatGetGameProductCountResp")
	proto.RegisterType((*GuildGiftCardInfo)(nil), "GuildStorage.GuildGiftCardInfo")
	proto.RegisterType((*GetGuildGiftCardReq)(nil), "GuildStorage.GetGuildGiftCardReq")
	proto.RegisterType((*GetGuildGiftCardResp)(nil), "GuildStorage.GetGuildGiftCardResp")
	proto.RegisterType((*AddGuildGiftCardReq)(nil), "GuildStorage.AddGuildGiftCardReq")
	proto.RegisterType((*AddGuildGiftCardResp)(nil), "GuildStorage.AddGuildGiftCardResp")
	proto.RegisterType((*SplitGuildGiftCardReq)(nil), "GuildStorage.SplitGuildGiftCardReq")
	proto.RegisterType((*SplitGuildGiftCardResp)(nil), "GuildStorage.SplitGuildGiftCardResp")
	proto.RegisterType((*GrantVoucherData)(nil), "GuildStorage.GrantVoucherData")
	proto.RegisterType((*GetGiftCardLogInfoReq)(nil), "GuildStorage.GetGiftCardLogInfoReq")
	proto.RegisterType((*GetGiftCardLogInfo)(nil), "GuildStorage.GetGiftCardLogInfo")
	proto.RegisterType((*GetGiftCardLogInfoResp)(nil), "GuildStorage.GetGiftCardLogInfoResp")
	proto.RegisterType((*SetGiftCardLogStatusReq)(nil), "GuildStorage.SetGiftCardLogStatusReq")
	proto.RegisterType((*SetGiftCardLogStatusResp)(nil), "GuildStorage.SetGiftCardLogStatusResp")
	proto.RegisterType((*DeleteGiftCardReq)(nil), "GuildStorage.DeleteGiftCardReq")
	proto.RegisterType((*DeleteGiftCardResp)(nil), "GuildStorage.DeleteGiftCardResp")
	proto.RegisterEnum("GuildStorage.EGiftCardAsyncType", EGiftCardAsyncType_name, EGiftCardAsyncType_value)
	proto.RegisterEnum("GuildStorage.EGrantVoucherStatus", EGrantVoucherStatus_name, EGrantVoucherStatus_value)
	proto.RegisterEnum("GuildStorage.StorageOperRecord_CATEGORY_TYPE", StorageOperRecord_CATEGORY_TYPE_name, StorageOperRecord_CATEGORY_TYPE_value)
	proto.RegisterEnum("GuildStorage.StorageOperRecord_GUILD_MEM_OPER", StorageOperRecord_GUILD_MEM_OPER_name, StorageOperRecord_GUILD_MEM_OPER_value)
	proto.RegisterEnum("GuildStorage.ModifyProductStatusReq_ModifyProductOperType", ModifyProductStatusReq_ModifyProductOperType_name, ModifyProductStatusReq_ModifyProductOperType_value)
}
func (m *MinGuildProduct) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MinGuildProduct) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.SourceType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.SourceId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ItemType))
	dAtA[i] = 0x32
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(len(m.Usage)))
	i += copy(dAtA[i:], m.Usage)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(len(m.Description)))
	i += copy(dAtA[i:], m.Description)
	dAtA[i] = 0x40
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x48
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x52
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(len(m.IconUrl)))
	i += copy(dAtA[i:], m.IconUrl)
	dAtA[i] = 0x58
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x60
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ExchangeType))
	dAtA[i] = 0x68
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Platform))
	return i, nil
}

func (m *GuildProduct) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildProduct) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.MinProduct == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("min_product")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildstorage(dAtA, i, uint64(m.MinProduct.Size()))
		n1, err := m.MinProduct.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.CurrencyType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Price))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Remain))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Total))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ValidSDate))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ValidEDate))
	dAtA[i] = 0x40
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Examine))
	dAtA[i] = 0x48
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.FetchLimit))
	dAtA[i] = 0x50
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.FetchTimes))
	dAtA[i] = 0x58
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.UpdateTs))
	dAtA[i] = 0x62
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(len(m.ExtendInfo)))
	i += copy(dAtA[i:], m.ExtendInfo)
	dAtA[i] = 0x68
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.DayLimit))
	return i, nil
}

func (m *ProductItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ProductItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ExchangeType))
	if m.ExchangeInfo != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintGuildstorage(dAtA, i, uint64(len(m.ExchangeInfo)))
		i += copy(dAtA[i:], m.ExchangeInfo)
	}
	dAtA[i] = 0x38
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Date))
	return i, nil
}

func (m *AddProductReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddProductReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Uid))
	if m.Product == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("product")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGuildstorage(dAtA, i, uint64(m.Product.Size()))
		n2, err := m.Product.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if len(m.ItemBinary) > 0 {
		for _, b := range m.ItemBinary {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(len(b)))
			i += copy(dAtA[i:], b)
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Payment))
	return i, nil
}

func (m *AddProductResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddProductResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ProductId))
	return i, nil
}

func (m *GetProductReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetProductReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.SourceType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ItemType))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ProductStatus))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.NeedUnshelve))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Index))
	dAtA[i] = 0x40
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Cnt))
	dAtA[i] = 0x48
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Platform))
	return i, nil
}

func (m *GetProductResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetProductResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ProductLst) > 0 {
		for _, msg := range m.ProductLst {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *BatchGetProductByIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetProductByIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	if len(m.ProductIdLst) > 0 {
		for _, num := range m.ProductIdLst {
			dAtA[i] = 0x10
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Platform))
	return i, nil
}

func (m *BatchGetProductByIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetProductByIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ProductLst) > 0 {
		for _, msg := range m.ProductLst {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SearchProductReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SearchProductReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(len(m.KeyWord)))
	i += copy(dAtA[i:], m.KeyWord)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Index))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Cnt))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.NeedUnshelve))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Platform))
	return i, nil
}

func (m *SearchProductResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SearchProductResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Total))
	if len(m.ProductLst) > 0 {
		for _, msg := range m.ProductLst {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ExamineRecord) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExamineRecord) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ExamineId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Date))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Num))
	if m.Product == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("product")
	} else {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintGuildstorage(dAtA, i, uint64(m.Product.Size()))
		n3, err := m.Product.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x40
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildMsgId))
	return i, nil
}

func (m *GetExamineRecordLstReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetExamineRecordLstReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ToUid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ExamineId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ExamineStatus))
	return i, nil
}

func (m *GetExamineRecordLstResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetExamineRecordLstResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RecordLst) > 0 {
		for _, msg := range m.RecordLst {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *DealExamineReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DealExamineReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ExamineId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *DealExamineResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DealExamineResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ToUid))
	if m.Product == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("product")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGuildstorage(dAtA, i, uint64(m.Product.Size()))
		n4, err := m.Product.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	if m.Item != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintGuildstorage(dAtA, i, uint64(m.Item.Size()))
		n5, err := m.Item.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildMsgId))
	return i, nil
}

func (m *ModifyExamineApplyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyExamineApplyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ExamineId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildMsgId))
	return i, nil
}

func (m *ModifyExamineApplyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyExamineApplyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ExamineId))
	return i, nil
}

func (m *StorageOperRecord) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StorageOperRecord) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.RecordId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.OperType))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Date))
	if m.Product != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintGuildstorage(dAtA, i, uint64(m.Product.Size()))
		n6, err := m.Product.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x38
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Payment))
	dAtA[i] = 0x40
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ToUid))
	dAtA[i] = 0x4a
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(len(m.ExtendInfo)))
	i += copy(dAtA[i:], m.ExtendInfo)
	dAtA[i] = 0x50
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Category))
	return i, nil
}

func (m *StorageGetOperRecordReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StorageGetOperRecordReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Index))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Cnt))
	return i, nil
}

func (m *StorageGetOperRecordResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StorageGetOperRecordResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.TotalRecord))
	if len(m.RecordLst) > 0 {
		for _, msg := range m.RecordLst {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GainProductReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GainProductReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.CurrencyType))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Price))
	return i, nil
}

func (m *GainProductResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GainProductResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Status))
	if m.Item != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGuildstorage(dAtA, i, uint64(m.Item.Size()))
		n7, err := m.Item.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if m.Product != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintGuildstorage(dAtA, i, uint64(m.Product.Size()))
		n8, err := m.Product.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ExamineId))
	return i, nil
}

func (m *AllotProductReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AllotProductReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ToUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Num))
	return i, nil
}

func (m *AllotProductResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AllotProductResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ToUid))
	if m.Product != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGuildstorage(dAtA, i, uint64(m.Product.Size()))
		n9, err := m.Product.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	if len(m.ItemLst) > 0 {
		for _, msg := range m.ItemLst {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *ModifyProductStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyProductStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.OperType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Num))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Price))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Examine))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.FetchLimit))
	dAtA[i] = 0x40
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.DayLimit))
	return i, nil
}

func (m *ModifyProductStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyProductStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetProductBySourceIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetProductBySourceIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.SourceType))
	if len(m.SourceIdLst) > 0 {
		for _, num := range m.SourceIdLst {
			dAtA[i] = 0x18
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetProductBySourceIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetProductBySourceIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ProductLst) > 0 {
		for _, msg := range m.ProductLst {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGameProductReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameProductReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.SourceType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ItemType))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.NeedUnshelve))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Platform))
	if len(m.InstallGameIdLst) > 0 {
		for _, num := range m.InstallGameIdLst {
			dAtA[i] = 0x38
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GameProduct) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameProduct) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GameId))
	if len(m.ProductLst) > 0 {
		for _, msg := range m.ProductLst {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGameProductResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameProductResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GameProductLst) > 0 {
		for _, msg := range m.GameProductLst {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *DeleteProductReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteProductReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Num))
	return i, nil
}

func (m *DeleteProductResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteProductResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetFetchRecordReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFetchRecordReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Index))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Cnt))
	return i, nil
}

func (m *UserFetchRecord) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserFetchRecord) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.RecordId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Date))
	return i, nil
}

func (m *GetFetchRecordResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFetchRecordResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Total))
	if len(m.RecordLst) > 0 {
		for _, msg := range m.RecordLst {
			dAtA[i] = 0x22
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchGetUserFetchRecordReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserFetchRecordReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	if len(m.ProductIdLst) > 0 {
		for _, num := range m.ProductIdLst {
			dAtA[i] = 0x18
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x20
	i++
	if m.OnlyTotal {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *BatchGetUserFetchRecordResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserFetchRecordResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RecordLst) > 0 {
		for _, msg := range m.RecordLst {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CalculateProductReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CalculateProductReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.SourceType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ItemType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.NeedUnshelve))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Platform))
	return i, nil
}

func (m *CalculateProductItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CalculateProductItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.SourceType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ItemType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *CalculateProductResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CalculateProductResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	if len(m.ItemLst) > 0 {
		for _, msg := range m.ItemLst {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGuildBySourceIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildBySourceIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.SourceId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.SourceType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ItemStatus))
	return i, nil
}

func (m *GetGuildBySourceIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildBySourceIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GuildIdLst) > 0 {
		for _, num := range m.GuildIdLst {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *UserStorageInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserStorageInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ExamineId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.FetchTimes))
	return i, nil
}

func (m *GetUserStorageInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserStorageInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ProductId))
	return i, nil
}

func (m *GetUserStorageInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserStorageInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *UserProductItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserProductItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ExchangeType))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(len(m.ExchangeInfo)))
	i += copy(dAtA[i:], m.ExchangeInfo)
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.FetchDate))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ItemId))
	return i, nil
}

func (m *GetUserProductItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserProductItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ProductId))
	return i, nil
}

func (m *GetUserProductItemResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserProductItemResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ProductItemLst) > 0 {
		for _, msg := range m.ProductItemLst {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatGetGameProductCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetGameProductCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GuildIdList) > 0 {
		for _, num := range m.GuildIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GuildProductCount) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildProductCount) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ProductCount))
	return i, nil
}

func (m *BatGetGameProductCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetGameProductCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ProductCountList) > 0 {
		for _, msg := range m.ProductCountList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GuildGiftCardInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildGiftCardInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.LyTicketId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.TotalPrice))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.UsedPrice))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ExpiryDate))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ActType))
	dAtA[i] = 0x40
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.ActId))
	dAtA[i] = 0x48
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.LowerBound))
	dAtA[i] = 0x50
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.UpperBound))
	dAtA[i] = 0x58
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.CreateDate))
	dAtA[i] = 0x60
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Balance))
	dAtA[i] = 0x68
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.MinRecharge))
	dAtA[i] = 0x70
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x78
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Repeat))
	dAtA[i] = 0x88
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Platform))
	return i, nil
}

func (m *GetGuildGiftCardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildGiftCardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.LyTicketId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.NeedDelete))
	return i, nil
}

func (m *GetGuildGiftCardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildGiftCardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TicketLst) > 0 {
		for _, msg := range m.TicketLst {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddGuildGiftCardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddGuildGiftCardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Ticket == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("ticket")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildstorage(dAtA, i, uint64(m.Ticket.Size()))
		n10, err := m.Ticket.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	return i, nil
}

func (m *AddGuildGiftCardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddGuildGiftCardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SplitGuildGiftCardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SplitGuildGiftCardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.LyTicketId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Value))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Num))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Price))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Examine))
	if m.Product != nil {
		dAtA[i] = 0x42
		i++
		i = encodeVarintGuildstorage(dAtA, i, uint64(m.Product.Size()))
		n11, err := m.Product.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	return i, nil
}

func (m *SplitGuildGiftCardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SplitGuildGiftCardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Product == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("product")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildstorage(dAtA, i, uint64(m.Product.Size()))
		n12, err := m.Product.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	return i, nil
}

func (m *GrantVoucherData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GrantVoucherData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.LyTicketId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Value))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x32
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(len(m.Msg)))
	i += copy(dAtA[i:], m.Msg)
	return i, nil
}

func (m *GetGiftCardLogInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftCardLogInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.LyTicketId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Index))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Cnt))
	return i, nil
}

func (m *GetGiftCardLogInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftCardLogInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.OrderInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("order_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildstorage(dAtA, i, uint64(m.OrderInfo.Size()))
		n13, err := m.OrderInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.CreateDate))
	return i, nil
}

func (m *GetGiftCardLogInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftCardLogInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.LogTotal))
	if len(m.LogLst) > 0 {
		for _, msg := range m.LogLst {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGuildstorage(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetGiftCardLogStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetGiftCardLogStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(len(m.RespMsg)))
	i += copy(dAtA[i:], m.RespMsg)
	return i, nil
}

func (m *SetGiftCardLogStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetGiftCardLogStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DeleteGiftCardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteGiftCardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildstorage(dAtA, i, uint64(m.LyTicketId))
	return i, nil
}

func (m *DeleteGiftCardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteGiftCardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Guildstorage(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Guildstorage(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintGuildstorage(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *MinGuildProduct) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.ProductId))
	l = len(m.Name)
	n += 1 + l + sovGuildstorage(uint64(l))
	n += 1 + sovGuildstorage(uint64(m.SourceType))
	n += 1 + sovGuildstorage(uint64(m.SourceId))
	n += 1 + sovGuildstorage(uint64(m.ItemType))
	l = len(m.Usage)
	n += 1 + l + sovGuildstorage(uint64(l))
	l = len(m.Description)
	n += 1 + l + sovGuildstorage(uint64(l))
	n += 1 + sovGuildstorage(uint64(m.Status))
	n += 1 + sovGuildstorage(uint64(m.GameId))
	l = len(m.IconUrl)
	n += 1 + l + sovGuildstorage(uint64(l))
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.ExchangeType))
	n += 1 + sovGuildstorage(uint64(m.Platform))
	return n
}

func (m *GuildProduct) Size() (n int) {
	var l int
	_ = l
	if m.MinProduct != nil {
		l = m.MinProduct.Size()
		n += 1 + l + sovGuildstorage(uint64(l))
	}
	n += 1 + sovGuildstorage(uint64(m.CurrencyType))
	n += 1 + sovGuildstorage(uint64(m.Price))
	n += 1 + sovGuildstorage(uint64(m.Remain))
	n += 1 + sovGuildstorage(uint64(m.Total))
	n += 1 + sovGuildstorage(uint64(m.ValidSDate))
	n += 1 + sovGuildstorage(uint64(m.ValidEDate))
	n += 1 + sovGuildstorage(uint64(m.Examine))
	n += 1 + sovGuildstorage(uint64(m.FetchLimit))
	n += 1 + sovGuildstorage(uint64(m.FetchTimes))
	n += 1 + sovGuildstorage(uint64(m.UpdateTs))
	l = len(m.ExtendInfo)
	n += 1 + l + sovGuildstorage(uint64(l))
	n += 1 + sovGuildstorage(uint64(m.DayLimit))
	return n
}

func (m *ProductItem) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.Id))
	n += 1 + sovGuildstorage(uint64(m.Uid))
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.ProductId))
	n += 1 + sovGuildstorage(uint64(m.ExchangeType))
	if m.ExchangeInfo != nil {
		l = len(m.ExchangeInfo)
		n += 1 + l + sovGuildstorage(uint64(l))
	}
	n += 1 + sovGuildstorage(uint64(m.Date))
	return n
}

func (m *AddProductReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.Uid))
	if m.Product != nil {
		l = m.Product.Size()
		n += 1 + l + sovGuildstorage(uint64(l))
	}
	if len(m.ItemBinary) > 0 {
		for _, b := range m.ItemBinary {
			l = len(b)
			n += 1 + l + sovGuildstorage(uint64(l))
		}
	}
	n += 1 + sovGuildstorage(uint64(m.Payment))
	return n
}

func (m *AddProductResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.ProductId))
	return n
}

func (m *GetProductReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.ProductId))
	n += 1 + sovGuildstorage(uint64(m.SourceType))
	n += 1 + sovGuildstorage(uint64(m.ItemType))
	n += 1 + sovGuildstorage(uint64(m.ProductStatus))
	n += 1 + sovGuildstorage(uint64(m.NeedUnshelve))
	n += 1 + sovGuildstorage(uint64(m.Index))
	n += 1 + sovGuildstorage(uint64(m.Cnt))
	n += 1 + sovGuildstorage(uint64(m.Platform))
	return n
}

func (m *GetProductResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ProductLst) > 0 {
		for _, e := range m.ProductLst {
			l = e.Size()
			n += 1 + l + sovGuildstorage(uint64(l))
		}
	}
	n += 1 + sovGuildstorage(uint64(m.Total))
	return n
}

func (m *BatchGetProductByIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	if len(m.ProductIdLst) > 0 {
		for _, e := range m.ProductIdLst {
			n += 1 + sovGuildstorage(uint64(e))
		}
	}
	n += 1 + sovGuildstorage(uint64(m.Platform))
	return n
}

func (m *BatchGetProductByIdResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ProductLst) > 0 {
		for _, e := range m.ProductLst {
			l = e.Size()
			n += 1 + l + sovGuildstorage(uint64(l))
		}
	}
	return n
}

func (m *SearchProductReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	l = len(m.KeyWord)
	n += 1 + l + sovGuildstorage(uint64(l))
	n += 1 + sovGuildstorage(uint64(m.Index))
	n += 1 + sovGuildstorage(uint64(m.Cnt))
	n += 1 + sovGuildstorage(uint64(m.NeedUnshelve))
	n += 1 + sovGuildstorage(uint64(m.Platform))
	return n
}

func (m *SearchProductResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.Total))
	if len(m.ProductLst) > 0 {
		for _, e := range m.ProductLst {
			l = e.Size()
			n += 1 + l + sovGuildstorage(uint64(l))
		}
	}
	return n
}

func (m *ExamineRecord) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.ExamineId))
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.Uid))
	n += 1 + sovGuildstorage(uint64(m.Date))
	n += 1 + sovGuildstorage(uint64(m.Status))
	n += 1 + sovGuildstorage(uint64(m.Num))
	if m.Product != nil {
		l = m.Product.Size()
		n += 1 + l + sovGuildstorage(uint64(l))
	}
	n += 1 + sovGuildstorage(uint64(m.GuildMsgId))
	return n
}

func (m *GetExamineRecordLstReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.ProductId))
	n += 1 + sovGuildstorage(uint64(m.ToUid))
	n += 1 + sovGuildstorage(uint64(m.ExamineId))
	n += 1 + sovGuildstorage(uint64(m.ExamineStatus))
	return n
}

func (m *GetExamineRecordLstResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RecordLst) > 0 {
		for _, e := range m.RecordLst {
			l = e.Size()
			n += 1 + l + sovGuildstorage(uint64(l))
		}
	}
	return n
}

func (m *DealExamineReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.ExamineId))
	n += 1 + sovGuildstorage(uint64(m.Status))
	return n
}

func (m *DealExamineResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.ToUid))
	if m.Product != nil {
		l = m.Product.Size()
		n += 1 + l + sovGuildstorage(uint64(l))
	}
	if m.Item != nil {
		l = m.Item.Size()
		n += 1 + l + sovGuildstorage(uint64(l))
	}
	n += 1 + sovGuildstorage(uint64(m.GuildMsgId))
	return n
}

func (m *ModifyExamineApplyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.ExamineId))
	n += 1 + sovGuildstorage(uint64(m.GuildMsgId))
	return n
}

func (m *ModifyExamineApplyResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.ExamineId))
	return n
}

func (m *StorageOperRecord) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.RecordId))
	n += 1 + sovGuildstorage(uint64(m.Uid))
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.OperType))
	n += 1 + sovGuildstorage(uint64(m.Date))
	if m.Product != nil {
		l = m.Product.Size()
		n += 1 + l + sovGuildstorage(uint64(l))
	}
	n += 1 + sovGuildstorage(uint64(m.Payment))
	n += 1 + sovGuildstorage(uint64(m.ToUid))
	l = len(m.ExtendInfo)
	n += 1 + l + sovGuildstorage(uint64(l))
	n += 1 + sovGuildstorage(uint64(m.Category))
	return n
}

func (m *StorageGetOperRecordReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.Index))
	n += 1 + sovGuildstorage(uint64(m.Cnt))
	return n
}

func (m *StorageGetOperRecordResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.TotalRecord))
	if len(m.RecordLst) > 0 {
		for _, e := range m.RecordLst {
			l = e.Size()
			n += 1 + l + sovGuildstorage(uint64(l))
		}
	}
	return n
}

func (m *GainProductReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.Uid))
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.ProductId))
	n += 1 + sovGuildstorage(uint64(m.CurrencyType))
	n += 1 + sovGuildstorage(uint64(m.Price))
	return n
}

func (m *GainProductResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.Status))
	if m.Item != nil {
		l = m.Item.Size()
		n += 1 + l + sovGuildstorage(uint64(l))
	}
	if m.Product != nil {
		l = m.Product.Size()
		n += 1 + l + sovGuildstorage(uint64(l))
	}
	n += 1 + sovGuildstorage(uint64(m.ExamineId))
	return n
}

func (m *AllotProductReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.ToUid))
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.ProductId))
	n += 1 + sovGuildstorage(uint64(m.Num))
	return n
}

func (m *AllotProductResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.ToUid))
	if m.Product != nil {
		l = m.Product.Size()
		n += 1 + l + sovGuildstorage(uint64(l))
	}
	if len(m.ItemLst) > 0 {
		for _, e := range m.ItemLst {
			l = e.Size()
			n += 1 + l + sovGuildstorage(uint64(l))
		}
	}
	n += 1 + sovGuildstorage(uint64(m.Total))
	return n
}

func (m *ModifyProductStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.ProductId))
	n += 1 + sovGuildstorage(uint64(m.OperType))
	n += 1 + sovGuildstorage(uint64(m.Num))
	n += 1 + sovGuildstorage(uint64(m.Price))
	n += 1 + sovGuildstorage(uint64(m.Examine))
	n += 1 + sovGuildstorage(uint64(m.FetchLimit))
	n += 1 + sovGuildstorage(uint64(m.DayLimit))
	return n
}

func (m *ModifyProductStatusResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetProductBySourceIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.SourceType))
	if len(m.SourceIdLst) > 0 {
		for _, e := range m.SourceIdLst {
			n += 1 + sovGuildstorage(uint64(e))
		}
	}
	return n
}

func (m *GetProductBySourceIdResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ProductLst) > 0 {
		for _, e := range m.ProductLst {
			l = e.Size()
			n += 1 + l + sovGuildstorage(uint64(l))
		}
	}
	return n
}

func (m *GetGameProductReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.GameId))
	n += 1 + sovGuildstorage(uint64(m.SourceType))
	n += 1 + sovGuildstorage(uint64(m.ItemType))
	n += 1 + sovGuildstorage(uint64(m.NeedUnshelve))
	n += 1 + sovGuildstorage(uint64(m.Platform))
	if len(m.InstallGameIdLst) > 0 {
		for _, e := range m.InstallGameIdLst {
			n += 1 + sovGuildstorage(uint64(e))
		}
	}
	return n
}

func (m *GameProduct) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GameId))
	if len(m.ProductLst) > 0 {
		for _, e := range m.ProductLst {
			l = e.Size()
			n += 1 + l + sovGuildstorage(uint64(l))
		}
	}
	return n
}

func (m *GetGameProductResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GameProductLst) > 0 {
		for _, e := range m.GameProductLst {
			l = e.Size()
			n += 1 + l + sovGuildstorage(uint64(l))
		}
	}
	n += 1 + sovGuildstorage(uint64(m.Total))
	return n
}

func (m *DeleteProductReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.ProductId))
	n += 1 + sovGuildstorage(uint64(m.Num))
	return n
}

func (m *DeleteProductResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetFetchRecordReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.ProductId))
	n += 1 + sovGuildstorage(uint64(m.Index))
	n += 1 + sovGuildstorage(uint64(m.Cnt))
	return n
}

func (m *UserFetchRecord) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.RecordId))
	n += 1 + sovGuildstorage(uint64(m.Uid))
	n += 1 + sovGuildstorage(uint64(m.Date))
	return n
}

func (m *GetFetchRecordResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.ProductId))
	n += 1 + sovGuildstorage(uint64(m.Total))
	if len(m.RecordLst) > 0 {
		for _, e := range m.RecordLst {
			l = e.Size()
			n += 1 + l + sovGuildstorage(uint64(l))
		}
	}
	return n
}

func (m *BatchGetUserFetchRecordReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.Uid))
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	if len(m.ProductIdLst) > 0 {
		for _, e := range m.ProductIdLst {
			n += 1 + sovGuildstorage(uint64(e))
		}
	}
	n += 2
	return n
}

func (m *BatchGetUserFetchRecordResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RecordLst) > 0 {
		for _, e := range m.RecordLst {
			l = e.Size()
			n += 1 + l + sovGuildstorage(uint64(l))
		}
	}
	return n
}

func (m *CalculateProductReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.SourceType))
	n += 1 + sovGuildstorage(uint64(m.ItemType))
	n += 1 + sovGuildstorage(uint64(m.NeedUnshelve))
	n += 1 + sovGuildstorage(uint64(m.Platform))
	return n
}

func (m *CalculateProductItem) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.SourceType))
	n += 1 + sovGuildstorage(uint64(m.ItemType))
	n += 1 + sovGuildstorage(uint64(m.Total))
	return n
}

func (m *CalculateProductResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	if len(m.ItemLst) > 0 {
		for _, e := range m.ItemLst {
			l = e.Size()
			n += 1 + l + sovGuildstorage(uint64(l))
		}
	}
	return n
}

func (m *GetGuildBySourceIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.SourceId))
	n += 1 + sovGuildstorage(uint64(m.SourceType))
	n += 1 + sovGuildstorage(uint64(m.ItemStatus))
	return n
}

func (m *GetGuildBySourceIdResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GuildIdLst) > 0 {
		for _, e := range m.GuildIdLst {
			n += 1 + sovGuildstorage(uint64(e))
		}
	}
	return n
}

func (m *UserStorageInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.Uid))
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.ProductId))
	n += 1 + sovGuildstorage(uint64(m.ExamineId))
	n += 1 + sovGuildstorage(uint64(m.FetchTimes))
	return n
}

func (m *GetUserStorageInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.Uid))
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.ProductId))
	return n
}

func (m *GetUserStorageInfoResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *UserProductItem) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.Uid))
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.ProductId))
	n += 1 + sovGuildstorage(uint64(m.ExchangeType))
	l = len(m.ExchangeInfo)
	n += 1 + l + sovGuildstorage(uint64(l))
	n += 1 + sovGuildstorage(uint64(m.FetchDate))
	n += 1 + sovGuildstorage(uint64(m.ItemId))
	return n
}

func (m *GetUserProductItemReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.Uid))
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.ProductId))
	return n
}

func (m *GetUserProductItemResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ProductItemLst) > 0 {
		for _, e := range m.ProductItemLst {
			l = e.Size()
			n += 1 + l + sovGuildstorage(uint64(l))
		}
	}
	return n
}

func (m *BatGetGameProductCountReq) Size() (n int) {
	var l int
	_ = l
	if len(m.GuildIdList) > 0 {
		for _, e := range m.GuildIdList {
			n += 1 + sovGuildstorage(uint64(e))
		}
	}
	return n
}

func (m *GuildProductCount) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.ProductCount))
	return n
}

func (m *BatGetGameProductCountResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ProductCountList) > 0 {
		for _, e := range m.ProductCountList {
			l = e.Size()
			n += 1 + l + sovGuildstorage(uint64(l))
		}
	}
	return n
}

func (m *GuildGiftCardInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.LyTicketId))
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.GameId))
	n += 1 + sovGuildstorage(uint64(m.TotalPrice))
	n += 1 + sovGuildstorage(uint64(m.UsedPrice))
	n += 1 + sovGuildstorage(uint64(m.ExpiryDate))
	n += 1 + sovGuildstorage(uint64(m.ActType))
	n += 1 + sovGuildstorage(uint64(m.ActId))
	n += 1 + sovGuildstorage(uint64(m.LowerBound))
	n += 1 + sovGuildstorage(uint64(m.UpperBound))
	n += 1 + sovGuildstorage(uint64(m.CreateDate))
	n += 1 + sovGuildstorage(uint64(m.Balance))
	n += 1 + sovGuildstorage(uint64(m.MinRecharge))
	n += 1 + sovGuildstorage(uint64(m.LyGameId))
	n += 1 + sovGuildstorage(uint64(m.Status))
	n += 2 + sovGuildstorage(uint64(m.Repeat))
	n += 2 + sovGuildstorage(uint64(m.Platform))
	return n
}

func (m *GetGuildGiftCardReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.LyTicketId))
	n += 1 + sovGuildstorage(uint64(m.NeedDelete))
	return n
}

func (m *GetGuildGiftCardResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TicketLst) > 0 {
		for _, e := range m.TicketLst {
			l = e.Size()
			n += 1 + l + sovGuildstorage(uint64(l))
		}
	}
	return n
}

func (m *AddGuildGiftCardReq) Size() (n int) {
	var l int
	_ = l
	if m.Ticket != nil {
		l = m.Ticket.Size()
		n += 1 + l + sovGuildstorage(uint64(l))
	}
	return n
}

func (m *AddGuildGiftCardResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SplitGuildGiftCardReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.LyTicketId))
	n += 1 + sovGuildstorage(uint64(m.Value))
	n += 1 + sovGuildstorage(uint64(m.GameId))
	n += 1 + sovGuildstorage(uint64(m.Num))
	n += 1 + sovGuildstorage(uint64(m.Price))
	n += 1 + sovGuildstorage(uint64(m.Examine))
	if m.Product != nil {
		l = m.Product.Size()
		n += 1 + l + sovGuildstorage(uint64(l))
	}
	return n
}

func (m *SplitGuildGiftCardResp) Size() (n int) {
	var l int
	_ = l
	if m.Product != nil {
		l = m.Product.Size()
		n += 1 + l + sovGuildstorage(uint64(l))
	}
	return n
}

func (m *GrantVoucherData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.Uid))
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.LyTicketId))
	n += 1 + sovGuildstorage(uint64(m.Value))
	n += 1 + sovGuildstorage(uint64(m.LyGameId))
	l = len(m.OrderId)
	n += 1 + l + sovGuildstorage(uint64(l))
	l = len(m.Msg)
	n += 1 + l + sovGuildstorage(uint64(l))
	return n
}

func (m *GetGiftCardLogInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.LyTicketId))
	l = len(m.OrderId)
	n += 1 + l + sovGuildstorage(uint64(l))
	n += 1 + sovGuildstorage(uint64(m.Status))
	n += 1 + sovGuildstorage(uint64(m.Uid))
	n += 1 + sovGuildstorage(uint64(m.Index))
	n += 1 + sovGuildstorage(uint64(m.Cnt))
	return n
}

func (m *GetGiftCardLogInfo) Size() (n int) {
	var l int
	_ = l
	if m.OrderInfo != nil {
		l = m.OrderInfo.Size()
		n += 1 + l + sovGuildstorage(uint64(l))
	}
	n += 1 + sovGuildstorage(uint64(m.Status))
	n += 1 + sovGuildstorage(uint64(m.CreateDate))
	return n
}

func (m *GetGiftCardLogInfoResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.LogTotal))
	if len(m.LogLst) > 0 {
		for _, e := range m.LogLst {
			l = e.Size()
			n += 1 + l + sovGuildstorage(uint64(l))
		}
	}
	return n
}

func (m *SetGiftCardLogStatusReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.OrderId)
	n += 1 + l + sovGuildstorage(uint64(l))
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.Status))
	l = len(m.RespMsg)
	n += 1 + l + sovGuildstorage(uint64(l))
	return n
}

func (m *SetGiftCardLogStatusResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DeleteGiftCardReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildstorage(uint64(m.GuildId))
	n += 1 + sovGuildstorage(uint64(m.LyTicketId))
	return n
}

func (m *DeleteGiftCardResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovGuildstorage(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozGuildstorage(x uint64) (n int) {
	return sovGuildstorage(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *MinGuildProduct) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MinGuildProduct: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MinGuildProduct: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceType", wireType)
			}
			m.SourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Usage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Usage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000100)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeType", wireType)
			}
			m.ExchangeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExchangeType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("source_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("source_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_type")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("usage")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("description")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("icon_url")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildProduct) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildProduct: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildProduct: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MinProduct", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MinProduct == nil {
				m.MinProduct = &MinGuildProduct{}
			}
			if err := m.MinProduct.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrencyType", wireType)
			}
			m.CurrencyType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrencyType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Remain", wireType)
			}
			m.Remain = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Remain |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValidSDate", wireType)
			}
			m.ValidSDate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ValidSDate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValidEDate", wireType)
			}
			m.ValidEDate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ValidEDate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Examine", wireType)
			}
			m.Examine = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Examine |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FetchLimit", wireType)
			}
			m.FetchLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FetchLimit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FetchTimes", wireType)
			}
			m.FetchTimes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FetchTimes |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTs", wireType)
			}
			m.UpdateTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtendInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExtendInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DayLimit", wireType)
			}
			m.DayLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DayLimit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("min_product")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("currency_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("price")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("remain")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ProductItem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ProductItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ProductItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeType", wireType)
			}
			m.ExchangeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExchangeType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeInfo", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExchangeInfo = append(m.ExchangeInfo[:0], dAtA[iNdEx:postIndex]...)
			if m.ExchangeInfo == nil {
				m.ExchangeInfo = []byte{}
			}
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Date", wireType)
			}
			m.Date = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Date |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddProductReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddProductReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddProductReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Product", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Product == nil {
				m.Product = &GuildProduct{}
			}
			if err := m.Product.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemBinary", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemBinary = append(m.ItemBinary, make([]byte, postIndex-iNdEx))
			copy(m.ItemBinary[len(m.ItemBinary)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Payment", wireType)
			}
			m.Payment = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Payment |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddProductResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddProductResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddProductResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetProductReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetProductReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetProductReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceType", wireType)
			}
			m.SourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductStatus", wireType)
			}
			m.ProductStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NeedUnshelve", wireType)
			}
			m.NeedUnshelve = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NeedUnshelve |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cnt", wireType)
			}
			m.Cnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetProductResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetProductResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetProductResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductLst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProductLst = append(m.ProductLst, &GuildProduct{})
			if err := m.ProductLst[len(m.ProductLst)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetProductByIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetProductByIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetProductByIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildstorage
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ProductIdLst = append(m.ProductIdLst, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildstorage
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGuildstorage
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGuildstorage
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ProductIdLst = append(m.ProductIdLst, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductIdLst", wireType)
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetProductByIdResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetProductByIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetProductByIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductLst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProductLst = append(m.ProductLst, &GuildProduct{})
			if err := m.ProductLst[len(m.ProductLst)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SearchProductReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SearchProductReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SearchProductReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KeyWord", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.KeyWord = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cnt", wireType)
			}
			m.Cnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NeedUnshelve", wireType)
			}
			m.NeedUnshelve = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NeedUnshelve |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("key_word")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SearchProductResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SearchProductResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SearchProductResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductLst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProductLst = append(m.ProductLst, &GuildProduct{})
			if err := m.ProductLst[len(m.ProductLst)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExamineRecord) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ExamineRecord: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ExamineRecord: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamineId", wireType)
			}
			m.ExamineId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExamineId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Date", wireType)
			}
			m.Date = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Date |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Num", wireType)
			}
			m.Num = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Num |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Product", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Product == nil {
				m.Product = &GuildProduct{}
			}
			if err := m.Product.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildMsgId", wireType)
			}
			m.GuildMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("examine_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("date")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("num")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetExamineRecordLstReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetExamineRecordLstReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetExamineRecordLstReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToUid", wireType)
			}
			m.ToUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamineId", wireType)
			}
			m.ExamineId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExamineId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamineStatus", wireType)
			}
			m.ExamineStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExamineStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetExamineRecordLstResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetExamineRecordLstResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetExamineRecordLstResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecordLst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecordLst = append(m.RecordLst, &ExamineRecord{})
			if err := m.RecordLst[len(m.RecordLst)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DealExamineReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DealExamineReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DealExamineReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamineId", wireType)
			}
			m.ExamineId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExamineId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("examine_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DealExamineResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DealExamineResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DealExamineResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToUid", wireType)
			}
			m.ToUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Product", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Product == nil {
				m.Product = &GuildProduct{}
			}
			if err := m.Product.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Item", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Item == nil {
				m.Item = &ProductItem{}
			}
			if err := m.Item.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildMsgId", wireType)
			}
			m.GuildMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("to_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyExamineApplyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyExamineApplyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyExamineApplyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamineId", wireType)
			}
			m.ExamineId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExamineId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildMsgId", wireType)
			}
			m.GuildMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("examine_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_msg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyExamineApplyResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyExamineApplyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyExamineApplyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamineId", wireType)
			}
			m.ExamineId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExamineId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("examine_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StorageOperRecord) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StorageOperRecord: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StorageOperRecord: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecordId", wireType)
			}
			m.RecordId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecordId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperType", wireType)
			}
			m.OperType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Date", wireType)
			}
			m.Date = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Date |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Product", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Product == nil {
				m.Product = &GuildProduct{}
			}
			if err := m.Product.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Payment", wireType)
			}
			m.Payment = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Payment |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToUid", wireType)
			}
			m.ToUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtendInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExtendInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Category", wireType)
			}
			m.Category = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Category |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("record_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("oper_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StorageGetOperRecordReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StorageGetOperRecordReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StorageGetOperRecordReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cnt", wireType)
			}
			m.Cnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StorageGetOperRecordResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StorageGetOperRecordResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StorageGetOperRecordResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalRecord", wireType)
			}
			m.TotalRecord = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalRecord |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecordLst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecordLst = append(m.RecordLst, &StorageOperRecord{})
			if err := m.RecordLst[len(m.RecordLst)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_record")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GainProductReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GainProductReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GainProductReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrencyType", wireType)
			}
			m.CurrencyType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrencyType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GainProductResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GainProductResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GainProductResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Item", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Item == nil {
				m.Item = &ProductItem{}
			}
			if err := m.Item.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Product", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Product == nil {
				m.Product = &GuildProduct{}
			}
			if err := m.Product.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamineId", wireType)
			}
			m.ExamineId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExamineId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AllotProductReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AllotProductReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AllotProductReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToUid", wireType)
			}
			m.ToUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Num", wireType)
			}
			m.Num = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Num |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("to_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("num")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AllotProductResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AllotProductResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AllotProductResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToUid", wireType)
			}
			m.ToUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Product", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Product == nil {
				m.Product = &GuildProduct{}
			}
			if err := m.Product.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemLst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemLst = append(m.ItemLst, &ProductItem{})
			if err := m.ItemLst[len(m.ItemLst)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("to_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyProductStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyProductStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyProductStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperType", wireType)
			}
			m.OperType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Num", wireType)
			}
			m.Num = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Num |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Examine", wireType)
			}
			m.Examine = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Examine |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FetchLimit", wireType)
			}
			m.FetchLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FetchLimit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DayLimit", wireType)
			}
			m.DayLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DayLimit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("oper_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyProductStatusResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyProductStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyProductStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetProductBySourceIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetProductBySourceIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetProductBySourceIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceType", wireType)
			}
			m.SourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildstorage
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.SourceIdLst = append(m.SourceIdLst, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildstorage
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGuildstorage
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGuildstorage
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint64(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.SourceIdLst = append(m.SourceIdLst, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceIdLst", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetProductBySourceIdResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetProductBySourceIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetProductBySourceIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductLst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProductLst = append(m.ProductLst, &GuildProduct{})
			if err := m.ProductLst[len(m.ProductLst)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameProductReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGameProductReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGameProductReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceType", wireType)
			}
			m.SourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NeedUnshelve", wireType)
			}
			m.NeedUnshelve = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NeedUnshelve |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildstorage
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.InstallGameIdLst = append(m.InstallGameIdLst, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildstorage
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGuildstorage
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGuildstorage
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.InstallGameIdLst = append(m.InstallGameIdLst, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field InstallGameIdLst", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameProduct) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameProduct: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameProduct: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductLst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProductLst = append(m.ProductLst, &GuildProduct{})
			if err := m.ProductLst[len(m.ProductLst)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameProductResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGameProductResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGameProductResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameProductLst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameProductLst = append(m.GameProductLst, &GameProduct{})
			if err := m.GameProductLst[len(m.GameProductLst)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteProductReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteProductReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteProductReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Num", wireType)
			}
			m.Num = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Num |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteProductResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteProductResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteProductResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFetchRecordReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFetchRecordReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFetchRecordReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cnt", wireType)
			}
			m.Cnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserFetchRecord) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserFetchRecord: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserFetchRecord: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecordId", wireType)
			}
			m.RecordId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecordId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Date", wireType)
			}
			m.Date = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Date |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("record_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("date")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFetchRecordResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFetchRecordResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFetchRecordResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecordLst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecordLst = append(m.RecordLst, &UserFetchRecord{})
			if err := m.RecordLst[len(m.RecordLst)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserFetchRecordReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserFetchRecordReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserFetchRecordReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildstorage
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ProductIdLst = append(m.ProductIdLst, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildstorage
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGuildstorage
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGuildstorage
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ProductIdLst = append(m.ProductIdLst, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductIdLst", wireType)
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnlyTotal", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.OnlyTotal = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserFetchRecordResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserFetchRecordResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserFetchRecordResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecordLst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecordLst = append(m.RecordLst, &GetFetchRecordResp{})
			if err := m.RecordLst[len(m.RecordLst)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CalculateProductReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CalculateProductReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CalculateProductReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceType", wireType)
			}
			m.SourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NeedUnshelve", wireType)
			}
			m.NeedUnshelve = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NeedUnshelve |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("source_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CalculateProductItem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CalculateProductItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CalculateProductItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceType", wireType)
			}
			m.SourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("source_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CalculateProductResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CalculateProductResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CalculateProductResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemLst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemLst = append(m.ItemLst, &CalculateProductItem{})
			if err := m.ItemLst[len(m.ItemLst)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildBySourceIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildBySourceIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildBySourceIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceType", wireType)
			}
			m.SourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemStatus", wireType)
			}
			m.ItemStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("source_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildBySourceIdResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildBySourceIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildBySourceIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildstorage
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GuildIdLst = append(m.GuildIdLst, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildstorage
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGuildstorage
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGuildstorage
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GuildIdLst = append(m.GuildIdLst, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildIdLst", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserStorageInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserStorageInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserStorageInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamineId", wireType)
			}
			m.ExamineId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExamineId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FetchTimes", wireType)
			}
			m.FetchTimes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FetchTimes |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserStorageInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserStorageInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserStorageInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserStorageInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserStorageInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserStorageInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserProductItem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserProductItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserProductItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeType", wireType)
			}
			m.ExchangeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExchangeType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExchangeInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FetchDate", wireType)
			}
			m.FetchDate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FetchDate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserProductItemReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserProductItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserProductItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserProductItemResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserProductItemResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserProductItemResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductItemLst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProductItemLst = append(m.ProductItemLst, &UserProductItem{})
			if err := m.ProductItemLst[len(m.ProductItemLst)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetGameProductCountReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetGameProductCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetGameProductCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildstorage
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GuildIdList = append(m.GuildIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildstorage
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGuildstorage
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGuildstorage
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GuildIdList = append(m.GuildIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildProductCount) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildProductCount: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildProductCount: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductCount", wireType)
			}
			m.ProductCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetGameProductCountResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetGameProductCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetGameProductCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductCountList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProductCountList = append(m.ProductCountList, &GuildProductCount{})
			if err := m.ProductCountList[len(m.ProductCountList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildGiftCardInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildGiftCardInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildGiftCardInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyTicketId", wireType)
			}
			m.LyTicketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyTicketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalPrice", wireType)
			}
			m.TotalPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UsedPrice", wireType)
			}
			m.UsedPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UsedPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpiryDate", wireType)
			}
			m.ExpiryDate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpiryDate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActType", wireType)
			}
			m.ActType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActId", wireType)
			}
			m.ActId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LowerBound", wireType)
			}
			m.LowerBound = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LowerBound |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpperBound", wireType)
			}
			m.UpperBound = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpperBound |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateDate", wireType)
			}
			m.CreateDate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateDate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Balance", wireType)
			}
			m.Balance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Balance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MinRecharge", wireType)
			}
			m.MinRecharge = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MinRecharge |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 16:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Repeat", wireType)
			}
			m.Repeat = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Repeat |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 17:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_ticket_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_price")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildGiftCardReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildGiftCardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildGiftCardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyTicketId", wireType)
			}
			m.LyTicketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyTicketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NeedDelete", wireType)
			}
			m.NeedDelete = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NeedDelete |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildGiftCardResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildGiftCardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildGiftCardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TicketLst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TicketLst = append(m.TicketLst, &GuildGiftCardInfo{})
			if err := m.TicketLst[len(m.TicketLst)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddGuildGiftCardReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddGuildGiftCardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddGuildGiftCardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ticket", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Ticket == nil {
				m.Ticket = &GuildGiftCardInfo{}
			}
			if err := m.Ticket.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ticket")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddGuildGiftCardResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddGuildGiftCardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddGuildGiftCardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SplitGuildGiftCardReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SplitGuildGiftCardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SplitGuildGiftCardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyTicketId", wireType)
			}
			m.LyTicketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyTicketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Num", wireType)
			}
			m.Num = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Num |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Examine", wireType)
			}
			m.Examine = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Examine |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Product", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Product == nil {
				m.Product = &GuildProduct{}
			}
			if err := m.Product.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_ticket_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("value")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("num")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("price")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("examine")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SplitGuildGiftCardResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SplitGuildGiftCardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SplitGuildGiftCardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Product", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Product == nil {
				m.Product = &GuildProduct{}
			}
			if err := m.Product.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GrantVoucherData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GrantVoucherData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GrantVoucherData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyTicketId", wireType)
			}
			m.LyTicketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyTicketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_ticket_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("value")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftCardLogInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftCardLogInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftCardLogInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyTicketId", wireType)
			}
			m.LyTicketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyTicketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cnt", wireType)
			}
			m.Cnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_ticket_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftCardLogInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftCardLogInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftCardLogInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.OrderInfo == nil {
				m.OrderInfo = &GrantVoucherData{}
			}
			if err := m.OrderInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateDate", wireType)
			}
			m.CreateDate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateDate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_info")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftCardLogInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftCardLogInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftCardLogInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LogTotal", wireType)
			}
			m.LogTotal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LogTotal |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LogLst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LogLst = append(m.LogLst, &GetGiftCardLogInfo{})
			if err := m.LogLst[len(m.LogLst)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("log_total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetGiftCardLogStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetGiftCardLogStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetGiftCardLogStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RespMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildstorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RespMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetGiftCardLogStatusResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetGiftCardLogStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetGiftCardLogStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteGiftCardReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteGiftCardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteGiftCardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyTicketId", wireType)
			}
			m.LyTicketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyTicketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_ticket_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteGiftCardResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteGiftCardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteGiftCardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGuildstorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildstorage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipGuildstorage(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGuildstorage
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGuildstorage
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthGuildstorage
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGuildstorage
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGuildstorage(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGuildstorage = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGuildstorage   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("services/guildstorage/guildstorage.proto", fileDescriptorGuildstorage)
}

var fileDescriptorGuildstorage = []byte{
	// 4021 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x5b, 0x5d, 0x8c, 0x1c, 0x47,
	0x5e, 0xbf, 0x9e, 0x9e, 0xdd, 0x99, 0xfd, 0xcf, 0xcc, 0x7a, 0xdc, 0xeb, 0x8f, 0xc9, 0x38, 0xd9,
	0x6d, 0xd7, 0xda, 0xce, 0x3a, 0xa7, 0x4d, 0x44, 0x08, 0x8a, 0x6e, 0x6e, 0xb2, 0x91, 0xd7, 0xde,
	0x6c, 0x16, 0xf9, 0x63, 0xb5, 0x1f, 0x89, 0x4e, 0x82, 0x6b, 0xb5, 0xa7, 0xdb, 0xe3, 0x66, 0x7b,
	0xba, 0x9b, 0xae, 0x1e, 0x5f, 0x46, 0x42, 0x02, 0x84, 0x4e, 0x0a, 0xe8, 0xee, 0x84, 0x10, 0xe2,
	0x5b, 0x10, 0x4e, 0x41, 0x02, 0x71, 0x2f, 0x48, 0xc0, 0x09, 0x9d, 0xc4, 0x0b, 0x2f, 0x27, 0xa4,
	0x20, 0x5e, 0x78, 0x45, 0x28, 0xf0, 0x60, 0x89, 0xbc, 0xf0, 0xca, 0x13, 0xaa, 0xea, 0xea, 0xee,
	0xaa, 0xea, 0x9a, 0x9d, 0xb1, 0xbd, 0xe6, 0xc9, 0x9e, 0x7f, 0x55, 0x77, 0xff, 0xea, 0xff, 0xfd,
	0x51, 0x0b, 0x1b, 0xd8, 0x8d, 0x9f, 0x78, 0x03, 0x17, 0xbf, 0x35, 0x1c, 0x7b, 0xbe, 0x83, 0x93,
	0x30, 0xb6, 0x87, 0xae, 0xf0, 0xe3, 0xcd, 0x28, 0x0e, 0x93, 0xd0, 0x68, 0xee, 0x12, 0xda, 0x61,
	0x4a, 0xeb, 0x5e, 0x1b, 0x84, 0xa3, 0x51, 0x18, 0xbc, 0x95, 0xf8, 0x4f, 0x22, 0x6f, 0x70, 0xe2,
	0xbb, 0x6f, 0xe1, 0x93, 0x87, 0x63, 0xcf, 0x4f, 0xbc, 0x20, 0x99, 0x44, 0xec, 0x19, 0xf4, 0x85,
	0x0e, 0xe7, 0xee, 0x79, 0x01, 0x7d, 0x72, 0x3f, 0x0e, 0x9d, 0xf1, 0x20, 0x31, 0xd6, 0x01, 0xa2,
	0xf4, 0xbf, 0x96, 0xe7, 0x74, 0x34, 0x53, 0xdb, 0x68, 0x6d, 0x57, 0x7f, 0xfa, 0xef, 0x6b, 0x5f,
	0x3b, 0x58, 0x62, 0xf4, 0x3d, 0xc7, 0xe8, 0x40, 0x35, 0xb0, 0x47, 0x6e, 0xa7, 0x62, 0x56, 0x36,
	0x96, 0xd8, 0x32, 0xa5, 0x18, 0xd7, 0xa1, 0x81, 0xc3, 0x71, 0x3c, 0x70, 0x2d, 0xf2, 0x9d, 0x8e,
	0x6e, 0x56, 0xf2, 0xe7, 0x21, 0x5d, 0x38, 0x9a, 0x44, 0xae, 0x71, 0x15, 0x96, 0xd8, 0x36, 0xcf,
	0xe9, 0x54, 0xcd, 0xca, 0x46, 0x95, 0x6d, 0xaa, 0xa7, 0xe4, 0x3d, 0x87, 0x6c, 0xf1, 0x12, 0x77,
	0x94, 0xbe, 0x67, 0x81, 0x7b, 0x4f, 0x9d, 0x90, 0xe9, 0x5b, 0xba, 0xb0, 0x30, 0xc6, 0xf6, 0xd0,
	0xed, 0x2c, 0x72, 0x38, 0x52, 0x92, 0x71, 0x03, 0x1a, 0x8e, 0x8b, 0x07, 0xb1, 0x17, 0x25, 0x5e,
	0x18, 0x74, 0x6a, 0xdc, 0x0e, 0x7e, 0xc1, 0x78, 0x15, 0x16, 0x71, 0x62, 0x27, 0x63, 0xdc, 0xa9,
	0x73, 0xdf, 0x60, 0x34, 0xe3, 0x35, 0xa8, 0x0d, 0xed, 0x11, 0x45, 0xb9, 0xc4, 0x2f, 0x13, 0xe2,
	0x9e, 0x63, 0xac, 0x41, 0xdd, 0x1b, 0x84, 0x81, 0x35, 0x8e, 0xfd, 0x0e, 0x70, 0x5f, 0xa8, 0x11,
	0xea, 0x71, 0xec, 0x93, 0x0d, 0x54, 0x56, 0xe4, 0x05, 0x0d, 0xee, 0x05, 0x35, 0x4a, 0xdd, 0x73,
	0x8c, 0x9b, 0xd0, 0x72, 0x3f, 0x19, 0x3c, 0xb6, 0x83, 0x21, 0xe3, 0x58, 0x93, 0xe3, 0x78, 0x33,
	0x5b, 0xa2, 0xa7, 0x35, 0xa1, 0x1e, 0xf9, 0x76, 0xf2, 0x28, 0x8c, 0x47, 0x9d, 0x16, 0xb7, 0x2b,
	0xa7, 0xa2, 0xff, 0xd5, 0xa1, 0x29, 0x08, 0x73, 0x0b, 0x1a, 0x23, 0x2f, 0xb0, 0x98, 0xe0, 0x3a,
	0x9a, 0x59, 0xd9, 0x68, 0xbc, 0xfd, 0xda, 0x9b, 0xbc, 0xaa, 0xbc, 0x29, 0x29, 0xc0, 0x01, 0x8c,
	0xbc, 0x20, 0x7b, 0xfe, 0x26, 0xb4, 0x06, 0xe3, 0x38, 0x76, 0x83, 0xc1, 0x24, 0x45, 0x57, 0xe1,
	0xce, 0xd0, 0xcc, 0x96, 0x32, 0x59, 0x44, 0xb1, 0x37, 0x10, 0x45, 0x9e, 0x92, 0x08, 0x8f, 0x63,
	0x77, 0x64, 0x7b, 0x01, 0x15, 0x75, 0xce, 0xc4, 0x94, 0x46, 0x9e, 0x4c, 0xc2, 0xc4, 0xf6, 0x05,
	0x21, 0xa7, 0x24, 0xe3, 0x06, 0x34, 0x9f, 0xd8, 0xbe, 0xe7, 0x58, 0xd8, 0x72, 0xec, 0x84, 0x08,
	0xba, 0x38, 0x37, 0xd0, 0x95, 0xc3, 0x3b, 0x76, 0xe2, 0x16, 0xfb, 0xdc, 0x74, 0x5f, 0xad, 0xb4,
	0x6f, 0x87, 0xee, 0x5b, 0x85, 0x9a, 0xfb, 0x89, 0x3d, 0xf2, 0x02, 0xb7, 0x53, 0xe7, 0xb6, 0x64,
	0x44, 0xa2, 0xbe, 0x8f, 0xdc, 0x64, 0xf0, 0xd8, 0xf2, 0xbd, 0x91, 0x97, 0x74, 0x96, 0xf8, 0xd7,
	0xd0, 0x85, 0xbb, 0x84, 0x5e, 0x6c, 0x4b, 0xbc, 0x91, 0x8b, 0x3b, 0x50, 0xda, 0x76, 0x44, 0xe8,
	0x44, 0x85, 0xc7, 0x11, 0xc1, 0x63, 0x25, 0xb8, 0xd3, 0xe0, 0x45, 0x96, 0x92, 0x8f, 0x30, 0x79,
	0x93, 0xfb, 0x49, 0xe2, 0x06, 0x8e, 0xe5, 0x05, 0x8f, 0x42, 0x2a, 0xfd, 0x4c, 0x89, 0x20, 0x5d,
	0xd8, 0x0b, 0x1e, 0x85, 0xe4, 0x4d, 0x8e, 0x3d, 0x61, 0xa8, 0x04, 0xe1, 0x3b, 0xf6, 0x84, 0x62,
	0x42, 0xff, 0xa3, 0x41, 0x83, 0xc9, 0x6d, 0x2f, 0x71, 0x47, 0xc6, 0x05, 0xa8, 0x50, 0x03, 0x2e,
	0x78, 0x5a, 0xf1, 0x1c, 0xe3, 0x12, 0xe8, 0x63, 0xcf, 0x11, 0xe4, 0x48, 0x08, 0x82, 0xa2, 0xea,
	0x2a, 0x45, 0x15, 0xfd, 0x02, 0x2f, 0x47, 0xce, 0x2f, 0x94, 0xb4, 0x79, 0x61, 0xaa, 0x36, 0xf3,
	0x5b, 0xe9, 0xd1, 0x89, 0x68, 0x9b, 0xf2, 0x56, 0x7a, 0xf8, 0x0e, 0x54, 0x4b, 0x42, 0xa5, 0x14,
	0xf4, 0xa7, 0x1a, 0xb4, 0x6e, 0x39, 0xb9, 0xea, 0xba, 0xbf, 0x9c, 0x9d, 0x4f, 0x93, 0xcf, 0xf7,
	0x0e, 0xd4, 0x32, 0x2b, 0xa8, 0x50, 0x2b, 0xe8, 0x8a, 0x56, 0x20, 0x98, 0x40, 0xb6, 0xd5, 0x58,
	0x83, 0x06, 0xf5, 0x41, 0x0f, 0xbd, 0xc0, 0x8e, 0x27, 0x1d, 0xdd, 0xd4, 0x37, 0x9a, 0x07, 0x40,
	0x48, 0xdb, 0x94, 0x42, 0xf4, 0x29, 0xb2, 0x27, 0x23, 0x37, 0x48, 0x3a, 0x55, 0x5e, 0x9f, 0x18,
	0x11, 0xfd, 0x1c, 0x2c, 0xf3, 0xf8, 0x70, 0x54, 0xf2, 0xaf, 0x2a, 0x3e, 0xa2, 0x2f, 0x2a, 0xd0,
	0xda, 0x75, 0x13, 0xee, 0x5c, 0xbc, 0x7c, 0xb4, 0xd9, 0xf2, 0xa9, 0xa8, 0xfd, 0x76, 0xc9, 0x3b,
	0x6b, 0xd3, 0xbc, 0x73, 0xe1, 0x7a, 0xf9, 0x73, 0x15, 0xae, 0xf7, 0xeb, 0xb0, 0x9c, 0x7d, 0x8e,
	0xb9, 0x4f, 0x5e, 0xd4, 0x2d, 0xb6, 0x76, 0x98, 0x7a, 0xd1, 0x9b, 0xd0, 0x0a, 0x5c, 0xd7, 0xb1,
	0xc6, 0x01, 0x7e, 0xec, 0xfa, 0x4f, 0x44, 0x33, 0x6e, 0x92, 0xa5, 0x63, 0xb6, 0x42, 0x9c, 0x81,
	0x17, 0x38, 0xee, 0x27, 0x82, 0xb0, 0x53, 0x12, 0x91, 0xed, 0x20, 0x48, 0x04, 0xc3, 0x25, 0x04,
	0xc1, 0x31, 0x2e, 0x29, 0x1d, 0xa3, 0x07, 0xcb, 0x3c, 0x3b, 0x71, 0x64, 0x7c, 0x13, 0x1a, 0x19,
	0x7e, 0x1f, 0x13, 0xcf, 0xa8, 0xcf, 0xd0, 0x89, 0x8c, 0xbb, 0x77, 0x71, 0x52, 0x78, 0x2c, 0x9e,
	0xcd, 0x29, 0x09, 0xfd, 0xba, 0x06, 0x97, 0xb6, 0xed, 0x64, 0xf0, 0xb8, 0xf8, 0xe0, 0xf6, 0x64,
	0xcf, 0x99, 0x4b, 0x86, 0xd7, 0x0a, 0xa6, 0x7a, 0x0e, 0xc5, 0x55, 0x31, 0xf5, 0x8d, 0xd6, 0x41,
	0x33, 0x97, 0x20, 0xf9, 0x3a, 0x7f, 0x5c, 0x5d, 0x79, 0xdc, 0x8f, 0xe0, 0xb2, 0x12, 0xc2, 0x0b,
	0x9e, 0x1b, 0xfd, 0x9b, 0x06, 0xed, 0x43, 0xd7, 0x8e, 0x07, 0x8f, 0x9f, 0x45, 0x33, 0xd7, 0xa0,
	0x7e, 0xe2, 0x4e, 0xac, 0xef, 0x84, 0xb1, 0x23, 0x24, 0x0c, 0xb5, 0x13, 0x77, 0xf2, 0x71, 0x18,
	0x3b, 0x85, 0xcc, 0xf5, 0xa9, 0x32, 0xaf, 0xca, 0x32, 0x2f, 0xa9, 0xd4, 0xc2, 0x54, 0x95, 0xe2,
	0xf9, 0xb5, 0xa8, 0xe4, 0x97, 0x0f, 0xe7, 0xa5, 0x63, 0xe1, 0xa8, 0x10, 0xb2, 0x56, 0x0e, 0x4b,
	0x12, 0x17, 0x2b, 0xcf, 0xc4, 0xc5, 0x3f, 0xae, 0x40, 0x6b, 0x27, 0x8d, 0x37, 0x07, 0xee, 0x80,
	0x30, 0x60, 0x1d, 0x80, 0x05, 0xa0, 0x92, 0x4f, 0x60, 0xf4, 0x3d, 0xd1, 0x43, 0x57, 0x54, 0x7c,
	0x66, 0xae, 0x4f, 0x97, 0x5d, 0x5f, 0xe6, 0x3e, 0x79, 0x9f, 0x4d, 0x29, 0x5c, 0xee, 0xb3, 0xa0,
	0xc8, 0x7d, 0x2e, 0x81, 0x1e, 0x8c, 0x47, 0x34, 0xb7, 0xca, 0xdf, 0x17, 0x8c, 0x47, 0xbc, 0x2b,
	0xad, 0xcd, 0xef, 0x4a, 0x6f, 0x40, 0x33, 0x85, 0x3f, 0xc2, 0x43, 0x72, 0x04, 0xde, 0x8a, 0x81,
	0xae, 0xdc, 0xc3, 0x43, 0xe2, 0xfa, 0x34, 0xb8, 0xb4, 0xeb, 0x26, 0x02, 0x83, 0xee, 0xe2, 0x33,
	0xf4, 0x81, 0x57, 0x60, 0x31, 0x09, 0xad, 0x94, 0x53, 0x82, 0xf5, 0x1e, 0x7b, 0xb2, 0x24, 0x78,
	0xad, 0xe3, 0x24, 0xf1, 0x75, 0x58, 0xce, 0x36, 0xa9, 0x7c, 0x1f, 0x5b, 0x4b, 0x7d, 0x1f, 0x3a,
	0x86, 0xcb, 0xca, 0xe3, 0xe0, 0xc8, 0xe8, 0x01, 0xc4, 0x94, 0xc0, 0x99, 0xe2, 0x15, 0x91, 0x97,
	0xc2, 0x73, 0x07, 0x4b, 0x71, 0xf6, 0x3c, 0x4a, 0x60, 0xf9, 0x8e, 0x6b, 0xfb, 0xf9, 0xfa, 0x7c,
	0xdc, 0xe1, 0xce, 0x56, 0x51, 0x6b, 0x59, 0xa1, 0x12, 0x7a, 0x59, 0x25, 0xd0, 0x4f, 0x34, 0x38,
	0x27, 0x7c, 0x16, 0x47, 0x1c, 0x3f, 0x25, 0x43, 0x39, 0x7e, 0xee, 0xb0, 0xbb, 0x09, 0x55, 0x12,
	0x68, 0xa8, 0x80, 0x1a, 0x6f, 0xbf, 0x22, 0x3e, 0xc2, 0xe5, 0x38, 0x07, 0x74, 0x5b, 0x49, 0xb5,
	0xaa, 0x53, 0x54, 0xeb, 0xbb, 0x1a, 0x5c, 0xbc, 0x17, 0x3a, 0xde, 0xa3, 0x09, 0xc3, 0x7f, 0x2b,
	0x8a, 0xfc, 0xc9, 0xd9, 0xf1, 0x4e, 0xc6, 0x21, 0x14, 0x3f, 0x1c, 0x8e, 0x6f, 0xc3, 0x25, 0x15,
	0x0c, 0x1c, 0x9d, 0x0d, 0x0e, 0xf4, 0x5f, 0x3a, 0x9c, 0x67, 0xdc, 0x7a, 0x10, 0xb9, 0x31, 0x73,
	0x32, 0x57, 0x81, 0xa9, 0x8f, 0xfc, 0xf2, 0x7a, 0x4a, 0xde, 0x7b, 0x81, 0xe4, 0xf0, 0x2a, 0x2c,
	0x85, 0x91, 0x1b, 0x67, 0x09, 0x03, 0xf7, 0x6e, 0x42, 0xa6, 0x09, 0x43, 0xe6, 0x85, 0x16, 0xe4,
	0x24, 0x8e, 0xd7, 0x91, 0x45, 0x2a, 0xf0, 0xb9, 0x74, 0x84, 0xcb, 0xbc, 0x6a, 0x8a, 0xcc, 0x8b,
	0x53, 0xcb, 0x7a, 0xd9, 0xcc, 0xa5, 0xac, 0x7b, 0x69, 0x4a, 0xd6, 0x6d, 0x42, 0x7d, 0x60, 0x27,
	0xee, 0x30, 0x8c, 0x27, 0x42, 0x8e, 0x9f, 0x53, 0xd1, 0x1d, 0x68, 0xdd, 0xbe, 0x75, 0xb4, 0xb3,
	0xfb, 0xe0, 0xe0, 0x5b, 0xd6, 0xd1, 0xb7, 0xf6, 0x77, 0x8c, 0x0e, 0x5c, 0xc8, 0x09, 0xbb, 0xc7,
	0x7b, 0x77, 0xef, 0x58, 0x0f, 0x3e, 0xbe, 0xbf, 0x73, 0xd0, 0xd6, 0x8c, 0x57, 0xe0, 0xa2, 0xb4,
	0x72, 0x6f, 0xe7, 0xde, 0xf6, 0xce, 0x41, 0xbb, 0x82, 0xde, 0x81, 0xe5, 0x9c, 0x62, 0x3d, 0xd8,
	0xdf, 0x39, 0x30, 0xda, 0xd0, 0x24, 0xff, 0x5a, 0x1f, 0xec, 0x1c, 0xdd, 0xfe, 0xd0, 0xda, 0x6f,
	0x6b, 0x39, 0xe5, 0xd6, 0xdd, 0xbb, 0x0f, 0x8e, 0xac, 0xfd, 0x76, 0x05, 0x05, 0x70, 0x99, 0xb1,
	0x68, 0xd7, 0x4d, 0x0a, 0x41, 0xcf, 0xa5, 0xcf, 0x79, 0xc8, 0xad, 0x4c, 0x0d, 0xb9, 0xba, 0x14,
	0x72, 0xd1, 0x6f, 0x68, 0xd0, 0x51, 0x7f, 0x10, 0x47, 0xc6, 0xeb, 0xd0, 0xa4, 0xa1, 0xd1, 0x4a,
	0x95, 0x49, 0xf8, 0x6a, 0x83, 0xae, 0x30, 0x35, 0xdc, 0x12, 0x9c, 0x5e, 0x1a, 0x39, 0xd7, 0x44,
	0x81, 0x97, 0x74, 0x97, 0x77, 0x7c, 0x7f, 0xa7, 0xc1, 0xf2, 0xae, 0x9d, 0x97, 0xa8, 0xa7, 0xe5,
	0xfc, 0x33, 0x23, 0xa6, 0x18, 0x2f, 0xf4, 0xa9, 0x35, 0x8d, 0x58, 0x03, 0xf3, 0xee, 0x65, 0x4a,
	0x0d, 0xcc, 0x2b, 0x79, 0x4a, 0x42, 0xff, 0xa0, 0xc1, 0x39, 0x01, 0x37, 0x8e, 0x38, 0x67, 0xab,
	0x29, 0xe2, 0x6f, 0xe6, 0x05, 0x2b, 0xf3, 0x79, 0x41, 0xce, 0x8c, 0xf4, 0xf9, 0xcd, 0x68, 0x9e,
	0x80, 0x87, 0xbe, 0xa7, 0xc1, 0xb9, 0x5b, 0xbe, 0x1f, 0xf2, 0x05, 0xc9, 0xa9, 0x6e, 0xff, 0x6c,
	0x38, 0xcf, 0x12, 0x90, 0xaa, 0x94, 0x80, 0xa0, 0x1f, 0x6b, 0xd0, 0x16, 0xe1, 0x3c, 0x53, 0x18,
	0x9a, 0x9b, 0x37, 0xef, 0x00, 0xad, 0x77, 0xa8, 0xa2, 0xea, 0x54, 0x51, 0x4f, 0x11, 0x42, 0x8d,
	0x6c, 0x15, 0x8a, 0x83, 0x6a, 0xb9, 0x38, 0xf8, 0x4c, 0xcf, 0x5c, 0xff, 0x3e, 0x5f, 0x20, 0x3d,
	0x57, 0x72, 0xa3, 0x64, 0x99, 0xe0, 0x88, 0x75, 0xa5, 0x23, 0xce, 0xb9, 0xaa, 0x89, 0x69, 0xdd,
	0x29, 0xca, 0xcb, 0xb7, 0x4d, 0x16, 0xe7, 0x68, 0x9b, 0xd4, 0xa6, 0xb4, 0x4d, 0x84, 0x2e, 0x46,
	0x5d, 0xd9, 0xc5, 0xf8, 0xd5, 0x2c, 0x44, 0x33, 0x06, 0x3d, 0xc8, 0x60, 0x9b, 0xf0, 0xaa, 0x72,
	0xc1, 0x3a, 0xa4, 0x59, 0x7e, 0x5b, 0x33, 0x10, 0xac, 0xaa, 0x77, 0x1c, 0x07, 0x6c, 0x4f, 0x65,
	0xfa, 0x5b, 0x52, 0x6a, 0x5b, 0x47, 0xaf, 0xc0, 0x65, 0xa5, 0x84, 0x70, 0x44, 0xf2, 0x87, 0xcb,
	0x7c, 0x49, 0x75, 0xc8, 0x5a, 0x95, 0x73, 0x89, 0x4f, 0x2a, 0xbd, 0x2b, 0x53, 0x4a, 0x6f, 0x04,
	0xad, 0xbc, 0x31, 0x9a, 0x2b, 0x5e, 0xf5, 0xa0, 0x91, 0xb5, 0x45, 0x89, 0x0b, 0xfc, 0x18, 0x3a,
	0x6a, 0x18, 0x2f, 0x5a, 0xdf, 0xfd, 0x61, 0x05, 0xce, 0xef, 0xba, 0xc9, 0xae, 0x3d, 0x72, 0x9f,
	0xa5, 0xc0, 0xe3, 0x9a, 0xa4, 0xfc, 0xb1, 0xb2, 0x26, 0xe9, 0xd9, 0x35, 0x1d, 0xce, 0xb2, 0xe8,
	0x33, 0x36, 0x61, 0xc5, 0x0b, 0x70, 0x62, 0xfb, 0xbe, 0xc5, 0xd0, 0x53, 0x8e, 0xd5, 0x68, 0xc5,
	0xdd, 0x66, 0x4b, 0xbb, 0xf4, 0x08, 0x84, 0x37, 0x1e, 0x34, 0x38, 0xbe, 0xf0, 0x67, 0xd6, 0x14,
	0x8d, 0xe1, 0x17, 0x2a, 0x10, 0xc7, 0x60, 0xc8, 0x52, 0xc0, 0x91, 0x71, 0x1b, 0xda, 0xf4, 0x8b,
	0x65, 0xf1, 0x4a, 0x5e, 0x89, 0x7f, 0x70, 0x79, 0x58, 0xfc, 0x98, 0xd5, 0xb9, 0x88, 0xa0, 0x7d,
	0xc7, 0xf5, 0xdd, 0xc4, 0x7d, 0x91, 0xb6, 0xd3, 0x69, 0x8e, 0x5c, 0x97, 0x5c, 0x0e, 0x5a, 0x81,
	0xf3, 0xd2, 0x17, 0x71, 0x84, 0xbe, 0xaf, 0x51, 0x25, 0xfc, 0x80, 0xb8, 0x8d, 0x67, 0xc8, 0x68,
	0xe6, 0x02, 0xf2, 0x1c, 0x9d, 0x06, 0xf4, 0x08, 0xce, 0x1d, 0x63, 0x37, 0xe6, 0xf0, 0xbc, 0x48,
	0x2a, 0x9d, 0xa5, 0xc1, 0xba, 0x5c, 0x8c, 0xa3, 0xbf, 0xd5, 0xa8, 0xd8, 0x85, 0x73, 0xcf, 0x59,
	0x12, 0xcc, 0x75, 0xf0, 0x54, 0xee, 0x7a, 0xb9, 0x99, 0xd1, 0x17, 0x32, 0xb2, 0x2a, 0x55, 0x29,
	0x69, 0x46, 0x20, 0x31, 0x80, 0xcf, 0xc7, 0x3e, 0xd3, 0xa0, 0x9b, 0x35, 0x9b, 0xe4, 0x6d, 0x2f,
	0x92, 0x9b, 0x95, 0x7b, 0x61, 0xba, 0xa2, 0x17, 0xb6, 0x0e, 0x10, 0x06, 0xfe, 0xc4, 0x2a, 0x22,
	0x6e, 0x3d, 0x3b, 0x3c, 0xa1, 0x1f, 0x51, 0xc5, 0xfe, 0x36, 0x5c, 0x99, 0x8a, 0x10, 0x47, 0xc6,
	0xfb, 0x8a, 0x32, 0xdc, 0x94, 0x4c, 0xaa, 0x24, 0x17, 0x9e, 0x05, 0x5f, 0x68, 0xb0, 0x72, 0xdb,
	0xf6, 0x07, 0x63, 0xdf, 0x7e, 0x36, 0xe3, 0x29, 0xc5, 0x84, 0xca, 0x6c, 0xcf, 0xa8, 0xcf, 0xe7,
	0x19, 0xab, 0x73, 0x79, 0xc6, 0x05, 0x65, 0x3b, 0xec, 0x57, 0xe0, 0x82, 0x7c, 0x1c, 0x3a, 0x51,
	0x90, 0xe0, 0x6a, 0xf3, 0xc0, 0xad, 0x4c, 0x1b, 0xdc, 0x4d, 0x53, 0x47, 0xf4, 0xa4, 0xfc, 0xf5,
	0xf9, 0x0c, 0xe1, 0x3d, 0x2e, 0x5d, 0x4b, 0x1d, 0x2e, 0x12, 0xa5, 0xa8, 0x3a, 0x54, 0x9e, 0xb7,
	0xa1, 0x4f, 0x35, 0xb8, 0x48, 0xdc, 0x2e, 0x79, 0x42, 0x8c, 0xed, 0xc2, 0xb0, 0x52, 0x30, 0xf7,
	0x7c, 0x58, 0x39, 0x67, 0x74, 0xbf, 0xce, 0xe6, 0x09, 0x79, 0x8b, 0x85, 0xdb, 0x46, 0x16, 0x58,
	0xcf, 0xa8, 0x47, 0x5b, 0x60, 0x25, 0x24, 0x38, 0x32, 0xcc, 0xac, 0xc5, 0xe0, 0x15, 0xda, 0xda,
	0x62, 0xcd, 0x85, 0x34, 0x4e, 0xfd, 0x44, 0x4b, 0xfd, 0x15, 0x3b, 0x34, 0xad, 0x63, 0x5f, 0x6e,
	0x81, 0x34, 0x57, 0xcf, 0x4c, 0x9a, 0x98, 0x2d, 0xa8, 0x27, 0x66, 0x68, 0x4c, 0x65, 0x20, 0xe1,
	0x7f, 0xe9, 0x35, 0x1e, 0xea, 0x50, 0x86, 0x97, 0x3e, 0x8b, 0x23, 0xf4, 0x9b, 0x95, 0x94, 0x9d,
	0xbc, 0x1d, 0xbc, 0xf4, 0x7a, 0x53, 0x9c, 0xa1, 0x55, 0xe7, 0x9f, 0xa1, 0x2d, 0x70, 0x8d, 0x0c,
	0x71, 0x86, 0xb6, 0x0e, 0x29, 0x9b, 0xcb, 0x63, 0xd4, 0x25, 0x4a, 0xa7, 0xd3, 0xd1, 0xd7, 0x80,
	0x5a, 0x03, 0x01, 0xc7, 0xa7, 0xf0, 0x8b, 0x84, 0xb8, 0xe7, 0x70, 0xc2, 0xe1, 0x0d, 0xe8, 0xa5,
	0x0b, 0xc7, 0xce, 0x85, 0x23, 0x7c, 0x16, 0x47, 0xc6, 0x2e, 0xb4, 0xf3, 0xc7, 0x33, 0xcb, 0xd7,
	0xa6, 0xc5, 0x2f, 0xfe, 0xe1, 0x2c, 0xb4, 0xec, 0x31, 0xdb, 0x7f, 0x1f, 0x5e, 0xd9, 0xb6, 0x13,
	0x31, 0xe9, 0xba, 0x1d, 0x8e, 0x03, 0xea, 0xc6, 0x11, 0xb4, 0x0a, 0x9b, 0xf3, 0x72, 0xa3, 0x6b,
	0x64, 0x46, 0xe7, 0xe1, 0x04, 0x59, 0x70, 0x9e, 0x4f, 0xe7, 0xe8, 0xb3, 0xb3, 0x3d, 0xd6, 0x4d,
	0xc8, 0x06, 0x65, 0xd6, 0x80, 0x3c, 0x21, 0x8e, 0xd7, 0x23, 0xee, 0x5d, 0xe8, 0x84, 0x46, 0x59,
	0x25, 0x42, 0x1c, 0x19, 0xf7, 0xc0, 0x10, 0x5e, 0x54, 0xe0, 0x2c, 0x35, 0x57, 0x4a, 0x30, 0x0f,
	0xda, 0xfc, 0x87, 0xe8, 0x69, 0xbe, 0xaa, 0xb2, 0xe3, 0xec, 0x7a, 0x8f, 0x92, 0xdb, 0x76, 0x9c,
	0x76, 0xc3, 0x6e, 0x40, 0x93, 0x44, 0x5a, 0x6f, 0x70, 0xe2, 0x96, 0x66, 0x97, 0xe0, 0x4f, 0x8e,
	0xe8, 0xc2, 0x3c, 0x83, 0x0a, 0x2e, 0x77, 0xd6, 0x15, 0xb9, 0xf3, 0x75, 0x48, 0x1b, 0x46, 0x56,
	0x5a, 0x8e, 0xf2, 0xe5, 0x3f, 0xd0, 0x85, 0x7d, 0x5a, 0x93, 0xae, 0x03, 0x8c, 0xb1, 0xeb, 0x58,
	0xe5, 0xa2, 0x75, 0x89, 0xd0, 0xd3, 0x4d, 0xb4, 0xd1, 0x17, 0x79, 0xf1, 0x44, 0x71, 0x7d, 0x20,
	0x5d, 0xa0, 0x8a, 0xbf, 0x06, 0x75, 0x7b, 0x90, 0xa4, 0xe6, 0x26, 0x74, 0x13, 0xed, 0x41, 0x42,
	0x2d, 0xed, 0x0a, 0x2c, 0xda, 0xa9, 0x92, 0x0a, 0xdd, 0x44, 0x3b, 0x9b, 0xaa, 0xfa, 0xe1, 0x77,
	0xdc, 0xd8, 0x7a, 0x18, 0x8e, 0x03, 0x47, 0xbc, 0x34, 0x40, 0x17, 0xb6, 0x09, 0x9d, 0x6c, 0x1b,
	0x47, 0x51, 0xbe, 0x4d, 0xb8, 0x34, 0x40, 0x17, 0xf2, 0x6d, 0x83, 0xd8, 0xb5, 0x13, 0x76, 0x93,
	0x81, 0xbf, 0x36, 0x00, 0xe9, 0x42, 0x76, 0x93, 0xe1, 0xa1, 0xed, 0xdb, 0xc1, 0x40, 0xbc, 0x32,
	0x92, 0x11, 0x8d, 0xd7, 0xa1, 0x39, 0xf2, 0x02, 0x2b, 0x76, 0x07, 0x8f, 0xed, 0x78, 0xe8, 0x0a,
	0x97, 0x06, 0x1a, 0x23, 0x2f, 0x38, 0x60, 0x0b, 0x06, 0x02, 0xf0, 0x27, 0x59, 0x09, 0xd4, 0x59,
	0xe6, 0x33, 0x02, 0x7f, 0x92, 0xd6, 0x3f, 0x5c, 0xa3, 0xea, 0x1c, 0xef, 0x17, 0x58, 0xa3, 0x8a,
	0x5e, 0xef, 0x88, 0x5c, 0x3b, 0xe9, 0xb4, 0xf9, 0xd5, 0x94, 0x26, 0xe4, 0x1b, 0xe7, 0x95, 0xf9,
	0xc6, 0x77, 0x35, 0x58, 0xc9, 0xe2, 0x5d, 0xa6, 0x71, 0x73, 0xe5, 0x4f, 0xb2, 0x46, 0x0a, 0x61,
	0x97, 0xd3, 0xc8, 0xeb, 0xd0, 0xa0, 0xd9, 0x91, 0x43, 0x8b, 0x0d, 0x31, 0xec, 0x92, 0x85, 0xb4,
	0x08, 0x41, 0x1f, 0xc1, 0x85, 0x32, 0x0c, 0x1c, 0x19, 0x5b, 0x00, 0xec, 0x1b, 0xfe, 0xa9, 0x56,
	0xc5, 0x5b, 0xcb, 0xc1, 0x52, 0xfa, 0x08, 0xf1, 0x2e, 0xf7, 0x61, 0xe5, 0x96, 0xe3, 0x94, 0x8e,
	0xf7, 0x2e, 0x2c, 0xa6, 0x7b, 0xd8, 0xbd, 0x9c, 0x99, 0xaf, 0x64, 0xdb, 0xd1, 0x25, 0xb8, 0x50,
	0x7e, 0x1f, 0x8e, 0xd0, 0x5f, 0x54, 0xe0, 0xe2, 0x61, 0xe4, 0x7b, 0x67, 0xc2, 0x49, 0xb5, 0x6d,
	0x77, 0x61, 0xe1, 0x89, 0xed, 0x8f, 0xa5, 0x5b, 0x3e, 0x94, 0xc4, 0x9b, 0x75, 0x55, 0xd1, 0x06,
	0x60, 0x45, 0xe0, 0x82, 0x3c, 0x4e, 0xcc, 0xfb, 0x4e, 0x8b, 0xe5, 0x8b, 0x43, 0x5c, 0xdf, 0xa9,
	0xc6, 0xc3, 0xce, 0xfa, 0x4e, 0x5c, 0x5f, 0xaf, 0x3e, 0x77, 0x5f, 0x0f, 0xdd, 0x87, 0x4b, 0x2a,
	0x36, 0xe1, 0x88, 0x7f, 0x9f, 0x36, 0xf7, 0xb8, 0x0a, 0x7d, 0xa5, 0x41, 0x7b, 0x37, 0xb6, 0x83,
	0xe4, 0xa3, 0x70, 0x3c, 0x78, 0xec, 0xc6, 0x77, 0xec, 0xc4, 0x7e, 0xfe, 0x98, 0x28, 0x8b, 0x42,
	0x9f, 0x25, 0x8a, 0x6a, 0x59, 0x14, 0xa2, 0x4d, 0x0b, 0x97, 0xe7, 0x72, 0x9b, 0x5e, 0x83, 0x7a,
	0x18, 0x3b, 0x6e, 0x4c, 0x76, 0xf0, 0xf7, 0xe7, 0x6a, 0x94, 0x9a, 0x0a, 0x6c, 0x84, 0x87, 0xd4,
	0x1f, 0x66, 0x6b, 0x84, 0x80, 0xfe, 0x9b, 0x25, 0xca, 0x8c, 0x71, 0x77, 0xc3, 0x61, 0x96, 0xa4,
	0x9d, 0x99, 0x9a, 0xf1, 0xd8, 0x74, 0xee, 0xfb, 0x39, 0xb6, 0xc2, 0x21, 0x55, 0x15, 0x0e, 0x89,
	0xf1, 0x9e, 0x8f, 0x09, 0x94, 0xf7, 0x79, 0xf9, 0xbf, 0x38, 0xb5, 0xfc, 0xaf, 0xc9, 0xe5, 0xff,
	0x1f, 0xa4, 0x65, 0xb9, 0x74, 0x5a, 0xe3, 0x3d, 0x00, 0x86, 0x90, 0xe4, 0x5d, 0xa9, 0xb2, 0xac,
	0x4a, 0xca, 0x22, 0xa9, 0xc4, 0xc1, 0x52, 0x8a, 0x9e, 0x3c, 0x5e, 0xe0, 0xaf, 0x28, 0x3a, 0xff,
	0x52, 0x08, 0xd0, 0xd5, 0x21, 0x00, 0x3d, 0x49, 0xcb, 0x04, 0x59, 0x0e, 0x38, 0x22, 0x15, 0x8b,
	0x1f, 0x0e, 0xad, 0xf2, 0xfd, 0x85, 0xba, 0x1f, 0x0e, 0x69, 0x51, 0x6c, 0x7c, 0x03, 0x6a, 0x64,
	0x4b, 0x51, 0x2c, 0x95, 0x4b, 0x5e, 0xf9, 0xcd, 0x8b, 0x7e, 0x38, 0x24, 0xfe, 0xec, 0x8f, 0x34,
	0xb8, 0x7c, 0x28, 0x2c, 0x0b, 0x6d, 0xec, 0x5c, 0x72, 0x9a, 0x4a, 0xab, 0x66, 0xea, 0xff, 0xa9,
	0x13, 0x68, 0xf2, 0x78, 0xec, 0xe2, 0xc8, 0x22, 0x9a, 0x59, 0xe5, 0x35, 0x83, 0x50, 0xef, 0xe1,
	0x21, 0xea, 0x42, 0x47, 0x8d, 0x0d, 0x47, 0xe8, 0x17, 0xb2, 0x7e, 0xd3, 0xcb, 0xf0, 0x8d, 0xe8,
	0x02, 0x18, 0xf2, 0xdb, 0x71, 0xf4, 0xc6, 0x31, 0x18, 0x3b, 0x19, 0xe1, 0x16, 0x9e, 0x04, 0x03,
	0x9a, 0x4f, 0xac, 0x42, 0xb7, 0xa0, 0x4e, 0x02, 0x3c, 0xb0, 0x38, 0x7e, 0xb7, 0x35, 0x63, 0x0d,
	0xae, 0xc8, 0xeb, 0x9c, 0x36, 0xb5, 0x2b, 0x6f, 0xc4, 0xb0, 0xb2, 0xc3, 0x93, 0x0e, 0xb3, 0x50,
	0xdc, 0x51, 0x90, 0xad, 0xfb, 0x61, 0xe0, 0x66, 0x6f, 0x2d, 0xaf, 0x1e, 0x8e, 0x07, 0x03, 0x17,
	0xe3, 0x76, 0x65, 0xda, 0xe3, 0x1f, 0xd8, 0x9e, 0xdf, 0xd6, 0xdf, 0xfe, 0x6a, 0x1d, 0x84, 0x5b,
	0xc6, 0xc6, 0xdf, 0x6b, 0x00, 0xc5, 0xf5, 0x36, 0x43, 0xba, 0xbb, 0x20, 0x5c, 0xcc, 0xeb, 0xbe,
	0x3a, 0x7d, 0x11, 0x47, 0x68, 0xf4, 0x6b, 0x9f, 0x3f, 0xd5, 0xb5, 0xdf, 0xfa, 0xfc, 0xa9, 0xbe,
	0x3c, 0xee, 0x0d, 0x7b, 0xb8, 0x37, 0xea, 0xd9, 0x3d, 0xb7, 0x17, 0xf4, 0x7e, 0xe7, 0xf3, 0xa7,
	0xfa, 0xfe, 0xe6, 0xd8, 0xec, 0x8f, 0x3d, 0x67, 0xcb, 0xdc, 0x1c, 0x9a, 0x7d, 0x26, 0x96, 0x2d,
	0x73, 0x13, 0x9b, 0xfd, 0xb4, 0xb6, 0xde, 0x32, 0x37, 0x47, 0x66, 0x3f, 0xd5, 0x90, 0x2d, 0x73,
	0xd3, 0x36, 0xfb, 0x69, 0xcc, 0xd9, 0x32, 0x37, 0x5d, 0xb3, 0xcf, 0x82, 0xc5, 0x96, 0xb9, 0x19,
	0x98, 0xfd, 0xc0, 0x1e, 0xb9, 0x5b, 0xc6, 0x8f, 0x34, 0x80, 0xa2, 0x85, 0x2e, 0x03, 0x17, 0x6e,
	0xde, 0xc9, 0xc0, 0xc5, 0x7b, 0x64, 0x68, 0x40, 0x80, 0x57, 0x08, 0xf0, 0x66, 0x0a, 0x3c, 0xea,
	0x25, 0xbd, 0x11, 0x85, 0xfd, 0xe1, 0x1c, 0xb0, 0x23, 0xb3, 0x1f, 0xd1, 0x1d, 0x89, 0xd9, 0xcf,
	0x32, 0x78, 0x92, 0x85, 0x0a, 0x27, 0x32, 0x7e, 0xa8, 0x41, 0x4b, 0xb8, 0xa0, 0x64, 0x48, 0xae,
	0x46, 0xbe, 0x94, 0xd5, 0x5d, 0x3b, 0x75, 0x1d, 0x47, 0x68, 0x9f, 0xe0, 0xd6, 0x09, 0x6e, 0x20,
	0xb8, 0x4f, 0x7a, 0x1e, 0x63, 0xf6, 0xbb, 0x0a, 0xd4, 0x77, 0xb6, 0xcc, 0xcd, 0x13, 0xb3, 0x9f,
	0xdd, 0xe2, 0xda, 0x32, 0x37, 0x3d, 0xb3, 0x4f, 0xdd, 0x66, 0xca, 0xd4, 0x41, 0x90, 0x6c, 0x19,
	0xdf, 0x4b, 0xb3, 0x38, 0xf9, 0xa6, 0x8b, 0x71, 0xad, 0xc4, 0x3f, 0xc5, 0xdd, 0x9e, 0xee, 0xf5,
	0x39, 0x76, 0xe1, 0x08, 0x6d, 0x10, 0xd8, 0x55, 0x02, 0xbb, 0x4a, 0x60, 0x13, 0xc0, 0x17, 0x95,
	0x80, 0x8d, 0xdf, 0xd5, 0xa0, 0xc1, 0x5d, 0x55, 0x31, 0x24, 0x31, 0x8a, 0x97, 0x67, 0xba, 0xaf,
	0x9d, 0xb2, 0x8a, 0x23, 0xf4, 0x21, 0xf9, 0xec, 0x02, 0xf9, 0x6c, 0x9d, 0x7c, 0xd6, 0xed, 0x61,
	0xfa, 0xe9, 0x9f, 0x29, 0x7f, 0xda, 0xf2, 0x44, 0xad, 0xcb, 0x25, 0xce, 0x44, 0xf9, 0x97, 0x1a,
	0x5c, 0x50, 0x0d, 0xd1, 0x8d, 0xeb, 0xca, 0x19, 0xb8, 0x3c, 0xd9, 0xef, 0xde, 0x98, 0x67, 0x1b,
	0x8e, 0xd0, 0x2d, 0x82, 0x78, 0x31, 0x47, 0x9c, 0x49, 0xf7, 0x8d, 0x69, 0x88, 0x15, 0x02, 0xfd,
	0x17, 0x0d, 0x56, 0x14, 0xa3, 0x30, 0x59, 0xa0, 0xea, 0x79, 0xa6, 0x2c, 0xd0, 0x69, 0x33, 0xb5,
	0x5f, 0x22, 0x38, 0x6b, 0xcc, 0xf0, 0xa3, 0x5e, 0xd8, 0x1b, 0xf5, 0x02, 0xca, 0x61, 0x82, 0xf6,
	0xde, 0x34, 0xb4, 0xb9, 0xd9, 0x84, 0x66, 0x3f, 0x9f, 0x77, 0xa6, 0x36, 0x43, 0x13, 0x47, 0x66,
	0xeb, 0xe3, 0x11, 0x95, 0x45, 0x96, 0x2d, 0xfe, 0x50, 0x83, 0x06, 0x37, 0x82, 0x97, 0x55, 0x42,
	0xbc, 0x55, 0x20, 0xab, 0x84, 0x34, 0xbb, 0x47, 0x1f, 0x11, 0xe0, 0x75, 0x66, 0x40, 0x51, 0x6f,
	0x98, 0x9b, 0xfd, 0x7b, 0x05, 0xe8, 0x1c, 0xa6, 0x88, 0x3e, 0x31, 0xfb, 0xe9, 0xb5, 0x81, 0x64,
	0xc2, 0x01, 0x1f, 0x85, 0x81, 0x3b, 0xd9, 0x32, 0x7e, 0xa0, 0x41, 0x93, 0x1f, 0x6e, 0x1b, 0x12,
	0x0e, 0x69, 0x0e, 0xdf, 0x5d, 0x3d, 0x6d, 0x19, 0x47, 0xe8, 0x7d, 0x82, 0x73, 0x89, 0x29, 0x02,
	0xc1, 0x99, 0x2a, 0xc2, 0xc6, 0x4c, 0x94, 0x8c, 0x81, 0xc6, 0x9f, 0x6b, 0xb4, 0x2c, 0x2a, 0x8d,
	0x1b, 0x8d, 0xeb, 0xd3, 0x1c, 0xa3, 0xd0, 0x3d, 0x95, 0x35, 0x76, 0xda, 0xe4, 0x12, 0xf5, 0x08,
	0x50, 0x20, 0x40, 0x17, 0xc7, 0x3d, 0xcc, 0x8c, 0xfb, 0x7a, 0x01, 0x33, 0x77, 0x9b, 0x56, 0x09,
	0xac, 0xf1, 0x37, 0x1a, 0xbd, 0xe0, 0xcb, 0x0f, 0xe8, 0xd6, 0xca, 0xe9, 0x8c, 0x30, 0xd6, 0xec,
	0x9a, 0xa7, 0x6f, 0x20, 0xe9, 0x02, 0x41, 0xd4, 0x60, 0x22, 0xb6, 0xa9, 0x88, 0x53, 0xbb, 0xbf,
	0x5d, 0xa0, 0x62, 0xb1, 0xc6, 0x52, 0x8b, 0x39, 0xef, 0x83, 0x0b, 0xf8, 0x29, 0x81, 0x78, 0x82,
	0x65, 0x71, 0xae, 0xa0, 0xc0, 0x2c, 0x4e, 0x53, 0xba, 0x33, 0xc7, 0x12, 0xe8, 0x90, 0x60, 0x6e,
	0x72, 0x6a, 0x99, 0x59, 0x7e, 0x4f, 0x14, 0x78, 0xde, 0x75, 0x9b, 0xe1, 0x09, 0xa8, 0x0a, 0xfc,
	0x48, 0x2b, 0x2e, 0x14, 0xcb, 0xb3, 0xb0, 0x0d, 0x11, 0xd2, 0xf4, 0x51, 0x50, 0xf7, 0xe6, 0x9c,
	0x3b, 0x71, 0x84, 0xfa, 0xe4, 0x14, 0x2d, 0xa6, 0x0b, 0x11, 0xd3, 0x85, 0xd7, 0xe7, 0x3c, 0x81,
	0xf1, 0x57, 0x1a, 0xb4, 0xe5, 0x5e, 0xbf, 0x71, 0xf5, 0xf4, 0x59, 0x00, 0x01, 0x88, 0x66, 0x6d,
	0xc1, 0x11, 0xba, 0x4f, 0x90, 0x2d, 0x33, 0x73, 0xc2, 0xbd, 0x84, 0x61, 0x7b, 0x57, 0xa5, 0xa7,
	0x4c, 0xf0, 0x92, 0x1e, 0x88, 0x58, 0xff, 0x9a, 0x95, 0x17, 0x62, 0xaf, 0xdf, 0x58, 0x2f, 0x2b,
	0x67, 0x69, 0x2e, 0xd1, 0xbd, 0x36, 0x7b, 0x13, 0x8e, 0xd0, 0xcf, 0x13, 0xc4, 0xe7, 0x38, 0xc4,
	0xa9, 0x9b, 0xfa, 0x59, 0x19, 0x71, 0x66, 0x58, 0x89, 0x8c, 0x9f, 0x4b, 0x44, 0xfe, 0x4c, 0x83,
	0x15, 0xc5, 0xcd, 0x72, 0x39, 0x24, 0xa8, 0xef, 0xbf, 0xcb, 0x21, 0x61, 0xca, 0x15, 0x75, 0xf4,
	0x4d, 0x02, 0xb8, 0xcd, 0x84, 0x3f, 0xec, 0x45, 0x14, 0xee, 0x8d, 0xd3, 0x42, 0x41, 0xa1, 0x0a,
	0xc6, 0x9f, 0x68, 0x60, 0x94, 0x2f, 0x57, 0xca, 0xfc, 0x54, 0xde, 0x02, 0xed, 0x5e, 0x9b, 0xbd,
	0x29, 0x83, 0x77, 0x3e, 0x87, 0xe7, 0x9e, 0x0e, 0xaf, 0xc8, 0x04, 0x72, 0x78, 0xe5, 0x66, 0xb6,
	0x42, 0xdc, 0xe5, 0x2e, 0xbb, 0x42, 0xdc, 0x8a, 0x9e, 0x78, 0x0a, 0xcf, 0x78, 0x4e, 0xee, 0x7d,
	0x5f, 0x83, 0x96, 0x30, 0x91, 0x97, 0x13, 0x4d, 0xf9, 0x82, 0x80, 0x9c, 0x68, 0x96, 0xc7, 0xf9,
	0x14, 0xcf, 0xca, 0x73, 0xe2, 0xf9, 0xfd, 0xf4, 0x8f, 0x29, 0x14, 0x6d, 0x6f, 0xe3, 0xf5, 0x92,
	0x32, 0xa9, 0xdb, 0xf7, 0xdd, 0x8d, 0xf9, 0x36, 0xe2, 0x08, 0xbd, 0x41, 0xa0, 0x5e, 0x10, 0x92,
	0xcb, 0xcb, 0x53, 0x80, 0x1a, 0xbf, 0xa7, 0x41, 0x5b, 0x6e, 0x16, 0xca, 0x3e, 0x46, 0xd1, 0xd3,
	0x94, 0x7d, 0x8c, 0xaa, 0xdf, 0x98, 0xb2, 0x6c, 0xf5, 0x39, 0x59, 0xf6, 0xcf, 0x1a, 0xb4, 0xe5,
	0xee, 0xa0, 0x0c, 0x4c, 0xd1, 0x8d, 0x94, 0x81, 0x29, 0x1b, 0x8c, 0x21, 0x01, 0xb6, 0x96, 0x17,
	0x3b, 0xa4, 0xd4, 0xb1, 0x59, 0x48, 0x3c, 0x9a, 0x0f, 0x1e, 0x75, 0x2f, 0x5c, 0x77, 0x5f, 0x8e,
	0xa0, 0xd8, 0xec, 0x17, 0x4d, 0xfd, 0x2d, 0xe3, 0x9f, 0x34, 0x30, 0xca, 0xad, 0x3a, 0xd9, 0x5c,
	0x94, 0x3d, 0x4f, 0xd9, 0x5c, 0xd4, 0x1d, 0x3f, 0xf4, 0x90, 0x1c, 0xc9, 0x54, 0x1e, 0x69, 0xf7,
	0x19, 0x8e, 0x34, 0xed, 0x30, 0x34, 0x7c, 0xfe, 0x40, 0x83, 0x65, 0xb1, 0x33, 0x60, 0x28, 0xcd,
	0x86, 0x47, 0x6f, 0x9e, 0xbe, 0x01, 0x47, 0xe8, 0x1b, 0x04, 0xf9, 0x55, 0x82, 0x7c, 0x81, 0x20,
	0x4f, 0xa4, 0x74, 0xa9, 0x94, 0x8b, 0xe4, 0x0d, 0x8c, 0x2d, 0xe3, 0x1f, 0xd5, 0x3d, 0xad, 0xf5,
	0x99, 0x1d, 0x20, 0x75, 0xd0, 0x29, 0x37, 0xa0, 0xd0, 0x2f, 0x12, 0x70, 0x28, 0x67, 0x2b, 0x49,
	0x9c, 0x1e, 0x32, 0x57, 0xb9, 0x3d, 0x05, 0xa3, 0xc0, 0x47, 0xcc, 0x95, 0xf3, 0x0f, 0x8b, 0x84,
	0xc4, 0x65, 0xa5, 0xc9, 0x67, 0xa4, 0x8a, 0x52, 0x74, 0x79, 0x4a, 0x55, 0x94, 0xba, 0x4b, 0x55,
	0xaa, 0xa2, 0xa6, 0x35, 0x8c, 0x28, 0x8f, 0xd7, 0x99, 0x25, 0x9e, 0x30, 0xbd, 0xb8, 0x56, 0x1c,
	0xe0, 0xc4, 0xec, 0x67, 0xdd, 0x2e, 0x72, 0x00, 0x0e, 0xb5, 0xf1, 0x63, 0x0d, 0x1a, 0x3b, 0x8e,
	0x97, 0x9c, 0x55, 0x73, 0xe4, 0xda, 0xff, 0x57, 0x73, 0xa4, 0xbb, 0xf8, 0xe9, 0xe7, 0x4f, 0xf5,
	0x4f, 0x27, 0xdb, 0xed, 0x9f, 0x7e, 0xb9, 0xaa, 0xfd, 0xeb, 0x97, 0xab, 0xda, 0x7f, 0x7c, 0xb9,
	0xaa, 0xfd, 0xf6, 0x7f, 0xae, 0x7e, 0xed, 0xff, 0x02, 0x00, 0x00, 0xff, 0xff, 0x18, 0x2b, 0xf3,
	0x58, 0x8e, 0x3e, 0x00, 0x00,
}
