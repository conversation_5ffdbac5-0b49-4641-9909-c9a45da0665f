// Code generated by protoc-gen-go. DO NOT EDIT.
// source: gameradar/gameradar.proto

package gameradar // import "golang.52tt.com/protocol/services/gameradar"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PatternType int32

const (
	PatternType_PATTERN_DEFAULT      PatternType = 0
	PatternType_PATTERN_OPTION       PatternType = 1
	PatternType_PATTERN_MULTI_OPTION PatternType = 2
	PatternType_PATTERN_INPUT        PatternType = 3
)

var PatternType_name = map[int32]string{
	0: "PATTERN_DEFAULT",
	1: "PATTERN_OPTION",
	2: "PATTERN_MULTI_OPTION",
	3: "PATTERN_INPUT",
}
var PatternType_value = map[string]int32{
	"PATTERN_DEFAULT":      0,
	"PATTERN_OPTION":       1,
	"PATTERN_MULTI_OPTION": 2,
	"PATTERN_INPUT":        3,
}

func (x PatternType) String() string {
	return proto.EnumName(PatternType_name, int32(x))
}
func (PatternType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{0}
}

// Mode 是 Block 的选择模式
type Block_Mode int32

const (
	Block_SINGLE Block_Mode = 0
	Block_MULTI  Block_Mode = 1
)

var Block_Mode_name = map[int32]string{
	0: "SINGLE",
	1: "MULTI",
}
var Block_Mode_value = map[string]int32{
	"SINGLE": 0,
	"MULTI":  1,
}

func (x Block_Mode) String() string {
	return proto.EnumName(Block_Mode_name, int32(x))
}
func (Block_Mode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{5, 0}
}

type TabsReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TabsReq) Reset()         { *m = TabsReq{} }
func (m *TabsReq) String() string { return proto.CompactTextString(m) }
func (*TabsReq) ProtoMessage()    {}
func (*TabsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{0}
}
func (m *TabsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TabsReq.Unmarshal(m, b)
}
func (m *TabsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TabsReq.Marshal(b, m, deterministic)
}
func (dst *TabsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabsReq.Merge(dst, src)
}
func (m *TabsReq) XXX_Size() int {
	return xxx_messageInfo_TabsReq.Size(m)
}
func (m *TabsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TabsReq.DiscardUnknown(m)
}

var xxx_messageInfo_TabsReq proto.InternalMessageInfo

type TabsResp struct {
	Tabs                 []*Tab   `protobuf:"bytes,1,rep,name=tabs,proto3" json:"tabs,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TabsResp) Reset()         { *m = TabsResp{} }
func (m *TabsResp) String() string { return proto.CompactTextString(m) }
func (*TabsResp) ProtoMessage()    {}
func (*TabsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{1}
}
func (m *TabsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TabsResp.Unmarshal(m, b)
}
func (m *TabsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TabsResp.Marshal(b, m, deterministic)
}
func (dst *TabsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabsResp.Merge(dst, src)
}
func (m *TabsResp) XXX_Size() int {
	return xxx_messageInfo_TabsResp.Size(m)
}
func (m *TabsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TabsResp.DiscardUnknown(m)
}

var xxx_messageInfo_TabsResp proto.InternalMessageInfo

func (m *TabsResp) GetTabs() []*Tab {
	if m != nil {
		return m.Tabs
	}
	return nil
}

type Tab struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	TagId                uint32   `protobuf:"varint,3,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Tab) Reset()         { *m = Tab{} }
func (m *Tab) String() string { return proto.CompactTextString(m) }
func (*Tab) ProtoMessage()    {}
func (*Tab) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{2}
}
func (m *Tab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Tab.Unmarshal(m, b)
}
func (m *Tab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Tab.Marshal(b, m, deterministic)
}
func (dst *Tab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Tab.Merge(dst, src)
}
func (m *Tab) XXX_Size() int {
	return xxx_messageInfo_Tab.Size(m)
}
func (m *Tab) XXX_DiscardUnknown() {
	xxx_messageInfo_Tab.DiscardUnknown(m)
}

var xxx_messageInfo_Tab proto.InternalMessageInfo

func (m *Tab) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Tab) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *Tab) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

type BlocksReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TagId                uint32   `protobuf:"varint,2,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlocksReq) Reset()         { *m = BlocksReq{} }
func (m *BlocksReq) String() string { return proto.CompactTextString(m) }
func (*BlocksReq) ProtoMessage()    {}
func (*BlocksReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{3}
}
func (m *BlocksReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlocksReq.Unmarshal(m, b)
}
func (m *BlocksReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlocksReq.Marshal(b, m, deterministic)
}
func (dst *BlocksReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlocksReq.Merge(dst, src)
}
func (m *BlocksReq) XXX_Size() int {
	return xxx_messageInfo_BlocksReq.Size(m)
}
func (m *BlocksReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BlocksReq.DiscardUnknown(m)
}

var xxx_messageInfo_BlocksReq proto.InternalMessageInfo

func (m *BlocksReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *BlocksReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

type BlocksResp struct {
	Blocks               []*Block `protobuf:"bytes,1,rep,name=blocks,proto3" json:"blocks,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlocksResp) Reset()         { *m = BlocksResp{} }
func (m *BlocksResp) String() string { return proto.CompactTextString(m) }
func (*BlocksResp) ProtoMessage()    {}
func (*BlocksResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{4}
}
func (m *BlocksResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlocksResp.Unmarshal(m, b)
}
func (m *BlocksResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlocksResp.Marshal(b, m, deterministic)
}
func (dst *BlocksResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlocksResp.Merge(dst, src)
}
func (m *BlocksResp) XXX_Size() int {
	return xxx_messageInfo_BlocksResp.Size(m)
}
func (m *BlocksResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BlocksResp.DiscardUnknown(m)
}

var xxx_messageInfo_BlocksResp proto.InternalMessageInfo

func (m *BlocksResp) GetBlocks() []*Block {
	if m != nil {
		return m.Blocks
	}
	return nil
}

// Block 栏目，由多个元素构成
type Block struct {
	Id                   uint32     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string     `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Mode                 Block_Mode `protobuf:"varint,3,opt,name=mode,proto3,enum=gameradar.Block_Mode" json:"mode,omitempty"`
	Elems                []*Elem    `protobuf:"bytes,4,rep,name=elems,proto3" json:"elems,omitempty"`
	BindId               uint32     `protobuf:"varint,5,opt,name=bind_id,json=bindId,proto3" json:"bind_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *Block) Reset()         { *m = Block{} }
func (m *Block) String() string { return proto.CompactTextString(m) }
func (*Block) ProtoMessage()    {}
func (*Block) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{5}
}
func (m *Block) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Block.Unmarshal(m, b)
}
func (m *Block) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Block.Marshal(b, m, deterministic)
}
func (dst *Block) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Block.Merge(dst, src)
}
func (m *Block) XXX_Size() int {
	return xxx_messageInfo_Block.Size(m)
}
func (m *Block) XXX_DiscardUnknown() {
	xxx_messageInfo_Block.DiscardUnknown(m)
}

var xxx_messageInfo_Block proto.InternalMessageInfo

func (m *Block) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Block) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *Block) GetMode() Block_Mode {
	if m != nil {
		return m.Mode
	}
	return Block_SINGLE
}

func (m *Block) GetElems() []*Elem {
	if m != nil {
		return m.Elems
	}
	return nil
}

func (m *Block) GetBindId() uint32 {
	if m != nil {
		return m.BindId
	}
	return 0
}

// Elem 元素，多个元素构成一个栏目
type Elem struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Elem) Reset()         { *m = Elem{} }
func (m *Elem) String() string { return proto.CompactTextString(m) }
func (*Elem) ProtoMessage()    {}
func (*Elem) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{6}
}
func (m *Elem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Elem.Unmarshal(m, b)
}
func (m *Elem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Elem.Marshal(b, m, deterministic)
}
func (dst *Elem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Elem.Merge(dst, src)
}
func (m *Elem) XXX_Size() int {
	return xxx_messageInfo_Elem.Size(m)
}
func (m *Elem) XXX_DiscardUnknown() {
	xxx_messageInfo_Elem.DiscardUnknown(m)
}

var xxx_messageInfo_Elem proto.InternalMessageInfo

func (m *Elem) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Elem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type GetRadarIconConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRadarIconConfigReq) Reset()         { *m = GetRadarIconConfigReq{} }
func (m *GetRadarIconConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetRadarIconConfigReq) ProtoMessage()    {}
func (*GetRadarIconConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{7}
}
func (m *GetRadarIconConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRadarIconConfigReq.Unmarshal(m, b)
}
func (m *GetRadarIconConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRadarIconConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetRadarIconConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRadarIconConfigReq.Merge(dst, src)
}
func (m *GetRadarIconConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetRadarIconConfigReq.Size(m)
}
func (m *GetRadarIconConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRadarIconConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRadarIconConfigReq proto.InternalMessageInfo

type GetRadarIconConfigResp struct {
	GameIcon             map[string]string                        `protobuf:"bytes,1,rep,name=game_icon,json=gameIcon,proto3" json:"game_icon,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	RoleIcon             map[string]string                        `protobuf:"bytes,2,rep,name=role_icon,json=roleIcon,proto3" json:"role_icon,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	HaveInvitedMeIcon    map[string]string                        `protobuf:"bytes,3,rep,name=have_invited_me_icon,json=haveInvitedMeIcon,proto3" json:"have_invited_me_icon,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	CardTheme            map[string]*GetRadarIconConfigResp_Theme `protobuf:"bytes,4,rep,name=card_theme,json=cardTheme,proto3" json:"card_theme,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	GameTitleColor       map[string]string                        `protobuf:"bytes,5,rep,name=game_title_color,json=gameTitleColor,proto3" json:"game_title_color,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	SubjectIcon          map[string]string                        `protobuf:"bytes,6,rep,name=subject_icon,json=subjectIcon,proto3" json:"subject_icon,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                 `json:"-"`
	XXX_unrecognized     []byte                                   `json:"-"`
	XXX_sizecache        int32                                    `json:"-"`
}

func (m *GetRadarIconConfigResp) Reset()         { *m = GetRadarIconConfigResp{} }
func (m *GetRadarIconConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetRadarIconConfigResp) ProtoMessage()    {}
func (*GetRadarIconConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{8}
}
func (m *GetRadarIconConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRadarIconConfigResp.Unmarshal(m, b)
}
func (m *GetRadarIconConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRadarIconConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetRadarIconConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRadarIconConfigResp.Merge(dst, src)
}
func (m *GetRadarIconConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetRadarIconConfigResp.Size(m)
}
func (m *GetRadarIconConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRadarIconConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRadarIconConfigResp proto.InternalMessageInfo

func (m *GetRadarIconConfigResp) GetGameIcon() map[string]string {
	if m != nil {
		return m.GameIcon
	}
	return nil
}

func (m *GetRadarIconConfigResp) GetRoleIcon() map[string]string {
	if m != nil {
		return m.RoleIcon
	}
	return nil
}

func (m *GetRadarIconConfigResp) GetHaveInvitedMeIcon() map[string]string {
	if m != nil {
		return m.HaveInvitedMeIcon
	}
	return nil
}

func (m *GetRadarIconConfigResp) GetCardTheme() map[string]*GetRadarIconConfigResp_Theme {
	if m != nil {
		return m.CardTheme
	}
	return nil
}

func (m *GetRadarIconConfigResp) GetGameTitleColor() map[string]string {
	if m != nil {
		return m.GameTitleColor
	}
	return nil
}

func (m *GetRadarIconConfigResp) GetSubjectIcon() map[string]string {
	if m != nil {
		return m.SubjectIcon
	}
	return nil
}

type GetRadarIconConfigResp_Theme struct {
	VerticalBg           string   `protobuf:"bytes,1,opt,name=vertical_bg,json=verticalBg,proto3" json:"vertical_bg,omitempty"`
	HorizontalBg         string   `protobuf:"bytes,2,opt,name=horizontal_bg,json=horizontalBg,proto3" json:"horizontal_bg,omitempty"`
	VerticalBgGradient   []string `protobuf:"bytes,3,rep,name=vertical_bg_gradient,json=verticalBgGradient,proto3" json:"vertical_bg_gradient,omitempty"`
	HorizontalBgGradient []string `protobuf:"bytes,4,rep,name=horizontal_bg_gradient,json=horizontalBgGradient,proto3" json:"horizontal_bg_gradient,omitempty"`
	DefaultColor         string   `protobuf:"bytes,5,opt,name=default_color,json=defaultColor,proto3" json:"default_color,omitempty"`
	SloganColor          string   `protobuf:"bytes,6,opt,name=slogan_color,json=sloganColor,proto3" json:"slogan_color,omitempty"`
	SubjectBg            string   `protobuf:"bytes,7,opt,name=subject_bg,json=subjectBg,proto3" json:"subject_bg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRadarIconConfigResp_Theme) Reset()         { *m = GetRadarIconConfigResp_Theme{} }
func (m *GetRadarIconConfigResp_Theme) String() string { return proto.CompactTextString(m) }
func (*GetRadarIconConfigResp_Theme) ProtoMessage()    {}
func (*GetRadarIconConfigResp_Theme) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{8, 6}
}
func (m *GetRadarIconConfigResp_Theme) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRadarIconConfigResp_Theme.Unmarshal(m, b)
}
func (m *GetRadarIconConfigResp_Theme) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRadarIconConfigResp_Theme.Marshal(b, m, deterministic)
}
func (dst *GetRadarIconConfigResp_Theme) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRadarIconConfigResp_Theme.Merge(dst, src)
}
func (m *GetRadarIconConfigResp_Theme) XXX_Size() int {
	return xxx_messageInfo_GetRadarIconConfigResp_Theme.Size(m)
}
func (m *GetRadarIconConfigResp_Theme) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRadarIconConfigResp_Theme.DiscardUnknown(m)
}

var xxx_messageInfo_GetRadarIconConfigResp_Theme proto.InternalMessageInfo

func (m *GetRadarIconConfigResp_Theme) GetVerticalBg() string {
	if m != nil {
		return m.VerticalBg
	}
	return ""
}

func (m *GetRadarIconConfigResp_Theme) GetHorizontalBg() string {
	if m != nil {
		return m.HorizontalBg
	}
	return ""
}

func (m *GetRadarIconConfigResp_Theme) GetVerticalBgGradient() []string {
	if m != nil {
		return m.VerticalBgGradient
	}
	return nil
}

func (m *GetRadarIconConfigResp_Theme) GetHorizontalBgGradient() []string {
	if m != nil {
		return m.HorizontalBgGradient
	}
	return nil
}

func (m *GetRadarIconConfigResp_Theme) GetDefaultColor() string {
	if m != nil {
		return m.DefaultColor
	}
	return ""
}

func (m *GetRadarIconConfigResp_Theme) GetSloganColor() string {
	if m != nil {
		return m.SloganColor
	}
	return ""
}

func (m *GetRadarIconConfigResp_Theme) GetSubjectBg() string {
	if m != nil {
		return m.SubjectBg
	}
	return ""
}

type InvitePlayReq struct {
	FromUid              uint32   `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	CardInfo             []byte   `protobuf:"bytes,3,opt,name=card_info,json=cardInfo,proto3" json:"card_info,omitempty"`
	TabName              string   `protobuf:"bytes,4,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InvitePlayReq) Reset()         { *m = InvitePlayReq{} }
func (m *InvitePlayReq) String() string { return proto.CompactTextString(m) }
func (*InvitePlayReq) ProtoMessage()    {}
func (*InvitePlayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{9}
}
func (m *InvitePlayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InvitePlayReq.Unmarshal(m, b)
}
func (m *InvitePlayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InvitePlayReq.Marshal(b, m, deterministic)
}
func (dst *InvitePlayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InvitePlayReq.Merge(dst, src)
}
func (m *InvitePlayReq) XXX_Size() int {
	return xxx_messageInfo_InvitePlayReq.Size(m)
}
func (m *InvitePlayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InvitePlayReq.DiscardUnknown(m)
}

var xxx_messageInfo_InvitePlayReq proto.InternalMessageInfo

func (m *InvitePlayReq) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *InvitePlayReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *InvitePlayReq) GetCardInfo() []byte {
	if m != nil {
		return m.CardInfo
	}
	return nil
}

func (m *InvitePlayReq) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

type InvitePlayRsp struct {
	HasInviteUids        []uint32          `protobuf:"varint,1,rep,packed,name=has_invite_uids,json=hasInviteUids,proto3" json:"has_invite_uids,omitempty"`
	ImMsgInfos           []*ImMsgInfo      `protobuf:"bytes,2,rep,name=im_msg_infos,json=imMsgInfos,proto3" json:"im_msg_infos,omitempty"`
	InviteIcon           map[string]string `protobuf:"bytes,3,rep,name=invite_icon,json=inviteIcon,proto3" json:"invite_icon,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	InviteTime           int64             `protobuf:"varint,4,opt,name=invite_time,json=inviteTime,proto3" json:"invite_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *InvitePlayRsp) Reset()         { *m = InvitePlayRsp{} }
func (m *InvitePlayRsp) String() string { return proto.CompactTextString(m) }
func (*InvitePlayRsp) ProtoMessage()    {}
func (*InvitePlayRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{10}
}
func (m *InvitePlayRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InvitePlayRsp.Unmarshal(m, b)
}
func (m *InvitePlayRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InvitePlayRsp.Marshal(b, m, deterministic)
}
func (dst *InvitePlayRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InvitePlayRsp.Merge(dst, src)
}
func (m *InvitePlayRsp) XXX_Size() int {
	return xxx_messageInfo_InvitePlayRsp.Size(m)
}
func (m *InvitePlayRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_InvitePlayRsp.DiscardUnknown(m)
}

var xxx_messageInfo_InvitePlayRsp proto.InternalMessageInfo

func (m *InvitePlayRsp) GetHasInviteUids() []uint32 {
	if m != nil {
		return m.HasInviteUids
	}
	return nil
}

func (m *InvitePlayRsp) GetImMsgInfos() []*ImMsgInfo {
	if m != nil {
		return m.ImMsgInfos
	}
	return nil
}

func (m *InvitePlayRsp) GetInviteIcon() map[string]string {
	if m != nil {
		return m.InviteIcon
	}
	return nil
}

func (m *InvitePlayRsp) GetInviteTime() int64 {
	if m != nil {
		return m.InviteTime
	}
	return 0
}

type InviteSucReq struct {
	FromUid              uint32   `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	Status               uint32   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InviteSucReq) Reset()         { *m = InviteSucReq{} }
func (m *InviteSucReq) String() string { return proto.CompactTextString(m) }
func (*InviteSucReq) ProtoMessage()    {}
func (*InviteSucReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{11}
}
func (m *InviteSucReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InviteSucReq.Unmarshal(m, b)
}
func (m *InviteSucReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InviteSucReq.Marshal(b, m, deterministic)
}
func (dst *InviteSucReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InviteSucReq.Merge(dst, src)
}
func (m *InviteSucReq) XXX_Size() int {
	return xxx_messageInfo_InviteSucReq.Size(m)
}
func (m *InviteSucReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InviteSucReq.DiscardUnknown(m)
}

var xxx_messageInfo_InviteSucReq proto.InternalMessageInfo

func (m *InviteSucReq) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *InviteSucReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *InviteSucReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type InviteSucRsp struct {
	TabName              string   `protobuf:"bytes,1,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InviteSucRsp) Reset()         { *m = InviteSucRsp{} }
func (m *InviteSucRsp) String() string { return proto.CompactTextString(m) }
func (*InviteSucRsp) ProtoMessage()    {}
func (*InviteSucRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{12}
}
func (m *InviteSucRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InviteSucRsp.Unmarshal(m, b)
}
func (m *InviteSucRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InviteSucRsp.Marshal(b, m, deterministic)
}
func (dst *InviteSucRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InviteSucRsp.Merge(dst, src)
}
func (m *InviteSucRsp) XXX_Size() int {
	return xxx_messageInfo_InviteSucRsp.Size(m)
}
func (m *InviteSucRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_InviteSucRsp.DiscardUnknown(m)
}

var xxx_messageInfo_InviteSucRsp proto.InternalMessageInfo

func (m *InviteSucRsp) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

type RadarDisplayInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CardInfo             []byte   `protobuf:"bytes,2,opt,name=card_info,json=cardInfo,proto3" json:"card_info,omitempty"`
	TabName              string   `protobuf:"bytes,3,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	InviteTime           int64    `protobuf:"varint,4,opt,name=invite_time,json=inviteTime,proto3" json:"invite_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RadarDisplayInfo) Reset()         { *m = RadarDisplayInfo{} }
func (m *RadarDisplayInfo) String() string { return proto.CompactTextString(m) }
func (*RadarDisplayInfo) ProtoMessage()    {}
func (*RadarDisplayInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{13}
}
func (m *RadarDisplayInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RadarDisplayInfo.Unmarshal(m, b)
}
func (m *RadarDisplayInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RadarDisplayInfo.Marshal(b, m, deterministic)
}
func (dst *RadarDisplayInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RadarDisplayInfo.Merge(dst, src)
}
func (m *RadarDisplayInfo) XXX_Size() int {
	return xxx_messageInfo_RadarDisplayInfo.Size(m)
}
func (m *RadarDisplayInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RadarDisplayInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RadarDisplayInfo proto.InternalMessageInfo

func (m *RadarDisplayInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RadarDisplayInfo) GetCardInfo() []byte {
	if m != nil {
		return m.CardInfo
	}
	return nil
}

func (m *RadarDisplayInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *RadarDisplayInfo) GetInviteTime() int64 {
	if m != nil {
		return m.InviteTime
	}
	return 0
}

type LoadMore struct {
	OffsetInviteTime     int64    `protobuf:"varint,1,opt,name=offset_invite_time,json=offsetInviteTime,proto3" json:"offset_invite_time,omitempty"`
	OffsetStatus         uint32   `protobuf:"varint,2,opt,name=offset_status,json=offsetStatus,proto3" json:"offset_status,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LoadMore) Reset()         { *m = LoadMore{} }
func (m *LoadMore) String() string { return proto.CompactTextString(m) }
func (*LoadMore) ProtoMessage()    {}
func (*LoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{14}
}
func (m *LoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LoadMore.Unmarshal(m, b)
}
func (m *LoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LoadMore.Marshal(b, m, deterministic)
}
func (dst *LoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LoadMore.Merge(dst, src)
}
func (m *LoadMore) XXX_Size() int {
	return xxx_messageInfo_LoadMore.Size(m)
}
func (m *LoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_LoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_LoadMore proto.InternalMessageInfo

func (m *LoadMore) GetOffsetInviteTime() int64 {
	if m != nil {
		return m.OffsetInviteTime
	}
	return 0
}

func (m *LoadMore) GetOffsetStatus() uint32 {
	if m != nil {
		return m.OffsetStatus
	}
	return 0
}

func (m *LoadMore) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type RadarDisplayReq struct {
	ToUid                uint32    `protobuf:"varint,1,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	LoadMore             *LoadMore `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *RadarDisplayReq) Reset()         { *m = RadarDisplayReq{} }
func (m *RadarDisplayReq) String() string { return proto.CompactTextString(m) }
func (*RadarDisplayReq) ProtoMessage()    {}
func (*RadarDisplayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{15}
}
func (m *RadarDisplayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RadarDisplayReq.Unmarshal(m, b)
}
func (m *RadarDisplayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RadarDisplayReq.Marshal(b, m, deterministic)
}
func (dst *RadarDisplayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RadarDisplayReq.Merge(dst, src)
}
func (m *RadarDisplayReq) XXX_Size() int {
	return xxx_messageInfo_RadarDisplayReq.Size(m)
}
func (m *RadarDisplayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RadarDisplayReq.DiscardUnknown(m)
}

var xxx_messageInfo_RadarDisplayReq proto.InternalMessageInfo

func (m *RadarDisplayReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *RadarDisplayReq) GetLoadMore() *LoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type ImMsgInfo struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	ImMsg                []string `protobuf:"bytes,2,rep,name=im_msg,json=imMsg,proto3" json:"im_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImMsgInfo) Reset()         { *m = ImMsgInfo{} }
func (m *ImMsgInfo) String() string { return proto.CompactTextString(m) }
func (*ImMsgInfo) ProtoMessage()    {}
func (*ImMsgInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{16}
}
func (m *ImMsgInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImMsgInfo.Unmarshal(m, b)
}
func (m *ImMsgInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImMsgInfo.Marshal(b, m, deterministic)
}
func (dst *ImMsgInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImMsgInfo.Merge(dst, src)
}
func (m *ImMsgInfo) XXX_Size() int {
	return xxx_messageInfo_ImMsgInfo.Size(m)
}
func (m *ImMsgInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ImMsgInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ImMsgInfo proto.InternalMessageInfo

func (m *ImMsgInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ImMsgInfo) GetImMsg() []string {
	if m != nil {
		return m.ImMsg
	}
	return nil
}

type RadarDisplayRsp struct {
	Info                 []*RadarDisplayInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	InviteCnt            uint32              `protobuf:"varint,2,opt,name=invite_cnt,json=inviteCnt,proto3" json:"invite_cnt,omitempty"`
	ImMsgInfos           []*ImMsgInfo        `protobuf:"bytes,3,rep,name=im_msg_infos,json=imMsgInfos,proto3" json:"im_msg_infos,omitempty"`
	CanLoadmore          bool                `protobuf:"varint,4,opt,name=can_loadmore,json=canLoadmore,proto3" json:"can_loadmore,omitempty"`
	MinLoadmoreTime      int64               `protobuf:"varint,5,opt,name=min_loadmore_time,json=minLoadmoreTime,proto3" json:"min_loadmore_time,omitempty"`
	OffsetStatus         uint32              `protobuf:"varint,6,opt,name=offset_status,json=offsetStatus,proto3" json:"offset_status,omitempty"`
	InviteIcon           map[string]string   `protobuf:"bytes,7,rep,name=invite_icon,json=inviteIcon,proto3" json:"invite_icon,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *RadarDisplayRsp) Reset()         { *m = RadarDisplayRsp{} }
func (m *RadarDisplayRsp) String() string { return proto.CompactTextString(m) }
func (*RadarDisplayRsp) ProtoMessage()    {}
func (*RadarDisplayRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{17}
}
func (m *RadarDisplayRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RadarDisplayRsp.Unmarshal(m, b)
}
func (m *RadarDisplayRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RadarDisplayRsp.Marshal(b, m, deterministic)
}
func (dst *RadarDisplayRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RadarDisplayRsp.Merge(dst, src)
}
func (m *RadarDisplayRsp) XXX_Size() int {
	return xxx_messageInfo_RadarDisplayRsp.Size(m)
}
func (m *RadarDisplayRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_RadarDisplayRsp.DiscardUnknown(m)
}

var xxx_messageInfo_RadarDisplayRsp proto.InternalMessageInfo

func (m *RadarDisplayRsp) GetInfo() []*RadarDisplayInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *RadarDisplayRsp) GetInviteCnt() uint32 {
	if m != nil {
		return m.InviteCnt
	}
	return 0
}

func (m *RadarDisplayRsp) GetImMsgInfos() []*ImMsgInfo {
	if m != nil {
		return m.ImMsgInfos
	}
	return nil
}

func (m *RadarDisplayRsp) GetCanLoadmore() bool {
	if m != nil {
		return m.CanLoadmore
	}
	return false
}

func (m *RadarDisplayRsp) GetMinLoadmoreTime() int64 {
	if m != nil {
		return m.MinLoadmoreTime
	}
	return 0
}

func (m *RadarDisplayRsp) GetOffsetStatus() uint32 {
	if m != nil {
		return m.OffsetStatus
	}
	return 0
}

func (m *RadarDisplayRsp) GetInviteIcon() map[string]string {
	if m != nil {
		return m.InviteIcon
	}
	return nil
}

type InviteInfoReq struct {
	ToUid                uint32   `protobuf:"varint,1,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	FromUid              uint32   `protobuf:"varint,2,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InviteInfoReq) Reset()         { *m = InviteInfoReq{} }
func (m *InviteInfoReq) String() string { return proto.CompactTextString(m) }
func (*InviteInfoReq) ProtoMessage()    {}
func (*InviteInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{18}
}
func (m *InviteInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InviteInfoReq.Unmarshal(m, b)
}
func (m *InviteInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InviteInfoReq.Marshal(b, m, deterministic)
}
func (dst *InviteInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InviteInfoReq.Merge(dst, src)
}
func (m *InviteInfoReq) XXX_Size() int {
	return xxx_messageInfo_InviteInfoReq.Size(m)
}
func (m *InviteInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InviteInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_InviteInfoReq proto.InternalMessageInfo

func (m *InviteInfoReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *InviteInfoReq) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

type InviteInfoRsp struct {
	CardInfo             []byte   `protobuf:"bytes,1,opt,name=card_info,json=cardInfo,proto3" json:"card_info,omitempty"`
	TabName              string   `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InviteInfoRsp) Reset()         { *m = InviteInfoRsp{} }
func (m *InviteInfoRsp) String() string { return proto.CompactTextString(m) }
func (*InviteInfoRsp) ProtoMessage()    {}
func (*InviteInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{19}
}
func (m *InviteInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InviteInfoRsp.Unmarshal(m, b)
}
func (m *InviteInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InviteInfoRsp.Marshal(b, m, deterministic)
}
func (dst *InviteInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InviteInfoRsp.Merge(dst, src)
}
func (m *InviteInfoRsp) XXX_Size() int {
	return xxx_messageInfo_InviteInfoRsp.Size(m)
}
func (m *InviteInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_InviteInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_InviteInfoRsp proto.InternalMessageInfo

func (m *InviteInfoRsp) GetCardInfo() []byte {
	if m != nil {
		return m.CardInfo
	}
	return nil
}

func (m *InviteInfoRsp) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

// 雷达状态 begin
type ModelInfo struct {
	ModelId              uint32     `protobuf:"varint,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	OptionIdList         []*Options `protobuf:"bytes,2,rep,name=option_id_list,json=optionIdList,proto3" json:"option_id_list,omitempty"`
	Text                 string     `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	ModelTitle           string     `protobuf:"bytes,4,opt,name=model_title,json=modelTitle,proto3" json:"model_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ModelInfo) Reset()         { *m = ModelInfo{} }
func (m *ModelInfo) String() string { return proto.CompactTextString(m) }
func (*ModelInfo) ProtoMessage()    {}
func (*ModelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{20}
}
func (m *ModelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModelInfo.Unmarshal(m, b)
}
func (m *ModelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModelInfo.Marshal(b, m, deterministic)
}
func (dst *ModelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModelInfo.Merge(dst, src)
}
func (m *ModelInfo) XXX_Size() int {
	return xxx_messageInfo_ModelInfo.Size(m)
}
func (m *ModelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ModelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ModelInfo proto.InternalMessageInfo

func (m *ModelInfo) GetModelId() uint32 {
	if m != nil {
		return m.ModelId
	}
	return 0
}

func (m *ModelInfo) GetOptionIdList() []*Options {
	if m != nil {
		return m.OptionIdList
	}
	return nil
}

func (m *ModelInfo) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *ModelInfo) GetModelTitle() string {
	if m != nil {
		return m.ModelTitle
	}
	return ""
}

type PublishItem struct {
	GameId               uint32       `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	IsCurrentPage        bool         `protobuf:"varint,2,opt,name=is_current_page,json=isCurrentPage,proto3" json:"is_current_page,omitempty"`
	ModelList            []*ModelInfo `protobuf:"bytes,3,rep,name=model_list,json=modelList,proto3" json:"model_list,omitempty"`
	GameTitle            string       `protobuf:"bytes,4,opt,name=game_title,json=gameTitle,proto3" json:"game_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PublishItem) Reset()         { *m = PublishItem{} }
func (m *PublishItem) String() string { return proto.CompactTextString(m) }
func (*PublishItem) ProtoMessage()    {}
func (*PublishItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{21}
}
func (m *PublishItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishItem.Unmarshal(m, b)
}
func (m *PublishItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishItem.Marshal(b, m, deterministic)
}
func (dst *PublishItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishItem.Merge(dst, src)
}
func (m *PublishItem) XXX_Size() int {
	return xxx_messageInfo_PublishItem.Size(m)
}
func (m *PublishItem) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishItem.DiscardUnknown(m)
}

var xxx_messageInfo_PublishItem proto.InternalMessageInfo

func (m *PublishItem) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *PublishItem) GetIsCurrentPage() bool {
	if m != nil {
		return m.IsCurrentPage
	}
	return false
}

func (m *PublishItem) GetModelList() []*ModelInfo {
	if m != nil {
		return m.ModelList
	}
	return nil
}

func (m *PublishItem) GetGameTitle() string {
	if m != nil {
		return m.GameTitle
	}
	return ""
}

type UserRadarItems struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemList             []*PublishItem `protobuf:"bytes,2,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	IsOpen               bool           `protobuf:"varint,3,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	RemainTime           uint32         `protobuf:"varint,4,opt,name=remain_time,json=remainTime,proto3" json:"remain_time,omitempty"`
	ConfigUpdated        bool           `protobuf:"varint,5,opt,name=config_updated,json=configUpdated,proto3" json:"config_updated,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserRadarItems) Reset()         { *m = UserRadarItems{} }
func (m *UserRadarItems) String() string { return proto.CompactTextString(m) }
func (*UserRadarItems) ProtoMessage()    {}
func (*UserRadarItems) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{22}
}
func (m *UserRadarItems) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRadarItems.Unmarshal(m, b)
}
func (m *UserRadarItems) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRadarItems.Marshal(b, m, deterministic)
}
func (dst *UserRadarItems) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRadarItems.Merge(dst, src)
}
func (m *UserRadarItems) XXX_Size() int {
	return xxx_messageInfo_UserRadarItems.Size(m)
}
func (m *UserRadarItems) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRadarItems.DiscardUnknown(m)
}

var xxx_messageInfo_UserRadarItems proto.InternalMessageInfo

func (m *UserRadarItems) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserRadarItems) GetItemList() []*PublishItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *UserRadarItems) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

func (m *UserRadarItems) GetRemainTime() uint32 {
	if m != nil {
		return m.RemainTime
	}
	return 0
}

func (m *UserRadarItems) GetConfigUpdated() bool {
	if m != nil {
		return m.ConfigUpdated
	}
	return false
}

// -------获取用户雷达状态-------
type GetUserRadarStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRadarStatusReq) Reset()         { *m = GetUserRadarStatusReq{} }
func (m *GetUserRadarStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetUserRadarStatusReq) ProtoMessage()    {}
func (*GetUserRadarStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{23}
}
func (m *GetUserRadarStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRadarStatusReq.Unmarshal(m, b)
}
func (m *GetUserRadarStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRadarStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetUserRadarStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRadarStatusReq.Merge(dst, src)
}
func (m *GetUserRadarStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetUserRadarStatusReq.Size(m)
}
func (m *GetUserRadarStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRadarStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRadarStatusReq proto.InternalMessageInfo

func (m *GetUserRadarStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserRadarStatusResp struct {
	Status               *UserRadarItems `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetUserRadarStatusResp) Reset()         { *m = GetUserRadarStatusResp{} }
func (m *GetUserRadarStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetUserRadarStatusResp) ProtoMessage()    {}
func (*GetUserRadarStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{24}
}
func (m *GetUserRadarStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRadarStatusResp.Unmarshal(m, b)
}
func (m *GetUserRadarStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRadarStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetUserRadarStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRadarStatusResp.Merge(dst, src)
}
func (m *GetUserRadarStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetUserRadarStatusResp.Size(m)
}
func (m *GetUserRadarStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRadarStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRadarStatusResp proto.InternalMessageInfo

func (m *GetUserRadarStatusResp) GetStatus() *UserRadarItems {
	if m != nil {
		return m.Status
	}
	return nil
}

type Options struct {
	OptionId             uint32   `protobuf:"varint,1,opt,name=option_id,json=optionId,proto3" json:"option_id,omitempty"`
	OptionContent        string   `protobuf:"bytes,2,opt,name=option_content,json=optionContent,proto3" json:"option_content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Options) Reset()         { *m = Options{} }
func (m *Options) String() string { return proto.CompactTextString(m) }
func (*Options) ProtoMessage()    {}
func (*Options) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{25}
}
func (m *Options) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Options.Unmarshal(m, b)
}
func (m *Options) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Options.Marshal(b, m, deterministic)
}
func (dst *Options) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Options.Merge(dst, src)
}
func (m *Options) XXX_Size() int {
	return xxx_messageInfo_Options.Size(m)
}
func (m *Options) XXX_DiscardUnknown() {
	xxx_messageInfo_Options.DiscardUnknown(m)
}

var xxx_messageInfo_Options proto.InternalMessageInfo

func (m *Options) GetOptionId() uint32 {
	if m != nil {
		return m.OptionId
	}
	return 0
}

func (m *Options) GetOptionContent() string {
	if m != nil {
		return m.OptionContent
	}
	return ""
}

type Models struct {
	ModelId              uint32     `protobuf:"varint,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ModelTitle           string     `protobuf:"bytes,2,opt,name=model_title,json=modelTitle,proto3" json:"model_title,omitempty"`
	Pattern              uint32     `protobuf:"varint,3,opt,name=pattern,proto3" json:"pattern,omitempty"`
	DefaultOptionId      uint32     `protobuf:"varint,4,opt,name=default_option_id,json=defaultOptionId,proto3" json:"default_option_id,omitempty"`
	OptionList           []*Options `protobuf:"bytes,5,rep,name=option_list,json=optionList,proto3" json:"option_list,omitempty"`
	Text                 []string   `protobuf:"bytes,6,rep,name=text,proto3" json:"text,omitempty"`
	UniqueOptionId       uint32     `protobuf:"varint,7,opt,name=unique_option_id,json=uniqueOptionId,proto3" json:"unique_option_id,omitempty"`
	MaxNumber            uint32     `protobuf:"varint,8,opt,name=max_number,json=maxNumber,proto3" json:"max_number,omitempty"`
	WrongText            string     `protobuf:"bytes,9,opt,name=wrong_text,json=wrongText,proto3" json:"wrong_text,omitempty"`
	DefaultText          string     `protobuf:"bytes,10,opt,name=default_text,json=defaultText,proto3" json:"default_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *Models) Reset()         { *m = Models{} }
func (m *Models) String() string { return proto.CompactTextString(m) }
func (*Models) ProtoMessage()    {}
func (*Models) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{26}
}
func (m *Models) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Models.Unmarshal(m, b)
}
func (m *Models) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Models.Marshal(b, m, deterministic)
}
func (dst *Models) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Models.Merge(dst, src)
}
func (m *Models) XXX_Size() int {
	return xxx_messageInfo_Models.Size(m)
}
func (m *Models) XXX_DiscardUnknown() {
	xxx_messageInfo_Models.DiscardUnknown(m)
}

var xxx_messageInfo_Models proto.InternalMessageInfo

func (m *Models) GetModelId() uint32 {
	if m != nil {
		return m.ModelId
	}
	return 0
}

func (m *Models) GetModelTitle() string {
	if m != nil {
		return m.ModelTitle
	}
	return ""
}

func (m *Models) GetPattern() uint32 {
	if m != nil {
		return m.Pattern
	}
	return 0
}

func (m *Models) GetDefaultOptionId() uint32 {
	if m != nil {
		return m.DefaultOptionId
	}
	return 0
}

func (m *Models) GetOptionList() []*Options {
	if m != nil {
		return m.OptionList
	}
	return nil
}

func (m *Models) GetText() []string {
	if m != nil {
		return m.Text
	}
	return nil
}

func (m *Models) GetUniqueOptionId() uint32 {
	if m != nil {
		return m.UniqueOptionId
	}
	return 0
}

func (m *Models) GetMaxNumber() uint32 {
	if m != nil {
		return m.MaxNumber
	}
	return 0
}

func (m *Models) GetWrongText() string {
	if m != nil {
		return m.WrongText
	}
	return ""
}

func (m *Models) GetDefaultText() string {
	if m != nil {
		return m.DefaultText
	}
	return ""
}

type Games struct {
	GameId               uint32    `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameTitle            string    `protobuf:"bytes,2,opt,name=game_title,json=gameTitle,proto3" json:"game_title,omitempty"`
	GameIconUrl          string    `protobuf:"bytes,3,opt,name=game_icon_url,json=gameIconUrl,proto3" json:"game_icon_url,omitempty"`
	ModelList            []*Models `protobuf:"bytes,4,rep,name=model_list,json=modelList,proto3" json:"model_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *Games) Reset()         { *m = Games{} }
func (m *Games) String() string { return proto.CompactTextString(m) }
func (*Games) ProtoMessage()    {}
func (*Games) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{27}
}
func (m *Games) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Games.Unmarshal(m, b)
}
func (m *Games) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Games.Marshal(b, m, deterministic)
}
func (dst *Games) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Games.Merge(dst, src)
}
func (m *Games) XXX_Size() int {
	return xxx_messageInfo_Games.Size(m)
}
func (m *Games) XXX_DiscardUnknown() {
	xxx_messageInfo_Games.DiscardUnknown(m)
}

var xxx_messageInfo_Games proto.InternalMessageInfo

func (m *Games) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *Games) GetGameTitle() string {
	if m != nil {
		return m.GameTitle
	}
	return ""
}

func (m *Games) GetGameIconUrl() string {
	if m != nil {
		return m.GameIconUrl
	}
	return ""
}

func (m *Games) GetModelList() []*Models {
	if m != nil {
		return m.ModelList
	}
	return nil
}

// -------获取雷达配置-------
type GetRadarConfigReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRadarConfigReq) Reset()         { *m = GetRadarConfigReq{} }
func (m *GetRadarConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetRadarConfigReq) ProtoMessage()    {}
func (*GetRadarConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{28}
}
func (m *GetRadarConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRadarConfigReq.Unmarshal(m, b)
}
func (m *GetRadarConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRadarConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetRadarConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRadarConfigReq.Merge(dst, src)
}
func (m *GetRadarConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetRadarConfigReq.Size(m)
}
func (m *GetRadarConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRadarConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRadarConfigReq proto.InternalMessageInfo

func (m *GetRadarConfigReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type RecommendNumConfig struct {
	RangeMin             uint32   `protobuf:"varint,1,opt,name=range_min,json=rangeMin,proto3" json:"range_min,omitempty"`
	RangeMax             uint32   `protobuf:"varint,2,opt,name=range_max,json=rangeMax,proto3" json:"range_max,omitempty"`
	TimerInterval        uint32   `protobuf:"varint,3,opt,name=timer_interval,json=timerInterval,proto3" json:"timer_interval,omitempty"`
	TimerMax             uint32   `protobuf:"varint,4,opt,name=timer_max,json=timerMax,proto3" json:"timer_max,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendNumConfig) Reset()         { *m = RecommendNumConfig{} }
func (m *RecommendNumConfig) String() string { return proto.CompactTextString(m) }
func (*RecommendNumConfig) ProtoMessage()    {}
func (*RecommendNumConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{29}
}
func (m *RecommendNumConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendNumConfig.Unmarshal(m, b)
}
func (m *RecommendNumConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendNumConfig.Marshal(b, m, deterministic)
}
func (dst *RecommendNumConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendNumConfig.Merge(dst, src)
}
func (m *RecommendNumConfig) XXX_Size() int {
	return xxx_messageInfo_RecommendNumConfig.Size(m)
}
func (m *RecommendNumConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendNumConfig.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendNumConfig proto.InternalMessageInfo

func (m *RecommendNumConfig) GetRangeMin() uint32 {
	if m != nil {
		return m.RangeMin
	}
	return 0
}

func (m *RecommendNumConfig) GetRangeMax() uint32 {
	if m != nil {
		return m.RangeMax
	}
	return 0
}

func (m *RecommendNumConfig) GetTimerInterval() uint32 {
	if m != nil {
		return m.TimerInterval
	}
	return 0
}

func (m *RecommendNumConfig) GetTimerMax() uint32 {
	if m != nil {
		return m.TimerMax
	}
	return 0
}

type GetRadarConfigResp struct {
	HeadTitle            string              `protobuf:"bytes,1,opt,name=head_title,json=headTitle,proto3" json:"head_title,omitempty"`
	FirstModelTitle      string              `protobuf:"bytes,2,opt,name=first_model_title,json=firstModelTitle,proto3" json:"first_model_title,omitempty"`
	GameList             []*Games            `protobuf:"bytes,3,rep,name=game_list,json=gameList,proto3" json:"game_list,omitempty"`
	RecommendNumConfig   *RecommendNumConfig `protobuf:"bytes,4,opt,name=recommend_num_config,json=recommendNumConfig,proto3" json:"recommend_num_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetRadarConfigResp) Reset()         { *m = GetRadarConfigResp{} }
func (m *GetRadarConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetRadarConfigResp) ProtoMessage()    {}
func (*GetRadarConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{30}
}
func (m *GetRadarConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRadarConfigResp.Unmarshal(m, b)
}
func (m *GetRadarConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRadarConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetRadarConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRadarConfigResp.Merge(dst, src)
}
func (m *GetRadarConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetRadarConfigResp.Size(m)
}
func (m *GetRadarConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRadarConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRadarConfigResp proto.InternalMessageInfo

func (m *GetRadarConfigResp) GetHeadTitle() string {
	if m != nil {
		return m.HeadTitle
	}
	return ""
}

func (m *GetRadarConfigResp) GetFirstModelTitle() string {
	if m != nil {
		return m.FirstModelTitle
	}
	return ""
}

func (m *GetRadarConfigResp) GetGameList() []*Games {
	if m != nil {
		return m.GameList
	}
	return nil
}

func (m *GetRadarConfigResp) GetRecommendNumConfig() *RecommendNumConfig {
	if m != nil {
		return m.RecommendNumConfig
	}
	return nil
}

// -------开启雷达-------
type StartRadarReq struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Item                 *PublishItem `protobuf:"bytes,2,opt,name=item,proto3" json:"item,omitempty"`
	IsQuickStart         bool         `protobuf:"varint,3,opt,name=is_quick_start,json=isQuickStart,proto3" json:"is_quick_start,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *StartRadarReq) Reset()         { *m = StartRadarReq{} }
func (m *StartRadarReq) String() string { return proto.CompactTextString(m) }
func (*StartRadarReq) ProtoMessage()    {}
func (*StartRadarReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{31}
}
func (m *StartRadarReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartRadarReq.Unmarshal(m, b)
}
func (m *StartRadarReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartRadarReq.Marshal(b, m, deterministic)
}
func (dst *StartRadarReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartRadarReq.Merge(dst, src)
}
func (m *StartRadarReq) XXX_Size() int {
	return xxx_messageInfo_StartRadarReq.Size(m)
}
func (m *StartRadarReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartRadarReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartRadarReq proto.InternalMessageInfo

func (m *StartRadarReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StartRadarReq) GetItem() *PublishItem {
	if m != nil {
		return m.Item
	}
	return nil
}

func (m *StartRadarReq) GetIsQuickStart() bool {
	if m != nil {
		return m.IsQuickStart
	}
	return false
}

type StartRadarResp struct {
	RemainTime           uint32    `protobuf:"varint,1,opt,name=remain_time,json=remainTime,proto3" json:"remain_time,omitempty"`
	Uid                  uint32    `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32    `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameName             string    `protobuf:"bytes,4,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	OpenTime             uint32    `protobuf:"varint,5,opt,name=open_time,json=openTime,proto3" json:"open_time,omitempty"`
	ModelList            []*Models `protobuf:"bytes,6,rep,name=model_list,json=modelList,proto3" json:"model_list,omitempty"`
	Duration             uint32    `protobuf:"varint,7,opt,name=duration,proto3" json:"duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *StartRadarResp) Reset()         { *m = StartRadarResp{} }
func (m *StartRadarResp) String() string { return proto.CompactTextString(m) }
func (*StartRadarResp) ProtoMessage()    {}
func (*StartRadarResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{32}
}
func (m *StartRadarResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartRadarResp.Unmarshal(m, b)
}
func (m *StartRadarResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartRadarResp.Marshal(b, m, deterministic)
}
func (dst *StartRadarResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartRadarResp.Merge(dst, src)
}
func (m *StartRadarResp) XXX_Size() int {
	return xxx_messageInfo_StartRadarResp.Size(m)
}
func (m *StartRadarResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartRadarResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartRadarResp proto.InternalMessageInfo

func (m *StartRadarResp) GetRemainTime() uint32 {
	if m != nil {
		return m.RemainTime
	}
	return 0
}

func (m *StartRadarResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StartRadarResp) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *StartRadarResp) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *StartRadarResp) GetOpenTime() uint32 {
	if m != nil {
		return m.OpenTime
	}
	return 0
}

func (m *StartRadarResp) GetModelList() []*Models {
	if m != nil {
		return m.ModelList
	}
	return nil
}

func (m *StartRadarResp) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

// -------关闭雷达-------
type StopRadarReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopRadarReq) Reset()         { *m = StopRadarReq{} }
func (m *StopRadarReq) String() string { return proto.CompactTextString(m) }
func (*StopRadarReq) ProtoMessage()    {}
func (*StopRadarReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{33}
}
func (m *StopRadarReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopRadarReq.Unmarshal(m, b)
}
func (m *StopRadarReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopRadarReq.Marshal(b, m, deterministic)
}
func (dst *StopRadarReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopRadarReq.Merge(dst, src)
}
func (m *StopRadarReq) XXX_Size() int {
	return xxx_messageInfo_StopRadarReq.Size(m)
}
func (m *StopRadarReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StopRadarReq.DiscardUnknown(m)
}

var xxx_messageInfo_StopRadarReq proto.InternalMessageInfo

func (m *StopRadarReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type StopRadarResp struct {
	Gameid               uint32   `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopRadarResp) Reset()         { *m = StopRadarResp{} }
func (m *StopRadarResp) String() string { return proto.CompactTextString(m) }
func (*StopRadarResp) ProtoMessage()    {}
func (*StopRadarResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{34}
}
func (m *StopRadarResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopRadarResp.Unmarshal(m, b)
}
func (m *StopRadarResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopRadarResp.Marshal(b, m, deterministic)
}
func (dst *StopRadarResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopRadarResp.Merge(dst, src)
}
func (m *StopRadarResp) XXX_Size() int {
	return xxx_messageInfo_StopRadarResp.Size(m)
}
func (m *StopRadarResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StopRadarResp.DiscardUnknown(m)
}

var xxx_messageInfo_StopRadarResp proto.InternalMessageInfo

func (m *StopRadarResp) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

// 批量获取用户雷达状态
type BatchGetUserRadarStatusReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserRadarStatusReq) Reset()         { *m = BatchGetUserRadarStatusReq{} }
func (m *BatchGetUserRadarStatusReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserRadarStatusReq) ProtoMessage()    {}
func (*BatchGetUserRadarStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{35}
}
func (m *BatchGetUserRadarStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserRadarStatusReq.Unmarshal(m, b)
}
func (m *BatchGetUserRadarStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserRadarStatusReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserRadarStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserRadarStatusReq.Merge(dst, src)
}
func (m *BatchGetUserRadarStatusReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserRadarStatusReq.Size(m)
}
func (m *BatchGetUserRadarStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserRadarStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserRadarStatusReq proto.InternalMessageInfo

func (m *BatchGetUserRadarStatusReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetUserRadarStatusResp struct {
	MapUserItem          map[uint32]*UserRadarItems `protobuf:"bytes,1,rep,name=map_user_item,json=mapUserItem,proto3" json:"map_user_item,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *BatchGetUserRadarStatusResp) Reset()         { *m = BatchGetUserRadarStatusResp{} }
func (m *BatchGetUserRadarStatusResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserRadarStatusResp) ProtoMessage()    {}
func (*BatchGetUserRadarStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{36}
}
func (m *BatchGetUserRadarStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserRadarStatusResp.Unmarshal(m, b)
}
func (m *BatchGetUserRadarStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserRadarStatusResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserRadarStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserRadarStatusResp.Merge(dst, src)
}
func (m *BatchGetUserRadarStatusResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserRadarStatusResp.Size(m)
}
func (m *BatchGetUserRadarStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserRadarStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserRadarStatusResp proto.InternalMessageInfo

func (m *BatchGetUserRadarStatusResp) GetMapUserItem() map[uint32]*UserRadarItems {
	if m != nil {
		return m.MapUserItem
	}
	return nil
}

// 获取总的开启雷达用户数，加上兜底数字
type GetTotalOpenUserNumReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTotalOpenUserNumReq) Reset()         { *m = GetTotalOpenUserNumReq{} }
func (m *GetTotalOpenUserNumReq) String() string { return proto.CompactTextString(m) }
func (*GetTotalOpenUserNumReq) ProtoMessage()    {}
func (*GetTotalOpenUserNumReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{37}
}
func (m *GetTotalOpenUserNumReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTotalOpenUserNumReq.Unmarshal(m, b)
}
func (m *GetTotalOpenUserNumReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTotalOpenUserNumReq.Marshal(b, m, deterministic)
}
func (dst *GetTotalOpenUserNumReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTotalOpenUserNumReq.Merge(dst, src)
}
func (m *GetTotalOpenUserNumReq) XXX_Size() int {
	return xxx_messageInfo_GetTotalOpenUserNumReq.Size(m)
}
func (m *GetTotalOpenUserNumReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTotalOpenUserNumReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTotalOpenUserNumReq proto.InternalMessageInfo

type GetTotalOpenUserNumResp struct {
	Num                  uint32   `protobuf:"varint,1,opt,name=num,proto3" json:"num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTotalOpenUserNumResp) Reset()         { *m = GetTotalOpenUserNumResp{} }
func (m *GetTotalOpenUserNumResp) String() string { return proto.CompactTextString(m) }
func (*GetTotalOpenUserNumResp) ProtoMessage()    {}
func (*GetTotalOpenUserNumResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{38}
}
func (m *GetTotalOpenUserNumResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTotalOpenUserNumResp.Unmarshal(m, b)
}
func (m *GetTotalOpenUserNumResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTotalOpenUserNumResp.Marshal(b, m, deterministic)
}
func (dst *GetTotalOpenUserNumResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTotalOpenUserNumResp.Merge(dst, src)
}
func (m *GetTotalOpenUserNumResp) XXX_Size() int {
	return xxx_messageInfo_GetTotalOpenUserNumResp.Size(m)
}
func (m *GetTotalOpenUserNumResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTotalOpenUserNumResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTotalOpenUserNumResp proto.InternalMessageInfo

func (m *GetTotalOpenUserNumResp) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

// 删除约玩记录
type DelInviteInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelInviteInfoReq) Reset()         { *m = DelInviteInfoReq{} }
func (m *DelInviteInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelInviteInfoReq) ProtoMessage()    {}
func (*DelInviteInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{39}
}
func (m *DelInviteInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelInviteInfoReq.Unmarshal(m, b)
}
func (m *DelInviteInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelInviteInfoReq.Marshal(b, m, deterministic)
}
func (dst *DelInviteInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelInviteInfoReq.Merge(dst, src)
}
func (m *DelInviteInfoReq) XXX_Size() int {
	return xxx_messageInfo_DelInviteInfoReq.Size(m)
}
func (m *DelInviteInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelInviteInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelInviteInfoReq proto.InternalMessageInfo

func (m *DelInviteInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type DelInviteInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelInviteInfoResp) Reset()         { *m = DelInviteInfoResp{} }
func (m *DelInviteInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelInviteInfoResp) ProtoMessage()    {}
func (*DelInviteInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{40}
}
func (m *DelInviteInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelInviteInfoResp.Unmarshal(m, b)
}
func (m *DelInviteInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelInviteInfoResp.Marshal(b, m, deterministic)
}
func (dst *DelInviteInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelInviteInfoResp.Merge(dst, src)
}
func (m *DelInviteInfoResp) XXX_Size() int {
	return xxx_messageInfo_DelInviteInfoResp.Size(m)
}
func (m *DelInviteInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelInviteInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelInviteInfoResp proto.InternalMessageInfo

// 房间下发需要 想玩模式 信息
type GetUserChannelPushInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserChannelPushInfoReq) Reset()         { *m = GetUserChannelPushInfoReq{} }
func (m *GetUserChannelPushInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserChannelPushInfoReq) ProtoMessage()    {}
func (*GetUserChannelPushInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{41}
}
func (m *GetUserChannelPushInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserChannelPushInfoReq.Unmarshal(m, b)
}
func (m *GetUserChannelPushInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserChannelPushInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserChannelPushInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserChannelPushInfoReq.Merge(dst, src)
}
func (m *GetUserChannelPushInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserChannelPushInfoReq.Size(m)
}
func (m *GetUserChannelPushInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserChannelPushInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserChannelPushInfoReq proto.InternalMessageInfo

func (m *GetUserChannelPushInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserChannelPushInfoResp struct {
	TagId                uint32   `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	GameName             string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	ModelName            string   `protobuf:"bytes,3,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	ModelId              uint32   `protobuf:"varint,4,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	RadarOpenStatus      bool     `protobuf:"varint,5,opt,name=radar_open_status,json=radarOpenStatus,proto3" json:"radar_open_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserChannelPushInfoResp) Reset()         { *m = GetUserChannelPushInfoResp{} }
func (m *GetUserChannelPushInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserChannelPushInfoResp) ProtoMessage()    {}
func (*GetUserChannelPushInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{42}
}
func (m *GetUserChannelPushInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserChannelPushInfoResp.Unmarshal(m, b)
}
func (m *GetUserChannelPushInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserChannelPushInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserChannelPushInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserChannelPushInfoResp.Merge(dst, src)
}
func (m *GetUserChannelPushInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserChannelPushInfoResp.Size(m)
}
func (m *GetUserChannelPushInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserChannelPushInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserChannelPushInfoResp proto.InternalMessageInfo

func (m *GetUserChannelPushInfoResp) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *GetUserChannelPushInfoResp) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GetUserChannelPushInfoResp) GetModelName() string {
	if m != nil {
		return m.ModelName
	}
	return ""
}

func (m *GetUserChannelPushInfoResp) GetModelId() uint32 {
	if m != nil {
		return m.ModelId
	}
	return 0
}

func (m *GetUserChannelPushInfoResp) GetRadarOpenStatus() bool {
	if m != nil {
		return m.RadarOpenStatus
	}
	return false
}

// 获取全部已开启雷达用户列表
type GetValidOpenRadarUserReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetValidOpenRadarUserReq) Reset()         { *m = GetValidOpenRadarUserReq{} }
func (m *GetValidOpenRadarUserReq) String() string { return proto.CompactTextString(m) }
func (*GetValidOpenRadarUserReq) ProtoMessage()    {}
func (*GetValidOpenRadarUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{43}
}
func (m *GetValidOpenRadarUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetValidOpenRadarUserReq.Unmarshal(m, b)
}
func (m *GetValidOpenRadarUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetValidOpenRadarUserReq.Marshal(b, m, deterministic)
}
func (dst *GetValidOpenRadarUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetValidOpenRadarUserReq.Merge(dst, src)
}
func (m *GetValidOpenRadarUserReq) XXX_Size() int {
	return xxx_messageInfo_GetValidOpenRadarUserReq.Size(m)
}
func (m *GetValidOpenRadarUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetValidOpenRadarUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetValidOpenRadarUserReq proto.InternalMessageInfo

func (m *GetValidOpenRadarUserReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetValidOpenRadarUserReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetValidOpenRadarUserResp struct {
	ItemList             []*UserRadarItems `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetValidOpenRadarUserResp) Reset()         { *m = GetValidOpenRadarUserResp{} }
func (m *GetValidOpenRadarUserResp) String() string { return proto.CompactTextString(m) }
func (*GetValidOpenRadarUserResp) ProtoMessage()    {}
func (*GetValidOpenRadarUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{44}
}
func (m *GetValidOpenRadarUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetValidOpenRadarUserResp.Unmarshal(m, b)
}
func (m *GetValidOpenRadarUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetValidOpenRadarUserResp.Marshal(b, m, deterministic)
}
func (dst *GetValidOpenRadarUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetValidOpenRadarUserResp.Merge(dst, src)
}
func (m *GetValidOpenRadarUserResp) XXX_Size() int {
	return xxx_messageInfo_GetValidOpenRadarUserResp.Size(m)
}
func (m *GetValidOpenRadarUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetValidOpenRadarUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetValidOpenRadarUserResp proto.InternalMessageInfo

func (m *GetValidOpenRadarUserResp) GetItemList() []*UserRadarItems {
	if m != nil {
		return m.ItemList
	}
	return nil
}

// 获取开启雷达状态的用户数
type GetAllUserInRadarOpeningReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllUserInRadarOpeningReq) Reset()         { *m = GetAllUserInRadarOpeningReq{} }
func (m *GetAllUserInRadarOpeningReq) String() string { return proto.CompactTextString(m) }
func (*GetAllUserInRadarOpeningReq) ProtoMessage()    {}
func (*GetAllUserInRadarOpeningReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{45}
}
func (m *GetAllUserInRadarOpeningReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllUserInRadarOpeningReq.Unmarshal(m, b)
}
func (m *GetAllUserInRadarOpeningReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllUserInRadarOpeningReq.Marshal(b, m, deterministic)
}
func (dst *GetAllUserInRadarOpeningReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllUserInRadarOpeningReq.Merge(dst, src)
}
func (m *GetAllUserInRadarOpeningReq) XXX_Size() int {
	return xxx_messageInfo_GetAllUserInRadarOpeningReq.Size(m)
}
func (m *GetAllUserInRadarOpeningReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllUserInRadarOpeningReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllUserInRadarOpeningReq proto.InternalMessageInfo

type GetAllUserInRadarOpeningResp struct {
	Cnt                  uint32   `protobuf:"varint,1,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllUserInRadarOpeningResp) Reset()         { *m = GetAllUserInRadarOpeningResp{} }
func (m *GetAllUserInRadarOpeningResp) String() string { return proto.CompactTextString(m) }
func (*GetAllUserInRadarOpeningResp) ProtoMessage()    {}
func (*GetAllUserInRadarOpeningResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{46}
}
func (m *GetAllUserInRadarOpeningResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllUserInRadarOpeningResp.Unmarshal(m, b)
}
func (m *GetAllUserInRadarOpeningResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllUserInRadarOpeningResp.Marshal(b, m, deterministic)
}
func (dst *GetAllUserInRadarOpeningResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllUserInRadarOpeningResp.Merge(dst, src)
}
func (m *GetAllUserInRadarOpeningResp) XXX_Size() int {
	return xxx_messageInfo_GetAllUserInRadarOpeningResp.Size(m)
}
func (m *GetAllUserInRadarOpeningResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllUserInRadarOpeningResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllUserInRadarOpeningResp proto.InternalMessageInfo

func (m *GetAllUserInRadarOpeningResp) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

// 获取开启雷达状态的用户数V2 带游戏类型
type GetAllUserInRadarOpeningV2Req struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllUserInRadarOpeningV2Req) Reset()         { *m = GetAllUserInRadarOpeningV2Req{} }
func (m *GetAllUserInRadarOpeningV2Req) String() string { return proto.CompactTextString(m) }
func (*GetAllUserInRadarOpeningV2Req) ProtoMessage()    {}
func (*GetAllUserInRadarOpeningV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{47}
}
func (m *GetAllUserInRadarOpeningV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllUserInRadarOpeningV2Req.Unmarshal(m, b)
}
func (m *GetAllUserInRadarOpeningV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllUserInRadarOpeningV2Req.Marshal(b, m, deterministic)
}
func (dst *GetAllUserInRadarOpeningV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllUserInRadarOpeningV2Req.Merge(dst, src)
}
func (m *GetAllUserInRadarOpeningV2Req) XXX_Size() int {
	return xxx_messageInfo_GetAllUserInRadarOpeningV2Req.Size(m)
}
func (m *GetAllUserInRadarOpeningV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllUserInRadarOpeningV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllUserInRadarOpeningV2Req proto.InternalMessageInfo

type GameOpenRadarCnt struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameName             string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	Cnt                  uint32   `protobuf:"varint,3,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameOpenRadarCnt) Reset()         { *m = GameOpenRadarCnt{} }
func (m *GameOpenRadarCnt) String() string { return proto.CompactTextString(m) }
func (*GameOpenRadarCnt) ProtoMessage()    {}
func (*GameOpenRadarCnt) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{48}
}
func (m *GameOpenRadarCnt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameOpenRadarCnt.Unmarshal(m, b)
}
func (m *GameOpenRadarCnt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameOpenRadarCnt.Marshal(b, m, deterministic)
}
func (dst *GameOpenRadarCnt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameOpenRadarCnt.Merge(dst, src)
}
func (m *GameOpenRadarCnt) XXX_Size() int {
	return xxx_messageInfo_GameOpenRadarCnt.Size(m)
}
func (m *GameOpenRadarCnt) XXX_DiscardUnknown() {
	xxx_messageInfo_GameOpenRadarCnt.DiscardUnknown(m)
}

var xxx_messageInfo_GameOpenRadarCnt proto.InternalMessageInfo

func (m *GameOpenRadarCnt) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GameOpenRadarCnt) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GameOpenRadarCnt) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type GetAllUserInRadarOpeningV2Resp struct {
	Cnt                  uint32              `protobuf:"varint,1,opt,name=cnt,proto3" json:"cnt,omitempty"`
	GameRadarCnt         []*GameOpenRadarCnt `protobuf:"bytes,2,rep,name=game_radar_cnt,json=gameRadarCnt,proto3" json:"game_radar_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetAllUserInRadarOpeningV2Resp) Reset()         { *m = GetAllUserInRadarOpeningV2Resp{} }
func (m *GetAllUserInRadarOpeningV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetAllUserInRadarOpeningV2Resp) ProtoMessage()    {}
func (*GetAllUserInRadarOpeningV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{49}
}
func (m *GetAllUserInRadarOpeningV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllUserInRadarOpeningV2Resp.Unmarshal(m, b)
}
func (m *GetAllUserInRadarOpeningV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllUserInRadarOpeningV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetAllUserInRadarOpeningV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllUserInRadarOpeningV2Resp.Merge(dst, src)
}
func (m *GetAllUserInRadarOpeningV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetAllUserInRadarOpeningV2Resp.Size(m)
}
func (m *GetAllUserInRadarOpeningV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllUserInRadarOpeningV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllUserInRadarOpeningV2Resp proto.InternalMessageInfo

func (m *GetAllUserInRadarOpeningV2Resp) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

func (m *GetAllUserInRadarOpeningV2Resp) GetGameRadarCnt() []*GameOpenRadarCnt {
	if m != nil {
		return m.GameRadarCnt
	}
	return nil
}

type GetHasInviteReq struct {
	FollowFromUid        uint32   `protobuf:"varint,1,opt,name=follow_from_uid,json=followFromUid,proto3" json:"follow_from_uid,omitempty"`
	FollowToUid          []uint32 `protobuf:"varint,2,rep,packed,name=follow_to_uid,json=followToUid,proto3" json:"follow_to_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHasInviteReq) Reset()         { *m = GetHasInviteReq{} }
func (m *GetHasInviteReq) String() string { return proto.CompactTextString(m) }
func (*GetHasInviteReq) ProtoMessage()    {}
func (*GetHasInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{50}
}
func (m *GetHasInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHasInviteReq.Unmarshal(m, b)
}
func (m *GetHasInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHasInviteReq.Marshal(b, m, deterministic)
}
func (dst *GetHasInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHasInviteReq.Merge(dst, src)
}
func (m *GetHasInviteReq) XXX_Size() int {
	return xxx_messageInfo_GetHasInviteReq.Size(m)
}
func (m *GetHasInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHasInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHasInviteReq proto.InternalMessageInfo

func (m *GetHasInviteReq) GetFollowFromUid() uint32 {
	if m != nil {
		return m.FollowFromUid
	}
	return 0
}

func (m *GetHasInviteReq) GetFollowToUid() []uint32 {
	if m != nil {
		return m.FollowToUid
	}
	return nil
}

type HasInviteInfo struct {
	FollowToUid          uint32   `protobuf:"varint,1,opt,name=follow_to_uid,json=followToUid,proto3" json:"follow_to_uid,omitempty"`
	HasInvite            bool     `protobuf:"varint,2,opt,name=has_invite,json=hasInvite,proto3" json:"has_invite,omitempty"`
	Has_3DayBeInvite     bool     `protobuf:"varint,3,opt,name=has_3day_be_invite,json=has3dayBeInvite,proto3" json:"has_3day_be_invite,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HasInviteInfo) Reset()         { *m = HasInviteInfo{} }
func (m *HasInviteInfo) String() string { return proto.CompactTextString(m) }
func (*HasInviteInfo) ProtoMessage()    {}
func (*HasInviteInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{51}
}
func (m *HasInviteInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HasInviteInfo.Unmarshal(m, b)
}
func (m *HasInviteInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HasInviteInfo.Marshal(b, m, deterministic)
}
func (dst *HasInviteInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HasInviteInfo.Merge(dst, src)
}
func (m *HasInviteInfo) XXX_Size() int {
	return xxx_messageInfo_HasInviteInfo.Size(m)
}
func (m *HasInviteInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_HasInviteInfo.DiscardUnknown(m)
}

var xxx_messageInfo_HasInviteInfo proto.InternalMessageInfo

func (m *HasInviteInfo) GetFollowToUid() uint32 {
	if m != nil {
		return m.FollowToUid
	}
	return 0
}

func (m *HasInviteInfo) GetHasInvite() bool {
	if m != nil {
		return m.HasInvite
	}
	return false
}

func (m *HasInviteInfo) GetHas_3DayBeInvite() bool {
	if m != nil {
		return m.Has_3DayBeInvite
	}
	return false
}

type GetHasInviteRsp struct {
	FollowFromUid        uint32           `protobuf:"varint,1,opt,name=follow_from_uid,json=followFromUid,proto3" json:"follow_from_uid,omitempty"`
	Infos                []*HasInviteInfo `protobuf:"bytes,2,rep,name=infos,proto3" json:"infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetHasInviteRsp) Reset()         { *m = GetHasInviteRsp{} }
func (m *GetHasInviteRsp) String() string { return proto.CompactTextString(m) }
func (*GetHasInviteRsp) ProtoMessage()    {}
func (*GetHasInviteRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gameradar_e92d2f16343b056d, []int{52}
}
func (m *GetHasInviteRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHasInviteRsp.Unmarshal(m, b)
}
func (m *GetHasInviteRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHasInviteRsp.Marshal(b, m, deterministic)
}
func (dst *GetHasInviteRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHasInviteRsp.Merge(dst, src)
}
func (m *GetHasInviteRsp) XXX_Size() int {
	return xxx_messageInfo_GetHasInviteRsp.Size(m)
}
func (m *GetHasInviteRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHasInviteRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHasInviteRsp proto.InternalMessageInfo

func (m *GetHasInviteRsp) GetFollowFromUid() uint32 {
	if m != nil {
		return m.FollowFromUid
	}
	return 0
}

func (m *GetHasInviteRsp) GetInfos() []*HasInviteInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

func init() {
	proto.RegisterType((*TabsReq)(nil), "gameradar.TabsReq")
	proto.RegisterType((*TabsResp)(nil), "gameradar.TabsResp")
	proto.RegisterType((*Tab)(nil), "gameradar.Tab")
	proto.RegisterType((*BlocksReq)(nil), "gameradar.BlocksReq")
	proto.RegisterType((*BlocksResp)(nil), "gameradar.BlocksResp")
	proto.RegisterType((*Block)(nil), "gameradar.Block")
	proto.RegisterType((*Elem)(nil), "gameradar.Elem")
	proto.RegisterType((*GetRadarIconConfigReq)(nil), "gameradar.GetRadarIconConfigReq")
	proto.RegisterType((*GetRadarIconConfigResp)(nil), "gameradar.GetRadarIconConfigResp")
	proto.RegisterMapType((map[string]*GetRadarIconConfigResp_Theme)(nil), "gameradar.GetRadarIconConfigResp.CardThemeEntry")
	proto.RegisterMapType((map[string]string)(nil), "gameradar.GetRadarIconConfigResp.GameIconEntry")
	proto.RegisterMapType((map[string]string)(nil), "gameradar.GetRadarIconConfigResp.GameTitleColorEntry")
	proto.RegisterMapType((map[string]string)(nil), "gameradar.GetRadarIconConfigResp.HaveInvitedMeIconEntry")
	proto.RegisterMapType((map[string]string)(nil), "gameradar.GetRadarIconConfigResp.RoleIconEntry")
	proto.RegisterMapType((map[string]string)(nil), "gameradar.GetRadarIconConfigResp.SubjectIconEntry")
	proto.RegisterType((*GetRadarIconConfigResp_Theme)(nil), "gameradar.GetRadarIconConfigResp.Theme")
	proto.RegisterType((*InvitePlayReq)(nil), "gameradar.InvitePlayReq")
	proto.RegisterType((*InvitePlayRsp)(nil), "gameradar.InvitePlayRsp")
	proto.RegisterMapType((map[string]string)(nil), "gameradar.InvitePlayRsp.InviteIconEntry")
	proto.RegisterType((*InviteSucReq)(nil), "gameradar.InviteSucReq")
	proto.RegisterType((*InviteSucRsp)(nil), "gameradar.InviteSucRsp")
	proto.RegisterType((*RadarDisplayInfo)(nil), "gameradar.RadarDisplayInfo")
	proto.RegisterType((*LoadMore)(nil), "gameradar.LoadMore")
	proto.RegisterType((*RadarDisplayReq)(nil), "gameradar.RadarDisplayReq")
	proto.RegisterType((*ImMsgInfo)(nil), "gameradar.ImMsgInfo")
	proto.RegisterType((*RadarDisplayRsp)(nil), "gameradar.RadarDisplayRsp")
	proto.RegisterMapType((map[string]string)(nil), "gameradar.RadarDisplayRsp.InviteIconEntry")
	proto.RegisterType((*InviteInfoReq)(nil), "gameradar.InviteInfoReq")
	proto.RegisterType((*InviteInfoRsp)(nil), "gameradar.InviteInfoRsp")
	proto.RegisterType((*ModelInfo)(nil), "gameradar.ModelInfo")
	proto.RegisterType((*PublishItem)(nil), "gameradar.PublishItem")
	proto.RegisterType((*UserRadarItems)(nil), "gameradar.UserRadarItems")
	proto.RegisterType((*GetUserRadarStatusReq)(nil), "gameradar.GetUserRadarStatusReq")
	proto.RegisterType((*GetUserRadarStatusResp)(nil), "gameradar.GetUserRadarStatusResp")
	proto.RegisterType((*Options)(nil), "gameradar.Options")
	proto.RegisterType((*Models)(nil), "gameradar.Models")
	proto.RegisterType((*Games)(nil), "gameradar.Games")
	proto.RegisterType((*GetRadarConfigReq)(nil), "gameradar.GetRadarConfigReq")
	proto.RegisterType((*RecommendNumConfig)(nil), "gameradar.RecommendNumConfig")
	proto.RegisterType((*GetRadarConfigResp)(nil), "gameradar.GetRadarConfigResp")
	proto.RegisterType((*StartRadarReq)(nil), "gameradar.StartRadarReq")
	proto.RegisterType((*StartRadarResp)(nil), "gameradar.StartRadarResp")
	proto.RegisterType((*StopRadarReq)(nil), "gameradar.StopRadarReq")
	proto.RegisterType((*StopRadarResp)(nil), "gameradar.StopRadarResp")
	proto.RegisterType((*BatchGetUserRadarStatusReq)(nil), "gameradar.BatchGetUserRadarStatusReq")
	proto.RegisterType((*BatchGetUserRadarStatusResp)(nil), "gameradar.BatchGetUserRadarStatusResp")
	proto.RegisterMapType((map[uint32]*UserRadarItems)(nil), "gameradar.BatchGetUserRadarStatusResp.MapUserItemEntry")
	proto.RegisterType((*GetTotalOpenUserNumReq)(nil), "gameradar.GetTotalOpenUserNumReq")
	proto.RegisterType((*GetTotalOpenUserNumResp)(nil), "gameradar.GetTotalOpenUserNumResp")
	proto.RegisterType((*DelInviteInfoReq)(nil), "gameradar.DelInviteInfoReq")
	proto.RegisterType((*DelInviteInfoResp)(nil), "gameradar.DelInviteInfoResp")
	proto.RegisterType((*GetUserChannelPushInfoReq)(nil), "gameradar.GetUserChannelPushInfoReq")
	proto.RegisterType((*GetUserChannelPushInfoResp)(nil), "gameradar.GetUserChannelPushInfoResp")
	proto.RegisterType((*GetValidOpenRadarUserReq)(nil), "gameradar.GetValidOpenRadarUserReq")
	proto.RegisterType((*GetValidOpenRadarUserResp)(nil), "gameradar.GetValidOpenRadarUserResp")
	proto.RegisterType((*GetAllUserInRadarOpeningReq)(nil), "gameradar.GetAllUserInRadarOpeningReq")
	proto.RegisterType((*GetAllUserInRadarOpeningResp)(nil), "gameradar.GetAllUserInRadarOpeningResp")
	proto.RegisterType((*GetAllUserInRadarOpeningV2Req)(nil), "gameradar.GetAllUserInRadarOpeningV2Req")
	proto.RegisterType((*GameOpenRadarCnt)(nil), "gameradar.GameOpenRadarCnt")
	proto.RegisterType((*GetAllUserInRadarOpeningV2Resp)(nil), "gameradar.GetAllUserInRadarOpeningV2Resp")
	proto.RegisterType((*GetHasInviteReq)(nil), "gameradar.GetHasInviteReq")
	proto.RegisterType((*HasInviteInfo)(nil), "gameradar.HasInviteInfo")
	proto.RegisterType((*GetHasInviteRsp)(nil), "gameradar.GetHasInviteRsp")
	proto.RegisterEnum("gameradar.PatternType", PatternType_name, PatternType_value)
	proto.RegisterEnum("gameradar.Block_Mode", Block_Mode_name, Block_Mode_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GameRadarClient is the client API for GameRadar service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GameRadarClient interface {
	InvitePlay(ctx context.Context, in *InvitePlayReq, opts ...grpc.CallOption) (*InvitePlayRsp, error)
	InviteSuc(ctx context.Context, in *InviteSucReq, opts ...grpc.CallOption) (*InviteSucRsp, error)
	RadarDisplay(ctx context.Context, in *RadarDisplayReq, opts ...grpc.CallOption) (*RadarDisplayRsp, error)
	GetHasInviteInfo(ctx context.Context, in *GetHasInviteReq, opts ...grpc.CallOption) (*GetHasInviteRsp, error)
	GetInviteInfo(ctx context.Context, in *InviteInfoReq, opts ...grpc.CallOption) (*InviteInfoRsp, error)
	// -------获取用户雷达状态-------
	GetUserRadarStatus(ctx context.Context, in *GetUserRadarStatusReq, opts ...grpc.CallOption) (*GetUserRadarStatusResp, error)
	// -------获取雷达配置-------
	GetRadarConfig(ctx context.Context, in *GetRadarConfigReq, opts ...grpc.CallOption) (*GetRadarConfigResp, error)
	// -------开启雷达-------
	StartRadar(ctx context.Context, in *StartRadarReq, opts ...grpc.CallOption) (*StartRadarResp, error)
	// -------关闭雷达-------
	StopRadar(ctx context.Context, in *StopRadarReq, opts ...grpc.CallOption) (*StopRadarResp, error)
	// 批量获取用户雷达状态
	BatchGetUserRadarStatus(ctx context.Context, in *BatchGetUserRadarStatusReq, opts ...grpc.CallOption) (*BatchGetUserRadarStatusResp, error)
	// 获取总的开启雷达用户数，加上兜底数字
	GetTotalOpenUserNum(ctx context.Context, in *GetTotalOpenUserNumReq, opts ...grpc.CallOption) (*GetTotalOpenUserNumResp, error)
	// 删除约玩记录
	DelInviteInfo(ctx context.Context, in *DelInviteInfoReq, opts ...grpc.CallOption) (*DelInviteInfoResp, error)
	// 房间下发需要 想玩模式 信息
	GetUserChannelPushInfo(ctx context.Context, in *GetUserChannelPushInfoReq, opts ...grpc.CallOption) (*GetUserChannelPushInfoResp, error)
	// 获取全部已开启雷达用户列表
	GetValidOpenRadarUser(ctx context.Context, in *GetValidOpenRadarUserReq, opts ...grpc.CallOption) (*GetValidOpenRadarUserResp, error)
	// 获取开启雷达状态的用户数
	GetAllUserInRadarOpening(ctx context.Context, in *GetAllUserInRadarOpeningReq, opts ...grpc.CallOption) (*GetAllUserInRadarOpeningResp, error)
	// 获取开启雷达状态的用户数V2 带游戏类型
	GetAllUserInRadarOpeningV2(ctx context.Context, in *GetAllUserInRadarOpeningV2Req, opts ...grpc.CallOption) (*GetAllUserInRadarOpeningV2Resp, error)
	// 雷达图标配置
	GetRadarIconConfig(ctx context.Context, in *GetRadarIconConfigReq, opts ...grpc.CallOption) (*GetRadarIconConfigResp, error)
	// Tabs 用于获取所有游戏类型（王者荣耀、和平精英等）
	Tabs(ctx context.Context, in *TabsReq, opts ...grpc.CallOption) (*TabsResp, error)
	// Blocks 用于获取游戏选项（段位、位置等）
	Blocks(ctx context.Context, in *BlocksReq, opts ...grpc.CallOption) (*BlocksResp, error)
}

type gameRadarClient struct {
	cc *grpc.ClientConn
}

func NewGameRadarClient(cc *grpc.ClientConn) GameRadarClient {
	return &gameRadarClient{cc}
}

func (c *gameRadarClient) InvitePlay(ctx context.Context, in *InvitePlayReq, opts ...grpc.CallOption) (*InvitePlayRsp, error) {
	out := new(InvitePlayRsp)
	err := c.cc.Invoke(ctx, "/gameradar.GameRadar/InvitePlay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRadarClient) InviteSuc(ctx context.Context, in *InviteSucReq, opts ...grpc.CallOption) (*InviteSucRsp, error) {
	out := new(InviteSucRsp)
	err := c.cc.Invoke(ctx, "/gameradar.GameRadar/InviteSuc", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRadarClient) RadarDisplay(ctx context.Context, in *RadarDisplayReq, opts ...grpc.CallOption) (*RadarDisplayRsp, error) {
	out := new(RadarDisplayRsp)
	err := c.cc.Invoke(ctx, "/gameradar.GameRadar/RadarDisplay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRadarClient) GetHasInviteInfo(ctx context.Context, in *GetHasInviteReq, opts ...grpc.CallOption) (*GetHasInviteRsp, error) {
	out := new(GetHasInviteRsp)
	err := c.cc.Invoke(ctx, "/gameradar.GameRadar/GetHasInviteInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRadarClient) GetInviteInfo(ctx context.Context, in *InviteInfoReq, opts ...grpc.CallOption) (*InviteInfoRsp, error) {
	out := new(InviteInfoRsp)
	err := c.cc.Invoke(ctx, "/gameradar.GameRadar/GetInviteInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRadarClient) GetUserRadarStatus(ctx context.Context, in *GetUserRadarStatusReq, opts ...grpc.CallOption) (*GetUserRadarStatusResp, error) {
	out := new(GetUserRadarStatusResp)
	err := c.cc.Invoke(ctx, "/gameradar.GameRadar/GetUserRadarStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRadarClient) GetRadarConfig(ctx context.Context, in *GetRadarConfigReq, opts ...grpc.CallOption) (*GetRadarConfigResp, error) {
	out := new(GetRadarConfigResp)
	err := c.cc.Invoke(ctx, "/gameradar.GameRadar/GetRadarConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRadarClient) StartRadar(ctx context.Context, in *StartRadarReq, opts ...grpc.CallOption) (*StartRadarResp, error) {
	out := new(StartRadarResp)
	err := c.cc.Invoke(ctx, "/gameradar.GameRadar/StartRadar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRadarClient) StopRadar(ctx context.Context, in *StopRadarReq, opts ...grpc.CallOption) (*StopRadarResp, error) {
	out := new(StopRadarResp)
	err := c.cc.Invoke(ctx, "/gameradar.GameRadar/StopRadar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRadarClient) BatchGetUserRadarStatus(ctx context.Context, in *BatchGetUserRadarStatusReq, opts ...grpc.CallOption) (*BatchGetUserRadarStatusResp, error) {
	out := new(BatchGetUserRadarStatusResp)
	err := c.cc.Invoke(ctx, "/gameradar.GameRadar/BatchGetUserRadarStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRadarClient) GetTotalOpenUserNum(ctx context.Context, in *GetTotalOpenUserNumReq, opts ...grpc.CallOption) (*GetTotalOpenUserNumResp, error) {
	out := new(GetTotalOpenUserNumResp)
	err := c.cc.Invoke(ctx, "/gameradar.GameRadar/GetTotalOpenUserNum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRadarClient) DelInviteInfo(ctx context.Context, in *DelInviteInfoReq, opts ...grpc.CallOption) (*DelInviteInfoResp, error) {
	out := new(DelInviteInfoResp)
	err := c.cc.Invoke(ctx, "/gameradar.GameRadar/DelInviteInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRadarClient) GetUserChannelPushInfo(ctx context.Context, in *GetUserChannelPushInfoReq, opts ...grpc.CallOption) (*GetUserChannelPushInfoResp, error) {
	out := new(GetUserChannelPushInfoResp)
	err := c.cc.Invoke(ctx, "/gameradar.GameRadar/GetUserChannelPushInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRadarClient) GetValidOpenRadarUser(ctx context.Context, in *GetValidOpenRadarUserReq, opts ...grpc.CallOption) (*GetValidOpenRadarUserResp, error) {
	out := new(GetValidOpenRadarUserResp)
	err := c.cc.Invoke(ctx, "/gameradar.GameRadar/GetValidOpenRadarUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRadarClient) GetAllUserInRadarOpening(ctx context.Context, in *GetAllUserInRadarOpeningReq, opts ...grpc.CallOption) (*GetAllUserInRadarOpeningResp, error) {
	out := new(GetAllUserInRadarOpeningResp)
	err := c.cc.Invoke(ctx, "/gameradar.GameRadar/GetAllUserInRadarOpening", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRadarClient) GetAllUserInRadarOpeningV2(ctx context.Context, in *GetAllUserInRadarOpeningV2Req, opts ...grpc.CallOption) (*GetAllUserInRadarOpeningV2Resp, error) {
	out := new(GetAllUserInRadarOpeningV2Resp)
	err := c.cc.Invoke(ctx, "/gameradar.GameRadar/GetAllUserInRadarOpeningV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRadarClient) GetRadarIconConfig(ctx context.Context, in *GetRadarIconConfigReq, opts ...grpc.CallOption) (*GetRadarIconConfigResp, error) {
	out := new(GetRadarIconConfigResp)
	err := c.cc.Invoke(ctx, "/gameradar.GameRadar/GetRadarIconConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRadarClient) Tabs(ctx context.Context, in *TabsReq, opts ...grpc.CallOption) (*TabsResp, error) {
	out := new(TabsResp)
	err := c.cc.Invoke(ctx, "/gameradar.GameRadar/Tabs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRadarClient) Blocks(ctx context.Context, in *BlocksReq, opts ...grpc.CallOption) (*BlocksResp, error) {
	out := new(BlocksResp)
	err := c.cc.Invoke(ctx, "/gameradar.GameRadar/Blocks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameRadarServer is the server API for GameRadar service.
type GameRadarServer interface {
	InvitePlay(context.Context, *InvitePlayReq) (*InvitePlayRsp, error)
	InviteSuc(context.Context, *InviteSucReq) (*InviteSucRsp, error)
	RadarDisplay(context.Context, *RadarDisplayReq) (*RadarDisplayRsp, error)
	GetHasInviteInfo(context.Context, *GetHasInviteReq) (*GetHasInviteRsp, error)
	GetInviteInfo(context.Context, *InviteInfoReq) (*InviteInfoRsp, error)
	// -------获取用户雷达状态-------
	GetUserRadarStatus(context.Context, *GetUserRadarStatusReq) (*GetUserRadarStatusResp, error)
	// -------获取雷达配置-------
	GetRadarConfig(context.Context, *GetRadarConfigReq) (*GetRadarConfigResp, error)
	// -------开启雷达-------
	StartRadar(context.Context, *StartRadarReq) (*StartRadarResp, error)
	// -------关闭雷达-------
	StopRadar(context.Context, *StopRadarReq) (*StopRadarResp, error)
	// 批量获取用户雷达状态
	BatchGetUserRadarStatus(context.Context, *BatchGetUserRadarStatusReq) (*BatchGetUserRadarStatusResp, error)
	// 获取总的开启雷达用户数，加上兜底数字
	GetTotalOpenUserNum(context.Context, *GetTotalOpenUserNumReq) (*GetTotalOpenUserNumResp, error)
	// 删除约玩记录
	DelInviteInfo(context.Context, *DelInviteInfoReq) (*DelInviteInfoResp, error)
	// 房间下发需要 想玩模式 信息
	GetUserChannelPushInfo(context.Context, *GetUserChannelPushInfoReq) (*GetUserChannelPushInfoResp, error)
	// 获取全部已开启雷达用户列表
	GetValidOpenRadarUser(context.Context, *GetValidOpenRadarUserReq) (*GetValidOpenRadarUserResp, error)
	// 获取开启雷达状态的用户数
	GetAllUserInRadarOpening(context.Context, *GetAllUserInRadarOpeningReq) (*GetAllUserInRadarOpeningResp, error)
	// 获取开启雷达状态的用户数V2 带游戏类型
	GetAllUserInRadarOpeningV2(context.Context, *GetAllUserInRadarOpeningV2Req) (*GetAllUserInRadarOpeningV2Resp, error)
	// 雷达图标配置
	GetRadarIconConfig(context.Context, *GetRadarIconConfigReq) (*GetRadarIconConfigResp, error)
	// Tabs 用于获取所有游戏类型（王者荣耀、和平精英等）
	Tabs(context.Context, *TabsReq) (*TabsResp, error)
	// Blocks 用于获取游戏选项（段位、位置等）
	Blocks(context.Context, *BlocksReq) (*BlocksResp, error)
}

func RegisterGameRadarServer(s *grpc.Server, srv GameRadarServer) {
	s.RegisterService(&_GameRadar_serviceDesc, srv)
}

func _GameRadar_InvitePlay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InvitePlayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRadarServer).InvitePlay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameradar.GameRadar/InvitePlay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRadarServer).InvitePlay(ctx, req.(*InvitePlayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRadar_InviteSuc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InviteSucReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRadarServer).InviteSuc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameradar.GameRadar/InviteSuc",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRadarServer).InviteSuc(ctx, req.(*InviteSucReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRadar_RadarDisplay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RadarDisplayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRadarServer).RadarDisplay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameradar.GameRadar/RadarDisplay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRadarServer).RadarDisplay(ctx, req.(*RadarDisplayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRadar_GetHasInviteInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHasInviteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRadarServer).GetHasInviteInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameradar.GameRadar/GetHasInviteInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRadarServer).GetHasInviteInfo(ctx, req.(*GetHasInviteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRadar_GetInviteInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InviteInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRadarServer).GetInviteInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameradar.GameRadar/GetInviteInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRadarServer).GetInviteInfo(ctx, req.(*InviteInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRadar_GetUserRadarStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRadarStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRadarServer).GetUserRadarStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameradar.GameRadar/GetUserRadarStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRadarServer).GetUserRadarStatus(ctx, req.(*GetUserRadarStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRadar_GetRadarConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRadarConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRadarServer).GetRadarConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameradar.GameRadar/GetRadarConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRadarServer).GetRadarConfig(ctx, req.(*GetRadarConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRadar_StartRadar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartRadarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRadarServer).StartRadar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameradar.GameRadar/StartRadar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRadarServer).StartRadar(ctx, req.(*StartRadarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRadar_StopRadar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopRadarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRadarServer).StopRadar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameradar.GameRadar/StopRadar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRadarServer).StopRadar(ctx, req.(*StopRadarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRadar_BatchGetUserRadarStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserRadarStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRadarServer).BatchGetUserRadarStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameradar.GameRadar/BatchGetUserRadarStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRadarServer).BatchGetUserRadarStatus(ctx, req.(*BatchGetUserRadarStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRadar_GetTotalOpenUserNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTotalOpenUserNumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRadarServer).GetTotalOpenUserNum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameradar.GameRadar/GetTotalOpenUserNum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRadarServer).GetTotalOpenUserNum(ctx, req.(*GetTotalOpenUserNumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRadar_DelInviteInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelInviteInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRadarServer).DelInviteInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameradar.GameRadar/DelInviteInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRadarServer).DelInviteInfo(ctx, req.(*DelInviteInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRadar_GetUserChannelPushInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserChannelPushInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRadarServer).GetUserChannelPushInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameradar.GameRadar/GetUserChannelPushInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRadarServer).GetUserChannelPushInfo(ctx, req.(*GetUserChannelPushInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRadar_GetValidOpenRadarUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetValidOpenRadarUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRadarServer).GetValidOpenRadarUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameradar.GameRadar/GetValidOpenRadarUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRadarServer).GetValidOpenRadarUser(ctx, req.(*GetValidOpenRadarUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRadar_GetAllUserInRadarOpening_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllUserInRadarOpeningReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRadarServer).GetAllUserInRadarOpening(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameradar.GameRadar/GetAllUserInRadarOpening",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRadarServer).GetAllUserInRadarOpening(ctx, req.(*GetAllUserInRadarOpeningReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRadar_GetAllUserInRadarOpeningV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllUserInRadarOpeningV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRadarServer).GetAllUserInRadarOpeningV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameradar.GameRadar/GetAllUserInRadarOpeningV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRadarServer).GetAllUserInRadarOpeningV2(ctx, req.(*GetAllUserInRadarOpeningV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRadar_GetRadarIconConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRadarIconConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRadarServer).GetRadarIconConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameradar.GameRadar/GetRadarIconConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRadarServer).GetRadarIconConfig(ctx, req.(*GetRadarIconConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRadar_Tabs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TabsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRadarServer).Tabs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameradar.GameRadar/Tabs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRadarServer).Tabs(ctx, req.(*TabsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRadar_Blocks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlocksReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRadarServer).Blocks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gameradar.GameRadar/Blocks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRadarServer).Blocks(ctx, req.(*BlocksReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GameRadar_serviceDesc = grpc.ServiceDesc{
	ServiceName: "gameradar.GameRadar",
	HandlerType: (*GameRadarServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "InvitePlay",
			Handler:    _GameRadar_InvitePlay_Handler,
		},
		{
			MethodName: "InviteSuc",
			Handler:    _GameRadar_InviteSuc_Handler,
		},
		{
			MethodName: "RadarDisplay",
			Handler:    _GameRadar_RadarDisplay_Handler,
		},
		{
			MethodName: "GetHasInviteInfo",
			Handler:    _GameRadar_GetHasInviteInfo_Handler,
		},
		{
			MethodName: "GetInviteInfo",
			Handler:    _GameRadar_GetInviteInfo_Handler,
		},
		{
			MethodName: "GetUserRadarStatus",
			Handler:    _GameRadar_GetUserRadarStatus_Handler,
		},
		{
			MethodName: "GetRadarConfig",
			Handler:    _GameRadar_GetRadarConfig_Handler,
		},
		{
			MethodName: "StartRadar",
			Handler:    _GameRadar_StartRadar_Handler,
		},
		{
			MethodName: "StopRadar",
			Handler:    _GameRadar_StopRadar_Handler,
		},
		{
			MethodName: "BatchGetUserRadarStatus",
			Handler:    _GameRadar_BatchGetUserRadarStatus_Handler,
		},
		{
			MethodName: "GetTotalOpenUserNum",
			Handler:    _GameRadar_GetTotalOpenUserNum_Handler,
		},
		{
			MethodName: "DelInviteInfo",
			Handler:    _GameRadar_DelInviteInfo_Handler,
		},
		{
			MethodName: "GetUserChannelPushInfo",
			Handler:    _GameRadar_GetUserChannelPushInfo_Handler,
		},
		{
			MethodName: "GetValidOpenRadarUser",
			Handler:    _GameRadar_GetValidOpenRadarUser_Handler,
		},
		{
			MethodName: "GetAllUserInRadarOpening",
			Handler:    _GameRadar_GetAllUserInRadarOpening_Handler,
		},
		{
			MethodName: "GetAllUserInRadarOpeningV2",
			Handler:    _GameRadar_GetAllUserInRadarOpeningV2_Handler,
		},
		{
			MethodName: "GetRadarIconConfig",
			Handler:    _GameRadar_GetRadarIconConfig_Handler,
		},
		{
			MethodName: "Tabs",
			Handler:    _GameRadar_Tabs_Handler,
		},
		{
			MethodName: "Blocks",
			Handler:    _GameRadar_Blocks_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gameradar/gameradar.proto",
}

func init() {
	proto.RegisterFile("gameradar/gameradar.proto", fileDescriptor_gameradar_e92d2f16343b056d)
}

var fileDescriptor_gameradar_e92d2f16343b056d = []byte{
	// 2833 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x3a, 0xdb, 0x72, 0x1b, 0xc7,
	0x95, 0x02, 0x48, 0x80, 0xc0, 0x21, 0x00, 0x82, 0x2d, 0x4a, 0x82, 0x20, 0x73, 0x2d, 0x8d, 0x25,
	0x99, 0x92, 0x6d, 0x52, 0x4b, 0xad, 0x6d, 0xed, 0x6e, 0xd9, 0x29, 0x92, 0x92, 0x29, 0xc4, 0xbc,
	0x65, 0x08, 0xba, 0x62, 0x27, 0xa9, 0xa9, 0x06, 0xa6, 0x09, 0x4c, 0x34, 0x37, 0x4e, 0x0f, 0x68,
	0x31, 0x55, 0xa9, 0xfc, 0x43, 0xaa, 0xf2, 0x90, 0x54, 0x2a, 0x0f, 0xf9, 0x88, 0x24, 0x3f, 0x92,
	0xd7, 0xe4, 0x25, 0x3f, 0x90, 0x3f, 0x48, 0xf5, 0xe9, 0x9e, 0x41, 0xcf, 0x00, 0x20, 0xa9, 0xe4,
	0x6d, 0xfa, 0xdc, 0xba, 0xfb, 0xdc, 0xfb, 0x00, 0x70, 0x77, 0x40, 0x3d, 0x16, 0x51, 0x9b, 0x46,
	0x1b, 0xe9, 0xd7, 0x7a, 0x18, 0x05, 0x71, 0x40, 0xaa, 0x29, 0xc0, 0xa8, 0xc2, 0x42, 0x97, 0xf6,
	0xb8, 0xc9, 0xce, 0x8c, 0x75, 0xa8, 0xc8, 0x4f, 0x1e, 0x12, 0x03, 0xe6, 0x63, 0xda, 0xe3, 0xad,
	0xc2, 0xfd, 0xb9, 0xb5, 0xc5, 0xcd, 0xc6, 0xfa, 0x58, 0x42, 0x97, 0xf6, 0x4c, 0xc4, 0x19, 0xdb,
	0x30, 0xd7, 0xa5, 0x3d, 0xd2, 0x80, 0xa2, 0x63, 0xb7, 0x0a, 0xf7, 0x0b, 0x6b, 0x75, 0xb3, 0xe8,
	0xd8, 0x64, 0x05, 0x4a, 0xb1, 0x13, 0xbb, 0xac, 0x55, 0xbc, 0x5f, 0x58, 0xab, 0x9a, 0x72, 0x41,
	0x6e, 0x41, 0x39, 0xa6, 0x03, 0xcb, 0xb1, 0x5b, 0x73, 0x48, 0x59, 0x8a, 0xe9, 0xa0, 0x63, 0x1b,
	0xff, 0x0b, 0xd5, 0x6d, 0x37, 0xe8, 0xbf, 0x11, 0x07, 0x90, 0x34, 0x3d, 0x2b, 0x95, 0x56, 0x8a,
	0x69, 0xaf, 0x63, 0x6b, 0xac, 0x45, 0x9d, 0xf5, 0x33, 0x80, 0x84, 0x95, 0x87, 0x64, 0x0d, 0xca,
	0x3d, 0x5c, 0xa9, 0x23, 0x37, 0xb5, 0x23, 0x23, 0x99, 0xa9, 0xf0, 0xc6, 0x9f, 0x0b, 0x50, 0x42,
	0xc8, 0x35, 0x4f, 0xfe, 0x04, 0xe6, 0xbd, 0xc0, 0x66, 0x78, 0xee, 0xc6, 0xe6, 0xad, 0xbc, 0xdc,
	0xf5, 0xfd, 0xc0, 0x66, 0x26, 0x92, 0x90, 0x47, 0x50, 0x62, 0x2e, 0xf3, 0x78, 0x6b, 0x1e, 0xcf,
	0xb0, 0xa4, 0xd1, 0xbe, 0x72, 0x99, 0x67, 0x4a, 0x2c, 0xb9, 0x03, 0x0b, 0x3d, 0xc7, 0xb7, 0xc5,
	0x8d, 0x4a, 0xb8, 0x79, 0x59, 0x2c, 0x3b, 0xb6, 0xb1, 0x0a, 0xf3, 0x42, 0x1a, 0x01, 0x28, 0x1f,
	0x77, 0x0e, 0x76, 0xf7, 0x5e, 0x35, 0x6f, 0x90, 0x2a, 0x94, 0xf6, 0x4f, 0xf6, 0xba, 0x9d, 0x66,
	0xc1, 0xf8, 0x18, 0xe6, 0x85, 0x98, 0xeb, 0x9d, 0xdb, 0xb8, 0x03, 0xb7, 0x76, 0x59, 0x6c, 0x8a,
	0xdd, 0x3b, 0xfd, 0xc0, 0xdf, 0x09, 0xfc, 0x53, 0x67, 0x20, 0xec, 0xfc, 0xcf, 0x2a, 0xdc, 0x9e,
	0x86, 0xe1, 0x21, 0xd9, 0x03, 0x74, 0x0d, 0xcb, 0xe9, 0x07, 0xbe, 0x52, 0xe4, 0x86, 0x76, 0x89,
	0xe9, 0x5c, 0xeb, 0xbb, 0xd4, 0x63, 0x02, 0xf4, 0xca, 0x8f, 0xa3, 0x0b, 0xb3, 0x32, 0x50, 0x4b,
	0x21, 0x2d, 0x0a, 0x5c, 0x25, 0xad, 0x78, 0x5d, 0x69, 0x66, 0xe0, 0xea, 0xd2, 0x22, 0xb5, 0x24,
	0x0e, 0xac, 0x0c, 0xe9, 0x39, 0xb3, 0x1c, 0xff, 0xdc, 0x89, 0x99, 0x6d, 0x25, 0xc7, 0x9c, 0x43,
	0xc1, 0x2f, 0xae, 0x16, 0xfc, 0x9a, 0x9e, 0xb3, 0x8e, 0x64, 0xde, 0xd7, 0x76, 0x58, 0x1e, 0xe6,
	0xe1, 0xe4, 0x10, 0xa0, 0x4f, 0x23, 0xdb, 0x8a, 0x87, 0xcc, 0x63, 0xca, 0x98, 0xcf, 0xae, 0xde,
	0x60, 0x87, 0x46, 0x76, 0x57, 0xb0, 0x48, 0xc1, 0xd5, 0x7e, 0xb2, 0x26, 0x16, 0x34, 0x51, 0xaf,
	0x68, 0x19, 0xab, 0x1f, 0xb8, 0x41, 0xd4, 0x2a, 0xa1, 0xd8, 0x4f, 0xaf, 0xa7, 0xde, 0xae, 0x60,
	0xdc, 0x11, 0x7c, 0x52, 0x76, 0x63, 0x90, 0x01, 0x92, 0x13, 0xa8, 0xf1, 0x51, 0xef, 0xe7, 0xac,
	0x1f, 0x4b, 0xa5, 0x94, 0x51, 0xf8, 0xe6, 0xd5, 0xc2, 0x8f, 0x25, 0xd7, 0x58, 0x1d, 0x8b, 0x7c,
	0x0c, 0x69, 0xff, 0x3f, 0xd4, 0x33, 0xc6, 0x25, 0x4d, 0x98, 0x7b, 0xc3, 0x2e, 0xd0, 0xf7, 0xaa,
	0xa6, 0xf8, 0x14, 0xce, 0x77, 0x4e, 0xdd, 0x51, 0xea, 0x7c, 0xb8, 0xf8, 0xbf, 0xe2, 0x8b, 0x82,
	0x60, 0xce, 0xd8, 0xf2, 0x9d, 0x98, 0x5f, 0xc2, 0xed, 0xe9, 0xf6, 0x7a, 0x27, 0x29, 0x0c, 0x1a,
	0x59, 0xa3, 0x4c, 0xe1, 0xfe, 0x42, 0xe7, 0x5e, 0xdc, 0xfc, 0xf0, 0x6a, 0x9d, 0xa1, 0x38, 0x7d,
	0x9b, 0x2d, 0xb8, 0x39, 0xc5, 0x48, 0xef, 0x74, 0xd2, 0x2f, 0xa1, 0x99, 0x37, 0xc5, 0x3b, 0xf1,
	0xff, 0xbe, 0x08, 0x25, 0xe9, 0x6b, 0xef, 0xc3, 0xe2, 0x39, 0x8b, 0x62, 0xa7, 0x4f, 0x5d, 0xab,
	0x37, 0x50, 0xdc, 0x90, 0x80, 0xb6, 0x07, 0xe4, 0x03, 0xa8, 0x0f, 0x83, 0xc8, 0xf9, 0x45, 0xe0,
	0xc7, 0x92, 0x44, 0x0a, 0xab, 0x8d, 0x81, 0xdb, 0x03, 0xf2, 0x0c, 0x56, 0x34, 0x29, 0xd6, 0x20,
	0xa2, 0xb6, 0xc3, 0xfc, 0x18, 0xa3, 0xad, 0x6a, 0x92, 0xb1, 0xb8, 0x5d, 0x85, 0x21, 0xff, 0x03,
	0xb7, 0x33, 0x62, 0xc7, 0x3c, 0xf3, 0xc8, 0xb3, 0xa2, 0xcb, 0x4f, 0xb9, 0x3e, 0x80, 0xba, 0xcd,
	0x4e, 0xe9, 0xc8, 0x8d, 0xd3, 0xb0, 0xc0, 0xc3, 0x28, 0xa0, 0xf4, 0xee, 0x07, 0x50, 0xe3, 0x6e,
	0x30, 0xa0, 0xbe, 0xa2, 0x29, 0x23, 0xcd, 0xa2, 0x84, 0x49, 0x92, 0x55, 0x80, 0x24, 0x00, 0x7a,
	0x83, 0xd6, 0x02, 0x12, 0x54, 0x15, 0x64, 0x7b, 0x60, 0x5c, 0x40, 0x5d, 0xba, 0xd2, 0x91, 0x4b,
	0x2f, 0x44, 0xad, 0xb9, 0x0b, 0x95, 0xd3, 0x28, 0xf0, 0xac, 0x51, 0x9a, 0x49, 0x17, 0xc4, 0xfa,
	0xc4, 0x91, 0xf5, 0x26, 0x40, 0x44, 0x52, 0x6f, 0x02, 0x01, 0xbe, 0x07, 0x18, 0xd0, 0x96, 0xe3,
	0x9f, 0x06, 0x58, 0x0c, 0x6a, 0x66, 0x45, 0x00, 0x3a, 0xfe, 0x69, 0x20, 0xc4, 0x89, 0xd2, 0xe5,
	0x53, 0xcc, 0x17, 0x62, 0xf3, 0x85, 0x98, 0xf6, 0x0e, 0xa8, 0xc7, 0x8c, 0xdf, 0x15, 0x33, 0x7b,
	0xf3, 0x90, 0x3c, 0x86, 0xa5, 0x21, 0xe5, 0x2a, 0x91, 0x89, 0x8d, 0x64, 0xd1, 0xaa, 0x9b, 0xf5,
	0x21, 0xe5, 0x92, 0xf4, 0xc4, 0xb1, 0x39, 0xf9, 0x0c, 0x6a, 0x8e, 0x67, 0x79, 0x7c, 0x80, 0x7b,
	0x72, 0x95, 0x42, 0x57, 0x34, 0x07, 0xed, 0x78, 0xfb, 0x7c, 0x20, 0x0e, 0x60, 0x82, 0x93, 0x7c,
	0x72, 0xd2, 0x81, 0x45, 0x25, 0x5b, 0x4b, 0x90, 0x6b, 0x3a, 0x9b, 0x7e, 0x1c, 0xb5, 0x1a, 0x67,
	0x00, 0x70, 0x52, 0x80, 0x70, 0x26, 0x25, 0x2a, 0x76, 0xd4, 0xd5, 0xe6, 0x12, 0x82, 0xae, 0xe3,
	0xb1, 0xf6, 0x17, 0xb0, 0x94, 0xe3, 0x7f, 0x17, 0xb7, 0x35, 0x7e, 0x0c, 0x35, 0xc9, 0x7e, 0x3c,
	0xea, 0xff, 0x7b, 0x66, 0xb9, 0x0d, 0x65, 0x1e, 0xd3, 0x78, 0xc4, 0x55, 0x63, 0xa1, 0x56, 0xc6,
	0x13, 0x5d, 0x32, 0x0f, 0x33, 0x16, 0x2a, 0x64, 0x2d, 0xf4, 0x4b, 0x68, 0x62, 0x98, 0xbf, 0x74,
	0x78, 0xe8, 0xd2, 0x0b, 0x34, 0x68, 0x13, 0xe6, 0xc6, 0x67, 0x10, 0x9f, 0x59, 0xfb, 0x17, 0x2f,
	0xb1, 0xff, 0x5c, 0x46, 0xfa, 0x95, 0x2a, 0x34, 0x38, 0x54, 0xf6, 0x02, 0x6a, 0xef, 0x07, 0x11,
	0x23, 0x1f, 0x03, 0x09, 0x4e, 0x4f, 0x39, 0x8b, 0x2d, 0x9d, 0xa7, 0x80, 0x3c, 0x4d, 0x89, 0xe9,
	0xa4, 0x9c, 0x22, 0x78, 0x14, 0xb5, 0x52, 0x81, 0xd4, 0x4c, 0x4d, 0x02, 0x8f, 0x11, 0x26, 0x94,
	0xef, 0x3a, 0x9e, 0x13, 0x27, 0x8d, 0x17, 0x2e, 0x8c, 0xef, 0x60, 0x49, 0xbf, 0x73, 0xd2, 0x7e,
	0x05, 0x9a, 0xe6, 0x95, 0x82, 0x9f, 0x41, 0xd5, 0x0d, 0xa8, 0x6d, 0x79, 0x41, 0x94, 0xe4, 0xc8,
	0x9b, 0x9a, 0x2f, 0x25, 0x47, 0x37, 0x2b, 0xae, 0xfa, 0x32, 0x5e, 0x40, 0x35, 0x75, 0xcc, 0x71,
	0x73, 0x52, 0xc8, 0xb5, 0x83, 0xd2, 0xb5, 0xd1, 0xa9, 0xab, 0x66, 0x09, 0xdd, 0xd7, 0xf8, 0xed,
	0x5c, 0xee, 0x58, 0x3c, 0x24, 0x1b, 0x30, 0x8f, 0x2a, 0x97, 0xed, 0xc8, 0x3d, 0x6d, 0xeb, 0xbc,
	0xd1, 0x4c, 0x24, 0x14, 0xa9, 0x40, 0x29, 0xaf, 0xef, 0xc7, 0x4a, 0x25, 0x55, 0x09, 0xd9, 0xf1,
	0xe3, 0x89, 0xa8, 0x9a, 0xbb, 0x66, 0x54, 0x3d, 0x80, 0x5a, 0x9f, 0xfa, 0x96, 0xb8, 0x25, 0xaa,
	0x42, 0x18, 0xb2, 0x62, 0x2e, 0xf6, 0xa9, 0xbf, 0xa7, 0x40, 0xe4, 0x29, 0x2c, 0x7b, 0xce, 0x98,
	0x44, 0x1a, 0xaf, 0x84, 0xc6, 0x5b, 0xf2, 0x9c, 0x94, 0x6e, 0xba, 0xed, 0xca, 0x53, 0x6c, 0xf7,
	0x75, 0x36, 0x92, 0x17, 0xf0, 0xa8, 0x4f, 0x67, 0xa8, 0xe0, 0x8a, 0x58, 0xfe, 0x4f, 0x43, 0x75,
	0x2b, 0x49, 0x63, 0xa8, 0x99, 0xd9, 0xfe, 0xa2, 0x87, 0x70, 0x31, 0x13, 0xc2, 0xc6, 0x6e, 0x46,
	0x04, 0x0f, 0xb3, 0x31, 0x55, 0xb8, 0x24, 0xa6, 0x8a, 0xd9, 0x88, 0xfd, 0x4d, 0x01, 0xaa, 0xa2,
	0x53, 0x76, 0x13, 0x42, 0xd1, 0x7e, 0xbb, 0xe3, 0x97, 0xc3, 0x02, 0xae, 0x3b, 0x36, 0x79, 0x01,
	0x8d, 0x20, 0x8c, 0x9d, 0xc0, 0xb7, 0x1c, 0xdb, 0x72, 0x1d, 0x1e, 0xab, 0x24, 0x4a, 0x34, 0x1d,
	0x1e, 0x22, 0x01, 0x37, 0x6b, 0x92, 0xb2, 0x63, 0xef, 0x39, 0x3c, 0x26, 0x04, 0xe6, 0x63, 0xf6,
	0x36, 0x56, 0xd1, 0x8c, 0xdf, 0x22, 0x94, 0xe5, 0x46, 0xd2, 0xa3, 0x65, 0xa2, 0x07, 0x04, 0x61,
	0xed, 0x37, 0xfe, 0x58, 0x80, 0xc5, 0xa3, 0x51, 0xcf, 0x75, 0xf8, 0xb0, 0x13, 0x33, 0x4f, 0x74,
	0xfa, 0xb2, 0x9f, 0x4e, 0x0e, 0x56, 0xc6, 0xe6, 0xd8, 0x16, 0x25, 0xc0, 0xe1, 0x56, 0x7f, 0x14,
	0x45, 0xcc, 0x8f, 0xad, 0x90, 0x0e, 0xe4, 0x15, 0x2b, 0x66, 0xdd, 0xe1, 0x3b, 0x12, 0x7a, 0x44,
	0x07, 0x8c, 0x3c, 0x07, 0x29, 0x5e, 0x9e, 0x7d, 0xd2, 0x55, 0x53, 0x25, 0x98, 0x55, 0xa4, 0xc3,
	0xa3, 0xaf, 0x02, 0x8c, 0xbb, 0x4d, 0x75, 0xca, 0x6a, 0xda, 0x30, 0x1a, 0x7f, 0x29, 0x40, 0xe3,
	0x84, 0xb3, 0x48, 0xb6, 0x36, 0xb1, 0x78, 0x91, 0x4c, 0x66, 0xbb, 0xe7, 0x50, 0x75, 0x62, 0xe6,
	0xe9, 0x3a, 0xbb, 0xad, 0xed, 0xab, 0x5d, 0xd2, 0xac, 0x08, 0x42, 0xdc, 0xf8, 0x0e, 0x2c, 0x38,
	0xdc, 0x0a, 0x42, 0xe6, 0xa3, 0xda, 0x2a, 0x66, 0xd9, 0xe1, 0x87, 0x21, 0xc3, 0x32, 0x12, 0x31,
	0x8f, 0x3a, 0xfe, 0x38, 0x07, 0xd6, 0x4d, 0x90, 0x20, 0x8c, 0x86, 0x47, 0xd0, 0xe8, 0x63, 0x83,
	0x65, 0x8d, 0x42, 0x9b, 0xc6, 0x4c, 0xbe, 0x8c, 0x2a, 0x66, 0x5d, 0x42, 0x4f, 0x24, 0xd0, 0x78,
	0x82, 0x6f, 0x9a, 0xf4, 0xf0, 0x32, 0x4a, 0x84, 0x2f, 0x4e, 0x5c, 0xc0, 0xf8, 0x1a, 0x1f, 0x39,
	0x13, 0xa4, 0x3c, 0x24, 0xff, 0x9d, 0x56, 0x8c, 0x02, 0x66, 0xb3, 0xbb, 0xda, 0xbd, 0xb2, 0x7a,
	0x49, 0x8b, 0xc9, 0x3e, 0x2c, 0x28, 0x2f, 0x11, 0x2e, 0x9b, 0x7a, 0x94, 0xda, 0xaf, 0x92, 0x38,
	0x8e, 0xb8, 0x86, 0x42, 0xf6, 0x03, 0x3f, 0x66, 0x2a, 0xfd, 0x54, 0xcd, 0xba, 0x84, 0xee, 0x48,
	0xa0, 0xf1, 0xb7, 0x22, 0x94, 0xd1, 0x72, 0xfc, 0x32, 0xdf, 0xcd, 0x79, 0x5b, 0x31, 0xef, 0x6d,
	0xa4, 0x05, 0x0b, 0x21, 0x8d, 0x63, 0x16, 0xf9, 0x2a, 0xb7, 0x27, 0x4b, 0x91, 0x88, 0x92, 0xae,
	0x6a, 0x7c, 0x58, 0xa9, 0xf5, 0x25, 0x85, 0x38, 0x4c, 0xce, 0xfc, 0x1c, 0x16, 0x15, 0x0d, 0xda,
	0xba, 0x34, 0x33, 0x3e, 0x40, 0x92, 0x65, 0xa2, 0xa3, 0x8c, 0xd9, 0x5b, 0x46, 0xc7, 0x1a, 0x34,
	0x47, 0xbe, 0x73, 0x36, 0x62, 0xda, 0x9e, 0x0b, 0xb8, 0x67, 0x43, 0xc2, 0xd3, 0x2d, 0x57, 0x01,
	0x3c, 0xfa, 0xd6, 0xf2, 0x47, 0x5e, 0x8f, 0x45, 0xad, 0x8a, 0xcc, 0xd0, 0x1e, 0x7d, 0x7b, 0x80,
	0x00, 0x81, 0xfe, 0x3e, 0x0a, 0xfc, 0x81, 0x85, 0x5b, 0x54, 0xa5, 0xff, 0x22, 0xa4, 0x2b, 0xf6,
	0x79, 0x00, 0x49, 0x77, 0x28, 0x09, 0x40, 0x76, 0x83, 0x0a, 0x26, 0x48, 0x44, 0x7e, 0x28, 0x89,
	0x8e, 0x9c, 0xcf, 0x8e, 0xc0, 0x6c, 0x90, 0x14, 0x73, 0x41, 0x42, 0x0c, 0xa8, 0xa7, 0x2f, 0x61,
	0x6b, 0x14, 0xb9, 0x2a, 0x0f, 0x2c, 0x26, 0x8f, 0xdb, 0x93, 0xc8, 0x25, 0xcf, 0x32, 0xc1, 0x29,
	0x9f, 0x89, 0xcb, 0xf9, 0xe0, 0xe4, 0x5a, 0x64, 0x1a, 0x8f, 0x60, 0x39, 0x79, 0x53, 0xa4, 0xef,
	0xf1, 0x29, 0xbe, 0xfb, 0xeb, 0x02, 0x10, 0x93, 0xf5, 0x03, 0xcf, 0x63, 0xbe, 0x7d, 0x30, 0xf2,
	0x24, 0xad, 0x70, 0xbd, 0x88, 0xfa, 0x03, 0x66, 0x79, 0x8e, 0x9f, 0xb8, 0x1e, 0x02, 0xf6, 0x1d,
	0x5f, 0x43, 0xd2, 0xb7, 0x2a, 0xef, 0x2a, 0x24, 0x7d, 0x2b, 0xfc, 0x52, 0x04, 0x5e, 0x64, 0x39,
	0x7e, 0xcc, 0xa2, 0x73, 0xea, 0x2a, 0x87, 0xa9, 0x23, 0xb4, 0xa3, 0x80, 0x42, 0x86, 0x24, 0x13,
	0x32, 0xa4, 0xbb, 0x54, 0x10, 0xb0, 0x4f, 0xdf, 0x1a, 0x7f, 0x2f, 0x00, 0xc9, 0x1f, 0x9e, 0x87,
	0x42, 0x8f, 0x43, 0x46, 0x6d, 0x4b, 0x2f, 0xf2, 0x55, 0x01, 0x91, 0x7a, 0x7c, 0x0a, 0xcb, 0xa7,
	0x4e, 0xc4, 0x63, 0x6b, 0xd2, 0x95, 0x97, 0x10, 0xb1, 0x3f, 0xf6, 0xe7, 0x4f, 0xd4, 0xf4, 0x41,
	0xcb, 0x75, 0xfa, 0x18, 0x07, 0x0d, 0x2a, 0xc7, 0x0b, 0xe8, 0x83, 0x87, 0xb0, 0x12, 0x25, 0x4a,
	0x12, 0xbe, 0x64, 0xc9, 0x5c, 0x81, 0x07, 0x5f, 0xdc, 0x5c, 0xd5, 0xab, 0xe4, 0x84, 0x2e, 0x4d,
	0x12, 0x4d, 0xc0, 0x0c, 0x0e, 0xf5, 0xe3, 0x98, 0x46, 0xf2, 0x8a, 0x53, 0x2d, 0x43, 0x9e, 0xc2,
	0xbc, 0xc8, 0x76, 0xaa, 0x0f, 0x9a, 0x95, 0x11, 0x91, 0x86, 0x3c, 0x84, 0x86, 0xc3, 0xad, 0xb3,
	0x91, 0xd3, 0x7f, 0x23, 0x6a, 0x7c, 0x14, 0xab, 0xa4, 0x58, 0x73, 0xf8, 0x8f, 0x04, 0x10, 0xf7,
	0x32, 0xfe, 0x51, 0x80, 0x86, 0xbe, 0x2b, 0x0f, 0xf3, 0xd9, 0xb2, 0x30, 0x91, 0x2d, 0xd5, 0xb9,
	0x8a, 0xe3, 0x73, 0x69, 0x6e, 0x3e, 0x97, 0x71, 0xf3, 0x7b, 0x4a, 0xa7, 0xda, 0xcb, 0x04, 0x35,
	0x88, 0xad, 0x29, 0xe6, 0x32, 0xe6, 0x8f, 0xfb, 0x14, 0xcc, 0x65, 0x4c, 0x6e, 0x92, 0xf5, 0xee,
	0xf2, 0xd5, 0xde, 0x4d, 0xda, 0x50, 0xb1, 0x47, 0x11, 0x15, 0x41, 0xae, 0x02, 0x3f, 0x5d, 0x1b,
	0xf7, 0xa1, 0x76, 0x1c, 0x07, 0xe1, 0x6c, 0xd5, 0x1a, 0x1f, 0x0a, 0xed, 0xa7, 0x14, 0x3c, 0x14,
	0x9d, 0xbd, 0xd8, 0x2d, 0x1b, 0xb9, 0x8e, 0x6d, 0x7c, 0x0e, 0xed, 0x6d, 0x1a, 0xf7, 0x87, 0xd3,
	0x2b, 0xc1, 0x5d, 0xa8, 0x8c, 0x92, 0x5a, 0x2f, 0x5f, 0x55, 0x0b, 0x23, 0x07, 0x4b, 0xba, 0xf1,
	0xd7, 0x02, 0xdc, 0x9b, 0xc9, 0xc9, 0x43, 0xf2, 0x13, 0xa8, 0x7b, 0x34, 0xb4, 0x46, 0x5c, 0x04,
	0x8a, 0xb0, 0xb2, 0x6c, 0x39, 0x3f, 0xd7, 0x47, 0x7e, 0xb3, 0xd9, 0xd7, 0xf7, 0x69, 0x28, 0xc0,
	0xc2, 0x03, 0xd4, 0x28, 0xc5, 0x1b, 0x43, 0xda, 0xdf, 0x42, 0x33, 0x4f, 0xa0, 0xb7, 0x5f, 0x75,
	0xd9, 0x7e, 0x6d, 0x64, 0x87, 0x11, 0x97, 0x94, 0x26, 0xad, 0x33, 0x6b, 0x61, 0xa9, 0xeb, 0x06,
	0x31, 0x75, 0x45, 0xb5, 0x15, 0x84, 0x07, 0x23, 0xcf, 0x64, 0x67, 0xc6, 0x47, 0x70, 0x67, 0x2a,
	0x86, 0x87, 0x62, 0x6f, 0x7f, 0xe4, 0x25, 0x7b, 0xfb, 0x23, 0xcf, 0x78, 0x08, 0xcd, 0x97, 0xa2,
	0x99, 0xd0, 0x7b, 0xbc, 0x49, 0x33, 0xdd, 0x84, 0xe5, 0x1c, 0x15, 0x0f, 0x8d, 0x4f, 0xe0, 0xae,
	0x52, 0xca, 0xce, 0x90, 0xfa, 0x3e, 0x73, 0x8f, 0x46, 0x7c, 0x38, 0x5b, 0xc6, 0x9f, 0x0a, 0xd0,
	0x9e, 0x45, 0xcf, 0x43, 0x6d, 0xe0, 0x5b, 0xd0, 0x06, 0xbe, 0x59, 0x57, 0x2e, 0xe6, 0x5c, 0x79,
	0x35, 0xf1, 0x56, 0xed, 0x09, 0x26, 0x5d, 0x13, 0xd1, 0x7a, 0x99, 0x9d, 0xcf, 0x96, 0xd9, 0xa7,
	0xb0, 0x8c, 0x0a, 0xc6, 0xbe, 0x25, 0x69, 0xc6, 0x65, 0xf7, 0xb1, 0x84, 0x08, 0xa1, 0x39, 0x69,
	0x66, 0xe3, 0x35, 0xb4, 0x76, 0x59, 0xfc, 0x0d, 0x75, 0x1d, 0x5b, 0x40, 0xd1, 0x1c, 0x68, 0x17,
	0x76, 0x26, 0xdc, 0x55, 0xf6, 0xee, 0x89, 0xbb, 0xca, 0xd5, 0xf8, 0xfd, 0x55, 0xd4, 0xdf, 0x5f,
	0xc7, 0xa8, 0xb1, 0x69, 0x92, 0x78, 0x48, 0x3e, 0xd3, 0x9b, 0x2f, 0xe9, 0x84, 0x97, 0x78, 0x42,
	0xda, 0x7f, 0x19, 0xab, 0x70, 0x6f, 0x97, 0xc5, 0x5b, 0xae, 0x8b, 0x6e, 0x26, 0x85, 0x0a, 0xe9,
	0x8e, 0x8f, 0x83, 0xdf, 0x67, 0xf0, 0xde, 0x6c, 0xb4, 0x74, 0x09, 0xf1, 0x62, 0x52, 0x86, 0xea,
	0xfb, 0xb1, 0xf1, 0x3e, 0xac, 0xce, 0xe2, 0xf8, 0x66, 0x53, 0x88, 0xfc, 0x0e, 0x9a, 0x22, 0x2d,
	0xa7, 0x57, 0x10, 0x0f, 0xac, 0x99, 0x25, 0xf7, 0x52, 0x03, 0xaa, 0xcd, 0xe7, 0xc6, 0x9b, 0x8f,
	0xe0, 0xbf, 0x2e, 0xdb, 0x7c, 0xda, 0x81, 0xc9, 0x16, 0xe0, 0x64, 0xd4, 0x92, 0x16, 0x95, 0xef,
	0xbf, 0xfc, 0xb3, 0x31, 0x7f, 0x60, 0xb3, 0x26, 0x70, 0xc9, 0xca, 0xf8, 0x19, 0x2c, 0xed, 0xb2,
	0xf8, 0x75, 0x32, 0x89, 0x11, 0xa6, 0x7d, 0x0c, 0x4b, 0xa7, 0x81, 0xeb, 0x06, 0xdf, 0x5b, 0xb9,
	0xe1, 0x44, 0x5d, 0x82, 0xbf, 0x52, 0x23, 0x0a, 0x03, 0x14, 0xc0, 0x4a, 0x27, 0x15, 0x22, 0x01,
	0x2d, 0x4a, 0x60, 0x57, 0x3c, 0x8f, 0x8c, 0x5f, 0x41, 0x3d, 0x95, 0x8d, 0xaf, 0x97, 0x09, 0x26,
	0x29, 0x5a, 0x67, 0xc2, 0x22, 0x9b, 0x4e, 0x8c, 0xd4, 0x4b, 0xa1, 0x9a, 0x0e, 0x8b, 0xc8, 0x47,
	0x40, 0x04, 0xfa, 0xb9, 0x4d, 0x2f, 0xac, 0x5e, 0x32, 0x21, 0x57, 0xd5, 0x66, 0x69, 0x48, 0xb9,
	0x40, 0x6c, 0xab, 0x59, 0xaa, 0xe1, 0xe4, 0xee, 0x27, 0x07, 0x52, 0xd7, 0xba, 0xdf, 0x3a, 0x94,
	0xf4, 0x49, 0x54, 0x4b, 0x53, 0x6a, 0xe6, 0x4e, 0xa6, 0x24, 0x7b, 0xda, 0x87, 0xc5, 0x23, 0xd9,
	0x91, 0x76, 0x2f, 0x42, 0x46, 0x6e, 0xc2, 0xd2, 0xd1, 0x56, 0xb7, 0xfb, 0xca, 0x3c, 0xb0, 0x5e,
	0xbe, 0xfa, 0x6a, 0xeb, 0x64, 0xaf, 0xdb, 0xbc, 0x41, 0x08, 0x34, 0x12, 0xe0, 0xe1, 0x51, 0xb7,
	0x73, 0x78, 0xd0, 0x2c, 0x90, 0x16, 0xac, 0x24, 0x30, 0xfc, 0xed, 0x23, 0xc1, 0x14, 0xc9, 0x32,
	0xd4, 0x13, 0x4c, 0xe7, 0xe0, 0xe8, 0xa4, 0xdb, 0x9c, 0xdb, 0xfc, 0x43, 0x0d, 0xaa, 0xbb, 0x89,
	0x01, 0xc9, 0x36, 0xc0, 0x78, 0xba, 0x45, 0x5a, 0xd3, 0x87, 0x5e, 0xec, 0xac, 0xdd, 0x9a, 0x35,
	0x0e, 0x33, 0x6e, 0x90, 0x1f, 0x40, 0x35, 0x1d, 0x1d, 0x91, 0x3b, 0x13, 0x84, 0x72, 0x54, 0xd5,
	0x9e, 0x8e, 0x40, 0x01, 0xaf, 0xa1, 0xa6, 0x3f, 0xcc, 0x49, 0x7b, 0xd6, 0x8b, 0x9d, 0x9d, 0xb5,
	0xdb, 0xb3, 0x5f, 0xf3, 0xc6, 0x0d, 0xb2, 0x07, 0x4d, 0xdd, 0x58, 0xe8, 0x30, 0xed, 0xec, 0x84,
	0x5a, 0xf7, 0xd4, 0xf6, 0x4c, 0x1c, 0x4a, 0x7b, 0x05, 0xf5, 0xdd, 0x64, 0x80, 0x84, 0xa2, 0x26,
	0xb5, 0xa0, 0x92, 0x76, 0x7b, 0x06, 0x06, 0xc5, 0x7c, 0x8b, 0x8d, 0x60, 0xae, 0x04, 0x92, 0xfb,
	0xd9, 0xad, 0x27, 0x4b, 0x73, 0xfb, 0xc1, 0x15, 0x14, 0x3c, 0x24, 0xfb, 0xd0, 0xc8, 0xf6, 0x98,
	0xe4, 0xbd, 0x29, 0xf3, 0xf8, 0xb4, 0x77, 0x6e, 0xaf, 0x5e, 0x82, 0xe5, 0x21, 0xd9, 0x02, 0x18,
	0xf7, 0x56, 0x99, 0xdb, 0x66, 0x1a, 0xbd, 0xf6, 0xdd, 0x19, 0x18, 0x1e, 0x92, 0x2f, 0xa1, 0x9a,
	0xb6, 0x25, 0x19, 0x67, 0xd0, 0xdb, 0x99, 0x76, 0x6b, 0x3a, 0x82, 0x87, 0x64, 0x08, 0x77, 0x66,
	0x34, 0x0d, 0xe4, 0xd1, 0x75, 0x1a, 0x8b, 0xb3, 0xf6, 0xe3, 0xeb, 0xf5, 0x1f, 0xe4, 0xa7, 0x70,
	0x73, 0x4a, 0xb1, 0x27, 0x39, 0xad, 0x4f, 0x69, 0x13, 0xda, 0xc6, 0x55, 0x24, 0x3c, 0x24, 0x3f,
	0x84, 0x7a, 0xa6, 0xee, 0x13, 0x3d, 0xa5, 0xe6, 0xfb, 0x86, 0xf6, 0x7b, 0xb3, 0x91, 0x3c, 0x24,
	0x2c, 0x7d, 0x9b, 0xe7, 0xca, 0x3f, 0x79, 0x38, 0xe9, 0x22, 0x93, 0x1d, 0x45, 0xfb, 0xd1, 0x35,
	0xa8, 0x78, 0x48, 0x7a, 0x38, 0x2d, 0x98, 0xac, 0xb1, 0xe4, 0x83, 0x2c, 0xff, 0xd4, 0x7a, 0xde,
	0x7e, 0x78, 0x35, 0x11, 0x0f, 0xc9, 0x1b, 0xec, 0x08, 0xa6, 0x16, 0x29, 0xf2, 0x38, 0x2b, 0x61,
	0x56, 0x5d, 0x6e, 0x7f, 0x78, 0x2d, 0x3a, 0x1e, 0x92, 0x33, 0x6c, 0x9b, 0x66, 0x54, 0x44, 0xb2,
	0x76, 0x0d, 0x31, 0x58, 0xb5, 0xdb, 0x4f, 0xae, 0x49, 0xc9, 0x43, 0x15, 0xeb, 0xb9, 0x5f, 0xc1,
	0xf2, 0xb1, 0x3e, 0xf9, 0x23, 0x73, 0x3e, 0xd6, 0xa7, 0xfd, 0xd8, 0xbc, 0x01, 0xf3, 0x5d, 0xda,
	0xe3, 0x84, 0x64, 0xff, 0x5d, 0x80, 0x3e, 0x7f, 0x73, 0x02, 0xc6, 0x43, 0xf2, 0x29, 0x94, 0xe5,
	0x2f, 0xfe, 0x64, 0x25, 0xff, 0x2b, 0x3c, 0x32, 0xdd, 0x9a, 0x02, 0xe5, 0xe1, 0xf6, 0x27, 0xdf,
	0x7d, 0x34, 0x08, 0x5c, 0xea, 0x0f, 0xd6, 0x3f, 0xdd, 0x8c, 0xe3, 0xf5, 0x7e, 0xe0, 0x6d, 0xe0,
	0xdf, 0x20, 0xfa, 0x81, 0xbb, 0xc1, 0x59, 0x74, 0xee, 0xf4, 0x19, 0x1f, 0xff, 0x45, 0xa2, 0x57,
	0x46, 0xe4, 0xf3, 0x7f, 0x05, 0x00, 0x00, 0xff, 0xff, 0x86, 0xd0, 0x30, 0xe2, 0x40, 0x21, 0x00,
	0x00,
}
