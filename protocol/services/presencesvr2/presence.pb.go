// Code generated by protoc-gen-gogo.
// source: services/presencesvr2/presence.proto
// DO NOT EDIT!

/*
	Package Presence is a generated protocol buffer package.

	namespace

	It is generated from these files:
		services/presencesvr2/presence.proto

	It has these top-level messages:
		Pres
		Proxy
		UpdatePresReq
		UpdatePresResp
		GetPresReq
		GetPresResp
		PresStat
		BatchGetPresReq
		BatchGetPresResp
		StatPresReq
		StatPresResp
		SessionModel
		Session
		CreateSessionReq
		CreateSessionResp
		GetSessionReq
		GetSessionResp
		RemoveSessionReq
		RemoveSessionResp
*/
package Presence

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 专用错误码从-20开始
type ERR_PRES int32

const (
	ERR_PRES_ERR_PRES_EXIST        ERR_PRES = -1800
	ERR_PRES_ERR_SESSION_NOT_FOUND ERR_PRES = -1801
	ERR_PRES_ERR_SESSION_EXPIRED   ERR_PRES = -1802
)

var ERR_PRES_name = map[int32]string{
	-1800: "ERR_PRES_EXIST",
	-1801: "ERR_SESSION_NOT_FOUND",
	-1802: "ERR_SESSION_EXPIRED",
}
var ERR_PRES_value = map[string]int32{
	"ERR_PRES_EXIST":        -1800,
	"ERR_SESSION_NOT_FOUND": -1801,
	"ERR_SESSION_EXPIRED":   -1802,
}

func (x ERR_PRES) Enum() *ERR_PRES {
	p := new(ERR_PRES)
	*p = x
	return p
}
func (x ERR_PRES) String() string {
	return proto.EnumName(ERR_PRES_name, int32(x))
}
func (x *ERR_PRES) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ERR_PRES_value, data, "ERR_PRES")
	if err != nil {
		return err
	}
	*x = ERR_PRES(value)
	return nil
}
func (ERR_PRES) EnumDescriptor() ([]byte, []int) { return fileDescriptorPresence, []int{0} }

type PRES_TYPE int32

const (
	PRES_TYPE_PRES_TYPE_ANDROID PRES_TYPE = 0
	PRES_TYPE_PRES_TYPE_IOS     PRES_TYPE = 1
	PRES_TYPE_PRES_TYPE_PC      PRES_TYPE = 2
)

var PRES_TYPE_name = map[int32]string{
	0: "PRES_TYPE_ANDROID",
	1: "PRES_TYPE_IOS",
	2: "PRES_TYPE_PC",
}
var PRES_TYPE_value = map[string]int32{
	"PRES_TYPE_ANDROID": 0,
	"PRES_TYPE_IOS":     1,
	"PRES_TYPE_PC":      2,
}

func (x PRES_TYPE) Enum() *PRES_TYPE {
	p := new(PRES_TYPE)
	*p = x
	return p
}
func (x PRES_TYPE) String() string {
	return proto.EnumName(PRES_TYPE_name, int32(x))
}
func (x *PRES_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PRES_TYPE_value, data, "PRES_TYPE")
	if err != nil {
		return err
	}
	*x = PRES_TYPE(value)
	return nil
}
func (PRES_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorPresence, []int{1} }

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list
type Pres struct {
	ProxyIp      uint32 `protobuf:"varint,1,req,name=proxy_ip,json=proxyIp" json:"proxy_ip"`
	ProxyPort    uint32 `protobuf:"varint,2,req,name=proxy_port,json=proxyPort" json:"proxy_port"`
	Uid          uint32 `protobuf:"varint,3,req,name=uid" json:"uid"`
	DeviceId     []byte `protobuf:"bytes,4,req,name=device_id,json=deviceId" json:"device_id"`
	Status       uint32 `protobuf:"varint,5,req,name=status" json:"status"`
	ClientId     uint32 `protobuf:"varint,6,req,name=client_id,json=clientId" json:"client_id"`
	ClientIp     uint32 `protobuf:"varint,7,req,name=client_ip,json=clientIp" json:"client_ip"`
	OnlineTime   uint32 `protobuf:"varint,8,req,name=online_time,json=onlineTime" json:"online_time"`
	TerminalType uint32 `protobuf:"varint,9,opt,name=terminal_type,json=terminalType" json:"terminal_type"`
}

func (m *Pres) Reset()                    { *m = Pres{} }
func (m *Pres) String() string            { return proto.CompactTextString(m) }
func (*Pres) ProtoMessage()               {}
func (*Pres) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{0} }

func (m *Pres) GetProxyIp() uint32 {
	if m != nil {
		return m.ProxyIp
	}
	return 0
}

func (m *Pres) GetProxyPort() uint32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

func (m *Pres) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Pres) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *Pres) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *Pres) GetClientId() uint32 {
	if m != nil {
		return m.ClientId
	}
	return 0
}

func (m *Pres) GetClientIp() uint32 {
	if m != nil {
		return m.ClientIp
	}
	return 0
}

func (m *Pres) GetOnlineTime() uint32 {
	if m != nil {
		return m.OnlineTime
	}
	return 0
}

func (m *Pres) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

type Proxy struct {
	ProxyIp   uint32 `protobuf:"varint,1,req,name=proxy_ip,json=proxyIp" json:"proxy_ip"`
	ProxyPort uint32 `protobuf:"varint,2,req,name=proxy_port,json=proxyPort" json:"proxy_port"`
	BootTime  uint32 `protobuf:"varint,3,req,name=boot_time,json=bootTime" json:"boot_time"`
}

func (m *Proxy) Reset()                    { *m = Proxy{} }
func (m *Proxy) String() string            { return proto.CompactTextString(m) }
func (*Proxy) ProtoMessage()               {}
func (*Proxy) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{1} }

func (m *Proxy) GetProxyIp() uint32 {
	if m != nil {
		return m.ProxyIp
	}
	return 0
}

func (m *Proxy) GetProxyPort() uint32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

func (m *Proxy) GetBootTime() uint32 {
	if m != nil {
		return m.BootTime
	}
	return 0
}

// ////////////////
type UpdatePresReq struct {
	// required uint32 uid = 1;
	// required uint32 prestype = 2;
	Proxy    *Proxy  `protobuf:"bytes,1,req,name=proxy" json:"proxy,omitempty"`
	PresList []*Pres `protobuf:"bytes,4,rep,name=pres_list,json=presList" json:"pres_list,omitempty"`
}

func (m *UpdatePresReq) Reset()                    { *m = UpdatePresReq{} }
func (m *UpdatePresReq) String() string            { return proto.CompactTextString(m) }
func (*UpdatePresReq) ProtoMessage()               {}
func (*UpdatePresReq) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{2} }

func (m *UpdatePresReq) GetProxy() *Proxy {
	if m != nil {
		return m.Proxy
	}
	return nil
}

func (m *UpdatePresReq) GetPresList() []*Pres {
	if m != nil {
		return m.PresList
	}
	return nil
}

type UpdatePresResp struct {
}

func (m *UpdatePresResp) Reset()                    { *m = UpdatePresResp{} }
func (m *UpdatePresResp) String() string            { return proto.CompactTextString(m) }
func (*UpdatePresResp) ProtoMessage()               {}
func (*UpdatePresResp) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{3} }

// ////////////////
type GetPresReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetPresReq) Reset()                    { *m = GetPresReq{} }
func (m *GetPresReq) String() string            { return proto.CompactTextString(m) }
func (*GetPresReq) ProtoMessage()               {}
func (*GetPresReq) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{4} }

func (m *GetPresReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPresResp struct {
	PresList []*Pres `protobuf:"bytes,3,rep,name=pres_list,json=presList" json:"pres_list,omitempty"`
}

func (m *GetPresResp) Reset()                    { *m = GetPresResp{} }
func (m *GetPresResp) String() string            { return proto.CompactTextString(m) }
func (*GetPresResp) ProtoMessage()               {}
func (*GetPresResp) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{5} }

func (m *GetPresResp) GetPresList() []*Pres {
	if m != nil {
		return m.PresList
	}
	return nil
}

// ////////////////
type PresStat struct {
	ProxyIp         uint32 `protobuf:"varint,1,req,name=proxy_ip,json=proxyIp" json:"proxy_ip"`
	ProxyPort       uint32 `protobuf:"varint,2,req,name=proxy_port,json=proxyPort" json:"proxy_port"`
	OnlineCount     uint32 `protobuf:"varint,3,req,name=online_count,json=onlineCount" json:"online_count"`
	MaxOnlineCount  uint32 `protobuf:"varint,4,req,name=max_online_count,json=maxOnlineCount" json:"max_online_count"`
	MaxOnlineAtTime uint32 `protobuf:"varint,5,req,name=max_online_at_time,json=maxOnlineAtTime" json:"max_online_at_time"`
}

func (m *PresStat) Reset()                    { *m = PresStat{} }
func (m *PresStat) String() string            { return proto.CompactTextString(m) }
func (*PresStat) ProtoMessage()               {}
func (*PresStat) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{6} }

func (m *PresStat) GetProxyIp() uint32 {
	if m != nil {
		return m.ProxyIp
	}
	return 0
}

func (m *PresStat) GetProxyPort() uint32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

func (m *PresStat) GetOnlineCount() uint32 {
	if m != nil {
		return m.OnlineCount
	}
	return 0
}

func (m *PresStat) GetMaxOnlineCount() uint32 {
	if m != nil {
		return m.MaxOnlineCount
	}
	return 0
}

func (m *PresStat) GetMaxOnlineAtTime() uint32 {
	if m != nil {
		return m.MaxOnlineAtTime
	}
	return 0
}

type BatchGetPresReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatchGetPresReq) Reset()                    { *m = BatchGetPresReq{} }
func (m *BatchGetPresReq) String() string            { return proto.CompactTextString(m) }
func (*BatchGetPresReq) ProtoMessage()               {}
func (*BatchGetPresReq) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{7} }

func (m *BatchGetPresReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetPresResp struct {
	PresList []*Pres `protobuf:"bytes,3,rep,name=pres_list,json=presList" json:"pres_list,omitempty"`
}

func (m *BatchGetPresResp) Reset()                    { *m = BatchGetPresResp{} }
func (m *BatchGetPresResp) String() string            { return proto.CompactTextString(m) }
func (*BatchGetPresResp) ProtoMessage()               {}
func (*BatchGetPresResp) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{8} }

func (m *BatchGetPresResp) GetPresList() []*Pres {
	if m != nil {
		return m.PresList
	}
	return nil
}

type StatPresReq struct {
}

func (m *StatPresReq) Reset()                    { *m = StatPresReq{} }
func (m *StatPresReq) String() string            { return proto.CompactTextString(m) }
func (*StatPresReq) ProtoMessage()               {}
func (*StatPresReq) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{9} }

type StatPresResp struct {
	PresStatList []*PresStat `protobuf:"bytes,2,rep,name=pres_stat_list,json=presStatList" json:"pres_stat_list,omitempty"`
}

func (m *StatPresResp) Reset()                    { *m = StatPresResp{} }
func (m *StatPresResp) String() string            { return proto.CompactTextString(m) }
func (*StatPresResp) ProtoMessage()               {}
func (*StatPresResp) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{10} }

func (m *StatPresResp) GetPresStatList() []*PresStat {
	if m != nil {
		return m.PresStatList
	}
	return nil
}

// 存储
type SessionModel struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Appid   uint32 `protobuf:"varint,2,req,name=appid" json:"appid"`
	Expired uint32 `protobuf:"varint,3,req,name=expired" json:"expired"`
	Skey    string `protobuf:"bytes,4,req,name=skey" json:"skey"`
}

func (m *SessionModel) Reset()                    { *m = SessionModel{} }
func (m *SessionModel) String() string            { return proto.CompactTextString(m) }
func (*SessionModel) ProtoMessage()               {}
func (*SessionModel) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{11} }

func (m *SessionModel) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SessionModel) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

func (m *SessionModel) GetExpired() uint32 {
	if m != nil {
		return m.Expired
	}
	return 0
}

func (m *SessionModel) GetSkey() string {
	if m != nil {
		return m.Skey
	}
	return ""
}

// 接口
type Session struct {
	Uid         uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ExpiredTime uint32 `protobuf:"varint,2,req,name=expired_time,json=expiredTime" json:"expired_time"`
	Skey        string `protobuf:"bytes,3,req,name=skey" json:"skey"`
	Appid       uint32 `protobuf:"varint,4,opt,name=appid" json:"appid"`
}

func (m *Session) Reset()                    { *m = Session{} }
func (m *Session) String() string            { return proto.CompactTextString(m) }
func (*Session) ProtoMessage()               {}
func (*Session) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{12} }

func (m *Session) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Session) GetExpiredTime() uint32 {
	if m != nil {
		return m.ExpiredTime
	}
	return 0
}

func (m *Session) GetSkey() string {
	if m != nil {
		return m.Skey
	}
	return ""
}

func (m *Session) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

type CreateSessionReq struct {
	Uid   uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Appid uint32 `protobuf:"varint,2,opt,name=appid" json:"appid"`
}

func (m *CreateSessionReq) Reset()                    { *m = CreateSessionReq{} }
func (m *CreateSessionReq) String() string            { return proto.CompactTextString(m) }
func (*CreateSessionReq) ProtoMessage()               {}
func (*CreateSessionReq) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{13} }

func (m *CreateSessionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CreateSessionReq) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

type CreateSessionResp struct {
	Session *Session `protobuf:"bytes,1,req,name=session" json:"session,omitempty"`
}

func (m *CreateSessionResp) Reset()                    { *m = CreateSessionResp{} }
func (m *CreateSessionResp) String() string            { return proto.CompactTextString(m) }
func (*CreateSessionResp) ProtoMessage()               {}
func (*CreateSessionResp) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{14} }

func (m *CreateSessionResp) GetSession() *Session {
	if m != nil {
		return m.Session
	}
	return nil
}

type GetSessionReq struct {
	Uid   uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Appid uint32 `protobuf:"varint,2,opt,name=appid" json:"appid"`
}

func (m *GetSessionReq) Reset()                    { *m = GetSessionReq{} }
func (m *GetSessionReq) String() string            { return proto.CompactTextString(m) }
func (*GetSessionReq) ProtoMessage()               {}
func (*GetSessionReq) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{15} }

func (m *GetSessionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetSessionReq) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

type GetSessionResp struct {
	Session *Session `protobuf:"bytes,1,req,name=session" json:"session,omitempty"`
}

func (m *GetSessionResp) Reset()                    { *m = GetSessionResp{} }
func (m *GetSessionResp) String() string            { return proto.CompactTextString(m) }
func (*GetSessionResp) ProtoMessage()               {}
func (*GetSessionResp) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{16} }

func (m *GetSessionResp) GetSession() *Session {
	if m != nil {
		return m.Session
	}
	return nil
}

type RemoveSessionReq struct {
	Uid   uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Appid uint32 `protobuf:"varint,2,opt,name=appid" json:"appid"`
}

func (m *RemoveSessionReq) Reset()                    { *m = RemoveSessionReq{} }
func (m *RemoveSessionReq) String() string            { return proto.CompactTextString(m) }
func (*RemoveSessionReq) ProtoMessage()               {}
func (*RemoveSessionReq) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{17} }

func (m *RemoveSessionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RemoveSessionReq) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

type RemoveSessionResp struct {
}

func (m *RemoveSessionResp) Reset()                    { *m = RemoveSessionResp{} }
func (m *RemoveSessionResp) String() string            { return proto.CompactTextString(m) }
func (*RemoveSessionResp) ProtoMessage()               {}
func (*RemoveSessionResp) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{18} }

func init() {
	proto.RegisterType((*Pres)(nil), "Presence.Pres")
	proto.RegisterType((*Proxy)(nil), "Presence.Proxy")
	proto.RegisterType((*UpdatePresReq)(nil), "Presence.UpdatePresReq")
	proto.RegisterType((*UpdatePresResp)(nil), "Presence.UpdatePresResp")
	proto.RegisterType((*GetPresReq)(nil), "Presence.GetPresReq")
	proto.RegisterType((*GetPresResp)(nil), "Presence.GetPresResp")
	proto.RegisterType((*PresStat)(nil), "Presence.PresStat")
	proto.RegisterType((*BatchGetPresReq)(nil), "Presence.BatchGetPresReq")
	proto.RegisterType((*BatchGetPresResp)(nil), "Presence.BatchGetPresResp")
	proto.RegisterType((*StatPresReq)(nil), "Presence.StatPresReq")
	proto.RegisterType((*StatPresResp)(nil), "Presence.StatPresResp")
	proto.RegisterType((*SessionModel)(nil), "Presence.SessionModel")
	proto.RegisterType((*Session)(nil), "Presence.Session")
	proto.RegisterType((*CreateSessionReq)(nil), "Presence.CreateSessionReq")
	proto.RegisterType((*CreateSessionResp)(nil), "Presence.CreateSessionResp")
	proto.RegisterType((*GetSessionReq)(nil), "Presence.GetSessionReq")
	proto.RegisterType((*GetSessionResp)(nil), "Presence.GetSessionResp")
	proto.RegisterType((*RemoveSessionReq)(nil), "Presence.RemoveSessionReq")
	proto.RegisterType((*RemoveSessionResp)(nil), "Presence.RemoveSessionResp")
	proto.RegisterEnum("Presence.ERR_PRES", ERR_PRES_name, ERR_PRES_value)
	proto.RegisterEnum("Presence.PRES_TYPE", PRES_TYPE_name, PRES_TYPE_value)
}
func (m *Pres) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Pres) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ProxyIp))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ProxyPort))
	dAtA[i] = 0x18
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Uid))
	if m.DeviceId != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintPresence(dAtA, i, uint64(len(m.DeviceId)))
		i += copy(dAtA[i:], m.DeviceId)
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x30
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ClientId))
	dAtA[i] = 0x38
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ClientIp))
	dAtA[i] = 0x40
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.OnlineTime))
	dAtA[i] = 0x48
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.TerminalType))
	return i, nil
}

func (m *Proxy) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Proxy) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ProxyIp))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ProxyPort))
	dAtA[i] = 0x18
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.BootTime))
	return i, nil
}

func (m *UpdatePresReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdatePresReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Proxy == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("proxy")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintPresence(dAtA, i, uint64(m.Proxy.Size()))
		n1, err := m.Proxy.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if len(m.PresList) > 0 {
		for _, msg := range m.PresList {
			dAtA[i] = 0x22
			i++
			i = encodeVarintPresence(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UpdatePresResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdatePresResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetPresReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetPresResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PresList) > 0 {
		for _, msg := range m.PresList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintPresence(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *PresStat) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PresStat) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ProxyIp))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ProxyPort))
	dAtA[i] = 0x18
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.OnlineCount))
	dAtA[i] = 0x20
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.MaxOnlineCount))
	dAtA[i] = 0x28
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.MaxOnlineAtTime))
	return i, nil
}

func (m *BatchGetPresReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetPresReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintPresence(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchGetPresResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetPresResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PresList) > 0 {
		for _, msg := range m.PresList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintPresence(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *StatPresReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StatPresReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *StatPresResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StatPresResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PresStatList) > 0 {
		for _, msg := range m.PresStatList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintPresence(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SessionModel) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SessionModel) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Appid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Expired))
	dAtA[i] = 0x22
	i++
	i = encodeVarintPresence(dAtA, i, uint64(len(m.Skey)))
	i += copy(dAtA[i:], m.Skey)
	return i, nil
}

func (m *Session) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Session) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ExpiredTime))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintPresence(dAtA, i, uint64(len(m.Skey)))
	i += copy(dAtA[i:], m.Skey)
	dAtA[i] = 0x20
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Appid))
	return i, nil
}

func (m *CreateSessionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateSessionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Appid))
	return i, nil
}

func (m *CreateSessionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateSessionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Session == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("session")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintPresence(dAtA, i, uint64(m.Session.Size()))
		n2, err := m.Session.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *GetSessionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSessionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Appid))
	return i, nil
}

func (m *GetSessionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSessionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Session == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("session")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintPresence(dAtA, i, uint64(m.Session.Size()))
		n3, err := m.Session.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *RemoveSessionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveSessionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Appid))
	return i, nil
}

func (m *RemoveSessionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveSessionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Presence(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Presence(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintPresence(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *Pres) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPresence(uint64(m.ProxyIp))
	n += 1 + sovPresence(uint64(m.ProxyPort))
	n += 1 + sovPresence(uint64(m.Uid))
	if m.DeviceId != nil {
		l = len(m.DeviceId)
		n += 1 + l + sovPresence(uint64(l))
	}
	n += 1 + sovPresence(uint64(m.Status))
	n += 1 + sovPresence(uint64(m.ClientId))
	n += 1 + sovPresence(uint64(m.ClientIp))
	n += 1 + sovPresence(uint64(m.OnlineTime))
	n += 1 + sovPresence(uint64(m.TerminalType))
	return n
}

func (m *Proxy) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPresence(uint64(m.ProxyIp))
	n += 1 + sovPresence(uint64(m.ProxyPort))
	n += 1 + sovPresence(uint64(m.BootTime))
	return n
}

func (m *UpdatePresReq) Size() (n int) {
	var l int
	_ = l
	if m.Proxy != nil {
		l = m.Proxy.Size()
		n += 1 + l + sovPresence(uint64(l))
	}
	if len(m.PresList) > 0 {
		for _, e := range m.PresList {
			l = e.Size()
			n += 1 + l + sovPresence(uint64(l))
		}
	}
	return n
}

func (m *UpdatePresResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetPresReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPresence(uint64(m.Uid))
	return n
}

func (m *GetPresResp) Size() (n int) {
	var l int
	_ = l
	if len(m.PresList) > 0 {
		for _, e := range m.PresList {
			l = e.Size()
			n += 1 + l + sovPresence(uint64(l))
		}
	}
	return n
}

func (m *PresStat) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPresence(uint64(m.ProxyIp))
	n += 1 + sovPresence(uint64(m.ProxyPort))
	n += 1 + sovPresence(uint64(m.OnlineCount))
	n += 1 + sovPresence(uint64(m.MaxOnlineCount))
	n += 1 + sovPresence(uint64(m.MaxOnlineAtTime))
	return n
}

func (m *BatchGetPresReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovPresence(uint64(e))
		}
	}
	return n
}

func (m *BatchGetPresResp) Size() (n int) {
	var l int
	_ = l
	if len(m.PresList) > 0 {
		for _, e := range m.PresList {
			l = e.Size()
			n += 1 + l + sovPresence(uint64(l))
		}
	}
	return n
}

func (m *StatPresReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *StatPresResp) Size() (n int) {
	var l int
	_ = l
	if len(m.PresStatList) > 0 {
		for _, e := range m.PresStatList {
			l = e.Size()
			n += 1 + l + sovPresence(uint64(l))
		}
	}
	return n
}

func (m *SessionModel) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPresence(uint64(m.Uid))
	n += 1 + sovPresence(uint64(m.Appid))
	n += 1 + sovPresence(uint64(m.Expired))
	l = len(m.Skey)
	n += 1 + l + sovPresence(uint64(l))
	return n
}

func (m *Session) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPresence(uint64(m.Uid))
	n += 1 + sovPresence(uint64(m.ExpiredTime))
	l = len(m.Skey)
	n += 1 + l + sovPresence(uint64(l))
	n += 1 + sovPresence(uint64(m.Appid))
	return n
}

func (m *CreateSessionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPresence(uint64(m.Uid))
	n += 1 + sovPresence(uint64(m.Appid))
	return n
}

func (m *CreateSessionResp) Size() (n int) {
	var l int
	_ = l
	if m.Session != nil {
		l = m.Session.Size()
		n += 1 + l + sovPresence(uint64(l))
	}
	return n
}

func (m *GetSessionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPresence(uint64(m.Uid))
	n += 1 + sovPresence(uint64(m.Appid))
	return n
}

func (m *GetSessionResp) Size() (n int) {
	var l int
	_ = l
	if m.Session != nil {
		l = m.Session.Size()
		n += 1 + l + sovPresence(uint64(l))
	}
	return n
}

func (m *RemoveSessionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPresence(uint64(m.Uid))
	n += 1 + sovPresence(uint64(m.Appid))
	return n
}

func (m *RemoveSessionResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovPresence(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozPresence(x uint64) (n int) {
	return sovPresence(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *Pres) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Pres: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Pres: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProxyIp", wireType)
			}
			m.ProxyIp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProxyIp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProxyPort", wireType)
			}
			m.ProxyPort = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProxyPort |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = append(m.DeviceId[:0], dAtA[iNdEx:postIndex]...)
			if m.DeviceId == nil {
				m.DeviceId = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientId", wireType)
			}
			m.ClientId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientIp", wireType)
			}
			m.ClientIp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientIp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnlineTime", wireType)
			}
			m.OnlineTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OnlineTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TerminalType", wireType)
			}
			m.TerminalType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TerminalType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("proxy_ip")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("proxy_port")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("client_id")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("client_ip")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("online_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Proxy) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Proxy: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Proxy: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProxyIp", wireType)
			}
			m.ProxyIp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProxyIp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProxyPort", wireType)
			}
			m.ProxyPort = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProxyPort |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BootTime", wireType)
			}
			m.BootTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BootTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("proxy_ip")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("proxy_port")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("boot_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdatePresReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdatePresReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdatePresReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Proxy", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Proxy == nil {
				m.Proxy = &Proxy{}
			}
			if err := m.Proxy.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PresList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PresList = append(m.PresList, &Pres{})
			if err := m.PresList[len(m.PresList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("proxy")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdatePresResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdatePresResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdatePresResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PresList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PresList = append(m.PresList, &Pres{})
			if err := m.PresList[len(m.PresList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PresStat) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PresStat: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PresStat: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProxyIp", wireType)
			}
			m.ProxyIp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProxyIp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProxyPort", wireType)
			}
			m.ProxyPort = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProxyPort |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnlineCount", wireType)
			}
			m.OnlineCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OnlineCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaxOnlineCount", wireType)
			}
			m.MaxOnlineCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxOnlineCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaxOnlineAtTime", wireType)
			}
			m.MaxOnlineAtTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxOnlineAtTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("proxy_ip")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("proxy_port")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("online_count")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("max_online_count")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("max_online_at_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetPresReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetPresReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetPresReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPresence
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPresence
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthPresence
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPresence
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetPresResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetPresResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetPresResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PresList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PresList = append(m.PresList, &Pres{})
			if err := m.PresList[len(m.PresList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StatPresReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StatPresReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StatPresReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StatPresResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StatPresResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StatPresResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PresStatList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PresStatList = append(m.PresStatList, &PresStat{})
			if err := m.PresStatList[len(m.PresStatList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SessionModel) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SessionModel: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SessionModel: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			m.Appid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Appid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Expired", wireType)
			}
			m.Expired = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Expired |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Skey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Skey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("appid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("expired")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("skey")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Session) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Session: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Session: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpiredTime", wireType)
			}
			m.ExpiredTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpiredTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Skey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Skey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			m.Appid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Appid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("expired_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("skey")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateSessionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateSessionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateSessionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			m.Appid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Appid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateSessionResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateSessionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateSessionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Session", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Session == nil {
				m.Session = &Session{}
			}
			if err := m.Session.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("session")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSessionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSessionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSessionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			m.Appid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Appid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSessionResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSessionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSessionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Session", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Session == nil {
				m.Session = &Session{}
			}
			if err := m.Session.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("session")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveSessionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveSessionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveSessionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			m.Appid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Appid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveSessionResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveSessionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveSessionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipPresence(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthPresence
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowPresence
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipPresence(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthPresence = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowPresence   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("services/presencesvr2/presence.proto", fileDescriptorPresence) }

var fileDescriptorPresence = []byte{
	// 1132 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x56, 0x4f, 0x73, 0xdb, 0x44,
	0x14, 0x8f, 0x2c, 0x27, 0x96, 0x9f, 0xff, 0x29, 0x5b, 0x5a, 0x54, 0xb7, 0x93, 0xaa, 0x22, 0xa1,
	0xa1, 0x10, 0x07, 0x72, 0x62, 0x32, 0x21, 0xd0, 0x24, 0x4e, 0x30, 0x7f, 0x62, 0x23, 0xa7, 0x33,
	0xe5, 0xc0, 0x68, 0x5c, 0x6b, 0x19, 0x76, 0x62, 0x5b, 0x5b, 0xed, 0x2a, 0xe3, 0xcc, 0x70, 0xe8,
	0xb1, 0xc3, 0x89, 0xe1, 0x33, 0xe4, 0xca, 0x47, 0xe0, 0xde, 0x23, 0x1f, 0x80, 0x61, 0x20, 0x5c,
	0x72, 0xe5, 0x02, 0xdc, 0x60, 0x56, 0x92, 0xad, 0x95, 0xe3, 0x24, 0x74, 0x5a, 0x9f, 0xbc, 0xef,
	0xfd, 0xf6, 0xfd, 0xde, 0xfe, 0xde, 0xbe, 0xb7, 0x82, 0x45, 0x86, 0xfd, 0x23, 0xd2, 0xc5, 0x6c,
	0x95, 0xfa, 0x98, 0xe1, 0x41, 0x17, 0xb3, 0x23, 0x7f, 0x6d, 0xbc, 0xa8, 0x51, 0xdf, 0xe3, 0x1e,
	0xd2, 0x5a, 0xf1, 0xba, 0xba, 0xd8, 0xf5, 0xfa, 0x7d, 0x6f, 0xb0, 0xca, 0x7b, 0x47, 0x94, 0x74,
	0x0f, 0x7b, 0x78, 0x95, 0x1d, 0x3e, 0x0e, 0x48, 0x8f, 0x93, 0x01, 0x3f, 0xa6, 0x31, 0xde, 0xfa,
	0x29, 0x03, 0x59, 0xb1, 0x05, 0xdd, 0x01, 0x8d, 0xfa, 0xde, 0xf0, 0xd8, 0x21, 0xd4, 0x50, 0xcc,
	0xcc, 0x72, 0x69, 0x2b, 0xfb, 0xfc, 0xd7, 0x3b, 0x33, 0x76, 0x2e, 0xb4, 0x36, 0x28, 0x7a, 0x03,
	0x20, 0x02, 0x50, 0xcf, 0xe7, 0x46, 0x46, 0x82, 0xe4, 0x43, 0x7b, 0xcb, 0xf3, 0x39, 0xba, 0x01,
	0x6a, 0x40, 0x5c, 0x43, 0x95, 0xbc, 0xc2, 0x80, 0xee, 0x42, 0xde, 0xc5, 0x22, 0x7b, 0x87, 0xb8,
	0x46, 0xd6, 0xcc, 0x2c, 0x17, 0x63, 0xaf, 0x16, 0x99, 0x1b, 0x2e, 0xba, 0x0d, 0x73, 0x8c, 0x77,
	0x78, 0xc0, 0x8c, 0x59, 0x69, 0x77, 0x6c, 0x13, 0x01, 0xba, 0x3d, 0x82, 0x07, 0x5c, 0x04, 0x98,
	0x93, 0x00, 0x5a, 0x64, 0x6e, 0xb8, 0x32, 0x84, 0x1a, 0xb9, 0x29, 0x10, 0x8a, 0x96, 0xa0, 0xe0,
	0x0d, 0x7a, 0x64, 0x80, 0x1d, 0x4e, 0xfa, 0xd8, 0xd0, 0x24, 0x10, 0x44, 0x8e, 0x03, 0xd2, 0xc7,
	0xe8, 0x2d, 0x28, 0x71, 0xec, 0xf7, 0xc9, 0xa0, 0xd3, 0x73, 0x84, 0x56, 0x46, 0xde, 0x54, 0xc6,
	0xc0, 0xe2, 0xc8, 0x75, 0x70, 0x4c, 0xb1, 0x45, 0x61, 0xb6, 0x25, 0x4e, 0xff, 0x8a, 0xf4, 0xbb,
	0x0b, 0xf9, 0xc7, 0x9e, 0xc7, 0xa3, 0xf4, 0x64, 0x15, 0x35, 0x61, 0x16, 0xc9, 0x59, 0x5d, 0x28,
	0x3d, 0xa4, 0x6e, 0x87, 0x63, 0x51, 0x36, 0x1b, 0x3f, 0x41, 0x4b, 0x30, 0x1b, 0x06, 0x08, 0x69,
	0x0b, 0x6b, 0x95, 0xda, 0xe8, 0x0a, 0xd4, 0xc2, 0xcc, 0xec, 0xc8, 0x8b, 0xde, 0x86, 0xbc, 0xb8,
	0x2b, 0x4e, 0x8f, 0x30, 0x6e, 0x64, 0x4d, 0x75, 0xb9, 0xb0, 0x56, 0x96, 0xa1, 0x98, 0xd9, 0x9a,
	0x00, 0x7c, 0x46, 0x18, 0xb7, 0x74, 0x28, 0xcb, 0x24, 0x8c, 0x5a, 0x8b, 0x00, 0x7b, 0x98, 0x8f,
	0x38, 0xe3, 0x3a, 0x2b, 0x13, 0x75, 0xb6, 0xd6, 0xa1, 0x30, 0x46, 0x31, 0x9a, 0xe6, 0x54, 0xaf,
	0xe0, 0xfc, 0x45, 0x81, 0xf0, 0xf6, 0xb6, 0x79, 0x87, 0xbf, 0x22, 0x39, 0xef, 0x41, 0x31, 0xae,
	0x77, 0xd7, 0x0b, 0x06, 0x3c, 0xa5, 0x68, 0x7c, 0x13, 0xb6, 0x85, 0x03, 0xd5, 0x40, 0xef, 0x77,
	0x86, 0x4e, 0x0a, 0x9c, 0x95, 0xc0, 0xe5, 0x7e, 0x67, 0xd8, 0x94, 0xf0, 0xef, 0x01, 0x92, 0xf0,
	0x9d, 0xb8, 0x60, 0xf2, 0xc5, 0xad, 0x8c, 0x77, 0x3c, 0x88, 0xea, 0xf6, 0x0e, 0x54, 0xb6, 0x3a,
	0xbc, 0xfb, 0x8d, 0xa4, 0xe2, 0x4d, 0xd0, 0x02, 0xe2, 0x46, 0xea, 0x28, 0xa6, 0xba, 0x5c, 0xb2,
	0x73, 0x01, 0x71, 0x43, 0x31, 0x3e, 0x04, 0x3d, 0x8d, 0x7e, 0x51, 0x35, 0x4b, 0x50, 0x10, 0x42,
	0xc6, 0x54, 0xd6, 0xc7, 0x50, 0x4c, 0x96, 0x8c, 0xa2, 0xf7, 0xa1, 0x1c, 0xc6, 0x12, 0xed, 0x15,
	0x05, 0xcc, 0x84, 0x01, 0x51, 0x3a, 0xa0, 0xd8, 0x63, 0x17, 0x69, 0xfc, 0x2f, 0x0c, 0xfc, 0x2d,
	0x14, 0xdb, 0x98, 0x31, 0xe2, 0x0d, 0x3e, 0xf7, 0x5c, 0xdc, 0xbb, 0xe8, 0x2a, 0xa0, 0x2a, 0xcc,
	0x76, 0x28, 0x25, 0x6e, 0xaa, 0x36, 0x91, 0x09, 0x2d, 0x40, 0x0e, 0x0f, 0x29, 0xf1, 0x71, 0x7a,
	0x54, 0x8c, 0x8c, 0xc8, 0x80, 0x2c, 0x3b, 0xc4, 0xc7, 0x61, 0x09, 0xf2, 0xb1, 0x33, 0xb4, 0x58,
	0x4f, 0x15, 0xc8, 0xc5, 0xf4, 0x17, 0x32, 0xdf, 0x83, 0x62, 0x1c, 0x28, 0x2a, 0x8b, 0x9c, 0x40,
	0x21, 0xf6, 0x84, 0x7d, 0x3e, 0xa2, 0x51, 0x27, 0x69, 0x92, 0xe4, 0xb3, 0x52, 0xe7, 0x47, 0x26,
	0x6b, 0x17, 0xf4, 0x6d, 0x1f, 0x77, 0x38, 0x8e, 0xf3, 0xb8, 0xa4, 0x1f, 0x64, 0x11, 0xce, 0xc5,
	0xf9, 0x08, 0xe6, 0x27, 0xe2, 0x84, 0x35, 0xce, 0xb1, 0x68, 0x19, 0xb7, 0xf3, 0x7c, 0x52, 0x90,
	0x11, 0x6e, 0x84, 0xb0, 0xb6, 0xa1, 0xb4, 0x87, 0xf9, 0x4b, 0xa6, 0xf1, 0x01, 0x94, 0xe5, 0x20,
	0x2f, 0x9a, 0xc3, 0x2e, 0xe8, 0x36, 0xee, 0x7b, 0x47, 0x2f, 0xab, 0xc6, 0x35, 0x98, 0x9f, 0x88,
	0xc3, 0xe8, 0xfd, 0x3e, 0x68, 0x75, 0xdb, 0x76, 0x5a, 0x76, 0xbd, 0x8d, 0x6e, 0x41, 0x79, 0xf4,
	0xdf, 0xa9, 0x3f, 0x6a, 0xb4, 0x0f, 0xf4, 0x7f, 0xfe, 0xfc, 0x37, 0xfa, 0x29, 0xc8, 0x82, 0xeb,
	0xc2, 0xd9, 0xae, 0xb7, 0xdb, 0x8d, 0xe6, 0xbe, 0xb3, 0xdf, 0x3c, 0x70, 0x76, 0x9b, 0x0f, 0xf7,
	0x77, 0xf4, 0xbf, 0x13, 0x8c, 0x09, 0xd7, 0x64, 0x4c, 0xfd, 0x51, 0xab, 0x61, 0xd7, 0x77, 0xf4,
	0xbf, 0xc6, 0x88, 0xfb, 0x7b, 0x90, 0x0f, 0xc3, 0x1f, 0x7c, 0xd9, 0xaa, 0xa3, 0xeb, 0x30, 0x3f,
	0x5e, 0x38, 0x0f, 0xf6, 0x77, 0xec, 0x66, 0x63, 0x47, 0x9f, 0x41, 0xf3, 0x50, 0x4a, 0xcc, 0x8d,
	0x66, 0x5b, 0x57, 0x90, 0x0e, 0xc5, 0xc4, 0xd4, 0xda, 0xd6, 0x33, 0x6b, 0x3f, 0x6a, 0x30, 0x7e,
	0x88, 0xd1, 0x33, 0x05, 0x20, 0x19, 0xa6, 0xe8, 0xf5, 0x44, 0xcc, 0xd4, 0x1c, 0xaf, 0x1a, 0xd3,
	0x1d, 0x8c, 0x5a, 0x7b, 0x4f, 0x4f, 0xce, 0x54, 0xe5, 0xbb, 0x93, 0x33, 0x55, 0x0b, 0xd6, 0xd9,
	0xba, 0xbb, 0x4e, 0xd6, 0x7f, 0x38, 0x39, 0x53, 0xdf, 0x5d, 0x09, 0xcc, 0x8d, 0x80, 0xb8, 0x9b,
	0xe6, 0x0a, 0x33, 0x37, 0xa2, 0x87, 0x72, 0xd3, 0x5c, 0x71, 0xcd, 0x8d, 0xe8, 0x51, 0x0d, 0x3d,
	0xc4, 0xdc, 0x88, 0x9f, 0xbf, 0x9d, 0x4d, 0xf4, 0x05, 0xe4, 0xe2, 0x81, 0x82, 0x5e, 0x4b, 0xd8,
	0x92, 0x89, 0x54, 0xbd, 0x3e, 0xc5, 0xca, 0xa8, 0x75, 0x53, 0x24, 0x90, 0x11, 0x09, 0x64, 0x82,
	0x90, 0x5a, 0x1b, 0x51, 0xa3, 0x1e, 0x14, 0xe5, 0x41, 0x85, 0x6e, 0x26, 0x11, 0x26, 0xc6, 0x5d,
	0xb5, 0x7a, 0x91, 0x8b, 0x51, 0x6b, 0x49, 0x30, 0xa8, 0x82, 0x21, 0x2b, 0x8e, 0x28, 0x38, 0x50,
	0xea, 0x78, 0x01, 0x71, 0xd7, 0x36, 0xd1, 0x27, 0xa0, 0x8d, 0xc6, 0x18, 0x92, 0x72, 0x95, 0x26,
	0x5d, 0xf5, 0xc6, 0x34, 0x33, 0xa3, 0x56, 0x45, 0x30, 0x64, 0x05, 0xc3, 0x8c, 0x88, 0x3e, 0x83,
	0x30, 0x94, 0x52, 0xfd, 0x87, 0x6e, 0xd7, 0xc6, 0xdf, 0x4a, 0xb5, 0xf6, 0xa7, 0x5b, 0xd1, 0xb7,
	0x52, 0xbd, 0x4f, 0xf9, 0xb1, 0xd3, 0xda, 0xaa, 0xde, 0x4a, 0xe2, 0x9e, 0x6b, 0xdb, 0x48, 0xa0,
	0xd9, 0xa9, 0x02, 0x39, 0xe1, 0xc3, 0xf9, 0xff, 0x38, 0x8c, 0x94, 0xfc, 0xe7, 0x08, 0xe6, 0xa6,
	0x12, 0x7c, 0x0d, 0xa5, 0x54, 0xe7, 0x5c, 0xc1, 0x71, 0xa9, 0x37, 0xe2, 0xc9, 0x4d, 0xe5, 0xf1,
	0xa1, 0x92, 0x3a, 0x78, 0x7d, 0x88, 0xaa, 0x17, 0x6a, 0xf2, 0xe4, 0x72, 0xbd, 0xde, 0x14, 0x34,
	0x5a, 0x5c, 0x6e, 0x1a, 0x12, 0x5d, 0x4b, 0xca, 0x4d, 0xcd, 0x8d, 0x70, 0x28, 0x6c, 0x22, 0x02,
	0xc5, 0x44, 0x88, 0xfa, 0x50, 0x6e, 0x9e, 0xd4, 0xe4, 0xbb, 0x44, 0xb9, 0x90, 0x2a, 0x7f, 0x35,
	0x95, 0x0f, 0x95, 0x94, 0x8c, 0xe9, 0xe3, 0x4d, 0xce, 0x38, 0xf9, 0x78, 0xe7, 0xe6, 0x56, 0xc4,
	0x09, 0x57, 0x72, 0x56, 0xe7, 0x9e, 0x9d, 0x9c, 0xa9, 0xbf, 0x7f, 0xb5, 0xa5, 0x3f, 0x3f, 0x5d,
	0x50, 0x7e, 0x3e, 0x5d, 0x50, 0x7e, 0x3b, 0x5d, 0x50, 0xbe, 0xff, 0x63, 0x61, 0xe6, 0xbf, 0x00,
	0x00, 0x00, 0xff, 0xff, 0x55, 0xd4, 0x93, 0x7b, 0xee, 0x0b, 0x00, 0x00,
}
