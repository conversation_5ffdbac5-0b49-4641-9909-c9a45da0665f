// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-movie/movie-info-mgr.proto

package channel_movie // import "golang.52tt.com/protocol/services/channel-movie"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetMovieCategoryReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMovieCategoryReq) Reset()         { *m = GetMovieCategoryReq{} }
func (m *GetMovieCategoryReq) String() string { return proto.CompactTextString(m) }
func (*GetMovieCategoryReq) ProtoMessage()    {}
func (*GetMovieCategoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{0}
}
func (m *GetMovieCategoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMovieCategoryReq.Unmarshal(m, b)
}
func (m *GetMovieCategoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMovieCategoryReq.Marshal(b, m, deterministic)
}
func (dst *GetMovieCategoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMovieCategoryReq.Merge(dst, src)
}
func (m *GetMovieCategoryReq) XXX_Size() int {
	return xxx_messageInfo_GetMovieCategoryReq.Size(m)
}
func (m *GetMovieCategoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMovieCategoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMovieCategoryReq proto.InternalMessageInfo

type MovieCategoryInfo struct {
	CategoryName         string   `protobuf:"bytes,1,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"`
	CategoryId           uint32   `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MovieCategoryInfo) Reset()         { *m = MovieCategoryInfo{} }
func (m *MovieCategoryInfo) String() string { return proto.CompactTextString(m) }
func (*MovieCategoryInfo) ProtoMessage()    {}
func (*MovieCategoryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{1}
}
func (m *MovieCategoryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MovieCategoryInfo.Unmarshal(m, b)
}
func (m *MovieCategoryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MovieCategoryInfo.Marshal(b, m, deterministic)
}
func (dst *MovieCategoryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MovieCategoryInfo.Merge(dst, src)
}
func (m *MovieCategoryInfo) XXX_Size() int {
	return xxx_messageInfo_MovieCategoryInfo.Size(m)
}
func (m *MovieCategoryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MovieCategoryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MovieCategoryInfo proto.InternalMessageInfo

func (m *MovieCategoryInfo) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

func (m *MovieCategoryInfo) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

type GetMovieCategoryResp struct {
	CategoryList         []*MovieCategoryInfo `protobuf:"bytes,1,rep,name=category_list,json=categoryList,proto3" json:"category_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetMovieCategoryResp) Reset()         { *m = GetMovieCategoryResp{} }
func (m *GetMovieCategoryResp) String() string { return proto.CompactTextString(m) }
func (*GetMovieCategoryResp) ProtoMessage()    {}
func (*GetMovieCategoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{2}
}
func (m *GetMovieCategoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMovieCategoryResp.Unmarshal(m, b)
}
func (m *GetMovieCategoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMovieCategoryResp.Marshal(b, m, deterministic)
}
func (dst *GetMovieCategoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMovieCategoryResp.Merge(dst, src)
}
func (m *GetMovieCategoryResp) XXX_Size() int {
	return xxx_messageInfo_GetMovieCategoryResp.Size(m)
}
func (m *GetMovieCategoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMovieCategoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMovieCategoryResp proto.InternalMessageInfo

func (m *GetMovieCategoryResp) GetCategoryList() []*MovieCategoryInfo {
	if m != nil {
		return m.CategoryList
	}
	return nil
}

type GetMovieRankListReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	CategoryId           uint32   `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Size                 uint32   `protobuf:"varint,4,opt,name=size,proto3" json:"size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMovieRankListReq) Reset()         { *m = GetMovieRankListReq{} }
func (m *GetMovieRankListReq) String() string { return proto.CompactTextString(m) }
func (*GetMovieRankListReq) ProtoMessage()    {}
func (*GetMovieRankListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{3}
}
func (m *GetMovieRankListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMovieRankListReq.Unmarshal(m, b)
}
func (m *GetMovieRankListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMovieRankListReq.Marshal(b, m, deterministic)
}
func (dst *GetMovieRankListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMovieRankListReq.Merge(dst, src)
}
func (m *GetMovieRankListReq) XXX_Size() int {
	return xxx_messageInfo_GetMovieRankListReq.Size(m)
}
func (m *GetMovieRankListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMovieRankListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMovieRankListReq proto.InternalMessageInfo

func (m *GetMovieRankListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMovieRankListReq) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *GetMovieRankListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetMovieRankListReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

type MovieRankInfo struct {
	MovieId              uint32   `protobuf:"varint,1,opt,name=movie_id,json=movieId,proto3" json:"movie_id,omitempty"`
	Playing              bool     `protobuf:"varint,2,opt,name=playing,proto3" json:"playing,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	CoverUrl             string   `protobuf:"bytes,4,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"`
	Duration             uint32   `protobuf:"varint,5,opt,name=duration,proto3" json:"duration,omitempty"`
	MovieType            uint32   `protobuf:"varint,6,opt,name=movie_type,json=movieType,proto3" json:"movie_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MovieRankInfo) Reset()         { *m = MovieRankInfo{} }
func (m *MovieRankInfo) String() string { return proto.CompactTextString(m) }
func (*MovieRankInfo) ProtoMessage()    {}
func (*MovieRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{4}
}
func (m *MovieRankInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MovieRankInfo.Unmarshal(m, b)
}
func (m *MovieRankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MovieRankInfo.Marshal(b, m, deterministic)
}
func (dst *MovieRankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MovieRankInfo.Merge(dst, src)
}
func (m *MovieRankInfo) XXX_Size() int {
	return xxx_messageInfo_MovieRankInfo.Size(m)
}
func (m *MovieRankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MovieRankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MovieRankInfo proto.InternalMessageInfo

func (m *MovieRankInfo) GetMovieId() uint32 {
	if m != nil {
		return m.MovieId
	}
	return 0
}

func (m *MovieRankInfo) GetPlaying() bool {
	if m != nil {
		return m.Playing
	}
	return false
}

func (m *MovieRankInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MovieRankInfo) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *MovieRankInfo) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *MovieRankInfo) GetMovieType() uint32 {
	if m != nil {
		return m.MovieType
	}
	return 0
}

type GetMovieRankListResp struct {
	Total                uint32           `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	MovieRankList        []*MovieRankInfo `protobuf:"bytes,2,rep,name=movie_rank_list,json=movieRankList,proto3" json:"movie_rank_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetMovieRankListResp) Reset()         { *m = GetMovieRankListResp{} }
func (m *GetMovieRankListResp) String() string { return proto.CompactTextString(m) }
func (*GetMovieRankListResp) ProtoMessage()    {}
func (*GetMovieRankListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{5}
}
func (m *GetMovieRankListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMovieRankListResp.Unmarshal(m, b)
}
func (m *GetMovieRankListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMovieRankListResp.Marshal(b, m, deterministic)
}
func (dst *GetMovieRankListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMovieRankListResp.Merge(dst, src)
}
func (m *GetMovieRankListResp) XXX_Size() int {
	return xxx_messageInfo_GetMovieRankListResp.Size(m)
}
func (m *GetMovieRankListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMovieRankListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMovieRankListResp proto.InternalMessageInfo

func (m *GetMovieRankListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetMovieRankListResp) GetMovieRankList() []*MovieRankInfo {
	if m != nil {
		return m.MovieRankList
	}
	return nil
}

type SearchMovieReq struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Size                 uint32   `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchMovieReq) Reset()         { *m = SearchMovieReq{} }
func (m *SearchMovieReq) String() string { return proto.CompactTextString(m) }
func (*SearchMovieReq) ProtoMessage()    {}
func (*SearchMovieReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{6}
}
func (m *SearchMovieReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchMovieReq.Unmarshal(m, b)
}
func (m *SearchMovieReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchMovieReq.Marshal(b, m, deterministic)
}
func (dst *SearchMovieReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchMovieReq.Merge(dst, src)
}
func (m *SearchMovieReq) XXX_Size() int {
	return xxx_messageInfo_SearchMovieReq.Size(m)
}
func (m *SearchMovieReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchMovieReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchMovieReq proto.InternalMessageInfo

func (m *SearchMovieReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *SearchMovieReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *SearchMovieReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

type SearchMovieInfo struct {
	MovieId              uint32   `protobuf:"varint,1,opt,name=movie_id,json=movieId,proto3" json:"movie_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	CoverUrl             string   `protobuf:"bytes,3,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"`
	Duration             uint32   `protobuf:"varint,4,opt,name=duration,proto3" json:"duration,omitempty"`
	MovieType            uint32   `protobuf:"varint,5,opt,name=movie_type,json=movieType,proto3" json:"movie_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchMovieInfo) Reset()         { *m = SearchMovieInfo{} }
func (m *SearchMovieInfo) String() string { return proto.CompactTextString(m) }
func (*SearchMovieInfo) ProtoMessage()    {}
func (*SearchMovieInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{7}
}
func (m *SearchMovieInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchMovieInfo.Unmarshal(m, b)
}
func (m *SearchMovieInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchMovieInfo.Marshal(b, m, deterministic)
}
func (dst *SearchMovieInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchMovieInfo.Merge(dst, src)
}
func (m *SearchMovieInfo) XXX_Size() int {
	return xxx_messageInfo_SearchMovieInfo.Size(m)
}
func (m *SearchMovieInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchMovieInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SearchMovieInfo proto.InternalMessageInfo

func (m *SearchMovieInfo) GetMovieId() uint32 {
	if m != nil {
		return m.MovieId
	}
	return 0
}

func (m *SearchMovieInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SearchMovieInfo) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *SearchMovieInfo) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *SearchMovieInfo) GetMovieType() uint32 {
	if m != nil {
		return m.MovieType
	}
	return 0
}

type SearchMovieResp struct {
	Total                uint32             `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	MovieList            []*SearchMovieInfo `protobuf:"bytes,2,rep,name=movie_list,json=movieList,proto3" json:"movie_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SearchMovieResp) Reset()         { *m = SearchMovieResp{} }
func (m *SearchMovieResp) String() string { return proto.CompactTextString(m) }
func (*SearchMovieResp) ProtoMessage()    {}
func (*SearchMovieResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{8}
}
func (m *SearchMovieResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchMovieResp.Unmarshal(m, b)
}
func (m *SearchMovieResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchMovieResp.Marshal(b, m, deterministic)
}
func (dst *SearchMovieResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchMovieResp.Merge(dst, src)
}
func (m *SearchMovieResp) XXX_Size() int {
	return xxx_messageInfo_SearchMovieResp.Size(m)
}
func (m *SearchMovieResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchMovieResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchMovieResp proto.InternalMessageInfo

func (m *SearchMovieResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *SearchMovieResp) GetMovieList() []*SearchMovieInfo {
	if m != nil {
		return m.MovieList
	}
	return nil
}

type GetMovieDetailReq struct {
	MovieId              uint32   `protobuf:"varint,1,opt,name=movie_id,json=movieId,proto3" json:"movie_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMovieDetailReq) Reset()         { *m = GetMovieDetailReq{} }
func (m *GetMovieDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetMovieDetailReq) ProtoMessage()    {}
func (*GetMovieDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{9}
}
func (m *GetMovieDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMovieDetailReq.Unmarshal(m, b)
}
func (m *GetMovieDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMovieDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetMovieDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMovieDetailReq.Merge(dst, src)
}
func (m *GetMovieDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetMovieDetailReq.Size(m)
}
func (m *GetMovieDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMovieDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMovieDetailReq proto.InternalMessageInfo

func (m *GetMovieDetailReq) GetMovieId() uint32 {
	if m != nil {
		return m.MovieId
	}
	return 0
}

type MovieDetailInfo struct {
	MovieId              uint32   `protobuf:"varint,1,opt,name=movie_id,json=movieId,proto3" json:"movie_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	ReleaseDate          string   `protobuf:"bytes,3,opt,name=release_date,json=releaseDate,proto3" json:"release_date,omitempty"`
	Category             string   `protobuf:"bytes,4,opt,name=category,proto3" json:"category,omitempty"`
	Language             string   `protobuf:"bytes,5,opt,name=language,proto3" json:"language,omitempty"`
	Actors               string   `protobuf:"bytes,6,opt,name=actors,proto3" json:"actors,omitempty"`
	Score                float32  `protobuf:"fixed32,7,opt,name=score,proto3" json:"score,omitempty"`
	Description          string   `protobuf:"bytes,8,opt,name=description,proto3" json:"description,omitempty"`
	CoverUrl             string   `protobuf:"bytes,9,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"`
	Duration             uint32   `protobuf:"varint,10,opt,name=duration,proto3" json:"duration,omitempty"`
	ResourceUrl          string   `protobuf:"bytes,11,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	MovieType            uint32   `protobuf:"varint,12,opt,name=movie_type,json=movieType,proto3" json:"movie_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MovieDetailInfo) Reset()         { *m = MovieDetailInfo{} }
func (m *MovieDetailInfo) String() string { return proto.CompactTextString(m) }
func (*MovieDetailInfo) ProtoMessage()    {}
func (*MovieDetailInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{10}
}
func (m *MovieDetailInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MovieDetailInfo.Unmarshal(m, b)
}
func (m *MovieDetailInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MovieDetailInfo.Marshal(b, m, deterministic)
}
func (dst *MovieDetailInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MovieDetailInfo.Merge(dst, src)
}
func (m *MovieDetailInfo) XXX_Size() int {
	return xxx_messageInfo_MovieDetailInfo.Size(m)
}
func (m *MovieDetailInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MovieDetailInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MovieDetailInfo proto.InternalMessageInfo

func (m *MovieDetailInfo) GetMovieId() uint32 {
	if m != nil {
		return m.MovieId
	}
	return 0
}

func (m *MovieDetailInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MovieDetailInfo) GetReleaseDate() string {
	if m != nil {
		return m.ReleaseDate
	}
	return ""
}

func (m *MovieDetailInfo) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *MovieDetailInfo) GetLanguage() string {
	if m != nil {
		return m.Language
	}
	return ""
}

func (m *MovieDetailInfo) GetActors() string {
	if m != nil {
		return m.Actors
	}
	return ""
}

func (m *MovieDetailInfo) GetScore() float32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *MovieDetailInfo) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *MovieDetailInfo) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *MovieDetailInfo) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *MovieDetailInfo) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *MovieDetailInfo) GetMovieType() uint32 {
	if m != nil {
		return m.MovieType
	}
	return 0
}

type GetMovieDetailResp struct {
	MovieInfo            *MovieDetailInfo `protobuf:"bytes,1,opt,name=movie_info,json=movieInfo,proto3" json:"movie_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetMovieDetailResp) Reset()         { *m = GetMovieDetailResp{} }
func (m *GetMovieDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetMovieDetailResp) ProtoMessage()    {}
func (*GetMovieDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{11}
}
func (m *GetMovieDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMovieDetailResp.Unmarshal(m, b)
}
func (m *GetMovieDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMovieDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetMovieDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMovieDetailResp.Merge(dst, src)
}
func (m *GetMovieDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetMovieDetailResp.Size(m)
}
func (m *GetMovieDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMovieDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMovieDetailResp proto.InternalMessageInfo

func (m *GetMovieDetailResp) GetMovieInfo() *MovieDetailInfo {
	if m != nil {
		return m.MovieInfo
	}
	return nil
}

type PlayMovieReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MovieId              uint32   `protobuf:"varint,2,opt,name=movie_id,json=movieId,proto3" json:"movie_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlayMovieReq) Reset()         { *m = PlayMovieReq{} }
func (m *PlayMovieReq) String() string { return proto.CompactTextString(m) }
func (*PlayMovieReq) ProtoMessage()    {}
func (*PlayMovieReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{12}
}
func (m *PlayMovieReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayMovieReq.Unmarshal(m, b)
}
func (m *PlayMovieReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayMovieReq.Marshal(b, m, deterministic)
}
func (dst *PlayMovieReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayMovieReq.Merge(dst, src)
}
func (m *PlayMovieReq) XXX_Size() int {
	return xxx_messageInfo_PlayMovieReq.Size(m)
}
func (m *PlayMovieReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayMovieReq.DiscardUnknown(m)
}

var xxx_messageInfo_PlayMovieReq proto.InternalMessageInfo

func (m *PlayMovieReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PlayMovieReq) GetMovieId() uint32 {
	if m != nil {
		return m.MovieId
	}
	return 0
}

type PlayMovieResp struct {
	MovieInfo            *MovieDetailInfo `protobuf:"bytes,1,opt,name=movie_info,json=movieInfo,proto3" json:"movie_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PlayMovieResp) Reset()         { *m = PlayMovieResp{} }
func (m *PlayMovieResp) String() string { return proto.CompactTextString(m) }
func (*PlayMovieResp) ProtoMessage()    {}
func (*PlayMovieResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{13}
}
func (m *PlayMovieResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayMovieResp.Unmarshal(m, b)
}
func (m *PlayMovieResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayMovieResp.Marshal(b, m, deterministic)
}
func (dst *PlayMovieResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayMovieResp.Merge(dst, src)
}
func (m *PlayMovieResp) XXX_Size() int {
	return xxx_messageInfo_PlayMovieResp.Size(m)
}
func (m *PlayMovieResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayMovieResp.DiscardUnknown(m)
}

var xxx_messageInfo_PlayMovieResp proto.InternalMessageInfo

func (m *PlayMovieResp) GetMovieInfo() *MovieDetailInfo {
	if m != nil {
		return m.MovieInfo
	}
	return nil
}

type CreateMovieReq struct {
	Partner              uint32   `protobuf:"varint,1,opt,name=partner,proto3" json:"partner,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	ReleaseDate          string   `protobuf:"bytes,3,opt,name=release_date,json=releaseDate,proto3" json:"release_date,omitempty"`
	Category             string   `protobuf:"bytes,4,opt,name=category,proto3" json:"category,omitempty"`
	Language             string   `protobuf:"bytes,5,opt,name=language,proto3" json:"language,omitempty"`
	Actors               string   `protobuf:"bytes,6,opt,name=actors,proto3" json:"actors,omitempty"`
	Score                float32  `protobuf:"fixed32,7,opt,name=score,proto3" json:"score,omitempty"`
	Description          string   `protobuf:"bytes,8,opt,name=description,proto3" json:"description,omitempty"`
	CoverUrl             string   `protobuf:"bytes,9,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"`
	Duration             uint32   `protobuf:"varint,10,opt,name=duration,proto3" json:"duration,omitempty"`
	ResourceUrl          string   `protobuf:"bytes,11,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	MovieType            uint32   `protobuf:"varint,12,opt,name=movie_type,json=movieType,proto3" json:"movie_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateMovieReq) Reset()         { *m = CreateMovieReq{} }
func (m *CreateMovieReq) String() string { return proto.CompactTextString(m) }
func (*CreateMovieReq) ProtoMessage()    {}
func (*CreateMovieReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{14}
}
func (m *CreateMovieReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateMovieReq.Unmarshal(m, b)
}
func (m *CreateMovieReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateMovieReq.Marshal(b, m, deterministic)
}
func (dst *CreateMovieReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateMovieReq.Merge(dst, src)
}
func (m *CreateMovieReq) XXX_Size() int {
	return xxx_messageInfo_CreateMovieReq.Size(m)
}
func (m *CreateMovieReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateMovieReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateMovieReq proto.InternalMessageInfo

func (m *CreateMovieReq) GetPartner() uint32 {
	if m != nil {
		return m.Partner
	}
	return 0
}

func (m *CreateMovieReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreateMovieReq) GetReleaseDate() string {
	if m != nil {
		return m.ReleaseDate
	}
	return ""
}

func (m *CreateMovieReq) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *CreateMovieReq) GetLanguage() string {
	if m != nil {
		return m.Language
	}
	return ""
}

func (m *CreateMovieReq) GetActors() string {
	if m != nil {
		return m.Actors
	}
	return ""
}

func (m *CreateMovieReq) GetScore() float32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *CreateMovieReq) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *CreateMovieReq) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *CreateMovieReq) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *CreateMovieReq) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *CreateMovieReq) GetMovieType() uint32 {
	if m != nil {
		return m.MovieType
	}
	return 0
}

type CreateMovieResp struct {
	MovieId              uint32   `protobuf:"varint,1,opt,name=movie_id,json=movieId,proto3" json:"movie_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateMovieResp) Reset()         { *m = CreateMovieResp{} }
func (m *CreateMovieResp) String() string { return proto.CompactTextString(m) }
func (*CreateMovieResp) ProtoMessage()    {}
func (*CreateMovieResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{15}
}
func (m *CreateMovieResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateMovieResp.Unmarshal(m, b)
}
func (m *CreateMovieResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateMovieResp.Marshal(b, m, deterministic)
}
func (dst *CreateMovieResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateMovieResp.Merge(dst, src)
}
func (m *CreateMovieResp) XXX_Size() int {
	return xxx_messageInfo_CreateMovieResp.Size(m)
}
func (m *CreateMovieResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateMovieResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateMovieResp proto.InternalMessageInfo

func (m *CreateMovieResp) GetMovieId() uint32 {
	if m != nil {
		return m.MovieId
	}
	return 0
}

// 分页查询，模糊查询等
type QueryMovieReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryMovieReq) Reset()         { *m = QueryMovieReq{} }
func (m *QueryMovieReq) String() string { return proto.CompactTextString(m) }
func (*QueryMovieReq) ProtoMessage()    {}
func (*QueryMovieReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{16}
}
func (m *QueryMovieReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryMovieReq.Unmarshal(m, b)
}
func (m *QueryMovieReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryMovieReq.Marshal(b, m, deterministic)
}
func (dst *QueryMovieReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryMovieReq.Merge(dst, src)
}
func (m *QueryMovieReq) XXX_Size() int {
	return xxx_messageInfo_QueryMovieReq.Size(m)
}
func (m *QueryMovieReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryMovieReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryMovieReq proto.InternalMessageInfo

type QueryMovieResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryMovieResp) Reset()         { *m = QueryMovieResp{} }
func (m *QueryMovieResp) String() string { return proto.CompactTextString(m) }
func (*QueryMovieResp) ProtoMessage()    {}
func (*QueryMovieResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{17}
}
func (m *QueryMovieResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryMovieResp.Unmarshal(m, b)
}
func (m *QueryMovieResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryMovieResp.Marshal(b, m, deterministic)
}
func (dst *QueryMovieResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryMovieResp.Merge(dst, src)
}
func (m *QueryMovieResp) XXX_Size() int {
	return xxx_messageInfo_QueryMovieResp.Size(m)
}
func (m *QueryMovieResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryMovieResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryMovieResp proto.InternalMessageInfo

type DeleteMovieReq struct {
	MovieIdList          []uint32 `protobuf:"varint,1,rep,packed,name=movie_id_list,json=movieIdList,proto3" json:"movie_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteMovieReq) Reset()         { *m = DeleteMovieReq{} }
func (m *DeleteMovieReq) String() string { return proto.CompactTextString(m) }
func (*DeleteMovieReq) ProtoMessage()    {}
func (*DeleteMovieReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{18}
}
func (m *DeleteMovieReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteMovieReq.Unmarshal(m, b)
}
func (m *DeleteMovieReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteMovieReq.Marshal(b, m, deterministic)
}
func (dst *DeleteMovieReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteMovieReq.Merge(dst, src)
}
func (m *DeleteMovieReq) XXX_Size() int {
	return xxx_messageInfo_DeleteMovieReq.Size(m)
}
func (m *DeleteMovieReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteMovieReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteMovieReq proto.InternalMessageInfo

func (m *DeleteMovieReq) GetMovieIdList() []uint32 {
	if m != nil {
		return m.MovieIdList
	}
	return nil
}

type DeleteMovieResp struct {
	SuccessDelId         []uint32 `protobuf:"varint,2,rep,packed,name=success_del_id,json=successDelId,proto3" json:"success_del_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteMovieResp) Reset()         { *m = DeleteMovieResp{} }
func (m *DeleteMovieResp) String() string { return proto.CompactTextString(m) }
func (*DeleteMovieResp) ProtoMessage()    {}
func (*DeleteMovieResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{19}
}
func (m *DeleteMovieResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteMovieResp.Unmarshal(m, b)
}
func (m *DeleteMovieResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteMovieResp.Marshal(b, m, deterministic)
}
func (dst *DeleteMovieResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteMovieResp.Merge(dst, src)
}
func (m *DeleteMovieResp) XXX_Size() int {
	return xxx_messageInfo_DeleteMovieResp.Size(m)
}
func (m *DeleteMovieResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteMovieResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteMovieResp proto.InternalMessageInfo

func (m *DeleteMovieResp) GetSuccessDelId() []uint32 {
	if m != nil {
		return m.SuccessDelId
	}
	return nil
}

type ChangeMovieRankReq struct {
	MovieId              uint32   `protobuf:"varint,1,opt,name=movie_id,json=movieId,proto3" json:"movie_id,omitempty"`
	Rank                 uint32   `protobuf:"varint,2,opt,name=rank,proto3" json:"rank,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,3,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	Category             string   `protobuf:"bytes,4,opt,name=category,proto3" json:"category,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeMovieRankReq) Reset()         { *m = ChangeMovieRankReq{} }
func (m *ChangeMovieRankReq) String() string { return proto.CompactTextString(m) }
func (*ChangeMovieRankReq) ProtoMessage()    {}
func (*ChangeMovieRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{20}
}
func (m *ChangeMovieRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeMovieRankReq.Unmarshal(m, b)
}
func (m *ChangeMovieRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeMovieRankReq.Marshal(b, m, deterministic)
}
func (dst *ChangeMovieRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeMovieRankReq.Merge(dst, src)
}
func (m *ChangeMovieRankReq) XXX_Size() int {
	return xxx_messageInfo_ChangeMovieRankReq.Size(m)
}
func (m *ChangeMovieRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeMovieRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeMovieRankReq proto.InternalMessageInfo

func (m *ChangeMovieRankReq) GetMovieId() uint32 {
	if m != nil {
		return m.MovieId
	}
	return 0
}

func (m *ChangeMovieRankReq) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *ChangeMovieRankReq) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *ChangeMovieRankReq) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

type ChangeMovieRankResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeMovieRankResp) Reset()         { *m = ChangeMovieRankResp{} }
func (m *ChangeMovieRankResp) String() string { return proto.CompactTextString(m) }
func (*ChangeMovieRankResp) ProtoMessage()    {}
func (*ChangeMovieRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{21}
}
func (m *ChangeMovieRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeMovieRankResp.Unmarshal(m, b)
}
func (m *ChangeMovieRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeMovieRankResp.Marshal(b, m, deterministic)
}
func (dst *ChangeMovieRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeMovieRankResp.Merge(dst, src)
}
func (m *ChangeMovieRankResp) XXX_Size() int {
	return xxx_messageInfo_ChangeMovieRankResp.Size(m)
}
func (m *ChangeMovieRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeMovieRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeMovieRankResp proto.InternalMessageInfo

type RecoverMovieRankReq struct {
	MovieId              uint32   `protobuf:"varint,1,opt,name=movie_id,json=movieId,proto3" json:"movie_id,omitempty"`
	Category             string   `protobuf:"bytes,2,opt,name=category,proto3" json:"category,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecoverMovieRankReq) Reset()         { *m = RecoverMovieRankReq{} }
func (m *RecoverMovieRankReq) String() string { return proto.CompactTextString(m) }
func (*RecoverMovieRankReq) ProtoMessage()    {}
func (*RecoverMovieRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{22}
}
func (m *RecoverMovieRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecoverMovieRankReq.Unmarshal(m, b)
}
func (m *RecoverMovieRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecoverMovieRankReq.Marshal(b, m, deterministic)
}
func (dst *RecoverMovieRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecoverMovieRankReq.Merge(dst, src)
}
func (m *RecoverMovieRankReq) XXX_Size() int {
	return xxx_messageInfo_RecoverMovieRankReq.Size(m)
}
func (m *RecoverMovieRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecoverMovieRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecoverMovieRankReq proto.InternalMessageInfo

func (m *RecoverMovieRankReq) GetMovieId() uint32 {
	if m != nil {
		return m.MovieId
	}
	return 0
}

func (m *RecoverMovieRankReq) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

type RecoverMovieRankResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecoverMovieRankResp) Reset()         { *m = RecoverMovieRankResp{} }
func (m *RecoverMovieRankResp) String() string { return proto.CompactTextString(m) }
func (*RecoverMovieRankResp) ProtoMessage()    {}
func (*RecoverMovieRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_movie_info_mgr_4cee05a3478b75a9, []int{23}
}
func (m *RecoverMovieRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecoverMovieRankResp.Unmarshal(m, b)
}
func (m *RecoverMovieRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecoverMovieRankResp.Marshal(b, m, deterministic)
}
func (dst *RecoverMovieRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecoverMovieRankResp.Merge(dst, src)
}
func (m *RecoverMovieRankResp) XXX_Size() int {
	return xxx_messageInfo_RecoverMovieRankResp.Size(m)
}
func (m *RecoverMovieRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecoverMovieRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecoverMovieRankResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*GetMovieCategoryReq)(nil), "channel_movie.GetMovieCategoryReq")
	proto.RegisterType((*MovieCategoryInfo)(nil), "channel_movie.MovieCategoryInfo")
	proto.RegisterType((*GetMovieCategoryResp)(nil), "channel_movie.GetMovieCategoryResp")
	proto.RegisterType((*GetMovieRankListReq)(nil), "channel_movie.GetMovieRankListReq")
	proto.RegisterType((*MovieRankInfo)(nil), "channel_movie.MovieRankInfo")
	proto.RegisterType((*GetMovieRankListResp)(nil), "channel_movie.GetMovieRankListResp")
	proto.RegisterType((*SearchMovieReq)(nil), "channel_movie.SearchMovieReq")
	proto.RegisterType((*SearchMovieInfo)(nil), "channel_movie.SearchMovieInfo")
	proto.RegisterType((*SearchMovieResp)(nil), "channel_movie.SearchMovieResp")
	proto.RegisterType((*GetMovieDetailReq)(nil), "channel_movie.GetMovieDetailReq")
	proto.RegisterType((*MovieDetailInfo)(nil), "channel_movie.MovieDetailInfo")
	proto.RegisterType((*GetMovieDetailResp)(nil), "channel_movie.GetMovieDetailResp")
	proto.RegisterType((*PlayMovieReq)(nil), "channel_movie.PlayMovieReq")
	proto.RegisterType((*PlayMovieResp)(nil), "channel_movie.PlayMovieResp")
	proto.RegisterType((*CreateMovieReq)(nil), "channel_movie.CreateMovieReq")
	proto.RegisterType((*CreateMovieResp)(nil), "channel_movie.CreateMovieResp")
	proto.RegisterType((*QueryMovieReq)(nil), "channel_movie.QueryMovieReq")
	proto.RegisterType((*QueryMovieResp)(nil), "channel_movie.QueryMovieResp")
	proto.RegisterType((*DeleteMovieReq)(nil), "channel_movie.DeleteMovieReq")
	proto.RegisterType((*DeleteMovieResp)(nil), "channel_movie.DeleteMovieResp")
	proto.RegisterType((*ChangeMovieRankReq)(nil), "channel_movie.ChangeMovieRankReq")
	proto.RegisterType((*ChangeMovieRankResp)(nil), "channel_movie.ChangeMovieRankResp")
	proto.RegisterType((*RecoverMovieRankReq)(nil), "channel_movie.RecoverMovieRankReq")
	proto.RegisterType((*RecoverMovieRankResp)(nil), "channel_movie.RecoverMovieRankResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MovieInfoMgrClient is the client API for MovieInfoMgr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MovieInfoMgrClient interface {
	GetMovieCategory(ctx context.Context, in *GetMovieCategoryReq, opts ...grpc.CallOption) (*GetMovieCategoryResp, error)
	GetMovieRankList(ctx context.Context, in *GetMovieRankListReq, opts ...grpc.CallOption) (*GetMovieRankListResp, error)
	GetMovieDetail(ctx context.Context, in *GetMovieDetailReq, opts ...grpc.CallOption) (*GetMovieDetailResp, error)
	SearchMovie(ctx context.Context, in *SearchMovieReq, opts ...grpc.CallOption) (*SearchMovieResp, error)
	PlayMovie(ctx context.Context, in *PlayMovieReq, opts ...grpc.CallOption) (*PlayMovieResp, error)
	// 运营后台
	CreateMovie(ctx context.Context, in *CreateMovieReq, opts ...grpc.CallOption) (*CreateMovieResp, error)
	QueryMovie(ctx context.Context, in *QueryMovieReq, opts ...grpc.CallOption) (*QueryMovieResp, error)
	DeleteMovie(ctx context.Context, in *DeleteMovieReq, opts ...grpc.CallOption) (*DeleteMovieResp, error)
	// 强插
	ChangeMovieRank(ctx context.Context, in *ChangeMovieRankReq, opts ...grpc.CallOption) (*ChangeMovieRankResp, error)
	RecoverMovieRank(ctx context.Context, in *RecoverMovieRankReq, opts ...grpc.CallOption) (*RecoverMovieRankResp, error)
}

type movieInfoMgrClient struct {
	cc *grpc.ClientConn
}

func NewMovieInfoMgrClient(cc *grpc.ClientConn) MovieInfoMgrClient {
	return &movieInfoMgrClient{cc}
}

func (c *movieInfoMgrClient) GetMovieCategory(ctx context.Context, in *GetMovieCategoryReq, opts ...grpc.CallOption) (*GetMovieCategoryResp, error) {
	out := new(GetMovieCategoryResp)
	err := c.cc.Invoke(ctx, "/channel_movie.MovieInfoMgr/GetMovieCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *movieInfoMgrClient) GetMovieRankList(ctx context.Context, in *GetMovieRankListReq, opts ...grpc.CallOption) (*GetMovieRankListResp, error) {
	out := new(GetMovieRankListResp)
	err := c.cc.Invoke(ctx, "/channel_movie.MovieInfoMgr/GetMovieRankList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *movieInfoMgrClient) GetMovieDetail(ctx context.Context, in *GetMovieDetailReq, opts ...grpc.CallOption) (*GetMovieDetailResp, error) {
	out := new(GetMovieDetailResp)
	err := c.cc.Invoke(ctx, "/channel_movie.MovieInfoMgr/GetMovieDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *movieInfoMgrClient) SearchMovie(ctx context.Context, in *SearchMovieReq, opts ...grpc.CallOption) (*SearchMovieResp, error) {
	out := new(SearchMovieResp)
	err := c.cc.Invoke(ctx, "/channel_movie.MovieInfoMgr/SearchMovie", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *movieInfoMgrClient) PlayMovie(ctx context.Context, in *PlayMovieReq, opts ...grpc.CallOption) (*PlayMovieResp, error) {
	out := new(PlayMovieResp)
	err := c.cc.Invoke(ctx, "/channel_movie.MovieInfoMgr/PlayMovie", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *movieInfoMgrClient) CreateMovie(ctx context.Context, in *CreateMovieReq, opts ...grpc.CallOption) (*CreateMovieResp, error) {
	out := new(CreateMovieResp)
	err := c.cc.Invoke(ctx, "/channel_movie.MovieInfoMgr/CreateMovie", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *movieInfoMgrClient) QueryMovie(ctx context.Context, in *QueryMovieReq, opts ...grpc.CallOption) (*QueryMovieResp, error) {
	out := new(QueryMovieResp)
	err := c.cc.Invoke(ctx, "/channel_movie.MovieInfoMgr/QueryMovie", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *movieInfoMgrClient) DeleteMovie(ctx context.Context, in *DeleteMovieReq, opts ...grpc.CallOption) (*DeleteMovieResp, error) {
	out := new(DeleteMovieResp)
	err := c.cc.Invoke(ctx, "/channel_movie.MovieInfoMgr/DeleteMovie", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *movieInfoMgrClient) ChangeMovieRank(ctx context.Context, in *ChangeMovieRankReq, opts ...grpc.CallOption) (*ChangeMovieRankResp, error) {
	out := new(ChangeMovieRankResp)
	err := c.cc.Invoke(ctx, "/channel_movie.MovieInfoMgr/ChangeMovieRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *movieInfoMgrClient) RecoverMovieRank(ctx context.Context, in *RecoverMovieRankReq, opts ...grpc.CallOption) (*RecoverMovieRankResp, error) {
	out := new(RecoverMovieRankResp)
	err := c.cc.Invoke(ctx, "/channel_movie.MovieInfoMgr/RecoverMovieRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MovieInfoMgrServer is the server API for MovieInfoMgr service.
type MovieInfoMgrServer interface {
	GetMovieCategory(context.Context, *GetMovieCategoryReq) (*GetMovieCategoryResp, error)
	GetMovieRankList(context.Context, *GetMovieRankListReq) (*GetMovieRankListResp, error)
	GetMovieDetail(context.Context, *GetMovieDetailReq) (*GetMovieDetailResp, error)
	SearchMovie(context.Context, *SearchMovieReq) (*SearchMovieResp, error)
	PlayMovie(context.Context, *PlayMovieReq) (*PlayMovieResp, error)
	// 运营后台
	CreateMovie(context.Context, *CreateMovieReq) (*CreateMovieResp, error)
	QueryMovie(context.Context, *QueryMovieReq) (*QueryMovieResp, error)
	DeleteMovie(context.Context, *DeleteMovieReq) (*DeleteMovieResp, error)
	// 强插
	ChangeMovieRank(context.Context, *ChangeMovieRankReq) (*ChangeMovieRankResp, error)
	RecoverMovieRank(context.Context, *RecoverMovieRankReq) (*RecoverMovieRankResp, error)
}

func RegisterMovieInfoMgrServer(s *grpc.Server, srv MovieInfoMgrServer) {
	s.RegisterService(&_MovieInfoMgr_serviceDesc, srv)
}

func _MovieInfoMgr_GetMovieCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMovieCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MovieInfoMgrServer).GetMovieCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_movie.MovieInfoMgr/GetMovieCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MovieInfoMgrServer).GetMovieCategory(ctx, req.(*GetMovieCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MovieInfoMgr_GetMovieRankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMovieRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MovieInfoMgrServer).GetMovieRankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_movie.MovieInfoMgr/GetMovieRankList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MovieInfoMgrServer).GetMovieRankList(ctx, req.(*GetMovieRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MovieInfoMgr_GetMovieDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMovieDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MovieInfoMgrServer).GetMovieDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_movie.MovieInfoMgr/GetMovieDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MovieInfoMgrServer).GetMovieDetail(ctx, req.(*GetMovieDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MovieInfoMgr_SearchMovie_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchMovieReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MovieInfoMgrServer).SearchMovie(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_movie.MovieInfoMgr/SearchMovie",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MovieInfoMgrServer).SearchMovie(ctx, req.(*SearchMovieReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MovieInfoMgr_PlayMovie_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlayMovieReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MovieInfoMgrServer).PlayMovie(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_movie.MovieInfoMgr/PlayMovie",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MovieInfoMgrServer).PlayMovie(ctx, req.(*PlayMovieReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MovieInfoMgr_CreateMovie_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMovieReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MovieInfoMgrServer).CreateMovie(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_movie.MovieInfoMgr/CreateMovie",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MovieInfoMgrServer).CreateMovie(ctx, req.(*CreateMovieReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MovieInfoMgr_QueryMovie_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryMovieReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MovieInfoMgrServer).QueryMovie(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_movie.MovieInfoMgr/QueryMovie",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MovieInfoMgrServer).QueryMovie(ctx, req.(*QueryMovieReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MovieInfoMgr_DeleteMovie_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMovieReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MovieInfoMgrServer).DeleteMovie(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_movie.MovieInfoMgr/DeleteMovie",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MovieInfoMgrServer).DeleteMovie(ctx, req.(*DeleteMovieReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MovieInfoMgr_ChangeMovieRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeMovieRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MovieInfoMgrServer).ChangeMovieRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_movie.MovieInfoMgr/ChangeMovieRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MovieInfoMgrServer).ChangeMovieRank(ctx, req.(*ChangeMovieRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MovieInfoMgr_RecoverMovieRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecoverMovieRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MovieInfoMgrServer).RecoverMovieRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_movie.MovieInfoMgr/RecoverMovieRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MovieInfoMgrServer).RecoverMovieRank(ctx, req.(*RecoverMovieRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MovieInfoMgr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_movie.MovieInfoMgr",
	HandlerType: (*MovieInfoMgrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMovieCategory",
			Handler:    _MovieInfoMgr_GetMovieCategory_Handler,
		},
		{
			MethodName: "GetMovieRankList",
			Handler:    _MovieInfoMgr_GetMovieRankList_Handler,
		},
		{
			MethodName: "GetMovieDetail",
			Handler:    _MovieInfoMgr_GetMovieDetail_Handler,
		},
		{
			MethodName: "SearchMovie",
			Handler:    _MovieInfoMgr_SearchMovie_Handler,
		},
		{
			MethodName: "PlayMovie",
			Handler:    _MovieInfoMgr_PlayMovie_Handler,
		},
		{
			MethodName: "CreateMovie",
			Handler:    _MovieInfoMgr_CreateMovie_Handler,
		},
		{
			MethodName: "QueryMovie",
			Handler:    _MovieInfoMgr_QueryMovie_Handler,
		},
		{
			MethodName: "DeleteMovie",
			Handler:    _MovieInfoMgr_DeleteMovie_Handler,
		},
		{
			MethodName: "ChangeMovieRank",
			Handler:    _MovieInfoMgr_ChangeMovieRank_Handler,
		},
		{
			MethodName: "RecoverMovieRank",
			Handler:    _MovieInfoMgr_RecoverMovieRank_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-movie/movie-info-mgr.proto",
}

func init() {
	proto.RegisterFile("channel-movie/movie-info-mgr.proto", fileDescriptor_movie_info_mgr_4cee05a3478b75a9)
}

var fileDescriptor_movie_info_mgr_4cee05a3478b75a9 = []byte{
	// 1012 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x57, 0xdd, 0x6e, 0x1b, 0x45,
	0x14, 0xb6, 0xd7, 0xf9, 0xb1, 0x8f, 0xbd, 0x76, 0x3a, 0x0d, 0x95, 0x71, 0x9b, 0xe2, 0x4e, 0xb9,
	0xc8, 0x05, 0xb1, 0x45, 0x00, 0x71, 0xc5, 0x0d, 0x31, 0xa2, 0x11, 0x6d, 0x04, 0xdb, 0x22, 0x44,
	0xa5, 0xca, 0x1a, 0xd6, 0xc7, 0xce, 0x2a, 0xeb, 0xdd, 0xcd, 0xcc, 0x38, 0xc2, 0x5c, 0xc1, 0x5b,
	0xf0, 0x18, 0x3c, 0x14, 0x82, 0xd7, 0x40, 0x33, 0xfb, 0xe3, 0xd9, 0x5d, 0xff, 0xa0, 0x70, 0xcb,
	0x8d, 0x35, 0xe7, 0xcc, 0xec, 0x39, 0xdf, 0x7c, 0xe7, 0x6f, 0x0c, 0xd4, 0xbd, 0x66, 0x41, 0x80,
	0xfe, 0xd9, 0x3c, 0xbc, 0xf3, 0x70, 0xa8, 0x7f, 0xcf, 0xbc, 0x60, 0x1a, 0x9e, 0xcd, 0x67, 0x7c,
	0x10, 0xf1, 0x50, 0x86, 0xc4, 0x4e, 0xce, 0x8c, 0xf5, 0x2e, 0x7d, 0x0f, 0x1e, 0x7e, 0x8d, 0xf2,
	0x95, 0x5a, 0x5f, 0x30, 0x89, 0xb3, 0x90, 0x2f, 0x1d, 0xbc, 0xa5, 0x3f, 0xc2, 0x83, 0x9c, 0xee,
	0x32, 0x98, 0x86, 0xe4, 0x39, 0xd8, 0x6e, 0x22, 0x8f, 0x03, 0x36, 0xc7, 0x6e, 0xb5, 0x5f, 0x3d,
	0x6d, 0x38, 0xad, 0x54, 0x79, 0xc5, 0xe6, 0x48, 0x3e, 0x80, 0x66, 0x76, 0xc8, 0x9b, 0x74, 0xad,
	0x7e, 0xf5, 0xd4, 0x76, 0x20, 0x55, 0x5d, 0x4e, 0xe8, 0x3b, 0x38, 0x2e, 0x7b, 0x14, 0x11, 0xf9,
	0xca, 0xb0, 0xee, 0x7b, 0x42, 0x76, 0xab, 0xfd, 0xda, 0x69, 0xf3, 0xbc, 0x3f, 0xc8, 0x01, 0x1e,
	0x94, 0x60, 0xad, 0xfc, 0xbf, 0xf4, 0x84, 0xa4, 0xbf, 0x55, 0x57, 0x37, 0x72, 0x58, 0x70, 0xa3,
	0x94, 0x0e, 0xde, 0x92, 0x13, 0x80, 0xd4, 0x90, 0x37, 0xd1, 0xc8, 0x6d, 0xa7, 0x91, 0x68, 0x2e,
	0x27, 0x3b, 0x61, 0x93, 0x47, 0x70, 0x10, 0x4e, 0xa7, 0x02, 0x65, 0xb7, 0xa6, 0xf7, 0x12, 0x89,
	0x10, 0xd8, 0x13, 0xde, 0x2f, 0xd8, 0xdd, 0xd3, 0x5a, 0xbd, 0xa6, 0x7f, 0x54, 0xc1, 0xce, 0x00,
	0x68, 0xea, 0xde, 0x87, 0xba, 0x86, 0xbf, 0xf2, 0x7d, 0xa8, 0xe5, 0xcb, 0x09, 0xe9, 0xc2, 0x61,
	0xe4, 0xb3, 0xa5, 0x17, 0xcc, 0xb4, 0xd7, 0xba, 0x93, 0x8a, 0xca, 0xb4, 0xa6, 0xb9, 0xa6, 0x69,
	0xd6, 0x6b, 0xf2, 0x18, 0x1a, 0x6e, 0x78, 0x87, 0x7c, 0xbc, 0xe0, 0xbe, 0xf6, 0xd9, 0x70, 0xea,
	0x5a, 0xf1, 0x3d, 0xf7, 0x49, 0x0f, 0xea, 0x93, 0x05, 0x67, 0xd2, 0x0b, 0x83, 0xee, 0xbe, 0xf6,
	0x92, 0xc9, 0xea, 0xfe, 0x31, 0x02, 0xb9, 0x8c, 0xb0, 0x7b, 0x10, 0xdf, 0x5f, 0x6b, 0xde, 0x2c,
	0x23, 0xa4, 0x7c, 0x15, 0x95, 0x15, 0x6b, 0x22, 0x22, 0xc7, 0xb0, 0x2f, 0x43, 0xc9, 0xfc, 0x04,
	0x75, 0x2c, 0x90, 0x11, 0x74, 0x62, 0x63, 0x9c, 0x05, 0x37, 0x71, 0xb4, 0x2c, 0x1d, 0xad, 0x27,
	0xeb, 0xa2, 0x95, 0xb2, 0xe0, 0xd8, 0x73, 0xd3, 0x3e, 0xbd, 0x82, 0xf6, 0x6b, 0x64, 0xdc, 0xbd,
	0x8e, 0x4f, 0xe1, 0x2d, 0x39, 0x82, 0xda, 0x0d, 0x2e, 0x93, 0xbc, 0x52, 0x4b, 0x83, 0x76, 0x6b,
	0x2d, 0xed, 0x35, 0x83, 0xf6, 0xdf, 0xab, 0xd0, 0x31, 0x0c, 0xee, 0x22, 0x3e, 0xa5, 0xd7, 0xda,
	0x44, 0x6f, 0x6d, 0x0b, 0xbd, 0x7b, 0x5b, 0xe9, 0xdd, 0x2f, 0xd2, 0x3b, 0xcd, 0x21, 0xdb, 0xc2,
	0xec, 0x17, 0xa9, 0x1d, 0x83, 0xd4, 0xa7, 0x05, 0x52, 0x0b, 0x77, 0x4c, 0xfc, 0x68, 0x4a, 0x07,
	0xf0, 0x20, 0x0d, 0xe3, 0x08, 0x25, 0xf3, 0x7c, 0xc5, 0xea, 0x66, 0x0e, 0xe8, 0x5f, 0x16, 0x74,
	0x8c, 0xd3, 0xf7, 0xa1, 0xec, 0x19, 0xb4, 0x38, 0xfa, 0xc8, 0x04, 0x8e, 0x27, 0x4c, 0xa6, 0xd9,
	0xda, 0x4c, 0x74, 0x23, 0x26, 0x51, 0x11, 0x97, 0x56, 0x52, 0x96, 0xb3, 0x89, 0xac, 0xf6, 0x7c,
	0x16, 0xcc, 0x16, 0x6c, 0x16, 0xd3, 0xd6, 0x70, 0x32, 0x59, 0x05, 0x9f, 0xb9, 0x32, 0xe4, 0x42,
	0xe7, 0x6b, 0xc3, 0x49, 0x24, 0x45, 0x9d, 0x70, 0x43, 0x8e, 0xdd, 0xc3, 0x7e, 0xf5, 0xd4, 0x72,
	0x62, 0x81, 0xf4, 0xa1, 0x39, 0x41, 0xe1, 0x72, 0x2f, 0xd2, 0x11, 0xaa, 0xc7, 0x38, 0x0c, 0x55,
	0x3e, 0xba, 0x8d, 0x2d, 0xd1, 0x85, 0x42, 0x74, 0xf5, 0x1d, 0x45, 0xb8, 0xe0, 0x2e, 0xea, 0x6f,
	0x9b, 0xe9, 0x1d, 0x63, 0x9d, 0xfa, 0x3c, 0x9f, 0x00, 0xad, 0x62, 0x02, 0xbc, 0x06, 0x52, 0x0c,
	0x8c, 0x88, 0x56, 0xd1, 0x56, 0x4d, 0x5a, 0x93, 0x5d, 0x8e, 0x76, 0x21, 0x3c, 0x89, 0x51, 0xb5,
	0xa4, 0x2f, 0xa0, 0xf5, 0xad, 0xcf, 0x96, 0x59, 0xf9, 0xec, 0xe8, 0x71, 0x66, 0x60, 0xad, 0x7c,
	0x1e, 0x5c, 0x81, 0x6d, 0x58, 0xfa, 0xef, 0xc8, 0xfe, 0xb4, 0xa0, 0x7d, 0xc1, 0x91, 0x49, 0xcc,
	0xc0, 0xa9, 0x3e, 0xc7, 0xb8, 0x0c, 0x90, 0xa7, 0x59, 0x95, 0x88, 0xff, 0x67, 0xd5, 0xbd, 0xb3,
	0xea, 0x23, 0xe8, 0xe4, 0x58, 0x16, 0xd1, 0xb6, 0x62, 0xef, 0x80, 0xfd, 0xdd, 0x02, 0x79, 0x96,
	0x2f, 0xf4, 0x08, 0xda, 0xa6, 0x42, 0x44, 0xf4, 0x53, 0x68, 0x8f, 0xd0, 0x47, 0x23, 0x6c, 0x14,
	0xec, 0xd4, 0xde, 0x6a, 0x2c, 0xdb, 0x4e, 0x33, 0x31, 0xaa, 0xbb, 0xce, 0xe7, 0xd0, 0xc9, 0x7d,
	0x25, 0x22, 0xf2, 0x21, 0xb4, 0xc5, 0xc2, 0x75, 0x51, 0x88, 0xf1, 0x24, 0x4e, 0x47, 0x4b, 0x7f,
	0xd7, 0x4a, 0xb4, 0x23, 0x95, 0x91, 0xf4, 0xd7, 0x2a, 0x90, 0x8b, 0x6b, 0x16, 0xcc, 0x30, 0x1b,
	0x14, 0xdb, 0x1b, 0x96, 0xca, 0x15, 0x35, 0x73, 0x92, 0xfc, 0xd5, 0x6b, 0x35, 0xbb, 0xf1, 0xe7,
	0xc8, 0xe3, 0x38, 0x96, 0xde, 0x3c, 0x1d, 0x09, 0x10, 0xab, 0xde, 0x78, 0xf3, 0xad, 0x99, 0xa2,
	0x1e, 0x40, 0x25, 0x04, 0x22, 0xa2, 0x2f, 0xe1, 0xa1, 0x83, 0x3a, 0x8a, 0xff, 0x16, 0x99, 0xe9,
	0xc4, 0x2a, 0x38, 0x79, 0x04, 0xc7, 0x65, 0x6b, 0x22, 0x3a, 0xff, 0xfb, 0x00, 0x5a, 0x59, 0x1f,
	0x7f, 0x35, 0xe3, 0xe4, 0x1d, 0x1c, 0x15, 0x1f, 0x47, 0x84, 0x16, 0xca, 0x6e, 0xcd, 0x7b, 0xad,
	0xf7, 0x7c, 0xe7, 0x19, 0x11, 0xd1, 0x8a, 0x69, 0x3e, 0x9d, 0xc2, 0x1b, 0xcd, 0x1b, 0x8f, 0xa7,
	0x8d, 0xe6, 0xcd, 0xa7, 0x02, 0xad, 0x90, 0x1f, 0xa0, 0x9d, 0x6f, 0x72, 0xa4, 0xbf, 0xe1, 0xc3,
	0x6c, 0x38, 0xf5, 0x9e, 0xed, 0x38, 0xa1, 0x0d, 0x5f, 0x41, 0xd3, 0x18, 0x7a, 0xe4, 0x64, 0xf3,
	0x40, 0x54, 0x26, 0x9f, 0x6e, 0xdb, 0xd6, 0xf6, 0x5e, 0x40, 0x23, 0x6b, 0x77, 0xe4, 0x71, 0xe1,
	0xb8, 0xd9, 0x52, 0x7b, 0x4f, 0x36, 0x6f, 0xa6, 0xc8, 0x8c, 0x0a, 0x2c, 0x21, 0xcb, 0xf7, 0xc0,
	0x12, 0xb2, 0x42, 0xf1, 0xd2, 0x0a, 0xf9, 0x06, 0x60, 0x55, 0x92, 0xa4, 0xe8, 0x3d, 0x57, 0xbe,
	0xbd, 0x93, 0x2d, 0xbb, 0x29, 0x38, 0xa3, 0x2e, 0x4b, 0xe0, 0xf2, 0x95, 0x5e, 0x02, 0x57, 0x28,
	0x69, 0x5a, 0x21, 0x6f, 0xa1, 0x53, 0xa8, 0x15, 0x52, 0x0c, 0x5f, 0xb9, 0x9a, 0x7b, 0x74, 0xd7,
	0x91, 0x34, 0x35, 0x8b, 0x25, 0x52, 0x4a, 0xcd, 0x35, 0x15, 0x59, 0x4a, 0xcd, 0x75, 0x75, 0x46,
	0x2b, 0x5f, 0x7e, 0xfc, 0x76, 0x38, 0x0b, 0xd5, 0x08, 0x18, 0x7c, 0x76, 0x2e, 0xe5, 0xc0, 0x0d,
	0xe7, 0x43, 0xfd, 0x7f, 0xc8, 0x0d, 0xfd, 0xa1, 0x40, 0x7e, 0xe7, 0xb9, 0x28, 0x86, 0xb9, 0xbf,
	0x4f, 0x3f, 0x1d, 0xe8, 0x03, 0x9f, 0xfc, 0x13, 0x00, 0x00, 0xff, 0xff, 0x5f, 0x9b, 0xd5, 0x6a,
	0x56, 0x0d, 0x00, 0x00,
}
