// Code generated by protoc-gen-go. DO NOT EDIT.
// source: src/channelsvr/channel.proto

package Channel

/*
namespace
*/

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 与 ga::ChannelType 的类型保持一致
type ChannelBindType int32

const (
	ChannelBindType_GUILD_BIND_CHANNEL            ChannelBindType = 1
	ChannelBindType_FREE_BIND_CHANNEL             ChannelBindType = 2
	ChannelBindType_USER_BIND_CHANNEL             ChannelBindType = 3
	ChannelBindType_GUILD_PUBLIC_FUN_BIND_CHANNEL ChannelBindType = 4
)

var ChannelBindType_name = map[int32]string{
	1: "GUILD_BIND_CHANNEL",
	2: "FREE_BIND_CHANNEL",
	3: "USER_BIND_CHANNEL",
	4: "GUILD_PUBLIC_FUN_BIND_CHANNEL",
}
var ChannelBindType_value = map[string]int32{
	"GUILD_BIND_CHANNEL":            1,
	"FREE_BIND_CHANNEL":             2,
	"USER_BIND_CHANNEL":             3,
	"GUILD_PUBLIC_FUN_BIND_CHANNEL": 4,
}

func (x ChannelBindType) Enum() *ChannelBindType {
	p := new(ChannelBindType)
	*p = x
	return p
}
func (x ChannelBindType) String() string {
	return proto.EnumName(ChannelBindType_name, int32(x))
}
func (x *ChannelBindType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ChannelBindType_value, data, "ChannelBindType")
	if err != nil {
		return err
	}
	*x = ChannelBindType(value)
	return nil
}
func (ChannelBindType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{0}
}

// 与 ga::EChannelAdminRoleType 的类型保持一致
type ChannelAdminRole int32

const (
	ChannelAdminRole_CHANNEL_INVALID_ROLE   ChannelAdminRole = 0
	ChannelAdminRole_CHANNEL_OWNER          ChannelAdminRole = 1
	ChannelAdminRole_CHANNEL_ADMIN          ChannelAdminRole = 2
	ChannelAdminRole_CHANNEL_NOMAL_ROLE     ChannelAdminRole = 3
	ChannelAdminRole_CHANNEL_ADMIN_SUPER    ChannelAdminRole = 4
	ChannelAdminRole_CHANNEL_ADMIN_ROLE_MAX ChannelAdminRole = 255
)

var ChannelAdminRole_name = map[int32]string{
	0:   "CHANNEL_INVALID_ROLE",
	1:   "CHANNEL_OWNER",
	2:   "CHANNEL_ADMIN",
	3:   "CHANNEL_NOMAL_ROLE",
	4:   "CHANNEL_ADMIN_SUPER",
	255: "CHANNEL_ADMIN_ROLE_MAX",
}
var ChannelAdminRole_value = map[string]int32{
	"CHANNEL_INVALID_ROLE":   0,
	"CHANNEL_OWNER":          1,
	"CHANNEL_ADMIN":          2,
	"CHANNEL_NOMAL_ROLE":     3,
	"CHANNEL_ADMIN_SUPER":    4,
	"CHANNEL_ADMIN_ROLE_MAX": 255,
}

func (x ChannelAdminRole) Enum() *ChannelAdminRole {
	p := new(ChannelAdminRole)
	*p = x
	return p
}
func (x ChannelAdminRole) String() string {
	return proto.EnumName(ChannelAdminRole_name, int32(x))
}
func (x *ChannelAdminRole) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ChannelAdminRole_value, data, "ChannelAdminRole")
	if err != nil {
		return err
	}
	*x = ChannelAdminRole(value)
	return nil
}
func (ChannelAdminRole) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{1}
}

type ChannelSortType int32

const (
	ChannelSortType_NOT_SORT           ChannelSortType = 0
	ChannelSortType_SORT_BY_USER_COUNT ChannelSortType = 1
)

var ChannelSortType_name = map[int32]string{
	0: "NOT_SORT",
	1: "SORT_BY_USER_COUNT",
}
var ChannelSortType_value = map[string]int32{
	"NOT_SORT":           0,
	"SORT_BY_USER_COUNT": 1,
}

func (x ChannelSortType) Enum() *ChannelSortType {
	p := new(ChannelSortType)
	*p = x
	return p
}
func (x ChannelSortType) String() string {
	return proto.EnumName(ChannelSortType_name, int32(x))
}
func (x *ChannelSortType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ChannelSortType_value, data, "ChannelSortType")
	if err != nil {
		return err
	}
	*x = ChannelSortType(value)
	return nil
}
func (ChannelSortType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{2}
}

type ChannelMemberRole struct {
	GuildRole            *uint32  `protobuf:"varint,1,opt,name=guild_role,json=guildRole" json:"guild_role,omitempty"`
	GroupRole            *uint32  `protobuf:"varint,2,opt,name=group_role,json=groupRole" json:"group_role,omitempty"`
	ChannelRole          *uint32  `protobuf:"varint,3,opt,name=channel_role,json=channelRole" json:"channel_role,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelMemberRole) Reset()         { *m = ChannelMemberRole{} }
func (m *ChannelMemberRole) String() string { return proto.CompactTextString(m) }
func (*ChannelMemberRole) ProtoMessage()    {}
func (*ChannelMemberRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{0}
}
func (m *ChannelMemberRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMemberRole.Unmarshal(m, b)
}
func (m *ChannelMemberRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMemberRole.Marshal(b, m, deterministic)
}
func (dst *ChannelMemberRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMemberRole.Merge(dst, src)
}
func (m *ChannelMemberRole) XXX_Size() int {
	return xxx_messageInfo_ChannelMemberRole.Size(m)
}
func (m *ChannelMemberRole) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMemberRole.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMemberRole proto.InternalMessageInfo

func (m *ChannelMemberRole) GetGuildRole() uint32 {
	if m != nil && m.GuildRole != nil {
		return *m.GuildRole
	}
	return 0
}

func (m *ChannelMemberRole) GetGroupRole() uint32 {
	if m != nil && m.GroupRole != nil {
		return *m.GroupRole
	}
	return 0
}

func (m *ChannelMemberRole) GetChannelRole() uint32 {
	if m != nil && m.ChannelRole != nil {
		return *m.ChannelRole
	}
	return 0
}

// 房间成员
type ChannelMemberBaseInfo struct {
	Uid                  *uint32  `protobuf:"varint,1,req,name=uid" json:"uid,omitempty"`
	Ts                   *uint32  `protobuf:"varint,2,req,name=ts" json:"ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelMemberBaseInfo) Reset()         { *m = ChannelMemberBaseInfo{} }
func (m *ChannelMemberBaseInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelMemberBaseInfo) ProtoMessage()    {}
func (*ChannelMemberBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{1}
}
func (m *ChannelMemberBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMemberBaseInfo.Unmarshal(m, b)
}
func (m *ChannelMemberBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMemberBaseInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelMemberBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMemberBaseInfo.Merge(dst, src)
}
func (m *ChannelMemberBaseInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelMemberBaseInfo.Size(m)
}
func (m *ChannelMemberBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMemberBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMemberBaseInfo proto.InternalMessageInfo

func (m *ChannelMemberBaseInfo) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *ChannelMemberBaseInfo) GetTs() uint32 {
	if m != nil && m.Ts != nil {
		return *m.Ts
	}
	return 0
}

type ChannelMemberDetailInfo struct {
	Uid                  *uint32  `protobuf:"varint,1,req,name=uid" json:"uid,omitempty"`
	Ts                   *uint32  `protobuf:"varint,2,req,name=ts" json:"ts,omitempty"`
	IsRobot              *uint32  `protobuf:"varint,3,req,name=is_robot,json=isRobot" json:"is_robot,omitempty"`
	GroupRole            *uint32  `protobuf:"varint,4,req,name=group_role,json=groupRole" json:"group_role,omitempty"`
	IsHoldmic            *bool    `protobuf:"varint,5,req,name=is_holdmic,json=isHoldmic" json:"is_holdmic,omitempty"`
	IsMute               *bool    `protobuf:"varint,6,req,name=is_mute,json=isMute" json:"is_mute,omitempty"`
	Reddiamond           *uint32  `protobuf:"varint,7,opt,name=reddiamond" json:"reddiamond,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelMemberDetailInfo) Reset()         { *m = ChannelMemberDetailInfo{} }
func (m *ChannelMemberDetailInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelMemberDetailInfo) ProtoMessage()    {}
func (*ChannelMemberDetailInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{2}
}
func (m *ChannelMemberDetailInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMemberDetailInfo.Unmarshal(m, b)
}
func (m *ChannelMemberDetailInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMemberDetailInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelMemberDetailInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMemberDetailInfo.Merge(dst, src)
}
func (m *ChannelMemberDetailInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelMemberDetailInfo.Size(m)
}
func (m *ChannelMemberDetailInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMemberDetailInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMemberDetailInfo proto.InternalMessageInfo

func (m *ChannelMemberDetailInfo) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *ChannelMemberDetailInfo) GetTs() uint32 {
	if m != nil && m.Ts != nil {
		return *m.Ts
	}
	return 0
}

func (m *ChannelMemberDetailInfo) GetIsRobot() uint32 {
	if m != nil && m.IsRobot != nil {
		return *m.IsRobot
	}
	return 0
}

func (m *ChannelMemberDetailInfo) GetGroupRole() uint32 {
	if m != nil && m.GroupRole != nil {
		return *m.GroupRole
	}
	return 0
}

func (m *ChannelMemberDetailInfo) GetIsHoldmic() bool {
	if m != nil && m.IsHoldmic != nil {
		return *m.IsHoldmic
	}
	return false
}

func (m *ChannelMemberDetailInfo) GetIsMute() bool {
	if m != nil && m.IsMute != nil {
		return *m.IsMute
	}
	return false
}

func (m *ChannelMemberDetailInfo) GetReddiamond() uint32 {
	if m != nil && m.Reddiamond != nil {
		return *m.Reddiamond
	}
	return 0
}

// 麦位信息
type MicrSpaceInfo struct {
	MicId                *uint32  `protobuf:"varint,1,req,name=mic_id,json=micId" json:"mic_id,omitempty"`
	MicState             *uint32  `protobuf:"varint,2,opt,name=mic_state,json=micState" json:"mic_state,omitempty"`
	MicUid               *uint32  `protobuf:"varint,3,opt,name=mic_uid,json=micUid" json:"mic_uid,omitempty"`
	MicTs                *uint32  `protobuf:"varint,4,opt,name=mic_ts,json=micTs" json:"mic_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MicrSpaceInfo) Reset()         { *m = MicrSpaceInfo{} }
func (m *MicrSpaceInfo) String() string { return proto.CompactTextString(m) }
func (*MicrSpaceInfo) ProtoMessage()    {}
func (*MicrSpaceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{3}
}
func (m *MicrSpaceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicrSpaceInfo.Unmarshal(m, b)
}
func (m *MicrSpaceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicrSpaceInfo.Marshal(b, m, deterministic)
}
func (dst *MicrSpaceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicrSpaceInfo.Merge(dst, src)
}
func (m *MicrSpaceInfo) XXX_Size() int {
	return xxx_messageInfo_MicrSpaceInfo.Size(m)
}
func (m *MicrSpaceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MicrSpaceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MicrSpaceInfo proto.InternalMessageInfo

func (m *MicrSpaceInfo) GetMicId() uint32 {
	if m != nil && m.MicId != nil {
		return *m.MicId
	}
	return 0
}

func (m *MicrSpaceInfo) GetMicState() uint32 {
	if m != nil && m.MicState != nil {
		return *m.MicState
	}
	return 0
}

func (m *MicrSpaceInfo) GetMicUid() uint32 {
	if m != nil && m.MicUid != nil {
		return *m.MicUid
	}
	return 0
}

func (m *MicrSpaceInfo) GetMicTs() uint32 {
	if m != nil && m.MicTs != nil {
		return *m.MicTs
	}
	return 0
}

type ChannelSimpleInfo struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	DisplayId            *uint32  `protobuf:"varint,2,req,name=display_id,json=displayId" json:"display_id,omitempty"`
	AppId                *uint32  `protobuf:"varint,3,opt,name=app_id,json=appId" json:"app_id,omitempty"`
	HasPwd               *bool    `protobuf:"varint,4,opt,name=has_pwd,json=hasPwd" json:"has_pwd,omitempty"`
	ChannelType          *uint32  `protobuf:"varint,5,opt,name=channel_type,json=channelType" json:"channel_type,omitempty"`
	BindId               *uint32  `protobuf:"varint,6,opt,name=bind_id,json=bindId" json:"bind_id,omitempty"`
	SwitchFlag           *uint32  `protobuf:"varint,7,opt,name=switch_flag,json=switchFlag" json:"switch_flag,omitempty"`
	CreaterUid           *uint32  `protobuf:"varint,8,opt,name=creater_uid,json=createrUid" json:"creater_uid,omitempty"`
	CreateTs             *uint32  `protobuf:"varint,9,opt,name=create_ts,json=createTs" json:"create_ts,omitempty"`
	IconMd5              *string  `protobuf:"bytes,10,opt,name=icon_md5,json=iconMd5" json:"icon_md5,omitempty"`
	TopicTitle           *string  `protobuf:"bytes,11,opt,name=topic_title,json=topicTitle" json:"topic_title,omitempty"`
	Passwd               *string  `protobuf:"bytes,12,opt,name=passwd" json:"passwd,omitempty"`
	Name                 *string  `protobuf:"bytes,13,opt,name=name" json:"name,omitempty"`
	IsDel                *bool    `protobuf:"varint,14,opt,name=is_del,json=isDel" json:"is_del,omitempty"`
	EnterControlType     *uint32  `protobuf:"varint,15,opt,name=enter_control_type,json=enterControlType" json:"enter_control_type,omitempty"`
	ChannelViewId        *string  `protobuf:"bytes,16,opt,name=channel_view_id,json=channelViewId" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelSimpleInfo) Reset()         { *m = ChannelSimpleInfo{} }
func (m *ChannelSimpleInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelSimpleInfo) ProtoMessage()    {}
func (*ChannelSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{4}
}
func (m *ChannelSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSimpleInfo.Unmarshal(m, b)
}
func (m *ChannelSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSimpleInfo.Merge(dst, src)
}
func (m *ChannelSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelSimpleInfo.Size(m)
}
func (m *ChannelSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSimpleInfo proto.InternalMessageInfo

func (m *ChannelSimpleInfo) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *ChannelSimpleInfo) GetDisplayId() uint32 {
	if m != nil && m.DisplayId != nil {
		return *m.DisplayId
	}
	return 0
}

func (m *ChannelSimpleInfo) GetAppId() uint32 {
	if m != nil && m.AppId != nil {
		return *m.AppId
	}
	return 0
}

func (m *ChannelSimpleInfo) GetHasPwd() bool {
	if m != nil && m.HasPwd != nil {
		return *m.HasPwd
	}
	return false
}

func (m *ChannelSimpleInfo) GetChannelType() uint32 {
	if m != nil && m.ChannelType != nil {
		return *m.ChannelType
	}
	return 0
}

func (m *ChannelSimpleInfo) GetBindId() uint32 {
	if m != nil && m.BindId != nil {
		return *m.BindId
	}
	return 0
}

func (m *ChannelSimpleInfo) GetSwitchFlag() uint32 {
	if m != nil && m.SwitchFlag != nil {
		return *m.SwitchFlag
	}
	return 0
}

func (m *ChannelSimpleInfo) GetCreaterUid() uint32 {
	if m != nil && m.CreaterUid != nil {
		return *m.CreaterUid
	}
	return 0
}

func (m *ChannelSimpleInfo) GetCreateTs() uint32 {
	if m != nil && m.CreateTs != nil {
		return *m.CreateTs
	}
	return 0
}

func (m *ChannelSimpleInfo) GetIconMd5() string {
	if m != nil && m.IconMd5 != nil {
		return *m.IconMd5
	}
	return ""
}

func (m *ChannelSimpleInfo) GetTopicTitle() string {
	if m != nil && m.TopicTitle != nil {
		return *m.TopicTitle
	}
	return ""
}

func (m *ChannelSimpleInfo) GetPasswd() string {
	if m != nil && m.Passwd != nil {
		return *m.Passwd
	}
	return ""
}

func (m *ChannelSimpleInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *ChannelSimpleInfo) GetIsDel() bool {
	if m != nil && m.IsDel != nil {
		return *m.IsDel
	}
	return false
}

func (m *ChannelSimpleInfo) GetEnterControlType() uint32 {
	if m != nil && m.EnterControlType != nil {
		return *m.EnterControlType
	}
	return 0
}

func (m *ChannelSimpleInfo) GetChannelViewId() string {
	if m != nil && m.ChannelViewId != nil {
		return *m.ChannelViewId
	}
	return ""
}

// 该结构废弃
type ChannelBaseInfo struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	ChannelName          *string  `protobuf:"bytes,2,req,name=channel_name,json=channelName" json:"channel_name,omitempty"`
	ChannelMemberSize    *uint32  `protobuf:"varint,3,req,name=channel_member_size,json=channelMemberSize" json:"channel_member_size,omitempty"`
	SdkSessionId         *uint32  `protobuf:"varint,4,req,name=sdk_session_id,json=sdkSessionId" json:"sdk_session_id,omitempty"`
	AppId                *uint32  `protobuf:"varint,5,opt,name=app_id,json=appId" json:"app_id,omitempty"`
	HasPwd               *bool    `protobuf:"varint,6,opt,name=has_pwd,json=hasPwd" json:"has_pwd,omitempty"`
	Passwd               *string  `protobuf:"bytes,7,opt,name=passwd" json:"passwd,omitempty"`
	DisplayId            *uint32  `protobuf:"varint,8,opt,name=display_id,json=displayId" json:"display_id,omitempty"`
	IconMd5              *string  `protobuf:"bytes,9,opt,name=icon_md5,json=iconMd5" json:"icon_md5,omitempty"`
	Desc                 *string  `protobuf:"bytes,10,opt,name=desc" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelBaseInfo) Reset()         { *m = ChannelBaseInfo{} }
func (m *ChannelBaseInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelBaseInfo) ProtoMessage()    {}
func (*ChannelBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{5}
}
func (m *ChannelBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelBaseInfo.Unmarshal(m, b)
}
func (m *ChannelBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelBaseInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelBaseInfo.Merge(dst, src)
}
func (m *ChannelBaseInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelBaseInfo.Size(m)
}
func (m *ChannelBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelBaseInfo proto.InternalMessageInfo

func (m *ChannelBaseInfo) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *ChannelBaseInfo) GetChannelName() string {
	if m != nil && m.ChannelName != nil {
		return *m.ChannelName
	}
	return ""
}

func (m *ChannelBaseInfo) GetChannelMemberSize() uint32 {
	if m != nil && m.ChannelMemberSize != nil {
		return *m.ChannelMemberSize
	}
	return 0
}

func (m *ChannelBaseInfo) GetSdkSessionId() uint32 {
	if m != nil && m.SdkSessionId != nil {
		return *m.SdkSessionId
	}
	return 0
}

func (m *ChannelBaseInfo) GetAppId() uint32 {
	if m != nil && m.AppId != nil {
		return *m.AppId
	}
	return 0
}

func (m *ChannelBaseInfo) GetHasPwd() bool {
	if m != nil && m.HasPwd != nil {
		return *m.HasPwd
	}
	return false
}

func (m *ChannelBaseInfo) GetPasswd() string {
	if m != nil && m.Passwd != nil {
		return *m.Passwd
	}
	return ""
}

func (m *ChannelBaseInfo) GetDisplayId() uint32 {
	if m != nil && m.DisplayId != nil {
		return *m.DisplayId
	}
	return 0
}

func (m *ChannelBaseInfo) GetIconMd5() string {
	if m != nil && m.IconMd5 != nil {
		return *m.IconMd5
	}
	return ""
}

func (m *ChannelBaseInfo) GetDesc() string {
	if m != nil && m.Desc != nil {
		return *m.Desc
	}
	return ""
}

// 该结构废弃
type ChannelDetailInfo struct {
	ChannelBaseinfo      *ChannelBaseInfo `protobuf:"bytes,1,req,name=channel_baseinfo,json=channelBaseinfo" json:"channel_baseinfo,omitempty"`
	CreaterUid           *uint32          `protobuf:"varint,2,opt,name=creater_uid,json=createrUid" json:"creater_uid,omitempty"`
	CreateTs             *uint32          `protobuf:"varint,3,opt,name=create_ts,json=createTs" json:"create_ts,omitempty"`
	ChannelBindType      *uint32          `protobuf:"varint,4,opt,name=channel_bind_type,json=channelBindType" json:"channel_bind_type,omitempty"`
	BindId               *uint32          `protobuf:"varint,5,opt,name=bind_id,json=bindId" json:"bind_id,omitempty"`
	DisableMicSize       *uint32          `protobuf:"varint,6,opt,name=disable_mic_size,json=disableMicSize" json:"disable_mic_size,omitempty"`
	MicMode              *uint32          `protobuf:"varint,7,opt,name=mic_mode,json=micMode" json:"mic_mode,omitempty"`
	SwitchFlag           *uint32          `protobuf:"varint,8,opt,name=switch_flag,json=switchFlag" json:"switch_flag,omitempty"`
	IsTempAlloced        *bool            `protobuf:"varint,9,opt,name=is_temp_alloced,json=isTempAlloced" json:"is_temp_alloced,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ChannelDetailInfo) Reset()         { *m = ChannelDetailInfo{} }
func (m *ChannelDetailInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelDetailInfo) ProtoMessage()    {}
func (*ChannelDetailInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{6}
}
func (m *ChannelDetailInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelDetailInfo.Unmarshal(m, b)
}
func (m *ChannelDetailInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelDetailInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelDetailInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelDetailInfo.Merge(dst, src)
}
func (m *ChannelDetailInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelDetailInfo.Size(m)
}
func (m *ChannelDetailInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelDetailInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelDetailInfo proto.InternalMessageInfo

func (m *ChannelDetailInfo) GetChannelBaseinfo() *ChannelBaseInfo {
	if m != nil {
		return m.ChannelBaseinfo
	}
	return nil
}

func (m *ChannelDetailInfo) GetCreaterUid() uint32 {
	if m != nil && m.CreaterUid != nil {
		return *m.CreaterUid
	}
	return 0
}

func (m *ChannelDetailInfo) GetCreateTs() uint32 {
	if m != nil && m.CreateTs != nil {
		return *m.CreateTs
	}
	return 0
}

func (m *ChannelDetailInfo) GetChannelBindType() uint32 {
	if m != nil && m.ChannelBindType != nil {
		return *m.ChannelBindType
	}
	return 0
}

func (m *ChannelDetailInfo) GetBindId() uint32 {
	if m != nil && m.BindId != nil {
		return *m.BindId
	}
	return 0
}

func (m *ChannelDetailInfo) GetDisableMicSize() uint32 {
	if m != nil && m.DisableMicSize != nil {
		return *m.DisableMicSize
	}
	return 0
}

func (m *ChannelDetailInfo) GetMicMode() uint32 {
	if m != nil && m.MicMode != nil {
		return *m.MicMode
	}
	return 0
}

func (m *ChannelDetailInfo) GetSwitchFlag() uint32 {
	if m != nil && m.SwitchFlag != nil {
		return *m.SwitchFlag
	}
	return 0
}

func (m *ChannelDetailInfo) GetIsTempAlloced() bool {
	if m != nil && m.IsTempAlloced != nil {
		return *m.IsTempAlloced
	}
	return false
}

// 创建频道
type CreateChannelReq struct {
	Name                 *string  `protobuf:"bytes,1,req,name=name" json:"name,omitempty"`
	CreaterUid           *uint32  `protobuf:"varint,2,req,name=creater_uid,json=createrUid" json:"creater_uid,omitempty"`
	ChannelBindType      *uint32  `protobuf:"varint,3,req,name=channel_bind_type,json=channelBindType" json:"channel_bind_type,omitempty"`
	BindId               *uint32  `protobuf:"varint,4,opt,name=bind_id,json=bindId" json:"bind_id,omitempty"`
	CreaterGuildRole     *uint32  `protobuf:"varint,5,opt,name=creater_guild_role,json=createrGuildRole" json:"creater_guild_role,omitempty"`
	CreaterGroupRole     *uint32  `protobuf:"varint,6,opt,name=creater_group_role,json=createrGroupRole" json:"creater_group_role,omitempty"`
	Appid                *uint32  `protobuf:"varint,7,opt,name=appid" json:"appid,omitempty"`
	HasPwd               *bool    `protobuf:"varint,8,opt,name=has_pwd,json=hasPwd" json:"has_pwd,omitempty"`
	Passwd               *string  `protobuf:"bytes,9,opt,name=passwd" json:"passwd,omitempty"`
	GuildLevel           *uint32  `protobuf:"varint,11,opt,name=guild_level,json=guildLevel" json:"guild_level,omitempty"`
	MarketId             *uint32  `protobuf:"varint,12,opt,name=market_id,json=marketId" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateChannelReq) Reset()         { *m = CreateChannelReq{} }
func (m *CreateChannelReq) String() string { return proto.CompactTextString(m) }
func (*CreateChannelReq) ProtoMessage()    {}
func (*CreateChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{7}
}
func (m *CreateChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateChannelReq.Unmarshal(m, b)
}
func (m *CreateChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateChannelReq.Marshal(b, m, deterministic)
}
func (dst *CreateChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateChannelReq.Merge(dst, src)
}
func (m *CreateChannelReq) XXX_Size() int {
	return xxx_messageInfo_CreateChannelReq.Size(m)
}
func (m *CreateChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateChannelReq proto.InternalMessageInfo

func (m *CreateChannelReq) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *CreateChannelReq) GetCreaterUid() uint32 {
	if m != nil && m.CreaterUid != nil {
		return *m.CreaterUid
	}
	return 0
}

func (m *CreateChannelReq) GetChannelBindType() uint32 {
	if m != nil && m.ChannelBindType != nil {
		return *m.ChannelBindType
	}
	return 0
}

func (m *CreateChannelReq) GetBindId() uint32 {
	if m != nil && m.BindId != nil {
		return *m.BindId
	}
	return 0
}

func (m *CreateChannelReq) GetCreaterGuildRole() uint32 {
	if m != nil && m.CreaterGuildRole != nil {
		return *m.CreaterGuildRole
	}
	return 0
}

func (m *CreateChannelReq) GetCreaterGroupRole() uint32 {
	if m != nil && m.CreaterGroupRole != nil {
		return *m.CreaterGroupRole
	}
	return 0
}

func (m *CreateChannelReq) GetAppid() uint32 {
	if m != nil && m.Appid != nil {
		return *m.Appid
	}
	return 0
}

func (m *CreateChannelReq) GetHasPwd() bool {
	if m != nil && m.HasPwd != nil {
		return *m.HasPwd
	}
	return false
}

func (m *CreateChannelReq) GetPasswd() string {
	if m != nil && m.Passwd != nil {
		return *m.Passwd
	}
	return ""
}

func (m *CreateChannelReq) GetGuildLevel() uint32 {
	if m != nil && m.GuildLevel != nil {
		return *m.GuildLevel
	}
	return 0
}

func (m *CreateChannelReq) GetMarketId() uint32 {
	if m != nil && m.MarketId != nil {
		return *m.MarketId
	}
	return 0
}

type CreateChannelResp struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	SdkSessionId         *uint32  `protobuf:"varint,2,req,name=sdk_session_id,json=sdkSessionId" json:"sdk_session_id,omitempty"`
	GuildChannelCount    *uint32  `protobuf:"varint,3,opt,name=guild_channel_count,json=guildChannelCount" json:"guild_channel_count,omitempty"`
	DisplayId            *uint32  `protobuf:"varint,4,opt,name=display_id,json=displayId" json:"display_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateChannelResp) Reset()         { *m = CreateChannelResp{} }
func (m *CreateChannelResp) String() string { return proto.CompactTextString(m) }
func (*CreateChannelResp) ProtoMessage()    {}
func (*CreateChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{8}
}
func (m *CreateChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateChannelResp.Unmarshal(m, b)
}
func (m *CreateChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateChannelResp.Marshal(b, m, deterministic)
}
func (dst *CreateChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateChannelResp.Merge(dst, src)
}
func (m *CreateChannelResp) XXX_Size() int {
	return xxx_messageInfo_CreateChannelResp.Size(m)
}
func (m *CreateChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateChannelResp proto.InternalMessageInfo

func (m *CreateChannelResp) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *CreateChannelResp) GetSdkSessionId() uint32 {
	if m != nil && m.SdkSessionId != nil {
		return *m.SdkSessionId
	}
	return 0
}

func (m *CreateChannelResp) GetGuildChannelCount() uint32 {
	if m != nil && m.GuildChannelCount != nil {
		return *m.GuildChannelCount
	}
	return 0
}

func (m *CreateChannelResp) GetDisplayId() uint32 {
	if m != nil && m.DisplayId != nil {
		return *m.DisplayId
	}
	return 0
}

// 解散频道
type DissolveChannelReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	OpUid                *uint32  `protobuf:"varint,2,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DissolveChannelReq) Reset()         { *m = DissolveChannelReq{} }
func (m *DissolveChannelReq) String() string { return proto.CompactTextString(m) }
func (*DissolveChannelReq) ProtoMessage()    {}
func (*DissolveChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{9}
}
func (m *DissolveChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DissolveChannelReq.Unmarshal(m, b)
}
func (m *DissolveChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DissolveChannelReq.Marshal(b, m, deterministic)
}
func (dst *DissolveChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DissolveChannelReq.Merge(dst, src)
}
func (m *DissolveChannelReq) XXX_Size() int {
	return xxx_messageInfo_DissolveChannelReq.Size(m)
}
func (m *DissolveChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DissolveChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_DissolveChannelReq proto.InternalMessageInfo

func (m *DissolveChannelReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *DissolveChannelReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

// 修改房间属性（名字 密码 描述 头像 标签 各种标志位）(2022-11-15:修改密码请使用ModifyChannelEnterControlType)
type ModifyChannelReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	OpUid                *uint32  `protobuf:"varint,2,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	Name                 *string  `protobuf:"bytes,3,opt,name=name" json:"name,omitempty"`
	PwdFlag              *uint32  `protobuf:"varint,4,opt,name=pwd_flag,json=pwdFlag" json:"pwd_flag,omitempty"`
	Passwd               *string  `protobuf:"bytes,5,opt,name=passwd" json:"passwd,omitempty"`
	IconMd5              *string  `protobuf:"bytes,6,opt,name=icon_md5,json=iconMd5" json:"icon_md5,omitempty"`
	Desc                 *string  `protobuf:"bytes,7,opt,name=desc" json:"desc,omitempty"`
	SwitchFlagBitmap     *uint32  `protobuf:"varint,8,opt,name=switch_flag_bitmap,json=switchFlagBitmap" json:"switch_flag_bitmap,omitempty"`
	TopicDetail          *string  `protobuf:"bytes,9,opt,name=topic_detail,json=topicDetail" json:"topic_detail,omitempty"`
	WelcomeMsg           *string  `protobuf:"bytes,10,opt,name=welcome_msg,json=welcomeMsg" json:"welcome_msg,omitempty"`
	SwitchFlagBitsAdd    *uint32  `protobuf:"varint,11,opt,name=switch_flag_bits_add,json=switchFlagBitsAdd" json:"switch_flag_bits_add,omitempty"`
	SwitchFlagBitsDel    *uint32  `protobuf:"varint,12,opt,name=switch_flag_bits_del,json=switchFlagBitsDel" json:"switch_flag_bits_del,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyChannelReq) Reset()         { *m = ModifyChannelReq{} }
func (m *ModifyChannelReq) String() string { return proto.CompactTextString(m) }
func (*ModifyChannelReq) ProtoMessage()    {}
func (*ModifyChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{10}
}
func (m *ModifyChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyChannelReq.Unmarshal(m, b)
}
func (m *ModifyChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyChannelReq.Marshal(b, m, deterministic)
}
func (dst *ModifyChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyChannelReq.Merge(dst, src)
}
func (m *ModifyChannelReq) XXX_Size() int {
	return xxx_messageInfo_ModifyChannelReq.Size(m)
}
func (m *ModifyChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyChannelReq proto.InternalMessageInfo

func (m *ModifyChannelReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *ModifyChannelReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

func (m *ModifyChannelReq) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *ModifyChannelReq) GetPwdFlag() uint32 {
	if m != nil && m.PwdFlag != nil {
		return *m.PwdFlag
	}
	return 0
}

func (m *ModifyChannelReq) GetPasswd() string {
	if m != nil && m.Passwd != nil {
		return *m.Passwd
	}
	return ""
}

func (m *ModifyChannelReq) GetIconMd5() string {
	if m != nil && m.IconMd5 != nil {
		return *m.IconMd5
	}
	return ""
}

func (m *ModifyChannelReq) GetDesc() string {
	if m != nil && m.Desc != nil {
		return *m.Desc
	}
	return ""
}

func (m *ModifyChannelReq) GetSwitchFlagBitmap() uint32 {
	if m != nil && m.SwitchFlagBitmap != nil {
		return *m.SwitchFlagBitmap
	}
	return 0
}

func (m *ModifyChannelReq) GetTopicDetail() string {
	if m != nil && m.TopicDetail != nil {
		return *m.TopicDetail
	}
	return ""
}

func (m *ModifyChannelReq) GetWelcomeMsg() string {
	if m != nil && m.WelcomeMsg != nil {
		return *m.WelcomeMsg
	}
	return ""
}

func (m *ModifyChannelReq) GetSwitchFlagBitsAdd() uint32 {
	if m != nil && m.SwitchFlagBitsAdd != nil {
		return *m.SwitchFlagBitsAdd
	}
	return 0
}

func (m *ModifyChannelReq) GetSwitchFlagBitsDel() uint32 {
	if m != nil && m.SwitchFlagBitsDel != nil {
		return *m.SwitchFlagBitsDel
	}
	return 0
}

type ModifyChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyChannelResp) Reset()         { *m = ModifyChannelResp{} }
func (m *ModifyChannelResp) String() string { return proto.CompactTextString(m) }
func (*ModifyChannelResp) ProtoMessage()    {}
func (*ModifyChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{11}
}
func (m *ModifyChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyChannelResp.Unmarshal(m, b)
}
func (m *ModifyChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyChannelResp.Marshal(b, m, deterministic)
}
func (dst *ModifyChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyChannelResp.Merge(dst, src)
}
func (m *ModifyChannelResp) XXX_Size() int {
	return xxx_messageInfo_ModifyChannelResp.Size(m)
}
func (m *ModifyChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyChannelResp proto.InternalMessageInfo

// 修改进房控制类型
type ModifyChannelEnterControlTypeReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	OpUid                *uint32  `protobuf:"varint,2,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	EnterControlType     *uint32  `protobuf:"varint,3,req,name=enter_control_type,json=enterControlType" json:"enter_control_type,omitempty"`
	Passwd               *string  `protobuf:"bytes,4,opt,name=passwd" json:"passwd,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyChannelEnterControlTypeReq) Reset()         { *m = ModifyChannelEnterControlTypeReq{} }
func (m *ModifyChannelEnterControlTypeReq) String() string { return proto.CompactTextString(m) }
func (*ModifyChannelEnterControlTypeReq) ProtoMessage()    {}
func (*ModifyChannelEnterControlTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{12}
}
func (m *ModifyChannelEnterControlTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyChannelEnterControlTypeReq.Unmarshal(m, b)
}
func (m *ModifyChannelEnterControlTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyChannelEnterControlTypeReq.Marshal(b, m, deterministic)
}
func (dst *ModifyChannelEnterControlTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyChannelEnterControlTypeReq.Merge(dst, src)
}
func (m *ModifyChannelEnterControlTypeReq) XXX_Size() int {
	return xxx_messageInfo_ModifyChannelEnterControlTypeReq.Size(m)
}
func (m *ModifyChannelEnterControlTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyChannelEnterControlTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyChannelEnterControlTypeReq proto.InternalMessageInfo

func (m *ModifyChannelEnterControlTypeReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *ModifyChannelEnterControlTypeReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

func (m *ModifyChannelEnterControlTypeReq) GetEnterControlType() uint32 {
	if m != nil && m.EnterControlType != nil {
		return *m.EnterControlType
	}
	return 0
}

func (m *ModifyChannelEnterControlTypeReq) GetPasswd() string {
	if m != nil && m.Passwd != nil {
		return *m.Passwd
	}
	return ""
}

type ModifyChannelEnterControlTypeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyChannelEnterControlTypeResp) Reset()         { *m = ModifyChannelEnterControlTypeResp{} }
func (m *ModifyChannelEnterControlTypeResp) String() string { return proto.CompactTextString(m) }
func (*ModifyChannelEnterControlTypeResp) ProtoMessage()    {}
func (*ModifyChannelEnterControlTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{13}
}
func (m *ModifyChannelEnterControlTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyChannelEnterControlTypeResp.Unmarshal(m, b)
}
func (m *ModifyChannelEnterControlTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyChannelEnterControlTypeResp.Marshal(b, m, deterministic)
}
func (dst *ModifyChannelEnterControlTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyChannelEnterControlTypeResp.Merge(dst, src)
}
func (m *ModifyChannelEnterControlTypeResp) XXX_Size() int {
	return xxx_messageInfo_ModifyChannelEnterControlTypeResp.Size(m)
}
func (m *ModifyChannelEnterControlTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyChannelEnterControlTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyChannelEnterControlTypeResp proto.InternalMessageInfo

// 开麦(上麦)// 接口废弃 已经不再支持
type HoldMicrSpaceReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	Uid                  *uint32  `protobuf:"varint,2,req,name=uid" json:"uid,omitempty"`
	GuildRole            *uint32  `protobuf:"varint,3,opt,name=guild_role,json=guildRole" json:"guild_role,omitempty"`
	GroupRole            *uint32  `protobuf:"varint,4,opt,name=group_role,json=groupRole" json:"group_role,omitempty"`
	MicPosId             *uint32  `protobuf:"varint,5,opt,name=mic_pos_id,json=micPosId" json:"mic_pos_id,omitempty"`
	IsForce              *bool    `protobuf:"varint,6,opt,name=is_force,json=isForce" json:"is_force,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HoldMicrSpaceReq) Reset()         { *m = HoldMicrSpaceReq{} }
func (m *HoldMicrSpaceReq) String() string { return proto.CompactTextString(m) }
func (*HoldMicrSpaceReq) ProtoMessage()    {}
func (*HoldMicrSpaceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{14}
}
func (m *HoldMicrSpaceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HoldMicrSpaceReq.Unmarshal(m, b)
}
func (m *HoldMicrSpaceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HoldMicrSpaceReq.Marshal(b, m, deterministic)
}
func (dst *HoldMicrSpaceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HoldMicrSpaceReq.Merge(dst, src)
}
func (m *HoldMicrSpaceReq) XXX_Size() int {
	return xxx_messageInfo_HoldMicrSpaceReq.Size(m)
}
func (m *HoldMicrSpaceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HoldMicrSpaceReq.DiscardUnknown(m)
}

var xxx_messageInfo_HoldMicrSpaceReq proto.InternalMessageInfo

func (m *HoldMicrSpaceReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *HoldMicrSpaceReq) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *HoldMicrSpaceReq) GetGuildRole() uint32 {
	if m != nil && m.GuildRole != nil {
		return *m.GuildRole
	}
	return 0
}

func (m *HoldMicrSpaceReq) GetGroupRole() uint32 {
	if m != nil && m.GroupRole != nil {
		return *m.GroupRole
	}
	return 0
}

func (m *HoldMicrSpaceReq) GetMicPosId() uint32 {
	if m != nil && m.MicPosId != nil {
		return *m.MicPosId
	}
	return 0
}

func (m *HoldMicrSpaceReq) GetIsForce() bool {
	if m != nil && m.IsForce != nil {
		return *m.IsForce
	}
	return false
}

// 接口废弃 已经不再支持
type HoldMicrSpaceResp struct {
	OpenMicInfo          *MicrSpaceInfo   `protobuf:"bytes,1,opt,name=open_mic_info,json=openMicInfo" json:"open_mic_info,omitempty"`
	KickOutUid           *uint32          `protobuf:"varint,2,opt,name=kick_out_uid,json=kickOutUid" json:"kick_out_uid,omitempty"`
	AllMicList           []*MicrSpaceInfo `protobuf:"bytes,3,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	ServerTimeMs         *uint64          `protobuf:"varint,4,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *HoldMicrSpaceResp) Reset()         { *m = HoldMicrSpaceResp{} }
func (m *HoldMicrSpaceResp) String() string { return proto.CompactTextString(m) }
func (*HoldMicrSpaceResp) ProtoMessage()    {}
func (*HoldMicrSpaceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{15}
}
func (m *HoldMicrSpaceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HoldMicrSpaceResp.Unmarshal(m, b)
}
func (m *HoldMicrSpaceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HoldMicrSpaceResp.Marshal(b, m, deterministic)
}
func (dst *HoldMicrSpaceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HoldMicrSpaceResp.Merge(dst, src)
}
func (m *HoldMicrSpaceResp) XXX_Size() int {
	return xxx_messageInfo_HoldMicrSpaceResp.Size(m)
}
func (m *HoldMicrSpaceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HoldMicrSpaceResp.DiscardUnknown(m)
}

var xxx_messageInfo_HoldMicrSpaceResp proto.InternalMessageInfo

func (m *HoldMicrSpaceResp) GetOpenMicInfo() *MicrSpaceInfo {
	if m != nil {
		return m.OpenMicInfo
	}
	return nil
}

func (m *HoldMicrSpaceResp) GetKickOutUid() uint32 {
	if m != nil && m.KickOutUid != nil {
		return *m.KickOutUid
	}
	return 0
}

func (m *HoldMicrSpaceResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *HoldMicrSpaceResp) GetServerTimeMs() uint64 {
	if m != nil && m.ServerTimeMs != nil {
		return *m.ServerTimeMs
	}
	return 0
}

// 关麦(下麦)// 接口废弃 已经不再支持
type ReleaseMicrSpaceReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	Uid                  *uint32  `protobuf:"varint,2,req,name=uid" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReleaseMicrSpaceReq) Reset()         { *m = ReleaseMicrSpaceReq{} }
func (m *ReleaseMicrSpaceReq) String() string { return proto.CompactTextString(m) }
func (*ReleaseMicrSpaceReq) ProtoMessage()    {}
func (*ReleaseMicrSpaceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{16}
}
func (m *ReleaseMicrSpaceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReleaseMicrSpaceReq.Unmarshal(m, b)
}
func (m *ReleaseMicrSpaceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReleaseMicrSpaceReq.Marshal(b, m, deterministic)
}
func (dst *ReleaseMicrSpaceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReleaseMicrSpaceReq.Merge(dst, src)
}
func (m *ReleaseMicrSpaceReq) XXX_Size() int {
	return xxx_messageInfo_ReleaseMicrSpaceReq.Size(m)
}
func (m *ReleaseMicrSpaceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReleaseMicrSpaceReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReleaseMicrSpaceReq proto.InternalMessageInfo

func (m *ReleaseMicrSpaceReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *ReleaseMicrSpaceReq) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

// 接口废弃 已经不再支持
type ReleaseMicrSpaceResp struct {
	CloseMicInfo         *MicrSpaceInfo   `protobuf:"bytes,1,opt,name=close_mic_info,json=closeMicInfo" json:"close_mic_info,omitempty"`
	AllMicList           []*MicrSpaceInfo `protobuf:"bytes,2,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	ServerTimeMs         *uint64          `protobuf:"varint,3,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms,omitempty"`
	IsAutoDisableMic     *bool            `protobuf:"varint,4,opt,name=is_auto_disable_mic,json=isAutoDisableMic" json:"is_auto_disable_mic,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ReleaseMicrSpaceResp) Reset()         { *m = ReleaseMicrSpaceResp{} }
func (m *ReleaseMicrSpaceResp) String() string { return proto.CompactTextString(m) }
func (*ReleaseMicrSpaceResp) ProtoMessage()    {}
func (*ReleaseMicrSpaceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{17}
}
func (m *ReleaseMicrSpaceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReleaseMicrSpaceResp.Unmarshal(m, b)
}
func (m *ReleaseMicrSpaceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReleaseMicrSpaceResp.Marshal(b, m, deterministic)
}
func (dst *ReleaseMicrSpaceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReleaseMicrSpaceResp.Merge(dst, src)
}
func (m *ReleaseMicrSpaceResp) XXX_Size() int {
	return xxx_messageInfo_ReleaseMicrSpaceResp.Size(m)
}
func (m *ReleaseMicrSpaceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReleaseMicrSpaceResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReleaseMicrSpaceResp proto.InternalMessageInfo

func (m *ReleaseMicrSpaceResp) GetCloseMicInfo() *MicrSpaceInfo {
	if m != nil {
		return m.CloseMicInfo
	}
	return nil
}

func (m *ReleaseMicrSpaceResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *ReleaseMicrSpaceResp) GetServerTimeMs() uint64 {
	if m != nil && m.ServerTimeMs != nil {
		return *m.ServerTimeMs
	}
	return 0
}

func (m *ReleaseMicrSpaceResp) GetIsAutoDisableMic() bool {
	if m != nil && m.IsAutoDisableMic != nil {
		return *m.IsAutoDisableMic
	}
	return false
}

// 接口废弃 已经不再支持
// 检查并从指定的绑定上频道的麦列表上剔除某人
// 比如从公会下所有频道踢某人下麦
type CheckAndKickChannelMicrByBindIdReq struct {
	Uid                  *uint32  `protobuf:"varint,1,req,name=uid" json:"uid,omitempty"`
	ChannelBindType      *uint32  `protobuf:"varint,2,opt,name=channel_bind_type,json=channelBindType" json:"channel_bind_type,omitempty"`
	BindId               *uint32  `protobuf:"varint,3,opt,name=bind_id,json=bindId" json:"bind_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckAndKickChannelMicrByBindIdReq) Reset()         { *m = CheckAndKickChannelMicrByBindIdReq{} }
func (m *CheckAndKickChannelMicrByBindIdReq) String() string { return proto.CompactTextString(m) }
func (*CheckAndKickChannelMicrByBindIdReq) ProtoMessage()    {}
func (*CheckAndKickChannelMicrByBindIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{18}
}
func (m *CheckAndKickChannelMicrByBindIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckAndKickChannelMicrByBindIdReq.Unmarshal(m, b)
}
func (m *CheckAndKickChannelMicrByBindIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckAndKickChannelMicrByBindIdReq.Marshal(b, m, deterministic)
}
func (dst *CheckAndKickChannelMicrByBindIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckAndKickChannelMicrByBindIdReq.Merge(dst, src)
}
func (m *CheckAndKickChannelMicrByBindIdReq) XXX_Size() int {
	return xxx_messageInfo_CheckAndKickChannelMicrByBindIdReq.Size(m)
}
func (m *CheckAndKickChannelMicrByBindIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckAndKickChannelMicrByBindIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckAndKickChannelMicrByBindIdReq proto.InternalMessageInfo

func (m *CheckAndKickChannelMicrByBindIdReq) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *CheckAndKickChannelMicrByBindIdReq) GetChannelBindType() uint32 {
	if m != nil && m.ChannelBindType != nil {
		return *m.ChannelBindType
	}
	return 0
}

func (m *CheckAndKickChannelMicrByBindIdReq) GetBindId() uint32 {
	if m != nil && m.BindId != nil {
		return *m.BindId
	}
	return 0
}

type CheckAndKickChannelMicrByBindIdResp struct {
	Uid                  *uint32  `protobuf:"varint,1,req,name=uid" json:"uid,omitempty"`
	IsKicked             *bool    `protobuf:"varint,2,req,name=is_kicked,json=isKicked" json:"is_kicked,omitempty"`
	ChannelBindType      *uint32  `protobuf:"varint,3,opt,name=channel_bind_type,json=channelBindType" json:"channel_bind_type,omitempty"`
	BindId               *uint32  `protobuf:"varint,4,opt,name=bind_id,json=bindId" json:"bind_id,omitempty"`
	KickedChannelIdList  []uint32 `protobuf:"varint,5,rep,name=kicked_channel_id_list,json=kickedChannelIdList" json:"kicked_channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckAndKickChannelMicrByBindIdResp) Reset()         { *m = CheckAndKickChannelMicrByBindIdResp{} }
func (m *CheckAndKickChannelMicrByBindIdResp) String() string { return proto.CompactTextString(m) }
func (*CheckAndKickChannelMicrByBindIdResp) ProtoMessage()    {}
func (*CheckAndKickChannelMicrByBindIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{19}
}
func (m *CheckAndKickChannelMicrByBindIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckAndKickChannelMicrByBindIdResp.Unmarshal(m, b)
}
func (m *CheckAndKickChannelMicrByBindIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckAndKickChannelMicrByBindIdResp.Marshal(b, m, deterministic)
}
func (dst *CheckAndKickChannelMicrByBindIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckAndKickChannelMicrByBindIdResp.Merge(dst, src)
}
func (m *CheckAndKickChannelMicrByBindIdResp) XXX_Size() int {
	return xxx_messageInfo_CheckAndKickChannelMicrByBindIdResp.Size(m)
}
func (m *CheckAndKickChannelMicrByBindIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckAndKickChannelMicrByBindIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckAndKickChannelMicrByBindIdResp proto.InternalMessageInfo

func (m *CheckAndKickChannelMicrByBindIdResp) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *CheckAndKickChannelMicrByBindIdResp) GetIsKicked() bool {
	if m != nil && m.IsKicked != nil {
		return *m.IsKicked
	}
	return false
}

func (m *CheckAndKickChannelMicrByBindIdResp) GetChannelBindType() uint32 {
	if m != nil && m.ChannelBindType != nil {
		return *m.ChannelBindType
	}
	return 0
}

func (m *CheckAndKickChannelMicrByBindIdResp) GetBindId() uint32 {
	if m != nil && m.BindId != nil {
		return *m.BindId
	}
	return 0
}

func (m *CheckAndKickChannelMicrByBindIdResp) GetKickedChannelIdList() []uint32 {
	if m != nil {
		return m.KickedChannelIdList
	}
	return nil
}

// 频道禁言(有响应的禁言+踢麦)
// 废弃 改用 MuteChannelMemberReq
type SetChannelMuteAndKickMicReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	OpUid                *uint32  `protobuf:"varint,2,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	TargetUidList        []uint32 `protobuf:"varint,3,rep,name=target_uid_list,json=targetUidList" json:"target_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelMuteAndKickMicReq) Reset()         { *m = SetChannelMuteAndKickMicReq{} }
func (m *SetChannelMuteAndKickMicReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelMuteAndKickMicReq) ProtoMessage()    {}
func (*SetChannelMuteAndKickMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{20}
}
func (m *SetChannelMuteAndKickMicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMuteAndKickMicReq.Unmarshal(m, b)
}
func (m *SetChannelMuteAndKickMicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMuteAndKickMicReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelMuteAndKickMicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMuteAndKickMicReq.Merge(dst, src)
}
func (m *SetChannelMuteAndKickMicReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelMuteAndKickMicReq.Size(m)
}
func (m *SetChannelMuteAndKickMicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMuteAndKickMicReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMuteAndKickMicReq proto.InternalMessageInfo

func (m *SetChannelMuteAndKickMicReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *SetChannelMuteAndKickMicReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

func (m *SetChannelMuteAndKickMicReq) GetTargetUidList() []uint32 {
	if m != nil {
		return m.TargetUidList
	}
	return nil
}

type SetChannelMuteAndKickMicResp struct {
	ChannelId            *uint32          `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	MuteUidList          []uint32         `protobuf:"varint,2,rep,name=mute_uid_list,json=muteUidList" json:"mute_uid_list,omitempty"`
	KickmicUidList       []uint32         `protobuf:"varint,3,rep,name=kickmic_uid_list,json=kickmicUidList" json:"kickmic_uid_list,omitempty"`
	MuteKickMicList      []*MicrSpaceInfo `protobuf:"bytes,4,rep,name=mute_kick_mic_list,json=muteKickMicList" json:"mute_kick_mic_list,omitempty"`
	AllMicList           []*MicrSpaceInfo `protobuf:"bytes,5,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	ServerTimeMs         *uint64          `protobuf:"varint,6,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SetChannelMuteAndKickMicResp) Reset()         { *m = SetChannelMuteAndKickMicResp{} }
func (m *SetChannelMuteAndKickMicResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelMuteAndKickMicResp) ProtoMessage()    {}
func (*SetChannelMuteAndKickMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{21}
}
func (m *SetChannelMuteAndKickMicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMuteAndKickMicResp.Unmarshal(m, b)
}
func (m *SetChannelMuteAndKickMicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMuteAndKickMicResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelMuteAndKickMicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMuteAndKickMicResp.Merge(dst, src)
}
func (m *SetChannelMuteAndKickMicResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelMuteAndKickMicResp.Size(m)
}
func (m *SetChannelMuteAndKickMicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMuteAndKickMicResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMuteAndKickMicResp proto.InternalMessageInfo

func (m *SetChannelMuteAndKickMicResp) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *SetChannelMuteAndKickMicResp) GetMuteUidList() []uint32 {
	if m != nil {
		return m.MuteUidList
	}
	return nil
}

func (m *SetChannelMuteAndKickMicResp) GetKickmicUidList() []uint32 {
	if m != nil {
		return m.KickmicUidList
	}
	return nil
}

func (m *SetChannelMuteAndKickMicResp) GetMuteKickMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.MuteKickMicList
	}
	return nil
}

func (m *SetChannelMuteAndKickMicResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *SetChannelMuteAndKickMicResp) GetServerTimeMs() uint64 {
	if m != nil && m.ServerTimeMs != nil {
		return *m.ServerTimeMs
	}
	return 0
}

// 解除频道禁言
// 废弃 改用 UnmuteChannelMemberReq
type UnsetChannelMuteReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	OpUid                *uint32  `protobuf:"varint,2,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	TargetUidList        []uint32 `protobuf:"varint,3,rep,name=target_uid_list,json=targetUidList" json:"target_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnsetChannelMuteReq) Reset()         { *m = UnsetChannelMuteReq{} }
func (m *UnsetChannelMuteReq) String() string { return proto.CompactTextString(m) }
func (*UnsetChannelMuteReq) ProtoMessage()    {}
func (*UnsetChannelMuteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{22}
}
func (m *UnsetChannelMuteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnsetChannelMuteReq.Unmarshal(m, b)
}
func (m *UnsetChannelMuteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnsetChannelMuteReq.Marshal(b, m, deterministic)
}
func (dst *UnsetChannelMuteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnsetChannelMuteReq.Merge(dst, src)
}
func (m *UnsetChannelMuteReq) XXX_Size() int {
	return xxx_messageInfo_UnsetChannelMuteReq.Size(m)
}
func (m *UnsetChannelMuteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnsetChannelMuteReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnsetChannelMuteReq proto.InternalMessageInfo

func (m *UnsetChannelMuteReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *UnsetChannelMuteReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

func (m *UnsetChannelMuteReq) GetTargetUidList() []uint32 {
	if m != nil {
		return m.TargetUidList
	}
	return nil
}

// 频道禁言
type MuteChannelMemberReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	TargetUids           []uint32 `protobuf:"varint,2,rep,name=target_uids,json=targetUids" json:"target_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuteChannelMemberReq) Reset()         { *m = MuteChannelMemberReq{} }
func (m *MuteChannelMemberReq) String() string { return proto.CompactTextString(m) }
func (*MuteChannelMemberReq) ProtoMessage()    {}
func (*MuteChannelMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{23}
}
func (m *MuteChannelMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuteChannelMemberReq.Unmarshal(m, b)
}
func (m *MuteChannelMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuteChannelMemberReq.Marshal(b, m, deterministic)
}
func (dst *MuteChannelMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuteChannelMemberReq.Merge(dst, src)
}
func (m *MuteChannelMemberReq) XXX_Size() int {
	return xxx_messageInfo_MuteChannelMemberReq.Size(m)
}
func (m *MuteChannelMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MuteChannelMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_MuteChannelMemberReq proto.InternalMessageInfo

func (m *MuteChannelMemberReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *MuteChannelMemberReq) GetTargetUids() []uint32 {
	if m != nil {
		return m.TargetUids
	}
	return nil
}

type MuteChannelMemberResp struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	MutedUids            []uint32 `protobuf:"varint,2,rep,name=muted_uids,json=mutedUids" json:"muted_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuteChannelMemberResp) Reset()         { *m = MuteChannelMemberResp{} }
func (m *MuteChannelMemberResp) String() string { return proto.CompactTextString(m) }
func (*MuteChannelMemberResp) ProtoMessage()    {}
func (*MuteChannelMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{24}
}
func (m *MuteChannelMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuteChannelMemberResp.Unmarshal(m, b)
}
func (m *MuteChannelMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuteChannelMemberResp.Marshal(b, m, deterministic)
}
func (dst *MuteChannelMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuteChannelMemberResp.Merge(dst, src)
}
func (m *MuteChannelMemberResp) XXX_Size() int {
	return xxx_messageInfo_MuteChannelMemberResp.Size(m)
}
func (m *MuteChannelMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MuteChannelMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_MuteChannelMemberResp proto.InternalMessageInfo

func (m *MuteChannelMemberResp) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *MuteChannelMemberResp) GetMutedUids() []uint32 {
	if m != nil {
		return m.MutedUids
	}
	return nil
}

type UnmuteChannelMemberReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	TargetUids           []uint32 `protobuf:"varint,2,rep,name=target_uids,json=targetUids" json:"target_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnmuteChannelMemberReq) Reset()         { *m = UnmuteChannelMemberReq{} }
func (m *UnmuteChannelMemberReq) String() string { return proto.CompactTextString(m) }
func (*UnmuteChannelMemberReq) ProtoMessage()    {}
func (*UnmuteChannelMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{25}
}
func (m *UnmuteChannelMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnmuteChannelMemberReq.Unmarshal(m, b)
}
func (m *UnmuteChannelMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnmuteChannelMemberReq.Marshal(b, m, deterministic)
}
func (dst *UnmuteChannelMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnmuteChannelMemberReq.Merge(dst, src)
}
func (m *UnmuteChannelMemberReq) XXX_Size() int {
	return xxx_messageInfo_UnmuteChannelMemberReq.Size(m)
}
func (m *UnmuteChannelMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnmuteChannelMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnmuteChannelMemberReq proto.InternalMessageInfo

func (m *UnmuteChannelMemberReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *UnmuteChannelMemberReq) GetTargetUids() []uint32 {
	if m != nil {
		return m.TargetUids
	}
	return nil
}

type UnmuteChannelMemberResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnmuteChannelMemberResp) Reset()         { *m = UnmuteChannelMemberResp{} }
func (m *UnmuteChannelMemberResp) String() string { return proto.CompactTextString(m) }
func (*UnmuteChannelMemberResp) ProtoMessage()    {}
func (*UnmuteChannelMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{26}
}
func (m *UnmuteChannelMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnmuteChannelMemberResp.Unmarshal(m, b)
}
func (m *UnmuteChannelMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnmuteChannelMemberResp.Marshal(b, m, deterministic)
}
func (dst *UnmuteChannelMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnmuteChannelMemberResp.Merge(dst, src)
}
func (m *UnmuteChannelMemberResp) XXX_Size() int {
	return xxx_messageInfo_UnmuteChannelMemberResp.Size(m)
}
func (m *UnmuteChannelMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnmuteChannelMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnmuteChannelMemberResp proto.InternalMessageInfo

// 获取频道详细信息
type GetChannelDetailInfoReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	OpUid                *uint32  `protobuf:"varint,2,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelDetailInfoReq) Reset()         { *m = GetChannelDetailInfoReq{} }
func (m *GetChannelDetailInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelDetailInfoReq) ProtoMessage()    {}
func (*GetChannelDetailInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{27}
}
func (m *GetChannelDetailInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelDetailInfoReq.Unmarshal(m, b)
}
func (m *GetChannelDetailInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelDetailInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelDetailInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelDetailInfoReq.Merge(dst, src)
}
func (m *GetChannelDetailInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelDetailInfoReq.Size(m)
}
func (m *GetChannelDetailInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelDetailInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelDetailInfoReq proto.InternalMessageInfo

func (m *GetChannelDetailInfoReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *GetChannelDetailInfoReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

type GetChannelDetailInfoResp struct {
	ChannelBaseinfo      *ChannelBaseInfo `protobuf:"bytes,1,req,name=channel_baseinfo,json=channelBaseinfo" json:"channel_baseinfo,omitempty"`
	CreaterUid           *uint32          `protobuf:"varint,2,req,name=creater_uid,json=createrUid" json:"creater_uid,omitempty"`
	CreateTs             *uint32          `protobuf:"varint,3,req,name=create_ts,json=createTs" json:"create_ts,omitempty"`
	ChannelBindType      *uint32          `protobuf:"varint,4,req,name=channel_bind_type,json=channelBindType" json:"channel_bind_type,omitempty"`
	BindId               *uint32          `protobuf:"varint,5,opt,name=bind_id,json=bindId" json:"bind_id,omitempty"`
	DisableMicSize       *uint32          `protobuf:"varint,6,opt,name=disable_mic_size,json=disableMicSize" json:"disable_mic_size,omitempty"`
	MicMode              *uint32          `protobuf:"varint,7,opt,name=mic_mode,json=micMode" json:"mic_mode,omitempty"`
	SwitchFlag           *uint32          `protobuf:"varint,8,opt,name=switch_flag,json=switchFlag" json:"switch_flag,omitempty"`
	IsTempAlloced        *bool            `protobuf:"varint,9,opt,name=is_temp_alloced,json=isTempAlloced" json:"is_temp_alloced,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetChannelDetailInfoResp) Reset()         { *m = GetChannelDetailInfoResp{} }
func (m *GetChannelDetailInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelDetailInfoResp) ProtoMessage()    {}
func (*GetChannelDetailInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{28}
}
func (m *GetChannelDetailInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelDetailInfoResp.Unmarshal(m, b)
}
func (m *GetChannelDetailInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelDetailInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelDetailInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelDetailInfoResp.Merge(dst, src)
}
func (m *GetChannelDetailInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelDetailInfoResp.Size(m)
}
func (m *GetChannelDetailInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelDetailInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelDetailInfoResp proto.InternalMessageInfo

func (m *GetChannelDetailInfoResp) GetChannelBaseinfo() *ChannelBaseInfo {
	if m != nil {
		return m.ChannelBaseinfo
	}
	return nil
}

func (m *GetChannelDetailInfoResp) GetCreaterUid() uint32 {
	if m != nil && m.CreaterUid != nil {
		return *m.CreaterUid
	}
	return 0
}

func (m *GetChannelDetailInfoResp) GetCreateTs() uint32 {
	if m != nil && m.CreateTs != nil {
		return *m.CreateTs
	}
	return 0
}

func (m *GetChannelDetailInfoResp) GetChannelBindType() uint32 {
	if m != nil && m.ChannelBindType != nil {
		return *m.ChannelBindType
	}
	return 0
}

func (m *GetChannelDetailInfoResp) GetBindId() uint32 {
	if m != nil && m.BindId != nil {
		return *m.BindId
	}
	return 0
}

func (m *GetChannelDetailInfoResp) GetDisableMicSize() uint32 {
	if m != nil && m.DisableMicSize != nil {
		return *m.DisableMicSize
	}
	return 0
}

func (m *GetChannelDetailInfoResp) GetMicMode() uint32 {
	if m != nil && m.MicMode != nil {
		return *m.MicMode
	}
	return 0
}

func (m *GetChannelDetailInfoResp) GetSwitchFlag() uint32 {
	if m != nil && m.SwitchFlag != nil {
		return *m.SwitchFlag
	}
	return 0
}

func (m *GetChannelDetailInfoResp) GetIsTempAlloced() bool {
	if m != nil && m.IsTempAlloced != nil {
		return *m.IsTempAlloced
	}
	return false
}

type GetChannelDetailInfoBatchReq struct {
	OpUid                *uint32  `protobuf:"varint,1,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	ChannelIdList        []uint32 `protobuf:"varint,2,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
	MaxGetsize           *uint32  `protobuf:"varint,3,opt,name=max_getsize,json=maxGetsize" json:"max_getsize,omitempty"`
	SortType             *uint32  `protobuf:"varint,4,opt,name=sort_type,json=sortType" json:"sort_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelDetailInfoBatchReq) Reset()         { *m = GetChannelDetailInfoBatchReq{} }
func (m *GetChannelDetailInfoBatchReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelDetailInfoBatchReq) ProtoMessage()    {}
func (*GetChannelDetailInfoBatchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{29}
}
func (m *GetChannelDetailInfoBatchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelDetailInfoBatchReq.Unmarshal(m, b)
}
func (m *GetChannelDetailInfoBatchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelDetailInfoBatchReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelDetailInfoBatchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelDetailInfoBatchReq.Merge(dst, src)
}
func (m *GetChannelDetailInfoBatchReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelDetailInfoBatchReq.Size(m)
}
func (m *GetChannelDetailInfoBatchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelDetailInfoBatchReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelDetailInfoBatchReq proto.InternalMessageInfo

func (m *GetChannelDetailInfoBatchReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

func (m *GetChannelDetailInfoBatchReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *GetChannelDetailInfoBatchReq) GetMaxGetsize() uint32 {
	if m != nil && m.MaxGetsize != nil {
		return *m.MaxGetsize
	}
	return 0
}

func (m *GetChannelDetailInfoBatchReq) GetSortType() uint32 {
	if m != nil && m.SortType != nil {
		return *m.SortType
	}
	return 0
}

type GetChannelDetailInfoBatchResp struct {
	ChannelDetailList    []*ChannelDetailInfo `protobuf:"bytes,1,rep,name=channel_detail_list,json=channelDetailList" json:"channel_detail_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetChannelDetailInfoBatchResp) Reset()         { *m = GetChannelDetailInfoBatchResp{} }
func (m *GetChannelDetailInfoBatchResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelDetailInfoBatchResp) ProtoMessage()    {}
func (*GetChannelDetailInfoBatchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{30}
}
func (m *GetChannelDetailInfoBatchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelDetailInfoBatchResp.Unmarshal(m, b)
}
func (m *GetChannelDetailInfoBatchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelDetailInfoBatchResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelDetailInfoBatchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelDetailInfoBatchResp.Merge(dst, src)
}
func (m *GetChannelDetailInfoBatchResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelDetailInfoBatchResp.Size(m)
}
func (m *GetChannelDetailInfoBatchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelDetailInfoBatchResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelDetailInfoBatchResp proto.InternalMessageInfo

func (m *GetChannelDetailInfoBatchResp) GetChannelDetailList() []*ChannelDetailInfo {
	if m != nil {
		return m.ChannelDetailList
	}
	return nil
}

// 获取频道基础信息
type GetChannelSimpleInfoReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	OpUid                *uint32  `protobuf:"varint,2,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	DisplayId            *uint32  `protobuf:"varint,3,opt,name=display_id,json=displayId" json:"display_id,omitempty"`
	ChannelViewId        *string  `protobuf:"bytes,4,opt,name=channel_view_id,json=channelViewId" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelSimpleInfoReq) Reset()         { *m = GetChannelSimpleInfoReq{} }
func (m *GetChannelSimpleInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelSimpleInfoReq) ProtoMessage()    {}
func (*GetChannelSimpleInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{31}
}
func (m *GetChannelSimpleInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelSimpleInfoReq.Unmarshal(m, b)
}
func (m *GetChannelSimpleInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelSimpleInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelSimpleInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelSimpleInfoReq.Merge(dst, src)
}
func (m *GetChannelSimpleInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelSimpleInfoReq.Size(m)
}
func (m *GetChannelSimpleInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelSimpleInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelSimpleInfoReq proto.InternalMessageInfo

func (m *GetChannelSimpleInfoReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *GetChannelSimpleInfoReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

func (m *GetChannelSimpleInfoReq) GetDisplayId() uint32 {
	if m != nil && m.DisplayId != nil {
		return *m.DisplayId
	}
	return 0
}

func (m *GetChannelSimpleInfoReq) GetChannelViewId() string {
	if m != nil && m.ChannelViewId != nil {
		return *m.ChannelViewId
	}
	return ""
}

type GetChannelSimpleInfoResp struct {
	ChannelSimple        *ChannelSimpleInfo `protobuf:"bytes,1,req,name=channel_simple,json=channelSimple" json:"channel_simple,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetChannelSimpleInfoResp) Reset()         { *m = GetChannelSimpleInfoResp{} }
func (m *GetChannelSimpleInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelSimpleInfoResp) ProtoMessage()    {}
func (*GetChannelSimpleInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{32}
}
func (m *GetChannelSimpleInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelSimpleInfoResp.Unmarshal(m, b)
}
func (m *GetChannelSimpleInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelSimpleInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelSimpleInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelSimpleInfoResp.Merge(dst, src)
}
func (m *GetChannelSimpleInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelSimpleInfoResp.Size(m)
}
func (m *GetChannelSimpleInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelSimpleInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelSimpleInfoResp proto.InternalMessageInfo

func (m *GetChannelSimpleInfoResp) GetChannelSimple() *ChannelSimpleInfo {
	if m != nil {
		return m.ChannelSimple
	}
	return nil
}

type BatchGetChannelSimpleInfoReq struct {
	OpUid                *uint32  `protobuf:"varint,1,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	ChannelIdList        []uint32 `protobuf:"varint,2,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
	DisplayIdList        []uint32 `protobuf:"varint,3,rep,name=display_id_list,json=displayIdList" json:"display_id_list,omitempty"`
	ChannelViewIdList    []string `protobuf:"bytes,4,rep,name=channel_view_id_list,json=channelViewIdList" json:"channel_view_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelSimpleInfoReq) Reset()         { *m = BatchGetChannelSimpleInfoReq{} }
func (m *BatchGetChannelSimpleInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelSimpleInfoReq) ProtoMessage()    {}
func (*BatchGetChannelSimpleInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{33}
}
func (m *BatchGetChannelSimpleInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelSimpleInfoReq.Unmarshal(m, b)
}
func (m *BatchGetChannelSimpleInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelSimpleInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelSimpleInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelSimpleInfoReq.Merge(dst, src)
}
func (m *BatchGetChannelSimpleInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelSimpleInfoReq.Size(m)
}
func (m *BatchGetChannelSimpleInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelSimpleInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelSimpleInfoReq proto.InternalMessageInfo

func (m *BatchGetChannelSimpleInfoReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

func (m *BatchGetChannelSimpleInfoReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *BatchGetChannelSimpleInfoReq) GetDisplayIdList() []uint32 {
	if m != nil {
		return m.DisplayIdList
	}
	return nil
}

func (m *BatchGetChannelSimpleInfoReq) GetChannelViewIdList() []string {
	if m != nil {
		return m.ChannelViewIdList
	}
	return nil
}

type BatchGetChannelSimpleInfoResp struct {
	ChannelSimpleList    []*ChannelSimpleInfo `protobuf:"bytes,1,rep,name=channel_simple_list,json=channelSimpleList" json:"channel_simple_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatchGetChannelSimpleInfoResp) Reset()         { *m = BatchGetChannelSimpleInfoResp{} }
func (m *BatchGetChannelSimpleInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelSimpleInfoResp) ProtoMessage()    {}
func (*BatchGetChannelSimpleInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{34}
}
func (m *BatchGetChannelSimpleInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelSimpleInfoResp.Unmarshal(m, b)
}
func (m *BatchGetChannelSimpleInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelSimpleInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelSimpleInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelSimpleInfoResp.Merge(dst, src)
}
func (m *BatchGetChannelSimpleInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelSimpleInfoResp.Size(m)
}
func (m *BatchGetChannelSimpleInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelSimpleInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelSimpleInfoResp proto.InternalMessageInfo

func (m *BatchGetChannelSimpleInfoResp) GetChannelSimpleList() []*ChannelSimpleInfo {
	if m != nil {
		return m.ChannelSimpleList
	}
	return nil
}

// 获取频道成员数目 (废弃过期)
type GetChannelMemberSizeReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	OpUid                *uint32  `protobuf:"varint,2,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelMemberSizeReq) Reset()         { *m = GetChannelMemberSizeReq{} }
func (m *GetChannelMemberSizeReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelMemberSizeReq) ProtoMessage()    {}
func (*GetChannelMemberSizeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{35}
}
func (m *GetChannelMemberSizeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMemberSizeReq.Unmarshal(m, b)
}
func (m *GetChannelMemberSizeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMemberSizeReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelMemberSizeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMemberSizeReq.Merge(dst, src)
}
func (m *GetChannelMemberSizeReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelMemberSizeReq.Size(m)
}
func (m *GetChannelMemberSizeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMemberSizeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMemberSizeReq proto.InternalMessageInfo

func (m *GetChannelMemberSizeReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *GetChannelMemberSizeReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

type GetChannelMemberSizeResp struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	MemberSize           *uint32  `protobuf:"varint,2,req,name=member_size,json=memberSize" json:"member_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelMemberSizeResp) Reset()         { *m = GetChannelMemberSizeResp{} }
func (m *GetChannelMemberSizeResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelMemberSizeResp) ProtoMessage()    {}
func (*GetChannelMemberSizeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{36}
}
func (m *GetChannelMemberSizeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMemberSizeResp.Unmarshal(m, b)
}
func (m *GetChannelMemberSizeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMemberSizeResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelMemberSizeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMemberSizeResp.Merge(dst, src)
}
func (m *GetChannelMemberSizeResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelMemberSizeResp.Size(m)
}
func (m *GetChannelMemberSizeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMemberSizeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMemberSizeResp proto.InternalMessageInfo

func (m *GetChannelMemberSizeResp) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *GetChannelMemberSizeResp) GetMemberSize() uint32 {
	if m != nil && m.MemberSize != nil {
		return *m.MemberSize
	}
	return 0
}

// 获取频道禁言成员列表
type GetChannelMuteListReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	OpUid                *uint32  `protobuf:"varint,2,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelMuteListReq) Reset()         { *m = GetChannelMuteListReq{} }
func (m *GetChannelMuteListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelMuteListReq) ProtoMessage()    {}
func (*GetChannelMuteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{37}
}
func (m *GetChannelMuteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMuteListReq.Unmarshal(m, b)
}
func (m *GetChannelMuteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMuteListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelMuteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMuteListReq.Merge(dst, src)
}
func (m *GetChannelMuteListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelMuteListReq.Size(m)
}
func (m *GetChannelMuteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMuteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMuteListReq proto.InternalMessageInfo

func (m *GetChannelMuteListReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *GetChannelMuteListReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

type GetChannelMuteListResp struct {
	ChannelId            *uint32                  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	MuteList             []*ChannelMemberBaseInfo `protobuf:"bytes,2,rep,name=mute_list,json=muteList" json:"mute_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetChannelMuteListResp) Reset()         { *m = GetChannelMuteListResp{} }
func (m *GetChannelMuteListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelMuteListResp) ProtoMessage()    {}
func (*GetChannelMuteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{38}
}
func (m *GetChannelMuteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMuteListResp.Unmarshal(m, b)
}
func (m *GetChannelMuteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMuteListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelMuteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMuteListResp.Merge(dst, src)
}
func (m *GetChannelMuteListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelMuteListResp.Size(m)
}
func (m *GetChannelMuteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMuteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMuteListResp proto.InternalMessageInfo

func (m *GetChannelMuteListResp) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *GetChannelMuteListResp) GetMuteList() []*ChannelMemberBaseInfo {
	if m != nil {
		return m.MuteList
	}
	return nil
}

// 接口废弃 不再支持
// 获取麦列表
type GetMicrListReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	OpUid                *uint32  `protobuf:"varint,2,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMicrListReq) Reset()         { *m = GetMicrListReq{} }
func (m *GetMicrListReq) String() string { return proto.CompactTextString(m) }
func (*GetMicrListReq) ProtoMessage()    {}
func (*GetMicrListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{39}
}
func (m *GetMicrListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMicrListReq.Unmarshal(m, b)
}
func (m *GetMicrListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMicrListReq.Marshal(b, m, deterministic)
}
func (dst *GetMicrListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMicrListReq.Merge(dst, src)
}
func (m *GetMicrListReq) XXX_Size() int {
	return xxx_messageInfo_GetMicrListReq.Size(m)
}
func (m *GetMicrListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMicrListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMicrListReq proto.InternalMessageInfo

func (m *GetMicrListReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *GetMicrListReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

// 接口废弃 不再支持
type GetMicrListResp struct {
	ChannelId            *uint32                  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	OpenMicrList         []*ChannelMemberBaseInfo `protobuf:"bytes,2,rep,name=openMicr_list,json=openMicrList" json:"openMicr_list,omitempty"`
	AllDisabledMicsize   *uint32                  `protobuf:"varint,3,opt,name=all_disabled_micsize,json=allDisabledMicsize" json:"all_disabled_micsize,omitempty"`
	AllMicList           []*MicrSpaceInfo         `protobuf:"bytes,4,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	MicrMode             *uint32                  `protobuf:"varint,5,opt,name=micr_mode,json=micrMode" json:"micr_mode,omitempty"`
	ServerTimeMs         *uint64                  `protobuf:"varint,6,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetMicrListResp) Reset()         { *m = GetMicrListResp{} }
func (m *GetMicrListResp) String() string { return proto.CompactTextString(m) }
func (*GetMicrListResp) ProtoMessage()    {}
func (*GetMicrListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{40}
}
func (m *GetMicrListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMicrListResp.Unmarshal(m, b)
}
func (m *GetMicrListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMicrListResp.Marshal(b, m, deterministic)
}
func (dst *GetMicrListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMicrListResp.Merge(dst, src)
}
func (m *GetMicrListResp) XXX_Size() int {
	return xxx_messageInfo_GetMicrListResp.Size(m)
}
func (m *GetMicrListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMicrListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMicrListResp proto.InternalMessageInfo

func (m *GetMicrListResp) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *GetMicrListResp) GetOpenMicrList() []*ChannelMemberBaseInfo {
	if m != nil {
		return m.OpenMicrList
	}
	return nil
}

func (m *GetMicrListResp) GetAllDisabledMicsize() uint32 {
	if m != nil && m.AllDisabledMicsize != nil {
		return *m.AllDisabledMicsize
	}
	return 0
}

func (m *GetMicrListResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *GetMicrListResp) GetMicrMode() uint32 {
	if m != nil && m.MicrMode != nil {
		return *m.MicrMode
	}
	return 0
}

func (m *GetMicrListResp) GetServerTimeMs() uint64 {
	if m != nil && m.ServerTimeMs != nil {
		return *m.ServerTimeMs
	}
	return 0
}

// 获取与绑定ID关联的 频道列表
// 比如获取指定公会ID绑定的频道列表(按照当前人数排序)
type GetChannelListByBindIdReq struct {
	OpUid                *uint32  `protobuf:"varint,1,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	MaxGetsize           *uint32  `protobuf:"varint,2,req,name=max_getsize,json=maxGetsize" json:"max_getsize,omitempty"`
	BindID               *uint32  `protobuf:"varint,3,req,name=bindID" json:"bindID,omitempty"`
	BindType             *uint32  `protobuf:"varint,4,req,name=bind_type,json=bindType" json:"bind_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelListByBindIdReq) Reset()         { *m = GetChannelListByBindIdReq{} }
func (m *GetChannelListByBindIdReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelListByBindIdReq) ProtoMessage()    {}
func (*GetChannelListByBindIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{41}
}
func (m *GetChannelListByBindIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelListByBindIdReq.Unmarshal(m, b)
}
func (m *GetChannelListByBindIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelListByBindIdReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelListByBindIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelListByBindIdReq.Merge(dst, src)
}
func (m *GetChannelListByBindIdReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelListByBindIdReq.Size(m)
}
func (m *GetChannelListByBindIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelListByBindIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelListByBindIdReq proto.InternalMessageInfo

func (m *GetChannelListByBindIdReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

func (m *GetChannelListByBindIdReq) GetMaxGetsize() uint32 {
	if m != nil && m.MaxGetsize != nil {
		return *m.MaxGetsize
	}
	return 0
}

func (m *GetChannelListByBindIdReq) GetBindID() uint32 {
	if m != nil && m.BindID != nil {
		return *m.BindID
	}
	return 0
}

func (m *GetChannelListByBindIdReq) GetBindType() uint32 {
	if m != nil && m.BindType != nil {
		return *m.BindType
	}
	return 0
}

type GetChannelListByBindIdResp struct {
	BindID               *uint32            `protobuf:"varint,1,req,name=bindID" json:"bindID,omitempty"`
	BindType             *uint32            `protobuf:"varint,2,req,name=bind_type,json=bindType" json:"bind_type,omitempty"`
	ChannelList          []*ChannelBaseInfo `protobuf:"bytes,3,rep,name=channel_list,json=channelList" json:"channel_list,omitempty"`
	TotalChannelSize     *uint32            `protobuf:"varint,4,opt,name=total_channel_size,json=totalChannelSize" json:"total_channel_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetChannelListByBindIdResp) Reset()         { *m = GetChannelListByBindIdResp{} }
func (m *GetChannelListByBindIdResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelListByBindIdResp) ProtoMessage()    {}
func (*GetChannelListByBindIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{42}
}
func (m *GetChannelListByBindIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelListByBindIdResp.Unmarshal(m, b)
}
func (m *GetChannelListByBindIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelListByBindIdResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelListByBindIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelListByBindIdResp.Merge(dst, src)
}
func (m *GetChannelListByBindIdResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelListByBindIdResp.Size(m)
}
func (m *GetChannelListByBindIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelListByBindIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelListByBindIdResp proto.InternalMessageInfo

func (m *GetChannelListByBindIdResp) GetBindID() uint32 {
	if m != nil && m.BindID != nil {
		return *m.BindID
	}
	return 0
}

func (m *GetChannelListByBindIdResp) GetBindType() uint32 {
	if m != nil && m.BindType != nil {
		return *m.BindType
	}
	return 0
}

func (m *GetChannelListByBindIdResp) GetChannelList() []*ChannelBaseInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *GetChannelListByBindIdResp) GetTotalChannelSize() uint32 {
	if m != nil && m.TotalChannelSize != nil {
		return *m.TotalChannelSize
	}
	return 0
}

// 获取的频道的绑定关系
// 比如获取频道是属于哪个公会的
type GetChannelBindInfoReq struct {
	ChannelID            *uint32  `protobuf:"varint,1,req,name=channelID" json:"channelID,omitempty"`
	OpUid                *uint32  `protobuf:"varint,2,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelBindInfoReq) Reset()         { *m = GetChannelBindInfoReq{} }
func (m *GetChannelBindInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelBindInfoReq) ProtoMessage()    {}
func (*GetChannelBindInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{43}
}
func (m *GetChannelBindInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelBindInfoReq.Unmarshal(m, b)
}
func (m *GetChannelBindInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelBindInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelBindInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelBindInfoReq.Merge(dst, src)
}
func (m *GetChannelBindInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelBindInfoReq.Size(m)
}
func (m *GetChannelBindInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelBindInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelBindInfoReq proto.InternalMessageInfo

func (m *GetChannelBindInfoReq) GetChannelID() uint32 {
	if m != nil && m.ChannelID != nil {
		return *m.ChannelID
	}
	return 0
}

func (m *GetChannelBindInfoReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

type GetChannelBindInfoResp struct {
	BindID               *uint32  `protobuf:"varint,1,req,name=bindID" json:"bindID,omitempty"`
	BindType             *uint32  `protobuf:"varint,2,req,name=bind_type,json=bindType" json:"bind_type,omitempty"`
	AppId                *uint32  `protobuf:"varint,3,opt,name=app_id,json=appId" json:"app_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelBindInfoResp) Reset()         { *m = GetChannelBindInfoResp{} }
func (m *GetChannelBindInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelBindInfoResp) ProtoMessage()    {}
func (*GetChannelBindInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{44}
}
func (m *GetChannelBindInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelBindInfoResp.Unmarshal(m, b)
}
func (m *GetChannelBindInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelBindInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelBindInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelBindInfoResp.Merge(dst, src)
}
func (m *GetChannelBindInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelBindInfoResp.Size(m)
}
func (m *GetChannelBindInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelBindInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelBindInfoResp proto.InternalMessageInfo

func (m *GetChannelBindInfoResp) GetBindID() uint32 {
	if m != nil && m.BindID != nil {
		return *m.BindID
	}
	return 0
}

func (m *GetChannelBindInfoResp) GetBindType() uint32 {
	if m != nil && m.BindType != nil {
		return *m.BindType
	}
	return 0
}

func (m *GetChannelBindInfoResp) GetAppId() uint32 {
	if m != nil && m.AppId != nil {
		return *m.AppId
	}
	return 0
}

// 获取用户当前频道ID
// message GetUserChannelIDReq
// {
// 	required uint32 uid = 1;
// }
//
// message GetUserChannelIDResp
// {
// 	required uint32 channelID = 1;
// 	optional uint32 appid = 2;      // 房间的APP类型
// }
//
type UserChannelID struct {
	Uid                  *uint32  `protobuf:"varint,1,req,name=uid" json:"uid,omitempty"`
	ChannelID            *uint32  `protobuf:"varint,2,req,name=channelID" json:"channelID,omitempty"`
	Appid                *uint32  `protobuf:"varint,3,opt,name=appid" json:"appid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserChannelID) Reset()         { *m = UserChannelID{} }
func (m *UserChannelID) String() string { return proto.CompactTextString(m) }
func (*UserChannelID) ProtoMessage()    {}
func (*UserChannelID) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{45}
}
func (m *UserChannelID) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserChannelID.Unmarshal(m, b)
}
func (m *UserChannelID) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserChannelID.Marshal(b, m, deterministic)
}
func (dst *UserChannelID) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserChannelID.Merge(dst, src)
}
func (m *UserChannelID) XXX_Size() int {
	return xxx_messageInfo_UserChannelID.Size(m)
}
func (m *UserChannelID) XXX_DiscardUnknown() {
	xxx_messageInfo_UserChannelID.DiscardUnknown(m)
}

var xxx_messageInfo_UserChannelID proto.InternalMessageInfo

func (m *UserChannelID) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *UserChannelID) GetChannelID() uint32 {
	if m != nil && m.ChannelID != nil {
		return *m.ChannelID
	}
	return 0
}

func (m *UserChannelID) GetAppid() uint32 {
	if m != nil && m.Appid != nil {
		return *m.Appid
	}
	return 0
}

// 检查用户是否被禁言
type CheckUserIsMuteReq struct {
	Uid                  *uint32  `protobuf:"varint,1,req,name=uid" json:"uid,omitempty"`
	ChannelId            *uint32  `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserIsMuteReq) Reset()         { *m = CheckUserIsMuteReq{} }
func (m *CheckUserIsMuteReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserIsMuteReq) ProtoMessage()    {}
func (*CheckUserIsMuteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{46}
}
func (m *CheckUserIsMuteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserIsMuteReq.Unmarshal(m, b)
}
func (m *CheckUserIsMuteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserIsMuteReq.Marshal(b, m, deterministic)
}
func (dst *CheckUserIsMuteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserIsMuteReq.Merge(dst, src)
}
func (m *CheckUserIsMuteReq) XXX_Size() int {
	return xxx_messageInfo_CheckUserIsMuteReq.Size(m)
}
func (m *CheckUserIsMuteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserIsMuteReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserIsMuteReq proto.InternalMessageInfo

func (m *CheckUserIsMuteReq) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *CheckUserIsMuteReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

type CheckUserIsMuteResp struct {
	Uid                  *uint32  `protobuf:"varint,1,req,name=uid" json:"uid,omitempty"`
	ChannelId            *uint32  `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	IsMute               *bool    `protobuf:"varint,3,req,name=is_mute,json=isMute" json:"is_mute,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserIsMuteResp) Reset()         { *m = CheckUserIsMuteResp{} }
func (m *CheckUserIsMuteResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserIsMuteResp) ProtoMessage()    {}
func (*CheckUserIsMuteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{47}
}
func (m *CheckUserIsMuteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserIsMuteResp.Unmarshal(m, b)
}
func (m *CheckUserIsMuteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserIsMuteResp.Marshal(b, m, deterministic)
}
func (dst *CheckUserIsMuteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserIsMuteResp.Merge(dst, src)
}
func (m *CheckUserIsMuteResp) XXX_Size() int {
	return xxx_messageInfo_CheckUserIsMuteResp.Size(m)
}
func (m *CheckUserIsMuteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserIsMuteResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserIsMuteResp proto.InternalMessageInfo

func (m *CheckUserIsMuteResp) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *CheckUserIsMuteResp) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *CheckUserIsMuteResp) GetIsMute() bool {
	if m != nil && m.IsMute != nil {
		return *m.IsMute
	}
	return false
}

type User2ChannelRelation struct {
	Uid                  *uint32  `protobuf:"varint,1,req,name=uid" json:"uid,omitempty"`
	Channelid            *uint32  `protobuf:"varint,2,opt,name=channelid" json:"channelid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *User2ChannelRelation) Reset()         { *m = User2ChannelRelation{} }
func (m *User2ChannelRelation) String() string { return proto.CompactTextString(m) }
func (*User2ChannelRelation) ProtoMessage()    {}
func (*User2ChannelRelation) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{48}
}
func (m *User2ChannelRelation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_User2ChannelRelation.Unmarshal(m, b)
}
func (m *User2ChannelRelation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_User2ChannelRelation.Marshal(b, m, deterministic)
}
func (dst *User2ChannelRelation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_User2ChannelRelation.Merge(dst, src)
}
func (m *User2ChannelRelation) XXX_Size() int {
	return xxx_messageInfo_User2ChannelRelation.Size(m)
}
func (m *User2ChannelRelation) XXX_DiscardUnknown() {
	xxx_messageInfo_User2ChannelRelation.DiscardUnknown(m)
}

var xxx_messageInfo_User2ChannelRelation proto.InternalMessageInfo

func (m *User2ChannelRelation) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *User2ChannelRelation) GetChannelid() uint32 {
	if m != nil && m.Channelid != nil {
		return *m.Channelid
	}
	return 0
}

// 将用户踢出公会下面的所在频道
// 比如用户被踢出公会 需要被踢出当前公会所在的频道
type KickUserOutOfGuildChannelReq struct {
	OpUid                *uint32  `protobuf:"varint,1,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	BindID               *uint32  `protobuf:"varint,2,req,name=bindID" json:"bindID,omitempty"`
	BindType             *uint32  `protobuf:"varint,3,req,name=bind_type,json=bindType" json:"bind_type,omitempty"`
	UidList              []uint32 `protobuf:"varint,4,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KickUserOutOfGuildChannelReq) Reset()         { *m = KickUserOutOfGuildChannelReq{} }
func (m *KickUserOutOfGuildChannelReq) String() string { return proto.CompactTextString(m) }
func (*KickUserOutOfGuildChannelReq) ProtoMessage()    {}
func (*KickUserOutOfGuildChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{49}
}
func (m *KickUserOutOfGuildChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KickUserOutOfGuildChannelReq.Unmarshal(m, b)
}
func (m *KickUserOutOfGuildChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KickUserOutOfGuildChannelReq.Marshal(b, m, deterministic)
}
func (dst *KickUserOutOfGuildChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KickUserOutOfGuildChannelReq.Merge(dst, src)
}
func (m *KickUserOutOfGuildChannelReq) XXX_Size() int {
	return xxx_messageInfo_KickUserOutOfGuildChannelReq.Size(m)
}
func (m *KickUserOutOfGuildChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_KickUserOutOfGuildChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_KickUserOutOfGuildChannelReq proto.InternalMessageInfo

func (m *KickUserOutOfGuildChannelReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

func (m *KickUserOutOfGuildChannelReq) GetBindID() uint32 {
	if m != nil && m.BindID != nil {
		return *m.BindID
	}
	return 0
}

func (m *KickUserOutOfGuildChannelReq) GetBindType() uint32 {
	if m != nil && m.BindType != nil {
		return *m.BindType
	}
	return 0
}

func (m *KickUserOutOfGuildChannelReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type KickUserOutOfGuildChannelResp struct {
	BindID               *uint32                 `protobuf:"varint,1,req,name=bindID" json:"bindID,omitempty"`
	BindType             *uint32                 `protobuf:"varint,2,req,name=bind_type,json=bindType" json:"bind_type,omitempty"`
	User2ChannelList     []*User2ChannelRelation `protobuf:"bytes,3,rep,name=user2channel_list,json=user2channelList" json:"user2channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *KickUserOutOfGuildChannelResp) Reset()         { *m = KickUserOutOfGuildChannelResp{} }
func (m *KickUserOutOfGuildChannelResp) String() string { return proto.CompactTextString(m) }
func (*KickUserOutOfGuildChannelResp) ProtoMessage()    {}
func (*KickUserOutOfGuildChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{50}
}
func (m *KickUserOutOfGuildChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KickUserOutOfGuildChannelResp.Unmarshal(m, b)
}
func (m *KickUserOutOfGuildChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KickUserOutOfGuildChannelResp.Marshal(b, m, deterministic)
}
func (dst *KickUserOutOfGuildChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KickUserOutOfGuildChannelResp.Merge(dst, src)
}
func (m *KickUserOutOfGuildChannelResp) XXX_Size() int {
	return xxx_messageInfo_KickUserOutOfGuildChannelResp.Size(m)
}
func (m *KickUserOutOfGuildChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_KickUserOutOfGuildChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_KickUserOutOfGuildChannelResp proto.InternalMessageInfo

func (m *KickUserOutOfGuildChannelResp) GetBindID() uint32 {
	if m != nil && m.BindID != nil {
		return *m.BindID
	}
	return 0
}

func (m *KickUserOutOfGuildChannelResp) GetBindType() uint32 {
	if m != nil && m.BindType != nil {
		return *m.BindType
	}
	return 0
}

func (m *KickUserOutOfGuildChannelResp) GetUser2ChannelList() []*User2ChannelRelation {
	if m != nil {
		return m.User2ChannelList
	}
	return nil
}

// 接口废弃不再支持
// 关闭麦位入口
type DisableChannelMicEntryReq struct {
	OpUid                *uint32  `protobuf:"varint,1,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	ChannelId            *uint32  `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	DisableMicSize       *uint32  `protobuf:"varint,3,opt,name=disable_mic_size,json=disableMicSize" json:"disable_mic_size,omitempty"`
	KickUid              *uint32  `protobuf:"varint,4,opt,name=kick_uid,json=kickUid" json:"kick_uid,omitempty"`
	MicPosId             *uint32  `protobuf:"varint,5,opt,name=mic_pos_id,json=micPosId" json:"mic_pos_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisableChannelMicEntryReq) Reset()         { *m = DisableChannelMicEntryReq{} }
func (m *DisableChannelMicEntryReq) String() string { return proto.CompactTextString(m) }
func (*DisableChannelMicEntryReq) ProtoMessage()    {}
func (*DisableChannelMicEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{51}
}
func (m *DisableChannelMicEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisableChannelMicEntryReq.Unmarshal(m, b)
}
func (m *DisableChannelMicEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisableChannelMicEntryReq.Marshal(b, m, deterministic)
}
func (dst *DisableChannelMicEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisableChannelMicEntryReq.Merge(dst, src)
}
func (m *DisableChannelMicEntryReq) XXX_Size() int {
	return xxx_messageInfo_DisableChannelMicEntryReq.Size(m)
}
func (m *DisableChannelMicEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DisableChannelMicEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_DisableChannelMicEntryReq proto.InternalMessageInfo

func (m *DisableChannelMicEntryReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

func (m *DisableChannelMicEntryReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *DisableChannelMicEntryReq) GetDisableMicSize() uint32 {
	if m != nil && m.DisableMicSize != nil {
		return *m.DisableMicSize
	}
	return 0
}

func (m *DisableChannelMicEntryReq) GetKickUid() uint32 {
	if m != nil && m.KickUid != nil {
		return *m.KickUid
	}
	return 0
}

func (m *DisableChannelMicEntryReq) GetMicPosId() uint32 {
	if m != nil && m.MicPosId != nil {
		return *m.MicPosId
	}
	return 0
}

// 接口废弃不再支持
type DisableChannelMicEntryResp struct {
	ChannelId            *uint32          `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	AllCloseSize         *uint32          `protobuf:"varint,2,req,name=all_close_size,json=allCloseSize" json:"all_close_size,omitempty"`
	MicPosId             *uint32          `protobuf:"varint,3,opt,name=mic_pos_id,json=micPosId" json:"mic_pos_id,omitempty"`
	AllMicList           []*MicrSpaceInfo `protobuf:"bytes,4,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	ServerTimeMs         *uint64          `protobuf:"varint,5,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *DisableChannelMicEntryResp) Reset()         { *m = DisableChannelMicEntryResp{} }
func (m *DisableChannelMicEntryResp) String() string { return proto.CompactTextString(m) }
func (*DisableChannelMicEntryResp) ProtoMessage()    {}
func (*DisableChannelMicEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{52}
}
func (m *DisableChannelMicEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisableChannelMicEntryResp.Unmarshal(m, b)
}
func (m *DisableChannelMicEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisableChannelMicEntryResp.Marshal(b, m, deterministic)
}
func (dst *DisableChannelMicEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisableChannelMicEntryResp.Merge(dst, src)
}
func (m *DisableChannelMicEntryResp) XXX_Size() int {
	return xxx_messageInfo_DisableChannelMicEntryResp.Size(m)
}
func (m *DisableChannelMicEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DisableChannelMicEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_DisableChannelMicEntryResp proto.InternalMessageInfo

func (m *DisableChannelMicEntryResp) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *DisableChannelMicEntryResp) GetAllCloseSize() uint32 {
	if m != nil && m.AllCloseSize != nil {
		return *m.AllCloseSize
	}
	return 0
}

func (m *DisableChannelMicEntryResp) GetMicPosId() uint32 {
	if m != nil && m.MicPosId != nil {
		return *m.MicPosId
	}
	return 0
}

func (m *DisableChannelMicEntryResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *DisableChannelMicEntryResp) GetServerTimeMs() uint64 {
	if m != nil && m.ServerTimeMs != nil {
		return *m.ServerTimeMs
	}
	return 0
}

// 接口废弃不再支持
// 开启麦位入口
type EnableChannelMicEntryReq struct {
	OpUid                *uint32  `protobuf:"varint,1,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	ChannelId            *uint32  `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	MicPosId             *uint32  `protobuf:"varint,3,opt,name=mic_pos_id,json=micPosId" json:"mic_pos_id,omitempty"`
	OpenMic              *bool    `protobuf:"varint,4,opt,name=open_mic,json=openMic" json:"open_mic,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EnableChannelMicEntryReq) Reset()         { *m = EnableChannelMicEntryReq{} }
func (m *EnableChannelMicEntryReq) String() string { return proto.CompactTextString(m) }
func (*EnableChannelMicEntryReq) ProtoMessage()    {}
func (*EnableChannelMicEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{53}
}
func (m *EnableChannelMicEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnableChannelMicEntryReq.Unmarshal(m, b)
}
func (m *EnableChannelMicEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnableChannelMicEntryReq.Marshal(b, m, deterministic)
}
func (dst *EnableChannelMicEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnableChannelMicEntryReq.Merge(dst, src)
}
func (m *EnableChannelMicEntryReq) XXX_Size() int {
	return xxx_messageInfo_EnableChannelMicEntryReq.Size(m)
}
func (m *EnableChannelMicEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EnableChannelMicEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_EnableChannelMicEntryReq proto.InternalMessageInfo

func (m *EnableChannelMicEntryReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

func (m *EnableChannelMicEntryReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *EnableChannelMicEntryReq) GetMicPosId() uint32 {
	if m != nil && m.MicPosId != nil {
		return *m.MicPosId
	}
	return 0
}

func (m *EnableChannelMicEntryReq) GetOpenMic() bool {
	if m != nil && m.OpenMic != nil {
		return *m.OpenMic
	}
	return false
}

// 接口废弃不再支持
type EnableChannelMicEntryResp struct {
	ChannelId            *uint32          `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	AllCloseSize         *uint32          `protobuf:"varint,2,req,name=all_close_size,json=allCloseSize" json:"all_close_size,omitempty"`
	MicPosId             *uint32          `protobuf:"varint,3,opt,name=mic_pos_id,json=micPosId" json:"mic_pos_id,omitempty"`
	AllMicList           []*MicrSpaceInfo `protobuf:"bytes,4,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	ServerTimeMs         *uint64          `protobuf:"varint,5,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *EnableChannelMicEntryResp) Reset()         { *m = EnableChannelMicEntryResp{} }
func (m *EnableChannelMicEntryResp) String() string { return proto.CompactTextString(m) }
func (*EnableChannelMicEntryResp) ProtoMessage()    {}
func (*EnableChannelMicEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{54}
}
func (m *EnableChannelMicEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnableChannelMicEntryResp.Unmarshal(m, b)
}
func (m *EnableChannelMicEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnableChannelMicEntryResp.Marshal(b, m, deterministic)
}
func (dst *EnableChannelMicEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnableChannelMicEntryResp.Merge(dst, src)
}
func (m *EnableChannelMicEntryResp) XXX_Size() int {
	return xxx_messageInfo_EnableChannelMicEntryResp.Size(m)
}
func (m *EnableChannelMicEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EnableChannelMicEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_EnableChannelMicEntryResp proto.InternalMessageInfo

func (m *EnableChannelMicEntryResp) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *EnableChannelMicEntryResp) GetAllCloseSize() uint32 {
	if m != nil && m.AllCloseSize != nil {
		return *m.AllCloseSize
	}
	return 0
}

func (m *EnableChannelMicEntryResp) GetMicPosId() uint32 {
	if m != nil && m.MicPosId != nil {
		return *m.MicPosId
	}
	return 0
}

func (m *EnableChannelMicEntryResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *EnableChannelMicEntryResp) GetServerTimeMs() uint64 {
	if m != nil && m.ServerTimeMs != nil {
		return *m.ServerTimeMs
	}
	return 0
}

// 踢下麦 // 接口废弃不再支持
type KickoutChannelMicReq struct {
	OpUid                *uint32  `protobuf:"varint,1,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	ChannelId            *uint32  `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	TargetUidList        []uint32 `protobuf:"varint,3,rep,name=target_uid_list,json=targetUidList" json:"target_uid_list,omitempty"`
	BanSecond            *uint32  `protobuf:"varint,4,opt,name=ban_second,json=banSecond" json:"ban_second,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KickoutChannelMicReq) Reset()         { *m = KickoutChannelMicReq{} }
func (m *KickoutChannelMicReq) String() string { return proto.CompactTextString(m) }
func (*KickoutChannelMicReq) ProtoMessage()    {}
func (*KickoutChannelMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{55}
}
func (m *KickoutChannelMicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KickoutChannelMicReq.Unmarshal(m, b)
}
func (m *KickoutChannelMicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KickoutChannelMicReq.Marshal(b, m, deterministic)
}
func (dst *KickoutChannelMicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KickoutChannelMicReq.Merge(dst, src)
}
func (m *KickoutChannelMicReq) XXX_Size() int {
	return xxx_messageInfo_KickoutChannelMicReq.Size(m)
}
func (m *KickoutChannelMicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_KickoutChannelMicReq.DiscardUnknown(m)
}

var xxx_messageInfo_KickoutChannelMicReq proto.InternalMessageInfo

func (m *KickoutChannelMicReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

func (m *KickoutChannelMicReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *KickoutChannelMicReq) GetTargetUidList() []uint32 {
	if m != nil {
		return m.TargetUidList
	}
	return nil
}

func (m *KickoutChannelMicReq) GetBanSecond() uint32 {
	if m != nil && m.BanSecond != nil {
		return *m.BanSecond
	}
	return 0
}

// 接口废弃不再支持
type KickoutChannelMicResp struct {
	ChannelId            *uint32          `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	DisableMicIdList     []uint32         `protobuf:"varint,2,rep,name=disable_mic_id_list,json=disableMicIdList" json:"disable_mic_id_list,omitempty"`
	KickoutMicList       []*MicrSpaceInfo `protobuf:"bytes,3,rep,name=kickout_mic_list,json=kickoutMicList" json:"kickout_mic_list,omitempty"`
	AllMicList           []*MicrSpaceInfo `protobuf:"bytes,4,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	ServerTimeMs         *uint64          `protobuf:"varint,5,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *KickoutChannelMicResp) Reset()         { *m = KickoutChannelMicResp{} }
func (m *KickoutChannelMicResp) String() string { return proto.CompactTextString(m) }
func (*KickoutChannelMicResp) ProtoMessage()    {}
func (*KickoutChannelMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{56}
}
func (m *KickoutChannelMicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KickoutChannelMicResp.Unmarshal(m, b)
}
func (m *KickoutChannelMicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KickoutChannelMicResp.Marshal(b, m, deterministic)
}
func (dst *KickoutChannelMicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KickoutChannelMicResp.Merge(dst, src)
}
func (m *KickoutChannelMicResp) XXX_Size() int {
	return xxx_messageInfo_KickoutChannelMicResp.Size(m)
}
func (m *KickoutChannelMicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_KickoutChannelMicResp.DiscardUnknown(m)
}

var xxx_messageInfo_KickoutChannelMicResp proto.InternalMessageInfo

func (m *KickoutChannelMicResp) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *KickoutChannelMicResp) GetDisableMicIdList() []uint32 {
	if m != nil {
		return m.DisableMicIdList
	}
	return nil
}

func (m *KickoutChannelMicResp) GetKickoutMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.KickoutMicList
	}
	return nil
}

func (m *KickoutChannelMicResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *KickoutChannelMicResp) GetServerTimeMs() uint64 {
	if m != nil && m.ServerTimeMs != nil {
		return *m.ServerTimeMs
	}
	return 0
}

// 踢人// 接口废弃不再支持
type KickoutChannelMemberReq struct {
	OpUid                *uint32  `protobuf:"varint,1,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	ChannelId            *uint32  `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	TargetUidList        []uint32 `protobuf:"varint,3,rep,name=target_uid_list,json=targetUidList" json:"target_uid_list,omitempty"`
	BanEnterSecond       *uint32  `protobuf:"varint,4,opt,name=ban_enter_second,json=banEnterSecond" json:"ban_enter_second,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KickoutChannelMemberReq) Reset()         { *m = KickoutChannelMemberReq{} }
func (m *KickoutChannelMemberReq) String() string { return proto.CompactTextString(m) }
func (*KickoutChannelMemberReq) ProtoMessage()    {}
func (*KickoutChannelMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{57}
}
func (m *KickoutChannelMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KickoutChannelMemberReq.Unmarshal(m, b)
}
func (m *KickoutChannelMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KickoutChannelMemberReq.Marshal(b, m, deterministic)
}
func (dst *KickoutChannelMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KickoutChannelMemberReq.Merge(dst, src)
}
func (m *KickoutChannelMemberReq) XXX_Size() int {
	return xxx_messageInfo_KickoutChannelMemberReq.Size(m)
}
func (m *KickoutChannelMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_KickoutChannelMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_KickoutChannelMemberReq proto.InternalMessageInfo

func (m *KickoutChannelMemberReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

func (m *KickoutChannelMemberReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *KickoutChannelMemberReq) GetTargetUidList() []uint32 {
	if m != nil {
		return m.TargetUidList
	}
	return nil
}

func (m *KickoutChannelMemberReq) GetBanEnterSecond() uint32 {
	if m != nil && m.BanEnterSecond != nil {
		return *m.BanEnterSecond
	}
	return 0
}

// 接口废弃不再支持
type KickoutChannelMemberResp struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	SuccessUidList       []uint32 `protobuf:"varint,2,rep,name=success_uid_list,json=successUidList" json:"success_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KickoutChannelMemberResp) Reset()         { *m = KickoutChannelMemberResp{} }
func (m *KickoutChannelMemberResp) String() string { return proto.CompactTextString(m) }
func (*KickoutChannelMemberResp) ProtoMessage()    {}
func (*KickoutChannelMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{58}
}
func (m *KickoutChannelMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KickoutChannelMemberResp.Unmarshal(m, b)
}
func (m *KickoutChannelMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KickoutChannelMemberResp.Marshal(b, m, deterministic)
}
func (dst *KickoutChannelMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KickoutChannelMemberResp.Merge(dst, src)
}
func (m *KickoutChannelMemberResp) XXX_Size() int {
	return xxx_messageInfo_KickoutChannelMemberResp.Size(m)
}
func (m *KickoutChannelMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_KickoutChannelMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_KickoutChannelMemberResp proto.InternalMessageInfo

func (m *KickoutChannelMemberResp) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *KickoutChannelMemberResp) GetSuccessUidList() []uint32 {
	if m != nil {
		return m.SuccessUidList
	}
	return nil
}

type CheckUserKickoutFromChannelReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	Uid                  *uint32  `protobuf:"varint,2,req,name=uid" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserKickoutFromChannelReq) Reset()         { *m = CheckUserKickoutFromChannelReq{} }
func (m *CheckUserKickoutFromChannelReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserKickoutFromChannelReq) ProtoMessage()    {}
func (*CheckUserKickoutFromChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{59}
}
func (m *CheckUserKickoutFromChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserKickoutFromChannelReq.Unmarshal(m, b)
}
func (m *CheckUserKickoutFromChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserKickoutFromChannelReq.Marshal(b, m, deterministic)
}
func (dst *CheckUserKickoutFromChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserKickoutFromChannelReq.Merge(dst, src)
}
func (m *CheckUserKickoutFromChannelReq) XXX_Size() int {
	return xxx_messageInfo_CheckUserKickoutFromChannelReq.Size(m)
}
func (m *CheckUserKickoutFromChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserKickoutFromChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserKickoutFromChannelReq proto.InternalMessageInfo

func (m *CheckUserKickoutFromChannelReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *CheckUserKickoutFromChannelReq) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

type CheckUserKickoutFromChannelResp struct {
	IsKicked             *bool    `protobuf:"varint,1,opt,name=is_kicked,json=isKicked" json:"is_kicked,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserKickoutFromChannelResp) Reset()         { *m = CheckUserKickoutFromChannelResp{} }
func (m *CheckUserKickoutFromChannelResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserKickoutFromChannelResp) ProtoMessage()    {}
func (*CheckUserKickoutFromChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{60}
}
func (m *CheckUserKickoutFromChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserKickoutFromChannelResp.Unmarshal(m, b)
}
func (m *CheckUserKickoutFromChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserKickoutFromChannelResp.Marshal(b, m, deterministic)
}
func (dst *CheckUserKickoutFromChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserKickoutFromChannelResp.Merge(dst, src)
}
func (m *CheckUserKickoutFromChannelResp) XXX_Size() int {
	return xxx_messageInfo_CheckUserKickoutFromChannelResp.Size(m)
}
func (m *CheckUserKickoutFromChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserKickoutFromChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserKickoutFromChannelResp proto.InternalMessageInfo

func (m *CheckUserKickoutFromChannelResp) GetIsKicked() bool {
	if m != nil && m.IsKicked != nil {
		return *m.IsKicked
	}
	return false
}

type KickoutChannelMemberLiteReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	TargetUids           []uint32 `protobuf:"varint,2,rep,name=target_uids,json=targetUids" json:"target_uids,omitempty"`
	BanDuration          *uint32  `protobuf:"varint,3,opt,name=ban_duration,json=banDuration" json:"ban_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KickoutChannelMemberLiteReq) Reset()         { *m = KickoutChannelMemberLiteReq{} }
func (m *KickoutChannelMemberLiteReq) String() string { return proto.CompactTextString(m) }
func (*KickoutChannelMemberLiteReq) ProtoMessage()    {}
func (*KickoutChannelMemberLiteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{61}
}
func (m *KickoutChannelMemberLiteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KickoutChannelMemberLiteReq.Unmarshal(m, b)
}
func (m *KickoutChannelMemberLiteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KickoutChannelMemberLiteReq.Marshal(b, m, deterministic)
}
func (dst *KickoutChannelMemberLiteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KickoutChannelMemberLiteReq.Merge(dst, src)
}
func (m *KickoutChannelMemberLiteReq) XXX_Size() int {
	return xxx_messageInfo_KickoutChannelMemberLiteReq.Size(m)
}
func (m *KickoutChannelMemberLiteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_KickoutChannelMemberLiteReq.DiscardUnknown(m)
}

var xxx_messageInfo_KickoutChannelMemberLiteReq proto.InternalMessageInfo

func (m *KickoutChannelMemberLiteReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *KickoutChannelMemberLiteReq) GetTargetUids() []uint32 {
	if m != nil {
		return m.TargetUids
	}
	return nil
}

func (m *KickoutChannelMemberLiteReq) GetBanDuration() uint32 {
	if m != nil && m.BanDuration != nil {
		return *m.BanDuration
	}
	return 0
}

type KickoutChannelMemberLiteResp struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	KickoutedUids        []uint32 `protobuf:"varint,2,rep,name=kickouted_uids,json=kickoutedUids" json:"kickouted_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KickoutChannelMemberLiteResp) Reset()         { *m = KickoutChannelMemberLiteResp{} }
func (m *KickoutChannelMemberLiteResp) String() string { return proto.CompactTextString(m) }
func (*KickoutChannelMemberLiteResp) ProtoMessage()    {}
func (*KickoutChannelMemberLiteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{62}
}
func (m *KickoutChannelMemberLiteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KickoutChannelMemberLiteResp.Unmarshal(m, b)
}
func (m *KickoutChannelMemberLiteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KickoutChannelMemberLiteResp.Marshal(b, m, deterministic)
}
func (dst *KickoutChannelMemberLiteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KickoutChannelMemberLiteResp.Merge(dst, src)
}
func (m *KickoutChannelMemberLiteResp) XXX_Size() int {
	return xxx_messageInfo_KickoutChannelMemberLiteResp.Size(m)
}
func (m *KickoutChannelMemberLiteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_KickoutChannelMemberLiteResp.DiscardUnknown(m)
}

var xxx_messageInfo_KickoutChannelMemberLiteResp proto.InternalMessageInfo

func (m *KickoutChannelMemberLiteResp) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *KickoutChannelMemberLiteResp) GetKickoutedUids() []uint32 {
	if m != nil {
		return m.KickoutedUids
	}
	return nil
}

// 根据displayId查找channel
type GetChannelByDisplayIdReq struct {
	DisplayId            *uint32  `protobuf:"varint,1,req,name=display_id,json=displayId" json:"display_id,omitempty"`
	AppId                *uint32  `protobuf:"varint,2,req,name=app_id,json=appId" json:"app_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelByDisplayIdReq) Reset()         { *m = GetChannelByDisplayIdReq{} }
func (m *GetChannelByDisplayIdReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelByDisplayIdReq) ProtoMessage()    {}
func (*GetChannelByDisplayIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{63}
}
func (m *GetChannelByDisplayIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelByDisplayIdReq.Unmarshal(m, b)
}
func (m *GetChannelByDisplayIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelByDisplayIdReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelByDisplayIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelByDisplayIdReq.Merge(dst, src)
}
func (m *GetChannelByDisplayIdReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelByDisplayIdReq.Size(m)
}
func (m *GetChannelByDisplayIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelByDisplayIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelByDisplayIdReq proto.InternalMessageInfo

func (m *GetChannelByDisplayIdReq) GetDisplayId() uint32 {
	if m != nil && m.DisplayId != nil {
		return *m.DisplayId
	}
	return 0
}

func (m *GetChannelByDisplayIdReq) GetAppId() uint32 {
	if m != nil && m.AppId != nil {
		return *m.AppId
	}
	return 0
}

type GetChannelByDisplayIdResp struct {
	ChannelInfo          *ChannelDetailInfo `protobuf:"bytes,1,req,name=channel_info,json=channelInfo" json:"channel_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetChannelByDisplayIdResp) Reset()         { *m = GetChannelByDisplayIdResp{} }
func (m *GetChannelByDisplayIdResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelByDisplayIdResp) ProtoMessage()    {}
func (*GetChannelByDisplayIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{64}
}
func (m *GetChannelByDisplayIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelByDisplayIdResp.Unmarshal(m, b)
}
func (m *GetChannelByDisplayIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelByDisplayIdResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelByDisplayIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelByDisplayIdResp.Merge(dst, src)
}
func (m *GetChannelByDisplayIdResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelByDisplayIdResp.Size(m)
}
func (m *GetChannelByDisplayIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelByDisplayIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelByDisplayIdResp proto.InternalMessageInfo

func (m *GetChannelByDisplayIdResp) GetChannelInfo() *ChannelDetailInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

// channel的管理权限
type UserChannelRoleInfo struct {
	Uid                  *uint32            `protobuf:"varint,1,req,name=uid" json:"uid,omitempty"`
	Role                 *uint32            `protobuf:"varint,2,req,name=role" json:"role,omitempty"`
	ChannelInfo          *ChannelDetailInfo `protobuf:"bytes,3,opt,name=channel_info,json=channelInfo" json:"channel_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UserChannelRoleInfo) Reset()         { *m = UserChannelRoleInfo{} }
func (m *UserChannelRoleInfo) String() string { return proto.CompactTextString(m) }
func (*UserChannelRoleInfo) ProtoMessage()    {}
func (*UserChannelRoleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{65}
}
func (m *UserChannelRoleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserChannelRoleInfo.Unmarshal(m, b)
}
func (m *UserChannelRoleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserChannelRoleInfo.Marshal(b, m, deterministic)
}
func (dst *UserChannelRoleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserChannelRoleInfo.Merge(dst, src)
}
func (m *UserChannelRoleInfo) XXX_Size() int {
	return xxx_messageInfo_UserChannelRoleInfo.Size(m)
}
func (m *UserChannelRoleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserChannelRoleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserChannelRoleInfo proto.InternalMessageInfo

func (m *UserChannelRoleInfo) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *UserChannelRoleInfo) GetRole() uint32 {
	if m != nil && m.Role != nil {
		return *m.Role
	}
	return 0
}

func (m *UserChannelRoleInfo) GetChannelInfo() *ChannelDetailInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

type GetUserChannelRoleListReq struct {
	Uid                  *uint32  `protobuf:"varint,1,req,name=uid" json:"uid,omitempty"`
	AdminRole            *uint32  `protobuf:"varint,2,req,name=admin_role,json=adminRole" json:"admin_role,omitempty"`
	Appid                *uint32  `protobuf:"varint,3,opt,name=appid" json:"appid,omitempty"`
	MarketId             *uint32  `protobuf:"varint,4,opt,name=market_id,json=marketId" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserChannelRoleListReq) Reset()         { *m = GetUserChannelRoleListReq{} }
func (m *GetUserChannelRoleListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserChannelRoleListReq) ProtoMessage()    {}
func (*GetUserChannelRoleListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{66}
}
func (m *GetUserChannelRoleListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserChannelRoleListReq.Unmarshal(m, b)
}
func (m *GetUserChannelRoleListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserChannelRoleListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserChannelRoleListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserChannelRoleListReq.Merge(dst, src)
}
func (m *GetUserChannelRoleListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserChannelRoleListReq.Size(m)
}
func (m *GetUserChannelRoleListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserChannelRoleListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserChannelRoleListReq proto.InternalMessageInfo

func (m *GetUserChannelRoleListReq) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *GetUserChannelRoleListReq) GetAdminRole() uint32 {
	if m != nil && m.AdminRole != nil {
		return *m.AdminRole
	}
	return 0
}

func (m *GetUserChannelRoleListReq) GetAppid() uint32 {
	if m != nil && m.Appid != nil {
		return *m.Appid
	}
	return 0
}

func (m *GetUserChannelRoleListReq) GetMarketId() uint32 {
	if m != nil && m.MarketId != nil {
		return *m.MarketId
	}
	return 0
}

type GetUserChannelRoleListResp struct {
	RoleList             []*UserChannelRoleInfo `protobuf:"bytes,1,rep,name=role_list,json=roleList" json:"role_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetUserChannelRoleListResp) Reset()         { *m = GetUserChannelRoleListResp{} }
func (m *GetUserChannelRoleListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserChannelRoleListResp) ProtoMessage()    {}
func (*GetUserChannelRoleListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{67}
}
func (m *GetUserChannelRoleListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserChannelRoleListResp.Unmarshal(m, b)
}
func (m *GetUserChannelRoleListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserChannelRoleListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserChannelRoleListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserChannelRoleListResp.Merge(dst, src)
}
func (m *GetUserChannelRoleListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserChannelRoleListResp.Size(m)
}
func (m *GetUserChannelRoleListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserChannelRoleListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserChannelRoleListResp proto.InternalMessageInfo

func (m *GetUserChannelRoleListResp) GetRoleList() []*UserChannelRoleInfo {
	if m != nil {
		return m.RoleList
	}
	return nil
}

// 修改mic模式 // 接口废弃 不再支持
type SetChannelMicModelReq struct {
	Uid                  *uint32            `protobuf:"varint,1,req,name=uid" json:"uid,omitempty"`
	ChannelId            *uint32            `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	MicModel             *uint32            `protobuf:"varint,3,req,name=mic_model,json=micModel" json:"mic_model,omitempty"`
	IsDisableAllMic      *bool              `protobuf:"varint,4,opt,name=is_disable_all_mic,json=isDisableAllMic" json:"is_disable_all_mic,omitempty"`
	IsNeedHoldMic        *bool              `protobuf:"varint,5,opt,name=is_need_hold_mic,json=isNeedHoldMic" json:"is_need_hold_mic,omitempty"`
	OpUidRole            *ChannelMemberRole `protobuf:"bytes,6,opt,name=op_uid_role,json=opUidRole" json:"op_uid_role,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SetChannelMicModelReq) Reset()         { *m = SetChannelMicModelReq{} }
func (m *SetChannelMicModelReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelMicModelReq) ProtoMessage()    {}
func (*SetChannelMicModelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{68}
}
func (m *SetChannelMicModelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMicModelReq.Unmarshal(m, b)
}
func (m *SetChannelMicModelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMicModelReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelMicModelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMicModelReq.Merge(dst, src)
}
func (m *SetChannelMicModelReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelMicModelReq.Size(m)
}
func (m *SetChannelMicModelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMicModelReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMicModelReq proto.InternalMessageInfo

func (m *SetChannelMicModelReq) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *SetChannelMicModelReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *SetChannelMicModelReq) GetMicModel() uint32 {
	if m != nil && m.MicModel != nil {
		return *m.MicModel
	}
	return 0
}

func (m *SetChannelMicModelReq) GetIsDisableAllMic() bool {
	if m != nil && m.IsDisableAllMic != nil {
		return *m.IsDisableAllMic
	}
	return false
}

func (m *SetChannelMicModelReq) GetIsNeedHoldMic() bool {
	if m != nil && m.IsNeedHoldMic != nil {
		return *m.IsNeedHoldMic
	}
	return false
}

func (m *SetChannelMicModelReq) GetOpUidRole() *ChannelMemberRole {
	if m != nil {
		return m.OpUidRole
	}
	return nil
}

// 接口废弃 不再支持
type SetChannelMicModelResp struct {
	ChannelId       *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	MicMode         *uint32  `protobuf:"varint,2,req,name=mic_mode,json=micMode" json:"mic_mode,omitempty"`
	DisabledMicSize *uint32  `protobuf:"varint,3,opt,name=disabled_mic_size,json=disabledMicSize" json:"disabled_mic_size,omitempty"`
	KickUidList     []uint32 `protobuf:"varint,4,rep,name=kick_uid_list,json=kickUidList" json:"kick_uid_list,omitempty"`
	EnabledMicSize  *uint32  `protobuf:"varint,5,opt,name=enabled_mic_size,json=enabledMicSize" json:"enabled_mic_size,omitempty"`
	HoldMicUid      *uint32  `protobuf:"varint,6,opt,name=hold_mic_uid,json=holdMicUid" json:"hold_mic_uid,omitempty"`
	// 因为本次模式切换 而变化的麦位信息
	DisableMicList []*MicrSpaceInfo `protobuf:"bytes,7,rep,name=disable_mic_list,json=disableMicList" json:"disable_mic_list,omitempty"`
	EnableMicList  []*MicrSpaceInfo `protobuf:"bytes,8,rep,name=enable_mic_list,json=enableMicList" json:"enable_mic_list,omitempty"`
	KickoutMicList []*MicrSpaceInfo `protobuf:"bytes,9,rep,name=kickout_mic_list,json=kickoutMicList" json:"kickout_mic_list,omitempty"`
	HoldMicInfo    *MicrSpaceInfo   `protobuf:"bytes,10,opt,name=hold_mic_info,json=holdMicInfo" json:"hold_mic_info,omitempty"`
	// 模式设置之后 当前的麦位信息
	AllMicList           []*MicrSpaceInfo `protobuf:"bytes,11,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	ServerTimeMs         *uint64          `protobuf:"varint,12,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms,omitempty"`
	FromMicMode          *uint32          `protobuf:"varint,13,opt,name=from_mic_mode,json=fromMicMode" json:"from_mic_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SetChannelMicModelResp) Reset()         { *m = SetChannelMicModelResp{} }
func (m *SetChannelMicModelResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelMicModelResp) ProtoMessage()    {}
func (*SetChannelMicModelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{69}
}
func (m *SetChannelMicModelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMicModelResp.Unmarshal(m, b)
}
func (m *SetChannelMicModelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMicModelResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelMicModelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMicModelResp.Merge(dst, src)
}
func (m *SetChannelMicModelResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelMicModelResp.Size(m)
}
func (m *SetChannelMicModelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMicModelResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMicModelResp proto.InternalMessageInfo

func (m *SetChannelMicModelResp) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *SetChannelMicModelResp) GetMicMode() uint32 {
	if m != nil && m.MicMode != nil {
		return *m.MicMode
	}
	return 0
}

func (m *SetChannelMicModelResp) GetDisabledMicSize() uint32 {
	if m != nil && m.DisabledMicSize != nil {
		return *m.DisabledMicSize
	}
	return 0
}

func (m *SetChannelMicModelResp) GetKickUidList() []uint32 {
	if m != nil {
		return m.KickUidList
	}
	return nil
}

func (m *SetChannelMicModelResp) GetEnabledMicSize() uint32 {
	if m != nil && m.EnabledMicSize != nil {
		return *m.EnabledMicSize
	}
	return 0
}

func (m *SetChannelMicModelResp) GetHoldMicUid() uint32 {
	if m != nil && m.HoldMicUid != nil {
		return *m.HoldMicUid
	}
	return 0
}

func (m *SetChannelMicModelResp) GetDisableMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.DisableMicList
	}
	return nil
}

func (m *SetChannelMicModelResp) GetEnableMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.EnableMicList
	}
	return nil
}

func (m *SetChannelMicModelResp) GetKickoutMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.KickoutMicList
	}
	return nil
}

func (m *SetChannelMicModelResp) GetHoldMicInfo() *MicrSpaceInfo {
	if m != nil {
		return m.HoldMicInfo
	}
	return nil
}

func (m *SetChannelMicModelResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *SetChannelMicModelResp) GetServerTimeMs() uint64 {
	if m != nil && m.ServerTimeMs != nil {
		return *m.ServerTimeMs
	}
	return 0
}

func (m *SetChannelMicModelResp) GetFromMicMode() uint32 {
	if m != nil && m.FromMicMode != nil {
		return *m.FromMicMode
	}
	return 0
}

// 通过displayID创建频道
type CreateChannelByDisplayIDReq struct {
	DisplayId            *uint32  `protobuf:"varint,1,req,name=display_id,json=displayId" json:"display_id,omitempty"`
	Name                 *string  `protobuf:"bytes,2,req,name=name" json:"name,omitempty"`
	ChannelBindType      *uint32  `protobuf:"varint,3,req,name=channel_bind_type,json=channelBindType" json:"channel_bind_type,omitempty"`
	BindId               *uint32  `protobuf:"varint,4,opt,name=bind_id,json=bindId" json:"bind_id,omitempty"`
	Appid                *uint32  `protobuf:"varint,5,opt,name=appid" json:"appid,omitempty"`
	Passwd               *string  `protobuf:"bytes,6,opt,name=passwd" json:"passwd,omitempty"`
	MicMode              *uint32  `protobuf:"varint,7,opt,name=mic_mode,json=micMode" json:"mic_mode,omitempty"`
	CreatorUid           *uint32  `protobuf:"varint,8,opt,name=creator_uid,json=creatorUid" json:"creator_uid,omitempty"`
	MarketId             *uint32  `protobuf:"varint,9,opt,name=market_id,json=marketId" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateChannelByDisplayIDReq) Reset()         { *m = CreateChannelByDisplayIDReq{} }
func (m *CreateChannelByDisplayIDReq) String() string { return proto.CompactTextString(m) }
func (*CreateChannelByDisplayIDReq) ProtoMessage()    {}
func (*CreateChannelByDisplayIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{70}
}
func (m *CreateChannelByDisplayIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateChannelByDisplayIDReq.Unmarshal(m, b)
}
func (m *CreateChannelByDisplayIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateChannelByDisplayIDReq.Marshal(b, m, deterministic)
}
func (dst *CreateChannelByDisplayIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateChannelByDisplayIDReq.Merge(dst, src)
}
func (m *CreateChannelByDisplayIDReq) XXX_Size() int {
	return xxx_messageInfo_CreateChannelByDisplayIDReq.Size(m)
}
func (m *CreateChannelByDisplayIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateChannelByDisplayIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateChannelByDisplayIDReq proto.InternalMessageInfo

func (m *CreateChannelByDisplayIDReq) GetDisplayId() uint32 {
	if m != nil && m.DisplayId != nil {
		return *m.DisplayId
	}
	return 0
}

func (m *CreateChannelByDisplayIDReq) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *CreateChannelByDisplayIDReq) GetChannelBindType() uint32 {
	if m != nil && m.ChannelBindType != nil {
		return *m.ChannelBindType
	}
	return 0
}

func (m *CreateChannelByDisplayIDReq) GetBindId() uint32 {
	if m != nil && m.BindId != nil {
		return *m.BindId
	}
	return 0
}

func (m *CreateChannelByDisplayIDReq) GetAppid() uint32 {
	if m != nil && m.Appid != nil {
		return *m.Appid
	}
	return 0
}

func (m *CreateChannelByDisplayIDReq) GetPasswd() string {
	if m != nil && m.Passwd != nil {
		return *m.Passwd
	}
	return ""
}

func (m *CreateChannelByDisplayIDReq) GetMicMode() uint32 {
	if m != nil && m.MicMode != nil {
		return *m.MicMode
	}
	return 0
}

func (m *CreateChannelByDisplayIDReq) GetCreatorUid() uint32 {
	if m != nil && m.CreatorUid != nil {
		return *m.CreatorUid
	}
	return 0
}

func (m *CreateChannelByDisplayIDReq) GetMarketId() uint32 {
	if m != nil && m.MarketId != nil {
		return *m.MarketId
	}
	return 0
}

type CreateChannelByDisplayIDResp struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	DisplayId            *uint32  `protobuf:"varint,2,req,name=display_id,json=displayId" json:"display_id,omitempty"`
	SdkSessionId         *uint32  `protobuf:"varint,3,req,name=sdk_session_id,json=sdkSessionId" json:"sdk_session_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateChannelByDisplayIDResp) Reset()         { *m = CreateChannelByDisplayIDResp{} }
func (m *CreateChannelByDisplayIDResp) String() string { return proto.CompactTextString(m) }
func (*CreateChannelByDisplayIDResp) ProtoMessage()    {}
func (*CreateChannelByDisplayIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{71}
}
func (m *CreateChannelByDisplayIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateChannelByDisplayIDResp.Unmarshal(m, b)
}
func (m *CreateChannelByDisplayIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateChannelByDisplayIDResp.Marshal(b, m, deterministic)
}
func (dst *CreateChannelByDisplayIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateChannelByDisplayIDResp.Merge(dst, src)
}
func (m *CreateChannelByDisplayIDResp) XXX_Size() int {
	return xxx_messageInfo_CreateChannelByDisplayIDResp.Size(m)
}
func (m *CreateChannelByDisplayIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateChannelByDisplayIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateChannelByDisplayIDResp proto.InternalMessageInfo

func (m *CreateChannelByDisplayIDResp) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *CreateChannelByDisplayIDResp) GetDisplayId() uint32 {
	if m != nil && m.DisplayId != nil {
		return *m.DisplayId
	}
	return 0
}

func (m *CreateChannelByDisplayIDResp) GetSdkSessionId() uint32 {
	if m != nil && m.SdkSessionId != nil {
		return *m.SdkSessionId
	}
	return 0
}

// message NotifyUserHeartbeatExpireResp
// {
// }
//
// 分配一个临时房间ID
type AllocTempChannelReq struct {
	ChannelType          *uint32  `protobuf:"varint,1,req,name=channel_type,json=channelType" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AllocTempChannelReq) Reset()         { *m = AllocTempChannelReq{} }
func (m *AllocTempChannelReq) String() string { return proto.CompactTextString(m) }
func (*AllocTempChannelReq) ProtoMessage()    {}
func (*AllocTempChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{72}
}
func (m *AllocTempChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AllocTempChannelReq.Unmarshal(m, b)
}
func (m *AllocTempChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AllocTempChannelReq.Marshal(b, m, deterministic)
}
func (dst *AllocTempChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllocTempChannelReq.Merge(dst, src)
}
func (m *AllocTempChannelReq) XXX_Size() int {
	return xxx_messageInfo_AllocTempChannelReq.Size(m)
}
func (m *AllocTempChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AllocTempChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_AllocTempChannelReq proto.InternalMessageInfo

func (m *AllocTempChannelReq) GetChannelType() uint32 {
	if m != nil && m.ChannelType != nil {
		return *m.ChannelType
	}
	return 0
}

type AllocTempChannelResp struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	ChannelName          *string  `protobuf:"bytes,2,req,name=channel_name,json=channelName" json:"channel_name,omitempty"`
	SdkSessionId         *uint32  `protobuf:"varint,3,req,name=sdk_session_id,json=sdkSessionId" json:"sdk_session_id,omitempty"`
	AppId                *uint32  `protobuf:"varint,4,req,name=app_id,json=appId" json:"app_id,omitempty"`
	ChannelType          *uint32  `protobuf:"varint,5,req,name=channel_type,json=channelType" json:"channel_type,omitempty"`
	DisplayId            *uint32  `protobuf:"varint,6,opt,name=display_id,json=displayId" json:"display_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AllocTempChannelResp) Reset()         { *m = AllocTempChannelResp{} }
func (m *AllocTempChannelResp) String() string { return proto.CompactTextString(m) }
func (*AllocTempChannelResp) ProtoMessage()    {}
func (*AllocTempChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{73}
}
func (m *AllocTempChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AllocTempChannelResp.Unmarshal(m, b)
}
func (m *AllocTempChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AllocTempChannelResp.Marshal(b, m, deterministic)
}
func (dst *AllocTempChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllocTempChannelResp.Merge(dst, src)
}
func (m *AllocTempChannelResp) XXX_Size() int {
	return xxx_messageInfo_AllocTempChannelResp.Size(m)
}
func (m *AllocTempChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AllocTempChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_AllocTempChannelResp proto.InternalMessageInfo

func (m *AllocTempChannelResp) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *AllocTempChannelResp) GetChannelName() string {
	if m != nil && m.ChannelName != nil {
		return *m.ChannelName
	}
	return ""
}

func (m *AllocTempChannelResp) GetSdkSessionId() uint32 {
	if m != nil && m.SdkSessionId != nil {
		return *m.SdkSessionId
	}
	return 0
}

func (m *AllocTempChannelResp) GetAppId() uint32 {
	if m != nil && m.AppId != nil {
		return *m.AppId
	}
	return 0
}

func (m *AllocTempChannelResp) GetChannelType() uint32 {
	if m != nil && m.ChannelType != nil {
		return *m.ChannelType
	}
	return 0
}

func (m *AllocTempChannelResp) GetDisplayId() uint32 {
	if m != nil && m.DisplayId != nil {
		return *m.DisplayId
	}
	return 0
}

type ReleaseTempChannelReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	BindType             *uint32  `protobuf:"varint,2,req,name=bind_type,json=bindType" json:"bind_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReleaseTempChannelReq) Reset()         { *m = ReleaseTempChannelReq{} }
func (m *ReleaseTempChannelReq) String() string { return proto.CompactTextString(m) }
func (*ReleaseTempChannelReq) ProtoMessage()    {}
func (*ReleaseTempChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{74}
}
func (m *ReleaseTempChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReleaseTempChannelReq.Unmarshal(m, b)
}
func (m *ReleaseTempChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReleaseTempChannelReq.Marshal(b, m, deterministic)
}
func (dst *ReleaseTempChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReleaseTempChannelReq.Merge(dst, src)
}
func (m *ReleaseTempChannelReq) XXX_Size() int {
	return xxx_messageInfo_ReleaseTempChannelReq.Size(m)
}
func (m *ReleaseTempChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReleaseTempChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReleaseTempChannelReq proto.InternalMessageInfo

func (m *ReleaseTempChannelReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *ReleaseTempChannelReq) GetBindType() uint32 {
	if m != nil && m.BindType != nil {
		return *m.BindType
	}
	return 0
}

type ReleaseTempChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReleaseTempChannelResp) Reset()         { *m = ReleaseTempChannelResp{} }
func (m *ReleaseTempChannelResp) String() string { return proto.CompactTextString(m) }
func (*ReleaseTempChannelResp) ProtoMessage()    {}
func (*ReleaseTempChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{75}
}
func (m *ReleaseTempChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReleaseTempChannelResp.Unmarshal(m, b)
}
func (m *ReleaseTempChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReleaseTempChannelResp.Marshal(b, m, deterministic)
}
func (dst *ReleaseTempChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReleaseTempChannelResp.Merge(dst, src)
}
func (m *ReleaseTempChannelResp) XXX_Size() int {
	return xxx_messageInfo_ReleaseTempChannelResp.Size(m)
}
func (m *ReleaseTempChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReleaseTempChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReleaseTempChannelResp proto.InternalMessageInfo

// 批量分配多个临时房间ID
type BatchAllocTempChannelReq struct {
	ChannelType          *uint32  `protobuf:"varint,1,req,name=channel_type,json=channelType" json:"channel_type,omitempty"`
	BatchCount           *uint32  `protobuf:"varint,2,req,name=batch_count,json=batchCount" json:"batch_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAllocTempChannelReq) Reset()         { *m = BatchAllocTempChannelReq{} }
func (m *BatchAllocTempChannelReq) String() string { return proto.CompactTextString(m) }
func (*BatchAllocTempChannelReq) ProtoMessage()    {}
func (*BatchAllocTempChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{76}
}
func (m *BatchAllocTempChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAllocTempChannelReq.Unmarshal(m, b)
}
func (m *BatchAllocTempChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAllocTempChannelReq.Marshal(b, m, deterministic)
}
func (dst *BatchAllocTempChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAllocTempChannelReq.Merge(dst, src)
}
func (m *BatchAllocTempChannelReq) XXX_Size() int {
	return xxx_messageInfo_BatchAllocTempChannelReq.Size(m)
}
func (m *BatchAllocTempChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAllocTempChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAllocTempChannelReq proto.InternalMessageInfo

func (m *BatchAllocTempChannelReq) GetChannelType() uint32 {
	if m != nil && m.ChannelType != nil {
		return *m.ChannelType
	}
	return 0
}

func (m *BatchAllocTempChannelReq) GetBatchCount() uint32 {
	if m != nil && m.BatchCount != nil {
		return *m.BatchCount
	}
	return 0
}

type BatchAllocTempChannelResp struct {
	TempChannels         []*AllocTempChannelResp `protobuf:"bytes,1,rep,name=temp_channels,json=tempChannels" json:"temp_channels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchAllocTempChannelResp) Reset()         { *m = BatchAllocTempChannelResp{} }
func (m *BatchAllocTempChannelResp) String() string { return proto.CompactTextString(m) }
func (*BatchAllocTempChannelResp) ProtoMessage()    {}
func (*BatchAllocTempChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{77}
}
func (m *BatchAllocTempChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAllocTempChannelResp.Unmarshal(m, b)
}
func (m *BatchAllocTempChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAllocTempChannelResp.Marshal(b, m, deterministic)
}
func (dst *BatchAllocTempChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAllocTempChannelResp.Merge(dst, src)
}
func (m *BatchAllocTempChannelResp) XXX_Size() int {
	return xxx_messageInfo_BatchAllocTempChannelResp.Size(m)
}
func (m *BatchAllocTempChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAllocTempChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAllocTempChannelResp proto.InternalMessageInfo

func (m *BatchAllocTempChannelResp) GetTempChannels() []*AllocTempChannelResp {
	if m != nil {
		return m.TempChannels
	}
	return nil
}

// 批量释放多个临时房间
type BatchReleaseTempChannelReq struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,name=channel_ids,json=channelIds" json:"channel_ids,omitempty"`
	BindType             *uint32  `protobuf:"varint,2,req,name=bind_type,json=bindType" json:"bind_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchReleaseTempChannelReq) Reset()         { *m = BatchReleaseTempChannelReq{} }
func (m *BatchReleaseTempChannelReq) String() string { return proto.CompactTextString(m) }
func (*BatchReleaseTempChannelReq) ProtoMessage()    {}
func (*BatchReleaseTempChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{78}
}
func (m *BatchReleaseTempChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchReleaseTempChannelReq.Unmarshal(m, b)
}
func (m *BatchReleaseTempChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchReleaseTempChannelReq.Marshal(b, m, deterministic)
}
func (dst *BatchReleaseTempChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchReleaseTempChannelReq.Merge(dst, src)
}
func (m *BatchReleaseTempChannelReq) XXX_Size() int {
	return xxx_messageInfo_BatchReleaseTempChannelReq.Size(m)
}
func (m *BatchReleaseTempChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchReleaseTempChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchReleaseTempChannelReq proto.InternalMessageInfo

func (m *BatchReleaseTempChannelReq) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

func (m *BatchReleaseTempChannelReq) GetBindType() uint32 {
	if m != nil && m.BindType != nil {
		return *m.BindType
	}
	return 0
}

type BatchReleaseTempChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchReleaseTempChannelResp) Reset()         { *m = BatchReleaseTempChannelResp{} }
func (m *BatchReleaseTempChannelResp) String() string { return proto.CompactTextString(m) }
func (*BatchReleaseTempChannelResp) ProtoMessage()    {}
func (*BatchReleaseTempChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{79}
}
func (m *BatchReleaseTempChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchReleaseTempChannelResp.Unmarshal(m, b)
}
func (m *BatchReleaseTempChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchReleaseTempChannelResp.Marshal(b, m, deterministic)
}
func (dst *BatchReleaseTempChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchReleaseTempChannelResp.Merge(dst, src)
}
func (m *BatchReleaseTempChannelResp) XXX_Size() int {
	return xxx_messageInfo_BatchReleaseTempChannelResp.Size(m)
}
func (m *BatchReleaseTempChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchReleaseTempChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchReleaseTempChannelResp proto.InternalMessageInfo

// 查询临时房池余量
type QueryTempChannelsNumReq struct {
	ChannelType          *uint32  `protobuf:"varint,1,req,name=channel_type,json=channelType" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryTempChannelsNumReq) Reset()         { *m = QueryTempChannelsNumReq{} }
func (m *QueryTempChannelsNumReq) String() string { return proto.CompactTextString(m) }
func (*QueryTempChannelsNumReq) ProtoMessage()    {}
func (*QueryTempChannelsNumReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{80}
}
func (m *QueryTempChannelsNumReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryTempChannelsNumReq.Unmarshal(m, b)
}
func (m *QueryTempChannelsNumReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryTempChannelsNumReq.Marshal(b, m, deterministic)
}
func (dst *QueryTempChannelsNumReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryTempChannelsNumReq.Merge(dst, src)
}
func (m *QueryTempChannelsNumReq) XXX_Size() int {
	return xxx_messageInfo_QueryTempChannelsNumReq.Size(m)
}
func (m *QueryTempChannelsNumReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryTempChannelsNumReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryTempChannelsNumReq proto.InternalMessageInfo

func (m *QueryTempChannelsNumReq) GetChannelType() uint32 {
	if m != nil && m.ChannelType != nil {
		return *m.ChannelType
	}
	return 0
}

type QueryTempChannelsNumResp struct {
	TotalNum             *uint32  `protobuf:"varint,1,req,name=total_num,json=totalNum" json:"total_num,omitempty"`
	IdleNum              *uint32  `protobuf:"varint,2,req,name=idle_num,json=idleNum" json:"idle_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryTempChannelsNumResp) Reset()         { *m = QueryTempChannelsNumResp{} }
func (m *QueryTempChannelsNumResp) String() string { return proto.CompactTextString(m) }
func (*QueryTempChannelsNumResp) ProtoMessage()    {}
func (*QueryTempChannelsNumResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{81}
}
func (m *QueryTempChannelsNumResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryTempChannelsNumResp.Unmarshal(m, b)
}
func (m *QueryTempChannelsNumResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryTempChannelsNumResp.Marshal(b, m, deterministic)
}
func (dst *QueryTempChannelsNumResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryTempChannelsNumResp.Merge(dst, src)
}
func (m *QueryTempChannelsNumResp) XXX_Size() int {
	return xxx_messageInfo_QueryTempChannelsNumResp.Size(m)
}
func (m *QueryTempChannelsNumResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryTempChannelsNumResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryTempChannelsNumResp proto.InternalMessageInfo

func (m *QueryTempChannelsNumResp) GetTotalNum() uint32 {
	if m != nil && m.TotalNum != nil {
		return *m.TotalNum
	}
	return 0
}

func (m *QueryTempChannelsNumResp) GetIdleNum() uint32 {
	if m != nil && m.IdleNum != nil {
		return *m.IdleNum
	}
	return 0
}

// 检查临时房的分配状态
type CheckTmpChannelIsAllocedReq struct {
	Uid                  *uint32  `protobuf:"varint,1,req,name=uid" json:"uid,omitempty"`
	ChannelId            *uint32  `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	ChannelType          *uint32  `protobuf:"varint,3,req,name=channel_type,json=channelType" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckTmpChannelIsAllocedReq) Reset()         { *m = CheckTmpChannelIsAllocedReq{} }
func (m *CheckTmpChannelIsAllocedReq) String() string { return proto.CompactTextString(m) }
func (*CheckTmpChannelIsAllocedReq) ProtoMessage()    {}
func (*CheckTmpChannelIsAllocedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{82}
}
func (m *CheckTmpChannelIsAllocedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTmpChannelIsAllocedReq.Unmarshal(m, b)
}
func (m *CheckTmpChannelIsAllocedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTmpChannelIsAllocedReq.Marshal(b, m, deterministic)
}
func (dst *CheckTmpChannelIsAllocedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTmpChannelIsAllocedReq.Merge(dst, src)
}
func (m *CheckTmpChannelIsAllocedReq) XXX_Size() int {
	return xxx_messageInfo_CheckTmpChannelIsAllocedReq.Size(m)
}
func (m *CheckTmpChannelIsAllocedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTmpChannelIsAllocedReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTmpChannelIsAllocedReq proto.InternalMessageInfo

func (m *CheckTmpChannelIsAllocedReq) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *CheckTmpChannelIsAllocedReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *CheckTmpChannelIsAllocedReq) GetChannelType() uint32 {
	if m != nil && m.ChannelType != nil {
		return *m.ChannelType
	}
	return 0
}

type CheckTmpChannelIsAllocedResp struct {
	IsAlloced            *bool    `protobuf:"varint,1,req,name=is_alloced,json=isAlloced" json:"is_alloced,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckTmpChannelIsAllocedResp) Reset()         { *m = CheckTmpChannelIsAllocedResp{} }
func (m *CheckTmpChannelIsAllocedResp) String() string { return proto.CompactTextString(m) }
func (*CheckTmpChannelIsAllocedResp) ProtoMessage()    {}
func (*CheckTmpChannelIsAllocedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{83}
}
func (m *CheckTmpChannelIsAllocedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTmpChannelIsAllocedResp.Unmarshal(m, b)
}
func (m *CheckTmpChannelIsAllocedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTmpChannelIsAllocedResp.Marshal(b, m, deterministic)
}
func (dst *CheckTmpChannelIsAllocedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTmpChannelIsAllocedResp.Merge(dst, src)
}
func (m *CheckTmpChannelIsAllocedResp) XXX_Size() int {
	return xxx_messageInfo_CheckTmpChannelIsAllocedResp.Size(m)
}
func (m *CheckTmpChannelIsAllocedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTmpChannelIsAllocedResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTmpChannelIsAllocedResp proto.InternalMessageInfo

func (m *CheckTmpChannelIsAllocedResp) GetIsAlloced() bool {
	if m != nil && m.IsAlloced != nil {
		return *m.IsAlloced
	}
	return false
}

type BatchCheckIsTempAllocChannelReq struct {
	ChannelidList        []uint32 `protobuf:"varint,1,rep,name=channelid_list,json=channelidList" json:"channelid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchCheckIsTempAllocChannelReq) Reset()         { *m = BatchCheckIsTempAllocChannelReq{} }
func (m *BatchCheckIsTempAllocChannelReq) String() string { return proto.CompactTextString(m) }
func (*BatchCheckIsTempAllocChannelReq) ProtoMessage()    {}
func (*BatchCheckIsTempAllocChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{84}
}
func (m *BatchCheckIsTempAllocChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckIsTempAllocChannelReq.Unmarshal(m, b)
}
func (m *BatchCheckIsTempAllocChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckIsTempAllocChannelReq.Marshal(b, m, deterministic)
}
func (dst *BatchCheckIsTempAllocChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckIsTempAllocChannelReq.Merge(dst, src)
}
func (m *BatchCheckIsTempAllocChannelReq) XXX_Size() int {
	return xxx_messageInfo_BatchCheckIsTempAllocChannelReq.Size(m)
}
func (m *BatchCheckIsTempAllocChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckIsTempAllocChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckIsTempAllocChannelReq proto.InternalMessageInfo

func (m *BatchCheckIsTempAllocChannelReq) GetChannelidList() []uint32 {
	if m != nil {
		return m.ChannelidList
	}
	return nil
}

type BatchCheckIsTempAllocChannelResp struct {
	TempallocIdList      []uint32 `protobuf:"varint,1,rep,name=tempalloc_id_list,json=tempallocIdList" json:"tempalloc_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchCheckIsTempAllocChannelResp) Reset()         { *m = BatchCheckIsTempAllocChannelResp{} }
func (m *BatchCheckIsTempAllocChannelResp) String() string { return proto.CompactTextString(m) }
func (*BatchCheckIsTempAllocChannelResp) ProtoMessage()    {}
func (*BatchCheckIsTempAllocChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{85}
}
func (m *BatchCheckIsTempAllocChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckIsTempAllocChannelResp.Unmarshal(m, b)
}
func (m *BatchCheckIsTempAllocChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckIsTempAllocChannelResp.Marshal(b, m, deterministic)
}
func (dst *BatchCheckIsTempAllocChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckIsTempAllocChannelResp.Merge(dst, src)
}
func (m *BatchCheckIsTempAllocChannelResp) XXX_Size() int {
	return xxx_messageInfo_BatchCheckIsTempAllocChannelResp.Size(m)
}
func (m *BatchCheckIsTempAllocChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckIsTempAllocChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckIsTempAllocChannelResp proto.InternalMessageInfo

func (m *BatchCheckIsTempAllocChannelResp) GetTempallocIdList() []uint32 {
	if m != nil {
		return m.TempallocIdList
	}
	return nil
}

// 配置歌曲
type ConfigMusicInfo struct {
	MusicName            *string  `protobuf:"bytes,1,req,name=music_name,json=musicName" json:"music_name,omitempty"`
	DownloadUrl          *string  `protobuf:"bytes,2,req,name=download_url,json=downloadUrl" json:"download_url,omitempty"`
	Artist               *string  `protobuf:"bytes,3,opt,name=artist" json:"artist,omitempty"`
	ImgUrl               *string  `protobuf:"bytes,4,opt,name=img_url,json=imgUrl" json:"img_url,omitempty"`
	MusicId              *uint32  `protobuf:"varint,5,opt,name=music_id,json=musicId" json:"music_id,omitempty"`
	AlbumName            *string  `protobuf:"bytes,6,opt,name=album_name,json=albumName" json:"album_name,omitempty"`
	MusicType            *uint32  `protobuf:"varint,7,opt,name=music_type,json=musicType" json:"music_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfigMusicInfo) Reset()         { *m = ConfigMusicInfo{} }
func (m *ConfigMusicInfo) String() string { return proto.CompactTextString(m) }
func (*ConfigMusicInfo) ProtoMessage()    {}
func (*ConfigMusicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{86}
}
func (m *ConfigMusicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfigMusicInfo.Unmarshal(m, b)
}
func (m *ConfigMusicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfigMusicInfo.Marshal(b, m, deterministic)
}
func (dst *ConfigMusicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfigMusicInfo.Merge(dst, src)
}
func (m *ConfigMusicInfo) XXX_Size() int {
	return xxx_messageInfo_ConfigMusicInfo.Size(m)
}
func (m *ConfigMusicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfigMusicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ConfigMusicInfo proto.InternalMessageInfo

func (m *ConfigMusicInfo) GetMusicName() string {
	if m != nil && m.MusicName != nil {
		return *m.MusicName
	}
	return ""
}

func (m *ConfigMusicInfo) GetDownloadUrl() string {
	if m != nil && m.DownloadUrl != nil {
		return *m.DownloadUrl
	}
	return ""
}

func (m *ConfigMusicInfo) GetArtist() string {
	if m != nil && m.Artist != nil {
		return *m.Artist
	}
	return ""
}

func (m *ConfigMusicInfo) GetImgUrl() string {
	if m != nil && m.ImgUrl != nil {
		return *m.ImgUrl
	}
	return ""
}

func (m *ConfigMusicInfo) GetMusicId() uint32 {
	if m != nil && m.MusicId != nil {
		return *m.MusicId
	}
	return 0
}

func (m *ConfigMusicInfo) GetAlbumName() string {
	if m != nil && m.AlbumName != nil {
		return *m.AlbumName
	}
	return ""
}

func (m *ConfigMusicInfo) GetMusicType() uint32 {
	if m != nil && m.MusicType != nil {
		return *m.MusicType
	}
	return 0
}

type AddConfigMusicReq struct {
	ConfigMusic          *ConfigMusicInfo `protobuf:"bytes,1,req,name=config_music,json=configMusic" json:"config_music,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *AddConfigMusicReq) Reset()         { *m = AddConfigMusicReq{} }
func (m *AddConfigMusicReq) String() string { return proto.CompactTextString(m) }
func (*AddConfigMusicReq) ProtoMessage()    {}
func (*AddConfigMusicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{87}
}
func (m *AddConfigMusicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddConfigMusicReq.Unmarshal(m, b)
}
func (m *AddConfigMusicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddConfigMusicReq.Marshal(b, m, deterministic)
}
func (dst *AddConfigMusicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddConfigMusicReq.Merge(dst, src)
}
func (m *AddConfigMusicReq) XXX_Size() int {
	return xxx_messageInfo_AddConfigMusicReq.Size(m)
}
func (m *AddConfigMusicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddConfigMusicReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddConfigMusicReq proto.InternalMessageInfo

func (m *AddConfigMusicReq) GetConfigMusic() *ConfigMusicInfo {
	if m != nil {
		return m.ConfigMusic
	}
	return nil
}

type AddConfigMusicResp struct {
	MusicId              *uint32  `protobuf:"varint,1,req,name=music_id,json=musicId" json:"music_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddConfigMusicResp) Reset()         { *m = AddConfigMusicResp{} }
func (m *AddConfigMusicResp) String() string { return proto.CompactTextString(m) }
func (*AddConfigMusicResp) ProtoMessage()    {}
func (*AddConfigMusicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{88}
}
func (m *AddConfigMusicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddConfigMusicResp.Unmarshal(m, b)
}
func (m *AddConfigMusicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddConfigMusicResp.Marshal(b, m, deterministic)
}
func (dst *AddConfigMusicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddConfigMusicResp.Merge(dst, src)
}
func (m *AddConfigMusicResp) XXX_Size() int {
	return xxx_messageInfo_AddConfigMusicResp.Size(m)
}
func (m *AddConfigMusicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddConfigMusicResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddConfigMusicResp proto.InternalMessageInfo

func (m *AddConfigMusicResp) GetMusicId() uint32 {
	if m != nil && m.MusicId != nil {
		return *m.MusicId
	}
	return 0
}

type DelConfigMusicReq struct {
	MusicidList          []uint32 `protobuf:"varint,1,rep,name=musicid_list,json=musicidList" json:"musicid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelConfigMusicReq) Reset()         { *m = DelConfigMusicReq{} }
func (m *DelConfigMusicReq) String() string { return proto.CompactTextString(m) }
func (*DelConfigMusicReq) ProtoMessage()    {}
func (*DelConfigMusicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{89}
}
func (m *DelConfigMusicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelConfigMusicReq.Unmarshal(m, b)
}
func (m *DelConfigMusicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelConfigMusicReq.Marshal(b, m, deterministic)
}
func (dst *DelConfigMusicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelConfigMusicReq.Merge(dst, src)
}
func (m *DelConfigMusicReq) XXX_Size() int {
	return xxx_messageInfo_DelConfigMusicReq.Size(m)
}
func (m *DelConfigMusicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelConfigMusicReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelConfigMusicReq proto.InternalMessageInfo

func (m *DelConfigMusicReq) GetMusicidList() []uint32 {
	if m != nil {
		return m.MusicidList
	}
	return nil
}

type DelConfigMusicResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelConfigMusicResp) Reset()         { *m = DelConfigMusicResp{} }
func (m *DelConfigMusicResp) String() string { return proto.CompactTextString(m) }
func (*DelConfigMusicResp) ProtoMessage()    {}
func (*DelConfigMusicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{90}
}
func (m *DelConfigMusicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelConfigMusicResp.Unmarshal(m, b)
}
func (m *DelConfigMusicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelConfigMusicResp.Marshal(b, m, deterministic)
}
func (dst *DelConfigMusicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelConfigMusicResp.Merge(dst, src)
}
func (m *DelConfigMusicResp) XXX_Size() int {
	return xxx_messageInfo_DelConfigMusicResp.Size(m)
}
func (m *DelConfigMusicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelConfigMusicResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelConfigMusicResp proto.InternalMessageInfo

type GetConfigMusicListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetConfigMusicListReq) Reset()         { *m = GetConfigMusicListReq{} }
func (m *GetConfigMusicListReq) String() string { return proto.CompactTextString(m) }
func (*GetConfigMusicListReq) ProtoMessage()    {}
func (*GetConfigMusicListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{91}
}
func (m *GetConfigMusicListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConfigMusicListReq.Unmarshal(m, b)
}
func (m *GetConfigMusicListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConfigMusicListReq.Marshal(b, m, deterministic)
}
func (dst *GetConfigMusicListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConfigMusicListReq.Merge(dst, src)
}
func (m *GetConfigMusicListReq) XXX_Size() int {
	return xxx_messageInfo_GetConfigMusicListReq.Size(m)
}
func (m *GetConfigMusicListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConfigMusicListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConfigMusicListReq proto.InternalMessageInfo

type GetConfigMusicListResp struct {
	ConfigList           []*ConfigMusicInfo `protobuf:"bytes,1,rep,name=config_list,json=configList" json:"config_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetConfigMusicListResp) Reset()         { *m = GetConfigMusicListResp{} }
func (m *GetConfigMusicListResp) String() string { return proto.CompactTextString(m) }
func (*GetConfigMusicListResp) ProtoMessage()    {}
func (*GetConfigMusicListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{92}
}
func (m *GetConfigMusicListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConfigMusicListResp.Unmarshal(m, b)
}
func (m *GetConfigMusicListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConfigMusicListResp.Marshal(b, m, deterministic)
}
func (dst *GetConfigMusicListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConfigMusicListResp.Merge(dst, src)
}
func (m *GetConfigMusicListResp) XXX_Size() int {
	return xxx_messageInfo_GetConfigMusicListResp.Size(m)
}
func (m *GetConfigMusicListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConfigMusicListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConfigMusicListResp proto.InternalMessageInfo

func (m *GetConfigMusicListResp) GetConfigList() []*ConfigMusicInfo {
	if m != nil {
		return m.ConfigList
	}
	return nil
}

type GetChannelCreateTsReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelCreateTsReq) Reset()         { *m = GetChannelCreateTsReq{} }
func (m *GetChannelCreateTsReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelCreateTsReq) ProtoMessage()    {}
func (*GetChannelCreateTsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{93}
}
func (m *GetChannelCreateTsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCreateTsReq.Unmarshal(m, b)
}
func (m *GetChannelCreateTsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCreateTsReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelCreateTsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCreateTsReq.Merge(dst, src)
}
func (m *GetChannelCreateTsReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelCreateTsReq.Size(m)
}
func (m *GetChannelCreateTsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCreateTsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCreateTsReq proto.InternalMessageInfo

func (m *GetChannelCreateTsReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

type GetChannelCreateTsResp struct {
	CreateTs             *uint32  `protobuf:"varint,1,req,name=create_ts,json=createTs" json:"create_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelCreateTsResp) Reset()         { *m = GetChannelCreateTsResp{} }
func (m *GetChannelCreateTsResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelCreateTsResp) ProtoMessage()    {}
func (*GetChannelCreateTsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{94}
}
func (m *GetChannelCreateTsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCreateTsResp.Unmarshal(m, b)
}
func (m *GetChannelCreateTsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCreateTsResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelCreateTsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCreateTsResp.Merge(dst, src)
}
func (m *GetChannelCreateTsResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelCreateTsResp.Size(m)
}
func (m *GetChannelCreateTsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCreateTsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCreateTsResp proto.InternalMessageInfo

func (m *GetChannelCreateTsResp) GetCreateTs() uint32 {
	if m != nil && m.CreateTs != nil {
		return *m.CreateTs
	}
	return 0
}

// 记录房间礼物信息
type RecordSendGiftEventReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	SendUid              *uint32  `protobuf:"varint,2,req,name=send_uid,json=sendUid" json:"send_uid,omitempty"`
	RecvUid              *uint32  `protobuf:"varint,3,req,name=recv_uid,json=recvUid" json:"recv_uid,omitempty"`
	GiftId               *uint32  `protobuf:"varint,4,req,name=gift_id,json=giftId" json:"gift_id,omitempty"`
	GiftCnt              *uint32  `protobuf:"varint,5,req,name=gift_cnt,json=giftCnt" json:"gift_cnt,omitempty"`
	GiftUnitCost         *uint32  `protobuf:"varint,6,req,name=gift_unit_cost,json=giftUnitCost" json:"gift_unit_cost,omitempty"`
	OrderId              *string  `protobuf:"bytes,7,opt,name=order_id,json=orderId" json:"order_id,omitempty"`
	GiftPriceType        *uint32  `protobuf:"varint,8,opt,name=gift_price_type,json=giftPriceType" json:"gift_price_type,omitempty"`
	ChannelType          *uint32  `protobuf:"varint,9,opt,name=channel_type,json=channelType" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordSendGiftEventReq) Reset()         { *m = RecordSendGiftEventReq{} }
func (m *RecordSendGiftEventReq) String() string { return proto.CompactTextString(m) }
func (*RecordSendGiftEventReq) ProtoMessage()    {}
func (*RecordSendGiftEventReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{95}
}
func (m *RecordSendGiftEventReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordSendGiftEventReq.Unmarshal(m, b)
}
func (m *RecordSendGiftEventReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordSendGiftEventReq.Marshal(b, m, deterministic)
}
func (dst *RecordSendGiftEventReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordSendGiftEventReq.Merge(dst, src)
}
func (m *RecordSendGiftEventReq) XXX_Size() int {
	return xxx_messageInfo_RecordSendGiftEventReq.Size(m)
}
func (m *RecordSendGiftEventReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordSendGiftEventReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecordSendGiftEventReq proto.InternalMessageInfo

func (m *RecordSendGiftEventReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetSendUid() uint32 {
	if m != nil && m.SendUid != nil {
		return *m.SendUid
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetRecvUid() uint32 {
	if m != nil && m.RecvUid != nil {
		return *m.RecvUid
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetGiftId() uint32 {
	if m != nil && m.GiftId != nil {
		return *m.GiftId
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetGiftCnt() uint32 {
	if m != nil && m.GiftCnt != nil {
		return *m.GiftCnt
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetGiftUnitCost() uint32 {
	if m != nil && m.GiftUnitCost != nil {
		return *m.GiftUnitCost
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetOrderId() string {
	if m != nil && m.OrderId != nil {
		return *m.OrderId
	}
	return ""
}

func (m *RecordSendGiftEventReq) GetGiftPriceType() uint32 {
	if m != nil && m.GiftPriceType != nil {
		return *m.GiftPriceType
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetChannelType() uint32 {
	if m != nil && m.ChannelType != nil {
		return *m.ChannelType
	}
	return 0
}

type RecordSendGiftEventResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordSendGiftEventResp) Reset()         { *m = RecordSendGiftEventResp{} }
func (m *RecordSendGiftEventResp) String() string { return proto.CompactTextString(m) }
func (*RecordSendGiftEventResp) ProtoMessage()    {}
func (*RecordSendGiftEventResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{96}
}
func (m *RecordSendGiftEventResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordSendGiftEventResp.Unmarshal(m, b)
}
func (m *RecordSendGiftEventResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordSendGiftEventResp.Marshal(b, m, deterministic)
}
func (dst *RecordSendGiftEventResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordSendGiftEventResp.Merge(dst, src)
}
func (m *RecordSendGiftEventResp) XXX_Size() int {
	return xxx_messageInfo_RecordSendGiftEventResp.Size(m)
}
func (m *RecordSendGiftEventResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordSendGiftEventResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecordSendGiftEventResp proto.InternalMessageInfo

// 获取房间消费土豪榜// 接口已经废弃
type MemberConsumeInfo struct {
	Uid                  *uint32  `protobuf:"varint,1,req,name=uid" json:"uid,omitempty"`
	ConsumeReddiamondCnt *uint32  `protobuf:"varint,2,req,name=consume_reddiamond_cnt,json=consumeReddiamondCnt" json:"consume_reddiamond_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberConsumeInfo) Reset()         { *m = MemberConsumeInfo{} }
func (m *MemberConsumeInfo) String() string { return proto.CompactTextString(m) }
func (*MemberConsumeInfo) ProtoMessage()    {}
func (*MemberConsumeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{97}
}
func (m *MemberConsumeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberConsumeInfo.Unmarshal(m, b)
}
func (m *MemberConsumeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberConsumeInfo.Marshal(b, m, deterministic)
}
func (dst *MemberConsumeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberConsumeInfo.Merge(dst, src)
}
func (m *MemberConsumeInfo) XXX_Size() int {
	return xxx_messageInfo_MemberConsumeInfo.Size(m)
}
func (m *MemberConsumeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberConsumeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MemberConsumeInfo proto.InternalMessageInfo

func (m *MemberConsumeInfo) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *MemberConsumeInfo) GetConsumeReddiamondCnt() uint32 {
	if m != nil && m.ConsumeReddiamondCnt != nil {
		return *m.ConsumeReddiamondCnt
	}
	return 0
}

// 接口已经废弃
type GetConsumeTopNReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	BeginIdx             *uint32  `protobuf:"varint,2,req,name=begin_idx,json=beginIdx" json:"begin_idx,omitempty"`
	Limit                *uint32  `protobuf:"varint,3,req,name=limit" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetConsumeTopNReq) Reset()         { *m = GetConsumeTopNReq{} }
func (m *GetConsumeTopNReq) String() string { return proto.CompactTextString(m) }
func (*GetConsumeTopNReq) ProtoMessage()    {}
func (*GetConsumeTopNReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{98}
}
func (m *GetConsumeTopNReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConsumeTopNReq.Unmarshal(m, b)
}
func (m *GetConsumeTopNReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConsumeTopNReq.Marshal(b, m, deterministic)
}
func (dst *GetConsumeTopNReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConsumeTopNReq.Merge(dst, src)
}
func (m *GetConsumeTopNReq) XXX_Size() int {
	return xxx_messageInfo_GetConsumeTopNReq.Size(m)
}
func (m *GetConsumeTopNReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConsumeTopNReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConsumeTopNReq proto.InternalMessageInfo

func (m *GetConsumeTopNReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *GetConsumeTopNReq) GetBeginIdx() uint32 {
	if m != nil && m.BeginIdx != nil {
		return *m.BeginIdx
	}
	return 0
}

func (m *GetConsumeTopNReq) GetLimit() uint32 {
	if m != nil && m.Limit != nil {
		return *m.Limit
	}
	return 0
}

// 接口已经废弃
type GetConsumeTopNResp struct {
	ConsumeList          []*MemberConsumeInfo `protobuf:"bytes,1,rep,name=consume_list,json=consumeList" json:"consume_list,omitempty"`
	TotalCnt             *uint32              `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetConsumeTopNResp) Reset()         { *m = GetConsumeTopNResp{} }
func (m *GetConsumeTopNResp) String() string { return proto.CompactTextString(m) }
func (*GetConsumeTopNResp) ProtoMessage()    {}
func (*GetConsumeTopNResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{99}
}
func (m *GetConsumeTopNResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConsumeTopNResp.Unmarshal(m, b)
}
func (m *GetConsumeTopNResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConsumeTopNResp.Marshal(b, m, deterministic)
}
func (dst *GetConsumeTopNResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConsumeTopNResp.Merge(dst, src)
}
func (m *GetConsumeTopNResp) XXX_Size() int {
	return xxx_messageInfo_GetConsumeTopNResp.Size(m)
}
func (m *GetConsumeTopNResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConsumeTopNResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConsumeTopNResp proto.InternalMessageInfo

func (m *GetConsumeTopNResp) GetConsumeList() []*MemberConsumeInfo {
	if m != nil {
		return m.ConsumeList
	}
	return nil
}

func (m *GetConsumeTopNResp) GetTotalCnt() uint32 {
	if m != nil && m.TotalCnt != nil {
		return *m.TotalCnt
	}
	return 0
}

// 接口已经废弃
type GetUserAccumulateConsumeReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	Uid                  *uint32  `protobuf:"varint,2,req,name=uid" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserAccumulateConsumeReq) Reset()         { *m = GetUserAccumulateConsumeReq{} }
func (m *GetUserAccumulateConsumeReq) String() string { return proto.CompactTextString(m) }
func (*GetUserAccumulateConsumeReq) ProtoMessage()    {}
func (*GetUserAccumulateConsumeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{100}
}
func (m *GetUserAccumulateConsumeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAccumulateConsumeReq.Unmarshal(m, b)
}
func (m *GetUserAccumulateConsumeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAccumulateConsumeReq.Marshal(b, m, deterministic)
}
func (dst *GetUserAccumulateConsumeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAccumulateConsumeReq.Merge(dst, src)
}
func (m *GetUserAccumulateConsumeReq) XXX_Size() int {
	return xxx_messageInfo_GetUserAccumulateConsumeReq.Size(m)
}
func (m *GetUserAccumulateConsumeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAccumulateConsumeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAccumulateConsumeReq proto.InternalMessageInfo

func (m *GetUserAccumulateConsumeReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *GetUserAccumulateConsumeReq) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

// 接口已经废弃
type GetUserAccumulateConsumeResp struct {
	Amount               *uint32  `protobuf:"varint,1,opt,name=amount" json:"amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserAccumulateConsumeResp) Reset()         { *m = GetUserAccumulateConsumeResp{} }
func (m *GetUserAccumulateConsumeResp) String() string { return proto.CompactTextString(m) }
func (*GetUserAccumulateConsumeResp) ProtoMessage()    {}
func (*GetUserAccumulateConsumeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{101}
}
func (m *GetUserAccumulateConsumeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAccumulateConsumeResp.Unmarshal(m, b)
}
func (m *GetUserAccumulateConsumeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAccumulateConsumeResp.Marshal(b, m, deterministic)
}
func (dst *GetUserAccumulateConsumeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAccumulateConsumeResp.Merge(dst, src)
}
func (m *GetUserAccumulateConsumeResp) XXX_Size() int {
	return xxx_messageInfo_GetUserAccumulateConsumeResp.Size(m)
}
func (m *GetUserAccumulateConsumeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAccumulateConsumeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAccumulateConsumeResp proto.InternalMessageInfo

func (m *GetUserAccumulateConsumeResp) GetAmount() uint32 {
	if m != nil && m.Amount != nil {
		return *m.Amount
	}
	return 0
}

// 获取房间送礼物的统计信息
type ChannelGiftStat struct {
	GiftId               *uint32  `protobuf:"varint,1,req,name=gift_id,json=giftId" json:"gift_id,omitempty"`
	GiftCnt              *uint32  `protobuf:"varint,2,req,name=gift_cnt,json=giftCnt" json:"gift_cnt,omitempty"`
	GiftReddiamond       *uint32  `protobuf:"varint,3,req,name=gift_reddiamond,json=giftReddiamond" json:"gift_reddiamond,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelGiftStat) Reset()         { *m = ChannelGiftStat{} }
func (m *ChannelGiftStat) String() string { return proto.CompactTextString(m) }
func (*ChannelGiftStat) ProtoMessage()    {}
func (*ChannelGiftStat) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{102}
}
func (m *ChannelGiftStat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelGiftStat.Unmarshal(m, b)
}
func (m *ChannelGiftStat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelGiftStat.Marshal(b, m, deterministic)
}
func (dst *ChannelGiftStat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelGiftStat.Merge(dst, src)
}
func (m *ChannelGiftStat) XXX_Size() int {
	return xxx_messageInfo_ChannelGiftStat.Size(m)
}
func (m *ChannelGiftStat) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelGiftStat.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelGiftStat proto.InternalMessageInfo

func (m *ChannelGiftStat) GetGiftId() uint32 {
	if m != nil && m.GiftId != nil {
		return *m.GiftId
	}
	return 0
}

func (m *ChannelGiftStat) GetGiftCnt() uint32 {
	if m != nil && m.GiftCnt != nil {
		return *m.GiftCnt
	}
	return 0
}

func (m *ChannelGiftStat) GetGiftReddiamond() uint32 {
	if m != nil && m.GiftReddiamond != nil {
		return *m.GiftReddiamond
	}
	return 0
}

// 接口废弃，改用ChangeChannelViewID接口
// 更新房间的displayID
type ChangeDisplayIDReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	NewDisplayId         *uint32  `protobuf:"varint,2,req,name=new_display_id,json=newDisplayId" json:"new_display_id,omitempty"`
	MagicId              *uint32  `protobuf:"varint,3,req,name=magic_id,json=magicId" json:"magic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeDisplayIDReq) Reset()         { *m = ChangeDisplayIDReq{} }
func (m *ChangeDisplayIDReq) String() string { return proto.CompactTextString(m) }
func (*ChangeDisplayIDReq) ProtoMessage()    {}
func (*ChangeDisplayIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{103}
}
func (m *ChangeDisplayIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeDisplayIDReq.Unmarshal(m, b)
}
func (m *ChangeDisplayIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeDisplayIDReq.Marshal(b, m, deterministic)
}
func (dst *ChangeDisplayIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeDisplayIDReq.Merge(dst, src)
}
func (m *ChangeDisplayIDReq) XXX_Size() int {
	return xxx_messageInfo_ChangeDisplayIDReq.Size(m)
}
func (m *ChangeDisplayIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeDisplayIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeDisplayIDReq proto.InternalMessageInfo

func (m *ChangeDisplayIDReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *ChangeDisplayIDReq) GetNewDisplayId() uint32 {
	if m != nil && m.NewDisplayId != nil {
		return *m.NewDisplayId
	}
	return 0
}

func (m *ChangeDisplayIDReq) GetMagicId() uint32 {
	if m != nil && m.MagicId != nil {
		return *m.MagicId
	}
	return 0
}

type ChangeDisplayIDResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeDisplayIDResp) Reset()         { *m = ChangeDisplayIDResp{} }
func (m *ChangeDisplayIDResp) String() string { return proto.CompactTextString(m) }
func (*ChangeDisplayIDResp) ProtoMessage()    {}
func (*ChangeDisplayIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{104}
}
func (m *ChangeDisplayIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeDisplayIDResp.Unmarshal(m, b)
}
func (m *ChangeDisplayIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeDisplayIDResp.Marshal(b, m, deterministic)
}
func (dst *ChangeDisplayIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeDisplayIDResp.Merge(dst, src)
}
func (m *ChangeDisplayIDResp) XXX_Size() int {
	return xxx_messageInfo_ChangeDisplayIDResp.Size(m)
}
func (m *ChangeDisplayIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeDisplayIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeDisplayIDResp proto.InternalMessageInfo

// 获取房间最近访客列表
type ChannelHistory struct {
	Uid                  *uint32  `protobuf:"varint,1,req,name=uid" json:"uid,omitempty"`
	LastEnterTs          *uint32  `protobuf:"varint,2,req,name=last_enter_ts,json=lastEnterTs" json:"last_enter_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelHistory) Reset()         { *m = ChannelHistory{} }
func (m *ChannelHistory) String() string { return proto.CompactTextString(m) }
func (*ChannelHistory) ProtoMessage()    {}
func (*ChannelHistory) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{105}
}
func (m *ChannelHistory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelHistory.Unmarshal(m, b)
}
func (m *ChannelHistory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelHistory.Marshal(b, m, deterministic)
}
func (dst *ChannelHistory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelHistory.Merge(dst, src)
}
func (m *ChannelHistory) XXX_Size() int {
	return xxx_messageInfo_ChannelHistory.Size(m)
}
func (m *ChannelHistory) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelHistory.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelHistory proto.InternalMessageInfo

func (m *ChannelHistory) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *ChannelHistory) GetLastEnterTs() uint32 {
	if m != nil && m.LastEnterTs != nil {
		return *m.LastEnterTs
	}
	return 0
}

type GetChannelHistoryListReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	BeginIdx             *uint32  `protobuf:"varint,2,req,name=begin_idx,json=beginIdx" json:"begin_idx,omitempty"`
	Limit                *uint32  `protobuf:"varint,3,req,name=limit" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelHistoryListReq) Reset()         { *m = GetChannelHistoryListReq{} }
func (m *GetChannelHistoryListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelHistoryListReq) ProtoMessage()    {}
func (*GetChannelHistoryListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{106}
}
func (m *GetChannelHistoryListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelHistoryListReq.Unmarshal(m, b)
}
func (m *GetChannelHistoryListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelHistoryListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelHistoryListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelHistoryListReq.Merge(dst, src)
}
func (m *GetChannelHistoryListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelHistoryListReq.Size(m)
}
func (m *GetChannelHistoryListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelHistoryListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelHistoryListReq proto.InternalMessageInfo

func (m *GetChannelHistoryListReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *GetChannelHistoryListReq) GetBeginIdx() uint32 {
	if m != nil && m.BeginIdx != nil {
		return *m.BeginIdx
	}
	return 0
}

func (m *GetChannelHistoryListReq) GetLimit() uint32 {
	if m != nil && m.Limit != nil {
		return *m.Limit
	}
	return 0
}

type GetChannelHistoryListResp struct {
	EnterList            []*ChannelHistory `protobuf:"bytes,1,rep,name=enter_list,json=enterList" json:"enter_list,omitempty"`
	AllHistorySize       *uint32           `protobuf:"varint,2,req,name=all_history_size,json=allHistorySize" json:"all_history_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetChannelHistoryListResp) Reset()         { *m = GetChannelHistoryListResp{} }
func (m *GetChannelHistoryListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelHistoryListResp) ProtoMessage()    {}
func (*GetChannelHistoryListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{107}
}
func (m *GetChannelHistoryListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelHistoryListResp.Unmarshal(m, b)
}
func (m *GetChannelHistoryListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelHistoryListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelHistoryListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelHistoryListResp.Merge(dst, src)
}
func (m *GetChannelHistoryListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelHistoryListResp.Size(m)
}
func (m *GetChannelHistoryListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelHistoryListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelHistoryListResp proto.InternalMessageInfo

func (m *GetChannelHistoryListResp) GetEnterList() []*ChannelHistory {
	if m != nil {
		return m.EnterList
	}
	return nil
}

func (m *GetChannelHistoryListResp) GetAllHistorySize() uint32 {
	if m != nil && m.AllHistorySize != nil {
		return *m.AllHistorySize
	}
	return 0
}

// 获取房间统计信息(包括房间最大在线人数)
type GetChannelStatReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelStatReq) Reset()         { *m = GetChannelStatReq{} }
func (m *GetChannelStatReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelStatReq) ProtoMessage()    {}
func (*GetChannelStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{108}
}
func (m *GetChannelStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelStatReq.Unmarshal(m, b)
}
func (m *GetChannelStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelStatReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelStatReq.Merge(dst, src)
}
func (m *GetChannelStatReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelStatReq.Size(m)
}
func (m *GetChannelStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelStatReq proto.InternalMessageInfo

func (m *GetChannelStatReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

type GetChannelStatResp struct {
	MaxOnlineCnt         *uint32  `protobuf:"varint,1,req,name=max_online_cnt,json=maxOnlineCnt" json:"max_online_cnt,omitempty"`
	MaxOnlineTime        *uint32  `protobuf:"varint,2,req,name=max_online_time,json=maxOnlineTime" json:"max_online_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelStatResp) Reset()         { *m = GetChannelStatResp{} }
func (m *GetChannelStatResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelStatResp) ProtoMessage()    {}
func (*GetChannelStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{109}
}
func (m *GetChannelStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelStatResp.Unmarshal(m, b)
}
func (m *GetChannelStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelStatResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelStatResp.Merge(dst, src)
}
func (m *GetChannelStatResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelStatResp.Size(m)
}
func (m *GetChannelStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelStatResp proto.InternalMessageInfo

func (m *GetChannelStatResp) GetMaxOnlineCnt() uint32 {
	if m != nil && m.MaxOnlineCnt != nil {
		return *m.MaxOnlineCnt
	}
	return 0
}

func (m *GetChannelStatResp) GetMaxOnlineTime() uint32 {
	if m != nil && m.MaxOnlineTime != nil {
		return *m.MaxOnlineTime
	}
	return 0
}

// 获取房间管理员列表
type ChannelAdmin struct {
	Uid                  *uint32  `protobuf:"varint,1,req,name=uid" json:"uid,omitempty"`
	AdminRole            *uint32  `protobuf:"varint,2,req,name=admin_role,json=adminRole" json:"admin_role,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelAdmin) Reset()         { *m = ChannelAdmin{} }
func (m *ChannelAdmin) String() string { return proto.CompactTextString(m) }
func (*ChannelAdmin) ProtoMessage()    {}
func (*ChannelAdmin) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{110}
}
func (m *ChannelAdmin) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelAdmin.Unmarshal(m, b)
}
func (m *ChannelAdmin) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelAdmin.Marshal(b, m, deterministic)
}
func (dst *ChannelAdmin) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelAdmin.Merge(dst, src)
}
func (m *ChannelAdmin) XXX_Size() int {
	return xxx_messageInfo_ChannelAdmin.Size(m)
}
func (m *ChannelAdmin) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelAdmin.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelAdmin proto.InternalMessageInfo

func (m *ChannelAdmin) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *ChannelAdmin) GetAdminRole() uint32 {
	if m != nil && m.AdminRole != nil {
		return *m.AdminRole
	}
	return 0
}

type GetChannelAdminReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	OptUid               *uint32  `protobuf:"varint,2,opt,name=opt_uid,json=optUid" json:"opt_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelAdminReq) Reset()         { *m = GetChannelAdminReq{} }
func (m *GetChannelAdminReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelAdminReq) ProtoMessage()    {}
func (*GetChannelAdminReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{111}
}
func (m *GetChannelAdminReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelAdminReq.Unmarshal(m, b)
}
func (m *GetChannelAdminReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelAdminReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelAdminReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelAdminReq.Merge(dst, src)
}
func (m *GetChannelAdminReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelAdminReq.Size(m)
}
func (m *GetChannelAdminReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelAdminReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelAdminReq proto.InternalMessageInfo

func (m *GetChannelAdminReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *GetChannelAdminReq) GetOptUid() uint32 {
	if m != nil && m.OptUid != nil {
		return *m.OptUid
	}
	return 0
}

type GetChannelAdminResp struct {
	AdminList            []*ChannelAdmin `protobuf:"bytes,1,rep,name=admin_list,json=adminList" json:"admin_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetChannelAdminResp) Reset()         { *m = GetChannelAdminResp{} }
func (m *GetChannelAdminResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelAdminResp) ProtoMessage()    {}
func (*GetChannelAdminResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{112}
}
func (m *GetChannelAdminResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelAdminResp.Unmarshal(m, b)
}
func (m *GetChannelAdminResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelAdminResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelAdminResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelAdminResp.Merge(dst, src)
}
func (m *GetChannelAdminResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelAdminResp.Size(m)
}
func (m *GetChannelAdminResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelAdminResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelAdminResp proto.InternalMessageInfo

func (m *GetChannelAdminResp) GetAdminList() []*ChannelAdmin {
	if m != nil {
		return m.AdminList
	}
	return nil
}

type AddChannelAdminReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	Uid                  *uint32  `protobuf:"varint,2,req,name=uid" json:"uid,omitempty"`
	Role                 *uint32  `protobuf:"varint,3,req,name=role" json:"role,omitempty"`
	MaxCount             *uint32  `protobuf:"varint,4,opt,name=max_count,json=maxCount" json:"max_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelAdminReq) Reset()         { *m = AddChannelAdminReq{} }
func (m *AddChannelAdminReq) String() string { return proto.CompactTextString(m) }
func (*AddChannelAdminReq) ProtoMessage()    {}
func (*AddChannelAdminReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{113}
}
func (m *AddChannelAdminReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelAdminReq.Unmarshal(m, b)
}
func (m *AddChannelAdminReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelAdminReq.Marshal(b, m, deterministic)
}
func (dst *AddChannelAdminReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelAdminReq.Merge(dst, src)
}
func (m *AddChannelAdminReq) XXX_Size() int {
	return xxx_messageInfo_AddChannelAdminReq.Size(m)
}
func (m *AddChannelAdminReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelAdminReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelAdminReq proto.InternalMessageInfo

func (m *AddChannelAdminReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *AddChannelAdminReq) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *AddChannelAdminReq) GetRole() uint32 {
	if m != nil && m.Role != nil {
		return *m.Role
	}
	return 0
}

func (m *AddChannelAdminReq) GetMaxCount() uint32 {
	if m != nil && m.MaxCount != nil {
		return *m.MaxCount
	}
	return 0
}

type AddChannelAdminResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelAdminResp) Reset()         { *m = AddChannelAdminResp{} }
func (m *AddChannelAdminResp) String() string { return proto.CompactTextString(m) }
func (*AddChannelAdminResp) ProtoMessage()    {}
func (*AddChannelAdminResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{114}
}
func (m *AddChannelAdminResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelAdminResp.Unmarshal(m, b)
}
func (m *AddChannelAdminResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelAdminResp.Marshal(b, m, deterministic)
}
func (dst *AddChannelAdminResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelAdminResp.Merge(dst, src)
}
func (m *AddChannelAdminResp) XXX_Size() int {
	return xxx_messageInfo_AddChannelAdminResp.Size(m)
}
func (m *AddChannelAdminResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelAdminResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelAdminResp proto.InternalMessageInfo

type RemoveChannelAdminReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	Uid                  *uint32  `protobuf:"varint,2,req,name=uid" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveChannelAdminReq) Reset()         { *m = RemoveChannelAdminReq{} }
func (m *RemoveChannelAdminReq) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelAdminReq) ProtoMessage()    {}
func (*RemoveChannelAdminReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{115}
}
func (m *RemoveChannelAdminReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveChannelAdminReq.Unmarshal(m, b)
}
func (m *RemoveChannelAdminReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveChannelAdminReq.Marshal(b, m, deterministic)
}
func (dst *RemoveChannelAdminReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveChannelAdminReq.Merge(dst, src)
}
func (m *RemoveChannelAdminReq) XXX_Size() int {
	return xxx_messageInfo_RemoveChannelAdminReq.Size(m)
}
func (m *RemoveChannelAdminReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveChannelAdminReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveChannelAdminReq proto.InternalMessageInfo

func (m *RemoveChannelAdminReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *RemoveChannelAdminReq) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

type RemoveChannelAdminResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveChannelAdminResp) Reset()         { *m = RemoveChannelAdminResp{} }
func (m *RemoveChannelAdminResp) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelAdminResp) ProtoMessage()    {}
func (*RemoveChannelAdminResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{116}
}
func (m *RemoveChannelAdminResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveChannelAdminResp.Unmarshal(m, b)
}
func (m *RemoveChannelAdminResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveChannelAdminResp.Marshal(b, m, deterministic)
}
func (dst *RemoveChannelAdminResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveChannelAdminResp.Merge(dst, src)
}
func (m *RemoveChannelAdminResp) XXX_Size() int {
	return xxx_messageInfo_RemoveChannelAdminResp.Size(m)
}
func (m *RemoveChannelAdminResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveChannelAdminResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveChannelAdminResp proto.InternalMessageInfo

// 设置麦位状态 比如打开/关闭/禁言 麦位 (可以取代之前的麦位开启和麦位关闭命令)
// 接口已经废弃
type SetChannelMicSpaceStatusReq struct {
	ChannelId            *uint32        `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	OpUid                *uint32        `protobuf:"varint,2,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	MicInfo              *MicrSpaceInfo `protobuf:"bytes,3,req,name=mic_info,json=micInfo" json:"mic_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SetChannelMicSpaceStatusReq) Reset()         { *m = SetChannelMicSpaceStatusReq{} }
func (m *SetChannelMicSpaceStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelMicSpaceStatusReq) ProtoMessage()    {}
func (*SetChannelMicSpaceStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{117}
}
func (m *SetChannelMicSpaceStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMicSpaceStatusReq.Unmarshal(m, b)
}
func (m *SetChannelMicSpaceStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMicSpaceStatusReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelMicSpaceStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMicSpaceStatusReq.Merge(dst, src)
}
func (m *SetChannelMicSpaceStatusReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelMicSpaceStatusReq.Size(m)
}
func (m *SetChannelMicSpaceStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMicSpaceStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMicSpaceStatusReq proto.InternalMessageInfo

func (m *SetChannelMicSpaceStatusReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *SetChannelMicSpaceStatusReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

func (m *SetChannelMicSpaceStatusReq) GetMicInfo() *MicrSpaceInfo {
	if m != nil {
		return m.MicInfo
	}
	return nil
}

// 接口已经废弃
type SetChannelMicSpaceStatusResp struct {
	AllMicList           []*MicrSpaceInfo `protobuf:"bytes,1,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	KickedUid            *uint32          `protobuf:"varint,2,opt,name=kicked_uid,json=kickedUid" json:"kicked_uid,omitempty"`
	ServerTimeMs         *uint64          `protobuf:"varint,3,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SetChannelMicSpaceStatusResp) Reset()         { *m = SetChannelMicSpaceStatusResp{} }
func (m *SetChannelMicSpaceStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelMicSpaceStatusResp) ProtoMessage()    {}
func (*SetChannelMicSpaceStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{118}
}
func (m *SetChannelMicSpaceStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMicSpaceStatusResp.Unmarshal(m, b)
}
func (m *SetChannelMicSpaceStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMicSpaceStatusResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelMicSpaceStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMicSpaceStatusResp.Merge(dst, src)
}
func (m *SetChannelMicSpaceStatusResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelMicSpaceStatusResp.Size(m)
}
func (m *SetChannelMicSpaceStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMicSpaceStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMicSpaceStatusResp proto.InternalMessageInfo

func (m *SetChannelMicSpaceStatusResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *SetChannelMicSpaceStatusResp) GetKickedUid() uint32 {
	if m != nil && m.KickedUid != nil {
		return *m.KickedUid
	}
	return 0
}

func (m *SetChannelMicSpaceStatusResp) GetServerTimeMs() uint64 {
	if m != nil && m.ServerTimeMs != nil {
		return *m.ServerTimeMs
	}
	return 0
}

// 换自己的麦的位置
// 接口已经废弃
type ChangeMicrophoneReq struct {
	OpUid                *uint32        `protobuf:"varint,1,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	ChannelId            *uint32        `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	ToMicInfo            *MicrSpaceInfo `protobuf:"bytes,3,req,name=to_mic_info,json=toMicInfo" json:"to_mic_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ChangeMicrophoneReq) Reset()         { *m = ChangeMicrophoneReq{} }
func (m *ChangeMicrophoneReq) String() string { return proto.CompactTextString(m) }
func (*ChangeMicrophoneReq) ProtoMessage()    {}
func (*ChangeMicrophoneReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{119}
}
func (m *ChangeMicrophoneReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeMicrophoneReq.Unmarshal(m, b)
}
func (m *ChangeMicrophoneReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeMicrophoneReq.Marshal(b, m, deterministic)
}
func (dst *ChangeMicrophoneReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeMicrophoneReq.Merge(dst, src)
}
func (m *ChangeMicrophoneReq) XXX_Size() int {
	return xxx_messageInfo_ChangeMicrophoneReq.Size(m)
}
func (m *ChangeMicrophoneReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeMicrophoneReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeMicrophoneReq proto.InternalMessageInfo

func (m *ChangeMicrophoneReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

func (m *ChangeMicrophoneReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *ChangeMicrophoneReq) GetToMicInfo() *MicrSpaceInfo {
	if m != nil {
		return m.ToMicInfo
	}
	return nil
}

// 接口已经废弃
type ChangeMicrophoneResp struct {
	FromMicInfo          *MicrSpaceInfo   `protobuf:"bytes,1,req,name=from_mic_info,json=fromMicInfo" json:"from_mic_info,omitempty"`
	ToMicInfo            *MicrSpaceInfo   `protobuf:"bytes,2,req,name=to_mic_info,json=toMicInfo" json:"to_mic_info,omitempty"`
	ServerTimeMs         *uint64          `protobuf:"varint,3,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms,omitempty"`
	AllMicList           []*MicrSpaceInfo `protobuf:"bytes,4,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	MicMode              *uint32          `protobuf:"varint,5,opt,name=mic_mode,json=micMode" json:"mic_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ChangeMicrophoneResp) Reset()         { *m = ChangeMicrophoneResp{} }
func (m *ChangeMicrophoneResp) String() string { return proto.CompactTextString(m) }
func (*ChangeMicrophoneResp) ProtoMessage()    {}
func (*ChangeMicrophoneResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{120}
}
func (m *ChangeMicrophoneResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeMicrophoneResp.Unmarshal(m, b)
}
func (m *ChangeMicrophoneResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeMicrophoneResp.Marshal(b, m, deterministic)
}
func (dst *ChangeMicrophoneResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeMicrophoneResp.Merge(dst, src)
}
func (m *ChangeMicrophoneResp) XXX_Size() int {
	return xxx_messageInfo_ChangeMicrophoneResp.Size(m)
}
func (m *ChangeMicrophoneResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeMicrophoneResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeMicrophoneResp proto.InternalMessageInfo

func (m *ChangeMicrophoneResp) GetFromMicInfo() *MicrSpaceInfo {
	if m != nil {
		return m.FromMicInfo
	}
	return nil
}

func (m *ChangeMicrophoneResp) GetToMicInfo() *MicrSpaceInfo {
	if m != nil {
		return m.ToMicInfo
	}
	return nil
}

func (m *ChangeMicrophoneResp) GetServerTimeMs() uint64 {
	if m != nil && m.ServerTimeMs != nil {
		return *m.ServerTimeMs
	}
	return 0
}

func (m *ChangeMicrophoneResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *ChangeMicrophoneResp) GetMicMode() uint32 {
	if m != nil && m.MicMode != nil {
		return *m.MicMode
	}
	return 0
}

// 获取房间主题详情
type GetChannelTopicDetailReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelTopicDetailReq) Reset()         { *m = GetChannelTopicDetailReq{} }
func (m *GetChannelTopicDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelTopicDetailReq) ProtoMessage()    {}
func (*GetChannelTopicDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{121}
}
func (m *GetChannelTopicDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTopicDetailReq.Unmarshal(m, b)
}
func (m *GetChannelTopicDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTopicDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelTopicDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTopicDetailReq.Merge(dst, src)
}
func (m *GetChannelTopicDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelTopicDetailReq.Size(m)
}
func (m *GetChannelTopicDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTopicDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTopicDetailReq proto.InternalMessageInfo

func (m *GetChannelTopicDetailReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

type GetChannelTopicDetailResp struct {
	TopicDetail          *string  `protobuf:"bytes,1,req,name=topic_detail,json=topicDetail" json:"topic_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelTopicDetailResp) Reset()         { *m = GetChannelTopicDetailResp{} }
func (m *GetChannelTopicDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelTopicDetailResp) ProtoMessage()    {}
func (*GetChannelTopicDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{122}
}
func (m *GetChannelTopicDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTopicDetailResp.Unmarshal(m, b)
}
func (m *GetChannelTopicDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTopicDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelTopicDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTopicDetailResp.Merge(dst, src)
}
func (m *GetChannelTopicDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelTopicDetailResp.Size(m)
}
func (m *GetChannelTopicDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTopicDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTopicDetailResp proto.InternalMessageInfo

func (m *GetChannelTopicDetailResp) GetTopicDetail() string {
	if m != nil && m.TopicDetail != nil {
		return *m.TopicDetail
	}
	return ""
}

// 获取房间欢迎语
type GetChannelWelcomeMsgReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelWelcomeMsgReq) Reset()         { *m = GetChannelWelcomeMsgReq{} }
func (m *GetChannelWelcomeMsgReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelWelcomeMsgReq) ProtoMessage()    {}
func (*GetChannelWelcomeMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{123}
}
func (m *GetChannelWelcomeMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelWelcomeMsgReq.Unmarshal(m, b)
}
func (m *GetChannelWelcomeMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelWelcomeMsgReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelWelcomeMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelWelcomeMsgReq.Merge(dst, src)
}
func (m *GetChannelWelcomeMsgReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelWelcomeMsgReq.Size(m)
}
func (m *GetChannelWelcomeMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelWelcomeMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelWelcomeMsgReq proto.InternalMessageInfo

func (m *GetChannelWelcomeMsgReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

type GetChannelWelcomeMsgResp struct {
	WelcomeMsg           *string  `protobuf:"bytes,1,req,name=welcome_msg,json=welcomeMsg" json:"welcome_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelWelcomeMsgResp) Reset()         { *m = GetChannelWelcomeMsgResp{} }
func (m *GetChannelWelcomeMsgResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelWelcomeMsgResp) ProtoMessage()    {}
func (*GetChannelWelcomeMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{124}
}
func (m *GetChannelWelcomeMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelWelcomeMsgResp.Unmarshal(m, b)
}
func (m *GetChannelWelcomeMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelWelcomeMsgResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelWelcomeMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelWelcomeMsgResp.Merge(dst, src)
}
func (m *GetChannelWelcomeMsgResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelWelcomeMsgResp.Size(m)
}
func (m *GetChannelWelcomeMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelWelcomeMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelWelcomeMsgResp proto.InternalMessageInfo

func (m *GetChannelWelcomeMsgResp) GetWelcomeMsg() string {
	if m != nil && m.WelcomeMsg != nil {
		return *m.WelcomeMsg
	}
	return ""
}

// 对所有空麦位锁麦
// 接口已经废弃
type DisableAllEmptyMicrSpaceReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	OpUid                *uint32  `protobuf:"varint,2,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisableAllEmptyMicrSpaceReq) Reset()         { *m = DisableAllEmptyMicrSpaceReq{} }
func (m *DisableAllEmptyMicrSpaceReq) String() string { return proto.CompactTextString(m) }
func (*DisableAllEmptyMicrSpaceReq) ProtoMessage()    {}
func (*DisableAllEmptyMicrSpaceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{125}
}
func (m *DisableAllEmptyMicrSpaceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisableAllEmptyMicrSpaceReq.Unmarshal(m, b)
}
func (m *DisableAllEmptyMicrSpaceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisableAllEmptyMicrSpaceReq.Marshal(b, m, deterministic)
}
func (dst *DisableAllEmptyMicrSpaceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisableAllEmptyMicrSpaceReq.Merge(dst, src)
}
func (m *DisableAllEmptyMicrSpaceReq) XXX_Size() int {
	return xxx_messageInfo_DisableAllEmptyMicrSpaceReq.Size(m)
}
func (m *DisableAllEmptyMicrSpaceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DisableAllEmptyMicrSpaceReq.DiscardUnknown(m)
}

var xxx_messageInfo_DisableAllEmptyMicrSpaceReq proto.InternalMessageInfo

func (m *DisableAllEmptyMicrSpaceReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *DisableAllEmptyMicrSpaceReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

type DisableAllEmptyMicrSpaceResp struct {
	DisableMicidList     []uint32         `protobuf:"varint,1,rep,name=disable_micid_list,json=disableMicidList" json:"disable_micid_list,omitempty"`
	AllMicList           []*MicrSpaceInfo `protobuf:"bytes,2,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	MicMode              *uint32          `protobuf:"varint,3,req,name=mic_mode,json=micMode" json:"mic_mode,omitempty"`
	ServerTimeMs         *uint64          `protobuf:"varint,4,req,name=server_time_ms,json=serverTimeMs" json:"server_time_ms,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *DisableAllEmptyMicrSpaceResp) Reset()         { *m = DisableAllEmptyMicrSpaceResp{} }
func (m *DisableAllEmptyMicrSpaceResp) String() string { return proto.CompactTextString(m) }
func (*DisableAllEmptyMicrSpaceResp) ProtoMessage()    {}
func (*DisableAllEmptyMicrSpaceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{126}
}
func (m *DisableAllEmptyMicrSpaceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisableAllEmptyMicrSpaceResp.Unmarshal(m, b)
}
func (m *DisableAllEmptyMicrSpaceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisableAllEmptyMicrSpaceResp.Marshal(b, m, deterministic)
}
func (dst *DisableAllEmptyMicrSpaceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisableAllEmptyMicrSpaceResp.Merge(dst, src)
}
func (m *DisableAllEmptyMicrSpaceResp) XXX_Size() int {
	return xxx_messageInfo_DisableAllEmptyMicrSpaceResp.Size(m)
}
func (m *DisableAllEmptyMicrSpaceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DisableAllEmptyMicrSpaceResp.DiscardUnknown(m)
}

var xxx_messageInfo_DisableAllEmptyMicrSpaceResp proto.InternalMessageInfo

func (m *DisableAllEmptyMicrSpaceResp) GetDisableMicidList() []uint32 {
	if m != nil {
		return m.DisableMicidList
	}
	return nil
}

func (m *DisableAllEmptyMicrSpaceResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *DisableAllEmptyMicrSpaceResp) GetMicMode() uint32 {
	if m != nil && m.MicMode != nil {
		return *m.MicMode
	}
	return 0
}

func (m *DisableAllEmptyMicrSpaceResp) GetServerTimeMs() uint64 {
	if m != nil && m.ServerTimeMs != nil {
		return *m.ServerTimeMs
	}
	return 0
}

// 开播// 接口已经废弃
type StartLiveReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartLiveReq) Reset()         { *m = StartLiveReq{} }
func (m *StartLiveReq) String() string { return proto.CompactTextString(m) }
func (*StartLiveReq) ProtoMessage()    {}
func (*StartLiveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{127}
}
func (m *StartLiveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartLiveReq.Unmarshal(m, b)
}
func (m *StartLiveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartLiveReq.Marshal(b, m, deterministic)
}
func (dst *StartLiveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartLiveReq.Merge(dst, src)
}
func (m *StartLiveReq) XXX_Size() int {
	return xxx_messageInfo_StartLiveReq.Size(m)
}
func (m *StartLiveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartLiveReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartLiveReq proto.InternalMessageInfo

func (m *StartLiveReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

type StartLiveResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartLiveResp) Reset()         { *m = StartLiveResp{} }
func (m *StartLiveResp) String() string { return proto.CompactTextString(m) }
func (*StartLiveResp) ProtoMessage()    {}
func (*StartLiveResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{128}
}
func (m *StartLiveResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartLiveResp.Unmarshal(m, b)
}
func (m *StartLiveResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartLiveResp.Marshal(b, m, deterministic)
}
func (dst *StartLiveResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartLiveResp.Merge(dst, src)
}
func (m *StartLiveResp) XXX_Size() int {
	return xxx_messageInfo_StartLiveResp.Size(m)
}
func (m *StartLiveResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartLiveResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartLiveResp proto.InternalMessageInfo

// 结束直播 // 接口已经废弃
type FinishLiveReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	OpUid                *uint32  `protobuf:"varint,2,opt,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FinishLiveReq) Reset()         { *m = FinishLiveReq{} }
func (m *FinishLiveReq) String() string { return proto.CompactTextString(m) }
func (*FinishLiveReq) ProtoMessage()    {}
func (*FinishLiveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{129}
}
func (m *FinishLiveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FinishLiveReq.Unmarshal(m, b)
}
func (m *FinishLiveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FinishLiveReq.Marshal(b, m, deterministic)
}
func (dst *FinishLiveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FinishLiveReq.Merge(dst, src)
}
func (m *FinishLiveReq) XXX_Size() int {
	return xxx_messageInfo_FinishLiveReq.Size(m)
}
func (m *FinishLiveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FinishLiveReq.DiscardUnknown(m)
}

var xxx_messageInfo_FinishLiveReq proto.InternalMessageInfo

func (m *FinishLiveReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *FinishLiveReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

// 接口已经废弃
type FinishLiveResp struct {
	UserPv               *uint32  `protobuf:"varint,1,req,name=user_pv,json=userPv" json:"user_pv,omitempty"`
	UserUv               *uint32  `protobuf:"varint,2,req,name=user_uv,json=userUv" json:"user_uv,omitempty"`
	TcoinGiftCnt         *uint32  `protobuf:"varint,3,req,name=tcoin_gift_cnt,json=tcoinGiftCnt" json:"tcoin_gift_cnt,omitempty"`
	TcoinGiftCost        *uint32  `protobuf:"varint,4,req,name=tcoin_gift_cost,json=tcoinGiftCost" json:"tcoin_gift_cost,omitempty"`
	TimeSecond           *uint32  `protobuf:"varint,5,req,name=time_second,json=timeSecond" json:"time_second,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FinishLiveResp) Reset()         { *m = FinishLiveResp{} }
func (m *FinishLiveResp) String() string { return proto.CompactTextString(m) }
func (*FinishLiveResp) ProtoMessage()    {}
func (*FinishLiveResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{130}
}
func (m *FinishLiveResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FinishLiveResp.Unmarshal(m, b)
}
func (m *FinishLiveResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FinishLiveResp.Marshal(b, m, deterministic)
}
func (dst *FinishLiveResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FinishLiveResp.Merge(dst, src)
}
func (m *FinishLiveResp) XXX_Size() int {
	return xxx_messageInfo_FinishLiveResp.Size(m)
}
func (m *FinishLiveResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FinishLiveResp.DiscardUnknown(m)
}

var xxx_messageInfo_FinishLiveResp proto.InternalMessageInfo

func (m *FinishLiveResp) GetUserPv() uint32 {
	if m != nil && m.UserPv != nil {
		return *m.UserPv
	}
	return 0
}

func (m *FinishLiveResp) GetUserUv() uint32 {
	if m != nil && m.UserUv != nil {
		return *m.UserUv
	}
	return 0
}

func (m *FinishLiveResp) GetTcoinGiftCnt() uint32 {
	if m != nil && m.TcoinGiftCnt != nil {
		return *m.TcoinGiftCnt
	}
	return 0
}

func (m *FinishLiveResp) GetTcoinGiftCost() uint32 {
	if m != nil && m.TcoinGiftCost != nil {
		return *m.TcoinGiftCost
	}
	return 0
}

func (m *FinishLiveResp) GetTimeSecond() uint32 {
	if m != nil && m.TimeSecond != nil {
		return *m.TimeSecond
	}
	return 0
}

// 检查是否在直播中// 接口已经废弃
type CheckIsLiveStartingReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIsLiveStartingReq) Reset()         { *m = CheckIsLiveStartingReq{} }
func (m *CheckIsLiveStartingReq) String() string { return proto.CompactTextString(m) }
func (*CheckIsLiveStartingReq) ProtoMessage()    {}
func (*CheckIsLiveStartingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{131}
}
func (m *CheckIsLiveStartingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIsLiveStartingReq.Unmarshal(m, b)
}
func (m *CheckIsLiveStartingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIsLiveStartingReq.Marshal(b, m, deterministic)
}
func (dst *CheckIsLiveStartingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIsLiveStartingReq.Merge(dst, src)
}
func (m *CheckIsLiveStartingReq) XXX_Size() int {
	return xxx_messageInfo_CheckIsLiveStartingReq.Size(m)
}
func (m *CheckIsLiveStartingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIsLiveStartingReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIsLiveStartingReq proto.InternalMessageInfo

func (m *CheckIsLiveStartingReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

// 接口已经废弃
type CheckIsLiveStartingResp struct {
	StartTs              *uint32  `protobuf:"varint,1,req,name=start_ts,json=startTs" json:"start_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIsLiveStartingResp) Reset()         { *m = CheckIsLiveStartingResp{} }
func (m *CheckIsLiveStartingResp) String() string { return proto.CompactTextString(m) }
func (*CheckIsLiveStartingResp) ProtoMessage()    {}
func (*CheckIsLiveStartingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{132}
}
func (m *CheckIsLiveStartingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIsLiveStartingResp.Unmarshal(m, b)
}
func (m *CheckIsLiveStartingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIsLiveStartingResp.Marshal(b, m, deterministic)
}
func (dst *CheckIsLiveStartingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIsLiveStartingResp.Merge(dst, src)
}
func (m *CheckIsLiveStartingResp) XXX_Size() int {
	return xxx_messageInfo_CheckIsLiveStartingResp.Size(m)
}
func (m *CheckIsLiveStartingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIsLiveStartingResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIsLiveStartingResp proto.InternalMessageInfo

func (m *CheckIsLiveStartingResp) GetStartTs() uint32 {
	if m != nil && m.StartTs != nil {
		return *m.StartTs
	}
	return 0
}

// 接口已经废弃
type EnterLiveChannelReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EnterLiveChannelReq) Reset()         { *m = EnterLiveChannelReq{} }
func (m *EnterLiveChannelReq) String() string { return proto.CompactTextString(m) }
func (*EnterLiveChannelReq) ProtoMessage()    {}
func (*EnterLiveChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{133}
}
func (m *EnterLiveChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnterLiveChannelReq.Unmarshal(m, b)
}
func (m *EnterLiveChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnterLiveChannelReq.Marshal(b, m, deterministic)
}
func (dst *EnterLiveChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnterLiveChannelReq.Merge(dst, src)
}
func (m *EnterLiveChannelReq) XXX_Size() int {
	return xxx_messageInfo_EnterLiveChannelReq.Size(m)
}
func (m *EnterLiveChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EnterLiveChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_EnterLiveChannelReq proto.InternalMessageInfo

func (m *EnterLiveChannelReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

// 接口已经废弃
type EnterLiveChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EnterLiveChannelResp) Reset()         { *m = EnterLiveChannelResp{} }
func (m *EnterLiveChannelResp) String() string { return proto.CompactTextString(m) }
func (*EnterLiveChannelResp) ProtoMessage()    {}
func (*EnterLiveChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{134}
}
func (m *EnterLiveChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnterLiveChannelResp.Unmarshal(m, b)
}
func (m *EnterLiveChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnterLiveChannelResp.Marshal(b, m, deterministic)
}
func (dst *EnterLiveChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnterLiveChannelResp.Merge(dst, src)
}
func (m *EnterLiveChannelResp) XXX_Size() int {
	return xxx_messageInfo_EnterLiveChannelResp.Size(m)
}
func (m *EnterLiveChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EnterLiveChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_EnterLiveChannelResp proto.InternalMessageInfo

// 接口已经废弃
type QuitLiveChannelReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	Finish               *bool    `protobuf:"varint,2,opt,name=finish" json:"finish,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QuitLiveChannelReq) Reset()         { *m = QuitLiveChannelReq{} }
func (m *QuitLiveChannelReq) String() string { return proto.CompactTextString(m) }
func (*QuitLiveChannelReq) ProtoMessage()    {}
func (*QuitLiveChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{135}
}
func (m *QuitLiveChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuitLiveChannelReq.Unmarshal(m, b)
}
func (m *QuitLiveChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuitLiveChannelReq.Marshal(b, m, deterministic)
}
func (dst *QuitLiveChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuitLiveChannelReq.Merge(dst, src)
}
func (m *QuitLiveChannelReq) XXX_Size() int {
	return xxx_messageInfo_QuitLiveChannelReq.Size(m)
}
func (m *QuitLiveChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QuitLiveChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_QuitLiveChannelReq proto.InternalMessageInfo

func (m *QuitLiveChannelReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *QuitLiveChannelReq) GetFinish() bool {
	if m != nil && m.Finish != nil {
		return *m.Finish
	}
	return false
}

// 接口已经废弃
type QuitLiveChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QuitLiveChannelResp) Reset()         { *m = QuitLiveChannelResp{} }
func (m *QuitLiveChannelResp) String() string { return proto.CompactTextString(m) }
func (*QuitLiveChannelResp) ProtoMessage()    {}
func (*QuitLiveChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{136}
}
func (m *QuitLiveChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuitLiveChannelResp.Unmarshal(m, b)
}
func (m *QuitLiveChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuitLiveChannelResp.Marshal(b, m, deterministic)
}
func (dst *QuitLiveChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuitLiveChannelResp.Merge(dst, src)
}
func (m *QuitLiveChannelResp) XXX_Size() int {
	return xxx_messageInfo_QuitLiveChannelResp.Size(m)
}
func (m *QuitLiveChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QuitLiveChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_QuitLiveChannelResp proto.InternalMessageInfo

// 申请连麦// 接口已经废弃
type LiveConnectMicApplyReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	Uid                  *uint32  `protobuf:"varint,2,req,name=uid" json:"uid,omitempty"`
	IsCancel             *bool    `protobuf:"varint,3,req,name=is_cancel,json=isCancel" json:"is_cancel,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LiveConnectMicApplyReq) Reset()         { *m = LiveConnectMicApplyReq{} }
func (m *LiveConnectMicApplyReq) String() string { return proto.CompactTextString(m) }
func (*LiveConnectMicApplyReq) ProtoMessage()    {}
func (*LiveConnectMicApplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{137}
}
func (m *LiveConnectMicApplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LiveConnectMicApplyReq.Unmarshal(m, b)
}
func (m *LiveConnectMicApplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LiveConnectMicApplyReq.Marshal(b, m, deterministic)
}
func (dst *LiveConnectMicApplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LiveConnectMicApplyReq.Merge(dst, src)
}
func (m *LiveConnectMicApplyReq) XXX_Size() int {
	return xxx_messageInfo_LiveConnectMicApplyReq.Size(m)
}
func (m *LiveConnectMicApplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LiveConnectMicApplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_LiveConnectMicApplyReq proto.InternalMessageInfo

func (m *LiveConnectMicApplyReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *LiveConnectMicApplyReq) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *LiveConnectMicApplyReq) GetIsCancel() bool {
	if m != nil && m.IsCancel != nil {
		return *m.IsCancel
	}
	return false
}

// 接口已经废弃
type LiveConnectMicApplyResp struct {
	RaminApplyCnt        *uint32  `protobuf:"varint,1,req,name=ramin_apply_cnt,json=raminApplyCnt" json:"ramin_apply_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LiveConnectMicApplyResp) Reset()         { *m = LiveConnectMicApplyResp{} }
func (m *LiveConnectMicApplyResp) String() string { return proto.CompactTextString(m) }
func (*LiveConnectMicApplyResp) ProtoMessage()    {}
func (*LiveConnectMicApplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{138}
}
func (m *LiveConnectMicApplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LiveConnectMicApplyResp.Unmarshal(m, b)
}
func (m *LiveConnectMicApplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LiveConnectMicApplyResp.Marshal(b, m, deterministic)
}
func (dst *LiveConnectMicApplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LiveConnectMicApplyResp.Merge(dst, src)
}
func (m *LiveConnectMicApplyResp) XXX_Size() int {
	return xxx_messageInfo_LiveConnectMicApplyResp.Size(m)
}
func (m *LiveConnectMicApplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LiveConnectMicApplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_LiveConnectMicApplyResp proto.InternalMessageInfo

func (m *LiveConnectMicApplyResp) GetRaminApplyCnt() uint32 {
	if m != nil && m.RaminApplyCnt != nil {
		return *m.RaminApplyCnt
	}
	return 0
}

// 回应连麦// 接口已经废弃
type LiveConnectMicHandleReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	ApplyUidList         []uint32 `protobuf:"varint,2,rep,name=apply_uid_list,json=applyUidList" json:"apply_uid_list,omitempty"`
	OpUid                *uint32  `protobuf:"varint,3,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	IsAllow              *bool    `protobuf:"varint,4,req,name=is_allow,json=isAllow" json:"is_allow,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LiveConnectMicHandleReq) Reset()         { *m = LiveConnectMicHandleReq{} }
func (m *LiveConnectMicHandleReq) String() string { return proto.CompactTextString(m) }
func (*LiveConnectMicHandleReq) ProtoMessage()    {}
func (*LiveConnectMicHandleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{139}
}
func (m *LiveConnectMicHandleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LiveConnectMicHandleReq.Unmarshal(m, b)
}
func (m *LiveConnectMicHandleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LiveConnectMicHandleReq.Marshal(b, m, deterministic)
}
func (dst *LiveConnectMicHandleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LiveConnectMicHandleReq.Merge(dst, src)
}
func (m *LiveConnectMicHandleReq) XXX_Size() int {
	return xxx_messageInfo_LiveConnectMicHandleReq.Size(m)
}
func (m *LiveConnectMicHandleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LiveConnectMicHandleReq.DiscardUnknown(m)
}

var xxx_messageInfo_LiveConnectMicHandleReq proto.InternalMessageInfo

func (m *LiveConnectMicHandleReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *LiveConnectMicHandleReq) GetApplyUidList() []uint32 {
	if m != nil {
		return m.ApplyUidList
	}
	return nil
}

func (m *LiveConnectMicHandleReq) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

func (m *LiveConnectMicHandleReq) GetIsAllow() bool {
	if m != nil && m.IsAllow != nil {
		return *m.IsAllow
	}
	return false
}

// 接口已经废弃
type LiveConnectMicHandleResp struct {
	ServerTimeMs         *uint64          `protobuf:"varint,1,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms,omitempty"`
	SuccessUidList       []uint32         `protobuf:"varint,2,rep,name=success_uid_list,json=successUidList" json:"success_uid_list,omitempty"`
	AllMicList           []*MicrSpaceInfo `protobuf:"bytes,3,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	RaminApplyCnt        *uint32          `protobuf:"varint,4,req,name=ramin_apply_cnt,json=raminApplyCnt" json:"ramin_apply_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *LiveConnectMicHandleResp) Reset()         { *m = LiveConnectMicHandleResp{} }
func (m *LiveConnectMicHandleResp) String() string { return proto.CompactTextString(m) }
func (*LiveConnectMicHandleResp) ProtoMessage()    {}
func (*LiveConnectMicHandleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{140}
}
func (m *LiveConnectMicHandleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LiveConnectMicHandleResp.Unmarshal(m, b)
}
func (m *LiveConnectMicHandleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LiveConnectMicHandleResp.Marshal(b, m, deterministic)
}
func (dst *LiveConnectMicHandleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LiveConnectMicHandleResp.Merge(dst, src)
}
func (m *LiveConnectMicHandleResp) XXX_Size() int {
	return xxx_messageInfo_LiveConnectMicHandleResp.Size(m)
}
func (m *LiveConnectMicHandleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LiveConnectMicHandleResp.DiscardUnknown(m)
}

var xxx_messageInfo_LiveConnectMicHandleResp proto.InternalMessageInfo

func (m *LiveConnectMicHandleResp) GetServerTimeMs() uint64 {
	if m != nil && m.ServerTimeMs != nil {
		return *m.ServerTimeMs
	}
	return 0
}

func (m *LiveConnectMicHandleResp) GetSuccessUidList() []uint32 {
	if m != nil {
		return m.SuccessUidList
	}
	return nil
}

func (m *LiveConnectMicHandleResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *LiveConnectMicHandleResp) GetRaminApplyCnt() uint32 {
	if m != nil && m.RaminApplyCnt != nil {
		return *m.RaminApplyCnt
	}
	return 0
}

// 获取连麦申请人列表// 接口已经废弃
type GetLiveConnectMicApplyUserListReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	LimitCnt             *uint32  `protobuf:"varint,2,opt,name=limit_cnt,json=limitCnt" json:"limit_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveConnectMicApplyUserListReq) Reset()         { *m = GetLiveConnectMicApplyUserListReq{} }
func (m *GetLiveConnectMicApplyUserListReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveConnectMicApplyUserListReq) ProtoMessage()    {}
func (*GetLiveConnectMicApplyUserListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{141}
}
func (m *GetLiveConnectMicApplyUserListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveConnectMicApplyUserListReq.Unmarshal(m, b)
}
func (m *GetLiveConnectMicApplyUserListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveConnectMicApplyUserListReq.Marshal(b, m, deterministic)
}
func (dst *GetLiveConnectMicApplyUserListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveConnectMicApplyUserListReq.Merge(dst, src)
}
func (m *GetLiveConnectMicApplyUserListReq) XXX_Size() int {
	return xxx_messageInfo_GetLiveConnectMicApplyUserListReq.Size(m)
}
func (m *GetLiveConnectMicApplyUserListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveConnectMicApplyUserListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveConnectMicApplyUserListReq proto.InternalMessageInfo

func (m *GetLiveConnectMicApplyUserListReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *GetLiveConnectMicApplyUserListReq) GetLimitCnt() uint32 {
	if m != nil && m.LimitCnt != nil {
		return *m.LimitCnt
	}
	return 0
}

// 接口已经废弃
type GetLiveConnectMicApplyUserListResp struct {
	UidList              []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
	AllApplyCnt          *uint32  `protobuf:"varint,2,opt,name=all_apply_cnt,json=allApplyCnt" json:"all_apply_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveConnectMicApplyUserListResp) Reset()         { *m = GetLiveConnectMicApplyUserListResp{} }
func (m *GetLiveConnectMicApplyUserListResp) String() string { return proto.CompactTextString(m) }
func (*GetLiveConnectMicApplyUserListResp) ProtoMessage()    {}
func (*GetLiveConnectMicApplyUserListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{142}
}
func (m *GetLiveConnectMicApplyUserListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveConnectMicApplyUserListResp.Unmarshal(m, b)
}
func (m *GetLiveConnectMicApplyUserListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveConnectMicApplyUserListResp.Marshal(b, m, deterministic)
}
func (dst *GetLiveConnectMicApplyUserListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveConnectMicApplyUserListResp.Merge(dst, src)
}
func (m *GetLiveConnectMicApplyUserListResp) XXX_Size() int {
	return xxx_messageInfo_GetLiveConnectMicApplyUserListResp.Size(m)
}
func (m *GetLiveConnectMicApplyUserListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveConnectMicApplyUserListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveConnectMicApplyUserListResp proto.InternalMessageInfo

func (m *GetLiveConnectMicApplyUserListResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetLiveConnectMicApplyUserListResp) GetAllApplyCnt() uint32 {
	if m != nil && m.AllApplyCnt != nil {
		return *m.AllApplyCnt
	}
	return 0
}

// 获取直播记录// 接口已经废弃
type GetLiveStatReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveStatReq) Reset()         { *m = GetLiveStatReq{} }
func (m *GetLiveStatReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveStatReq) ProtoMessage()    {}
func (*GetLiveStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{143}
}
func (m *GetLiveStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveStatReq.Unmarshal(m, b)
}
func (m *GetLiveStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveStatReq.Marshal(b, m, deterministic)
}
func (dst *GetLiveStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveStatReq.Merge(dst, src)
}
func (m *GetLiveStatReq) XXX_Size() int {
	return xxx_messageInfo_GetLiveStatReq.Size(m)
}
func (m *GetLiveStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveStatReq proto.InternalMessageInfo

func (m *GetLiveStatReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

// 接口已经废弃
type GetLiveStatResp struct {
	UserPv               *uint32  `protobuf:"varint,1,req,name=user_pv,json=userPv" json:"user_pv,omitempty"`
	UserUv               *uint32  `protobuf:"varint,2,req,name=user_uv,json=userUv" json:"user_uv,omitempty"`
	TcoinGiftCnt         *uint32  `protobuf:"varint,3,req,name=tcoin_gift_cnt,json=tcoinGiftCnt" json:"tcoin_gift_cnt,omitempty"`
	TcoinGiftCost        *uint32  `protobuf:"varint,4,req,name=tcoin_gift_cost,json=tcoinGiftCost" json:"tcoin_gift_cost,omitempty"`
	StartTs              *uint32  `protobuf:"varint,5,req,name=start_ts,json=startTs" json:"start_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveStatResp) Reset()         { *m = GetLiveStatResp{} }
func (m *GetLiveStatResp) String() string { return proto.CompactTextString(m) }
func (*GetLiveStatResp) ProtoMessage()    {}
func (*GetLiveStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{144}
}
func (m *GetLiveStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveStatResp.Unmarshal(m, b)
}
func (m *GetLiveStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveStatResp.Marshal(b, m, deterministic)
}
func (dst *GetLiveStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveStatResp.Merge(dst, src)
}
func (m *GetLiveStatResp) XXX_Size() int {
	return xxx_messageInfo_GetLiveStatResp.Size(m)
}
func (m *GetLiveStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveStatResp proto.InternalMessageInfo

func (m *GetLiveStatResp) GetUserPv() uint32 {
	if m != nil && m.UserPv != nil {
		return *m.UserPv
	}
	return 0
}

func (m *GetLiveStatResp) GetUserUv() uint32 {
	if m != nil && m.UserUv != nil {
		return *m.UserUv
	}
	return 0
}

func (m *GetLiveStatResp) GetTcoinGiftCnt() uint32 {
	if m != nil && m.TcoinGiftCnt != nil {
		return *m.TcoinGiftCnt
	}
	return 0
}

func (m *GetLiveStatResp) GetTcoinGiftCost() uint32 {
	if m != nil && m.TcoinGiftCost != nil {
		return *m.TcoinGiftCost
	}
	return 0
}

func (m *GetLiveStatResp) GetStartTs() uint32 {
	if m != nil && m.StartTs != nil {
		return *m.StartTs
	}
	return 0
}

type CreateChannelLiteReq struct {
	CreateReq            *CreateChannelReq `protobuf:"bytes,1,opt,name=create_req,json=createReq" json:"create_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CreateChannelLiteReq) Reset()         { *m = CreateChannelLiteReq{} }
func (m *CreateChannelLiteReq) String() string { return proto.CompactTextString(m) }
func (*CreateChannelLiteReq) ProtoMessage()    {}
func (*CreateChannelLiteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{145}
}
func (m *CreateChannelLiteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateChannelLiteReq.Unmarshal(m, b)
}
func (m *CreateChannelLiteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateChannelLiteReq.Marshal(b, m, deterministic)
}
func (dst *CreateChannelLiteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateChannelLiteReq.Merge(dst, src)
}
func (m *CreateChannelLiteReq) XXX_Size() int {
	return xxx_messageInfo_CreateChannelLiteReq.Size(m)
}
func (m *CreateChannelLiteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateChannelLiteReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateChannelLiteReq proto.InternalMessageInfo

func (m *CreateChannelLiteReq) GetCreateReq() *CreateChannelReq {
	if m != nil {
		return m.CreateReq
	}
	return nil
}

type CreateChannelLiteResp struct {
	CreateResp           *CreateChannelResp `protobuf:"bytes,1,opt,name=create_resp,json=createResp" json:"create_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CreateChannelLiteResp) Reset()         { *m = CreateChannelLiteResp{} }
func (m *CreateChannelLiteResp) String() string { return proto.CompactTextString(m) }
func (*CreateChannelLiteResp) ProtoMessage()    {}
func (*CreateChannelLiteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{146}
}
func (m *CreateChannelLiteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateChannelLiteResp.Unmarshal(m, b)
}
func (m *CreateChannelLiteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateChannelLiteResp.Marshal(b, m, deterministic)
}
func (dst *CreateChannelLiteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateChannelLiteResp.Merge(dst, src)
}
func (m *CreateChannelLiteResp) XXX_Size() int {
	return xxx_messageInfo_CreateChannelLiteResp.Size(m)
}
func (m *CreateChannelLiteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateChannelLiteResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateChannelLiteResp proto.InternalMessageInfo

func (m *CreateChannelLiteResp) GetCreateResp() *CreateChannelResp {
	if m != nil {
		return m.CreateResp
	}
	return nil
}

// 批量获取频道类型
type BatchGetChannelBindTypeReq struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelBindTypeReq) Reset()         { *m = BatchGetChannelBindTypeReq{} }
func (m *BatchGetChannelBindTypeReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelBindTypeReq) ProtoMessage()    {}
func (*BatchGetChannelBindTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{147}
}
func (m *BatchGetChannelBindTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelBindTypeReq.Unmarshal(m, b)
}
func (m *BatchGetChannelBindTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelBindTypeReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelBindTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelBindTypeReq.Merge(dst, src)
}
func (m *BatchGetChannelBindTypeReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelBindTypeReq.Size(m)
}
func (m *BatchGetChannelBindTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelBindTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelBindTypeReq proto.InternalMessageInfo

func (m *BatchGetChannelBindTypeReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type BatchGetChannelBindTypeResp struct {
	ChannelBindTypeList  []uint32 `protobuf:"varint,1,rep,name=channel_bind_type_list,json=channelBindTypeList" json:"channel_bind_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelBindTypeResp) Reset()         { *m = BatchGetChannelBindTypeResp{} }
func (m *BatchGetChannelBindTypeResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelBindTypeResp) ProtoMessage()    {}
func (*BatchGetChannelBindTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{148}
}
func (m *BatchGetChannelBindTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelBindTypeResp.Unmarshal(m, b)
}
func (m *BatchGetChannelBindTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelBindTypeResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelBindTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelBindTypeResp.Merge(dst, src)
}
func (m *BatchGetChannelBindTypeResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelBindTypeResp.Size(m)
}
func (m *BatchGetChannelBindTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelBindTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelBindTypeResp proto.InternalMessageInfo

func (m *BatchGetChannelBindTypeResp) GetChannelBindTypeList() []uint32 {
	if m != nil {
		return m.ChannelBindTypeList
	}
	return nil
}

type RefixChannelOwnerReq struct {
	Uid                  *uint32  `protobuf:"varint,1,req,name=uid" json:"uid,omitempty"`
	ChannelId            *uint32  `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefixChannelOwnerReq) Reset()         { *m = RefixChannelOwnerReq{} }
func (m *RefixChannelOwnerReq) String() string { return proto.CompactTextString(m) }
func (*RefixChannelOwnerReq) ProtoMessage()    {}
func (*RefixChannelOwnerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{149}
}
func (m *RefixChannelOwnerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefixChannelOwnerReq.Unmarshal(m, b)
}
func (m *RefixChannelOwnerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefixChannelOwnerReq.Marshal(b, m, deterministic)
}
func (dst *RefixChannelOwnerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefixChannelOwnerReq.Merge(dst, src)
}
func (m *RefixChannelOwnerReq) XXX_Size() int {
	return xxx_messageInfo_RefixChannelOwnerReq.Size(m)
}
func (m *RefixChannelOwnerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RefixChannelOwnerReq.DiscardUnknown(m)
}

var xxx_messageInfo_RefixChannelOwnerReq proto.InternalMessageInfo

func (m *RefixChannelOwnerReq) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *RefixChannelOwnerReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

type RefixChannelOwnerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefixChannelOwnerResp) Reset()         { *m = RefixChannelOwnerResp{} }
func (m *RefixChannelOwnerResp) String() string { return proto.CompactTextString(m) }
func (*RefixChannelOwnerResp) ProtoMessage()    {}
func (*RefixChannelOwnerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{150}
}
func (m *RefixChannelOwnerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefixChannelOwnerResp.Unmarshal(m, b)
}
func (m *RefixChannelOwnerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefixChannelOwnerResp.Marshal(b, m, deterministic)
}
func (dst *RefixChannelOwnerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefixChannelOwnerResp.Merge(dst, src)
}
func (m *RefixChannelOwnerResp) XXX_Size() int {
	return xxx_messageInfo_RefixChannelOwnerResp.Size(m)
}
func (m *RefixChannelOwnerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RefixChannelOwnerResp.DiscardUnknown(m)
}

var xxx_messageInfo_RefixChannelOwnerResp proto.InternalMessageInfo

type ChackAndRefixChannelOwnerReq struct {
	ContextId            *uint32  `protobuf:"varint,1,req,name=context_id,json=contextId" json:"context_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChackAndRefixChannelOwnerReq) Reset()         { *m = ChackAndRefixChannelOwnerReq{} }
func (m *ChackAndRefixChannelOwnerReq) String() string { return proto.CompactTextString(m) }
func (*ChackAndRefixChannelOwnerReq) ProtoMessage()    {}
func (*ChackAndRefixChannelOwnerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{151}
}
func (m *ChackAndRefixChannelOwnerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChackAndRefixChannelOwnerReq.Unmarshal(m, b)
}
func (m *ChackAndRefixChannelOwnerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChackAndRefixChannelOwnerReq.Marshal(b, m, deterministic)
}
func (dst *ChackAndRefixChannelOwnerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChackAndRefixChannelOwnerReq.Merge(dst, src)
}
func (m *ChackAndRefixChannelOwnerReq) XXX_Size() int {
	return xxx_messageInfo_ChackAndRefixChannelOwnerReq.Size(m)
}
func (m *ChackAndRefixChannelOwnerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChackAndRefixChannelOwnerReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChackAndRefixChannelOwnerReq proto.InternalMessageInfo

func (m *ChackAndRefixChannelOwnerReq) GetContextId() uint32 {
	if m != nil && m.ContextId != nil {
		return *m.ContextId
	}
	return 0
}

type ChackAndRefixChannelOwnerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChackAndRefixChannelOwnerResp) Reset()         { *m = ChackAndRefixChannelOwnerResp{} }
func (m *ChackAndRefixChannelOwnerResp) String() string { return proto.CompactTextString(m) }
func (*ChackAndRefixChannelOwnerResp) ProtoMessage()    {}
func (*ChackAndRefixChannelOwnerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{152}
}
func (m *ChackAndRefixChannelOwnerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChackAndRefixChannelOwnerResp.Unmarshal(m, b)
}
func (m *ChackAndRefixChannelOwnerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChackAndRefixChannelOwnerResp.Marshal(b, m, deterministic)
}
func (dst *ChackAndRefixChannelOwnerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChackAndRefixChannelOwnerResp.Merge(dst, src)
}
func (m *ChackAndRefixChannelOwnerResp) XXX_Size() int {
	return xxx_messageInfo_ChackAndRefixChannelOwnerResp.Size(m)
}
func (m *ChackAndRefixChannelOwnerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChackAndRefixChannelOwnerResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChackAndRefixChannelOwnerResp proto.InternalMessageInfo

// 白名单
type AddChannelToWhiteListReq struct {
	ChannelList          []uint32 `protobuf:"varint,1,rep,name=channel_list,json=channelList" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelToWhiteListReq) Reset()         { *m = AddChannelToWhiteListReq{} }
func (m *AddChannelToWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*AddChannelToWhiteListReq) ProtoMessage()    {}
func (*AddChannelToWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{153}
}
func (m *AddChannelToWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelToWhiteListReq.Unmarshal(m, b)
}
func (m *AddChannelToWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelToWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *AddChannelToWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelToWhiteListReq.Merge(dst, src)
}
func (m *AddChannelToWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_AddChannelToWhiteListReq.Size(m)
}
func (m *AddChannelToWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelToWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelToWhiteListReq proto.InternalMessageInfo

func (m *AddChannelToWhiteListReq) GetChannelList() []uint32 {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type AddChannelToWhiteListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelToWhiteListResp) Reset()         { *m = AddChannelToWhiteListResp{} }
func (m *AddChannelToWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*AddChannelToWhiteListResp) ProtoMessage()    {}
func (*AddChannelToWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{154}
}
func (m *AddChannelToWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelToWhiteListResp.Unmarshal(m, b)
}
func (m *AddChannelToWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelToWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *AddChannelToWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelToWhiteListResp.Merge(dst, src)
}
func (m *AddChannelToWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_AddChannelToWhiteListResp.Size(m)
}
func (m *AddChannelToWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelToWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelToWhiteListResp proto.InternalMessageInfo

type RemoveFromWhiteListReq struct {
	ChannelList          []uint32 `protobuf:"varint,1,rep,name=channel_list,json=channelList" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveFromWhiteListReq) Reset()         { *m = RemoveFromWhiteListReq{} }
func (m *RemoveFromWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*RemoveFromWhiteListReq) ProtoMessage()    {}
func (*RemoveFromWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{155}
}
func (m *RemoveFromWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveFromWhiteListReq.Unmarshal(m, b)
}
func (m *RemoveFromWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveFromWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *RemoveFromWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveFromWhiteListReq.Merge(dst, src)
}
func (m *RemoveFromWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_RemoveFromWhiteListReq.Size(m)
}
func (m *RemoveFromWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveFromWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveFromWhiteListReq proto.InternalMessageInfo

func (m *RemoveFromWhiteListReq) GetChannelList() []uint32 {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type RemoveFromWhiteListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveFromWhiteListResp) Reset()         { *m = RemoveFromWhiteListResp{} }
func (m *RemoveFromWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*RemoveFromWhiteListResp) ProtoMessage()    {}
func (*RemoveFromWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{156}
}
func (m *RemoveFromWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveFromWhiteListResp.Unmarshal(m, b)
}
func (m *RemoveFromWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveFromWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *RemoveFromWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveFromWhiteListResp.Merge(dst, src)
}
func (m *RemoveFromWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_RemoveFromWhiteListResp.Size(m)
}
func (m *RemoveFromWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveFromWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveFromWhiteListResp proto.InternalMessageInfo

type GetChannelWhiteListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelWhiteListReq) Reset()         { *m = GetChannelWhiteListReq{} }
func (m *GetChannelWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelWhiteListReq) ProtoMessage()    {}
func (*GetChannelWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{157}
}
func (m *GetChannelWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelWhiteListReq.Unmarshal(m, b)
}
func (m *GetChannelWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelWhiteListReq.Merge(dst, src)
}
func (m *GetChannelWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelWhiteListReq.Size(m)
}
func (m *GetChannelWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelWhiteListReq proto.InternalMessageInfo

type GetChannelWhiteListResp struct {
	ChannelList          []uint32 `protobuf:"varint,1,rep,name=channel_list,json=channelList" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelWhiteListResp) Reset()         { *m = GetChannelWhiteListResp{} }
func (m *GetChannelWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelWhiteListResp) ProtoMessage()    {}
func (*GetChannelWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{158}
}
func (m *GetChannelWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelWhiteListResp.Unmarshal(m, b)
}
func (m *GetChannelWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelWhiteListResp.Merge(dst, src)
}
func (m *GetChannelWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelWhiteListResp.Size(m)
}
func (m *GetChannelWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelWhiteListResp proto.InternalMessageInfo

func (m *GetChannelWhiteListResp) GetChannelList() []uint32 {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type ChangeChannelTypeReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	ChannelType          *uint32  `protobuf:"varint,2,req,name=channel_type,json=channelType" json:"channel_type,omitempty"`
	BindId               *uint32  `protobuf:"varint,3,req,name=bind_id,json=bindId" json:"bind_id,omitempty"`
	CreateUid            *uint32  `protobuf:"varint,4,req,name=create_uid,json=createUid" json:"create_uid,omitempty"`
	IsNeedCleanAdmin     *bool    `protobuf:"varint,5,opt,name=is_need_clean_admin,json=isNeedCleanAdmin" json:"is_need_clean_admin,omitempty"`
	MagicId              *uint32  `protobuf:"varint,6,req,name=magic_id,json=magicId" json:"magic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeChannelTypeReq) Reset()         { *m = ChangeChannelTypeReq{} }
func (m *ChangeChannelTypeReq) String() string { return proto.CompactTextString(m) }
func (*ChangeChannelTypeReq) ProtoMessage()    {}
func (*ChangeChannelTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{159}
}
func (m *ChangeChannelTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeChannelTypeReq.Unmarshal(m, b)
}
func (m *ChangeChannelTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeChannelTypeReq.Marshal(b, m, deterministic)
}
func (dst *ChangeChannelTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeChannelTypeReq.Merge(dst, src)
}
func (m *ChangeChannelTypeReq) XXX_Size() int {
	return xxx_messageInfo_ChangeChannelTypeReq.Size(m)
}
func (m *ChangeChannelTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeChannelTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeChannelTypeReq proto.InternalMessageInfo

func (m *ChangeChannelTypeReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *ChangeChannelTypeReq) GetChannelType() uint32 {
	if m != nil && m.ChannelType != nil {
		return *m.ChannelType
	}
	return 0
}

func (m *ChangeChannelTypeReq) GetBindId() uint32 {
	if m != nil && m.BindId != nil {
		return *m.BindId
	}
	return 0
}

func (m *ChangeChannelTypeReq) GetCreateUid() uint32 {
	if m != nil && m.CreateUid != nil {
		return *m.CreateUid
	}
	return 0
}

func (m *ChangeChannelTypeReq) GetIsNeedCleanAdmin() bool {
	if m != nil && m.IsNeedCleanAdmin != nil {
		return *m.IsNeedCleanAdmin
	}
	return false
}

func (m *ChangeChannelTypeReq) GetMagicId() uint32 {
	if m != nil && m.MagicId != nil {
		return *m.MagicId
	}
	return 0
}

type ChangeChannelTypeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeChannelTypeResp) Reset()         { *m = ChangeChannelTypeResp{} }
func (m *ChangeChannelTypeResp) String() string { return proto.CompactTextString(m) }
func (*ChangeChannelTypeResp) ProtoMessage()    {}
func (*ChangeChannelTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{160}
}
func (m *ChangeChannelTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeChannelTypeResp.Unmarshal(m, b)
}
func (m *ChangeChannelTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeChannelTypeResp.Marshal(b, m, deterministic)
}
func (dst *ChangeChannelTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeChannelTypeResp.Merge(dst, src)
}
func (m *ChangeChannelTypeResp) XXX_Size() int {
	return xxx_messageInfo_ChangeChannelTypeResp.Size(m)
}
func (m *ChangeChannelTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeChannelTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeChannelTypeResp proto.InternalMessageInfo

type ChangeChannelViewIdReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	NewChannelViewId     *string  `protobuf:"bytes,2,req,name=new_channel_view_id,json=newChannelViewId" json:"new_channel_view_id,omitempty"`
	MagicId              *uint32  `protobuf:"varint,3,req,name=magic_id,json=magicId" json:"magic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeChannelViewIdReq) Reset()         { *m = ChangeChannelViewIdReq{} }
func (m *ChangeChannelViewIdReq) String() string { return proto.CompactTextString(m) }
func (*ChangeChannelViewIdReq) ProtoMessage()    {}
func (*ChangeChannelViewIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{161}
}
func (m *ChangeChannelViewIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeChannelViewIdReq.Unmarshal(m, b)
}
func (m *ChangeChannelViewIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeChannelViewIdReq.Marshal(b, m, deterministic)
}
func (dst *ChangeChannelViewIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeChannelViewIdReq.Merge(dst, src)
}
func (m *ChangeChannelViewIdReq) XXX_Size() int {
	return xxx_messageInfo_ChangeChannelViewIdReq.Size(m)
}
func (m *ChangeChannelViewIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeChannelViewIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeChannelViewIdReq proto.InternalMessageInfo

func (m *ChangeChannelViewIdReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *ChangeChannelViewIdReq) GetNewChannelViewId() string {
	if m != nil && m.NewChannelViewId != nil {
		return *m.NewChannelViewId
	}
	return ""
}

func (m *ChangeChannelViewIdReq) GetMagicId() uint32 {
	if m != nil && m.MagicId != nil {
		return *m.MagicId
	}
	return 0
}

type ChangeChannelViewIdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeChannelViewIdResp) Reset()         { *m = ChangeChannelViewIdResp{} }
func (m *ChangeChannelViewIdResp) String() string { return proto.CompactTextString(m) }
func (*ChangeChannelViewIdResp) ProtoMessage()    {}
func (*ChangeChannelViewIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_679d35ab756dc52b, []int{162}
}
func (m *ChangeChannelViewIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeChannelViewIdResp.Unmarshal(m, b)
}
func (m *ChangeChannelViewIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeChannelViewIdResp.Marshal(b, m, deterministic)
}
func (dst *ChangeChannelViewIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeChannelViewIdResp.Merge(dst, src)
}
func (m *ChangeChannelViewIdResp) XXX_Size() int {
	return xxx_messageInfo_ChangeChannelViewIdResp.Size(m)
}
func (m *ChangeChannelViewIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeChannelViewIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeChannelViewIdResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*ChannelMemberRole)(nil), "Channel.ChannelMemberRole")
	proto.RegisterType((*ChannelMemberBaseInfo)(nil), "Channel.ChannelMemberBaseInfo")
	proto.RegisterType((*ChannelMemberDetailInfo)(nil), "Channel.ChannelMemberDetailInfo")
	proto.RegisterType((*MicrSpaceInfo)(nil), "Channel.MicrSpaceInfo")
	proto.RegisterType((*ChannelSimpleInfo)(nil), "Channel.ChannelSimpleInfo")
	proto.RegisterType((*ChannelBaseInfo)(nil), "Channel.ChannelBaseInfo")
	proto.RegisterType((*ChannelDetailInfo)(nil), "Channel.ChannelDetailInfo")
	proto.RegisterType((*CreateChannelReq)(nil), "Channel.CreateChannelReq")
	proto.RegisterType((*CreateChannelResp)(nil), "Channel.CreateChannelResp")
	proto.RegisterType((*DissolveChannelReq)(nil), "Channel.DissolveChannelReq")
	proto.RegisterType((*ModifyChannelReq)(nil), "Channel.ModifyChannelReq")
	proto.RegisterType((*ModifyChannelResp)(nil), "Channel.ModifyChannelResp")
	proto.RegisterType((*ModifyChannelEnterControlTypeReq)(nil), "Channel.ModifyChannelEnterControlTypeReq")
	proto.RegisterType((*ModifyChannelEnterControlTypeResp)(nil), "Channel.ModifyChannelEnterControlTypeResp")
	proto.RegisterType((*HoldMicrSpaceReq)(nil), "Channel.HoldMicrSpaceReq")
	proto.RegisterType((*HoldMicrSpaceResp)(nil), "Channel.HoldMicrSpaceResp")
	proto.RegisterType((*ReleaseMicrSpaceReq)(nil), "Channel.ReleaseMicrSpaceReq")
	proto.RegisterType((*ReleaseMicrSpaceResp)(nil), "Channel.ReleaseMicrSpaceResp")
	proto.RegisterType((*CheckAndKickChannelMicrByBindIdReq)(nil), "Channel.CheckAndKickChannelMicrByBindIdReq")
	proto.RegisterType((*CheckAndKickChannelMicrByBindIdResp)(nil), "Channel.CheckAndKickChannelMicrByBindIdResp")
	proto.RegisterType((*SetChannelMuteAndKickMicReq)(nil), "Channel.SetChannelMuteAndKickMicReq")
	proto.RegisterType((*SetChannelMuteAndKickMicResp)(nil), "Channel.SetChannelMuteAndKickMicResp")
	proto.RegisterType((*UnsetChannelMuteReq)(nil), "Channel.UnsetChannelMuteReq")
	proto.RegisterType((*MuteChannelMemberReq)(nil), "Channel.MuteChannelMemberReq")
	proto.RegisterType((*MuteChannelMemberResp)(nil), "Channel.MuteChannelMemberResp")
	proto.RegisterType((*UnmuteChannelMemberReq)(nil), "Channel.UnmuteChannelMemberReq")
	proto.RegisterType((*UnmuteChannelMemberResp)(nil), "Channel.UnmuteChannelMemberResp")
	proto.RegisterType((*GetChannelDetailInfoReq)(nil), "Channel.GetChannelDetailInfoReq")
	proto.RegisterType((*GetChannelDetailInfoResp)(nil), "Channel.GetChannelDetailInfoResp")
	proto.RegisterType((*GetChannelDetailInfoBatchReq)(nil), "Channel.GetChannelDetailInfoBatchReq")
	proto.RegisterType((*GetChannelDetailInfoBatchResp)(nil), "Channel.GetChannelDetailInfoBatchResp")
	proto.RegisterType((*GetChannelSimpleInfoReq)(nil), "Channel.GetChannelSimpleInfoReq")
	proto.RegisterType((*GetChannelSimpleInfoResp)(nil), "Channel.GetChannelSimpleInfoResp")
	proto.RegisterType((*BatchGetChannelSimpleInfoReq)(nil), "Channel.BatchGetChannelSimpleInfoReq")
	proto.RegisterType((*BatchGetChannelSimpleInfoResp)(nil), "Channel.BatchGetChannelSimpleInfoResp")
	proto.RegisterType((*GetChannelMemberSizeReq)(nil), "Channel.GetChannelMemberSizeReq")
	proto.RegisterType((*GetChannelMemberSizeResp)(nil), "Channel.GetChannelMemberSizeResp")
	proto.RegisterType((*GetChannelMuteListReq)(nil), "Channel.GetChannelMuteListReq")
	proto.RegisterType((*GetChannelMuteListResp)(nil), "Channel.GetChannelMuteListResp")
	proto.RegisterType((*GetMicrListReq)(nil), "Channel.GetMicrListReq")
	proto.RegisterType((*GetMicrListResp)(nil), "Channel.GetMicrListResp")
	proto.RegisterType((*GetChannelListByBindIdReq)(nil), "Channel.GetChannelListByBindIdReq")
	proto.RegisterType((*GetChannelListByBindIdResp)(nil), "Channel.GetChannelListByBindIdResp")
	proto.RegisterType((*GetChannelBindInfoReq)(nil), "Channel.GetChannelBindInfoReq")
	proto.RegisterType((*GetChannelBindInfoResp)(nil), "Channel.GetChannelBindInfoResp")
	proto.RegisterType((*UserChannelID)(nil), "Channel.UserChannelID")
	proto.RegisterType((*CheckUserIsMuteReq)(nil), "Channel.CheckUserIsMuteReq")
	proto.RegisterType((*CheckUserIsMuteResp)(nil), "Channel.CheckUserIsMuteResp")
	proto.RegisterType((*User2ChannelRelation)(nil), "Channel.User2ChannelRelation")
	proto.RegisterType((*KickUserOutOfGuildChannelReq)(nil), "Channel.KickUserOutOfGuildChannelReq")
	proto.RegisterType((*KickUserOutOfGuildChannelResp)(nil), "Channel.KickUserOutOfGuildChannelResp")
	proto.RegisterType((*DisableChannelMicEntryReq)(nil), "Channel.DisableChannelMicEntryReq")
	proto.RegisterType((*DisableChannelMicEntryResp)(nil), "Channel.DisableChannelMicEntryResp")
	proto.RegisterType((*EnableChannelMicEntryReq)(nil), "Channel.EnableChannelMicEntryReq")
	proto.RegisterType((*EnableChannelMicEntryResp)(nil), "Channel.EnableChannelMicEntryResp")
	proto.RegisterType((*KickoutChannelMicReq)(nil), "Channel.KickoutChannelMicReq")
	proto.RegisterType((*KickoutChannelMicResp)(nil), "Channel.KickoutChannelMicResp")
	proto.RegisterType((*KickoutChannelMemberReq)(nil), "Channel.KickoutChannelMemberReq")
	proto.RegisterType((*KickoutChannelMemberResp)(nil), "Channel.KickoutChannelMemberResp")
	proto.RegisterType((*CheckUserKickoutFromChannelReq)(nil), "Channel.CheckUserKickoutFromChannelReq")
	proto.RegisterType((*CheckUserKickoutFromChannelResp)(nil), "Channel.CheckUserKickoutFromChannelResp")
	proto.RegisterType((*KickoutChannelMemberLiteReq)(nil), "Channel.KickoutChannelMemberLiteReq")
	proto.RegisterType((*KickoutChannelMemberLiteResp)(nil), "Channel.KickoutChannelMemberLiteResp")
	proto.RegisterType((*GetChannelByDisplayIdReq)(nil), "Channel.GetChannelByDisplayIdReq")
	proto.RegisterType((*GetChannelByDisplayIdResp)(nil), "Channel.GetChannelByDisplayIdResp")
	proto.RegisterType((*UserChannelRoleInfo)(nil), "Channel.UserChannelRoleInfo")
	proto.RegisterType((*GetUserChannelRoleListReq)(nil), "Channel.GetUserChannelRoleListReq")
	proto.RegisterType((*GetUserChannelRoleListResp)(nil), "Channel.GetUserChannelRoleListResp")
	proto.RegisterType((*SetChannelMicModelReq)(nil), "Channel.SetChannelMicModelReq")
	proto.RegisterType((*SetChannelMicModelResp)(nil), "Channel.SetChannelMicModelResp")
	proto.RegisterType((*CreateChannelByDisplayIDReq)(nil), "Channel.CreateChannelByDisplayIDReq")
	proto.RegisterType((*CreateChannelByDisplayIDResp)(nil), "Channel.CreateChannelByDisplayIDResp")
	proto.RegisterType((*AllocTempChannelReq)(nil), "Channel.AllocTempChannelReq")
	proto.RegisterType((*AllocTempChannelResp)(nil), "Channel.AllocTempChannelResp")
	proto.RegisterType((*ReleaseTempChannelReq)(nil), "Channel.ReleaseTempChannelReq")
	proto.RegisterType((*ReleaseTempChannelResp)(nil), "Channel.ReleaseTempChannelResp")
	proto.RegisterType((*BatchAllocTempChannelReq)(nil), "Channel.BatchAllocTempChannelReq")
	proto.RegisterType((*BatchAllocTempChannelResp)(nil), "Channel.BatchAllocTempChannelResp")
	proto.RegisterType((*BatchReleaseTempChannelReq)(nil), "Channel.BatchReleaseTempChannelReq")
	proto.RegisterType((*BatchReleaseTempChannelResp)(nil), "Channel.BatchReleaseTempChannelResp")
	proto.RegisterType((*QueryTempChannelsNumReq)(nil), "Channel.QueryTempChannelsNumReq")
	proto.RegisterType((*QueryTempChannelsNumResp)(nil), "Channel.QueryTempChannelsNumResp")
	proto.RegisterType((*CheckTmpChannelIsAllocedReq)(nil), "Channel.CheckTmpChannelIsAllocedReq")
	proto.RegisterType((*CheckTmpChannelIsAllocedResp)(nil), "Channel.CheckTmpChannelIsAllocedResp")
	proto.RegisterType((*BatchCheckIsTempAllocChannelReq)(nil), "Channel.BatchCheckIsTempAllocChannelReq")
	proto.RegisterType((*BatchCheckIsTempAllocChannelResp)(nil), "Channel.BatchCheckIsTempAllocChannelResp")
	proto.RegisterType((*ConfigMusicInfo)(nil), "Channel.ConfigMusicInfo")
	proto.RegisterType((*AddConfigMusicReq)(nil), "Channel.AddConfigMusicReq")
	proto.RegisterType((*AddConfigMusicResp)(nil), "Channel.AddConfigMusicResp")
	proto.RegisterType((*DelConfigMusicReq)(nil), "Channel.DelConfigMusicReq")
	proto.RegisterType((*DelConfigMusicResp)(nil), "Channel.DelConfigMusicResp")
	proto.RegisterType((*GetConfigMusicListReq)(nil), "Channel.GetConfigMusicListReq")
	proto.RegisterType((*GetConfigMusicListResp)(nil), "Channel.GetConfigMusicListResp")
	proto.RegisterType((*GetChannelCreateTsReq)(nil), "Channel.GetChannelCreateTsReq")
	proto.RegisterType((*GetChannelCreateTsResp)(nil), "Channel.GetChannelCreateTsResp")
	proto.RegisterType((*RecordSendGiftEventReq)(nil), "Channel.RecordSendGiftEventReq")
	proto.RegisterType((*RecordSendGiftEventResp)(nil), "Channel.RecordSendGiftEventResp")
	proto.RegisterType((*MemberConsumeInfo)(nil), "Channel.MemberConsumeInfo")
	proto.RegisterType((*GetConsumeTopNReq)(nil), "Channel.GetConsumeTopNReq")
	proto.RegisterType((*GetConsumeTopNResp)(nil), "Channel.GetConsumeTopNResp")
	proto.RegisterType((*GetUserAccumulateConsumeReq)(nil), "Channel.GetUserAccumulateConsumeReq")
	proto.RegisterType((*GetUserAccumulateConsumeResp)(nil), "Channel.GetUserAccumulateConsumeResp")
	proto.RegisterType((*ChannelGiftStat)(nil), "Channel.ChannelGiftStat")
	proto.RegisterType((*ChangeDisplayIDReq)(nil), "Channel.ChangeDisplayIDReq")
	proto.RegisterType((*ChangeDisplayIDResp)(nil), "Channel.ChangeDisplayIDResp")
	proto.RegisterType((*ChannelHistory)(nil), "Channel.ChannelHistory")
	proto.RegisterType((*GetChannelHistoryListReq)(nil), "Channel.GetChannelHistoryListReq")
	proto.RegisterType((*GetChannelHistoryListResp)(nil), "Channel.GetChannelHistoryListResp")
	proto.RegisterType((*GetChannelStatReq)(nil), "Channel.GetChannelStatReq")
	proto.RegisterType((*GetChannelStatResp)(nil), "Channel.GetChannelStatResp")
	proto.RegisterType((*ChannelAdmin)(nil), "Channel.ChannelAdmin")
	proto.RegisterType((*GetChannelAdminReq)(nil), "Channel.GetChannelAdminReq")
	proto.RegisterType((*GetChannelAdminResp)(nil), "Channel.GetChannelAdminResp")
	proto.RegisterType((*AddChannelAdminReq)(nil), "Channel.AddChannelAdminReq")
	proto.RegisterType((*AddChannelAdminResp)(nil), "Channel.AddChannelAdminResp")
	proto.RegisterType((*RemoveChannelAdminReq)(nil), "Channel.RemoveChannelAdminReq")
	proto.RegisterType((*RemoveChannelAdminResp)(nil), "Channel.RemoveChannelAdminResp")
	proto.RegisterType((*SetChannelMicSpaceStatusReq)(nil), "Channel.SetChannelMicSpaceStatusReq")
	proto.RegisterType((*SetChannelMicSpaceStatusResp)(nil), "Channel.SetChannelMicSpaceStatusResp")
	proto.RegisterType((*ChangeMicrophoneReq)(nil), "Channel.ChangeMicrophoneReq")
	proto.RegisterType((*ChangeMicrophoneResp)(nil), "Channel.ChangeMicrophoneResp")
	proto.RegisterType((*GetChannelTopicDetailReq)(nil), "Channel.GetChannelTopicDetailReq")
	proto.RegisterType((*GetChannelTopicDetailResp)(nil), "Channel.GetChannelTopicDetailResp")
	proto.RegisterType((*GetChannelWelcomeMsgReq)(nil), "Channel.GetChannelWelcomeMsgReq")
	proto.RegisterType((*GetChannelWelcomeMsgResp)(nil), "Channel.GetChannelWelcomeMsgResp")
	proto.RegisterType((*DisableAllEmptyMicrSpaceReq)(nil), "Channel.DisableAllEmptyMicrSpaceReq")
	proto.RegisterType((*DisableAllEmptyMicrSpaceResp)(nil), "Channel.DisableAllEmptyMicrSpaceResp")
	proto.RegisterType((*StartLiveReq)(nil), "Channel.StartLiveReq")
	proto.RegisterType((*StartLiveResp)(nil), "Channel.StartLiveResp")
	proto.RegisterType((*FinishLiveReq)(nil), "Channel.FinishLiveReq")
	proto.RegisterType((*FinishLiveResp)(nil), "Channel.FinishLiveResp")
	proto.RegisterType((*CheckIsLiveStartingReq)(nil), "Channel.CheckIsLiveStartingReq")
	proto.RegisterType((*CheckIsLiveStartingResp)(nil), "Channel.CheckIsLiveStartingResp")
	proto.RegisterType((*EnterLiveChannelReq)(nil), "Channel.EnterLiveChannelReq")
	proto.RegisterType((*EnterLiveChannelResp)(nil), "Channel.EnterLiveChannelResp")
	proto.RegisterType((*QuitLiveChannelReq)(nil), "Channel.QuitLiveChannelReq")
	proto.RegisterType((*QuitLiveChannelResp)(nil), "Channel.QuitLiveChannelResp")
	proto.RegisterType((*LiveConnectMicApplyReq)(nil), "Channel.LiveConnectMicApplyReq")
	proto.RegisterType((*LiveConnectMicApplyResp)(nil), "Channel.LiveConnectMicApplyResp")
	proto.RegisterType((*LiveConnectMicHandleReq)(nil), "Channel.LiveConnectMicHandleReq")
	proto.RegisterType((*LiveConnectMicHandleResp)(nil), "Channel.LiveConnectMicHandleResp")
	proto.RegisterType((*GetLiveConnectMicApplyUserListReq)(nil), "Channel.GetLiveConnectMicApplyUserListReq")
	proto.RegisterType((*GetLiveConnectMicApplyUserListResp)(nil), "Channel.GetLiveConnectMicApplyUserListResp")
	proto.RegisterType((*GetLiveStatReq)(nil), "Channel.GetLiveStatReq")
	proto.RegisterType((*GetLiveStatResp)(nil), "Channel.GetLiveStatResp")
	proto.RegisterType((*CreateChannelLiteReq)(nil), "Channel.CreateChannelLiteReq")
	proto.RegisterType((*CreateChannelLiteResp)(nil), "Channel.CreateChannelLiteResp")
	proto.RegisterType((*BatchGetChannelBindTypeReq)(nil), "Channel.BatchGetChannelBindTypeReq")
	proto.RegisterType((*BatchGetChannelBindTypeResp)(nil), "Channel.BatchGetChannelBindTypeResp")
	proto.RegisterType((*RefixChannelOwnerReq)(nil), "Channel.RefixChannelOwnerReq")
	proto.RegisterType((*RefixChannelOwnerResp)(nil), "Channel.RefixChannelOwnerResp")
	proto.RegisterType((*ChackAndRefixChannelOwnerReq)(nil), "Channel.ChackAndRefixChannelOwnerReq")
	proto.RegisterType((*ChackAndRefixChannelOwnerResp)(nil), "Channel.ChackAndRefixChannelOwnerResp")
	proto.RegisterType((*AddChannelToWhiteListReq)(nil), "Channel.AddChannelToWhiteListReq")
	proto.RegisterType((*AddChannelToWhiteListResp)(nil), "Channel.AddChannelToWhiteListResp")
	proto.RegisterType((*RemoveFromWhiteListReq)(nil), "Channel.RemoveFromWhiteListReq")
	proto.RegisterType((*RemoveFromWhiteListResp)(nil), "Channel.RemoveFromWhiteListResp")
	proto.RegisterType((*GetChannelWhiteListReq)(nil), "Channel.GetChannelWhiteListReq")
	proto.RegisterType((*GetChannelWhiteListResp)(nil), "Channel.GetChannelWhiteListResp")
	proto.RegisterType((*ChangeChannelTypeReq)(nil), "Channel.ChangeChannelTypeReq")
	proto.RegisterType((*ChangeChannelTypeResp)(nil), "Channel.ChangeChannelTypeResp")
	proto.RegisterType((*ChangeChannelViewIdReq)(nil), "Channel.ChangeChannelViewIdReq")
	proto.RegisterType((*ChangeChannelViewIdResp)(nil), "Channel.ChangeChannelViewIdResp")
	proto.RegisterEnum("Channel.ChannelBindType", ChannelBindType_name, ChannelBindType_value)
	proto.RegisterEnum("Channel.ChannelAdminRole", ChannelAdminRole_name, ChannelAdminRole_value)
	proto.RegisterEnum("Channel.ChannelSortType", ChannelSortType_name, ChannelSortType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelClient is the client API for Channel service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelClient interface {
	// CreateChannel 接口已经废弃 目前只是作为test tool工具调用保留
	CreateChannel(ctx context.Context, in *CreateChannelReq, opts ...grpc.CallOption) (*CreateChannelResp, error)
	DissolveChannel(ctx context.Context, in *DissolveChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取房间主题的详情
	GetChannelTopicDetail(ctx context.Context, in *GetChannelTopicDetailReq, opts ...grpc.CallOption) (*GetChannelTopicDetailResp, error)
	// 废弃 改用 UnmuteChannelMember
	UnsetChannelMute(ctx context.Context, in *UnsetChannelMuteReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 过期接口 改用GetChannelSimpleInfo
	GetChannelDetailInfo(ctx context.Context, in *GetChannelDetailInfoReq, opts ...grpc.CallOption) (*GetChannelDetailInfoResp, error)
	GetChannelMuteList(ctx context.Context, in *GetChannelMuteListReq, opts ...grpc.CallOption) (*GetChannelMuteListResp, error)
	// 过期接口 废弃 查询公会下的房间列表 改有channelguild服务提供
	GetChannelListByBindId(ctx context.Context, in *GetChannelListByBindIdReq, opts ...grpc.CallOption) (*GetChannelListByBindIdResp, error)
	GetChannelBindInfo(ctx context.Context, in *GetChannelBindInfoReq, opts ...grpc.CallOption) (*GetChannelBindInfoResp, error)
	ModifyChannel(ctx context.Context, in *ModifyChannelReq, opts ...grpc.CallOption) (*ModifyChannelResp, error)
	// 接口废弃 已经不再支持
	// 检查并从指定的绑定上频道的麦列表上剔除某人
	// 比如从公会下所有频道踢某人下麦(当用户被撤销管理员时)
	CheckAndKickChannelMicrByBindId(ctx context.Context, in *CheckAndKickChannelMicrByBindIdReq, opts ...grpc.CallOption) (*CheckAndKickChannelMicrByBindIdResp, error)
	CheckUserIsMute(ctx context.Context, in *CheckUserIsMuteReq, opts ...grpc.CallOption) (*CheckUserIsMuteResp, error)
	// 将用户出公会下面的所在频道 比如用户移出公会时被调用
	// 废弃
	KickUserOutOfGuildChannel(ctx context.Context, in *KickUserOutOfGuildChannelReq, opts ...grpc.CallOption) (*KickUserOutOfGuildChannelResp, error)
	// 踢人出频道
	// 过期接口 废弃
	KickoutChannelMember(ctx context.Context, in *KickoutChannelMemberReq, opts ...grpc.CallOption) (*KickoutChannelMemberResp, error)
	// 获取临时房的 分配状态
	CheckTmpChannelIsAlloced(ctx context.Context, in *CheckTmpChannelIsAllocedReq, opts ...grpc.CallOption) (*CheckTmpChannelIsAllocedResp, error)
	// 禁言(如果有上麦的话 会被踢下麦)
	// 废弃 改用 MuteChannelMember接口
	SetChannelMuteAndKickMic(ctx context.Context, in *SetChannelMuteAndKickMicReq, opts ...grpc.CallOption) (*SetChannelMuteAndKickMicResp, error)
	// 批量获取用户当前房间ID
	// rpc BatchGetUserCurrChannelID( BatchGetUserCurrChannelIDReq ) returns( BatchGetUserCurrChannelIDResp ){
	// 	option( tlvpickle.CmdID ) = 29;
	//    option( tlvpickle.OptString ) = "u:";
	//    option( tlvpickle.Usage ) = "-u <uid>";
	// }
	//
	// 根据displayId搜索channel
	GetChannelByDisplayId(ctx context.Context, in *GetChannelByDisplayIdReq, opts ...grpc.CallOption) (*GetChannelByDisplayIdResp, error)
	// 根据uid获取权限列表
	GetUserChannelRoleList(ctx context.Context, in *GetUserChannelRoleListReq, opts ...grpc.CallOption) (*GetUserChannelRoleListResp, error)
	// 通过displayId创建频道
	CreateChannelByDisplayID(ctx context.Context, in *CreateChannelByDisplayIDReq, opts ...grpc.CallOption) (*CreateChannelByDisplayIDResp, error)
	// 根据channelID列表批量获取房间信息
	// 过期接口
	GetChannelDetailInfoBatch(ctx context.Context, in *GetChannelDetailInfoBatchReq, opts ...grpc.CallOption) (*GetChannelDetailInfoBatchResp, error)
	// 分配临时房间
	AllocTempChannel(ctx context.Context, in *AllocTempChannelReq, opts ...grpc.CallOption) (*AllocTempChannelResp, error)
	// 获取房间的欢迎语
	GetChannelWelcomeMsg(ctx context.Context, in *GetChannelWelcomeMsgReq, opts ...grpc.CallOption) (*GetChannelWelcomeMsgResp, error)
	// 添加一首配置歌曲
	AddConfigMusic(ctx context.Context, in *AddConfigMusicReq, opts ...grpc.CallOption) (*AddConfigMusicResp, error)
	// 删除一首配置歌曲
	DelConfigMusic(ctx context.Context, in *DelConfigMusicReq, opts ...grpc.CallOption) (*DelConfigMusicResp, error)
	// 获取配置歌曲列表
	GetConfigMusicList(ctx context.Context, in *GetConfigMusicListReq, opts ...grpc.CallOption) (*GetConfigMusicListResp, error)
	// 获取频道创建时间
	GetChannelCreateTs(ctx context.Context, in *GetChannelCreateTsReq, opts ...grpc.CallOption) (*GetChannelCreateTsResp, error)
	// 记录房间礼物信息
	RecordSendGiftEvent(ctx context.Context, in *RecordSendGiftEventReq, opts ...grpc.CallOption) (*RecordSendGiftEventResp, error)
	// 获取房间消费土豪榜 // 接口已经废弃
	GetConsumeTopN(ctx context.Context, in *GetConsumeTopNReq, opts ...grpc.CallOption) (*GetConsumeTopNResp, error)
	// 更新房间的displayID  接口废弃，改用ChangeChannelViewID接口
	ChangeDisplayID(ctx context.Context, in *ChangeDisplayIDReq, opts ...grpc.CallOption) (*ChangeDisplayIDResp, error)
	// 获取房间最近访客列表 （接口废弃）
	GetChannelHistoryList(ctx context.Context, in *GetChannelHistoryListReq, opts ...grpc.CallOption) (*GetChannelHistoryListResp, error)
	// 获取房间统计信息(包括房间最大在线人数)（接口废弃）
	GetChannelStat(ctx context.Context, in *GetChannelStatReq, opts ...grpc.CallOption) (*GetChannelStatResp, error)
	// 获取房间管理员列表
	GetChannelAdmin(ctx context.Context, in *GetChannelAdminReq, opts ...grpc.CallOption) (*GetChannelAdminResp, error)
	// 换麦位
	// 过期接口 废弃 改为使用channelmic服务提供的接口
	ChangeMicrophone(ctx context.Context, in *ChangeMicrophoneReq, opts ...grpc.CallOption) (*ChangeMicrophoneResp, error)
	// 批量判断房间是否属于临时分配房
	BatchCheckIsTempAllocChannel(ctx context.Context, in *BatchCheckIsTempAllocChannelReq, opts ...grpc.CallOption) (*BatchCheckIsTempAllocChannelResp, error)
	// 获取房间简单信息
	GetChannelSimpleInfo(ctx context.Context, in *GetChannelSimpleInfoReq, opts ...grpc.CallOption) (*GetChannelSimpleInfoResp, error)
	// 批量获取房间简单信息
	BatchGetChannelSimpleInfo(ctx context.Context, in *BatchGetChannelSimpleInfoReq, opts ...grpc.CallOption) (*BatchGetChannelSimpleInfoResp, error)
	// 连麦请求
	// 过期接口 改为使用channellive服务提供的接口
	LiveConnectMicApply(ctx context.Context, in *LiveConnectMicApplyReq, opts ...grpc.CallOption) (*LiveConnectMicApplyResp, error)
	// 回应连麦
	// 过期接口 改为使用channellive服务提供的接口
	LiveConnectMicHandle(ctx context.Context, in *LiveConnectMicHandleReq, opts ...grpc.CallOption) (*LiveConnectMicHandleResp, error)
	// 获取连麦申请人列表
	// 过期接口 改为使用channellive服务提供的接口
	GetLiveConnectMicApplyUserList(ctx context.Context, in *GetLiveConnectMicApplyUserListReq, opts ...grpc.CallOption) (*GetLiveConnectMicApplyUserListResp, error)
	// 结束直播
	// 过期接口 改为使用channellive服务提供的接口
	FinishLive(ctx context.Context, in *FinishLiveReq, opts ...grpc.CallOption) (*FinishLiveResp, error)
	// 检查是否在直播中
	// 过期接口 改为使用channellive服务提供的接口
	CheckIsLiveStarting(ctx context.Context, in *CheckIsLiveStartingReq, opts ...grpc.CallOption) (*CheckIsLiveStartingResp, error)
	// 标记直播开始
	// 过期接口 改为使用channellive服务提供的接口
	StartLive(ctx context.Context, in *StartLiveReq, opts ...grpc.CallOption) (*StartLiveResp, error)
	// 检测用户是否被踢
	CheckUserKickoutFromChannel(ctx context.Context, in *CheckUserKickoutFromChannelReq, opts ...grpc.CallOption) (*CheckUserKickoutFromChannelResp, error)
	// 获取用户房间内累计消费 // 接口已经废弃
	GetUserAccumulateConsume(ctx context.Context, in *GetUserAccumulateConsumeReq, opts ...grpc.CallOption) (*GetUserAccumulateConsumeResp, error)
	// 过期接口 改为使用channellive服务提供的接口
	EnterLiveChannel(ctx context.Context, in *EnterLiveChannelReq, opts ...grpc.CallOption) (*EnterLiveChannelResp, error)
	// 过期接口 改为使用channellive服务提供的接口
	QuitLiveChannel(ctx context.Context, in *QuitLiveChannelReq, opts ...grpc.CallOption) (*QuitLiveChannelResp, error)
	// 释放临时房间
	ReleaseTempChannel(ctx context.Context, in *ReleaseTempChannelReq, opts ...grpc.CallOption) (*ReleaseTempChannelResp, error)
	AddChannelAdmin(ctx context.Context, in *AddChannelAdminReq, opts ...grpc.CallOption) (*AddChannelAdminResp, error)
	RemoveChannelAdmin(ctx context.Context, in *RemoveChannelAdminReq, opts ...grpc.CallOption) (*RemoveChannelAdminResp, error)
	// 新建频道简化版: 只创建频道，不进入频道
	CreateChannelLite(ctx context.Context, in *CreateChannelLiteReq, opts ...grpc.CallOption) (*CreateChannelLiteResp, error)
	BatchGetChannelBindType(ctx context.Context, in *BatchGetChannelBindTypeReq, opts ...grpc.CallOption) (*BatchGetChannelBindTypeResp, error)
	// 新的简单禁言接口
	MuteChannelMember(ctx context.Context, in *MuteChannelMemberReq, opts ...grpc.CallOption) (*MuteChannelMemberResp, error)
	UnmuteChannelMember(ctx context.Context, in *UnmuteChannelMemberReq, opts ...grpc.CallOption) (*UnmuteChannelMemberResp, error)
	KickoutChannelMemberLite(ctx context.Context, in *KickoutChannelMemberLiteReq, opts ...grpc.CallOption) (*KickoutChannelMemberLiteResp, error)
	// ============= bugfix 系列接口 ============
	ChackAndRefixChannelOwner(ctx context.Context, in *ChackAndRefixChannelOwnerReq, opts ...grpc.CallOption) (*ChackAndRefixChannelOwnerResp, error)
	RefixChannelOwner(ctx context.Context, in *RefixChannelOwnerReq, opts ...grpc.CallOption) (*RefixChannelOwnerResp, error)
	// ============ 白名单 系列接口 =======================
	AddChannelToWhiteList(ctx context.Context, in *AddChannelToWhiteListReq, opts ...grpc.CallOption) (*AddChannelToWhiteListResp, error)
	RemoveFromWhiteList(ctx context.Context, in *RemoveFromWhiteListReq, opts ...grpc.CallOption) (*RemoveFromWhiteListResp, error)
	GetChannelWhiteList(ctx context.Context, in *GetChannelWhiteListReq, opts ...grpc.CallOption) (*GetChannelWhiteListResp, error)
	// ============ 修改房间的bind类型  =======================
	ChangeChannelType(ctx context.Context, in *ChangeChannelTypeReq, opts ...grpc.CallOption) (*ChangeChannelTypeResp, error)
	// 更新房间的displayID，允许房间不存在displayid，用于修复异常情况   接口废弃，改用ChangeChannelViewID接口
	ChangeDisplayIDV2(ctx context.Context, in *ChangeDisplayIDReq, opts ...grpc.CallOption) (*ChangeDisplayIDResp, error)
	// 修改房间进房控制类型:密码进房，白名单进房等
	ModifyChannelEnterControlType(ctx context.Context, in *ModifyChannelEnterControlTypeReq, opts ...grpc.CallOption) (*ModifyChannelEnterControlTypeResp, error)
	// ============ 临时房的批量和监控接口  =======================
	BatchAllocTempChannels(ctx context.Context, in *BatchAllocTempChannelReq, opts ...grpc.CallOption) (*BatchAllocTempChannelResp, error)
	BatchReleaseTempChannels(ctx context.Context, in *BatchReleaseTempChannelReq, opts ...grpc.CallOption) (*BatchReleaseTempChannelResp, error)
	QueryTempChannelsNum(ctx context.Context, in *QueryTempChannelsNumReq, opts ...grpc.CallOption) (*QueryTempChannelsNumResp, error)
	ChangeChannelViewId(ctx context.Context, in *ChangeChannelViewIdReq, opts ...grpc.CallOption) (*ChangeChannelViewIdResp, error)
}

type channelClient struct {
	cc *grpc.ClientConn
}

func NewChannelClient(cc *grpc.ClientConn) ChannelClient {
	return &channelClient{cc}
}

func (c *channelClient) CreateChannel(ctx context.Context, in *CreateChannelReq, opts ...grpc.CallOption) (*CreateChannelResp, error) {
	out := new(CreateChannelResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/CreateChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) DissolveChannel(ctx context.Context, in *DissolveChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, "/Channel.Channel/DissolveChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetChannelTopicDetail(ctx context.Context, in *GetChannelTopicDetailReq, opts ...grpc.CallOption) (*GetChannelTopicDetailResp, error) {
	out := new(GetChannelTopicDetailResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/GetChannelTopicDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) UnsetChannelMute(ctx context.Context, in *UnsetChannelMuteReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, "/Channel.Channel/UnsetChannelMute", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetChannelDetailInfo(ctx context.Context, in *GetChannelDetailInfoReq, opts ...grpc.CallOption) (*GetChannelDetailInfoResp, error) {
	out := new(GetChannelDetailInfoResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/GetChannelDetailInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetChannelMuteList(ctx context.Context, in *GetChannelMuteListReq, opts ...grpc.CallOption) (*GetChannelMuteListResp, error) {
	out := new(GetChannelMuteListResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/GetChannelMuteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetChannelListByBindId(ctx context.Context, in *GetChannelListByBindIdReq, opts ...grpc.CallOption) (*GetChannelListByBindIdResp, error) {
	out := new(GetChannelListByBindIdResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/GetChannelListByBindId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetChannelBindInfo(ctx context.Context, in *GetChannelBindInfoReq, opts ...grpc.CallOption) (*GetChannelBindInfoResp, error) {
	out := new(GetChannelBindInfoResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/GetChannelBindInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) ModifyChannel(ctx context.Context, in *ModifyChannelReq, opts ...grpc.CallOption) (*ModifyChannelResp, error) {
	out := new(ModifyChannelResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/ModifyChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) CheckAndKickChannelMicrByBindId(ctx context.Context, in *CheckAndKickChannelMicrByBindIdReq, opts ...grpc.CallOption) (*CheckAndKickChannelMicrByBindIdResp, error) {
	out := new(CheckAndKickChannelMicrByBindIdResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/CheckAndKickChannelMicrByBindId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) CheckUserIsMute(ctx context.Context, in *CheckUserIsMuteReq, opts ...grpc.CallOption) (*CheckUserIsMuteResp, error) {
	out := new(CheckUserIsMuteResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/CheckUserIsMute", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) KickUserOutOfGuildChannel(ctx context.Context, in *KickUserOutOfGuildChannelReq, opts ...grpc.CallOption) (*KickUserOutOfGuildChannelResp, error) {
	out := new(KickUserOutOfGuildChannelResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/KickUserOutOfGuildChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) KickoutChannelMember(ctx context.Context, in *KickoutChannelMemberReq, opts ...grpc.CallOption) (*KickoutChannelMemberResp, error) {
	out := new(KickoutChannelMemberResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/KickoutChannelMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) CheckTmpChannelIsAlloced(ctx context.Context, in *CheckTmpChannelIsAllocedReq, opts ...grpc.CallOption) (*CheckTmpChannelIsAllocedResp, error) {
	out := new(CheckTmpChannelIsAllocedResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/CheckTmpChannelIsAlloced", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) SetChannelMuteAndKickMic(ctx context.Context, in *SetChannelMuteAndKickMicReq, opts ...grpc.CallOption) (*SetChannelMuteAndKickMicResp, error) {
	out := new(SetChannelMuteAndKickMicResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/SetChannelMuteAndKickMic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetChannelByDisplayId(ctx context.Context, in *GetChannelByDisplayIdReq, opts ...grpc.CallOption) (*GetChannelByDisplayIdResp, error) {
	out := new(GetChannelByDisplayIdResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/GetChannelByDisplayId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetUserChannelRoleList(ctx context.Context, in *GetUserChannelRoleListReq, opts ...grpc.CallOption) (*GetUserChannelRoleListResp, error) {
	out := new(GetUserChannelRoleListResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/GetUserChannelRoleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) CreateChannelByDisplayID(ctx context.Context, in *CreateChannelByDisplayIDReq, opts ...grpc.CallOption) (*CreateChannelByDisplayIDResp, error) {
	out := new(CreateChannelByDisplayIDResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/CreateChannelByDisplayID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetChannelDetailInfoBatch(ctx context.Context, in *GetChannelDetailInfoBatchReq, opts ...grpc.CallOption) (*GetChannelDetailInfoBatchResp, error) {
	out := new(GetChannelDetailInfoBatchResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/GetChannelDetailInfoBatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) AllocTempChannel(ctx context.Context, in *AllocTempChannelReq, opts ...grpc.CallOption) (*AllocTempChannelResp, error) {
	out := new(AllocTempChannelResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/AllocTempChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetChannelWelcomeMsg(ctx context.Context, in *GetChannelWelcomeMsgReq, opts ...grpc.CallOption) (*GetChannelWelcomeMsgResp, error) {
	out := new(GetChannelWelcomeMsgResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/GetChannelWelcomeMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) AddConfigMusic(ctx context.Context, in *AddConfigMusicReq, opts ...grpc.CallOption) (*AddConfigMusicResp, error) {
	out := new(AddConfigMusicResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/AddConfigMusic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) DelConfigMusic(ctx context.Context, in *DelConfigMusicReq, opts ...grpc.CallOption) (*DelConfigMusicResp, error) {
	out := new(DelConfigMusicResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/DelConfigMusic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetConfigMusicList(ctx context.Context, in *GetConfigMusicListReq, opts ...grpc.CallOption) (*GetConfigMusicListResp, error) {
	out := new(GetConfigMusicListResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/GetConfigMusicList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetChannelCreateTs(ctx context.Context, in *GetChannelCreateTsReq, opts ...grpc.CallOption) (*GetChannelCreateTsResp, error) {
	out := new(GetChannelCreateTsResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/GetChannelCreateTs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) RecordSendGiftEvent(ctx context.Context, in *RecordSendGiftEventReq, opts ...grpc.CallOption) (*RecordSendGiftEventResp, error) {
	out := new(RecordSendGiftEventResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/RecordSendGiftEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetConsumeTopN(ctx context.Context, in *GetConsumeTopNReq, opts ...grpc.CallOption) (*GetConsumeTopNResp, error) {
	out := new(GetConsumeTopNResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/GetConsumeTopN", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) ChangeDisplayID(ctx context.Context, in *ChangeDisplayIDReq, opts ...grpc.CallOption) (*ChangeDisplayIDResp, error) {
	out := new(ChangeDisplayIDResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/ChangeDisplayID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetChannelHistoryList(ctx context.Context, in *GetChannelHistoryListReq, opts ...grpc.CallOption) (*GetChannelHistoryListResp, error) {
	out := new(GetChannelHistoryListResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/GetChannelHistoryList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetChannelStat(ctx context.Context, in *GetChannelStatReq, opts ...grpc.CallOption) (*GetChannelStatResp, error) {
	out := new(GetChannelStatResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/GetChannelStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetChannelAdmin(ctx context.Context, in *GetChannelAdminReq, opts ...grpc.CallOption) (*GetChannelAdminResp, error) {
	out := new(GetChannelAdminResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/GetChannelAdmin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) ChangeMicrophone(ctx context.Context, in *ChangeMicrophoneReq, opts ...grpc.CallOption) (*ChangeMicrophoneResp, error) {
	out := new(ChangeMicrophoneResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/ChangeMicrophone", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) BatchCheckIsTempAllocChannel(ctx context.Context, in *BatchCheckIsTempAllocChannelReq, opts ...grpc.CallOption) (*BatchCheckIsTempAllocChannelResp, error) {
	out := new(BatchCheckIsTempAllocChannelResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/BatchCheckIsTempAllocChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetChannelSimpleInfo(ctx context.Context, in *GetChannelSimpleInfoReq, opts ...grpc.CallOption) (*GetChannelSimpleInfoResp, error) {
	out := new(GetChannelSimpleInfoResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/GetChannelSimpleInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) BatchGetChannelSimpleInfo(ctx context.Context, in *BatchGetChannelSimpleInfoReq, opts ...grpc.CallOption) (*BatchGetChannelSimpleInfoResp, error) {
	out := new(BatchGetChannelSimpleInfoResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/BatchGetChannelSimpleInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) LiveConnectMicApply(ctx context.Context, in *LiveConnectMicApplyReq, opts ...grpc.CallOption) (*LiveConnectMicApplyResp, error) {
	out := new(LiveConnectMicApplyResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/LiveConnectMicApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) LiveConnectMicHandle(ctx context.Context, in *LiveConnectMicHandleReq, opts ...grpc.CallOption) (*LiveConnectMicHandleResp, error) {
	out := new(LiveConnectMicHandleResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/LiveConnectMicHandle", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetLiveConnectMicApplyUserList(ctx context.Context, in *GetLiveConnectMicApplyUserListReq, opts ...grpc.CallOption) (*GetLiveConnectMicApplyUserListResp, error) {
	out := new(GetLiveConnectMicApplyUserListResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/GetLiveConnectMicApplyUserList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) FinishLive(ctx context.Context, in *FinishLiveReq, opts ...grpc.CallOption) (*FinishLiveResp, error) {
	out := new(FinishLiveResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/FinishLive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) CheckIsLiveStarting(ctx context.Context, in *CheckIsLiveStartingReq, opts ...grpc.CallOption) (*CheckIsLiveStartingResp, error) {
	out := new(CheckIsLiveStartingResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/CheckIsLiveStarting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) StartLive(ctx context.Context, in *StartLiveReq, opts ...grpc.CallOption) (*StartLiveResp, error) {
	out := new(StartLiveResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/StartLive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) CheckUserKickoutFromChannel(ctx context.Context, in *CheckUserKickoutFromChannelReq, opts ...grpc.CallOption) (*CheckUserKickoutFromChannelResp, error) {
	out := new(CheckUserKickoutFromChannelResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/CheckUserKickoutFromChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetUserAccumulateConsume(ctx context.Context, in *GetUserAccumulateConsumeReq, opts ...grpc.CallOption) (*GetUserAccumulateConsumeResp, error) {
	out := new(GetUserAccumulateConsumeResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/GetUserAccumulateConsume", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) EnterLiveChannel(ctx context.Context, in *EnterLiveChannelReq, opts ...grpc.CallOption) (*EnterLiveChannelResp, error) {
	out := new(EnterLiveChannelResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/EnterLiveChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) QuitLiveChannel(ctx context.Context, in *QuitLiveChannelReq, opts ...grpc.CallOption) (*QuitLiveChannelResp, error) {
	out := new(QuitLiveChannelResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/QuitLiveChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) ReleaseTempChannel(ctx context.Context, in *ReleaseTempChannelReq, opts ...grpc.CallOption) (*ReleaseTempChannelResp, error) {
	out := new(ReleaseTempChannelResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/ReleaseTempChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) AddChannelAdmin(ctx context.Context, in *AddChannelAdminReq, opts ...grpc.CallOption) (*AddChannelAdminResp, error) {
	out := new(AddChannelAdminResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/AddChannelAdmin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) RemoveChannelAdmin(ctx context.Context, in *RemoveChannelAdminReq, opts ...grpc.CallOption) (*RemoveChannelAdminResp, error) {
	out := new(RemoveChannelAdminResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/RemoveChannelAdmin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) CreateChannelLite(ctx context.Context, in *CreateChannelLiteReq, opts ...grpc.CallOption) (*CreateChannelLiteResp, error) {
	out := new(CreateChannelLiteResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/CreateChannelLite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) BatchGetChannelBindType(ctx context.Context, in *BatchGetChannelBindTypeReq, opts ...grpc.CallOption) (*BatchGetChannelBindTypeResp, error) {
	out := new(BatchGetChannelBindTypeResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/BatchGetChannelBindType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) MuteChannelMember(ctx context.Context, in *MuteChannelMemberReq, opts ...grpc.CallOption) (*MuteChannelMemberResp, error) {
	out := new(MuteChannelMemberResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/MuteChannelMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) UnmuteChannelMember(ctx context.Context, in *UnmuteChannelMemberReq, opts ...grpc.CallOption) (*UnmuteChannelMemberResp, error) {
	out := new(UnmuteChannelMemberResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/UnmuteChannelMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) KickoutChannelMemberLite(ctx context.Context, in *KickoutChannelMemberLiteReq, opts ...grpc.CallOption) (*KickoutChannelMemberLiteResp, error) {
	out := new(KickoutChannelMemberLiteResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/KickoutChannelMemberLite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) ChackAndRefixChannelOwner(ctx context.Context, in *ChackAndRefixChannelOwnerReq, opts ...grpc.CallOption) (*ChackAndRefixChannelOwnerResp, error) {
	out := new(ChackAndRefixChannelOwnerResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/ChackAndRefixChannelOwner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) RefixChannelOwner(ctx context.Context, in *RefixChannelOwnerReq, opts ...grpc.CallOption) (*RefixChannelOwnerResp, error) {
	out := new(RefixChannelOwnerResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/RefixChannelOwner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) AddChannelToWhiteList(ctx context.Context, in *AddChannelToWhiteListReq, opts ...grpc.CallOption) (*AddChannelToWhiteListResp, error) {
	out := new(AddChannelToWhiteListResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/AddChannelToWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) RemoveFromWhiteList(ctx context.Context, in *RemoveFromWhiteListReq, opts ...grpc.CallOption) (*RemoveFromWhiteListResp, error) {
	out := new(RemoveFromWhiteListResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/RemoveFromWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetChannelWhiteList(ctx context.Context, in *GetChannelWhiteListReq, opts ...grpc.CallOption) (*GetChannelWhiteListResp, error) {
	out := new(GetChannelWhiteListResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/GetChannelWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) ChangeChannelType(ctx context.Context, in *ChangeChannelTypeReq, opts ...grpc.CallOption) (*ChangeChannelTypeResp, error) {
	out := new(ChangeChannelTypeResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/ChangeChannelType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) ChangeDisplayIDV2(ctx context.Context, in *ChangeDisplayIDReq, opts ...grpc.CallOption) (*ChangeDisplayIDResp, error) {
	out := new(ChangeDisplayIDResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/ChangeDisplayIDV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) ModifyChannelEnterControlType(ctx context.Context, in *ModifyChannelEnterControlTypeReq, opts ...grpc.CallOption) (*ModifyChannelEnterControlTypeResp, error) {
	out := new(ModifyChannelEnterControlTypeResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/ModifyChannelEnterControlType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) BatchAllocTempChannels(ctx context.Context, in *BatchAllocTempChannelReq, opts ...grpc.CallOption) (*BatchAllocTempChannelResp, error) {
	out := new(BatchAllocTempChannelResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/BatchAllocTempChannels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) BatchReleaseTempChannels(ctx context.Context, in *BatchReleaseTempChannelReq, opts ...grpc.CallOption) (*BatchReleaseTempChannelResp, error) {
	out := new(BatchReleaseTempChannelResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/BatchReleaseTempChannels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) QueryTempChannelsNum(ctx context.Context, in *QueryTempChannelsNumReq, opts ...grpc.CallOption) (*QueryTempChannelsNumResp, error) {
	out := new(QueryTempChannelsNumResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/QueryTempChannelsNum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) ChangeChannelViewId(ctx context.Context, in *ChangeChannelViewIdReq, opts ...grpc.CallOption) (*ChangeChannelViewIdResp, error) {
	out := new(ChangeChannelViewIdResp)
	err := c.cc.Invoke(ctx, "/Channel.Channel/ChangeChannelViewId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelServer is the server API for Channel service.
type ChannelServer interface {
	// CreateChannel 接口已经废弃 目前只是作为test tool工具调用保留
	CreateChannel(context.Context, *CreateChannelReq) (*CreateChannelResp, error)
	DissolveChannel(context.Context, *DissolveChannelReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取房间主题的详情
	GetChannelTopicDetail(context.Context, *GetChannelTopicDetailReq) (*GetChannelTopicDetailResp, error)
	// 废弃 改用 UnmuteChannelMember
	UnsetChannelMute(context.Context, *UnsetChannelMuteReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 过期接口 改用GetChannelSimpleInfo
	GetChannelDetailInfo(context.Context, *GetChannelDetailInfoReq) (*GetChannelDetailInfoResp, error)
	GetChannelMuteList(context.Context, *GetChannelMuteListReq) (*GetChannelMuteListResp, error)
	// 过期接口 废弃 查询公会下的房间列表 改有channelguild服务提供
	GetChannelListByBindId(context.Context, *GetChannelListByBindIdReq) (*GetChannelListByBindIdResp, error)
	GetChannelBindInfo(context.Context, *GetChannelBindInfoReq) (*GetChannelBindInfoResp, error)
	ModifyChannel(context.Context, *ModifyChannelReq) (*ModifyChannelResp, error)
	// 接口废弃 已经不再支持
	// 检查并从指定的绑定上频道的麦列表上剔除某人
	// 比如从公会下所有频道踢某人下麦(当用户被撤销管理员时)
	CheckAndKickChannelMicrByBindId(context.Context, *CheckAndKickChannelMicrByBindIdReq) (*CheckAndKickChannelMicrByBindIdResp, error)
	CheckUserIsMute(context.Context, *CheckUserIsMuteReq) (*CheckUserIsMuteResp, error)
	// 将用户出公会下面的所在频道 比如用户移出公会时被调用
	// 废弃
	KickUserOutOfGuildChannel(context.Context, *KickUserOutOfGuildChannelReq) (*KickUserOutOfGuildChannelResp, error)
	// 踢人出频道
	// 过期接口 废弃
	KickoutChannelMember(context.Context, *KickoutChannelMemberReq) (*KickoutChannelMemberResp, error)
	// 获取临时房的 分配状态
	CheckTmpChannelIsAlloced(context.Context, *CheckTmpChannelIsAllocedReq) (*CheckTmpChannelIsAllocedResp, error)
	// 禁言(如果有上麦的话 会被踢下麦)
	// 废弃 改用 MuteChannelMember接口
	SetChannelMuteAndKickMic(context.Context, *SetChannelMuteAndKickMicReq) (*SetChannelMuteAndKickMicResp, error)
	// 批量获取用户当前房间ID
	// rpc BatchGetUserCurrChannelID( BatchGetUserCurrChannelIDReq ) returns( BatchGetUserCurrChannelIDResp ){
	// 	option( tlvpickle.CmdID ) = 29;
	//    option( tlvpickle.OptString ) = "u:";
	//    option( tlvpickle.Usage ) = "-u <uid>";
	// }
	//
	// 根据displayId搜索channel
	GetChannelByDisplayId(context.Context, *GetChannelByDisplayIdReq) (*GetChannelByDisplayIdResp, error)
	// 根据uid获取权限列表
	GetUserChannelRoleList(context.Context, *GetUserChannelRoleListReq) (*GetUserChannelRoleListResp, error)
	// 通过displayId创建频道
	CreateChannelByDisplayID(context.Context, *CreateChannelByDisplayIDReq) (*CreateChannelByDisplayIDResp, error)
	// 根据channelID列表批量获取房间信息
	// 过期接口
	GetChannelDetailInfoBatch(context.Context, *GetChannelDetailInfoBatchReq) (*GetChannelDetailInfoBatchResp, error)
	// 分配临时房间
	AllocTempChannel(context.Context, *AllocTempChannelReq) (*AllocTempChannelResp, error)
	// 获取房间的欢迎语
	GetChannelWelcomeMsg(context.Context, *GetChannelWelcomeMsgReq) (*GetChannelWelcomeMsgResp, error)
	// 添加一首配置歌曲
	AddConfigMusic(context.Context, *AddConfigMusicReq) (*AddConfigMusicResp, error)
	// 删除一首配置歌曲
	DelConfigMusic(context.Context, *DelConfigMusicReq) (*DelConfigMusicResp, error)
	// 获取配置歌曲列表
	GetConfigMusicList(context.Context, *GetConfigMusicListReq) (*GetConfigMusicListResp, error)
	// 获取频道创建时间
	GetChannelCreateTs(context.Context, *GetChannelCreateTsReq) (*GetChannelCreateTsResp, error)
	// 记录房间礼物信息
	RecordSendGiftEvent(context.Context, *RecordSendGiftEventReq) (*RecordSendGiftEventResp, error)
	// 获取房间消费土豪榜 // 接口已经废弃
	GetConsumeTopN(context.Context, *GetConsumeTopNReq) (*GetConsumeTopNResp, error)
	// 更新房间的displayID  接口废弃，改用ChangeChannelViewID接口
	ChangeDisplayID(context.Context, *ChangeDisplayIDReq) (*ChangeDisplayIDResp, error)
	// 获取房间最近访客列表 （接口废弃）
	GetChannelHistoryList(context.Context, *GetChannelHistoryListReq) (*GetChannelHistoryListResp, error)
	// 获取房间统计信息(包括房间最大在线人数)（接口废弃）
	GetChannelStat(context.Context, *GetChannelStatReq) (*GetChannelStatResp, error)
	// 获取房间管理员列表
	GetChannelAdmin(context.Context, *GetChannelAdminReq) (*GetChannelAdminResp, error)
	// 换麦位
	// 过期接口 废弃 改为使用channelmic服务提供的接口
	ChangeMicrophone(context.Context, *ChangeMicrophoneReq) (*ChangeMicrophoneResp, error)
	// 批量判断房间是否属于临时分配房
	BatchCheckIsTempAllocChannel(context.Context, *BatchCheckIsTempAllocChannelReq) (*BatchCheckIsTempAllocChannelResp, error)
	// 获取房间简单信息
	GetChannelSimpleInfo(context.Context, *GetChannelSimpleInfoReq) (*GetChannelSimpleInfoResp, error)
	// 批量获取房间简单信息
	BatchGetChannelSimpleInfo(context.Context, *BatchGetChannelSimpleInfoReq) (*BatchGetChannelSimpleInfoResp, error)
	// 连麦请求
	// 过期接口 改为使用channellive服务提供的接口
	LiveConnectMicApply(context.Context, *LiveConnectMicApplyReq) (*LiveConnectMicApplyResp, error)
	// 回应连麦
	// 过期接口 改为使用channellive服务提供的接口
	LiveConnectMicHandle(context.Context, *LiveConnectMicHandleReq) (*LiveConnectMicHandleResp, error)
	// 获取连麦申请人列表
	// 过期接口 改为使用channellive服务提供的接口
	GetLiveConnectMicApplyUserList(context.Context, *GetLiveConnectMicApplyUserListReq) (*GetLiveConnectMicApplyUserListResp, error)
	// 结束直播
	// 过期接口 改为使用channellive服务提供的接口
	FinishLive(context.Context, *FinishLiveReq) (*FinishLiveResp, error)
	// 检查是否在直播中
	// 过期接口 改为使用channellive服务提供的接口
	CheckIsLiveStarting(context.Context, *CheckIsLiveStartingReq) (*CheckIsLiveStartingResp, error)
	// 标记直播开始
	// 过期接口 改为使用channellive服务提供的接口
	StartLive(context.Context, *StartLiveReq) (*StartLiveResp, error)
	// 检测用户是否被踢
	CheckUserKickoutFromChannel(context.Context, *CheckUserKickoutFromChannelReq) (*CheckUserKickoutFromChannelResp, error)
	// 获取用户房间内累计消费 // 接口已经废弃
	GetUserAccumulateConsume(context.Context, *GetUserAccumulateConsumeReq) (*GetUserAccumulateConsumeResp, error)
	// 过期接口 改为使用channellive服务提供的接口
	EnterLiveChannel(context.Context, *EnterLiveChannelReq) (*EnterLiveChannelResp, error)
	// 过期接口 改为使用channellive服务提供的接口
	QuitLiveChannel(context.Context, *QuitLiveChannelReq) (*QuitLiveChannelResp, error)
	// 释放临时房间
	ReleaseTempChannel(context.Context, *ReleaseTempChannelReq) (*ReleaseTempChannelResp, error)
	AddChannelAdmin(context.Context, *AddChannelAdminReq) (*AddChannelAdminResp, error)
	RemoveChannelAdmin(context.Context, *RemoveChannelAdminReq) (*RemoveChannelAdminResp, error)
	// 新建频道简化版: 只创建频道，不进入频道
	CreateChannelLite(context.Context, *CreateChannelLiteReq) (*CreateChannelLiteResp, error)
	BatchGetChannelBindType(context.Context, *BatchGetChannelBindTypeReq) (*BatchGetChannelBindTypeResp, error)
	// 新的简单禁言接口
	MuteChannelMember(context.Context, *MuteChannelMemberReq) (*MuteChannelMemberResp, error)
	UnmuteChannelMember(context.Context, *UnmuteChannelMemberReq) (*UnmuteChannelMemberResp, error)
	KickoutChannelMemberLite(context.Context, *KickoutChannelMemberLiteReq) (*KickoutChannelMemberLiteResp, error)
	// ============= bugfix 系列接口 ============
	ChackAndRefixChannelOwner(context.Context, *ChackAndRefixChannelOwnerReq) (*ChackAndRefixChannelOwnerResp, error)
	RefixChannelOwner(context.Context, *RefixChannelOwnerReq) (*RefixChannelOwnerResp, error)
	// ============ 白名单 系列接口 =======================
	AddChannelToWhiteList(context.Context, *AddChannelToWhiteListReq) (*AddChannelToWhiteListResp, error)
	RemoveFromWhiteList(context.Context, *RemoveFromWhiteListReq) (*RemoveFromWhiteListResp, error)
	GetChannelWhiteList(context.Context, *GetChannelWhiteListReq) (*GetChannelWhiteListResp, error)
	// ============ 修改房间的bind类型  =======================
	ChangeChannelType(context.Context, *ChangeChannelTypeReq) (*ChangeChannelTypeResp, error)
	// 更新房间的displayID，允许房间不存在displayid，用于修复异常情况   接口废弃，改用ChangeChannelViewID接口
	ChangeDisplayIDV2(context.Context, *ChangeDisplayIDReq) (*ChangeDisplayIDResp, error)
	// 修改房间进房控制类型:密码进房，白名单进房等
	ModifyChannelEnterControlType(context.Context, *ModifyChannelEnterControlTypeReq) (*ModifyChannelEnterControlTypeResp, error)
	// ============ 临时房的批量和监控接口  =======================
	BatchAllocTempChannels(context.Context, *BatchAllocTempChannelReq) (*BatchAllocTempChannelResp, error)
	BatchReleaseTempChannels(context.Context, *BatchReleaseTempChannelReq) (*BatchReleaseTempChannelResp, error)
	QueryTempChannelsNum(context.Context, *QueryTempChannelsNumReq) (*QueryTempChannelsNumResp, error)
	ChangeChannelViewId(context.Context, *ChangeChannelViewIdReq) (*ChangeChannelViewIdResp, error)
}

func RegisterChannelServer(s *grpc.Server, srv ChannelServer) {
	s.RegisterService(&_Channel_serviceDesc, srv)
}

func _Channel_CreateChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).CreateChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/CreateChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).CreateChannel(ctx, req.(*CreateChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_DissolveChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DissolveChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).DissolveChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/DissolveChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).DissolveChannel(ctx, req.(*DissolveChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetChannelTopicDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelTopicDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetChannelTopicDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/GetChannelTopicDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetChannelTopicDetail(ctx, req.(*GetChannelTopicDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_UnsetChannelMute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnsetChannelMuteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).UnsetChannelMute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/UnsetChannelMute",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).UnsetChannelMute(ctx, req.(*UnsetChannelMuteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetChannelDetailInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelDetailInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetChannelDetailInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/GetChannelDetailInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetChannelDetailInfo(ctx, req.(*GetChannelDetailInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetChannelMuteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMuteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetChannelMuteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/GetChannelMuteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetChannelMuteList(ctx, req.(*GetChannelMuteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetChannelListByBindId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelListByBindIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetChannelListByBindId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/GetChannelListByBindId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetChannelListByBindId(ctx, req.(*GetChannelListByBindIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetChannelBindInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelBindInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetChannelBindInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/GetChannelBindInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetChannelBindInfo(ctx, req.(*GetChannelBindInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_ModifyChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).ModifyChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/ModifyChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).ModifyChannel(ctx, req.(*ModifyChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_CheckAndKickChannelMicrByBindId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckAndKickChannelMicrByBindIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).CheckAndKickChannelMicrByBindId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/CheckAndKickChannelMicrByBindId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).CheckAndKickChannelMicrByBindId(ctx, req.(*CheckAndKickChannelMicrByBindIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_CheckUserIsMute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserIsMuteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).CheckUserIsMute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/CheckUserIsMute",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).CheckUserIsMute(ctx, req.(*CheckUserIsMuteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_KickUserOutOfGuildChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KickUserOutOfGuildChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).KickUserOutOfGuildChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/KickUserOutOfGuildChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).KickUserOutOfGuildChannel(ctx, req.(*KickUserOutOfGuildChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_KickoutChannelMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KickoutChannelMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).KickoutChannelMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/KickoutChannelMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).KickoutChannelMember(ctx, req.(*KickoutChannelMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_CheckTmpChannelIsAlloced_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckTmpChannelIsAllocedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).CheckTmpChannelIsAlloced(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/CheckTmpChannelIsAlloced",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).CheckTmpChannelIsAlloced(ctx, req.(*CheckTmpChannelIsAllocedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_SetChannelMuteAndKickMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelMuteAndKickMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).SetChannelMuteAndKickMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/SetChannelMuteAndKickMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).SetChannelMuteAndKickMic(ctx, req.(*SetChannelMuteAndKickMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetChannelByDisplayId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelByDisplayIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetChannelByDisplayId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/GetChannelByDisplayId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetChannelByDisplayId(ctx, req.(*GetChannelByDisplayIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetUserChannelRoleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserChannelRoleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetUserChannelRoleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/GetUserChannelRoleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetUserChannelRoleList(ctx, req.(*GetUserChannelRoleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_CreateChannelByDisplayID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateChannelByDisplayIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).CreateChannelByDisplayID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/CreateChannelByDisplayID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).CreateChannelByDisplayID(ctx, req.(*CreateChannelByDisplayIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetChannelDetailInfoBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelDetailInfoBatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetChannelDetailInfoBatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/GetChannelDetailInfoBatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetChannelDetailInfoBatch(ctx, req.(*GetChannelDetailInfoBatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_AllocTempChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AllocTempChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).AllocTempChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/AllocTempChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).AllocTempChannel(ctx, req.(*AllocTempChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetChannelWelcomeMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelWelcomeMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetChannelWelcomeMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/GetChannelWelcomeMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetChannelWelcomeMsg(ctx, req.(*GetChannelWelcomeMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_AddConfigMusic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddConfigMusicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).AddConfigMusic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/AddConfigMusic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).AddConfigMusic(ctx, req.(*AddConfigMusicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_DelConfigMusic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelConfigMusicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).DelConfigMusic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/DelConfigMusic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).DelConfigMusic(ctx, req.(*DelConfigMusicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetConfigMusicList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConfigMusicListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetConfigMusicList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/GetConfigMusicList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetConfigMusicList(ctx, req.(*GetConfigMusicListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetChannelCreateTs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelCreateTsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetChannelCreateTs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/GetChannelCreateTs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetChannelCreateTs(ctx, req.(*GetChannelCreateTsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_RecordSendGiftEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordSendGiftEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).RecordSendGiftEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/RecordSendGiftEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).RecordSendGiftEvent(ctx, req.(*RecordSendGiftEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetConsumeTopN_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConsumeTopNReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetConsumeTopN(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/GetConsumeTopN",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetConsumeTopN(ctx, req.(*GetConsumeTopNReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_ChangeDisplayID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeDisplayIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).ChangeDisplayID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/ChangeDisplayID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).ChangeDisplayID(ctx, req.(*ChangeDisplayIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetChannelHistoryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelHistoryListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetChannelHistoryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/GetChannelHistoryList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetChannelHistoryList(ctx, req.(*GetChannelHistoryListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetChannelStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetChannelStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/GetChannelStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetChannelStat(ctx, req.(*GetChannelStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetChannelAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelAdminReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetChannelAdmin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/GetChannelAdmin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetChannelAdmin(ctx, req.(*GetChannelAdminReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_ChangeMicrophone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeMicrophoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).ChangeMicrophone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/ChangeMicrophone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).ChangeMicrophone(ctx, req.(*ChangeMicrophoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_BatchCheckIsTempAllocChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckIsTempAllocChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).BatchCheckIsTempAllocChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/BatchCheckIsTempAllocChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).BatchCheckIsTempAllocChannel(ctx, req.(*BatchCheckIsTempAllocChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetChannelSimpleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelSimpleInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetChannelSimpleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/GetChannelSimpleInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetChannelSimpleInfo(ctx, req.(*GetChannelSimpleInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_BatchGetChannelSimpleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelSimpleInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).BatchGetChannelSimpleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/BatchGetChannelSimpleInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).BatchGetChannelSimpleInfo(ctx, req.(*BatchGetChannelSimpleInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_LiveConnectMicApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LiveConnectMicApplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).LiveConnectMicApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/LiveConnectMicApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).LiveConnectMicApply(ctx, req.(*LiveConnectMicApplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_LiveConnectMicHandle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LiveConnectMicHandleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).LiveConnectMicHandle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/LiveConnectMicHandle",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).LiveConnectMicHandle(ctx, req.(*LiveConnectMicHandleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetLiveConnectMicApplyUserList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveConnectMicApplyUserListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetLiveConnectMicApplyUserList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/GetLiveConnectMicApplyUserList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetLiveConnectMicApplyUserList(ctx, req.(*GetLiveConnectMicApplyUserListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_FinishLive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FinishLiveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).FinishLive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/FinishLive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).FinishLive(ctx, req.(*FinishLiveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_CheckIsLiveStarting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckIsLiveStartingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).CheckIsLiveStarting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/CheckIsLiveStarting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).CheckIsLiveStarting(ctx, req.(*CheckIsLiveStartingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_StartLive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartLiveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).StartLive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/StartLive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).StartLive(ctx, req.(*StartLiveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_CheckUserKickoutFromChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserKickoutFromChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).CheckUserKickoutFromChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/CheckUserKickoutFromChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).CheckUserKickoutFromChannel(ctx, req.(*CheckUserKickoutFromChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetUserAccumulateConsume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAccumulateConsumeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetUserAccumulateConsume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/GetUserAccumulateConsume",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetUserAccumulateConsume(ctx, req.(*GetUserAccumulateConsumeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_EnterLiveChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnterLiveChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).EnterLiveChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/EnterLiveChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).EnterLiveChannel(ctx, req.(*EnterLiveChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_QuitLiveChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuitLiveChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).QuitLiveChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/QuitLiveChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).QuitLiveChannel(ctx, req.(*QuitLiveChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_ReleaseTempChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseTempChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).ReleaseTempChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/ReleaseTempChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).ReleaseTempChannel(ctx, req.(*ReleaseTempChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_AddChannelAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelAdminReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).AddChannelAdmin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/AddChannelAdmin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).AddChannelAdmin(ctx, req.(*AddChannelAdminReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_RemoveChannelAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveChannelAdminReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).RemoveChannelAdmin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/RemoveChannelAdmin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).RemoveChannelAdmin(ctx, req.(*RemoveChannelAdminReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_CreateChannelLite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateChannelLiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).CreateChannelLite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/CreateChannelLite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).CreateChannelLite(ctx, req.(*CreateChannelLiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_BatchGetChannelBindType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelBindTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).BatchGetChannelBindType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/BatchGetChannelBindType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).BatchGetChannelBindType(ctx, req.(*BatchGetChannelBindTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_MuteChannelMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MuteChannelMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).MuteChannelMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/MuteChannelMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).MuteChannelMember(ctx, req.(*MuteChannelMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_UnmuteChannelMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnmuteChannelMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).UnmuteChannelMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/UnmuteChannelMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).UnmuteChannelMember(ctx, req.(*UnmuteChannelMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_KickoutChannelMemberLite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KickoutChannelMemberLiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).KickoutChannelMemberLite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/KickoutChannelMemberLite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).KickoutChannelMemberLite(ctx, req.(*KickoutChannelMemberLiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_ChackAndRefixChannelOwner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChackAndRefixChannelOwnerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).ChackAndRefixChannelOwner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/ChackAndRefixChannelOwner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).ChackAndRefixChannelOwner(ctx, req.(*ChackAndRefixChannelOwnerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_RefixChannelOwner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefixChannelOwnerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).RefixChannelOwner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/RefixChannelOwner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).RefixChannelOwner(ctx, req.(*RefixChannelOwnerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_AddChannelToWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelToWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).AddChannelToWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/AddChannelToWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).AddChannelToWhiteList(ctx, req.(*AddChannelToWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_RemoveFromWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveFromWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).RemoveFromWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/RemoveFromWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).RemoveFromWhiteList(ctx, req.(*RemoveFromWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetChannelWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetChannelWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/GetChannelWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetChannelWhiteList(ctx, req.(*GetChannelWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_ChangeChannelType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeChannelTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).ChangeChannelType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/ChangeChannelType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).ChangeChannelType(ctx, req.(*ChangeChannelTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_ChangeDisplayIDV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeDisplayIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).ChangeDisplayIDV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/ChangeDisplayIDV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).ChangeDisplayIDV2(ctx, req.(*ChangeDisplayIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_ModifyChannelEnterControlType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyChannelEnterControlTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).ModifyChannelEnterControlType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/ModifyChannelEnterControlType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).ModifyChannelEnterControlType(ctx, req.(*ModifyChannelEnterControlTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_BatchAllocTempChannels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAllocTempChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).BatchAllocTempChannels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/BatchAllocTempChannels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).BatchAllocTempChannels(ctx, req.(*BatchAllocTempChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_BatchReleaseTempChannels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchReleaseTempChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).BatchReleaseTempChannels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/BatchReleaseTempChannels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).BatchReleaseTempChannels(ctx, req.(*BatchReleaseTempChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_QueryTempChannelsNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryTempChannelsNumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).QueryTempChannelsNum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/QueryTempChannelsNum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).QueryTempChannelsNum(ctx, req.(*QueryTempChannelsNumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_ChangeChannelViewId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeChannelViewIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).ChangeChannelViewId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Channel.Channel/ChangeChannelViewId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).ChangeChannelViewId(ctx, req.(*ChangeChannelViewIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Channel_serviceDesc = grpc.ServiceDesc{
	ServiceName: "Channel.Channel",
	HandlerType: (*ChannelServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateChannel",
			Handler:    _Channel_CreateChannel_Handler,
		},
		{
			MethodName: "DissolveChannel",
			Handler:    _Channel_DissolveChannel_Handler,
		},
		{
			MethodName: "GetChannelTopicDetail",
			Handler:    _Channel_GetChannelTopicDetail_Handler,
		},
		{
			MethodName: "UnsetChannelMute",
			Handler:    _Channel_UnsetChannelMute_Handler,
		},
		{
			MethodName: "GetChannelDetailInfo",
			Handler:    _Channel_GetChannelDetailInfo_Handler,
		},
		{
			MethodName: "GetChannelMuteList",
			Handler:    _Channel_GetChannelMuteList_Handler,
		},
		{
			MethodName: "GetChannelListByBindId",
			Handler:    _Channel_GetChannelListByBindId_Handler,
		},
		{
			MethodName: "GetChannelBindInfo",
			Handler:    _Channel_GetChannelBindInfo_Handler,
		},
		{
			MethodName: "ModifyChannel",
			Handler:    _Channel_ModifyChannel_Handler,
		},
		{
			MethodName: "CheckAndKickChannelMicrByBindId",
			Handler:    _Channel_CheckAndKickChannelMicrByBindId_Handler,
		},
		{
			MethodName: "CheckUserIsMute",
			Handler:    _Channel_CheckUserIsMute_Handler,
		},
		{
			MethodName: "KickUserOutOfGuildChannel",
			Handler:    _Channel_KickUserOutOfGuildChannel_Handler,
		},
		{
			MethodName: "KickoutChannelMember",
			Handler:    _Channel_KickoutChannelMember_Handler,
		},
		{
			MethodName: "CheckTmpChannelIsAlloced",
			Handler:    _Channel_CheckTmpChannelIsAlloced_Handler,
		},
		{
			MethodName: "SetChannelMuteAndKickMic",
			Handler:    _Channel_SetChannelMuteAndKickMic_Handler,
		},
		{
			MethodName: "GetChannelByDisplayId",
			Handler:    _Channel_GetChannelByDisplayId_Handler,
		},
		{
			MethodName: "GetUserChannelRoleList",
			Handler:    _Channel_GetUserChannelRoleList_Handler,
		},
		{
			MethodName: "CreateChannelByDisplayID",
			Handler:    _Channel_CreateChannelByDisplayID_Handler,
		},
		{
			MethodName: "GetChannelDetailInfoBatch",
			Handler:    _Channel_GetChannelDetailInfoBatch_Handler,
		},
		{
			MethodName: "AllocTempChannel",
			Handler:    _Channel_AllocTempChannel_Handler,
		},
		{
			MethodName: "GetChannelWelcomeMsg",
			Handler:    _Channel_GetChannelWelcomeMsg_Handler,
		},
		{
			MethodName: "AddConfigMusic",
			Handler:    _Channel_AddConfigMusic_Handler,
		},
		{
			MethodName: "DelConfigMusic",
			Handler:    _Channel_DelConfigMusic_Handler,
		},
		{
			MethodName: "GetConfigMusicList",
			Handler:    _Channel_GetConfigMusicList_Handler,
		},
		{
			MethodName: "GetChannelCreateTs",
			Handler:    _Channel_GetChannelCreateTs_Handler,
		},
		{
			MethodName: "RecordSendGiftEvent",
			Handler:    _Channel_RecordSendGiftEvent_Handler,
		},
		{
			MethodName: "GetConsumeTopN",
			Handler:    _Channel_GetConsumeTopN_Handler,
		},
		{
			MethodName: "ChangeDisplayID",
			Handler:    _Channel_ChangeDisplayID_Handler,
		},
		{
			MethodName: "GetChannelHistoryList",
			Handler:    _Channel_GetChannelHistoryList_Handler,
		},
		{
			MethodName: "GetChannelStat",
			Handler:    _Channel_GetChannelStat_Handler,
		},
		{
			MethodName: "GetChannelAdmin",
			Handler:    _Channel_GetChannelAdmin_Handler,
		},
		{
			MethodName: "ChangeMicrophone",
			Handler:    _Channel_ChangeMicrophone_Handler,
		},
		{
			MethodName: "BatchCheckIsTempAllocChannel",
			Handler:    _Channel_BatchCheckIsTempAllocChannel_Handler,
		},
		{
			MethodName: "GetChannelSimpleInfo",
			Handler:    _Channel_GetChannelSimpleInfo_Handler,
		},
		{
			MethodName: "BatchGetChannelSimpleInfo",
			Handler:    _Channel_BatchGetChannelSimpleInfo_Handler,
		},
		{
			MethodName: "LiveConnectMicApply",
			Handler:    _Channel_LiveConnectMicApply_Handler,
		},
		{
			MethodName: "LiveConnectMicHandle",
			Handler:    _Channel_LiveConnectMicHandle_Handler,
		},
		{
			MethodName: "GetLiveConnectMicApplyUserList",
			Handler:    _Channel_GetLiveConnectMicApplyUserList_Handler,
		},
		{
			MethodName: "FinishLive",
			Handler:    _Channel_FinishLive_Handler,
		},
		{
			MethodName: "CheckIsLiveStarting",
			Handler:    _Channel_CheckIsLiveStarting_Handler,
		},
		{
			MethodName: "StartLive",
			Handler:    _Channel_StartLive_Handler,
		},
		{
			MethodName: "CheckUserKickoutFromChannel",
			Handler:    _Channel_CheckUserKickoutFromChannel_Handler,
		},
		{
			MethodName: "GetUserAccumulateConsume",
			Handler:    _Channel_GetUserAccumulateConsume_Handler,
		},
		{
			MethodName: "EnterLiveChannel",
			Handler:    _Channel_EnterLiveChannel_Handler,
		},
		{
			MethodName: "QuitLiveChannel",
			Handler:    _Channel_QuitLiveChannel_Handler,
		},
		{
			MethodName: "ReleaseTempChannel",
			Handler:    _Channel_ReleaseTempChannel_Handler,
		},
		{
			MethodName: "AddChannelAdmin",
			Handler:    _Channel_AddChannelAdmin_Handler,
		},
		{
			MethodName: "RemoveChannelAdmin",
			Handler:    _Channel_RemoveChannelAdmin_Handler,
		},
		{
			MethodName: "CreateChannelLite",
			Handler:    _Channel_CreateChannelLite_Handler,
		},
		{
			MethodName: "BatchGetChannelBindType",
			Handler:    _Channel_BatchGetChannelBindType_Handler,
		},
		{
			MethodName: "MuteChannelMember",
			Handler:    _Channel_MuteChannelMember_Handler,
		},
		{
			MethodName: "UnmuteChannelMember",
			Handler:    _Channel_UnmuteChannelMember_Handler,
		},
		{
			MethodName: "KickoutChannelMemberLite",
			Handler:    _Channel_KickoutChannelMemberLite_Handler,
		},
		{
			MethodName: "ChackAndRefixChannelOwner",
			Handler:    _Channel_ChackAndRefixChannelOwner_Handler,
		},
		{
			MethodName: "RefixChannelOwner",
			Handler:    _Channel_RefixChannelOwner_Handler,
		},
		{
			MethodName: "AddChannelToWhiteList",
			Handler:    _Channel_AddChannelToWhiteList_Handler,
		},
		{
			MethodName: "RemoveFromWhiteList",
			Handler:    _Channel_RemoveFromWhiteList_Handler,
		},
		{
			MethodName: "GetChannelWhiteList",
			Handler:    _Channel_GetChannelWhiteList_Handler,
		},
		{
			MethodName: "ChangeChannelType",
			Handler:    _Channel_ChangeChannelType_Handler,
		},
		{
			MethodName: "ChangeDisplayIDV2",
			Handler:    _Channel_ChangeDisplayIDV2_Handler,
		},
		{
			MethodName: "ModifyChannelEnterControlType",
			Handler:    _Channel_ModifyChannelEnterControlType_Handler,
		},
		{
			MethodName: "BatchAllocTempChannels",
			Handler:    _Channel_BatchAllocTempChannels_Handler,
		},
		{
			MethodName: "BatchReleaseTempChannels",
			Handler:    _Channel_BatchReleaseTempChannels_Handler,
		},
		{
			MethodName: "QueryTempChannelsNum",
			Handler:    _Channel_QueryTempChannelsNum_Handler,
		},
		{
			MethodName: "ChangeChannelViewId",
			Handler:    _Channel_ChangeChannelViewId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/channelsvr/channel.proto",
}

func init() {
	proto.RegisterFile("src/channelsvr/channel.proto", fileDescriptor_channel_679d35ab756dc52b)
}

var fileDescriptor_channel_679d35ab756dc52b = []byte{
	// 7569 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x7d, 0x5b, 0x6c, 0xe4, 0xc8,
	0x75, 0xe8, 0xb2, 0x5b, 0xcf, 0x23, 0xb5, 0xd4, 0xa2, 0xde, 0xad, 0xd1, 0x4c, 0x4f, 0xed, 0xcc,
	0xec, 0x78, 0x76, 0x35, 0xb3, 0x77, 0x5f, 0xde, 0xed, 0x95, 0x65, 0x6b, 0x24, 0xcd, 0x8c, 0xbc,
	0x23, 0xcd, 0xb8, 0x25, 0xed, 0xfa, 0xda, 0xb8, 0xe6, 0xa5, 0x48, 0x4a, 0xa2, 0x87, 0x4d, 0x72,
	0xbb, 0xd8, 0x92, 0x66, 0xf7, 0x1a, 0x5e, 0x5f, 0x5f, 0xdb, 0x7b, 0x83, 0x18, 0x30, 0x6c, 0xc4,
	0x41, 0x62, 0x3b, 0x70, 0x92, 0x4d, 0xf2, 0x93, 0x04, 0x09, 0x10, 0xe4, 0xc3, 0x09, 0x90, 0xcf,
	0x3c, 0xec, 0x04, 0x70, 0x12, 0x20, 0x40, 0x10, 0xdb, 0x01, 0xf2, 0xb3, 0xf9, 0x08, 0x90, 0xe4,
	0x27, 0x9f, 0x09, 0xea, 0x41, 0xb2, 0x48, 0x16, 0xd9, 0x3d, 0x1a, 0x2f, 0x90, 0xe4, 0xaf, 0x59,
	0x55, 0x3c, 0x75, 0xea, 0x9c, 0x53, 0xe7, 0x55, 0xa7, 0xd8, 0x70, 0x0e, 0xb7, 0x8d, 0x1b, 0xc6,
	0x91, 0xee, 0xba, 0x96, 0x83, 0x8f, 0xdb, 0xe1, 0xcf, 0xeb, 0x7e, 0xdb, 0x0b, 0x3c, 0x75, 0x70,
	0x8d, 0x3d, 0xd6, 0x2e, 0x19, 0x5e, 0xab, 0xe5, 0xb9, 0x37, 0x02, 0xe7, 0xd8, 0xb7, 0x8d, 0x07,
	0x8e, 0x75, 0x03, 0x3f, 0xd8, 0xef, 0xd8, 0x4e, 0x60, 0xbb, 0xc1, 0x43, 0xdf, 0x62, 0xc3, 0x51,
	0x00, 0x13, 0xfc, 0x85, 0x2d, 0xab, 0xb5, 0x6f, 0xb5, 0x9b, 0x9e, 0x63, 0xa9, 0x8b, 0x00, 0x87,
	0x1d, 0xdb, 0x31, 0xb5, 0xb6, 0xe7, 0x58, 0x73, 0x4a, 0x5d, 0xb9, 0x5a, 0x69, 0x0e, 0xd3, 0x96,
	0xa8, 0xbb, 0xed, 0x75, 0x7c, 0xd6, 0x5d, 0xe2, 0xdd, 0xa4, 0x85, 0x76, 0x5f, 0x84, 0x51, 0x8e,
	0x12, 0x1b, 0x50, 0xa6, 0x03, 0x46, 0x78, 0x1b, 0x19, 0x82, 0x5e, 0x81, 0xe9, 0xc4, 0xac, 0x37,
	0x75, 0x6c, 0x6d, 0xba, 0x07, 0x9e, 0x5a, 0x85, 0x72, 0xc7, 0x36, 0xe7, 0x94, 0x7a, 0xe9, 0x6a,
	0xa5, 0x49, 0x7e, 0xaa, 0x63, 0x50, 0x0a, 0xf0, 0x5c, 0x89, 0x36, 0x94, 0x02, 0x8c, 0xfe, 0x5c,
	0x81, 0xd9, 0xc4, 0xbb, 0xeb, 0x56, 0xa0, 0xdb, 0x4e, 0x6f, 0x6f, 0xab, 0xf3, 0x30, 0x64, 0x63,
	0xad, 0xed, 0xed, 0x7b, 0xc1, 0x5c, 0x99, 0xb6, 0x0e, 0xda, 0xb8, 0x49, 0x1e, 0x53, 0xab, 0xea,
	0xa3, 0x9d, 0xc2, 0xaa, 0x16, 0x01, 0x6c, 0xac, 0x1d, 0x79, 0x8e, 0xd9, 0xb2, 0x8d, 0xb9, 0xfe,
	0x7a, 0xe9, 0xea, 0x50, 0x73, 0xd8, 0xc6, 0x77, 0x58, 0x83, 0x3a, 0x0b, 0x83, 0x36, 0xd6, 0x5a,
	0x9d, 0xc0, 0x9a, 0x1b, 0xa0, 0x7d, 0x03, 0x36, 0xde, 0xea, 0x04, 0x96, 0x7a, 0x1e, 0xa0, 0x6d,
	0x99, 0xa6, 0xad, 0xb7, 0x3c, 0xd7, 0x9c, 0x1b, 0xa4, 0xb4, 0x10, 0x5a, 0x10, 0x86, 0xca, 0x96,
	0x6d, 0xb4, 0x77, 0x7c, 0xdd, 0x60, 0x24, 0x98, 0x86, 0x81, 0x96, 0x6d, 0x68, 0xd1, 0x3a, 0xfa,
	0x5b, 0xb6, 0xb1, 0x69, 0xaa, 0x0b, 0x30, 0x4c, 0x9a, 0x71, 0xa0, 0x07, 0x21, 0xcd, 0x87, 0x5a,
	0xb6, 0xb1, 0x43, 0x9e, 0xc9, 0xec, 0xa4, 0x93, 0x2c, 0x9e, 0x51, 0x9b, 0x80, 0xd8, 0xb3, 0xcd,
	0x10, 0x58, 0x80, 0xe7, 0xfa, 0x68, 0x3b, 0x01, 0xb6, 0x8b, 0xd1, 0xbf, 0x94, 0x23, 0xb6, 0xef,
	0xd8, 0x2d, 0xdf, 0x61, 0x33, 0x2f, 0x02, 0x84, 0x8c, 0x8b, 0x66, 0x1f, 0xe6, 0x2d, 0x9b, 0x26,
	0xe9, 0x36, 0x6d, 0xec, 0x3b, 0xfa, 0x43, 0xd2, 0xcd, 0x68, 0x3a, 0xcc, 0x5b, 0x36, 0xe9, 0x54,
	0xba, 0xef, 0x6b, 0x11, 0x0a, 0xfd, 0xba, 0xef, 0x6f, 0x9a, 0x04, 0xb5, 0x23, 0x1d, 0x6b, 0xfe,
	0x89, 0x49, 0x51, 0x18, 0x6a, 0x0e, 0x1c, 0xe9, 0xf8, 0xfe, 0x89, 0x29, 0x8a, 0x09, 0x91, 0xc7,
	0xb9, 0xfe, 0x84, 0x98, 0xec, 0x3e, 0xf4, 0xe9, 0xb2, 0xf6, 0x6d, 0xd7, 0x24, 0x30, 0x07, 0xd8,
	0xb2, 0xc8, 0xe3, 0xa6, 0xa9, 0x5e, 0x80, 0x11, 0x7c, 0x62, 0x07, 0xc6, 0x91, 0x76, 0xe0, 0xe8,
	0x87, 0x21, 0x55, 0x59, 0xd3, 0x2d, 0x47, 0x3f, 0x24, 0x03, 0x8c, 0xb6, 0xa5, 0x07, 0x56, 0x9b,
	0x12, 0x65, 0x88, 0x0d, 0xe0, 0x4d, 0x84, 0x30, 0x0b, 0x30, 0xcc, 0x9e, 0x08, 0x6d, 0x86, 0x19,
	0x39, 0x59, 0xc3, 0x2e, 0x93, 0x12, 0xc3, 0x73, 0xb5, 0x96, 0xf9, 0xe2, 0x1c, 0xd4, 0x95, 0xab,
	0xc3, 0xcd, 0x41, 0xf2, 0xbc, 0x65, 0xbe, 0x48, 0x00, 0x07, 0x9e, 0x4f, 0x48, 0x6a, 0x07, 0x8e,
	0x35, 0x37, 0x42, 0x7b, 0x81, 0x36, 0xed, 0x92, 0x16, 0x75, 0x06, 0x06, 0x7c, 0x1d, 0xe3, 0x13,
	0x73, 0x6e, 0x94, 0xf6, 0xf1, 0x27, 0x55, 0x85, 0x3e, 0x57, 0x6f, 0x59, 0x73, 0x15, 0xda, 0x4a,
	0x7f, 0x13, 0x92, 0xd9, 0x58, 0x33, 0x2d, 0x67, 0x6e, 0x8c, 0x92, 0xa6, 0xdf, 0xc6, 0xeb, 0x96,
	0xa3, 0x3e, 0x03, 0xaa, 0xe5, 0x12, 0xd4, 0x0d, 0xcf, 0x0d, 0xda, 0x1e, 0xa7, 0xcf, 0x38, 0x45,
	0xb2, 0x4a, 0x7b, 0xd6, 0x58, 0x07, 0x25, 0xd2, 0x15, 0x18, 0x0f, 0xe9, 0x78, 0x6c, 0x5b, 0x27,
	0x84, 0x58, 0x55, 0x3a, 0x47, 0x85, 0x37, 0xbf, 0x6e, 0x5b, 0x27, 0x9b, 0x26, 0xfa, 0xa3, 0x12,
	0x8c, 0x73, 0x9e, 0x47, 0xdb, 0xad, 0x0b, 0xc7, 0x05, 0x16, 0x51, 0xdc, 0x09, 0xcf, 0x87, 0x23,
	0x16, 0x6d, 0x93, 0x25, 0x5c, 0x87, 0xc9, 0x70, 0x48, 0x8b, 0x6e, 0x47, 0x0d, 0xdb, 0x6f, 0x59,
	0x7c, 0x6f, 0x4d, 0x18, 0xe2, 0x46, 0xdd, 0xb1, 0xdf, 0xb2, 0xd4, 0x4b, 0x30, 0x86, 0xcd, 0x07,
	0x1a, 0xb6, 0x30, 0xb6, 0x3d, 0x97, 0xcc, 0xca, 0x76, 0xda, 0x28, 0x36, 0x1f, 0xec, 0xb0, 0xc6,
	0x84, 0x2c, 0xf5, 0xe7, 0xc8, 0xd2, 0x40, 0x42, 0x96, 0x62, 0xa2, 0x0f, 0x26, 0x88, 0x9e, 0x14,
	0x59, 0x26, 0x05, 0x82, 0xc8, 0x8a, 0x7c, 0x1e, 0x4e, 0xf2, 0x59, 0x85, 0x3e, 0xd3, 0xc2, 0x06,
	0x67, 0x3f, 0xfd, 0x8d, 0xfe, 0xb1, 0x14, 0xed, 0x1a, 0x41, 0xe9, 0xac, 0x41, 0x35, 0xa4, 0xc0,
	0xbe, 0x8e, 0x2d, 0xdb, 0x3d, 0xf0, 0x28, 0x25, 0x47, 0x9e, 0x9b, 0xbb, 0xce, 0x47, 0x5f, 0x4f,
	0xd1, 0xbd, 0x19, 0x72, 0xec, 0x26, 0x7f, 0x21, 0x2d, 0xaf, 0xa5, 0x62, 0x79, 0x2d, 0xa7, 0xe4,
	0xf5, 0x1a, 0x4c, 0x44, 0x28, 0x90, 0xfd, 0x42, 0xe5, 0x85, 0x6d, 0xf8, 0x68, 0x26, 0xdb, 0x35,
	0xd3, 0x7b, 0xaa, 0x3f, 0xb1, 0xa7, 0xae, 0x42, 0xd5, 0xb4, 0xb1, 0xbe, 0xef, 0x58, 0x1a, 0x55,
	0x34, 0x84, 0x8d, 0x6c, 0xd7, 0x8d, 0xf1, 0xf6, 0x2d, 0xdb, 0xa0, 0x3c, 0x9c, 0x07, 0xa2, 0x79,
	0xb4, 0x96, 0x67, 0x5a, 0x7c, 0xeb, 0x11, 0xed, 0xb3, 0xe5, 0x99, 0x56, 0x7a, 0x63, 0x0e, 0x65,
	0x36, 0xe6, 0x15, 0x18, 0xb7, 0xb1, 0x16, 0x58, 0x2d, 0x5f, 0xd3, 0x1d, 0xc7, 0x33, 0x2c, 0x93,
	0x52, 0x7e, 0xa8, 0x59, 0xb1, 0xf1, 0xae, 0xd5, 0xf2, 0x57, 0x59, 0x23, 0xfa, 0xa7, 0x12, 0x54,
	0xd7, 0xe8, 0xfa, 0x38, 0xed, 0x9a, 0xd6, 0x9b, 0xd1, 0x1e, 0x52, 0xa8, 0x1c, 0xb2, 0x3d, 0x94,
	0xa1, 0x5c, 0x29, 0x45, 0x39, 0x29, 0x71, 0x98, 0x7c, 0x16, 0x11, 0xa7, 0x2f, 0x41, 0x9c, 0x67,
	0x40, 0x0d, 0x67, 0x11, 0x2c, 0x23, 0x23, 0x60, 0x95, 0xf7, 0xdc, 0x8e, 0x0c, 0xa4, 0x38, 0x3a,
	0x36, 0x29, 0x03, 0xc9, 0xd1, 0x91, 0x65, 0x99, 0x02, 0x22, 0xde, 0x76, 0x68, 0x1c, 0xd8, 0x83,
	0x28, 0xeb, 0x43, 0x39, 0xb2, 0x3e, 0x9c, 0x90, 0xf5, 0x0b, 0x30, 0xc2, 0x50, 0x73, 0xac, 0x63,
	0xcb, 0xa1, 0x9a, 0xa9, 0xd2, 0x64, 0x76, 0xfc, 0x2e, 0x69, 0xa1, 0x16, 0x44, 0x6f, 0x3f, 0xb0,
	0x02, 0xb2, 0xbc, 0x51, 0x6e, 0x41, 0x68, 0xc3, 0xa6, 0x89, 0x7e, 0x43, 0x81, 0x89, 0x14, 0xbd,
	0xb1, 0xdf, 0x4d, 0x3f, 0x64, 0x37, 0x73, 0x49, 0xb2, 0x99, 0xaf, 0xc3, 0x24, 0x43, 0x2c, 0x04,
	0x65, 0x78, 0x1d, 0x37, 0xe0, 0x42, 0x3c, 0x41, 0xbb, 0xf8, 0x9c, 0x6b, 0xa4, 0x23, 0xb5, 0x69,
	0xfb, 0x52, 0x9b, 0x16, 0x7d, 0x1c, 0xd4, 0x75, 0x1b, 0x63, 0xcf, 0x39, 0x16, 0x45, 0xa3, 0x0b,
	0xa6, 0xd3, 0x30, 0xe0, 0xf9, 0x82, 0x80, 0xf4, 0x7b, 0xfe, 0x9e, 0x6d, 0xa2, 0xaf, 0x97, 0xa1,
	0xba, 0xe5, 0x99, 0xf6, 0xc1, 0xc3, 0xc7, 0x05, 0x15, 0xc9, 0x66, 0x59, 0xd0, 0xef, 0xf3, 0x30,
	0xe4, 0x9f, 0x98, 0x6c, 0x2b, 0xb0, 0x75, 0x0c, 0xfa, 0x27, 0x26, 0xdd, 0x07, 0x31, 0x17, 0xfb,
	0x13, 0x5c, 0x14, 0x55, 0xd2, 0x80, 0x5c, 0x25, 0x0d, 0xc6, 0x2a, 0x89, 0x48, 0x9a, 0xb0, 0xdf,
	0xb4, 0x7d, 0x3b, 0x68, 0xe9, 0x3e, 0xdf, 0x76, 0xd5, 0x78, 0xdb, 0xdd, 0xa4, 0xed, 0x44, 0x9f,
	0x33, 0xe3, 0x65, 0x52, 0xf5, 0xc5, 0x05, 0x88, 0x19, 0x34, 0xa6, 0xd1, 0x88, 0x14, 0x9d, 0x58,
	0x8e, 0xe1, 0xb5, 0x2c, 0xad, 0x85, 0x0f, 0xb9, 0xfa, 0x03, 0xde, 0xb4, 0x85, 0x0f, 0xd5, 0x1b,
	0x30, 0x95, 0x9a, 0x11, 0x6b, 0xba, 0x69, 0x72, 0x79, 0x9b, 0x48, 0xcc, 0x89, 0x57, 0x4d, 0x53,
	0xfa, 0x02, 0x31, 0x79, 0xa3, 0xb2, 0x17, 0xd6, 0x2d, 0x07, 0x4d, 0xc2, 0x44, 0x8a, 0x27, 0xd8,
	0x47, 0xbf, 0xa4, 0x40, 0x3d, 0xd1, 0xba, 0x91, 0xb2, 0x83, 0x67, 0xe7, 0x9c, 0xdc, 0xdc, 0x32,
	0x0d, 0x91, 0x35, 0xb7, 0x31, 0xe3, 0xfa, 0x44, 0xc6, 0xa1, 0x27, 0xe1, 0x62, 0x17, 0xfc, 0xb0,
	0x8f, 0xbe, 0xa7, 0x40, 0x95, 0x78, 0x8c, 0x91, 0xc7, 0xd7, 0x03, 0xd6, 0xdc, 0xa9, 0x2d, 0xc5,
	0x4e, 0x6d, 0xd2, 0x3d, 0x2f, 0x17, 0xbb, 0xe7, 0x7d, 0x69, 0xf7, 0xfc, 0x1c, 0x00, 0xd1, 0xde,
	0xbe, 0x87, 0x63, 0x1b, 0x40, 0xf4, 0xf9, 0x7d, 0x0f, 0x73, 0x93, 0x88, 0xb5, 0x03, 0xaf, 0x6d,
	0x58, 0xdc, 0xc6, 0x0e, 0xda, 0xf8, 0x16, 0x79, 0x44, 0x3f, 0x54, 0x60, 0x22, 0x85, 0x3c, 0xf6,
	0xd5, 0x06, 0x54, 0x3c, 0xdf, 0x72, 0xa9, 0xcd, 0xe0, 0xb6, 0x4f, 0xb9, 0x3a, 0xf2, 0xdc, 0x4c,
	0x64, 0xfb, 0x12, 0xde, 0x6d, 0x73, 0x84, 0x0c, 0xde, 0xb2, 0x0d, 0x6a, 0x3a, 0xeb, 0x30, 0xfa,
	0xc0, 0x36, 0x1e, 0x68, 0x5e, 0x27, 0x10, 0xcd, 0x1e, 0x69, 0xbb, 0xd7, 0x09, 0x08, 0x6f, 0x5e,
	0x86, 0x51, 0xdd, 0x71, 0x28, 0x70, 0xc7, 0xc6, 0x44, 0x69, 0x94, 0x0b, 0x80, 0x83, 0xee, 0x38,
	0x5b, 0xb6, 0x71, 0xd7, 0xc6, 0x01, 0xd5, 0x4d, 0x56, 0xfb, 0xd8, 0x6a, 0x6b, 0x81, 0x4d, 0x85,
	0x99, 0x52, 0xa2, 0xaf, 0x39, 0xca, 0x5a, 0x77, 0x6d, 0x22, 0xce, 0xe8, 0x16, 0x4c, 0x36, 0x2d,
	0xc7, 0xd2, 0xb1, 0xf5, 0x58, 0x2c, 0x41, 0xff, 0xa0, 0xc0, 0x54, 0x16, 0x10, 0xf6, 0xd5, 0x65,
	0x18, 0x33, 0x1c, 0x0f, 0x5b, 0xbd, 0xd2, 0x67, 0x94, 0x8e, 0x0e, 0x09, 0x94, 0x5e, 0x7e, 0xe9,
	0x31, 0x96, 0x5f, 0xce, 0x2e, 0x5f, 0x5d, 0x82, 0x49, 0x1b, 0x6b, 0x7a, 0x27, 0xf0, 0x34, 0xc1,
	0xf6, 0x73, 0x47, 0xbd, 0x6a, 0xe3, 0xd5, 0x4e, 0xe0, 0xad, 0x47, 0xc6, 0x1f, 0xbd, 0x0d, 0x68,
	0xed, 0xc8, 0x32, 0x1e, 0xac, 0xba, 0xe6, 0x6b, 0xb6, 0xf1, 0x20, 0x0c, 0xc3, 0x6c, 0xa3, 0x7d,
	0xf3, 0xe1, 0x4d, 0x6a, 0x28, 0x09, 0xf1, 0xb2, 0x51, 0x98, 0xd4, 0x04, 0x97, 0xba, 0xfa, 0x27,
	0x65, 0xd1, 0x04, 0xa3, 0xbf, 0x50, 0xe0, 0xc9, 0xae, 0xb3, 0x63, 0x5f, 0x32, 0xfd, 0x02, 0x0c,
	0xdb, 0x58, 0x23, 0x52, 0x65, 0x31, 0xa6, 0x0d, 0x35, 0x87, 0x6c, 0xfc, 0x1a, 0x7d, 0xce, 0x73,
	0x0f, 0x94, 0x47, 0x72, 0x0f, 0x9e, 0x87, 0x19, 0x06, 0x5e, 0x8b, 0xc5, 0x86, 0x71, 0xac, 0xbf,
	0x5e, 0xbe, 0x5a, 0x69, 0x4e, 0xb2, 0xde, 0xb5, 0x50, 0x82, 0x08, 0x8b, 0xd0, 0xdb, 0xb0, 0xb0,
	0x63, 0x05, 0xe1, 0x32, 0x3a, 0x81, 0xc5, 0x17, 0xb6, 0x65, 0x1b, 0x67, 0x57, 0x66, 0x57, 0x60,
	0x3c, 0xd0, 0xdb, 0x87, 0x16, 0xdd, 0x50, 0xf1, 0x9e, 0xa9, 0x34, 0x2b, 0xac, 0x79, 0xcf, 0x66,
	0x93, 0xff, 0x4e, 0x09, 0xce, 0xe5, 0xcf, 0xde, 0xdd, 0xf4, 0x23, 0xa8, 0x90, 0x60, 0x37, 0x9e,
	0xa5, 0x44, 0x67, 0x19, 0x21, 0x8d, 0x7c, 0x0e, 0xe2, 0x51, 0x92, 0x75, 0xf3, 0xc8, 0x54, 0x44,
	0x66, 0x8c, 0xb7, 0x87, 0x23, 0xd7, 0x40, 0xa5, 0xd0, 0xa8, 0x36, 0x88, 0xa4, 0xbd, 0xaf, 0x50,
	0xda, 0xc7, 0xc9, 0x1b, 0x1c, 0x63, 0x0a, 0x24, 0xbd, 0x59, 0xfa, 0x1f, 0x63, 0xb3, 0x0c, 0x48,
	0x74, 0x05, 0x86, 0xc9, 0x3d, 0x17, 0x27, 0x68, 0xf6, 0xc1, 0xf3, 0xe9, 0x75, 0x98, 0x22, 0x13,
	0x25, 0x73, 0x34, 0xdd, 0x67, 0x25, 0x61, 0x6a, 0x04, 0x1e, 0x73, 0xe6, 0x40, 0x04, 0x1a, 0xa3,
	0x3d, 0x98, 0x96, 0xc0, 0xed, 0xce, 0xf7, 0x45, 0x00, 0x42, 0x77, 0x53, 0x84, 0x3b, 0x4c, 0x5b,
	0x28, 0xd8, 0x4f, 0xc2, 0xcc, 0x9e, 0xdb, 0xfa, 0x20, 0x10, 0x9e, 0x87, 0x59, 0x29, 0x64, 0xec,
	0xa3, 0x7b, 0x30, 0x7b, 0x3b, 0x62, 0x4b, 0x1c, 0x99, 0x9d, 0xdd, 0x2d, 0xfc, 0xe7, 0x12, 0xcc,
	0xc9, 0x21, 0x62, 0xff, 0x03, 0x8a, 0xf7, 0x4a, 0xc5, 0xf1, 0x5e, 0xa9, 0x97, 0x78, 0xaf, 0xf4,
	0x5f, 0x38, 0xde, 0xfb, 0x96, 0x02, 0xe7, 0x64, 0x24, 0xbf, 0xa9, 0x07, 0xc6, 0x11, 0xe1, 0x64,
	0xcc, 0x2a, 0x25, 0xb5, 0x8f, 0xd2, 0x2a, 0x97, 0xc9, 0x4e, 0xc5, 0x10, 0x95, 0x2d, 0x41, 0xb4,
	0xa5, 0x9f, 0x6a, 0x87, 0x56, 0xc0, 0xf3, 0x13, 0x14, 0xd1, 0x96, 0x7e, 0x7a, 0x9b, 0xb5, 0x10,
	0x82, 0x63, 0xaf, 0x1d, 0x88, 0xb1, 0xf3, 0x10, 0x69, 0x20, 0x44, 0x44, 0x0f, 0x60, 0xb1, 0x00,
	0x39, 0xec, 0xab, 0x1f, 0x8f, 0xd3, 0x20, 0xcc, 0xb7, 0x66, 0xa8, 0x28, 0x54, 0x05, 0xd5, 0xd2,
	0x72, 0x21, 0x48, 0x54, 0xc8, 0x48, 0xd6, 0x44, 0xb7, 0xfc, 0x37, 0x15, 0x51, 0x9e, 0xe3, 0xfc,
	0xdc, 0xd9, 0x95, 0x4d, 0x32, 0xa2, 0x2a, 0xa7, 0xd3, 0x20, 0x92, 0x0c, 0x52, 0x9f, 0x2c, 0x83,
	0xf4, 0xbf, 0xc4, 0x5d, 0x21, 0xe2, 0x85, 0x7d, 0x75, 0x15, 0xc6, 0x42, 0x18, 0x98, 0xf6, 0xf0,
	0x3d, 0x91, 0x59, 0xbb, 0xf0, 0x5e, 0x08, 0x9e, 0x35, 0xa1, 0xdf, 0x57, 0xe0, 0x1c, 0xa5, 0x68,
	0xde, 0xe2, 0x1f, 0x53, 0x04, 0xae, 0xc0, 0x78, 0x4c, 0x85, 0x84, 0xca, 0x8d, 0x48, 0x41, 0xc7,
	0xdd, 0x80, 0xa9, 0x14, 0x39, 0x62, 0x73, 0x34, 0x1c, 0x31, 0x8c, 0xd1, 0x84, 0x32, 0xec, 0x01,
	0x2c, 0x16, 0xe0, 0x9d, 0x94, 0x0e, 0x46, 0x9c, 0x42, 0xe9, 0x10, 0x5e, 0x9e, 0x48, 0x50, 0x88,
	0x4e, 0x96, 0x50, 0x76, 0x71, 0x62, 0xed, 0xec, 0xca, 0xee, 0x53, 0x22, 0x57, 0x45, 0x80, 0xdd,
	0x8d, 0x01, 0xd9, 0x54, 0x42, 0xd2, 0x8f, 0x6b, 0xb1, 0x56, 0x04, 0x03, 0x6d, 0xc1, 0xf4, 0xed,
	0x84, 0xc1, 0x24, 0x4b, 0x38, 0x3b, 0xaa, 0x01, 0xcc, 0xc8, 0xc0, 0x75, 0x47, 0xf4, 0x55, 0xa0,
	0x36, 0x4a, 0x74, 0xa2, 0xcf, 0xa7, 0xc9, 0x9e, 0x3c, 0x89, 0x68, 0x0e, 0xb5, 0x38, 0x7c, 0x74,
	0x0b, 0xc6, 0x6e, 0x5b, 0x01, 0xf1, 0x1e, 0x1e, 0x0f, 0xfb, 0xf7, 0x4a, 0x30, 0x9e, 0x00, 0xd4,
	0x1d, 0xef, 0x35, 0x16, 0x5c, 0x91, 0x57, 0x1e, 0x05, 0xf7, 0xd1, 0xf0, 0x25, 0x2a, 0xcf, 0xcf,
	0xc2, 0x14, 0xf1, 0x8b, 0xb8, 0x52, 0x37, 0x89, 0xb6, 0x17, 0x74, 0xa0, 0xaa, 0x3b, 0x0e, 0x77,
	0xf1, 0x49, 0x64, 0x47, 0x75, 0x61, 0xda, 0x93, 0xea, 0xeb, 0xd9, 0x93, 0x62, 0xa7, 0x14, 0x6d,
	0x66, 0x2b, 0xe2, 0xd8, 0xb2, 0x4d, 0x8d, 0x45, 0x6f, 0x6e, 0xd6, 0xbb, 0x0a, 0xcc, 0xc7, 0x5c,
	0x26, 0x50, 0xc5, 0xe0, 0x22, 0x47, 0x07, 0xa4, 0xd4, 0x7b, 0x28, 0x89, 0xb1, 0x7a, 0x9f, 0x01,
	0x66, 0xf7, 0xd6, 0xb9, 0x31, 0xe5, 0x4f, 0x04, 0xe1, 0xb4, 0x09, 0x1d, 0xda, 0xe7, 0xb6, 0x13,
	0xfd, 0xa1, 0x02, 0xb5, 0x3c, 0x54, 0xb0, 0x2f, 0xc0, 0x54, 0xf2, 0x61, 0x96, 0x92, 0x30, 0xd5,
	0x57, 0xe3, 0x9c, 0xba, 0x10, 0xd1, 0xe6, 0xbb, 0x0e, 0x61, 0xb6, 0x9d, 0x92, 0xf7, 0x19, 0x50,
	0x03, 0x2f, 0xd0, 0x1d, 0x2d, 0x56, 0x27, 0x6f, 0x85, 0xd6, 0xaa, 0x4a, 0x7b, 0x22, 0x1d, 0xf2,
	0x96, 0x85, 0xee, 0x8a, 0xbb, 0x8f, 0xe2, 0xcd, 0x15, 0xe9, 0x39, 0x88, 0x64, 0x6c, 0x3d, 0x2d,
	0x74, 0xeb, 0x79, 0xe2, 0x6b, 0x8a, 0x9b, 0x2f, 0x86, 0x76, 0x56, 0x3a, 0xc8, 0x8f, 0x8b, 0xd0,
	0x1e, 0x54, 0xf6, 0xb0, 0xd5, 0x5e, 0x8b, 0xb0, 0xc9, 0x86, 0x73, 0x09, 0xec, 0x4b, 0x69, 0xec,
	0xa3, 0x6c, 0x6a, 0x59, 0xc8, 0xa6, 0xa2, 0x0d, 0x50, 0x69, 0xec, 0x48, 0x60, 0x6f, 0xe2, 0xd0,
	0x75, 0xcf, 0xc2, 0x4e, 0xee, 0xc7, 0x14, 0x70, 0x13, 0x69, 0x30, 0x99, 0x01, 0x23, 0x0d, 0x39,
	0x8b, 0xe1, 0x88, 0xa7, 0x85, 0x65, 0xf1, 0xb4, 0x10, 0xdd, 0x82, 0x29, 0x02, 0xfb, 0xb9, 0x28,
	0xf5, 0xe5, 0xe8, 0x81, 0xed, 0xb9, 0x85, 0x54, 0x88, 0x12, 0x27, 0x71, 0x03, 0xfa, 0x92, 0x02,
	0xe7, 0x48, 0x6c, 0x44, 0x80, 0xdd, 0xeb, 0x04, 0xf7, 0x0e, 0x6e, 0x0b, 0x69, 0xd6, 0x82, 0x7d,
	0x14, 0xb3, 0xb2, 0x94, 0xcf, 0xca, 0x72, 0x8a, 0x95, 0xf3, 0x30, 0xd4, 0x11, 0x8d, 0x64, 0xa5,
	0x39, 0xd8, 0xe1, 0xe1, 0xcb, 0x77, 0x15, 0x58, 0x2c, 0xc0, 0xe3, 0xac, 0xc2, 0xf3, 0x71, 0x98,
	0xe8, 0x10, 0x32, 0x49, 0x76, 0xd2, 0x62, 0xb4, 0x93, 0x64, 0x84, 0x6c, 0x56, 0xc5, 0xf7, 0x28,
	0x8a, 0xbf, 0xa7, 0xc0, 0x3c, 0x57, 0x80, 0x71, 0x4a, 0x61, 0xc3, 0x0d, 0xda, 0x0f, 0x0b, 0xe8,
	0xd4, 0x85, 0xbf, 0x32, 0xdf, 0xba, 0x9c, 0xe7, 0x5b, 0xd3, 0xa0, 0xb7, 0x13, 0xe5, 0x14, 0x06,
	0xc9, 0xf3, 0x1e, 0xe5, 0x70, 0x41, 0xa2, 0x0e, 0xfd, 0x44, 0x81, 0x5a, 0x1e, 0xda, 0x3d, 0x65,
	0xee, 0x89, 0x86, 0x67, 0xa9, 0x29, 0x41, 0x65, 0x12, 0xbd, 0xbf, 0x46, 0x1a, 0x29, 0x72, 0x49,
	0x0c, 0xca, 0xa9, 0x54, 0xe1, 0xd9, 0xad, 0x44, 0xd6, 0x10, 0xf4, 0x4b, 0x0c, 0xc1, 0xff, 0x57,
	0x60, 0x6e, 0xc3, 0xfd, 0xa9, 0xf2, 0xa5, 0x78, 0x41, 0xf3, 0x30, 0x14, 0xa6, 0x32, 0x79, 0x0a,
	0x6c, 0x90, 0x1b, 0x52, 0xf4, 0x63, 0x05, 0xe6, 0x73, 0x70, 0xf9, 0xef, 0x41, 0xec, 0x6f, 0x28,
	0x30, 0x45, 0x36, 0xaa, 0xd7, 0x09, 0xe2, 0x15, 0x9e, 0x9d, 0xd0, 0x3d, 0xa6, 0x37, 0x08, 0x98,
	0x7d, 0xdd, 0xd5, 0xb0, 0x65, 0x78, 0x6e, 0x74, 0xd6, 0xb3, 0xaf, 0xbb, 0x3b, 0xb4, 0x01, 0x7d,
	0xa5, 0x04, 0xd3, 0x12, 0xac, 0xba, 0x93, 0x7c, 0x09, 0x26, 0xc5, 0x0d, 0x98, 0x8c, 0x0b, 0xaa,
	0xf1, 0x1e, 0xe4, 0x2e, 0xff, 0xc7, 0x58, 0xa6, 0xca, 0xeb, 0x04, 0xbd, 0xa6, 0x9a, 0xc7, 0xf8,
	0xf8, 0xbc, 0xe4, 0xd3, 0x4f, 0x9b, 0x3f, 0xbf, 0xac, 0xc0, 0x6c, 0x8a, 0x12, 0x51, 0x6a, 0xe5,
	0x83, 0x65, 0xd1, 0x55, 0xa8, 0x12, 0x16, 0xb1, 0x23, 0x92, 0x04, 0xa3, 0xc6, 0xf6, 0x75, 0x97,
	0x1e, 0x73, 0x70, 0x6e, 0x19, 0x30, 0x27, 0x47, 0xb1, 0x3b, 0xbf, 0xae, 0x42, 0x15, 0x77, 0x0c,
	0xc3, 0xc2, 0x38, 0x9d, 0x51, 0x1c, 0xe3, 0xed, 0x61, 0x42, 0xec, 0x13, 0x70, 0x3e, 0x32, 0xc1,
	0x7c, 0xb6, 0x5b, 0x6d, 0xaf, 0xd5, 0xfb, 0xf9, 0x5d, 0x36, 0x79, 0xbf, 0x02, 0x17, 0x0a, 0x41,
	0x62, 0x3f, 0x99, 0x42, 0x56, 0xa8, 0x6e, 0x88, 0x52, 0xc8, 0xe8, 0x1d, 0x05, 0x16, 0x64, 0x0b,
	0xbf, 0x6b, 0xf7, 0x94, 0x21, 0xec, 0x96, 0xfa, 0x52, 0x2f, 0xc2, 0x28, 0xe1, 0x80, 0xd9, 0x69,
	0x53, 0x1b, 0x16, 0x16, 0x54, 0xed, 0xeb, 0xee, 0x3a, 0x6f, 0x42, 0x26, 0x33, 0xf7, 0x72, 0x0c,
	0xba, 0x93, 0xff, 0x32, 0x84, 0xf2, 0x9c, 0xcc, 0xec, 0x55, 0xa2, 0x56, 0x9a, 0x83, 0xbb, 0x2f,
	0x86, 0x8a, 0x37, 0x1f, 0xae, 0x87, 0x61, 0x33, 0x5f, 0xa4, 0x90, 0x63, 0x50, 0xf2, 0xab, 0x83,
	0xb8, 0x53, 0xc9, 0xdc, 0xbd, 0x4f, 0x89, 0xbe, 0x7e, 0x02, 0x22, 0xf6, 0xd5, 0x8f, 0xc4, 0xae,
	0xb2, 0x90, 0x65, 0x2b, 0xca, 0xa6, 0x84, 0xce, 0x32, 0x79, 0x40, 0x6f, 0xc1, 0xa4, 0xe0, 0x4a,
	0x36, 0x3d, 0x27, 0xaf, 0xc4, 0x4c, 0x85, 0x3e, 0x5e, 0xc9, 0x46, 0x9a, 0xe8, 0xef, 0xcc, 0xdc,
	0x65, 0x7a, 0x6a, 0xd3, 0xf3, 0xdc, 0x9f, 0xa7, 0xeb, 0x4a, 0x4d, 0x1f, 0x86, 0x8f, 0x52, 0x77,
	0x51, 0x37, 0x5b, 0xb6, 0xab, 0x09, 0x78, 0x0c, 0xd3, 0x96, 0x64, 0x85, 0x80, 0xe8, 0xd3, 0x26,
	0xcf, 0xf3, 0xfb, 0x52, 0xe7, 0xf9, 0x6f, 0xd0, 0xc8, 0x45, 0x8a, 0x00, 0xf6, 0xd5, 0x57, 0x60,
	0x98, 0xcc, 0x24, 0xa6, 0x21, 0xce, 0x25, 0xfc, 0xa6, 0x14, 0xd1, 0x9a, 0x43, 0x6d, 0xfe, 0x3a,
	0xfa, 0x37, 0x05, 0xa6, 0x85, 0x83, 0x03, 0x96, 0x07, 0x74, 0xce, 0xe2, 0x4d, 0x87, 0x25, 0x6d,
	0x24, 0x56, 0x74, 0x42, 0xa7, 0x92, 0x27, 0x16, 0x1d, 0xf5, 0x69, 0x50, 0x6d, 0x1c, 0x9d, 0x4a,
	0x71, 0xdd, 0xca, 0xcd, 0xf2, 0xb8, 0x8d, 0xb9, 0xeb, 0xb3, 0x4a, 0xf5, 0xa8, 0xfa, 0x14, 0x54,
	0x6d, 0xac, 0xb9, 0x96, 0x65, 0xd2, 0x0a, 0x3d, 0x8d, 0x95, 0xe8, 0xf1, 0x34, 0xe3, 0xb6, 0x65,
	0x99, 0xfc, 0xdc, 0x52, 0x6d, 0xc0, 0x08, 0x53, 0x95, 0x71, 0x49, 0x86, 0x84, 0xab, 0x71, 0x29,
	0x64, 0x73, 0x98, 0xea, 0x52, 0x5a, 0xb4, 0xf8, 0xaf, 0x7d, 0x30, 0x23, 0x5b, 0x79, 0xf7, 0xed,
	0x25, 0x26, 0x50, 0x19, 0x15, 0xa2, 0x04, 0xea, 0x35, 0x98, 0x10, 0x03, 0x73, 0xd1, 0x55, 0x1c,
	0x37, 0xe3, 0xb0, 0x9c, 0x7a, 0x08, 0x08, 0x2a, 0xa1, 0xaf, 0x28, 0x3a, 0xdb, 0x23, 0xdc, 0x61,
	0x0c, 0xb5, 0xb5, 0xe5, 0xa6, 0xc0, 0x31, 0xd7, 0x71, 0x8c, 0xb7, 0x87, 0xd0, 0xea, 0x30, 0x1a,
	0xd2, 0x8a, 0xda, 0x0e, 0x96, 0xfb, 0x85, 0x23, 0x46, 0x29, 0x62, 0x40, 0x3e, 0x96, 0xf4, 0x62,
	0xe9, 0x94, 0x83, 0xc5, 0x56, 0x31, 0xb6, 0xac, 0x14, 0x9b, 0x15, 0x18, 0x67, 0xb3, 0xc6, 0x00,
	0x86, 0x0a, 0x01, 0x54, 0xd8, 0xf0, 0xf0, 0x7d, 0x99, 0x5d, 0x1e, 0x7e, 0x24, 0xbb, 0xdc, 0x80,
	0x4a, 0xb4, 0x4a, 0xba, 0x91, 0xa1, 0xf8, 0x78, 0x9a, 0x2f, 0x5f, 0x7a, 0xfa, 0x3a, 0xf2, 0x18,
	0x36, 0x7d, 0x54, 0x72, 0xfa, 0x8a, 0xa0, 0x72, 0xd0, 0xf6, 0x5a, 0x5a, 0x24, 0x1b, 0x15, 0xa6,
	0xd8, 0x49, 0x23, 0x17, 0x2f, 0xf4, 0xed, 0x12, 0x2c, 0x24, 0xea, 0x72, 0x62, 0x25, 0xb9, 0xde,
	0x83, 0xda, 0x0d, 0xab, 0x52, 0x4a, 0x42, 0xc5, 0xd4, 0x4f, 0xa5, 0x20, 0x2a, 0x52, 0x49, 0xfd,
	0xa2, 0x4a, 0x8a, 0x8b, 0x23, 0x06, 0xd2, 0x55, 0x2d, 0x05, 0x27, 0x08, 0xf4, 0x5c, 0xc3, 0xcb,
	0x56, 0x6a, 0x7a, 0xe1, 0x49, 0x48, 0xac, 0xe6, 0x86, 0x53, 0x6a, 0xee, 0xff, 0x2a, 0x70, 0x2e,
	0x9f, 0x3c, 0x3d, 0x1d, 0x67, 0x15, 0xd5, 0xb4, 0x66, 0x0b, 0x9c, 0xca, 0xd9, 0x02, 0x27, 0xf4,
	0x32, 0x4c, 0xd2, 0x63, 0x8c, 0x5d, 0xab, 0xe5, 0x0b, 0x7e, 0x48, 0xba, 0xc0, 0x95, 0x4d, 0x2e,
	0x16, 0xb8, 0xa2, 0xbf, 0x51, 0x60, 0x2a, 0xfb, 0x6a, 0x77, 0xb4, 0x7b, 0x28, 0xcc, 0xec, 0x09,
	0x75, 0xc1, 0x2c, 0xf7, 0x09, 0x66, 0x59, 0x52, 0x9b, 0x9b, 0x46, 0x3d, 0x45, 0xb9, 0x81, 0x74,
	0x95, 0xd6, 0x0e, 0x4c, 0xf3, 0x7a, 0x88, 0x14, 0x55, 0xba, 0xac, 0xac, 0x28, 0xec, 0x47, 0x73,
	0x30, 0x23, 0x03, 0x8a, 0x7d, 0xf4, 0x19, 0x98, 0xa3, 0x29, 0xf8, 0xb3, 0xf1, 0x81, 0x08, 0xe1,
	0x3e, 0x79, 0x9d, 0x97, 0xa6, 0xf1, 0xf4, 0x21, 0x6d, 0xa2, 0x35, 0x69, 0x48, 0x83, 0xf9, 0x1c,
	0xf8, 0xd8, 0x57, 0x6f, 0x42, 0x85, 0x1e, 0x70, 0x85, 0x35, 0xf9, 0xdc, 0xa2, 0xc6, 0x99, 0x08,
	0xd9, 0x5b, 0xcd, 0xd1, 0x20, 0x6e, 0xc0, 0xe8, 0x53, 0x50, 0xe3, 0xa7, 0x49, 0x32, 0xa2, 0x91,
	0x4d, 0x12, 0x11, 0x8d, 0xc1, 0x27, 0x9b, 0x24, 0xa4, 0x1a, 0x2e, 0x26, 0xdb, 0x22, 0x2c, 0xe4,
	0xc2, 0xc6, 0x3e, 0x5a, 0x86, 0xd9, 0x4f, 0x74, 0xac, 0xf6, 0x43, 0xa1, 0x1d, 0x6f, 0x77, 0x5a,
	0x3d, 0x8a, 0x70, 0x13, 0xe6, 0xe4, 0x6f, 0x33, 0xaf, 0x99, 0xa5, 0x2b, 0xdd, 0x4e, 0x8b, 0xbf,
	0x3b, 0x44, 0x1b, 0xb6, 0x3b, 0x2d, 0x5a, 0x69, 0x64, 0x3a, 0x16, 0xed, 0xe3, 0x46, 0x91, 0x3c,
	0x6f, 0x77, 0x5a, 0xe8, 0x4d, 0x58, 0xa0, 0x0e, 0xf9, 0x6e, 0x04, 0x72, 0x13, 0xf3, 0x83, 0xc2,
	0x33, 0x39, 0x1a, 0xe9, 0x65, 0x94, 0xb3, 0xcb, 0xf8, 0x08, 0x9c, 0xcb, 0x9f, 0x92, 0x6d, 0x48,
	0x1b, 0x47, 0x47, 0x98, 0x4a, 0x58, 0xfe, 0x1f, 0x1e, 0x5f, 0xde, 0x81, 0x0b, 0x94, 0xc4, 0x14,
	0xc6, 0x66, 0x7c, 0xb2, 0x29, 0xf0, 0xf0, 0x72, 0x74, 0x42, 0x16, 0x9a, 0x6f, 0x25, 0x71, 0x4a,
	0xc5, 0xe3, 0x9b, 0x6d, 0xa8, 0x17, 0x43, 0xc2, 0x3e, 0xd1, 0xe0, 0x44, 0x78, 0x28, 0x36, 0x5a,
	0x12, 0xda, 0x78, 0xd4, 0xc1, 0x0f, 0xa7, 0x7e, 0xa4, 0xc0, 0xf8, 0x9a, 0xe7, 0x1e, 0xd8, 0x87,
	0x5b, 0x1d, 0xcc, 0x0d, 0x1b, 0x3d, 0xc4, 0xc7, 0xb6, 0xa1, 0x09, 0xd5, 0xb4, 0xc3, 0xb4, 0x85,
	0xaa, 0x8e, 0x8b, 0x30, 0x6a, 0x7a, 0x27, 0xae, 0xe3, 0xe9, 0xa6, 0xd6, 0x69, 0x3b, 0xa1, 0x76,
	0x09, 0xdb, 0xf6, 0xda, 0x0e, 0x51, 0xf4, 0x7a, 0x3b, 0x60, 0x31, 0x23, 0x55, 0xf4, 0xec, 0x89,
	0x26, 0x36, 0x5b, 0x87, 0xf4, 0x2d, 0x5e, 0x1e, 0x67, 0xb7, 0x0e, 0xc9, 0x0b, 0xc4, 0x02, 0xd0,
	0x29, 0x23, 0x93, 0x31, 0x48, 0x9f, 0x99, 0x0e, 0xd6, 0x9d, 0xfd, 0x4e, 0x8b, 0x61, 0xc3, 0x0c,
	0xc7, 0x30, 0x6d, 0xa1, 0xd8, 0x44, 0xc8, 0x52, 0xd6, 0x31, 0xeb, 0xc1, 0x90, 0xa5, 0x8c, 0xbb,
	0x0f, 0x13, 0xab, 0xa6, 0x29, 0xac, 0x90, 0xd0, 0xfa, 0x55, 0x18, 0x35, 0x68, 0x8b, 0x46, 0x07,
	0x66, 0xcf, 0xe7, 0x93, 0x04, 0x69, 0x8e, 0x18, 0x71, 0x03, 0xba, 0x01, 0x6a, 0x1a, 0x22, 0xf6,
	0x13, 0x0b, 0x50, 0xb8, 0x0f, 0xc7, 0x16, 0x80, 0x5e, 0x82, 0x89, 0x75, 0xcb, 0x49, 0xa1, 0x70,
	0x11, 0x46, 0x69, 0x7f, 0x92, 0x3d, 0x23, 0xbc, 0x8d, 0xb2, 0x66, 0x0a, 0xd4, 0xf4, 0x7b, 0xd8,
	0x47, 0xb3, 0x2c, 0x6b, 0x1f, 0xb7, 0xf2, 0xb0, 0x01, 0xed, 0xb0, 0x04, 0x7c, 0xba, 0x83, 0xba,
	0xf3, 0x7c, 0x01, 0xa2, 0x43, 0x9f, 0xbf, 0x5a, 0x60, 0x83, 0x29, 0x0e, 0x2f, 0x89, 0x67, 0x04,
	0x6b, 0xbc, 0xc0, 0xa0, 0xbb, 0x9e, 0x46, 0x2f, 0x8a, 0xa7, 0x01, 0xf1, 0x7b, 0x6c, 0xd3, 0xc7,
	0x95, 0x0b, 0x4a, 0xb2, 0x72, 0x01, 0xfd, 0x56, 0x89, 0xa8, 0x70, 0xc3, 0x6b, 0x9b, 0x3b, 0x96,
	0x6b, 0xde, 0xb6, 0x0f, 0x82, 0x8d, 0x63, 0xcb, 0xed, 0xe5, 0x50, 0x6d, 0x1e, 0x86, 0xb0, 0xe5,
	0x9a, 0xc2, 0xb9, 0xc4, 0x20, 0x79, 0x26, 0x1e, 0xc2, 0x3c, 0x0c, 0xb5, 0x2d, 0xe3, 0x98, 0x5f,
	0x7f, 0xa1, 0x5d, 0xe4, 0x79, 0x8f, 0x55, 0x51, 0x1f, 0xda, 0x07, 0x41, 0x6c, 0xe0, 0x06, 0xc8,
	0x23, 0x03, 0x47, 0x3b, 0x0c, 0x37, 0xe0, 0xd6, 0x8d, 0x0e, 0x5c, 0x73, 0xa9, 0xf3, 0x46, 0xbb,
	0x3a, 0xae, 0x1d, 0x68, 0x86, 0x87, 0x03, 0x7a, 0xa3, 0xa7, 0xd2, 0x1c, 0x25, 0xad, 0x7b, 0xae,
	0x1d, 0xac, 0x79, 0x38, 0xa0, 0xc9, 0xc2, 0xb6, 0x69, 0xb5, 0x35, 0x3b, 0xbc, 0x74, 0x30, 0x48,
	0x9f, 0x59, 0x66, 0x85, 0x02, 0xf0, 0xdb, 0xb6, 0x61, 0x31, 0xb1, 0x65, 0x6e, 0x4d, 0x85, 0x34,
	0xdf, 0x27, 0xad, 0xd4, 0xea, 0xa4, 0xd5, 0xd2, 0x70, 0xe6, 0x06, 0x0c, 0x9a, 0x87, 0x59, 0x29,
	0xb9, 0xb0, 0x8f, 0x3e, 0x0d, 0x13, 0x2c, 0x4e, 0x59, 0xf3, 0x5c, 0xdc, 0x69, 0xe5, 0x05, 0xb7,
	0x2f, 0xc0, 0x8c, 0xc1, 0x06, 0x68, 0xf1, 0xad, 0x23, 0xba, 0x6c, 0x46, 0xc5, 0x29, 0xde, 0xdb,
	0x8c, 0x3a, 0xd7, 0xdc, 0x00, 0x59, 0x30, 0xc1, 0x64, 0x8d, 0x74, 0xed, 0x7a, 0xfe, 0x76, 0x8f,
	0xa6, 0xdb, 0x3a, 0xb4, 0x89, 0xaf, 0x71, 0x1a, 0xd9, 0x20, 0xd2, 0xb0, 0x69, 0x9e, 0x12, 0x7f,
	0xd1, 0xb1, 0x5b, 0x76, 0x78, 0xeb, 0x8a, 0x3d, 0x20, 0x1f, 0xd4, 0xf4, 0x34, 0x3c, 0xee, 0xe7,
	0x28, 0x4b, 0xcf, 0xc9, 0x33, 0xcb, 0xa6, 0xfb, 0x97, 0x3c, 0x84, 0x67, 0x90, 0xfc, 0x90, 0x8c,
	0x2e, 0x52, 0x89, 0xac, 0x0e, 0x59, 0xd8, 0x36, 0x2c, 0xf0, 0xb8, 0x78, 0xd5, 0x30, 0x3a, 0xad,
	0x8e, 0x43, 0x5c, 0xc7, 0x90, 0x02, 0x67, 0xc8, 0x1d, 0xbd, 0x44, 0xcb, 0x56, 0x72, 0xe0, 0xb1,
	0xe3, 0x0d, 0xbd, 0x45, 0x9d, 0x0a, 0x76, 0x8d, 0x8e, 0x3f, 0x21, 0x37, 0xba, 0x8c, 0x43, 0xb8,
	0xba, 0x13, 0xe8, 0x81, 0x28, 0xab, 0x4a, 0xae, 0xac, 0x96, 0x92, 0xb2, 0xfa, 0x14, 0x17, 0x35,
	0xe1, 0x8a, 0x19, 0x23, 0x30, 0x15, 0xe1, 0x98, 0xa7, 0x28, 0x00, 0x95, 0xcc, 0x77, 0x68, 0xa5,
	0xa3, 0x87, 0x2e, 0x89, 0x6b, 0xd7, 0x3a, 0xd1, 0x32, 0x1e, 0xf2, 0xa8, 0x6b, 0x9d, 0xac, 0x8b,
	0xb7, 0x68, 0x5a, 0xfa, 0x21, 0xd3, 0x8c, 0x7c, 0xfb, 0xd1, 0xe7, 0x4d, 0x13, 0x4d, 0xc3, 0x64,
	0x66, 0x56, 0xec, 0xa3, 0x5b, 0x30, 0xc6, 0x17, 0x7f, 0xc7, 0xc6, 0x81, 0xd7, 0x7e, 0x28, 0x91,
	0x5b, 0x04, 0x15, 0x47, 0xc7, 0x01, 0xcf, 0x3b, 0x46, 0x97, 0xf8, 0x46, 0x48, 0x23, 0x4d, 0x3a,
	0xee, 0x62, 0xe4, 0x88, 0xf9, 0x28, 0x0e, 0xaa, 0xc7, 0x33, 0xfa, 0x33, 0x08, 0xeb, 0xe7, 0xc4,
	0x5c, 0x55, 0x62, 0x36, 0xec, 0xab, 0x2f, 0x01, 0x30, 0x4c, 0x05, 0x89, 0x9d, 0x4d, 0xe7, 0x15,
	0xf8, 0x4b, 0xcd, 0x61, 0x3a, 0x34, 0x8c, 0xd7, 0x49, 0x8c, 0x79, 0xc4, 0x7a, 0xc4, 0xd3, 0x81,
	0x31, 0xdd, 0x09, 0x5f, 0xa0, 0xa7, 0xb9, 0xcf, 0xb1, 0x2d, 0xc9, 0xcf, 0x77, 0x03, 0xbd, 0x87,
	0x55, 0xa2, 0x7d, 0xb6, 0xbf, 0xc4, 0x77, 0xb0, 0x4f, 0xd8, 0xda, 0xd2, 0x4f, 0x35, 0xcf, 0x75,
	0x6c, 0xd7, 0xa2, 0x52, 0xc5, 0x5e, 0x1c, 0x6d, 0xe9, 0xa7, 0xf7, 0x68, 0x23, 0x11, 0xad, 0x2b,
	0x30, 0x2e, 0x8c, 0x22, 0x71, 0x2c, 0x47, 0xac, 0x12, 0x0d, 0x23, 0x71, 0x2c, 0xfa, 0x28, 0x8c,
	0xf2, 0x09, 0x56, 0xcd, 0x96, 0xed, 0x3e, 0x72, 0x76, 0x0b, 0xdd, 0x15, 0x91, 0xa4, 0x30, 0x7a,
	0xe0, 0xdf, 0x2c, 0x0c, 0x7a, 0xbe, 0x58, 0x35, 0x3e, 0xe0, 0xf9, 0xc1, 0x9e, 0x6d, 0xa2, 0xd7,
	0x60, 0x32, 0x03, 0x0d, 0xfb, 0xea, 0x0b, 0x21, 0x0e, 0x02, 0x7f, 0xa6, 0xd3, 0xfc, 0x61, 0xc3,
	0x19, 0x6a, 0xd4, 0x3a, 0x1e, 0x33, 0x57, 0xe0, 0xd1, 0x50, 0xcb, 0x16, 0xec, 0x87, 0x09, 0xc6,
	0xb2, 0x90, 0x60, 0xa4, 0x61, 0xed, 0x29, 0x0f, 0x38, 0xa2, 0xec, 0xdd, 0x29, 0x0b, 0x37, 0xa6,
	0x61, 0x32, 0x33, 0x2f, 0xf6, 0xd1, 0x1d, 0x12, 0x54, 0xb5, 0xbc, 0xe8, 0xe2, 0xcb, 0x99, 0x31,
	0x62, 0x91, 0x54, 0x16, 0x12, 0xf6, 0xd1, 0x97, 0x95, 0x44, 0x59, 0xb2, 0x6d, 0xd0, 0x0c, 0x07,
	0x91, 0x9d, 0x0e, 0x3e, 0x7b, 0x05, 0xda, 0xff, 0x60, 0x09, 0x00, 0x9e, 0x4a, 0x2d, 0x15, 0xa4,
	0x51, 0x06, 0x5b, 0xcc, 0x4b, 0x41, 0xdf, 0x51, 0x12, 0x15, 0xca, 0x29, 0x44, 0xb0, 0x9f, 0x49,
	0xcf, 0x28, 0x3d, 0xa7, 0x67, 0x16, 0x01, 0x78, 0xb9, 0x76, 0x2c, 0x3f, 0xc3, 0xac, 0x85, 0x20,
	0xdb, 0x53, 0xed, 0x3c, 0xfa, 0xa2, 0x12, 0x2a, 0x37, 0x32, 0x91, 0xe7, 0x1f, 0x79, 0xae, 0x75,
	0xf6, 0xd3, 0x98, 0x97, 0x60, 0x24, 0xf0, 0xb4, 0x1e, 0x69, 0x34, 0x1c, 0x78, 0x3c, 0x47, 0x85,
	0xbe, 0x54, 0x82, 0xa9, 0x2c, 0x16, 0xec, 0x5e, 0x46, 0x94, 0x5c, 0x12, 0xb2, 0xe7, 0xb9, 0x89,
	0x2f, 0x9e, 0x74, 0xa2, 0x5e, 0x44, 0x0a, 0x99, 0x52, 0x8f, 0xc8, 0xf4, 0x78, 0xe9, 0xe0, 0xec,
	0x47, 0x65, 0x62, 0x1a, 0xa9, 0x3f, 0x91, 0x46, 0x42, 0xaf, 0x88, 0xa6, 0x60, 0x37, 0xbe, 0xd0,
	0xd4, 0x83, 0x92, 0x5c, 0x11, 0xf5, 0x7a, 0xe2, 0x55, 0x9c, 0xbd, 0x32, 0xc5, 0x82, 0x25, 0xf1,
	0xca, 0x14, 0x7a, 0x59, 0xac, 0xc8, 0x7b, 0x23, 0xba, 0x29, 0xd5, 0xc3, 0xcc, 0xaf, 0x8a, 0x48,
	0x8b, 0x6f, 0x62, 0x3f, 0x7d, 0x11, 0x8b, 0xcd, 0x2b, 0x5c, 0xc4, 0x42, 0x3b, 0xb0, 0x10, 0x27,
	0xc1, 0x37, 0x5a, 0x7e, 0xf0, 0xf0, 0x51, 0xae, 0xb0, 0xe4, 0x14, 0xf9, 0xfc, 0xb1, 0x02, 0xe7,
	0xf2, 0xa1, 0x62, 0x5f, 0x7d, 0x06, 0x54, 0x21, 0x27, 0x9c, 0x0c, 0x6e, 0x84, 0x73, 0x55, 0x9e,
	0x8d, 0x3e, 0xfb, 0xfd, 0x15, 0x91, 0xd5, 0xe5, 0x64, 0xca, 0x5c, 0x76, 0xb3, 0xa7, 0x94, 0xd9,
	0x9e, 0x4b, 0x30, 0xba, 0x13, 0xe8, 0xed, 0xe0, 0xae, 0x7d, 0xdc, 0x03, 0x3d, 0xd0, 0x38, 0x54,
	0x84, 0xe1, 0xd8, 0x47, 0x1b, 0x50, 0xb9, 0x65, 0xbb, 0x36, 0x3e, 0xea, 0x0d, 0x40, 0x82, 0xa0,
	0x4a, 0x4c, 0xd0, 0xdf, 0x55, 0x60, 0x4c, 0x84, 0x83, 0x7d, 0x62, 0xba, 0x3a, 0xd8, 0x6a, 0x6b,
	0xfe, 0x71, 0xe8, 0xe7, 0x91, 0xc7, 0xfb, 0xc7, 0x51, 0x47, 0xe7, 0x38, 0xac, 0xbe, 0x21, 0x8f,
	0x7b, 0xc7, 0x64, 0xc5, 0x81, 0xe1, 0xd9, 0xae, 0x16, 0xb9, 0x81, 0x3c, 0x97, 0x47, 0x5b, 0x6f,
	0x73, 0x5f, 0xf0, 0x0a, 0x8c, 0x8b, 0xa3, 0x3c, 0xba, 0xb5, 0xa8, 0xc1, 0x8e, 0x87, 0x79, 0xac,
	0x14, 0x9a, 0x12, 0x8e, 0x9f, 0xe5, 0xb2, 0xe8, 0x07, 0x48, 0x13, 0x3f, 0xc7, 0xfd, 0x30, 0xcc,
	0xf0, 0xec, 0x03, 0xc1, 0x99, 0x92, 0xc5, 0x76, 0x7b, 0x91, 0xe7, 0x17, 0x60, 0x56, 0xfa, 0x22,
	0x0b, 0x9f, 0x31, 0x79, 0x8e, 0x83, 0xc2, 0x41, 0xfa, 0xbc, 0x8b, 0xd1, 0x0b, 0x30, 0xb9, 0xc1,
	0xfc, 0xa1, 0x47, 0xb8, 0xd1, 0x89, 0x66, 0x60, 0x2a, 0xfb, 0x16, 0xf6, 0xd1, 0x6b, 0xa0, 0x7e,
	0xa2, 0x63, 0x07, 0x8f, 0x04, 0x8c, 0x78, 0xe9, 0x07, 0x94, 0x49, 0x94, 0x79, 0x43, 0x4d, 0xfe,
	0x44, 0xec, 0x70, 0x06, 0x18, 0xf6, 0xd1, 0x01, 0xcc, 0xd0, 0x26, 0xcf, 0x75, 0x2d, 0x23, 0xd8,
	0xb2, 0x8d, 0x55, 0xdf, 0x77, 0x1e, 0x9e, 0xc9, 0x35, 0x60, 0x07, 0xcb, 0x86, 0xee, 0x1a, 0xfc,
	0x0c, 0x8c, 0x1e, 0x2c, 0xaf, 0xd1, 0x67, 0xb4, 0x0a, 0xb3, 0xd2, 0x79, 0xb0, 0x4f, 0x98, 0xdd,
	0xd6, 0x89, 0x3f, 0xa3, 0x93, 0x26, 0xc1, 0x89, 0xab, 0xd0, 0x66, 0x3a, 0x90, 0xc4, 0x3b, 0x5f,
	0x53, 0xd2, 0x30, 0xee, 0xe8, 0xae, 0xe9, 0x58, 0xbd, 0x79, 0xff, 0x0c, 0x78, 0xea, 0x44, 0x7e,
	0x94, 0xb6, 0x86, 0x07, 0x4e, 0xb1, 0xdc, 0x97, 0x45, 0x73, 0xc7, 0xee, 0x11, 0xea, 0x8e, 0xe3,
	0x9d, 0x50, 0x29, 0xa4, 0xf7, 0x08, 0x57, 0xc9, 0x23, 0xfa, 0x81, 0x02, 0x73, 0x72, 0x94, 0x98,
	0x6f, 0x9a, 0xda, 0xdc, 0x8a, 0xc4, 0x84, 0xf4, 0x5c, 0x2e, 0xf0, 0x18, 0x17, 0x08, 0x25, 0x14,
	0xee, 0x93, 0x51, 0x58, 0x83, 0x8b, 0xb7, 0xad, 0x40, 0xc2, 0x27, 0x12, 0x14, 0xf6, 0x1e, 0x8d,
	0xd0, 0x18, 0x43, 0x0c, 0x59, 0x69, 0x03, 0x99, 0xc0, 0x00, 0xd4, 0x6d, 0x02, 0xb6, 0xc1, 0x3a,
	0x49, 0x75, 0x1c, 0x16, 0xe1, 0x91, 0x50, 0x8a, 0xd0, 0x20, 0x5e, 0x07, 0x9b, 0x61, 0x44, 0x77,
	0x9c, 0x68, 0x15, 0x37, 0x68, 0x91, 0x33, 0xdf, 0xb6, 0xbd, 0x84, 0x16, 0xbf, 0xa9, 0xd0, 0x6a,
	0xe6, 0xf8, 0x8d, 0xff, 0x04, 0x9a, 0x4d, 0x54, 0x32, 0xfd, 0x49, 0x25, 0x73, 0x1f, 0xa6, 0x12,
	0xe7, 0x44, 0x61, 0x6d, 0xc6, 0xcb, 0xc0, 0x6f, 0xdd, 0x68, 0x6d, 0xeb, 0x4d, 0x7e, 0x37, 0x73,
	0x3e, 0x8e, 0x0b, 0x52, 0x5f, 0x20, 0x68, 0xf2, 0xd4, 0x56, 0xd3, 0x7a, 0x13, 0xed, 0xc2, 0xb4,
	0x04, 0x22, 0xf6, 0xd5, 0x57, 0xc3, 0xbb, 0x3d, 0x5a, 0xdb, 0xc2, 0x3e, 0x87, 0x59, 0xcb, 0x83,
	0x89, 0xfd, 0xf0, 0xde, 0x0f, 0x55, 0x2d, 0xeb, 0xfc, 0x1c, 0x20, 0x59, 0x6a, 0x1b, 0x5e, 0x70,
	0x96, 0x5c, 0x75, 0x50, 0x24, 0x57, 0x1d, 0x50, 0x93, 0x67, 0xfc, 0x65, 0x50, 0xb0, 0xaf, 0x3e,
	0x0f, 0x33, 0x99, 0x13, 0x40, 0x11, 0xda, 0x64, 0xea, 0x18, 0x90, 0xc2, 0xbc, 0x0d, 0x53, 0x4d,
	0xeb, 0xc0, 0x3e, 0xe5, 0x00, 0xef, 0x9d, 0xb8, 0xac, 0xfa, 0xe8, 0x91, 0x8b, 0x68, 0x67, 0x49,
	0x14, 0x93, 0x01, 0x84, 0x7d, 0x96, 0x83, 0xd7, 0xe9, 0x05, 0x4f, 0xe9, 0x4c, 0x04, 0xae, 0xe7,
	0x06, 0xd6, 0x69, 0x20, 0x4a, 0x24, 0x6b, 0xd9, 0x34, 0xd1, 0x05, 0x58, 0x2c, 0x78, 0x9d, 0xc2,
	0x9f, 0x8b, 0xa3, 0xaa, 0x5d, 0xef, 0x8d, 0x23, 0x3b, 0xbe, 0x90, 0x70, 0x31, 0x55, 0x96, 0xcd,
	0xd3, 0xb5, 0x62, 0xa1, 0xe8, 0x02, 0xcc, 0xe7, 0xbc, 0x8e, 0x7d, 0xf4, 0x6a, 0x18, 0x50, 0xdd,
	0x6a, 0x7b, 0xad, 0x47, 0x85, 0x4c, 0xb3, 0x7c, 0x92, 0x97, 0xb1, 0x4f, 0x02, 0x35, 0xc1, 0x45,
	0x14, 0xe0, 0xa2, 0xe5, 0x84, 0xdb, 0x29, 0xbe, 0xd4, 0xcb, 0x94, 0x7f, 0xab, 0x84, 0x71, 0xc3,
	0x5a, 0x9c, 0x6e, 0xec, 0x41, 0x53, 0xa5, 0x73, 0x96, 0xa5, 0xec, 0x61, 0x5a, 0xe2, 0x06, 0x6f,
	0x49, 0x38, 0x33, 0x5e, 0x8c, 0xf6, 0x5a, 0x27, 0xca, 0xc7, 0xf2, 0x0d, 0x45, 0x4c, 0x06, 0xbb,
	0x8c, 0x4c, 0x8b, 0x38, 0x0c, 0xc7, 0xd2, 0x5d, 0x8d, 0xc6, 0xe1, 0xbc, 0x8e, 0xa3, 0xca, 0xea,
	0x38, 0xd6, 0x48, 0x07, 0xcb, 0x33, 0x88, 0x69, 0xa7, 0x81, 0x64, 0xda, 0x69, 0x96, 0x7d, 0x5e,
	0x2a, 0xb5, 0x36, 0xec, 0xa3, 0x2f, 0x28, 0xc4, 0xb5, 0x11, 0x7a, 0xd8, 0x2d, 0x9e, 0x1e, 0xd6,
	0xbd, 0x04, 0x93, 0xae, 0x75, 0xa2, 0xa5, 0xef, 0x49, 0xb1, 0xa3, 0x91, 0xaa, 0x6b, 0x9d, 0x24,
	0x00, 0x16, 0xe5, 0xc4, 0xe6, 0xd9, 0xf7, 0xab, 0x32, 0x28, 0x60, 0xff, 0xda, 0x5b, 0xf1, 0x17,
	0x7a, 0xc2, 0x03, 0xf8, 0x19, 0x50, 0x6f, 0xef, 0x6d, 0xde, 0x5d, 0xd7, 0x6e, 0x6e, 0x6e, 0xaf,
	0x6b, 0x6b, 0x77, 0x56, 0xb7, 0xb7, 0x37, 0xee, 0x56, 0x15, 0x75, 0x1a, 0x26, 0x6e, 0x35, 0x37,
	0x36, 0x92, 0xcd, 0x25, 0xd2, 0xbc, 0xb7, 0xb3, 0xd1, 0x4c, 0x36, 0x97, 0xd5, 0x8b, 0xb0, 0xc8,
	0xa0, 0xdc, 0xdf, 0xbb, 0x79, 0x77, 0x73, 0x4d, 0xbb, 0xb5, 0xb7, 0x9d, 0x1c, 0xd2, 0x77, 0xed,
	0xbb, 0x0a, 0x54, 0x13, 0xc9, 0x00, 0xcf, 0xb1, 0xd4, 0x39, 0x98, 0xe2, 0x23, 0xb4, 0xcd, 0xed,
	0xd7, 0x57, 0xef, 0x6e, 0xae, 0x6b, 0xcd, 0x7b, 0x77, 0x37, 0xaa, 0x4f, 0xa8, 0x13, 0x50, 0x09,
	0x7b, 0xee, 0xbd, 0xb1, 0xbd, 0xd1, 0xac, 0x2a, 0x62, 0xd3, 0xea, 0xfa, 0xd6, 0xe6, 0x76, 0xb5,
	0x44, 0xb0, 0x0f, 0x9b, 0xb6, 0xef, 0x6d, 0xad, 0xde, 0x65, 0x6f, 0x97, 0xd5, 0x59, 0x98, 0x4c,
	0x0c, 0xd5, 0x76, 0xf6, 0xee, 0x6f, 0x34, 0xab, 0x7d, 0xea, 0x02, 0xcc, 0x24, 0x3b, 0xc8, 0x0b,
	0xda, 0xd6, 0xea, 0x27, 0xab, 0xff, 0xae, 0x5c, 0xfb, 0x70, 0x44, 0x9e, 0x1d, 0x7e, 0x31, 0x4f,
	0x1d, 0x85, 0xa1, 0xed, 0x7b, 0xbb, 0xda, 0xce, 0xbd, 0xe6, 0x6e, 0xf5, 0x09, 0x32, 0x1d, 0xf9,
	0xa5, 0xdd, 0xfc, 0x9f, 0x1a, 0xa5, 0xc2, 0xda, 0xbd, 0xbd, 0xed, 0xdd, 0xaa, 0xf2, 0xdc, 0xaf,
	0xde, 0x86, 0xf0, 0xb3, 0x68, 0xea, 0x0f, 0x15, 0xa8, 0x24, 0x54, 0xb0, 0x9a, 0xaf, 0xee, 0x6b,
	0x05, 0x5a, 0x1b, 0xbd, 0xab, 0xbc, 0xf3, 0xde, 0xfb, 0x65, 0xe5, 0x67, 0xde, 0x7b, 0xbf, 0x5c,
	0x75, 0x1b, 0x9d, 0xc6, 0x69, 0x63, 0xbf, 0xa1, 0x37, 0x8e, 0x1a, 0x7e, 0x03, 0x37, 0xbe, 0xfe,
	0xde, 0xfb, 0x65, 0x7b, 0xc9, 0xad, 0x2f, 0x73, 0x79, 0xa9, 0xbb, 0x7a, 0xcb, 0x5a, 0xa9, 0x2f,
	0x75, 0xea, 0xcb, 0x1d, 0xdb, 0x5c, 0xa9, 0x2f, 0x9d, 0x46, 0x5d, 0x64, 0x17, 0xad, 0xd4, 0x97,
	0xf6, 0xeb, 0xcb, 0x64, 0x97, 0xd0, 0x4e, 0xbd, 0xbe, 0x4c, 0xcb, 0x29, 0x56, 0xea, 0x4b, 0x47,
	0xf5, 0xe5, 0x23, 0x1d, 0xfb, 0x27, 0xe4, 0xb7, 0x5f, 0x5f, 0x66, 0xe5, 0x14, 0x2b, 0xf5, 0x25,
	0x5c, 0x5f, 0x26, 0x96, 0x8d, 0x7e, 0xea, 0x65, 0x45, 0xf5, 0x60, 0x3c, 0xf5, 0x49, 0x14, 0x75,
	0x21, 0xc2, 0x3c, 0xfb, 0xb1, 0x94, 0xda, 0xb9, 0xeb, 0xd1, 0x47, 0xe1, 0xae, 0xef, 0xbc, 0x76,
	0x93, 0x7d, 0x14, 0x8e, 0x06, 0x7b, 0xda, 0xfd, 0x9b, 0xe8, 0x22, 0x59, 0x57, 0x89, 0xac, 0xab,
	0x74, 0x4a, 0x57, 0x52, 0x15, 0xd0, 0xad, 0x13, 0xb4, 0xd4, 0x2f, 0x28, 0xe2, 0xb1, 0x91, 0x10,
	0x33, 0xab, 0x17, 0xa3, 0x79, 0xf3, 0xc2, 0xf1, 0x1a, 0xea, 0x36, 0x04, 0xfb, 0x0c, 0x87, 0xc1,
	0x42, 0x1c, 0xde, 0x86, 0x6a, 0xfa, 0x3a, 0xb6, 0x2a, 0x14, 0xb1, 0x65, 0x6f, 0x6a, 0x77, 0x59,
	0xf6, 0xd3, 0x64, 0xca, 0x21, 0x32, 0x65, 0xdf, 0x69, 0xa3, 0x43, 0x27, 0x9d, 0x4b, 0x4f, 0x1a,
	0x32, 0x50, 0xfd, 0x1c, 0x4c, 0xc9, 0x2e, 0x84, 0xaa, 0x75, 0xc9, 0xda, 0x12, 0x37, 0x92, 0x6b,
	0x17, 0xbb, 0x8c, 0xc0, 0x3e, 0xaa, 0x13, 0x4c, 0x86, 0x85, 0xc5, 0x8f, 0xa7, 0xf0, 0x50, 0x4f,
	0xc4, 0x94, 0x69, 0x78, 0x11, 0x4e, 0x3d, 0x2f, 0x01, 0x2d, 0x5c, 0xba, 0xab, 0x5d, 0x28, 0xec,
	0x0f, 0x27, 0x1e, 0x2d, 0x9a, 0xf8, 0x57, 0x14, 0xd1, 0x1e, 0x89, 0x37, 0xa2, 0x54, 0x19, 0x5b,
	0x53, 0xb7, 0xb7, 0x6a, 0x4f, 0x76, 0x1d, 0x83, 0x7d, 0xf4, 0x51, 0x82, 0xc5, 0x18, 0xc1, 0x62,
	0xc0, 0x6d, 0x9c, 0x36, 0x02, 0x8a, 0xc9, 0x35, 0xb2, 0x9b, 0x84, 0x2b, 0x5e, 0x6c, 0x0f, 0x45,
	0x5b, 0x26, 0x60, 0xbf, 0xe9, 0x66, 0x22, 0xd2, 0xa9, 0x66, 0xaf, 0x2a, 0x49, 0xc9, 0x23, 0xdc,
	0x8a, 0x92, 0x92, 0x47, 0xbc, 0xe7, 0x84, 0xae, 0x11, 0xc4, 0xc6, 0xa9, 0x84, 0x90, 0xed, 0x4e,
	0xd0, 0x9a, 0x95, 0x6d, 0xe9, 0xcd, 0xf5, 0x15, 0xf5, 0xd7, 0x15, 0xa8, 0x24, 0xbe, 0x07, 0x23,
	0xa8, 0x99, 0xf4, 0x17, 0x87, 0x04, 0x35, 0x93, 0xfd, 0xf0, 0xcd, 0x67, 0xc8, 0xa4, 0x55, 0x32,
	0x29, 0x9c, 0x36, 0x5c, 0xaa, 0x5f, 0x5a, 0x74, 0xea, 0x8d, 0xe4, 0x8c, 0x75, 0x89, 0xba, 0x61,
	0xda, 0xe3, 0x7e, 0x46, 0x7b, 0xb4, 0xea, 0xcb, 0x76, 0xeb, 0xb0, 0xde, 0x32, 0x5f, 0x5c, 0x51,
	0x7f, 0x5b, 0xe1, 0xd5, 0xcf, 0xf9, 0x9f, 0xd5, 0x50, 0x9f, 0x16, 0x12, 0xe5, 0xdd, 0x3e, 0xff,
	0x51, 0x7b, 0xa6, 0xf7, 0xc1, 0xd8, 0x47, 0xcf, 0x92, 0xe5, 0x4d, 0x26, 0x68, 0xba, 0x48, 0x68,
	0xca, 0xca, 0x9f, 0xeb, 0x11, 0x69, 0xd9, 0x07, 0x6f, 0x88, 0x08, 0x3e, 0x24, 0x56, 0x20, 0x71,
	0x07, 0x4b, 0x50, 0x76, 0xd9, 0x4b, 0x5e, 0xb5, 0x73, 0xf9, 0x9d, 0xd8, 0x67, 0xbb, 0x7e, 0x3a,
	0x31, 0xff, 0x9c, 0x8c, 0xa7, 0x54, 0xfa, 0xbf, 0xad, 0xc0, 0x7c, 0xee, 0x6d, 0x26, 0xf5, 0x72,
	0x34, 0x51, 0xd1, 0xcd, 0xab, 0xda, 0x95, 0x5e, 0x86, 0x85, 0x94, 0x99, 0xe9, 0x91, 0x32, 0x14,
	0xbd, 0xaf, 0x65, 0xef, 0x70, 0xd0, 0x93, 0x52, 0x41, 0x2b, 0xe5, 0x5c, 0x21, 0x10, 0xb4, 0x52,
	0x5e, 0x05, 0x3f, 0x7a, 0x9e, 0xe0, 0x53, 0x4b, 0xe8, 0xc7, 0xba, 0x4c, 0x3f, 0x8a, 0xf8, 0x11,
	0x8a, 0xcd, 0xe5, 0xd5, 0xd5, 0xa8, 0x97, 0x92, 0x9c, 0x91, 0x57, 0xfb, 0xd4, 0x2e, 0xf7, 0x30,
	0x2a, 0x44, 0x6f, 0x21, 0x41, 0xae, 0x7a, 0x3e, 0xb9, 0xda, 0x9e, 0x63, 0xad, 0xd4, 0xd5, 0x6f,
	0x29, 0x30, 0x97, 0xf7, 0x15, 0x14, 0x01, 0xbd, 0x82, 0xcf, 0xb4, 0x08, 0xe8, 0x15, 0x7d, 0x4e,
	0x05, 0x3d, 0x47, 0xd0, 0x3b, 0x97, 0xa0, 0xde, 0x85, 0x2e, 0xd4, 0x53, 0x3f, 0x9f, 0xb8, 0xbf,
	0x19, 0x17, 0xc7, 0x4b, 0x8d, 0x6c, 0xb2, 0x1c, 0x5f, 0x6a, 0x64, 0x53, 0xf5, 0xf5, 0xe8, 0x02,
	0xc1, 0xe9, 0xbc, 0xa0, 0xee, 0xc7, 0x08, 0x46, 0x61, 0x0d, 0xdf, 0xfa, 0x8a, 0xfa, 0x2e, 0xd3,
	0xf6, 0x92, 0x2a, 0xf2, 0xa4, 0xb6, 0x97, 0xd7, 0xb9, 0x27, 0xb5, 0x7d, 0x4e, 0x29, 0x3a, 0xba,
	0x4c, 0x90, 0xb8, 0xc0, 0xf9, 0xd6, 0xa6, 0x68, 0xa8, 0xf1, 0x06, 0x6c, 0xd7, 0x97, 0x29, 0xab,
	0xd4, 0x1f, 0x10, 0x41, 0xca, 0x29, 0xf4, 0x14, 0x05, 0x29, 0xbf, 0x54, 0x56, 0x14, 0xa4, 0x82,
	0x8a, 0x51, 0x64, 0x10, 0x84, 0x2e, 0x12, 0x84, 0x46, 0x3b, 0x0d, 0xb3, 0xb1, 0xdf, 0x08, 0x1a,
	0x7e, 0x43, 0xa7, 0x88, 0xdd, 0x89, 0x11, 0x33, 0x63, 0x42, 0x99, 0xb1, 0xfb, 0xb6, 0xcb, 0x9c,
	0x39, 0x6e, 0x8d, 0xa8, 0x56, 0x16, 0xf5, 0x6e, 0xe4, 0xd8, 0xa9, 0x6f, 0x8b, 0x67, 0x0e, 0xa9,
	0x0f, 0x4a, 0x08, 0x8a, 0xa4, 0xe8, 0x8b, 0x18, 0x82, 0x22, 0x29, 0xfc, 0x36, 0x05, 0x1a, 0x27,
	0x0b, 0x42, 0x64, 0x41, 0x4f, 0x90, 0x45, 0x3c, 0xa1, 0x06, 0x50, 0x4d, 0x57, 0x24, 0x0a, 0x9e,
	0x93, 0xa4, 0x84, 0xb2, 0x56, 0x5c, 0xca, 0x88, 0x9e, 0x24, 0x33, 0x5c, 0xa2, 0x82, 0x14, 0x70,
	0x0e, 0x06, 0xb1, 0x68, 0x33, 0x0f, 0x57, 0xfd, 0x3f, 0xa2, 0xcb, 0x14, 0x1f, 0x76, 0x48, 0x5d,
	0xa6, 0xc4, 0x29, 0x8a, 0xd4, 0x65, 0x4a, 0x9e, 0x96, 0x30, 0x51, 0xbe, 0x9c, 0x16, 0x65, 0xc1,
	0x1e, 0xff, 0xa2, 0x02, 0x63, 0xc9, 0xaa, 0x2e, 0x35, 0xb6, 0xba, 0x99, 0x02, 0xb2, 0xda, 0x42,
	0x6e, 0x1f, 0xf6, 0xd1, 0x3d, 0x32, 0xd9, 0x53, 0xd4, 0x41, 0x69, 0x35, 0x4e, 0x1b, 0x26, 0x9d,
	0xb0, 0x41, 0xac, 0x2a, 0xad, 0xee, 0x0a, 0xad, 0xef, 0x69, 0xf8, 0x4c, 0x6c, 0x6d, 0xa7, 0xed,
	0x30, 0xa9, 0x61, 0x4d, 0x61, 0x2d, 0x1d, 0x6d, 0x57, 0x3f, 0x0b, 0x63, 0xc9, 0x42, 0x30, 0x01,
	0xb7, 0x4c, 0x65, 0x99, 0x80, 0x9b, 0xa4, 0x7a, 0x8c, 0x12, 0xe2, 0x2a, 0x25, 0x44, 0x8b, 0x13,
	0x22, 0xc2, 0x8b, 0x6a, 0xe4, 0x83, 0xb0, 0xe4, 0x46, 0xac, 0x22, 0x4b, 0xf9, 0x46, 0x99, 0xda,
	0xb3, 0x94, 0x6f, 0x94, 0x2d, 0x41, 0x63, 0x42, 0xf6, 0x21, 0x41, 0xc8, 0x8e, 0x45, 0x1f, 0x2c,
	0x2c, 0x10, 0x93, 0xfa, 0x60, 0x42, 0xd5, 0x99, 0xd4, 0x07, 0x13, 0xab, 0xcb, 0xd8, 0xfa, 0xae,
	0x15, 0x30, 0xfa, 0x7b, 0x0a, 0x4c, 0x4a, 0x4a, 0xa6, 0xd4, 0x18, 0xb2, 0xbc, 0xfe, 0xac, 0x56,
	0x2f, 0x1e, 0x80, 0x7d, 0xf4, 0xbf, 0xc9, 0xdc, 0x4f, 0x53, 0x57, 0x8c, 0xa8, 0xaa, 0xd3, 0xc6,
	0x61, 0xc3, 0xa7, 0x38, 0xdc, 0x26, 0x7a, 0x01, 0x5b, 0xae, 0x59, 0x8f, 0xb5, 0x96, 0x65, 0x1c,
	0xd7, 0x25, 0x8e, 0x61, 0x7d, 0xe9, 0xb0, 0xbe, 0x7c, 0x68, 0x1f, 0x04, 0x4c, 0xe3, 0xfb, 0xfc,
	0xe1, 0x58, 0x77, 0x3a, 0xd6, 0x8a, 0xfa, 0x25, 0x85, 0xa6, 0x61, 0x85, 0x82, 0x28, 0x41, 0x12,
	0x32, 0x05, 0x59, 0x82, 0x24, 0x64, 0xab, 0xa8, 0x50, 0x83, 0x60, 0xfb, 0x0c, 0x95, 0x52, 0x12,
	0x9a, 0x3a, 0x14, 0xd3, 0xcb, 0x69, 0x6c, 0x88, 0xe2, 0xb2, 0x0e, 0x6d, 0x77, 0xa5, 0xbe, 0xe4,
	0xd4, 0x97, 0x69, 0xce, 0xb9, 0xbe, 0xa2, 0x7e, 0x43, 0x61, 0xa1, 0xb6, 0x50, 0xb8, 0x93, 0x70,
	0xb2, 0xd2, 0x85, 0x44, 0x09, 0x27, 0x2b, 0x5b, 0xef, 0xb3, 0x41, 0x50, 0x59, 0xe2, 0xa8, 0x98,
	0xdc, 0x7f, 0x7d, 0x36, 0x8d, 0x8a, 0x59, 0x5f, 0x76, 0xad, 0x93, 0x7a, 0x6c, 0x7f, 0xa8, 0xa3,
	0x1a, 0xa6, 0x51, 0x56, 0xea, 0x49, 0x7b, 0x28, 0x14, 0xe0, 0x48, 0xed, 0x61, 0xb2, 0x1c, 0x48,
	0x6a, 0x0f, 0x53, 0x35, 0x3c, 0x4c, 0xb6, 0xae, 0x17, 0xc8, 0xd6, 0x67, 0x19, 0x77, 0xe2, 0x72,
	0x9a, 0x14, 0x77, 0x12, 0xb5, 0x39, 0x29, 0xee, 0x24, 0x6b, 0x70, 0xd8, 0x5c, 0x37, 0x0a, 0xe6,
	0x72, 0x69, 0x7a, 0x3d, 0x51, 0x59, 0x23, 0x03, 0x18, 0x96, 0x80, 0x08, 0x1c, 0x90, 0x94, 0xbf,
	0xb0, 0xc8, 0xee, 0xd9, 0x9c, 0xc8, 0x8e, 0x90, 0x5a, 0xfd, 0x05, 0x9e, 0xff, 0x11, 0x0b, 0x09,
	0xd4, 0x34, 0x5b, 0x13, 0x95, 0x0e, 0x82, 0x55, 0x90, 0x55, 0x20, 0xa0, 0x3b, 0x64, 0xce, 0xe7,
	0x39, 0xd7, 0x3b, 0x3c, 0x8e, 0x7b, 0x3e, 0xcd, 0xf5, 0xb4, 0x8b, 0x16, 0xd4, 0x97, 0xeb, 0xbc,
	0xa1, 0x65, 0x1b, 0xf5, 0xcd, 0xf5, 0xfa, 0x8a, 0xfa, 0x73, 0xe1, 0x97, 0x81, 0x72, 0x8a, 0xa2,
	0xd5, 0xab, 0x11, 0x26, 0x5d, 0xaa, 0xb0, 0x6b, 0x1f, 0xea, 0x71, 0x64, 0x48, 0xb3, 0x17, 0x8a,
	0x68, 0xf6, 0x4d, 0x45, 0xb4, 0x69, 0xc2, 0x97, 0xd4, 0x65, 0x36, 0x2d, 0xf1, 0x2d, 0x23, 0xa9,
	0x4d, 0x4b, 0x7e, 0x35, 0x08, 0xbd, 0x42, 0xe6, 0x7f, 0x91, 0xbb, 0x8c, 0xcc, 0xc8, 0x5c, 0x91,
	0xec, 0x19, 0xcf, 0x0f, 0x6c, 0xcf, 0xd5, 0x1d, 0x61, 0xe3, 0xa8, 0x3f, 0xab, 0xf0, 0xfb, 0x0a,
	0x52, 0xec, 0x2e, 0x27, 0x69, 0x90, 0x87, 0xe2, 0x95, 0x5e, 0x86, 0x85, 0x74, 0x7a, 0xa9, 0x88,
	0x4e, 0xff, 0x4f, 0x81, 0x49, 0xc9, 0xf9, 0x95, 0xa0, 0x93, 0xe5, 0xc7, 0xa9, 0x82, 0x4e, 0xce,
	0x39, 0x07, 0x65, 0x31, 0xf9, 0x87, 0x13, 0x7e, 0xf5, 0xac, 0x44, 0xc4, 0xa8, 0x3f, 0xfd, 0xf3,
	0x0a, 0x4c, 0xc9, 0x0e, 0x1e, 0xd5, 0xbc, 0x69, 0xa2, 0xa3, 0x52, 0x81, 0x5d, 0x79, 0x27, 0x97,
	0x4c, 0xdf, 0xbe, 0x9c, 0x12, 0xf7, 0xcb, 0x39, 0xb8, 0x50, 0x39, 0xb7, 0x71, 0x9d, 0x1e, 0x90,
	0xae, 0x90, 0x38, 0xe4, 0x7c, 0xf1, 0x19, 0x9f, 0x7a, 0x4d, 0x14, 0x98, 0xe2, 0xd3, 0xc6, 0xda,
	0xd3, 0x3d, 0x8f, 0x0d, 0xd9, 0xf7, 0x4a, 0x11, 0xfb, 0x6c, 0x80, 0xb8, 0x84, 0x41, 0x8d, 0xcf,
	0x4e, 0x13, 0xf5, 0x11, 0xb5, 0x59, 0x69, 0x7b, 0xc8, 0xa2, 0x46, 0x6f, 0x2c, 0x7a, 0x8b, 0x7f,
	0x60, 0x25, 0x59, 0x41, 0x20, 0x08, 0x8a, 0xbc, 0x30, 0x41, 0x10, 0x94, 0x9c, 0x02, 0x04, 0xb6,
	0xcc, 0x57, 0x8b, 0x96, 0xf9, 0x69, 0x18, 0x8e, 0x4a, 0x40, 0xd4, 0xb8, 0x36, 0x50, 0xac, 0x22,
	0xa9, 0xcd, 0xc8, 0x9a, 0x43, 0x75, 0xbe, 0x5c, 0xa0, 0xce, 0x7f, 0x4d, 0xe1, 0x77, 0x5a, 0xe4,
	0x97, 0xcc, 0xd5, 0xa7, 0xb2, 0x59, 0x0a, 0xe9, 0xed, 0xf6, 0xda, 0xd5, 0xde, 0x06, 0x86, 0x11,
	0xf1, 0x47, 0x1e, 0x31, 0x60, 0xff, 0x86, 0x42, 0x6b, 0x92, 0xa4, 0x05, 0xcd, 0x42, 0x9c, 0x55,
	0x50, 0x43, 0x5d, 0xbb, 0xdc, 0xc3, 0xa8, 0x50, 0x2c, 0x56, 0x7a, 0x13, 0x8b, 0xb7, 0xa1, 0x9a,
	0x2e, 0xf6, 0x10, 0x6c, 0x93, 0xa4, 0x7a, 0x44, 0xb0, 0x4d, 0xd2, 0x2a, 0x11, 0x3a, 0xf9, 0x47,
	0x7b, 0x9b, 0xfc, 0x14, 0xc6, 0x53, 0x45, 0x20, 0x82, 0x25, 0xce, 0xd6, 0x9a, 0x08, 0x96, 0x58,
	0x56, 0x3b, 0x42, 0x67, 0xfe, 0x58, 0xaf, 0x33, 0xab, 0xd9, 0x3b, 0x5b, 0x82, 0x0f, 0x2d, 0xbd,
	0x2c, 0x56, 0xbb, 0x50, 0xd8, 0x1f, 0x26, 0xd7, 0x57, 0x73, 0x92, 0xeb, 0xcc, 0xd5, 0xfa, 0xb2,
	0x02, 0xe3, 0xa9, 0x0a, 0x54, 0x35, 0x19, 0x13, 0xe5, 0xba, 0x1f, 0xb2, 0xc2, 0xd5, 0x97, 0xc9,
	0x8c, 0x37, 0x23, 0xdd, 0xc8, 0xc2, 0xfc, 0x27, 0xd3, 0xb3, 0x66, 0xc3, 0xfe, 0xba, 0xfa, 0x45,
	0x85, 0xd0, 0x20, 0x5d, 0xa9, 0x9a, 0xa0, 0x81, 0xa4, 0x20, 0x36, 0x41, 0x03, 0x69, 0x99, 0xeb,
	0x33, 0x04, 0xa3, 0xb5, 0x04, 0x1b, 0xe6, 0x73, 0xf1, 0x51, 0x3f, 0x9f, 0xfa, 0x38, 0xfe, 0x5d,
	0x3b, 0xb0, 0xd4, 0x45, 0x79, 0x3e, 0x81, 0x57, 0x16, 0xd4, 0xce, 0x17, 0x75, 0x87, 0x99, 0x47,
	0x33, 0xf7, 0xbc, 0x21, 0x81, 0x81, 0xda, 0x81, 0xd9, 0x9c, 0x03, 0x7d, 0xf5, 0xc9, 0x3c, 0x33,
	0x2d, 0x14, 0x0e, 0xd4, 0x2e, 0x75, 0x1f, 0x14, 0x06, 0x71, 0x07, 0x42, 0x10, 0x67, 0xc0, 0x44,
	0xe6, 0x2b, 0xb1, 0xc2, 0xba, 0x65, 0x5f, 0xa6, 0x15, 0xd6, 0x2d, 0xfd, 0xc0, 0x2c, 0x9b, 0xe4,
	0x50, 0x98, 0xc4, 0x86, 0x49, 0xc9, 0x97, 0x5d, 0x05, 0x9d, 0x2f, 0xff, 0xa2, 0xac, 0xa0, 0xf3,
	0xf3, 0x3e, 0x0c, 0x4b, 0xa7, 0x3a, 0x12, 0xa6, 0x3a, 0x95, 0x7f, 0xa1, 0x84, 0xb2, 0xf3, 0x52,
	0x61, 0x0a, 0x34, 0xe4, 0xea, 0xe5, 0x1e, 0x46, 0x85, 0x33, 0xdb, 0xc2, 0xcc, 0x5f, 0x55, 0x60,
	0x3e, 0xb7, 0x3a, 0x41, 0x15, 0x73, 0x9c, 0xf9, 0x05, 0x10, 0x82, 0x47, 0x56, 0x5c, 0xe8, 0x40,
	0x37, 0xf8, 0x9f, 0x28, 0x42, 0x42, 0x66, 0x9c, 0x26, 0x64, 0x58, 0xa9, 0x04, 0xcd, 0x15, 0xbf,
	0xa3, 0xc0, 0x44, 0x16, 0x8f, 0x45, 0x61, 0xdb, 0x48, 0xe6, 0x3f, 0x5f, 0xd4, 0x1d, 0x6e, 0xaa,
	0x3f, 0x55, 0x7a, 0xcc, 0xa6, 0x7f, 0x55, 0x81, 0x69, 0x69, 0x41, 0x85, 0x10, 0xcf, 0xe5, 0xd5,
	0x6b, 0x08, 0xf1, 0x5c, 0x7e, 0x4d, 0xc6, 0x12, 0x41, 0xe7, 0xcf, 0x42, 0x74, 0x1e, 0xf0, 0x4d,
	0x1e, 0xa1, 0xf3, 0x40, 0x44, 0x07, 0xaf, 0xa8, 0x5f, 0xa1, 0x99, 0x83, 0x4c, 0x19, 0x86, 0x9a,
	0xd6, 0x25, 0xe9, 0x0a, 0x8f, 0x44, 0xe6, 0x40, 0x5e, 0xc5, 0x41, 0x31, 0xf9, 0x7e, 0xcf, 0x98,
	0x74, 0xc4, 0x3b, 0x0c, 0x32, 0x44, 0xe4, 0x25, 0x21, 0xb5, 0x7a, 0xf1, 0x00, 0xec, 0xa3, 0x05,
	0x82, 0xc8, 0x0f, 0x98, 0x64, 0x30, 0x9d, 0x33, 0x1c, 0x6b, 0xb9, 0xef, 0x2b, 0xec, 0xff, 0x6d,
	0x12, 0x75, 0x13, 0x6a, 0x3a, 0xca, 0x4b, 0xd6, 0x8b, 0xd4, 0xce, 0x17, 0x75, 0x63, 0x1f, 0x9d,
	0x90, 0x19, 0xff, 0x92, 0xce, 0x38, 0x7a, 0xda, 0x08, 0x1a, 0x76, 0xa3, 0xdd, 0xd8, 0xe7, 0x29,
	0x80, 0x4f, 0x53, 0x91, 0x08, 0xdd, 0x61, 0x12, 0xfc, 0xf3, 0xd3, 0x70, 0x9b, 0x3d, 0x45, 0x49,
	0xd4, 0x36, 0x7b, 0xe6, 0xdf, 0x47, 0xe6, 0x91, 0xe2, 0x3e, 0xf5, 0xa0, 0x69, 0xa9, 0x48, 0x9d,
	0x96, 0x8a, 0xac, 0xd4, 0x97, 0x5a, 0x2c, 0x5f, 0x40, 0xc4, 0xeb, 0x9b, 0xd1, 0x6a, 0xa2, 0x64,
	0xc4, 0xeb, 0xcf, 0x3d, 0x4e, 0x16, 0xe3, 0x16, 0x59, 0xc9, 0x5f, 0x29, 0x8f, 0x9d, 0xc6, 0xf8,
	0x03, 0x05, 0x16, 0x0b, 0xff, 0x2a, 0x42, 0xfd, 0x90, 0xfc, 0x3c, 0x50, 0xf2, 0x97, 0x17, 0xb5,
	0x6b, 0xbd, 0x0e, 0xc5, 0x3e, 0x5a, 0x23, 0x0b, 0xf8, 0x6b, 0xb6, 0x00, 0xa3, 0x61, 0xf1, 0xe4,
	0xd5, 0xd2, 0x92, 0x91, 0x5c, 0x80, 0xc1, 0x34, 0x45, 0xf8, 0x4f, 0x18, 0xc9, 0x1c, 0x36, 0x09,
	0xa2, 0x66, 0xa4, 0x57, 0xe1, 0xb1, 0xb0, 0x6d, 0xf3, 0xee, 0xe2, 0x0b, 0xdb, 0x36, 0xf7, 0x3a,
	0x3d, 0x7a, 0x89, 0xa0, 0xf9, 0x77, 0x6c, 0xb3, 0x04, 0x0d, 0x83, 0x22, 0x89, 0xb2, 0x09, 0x65,
	0x82, 0xa7, 0x70, 0x69, 0x7f, 0x45, 0xfd, 0x8e, 0xc2, 0x3f, 0x02, 0x90, 0x75, 0x7b, 0x70, 0xda,
	0x4a, 0xca, 0x3d, 0xa7, 0x4b, 0xdd, 0x07, 0x85, 0xf8, 0xfd, 0x28, 0xc4, 0xef, 0x41, 0x2e, 0x7e,
	0xe9, 0x5d, 0xfd, 0x8e, 0x02, 0x53, 0xb2, 0xab, 0xf2, 0x42, 0xf8, 0x99, 0x73, 0x0f, 0x5f, 0x08,
	0x3f, 0xf3, 0xee, 0xda, 0xb3, 0x1c, 0xfc, 0x8f, 0x45, 0x9d, 0x3f, 0x91, 0xc1, 0x49, 0xfd, 0x7a,
	0x74, 0x67, 0x25, 0x59, 0xae, 0x74, 0x41, 0xbe, 0x89, 0xa3, 0xea, 0xa8, 0x44, 0x7c, 0x25, 0xad,
	0x5d, 0x62, 0x2e, 0xde, 0x4f, 0x18, 0x55, 0x8c, 0xc6, 0x31, 0xc5, 0xe0, 0x92, 0x20, 0x5a, 0x2c,
	0xdc, 0x38, 0x8e, 0x9f, 0x8f, 0x6d, 0xeb, 0x84, 0x34, 0xd6, 0x06, 0xde, 0x7d, 0xef, 0xfd, 0xf2,
	0xdf, 0x1f, 0xff, 0x47, 0x00, 0x00, 0x00, 0xff, 0xff, 0x1e, 0xc0, 0x50, 0x63, 0xd8, 0x70, 0x00,
	0x00,
}
