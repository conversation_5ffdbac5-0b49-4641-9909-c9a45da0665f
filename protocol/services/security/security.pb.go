// Code generated by protoc-gen-gogo.
// source: src/security/security.proto
// DO NOT EDIT!

/*
	Package security is a generated protocol buffer package.

	It is generated from these files:
		src/security/security.proto

	It has these top-level messages:
		SecurityQuestion
		SecurityQuestionAnswer
		GetSummaryReq
		SecuritySummary
		GetSummaryRsp
		TokenModel
		AccessToken
		GetAccessTokenReq
		GetAccessTokenRsp
		ValidateAccessTokenReq
		ValidateAccessTokenRsp
		SessionModel
		SessionInfo
		GetSessionReq
		GetSessionRsp
		ValidateSessionReq
		ValidateSessionRsp
		GetPresetQuestionReq
		GetPresetQuestionRsp
		SetQuestionReq
		SetQuestionRsp
		VerifyQuestionReq
		VerifyQuestionRsp
		BindPhoneReq
		BindPhoneRsp
		UnbindPhoneReq
		RebindPhoneReq
		RebindPhoneResp
		UnbindPhoneRsp
		UpdatePasswordReq
		UpdatePasswordRsp
		DetachThirdPartyReq
		DetachThirdPartyRsp
		GetOperationLogReq
		LogInfo
		GetOperationLogRsp
		BatchGetLatestOperationReq
		UserLatestOperation
		BatchGetLatestOperationRsp
		GetUnregApplyAuditStatusReq
		GetUnregApplyAuditStatusRsp
		UpdateUnregApplyAuditStatusReq
		UpdateUnregApplyAuditStatusRsp
		ClearUnregApplyAuditStatusReq
		ClearUnregApplyAuditStatusRsp
		GetUnregApplyAuditRecordReq
		UnregApplyAuditInfo
		GetUnregApplyAuditRecordRsp
		GetUserLastOperationReq
		GetUserLastOperationRsp
		UpdateAutoProcUnregApplyStatusReq
		UpdateAutoProcUnregApplyStatusRsp
		GetAutoProcUnregRecordReq
		AutoProcUnregInfo
		GetAutoProcUnregRecordRsp
		UpdateUnregVisitReq
		UpdateUnregVisitRsp
*/
package security

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 安全级别
type Security_Level int32

const (
	Security_Level_SECURITY_LEVEL_LOW  Security_Level = 1
	Security_Level_SECURITY_LEVEL_MID  Security_Level = 2
	Security_Level_SECURITY_LEVEL_HIGH Security_Level = 3
)

var Security_Level_name = map[int32]string{
	1: "SECURITY_LEVEL_LOW",
	2: "SECURITY_LEVEL_MID",
	3: "SECURITY_LEVEL_HIGH",
}
var Security_Level_value = map[string]int32{
	"SECURITY_LEVEL_LOW":  1,
	"SECURITY_LEVEL_MID":  2,
	"SECURITY_LEVEL_HIGH": 3,
}

func (x Security_Level) Enum() *Security_Level {
	p := new(Security_Level)
	*p = x
	return p
}
func (x Security_Level) String() string {
	return proto.EnumName(Security_Level_name, int32(x))
}
func (x *Security_Level) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Security_Level_value, data, "Security_Level")
	if err != nil {
		return err
	}
	*x = Security_Level(value)
	return nil
}
func (Security_Level) EnumDescriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{0} }

// 第三方注册类型
type ThirdParty_Attached int32

const (
	ThirdParty_Attached_THIRD_PARTY_NIL    ThirdParty_Attached = 0
	ThirdParty_Attached_THIRD_PARTY_QQ     ThirdParty_Attached = 1
	ThirdParty_Attached_THIRD_PARTY_WECHAT ThirdParty_Attached = 2
)

var ThirdParty_Attached_name = map[int32]string{
	0: "THIRD_PARTY_NIL",
	1: "THIRD_PARTY_QQ",
	2: "THIRD_PARTY_WECHAT",
}
var ThirdParty_Attached_value = map[string]int32{
	"THIRD_PARTY_NIL":    0,
	"THIRD_PARTY_QQ":     1,
	"THIRD_PARTY_WECHAT": 2,
}

func (x ThirdParty_Attached) Enum() *ThirdParty_Attached {
	p := new(ThirdParty_Attached)
	*p = x
	return p
}
func (x ThirdParty_Attached) String() string {
	return proto.EnumName(ThirdParty_Attached_name, int32(x))
}
func (x *ThirdParty_Attached) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ThirdParty_Attached_value, data, "ThirdParty_Attached")
	if err != nil {
		return err
	}
	*x = ThirdParty_Attached(value)
	return nil
}
func (ThirdParty_Attached) EnumDescriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{1} }

type PhoneType int32

const (
	PhoneType_UNKNOWN_PHONE PhoneType = 0
	PhoneType_LOGIN_PHONE   PhoneType = 1
	PhoneType_SECURE_PHONE  PhoneType = 2
)

var PhoneType_name = map[int32]string{
	0: "UNKNOWN_PHONE",
	1: "LOGIN_PHONE",
	2: "SECURE_PHONE",
}
var PhoneType_value = map[string]int32{
	"UNKNOWN_PHONE": 0,
	"LOGIN_PHONE":   1,
	"SECURE_PHONE":  2,
}

func (x PhoneType) Enum() *PhoneType {
	p := new(PhoneType)
	*p = x
	return p
}
func (x PhoneType) String() string {
	return proto.EnumName(PhoneType_name, int32(x))
}
func (x *PhoneType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PhoneType_value, data, "PhoneType")
	if err != nil {
		return err
	}
	*x = PhoneType(value)
	return nil
}
func (PhoneType) EnumDescriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{2} }

// session
type Session_Usage int32

const (
	Session_Usage_SESSION_USAGE_PHONE_BIND         Session_Usage = 1
	Session_Usage_SESSION_USAGE_PHONE_UNBIND       Session_Usage = 2
	Session_Usage_SESSION_USAGE_QUESTION_SET       Session_Usage = 3
	Session_Usage_SESSION_USAGE_PASSWORD_UPDATE    Session_Usage = 4
	Session_Usage_SESSION_USAGE_PHONE_REBIND       Session_Usage = 5
	Session_Usage_SESSION_USAGE_DETACH_THIRD_PARTY Session_Usage = 6
)

var Session_Usage_name = map[int32]string{
	1: "SESSION_USAGE_PHONE_BIND",
	2: "SESSION_USAGE_PHONE_UNBIND",
	3: "SESSION_USAGE_QUESTION_SET",
	4: "SESSION_USAGE_PASSWORD_UPDATE",
	5: "SESSION_USAGE_PHONE_REBIND",
	6: "SESSION_USAGE_DETACH_THIRD_PARTY",
}
var Session_Usage_value = map[string]int32{
	"SESSION_USAGE_PHONE_BIND":         1,
	"SESSION_USAGE_PHONE_UNBIND":       2,
	"SESSION_USAGE_QUESTION_SET":       3,
	"SESSION_USAGE_PASSWORD_UPDATE":    4,
	"SESSION_USAGE_PHONE_REBIND":       5,
	"SESSION_USAGE_DETACH_THIRD_PARTY": 6,
}

func (x Session_Usage) Enum() *Session_Usage {
	p := new(Session_Usage)
	*p = x
	return p
}
func (x Session_Usage) String() string {
	return proto.EnumName(Session_Usage_name, int32(x))
}
func (x *Session_Usage) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Session_Usage_value, data, "Session_Usage")
	if err != nil {
		return err
	}
	*x = Session_Usage(value)
	return nil
}
func (Session_Usage) EnumDescriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{3} }

// 获取验证方式：手机验证码，密保问题
type Verification_Scheme int32

const (
	Verification_Scheme_VERIFY_SCHEME_SMS      Verification_Scheme = 1
	Verification_Scheme_VERIFY_SCHEME_QUESTION Verification_Scheme = 2
	Verification_Scheme_VERIFY_SCHEME_CODE     Verification_Scheme = 3
)

var Verification_Scheme_name = map[int32]string{
	1: "VERIFY_SCHEME_SMS",
	2: "VERIFY_SCHEME_QUESTION",
	3: "VERIFY_SCHEME_CODE",
}
var Verification_Scheme_value = map[string]int32{
	"VERIFY_SCHEME_SMS":      1,
	"VERIFY_SCHEME_QUESTION": 2,
	"VERIFY_SCHEME_CODE":     3,
}

func (x Verification_Scheme) Enum() *Verification_Scheme {
	p := new(Verification_Scheme)
	*p = x
	return p
}
func (x Verification_Scheme) String() string {
	return proto.EnumName(Verification_Scheme_name, int32(x))
}
func (x *Verification_Scheme) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Verification_Scheme_value, data, "Verification_Scheme")
	if err != nil {
		return err
	}
	*x = Verification_Scheme(value)
	return nil
}
func (Verification_Scheme) EnumDescriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{4} }

type OP_TYPE int32

const (
	OP_TYPE_OP_NIL               OP_TYPE = 0
	OP_TYPE_OP_BIND_PHONE        OP_TYPE = 1
	OP_TYPE_OP_UNBIND_PHONE      OP_TYPE = 2
	OP_TYPE_OP_REBIND_PHONE      OP_TYPE = 4
	OP_TYPE_OP_UPDATE_PASSWD     OP_TYPE = 8
	OP_TYPE_OP_SET_QUESTION      OP_TYPE = 16
	OP_TYPE_OP_DETACH_THIRDPARTY OP_TYPE = 32
)

var OP_TYPE_name = map[int32]string{
	0:  "OP_NIL",
	1:  "OP_BIND_PHONE",
	2:  "OP_UNBIND_PHONE",
	4:  "OP_REBIND_PHONE",
	8:  "OP_UPDATE_PASSWD",
	16: "OP_SET_QUESTION",
	32: "OP_DETACH_THIRDPARTY",
}
var OP_TYPE_value = map[string]int32{
	"OP_NIL":               0,
	"OP_BIND_PHONE":        1,
	"OP_UNBIND_PHONE":      2,
	"OP_REBIND_PHONE":      4,
	"OP_UPDATE_PASSWD":     8,
	"OP_SET_QUESTION":      16,
	"OP_DETACH_THIRDPARTY": 32,
}

func (x OP_TYPE) Enum() *OP_TYPE {
	p := new(OP_TYPE)
	*p = x
	return p
}
func (x OP_TYPE) String() string {
	return proto.EnumName(OP_TYPE_name, int32(x))
}
func (x *OP_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(OP_TYPE_value, data, "OP_TYPE")
	if err != nil {
		return err
	}
	*x = OP_TYPE(value)
	return nil
}
func (OP_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{5} }

type SortType int32

const (
	SortType_SORT_ASC  SortType = 1
	SortType_SORT_DESC SortType = 2
)

var SortType_name = map[int32]string{
	1: "SORT_ASC",
	2: "SORT_DESC",
}
var SortType_value = map[string]int32{
	"SORT_ASC":  1,
	"SORT_DESC": 2,
}

func (x SortType) Enum() *SortType {
	p := new(SortType)
	*p = x
	return p
}
func (x SortType) String() string {
	return proto.EnumName(SortType_name, int32(x))
}
func (x *SortType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(SortType_value, data, "SortType")
	if err != nil {
		return err
	}
	*x = SortType(value)
	return nil
}
func (SortType) EnumDescriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{6} }

// 安全问题: 预置问题、用户问题
type SecurityQuestion struct {
	Id       uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Question string `protobuf:"bytes,2,req,name=question" json:"question"`
}

func (m *SecurityQuestion) Reset()                    { *m = SecurityQuestion{} }
func (m *SecurityQuestion) String() string            { return proto.CompactTextString(m) }
func (*SecurityQuestion) ProtoMessage()               {}
func (*SecurityQuestion) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{0} }

func (m *SecurityQuestion) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SecurityQuestion) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

// 安全问题答案
type SecurityQuestionAnswer struct {
	Id     uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Answer string `protobuf:"bytes,2,opt,name=answer" json:"answer"`
}

func (m *SecurityQuestionAnswer) Reset()                    { *m = SecurityQuestionAnswer{} }
func (m *SecurityQuestionAnswer) String() string            { return proto.CompactTextString(m) }
func (*SecurityQuestionAnswer) ProtoMessage()               {}
func (*SecurityQuestionAnswer) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{1} }

func (m *SecurityQuestionAnswer) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SecurityQuestionAnswer) GetAnswer() string {
	if m != nil {
		return m.Answer
	}
	return ""
}

// 安全状态信息  /security/summary
type GetSummaryReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetSummaryReq) Reset()                    { *m = GetSummaryReq{} }
func (m *GetSummaryReq) String() string            { return proto.CompactTextString(m) }
func (*GetSummaryReq) ProtoMessage()               {}
func (*GetSummaryReq) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{2} }

func (m *GetSummaryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SecuritySummary struct {
	Uid                uint32              `protobuf:"varint,1,req,name=uid" json:"uid"`
	Level              uint32              `protobuf:"varint,2,req,name=level" json:"level"`
	Phone              string              `protobuf:"bytes,3,opt,name=phone" json:"phone"`
	Questions          []*SecurityQuestion `protobuf:"bytes,4,rep,name=questions" json:"questions,omitempty"`
	ThirdPartyAttached uint32              `protobuf:"varint,5,opt,name=thirdParty_attached,json=thirdPartyAttached" json:"thirdParty_attached"`
	PasswordSet        bool                `protobuf:"varint,6,opt,name=password_set,json=passwordSet" json:"password_set"`
	PhoneType          PhoneType           `protobuf:"varint,7,opt,name=phone_type,json=phoneType,enum=security.PhoneType" json:"phone_type"`
}

func (m *SecuritySummary) Reset()                    { *m = SecuritySummary{} }
func (m *SecuritySummary) String() string            { return proto.CompactTextString(m) }
func (*SecuritySummary) ProtoMessage()               {}
func (*SecuritySummary) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{3} }

func (m *SecuritySummary) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SecuritySummary) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *SecuritySummary) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *SecuritySummary) GetQuestions() []*SecurityQuestion {
	if m != nil {
		return m.Questions
	}
	return nil
}

func (m *SecuritySummary) GetThirdPartyAttached() uint32 {
	if m != nil {
		return m.ThirdPartyAttached
	}
	return 0
}

func (m *SecuritySummary) GetPasswordSet() bool {
	if m != nil {
		return m.PasswordSet
	}
	return false
}

func (m *SecuritySummary) GetPhoneType() PhoneType {
	if m != nil {
		return m.PhoneType
	}
	return PhoneType_UNKNOWN_PHONE
}

type GetSummaryRsp struct {
	Summary *SecuritySummary `protobuf:"bytes,1,req,name=summary" json:"summary,omitempty"`
}

func (m *GetSummaryRsp) Reset()                    { *m = GetSummaryRsp{} }
func (m *GetSummaryRsp) String() string            { return proto.CompactTextString(m) }
func (*GetSummaryRsp) ProtoMessage()               {}
func (*GetSummaryRsp) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{4} }

func (m *GetSummaryRsp) GetSummary() *SecuritySummary {
	if m != nil {
		return m.Summary
	}
	return nil
}

// 令牌
type TokenModel struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Expired uint32 `protobuf:"varint,2,req,name=expired" json:"expired"`
}

func (m *TokenModel) Reset()                    { *m = TokenModel{} }
func (m *TokenModel) String() string            { return proto.CompactTextString(m) }
func (*TokenModel) ProtoMessage()               {}
func (*TokenModel) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{5} }

func (m *TokenModel) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TokenModel) GetExpired() uint32 {
	if m != nil {
		return m.Expired
	}
	return 0
}

type AccessToken struct {
	Token string `protobuf:"bytes,1,req,name=token" json:"token"`
}

func (m *AccessToken) Reset()                    { *m = AccessToken{} }
func (m *AccessToken) String() string            { return proto.CompactTextString(m) }
func (*AccessToken) ProtoMessage()               {}
func (*AccessToken) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{6} }

func (m *AccessToken) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type GetAccessTokenReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetAccessTokenReq) Reset()                    { *m = GetAccessTokenReq{} }
func (m *GetAccessTokenReq) String() string            { return proto.CompactTextString(m) }
func (*GetAccessTokenReq) ProtoMessage()               {}
func (*GetAccessTokenReq) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{7} }

func (m *GetAccessTokenReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAccessTokenRsp struct {
	AccessToken *AccessToken `protobuf:"bytes,1,req,name=access_token,json=accessToken" json:"access_token,omitempty"`
}

func (m *GetAccessTokenRsp) Reset()                    { *m = GetAccessTokenRsp{} }
func (m *GetAccessTokenRsp) String() string            { return proto.CompactTextString(m) }
func (*GetAccessTokenRsp) ProtoMessage()               {}
func (*GetAccessTokenRsp) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{8} }

func (m *GetAccessTokenRsp) GetAccessToken() *AccessToken {
	if m != nil {
		return m.AccessToken
	}
	return nil
}

type ValidateAccessTokenReq struct {
	AccessToken *AccessToken `protobuf:"bytes,1,req,name=access_token,json=accessToken" json:"access_token,omitempty"`
}

func (m *ValidateAccessTokenReq) Reset()                    { *m = ValidateAccessTokenReq{} }
func (m *ValidateAccessTokenReq) String() string            { return proto.CompactTextString(m) }
func (*ValidateAccessTokenReq) ProtoMessage()               {}
func (*ValidateAccessTokenReq) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{9} }

func (m *ValidateAccessTokenReq) GetAccessToken() *AccessToken {
	if m != nil {
		return m.AccessToken
	}
	return nil
}

type ValidateAccessTokenRsp struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid" json:"uid"`
}

func (m *ValidateAccessTokenRsp) Reset()                    { *m = ValidateAccessTokenRsp{} }
func (m *ValidateAccessTokenRsp) String() string            { return proto.CompactTextString(m) }
func (*ValidateAccessTokenRsp) ProtoMessage()               {}
func (*ValidateAccessTokenRsp) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{10} }

func (m *ValidateAccessTokenRsp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SessionModel struct {
	Uid            uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Usage          uint32 `protobuf:"varint,2,req,name=usage" json:"usage"`
	CreateTime     uint32 `protobuf:"varint,3,req,name=create_time,json=createTime" json:"create_time"`
	Salt           string `protobuf:"bytes,4,req,name=salt" json:"salt"`
	ValidatedPhone string `protobuf:"bytes,5,opt,name=validated_phone,json=validatedPhone" json:"validated_phone"`
}

func (m *SessionModel) Reset()                    { *m = SessionModel{} }
func (m *SessionModel) String() string            { return proto.CompactTextString(m) }
func (*SessionModel) ProtoMessage()               {}
func (*SessionModel) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{11} }

func (m *SessionModel) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SessionModel) GetUsage() uint32 {
	if m != nil {
		return m.Usage
	}
	return 0
}

func (m *SessionModel) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *SessionModel) GetSalt() string {
	if m != nil {
		return m.Salt
	}
	return ""
}

func (m *SessionModel) GetValidatedPhone() string {
	if m != nil {
		return m.ValidatedPhone
	}
	return ""
}

type SessionInfo struct {
	Uid            uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Usage          uint32 `protobuf:"varint,2,req,name=usage" json:"usage"`
	ValidatedPhone string `protobuf:"bytes,3,opt,name=validated_phone,json=validatedPhone" json:"validated_phone"`
}

func (m *SessionInfo) Reset()                    { *m = SessionInfo{} }
func (m *SessionInfo) String() string            { return proto.CompactTextString(m) }
func (*SessionInfo) ProtoMessage()               {}
func (*SessionInfo) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{12} }

func (m *SessionInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SessionInfo) GetUsage() uint32 {
	if m != nil {
		return m.Usage
	}
	return 0
}

func (m *SessionInfo) GetValidatedPhone() string {
	if m != nil {
		return m.ValidatedPhone
	}
	return ""
}

type GetSessionReq struct {
	Uid     uint32       `protobuf:"varint,1,req,name=uid" json:"uid"`
	Session *SessionInfo `protobuf:"bytes,2,req,name=session" json:"session,omitempty"`
}

func (m *GetSessionReq) Reset()                    { *m = GetSessionReq{} }
func (m *GetSessionReq) String() string            { return proto.CompactTextString(m) }
func (*GetSessionReq) ProtoMessage()               {}
func (*GetSessionReq) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{13} }

func (m *GetSessionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetSessionReq) GetSession() *SessionInfo {
	if m != nil {
		return m.Session
	}
	return nil
}

type GetSessionRsp struct {
	SessionId string `protobuf:"bytes,1,req,name=session_id,json=sessionId" json:"session_id"`
}

func (m *GetSessionRsp) Reset()                    { *m = GetSessionRsp{} }
func (m *GetSessionRsp) String() string            { return proto.CompactTextString(m) }
func (*GetSessionRsp) ProtoMessage()               {}
func (*GetSessionRsp) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{14} }

func (m *GetSessionRsp) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

type ValidateSessionReq struct {
	SessionId string `protobuf:"bytes,1,req,name=session_id,json=sessionId" json:"session_id"`
}

func (m *ValidateSessionReq) Reset()                    { *m = ValidateSessionReq{} }
func (m *ValidateSessionReq) String() string            { return proto.CompactTextString(m) }
func (*ValidateSessionReq) ProtoMessage()               {}
func (*ValidateSessionReq) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{15} }

func (m *ValidateSessionReq) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

type ValidateSessionRsp struct {
	Session *SessionInfo `protobuf:"bytes,1,opt,name=session" json:"session,omitempty"`
}

func (m *ValidateSessionRsp) Reset()                    { *m = ValidateSessionRsp{} }
func (m *ValidateSessionRsp) String() string            { return proto.CompactTextString(m) }
func (*ValidateSessionRsp) ProtoMessage()               {}
func (*ValidateSessionRsp) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{16} }

func (m *ValidateSessionRsp) GetSession() *SessionInfo {
	if m != nil {
		return m.Session
	}
	return nil
}

// 预置安全问题列表
type GetPresetQuestionReq struct {
}

func (m *GetPresetQuestionReq) Reset()                    { *m = GetPresetQuestionReq{} }
func (m *GetPresetQuestionReq) String() string            { return proto.CompactTextString(m) }
func (*GetPresetQuestionReq) ProtoMessage()               {}
func (*GetPresetQuestionReq) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{17} }

type GetPresetQuestionRsp struct {
	Questions []*SecurityQuestion `protobuf:"bytes,1,rep,name=questions" json:"questions,omitempty"`
}

func (m *GetPresetQuestionRsp) Reset()                    { *m = GetPresetQuestionRsp{} }
func (m *GetPresetQuestionRsp) String() string            { return proto.CompactTextString(m) }
func (*GetPresetQuestionRsp) ProtoMessage()               {}
func (*GetPresetQuestionRsp) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{18} }

func (m *GetPresetQuestionRsp) GetQuestions() []*SecurityQuestion {
	if m != nil {
		return m.Questions
	}
	return nil
}

// 设置密保问题
type SetQuestionReq struct {
	Uid         uint32                    `protobuf:"varint,1,req,name=uid" json:"uid"`
	Answers     []*SecurityQuestionAnswer `protobuf:"bytes,2,rep,name=answers" json:"answers,omitempty"`
	AccessToken *AccessToken              `protobuf:"bytes,3,opt,name=access_token,json=accessToken" json:"access_token,omitempty"`
	SessionId   string                    `protobuf:"bytes,4,opt,name=session_id,json=sessionId" json:"session_id"`
	Source      int32                     `protobuf:"varint,5,opt,name=source" json:"source"`
}

func (m *SetQuestionReq) Reset()                    { *m = SetQuestionReq{} }
func (m *SetQuestionReq) String() string            { return proto.CompactTextString(m) }
func (*SetQuestionReq) ProtoMessage()               {}
func (*SetQuestionReq) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{19} }

func (m *SetQuestionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetQuestionReq) GetAnswers() []*SecurityQuestionAnswer {
	if m != nil {
		return m.Answers
	}
	return nil
}

func (m *SetQuestionReq) GetAccessToken() *AccessToken {
	if m != nil {
		return m.AccessToken
	}
	return nil
}

func (m *SetQuestionReq) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

func (m *SetQuestionReq) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type SetQuestionRsp struct {
	Summary *SecuritySummary `protobuf:"bytes,1,opt,name=summary" json:"summary,omitempty"`
}

func (m *SetQuestionRsp) Reset()                    { *m = SetQuestionRsp{} }
func (m *SetQuestionRsp) String() string            { return proto.CompactTextString(m) }
func (*SetQuestionRsp) ProtoMessage()               {}
func (*SetQuestionRsp) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{20} }

func (m *SetQuestionRsp) GetSummary() *SecuritySummary {
	if m != nil {
		return m.Summary
	}
	return nil
}

// 验证密保问题
type VerifyQuestionReq struct {
	Uid     uint32                    `protobuf:"varint,1,req,name=uid" json:"uid"`
	Answers []*SecurityQuestionAnswer `protobuf:"bytes,2,rep,name=answers" json:"answers,omitempty"`
}

func (m *VerifyQuestionReq) Reset()                    { *m = VerifyQuestionReq{} }
func (m *VerifyQuestionReq) String() string            { return proto.CompactTextString(m) }
func (*VerifyQuestionReq) ProtoMessage()               {}
func (*VerifyQuestionReq) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{21} }

func (m *VerifyQuestionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *VerifyQuestionReq) GetAnswers() []*SecurityQuestionAnswer {
	if m != nil {
		return m.Answers
	}
	return nil
}

type VerifyQuestionRsp struct {
}

func (m *VerifyQuestionRsp) Reset()                    { *m = VerifyQuestionRsp{} }
func (m *VerifyQuestionRsp) String() string            { return proto.CompactTextString(m) }
func (*VerifyQuestionRsp) ProtoMessage()               {}
func (*VerifyQuestionRsp) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{22} }

type BindPhoneReq struct {
	Uid            uint32       `protobuf:"varint,1,req,name=uid" json:"uid"`
	Phone          string       `protobuf:"bytes,2,req,name=phone" json:"phone"`
	AccessToken    *AccessToken `protobuf:"bytes,3,opt,name=access_token,json=accessToken" json:"access_token,omitempty"`
	SessionId      string       `protobuf:"bytes,4,opt,name=session_id,json=sessionId" json:"session_id"`
	WithoutSummary bool         `protobuf:"varint,5,opt,name=without_summary,json=withoutSummary" json:"without_summary"`
	Source         int32        `protobuf:"varint,6,opt,name=source" json:"source"`
	PhoneType      PhoneType    `protobuf:"varint,7,opt,name=phone_type,json=phoneType,enum=security.PhoneType" json:"phone_type"`
}

func (m *BindPhoneReq) Reset()                    { *m = BindPhoneReq{} }
func (m *BindPhoneReq) String() string            { return proto.CompactTextString(m) }
func (*BindPhoneReq) ProtoMessage()               {}
func (*BindPhoneReq) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{23} }

func (m *BindPhoneReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BindPhoneReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *BindPhoneReq) GetAccessToken() *AccessToken {
	if m != nil {
		return m.AccessToken
	}
	return nil
}

func (m *BindPhoneReq) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

func (m *BindPhoneReq) GetWithoutSummary() bool {
	if m != nil {
		return m.WithoutSummary
	}
	return false
}

func (m *BindPhoneReq) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *BindPhoneReq) GetPhoneType() PhoneType {
	if m != nil {
		return m.PhoneType
	}
	return PhoneType_UNKNOWN_PHONE
}

type BindPhoneRsp struct {
	Summary *SecuritySummary `protobuf:"bytes,1,opt,name=summary" json:"summary,omitempty"`
}

func (m *BindPhoneRsp) Reset()                    { *m = BindPhoneRsp{} }
func (m *BindPhoneRsp) String() string            { return proto.CompactTextString(m) }
func (*BindPhoneRsp) ProtoMessage()               {}
func (*BindPhoneRsp) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{24} }

func (m *BindPhoneRsp) GetSummary() *SecuritySummary {
	if m != nil {
		return m.Summary
	}
	return nil
}

type UnbindPhoneReq struct {
	Uid            uint32       `protobuf:"varint,1,req,name=uid" json:"uid"`
	AccessToken    *AccessToken `protobuf:"bytes,2,opt,name=access_token,json=accessToken" json:"access_token,omitempty"`
	SessionId      string       `protobuf:"bytes,3,opt,name=session_id,json=sessionId" json:"session_id"`
	WithoutSummary bool         `protobuf:"varint,4,opt,name=without_summary,json=withoutSummary" json:"without_summary"`
	Source         int32        `protobuf:"varint,5,opt,name=source" json:"source"`
}

func (m *UnbindPhoneReq) Reset()                    { *m = UnbindPhoneReq{} }
func (m *UnbindPhoneReq) String() string            { return proto.CompactTextString(m) }
func (*UnbindPhoneReq) ProtoMessage()               {}
func (*UnbindPhoneReq) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{25} }

func (m *UnbindPhoneReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UnbindPhoneReq) GetAccessToken() *AccessToken {
	if m != nil {
		return m.AccessToken
	}
	return nil
}

func (m *UnbindPhoneReq) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

func (m *UnbindPhoneReq) GetWithoutSummary() bool {
	if m != nil {
		return m.WithoutSummary
	}
	return false
}

func (m *UnbindPhoneReq) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type RebindPhoneReq struct {
	Uid         uint32       `protobuf:"varint,1,req,name=uid" json:"uid"`
	Phone       string       `protobuf:"bytes,2,req,name=phone" json:"phone"`
	IsBind      bool         `protobuf:"varint,3,req,name=is_bind,json=isBind" json:"is_bind"`
	Source      int32        `protobuf:"varint,4,opt,name=source" json:"source"`
	WithSummary bool         `protobuf:"varint,5,opt,name=with_summary,json=withSummary" json:"with_summary"`
	AccessToken *AccessToken `protobuf:"bytes,6,opt,name=access_token,json=accessToken" json:"access_token,omitempty"`
	SessionId   string       `protobuf:"bytes,7,opt,name=session_id,json=sessionId" json:"session_id"`
	PhoneType   PhoneType    `protobuf:"varint,8,opt,name=phone_type,json=phoneType,enum=security.PhoneType" json:"phone_type"`
}

func (m *RebindPhoneReq) Reset()                    { *m = RebindPhoneReq{} }
func (m *RebindPhoneReq) String() string            { return proto.CompactTextString(m) }
func (*RebindPhoneReq) ProtoMessage()               {}
func (*RebindPhoneReq) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{26} }

func (m *RebindPhoneReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RebindPhoneReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *RebindPhoneReq) GetIsBind() bool {
	if m != nil {
		return m.IsBind
	}
	return false
}

func (m *RebindPhoneReq) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *RebindPhoneReq) GetWithSummary() bool {
	if m != nil {
		return m.WithSummary
	}
	return false
}

func (m *RebindPhoneReq) GetAccessToken() *AccessToken {
	if m != nil {
		return m.AccessToken
	}
	return nil
}

func (m *RebindPhoneReq) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

func (m *RebindPhoneReq) GetPhoneType() PhoneType {
	if m != nil {
		return m.PhoneType
	}
	return PhoneType_UNKNOWN_PHONE
}

type RebindPhoneResp struct {
	Summary *SecuritySummary `protobuf:"bytes,1,opt,name=summary" json:"summary,omitempty"`
}

func (m *RebindPhoneResp) Reset()                    { *m = RebindPhoneResp{} }
func (m *RebindPhoneResp) String() string            { return proto.CompactTextString(m) }
func (*RebindPhoneResp) ProtoMessage()               {}
func (*RebindPhoneResp) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{27} }

func (m *RebindPhoneResp) GetSummary() *SecuritySummary {
	if m != nil {
		return m.Summary
	}
	return nil
}

type UnbindPhoneRsp struct {
	Summary *SecuritySummary `protobuf:"bytes,1,opt,name=summary" json:"summary,omitempty"`
}

func (m *UnbindPhoneRsp) Reset()                    { *m = UnbindPhoneRsp{} }
func (m *UnbindPhoneRsp) String() string            { return proto.CompactTextString(m) }
func (*UnbindPhoneRsp) ProtoMessage()               {}
func (*UnbindPhoneRsp) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{28} }

func (m *UnbindPhoneRsp) GetSummary() *SecuritySummary {
	if m != nil {
		return m.Summary
	}
	return nil
}

// 密码
type UpdatePasswordReq struct {
	Uid            uint32       `protobuf:"varint,1,req,name=uid" json:"uid"`
	NewPasswd      string       `protobuf:"bytes,2,req,name=new_passwd,json=newPasswd" json:"new_passwd"`
	AccessToken    *AccessToken `protobuf:"bytes,3,opt,name=access_token,json=accessToken" json:"access_token,omitempty"`
	SessionId      string       `protobuf:"bytes,4,opt,name=session_id,json=sessionId" json:"session_id"`
	WithoutSummary bool         `protobuf:"varint,5,opt,name=without_summary,json=withoutSummary" json:"without_summary"`
	Source         int32        `protobuf:"varint,6,opt,name=source" json:"source"`
}

func (m *UpdatePasswordReq) Reset()                    { *m = UpdatePasswordReq{} }
func (m *UpdatePasswordReq) String() string            { return proto.CompactTextString(m) }
func (*UpdatePasswordReq) ProtoMessage()               {}
func (*UpdatePasswordReq) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{29} }

func (m *UpdatePasswordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdatePasswordReq) GetNewPasswd() string {
	if m != nil {
		return m.NewPasswd
	}
	return ""
}

func (m *UpdatePasswordReq) GetAccessToken() *AccessToken {
	if m != nil {
		return m.AccessToken
	}
	return nil
}

func (m *UpdatePasswordReq) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

func (m *UpdatePasswordReq) GetWithoutSummary() bool {
	if m != nil {
		return m.WithoutSummary
	}
	return false
}

func (m *UpdatePasswordReq) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type UpdatePasswordRsp struct {
	Summary *SecuritySummary `protobuf:"bytes,1,opt,name=summary" json:"summary,omitempty"`
}

func (m *UpdatePasswordRsp) Reset()                    { *m = UpdatePasswordRsp{} }
func (m *UpdatePasswordRsp) String() string            { return proto.CompactTextString(m) }
func (*UpdatePasswordRsp) ProtoMessage()               {}
func (*UpdatePasswordRsp) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{30} }

func (m *UpdatePasswordRsp) GetSummary() *SecuritySummary {
	if m != nil {
		return m.Summary
	}
	return nil
}

type DetachThirdPartyReq struct {
	Uid            uint32       `protobuf:"varint,1,req,name=uid" json:"uid"`
	AccessToken    *AccessToken `protobuf:"bytes,2,opt,name=access_token,json=accessToken" json:"access_token,omitempty"`
	SessionId      string       `protobuf:"bytes,3,opt,name=session_id,json=sessionId" json:"session_id"`
	WithoutSummary bool         `protobuf:"varint,4,opt,name=without_summary,json=withoutSummary" json:"without_summary"`
	Source         int32        `protobuf:"varint,5,opt,name=source" json:"source"`
}

func (m *DetachThirdPartyReq) Reset()                    { *m = DetachThirdPartyReq{} }
func (m *DetachThirdPartyReq) String() string            { return proto.CompactTextString(m) }
func (*DetachThirdPartyReq) ProtoMessage()               {}
func (*DetachThirdPartyReq) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{31} }

func (m *DetachThirdPartyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DetachThirdPartyReq) GetAccessToken() *AccessToken {
	if m != nil {
		return m.AccessToken
	}
	return nil
}

func (m *DetachThirdPartyReq) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

func (m *DetachThirdPartyReq) GetWithoutSummary() bool {
	if m != nil {
		return m.WithoutSummary
	}
	return false
}

func (m *DetachThirdPartyReq) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type DetachThirdPartyRsp struct {
	Summary *SecuritySummary `protobuf:"bytes,1,opt,name=summary" json:"summary,omitempty"`
}

func (m *DetachThirdPartyRsp) Reset()                    { *m = DetachThirdPartyRsp{} }
func (m *DetachThirdPartyRsp) String() string            { return proto.CompactTextString(m) }
func (*DetachThirdPartyRsp) ProtoMessage()               {}
func (*DetachThirdPartyRsp) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{32} }

func (m *DetachThirdPartyRsp) GetSummary() *SecuritySummary {
	if m != nil {
		return m.Summary
	}
	return nil
}

type GetOperationLogReq struct {
	Uid       uint32 `protobuf:"varint,1,opt,name=uid" json:"uid"`
	OpType    uint32 `protobuf:"varint,2,opt,name=op_type,json=opType" json:"op_type"`
	OpSource  int32  `protobuf:"varint,3,opt,name=op_source,json=opSource" json:"op_source"`
	BeginTime uint32 `protobuf:"varint,4,opt,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime   uint32 `protobuf:"varint,5,opt,name=end_time,json=endTime" json:"end_time"`
	Offset    uint32 `protobuf:"varint,6,opt,name=offset" json:"offset"`
	Limit     uint32 `protobuf:"varint,7,opt,name=limit" json:"limit"`
}

func (m *GetOperationLogReq) Reset()                    { *m = GetOperationLogReq{} }
func (m *GetOperationLogReq) String() string            { return proto.CompactTextString(m) }
func (*GetOperationLogReq) ProtoMessage()               {}
func (*GetOperationLogReq) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{33} }

func (m *GetOperationLogReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetOperationLogReq) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *GetOperationLogReq) GetOpSource() int32 {
	if m != nil {
		return m.OpSource
	}
	return 0
}

func (m *GetOperationLogReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetOperationLogReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetOperationLogReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetOperationLogReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type LogInfo struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	OpType   uint32 `protobuf:"varint,2,req,name=op_type,json=opType" json:"op_type"`
	OpSource int32  `protobuf:"varint,3,req,name=op_source,json=opSource" json:"op_source"`
	Content  string `protobuf:"bytes,4,req,name=content" json:"content"`
	OpTime   uint32 `protobuf:"varint,5,req,name=op_time,json=opTime" json:"op_time"`
}

func (m *LogInfo) Reset()                    { *m = LogInfo{} }
func (m *LogInfo) String() string            { return proto.CompactTextString(m) }
func (*LogInfo) ProtoMessage()               {}
func (*LogInfo) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{34} }

func (m *LogInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LogInfo) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *LogInfo) GetOpSource() int32 {
	if m != nil {
		return m.OpSource
	}
	return 0
}

func (m *LogInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *LogInfo) GetOpTime() uint32 {
	if m != nil {
		return m.OpTime
	}
	return 0
}

type GetOperationLogRsp struct {
	InfoList []*LogInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList" json:"info_list,omitempty"`
}

func (m *GetOperationLogRsp) Reset()                    { *m = GetOperationLogRsp{} }
func (m *GetOperationLogRsp) String() string            { return proto.CompactTextString(m) }
func (*GetOperationLogRsp) ProtoMessage()               {}
func (*GetOperationLogRsp) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{35} }

func (m *GetOperationLogRsp) GetInfoList() []*LogInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type BatchGetLatestOperationReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
	OpType  uint32   `protobuf:"varint,2,opt,name=op_type,json=opType" json:"op_type"`
}

func (m *BatchGetLatestOperationReq) Reset()         { *m = BatchGetLatestOperationReq{} }
func (m *BatchGetLatestOperationReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetLatestOperationReq) ProtoMessage()    {}
func (*BatchGetLatestOperationReq) Descriptor() ([]byte, []int) {
	return fileDescriptorSecurity, []int{36}
}

func (m *BatchGetLatestOperationReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetLatestOperationReq) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

type UserLatestOperation struct {
	Uid      uint32     `protobuf:"varint,1,req,name=uid" json:"uid"`
	InfoList []*LogInfo `protobuf:"bytes,2,rep,name=info_list,json=infoList" json:"info_list,omitempty"`
}

func (m *UserLatestOperation) Reset()                    { *m = UserLatestOperation{} }
func (m *UserLatestOperation) String() string            { return proto.CompactTextString(m) }
func (*UserLatestOperation) ProtoMessage()               {}
func (*UserLatestOperation) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{37} }

func (m *UserLatestOperation) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserLatestOperation) GetInfoList() []*LogInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type BatchGetLatestOperationRsp struct {
	OpList []*UserLatestOperation `protobuf:"bytes,1,rep,name=op_list,json=opList" json:"op_list,omitempty"`
}

func (m *BatchGetLatestOperationRsp) Reset()         { *m = BatchGetLatestOperationRsp{} }
func (m *BatchGetLatestOperationRsp) String() string { return proto.CompactTextString(m) }
func (*BatchGetLatestOperationRsp) ProtoMessage()    {}
func (*BatchGetLatestOperationRsp) Descriptor() ([]byte, []int) {
	return fileDescriptorSecurity, []int{38}
}

func (m *BatchGetLatestOperationRsp) GetOpList() []*UserLatestOperation {
	if m != nil {
		return m.OpList
	}
	return nil
}

type GetUnregApplyAuditStatusReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUnregApplyAuditStatusReq) Reset()         { *m = GetUnregApplyAuditStatusReq{} }
func (m *GetUnregApplyAuditStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetUnregApplyAuditStatusReq) ProtoMessage()    {}
func (*GetUnregApplyAuditStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorSecurity, []int{39}
}

func (m *GetUnregApplyAuditStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUnregApplyAuditStatusRsp struct {
	Status uint32 `protobuf:"varint,1,opt,name=status" json:"status"`
}

func (m *GetUnregApplyAuditStatusRsp) Reset()         { *m = GetUnregApplyAuditStatusRsp{} }
func (m *GetUnregApplyAuditStatusRsp) String() string { return proto.CompactTextString(m) }
func (*GetUnregApplyAuditStatusRsp) ProtoMessage()    {}
func (*GetUnregApplyAuditStatusRsp) Descriptor() ([]byte, []int) {
	return fileDescriptorSecurity, []int{40}
}

func (m *GetUnregApplyAuditStatusRsp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type UpdateUnregApplyAuditStatusReq struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Status     uint32 `protobuf:"varint,2,req,name=status" json:"status"`
	ApplyId    uint32 `protobuf:"varint,3,opt,name=apply_id,json=applyId" json:"apply_id"`
	OperatorId string `protobuf:"bytes,4,opt,name=operator_id,json=operatorId" json:"operator_id"`
}

func (m *UpdateUnregApplyAuditStatusReq) Reset()         { *m = UpdateUnregApplyAuditStatusReq{} }
func (m *UpdateUnregApplyAuditStatusReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUnregApplyAuditStatusReq) ProtoMessage()    {}
func (*UpdateUnregApplyAuditStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorSecurity, []int{41}
}

func (m *UpdateUnregApplyAuditStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateUnregApplyAuditStatusReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *UpdateUnregApplyAuditStatusReq) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

func (m *UpdateUnregApplyAuditStatusReq) GetOperatorId() string {
	if m != nil {
		return m.OperatorId
	}
	return ""
}

type UpdateUnregApplyAuditStatusRsp struct {
}

func (m *UpdateUnregApplyAuditStatusRsp) Reset()         { *m = UpdateUnregApplyAuditStatusRsp{} }
func (m *UpdateUnregApplyAuditStatusRsp) String() string { return proto.CompactTextString(m) }
func (*UpdateUnregApplyAuditStatusRsp) ProtoMessage()    {}
func (*UpdateUnregApplyAuditStatusRsp) Descriptor() ([]byte, []int) {
	return fileDescriptorSecurity, []int{42}
}

type ClearUnregApplyAuditStatusReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *ClearUnregApplyAuditStatusReq) Reset()         { *m = ClearUnregApplyAuditStatusReq{} }
func (m *ClearUnregApplyAuditStatusReq) String() string { return proto.CompactTextString(m) }
func (*ClearUnregApplyAuditStatusReq) ProtoMessage()    {}
func (*ClearUnregApplyAuditStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorSecurity, []int{43}
}

func (m *ClearUnregApplyAuditStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ClearUnregApplyAuditStatusRsp struct {
}

func (m *ClearUnregApplyAuditStatusRsp) Reset()         { *m = ClearUnregApplyAuditStatusRsp{} }
func (m *ClearUnregApplyAuditStatusRsp) String() string { return proto.CompactTextString(m) }
func (*ClearUnregApplyAuditStatusRsp) ProtoMessage()    {}
func (*ClearUnregApplyAuditStatusRsp) Descriptor() ([]byte, []int) {
	return fileDescriptorSecurity, []int{44}
}

type GetUnregApplyAuditRecordReq struct {
	Uid           uint32 `protobuf:"varint,1,opt,name=uid" json:"uid"`
	Status        uint32 `protobuf:"varint,2,opt,name=status" json:"status"`
	Offset        uint32 `protobuf:"varint,3,opt,name=offset" json:"offset"`
	OffsetApplyId uint32 `protobuf:"varint,4,opt,name=offset_apply_id,json=offsetApplyId" json:"offset_apply_id"`
	Limit         uint32 `protobuf:"varint,5,opt,name=limit" json:"limit"`
	BeginTime     uint32 `protobuf:"varint,6,opt,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime       uint32 `protobuf:"varint,7,opt,name=end_time,json=endTime" json:"end_time"`
	SortType      uint32 `protobuf:"varint,8,opt,name=sort_type,json=sortType" json:"sort_type"`
}

func (m *GetUnregApplyAuditRecordReq) Reset()         { *m = GetUnregApplyAuditRecordReq{} }
func (m *GetUnregApplyAuditRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetUnregApplyAuditRecordReq) ProtoMessage()    {}
func (*GetUnregApplyAuditRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptorSecurity, []int{45}
}

func (m *GetUnregApplyAuditRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUnregApplyAuditRecordReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetUnregApplyAuditRecordReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetUnregApplyAuditRecordReq) GetOffsetApplyId() uint32 {
	if m != nil {
		return m.OffsetApplyId
	}
	return 0
}

func (m *GetUnregApplyAuditRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetUnregApplyAuditRecordReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetUnregApplyAuditRecordReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetUnregApplyAuditRecordReq) GetSortType() uint32 {
	if m != nil {
		return m.SortType
	}
	return 0
}

type UnregApplyAuditInfo struct {
	ApplyId    uint32 `protobuf:"varint,1,req,name=apply_id,json=applyId" json:"apply_id"`
	Uid        uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	Status     uint32 `protobuf:"varint,3,req,name=status" json:"status"`
	ApplyAt    uint32 `protobuf:"varint,4,req,name=apply_at,json=applyAt" json:"apply_at"`
	OperatorId string `protobuf:"bytes,5,opt,name=operator_id,json=operatorId" json:"operator_id"`
	OpAt       uint32 `protobuf:"varint,6,opt,name=op_at,json=opAt" json:"op_at"`
}

func (m *UnregApplyAuditInfo) Reset()                    { *m = UnregApplyAuditInfo{} }
func (m *UnregApplyAuditInfo) String() string            { return proto.CompactTextString(m) }
func (*UnregApplyAuditInfo) ProtoMessage()               {}
func (*UnregApplyAuditInfo) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{46} }

func (m *UnregApplyAuditInfo) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

func (m *UnregApplyAuditInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UnregApplyAuditInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *UnregApplyAuditInfo) GetApplyAt() uint32 {
	if m != nil {
		return m.ApplyAt
	}
	return 0
}

func (m *UnregApplyAuditInfo) GetOperatorId() string {
	if m != nil {
		return m.OperatorId
	}
	return ""
}

func (m *UnregApplyAuditInfo) GetOpAt() uint32 {
	if m != nil {
		return m.OpAt
	}
	return 0
}

type GetUnregApplyAuditRecordRsp struct {
	InfoList []*UnregApplyAuditInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList" json:"info_list,omitempty"`
	Total    uint32                 `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetUnregApplyAuditRecordRsp) Reset()         { *m = GetUnregApplyAuditRecordRsp{} }
func (m *GetUnregApplyAuditRecordRsp) String() string { return proto.CompactTextString(m) }
func (*GetUnregApplyAuditRecordRsp) ProtoMessage()    {}
func (*GetUnregApplyAuditRecordRsp) Descriptor() ([]byte, []int) {
	return fileDescriptorSecurity, []int{47}
}

func (m *GetUnregApplyAuditRecordRsp) GetInfoList() []*UnregApplyAuditInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetUnregApplyAuditRecordRsp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetUserLastOperationReq struct {
	Uid    uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	OpType uint32 `protobuf:"varint,2,opt,name=op_type,json=opType" json:"op_type"`
}

func (m *GetUserLastOperationReq) Reset()                    { *m = GetUserLastOperationReq{} }
func (m *GetUserLastOperationReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserLastOperationReq) ProtoMessage()               {}
func (*GetUserLastOperationReq) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{48} }

func (m *GetUserLastOperationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserLastOperationReq) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

type GetUserLastOperationRsp struct {
	InfoList []*LogInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList" json:"info_list,omitempty"`
}

func (m *GetUserLastOperationRsp) Reset()                    { *m = GetUserLastOperationRsp{} }
func (m *GetUserLastOperationRsp) String() string            { return proto.CompactTextString(m) }
func (*GetUserLastOperationRsp) ProtoMessage()               {}
func (*GetUserLastOperationRsp) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{49} }

func (m *GetUserLastOperationRsp) GetInfoList() []*LogInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type UpdateAutoProcUnregApplyStatusReq struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Status     uint32 `protobuf:"varint,2,req,name=status" json:"status"`
	ApplyId    uint32 `protobuf:"varint,3,opt,name=apply_id,json=applyId" json:"apply_id"`
	Ttid       string `protobuf:"bytes,4,opt,name=ttid" json:"ttid"`
	CancelTime uint32 `protobuf:"varint,5,opt,name=cancel_time,json=cancelTime" json:"cancel_time"`
	MarketId   uint32 `protobuf:"varint,6,opt,name=market_id,json=marketId" json:"market_id"`
}

func (m *UpdateAutoProcUnregApplyStatusReq) Reset()         { *m = UpdateAutoProcUnregApplyStatusReq{} }
func (m *UpdateAutoProcUnregApplyStatusReq) String() string { return proto.CompactTextString(m) }
func (*UpdateAutoProcUnregApplyStatusReq) ProtoMessage()    {}
func (*UpdateAutoProcUnregApplyStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorSecurity, []int{50}
}

func (m *UpdateAutoProcUnregApplyStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateAutoProcUnregApplyStatusReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *UpdateAutoProcUnregApplyStatusReq) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

func (m *UpdateAutoProcUnregApplyStatusReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *UpdateAutoProcUnregApplyStatusReq) GetCancelTime() uint32 {
	if m != nil {
		return m.CancelTime
	}
	return 0
}

func (m *UpdateAutoProcUnregApplyStatusReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type UpdateAutoProcUnregApplyStatusRsp struct {
}

func (m *UpdateAutoProcUnregApplyStatusRsp) Reset()         { *m = UpdateAutoProcUnregApplyStatusRsp{} }
func (m *UpdateAutoProcUnregApplyStatusRsp) String() string { return proto.CompactTextString(m) }
func (*UpdateAutoProcUnregApplyStatusRsp) ProtoMessage()    {}
func (*UpdateAutoProcUnregApplyStatusRsp) Descriptor() ([]byte, []int) {
	return fileDescriptorSecurity, []int{51}
}

type GetAutoProcUnregRecordReq struct {
	Status uint32 `protobuf:"varint,1,req,name=status" json:"status"`
	Uid    uint32 `protobuf:"varint,2,opt,name=uid" json:"uid"`
	Ttid   string `protobuf:"bytes,3,opt,name=ttid" json:"ttid"`
	Sort   bool   `protobuf:"varint,4,opt,name=sort" json:"sort"`
	Offset uint32 `protobuf:"varint,5,opt,name=offset" json:"offset"`
	Limit  uint32 `protobuf:"varint,6,opt,name=limit" json:"limit"`
}

func (m *GetAutoProcUnregRecordReq) Reset()         { *m = GetAutoProcUnregRecordReq{} }
func (m *GetAutoProcUnregRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetAutoProcUnregRecordReq) ProtoMessage()    {}
func (*GetAutoProcUnregRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptorSecurity, []int{52}
}

func (m *GetAutoProcUnregRecordReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetAutoProcUnregRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAutoProcUnregRecordReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *GetAutoProcUnregRecordReq) GetSort() bool {
	if m != nil {
		return m.Sort
	}
	return false
}

func (m *GetAutoProcUnregRecordReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetAutoProcUnregRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type AutoProcUnregInfo struct {
	Id          uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Uid         uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	Ttid        string `protobuf:"bytes,3,req,name=ttid" json:"ttid"`
	Status      uint32 `protobuf:"varint,4,req,name=status" json:"status"`
	ApplyAt     uint32 `protobuf:"varint,5,req,name=apply_at,json=applyAt" json:"apply_at"`
	RemainWait  uint32 `protobuf:"varint,6,req,name=remain_wait,json=remainWait" json:"remain_wait"`
	Visit       bool   `protobuf:"varint,7,opt,name=visit" json:"visit"`
	UnregReason string `protobuf:"bytes,8,opt,name=unreg_reason,json=unregReason" json:"unreg_reason"`
	MarketId    uint32 `protobuf:"varint,9,opt,name=market_id,json=marketId" json:"market_id"`
}

func (m *AutoProcUnregInfo) Reset()                    { *m = AutoProcUnregInfo{} }
func (m *AutoProcUnregInfo) String() string            { return proto.CompactTextString(m) }
func (*AutoProcUnregInfo) ProtoMessage()               {}
func (*AutoProcUnregInfo) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{53} }

func (m *AutoProcUnregInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AutoProcUnregInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AutoProcUnregInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *AutoProcUnregInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *AutoProcUnregInfo) GetApplyAt() uint32 {
	if m != nil {
		return m.ApplyAt
	}
	return 0
}

func (m *AutoProcUnregInfo) GetRemainWait() uint32 {
	if m != nil {
		return m.RemainWait
	}
	return 0
}

func (m *AutoProcUnregInfo) GetVisit() bool {
	if m != nil {
		return m.Visit
	}
	return false
}

func (m *AutoProcUnregInfo) GetUnregReason() string {
	if m != nil {
		return m.UnregReason
	}
	return ""
}

func (m *AutoProcUnregInfo) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type GetAutoProcUnregRecordRsp struct {
	InfoList []*AutoProcUnregInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList" json:"info_list,omitempty"`
	Total    uint32               `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetAutoProcUnregRecordRsp) Reset()         { *m = GetAutoProcUnregRecordRsp{} }
func (m *GetAutoProcUnregRecordRsp) String() string { return proto.CompactTextString(m) }
func (*GetAutoProcUnregRecordRsp) ProtoMessage()    {}
func (*GetAutoProcUnregRecordRsp) Descriptor() ([]byte, []int) {
	return fileDescriptorSecurity, []int{54}
}

func (m *GetAutoProcUnregRecordRsp) GetInfoList() []*AutoProcUnregInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetAutoProcUnregRecordRsp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 注销回访
type UpdateUnregVisitReq struct {
	Id          uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Visit       bool   `protobuf:"varint,2,req,name=visit" json:"visit"`
	UnregReason string `protobuf:"bytes,3,req,name=unreg_reason,json=unregReason" json:"unreg_reason"`
}

func (m *UpdateUnregVisitReq) Reset()                    { *m = UpdateUnregVisitReq{} }
func (m *UpdateUnregVisitReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateUnregVisitReq) ProtoMessage()               {}
func (*UpdateUnregVisitReq) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{55} }

func (m *UpdateUnregVisitReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateUnregVisitReq) GetVisit() bool {
	if m != nil {
		return m.Visit
	}
	return false
}

func (m *UpdateUnregVisitReq) GetUnregReason() string {
	if m != nil {
		return m.UnregReason
	}
	return ""
}

type UpdateUnregVisitRsp struct {
}

func (m *UpdateUnregVisitRsp) Reset()                    { *m = UpdateUnregVisitRsp{} }
func (m *UpdateUnregVisitRsp) String() string            { return proto.CompactTextString(m) }
func (*UpdateUnregVisitRsp) ProtoMessage()               {}
func (*UpdateUnregVisitRsp) Descriptor() ([]byte, []int) { return fileDescriptorSecurity, []int{56} }

func init() {
	proto.RegisterType((*SecurityQuestion)(nil), "security.SecurityQuestion")
	proto.RegisterType((*SecurityQuestionAnswer)(nil), "security.SecurityQuestionAnswer")
	proto.RegisterType((*GetSummaryReq)(nil), "security.GetSummaryReq")
	proto.RegisterType((*SecuritySummary)(nil), "security.SecuritySummary")
	proto.RegisterType((*GetSummaryRsp)(nil), "security.GetSummaryRsp")
	proto.RegisterType((*TokenModel)(nil), "security.TokenModel")
	proto.RegisterType((*AccessToken)(nil), "security.AccessToken")
	proto.RegisterType((*GetAccessTokenReq)(nil), "security.GetAccessTokenReq")
	proto.RegisterType((*GetAccessTokenRsp)(nil), "security.GetAccessTokenRsp")
	proto.RegisterType((*ValidateAccessTokenReq)(nil), "security.ValidateAccessTokenReq")
	proto.RegisterType((*ValidateAccessTokenRsp)(nil), "security.ValidateAccessTokenRsp")
	proto.RegisterType((*SessionModel)(nil), "security.SessionModel")
	proto.RegisterType((*SessionInfo)(nil), "security.SessionInfo")
	proto.RegisterType((*GetSessionReq)(nil), "security.GetSessionReq")
	proto.RegisterType((*GetSessionRsp)(nil), "security.GetSessionRsp")
	proto.RegisterType((*ValidateSessionReq)(nil), "security.ValidateSessionReq")
	proto.RegisterType((*ValidateSessionRsp)(nil), "security.ValidateSessionRsp")
	proto.RegisterType((*GetPresetQuestionReq)(nil), "security.GetPresetQuestionReq")
	proto.RegisterType((*GetPresetQuestionRsp)(nil), "security.GetPresetQuestionRsp")
	proto.RegisterType((*SetQuestionReq)(nil), "security.SetQuestionReq")
	proto.RegisterType((*SetQuestionRsp)(nil), "security.SetQuestionRsp")
	proto.RegisterType((*VerifyQuestionReq)(nil), "security.VerifyQuestionReq")
	proto.RegisterType((*VerifyQuestionRsp)(nil), "security.VerifyQuestionRsp")
	proto.RegisterType((*BindPhoneReq)(nil), "security.BindPhoneReq")
	proto.RegisterType((*BindPhoneRsp)(nil), "security.BindPhoneRsp")
	proto.RegisterType((*UnbindPhoneReq)(nil), "security.UnbindPhoneReq")
	proto.RegisterType((*RebindPhoneReq)(nil), "security.RebindPhoneReq")
	proto.RegisterType((*RebindPhoneResp)(nil), "security.RebindPhoneResp")
	proto.RegisterType((*UnbindPhoneRsp)(nil), "security.UnbindPhoneRsp")
	proto.RegisterType((*UpdatePasswordReq)(nil), "security.UpdatePasswordReq")
	proto.RegisterType((*UpdatePasswordRsp)(nil), "security.UpdatePasswordRsp")
	proto.RegisterType((*DetachThirdPartyReq)(nil), "security.DetachThirdPartyReq")
	proto.RegisterType((*DetachThirdPartyRsp)(nil), "security.DetachThirdPartyRsp")
	proto.RegisterType((*GetOperationLogReq)(nil), "security.GetOperationLogReq")
	proto.RegisterType((*LogInfo)(nil), "security.LogInfo")
	proto.RegisterType((*GetOperationLogRsp)(nil), "security.GetOperationLogRsp")
	proto.RegisterType((*BatchGetLatestOperationReq)(nil), "security.BatchGetLatestOperationReq")
	proto.RegisterType((*UserLatestOperation)(nil), "security.UserLatestOperation")
	proto.RegisterType((*BatchGetLatestOperationRsp)(nil), "security.BatchGetLatestOperationRsp")
	proto.RegisterType((*GetUnregApplyAuditStatusReq)(nil), "security.GetUnregApplyAuditStatusReq")
	proto.RegisterType((*GetUnregApplyAuditStatusRsp)(nil), "security.GetUnregApplyAuditStatusRsp")
	proto.RegisterType((*UpdateUnregApplyAuditStatusReq)(nil), "security.UpdateUnregApplyAuditStatusReq")
	proto.RegisterType((*UpdateUnregApplyAuditStatusRsp)(nil), "security.UpdateUnregApplyAuditStatusRsp")
	proto.RegisterType((*ClearUnregApplyAuditStatusReq)(nil), "security.ClearUnregApplyAuditStatusReq")
	proto.RegisterType((*ClearUnregApplyAuditStatusRsp)(nil), "security.ClearUnregApplyAuditStatusRsp")
	proto.RegisterType((*GetUnregApplyAuditRecordReq)(nil), "security.GetUnregApplyAuditRecordReq")
	proto.RegisterType((*UnregApplyAuditInfo)(nil), "security.UnregApplyAuditInfo")
	proto.RegisterType((*GetUnregApplyAuditRecordRsp)(nil), "security.GetUnregApplyAuditRecordRsp")
	proto.RegisterType((*GetUserLastOperationReq)(nil), "security.GetUserLastOperationReq")
	proto.RegisterType((*GetUserLastOperationRsp)(nil), "security.GetUserLastOperationRsp")
	proto.RegisterType((*UpdateAutoProcUnregApplyStatusReq)(nil), "security.UpdateAutoProcUnregApplyStatusReq")
	proto.RegisterType((*UpdateAutoProcUnregApplyStatusRsp)(nil), "security.UpdateAutoProcUnregApplyStatusRsp")
	proto.RegisterType((*GetAutoProcUnregRecordReq)(nil), "security.GetAutoProcUnregRecordReq")
	proto.RegisterType((*AutoProcUnregInfo)(nil), "security.AutoProcUnregInfo")
	proto.RegisterType((*GetAutoProcUnregRecordRsp)(nil), "security.GetAutoProcUnregRecordRsp")
	proto.RegisterType((*UpdateUnregVisitReq)(nil), "security.UpdateUnregVisitReq")
	proto.RegisterType((*UpdateUnregVisitRsp)(nil), "security.UpdateUnregVisitRsp")
	proto.RegisterEnum("security.Security_Level", Security_Level_name, Security_Level_value)
	proto.RegisterEnum("security.ThirdParty_Attached", ThirdParty_Attached_name, ThirdParty_Attached_value)
	proto.RegisterEnum("security.PhoneType", PhoneType_name, PhoneType_value)
	proto.RegisterEnum("security.Session_Usage", Session_Usage_name, Session_Usage_value)
	proto.RegisterEnum("security.Verification_Scheme", Verification_Scheme_name, Verification_Scheme_value)
	proto.RegisterEnum("security.OP_TYPE", OP_TYPE_name, OP_TYPE_value)
	proto.RegisterEnum("security.SortType", SortType_name, SortType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Security service

type SecurityClient interface {
	// 获取安全状态
	GetSummary(ctx context.Context, in *GetSummaryReq, opts ...grpc.CallOption) (*GetSummaryRsp, error)
	// 访问令牌
	GetAccessToken(ctx context.Context, in *GetAccessTokenReq, opts ...grpc.CallOption) (*GetAccessTokenRsp, error)
	ValidateAccessToken(ctx context.Context, in *ValidateAccessTokenReq, opts ...grpc.CallOption) (*ValidateAccessTokenRsp, error)
	// session
	GetSession(ctx context.Context, in *GetSessionReq, opts ...grpc.CallOption) (*GetSessionRsp, error)
	ValidateSession(ctx context.Context, in *ValidateSessionReq, opts ...grpc.CallOption) (*ValidateSessionRsp, error)
	// 获取预置问题列表
	GetPresetQuestion(ctx context.Context, in *GetPresetQuestionReq, opts ...grpc.CallOption) (*GetPresetQuestionRsp, error)
	// 设置/取消密保问题
	SetQuestion(ctx context.Context, in *SetQuestionReq, opts ...grpc.CallOption) (*SetQuestionRsp, error)
	// 验证密保问题
	VerifyQuestion(ctx context.Context, in *VerifyQuestionReq, opts ...grpc.CallOption) (*VerifyQuestionRsp, error)
	BindPhone(ctx context.Context, in *BindPhoneReq, opts ...grpc.CallOption) (*BindPhoneRsp, error)
	UnbindPhone(ctx context.Context, in *UnbindPhoneReq, opts ...grpc.CallOption) (*UnbindPhoneRsp, error)
	RebindPhone(ctx context.Context, in *RebindPhoneReq, opts ...grpc.CallOption) (*RebindPhoneResp, error)
	// 修改密码
	UpdatePassword(ctx context.Context, in *UpdatePasswordReq, opts ...grpc.CallOption) (*UpdatePasswordRsp, error)
	//
	DetachThirdParty(ctx context.Context, in *DetachThirdPartyReq, opts ...grpc.CallOption) (*DetachThirdPartyRsp, error)
	GetOperationLog(ctx context.Context, in *GetOperationLogReq, opts ...grpc.CallOption) (*GetOperationLogRsp, error)
	GetUnregApplyAuditStatus(ctx context.Context, in *GetUnregApplyAuditStatusReq, opts ...grpc.CallOption) (*GetUnregApplyAuditStatusRsp, error)
	UpdateUnregApplyAuditStatus(ctx context.Context, in *UpdateUnregApplyAuditStatusReq, opts ...grpc.CallOption) (*UpdateUnregApplyAuditStatusRsp, error)
	ClearUnregApplyAuditStatus(ctx context.Context, in *ClearUnregApplyAuditStatusReq, opts ...grpc.CallOption) (*ClearUnregApplyAuditStatusRsp, error)
	GetUnregApplyAuditRecord(ctx context.Context, in *GetUnregApplyAuditRecordReq, opts ...grpc.CallOption) (*GetUnregApplyAuditRecordRsp, error)
	BatchGetLatestOperation(ctx context.Context, in *BatchGetLatestOperationReq, opts ...grpc.CallOption) (*BatchGetLatestOperationRsp, error)
	GetUserLastOperation(ctx context.Context, in *GetUserLastOperationReq, opts ...grpc.CallOption) (*GetUserLastOperationRsp, error)
	// 新的用户注销流程接口
	UpdateAutoProcUnregApplyStatus(ctx context.Context, in *UpdateAutoProcUnregApplyStatusReq, opts ...grpc.CallOption) (*UpdateAutoProcUnregApplyStatusRsp, error)
	GetAutoProcUnregRecord(ctx context.Context, in *GetAutoProcUnregRecordReq, opts ...grpc.CallOption) (*GetAutoProcUnregRecordRsp, error)
	UpdateUnregVisit(ctx context.Context, in *UpdateUnregVisitReq, opts ...grpc.CallOption) (*UpdateUnregVisitRsp, error)
}

type securityClient struct {
	cc *grpc.ClientConn
}

func NewSecurityClient(cc *grpc.ClientConn) SecurityClient {
	return &securityClient{cc}
}

func (c *securityClient) GetSummary(ctx context.Context, in *GetSummaryReq, opts ...grpc.CallOption) (*GetSummaryRsp, error) {
	out := new(GetSummaryRsp)
	err := grpc.Invoke(ctx, "/security.Security/GetSummary", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) GetAccessToken(ctx context.Context, in *GetAccessTokenReq, opts ...grpc.CallOption) (*GetAccessTokenRsp, error) {
	out := new(GetAccessTokenRsp)
	err := grpc.Invoke(ctx, "/security.Security/GetAccessToken", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) ValidateAccessToken(ctx context.Context, in *ValidateAccessTokenReq, opts ...grpc.CallOption) (*ValidateAccessTokenRsp, error) {
	out := new(ValidateAccessTokenRsp)
	err := grpc.Invoke(ctx, "/security.Security/ValidateAccessToken", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) GetSession(ctx context.Context, in *GetSessionReq, opts ...grpc.CallOption) (*GetSessionRsp, error) {
	out := new(GetSessionRsp)
	err := grpc.Invoke(ctx, "/security.Security/GetSession", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) ValidateSession(ctx context.Context, in *ValidateSessionReq, opts ...grpc.CallOption) (*ValidateSessionRsp, error) {
	out := new(ValidateSessionRsp)
	err := grpc.Invoke(ctx, "/security.Security/ValidateSession", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) GetPresetQuestion(ctx context.Context, in *GetPresetQuestionReq, opts ...grpc.CallOption) (*GetPresetQuestionRsp, error) {
	out := new(GetPresetQuestionRsp)
	err := grpc.Invoke(ctx, "/security.Security/GetPresetQuestion", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) SetQuestion(ctx context.Context, in *SetQuestionReq, opts ...grpc.CallOption) (*SetQuestionRsp, error) {
	out := new(SetQuestionRsp)
	err := grpc.Invoke(ctx, "/security.Security/SetQuestion", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) VerifyQuestion(ctx context.Context, in *VerifyQuestionReq, opts ...grpc.CallOption) (*VerifyQuestionRsp, error) {
	out := new(VerifyQuestionRsp)
	err := grpc.Invoke(ctx, "/security.Security/VerifyQuestion", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) BindPhone(ctx context.Context, in *BindPhoneReq, opts ...grpc.CallOption) (*BindPhoneRsp, error) {
	out := new(BindPhoneRsp)
	err := grpc.Invoke(ctx, "/security.Security/BindPhone", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) UnbindPhone(ctx context.Context, in *UnbindPhoneReq, opts ...grpc.CallOption) (*UnbindPhoneRsp, error) {
	out := new(UnbindPhoneRsp)
	err := grpc.Invoke(ctx, "/security.Security/UnbindPhone", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) RebindPhone(ctx context.Context, in *RebindPhoneReq, opts ...grpc.CallOption) (*RebindPhoneResp, error) {
	out := new(RebindPhoneResp)
	err := grpc.Invoke(ctx, "/security.Security/RebindPhone", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) UpdatePassword(ctx context.Context, in *UpdatePasswordReq, opts ...grpc.CallOption) (*UpdatePasswordRsp, error) {
	out := new(UpdatePasswordRsp)
	err := grpc.Invoke(ctx, "/security.Security/UpdatePassword", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) DetachThirdParty(ctx context.Context, in *DetachThirdPartyReq, opts ...grpc.CallOption) (*DetachThirdPartyRsp, error) {
	out := new(DetachThirdPartyRsp)
	err := grpc.Invoke(ctx, "/security.Security/DetachThirdParty", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) GetOperationLog(ctx context.Context, in *GetOperationLogReq, opts ...grpc.CallOption) (*GetOperationLogRsp, error) {
	out := new(GetOperationLogRsp)
	err := grpc.Invoke(ctx, "/security.Security/GetOperationLog", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) GetUnregApplyAuditStatus(ctx context.Context, in *GetUnregApplyAuditStatusReq, opts ...grpc.CallOption) (*GetUnregApplyAuditStatusRsp, error) {
	out := new(GetUnregApplyAuditStatusRsp)
	err := grpc.Invoke(ctx, "/security.Security/GetUnregApplyAuditStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) UpdateUnregApplyAuditStatus(ctx context.Context, in *UpdateUnregApplyAuditStatusReq, opts ...grpc.CallOption) (*UpdateUnregApplyAuditStatusRsp, error) {
	out := new(UpdateUnregApplyAuditStatusRsp)
	err := grpc.Invoke(ctx, "/security.Security/UpdateUnregApplyAuditStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) ClearUnregApplyAuditStatus(ctx context.Context, in *ClearUnregApplyAuditStatusReq, opts ...grpc.CallOption) (*ClearUnregApplyAuditStatusRsp, error) {
	out := new(ClearUnregApplyAuditStatusRsp)
	err := grpc.Invoke(ctx, "/security.Security/ClearUnregApplyAuditStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) GetUnregApplyAuditRecord(ctx context.Context, in *GetUnregApplyAuditRecordReq, opts ...grpc.CallOption) (*GetUnregApplyAuditRecordRsp, error) {
	out := new(GetUnregApplyAuditRecordRsp)
	err := grpc.Invoke(ctx, "/security.Security/GetUnregApplyAuditRecord", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) BatchGetLatestOperation(ctx context.Context, in *BatchGetLatestOperationReq, opts ...grpc.CallOption) (*BatchGetLatestOperationRsp, error) {
	out := new(BatchGetLatestOperationRsp)
	err := grpc.Invoke(ctx, "/security.Security/BatchGetLatestOperation", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) GetUserLastOperation(ctx context.Context, in *GetUserLastOperationReq, opts ...grpc.CallOption) (*GetUserLastOperationRsp, error) {
	out := new(GetUserLastOperationRsp)
	err := grpc.Invoke(ctx, "/security.Security/GetUserLastOperation", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) UpdateAutoProcUnregApplyStatus(ctx context.Context, in *UpdateAutoProcUnregApplyStatusReq, opts ...grpc.CallOption) (*UpdateAutoProcUnregApplyStatusRsp, error) {
	out := new(UpdateAutoProcUnregApplyStatusRsp)
	err := grpc.Invoke(ctx, "/security.Security/UpdateAutoProcUnregApplyStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) GetAutoProcUnregRecord(ctx context.Context, in *GetAutoProcUnregRecordReq, opts ...grpc.CallOption) (*GetAutoProcUnregRecordRsp, error) {
	out := new(GetAutoProcUnregRecordRsp)
	err := grpc.Invoke(ctx, "/security.Security/GetAutoProcUnregRecord", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityClient) UpdateUnregVisit(ctx context.Context, in *UpdateUnregVisitReq, opts ...grpc.CallOption) (*UpdateUnregVisitRsp, error) {
	out := new(UpdateUnregVisitRsp)
	err := grpc.Invoke(ctx, "/security.Security/UpdateUnregVisit", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Security service

type SecurityServer interface {
	// 获取安全状态
	GetSummary(context.Context, *GetSummaryReq) (*GetSummaryRsp, error)
	// 访问令牌
	GetAccessToken(context.Context, *GetAccessTokenReq) (*GetAccessTokenRsp, error)
	ValidateAccessToken(context.Context, *ValidateAccessTokenReq) (*ValidateAccessTokenRsp, error)
	// session
	GetSession(context.Context, *GetSessionReq) (*GetSessionRsp, error)
	ValidateSession(context.Context, *ValidateSessionReq) (*ValidateSessionRsp, error)
	// 获取预置问题列表
	GetPresetQuestion(context.Context, *GetPresetQuestionReq) (*GetPresetQuestionRsp, error)
	// 设置/取消密保问题
	SetQuestion(context.Context, *SetQuestionReq) (*SetQuestionRsp, error)
	// 验证密保问题
	VerifyQuestion(context.Context, *VerifyQuestionReq) (*VerifyQuestionRsp, error)
	BindPhone(context.Context, *BindPhoneReq) (*BindPhoneRsp, error)
	UnbindPhone(context.Context, *UnbindPhoneReq) (*UnbindPhoneRsp, error)
	RebindPhone(context.Context, *RebindPhoneReq) (*RebindPhoneResp, error)
	// 修改密码
	UpdatePassword(context.Context, *UpdatePasswordReq) (*UpdatePasswordRsp, error)
	//
	DetachThirdParty(context.Context, *DetachThirdPartyReq) (*DetachThirdPartyRsp, error)
	GetOperationLog(context.Context, *GetOperationLogReq) (*GetOperationLogRsp, error)
	GetUnregApplyAuditStatus(context.Context, *GetUnregApplyAuditStatusReq) (*GetUnregApplyAuditStatusRsp, error)
	UpdateUnregApplyAuditStatus(context.Context, *UpdateUnregApplyAuditStatusReq) (*UpdateUnregApplyAuditStatusRsp, error)
	ClearUnregApplyAuditStatus(context.Context, *ClearUnregApplyAuditStatusReq) (*ClearUnregApplyAuditStatusRsp, error)
	GetUnregApplyAuditRecord(context.Context, *GetUnregApplyAuditRecordReq) (*GetUnregApplyAuditRecordRsp, error)
	BatchGetLatestOperation(context.Context, *BatchGetLatestOperationReq) (*BatchGetLatestOperationRsp, error)
	GetUserLastOperation(context.Context, *GetUserLastOperationReq) (*GetUserLastOperationRsp, error)
	// 新的用户注销流程接口
	UpdateAutoProcUnregApplyStatus(context.Context, *UpdateAutoProcUnregApplyStatusReq) (*UpdateAutoProcUnregApplyStatusRsp, error)
	GetAutoProcUnregRecord(context.Context, *GetAutoProcUnregRecordReq) (*GetAutoProcUnregRecordRsp, error)
	UpdateUnregVisit(context.Context, *UpdateUnregVisitReq) (*UpdateUnregVisitRsp, error)
}

func RegisterSecurityServer(s *grpc.Server, srv SecurityServer) {
	s.RegisterService(&_Security_serviceDesc, srv)
}

func _Security_GetSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSummaryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).GetSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/GetSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).GetSummary(ctx, req.(*GetSummaryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_GetAccessToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccessTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).GetAccessToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/GetAccessToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).GetAccessToken(ctx, req.(*GetAccessTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_ValidateAccessToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateAccessTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).ValidateAccessToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/ValidateAccessToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).ValidateAccessToken(ctx, req.(*ValidateAccessTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_GetSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSessionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).GetSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/GetSession",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).GetSession(ctx, req.(*GetSessionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_ValidateSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateSessionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).ValidateSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/ValidateSession",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).ValidateSession(ctx, req.(*ValidateSessionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_GetPresetQuestion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresetQuestionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).GetPresetQuestion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/GetPresetQuestion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).GetPresetQuestion(ctx, req.(*GetPresetQuestionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_SetQuestion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetQuestionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).SetQuestion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/SetQuestion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).SetQuestion(ctx, req.(*SetQuestionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_VerifyQuestion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyQuestionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).VerifyQuestion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/VerifyQuestion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).VerifyQuestion(ctx, req.(*VerifyQuestionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_BindPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindPhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).BindPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/BindPhone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).BindPhone(ctx, req.(*BindPhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_UnbindPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnbindPhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).UnbindPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/UnbindPhone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).UnbindPhone(ctx, req.(*UnbindPhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_RebindPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RebindPhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).RebindPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/RebindPhone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).RebindPhone(ctx, req.(*RebindPhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_UpdatePassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePasswordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).UpdatePassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/UpdatePassword",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).UpdatePassword(ctx, req.(*UpdatePasswordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_DetachThirdParty_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetachThirdPartyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).DetachThirdParty(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/DetachThirdParty",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).DetachThirdParty(ctx, req.(*DetachThirdPartyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_GetOperationLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOperationLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).GetOperationLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/GetOperationLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).GetOperationLog(ctx, req.(*GetOperationLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_GetUnregApplyAuditStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUnregApplyAuditStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).GetUnregApplyAuditStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/GetUnregApplyAuditStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).GetUnregApplyAuditStatus(ctx, req.(*GetUnregApplyAuditStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_UpdateUnregApplyAuditStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUnregApplyAuditStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).UpdateUnregApplyAuditStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/UpdateUnregApplyAuditStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).UpdateUnregApplyAuditStatus(ctx, req.(*UpdateUnregApplyAuditStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_ClearUnregApplyAuditStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearUnregApplyAuditStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).ClearUnregApplyAuditStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/ClearUnregApplyAuditStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).ClearUnregApplyAuditStatus(ctx, req.(*ClearUnregApplyAuditStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_GetUnregApplyAuditRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUnregApplyAuditRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).GetUnregApplyAuditRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/GetUnregApplyAuditRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).GetUnregApplyAuditRecord(ctx, req.(*GetUnregApplyAuditRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_BatchGetLatestOperation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetLatestOperationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).BatchGetLatestOperation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/BatchGetLatestOperation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).BatchGetLatestOperation(ctx, req.(*BatchGetLatestOperationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_GetUserLastOperation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLastOperationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).GetUserLastOperation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/GetUserLastOperation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).GetUserLastOperation(ctx, req.(*GetUserLastOperationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_UpdateAutoProcUnregApplyStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAutoProcUnregApplyStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).UpdateAutoProcUnregApplyStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/UpdateAutoProcUnregApplyStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).UpdateAutoProcUnregApplyStatus(ctx, req.(*UpdateAutoProcUnregApplyStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_GetAutoProcUnregRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAutoProcUnregRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).GetAutoProcUnregRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/GetAutoProcUnregRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).GetAutoProcUnregRecord(ctx, req.(*GetAutoProcUnregRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Security_UpdateUnregVisit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUnregVisitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServer).UpdateUnregVisit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.Security/UpdateUnregVisit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServer).UpdateUnregVisit(ctx, req.(*UpdateUnregVisitReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Security_serviceDesc = grpc.ServiceDesc{
	ServiceName: "security.Security",
	HandlerType: (*SecurityServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSummary",
			Handler:    _Security_GetSummary_Handler,
		},
		{
			MethodName: "GetAccessToken",
			Handler:    _Security_GetAccessToken_Handler,
		},
		{
			MethodName: "ValidateAccessToken",
			Handler:    _Security_ValidateAccessToken_Handler,
		},
		{
			MethodName: "GetSession",
			Handler:    _Security_GetSession_Handler,
		},
		{
			MethodName: "ValidateSession",
			Handler:    _Security_ValidateSession_Handler,
		},
		{
			MethodName: "GetPresetQuestion",
			Handler:    _Security_GetPresetQuestion_Handler,
		},
		{
			MethodName: "SetQuestion",
			Handler:    _Security_SetQuestion_Handler,
		},
		{
			MethodName: "VerifyQuestion",
			Handler:    _Security_VerifyQuestion_Handler,
		},
		{
			MethodName: "BindPhone",
			Handler:    _Security_BindPhone_Handler,
		},
		{
			MethodName: "UnbindPhone",
			Handler:    _Security_UnbindPhone_Handler,
		},
		{
			MethodName: "RebindPhone",
			Handler:    _Security_RebindPhone_Handler,
		},
		{
			MethodName: "UpdatePassword",
			Handler:    _Security_UpdatePassword_Handler,
		},
		{
			MethodName: "DetachThirdParty",
			Handler:    _Security_DetachThirdParty_Handler,
		},
		{
			MethodName: "GetOperationLog",
			Handler:    _Security_GetOperationLog_Handler,
		},
		{
			MethodName: "GetUnregApplyAuditStatus",
			Handler:    _Security_GetUnregApplyAuditStatus_Handler,
		},
		{
			MethodName: "UpdateUnregApplyAuditStatus",
			Handler:    _Security_UpdateUnregApplyAuditStatus_Handler,
		},
		{
			MethodName: "ClearUnregApplyAuditStatus",
			Handler:    _Security_ClearUnregApplyAuditStatus_Handler,
		},
		{
			MethodName: "GetUnregApplyAuditRecord",
			Handler:    _Security_GetUnregApplyAuditRecord_Handler,
		},
		{
			MethodName: "BatchGetLatestOperation",
			Handler:    _Security_BatchGetLatestOperation_Handler,
		},
		{
			MethodName: "GetUserLastOperation",
			Handler:    _Security_GetUserLastOperation_Handler,
		},
		{
			MethodName: "UpdateAutoProcUnregApplyStatus",
			Handler:    _Security_UpdateAutoProcUnregApplyStatus_Handler,
		},
		{
			MethodName: "GetAutoProcUnregRecord",
			Handler:    _Security_GetAutoProcUnregRecord_Handler,
		},
		{
			MethodName: "UpdateUnregVisit",
			Handler:    _Security_UpdateUnregVisit_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/security/security.proto",
}

func (m *SecurityQuestion) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SecurityQuestion) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.Question)))
	i += copy(dAtA[i:], m.Question)
	return i, nil
}

func (m *SecurityQuestionAnswer) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SecurityQuestionAnswer) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.Answer)))
	i += copy(dAtA[i:], m.Answer)
	return i, nil
}

func (m *GetSummaryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSummaryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *SecuritySummary) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SecuritySummary) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Level))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	if len(m.Questions) > 0 {
		for _, msg := range m.Questions {
			dAtA[i] = 0x22
			i++
			i = encodeVarintSecurity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.ThirdPartyAttached))
	dAtA[i] = 0x30
	i++
	if m.PasswordSet {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x38
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.PhoneType))
	return i, nil
}

func (m *GetSummaryRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSummaryRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Summary == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("summary")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSecurity(dAtA, i, uint64(m.Summary.Size()))
		n1, err := m.Summary.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *TokenModel) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TokenModel) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Expired))
	return i, nil
}

func (m *AccessToken) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AccessToken) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.Token)))
	i += copy(dAtA[i:], m.Token)
	return i, nil
}

func (m *GetAccessTokenReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAccessTokenReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetAccessTokenRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAccessTokenRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.AccessToken == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("access_token")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSecurity(dAtA, i, uint64(m.AccessToken.Size()))
		n2, err := m.AccessToken.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *ValidateAccessTokenReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ValidateAccessTokenReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.AccessToken == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("access_token")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSecurity(dAtA, i, uint64(m.AccessToken.Size()))
		n3, err := m.AccessToken.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *ValidateAccessTokenRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ValidateAccessTokenRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *SessionModel) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SessionModel) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Usage))
	dAtA[i] = 0x18
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.CreateTime))
	dAtA[i] = 0x22
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.Salt)))
	i += copy(dAtA[i:], m.Salt)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.ValidatedPhone)))
	i += copy(dAtA[i:], m.ValidatedPhone)
	return i, nil
}

func (m *SessionInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SessionInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Usage))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.ValidatedPhone)))
	i += copy(dAtA[i:], m.ValidatedPhone)
	return i, nil
}

func (m *GetSessionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSessionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	if m.Session == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("session")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSecurity(dAtA, i, uint64(m.Session.Size()))
		n4, err := m.Session.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *GetSessionRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSessionRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.SessionId)))
	i += copy(dAtA[i:], m.SessionId)
	return i, nil
}

func (m *ValidateSessionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ValidateSessionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.SessionId)))
	i += copy(dAtA[i:], m.SessionId)
	return i, nil
}

func (m *ValidateSessionRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ValidateSessionRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Session != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSecurity(dAtA, i, uint64(m.Session.Size()))
		n5, err := m.Session.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *GetPresetQuestionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresetQuestionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetPresetQuestionRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresetQuestionRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Questions) > 0 {
		for _, msg := range m.Questions {
			dAtA[i] = 0xa
			i++
			i = encodeVarintSecurity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetQuestionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetQuestionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	if len(m.Answers) > 0 {
		for _, msg := range m.Answers {
			dAtA[i] = 0x12
			i++
			i = encodeVarintSecurity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.AccessToken != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintSecurity(dAtA, i, uint64(m.AccessToken.Size()))
		n6, err := m.AccessToken.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x22
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.SessionId)))
	i += copy(dAtA[i:], m.SessionId)
	dAtA[i] = 0x28
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Source))
	return i, nil
}

func (m *SetQuestionRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetQuestionRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Summary != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSecurity(dAtA, i, uint64(m.Summary.Size()))
		n7, err := m.Summary.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *VerifyQuestionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyQuestionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	if len(m.Answers) > 0 {
		for _, msg := range m.Answers {
			dAtA[i] = 0x12
			i++
			i = encodeVarintSecurity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *VerifyQuestionRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyQuestionRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *BindPhoneReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BindPhoneReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	if m.AccessToken != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintSecurity(dAtA, i, uint64(m.AccessToken.Size()))
		n8, err := m.AccessToken.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	dAtA[i] = 0x22
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.SessionId)))
	i += copy(dAtA[i:], m.SessionId)
	dAtA[i] = 0x28
	i++
	if m.WithoutSummary {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x30
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Source))
	dAtA[i] = 0x38
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.PhoneType))
	return i, nil
}

func (m *BindPhoneRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BindPhoneRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Summary != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSecurity(dAtA, i, uint64(m.Summary.Size()))
		n9, err := m.Summary.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	return i, nil
}

func (m *UnbindPhoneReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnbindPhoneReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	if m.AccessToken != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSecurity(dAtA, i, uint64(m.AccessToken.Size()))
		n10, err := m.AccessToken.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.SessionId)))
	i += copy(dAtA[i:], m.SessionId)
	dAtA[i] = 0x20
	i++
	if m.WithoutSummary {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Source))
	return i, nil
}

func (m *RebindPhoneReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RebindPhoneReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x18
	i++
	if m.IsBind {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x20
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Source))
	dAtA[i] = 0x28
	i++
	if m.WithSummary {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	if m.AccessToken != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintSecurity(dAtA, i, uint64(m.AccessToken.Size()))
		n11, err := m.AccessToken.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	dAtA[i] = 0x3a
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.SessionId)))
	i += copy(dAtA[i:], m.SessionId)
	dAtA[i] = 0x40
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.PhoneType))
	return i, nil
}

func (m *RebindPhoneResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RebindPhoneResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Summary != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSecurity(dAtA, i, uint64(m.Summary.Size()))
		n12, err := m.Summary.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	return i, nil
}

func (m *UnbindPhoneRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnbindPhoneRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Summary != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSecurity(dAtA, i, uint64(m.Summary.Size()))
		n13, err := m.Summary.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	return i, nil
}

func (m *UpdatePasswordReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdatePasswordReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.NewPasswd)))
	i += copy(dAtA[i:], m.NewPasswd)
	if m.AccessToken != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintSecurity(dAtA, i, uint64(m.AccessToken.Size()))
		n14, err := m.AccessToken.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	dAtA[i] = 0x22
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.SessionId)))
	i += copy(dAtA[i:], m.SessionId)
	dAtA[i] = 0x28
	i++
	if m.WithoutSummary {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x30
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Source))
	return i, nil
}

func (m *UpdatePasswordRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdatePasswordRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Summary != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSecurity(dAtA, i, uint64(m.Summary.Size()))
		n15, err := m.Summary.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	return i, nil
}

func (m *DetachThirdPartyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DetachThirdPartyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	if m.AccessToken != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSecurity(dAtA, i, uint64(m.AccessToken.Size()))
		n16, err := m.AccessToken.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.SessionId)))
	i += copy(dAtA[i:], m.SessionId)
	dAtA[i] = 0x20
	i++
	if m.WithoutSummary {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Source))
	return i, nil
}

func (m *DetachThirdPartyRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DetachThirdPartyRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Summary != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSecurity(dAtA, i, uint64(m.Summary.Size()))
		n17, err := m.Summary.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	return i, nil
}

func (m *GetOperationLogReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOperationLogReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.OpType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.OpSource))
	dAtA[i] = 0x20
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x28
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x30
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x38
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *LogInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LogInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.OpType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.OpSource))
	dAtA[i] = 0x22
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x28
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.OpTime))
	return i, nil
}

func (m *GetOperationLogRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOperationLogRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, msg := range m.InfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintSecurity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchGetLatestOperationReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetLatestOperationReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintSecurity(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.OpType))
	return i, nil
}

func (m *UserLatestOperation) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserLatestOperation) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	if len(m.InfoList) > 0 {
		for _, msg := range m.InfoList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintSecurity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchGetLatestOperationRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetLatestOperationRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.OpList) > 0 {
		for _, msg := range m.OpList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintSecurity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUnregApplyAuditStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUnregApplyAuditStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetUnregApplyAuditStatusRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUnregApplyAuditStatusRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *UpdateUnregApplyAuditStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUnregApplyAuditStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x18
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.ApplyId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.OperatorId)))
	i += copy(dAtA[i:], m.OperatorId)
	return i, nil
}

func (m *UpdateUnregApplyAuditStatusRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUnregApplyAuditStatusRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *ClearUnregApplyAuditStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClearUnregApplyAuditStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *ClearUnregApplyAuditStatusRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClearUnregApplyAuditStatusRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetUnregApplyAuditRecordReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUnregApplyAuditRecordReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x18
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x20
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.OffsetApplyId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Limit))
	dAtA[i] = 0x30
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x38
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x40
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.SortType))
	return i, nil
}

func (m *UnregApplyAuditInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnregApplyAuditInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.ApplyId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x20
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.ApplyAt))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.OperatorId)))
	i += copy(dAtA[i:], m.OperatorId)
	dAtA[i] = 0x30
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.OpAt))
	return i, nil
}

func (m *GetUnregApplyAuditRecordRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUnregApplyAuditRecordRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, msg := range m.InfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintSecurity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *GetUserLastOperationReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLastOperationReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.OpType))
	return i, nil
}

func (m *GetUserLastOperationRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLastOperationRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, msg := range m.InfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintSecurity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UpdateAutoProcUnregApplyStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateAutoProcUnregApplyStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x18
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.ApplyId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.Ttid)))
	i += copy(dAtA[i:], m.Ttid)
	dAtA[i] = 0x28
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.CancelTime))
	dAtA[i] = 0x30
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.MarketId))
	return i, nil
}

func (m *UpdateAutoProcUnregApplyStatusRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateAutoProcUnregApplyStatusRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetAutoProcUnregRecordReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAutoProcUnregRecordReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.Ttid)))
	i += copy(dAtA[i:], m.Ttid)
	dAtA[i] = 0x20
	i++
	if m.Sort {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x30
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *AutoProcUnregInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AutoProcUnregInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.Ttid)))
	i += copy(dAtA[i:], m.Ttid)
	dAtA[i] = 0x20
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x28
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.ApplyAt))
	dAtA[i] = 0x30
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.RemainWait))
	dAtA[i] = 0x38
	i++
	if m.Visit {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x42
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.UnregReason)))
	i += copy(dAtA[i:], m.UnregReason)
	dAtA[i] = 0x48
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.MarketId))
	return i, nil
}

func (m *GetAutoProcUnregRecordRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAutoProcUnregRecordRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, msg := range m.InfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintSecurity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *UpdateUnregVisitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUnregVisitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x10
	i++
	if m.Visit {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x1a
	i++
	i = encodeVarintSecurity(dAtA, i, uint64(len(m.UnregReason)))
	i += copy(dAtA[i:], m.UnregReason)
	return i, nil
}

func (m *UpdateUnregVisitRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUnregVisitRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Security(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Security(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintSecurity(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *SecurityQuestion) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Id))
	l = len(m.Question)
	n += 1 + l + sovSecurity(uint64(l))
	return n
}

func (m *SecurityQuestionAnswer) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Id))
	l = len(m.Answer)
	n += 1 + l + sovSecurity(uint64(l))
	return n
}

func (m *GetSummaryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	return n
}

func (m *SecuritySummary) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	n += 1 + sovSecurity(uint64(m.Level))
	l = len(m.Phone)
	n += 1 + l + sovSecurity(uint64(l))
	if len(m.Questions) > 0 {
		for _, e := range m.Questions {
			l = e.Size()
			n += 1 + l + sovSecurity(uint64(l))
		}
	}
	n += 1 + sovSecurity(uint64(m.ThirdPartyAttached))
	n += 2
	n += 1 + sovSecurity(uint64(m.PhoneType))
	return n
}

func (m *GetSummaryRsp) Size() (n int) {
	var l int
	_ = l
	if m.Summary != nil {
		l = m.Summary.Size()
		n += 1 + l + sovSecurity(uint64(l))
	}
	return n
}

func (m *TokenModel) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	n += 1 + sovSecurity(uint64(m.Expired))
	return n
}

func (m *AccessToken) Size() (n int) {
	var l int
	_ = l
	l = len(m.Token)
	n += 1 + l + sovSecurity(uint64(l))
	return n
}

func (m *GetAccessTokenReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	return n
}

func (m *GetAccessTokenRsp) Size() (n int) {
	var l int
	_ = l
	if m.AccessToken != nil {
		l = m.AccessToken.Size()
		n += 1 + l + sovSecurity(uint64(l))
	}
	return n
}

func (m *ValidateAccessTokenReq) Size() (n int) {
	var l int
	_ = l
	if m.AccessToken != nil {
		l = m.AccessToken.Size()
		n += 1 + l + sovSecurity(uint64(l))
	}
	return n
}

func (m *ValidateAccessTokenRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	return n
}

func (m *SessionModel) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	n += 1 + sovSecurity(uint64(m.Usage))
	n += 1 + sovSecurity(uint64(m.CreateTime))
	l = len(m.Salt)
	n += 1 + l + sovSecurity(uint64(l))
	l = len(m.ValidatedPhone)
	n += 1 + l + sovSecurity(uint64(l))
	return n
}

func (m *SessionInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	n += 1 + sovSecurity(uint64(m.Usage))
	l = len(m.ValidatedPhone)
	n += 1 + l + sovSecurity(uint64(l))
	return n
}

func (m *GetSessionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	if m.Session != nil {
		l = m.Session.Size()
		n += 1 + l + sovSecurity(uint64(l))
	}
	return n
}

func (m *GetSessionRsp) Size() (n int) {
	var l int
	_ = l
	l = len(m.SessionId)
	n += 1 + l + sovSecurity(uint64(l))
	return n
}

func (m *ValidateSessionReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.SessionId)
	n += 1 + l + sovSecurity(uint64(l))
	return n
}

func (m *ValidateSessionRsp) Size() (n int) {
	var l int
	_ = l
	if m.Session != nil {
		l = m.Session.Size()
		n += 1 + l + sovSecurity(uint64(l))
	}
	return n
}

func (m *GetPresetQuestionReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetPresetQuestionRsp) Size() (n int) {
	var l int
	_ = l
	if len(m.Questions) > 0 {
		for _, e := range m.Questions {
			l = e.Size()
			n += 1 + l + sovSecurity(uint64(l))
		}
	}
	return n
}

func (m *SetQuestionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	if len(m.Answers) > 0 {
		for _, e := range m.Answers {
			l = e.Size()
			n += 1 + l + sovSecurity(uint64(l))
		}
	}
	if m.AccessToken != nil {
		l = m.AccessToken.Size()
		n += 1 + l + sovSecurity(uint64(l))
	}
	l = len(m.SessionId)
	n += 1 + l + sovSecurity(uint64(l))
	n += 1 + sovSecurity(uint64(m.Source))
	return n
}

func (m *SetQuestionRsp) Size() (n int) {
	var l int
	_ = l
	if m.Summary != nil {
		l = m.Summary.Size()
		n += 1 + l + sovSecurity(uint64(l))
	}
	return n
}

func (m *VerifyQuestionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	if len(m.Answers) > 0 {
		for _, e := range m.Answers {
			l = e.Size()
			n += 1 + l + sovSecurity(uint64(l))
		}
	}
	return n
}

func (m *VerifyQuestionRsp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *BindPhoneReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	l = len(m.Phone)
	n += 1 + l + sovSecurity(uint64(l))
	if m.AccessToken != nil {
		l = m.AccessToken.Size()
		n += 1 + l + sovSecurity(uint64(l))
	}
	l = len(m.SessionId)
	n += 1 + l + sovSecurity(uint64(l))
	n += 2
	n += 1 + sovSecurity(uint64(m.Source))
	n += 1 + sovSecurity(uint64(m.PhoneType))
	return n
}

func (m *BindPhoneRsp) Size() (n int) {
	var l int
	_ = l
	if m.Summary != nil {
		l = m.Summary.Size()
		n += 1 + l + sovSecurity(uint64(l))
	}
	return n
}

func (m *UnbindPhoneReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	if m.AccessToken != nil {
		l = m.AccessToken.Size()
		n += 1 + l + sovSecurity(uint64(l))
	}
	l = len(m.SessionId)
	n += 1 + l + sovSecurity(uint64(l))
	n += 2
	n += 1 + sovSecurity(uint64(m.Source))
	return n
}

func (m *RebindPhoneReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	l = len(m.Phone)
	n += 1 + l + sovSecurity(uint64(l))
	n += 2
	n += 1 + sovSecurity(uint64(m.Source))
	n += 2
	if m.AccessToken != nil {
		l = m.AccessToken.Size()
		n += 1 + l + sovSecurity(uint64(l))
	}
	l = len(m.SessionId)
	n += 1 + l + sovSecurity(uint64(l))
	n += 1 + sovSecurity(uint64(m.PhoneType))
	return n
}

func (m *RebindPhoneResp) Size() (n int) {
	var l int
	_ = l
	if m.Summary != nil {
		l = m.Summary.Size()
		n += 1 + l + sovSecurity(uint64(l))
	}
	return n
}

func (m *UnbindPhoneRsp) Size() (n int) {
	var l int
	_ = l
	if m.Summary != nil {
		l = m.Summary.Size()
		n += 1 + l + sovSecurity(uint64(l))
	}
	return n
}

func (m *UpdatePasswordReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	l = len(m.NewPasswd)
	n += 1 + l + sovSecurity(uint64(l))
	if m.AccessToken != nil {
		l = m.AccessToken.Size()
		n += 1 + l + sovSecurity(uint64(l))
	}
	l = len(m.SessionId)
	n += 1 + l + sovSecurity(uint64(l))
	n += 2
	n += 1 + sovSecurity(uint64(m.Source))
	return n
}

func (m *UpdatePasswordRsp) Size() (n int) {
	var l int
	_ = l
	if m.Summary != nil {
		l = m.Summary.Size()
		n += 1 + l + sovSecurity(uint64(l))
	}
	return n
}

func (m *DetachThirdPartyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	if m.AccessToken != nil {
		l = m.AccessToken.Size()
		n += 1 + l + sovSecurity(uint64(l))
	}
	l = len(m.SessionId)
	n += 1 + l + sovSecurity(uint64(l))
	n += 2
	n += 1 + sovSecurity(uint64(m.Source))
	return n
}

func (m *DetachThirdPartyRsp) Size() (n int) {
	var l int
	_ = l
	if m.Summary != nil {
		l = m.Summary.Size()
		n += 1 + l + sovSecurity(uint64(l))
	}
	return n
}

func (m *GetOperationLogReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	n += 1 + sovSecurity(uint64(m.OpType))
	n += 1 + sovSecurity(uint64(m.OpSource))
	n += 1 + sovSecurity(uint64(m.BeginTime))
	n += 1 + sovSecurity(uint64(m.EndTime))
	n += 1 + sovSecurity(uint64(m.Offset))
	n += 1 + sovSecurity(uint64(m.Limit))
	return n
}

func (m *LogInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	n += 1 + sovSecurity(uint64(m.OpType))
	n += 1 + sovSecurity(uint64(m.OpSource))
	l = len(m.Content)
	n += 1 + l + sovSecurity(uint64(l))
	n += 1 + sovSecurity(uint64(m.OpTime))
	return n
}

func (m *GetOperationLogRsp) Size() (n int) {
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, e := range m.InfoList {
			l = e.Size()
			n += 1 + l + sovSecurity(uint64(l))
		}
	}
	return n
}

func (m *BatchGetLatestOperationReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovSecurity(uint64(e))
		}
	}
	n += 1 + sovSecurity(uint64(m.OpType))
	return n
}

func (m *UserLatestOperation) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	if len(m.InfoList) > 0 {
		for _, e := range m.InfoList {
			l = e.Size()
			n += 1 + l + sovSecurity(uint64(l))
		}
	}
	return n
}

func (m *BatchGetLatestOperationRsp) Size() (n int) {
	var l int
	_ = l
	if len(m.OpList) > 0 {
		for _, e := range m.OpList {
			l = e.Size()
			n += 1 + l + sovSecurity(uint64(l))
		}
	}
	return n
}

func (m *GetUnregApplyAuditStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	return n
}

func (m *GetUnregApplyAuditStatusRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Status))
	return n
}

func (m *UpdateUnregApplyAuditStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	n += 1 + sovSecurity(uint64(m.Status))
	n += 1 + sovSecurity(uint64(m.ApplyId))
	l = len(m.OperatorId)
	n += 1 + l + sovSecurity(uint64(l))
	return n
}

func (m *UpdateUnregApplyAuditStatusRsp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *ClearUnregApplyAuditStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	return n
}

func (m *ClearUnregApplyAuditStatusRsp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetUnregApplyAuditRecordReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	n += 1 + sovSecurity(uint64(m.Status))
	n += 1 + sovSecurity(uint64(m.Offset))
	n += 1 + sovSecurity(uint64(m.OffsetApplyId))
	n += 1 + sovSecurity(uint64(m.Limit))
	n += 1 + sovSecurity(uint64(m.BeginTime))
	n += 1 + sovSecurity(uint64(m.EndTime))
	n += 1 + sovSecurity(uint64(m.SortType))
	return n
}

func (m *UnregApplyAuditInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.ApplyId))
	n += 1 + sovSecurity(uint64(m.Uid))
	n += 1 + sovSecurity(uint64(m.Status))
	n += 1 + sovSecurity(uint64(m.ApplyAt))
	l = len(m.OperatorId)
	n += 1 + l + sovSecurity(uint64(l))
	n += 1 + sovSecurity(uint64(m.OpAt))
	return n
}

func (m *GetUnregApplyAuditRecordRsp) Size() (n int) {
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, e := range m.InfoList {
			l = e.Size()
			n += 1 + l + sovSecurity(uint64(l))
		}
	}
	n += 1 + sovSecurity(uint64(m.Total))
	return n
}

func (m *GetUserLastOperationReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	n += 1 + sovSecurity(uint64(m.OpType))
	return n
}

func (m *GetUserLastOperationRsp) Size() (n int) {
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, e := range m.InfoList {
			l = e.Size()
			n += 1 + l + sovSecurity(uint64(l))
		}
	}
	return n
}

func (m *UpdateAutoProcUnregApplyStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Uid))
	n += 1 + sovSecurity(uint64(m.Status))
	n += 1 + sovSecurity(uint64(m.ApplyId))
	l = len(m.Ttid)
	n += 1 + l + sovSecurity(uint64(l))
	n += 1 + sovSecurity(uint64(m.CancelTime))
	n += 1 + sovSecurity(uint64(m.MarketId))
	return n
}

func (m *UpdateAutoProcUnregApplyStatusRsp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetAutoProcUnregRecordReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Status))
	n += 1 + sovSecurity(uint64(m.Uid))
	l = len(m.Ttid)
	n += 1 + l + sovSecurity(uint64(l))
	n += 2
	n += 1 + sovSecurity(uint64(m.Offset))
	n += 1 + sovSecurity(uint64(m.Limit))
	return n
}

func (m *AutoProcUnregInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Id))
	n += 1 + sovSecurity(uint64(m.Uid))
	l = len(m.Ttid)
	n += 1 + l + sovSecurity(uint64(l))
	n += 1 + sovSecurity(uint64(m.Status))
	n += 1 + sovSecurity(uint64(m.ApplyAt))
	n += 1 + sovSecurity(uint64(m.RemainWait))
	n += 2
	l = len(m.UnregReason)
	n += 1 + l + sovSecurity(uint64(l))
	n += 1 + sovSecurity(uint64(m.MarketId))
	return n
}

func (m *GetAutoProcUnregRecordRsp) Size() (n int) {
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, e := range m.InfoList {
			l = e.Size()
			n += 1 + l + sovSecurity(uint64(l))
		}
	}
	n += 1 + sovSecurity(uint64(m.Total))
	return n
}

func (m *UpdateUnregVisitReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSecurity(uint64(m.Id))
	n += 2
	l = len(m.UnregReason)
	n += 1 + l + sovSecurity(uint64(l))
	return n
}

func (m *UpdateUnregVisitRsp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovSecurity(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozSecurity(x uint64) (n int) {
	return sovSecurity(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *SecurityQuestion) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SecurityQuestion: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SecurityQuestion: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Question", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Question = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("question")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SecurityQuestionAnswer) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SecurityQuestionAnswer: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SecurityQuestionAnswer: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Answer", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Answer = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSummaryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSummaryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSummaryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SecuritySummary) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SecuritySummary: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SecuritySummary: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Questions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Questions = append(m.Questions, &SecurityQuestion{})
			if err := m.Questions[len(m.Questions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ThirdPartyAttached", wireType)
			}
			m.ThirdPartyAttached = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ThirdPartyAttached |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PasswordSet", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.PasswordSet = bool(v != 0)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhoneType", wireType)
			}
			m.PhoneType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PhoneType |= (PhoneType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSummaryRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSummaryRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSummaryRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Summary", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Summary == nil {
				m.Summary = &SecuritySummary{}
			}
			if err := m.Summary.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("summary")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TokenModel) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TokenModel: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TokenModel: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Expired", wireType)
			}
			m.Expired = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Expired |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("expired")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AccessToken) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AccessToken: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AccessToken: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Token", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Token = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("token")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAccessTokenReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAccessTokenReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAccessTokenReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAccessTokenRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAccessTokenRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAccessTokenRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccessToken", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.AccessToken == nil {
				m.AccessToken = &AccessToken{}
			}
			if err := m.AccessToken.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("access_token")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ValidateAccessTokenReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ValidateAccessTokenReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ValidateAccessTokenReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccessToken", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.AccessToken == nil {
				m.AccessToken = &AccessToken{}
			}
			if err := m.AccessToken.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("access_token")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ValidateAccessTokenRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ValidateAccessTokenRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ValidateAccessTokenRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SessionModel) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SessionModel: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SessionModel: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Usage", wireType)
			}
			m.Usage = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Usage |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Salt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Salt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValidatedPhone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ValidatedPhone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("usage")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("create_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("salt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SessionInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SessionInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SessionInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Usage", wireType)
			}
			m.Usage = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Usage |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValidatedPhone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ValidatedPhone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("usage")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSessionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSessionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSessionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Session", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Session == nil {
				m.Session = &SessionInfo{}
			}
			if err := m.Session.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("session")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSessionRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSessionRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSessionRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SessionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SessionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("session_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ValidateSessionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ValidateSessionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ValidateSessionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SessionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SessionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("session_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ValidateSessionRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ValidateSessionRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ValidateSessionRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Session", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Session == nil {
				m.Session = &SessionInfo{}
			}
			if err := m.Session.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresetQuestionReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresetQuestionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresetQuestionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresetQuestionRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresetQuestionRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresetQuestionRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Questions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Questions = append(m.Questions, &SecurityQuestion{})
			if err := m.Questions[len(m.Questions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetQuestionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetQuestionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetQuestionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Answers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Answers = append(m.Answers, &SecurityQuestionAnswer{})
			if err := m.Answers[len(m.Answers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccessToken", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.AccessToken == nil {
				m.AccessToken = &AccessToken{}
			}
			if err := m.AccessToken.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SessionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SessionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			m.Source = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Source |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetQuestionRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetQuestionRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetQuestionRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Summary", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Summary == nil {
				m.Summary = &SecuritySummary{}
			}
			if err := m.Summary.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyQuestionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyQuestionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyQuestionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Answers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Answers = append(m.Answers, &SecurityQuestionAnswer{})
			if err := m.Answers[len(m.Answers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyQuestionRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyQuestionRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyQuestionRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BindPhoneReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BindPhoneReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BindPhoneReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccessToken", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.AccessToken == nil {
				m.AccessToken = &AccessToken{}
			}
			if err := m.AccessToken.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SessionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SessionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WithoutSummary", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithoutSummary = bool(v != 0)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			m.Source = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Source |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhoneType", wireType)
			}
			m.PhoneType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PhoneType |= (PhoneType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("phone")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BindPhoneRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BindPhoneRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BindPhoneRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Summary", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Summary == nil {
				m.Summary = &SecuritySummary{}
			}
			if err := m.Summary.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnbindPhoneReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UnbindPhoneReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UnbindPhoneReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccessToken", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.AccessToken == nil {
				m.AccessToken = &AccessToken{}
			}
			if err := m.AccessToken.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SessionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SessionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WithoutSummary", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithoutSummary = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			m.Source = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Source |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RebindPhoneReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RebindPhoneReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RebindPhoneReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsBind", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsBind = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			m.Source = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Source |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WithSummary", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithSummary = bool(v != 0)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccessToken", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.AccessToken == nil {
				m.AccessToken = &AccessToken{}
			}
			if err := m.AccessToken.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SessionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SessionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhoneType", wireType)
			}
			m.PhoneType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PhoneType |= (PhoneType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("phone")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_bind")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RebindPhoneResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RebindPhoneResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RebindPhoneResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Summary", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Summary == nil {
				m.Summary = &SecuritySummary{}
			}
			if err := m.Summary.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnbindPhoneRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UnbindPhoneRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UnbindPhoneRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Summary", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Summary == nil {
				m.Summary = &SecuritySummary{}
			}
			if err := m.Summary.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdatePasswordReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdatePasswordReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdatePasswordReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewPasswd", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NewPasswd = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccessToken", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.AccessToken == nil {
				m.AccessToken = &AccessToken{}
			}
			if err := m.AccessToken.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SessionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SessionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WithoutSummary", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithoutSummary = bool(v != 0)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			m.Source = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Source |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("new_passwd")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdatePasswordRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdatePasswordRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdatePasswordRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Summary", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Summary == nil {
				m.Summary = &SecuritySummary{}
			}
			if err := m.Summary.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DetachThirdPartyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DetachThirdPartyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DetachThirdPartyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccessToken", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.AccessToken == nil {
				m.AccessToken = &AccessToken{}
			}
			if err := m.AccessToken.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SessionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SessionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WithoutSummary", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithoutSummary = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			m.Source = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Source |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DetachThirdPartyRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DetachThirdPartyRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DetachThirdPartyRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Summary", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Summary == nil {
				m.Summary = &SecuritySummary{}
			}
			if err := m.Summary.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOperationLogReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOperationLogReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOperationLogReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpType", wireType)
			}
			m.OpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpSource", wireType)
			}
			m.OpSource = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpSource |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LogInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LogInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LogInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpType", wireType)
			}
			m.OpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpSource", wireType)
			}
			m.OpSource = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpSource |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpTime", wireType)
			}
			m.OpTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_source")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOperationLogRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOperationLogRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOperationLogRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.InfoList = append(m.InfoList, &LogInfo{})
			if err := m.InfoList[len(m.InfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetLatestOperationReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetLatestOperationReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetLatestOperationReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSecurity
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSecurity
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthSecurity
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowSecurity
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpType", wireType)
			}
			m.OpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserLatestOperation) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserLatestOperation: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserLatestOperation: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.InfoList = append(m.InfoList, &LogInfo{})
			if err := m.InfoList[len(m.InfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetLatestOperationRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetLatestOperationRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetLatestOperationRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpList = append(m.OpList, &UserLatestOperation{})
			if err := m.OpList[len(m.OpList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUnregApplyAuditStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUnregApplyAuditStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUnregApplyAuditStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUnregApplyAuditStatusRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUnregApplyAuditStatusRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUnregApplyAuditStatusRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUnregApplyAuditStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUnregApplyAuditStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUnregApplyAuditStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyId", wireType)
			}
			m.ApplyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperatorId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OperatorId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUnregApplyAuditStatusRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUnregApplyAuditStatusRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUnregApplyAuditStatusRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClearUnregApplyAuditStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ClearUnregApplyAuditStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ClearUnregApplyAuditStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClearUnregApplyAuditStatusRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ClearUnregApplyAuditStatusRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ClearUnregApplyAuditStatusRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUnregApplyAuditRecordReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUnregApplyAuditRecordReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUnregApplyAuditRecordReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OffsetApplyId", wireType)
			}
			m.OffsetApplyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OffsetApplyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SortType", wireType)
			}
			m.SortType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SortType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnregApplyAuditInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UnregApplyAuditInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UnregApplyAuditInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyId", wireType)
			}
			m.ApplyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyAt", wireType)
			}
			m.ApplyAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperatorId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OperatorId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpAt", wireType)
			}
			m.OpAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("apply_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("apply_at")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUnregApplyAuditRecordRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUnregApplyAuditRecordRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUnregApplyAuditRecordRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.InfoList = append(m.InfoList, &UnregApplyAuditInfo{})
			if err := m.InfoList[len(m.InfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLastOperationReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLastOperationReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLastOperationReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpType", wireType)
			}
			m.OpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLastOperationRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLastOperationRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLastOperationRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.InfoList = append(m.InfoList, &LogInfo{})
			if err := m.InfoList[len(m.InfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateAutoProcUnregApplyStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateAutoProcUnregApplyStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateAutoProcUnregApplyStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyId", wireType)
			}
			m.ApplyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ttid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ttid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CancelTime", wireType)
			}
			m.CancelTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CancelTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateAutoProcUnregApplyStatusRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateAutoProcUnregApplyStatusRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateAutoProcUnregApplyStatusRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAutoProcUnregRecordReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAutoProcUnregRecordReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAutoProcUnregRecordReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ttid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ttid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sort", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Sort = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AutoProcUnregInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AutoProcUnregInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AutoProcUnregInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ttid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ttid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyAt", wireType)
			}
			m.ApplyAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RemainWait", wireType)
			}
			m.RemainWait = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainWait |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Visit", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Visit = bool(v != 0)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UnregReason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UnregReason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ttid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("apply_at")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("remain_wait")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAutoProcUnregRecordRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAutoProcUnregRecordRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAutoProcUnregRecordRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.InfoList = append(m.InfoList, &AutoProcUnregInfo{})
			if err := m.InfoList[len(m.InfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUnregVisitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUnregVisitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUnregVisitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Visit", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Visit = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UnregReason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSecurity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UnregReason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("visit")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("unreg_reason")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUnregVisitRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUnregVisitRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUnregVisitRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSecurity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSecurity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipSecurity(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowSecurity
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSecurity
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthSecurity
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowSecurity
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipSecurity(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthSecurity = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowSecurity   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/security/security.proto", fileDescriptorSecurity) }

var fileDescriptorSecurity = []byte{
	// 3116 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x5a, 0xc1, 0x6f, 0x1b, 0xc7,
	0xd5, 0xf7, 0x92, 0x94, 0x44, 0x3d, 0x5a, 0x12, 0x35, 0xb2, 0x65, 0x7a, 0x65, 0x2b, 0xeb, 0xb5,
	0x03, 0x3b, 0x72, 0x68, 0xcb, 0x4a, 0xf2, 0x7d, 0xee, 0x96, 0x10, 0x4a, 0x4b, 0x8c, 0xc4, 0x54,
	0x16, 0x69, 0x92, 0xb2, 0xeb, 0x06, 0xee, 0x76, 0x45, 0x8e, 0xed, 0xad, 0x29, 0xee, 0x88, 0xb3,
	0xb4, 0xa2, 0x00, 0x01, 0x02, 0xa4, 0x40, 0xdb, 0x1c, 0x8a, 0xa2, 0x40, 0xd0, 0x9e, 0x7a, 0x28,
	0x7c, 0x68, 0xae, 0x2d, 0x0a, 0xf4, 0xd2, 0xf6, 0xd0, 0x1e, 0x72, 0x6b, 0x0f, 0x45, 0x81, 0x02,
	0x45, 0xd0, 0xa6, 0x87, 0xfa, 0xd4, 0xbf, 0xa0, 0x87, 0x62, 0x66, 0x77, 0xb9, 0xb3, 0xcb, 0x5d,
	0x8a, 0x56, 0x90, 0x43, 0x0b, 0xf8, 0x20, 0xbe, 0xf7, 0x76, 0xde, 0xef, 0xfd, 0xe6, 0xcd, 0x9b,
	0x99, 0x37, 0x86, 0x05, 0xda, 0x6d, 0x5e, 0xa7, 0xb8, 0xd9, 0xeb, 0x9a, 0xf6, 0x61, 0xff, 0x8f,
	0x6b, 0xa4, 0x6b, 0xd9, 0x16, 0x4a, 0x7b, 0xbf, 0xe5, 0x4b, 0x4d, 0x6b, 0x6f, 0xcf, 0xea, 0x5c,
	0xb7, 0xdb, 0x4f, 0x89, 0xd9, 0x7c, 0xd2, 0xc6, 0xd7, 0xe9, 0x93, 0xdd, 0x9e, 0xd9, 0xb6, 0xcd,
	0x8e, 0x7d, 0x48, 0xb0, 0x63, 0xaf, 0xbe, 0x05, 0xd9, 0xba, 0xfb, 0xc5, 0x9d, 0x1e, 0xa6, 0xb6,
	0x69, 0x75, 0xd0, 0x29, 0x48, 0x98, 0xad, 0x9c, 0xa4, 0x24, 0xae, 0x4c, 0xdd, 0x4a, 0x7d, 0xf2,
	0xe9, 0x4b, 0x27, 0x6a, 0x09, 0xb3, 0x85, 0x14, 0x48, 0xef, 0xbb, 0x16, 0xb9, 0x84, 0x92, 0xb8,
	0x32, 0xe9, 0xea, 0xfa, 0x52, 0x75, 0x0b, 0xe6, 0xc3, 0x63, 0x15, 0x3b, 0xf4, 0x00, 0x77, 0x63,
	0x46, 0x3c, 0x07, 0xe3, 0x06, 0xd7, 0xe7, 0x12, 0x8a, 0xd4, 0x1f, 0xcf, 0x95, 0xa9, 0x97, 0x61,
	0x6a, 0x03, 0xdb, 0xf5, 0xde, 0xde, 0x9e, 0xd1, 0x3d, 0xac, 0xe1, 0x7d, 0x34, 0x0f, 0xc9, 0x5e,
	0x68, 0x14, 0x26, 0x50, 0x7f, 0x99, 0x80, 0x19, 0xcf, 0xaf, 0x6b, 0x1e, 0x67, 0x8b, 0x64, 0x18,
	0x6b, 0xe3, 0xa7, 0xb8, 0xcd, 0x23, 0xf0, 0x34, 0x8e, 0x88, 0xe9, 0xc8, 0x63, 0xab, 0x83, 0x73,
	0x49, 0x01, 0x8d, 0x23, 0x42, 0x37, 0x61, 0xd2, 0x0b, 0x93, 0xe6, 0x52, 0x4a, 0xf2, 0x4a, 0x66,
	0x45, 0xbe, 0xd6, 0xa7, 0x3e, 0x1c, 0x75, 0xcd, 0x37, 0x46, 0x6f, 0xc0, 0x9c, 0xfd, 0xd8, 0xec,
	0xb6, 0xaa, 0x46, 0xd7, 0x3e, 0xd4, 0x0d, 0xdb, 0x36, 0x9a, 0x8f, 0x71, 0x2b, 0x37, 0xa6, 0x48,
	0x7d, 0xff, 0xc8, 0x37, 0x28, 0xba, 0x7a, 0x74, 0x19, 0x4e, 0x12, 0x83, 0xd2, 0x03, 0xab, 0xdb,
	0xd2, 0x29, 0xb6, 0x73, 0xe3, 0x8a, 0x74, 0x25, 0xed, 0xda, 0x67, 0x3c, 0x4d, 0x1d, 0xdb, 0xe8,
	0x26, 0x00, 0x87, 0xa8, 0xb3, 0x49, 0xcd, 0x4d, 0x28, 0xd2, 0x95, 0xe9, 0x95, 0x39, 0x1f, 0x5a,
	0x95, 0xe9, 0x1a, 0x87, 0x04, 0xbb, 0xdf, 0x4e, 0x12, 0x4f, 0xa0, 0xae, 0x07, 0x08, 0xa6, 0x04,
	0xbd, 0x06, 0x13, 0xd4, 0xf9, 0xc5, 0x89, 0xcb, 0xac, 0x9c, 0x1d, 0x0c, 0xd1, 0x33, 0xf7, 0x2c,
	0xd5, 0x75, 0x80, 0x86, 0xf5, 0x04, 0x77, 0x6e, 0x5b, 0x2d, 0xdc, 0x8e, 0xe5, 0x7d, 0x11, 0x26,
	0xf0, 0x3b, 0xc4, 0xec, 0xe2, 0x56, 0x80, 0x79, 0x4f, 0xa8, 0xbe, 0x02, 0x99, 0x62, 0xb3, 0x89,
	0x29, 0xe5, 0x63, 0xb1, 0xa9, 0xb0, 0xd9, 0x1f, 0x7c, 0xa0, 0xfe, 0x54, 0x70, 0x91, 0x7a, 0x15,
	0x66, 0x37, 0xb0, 0x2d, 0x58, 0x0f, 0xcb, 0x8d, 0xdb, 0x03, 0xc6, 0x94, 0xa0, 0x9b, 0x70, 0xd2,
	0xe0, 0x12, 0xdd, 0x77, 0x92, 0x59, 0x39, 0xed, 0x07, 0x2b, 0xda, 0x67, 0x0c, 0xff, 0x87, 0x5a,
	0x83, 0xf9, 0xbb, 0x46, 0xdb, 0x6c, 0x19, 0x36, 0x0e, 0x01, 0x38, 0xfe, 0x98, 0xcb, 0xd1, 0x63,
	0x52, 0xe2, 0x07, 0x25, 0x05, 0x83, 0xfa, 0xb9, 0x04, 0x27, 0xeb, 0x98, 0x52, 0xd3, 0x3a, 0x82,
	0x75, 0x19, 0xc6, 0x7a, 0xd4, 0x78, 0x84, 0x83, 0xd9, 0xce, 0x45, 0xe8, 0x65, 0xc8, 0x34, 0xbb,
	0xd8, 0xb0, 0xb1, 0x6e, 0x9b, 0x7b, 0x2c, 0xe7, 0x7d, 0x0b, 0x70, 0x14, 0x0d, 0x73, 0x0f, 0xa3,
	0x1c, 0xa4, 0xa8, 0xd1, 0xb6, 0x73, 0x29, 0x61, 0x22, 0xb8, 0x04, 0xe5, 0x61, 0xe6, 0xa9, 0x8b,
	0xbb, 0xa5, 0x3b, 0x0b, 0x67, 0x4c, 0x58, 0x38, 0xd3, 0x7d, 0x25, 0x4f, 0x41, 0x95, 0x40, 0xc6,
	0xc5, 0x5c, 0xee, 0x3c, 0xb4, 0x8e, 0x05, 0x39, 0xc2, 0x63, 0x72, 0x88, 0xc7, 0xaf, 0x39, 0xf9,
	0xed, 0x38, 0x1d, 0x92, 0x24, 0xe8, 0x3a, 0x4c, 0x50, 0xc7, 0x8a, 0x7b, 0x0d, 0x4c, 0x9b, 0x80,
	0xb9, 0xe6, 0x59, 0xa9, 0xaf, 0x07, 0x46, 0xa6, 0x04, 0x5d, 0x04, 0x70, 0x75, 0xba, 0xeb, 0xc0,
	0x03, 0x35, 0xe9, 0xca, 0xcb, 0x2d, 0xf5, 0x4b, 0x80, 0xbc, 0x89, 0x16, 0x40, 0x8d, 0xf4, 0x69,
	0x69, 0xf0, 0x53, 0x4a, 0x44, 0xdc, 0x2c, 0x47, 0x8e, 0xc6, 0x3d, 0x0f, 0xa7, 0x36, 0xb0, 0x5d,
	0xed, 0x62, 0x8a, 0xed, 0x7e, 0xad, 0xc2, 0xfb, 0x6a, 0x35, 0x4a, 0xce, 0x17, 0x8a, 0x50, 0xf5,
	0xa4, 0x17, 0xa8, 0x7a, 0xea, 0xdf, 0x25, 0x98, 0xae, 0x07, 0x9c, 0xc4, 0xb2, 0xaf, 0xc1, 0x84,
	0x53, 0xf1, 0x69, 0x2e, 0xc1, 0x5d, 0x28, 0xf1, 0x2e, 0x9c, 0xed, 0xa4, 0xe6, 0x7d, 0x30, 0xb0,
	0xea, 0x92, 0x61, 0x1a, 0xe2, 0x56, 0x5d, 0x88, 0xf6, 0x94, 0x90, 0x46, 0x3e, 0xed, 0x6c, 0x83,
	0xa2, 0x56, 0xaf, 0xdb, 0x74, 0x32, 0x7b, 0xcc, 0xdb, 0xa0, 0x1c, 0x99, 0x5a, 0x0a, 0x86, 0x18,
	0x2e, 0xa0, 0xd2, 0x88, 0x05, 0xf4, 0x11, 0xcc, 0xde, 0xc5, 0x5d, 0xf3, 0xe1, 0xe1, 0x17, 0x4c,
	0x96, 0x3a, 0x37, 0xe0, 0x88, 0x12, 0xf5, 0x67, 0x09, 0x38, 0x79, 0xcb, 0xec, 0x38, 0x4b, 0x66,
	0x98, 0xe7, 0xfe, 0xee, 0x28, 0xee, 0xfd, 0xfd, 0xdd, 0xf1, 0x0b, 0x9d, 0x86, 0x3c, 0xcc, 0x1c,
	0x98, 0xf6, 0x63, 0xab, 0x67, 0xeb, 0x1e, 0xbd, 0x63, 0xc2, 0x76, 0x38, 0xed, 0x2a, 0xbd, 0xbd,
	0xdf, 0x9f, 0xb5, 0xf1, 0xc1, 0x59, 0xfb, 0x1c, 0xfb, 0xe5, 0x9a, 0xc8, 0xd4, 0x71, 0x67, 0xfb,
	0x4f, 0x12, 0x4c, 0xef, 0x74, 0x76, 0x47, 0x61, 0x3c, 0xcc, 0x6a, 0xe2, 0x98, 0xac, 0x26, 0x47,
	0x66, 0x35, 0x35, 0x12, 0xab, 0x51, 0x6b, 0xe1, 0x77, 0x09, 0x98, 0xae, 0xe1, 0xdd, 0xcf, 0x9b,
	0x48, 0xe7, 0x61, 0xc2, 0xa4, 0x3a, 0x1b, 0x86, 0x6f, 0x48, 0x1e, 0x96, 0x71, 0x93, 0x32, 0xe6,
	0x05, 0x0c, 0xa9, 0x88, 0x99, 0xbd, 0x0c, 0x27, 0x19, 0xe6, 0xc8, 0x1c, 0xc9, 0x30, 0x8d, 0x17,
	0x4a, 0x98, 0xd8, 0xf1, 0x63, 0x12, 0x3b, 0x11, 0x4d, 0x6c, 0x30, 0xc3, 0xd2, 0x2f, 0x90, 0x61,
	0x6f, 0xc2, 0x4c, 0x80, 0xc4, 0xe3, 0x26, 0x59, 0x29, 0x98, 0x63, 0xc7, 0x1d, 0xe6, 0xdf, 0x12,
	0xcc, 0xee, 0x10, 0xb6, 0xe9, 0x54, 0xdd, 0x03, 0xe7, 0xb0, 0x79, 0xbd, 0x08, 0xd0, 0xc1, 0x07,
	0x3a, 0x3f, 0x9b, 0xb6, 0x02, 0x93, 0x3b, 0xd9, 0xc1, 0x07, 0x7c, 0x84, 0xd6, 0x7f, 0x53, 0xa5,
	0x50, 0x37, 0x07, 0xa2, 0x3f, 0x2e, 0x91, 0x7f, 0x91, 0x60, 0x6e, 0x1d, 0xb3, 0x93, 0x7d, 0xa3,
	0x7f, 0xd2, 0xff, 0x5f, 0x59, 0xf9, 0x6f, 0x45, 0x84, 0x76, 0x5c, 0x9e, 0xfe, 0x25, 0x01, 0xda,
	0xc0, 0x76, 0x85, 0xe0, 0xae, 0xc1, 0x36, 0xa8, 0x2d, 0xeb, 0x51, 0x80, 0xa6, 0xe0, 0x39, 0x98,
	0x55, 0x0b, 0x8b, 0x38, 0xab, 0x2c, 0x21, 0xe8, 0xc6, 0x2d, 0xc2, 0x56, 0x13, 0xba, 0x00, 0x93,
	0x16, 0xd1, 0x5d, 0xe8, 0x49, 0x01, 0x7a, 0xda, 0x22, 0x75, 0xa7, 0x64, 0x5c, 0x04, 0xd8, 0xc5,
	0x8f, 0xcc, 0x8e, 0x73, 0x06, 0x4e, 0x09, 0x83, 0x4c, 0x72, 0x39, 0x3f, 0x02, 0xbf, 0x04, 0x69,
	0xdc, 0x69, 0x39, 0x26, 0xe2, 0xb5, 0x6d, 0x02, 0x77, 0x5a, 0xdc, 0xe0, 0x1c, 0x8c, 0x5b, 0x0f,
	0x1f, 0x7a, 0xb7, 0x34, 0x1f, 0x06, 0x97, 0xf1, 0x2b, 0xa7, 0xb9, 0x67, 0xda, 0xbc, 0x5c, 0xf8,
	0x57, 0x4e, 0x26, 0x52, 0x7f, 0x2a, 0xc1, 0xc4, 0x96, 0xf5, 0x68, 0xe8, 0x89, 0x38, 0x10, 0x65,
	0xe2, 0xa8, 0x28, 0x13, 0x11, 0x51, 0x2e, 0xc2, 0x44, 0xd3, 0xea, 0xd8, 0xb8, 0x13, 0x3c, 0xc6,
	0x7b, 0x42, 0xcf, 0x83, 0x13, 0x5f, 0xd0, 0x83, 0xb9, 0xc7, 0xee, 0x89, 0x03, 0x93, 0x42, 0x09,
	0xba, 0x06, 0x93, 0x66, 0xe7, 0xa1, 0xa5, 0xb7, 0x4d, 0x6a, 0xbb, 0x67, 0xc3, 0x59, 0x7f, 0x8a,
	0xdd, 0xa0, 0x6a, 0x69, 0x66, 0xb3, 0x65, 0x52, 0x5b, 0xbd, 0x0b, 0xf2, 0x2d, 0xc3, 0x6e, 0x3e,
	0xde, 0xc0, 0xf6, 0x96, 0x61, 0x63, 0xea, 0x0f, 0xc8, 0xa6, 0xf8, 0x2c, 0xa4, 0x7b, 0x66, 0xcb,
	0x1f, 0x6c, 0xaa, 0x36, 0xd1, 0x33, 0x5b, 0xec, 0xc3, 0x23, 0x66, 0x59, 0x7d, 0x00, 0x73, 0x3b,
	0x14, 0x77, 0x43, 0x63, 0xc6, 0xb2, 0x19, 0x80, 0x9d, 0x38, 0x1a, 0x76, 0x23, 0x1e, 0x36, 0x25,
	0xe8, 0xff, 0x38, 0x36, 0x81, 0x82, 0xf3, 0xfe, 0x58, 0x11, 0xa8, 0x18, 0x68, 0x3e, 0xea, 0x1b,
	0xb0, 0xb0, 0x81, 0xed, 0x9d, 0x4e, 0x17, 0x3f, 0x2a, 0x12, 0xd2, 0x3e, 0x2c, 0xf6, 0x5a, 0xa6,
	0x5d, 0xb7, 0x0d, 0xbb, 0x47, 0x87, 0xdd, 0x66, 0xbf, 0x3c, 0xe4, 0x33, 0x4a, 0xf8, 0x42, 0xe5,
	0x3f, 0x02, 0x4b, 0xc5, 0x95, 0xa9, 0x3f, 0x91, 0x60, 0xd1, 0xa9, 0x67, 0x2f, 0xea, 0x57, 0x18,
	0x38, 0x90, 0x81, 0x8e, 0x8c, 0xad, 0x0f, 0x83, 0x0d, 0xe6, 0x55, 0x9c, 0xfe, 0xfa, 0xe0, 0xd2,
	0x72, 0x8b, 0x5d, 0x35, 0x2d, 0x4e, 0x81, 0xd5, 0x0d, 0xd7, 0x6e, 0xf0, 0x14, 0xe5, 0x96, 0xaa,
	0x0c, 0xc7, 0x47, 0x89, 0xfa, 0xff, 0x70, 0x7e, 0xad, 0x8d, 0x8d, 0xee, 0x0b, 0x13, 0xf7, 0xd2,
	0xd0, 0x0f, 0x29, 0x51, 0x9f, 0x25, 0xa2, 0xa8, 0xad, 0xe1, 0x66, 0x78, 0xd3, 0x93, 0xe2, 0x99,
	0x19, 0xa0, 0x5c, 0x28, 0x0c, 0xc9, 0x88, 0xc2, 0xf0, 0x2a, 0xcc, 0x38, 0x7f, 0xe9, 0x7d, 0xfa,
	0xc4, 0x0a, 0x34, 0xe5, 0x28, 0x8b, 0x2e, 0x89, 0xfd, 0x32, 0x32, 0x36, 0x50, 0x46, 0x42, 0x65,
	0x6c, 0xfc, 0xe8, 0x32, 0x36, 0x11, 0x55, 0xc6, 0x2e, 0xc0, 0x24, 0xb5, 0xba, 0xb6, 0x7f, 0x6c,
	0xf1, 0x2c, 0xd2, 0x4c, 0xcc, 0x17, 0xdb, 0x1f, 0x24, 0x98, 0x0b, 0x71, 0xc4, 0x6b, 0x97, 0x98,
	0x02, 0x22, 0xf9, 0xfd, 0x14, 0x70, 0xf9, 0x4b, 0xc4, 0x67, 0x56, 0x72, 0x58, 0x66, 0x19, 0x4e,
	0xe5, 0x0a, 0x0e, 0x5b, 0xb4, 0xc3, 0x99, 0x35, 0x16, 0x9d, 0x59, 0xe8, 0x2c, 0x8c, 0x59, 0x84,
	0x0d, 0x22, 0x52, 0x93, 0xb2, 0x48, 0xd1, 0x56, 0x7b, 0x43, 0xe6, 0x9d, 0x12, 0xa4, 0x0d, 0x56,
	0x39, 0x71, 0x89, 0x0f, 0x52, 0xe1, 0x97, 0x0e, 0xa7, 0x89, 0x65, 0x1b, 0xa1, 0x5e, 0x23, 0x17,
	0xa9, 0x55, 0x38, 0xc3, 0xdc, 0xf2, 0x12, 0x11, 0x2a, 0x85, 0x23, 0xed, 0x03, 0x83, 0x75, 0xb0,
	0x1c, 0x33, 0xe2, 0x31, 0x4a, 0xf5, 0xa7, 0x12, 0x5c, 0x70, 0x56, 0x62, 0xb1, 0x67, 0x5b, 0xd5,
	0xae, 0xd5, 0xf4, 0x03, 0xfd, 0xc2, 0x8b, 0x45, 0x0e, 0x52, 0xb6, 0x1d, 0xaa, 0x12, 0x5c, 0xc2,
	0x3b, 0x56, 0x46, 0xa7, 0x89, 0xdb, 0x83, 0x5b, 0x31, 0x38, 0x0a, 0x2f, 0x8d, 0xf7, 0x8c, 0xee,
	0x13, 0x6c, 0x33, 0x17, 0xe2, 0x84, 0xa7, 0x1d, 0x71, 0xb9, 0xa5, 0x5e, 0x3c, 0x32, 0x3e, 0x4a,
	0xd4, 0xdf, 0x4a, 0x70, 0x76, 0x03, 0xdb, 0x01, 0x13, 0xbf, 0x20, 0x88, 0xb5, 0x76, 0x30, 0xca,
	0x7e, 0xba, 0x87, 0xca, 0x85, 0x17, 0x5c, 0x72, 0x20, 0xb8, 0x1c, 0xa4, 0xd8, 0x2a, 0x0b, 0x1c,
	0xc4, 0xb8, 0x44, 0x28, 0x22, 0x63, 0xc3, 0x4e, 0x17, 0xe3, 0x83, 0xa7, 0x8b, 0x8f, 0x13, 0x30,
	0x1b, 0x80, 0xcf, 0xd7, 0x6a, 0x74, 0x2f, 0x3e, 0x6e, 0x81, 0xfa, 0x88, 0x13, 0x21, 0xc4, 0x3e,
	0x03, 0xa9, 0x23, 0x96, 0xee, 0x58, 0xcc, 0xd2, 0xed, 0xe2, 0x3d, 0xc3, 0xec, 0xe8, 0x07, 0x06,
	0x87, 0x2f, 0xf4, 0x1f, 0x1d, 0xc5, 0x3d, 0xc3, 0xe4, 0xf1, 0x3d, 0x35, 0xa9, 0x7b, 0x7a, 0xf2,
	0x88, 0x71, 0x44, 0xec, 0xc2, 0xd7, 0x63, 0x61, 0xe9, 0x5d, 0x6c, 0x50, 0xab, 0xc3, 0x6b, 0x96,
	0x87, 0x31, 0xd3, 0x73, 0xe6, 0x8b, 0x29, 0x82, 0x29, 0x31, 0x19, 0x99, 0x12, 0xfb, 0xb1, 0x93,
	0xed, 0xf4, 0xc1, 0xc2, 0x0b, 0x68, 0x41, 0x38, 0x8c, 0x87, 0x29, 0x1e, 0xb1, 0x06, 0x10, 0x98,
	0x13, 0xf6, 0xbb, 0xbb, 0x2c, 0x24, 0x96, 0x59, 0xd1, 0xf3, 0xd3, 0xe7, 0x21, 0x21, 0xdc, 0x8b,
	0x63, 0x78, 0x10, 0xe7, 0x4a, 0xe4, 0x41, 0x3d, 0x1d, 0xe1, 0x91, 0x92, 0xa5, 0xfb, 0x30, 0xed,
	0x1d, 0xc9, 0xf5, 0x2d, 0xfe, 0x14, 0x32, 0x0f, 0xa8, 0x5e, 0x5a, 0xdb, 0xa9, 0x95, 0x1b, 0xf7,
	0xf5, 0xad, 0xd2, 0xdd, 0xd2, 0x96, 0xbe, 0x55, 0xb9, 0x97, 0x95, 0x22, 0xe4, 0xb7, 0xcb, 0xeb,
	0xd9, 0x04, 0x3a, 0x03, 0x73, 0x21, 0xf9, 0x66, 0x79, 0x63, 0x33, 0x9b, 0x5c, 0xba, 0x0b, 0x73,
	0xfe, 0xbd, 0x40, 0xef, 0xbf, 0x6e, 0xcc, 0xc1, 0x4c, 0x63, 0xb3, 0x5c, 0x5b, 0xd7, 0xab, 0xc5,
	0x5a, 0xe3, 0xbe, 0xbe, 0x5d, 0xde, 0xca, 0x9e, 0x40, 0x08, 0xa6, 0x45, 0xe1, 0x9d, 0x3b, 0x8e,
	0x43, 0x51, 0x76, 0xaf, 0xb4, 0xb6, 0x59, 0x6c, 0x64, 0x13, 0x4b, 0x45, 0x98, 0xec, 0xdf, 0xa3,
	0xd1, 0x2c, 0x4c, 0xed, 0x6c, 0x7f, 0x75, 0xbb, 0x72, 0x6f, 0x5b, 0xaf, 0x6e, 0x56, 0xb6, 0x4b,
	0xd9, 0x13, 0x68, 0x06, 0x32, 0x5b, 0x95, 0x8d, 0xb2, 0x27, 0x90, 0x50, 0x16, 0x4e, 0x72, 0x84,
	0x25, 0x57, 0x92, 0x58, 0xfa, 0xb3, 0x04, 0x53, 0x6e, 0x97, 0x54, 0xdf, 0xe1, 0xfd, 0xe5, 0x73,
	0x90, 0xab, 0x97, 0xea, 0xf5, 0x72, 0x65, 0x5b, 0xdf, 0xa9, 0x17, 0x37, 0x5c, 0x53, 0xfd, 0x56,
	0x79, 0x7b, 0x3d, 0x2b, 0xa1, 0x45, 0x90, 0xa3, 0xb4, 0x3b, 0xdb, 0x5c, 0x9f, 0x18, 0xd4, 0xdf,
	0xd9, 0x29, 0xd5, 0x1b, 0xec, 0x67, 0xbd, 0xd4, 0xc8, 0x26, 0xd1, 0x05, 0x38, 0x1f, 0xfa, 0xbe,
	0x58, 0xaf, 0xdf, 0xab, 0xd4, 0xd6, 0xf5, 0x9d, 0xea, 0x7a, 0xb1, 0x51, 0xca, 0xa6, 0xe2, 0x5c,
	0xd4, 0x4a, 0xdc, 0xc5, 0x18, 0xba, 0x04, 0x4a, 0x50, 0xbf, 0x5e, 0x6a, 0x14, 0xd7, 0x36, 0x75,
	0x81, 0xa2, 0xec, 0xf8, 0xd2, 0x37, 0x61, 0x8e, 0xf7, 0xf9, 0xcc, 0x26, 0xdf, 0x01, 0xf4, 0x7a,
	0xf3, 0x31, 0xde, 0xc3, 0xe8, 0x34, 0xcc, 0xde, 0x2d, 0xd5, 0xca, 0x6f, 0xde, 0xd7, 0xeb, 0x6b,
	0x9b, 0xa5, 0xdb, 0x25, 0xbd, 0x7e, 0xbb, 0x9e, 0x95, 0x90, 0x0c, 0xf3, 0x41, 0xb1, 0x07, 0x3b,
	0x9b, 0x60, 0xec, 0x07, 0x75, 0x6b, 0x95, 0xf5, 0x52, 0x36, 0xb9, 0xf4, 0x23, 0x09, 0x26, 0x2a,
	0x55, 0xbd, 0x71, 0xbf, 0x5a, 0x42, 0x00, 0xe3, 0x95, 0xaa, 0x3b, 0x83, 0xb3, 0x30, 0x55, 0xa9,
	0x72, 0xbe, 0xfa, 0xbc, 0xcf, 0xc1, 0x4c, 0xa5, 0xea, 0x92, 0xe4, 0x51, 0xef, 0x0a, 0x9d, 0xb0,
	0x5c, 0x61, 0x0a, 0x9d, 0x82, 0x2c, 0xb3, 0xe4, 0x5c, 0x38, 0xdc, 0xac, 0x67, 0xd3, 0xae, 0x69,
	0xbd, 0xd4, 0xf0, 0x71, 0x65, 0x51, 0x0e, 0x4e, 0x55, 0xaa, 0x81, 0xe0, 0x9d, 0xd8, 0x95, 0xa5,
	0xcb, 0x90, 0xae, 0xbb, 0x87, 0x15, 0x74, 0x12, 0xd2, 0xf5, 0x4a, 0xad, 0xa1, 0x17, 0xeb, 0x6b,
	0x59, 0x09, 0x4d, 0xc1, 0x24, 0xff, 0xb5, 0x5e, 0xaa, 0xaf, 0x65, 0x13, 0x2b, 0xdf, 0x5f, 0x80,
	0xb4, 0x97, 0xf4, 0xe8, 0x3e, 0x80, 0xff, 0x12, 0x86, 0xce, 0xf8, 0x4b, 0x3b, 0xf0, 0x00, 0x29,
	0x47, 0x2b, 0x28, 0x51, 0xcf, 0xbe, 0xff, 0xec, 0x79, 0x52, 0xfa, 0xf0, 0xd9, 0xf3, 0x64, 0xa2,
	0xa7, 0xfd, 0xf0, 0xd9, 0xf3, 0x64, 0x3a, 0xdf, 0x53, 0x0a, 0x3d, 0xb3, 0xb5, 0x8a, 0x30, 0x4c,
	0x07, 0x1f, 0xa0, 0xd0, 0x42, 0x60, 0x94, 0xe0, 0x33, 0x92, 0x1c, 0xaf, 0xf4, 0xdc, 0x40, 0xa4,
	0x9b, 0xa7, 0x30, 0x17, 0xf1, 0x88, 0x84, 0x84, 0xee, 0x70, 0xf4, 0xbb, 0x95, 0x7c, 0x84, 0x05,
	0x25, 0xea, 0x02, 0xf3, 0x9a, 0xe1, 0x5e, 0x6d, 0xee, 0x15, 0xf2, 0xb6, 0x52, 0xe0, 0x8d, 0x89,
	0x55, 0xf4, 0x2d, 0x87, 0x39, 0x67, 0x19, 0x85, 0x99, 0xeb, 0x3f, 0x72, 0xc8, 0xd1, 0x0a, 0x4a,
	0xd4, 0x25, 0x36, 0xf8, 0x0c, 0x1b, 0x3c, 0xd5, 0xd3, 0x30, 0x1f, 0xfe, 0x8c, 0x17, 0x94, 0x92,
	0xc7, 0x4a, 0x81, 0xbf, 0xfc, 0x68, 0xcb, 0xf9, 0xd7, 0x57, 0x91, 0x05, 0x33, 0xa1, 0x47, 0x10,
	0x74, 0x6e, 0x10, 0xbd, 0xe0, 0x75, 0x88, 0x96, 0x12, 0x55, 0x61, 0xae, 0xb3, 0x3c, 0x2e, 0xca,
	0x1d, 0xcf, 0xe4, 0xa9, 0x52, 0x70, 0xbb, 0x24, 0x0a, 0x23, 0xb5, 0xc5, 0x1f, 0x0f, 0x83, 0xcf,
	0x22, 0x68, 0x31, 0x10, 0xca, 0xc0, 0x5b, 0x8a, 0x3c, 0x54, 0x4f, 0x89, 0x3a, 0xc3, 0xdc, 0x9e,
	0x62, 0x6e, 0x4f, 0x30, 0xa7, 0x27, 0xd0, 0xef, 0x25, 0xc8, 0x08, 0xef, 0x08, 0x28, 0x27, 0x36,
	0x4a, 0x02, 0x43, 0xc7, 0x68, 0x28, 0x51, 0xdf, 0x97, 0xd8, 0xa8, 0xa7, 0xd9, 0xa8, 0xa8, 0xa7,
	0x59, 0x1a, 0xd1, 0xf6, 0xb5, 0x77, 0xb4, 0x43, 0xed, 0x5d, 0x8d, 0x6a, 0xce, 0xa4, 0xe9, 0x3e,
	0xab, 0x96, 0x52, 0xd8, 0xbf, 0xa1, 0xf0, 0x3f, 0x89, 0x52, 0xd8, 0x5f, 0x71, 0xfe, 0xdc, 0x57,
	0x0a, 0xfb, 0xaf, 0xf1, 0x3f, 0xdf, 0xce, 0xbf, 0xa3, 0x14, 0x8c, 0x1b, 0xab, 0x4a, 0xfe, 0xb0,
	0x60, 0xac, 0xac, 0x2a, 0xf9, 0x77, 0x95, 0x82, 0xf1, 0xda, 0xea, 0x03, 0xe5, 0xed, 0x10, 0x41,
	0x8a, 0x9f, 0x08, 0x0f, 0xd0, 0xc7, 0x12, 0x4c, 0x07, 0x9f, 0x17, 0xc4, 0x4c, 0x1f, 0x78, 0xe1,
	0x90, 0xe3, 0x95, 0x94, 0xa8, 0x0f, 0x58, 0x38, 0xf3, 0x2c, 0x9c, 0xe9, 0x60, 0x38, 0x2c, 0x94,
	0xaf, 0x8c, 0x1e, 0x4a, 0x74, 0x24, 0xe8, 0xdb, 0x12, 0x4c, 0xf6, 0x5b, 0xf9, 0x68, 0xde, 0x47,
	0x22, 0xbe, 0x84, 0xc8, 0x91, 0x72, 0x4a, 0xd4, 0x0d, 0x06, 0x4e, 0x66, 0xe0, 0xd2, 0x3d, 0x8d,
	0xf4, 0x19, 0x5e, 0xf6, 0x61, 0x11, 0xa5, 0xc0, 0x9b, 0xba, 0xab, 0xc3, 0x29, 0x7b, 0x0f, 0x32,
	0x42, 0x9b, 0x56, 0x9c, 0xf8, 0xe0, 0x0b, 0x81, 0x1c, 0xa3, 0xa1, 0x44, 0xd5, 0x18, 0x96, 0x05,
	0x86, 0x65, 0xbc, 0xd7, 0x47, 0xf2, 0x72, 0x1f, 0xc9, 0x50, 0xf7, 0x16, 0x64, 0x84, 0x6e, 0xb3,
	0xe8, 0x3e, 0xd8, 0xc9, 0x97, 0xcf, 0xc6, 0x68, 0x28, 0x51, 0xaf, 0x32, 0xff, 0xe7, 0x98, 0xff,
	0x31, 0xc6, 0xc5, 0x6e, 0x68, 0x01, 0xfb, 0x44, 0xe4, 0x77, 0x15, 0xf4, 0x63, 0x09, 0xa6, 0x83,
	0x1d, 0x55, 0x31, 0x45, 0x06, 0x3a, 0xcd, 0x72, 0xbc, 0x92, 0x12, 0x75, 0x8b, 0x79, 0x5e, 0x1c,
	0x98, 0x85, 0x37, 0x02, 0xce, 0x3b, 0xf8, 0x40, 0xf1, 0xfe, 0xb3, 0xc4, 0xaa, 0x12, 0x4f, 0x06,
	0xfa, 0x50, 0x82, 0x6c, 0xb8, 0x8d, 0x89, 0x84, 0x9b, 0x5e, 0x44, 0xf7, 0x56, 0x1e, 0xa6, 0xa6,
	0x44, 0xbd, 0xc9, 0x00, 0x5e, 0x0a, 0x4d, 0xcd, 0x45, 0x1f, 0x5e, 0x3c, 0x98, 0x0f, 0x12, 0x30,
	0x13, 0xea, 0xb8, 0x89, 0x95, 0x6e, 0xb0, 0x43, 0x2a, 0x0f, 0xd1, 0x52, 0xa2, 0xfe, 0x82, 0x57,
	0x87, 0x2b, 0xee, 0x72, 0xb2, 0x35, 0xaa, 0xed, 0x6a, 0x58, 0xb3, 0xb4, 0x36, 0x87, 0xf4, 0x91,
	0x24, 0xa4, 0x8b, 0xad, 0x14, 0xdc, 0x4b, 0xa6, 0xb7, 0xde, 0xfb, 0xcd, 0x45, 0x2e, 0xd8, 0x55,
	0x0a, 0x7e, 0xa7, 0xe1, 0x55, 0x05, 0x3f, 0xd2, 0x56, 0x96, 0x57, 0x56, 0xf2, 0xcb, 0x2b, 0xf9,
	0xe5, 0x1b, 0xca, 0xf2, 0xb2, 0xc6, 0xff, 0x71, 0x4b, 0xac, 0x14, 0xbc, 0x76, 0x43, 0xc0, 0x6e,
	0xe5, 0x66, 0xd0, 0xce, 0x52, 0x0a, 0xce, 0x55, 0x86, 0xff, 0x6a, 0x2b, 0x05, 0x7e, 0x77, 0x59,
	0x7d, 0x80, 0x3e, 0x90, 0x20, 0x17, 0xd7, 0xed, 0x42, 0x2f, 0x07, 0x02, 0x8e, 0xeb, 0x07, 0xc9,
	0xa3, 0x98, 0x79, 0x1b, 0xeb, 0x2b, 0x91, 0x1b, 0xeb, 0xaf, 0x24, 0x58, 0x18, 0xd2, 0x95, 0x42,
	0x57, 0xc2, 0x39, 0x1a, 0x8b, 0x65, 0x44, 0x4b, 0x4a, 0xd4, 0x12, 0x83, 0xb3, 0xe4, 0xa6, 0x36,
	0xd5, 0x0c, 0xcd, 0xe2, 0xa0, 0xae, 0x05, 0x73, 0x87, 0x9b, 0xaf, 0x2a, 0x79, 0x43, 0x29, 0x78,
	0x37, 0x6a, 0x5e, 0x11, 0xbd, 0xbe, 0x07, 0xfa, 0x9e, 0x04, 0x72, 0x7c, 0xd7, 0x0b, 0x5d, 0xf6,
	0xf1, 0x0c, 0x6d, 0xaa, 0xc9, 0xa3, 0x19, 0x7a, 0x34, 0x5e, 0x8d, 0xa4, 0xf1, 0x9f, 0x91, 0x93,
	0xe9, 0xdc, 0xb0, 0x86, 0x4f, 0x66, 0xff, 0xca, 0x2d, 0x8f, 0x62, 0x46, 0x89, 0xfa, 0x1e, 0x43,
	0xf1, 0x2a, 0x43, 0x91, 0x65, 0xec, 0x59, 0x9a, 0xa1, 0xb5, 0xb5, 0x16, 0x4f, 0x79, 0x86, 0xe9,
	0x1b, 0xd1, 0x2c, 0xfa, 0x69, 0xc8, 0x19, 0x0d, 0x35, 0xe6, 0x56, 0x15, 0x3f, 0x33, 0x95, 0xe0,
	0x22, 0x70, 0x8e, 0x29, 0x5e, 0xaa, 0xaf, 0xa2, 0x8f, 0x24, 0x38, 0x13, 0xd3, 0x31, 0x46, 0x97,
	0x84, 0x1d, 0x25, 0xb6, 0x17, 0x2e, 0x8f, 0x60, 0x45, 0x89, 0x7a, 0x8d, 0x85, 0x99, 0x77, 0x4f,
	0x4e, 0x4e, 0x71, 0x59, 0x70, 0x43, 0xe3, 0xf7, 0x54, 0xa7, 0xa2, 0x78, 0x8b, 0x59, 0x41, 0xdf,
	0x91, 0xf8, 0x7f, 0xf2, 0x18, 0x68, 0x10, 0xa1, 0x0b, 0x41, 0x5a, 0x23, 0x5a, 0x52, 0xf2, 0x51,
	0x26, 0x94, 0xa8, 0xaf, 0x30, 0x38, 0xd7, 0x02, 0x70, 0xe6, 0x7d, 0xa6, 0x03, 0x48, 0x7e, 0xdd,
	0x6f, 0x44, 0xc7, 0xb5, 0x5f, 0xd0, 0xd5, 0xf0, 0x5a, 0x19, 0xd2, 0x88, 0x92, 0x47, 0x37, 0xa6,
	0x44, 0x2d, 0x32, 0x9c, 0xd7, 0x85, 0xb5, 0xe5, 0x60, 0x5d, 0x1a, 0x65, 0x6d, 0xd9, 0x0a, 0x6f,
	0x7c, 0xfc, 0x46, 0x82, 0xf9, 0xe8, 0x5e, 0x01, 0xba, 0x18, 0x3c, 0xbe, 0x47, 0xb6, 0x8e, 0xe4,
	0xa3, 0x8d, 0x28, 0x51, 0xbf, 0xce, 0x70, 0x2e, 0x33, 0x9c, 0x19, 0xaa, 0x31, 0x46, 0x2d, 0xed,
	0xa1, 0xd6, 0x76, 0x8e, 0x3f, 0x22, 0x42, 0x91, 0x62, 0x86, 0xce, 0x49, 0x65, 0x6a, 0x75, 0x59,
	0x02, 0x3c, 0x14, 0x92, 0xda, 0xcf, 0x5f, 0xbe, 0xd9, 0x85, 0xfb, 0x00, 0xe2, 0x66, 0x17, 0xd1,
	0x95, 0x90, 0x87, 0xa9, 0xbd, 0xcd, 0xee, 0x06, 0x3f, 0x07, 0x98, 0xda, 0x53, 0xad, 0xc7, 0x80,
	0x5e, 0xca, 0x9b, 0x4a, 0x81, 0x23, 0x7a, 0xaa, 0x14, 0x78, 0xa7, 0xc2, 0xc5, 0x2b, 0xf4, 0x2a,
	0x56, 0xe5, 0xf1, 0xef, 0x3e, 0x7b, 0x9e, 0xfc, 0xeb, 0xe1, 0xad, 0xec, 0x27, 0x9f, 0x2d, 0x4a,
	0x7f, 0xfc, 0x6c, 0x51, 0xfa, 0xdb, 0x67, 0x8b, 0xd2, 0x0f, 0xfe, 0xb1, 0x78, 0xe2, 0x3f, 0x01,
	0x00, 0x00, 0xff, 0xff, 0x77, 0x03, 0x56, 0xa2, 0xe8, 0x2a, 0x00, 0x00,
}
