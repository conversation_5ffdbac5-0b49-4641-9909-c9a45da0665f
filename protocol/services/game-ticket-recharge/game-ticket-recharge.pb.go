// Code generated by protoc-gen-go. DO NOT EDIT.
// source: game-ticket-recharge/game-ticket-recharge.proto

package game_ticket_recharge // import "golang.52tt.com/protocol/services/game-ticket-recharge"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
import unified_pay_rmb "golang.52tt.com/protocol/services/unified-pay-rmb"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 订单状态
type MiJingPayStatus int32

const (
	MiJingPayStatus_MI_JING_PAY_STATUS_UNSPECIFIED MiJingPayStatus = 0
	MiJingPayStatus_MI_JING_PAY_STATUS_PAYING      MiJingPayStatus = 1
	MiJingPayStatus_MI_JING_PAY_STATUS_SUCCESS     MiJingPayStatus = 2
	MiJingPayStatus_MI_JING_PAY_STATUS_FAILED      MiJingPayStatus = 3
)

var MiJingPayStatus_name = map[int32]string{
	0: "MI_JING_PAY_STATUS_UNSPECIFIED",
	1: "MI_JING_PAY_STATUS_PAYING",
	2: "MI_JING_PAY_STATUS_SUCCESS",
	3: "MI_JING_PAY_STATUS_FAILED",
}
var MiJingPayStatus_value = map[string]int32{
	"MI_JING_PAY_STATUS_UNSPECIFIED": 0,
	"MI_JING_PAY_STATUS_PAYING":      1,
	"MI_JING_PAY_STATUS_SUCCESS":     2,
	"MI_JING_PAY_STATUS_FAILED":      3,
}

func (x MiJingPayStatus) String() string {
	return proto.EnumName(MiJingPayStatus_name, int32(x))
}
func (MiJingPayStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{0}
}

type GameType int32

const (
	GameType_UnknownGameType GameType = 0
	GameType_MysteryRealm    GameType = 1
)

var GameType_name = map[int32]string{
	0: "UnknownGameType",
	1: "MysteryRealm",
}
var GameType_value = map[string]int32{
	"UnknownGameType": 0,
	"MysteryRealm":    1,
}

func (x GameType) String() string {
	return proto.EnumName(GameType_name, int32(x))
}
func (GameType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{1}
}

// 支付结果回调
type PayRmbResultNotifyReq struct {
	OrderResult          *unified_pay_rmb.RmbPayOrderInfo `protobuf:"bytes,1,opt,name=order_result,json=orderResult,proto3" json:"order_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *PayRmbResultNotifyReq) Reset()         { *m = PayRmbResultNotifyReq{} }
func (m *PayRmbResultNotifyReq) String() string { return proto.CompactTextString(m) }
func (*PayRmbResultNotifyReq) ProtoMessage()    {}
func (*PayRmbResultNotifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{0}
}
func (m *PayRmbResultNotifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayRmbResultNotifyReq.Unmarshal(m, b)
}
func (m *PayRmbResultNotifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayRmbResultNotifyReq.Marshal(b, m, deterministic)
}
func (dst *PayRmbResultNotifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayRmbResultNotifyReq.Merge(dst, src)
}
func (m *PayRmbResultNotifyReq) XXX_Size() int {
	return xxx_messageInfo_PayRmbResultNotifyReq.Size(m)
}
func (m *PayRmbResultNotifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PayRmbResultNotifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_PayRmbResultNotifyReq proto.InternalMessageInfo

func (m *PayRmbResultNotifyReq) GetOrderResult() *unified_pay_rmb.RmbPayOrderInfo {
	if m != nil {
		return m.OrderResult
	}
	return nil
}

type PayRmbResultNotifyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayRmbResultNotifyResp) Reset()         { *m = PayRmbResultNotifyResp{} }
func (m *PayRmbResultNotifyResp) String() string { return proto.CompactTextString(m) }
func (*PayRmbResultNotifyResp) ProtoMessage()    {}
func (*PayRmbResultNotifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{1}
}
func (m *PayRmbResultNotifyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayRmbResultNotifyResp.Unmarshal(m, b)
}
func (m *PayRmbResultNotifyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayRmbResultNotifyResp.Marshal(b, m, deterministic)
}
func (dst *PayRmbResultNotifyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayRmbResultNotifyResp.Merge(dst, src)
}
func (m *PayRmbResultNotifyResp) XXX_Size() int {
	return xxx_messageInfo_PayRmbResultNotifyResp.Size(m)
}
func (m *PayRmbResultNotifyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PayRmbResultNotifyResp.DiscardUnknown(m)
}

var xxx_messageInfo_PayRmbResultNotifyResp proto.InternalMessageInfo

// 新增充值配置
type AddMiJingPayConfigReq struct {
	Opt                  *PayOption `protobuf:"bytes,1,opt,name=opt,proto3" json:"opt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *AddMiJingPayConfigReq) Reset()         { *m = AddMiJingPayConfigReq{} }
func (m *AddMiJingPayConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddMiJingPayConfigReq) ProtoMessage()    {}
func (*AddMiJingPayConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{2}
}
func (m *AddMiJingPayConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMiJingPayConfigReq.Unmarshal(m, b)
}
func (m *AddMiJingPayConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMiJingPayConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddMiJingPayConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMiJingPayConfigReq.Merge(dst, src)
}
func (m *AddMiJingPayConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddMiJingPayConfigReq.Size(m)
}
func (m *AddMiJingPayConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMiJingPayConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddMiJingPayConfigReq proto.InternalMessageInfo

func (m *AddMiJingPayConfigReq) GetOpt() *PayOption {
	if m != nil {
		return m.Opt
	}
	return nil
}

type AddMiJingPayConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddMiJingPayConfigResp) Reset()         { *m = AddMiJingPayConfigResp{} }
func (m *AddMiJingPayConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddMiJingPayConfigResp) ProtoMessage()    {}
func (*AddMiJingPayConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{3}
}
func (m *AddMiJingPayConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMiJingPayConfigResp.Unmarshal(m, b)
}
func (m *AddMiJingPayConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMiJingPayConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddMiJingPayConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMiJingPayConfigResp.Merge(dst, src)
}
func (m *AddMiJingPayConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddMiJingPayConfigResp.Size(m)
}
func (m *AddMiJingPayConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMiJingPayConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddMiJingPayConfigResp proto.InternalMessageInfo

// 删除充值配置
type DeleteMiJingPayConfigReq struct {
	OptId                uint32   `protobuf:"varint,1,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteMiJingPayConfigReq) Reset()         { *m = DeleteMiJingPayConfigReq{} }
func (m *DeleteMiJingPayConfigReq) String() string { return proto.CompactTextString(m) }
func (*DeleteMiJingPayConfigReq) ProtoMessage()    {}
func (*DeleteMiJingPayConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{4}
}
func (m *DeleteMiJingPayConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteMiJingPayConfigReq.Unmarshal(m, b)
}
func (m *DeleteMiJingPayConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteMiJingPayConfigReq.Marshal(b, m, deterministic)
}
func (dst *DeleteMiJingPayConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteMiJingPayConfigReq.Merge(dst, src)
}
func (m *DeleteMiJingPayConfigReq) XXX_Size() int {
	return xxx_messageInfo_DeleteMiJingPayConfigReq.Size(m)
}
func (m *DeleteMiJingPayConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteMiJingPayConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteMiJingPayConfigReq proto.InternalMessageInfo

func (m *DeleteMiJingPayConfigReq) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

type DeleteMiJingPayConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteMiJingPayConfigResp) Reset()         { *m = DeleteMiJingPayConfigResp{} }
func (m *DeleteMiJingPayConfigResp) String() string { return proto.CompactTextString(m) }
func (*DeleteMiJingPayConfigResp) ProtoMessage()    {}
func (*DeleteMiJingPayConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{5}
}
func (m *DeleteMiJingPayConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteMiJingPayConfigResp.Unmarshal(m, b)
}
func (m *DeleteMiJingPayConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteMiJingPayConfigResp.Marshal(b, m, deterministic)
}
func (dst *DeleteMiJingPayConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteMiJingPayConfigResp.Merge(dst, src)
}
func (m *DeleteMiJingPayConfigResp) XXX_Size() int {
	return xxx_messageInfo_DeleteMiJingPayConfigResp.Size(m)
}
func (m *DeleteMiJingPayConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteMiJingPayConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteMiJingPayConfigResp proto.InternalMessageInfo

// 获取充值配置
type GetMiJingPayConfigReq struct {
	OsType               uint32   `protobuf:"varint,1,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMiJingPayConfigReq) Reset()         { *m = GetMiJingPayConfigReq{} }
func (m *GetMiJingPayConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetMiJingPayConfigReq) ProtoMessage()    {}
func (*GetMiJingPayConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{6}
}
func (m *GetMiJingPayConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMiJingPayConfigReq.Unmarshal(m, b)
}
func (m *GetMiJingPayConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMiJingPayConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetMiJingPayConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMiJingPayConfigReq.Merge(dst, src)
}
func (m *GetMiJingPayConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetMiJingPayConfigReq.Size(m)
}
func (m *GetMiJingPayConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMiJingPayConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMiJingPayConfigReq proto.InternalMessageInfo

func (m *GetMiJingPayConfigReq) GetOsType() uint32 {
	if m != nil {
		return m.OsType
	}
	return 0
}

type GetMiJingPayConfigResp struct {
	Opts                 []*PayOption `protobuf:"bytes,1,rep,name=opts,proto3" json:"opts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMiJingPayConfigResp) Reset()         { *m = GetMiJingPayConfigResp{} }
func (m *GetMiJingPayConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetMiJingPayConfigResp) ProtoMessage()    {}
func (*GetMiJingPayConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{7}
}
func (m *GetMiJingPayConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMiJingPayConfigResp.Unmarshal(m, b)
}
func (m *GetMiJingPayConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMiJingPayConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetMiJingPayConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMiJingPayConfigResp.Merge(dst, src)
}
func (m *GetMiJingPayConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetMiJingPayConfigResp.Size(m)
}
func (m *GetMiJingPayConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMiJingPayConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMiJingPayConfigResp proto.InternalMessageInfo

func (m *GetMiJingPayConfigResp) GetOpts() []*PayOption {
	if m != nil {
		return m.Opts
	}
	return nil
}

type PayOption struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	OsType               uint32   `protobuf:"varint,2,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`
	ApplePayProductId    string   `protobuf:"bytes,3,opt,name=apple_pay_product_id,json=applePayProductId,proto3" json:"apple_pay_product_id,omitempty"`
	TicketId             uint32   `protobuf:"varint,4,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	TicketNum            uint32   `protobuf:"varint,5,opt,name=ticket_num,json=ticketNum,proto3" json:"ticket_num,omitempty"`
	TBeanNum             uint32   `protobuf:"varint,6,opt,name=t_bean_num,json=tBeanNum,proto3" json:"t_bean_num,omitempty"`
	GiftName             string   `protobuf:"bytes,7,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GameTicketUrl        string   `protobuf:"bytes,8,opt,name=game_ticket_url,json=gameTicketUrl,proto3" json:"game_ticket_url,omitempty"`
	GiftUrl              string   `protobuf:"bytes,9,opt,name=gift_url,json=giftUrl,proto3" json:"gift_url,omitempty"`
	Price                uint32   `protobuf:"varint,10,opt,name=price,proto3" json:"price,omitempty"`
	PackId               uint32   `protobuf:"varint,11,opt,name=pack_id,json=packId,proto3" json:"pack_id,omitempty"`
	PackNum              uint32   `protobuf:"varint,12,opt,name=pack_num,json=packNum,proto3" json:"pack_num,omitempty"`
	TicketName           string   `protobuf:"bytes,13,opt,name=ticket_name,json=ticketName,proto3" json:"ticket_name,omitempty"`
	GameTicketsUrl       string   `protobuf:"bytes,14,opt,name=game_tickets_url,json=gameTicketsUrl,proto3" json:"game_tickets_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayOption) Reset()         { *m = PayOption{} }
func (m *PayOption) String() string { return proto.CompactTextString(m) }
func (*PayOption) ProtoMessage()    {}
func (*PayOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{8}
}
func (m *PayOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayOption.Unmarshal(m, b)
}
func (m *PayOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayOption.Marshal(b, m, deterministic)
}
func (dst *PayOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayOption.Merge(dst, src)
}
func (m *PayOption) XXX_Size() int {
	return xxx_messageInfo_PayOption.Size(m)
}
func (m *PayOption) XXX_DiscardUnknown() {
	xxx_messageInfo_PayOption.DiscardUnknown(m)
}

var xxx_messageInfo_PayOption proto.InternalMessageInfo

func (m *PayOption) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PayOption) GetOsType() uint32 {
	if m != nil {
		return m.OsType
	}
	return 0
}

func (m *PayOption) GetApplePayProductId() string {
	if m != nil {
		return m.ApplePayProductId
	}
	return ""
}

func (m *PayOption) GetTicketId() uint32 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *PayOption) GetTicketNum() uint32 {
	if m != nil {
		return m.TicketNum
	}
	return 0
}

func (m *PayOption) GetTBeanNum() uint32 {
	if m != nil {
		return m.TBeanNum
	}
	return 0
}

func (m *PayOption) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *PayOption) GetGameTicketUrl() string {
	if m != nil {
		return m.GameTicketUrl
	}
	return ""
}

func (m *PayOption) GetGiftUrl() string {
	if m != nil {
		return m.GiftUrl
	}
	return ""
}

func (m *PayOption) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *PayOption) GetPackId() uint32 {
	if m != nil {
		return m.PackId
	}
	return 0
}

func (m *PayOption) GetPackNum() uint32 {
	if m != nil {
		return m.PackNum
	}
	return 0
}

func (m *PayOption) GetTicketName() string {
	if m != nil {
		return m.TicketName
	}
	return ""
}

func (m *PayOption) GetGameTicketsUrl() string {
	if m != nil {
		return m.GameTicketsUrl
	}
	return ""
}

// 获取支付渠道
type GetMiJingPayChannelReq struct {
	OsType               uint32   `protobuf:"varint,1,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMiJingPayChannelReq) Reset()         { *m = GetMiJingPayChannelReq{} }
func (m *GetMiJingPayChannelReq) String() string { return proto.CompactTextString(m) }
func (*GetMiJingPayChannelReq) ProtoMessage()    {}
func (*GetMiJingPayChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{9}
}
func (m *GetMiJingPayChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMiJingPayChannelReq.Unmarshal(m, b)
}
func (m *GetMiJingPayChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMiJingPayChannelReq.Marshal(b, m, deterministic)
}
func (dst *GetMiJingPayChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMiJingPayChannelReq.Merge(dst, src)
}
func (m *GetMiJingPayChannelReq) XXX_Size() int {
	return xxx_messageInfo_GetMiJingPayChannelReq.Size(m)
}
func (m *GetMiJingPayChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMiJingPayChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMiJingPayChannelReq proto.InternalMessageInfo

func (m *GetMiJingPayChannelReq) GetOsType() uint32 {
	if m != nil {
		return m.OsType
	}
	return 0
}

type GetMiJingPayChannelResp struct {
	PayChannels          []string `protobuf:"bytes,1,rep,name=pay_channels,json=payChannels,proto3" json:"pay_channels,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMiJingPayChannelResp) Reset()         { *m = GetMiJingPayChannelResp{} }
func (m *GetMiJingPayChannelResp) String() string { return proto.CompactTextString(m) }
func (*GetMiJingPayChannelResp) ProtoMessage()    {}
func (*GetMiJingPayChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{10}
}
func (m *GetMiJingPayChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMiJingPayChannelResp.Unmarshal(m, b)
}
func (m *GetMiJingPayChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMiJingPayChannelResp.Marshal(b, m, deterministic)
}
func (dst *GetMiJingPayChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMiJingPayChannelResp.Merge(dst, src)
}
func (m *GetMiJingPayChannelResp) XXX_Size() int {
	return xxx_messageInfo_GetMiJingPayChannelResp.Size(m)
}
func (m *GetMiJingPayChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMiJingPayChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMiJingPayChannelResp proto.InternalMessageInfo

func (m *GetMiJingPayChannelResp) GetPayChannels() []string {
	if m != nil {
		return m.PayChannels
	}
	return nil
}

// 下单
type MiJingPayReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OptId                uint32   `protobuf:"varint,2,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	PayChannel           string   `protobuf:"bytes,3,opt,name=pay_channel,json=payChannel,proto3" json:"pay_channel,omitempty"`
	BundleId             string   `protobuf:"bytes,4,opt,name=bundle_id,json=bundleId,proto3" json:"bundle_id,omitempty"`
	ProductId            string   `protobuf:"bytes,5,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MiJingPayReq) Reset()         { *m = MiJingPayReq{} }
func (m *MiJingPayReq) String() string { return proto.CompactTextString(m) }
func (*MiJingPayReq) ProtoMessage()    {}
func (*MiJingPayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{11}
}
func (m *MiJingPayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MiJingPayReq.Unmarshal(m, b)
}
func (m *MiJingPayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MiJingPayReq.Marshal(b, m, deterministic)
}
func (dst *MiJingPayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MiJingPayReq.Merge(dst, src)
}
func (m *MiJingPayReq) XXX_Size() int {
	return xxx_messageInfo_MiJingPayReq.Size(m)
}
func (m *MiJingPayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MiJingPayReq.DiscardUnknown(m)
}

var xxx_messageInfo_MiJingPayReq proto.InternalMessageInfo

func (m *MiJingPayReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MiJingPayReq) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

func (m *MiJingPayReq) GetPayChannel() string {
	if m != nil {
		return m.PayChannel
	}
	return ""
}

func (m *MiJingPayReq) GetBundleId() string {
	if m != nil {
		return m.BundleId
	}
	return ""
}

func (m *MiJingPayReq) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

type MiJingPayResp struct {
	Token                string   `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	PayOrderId           string   `protobuf:"bytes,2,opt,name=pay_order_id,json=payOrderId,proto3" json:"pay_order_id,omitempty"`
	CliOrderTitle        string   `protobuf:"bytes,3,opt,name=cli_order_title,json=cliOrderTitle,proto3" json:"cli_order_title,omitempty"`
	Tsk                  string   `protobuf:"bytes,4,opt,name=tsk,proto3" json:"tsk,omitempty"`
	ChannelMap           string   `protobuf:"bytes,5,opt,name=channel_map,json=channelMap,proto3" json:"channel_map,omitempty"`
	OrderTime            uint32   `protobuf:"varint,6,opt,name=order_time,json=orderTime,proto3" json:"order_time,omitempty"`
	TPayOrderNo          string   `protobuf:"bytes,7,opt,name=t_pay_order_no,json=tPayOrderNo,proto3" json:"t_pay_order_no,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MiJingPayResp) Reset()         { *m = MiJingPayResp{} }
func (m *MiJingPayResp) String() string { return proto.CompactTextString(m) }
func (*MiJingPayResp) ProtoMessage()    {}
func (*MiJingPayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{12}
}
func (m *MiJingPayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MiJingPayResp.Unmarshal(m, b)
}
func (m *MiJingPayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MiJingPayResp.Marshal(b, m, deterministic)
}
func (dst *MiJingPayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MiJingPayResp.Merge(dst, src)
}
func (m *MiJingPayResp) XXX_Size() int {
	return xxx_messageInfo_MiJingPayResp.Size(m)
}
func (m *MiJingPayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MiJingPayResp.DiscardUnknown(m)
}

var xxx_messageInfo_MiJingPayResp proto.InternalMessageInfo

func (m *MiJingPayResp) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *MiJingPayResp) GetPayOrderId() string {
	if m != nil {
		return m.PayOrderId
	}
	return ""
}

func (m *MiJingPayResp) GetCliOrderTitle() string {
	if m != nil {
		return m.CliOrderTitle
	}
	return ""
}

func (m *MiJingPayResp) GetTsk() string {
	if m != nil {
		return m.Tsk
	}
	return ""
}

func (m *MiJingPayResp) GetChannelMap() string {
	if m != nil {
		return m.ChannelMap
	}
	return ""
}

func (m *MiJingPayResp) GetOrderTime() uint32 {
	if m != nil {
		return m.OrderTime
	}
	return 0
}

func (m *MiJingPayResp) GetTPayOrderNo() string {
	if m != nil {
		return m.TPayOrderNo
	}
	return ""
}

// 查询支付结果
type GetMiJingPayResultReq struct {
	PayOrderId           string   `protobuf:"bytes,1,opt,name=pay_order_id,json=payOrderId,proto3" json:"pay_order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMiJingPayResultReq) Reset()         { *m = GetMiJingPayResultReq{} }
func (m *GetMiJingPayResultReq) String() string { return proto.CompactTextString(m) }
func (*GetMiJingPayResultReq) ProtoMessage()    {}
func (*GetMiJingPayResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{13}
}
func (m *GetMiJingPayResultReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMiJingPayResultReq.Unmarshal(m, b)
}
func (m *GetMiJingPayResultReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMiJingPayResultReq.Marshal(b, m, deterministic)
}
func (dst *GetMiJingPayResultReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMiJingPayResultReq.Merge(dst, src)
}
func (m *GetMiJingPayResultReq) XXX_Size() int {
	return xxx_messageInfo_GetMiJingPayResultReq.Size(m)
}
func (m *GetMiJingPayResultReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMiJingPayResultReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMiJingPayResultReq proto.InternalMessageInfo

func (m *GetMiJingPayResultReq) GetPayOrderId() string {
	if m != nil {
		return m.PayOrderId
	}
	return ""
}

type GetMiJingPayResultResp struct {
	Status               MiJingPayStatus  `protobuf:"varint,1,opt,name=status,proto3,enum=game_ticket_recharge.MiJingPayStatus" json:"status,omitempty"`
	Products             []*MiJingProduct `protobuf:"bytes,2,rep,name=products,proto3" json:"products,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetMiJingPayResultResp) Reset()         { *m = GetMiJingPayResultResp{} }
func (m *GetMiJingPayResultResp) String() string { return proto.CompactTextString(m) }
func (*GetMiJingPayResultResp) ProtoMessage()    {}
func (*GetMiJingPayResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{14}
}
func (m *GetMiJingPayResultResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMiJingPayResultResp.Unmarshal(m, b)
}
func (m *GetMiJingPayResultResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMiJingPayResultResp.Marshal(b, m, deterministic)
}
func (dst *GetMiJingPayResultResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMiJingPayResultResp.Merge(dst, src)
}
func (m *GetMiJingPayResultResp) XXX_Size() int {
	return xxx_messageInfo_GetMiJingPayResultResp.Size(m)
}
func (m *GetMiJingPayResultResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMiJingPayResultResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMiJingPayResultResp proto.InternalMessageInfo

func (m *GetMiJingPayResultResp) GetStatus() MiJingPayStatus {
	if m != nil {
		return m.Status
	}
	return MiJingPayStatus_MI_JING_PAY_STATUS_UNSPECIFIED
}

func (m *GetMiJingPayResultResp) GetProducts() []*MiJingProduct {
	if m != nil {
		return m.Products
	}
	return nil
}

type MiJingProduct struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string   `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	Num                  uint32   `protobuf:"varint,3,opt,name=num,proto3" json:"num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MiJingProduct) Reset()         { *m = MiJingProduct{} }
func (m *MiJingProduct) String() string { return proto.CompactTextString(m) }
func (*MiJingProduct) ProtoMessage()    {}
func (*MiJingProduct) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{15}
}
func (m *MiJingProduct) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MiJingProduct.Unmarshal(m, b)
}
func (m *MiJingProduct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MiJingProduct.Marshal(b, m, deterministic)
}
func (dst *MiJingProduct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MiJingProduct.Merge(dst, src)
}
func (m *MiJingProduct) XXX_Size() int {
	return xxx_messageInfo_MiJingProduct.Size(m)
}
func (m *MiJingProduct) XXX_DiscardUnknown() {
	xxx_messageInfo_MiJingProduct.DiscardUnknown(m)
}

var xxx_messageInfo_MiJingProduct proto.InternalMessageInfo

func (m *MiJingProduct) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MiJingProduct) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *MiJingProduct) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

// 充值游戏券赠送的礼物配置
type FreebieCfg struct {
	FreebieId            uint32   `protobuf:"varint,1,opt,name=freebie_id,json=freebieId,proto3" json:"freebie_id,omitempty"`
	PackId               uint32   `protobuf:"varint,2,opt,name=pack_id,json=packId,proto3" json:"pack_id,omitempty"`
	Price                uint32   `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`
	TicketId             uint32   `protobuf:"varint,4,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	TicketName           string   `protobuf:"bytes,5,opt,name=ticket_name,json=ticketName,proto3" json:"ticket_name,omitempty"`
	TicketNumPerPack     uint32   `protobuf:"varint,6,opt,name=ticket_num_per_pack,json=ticketNumPerPack,proto3" json:"ticket_num_per_pack,omitempty"`
	GameType             uint32   `protobuf:"varint,7,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreebieCfg) Reset()         { *m = FreebieCfg{} }
func (m *FreebieCfg) String() string { return proto.CompactTextString(m) }
func (*FreebieCfg) ProtoMessage()    {}
func (*FreebieCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{16}
}
func (m *FreebieCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreebieCfg.Unmarshal(m, b)
}
func (m *FreebieCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreebieCfg.Marshal(b, m, deterministic)
}
func (dst *FreebieCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreebieCfg.Merge(dst, src)
}
func (m *FreebieCfg) XXX_Size() int {
	return xxx_messageInfo_FreebieCfg.Size(m)
}
func (m *FreebieCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_FreebieCfg.DiscardUnknown(m)
}

var xxx_messageInfo_FreebieCfg proto.InternalMessageInfo

func (m *FreebieCfg) GetFreebieId() uint32 {
	if m != nil {
		return m.FreebieId
	}
	return 0
}

func (m *FreebieCfg) GetPackId() uint32 {
	if m != nil {
		return m.PackId
	}
	return 0
}

func (m *FreebieCfg) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *FreebieCfg) GetTicketId() uint32 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *FreebieCfg) GetTicketName() string {
	if m != nil {
		return m.TicketName
	}
	return ""
}

func (m *FreebieCfg) GetTicketNumPerPack() uint32 {
	if m != nil {
		return m.TicketNumPerPack
	}
	return 0
}

func (m *FreebieCfg) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

type FreebieDetailCfg struct {
	Config               *FreebieCfg `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	GiftId               uint32      `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftType             uint32      `protobuf:"varint,3,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	GiftName             string      `protobuf:"bytes,4,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GiftUrl              string      `protobuf:"bytes,5,opt,name=gift_url,json=giftUrl,proto3" json:"gift_url,omitempty"`
	GiftNum              uint32      `protobuf:"varint,6,opt,name=gift_num,json=giftNum,proto3" json:"gift_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *FreebieDetailCfg) Reset()         { *m = FreebieDetailCfg{} }
func (m *FreebieDetailCfg) String() string { return proto.CompactTextString(m) }
func (*FreebieDetailCfg) ProtoMessage()    {}
func (*FreebieDetailCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{17}
}
func (m *FreebieDetailCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreebieDetailCfg.Unmarshal(m, b)
}
func (m *FreebieDetailCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreebieDetailCfg.Marshal(b, m, deterministic)
}
func (dst *FreebieDetailCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreebieDetailCfg.Merge(dst, src)
}
func (m *FreebieDetailCfg) XXX_Size() int {
	return xxx_messageInfo_FreebieDetailCfg.Size(m)
}
func (m *FreebieDetailCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_FreebieDetailCfg.DiscardUnknown(m)
}

var xxx_messageInfo_FreebieDetailCfg proto.InternalMessageInfo

func (m *FreebieDetailCfg) GetConfig() *FreebieCfg {
	if m != nil {
		return m.Config
	}
	return nil
}

func (m *FreebieDetailCfg) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *FreebieDetailCfg) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *FreebieDetailCfg) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *FreebieDetailCfg) GetGiftUrl() string {
	if m != nil {
		return m.GiftUrl
	}
	return ""
}

func (m *FreebieDetailCfg) GetGiftNum() uint32 {
	if m != nil {
		return m.GiftNum
	}
	return 0
}

// 购买游戏券指定礼物
type DoRechargeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FreebieId            uint32   `protobuf:"varint,2,opt,name=freebie_id,json=freebieId,proto3" json:"freebie_id,omitempty"`
	Num                  uint32   `protobuf:"varint,3,opt,name=num,proto3" json:"num,omitempty"`
	GameType             uint32   `protobuf:"varint,4,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	Cost                 uint32   `protobuf:"varint,5,opt,name=cost,proto3" json:"cost,omitempty"`
	OutsideTime          uint32   `protobuf:"varint,6,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoRechargeReq) Reset()         { *m = DoRechargeReq{} }
func (m *DoRechargeReq) String() string { return proto.CompactTextString(m) }
func (*DoRechargeReq) ProtoMessage()    {}
func (*DoRechargeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{18}
}
func (m *DoRechargeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoRechargeReq.Unmarshal(m, b)
}
func (m *DoRechargeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoRechargeReq.Marshal(b, m, deterministic)
}
func (dst *DoRechargeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoRechargeReq.Merge(dst, src)
}
func (m *DoRechargeReq) XXX_Size() int {
	return xxx_messageInfo_DoRechargeReq.Size(m)
}
func (m *DoRechargeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DoRechargeReq.DiscardUnknown(m)
}

var xxx_messageInfo_DoRechargeReq proto.InternalMessageInfo

func (m *DoRechargeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DoRechargeReq) GetFreebieId() uint32 {
	if m != nil {
		return m.FreebieId
	}
	return 0
}

func (m *DoRechargeReq) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *DoRechargeReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *DoRechargeReq) GetCost() uint32 {
	if m != nil {
		return m.Cost
	}
	return 0
}

func (m *DoRechargeReq) GetOutsideTime() uint32 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

type DoRechargeResp struct {
	PackId               uint32   `protobuf:"varint,1,opt,name=pack_id,json=packId,proto3" json:"pack_id,omitempty"`
	TicketId             uint32   `protobuf:"varint,2,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	TicketNum            uint32   `protobuf:"varint,3,opt,name=ticket_num,json=ticketNum,proto3" json:"ticket_num,omitempty"`
	Balance              uint32   `protobuf:"varint,4,opt,name=balance,proto3" json:"balance,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoRechargeResp) Reset()         { *m = DoRechargeResp{} }
func (m *DoRechargeResp) String() string { return proto.CompactTextString(m) }
func (*DoRechargeResp) ProtoMessage()    {}
func (*DoRechargeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{19}
}
func (m *DoRechargeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoRechargeResp.Unmarshal(m, b)
}
func (m *DoRechargeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoRechargeResp.Marshal(b, m, deterministic)
}
func (dst *DoRechargeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoRechargeResp.Merge(dst, src)
}
func (m *DoRechargeResp) XXX_Size() int {
	return xxx_messageInfo_DoRechargeResp.Size(m)
}
func (m *DoRechargeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DoRechargeResp.DiscardUnknown(m)
}

var xxx_messageInfo_DoRechargeResp proto.InternalMessageInfo

func (m *DoRechargeResp) GetPackId() uint32 {
	if m != nil {
		return m.PackId
	}
	return 0
}

func (m *DoRechargeResp) GetTicketId() uint32 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *DoRechargeResp) GetTicketNum() uint32 {
	if m != nil {
		return m.TicketNum
	}
	return 0
}

func (m *DoRechargeResp) GetBalance() uint32 {
	if m != nil {
		return m.Balance
	}
	return 0
}

// 获取游戏券指定的礼物id
type GetFreebieCfgByGameTypeReq struct {
	GameType             uint32   `protobuf:"varint,1,opt,name=gameType,proto3" json:"gameType,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFreebieCfgByGameTypeReq) Reset()         { *m = GetFreebieCfgByGameTypeReq{} }
func (m *GetFreebieCfgByGameTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetFreebieCfgByGameTypeReq) ProtoMessage()    {}
func (*GetFreebieCfgByGameTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{20}
}
func (m *GetFreebieCfgByGameTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFreebieCfgByGameTypeReq.Unmarshal(m, b)
}
func (m *GetFreebieCfgByGameTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFreebieCfgByGameTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetFreebieCfgByGameTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFreebieCfgByGameTypeReq.Merge(dst, src)
}
func (m *GetFreebieCfgByGameTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetFreebieCfgByGameTypeReq.Size(m)
}
func (m *GetFreebieCfgByGameTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFreebieCfgByGameTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFreebieCfgByGameTypeReq proto.InternalMessageInfo

func (m *GetFreebieCfgByGameTypeReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

type GetFreebieCfgByGameTypeResp struct {
	Config               *FreebieDetailCfg `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	RechargeLimit        uint32            `protobuf:"varint,2,opt,name=recharge_limit,json=rechargeLimit,proto3" json:"recharge_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetFreebieCfgByGameTypeResp) Reset()         { *m = GetFreebieCfgByGameTypeResp{} }
func (m *GetFreebieCfgByGameTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetFreebieCfgByGameTypeResp) ProtoMessage()    {}
func (*GetFreebieCfgByGameTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{21}
}
func (m *GetFreebieCfgByGameTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFreebieCfgByGameTypeResp.Unmarshal(m, b)
}
func (m *GetFreebieCfgByGameTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFreebieCfgByGameTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetFreebieCfgByGameTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFreebieCfgByGameTypeResp.Merge(dst, src)
}
func (m *GetFreebieCfgByGameTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetFreebieCfgByGameTypeResp.Size(m)
}
func (m *GetFreebieCfgByGameTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFreebieCfgByGameTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFreebieCfgByGameTypeResp proto.InternalMessageInfo

func (m *GetFreebieCfgByGameTypeResp) GetConfig() *FreebieDetailCfg {
	if m != nil {
		return m.Config
	}
	return nil
}

func (m *GetFreebieCfgByGameTypeResp) GetRechargeLimit() uint32 {
	if m != nil {
		return m.RechargeLimit
	}
	return 0
}

// 设置游戏券礼物配置
type SetGameTicketFreebieCfgReq struct {
	Config               *FreebieCfg `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SetGameTicketFreebieCfgReq) Reset()         { *m = SetGameTicketFreebieCfgReq{} }
func (m *SetGameTicketFreebieCfgReq) String() string { return proto.CompactTextString(m) }
func (*SetGameTicketFreebieCfgReq) ProtoMessage()    {}
func (*SetGameTicketFreebieCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{22}
}
func (m *SetGameTicketFreebieCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameTicketFreebieCfgReq.Unmarshal(m, b)
}
func (m *SetGameTicketFreebieCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameTicketFreebieCfgReq.Marshal(b, m, deterministic)
}
func (dst *SetGameTicketFreebieCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameTicketFreebieCfgReq.Merge(dst, src)
}
func (m *SetGameTicketFreebieCfgReq) XXX_Size() int {
	return xxx_messageInfo_SetGameTicketFreebieCfgReq.Size(m)
}
func (m *SetGameTicketFreebieCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameTicketFreebieCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameTicketFreebieCfgReq proto.InternalMessageInfo

func (m *SetGameTicketFreebieCfgReq) GetConfig() *FreebieCfg {
	if m != nil {
		return m.Config
	}
	return nil
}

type SetGameTicketFreebieCfgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGameTicketFreebieCfgResp) Reset()         { *m = SetGameTicketFreebieCfgResp{} }
func (m *SetGameTicketFreebieCfgResp) String() string { return proto.CompactTextString(m) }
func (*SetGameTicketFreebieCfgResp) ProtoMessage()    {}
func (*SetGameTicketFreebieCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{23}
}
func (m *SetGameTicketFreebieCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameTicketFreebieCfgResp.Unmarshal(m, b)
}
func (m *SetGameTicketFreebieCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameTicketFreebieCfgResp.Marshal(b, m, deterministic)
}
func (dst *SetGameTicketFreebieCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameTicketFreebieCfgResp.Merge(dst, src)
}
func (m *SetGameTicketFreebieCfgResp) XXX_Size() int {
	return xxx_messageInfo_SetGameTicketFreebieCfgResp.Size(m)
}
func (m *SetGameTicketFreebieCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameTicketFreebieCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameTicketFreebieCfgResp proto.InternalMessageInfo

type DelGameTicketFreebieCfgReq struct {
	FreebieId            uint32   `protobuf:"varint,1,opt,name=freebie_id,json=freebieId,proto3" json:"freebie_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelGameTicketFreebieCfgReq) Reset()         { *m = DelGameTicketFreebieCfgReq{} }
func (m *DelGameTicketFreebieCfgReq) String() string { return proto.CompactTextString(m) }
func (*DelGameTicketFreebieCfgReq) ProtoMessage()    {}
func (*DelGameTicketFreebieCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{24}
}
func (m *DelGameTicketFreebieCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelGameTicketFreebieCfgReq.Unmarshal(m, b)
}
func (m *DelGameTicketFreebieCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelGameTicketFreebieCfgReq.Marshal(b, m, deterministic)
}
func (dst *DelGameTicketFreebieCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelGameTicketFreebieCfgReq.Merge(dst, src)
}
func (m *DelGameTicketFreebieCfgReq) XXX_Size() int {
	return xxx_messageInfo_DelGameTicketFreebieCfgReq.Size(m)
}
func (m *DelGameTicketFreebieCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelGameTicketFreebieCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelGameTicketFreebieCfgReq proto.InternalMessageInfo

func (m *DelGameTicketFreebieCfgReq) GetFreebieId() uint32 {
	if m != nil {
		return m.FreebieId
	}
	return 0
}

type DelGameTicketFreebieCfgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelGameTicketFreebieCfgResp) Reset()         { *m = DelGameTicketFreebieCfgResp{} }
func (m *DelGameTicketFreebieCfgResp) String() string { return proto.CompactTextString(m) }
func (*DelGameTicketFreebieCfgResp) ProtoMessage()    {}
func (*DelGameTicketFreebieCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{25}
}
func (m *DelGameTicketFreebieCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelGameTicketFreebieCfgResp.Unmarshal(m, b)
}
func (m *DelGameTicketFreebieCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelGameTicketFreebieCfgResp.Marshal(b, m, deterministic)
}
func (dst *DelGameTicketFreebieCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelGameTicketFreebieCfgResp.Merge(dst, src)
}
func (m *DelGameTicketFreebieCfgResp) XXX_Size() int {
	return xxx_messageInfo_DelGameTicketFreebieCfgResp.Size(m)
}
func (m *DelGameTicketFreebieCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelGameTicketFreebieCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelGameTicketFreebieCfgResp proto.InternalMessageInfo

type GetALLFreebieDetailCfgReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetALLFreebieDetailCfgReq) Reset()         { *m = GetALLFreebieDetailCfgReq{} }
func (m *GetALLFreebieDetailCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetALLFreebieDetailCfgReq) ProtoMessage()    {}
func (*GetALLFreebieDetailCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{26}
}
func (m *GetALLFreebieDetailCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetALLFreebieDetailCfgReq.Unmarshal(m, b)
}
func (m *GetALLFreebieDetailCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetALLFreebieDetailCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetALLFreebieDetailCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetALLFreebieDetailCfgReq.Merge(dst, src)
}
func (m *GetALLFreebieDetailCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetALLFreebieDetailCfgReq.Size(m)
}
func (m *GetALLFreebieDetailCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetALLFreebieDetailCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetALLFreebieDetailCfgReq proto.InternalMessageInfo

type GetALLFreebieDetailCfgResp struct {
	ConfigList           []*FreebieDetailCfg `protobuf:"bytes,1,rep,name=config_list,json=configList,proto3" json:"config_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetALLFreebieDetailCfgResp) Reset()         { *m = GetALLFreebieDetailCfgResp{} }
func (m *GetALLFreebieDetailCfgResp) String() string { return proto.CompactTextString(m) }
func (*GetALLFreebieDetailCfgResp) ProtoMessage()    {}
func (*GetALLFreebieDetailCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{27}
}
func (m *GetALLFreebieDetailCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetALLFreebieDetailCfgResp.Unmarshal(m, b)
}
func (m *GetALLFreebieDetailCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetALLFreebieDetailCfgResp.Marshal(b, m, deterministic)
}
func (dst *GetALLFreebieDetailCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetALLFreebieDetailCfgResp.Merge(dst, src)
}
func (m *GetALLFreebieDetailCfgResp) XXX_Size() int {
	return xxx_messageInfo_GetALLFreebieDetailCfgResp.Size(m)
}
func (m *GetALLFreebieDetailCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetALLFreebieDetailCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetALLFreebieDetailCfgResp proto.InternalMessageInfo

func (m *GetALLFreebieDetailCfgResp) GetConfigList() []*FreebieDetailCfg {
	if m != nil {
		return m.ConfigList
	}
	return nil
}

type GetGameTicketFreebieCfgReq struct {
	FreebieId            uint32   `protobuf:"varint,1,opt,name=freebie_id,json=freebieId,proto3" json:"freebie_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameTicketFreebieCfgReq) Reset()         { *m = GetGameTicketFreebieCfgReq{} }
func (m *GetGameTicketFreebieCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetGameTicketFreebieCfgReq) ProtoMessage()    {}
func (*GetGameTicketFreebieCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{28}
}
func (m *GetGameTicketFreebieCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameTicketFreebieCfgReq.Unmarshal(m, b)
}
func (m *GetGameTicketFreebieCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameTicketFreebieCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetGameTicketFreebieCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameTicketFreebieCfgReq.Merge(dst, src)
}
func (m *GetGameTicketFreebieCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetGameTicketFreebieCfgReq.Size(m)
}
func (m *GetGameTicketFreebieCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameTicketFreebieCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameTicketFreebieCfgReq proto.InternalMessageInfo

func (m *GetGameTicketFreebieCfgReq) GetFreebieId() uint32 {
	if m != nil {
		return m.FreebieId
	}
	return 0
}

type GetGameTicketFreebieCfgResp struct {
	Config               *FreebieDetailCfg `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetGameTicketFreebieCfgResp) Reset()         { *m = GetGameTicketFreebieCfgResp{} }
func (m *GetGameTicketFreebieCfgResp) String() string { return proto.CompactTextString(m) }
func (*GetGameTicketFreebieCfgResp) ProtoMessage()    {}
func (*GetGameTicketFreebieCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_recharge_ba912a26f62fdc59, []int{29}
}
func (m *GetGameTicketFreebieCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameTicketFreebieCfgResp.Unmarshal(m, b)
}
func (m *GetGameTicketFreebieCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameTicketFreebieCfgResp.Marshal(b, m, deterministic)
}
func (dst *GetGameTicketFreebieCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameTicketFreebieCfgResp.Merge(dst, src)
}
func (m *GetGameTicketFreebieCfgResp) XXX_Size() int {
	return xxx_messageInfo_GetGameTicketFreebieCfgResp.Size(m)
}
func (m *GetGameTicketFreebieCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameTicketFreebieCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameTicketFreebieCfgResp proto.InternalMessageInfo

func (m *GetGameTicketFreebieCfgResp) GetConfig() *FreebieDetailCfg {
	if m != nil {
		return m.Config
	}
	return nil
}

func init() {
	proto.RegisterType((*PayRmbResultNotifyReq)(nil), "game_ticket_recharge.PayRmbResultNotifyReq")
	proto.RegisterType((*PayRmbResultNotifyResp)(nil), "game_ticket_recharge.PayRmbResultNotifyResp")
	proto.RegisterType((*AddMiJingPayConfigReq)(nil), "game_ticket_recharge.AddMiJingPayConfigReq")
	proto.RegisterType((*AddMiJingPayConfigResp)(nil), "game_ticket_recharge.AddMiJingPayConfigResp")
	proto.RegisterType((*DeleteMiJingPayConfigReq)(nil), "game_ticket_recharge.DeleteMiJingPayConfigReq")
	proto.RegisterType((*DeleteMiJingPayConfigResp)(nil), "game_ticket_recharge.DeleteMiJingPayConfigResp")
	proto.RegisterType((*GetMiJingPayConfigReq)(nil), "game_ticket_recharge.GetMiJingPayConfigReq")
	proto.RegisterType((*GetMiJingPayConfigResp)(nil), "game_ticket_recharge.GetMiJingPayConfigResp")
	proto.RegisterType((*PayOption)(nil), "game_ticket_recharge.PayOption")
	proto.RegisterType((*GetMiJingPayChannelReq)(nil), "game_ticket_recharge.GetMiJingPayChannelReq")
	proto.RegisterType((*GetMiJingPayChannelResp)(nil), "game_ticket_recharge.GetMiJingPayChannelResp")
	proto.RegisterType((*MiJingPayReq)(nil), "game_ticket_recharge.MiJingPayReq")
	proto.RegisterType((*MiJingPayResp)(nil), "game_ticket_recharge.MiJingPayResp")
	proto.RegisterType((*GetMiJingPayResultReq)(nil), "game_ticket_recharge.GetMiJingPayResultReq")
	proto.RegisterType((*GetMiJingPayResultResp)(nil), "game_ticket_recharge.GetMiJingPayResultResp")
	proto.RegisterType((*MiJingProduct)(nil), "game_ticket_recharge.MiJingProduct")
	proto.RegisterType((*FreebieCfg)(nil), "game_ticket_recharge.FreebieCfg")
	proto.RegisterType((*FreebieDetailCfg)(nil), "game_ticket_recharge.FreebieDetailCfg")
	proto.RegisterType((*DoRechargeReq)(nil), "game_ticket_recharge.DoRechargeReq")
	proto.RegisterType((*DoRechargeResp)(nil), "game_ticket_recharge.DoRechargeResp")
	proto.RegisterType((*GetFreebieCfgByGameTypeReq)(nil), "game_ticket_recharge.GetFreebieCfgByGameTypeReq")
	proto.RegisterType((*GetFreebieCfgByGameTypeResp)(nil), "game_ticket_recharge.GetFreebieCfgByGameTypeResp")
	proto.RegisterType((*SetGameTicketFreebieCfgReq)(nil), "game_ticket_recharge.SetGameTicketFreebieCfgReq")
	proto.RegisterType((*SetGameTicketFreebieCfgResp)(nil), "game_ticket_recharge.SetGameTicketFreebieCfgResp")
	proto.RegisterType((*DelGameTicketFreebieCfgReq)(nil), "game_ticket_recharge.DelGameTicketFreebieCfgReq")
	proto.RegisterType((*DelGameTicketFreebieCfgResp)(nil), "game_ticket_recharge.DelGameTicketFreebieCfgResp")
	proto.RegisterType((*GetALLFreebieDetailCfgReq)(nil), "game_ticket_recharge.GetALLFreebieDetailCfgReq")
	proto.RegisterType((*GetALLFreebieDetailCfgResp)(nil), "game_ticket_recharge.GetALLFreebieDetailCfgResp")
	proto.RegisterType((*GetGameTicketFreebieCfgReq)(nil), "game_ticket_recharge.GetGameTicketFreebieCfgReq")
	proto.RegisterType((*GetGameTicketFreebieCfgResp)(nil), "game_ticket_recharge.GetGameTicketFreebieCfgResp")
	proto.RegisterEnum("game_ticket_recharge.MiJingPayStatus", MiJingPayStatus_name, MiJingPayStatus_value)
	proto.RegisterEnum("game_ticket_recharge.GameType", GameType_name, GameType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GameTicketRechargeClient is the client API for GameTicketRecharge service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GameTicketRechargeClient interface {
	// 消耗T豆，购买礼物包裹，同时赠送游戏券
	DoRecharge(ctx context.Context, in *DoRechargeReq, opts ...grpc.CallOption) (*DoRechargeResp, error)
	// 根据GameType找到游戏券购买的配置
	GetFreebieCfgByGameType(ctx context.Context, in *GetFreebieCfgByGameTypeReq, opts ...grpc.CallOption) (*GetFreebieCfgByGameTypeResp, error)
	// 设置游戏券购买的配置
	SetGameTicketFreebieCfg(ctx context.Context, in *SetGameTicketFreebieCfgReq, opts ...grpc.CallOption) (*SetGameTicketFreebieCfgResp, error)
	// 获取所有游戏券购买的配置
	GetALLFreebieDetailCfg(ctx context.Context, in *GetALLFreebieDetailCfgReq, opts ...grpc.CallOption) (*GetALLFreebieDetailCfgResp, error)
	// 删除游戏券购买的配置
	DelGameTicketFreebieCfg(ctx context.Context, in *DelGameTicketFreebieCfgReq, opts ...grpc.CallOption) (*DelGameTicketFreebieCfgResp, error)
	// 根据freebie_id获取配置
	GetGameTicketFreebieCfg(ctx context.Context, in *GetGameTicketFreebieCfgReq, opts ...grpc.CallOption) (*GetGameTicketFreebieCfgResp, error)
	// 发放包裹数据对账
	GetGainPackTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetGainPackOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 发放游戏券数据对账
	GetGainTicketTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetGainTicketOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 使用数据对账
	GetCostTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetCostOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 获取充值配置
	GetMiJingPayConfig(ctx context.Context, in *GetMiJingPayConfigReq, opts ...grpc.CallOption) (*GetMiJingPayConfigResp, error)
	// 获取支付渠道
	GetMiJingPayChannel(ctx context.Context, in *GetMiJingPayChannelReq, opts ...grpc.CallOption) (*GetMiJingPayChannelResp, error)
	// 下单
	MiJingPay(ctx context.Context, in *MiJingPayReq, opts ...grpc.CallOption) (*MiJingPayResp, error)
	// 查询支付结果
	GetMiJingPayResult(ctx context.Context, in *GetMiJingPayResultReq, opts ...grpc.CallOption) (*GetMiJingPayResultResp, error)
	// 支付结果回调
	PayRmbResultNotify(ctx context.Context, in *PayRmbResultNotifyReq, opts ...grpc.CallOption) (*PayRmbResultNotifyResp, error)
	// 新增充值配置
	AddMiJingPayConfig(ctx context.Context, in *AddMiJingPayConfigReq, opts ...grpc.CallOption) (*AddMiJingPayConfigResp, error)
	// 删除充值配置
	DeleteMiJingPayConfig(ctx context.Context, in *DeleteMiJingPayConfigReq, opts ...grpc.CallOption) (*DeleteMiJingPayConfigResp, error)
}

type gameTicketRechargeClient struct {
	cc *grpc.ClientConn
}

func NewGameTicketRechargeClient(cc *grpc.ClientConn) GameTicketRechargeClient {
	return &gameTicketRechargeClient{cc}
}

func (c *gameTicketRechargeClient) DoRecharge(ctx context.Context, in *DoRechargeReq, opts ...grpc.CallOption) (*DoRechargeResp, error) {
	out := new(DoRechargeResp)
	err := c.cc.Invoke(ctx, "/game_ticket_recharge.GameTicketRecharge/DoRecharge", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketRechargeClient) GetFreebieCfgByGameType(ctx context.Context, in *GetFreebieCfgByGameTypeReq, opts ...grpc.CallOption) (*GetFreebieCfgByGameTypeResp, error) {
	out := new(GetFreebieCfgByGameTypeResp)
	err := c.cc.Invoke(ctx, "/game_ticket_recharge.GameTicketRecharge/GetFreebieCfgByGameType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketRechargeClient) SetGameTicketFreebieCfg(ctx context.Context, in *SetGameTicketFreebieCfgReq, opts ...grpc.CallOption) (*SetGameTicketFreebieCfgResp, error) {
	out := new(SetGameTicketFreebieCfgResp)
	err := c.cc.Invoke(ctx, "/game_ticket_recharge.GameTicketRecharge/SetGameTicketFreebieCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketRechargeClient) GetALLFreebieDetailCfg(ctx context.Context, in *GetALLFreebieDetailCfgReq, opts ...grpc.CallOption) (*GetALLFreebieDetailCfgResp, error) {
	out := new(GetALLFreebieDetailCfgResp)
	err := c.cc.Invoke(ctx, "/game_ticket_recharge.GameTicketRecharge/GetALLFreebieDetailCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketRechargeClient) DelGameTicketFreebieCfg(ctx context.Context, in *DelGameTicketFreebieCfgReq, opts ...grpc.CallOption) (*DelGameTicketFreebieCfgResp, error) {
	out := new(DelGameTicketFreebieCfgResp)
	err := c.cc.Invoke(ctx, "/game_ticket_recharge.GameTicketRecharge/DelGameTicketFreebieCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketRechargeClient) GetGameTicketFreebieCfg(ctx context.Context, in *GetGameTicketFreebieCfgReq, opts ...grpc.CallOption) (*GetGameTicketFreebieCfgResp, error) {
	out := new(GetGameTicketFreebieCfgResp)
	err := c.cc.Invoke(ctx, "/game_ticket_recharge.GameTicketRecharge/GetGameTicketFreebieCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketRechargeClient) GetGainPackTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/game_ticket_recharge.GameTicketRecharge/GetGainPackTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketRechargeClient) GetGainPackOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/game_ticket_recharge.GameTicketRecharge/GetGainPackOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketRechargeClient) GetGainTicketTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/game_ticket_recharge.GameTicketRecharge/GetGainTicketTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketRechargeClient) GetGainTicketOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/game_ticket_recharge.GameTicketRecharge/GetGainTicketOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketRechargeClient) GetCostTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/game_ticket_recharge.GameTicketRecharge/GetCostTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketRechargeClient) GetCostOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/game_ticket_recharge.GameTicketRecharge/GetCostOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketRechargeClient) GetMiJingPayConfig(ctx context.Context, in *GetMiJingPayConfigReq, opts ...grpc.CallOption) (*GetMiJingPayConfigResp, error) {
	out := new(GetMiJingPayConfigResp)
	err := c.cc.Invoke(ctx, "/game_ticket_recharge.GameTicketRecharge/GetMiJingPayConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketRechargeClient) GetMiJingPayChannel(ctx context.Context, in *GetMiJingPayChannelReq, opts ...grpc.CallOption) (*GetMiJingPayChannelResp, error) {
	out := new(GetMiJingPayChannelResp)
	err := c.cc.Invoke(ctx, "/game_ticket_recharge.GameTicketRecharge/GetMiJingPayChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketRechargeClient) MiJingPay(ctx context.Context, in *MiJingPayReq, opts ...grpc.CallOption) (*MiJingPayResp, error) {
	out := new(MiJingPayResp)
	err := c.cc.Invoke(ctx, "/game_ticket_recharge.GameTicketRecharge/MiJingPay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketRechargeClient) GetMiJingPayResult(ctx context.Context, in *GetMiJingPayResultReq, opts ...grpc.CallOption) (*GetMiJingPayResultResp, error) {
	out := new(GetMiJingPayResultResp)
	err := c.cc.Invoke(ctx, "/game_ticket_recharge.GameTicketRecharge/GetMiJingPayResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketRechargeClient) PayRmbResultNotify(ctx context.Context, in *PayRmbResultNotifyReq, opts ...grpc.CallOption) (*PayRmbResultNotifyResp, error) {
	out := new(PayRmbResultNotifyResp)
	err := c.cc.Invoke(ctx, "/game_ticket_recharge.GameTicketRecharge/PayRmbResultNotify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketRechargeClient) AddMiJingPayConfig(ctx context.Context, in *AddMiJingPayConfigReq, opts ...grpc.CallOption) (*AddMiJingPayConfigResp, error) {
	out := new(AddMiJingPayConfigResp)
	err := c.cc.Invoke(ctx, "/game_ticket_recharge.GameTicketRecharge/AddMiJingPayConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketRechargeClient) DeleteMiJingPayConfig(ctx context.Context, in *DeleteMiJingPayConfigReq, opts ...grpc.CallOption) (*DeleteMiJingPayConfigResp, error) {
	out := new(DeleteMiJingPayConfigResp)
	err := c.cc.Invoke(ctx, "/game_ticket_recharge.GameTicketRecharge/DeleteMiJingPayConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameTicketRechargeServer is the server API for GameTicketRecharge service.
type GameTicketRechargeServer interface {
	// 消耗T豆，购买礼物包裹，同时赠送游戏券
	DoRecharge(context.Context, *DoRechargeReq) (*DoRechargeResp, error)
	// 根据GameType找到游戏券购买的配置
	GetFreebieCfgByGameType(context.Context, *GetFreebieCfgByGameTypeReq) (*GetFreebieCfgByGameTypeResp, error)
	// 设置游戏券购买的配置
	SetGameTicketFreebieCfg(context.Context, *SetGameTicketFreebieCfgReq) (*SetGameTicketFreebieCfgResp, error)
	// 获取所有游戏券购买的配置
	GetALLFreebieDetailCfg(context.Context, *GetALLFreebieDetailCfgReq) (*GetALLFreebieDetailCfgResp, error)
	// 删除游戏券购买的配置
	DelGameTicketFreebieCfg(context.Context, *DelGameTicketFreebieCfgReq) (*DelGameTicketFreebieCfgResp, error)
	// 根据freebie_id获取配置
	GetGameTicketFreebieCfg(context.Context, *GetGameTicketFreebieCfgReq) (*GetGameTicketFreebieCfgResp, error)
	// 发放包裹数据对账
	GetGainPackTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetGainPackOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 发放游戏券数据对账
	GetGainTicketTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetGainTicketOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 使用数据对账
	GetCostTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetCostOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 获取充值配置
	GetMiJingPayConfig(context.Context, *GetMiJingPayConfigReq) (*GetMiJingPayConfigResp, error)
	// 获取支付渠道
	GetMiJingPayChannel(context.Context, *GetMiJingPayChannelReq) (*GetMiJingPayChannelResp, error)
	// 下单
	MiJingPay(context.Context, *MiJingPayReq) (*MiJingPayResp, error)
	// 查询支付结果
	GetMiJingPayResult(context.Context, *GetMiJingPayResultReq) (*GetMiJingPayResultResp, error)
	// 支付结果回调
	PayRmbResultNotify(context.Context, *PayRmbResultNotifyReq) (*PayRmbResultNotifyResp, error)
	// 新增充值配置
	AddMiJingPayConfig(context.Context, *AddMiJingPayConfigReq) (*AddMiJingPayConfigResp, error)
	// 删除充值配置
	DeleteMiJingPayConfig(context.Context, *DeleteMiJingPayConfigReq) (*DeleteMiJingPayConfigResp, error)
}

func RegisterGameTicketRechargeServer(s *grpc.Server, srv GameTicketRechargeServer) {
	s.RegisterService(&_GameTicketRecharge_serviceDesc, srv)
}

func _GameTicketRecharge_DoRecharge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoRechargeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketRechargeServer).DoRecharge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_recharge.GameTicketRecharge/DoRecharge",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketRechargeServer).DoRecharge(ctx, req.(*DoRechargeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketRecharge_GetFreebieCfgByGameType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFreebieCfgByGameTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketRechargeServer).GetFreebieCfgByGameType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_recharge.GameTicketRecharge/GetFreebieCfgByGameType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketRechargeServer).GetFreebieCfgByGameType(ctx, req.(*GetFreebieCfgByGameTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketRecharge_SetGameTicketFreebieCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGameTicketFreebieCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketRechargeServer).SetGameTicketFreebieCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_recharge.GameTicketRecharge/SetGameTicketFreebieCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketRechargeServer).SetGameTicketFreebieCfg(ctx, req.(*SetGameTicketFreebieCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketRecharge_GetALLFreebieDetailCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetALLFreebieDetailCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketRechargeServer).GetALLFreebieDetailCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_recharge.GameTicketRecharge/GetALLFreebieDetailCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketRechargeServer).GetALLFreebieDetailCfg(ctx, req.(*GetALLFreebieDetailCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketRecharge_DelGameTicketFreebieCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelGameTicketFreebieCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketRechargeServer).DelGameTicketFreebieCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_recharge.GameTicketRecharge/DelGameTicketFreebieCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketRechargeServer).DelGameTicketFreebieCfg(ctx, req.(*DelGameTicketFreebieCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketRecharge_GetGameTicketFreebieCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameTicketFreebieCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketRechargeServer).GetGameTicketFreebieCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_recharge.GameTicketRecharge/GetGameTicketFreebieCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketRechargeServer).GetGameTicketFreebieCfg(ctx, req.(*GetGameTicketFreebieCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketRecharge_GetGainPackTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketRechargeServer).GetGainPackTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_recharge.GameTicketRecharge/GetGainPackTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketRechargeServer).GetGainPackTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketRecharge_GetGainPackOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketRechargeServer).GetGainPackOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_recharge.GameTicketRecharge/GetGainPackOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketRechargeServer).GetGainPackOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketRecharge_GetGainTicketTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketRechargeServer).GetGainTicketTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_recharge.GameTicketRecharge/GetGainTicketTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketRechargeServer).GetGainTicketTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketRecharge_GetGainTicketOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketRechargeServer).GetGainTicketOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_recharge.GameTicketRecharge/GetGainTicketOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketRechargeServer).GetGainTicketOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketRecharge_GetCostTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketRechargeServer).GetCostTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_recharge.GameTicketRecharge/GetCostTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketRechargeServer).GetCostTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketRecharge_GetCostOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketRechargeServer).GetCostOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_recharge.GameTicketRecharge/GetCostOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketRechargeServer).GetCostOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketRecharge_GetMiJingPayConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMiJingPayConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketRechargeServer).GetMiJingPayConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_recharge.GameTicketRecharge/GetMiJingPayConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketRechargeServer).GetMiJingPayConfig(ctx, req.(*GetMiJingPayConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketRecharge_GetMiJingPayChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMiJingPayChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketRechargeServer).GetMiJingPayChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_recharge.GameTicketRecharge/GetMiJingPayChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketRechargeServer).GetMiJingPayChannel(ctx, req.(*GetMiJingPayChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketRecharge_MiJingPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MiJingPayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketRechargeServer).MiJingPay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_recharge.GameTicketRecharge/MiJingPay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketRechargeServer).MiJingPay(ctx, req.(*MiJingPayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketRecharge_GetMiJingPayResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMiJingPayResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketRechargeServer).GetMiJingPayResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_recharge.GameTicketRecharge/GetMiJingPayResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketRechargeServer).GetMiJingPayResult(ctx, req.(*GetMiJingPayResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketRecharge_PayRmbResultNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayRmbResultNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketRechargeServer).PayRmbResultNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_recharge.GameTicketRecharge/PayRmbResultNotify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketRechargeServer).PayRmbResultNotify(ctx, req.(*PayRmbResultNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketRecharge_AddMiJingPayConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMiJingPayConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketRechargeServer).AddMiJingPayConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_recharge.GameTicketRecharge/AddMiJingPayConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketRechargeServer).AddMiJingPayConfig(ctx, req.(*AddMiJingPayConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketRecharge_DeleteMiJingPayConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMiJingPayConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketRechargeServer).DeleteMiJingPayConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_recharge.GameTicketRecharge/DeleteMiJingPayConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketRechargeServer).DeleteMiJingPayConfig(ctx, req.(*DeleteMiJingPayConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GameTicketRecharge_serviceDesc = grpc.ServiceDesc{
	ServiceName: "game_ticket_recharge.GameTicketRecharge",
	HandlerType: (*GameTicketRechargeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DoRecharge",
			Handler:    _GameTicketRecharge_DoRecharge_Handler,
		},
		{
			MethodName: "GetFreebieCfgByGameType",
			Handler:    _GameTicketRecharge_GetFreebieCfgByGameType_Handler,
		},
		{
			MethodName: "SetGameTicketFreebieCfg",
			Handler:    _GameTicketRecharge_SetGameTicketFreebieCfg_Handler,
		},
		{
			MethodName: "GetALLFreebieDetailCfg",
			Handler:    _GameTicketRecharge_GetALLFreebieDetailCfg_Handler,
		},
		{
			MethodName: "DelGameTicketFreebieCfg",
			Handler:    _GameTicketRecharge_DelGameTicketFreebieCfg_Handler,
		},
		{
			MethodName: "GetGameTicketFreebieCfg",
			Handler:    _GameTicketRecharge_GetGameTicketFreebieCfg_Handler,
		},
		{
			MethodName: "GetGainPackTotalCount",
			Handler:    _GameTicketRecharge_GetGainPackTotalCount_Handler,
		},
		{
			MethodName: "GetGainPackOrderIds",
			Handler:    _GameTicketRecharge_GetGainPackOrderIds_Handler,
		},
		{
			MethodName: "GetGainTicketTotalCount",
			Handler:    _GameTicketRecharge_GetGainTicketTotalCount_Handler,
		},
		{
			MethodName: "GetGainTicketOrderIds",
			Handler:    _GameTicketRecharge_GetGainTicketOrderIds_Handler,
		},
		{
			MethodName: "GetCostTotalCount",
			Handler:    _GameTicketRecharge_GetCostTotalCount_Handler,
		},
		{
			MethodName: "GetCostOrderIds",
			Handler:    _GameTicketRecharge_GetCostOrderIds_Handler,
		},
		{
			MethodName: "GetMiJingPayConfig",
			Handler:    _GameTicketRecharge_GetMiJingPayConfig_Handler,
		},
		{
			MethodName: "GetMiJingPayChannel",
			Handler:    _GameTicketRecharge_GetMiJingPayChannel_Handler,
		},
		{
			MethodName: "MiJingPay",
			Handler:    _GameTicketRecharge_MiJingPay_Handler,
		},
		{
			MethodName: "GetMiJingPayResult",
			Handler:    _GameTicketRecharge_GetMiJingPayResult_Handler,
		},
		{
			MethodName: "PayRmbResultNotify",
			Handler:    _GameTicketRecharge_PayRmbResultNotify_Handler,
		},
		{
			MethodName: "AddMiJingPayConfig",
			Handler:    _GameTicketRecharge_AddMiJingPayConfig_Handler,
		},
		{
			MethodName: "DeleteMiJingPayConfig",
			Handler:    _GameTicketRecharge_DeleteMiJingPayConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "game-ticket-recharge/game-ticket-recharge.proto",
}

func init() {
	proto.RegisterFile("game-ticket-recharge/game-ticket-recharge.proto", fileDescriptor_game_ticket_recharge_ba912a26f62fdc59)
}

var fileDescriptor_game_ticket_recharge_ba912a26f62fdc59 = []byte{
	// 1675 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x58, 0x6b, 0x6e, 0xdb, 0xca,
	0x15, 0x36, 0x25, 0xbf, 0x74, 0xe4, 0x87, 0x32, 0x89, 0x13, 0x59, 0x6e, 0x12, 0x97, 0x69, 0x82,
	0x20, 0xad, 0xa5, 0xd8, 0x41, 0x8b, 0x14, 0x7d, 0xc1, 0x91, 0x1d, 0x55, 0x81, 0xad, 0x08, 0x94,
	0x1d, 0x20, 0x45, 0x0b, 0x82, 0x22, 0x47, 0x0a, 0x61, 0x3e, 0xc6, 0x9c, 0x51, 0x0a, 0x15, 0x28,
	0xda, 0x1f, 0xdd, 0x41, 0xfb, 0xa3, 0x2b, 0xe8, 0x0a, 0xba, 0x95, 0xbb, 0x82, 0x7b, 0x71, 0xd7,
	0x71, 0x31, 0x0f, 0x52, 0x94, 0x4c, 0xca, 0xf6, 0xf5, 0x3f, 0x72, 0xce, 0xeb, 0x3b, 0x67, 0xbe,
	0x73, 0x86, 0x1c, 0x68, 0x0c, 0x2d, 0x1f, 0xef, 0x31, 0xd7, 0xbe, 0xc0, 0x6c, 0x2f, 0xc2, 0xf6,
	0x17, 0x2b, 0x1a, 0xe2, 0xcc, 0xc5, 0x3a, 0x89, 0x42, 0x16, 0xa2, 0x07, 0x5c, 0x66, 0x4a, 0x99,
	0x19, 0xcb, 0x6a, 0x4f, 0x23, 0x6c, 0x87, 0x81, 0xed, 0x7a, 0x78, 0xef, 0xeb, 0x41, 0x23, 0xfd,
	0x22, 0xcd, 0x6a, 0xcf, 0x47, 0x81, 0x3b, 0x70, 0xb1, 0xb3, 0x47, 0xac, 0xf1, 0x5e, 0xe4, 0xf7,
	0x1b, 0x33, 0xef, 0x52, 0x4d, 0xff, 0x33, 0x6c, 0x75, 0xad, 0xb1, 0xe1, 0xf7, 0x0d, 0x4c, 0x47,
	0x1e, 0xeb, 0x84, 0xcc, 0x1d, 0x8c, 0x0d, 0x7c, 0x89, 0x9a, 0xb0, 0x16, 0x46, 0x0e, 0x8e, 0xcc,
	0x48, 0x08, 0xaa, 0xda, 0xae, 0xf6, 0xb2, 0x7c, 0xb0, 0x5b, 0x57, 0x6e, 0x4c, 0x62, 0x8d, 0x4d,
	0xee, 0xc6, 0xf0, 0xfb, 0x5d, 0x6b, 0xfc, 0x91, 0xab, 0xb6, 0x83, 0x41, 0x68, 0x94, 0x85, 0x95,
	0xf4, 0xa6, 0x57, 0xe1, 0x61, 0x96, 0x77, 0x4a, 0xf4, 0x0f, 0xb0, 0x75, 0xe8, 0x38, 0xa7, 0xee,
	0x07, 0x37, 0x18, 0x76, 0xad, 0x71, 0x33, 0x0c, 0x06, 0xee, 0x90, 0xc7, 0xdd, 0x87, 0x62, 0x48,
	0xe2, 0x70, 0x4f, 0xeb, 0x59, 0xc9, 0xd7, 0x79, 0x40, 0xc2, 0xdc, 0x30, 0x30, 0xb8, 0x2e, 0x8f,
	0x92, 0xe5, 0x8b, 0x12, 0x7d, 0x1f, 0xaa, 0x47, 0xd8, 0xc3, 0x0c, 0x67, 0x04, 0xda, 0x82, 0xe5,
	0x90, 0x30, 0xd3, 0x75, 0x44, 0xac, 0x75, 0x63, 0x29, 0x24, 0xac, 0xed, 0xe8, 0x3b, 0xb0, 0x9d,
	0x63, 0x42, 0x89, 0xfe, 0x1a, 0xb6, 0x5a, 0x98, 0x65, 0x38, 0x7b, 0x04, 0x2b, 0x21, 0x35, 0xd9,
	0x98, 0x60, 0xe5, 0x6d, 0x39, 0xa4, 0x67, 0x63, 0x82, 0xf5, 0x53, 0x78, 0x98, 0x65, 0x41, 0x09,
	0x7a, 0x03, 0x8b, 0x21, 0x61, 0xb4, 0xaa, 0xed, 0x16, 0x6f, 0x92, 0xa9, 0x50, 0xd6, 0xff, 0x5f,
	0x84, 0x52, 0xb2, 0x86, 0x36, 0xa0, 0x90, 0xc0, 0x2f, 0xb8, 0x4e, 0x1a, 0x45, 0x21, 0x8d, 0x02,
	0x35, 0xe0, 0x81, 0x45, 0x88, 0x87, 0xc5, 0xae, 0x91, 0x28, 0x74, 0x46, 0xb6, 0xc8, 0xbc, 0xb8,
	0xab, 0xbd, 0x2c, 0x19, 0xf7, 0x84, 0xac, 0x6b, 0x8d, 0xbb, 0x52, 0xd2, 0x76, 0xd0, 0x0e, 0x94,
	0x14, 0x14, 0xd7, 0xa9, 0x2e, 0x0a, 0x5f, 0xab, 0x72, 0xa1, 0xed, 0xa0, 0xc7, 0x00, 0x4a, 0x18,
	0x8c, 0xfc, 0xea, 0x92, 0x90, 0x2a, 0xf5, 0xce, 0xc8, 0x47, 0x3f, 0x01, 0x60, 0x66, 0x1f, 0x5b,
	0x81, 0x10, 0x2f, 0x2b, 0xe3, 0x77, 0xd8, 0x0a, 0xb8, 0x74, 0x07, 0x4a, 0x43, 0x77, 0xc0, 0xcc,
	0xc0, 0xf2, 0x71, 0x75, 0x45, 0xc4, 0x5f, 0xe5, 0x0b, 0x1d, 0xcb, 0xc7, 0xe8, 0x05, 0x6c, 0xa6,
	0xcb, 0x30, 0x8a, 0xbc, 0xea, 0xaa, 0x50, 0x59, 0xe7, 0xcb, 0x67, 0x62, 0xf5, 0x3c, 0xf2, 0xd0,
	0x36, 0x08, 0x1b, 0xa1, 0x50, 0x12, 0x0a, 0x2b, 0xfc, 0x9d, 0x8b, 0x1e, 0xc0, 0x12, 0x89, 0x5c,
	0x1b, 0x57, 0x41, 0xee, 0xaa, 0x78, 0xe1, 0x95, 0x21, 0x96, 0x7d, 0xc1, 0xb3, 0x29, 0xcb, 0xca,
	0xf0, 0xd7, 0xb6, 0xc3, 0x3d, 0x09, 0x01, 0x87, 0xba, 0x26, 0x24, 0x42, 0x91, 0x23, 0x7d, 0x0a,
	0xe5, 0x38, 0x4d, 0x8e, 0x75, 0x5d, 0xc4, 0x51, 0x99, 0x0b, 0xb4, 0x2f, 0xa1, 0x92, 0x42, 0x4b,
	0x05, 0x9a, 0x0d, 0xa1, 0xb5, 0x31, 0x81, 0x4b, 0xcf, 0x23, 0x4f, 0xdf, 0x9f, 0x61, 0xc1, 0x17,
	0x2b, 0x08, 0xb0, 0x37, 0x97, 0x38, 0xbf, 0x85, 0x47, 0x99, 0x26, 0x94, 0xa0, 0x9f, 0xc2, 0x1a,
	0xdf, 0x47, 0x5b, 0x2e, 0x49, 0x06, 0x95, 0x8c, 0x32, 0x49, 0xb4, 0xa8, 0xfe, 0x1f, 0x0d, 0xd6,
	0x12, 0x5b, 0x1e, 0xa7, 0x02, 0xc5, 0x51, 0xc2, 0x15, 0xfe, 0x98, 0xe2, 0x7f, 0x21, 0xc5, 0x7f,
	0x9e, 0x75, 0xca, 0xb9, 0x62, 0x08, 0x4c, 0x7c, 0xf3, 0x0d, 0xec, 0x8f, 0x02, 0xc7, 0xc3, 0x31,
	0x35, 0x4a, 0xc6, 0xaa, 0x5c, 0x90, 0xd4, 0x48, 0xd1, 0x6b, 0x49, 0x48, 0x4b, 0x24, 0xa6, 0x95,
	0xfe, 0xbd, 0x06, 0xeb, 0x29, 0x58, 0x94, 0xf0, 0xed, 0x62, 0xe1, 0x05, 0x0e, 0x04, 0xb2, 0x92,
	0x21, 0x5f, 0xd0, 0xae, 0xcc, 0x50, 0x0e, 0x20, 0x85, 0x50, 0xa2, 0x90, 0x83, 0xc6, 0xe1, 0x4c,
	0xb1, 0x3d, 0x57, 0x69, 0x30, 0x97, 0x79, 0x58, 0x41, 0x5d, 0xb7, 0x3d, 0x57, 0x28, 0x9d, 0xf1,
	0x45, 0x9e, 0x37, 0xa3, 0x17, 0x0a, 0x27, 0x7f, 0xe4, 0x09, 0xaa, 0xe4, 0x4c, 0xdf, 0x22, 0x0a,
	0x23, 0xa8, 0xa5, 0x53, 0x8b, 0xf0, 0x1c, 0x62, 0xb7, 0x3e, 0x56, 0xfc, 0x2d, 0x85, 0xd2, 0xa5,
	0x8f, 0xd1, 0x33, 0xd8, 0x60, 0xe6, 0x04, 0x5d, 0x10, 0x2a, 0x16, 0x97, 0x59, 0x3c, 0x07, 0x3b,
	0xa1, 0xfe, 0xeb, 0xe9, 0x41, 0x21, 0xc7, 0x1f, 0xdf, 0x87, 0xd9, 0xcc, 0xb4, 0xd9, 0xcc, 0xf4,
	0xff, 0x6a, 0xd3, 0x64, 0x89, 0x6d, 0x29, 0x41, 0xbf, 0x83, 0x65, 0xca, 0x2c, 0x36, 0xa2, 0xc2,
	0x6c, 0xe3, 0xe0, 0x79, 0xf6, 0xd0, 0x48, 0x4c, 0x7b, 0x42, 0xd9, 0x50, 0x46, 0xe8, 0x0f, 0xb0,
	0xaa, 0xb6, 0x82, 0x56, 0x0b, 0x62, 0xea, 0x3c, 0x9b, 0xeb, 0x40, 0xea, 0x1a, 0x89, 0x91, 0xde,
	0x4e, 0x76, 0x4f, 0xae, 0x20, 0x04, 0x8b, 0xa2, 0x37, 0x64, 0x16, 0xe2, 0x99, 0xaf, 0xb9, 0x76,
	0x18, 0xa8, 0x3d, 0x13, 0xcf, 0x7c, 0x17, 0x78, 0x83, 0x15, 0x25, 0xfb, 0x82, 0x91, 0xaf, 0x7f,
	0xa7, 0x01, 0xbc, 0x8f, 0x30, 0xee, 0xbb, 0xb8, 0x39, 0x18, 0xf2, 0x9a, 0x0f, 0xe4, 0xdb, 0x64,
	0x20, 0x97, 0xd4, 0x4a, 0xdb, 0x49, 0xb7, 0x6f, 0x61, 0xaa, 0x7d, 0x93, 0x6e, 0x2f, 0xa6, 0xbb,
	0x7d, 0xee, 0xf4, 0x9a, 0x69, 0xeb, 0xa5, 0x2b, 0x6d, 0xbd, 0x07, 0xf7, 0x27, 0xe3, 0xcd, 0x24,
	0x38, 0x32, 0x79, 0x30, 0x45, 0x84, 0x4a, 0x32, 0xe7, 0xba, 0x38, 0xea, 0x5a, 0xf6, 0x85, 0x18,
	0x68, 0xa2, 0x88, 0xbc, 0x87, 0x57, 0x64, 0x30, 0xd1, 0xfe, 0xbc, 0x8b, 0xbf, 0xd1, 0xa0, 0xa2,
	0xd2, 0x3c, 0xc2, 0xcc, 0x72, 0x3d, 0x9e, 0xec, 0x5b, 0x58, 0xb6, 0xc5, 0x39, 0x90, 0x1c, 0xaa,
	0x99, 0xbb, 0x30, 0x29, 0x8f, 0xa1, 0xf4, 0x79, 0x1d, 0xc4, 0xdc, 0x9b, 0xd4, 0x81, 0xbf, 0xca,
	0x79, 0x2d, 0x04, 0x02, 0x44, 0x51, 0x81, 0x70, 0x07, 0x4c, 0x4c, 0xff, 0xa9, 0x91, 0xbb, 0x38,
	0x33, 0x72, 0xd3, 0xa3, 0x74, 0x69, 0x7a, 0x94, 0xc6, 0xa2, 0xc9, 0x18, 0x17, 0xa2, 0xce, 0xc8,
	0xd7, 0xff, 0xa7, 0xc1, 0xfa, 0x51, 0x68, 0x28, 0xa8, 0xd9, 0x03, 0x66, 0x7a, 0x4f, 0x0b, 0xb3,
	0x7b, 0x7a, 0x85, 0x13, 0xd3, 0x95, 0x5c, 0x9c, 0xae, 0x24, 0xa7, 0x95, 0x1d, 0x52, 0xa6, 0x8e,
	0x1b, 0xf1, 0xcc, 0x07, 0x61, 0x38, 0x62, 0xd4, 0x75, 0x70, 0xba, 0x57, 0xcb, 0x6a, 0x8d, 0x77,
	0xab, 0xfe, 0x0f, 0xd8, 0x48, 0xe3, 0xa4, 0x24, 0xcd, 0x25, 0x6d, 0x8a, 0x4b, 0x53, 0xac, 0x29,
	0xcc, 0x3d, 0xf3, 0x8a, 0xb3, 0x67, 0x5e, 0x15, 0x56, 0xfa, 0x96, 0x67, 0x05, 0x76, 0x0c, 0x3c,
	0x7e, 0xd5, 0xdf, 0x42, 0xad, 0x85, 0xd9, 0x64, 0x2f, 0xdf, 0x8d, 0x5b, 0x2a, 0x25, 0x5e, 0xb5,
	0x1a, 0x24, 0x19, 0x2a, 0x34, 0x13, 0xee, 0xfc, 0x4b, 0x83, 0x9d, 0x5c, 0x53, 0x4a, 0xd0, 0xef,
	0x67, 0x68, 0xf4, 0x62, 0x2e, 0x8d, 0x12, 0xfa, 0x25, 0x64, 0x7a, 0x0e, 0x1b, 0xb1, 0x92, 0xe9,
	0xb9, 0xbe, 0xcb, 0x54, 0xd2, 0xeb, 0xf1, 0xea, 0x09, 0x5f, 0xd4, 0x3f, 0x41, 0xad, 0x87, 0x59,
	0x2b, 0x39, 0xd0, 0x52, 0xb4, 0xc4, 0x97, 0x3f, 0x9e, 0xcb, 0xfa, 0x63, 0xd8, 0xc9, 0xf5, 0x4b,
	0x89, 0xfe, 0x1b, 0xa8, 0x1d, 0x61, 0x2f, 0x2f, 0xec, 0xfc, 0x79, 0xc1, 0x7d, 0xe7, 0x1a, 0x53,
	0xc2, 0xbf, 0xf1, 0x5a, 0x98, 0x1d, 0x9e, 0x9c, 0x5c, 0xa9, 0x0d, 0xbe, 0xd4, 0xb1, 0xd8, 0xb0,
	0x4c, 0x21, 0x25, 0xa8, 0x05, 0x65, 0x89, 0xdf, 0xf4, 0x5c, 0xca, 0xd4, 0xc7, 0xdb, 0x4d, 0x2b,
	0x0f, 0xd2, 0xf4, 0xc4, 0xa5, 0x8c, 0xe7, 0xd7, 0xca, 0x2f, 0xeb, 0x35, 0xf9, 0xfd, 0x45, 0x30,
	0x23, 0x2f, 0xbf, 0xbb, 0x32, 0xe3, 0xd5, 0xbf, 0x35, 0xd8, 0x9c, 0x39, 0x44, 0x90, 0x0e, 0x4f,
	0x4e, 0xdb, 0xe6, 0x87, 0x76, 0xa7, 0x65, 0x76, 0x0f, 0x3f, 0x9b, 0xbd, 0xb3, 0xc3, 0xb3, 0xf3,
	0x9e, 0x79, 0xde, 0xe9, 0x75, 0x8f, 0x9b, 0xed, 0xf7, 0xed, 0xe3, 0xa3, 0xca, 0x02, 0x7a, 0x0c,
	0xdb, 0x19, 0x3a, 0xdd, 0xc3, 0xcf, 0xed, 0x4e, 0xab, 0xa2, 0xa1, 0x27, 0x50, 0xcb, 0x10, 0xf7,
	0xce, 0x9b, 0xcd, 0xe3, 0x5e, 0xaf, 0x52, 0xc8, 0x31, 0x7f, 0x7f, 0xd8, 0x3e, 0x39, 0x3e, 0xaa,
	0x14, 0x5f, 0xed, 0xc3, 0x6a, 0xcc, 0x7f, 0x74, 0x1f, 0x36, 0xcf, 0x83, 0x8b, 0x20, 0xfc, 0x6b,
	0x10, 0x2f, 0x55, 0x16, 0x50, 0x05, 0xd6, 0x4e, 0xc7, 0x94, 0xe1, 0x68, 0x6c, 0x60, 0xcb, 0xf3,
	0x2b, 0xda, 0xc1, 0xb7, 0x9b, 0x80, 0x26, 0x55, 0x8a, 0xc7, 0x00, 0xfa, 0x0c, 0x30, 0x19, 0x0a,
	0x28, 0xe7, 0x10, 0x9c, 0x1a, 0x6f, 0xb5, 0x9f, 0x5d, 0xaf, 0x44, 0x89, 0xbe, 0x80, 0xfe, 0xa9,
	0x89, 0xef, 0xb6, 0xac, 0xa6, 0x45, 0xaf, 0xb3, 0x7d, 0xe4, 0x8f, 0x87, 0xda, 0xfe, 0x2d, 0x2d,
	0x12, 0x08, 0x39, 0x9d, 0x95, 0x07, 0x21, 0xbf, 0xc1, 0xf3, 0x20, 0xcc, 0x6b, 0xdd, 0x05, 0xf4,
	0x77, 0xf1, 0x09, 0x93, 0xd1, 0x43, 0xa8, 0x91, 0x9b, 0x51, 0x76, 0x3b, 0xd6, 0x5e, 0xdf, 0xce,
	0x20, 0xa9, 0x40, 0x4e, 0xff, 0xe7, 0x55, 0x20, 0x7f, 0xd6, 0xe4, 0x55, 0x60, 0xde, 0x80, 0x49,
	0x78, 0x70, 0x1b, 0x08, 0xad, 0x5b, 0x6f, 0x42, 0x6b, 0xee, 0x26, 0x9c, 0x88, 0x6f, 0xd0, 0x96,
	0xe5, 0x06, 0xfc, 0x3b, 0xe5, 0x2c, 0x64, 0x96, 0xd7, 0x0c, 0x47, 0x01, 0x43, 0xdb, 0x75, 0x23,
	0xbe, 0x2f, 0xf8, 0x74, 0x50, 0xe7, 0xc7, 0xa4, 0x61, 0x05, 0x92, 0xe6, 0x0f, 0xa7, 0x44, 0x42,
	0x5d, 0x79, 0x3b, 0x85, 0xfb, 0x29, 0x6f, 0xea, 0x63, 0x95, 0xce, 0xf3, 0x35, 0x2d, 0x8a, 0x2d,
	0x94, 0xbb, 0x8e, 0x2a, 0x8f, 0x1b, 0x48, 0xf4, 0x77, 0x85, 0xf7, 0x31, 0x49, 0x56, 0xfa, 0xbb,
	0x33, 0xc0, 0x3f, 0xc2, 0xbd, 0x16, 0x66, 0xcd, 0x90, 0xde, 0x19, 0x5a, 0x1b, 0x36, 0x95, 0xa7,
	0x3b, 0x83, 0xba, 0x04, 0x74, 0xf5, 0x36, 0x01, 0xfd, 0x3c, 0x97, 0x1d, 0x57, 0x6f, 0x2a, 0x6a,
	0xbf, 0xb8, 0xb9, 0xb2, 0x08, 0xc9, 0xc4, 0xbe, 0xcf, 0xfe, 0x87, 0xa2, 0x9b, 0xb8, 0x49, 0xfe,
	0x72, 0x6b, 0x7b, 0xb7, 0xd0, 0x16, 0x51, 0x3f, 0x41, 0x29, 0x91, 0x20, 0xfd, 0x9a, 0xdf, 0x1c,
	0x1e, 0xe1, 0xd9, 0xb5, 0x3a, 0x59, 0x05, 0x94, 0xff, 0x56, 0x37, 0x29, 0x60, 0xf2, 0x07, 0x77,
	0x93, 0x02, 0x4e, 0x7e, 0xd9, 0x64, 0xc8, 0xab, 0x77, 0x60, 0x79, 0x21, 0x33, 0xef, 0xe2, 0xf2,
	0x42, 0xe6, 0x5c, 0xad, 0x89, 0x90, 0x57, 0x2f, 0xc4, 0xf2, 0x42, 0x66, 0x5e, 0xc3, 0xe5, 0x85,
	0xcc, 0xb9, 0x67, 0x5b, 0x40, 0x7f, 0x83, 0xad, 0xcc, 0x6b, 0x33, 0x54, 0xcf, 0x9d, 0x9e, 0x99,
	0xd7, 0x72, 0xb5, 0xc6, 0xad, 0xf4, 0x79, 0xec, 0x77, 0x6f, 0xff, 0xf4, 0xab, 0x61, 0xe8, 0x59,
	0xc1, 0xb0, 0xfe, 0xcb, 0x03, 0xc6, 0xea, 0x76, 0xe8, 0x37, 0xc4, 0xe5, 0xa6, 0x1d, 0x7a, 0x0d,
	0x8a, 0xa3, 0xaf, 0xae, 0x8d, 0x69, 0xe6, 0x0d, 0x6b, 0x7f, 0x59, 0xe8, 0xbd, 0xf9, 0x21, 0x00,
	0x00, 0xff, 0xff, 0x75, 0x06, 0x71, 0x4a, 0x95, 0x15, 0x00, 0x00,
}
