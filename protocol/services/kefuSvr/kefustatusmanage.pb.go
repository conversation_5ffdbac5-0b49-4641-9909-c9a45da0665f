// Code generated by protoc-gen-gogo.
// source: src/kefuSvr/kefustatusmanage.proto
// DO NOT EDIT!

/*
	Package KefuStatusManage is a generated protocol buffer package.

	It is generated from these files:
		src/kefuSvr/kefustatusmanage.proto

	It has these top-level messages:
		KefuStatus
		GetAllKefuStatusResp
		SetKefuStatusReq
		Msg
		AddMsgReq
		GetMsgReq
		GetMsgResp
		GetRelationUserToKefuReq
		GetRelationUserToKefuResp
*/
package KefuStatusManage

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type KEFU_STATUS int32

const (
	KEFU_STATUS_OFF KEFU_STATUS = 0
	KEFU_STATUS_ON  KEFU_STATUS = 1
)

var KEFU_STATUS_name = map[int32]string{
	0: "OFF",
	1: "ON",
}
var KEFU_STATUS_value = map[string]int32{
	"OFF": 0,
	"ON":  1,
}

func (x KEFU_STATUS) Enum() *KEFU_STATUS {
	p := new(KEFU_STATUS)
	*p = x
	return p
}
func (x KEFU_STATUS) String() string {
	return proto.EnumName(KEFU_STATUS_name, int32(x))
}
func (x *KEFU_STATUS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(KEFU_STATUS_value, data, "KEFU_STATUS")
	if err != nil {
		return err
	}
	*x = KEFU_STATUS(value)
	return nil
}
func (KEFU_STATUS) EnumDescriptor() ([]byte, []int) { return fileDescriptorKefustatusmanage, []int{0} }

type KefuStatus struct {
	CmsId       uint32 `protobuf:"varint,1,req,name=cms_id,json=cmsId" json:"cms_id"`
	Uid         uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	Ttid        string `protobuf:"bytes,3,req,name=ttid" json:"ttid"`
	ServiceName string `protobuf:"bytes,4,req,name=service_name,json=serviceName" json:"service_name"`
	Name        string `protobuf:"bytes,5,req,name=name" json:"name"`
	Status      uint32 `protobuf:"varint,6,req,name=status" json:"status"`
	Ttusername  string `protobuf:"bytes,7,opt,name=ttusername" json:"ttusername"`
}

func (m *KefuStatus) Reset()                    { *m = KefuStatus{} }
func (m *KefuStatus) String() string            { return proto.CompactTextString(m) }
func (*KefuStatus) ProtoMessage()               {}
func (*KefuStatus) Descriptor() ([]byte, []int) { return fileDescriptorKefustatusmanage, []int{0} }

func (m *KefuStatus) GetCmsId() uint32 {
	if m != nil {
		return m.CmsId
	}
	return 0
}

func (m *KefuStatus) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *KefuStatus) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *KefuStatus) GetServiceName() string {
	if m != nil {
		return m.ServiceName
	}
	return ""
}

func (m *KefuStatus) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *KefuStatus) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *KefuStatus) GetTtusername() string {
	if m != nil {
		return m.Ttusername
	}
	return ""
}

type GetAllKefuStatusResp struct {
	KefuStatusList []*KefuStatus `protobuf:"bytes,1,rep,name=kefu_status_list,json=kefuStatusList" json:"kefu_status_list,omitempty"`
}

func (m *GetAllKefuStatusResp) Reset()         { *m = GetAllKefuStatusResp{} }
func (m *GetAllKefuStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetAllKefuStatusResp) ProtoMessage()    {}
func (*GetAllKefuStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorKefustatusmanage, []int{1}
}

func (m *GetAllKefuStatusResp) GetKefuStatusList() []*KefuStatus {
	if m != nil {
		return m.KefuStatusList
	}
	return nil
}

type SetKefuStatusReq struct {
	Status *KefuStatus `protobuf:"bytes,1,req,name=status" json:"status,omitempty"`
	Tag    uint32      `protobuf:"varint,2,req,name=tag" json:"tag"`
}

func (m *SetKefuStatusReq) Reset()                    { *m = SetKefuStatusReq{} }
func (m *SetKefuStatusReq) String() string            { return proto.CompactTextString(m) }
func (*SetKefuStatusReq) ProtoMessage()               {}
func (*SetKefuStatusReq) Descriptor() ([]byte, []int) { return fileDescriptorKefustatusmanage, []int{2} }

func (m *SetKefuStatusReq) GetStatus() *KefuStatus {
	if m != nil {
		return m.Status
	}
	return nil
}

func (m *SetKefuStatusReq) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

type Msg struct {
	FromId     uint32 `protobuf:"varint,1,req,name=from_id,json=fromId" json:"from_id"`
	ToId       uint32 `protobuf:"varint,2,req,name=to_id,json=toId" json:"to_id"`
	Content    []byte `protobuf:"bytes,3,req,name=content" json:"content"`
	SerDbMsgId uint32 `protobuf:"varint,4,opt,name=ser_db_msgId,json=serDbMsgId" json:"ser_db_msgId"`
}

func (m *Msg) Reset()                    { *m = Msg{} }
func (m *Msg) String() string            { return proto.CompactTextString(m) }
func (*Msg) ProtoMessage()               {}
func (*Msg) Descriptor() ([]byte, []int) { return fileDescriptorKefustatusmanage, []int{3} }

func (m *Msg) GetFromId() uint32 {
	if m != nil {
		return m.FromId
	}
	return 0
}

func (m *Msg) GetToId() uint32 {
	if m != nil {
		return m.ToId
	}
	return 0
}

func (m *Msg) GetContent() []byte {
	if m != nil {
		return m.Content
	}
	return nil
}

func (m *Msg) GetSerDbMsgId() uint32 {
	if m != nil {
		return m.SerDbMsgId
	}
	return 0
}

type AddMsgReq struct {
	Msg *Msg `protobuf:"bytes,1,req,name=msg" json:"msg,omitempty"`
}

func (m *AddMsgReq) Reset()                    { *m = AddMsgReq{} }
func (m *AddMsgReq) String() string            { return proto.CompactTextString(m) }
func (*AddMsgReq) ProtoMessage()               {}
func (*AddMsgReq) Descriptor() ([]byte, []int) { return fileDescriptorKefustatusmanage, []int{4} }

func (m *AddMsgReq) GetMsg() *Msg {
	if m != nil {
		return m.Msg
	}
	return nil
}

type GetMsgReq struct {
	ClientId   uint32 `protobuf:"varint,1,req,name=client_id,json=clientId" json:"client_id"`
	SerDbMsgId uint32 `protobuf:"varint,2,req,name=ser_db_msgId,json=serDbMsgId" json:"ser_db_msgId"`
	GetMsgSize uint32 `protobuf:"varint,3,req,name=get_msg_size,json=getMsgSize" json:"get_msg_size"`
}

func (m *GetMsgReq) Reset()                    { *m = GetMsgReq{} }
func (m *GetMsgReq) String() string            { return proto.CompactTextString(m) }
func (*GetMsgReq) ProtoMessage()               {}
func (*GetMsgReq) Descriptor() ([]byte, []int) { return fileDescriptorKefustatusmanage, []int{5} }

func (m *GetMsgReq) GetClientId() uint32 {
	if m != nil {
		return m.ClientId
	}
	return 0
}

func (m *GetMsgReq) GetSerDbMsgId() uint32 {
	if m != nil {
		return m.SerDbMsgId
	}
	return 0
}

func (m *GetMsgReq) GetGetMsgSize() uint32 {
	if m != nil {
		return m.GetMsgSize
	}
	return 0
}

type GetMsgResp struct {
	MsgList []*Msg `protobuf:"bytes,1,rep,name=msg_list,json=msgList" json:"msg_list,omitempty"`
}

func (m *GetMsgResp) Reset()                    { *m = GetMsgResp{} }
func (m *GetMsgResp) String() string            { return proto.CompactTextString(m) }
func (*GetMsgResp) ProtoMessage()               {}
func (*GetMsgResp) Descriptor() ([]byte, []int) { return fileDescriptorKefustatusmanage, []int{6} }

func (m *GetMsgResp) GetMsgList() []*Msg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

type GetRelationUserToKefuReq struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	KefuUid uint32 `protobuf:"varint,2,opt,name=kefu_uid,json=kefuUid" json:"kefu_uid"`
}

func (m *GetRelationUserToKefuReq) Reset()         { *m = GetRelationUserToKefuReq{} }
func (m *GetRelationUserToKefuReq) String() string { return proto.CompactTextString(m) }
func (*GetRelationUserToKefuReq) ProtoMessage()    {}
func (*GetRelationUserToKefuReq) Descriptor() ([]byte, []int) {
	return fileDescriptorKefustatusmanage, []int{7}
}

func (m *GetRelationUserToKefuReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRelationUserToKefuReq) GetKefuUid() uint32 {
	if m != nil {
		return m.KefuUid
	}
	return 0
}

type GetRelationUserToKefuResp struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	KefuUid      uint32 `protobuf:"varint,2,req,name=kefu_uid,json=kefuUid" json:"kefu_uid"`
	KefuUsername string `protobuf:"bytes,3,req,name=kefu_username,json=kefuUsername" json:"kefu_username"`
}

func (m *GetRelationUserToKefuResp) Reset()         { *m = GetRelationUserToKefuResp{} }
func (m *GetRelationUserToKefuResp) String() string { return proto.CompactTextString(m) }
func (*GetRelationUserToKefuResp) ProtoMessage()    {}
func (*GetRelationUserToKefuResp) Descriptor() ([]byte, []int) {
	return fileDescriptorKefustatusmanage, []int{8}
}

func (m *GetRelationUserToKefuResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRelationUserToKefuResp) GetKefuUid() uint32 {
	if m != nil {
		return m.KefuUid
	}
	return 0
}

func (m *GetRelationUserToKefuResp) GetKefuUsername() string {
	if m != nil {
		return m.KefuUsername
	}
	return ""
}

func init() {
	proto.RegisterType((*KefuStatus)(nil), "KefuStatusManage.kefuStatus")
	proto.RegisterType((*GetAllKefuStatusResp)(nil), "KefuStatusManage.GetAllKefuStatusResp")
	proto.RegisterType((*SetKefuStatusReq)(nil), "KefuStatusManage.SetKefuStatusReq")
	proto.RegisterType((*Msg)(nil), "KefuStatusManage.Msg")
	proto.RegisterType((*AddMsgReq)(nil), "KefuStatusManage.AddMsgReq")
	proto.RegisterType((*GetMsgReq)(nil), "KefuStatusManage.GetMsgReq")
	proto.RegisterType((*GetMsgResp)(nil), "KefuStatusManage.GetMsgResp")
	proto.RegisterType((*GetRelationUserToKefuReq)(nil), "KefuStatusManage.GetRelationUserToKefuReq")
	proto.RegisterType((*GetRelationUserToKefuResp)(nil), "KefuStatusManage.GetRelationUserToKefuResp")
	proto.RegisterEnum("KefuStatusManage.KEFU_STATUS", KEFU_STATUS_name, KEFU_STATUS_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for KefuStatusManage service

type KefuStatusManageClient interface {
	// 设置客服状态
	SetKefuStatus(ctx context.Context, in *SetKefuStatusReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取全量客服状态列表
	GetAllKefuStatus(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetAllKefuStatusResp, error)
	// 客服消息全存储
	AddMsg2Db(ctx context.Context, in *AddMsgReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetMsgFromDb(ctx context.Context, in *GetMsgReq, opts ...grpc.CallOption) (*GetMsgResp, error)
	// 获取用户与客服的绑定关系
	GetRelationUserToKefu(ctx context.Context, in *GetRelationUserToKefuReq, opts ...grpc.CallOption) (*GetRelationUserToKefuResp, error)
}

type kefuStatusManageClient struct {
	cc *grpc.ClientConn
}

func NewKefuStatusManageClient(cc *grpc.ClientConn) KefuStatusManageClient {
	return &kefuStatusManageClient{cc}
}

func (c *kefuStatusManageClient) SetKefuStatus(ctx context.Context, in *SetKefuStatusReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/KefuStatusManage.KefuStatusManage/SetKefuStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *kefuStatusManageClient) GetAllKefuStatus(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetAllKefuStatusResp, error) {
	out := new(GetAllKefuStatusResp)
	err := grpc.Invoke(ctx, "/KefuStatusManage.KefuStatusManage/GetAllKefuStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *kefuStatusManageClient) AddMsg2Db(ctx context.Context, in *AddMsgReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/KefuStatusManage.KefuStatusManage/AddMsg2Db", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *kefuStatusManageClient) GetMsgFromDb(ctx context.Context, in *GetMsgReq, opts ...grpc.CallOption) (*GetMsgResp, error) {
	out := new(GetMsgResp)
	err := grpc.Invoke(ctx, "/KefuStatusManage.KefuStatusManage/GetMsgFromDb", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *kefuStatusManageClient) GetRelationUserToKefu(ctx context.Context, in *GetRelationUserToKefuReq, opts ...grpc.CallOption) (*GetRelationUserToKefuResp, error) {
	out := new(GetRelationUserToKefuResp)
	err := grpc.Invoke(ctx, "/KefuStatusManage.KefuStatusManage/GetRelationUserToKefu", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for KefuStatusManage service

type KefuStatusManageServer interface {
	// 设置客服状态
	SetKefuStatus(context.Context, *SetKefuStatusReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取全量客服状态列表
	GetAllKefuStatus(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*GetAllKefuStatusResp, error)
	// 客服消息全存储
	AddMsg2Db(context.Context, *AddMsgReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetMsgFromDb(context.Context, *GetMsgReq) (*GetMsgResp, error)
	// 获取用户与客服的绑定关系
	GetRelationUserToKefu(context.Context, *GetRelationUserToKefuReq) (*GetRelationUserToKefuResp, error)
}

func RegisterKefuStatusManageServer(s *grpc.Server, srv KefuStatusManageServer) {
	s.RegisterService(&_KefuStatusManage_serviceDesc, srv)
}

func _KefuStatusManage_SetKefuStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetKefuStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KefuStatusManageServer).SetKefuStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/KefuStatusManage.KefuStatusManage/SetKefuStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KefuStatusManageServer).SetKefuStatus(ctx, req.(*SetKefuStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KefuStatusManage_GetAllKefuStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KefuStatusManageServer).GetAllKefuStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/KefuStatusManage.KefuStatusManage/GetAllKefuStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KefuStatusManageServer).GetAllKefuStatus(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _KefuStatusManage_AddMsg2Db_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KefuStatusManageServer).AddMsg2Db(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/KefuStatusManage.KefuStatusManage/AddMsg2Db",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KefuStatusManageServer).AddMsg2Db(ctx, req.(*AddMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KefuStatusManage_GetMsgFromDb_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KefuStatusManageServer).GetMsgFromDb(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/KefuStatusManage.KefuStatusManage/GetMsgFromDb",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KefuStatusManageServer).GetMsgFromDb(ctx, req.(*GetMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KefuStatusManage_GetRelationUserToKefu_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRelationUserToKefuReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KefuStatusManageServer).GetRelationUserToKefu(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/KefuStatusManage.KefuStatusManage/GetRelationUserToKefu",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KefuStatusManageServer).GetRelationUserToKefu(ctx, req.(*GetRelationUserToKefuReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _KefuStatusManage_serviceDesc = grpc.ServiceDesc{
	ServiceName: "KefuStatusManage.KefuStatusManage",
	HandlerType: (*KefuStatusManageServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetKefuStatus",
			Handler:    _KefuStatusManage_SetKefuStatus_Handler,
		},
		{
			MethodName: "GetAllKefuStatus",
			Handler:    _KefuStatusManage_GetAllKefuStatus_Handler,
		},
		{
			MethodName: "AddMsg2Db",
			Handler:    _KefuStatusManage_AddMsg2Db_Handler,
		},
		{
			MethodName: "GetMsgFromDb",
			Handler:    _KefuStatusManage_GetMsgFromDb_Handler,
		},
		{
			MethodName: "GetRelationUserToKefu",
			Handler:    _KefuStatusManage_GetRelationUserToKefu_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/kefuSvr/kefustatusmanage.proto",
}

func (m *KefuStatus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KefuStatus) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKefustatusmanage(dAtA, i, uint64(m.CmsId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKefustatusmanage(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintKefustatusmanage(dAtA, i, uint64(len(m.Ttid)))
	i += copy(dAtA[i:], m.Ttid)
	dAtA[i] = 0x22
	i++
	i = encodeVarintKefustatusmanage(dAtA, i, uint64(len(m.ServiceName)))
	i += copy(dAtA[i:], m.ServiceName)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintKefustatusmanage(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x30
	i++
	i = encodeVarintKefustatusmanage(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintKefustatusmanage(dAtA, i, uint64(len(m.Ttusername)))
	i += copy(dAtA[i:], m.Ttusername)
	return i, nil
}

func (m *GetAllKefuStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllKefuStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.KefuStatusList) > 0 {
		for _, msg := range m.KefuStatusList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintKefustatusmanage(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetKefuStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetKefuStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Status == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("status")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintKefustatusmanage(dAtA, i, uint64(m.Status.Size()))
		n1, err := m.Status.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintKefustatusmanage(dAtA, i, uint64(m.Tag))
	return i, nil
}

func (m *Msg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Msg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKefustatusmanage(dAtA, i, uint64(m.FromId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKefustatusmanage(dAtA, i, uint64(m.ToId))
	if m.Content != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintKefustatusmanage(dAtA, i, uint64(len(m.Content)))
		i += copy(dAtA[i:], m.Content)
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintKefustatusmanage(dAtA, i, uint64(m.SerDbMsgId))
	return i, nil
}

func (m *AddMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Msg == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("msg")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintKefustatusmanage(dAtA, i, uint64(m.Msg.Size()))
		n2, err := m.Msg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *GetMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKefustatusmanage(dAtA, i, uint64(m.ClientId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKefustatusmanage(dAtA, i, uint64(m.SerDbMsgId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintKefustatusmanage(dAtA, i, uint64(m.GetMsgSize))
	return i, nil
}

func (m *GetMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MsgList) > 0 {
		for _, msg := range m.MsgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintKefustatusmanage(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetRelationUserToKefuReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRelationUserToKefuReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKefustatusmanage(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKefustatusmanage(dAtA, i, uint64(m.KefuUid))
	return i, nil
}

func (m *GetRelationUserToKefuResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRelationUserToKefuResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKefustatusmanage(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKefustatusmanage(dAtA, i, uint64(m.KefuUid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintKefustatusmanage(dAtA, i, uint64(len(m.KefuUsername)))
	i += copy(dAtA[i:], m.KefuUsername)
	return i, nil
}

func encodeFixed64Kefustatusmanage(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Kefustatusmanage(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintKefustatusmanage(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *KefuStatus) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKefustatusmanage(uint64(m.CmsId))
	n += 1 + sovKefustatusmanage(uint64(m.Uid))
	l = len(m.Ttid)
	n += 1 + l + sovKefustatusmanage(uint64(l))
	l = len(m.ServiceName)
	n += 1 + l + sovKefustatusmanage(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovKefustatusmanage(uint64(l))
	n += 1 + sovKefustatusmanage(uint64(m.Status))
	l = len(m.Ttusername)
	n += 1 + l + sovKefustatusmanage(uint64(l))
	return n
}

func (m *GetAllKefuStatusResp) Size() (n int) {
	var l int
	_ = l
	if len(m.KefuStatusList) > 0 {
		for _, e := range m.KefuStatusList {
			l = e.Size()
			n += 1 + l + sovKefustatusmanage(uint64(l))
		}
	}
	return n
}

func (m *SetKefuStatusReq) Size() (n int) {
	var l int
	_ = l
	if m.Status != nil {
		l = m.Status.Size()
		n += 1 + l + sovKefustatusmanage(uint64(l))
	}
	n += 1 + sovKefustatusmanage(uint64(m.Tag))
	return n
}

func (m *Msg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKefustatusmanage(uint64(m.FromId))
	n += 1 + sovKefustatusmanage(uint64(m.ToId))
	if m.Content != nil {
		l = len(m.Content)
		n += 1 + l + sovKefustatusmanage(uint64(l))
	}
	n += 1 + sovKefustatusmanage(uint64(m.SerDbMsgId))
	return n
}

func (m *AddMsgReq) Size() (n int) {
	var l int
	_ = l
	if m.Msg != nil {
		l = m.Msg.Size()
		n += 1 + l + sovKefustatusmanage(uint64(l))
	}
	return n
}

func (m *GetMsgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKefustatusmanage(uint64(m.ClientId))
	n += 1 + sovKefustatusmanage(uint64(m.SerDbMsgId))
	n += 1 + sovKefustatusmanage(uint64(m.GetMsgSize))
	return n
}

func (m *GetMsgResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MsgList) > 0 {
		for _, e := range m.MsgList {
			l = e.Size()
			n += 1 + l + sovKefustatusmanage(uint64(l))
		}
	}
	return n
}

func (m *GetRelationUserToKefuReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKefustatusmanage(uint64(m.Uid))
	n += 1 + sovKefustatusmanage(uint64(m.KefuUid))
	return n
}

func (m *GetRelationUserToKefuResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKefustatusmanage(uint64(m.Uid))
	n += 1 + sovKefustatusmanage(uint64(m.KefuUid))
	l = len(m.KefuUsername)
	n += 1 + l + sovKefustatusmanage(uint64(l))
	return n
}

func sovKefustatusmanage(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozKefustatusmanage(x uint64) (n int) {
	return sovKefustatusmanage(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *KefuStatus) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKefustatusmanage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: kefuStatus: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: kefuStatus: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CmsId", wireType)
			}
			m.CmsId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CmsId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ttid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKefustatusmanage
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ttid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServiceName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKefustatusmanage
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ServiceName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKefustatusmanage
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ttusername", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKefustatusmanage
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ttusername = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKefustatusmanage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKefustatusmanage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cms_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ttid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("service_name")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllKefuStatusResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKefustatusmanage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllKefuStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllKefuStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KefuStatusList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKefustatusmanage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.KefuStatusList = append(m.KefuStatusList, &KefuStatus{})
			if err := m.KefuStatusList[len(m.KefuStatusList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKefustatusmanage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKefustatusmanage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetKefuStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKefustatusmanage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetKefuStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetKefuStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKefustatusmanage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Status == nil {
				m.Status = &KefuStatus{}
			}
			if err := m.Status.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Tag", wireType)
			}
			m.Tag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Tag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipKefustatusmanage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKefustatusmanage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Msg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKefustatusmanage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Msg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Msg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromId", wireType)
			}
			m.FromId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToId", wireType)
			}
			m.ToId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthKefustatusmanage
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = append(m.Content[:0], dAtA[iNdEx:postIndex]...)
			if m.Content == nil {
				m.Content = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerDbMsgId", wireType)
			}
			m.SerDbMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SerDbMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKefustatusmanage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKefustatusmanage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("from_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("to_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKefustatusmanage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKefustatusmanage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Msg == nil {
				m.Msg = &Msg{}
			}
			if err := m.Msg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipKefustatusmanage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKefustatusmanage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKefustatusmanage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientId", wireType)
			}
			m.ClientId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerDbMsgId", wireType)
			}
			m.SerDbMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SerDbMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GetMsgSize", wireType)
			}
			m.GetMsgSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GetMsgSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipKefustatusmanage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKefustatusmanage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("client_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ser_db_msgId")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("get_msg_size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMsgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKefustatusmanage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKefustatusmanage
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MsgList = append(m.MsgList, &Msg{})
			if err := m.MsgList[len(m.MsgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKefustatusmanage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKefustatusmanage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRelationUserToKefuReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKefustatusmanage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRelationUserToKefuReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRelationUserToKefuReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KefuUid", wireType)
			}
			m.KefuUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KefuUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKefustatusmanage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKefustatusmanage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRelationUserToKefuResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKefustatusmanage
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRelationUserToKefuResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRelationUserToKefuResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KefuUid", wireType)
			}
			m.KefuUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KefuUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KefuUsername", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKefustatusmanage
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.KefuUsername = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipKefustatusmanage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKefustatusmanage
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("kefu_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("kefu_username")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipKefustatusmanage(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowKefustatusmanage
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowKefustatusmanage
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthKefustatusmanage
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowKefustatusmanage
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipKefustatusmanage(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthKefustatusmanage = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowKefustatusmanage   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/kefuSvr/kefustatusmanage.proto", fileDescriptorKefustatusmanage) }

var fileDescriptorKefustatusmanage = []byte{
	// 912 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x55, 0xcd, 0x6e, 0xdb, 0x46,
	0x17, 0x15, 0x45, 0x59, 0xb2, 0xae, 0xed, 0x7c, 0xc2, 0xe0, 0x4b, 0xa0, 0x28, 0xae, 0xc2, 0x12,
	0x41, 0xe2, 0xa6, 0xa5, 0x5c, 0x18, 0x59, 0x11, 0x04, 0x01, 0x1b, 0x8e, 0x5c, 0xc1, 0x75, 0x52,
	0x58, 0xd6, 0xb6, 0x2c, 0x4d, 0x4e, 0xe8, 0x81, 0xc4, 0x9f, 0x72, 0x2e, 0x0d, 0x38, 0x8b, 0xb6,
	0xbb, 0x06, 0x45, 0x17, 0x45, 0xf7, 0xdd, 0xf9, 0x21, 0xba, 0xe8, 0x03, 0x64, 0xd9, 0x27, 0x28,
	0x0a, 0x77, 0xe3, 0xc7, 0x28, 0x66, 0x48, 0xc9, 0x8c, 0x24, 0x37, 0x59, 0x89, 0xb8, 0x7f, 0x3c,
	0xe7, 0x5c, 0x9e, 0x2b, 0xd0, 0x79, 0xea, 0x6d, 0x8f, 0xe9, 0xab, 0x6c, 0x78, 0x9e, 0xca, 0x5f,
	0x8e, 0x2e, 0x66, 0x3c, 0x74, 0x23, 0x37, 0xa0, 0xbd, 0x24, 0x8d, 0x31, 0x26, 0xad, 0x43, 0x91,
	0x97, 0xf1, 0x23, 0x19, 0xef, 0x3c, 0xf2, 0xe2, 0x30, 0x8c, 0xa3, 0x6d, 0x9c, 0x9c, 0x27, 0xcc,
	0x1b, 0x4f, 0xe8, 0x36, 0x1f, 0x9f, 0x66, 0x6c, 0x82, 0x2c, 0xc2, 0x8b, 0xa4, 0xe8, 0xd3, 0xaf,
	0x14, 0x80, 0xf1, 0xac, 0x95, 0x3c, 0x80, 0xba, 0x17, 0x72, 0x87, 0xf9, 0x6d, 0x45, 0xab, 0x6e,
	0x6d, 0xec, 0xd5, 0xde, 0xfe, 0xf5, 0xb0, 0x72, 0xbc, 0xe2, 0x85, 0x7c, 0xe0, 0x93, 0x7b, 0xa0,
	0x66, 0xcc, 0x6f, 0x57, 0x4b, 0x19, 0x11, 0x20, 0x6d, 0xa8, 0x21, 0x32, 0xbf, 0xad, 0x6a, 0xd5,
	0xad, 0x66, 0x91, 0x90, 0x11, 0xf2, 0x04, 0xd6, 0x39, 0x4d, 0xcf, 0x99, 0x47, 0x9d, 0xc8, 0x0d,
	0x69, 0xbb, 0x56, 0xaa, 0x58, 0x2b, 0x32, 0x2f, 0xdc, 0x90, 0x8a, 0x11, 0xb2, 0x60, 0xa5, 0x3c,
	0x42, 0x44, 0xc8, 0x26, 0xd4, 0x73, 0xba, 0xed, 0x7a, 0xe9, 0xbd, 0x45, 0x8c, 0x3c, 0x02, 0x40,
	0xcc, 0x38, 0x4d, 0x65, 0x77, 0x43, 0x53, 0x66, 0xdd, 0xa5, 0xb8, 0xfe, 0x35, 0xfc, 0xff, 0x80,
	0xe2, 0xee, 0x64, 0x72, 0x23, 0xd2, 0x31, 0xe5, 0x09, 0xe9, 0x43, 0x4b, 0x70, 0x77, 0xf2, 0x61,
	0xce, 0x84, 0x71, 0x6c, 0x2b, 0x9a, 0xba, 0xb5, 0xb6, 0xb3, 0xd9, 0x9b, 0xd7, 0xb3, 0x77, 0xa3,
	0xd2, 0xf1, 0x9d, 0x9b, 0xe7, 0x2f, 0x19, 0x47, 0xfd, 0x1b, 0x68, 0x0d, 0x29, 0x96, 0x87, 0x7f,
	0x4b, 0x9e, 0xcd, 0x70, 0x0b, 0x25, 0xdf, 0x37, 0x71, 0xca, 0xe7, 0x1e, 0xa8, 0xe8, 0x06, 0xef,
	0x4a, 0x8c, 0x6e, 0xa0, 0xff, 0xa8, 0x80, 0x7a, 0xc4, 0x03, 0xf2, 0x11, 0x34, 0x5e, 0xa5, 0x71,
	0x38, 0xbf, 0xa0, 0xba, 0x08, 0x0e, 0x7c, 0x72, 0x1f, 0x56, 0x30, 0x76, 0xe6, 0x76, 0x54, 0xc3,
	0x78, 0xe0, 0x93, 0x2e, 0x34, 0xbc, 0x38, 0x42, 0x1a, 0xa1, 0xdc, 0xd3, 0x7a, 0x91, 0x9c, 0x06,
	0xc9, 0x63, 0xb9, 0x2a, 0xc7, 0x3f, 0x75, 0x42, 0x1e, 0x0c, 0xfc, 0x76, 0x4d, 0x53, 0x66, 0x13,
	0x80, 0xd3, 0x74, 0xff, 0xf4, 0x48, 0xc4, 0xf5, 0x67, 0xd0, 0xdc, 0xf5, 0xfd, 0x23, 0x1e, 0x08,
	0x92, 0x4f, 0x40, 0x0d, 0x79, 0x50, 0x30, 0xbc, 0xbb, 0xc8, 0x50, 0x94, 0x89, 0x0a, 0xfd, 0x3b,
	0x68, 0x1e, 0x50, 0x2c, 0xba, 0x3e, 0x86, 0xa6, 0x37, 0x61, 0x34, 0xc2, 0x79, 0x1a, 0xab, 0x79,
	0x78, 0xe0, 0x2f, 0xa0, 0x29, 0xf3, 0x29, 0xa1, 0x11, 0x75, 0x01, 0x45, 0x51, 0xe4, 0x70, 0xf6,
	0x9a, 0x4a, 0x6a, 0xb3, 0xba, 0x40, 0xbe, 0x71, 0xc8, 0x5e, 0x53, 0xdd, 0x06, 0x98, 0xbe, 0x9f,
	0x27, 0xe4, 0x73, 0x58, 0x15, 0x1d, 0xa5, 0x7d, 0xdf, 0x82, 0xbd, 0x11, 0xf2, 0x40, 0x6e, 0x78,
	0x08, 0xed, 0x03, 0x8a, 0xc7, 0x74, 0xe2, 0x22, 0x8b, 0xa3, 0x11, 0xa7, 0xe9, 0x49, 0x2c, 0x3a,
	0x04, 0x9d, 0xc2, 0x16, 0xca, 0xbc, 0x2d, 0x1e, 0xc2, 0xaa, 0xfc, 0xba, 0x72, 0xcf, 0xdc, 0xa8,
	0xd9, 0x10, 0xd1, 0x11, 0xf3, 0xf5, 0xef, 0xe1, 0xfe, 0x2d, 0x43, 0x79, 0xf2, 0x81, 0x53, 0xab,
	0x0b, 0x53, 0xc9, 0x27, 0xb0, 0x91, 0x17, 0x4c, 0x5d, 0x51, 0xb6, 0xe5, 0xba, 0xac, 0x2a, 0x32,
	0x4f, 0xbb, 0xb0, 0x76, 0xf8, 0xbc, 0x3f, 0x72, 0x86, 0x27, 0xbb, 0x27, 0xa3, 0x21, 0x69, 0x80,
	0xfa, 0xb2, 0xdf, 0x6f, 0x55, 0x48, 0x1d, 0xaa, 0x2f, 0x5f, 0xb4, 0x94, 0x9d, 0xdf, 0xea, 0xb0,
	0x70, 0x57, 0xc8, 0xef, 0x0a, 0x6c, 0xbc, 0xf3, 0xb5, 0x13, 0x7d, 0x51, 0xbc, 0x79, 0x3b, 0x74,
	0x36, 0x7b, 0xb3, 0x3b, 0xd4, 0x1b, 0x1e, 0xee, 0xe5, 0x77, 0xe8, 0x79, 0x98, 0xe0, 0x85, 0xf3,
	0xd5, 0x9e, 0x4e, 0x7f, 0xb8, 0xbc, 0x56, 0x95, 0x9f, 0x2e, 0xaf, 0xd5, 0x3b, 0xa1, 0x99, 0x99,
	0xcc, 0x1c, 0x9b, 0x91, 0xc9, 0x4d, 0x34, 0x7f, 0xbd, 0xbc, 0x56, 0xbf, 0x30, 0x42, 0x2b, 0x3f,
	0x4a, 0xb6, 0x66, 0x64, 0x56, 0xc6, 0x7c, 0xdb, 0x60, 0x96, 0xb8, 0x2f, 0xb6, 0x66, 0x8c, 0xad,
	0xf2, 0x81, 0xb1, 0x35, 0x23, 0xb2, 0x8a, 0x07, 0xae, 0x59, 0xb9, 0xa9, 0x6c, 0xcd, 0x40, 0x0b,
	0xdd, 0xc0, 0x26, 0x0c, 0x5a, 0xf3, 0x77, 0x80, 0xfc, 0x27, 0xb0, 0xce, 0xe3, 0x45, 0x6a, 0xcb,
	0x2e, 0x89, 0xfe, 0x3f, 0x41, 0xa0, 0x2a, 0x08, 0x54, 0x04, 0xe4, 0x0a, 0xf9, 0x43, 0x99, 0xfa,
	0x64, 0x67, 0xff, 0x94, 0x3c, 0x58, 0x1c, 0x33, 0x33, 0xd1, 0x7b, 0xa4, 0x41, 0x31, 0x59, 0x15,
	0x93, 0x5b, 0x68, 0x8e, 0xcd, 0xd0, 0x8c, 0xcd, 0x33, 0xf3, 0xc2, 0xe4, 0xa6, 0x2b, 0xc5, 0x19,
	0x19, 0x68, 0xe5, 0xae, 0x91, 0x62, 0x88, 0xf5, 0xda, 0x9a, 0x11, 0x5a, 0xf2, 0x4a, 0x14, 0x4a,
	0xc4, 0x16, 0xc6, 0xd3, 0xe7, 0x33, 0xab, 0x30, 0xbc, 0x6d, 0x5c, 0x58, 0xe2, 0x7f, 0xc0, 0x36,
	0xf8, 0x54, 0x27, 0xc3, 0xb5, 0x5c, 0x44, 0xd7, 0x3b, 0xb3, 0xc9, 0xcf, 0x0a, 0xac, 0xe7, 0x86,
	0xe9, 0xa7, 0x71, 0xb8, 0x9c, 0xc1, 0xcc, 0xd0, 0x9d, 0xcd, 0xdb, 0x93, 0x3c, 0xd1, 0x77, 0x05,
	0x83, 0x9a, 0x60, 0x50, 0x47, 0x33, 0x30, 0xb9, 0xc4, 0xfd, 0x59, 0x19, 0x77, 0x60, 0x95, 0x4d,
	0x2c, 0x76, 0x67, 0x95, 0xdc, 0xbf, 0x2f, 0xe1, 0xdc, 0x5d, 0x6a, 0x15, 0xf2, 0x74, 0xe9, 0xab,
	0x97, 0x1a, 0xb5, 0xf3, 0xe9, 0x07, 0xd7, 0xf2, 0x44, 0xef, 0x08, 0xd4, 0x2b, 0x02, 0x75, 0x35,
	0x93, 0x88, 0x9b, 0x46, 0xa6, 0xc9, 0x8f, 0x4f, 0xeb, 0xd4, 0xdf, 0x5c, 0x5e, 0xab, 0x6f, 0xa2,
	0xbd, 0xd6, 0xdb, 0xab, 0xae, 0xf2, 0xe7, 0x55, 0x57, 0xf9, 0xfb, 0xaa, 0xab, 0xfc, 0xf2, 0x4f,
	0xb7, 0xf2, 0x6f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xb1, 0x10, 0x72, 0xf6, 0xab, 0x07, 0x00, 0x00,
}
