// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channel-member-rank/channel-member-rank.proto

package channel_member_rank // import "golang.52tt.com/protocol/services/channel-member-rank"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SourceType int32

const (
	SourceType_SOURCE_TYPE_UNSPECIFIED SourceType = 0
	SourceType_SOURCE_TYPE_PRESENT     SourceType = 1
	SourceType_SOURCE_TYPE_KNIGHT      SourceType = 2
	SourceType_SOURCE_TYPE_TBEAN       SourceType = 3
	SourceType_SOURCE_TYPE_ACTIVE      SourceType = 4
)

var SourceType_name = map[int32]string{
	0: "SOURCE_TYPE_UNSPECIFIED",
	1: "SOURCE_TYPE_PRESENT",
	2: "SOURCE_TYPE_KNIGHT",
	3: "SOURCE_TYPE_TBEAN",
	4: "SOURCE_TYPE_ACTIVE",
}
var SourceType_value = map[string]int32{
	"SOURCE_TYPE_UNSPECIFIED": 0,
	"SOURCE_TYPE_PRESENT":     1,
	"SOURCE_TYPE_KNIGHT":      2,
	"SOURCE_TYPE_TBEAN":       3,
	"SOURCE_TYPE_ACTIVE":      4,
}

func (x SourceType) String() string {
	return proto.EnumName(SourceType_name, int32(x))
}
func (SourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_member_rank_0887b4b7efc67033, []int{0}
}

type OrderStatus int32

const (
	OrderStatus_ORDER_STATUS_DELAY   OrderStatus = 0
	OrderStatus_ORDER_STATUS_ABANDON OrderStatus = 1
	OrderStatus_ORDER_STATUS_COMMIT  OrderStatus = 2
)

var OrderStatus_name = map[int32]string{
	0: "ORDER_STATUS_DELAY",
	1: "ORDER_STATUS_ABANDON",
	2: "ORDER_STATUS_COMMIT",
}
var OrderStatus_value = map[string]int32{
	"ORDER_STATUS_DELAY":   0,
	"ORDER_STATUS_ABANDON": 1,
	"ORDER_STATUS_COMMIT":  2,
}

func (x OrderStatus) String() string {
	return proto.EnumName(OrderStatus_name, int32(x))
}
func (OrderStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_member_rank_0887b4b7efc67033, []int{1}
}

// 成员消费信息
type MemberConsumeInfo struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ConsumeValue         uint64            `protobuf:"varint,2,opt,name=consume_value,json=consumeValue,proto3" json:"consume_value,omitempty"`
	VipLevelInfo         *ChannelMemberVip `protobuf:"bytes,3,opt,name=vip_level_info,json=vipLevelInfo,proto3" json:"vip_level_info,omitempty"`
	IsAutoHiddenConsume  bool              `protobuf:"varint,4,opt,name=is_auto_hidden_consume,json=isAutoHiddenConsume,proto3" json:"is_auto_hidden_consume,omitempty"`
	Rank                 uint32            `protobuf:"varint,5,opt,name=rank,proto3" json:"rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *MemberConsumeInfo) Reset()         { *m = MemberConsumeInfo{} }
func (m *MemberConsumeInfo) String() string { return proto.CompactTextString(m) }
func (*MemberConsumeInfo) ProtoMessage()    {}
func (*MemberConsumeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_member_rank_0887b4b7efc67033, []int{0}
}
func (m *MemberConsumeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberConsumeInfo.Unmarshal(m, b)
}
func (m *MemberConsumeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberConsumeInfo.Marshal(b, m, deterministic)
}
func (dst *MemberConsumeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberConsumeInfo.Merge(dst, src)
}
func (m *MemberConsumeInfo) XXX_Size() int {
	return xxx_messageInfo_MemberConsumeInfo.Size(m)
}
func (m *MemberConsumeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberConsumeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MemberConsumeInfo proto.InternalMessageInfo

func (m *MemberConsumeInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MemberConsumeInfo) GetConsumeValue() uint64 {
	if m != nil {
		return m.ConsumeValue
	}
	return 0
}

func (m *MemberConsumeInfo) GetVipLevelInfo() *ChannelMemberVip {
	if m != nil {
		return m.VipLevelInfo
	}
	return nil
}

func (m *MemberConsumeInfo) GetIsAutoHiddenConsume() bool {
	if m != nil {
		return m.IsAutoHiddenConsume
	}
	return false
}

func (m *MemberConsumeInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

// 成员等级信息
type ChannelMemberVip struct {
	CurrLevelId          uint32   `protobuf:"varint,1,opt,name=curr_level_id,json=currLevelId,proto3" json:"curr_level_id,omitempty"`
	CurrLevelName        string   `protobuf:"bytes,2,opt,name=curr_level_name,json=currLevelName,proto3" json:"curr_level_name,omitempty"`
	CurrLevelValue       uint64   `protobuf:"varint,3,opt,name=curr_level_value,json=currLevelValue,proto3" json:"curr_level_value,omitempty"`
	CurrLevelMinValue    uint64   `protobuf:"varint,7,opt,name=curr_level_min_value,json=currLevelMinValue,proto3" json:"curr_level_min_value,omitempty"`
	NextLevelId          uint32   `protobuf:"varint,4,opt,name=next_level_id,json=nextLevelId,proto3" json:"next_level_id,omitempty"`
	NextLevelName        string   `protobuf:"bytes,5,opt,name=next_level_name,json=nextLevelName,proto3" json:"next_level_name,omitempty"`
	NextLevelMinValue    uint64   `protobuf:"varint,6,opt,name=next_level_min_value,json=nextLevelMinValue,proto3" json:"next_level_min_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelMemberVip) Reset()         { *m = ChannelMemberVip{} }
func (m *ChannelMemberVip) String() string { return proto.CompactTextString(m) }
func (*ChannelMemberVip) ProtoMessage()    {}
func (*ChannelMemberVip) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_member_rank_0887b4b7efc67033, []int{1}
}
func (m *ChannelMemberVip) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMemberVip.Unmarshal(m, b)
}
func (m *ChannelMemberVip) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMemberVip.Marshal(b, m, deterministic)
}
func (dst *ChannelMemberVip) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMemberVip.Merge(dst, src)
}
func (m *ChannelMemberVip) XXX_Size() int {
	return xxx_messageInfo_ChannelMemberVip.Size(m)
}
func (m *ChannelMemberVip) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMemberVip.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMemberVip proto.InternalMessageInfo

func (m *ChannelMemberVip) GetCurrLevelId() uint32 {
	if m != nil {
		return m.CurrLevelId
	}
	return 0
}

func (m *ChannelMemberVip) GetCurrLevelName() string {
	if m != nil {
		return m.CurrLevelName
	}
	return ""
}

func (m *ChannelMemberVip) GetCurrLevelValue() uint64 {
	if m != nil {
		return m.CurrLevelValue
	}
	return 0
}

func (m *ChannelMemberVip) GetCurrLevelMinValue() uint64 {
	if m != nil {
		return m.CurrLevelMinValue
	}
	return 0
}

func (m *ChannelMemberVip) GetNextLevelId() uint32 {
	if m != nil {
		return m.NextLevelId
	}
	return 0
}

func (m *ChannelMemberVip) GetNextLevelName() string {
	if m != nil {
		return m.NextLevelName
	}
	return ""
}

func (m *ChannelMemberVip) GetNextLevelMinValue() uint64 {
	if m != nil {
		return m.NextLevelMinValue
	}
	return 0
}

// 获取房间爱意榜
type GetChannelConsumeRankListReq struct {
	ChannelId uint32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Offset    uint32 `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit     uint32 `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	// 需要获取自己的排名信息才填以下字段
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	IsShowHidden         bool     `protobuf:"varint,6,opt,name=is_show_hidden,json=isShowHidden,proto3" json:"is_show_hidden,omitempty"`
	ChannelType          uint32   `protobuf:"varint,7,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelConsumeRankListReq) Reset()         { *m = GetChannelConsumeRankListReq{} }
func (m *GetChannelConsumeRankListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelConsumeRankListReq) ProtoMessage()    {}
func (*GetChannelConsumeRankListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_member_rank_0887b4b7efc67033, []int{2}
}
func (m *GetChannelConsumeRankListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelConsumeRankListReq.Unmarshal(m, b)
}
func (m *GetChannelConsumeRankListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelConsumeRankListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelConsumeRankListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelConsumeRankListReq.Merge(dst, src)
}
func (m *GetChannelConsumeRankListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelConsumeRankListReq.Size(m)
}
func (m *GetChannelConsumeRankListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelConsumeRankListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelConsumeRankListReq proto.InternalMessageInfo

func (m *GetChannelConsumeRankListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelConsumeRankListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetChannelConsumeRankListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetChannelConsumeRankListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelConsumeRankListReq) GetIsShowHidden() bool {
	if m != nil {
		return m.IsShowHidden
	}
	return false
}

func (m *GetChannelConsumeRankListReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type GetChannelConsumeRankListResp struct {
	RankInfoList         []*MemberConsumeInfo `protobuf:"bytes,1,rep,name=rank_info_list,json=rankInfoList,proto3" json:"rank_info_list,omitempty"`
	MyRankInfo           *MemberConsumeInfo   `protobuf:"bytes,2,opt,name=my_rank_info,json=myRankInfo,proto3" json:"my_rank_info,omitempty"`
	DValue               uint64               `protobuf:"varint,3,opt,name=d_value,json=dValue,proto3" json:"d_value,omitempty"`
	Rank                 uint32               `protobuf:"varint,4,opt,name=rank,proto3" json:"rank,omitempty"`
	IsCanHideConsume     bool                 `protobuf:"varint,5,opt,name=is_can_hide_consume,json=isCanHideConsume,proto3" json:"is_can_hide_consume,omitempty"`
	IsHiddenConsume      bool                 `protobuf:"varint,6,opt,name=is_hidden_consume,json=isHiddenConsume,proto3" json:"is_hidden_consume,omitempty"`
	IsAutoHiddenConsume  bool                 `protobuf:"varint,7,opt,name=is_auto_hidden_consume,json=isAutoHiddenConsume,proto3" json:"is_auto_hidden_consume,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetChannelConsumeRankListResp) Reset()         { *m = GetChannelConsumeRankListResp{} }
func (m *GetChannelConsumeRankListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelConsumeRankListResp) ProtoMessage()    {}
func (*GetChannelConsumeRankListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_member_rank_0887b4b7efc67033, []int{3}
}
func (m *GetChannelConsumeRankListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelConsumeRankListResp.Unmarshal(m, b)
}
func (m *GetChannelConsumeRankListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelConsumeRankListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelConsumeRankListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelConsumeRankListResp.Merge(dst, src)
}
func (m *GetChannelConsumeRankListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelConsumeRankListResp.Size(m)
}
func (m *GetChannelConsumeRankListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelConsumeRankListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelConsumeRankListResp proto.InternalMessageInfo

func (m *GetChannelConsumeRankListResp) GetRankInfoList() []*MemberConsumeInfo {
	if m != nil {
		return m.RankInfoList
	}
	return nil
}

func (m *GetChannelConsumeRankListResp) GetMyRankInfo() *MemberConsumeInfo {
	if m != nil {
		return m.MyRankInfo
	}
	return nil
}

func (m *GetChannelConsumeRankListResp) GetDValue() uint64 {
	if m != nil {
		return m.DValue
	}
	return 0
}

func (m *GetChannelConsumeRankListResp) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *GetChannelConsumeRankListResp) GetIsCanHideConsume() bool {
	if m != nil {
		return m.IsCanHideConsume
	}
	return false
}

func (m *GetChannelConsumeRankListResp) GetIsHiddenConsume() bool {
	if m != nil {
		return m.IsHiddenConsume
	}
	return false
}

func (m *GetChannelConsumeRankListResp) GetIsAutoHiddenConsume() bool {
	if m != nil {
		return m.IsAutoHiddenConsume
	}
	return false
}

// 获取指定用户的消费信息信息
type GetUserConsumeInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserConsumeInfoReq) Reset()         { *m = GetUserConsumeInfoReq{} }
func (m *GetUserConsumeInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserConsumeInfoReq) ProtoMessage()    {}
func (*GetUserConsumeInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_member_rank_0887b4b7efc67033, []int{4}
}
func (m *GetUserConsumeInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserConsumeInfoReq.Unmarshal(m, b)
}
func (m *GetUserConsumeInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserConsumeInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserConsumeInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserConsumeInfoReq.Merge(dst, src)
}
func (m *GetUserConsumeInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserConsumeInfoReq.Size(m)
}
func (m *GetUserConsumeInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserConsumeInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserConsumeInfoReq proto.InternalMessageInfo

func (m *GetUserConsumeInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetUserConsumeInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserConsumeInfoResp struct {
	ConsumeInfo          *MemberConsumeInfo `protobuf:"bytes,1,opt,name=consume_info,json=consumeInfo,proto3" json:"consume_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetUserConsumeInfoResp) Reset()         { *m = GetUserConsumeInfoResp{} }
func (m *GetUserConsumeInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserConsumeInfoResp) ProtoMessage()    {}
func (*GetUserConsumeInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_member_rank_0887b4b7efc67033, []int{5}
}
func (m *GetUserConsumeInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserConsumeInfoResp.Unmarshal(m, b)
}
func (m *GetUserConsumeInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserConsumeInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserConsumeInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserConsumeInfoResp.Merge(dst, src)
}
func (m *GetUserConsumeInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserConsumeInfoResp.Size(m)
}
func (m *GetUserConsumeInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserConsumeInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserConsumeInfoResp proto.InternalMessageInfo

func (m *GetUserConsumeInfoResp) GetConsumeInfo() *MemberConsumeInfo {
	if m != nil {
		return m.ConsumeInfo
	}
	return nil
}

// 批量获取指定用户的消费信息信息
type BatGetUserConsumeInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetUserConsumeInfoReq) Reset()         { *m = BatGetUserConsumeInfoReq{} }
func (m *BatGetUserConsumeInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatGetUserConsumeInfoReq) ProtoMessage()    {}
func (*BatGetUserConsumeInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_member_rank_0887b4b7efc67033, []int{6}
}
func (m *BatGetUserConsumeInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetUserConsumeInfoReq.Unmarshal(m, b)
}
func (m *BatGetUserConsumeInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetUserConsumeInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatGetUserConsumeInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetUserConsumeInfoReq.Merge(dst, src)
}
func (m *BatGetUserConsumeInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatGetUserConsumeInfoReq.Size(m)
}
func (m *BatGetUserConsumeInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetUserConsumeInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetUserConsumeInfoReq proto.InternalMessageInfo

func (m *BatGetUserConsumeInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BatGetUserConsumeInfoReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatGetUserConsumeInfoResp struct {
	ConsumeInfoList      []*MemberConsumeInfo `protobuf:"bytes,1,rep,name=consume_info_list,json=consumeInfoList,proto3" json:"consume_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatGetUserConsumeInfoResp) Reset()         { *m = BatGetUserConsumeInfoResp{} }
func (m *BatGetUserConsumeInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatGetUserConsumeInfoResp) ProtoMessage()    {}
func (*BatGetUserConsumeInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_member_rank_0887b4b7efc67033, []int{7}
}
func (m *BatGetUserConsumeInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetUserConsumeInfoResp.Unmarshal(m, b)
}
func (m *BatGetUserConsumeInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetUserConsumeInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatGetUserConsumeInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetUserConsumeInfoResp.Merge(dst, src)
}
func (m *BatGetUserConsumeInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatGetUserConsumeInfoResp.Size(m)
}
func (m *BatGetUserConsumeInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetUserConsumeInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetUserConsumeInfoResp proto.InternalMessageInfo

func (m *BatGetUserConsumeInfoResp) GetConsumeInfoList() []*MemberConsumeInfo {
	if m != nil {
		return m.ConsumeInfoList
	}
	return nil
}

// 隐藏/取消隐藏用户的消费信息
type HideUserChannelConsumeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	IsHide               bool     `protobuf:"varint,3,opt,name=is_hide,json=isHide,proto3" json:"is_hide,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HideUserChannelConsumeReq) Reset()         { *m = HideUserChannelConsumeReq{} }
func (m *HideUserChannelConsumeReq) String() string { return proto.CompactTextString(m) }
func (*HideUserChannelConsumeReq) ProtoMessage()    {}
func (*HideUserChannelConsumeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_member_rank_0887b4b7efc67033, []int{8}
}
func (m *HideUserChannelConsumeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HideUserChannelConsumeReq.Unmarshal(m, b)
}
func (m *HideUserChannelConsumeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HideUserChannelConsumeReq.Marshal(b, m, deterministic)
}
func (dst *HideUserChannelConsumeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HideUserChannelConsumeReq.Merge(dst, src)
}
func (m *HideUserChannelConsumeReq) XXX_Size() int {
	return xxx_messageInfo_HideUserChannelConsumeReq.Size(m)
}
func (m *HideUserChannelConsumeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HideUserChannelConsumeReq.DiscardUnknown(m)
}

var xxx_messageInfo_HideUserChannelConsumeReq proto.InternalMessageInfo

func (m *HideUserChannelConsumeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HideUserChannelConsumeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *HideUserChannelConsumeReq) GetIsHide() bool {
	if m != nil {
		return m.IsHide
	}
	return false
}

type HideUserChannelConsumeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HideUserChannelConsumeResp) Reset()         { *m = HideUserChannelConsumeResp{} }
func (m *HideUserChannelConsumeResp) String() string { return proto.CompactTextString(m) }
func (*HideUserChannelConsumeResp) ProtoMessage()    {}
func (*HideUserChannelConsumeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_member_rank_0887b4b7efc67033, []int{9}
}
func (m *HideUserChannelConsumeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HideUserChannelConsumeResp.Unmarshal(m, b)
}
func (m *HideUserChannelConsumeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HideUserChannelConsumeResp.Marshal(b, m, deterministic)
}
func (dst *HideUserChannelConsumeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HideUserChannelConsumeResp.Merge(dst, src)
}
func (m *HideUserChannelConsumeResp) XXX_Size() int {
	return xxx_messageInfo_HideUserChannelConsumeResp.Size(m)
}
func (m *HideUserChannelConsumeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HideUserChannelConsumeResp.DiscardUnknown(m)
}

var xxx_messageInfo_HideUserChannelConsumeResp proto.InternalMessageInfo

// 结算神秘人消费
type SettleUkwConsumeReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Certain              bool     `protobuf:"varint,3,opt,name=certain,proto3" json:"certain,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SettleUkwConsumeReq) Reset()         { *m = SettleUkwConsumeReq{} }
func (m *SettleUkwConsumeReq) String() string { return proto.CompactTextString(m) }
func (*SettleUkwConsumeReq) ProtoMessage()    {}
func (*SettleUkwConsumeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_member_rank_0887b4b7efc67033, []int{10}
}
func (m *SettleUkwConsumeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SettleUkwConsumeReq.Unmarshal(m, b)
}
func (m *SettleUkwConsumeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SettleUkwConsumeReq.Marshal(b, m, deterministic)
}
func (dst *SettleUkwConsumeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SettleUkwConsumeReq.Merge(dst, src)
}
func (m *SettleUkwConsumeReq) XXX_Size() int {
	return xxx_messageInfo_SettleUkwConsumeReq.Size(m)
}
func (m *SettleUkwConsumeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SettleUkwConsumeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SettleUkwConsumeReq proto.InternalMessageInfo

func (m *SettleUkwConsumeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SettleUkwConsumeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SettleUkwConsumeReq) GetCertain() bool {
	if m != nil {
		return m.Certain
	}
	return false
}

type SettleUkwConsumeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SettleUkwConsumeResp) Reset()         { *m = SettleUkwConsumeResp{} }
func (m *SettleUkwConsumeResp) String() string { return proto.CompactTextString(m) }
func (*SettleUkwConsumeResp) ProtoMessage()    {}
func (*SettleUkwConsumeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_member_rank_0887b4b7efc67033, []int{11}
}
func (m *SettleUkwConsumeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SettleUkwConsumeResp.Unmarshal(m, b)
}
func (m *SettleUkwConsumeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SettleUkwConsumeResp.Marshal(b, m, deterministic)
}
func (dst *SettleUkwConsumeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SettleUkwConsumeResp.Merge(dst, src)
}
func (m *SettleUkwConsumeResp) XXX_Size() int {
	return xxx_messageInfo_SettleUkwConsumeResp.Size(m)
}
func (m *SettleUkwConsumeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SettleUkwConsumeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SettleUkwConsumeResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*MemberConsumeInfo)(nil), "channel_member_rank.MemberConsumeInfo")
	proto.RegisterType((*ChannelMemberVip)(nil), "channel_member_rank.ChannelMemberVip")
	proto.RegisterType((*GetChannelConsumeRankListReq)(nil), "channel_member_rank.GetChannelConsumeRankListReq")
	proto.RegisterType((*GetChannelConsumeRankListResp)(nil), "channel_member_rank.GetChannelConsumeRankListResp")
	proto.RegisterType((*GetUserConsumeInfoReq)(nil), "channel_member_rank.GetUserConsumeInfoReq")
	proto.RegisterType((*GetUserConsumeInfoResp)(nil), "channel_member_rank.GetUserConsumeInfoResp")
	proto.RegisterType((*BatGetUserConsumeInfoReq)(nil), "channel_member_rank.BatGetUserConsumeInfoReq")
	proto.RegisterType((*BatGetUserConsumeInfoResp)(nil), "channel_member_rank.BatGetUserConsumeInfoResp")
	proto.RegisterType((*HideUserChannelConsumeReq)(nil), "channel_member_rank.HideUserChannelConsumeReq")
	proto.RegisterType((*HideUserChannelConsumeResp)(nil), "channel_member_rank.HideUserChannelConsumeResp")
	proto.RegisterType((*SettleUkwConsumeReq)(nil), "channel_member_rank.SettleUkwConsumeReq")
	proto.RegisterType((*SettleUkwConsumeResp)(nil), "channel_member_rank.SettleUkwConsumeResp")
	proto.RegisterEnum("channel_member_rank.SourceType", SourceType_name, SourceType_value)
	proto.RegisterEnum("channel_member_rank.OrderStatus", OrderStatus_name, OrderStatus_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelMemberRankClient is the client API for ChannelMemberRank service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelMemberRankClient interface {
	// 获取房间爱意榜
	GetChannelConsumeRankList(ctx context.Context, in *GetChannelConsumeRankListReq, opts ...grpc.CallOption) (*GetChannelConsumeRankListResp, error)
	// 获取指定用户的消费信息信息
	GetUserConsumeInfo(ctx context.Context, in *GetUserConsumeInfoReq, opts ...grpc.CallOption) (*GetUserConsumeInfoResp, error)
	// 批量获取指定用户的消费信息信息
	BatGetUserConsumeInfo(ctx context.Context, in *BatGetUserConsumeInfoReq, opts ...grpc.CallOption) (*BatGetUserConsumeInfoResp, error)
	// 隐藏/取消隐藏用户的消费信息
	HideUserChannelConsume(ctx context.Context, in *HideUserChannelConsumeReq, opts ...grpc.CallOption) (*HideUserChannelConsumeResp, error)
	// 结算神秘人消费
	SettleUkwConsume(ctx context.Context, in *SettleUkwConsumeReq, opts ...grpc.CallOption) (*SettleUkwConsumeResp, error)
}

type channelMemberRankClient struct {
	cc *grpc.ClientConn
}

func NewChannelMemberRankClient(cc *grpc.ClientConn) ChannelMemberRankClient {
	return &channelMemberRankClient{cc}
}

func (c *channelMemberRankClient) GetChannelConsumeRankList(ctx context.Context, in *GetChannelConsumeRankListReq, opts ...grpc.CallOption) (*GetChannelConsumeRankListResp, error) {
	out := new(GetChannelConsumeRankListResp)
	err := c.cc.Invoke(ctx, "/channel_member_rank.ChannelMemberRank/GetChannelConsumeRankList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberRankClient) GetUserConsumeInfo(ctx context.Context, in *GetUserConsumeInfoReq, opts ...grpc.CallOption) (*GetUserConsumeInfoResp, error) {
	out := new(GetUserConsumeInfoResp)
	err := c.cc.Invoke(ctx, "/channel_member_rank.ChannelMemberRank/GetUserConsumeInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberRankClient) BatGetUserConsumeInfo(ctx context.Context, in *BatGetUserConsumeInfoReq, opts ...grpc.CallOption) (*BatGetUserConsumeInfoResp, error) {
	out := new(BatGetUserConsumeInfoResp)
	err := c.cc.Invoke(ctx, "/channel_member_rank.ChannelMemberRank/BatGetUserConsumeInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberRankClient) HideUserChannelConsume(ctx context.Context, in *HideUserChannelConsumeReq, opts ...grpc.CallOption) (*HideUserChannelConsumeResp, error) {
	out := new(HideUserChannelConsumeResp)
	err := c.cc.Invoke(ctx, "/channel_member_rank.ChannelMemberRank/HideUserChannelConsume", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberRankClient) SettleUkwConsume(ctx context.Context, in *SettleUkwConsumeReq, opts ...grpc.CallOption) (*SettleUkwConsumeResp, error) {
	out := new(SettleUkwConsumeResp)
	err := c.cc.Invoke(ctx, "/channel_member_rank.ChannelMemberRank/SettleUkwConsume", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelMemberRankServer is the server API for ChannelMemberRank service.
type ChannelMemberRankServer interface {
	// 获取房间爱意榜
	GetChannelConsumeRankList(context.Context, *GetChannelConsumeRankListReq) (*GetChannelConsumeRankListResp, error)
	// 获取指定用户的消费信息信息
	GetUserConsumeInfo(context.Context, *GetUserConsumeInfoReq) (*GetUserConsumeInfoResp, error)
	// 批量获取指定用户的消费信息信息
	BatGetUserConsumeInfo(context.Context, *BatGetUserConsumeInfoReq) (*BatGetUserConsumeInfoResp, error)
	// 隐藏/取消隐藏用户的消费信息
	HideUserChannelConsume(context.Context, *HideUserChannelConsumeReq) (*HideUserChannelConsumeResp, error)
	// 结算神秘人消费
	SettleUkwConsume(context.Context, *SettleUkwConsumeReq) (*SettleUkwConsumeResp, error)
}

func RegisterChannelMemberRankServer(s *grpc.Server, srv ChannelMemberRankServer) {
	s.RegisterService(&_ChannelMemberRank_serviceDesc, srv)
}

func _ChannelMemberRank_GetChannelConsumeRankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelConsumeRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberRankServer).GetChannelConsumeRankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_member_rank.ChannelMemberRank/GetChannelConsumeRankList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberRankServer).GetChannelConsumeRankList(ctx, req.(*GetChannelConsumeRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberRank_GetUserConsumeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserConsumeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberRankServer).GetUserConsumeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_member_rank.ChannelMemberRank/GetUserConsumeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberRankServer).GetUserConsumeInfo(ctx, req.(*GetUserConsumeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberRank_BatGetUserConsumeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetUserConsumeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberRankServer).BatGetUserConsumeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_member_rank.ChannelMemberRank/BatGetUserConsumeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberRankServer).BatGetUserConsumeInfo(ctx, req.(*BatGetUserConsumeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberRank_HideUserChannelConsume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HideUserChannelConsumeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberRankServer).HideUserChannelConsume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_member_rank.ChannelMemberRank/HideUserChannelConsume",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberRankServer).HideUserChannelConsume(ctx, req.(*HideUserChannelConsumeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberRank_SettleUkwConsume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SettleUkwConsumeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberRankServer).SettleUkwConsume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_member_rank.ChannelMemberRank/SettleUkwConsume",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberRankServer).SettleUkwConsume(ctx, req.(*SettleUkwConsumeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelMemberRank_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_member_rank.ChannelMemberRank",
	HandlerType: (*ChannelMemberRankServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChannelConsumeRankList",
			Handler:    _ChannelMemberRank_GetChannelConsumeRankList_Handler,
		},
		{
			MethodName: "GetUserConsumeInfo",
			Handler:    _ChannelMemberRank_GetUserConsumeInfo_Handler,
		},
		{
			MethodName: "BatGetUserConsumeInfo",
			Handler:    _ChannelMemberRank_BatGetUserConsumeInfo_Handler,
		},
		{
			MethodName: "HideUserChannelConsume",
			Handler:    _ChannelMemberRank_HideUserChannelConsume_Handler,
		},
		{
			MethodName: "SettleUkwConsume",
			Handler:    _ChannelMemberRank_SettleUkwConsume_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/channel-member-rank/channel-member-rank.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/channel-member-rank/channel-member-rank.proto", fileDescriptor_channel_member_rank_0887b4b7efc67033)
}

var fileDescriptor_channel_member_rank_0887b4b7efc67033 = []byte{
	// 1017 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x56, 0xdd, 0x52, 0xdb, 0x46,
	0x14, 0x8e, 0x6c, 0x83, 0xc9, 0xc1, 0x06, 0x79, 0x01, 0x63, 0xdc, 0x64, 0xc6, 0x55, 0xdb, 0x8c,
	0x4b, 0x07, 0x7b, 0x4a, 0x26, 0xd3, 0x9b, 0xde, 0x18, 0xe3, 0x82, 0x27, 0x60, 0x18, 0x59, 0x30,
	0x4d, 0x6f, 0x54, 0x45, 0x5a, 0xc2, 0x0e, 0xfa, 0x43, 0xbb, 0x32, 0x61, 0x7a, 0xdb, 0x9b, 0xbe,
	0x50, 0x1f, 0xa1, 0xcf, 0xd1, 0x17, 0xe8, 0x5d, 0x1f, 0xa0, 0xb3, 0xbb, 0x92, 0x91, 0x41, 0x4e,
	0x21, 0x77, 0xda, 0xb3, 0xdf, 0x9e, 0xef, 0x3b, 0x67, 0xbf, 0xb3, 0x36, 0xfc, 0xc8, 0x58, 0xf7,
	0x3a, 0x26, 0xf6, 0x15, 0x25, 0xee, 0x04, 0x47, 0x5d, 0xfb, 0xd2, 0xf2, 0x7d, 0xec, 0xee, 0x78,
	0xd8, 0x7b, 0x8f, 0xa3, 0x9d, 0xc8, 0xf2, 0xaf, 0xf2, 0x62, 0x9d, 0x30, 0x0a, 0x58, 0x80, 0xd6,
	0x92, 0x2d, 0x53, 0x6e, 0x99, 0x7c, 0x4b, 0xfb, 0x5b, 0x81, 0xda, 0xb1, 0x58, 0xf7, 0x03, 0x9f,
	0xc6, 0x1e, 0x1e, 0xfa, 0x17, 0x01, 0x52, 0xa1, 0x18, 0x13, 0xa7, 0xa1, 0xb4, 0x94, 0x76, 0x55,
	0xe7, 0x9f, 0xe8, 0x2b, 0xa8, 0xda, 0x12, 0x60, 0x4e, 0x2c, 0x37, 0xc6, 0x8d, 0x42, 0x4b, 0x69,
	0x97, 0xf4, 0x4a, 0x12, 0x3c, 0xe7, 0x31, 0xf4, 0x16, 0x56, 0x26, 0x24, 0x34, 0x5d, 0x3c, 0xc1,
	0xae, 0x49, 0xfc, 0x8b, 0xa0, 0x51, 0x6c, 0x29, 0xed, 0xe5, 0xdd, 0x6f, 0x3a, 0x39, 0xd4, 0x9d,
	0xbe, 0x8c, 0x49, 0xf6, 0x73, 0x12, 0xea, 0x95, 0x09, 0x09, 0x8f, 0xf8, 0x59, 0xa1, 0xe1, 0x35,
	0xd4, 0x09, 0x35, 0xad, 0x98, 0x05, 0xe6, 0x25, 0x71, 0x1c, 0xec, 0x9b, 0x09, 0x57, 0xa3, 0xd4,
	0x52, 0xda, 0x4b, 0xfa, 0x1a, 0xa1, 0xbd, 0x98, 0x05, 0x87, 0x62, 0x2f, 0x11, 0x8f, 0x10, 0x94,
	0x78, 0xee, 0xc6, 0x82, 0x50, 0x2e, 0xbe, 0xb5, 0x3f, 0x0b, 0xa0, 0xde, 0xe7, 0x42, 0x1a, 0x54,
	0xed, 0x38, 0x8a, 0x52, 0xad, 0x69, 0xad, 0xcb, 0x3c, 0x28, 0x35, 0x38, 0xe8, 0x15, 0xac, 0x66,
	0x30, 0xbe, 0xe5, 0xc9, 0xaa, 0x9f, 0xeb, 0xd5, 0x29, 0x6a, 0x64, 0x79, 0x18, 0xb5, 0x41, 0xcd,
	0xe0, 0x64, 0x7b, 0x8a, 0xa2, 0x3d, 0x2b, 0x53, 0xa0, 0x6c, 0x50, 0x17, 0xd6, 0x33, 0x48, 0x8f,
	0xf8, 0x09, 0xba, 0x2c, 0xd0, 0xb5, 0x29, 0xfa, 0x98, 0xf8, 0xf2, 0x80, 0x06, 0x55, 0x1f, 0x7f,
	0x64, 0x77, 0x32, 0x4b, 0x52, 0x26, 0x0f, 0x66, 0x64, 0x66, 0x30, 0x42, 0xe6, 0x82, 0x94, 0x39,
	0x45, 0x09, 0x99, 0x5d, 0x58, 0xcf, 0xe0, 0xee, 0xc8, 0x17, 0x25, 0xf9, 0x14, 0x9c, 0x92, 0x6b,
	0x7f, 0x29, 0xf0, 0xe2, 0x00, 0xb3, 0xa4, 0x77, 0x49, 0x8b, 0x75, 0xcb, 0xbf, 0x3a, 0x22, 0x94,
	0xe9, 0xf8, 0x1a, 0xbd, 0x04, 0x48, 0x2f, 0x76, 0xda, 0xc1, 0xe7, 0x49, 0x64, 0xe8, 0xa0, 0x3a,
	0x2c, 0x06, 0x17, 0x17, 0x14, 0x33, 0xd1, 0xb6, 0xaa, 0x9e, 0xac, 0xd0, 0x3a, 0x2c, 0xb8, 0xc4,
	0x23, 0x4c, 0x34, 0xa9, 0xaa, 0xcb, 0x45, 0xea, 0xb9, 0xd2, 0x9d, 0xe7, 0xbe, 0x86, 0x15, 0x42,
	0x4d, 0x7a, 0x19, 0xdc, 0x24, 0x0e, 0x10, 0x52, 0x97, 0xf4, 0x0a, 0xa1, 0xe3, 0xcb, 0xe0, 0x46,
	0xde, 0x3c, 0xfa, 0x12, 0x2a, 0xa9, 0x08, 0x76, 0x1b, 0xca, 0x5e, 0xf2, 0x8b, 0x94, 0x31, 0xe3,
	0x36, 0xc4, 0xda, 0xbf, 0x05, 0x78, 0xf9, 0x89, 0x42, 0x68, 0x88, 0x8e, 0x60, 0x85, 0x7b, 0x45,
	0x98, 0xd6, 0x74, 0x09, 0x65, 0x0d, 0xa5, 0x55, 0x6c, 0x2f, 0xef, 0xbe, 0xca, 0x75, 0xee, 0x83,
	0x81, 0xd1, 0x2b, 0x3c, 0xce, 0xbf, 0x78, 0x46, 0x74, 0x08, 0x15, 0xef, 0xd6, 0x9c, 0x26, 0x14,
	0xe5, 0x3f, 0x3e, 0x17, 0x78, 0xb7, 0x7a, 0x92, 0x0d, 0x6d, 0x42, 0xd9, 0x99, 0x71, 0xd4, 0xa2,
	0x23, 0x8d, 0x91, 0x1a, 0xbd, 0x74, 0x67, 0x74, 0xb4, 0x03, 0x6b, 0x84, 0x9a, 0xb6, 0xe5, 0xf3,
	0x76, 0xe1, 0xe9, 0xb8, 0x2c, 0x88, 0xa6, 0xa9, 0x84, 0xf6, 0x2d, 0xff, 0x90, 0x38, 0x38, 0x9d,
	0x95, 0x6d, 0xa8, 0x11, 0x7a, 0x7f, 0xb6, 0x64, 0x87, 0x57, 0x09, 0x9d, 0x9d, 0xab, 0xf9, 0xc3,
	0x58, 0x9e, 0x3b, 0x8c, 0xda, 0x21, 0x6c, 0x1c, 0x60, 0x76, 0x46, 0x67, 0xcb, 0xfb, 0x7f, 0xdf,
	0x24, 0x4e, 0x28, 0x4c, 0x9d, 0xa0, 0xd9, 0x50, 0xcf, 0xcb, 0x44, 0x43, 0x34, 0x84, 0xf4, 0x09,
	0x92, 0xad, 0x56, 0x9e, 0xd4, 0xea, 0x65, 0xfb, 0x6e, 0xa1, 0x19, 0xd0, 0xd8, 0xb3, 0xd8, 0x67,
	0x29, 0xde, 0x82, 0xa5, 0x98, 0x38, 0xd2, 0x38, 0x85, 0x56, 0xb1, 0x5d, 0xd5, 0xcb, 0x31, 0x71,
	0xb8, 0x17, 0xb4, 0x00, 0xb6, 0xe6, 0x64, 0xa5, 0x21, 0xd2, 0xa1, 0x96, 0x55, 0xff, 0x39, 0xce,
	0x5b, 0xcd, 0x94, 0x20, 0x08, 0x31, 0x6c, 0xf1, 0x5b, 0x16, 0x74, 0xb3, 0x86, 0xc7, 0xd7, 0x39,
	0x0f, 0xfb, 0x6c, 0x65, 0x85, 0xfb, 0x95, 0x6d, 0x42, 0x59, 0x9a, 0x44, 0x1a, 0x70, 0x49, 0x5f,
	0x14, 0xd6, 0xc0, 0xda, 0x0b, 0x68, 0xce, 0xa3, 0xa1, 0xa1, 0xf6, 0x2b, 0xac, 0x8d, 0x31, 0x63,
	0x2e, 0x3e, 0xbb, 0xba, 0xc9, 0xd0, 0x3f, 0xf5, 0xe2, 0x51, 0x03, 0xca, 0x36, 0x8e, 0x98, 0x45,
	0xfc, 0x84, 0x3e, 0x5d, 0x6a, 0x75, 0x58, 0x7f, 0xc8, 0x40, 0xc3, 0xed, 0x3f, 0x14, 0x80, 0x71,
	0x10, 0x47, 0x36, 0xe6, 0xa3, 0x8f, 0xbe, 0x80, 0xcd, 0xf1, 0xc9, 0x99, 0xde, 0x1f, 0x98, 0xc6,
	0xbb, 0xd3, 0x81, 0x79, 0x36, 0x1a, 0x9f, 0x0e, 0xfa, 0xc3, 0x9f, 0x86, 0x83, 0x7d, 0xf5, 0x19,
	0xda, 0x84, 0xb5, 0xec, 0xe6, 0xa9, 0x3e, 0x18, 0x0f, 0x46, 0x86, 0xaa, 0xa0, 0x3a, 0xa0, 0xec,
	0xc6, 0xdb, 0xd1, 0xf0, 0xe0, 0xd0, 0x50, 0x0b, 0x68, 0x03, 0x6a, 0xd9, 0xb8, 0xb1, 0x37, 0xe8,
	0x8d, 0xd4, 0xe2, 0x7d, 0x78, 0xaf, 0x6f, 0x0c, 0xcf, 0x07, 0x6a, 0x69, 0xfb, 0x67, 0x58, 0x3e,
	0x89, 0x1c, 0x1c, 0x8d, 0x99, 0xc5, 0x62, 0xca, 0x61, 0x27, 0xfa, 0xfe, 0x40, 0x37, 0xc7, 0x46,
	0xcf, 0x38, 0x1b, 0x9b, 0xfb, 0x83, 0xa3, 0xde, 0x3b, 0xf5, 0x19, 0x6a, 0xc0, 0xfa, 0x4c, 0xbc,
	0xb7, 0xd7, 0x1b, 0xed, 0x9f, 0x8c, 0x54, 0x85, 0x0b, 0x9c, 0xd9, 0xe9, 0x9f, 0x1c, 0x1f, 0x0f,
	0x0d, 0xb5, 0xb0, 0xfb, 0x4f, 0x09, 0x6a, 0x33, 0xbf, 0x69, 0xfc, 0xc5, 0x40, 0xbf, 0x2b, 0xb0,
	0x35, 0xf7, 0x9d, 0x43, 0xdf, 0xe7, 0x3a, 0xea, 0x53, 0x0f, 0x7c, 0x73, 0xf7, 0xa9, 0x47, 0x68,
	0xa8, 0x3d, 0x43, 0x01, 0xa0, 0x87, 0x7e, 0x47, 0xdb, 0xf3, 0x72, 0x3d, 0x1c, 0xb7, 0xe6, 0x77,
	0x8f, 0xc6, 0x0a, 0xc2, 0x8f, 0xb0, 0x91, 0x3b, 0x63, 0x68, 0x27, 0x37, 0xcf, 0xbc, 0x29, 0x6f,
	0x76, 0x9e, 0x02, 0x17, 0xcc, 0xbf, 0x41, 0x3d, 0x7f, 0x0a, 0x50, 0x7e, 0xae, 0xb9, 0x93, 0xd9,
	0xec, 0x3e, 0x09, 0x2f, 0xc8, 0x09, 0xa8, 0xf7, 0x47, 0x00, 0xb5, 0x73, 0xd3, 0xe4, 0xcc, 0x62,
	0xf3, 0xdb, 0x47, 0x22, 0x39, 0xd5, 0xde, 0x0f, 0xbf, 0xbc, 0xf9, 0x10, 0xb8, 0x96, 0xff, 0xa1,
	0xf3, 0x66, 0x97, 0xb1, 0x8e, 0x1d, 0x78, 0x5d, 0xf1, 0xa7, 0xd2, 0x0e, 0xdc, 0x2e, 0xc5, 0xd1,
	0x84, 0xd8, 0x98, 0xe6, 0xfd, 0xf5, 0x7c, 0xbf, 0x28, 0x60, 0xaf, 0xff, 0x0b, 0x00, 0x00, 0xff,
	0xff, 0xb3, 0x2d, 0xa1, 0xe5, 0xbb, 0x0a, 0x00, 0x00,
}
