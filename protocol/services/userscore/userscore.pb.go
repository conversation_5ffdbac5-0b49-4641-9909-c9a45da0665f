// Code generated by protoc-gen-gogo.
// source: src/userscore/userscore.proto
// DO NOT EDIT!

/*
	Package userscore is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/userscore/userscore.proto

	It has these top-level messages:
		StScoreConfig
		GetScoreConfigReq
		GetScoreConfigResp
		GetScoreConfigUpdateTimeReq
		GetScoreConfigUpdateTimeResp
		SetScoreConfigReq
		GetUserScoreReq
		GetUserScoreResp
		AddUserScoreReq
		AddUserScoreResp
		StUserScoreDetail
		GetUserScoreDetailListReq
		GetUserScoreDetailListResp
		GetUserScoreDetailListByReasonReq
		GetUserScoreDetailListByReasonResp
		GetUserScoreByTableIndexReq
		UserScoreEntry
		GetUserScoreByTableIndexResp
		SetUserExchangeTypeReq
		GetUserExchangeTypeReq
		GetUserExchangeTypeResp
		GetAllUserScoreOrderCountReq
		GetAllUserScoreOrderCountResp
		GetAllUserScoreOrderListReq
		GetAllUserScoreOrderListResp
		StUserScoreOrderLog
		GetOrderLogByOrderIdsReq
		GetOrderLogByOrderIdsResp
*/
package userscore

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type ScoreType int32

const (
	ScoreType_Origin    ScoreType = 0
	ScoreType_TbeanOnly ScoreType = 1
)

var ScoreType_name = map[int32]string{
	0: "Origin",
	1: "TbeanOnly",
}
var ScoreType_value = map[string]int32{
	"Origin":    0,
	"TbeanOnly": 1,
}

func (x ScoreType) String() string {
	return proto.EnumName(ScoreType_name, int32(x))
}
func (ScoreType) EnumDescriptor() ([]byte, []int) { return fileDescriptorUserscore, []int{0} }

type ScoreChangeReason int32

const (
	ScoreChangeReason_REASON_UNKNOWN                    ScoreChangeReason = 0
	ScoreChangeReason_REASON_RECEIVE_PRESENT            ScoreChangeReason = 1
	ScoreChangeReason_REASON_EXCHANGE_RED_DIAMOND       ScoreChangeReason = 2
	ScoreChangeReason_REASON_SETTLEMENT                 ScoreChangeReason = 3
	ScoreChangeReason_REASON_EXCHANGE_TBEANS            ScoreChangeReason = 4
	ScoreChangeReason_REASON_SETTLEMENT_REVERT          ScoreChangeReason = 5
	ScoreChangeReason_REASON_TRIVIAGAME_REWARD          ScoreChangeReason = 6
	ScoreChangeReason_REASON_WITHDRAW_FAILED_ROLL       ScoreChangeReason = 7
	ScoreChangeReason_REASON_EXCHANGE_TCOIN_FAILED_ROLL ScoreChangeReason = 8
	ScoreChangeReason_REASON_OFFICIAL_RECYCLE           ScoreChangeReason = 9
	ScoreChangeReason_REASON_OFFICIAL_REWARD            ScoreChangeReason = 10
	ScoreChangeReason_REASON_GUILD_CHANGE_PRIVATE       ScoreChangeReason = 11
	ScoreChangeReason_REASON_GUILD_QUIT                 ScoreChangeReason = 12
	ScoreChangeReason_REASON_GUILD_OFFICIAL_RECYCLE     ScoreChangeReason = 13
	ScoreChangeReason_REASON_GUILD_EXCHANGE             ScoreChangeReason = 14
	ScoreChangeReason_REASON_WERWOLF_BUY_IDENTITY       ScoreChangeReason = 15
	ScoreChangeReason_REASON_WERWOLF_BUY_TIME           ScoreChangeReason = 16
)

var ScoreChangeReason_name = map[int32]string{
	0:  "REASON_UNKNOWN",
	1:  "REASON_RECEIVE_PRESENT",
	2:  "REASON_EXCHANGE_RED_DIAMOND",
	3:  "REASON_SETTLEMENT",
	4:  "REASON_EXCHANGE_TBEANS",
	5:  "REASON_SETTLEMENT_REVERT",
	6:  "REASON_TRIVIAGAME_REWARD",
	7:  "REASON_WITHDRAW_FAILED_ROLL",
	8:  "REASON_EXCHANGE_TCOIN_FAILED_ROLL",
	9:  "REASON_OFFICIAL_RECYCLE",
	10: "REASON_OFFICIAL_REWARD",
	11: "REASON_GUILD_CHANGE_PRIVATE",
	12: "REASON_GUILD_QUIT",
	13: "REASON_GUILD_OFFICIAL_RECYCLE",
	14: "REASON_GUILD_EXCHANGE",
	15: "REASON_WERWOLF_BUY_IDENTITY",
	16: "REASON_WERWOLF_BUY_TIME",
}
var ScoreChangeReason_value = map[string]int32{
	"REASON_UNKNOWN":                    0,
	"REASON_RECEIVE_PRESENT":            1,
	"REASON_EXCHANGE_RED_DIAMOND":       2,
	"REASON_SETTLEMENT":                 3,
	"REASON_EXCHANGE_TBEANS":            4,
	"REASON_SETTLEMENT_REVERT":          5,
	"REASON_TRIVIAGAME_REWARD":          6,
	"REASON_WITHDRAW_FAILED_ROLL":       7,
	"REASON_EXCHANGE_TCOIN_FAILED_ROLL": 8,
	"REASON_OFFICIAL_RECYCLE":           9,
	"REASON_OFFICIAL_REWARD":            10,
	"REASON_GUILD_CHANGE_PRIVATE":       11,
	"REASON_GUILD_QUIT":                 12,
	"REASON_GUILD_OFFICIAL_RECYCLE":     13,
	"REASON_GUILD_EXCHANGE":             14,
	"REASON_WERWOLF_BUY_IDENTITY":       15,
	"REASON_WERWOLF_BUY_TIME":           16,
}

func (x ScoreChangeReason) String() string {
	return proto.EnumName(ScoreChangeReason_name, int32(x))
}
func (ScoreChangeReason) EnumDescriptor() ([]byte, []int) { return fileDescriptorUserscore, []int{1} }

type StScoreConfig struct {
	Title      string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	UpdateTime uint32 `protobuf:"varint,2,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (m *StScoreConfig) Reset()                    { *m = StScoreConfig{} }
func (m *StScoreConfig) String() string            { return proto.CompactTextString(m) }
func (*StScoreConfig) ProtoMessage()               {}
func (*StScoreConfig) Descriptor() ([]byte, []int) { return fileDescriptorUserscore, []int{0} }

func (m *StScoreConfig) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *StScoreConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

// 获取积分配置
type GetScoreConfigReq struct {
}

func (m *GetScoreConfigReq) Reset()                    { *m = GetScoreConfigReq{} }
func (m *GetScoreConfigReq) String() string            { return proto.CompactTextString(m) }
func (*GetScoreConfigReq) ProtoMessage()               {}
func (*GetScoreConfigReq) Descriptor() ([]byte, []int) { return fileDescriptorUserscore, []int{1} }

type GetScoreConfigResp struct {
	Config *StScoreConfig `protobuf:"bytes,1,opt,name=config" json:"config,omitempty"`
}

func (m *GetScoreConfigResp) Reset()                    { *m = GetScoreConfigResp{} }
func (m *GetScoreConfigResp) String() string            { return proto.CompactTextString(m) }
func (*GetScoreConfigResp) ProtoMessage()               {}
func (*GetScoreConfigResp) Descriptor() ([]byte, []int) { return fileDescriptorUserscore, []int{2} }

func (m *GetScoreConfigResp) GetConfig() *StScoreConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

// 获取积分配置的更新时间
type GetScoreConfigUpdateTimeReq struct {
}

func (m *GetScoreConfigUpdateTimeReq) Reset()         { *m = GetScoreConfigUpdateTimeReq{} }
func (m *GetScoreConfigUpdateTimeReq) String() string { return proto.CompactTextString(m) }
func (*GetScoreConfigUpdateTimeReq) ProtoMessage()    {}
func (*GetScoreConfigUpdateTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserscore, []int{3}
}

type GetScoreConfigUpdateTimeResp struct {
	UpdateTime uint32 `protobuf:"varint,1,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (m *GetScoreConfigUpdateTimeResp) Reset()         { *m = GetScoreConfigUpdateTimeResp{} }
func (m *GetScoreConfigUpdateTimeResp) String() string { return proto.CompactTextString(m) }
func (*GetScoreConfigUpdateTimeResp) ProtoMessage()    {}
func (*GetScoreConfigUpdateTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserscore, []int{4}
}

func (m *GetScoreConfigUpdateTimeResp) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

// 更新积分配置
type SetScoreConfigReq struct {
	Config *StScoreConfig `protobuf:"bytes,1,opt,name=config" json:"config,omitempty"`
}

func (m *SetScoreConfigReq) Reset()                    { *m = SetScoreConfigReq{} }
func (m *SetScoreConfigReq) String() string            { return proto.CompactTextString(m) }
func (*SetScoreConfigReq) ProtoMessage()               {}
func (*SetScoreConfigReq) Descriptor() ([]byte, []int) { return fileDescriptorUserscore, []int{5} }

func (m *SetScoreConfigReq) GetConfig() *StScoreConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

// 获取用户的积分
type GetUserScoreReq struct {
	Uid       uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ScoreType uint32 `protobuf:"varint,2,opt,name=score_type,json=scoreType,proto3" json:"score_type,omitempty"`
}

func (m *GetUserScoreReq) Reset()                    { *m = GetUserScoreReq{} }
func (m *GetUserScoreReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserScoreReq) ProtoMessage()               {}
func (*GetUserScoreReq) Descriptor() ([]byte, []int) { return fileDescriptorUserscore, []int{6} }

func (m *GetUserScoreReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserScoreReq) GetScoreType() uint32 {
	if m != nil {
		return m.ScoreType
	}
	return 0
}

type GetUserScoreResp struct {
	Score uint32 `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"`
}

func (m *GetUserScoreResp) Reset()                    { *m = GetUserScoreResp{} }
func (m *GetUserScoreResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserScoreResp) ProtoMessage()               {}
func (*GetUserScoreResp) Descriptor() ([]byte, []int) { return fileDescriptorUserscore, []int{7} }

func (m *GetUserScoreResp) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

// 增加用户积分
type AddUserScoreReq struct {
	Uid          uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AddScore     int32  `protobuf:"varint,2,opt,name=add_score,json=addScore,proto3" json:"add_score,omitempty"`
	OrderId      string `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	OpUid        uint32 `protobuf:"varint,4,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	ChangeReason uint32 `protobuf:"varint,5,opt,name=change_reason,json=changeReason,proto3" json:"change_reason,omitempty"`
	OrderDesc    string `protobuf:"bytes,6,opt,name=order_desc,json=orderDesc,proto3" json:"order_desc,omitempty"`
	Extend       []byte `protobuf:"bytes,7,opt,name=extend,proto3" json:"extend,omitempty"`
	TimeValue    uint32 `protobuf:"varint,8,opt,name=time_value,json=timeValue,proto3" json:"time_value,omitempty"`
	DealToken    string `protobuf:"bytes,9,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	ScoreType    uint32 `protobuf:"varint,10,opt,name=score_type,json=scoreType,proto3" json:"score_type,omitempty"`
}

func (m *AddUserScoreReq) Reset()                    { *m = AddUserScoreReq{} }
func (m *AddUserScoreReq) String() string            { return proto.CompactTextString(m) }
func (*AddUserScoreReq) ProtoMessage()               {}
func (*AddUserScoreReq) Descriptor() ([]byte, []int) { return fileDescriptorUserscore, []int{8} }

func (m *AddUserScoreReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserScoreReq) GetAddScore() int32 {
	if m != nil {
		return m.AddScore
	}
	return 0
}

func (m *AddUserScoreReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AddUserScoreReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *AddUserScoreReq) GetChangeReason() uint32 {
	if m != nil {
		return m.ChangeReason
	}
	return 0
}

func (m *AddUserScoreReq) GetOrderDesc() string {
	if m != nil {
		return m.OrderDesc
	}
	return ""
}

func (m *AddUserScoreReq) GetExtend() []byte {
	if m != nil {
		return m.Extend
	}
	return nil
}

func (m *AddUserScoreReq) GetTimeValue() uint32 {
	if m != nil {
		return m.TimeValue
	}
	return 0
}

func (m *AddUserScoreReq) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

func (m *AddUserScoreReq) GetScoreType() uint32 {
	if m != nil {
		return m.ScoreType
	}
	return 0
}

type AddUserScoreResp struct {
	FinalScore uint32 `protobuf:"varint,1,opt,name=final_score,json=finalScore,proto3" json:"final_score,omitempty"`
}

func (m *AddUserScoreResp) Reset()                    { *m = AddUserScoreResp{} }
func (m *AddUserScoreResp) String() string            { return proto.CompactTextString(m) }
func (*AddUserScoreResp) ProtoMessage()               {}
func (*AddUserScoreResp) Descriptor() ([]byte, []int) { return fileDescriptorUserscore, []int{9} }

func (m *AddUserScoreResp) GetFinalScore() uint32 {
	if m != nil {
		return m.FinalScore
	}
	return 0
}

type StUserScoreDetail struct {
	Uid          uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId      string `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ChangeScore  int32  `protobuf:"varint,3,opt,name=change_score,json=changeScore,proto3" json:"change_score,omitempty"`
	ChangeReason uint32 `protobuf:"varint,4,opt,name=change_reason,json=changeReason,proto3" json:"change_reason,omitempty"`
	OpUid        uint32 `protobuf:"varint,5,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	CreateTime   uint32 `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	ScoreType    uint32 `protobuf:"varint,7,opt,name=score_type,json=scoreType,proto3" json:"score_type,omitempty"`
}

func (m *StUserScoreDetail) Reset()                    { *m = StUserScoreDetail{} }
func (m *StUserScoreDetail) String() string            { return proto.CompactTextString(m) }
func (*StUserScoreDetail) ProtoMessage()               {}
func (*StUserScoreDetail) Descriptor() ([]byte, []int) { return fileDescriptorUserscore, []int{10} }

func (m *StUserScoreDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StUserScoreDetail) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *StUserScoreDetail) GetChangeScore() int32 {
	if m != nil {
		return m.ChangeScore
	}
	return 0
}

func (m *StUserScoreDetail) GetChangeReason() uint32 {
	if m != nil {
		return m.ChangeReason
	}
	return 0
}

func (m *StUserScoreDetail) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *StUserScoreDetail) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *StUserScoreDetail) GetScoreType() uint32 {
	if m != nil {
		return m.ScoreType
	}
	return 0
}

// 获取用户的积分明细
type GetUserScoreDetailListReq struct {
	Uid    uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset uint32 `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit  uint32 `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (m *GetUserScoreDetailListReq) Reset()         { *m = GetUserScoreDetailListReq{} }
func (m *GetUserScoreDetailListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserScoreDetailListReq) ProtoMessage()    {}
func (*GetUserScoreDetailListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserscore, []int{11}
}

func (m *GetUserScoreDetailListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserScoreDetailListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetUserScoreDetailListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetUserScoreDetailListResp struct {
	DetailList []*StUserScoreDetail `protobuf:"bytes,1,rep,name=detail_list,json=detailList" json:"detail_list,omitempty"`
}

func (m *GetUserScoreDetailListResp) Reset()         { *m = GetUserScoreDetailListResp{} }
func (m *GetUserScoreDetailListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserScoreDetailListResp) ProtoMessage()    {}
func (*GetUserScoreDetailListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserscore, []int{12}
}

func (m *GetUserScoreDetailListResp) GetDetailList() []*StUserScoreDetail {
	if m != nil {
		return m.DetailList
	}
	return nil
}

// 根据reason获取用户的积分明细
type GetUserScoreDetailListByReasonReq struct {
	Uid              uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset           uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit            uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	ChangeReasonList []uint32 `protobuf:"varint,4,rep,packed,name=change_reason_list,json=changeReasonList" json:"change_reason_list,omitempty"`
}

func (m *GetUserScoreDetailListByReasonReq) Reset()         { *m = GetUserScoreDetailListByReasonReq{} }
func (m *GetUserScoreDetailListByReasonReq) String() string { return proto.CompactTextString(m) }
func (*GetUserScoreDetailListByReasonReq) ProtoMessage()    {}
func (*GetUserScoreDetailListByReasonReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserscore, []int{13}
}

func (m *GetUserScoreDetailListByReasonReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserScoreDetailListByReasonReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetUserScoreDetailListByReasonReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetUserScoreDetailListByReasonReq) GetChangeReasonList() []uint32 {
	if m != nil {
		return m.ChangeReasonList
	}
	return nil
}

type GetUserScoreDetailListByReasonResp struct {
	DetailList []*StUserScoreDetail `protobuf:"bytes,1,rep,name=detail_list,json=detailList" json:"detail_list,omitempty"`
}

func (m *GetUserScoreDetailListByReasonResp) Reset()         { *m = GetUserScoreDetailListByReasonResp{} }
func (m *GetUserScoreDetailListByReasonResp) String() string { return proto.CompactTextString(m) }
func (*GetUserScoreDetailListByReasonResp) ProtoMessage()    {}
func (*GetUserScoreDetailListByReasonResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserscore, []int{14}
}

func (m *GetUserScoreDetailListByReasonResp) GetDetailList() []*StUserScoreDetail {
	if m != nil {
		return m.DetailList
	}
	return nil
}

type GetUserScoreByTableIndexReq struct {
	Index        uint32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	CurrentMonth string `protobuf:"bytes,2,opt,name=current_month,json=currentMonth,proto3" json:"current_month,omitempty"`
}

func (m *GetUserScoreByTableIndexReq) Reset()         { *m = GetUserScoreByTableIndexReq{} }
func (m *GetUserScoreByTableIndexReq) String() string { return proto.CompactTextString(m) }
func (*GetUserScoreByTableIndexReq) ProtoMessage()    {}
func (*GetUserScoreByTableIndexReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserscore, []int{15}
}

func (m *GetUserScoreByTableIndexReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *GetUserScoreByTableIndexReq) GetCurrentMonth() string {
	if m != nil {
		return m.CurrentMonth
	}
	return ""
}

type UserScoreEntry struct {
	Uid   uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Score uint32 `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
}

func (m *UserScoreEntry) Reset()                    { *m = UserScoreEntry{} }
func (m *UserScoreEntry) String() string            { return proto.CompactTextString(m) }
func (*UserScoreEntry) ProtoMessage()               {}
func (*UserScoreEntry) Descriptor() ([]byte, []int) { return fileDescriptorUserscore, []int{16} }

func (m *UserScoreEntry) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserScoreEntry) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type GetUserScoreByTableIndexResp struct {
	ScoreList []*UserScoreEntry `protobuf:"bytes,1,rep,name=score_list,json=scoreList" json:"score_list,omitempty"`
}

func (m *GetUserScoreByTableIndexResp) Reset()         { *m = GetUserScoreByTableIndexResp{} }
func (m *GetUserScoreByTableIndexResp) String() string { return proto.CompactTextString(m) }
func (*GetUserScoreByTableIndexResp) ProtoMessage()    {}
func (*GetUserScoreByTableIndexResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserscore, []int{17}
}

func (m *GetUserScoreByTableIndexResp) GetScoreList() []*UserScoreEntry {
	if m != nil {
		return m.ScoreList
	}
	return nil
}

type SetUserExchangeTypeReq struct {
	Uid          uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ExchangeType uint32 `protobuf:"varint,2,opt,name=exchange_type,json=exchangeType,proto3" json:"exchange_type,omitempty"`
}

func (m *SetUserExchangeTypeReq) Reset()                    { *m = SetUserExchangeTypeReq{} }
func (m *SetUserExchangeTypeReq) String() string            { return proto.CompactTextString(m) }
func (*SetUserExchangeTypeReq) ProtoMessage()               {}
func (*SetUserExchangeTypeReq) Descriptor() ([]byte, []int) { return fileDescriptorUserscore, []int{18} }

func (m *SetUserExchangeTypeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserExchangeTypeReq) GetExchangeType() uint32 {
	if m != nil {
		return m.ExchangeType
	}
	return 0
}

type GetUserExchangeTypeReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (m *GetUserExchangeTypeReq) Reset()                    { *m = GetUserExchangeTypeReq{} }
func (m *GetUserExchangeTypeReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserExchangeTypeReq) ProtoMessage()               {}
func (*GetUserExchangeTypeReq) Descriptor() ([]byte, []int) { return fileDescriptorUserscore, []int{19} }

func (m *GetUserExchangeTypeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserExchangeTypeResp struct {
	Uid          uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ExchangeType uint32 `protobuf:"varint,2,opt,name=exchange_type,json=exchangeType,proto3" json:"exchange_type,omitempty"`
}

func (m *GetUserExchangeTypeResp) Reset()         { *m = GetUserExchangeTypeResp{} }
func (m *GetUserExchangeTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetUserExchangeTypeResp) ProtoMessage()    {}
func (*GetUserExchangeTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserscore, []int{20}
}

func (m *GetUserExchangeTypeResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserExchangeTypeResp) GetExchangeType() uint32 {
	if m != nil {
		return m.ExchangeType
	}
	return 0
}

// (对账用)根据指定时间段及reason获取订单数
type GetAllUserScoreOrderCountReq struct {
	ChangeReason uint32 `protobuf:"varint,1,opt,name=change_reason,json=changeReason,proto3" json:"change_reason,omitempty"`
	BeginTime    uint32 `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime      uint32 `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (m *GetAllUserScoreOrderCountReq) Reset()         { *m = GetAllUserScoreOrderCountReq{} }
func (m *GetAllUserScoreOrderCountReq) String() string { return proto.CompactTextString(m) }
func (*GetAllUserScoreOrderCountReq) ProtoMessage()    {}
func (*GetAllUserScoreOrderCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserscore, []int{21}
}

func (m *GetAllUserScoreOrderCountReq) GetChangeReason() uint32 {
	if m != nil {
		return m.ChangeReason
	}
	return 0
}

func (m *GetAllUserScoreOrderCountReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetAllUserScoreOrderCountReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetAllUserScoreOrderCountResp struct {
	TotalCount uint32 `protobuf:"varint,1,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (m *GetAllUserScoreOrderCountResp) Reset()         { *m = GetAllUserScoreOrderCountResp{} }
func (m *GetAllUserScoreOrderCountResp) String() string { return proto.CompactTextString(m) }
func (*GetAllUserScoreOrderCountResp) ProtoMessage()    {}
func (*GetAllUserScoreOrderCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserscore, []int{22}
}

func (m *GetAllUserScoreOrderCountResp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

// (对账用)指定时间段及reason获取订单id列表
type GetAllUserScoreOrderListReq struct {
	ChangeReason uint32 `protobuf:"varint,1,opt,name=change_reason,json=changeReason,proto3" json:"change_reason,omitempty"`
	BeginTime    uint32 `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime      uint32 `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (m *GetAllUserScoreOrderListReq) Reset()         { *m = GetAllUserScoreOrderListReq{} }
func (m *GetAllUserScoreOrderListReq) String() string { return proto.CompactTextString(m) }
func (*GetAllUserScoreOrderListReq) ProtoMessage()    {}
func (*GetAllUserScoreOrderListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserscore, []int{23}
}

func (m *GetAllUserScoreOrderListReq) GetChangeReason() uint32 {
	if m != nil {
		return m.ChangeReason
	}
	return 0
}

func (m *GetAllUserScoreOrderListReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetAllUserScoreOrderListReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetAllUserScoreOrderListResp struct {
	Orders []string `protobuf:"bytes,1,rep,name=orders" json:"orders,omitempty"`
}

func (m *GetAllUserScoreOrderListResp) Reset()         { *m = GetAllUserScoreOrderListResp{} }
func (m *GetAllUserScoreOrderListResp) String() string { return proto.CompactTextString(m) }
func (*GetAllUserScoreOrderListResp) ProtoMessage()    {}
func (*GetAllUserScoreOrderListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserscore, []int{24}
}

func (m *GetAllUserScoreOrderListResp) GetOrders() []string {
	if m != nil {
		return m.Orders
	}
	return nil
}

type StUserScoreOrderLog struct {
	Uid          uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId      string `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ChangeScore  int32  `protobuf:"varint,3,opt,name=change_score,json=changeScore,proto3" json:"change_score,omitempty"`
	ChangeReason uint32 `protobuf:"varint,4,opt,name=change_reason,json=changeReason,proto3" json:"change_reason,omitempty"`
	OpUid        uint32 `protobuf:"varint,5,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	CreateTime   uint32 `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Extend       []byte `protobuf:"bytes,7,opt,name=extend,proto3" json:"extend,omitempty"`
	TimeValue    uint32 `protobuf:"varint,8,opt,name=time_value,json=timeValue,proto3" json:"time_value,omitempty"`
}

func (m *StUserScoreOrderLog) Reset()                    { *m = StUserScoreOrderLog{} }
func (m *StUserScoreOrderLog) String() string            { return proto.CompactTextString(m) }
func (*StUserScoreOrderLog) ProtoMessage()               {}
func (*StUserScoreOrderLog) Descriptor() ([]byte, []int) { return fileDescriptorUserscore, []int{25} }

func (m *StUserScoreOrderLog) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StUserScoreOrderLog) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *StUserScoreOrderLog) GetChangeScore() int32 {
	if m != nil {
		return m.ChangeScore
	}
	return 0
}

func (m *StUserScoreOrderLog) GetChangeReason() uint32 {
	if m != nil {
		return m.ChangeReason
	}
	return 0
}

func (m *StUserScoreOrderLog) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *StUserScoreOrderLog) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *StUserScoreOrderLog) GetExtend() []byte {
	if m != nil {
		return m.Extend
	}
	return nil
}

func (m *StUserScoreOrderLog) GetTimeValue() uint32 {
	if m != nil {
		return m.TimeValue
	}
	return 0
}

type GetOrderLogByOrderIdsReq struct {
	OrderIdList []string `protobuf:"bytes,1,rep,name=order_id_list,json=orderIdList" json:"order_id_list,omitempty"`
}

func (m *GetOrderLogByOrderIdsReq) Reset()         { *m = GetOrderLogByOrderIdsReq{} }
func (m *GetOrderLogByOrderIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetOrderLogByOrderIdsReq) ProtoMessage()    {}
func (*GetOrderLogByOrderIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserscore, []int{26}
}

func (m *GetOrderLogByOrderIdsReq) GetOrderIdList() []string {
	if m != nil {
		return m.OrderIdList
	}
	return nil
}

// (对账用) 根据orderId获取订单
type GetOrderLogByOrderIdsResp struct {
	OrderLogList []*StUserScoreOrderLog `protobuf:"bytes,1,rep,name=order_log_list,json=orderLogList" json:"order_log_list,omitempty"`
}

func (m *GetOrderLogByOrderIdsResp) Reset()         { *m = GetOrderLogByOrderIdsResp{} }
func (m *GetOrderLogByOrderIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetOrderLogByOrderIdsResp) ProtoMessage()    {}
func (*GetOrderLogByOrderIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserscore, []int{27}
}

func (m *GetOrderLogByOrderIdsResp) GetOrderLogList() []*StUserScoreOrderLog {
	if m != nil {
		return m.OrderLogList
	}
	return nil
}

func init() {
	proto.RegisterType((*StScoreConfig)(nil), "userscore.StScoreConfig")
	proto.RegisterType((*GetScoreConfigReq)(nil), "userscore.GetScoreConfigReq")
	proto.RegisterType((*GetScoreConfigResp)(nil), "userscore.GetScoreConfigResp")
	proto.RegisterType((*GetScoreConfigUpdateTimeReq)(nil), "userscore.GetScoreConfigUpdateTimeReq")
	proto.RegisterType((*GetScoreConfigUpdateTimeResp)(nil), "userscore.GetScoreConfigUpdateTimeResp")
	proto.RegisterType((*SetScoreConfigReq)(nil), "userscore.SetScoreConfigReq")
	proto.RegisterType((*GetUserScoreReq)(nil), "userscore.GetUserScoreReq")
	proto.RegisterType((*GetUserScoreResp)(nil), "userscore.GetUserScoreResp")
	proto.RegisterType((*AddUserScoreReq)(nil), "userscore.AddUserScoreReq")
	proto.RegisterType((*AddUserScoreResp)(nil), "userscore.AddUserScoreResp")
	proto.RegisterType((*StUserScoreDetail)(nil), "userscore.StUserScoreDetail")
	proto.RegisterType((*GetUserScoreDetailListReq)(nil), "userscore.GetUserScoreDetailListReq")
	proto.RegisterType((*GetUserScoreDetailListResp)(nil), "userscore.GetUserScoreDetailListResp")
	proto.RegisterType((*GetUserScoreDetailListByReasonReq)(nil), "userscore.GetUserScoreDetailListByReasonReq")
	proto.RegisterType((*GetUserScoreDetailListByReasonResp)(nil), "userscore.GetUserScoreDetailListByReasonResp")
	proto.RegisterType((*GetUserScoreByTableIndexReq)(nil), "userscore.GetUserScoreByTableIndexReq")
	proto.RegisterType((*UserScoreEntry)(nil), "userscore.UserScoreEntry")
	proto.RegisterType((*GetUserScoreByTableIndexResp)(nil), "userscore.GetUserScoreByTableIndexResp")
	proto.RegisterType((*SetUserExchangeTypeReq)(nil), "userscore.SetUserExchangeTypeReq")
	proto.RegisterType((*GetUserExchangeTypeReq)(nil), "userscore.GetUserExchangeTypeReq")
	proto.RegisterType((*GetUserExchangeTypeResp)(nil), "userscore.GetUserExchangeTypeResp")
	proto.RegisterType((*GetAllUserScoreOrderCountReq)(nil), "userscore.GetAllUserScoreOrderCountReq")
	proto.RegisterType((*GetAllUserScoreOrderCountResp)(nil), "userscore.GetAllUserScoreOrderCountResp")
	proto.RegisterType((*GetAllUserScoreOrderListReq)(nil), "userscore.GetAllUserScoreOrderListReq")
	proto.RegisterType((*GetAllUserScoreOrderListResp)(nil), "userscore.GetAllUserScoreOrderListResp")
	proto.RegisterType((*StUserScoreOrderLog)(nil), "userscore.StUserScoreOrderLog")
	proto.RegisterType((*GetOrderLogByOrderIdsReq)(nil), "userscore.GetOrderLogByOrderIdsReq")
	proto.RegisterType((*GetOrderLogByOrderIdsResp)(nil), "userscore.GetOrderLogByOrderIdsResp")
	proto.RegisterEnum("userscore.ScoreType", ScoreType_name, ScoreType_value)
	proto.RegisterEnum("userscore.ScoreChangeReason", ScoreChangeReason_name, ScoreChangeReason_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for UserScore service

type UserScoreClient interface {
	GetScoreConfig(ctx context.Context, in *GetScoreConfigReq, opts ...grpc.CallOption) (*GetScoreConfigResp, error)
	GetScoreConfigUpdateTime(ctx context.Context, in *GetScoreConfigUpdateTimeReq, opts ...grpc.CallOption) (*GetScoreConfigUpdateTimeResp, error)
	SetScoreConfig(ctx context.Context, in *SetScoreConfigReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetUserScore(ctx context.Context, in *GetUserScoreReq, opts ...grpc.CallOption) (*GetUserScoreResp, error)
	GetUserScoreDetailList(ctx context.Context, in *GetUserScoreDetailListReq, opts ...grpc.CallOption) (*GetUserScoreDetailListResp, error)
	AddUserScore(ctx context.Context, in *AddUserScoreReq, opts ...grpc.CallOption) (*AddUserScoreResp, error)
	GetUserScoreByTableIndex(ctx context.Context, in *GetUserScoreByTableIndexReq, opts ...grpc.CallOption) (*GetUserScoreByTableIndexResp, error)
	SetUserExchangeType(ctx context.Context, in *SetUserExchangeTypeReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetUserExchangeType(ctx context.Context, in *GetUserExchangeTypeReq, opts ...grpc.CallOption) (*GetUserExchangeTypeResp, error)
	GetUserScoreDetailListByReason(ctx context.Context, in *GetUserScoreDetailListByReasonReq, opts ...grpc.CallOption) (*GetUserScoreDetailListByReasonResp, error)
	GetAllUserScoreOrderCount(ctx context.Context, in *GetAllUserScoreOrderCountReq, opts ...grpc.CallOption) (*GetAllUserScoreOrderCountResp, error)
	GetAllUserScoreOrderList(ctx context.Context, in *GetAllUserScoreOrderListReq, opts ...grpc.CallOption) (*GetAllUserScoreOrderListResp, error)
	GetOrderLogByOrderIds(ctx context.Context, in *GetOrderLogByOrderIdsReq, opts ...grpc.CallOption) (*GetOrderLogByOrderIdsResp, error)
}

type userScoreClient struct {
	cc *grpc.ClientConn
}

func NewUserScoreClient(cc *grpc.ClientConn) UserScoreClient {
	return &userScoreClient{cc}
}

func (c *userScoreClient) GetScoreConfig(ctx context.Context, in *GetScoreConfigReq, opts ...grpc.CallOption) (*GetScoreConfigResp, error) {
	out := new(GetScoreConfigResp)
	err := grpc.Invoke(ctx, "/userscore.UserScore/GetScoreConfig", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreClient) GetScoreConfigUpdateTime(ctx context.Context, in *GetScoreConfigUpdateTimeReq, opts ...grpc.CallOption) (*GetScoreConfigUpdateTimeResp, error) {
	out := new(GetScoreConfigUpdateTimeResp)
	err := grpc.Invoke(ctx, "/userscore.UserScore/GetScoreConfigUpdateTime", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreClient) SetScoreConfig(ctx context.Context, in *SetScoreConfigReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/userscore.UserScore/SetScoreConfig", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreClient) GetUserScore(ctx context.Context, in *GetUserScoreReq, opts ...grpc.CallOption) (*GetUserScoreResp, error) {
	out := new(GetUserScoreResp)
	err := grpc.Invoke(ctx, "/userscore.UserScore/GetUserScore", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreClient) GetUserScoreDetailList(ctx context.Context, in *GetUserScoreDetailListReq, opts ...grpc.CallOption) (*GetUserScoreDetailListResp, error) {
	out := new(GetUserScoreDetailListResp)
	err := grpc.Invoke(ctx, "/userscore.UserScore/GetUserScoreDetailList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreClient) AddUserScore(ctx context.Context, in *AddUserScoreReq, opts ...grpc.CallOption) (*AddUserScoreResp, error) {
	out := new(AddUserScoreResp)
	err := grpc.Invoke(ctx, "/userscore.UserScore/AddUserScore", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreClient) GetUserScoreByTableIndex(ctx context.Context, in *GetUserScoreByTableIndexReq, opts ...grpc.CallOption) (*GetUserScoreByTableIndexResp, error) {
	out := new(GetUserScoreByTableIndexResp)
	err := grpc.Invoke(ctx, "/userscore.UserScore/GetUserScoreByTableIndex", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreClient) SetUserExchangeType(ctx context.Context, in *SetUserExchangeTypeReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/userscore.UserScore/SetUserExchangeType", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreClient) GetUserExchangeType(ctx context.Context, in *GetUserExchangeTypeReq, opts ...grpc.CallOption) (*GetUserExchangeTypeResp, error) {
	out := new(GetUserExchangeTypeResp)
	err := grpc.Invoke(ctx, "/userscore.UserScore/GetUserExchangeType", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreClient) GetUserScoreDetailListByReason(ctx context.Context, in *GetUserScoreDetailListByReasonReq, opts ...grpc.CallOption) (*GetUserScoreDetailListByReasonResp, error) {
	out := new(GetUserScoreDetailListByReasonResp)
	err := grpc.Invoke(ctx, "/userscore.UserScore/GetUserScoreDetailListByReason", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreClient) GetAllUserScoreOrderCount(ctx context.Context, in *GetAllUserScoreOrderCountReq, opts ...grpc.CallOption) (*GetAllUserScoreOrderCountResp, error) {
	out := new(GetAllUserScoreOrderCountResp)
	err := grpc.Invoke(ctx, "/userscore.UserScore/GetAllUserScoreOrderCount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreClient) GetAllUserScoreOrderList(ctx context.Context, in *GetAllUserScoreOrderListReq, opts ...grpc.CallOption) (*GetAllUserScoreOrderListResp, error) {
	out := new(GetAllUserScoreOrderListResp)
	err := grpc.Invoke(ctx, "/userscore.UserScore/GetAllUserScoreOrderList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreClient) GetOrderLogByOrderIds(ctx context.Context, in *GetOrderLogByOrderIdsReq, opts ...grpc.CallOption) (*GetOrderLogByOrderIdsResp, error) {
	out := new(GetOrderLogByOrderIdsResp)
	err := grpc.Invoke(ctx, "/userscore.UserScore/GetOrderLogByOrderIds", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for UserScore service

type UserScoreServer interface {
	GetScoreConfig(context.Context, *GetScoreConfigReq) (*GetScoreConfigResp, error)
	GetScoreConfigUpdateTime(context.Context, *GetScoreConfigUpdateTimeReq) (*GetScoreConfigUpdateTimeResp, error)
	SetScoreConfig(context.Context, *SetScoreConfigReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetUserScore(context.Context, *GetUserScoreReq) (*GetUserScoreResp, error)
	GetUserScoreDetailList(context.Context, *GetUserScoreDetailListReq) (*GetUserScoreDetailListResp, error)
	AddUserScore(context.Context, *AddUserScoreReq) (*AddUserScoreResp, error)
	GetUserScoreByTableIndex(context.Context, *GetUserScoreByTableIndexReq) (*GetUserScoreByTableIndexResp, error)
	SetUserExchangeType(context.Context, *SetUserExchangeTypeReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetUserExchangeType(context.Context, *GetUserExchangeTypeReq) (*GetUserExchangeTypeResp, error)
	GetUserScoreDetailListByReason(context.Context, *GetUserScoreDetailListByReasonReq) (*GetUserScoreDetailListByReasonResp, error)
	GetAllUserScoreOrderCount(context.Context, *GetAllUserScoreOrderCountReq) (*GetAllUserScoreOrderCountResp, error)
	GetAllUserScoreOrderList(context.Context, *GetAllUserScoreOrderListReq) (*GetAllUserScoreOrderListResp, error)
	GetOrderLogByOrderIds(context.Context, *GetOrderLogByOrderIdsReq) (*GetOrderLogByOrderIdsResp, error)
}

func RegisterUserScoreServer(s *grpc.Server, srv UserScoreServer) {
	s.RegisterService(&_UserScore_serviceDesc, srv)
}

func _UserScore_GetScoreConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScoreConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreServer).GetScoreConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore.UserScore/GetScoreConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreServer).GetScoreConfig(ctx, req.(*GetScoreConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScore_GetScoreConfigUpdateTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScoreConfigUpdateTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreServer).GetScoreConfigUpdateTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore.UserScore/GetScoreConfigUpdateTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreServer).GetScoreConfigUpdateTime(ctx, req.(*GetScoreConfigUpdateTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScore_SetScoreConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetScoreConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreServer).SetScoreConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore.UserScore/SetScoreConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreServer).SetScoreConfig(ctx, req.(*SetScoreConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScore_GetUserScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserScoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreServer).GetUserScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore.UserScore/GetUserScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreServer).GetUserScore(ctx, req.(*GetUserScoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScore_GetUserScoreDetailList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserScoreDetailListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreServer).GetUserScoreDetailList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore.UserScore/GetUserScoreDetailList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreServer).GetUserScoreDetailList(ctx, req.(*GetUserScoreDetailListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScore_AddUserScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserScoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreServer).AddUserScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore.UserScore/AddUserScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreServer).AddUserScore(ctx, req.(*AddUserScoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScore_GetUserScoreByTableIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserScoreByTableIndexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreServer).GetUserScoreByTableIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore.UserScore/GetUserScoreByTableIndex",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreServer).GetUserScoreByTableIndex(ctx, req.(*GetUserScoreByTableIndexReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScore_SetUserExchangeType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserExchangeTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreServer).SetUserExchangeType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore.UserScore/SetUserExchangeType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreServer).SetUserExchangeType(ctx, req.(*SetUserExchangeTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScore_GetUserExchangeType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserExchangeTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreServer).GetUserExchangeType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore.UserScore/GetUserExchangeType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreServer).GetUserExchangeType(ctx, req.(*GetUserExchangeTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScore_GetUserScoreDetailListByReason_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserScoreDetailListByReasonReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreServer).GetUserScoreDetailListByReason(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore.UserScore/GetUserScoreDetailListByReason",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreServer).GetUserScoreDetailListByReason(ctx, req.(*GetUserScoreDetailListByReasonReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScore_GetAllUserScoreOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllUserScoreOrderCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreServer).GetAllUserScoreOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore.UserScore/GetAllUserScoreOrderCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreServer).GetAllUserScoreOrderCount(ctx, req.(*GetAllUserScoreOrderCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScore_GetAllUserScoreOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllUserScoreOrderListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreServer).GetAllUserScoreOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore.UserScore/GetAllUserScoreOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreServer).GetAllUserScoreOrderList(ctx, req.(*GetAllUserScoreOrderListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScore_GetOrderLogByOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderLogByOrderIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreServer).GetOrderLogByOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore.UserScore/GetOrderLogByOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreServer).GetOrderLogByOrderIds(ctx, req.(*GetOrderLogByOrderIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserScore_serviceDesc = grpc.ServiceDesc{
	ServiceName: "userscore.UserScore",
	HandlerType: (*UserScoreServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetScoreConfig",
			Handler:    _UserScore_GetScoreConfig_Handler,
		},
		{
			MethodName: "GetScoreConfigUpdateTime",
			Handler:    _UserScore_GetScoreConfigUpdateTime_Handler,
		},
		{
			MethodName: "SetScoreConfig",
			Handler:    _UserScore_SetScoreConfig_Handler,
		},
		{
			MethodName: "GetUserScore",
			Handler:    _UserScore_GetUserScore_Handler,
		},
		{
			MethodName: "GetUserScoreDetailList",
			Handler:    _UserScore_GetUserScoreDetailList_Handler,
		},
		{
			MethodName: "AddUserScore",
			Handler:    _UserScore_AddUserScore_Handler,
		},
		{
			MethodName: "GetUserScoreByTableIndex",
			Handler:    _UserScore_GetUserScoreByTableIndex_Handler,
		},
		{
			MethodName: "SetUserExchangeType",
			Handler:    _UserScore_SetUserExchangeType_Handler,
		},
		{
			MethodName: "GetUserExchangeType",
			Handler:    _UserScore_GetUserExchangeType_Handler,
		},
		{
			MethodName: "GetUserScoreDetailListByReason",
			Handler:    _UserScore_GetUserScoreDetailListByReason_Handler,
		},
		{
			MethodName: "GetAllUserScoreOrderCount",
			Handler:    _UserScore_GetAllUserScoreOrderCount_Handler,
		},
		{
			MethodName: "GetAllUserScoreOrderList",
			Handler:    _UserScore_GetAllUserScoreOrderList_Handler,
		},
		{
			MethodName: "GetOrderLogByOrderIds",
			Handler:    _UserScore_GetOrderLogByOrderIds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/userscore/userscore.proto",
}

func (m *StScoreConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StScoreConfig) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Title) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(len(m.Title)))
		i += copy(dAtA[i:], m.Title)
	}
	if m.UpdateTime != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.UpdateTime))
	}
	return i, nil
}

func (m *GetScoreConfigReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetScoreConfigReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetScoreConfigResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetScoreConfigResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Config != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.Config.Size()))
		n1, err := m.Config.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *GetScoreConfigUpdateTimeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetScoreConfigUpdateTimeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetScoreConfigUpdateTimeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetScoreConfigUpdateTimeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UpdateTime != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.UpdateTime))
	}
	return i, nil
}

func (m *SetScoreConfigReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetScoreConfigReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Config != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.Config.Size()))
		n2, err := m.Config.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *GetUserScoreReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserScoreReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.Uid))
	}
	if m.ScoreType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.ScoreType))
	}
	return i, nil
}

func (m *GetUserScoreResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserScoreResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Score != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.Score))
	}
	return i, nil
}

func (m *AddUserScoreReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserScoreReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.Uid))
	}
	if m.AddScore != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.AddScore))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if m.OpUid != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.OpUid))
	}
	if m.ChangeReason != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.ChangeReason))
	}
	if len(m.OrderDesc) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(len(m.OrderDesc)))
		i += copy(dAtA[i:], m.OrderDesc)
	}
	if len(m.Extend) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(len(m.Extend)))
		i += copy(dAtA[i:], m.Extend)
	}
	if m.TimeValue != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.TimeValue))
	}
	if len(m.DealToken) > 0 {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(len(m.DealToken)))
		i += copy(dAtA[i:], m.DealToken)
	}
	if m.ScoreType != 0 {
		dAtA[i] = 0x50
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.ScoreType))
	}
	return i, nil
}

func (m *AddUserScoreResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserScoreResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.FinalScore != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.FinalScore))
	}
	return i, nil
}

func (m *StUserScoreDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StUserScoreDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.Uid))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if m.ChangeScore != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.ChangeScore))
	}
	if m.ChangeReason != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.ChangeReason))
	}
	if m.OpUid != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.OpUid))
	}
	if m.CreateTime != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.CreateTime))
	}
	if m.ScoreType != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.ScoreType))
	}
	return i, nil
}

func (m *GetUserScoreDetailListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserScoreDetailListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.Uid))
	}
	if m.Offset != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.Offset))
	}
	if m.Limit != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.Limit))
	}
	return i, nil
}

func (m *GetUserScoreDetailListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserScoreDetailListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.DetailList) > 0 {
		for _, msg := range m.DetailList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintUserscore(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserScoreDetailListByReasonReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserScoreDetailListByReasonReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.Uid))
	}
	if m.Offset != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.Offset))
	}
	if m.Limit != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.Limit))
	}
	if len(m.ChangeReasonList) > 0 {
		dAtA4 := make([]byte, len(m.ChangeReasonList)*10)
		var j3 int
		for _, num := range m.ChangeReasonList {
			for num >= 1<<7 {
				dAtA4[j3] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j3++
			}
			dAtA4[j3] = uint8(num)
			j3++
		}
		dAtA[i] = 0x22
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(j3))
		i += copy(dAtA[i:], dAtA4[:j3])
	}
	return i, nil
}

func (m *GetUserScoreDetailListByReasonResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserScoreDetailListByReasonResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.DetailList) > 0 {
		for _, msg := range m.DetailList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintUserscore(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserScoreByTableIndexReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserScoreByTableIndexReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Index != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.Index))
	}
	if len(m.CurrentMonth) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(len(m.CurrentMonth)))
		i += copy(dAtA[i:], m.CurrentMonth)
	}
	return i, nil
}

func (m *UserScoreEntry) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserScoreEntry) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.Uid))
	}
	if m.Score != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.Score))
	}
	return i, nil
}

func (m *GetUserScoreByTableIndexResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserScoreByTableIndexResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ScoreList) > 0 {
		for _, msg := range m.ScoreList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintUserscore(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetUserExchangeTypeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserExchangeTypeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.Uid))
	}
	if m.ExchangeType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.ExchangeType))
	}
	return i, nil
}

func (m *GetUserExchangeTypeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserExchangeTypeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.Uid))
	}
	return i, nil
}

func (m *GetUserExchangeTypeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserExchangeTypeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.Uid))
	}
	if m.ExchangeType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.ExchangeType))
	}
	return i, nil
}

func (m *GetAllUserScoreOrderCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllUserScoreOrderCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ChangeReason != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.ChangeReason))
	}
	if m.BeginTime != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.EndTime))
	}
	return i, nil
}

func (m *GetAllUserScoreOrderCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllUserScoreOrderCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TotalCount != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.TotalCount))
	}
	return i, nil
}

func (m *GetAllUserScoreOrderListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllUserScoreOrderListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ChangeReason != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.ChangeReason))
	}
	if m.BeginTime != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.EndTime))
	}
	return i, nil
}

func (m *GetAllUserScoreOrderListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllUserScoreOrderListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Orders) > 0 {
		for _, s := range m.Orders {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *StUserScoreOrderLog) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StUserScoreOrderLog) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.Uid))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if m.ChangeScore != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.ChangeScore))
	}
	if m.ChangeReason != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.ChangeReason))
	}
	if m.OpUid != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.OpUid))
	}
	if m.CreateTime != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.CreateTime))
	}
	if len(m.Extend) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(len(m.Extend)))
		i += copy(dAtA[i:], m.Extend)
	}
	if m.TimeValue != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintUserscore(dAtA, i, uint64(m.TimeValue))
	}
	return i, nil
}

func (m *GetOrderLogByOrderIdsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOrderLogByOrderIdsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.OrderIdList) > 0 {
		for _, s := range m.OrderIdList {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *GetOrderLogByOrderIdsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOrderLogByOrderIdsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.OrderLogList) > 0 {
		for _, msg := range m.OrderLogList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintUserscore(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func encodeFixed64Userscore(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Userscore(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintUserscore(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *StScoreConfig) Size() (n int) {
	var l int
	_ = l
	l = len(m.Title)
	if l > 0 {
		n += 1 + l + sovUserscore(uint64(l))
	}
	if m.UpdateTime != 0 {
		n += 1 + sovUserscore(uint64(m.UpdateTime))
	}
	return n
}

func (m *GetScoreConfigReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetScoreConfigResp) Size() (n int) {
	var l int
	_ = l
	if m.Config != nil {
		l = m.Config.Size()
		n += 1 + l + sovUserscore(uint64(l))
	}
	return n
}

func (m *GetScoreConfigUpdateTimeReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetScoreConfigUpdateTimeResp) Size() (n int) {
	var l int
	_ = l
	if m.UpdateTime != 0 {
		n += 1 + sovUserscore(uint64(m.UpdateTime))
	}
	return n
}

func (m *SetScoreConfigReq) Size() (n int) {
	var l int
	_ = l
	if m.Config != nil {
		l = m.Config.Size()
		n += 1 + l + sovUserscore(uint64(l))
	}
	return n
}

func (m *GetUserScoreReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovUserscore(uint64(m.Uid))
	}
	if m.ScoreType != 0 {
		n += 1 + sovUserscore(uint64(m.ScoreType))
	}
	return n
}

func (m *GetUserScoreResp) Size() (n int) {
	var l int
	_ = l
	if m.Score != 0 {
		n += 1 + sovUserscore(uint64(m.Score))
	}
	return n
}

func (m *AddUserScoreReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovUserscore(uint64(m.Uid))
	}
	if m.AddScore != 0 {
		n += 1 + sovUserscore(uint64(m.AddScore))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovUserscore(uint64(l))
	}
	if m.OpUid != 0 {
		n += 1 + sovUserscore(uint64(m.OpUid))
	}
	if m.ChangeReason != 0 {
		n += 1 + sovUserscore(uint64(m.ChangeReason))
	}
	l = len(m.OrderDesc)
	if l > 0 {
		n += 1 + l + sovUserscore(uint64(l))
	}
	l = len(m.Extend)
	if l > 0 {
		n += 1 + l + sovUserscore(uint64(l))
	}
	if m.TimeValue != 0 {
		n += 1 + sovUserscore(uint64(m.TimeValue))
	}
	l = len(m.DealToken)
	if l > 0 {
		n += 1 + l + sovUserscore(uint64(l))
	}
	if m.ScoreType != 0 {
		n += 1 + sovUserscore(uint64(m.ScoreType))
	}
	return n
}

func (m *AddUserScoreResp) Size() (n int) {
	var l int
	_ = l
	if m.FinalScore != 0 {
		n += 1 + sovUserscore(uint64(m.FinalScore))
	}
	return n
}

func (m *StUserScoreDetail) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovUserscore(uint64(m.Uid))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovUserscore(uint64(l))
	}
	if m.ChangeScore != 0 {
		n += 1 + sovUserscore(uint64(m.ChangeScore))
	}
	if m.ChangeReason != 0 {
		n += 1 + sovUserscore(uint64(m.ChangeReason))
	}
	if m.OpUid != 0 {
		n += 1 + sovUserscore(uint64(m.OpUid))
	}
	if m.CreateTime != 0 {
		n += 1 + sovUserscore(uint64(m.CreateTime))
	}
	if m.ScoreType != 0 {
		n += 1 + sovUserscore(uint64(m.ScoreType))
	}
	return n
}

func (m *GetUserScoreDetailListReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovUserscore(uint64(m.Uid))
	}
	if m.Offset != 0 {
		n += 1 + sovUserscore(uint64(m.Offset))
	}
	if m.Limit != 0 {
		n += 1 + sovUserscore(uint64(m.Limit))
	}
	return n
}

func (m *GetUserScoreDetailListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.DetailList) > 0 {
		for _, e := range m.DetailList {
			l = e.Size()
			n += 1 + l + sovUserscore(uint64(l))
		}
	}
	return n
}

func (m *GetUserScoreDetailListByReasonReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovUserscore(uint64(m.Uid))
	}
	if m.Offset != 0 {
		n += 1 + sovUserscore(uint64(m.Offset))
	}
	if m.Limit != 0 {
		n += 1 + sovUserscore(uint64(m.Limit))
	}
	if len(m.ChangeReasonList) > 0 {
		l = 0
		for _, e := range m.ChangeReasonList {
			l += sovUserscore(uint64(e))
		}
		n += 1 + sovUserscore(uint64(l)) + l
	}
	return n
}

func (m *GetUserScoreDetailListByReasonResp) Size() (n int) {
	var l int
	_ = l
	if len(m.DetailList) > 0 {
		for _, e := range m.DetailList {
			l = e.Size()
			n += 1 + l + sovUserscore(uint64(l))
		}
	}
	return n
}

func (m *GetUserScoreByTableIndexReq) Size() (n int) {
	var l int
	_ = l
	if m.Index != 0 {
		n += 1 + sovUserscore(uint64(m.Index))
	}
	l = len(m.CurrentMonth)
	if l > 0 {
		n += 1 + l + sovUserscore(uint64(l))
	}
	return n
}

func (m *UserScoreEntry) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovUserscore(uint64(m.Uid))
	}
	if m.Score != 0 {
		n += 1 + sovUserscore(uint64(m.Score))
	}
	return n
}

func (m *GetUserScoreByTableIndexResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ScoreList) > 0 {
		for _, e := range m.ScoreList {
			l = e.Size()
			n += 1 + l + sovUserscore(uint64(l))
		}
	}
	return n
}

func (m *SetUserExchangeTypeReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovUserscore(uint64(m.Uid))
	}
	if m.ExchangeType != 0 {
		n += 1 + sovUserscore(uint64(m.ExchangeType))
	}
	return n
}

func (m *GetUserExchangeTypeReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovUserscore(uint64(m.Uid))
	}
	return n
}

func (m *GetUserExchangeTypeResp) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovUserscore(uint64(m.Uid))
	}
	if m.ExchangeType != 0 {
		n += 1 + sovUserscore(uint64(m.ExchangeType))
	}
	return n
}

func (m *GetAllUserScoreOrderCountReq) Size() (n int) {
	var l int
	_ = l
	if m.ChangeReason != 0 {
		n += 1 + sovUserscore(uint64(m.ChangeReason))
	}
	if m.BeginTime != 0 {
		n += 1 + sovUserscore(uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		n += 1 + sovUserscore(uint64(m.EndTime))
	}
	return n
}

func (m *GetAllUserScoreOrderCountResp) Size() (n int) {
	var l int
	_ = l
	if m.TotalCount != 0 {
		n += 1 + sovUserscore(uint64(m.TotalCount))
	}
	return n
}

func (m *GetAllUserScoreOrderListReq) Size() (n int) {
	var l int
	_ = l
	if m.ChangeReason != 0 {
		n += 1 + sovUserscore(uint64(m.ChangeReason))
	}
	if m.BeginTime != 0 {
		n += 1 + sovUserscore(uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		n += 1 + sovUserscore(uint64(m.EndTime))
	}
	return n
}

func (m *GetAllUserScoreOrderListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.Orders) > 0 {
		for _, s := range m.Orders {
			l = len(s)
			n += 1 + l + sovUserscore(uint64(l))
		}
	}
	return n
}

func (m *StUserScoreOrderLog) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovUserscore(uint64(m.Uid))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovUserscore(uint64(l))
	}
	if m.ChangeScore != 0 {
		n += 1 + sovUserscore(uint64(m.ChangeScore))
	}
	if m.ChangeReason != 0 {
		n += 1 + sovUserscore(uint64(m.ChangeReason))
	}
	if m.OpUid != 0 {
		n += 1 + sovUserscore(uint64(m.OpUid))
	}
	if m.CreateTime != 0 {
		n += 1 + sovUserscore(uint64(m.CreateTime))
	}
	l = len(m.Extend)
	if l > 0 {
		n += 1 + l + sovUserscore(uint64(l))
	}
	if m.TimeValue != 0 {
		n += 1 + sovUserscore(uint64(m.TimeValue))
	}
	return n
}

func (m *GetOrderLogByOrderIdsReq) Size() (n int) {
	var l int
	_ = l
	if len(m.OrderIdList) > 0 {
		for _, s := range m.OrderIdList {
			l = len(s)
			n += 1 + l + sovUserscore(uint64(l))
		}
	}
	return n
}

func (m *GetOrderLogByOrderIdsResp) Size() (n int) {
	var l int
	_ = l
	if len(m.OrderLogList) > 0 {
		for _, e := range m.OrderLogList {
			l = e.Size()
			n += 1 + l + sovUserscore(uint64(l))
		}
	}
	return n
}

func sovUserscore(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozUserscore(x uint64) (n int) {
	return sovUserscore(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *StScoreConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StScoreConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StScoreConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserscore
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetScoreConfigReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetScoreConfigReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetScoreConfigReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetScoreConfigResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetScoreConfigResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetScoreConfigResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Config", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserscore
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Config == nil {
				m.Config = &StScoreConfig{}
			}
			if err := m.Config.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetScoreConfigUpdateTimeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetScoreConfigUpdateTimeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetScoreConfigUpdateTimeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetScoreConfigUpdateTimeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetScoreConfigUpdateTimeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetScoreConfigUpdateTimeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetScoreConfigReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetScoreConfigReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetScoreConfigReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Config", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserscore
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Config == nil {
				m.Config = &StScoreConfig{}
			}
			if err := m.Config.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserScoreReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserScoreReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserScoreReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ScoreType", wireType)
			}
			m.ScoreType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ScoreType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserScoreResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserScoreResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserScoreResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			m.Score = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Score |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserScoreReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserScoreReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserScoreReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AddScore", wireType)
			}
			m.AddScore = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AddScore |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserscore
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChangeReason", wireType)
			}
			m.ChangeReason = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChangeReason |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserscore
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Extend", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthUserscore
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Extend = append(m.Extend[:0], dAtA[iNdEx:postIndex]...)
			if m.Extend == nil {
				m.Extend = []byte{}
			}
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimeValue", wireType)
			}
			m.TimeValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DealToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserscore
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DealToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ScoreType", wireType)
			}
			m.ScoreType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ScoreType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserScoreResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserScoreResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserScoreResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinalScore", wireType)
			}
			m.FinalScore = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinalScore |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StUserScoreDetail) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StUserScoreDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StUserScoreDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserscore
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChangeScore", wireType)
			}
			m.ChangeScore = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChangeScore |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChangeReason", wireType)
			}
			m.ChangeReason = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChangeReason |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ScoreType", wireType)
			}
			m.ScoreType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ScoreType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserScoreDetailListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserScoreDetailListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserScoreDetailListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserScoreDetailListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserScoreDetailListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserScoreDetailListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DetailList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserscore
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DetailList = append(m.DetailList, &StUserScoreDetail{})
			if err := m.DetailList[len(m.DetailList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserScoreDetailListByReasonReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserScoreDetailListByReasonReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserScoreDetailListByReasonReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserscore
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChangeReasonList = append(m.ChangeReasonList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUserscore
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthUserscore
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowUserscore
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChangeReasonList = append(m.ChangeReasonList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChangeReasonList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserScoreDetailListByReasonResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserScoreDetailListByReasonResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserScoreDetailListByReasonResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DetailList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserscore
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DetailList = append(m.DetailList, &StUserScoreDetail{})
			if err := m.DetailList[len(m.DetailList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserScoreByTableIndexReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserScoreByTableIndexReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserScoreByTableIndexReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrentMonth", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserscore
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CurrentMonth = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserScoreEntry) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserScoreEntry: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserScoreEntry: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			m.Score = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Score |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserScoreByTableIndexResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserScoreByTableIndexResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserScoreByTableIndexResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ScoreList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserscore
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ScoreList = append(m.ScoreList, &UserScoreEntry{})
			if err := m.ScoreList[len(m.ScoreList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserExchangeTypeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserExchangeTypeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserExchangeTypeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeType", wireType)
			}
			m.ExchangeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExchangeType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserExchangeTypeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserExchangeTypeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserExchangeTypeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserExchangeTypeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserExchangeTypeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserExchangeTypeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeType", wireType)
			}
			m.ExchangeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExchangeType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllUserScoreOrderCountReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllUserScoreOrderCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllUserScoreOrderCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChangeReason", wireType)
			}
			m.ChangeReason = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChangeReason |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllUserScoreOrderCountResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllUserScoreOrderCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllUserScoreOrderCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalCount", wireType)
			}
			m.TotalCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllUserScoreOrderListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllUserScoreOrderListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllUserScoreOrderListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChangeReason", wireType)
			}
			m.ChangeReason = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChangeReason |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllUserScoreOrderListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllUserScoreOrderListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllUserScoreOrderListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Orders", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserscore
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Orders = append(m.Orders, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StUserScoreOrderLog) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StUserScoreOrderLog: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StUserScoreOrderLog: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserscore
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChangeScore", wireType)
			}
			m.ChangeScore = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChangeScore |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChangeReason", wireType)
			}
			m.ChangeReason = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChangeReason |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Extend", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthUserscore
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Extend = append(m.Extend[:0], dAtA[iNdEx:postIndex]...)
			if m.Extend == nil {
				m.Extend = []byte{}
			}
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimeValue", wireType)
			}
			m.TimeValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOrderLogByOrderIdsReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOrderLogByOrderIdsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOrderLogByOrderIdsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderIdList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserscore
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderIdList = append(m.OrderIdList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOrderLogByOrderIdsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOrderLogByOrderIdsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOrderLogByOrderIdsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderLogList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserscore
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderLogList = append(m.OrderLogList, &StUserScoreOrderLog{})
			if err := m.OrderLogList[len(m.OrderLogList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserscore(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserscore
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipUserscore(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowUserscore
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserscore
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthUserscore
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowUserscore
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipUserscore(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthUserscore = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowUserscore   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/userscore/userscore.proto", fileDescriptorUserscore) }

var fileDescriptorUserscore = []byte{
	// 1803 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x58, 0x51, 0x6f, 0xdb, 0xc8,
	0x11, 0x0e, 0x2d, 0x5b, 0xb6, 0x46, 0x92, 0x4d, 0x6f, 0x2e, 0x3e, 0x59, 0x8e, 0x1d, 0x9a, 0x49,
	0xee, 0x8c, 0xe0, 0xe4, 0x5c, 0x73, 0xc5, 0xe1, 0x20, 0xb8, 0x6a, 0x25, 0x8b, 0xf6, 0x11, 0x91,
	0xa5, 0x94, 0xa2, 0xed, 0x04, 0xc1, 0x95, 0x90, 0xc4, 0x8d, 0x8f, 0x08, 0x45, 0xb2, 0x22, 0x75,
	0x8d, 0x80, 0x3b, 0xa0, 0x40, 0x81, 0xe2, 0x70, 0x4f, 0x6d, 0x5f, 0x8b, 0xbe, 0xe5, 0xad, 0x8f,
	0x45, 0xff, 0x43, 0xd1, 0xa7, 0xfb, 0x07, 0x6d, 0xd3, 0x97, 0xfc, 0x85, 0xbe, 0x15, 0xbb, 0x4b,
	0x49, 0x4b, 0x8a, 0x8a, 0x9d, 0xb4, 0x7d, 0xe8, 0x8b, 0xc0, 0x9d, 0x9d, 0x9d, 0xf9, 0x66, 0x76,
	0x66, 0x76, 0x46, 0xb0, 0xed, 0x0f, 0x7a, 0xf7, 0x87, 0x3e, 0x1e, 0xf8, 0x3d, 0x77, 0x80, 0xa7,
	0x5f, 0xfb, 0xde, 0xc0, 0x0d, 0x5c, 0x94, 0x99, 0x10, 0x8a, 0x77, 0x7a, 0x6e, 0xbf, 0xef, 0x3a,
	0xf7, 0x03, 0xfb, 0x2b, 0xcf, 0xea, 0x3d, 0xb7, 0xf1, 0x7d, 0xff, 0x79, 0x77, 0x68, 0xd9, 0x81,
	0xe5, 0x04, 0x23, 0x2f, 0x3c, 0x20, 0x1f, 0x41, 0xbe, 0x1d, 0xb4, 0xc9, 0x81, 0x43, 0xd7, 0x79,
	0x66, 0x5d, 0xa0, 0xf7, 0x60, 0x29, 0xb0, 0x02, 0x1b, 0x17, 0x04, 0x49, 0xd8, 0xcb, 0x68, 0x6c,
	0x81, 0x6e, 0x41, 0x76, 0xe8, 0x99, 0x9d, 0x00, 0x1b, 0x81, 0xd5, 0xc7, 0x85, 0x05, 0x49, 0xd8,
	0xcb, 0x6b, 0xc0, 0x48, 0xba, 0xd5, 0xc7, 0xf2, 0x75, 0x58, 0x3f, 0xc6, 0xbc, 0x20, 0x0d, 0xff,
	0x5c, 0x3e, 0x02, 0x14, 0x27, 0xfa, 0x1e, 0xfa, 0x18, 0xd2, 0x3d, 0xba, 0xa2, 0x2a, 0xb2, 0x0f,
	0x0a, 0xfb, 0x53, 0x2b, 0x22, 0x58, 0xb4, 0x90, 0x4f, 0xde, 0x86, 0xad, 0xa8, 0x9c, 0xd3, 0x89,
	0x62, 0xa2, 0xe6, 0xc7, 0x70, 0x73, 0xfe, 0xb6, 0xef, 0xc5, 0xc1, 0x0b, 0x33, 0xe0, 0x15, 0x58,
	0x6f, 0xc7, 0xc1, 0xbf, 0x03, 0xcc, 0x1a, 0xac, 0x1d, 0xe3, 0xe0, 0xd4, 0xc7, 0x03, 0xba, 0x4b,
	0x84, 0x88, 0x90, 0x1a, 0x5a, 0x66, 0xa8, 0x92, 0x7c, 0xa2, 0x6d, 0x00, 0x2a, 0xc3, 0x20, 0x97,
	0x10, 0x3a, 0x32, 0x43, 0x29, 0xfa, 0xc8, 0xc3, 0xf2, 0x1e, 0x88, 0x51, 0x19, 0xbe, 0x47, 0xae,
	0x84, 0x32, 0x84, 0x62, 0xd8, 0x42, 0xfe, 0xe3, 0x02, 0xac, 0x55, 0x4d, 0xf3, 0x12, 0x75, 0x5b,
	0x90, 0xe9, 0x98, 0xa6, 0xc1, 0xce, 0x13, 0x6d, 0x4b, 0xda, 0x4a, 0xc7, 0x34, 0xe9, 0x09, 0xb4,
	0x09, 0x2b, 0xee, 0xc0, 0xc4, 0x03, 0xc3, 0x32, 0x0b, 0x29, 0x7a, 0xdd, 0xcb, 0x74, 0xad, 0x9a,
	0xe8, 0x06, 0xa4, 0x5d, 0xcf, 0x20, 0xc2, 0x16, 0x99, 0x52, 0xd7, 0x3b, 0xb5, 0x4c, 0x74, 0x1b,
	0xf2, 0xbd, 0x2f, 0x3b, 0xce, 0x05, 0x36, 0x06, 0xb8, 0xe3, 0xbb, 0x4e, 0x61, 0x89, 0xee, 0xe6,
	0x18, 0x51, 0xa3, 0x34, 0x62, 0x22, 0x13, 0x6b, 0x62, 0xbf, 0x57, 0x48, 0x53, 0xc1, 0x19, 0x4a,
	0xa9, 0x63, 0xbf, 0x87, 0x36, 0x20, 0x8d, 0x5f, 0x04, 0xd8, 0x31, 0x0b, 0xcb, 0x92, 0xb0, 0x97,
	0xd3, 0xc2, 0x15, 0x39, 0x46, 0xee, 0xc7, 0xf8, 0xaa, 0x63, 0x0f, 0x71, 0x61, 0x85, 0x79, 0x86,
	0x50, 0xce, 0x08, 0x81, 0x6c, 0x9b, 0xb8, 0x63, 0x1b, 0x81, 0xfb, 0x1c, 0x3b, 0x85, 0x0c, 0x93,
	0x4a, 0x28, 0x3a, 0x21, 0xc4, 0xfc, 0x0a, 0x71, 0xbf, 0x7e, 0x02, 0x62, 0xd4, 0x59, 0x2c, 0x2e,
	0x9e, 0x59, 0x4e, 0xc7, 0x36, 0x78, 0xef, 0x02, 0x25, 0x51, 0x26, 0xf9, 0x6f, 0x02, 0xac, 0xb7,
	0xa7, 0x97, 0x51, 0xc7, 0x41, 0xc7, 0xb2, 0x13, 0x9c, 0xcc, 0xfb, 0x71, 0x21, 0xea, 0xc7, 0x5d,
	0x08, 0x7d, 0x13, 0x2a, 0x49, 0xd1, 0x2b, 0xc8, 0x32, 0x1a, 0xbb, 0x85, 0x19, 0x9f, 0x2e, 0x26,
	0xf8, 0x74, 0x7a, 0x1f, 0x4b, 0xfc, 0x7d, 0xdc, 0x82, 0x6c, 0x6f, 0x80, 0x27, 0xa1, 0x9d, 0x66,
	0x26, 0x30, 0x12, 0x09, 0xed, 0x98, 0x5b, 0x96, 0xe3, 0x6e, 0x79, 0x0a, 0x9b, 0x7c, 0xb8, 0x31,
	0x0b, 0x1b, 0x96, 0x1f, 0x24, 0x47, 0xd3, 0x06, 0xa4, 0xdd, 0x67, 0xcf, 0x7c, 0x1c, 0x84, 0x81,
	0x1b, 0xae, 0x48, 0x84, 0xda, 0x56, 0xdf, 0x0a, 0xa8, 0x79, 0x79, 0x8d, 0x2d, 0xe4, 0xa7, 0x50,
	0x9c, 0x27, 0xdc, 0xf7, 0xd0, 0x8f, 0x20, 0x6b, 0x52, 0x8a, 0x61, 0x5b, 0x7e, 0x50, 0x10, 0xa4,
	0xd4, 0x5e, 0xf6, 0xc1, 0xcd, 0x48, 0x92, 0xc5, 0x8e, 0x6a, 0x60, 0x4e, 0x44, 0xc8, 0xbf, 0x15,
	0x60, 0x37, 0x59, 0x7a, 0x6d, 0xc4, 0x7c, 0xf6, 0x5f, 0x30, 0x01, 0x7d, 0x04, 0x28, 0x72, 0x37,
	0x0c, 0xeb, 0xa2, 0x94, 0xda, 0xcb, 0x6b, 0x22, 0x7f, 0x41, 0x14, 0x53, 0x0f, 0xe4, 0xcb, 0x20,
	0xfd, 0xe7, 0x86, 0x3f, 0xa6, 0xc5, 0x70, 0xc2, 0x51, 0x1b, 0xe9, 0x9d, 0xae, 0x8d, 0x55, 0xc7,
	0xc4, 0x2f, 0x88, 0xc5, 0xef, 0xc1, 0x92, 0x45, 0xbe, 0xc7, 0xc5, 0x82, 0x2e, 0x68, 0x8c, 0x0d,
	0x07, 0x03, 0xec, 0x04, 0x46, 0xdf, 0x75, 0x82, 0x2f, 0xc3, 0x30, 0xcd, 0x85, 0xc4, 0x13, 0x42,
	0x93, 0x3f, 0x83, 0xd5, 0x89, 0x58, 0xc5, 0x09, 0x06, 0xa3, 0x04, 0xf7, 0x4d, 0x6a, 0xd1, 0x02,
	0x5f, 0x8b, 0x1e, 0xd3, 0x0a, 0x3c, 0x07, 0x93, 0xef, 0xa1, 0xcf, 0xc6, 0x51, 0xc8, 0x59, 0xbc,
	0xc9, 0x59, 0x1c, 0x55, 0x1b, 0x06, 0x28, 0xb5, 0xb6, 0x05, 0x1b, 0x6d, 0x26, 0x59, 0x79, 0xc1,
	0xfc, 0x4d, 0xe2, 0x36, 0xf9, 0x6a, 0x6f, 0x43, 0x1e, 0x87, 0x4c, 0x7c, 0x75, 0xcd, 0x61, 0xee,
	0xa4, 0x7c, 0x0f, 0x36, 0x8e, 0xaf, 0x28, 0x50, 0x7e, 0x04, 0xef, 0x27, 0xf2, 0xfa, 0xde, 0xbb,
	0x6a, 0xff, 0x86, 0x3a, 0xaa, 0x6a, 0xdb, 0x13, 0x8b, 0x5b, 0xa4, 0x50, 0x1c, 0xba, 0x43, 0x87,
	0xa6, 0xdc, 0x4c, 0x2d, 0x10, 0x92, 0xeb, 0x6b, 0x17, 0x5f, 0x58, 0x0e, 0xff, 0x16, 0x67, 0x28,
	0x85, 0xa6, 0xfc, 0x26, 0xac, 0x60, 0xc7, 0x64, 0x9b, 0x2c, 0x98, 0x97, 0xb1, 0x63, 0xd2, 0x87,
	0xee, 0x27, 0xb0, 0xfd, 0x06, 0xf5, 0xac, 0x24, 0x06, 0x6e, 0xd0, 0xb1, 0x8d, 0x1e, 0x21, 0x8d,
	0x4b, 0x22, 0x25, 0x51, 0x26, 0xf9, 0x6b, 0x1a, 0x7d, 0x33, 0x12, 0xc6, 0x25, 0xe3, 0x7f, 0x8c,
	0xff, 0xd3, 0x64, 0xf7, 0x4d, 0x6a, 0x0a, 0x49, 0x6e, 0x42, 0xf0, 0x69, 0x8c, 0x65, 0xb4, 0x70,
	0x25, 0xff, 0x4b, 0x80, 0xeb, 0x5c, 0x56, 0xb1, 0x43, 0xee, 0xc5, 0xff, 0x57, 0x29, 0x7f, 0xb7,
	0x77, 0x53, 0xae, 0x40, 0xe1, 0x18, 0x07, 0x63, 0x93, 0x6b, 0xa3, 0x16, 0x33, 0xc7, 0x27, 0xd7,
	0x25, 0x43, 0x7e, 0x6c, 0xed, 0x34, 0x35, 0x33, 0x5a, 0x36, 0x34, 0x99, 0x66, 0x60, 0x87, 0x3e,
	0x11, 0x49, 0xe7, 0x7d, 0x0f, 0xd5, 0x61, 0x95, 0x09, 0xb0, 0xdd, 0x0b, 0x3e, 0xb9, 0x77, 0x92,
	0xcb, 0xd9, 0x58, 0x8a, 0x96, 0x73, 0xc3, 0x2f, 0xa2, 0xe2, 0xde, 0x07, 0x90, 0x69, 0x8f, 0x9f,
	0x24, 0x04, 0x90, 0x6e, 0x0d, 0xac, 0x0b, 0xcb, 0x11, 0xaf, 0xa1, 0x3c, 0x64, 0xf4, 0x2e, 0xee,
	0x38, 0x2d, 0xc7, 0x1e, 0x89, 0xc2, 0xbd, 0x6f, 0x17, 0x61, 0x9d, 0x35, 0x5e, 0xbc, 0x3f, 0x11,
	0xac, 0x6a, 0x4a, 0xb5, 0xdd, 0x6a, 0x1a, 0xa7, 0xcd, 0x87, 0xcd, 0xd6, 0x79, 0x53, 0xbc, 0x86,
	0x8a, 0xb0, 0x11, 0xd2, 0x34, 0xe5, 0x50, 0x51, 0xcf, 0x14, 0xe3, 0x91, 0xa6, 0xb4, 0x95, 0xa6,
	0x2e, 0x0a, 0xe8, 0x16, 0x6c, 0x85, 0x7b, 0xca, 0xe3, 0xc3, 0xcf, 0xab, 0xcd, 0x63, 0xc5, 0xd0,
	0x94, 0xba, 0x51, 0x57, 0xab, 0x27, 0xad, 0x66, 0x5d, 0x5c, 0x40, 0x37, 0x60, 0x3d, 0x64, 0x68,
	0x2b, 0xba, 0xde, 0x50, 0x4e, 0xc8, 0xb9, 0x14, 0x27, 0x73, 0x72, 0x4e, 0xaf, 0x29, 0xd5, 0x66,
	0x5b, 0x5c, 0x44, 0x37, 0xa1, 0x30, 0x73, 0xc4, 0xd0, 0x94, 0x33, 0x45, 0xd3, 0xc5, 0x25, 0x6e,
	0x57, 0xd7, 0xd4, 0x33, 0xb5, 0x7a, 0x5c, 0x3d, 0x21, 0x3a, 0xcf, 0xab, 0x5a, 0x5d, 0x4c, 0x73,
	0x78, 0xce, 0x55, 0xfd, 0xf3, 0xba, 0x56, 0x3d, 0x37, 0x8e, 0xaa, 0x6a, 0x43, 0xa9, 0x1b, 0x5a,
	0xab, 0xd1, 0x10, 0x97, 0xd1, 0x5d, 0xd8, 0x9d, 0x51, 0x7c, 0xd8, 0x52, 0x9b, 0x11, 0xb6, 0x15,
	0xb4, 0x05, 0xef, 0x87, 0x6c, 0xad, 0xa3, 0x23, 0xf5, 0x50, 0xad, 0x36, 0x88, 0xf1, 0x4f, 0x0e,
	0x1b, 0x8a, 0x98, 0xe1, 0xc0, 0x73, 0x9b, 0x14, 0x00, 0x70, 0x00, 0x8e, 0x4f, 0xd5, 0x46, 0xdd,
	0x08, 0x75, 0x3c, 0xd2, 0xd4, 0xb3, 0xaa, 0xae, 0x88, 0x59, 0xce, 0x21, 0x8c, 0xe1, 0xa7, 0xa7,
	0xaa, 0x2e, 0xe6, 0xd0, 0x2e, 0x6c, 0x47, 0xc8, 0x33, 0x6a, 0xf3, 0x68, 0x13, 0x6e, 0x44, 0x58,
	0xc6, 0x06, 0x88, 0xab, 0xbc, 0xd9, 0x8a, 0x76, 0xde, 0x6a, 0x1c, 0x19, 0xb5, 0xd3, 0x27, 0x86,
	0x5a, 0x57, 0x9a, 0xba, 0xaa, 0x3f, 0x11, 0xd7, 0x38, 0x7b, 0x78, 0x06, 0x5d, 0x3d, 0x51, 0x44,
	0xf1, 0xc1, 0x77, 0x6b, 0x90, 0x99, 0x84, 0x15, 0xfa, 0x19, 0xac, 0x46, 0x27, 0x00, 0xc4, 0xbf,
	0xa7, 0x33, 0x83, 0x49, 0x71, 0xfb, 0x0d, 0xbb, 0xbe, 0x27, 0xaf, 0xfd, 0xf2, 0xe5, 0xeb, 0x94,
	0xf0, 0xdd, 0xcb, 0xd7, 0xa9, 0x6b, 0xbf, 0x23, 0x3f, 0xe8, 0x6b, 0x9a, 0x43, 0x89, 0x13, 0x06,
	0xfa, 0x60, 0xae, 0xac, 0xc8, 0x94, 0x52, 0xfc, 0xf0, 0x4a, 0x7c, 0x63, 0xed, 0x0b, 0x9c, 0x76,
	0x0b, 0x56, 0xdb, 0xf3, 0xad, 0x9b, 0x99, 0x5c, 0x8a, 0x37, 0xf7, 0x27, 0x33, 0xdf, 0x7e, 0xfb,
	0x61, 0x8d, 0xcd, 0x7c, 0x4a, 0xdf, 0x0b, 0x46, 0xc6, 0xa3, 0x9a, 0xbc, 0x45, 0xc4, 0xa7, 0x88,
	0xf8, 0x85, 0xa0, 0x4c, 0x14, 0x40, 0x29, 0x90, 0x0e, 0xe8, 0x98, 0x57, 0x41, 0x5d, 0xc8, 0xf1,
	0x0f, 0x39, 0x2a, 0x46, 0x41, 0xf3, 0xc3, 0x46, 0x71, 0x6b, 0xee, 0x9e, 0xef, 0xc9, 0x9b, 0x44,
	0xcb, 0x22, 0xd5, 0x32, 0xa4, 0x5a, 0x56, 0x4a, 0x43, 0xe9, 0x60, 0x68, 0x99, 0x15, 0xf4, 0x07,
	0x61, 0xf2, 0x04, 0xc7, 0xda, 0x24, 0x74, 0x67, 0x8e, 0xc8, 0x48, 0x5f, 0x5a, 0xbc, 0x7b, 0x05,
	0x2e, 0xdf, 0x93, 0x3f, 0x25, 0x10, 0x96, 0x08, 0x84, 0xf4, 0xb0, 0xec, 0x96, 0x1d, 0x0a, 0x63,
	0x77, 0x0c, 0x43, 0x7a, 0x5a, 0x72, 0xa5, 0x03, 0xd6, 0xfc, 0x55, 0xa4, 0x92, 0x23, 0x1d, 0xd0,
	0x8e, 0xaf, 0xf2, 0x05, 0xfa, 0x5e, 0x80, 0x1c, 0x3f, 0x2b, 0x44, 0x9c, 0x10, 0x9b, 0xb8, 0x22,
	0x4e, 0x88, 0x0f, 0x18, 0xf2, 0xaf, 0x05, 0x02, 0x21, 0x4d, 0x20, 0xac, 0x0e, 0xcb, 0x4e, 0x39,
	0x28, 0x7b, 0x65, 0xb7, 0xec, 0x97, 0x31, 0x85, 0x62, 0x4d, 0xa0, 0x10, 0xed, 0xfc, 0xd3, 0x52,
	0x91, 0xc8, 0x95, 0x70, 0xbd, 0x44, 0x45, 0x2a, 0x79, 0xd2, 0x01, 0x7b, 0x35, 0x2a, 0x12, 0x05,
	0x1e, 0x96, 0x6d, 0x62, 0x87, 0x3f, 0x5e, 0x92, 0x79, 0xab, 0x22, 0x95, 0xf0, 0x78, 0xcd, 0x1e,
	0x88, 0xca, 0x17, 0xe8, 0xf7, 0x02, 0x0d, 0xe0, 0xc4, 0x06, 0x2d, 0x1e, 0xc0, 0xf3, 0x3a, 0xcb,
	0x78, 0x00, 0xcf, 0xed, 0xf6, 0xe4, 0x7d, 0x62, 0xf5, 0x32, 0xb1, 0x7a, 0xd1, 0x2a, 0xf7, 0xa9,
	0xad, 0x5b, 0x25, 0x4b, 0x3a, 0x08, 0x08, 0x9b, 0x41, 0x1b, 0xd2, 0x8a, 0x54, 0xea, 0x4b, 0x07,
	0xb4, 0x15, 0xad, 0xa0, 0x6f, 0xe0, 0x7a, 0x42, 0x8f, 0x87, 0x76, 0xa3, 0x41, 0x9e, 0xd0, 0xb2,
	0x5d, 0x12, 0xe9, 0x77, 0x09, 0x8e, 0x15, 0x8a, 0x63, 0x58, 0x66, 0xb1, 0x8e, 0xa6, 0x3e, 0x27,
	0x41, 0x4f, 0x5c, 0x8b, 0x7e, 0x01, 0xd7, 0x8f, 0x2f, 0x51, 0x9f, 0xdc, 0x31, 0x16, 0xe5, 0xcb,
	0x58, 0xc6, 0x89, 0x90, 0x49, 0x4c, 0x84, 0xbf, 0x0a, 0xb0, 0xf3, 0xe6, 0x79, 0x01, 0x7d, 0x74,
	0x69, 0xa8, 0x73, 0xd3, 0x4e, 0xb1, 0xf4, 0x16, 0xdc, 0xbe, 0x27, 0x3f, 0x24, 0xd0, 0x80, 0x40,
	0x5b, 0x61, 0x09, 0x32, 0xa0, 0x00, 0x7f, 0x38, 0xf5, 0xd1, 0x60, 0x12, 0x85, 0xac, 0x9f, 0x79,
	0x43, 0xd6, 0xfc, 0x59, 0xa0, 0x7d, 0x42, 0x72, 0x6f, 0x89, 0x62, 0xb1, 0x33, 0xb7, 0x01, 0x2e,
	0xee, 0x5d, 0x8d, 0xd1, 0xf7, 0x64, 0x85, 0xa0, 0xcf, 0xd2, 0xf4, 0xee, 0x96, 0x71, 0x88, 0xfd,
	0xe3, 0x04, 0xc8, 0xa5, 0xae, 0x74, 0x30, 0xed, 0x37, 0x59, 0x7e, 0x8c, 0x1b, 0xcc, 0x0a, 0xfa,
	0x13, 0x4b, 0x8d, 0xc4, 0x9e, 0x32, 0x9e, 0x1a, 0xf3, 0xda, 0xde, 0xe2, 0x87, 0x57, 0xe2, 0xf3,
	0x3d, 0xf9, 0x88, 0x80, 0xce, 0xc5, 0x40, 0xff, 0x40, 0x7a, 0x6b, 0xd4, 0xbf, 0x12, 0xe0, 0x46,
	0x62, 0x57, 0x86, 0x6e, 0x47, 0xa1, 0x24, 0xf6, 0x7d, 0xc5, 0x3b, 0x97, 0x33, 0xf9, 0x9e, 0x7c,
	0x8b, 0x80, 0xcd, 0xd3, 0xd0, 0x75, 0x29, 0xd0, 0xd5, 0x68, 0xe9, 0x29, 0xa6, 0xbf, 0x7d, 0xf9,
	0x3a, 0xf5, 0x8f, 0x51, 0x4d, 0xfc, 0xcb, 0xab, 0x1d, 0xe1, 0xfb, 0x57, 0x3b, 0xc2, 0xdf, 0x5f,
	0xed, 0x08, 0xbf, 0xf9, 0xe7, 0xce, 0xb5, 0x6e, 0x9a, 0xfe, 0xbb, 0xf8, 0xc9, 0xbf, 0x03, 0x00,
	0x00, 0xff, 0xff, 0x54, 0x17, 0x22, 0x66, 0xaf, 0x14, 0x00, 0x00,
}
