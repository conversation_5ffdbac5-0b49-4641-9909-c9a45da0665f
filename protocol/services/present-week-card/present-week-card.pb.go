// Code generated by protoc-gen-go. DO NOT EDIT.
// source: present-week-card/present-week-card.proto

package present_week_card // import "golang.52tt.com/protocol/services/present-week-card"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 奖励领取状态
type AwardStatusType int32

const (
	AwardStatusType_AWARD_STATUS_TYPE_UNSPECIFIED      AwardStatusType = 0
	AwardStatusType_AWARD_STATUS_TYPE_LOCKING          AwardStatusType = 1
	AwardStatusType_AWARD_STATUS_TYPE_WAIT_TO_RECV     AwardStatusType = 2
	AwardStatusType_AWARD_STATUS_TYPE_ALREADY_RECEIVED AwardStatusType = 3
	AwardStatusType_AWARD_STATUS_TYPE_EXPIRED          AwardStatusType = 4
)

var AwardStatusType_name = map[int32]string{
	0: "AWARD_STATUS_TYPE_UNSPECIFIED",
	1: "AWARD_STATUS_TYPE_LOCKING",
	2: "AWARD_STATUS_TYPE_WAIT_TO_RECV",
	3: "AWARD_STATUS_TYPE_ALREADY_RECEIVED",
	4: "AWARD_STATUS_TYPE_EXPIRED",
}
var AwardStatusType_value = map[string]int32{
	"AWARD_STATUS_TYPE_UNSPECIFIED":      0,
	"AWARD_STATUS_TYPE_LOCKING":          1,
	"AWARD_STATUS_TYPE_WAIT_TO_RECV":     2,
	"AWARD_STATUS_TYPE_ALREADY_RECEIVED": 3,
	"AWARD_STATUS_TYPE_EXPIRED":          4,
}

func (x AwardStatusType) String() string {
	return proto.EnumName(AwardStatusType_name, int32(x))
}
func (AwardStatusType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{0}
}

// 奖励类型
type AwardItemType int32

const (
	AwardItemType_AWARD_ITEM_TYPE_UNSPECIFIED AwardItemType = 0
	AwardItemType_AWARD_ITEM_TYPE_PACKAGE     AwardItemType = 1
	AwardItemType_AWARD_ITEM_TYPE_NOBLE_CARD  AwardItemType = 2
	AwardItemType_AWARD_ITEM_TYPE_MIC_STYLE   AwardItemType = 3
	AwardItemType_AWARD_ITEM_TYPE_HORSE       AwardItemType = 4
	AwardItemType_AWARD_SUPER_PLAYER          AwardItemType = 5
	AwardItemType_AWARD_ITEM_TYPE_DECORATION  AwardItemType = 6
	AwardItemType_AWARD_ITEM_TYPE_NAMEPLATE   AwardItemType = 7
)

var AwardItemType_name = map[int32]string{
	0: "AWARD_ITEM_TYPE_UNSPECIFIED",
	1: "AWARD_ITEM_TYPE_PACKAGE",
	2: "AWARD_ITEM_TYPE_NOBLE_CARD",
	3: "AWARD_ITEM_TYPE_MIC_STYLE",
	4: "AWARD_ITEM_TYPE_HORSE",
	5: "AWARD_SUPER_PLAYER",
	6: "AWARD_ITEM_TYPE_DECORATION",
	7: "AWARD_ITEM_TYPE_NAMEPLATE",
}
var AwardItemType_value = map[string]int32{
	"AWARD_ITEM_TYPE_UNSPECIFIED": 0,
	"AWARD_ITEM_TYPE_PACKAGE":     1,
	"AWARD_ITEM_TYPE_NOBLE_CARD":  2,
	"AWARD_ITEM_TYPE_MIC_STYLE":   3,
	"AWARD_ITEM_TYPE_HORSE":       4,
	"AWARD_SUPER_PLAYER":          5,
	"AWARD_ITEM_TYPE_DECORATION":  6,
	"AWARD_ITEM_TYPE_NAMEPLATE":   7,
}

func (x AwardItemType) String() string {
	return proto.EnumName(AwardItemType_name, int32(x))
}
func (AwardItemType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{1}
}

type PresentWeekCardInfo_BuyStatus int32

const (
	PresentWeekCardInfo_BUY_STATUS_UNSPECIFIED PresentWeekCardInfo_BuyStatus = 0
	PresentWeekCardInfo_BUY_STATUS_NOT_BUY     PresentWeekCardInfo_BuyStatus = 1
	PresentWeekCardInfo_BUY_STATUS_ALREADY_BUY PresentWeekCardInfo_BuyStatus = 2
	PresentWeekCardInfo_BUY_STATUS_LOCKING     PresentWeekCardInfo_BuyStatus = 3
)

var PresentWeekCardInfo_BuyStatus_name = map[int32]string{
	0: "BUY_STATUS_UNSPECIFIED",
	1: "BUY_STATUS_NOT_BUY",
	2: "BUY_STATUS_ALREADY_BUY",
	3: "BUY_STATUS_LOCKING",
}
var PresentWeekCardInfo_BuyStatus_value = map[string]int32{
	"BUY_STATUS_UNSPECIFIED": 0,
	"BUY_STATUS_NOT_BUY":     1,
	"BUY_STATUS_ALREADY_BUY": 2,
	"BUY_STATUS_LOCKING":     3,
}

func (x PresentWeekCardInfo_BuyStatus) String() string {
	return proto.EnumName(PresentWeekCardInfo_BuyStatus_name, int32(x))
}
func (PresentWeekCardInfo_BuyStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{2, 0}
}

type GetPresentWeekCardEntryResp_ReceiveStatus int32

const (
	GetPresentWeekCardEntryResp_RECEIVE_STATUS_UNSPECIFIED        GetPresentWeekCardEntryResp_ReceiveStatus = 0
	GetPresentWeekCardEntryResp_RECEIVE_STATUS_WAIT_TO_RECV       GetPresentWeekCardEntryResp_ReceiveStatus = 1
	GetPresentWeekCardEntryResp_RECEIVE_STATUS_NO_AWARD_AVAILABLE GetPresentWeekCardEntryResp_ReceiveStatus = 2
)

var GetPresentWeekCardEntryResp_ReceiveStatus_name = map[int32]string{
	0: "RECEIVE_STATUS_UNSPECIFIED",
	1: "RECEIVE_STATUS_WAIT_TO_RECV",
	2: "RECEIVE_STATUS_NO_AWARD_AVAILABLE",
}
var GetPresentWeekCardEntryResp_ReceiveStatus_value = map[string]int32{
	"RECEIVE_STATUS_UNSPECIFIED":        0,
	"RECEIVE_STATUS_WAIT_TO_RECV":       1,
	"RECEIVE_STATUS_NO_AWARD_AVAILABLE": 2,
}

func (x GetPresentWeekCardEntryResp_ReceiveStatus) String() string {
	return proto.EnumName(GetPresentWeekCardEntryResp_ReceiveStatus_name, int32(x))
}
func (GetPresentWeekCardEntryResp_ReceiveStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{4, 0}
}

type UpdatePresentWeekCardConfigRequest_UpdateField int32

const (
	UpdatePresentWeekCardConfigRequest_UPDATE_FIELD_UNSPECIFIED            UpdatePresentWeekCardConfigRequest_UpdateField = 0
	UpdatePresentWeekCardConfigRequest_UPDATE_FIELD_PRICE                  UpdatePresentWeekCardConfigRequest_UpdateField = 1
	UpdatePresentWeekCardConfigRequest_UPDATE_FIELD_ORIGIN_PRICE           UpdatePresentWeekCardConfigRequest_UpdateField = 2
	UpdatePresentWeekCardConfigRequest_UPDATE_FIELD_TIER_NAME              UpdatePresentWeekCardConfigRequest_UpdateField = 3
	UpdatePresentWeekCardConfigRequest_UPDATE_FIELD_MIN_LAST_MONTH_CONSUME UpdatePresentWeekCardConfigRequest_UpdateField = 4
	UpdatePresentWeekCardConfigRequest_UPDATE_FIELD_MIN_HISTORY_CONSUME    UpdatePresentWeekCardConfigRequest_UpdateField = 5
	UpdatePresentWeekCardConfigRequest_UPDARE_FIELD_SELLIN_POINT_TEXT      UpdatePresentWeekCardConfigRequest_UpdateField = 6
)

var UpdatePresentWeekCardConfigRequest_UpdateField_name = map[int32]string{
	0: "UPDATE_FIELD_UNSPECIFIED",
	1: "UPDATE_FIELD_PRICE",
	2: "UPDATE_FIELD_ORIGIN_PRICE",
	3: "UPDATE_FIELD_TIER_NAME",
	4: "UPDATE_FIELD_MIN_LAST_MONTH_CONSUME",
	5: "UPDATE_FIELD_MIN_HISTORY_CONSUME",
	6: "UPDARE_FIELD_SELLIN_POINT_TEXT",
}
var UpdatePresentWeekCardConfigRequest_UpdateField_value = map[string]int32{
	"UPDATE_FIELD_UNSPECIFIED":            0,
	"UPDATE_FIELD_PRICE":                  1,
	"UPDATE_FIELD_ORIGIN_PRICE":           2,
	"UPDATE_FIELD_TIER_NAME":              3,
	"UPDATE_FIELD_MIN_LAST_MONTH_CONSUME": 4,
	"UPDATE_FIELD_MIN_HISTORY_CONSUME":    5,
	"UPDARE_FIELD_SELLIN_POINT_TEXT":      6,
}

func (x UpdatePresentWeekCardConfigRequest_UpdateField) String() string {
	return proto.EnumName(UpdatePresentWeekCardConfigRequest_UpdateField_name, int32(x))
}
func (UpdatePresentWeekCardConfigRequest_UpdateField) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{21, 0}
}

type AwardItemInfo struct {
	AwardId              string   `protobuf:"bytes,1,opt,name=award_id,json=awardId,proto3" json:"award_id,omitempty"`
	Amount               uint32   `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
	AwardType            uint32   `protobuf:"varint,3,opt,name=award_type,json=awardType,proto3" json:"award_type,omitempty"`
	AwardValue           uint32   `protobuf:"varint,4,opt,name=award_value,json=awardValue,proto3" json:"award_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardItemInfo) Reset()         { *m = AwardItemInfo{} }
func (m *AwardItemInfo) String() string { return proto.CompactTextString(m) }
func (*AwardItemInfo) ProtoMessage()    {}
func (*AwardItemInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{0}
}
func (m *AwardItemInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardItemInfo.Unmarshal(m, b)
}
func (m *AwardItemInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardItemInfo.Marshal(b, m, deterministic)
}
func (dst *AwardItemInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardItemInfo.Merge(dst, src)
}
func (m *AwardItemInfo) XXX_Size() int {
	return xxx_messageInfo_AwardItemInfo.Size(m)
}
func (m *AwardItemInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardItemInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AwardItemInfo proto.InternalMessageInfo

func (m *AwardItemInfo) GetAwardId() string {
	if m != nil {
		return m.AwardId
	}
	return ""
}

func (m *AwardItemInfo) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *AwardItemInfo) GetAwardType() uint32 {
	if m != nil {
		return m.AwardType
	}
	return 0
}

func (m *AwardItemInfo) GetAwardValue() uint32 {
	if m != nil {
		return m.AwardValue
	}
	return 0
}

type DailyAwardInfo struct {
	Status               uint32           `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	AwardList            []*AwardItemInfo `protobuf:"bytes,2,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	AwardOrder           uint32           `protobuf:"varint,3,opt,name=award_order,json=awardOrder,proto3" json:"award_order,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *DailyAwardInfo) Reset()         { *m = DailyAwardInfo{} }
func (m *DailyAwardInfo) String() string { return proto.CompactTextString(m) }
func (*DailyAwardInfo) ProtoMessage()    {}
func (*DailyAwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{1}
}
func (m *DailyAwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DailyAwardInfo.Unmarshal(m, b)
}
func (m *DailyAwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DailyAwardInfo.Marshal(b, m, deterministic)
}
func (dst *DailyAwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DailyAwardInfo.Merge(dst, src)
}
func (m *DailyAwardInfo) XXX_Size() int {
	return xxx_messageInfo_DailyAwardInfo.Size(m)
}
func (m *DailyAwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DailyAwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DailyAwardInfo proto.InternalMessageInfo

func (m *DailyAwardInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *DailyAwardInfo) GetAwardList() []*AwardItemInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

func (m *DailyAwardInfo) GetAwardOrder() uint32 {
	if m != nil {
		return m.AwardOrder
	}
	return 0
}

// 周卡详情
type PresentWeekCardInfo struct {
	CardId               uint32            `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	TierName             string            `protobuf:"bytes,2,opt,name=tier_name,json=tierName,proto3" json:"tier_name,omitempty"`
	Price                uint32            `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`
	OriginPrice          uint32            `protobuf:"varint,4,opt,name=origin_price,json=originPrice,proto3" json:"origin_price,omitempty"`
	BuyStatus            uint32            `protobuf:"varint,5,opt,name=buy_status,json=buyStatus,proto3" json:"buy_status,omitempty"`
	AwardList            []*DailyAwardInfo `protobuf:"bytes,6,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	SellingPointText     string            `protobuf:"bytes,7,opt,name=selling_point_text,json=sellingPointText,proto3" json:"selling_point_text,omitempty"`
	ExpireTime           uint32            `protobuf:"varint,8,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	BuyTime              uint32            `protobuf:"varint,9,opt,name=buy_time,json=buyTime,proto3" json:"buy_time,omitempty"`
	CycleCnt             uint32            `protobuf:"varint,10,opt,name=cycle_cnt,json=cycleCnt,proto3" json:"cycle_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PresentWeekCardInfo) Reset()         { *m = PresentWeekCardInfo{} }
func (m *PresentWeekCardInfo) String() string { return proto.CompactTextString(m) }
func (*PresentWeekCardInfo) ProtoMessage()    {}
func (*PresentWeekCardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{2}
}
func (m *PresentWeekCardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentWeekCardInfo.Unmarshal(m, b)
}
func (m *PresentWeekCardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentWeekCardInfo.Marshal(b, m, deterministic)
}
func (dst *PresentWeekCardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentWeekCardInfo.Merge(dst, src)
}
func (m *PresentWeekCardInfo) XXX_Size() int {
	return xxx_messageInfo_PresentWeekCardInfo.Size(m)
}
func (m *PresentWeekCardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentWeekCardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentWeekCardInfo proto.InternalMessageInfo

func (m *PresentWeekCardInfo) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *PresentWeekCardInfo) GetTierName() string {
	if m != nil {
		return m.TierName
	}
	return ""
}

func (m *PresentWeekCardInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *PresentWeekCardInfo) GetOriginPrice() uint32 {
	if m != nil {
		return m.OriginPrice
	}
	return 0
}

func (m *PresentWeekCardInfo) GetBuyStatus() uint32 {
	if m != nil {
		return m.BuyStatus
	}
	return 0
}

func (m *PresentWeekCardInfo) GetAwardList() []*DailyAwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

func (m *PresentWeekCardInfo) GetSellingPointText() string {
	if m != nil {
		return m.SellingPointText
	}
	return ""
}

func (m *PresentWeekCardInfo) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *PresentWeekCardInfo) GetBuyTime() uint32 {
	if m != nil {
		return m.BuyTime
	}
	return 0
}

func (m *PresentWeekCardInfo) GetCycleCnt() uint32 {
	if m != nil {
		return m.CycleCnt
	}
	return 0
}

// 获取周卡入口信息
type GetPresentWeekCardEntryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentWeekCardEntryReq) Reset()         { *m = GetPresentWeekCardEntryReq{} }
func (m *GetPresentWeekCardEntryReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentWeekCardEntryReq) ProtoMessage()    {}
func (*GetPresentWeekCardEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{3}
}
func (m *GetPresentWeekCardEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentWeekCardEntryReq.Unmarshal(m, b)
}
func (m *GetPresentWeekCardEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentWeekCardEntryReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentWeekCardEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentWeekCardEntryReq.Merge(dst, src)
}
func (m *GetPresentWeekCardEntryReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentWeekCardEntryReq.Size(m)
}
func (m *GetPresentWeekCardEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentWeekCardEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentWeekCardEntryReq proto.InternalMessageInfo

func (m *GetPresentWeekCardEntryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPresentWeekCardEntryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetPresentWeekCardEntryResp struct {
	HaveAccess    bool   `protobuf:"varint,1,opt,name=have_access,json=haveAccess,proto3" json:"have_access,omitempty"`
	ReceiveStatus uint32 `protobuf:"varint,2,opt,name=receive_status,json=receiveStatus,proto3" json:"receive_status,omitempty"`
	// pgc 自动拉起半屏提醒配置
	NeedToAutoShow       bool     `protobuf:"varint,3,opt,name=need_to_auto_show,json=needToAutoShow,proto3" json:"need_to_auto_show,omitempty"`
	StaySeconds          uint32   `protobuf:"varint,4,opt,name=stay_seconds,json=staySeconds,proto3" json:"stay_seconds,omitempty"`
	EveryNDay            uint32   `protobuf:"varint,5,opt,name=every_n_day,json=everyNDay,proto3" json:"every_n_day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentWeekCardEntryResp) Reset()         { *m = GetPresentWeekCardEntryResp{} }
func (m *GetPresentWeekCardEntryResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentWeekCardEntryResp) ProtoMessage()    {}
func (*GetPresentWeekCardEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{4}
}
func (m *GetPresentWeekCardEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentWeekCardEntryResp.Unmarshal(m, b)
}
func (m *GetPresentWeekCardEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentWeekCardEntryResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentWeekCardEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentWeekCardEntryResp.Merge(dst, src)
}
func (m *GetPresentWeekCardEntryResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentWeekCardEntryResp.Size(m)
}
func (m *GetPresentWeekCardEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentWeekCardEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentWeekCardEntryResp proto.InternalMessageInfo

func (m *GetPresentWeekCardEntryResp) GetHaveAccess() bool {
	if m != nil {
		return m.HaveAccess
	}
	return false
}

func (m *GetPresentWeekCardEntryResp) GetReceiveStatus() uint32 {
	if m != nil {
		return m.ReceiveStatus
	}
	return 0
}

func (m *GetPresentWeekCardEntryResp) GetNeedToAutoShow() bool {
	if m != nil {
		return m.NeedToAutoShow
	}
	return false
}

func (m *GetPresentWeekCardEntryResp) GetStaySeconds() uint32 {
	if m != nil {
		return m.StaySeconds
	}
	return 0
}

func (m *GetPresentWeekCardEntryResp) GetEveryNDay() uint32 {
	if m != nil {
		return m.EveryNDay
	}
	return 0
}

// 获取周卡详情
type GetPresentWeekCardInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentWeekCardInfoReq) Reset()         { *m = GetPresentWeekCardInfoReq{} }
func (m *GetPresentWeekCardInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentWeekCardInfoReq) ProtoMessage()    {}
func (*GetPresentWeekCardInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{5}
}
func (m *GetPresentWeekCardInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentWeekCardInfoReq.Unmarshal(m, b)
}
func (m *GetPresentWeekCardInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentWeekCardInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentWeekCardInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentWeekCardInfoReq.Merge(dst, src)
}
func (m *GetPresentWeekCardInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentWeekCardInfoReq.Size(m)
}
func (m *GetPresentWeekCardInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentWeekCardInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentWeekCardInfoReq proto.InternalMessageInfo

func (m *GetPresentWeekCardInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPresentWeekCardInfoResp struct {
	CardList             []*PresentWeekCardInfo `protobuf:"bytes,1,rep,name=card_list,json=cardList,proto3" json:"card_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetPresentWeekCardInfoResp) Reset()         { *m = GetPresentWeekCardInfoResp{} }
func (m *GetPresentWeekCardInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentWeekCardInfoResp) ProtoMessage()    {}
func (*GetPresentWeekCardInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{6}
}
func (m *GetPresentWeekCardInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentWeekCardInfoResp.Unmarshal(m, b)
}
func (m *GetPresentWeekCardInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentWeekCardInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentWeekCardInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentWeekCardInfoResp.Merge(dst, src)
}
func (m *GetPresentWeekCardInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentWeekCardInfoResp.Size(m)
}
func (m *GetPresentWeekCardInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentWeekCardInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentWeekCardInfoResp proto.InternalMessageInfo

func (m *GetPresentWeekCardInfoResp) GetCardList() []*PresentWeekCardInfo {
	if m != nil {
		return m.CardList
	}
	return nil
}

// 购买周卡
type BuyPresentWeekCardReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CardId               uint32   `protobuf:"varint,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	OutsideTime          int64    `protobuf:"varint,3,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuyPresentWeekCardReq) Reset()         { *m = BuyPresentWeekCardReq{} }
func (m *BuyPresentWeekCardReq) String() string { return proto.CompactTextString(m) }
func (*BuyPresentWeekCardReq) ProtoMessage()    {}
func (*BuyPresentWeekCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{7}
}
func (m *BuyPresentWeekCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyPresentWeekCardReq.Unmarshal(m, b)
}
func (m *BuyPresentWeekCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyPresentWeekCardReq.Marshal(b, m, deterministic)
}
func (dst *BuyPresentWeekCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyPresentWeekCardReq.Merge(dst, src)
}
func (m *BuyPresentWeekCardReq) XXX_Size() int {
	return xxx_messageInfo_BuyPresentWeekCardReq.Size(m)
}
func (m *BuyPresentWeekCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyPresentWeekCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_BuyPresentWeekCardReq proto.InternalMessageInfo

func (m *BuyPresentWeekCardReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BuyPresentWeekCardReq) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *BuyPresentWeekCardReq) GetOutsideTime() int64 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

type BuyPresentWeekCardResp struct {
	Balance              uint32   `protobuf:"varint,1,opt,name=balance,proto3" json:"balance,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuyPresentWeekCardResp) Reset()         { *m = BuyPresentWeekCardResp{} }
func (m *BuyPresentWeekCardResp) String() string { return proto.CompactTextString(m) }
func (*BuyPresentWeekCardResp) ProtoMessage()    {}
func (*BuyPresentWeekCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{8}
}
func (m *BuyPresentWeekCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyPresentWeekCardResp.Unmarshal(m, b)
}
func (m *BuyPresentWeekCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyPresentWeekCardResp.Marshal(b, m, deterministic)
}
func (dst *BuyPresentWeekCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyPresentWeekCardResp.Merge(dst, src)
}
func (m *BuyPresentWeekCardResp) XXX_Size() int {
	return xxx_messageInfo_BuyPresentWeekCardResp.Size(m)
}
func (m *BuyPresentWeekCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyPresentWeekCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_BuyPresentWeekCardResp proto.InternalMessageInfo

func (m *BuyPresentWeekCardResp) GetBalance() uint32 {
	if m != nil {
		return m.Balance
	}
	return 0
}

// 领取周卡奖励
type ReceivePresentWeekCardRewardReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CardId               uint32   `protobuf:"varint,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	OutsideTime          int64    `protobuf:"varint,3,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
	RecvIdx              uint32   `protobuf:"varint,4,opt,name=recv_idx,json=recvIdx,proto3" json:"recv_idx,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReceivePresentWeekCardRewardReq) Reset()         { *m = ReceivePresentWeekCardRewardReq{} }
func (m *ReceivePresentWeekCardRewardReq) String() string { return proto.CompactTextString(m) }
func (*ReceivePresentWeekCardRewardReq) ProtoMessage()    {}
func (*ReceivePresentWeekCardRewardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{9}
}
func (m *ReceivePresentWeekCardRewardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceivePresentWeekCardRewardReq.Unmarshal(m, b)
}
func (m *ReceivePresentWeekCardRewardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceivePresentWeekCardRewardReq.Marshal(b, m, deterministic)
}
func (dst *ReceivePresentWeekCardRewardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceivePresentWeekCardRewardReq.Merge(dst, src)
}
func (m *ReceivePresentWeekCardRewardReq) XXX_Size() int {
	return xxx_messageInfo_ReceivePresentWeekCardRewardReq.Size(m)
}
func (m *ReceivePresentWeekCardRewardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceivePresentWeekCardRewardReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReceivePresentWeekCardRewardReq proto.InternalMessageInfo

func (m *ReceivePresentWeekCardRewardReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReceivePresentWeekCardRewardReq) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *ReceivePresentWeekCardRewardReq) GetOutsideTime() int64 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

func (m *ReceivePresentWeekCardRewardReq) GetRecvIdx() uint32 {
	if m != nil {
		return m.RecvIdx
	}
	return 0
}

type ReceivePresentWeekCardRewardResp struct {
	AwardList            []*DailyAwardInfo `protobuf:"bytes,1,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	WithExtraAward       bool              `protobuf:"varint,2,opt,name=with_extra_award,json=withExtraAward,proto3" json:"with_extra_award,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ReceivePresentWeekCardRewardResp) Reset()         { *m = ReceivePresentWeekCardRewardResp{} }
func (m *ReceivePresentWeekCardRewardResp) String() string { return proto.CompactTextString(m) }
func (*ReceivePresentWeekCardRewardResp) ProtoMessage()    {}
func (*ReceivePresentWeekCardRewardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{10}
}
func (m *ReceivePresentWeekCardRewardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceivePresentWeekCardRewardResp.Unmarshal(m, b)
}
func (m *ReceivePresentWeekCardRewardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceivePresentWeekCardRewardResp.Marshal(b, m, deterministic)
}
func (dst *ReceivePresentWeekCardRewardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceivePresentWeekCardRewardResp.Merge(dst, src)
}
func (m *ReceivePresentWeekCardRewardResp) XXX_Size() int {
	return xxx_messageInfo_ReceivePresentWeekCardRewardResp.Size(m)
}
func (m *ReceivePresentWeekCardRewardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceivePresentWeekCardRewardResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReceivePresentWeekCardRewardResp proto.InternalMessageInfo

func (m *ReceivePresentWeekCardRewardResp) GetAwardList() []*DailyAwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

func (m *ReceivePresentWeekCardRewardResp) GetWithExtraAward() bool {
	if m != nil {
		return m.WithExtraAward
	}
	return false
}

// 获取全量奖励信息
type GetAllAwardInfoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllAwardInfoReq) Reset()         { *m = GetAllAwardInfoReq{} }
func (m *GetAllAwardInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetAllAwardInfoReq) ProtoMessage()    {}
func (*GetAllAwardInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{11}
}
func (m *GetAllAwardInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllAwardInfoReq.Unmarshal(m, b)
}
func (m *GetAllAwardInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllAwardInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetAllAwardInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllAwardInfoReq.Merge(dst, src)
}
func (m *GetAllAwardInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetAllAwardInfoReq.Size(m)
}
func (m *GetAllAwardInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllAwardInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllAwardInfoReq proto.InternalMessageInfo

type GetAllAwardInfoResp struct {
	AwardList            []*AwardItemInfo `protobuf:"bytes,1,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetAllAwardInfoResp) Reset()         { *m = GetAllAwardInfoResp{} }
func (m *GetAllAwardInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetAllAwardInfoResp) ProtoMessage()    {}
func (*GetAllAwardInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{12}
}
func (m *GetAllAwardInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllAwardInfoResp.Unmarshal(m, b)
}
func (m *GetAllAwardInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllAwardInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetAllAwardInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllAwardInfoResp.Merge(dst, src)
}
func (m *GetAllAwardInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetAllAwardInfoResp.Size(m)
}
func (m *GetAllAwardInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllAwardInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllAwardInfoResp proto.InternalMessageInfo

func (m *GetAllAwardInfoResp) GetAwardList() []*AwardItemInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

type PresentWeekCardConf struct {
	CardId               uint32   `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	TierName             string   `protobuf:"bytes,2,opt,name=tier_name,json=tierName,proto3" json:"tier_name,omitempty"`
	Price                uint32   `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`
	OriginPrice          uint32   `protobuf:"varint,4,opt,name=origin_price,json=originPrice,proto3" json:"origin_price,omitempty"`
	SellingPointText     string   `protobuf:"bytes,5,opt,name=selling_point_text,json=sellingPointText,proto3" json:"selling_point_text,omitempty"`
	MinLastMonthConsume  int64    `protobuf:"varint,6,opt,name=min_last_month_consume,json=minLastMonthConsume,proto3" json:"min_last_month_consume,omitempty"`
	MinHistoryConsume    int64    `protobuf:"varint,7,opt,name=min_history_consume,json=minHistoryConsume,proto3" json:"min_history_consume,omitempty"`
	IsDel                bool     `protobuf:"varint,8,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentWeekCardConf) Reset()         { *m = PresentWeekCardConf{} }
func (m *PresentWeekCardConf) String() string { return proto.CompactTextString(m) }
func (*PresentWeekCardConf) ProtoMessage()    {}
func (*PresentWeekCardConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{13}
}
func (m *PresentWeekCardConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentWeekCardConf.Unmarshal(m, b)
}
func (m *PresentWeekCardConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentWeekCardConf.Marshal(b, m, deterministic)
}
func (dst *PresentWeekCardConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentWeekCardConf.Merge(dst, src)
}
func (m *PresentWeekCardConf) XXX_Size() int {
	return xxx_messageInfo_PresentWeekCardConf.Size(m)
}
func (m *PresentWeekCardConf) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentWeekCardConf.DiscardUnknown(m)
}

var xxx_messageInfo_PresentWeekCardConf proto.InternalMessageInfo

func (m *PresentWeekCardConf) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *PresentWeekCardConf) GetTierName() string {
	if m != nil {
		return m.TierName
	}
	return ""
}

func (m *PresentWeekCardConf) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *PresentWeekCardConf) GetOriginPrice() uint32 {
	if m != nil {
		return m.OriginPrice
	}
	return 0
}

func (m *PresentWeekCardConf) GetSellingPointText() string {
	if m != nil {
		return m.SellingPointText
	}
	return ""
}

func (m *PresentWeekCardConf) GetMinLastMonthConsume() int64 {
	if m != nil {
		return m.MinLastMonthConsume
	}
	return 0
}

func (m *PresentWeekCardConf) GetMinHistoryConsume() int64 {
	if m != nil {
		return m.MinHistoryConsume
	}
	return 0
}

func (m *PresentWeekCardConf) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

type PresentWeekCardAwardInfo struct {
	CardId               uint32            `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	AwardList            []*DailyAwardInfo `protobuf:"bytes,2,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	ExtraAward           *DailyAwardInfo   `protobuf:"bytes,3,opt,name=extra_award,json=extraAward,proto3" json:"extra_award,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PresentWeekCardAwardInfo) Reset()         { *m = PresentWeekCardAwardInfo{} }
func (m *PresentWeekCardAwardInfo) String() string { return proto.CompactTextString(m) }
func (*PresentWeekCardAwardInfo) ProtoMessage()    {}
func (*PresentWeekCardAwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{14}
}
func (m *PresentWeekCardAwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentWeekCardAwardInfo.Unmarshal(m, b)
}
func (m *PresentWeekCardAwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentWeekCardAwardInfo.Marshal(b, m, deterministic)
}
func (dst *PresentWeekCardAwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentWeekCardAwardInfo.Merge(dst, src)
}
func (m *PresentWeekCardAwardInfo) XXX_Size() int {
	return xxx_messageInfo_PresentWeekCardAwardInfo.Size(m)
}
func (m *PresentWeekCardAwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentWeekCardAwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentWeekCardAwardInfo proto.InternalMessageInfo

func (m *PresentWeekCardAwardInfo) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *PresentWeekCardAwardInfo) GetAwardList() []*DailyAwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

func (m *PresentWeekCardAwardInfo) GetExtraAward() *DailyAwardInfo {
	if m != nil {
		return m.ExtraAward
	}
	return nil
}

// 设置周卡信息
type SetPresentWeekCardInfoRequest struct {
	Conf                 *PresentWeekCardConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SetPresentWeekCardInfoRequest) Reset()         { *m = SetPresentWeekCardInfoRequest{} }
func (m *SetPresentWeekCardInfoRequest) String() string { return proto.CompactTextString(m) }
func (*SetPresentWeekCardInfoRequest) ProtoMessage()    {}
func (*SetPresentWeekCardInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{15}
}
func (m *SetPresentWeekCardInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPresentWeekCardInfoRequest.Unmarshal(m, b)
}
func (m *SetPresentWeekCardInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPresentWeekCardInfoRequest.Marshal(b, m, deterministic)
}
func (dst *SetPresentWeekCardInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPresentWeekCardInfoRequest.Merge(dst, src)
}
func (m *SetPresentWeekCardInfoRequest) XXX_Size() int {
	return xxx_messageInfo_SetPresentWeekCardInfoRequest.Size(m)
}
func (m *SetPresentWeekCardInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPresentWeekCardInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetPresentWeekCardInfoRequest proto.InternalMessageInfo

func (m *SetPresentWeekCardInfoRequest) GetConf() *PresentWeekCardConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type SetPresentWeekCardInfoResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPresentWeekCardInfoResponse) Reset()         { *m = SetPresentWeekCardInfoResponse{} }
func (m *SetPresentWeekCardInfoResponse) String() string { return proto.CompactTextString(m) }
func (*SetPresentWeekCardInfoResponse) ProtoMessage()    {}
func (*SetPresentWeekCardInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{16}
}
func (m *SetPresentWeekCardInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPresentWeekCardInfoResponse.Unmarshal(m, b)
}
func (m *SetPresentWeekCardInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPresentWeekCardInfoResponse.Marshal(b, m, deterministic)
}
func (dst *SetPresentWeekCardInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPresentWeekCardInfoResponse.Merge(dst, src)
}
func (m *SetPresentWeekCardInfoResponse) XXX_Size() int {
	return xxx_messageInfo_SetPresentWeekCardInfoResponse.Size(m)
}
func (m *SetPresentWeekCardInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPresentWeekCardInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetPresentWeekCardInfoResponse proto.InternalMessageInfo

// 获取所有生效中周卡配置信息
type GetPresentWeekCardConfigRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentWeekCardConfigRequest) Reset()         { *m = GetPresentWeekCardConfigRequest{} }
func (m *GetPresentWeekCardConfigRequest) String() string { return proto.CompactTextString(m) }
func (*GetPresentWeekCardConfigRequest) ProtoMessage()    {}
func (*GetPresentWeekCardConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{17}
}
func (m *GetPresentWeekCardConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentWeekCardConfigRequest.Unmarshal(m, b)
}
func (m *GetPresentWeekCardConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentWeekCardConfigRequest.Marshal(b, m, deterministic)
}
func (dst *GetPresentWeekCardConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentWeekCardConfigRequest.Merge(dst, src)
}
func (m *GetPresentWeekCardConfigRequest) XXX_Size() int {
	return xxx_messageInfo_GetPresentWeekCardConfigRequest.Size(m)
}
func (m *GetPresentWeekCardConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentWeekCardConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentWeekCardConfigRequest proto.InternalMessageInfo

type GetPresentWeekCardConfigResponse struct {
	CardList             []*PresentWeekCardConf `protobuf:"bytes,1,rep,name=card_list,json=cardList,proto3" json:"card_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetPresentWeekCardConfigResponse) Reset()         { *m = GetPresentWeekCardConfigResponse{} }
func (m *GetPresentWeekCardConfigResponse) String() string { return proto.CompactTextString(m) }
func (*GetPresentWeekCardConfigResponse) ProtoMessage()    {}
func (*GetPresentWeekCardConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{18}
}
func (m *GetPresentWeekCardConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentWeekCardConfigResponse.Unmarshal(m, b)
}
func (m *GetPresentWeekCardConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentWeekCardConfigResponse.Marshal(b, m, deterministic)
}
func (dst *GetPresentWeekCardConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentWeekCardConfigResponse.Merge(dst, src)
}
func (m *GetPresentWeekCardConfigResponse) XXX_Size() int {
	return xxx_messageInfo_GetPresentWeekCardConfigResponse.Size(m)
}
func (m *GetPresentWeekCardConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentWeekCardConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentWeekCardConfigResponse proto.InternalMessageInfo

func (m *GetPresentWeekCardConfigResponse) GetCardList() []*PresentWeekCardConf {
	if m != nil {
		return m.CardList
	}
	return nil
}

// 获取指定周卡的奖励信息
type GetPresentWeekCardAwardInfoRequest struct {
	CardId               uint32   `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentWeekCardAwardInfoRequest) Reset()         { *m = GetPresentWeekCardAwardInfoRequest{} }
func (m *GetPresentWeekCardAwardInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetPresentWeekCardAwardInfoRequest) ProtoMessage()    {}
func (*GetPresentWeekCardAwardInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{19}
}
func (m *GetPresentWeekCardAwardInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentWeekCardAwardInfoRequest.Unmarshal(m, b)
}
func (m *GetPresentWeekCardAwardInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentWeekCardAwardInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetPresentWeekCardAwardInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentWeekCardAwardInfoRequest.Merge(dst, src)
}
func (m *GetPresentWeekCardAwardInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetPresentWeekCardAwardInfoRequest.Size(m)
}
func (m *GetPresentWeekCardAwardInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentWeekCardAwardInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentWeekCardAwardInfoRequest proto.InternalMessageInfo

func (m *GetPresentWeekCardAwardInfoRequest) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

type GetPresentWeekCardAwardInfoResponse struct {
	AwardInfo            *PresentWeekCardAwardInfo `protobuf:"bytes,1,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetPresentWeekCardAwardInfoResponse) Reset()         { *m = GetPresentWeekCardAwardInfoResponse{} }
func (m *GetPresentWeekCardAwardInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetPresentWeekCardAwardInfoResponse) ProtoMessage()    {}
func (*GetPresentWeekCardAwardInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{20}
}
func (m *GetPresentWeekCardAwardInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentWeekCardAwardInfoResponse.Unmarshal(m, b)
}
func (m *GetPresentWeekCardAwardInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentWeekCardAwardInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetPresentWeekCardAwardInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentWeekCardAwardInfoResponse.Merge(dst, src)
}
func (m *GetPresentWeekCardAwardInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetPresentWeekCardAwardInfoResponse.Size(m)
}
func (m *GetPresentWeekCardAwardInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentWeekCardAwardInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentWeekCardAwardInfoResponse proto.InternalMessageInfo

func (m *GetPresentWeekCardAwardInfoResponse) GetAwardInfo() *PresentWeekCardAwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

// 更新周卡配置
type UpdatePresentWeekCardConfigRequest struct {
	CardId               uint32   `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	FieldType            uint32   `protobuf:"varint,2,opt,name=field_type,json=fieldType,proto3" json:"field_type,omitempty"`
	Value                uint32   `protobuf:"varint,3,opt,name=value,proto3" json:"value,omitempty"`
	StrValue             string   `protobuf:"bytes,4,opt,name=str_value,json=strValue,proto3" json:"str_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePresentWeekCardConfigRequest) Reset()         { *m = UpdatePresentWeekCardConfigRequest{} }
func (m *UpdatePresentWeekCardConfigRequest) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentWeekCardConfigRequest) ProtoMessage()    {}
func (*UpdatePresentWeekCardConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{21}
}
func (m *UpdatePresentWeekCardConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentWeekCardConfigRequest.Unmarshal(m, b)
}
func (m *UpdatePresentWeekCardConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentWeekCardConfigRequest.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentWeekCardConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentWeekCardConfigRequest.Merge(dst, src)
}
func (m *UpdatePresentWeekCardConfigRequest) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentWeekCardConfigRequest.Size(m)
}
func (m *UpdatePresentWeekCardConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentWeekCardConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentWeekCardConfigRequest proto.InternalMessageInfo

func (m *UpdatePresentWeekCardConfigRequest) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *UpdatePresentWeekCardConfigRequest) GetFieldType() uint32 {
	if m != nil {
		return m.FieldType
	}
	return 0
}

func (m *UpdatePresentWeekCardConfigRequest) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *UpdatePresentWeekCardConfigRequest) GetStrValue() string {
	if m != nil {
		return m.StrValue
	}
	return ""
}

type UpdatePresentWeekCardConfigResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePresentWeekCardConfigResponse) Reset()         { *m = UpdatePresentWeekCardConfigResponse{} }
func (m *UpdatePresentWeekCardConfigResponse) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentWeekCardConfigResponse) ProtoMessage()    {}
func (*UpdatePresentWeekCardConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{22}
}
func (m *UpdatePresentWeekCardConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentWeekCardConfigResponse.Unmarshal(m, b)
}
func (m *UpdatePresentWeekCardConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentWeekCardConfigResponse.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentWeekCardConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentWeekCardConfigResponse.Merge(dst, src)
}
func (m *UpdatePresentWeekCardConfigResponse) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentWeekCardConfigResponse.Size(m)
}
func (m *UpdatePresentWeekCardConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentWeekCardConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentWeekCardConfigResponse proto.InternalMessageInfo

// 更新周卡奖励信息
type UpdatePresentWeekCardAwardInfoRequest struct {
	CardId               uint32                    `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	AwardInfo            *PresentWeekCardAwardInfo `protobuf:"bytes,2,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *UpdatePresentWeekCardAwardInfoRequest) Reset()         { *m = UpdatePresentWeekCardAwardInfoRequest{} }
func (m *UpdatePresentWeekCardAwardInfoRequest) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentWeekCardAwardInfoRequest) ProtoMessage()    {}
func (*UpdatePresentWeekCardAwardInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{23}
}
func (m *UpdatePresentWeekCardAwardInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentWeekCardAwardInfoRequest.Unmarshal(m, b)
}
func (m *UpdatePresentWeekCardAwardInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentWeekCardAwardInfoRequest.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentWeekCardAwardInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentWeekCardAwardInfoRequest.Merge(dst, src)
}
func (m *UpdatePresentWeekCardAwardInfoRequest) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentWeekCardAwardInfoRequest.Size(m)
}
func (m *UpdatePresentWeekCardAwardInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentWeekCardAwardInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentWeekCardAwardInfoRequest proto.InternalMessageInfo

func (m *UpdatePresentWeekCardAwardInfoRequest) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *UpdatePresentWeekCardAwardInfoRequest) GetAwardInfo() *PresentWeekCardAwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

type UpdatePresentWeekCardAwardInfoResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePresentWeekCardAwardInfoResponse) Reset() {
	*m = UpdatePresentWeekCardAwardInfoResponse{}
}
func (m *UpdatePresentWeekCardAwardInfoResponse) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentWeekCardAwardInfoResponse) ProtoMessage()    {}
func (*UpdatePresentWeekCardAwardInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{24}
}
func (m *UpdatePresentWeekCardAwardInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentWeekCardAwardInfoResponse.Unmarshal(m, b)
}
func (m *UpdatePresentWeekCardAwardInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentWeekCardAwardInfoResponse.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentWeekCardAwardInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentWeekCardAwardInfoResponse.Merge(dst, src)
}
func (m *UpdatePresentWeekCardAwardInfoResponse) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentWeekCardAwardInfoResponse.Size(m)
}
func (m *UpdatePresentWeekCardAwardInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentWeekCardAwardInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentWeekCardAwardInfoResponse proto.InternalMessageInfo

// 删除周卡配置
type DeletePresentWeekCardConfigRequest struct {
	CardId               uint32   `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePresentWeekCardConfigRequest) Reset()         { *m = DeletePresentWeekCardConfigRequest{} }
func (m *DeletePresentWeekCardConfigRequest) String() string { return proto.CompactTextString(m) }
func (*DeletePresentWeekCardConfigRequest) ProtoMessage()    {}
func (*DeletePresentWeekCardConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{25}
}
func (m *DeletePresentWeekCardConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeletePresentWeekCardConfigRequest.Unmarshal(m, b)
}
func (m *DeletePresentWeekCardConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeletePresentWeekCardConfigRequest.Marshal(b, m, deterministic)
}
func (dst *DeletePresentWeekCardConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePresentWeekCardConfigRequest.Merge(dst, src)
}
func (m *DeletePresentWeekCardConfigRequest) XXX_Size() int {
	return xxx_messageInfo_DeletePresentWeekCardConfigRequest.Size(m)
}
func (m *DeletePresentWeekCardConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePresentWeekCardConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePresentWeekCardConfigRequest proto.InternalMessageInfo

func (m *DeletePresentWeekCardConfigRequest) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

type DeletePresentWeekCardConfigResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePresentWeekCardConfigResponse) Reset()         { *m = DeletePresentWeekCardConfigResponse{} }
func (m *DeletePresentWeekCardConfigResponse) String() string { return proto.CompactTextString(m) }
func (*DeletePresentWeekCardConfigResponse) ProtoMessage()    {}
func (*DeletePresentWeekCardConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{26}
}
func (m *DeletePresentWeekCardConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeletePresentWeekCardConfigResponse.Unmarshal(m, b)
}
func (m *DeletePresentWeekCardConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeletePresentWeekCardConfigResponse.Marshal(b, m, deterministic)
}
func (dst *DeletePresentWeekCardConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePresentWeekCardConfigResponse.Merge(dst, src)
}
func (m *DeletePresentWeekCardConfigResponse) XXX_Size() int {
	return xxx_messageInfo_DeletePresentWeekCardConfigResponse.Size(m)
}
func (m *DeletePresentWeekCardConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePresentWeekCardConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePresentWeekCardConfigResponse proto.InternalMessageInfo

// 获取本地周卡信息
type GetWeekCardInfoLocalRequest struct {
	CardId               uint32   `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeekCardInfoLocalRequest) Reset()         { *m = GetWeekCardInfoLocalRequest{} }
func (m *GetWeekCardInfoLocalRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeekCardInfoLocalRequest) ProtoMessage()    {}
func (*GetWeekCardInfoLocalRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{27}
}
func (m *GetWeekCardInfoLocalRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeekCardInfoLocalRequest.Unmarshal(m, b)
}
func (m *GetWeekCardInfoLocalRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeekCardInfoLocalRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeekCardInfoLocalRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeekCardInfoLocalRequest.Merge(dst, src)
}
func (m *GetWeekCardInfoLocalRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeekCardInfoLocalRequest.Size(m)
}
func (m *GetWeekCardInfoLocalRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeekCardInfoLocalRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeekCardInfoLocalRequest proto.InternalMessageInfo

func (m *GetWeekCardInfoLocalRequest) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

type GetWeekCardInfoLocalResponse struct {
	Conf                 *PresentWeekCardConf      `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	AwardInfo            *PresentWeekCardAwardInfo `protobuf:"bytes,2,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetWeekCardInfoLocalResponse) Reset()         { *m = GetWeekCardInfoLocalResponse{} }
func (m *GetWeekCardInfoLocalResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeekCardInfoLocalResponse) ProtoMessage()    {}
func (*GetWeekCardInfoLocalResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{28}
}
func (m *GetWeekCardInfoLocalResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeekCardInfoLocalResponse.Unmarshal(m, b)
}
func (m *GetWeekCardInfoLocalResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeekCardInfoLocalResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeekCardInfoLocalResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeekCardInfoLocalResponse.Merge(dst, src)
}
func (m *GetWeekCardInfoLocalResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeekCardInfoLocalResponse.Size(m)
}
func (m *GetWeekCardInfoLocalResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeekCardInfoLocalResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeekCardInfoLocalResponse proto.InternalMessageInfo

func (m *GetWeekCardInfoLocalResponse) GetConf() *PresentWeekCardConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

func (m *GetWeekCardInfoLocalResponse) GetAwardInfo() *PresentWeekCardAwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

type ManualOnlineEventRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	EventType            uint32   `protobuf:"varint,2,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManualOnlineEventRequest) Reset()         { *m = ManualOnlineEventRequest{} }
func (m *ManualOnlineEventRequest) String() string { return proto.CompactTextString(m) }
func (*ManualOnlineEventRequest) ProtoMessage()    {}
func (*ManualOnlineEventRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{29}
}
func (m *ManualOnlineEventRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualOnlineEventRequest.Unmarshal(m, b)
}
func (m *ManualOnlineEventRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualOnlineEventRequest.Marshal(b, m, deterministic)
}
func (dst *ManualOnlineEventRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualOnlineEventRequest.Merge(dst, src)
}
func (m *ManualOnlineEventRequest) XXX_Size() int {
	return xxx_messageInfo_ManualOnlineEventRequest.Size(m)
}
func (m *ManualOnlineEventRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualOnlineEventRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ManualOnlineEventRequest proto.InternalMessageInfo

func (m *ManualOnlineEventRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ManualOnlineEventRequest) GetEventType() uint32 {
	if m != nil {
		return m.EventType
	}
	return 0
}

type ManualOnlineEventResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManualOnlineEventResponse) Reset()         { *m = ManualOnlineEventResponse{} }
func (m *ManualOnlineEventResponse) String() string { return proto.CompactTextString(m) }
func (*ManualOnlineEventResponse) ProtoMessage()    {}
func (*ManualOnlineEventResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_week_card_7d29b0795bb0f9a6, []int{30}
}
func (m *ManualOnlineEventResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualOnlineEventResponse.Unmarshal(m, b)
}
func (m *ManualOnlineEventResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualOnlineEventResponse.Marshal(b, m, deterministic)
}
func (dst *ManualOnlineEventResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualOnlineEventResponse.Merge(dst, src)
}
func (m *ManualOnlineEventResponse) XXX_Size() int {
	return xxx_messageInfo_ManualOnlineEventResponse.Size(m)
}
func (m *ManualOnlineEventResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualOnlineEventResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ManualOnlineEventResponse proto.InternalMessageInfo

func init() {
	proto.RegisterType((*AwardItemInfo)(nil), "present_week_card.AwardItemInfo")
	proto.RegisterType((*DailyAwardInfo)(nil), "present_week_card.DailyAwardInfo")
	proto.RegisterType((*PresentWeekCardInfo)(nil), "present_week_card.PresentWeekCardInfo")
	proto.RegisterType((*GetPresentWeekCardEntryReq)(nil), "present_week_card.GetPresentWeekCardEntryReq")
	proto.RegisterType((*GetPresentWeekCardEntryResp)(nil), "present_week_card.GetPresentWeekCardEntryResp")
	proto.RegisterType((*GetPresentWeekCardInfoReq)(nil), "present_week_card.GetPresentWeekCardInfoReq")
	proto.RegisterType((*GetPresentWeekCardInfoResp)(nil), "present_week_card.GetPresentWeekCardInfoResp")
	proto.RegisterType((*BuyPresentWeekCardReq)(nil), "present_week_card.BuyPresentWeekCardReq")
	proto.RegisterType((*BuyPresentWeekCardResp)(nil), "present_week_card.BuyPresentWeekCardResp")
	proto.RegisterType((*ReceivePresentWeekCardRewardReq)(nil), "present_week_card.ReceivePresentWeekCardRewardReq")
	proto.RegisterType((*ReceivePresentWeekCardRewardResp)(nil), "present_week_card.ReceivePresentWeekCardRewardResp")
	proto.RegisterType((*GetAllAwardInfoReq)(nil), "present_week_card.GetAllAwardInfoReq")
	proto.RegisterType((*GetAllAwardInfoResp)(nil), "present_week_card.GetAllAwardInfoResp")
	proto.RegisterType((*PresentWeekCardConf)(nil), "present_week_card.PresentWeekCardConf")
	proto.RegisterType((*PresentWeekCardAwardInfo)(nil), "present_week_card.PresentWeekCardAwardInfo")
	proto.RegisterType((*SetPresentWeekCardInfoRequest)(nil), "present_week_card.SetPresentWeekCardInfoRequest")
	proto.RegisterType((*SetPresentWeekCardInfoResponse)(nil), "present_week_card.SetPresentWeekCardInfoResponse")
	proto.RegisterType((*GetPresentWeekCardConfigRequest)(nil), "present_week_card.GetPresentWeekCardConfigRequest")
	proto.RegisterType((*GetPresentWeekCardConfigResponse)(nil), "present_week_card.GetPresentWeekCardConfigResponse")
	proto.RegisterType((*GetPresentWeekCardAwardInfoRequest)(nil), "present_week_card.GetPresentWeekCardAwardInfoRequest")
	proto.RegisterType((*GetPresentWeekCardAwardInfoResponse)(nil), "present_week_card.GetPresentWeekCardAwardInfoResponse")
	proto.RegisterType((*UpdatePresentWeekCardConfigRequest)(nil), "present_week_card.UpdatePresentWeekCardConfigRequest")
	proto.RegisterType((*UpdatePresentWeekCardConfigResponse)(nil), "present_week_card.UpdatePresentWeekCardConfigResponse")
	proto.RegisterType((*UpdatePresentWeekCardAwardInfoRequest)(nil), "present_week_card.UpdatePresentWeekCardAwardInfoRequest")
	proto.RegisterType((*UpdatePresentWeekCardAwardInfoResponse)(nil), "present_week_card.UpdatePresentWeekCardAwardInfoResponse")
	proto.RegisterType((*DeletePresentWeekCardConfigRequest)(nil), "present_week_card.DeletePresentWeekCardConfigRequest")
	proto.RegisterType((*DeletePresentWeekCardConfigResponse)(nil), "present_week_card.DeletePresentWeekCardConfigResponse")
	proto.RegisterType((*GetWeekCardInfoLocalRequest)(nil), "present_week_card.GetWeekCardInfoLocalRequest")
	proto.RegisterType((*GetWeekCardInfoLocalResponse)(nil), "present_week_card.GetWeekCardInfoLocalResponse")
	proto.RegisterType((*ManualOnlineEventRequest)(nil), "present_week_card.ManualOnlineEventRequest")
	proto.RegisterType((*ManualOnlineEventResponse)(nil), "present_week_card.ManualOnlineEventResponse")
	proto.RegisterEnum("present_week_card.AwardStatusType", AwardStatusType_name, AwardStatusType_value)
	proto.RegisterEnum("present_week_card.AwardItemType", AwardItemType_name, AwardItemType_value)
	proto.RegisterEnum("present_week_card.PresentWeekCardInfo_BuyStatus", PresentWeekCardInfo_BuyStatus_name, PresentWeekCardInfo_BuyStatus_value)
	proto.RegisterEnum("present_week_card.GetPresentWeekCardEntryResp_ReceiveStatus", GetPresentWeekCardEntryResp_ReceiveStatus_name, GetPresentWeekCardEntryResp_ReceiveStatus_value)
	proto.RegisterEnum("present_week_card.UpdatePresentWeekCardConfigRequest_UpdateField", UpdatePresentWeekCardConfigRequest_UpdateField_name, UpdatePresentWeekCardConfigRequest_UpdateField_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PresentWeekCardClient is the client API for PresentWeekCard service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PresentWeekCardClient interface {
	// 获取周卡详情
	GetPresentWeekCardInfo(ctx context.Context, in *GetPresentWeekCardInfoReq, opts ...grpc.CallOption) (*GetPresentWeekCardInfoResp, error)
	// 获取周卡入口信息
	GetPresentWeekCardAccess(ctx context.Context, in *GetPresentWeekCardEntryReq, opts ...grpc.CallOption) (*GetPresentWeekCardEntryResp, error)
	// 购买周卡
	BuyPresentWeekCard(ctx context.Context, in *BuyPresentWeekCardReq, opts ...grpc.CallOption) (*BuyPresentWeekCardResp, error)
	// 领取周卡奖励
	ReceivePresentWeekCardReward(ctx context.Context, in *ReceivePresentWeekCardRewardReq, opts ...grpc.CallOption) (*ReceivePresentWeekCardRewardResp, error)
	// 获取全量礼物奖励信息
	GetAllAwardInfo(ctx context.Context, in *GetAllAwardInfoReq, opts ...grpc.CallOption) (*GetAllAwardInfoResp, error)
	// 手动上下线
	ManualOnlineEvent(ctx context.Context, in *ManualOnlineEventRequest, opts ...grpc.CallOption) (*ManualOnlineEventResponse, error)
	// ======================== 后台接口 ============================
	// 获取周卡配置
	GetPresentWeekCardConfig(ctx context.Context, in *GetPresentWeekCardConfigRequest, opts ...grpc.CallOption) (*GetPresentWeekCardConfigResponse, error)
	// 设置周卡配置
	SetPresentWeekCardConfig(ctx context.Context, in *SetPresentWeekCardInfoRequest, opts ...grpc.CallOption) (*SetPresentWeekCardInfoResponse, error)
	// 更新周卡配置
	UpdatePresentWeekCardConfig(ctx context.Context, in *UpdatePresentWeekCardConfigRequest, opts ...grpc.CallOption) (*UpdatePresentWeekCardConfigResponse, error)
	// 删除周卡配置
	DeletePresentWeekCardConfig(ctx context.Context, in *DeletePresentWeekCardConfigRequest, opts ...grpc.CallOption) (*DeletePresentWeekCardConfigResponse, error)
	// 更新周卡奖励信息
	UpdatePresentWeekCardAwardInfo(ctx context.Context, in *UpdatePresentWeekCardAwardInfoRequest, opts ...grpc.CallOption) (*UpdatePresentWeekCardAwardInfoResponse, error)
	// 获取指定周卡奖励信息
	GetPresentWeekCardAwardInfo(ctx context.Context, in *GetPresentWeekCardAwardInfoRequest, opts ...grpc.CallOption) (*GetPresentWeekCardAwardInfoResponse, error)
	// 获取本地周卡信息
	GetWeekCardInfoLocal(ctx context.Context, in *GetWeekCardInfoLocalRequest, opts ...grpc.CallOption) (*GetWeekCardInfoLocalResponse, error)
	// ======================== 对账接口 ===============================
	// 发放包裹数据对账
	GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// T豆消费数据对账
	GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
}

type presentWeekCardClient struct {
	cc *grpc.ClientConn
}

func NewPresentWeekCardClient(cc *grpc.ClientConn) PresentWeekCardClient {
	return &presentWeekCardClient{cc}
}

func (c *presentWeekCardClient) GetPresentWeekCardInfo(ctx context.Context, in *GetPresentWeekCardInfoReq, opts ...grpc.CallOption) (*GetPresentWeekCardInfoResp, error) {
	out := new(GetPresentWeekCardInfoResp)
	err := c.cc.Invoke(ctx, "/present_week_card.PresentWeekCard/GetPresentWeekCardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWeekCardClient) GetPresentWeekCardAccess(ctx context.Context, in *GetPresentWeekCardEntryReq, opts ...grpc.CallOption) (*GetPresentWeekCardEntryResp, error) {
	out := new(GetPresentWeekCardEntryResp)
	err := c.cc.Invoke(ctx, "/present_week_card.PresentWeekCard/GetPresentWeekCardAccess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWeekCardClient) BuyPresentWeekCard(ctx context.Context, in *BuyPresentWeekCardReq, opts ...grpc.CallOption) (*BuyPresentWeekCardResp, error) {
	out := new(BuyPresentWeekCardResp)
	err := c.cc.Invoke(ctx, "/present_week_card.PresentWeekCard/BuyPresentWeekCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWeekCardClient) ReceivePresentWeekCardReward(ctx context.Context, in *ReceivePresentWeekCardRewardReq, opts ...grpc.CallOption) (*ReceivePresentWeekCardRewardResp, error) {
	out := new(ReceivePresentWeekCardRewardResp)
	err := c.cc.Invoke(ctx, "/present_week_card.PresentWeekCard/ReceivePresentWeekCardReward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWeekCardClient) GetAllAwardInfo(ctx context.Context, in *GetAllAwardInfoReq, opts ...grpc.CallOption) (*GetAllAwardInfoResp, error) {
	out := new(GetAllAwardInfoResp)
	err := c.cc.Invoke(ctx, "/present_week_card.PresentWeekCard/GetAllAwardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWeekCardClient) ManualOnlineEvent(ctx context.Context, in *ManualOnlineEventRequest, opts ...grpc.CallOption) (*ManualOnlineEventResponse, error) {
	out := new(ManualOnlineEventResponse)
	err := c.cc.Invoke(ctx, "/present_week_card.PresentWeekCard/ManualOnlineEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWeekCardClient) GetPresentWeekCardConfig(ctx context.Context, in *GetPresentWeekCardConfigRequest, opts ...grpc.CallOption) (*GetPresentWeekCardConfigResponse, error) {
	out := new(GetPresentWeekCardConfigResponse)
	err := c.cc.Invoke(ctx, "/present_week_card.PresentWeekCard/GetPresentWeekCardConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWeekCardClient) SetPresentWeekCardConfig(ctx context.Context, in *SetPresentWeekCardInfoRequest, opts ...grpc.CallOption) (*SetPresentWeekCardInfoResponse, error) {
	out := new(SetPresentWeekCardInfoResponse)
	err := c.cc.Invoke(ctx, "/present_week_card.PresentWeekCard/SetPresentWeekCardConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWeekCardClient) UpdatePresentWeekCardConfig(ctx context.Context, in *UpdatePresentWeekCardConfigRequest, opts ...grpc.CallOption) (*UpdatePresentWeekCardConfigResponse, error) {
	out := new(UpdatePresentWeekCardConfigResponse)
	err := c.cc.Invoke(ctx, "/present_week_card.PresentWeekCard/UpdatePresentWeekCardConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWeekCardClient) DeletePresentWeekCardConfig(ctx context.Context, in *DeletePresentWeekCardConfigRequest, opts ...grpc.CallOption) (*DeletePresentWeekCardConfigResponse, error) {
	out := new(DeletePresentWeekCardConfigResponse)
	err := c.cc.Invoke(ctx, "/present_week_card.PresentWeekCard/DeletePresentWeekCardConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWeekCardClient) UpdatePresentWeekCardAwardInfo(ctx context.Context, in *UpdatePresentWeekCardAwardInfoRequest, opts ...grpc.CallOption) (*UpdatePresentWeekCardAwardInfoResponse, error) {
	out := new(UpdatePresentWeekCardAwardInfoResponse)
	err := c.cc.Invoke(ctx, "/present_week_card.PresentWeekCard/UpdatePresentWeekCardAwardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWeekCardClient) GetPresentWeekCardAwardInfo(ctx context.Context, in *GetPresentWeekCardAwardInfoRequest, opts ...grpc.CallOption) (*GetPresentWeekCardAwardInfoResponse, error) {
	out := new(GetPresentWeekCardAwardInfoResponse)
	err := c.cc.Invoke(ctx, "/present_week_card.PresentWeekCard/GetPresentWeekCardAwardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWeekCardClient) GetWeekCardInfoLocal(ctx context.Context, in *GetWeekCardInfoLocalRequest, opts ...grpc.CallOption) (*GetWeekCardInfoLocalResponse, error) {
	out := new(GetWeekCardInfoLocalResponse)
	err := c.cc.Invoke(ctx, "/present_week_card.PresentWeekCard/GetWeekCardInfoLocal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWeekCardClient) GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/present_week_card.PresentWeekCard/GetAwardTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWeekCardClient) GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/present_week_card.PresentWeekCard/GetAwardOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWeekCardClient) GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/present_week_card.PresentWeekCard/GetConsumeTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWeekCardClient) GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/present_week_card.PresentWeekCard/GetConsumeOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PresentWeekCardServer is the server API for PresentWeekCard service.
type PresentWeekCardServer interface {
	// 获取周卡详情
	GetPresentWeekCardInfo(context.Context, *GetPresentWeekCardInfoReq) (*GetPresentWeekCardInfoResp, error)
	// 获取周卡入口信息
	GetPresentWeekCardAccess(context.Context, *GetPresentWeekCardEntryReq) (*GetPresentWeekCardEntryResp, error)
	// 购买周卡
	BuyPresentWeekCard(context.Context, *BuyPresentWeekCardReq) (*BuyPresentWeekCardResp, error)
	// 领取周卡奖励
	ReceivePresentWeekCardReward(context.Context, *ReceivePresentWeekCardRewardReq) (*ReceivePresentWeekCardRewardResp, error)
	// 获取全量礼物奖励信息
	GetAllAwardInfo(context.Context, *GetAllAwardInfoReq) (*GetAllAwardInfoResp, error)
	// 手动上下线
	ManualOnlineEvent(context.Context, *ManualOnlineEventRequest) (*ManualOnlineEventResponse, error)
	// ======================== 后台接口 ============================
	// 获取周卡配置
	GetPresentWeekCardConfig(context.Context, *GetPresentWeekCardConfigRequest) (*GetPresentWeekCardConfigResponse, error)
	// 设置周卡配置
	SetPresentWeekCardConfig(context.Context, *SetPresentWeekCardInfoRequest) (*SetPresentWeekCardInfoResponse, error)
	// 更新周卡配置
	UpdatePresentWeekCardConfig(context.Context, *UpdatePresentWeekCardConfigRequest) (*UpdatePresentWeekCardConfigResponse, error)
	// 删除周卡配置
	DeletePresentWeekCardConfig(context.Context, *DeletePresentWeekCardConfigRequest) (*DeletePresentWeekCardConfigResponse, error)
	// 更新周卡奖励信息
	UpdatePresentWeekCardAwardInfo(context.Context, *UpdatePresentWeekCardAwardInfoRequest) (*UpdatePresentWeekCardAwardInfoResponse, error)
	// 获取指定周卡奖励信息
	GetPresentWeekCardAwardInfo(context.Context, *GetPresentWeekCardAwardInfoRequest) (*GetPresentWeekCardAwardInfoResponse, error)
	// 获取本地周卡信息
	GetWeekCardInfoLocal(context.Context, *GetWeekCardInfoLocalRequest) (*GetWeekCardInfoLocalResponse, error)
	// ======================== 对账接口 ===============================
	// 发放包裹数据对账
	GetAwardTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// T豆消费数据对账
	GetConsumeTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetConsumeOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
}

func RegisterPresentWeekCardServer(s *grpc.Server, srv PresentWeekCardServer) {
	s.RegisterService(&_PresentWeekCard_serviceDesc, srv)
}

func _PresentWeekCard_GetPresentWeekCardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentWeekCardInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWeekCardServer).GetPresentWeekCardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_week_card.PresentWeekCard/GetPresentWeekCardInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWeekCardServer).GetPresentWeekCardInfo(ctx, req.(*GetPresentWeekCardInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWeekCard_GetPresentWeekCardAccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentWeekCardEntryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWeekCardServer).GetPresentWeekCardAccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_week_card.PresentWeekCard/GetPresentWeekCardAccess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWeekCardServer).GetPresentWeekCardAccess(ctx, req.(*GetPresentWeekCardEntryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWeekCard_BuyPresentWeekCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuyPresentWeekCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWeekCardServer).BuyPresentWeekCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_week_card.PresentWeekCard/BuyPresentWeekCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWeekCardServer).BuyPresentWeekCard(ctx, req.(*BuyPresentWeekCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWeekCard_ReceivePresentWeekCardReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceivePresentWeekCardRewardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWeekCardServer).ReceivePresentWeekCardReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_week_card.PresentWeekCard/ReceivePresentWeekCardReward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWeekCardServer).ReceivePresentWeekCardReward(ctx, req.(*ReceivePresentWeekCardRewardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWeekCard_GetAllAwardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllAwardInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWeekCardServer).GetAllAwardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_week_card.PresentWeekCard/GetAllAwardInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWeekCardServer).GetAllAwardInfo(ctx, req.(*GetAllAwardInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWeekCard_ManualOnlineEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ManualOnlineEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWeekCardServer).ManualOnlineEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_week_card.PresentWeekCard/ManualOnlineEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWeekCardServer).ManualOnlineEvent(ctx, req.(*ManualOnlineEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWeekCard_GetPresentWeekCardConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentWeekCardConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWeekCardServer).GetPresentWeekCardConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_week_card.PresentWeekCard/GetPresentWeekCardConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWeekCardServer).GetPresentWeekCardConfig(ctx, req.(*GetPresentWeekCardConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWeekCard_SetPresentWeekCardConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPresentWeekCardInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWeekCardServer).SetPresentWeekCardConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_week_card.PresentWeekCard/SetPresentWeekCardConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWeekCardServer).SetPresentWeekCardConfig(ctx, req.(*SetPresentWeekCardInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWeekCard_UpdatePresentWeekCardConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePresentWeekCardConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWeekCardServer).UpdatePresentWeekCardConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_week_card.PresentWeekCard/UpdatePresentWeekCardConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWeekCardServer).UpdatePresentWeekCardConfig(ctx, req.(*UpdatePresentWeekCardConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWeekCard_DeletePresentWeekCardConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePresentWeekCardConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWeekCardServer).DeletePresentWeekCardConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_week_card.PresentWeekCard/DeletePresentWeekCardConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWeekCardServer).DeletePresentWeekCardConfig(ctx, req.(*DeletePresentWeekCardConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWeekCard_UpdatePresentWeekCardAwardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePresentWeekCardAwardInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWeekCardServer).UpdatePresentWeekCardAwardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_week_card.PresentWeekCard/UpdatePresentWeekCardAwardInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWeekCardServer).UpdatePresentWeekCardAwardInfo(ctx, req.(*UpdatePresentWeekCardAwardInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWeekCard_GetPresentWeekCardAwardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentWeekCardAwardInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWeekCardServer).GetPresentWeekCardAwardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_week_card.PresentWeekCard/GetPresentWeekCardAwardInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWeekCardServer).GetPresentWeekCardAwardInfo(ctx, req.(*GetPresentWeekCardAwardInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWeekCard_GetWeekCardInfoLocal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWeekCardInfoLocalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWeekCardServer).GetWeekCardInfoLocal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_week_card.PresentWeekCard/GetWeekCardInfoLocal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWeekCardServer).GetWeekCardInfoLocal(ctx, req.(*GetWeekCardInfoLocalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWeekCard_GetAwardTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWeekCardServer).GetAwardTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_week_card.PresentWeekCard/GetAwardTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWeekCardServer).GetAwardTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWeekCard_GetAwardOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWeekCardServer).GetAwardOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_week_card.PresentWeekCard/GetAwardOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWeekCardServer).GetAwardOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWeekCard_GetConsumeTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWeekCardServer).GetConsumeTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_week_card.PresentWeekCard/GetConsumeTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWeekCardServer).GetConsumeTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWeekCard_GetConsumeOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWeekCardServer).GetConsumeOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_week_card.PresentWeekCard/GetConsumeOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWeekCardServer).GetConsumeOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PresentWeekCard_serviceDesc = grpc.ServiceDesc{
	ServiceName: "present_week_card.PresentWeekCard",
	HandlerType: (*PresentWeekCardServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPresentWeekCardInfo",
			Handler:    _PresentWeekCard_GetPresentWeekCardInfo_Handler,
		},
		{
			MethodName: "GetPresentWeekCardAccess",
			Handler:    _PresentWeekCard_GetPresentWeekCardAccess_Handler,
		},
		{
			MethodName: "BuyPresentWeekCard",
			Handler:    _PresentWeekCard_BuyPresentWeekCard_Handler,
		},
		{
			MethodName: "ReceivePresentWeekCardReward",
			Handler:    _PresentWeekCard_ReceivePresentWeekCardReward_Handler,
		},
		{
			MethodName: "GetAllAwardInfo",
			Handler:    _PresentWeekCard_GetAllAwardInfo_Handler,
		},
		{
			MethodName: "ManualOnlineEvent",
			Handler:    _PresentWeekCard_ManualOnlineEvent_Handler,
		},
		{
			MethodName: "GetPresentWeekCardConfig",
			Handler:    _PresentWeekCard_GetPresentWeekCardConfig_Handler,
		},
		{
			MethodName: "SetPresentWeekCardConfig",
			Handler:    _PresentWeekCard_SetPresentWeekCardConfig_Handler,
		},
		{
			MethodName: "UpdatePresentWeekCardConfig",
			Handler:    _PresentWeekCard_UpdatePresentWeekCardConfig_Handler,
		},
		{
			MethodName: "DeletePresentWeekCardConfig",
			Handler:    _PresentWeekCard_DeletePresentWeekCardConfig_Handler,
		},
		{
			MethodName: "UpdatePresentWeekCardAwardInfo",
			Handler:    _PresentWeekCard_UpdatePresentWeekCardAwardInfo_Handler,
		},
		{
			MethodName: "GetPresentWeekCardAwardInfo",
			Handler:    _PresentWeekCard_GetPresentWeekCardAwardInfo_Handler,
		},
		{
			MethodName: "GetWeekCardInfoLocal",
			Handler:    _PresentWeekCard_GetWeekCardInfoLocal_Handler,
		},
		{
			MethodName: "GetAwardTotalCount",
			Handler:    _PresentWeekCard_GetAwardTotalCount_Handler,
		},
		{
			MethodName: "GetAwardOrderIds",
			Handler:    _PresentWeekCard_GetAwardOrderIds_Handler,
		},
		{
			MethodName: "GetConsumeTotalCount",
			Handler:    _PresentWeekCard_GetConsumeTotalCount_Handler,
		},
		{
			MethodName: "GetConsumeOrderIds",
			Handler:    _PresentWeekCard_GetConsumeOrderIds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "present-week-card/present-week-card.proto",
}

func init() {
	proto.RegisterFile("present-week-card/present-week-card.proto", fileDescriptor_present_week_card_7d29b0795bb0f9a6)
}

var fileDescriptor_present_week_card_7d29b0795bb0f9a6 = []byte{
	// 1943 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x59, 0x5f, 0x53, 0xdb, 0xca,
	0x15, 0x47, 0x26, 0x80, 0x7d, 0x28, 0x89, 0xb2, 0x37, 0xe1, 0x1a, 0x13, 0xfe, 0x29, 0x25, 0x25,
	0xb7, 0x01, 0x5a, 0x32, 0xb9, 0xd3, 0x76, 0xe6, 0x4e, 0xaf, 0xb0, 0x15, 0xa2, 0x1b, 0x63, 0x7b,
	0x64, 0x41, 0x2e, 0xed, 0xc3, 0x8e, 0x90, 0x17, 0xd0, 0x44, 0x96, 0x1c, 0x69, 0x0d, 0x78, 0xfa,
	0xd4, 0x76, 0xfa, 0xd0, 0x4e, 0xe7, 0xbe, 0xf6, 0x1b, 0xf4, 0xb1, 0xef, 0x7d, 0xe9, 0x27, 0xe8,
	0x47, 0xe8, 0x4b, 0x3f, 0x42, 0xbf, 0x40, 0xa7, 0xb3, 0xab, 0x35, 0xd8, 0xf2, 0xfa, 0x0f, 0x49,
	0xdb, 0x37, 0x74, 0xfe, 0xed, 0x6f, 0x7f, 0x7b, 0xce, 0xd9, 0xb3, 0x06, 0x9e, 0xb7, 0x22, 0x12,
	0x93, 0x80, 0x6e, 0x5f, 0x11, 0xf2, 0x7e, 0xdb, 0x75, 0xa2, 0xc6, 0xee, 0x80, 0x64, 0xa7, 0x15,
	0x85, 0x34, 0x44, 0x0f, 0x85, 0x02, 0x33, 0x05, 0x66, 0x8a, 0xc2, 0x5a, 0x44, 0xdc, 0x30, 0x70,
	0x3d, 0x9f, 0x6c, 0x5f, 0xee, 0xed, 0xf6, 0x7e, 0x24, 0x3e, 0xda, 0x6f, 0x14, 0x58, 0xd0, 0xaf,
	0x9c, 0xa8, 0x61, 0x52, 0xd2, 0x34, 0x83, 0xb3, 0x10, 0x2d, 0x41, 0xd6, 0x61, 0x02, 0xec, 0x35,
	0xf2, 0xca, 0xba, 0xb2, 0x95, 0xb3, 0xe6, 0xf8, 0xb7, 0xd9, 0x40, 0x8b, 0x30, 0xeb, 0x34, 0xc3,
	0x76, 0x40, 0xf3, 0x99, 0x75, 0x65, 0x6b, 0xc1, 0x12, 0x5f, 0x68, 0x05, 0x20, 0x71, 0xa1, 0x9d,
	0x16, 0xc9, 0x4f, 0x73, 0x5d, 0x8e, 0x4b, 0xec, 0x4e, 0x8b, 0xa0, 0x35, 0x98, 0x4f, 0xd4, 0x97,
	0x8e, 0xdf, 0x26, 0xf9, 0x7b, 0x5c, 0x9f, 0x78, 0x1c, 0x33, 0x89, 0xf6, 0x07, 0x05, 0xee, 0x97,
	0x1c, 0xcf, 0xef, 0x24, 0x48, 0x18, 0x8a, 0x45, 0x98, 0x8d, 0xa9, 0x43, 0xdb, 0x31, 0xc7, 0xb0,
	0x60, 0x89, 0x2f, 0xf4, 0xf3, 0xee, 0x52, 0xbe, 0x17, 0x33, 0x18, 0xd3, 0x5b, 0xf3, 0x7b, 0xeb,
	0x3b, 0x03, 0x1b, 0xdf, 0xe9, 0xdb, 0x93, 0x00, 0x53, 0xf6, 0x62, 0x7a, 0x0b, 0x26, 0x8c, 0x1a,
	0x24, 0x12, 0x60, 0x93, 0x98, 0x55, 0x26, 0xd1, 0xfe, 0x39, 0x0d, 0x9f, 0xd5, 0x92, 0x78, 0xef,
	0x08, 0x79, 0x5f, 0xec, 0x22, 0xfa, 0x1c, 0xe6, 0xdc, 0x1e, 0x5a, 0x16, 0xac, 0x59, 0x37, 0x61,
	0x65, 0x19, 0x72, 0xd4, 0x23, 0x11, 0x0e, 0x9c, 0x26, 0xe1, 0xc4, 0xe4, 0xac, 0x2c, 0x13, 0x54,
	0x9c, 0x26, 0x41, 0x8f, 0x60, 0xa6, 0x15, 0x79, 0x6e, 0x97, 0x95, 0xe4, 0x03, 0x6d, 0xc0, 0xf7,
	0xc2, 0xc8, 0x3b, 0xf7, 0x02, 0x9c, 0x28, 0x13, 0x4a, 0xe6, 0x13, 0x59, 0x8d, 0x9b, 0xac, 0x00,
	0x9c, 0xb6, 0x3b, 0x58, 0x90, 0x30, 0x93, 0x70, 0x7a, 0xda, 0xee, 0xd4, 0x13, 0x1e, 0xbe, 0xee,
	0xe3, 0x61, 0x96, 0xf3, 0xb0, 0x21, 0xe1, 0xa1, 0x9f, 0xd6, 0x5e, 0x22, 0x5e, 0x00, 0x8a, 0x89,
	0xef, 0x7b, 0xc1, 0x39, 0x6e, 0x85, 0x5e, 0x40, 0x31, 0x25, 0xd7, 0x34, 0x3f, 0xc7, 0xf1, 0xab,
	0x42, 0x53, 0x63, 0x0a, 0x9b, 0x5c, 0x73, 0xda, 0xc8, 0x75, 0xcb, 0x8b, 0x08, 0xa6, 0x5e, 0x93,
	0xe4, 0xb3, 0x09, 0x6d, 0x89, 0xc8, 0xf6, 0x9a, 0x84, 0xa5, 0x0d, 0xc3, 0xcb, 0xb5, 0x39, 0xae,
	0x9d, 0x3b, 0x6d, 0x77, 0xb8, 0x6a, 0x19, 0x72, 0x6e, 0xc7, 0xf5, 0x09, 0x76, 0x03, 0x9a, 0x07,
	0xae, 0xcb, 0x72, 0x41, 0x31, 0xa0, 0x5a, 0x0c, 0xb9, 0xfd, 0x9b, 0x5d, 0x15, 0x60, 0x71, 0xff,
	0xe8, 0x04, 0xd7, 0x6d, 0xdd, 0x3e, 0xaa, 0xe3, 0xa3, 0x4a, 0xbd, 0x66, 0x14, 0xcd, 0xd7, 0xa6,
	0x51, 0x52, 0xa7, 0xd0, 0x22, 0xa0, 0x1e, 0x5d, 0xa5, 0x6a, 0xe3, 0xfd, 0xa3, 0x13, 0x55, 0x49,
	0xf9, 0xe8, 0x65, 0xcb, 0xd0, 0x4b, 0x27, 0x5c, 0x97, 0x49, 0xf9, 0x94, 0xab, 0xc5, 0xb7, 0x66,
	0xe5, 0x40, 0x9d, 0xd6, 0x0e, 0xa1, 0x70, 0x40, 0x68, 0xea, 0x94, 0x8d, 0x80, 0x46, 0x1d, 0x8b,
	0x7c, 0x40, 0x2a, 0x4c, 0xb7, 0x6f, 0x4e, 0x99, 0xfd, 0xc9, 0x0e, 0xc3, 0xbd, 0x70, 0x82, 0x80,
	0xf8, 0xec, 0xf8, 0x93, 0xe4, 0xcf, 0x09, 0x89, 0xd9, 0xd0, 0xfe, 0x9e, 0x81, 0xe5, 0xa1, 0xf1,
	0xe2, 0x16, 0x23, 0xef, 0xc2, 0xb9, 0x24, 0xd8, 0x71, 0x5d, 0x12, 0x27, 0x19, 0x9d, 0xb5, 0x80,
	0x89, 0x74, 0x2e, 0x41, 0x9b, 0x70, 0x3f, 0x22, 0x2e, 0xf1, 0x2e, 0x49, 0xf7, 0xc0, 0x93, 0x35,
	0x16, 0x84, 0x54, 0xd0, 0xf3, 0x1c, 0x1e, 0x06, 0x84, 0x34, 0x30, 0x0d, 0xb1, 0xd3, 0xa6, 0x21,
	0x8e, 0x2f, 0xc2, 0x2b, 0x9e, 0x58, 0x59, 0xeb, 0x3e, 0x53, 0xd8, 0xa1, 0xde, 0xa6, 0x61, 0xfd,
	0x22, 0xbc, 0x62, 0x19, 0x16, 0x53, 0xa7, 0x83, 0x63, 0x56, 0xf2, 0x8d, 0xb8, 0x9b, 0x61, 0x4c,
	0x56, 0x4f, 0x44, 0x68, 0x15, 0xe6, 0xc9, 0x25, 0x89, 0x3a, 0x38, 0xc0, 0x0d, 0xa7, 0xd3, 0x4d,
	0x31, 0x2e, 0xaa, 0x94, 0x9c, 0x8e, 0x76, 0x05, 0x0b, 0x56, 0xdf, 0xf2, 0xab, 0x50, 0xb0, 0x8c,
	0xa2, 0x61, 0x1e, 0x1b, 0xf2, 0x13, 0x5a, 0x83, 0xe5, 0x94, 0xfe, 0x9d, 0x6e, 0xda, 0xd8, 0xae,
	0x62, 0xcb, 0x28, 0x1e, 0xab, 0x0a, 0xda, 0x84, 0x8d, 0x94, 0x41, 0xa5, 0x8a, 0xf5, 0x77, 0xba,
	0x55, 0xc2, 0xfa, 0xb1, 0x6e, 0x96, 0xf5, 0xfd, 0xb2, 0xa1, 0x66, 0xb4, 0x6d, 0x58, 0x1a, 0x64,
	0x93, 0xa7, 0xaf, 0xec, 0x70, 0x34, 0x47, 0x76, 0x98, 0x89, 0x79, 0xdc, 0x42, 0x45, 0xc8, 0xb9,
	0x37, 0x75, 0xa2, 0xf0, 0x3a, 0x79, 0x26, 0xa9, 0x13, 0x99, 0x7b, 0xd6, 0x15, 0xb5, 0xa2, 0x11,
	0x78, 0xbc, 0xdf, 0xee, 0xa4, 0x6c, 0xe4, 0xa9, 0xd2, 0xd3, 0x26, 0x32, 0x7d, 0x6d, 0x82, 0xd5,
	0x7c, 0x9b, 0xc6, 0x5e, 0x43, 0x94, 0x10, 0x3b, 0xb7, 0x69, 0x6b, 0x5e, 0xc8, 0x58, 0xa1, 0x68,
	0x7b, 0xb0, 0x28, 0x5b, 0x26, 0x6e, 0xa1, 0x3c, 0xcc, 0x9d, 0x3a, 0xbe, 0x13, 0xb8, 0x44, 0xac,
	0xd5, 0xfd, 0x64, 0xbd, 0x73, 0x4d, 0x1c, 0xd3, 0x80, 0xe3, 0xd5, 0xff, 0x02, 0x25, 0xab, 0xf4,
	0x88, 0xb8, 0x97, 0xd8, 0x6b, 0x5c, 0x8b, 0xb4, 0x9a, 0x63, 0xdf, 0x66, 0xe3, 0x5a, 0xfb, 0x4e,
	0x81, 0xf5, 0xd1, 0x60, 0xe2, 0x56, 0xaa, 0x75, 0x29, 0x1f, 0xd1, 0xba, 0xb6, 0x40, 0xbd, 0xf2,
	0xe8, 0x05, 0x26, 0xd7, 0x34, 0x72, 0x30, 0x97, 0xf3, 0x6d, 0x64, 0xad, 0xfb, 0x4c, 0x6e, 0x30,
	0x31, 0x77, 0xd4, 0x1e, 0x01, 0x3a, 0x20, 0x54, 0xf7, 0xfd, 0xdb, 0x38, 0xe4, 0x83, 0x76, 0x0c,
	0x9f, 0x0d, 0x48, 0xe3, 0x56, 0xea, 0x6e, 0x51, 0xee, 0x7c, 0xb7, 0x68, 0x7f, 0xc9, 0x0c, 0x5c,
	0x1d, 0xc5, 0x30, 0x38, 0xfb, 0x7f, 0x5f, 0x1d, 0xf2, 0xce, 0x3e, 0x33, 0xa4, 0xb3, 0xbf, 0x84,
	0xc5, 0xa6, 0x17, 0x60, 0xdf, 0x89, 0x29, 0x6e, 0x86, 0x01, 0xbd, 0xc0, 0x6e, 0x18, 0xc4, 0xed,
	0x26, 0xc9, 0xcf, 0xf2, 0xb3, 0xff, 0xac, 0xe9, 0x05, 0x65, 0x27, 0xa6, 0x87, 0x4c, 0x57, 0x4c,
	0x54, 0x68, 0x07, 0x98, 0x18, 0x5f, 0x78, 0x31, 0x0d, 0xa3, 0xce, 0x8d, 0xc7, 0x1c, 0xf7, 0x78,
	0xd8, 0xf4, 0x82, 0x37, 0x89, 0xa6, 0x6b, 0xff, 0x18, 0x66, 0xbd, 0x18, 0x37, 0x88, 0xcf, 0x6f,
	0x8e, 0xac, 0x35, 0xe3, 0xc5, 0x25, 0xe2, 0x6b, 0x7f, 0x55, 0x20, 0x9f, 0x22, 0xec, 0x76, 0x04,
	0x18, 0xca, 0xda, 0xd7, 0x92, 0x19, 0xe0, 0x6e, 0x09, 0xb4, 0xcf, 0x6e, 0xb3, 0xdb, 0xdc, 0x61,
	0x04, 0x4f, 0x14, 0x02, 0xc8, 0x6d, 0x6a, 0xfd, 0x12, 0x56, 0xea, 0xc3, 0xba, 0x54, 0x9b, 0xc4,
	0x14, 0xfd, 0x0c, 0xee, 0xb9, 0x61, 0x70, 0xc6, 0xc1, 0x4f, 0xd4, 0x74, 0x58, 0xae, 0x58, 0xdc,
	0x47, 0x5b, 0x87, 0xd5, 0xfa, 0xd0, 0x9e, 0x16, 0x06, 0x31, 0xd1, 0x36, 0x60, 0x6d, 0xb0, 0xeb,
	0xb1, 0x08, 0xde, 0xb9, 0x00, 0xa0, 0x9d, 0xc3, 0xfa, 0x70, 0x93, 0x24, 0xcc, 0x47, 0xb6, 0x47,
	0x8e, 0xf4, 0xb6, 0x3d, 0x7e, 0x05, 0xda, 0xe0, 0x42, 0xbd, 0x15, 0xc7, 0xf9, 0x18, 0x76, 0x9e,
	0xda, 0x07, 0x78, 0x3a, 0xd2, 0x5d, 0x40, 0xfd, 0xa6, 0x7b, 0xec, 0x5e, 0x70, 0x16, 0x0a, 0x56,
	0x7f, 0x38, 0x1e, 0x6b, 0x3a, 0x01, 0xd8, 0x9f, 0xda, 0x6f, 0xa7, 0x41, 0x3b, 0x6a, 0x35, 0x1c,
	0x4a, 0x46, 0x31, 0x38, 0x3c, 0x05, 0x57, 0x00, 0xce, 0x3c, 0xe2, 0x8b, 0x89, 0x57, 0x0c, 0x04,
	0x5c, 0xc2, 0x27, 0xde, 0x47, 0x30, 0x93, 0xcc, 0xba, 0xa2, 0x74, 0xf9, 0x07, 0xab, 0xf6, 0x98,
	0x46, 0x3d, 0x53, 0x70, 0xce, 0xca, 0xc6, 0x34, 0x4a, 0x66, 0xe0, 0x7f, 0x29, 0x30, 0x9f, 0x20,
	0x7a, 0xcd, 0xc2, 0xa0, 0x27, 0x90, 0x3f, 0xaa, 0x95, 0x74, 0xdb, 0xc0, 0xaf, 0x4d, 0xa3, 0x5c,
	0x1a, 0x1c, 0x86, 0xfa, 0xb4, 0x35, 0xcb, 0x2c, 0x1a, 0xaa, 0x82, 0x56, 0x60, 0xa9, 0x4f, 0x5e,
	0xb5, 0xcc, 0x03, 0xb3, 0x22, 0xd4, 0x19, 0x36, 0x2b, 0xf5, 0xa9, 0x6d, 0xd3, 0xb0, 0x70, 0x45,
	0x3f, 0x34, 0xd4, 0x69, 0xf4, 0x03, 0x78, 0xda, 0xa7, 0x3b, 0x34, 0x2b, 0xb8, 0xac, 0xd7, 0x6d,
	0x7c, 0x58, 0xad, 0xd8, 0x6f, 0x70, 0xb1, 0x5a, 0xa9, 0x1f, 0x1d, 0x1a, 0xea, 0x3d, 0xf4, 0x7d,
	0x58, 0x1f, 0x30, 0x7c, 0x63, 0xd6, 0xed, 0xaa, 0x75, 0x72, 0x63, 0x35, 0x83, 0x34, 0x58, 0x65,
	0x56, 0x56, 0xd7, 0xaa, 0x6e, 0x94, 0xcb, 0x0c, 0x49, 0xd5, 0xac, 0xd8, 0xd8, 0x36, 0xbe, 0xb5,
	0xd5, 0x59, 0x6d, 0x13, 0x9e, 0x8e, 0x3c, 0x04, 0x91, 0xea, 0x7f, 0x54, 0x60, 0x53, 0x6a, 0x37,
	0x71, 0x8a, 0xa5, 0x72, 0x27, 0xf3, 0x49, 0xb9, 0xb3, 0x05, 0xcf, 0xc6, 0xa1, 0x11, 0xc0, 0xbf,
	0x02, 0xad, 0x44, 0x7c, 0xf2, 0x91, 0x49, 0xc6, 0xe8, 0x19, 0xe9, 0x2e, 0x56, 0xf9, 0x92, 0x0f,
	0x9f, 0xbd, 0x4d, 0xa2, 0x1c, 0xba, 0x8e, 0x3f, 0x36, 0xfc, 0x9f, 0x15, 0x78, 0x22, 0x77, 0x14,
	0x05, 0xf7, 0x09, 0x0d, 0xec, 0xbf, 0x4a, 0xf8, 0x5b, 0xc8, 0x1f, 0x3a, 0x41, 0xdb, 0xf1, 0xab,
	0x81, 0xef, 0x05, 0xc4, 0xb8, 0x24, 0x01, 0xed, 0xee, 0x4e, 0x3a, 0xab, 0x13, 0x66, 0xd1, 0x57,
	0x9a, 0x5c, 0xc2, 0x4a, 0x53, 0x5b, 0x86, 0x25, 0x49, 0xb0, 0x64, 0xc7, 0x5f, 0xfc, 0x4d, 0x81,
	0x07, 0x1c, 0x42, 0x32, 0xf1, 0xf2, 0x5a, 0xde, 0x80, 0x95, 0x64, 0x44, 0x15, 0x23, 0xab, 0x7d,
	0x52, 0x33, 0x52, 0xd5, 0xb8, 0x02, 0x4b, 0x83, 0x26, 0xdd, 0xd7, 0x86, 0xc2, 0x4a, 0x61, 0x50,
	0xdd, 0x37, 0x1a, 0x67, 0xd0, 0x33, 0xd0, 0x06, 0x6d, 0xba, 0x8f, 0x19, 0x31, 0x34, 0x97, 0xd4,
	0x69, 0xf9, 0x52, 0xc6, 0xb7, 0x35, 0xd3, 0x32, 0x4a, 0xea, 0xbd, 0x2f, 0xfe, 0xdd, 0xfb, 0x9c,
	0x17, 0x8f, 0xef, 0xe5, 0xc4, 0xc1, 0xb4, 0x8d, 0x43, 0x19, 0xf8, 0x65, 0xf8, 0x3c, 0x6d, 0x50,
	0xd3, 0x8b, 0x6f, 0xf5, 0x03, 0xd6, 0x4f, 0x56, 0xa1, 0x90, 0x56, 0x56, 0xaa, 0xfb, 0x65, 0x03,
	0x17, 0x75, 0xab, 0xa4, 0x66, 0x6e, 0xe1, 0xdc, 0xea, 0x0f, 0xcd, 0x22, 0xae, 0xdb, 0x27, 0x65,
	0xd6, 0x53, 0x96, 0xe0, 0x71, 0x5a, 0xfd, 0xa6, 0x6a, 0xd5, 0x59, 0x17, 0x59, 0x04, 0x24, 0x36,
	0x72, 0x54, 0x33, 0x2c, 0x5c, 0x2b, 0xeb, 0x27, 0x86, 0xa5, 0xce, 0xc8, 0x56, 0x2c, 0x19, 0xc5,
	0xaa, 0xa5, 0xdb, 0x66, 0xb5, 0xa2, 0xce, 0xca, 0x56, 0x64, 0x0d, 0xac, 0x56, 0xd6, 0x6d, 0x43,
	0x9d, 0xdb, 0xfb, 0xc7, 0x03, 0x78, 0x90, 0xca, 0x29, 0x74, 0x05, 0x8b, 0xf2, 0x07, 0x02, 0x7a,
	0x21, 0xc9, 0xc8, 0xa1, 0x4f, 0x8f, 0xc2, 0xf6, 0x1d, 0xac, 0xe3, 0x96, 0x36, 0x85, 0x7e, 0x05,
	0x79, 0xc9, 0xc5, 0x96, 0x3c, 0xf9, 0x26, 0x0b, 0xd6, 0x7d, 0x93, 0x16, 0x76, 0xee, 0x62, 0xce,
	0x17, 0x7f, 0x0f, 0x68, 0xf0, 0x31, 0x81, 0xb6, 0x24, 0x71, 0xa4, 0x4f, 0x9b, 0xc2, 0xf3, 0x09,
	0x2d, 0xf9, 0x62, 0xbf, 0x57, 0xe0, 0xc9, 0xa8, 0xc1, 0x1f, 0xed, 0x49, 0xa2, 0x8d, 0x79, 0xb6,
	0x14, 0x5e, 0xde, 0xd9, 0x87, 0x63, 0x39, 0x85, 0x07, 0xa9, 0xe9, 0x1e, 0x6d, 0xca, 0xd9, 0x4b,
	0xbd, 0x0b, 0x0a, 0xcf, 0x26, 0x31, 0xe3, 0x6b, 0xb4, 0xe0, 0xe1, 0x40, 0x17, 0x41, 0xb2, 0xfe,
	0x36, 0xac, 0x71, 0x15, 0x5e, 0x4c, 0x66, 0x2c, 0x7a, 0xfc, 0x14, 0xfa, 0x9d, 0x22, 0x4b, 0xa6,
	0xe4, 0x2a, 0x90, 0xb2, 0x3b, 0x66, 0x3a, 0x94, 0xb2, 0x3b, 0x6e, 0x5c, 0xd4, 0xa6, 0xd0, 0xaf,
	0x15, 0xc8, 0xd7, 0x87, 0xe1, 0xf8, 0x91, 0x24, 0xe6, 0xc8, 0x21, 0xb9, 0xf0, 0xe3, 0x3b, 0x78,
	0xdc, 0x60, 0xf8, 0x4e, 0x81, 0xe5, 0x11, 0x83, 0x03, 0x7a, 0x25, 0x09, 0x3a, 0x7e, 0xda, 0x2b,
	0x7c, 0x79, 0x57, 0xb7, 0x3e, 0x40, 0x23, 0xae, 0x6a, 0x29, 0xa0, 0xf1, 0x93, 0x81, 0x14, 0xd0,
	0x24, 0x13, 0xc1, 0x14, 0xfa, 0x93, 0x02, 0xab, 0xa3, 0x87, 0x14, 0xf4, 0x93, 0x49, 0x77, 0x9b,
	0x9e, 0xb2, 0x0a, 0x3f, 0xfd, 0x08, 0xcf, 0x3e, 0xaa, 0x46, 0x4c, 0xfb, 0x52, 0xaa, 0xc6, 0x3f,
	0x2e, 0xa4, 0x54, 0x4d, 0xf0, 0xa8, 0xd0, 0xa6, 0x50, 0x07, 0x1e, 0xc9, 0xa6, 0x20, 0x34, 0xa4,
	0xe3, 0x0e, 0x9b, 0xb3, 0x0a, 0xbb, 0x13, 0xdb, 0xdf, 0x2c, 0x6d, 0x26, 0xbf, 0x4e, 0xf0, 0x1f,
	0xca, 0x43, 0xea, 0xf8, 0x45, 0xfe, 0x6b, 0xfa, 0x12, 0x6b, 0x72, 0xc9, 0xef, 0xf4, 0xc7, 0x7b,
	0x3b, 0xb6, 0xd7, 0x24, 0x96, 0x13, 0x9c, 0x13, 0xd6, 0xa0, 0x16, 0xfb, 0x54, 0xdc, 0x5c, 0x34,
	0xa4, 0x6f, 0x40, 0xed, 0x86, 0xe2, 0x3f, 0x63, 0x9b, 0x8d, 0x78, 0x54, 0xa0, 0x7e, 0x55, 0xd7,
	0x43, 0xc4, 0x7a, 0xcb, 0x19, 0x11, 0x4f, 0xf7, 0x4f, 0x05, 0x56, 0xe6, 0x7b, 0x14, 0xc1, 0x3e,
	0x15, 0xda, 0xfe, 0xab, 0x5f, 0xbc, 0x3c, 0x0f, 0x7d, 0x27, 0x38, 0xdf, 0x79, 0xb5, 0x47, 0xe9,
	0x8e, 0x1b, 0x36, 0x77, 0xf9, 0xff, 0x31, 0xdc, 0xd0, 0xdf, 0x8d, 0x49, 0x74, 0xe9, 0xb9, 0x24,
	0x1e, 0xfc, 0xff, 0xc8, 0xe9, 0x2c, 0x37, 0x7a, 0xf9, 0x9f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xad,
	0xf8, 0x59, 0xcf, 0x4d, 0x19, 0x00, 0x00,
}
