// Code generated by protoc-gen-gogo.
// source: src/realNameAuthSvr/realnameauth.proto
// DO NOT EDIT!

/*
	Package realnameauth is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/realNameAuthSvr/realnameauth.proto

	It has these top-level messages:
		AuthTypeInfo
		AuthIdCardInfo
		AuthInfo
		GetIsEnableCheckReq
		GetIsEnableCheckResp
		AddAuthOperHistoryReq
		AddAuthOperHistoryResp
		AuthInfoReq
		AuthInfoResp
		GetAuthInfoReq
		GetAuthInfoResp
		GetUserRealNameAuthInfoV2Req
		GetUserRealNameAuthInfoV2Resp
		GetUserIdentityExpireDateReq
		GetUserIdentityExpireDateResp
		GetUserIdentityInfoReq
		GetUserIdentityInfoResp
		UserIdentifyInfo
		BatchGetUserIdentifyInfoReq
		BatchGetUserIdentifyInfoResp
		AddUserIdentityInfoReq
		AddUserIdentityInfoResp
		UpdateUserIdentityInfoReq
		UpdateUserIdentityInfoResp
		AddOrUpdateUserIdentityInfoReq
		AddOrUpdateUserIdentityInfoResp
		DelUserIdentityInfoReq
		DelUserIdentityInfoResp
		AddIdentityExpireUserPushTimeReq
		AddIdentityExpireUserPushTimeResp
		RecordUserAuthPhoneReq
		RecordUserAuthPhoneResp
		GetUserAuthPhoneReq
		GetUserAuthPhoneResp
		GetPhoneBindNumberReq
		GetPhoneBindNumberResp
		RecordUserIdentityInfoReq
		RecordUserIdentityInfoResp
		GetRealNameCurrVersionReq
		GetRealNameCurrVersionResp
		ParentGuardianSwitchReq
		ParentGuardianSwitchResp
		ParentGuardianCheckPasswordReq
		ParentGuardianCheckPasswordResp
		ParentGuardianStateReq
		ParentGuardianStateResp
		ParentGuardianInfo
		BatchGetParentGuardianInfoReq
		BatchGetParentGuardianInfoResp
		CheckIdentityByFaceReq
		CheckIdentityByFaceResp
		CheckAppealCntIsOverLimitReq
		CheckAppealCntIsOverLimitResp
		ParentGuardianUpdatePwdReq
		RealNameAuthInfo
		GetRealNameAuthTokenReq
		GetRealNameAuthTokenResp
		GetRealNameFaceIdTokenByUidReq
		GetRealNameFaceIdTokenByUidResp
		ApplyRealNameAuthDataReq
		ApplyRealNameAuthDataResp
		RelieveRealNameAuthReq
		RelieveRealNameErrDetail
		RelieveRealNameAuthResp
		GetRealNameApplyInfoReq
		GetRealNameApplyInfoResp
		DelRealNameInfoReq
		DelRealNameInfoResp
		GetTheSameRealNameUserListReq
		GetTheSameRealNameUserListResp
		GetFaceAuthAliTokenReq
		GetFaceAuthAliTokenResp
		GetFaceAuthAliResultReq
		GetFaceAuthAliResultResp
		GetFaceAuthAliTokenByUidReq
		GetFaceAuthAliTokenByUidResp
		CheckFaceByAliTokenReq
		CheckFaceByAliTokenResp
		AuthByTwoElementReq
		AuthByTwoElementResp
		CheckIsInNameBlackListReq
		CheckIsInNameBlackListResp
		GetFaceRecognitionProviderReq
		GetFaceRecognitionProviderResp
		GetFaceRecognitionCertifyIdReq
		FaceRecognitionCertifyData
		GetFaceRecognitionCertifyIdResp
		GetFaceRecognitionResultReq
		GetFaceRecognitionResult
		GetFaceRecognitionResultResp
*/
package realnameauth

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type EOperType int32

const (
	EOperType_ENUM_REALNAME_OPER_SET   EOperType = 1
	EOperType_ENUM_REALNAME_OPER_UNSET EOperType = 2
	EOperType_ENUM_REALNAME_OPER_RESET EOperType = 3
)

var EOperType_name = map[int32]string{
	1: "ENUM_REALNAME_OPER_SET",
	2: "ENUM_REALNAME_OPER_UNSET",
	3: "ENUM_REALNAME_OPER_RESET",
}
var EOperType_value = map[string]int32{
	"ENUM_REALNAME_OPER_SET":   1,
	"ENUM_REALNAME_OPER_UNSET": 2,
	"ENUM_REALNAME_OPER_RESET": 3,
}

func (x EOperType) Enum() *EOperType {
	p := new(EOperType)
	*p = x
	return p
}
func (x EOperType) String() string {
	return proto.EnumName(EOperType_name, int32(x))
}
func (x *EOperType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(EOperType_value, data, "EOperType")
	if err != nil {
		return err
	}
	*x = EOperType(value)
	return nil
}
func (EOperType) EnumDescriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{0} }

type EAuthPhoneType int32

const (
	EAuthPhoneType_ENUM_REALNAME_AUTH_PHONE_TYPE     EAuthPhoneType = 1
	EAuthPhoneType_ENUM_REALNAME_SECURITY_PHONE_TYPE EAuthPhoneType = 2
	EAuthPhoneType_ENUM_REALNAME_TT_PHONE_TYPE       EAuthPhoneType = 3
)

var EAuthPhoneType_name = map[int32]string{
	1: "ENUM_REALNAME_AUTH_PHONE_TYPE",
	2: "ENUM_REALNAME_SECURITY_PHONE_TYPE",
	3: "ENUM_REALNAME_TT_PHONE_TYPE",
}
var EAuthPhoneType_value = map[string]int32{
	"ENUM_REALNAME_AUTH_PHONE_TYPE":     1,
	"ENUM_REALNAME_SECURITY_PHONE_TYPE": 2,
	"ENUM_REALNAME_TT_PHONE_TYPE":       3,
}

func (x EAuthPhoneType) Enum() *EAuthPhoneType {
	p := new(EAuthPhoneType)
	*p = x
	return p
}
func (x EAuthPhoneType) String() string {
	return proto.EnumName(EAuthPhoneType_name, int32(x))
}
func (x *EAuthPhoneType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(EAuthPhoneType_value, data, "EAuthPhoneType")
	if err != nil {
		return err
	}
	*x = EAuthPhoneType(value)
	return nil
}
func (EAuthPhoneType) EnumDescriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{1} }

// 认证类型
type AuthType int32

const (
	AuthType_ENUM_AUTH_TYPE_IDCARD AuthType = 1
	AuthType_ENUM_AUTH_TYPE_FACE   AuthType = 2
)

var AuthType_name = map[int32]string{
	1: "ENUM_AUTH_TYPE_IDCARD",
	2: "ENUM_AUTH_TYPE_FACE",
}
var AuthType_value = map[string]int32{
	"ENUM_AUTH_TYPE_IDCARD": 1,
	"ENUM_AUTH_TYPE_FACE":   2,
}

func (x AuthType) Enum() *AuthType {
	p := new(AuthType)
	*p = x
	return p
}
func (x AuthType) String() string {
	return proto.EnumName(AuthType_name, int32(x))
}
func (x *AuthType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(AuthType_value, data, "AuthType")
	if err != nil {
		return err
	}
	*x = AuthType(value)
	return nil
}
func (AuthType) EnumDescriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{2} }

// 认证状态
type EAuthStatus int32

const (
	EAuthStatus_ENUM_AUTH_NONE      EAuthStatus = 1
	EAuthStatus_ENUM_AUTH_CHECK     EAuthStatus = 2
	EAuthStatus_ENUM_AUTH_PASS      EAuthStatus = 3
	EAuthStatus_ENUM_AUTH_UNAPPROVE EAuthStatus = 4
)

var EAuthStatus_name = map[int32]string{
	1: "ENUM_AUTH_NONE",
	2: "ENUM_AUTH_CHECK",
	3: "ENUM_AUTH_PASS",
	4: "ENUM_AUTH_UNAPPROVE",
}
var EAuthStatus_value = map[string]int32{
	"ENUM_AUTH_NONE":      1,
	"ENUM_AUTH_CHECK":     2,
	"ENUM_AUTH_PASS":      3,
	"ENUM_AUTH_UNAPPROVE": 4,
}

func (x EAuthStatus) Enum() *EAuthStatus {
	p := new(EAuthStatus)
	*p = x
	return p
}
func (x EAuthStatus) String() string {
	return proto.EnumName(EAuthStatus_name, int32(x))
}
func (x *EAuthStatus) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(EAuthStatus_value, data, "EAuthStatus")
	if err != nil {
		return err
	}
	*x = EAuthStatus(value)
	return nil
}
func (EAuthStatus) EnumDescriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{3} }

type EAuthType int32

const (
	EAuthType_ENUM_REALNAME_AUTH_PHONE EAuthType = 1
)

var EAuthType_name = map[int32]string{
	1: "ENUM_REALNAME_AUTH_PHONE",
}
var EAuthType_value = map[string]int32{
	"ENUM_REALNAME_AUTH_PHONE": 1,
}

func (x EAuthType) Enum() *EAuthType {
	p := new(EAuthType)
	*p = x
	return p
}
func (x EAuthType) String() string {
	return proto.EnumName(EAuthType_name, int32(x))
}
func (x *EAuthType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(EAuthType_value, data, "EAuthType")
	if err != nil {
		return err
	}
	*x = EAuthType(value)
	return nil
}
func (EAuthType) EnumDescriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{4} }

// 认证类型信息
type AuthTypeInfo struct {
	AuthType   uint32 `protobuf:"varint,1,opt,name=auth_type,json=authType" json:"auth_type"`
	AuthStauts uint32 `protobuf:"varint,2,opt,name=auth_stauts,json=authStauts" json:"auth_stauts"`
}

func (m *AuthTypeInfo) Reset()                    { *m = AuthTypeInfo{} }
func (m *AuthTypeInfo) String() string            { return proto.CompactTextString(m) }
func (*AuthTypeInfo) ProtoMessage()               {}
func (*AuthTypeInfo) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{0} }

func (m *AuthTypeInfo) GetAuthType() uint32 {
	if m != nil {
		return m.AuthType
	}
	return 0
}

func (m *AuthTypeInfo) GetAuthStauts() uint32 {
	if m != nil {
		return m.AuthStauts
	}
	return 0
}

// 认证的身份证信息
type AuthIdCardInfo struct {
	Name        string `protobuf:"bytes,1,opt,name=name" json:"name"`
	IdentityNum string `protobuf:"bytes,2,opt,name=identity_num,json=identityNum" json:"identity_num"`
}

func (m *AuthIdCardInfo) Reset()                    { *m = AuthIdCardInfo{} }
func (m *AuthIdCardInfo) String() string            { return proto.CompactTextString(m) }
func (*AuthIdCardInfo) ProtoMessage()               {}
func (*AuthIdCardInfo) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{1} }

func (m *AuthIdCardInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AuthIdCardInfo) GetIdentityNum() string {
	if m != nil {
		return m.IdentityNum
	}
	return ""
}

// 认证信息, 从货币组那边获取存到redis的结构
type AuthInfo struct {
	Uid      uint32 `protobuf:"varint,1,opt,name=uid" json:"uid"`
	Status   int32  `protobuf:"varint,2,opt,name=status" json:"status"`
	Birthday string `protobuf:"bytes,3,opt,name=birthday" json:"birthday"`
	Level    uint32 `protobuf:"varint,4,opt,name=level" json:"level"`
}

func (m *AuthInfo) Reset()                    { *m = AuthInfo{} }
func (m *AuthInfo) String() string            { return proto.CompactTextString(m) }
func (*AuthInfo) ProtoMessage()               {}
func (*AuthInfo) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{2} }

func (m *AuthInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AuthInfo) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *AuthInfo) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

func (m *AuthInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type GetIsEnableCheckReq struct {
}

func (m *GetIsEnableCheckReq) Reset()                    { *m = GetIsEnableCheckReq{} }
func (m *GetIsEnableCheckReq) String() string            { return proto.CompactTextString(m) }
func (*GetIsEnableCheckReq) ProtoMessage()               {}
func (*GetIsEnableCheckReq) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{3} }

type GetIsEnableCheckResp struct {
	IsEnableCheck bool `protobuf:"varint,1,req,name=is_enable_check,json=isEnableCheck" json:"is_enable_check"`
}

func (m *GetIsEnableCheckResp) Reset()         { *m = GetIsEnableCheckResp{} }
func (m *GetIsEnableCheckResp) String() string { return proto.CompactTextString(m) }
func (*GetIsEnableCheckResp) ProtoMessage()    {}
func (*GetIsEnableCheckResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{4}
}

func (m *GetIsEnableCheckResp) GetIsEnableCheck() bool {
	if m != nil {
		return m.IsEnableCheck
	}
	return false
}

type AddAuthOperHistoryReq struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	OperType uint32 `protobuf:"varint,2,req,name=oper_type,json=operType" json:"oper_type"`
	AuthType uint32 `protobuf:"varint,3,req,name=auth_type,json=authType" json:"auth_type"`
	LogInfo  string `protobuf:"bytes,4,req,name=log_info,json=logInfo" json:"log_info"`
}

func (m *AddAuthOperHistoryReq) Reset()         { *m = AddAuthOperHistoryReq{} }
func (m *AddAuthOperHistoryReq) String() string { return proto.CompactTextString(m) }
func (*AddAuthOperHistoryReq) ProtoMessage()    {}
func (*AddAuthOperHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{5}
}

func (m *AddAuthOperHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddAuthOperHistoryReq) GetOperType() uint32 {
	if m != nil {
		return m.OperType
	}
	return 0
}

func (m *AddAuthOperHistoryReq) GetAuthType() uint32 {
	if m != nil {
		return m.AuthType
	}
	return 0
}

func (m *AddAuthOperHistoryReq) GetLogInfo() string {
	if m != nil {
		return m.LogInfo
	}
	return ""
}

type AddAuthOperHistoryResp struct {
}

func (m *AddAuthOperHistoryResp) Reset()         { *m = AddAuthOperHistoryResp{} }
func (m *AddAuthOperHistoryResp) String() string { return proto.CompactTextString(m) }
func (*AddAuthOperHistoryResp) ProtoMessage()    {}
func (*AddAuthOperHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{6}
}

// add 2018/11/15/11:15 by T1035
type AuthInfoReq struct {
	Uid    uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Status int32  `protobuf:"varint,2,req,name=status" json:"status"`
}

func (m *AuthInfoReq) Reset()                    { *m = AuthInfoReq{} }
func (m *AuthInfoReq) String() string            { return proto.CompactTextString(m) }
func (*AuthInfoReq) ProtoMessage()               {}
func (*AuthInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{7} }

func (m *AuthInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AuthInfoReq) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type AuthInfoResp struct {
}

func (m *AuthInfoResp) Reset()                    { *m = AuthInfoResp{} }
func (m *AuthInfoResp) String() string            { return proto.CompactTextString(m) }
func (*AuthInfoResp) ProtoMessage()               {}
func (*AuthInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{8} }

type GetAuthInfoReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetAuthInfoReq) Reset()                    { *m = GetAuthInfoReq{} }
func (m *GetAuthInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetAuthInfoReq) ProtoMessage()               {}
func (*GetAuthInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{9} }

func (m *GetAuthInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAuthInfoResp struct {
	// 忘记给1 占坑了。如果改回1，会导致线上上一个版本的 pb 解析失败
	Status       int32             `protobuf:"varint,2,req,name=status" json:"status"`
	RealnameInfo *RealNameAuthInfo `protobuf:"bytes,3,opt,name=realname_info,json=realnameInfo" json:"realname_info,omitempty"`
}

func (m *GetAuthInfoResp) Reset()                    { *m = GetAuthInfoResp{} }
func (m *GetAuthInfoResp) String() string            { return proto.CompactTextString(m) }
func (*GetAuthInfoResp) ProtoMessage()               {}
func (*GetAuthInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{10} }

func (m *GetAuthInfoResp) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetAuthInfoResp) GetRealnameInfo() *RealNameAuthInfo {
	if m != nil {
		return m.RealnameInfo
	}
	return nil
}

// 获取实名认证信息
type GetUserRealNameAuthInfoV2Req struct {
	Uid              uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	IsNeedAuthPhone  bool   `protobuf:"varint,2,opt,name=is_need_auth_phone,json=isNeedAuthPhone" json:"is_need_auth_phone"`
	IsNeedIdcardInfo bool   `protobuf:"varint,3,opt,name=is_need_idcard_info,json=isNeedIdcardInfo" json:"is_need_idcard_info"`
}

func (m *GetUserRealNameAuthInfoV2Req) Reset()         { *m = GetUserRealNameAuthInfoV2Req{} }
func (m *GetUserRealNameAuthInfoV2Req) String() string { return proto.CompactTextString(m) }
func (*GetUserRealNameAuthInfoV2Req) ProtoMessage()    {}
func (*GetUserRealNameAuthInfoV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{11}
}

func (m *GetUserRealNameAuthInfoV2Req) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserRealNameAuthInfoV2Req) GetIsNeedAuthPhone() bool {
	if m != nil {
		return m.IsNeedAuthPhone
	}
	return false
}

func (m *GetUserRealNameAuthInfoV2Req) GetIsNeedIdcardInfo() bool {
	if m != nil {
		return m.IsNeedIdcardInfo
	}
	return false
}

type GetUserRealNameAuthInfoV2Resp struct {
	AuthList        []*AuthTypeInfo `protobuf:"bytes,1,rep,name=auth_list,json=authList" json:"auth_list,omitempty"`
	IdcardInfo      *AuthIdCardInfo `protobuf:"bytes,2,opt,name=idcard_info,json=idcardInfo" json:"idcard_info,omitempty"`
	AuthPhone       string          `protobuf:"bytes,3,opt,name=auth_phone,json=authPhone" json:"auth_phone"`
	IsAdult         bool            `protobuf:"varint,4,opt,name=is_adult,json=isAdult" json:"is_adult"`
	Age             uint32          `protobuf:"varint,5,opt,name=age" json:"age"`
	RealnameVersion uint32          `protobuf:"varint,6,opt,name=realname_version,json=realnameVersion" json:"realname_version"`
}

func (m *GetUserRealNameAuthInfoV2Resp) Reset()         { *m = GetUserRealNameAuthInfoV2Resp{} }
func (m *GetUserRealNameAuthInfoV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetUserRealNameAuthInfoV2Resp) ProtoMessage()    {}
func (*GetUserRealNameAuthInfoV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{12}
}

func (m *GetUserRealNameAuthInfoV2Resp) GetAuthList() []*AuthTypeInfo {
	if m != nil {
		return m.AuthList
	}
	return nil
}

func (m *GetUserRealNameAuthInfoV2Resp) GetIdcardInfo() *AuthIdCardInfo {
	if m != nil {
		return m.IdcardInfo
	}
	return nil
}

func (m *GetUserRealNameAuthInfoV2Resp) GetAuthPhone() string {
	if m != nil {
		return m.AuthPhone
	}
	return ""
}

func (m *GetUserRealNameAuthInfoV2Resp) GetIsAdult() bool {
	if m != nil {
		return m.IsAdult
	}
	return false
}

func (m *GetUserRealNameAuthInfoV2Resp) GetAge() uint32 {
	if m != nil {
		return m.Age
	}
	return 0
}

func (m *GetUserRealNameAuthInfoV2Resp) GetRealnameVersion() uint32 {
	if m != nil {
		return m.RealnameVersion
	}
	return 0
}

type GetUserIdentityExpireDateReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserIdentityExpireDateReq) Reset()         { *m = GetUserIdentityExpireDateReq{} }
func (m *GetUserIdentityExpireDateReq) String() string { return proto.CompactTextString(m) }
func (*GetUserIdentityExpireDateReq) ProtoMessage()    {}
func (*GetUserIdentityExpireDateReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{13}
}

func (m *GetUserIdentityExpireDateReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserIdentityExpireDateResp struct {
	IdentityExpireDate string `protobuf:"bytes,1,opt,name=identity_expire_date,json=identityExpireDate" json:"identity_expire_date"`
}

func (m *GetUserIdentityExpireDateResp) Reset()         { *m = GetUserIdentityExpireDateResp{} }
func (m *GetUserIdentityExpireDateResp) String() string { return proto.CompactTextString(m) }
func (*GetUserIdentityExpireDateResp) ProtoMessage()    {}
func (*GetUserIdentityExpireDateResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{14}
}

func (m *GetUserIdentityExpireDateResp) GetIdentityExpireDate() string {
	if m != nil {
		return m.IdentityExpireDate
	}
	return ""
}

type GetUserIdentityInfoReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserIdentityInfoReq) Reset()         { *m = GetUserIdentityInfoReq{} }
func (m *GetUserIdentityInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserIdentityInfoReq) ProtoMessage()    {}
func (*GetUserIdentityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{15}
}

func (m *GetUserIdentityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserIdentityInfoResp struct {
	// 未认证/认证未通过/审核中/该功能上线前已认证的用户，数据库没有他们的身份证信息
	// 该功能上线前已认证的用户的身份证信息会逐步补充
	HaveInfo          bool   `protobuf:"varint,1,req,name=have_info,json=haveInfo" json:"have_info"`
	IsAdult           bool   `protobuf:"varint,3,req,name=is_adult,json=isAdult" json:"is_adult"`
	IdentityNumber    string `protobuf:"bytes,4,opt,name=identity_number,json=identityNumber" json:"identity_number"`
	IdentityValidTime string `protobuf:"bytes,5,opt,name=identity_valid_time,json=identityValidTime" json:"identity_valid_time"`
	Age               uint32 `protobuf:"varint,6,opt,name=age" json:"age"`
	Status            int32  `protobuf:"varint,7,opt,name=status" json:"status"`
}

func (m *GetUserIdentityInfoResp) Reset()         { *m = GetUserIdentityInfoResp{} }
func (m *GetUserIdentityInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserIdentityInfoResp) ProtoMessage()    {}
func (*GetUserIdentityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{16}
}

func (m *GetUserIdentityInfoResp) GetHaveInfo() bool {
	if m != nil {
		return m.HaveInfo
	}
	return false
}

func (m *GetUserIdentityInfoResp) GetIsAdult() bool {
	if m != nil {
		return m.IsAdult
	}
	return false
}

func (m *GetUserIdentityInfoResp) GetIdentityNumber() string {
	if m != nil {
		return m.IdentityNumber
	}
	return ""
}

func (m *GetUserIdentityInfoResp) GetIdentityValidTime() string {
	if m != nil {
		return m.IdentityValidTime
	}
	return ""
}

func (m *GetUserIdentityInfoResp) GetAge() uint32 {
	if m != nil {
		return m.Age
	}
	return 0
}

func (m *GetUserIdentityInfoResp) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type UserIdentifyInfo struct {
	Uid               uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	HaveInfo          bool   `protobuf:"varint,2,req,name=have_info,json=haveInfo" json:"have_info"`
	IsAdult           bool   `protobuf:"varint,3,opt,name=is_adult,json=isAdult" json:"is_adult"`
	IdentityNumber    string `protobuf:"bytes,4,opt,name=identity_number,json=identityNumber" json:"identity_number"`
	IdentityValidTime string `protobuf:"bytes,5,opt,name=identity_valid_time,json=identityValidTime" json:"identity_valid_time"`
	Name              string `protobuf:"bytes,6,opt,name=name" json:"name"`
	Level             uint32 `protobuf:"varint,7,opt,name=level" json:"level"`
}

func (m *UserIdentifyInfo) Reset()                    { *m = UserIdentifyInfo{} }
func (m *UserIdentifyInfo) String() string            { return proto.CompactTextString(m) }
func (*UserIdentifyInfo) ProtoMessage()               {}
func (*UserIdentifyInfo) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{17} }

func (m *UserIdentifyInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserIdentifyInfo) GetHaveInfo() bool {
	if m != nil {
		return m.HaveInfo
	}
	return false
}

func (m *UserIdentifyInfo) GetIsAdult() bool {
	if m != nil {
		return m.IsAdult
	}
	return false
}

func (m *UserIdentifyInfo) GetIdentityNumber() string {
	if m != nil {
		return m.IdentityNumber
	}
	return ""
}

func (m *UserIdentifyInfo) GetIdentityValidTime() string {
	if m != nil {
		return m.IdentityValidTime
	}
	return ""
}

func (m *UserIdentifyInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UserIdentifyInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

// 这个批量接口只返回用户 UserIdentifyInfo(uid,have_info,is_adult)
type BatchGetUserIdentifyInfoReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatchGetUserIdentifyInfoReq) Reset()         { *m = BatchGetUserIdentifyInfoReq{} }
func (m *BatchGetUserIdentifyInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserIdentifyInfoReq) ProtoMessage()    {}
func (*BatchGetUserIdentifyInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{18}
}

func (m *BatchGetUserIdentifyInfoReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetUserIdentifyInfoResp struct {
	InfoList []*UserIdentifyInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList" json:"info_list,omitempty"`
}

func (m *BatchGetUserIdentifyInfoResp) Reset()         { *m = BatchGetUserIdentifyInfoResp{} }
func (m *BatchGetUserIdentifyInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserIdentifyInfoResp) ProtoMessage()    {}
func (*BatchGetUserIdentifyInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{19}
}

func (m *BatchGetUserIdentifyInfoResp) GetInfoList() []*UserIdentifyInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type AddUserIdentityInfoReq struct {
	Uid               uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	IdentityNumber    string `protobuf:"bytes,2,req,name=identity_number,json=identityNumber" json:"identity_number"`
	IdentityValidTime string `protobuf:"bytes,3,req,name=identity_valid_time,json=identityValidTime" json:"identity_valid_time"`
	HavePush          int32  `protobuf:"varint,4,req,name=have_push,json=havePush" json:"have_push"`
	Name              string `protobuf:"bytes,5,opt,name=name" json:"name"`
	Level             uint32 `protobuf:"varint,6,opt,name=level" json:"level"`
}

func (m *AddUserIdentityInfoReq) Reset()         { *m = AddUserIdentityInfoReq{} }
func (m *AddUserIdentityInfoReq) String() string { return proto.CompactTextString(m) }
func (*AddUserIdentityInfoReq) ProtoMessage()    {}
func (*AddUserIdentityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{20}
}

func (m *AddUserIdentityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserIdentityInfoReq) GetIdentityNumber() string {
	if m != nil {
		return m.IdentityNumber
	}
	return ""
}

func (m *AddUserIdentityInfoReq) GetIdentityValidTime() string {
	if m != nil {
		return m.IdentityValidTime
	}
	return ""
}

func (m *AddUserIdentityInfoReq) GetHavePush() int32 {
	if m != nil {
		return m.HavePush
	}
	return 0
}

func (m *AddUserIdentityInfoReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AddUserIdentityInfoReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type AddUserIdentityInfoResp struct {
}

func (m *AddUserIdentityInfoResp) Reset()         { *m = AddUserIdentityInfoResp{} }
func (m *AddUserIdentityInfoResp) String() string { return proto.CompactTextString(m) }
func (*AddUserIdentityInfoResp) ProtoMessage()    {}
func (*AddUserIdentityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{21}
}

type UpdateUserIdentityInfoReq struct {
	Uid               uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	IdentityNumber    string `protobuf:"bytes,2,opt,name=identity_number,json=identityNumber" json:"identity_number"`
	IdentityValidTime string `protobuf:"bytes,3,opt,name=identity_valid_time,json=identityValidTime" json:"identity_valid_time"`
	HavePush          int32  `protobuf:"varint,4,req,name=have_push,json=havePush" json:"have_push"`
}

func (m *UpdateUserIdentityInfoReq) Reset()         { *m = UpdateUserIdentityInfoReq{} }
func (m *UpdateUserIdentityInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUserIdentityInfoReq) ProtoMessage()    {}
func (*UpdateUserIdentityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{22}
}

func (m *UpdateUserIdentityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateUserIdentityInfoReq) GetIdentityNumber() string {
	if m != nil {
		return m.IdentityNumber
	}
	return ""
}

func (m *UpdateUserIdentityInfoReq) GetIdentityValidTime() string {
	if m != nil {
		return m.IdentityValidTime
	}
	return ""
}

func (m *UpdateUserIdentityInfoReq) GetHavePush() int32 {
	if m != nil {
		return m.HavePush
	}
	return 0
}

type UpdateUserIdentityInfoResp struct {
}

func (m *UpdateUserIdentityInfoResp) Reset()         { *m = UpdateUserIdentityInfoResp{} }
func (m *UpdateUserIdentityInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpdateUserIdentityInfoResp) ProtoMessage()    {}
func (*UpdateUserIdentityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{23}
}

type AddOrUpdateUserIdentityInfoReq struct {
	Uid               uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	IdentityNumber    string `protobuf:"bytes,2,opt,name=identity_number,json=identityNumber" json:"identity_number"`
	IdentityValidTime string `protobuf:"bytes,3,opt,name=identity_valid_time,json=identityValidTime" json:"identity_valid_time"`
	HavePush          int32  `protobuf:"varint,4,req,name=have_push,json=havePush" json:"have_push"`
	Name              string `protobuf:"bytes,5,opt,name=name" json:"name"`
}

func (m *AddOrUpdateUserIdentityInfoReq) Reset()         { *m = AddOrUpdateUserIdentityInfoReq{} }
func (m *AddOrUpdateUserIdentityInfoReq) String() string { return proto.CompactTextString(m) }
func (*AddOrUpdateUserIdentityInfoReq) ProtoMessage()    {}
func (*AddOrUpdateUserIdentityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{24}
}

func (m *AddOrUpdateUserIdentityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddOrUpdateUserIdentityInfoReq) GetIdentityNumber() string {
	if m != nil {
		return m.IdentityNumber
	}
	return ""
}

func (m *AddOrUpdateUserIdentityInfoReq) GetIdentityValidTime() string {
	if m != nil {
		return m.IdentityValidTime
	}
	return ""
}

func (m *AddOrUpdateUserIdentityInfoReq) GetHavePush() int32 {
	if m != nil {
		return m.HavePush
	}
	return 0
}

func (m *AddOrUpdateUserIdentityInfoReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type AddOrUpdateUserIdentityInfoResp struct {
}

func (m *AddOrUpdateUserIdentityInfoResp) Reset()         { *m = AddOrUpdateUserIdentityInfoResp{} }
func (m *AddOrUpdateUserIdentityInfoResp) String() string { return proto.CompactTextString(m) }
func (*AddOrUpdateUserIdentityInfoResp) ProtoMessage()    {}
func (*AddOrUpdateUserIdentityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{25}
}

type DelUserIdentityInfoReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *DelUserIdentityInfoReq) Reset()         { *m = DelUserIdentityInfoReq{} }
func (m *DelUserIdentityInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelUserIdentityInfoReq) ProtoMessage()    {}
func (*DelUserIdentityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{26}
}

func (m *DelUserIdentityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type DelUserIdentityInfoResp struct {
}

func (m *DelUserIdentityInfoResp) Reset()         { *m = DelUserIdentityInfoResp{} }
func (m *DelUserIdentityInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelUserIdentityInfoResp) ProtoMessage()    {}
func (*DelUserIdentityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{27}
}

type AddIdentityExpireUserPushTimeReq struct {
	Uid      int32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	PushTime int32 `protobuf:"varint,2,req,name=push_time,json=pushTime" json:"push_time"`
}

func (m *AddIdentityExpireUserPushTimeReq) Reset()         { *m = AddIdentityExpireUserPushTimeReq{} }
func (m *AddIdentityExpireUserPushTimeReq) String() string { return proto.CompactTextString(m) }
func (*AddIdentityExpireUserPushTimeReq) ProtoMessage()    {}
func (*AddIdentityExpireUserPushTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{28}
}

func (m *AddIdentityExpireUserPushTimeReq) GetUid() int32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddIdentityExpireUserPushTimeReq) GetPushTime() int32 {
	if m != nil {
		return m.PushTime
	}
	return 0
}

type AddIdentityExpireUserPushTimeResp struct {
}

func (m *AddIdentityExpireUserPushTimeResp) Reset()         { *m = AddIdentityExpireUserPushTimeResp{} }
func (m *AddIdentityExpireUserPushTimeResp) String() string { return proto.CompactTextString(m) }
func (*AddIdentityExpireUserPushTimeResp) ProtoMessage()    {}
func (*AddIdentityExpireUserPushTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{29}
}

type RecordUserAuthPhoneReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	AuthPhone string `protobuf:"bytes,2,req,name=auth_phone,json=authPhone" json:"auth_phone"`
	PhoneType uint32 `protobuf:"varint,3,opt,name=phone_type,json=phoneType" json:"phone_type"`
}

func (m *RecordUserAuthPhoneReq) Reset()         { *m = RecordUserAuthPhoneReq{} }
func (m *RecordUserAuthPhoneReq) String() string { return proto.CompactTextString(m) }
func (*RecordUserAuthPhoneReq) ProtoMessage()    {}
func (*RecordUserAuthPhoneReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{30}
}

func (m *RecordUserAuthPhoneReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecordUserAuthPhoneReq) GetAuthPhone() string {
	if m != nil {
		return m.AuthPhone
	}
	return ""
}

func (m *RecordUserAuthPhoneReq) GetPhoneType() uint32 {
	if m != nil {
		return m.PhoneType
	}
	return 0
}

type RecordUserAuthPhoneResp struct {
}

func (m *RecordUserAuthPhoneResp) Reset()         { *m = RecordUserAuthPhoneResp{} }
func (m *RecordUserAuthPhoneResp) String() string { return proto.CompactTextString(m) }
func (*RecordUserAuthPhoneResp) ProtoMessage()    {}
func (*RecordUserAuthPhoneResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{31}
}

type GetUserAuthPhoneReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserAuthPhoneReq) Reset()         { *m = GetUserAuthPhoneReq{} }
func (m *GetUserAuthPhoneReq) String() string { return proto.CompactTextString(m) }
func (*GetUserAuthPhoneReq) ProtoMessage()    {}
func (*GetUserAuthPhoneReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{32}
}

func (m *GetUserAuthPhoneReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserAuthPhoneResp struct {
	HavePhone bool   `protobuf:"varint,1,req,name=have_phone,json=havePhone" json:"have_phone"`
	AuthPhone string `protobuf:"bytes,2,opt,name=auth_phone,json=authPhone" json:"auth_phone"`
}

func (m *GetUserAuthPhoneResp) Reset()         { *m = GetUserAuthPhoneResp{} }
func (m *GetUserAuthPhoneResp) String() string { return proto.CompactTextString(m) }
func (*GetUserAuthPhoneResp) ProtoMessage()    {}
func (*GetUserAuthPhoneResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{33}
}

func (m *GetUserAuthPhoneResp) GetHavePhone() bool {
	if m != nil {
		return m.HavePhone
	}
	return false
}

func (m *GetUserAuthPhoneResp) GetAuthPhone() string {
	if m != nil {
		return m.AuthPhone
	}
	return ""
}

type GetPhoneBindNumberReq struct {
	Phone string `protobuf:"bytes,1,req,name=phone" json:"phone"`
}

func (m *GetPhoneBindNumberReq) Reset()         { *m = GetPhoneBindNumberReq{} }
func (m *GetPhoneBindNumberReq) String() string { return proto.CompactTextString(m) }
func (*GetPhoneBindNumberReq) ProtoMessage()    {}
func (*GetPhoneBindNumberReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{34}
}

func (m *GetPhoneBindNumberReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

type GetPhoneBindNumberResp struct {
	BindNumber uint32 `protobuf:"varint,1,req,name=bind_number,json=bindNumber" json:"bind_number"`
}

func (m *GetPhoneBindNumberResp) Reset()         { *m = GetPhoneBindNumberResp{} }
func (m *GetPhoneBindNumberResp) String() string { return proto.CompactTextString(m) }
func (*GetPhoneBindNumberResp) ProtoMessage()    {}
func (*GetPhoneBindNumberResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{35}
}

func (m *GetPhoneBindNumberResp) GetBindNumber() uint32 {
	if m != nil {
		return m.BindNumber
	}
	return 0
}

type RecordUserIdentityInfoReq struct {
	Uid               uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	IdentityNum       string `protobuf:"bytes,2,req,name=identity_num,json=identityNum" json:"identity_num"`
	IdentityValidTime string `protobuf:"bytes,3,req,name=identity_valid_time,json=identityValidTime" json:"identity_valid_time"`
	Name              string `protobuf:"bytes,4,req,name=name" json:"name"`
}

func (m *RecordUserIdentityInfoReq) Reset()         { *m = RecordUserIdentityInfoReq{} }
func (m *RecordUserIdentityInfoReq) String() string { return proto.CompactTextString(m) }
func (*RecordUserIdentityInfoReq) ProtoMessage()    {}
func (*RecordUserIdentityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{36}
}

func (m *RecordUserIdentityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecordUserIdentityInfoReq) GetIdentityNum() string {
	if m != nil {
		return m.IdentityNum
	}
	return ""
}

func (m *RecordUserIdentityInfoReq) GetIdentityValidTime() string {
	if m != nil {
		return m.IdentityValidTime
	}
	return ""
}

func (m *RecordUserIdentityInfoReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type RecordUserIdentityInfoResp struct {
}

func (m *RecordUserIdentityInfoResp) Reset()         { *m = RecordUserIdentityInfoResp{} }
func (m *RecordUserIdentityInfoResp) String() string { return proto.CompactTextString(m) }
func (*RecordUserIdentityInfoResp) ProtoMessage()    {}
func (*RecordUserIdentityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{37}
}

type GetRealNameCurrVersionReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetRealNameCurrVersionReq) Reset()         { *m = GetRealNameCurrVersionReq{} }
func (m *GetRealNameCurrVersionReq) String() string { return proto.CompactTextString(m) }
func (*GetRealNameCurrVersionReq) ProtoMessage()    {}
func (*GetRealNameCurrVersionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{38}
}

func (m *GetRealNameCurrVersionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetRealNameCurrVersionResp struct {
	Version uint32 `protobuf:"varint,1,req,name=version" json:"version"`
}

func (m *GetRealNameCurrVersionResp) Reset()         { *m = GetRealNameCurrVersionResp{} }
func (m *GetRealNameCurrVersionResp) String() string { return proto.CompactTextString(m) }
func (*GetRealNameCurrVersionResp) ProtoMessage()    {}
func (*GetRealNameCurrVersionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{39}
}

func (m *GetRealNameCurrVersionResp) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

type ParentGuardianSwitchReq struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	OnOff      bool   `protobuf:"varint,2,req,name=on_off,json=onOff" json:"on_off"`
	Password   string `protobuf:"bytes,3,req,name=password" json:"password"`
	IsForceOff bool   `protobuf:"varint,4,opt,name=is_force_off,json=isForceOff" json:"is_force_off"`
}

func (m *ParentGuardianSwitchReq) Reset()         { *m = ParentGuardianSwitchReq{} }
func (m *ParentGuardianSwitchReq) String() string { return proto.CompactTextString(m) }
func (*ParentGuardianSwitchReq) ProtoMessage()    {}
func (*ParentGuardianSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{40}
}

func (m *ParentGuardianSwitchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ParentGuardianSwitchReq) GetOnOff() bool {
	if m != nil {
		return m.OnOff
	}
	return false
}

func (m *ParentGuardianSwitchReq) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

func (m *ParentGuardianSwitchReq) GetIsForceOff() bool {
	if m != nil {
		return m.IsForceOff
	}
	return false
}

type ParentGuardianSwitchResp struct {
}

func (m *ParentGuardianSwitchResp) Reset()         { *m = ParentGuardianSwitchResp{} }
func (m *ParentGuardianSwitchResp) String() string { return proto.CompactTextString(m) }
func (*ParentGuardianSwitchResp) ProtoMessage()    {}
func (*ParentGuardianSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{41}
}

type ParentGuardianCheckPasswordReq struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Password string `protobuf:"bytes,3,req,name=password" json:"password"`
}

func (m *ParentGuardianCheckPasswordReq) Reset()         { *m = ParentGuardianCheckPasswordReq{} }
func (m *ParentGuardianCheckPasswordReq) String() string { return proto.CompactTextString(m) }
func (*ParentGuardianCheckPasswordReq) ProtoMessage()    {}
func (*ParentGuardianCheckPasswordReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{42}
}

func (m *ParentGuardianCheckPasswordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ParentGuardianCheckPasswordReq) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

type ParentGuardianCheckPasswordResp struct {
	IsPass bool `protobuf:"varint,1,req,name=is_pass,json=isPass" json:"is_pass"`
}

func (m *ParentGuardianCheckPasswordResp) Reset()         { *m = ParentGuardianCheckPasswordResp{} }
func (m *ParentGuardianCheckPasswordResp) String() string { return proto.CompactTextString(m) }
func (*ParentGuardianCheckPasswordResp) ProtoMessage()    {}
func (*ParentGuardianCheckPasswordResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{43}
}

func (m *ParentGuardianCheckPasswordResp) GetIsPass() bool {
	if m != nil {
		return m.IsPass
	}
	return false
}

type ParentGuardianStateReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *ParentGuardianStateReq) Reset()         { *m = ParentGuardianStateReq{} }
func (m *ParentGuardianStateReq) String() string { return proto.CompactTextString(m) }
func (*ParentGuardianStateReq) ProtoMessage()    {}
func (*ParentGuardianStateReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{44}
}

func (m *ParentGuardianStateReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ParentGuardianStateResp struct {
	OnOff bool `protobuf:"varint,2,req,name=on_off,json=onOff" json:"on_off"`
}

func (m *ParentGuardianStateResp) Reset()         { *m = ParentGuardianStateResp{} }
func (m *ParentGuardianStateResp) String() string { return proto.CompactTextString(m) }
func (*ParentGuardianStateResp) ProtoMessage()    {}
func (*ParentGuardianStateResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{45}
}

func (m *ParentGuardianStateResp) GetOnOff() bool {
	if m != nil {
		return m.OnOff
	}
	return false
}

type ParentGuardianInfo struct {
	Uid  uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	IsOn bool   `protobuf:"varint,2,req,name=is_on,json=isOn" json:"is_on"`
}

func (m *ParentGuardianInfo) Reset()                    { *m = ParentGuardianInfo{} }
func (m *ParentGuardianInfo) String() string            { return proto.CompactTextString(m) }
func (*ParentGuardianInfo) ProtoMessage()               {}
func (*ParentGuardianInfo) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{46} }

func (m *ParentGuardianInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ParentGuardianInfo) GetIsOn() bool {
	if m != nil {
		return m.IsOn
	}
	return false
}

type BatchGetParentGuardianInfoReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatchGetParentGuardianInfoReq) Reset()         { *m = BatchGetParentGuardianInfoReq{} }
func (m *BatchGetParentGuardianInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetParentGuardianInfoReq) ProtoMessage()    {}
func (*BatchGetParentGuardianInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{47}
}

func (m *BatchGetParentGuardianInfoReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetParentGuardianInfoResp struct {
	InfoList []*ParentGuardianInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList" json:"info_list,omitempty"`
}

func (m *BatchGetParentGuardianInfoResp) Reset()         { *m = BatchGetParentGuardianInfoResp{} }
func (m *BatchGetParentGuardianInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetParentGuardianInfoResp) ProtoMessage()    {}
func (*BatchGetParentGuardianInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{48}
}

func (m *BatchGetParentGuardianInfoResp) GetInfoList() []*ParentGuardianInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 已实名用户扫脸验证身份
type CheckIdentityByFaceReq struct {
	Uid             uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	FaceidCheckData string `protobuf:"bytes,2,req,name=faceid_check_data,json=faceidCheckData" json:"faceid_check_data"`
	FaceidToken     string `protobuf:"bytes,3,req,name=faceid_token,json=faceidToken" json:"faceid_token"`
}

func (m *CheckIdentityByFaceReq) Reset()         { *m = CheckIdentityByFaceReq{} }
func (m *CheckIdentityByFaceReq) String() string { return proto.CompactTextString(m) }
func (*CheckIdentityByFaceReq) ProtoMessage()    {}
func (*CheckIdentityByFaceReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{49}
}

func (m *CheckIdentityByFaceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckIdentityByFaceReq) GetFaceidCheckData() string {
	if m != nil {
		return m.FaceidCheckData
	}
	return ""
}

func (m *CheckIdentityByFaceReq) GetFaceidToken() string {
	if m != nil {
		return m.FaceidToken
	}
	return ""
}

type CheckIdentityByFaceResp struct {
	IsPass     bool   `protobuf:"varint,1,req,name=is_pass,json=isPass" json:"is_pass"`
	ResultCode uint32 `protobuf:"varint,2,opt,name=result_code,json=resultCode" json:"result_code"`
	ResultMsg  string `protobuf:"bytes,3,opt,name=result_msg,json=resultMsg" json:"result_msg"`
}

func (m *CheckIdentityByFaceResp) Reset()         { *m = CheckIdentityByFaceResp{} }
func (m *CheckIdentityByFaceResp) String() string { return proto.CompactTextString(m) }
func (*CheckIdentityByFaceResp) ProtoMessage()    {}
func (*CheckIdentityByFaceResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{50}
}

func (m *CheckIdentityByFaceResp) GetIsPass() bool {
	if m != nil {
		return m.IsPass
	}
	return false
}

func (m *CheckIdentityByFaceResp) GetResultCode() uint32 {
	if m != nil {
		return m.ResultCode
	}
	return 0
}

func (m *CheckIdentityByFaceResp) GetResultMsg() string {
	if m != nil {
		return m.ResultMsg
	}
	return ""
}

// 检查是否超过今日申诉限制次数
type CheckAppealCntIsOverLimitReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *CheckAppealCntIsOverLimitReq) Reset()         { *m = CheckAppealCntIsOverLimitReq{} }
func (m *CheckAppealCntIsOverLimitReq) String() string { return proto.CompactTextString(m) }
func (*CheckAppealCntIsOverLimitReq) ProtoMessage()    {}
func (*CheckAppealCntIsOverLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{51}
}

func (m *CheckAppealCntIsOverLimitReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckAppealCntIsOverLimitResp struct {
	IsOverLimit bool `protobuf:"varint,1,req,name=is_over_limit,json=isOverLimit" json:"is_over_limit"`
}

func (m *CheckAppealCntIsOverLimitResp) Reset()         { *m = CheckAppealCntIsOverLimitResp{} }
func (m *CheckAppealCntIsOverLimitResp) String() string { return proto.CompactTextString(m) }
func (*CheckAppealCntIsOverLimitResp) ProtoMessage()    {}
func (*CheckAppealCntIsOverLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{52}
}

func (m *CheckAppealCntIsOverLimitResp) GetIsOverLimit() bool {
	if m != nil {
		return m.IsOverLimit
	}
	return false
}

type ParentGuardianUpdatePwdReq struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Password string `protobuf:"bytes,2,req,name=password" json:"password"`
}

func (m *ParentGuardianUpdatePwdReq) Reset()         { *m = ParentGuardianUpdatePwdReq{} }
func (m *ParentGuardianUpdatePwdReq) String() string { return proto.CompactTextString(m) }
func (*ParentGuardianUpdatePwdReq) ProtoMessage()    {}
func (*ParentGuardianUpdatePwdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{53}
}

func (m *ParentGuardianUpdatePwdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ParentGuardianUpdatePwdReq) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

type RealNameAuthInfo struct {
	AuthPhone   string `protobuf:"bytes,1,opt,name=auth_phone,json=authPhone" json:"auth_phone"`
	Name        string `protobuf:"bytes,2,opt,name=name" json:"name"`
	IdentityNum string `protobuf:"bytes,3,opt,name=identity_num,json=identityNum" json:"identity_num"`
	Age         uint32 `protobuf:"varint,4,opt,name=age" json:"age"`
}

func (m *RealNameAuthInfo) Reset()                    { *m = RealNameAuthInfo{} }
func (m *RealNameAuthInfo) String() string            { return proto.CompactTextString(m) }
func (*RealNameAuthInfo) ProtoMessage()               {}
func (*RealNameAuthInfo) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{54} }

func (m *RealNameAuthInfo) GetAuthPhone() string {
	if m != nil {
		return m.AuthPhone
	}
	return ""
}

func (m *RealNameAuthInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RealNameAuthInfo) GetIdentityNum() string {
	if m != nil {
		return m.IdentityNum
	}
	return ""
}

func (m *RealNameAuthInfo) GetAge() uint32 {
	if m != nil {
		return m.Age
	}
	return 0
}

type GetRealNameAuthTokenReq struct {
	Uid          uint32            `protobuf:"varint,1,req,name=uid" json:"uid"`
	RealnameInfo *RealNameAuthInfo `protobuf:"bytes,2,req,name=realname_info,json=realnameInfo" json:"realname_info,omitempty"`
}

func (m *GetRealNameAuthTokenReq) Reset()         { *m = GetRealNameAuthTokenReq{} }
func (m *GetRealNameAuthTokenReq) String() string { return proto.CompactTextString(m) }
func (*GetRealNameAuthTokenReq) ProtoMessage()    {}
func (*GetRealNameAuthTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{55}
}

func (m *GetRealNameAuthTokenReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRealNameAuthTokenReq) GetRealnameInfo() *RealNameAuthInfo {
	if m != nil {
		return m.RealnameInfo
	}
	return nil
}

type GetRealNameAuthTokenResp struct {
	FaceidToken string `protobuf:"bytes,1,req,name=faceid_token,json=faceidToken" json:"faceid_token"`
	ResultCode  uint32 `protobuf:"varint,2,opt,name=result_code,json=resultCode" json:"result_code"`
	ResultMsg   string `protobuf:"bytes,3,opt,name=result_msg,json=resultMsg" json:"result_msg"`
}

func (m *GetRealNameAuthTokenResp) Reset()         { *m = GetRealNameAuthTokenResp{} }
func (m *GetRealNameAuthTokenResp) String() string { return proto.CompactTextString(m) }
func (*GetRealNameAuthTokenResp) ProtoMessage()    {}
func (*GetRealNameAuthTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{56}
}

func (m *GetRealNameAuthTokenResp) GetFaceidToken() string {
	if m != nil {
		return m.FaceidToken
	}
	return ""
}

func (m *GetRealNameAuthTokenResp) GetResultCode() uint32 {
	if m != nil {
		return m.ResultCode
	}
	return 0
}

func (m *GetRealNameAuthTokenResp) GetResultMsg() string {
	if m != nil {
		return m.ResultMsg
	}
	return ""
}

// 已实名用户通过uid获取token
type GetRealNameFaceIdTokenByUidReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetRealNameFaceIdTokenByUidReq) Reset()         { *m = GetRealNameFaceIdTokenByUidReq{} }
func (m *GetRealNameFaceIdTokenByUidReq) String() string { return proto.CompactTextString(m) }
func (*GetRealNameFaceIdTokenByUidReq) ProtoMessage()    {}
func (*GetRealNameFaceIdTokenByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{57}
}

func (m *GetRealNameFaceIdTokenByUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetRealNameFaceIdTokenByUidResp struct {
	FaceidToken string `protobuf:"bytes,1,req,name=faceid_token,json=faceidToken" json:"faceid_token"`
	ResultCode  uint32 `protobuf:"varint,2,opt,name=result_code,json=resultCode" json:"result_code"`
	ResultMsg   string `protobuf:"bytes,3,opt,name=result_msg,json=resultMsg" json:"result_msg"`
}

func (m *GetRealNameFaceIdTokenByUidResp) Reset()         { *m = GetRealNameFaceIdTokenByUidResp{} }
func (m *GetRealNameFaceIdTokenByUidResp) String() string { return proto.CompactTextString(m) }
func (*GetRealNameFaceIdTokenByUidResp) ProtoMessage()    {}
func (*GetRealNameFaceIdTokenByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{58}
}

func (m *GetRealNameFaceIdTokenByUidResp) GetFaceidToken() string {
	if m != nil {
		return m.FaceidToken
	}
	return ""
}

func (m *GetRealNameFaceIdTokenByUidResp) GetResultCode() uint32 {
	if m != nil {
		return m.ResultCode
	}
	return 0
}

func (m *GetRealNameFaceIdTokenByUidResp) GetResultMsg() string {
	if m != nil {
		return m.ResultMsg
	}
	return ""
}

type ApplyRealNameAuthDataReq struct {
	Uid             uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	FaceidToken     string `protobuf:"bytes,2,req,name=faceid_token,json=faceidToken" json:"faceid_token"`
	FaceidCheckData string `protobuf:"bytes,3,req,name=faceid_check_data,json=faceidCheckData" json:"faceid_check_data"`
}

func (m *ApplyRealNameAuthDataReq) Reset()         { *m = ApplyRealNameAuthDataReq{} }
func (m *ApplyRealNameAuthDataReq) String() string { return proto.CompactTextString(m) }
func (*ApplyRealNameAuthDataReq) ProtoMessage()    {}
func (*ApplyRealNameAuthDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{59}
}

func (m *ApplyRealNameAuthDataReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApplyRealNameAuthDataReq) GetFaceidToken() string {
	if m != nil {
		return m.FaceidToken
	}
	return ""
}

func (m *ApplyRealNameAuthDataReq) GetFaceidCheckData() string {
	if m != nil {
		return m.FaceidCheckData
	}
	return ""
}

type ApplyRealNameAuthDataResp struct {
	ResultCode uint32 `protobuf:"varint,1,opt,name=result_code,json=resultCode" json:"result_code"`
	ResultMsg  string `protobuf:"bytes,2,opt,name=result_msg,json=resultMsg" json:"result_msg"`
}

func (m *ApplyRealNameAuthDataResp) Reset()         { *m = ApplyRealNameAuthDataResp{} }
func (m *ApplyRealNameAuthDataResp) String() string { return proto.CompactTextString(m) }
func (*ApplyRealNameAuthDataResp) ProtoMessage()    {}
func (*ApplyRealNameAuthDataResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{60}
}

func (m *ApplyRealNameAuthDataResp) GetResultCode() uint32 {
	if m != nil {
		return m.ResultCode
	}
	return 0
}

func (m *ApplyRealNameAuthDataResp) GetResultMsg() string {
	if m != nil {
		return m.ResultMsg
	}
	return ""
}

type RelieveRealNameAuthReq struct {
	Uid []uint32 `protobuf:"varint,1,rep,name=uid" json:"uid,omitempty"`
}

func (m *RelieveRealNameAuthReq) Reset()         { *m = RelieveRealNameAuthReq{} }
func (m *RelieveRealNameAuthReq) String() string { return proto.CompactTextString(m) }
func (*RelieveRealNameAuthReq) ProtoMessage()    {}
func (*RelieveRealNameAuthReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{61}
}

func (m *RelieveRealNameAuthReq) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

type RelieveRealNameErrDetail struct {
	Uid    uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Reason string `protobuf:"bytes,2,req,name=reason" json:"reason"`
}

func (m *RelieveRealNameErrDetail) Reset()         { *m = RelieveRealNameErrDetail{} }
func (m *RelieveRealNameErrDetail) String() string { return proto.CompactTextString(m) }
func (*RelieveRealNameErrDetail) ProtoMessage()    {}
func (*RelieveRealNameErrDetail) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{62}
}

func (m *RelieveRealNameErrDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RelieveRealNameErrDetail) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type RelieveRealNameAuthResp struct {
	ErrDetail []*RelieveRealNameErrDetail `protobuf:"bytes,1,rep,name=errDetail" json:"errDetail,omitempty"`
	ErrMsg    string                      `protobuf:"bytes,2,opt,name=errMsg" json:"errMsg"`
}

func (m *RelieveRealNameAuthResp) Reset()         { *m = RelieveRealNameAuthResp{} }
func (m *RelieveRealNameAuthResp) String() string { return proto.CompactTextString(m) }
func (*RelieveRealNameAuthResp) ProtoMessage()    {}
func (*RelieveRealNameAuthResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{63}
}

func (m *RelieveRealNameAuthResp) GetErrDetail() []*RelieveRealNameErrDetail {
	if m != nil {
		return m.ErrDetail
	}
	return nil
}

func (m *RelieveRealNameAuthResp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type GetRealNameApplyInfoReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetRealNameApplyInfoReq) Reset()         { *m = GetRealNameApplyInfoReq{} }
func (m *GetRealNameApplyInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetRealNameApplyInfoReq) ProtoMessage()    {}
func (*GetRealNameApplyInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{64}
}

func (m *GetRealNameApplyInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetRealNameApplyInfoResp struct {
	IdentityNum string `protobuf:"bytes,1,opt,name=identity_num,json=identityNum" json:"identity_num"`
	Name        string `protobuf:"bytes,2,opt,name=name" json:"name"`
	ApplyNum    uint32 `protobuf:"varint,3,opt,name=apply_num,json=applyNum" json:"apply_num"`
}

func (m *GetRealNameApplyInfoResp) Reset()         { *m = GetRealNameApplyInfoResp{} }
func (m *GetRealNameApplyInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetRealNameApplyInfoResp) ProtoMessage()    {}
func (*GetRealNameApplyInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{65}
}

func (m *GetRealNameApplyInfoResp) GetIdentityNum() string {
	if m != nil {
		return m.IdentityNum
	}
	return ""
}

func (m *GetRealNameApplyInfoResp) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetRealNameApplyInfoResp) GetApplyNum() uint32 {
	if m != nil {
		return m.ApplyNum
	}
	return 0
}

type DelRealNameInfoReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *DelRealNameInfoReq) Reset()                    { *m = DelRealNameInfoReq{} }
func (m *DelRealNameInfoReq) String() string            { return proto.CompactTextString(m) }
func (*DelRealNameInfoReq) ProtoMessage()               {}
func (*DelRealNameInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{66} }

func (m *DelRealNameInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type DelRealNameInfoResp struct {
}

func (m *DelRealNameInfoResp) Reset()         { *m = DelRealNameInfoResp{} }
func (m *DelRealNameInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelRealNameInfoResp) ProtoMessage()    {}
func (*DelRealNameInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{67}
}

type GetTheSameRealNameUserListReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetTheSameRealNameUserListReq) Reset()         { *m = GetTheSameRealNameUserListReq{} }
func (m *GetTheSameRealNameUserListReq) String() string { return proto.CompactTextString(m) }
func (*GetTheSameRealNameUserListReq) ProtoMessage()    {}
func (*GetTheSameRealNameUserListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{68}
}

func (m *GetTheSameRealNameUserListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetTheSameRealNameUserListResp struct {
	Uids []uint32 `protobuf:"varint,1,rep,name=uids" json:"uids,omitempty"`
}

func (m *GetTheSameRealNameUserListResp) Reset()         { *m = GetTheSameRealNameUserListResp{} }
func (m *GetTheSameRealNameUserListResp) String() string { return proto.CompactTextString(m) }
func (*GetTheSameRealNameUserListResp) ProtoMessage()    {}
func (*GetTheSameRealNameUserListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{69}
}

func (m *GetTheSameRealNameUserListResp) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

// 获取人脸识别 certifyid_token
type GetFaceAuthAliTokenReq struct {
	Uid          uint32            `protobuf:"varint,1,req,name=uid" json:"uid"`
	RealnameInfo *RealNameAuthInfo `protobuf:"bytes,2,req,name=realname_info,json=realnameInfo" json:"realname_info,omitempty"`
	MetaInfo     string            `protobuf:"bytes,3,req,name=meta_info,json=metaInfo" json:"meta_info"`
}

func (m *GetFaceAuthAliTokenReq) Reset()         { *m = GetFaceAuthAliTokenReq{} }
func (m *GetFaceAuthAliTokenReq) String() string { return proto.CompactTextString(m) }
func (*GetFaceAuthAliTokenReq) ProtoMessage()    {}
func (*GetFaceAuthAliTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{70}
}

func (m *GetFaceAuthAliTokenReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFaceAuthAliTokenReq) GetRealnameInfo() *RealNameAuthInfo {
	if m != nil {
		return m.RealnameInfo
	}
	return nil
}

func (m *GetFaceAuthAliTokenReq) GetMetaInfo() string {
	if m != nil {
		return m.MetaInfo
	}
	return ""
}

type GetFaceAuthAliTokenResp struct {
	CertifyidToken string `protobuf:"bytes,1,opt,name=certifyid_token,json=certifyidToken" json:"certifyid_token"`
	ResultCode     uint32 `protobuf:"varint,2,opt,name=result_code,json=resultCode" json:"result_code"`
	ResultMsg      string `protobuf:"bytes,3,opt,name=result_msg,json=resultMsg" json:"result_msg"`
}

func (m *GetFaceAuthAliTokenResp) Reset()         { *m = GetFaceAuthAliTokenResp{} }
func (m *GetFaceAuthAliTokenResp) String() string { return proto.CompactTextString(m) }
func (*GetFaceAuthAliTokenResp) ProtoMessage()    {}
func (*GetFaceAuthAliTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{71}
}

func (m *GetFaceAuthAliTokenResp) GetCertifyidToken() string {
	if m != nil {
		return m.CertifyidToken
	}
	return ""
}

func (m *GetFaceAuthAliTokenResp) GetResultCode() uint32 {
	if m != nil {
		return m.ResultCode
	}
	return 0
}

func (m *GetFaceAuthAliTokenResp) GetResultMsg() string {
	if m != nil {
		return m.ResultMsg
	}
	return ""
}

// 查询人脸识别认证结果
type GetFaceAuthAliResultReq struct {
	Uid            uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	CertifyidToken string `protobuf:"bytes,2,req,name=certifyid_token,json=certifyidToken" json:"certifyid_token"`
	Scene          string `protobuf:"bytes,3,opt,name=scene" json:"scene"`
}

func (m *GetFaceAuthAliResultReq) Reset()         { *m = GetFaceAuthAliResultReq{} }
func (m *GetFaceAuthAliResultReq) String() string { return proto.CompactTextString(m) }
func (*GetFaceAuthAliResultReq) ProtoMessage()    {}
func (*GetFaceAuthAliResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{72}
}

func (m *GetFaceAuthAliResultReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFaceAuthAliResultReq) GetCertifyidToken() string {
	if m != nil {
		return m.CertifyidToken
	}
	return ""
}

func (m *GetFaceAuthAliResultReq) GetScene() string {
	if m != nil {
		return m.Scene
	}
	return ""
}

type GetFaceAuthAliResultResp struct {
	ResultCode uint32 `protobuf:"varint,1,opt,name=result_code,json=resultCode" json:"result_code"`
	ResultMsg  string `protobuf:"bytes,2,opt,name=result_msg,json=resultMsg" json:"result_msg"`
}

func (m *GetFaceAuthAliResultResp) Reset()         { *m = GetFaceAuthAliResultResp{} }
func (m *GetFaceAuthAliResultResp) String() string { return proto.CompactTextString(m) }
func (*GetFaceAuthAliResultResp) ProtoMessage()    {}
func (*GetFaceAuthAliResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{73}
}

func (m *GetFaceAuthAliResultResp) GetResultCode() uint32 {
	if m != nil {
		return m.ResultCode
	}
	return 0
}

func (m *GetFaceAuthAliResultResp) GetResultMsg() string {
	if m != nil {
		return m.ResultMsg
	}
	return ""
}

// 已实名用户通过uid获取token
type GetFaceAuthAliTokenByUidReq struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	MetaInfo string `protobuf:"bytes,2,req,name=meta_info,json=metaInfo" json:"meta_info"`
}

func (m *GetFaceAuthAliTokenByUidReq) Reset()         { *m = GetFaceAuthAliTokenByUidReq{} }
func (m *GetFaceAuthAliTokenByUidReq) String() string { return proto.CompactTextString(m) }
func (*GetFaceAuthAliTokenByUidReq) ProtoMessage()    {}
func (*GetFaceAuthAliTokenByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{74}
}

func (m *GetFaceAuthAliTokenByUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFaceAuthAliTokenByUidReq) GetMetaInfo() string {
	if m != nil {
		return m.MetaInfo
	}
	return ""
}

type GetFaceAuthAliTokenByUidResp struct {
	CertifyidToken string `protobuf:"bytes,1,opt,name=certifyid_token,json=certifyidToken" json:"certifyid_token"`
	ResultCode     uint32 `protobuf:"varint,2,opt,name=result_code,json=resultCode" json:"result_code"`
	ResultMsg      string `protobuf:"bytes,3,opt,name=result_msg,json=resultMsg" json:"result_msg"`
}

func (m *GetFaceAuthAliTokenByUidResp) Reset()         { *m = GetFaceAuthAliTokenByUidResp{} }
func (m *GetFaceAuthAliTokenByUidResp) String() string { return proto.CompactTextString(m) }
func (*GetFaceAuthAliTokenByUidResp) ProtoMessage()    {}
func (*GetFaceAuthAliTokenByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{75}
}

func (m *GetFaceAuthAliTokenByUidResp) GetCertifyidToken() string {
	if m != nil {
		return m.CertifyidToken
	}
	return ""
}

func (m *GetFaceAuthAliTokenByUidResp) GetResultCode() uint32 {
	if m != nil {
		return m.ResultCode
	}
	return 0
}

func (m *GetFaceAuthAliTokenByUidResp) GetResultMsg() string {
	if m != nil {
		return m.ResultMsg
	}
	return ""
}

// 已实名用户扫脸验证身份
type CheckFaceByAliTokenReq struct {
	Uid            uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	CertifyidToken string `protobuf:"bytes,2,req,name=certifyid_token,json=certifyidToken" json:"certifyid_token"`
	Scene          string `protobuf:"bytes,3,opt,name=scene" json:"scene"`
}

func (m *CheckFaceByAliTokenReq) Reset()         { *m = CheckFaceByAliTokenReq{} }
func (m *CheckFaceByAliTokenReq) String() string { return proto.CompactTextString(m) }
func (*CheckFaceByAliTokenReq) ProtoMessage()    {}
func (*CheckFaceByAliTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{76}
}

func (m *CheckFaceByAliTokenReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckFaceByAliTokenReq) GetCertifyidToken() string {
	if m != nil {
		return m.CertifyidToken
	}
	return ""
}

func (m *CheckFaceByAliTokenReq) GetScene() string {
	if m != nil {
		return m.Scene
	}
	return ""
}

type CheckFaceByAliTokenResp struct {
	IsPass bool `protobuf:"varint,1,opt,name=is_pass,json=isPass" json:"is_pass"`
}

func (m *CheckFaceByAliTokenResp) Reset()         { *m = CheckFaceByAliTokenResp{} }
func (m *CheckFaceByAliTokenResp) String() string { return proto.CompactTextString(m) }
func (*CheckFaceByAliTokenResp) ProtoMessage()    {}
func (*CheckFaceByAliTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{77}
}

func (m *CheckFaceByAliTokenResp) GetIsPass() bool {
	if m != nil {
		return m.IsPass
	}
	return false
}

// 二元素实名认证 (身份证和姓名)
type AuthByTwoElementReq struct {
	Uid          uint32            `protobuf:"varint,1,req,name=uid" json:"uid"`
	RealnameInfo *RealNameAuthInfo `protobuf:"bytes,2,req,name=realname_info,json=realnameInfo" json:"realname_info,omitempty"`
}

func (m *AuthByTwoElementReq) Reset()         { *m = AuthByTwoElementReq{} }
func (m *AuthByTwoElementReq) String() string { return proto.CompactTextString(m) }
func (*AuthByTwoElementReq) ProtoMessage()    {}
func (*AuthByTwoElementReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{78}
}

func (m *AuthByTwoElementReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AuthByTwoElementReq) GetRealnameInfo() *RealNameAuthInfo {
	if m != nil {
		return m.RealnameInfo
	}
	return nil
}

type AuthByTwoElementResp struct {
	IsAdult bool `protobuf:"varint,1,opt,name=is_adult,json=isAdult" json:"is_adult"`
}

func (m *AuthByTwoElementResp) Reset()         { *m = AuthByTwoElementResp{} }
func (m *AuthByTwoElementResp) String() string { return proto.CompactTextString(m) }
func (*AuthByTwoElementResp) ProtoMessage()    {}
func (*AuthByTwoElementResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{79}
}

func (m *AuthByTwoElementResp) GetIsAdult() bool {
	if m != nil {
		return m.IsAdult
	}
	return false
}

// 检测是否在实名姓名黑名单中
type CheckIsInNameBlackListReq struct {
	Name string `protobuf:"bytes,1,opt,name=name" json:"name"`
}

func (m *CheckIsInNameBlackListReq) Reset()         { *m = CheckIsInNameBlackListReq{} }
func (m *CheckIsInNameBlackListReq) String() string { return proto.CompactTextString(m) }
func (*CheckIsInNameBlackListReq) ProtoMessage()    {}
func (*CheckIsInNameBlackListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{80}
}

func (m *CheckIsInNameBlackListReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type CheckIsInNameBlackListResp struct {
	IsBlack bool `protobuf:"varint,1,opt,name=is_black,json=isBlack" json:"is_black"`
}

func (m *CheckIsInNameBlackListResp) Reset()         { *m = CheckIsInNameBlackListResp{} }
func (m *CheckIsInNameBlackListResp) String() string { return proto.CompactTextString(m) }
func (*CheckIsInNameBlackListResp) ProtoMessage()    {}
func (*CheckIsInNameBlackListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{81}
}

func (m *CheckIsInNameBlackListResp) GetIsBlack() bool {
	if m != nil {
		return m.IsBlack
	}
	return false
}

type GetFaceRecognitionProviderReq struct {
	AppId            string   `protobuf:"bytes,1,opt,name=app_id,json=appId" json:"app_id"`
	Uid              uint64   `protobuf:"varint,2,req,name=uid" json:"uid"`
	SupportProviders []string `protobuf:"bytes,3,rep,name=support_providers,json=supportProviders" json:"support_providers,omitempty"`
}

func (m *GetFaceRecognitionProviderReq) Reset()         { *m = GetFaceRecognitionProviderReq{} }
func (m *GetFaceRecognitionProviderReq) String() string { return proto.CompactTextString(m) }
func (*GetFaceRecognitionProviderReq) ProtoMessage()    {}
func (*GetFaceRecognitionProviderReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{82}
}

func (m *GetFaceRecognitionProviderReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *GetFaceRecognitionProviderReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFaceRecognitionProviderReq) GetSupportProviders() []string {
	if m != nil {
		return m.SupportProviders
	}
	return nil
}

// 0	人工认证
// 1	旷视人脸识别
// 2	阿里人脸识别认证
// 3	中宣部网络游戏实名认证
// 4	诺正通认证
// 5	阿里云身份证二元素
// 6	腾讯云身份证二元素
// 7	华为云人脸识别
// 8	腾讯云人脸识别
type GetFaceRecognitionProviderResp struct {
	ProviderName string `protobuf:"bytes,1,opt,name=provider_name,json=providerName" json:"provider_name"`
	ProviderCode string `protobuf:"bytes,2,opt,name=provider_code,json=providerCode" json:"provider_code"`
}

func (m *GetFaceRecognitionProviderResp) Reset()         { *m = GetFaceRecognitionProviderResp{} }
func (m *GetFaceRecognitionProviderResp) String() string { return proto.CompactTextString(m) }
func (*GetFaceRecognitionProviderResp) ProtoMessage()    {}
func (*GetFaceRecognitionProviderResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{83}
}

func (m *GetFaceRecognitionProviderResp) GetProviderName() string {
	if m != nil {
		return m.ProviderName
	}
	return ""
}

func (m *GetFaceRecognitionProviderResp) GetProviderCode() string {
	if m != nil {
		return m.ProviderCode
	}
	return ""
}

type GetFaceRecognitionCertifyIdReq struct {
	AppId        string            `protobuf:"bytes,1,opt,name=app_id,json=appId" json:"app_id"`
	ProviderCode string            `protobuf:"bytes,2,req,name=provider_code,json=providerCode" json:"provider_code"`
	Uid          uint64            `protobuf:"varint,3,req,name=uid" json:"uid"`
	MetaInfo     string            `protobuf:"bytes,4,opt,name=meta_info,json=metaInfo" json:"meta_info"`
	DeviceInfo   string            `protobuf:"bytes,5,opt,name=device_info,json=deviceInfo" json:"device_info"`
	AuthInfo     *RealNameAuthInfo `protobuf:"bytes,6,opt,name=auth_info,json=authInfo" json:"auth_info,omitempty"`
}

func (m *GetFaceRecognitionCertifyIdReq) Reset()         { *m = GetFaceRecognitionCertifyIdReq{} }
func (m *GetFaceRecognitionCertifyIdReq) String() string { return proto.CompactTextString(m) }
func (*GetFaceRecognitionCertifyIdReq) ProtoMessage()    {}
func (*GetFaceRecognitionCertifyIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{84}
}

func (m *GetFaceRecognitionCertifyIdReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *GetFaceRecognitionCertifyIdReq) GetProviderCode() string {
	if m != nil {
		return m.ProviderCode
	}
	return ""
}

func (m *GetFaceRecognitionCertifyIdReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFaceRecognitionCertifyIdReq) GetMetaInfo() string {
	if m != nil {
		return m.MetaInfo
	}
	return ""
}

func (m *GetFaceRecognitionCertifyIdReq) GetDeviceInfo() string {
	if m != nil {
		return m.DeviceInfo
	}
	return ""
}

func (m *GetFaceRecognitionCertifyIdReq) GetAuthInfo() *RealNameAuthInfo {
	if m != nil {
		return m.AuthInfo
	}
	return nil
}

// http://s-doc2.ttyuyin.com/web/#/3/1651
type FaceRecognitionCertifyData struct {
	OrderNo   string `protobuf:"bytes,1,opt,name=order_no,json=orderNo" json:"order_no"`
	CertifyId string `protobuf:"bytes,2,opt,name=certify_id,json=certifyId" json:"certify_id"`
	RequestId string `protobuf:"bytes,3,opt,name=request_id,json=requestId" json:"request_id"`
	Message   string `protobuf:"bytes,4,opt,name=message" json:"message"`
	Exclusive string `protobuf:"bytes,5,opt,name=exclusive" json:"exclusive"`
}

func (m *FaceRecognitionCertifyData) Reset()         { *m = FaceRecognitionCertifyData{} }
func (m *FaceRecognitionCertifyData) String() string { return proto.CompactTextString(m) }
func (*FaceRecognitionCertifyData) ProtoMessage()    {}
func (*FaceRecognitionCertifyData) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{85}
}

func (m *FaceRecognitionCertifyData) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

func (m *FaceRecognitionCertifyData) GetCertifyId() string {
	if m != nil {
		return m.CertifyId
	}
	return ""
}

func (m *FaceRecognitionCertifyData) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *FaceRecognitionCertifyData) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *FaceRecognitionCertifyData) GetExclusive() string {
	if m != nil {
		return m.Exclusive
	}
	return ""
}

type GetFaceRecognitionCertifyIdResp struct {
	Data *FaceRecognitionCertifyData `protobuf:"bytes,1,opt,name=data" json:"data,omitempty"`
}

func (m *GetFaceRecognitionCertifyIdResp) Reset()         { *m = GetFaceRecognitionCertifyIdResp{} }
func (m *GetFaceRecognitionCertifyIdResp) String() string { return proto.CompactTextString(m) }
func (*GetFaceRecognitionCertifyIdResp) ProtoMessage()    {}
func (*GetFaceRecognitionCertifyIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{86}
}

func (m *GetFaceRecognitionCertifyIdResp) GetData() *FaceRecognitionCertifyData {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetFaceRecognitionResultReq struct {
	AppId          string `protobuf:"bytes,1,opt,name=app_id,json=appId" json:"app_id"`
	ProviderCode   string `protobuf:"bytes,2,req,name=provider_code,json=providerCode" json:"provider_code"`
	Uid            uint64 `protobuf:"varint,3,req,name=uid" json:"uid"`
	CertifyId      string `protobuf:"bytes,4,req,name=certify_id,json=certifyId" json:"certify_id"`
	Scene          string `protobuf:"bytes,5,req,name=scene" json:"scene"`
	IsRealNameAuth bool   `protobuf:"varint,6,opt,name=is_real_name_auth,json=isRealNameAuth" json:"is_real_name_auth"`
	ProviderData   string `protobuf:"bytes,7,opt,name=provider_data,json=providerData" json:"provider_data"`
}

func (m *GetFaceRecognitionResultReq) Reset()         { *m = GetFaceRecognitionResultReq{} }
func (m *GetFaceRecognitionResultReq) String() string { return proto.CompactTextString(m) }
func (*GetFaceRecognitionResultReq) ProtoMessage()    {}
func (*GetFaceRecognitionResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{87}
}

func (m *GetFaceRecognitionResultReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *GetFaceRecognitionResultReq) GetProviderCode() string {
	if m != nil {
		return m.ProviderCode
	}
	return ""
}

func (m *GetFaceRecognitionResultReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFaceRecognitionResultReq) GetCertifyId() string {
	if m != nil {
		return m.CertifyId
	}
	return ""
}

func (m *GetFaceRecognitionResultReq) GetScene() string {
	if m != nil {
		return m.Scene
	}
	return ""
}

func (m *GetFaceRecognitionResultReq) GetIsRealNameAuth() bool {
	if m != nil {
		return m.IsRealNameAuth
	}
	return false
}

func (m *GetFaceRecognitionResultReq) GetProviderData() string {
	if m != nil {
		return m.ProviderData
	}
	return ""
}

// http://s-doc2.ttyuyin.com/web/#/3/1652
type GetFaceRecognitionResult struct {
	IsPass                      bool   `protobuf:"varint,1,req,name=is_pass,json=isPass" json:"is_pass"`
	CertifyId                   string `protobuf:"bytes,2,opt,name=certify_id,json=certifyId" json:"certify_id"`
	RequestId                   string `protobuf:"bytes,3,opt,name=request_id,json=requestId" json:"request_id"`
	ProviderResponseCode        string `protobuf:"bytes,4,opt,name=provider_response_code,json=providerResponseCode" json:"provider_response_code"`
	ProviderResponseResult      string `protobuf:"bytes,5,opt,name=provider_response_result,json=providerResponseResult" json:"provider_response_result"`
	ProviderResponseDescription string `protobuf:"bytes,6,opt,name=provider_response_description,json=providerResponseDescription" json:"provider_response_description"`
}

func (m *GetFaceRecognitionResult) Reset()         { *m = GetFaceRecognitionResult{} }
func (m *GetFaceRecognitionResult) String() string { return proto.CompactTextString(m) }
func (*GetFaceRecognitionResult) ProtoMessage()    {}
func (*GetFaceRecognitionResult) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{88}
}

func (m *GetFaceRecognitionResult) GetIsPass() bool {
	if m != nil {
		return m.IsPass
	}
	return false
}

func (m *GetFaceRecognitionResult) GetCertifyId() string {
	if m != nil {
		return m.CertifyId
	}
	return ""
}

func (m *GetFaceRecognitionResult) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *GetFaceRecognitionResult) GetProviderResponseCode() string {
	if m != nil {
		return m.ProviderResponseCode
	}
	return ""
}

func (m *GetFaceRecognitionResult) GetProviderResponseResult() string {
	if m != nil {
		return m.ProviderResponseResult
	}
	return ""
}

func (m *GetFaceRecognitionResult) GetProviderResponseDescription() string {
	if m != nil {
		return m.ProviderResponseDescription
	}
	return ""
}

type GetFaceRecognitionResultResp struct {
	Result *GetFaceRecognitionResult `protobuf:"bytes,1,opt,name=result" json:"result,omitempty"`
}

func (m *GetFaceRecognitionResultResp) Reset()         { *m = GetFaceRecognitionResultResp{} }
func (m *GetFaceRecognitionResultResp) String() string { return proto.CompactTextString(m) }
func (*GetFaceRecognitionResultResp) ProtoMessage()    {}
func (*GetFaceRecognitionResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{89}
}

func (m *GetFaceRecognitionResultResp) GetResult() *GetFaceRecognitionResult {
	if m != nil {
		return m.Result
	}
	return nil
}

func init() {
	proto.RegisterType((*AuthTypeInfo)(nil), "realnameauth.AuthTypeInfo")
	proto.RegisterType((*AuthIdCardInfo)(nil), "realnameauth.AuthIdCardInfo")
	proto.RegisterType((*AuthInfo)(nil), "realnameauth.AuthInfo")
	proto.RegisterType((*GetIsEnableCheckReq)(nil), "realnameauth.GetIsEnableCheckReq")
	proto.RegisterType((*GetIsEnableCheckResp)(nil), "realnameauth.GetIsEnableCheckResp")
	proto.RegisterType((*AddAuthOperHistoryReq)(nil), "realnameauth.AddAuthOperHistoryReq")
	proto.RegisterType((*AddAuthOperHistoryResp)(nil), "realnameauth.AddAuthOperHistoryResp")
	proto.RegisterType((*AuthInfoReq)(nil), "realnameauth.AuthInfoReq")
	proto.RegisterType((*AuthInfoResp)(nil), "realnameauth.AuthInfoResp")
	proto.RegisterType((*GetAuthInfoReq)(nil), "realnameauth.GetAuthInfoReq")
	proto.RegisterType((*GetAuthInfoResp)(nil), "realnameauth.GetAuthInfoResp")
	proto.RegisterType((*GetUserRealNameAuthInfoV2Req)(nil), "realnameauth.GetUserRealNameAuthInfoV2Req")
	proto.RegisterType((*GetUserRealNameAuthInfoV2Resp)(nil), "realnameauth.GetUserRealNameAuthInfoV2Resp")
	proto.RegisterType((*GetUserIdentityExpireDateReq)(nil), "realnameauth.GetUserIdentityExpireDateReq")
	proto.RegisterType((*GetUserIdentityExpireDateResp)(nil), "realnameauth.GetUserIdentityExpireDateResp")
	proto.RegisterType((*GetUserIdentityInfoReq)(nil), "realnameauth.GetUserIdentityInfoReq")
	proto.RegisterType((*GetUserIdentityInfoResp)(nil), "realnameauth.GetUserIdentityInfoResp")
	proto.RegisterType((*UserIdentifyInfo)(nil), "realnameauth.UserIdentifyInfo")
	proto.RegisterType((*BatchGetUserIdentifyInfoReq)(nil), "realnameauth.BatchGetUserIdentifyInfoReq")
	proto.RegisterType((*BatchGetUserIdentifyInfoResp)(nil), "realnameauth.BatchGetUserIdentifyInfoResp")
	proto.RegisterType((*AddUserIdentityInfoReq)(nil), "realnameauth.AddUserIdentityInfoReq")
	proto.RegisterType((*AddUserIdentityInfoResp)(nil), "realnameauth.AddUserIdentityInfoResp")
	proto.RegisterType((*UpdateUserIdentityInfoReq)(nil), "realnameauth.UpdateUserIdentityInfoReq")
	proto.RegisterType((*UpdateUserIdentityInfoResp)(nil), "realnameauth.UpdateUserIdentityInfoResp")
	proto.RegisterType((*AddOrUpdateUserIdentityInfoReq)(nil), "realnameauth.AddOrUpdateUserIdentityInfoReq")
	proto.RegisterType((*AddOrUpdateUserIdentityInfoResp)(nil), "realnameauth.AddOrUpdateUserIdentityInfoResp")
	proto.RegisterType((*DelUserIdentityInfoReq)(nil), "realnameauth.DelUserIdentityInfoReq")
	proto.RegisterType((*DelUserIdentityInfoResp)(nil), "realnameauth.DelUserIdentityInfoResp")
	proto.RegisterType((*AddIdentityExpireUserPushTimeReq)(nil), "realnameauth.AddIdentityExpireUserPushTimeReq")
	proto.RegisterType((*AddIdentityExpireUserPushTimeResp)(nil), "realnameauth.AddIdentityExpireUserPushTimeResp")
	proto.RegisterType((*RecordUserAuthPhoneReq)(nil), "realnameauth.RecordUserAuthPhoneReq")
	proto.RegisterType((*RecordUserAuthPhoneResp)(nil), "realnameauth.RecordUserAuthPhoneResp")
	proto.RegisterType((*GetUserAuthPhoneReq)(nil), "realnameauth.GetUserAuthPhoneReq")
	proto.RegisterType((*GetUserAuthPhoneResp)(nil), "realnameauth.GetUserAuthPhoneResp")
	proto.RegisterType((*GetPhoneBindNumberReq)(nil), "realnameauth.GetPhoneBindNumberReq")
	proto.RegisterType((*GetPhoneBindNumberResp)(nil), "realnameauth.GetPhoneBindNumberResp")
	proto.RegisterType((*RecordUserIdentityInfoReq)(nil), "realnameauth.RecordUserIdentityInfoReq")
	proto.RegisterType((*RecordUserIdentityInfoResp)(nil), "realnameauth.RecordUserIdentityInfoResp")
	proto.RegisterType((*GetRealNameCurrVersionReq)(nil), "realnameauth.GetRealNameCurrVersionReq")
	proto.RegisterType((*GetRealNameCurrVersionResp)(nil), "realnameauth.GetRealNameCurrVersionResp")
	proto.RegisterType((*ParentGuardianSwitchReq)(nil), "realnameauth.ParentGuardianSwitchReq")
	proto.RegisterType((*ParentGuardianSwitchResp)(nil), "realnameauth.ParentGuardianSwitchResp")
	proto.RegisterType((*ParentGuardianCheckPasswordReq)(nil), "realnameauth.ParentGuardianCheckPasswordReq")
	proto.RegisterType((*ParentGuardianCheckPasswordResp)(nil), "realnameauth.ParentGuardianCheckPasswordResp")
	proto.RegisterType((*ParentGuardianStateReq)(nil), "realnameauth.ParentGuardianStateReq")
	proto.RegisterType((*ParentGuardianStateResp)(nil), "realnameauth.ParentGuardianStateResp")
	proto.RegisterType((*ParentGuardianInfo)(nil), "realnameauth.ParentGuardianInfo")
	proto.RegisterType((*BatchGetParentGuardianInfoReq)(nil), "realnameauth.BatchGetParentGuardianInfoReq")
	proto.RegisterType((*BatchGetParentGuardianInfoResp)(nil), "realnameauth.BatchGetParentGuardianInfoResp")
	proto.RegisterType((*CheckIdentityByFaceReq)(nil), "realnameauth.CheckIdentityByFaceReq")
	proto.RegisterType((*CheckIdentityByFaceResp)(nil), "realnameauth.CheckIdentityByFaceResp")
	proto.RegisterType((*CheckAppealCntIsOverLimitReq)(nil), "realnameauth.CheckAppealCntIsOverLimitReq")
	proto.RegisterType((*CheckAppealCntIsOverLimitResp)(nil), "realnameauth.CheckAppealCntIsOverLimitResp")
	proto.RegisterType((*ParentGuardianUpdatePwdReq)(nil), "realnameauth.ParentGuardianUpdatePwdReq")
	proto.RegisterType((*RealNameAuthInfo)(nil), "realnameauth.RealNameAuthInfo")
	proto.RegisterType((*GetRealNameAuthTokenReq)(nil), "realnameauth.GetRealNameAuthTokenReq")
	proto.RegisterType((*GetRealNameAuthTokenResp)(nil), "realnameauth.GetRealNameAuthTokenResp")
	proto.RegisterType((*GetRealNameFaceIdTokenByUidReq)(nil), "realnameauth.GetRealNameFaceIdTokenByUidReq")
	proto.RegisterType((*GetRealNameFaceIdTokenByUidResp)(nil), "realnameauth.GetRealNameFaceIdTokenByUidResp")
	proto.RegisterType((*ApplyRealNameAuthDataReq)(nil), "realnameauth.ApplyRealNameAuthDataReq")
	proto.RegisterType((*ApplyRealNameAuthDataResp)(nil), "realnameauth.ApplyRealNameAuthDataResp")
	proto.RegisterType((*RelieveRealNameAuthReq)(nil), "realnameauth.RelieveRealNameAuthReq")
	proto.RegisterType((*RelieveRealNameErrDetail)(nil), "realnameauth.RelieveRealNameErrDetail")
	proto.RegisterType((*RelieveRealNameAuthResp)(nil), "realnameauth.RelieveRealNameAuthResp")
	proto.RegisterType((*GetRealNameApplyInfoReq)(nil), "realnameauth.GetRealNameApplyInfoReq")
	proto.RegisterType((*GetRealNameApplyInfoResp)(nil), "realnameauth.GetRealNameApplyInfoResp")
	proto.RegisterType((*DelRealNameInfoReq)(nil), "realnameauth.DelRealNameInfoReq")
	proto.RegisterType((*DelRealNameInfoResp)(nil), "realnameauth.DelRealNameInfoResp")
	proto.RegisterType((*GetTheSameRealNameUserListReq)(nil), "realnameauth.GetTheSameRealNameUserListReq")
	proto.RegisterType((*GetTheSameRealNameUserListResp)(nil), "realnameauth.GetTheSameRealNameUserListResp")
	proto.RegisterType((*GetFaceAuthAliTokenReq)(nil), "realnameauth.GetFaceAuthAliTokenReq")
	proto.RegisterType((*GetFaceAuthAliTokenResp)(nil), "realnameauth.GetFaceAuthAliTokenResp")
	proto.RegisterType((*GetFaceAuthAliResultReq)(nil), "realnameauth.GetFaceAuthAliResultReq")
	proto.RegisterType((*GetFaceAuthAliResultResp)(nil), "realnameauth.GetFaceAuthAliResultResp")
	proto.RegisterType((*GetFaceAuthAliTokenByUidReq)(nil), "realnameauth.GetFaceAuthAliTokenByUidReq")
	proto.RegisterType((*GetFaceAuthAliTokenByUidResp)(nil), "realnameauth.GetFaceAuthAliTokenByUidResp")
	proto.RegisterType((*CheckFaceByAliTokenReq)(nil), "realnameauth.CheckFaceByAliTokenReq")
	proto.RegisterType((*CheckFaceByAliTokenResp)(nil), "realnameauth.CheckFaceByAliTokenResp")
	proto.RegisterType((*AuthByTwoElementReq)(nil), "realnameauth.AuthByTwoElementReq")
	proto.RegisterType((*AuthByTwoElementResp)(nil), "realnameauth.AuthByTwoElementResp")
	proto.RegisterType((*CheckIsInNameBlackListReq)(nil), "realnameauth.CheckIsInNameBlackListReq")
	proto.RegisterType((*CheckIsInNameBlackListResp)(nil), "realnameauth.CheckIsInNameBlackListResp")
	proto.RegisterType((*GetFaceRecognitionProviderReq)(nil), "realnameauth.GetFaceRecognitionProviderReq")
	proto.RegisterType((*GetFaceRecognitionProviderResp)(nil), "realnameauth.GetFaceRecognitionProviderResp")
	proto.RegisterType((*GetFaceRecognitionCertifyIdReq)(nil), "realnameauth.GetFaceRecognitionCertifyIdReq")
	proto.RegisterType((*FaceRecognitionCertifyData)(nil), "realnameauth.FaceRecognitionCertifyData")
	proto.RegisterType((*GetFaceRecognitionCertifyIdResp)(nil), "realnameauth.GetFaceRecognitionCertifyIdResp")
	proto.RegisterType((*GetFaceRecognitionResultReq)(nil), "realnameauth.GetFaceRecognitionResultReq")
	proto.RegisterType((*GetFaceRecognitionResult)(nil), "realnameauth.GetFaceRecognitionResult")
	proto.RegisterType((*GetFaceRecognitionResultResp)(nil), "realnameauth.GetFaceRecognitionResultResp")
	proto.RegisterEnum("realnameauth.EOperType", EOperType_name, EOperType_value)
	proto.RegisterEnum("realnameauth.EAuthPhoneType", EAuthPhoneType_name, EAuthPhoneType_value)
	proto.RegisterEnum("realnameauth.AuthType", AuthType_name, AuthType_value)
	proto.RegisterEnum("realnameauth.EAuthStatus", EAuthStatus_name, EAuthStatus_value)
	proto.RegisterEnum("realnameauth.EAuthType", EAuthType_name, EAuthType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for RealNameAuth service

type RealNameAuthClient interface {
	GetIsEnableCheck(ctx context.Context, in *GetIsEnableCheckReq, opts ...grpc.CallOption) (*GetIsEnableCheckResp, error)
	AddAuthOperHistory(ctx context.Context, in *AddAuthOperHistoryReq, opts ...grpc.CallOption) (*AddAuthOperHistoryResp, error)
	// 家长监护模式 add 2018/11/1/18:15 by T1035
	SwitchParentGuardian(ctx context.Context, in *ParentGuardianSwitchReq, opts ...grpc.CallOption) (*ParentGuardianSwitchResp, error)
	GetParentGuardianState(ctx context.Context, in *ParentGuardianStateReq, opts ...grpc.CallOption) (*ParentGuardianStateResp, error)
	AddUserRealNameAuthInfo(ctx context.Context, in *AuthInfoReq, opts ...grpc.CallOption) (*AuthInfoResp, error)
	GetUserRealNameAuthInfo(ctx context.Context, in *GetAuthInfoReq, opts ...grpc.CallOption) (*GetAuthInfoResp, error)
	ModifyUserRealNameAuthInfo(ctx context.Context, in *AuthInfoReq, opts ...grpc.CallOption) (*AuthInfoResp, error)
	GetUserIdentityInfo(ctx context.Context, in *GetUserIdentityInfoReq, opts ...grpc.CallOption) (*GetUserIdentityInfoResp, error)
	AddUserIdentityInfo(ctx context.Context, in *AddUserIdentityInfoReq, opts ...grpc.CallOption) (*AddUserIdentityInfoResp, error)
	UpdateUserIdentityInfo(ctx context.Context, in *UpdateUserIdentityInfoReq, opts ...grpc.CallOption) (*UpdateUserIdentityInfoResp, error)
	AddOrUpdateUserIdentityInfo(ctx context.Context, in *AddOrUpdateUserIdentityInfoReq, opts ...grpc.CallOption) (*AddOrUpdateUserIdentityInfoResp, error)
	DelUserIdentityInfo(ctx context.Context, in *DelUserIdentityInfoReq, opts ...grpc.CallOption) (*DelUserIdentityInfoResp, error)
	AddIdentityExpireUserPushTime(ctx context.Context, in *AddIdentityExpireUserPushTimeReq, opts ...grpc.CallOption) (*AddIdentityExpireUserPushTimeResp, error)
	GetUserIdentityExpireDate(ctx context.Context, in *GetUserIdentityExpireDateReq, opts ...grpc.CallOption) (*GetUserIdentityExpireDateResp, error)
	RecordUserAuthPhone(ctx context.Context, in *RecordUserAuthPhoneReq, opts ...grpc.CallOption) (*RecordUserAuthPhoneResp, error)
	GetUserAuthPhone(ctx context.Context, in *GetUserAuthPhoneReq, opts ...grpc.CallOption) (*GetUserAuthPhoneResp, error)
	GetPhoneBindNumber(ctx context.Context, in *GetPhoneBindNumberReq, opts ...grpc.CallOption) (*GetPhoneBindNumberResp, error)
	GetRealNameAuthToken(ctx context.Context, in *GetRealNameAuthTokenReq, opts ...grpc.CallOption) (*GetRealNameAuthTokenResp, error)
	ApplyRealNameAuthData(ctx context.Context, in *ApplyRealNameAuthDataReq, opts ...grpc.CallOption) (*ApplyRealNameAuthDataResp, error)
	RecordUserIdentityInfo(ctx context.Context, in *RecordUserIdentityInfoReq, opts ...grpc.CallOption) (*RecordUserIdentityInfoResp, error)
	GetRealNameApplyInfo(ctx context.Context, in *GetRealNameApplyInfoReq, opts ...grpc.CallOption) (*GetRealNameApplyInfoResp, error)
	GetRealNameCurrVersion(ctx context.Context, in *GetRealNameCurrVersionReq, opts ...grpc.CallOption) (*GetRealNameCurrVersionResp, error)
	DelRealNameInfo(ctx context.Context, in *DelRealNameInfoReq, opts ...grpc.CallOption) (*DelRealNameInfoResp, error)
	CheckIdentityByFace(ctx context.Context, in *CheckIdentityByFaceReq, opts ...grpc.CallOption) (*CheckIdentityByFaceResp, error)
	CheckAppealCntIsOverLimit(ctx context.Context, in *CheckAppealCntIsOverLimitReq, opts ...grpc.CallOption) (*CheckAppealCntIsOverLimitResp, error)
	GetRealNameFaceIdTokenByUid(ctx context.Context, in *GetRealNameFaceIdTokenByUidReq, opts ...grpc.CallOption) (*GetRealNameFaceIdTokenByUidResp, error)
	CheckParentGuardianPassword(ctx context.Context, in *ParentGuardianCheckPasswordReq, opts ...grpc.CallOption) (*ParentGuardianCheckPasswordResp, error)
	ParentGuardianUpdatePassword(ctx context.Context, in *ParentGuardianUpdatePwdReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	RelieveRealNameAuth(ctx context.Context, in *RelieveRealNameAuthReq, opts ...grpc.CallOption) (*RelieveRealNameAuthResp, error)
	BatchGetParentGuardianInfo(ctx context.Context, in *BatchGetParentGuardianInfoReq, opts ...grpc.CallOption) (*BatchGetParentGuardianInfoResp, error)
	BatchGetUserIdentifyInfo(ctx context.Context, in *BatchGetUserIdentifyInfoReq, opts ...grpc.CallOption) (*BatchGetUserIdentifyInfoResp, error)
	GetTheSameRealNameUserList(ctx context.Context, in *GetTheSameRealNameUserListReq, opts ...grpc.CallOption) (*GetTheSameRealNameUserListResp, error)
	GetFaceAuthAliToken(ctx context.Context, in *GetFaceAuthAliTokenReq, opts ...grpc.CallOption) (*GetFaceAuthAliTokenResp, error)
	GetFaceAuthAliResult(ctx context.Context, in *GetFaceAuthAliResultReq, opts ...grpc.CallOption) (*GetFaceAuthAliResultResp, error)
	GetFaceAuthAliTokenByUid(ctx context.Context, in *GetFaceAuthAliTokenByUidReq, opts ...grpc.CallOption) (*GetFaceAuthAliTokenByUidResp, error)
	CheckFaceByAliToken(ctx context.Context, in *CheckFaceByAliTokenReq, opts ...grpc.CallOption) (*CheckFaceByAliTokenResp, error)
	AuthByTwoElement(ctx context.Context, in *AuthByTwoElementReq, opts ...grpc.CallOption) (*AuthByTwoElementResp, error)
	GetUserRealNameAuthInfoV2(ctx context.Context, in *GetUserRealNameAuthInfoV2Req, opts ...grpc.CallOption) (*GetUserRealNameAuthInfoV2Resp, error)
	CheckIsInNameBlackList(ctx context.Context, in *CheckIsInNameBlackListReq, opts ...grpc.CallOption) (*CheckIsInNameBlackListResp, error)
	GetFaceRecognitionProvider(ctx context.Context, in *GetFaceRecognitionProviderReq, opts ...grpc.CallOption) (*GetFaceRecognitionProviderResp, error)
	GetFaceRecognitionCertifyId(ctx context.Context, in *GetFaceRecognitionCertifyIdReq, opts ...grpc.CallOption) (*GetFaceRecognitionCertifyIdResp, error)
	GetFaceRecognitionResult(ctx context.Context, in *GetFaceRecognitionResultReq, opts ...grpc.CallOption) (*GetFaceRecognitionResultResp, error)
}

type realNameAuthClient struct {
	cc *grpc.ClientConn
}

func NewRealNameAuthClient(cc *grpc.ClientConn) RealNameAuthClient {
	return &realNameAuthClient{cc}
}

func (c *realNameAuthClient) GetIsEnableCheck(ctx context.Context, in *GetIsEnableCheckReq, opts ...grpc.CallOption) (*GetIsEnableCheckResp, error) {
	out := new(GetIsEnableCheckResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/GetIsEnableCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) AddAuthOperHistory(ctx context.Context, in *AddAuthOperHistoryReq, opts ...grpc.CallOption) (*AddAuthOperHistoryResp, error) {
	out := new(AddAuthOperHistoryResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/AddAuthOperHistory", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) SwitchParentGuardian(ctx context.Context, in *ParentGuardianSwitchReq, opts ...grpc.CallOption) (*ParentGuardianSwitchResp, error) {
	out := new(ParentGuardianSwitchResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/SwitchParentGuardian", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) GetParentGuardianState(ctx context.Context, in *ParentGuardianStateReq, opts ...grpc.CallOption) (*ParentGuardianStateResp, error) {
	out := new(ParentGuardianStateResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/GetParentGuardianState", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) AddUserRealNameAuthInfo(ctx context.Context, in *AuthInfoReq, opts ...grpc.CallOption) (*AuthInfoResp, error) {
	out := new(AuthInfoResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/AddUserRealNameAuthInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) GetUserRealNameAuthInfo(ctx context.Context, in *GetAuthInfoReq, opts ...grpc.CallOption) (*GetAuthInfoResp, error) {
	out := new(GetAuthInfoResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/GetUserRealNameAuthInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) ModifyUserRealNameAuthInfo(ctx context.Context, in *AuthInfoReq, opts ...grpc.CallOption) (*AuthInfoResp, error) {
	out := new(AuthInfoResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/ModifyUserRealNameAuthInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) GetUserIdentityInfo(ctx context.Context, in *GetUserIdentityInfoReq, opts ...grpc.CallOption) (*GetUserIdentityInfoResp, error) {
	out := new(GetUserIdentityInfoResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/GetUserIdentityInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) AddUserIdentityInfo(ctx context.Context, in *AddUserIdentityInfoReq, opts ...grpc.CallOption) (*AddUserIdentityInfoResp, error) {
	out := new(AddUserIdentityInfoResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/AddUserIdentityInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) UpdateUserIdentityInfo(ctx context.Context, in *UpdateUserIdentityInfoReq, opts ...grpc.CallOption) (*UpdateUserIdentityInfoResp, error) {
	out := new(UpdateUserIdentityInfoResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/UpdateUserIdentityInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) AddOrUpdateUserIdentityInfo(ctx context.Context, in *AddOrUpdateUserIdentityInfoReq, opts ...grpc.CallOption) (*AddOrUpdateUserIdentityInfoResp, error) {
	out := new(AddOrUpdateUserIdentityInfoResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/AddOrUpdateUserIdentityInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) DelUserIdentityInfo(ctx context.Context, in *DelUserIdentityInfoReq, opts ...grpc.CallOption) (*DelUserIdentityInfoResp, error) {
	out := new(DelUserIdentityInfoResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/DelUserIdentityInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) AddIdentityExpireUserPushTime(ctx context.Context, in *AddIdentityExpireUserPushTimeReq, opts ...grpc.CallOption) (*AddIdentityExpireUserPushTimeResp, error) {
	out := new(AddIdentityExpireUserPushTimeResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/AddIdentityExpireUserPushTime", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) GetUserIdentityExpireDate(ctx context.Context, in *GetUserIdentityExpireDateReq, opts ...grpc.CallOption) (*GetUserIdentityExpireDateResp, error) {
	out := new(GetUserIdentityExpireDateResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/GetUserIdentityExpireDate", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) RecordUserAuthPhone(ctx context.Context, in *RecordUserAuthPhoneReq, opts ...grpc.CallOption) (*RecordUserAuthPhoneResp, error) {
	out := new(RecordUserAuthPhoneResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/RecordUserAuthPhone", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) GetUserAuthPhone(ctx context.Context, in *GetUserAuthPhoneReq, opts ...grpc.CallOption) (*GetUserAuthPhoneResp, error) {
	out := new(GetUserAuthPhoneResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/GetUserAuthPhone", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) GetPhoneBindNumber(ctx context.Context, in *GetPhoneBindNumberReq, opts ...grpc.CallOption) (*GetPhoneBindNumberResp, error) {
	out := new(GetPhoneBindNumberResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/GetPhoneBindNumber", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) GetRealNameAuthToken(ctx context.Context, in *GetRealNameAuthTokenReq, opts ...grpc.CallOption) (*GetRealNameAuthTokenResp, error) {
	out := new(GetRealNameAuthTokenResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/GetRealNameAuthToken", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) ApplyRealNameAuthData(ctx context.Context, in *ApplyRealNameAuthDataReq, opts ...grpc.CallOption) (*ApplyRealNameAuthDataResp, error) {
	out := new(ApplyRealNameAuthDataResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/ApplyRealNameAuthData", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) RecordUserIdentityInfo(ctx context.Context, in *RecordUserIdentityInfoReq, opts ...grpc.CallOption) (*RecordUserIdentityInfoResp, error) {
	out := new(RecordUserIdentityInfoResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/RecordUserIdentityInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) GetRealNameApplyInfo(ctx context.Context, in *GetRealNameApplyInfoReq, opts ...grpc.CallOption) (*GetRealNameApplyInfoResp, error) {
	out := new(GetRealNameApplyInfoResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/GetRealNameApplyInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) GetRealNameCurrVersion(ctx context.Context, in *GetRealNameCurrVersionReq, opts ...grpc.CallOption) (*GetRealNameCurrVersionResp, error) {
	out := new(GetRealNameCurrVersionResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/GetRealNameCurrVersion", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) DelRealNameInfo(ctx context.Context, in *DelRealNameInfoReq, opts ...grpc.CallOption) (*DelRealNameInfoResp, error) {
	out := new(DelRealNameInfoResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/DelRealNameInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) CheckIdentityByFace(ctx context.Context, in *CheckIdentityByFaceReq, opts ...grpc.CallOption) (*CheckIdentityByFaceResp, error) {
	out := new(CheckIdentityByFaceResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/CheckIdentityByFace", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) CheckAppealCntIsOverLimit(ctx context.Context, in *CheckAppealCntIsOverLimitReq, opts ...grpc.CallOption) (*CheckAppealCntIsOverLimitResp, error) {
	out := new(CheckAppealCntIsOverLimitResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/CheckAppealCntIsOverLimit", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) GetRealNameFaceIdTokenByUid(ctx context.Context, in *GetRealNameFaceIdTokenByUidReq, opts ...grpc.CallOption) (*GetRealNameFaceIdTokenByUidResp, error) {
	out := new(GetRealNameFaceIdTokenByUidResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/GetRealNameFaceIdTokenByUid", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) CheckParentGuardianPassword(ctx context.Context, in *ParentGuardianCheckPasswordReq, opts ...grpc.CallOption) (*ParentGuardianCheckPasswordResp, error) {
	out := new(ParentGuardianCheckPasswordResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/CheckParentGuardianPassword", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) ParentGuardianUpdatePassword(ctx context.Context, in *ParentGuardianUpdatePwdReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/ParentGuardianUpdatePassword", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) RelieveRealNameAuth(ctx context.Context, in *RelieveRealNameAuthReq, opts ...grpc.CallOption) (*RelieveRealNameAuthResp, error) {
	out := new(RelieveRealNameAuthResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/RelieveRealNameAuth", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) BatchGetParentGuardianInfo(ctx context.Context, in *BatchGetParentGuardianInfoReq, opts ...grpc.CallOption) (*BatchGetParentGuardianInfoResp, error) {
	out := new(BatchGetParentGuardianInfoResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/BatchGetParentGuardianInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) BatchGetUserIdentifyInfo(ctx context.Context, in *BatchGetUserIdentifyInfoReq, opts ...grpc.CallOption) (*BatchGetUserIdentifyInfoResp, error) {
	out := new(BatchGetUserIdentifyInfoResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/BatchGetUserIdentifyInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) GetTheSameRealNameUserList(ctx context.Context, in *GetTheSameRealNameUserListReq, opts ...grpc.CallOption) (*GetTheSameRealNameUserListResp, error) {
	out := new(GetTheSameRealNameUserListResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/GetTheSameRealNameUserList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) GetFaceAuthAliToken(ctx context.Context, in *GetFaceAuthAliTokenReq, opts ...grpc.CallOption) (*GetFaceAuthAliTokenResp, error) {
	out := new(GetFaceAuthAliTokenResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/GetFaceAuthAliToken", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) GetFaceAuthAliResult(ctx context.Context, in *GetFaceAuthAliResultReq, opts ...grpc.CallOption) (*GetFaceAuthAliResultResp, error) {
	out := new(GetFaceAuthAliResultResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/GetFaceAuthAliResult", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) GetFaceAuthAliTokenByUid(ctx context.Context, in *GetFaceAuthAliTokenByUidReq, opts ...grpc.CallOption) (*GetFaceAuthAliTokenByUidResp, error) {
	out := new(GetFaceAuthAliTokenByUidResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/GetFaceAuthAliTokenByUid", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) CheckFaceByAliToken(ctx context.Context, in *CheckFaceByAliTokenReq, opts ...grpc.CallOption) (*CheckFaceByAliTokenResp, error) {
	out := new(CheckFaceByAliTokenResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/CheckFaceByAliToken", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) AuthByTwoElement(ctx context.Context, in *AuthByTwoElementReq, opts ...grpc.CallOption) (*AuthByTwoElementResp, error) {
	out := new(AuthByTwoElementResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/AuthByTwoElement", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) GetUserRealNameAuthInfoV2(ctx context.Context, in *GetUserRealNameAuthInfoV2Req, opts ...grpc.CallOption) (*GetUserRealNameAuthInfoV2Resp, error) {
	out := new(GetUserRealNameAuthInfoV2Resp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/GetUserRealNameAuthInfoV2", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) CheckIsInNameBlackList(ctx context.Context, in *CheckIsInNameBlackListReq, opts ...grpc.CallOption) (*CheckIsInNameBlackListResp, error) {
	out := new(CheckIsInNameBlackListResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/CheckIsInNameBlackList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) GetFaceRecognitionProvider(ctx context.Context, in *GetFaceRecognitionProviderReq, opts ...grpc.CallOption) (*GetFaceRecognitionProviderResp, error) {
	out := new(GetFaceRecognitionProviderResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/GetFaceRecognitionProvider", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) GetFaceRecognitionCertifyId(ctx context.Context, in *GetFaceRecognitionCertifyIdReq, opts ...grpc.CallOption) (*GetFaceRecognitionCertifyIdResp, error) {
	out := new(GetFaceRecognitionCertifyIdResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/GetFaceRecognitionCertifyId", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthClient) GetFaceRecognitionResult(ctx context.Context, in *GetFaceRecognitionResultReq, opts ...grpc.CallOption) (*GetFaceRecognitionResultResp, error) {
	out := new(GetFaceRecognitionResultResp)
	err := grpc.Invoke(ctx, "/realnameauth.RealNameAuth/GetFaceRecognitionResult", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for RealNameAuth service

type RealNameAuthServer interface {
	GetIsEnableCheck(context.Context, *GetIsEnableCheckReq) (*GetIsEnableCheckResp, error)
	AddAuthOperHistory(context.Context, *AddAuthOperHistoryReq) (*AddAuthOperHistoryResp, error)
	// 家长监护模式 add 2018/11/1/18:15 by T1035
	SwitchParentGuardian(context.Context, *ParentGuardianSwitchReq) (*ParentGuardianSwitchResp, error)
	GetParentGuardianState(context.Context, *ParentGuardianStateReq) (*ParentGuardianStateResp, error)
	AddUserRealNameAuthInfo(context.Context, *AuthInfoReq) (*AuthInfoResp, error)
	GetUserRealNameAuthInfo(context.Context, *GetAuthInfoReq) (*GetAuthInfoResp, error)
	ModifyUserRealNameAuthInfo(context.Context, *AuthInfoReq) (*AuthInfoResp, error)
	GetUserIdentityInfo(context.Context, *GetUserIdentityInfoReq) (*GetUserIdentityInfoResp, error)
	AddUserIdentityInfo(context.Context, *AddUserIdentityInfoReq) (*AddUserIdentityInfoResp, error)
	UpdateUserIdentityInfo(context.Context, *UpdateUserIdentityInfoReq) (*UpdateUserIdentityInfoResp, error)
	AddOrUpdateUserIdentityInfo(context.Context, *AddOrUpdateUserIdentityInfoReq) (*AddOrUpdateUserIdentityInfoResp, error)
	DelUserIdentityInfo(context.Context, *DelUserIdentityInfoReq) (*DelUserIdentityInfoResp, error)
	AddIdentityExpireUserPushTime(context.Context, *AddIdentityExpireUserPushTimeReq) (*AddIdentityExpireUserPushTimeResp, error)
	GetUserIdentityExpireDate(context.Context, *GetUserIdentityExpireDateReq) (*GetUserIdentityExpireDateResp, error)
	RecordUserAuthPhone(context.Context, *RecordUserAuthPhoneReq) (*RecordUserAuthPhoneResp, error)
	GetUserAuthPhone(context.Context, *GetUserAuthPhoneReq) (*GetUserAuthPhoneResp, error)
	GetPhoneBindNumber(context.Context, *GetPhoneBindNumberReq) (*GetPhoneBindNumberResp, error)
	GetRealNameAuthToken(context.Context, *GetRealNameAuthTokenReq) (*GetRealNameAuthTokenResp, error)
	ApplyRealNameAuthData(context.Context, *ApplyRealNameAuthDataReq) (*ApplyRealNameAuthDataResp, error)
	RecordUserIdentityInfo(context.Context, *RecordUserIdentityInfoReq) (*RecordUserIdentityInfoResp, error)
	GetRealNameApplyInfo(context.Context, *GetRealNameApplyInfoReq) (*GetRealNameApplyInfoResp, error)
	GetRealNameCurrVersion(context.Context, *GetRealNameCurrVersionReq) (*GetRealNameCurrVersionResp, error)
	DelRealNameInfo(context.Context, *DelRealNameInfoReq) (*DelRealNameInfoResp, error)
	CheckIdentityByFace(context.Context, *CheckIdentityByFaceReq) (*CheckIdentityByFaceResp, error)
	CheckAppealCntIsOverLimit(context.Context, *CheckAppealCntIsOverLimitReq) (*CheckAppealCntIsOverLimitResp, error)
	GetRealNameFaceIdTokenByUid(context.Context, *GetRealNameFaceIdTokenByUidReq) (*GetRealNameFaceIdTokenByUidResp, error)
	CheckParentGuardianPassword(context.Context, *ParentGuardianCheckPasswordReq) (*ParentGuardianCheckPasswordResp, error)
	ParentGuardianUpdatePassword(context.Context, *ParentGuardianUpdatePwdReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	RelieveRealNameAuth(context.Context, *RelieveRealNameAuthReq) (*RelieveRealNameAuthResp, error)
	BatchGetParentGuardianInfo(context.Context, *BatchGetParentGuardianInfoReq) (*BatchGetParentGuardianInfoResp, error)
	BatchGetUserIdentifyInfo(context.Context, *BatchGetUserIdentifyInfoReq) (*BatchGetUserIdentifyInfoResp, error)
	GetTheSameRealNameUserList(context.Context, *GetTheSameRealNameUserListReq) (*GetTheSameRealNameUserListResp, error)
	GetFaceAuthAliToken(context.Context, *GetFaceAuthAliTokenReq) (*GetFaceAuthAliTokenResp, error)
	GetFaceAuthAliResult(context.Context, *GetFaceAuthAliResultReq) (*GetFaceAuthAliResultResp, error)
	GetFaceAuthAliTokenByUid(context.Context, *GetFaceAuthAliTokenByUidReq) (*GetFaceAuthAliTokenByUidResp, error)
	CheckFaceByAliToken(context.Context, *CheckFaceByAliTokenReq) (*CheckFaceByAliTokenResp, error)
	AuthByTwoElement(context.Context, *AuthByTwoElementReq) (*AuthByTwoElementResp, error)
	GetUserRealNameAuthInfoV2(context.Context, *GetUserRealNameAuthInfoV2Req) (*GetUserRealNameAuthInfoV2Resp, error)
	CheckIsInNameBlackList(context.Context, *CheckIsInNameBlackListReq) (*CheckIsInNameBlackListResp, error)
	GetFaceRecognitionProvider(context.Context, *GetFaceRecognitionProviderReq) (*GetFaceRecognitionProviderResp, error)
	GetFaceRecognitionCertifyId(context.Context, *GetFaceRecognitionCertifyIdReq) (*GetFaceRecognitionCertifyIdResp, error)
	GetFaceRecognitionResult(context.Context, *GetFaceRecognitionResultReq) (*GetFaceRecognitionResultResp, error)
}

func RegisterRealNameAuthServer(s *grpc.Server, srv RealNameAuthServer) {
	s.RegisterService(&_RealNameAuth_serviceDesc, srv)
}

func _RealNameAuth_GetIsEnableCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIsEnableCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).GetIsEnableCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/GetIsEnableCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).GetIsEnableCheck(ctx, req.(*GetIsEnableCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_AddAuthOperHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAuthOperHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).AddAuthOperHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/AddAuthOperHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).AddAuthOperHistory(ctx, req.(*AddAuthOperHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_SwitchParentGuardian_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ParentGuardianSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).SwitchParentGuardian(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/SwitchParentGuardian",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).SwitchParentGuardian(ctx, req.(*ParentGuardianSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_GetParentGuardianState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ParentGuardianStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).GetParentGuardianState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/GetParentGuardianState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).GetParentGuardianState(ctx, req.(*ParentGuardianStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_AddUserRealNameAuthInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).AddUserRealNameAuthInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/AddUserRealNameAuthInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).AddUserRealNameAuthInfo(ctx, req.(*AuthInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_GetUserRealNameAuthInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).GetUserRealNameAuthInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/GetUserRealNameAuthInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).GetUserRealNameAuthInfo(ctx, req.(*GetAuthInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_ModifyUserRealNameAuthInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).ModifyUserRealNameAuthInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/ModifyUserRealNameAuthInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).ModifyUserRealNameAuthInfo(ctx, req.(*AuthInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_GetUserIdentityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserIdentityInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).GetUserIdentityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/GetUserIdentityInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).GetUserIdentityInfo(ctx, req.(*GetUserIdentityInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_AddUserIdentityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserIdentityInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).AddUserIdentityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/AddUserIdentityInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).AddUserIdentityInfo(ctx, req.(*AddUserIdentityInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_UpdateUserIdentityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserIdentityInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).UpdateUserIdentityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/UpdateUserIdentityInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).UpdateUserIdentityInfo(ctx, req.(*UpdateUserIdentityInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_AddOrUpdateUserIdentityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddOrUpdateUserIdentityInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).AddOrUpdateUserIdentityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/AddOrUpdateUserIdentityInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).AddOrUpdateUserIdentityInfo(ctx, req.(*AddOrUpdateUserIdentityInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_DelUserIdentityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelUserIdentityInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).DelUserIdentityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/DelUserIdentityInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).DelUserIdentityInfo(ctx, req.(*DelUserIdentityInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_AddIdentityExpireUserPushTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddIdentityExpireUserPushTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).AddIdentityExpireUserPushTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/AddIdentityExpireUserPushTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).AddIdentityExpireUserPushTime(ctx, req.(*AddIdentityExpireUserPushTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_GetUserIdentityExpireDate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserIdentityExpireDateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).GetUserIdentityExpireDate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/GetUserIdentityExpireDate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).GetUserIdentityExpireDate(ctx, req.(*GetUserIdentityExpireDateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_RecordUserAuthPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordUserAuthPhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).RecordUserAuthPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/RecordUserAuthPhone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).RecordUserAuthPhone(ctx, req.(*RecordUserAuthPhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_GetUserAuthPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAuthPhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).GetUserAuthPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/GetUserAuthPhone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).GetUserAuthPhone(ctx, req.(*GetUserAuthPhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_GetPhoneBindNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPhoneBindNumberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).GetPhoneBindNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/GetPhoneBindNumber",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).GetPhoneBindNumber(ctx, req.(*GetPhoneBindNumberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_GetRealNameAuthToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRealNameAuthTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).GetRealNameAuthToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/GetRealNameAuthToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).GetRealNameAuthToken(ctx, req.(*GetRealNameAuthTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_ApplyRealNameAuthData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyRealNameAuthDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).ApplyRealNameAuthData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/ApplyRealNameAuthData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).ApplyRealNameAuthData(ctx, req.(*ApplyRealNameAuthDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_RecordUserIdentityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordUserIdentityInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).RecordUserIdentityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/RecordUserIdentityInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).RecordUserIdentityInfo(ctx, req.(*RecordUserIdentityInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_GetRealNameApplyInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRealNameApplyInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).GetRealNameApplyInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/GetRealNameApplyInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).GetRealNameApplyInfo(ctx, req.(*GetRealNameApplyInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_GetRealNameCurrVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRealNameCurrVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).GetRealNameCurrVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/GetRealNameCurrVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).GetRealNameCurrVersion(ctx, req.(*GetRealNameCurrVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_DelRealNameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelRealNameInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).DelRealNameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/DelRealNameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).DelRealNameInfo(ctx, req.(*DelRealNameInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_CheckIdentityByFace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckIdentityByFaceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).CheckIdentityByFace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/CheckIdentityByFace",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).CheckIdentityByFace(ctx, req.(*CheckIdentityByFaceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_CheckAppealCntIsOverLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckAppealCntIsOverLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).CheckAppealCntIsOverLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/CheckAppealCntIsOverLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).CheckAppealCntIsOverLimit(ctx, req.(*CheckAppealCntIsOverLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_GetRealNameFaceIdTokenByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRealNameFaceIdTokenByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).GetRealNameFaceIdTokenByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/GetRealNameFaceIdTokenByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).GetRealNameFaceIdTokenByUid(ctx, req.(*GetRealNameFaceIdTokenByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_CheckParentGuardianPassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ParentGuardianCheckPasswordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).CheckParentGuardianPassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/CheckParentGuardianPassword",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).CheckParentGuardianPassword(ctx, req.(*ParentGuardianCheckPasswordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_ParentGuardianUpdatePassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ParentGuardianUpdatePwdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).ParentGuardianUpdatePassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/ParentGuardianUpdatePassword",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).ParentGuardianUpdatePassword(ctx, req.(*ParentGuardianUpdatePwdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_RelieveRealNameAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RelieveRealNameAuthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).RelieveRealNameAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/RelieveRealNameAuth",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).RelieveRealNameAuth(ctx, req.(*RelieveRealNameAuthReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_BatchGetParentGuardianInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetParentGuardianInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).BatchGetParentGuardianInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/BatchGetParentGuardianInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).BatchGetParentGuardianInfo(ctx, req.(*BatchGetParentGuardianInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_BatchGetUserIdentifyInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserIdentifyInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).BatchGetUserIdentifyInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/BatchGetUserIdentifyInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).BatchGetUserIdentifyInfo(ctx, req.(*BatchGetUserIdentifyInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_GetTheSameRealNameUserList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTheSameRealNameUserListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).GetTheSameRealNameUserList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/GetTheSameRealNameUserList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).GetTheSameRealNameUserList(ctx, req.(*GetTheSameRealNameUserListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_GetFaceAuthAliToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFaceAuthAliTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).GetFaceAuthAliToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/GetFaceAuthAliToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).GetFaceAuthAliToken(ctx, req.(*GetFaceAuthAliTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_GetFaceAuthAliResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFaceAuthAliResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).GetFaceAuthAliResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/GetFaceAuthAliResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).GetFaceAuthAliResult(ctx, req.(*GetFaceAuthAliResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_GetFaceAuthAliTokenByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFaceAuthAliTokenByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).GetFaceAuthAliTokenByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/GetFaceAuthAliTokenByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).GetFaceAuthAliTokenByUid(ctx, req.(*GetFaceAuthAliTokenByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_CheckFaceByAliToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckFaceByAliTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).CheckFaceByAliToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/CheckFaceByAliToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).CheckFaceByAliToken(ctx, req.(*CheckFaceByAliTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_AuthByTwoElement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthByTwoElementReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).AuthByTwoElement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/AuthByTwoElement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).AuthByTwoElement(ctx, req.(*AuthByTwoElementReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_GetUserRealNameAuthInfoV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRealNameAuthInfoV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).GetUserRealNameAuthInfoV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/GetUserRealNameAuthInfoV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).GetUserRealNameAuthInfoV2(ctx, req.(*GetUserRealNameAuthInfoV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_CheckIsInNameBlackList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckIsInNameBlackListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).CheckIsInNameBlackList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/CheckIsInNameBlackList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).CheckIsInNameBlackList(ctx, req.(*CheckIsInNameBlackListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_GetFaceRecognitionProvider_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFaceRecognitionProviderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).GetFaceRecognitionProvider(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/GetFaceRecognitionProvider",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).GetFaceRecognitionProvider(ctx, req.(*GetFaceRecognitionProviderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_GetFaceRecognitionCertifyId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFaceRecognitionCertifyIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).GetFaceRecognitionCertifyId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/GetFaceRecognitionCertifyId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).GetFaceRecognitionCertifyId(ctx, req.(*GetFaceRecognitionCertifyIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuth_GetFaceRecognitionResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFaceRecognitionResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthServer).GetFaceRecognitionResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth.RealNameAuth/GetFaceRecognitionResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthServer).GetFaceRecognitionResult(ctx, req.(*GetFaceRecognitionResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RealNameAuth_serviceDesc = grpc.ServiceDesc{
	ServiceName: "realnameauth.RealNameAuth",
	HandlerType: (*RealNameAuthServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetIsEnableCheck",
			Handler:    _RealNameAuth_GetIsEnableCheck_Handler,
		},
		{
			MethodName: "AddAuthOperHistory",
			Handler:    _RealNameAuth_AddAuthOperHistory_Handler,
		},
		{
			MethodName: "SwitchParentGuardian",
			Handler:    _RealNameAuth_SwitchParentGuardian_Handler,
		},
		{
			MethodName: "GetParentGuardianState",
			Handler:    _RealNameAuth_GetParentGuardianState_Handler,
		},
		{
			MethodName: "AddUserRealNameAuthInfo",
			Handler:    _RealNameAuth_AddUserRealNameAuthInfo_Handler,
		},
		{
			MethodName: "GetUserRealNameAuthInfo",
			Handler:    _RealNameAuth_GetUserRealNameAuthInfo_Handler,
		},
		{
			MethodName: "ModifyUserRealNameAuthInfo",
			Handler:    _RealNameAuth_ModifyUserRealNameAuthInfo_Handler,
		},
		{
			MethodName: "GetUserIdentityInfo",
			Handler:    _RealNameAuth_GetUserIdentityInfo_Handler,
		},
		{
			MethodName: "AddUserIdentityInfo",
			Handler:    _RealNameAuth_AddUserIdentityInfo_Handler,
		},
		{
			MethodName: "UpdateUserIdentityInfo",
			Handler:    _RealNameAuth_UpdateUserIdentityInfo_Handler,
		},
		{
			MethodName: "AddOrUpdateUserIdentityInfo",
			Handler:    _RealNameAuth_AddOrUpdateUserIdentityInfo_Handler,
		},
		{
			MethodName: "DelUserIdentityInfo",
			Handler:    _RealNameAuth_DelUserIdentityInfo_Handler,
		},
		{
			MethodName: "AddIdentityExpireUserPushTime",
			Handler:    _RealNameAuth_AddIdentityExpireUserPushTime_Handler,
		},
		{
			MethodName: "GetUserIdentityExpireDate",
			Handler:    _RealNameAuth_GetUserIdentityExpireDate_Handler,
		},
		{
			MethodName: "RecordUserAuthPhone",
			Handler:    _RealNameAuth_RecordUserAuthPhone_Handler,
		},
		{
			MethodName: "GetUserAuthPhone",
			Handler:    _RealNameAuth_GetUserAuthPhone_Handler,
		},
		{
			MethodName: "GetPhoneBindNumber",
			Handler:    _RealNameAuth_GetPhoneBindNumber_Handler,
		},
		{
			MethodName: "GetRealNameAuthToken",
			Handler:    _RealNameAuth_GetRealNameAuthToken_Handler,
		},
		{
			MethodName: "ApplyRealNameAuthData",
			Handler:    _RealNameAuth_ApplyRealNameAuthData_Handler,
		},
		{
			MethodName: "RecordUserIdentityInfo",
			Handler:    _RealNameAuth_RecordUserIdentityInfo_Handler,
		},
		{
			MethodName: "GetRealNameApplyInfo",
			Handler:    _RealNameAuth_GetRealNameApplyInfo_Handler,
		},
		{
			MethodName: "GetRealNameCurrVersion",
			Handler:    _RealNameAuth_GetRealNameCurrVersion_Handler,
		},
		{
			MethodName: "DelRealNameInfo",
			Handler:    _RealNameAuth_DelRealNameInfo_Handler,
		},
		{
			MethodName: "CheckIdentityByFace",
			Handler:    _RealNameAuth_CheckIdentityByFace_Handler,
		},
		{
			MethodName: "CheckAppealCntIsOverLimit",
			Handler:    _RealNameAuth_CheckAppealCntIsOverLimit_Handler,
		},
		{
			MethodName: "GetRealNameFaceIdTokenByUid",
			Handler:    _RealNameAuth_GetRealNameFaceIdTokenByUid_Handler,
		},
		{
			MethodName: "CheckParentGuardianPassword",
			Handler:    _RealNameAuth_CheckParentGuardianPassword_Handler,
		},
		{
			MethodName: "ParentGuardianUpdatePassword",
			Handler:    _RealNameAuth_ParentGuardianUpdatePassword_Handler,
		},
		{
			MethodName: "RelieveRealNameAuth",
			Handler:    _RealNameAuth_RelieveRealNameAuth_Handler,
		},
		{
			MethodName: "BatchGetParentGuardianInfo",
			Handler:    _RealNameAuth_BatchGetParentGuardianInfo_Handler,
		},
		{
			MethodName: "BatchGetUserIdentifyInfo",
			Handler:    _RealNameAuth_BatchGetUserIdentifyInfo_Handler,
		},
		{
			MethodName: "GetTheSameRealNameUserList",
			Handler:    _RealNameAuth_GetTheSameRealNameUserList_Handler,
		},
		{
			MethodName: "GetFaceAuthAliToken",
			Handler:    _RealNameAuth_GetFaceAuthAliToken_Handler,
		},
		{
			MethodName: "GetFaceAuthAliResult",
			Handler:    _RealNameAuth_GetFaceAuthAliResult_Handler,
		},
		{
			MethodName: "GetFaceAuthAliTokenByUid",
			Handler:    _RealNameAuth_GetFaceAuthAliTokenByUid_Handler,
		},
		{
			MethodName: "CheckFaceByAliToken",
			Handler:    _RealNameAuth_CheckFaceByAliToken_Handler,
		},
		{
			MethodName: "AuthByTwoElement",
			Handler:    _RealNameAuth_AuthByTwoElement_Handler,
		},
		{
			MethodName: "GetUserRealNameAuthInfoV2",
			Handler:    _RealNameAuth_GetUserRealNameAuthInfoV2_Handler,
		},
		{
			MethodName: "CheckIsInNameBlackList",
			Handler:    _RealNameAuth_CheckIsInNameBlackList_Handler,
		},
		{
			MethodName: "GetFaceRecognitionProvider",
			Handler:    _RealNameAuth_GetFaceRecognitionProvider_Handler,
		},
		{
			MethodName: "GetFaceRecognitionCertifyId",
			Handler:    _RealNameAuth_GetFaceRecognitionCertifyId_Handler,
		},
		{
			MethodName: "GetFaceRecognitionResult",
			Handler:    _RealNameAuth_GetFaceRecognitionResult_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/realNameAuthSvr/realnameauth.proto",
}

func (m *AuthTypeInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AuthTypeInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.AuthType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.AuthStauts))
	return i, nil
}

func (m *AuthIdCardInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AuthIdCardInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityNum)))
	i += copy(dAtA[i:], m.IdentityNum)
	return i, nil
}

func (m *AuthInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AuthInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Birthday)))
	i += copy(dAtA[i:], m.Birthday)
	dAtA[i] = 0x20
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Level))
	return i, nil
}

func (m *GetIsEnableCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIsEnableCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetIsEnableCheckResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIsEnableCheckResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsEnableCheck {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *AddAuthOperHistoryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddAuthOperHistoryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.OperType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.AuthType))
	dAtA[i] = 0x22
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.LogInfo)))
	i += copy(dAtA[i:], m.LogInfo)
	return i, nil
}

func (m *AddAuthOperHistoryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddAuthOperHistoryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *AuthInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AuthInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *AuthInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AuthInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetAuthInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAuthInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetAuthInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAuthInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x10
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Status))
	if m.RealnameInfo != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintRealnameauth(dAtA, i, uint64(m.RealnameInfo.Size()))
		n1, err := m.RealnameInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *GetUserRealNameAuthInfoV2Req) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserRealNameAuthInfoV2Req) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	if m.IsNeedAuthPhone {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	if m.IsNeedIdcardInfo {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetUserRealNameAuthInfoV2Resp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserRealNameAuthInfoV2Resp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AuthList) > 0 {
		for _, msg := range m.AuthList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintRealnameauth(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.IdcardInfo != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRealnameauth(dAtA, i, uint64(m.IdcardInfo.Size()))
		n2, err := m.IdcardInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.AuthPhone)))
	i += copy(dAtA[i:], m.AuthPhone)
	dAtA[i] = 0x20
	i++
	if m.IsAdult {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Age))
	dAtA[i] = 0x30
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.RealnameVersion))
	return i, nil
}

func (m *GetUserIdentityExpireDateReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserIdentityExpireDateReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetUserIdentityExpireDateResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserIdentityExpireDateResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityExpireDate)))
	i += copy(dAtA[i:], m.IdentityExpireDate)
	return i, nil
}

func (m *GetUserIdentityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserIdentityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetUserIdentityInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserIdentityInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.HaveInfo {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	if m.IsAdult {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x22
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityNumber)))
	i += copy(dAtA[i:], m.IdentityNumber)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityValidTime)))
	i += copy(dAtA[i:], m.IdentityValidTime)
	dAtA[i] = 0x30
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Age))
	dAtA[i] = 0x38
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *UserIdentifyInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserIdentifyInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	if m.HaveInfo {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	if m.IsAdult {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x22
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityNumber)))
	i += copy(dAtA[i:], m.IdentityNumber)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityValidTime)))
	i += copy(dAtA[i:], m.IdentityValidTime)
	dAtA[i] = 0x32
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x38
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Level))
	return i, nil
}

func (m *BatchGetUserIdentifyInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserIdentifyInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintRealnameauth(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchGetUserIdentifyInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserIdentifyInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, msg := range m.InfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintRealnameauth(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddUserIdentityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserIdentityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityNumber)))
	i += copy(dAtA[i:], m.IdentityNumber)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityValidTime)))
	i += copy(dAtA[i:], m.IdentityValidTime)
	dAtA[i] = 0x20
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.HavePush))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x30
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Level))
	return i, nil
}

func (m *AddUserIdentityInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserIdentityInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *UpdateUserIdentityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserIdentityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityNumber)))
	i += copy(dAtA[i:], m.IdentityNumber)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityValidTime)))
	i += copy(dAtA[i:], m.IdentityValidTime)
	dAtA[i] = 0x20
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.HavePush))
	return i, nil
}

func (m *UpdateUserIdentityInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserIdentityInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *AddOrUpdateUserIdentityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddOrUpdateUserIdentityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityNumber)))
	i += copy(dAtA[i:], m.IdentityNumber)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityValidTime)))
	i += copy(dAtA[i:], m.IdentityValidTime)
	dAtA[i] = 0x20
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.HavePush))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	return i, nil
}

func (m *AddOrUpdateUserIdentityInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddOrUpdateUserIdentityInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DelUserIdentityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelUserIdentityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *DelUserIdentityInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelUserIdentityInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *AddIdentityExpireUserPushTimeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddIdentityExpireUserPushTimeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.PushTime))
	return i, nil
}

func (m *AddIdentityExpireUserPushTimeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddIdentityExpireUserPushTimeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *RecordUserAuthPhoneReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordUserAuthPhoneReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.AuthPhone)))
	i += copy(dAtA[i:], m.AuthPhone)
	dAtA[i] = 0x18
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.PhoneType))
	return i, nil
}

func (m *RecordUserAuthPhoneResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordUserAuthPhoneResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetUserAuthPhoneReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserAuthPhoneReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetUserAuthPhoneResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserAuthPhoneResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.HavePhone {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.AuthPhone)))
	i += copy(dAtA[i:], m.AuthPhone)
	return i, nil
}

func (m *GetPhoneBindNumberReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPhoneBindNumberReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	return i, nil
}

func (m *GetPhoneBindNumberResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPhoneBindNumberResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.BindNumber))
	return i, nil
}

func (m *RecordUserIdentityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordUserIdentityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityNum)))
	i += copy(dAtA[i:], m.IdentityNum)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityValidTime)))
	i += copy(dAtA[i:], m.IdentityValidTime)
	dAtA[i] = 0x22
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	return i, nil
}

func (m *RecordUserIdentityInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordUserIdentityInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetRealNameCurrVersionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRealNameCurrVersionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetRealNameCurrVersionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRealNameCurrVersionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Version))
	return i, nil
}

func (m *ParentGuardianSwitchReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ParentGuardianSwitchReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	if m.OnOff {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Password)))
	i += copy(dAtA[i:], m.Password)
	dAtA[i] = 0x20
	i++
	if m.IsForceOff {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ParentGuardianSwitchResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ParentGuardianSwitchResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *ParentGuardianCheckPasswordReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ParentGuardianCheckPasswordReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Password)))
	i += copy(dAtA[i:], m.Password)
	return i, nil
}

func (m *ParentGuardianCheckPasswordResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ParentGuardianCheckPasswordResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsPass {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ParentGuardianStateReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ParentGuardianStateReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *ParentGuardianStateResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ParentGuardianStateResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x10
	i++
	if m.OnOff {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ParentGuardianInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ParentGuardianInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	if m.IsOn {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *BatchGetParentGuardianInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetParentGuardianInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintRealnameauth(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchGetParentGuardianInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetParentGuardianInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, msg := range m.InfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintRealnameauth(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CheckIdentityByFaceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckIdentityByFaceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.FaceidCheckData)))
	i += copy(dAtA[i:], m.FaceidCheckData)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.FaceidToken)))
	i += copy(dAtA[i:], m.FaceidToken)
	return i, nil
}

func (m *CheckIdentityByFaceResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckIdentityByFaceResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsPass {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x10
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.ResultCode))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.ResultMsg)))
	i += copy(dAtA[i:], m.ResultMsg)
	return i, nil
}

func (m *CheckAppealCntIsOverLimitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckAppealCntIsOverLimitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *CheckAppealCntIsOverLimitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckAppealCntIsOverLimitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsOverLimit {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ParentGuardianUpdatePwdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ParentGuardianUpdatePwdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Password)))
	i += copy(dAtA[i:], m.Password)
	return i, nil
}

func (m *RealNameAuthInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RealNameAuthInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.AuthPhone)))
	i += copy(dAtA[i:], m.AuthPhone)
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityNum)))
	i += copy(dAtA[i:], m.IdentityNum)
	dAtA[i] = 0x20
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Age))
	return i, nil
}

func (m *GetRealNameAuthTokenReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRealNameAuthTokenReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	if m.RealnameInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("realname_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRealnameauth(dAtA, i, uint64(m.RealnameInfo.Size()))
		n3, err := m.RealnameInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *GetRealNameAuthTokenResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRealNameAuthTokenResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.FaceidToken)))
	i += copy(dAtA[i:], m.FaceidToken)
	dAtA[i] = 0x10
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.ResultCode))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.ResultMsg)))
	i += copy(dAtA[i:], m.ResultMsg)
	return i, nil
}

func (m *GetRealNameFaceIdTokenByUidReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRealNameFaceIdTokenByUidReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetRealNameFaceIdTokenByUidResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRealNameFaceIdTokenByUidResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.FaceidToken)))
	i += copy(dAtA[i:], m.FaceidToken)
	dAtA[i] = 0x10
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.ResultCode))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.ResultMsg)))
	i += copy(dAtA[i:], m.ResultMsg)
	return i, nil
}

func (m *ApplyRealNameAuthDataReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ApplyRealNameAuthDataReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.FaceidToken)))
	i += copy(dAtA[i:], m.FaceidToken)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.FaceidCheckData)))
	i += copy(dAtA[i:], m.FaceidCheckData)
	return i, nil
}

func (m *ApplyRealNameAuthDataResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ApplyRealNameAuthDataResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.ResultCode))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.ResultMsg)))
	i += copy(dAtA[i:], m.ResultMsg)
	return i, nil
}

func (m *RelieveRealNameAuthReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RelieveRealNameAuthReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Uid) > 0 {
		for _, num := range m.Uid {
			dAtA[i] = 0x8
			i++
			i = encodeVarintRealnameauth(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *RelieveRealNameErrDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RelieveRealNameErrDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Reason)))
	i += copy(dAtA[i:], m.Reason)
	return i, nil
}

func (m *RelieveRealNameAuthResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RelieveRealNameAuthResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ErrDetail) > 0 {
		for _, msg := range m.ErrDetail {
			dAtA[i] = 0xa
			i++
			i = encodeVarintRealnameauth(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.ErrMsg)))
	i += copy(dAtA[i:], m.ErrMsg)
	return i, nil
}

func (m *GetRealNameApplyInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRealNameApplyInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetRealNameApplyInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRealNameApplyInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityNum)))
	i += copy(dAtA[i:], m.IdentityNum)
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x18
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.ApplyNum))
	return i, nil
}

func (m *DelRealNameInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelRealNameInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *DelRealNameInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelRealNameInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetTheSameRealNameUserListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTheSameRealNameUserListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetTheSameRealNameUserListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTheSameRealNameUserListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Uids) > 0 {
		for _, num := range m.Uids {
			dAtA[i] = 0x8
			i++
			i = encodeVarintRealnameauth(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetFaceAuthAliTokenReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFaceAuthAliTokenReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	if m.RealnameInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("realname_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRealnameauth(dAtA, i, uint64(m.RealnameInfo.Size()))
		n4, err := m.RealnameInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.MetaInfo)))
	i += copy(dAtA[i:], m.MetaInfo)
	return i, nil
}

func (m *GetFaceAuthAliTokenResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFaceAuthAliTokenResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.CertifyidToken)))
	i += copy(dAtA[i:], m.CertifyidToken)
	dAtA[i] = 0x10
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.ResultCode))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.ResultMsg)))
	i += copy(dAtA[i:], m.ResultMsg)
	return i, nil
}

func (m *GetFaceAuthAliResultReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFaceAuthAliResultReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.CertifyidToken)))
	i += copy(dAtA[i:], m.CertifyidToken)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Scene)))
	i += copy(dAtA[i:], m.Scene)
	return i, nil
}

func (m *GetFaceAuthAliResultResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFaceAuthAliResultResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.ResultCode))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.ResultMsg)))
	i += copy(dAtA[i:], m.ResultMsg)
	return i, nil
}

func (m *GetFaceAuthAliTokenByUidReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFaceAuthAliTokenByUidReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.MetaInfo)))
	i += copy(dAtA[i:], m.MetaInfo)
	return i, nil
}

func (m *GetFaceAuthAliTokenByUidResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFaceAuthAliTokenByUidResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.CertifyidToken)))
	i += copy(dAtA[i:], m.CertifyidToken)
	dAtA[i] = 0x10
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.ResultCode))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.ResultMsg)))
	i += copy(dAtA[i:], m.ResultMsg)
	return i, nil
}

func (m *CheckFaceByAliTokenReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckFaceByAliTokenReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.CertifyidToken)))
	i += copy(dAtA[i:], m.CertifyidToken)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Scene)))
	i += copy(dAtA[i:], m.Scene)
	return i, nil
}

func (m *CheckFaceByAliTokenResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckFaceByAliTokenResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsPass {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *AuthByTwoElementReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AuthByTwoElementReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	if m.RealnameInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("realname_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRealnameauth(dAtA, i, uint64(m.RealnameInfo.Size()))
		n5, err := m.RealnameInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *AuthByTwoElementResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AuthByTwoElementResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsAdult {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *CheckIsInNameBlackListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckIsInNameBlackListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	return i, nil
}

func (m *CheckIsInNameBlackListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckIsInNameBlackListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsBlack {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetFaceRecognitionProviderReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFaceRecognitionProviderReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.AppId)))
	i += copy(dAtA[i:], m.AppId)
	dAtA[i] = 0x10
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	if len(m.SupportProviders) > 0 {
		for _, s := range m.SupportProviders {
			dAtA[i] = 0x1a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *GetFaceRecognitionProviderResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFaceRecognitionProviderResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.ProviderName)))
	i += copy(dAtA[i:], m.ProviderName)
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.ProviderCode)))
	i += copy(dAtA[i:], m.ProviderCode)
	return i, nil
}

func (m *GetFaceRecognitionCertifyIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFaceRecognitionCertifyIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.AppId)))
	i += copy(dAtA[i:], m.AppId)
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.ProviderCode)))
	i += copy(dAtA[i:], m.ProviderCode)
	dAtA[i] = 0x18
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x22
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.MetaInfo)))
	i += copy(dAtA[i:], m.MetaInfo)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.DeviceInfo)))
	i += copy(dAtA[i:], m.DeviceInfo)
	if m.AuthInfo != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintRealnameauth(dAtA, i, uint64(m.AuthInfo.Size()))
		n6, err := m.AuthInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}

func (m *FaceRecognitionCertifyData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FaceRecognitionCertifyData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.OrderNo)))
	i += copy(dAtA[i:], m.OrderNo)
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.CertifyId)))
	i += copy(dAtA[i:], m.CertifyId)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.RequestId)))
	i += copy(dAtA[i:], m.RequestId)
	dAtA[i] = 0x22
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Message)))
	i += copy(dAtA[i:], m.Message)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Exclusive)))
	i += copy(dAtA[i:], m.Exclusive)
	return i, nil
}

func (m *GetFaceRecognitionCertifyIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFaceRecognitionCertifyIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Data != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRealnameauth(dAtA, i, uint64(m.Data.Size()))
		n7, err := m.Data.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *GetFaceRecognitionResultReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFaceRecognitionResultReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.AppId)))
	i += copy(dAtA[i:], m.AppId)
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.ProviderCode)))
	i += copy(dAtA[i:], m.ProviderCode)
	dAtA[i] = 0x18
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x22
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.CertifyId)))
	i += copy(dAtA[i:], m.CertifyId)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Scene)))
	i += copy(dAtA[i:], m.Scene)
	dAtA[i] = 0x30
	i++
	if m.IsRealNameAuth {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x3a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.ProviderData)))
	i += copy(dAtA[i:], m.ProviderData)
	return i, nil
}

func (m *GetFaceRecognitionResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFaceRecognitionResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsPass {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.CertifyId)))
	i += copy(dAtA[i:], m.CertifyId)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.RequestId)))
	i += copy(dAtA[i:], m.RequestId)
	dAtA[i] = 0x22
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.ProviderResponseCode)))
	i += copy(dAtA[i:], m.ProviderResponseCode)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.ProviderResponseResult)))
	i += copy(dAtA[i:], m.ProviderResponseResult)
	dAtA[i] = 0x32
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.ProviderResponseDescription)))
	i += copy(dAtA[i:], m.ProviderResponseDescription)
	return i, nil
}

func (m *GetFaceRecognitionResultResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFaceRecognitionResultResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Result != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRealnameauth(dAtA, i, uint64(m.Result.Size()))
		n8, err := m.Result.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func encodeFixed64Realnameauth(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Realnameauth(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintRealnameauth(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *AuthTypeInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.AuthType))
	n += 1 + sovRealnameauth(uint64(m.AuthStauts))
	return n
}

func (m *AuthIdCardInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.Name)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.IdentityNum)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *AuthInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	n += 1 + sovRealnameauth(uint64(m.Status))
	l = len(m.Birthday)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.Level))
	return n
}

func (m *GetIsEnableCheckReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetIsEnableCheckResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *AddAuthOperHistoryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	n += 1 + sovRealnameauth(uint64(m.OperType))
	n += 1 + sovRealnameauth(uint64(m.AuthType))
	l = len(m.LogInfo)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *AddAuthOperHistoryResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *AuthInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	n += 1 + sovRealnameauth(uint64(m.Status))
	return n
}

func (m *AuthInfoResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetAuthInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	return n
}

func (m *GetAuthInfoResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Status))
	if m.RealnameInfo != nil {
		l = m.RealnameInfo.Size()
		n += 1 + l + sovRealnameauth(uint64(l))
	}
	return n
}

func (m *GetUserRealNameAuthInfoV2Req) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	n += 2
	n += 2
	return n
}

func (m *GetUserRealNameAuthInfoV2Resp) Size() (n int) {
	var l int
	_ = l
	if len(m.AuthList) > 0 {
		for _, e := range m.AuthList {
			l = e.Size()
			n += 1 + l + sovRealnameauth(uint64(l))
		}
	}
	if m.IdcardInfo != nil {
		l = m.IdcardInfo.Size()
		n += 1 + l + sovRealnameauth(uint64(l))
	}
	l = len(m.AuthPhone)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 2
	n += 1 + sovRealnameauth(uint64(m.Age))
	n += 1 + sovRealnameauth(uint64(m.RealnameVersion))
	return n
}

func (m *GetUserIdentityExpireDateReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	return n
}

func (m *GetUserIdentityExpireDateResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.IdentityExpireDate)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *GetUserIdentityInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	return n
}

func (m *GetUserIdentityInfoResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	n += 2
	l = len(m.IdentityNumber)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.IdentityValidTime)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.Age))
	n += 1 + sovRealnameauth(uint64(m.Status))
	return n
}

func (m *UserIdentifyInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	n += 2
	n += 2
	l = len(m.IdentityNumber)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.IdentityValidTime)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.Level))
	return n
}

func (m *BatchGetUserIdentifyInfoReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovRealnameauth(uint64(e))
		}
	}
	return n
}

func (m *BatchGetUserIdentifyInfoResp) Size() (n int) {
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, e := range m.InfoList {
			l = e.Size()
			n += 1 + l + sovRealnameauth(uint64(l))
		}
	}
	return n
}

func (m *AddUserIdentityInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.IdentityNumber)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.IdentityValidTime)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.HavePush))
	l = len(m.Name)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.Level))
	return n
}

func (m *AddUserIdentityInfoResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *UpdateUserIdentityInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.IdentityNumber)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.IdentityValidTime)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.HavePush))
	return n
}

func (m *UpdateUserIdentityInfoResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *AddOrUpdateUserIdentityInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.IdentityNumber)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.IdentityValidTime)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.HavePush))
	l = len(m.Name)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *AddOrUpdateUserIdentityInfoResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DelUserIdentityInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	return n
}

func (m *DelUserIdentityInfoResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *AddIdentityExpireUserPushTimeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	n += 1 + sovRealnameauth(uint64(m.PushTime))
	return n
}

func (m *AddIdentityExpireUserPushTimeResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *RecordUserAuthPhoneReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.AuthPhone)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.PhoneType))
	return n
}

func (m *RecordUserAuthPhoneResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetUserAuthPhoneReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	return n
}

func (m *GetUserAuthPhoneResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	l = len(m.AuthPhone)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *GetPhoneBindNumberReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Phone)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *GetPhoneBindNumberResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.BindNumber))
	return n
}

func (m *RecordUserIdentityInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.IdentityNum)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.IdentityValidTime)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *RecordUserIdentityInfoResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetRealNameCurrVersionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	return n
}

func (m *GetRealNameCurrVersionResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Version))
	return n
}

func (m *ParentGuardianSwitchReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	n += 2
	l = len(m.Password)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 2
	return n
}

func (m *ParentGuardianSwitchResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *ParentGuardianCheckPasswordReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.Password)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *ParentGuardianCheckPasswordResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *ParentGuardianStateReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	return n
}

func (m *ParentGuardianStateResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *ParentGuardianInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	n += 2
	return n
}

func (m *BatchGetParentGuardianInfoReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovRealnameauth(uint64(e))
		}
	}
	return n
}

func (m *BatchGetParentGuardianInfoResp) Size() (n int) {
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, e := range m.InfoList {
			l = e.Size()
			n += 1 + l + sovRealnameauth(uint64(l))
		}
	}
	return n
}

func (m *CheckIdentityByFaceReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.FaceidCheckData)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.FaceidToken)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *CheckIdentityByFaceResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	n += 1 + sovRealnameauth(uint64(m.ResultCode))
	l = len(m.ResultMsg)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *CheckAppealCntIsOverLimitReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	return n
}

func (m *CheckAppealCntIsOverLimitResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *ParentGuardianUpdatePwdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.Password)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *RealNameAuthInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.AuthPhone)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.IdentityNum)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.Age))
	return n
}

func (m *GetRealNameAuthTokenReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	if m.RealnameInfo != nil {
		l = m.RealnameInfo.Size()
		n += 1 + l + sovRealnameauth(uint64(l))
	}
	return n
}

func (m *GetRealNameAuthTokenResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.FaceidToken)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.ResultCode))
	l = len(m.ResultMsg)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *GetRealNameFaceIdTokenByUidReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	return n
}

func (m *GetRealNameFaceIdTokenByUidResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.FaceidToken)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.ResultCode))
	l = len(m.ResultMsg)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *ApplyRealNameAuthDataReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.FaceidToken)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.FaceidCheckData)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *ApplyRealNameAuthDataResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.ResultCode))
	l = len(m.ResultMsg)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *RelieveRealNameAuthReq) Size() (n int) {
	var l int
	_ = l
	if len(m.Uid) > 0 {
		for _, e := range m.Uid {
			n += 1 + sovRealnameauth(uint64(e))
		}
	}
	return n
}

func (m *RelieveRealNameErrDetail) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.Reason)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *RelieveRealNameAuthResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ErrDetail) > 0 {
		for _, e := range m.ErrDetail {
			l = e.Size()
			n += 1 + l + sovRealnameauth(uint64(l))
		}
	}
	l = len(m.ErrMsg)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *GetRealNameApplyInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	return n
}

func (m *GetRealNameApplyInfoResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.IdentityNum)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.ApplyNum))
	return n
}

func (m *DelRealNameInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	return n
}

func (m *DelRealNameInfoResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetTheSameRealNameUserListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	return n
}

func (m *GetTheSameRealNameUserListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.Uids) > 0 {
		for _, e := range m.Uids {
			n += 1 + sovRealnameauth(uint64(e))
		}
	}
	return n
}

func (m *GetFaceAuthAliTokenReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	if m.RealnameInfo != nil {
		l = m.RealnameInfo.Size()
		n += 1 + l + sovRealnameauth(uint64(l))
	}
	l = len(m.MetaInfo)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *GetFaceAuthAliTokenResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.CertifyidToken)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.ResultCode))
	l = len(m.ResultMsg)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *GetFaceAuthAliResultReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.CertifyidToken)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.Scene)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *GetFaceAuthAliResultResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.ResultCode))
	l = len(m.ResultMsg)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *GetFaceAuthAliTokenByUidReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.MetaInfo)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *GetFaceAuthAliTokenByUidResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.CertifyidToken)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.ResultCode))
	l = len(m.ResultMsg)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *CheckFaceByAliTokenReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.CertifyidToken)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.Scene)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *CheckFaceByAliTokenResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *AuthByTwoElementReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	if m.RealnameInfo != nil {
		l = m.RealnameInfo.Size()
		n += 1 + l + sovRealnameauth(uint64(l))
	}
	return n
}

func (m *AuthByTwoElementResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *CheckIsInNameBlackListReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Name)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *CheckIsInNameBlackListResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *GetFaceRecognitionProviderReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.Uid))
	if len(m.SupportProviders) > 0 {
		for _, s := range m.SupportProviders {
			l = len(s)
			n += 1 + l + sovRealnameauth(uint64(l))
		}
	}
	return n
}

func (m *GetFaceRecognitionProviderResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.ProviderName)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.ProviderCode)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *GetFaceRecognitionCertifyIdReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.ProviderCode)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.MetaInfo)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.DeviceInfo)
	n += 1 + l + sovRealnameauth(uint64(l))
	if m.AuthInfo != nil {
		l = m.AuthInfo.Size()
		n += 1 + l + sovRealnameauth(uint64(l))
	}
	return n
}

func (m *FaceRecognitionCertifyData) Size() (n int) {
	var l int
	_ = l
	l = len(m.OrderNo)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.CertifyId)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.RequestId)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.Message)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.Exclusive)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *GetFaceRecognitionCertifyIdResp) Size() (n int) {
	var l int
	_ = l
	if m.Data != nil {
		l = m.Data.Size()
		n += 1 + l + sovRealnameauth(uint64(l))
	}
	return n
}

func (m *GetFaceRecognitionResultReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.ProviderCode)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.CertifyId)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.Scene)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 2
	l = len(m.ProviderData)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *GetFaceRecognitionResult) Size() (n int) {
	var l int
	_ = l
	n += 2
	l = len(m.CertifyId)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.RequestId)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.ProviderResponseCode)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.ProviderResponseResult)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.ProviderResponseDescription)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *GetFaceRecognitionResultResp) Size() (n int) {
	var l int
	_ = l
	if m.Result != nil {
		l = m.Result.Size()
		n += 1 + l + sovRealnameauth(uint64(l))
	}
	return n
}

func sovRealnameauth(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozRealnameauth(x uint64) (n int) {
	return sovRealnameauth(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *AuthTypeInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AuthTypeInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AuthTypeInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuthType", wireType)
			}
			m.AuthType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AuthType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuthStauts", wireType)
			}
			m.AuthStauts = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AuthStauts |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AuthIdCardInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AuthIdCardInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AuthIdCardInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityNum", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityNum = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AuthInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AuthInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AuthInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Birthday", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Birthday = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetIsEnableCheckReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetIsEnableCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetIsEnableCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetIsEnableCheckResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetIsEnableCheckResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetIsEnableCheckResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsEnableCheck", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsEnableCheck = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_enable_check")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddAuthOperHistoryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddAuthOperHistoryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddAuthOperHistoryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperType", wireType)
			}
			m.OperType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuthType", wireType)
			}
			m.AuthType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AuthType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LogInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LogInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("oper_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("auth_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("log_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddAuthOperHistoryResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddAuthOperHistoryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddAuthOperHistoryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AuthInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AuthInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AuthInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AuthInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AuthInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AuthInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAuthInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAuthInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAuthInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAuthInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAuthInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAuthInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RealnameInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RealnameInfo == nil {
				m.RealnameInfo = &RealNameAuthInfo{}
			}
			if err := m.RealnameInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserRealNameAuthInfoV2Req) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserRealNameAuthInfoV2Req: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserRealNameAuthInfoV2Req: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsNeedAuthPhone", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNeedAuthPhone = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsNeedIdcardInfo", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNeedIdcardInfo = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserRealNameAuthInfoV2Resp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserRealNameAuthInfoV2Resp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserRealNameAuthInfoV2Resp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuthList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AuthList = append(m.AuthList, &AuthTypeInfo{})
			if err := m.AuthList[len(m.AuthList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdcardInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.IdcardInfo == nil {
				m.IdcardInfo = &AuthIdCardInfo{}
			}
			if err := m.IdcardInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuthPhone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AuthPhone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAdult", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAdult = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Age", wireType)
			}
			m.Age = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Age |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RealnameVersion", wireType)
			}
			m.RealnameVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RealnameVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserIdentityExpireDateReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserIdentityExpireDateReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserIdentityExpireDateReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserIdentityExpireDateResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserIdentityExpireDateResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserIdentityExpireDateResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityExpireDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityExpireDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserIdentityInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserIdentityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserIdentityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserIdentityInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserIdentityInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserIdentityInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HaveInfo", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HaveInfo = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAdult", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAdult = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityNumber", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityNumber = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityValidTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityValidTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Age", wireType)
			}
			m.Age = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Age |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("have_info")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_adult")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserIdentifyInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserIdentifyInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserIdentifyInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HaveInfo", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HaveInfo = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAdult", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAdult = bool(v != 0)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityNumber", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityNumber = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityValidTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityValidTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("have_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserIdentifyInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserIdentifyInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserIdentifyInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRealnameauth
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRealnameauth
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthRealnameauth
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowRealnameauth
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserIdentifyInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserIdentifyInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserIdentifyInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.InfoList = append(m.InfoList, &UserIdentifyInfo{})
			if err := m.InfoList[len(m.InfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserIdentityInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserIdentityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserIdentityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityNumber", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityNumber = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityValidTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityValidTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HavePush", wireType)
			}
			m.HavePush = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HavePush |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("identity_number")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("identity_valid_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("have_push")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserIdentityInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserIdentityInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserIdentityInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserIdentityInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserIdentityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserIdentityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityNumber", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityNumber = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityValidTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityValidTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HavePush", wireType)
			}
			m.HavePush = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HavePush |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("have_push")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserIdentityInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserIdentityInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserIdentityInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddOrUpdateUserIdentityInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddOrUpdateUserIdentityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddOrUpdateUserIdentityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityNumber", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityNumber = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityValidTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityValidTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HavePush", wireType)
			}
			m.HavePush = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HavePush |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("have_push")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddOrUpdateUserIdentityInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddOrUpdateUserIdentityInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddOrUpdateUserIdentityInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelUserIdentityInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelUserIdentityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelUserIdentityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelUserIdentityInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelUserIdentityInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelUserIdentityInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddIdentityExpireUserPushTimeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddIdentityExpireUserPushTimeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddIdentityExpireUserPushTimeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushTime", wireType)
			}
			m.PushTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PushTime |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddIdentityExpireUserPushTimeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddIdentityExpireUserPushTimeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddIdentityExpireUserPushTimeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordUserAuthPhoneReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordUserAuthPhoneReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordUserAuthPhoneReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuthPhone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AuthPhone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhoneType", wireType)
			}
			m.PhoneType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PhoneType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("auth_phone")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordUserAuthPhoneResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordUserAuthPhoneResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordUserAuthPhoneResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserAuthPhoneReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserAuthPhoneReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserAuthPhoneReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserAuthPhoneResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserAuthPhoneResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserAuthPhoneResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HavePhone", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HavePhone = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuthPhone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AuthPhone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("have_phone")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPhoneBindNumberReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPhoneBindNumberReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPhoneBindNumberReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("phone")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPhoneBindNumberResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPhoneBindNumberResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPhoneBindNumberResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BindNumber", wireType)
			}
			m.BindNumber = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BindNumber |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("bind_number")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordUserIdentityInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordUserIdentityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordUserIdentityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityNum", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityNum = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityValidTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityValidTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("identity_num")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("identity_valid_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordUserIdentityInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordUserIdentityInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordUserIdentityInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRealNameCurrVersionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRealNameCurrVersionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRealNameCurrVersionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRealNameCurrVersionResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRealNameCurrVersionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRealNameCurrVersionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			m.Version = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Version |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("version")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ParentGuardianSwitchReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ParentGuardianSwitchReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ParentGuardianSwitchReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnOff", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.OnOff = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Password", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Password = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsForceOff", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsForceOff = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("on_off")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("password")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ParentGuardianSwitchResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ParentGuardianSwitchResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ParentGuardianSwitchResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ParentGuardianCheckPasswordReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ParentGuardianCheckPasswordReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ParentGuardianCheckPasswordReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Password", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Password = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("password")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ParentGuardianCheckPasswordResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ParentGuardianCheckPasswordResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ParentGuardianCheckPasswordResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPass", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPass = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_pass")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ParentGuardianStateReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ParentGuardianStateReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ParentGuardianStateReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ParentGuardianStateResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ParentGuardianStateResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ParentGuardianStateResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnOff", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.OnOff = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("on_off")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ParentGuardianInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ParentGuardianInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ParentGuardianInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsOn", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsOn = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_on")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetParentGuardianInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetParentGuardianInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetParentGuardianInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRealnameauth
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRealnameauth
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthRealnameauth
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowRealnameauth
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetParentGuardianInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetParentGuardianInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetParentGuardianInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.InfoList = append(m.InfoList, &ParentGuardianInfo{})
			if err := m.InfoList[len(m.InfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckIdentityByFaceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckIdentityByFaceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckIdentityByFaceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FaceidCheckData", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FaceidCheckData = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FaceidToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FaceidToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("faceid_check_data")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("faceid_token")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckIdentityByFaceResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckIdentityByFaceResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckIdentityByFaceResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPass", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPass = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultCode", wireType)
			}
			m.ResultCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResultCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_pass")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckAppealCntIsOverLimitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckAppealCntIsOverLimitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckAppealCntIsOverLimitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckAppealCntIsOverLimitResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckAppealCntIsOverLimitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckAppealCntIsOverLimitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsOverLimit", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsOverLimit = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_over_limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ParentGuardianUpdatePwdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ParentGuardianUpdatePwdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ParentGuardianUpdatePwdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Password", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Password = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("password")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RealNameAuthInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RealNameAuthInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RealNameAuthInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuthPhone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AuthPhone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityNum", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityNum = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Age", wireType)
			}
			m.Age = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Age |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRealNameAuthTokenReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRealNameAuthTokenReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRealNameAuthTokenReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RealnameInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RealnameInfo == nil {
				m.RealnameInfo = &RealNameAuthInfo{}
			}
			if err := m.RealnameInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("realname_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRealNameAuthTokenResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRealNameAuthTokenResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRealNameAuthTokenResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FaceidToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FaceidToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultCode", wireType)
			}
			m.ResultCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResultCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("faceid_token")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRealNameFaceIdTokenByUidReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRealNameFaceIdTokenByUidReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRealNameFaceIdTokenByUidReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRealNameFaceIdTokenByUidResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRealNameFaceIdTokenByUidResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRealNameFaceIdTokenByUidResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FaceidToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FaceidToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultCode", wireType)
			}
			m.ResultCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResultCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("faceid_token")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ApplyRealNameAuthDataReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ApplyRealNameAuthDataReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ApplyRealNameAuthDataReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FaceidToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FaceidToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FaceidCheckData", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FaceidCheckData = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("faceid_token")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("faceid_check_data")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ApplyRealNameAuthDataResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ApplyRealNameAuthDataResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ApplyRealNameAuthDataResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultCode", wireType)
			}
			m.ResultCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResultCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RelieveRealNameAuthReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RelieveRealNameAuthReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RelieveRealNameAuthReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRealnameauth
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Uid = append(m.Uid, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRealnameauth
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthRealnameauth
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowRealnameauth
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Uid = append(m.Uid, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RelieveRealNameErrDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RelieveRealNameErrDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RelieveRealNameErrDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("reason")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RelieveRealNameAuthResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RelieveRealNameAuthResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RelieveRealNameAuthResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ErrDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ErrDetail = append(m.ErrDetail, &RelieveRealNameErrDetail{})
			if err := m.ErrDetail[len(m.ErrDetail)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ErrMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ErrMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRealNameApplyInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRealNameApplyInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRealNameApplyInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRealNameApplyInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRealNameApplyInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRealNameApplyInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityNum", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityNum = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyNum", wireType)
			}
			m.ApplyNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelRealNameInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelRealNameInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelRealNameInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelRealNameInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelRealNameInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelRealNameInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTheSameRealNameUserListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTheSameRealNameUserListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTheSameRealNameUserListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTheSameRealNameUserListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTheSameRealNameUserListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTheSameRealNameUserListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRealnameauth
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Uids = append(m.Uids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRealnameauth
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthRealnameauth
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowRealnameauth
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Uids = append(m.Uids, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uids", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFaceAuthAliTokenReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFaceAuthAliTokenReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFaceAuthAliTokenReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RealnameInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RealnameInfo == nil {
				m.RealnameInfo = &RealNameAuthInfo{}
			}
			if err := m.RealnameInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MetaInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MetaInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("realname_info")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("meta_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFaceAuthAliTokenResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFaceAuthAliTokenResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFaceAuthAliTokenResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CertifyidToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CertifyidToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultCode", wireType)
			}
			m.ResultCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResultCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFaceAuthAliResultReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFaceAuthAliResultReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFaceAuthAliResultReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CertifyidToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CertifyidToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Scene", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Scene = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("certifyid_token")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFaceAuthAliResultResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFaceAuthAliResultResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFaceAuthAliResultResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultCode", wireType)
			}
			m.ResultCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResultCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFaceAuthAliTokenByUidReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFaceAuthAliTokenByUidReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFaceAuthAliTokenByUidReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MetaInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MetaInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("meta_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFaceAuthAliTokenByUidResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFaceAuthAliTokenByUidResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFaceAuthAliTokenByUidResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CertifyidToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CertifyidToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultCode", wireType)
			}
			m.ResultCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResultCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckFaceByAliTokenReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckFaceByAliTokenReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckFaceByAliTokenReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CertifyidToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CertifyidToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Scene", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Scene = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("certifyid_token")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckFaceByAliTokenResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckFaceByAliTokenResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckFaceByAliTokenResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPass", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPass = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AuthByTwoElementReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AuthByTwoElementReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AuthByTwoElementReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RealnameInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RealnameInfo == nil {
				m.RealnameInfo = &RealNameAuthInfo{}
			}
			if err := m.RealnameInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("realname_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AuthByTwoElementResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AuthByTwoElementResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AuthByTwoElementResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAdult", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAdult = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckIsInNameBlackListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckIsInNameBlackListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckIsInNameBlackListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckIsInNameBlackListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckIsInNameBlackListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckIsInNameBlackListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsBlack", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsBlack = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFaceRecognitionProviderReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFaceRecognitionProviderReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFaceRecognitionProviderReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SupportProviders", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SupportProviders = append(m.SupportProviders, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFaceRecognitionProviderResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFaceRecognitionProviderResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFaceRecognitionProviderResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProviderName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProviderName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProviderCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProviderCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFaceRecognitionCertifyIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFaceRecognitionCertifyIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFaceRecognitionCertifyIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProviderCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProviderCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MetaInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MetaInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuthInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.AuthInfo == nil {
				m.AuthInfo = &RealNameAuthInfo{}
			}
			if err := m.AuthInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("provider_code")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FaceRecognitionCertifyData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FaceRecognitionCertifyData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FaceRecognitionCertifyData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CertifyId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CertifyId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequestId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RequestId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Exclusive", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Exclusive = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFaceRecognitionCertifyIdResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFaceRecognitionCertifyIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFaceRecognitionCertifyIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Data == nil {
				m.Data = &FaceRecognitionCertifyData{}
			}
			if err := m.Data.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFaceRecognitionResultReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFaceRecognitionResultReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFaceRecognitionResultReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProviderCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProviderCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CertifyId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CertifyId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Scene", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Scene = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsRealNameAuth", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsRealNameAuth = bool(v != 0)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProviderData", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProviderData = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("provider_code")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("certify_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("scene")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFaceRecognitionResult) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFaceRecognitionResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFaceRecognitionResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPass", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPass = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CertifyId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CertifyId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequestId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RequestId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProviderResponseCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProviderResponseCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProviderResponseResult", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProviderResponseResult = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProviderResponseDescription", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProviderResponseDescription = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_pass")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFaceRecognitionResultResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFaceRecognitionResultResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFaceRecognitionResultResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Result == nil {
				m.Result = &GetFaceRecognitionResult{}
			}
			if err := m.Result.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipRealnameauth(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthRealnameauth
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowRealnameauth
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipRealnameauth(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthRealnameauth = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowRealnameauth   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/realNameAuthSvr/realnameauth.proto", fileDescriptorRealnameauth) }

var fileDescriptorRealnameauth = []byte{
	// 4074 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x5b, 0x4d, 0x70, 0x1c, 0x49,
	0x56, 0x9e, 0xea, 0xd6, 0xef, 0x93, 0x6d, 0xb5, 0x53, 0xb6, 0xdc, 0x2a, 0xfd, 0xb5, 0xd2, 0x7f,
	0xb2, 0x46, 0xb2, 0xc7, 0xf6, 0xfc, 0x85, 0x56, 0xd3, 0xac, 0x7e, 0x7a, 0x6c, 0xc5, 0x8c, 0x25,
	0x85, 0x24, 0x9b, 0x5d, 0x08, 0x68, 0x4a, 0x5d, 0xd9, 0x52, 0xe1, 0xee, 0xaa, 0x9c, 0xca, 0x6a,
	0x8d, 0xb5, 0x33, 0x0b, 0x43, 0x40, 0xb0, 0xfc, 0x2d, 0x43, 0x4c, 0xc0, 0x06, 0xc3, 0x81, 0x8d,
	0x60, 0x0d, 0x41, 0x10, 0x1b, 0x70, 0xe2, 0x44, 0x10, 0xc1, 0x81, 0xc3, 0x12, 0x5c, 0x38, 0x73,
	0x20, 0x88, 0xe1, 0x32, 0x27, 0xce, 0x1c, 0x89, 0xcc, 0xfa, 0xe9, 0xac, 0xaa, 0xac, 0xea, 0xf6,
	0xae, 0x87, 0x09, 0x2e, 0x33, 0x72, 0xd6, 0xcb, 0x7c, 0xdf, 0x7b, 0xf9, 0xde, 0xcb, 0x97, 0xf9,
	0x5e, 0xc3, 0x0d, 0xe6, 0x36, 0xee, 0xb8, 0xc4, 0x68, 0xed, 0x18, 0x6d, 0xb2, 0xde, 0xf1, 0x4e,
	0x0e, 0x4e, 0x5d, 0xf1, 0x6f, 0xdb, 0x68, 0x13, 0xa3, 0xe3, 0x9d, 0xdc, 0xa6, 0xae, 0xe3, 0x39,
	0xe8, 0x9c, 0x3c, 0xa6, 0x5f, 0x6b, 0x38, 0xed, 0xb6, 0x63, 0xdf, 0xf1, 0x5a, 0xa7, 0xd4, 0x6a,
	0x3c, 0x6d, 0x91, 0x3b, 0xec, 0xe9, 0x51, 0xc7, 0x6a, 0x79, 0x96, 0xed, 0x9d, 0x51, 0xe2, 0xcf,
	0xc1, 0xdf, 0x82, 0x73, 0x7c, 0xc5, 0xc3, 0x33, 0x4a, 0xb6, 0xed, 0xa6, 0x83, 0x16, 0x60, 0x94,
	0xcf, 0xae, 0x73, 0x92, 0xb2, 0x56, 0xd1, 0x16, 0xcf, 0x6f, 0x0c, 0xfc, 0xe4, 0x3f, 0xe6, 0x5f,
	0xd9, 0x1f, 0x31, 0x02, 0x32, 0x74, 0x1d, 0xc6, 0x04, 0x09, 0xf3, 0x8c, 0x8e, 0xc7, 0xca, 0x05,
	0x89, 0x08, 0xf8, 0x87, 0x03, 0x31, 0x8e, 0x0f, 0xe0, 0x02, 0x5f, 0x79, 0xdb, 0xdc, 0x34, 0x5c,
	0x53, 0xac, 0x5d, 0x86, 0x01, 0x8e, 0x4e, 0x2c, 0x3b, 0x1a, 0xcc, 0x10, 0x23, 0xe8, 0x26, 0x9c,
	0xb3, 0x4c, 0x62, 0x7b, 0x96, 0x77, 0x56, 0xb7, 0x3b, 0x6d, 0xb1, 0x66, 0x48, 0x31, 0x16, 0x7e,
	0xd9, 0xe9, 0xb4, 0xf1, 0xaf, 0xc1, 0x88, 0x58, 0x94, 0x2f, 0x37, 0x09, 0xc5, 0x8e, 0x65, 0xc6,
	0x40, 0xf2, 0x01, 0x34, 0x03, 0x43, 0xcc, 0x33, 0xbc, 0x8e, 0x0f, 0x6d, 0x30, 0xf8, 0x14, 0x8c,
	0xa1, 0x0a, 0x8c, 0x1c, 0x59, 0xae, 0x77, 0x62, 0x1a, 0x67, 0xe5, 0xa2, 0xc4, 0x26, 0x1a, 0x45,
	0x3a, 0x0c, 0xb6, 0xc8, 0x29, 0x69, 0x95, 0x07, 0xa4, 0x95, 0xfd, 0x21, 0x7c, 0x19, 0x26, 0x1e,
	0x10, 0x6f, 0x9b, 0xd5, 0x6c, 0xe3, 0xa8, 0x45, 0x36, 0x4f, 0x48, 0xe3, 0xe9, 0x3e, 0xf9, 0x00,
	0x6f, 0xc1, 0xa5, 0xf4, 0x30, 0xa3, 0x68, 0x19, 0xc6, 0x2d, 0x56, 0x27, 0x62, 0xb4, 0xde, 0xe0,
	0xc3, 0x65, 0xad, 0x52, 0x58, 0x1c, 0x09, 0x16, 0x3d, 0x6f, 0xc9, 0x33, 0xf0, 0x0f, 0x34, 0xb8,
	0xbc, 0x6e, 0x9a, 0x5c, 0xc0, 0x5d, 0x4a, 0xdc, 0x87, 0x16, 0xf3, 0x1c, 0xf7, 0x6c, 0x9f, 0x7c,
	0xd0, 0x15, 0xb5, 0x10, 0x17, 0x75, 0x01, 0x46, 0x1d, 0x4a, 0x5c, 0x7f, 0xb7, 0x0a, 0xd2, 0xd7,
	0x11, 0x3e, 0x2c, 0x76, 0x2b, 0xb6, 0xa1, 0x45, 0x99, 0x24, 0xda, 0xd0, 0x79, 0x18, 0x69, 0x39,
	0xc7, 0x75, 0xcb, 0x6e, 0x3a, 0xe5, 0x81, 0x4a, 0x21, 0x52, 0xc9, 0x70, 0xcb, 0x39, 0xe6, 0x9a,
	0xc6, 0x65, 0x98, 0x54, 0xe1, 0x62, 0x14, 0x6f, 0xc2, 0x58, 0xb8, 0x1f, 0x79, 0x38, 0xe5, 0x2d,
	0x29, 0x24, 0xb7, 0x04, 0x5f, 0xf0, 0x6d, 0xd0, 0x5f, 0x84, 0x51, 0xbc, 0x08, 0x17, 0x1e, 0x10,
	0xaf, 0x8f, 0x75, 0xb1, 0x07, 0xe3, 0x31, 0x4a, 0x46, 0xf3, 0x59, 0xa1, 0x4d, 0x38, 0x1f, 0x3a,
	0x89, 0x2f, 0x2f, 0x37, 0x81, 0xb1, 0x7b, 0x73, 0xb7, 0x63, 0xee, 0xb4, 0x2f, 0xf9, 0x9a, 0x58,
	0x38, 0xf2, 0x2c, 0xa1, 0x8e, 0x3f, 0xd7, 0x60, 0xe6, 0x01, 0xf1, 0x1e, 0x33, 0xe2, 0x26, 0x29,
	0x9f, 0xdc, 0xcb, 0x53, 0xc3, 0x5d, 0x40, 0x16, 0xab, 0xdb, 0x84, 0x98, 0x75, 0xb1, 0x27, 0xf4,
	0xc4, 0xb1, 0x89, 0xb0, 0xd2, 0xd0, 0x22, 0xc6, 0x2d, 0xb6, 0x43, 0x88, 0x50, 0xf5, 0x1e, 0xff,
	0x88, 0xee, 0xc3, 0x44, 0x38, 0xc5, 0x32, 0x1b, 0x86, 0x6b, 0x76, 0x61, 0x87, 0x73, 0x4a, 0xfe,
	0x9c, 0x6d, 0xf1, 0x59, 0x00, 0xfc, 0xab, 0x02, 0xcc, 0xe6, 0x00, 0x64, 0x14, 0xbd, 0x15, 0x58,
	0x45, 0xcb, 0x62, 0x5e, 0x59, 0xab, 0x14, 0x17, 0xc7, 0xee, 0xe9, 0x71, 0x1d, 0xc8, 0x51, 0xc1,
	0xb7, 0x95, 0xf7, 0x2d, 0xe6, 0xa1, 0x77, 0x60, 0x4c, 0xc6, 0x51, 0x10, 0xea, 0x9b, 0x49, 0x4f,
	0xed, 0xba, 0xfd, 0x3e, 0x58, 0x11, 0x32, 0x74, 0x15, 0x40, 0x92, 0x5c, 0xf6, 0x3f, 0x81, 0xc7,
	0x97, 0x79, 0x1e, 0x46, 0x2c, 0x56, 0x37, 0xcc, 0x4e, 0xcb, 0x13, 0x3e, 0x18, 0x0a, 0x3a, 0x6c,
	0xb1, 0x75, 0x3e, 0xc8, 0xf5, 0x6b, 0x1c, 0x93, 0xf2, 0xa0, 0xec, 0xf9, 0xc6, 0x31, 0x41, 0x77,
	0xa0, 0x14, 0xed, 0xee, 0x29, 0x71, 0x99, 0xe5, 0xd8, 0xe5, 0x21, 0x89, 0x68, 0x3c, 0xfc, 0xfa,
	0xc4, 0xff, 0x88, 0xdf, 0x8c, 0x36, 0x72, 0x3b, 0x08, 0x32, 0xb5, 0x67, 0xd4, 0x72, 0xc9, 0x96,
	0xe1, 0x91, 0x3c, 0xbb, 0xfb, 0xf9, 0x48, 0xbf, 0xaa, 0x79, 0x8c, 0xa2, 0x37, 0xe1, 0x52, 0x14,
	0xd0, 0x88, 0xf8, 0x54, 0x37, 0x0d, 0x2f, 0x1e, 0xfa, 0x90, 0x95, 0x9a, 0x8b, 0x5f, 0x83, 0xc9,
	0xc4, 0xc2, 0xbd, 0x5c, 0xe0, 0x7f, 0x34, 0xb8, 0xa2, 0x9c, 0xc2, 0x28, 0xf7, 0xfd, 0x13, 0xe3,
	0x34, 0xb0, 0x74, 0x39, 0xf0, 0x8c, 0xf0, 0x61, 0xb1, 0x21, 0xb2, 0xae, 0x8b, 0x12, 0x45, 0xa4,
	0xeb, 0x15, 0x18, 0x97, 0x43, 0xf3, 0x11, 0x71, 0xc5, 0x9e, 0x84, 0x42, 0x5c, 0x90, 0xa2, 0xf3,
	0x11, 0x71, 0xd1, 0xeb, 0x30, 0x11, 0x91, 0x9f, 0x1a, 0x2d, 0xcb, 0xac, 0x7b, 0x56, 0xdb, 0xdf,
	0xaa, 0x70, 0xca, 0xc5, 0x90, 0xe0, 0x09, 0xff, 0x7e, 0x68, 0xb5, 0x49, 0xb8, 0xa1, 0x43, 0xc9,
	0x0d, 0xed, 0x3a, 0xf3, 0x70, 0x3a, 0x94, 0xe3, 0xdf, 0x2d, 0x40, 0xa9, 0x2b, 0x77, 0xf3, 0x2c,
	0x7e, 0x2a, 0xa4, 0x43, 0x65, 0x57, 0x17, 0x85, 0x3e, 0x74, 0xa1, 0x7d, 0x4d, 0xba, 0x08, 0x4f,
	0xc9, 0xa1, 0xd4, 0x29, 0x19, 0x1d, 0x4c, 0xc3, 0xe9, 0x83, 0xe9, 0x6d, 0x98, 0xde, 0x30, 0xbc,
	0xc6, 0x49, 0xcc, 0x14, 0x9a, 0x91, 0xf5, 0x4c, 0xc1, 0x48, 0xc7, 0x32, 0xbb, 0xee, 0x7e, 0x7e,
	0x7f, 0xb8, 0x63, 0x99, 0xdc, 0xa3, 0xf1, 0x2f, 0xc2, 0x4c, 0xf6, 0x4c, 0x46, 0xd1, 0x37, 0x60,
	0x94, 0xeb, 0x4c, 0x0e, 0x15, 0x89, 0x70, 0x99, 0x9a, 0x36, 0xc2, 0x27, 0x88, 0xc5, 0xff, 0x5b,
	0x13, 0x47, 0xc7, 0x0b, 0x18, 0xb4, 0x4a, 0xc9, 0x05, 0xe9, 0x50, 0xea, 0x53, 0xc9, 0x45, 0x69,
	0x8a, 0x42, 0xc9, 0xa1, 0x35, 0xd0, 0x0e, 0x3b, 0x11, 0x67, 0xde, 0xa0, 0x6c, 0x0d, 0x7b, 0x1d,
	0x76, 0x12, 0xed, 0xc3, 0x60, 0xf6, 0x3e, 0x0c, 0xa5, 0xf7, 0x61, 0x0a, 0xae, 0x28, 0xe5, 0x65,
	0x14, 0xff, 0x83, 0x06, 0x53, 0x8f, 0x29, 0x0f, 0x03, 0x3f, 0xb3, 0x3a, 0xb4, 0x17, 0x57, 0x87,
	0xf6, 0xb3, 0xa9, 0x03, 0xcf, 0x80, 0x9e, 0x05, 0x9e, 0x51, 0xfc, 0xef, 0x1a, 0xcc, 0xad, 0x9b,
	0xe6, 0xae, 0xfb, 0xff, 0x56, 0xc0, 0xec, 0xfd, 0xc6, 0x0b, 0x30, 0x9f, 0x2b, 0x1b, 0xa3, 0x3c,
	0x6e, 0x6f, 0x91, 0xd6, 0x8b, 0xc4, 0xed, 0x29, 0xb8, 0xa2, 0x9c, 0xc1, 0x28, 0xfe, 0x25, 0xa8,
	0xac, 0x9b, 0x66, 0xfc, 0x64, 0xe1, 0x84, 0x1c, 0x26, 0x97, 0x26, 0xb1, 0xec, 0x60, 0x22, 0xcc,
	0x71, 0x19, 0x7d, 0xa5, 0xc8, 0x19, 0xd0, 0x08, 0x0d, 0x66, 0xe3, 0xab, 0xb0, 0xd0, 0x63, 0x79,
	0x46, 0xf1, 0x77, 0x60, 0x72, 0x9f, 0x34, 0x1c, 0x57, 0x98, 0x72, 0x94, 0x8e, 0xe4, 0xed, 0x63,
	0xfc, 0x68, 0x97, 0x5d, 0x56, 0x3a, 0xda, 0xaf, 0x02, 0x88, 0xef, 0x61, 0x3a, 0xda, 0xf5, 0x9f,
	0x51, 0x31, 0xce, 0x33, 0x0e, 0xae, 0x1a, 0x25, 0x6f, 0x46, 0xf1, 0x8a, 0xc8, 0xbf, 0xfb, 0xc5,
	0x84, 0x7f, 0x45, 0xe4, 0xe5, 0xa9, 0x65, 0x38, 0x0c, 0xdf, 0x1c, 0x04, 0x56, 0xf9, 0x64, 0x14,
	0x66, 0x12, 0x61, 0x4d, 0x64, 0x69, 0x69, 0x81, 0xf0, 0x7d, 0xb8, 0xfc, 0x80, 0x78, 0xe2, 0xef,
	0x0d, 0xcb, 0x36, 0x7d, 0x23, 0xe5, 0x90, 0x74, 0x18, 0xec, 0xae, 0x1e, 0x4e, 0xf4, 0x87, 0xf0,
	0xcf, 0x89, 0x53, 0x3e, 0x35, 0x89, 0x51, 0x7e, 0xb7, 0x3a, 0xb2, 0x6c, 0x33, 0x74, 0x04, 0x59,
	0x20, 0x38, 0x8a, 0x48, 0xf1, 0x8f, 0x35, 0x98, 0xea, 0xaa, 0xa8, 0x5f, 0x4f, 0x4b, 0xdf, 0xb2,
	0x0a, 0xca, 0x5b, 0xd6, 0x4f, 0x19, 0x53, 0x43, 0x07, 0x92, 0xaf, 0x10, 0xbe, 0x03, 0xcd, 0x80,
	0x9e, 0x85, 0x96, 0x51, 0x7c, 0x1f, 0xa6, 0x1e, 0x10, 0x2f, 0x4c, 0x54, 0x37, 0x3b, 0xae, 0x1b,
	0xa4, 0x67, 0x79, 0x3b, 0xbb, 0x06, 0x7a, 0xd6, 0x24, 0x46, 0xd1, 0x1c, 0x0c, 0x87, 0xf9, 0x9f,
	0x3c, 0x33, 0x1c, 0xc4, 0x7f, 0xaa, 0xc1, 0x95, 0x3d, 0xc3, 0x25, 0xb6, 0xf7, 0xa0, 0x63, 0xb8,
	0xa6, 0x65, 0xd8, 0x07, 0x1f, 0x5a, 0x5e, 0xe3, 0x24, 0x4f, 0x7b, 0xd3, 0x30, 0xe4, 0xd8, 0x75,
	0xa7, 0xd9, 0x8c, 0x65, 0x0f, 0x83, 0x8e, 0xbd, 0xdb, 0x6c, 0xf2, 0x5b, 0x25, 0x35, 0x18, 0xfb,
	0xd0, 0x71, 0xcd, 0x98, 0x9a, 0xa2, 0x51, 0x74, 0x03, 0xce, 0x59, 0xac, 0xde, 0x74, 0xdc, 0x06,
	0x11, 0x8b, 0xc8, 0x89, 0x2d, 0x58, 0xec, 0x5d, 0xfe, 0x61, 0xb7, 0xd9, 0xc4, 0x3a, 0x94, 0xd5,
	0xc8, 0x18, 0xc5, 0xbf, 0x00, 0x73, 0xf1, 0x6f, 0xe2, 0xde, 0xb8, 0x17, 0xb0, 0xc8, 0x03, 0xdf,
	0x13, 0x1f, 0xfe, 0x26, 0xcc, 0xe7, 0xae, 0xcd, 0x28, 0x9a, 0x85, 0x61, 0x8b, 0xd5, 0xf9, 0x8c,
	0x98, 0xcb, 0x0c, 0x59, 0x8c, 0x93, 0xf1, 0x18, 0x98, 0x40, 0xee, 0xf5, 0x48, 0xa3, 0xdf, 0x4c,
	0xed, 0x82, 0x17, 0x26, 0xd0, 0x79, 0xda, 0xc6, 0x0f, 0x00, 0xc5, 0xe7, 0xe5, 0x66, 0x7e, 0x53,
	0x30, 0x68, 0xb1, 0xba, 0x63, 0xc7, 0x56, 0x1a, 0xb0, 0xd8, 0xae, 0x8d, 0x57, 0x61, 0x36, 0xcc,
	0x7d, 0xd2, 0x0b, 0xf6, 0xc8, 0x9b, 0xea, 0x30, 0x97, 0x37, 0x97, 0x51, 0xf4, 0x4e, 0x3a, 0x73,
	0xaa, 0xc4, 0x33, 0x27, 0xc5, 0xc4, 0x6e, 0xee, 0xf4, 0xfb, 0x1a, 0x4c, 0x8a, 0x4d, 0x08, 0x3d,
	0x66, 0xe3, 0xec, 0x5d, 0xa3, 0x91, 0x1b, 0x83, 0x5f, 0x83, 0x8b, 0x4d, 0xa3, 0x41, 0x2c, 0xd3,
	0x7f, 0x6c, 0xe0, 0x77, 0x0e, 0x23, 0xe6, 0xe6, 0xe3, 0xfe, 0x67, 0xb1, 0xec, 0x96, 0xe1, 0x19,
	0x3c, 0x26, 0x04, 0x33, 0x3c, 0xe7, 0x29, 0xb1, 0x63, 0xc6, 0x31, 0xe6, 0x7f, 0x39, 0xe4, 0x1f,
	0xf0, 0x6f, 0x69, 0x70, 0x45, 0x89, 0xa6, 0xa7, 0x61, 0xf0, 0xa0, 0xe6, 0x12, 0xd6, 0x69, 0x79,
	0xf5, 0x86, 0x63, 0x92, 0xf8, 0x83, 0x91, 0xff, 0x61, 0xd3, 0x31, 0x45, 0xbc, 0x0d, 0xc8, 0xda,
	0xec, 0x38, 0x7e, 0x37, 0xf4, 0xc7, 0x1f, 0xb1, 0x63, 0x7e, 0x63, 0x13, 0x28, 0xd6, 0x29, 0x25,
	0x46, 0x6b, 0xd3, 0xf6, 0xb6, 0xd9, 0xee, 0x29, 0x71, 0xdf, 0xb7, 0xda, 0x96, 0x97, 0x67, 0x6a,
	0xdb, 0x30, 0x9b, 0x33, 0x8f, 0x51, 0xb4, 0x08, 0xe7, 0xb9, 0x95, 0x9c, 0x12, 0xb7, 0xde, 0xe2,
	0x83, 0x31, 0x49, 0xc6, 0xac, 0x2e, 0x35, 0x7e, 0x02, 0x7a, 0x7c, 0xdf, 0xfc, 0xbc, 0x60, 0xef,
	0xc3, 0xbe, 0x3d, 0xb0, 0xa0, 0xf4, 0xc0, 0xcf, 0x34, 0x28, 0x25, 0xaf, 0xeb, 0x89, 0x43, 0x48,
	0x53, 0x5f, 0x98, 0xc3, 0xc8, 0x5b, 0xe8, 0xf9, 0xb0, 0x56, 0xcc, 0x78, 0x58, 0x0b, 0x6f, 0x60,
	0x03, 0x89, 0x1b, 0x18, 0x3e, 0x15, 0xb7, 0x4b, 0x19, 0x96, 0x30, 0x87, 0x3c, 0x49, 0x53, 0x6f,
	0x2c, 0x5c, 0xdc, 0x17, 0x7d, 0x63, 0xf9, 0x54, 0x83, 0xb2, 0x9a, 0x31, 0xa3, 0x29, 0xa3, 0xd5,
	0x32, 0x8c, 0xf6, 0xa5, 0x5a, 0xde, 0xdb, 0x30, 0x27, 0x01, 0xe2, 0xb6, 0xbf, 0xed, 0x73, 0xd9,
	0x38, 0x7b, 0x6c, 0xe5, 0x6d, 0x3d, 0xfe, 0x81, 0x06, 0xf3, 0xb9, 0x53, 0xbf, 0x2e, 0x91, 0xbe,
	0xaf, 0x41, 0x79, 0x9d, 0xd2, 0xd6, 0x99, 0xac, 0x66, 0x1e, 0x16, 0x7a, 0x64, 0x11, 0x31, 0xa4,
	0x85, 0x2c, 0xa4, 0xca, 0x60, 0x54, 0xcc, 0x09, 0x46, 0xf8, 0x18, 0xa6, 0x32, 0xe0, 0xf8, 0xa9,
	0x91, 0x2c, 0xb8, 0xd6, 0x97, 0xe0, 0x05, 0xb5, 0xe0, 0x4b, 0x3c, 0xbb, 0x6d, 0x59, 0xe4, 0x94,
	0xc8, 0xac, 0xb8, 0xd4, 0xa5, 0x50, 0x6a, 0x1e, 0xeb, 0xc5, 0xee, 0xed, 0x41, 0x39, 0x41, 0x5b,
	0x73, 0xdd, 0x2d, 0xe2, 0x19, 0x56, 0x2b, 0xef, 0xbd, 0xd3, 0x25, 0x06, 0x73, 0xe2, 0xda, 0x09,
	0xc6, 0xf0, 0x77, 0x79, 0x7e, 0xab, 0xe0, 0xce, 0x28, 0xda, 0x82, 0x51, 0x12, 0xae, 0x1e, 0x1c,
	0x19, 0x37, 0x92, 0x7e, 0xa3, 0xc6, 0xb2, 0xdf, 0x9d, 0xc8, 0xd9, 0x13, 0xd7, 0x7d, 0x94, 0x90,
	0x3f, 0x18, 0xc3, 0x77, 0xe3, 0x2e, 0xcd, 0x15, 0xde, 0xeb, 0xb2, 0xf2, 0x49, 0xc2, 0x1b, 0xbb,
	0x73, 0x7c, 0xd3, 0x8d, 0xc5, 0x18, 0x2d, 0x2b, 0xc6, 0x64, 0x87, 0xa9, 0x05, 0x18, 0x35, 0xf8,
	0x9a, 0x51, 0x8c, 0xea, 0x3e, 0x52, 0xf3, 0xe1, 0x9d, 0x4e, 0x1b, 0x2f, 0x03, 0xda, 0x22, 0xad,
	0x10, 0x41, 0x2f, 0xc0, 0x97, 0x61, 0x22, 0x45, 0xcd, 0x28, 0x7e, 0x4b, 0xbc, 0xdb, 0x1d, 0x9e,
	0x90, 0x03, 0xa3, 0x1d, 0xa9, 0x90, 0x27, 0xa5, 0xfc, 0xc0, 0xcd, 0x5b, 0xef, 0x75, 0xe1, 0xfc,
	0x99, 0x13, 0x19, 0x45, 0x08, 0x06, 0x3a, 0x96, 0xc9, 0x02, 0xcb, 0x11, 0x7f, 0xf3, 0x34, 0x93,
	0x27, 0xfa, 0xdc, 0xe1, 0xf9, 0x0e, 0xaf, 0xb7, 0xac, 0xff, 0x93, 0xe0, 0xc9, 0xd5, 0xd9, 0x26,
	0x9e, 0x11, 0x3e, 0x15, 0x4b, 0x87, 0x0d, 0x1f, 0x16, 0xf1, 0xf5, 0x8f, 0xfd, 0x67, 0xc3, 0x34,
	0x34, 0x46, 0xf9, 0x8d, 0xbc, 0x41, 0x5c, 0xcf, 0x6a, 0x9e, 0x49, 0xe1, 0x48, 0xba, 0x91, 0x47,
	0x1f, 0x5f, 0x7e, 0x44, 0xfa, 0x38, 0x89, 0x6a, 0x5f, 0x7c, 0xea, 0xf1, 0x7e, 0x90, 0x44, 0x1b,
	0x7b, 0x2f, 0x4a, 0xa0, 0xd5, 0x61, 0x90, 0x35, 0x48, 0xe2, 0xf1, 0xd9, 0x1f, 0xc2, 0x4d, 0x61,
	0xe5, 0x0a, 0xee, 0x2f, 0x39, 0xfc, 0x7c, 0x0b, 0xa6, 0x15, 0xba, 0xef, 0x75, 0x8e, 0xc4, 0xb7,
	0xb5, 0xa0, 0xdc, 0xd6, 0xcf, 0xfd, 0xd2, 0x44, 0xc6, 0xd2, 0x5f, 0xef, 0xde, 0x7e, 0x14, 0xa4,
	0xb3, 0x1c, 0xdc, 0xc6, 0x59, 0x3f, 0xce, 0xf0, 0x12, 0xb7, 0xf6, 0xed, 0x20, 0x7b, 0x4d, 0x32,
	0x4f, 0x66, 0xaf, 0x5a, 0xea, 0x5a, 0xe3, 0xc2, 0x04, 0x57, 0xe5, 0xc6, 0xd9, 0xe1, 0x87, 0x4e,
	0xad, 0x45, 0xda, 0xc4, 0xf6, 0xbe, 0xf2, 0xec, 0xe7, 0x2d, 0xb8, 0x94, 0xe6, 0xc9, 0x68, 0xec,
	0x85, 0x5a, 0x53, 0xbc, 0x50, 0xe3, 0x37, 0x60, 0xca, 0x4f, 0xd2, 0xd9, 0xb6, 0xcd, 0xd7, 0xdf,
	0x68, 0x19, 0x8d, 0xa7, 0x61, 0x70, 0xcb, 0xac, 0xbf, 0xe2, 0x77, 0x40, 0xcf, 0x9a, 0x16, 0x71,
	0x3d, 0xe2, 0x63, 0x49, 0xae, 0x82, 0x10, 0xff, 0x86, 0x26, 0xe2, 0xaa, 0x7f, 0x1f, 0x68, 0x38,
	0xc7, 0xb6, 0xe5, 0x59, 0x8e, 0xbd, 0xe7, 0x3a, 0xa7, 0x96, 0xe9, 0xbf, 0x86, 0x4c, 0xc3, 0x90,
	0x41, 0x69, 0x3d, 0x28, 0xd7, 0x46, 0x7b, 0x63, 0x50, 0xba, 0x6d, 0x86, 0xaa, 0xe4, 0x8a, 0x1a,
	0x90, 0x55, 0xf9, 0x2a, 0x5c, 0x64, 0x1d, 0x4a, 0x1d, 0xd7, 0xab, 0xd3, 0x60, 0x2d, 0x56, 0x2e,
	0x56, 0x8a, 0x8b, 0xa3, 0xfb, 0xa5, 0xe0, 0x43, 0xc8, 0x83, 0xe1, 0x53, 0x11, 0xa1, 0x33, 0x21,
	0x30, 0x8a, 0x6e, 0xc1, 0xf9, 0x70, 0x99, 0x7a, 0x4a, 0x0f, 0xe7, 0xc2, 0x4f, 0x5c, 0xfc, 0x18,
	0x69, 0x64, 0xf8, 0x29, 0x52, 0x6e, 0xfa, 0xf8, 0x7b, 0x05, 0x15, 0xe3, 0x4d, 0xdf, 0x32, 0xb7,
	0xcd, 0x9e, 0xc2, 0x2b, 0x58, 0x15, 0xd4, 0xac, 0x42, 0x3d, 0x15, 0x93, 0x7a, 0x8a, 0xc5, 0x05,
	0xb9, 0x20, 0x11, 0xc5, 0x05, 0xee, 0xc7, 0x26, 0x39, 0xb5, 0x1a, 0x81, 0x4d, 0xca, 0x6f, 0x9c,
	0xe0, 0x7f, 0x10, 0x64, 0xdf, 0x08, 0xca, 0x82, 0x82, 0x68, 0xa8, 0xaf, 0xd2, 0xa8, 0x28, 0x0d,
	0x0a, 0xa3, 0xfd, 0x57, 0x0d, 0x74, 0xb5, 0x1a, 0xc4, 0x4d, 0x73, 0x1e, 0x46, 0x1c, 0x57, 0xe8,
	0xde, 0x89, 0xe9, 0x61, 0x58, 0x8c, 0xee, 0x88, 0xab, 0x4e, 0xe0, 0xd0, 0x75, 0x61, 0x0d, 0x52,
	0x10, 0x69, 0x84, 0xea, 0xf4, 0x23, 0xcd, 0x07, 0x1d, 0xc2, 0xbc, 0xba, 0x50, 0x45, 0x2c, 0xd2,
	0x88, 0xf1, 0x6d, 0x13, 0xcd, 0xc1, 0x70, 0x9b, 0x30, 0x16, 0x5e, 0x68, 0x22, 0x4e, 0xc1, 0x20,
	0xc2, 0x30, 0x4a, 0x9e, 0x35, 0x5a, 0x1d, 0x66, 0x9d, 0xc6, 0xdf, 0x7b, 0xbb, 0xc3, 0xb8, 0x2e,
	0x72, 0xf6, 0xec, 0x6d, 0x65, 0x14, 0xad, 0xc1, 0x80, 0xc8, 0x69, 0x35, 0xa1, 0xa8, 0xc5, 0xb8,
	0xa2, 0xb2, 0x35, 0xb1, 0x2f, 0x66, 0xe1, 0x3f, 0x2b, 0x44, 0xa7, 0x80, 0x44, 0xd7, 0x3d, 0xef,
	0xbe, 0x6a, 0xab, 0x89, 0xab, 0x5b, 0x7e, 0xb4, 0x93, 0xd4, 0x1d, 0x85, 0xd4, 0x41, 0xf9, 0x15,
	0x53, 0x0c, 0xa1, 0x3b, 0x70, 0xd1, 0x62, 0x75, 0x2e, 0xb4, 0x70, 0x27, 0x51, 0xd2, 0x16, 0x46,
	0x33, 0x12, 0x3d, 0xdd, 0x33, 0xd9, 0x5c, 0x62, 0xa0, 0x85, 0xe2, 0x86, 0x55, 0x5e, 0x25, 0x6e,
	0x02, 0xff, 0x52, 0x88, 0x8e, 0xe2, 0x94, 0x72, 0x7a, 0x3d, 0x37, 0xbc, 0x3c, 0x3b, 0x5a, 0x85,
	0xc9, 0x08, 0xb0, 0x4b, 0x18, 0x75, 0x6c, 0x46, 0x7c, 0x75, 0xcb, 0x66, 0x75, 0x89, 0x4a, 0x51,
	0x86, 0x93, 0x08, 0xb5, 0x57, 0xa1, 0x9c, 0x9e, 0xeb, 0x1f, 0x86, 0x31, 0x93, 0x9b, 0x4c, 0xce,
	0x0e, 0x84, 0x7c, 0x08, 0xb3, 0xe9, 0xf9, 0x26, 0x61, 0x0d, 0xd7, 0xa2, 0x5e, 0x58, 0xd8, 0x0e,
	0x17, 0x99, 0x4e, 0x2e, 0xb2, 0xd5, 0x25, 0xc4, 0xbf, 0x1c, 0xa5, 0x04, 0x0a, 0x3b, 0x63, 0x14,
	0x55, 0xf9, 0x65, 0x85, 0x85, 0x47, 0x4a, 0xea, 0xc2, 0x91, 0x39, 0x37, 0x98, 0xb5, 0xd4, 0x80,
	0xd1, 0xda, 0x6e, 0xd8, 0x6e, 0xa2, 0xc3, 0x64, 0x6d, 0xe7, 0xf1, 0xa3, 0xfa, 0x7e, 0x6d, 0xfd,
	0xfd, 0x9d, 0xf5, 0x47, 0xb5, 0xfa, 0xee, 0x5e, 0x6d, 0xbf, 0x7e, 0x50, 0x3b, 0x2c, 0x69, 0x68,
	0x06, 0xca, 0x8a, 0x6f, 0x8f, 0x77, 0xf8, 0xd7, 0x42, 0xc6, 0xd7, 0xfd, 0x1a, 0xff, 0x5a, 0x5c,
	0xfa, 0x08, 0x2e, 0xd4, 0xa2, 0x37, 0xfc, 0xa0, 0xb1, 0x65, 0x36, 0x4e, 0xbf, 0xfe, 0xf8, 0xf0,
	0x61, 0x7d, 0xef, 0xe1, 0xee, 0x4e, 0xad, 0x7e, 0xf8, 0xed, 0xbd, 0x5a, 0x49, 0x43, 0xd7, 0x61,
	0x21, 0x4e, 0x72, 0x50, 0xdb, 0x7c, 0xbc, 0xbf, 0x7d, 0xf8, 0x6d, 0x99, 0xac, 0x80, 0xe6, 0x61,
	0x3a, 0x4e, 0x76, 0x78, 0x28, 0x13, 0x14, 0x97, 0xaa, 0x7e, 0xd7, 0x91, 0x60, 0x3b, 0x05, 0x97,
	0x05, 0xb1, 0xe0, 0xc6, 0xbf, 0xd7, 0xb7, 0xb7, 0x36, 0xd7, 0xf7, 0xb7, 0x4a, 0x1a, 0xba, 0x02,
	0x13, 0x89, 0x4f, 0xef, 0xae, 0x6f, 0xd6, 0x4a, 0x85, 0xa5, 0x06, 0x8c, 0x09, 0xf0, 0x07, 0x7e,
	0x13, 0x0a, 0x82, 0x0b, 0x5d, 0xba, 0x9d, 0xdd, 0x1d, 0x0e, 0x75, 0x02, 0xc6, 0xbb, 0x63, 0x9b,
	0x0f, 0x6b, 0x9b, 0xef, 0x95, 0x0a, 0x71, 0xc2, 0xbd, 0xf5, 0x83, 0x83, 0x52, 0x31, 0xce, 0xe4,
	0xf1, 0xce, 0xfa, 0xde, 0xde, 0xfe, 0xee, 0x93, 0x5a, 0x69, 0x60, 0xe9, 0x16, 0x8c, 0xd6, 0x22,
	0x94, 0x29, 0x65, 0x76, 0x95, 0x53, 0xd2, 0xee, 0x3d, 0xbf, 0x0b, 0xe7, 0x62, 0x9e, 0xf9, 0xab,
	0x50, 0x4a, 0xf6, 0x2f, 0xa1, 0x85, 0x94, 0x19, 0x24, 0xdb, 0x9e, 0x74, 0xdc, 0x8b, 0x84, 0x51,
	0x3c, 0xfe, 0xc9, 0xf3, 0x2f, 0x8b, 0xda, 0xef, 0x3d, 0xff, 0xb2, 0xf8, 0xca, 0x67, 0xfc, 0x3f,
	0xe8, 0x2f, 0x35, 0x40, 0xe9, 0x6e, 0x22, 0x74, 0x35, 0xd1, 0x43, 0xa2, 0xea, 0x83, 0xd2, 0xaf,
	0xf5, 0x26, 0x62, 0x14, 0x6f, 0x71, 0x96, 0x05, 0xce, 0x72, 0xa4, 0xb3, 0x6a, 0xae, 0x7a, 0xab,
	0xcf, 0x56, 0x39, 0xeb, 0x95, 0x95, 0xce, 0x5a, 0xc7, 0x32, 0xab, 0x95, 0x15, 0x73, 0xcd, 0x62,
	0x15, 0x93, 0xb4, 0xaa, 0x95, 0x15, 0xb6, 0xc6, 0x57, 0xaa, 0x78, 0x67, 0x94, 0x54, 0x2b, 0x2b,
	0xcf, 0xd6, 0xf8, 0xc9, 0x57, 0x69, 0x39, 0xc7, 0x55, 0xf4, 0x43, 0x0d, 0x2e, 0xf9, 0x6f, 0xef,
	0xf1, 0xd7, 0x3e, 0x74, 0x3d, 0xef, 0x0d, 0x37, 0xaa, 0x23, 0xe8, 0x37, 0xfa, 0x21, 0x63, 0x14,
	0xbf, 0xcd, 0xd1, 0x16, 0x39, 0xda, 0xa1, 0xce, 0x2a, 0x5d, 0x65, 0x02, 0xeb, 0xd5, 0x08, 0x2b,
	0x5d, 0xbb, 0xbb, 0xea, 0xd8, 0xcb, 0xaf, 0xad, 0x3a, 0xcd, 0xa6, 0xc0, 0x1b, 0xbe, 0x17, 0x56,
	0xd1, 0xaf, 0xfb, 0x65, 0xa4, 0xf4, 0x0b, 0x3a, 0xba, 0x96, 0xcb, 0x3b, 0x78, 0x96, 0xd7, 0xaf,
	0xf7, 0x41, 0xc5, 0x28, 0x2e, 0x73, 0x80, 0x03, 0x1c, 0x60, 0xa1, 0x23, 0xc0, 0x0d, 0x07, 0xe0,
	0xd0, 0x6f, 0x6a, 0x51, 0xb5, 0x3b, 0xf5, 0x70, 0x39, 0xa5, 0xe8, 0x09, 0xf2, 0x2f, 0xed, 0xba,
	0x9e, 0xf5, 0x89, 0x51, 0x7c, 0x97, 0x33, 0x1b, 0xe4, 0xcc, 0x06, 0x3a, 0x81, 0x2e, 0xe6, 0x22,
	0x5d, 0xb0, 0x35, 0xbf, 0xe7, 0x70, 0xf5, 0xee, 0xf2, 0xbd, 0xe5, 0xfb, 0xcb, 0xaf, 0x2f, 0xbf,
	0xb1, 0xfc, 0x66, 0x15, 0xd1, 0xa8, 0x01, 0x26, 0x05, 0x62, 0x26, 0x65, 0xa0, 0x32, 0x8e, 0xd9,
	0x9c, 0xaf, 0xa1, 0xdc, 0x43, 0x2a, 0xb9, 0x7f, 0x5b, 0x03, 0xfd, 0x91, 0x63, 0x5a, 0xcd, 0xb3,
	0x97, 0x2e, 0xfa, 0x70, 0x9e, 0xe8, 0x5e, 0x27, 0x21, 0xfa, 0xc7, 0x51, 0x39, 0x54, 0xae, 0xaa,
	0x25, 0xb7, 0x5f, 0xdd, 0x51, 0x94, 0xdc, 0xfe, 0x8c, 0x26, 0x22, 0x5f, 0x0d, 0xa3, 0x2a, 0x35,
	0xfc, 0x9d, 0x06, 0x13, 0x8a, 0x66, 0x07, 0x94, 0xf6, 0xd2, 0x3e, 0xd8, 0x67, 0x75, 0x4d, 0xbc,
	0xc7, 0xd9, 0x43, 0xe0, 0xcc, 0xf6, 0xaa, 0xb7, 0x4a, 0x05, 0x88, 0xd7, 0x23, 0xcd, 0xd8, 0x6b,
	0x89, 0xf6, 0x81, 0x6a, 0x65, 0xc5, 0x5b, 0x53, 0xd4, 0x2f, 0xab, 0x15, 0xf4, 0xf7, 0x1a, 0x4c,
	0xaa, 0xab, 0xf8, 0xe8, 0x66, 0xa2, 0xa7, 0x25, 0xab, 0x8f, 0x41, 0x5f, 0xec, 0x8f, 0x90, 0x51,
	0xfc, 0x90, 0x43, 0x1f, 0x0b, 0x3c, 0x9b, 0x43, 0xe7, 0xc0, 0xef, 0xff, 0x14, 0xc0, 0xd1, 0x3f,
	0x6b, 0x30, 0x9d, 0xd3, 0x82, 0x80, 0x96, 0x53, 0xba, 0xcc, 0xe9, 0xc4, 0xd0, 0x57, 0x5e, 0x80,
	0x3a, 0x14, 0xe3, 0xdc, 0xcb, 0x10, 0xe3, 0x63, 0xf1, 0x2a, 0xd7, 0xcb, 0x5e, 0xd4, 0x8d, 0x14,
	0x49, 0x7b, 0xc9, 0x6a, 0x9e, 0x10, 0xe6, 0x7a, 0x5e, 0x65, 0xae, 0x7f, 0xa3, 0xc1, 0x6c, 0x6e,
	0xe3, 0x03, 0xba, 0x9d, 0x52, 0x4c, 0x6e, 0x13, 0x86, 0x7e, 0xe7, 0x85, 0xe8, 0x19, 0xc5, 0x8b,
	0x1c, 0xdc, 0x85, 0xc0, 0xc5, 0x7d, 0x45, 0x5e, 0x8e, 0x14, 0xe9, 0xad, 0x45, 0x9d, 0x1b, 0x55,
	0xf4, 0x87, 0x9a, 0xa8, 0x8a, 0xab, 0x5b, 0x0c, 0xd1, 0x52, 0xae, 0xeb, 0xc6, 0x7a, 0x18, 0xf5,
	0x57, 0xfb, 0xa6, 0x0d, 0xb5, 0x37, 0xae, 0xd2, 0xde, 0xf7, 0x35, 0x98, 0x50, 0x74, 0x65, 0x24,
	0x37, 0x4f, 0xdd, 0x34, 0x92, 0xdc, 0xbc, 0xac, 0xf6, 0x8e, 0x5b, 0x9c, 0x7d, 0x29, 0x16, 0x02,
	0x27, 0xa5, 0x10, 0xd8, 0xad, 0x84, 0x55, 0x11, 0x13, 0x29, 0x4b, 0x1c, 0xcb, 0x82, 0x52, 0xd4,
	0x18, 0x10, 0xdc, 0x8b, 0x24, 0x54, 0xc2, 0x45, 0x95, 0x12, 0x3e, 0x02, 0x94, 0x6e, 0xdc, 0x48,
	0xa6, 0x2e, 0xca, 0x7e, 0x10, 0xfd, 0x5a, 0x6f, 0x22, 0x46, 0xb1, 0xce, 0x59, 0x23, 0xc1, 0xda,
	0x8f, 0x73, 0xa3, 0x2b, 0x74, 0x2d, 0x90, 0xf8, 0xc7, 0x9a, 0xe8, 0x66, 0x49, 0x95, 0xc4, 0x50,
	0x3a, 0x90, 0xab, 0xea, 0x75, 0xfa, 0x8d, 0x7e, 0xc8, 0x18, 0xc5, 0x35, 0x8e, 0x61, 0x22, 0x8a,
	0xb8, 0xed, 0x20, 0xe2, 0xde, 0x96, 0x3c, 0x9e, 0x2f, 0x52, 0xad, 0xac, 0xb4, 0x15, 0xae, 0x1f,
	0xc1, 0xfd, 0x5c, 0x83, 0xcb, 0xca, 0x6a, 0x0e, 0x4a, 0x00, 0xc9, 0xaa, 0x40, 0xe9, 0x37, 0xfb,
	0xa2, 0x63, 0x14, 0xdf, 0xe1, 0x88, 0x2f, 0x05, 0x11, 0xca, 0x5b, 0x35, 0x05, 0x5e, 0x5d, 0x72,
	0x2c, 0xf1, 0x76, 0x28, 0x12, 0x3f, 0x7e, 0xef, 0xac, 0xa2, 0x7f, 0xd2, 0xe4, 0xf6, 0xa6, 0xbc,
	0x73, 0x20, 0xb3, 0xcb, 0x26, 0x79, 0x0e, 0xe4, 0x34, 0xb8, 0x1c, 0x70, 0x78, 0x97, 0x03, 0x85,
	0x5a, 0x51, 0x08, 0x5d, 0x8b, 0x00, 0x5a, 0x0a, 0x3d, 0x46, 0x4a, 0xce, 0x88, 0xa5, 0x9f, 0x24,
	0xac, 0x21, 0x2c, 0xc9, 0xe4, 0x59, 0x83, 0x54, 0xea, 0xc9, 0xb3, 0x06, 0xb9, 0xba, 0x83, 0xa7,
	0x38, 0xf8, 0x49, 0xc9, 0x19, 0x46, 0x42, 0xd8, 0x3c, 0x0d, 0x9a, 0x54, 0x37, 0xe1, 0x24, 0xb5,
	0x98, 0xd9, 0xdf, 0x93, 0xd4, 0x62, 0x76, 0x4f, 0x8f, 0x0f, 0xe4, 0x8a, 0x12, 0xc8, 0x07, 0x30,
	0x9e, 0xa8, 0xf6, 0xa0, 0x4a, 0xea, 0xb4, 0x48, 0x94, 0x8e, 0xf4, 0x85, 0x1e, 0x14, 0x21, 0xcb,
	0xb2, 0x92, 0xe5, 0x77, 0x61, 0x42, 0xd1, 0x0d, 0x91, 0x8c, 0x86, 0xea, 0xf6, 0x8d, 0x64, 0x34,
	0xcc, 0x68, 0xab, 0xf0, 0xd9, 0x4f, 0x29, 0xd9, 0x7f, 0xaa, 0x05, 0x0f, 0xbd, 0xaa, 0x7e, 0x86,
	0xe4, 0xf1, 0x90, 0xd7, 0x30, 0x91, 0x3c, 0x1e, 0x72, 0x9b, 0x24, 0x7c, 0x44, 0xba, 0x12, 0xd1,
	0x9f, 0x68, 0xe2, 0x39, 0x2b, 0xab, 0xc8, 0x9d, 0x4c, 0x51, 0xf2, 0x4b, 0xe9, 0xc9, 0x14, 0xa5,
	0x47, 0xf5, 0xdc, 0xc7, 0x35, 0xad, 0xc4, 0xf5, 0x23, 0x0d, 0xa6, 0x83, 0x56, 0x26, 0xf9, 0x7a,
	0x13, 0x36, 0x36, 0x25, 0x71, 0xe5, 0xf7, 0x57, 0x25, 0x71, 0xf5, 0xe8, 0x98, 0xc2, 0x37, 0x39,
	0xae, 0x99, 0xd8, 0x79, 0x76, 0x49, 0x3a, 0xcf, 0xba, 0x57, 0xb9, 0x4f, 0x35, 0x98, 0x51, 0x36,
	0x95, 0x84, 0x30, 0x17, 0xf3, 0x18, 0xcb, 0x0d, 0x28, 0xfa, 0xcc, 0xed, 0xe8, 0x17, 0x5f, 0xb7,
	0x0f, 0xde, 0xdb, 0xf0, 0x7f, 0xf1, 0x55, 0x6b, 0x53, 0xef, 0xac, 0xbe, 0xb7, 0xe1, 0x23, 0x9a,
	0x0d, 0x10, 0xd1, 0x04, 0x22, 0x2a, 0x21, 0x7a, 0x2e, 0xce, 0xfb, 0x54, 0x95, 0x3a, 0x7d, 0xde,
	0xab, 0xca, 0xe8, 0xe9, 0xf3, 0x5e, 0x59, 0xee, 0xc6, 0xdf, 0xe4, 0x68, 0xe6, 0x02, 0x34, 0xd1,
	0x2d, 0xbd, 0x12, 0xc0, 0x79, 0xc6, 0xff, 0x5f, 0x69, 0x59, 0xcc, 0xab, 0x2c, 0x32, 0xcf, 0xb5,
	0xec, 0xe3, 0xe5, 0x0a, 0xa3, 0x2d, 0xcb, 0xab, 0x1c, 0x9d, 0x55, 0x6e, 0x2e, 0xdf, 0xbc, 0x55,
	0xa9, 0xa2, 0xbf, 0xd5, 0x40, 0xcf, 0x6e, 0xc3, 0x42, 0x09, 0xeb, 0xce, 0x6d, 0xf6, 0xd2, 0x97,
	0xfb, 0x27, 0x66, 0x14, 0xbf, 0xc1, 0xb1, 0xcf, 0x4b, 0x36, 0x77, 0x2d, 0x40, 0x9e, 0x87, 0xb8,
	0x8a, 0xfe, 0x5a, 0x83, 0x72, 0x56, 0xbf, 0x3d, 0xba, 0xa5, 0x46, 0xa0, 0xe8, 0xe8, 0xd7, 0x97,
	0xfa, 0x25, 0x0d, 0xa1, 0x56, 0x5e, 0x18, 0xea, 0x67, 0x9a, 0x68, 0xb2, 0xcc, 0xa8, 0x7a, 0xa3,
	0x74, 0x62, 0x99, 0x5d, 0x58, 0xd7, 0x97, 0xfb, 0x27, 0x0e, 0xfd, 0x79, 0x21, 0xee, 0xcf, 0xbe,
	0x55, 0xf0, 0xc0, 0xab, 0x28, 0x70, 0x2a, 0xae, 0xbc, 0x8a, 0xaa, 0xbb, 0xe2, 0xca, 0xab, 0x2a,
	0x80, 0xfb, 0xec, 0xb1, 0x92, 0x7d, 0x70, 0xec, 0xa6, 0x6a, 0xc4, 0x28, 0x77, 0xe9, 0xe8, 0x55,
	0x5f, 0xbf, 0xd1, 0x0f, 0x59, 0x08, 0xe1, 0xaa, 0x12, 0xc2, 0x1f, 0x68, 0xc9, 0x32, 0xb5, 0x14,
	0x66, 0x6f, 0xf5, 0x94, 0x30, 0x8a, 0xb1, 0x4b, 0xfd, 0x92, 0x86, 0x70, 0xae, 0x65, 0x6d, 0x88,
	0xa2, 0xb2, 0xaa, 0x3c, 0x09, 0x53, 0x95, 0x5f, 0xe5, 0x49, 0x98, 0x2e, 0xd1, 0xfa, 0xec, 0xaf,
	0x2b, 0xd9, 0xff, 0x48, 0x83, 0x52, 0xb2, 0x56, 0x9a, 0xbc, 0x08, 0x28, 0xea, 0xb7, 0xc9, 0x8b,
	0x80, 0xaa, 0xdc, 0xea, 0xdf, 0x7c, 0x6f, 0x04, 0x89, 0x9b, 0xb1, 0x6a, 0x07, 0x99, 0xe5, 0xdd,
	0x6e, 0x88, 0x32, 0xe4, 0x3b, 0x89, 0x94, 0xb4, 0x99, 0xb1, 0x8c, 0xae, 0x8a, 0xfe, 0xb1, 0x7b,
	0x9d, 0x4b, 0xff, 0x22, 0x2f, 0xe3, 0x3a, 0xa7, 0xfc, 0x6d, 0x61, 0xc6, 0x75, 0x4e, 0xfd, 0x33,
	0x3f, 0xfc, 0x80, 0x0b, 0x70, 0x33, 0x7a, 0x5b, 0x3c, 0x12, 0xf0, 0xef, 0x75, 0xe1, 0xd3, 0xb5,
	0xf0, 0xc7, 0x85, 0xa1, 0x04, 0x47, 0x6b, 0x8a, 0x9f, 0x1b, 0x56, 0xd1, 0xf7, 0xa2, 0x5e, 0xd4,
	0x64, 0x85, 0x38, 0x99, 0xea, 0x65, 0x96, 0x9f, 0x93, 0xa9, 0x5e, 0x76, 0xc1, 0xd9, 0xbf, 0x05,
	0x2d, 0x8a, 0xed, 0xb6, 0x83, 0x5b, 0x90, 0x5d, 0xf1, 0x55, 0x8b, 0x7e, 0xe8, 0x07, 0xa5, 0x8c,
	0x42, 0xaf, 0x22, 0x28, 0x65, 0x57, 0xa5, 0x15, 0x41, 0x29, 0xa7, 0x7e, 0x8c, 0x6f, 0x70, 0x54,
	0xb7, 0xc4, 0x61, 0x65, 0xac, 0xfa, 0x66, 0x38, 0xb1, 0x62, 0x54, 0xd6, 0x0c, 0x4a, 0x85, 0x32,
	0x43, 0x8b, 0xfc, 0x0b, 0x4d, 0x55, 0xd8, 0x8b, 0x4a, 0x87, 0xa8, 0x27, 0x57, 0xb9, 0x78, 0xac,
	0xc8, 0x84, 0xf2, 0x6a, 0x92, 0x3e, 0xc8, 0xa5, 0xde, 0x20, 0x3f, 0xd7, 0x72, 0x0a, 0x6c, 0xb7,
	0xfa, 0xac, 0x00, 0x65, 0x06, 0x11, 0x65, 0xa1, 0xc9, 0xc7, 0xf6, 0x6a, 0x4f, 0x6c, 0xfa, 0xd0,
	0xef, 0x3c, 0xff, 0xb2, 0xf8, 0xf9, 0x77, 0x36, 0x4a, 0x3f, 0xf9, 0x62, 0x4e, 0xfb, 0xb7, 0x2f,
	0xe6, 0xb4, 0xff, 0xfc, 0x62, 0x4e, 0xfb, 0xa3, 0xff, 0x9a, 0x7b, 0xe5, 0x7f, 0x03, 0x00, 0x00,
	0xff, 0xff, 0xe4, 0xca, 0x8f, 0xaa, 0x0a, 0x3f, 0x00, 0x00,
}
