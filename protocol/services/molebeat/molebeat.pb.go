// Code generated by protoc-gen-go. DO NOT EDIT.
// source: molebeat/molebeat.proto

package molebeat // import "golang.52tt.com/protocol/services/molebeat"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type MoleGameType int32

const (
	MoleGameType_NewYearGame   MoleGameType = 0
	MoleGameType_SeptemberGame MoleGameType = 1
)

var MoleGameType_name = map[int32]string{
	0: "NewYearGame",
	1: "SeptemberGame",
}
var MoleGameType_value = map[string]int32{
	"NewYearGame":   0,
	"SeptemberGame": 1,
}

func (x MoleGameType) String() string {
	return proto.EnumName(MoleGameType_name, int32(x))
}
func (MoleGameType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{0}
}

type GetMoleAttackGameConfigReq_ENV_PROD_STAGING_E int32

const (
	GetMoleAttackGameConfigReq_ENV_DEFAULT GetMoleAttackGameConfigReq_ENV_PROD_STAGING_E = 0
	GetMoleAttackGameConfigReq_ENV_PROD    GetMoleAttackGameConfigReq_ENV_PROD_STAGING_E = 1
	GetMoleAttackGameConfigReq_ENV_STAGING GetMoleAttackGameConfigReq_ENV_PROD_STAGING_E = 2
	GetMoleAttackGameConfigReq_ENV_TESTING GetMoleAttackGameConfigReq_ENV_PROD_STAGING_E = 3
)

var GetMoleAttackGameConfigReq_ENV_PROD_STAGING_E_name = map[int32]string{
	0: "ENV_DEFAULT",
	1: "ENV_PROD",
	2: "ENV_STAGING",
	3: "ENV_TESTING",
}
var GetMoleAttackGameConfigReq_ENV_PROD_STAGING_E_value = map[string]int32{
	"ENV_DEFAULT": 0,
	"ENV_PROD":    1,
	"ENV_STAGING": 2,
	"ENV_TESTING": 3,
}

func (x GetMoleAttackGameConfigReq_ENV_PROD_STAGING_E) String() string {
	return proto.EnumName(GetMoleAttackGameConfigReq_ENV_PROD_STAGING_E_name, int32(x))
}
func (GetMoleAttackGameConfigReq_ENV_PROD_STAGING_E) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{11, 0}
}

type MoleBeatReportReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Chid                 uint32   `protobuf:"varint,2,opt,name=chid,proto3" json:"chid,omitempty"`
	BeatCnt              uint32   `protobuf:"varint,3,opt,name=beat_cnt,json=beatCnt,proto3" json:"beat_cnt,omitempty"`
	MoleGameType         uint32   `protobuf:"varint,4,opt,name=mole_game_type,json=moleGameType,proto3" json:"mole_game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MoleBeatReportReq) Reset()         { *m = MoleBeatReportReq{} }
func (m *MoleBeatReportReq) String() string { return proto.CompactTextString(m) }
func (*MoleBeatReportReq) ProtoMessage()    {}
func (*MoleBeatReportReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{0}
}
func (m *MoleBeatReportReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoleBeatReportReq.Unmarshal(m, b)
}
func (m *MoleBeatReportReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoleBeatReportReq.Marshal(b, m, deterministic)
}
func (dst *MoleBeatReportReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoleBeatReportReq.Merge(dst, src)
}
func (m *MoleBeatReportReq) XXX_Size() int {
	return xxx_messageInfo_MoleBeatReportReq.Size(m)
}
func (m *MoleBeatReportReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MoleBeatReportReq.DiscardUnknown(m)
}

var xxx_messageInfo_MoleBeatReportReq proto.InternalMessageInfo

func (m *MoleBeatReportReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MoleBeatReportReq) GetChid() uint32 {
	if m != nil {
		return m.Chid
	}
	return 0
}

func (m *MoleBeatReportReq) GetBeatCnt() uint32 {
	if m != nil {
		return m.BeatCnt
	}
	return 0
}

func (m *MoleBeatReportReq) GetMoleGameType() uint32 {
	if m != nil {
		return m.MoleGameType
	}
	return 0
}

type MoleBeatReportRsp struct {
	AwardName            string   `protobuf:"bytes,1,opt,name=award_name,json=awardName,proto3" json:"award_name,omitempty"`
	AwardUrl             string   `protobuf:"bytes,2,opt,name=award_url,json=awardUrl,proto3" json:"award_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MoleBeatReportRsp) Reset()         { *m = MoleBeatReportRsp{} }
func (m *MoleBeatReportRsp) String() string { return proto.CompactTextString(m) }
func (*MoleBeatReportRsp) ProtoMessage()    {}
func (*MoleBeatReportRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{1}
}
func (m *MoleBeatReportRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoleBeatReportRsp.Unmarshal(m, b)
}
func (m *MoleBeatReportRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoleBeatReportRsp.Marshal(b, m, deterministic)
}
func (dst *MoleBeatReportRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoleBeatReportRsp.Merge(dst, src)
}
func (m *MoleBeatReportRsp) XXX_Size() int {
	return xxx_messageInfo_MoleBeatReportRsp.Size(m)
}
func (m *MoleBeatReportRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_MoleBeatReportRsp.DiscardUnknown(m)
}

var xxx_messageInfo_MoleBeatReportRsp proto.InternalMessageInfo

func (m *MoleBeatReportRsp) GetAwardName() string {
	if m != nil {
		return m.AwardName
	}
	return ""
}

func (m *MoleBeatReportRsp) GetAwardUrl() string {
	if m != nil {
		return m.AwardUrl
	}
	return ""
}

type GetUserTotalBeatCntReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserTotalBeatCntReq) Reset()         { *m = GetUserTotalBeatCntReq{} }
func (m *GetUserTotalBeatCntReq) String() string { return proto.CompactTextString(m) }
func (*GetUserTotalBeatCntReq) ProtoMessage()    {}
func (*GetUserTotalBeatCntReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{2}
}
func (m *GetUserTotalBeatCntReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTotalBeatCntReq.Unmarshal(m, b)
}
func (m *GetUserTotalBeatCntReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTotalBeatCntReq.Marshal(b, m, deterministic)
}
func (dst *GetUserTotalBeatCntReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTotalBeatCntReq.Merge(dst, src)
}
func (m *GetUserTotalBeatCntReq) XXX_Size() int {
	return xxx_messageInfo_GetUserTotalBeatCntReq.Size(m)
}
func (m *GetUserTotalBeatCntReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTotalBeatCntReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTotalBeatCntReq proto.InternalMessageInfo

type GetUserTotalBeatCntResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserTotalBeatCntResp) Reset()         { *m = GetUserTotalBeatCntResp{} }
func (m *GetUserTotalBeatCntResp) String() string { return proto.CompactTextString(m) }
func (*GetUserTotalBeatCntResp) ProtoMessage()    {}
func (*GetUserTotalBeatCntResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{3}
}
func (m *GetUserTotalBeatCntResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTotalBeatCntResp.Unmarshal(m, b)
}
func (m *GetUserTotalBeatCntResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTotalBeatCntResp.Marshal(b, m, deterministic)
}
func (dst *GetUserTotalBeatCntResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTotalBeatCntResp.Merge(dst, src)
}
func (m *GetUserTotalBeatCntResp) XXX_Size() int {
	return xxx_messageInfo_GetUserTotalBeatCntResp.Size(m)
}
func (m *GetUserTotalBeatCntResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTotalBeatCntResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTotalBeatCntResp proto.InternalMessageInfo

type StartMoleBeatReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartMoleBeatReq) Reset()         { *m = StartMoleBeatReq{} }
func (m *StartMoleBeatReq) String() string { return proto.CompactTextString(m) }
func (*StartMoleBeatReq) ProtoMessage()    {}
func (*StartMoleBeatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{4}
}
func (m *StartMoleBeatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartMoleBeatReq.Unmarshal(m, b)
}
func (m *StartMoleBeatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartMoleBeatReq.Marshal(b, m, deterministic)
}
func (dst *StartMoleBeatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartMoleBeatReq.Merge(dst, src)
}
func (m *StartMoleBeatReq) XXX_Size() int {
	return xxx_messageInfo_StartMoleBeatReq.Size(m)
}
func (m *StartMoleBeatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartMoleBeatReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartMoleBeatReq proto.InternalMessageInfo

type StartMoleBeatResp struct {
	UpdateTime           uint32   `protobuf:"varint,1,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	IsOpen               bool     `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartMoleBeatResp) Reset()         { *m = StartMoleBeatResp{} }
func (m *StartMoleBeatResp) String() string { return proto.CompactTextString(m) }
func (*StartMoleBeatResp) ProtoMessage()    {}
func (*StartMoleBeatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{5}
}
func (m *StartMoleBeatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartMoleBeatResp.Unmarshal(m, b)
}
func (m *StartMoleBeatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartMoleBeatResp.Marshal(b, m, deterministic)
}
func (dst *StartMoleBeatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartMoleBeatResp.Merge(dst, src)
}
func (m *StartMoleBeatResp) XXX_Size() int {
	return xxx_messageInfo_StartMoleBeatResp.Size(m)
}
func (m *StartMoleBeatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartMoleBeatResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartMoleBeatResp proto.InternalMessageInfo

func (m *StartMoleBeatResp) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *StartMoleBeatResp) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

type StopMoleBeatReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopMoleBeatReq) Reset()         { *m = StopMoleBeatReq{} }
func (m *StopMoleBeatReq) String() string { return proto.CompactTextString(m) }
func (*StopMoleBeatReq) ProtoMessage()    {}
func (*StopMoleBeatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{6}
}
func (m *StopMoleBeatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopMoleBeatReq.Unmarshal(m, b)
}
func (m *StopMoleBeatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopMoleBeatReq.Marshal(b, m, deterministic)
}
func (dst *StopMoleBeatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopMoleBeatReq.Merge(dst, src)
}
func (m *StopMoleBeatReq) XXX_Size() int {
	return xxx_messageInfo_StopMoleBeatReq.Size(m)
}
func (m *StopMoleBeatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StopMoleBeatReq.DiscardUnknown(m)
}

var xxx_messageInfo_StopMoleBeatReq proto.InternalMessageInfo

type StopMoleBeatResp struct {
	UpdateTime           uint32   `protobuf:"varint,1,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	IsOpen               bool     `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopMoleBeatResp) Reset()         { *m = StopMoleBeatResp{} }
func (m *StopMoleBeatResp) String() string { return proto.CompactTextString(m) }
func (*StopMoleBeatResp) ProtoMessage()    {}
func (*StopMoleBeatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{7}
}
func (m *StopMoleBeatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopMoleBeatResp.Unmarshal(m, b)
}
func (m *StopMoleBeatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopMoleBeatResp.Marshal(b, m, deterministic)
}
func (dst *StopMoleBeatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopMoleBeatResp.Merge(dst, src)
}
func (m *StopMoleBeatResp) XXX_Size() int {
	return xxx_messageInfo_StopMoleBeatResp.Size(m)
}
func (m *StopMoleBeatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StopMoleBeatResp.DiscardUnknown(m)
}

var xxx_messageInfo_StopMoleBeatResp proto.InternalMessageInfo

func (m *StopMoleBeatResp) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *StopMoleBeatResp) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

type GetBeatDataReq struct {
	GameTime             uint32   `protobuf:"varint,1,opt,name=game_time,json=gameTime,proto3" json:"game_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBeatDataReq) Reset()         { *m = GetBeatDataReq{} }
func (m *GetBeatDataReq) String() string { return proto.CompactTextString(m) }
func (*GetBeatDataReq) ProtoMessage()    {}
func (*GetBeatDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{8}
}
func (m *GetBeatDataReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBeatDataReq.Unmarshal(m, b)
}
func (m *GetBeatDataReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBeatDataReq.Marshal(b, m, deterministic)
}
func (dst *GetBeatDataReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBeatDataReq.Merge(dst, src)
}
func (m *GetBeatDataReq) XXX_Size() int {
	return xxx_messageInfo_GetBeatDataReq.Size(m)
}
func (m *GetBeatDataReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBeatDataReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBeatDataReq proto.InternalMessageInfo

func (m *GetBeatDataReq) GetGameTime() uint32 {
	if m != nil {
		return m.GameTime
	}
	return 0
}

type GetBeatDataResp struct {
	DataInfo             []string `protobuf:"bytes,1,rep,name=data_info,json=dataInfo,proto3" json:"data_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBeatDataResp) Reset()         { *m = GetBeatDataResp{} }
func (m *GetBeatDataResp) String() string { return proto.CompactTextString(m) }
func (*GetBeatDataResp) ProtoMessage()    {}
func (*GetBeatDataResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{9}
}
func (m *GetBeatDataResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBeatDataResp.Unmarshal(m, b)
}
func (m *GetBeatDataResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBeatDataResp.Marshal(b, m, deterministic)
}
func (dst *GetBeatDataResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBeatDataResp.Merge(dst, src)
}
func (m *GetBeatDataResp) XXX_Size() int {
	return xxx_messageInfo_GetBeatDataResp.Size(m)
}
func (m *GetBeatDataResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBeatDataResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBeatDataResp proto.InternalMessageInfo

func (m *GetBeatDataResp) GetDataInfo() []string {
	if m != nil {
		return m.DataInfo
	}
	return nil
}

type MoleAttackGamePeriodInfo struct {
	PeriodBeginTs        uint32   `protobuf:"varint,1,opt,name=period_begin_ts,json=periodBeginTs,proto3" json:"period_begin_ts,omitempty"`
	PeriodFinishTs       uint32   `protobuf:"varint,2,opt,name=period_finish_ts,json=periodFinishTs,proto3" json:"period_finish_ts,omitempty"`
	PeriodIntervalTs     uint32   `protobuf:"varint,3,opt,name=period_interval_ts,json=periodIntervalTs,proto3" json:"period_interval_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MoleAttackGamePeriodInfo) Reset()         { *m = MoleAttackGamePeriodInfo{} }
func (m *MoleAttackGamePeriodInfo) String() string { return proto.CompactTextString(m) }
func (*MoleAttackGamePeriodInfo) ProtoMessage()    {}
func (*MoleAttackGamePeriodInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{10}
}
func (m *MoleAttackGamePeriodInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoleAttackGamePeriodInfo.Unmarshal(m, b)
}
func (m *MoleAttackGamePeriodInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoleAttackGamePeriodInfo.Marshal(b, m, deterministic)
}
func (dst *MoleAttackGamePeriodInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoleAttackGamePeriodInfo.Merge(dst, src)
}
func (m *MoleAttackGamePeriodInfo) XXX_Size() int {
	return xxx_messageInfo_MoleAttackGamePeriodInfo.Size(m)
}
func (m *MoleAttackGamePeriodInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MoleAttackGamePeriodInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MoleAttackGamePeriodInfo proto.InternalMessageInfo

func (m *MoleAttackGamePeriodInfo) GetPeriodBeginTs() uint32 {
	if m != nil {
		return m.PeriodBeginTs
	}
	return 0
}

func (m *MoleAttackGamePeriodInfo) GetPeriodFinishTs() uint32 {
	if m != nil {
		return m.PeriodFinishTs
	}
	return 0
}

func (m *MoleAttackGamePeriodInfo) GetPeriodIntervalTs() uint32 {
	if m != nil {
		return m.PeriodIntervalTs
	}
	return 0
}

type GetMoleAttackGameConfigReq struct {
	Env                  uint32   `protobuf:"varint,1,opt,name=env,proto3" json:"env,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMoleAttackGameConfigReq) Reset()         { *m = GetMoleAttackGameConfigReq{} }
func (m *GetMoleAttackGameConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetMoleAttackGameConfigReq) ProtoMessage()    {}
func (*GetMoleAttackGameConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{11}
}
func (m *GetMoleAttackGameConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMoleAttackGameConfigReq.Unmarshal(m, b)
}
func (m *GetMoleAttackGameConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMoleAttackGameConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetMoleAttackGameConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMoleAttackGameConfigReq.Merge(dst, src)
}
func (m *GetMoleAttackGameConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetMoleAttackGameConfigReq.Size(m)
}
func (m *GetMoleAttackGameConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMoleAttackGameConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMoleAttackGameConfigReq proto.InternalMessageInfo

func (m *GetMoleAttackGameConfigReq) GetEnv() uint32 {
	if m != nil {
		return m.Env
	}
	return 0
}

func (m *GetMoleAttackGameConfigReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetMoleAttackGameConfigRsp struct {
	ConfigLastChangeTs             uint32                      `protobuf:"varint,1,opt,name=config_last_change_ts,json=configLastChangeTs,proto3" json:"config_last_change_ts,omitempty"`
	GameName                       string                      `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	GameBeginTs                    uint32                      `protobuf:"varint,3,opt,name=game_begin_ts,json=gameBeginTs,proto3" json:"game_begin_ts,omitempty"`
	GameFinishTs                   uint32                      `protobuf:"varint,4,opt,name=game_finish_ts,json=gameFinishTs,proto3" json:"game_finish_ts,omitempty"`
	PeriodInfoList                 []*MoleAttackGamePeriodInfo `protobuf:"bytes,5,rep,name=period_info_list,json=periodInfoList,proto3" json:"period_info_list,omitempty"`
	RandomGamestartDelaySecond     uint32                      `protobuf:"varint,6,opt,name=random_gamestart_delay_second,json=randomGamestartDelaySecond,proto3" json:"random_gamestart_delay_second,omitempty"`
	RandomGamefinReportDelaySecond uint32                      `protobuf:"varint,7,opt,name=random_gamefin_report_delay_second,json=randomGamefinReportDelaySecond,proto3" json:"random_gamefin_report_delay_second,omitempty"`
	GamePrepareSecond              uint32                      `protobuf:"varint,8,opt,name=game_prepare_second,json=gamePrepareSecond,proto3" json:"game_prepare_second,omitempty"`
	GameDurationSecond             uint32                      `protobuf:"varint,9,opt,name=game_duration_second,json=gameDurationSecond,proto3" json:"game_duration_second,omitempty"`
	MoleAppearCnt                  uint32                      `protobuf:"varint,10,opt,name=mole_appear_cnt,json=moleAppearCnt,proto3" json:"mole_appear_cnt,omitempty"`
	AttackAtleastCnt               uint32                      `protobuf:"varint,11,opt,name=attack_atleast_cnt,json=attackAtleastCnt,proto3" json:"attack_atleast_cnt,omitempty"`
	NewYearTime                    int64                       `protobuf:"varint,12,opt,name=new_year_time,json=newYearTime,proto3" json:"new_year_time,omitempty"`
	NeweYearSecond                 uint32                      `protobuf:"varint,13,opt,name=newe_year_second,json=neweYearSecond,proto3" json:"newe_year_second,omitempty"`
	MoleGameType                   uint32                      `protobuf:"varint,14,opt,name=mole_game_type,json=moleGameType,proto3" json:"mole_game_type,omitempty"`
	WhiteUids                      []uint32                    `protobuf:"varint,15,rep,packed,name=white_uids,json=whiteUids,proto3" json:"white_uids,omitempty"`
	WhiteOpen                      bool                        `protobuf:"varint,16,opt,name=white_open,json=whiteOpen,proto3" json:"white_open,omitempty"`
	UiRes                          *UiRes                      `protobuf:"bytes,17,opt,name=ui_res,json=uiRes,proto3" json:"ui_res,omitempty"`
	XXX_NoUnkeyedLiteral           struct{}                    `json:"-"`
	XXX_unrecognized               []byte                      `json:"-"`
	XXX_sizecache                  int32                       `json:"-"`
}

func (m *GetMoleAttackGameConfigRsp) Reset()         { *m = GetMoleAttackGameConfigRsp{} }
func (m *GetMoleAttackGameConfigRsp) String() string { return proto.CompactTextString(m) }
func (*GetMoleAttackGameConfigRsp) ProtoMessage()    {}
func (*GetMoleAttackGameConfigRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{12}
}
func (m *GetMoleAttackGameConfigRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMoleAttackGameConfigRsp.Unmarshal(m, b)
}
func (m *GetMoleAttackGameConfigRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMoleAttackGameConfigRsp.Marshal(b, m, deterministic)
}
func (dst *GetMoleAttackGameConfigRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMoleAttackGameConfigRsp.Merge(dst, src)
}
func (m *GetMoleAttackGameConfigRsp) XXX_Size() int {
	return xxx_messageInfo_GetMoleAttackGameConfigRsp.Size(m)
}
func (m *GetMoleAttackGameConfigRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMoleAttackGameConfigRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMoleAttackGameConfigRsp proto.InternalMessageInfo

func (m *GetMoleAttackGameConfigRsp) GetConfigLastChangeTs() uint32 {
	if m != nil {
		return m.ConfigLastChangeTs
	}
	return 0
}

func (m *GetMoleAttackGameConfigRsp) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GetMoleAttackGameConfigRsp) GetGameBeginTs() uint32 {
	if m != nil {
		return m.GameBeginTs
	}
	return 0
}

func (m *GetMoleAttackGameConfigRsp) GetGameFinishTs() uint32 {
	if m != nil {
		return m.GameFinishTs
	}
	return 0
}

func (m *GetMoleAttackGameConfigRsp) GetPeriodInfoList() []*MoleAttackGamePeriodInfo {
	if m != nil {
		return m.PeriodInfoList
	}
	return nil
}

func (m *GetMoleAttackGameConfigRsp) GetRandomGamestartDelaySecond() uint32 {
	if m != nil {
		return m.RandomGamestartDelaySecond
	}
	return 0
}

func (m *GetMoleAttackGameConfigRsp) GetRandomGamefinReportDelaySecond() uint32 {
	if m != nil {
		return m.RandomGamefinReportDelaySecond
	}
	return 0
}

func (m *GetMoleAttackGameConfigRsp) GetGamePrepareSecond() uint32 {
	if m != nil {
		return m.GamePrepareSecond
	}
	return 0
}

func (m *GetMoleAttackGameConfigRsp) GetGameDurationSecond() uint32 {
	if m != nil {
		return m.GameDurationSecond
	}
	return 0
}

func (m *GetMoleAttackGameConfigRsp) GetMoleAppearCnt() uint32 {
	if m != nil {
		return m.MoleAppearCnt
	}
	return 0
}

func (m *GetMoleAttackGameConfigRsp) GetAttackAtleastCnt() uint32 {
	if m != nil {
		return m.AttackAtleastCnt
	}
	return 0
}

func (m *GetMoleAttackGameConfigRsp) GetNewYearTime() int64 {
	if m != nil {
		return m.NewYearTime
	}
	return 0
}

func (m *GetMoleAttackGameConfigRsp) GetNeweYearSecond() uint32 {
	if m != nil {
		return m.NeweYearSecond
	}
	return 0
}

func (m *GetMoleAttackGameConfigRsp) GetMoleGameType() uint32 {
	if m != nil {
		return m.MoleGameType
	}
	return 0
}

func (m *GetMoleAttackGameConfigRsp) GetWhiteUids() []uint32 {
	if m != nil {
		return m.WhiteUids
	}
	return nil
}

func (m *GetMoleAttackGameConfigRsp) GetWhiteOpen() bool {
	if m != nil {
		return m.WhiteOpen
	}
	return false
}

func (m *GetMoleAttackGameConfigRsp) GetUiRes() *UiRes {
	if m != nil {
		return m.UiRes
	}
	return nil
}

type UiRes struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Md5                  string   `protobuf:"bytes,2,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UiRes) Reset()         { *m = UiRes{} }
func (m *UiRes) String() string { return proto.CompactTextString(m) }
func (*UiRes) ProtoMessage()    {}
func (*UiRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{13}
}
func (m *UiRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UiRes.Unmarshal(m, b)
}
func (m *UiRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UiRes.Marshal(b, m, deterministic)
}
func (dst *UiRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UiRes.Merge(dst, src)
}
func (m *UiRes) XXX_Size() int {
	return xxx_messageInfo_UiRes.Size(m)
}
func (m *UiRes) XXX_DiscardUnknown() {
	xxx_messageInfo_UiRes.DiscardUnknown(m)
}

var xxx_messageInfo_UiRes proto.InternalMessageInfo

func (m *UiRes) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *UiRes) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

type MqInfo struct {
	Msg                  []byte   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	MsgType              uint32   `protobuf:"varint,2,opt,name=msg_type,json=msgType,proto3" json:"msg_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MqInfo) Reset()         { *m = MqInfo{} }
func (m *MqInfo) String() string { return proto.CompactTextString(m) }
func (*MqInfo) ProtoMessage()    {}
func (*MqInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{14}
}
func (m *MqInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MqInfo.Unmarshal(m, b)
}
func (m *MqInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MqInfo.Marshal(b, m, deterministic)
}
func (dst *MqInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MqInfo.Merge(dst, src)
}
func (m *MqInfo) XXX_Size() int {
	return xxx_messageInfo_MqInfo.Size(m)
}
func (m *MqInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MqInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MqInfo proto.InternalMessageInfo

func (m *MqInfo) GetMsg() []byte {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *MqInfo) GetMsgType() uint32 {
	if m != nil {
		return m.MsgType
	}
	return 0
}

// 打年兽活动的奖励
type BeatAward struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	AwardName            string   `protobuf:"bytes,3,opt,name=award_name,json=awardName,proto3" json:"award_name,omitempty"`
	AwardUrl             string   `protobuf:"bytes,4,opt,name=award_url,json=awardUrl,proto3" json:"award_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BeatAward) Reset()         { *m = BeatAward{} }
func (m *BeatAward) String() string { return proto.CompactTextString(m) }
func (*BeatAward) ProtoMessage()    {}
func (*BeatAward) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{15}
}
func (m *BeatAward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BeatAward.Unmarshal(m, b)
}
func (m *BeatAward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BeatAward.Marshal(b, m, deterministic)
}
func (dst *BeatAward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BeatAward.Merge(dst, src)
}
func (m *BeatAward) XXX_Size() int {
	return xxx_messageInfo_BeatAward.Size(m)
}
func (m *BeatAward) XXX_DiscardUnknown() {
	xxx_messageInfo_BeatAward.DiscardUnknown(m)
}

var xxx_messageInfo_BeatAward proto.InternalMessageInfo

func (m *BeatAward) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BeatAward) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BeatAward) GetAwardName() string {
	if m != nil {
		return m.AwardName
	}
	return ""
}

func (m *BeatAward) GetAwardUrl() string {
	if m != nil {
		return m.AwardUrl
	}
	return ""
}

type AllBroadCast struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Gameid               uint32   `protobuf:"varint,3,opt,name=gameid,proto3" json:"gameid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AllBroadCast) Reset()         { *m = AllBroadCast{} }
func (m *AllBroadCast) String() string { return proto.CompactTextString(m) }
func (*AllBroadCast) ProtoMessage()    {}
func (*AllBroadCast) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{16}
}
func (m *AllBroadCast) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AllBroadCast.Unmarshal(m, b)
}
func (m *AllBroadCast) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AllBroadCast.Marshal(b, m, deterministic)
}
func (dst *AllBroadCast) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllBroadCast.Merge(dst, src)
}
func (m *AllBroadCast) XXX_Size() int {
	return xxx_messageInfo_AllBroadCast.Size(m)
}
func (m *AllBroadCast) XXX_DiscardUnknown() {
	xxx_messageInfo_AllBroadCast.DiscardUnknown(m)
}

var xxx_messageInfo_AllBroadCast proto.InternalMessageInfo

func (m *AllBroadCast) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AllBroadCast) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AllBroadCast) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

// web榜单
type BeatSumInfo struct {
	BeatSum              uint32   `protobuf:"varint,1,opt,name=beat_sum,json=beatSum,proto3" json:"beat_sum,omitempty"`
	AwardSum             uint32   `protobuf:"varint,2,opt,name=award_sum,json=awardSum,proto3" json:"award_sum,omitempty"`
	Ranking              uint32   `protobuf:"varint,3,opt,name=ranking,proto3" json:"ranking,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BeatSumInfo) Reset()         { *m = BeatSumInfo{} }
func (m *BeatSumInfo) String() string { return proto.CompactTextString(m) }
func (*BeatSumInfo) ProtoMessage()    {}
func (*BeatSumInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{17}
}
func (m *BeatSumInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BeatSumInfo.Unmarshal(m, b)
}
func (m *BeatSumInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BeatSumInfo.Marshal(b, m, deterministic)
}
func (dst *BeatSumInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BeatSumInfo.Merge(dst, src)
}
func (m *BeatSumInfo) XXX_Size() int {
	return xxx_messageInfo_BeatSumInfo.Size(m)
}
func (m *BeatSumInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BeatSumInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BeatSumInfo proto.InternalMessageInfo

func (m *BeatSumInfo) GetBeatSum() uint32 {
	if m != nil {
		return m.BeatSum
	}
	return 0
}

func (m *BeatSumInfo) GetAwardSum() uint32 {
	if m != nil {
		return m.AwardSum
	}
	return 0
}

func (m *BeatSumInfo) GetRanking() uint32 {
	if m != nil {
		return m.Ranking
	}
	return 0
}

type UserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Roomid               uint32   `protobuf:"varint,4,opt,name=roomid,proto3" json:"roomid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfo) Reset()         { *m = UserInfo{} }
func (m *UserInfo) String() string { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()    {}
func (*UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{18}
}
func (m *UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo.Unmarshal(m, b)
}
func (m *UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo.Marshal(b, m, deterministic)
}
func (dst *UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo.Merge(dst, src)
}
func (m *UserInfo) XXX_Size() int {
	return xxx_messageInfo_UserInfo.Size(m)
}
func (m *UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo proto.InternalMessageInfo

func (m *UserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserInfo) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

type RankListInfo struct {
	Suminfo              *BeatSumInfo `protobuf:"bytes,1,opt,name=suminfo,proto3" json:"suminfo,omitempty"`
	Userinfo             *UserInfo    `protobuf:"bytes,2,opt,name=userinfo,proto3" json:"userinfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RankListInfo) Reset()         { *m = RankListInfo{} }
func (m *RankListInfo) String() string { return proto.CompactTextString(m) }
func (*RankListInfo) ProtoMessage()    {}
func (*RankListInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{19}
}
func (m *RankListInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RankListInfo.Unmarshal(m, b)
}
func (m *RankListInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RankListInfo.Marshal(b, m, deterministic)
}
func (dst *RankListInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RankListInfo.Merge(dst, src)
}
func (m *RankListInfo) XXX_Size() int {
	return xxx_messageInfo_RankListInfo.Size(m)
}
func (m *RankListInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RankListInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RankListInfo proto.InternalMessageInfo

func (m *RankListInfo) GetSuminfo() *BeatSumInfo {
	if m != nil {
		return m.Suminfo
	}
	return nil
}

func (m *RankListInfo) GetUserinfo() *UserInfo {
	if m != nil {
		return m.Userinfo
	}
	return nil
}

type RequestRankListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PageNum              uint32   `protobuf:"varint,2,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageIdx              uint32   `protobuf:"varint,3,opt,name=pageIdx,proto3" json:"pageIdx,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RequestRankListReq) Reset()         { *m = RequestRankListReq{} }
func (m *RequestRankListReq) String() string { return proto.CompactTextString(m) }
func (*RequestRankListReq) ProtoMessage()    {}
func (*RequestRankListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{20}
}
func (m *RequestRankListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RequestRankListReq.Unmarshal(m, b)
}
func (m *RequestRankListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RequestRankListReq.Marshal(b, m, deterministic)
}
func (dst *RequestRankListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RequestRankListReq.Merge(dst, src)
}
func (m *RequestRankListReq) XXX_Size() int {
	return xxx_messageInfo_RequestRankListReq.Size(m)
}
func (m *RequestRankListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RequestRankListReq.DiscardUnknown(m)
}

var xxx_messageInfo_RequestRankListReq proto.InternalMessageInfo

func (m *RequestRankListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RequestRankListReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *RequestRankListReq) GetPageIdx() uint32 {
	if m != nil {
		return m.PageIdx
	}
	return 0
}

type RequestRankListResp struct {
	Info                 []*RankListInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	Meinfo               *RankListInfo   `protobuf:"bytes,2,opt,name=meinfo,proto3" json:"meinfo,omitempty"`
	TotalNum             uint64          `protobuf:"varint,3,opt,name=totalNum,proto3" json:"totalNum,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *RequestRankListResp) Reset()         { *m = RequestRankListResp{} }
func (m *RequestRankListResp) String() string { return proto.CompactTextString(m) }
func (*RequestRankListResp) ProtoMessage()    {}
func (*RequestRankListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{21}
}
func (m *RequestRankListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RequestRankListResp.Unmarshal(m, b)
}
func (m *RequestRankListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RequestRankListResp.Marshal(b, m, deterministic)
}
func (dst *RequestRankListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RequestRankListResp.Merge(dst, src)
}
func (m *RequestRankListResp) XXX_Size() int {
	return xxx_messageInfo_RequestRankListResp.Size(m)
}
func (m *RequestRankListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RequestRankListResp.DiscardUnknown(m)
}

var xxx_messageInfo_RequestRankListResp proto.InternalMessageInfo

func (m *RequestRankListResp) GetInfo() []*RankListInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *RequestRankListResp) GetMeinfo() *RankListInfo {
	if m != nil {
		return m.Meinfo
	}
	return nil
}

func (m *RequestRankListResp) GetTotalNum() uint64 {
	if m != nil {
		return m.TotalNum
	}
	return 0
}

// 榜单播报
type ReportRankingInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Price                uint32   `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	GiftName             string   `protobuf:"bytes,5,opt,name=giftName,proto3" json:"giftName,omitempty"`
	GiftNum              uint32   `protobuf:"varint,6,opt,name=giftNum,proto3" json:"giftNum,omitempty"`
	Ts                   uint32   `protobuf:"varint,7,opt,name=ts,proto3" json:"ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportRankingInfo) Reset()         { *m = ReportRankingInfo{} }
func (m *ReportRankingInfo) String() string { return proto.CompactTextString(m) }
func (*ReportRankingInfo) ProtoMessage()    {}
func (*ReportRankingInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{22}
}
func (m *ReportRankingInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportRankingInfo.Unmarshal(m, b)
}
func (m *ReportRankingInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportRankingInfo.Marshal(b, m, deterministic)
}
func (dst *ReportRankingInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportRankingInfo.Merge(dst, src)
}
func (m *ReportRankingInfo) XXX_Size() int {
	return xxx_messageInfo_ReportRankingInfo.Size(m)
}
func (m *ReportRankingInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportRankingInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ReportRankingInfo proto.InternalMessageInfo

func (m *ReportRankingInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportRankingInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ReportRankingInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ReportRankingInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *ReportRankingInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *ReportRankingInfo) GetGiftNum() uint32 {
	if m != nil {
		return m.GiftNum
	}
	return 0
}

func (m *ReportRankingInfo) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

type ReportRankingsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportRankingsReq) Reset()         { *m = ReportRankingsReq{} }
func (m *ReportRankingsReq) String() string { return proto.CompactTextString(m) }
func (*ReportRankingsReq) ProtoMessage()    {}
func (*ReportRankingsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{23}
}
func (m *ReportRankingsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportRankingsReq.Unmarshal(m, b)
}
func (m *ReportRankingsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportRankingsReq.Marshal(b, m, deterministic)
}
func (dst *ReportRankingsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportRankingsReq.Merge(dst, src)
}
func (m *ReportRankingsReq) XXX_Size() int {
	return xxx_messageInfo_ReportRankingsReq.Size(m)
}
func (m *ReportRankingsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportRankingsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportRankingsReq proto.InternalMessageInfo

func (m *ReportRankingsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ReportRankingsResp struct {
	Infos                []*ReportRankingInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ReportRankingsResp) Reset()         { *m = ReportRankingsResp{} }
func (m *ReportRankingsResp) String() string { return proto.CompactTextString(m) }
func (*ReportRankingsResp) ProtoMessage()    {}
func (*ReportRankingsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{24}
}
func (m *ReportRankingsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportRankingsResp.Unmarshal(m, b)
}
func (m *ReportRankingsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportRankingsResp.Marshal(b, m, deterministic)
}
func (dst *ReportRankingsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportRankingsResp.Merge(dst, src)
}
func (m *ReportRankingsResp) XXX_Size() int {
	return xxx_messageInfo_ReportRankingsResp.Size(m)
}
func (m *ReportRankingsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportRankingsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportRankingsResp proto.InternalMessageInfo

func (m *ReportRankingsResp) GetInfos() []*ReportRankingInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

type LotteryAwardAsync struct {
	Uid                      uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	LotteryType              uint32   `protobuf:"varint,2,opt,name=lottery_type,json=lotteryType,proto3" json:"lottery_type,omitempty"`
	SourceId                 uint32   `protobuf:"varint,3,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	EffectSecond             uint32   `protobuf:"varint,4,opt,name=effect_second,json=effectSecond,proto3" json:"effect_second,omitempty"`
	LotteryItemPoolIdx       uint32   `protobuf:"varint,5,opt,name=lottery_item_pool_idx,json=lotteryItemPoolIdx,proto3" json:"lottery_item_pool_idx,omitempty"`
	LotteryItemPrice         uint32   `protobuf:"varint,6,opt,name=lottery_item_price,json=lotteryItemPrice,proto3" json:"lottery_item_price,omitempty"`
	LotteryItemRealPriceSort uint32   `protobuf:"varint,7,opt,name=lottery_item_real_price_sort,json=lotteryItemRealPriceSort,proto3" json:"lottery_item_real_price_sort,omitempty"`
	LotteryGamePeriodId      uint32   `protobuf:"varint,8,opt,name=lottery_game_period_id,json=lotteryGamePeriodId,proto3" json:"lottery_game_period_id,omitempty"`
	LotteryGameTs            uint32   `protobuf:"varint,9,opt,name=lottery_game_ts,json=lotteryGameTs,proto3" json:"lottery_game_ts,omitempty"`
	ChannelId                uint32   `protobuf:"varint,10,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameTime                 uint32   `protobuf:"varint,11,opt,name=game_time,json=gameTime,proto3" json:"game_time,omitempty"`
	SourceKey                string   `protobuf:"bytes,12,opt,name=source_key,json=sourceKey,proto3" json:"source_key,omitempty"`
	IsTestAward              bool     `protobuf:"varint,13,opt,name=is_test_award,json=isTestAward,proto3" json:"is_test_award,omitempty"`
	Gameid                   uint32   `protobuf:"varint,14,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Name                     string   `protobuf:"bytes,15,opt,name=name,proto3" json:"name,omitempty"`
	CntType                  uint32   `protobuf:"varint,16,opt,name=cntType,proto3" json:"cntType,omitempty"`
	AwardKey                 string   `protobuf:"bytes,17,opt,name=awardKey,proto3" json:"awardKey,omitempty"`
	IsRetry                  bool     `protobuf:"varint,18,opt,name=is_retry,json=isRetry,proto3" json:"is_retry,omitempty"`
	TbeanPrice               uint32   `protobuf:"varint,19,opt,name=tbean_price,json=tbeanPrice,proto3" json:"tbean_price,omitempty"`
	XXX_NoUnkeyedLiteral     struct{} `json:"-"`
	XXX_unrecognized         []byte   `json:"-"`
	XXX_sizecache            int32    `json:"-"`
}

func (m *LotteryAwardAsync) Reset()         { *m = LotteryAwardAsync{} }
func (m *LotteryAwardAsync) String() string { return proto.CompactTextString(m) }
func (*LotteryAwardAsync) ProtoMessage()    {}
func (*LotteryAwardAsync) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{25}
}
func (m *LotteryAwardAsync) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryAwardAsync.Unmarshal(m, b)
}
func (m *LotteryAwardAsync) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryAwardAsync.Marshal(b, m, deterministic)
}
func (dst *LotteryAwardAsync) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryAwardAsync.Merge(dst, src)
}
func (m *LotteryAwardAsync) XXX_Size() int {
	return xxx_messageInfo_LotteryAwardAsync.Size(m)
}
func (m *LotteryAwardAsync) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryAwardAsync.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryAwardAsync proto.InternalMessageInfo

func (m *LotteryAwardAsync) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LotteryAwardAsync) GetLotteryType() uint32 {
	if m != nil {
		return m.LotteryType
	}
	return 0
}

func (m *LotteryAwardAsync) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *LotteryAwardAsync) GetEffectSecond() uint32 {
	if m != nil {
		return m.EffectSecond
	}
	return 0
}

func (m *LotteryAwardAsync) GetLotteryItemPoolIdx() uint32 {
	if m != nil {
		return m.LotteryItemPoolIdx
	}
	return 0
}

func (m *LotteryAwardAsync) GetLotteryItemPrice() uint32 {
	if m != nil {
		return m.LotteryItemPrice
	}
	return 0
}

func (m *LotteryAwardAsync) GetLotteryItemRealPriceSort() uint32 {
	if m != nil {
		return m.LotteryItemRealPriceSort
	}
	return 0
}

func (m *LotteryAwardAsync) GetLotteryGamePeriodId() uint32 {
	if m != nil {
		return m.LotteryGamePeriodId
	}
	return 0
}

func (m *LotteryAwardAsync) GetLotteryGameTs() uint32 {
	if m != nil {
		return m.LotteryGameTs
	}
	return 0
}

func (m *LotteryAwardAsync) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *LotteryAwardAsync) GetGameTime() uint32 {
	if m != nil {
		return m.GameTime
	}
	return 0
}

func (m *LotteryAwardAsync) GetSourceKey() string {
	if m != nil {
		return m.SourceKey
	}
	return ""
}

func (m *LotteryAwardAsync) GetIsTestAward() bool {
	if m != nil {
		return m.IsTestAward
	}
	return false
}

func (m *LotteryAwardAsync) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *LotteryAwardAsync) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *LotteryAwardAsync) GetCntType() uint32 {
	if m != nil {
		return m.CntType
	}
	return 0
}

func (m *LotteryAwardAsync) GetAwardKey() string {
	if m != nil {
		return m.AwardKey
	}
	return ""
}

func (m *LotteryAwardAsync) GetIsRetry() bool {
	if m != nil {
		return m.IsRetry
	}
	return false
}

func (m *LotteryAwardAsync) GetTbeanPrice() uint32 {
	if m != nil {
		return m.TbeanPrice
	}
	return 0
}

type KafkaChannelInfo struct {
	Gameid               uint32   `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid,omitempty"`
	Chid                 uint32   `protobuf:"varint,2,opt,name=chid,proto3" json:"chid,omitempty"`
	ReportTime           int64    `protobuf:"varint,3,opt,name=report_time,json=reportTime,proto3" json:"report_time,omitempty"`
	GameConfigBeginTime  int64    `protobuf:"varint,4,opt,name=game_config_begin_time,json=gameConfigBeginTime,proto3" json:"game_config_begin_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KafkaChannelInfo) Reset()         { *m = KafkaChannelInfo{} }
func (m *KafkaChannelInfo) String() string { return proto.CompactTextString(m) }
func (*KafkaChannelInfo) ProtoMessage()    {}
func (*KafkaChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{26}
}
func (m *KafkaChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KafkaChannelInfo.Unmarshal(m, b)
}
func (m *KafkaChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KafkaChannelInfo.Marshal(b, m, deterministic)
}
func (dst *KafkaChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KafkaChannelInfo.Merge(dst, src)
}
func (m *KafkaChannelInfo) XXX_Size() int {
	return xxx_messageInfo_KafkaChannelInfo.Size(m)
}
func (m *KafkaChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_KafkaChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_KafkaChannelInfo proto.InternalMessageInfo

func (m *KafkaChannelInfo) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *KafkaChannelInfo) GetChid() uint32 {
	if m != nil {
		return m.Chid
	}
	return 0
}

func (m *KafkaChannelInfo) GetReportTime() int64 {
	if m != nil {
		return m.ReportTime
	}
	return 0
}

func (m *KafkaChannelInfo) GetGameConfigBeginTime() int64 {
	if m != nil {
		return m.GameConfigBeginTime
	}
	return 0
}

type StatLottery struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Giftid               string   `protobuf:"bytes,2,opt,name=giftid,proto3" json:"giftid,omitempty"`
	Giftname             string   `protobuf:"bytes,3,opt,name=giftname,proto3" json:"giftname,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StatLottery) Reset()         { *m = StatLottery{} }
func (m *StatLottery) String() string { return proto.CompactTextString(m) }
func (*StatLottery) ProtoMessage()    {}
func (*StatLottery) Descriptor() ([]byte, []int) {
	return fileDescriptor_molebeat_646a32383e580486, []int{27}
}
func (m *StatLottery) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StatLottery.Unmarshal(m, b)
}
func (m *StatLottery) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StatLottery.Marshal(b, m, deterministic)
}
func (dst *StatLottery) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StatLottery.Merge(dst, src)
}
func (m *StatLottery) XXX_Size() int {
	return xxx_messageInfo_StatLottery.Size(m)
}
func (m *StatLottery) XXX_DiscardUnknown() {
	xxx_messageInfo_StatLottery.DiscardUnknown(m)
}

var xxx_messageInfo_StatLottery proto.InternalMessageInfo

func (m *StatLottery) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StatLottery) GetGiftid() string {
	if m != nil {
		return m.Giftid
	}
	return ""
}

func (m *StatLottery) GetGiftname() string {
	if m != nil {
		return m.Giftname
	}
	return ""
}

func init() {
	proto.RegisterType((*MoleBeatReportReq)(nil), "molebeat.MoleBeatReportReq")
	proto.RegisterType((*MoleBeatReportRsp)(nil), "molebeat.MoleBeatReportRsp")
	proto.RegisterType((*GetUserTotalBeatCntReq)(nil), "molebeat.GetUserTotalBeatCntReq")
	proto.RegisterType((*GetUserTotalBeatCntResp)(nil), "molebeat.GetUserTotalBeatCntResp")
	proto.RegisterType((*StartMoleBeatReq)(nil), "molebeat.StartMoleBeatReq")
	proto.RegisterType((*StartMoleBeatResp)(nil), "molebeat.StartMoleBeatResp")
	proto.RegisterType((*StopMoleBeatReq)(nil), "molebeat.StopMoleBeatReq")
	proto.RegisterType((*StopMoleBeatResp)(nil), "molebeat.StopMoleBeatResp")
	proto.RegisterType((*GetBeatDataReq)(nil), "molebeat.GetBeatDataReq")
	proto.RegisterType((*GetBeatDataResp)(nil), "molebeat.GetBeatDataResp")
	proto.RegisterType((*MoleAttackGamePeriodInfo)(nil), "molebeat.MoleAttackGamePeriodInfo")
	proto.RegisterType((*GetMoleAttackGameConfigReq)(nil), "molebeat.GetMoleAttackGameConfigReq")
	proto.RegisterType((*GetMoleAttackGameConfigRsp)(nil), "molebeat.GetMoleAttackGameConfigRsp")
	proto.RegisterType((*UiRes)(nil), "molebeat.UiRes")
	proto.RegisterType((*MqInfo)(nil), "molebeat.MqInfo")
	proto.RegisterType((*BeatAward)(nil), "molebeat.BeatAward")
	proto.RegisterType((*AllBroadCast)(nil), "molebeat.AllBroadCast")
	proto.RegisterType((*BeatSumInfo)(nil), "molebeat.BeatSumInfo")
	proto.RegisterType((*UserInfo)(nil), "molebeat.UserInfo")
	proto.RegisterType((*RankListInfo)(nil), "molebeat.RankListInfo")
	proto.RegisterType((*RequestRankListReq)(nil), "molebeat.RequestRankListReq")
	proto.RegisterType((*RequestRankListResp)(nil), "molebeat.RequestRankListResp")
	proto.RegisterType((*ReportRankingInfo)(nil), "molebeat.ReportRankingInfo")
	proto.RegisterType((*ReportRankingsReq)(nil), "molebeat.ReportRankingsReq")
	proto.RegisterType((*ReportRankingsResp)(nil), "molebeat.ReportRankingsResp")
	proto.RegisterType((*LotteryAwardAsync)(nil), "molebeat.LotteryAwardAsync")
	proto.RegisterType((*KafkaChannelInfo)(nil), "molebeat.kafka_channel_info")
	proto.RegisterType((*StatLottery)(nil), "molebeat.StatLottery")
	proto.RegisterEnum("molebeat.MoleGameType", MoleGameType_name, MoleGameType_value)
	proto.RegisterEnum("molebeat.GetMoleAttackGameConfigReq_ENV_PROD_STAGING_E", GetMoleAttackGameConfigReq_ENV_PROD_STAGING_E_name, GetMoleAttackGameConfigReq_ENV_PROD_STAGING_E_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MoleBeatClient is the client API for MoleBeat service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MoleBeatClient interface {
	GetMoleAttackGameConfig(ctx context.Context, in *GetMoleAttackGameConfigReq, opts ...grpc.CallOption) (*GetMoleAttackGameConfigRsp, error)
	ReportBeat(ctx context.Context, in *MoleBeatReportReq, opts ...grpc.CallOption) (*MoleBeatReportRsp, error)
	GetMoleBeatRankList(ctx context.Context, in *RequestRankListReq, opts ...grpc.CallOption) (*RequestRankListResp, error)
	GetReportRankings(ctx context.Context, in *ReportRankingsReq, opts ...grpc.CallOption) (*ReportRankingsResp, error)
	StartMoleBeat(ctx context.Context, in *StartMoleBeatReq, opts ...grpc.CallOption) (*StartMoleBeatResp, error)
	StopMoleBeat(ctx context.Context, in *StopMoleBeatReq, opts ...grpc.CallOption) (*StopMoleBeatResp, error)
	GetBeatData(ctx context.Context, in *GetBeatDataReq, opts ...grpc.CallOption) (*GetBeatDataResp, error)
}

type moleBeatClient struct {
	cc *grpc.ClientConn
}

func NewMoleBeatClient(cc *grpc.ClientConn) MoleBeatClient {
	return &moleBeatClient{cc}
}

func (c *moleBeatClient) GetMoleAttackGameConfig(ctx context.Context, in *GetMoleAttackGameConfigReq, opts ...grpc.CallOption) (*GetMoleAttackGameConfigRsp, error) {
	out := new(GetMoleAttackGameConfigRsp)
	err := c.cc.Invoke(ctx, "/molebeat.MoleBeat/GetMoleAttackGameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moleBeatClient) ReportBeat(ctx context.Context, in *MoleBeatReportReq, opts ...grpc.CallOption) (*MoleBeatReportRsp, error) {
	out := new(MoleBeatReportRsp)
	err := c.cc.Invoke(ctx, "/molebeat.MoleBeat/ReportBeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moleBeatClient) GetMoleBeatRankList(ctx context.Context, in *RequestRankListReq, opts ...grpc.CallOption) (*RequestRankListResp, error) {
	out := new(RequestRankListResp)
	err := c.cc.Invoke(ctx, "/molebeat.MoleBeat/GetMoleBeatRankList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moleBeatClient) GetReportRankings(ctx context.Context, in *ReportRankingsReq, opts ...grpc.CallOption) (*ReportRankingsResp, error) {
	out := new(ReportRankingsResp)
	err := c.cc.Invoke(ctx, "/molebeat.MoleBeat/GetReportRankings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moleBeatClient) StartMoleBeat(ctx context.Context, in *StartMoleBeatReq, opts ...grpc.CallOption) (*StartMoleBeatResp, error) {
	out := new(StartMoleBeatResp)
	err := c.cc.Invoke(ctx, "/molebeat.MoleBeat/StartMoleBeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moleBeatClient) StopMoleBeat(ctx context.Context, in *StopMoleBeatReq, opts ...grpc.CallOption) (*StopMoleBeatResp, error) {
	out := new(StopMoleBeatResp)
	err := c.cc.Invoke(ctx, "/molebeat.MoleBeat/StopMoleBeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moleBeatClient) GetBeatData(ctx context.Context, in *GetBeatDataReq, opts ...grpc.CallOption) (*GetBeatDataResp, error) {
	out := new(GetBeatDataResp)
	err := c.cc.Invoke(ctx, "/molebeat.MoleBeat/GetBeatData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MoleBeatServer is the server API for MoleBeat service.
type MoleBeatServer interface {
	GetMoleAttackGameConfig(context.Context, *GetMoleAttackGameConfigReq) (*GetMoleAttackGameConfigRsp, error)
	ReportBeat(context.Context, *MoleBeatReportReq) (*MoleBeatReportRsp, error)
	GetMoleBeatRankList(context.Context, *RequestRankListReq) (*RequestRankListResp, error)
	GetReportRankings(context.Context, *ReportRankingsReq) (*ReportRankingsResp, error)
	StartMoleBeat(context.Context, *StartMoleBeatReq) (*StartMoleBeatResp, error)
	StopMoleBeat(context.Context, *StopMoleBeatReq) (*StopMoleBeatResp, error)
	GetBeatData(context.Context, *GetBeatDataReq) (*GetBeatDataResp, error)
}

func RegisterMoleBeatServer(s *grpc.Server, srv MoleBeatServer) {
	s.RegisterService(&_MoleBeat_serviceDesc, srv)
}

func _MoleBeat_GetMoleAttackGameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMoleAttackGameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoleBeatServer).GetMoleAttackGameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/molebeat.MoleBeat/GetMoleAttackGameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoleBeatServer).GetMoleAttackGameConfig(ctx, req.(*GetMoleAttackGameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoleBeat_ReportBeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MoleBeatReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoleBeatServer).ReportBeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/molebeat.MoleBeat/ReportBeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoleBeatServer).ReportBeat(ctx, req.(*MoleBeatReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoleBeat_GetMoleBeatRankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RequestRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoleBeatServer).GetMoleBeatRankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/molebeat.MoleBeat/GetMoleBeatRankList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoleBeatServer).GetMoleBeatRankList(ctx, req.(*RequestRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoleBeat_GetReportRankings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportRankingsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoleBeatServer).GetReportRankings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/molebeat.MoleBeat/GetReportRankings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoleBeatServer).GetReportRankings(ctx, req.(*ReportRankingsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoleBeat_StartMoleBeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartMoleBeatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoleBeatServer).StartMoleBeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/molebeat.MoleBeat/StartMoleBeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoleBeatServer).StartMoleBeat(ctx, req.(*StartMoleBeatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoleBeat_StopMoleBeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopMoleBeatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoleBeatServer).StopMoleBeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/molebeat.MoleBeat/StopMoleBeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoleBeatServer).StopMoleBeat(ctx, req.(*StopMoleBeatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoleBeat_GetBeatData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBeatDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoleBeatServer).GetBeatData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/molebeat.MoleBeat/GetBeatData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoleBeatServer).GetBeatData(ctx, req.(*GetBeatDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MoleBeat_serviceDesc = grpc.ServiceDesc{
	ServiceName: "molebeat.MoleBeat",
	HandlerType: (*MoleBeatServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMoleAttackGameConfig",
			Handler:    _MoleBeat_GetMoleAttackGameConfig_Handler,
		},
		{
			MethodName: "ReportBeat",
			Handler:    _MoleBeat_ReportBeat_Handler,
		},
		{
			MethodName: "GetMoleBeatRankList",
			Handler:    _MoleBeat_GetMoleBeatRankList_Handler,
		},
		{
			MethodName: "GetReportRankings",
			Handler:    _MoleBeat_GetReportRankings_Handler,
		},
		{
			MethodName: "StartMoleBeat",
			Handler:    _MoleBeat_StartMoleBeat_Handler,
		},
		{
			MethodName: "StopMoleBeat",
			Handler:    _MoleBeat_StopMoleBeat_Handler,
		},
		{
			MethodName: "GetBeatData",
			Handler:    _MoleBeat_GetBeatData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "molebeat/molebeat.proto",
}

func init() { proto.RegisterFile("molebeat/molebeat.proto", fileDescriptor_molebeat_646a32383e580486) }

var fileDescriptor_molebeat_646a32383e580486 = []byte{
	// 1822 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0x5f, 0x53, 0x1b, 0xc9,
	0x11, 0xb7, 0x40, 0x80, 0xd4, 0x92, 0x90, 0x34, 0x9c, 0xf1, 0x22, 0xec, 0x1c, 0xd9, 0x38, 0x2e,
	0xca, 0x71, 0x70, 0x0e, 0x97, 0x5f, 0x53, 0x25, 0x8c, 0x4f, 0xe1, 0x0e, 0x63, 0x6a, 0x25, 0x92,
	0xca, 0xbd, 0x6c, 0x0d, 0xda, 0x91, 0x3c, 0x61, 0xff, 0xb1, 0x33, 0x32, 0x56, 0xbe, 0x43, 0x5e,
	0x93, 0xaa, 0xbc, 0xdf, 0x67, 0xc8, 0x6b, 0x3e, 0x5a, 0xaa, 0x7b, 0x66, 0xa5, 0x95, 0x40, 0x57,
	0x49, 0xe5, 0x9e, 0xb4, 0xfd, 0x67, 0x7a, 0x7a, 0xba, 0xa7, 0x7f, 0xdd, 0x23, 0x78, 0x12, 0x25,
	0xa1, 0xb8, 0x16, 0x5c, 0xbf, 0xce, 0x3f, 0x8e, 0xd2, 0x2c, 0xd1, 0x09, 0xab, 0xe4, 0xb4, 0xfb,
	0x57, 0x68, 0x7f, 0x48, 0x42, 0x71, 0x22, 0xb8, 0xf6, 0x44, 0x9a, 0x64, 0xda, 0x13, 0xb7, 0xac,
	0x05, 0xeb, 0x13, 0x19, 0x38, 0xa5, 0x83, 0xd2, 0x61, 0xc3, 0xc3, 0x4f, 0xc6, 0xa0, 0x3c, 0xfc,
	0x24, 0x03, 0x67, 0x8d, 0x58, 0xf4, 0xcd, 0xf6, 0xa0, 0x82, 0x26, 0xfc, 0x61, 0xac, 0x9d, 0x75,
	0xe2, 0x6f, 0x21, 0xfd, 0x2e, 0xd6, 0xec, 0x39, 0x6c, 0xe3, 0x0e, 0xfe, 0x98, 0x47, 0xc2, 0xd7,
	0xd3, 0x54, 0x38, 0x65, 0x52, 0xa8, 0x23, 0xb7, 0xc7, 0x23, 0x31, 0x98, 0xa6, 0xc2, 0xfd, 0x78,
	0x6f, 0x6f, 0x95, 0xb2, 0x67, 0x00, 0xfc, 0x8e, 0x67, 0x81, 0x1f, 0xf3, 0x48, 0x90, 0x0b, 0x55,
	0xaf, 0x4a, 0x9c, 0x0b, 0x1e, 0x09, 0xb6, 0x0f, 0x86, 0xf0, 0x27, 0x59, 0x48, 0xde, 0x54, 0xbd,
	0x0a, 0x31, 0xae, 0xb2, 0xd0, 0x75, 0x60, 0xb7, 0x27, 0xf4, 0x95, 0x12, 0xd9, 0x20, 0xd1, 0x3c,
	0x3c, 0x31, 0xde, 0x78, 0xe2, 0xd6, 0xdd, 0x83, 0x27, 0x0f, 0x4a, 0x54, 0xea, 0x32, 0x68, 0xf5,
	0x35, 0xcf, 0xf4, 0xdc, 0x95, 0x5b, 0xf7, 0x03, 0xb4, 0x97, 0x78, 0x2a, 0x65, 0x5f, 0x43, 0x6d,
	0x92, 0x06, 0x5c, 0x0b, 0x5f, 0x4b, 0xeb, 0x5a, 0xc3, 0x03, 0xc3, 0x1a, 0xc8, 0x48, 0xb0, 0x27,
	0xb0, 0x25, 0x95, 0x9f, 0xa4, 0x22, 0x26, 0xcf, 0x2a, 0xde, 0xa6, 0x54, 0x1f, 0x53, 0x11, 0xbb,
	0x6d, 0x68, 0xf6, 0x75, 0x92, 0x16, 0x77, 0x38, 0xc7, 0x5d, 0x8b, 0xac, 0xff, 0x6b, 0x83, 0xdf,
	0xc2, 0x76, 0x4f, 0x68, 0x34, 0x74, 0xca, 0x35, 0xc7, 0x14, 0xee, 0x43, 0xd5, 0x04, 0x7f, 0x6e,
	0xa9, 0x82, 0x0c, 0xb4, 0xe3, 0x1e, 0x41, 0x73, 0x41, 0x5d, 0xa5, 0xa8, 0x1f, 0x70, 0xcd, 0x7d,
	0x19, 0x8f, 0x12, 0xa7, 0x74, 0xb0, 0x8e, 0x71, 0x45, 0xc6, 0x59, 0x3c, 0x4a, 0xdc, 0x7f, 0x96,
	0xc0, 0x41, 0x4f, 0xbb, 0x5a, 0xf3, 0xe1, 0x0d, 0xe6, 0xef, 0x52, 0x64, 0x32, 0x09, 0x50, 0xc8,
	0x5e, 0x40, 0x33, 0x25, 0xca, 0xbf, 0x16, 0x63, 0x19, 0xfb, 0x5a, 0xd9, 0xfd, 0x1a, 0x86, 0x7d,
	0x82, 0xdc, 0x81, 0x62, 0x87, 0xd0, 0xb2, 0x7a, 0x23, 0x19, 0x4b, 0xf5, 0x09, 0x15, 0xcd, 0x75,
	0xda, 0x36, 0xfc, 0x6f, 0x89, 0x3d, 0x50, 0xec, 0x15, 0x30, 0xab, 0x29, 0x63, 0x2d, 0xb2, 0xcf,
	0x3c, 0x44, 0x5d, 0x73, 0xc5, 0xac, 0x8d, 0x33, 0x2b, 0x18, 0x28, 0xf7, 0x1f, 0x25, 0xe8, 0xf4,
	0x84, 0x5e, 0xf4, 0xef, 0x5d, 0x12, 0x8f, 0xe4, 0xd8, 0xde, 0x65, 0x11, 0x7f, 0xce, 0xef, 0xb2,
	0x88, 0x3f, 0xe7, 0xb7, 0x7b, 0x6d, 0x76, 0xbb, 0xdd, 0x2b, 0x60, 0xef, 0x2f, 0xfe, 0xe8, 0x5f,
	0x7a, 0x1f, 0x4f, 0xfd, 0xfe, 0xa0, 0xdb, 0x3b, 0xbb, 0xe8, 0xf9, 0xef, 0x59, 0x13, 0x6a, 0xc8,
	0x3d, 0x7d, 0xff, 0x6d, 0xf7, 0xea, 0x7c, 0xd0, 0x7a, 0xc4, 0xea, 0x50, 0xc9, 0xd5, 0x5a, 0xa5,
	0x5c, 0x6c, 0xf5, 0x5b, 0x6b, 0x39, 0x63, 0xf0, 0xbe, 0x3f, 0x40, 0xc6, 0xba, 0xfb, 0xe3, 0xe6,
	0x6a, 0xcf, 0x54, 0xca, 0xbe, 0x81, 0xc7, 0x43, 0x22, 0xfc, 0x90, 0x2b, 0xed, 0x0f, 0x3f, 0xf1,
	0x78, 0x2c, 0xe6, 0xe1, 0x63, 0x46, 0x78, 0xce, 0x95, 0x7e, 0x47, 0xa2, 0x81, 0x9a, 0x65, 0x95,
	0x6a, 0xc3, 0xde, 0x7e, 0x64, 0x50, 0x69, 0xb8, 0xd0, 0x20, 0xe1, 0x2c, 0x0d, 0x26, 0x62, 0x35,
	0x64, 0xe6, 0x49, 0x78, 0x0e, 0xdb, 0xa4, 0x33, 0x4f, 0x81, 0x2d, 0x4c, 0xe4, 0xce, 0x12, 0x70,
	0x3e, 0x4b, 0x15, 0x5e, 0x07, 0x3f, 0x94, 0x4a, 0x3b, 0x1b, 0x07, 0xeb, 0x87, 0xb5, 0x63, 0xf7,
	0x68, 0x86, 0x24, 0xab, 0x2e, 0x44, 0x9e, 0x4e, 0xfc, 0x3e, 0x97, 0x4a, 0xb3, 0x2e, 0x3c, 0xcb,
	0x78, 0x1c, 0x24, 0x11, 0xc1, 0x81, 0xc2, 0xba, 0xf2, 0x03, 0x11, 0xf2, 0xa9, 0xaf, 0xc4, 0x30,
	0x89, 0x03, 0x67, 0x93, 0x5c, 0xe8, 0x18, 0xa5, 0x5e, 0xae, 0x73, 0x8a, 0x2a, 0x7d, 0xd2, 0x60,
	0xdf, 0x81, 0x5b, 0x30, 0x31, 0x92, 0xb1, 0x9f, 0x11, 0x60, 0x2c, 0xda, 0xd9, 0x22, 0x3b, 0xbf,
	0x98, 0xdb, 0x19, 0xc9, 0xd8, 0x00, 0x4b, 0xd1, 0xd6, 0x11, 0xec, 0x50, 0x08, 0xd2, 0x4c, 0xa4,
	0x3c, 0x13, 0xf9, 0xe2, 0x0a, 0x2d, 0x6e, 0xa3, 0xe8, 0xd2, 0x48, 0xac, 0xfe, 0xef, 0xe0, 0x2b,
	0xd2, 0x0f, 0x26, 0x19, 0xd7, 0x32, 0x89, 0xf3, 0x05, 0x55, 0x93, 0x25, 0x94, 0x9d, 0x5a, 0x91,
	0x5d, 0xf1, 0x02, 0x9a, 0x84, 0x7e, 0x3c, 0x4d, 0x05, 0xcf, 0x08, 0x1f, 0xc1, 0x54, 0x04, 0xb2,
	0xbb, 0xc4, 0x45, 0x94, 0x7c, 0x05, 0x8c, 0x53, 0x00, 0x7d, 0xae, 0x43, 0x41, 0x77, 0x20, 0xd6,
	0x4e, 0xcd, 0xdc, 0x73, 0x23, 0xe9, 0x1a, 0x01, 0x6a, 0xbb, 0xd0, 0x88, 0xc5, 0x9d, 0x3f, 0x45,
	0x93, 0x54, 0xd5, 0xf5, 0x83, 0xd2, 0xe1, 0xba, 0x57, 0x8b, 0xc5, 0xdd, 0x9f, 0x05, 0xcf, 0x08,
	0x20, 0x0e, 0xa1, 0x15, 0x8b, 0x3b, 0x61, 0x94, 0xac, 0x9f, 0x0d, 0x53, 0x63, 0xc8, 0x47, 0x3d,
	0xeb, 0xe3, 0x7d, 0x84, 0xde, 0xbe, 0x8f, 0xd0, 0x08, 0xc6, 0x77, 0x9f, 0xa4, 0x16, 0xfe, 0x44,
	0x06, 0xca, 0x69, 0x1e, 0xac, 0x1f, 0x36, 0xbc, 0x2a, 0x71, 0xae, 0x64, 0xa0, 0xe6, 0x62, 0x82,
	0xa4, 0x16, 0x41, 0x92, 0x11, 0x23, 0x2a, 0xb1, 0x17, 0xb0, 0x39, 0x91, 0x7e, 0x26, 0x94, 0xd3,
	0x3e, 0x28, 0x1d, 0xd6, 0x8e, 0x9b, 0xf3, 0xcb, 0x73, 0x25, 0x3d, 0xa1, 0xbc, 0x8d, 0x09, 0xfe,
	0xb8, 0xbf, 0x81, 0x0d, 0xa2, 0xa9, 0x32, 0xb3, 0xd0, 0x82, 0x3e, 0x7e, 0x22, 0x27, 0x0a, 0xde,
	0xda, 0xab, 0x8e, 0x9f, 0xee, 0x5b, 0xd8, 0xfc, 0x70, 0x4b, 0xc0, 0x83, 0x32, 0x35, 0x26, 0xed,
	0xba, 0x87, 0x9f, 0xd8, 0x91, 0x22, 0x35, 0x36, 0xc7, 0x31, 0xe5, 0xbd, 0x15, 0xa9, 0x31, 0xf5,
	0x9a, 0x2f, 0x50, 0x45, 0xbc, 0xeb, 0x62, 0xab, 0x40, 0xbf, 0xb1, 0xda, 0x62, 0x11, 0xfa, 0xb3,
	0x36, 0x57, 0xb5, 0x9c, 0xb3, 0xe0, 0x3e, 0x40, 0x2c, 0x35, 0xa5, 0xf5, 0x9f, 0x6c, 0x4a, 0xe5,
	0xa5, 0xa6, 0xf4, 0x27, 0xa8, 0x77, 0xc3, 0xf0, 0x24, 0x4b, 0x78, 0xf0, 0x8e, 0x2b, 0xfd, 0xbf,
	0x6f, 0xbe, 0x0b, 0x9b, 0x98, 0x25, 0x19, 0xd8, 0x82, 0xb6, 0x94, 0xeb, 0x43, 0x0d, 0x8f, 0xd4,
	0x9f, 0x44, 0x14, 0x8e, 0xbc, 0x1d, 0xab, 0x49, 0x64, 0xad, 0x52, 0x3b, 0xee, 0x4f, 0xa2, 0xb9,
	0x7f, 0x28, 0x33, 0x96, 0x8d, 0x7f, 0x28, 0x74, 0x60, 0x2b, 0xe3, 0xf1, 0x8d, 0x8c, 0xc7, 0x79,
	0x17, 0xb7, 0xa4, 0xfb, 0x17, 0xa8, 0x60, 0xc7, 0xcc, 0x83, 0xbd, 0x34, 0x12, 0x74, 0xa0, 0x12,
	0xcb, 0xe1, 0x4d, 0x11, 0x8a, 0x72, 0x1a, 0x6d, 0xf2, 0xe1, 0x30, 0x99, 0xd8, 0xc9, 0xa0, 0xea,
	0xe5, 0x24, 0x1e, 0x26, 0x4b, 0x92, 0x48, 0x06, 0x16, 0x78, 0x2c, 0xe5, 0x26, 0x50, 0xf7, 0x78,
	0x7c, 0x83, 0x80, 0x41, 0xfb, 0xbd, 0x86, 0x2d, 0x35, 0x89, 0x6c, 0x37, 0xc2, 0xcb, 0xf3, 0x78,
	0x7e, 0x79, 0x0a, 0xa7, 0xf6, 0x72, 0x2d, 0x76, 0x04, 0x95, 0x89, 0x12, 0x19, 0xad, 0x58, 0xa3,
	0x15, 0xac, 0x70, 0xdd, 0xec, 0x31, 0xbc, 0x99, 0x8e, 0xfb, 0x03, 0x30, 0x4f, 0xdc, 0x4e, 0x84,
	0xd2, 0xf9, 0xbe, 0x0f, 0x4f, 0x3e, 0x0e, 0x6c, 0xa5, 0x7c, 0x2c, 0x2e, 0x66, 0x91, 0xcb, 0xc9,
	0x5c, 0x72, 0x16, 0x7c, 0xc9, 0x03, 0x67, 0x49, 0xf7, 0x6f, 0x25, 0xd8, 0xb9, 0x67, 0x5c, 0xa5,
	0xec, 0x25, 0x94, 0x67, 0xfd, 0xb5, 0x76, 0xbc, 0x3b, 0xf7, 0xaf, 0x78, 0x74, 0xaf, 0x6c, 0xcf,
	0xb3, 0x19, 0x89, 0xc2, 0x69, 0x56, 0x69, 0x5b, 0x2d, 0x4c, 0x87, 0xc6, 0xd1, 0x06, 0x1d, 0x45,
	0x77, 0xca, 0xde, 0x8c, 0x76, 0xff, 0x55, 0x82, 0xb6, 0x9d, 0xb0, 0x4c, 0x6a, 0x7f, 0xd6, 0x94,
	0x7e, 0x05, 0x1b, 0x69, 0x26, 0x87, 0xf9, 0x8c, 0x67, 0x08, 0xb4, 0x35, 0x96, 0x23, 0x8d, 0xf5,
	0xe1, 0x6c, 0xd8, 0x4e, 0x65, 0x69, 0xb4, 0x45, 0xdf, 0x93, 0xc8, 0x62, 0x7f, 0x4e, 0xb2, 0x6d,
	0x58, 0xd3, 0xca, 0x02, 0xf9, 0x9a, 0x56, 0xee, 0xaf, 0x97, 0x1c, 0x57, 0x0f, 0x26, 0xc9, 0xed,
	0x61, 0x32, 0x17, 0xd5, 0xa8, 0xc1, 0x6e, 0x60, 0x68, 0x94, 0x8d, 0xf7, 0x7e, 0x21, 0x82, 0xcb,
	0xc1, 0xf0, 0x8c, 0xa6, 0xfb, 0xe3, 0x06, 0xb4, 0xcf, 0x13, 0xad, 0x45, 0x36, 0x25, 0xa8, 0xe8,
	0xaa, 0x69, 0x3c, 0x7c, 0x20, 0x52, 0xbf, 0x84, 0x7a, 0x68, 0xd4, 0x8a, 0x68, 0x53, 0xb3, 0x3c,
	0xc2, 0xce, 0x7d, 0xa8, 0xaa, 0x64, 0x92, 0x0d, 0x85, 0x3f, 0xab, 0xdc, 0x8a, 0x61, 0x9c, 0x05,
	0xec, 0x57, 0xd0, 0x10, 0xa3, 0x91, 0x18, 0xea, 0x1c, 0xa5, 0x6d, 0x1b, 0x36, 0x4c, 0x8b, 0xd1,
	0xdf, 0xc0, 0xe3, 0x7c, 0x13, 0xa9, 0x45, 0xe4, 0xa7, 0x49, 0x82, 0x98, 0xf1, 0x85, 0xe2, 0xd9,
	0xf0, 0x98, 0x15, 0x9e, 0x69, 0x11, 0x5d, 0x26, 0x49, 0x78, 0x16, 0x7c, 0xc1, 0x96, 0xb2, 0xb8,
	0x84, 0x12, 0x63, 0x82, 0xdc, 0x2a, 0xea, 0x53, 0x8e, 0x7e, 0x0f, 0x4f, 0x17, 0xb4, 0x33, 0xc1,
	0x43, 0xb3, 0xc4, 0x57, 0x49, 0xa6, 0x6d, 0x1e, 0x9c, 0xc2, 0x3a, 0x4f, 0xf0, 0x90, 0xd6, 0xf6,
	0x93, 0x4c, 0xb3, 0x37, 0xb0, 0x9b, 0xaf, 0x37, 0x2d, 0xd5, 0x0e, 0x0d, 0x79, 0x37, 0xdd, 0xb1,
	0xd2, 0xc2, 0x80, 0x40, 0xdd, 0x71, 0x61, 0x91, 0x56, 0xb6, 0x95, 0x36, 0x0a, 0xda, 0x03, 0xb5,
	0x84, 0x93, 0xb0, 0x8c, 0x93, 0x0b, 0x03, 0x6e, 0x6d, 0x71, 0xc0, 0xc5, 0xb5, 0x36, 0xf6, 0x37,
	0x62, 0x4a, 0x8d, 0xb2, 0xea, 0xd9, 0x6c, 0x7c, 0x2f, 0xa6, 0xd8, 0x4a, 0xa5, 0xf2, 0xb5, 0x50,
	0xda, 0x27, 0x18, 0xa4, 0x1e, 0x59, 0xf1, 0x6a, 0x52, 0x0d, 0x84, 0xb2, 0x3d, 0x62, 0x8e, 0xba,
	0xdb, 0x45, 0xd4, 0xc5, 0x97, 0x10, 0xd5, 0x47, 0x93, 0x8c, 0x96, 0xf3, 0xda, 0x18, 0xc6, 0x1a,
	0xb3, 0x4e, 0x4d, 0xb0, 0xe1, 0xe5, 0x24, 0x56, 0x01, 0xed, 0xf0, 0xbd, 0x98, 0x52, 0x13, 0xcc,
	0x1b, 0x03, 0x7a, 0xb1, 0x07, 0x15, 0xa9, 0xfc, 0x4c, 0xe8, 0x6c, 0xea, 0x30, 0x72, 0x60, 0x4b,
	0x2a, 0x0f, 0x49, 0x7c, 0x09, 0xe8, 0x6b, 0xc1, 0x63, 0x9b, 0xbf, 0x1d, 0xf3, 0x12, 0x20, 0x16,
	0x45, 0xdf, 0xfd, 0x7b, 0x09, 0xd8, 0x0d, 0x1f, 0xdd, 0x70, 0x7f, 0x16, 0x23, 0x2c, 0xe9, 0xb9,
	0xd3, 0xa5, 0x65, 0xa7, 0xef, 0x3d, 0xdf, 0xbe, 0x86, 0x9a, 0x1d, 0xa2, 0x28, 0x84, 0xeb, 0x34,
	0x4d, 0x80, 0x61, 0x51, 0x10, 0xdf, 0xc0, 0x2e, 0x45, 0xd8, 0x0e, 0xa9, 0x76, 0xac, 0x44, 0xdd,
	0x32, 0xe9, 0xd2, 0x18, 0x65, 0xc6, 0x59, 0x33, 0x5e, 0xe2, 0xd3, 0xa2, 0x0f, 0xb5, 0xbe, 0xe6,
	0xda, 0xd6, 0xd0, 0x03, 0x95, 0x83, 0x2e, 0xca, 0x91, 0xb6, 0xce, 0x54, 0x3d, 0x4b, 0xe5, 0x78,
	0x51, 0x68, 0xb0, 0x33, 0xfa, 0xe5, 0x31, 0xd4, 0x3f, 0x14, 0xc7, 0x92, 0x26, 0xd4, 0x2e, 0xcc,
	0xd4, 0x83, 0xac, 0xd6, 0x23, 0xd6, 0x86, 0x46, 0x5f, 0xa4, 0x5a, 0x44, 0xd7, 0xc2, 0xb0, 0x4a,
	0xc7, 0xff, 0x2e, 0x43, 0x25, 0x7f, 0x5d, 0xb1, 0x31, 0x3d, 0xff, 0x1e, 0x1a, 0xc4, 0xd9, 0xf3,
	0x39, 0x2a, 0xac, 0x7e, 0x45, 0x74, 0xfe, 0x0b, 0x2d, 0x95, 0xba, 0x8f, 0xd8, 0x1f, 0x00, 0x0c,
	0xb6, 0xd0, 0xb6, 0xfb, 0x8b, 0xd3, 0xf2, 0xc2, 0x23, 0xbb, 0xb3, 0x5a, 0x48, 0x96, 0x06, 0xb0,
	0x63, 0x77, 0x22, 0x89, 0x85, 0x7c, 0xf6, 0xb4, 0x08, 0x62, 0xcb, 0xed, 0xab, 0xf3, 0xec, 0x27,
	0xa4, 0x64, 0xf5, 0x12, 0xda, 0x3d, 0xa1, 0x17, 0xb1, 0x92, 0xad, 0x02, 0x46, 0x04, 0xdb, 0xce,
	0xd3, 0xd5, 0x42, 0xb2, 0xf8, 0x1d, 0x34, 0x16, 0x9e, 0xca, 0xac, 0x33, 0x5f, 0xb0, 0xfc, 0xae,
	0x2e, 0x9e, 0xf9, 0xde, 0xfb, 0xda, 0x7d, 0xc4, 0x7a, 0x50, 0x2f, 0x3e, 0x8a, 0xd9, 0x5e, 0x51,
	0x7d, 0xe1, 0xfd, 0xdc, 0xe9, 0xac, 0x12, 0x91, 0xa1, 0x53, 0xa8, 0x15, 0x1e, 0xb8, 0xcc, 0x59,
	0xc8, 0x5e, 0xe1, 0x99, 0xdc, 0xd9, 0x5b, 0x21, 0x41, 0x2b, 0x27, 0xaf, 0x7e, 0x78, 0x39, 0x4e,
	0x42, 0x1e, 0x8f, 0x8f, 0xde, 0x1e, 0x6b, 0x7d, 0x34, 0x4c, 0xa2, 0xd7, 0xf4, 0xf7, 0xc9, 0x30,
	0x09, 0x5f, 0x2b, 0x91, 0x7d, 0x96, 0x43, 0xa1, 0x66, 0xff, 0xac, 0x5c, 0x6f, 0x92, 0xec, 0xcd,
	0x7f, 0x02, 0x00, 0x00, 0xff, 0xff, 0x06, 0x74, 0x76, 0x4a, 0x75, 0x11, 0x00, 0x00,
}
