// Code generated by protoc-gen-gogo.
// source: services/guildwelfare/guildwelfare.proto
// DO NOT EDIT!

/*
	Package GuildWelfare is a generated protocol buffer package.

	namespace

	It is generated from these files:
		services/guildwelfare/guildwelfare.proto

	It has these top-level messages:
		AddGuildFirstJoinReq
		AddGuildFirstJoinResp
		GuildFirstJoinCount
		GetTopJoinGuildListReq
		GetTopJoinGuildListResp
		GetSoonestJoinGuildListReq
		GetSoonestJoinGuildListResp
		AwardFirstJoinGuildReq
		AwardFirstJoinGuildResp
		FirstJoinActivityInfo
		GetFirstJoinActivityInfoReq
*/
package GuildWelfare

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type WELFARE_TYPE int32

const (
	WELFARE_TYPE_EACH_USER    WELFARE_TYPE = 0
	WELFARE_TYPE_SOONEST_JOIN WELFARE_TYPE = 1
	WELFARE_TYPE_TOP_JOIN     WELFARE_TYPE = 2
)

var WELFARE_TYPE_name = map[int32]string{
	0: "EACH_USER",
	1: "SOONEST_JOIN",
	2: "TOP_JOIN",
}
var WELFARE_TYPE_value = map[string]int32{
	"EACH_USER":    0,
	"SOONEST_JOIN": 1,
	"TOP_JOIN":     2,
}

func (x WELFARE_TYPE) Enum() *WELFARE_TYPE {
	p := new(WELFARE_TYPE)
	*p = x
	return p
}
func (x WELFARE_TYPE) String() string {
	return proto.EnumName(WELFARE_TYPE_name, int32(x))
}
func (x *WELFARE_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(WELFARE_TYPE_value, data, "WELFARE_TYPE")
	if err != nil {
		return err
	}
	*x = WELFARE_TYPE(value)
	return nil
}
func (WELFARE_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorGuildwelfare, []int{0} }

type DAILY_SETTLEMENT_LIMIT int32

const (
	DAILY_SETTLEMENT_LIMIT_AWARD_GUILDS_LIMIT DAILY_SETTLEMENT_LIMIT = 5
)

var DAILY_SETTLEMENT_LIMIT_name = map[int32]string{
	5: "AWARD_GUILDS_LIMIT",
}
var DAILY_SETTLEMENT_LIMIT_value = map[string]int32{
	"AWARD_GUILDS_LIMIT": 5,
}

func (x DAILY_SETTLEMENT_LIMIT) Enum() *DAILY_SETTLEMENT_LIMIT {
	p := new(DAILY_SETTLEMENT_LIMIT)
	*p = x
	return p
}
func (x DAILY_SETTLEMENT_LIMIT) String() string {
	return proto.EnumName(DAILY_SETTLEMENT_LIMIT_name, int32(x))
}
func (x *DAILY_SETTLEMENT_LIMIT) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(DAILY_SETTLEMENT_LIMIT_value, data, "DAILY_SETTLEMENT_LIMIT")
	if err != nil {
		return err
	}
	*x = DAILY_SETTLEMENT_LIMIT(value)
	return nil
}
func (DAILY_SETTLEMENT_LIMIT) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGuildwelfare, []int{1}
}

type FirstJoinActivityInfo_Type int32

const (
	FirstJoinActivityInfo_ALL_GUILD FirstJoinActivityInfo_Type = 0
	FirstJoinActivityInfo_NEW_GUILD FirstJoinActivityInfo_Type = 1
)

var FirstJoinActivityInfo_Type_name = map[int32]string{
	0: "ALL_GUILD",
	1: "NEW_GUILD",
}
var FirstJoinActivityInfo_Type_value = map[string]int32{
	"ALL_GUILD": 0,
	"NEW_GUILD": 1,
}

func (x FirstJoinActivityInfo_Type) Enum() *FirstJoinActivityInfo_Type {
	p := new(FirstJoinActivityInfo_Type)
	*p = x
	return p
}
func (x FirstJoinActivityInfo_Type) String() string {
	return proto.EnumName(FirstJoinActivityInfo_Type_name, int32(x))
}
func (x *FirstJoinActivityInfo_Type) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(FirstJoinActivityInfo_Type_value, data, "FirstJoinActivityInfo_Type")
	if err != nil {
		return err
	}
	*x = FirstJoinActivityInfo_Type(value)
	return nil
}
func (FirstJoinActivityInfo_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGuildwelfare, []int{9, 0}
}

// 用户首次加入公会
type AddGuildFirstJoinReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	Uid     uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
}

func (m *AddGuildFirstJoinReq) Reset()                    { *m = AddGuildFirstJoinReq{} }
func (m *AddGuildFirstJoinReq) String() string            { return proto.CompactTextString(m) }
func (*AddGuildFirstJoinReq) ProtoMessage()               {}
func (*AddGuildFirstJoinReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildwelfare, []int{0} }

func (m *AddGuildFirstJoinReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AddGuildFirstJoinReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type AddGuildFirstJoinResp struct {
}

func (m *AddGuildFirstJoinResp) Reset()         { *m = AddGuildFirstJoinResp{} }
func (m *AddGuildFirstJoinResp) String() string { return proto.CompactTextString(m) }
func (*AddGuildFirstJoinResp) ProtoMessage()    {}
func (*AddGuildFirstJoinResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildwelfare, []int{1}
}

type GuildFirstJoinCount struct {
	GuildId     uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	MemberCount uint32 `protobuf:"varint,2,req,name=member_count,json=memberCount" json:"member_count"`
}

func (m *GuildFirstJoinCount) Reset()                    { *m = GuildFirstJoinCount{} }
func (m *GuildFirstJoinCount) String() string            { return proto.CompactTextString(m) }
func (*GuildFirstJoinCount) ProtoMessage()               {}
func (*GuildFirstJoinCount) Descriptor() ([]byte, []int) { return fileDescriptorGuildwelfare, []int{2} }

func (m *GuildFirstJoinCount) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildFirstJoinCount) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

// 获取指定日期新增人数最高的公会列表
type GetTopJoinGuildListReq struct {
	Timestamp uint32 `protobuf:"varint,1,req,name=timestamp" json:"timestamp"`
	Limit     uint32 `protobuf:"varint,2,req,name=limit" json:"limit"`
}

func (m *GetTopJoinGuildListReq) Reset()         { *m = GetTopJoinGuildListReq{} }
func (m *GetTopJoinGuildListReq) String() string { return proto.CompactTextString(m) }
func (*GetTopJoinGuildListReq) ProtoMessage()    {}
func (*GetTopJoinGuildListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildwelfare, []int{3}
}

func (m *GetTopJoinGuildListReq) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *GetTopJoinGuildListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetTopJoinGuildListResp struct {
	GuildList []*GuildFirstJoinCount `protobuf:"bytes,1,rep,name=guild_list,json=guildList" json:"guild_list,omitempty"`
}

func (m *GetTopJoinGuildListResp) Reset()         { *m = GetTopJoinGuildListResp{} }
func (m *GetTopJoinGuildListResp) String() string { return proto.CompactTextString(m) }
func (*GetTopJoinGuildListResp) ProtoMessage()    {}
func (*GetTopJoinGuildListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildwelfare, []int{4}
}

func (m *GetTopJoinGuildListResp) GetGuildList() []*GuildFirstJoinCount {
	if m != nil {
		return m.GuildList
	}
	return nil
}

// 获取指定日期最快达到新增30人的公会列表
type GetSoonestJoinGuildListReq struct {
	Timestamp uint32 `protobuf:"varint,1,req,name=timestamp" json:"timestamp"`
	Limit     uint32 `protobuf:"varint,2,req,name=limit" json:"limit"`
}

func (m *GetSoonestJoinGuildListReq) Reset()         { *m = GetSoonestJoinGuildListReq{} }
func (m *GetSoonestJoinGuildListReq) String() string { return proto.CompactTextString(m) }
func (*GetSoonestJoinGuildListReq) ProtoMessage()    {}
func (*GetSoonestJoinGuildListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildwelfare, []int{5}
}

func (m *GetSoonestJoinGuildListReq) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *GetSoonestJoinGuildListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetSoonestJoinGuildListResp struct {
	GuildList []uint32 `protobuf:"varint,1,rep,name=guild_list,json=guildList" json:"guild_list,omitempty"`
}

func (m *GetSoonestJoinGuildListResp) Reset()         { *m = GetSoonestJoinGuildListResp{} }
func (m *GetSoonestJoinGuildListResp) String() string { return proto.CompactTextString(m) }
func (*GetSoonestJoinGuildListResp) ProtoMessage()    {}
func (*GetSoonestJoinGuildListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildwelfare, []int{6}
}

func (m *GetSoonestJoinGuildListResp) GetGuildList() []uint32 {
	if m != nil {
		return m.GuildList
	}
	return nil
}

// 附加福利奖励
type AwardFirstJoinGuildReq struct {
	Timestamp uint32 `protobuf:"varint,1,req,name=timestamp" json:"timestamp"`
	GuildId   uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	Rank      uint32 `protobuf:"varint,3,req,name=rank" json:"rank"`
	Type      uint32 `protobuf:"varint,4,req,name=type" json:"type"`
}

func (m *AwardFirstJoinGuildReq) Reset()         { *m = AwardFirstJoinGuildReq{} }
func (m *AwardFirstJoinGuildReq) String() string { return proto.CompactTextString(m) }
func (*AwardFirstJoinGuildReq) ProtoMessage()    {}
func (*AwardFirstJoinGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildwelfare, []int{7}
}

func (m *AwardFirstJoinGuildReq) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *AwardFirstJoinGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AwardFirstJoinGuildReq) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *AwardFirstJoinGuildReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type AwardFirstJoinGuildResp struct {
}

func (m *AwardFirstJoinGuildResp) Reset()         { *m = AwardFirstJoinGuildResp{} }
func (m *AwardFirstJoinGuildResp) String() string { return proto.CompactTextString(m) }
func (*AwardFirstJoinGuildResp) ProtoMessage()    {}
func (*AwardFirstJoinGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildwelfare, []int{8}
}

type FirstJoinActivityInfo struct {
	ActivityId uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
	Name       string `protobuf:"bytes,2,req,name=name" json:"name"`
	BeginTime  uint64 `protobuf:"varint,3,req,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime    uint64 `protobuf:"varint,4,req,name=end_time,json=endTime" json:"end_time"`
	Type       uint32 `protobuf:"varint,5,req,name=type" json:"type"`
}

func (m *FirstJoinActivityInfo) Reset()         { *m = FirstJoinActivityInfo{} }
func (m *FirstJoinActivityInfo) String() string { return proto.CompactTextString(m) }
func (*FirstJoinActivityInfo) ProtoMessage()    {}
func (*FirstJoinActivityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildwelfare, []int{9}
}

func (m *FirstJoinActivityInfo) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *FirstJoinActivityInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *FirstJoinActivityInfo) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *FirstJoinActivityInfo) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *FirstJoinActivityInfo) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

// 获取公会拉新增活动信息
type GetFirstJoinActivityInfoReq struct {
	Timestamp uint32 `protobuf:"varint,1,req,name=timestamp" json:"timestamp"`
}

func (m *GetFirstJoinActivityInfoReq) Reset()         { *m = GetFirstJoinActivityInfoReq{} }
func (m *GetFirstJoinActivityInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetFirstJoinActivityInfoReq) ProtoMessage()    {}
func (*GetFirstJoinActivityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildwelfare, []int{10}
}

func (m *GetFirstJoinActivityInfoReq) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func init() {
	proto.RegisterType((*AddGuildFirstJoinReq)(nil), "GuildWelfare.AddGuildFirstJoinReq")
	proto.RegisterType((*AddGuildFirstJoinResp)(nil), "GuildWelfare.AddGuildFirstJoinResp")
	proto.RegisterType((*GuildFirstJoinCount)(nil), "GuildWelfare.GuildFirstJoinCount")
	proto.RegisterType((*GetTopJoinGuildListReq)(nil), "GuildWelfare.GetTopJoinGuildListReq")
	proto.RegisterType((*GetTopJoinGuildListResp)(nil), "GuildWelfare.GetTopJoinGuildListResp")
	proto.RegisterType((*GetSoonestJoinGuildListReq)(nil), "GuildWelfare.GetSoonestJoinGuildListReq")
	proto.RegisterType((*GetSoonestJoinGuildListResp)(nil), "GuildWelfare.GetSoonestJoinGuildListResp")
	proto.RegisterType((*AwardFirstJoinGuildReq)(nil), "GuildWelfare.AwardFirstJoinGuildReq")
	proto.RegisterType((*AwardFirstJoinGuildResp)(nil), "GuildWelfare.AwardFirstJoinGuildResp")
	proto.RegisterType((*FirstJoinActivityInfo)(nil), "GuildWelfare.FirstJoinActivityInfo")
	proto.RegisterType((*GetFirstJoinActivityInfoReq)(nil), "GuildWelfare.GetFirstJoinActivityInfoReq")
	proto.RegisterEnum("GuildWelfare.WELFARE_TYPE", WELFARE_TYPE_name, WELFARE_TYPE_value)
	proto.RegisterEnum("GuildWelfare.DAILY_SETTLEMENT_LIMIT", DAILY_SETTLEMENT_LIMIT_name, DAILY_SETTLEMENT_LIMIT_value)
	proto.RegisterEnum("GuildWelfare.FirstJoinActivityInfo_Type", FirstJoinActivityInfo_Type_name, FirstJoinActivityInfo_Type_value)
}
func (m *AddGuildFirstJoinReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddGuildFirstJoinReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildwelfare(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildwelfare(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *AddGuildFirstJoinResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddGuildFirstJoinResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GuildFirstJoinCount) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildFirstJoinCount) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildwelfare(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildwelfare(dAtA, i, uint64(m.MemberCount))
	return i, nil
}

func (m *GetTopJoinGuildListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTopJoinGuildListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildwelfare(dAtA, i, uint64(m.Timestamp))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildwelfare(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GetTopJoinGuildListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTopJoinGuildListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GuildList) > 0 {
		for _, msg := range m.GuildList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGuildwelfare(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetSoonestJoinGuildListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSoonestJoinGuildListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildwelfare(dAtA, i, uint64(m.Timestamp))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildwelfare(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GetSoonestJoinGuildListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSoonestJoinGuildListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GuildList) > 0 {
		for _, num := range m.GuildList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGuildwelfare(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *AwardFirstJoinGuildReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AwardFirstJoinGuildReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildwelfare(dAtA, i, uint64(m.Timestamp))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildwelfare(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildwelfare(dAtA, i, uint64(m.Rank))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildwelfare(dAtA, i, uint64(m.Type))
	return i, nil
}

func (m *AwardFirstJoinGuildResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AwardFirstJoinGuildResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *FirstJoinActivityInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FirstJoinActivityInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildwelfare(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGuildwelfare(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildwelfare(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildwelfare(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildwelfare(dAtA, i, uint64(m.Type))
	return i, nil
}

func (m *GetFirstJoinActivityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFirstJoinActivityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildwelfare(dAtA, i, uint64(m.Timestamp))
	return i, nil
}

func encodeFixed64Guildwelfare(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Guildwelfare(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintGuildwelfare(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *AddGuildFirstJoinReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildwelfare(uint64(m.GuildId))
	n += 1 + sovGuildwelfare(uint64(m.Uid))
	return n
}

func (m *AddGuildFirstJoinResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GuildFirstJoinCount) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildwelfare(uint64(m.GuildId))
	n += 1 + sovGuildwelfare(uint64(m.MemberCount))
	return n
}

func (m *GetTopJoinGuildListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildwelfare(uint64(m.Timestamp))
	n += 1 + sovGuildwelfare(uint64(m.Limit))
	return n
}

func (m *GetTopJoinGuildListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GuildList) > 0 {
		for _, e := range m.GuildList {
			l = e.Size()
			n += 1 + l + sovGuildwelfare(uint64(l))
		}
	}
	return n
}

func (m *GetSoonestJoinGuildListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildwelfare(uint64(m.Timestamp))
	n += 1 + sovGuildwelfare(uint64(m.Limit))
	return n
}

func (m *GetSoonestJoinGuildListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GuildList) > 0 {
		for _, e := range m.GuildList {
			n += 1 + sovGuildwelfare(uint64(e))
		}
	}
	return n
}

func (m *AwardFirstJoinGuildReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildwelfare(uint64(m.Timestamp))
	n += 1 + sovGuildwelfare(uint64(m.GuildId))
	n += 1 + sovGuildwelfare(uint64(m.Rank))
	n += 1 + sovGuildwelfare(uint64(m.Type))
	return n
}

func (m *AwardFirstJoinGuildResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *FirstJoinActivityInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildwelfare(uint64(m.ActivityId))
	l = len(m.Name)
	n += 1 + l + sovGuildwelfare(uint64(l))
	n += 1 + sovGuildwelfare(uint64(m.BeginTime))
	n += 1 + sovGuildwelfare(uint64(m.EndTime))
	n += 1 + sovGuildwelfare(uint64(m.Type))
	return n
}

func (m *GetFirstJoinActivityInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildwelfare(uint64(m.Timestamp))
	return n
}

func sovGuildwelfare(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozGuildwelfare(x uint64) (n int) {
	return sovGuildwelfare(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *AddGuildFirstJoinReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildwelfare
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddGuildFirstJoinReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddGuildFirstJoinReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildwelfare(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildwelfare
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddGuildFirstJoinResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildwelfare
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddGuildFirstJoinResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddGuildFirstJoinResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGuildwelfare(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildwelfare
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildFirstJoinCount) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildwelfare
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildFirstJoinCount: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildFirstJoinCount: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberCount", wireType)
			}
			m.MemberCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemberCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildwelfare(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildwelfare
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("member_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTopJoinGuildListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildwelfare
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTopJoinGuildListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTopJoinGuildListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildwelfare(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildwelfare
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("timestamp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTopJoinGuildListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildwelfare
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTopJoinGuildListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTopJoinGuildListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildwelfare
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GuildList = append(m.GuildList, &GuildFirstJoinCount{})
			if err := m.GuildList[len(m.GuildList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildwelfare(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildwelfare
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSoonestJoinGuildListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildwelfare
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSoonestJoinGuildListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSoonestJoinGuildListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildwelfare(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildwelfare
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("timestamp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSoonestJoinGuildListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildwelfare
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSoonestJoinGuildListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSoonestJoinGuildListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildwelfare
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GuildList = append(m.GuildList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildwelfare
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGuildwelfare
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGuildwelfare
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GuildList = append(m.GuildList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildwelfare(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildwelfare
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AwardFirstJoinGuildReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildwelfare
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AwardFirstJoinGuildReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AwardFirstJoinGuildReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rank", wireType)
			}
			m.Rank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildwelfare(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildwelfare
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("timestamp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("rank")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AwardFirstJoinGuildResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildwelfare
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AwardFirstJoinGuildResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AwardFirstJoinGuildResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGuildwelfare(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildwelfare
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FirstJoinActivityInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildwelfare
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FirstJoinActivityInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FirstJoinActivityInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildwelfare
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildwelfare(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildwelfare
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("activity_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("begin_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("end_time")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFirstJoinActivityInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildwelfare
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFirstJoinActivityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFirstJoinActivityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildwelfare(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildwelfare
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("timestamp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipGuildwelfare(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGuildwelfare
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGuildwelfare
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthGuildwelfare
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGuildwelfare
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGuildwelfare(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGuildwelfare = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGuildwelfare   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("services/guildwelfare/guildwelfare.proto", fileDescriptorGuildwelfare)
}

var fileDescriptorGuildwelfare = []byte{
	// 815 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x94, 0xcf, 0x6f, 0x1a, 0x47,
	0x14, 0xc7, 0x59, 0x76, 0x49, 0xcc, 0x33, 0xae, 0xe8, 0xb8, 0xc1, 0x84, 0xaa, 0x78, 0xbb, 0x71,
	0x54, 0x12, 0x15, 0x3b, 0xca, 0x11, 0x51, 0xd4, 0x4d, 0xbc, 0xa1, 0x44, 0x1b, 0x88, 0x60, 0x2d,
	0x6a, 0xb5, 0xd2, 0x0a, 0xb3, 0x63, 0x34, 0x32, 0xfb, 0xa3, 0xbb, 0x83, 0x2d, 0xdf, 0x7a, 0x6b,
	0xd5, 0x4a, 0x95, 0xdb, 0x73, 0x8f, 0xdc, 0xfa, 0x8f, 0xf8, 0xd8, 0x63, 0x4f, 0x56, 0xe5, 0x5e,
	0xf8, 0x33, 0xaa, 0x99, 0x35, 0x36, 0x0b, 0x8b, 0xcc, 0x21, 0xc7, 0xf9, 0xbe, 0x37, 0xef, 0x7d,
	0xde, 0xbc, 0x79, 0x0f, 0x4a, 0x01, 0xf6, 0x4f, 0x49, 0x1f, 0x07, 0x7b, 0x83, 0x11, 0x19, 0x5a,
	0x67, 0x78, 0x78, 0xdc, 0xf3, 0x71, 0xe4, 0xb0, 0xeb, 0xf9, 0x2e, 0x75, 0x51, 0xa6, 0xce, 0xb4,
	0x6e, 0xa8, 0x15, 0x76, 0xfa, 0xae, 0x6d, 0xbb, 0xce, 0x1e, 0x1d, 0x9e, 0x7a, 0xa4, 0x7f, 0x32,
	0xc4, 0x7b, 0xc1, 0xc9, 0xd1, 0x88, 0x0c, 0x29, 0x71, 0xe8, 0xb9, 0x77, 0x73, 0x47, 0x69, 0xc1,
	0x27, 0xaa, 0x65, 0xf1, 0x8b, 0x6f, 0x88, 0x1f, 0xd0, 0xb7, 0x2e, 0x71, 0xda, 0xf8, 0x07, 0xb4,
	0x0d, 0x6b, 0x3c, 0x83, 0x49, 0xac, 0xbc, 0x20, 0x27, 0x4b, 0x1b, 0xaf, 0xa4, 0xcb, 0xab, 0xed,
	0x44, 0xfb, 0x21, 0x57, 0x1b, 0x16, 0xca, 0x81, 0x38, 0x22, 0x56, 0x3e, 0x39, 0x63, 0x63, 0x82,
	0xb2, 0x05, 0x8f, 0x62, 0x02, 0x06, 0x9e, 0x62, 0xc2, 0x66, 0x54, 0x7d, 0xed, 0x8e, 0x1c, 0x7a,
	0x7f, 0xa2, 0x2f, 0x20, 0x63, 0x63, 0xfb, 0x08, 0xfb, 0x66, 0x9f, 0x5d, 0x88, 0x64, 0x5c, 0x0f,
	0x2d, 0x3c, 0x92, 0xf2, 0x2d, 0xe4, 0xea, 0x98, 0x1a, 0xae, 0xc7, 0x82, 0xf3, 0x54, 0x3a, 0x09,
	0x28, 0x2b, 0x46, 0x81, 0x34, 0x25, 0x36, 0x0e, 0x68, 0xcf, 0xf6, 0x22, 0x49, 0xee, 0x64, 0x54,
	0x80, 0xd4, 0x90, 0xd8, 0x24, 0x1a, 0x3f, 0x94, 0x94, 0xef, 0x60, 0x2b, 0x36, 0x72, 0xe0, 0xa1,
	0xaf, 0x01, 0x42, 0xfc, 0x21, 0x09, 0x68, 0x5e, 0x90, 0xc5, 0xd2, 0xfa, 0xcb, 0xcf, 0x77, 0x67,
	0x1b, 0xb1, 0x1b, 0x53, 0x75, 0x3b, 0x3d, 0x98, 0x46, 0x51, 0xbe, 0x87, 0x42, 0x1d, 0xd3, 0x8e,
	0xeb, 0x3a, 0x38, 0xf4, 0xf8, 0xa0, 0xe8, 0x55, 0xf8, 0x74, 0x69, 0xf4, 0xc0, 0x43, 0x9f, 0x2d,
	0xe0, 0x6f, 0xcc, 0xb2, 0xfd, 0x2e, 0x40, 0x4e, 0x3d, 0xeb, 0xf9, 0x77, 0xf8, 0xfc, 0xfa, 0xaa,
	0x60, 0xb3, 0xbd, 0x4d, 0xc6, 0xf5, 0x36, 0x0f, 0x92, 0xdf, 0x73, 0x4e, 0xf2, 0xe2, 0x8c, 0x91,
	0x2b, 0xcc, 0xc2, 0x7e, 0x69, 0x5e, 0x9a, 0xb5, 0x30, 0x45, 0x79, 0x0c, 0x5b, 0xb1, 0x48, 0x81,
	0xa7, 0x5c, 0x09, 0xf0, 0xe8, 0x56, 0x56, 0xfb, 0x94, 0x9c, 0x12, 0x7a, 0xde, 0x70, 0x8e, 0x5d,
	0xf4, 0x14, 0xd6, 0x7b, 0x37, 0xe7, 0xf9, 0x8f, 0x06, 0x53, 0x43, 0xc8, 0xe3, 0xf4, 0x6c, 0xcc,
	0x61, 0xd3, 0xd3, 0xac, 0x4c, 0x41, 0x4f, 0x00, 0x8e, 0xf0, 0x80, 0x38, 0x26, 0xab, 0x8e, 0xf3,
	0x4a, 0xd3, 0x7a, 0xb9, 0x6e, 0x10, 0x1b, 0xb3, 0x7a, 0xb1, 0x63, 0x85, 0x2e, 0xd2, 0x8c, 0xcb,
	0x43, 0xec, 0x58, 0xdc, 0x61, 0x5a, 0x55, 0x6a, 0xa1, 0xaa, 0x1d, 0x90, 0x8c, 0x73, 0x0f, 0xa3,
	0x0d, 0x48, 0xab, 0xba, 0x6e, 0xd6, 0x0f, 0x1a, 0xfa, 0x7e, 0x36, 0xc1, 0x8e, 0x4d, 0xad, 0x7b,
	0x73, 0x14, 0x14, 0x95, 0x77, 0x33, 0xb6, 0xc4, 0x15, 0x7b, 0xf2, 0xfc, 0x2b, 0xc8, 0x74, 0x35,
	0xfd, 0x8d, 0xda, 0xd6, 0x4c, 0xe3, 0xf0, 0xbd, 0xc6, 0x32, 0x68, 0xea, 0xeb, 0x6f, 0xcc, 0x83,
	0x8e, 0xd6, 0xce, 0x26, 0x50, 0x16, 0x32, 0x9d, 0x56, 0xab, 0xa9, 0x75, 0x0c, 0xf3, 0x6d, 0xab,
	0xd1, 0xcc, 0x0a, 0x28, 0x03, 0x6b, 0x46, 0xeb, 0x7d, 0x78, 0x4a, 0x3e, 0x7f, 0x01, 0xb9, 0x7d,
	0xb5, 0xa1, 0x1f, 0x9a, 0x1d, 0xcd, 0x30, 0x74, 0xed, 0x9d, 0xd6, 0x34, 0x4c, 0xbd, 0xf1, 0xae,
	0x61, 0xa0, 0x1c, 0x20, 0xb5, 0xab, 0xb6, 0xf7, 0x43, 0xd8, 0x4e, 0xa8, 0x66, 0x53, 0x2f, 0x7f,
	0x7b, 0x00, 0x91, 0xc5, 0x84, 0x7e, 0x12, 0xe0, 0xe3, 0x85, 0x15, 0x81, 0x94, 0xe8, 0xd0, 0xc4,
	0x2d, 0xa5, 0xc2, 0x93, 0x7b, 0x7d, 0x02, 0x4f, 0x79, 0xf6, 0xe3, 0x78, 0x22, 0x0a, 0xbf, 0x8c,
	0x27, 0xa2, 0x34, 0xaa, 0x0c, 0x2a, 0x7f, 0x8c, 0x27, 0x62, 0xae, 0x3c, 0x92, 0xab, 0x23, 0x62,
	0xd5, 0xe4, 0xf2, 0x40, 0xae, 0x4e, 0xbf, 0x64, 0x0d, 0x5d, 0x08, 0xb0, 0x19, 0x33, 0xd8, 0x68,
	0x67, 0x6e, 0x80, 0x63, 0xb7, 0x4a, 0xe1, 0xe9, 0x0a, 0x5e, 0x81, 0xa7, 0x7c, 0xc9, 0x78, 0x92,
	0x9c, 0x87, 0x56, 0x1c, 0xce, 0xf3, 0xb8, 0x4c, 0xe5, 0xea, 0x6d, 0x4b, 0x6a, 0x72, 0xd9, 0x91,
	0xab, 0x7c, 0x5c, 0x6b, 0xe8, 0x4f, 0x81, 0xef, 0x9a, 0xb8, 0x81, 0x45, 0xa5, 0x85, 0x84, 0x4b,
	0xb6, 0x46, 0xe1, 0xd9, 0x8a, 0x9e, 0x53, 0x3c, 0x71, 0x55, 0xbc, 0xbf, 0x04, 0xd8, 0x8c, 0x99,
	0xbe, 0xf9, 0x17, 0x8b, 0xdf, 0x19, 0xf3, 0x2f, 0xb6, 0x6c, 0x8c, 0xeb, 0x0c, 0x49, 0x62, 0x48,
	0x6b, 0x83, 0x0a, 0xad, 0xf8, 0x15, 0x8f, 0x63, 0xbd, 0x88, 0x34, 0x4f, 0x9e, 0x87, 0xf4, 0xe5,
	0x2a, 0xdb, 0x1c, 0x35, 0xb9, 0xec, 0xc9, 0x55, 0x36, 0x53, 0x35, 0xf4, 0xab, 0x00, 0xf9, 0x65,
	0xf3, 0x82, 0x16, 0xdf, 0x68, 0xd9, 0x5c, 0xcd, 0xff, 0xbb, 0x58, 0x3f, 0x65, 0x9b, 0x51, 0xa7,
	0x18, 0x75, 0x92, 0x72, 0xde, 0x8f, 0xa2, 0x84, 0x85, 0x07, 0x3f, 0x8f, 0x27, 0xe2, 0x3f, 0x67,
	0xaf, 0xb2, 0x97, 0xd7, 0x45, 0xe1, 0xef, 0xeb, 0xa2, 0xf0, 0xef, 0x75, 0x51, 0xb8, 0xf8, 0xaf,
	0x98, 0xf8, 0x3f, 0x00, 0x00, 0xff, 0xff, 0xa8, 0x3c, 0xdb, 0x5f, 0xe3, 0x07, 0x00, 0x00,
}
