// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-im-go/channel-im-go.proto

package channel_im_go // import "golang.52tt.com/protocol/services/channel-im-go"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SendChannelImNotifyMsgReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChId                 uint32   `protobuf:"varint,2,opt,name=ch_id,json=chId,proto3" json:"ch_id,omitempty"`
	ChannelType          uint32   `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	OptPbInfo            []byte   `protobuf:"bytes,5,opt,name=opt_pb_info,json=optPbInfo,proto3" json:"opt_pb_info,omitempty"`
	AppId                uint32   `protobuf:"varint,6,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	BindId               uint32   `protobuf:"varint,7,opt,name=bind_id,json=bindId,proto3" json:"bind_id,omitempty"`
	ChannelBoxId         uint32   `protobuf:"varint,8,opt,name=channel_box_id,json=channelBoxId,proto3" json:"channel_box_id,omitempty"`
	SeqId                uint32   `protobuf:"varint,9,opt,name=seq_id,json=seqId,proto3" json:"seq_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendChannelImNotifyMsgReq) Reset()         { *m = SendChannelImNotifyMsgReq{} }
func (m *SendChannelImNotifyMsgReq) String() string { return proto.CompactTextString(m) }
func (*SendChannelImNotifyMsgReq) ProtoMessage()    {}
func (*SendChannelImNotifyMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_im_go_149ab96116c469ea, []int{0}
}
func (m *SendChannelImNotifyMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendChannelImNotifyMsgReq.Unmarshal(m, b)
}
func (m *SendChannelImNotifyMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendChannelImNotifyMsgReq.Marshal(b, m, deterministic)
}
func (dst *SendChannelImNotifyMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendChannelImNotifyMsgReq.Merge(dst, src)
}
func (m *SendChannelImNotifyMsgReq) XXX_Size() int {
	return xxx_messageInfo_SendChannelImNotifyMsgReq.Size(m)
}
func (m *SendChannelImNotifyMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendChannelImNotifyMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendChannelImNotifyMsgReq proto.InternalMessageInfo

func (m *SendChannelImNotifyMsgReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendChannelImNotifyMsgReq) GetChId() uint32 {
	if m != nil {
		return m.ChId
	}
	return 0
}

func (m *SendChannelImNotifyMsgReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *SendChannelImNotifyMsgReq) GetOptPbInfo() []byte {
	if m != nil {
		return m.OptPbInfo
	}
	return nil
}

func (m *SendChannelImNotifyMsgReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *SendChannelImNotifyMsgReq) GetBindId() uint32 {
	if m != nil {
		return m.BindId
	}
	return 0
}

func (m *SendChannelImNotifyMsgReq) GetChannelBoxId() uint32 {
	if m != nil {
		return m.ChannelBoxId
	}
	return 0
}

func (m *SendChannelImNotifyMsgReq) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

type SendChannelImNotifyMsgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendChannelImNotifyMsgResp) Reset()         { *m = SendChannelImNotifyMsgResp{} }
func (m *SendChannelImNotifyMsgResp) String() string { return proto.CompactTextString(m) }
func (*SendChannelImNotifyMsgResp) ProtoMessage()    {}
func (*SendChannelImNotifyMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_im_go_149ab96116c469ea, []int{1}
}
func (m *SendChannelImNotifyMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendChannelImNotifyMsgResp.Unmarshal(m, b)
}
func (m *SendChannelImNotifyMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendChannelImNotifyMsgResp.Marshal(b, m, deterministic)
}
func (dst *SendChannelImNotifyMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendChannelImNotifyMsgResp.Merge(dst, src)
}
func (m *SendChannelImNotifyMsgResp) XXX_Size() int {
	return xxx_messageInfo_SendChannelImNotifyMsgResp.Size(m)
}
func (m *SendChannelImNotifyMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendChannelImNotifyMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendChannelImNotifyMsgResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*SendChannelImNotifyMsgReq)(nil), "channel_im_go.SendChannelImNotifyMsgReq")
	proto.RegisterType((*SendChannelImNotifyMsgResp)(nil), "channel_im_go.SendChannelImNotifyMsgResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelImGoClient is the client API for ChannelImGo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelImGoClient interface {
	SendChannelImNotifyMsg(ctx context.Context, in *SendChannelImNotifyMsgReq, opts ...grpc.CallOption) (*SendChannelImNotifyMsgResp, error)
}

type channelImGoClient struct {
	cc *grpc.ClientConn
}

func NewChannelImGoClient(cc *grpc.ClientConn) ChannelImGoClient {
	return &channelImGoClient{cc}
}

func (c *channelImGoClient) SendChannelImNotifyMsg(ctx context.Context, in *SendChannelImNotifyMsgReq, opts ...grpc.CallOption) (*SendChannelImNotifyMsgResp, error) {
	out := new(SendChannelImNotifyMsgResp)
	err := c.cc.Invoke(ctx, "/channel_im_go.ChannelImGo/SendChannelImNotifyMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelImGoServer is the server API for ChannelImGo service.
type ChannelImGoServer interface {
	SendChannelImNotifyMsg(context.Context, *SendChannelImNotifyMsgReq) (*SendChannelImNotifyMsgResp, error)
}

func RegisterChannelImGoServer(s *grpc.Server, srv ChannelImGoServer) {
	s.RegisterService(&_ChannelImGo_serviceDesc, srv)
}

func _ChannelImGo_SendChannelImNotifyMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendChannelImNotifyMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelImGoServer).SendChannelImNotifyMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_im_go.ChannelImGo/SendChannelImNotifyMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelImGoServer).SendChannelImNotifyMsg(ctx, req.(*SendChannelImNotifyMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelImGo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_im_go.ChannelImGo",
	HandlerType: (*ChannelImGoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendChannelImNotifyMsg",
			Handler:    _ChannelImGo_SendChannelImNotifyMsg_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-im-go/channel-im-go.proto",
}

func init() {
	proto.RegisterFile("channel-im-go/channel-im-go.proto", fileDescriptor_channel_im_go_149ab96116c469ea)
}

var fileDescriptor_channel_im_go_149ab96116c469ea = []byte{
	// 313 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x91, 0xc1, 0x4e, 0x02, 0x31,
	0x10, 0x86, 0x5d, 0x85, 0x45, 0x0a, 0x18, 0x53, 0xa3, 0xae, 0xc4, 0x18, 0x20, 0x1e, 0xf0, 0xc0,
	0x6e, 0xc4, 0xf8, 0x02, 0x78, 0x30, 0x3d, 0x68, 0x0c, 0x7a, 0xf2, 0xb2, 0xd9, 0xdd, 0x96, 0xa5,
	0x09, 0xdb, 0x19, 0x68, 0x35, 0x70, 0xf1, 0xa1, 0x7d, 0x02, 0xd3, 0x02, 0x26, 0x9b, 0x48, 0xe2,
	0xad, 0xf3, 0xfd, 0x7f, 0xff, 0x76, 0x66, 0x48, 0x37, 0x9b, 0x26, 0x4a, 0x89, 0xd9, 0x40, 0x16,
	0x83, 0x1c, 0xa2, 0x52, 0x15, 0xe2, 0x02, 0x0c, 0xd0, 0xd6, 0x06, 0xc6, 0xb2, 0x88, 0x73, 0xe8,
	0x7d, 0x7b, 0xe4, 0xe2, 0x55, 0x28, 0xfe, 0xb0, 0xa6, 0xac, 0x78, 0x06, 0x23, 0x27, 0xab, 0x27,
	0x9d, 0x8f, 0xc5, 0x9c, 0x1e, 0x93, 0x83, 0x0f, 0xc9, 0x03, 0xaf, 0xe3, 0xf5, 0x5b, 0x63, 0x7b,
	0xa4, 0x27, 0xa4, 0x9a, 0x4d, 0x63, 0xc9, 0x83, 0x7d, 0xc7, 0x2a, 0xd9, 0x94, 0x71, 0xda, 0x25,
	0xcd, 0x6d, 0xaa, 0x59, 0xa1, 0x08, 0x2a, 0x4e, 0x6b, 0x6c, 0xd8, 0xdb, 0x0a, 0x05, 0xbd, 0x22,
	0x0d, 0x40, 0x13, 0x63, 0x1a, 0x4b, 0x35, 0x81, 0xa0, 0xda, 0xf1, 0xfa, 0xcd, 0x71, 0x1d, 0xd0,
	0xbc, 0xa4, 0x4c, 0x4d, 0x80, 0x9e, 0x12, 0x3f, 0x41, 0xb4, 0xc1, 0xbe, 0xbb, 0x5c, 0x4d, 0x10,
	0x19, 0xa7, 0xe7, 0xa4, 0x96, 0x4a, 0xc5, 0x2d, 0xaf, 0x39, 0xee, 0xdb, 0x92, 0x71, 0x7a, 0x4d,
	0x8e, 0xb6, 0x4f, 0xa6, 0xb0, 0xb4, 0xfa, 0xa1, 0xd3, 0xb7, 0x1f, 0x19, 0xc1, 0x92, 0x71, 0x9b,
	0xaa, 0xc5, 0xdc, 0xaa, 0xf5, 0x75, 0xaa, 0x16, 0x73, 0xc6, 0x7b, 0x97, 0xa4, 0xbd, 0xab, 0x67,
	0x8d, 0xc3, 0x2f, 0xd2, 0xf8, 0x55, 0x1e, 0x81, 0x02, 0x39, 0xfb, 0xdb, 0x4c, 0xfb, 0x61, 0x69,
	0x96, 0xe1, 0xce, 0x39, 0xb6, 0x6f, 0xfe, 0xe9, 0xd4, 0xd8, 0xdb, 0x1b, 0xdd, 0xbe, 0x47, 0x39,
	0xcc, 0x12, 0x95, 0x87, 0xf7, 0x43, 0x63, 0xc2, 0x0c, 0x8a, 0xc8, 0xad, 0x2e, 0x83, 0x59, 0xa4,
	0xc5, 0xe2, 0x53, 0x66, 0x42, 0x97, 0x57, 0x9b, 0xfa, 0xce, 0x70, 0xf7, 0x13, 0x00, 0x00, 0xff,
	0xff, 0xc3, 0x94, 0xcd, 0x21, 0x00, 0x02, 0x00, 0x00,
}
