// Code generated by protoc-gen-go. DO NOT EDIT.
// source: levelup-present/levelup-present.proto

package levelup_present // import "golang.52tt.com/protocol/services/levelup-present"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type EmptyMsg struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EmptyMsg) Reset()         { *m = EmptyMsg{} }
func (m *EmptyMsg) String() string { return proto.CompactTextString(m) }
func (*EmptyMsg) ProtoMessage()    {}
func (*EmptyMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{0}
}
func (m *EmptyMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmptyMsg.Unmarshal(m, b)
}
func (m *EmptyMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmptyMsg.Marshal(b, m, deterministic)
}
func (dst *EmptyMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmptyMsg.Merge(dst, src)
}
func (m *EmptyMsg) XXX_Size() int {
	return xxx_messageInfo_EmptyMsg.Size(m)
}
func (m *EmptyMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_EmptyMsg.DiscardUnknown(m)
}

var xxx_messageInfo_EmptyMsg proto.InternalMessageInfo

type ItemReq struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ItemReq) Reset()         { *m = ItemReq{} }
func (m *ItemReq) String() string { return proto.CompactTextString(m) }
func (*ItemReq) ProtoMessage()    {}
func (*ItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{1}
}
func (m *ItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ItemReq.Unmarshal(m, b)
}
func (m *ItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ItemReq.Marshal(b, m, deterministic)
}
func (dst *ItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ItemReq.Merge(dst, src)
}
func (m *ItemReq) XXX_Size() int {
	return xxx_messageInfo_ItemReq.Size(m)
}
func (m *ItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_ItemReq proto.InternalMessageInfo

func (m *ItemReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type ItemLevelReq struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	Version              uint32   `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ItemLevelReq) Reset()         { *m = ItemLevelReq{} }
func (m *ItemLevelReq) String() string { return proto.CompactTextString(m) }
func (*ItemLevelReq) ProtoMessage()    {}
func (*ItemLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{2}
}
func (m *ItemLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ItemLevelReq.Unmarshal(m, b)
}
func (m *ItemLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ItemLevelReq.Marshal(b, m, deterministic)
}
func (dst *ItemLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ItemLevelReq.Merge(dst, src)
}
func (m *ItemLevelReq) XXX_Size() int {
	return xxx_messageInfo_ItemLevelReq.Size(m)
}
func (m *ItemLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ItemLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_ItemLevelReq proto.InternalMessageInfo

func (m *ItemLevelReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *ItemLevelReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *ItemLevelReq) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *ItemLevelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ItemRes struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ItemRes) Reset()         { *m = ItemRes{} }
func (m *ItemRes) String() string { return proto.CompactTextString(m) }
func (*ItemRes) ProtoMessage()    {}
func (*ItemRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{3}
}
func (m *ItemRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ItemRes.Unmarshal(m, b)
}
func (m *ItemRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ItemRes.Marshal(b, m, deterministic)
}
func (dst *ItemRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ItemRes.Merge(dst, src)
}
func (m *ItemRes) XXX_Size() int {
	return xxx_messageInfo_ItemRes.Size(m)
}
func (m *ItemRes) XXX_DiscardUnknown() {
	xxx_messageInfo_ItemRes.DiscardUnknown(m)
}

var xxx_messageInfo_ItemRes proto.InternalMessageInfo

func (m *ItemRes) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type PushRes struct {
	Push                 bool     `protobuf:"varint,1,opt,name=push,proto3" json:"push,omitempty"`
	Rank                 uint32   `protobuf:"varint,2,opt,name=rank,proto3" json:"rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushRes) Reset()         { *m = PushRes{} }
func (m *PushRes) String() string { return proto.CompactTextString(m) }
func (*PushRes) ProtoMessage()    {}
func (*PushRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{4}
}
func (m *PushRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushRes.Unmarshal(m, b)
}
func (m *PushRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushRes.Marshal(b, m, deterministic)
}
func (dst *PushRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushRes.Merge(dst, src)
}
func (m *PushRes) XXX_Size() int {
	return xxx_messageInfo_PushRes.Size(m)
}
func (m *PushRes) XXX_DiscardUnknown() {
	xxx_messageInfo_PushRes.DiscardUnknown(m)
}

var xxx_messageInfo_PushRes proto.InternalMessageInfo

func (m *PushRes) GetPush() bool {
	if m != nil {
		return m.Push
	}
	return false
}

func (m *PushRes) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

type ItemVersionReq struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Version              uint32   `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ItemVersionReq) Reset()         { *m = ItemVersionReq{} }
func (m *ItemVersionReq) String() string { return proto.CompactTextString(m) }
func (*ItemVersionReq) ProtoMessage()    {}
func (*ItemVersionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{5}
}
func (m *ItemVersionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ItemVersionReq.Unmarshal(m, b)
}
func (m *ItemVersionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ItemVersionReq.Marshal(b, m, deterministic)
}
func (dst *ItemVersionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ItemVersionReq.Merge(dst, src)
}
func (m *ItemVersionReq) XXX_Size() int {
	return xxx_messageInfo_ItemVersionReq.Size(m)
}
func (m *ItemVersionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ItemVersionReq.DiscardUnknown(m)
}

var xxx_messageInfo_ItemVersionReq proto.InternalMessageInfo

func (m *ItemVersionReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *ItemVersionReq) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

type OffsetTypeReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	PresentType          int32    `protobuf:"varint,3,opt,name=present_type,json=presentType,proto3" json:"present_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OffsetTypeReq) Reset()         { *m = OffsetTypeReq{} }
func (m *OffsetTypeReq) String() string { return proto.CompactTextString(m) }
func (*OffsetTypeReq) ProtoMessage()    {}
func (*OffsetTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{6}
}
func (m *OffsetTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OffsetTypeReq.Unmarshal(m, b)
}
func (m *OffsetTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OffsetTypeReq.Marshal(b, m, deterministic)
}
func (dst *OffsetTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OffsetTypeReq.Merge(dst, src)
}
func (m *OffsetTypeReq) XXX_Size() int {
	return xxx_messageInfo_OffsetTypeReq.Size(m)
}
func (m *OffsetTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OffsetTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_OffsetTypeReq proto.InternalMessageInfo

func (m *OffsetTypeReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *OffsetTypeReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *OffsetTypeReq) GetPresentType() int32 {
	if m != nil {
		return m.PresentType
	}
	return 0
}

type UidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UidReq) Reset()         { *m = UidReq{} }
func (m *UidReq) String() string { return proto.CompactTextString(m) }
func (*UidReq) ProtoMessage()    {}
func (*UidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{7}
}
func (m *UidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UidReq.Unmarshal(m, b)
}
func (m *UidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UidReq.Marshal(b, m, deterministic)
}
func (dst *UidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UidReq.Merge(dst, src)
}
func (m *UidReq) XXX_Size() int {
	return xxx_messageInfo_UidReq.Size(m)
}
func (m *UidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UidReq.DiscardUnknown(m)
}

var xxx_messageInfo_UidReq proto.InternalMessageInfo

func (m *UidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type UidItemReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ParentItemId         uint32   `protobuf:"varint,2,opt,name=parent_item_id,json=parentItemId,proto3" json:"parent_item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UidItemReq) Reset()         { *m = UidItemReq{} }
func (m *UidItemReq) String() string { return proto.CompactTextString(m) }
func (*UidItemReq) ProtoMessage()    {}
func (*UidItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{8}
}
func (m *UidItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UidItemReq.Unmarshal(m, b)
}
func (m *UidItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UidItemReq.Marshal(b, m, deterministic)
}
func (dst *UidItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UidItemReq.Merge(dst, src)
}
func (m *UidItemReq) XXX_Size() int {
	return xxx_messageInfo_UidItemReq.Size(m)
}
func (m *UidItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UidItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_UidItemReq proto.InternalMessageInfo

func (m *UidItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UidItemReq) GetParentItemId() uint32 {
	if m != nil {
		return m.ParentItemId
	}
	return 0
}

type UidItemVersionReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemId               uint32   `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Version              uint32   `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`
	ItemCount            uint32   `protobuf:"varint,4,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	OrderId              string   `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UidItemVersionReq) Reset()         { *m = UidItemVersionReq{} }
func (m *UidItemVersionReq) String() string { return proto.CompactTextString(m) }
func (*UidItemVersionReq) ProtoMessage()    {}
func (*UidItemVersionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{9}
}
func (m *UidItemVersionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UidItemVersionReq.Unmarshal(m, b)
}
func (m *UidItemVersionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UidItemVersionReq.Marshal(b, m, deterministic)
}
func (dst *UidItemVersionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UidItemVersionReq.Merge(dst, src)
}
func (m *UidItemVersionReq) XXX_Size() int {
	return xxx_messageInfo_UidItemVersionReq.Size(m)
}
func (m *UidItemVersionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UidItemVersionReq.DiscardUnknown(m)
}

var xxx_messageInfo_UidItemVersionReq proto.InternalMessageInfo

func (m *UidItemVersionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UidItemVersionReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *UidItemVersionReq) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *UidItemVersionReq) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *UidItemVersionReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type UserLevelExp struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	Exp                  uint32   `protobuf:"varint,2,opt,name=exp,proto3" json:"exp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserLevelExp) Reset()         { *m = UserLevelExp{} }
func (m *UserLevelExp) String() string { return proto.CompactTextString(m) }
func (*UserLevelExp) ProtoMessage()    {}
func (*UserLevelExp) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{10}
}
func (m *UserLevelExp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevelExp.Unmarshal(m, b)
}
func (m *UserLevelExp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevelExp.Marshal(b, m, deterministic)
}
func (dst *UserLevelExp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevelExp.Merge(dst, src)
}
func (m *UserLevelExp) XXX_Size() int {
	return xxx_messageInfo_UserLevelExp.Size(m)
}
func (m *UserLevelExp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevelExp.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevelExp proto.InternalMessageInfo

func (m *UserLevelExp) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *UserLevelExp) GetExp() uint32 {
	if m != nil {
		return m.Exp
	}
	return 0
}

type ItemBatchReq struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	BatchCount           uint32   `protobuf:"varint,2,opt,name=batch_count,json=batchCount,proto3" json:"batch_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ItemBatchReq) Reset()         { *m = ItemBatchReq{} }
func (m *ItemBatchReq) String() string { return proto.CompactTextString(m) }
func (*ItemBatchReq) ProtoMessage()    {}
func (*ItemBatchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{11}
}
func (m *ItemBatchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ItemBatchReq.Unmarshal(m, b)
}
func (m *ItemBatchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ItemBatchReq.Marshal(b, m, deterministic)
}
func (dst *ItemBatchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ItemBatchReq.Merge(dst, src)
}
func (m *ItemBatchReq) XXX_Size() int {
	return xxx_messageInfo_ItemBatchReq.Size(m)
}
func (m *ItemBatchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ItemBatchReq.DiscardUnknown(m)
}

var xxx_messageInfo_ItemBatchReq proto.InternalMessageInfo

func (m *ItemBatchReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *ItemBatchReq) GetBatchCount() uint32 {
	if m != nil {
		return m.BatchCount
	}
	return 0
}

type LevelupParentPresentData struct {
	ParentItemId         uint32   `protobuf:"varint,1,opt,name=parent_item_id,json=parentItemId,proto3" json:"parent_item_id,omitempty"`
	PresentType          uint32   `protobuf:"varint,2,opt,name=present_type,json=presentType,proto3" json:"present_type,omitempty"`
	CurrentVersion       uint32   `protobuf:"varint,3,opt,name=current_version,json=currentVersion,proto3" json:"current_version,omitempty"`
	ZipUrl               string   `protobuf:"bytes,4,opt,name=zip_url,json=zipUrl,proto3" json:"zip_url,omitempty"`
	CreateTime           uint32   `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Color_1              string   `protobuf:"bytes,7,opt,name=color_1,json=color1,proto3" json:"color_1,omitempty"`
	Color_2              string   `protobuf:"bytes,8,opt,name=color_2,json=color2,proto3" json:"color_2,omitempty"`
	LevelupBg            string   `protobuf:"bytes,9,opt,name=levelup_bg,json=levelupBg,proto3" json:"levelup_bg,omitempty"`
	LevelupNumber        string   `protobuf:"bytes,10,opt,name=levelup_number,json=levelupNumber,proto3" json:"levelup_number,omitempty"`
	LevelupLevel         string   `protobuf:"bytes,11,opt,name=levelup_level,json=levelupLevel,proto3" json:"levelup_level,omitempty"`
	CmsSuffix            string   `protobuf:"bytes,12,opt,name=cms_suffix,json=cmsSuffix,proto3" json:"cms_suffix,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelupParentPresentData) Reset()         { *m = LevelupParentPresentData{} }
func (m *LevelupParentPresentData) String() string { return proto.CompactTextString(m) }
func (*LevelupParentPresentData) ProtoMessage()    {}
func (*LevelupParentPresentData) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{12}
}
func (m *LevelupParentPresentData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelupParentPresentData.Unmarshal(m, b)
}
func (m *LevelupParentPresentData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelupParentPresentData.Marshal(b, m, deterministic)
}
func (dst *LevelupParentPresentData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelupParentPresentData.Merge(dst, src)
}
func (m *LevelupParentPresentData) XXX_Size() int {
	return xxx_messageInfo_LevelupParentPresentData.Size(m)
}
func (m *LevelupParentPresentData) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelupParentPresentData.DiscardUnknown(m)
}

var xxx_messageInfo_LevelupParentPresentData proto.InternalMessageInfo

func (m *LevelupParentPresentData) GetParentItemId() uint32 {
	if m != nil {
		return m.ParentItemId
	}
	return 0
}

func (m *LevelupParentPresentData) GetPresentType() uint32 {
	if m != nil {
		return m.PresentType
	}
	return 0
}

func (m *LevelupParentPresentData) GetCurrentVersion() uint32 {
	if m != nil {
		return m.CurrentVersion
	}
	return 0
}

func (m *LevelupParentPresentData) GetZipUrl() string {
	if m != nil {
		return m.ZipUrl
	}
	return ""
}

func (m *LevelupParentPresentData) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *LevelupParentPresentData) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *LevelupParentPresentData) GetColor_1() string {
	if m != nil {
		return m.Color_1
	}
	return ""
}

func (m *LevelupParentPresentData) GetColor_2() string {
	if m != nil {
		return m.Color_2
	}
	return ""
}

func (m *LevelupParentPresentData) GetLevelupBg() string {
	if m != nil {
		return m.LevelupBg
	}
	return ""
}

func (m *LevelupParentPresentData) GetLevelupNumber() string {
	if m != nil {
		return m.LevelupNumber
	}
	return ""
}

func (m *LevelupParentPresentData) GetLevelupLevel() string {
	if m != nil {
		return m.LevelupLevel
	}
	return ""
}

func (m *LevelupParentPresentData) GetCmsSuffix() string {
	if m != nil {
		return m.CmsSuffix
	}
	return ""
}

type LevelupParentPresentAllData struct {
	ParentItemId         uint32                     `protobuf:"varint,1,opt,name=parent_item_id,json=parentItemId,proto3" json:"parent_item_id,omitempty"`
	PresentType          uint32                     `protobuf:"varint,2,opt,name=present_type,json=presentType,proto3" json:"present_type,omitempty"`
	CurrentVersion       uint32                     `protobuf:"varint,3,opt,name=current_version,json=currentVersion,proto3" json:"current_version,omitempty"`
	ZipUrl               string                     `protobuf:"bytes,4,opt,name=zip_url,json=zipUrl,proto3" json:"zip_url,omitempty"`
	CreateTime           uint32                     `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           uint32                     `protobuf:"varint,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	LevelList            []*LevelupChildPresentData `protobuf:"bytes,7,rep,name=level_list,json=levelList,proto3" json:"level_list,omitempty"`
	Color_1              string                     `protobuf:"bytes,8,opt,name=color_1,json=color1,proto3" json:"color_1,omitempty"`
	Color_2              string                     `protobuf:"bytes,9,opt,name=color_2,json=color2,proto3" json:"color_2,omitempty"`
	LevelupBg            string                     `protobuf:"bytes,10,opt,name=levelup_bg,json=levelupBg,proto3" json:"levelup_bg,omitempty"`
	LevelupNumber        string                     `protobuf:"bytes,11,opt,name=levelup_number,json=levelupNumber,proto3" json:"levelup_number,omitempty"`
	LevelupLevel         string                     `protobuf:"bytes,12,opt,name=levelup_level,json=levelupLevel,proto3" json:"levelup_level,omitempty"`
	CmsSuffix            string                     `protobuf:"bytes,13,opt,name=cms_suffix,json=cmsSuffix,proto3" json:"cms_suffix,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *LevelupParentPresentAllData) Reset()         { *m = LevelupParentPresentAllData{} }
func (m *LevelupParentPresentAllData) String() string { return proto.CompactTextString(m) }
func (*LevelupParentPresentAllData) ProtoMessage()    {}
func (*LevelupParentPresentAllData) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{13}
}
func (m *LevelupParentPresentAllData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelupParentPresentAllData.Unmarshal(m, b)
}
func (m *LevelupParentPresentAllData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelupParentPresentAllData.Marshal(b, m, deterministic)
}
func (dst *LevelupParentPresentAllData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelupParentPresentAllData.Merge(dst, src)
}
func (m *LevelupParentPresentAllData) XXX_Size() int {
	return xxx_messageInfo_LevelupParentPresentAllData.Size(m)
}
func (m *LevelupParentPresentAllData) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelupParentPresentAllData.DiscardUnknown(m)
}

var xxx_messageInfo_LevelupParentPresentAllData proto.InternalMessageInfo

func (m *LevelupParentPresentAllData) GetParentItemId() uint32 {
	if m != nil {
		return m.ParentItemId
	}
	return 0
}

func (m *LevelupParentPresentAllData) GetPresentType() uint32 {
	if m != nil {
		return m.PresentType
	}
	return 0
}

func (m *LevelupParentPresentAllData) GetCurrentVersion() uint32 {
	if m != nil {
		return m.CurrentVersion
	}
	return 0
}

func (m *LevelupParentPresentAllData) GetZipUrl() string {
	if m != nil {
		return m.ZipUrl
	}
	return ""
}

func (m *LevelupParentPresentAllData) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *LevelupParentPresentAllData) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *LevelupParentPresentAllData) GetLevelList() []*LevelupChildPresentData {
	if m != nil {
		return m.LevelList
	}
	return nil
}

func (m *LevelupParentPresentAllData) GetColor_1() string {
	if m != nil {
		return m.Color_1
	}
	return ""
}

func (m *LevelupParentPresentAllData) GetColor_2() string {
	if m != nil {
		return m.Color_2
	}
	return ""
}

func (m *LevelupParentPresentAllData) GetLevelupBg() string {
	if m != nil {
		return m.LevelupBg
	}
	return ""
}

func (m *LevelupParentPresentAllData) GetLevelupNumber() string {
	if m != nil {
		return m.LevelupNumber
	}
	return ""
}

func (m *LevelupParentPresentAllData) GetLevelupLevel() string {
	if m != nil {
		return m.LevelupLevel
	}
	return ""
}

func (m *LevelupParentPresentAllData) GetCmsSuffix() string {
	if m != nil {
		return m.CmsSuffix
	}
	return ""
}

type LevelupChildPresentData struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ParentItemId         uint32   `protobuf:"varint,2,opt,name=parent_item_id,json=parentItemId,proto3" json:"parent_item_id,omitempty"`
	Level                uint32   `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	Exp                  uint32   `protobuf:"varint,4,opt,name=exp,proto3" json:"exp,omitempty"`
	CreateTime           uint32   `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelupChildPresentData) Reset()         { *m = LevelupChildPresentData{} }
func (m *LevelupChildPresentData) String() string { return proto.CompactTextString(m) }
func (*LevelupChildPresentData) ProtoMessage()    {}
func (*LevelupChildPresentData) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{14}
}
func (m *LevelupChildPresentData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelupChildPresentData.Unmarshal(m, b)
}
func (m *LevelupChildPresentData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelupChildPresentData.Marshal(b, m, deterministic)
}
func (dst *LevelupChildPresentData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelupChildPresentData.Merge(dst, src)
}
func (m *LevelupChildPresentData) XXX_Size() int {
	return xxx_messageInfo_LevelupChildPresentData.Size(m)
}
func (m *LevelupChildPresentData) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelupChildPresentData.DiscardUnknown(m)
}

var xxx_messageInfo_LevelupChildPresentData proto.InternalMessageInfo

func (m *LevelupChildPresentData) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *LevelupChildPresentData) GetParentItemId() uint32 {
	if m != nil {
		return m.ParentItemId
	}
	return 0
}

func (m *LevelupChildPresentData) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *LevelupChildPresentData) GetExp() uint32 {
	if m != nil {
		return m.Exp
	}
	return 0
}

func (m *LevelupChildPresentData) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *LevelupChildPresentData) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type LevelupPresentBatchData struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	BatchCount           uint32   `protobuf:"varint,2,opt,name=batch_count,json=batchCount,proto3" json:"batch_count,omitempty"`
	EffectUrl            string   `protobuf:"bytes,3,opt,name=effect_url,json=effectUrl,proto3" json:"effect_url,omitempty"`
	EffectMd5            string   `protobuf:"bytes,4,opt,name=effect_md5,json=effectMd5,proto3" json:"effect_md5,omitempty"`
	EffectDesc           string   `protobuf:"bytes,5,opt,name=effect_desc,json=effectDesc,proto3" json:"effect_desc,omitempty"`
	CreateTime           uint32   `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelupPresentBatchData) Reset()         { *m = LevelupPresentBatchData{} }
func (m *LevelupPresentBatchData) String() string { return proto.CompactTextString(m) }
func (*LevelupPresentBatchData) ProtoMessage()    {}
func (*LevelupPresentBatchData) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{15}
}
func (m *LevelupPresentBatchData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelupPresentBatchData.Unmarshal(m, b)
}
func (m *LevelupPresentBatchData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelupPresentBatchData.Marshal(b, m, deterministic)
}
func (dst *LevelupPresentBatchData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelupPresentBatchData.Merge(dst, src)
}
func (m *LevelupPresentBatchData) XXX_Size() int {
	return xxx_messageInfo_LevelupPresentBatchData.Size(m)
}
func (m *LevelupPresentBatchData) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelupPresentBatchData.DiscardUnknown(m)
}

var xxx_messageInfo_LevelupPresentBatchData proto.InternalMessageInfo

func (m *LevelupPresentBatchData) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *LevelupPresentBatchData) GetBatchCount() uint32 {
	if m != nil {
		return m.BatchCount
	}
	return 0
}

func (m *LevelupPresentBatchData) GetEffectUrl() string {
	if m != nil {
		return m.EffectUrl
	}
	return ""
}

func (m *LevelupPresentBatchData) GetEffectMd5() string {
	if m != nil {
		return m.EffectMd5
	}
	return ""
}

func (m *LevelupPresentBatchData) GetEffectDesc() string {
	if m != nil {
		return m.EffectDesc
	}
	return ""
}

func (m *LevelupPresentBatchData) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *LevelupPresentBatchData) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type LevelupPresentBatchList struct {
	List                 []*LevelupPresentBatchData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *LevelupPresentBatchList) Reset()         { *m = LevelupPresentBatchList{} }
func (m *LevelupPresentBatchList) String() string { return proto.CompactTextString(m) }
func (*LevelupPresentBatchList) ProtoMessage()    {}
func (*LevelupPresentBatchList) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{16}
}
func (m *LevelupPresentBatchList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelupPresentBatchList.Unmarshal(m, b)
}
func (m *LevelupPresentBatchList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelupPresentBatchList.Marshal(b, m, deterministic)
}
func (dst *LevelupPresentBatchList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelupPresentBatchList.Merge(dst, src)
}
func (m *LevelupPresentBatchList) XXX_Size() int {
	return xxx_messageInfo_LevelupPresentBatchList.Size(m)
}
func (m *LevelupPresentBatchList) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelupPresentBatchList.DiscardUnknown(m)
}

var xxx_messageInfo_LevelupPresentBatchList proto.InternalMessageInfo

func (m *LevelupPresentBatchList) GetList() []*LevelupPresentBatchData {
	if m != nil {
		return m.List
	}
	return nil
}

type LevelupPresentBatchDataMap struct {
	BatchMap             map[uint32]*LevelupPresentBatchList `protobuf:"bytes,1,rep,name=batch_map,json=batchMap,proto3" json:"batch_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *LevelupPresentBatchDataMap) Reset()         { *m = LevelupPresentBatchDataMap{} }
func (m *LevelupPresentBatchDataMap) String() string { return proto.CompactTextString(m) }
func (*LevelupPresentBatchDataMap) ProtoMessage()    {}
func (*LevelupPresentBatchDataMap) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{17}
}
func (m *LevelupPresentBatchDataMap) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelupPresentBatchDataMap.Unmarshal(m, b)
}
func (m *LevelupPresentBatchDataMap) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelupPresentBatchDataMap.Marshal(b, m, deterministic)
}
func (dst *LevelupPresentBatchDataMap) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelupPresentBatchDataMap.Merge(dst, src)
}
func (m *LevelupPresentBatchDataMap) XXX_Size() int {
	return xxx_messageInfo_LevelupPresentBatchDataMap.Size(m)
}
func (m *LevelupPresentBatchDataMap) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelupPresentBatchDataMap.DiscardUnknown(m)
}

var xxx_messageInfo_LevelupPresentBatchDataMap proto.InternalMessageInfo

func (m *LevelupPresentBatchDataMap) GetBatchMap() map[uint32]*LevelupPresentBatchList {
	if m != nil {
		return m.BatchMap
	}
	return nil
}

type VersionData struct {
	Version              uint32   `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	CreateTime           uint32   `protobuf:"varint,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VersionData) Reset()         { *m = VersionData{} }
func (m *VersionData) String() string { return proto.CompactTextString(m) }
func (*VersionData) ProtoMessage()    {}
func (*VersionData) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{18}
}
func (m *VersionData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VersionData.Unmarshal(m, b)
}
func (m *VersionData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VersionData.Marshal(b, m, deterministic)
}
func (dst *VersionData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VersionData.Merge(dst, src)
}
func (m *VersionData) XXX_Size() int {
	return xxx_messageInfo_VersionData.Size(m)
}
func (m *VersionData) XXX_DiscardUnknown() {
	xxx_messageInfo_VersionData.DiscardUnknown(m)
}

var xxx_messageInfo_VersionData proto.InternalMessageInfo

func (m *VersionData) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *VersionData) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type VersionList struct {
	List                 []*VersionData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *VersionList) Reset()         { *m = VersionList{} }
func (m *VersionList) String() string { return proto.CompactTextString(m) }
func (*VersionList) ProtoMessage()    {}
func (*VersionList) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{19}
}
func (m *VersionList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VersionList.Unmarshal(m, b)
}
func (m *VersionList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VersionList.Marshal(b, m, deterministic)
}
func (dst *VersionList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VersionList.Merge(dst, src)
}
func (m *VersionList) XXX_Size() int {
	return xxx_messageInfo_VersionList.Size(m)
}
func (m *VersionList) XXX_DiscardUnknown() {
	xxx_messageInfo_VersionList.DiscardUnknown(m)
}

var xxx_messageInfo_VersionList proto.InternalMessageInfo

func (m *VersionList) GetList() []*VersionData {
	if m != nil {
		return m.List
	}
	return nil
}

type UserLevelupPresentStatusMap struct {
	Map                  map[uint32]*UserLevelExp `protobuf:"bytes,1,rep,name=map,proto3" json:"map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *UserLevelupPresentStatusMap) Reset()         { *m = UserLevelupPresentStatusMap{} }
func (m *UserLevelupPresentStatusMap) String() string { return proto.CompactTextString(m) }
func (*UserLevelupPresentStatusMap) ProtoMessage()    {}
func (*UserLevelupPresentStatusMap) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{20}
}
func (m *UserLevelupPresentStatusMap) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevelupPresentStatusMap.Unmarshal(m, b)
}
func (m *UserLevelupPresentStatusMap) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevelupPresentStatusMap.Marshal(b, m, deterministic)
}
func (dst *UserLevelupPresentStatusMap) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevelupPresentStatusMap.Merge(dst, src)
}
func (m *UserLevelupPresentStatusMap) XXX_Size() int {
	return xxx_messageInfo_UserLevelupPresentStatusMap.Size(m)
}
func (m *UserLevelupPresentStatusMap) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevelupPresentStatusMap.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevelupPresentStatusMap proto.InternalMessageInfo

func (m *UserLevelupPresentStatusMap) GetMap() map[uint32]*UserLevelExp {
	if m != nil {
		return m.Map
	}
	return nil
}

type LevelupPresentParentMap struct {
	Map                  map[uint32]*LevelupParentPresentAllData `protobuf:"bytes,1,rep,name=map,proto3" json:"map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                `json:"-"`
	XXX_unrecognized     []byte                                  `json:"-"`
	XXX_sizecache        int32                                   `json:"-"`
}

func (m *LevelupPresentParentMap) Reset()         { *m = LevelupPresentParentMap{} }
func (m *LevelupPresentParentMap) String() string { return proto.CompactTextString(m) }
func (*LevelupPresentParentMap) ProtoMessage()    {}
func (*LevelupPresentParentMap) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{21}
}
func (m *LevelupPresentParentMap) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelupPresentParentMap.Unmarshal(m, b)
}
func (m *LevelupPresentParentMap) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelupPresentParentMap.Marshal(b, m, deterministic)
}
func (dst *LevelupPresentParentMap) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelupPresentParentMap.Merge(dst, src)
}
func (m *LevelupPresentParentMap) XXX_Size() int {
	return xxx_messageInfo_LevelupPresentParentMap.Size(m)
}
func (m *LevelupPresentParentMap) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelupPresentParentMap.DiscardUnknown(m)
}

var xxx_messageInfo_LevelupPresentParentMap proto.InternalMessageInfo

func (m *LevelupPresentParentMap) GetMap() map[uint32]*LevelupParentPresentAllData {
	if m != nil {
		return m.Map
	}
	return nil
}

type LevelupParentPresentList struct {
	Total                uint32                      `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List                 []*LevelupParentPresentData `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *LevelupParentPresentList) Reset()         { *m = LevelupParentPresentList{} }
func (m *LevelupParentPresentList) String() string { return proto.CompactTextString(m) }
func (*LevelupParentPresentList) ProtoMessage()    {}
func (*LevelupParentPresentList) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{22}
}
func (m *LevelupParentPresentList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelupParentPresentList.Unmarshal(m, b)
}
func (m *LevelupParentPresentList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelupParentPresentList.Marshal(b, m, deterministic)
}
func (dst *LevelupParentPresentList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelupParentPresentList.Merge(dst, src)
}
func (m *LevelupParentPresentList) XXX_Size() int {
	return xxx_messageInfo_LevelupParentPresentList.Size(m)
}
func (m *LevelupParentPresentList) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelupParentPresentList.DiscardUnknown(m)
}

var xxx_messageInfo_LevelupParentPresentList proto.InternalMessageInfo

func (m *LevelupParentPresentList) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *LevelupParentPresentList) GetList() []*LevelupParentPresentData {
	if m != nil {
		return m.List
	}
	return nil
}

type LevelupChildPresentList struct {
	List                 []*LevelupChildPresentData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *LevelupChildPresentList) Reset()         { *m = LevelupChildPresentList{} }
func (m *LevelupChildPresentList) String() string { return proto.CompactTextString(m) }
func (*LevelupChildPresentList) ProtoMessage()    {}
func (*LevelupChildPresentList) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{23}
}
func (m *LevelupChildPresentList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelupChildPresentList.Unmarshal(m, b)
}
func (m *LevelupChildPresentList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelupChildPresentList.Marshal(b, m, deterministic)
}
func (dst *LevelupChildPresentList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelupChildPresentList.Merge(dst, src)
}
func (m *LevelupChildPresentList) XXX_Size() int {
	return xxx_messageInfo_LevelupChildPresentList.Size(m)
}
func (m *LevelupChildPresentList) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelupChildPresentList.DiscardUnknown(m)
}

var xxx_messageInfo_LevelupChildPresentList proto.InternalMessageInfo

func (m *LevelupChildPresentList) GetList() []*LevelupChildPresentData {
	if m != nil {
		return m.List
	}
	return nil
}

type LevelupUserRankReq struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	Version              uint32   `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`
	Offset               uint32   `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelupUserRankReq) Reset()         { *m = LevelupUserRankReq{} }
func (m *LevelupUserRankReq) String() string { return proto.CompactTextString(m) }
func (*LevelupUserRankReq) ProtoMessage()    {}
func (*LevelupUserRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{24}
}
func (m *LevelupUserRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelupUserRankReq.Unmarshal(m, b)
}
func (m *LevelupUserRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelupUserRankReq.Marshal(b, m, deterministic)
}
func (dst *LevelupUserRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelupUserRankReq.Merge(dst, src)
}
func (m *LevelupUserRankReq) XXX_Size() int {
	return xxx_messageInfo_LevelupUserRankReq.Size(m)
}
func (m *LevelupUserRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelupUserRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_LevelupUserRankReq proto.InternalMessageInfo

func (m *LevelupUserRankReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *LevelupUserRankReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *LevelupUserRankReq) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *LevelupUserRankReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *LevelupUserRankReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type UserRank struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Rank                 uint32   `protobuf:"varint,2,opt,name=rank,proto3" json:"rank,omitempty"`
	CreateTime           uint32   `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserRank) Reset()         { *m = UserRank{} }
func (m *UserRank) String() string { return proto.CompactTextString(m) }
func (*UserRank) ProtoMessage()    {}
func (*UserRank) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{25}
}
func (m *UserRank) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRank.Unmarshal(m, b)
}
func (m *UserRank) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRank.Marshal(b, m, deterministic)
}
func (dst *UserRank) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRank.Merge(dst, src)
}
func (m *UserRank) XXX_Size() int {
	return xxx_messageInfo_UserRank.Size(m)
}
func (m *UserRank) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRank.DiscardUnknown(m)
}

var xxx_messageInfo_UserRank proto.InternalMessageInfo

func (m *UserRank) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserRank) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *UserRank) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type LevelupUserRankRes struct {
	RankList             []*UserRank `protobuf:"bytes,1,rep,name=rank_list,json=rankList,proto3" json:"rank_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *LevelupUserRankRes) Reset()         { *m = LevelupUserRankRes{} }
func (m *LevelupUserRankRes) String() string { return proto.CompactTextString(m) }
func (*LevelupUserRankRes) ProtoMessage()    {}
func (*LevelupUserRankRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_4124a8719a2264b8, []int{26}
}
func (m *LevelupUserRankRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelupUserRankRes.Unmarshal(m, b)
}
func (m *LevelupUserRankRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelupUserRankRes.Marshal(b, m, deterministic)
}
func (dst *LevelupUserRankRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelupUserRankRes.Merge(dst, src)
}
func (m *LevelupUserRankRes) XXX_Size() int {
	return xxx_messageInfo_LevelupUserRankRes.Size(m)
}
func (m *LevelupUserRankRes) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelupUserRankRes.DiscardUnknown(m)
}

var xxx_messageInfo_LevelupUserRankRes proto.InternalMessageInfo

func (m *LevelupUserRankRes) GetRankList() []*UserRank {
	if m != nil {
		return m.RankList
	}
	return nil
}

func init() {
	proto.RegisterType((*EmptyMsg)(nil), "levelup_present.EmptyMsg")
	proto.RegisterType((*ItemReq)(nil), "levelup_present.ItemReq")
	proto.RegisterType((*ItemLevelReq)(nil), "levelup_present.ItemLevelReq")
	proto.RegisterType((*ItemRes)(nil), "levelup_present.ItemRes")
	proto.RegisterType((*PushRes)(nil), "levelup_present.PushRes")
	proto.RegisterType((*ItemVersionReq)(nil), "levelup_present.ItemVersionReq")
	proto.RegisterType((*OffsetTypeReq)(nil), "levelup_present.OffsetTypeReq")
	proto.RegisterType((*UidReq)(nil), "levelup_present.UidReq")
	proto.RegisterType((*UidItemReq)(nil), "levelup_present.UidItemReq")
	proto.RegisterType((*UidItemVersionReq)(nil), "levelup_present.UidItemVersionReq")
	proto.RegisterType((*UserLevelExp)(nil), "levelup_present.UserLevelExp")
	proto.RegisterType((*ItemBatchReq)(nil), "levelup_present.ItemBatchReq")
	proto.RegisterType((*LevelupParentPresentData)(nil), "levelup_present.LevelupParentPresentData")
	proto.RegisterType((*LevelupParentPresentAllData)(nil), "levelup_present.LevelupParentPresentAllData")
	proto.RegisterType((*LevelupChildPresentData)(nil), "levelup_present.LevelupChildPresentData")
	proto.RegisterType((*LevelupPresentBatchData)(nil), "levelup_present.LevelupPresentBatchData")
	proto.RegisterType((*LevelupPresentBatchList)(nil), "levelup_present.LevelupPresentBatchList")
	proto.RegisterType((*LevelupPresentBatchDataMap)(nil), "levelup_present.LevelupPresentBatchDataMap")
	proto.RegisterMapType((map[uint32]*LevelupPresentBatchList)(nil), "levelup_present.LevelupPresentBatchDataMap.BatchMapEntry")
	proto.RegisterType((*VersionData)(nil), "levelup_present.VersionData")
	proto.RegisterType((*VersionList)(nil), "levelup_present.VersionList")
	proto.RegisterType((*UserLevelupPresentStatusMap)(nil), "levelup_present.UserLevelupPresentStatusMap")
	proto.RegisterMapType((map[uint32]*UserLevelExp)(nil), "levelup_present.UserLevelupPresentStatusMap.MapEntry")
	proto.RegisterType((*LevelupPresentParentMap)(nil), "levelup_present.LevelupPresentParentMap")
	proto.RegisterMapType((map[uint32]*LevelupParentPresentAllData)(nil), "levelup_present.LevelupPresentParentMap.MapEntry")
	proto.RegisterType((*LevelupParentPresentList)(nil), "levelup_present.LevelupParentPresentList")
	proto.RegisterType((*LevelupChildPresentList)(nil), "levelup_present.LevelupChildPresentList")
	proto.RegisterType((*LevelupUserRankReq)(nil), "levelup_present.LevelupUserRankReq")
	proto.RegisterType((*UserRank)(nil), "levelup_present.UserRank")
	proto.RegisterType((*LevelupUserRankRes)(nil), "levelup_present.LevelupUserRankRes")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// LevelupPresentClient is the client API for LevelupPresent service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type LevelupPresentClient interface {
	// 新增父级升级礼物配置
	AddLevelupParentPresent(ctx context.Context, in *LevelupParentPresentData, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 修改父级升级礼物配置
	UpdateLevelupParentPresent(ctx context.Context, in *LevelupParentPresentData, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 删除父级升级礼物配置
	DeleteLevelupParentPresent(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 新增子级升级礼物配置
	AddLevelupChildPresent(ctx context.Context, in *LevelupChildPresentData, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 修改子级升级礼物配置
	UpdateLevelupChildPresent(ctx context.Context, in *LevelupChildPresentData, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 删除子级升级礼物配置
	DeleteLevelupChildPresent(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 获取全部的父级升级礼物全基础配置
	GetAllLevelupParentPresent(ctx context.Context, in *EmptyMsg, opts ...grpc.CallOption) (*LevelupPresentParentMap, error)
	// 获取礼物的父级礼物ID
	GetLevelupParentPresentById(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*ItemRes, error)
	// 新增升级礼物批量配置
	AddLevelupBatch(ctx context.Context, in *LevelupPresentBatchData, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 修改升级礼物批量配置
	UpdateLevelupBatch(ctx context.Context, in *LevelupPresentBatchData, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 删除升级礼物批量配置
	DeleteLevelupBatch(ctx context.Context, in *ItemBatchReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 获取某个升级礼物的批量配置
	GetLevelupBatchById(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*LevelupPresentBatchList, error)
	// 获取所有升级礼物的批量配置
	GetAllLevelupBatch(ctx context.Context, in *EmptyMsg, opts ...grpc.CallOption) (*LevelupPresentBatchDataMap, error)
	// 获取升级礼物的所有历史版本，后台管理使用
	GetLevelupPresentVersionList(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*VersionList, error)
	// 新增活动升级礼物的版本，后台管理使用
	AddLevelupPresentVersion(ctx context.Context, in *ItemVersionReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 获取用户全部升级礼物当前版本的状态，APP端礼物栏等级状态
	GetUserAllLevelupPresentStatus(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*UserLevelupPresentStatusMap, error)
	// 增加用户某个升级礼物的经验，version要上游传，防止请求瞬间，版本切换，导致用户看到的礼物是之前的等级，而写入的是新的等级
	AddUserLevelupPresentExp(ctx context.Context, in *UidItemVersionReq, opts ...grpc.CallOption) (*UserLevelExp, error)
	// 分页查询父级升级礼物
	GetLevelupParentPresent(ctx context.Context, in *OffsetTypeReq, opts ...grpc.CallOption) (*LevelupParentPresentList, error)
	// 查看父级下面的子升级礼物
	GetLevelupChildrenPresent(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*LevelupChildPresentList, error)
	// 获取父级礼物的全部配置信息
	GetLevelupParentPresentData(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*LevelupParentPresentAllData, error)
	// 根据ID获取子级礼物配置，无缓存，后台管理用
	GetLevelupChildPresentData(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*LevelupChildPresentData, error)
	// 增加礼物等级达到人数，并返回是否推送
	AddLevelupPerson(ctx context.Context, in *ItemLevelReq, opts ...grpc.CallOption) (*PushRes, error)
	// 获取升级rank
	GetLevelupUserRank(ctx context.Context, in *LevelupUserRankReq, opts ...grpc.CallOption) (*LevelupUserRankRes, error)
}

type levelupPresentClient struct {
	cc *grpc.ClientConn
}

func NewLevelupPresentClient(cc *grpc.ClientConn) LevelupPresentClient {
	return &levelupPresentClient{cc}
}

func (c *levelupPresentClient) AddLevelupParentPresent(ctx context.Context, in *LevelupParentPresentData, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/AddLevelupParentPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) UpdateLevelupParentPresent(ctx context.Context, in *LevelupParentPresentData, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/UpdateLevelupParentPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) DeleteLevelupParentPresent(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/DeleteLevelupParentPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) AddLevelupChildPresent(ctx context.Context, in *LevelupChildPresentData, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/AddLevelupChildPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) UpdateLevelupChildPresent(ctx context.Context, in *LevelupChildPresentData, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/UpdateLevelupChildPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) DeleteLevelupChildPresent(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/DeleteLevelupChildPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) GetAllLevelupParentPresent(ctx context.Context, in *EmptyMsg, opts ...grpc.CallOption) (*LevelupPresentParentMap, error) {
	out := new(LevelupPresentParentMap)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/GetAllLevelupParentPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) GetLevelupParentPresentById(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*ItemRes, error) {
	out := new(ItemRes)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/GetLevelupParentPresentById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) AddLevelupBatch(ctx context.Context, in *LevelupPresentBatchData, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/AddLevelupBatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) UpdateLevelupBatch(ctx context.Context, in *LevelupPresentBatchData, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/UpdateLevelupBatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) DeleteLevelupBatch(ctx context.Context, in *ItemBatchReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/DeleteLevelupBatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) GetLevelupBatchById(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*LevelupPresentBatchList, error) {
	out := new(LevelupPresentBatchList)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/GetLevelupBatchById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) GetAllLevelupBatch(ctx context.Context, in *EmptyMsg, opts ...grpc.CallOption) (*LevelupPresentBatchDataMap, error) {
	out := new(LevelupPresentBatchDataMap)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/GetAllLevelupBatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) GetLevelupPresentVersionList(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*VersionList, error) {
	out := new(VersionList)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/GetLevelupPresentVersionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) AddLevelupPresentVersion(ctx context.Context, in *ItemVersionReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/AddLevelupPresentVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) GetUserAllLevelupPresentStatus(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*UserLevelupPresentStatusMap, error) {
	out := new(UserLevelupPresentStatusMap)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/GetUserAllLevelupPresentStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) AddUserLevelupPresentExp(ctx context.Context, in *UidItemVersionReq, opts ...grpc.CallOption) (*UserLevelExp, error) {
	out := new(UserLevelExp)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/AddUserLevelupPresentExp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) GetLevelupParentPresent(ctx context.Context, in *OffsetTypeReq, opts ...grpc.CallOption) (*LevelupParentPresentList, error) {
	out := new(LevelupParentPresentList)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/GetLevelupParentPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) GetLevelupChildrenPresent(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*LevelupChildPresentList, error) {
	out := new(LevelupChildPresentList)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/GetLevelupChildrenPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) GetLevelupParentPresentData(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*LevelupParentPresentAllData, error) {
	out := new(LevelupParentPresentAllData)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/GetLevelupParentPresentData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) GetLevelupChildPresentData(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*LevelupChildPresentData, error) {
	out := new(LevelupChildPresentData)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/GetLevelupChildPresentData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) AddLevelupPerson(ctx context.Context, in *ItemLevelReq, opts ...grpc.CallOption) (*PushRes, error) {
	out := new(PushRes)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/AddLevelupPerson", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *levelupPresentClient) GetLevelupUserRank(ctx context.Context, in *LevelupUserRankReq, opts ...grpc.CallOption) (*LevelupUserRankRes, error) {
	out := new(LevelupUserRankRes)
	err := c.cc.Invoke(ctx, "/levelup_present.LevelupPresent/GetLevelupUserRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LevelupPresentServer is the server API for LevelupPresent service.
type LevelupPresentServer interface {
	// 新增父级升级礼物配置
	AddLevelupParentPresent(context.Context, *LevelupParentPresentData) (*EmptyMsg, error)
	// 修改父级升级礼物配置
	UpdateLevelupParentPresent(context.Context, *LevelupParentPresentData) (*EmptyMsg, error)
	// 删除父级升级礼物配置
	DeleteLevelupParentPresent(context.Context, *ItemReq) (*EmptyMsg, error)
	// 新增子级升级礼物配置
	AddLevelupChildPresent(context.Context, *LevelupChildPresentData) (*EmptyMsg, error)
	// 修改子级升级礼物配置
	UpdateLevelupChildPresent(context.Context, *LevelupChildPresentData) (*EmptyMsg, error)
	// 删除子级升级礼物配置
	DeleteLevelupChildPresent(context.Context, *ItemReq) (*EmptyMsg, error)
	// 获取全部的父级升级礼物全基础配置
	GetAllLevelupParentPresent(context.Context, *EmptyMsg) (*LevelupPresentParentMap, error)
	// 获取礼物的父级礼物ID
	GetLevelupParentPresentById(context.Context, *ItemReq) (*ItemRes, error)
	// 新增升级礼物批量配置
	AddLevelupBatch(context.Context, *LevelupPresentBatchData) (*EmptyMsg, error)
	// 修改升级礼物批量配置
	UpdateLevelupBatch(context.Context, *LevelupPresentBatchData) (*EmptyMsg, error)
	// 删除升级礼物批量配置
	DeleteLevelupBatch(context.Context, *ItemBatchReq) (*EmptyMsg, error)
	// 获取某个升级礼物的批量配置
	GetLevelupBatchById(context.Context, *ItemReq) (*LevelupPresentBatchList, error)
	// 获取所有升级礼物的批量配置
	GetAllLevelupBatch(context.Context, *EmptyMsg) (*LevelupPresentBatchDataMap, error)
	// 获取升级礼物的所有历史版本，后台管理使用
	GetLevelupPresentVersionList(context.Context, *ItemReq) (*VersionList, error)
	// 新增活动升级礼物的版本，后台管理使用
	AddLevelupPresentVersion(context.Context, *ItemVersionReq) (*EmptyMsg, error)
	// 获取用户全部升级礼物当前版本的状态，APP端礼物栏等级状态
	GetUserAllLevelupPresentStatus(context.Context, *UidReq) (*UserLevelupPresentStatusMap, error)
	// 增加用户某个升级礼物的经验，version要上游传，防止请求瞬间，版本切换，导致用户看到的礼物是之前的等级，而写入的是新的等级
	AddUserLevelupPresentExp(context.Context, *UidItemVersionReq) (*UserLevelExp, error)
	// 分页查询父级升级礼物
	GetLevelupParentPresent(context.Context, *OffsetTypeReq) (*LevelupParentPresentList, error)
	// 查看父级下面的子升级礼物
	GetLevelupChildrenPresent(context.Context, *ItemReq) (*LevelupChildPresentList, error)
	// 获取父级礼物的全部配置信息
	GetLevelupParentPresentData(context.Context, *ItemReq) (*LevelupParentPresentAllData, error)
	// 根据ID获取子级礼物配置，无缓存，后台管理用
	GetLevelupChildPresentData(context.Context, *ItemReq) (*LevelupChildPresentData, error)
	// 增加礼物等级达到人数，并返回是否推送
	AddLevelupPerson(context.Context, *ItemLevelReq) (*PushRes, error)
	// 获取升级rank
	GetLevelupUserRank(context.Context, *LevelupUserRankReq) (*LevelupUserRankRes, error)
}

func RegisterLevelupPresentServer(s *grpc.Server, srv LevelupPresentServer) {
	s.RegisterService(&_LevelupPresent_serviceDesc, srv)
}

func _LevelupPresent_AddLevelupParentPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LevelupParentPresentData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).AddLevelupParentPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/AddLevelupParentPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).AddLevelupParentPresent(ctx, req.(*LevelupParentPresentData))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_UpdateLevelupParentPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LevelupParentPresentData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).UpdateLevelupParentPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/UpdateLevelupParentPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).UpdateLevelupParentPresent(ctx, req.(*LevelupParentPresentData))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_DeleteLevelupParentPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).DeleteLevelupParentPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/DeleteLevelupParentPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).DeleteLevelupParentPresent(ctx, req.(*ItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_AddLevelupChildPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LevelupChildPresentData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).AddLevelupChildPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/AddLevelupChildPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).AddLevelupChildPresent(ctx, req.(*LevelupChildPresentData))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_UpdateLevelupChildPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LevelupChildPresentData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).UpdateLevelupChildPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/UpdateLevelupChildPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).UpdateLevelupChildPresent(ctx, req.(*LevelupChildPresentData))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_DeleteLevelupChildPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).DeleteLevelupChildPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/DeleteLevelupChildPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).DeleteLevelupChildPresent(ctx, req.(*ItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_GetAllLevelupParentPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyMsg)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).GetAllLevelupParentPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/GetAllLevelupParentPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).GetAllLevelupParentPresent(ctx, req.(*EmptyMsg))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_GetLevelupParentPresentById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).GetLevelupParentPresentById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/GetLevelupParentPresentById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).GetLevelupParentPresentById(ctx, req.(*ItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_AddLevelupBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LevelupPresentBatchData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).AddLevelupBatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/AddLevelupBatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).AddLevelupBatch(ctx, req.(*LevelupPresentBatchData))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_UpdateLevelupBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LevelupPresentBatchData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).UpdateLevelupBatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/UpdateLevelupBatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).UpdateLevelupBatch(ctx, req.(*LevelupPresentBatchData))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_DeleteLevelupBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ItemBatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).DeleteLevelupBatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/DeleteLevelupBatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).DeleteLevelupBatch(ctx, req.(*ItemBatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_GetLevelupBatchById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).GetLevelupBatchById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/GetLevelupBatchById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).GetLevelupBatchById(ctx, req.(*ItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_GetAllLevelupBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyMsg)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).GetAllLevelupBatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/GetAllLevelupBatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).GetAllLevelupBatch(ctx, req.(*EmptyMsg))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_GetLevelupPresentVersionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).GetLevelupPresentVersionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/GetLevelupPresentVersionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).GetLevelupPresentVersionList(ctx, req.(*ItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_AddLevelupPresentVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ItemVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).AddLevelupPresentVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/AddLevelupPresentVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).AddLevelupPresentVersion(ctx, req.(*ItemVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_GetUserAllLevelupPresentStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).GetUserAllLevelupPresentStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/GetUserAllLevelupPresentStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).GetUserAllLevelupPresentStatus(ctx, req.(*UidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_AddUserLevelupPresentExp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UidItemVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).AddUserLevelupPresentExp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/AddUserLevelupPresentExp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).AddUserLevelupPresentExp(ctx, req.(*UidItemVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_GetLevelupParentPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OffsetTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).GetLevelupParentPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/GetLevelupParentPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).GetLevelupParentPresent(ctx, req.(*OffsetTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_GetLevelupChildrenPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).GetLevelupChildrenPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/GetLevelupChildrenPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).GetLevelupChildrenPresent(ctx, req.(*ItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_GetLevelupParentPresentData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).GetLevelupParentPresentData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/GetLevelupParentPresentData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).GetLevelupParentPresentData(ctx, req.(*ItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_GetLevelupChildPresentData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).GetLevelupChildPresentData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/GetLevelupChildPresentData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).GetLevelupChildPresentData(ctx, req.(*ItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_AddLevelupPerson_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ItemLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).AddLevelupPerson(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/AddLevelupPerson",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).AddLevelupPerson(ctx, req.(*ItemLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LevelupPresent_GetLevelupUserRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LevelupUserRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LevelupPresentServer).GetLevelupUserRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/levelup_present.LevelupPresent/GetLevelupUserRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LevelupPresentServer).GetLevelupUserRank(ctx, req.(*LevelupUserRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _LevelupPresent_serviceDesc = grpc.ServiceDesc{
	ServiceName: "levelup_present.LevelupPresent",
	HandlerType: (*LevelupPresentServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddLevelupParentPresent",
			Handler:    _LevelupPresent_AddLevelupParentPresent_Handler,
		},
		{
			MethodName: "UpdateLevelupParentPresent",
			Handler:    _LevelupPresent_UpdateLevelupParentPresent_Handler,
		},
		{
			MethodName: "DeleteLevelupParentPresent",
			Handler:    _LevelupPresent_DeleteLevelupParentPresent_Handler,
		},
		{
			MethodName: "AddLevelupChildPresent",
			Handler:    _LevelupPresent_AddLevelupChildPresent_Handler,
		},
		{
			MethodName: "UpdateLevelupChildPresent",
			Handler:    _LevelupPresent_UpdateLevelupChildPresent_Handler,
		},
		{
			MethodName: "DeleteLevelupChildPresent",
			Handler:    _LevelupPresent_DeleteLevelupChildPresent_Handler,
		},
		{
			MethodName: "GetAllLevelupParentPresent",
			Handler:    _LevelupPresent_GetAllLevelupParentPresent_Handler,
		},
		{
			MethodName: "GetLevelupParentPresentById",
			Handler:    _LevelupPresent_GetLevelupParentPresentById_Handler,
		},
		{
			MethodName: "AddLevelupBatch",
			Handler:    _LevelupPresent_AddLevelupBatch_Handler,
		},
		{
			MethodName: "UpdateLevelupBatch",
			Handler:    _LevelupPresent_UpdateLevelupBatch_Handler,
		},
		{
			MethodName: "DeleteLevelupBatch",
			Handler:    _LevelupPresent_DeleteLevelupBatch_Handler,
		},
		{
			MethodName: "GetLevelupBatchById",
			Handler:    _LevelupPresent_GetLevelupBatchById_Handler,
		},
		{
			MethodName: "GetAllLevelupBatch",
			Handler:    _LevelupPresent_GetAllLevelupBatch_Handler,
		},
		{
			MethodName: "GetLevelupPresentVersionList",
			Handler:    _LevelupPresent_GetLevelupPresentVersionList_Handler,
		},
		{
			MethodName: "AddLevelupPresentVersion",
			Handler:    _LevelupPresent_AddLevelupPresentVersion_Handler,
		},
		{
			MethodName: "GetUserAllLevelupPresentStatus",
			Handler:    _LevelupPresent_GetUserAllLevelupPresentStatus_Handler,
		},
		{
			MethodName: "AddUserLevelupPresentExp",
			Handler:    _LevelupPresent_AddUserLevelupPresentExp_Handler,
		},
		{
			MethodName: "GetLevelupParentPresent",
			Handler:    _LevelupPresent_GetLevelupParentPresent_Handler,
		},
		{
			MethodName: "GetLevelupChildrenPresent",
			Handler:    _LevelupPresent_GetLevelupChildrenPresent_Handler,
		},
		{
			MethodName: "GetLevelupParentPresentData",
			Handler:    _LevelupPresent_GetLevelupParentPresentData_Handler,
		},
		{
			MethodName: "GetLevelupChildPresentData",
			Handler:    _LevelupPresent_GetLevelupChildPresentData_Handler,
		},
		{
			MethodName: "AddLevelupPerson",
			Handler:    _LevelupPresent_AddLevelupPerson_Handler,
		},
		{
			MethodName: "GetLevelupUserRank",
			Handler:    _LevelupPresent_GetLevelupUserRank_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "levelup-present/levelup-present.proto",
}

func init() {
	proto.RegisterFile("levelup-present/levelup-present.proto", fileDescriptor_levelup_present_4124a8719a2264b8)
}

var fileDescriptor_levelup_present_4124a8719a2264b8 = []byte{
	// 1484 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x58, 0xcb, 0x6e, 0xdb, 0x46,
	0x17, 0x16, 0x2d, 0x5b, 0x97, 0x63, 0xd9, 0x49, 0xe6, 0x0f, 0x62, 0x9a, 0x89, 0xe3, 0x84, 0xf9,
	0x83, 0xba, 0x68, 0x2a, 0xd7, 0x0e, 0x1c, 0xb4, 0x45, 0x2f, 0x88, 0x2f, 0x70, 0x0c, 0xd8, 0xad,
	0xab, 0xc4, 0x49, 0xd0, 0x02, 0x55, 0x68, 0x72, 0x64, 0x13, 0x96, 0x44, 0x96, 0x1c, 0x19, 0x56,
	0x5e, 0xa1, 0xbb, 0xbe, 0x42, 0x1f, 0xa1, 0xeb, 0x6e, 0x8a, 0x3e, 0x41, 0xb7, 0x7d, 0x88, 0x6e,
	0xba, 0x6f, 0x31, 0x67, 0x46, 0xe2, 0x9d, 0xa2, 0xdb, 0x64, 0xd3, 0x95, 0x34, 0x67, 0xce, 0x9c,
	0xcb, 0xf7, 0x9d, 0x39, 0x3c, 0x24, 0xdc, 0xef, 0xd2, 0x73, 0xda, 0x1d, 0xb8, 0xef, 0xbb, 0x1e,
	0xf5, 0x69, 0x9f, 0xad, 0xc6, 0xd6, 0x4d, 0xd7, 0x73, 0x98, 0x43, 0xae, 0x48, 0x71, 0x5b, 0x8a,
	0xb5, 0x65, 0x7a, 0xc1, 0x68, 0xdf, 0xb7, 0x9d, 0xfe, 0xaa, 0xe3, 0x32, 0xdb, 0xe9, 0xfb, 0xa3,
	0x5f, 0x71, 0x42, 0x07, 0xa8, 0xed, 0xf4, 0x5c, 0x36, 0x3c, 0xf0, 0x4f, 0x74, 0x1d, 0xaa, 0x7b,
	0x8c, 0xf6, 0x5a, 0xf4, 0x3b, 0xb2, 0x00, 0x55, 0x9b, 0xd1, 0x5e, 0xdb, 0xb6, 0x54, 0xe5, 0x8e,
	0xb2, 0x32, 0xd7, 0xaa, 0xf0, 0xe5, 0x9e, 0xa5, 0xdb, 0xd0, 0xe0, 0x3a, 0xfb, 0xdc, 0x4f, 0x9e,
	0x22, 0xb9, 0x0e, 0x33, 0x18, 0x8c, 0x3a, 0x85, 0x62, 0xb1, 0x20, 0x2a, 0x54, 0xcf, 0xa9, 0xc7,
	0xe3, 0x51, 0xcb, 0x28, 0x1f, 0x2d, 0xc9, 0x55, 0x28, 0x0f, 0x6c, 0x4b, 0x9d, 0x46, 0x29, 0xff,
	0x1b, 0x84, 0xe3, 0x67, 0x87, 0xb3, 0x06, 0xd5, 0xc3, 0x81, 0x7f, 0xca, 0x75, 0x08, 0x4c, 0xbb,
	0x03, 0xff, 0x14, 0x15, 0x6a, 0x2d, 0xfc, 0xcf, 0x65, 0x9e, 0xd1, 0x3f, 0x93, 0x31, 0xe0, 0x7f,
	0x7d, 0x0b, 0xe6, 0xb9, 0xd9, 0xe7, 0xc2, 0x6f, 0x6e, 0x0e, 0xa1, 0x68, 0xa7, 0x22, 0xd1, 0xea,
	0xaf, 0x60, 0xee, 0xcb, 0x4e, 0xc7, 0xa7, 0xec, 0xd9, 0xd0, 0xa5, 0xdc, 0xc6, 0x0d, 0xa8, 0x38,
	0x28, 0x18, 0x99, 0x10, 0x2b, 0x84, 0xc1, 0xee, 0xd9, 0x6c, 0x0c, 0x03, 0x5f, 0x90, 0xbb, 0xd0,
	0x90, 0x0c, 0xb5, 0xd9, 0xd0, 0xa5, 0x88, 0xc5, 0x4c, 0x6b, 0x56, 0xca, 0xb8, 0x4d, 0x5d, 0x83,
	0xca, 0x91, 0x6d, 0x71, 0xd3, 0x12, 0x19, 0x25, 0x40, 0x66, 0x1b, 0xe0, 0xc8, 0xb6, 0x46, 0x5c,
	0x25, 0xf6, 0xc9, 0xff, 0x61, 0xde, 0x35, 0x3c, 0x6e, 0x7d, 0x94, 0x97, 0xf0, 0xde, 0x10, 0xd2,
	0x3d, 0x81, 0xdd, 0x0f, 0x0a, 0x5c, 0x93, 0x66, 0x42, 0x60, 0x24, 0xad, 0x85, 0xe0, 0x99, 0xca,
	0x82, 0x27, 0x46, 0xe6, 0x12, 0x00, 0x1e, 0x31, 0x9d, 0x41, 0x9f, 0x49, 0x4e, 0xeb, 0x5c, 0xb2,
	0xc5, 0x05, 0x64, 0x11, 0x6a, 0x8e, 0x67, 0x51, 0x8f, 0x9b, 0x9c, 0xb9, 0xa3, 0xac, 0xd4, 0x5b,
	0x55, 0x5c, 0xef, 0x59, 0xfa, 0x23, 0x68, 0x1c, 0xf9, 0xd4, 0xc3, 0xfa, 0xda, 0xb9, 0x70, 0x83,
	0x32, 0x52, 0xc2, 0x65, 0x74, 0x15, 0xca, 0xf4, 0xc2, 0x95, 0xe1, 0xf0, 0xbf, 0xfa, 0x13, 0x51,
	0x97, 0x9b, 0x06, 0x33, 0x4f, 0x73, 0x39, 0x5d, 0x86, 0xd9, 0x63, 0xae, 0x24, 0x63, 0x13, 0x26,
	0x00, 0x45, 0x18, 0x9c, 0xfe, 0x63, 0x19, 0xd4, 0x7d, 0x71, 0x8d, 0x0e, 0x11, 0xae, 0x43, 0xc1,
	0xca, 0xb6, 0xc1, 0x8c, 0x14, 0x64, 0x95, 0x24, 0xb2, 0x09, 0x7a, 0x85, 0x93, 0x30, 0xbd, 0xe4,
	0x1d, 0xb8, 0x62, 0x0e, 0x3c, 0xb4, 0x14, 0xc5, 0x70, 0x5e, 0x8a, 0x25, 0x25, 0x3c, 0x91, 0xd7,
	0xb6, 0xdb, 0x1e, 0x78, 0x5d, 0xc4, 0xb1, 0xde, 0xaa, 0xbc, 0xb6, 0xdd, 0x23, 0xaf, 0xcb, 0x13,
	0x31, 0x3d, 0x6a, 0x30, 0xda, 0x66, 0x76, 0x8f, 0x22, 0x8e, 0x73, 0x2d, 0x10, 0xa2, 0x67, 0x76,
	0x8f, 0x72, 0x85, 0x81, 0x6b, 0x8d, 0x15, 0x2a, 0x42, 0x41, 0x88, 0x50, 0x61, 0x01, 0xaa, 0xa6,
	0xd3, 0x75, 0xbc, 0xf6, 0x9a, 0x5a, 0x15, 0xa6, 0x71, 0xb9, 0x16, 0x6c, 0xac, 0xab, 0xb5, 0xd0,
	0xc6, 0x3a, 0xe7, 0x75, 0xd4, 0x61, 0x8e, 0x4f, 0xd4, 0x3a, 0xee, 0xd5, 0xa5, 0x64, 0xf3, 0x84,
	0xdc, 0x87, 0xf9, 0xd1, 0x76, 0x7f, 0xd0, 0x3b, 0xa6, 0x9e, 0x0a, 0xa8, 0x32, 0x27, 0xa5, 0x5f,
	0xa0, 0x90, 0xdc, 0x83, 0x91, 0xa0, 0x2d, 0xb8, 0x9d, 0x45, 0xad, 0x86, 0x14, 0x22, 0xf8, 0xdc,
	0x95, 0xd9, 0xf3, 0xdb, 0xfe, 0xa0, 0xd3, 0xb1, 0x2f, 0xd4, 0x86, 0x70, 0x65, 0xf6, 0xfc, 0xa7,
	0x28, 0xd0, 0xff, 0x28, 0xc3, 0xcd, 0x34, 0x96, 0x1e, 0x77, 0xbb, 0xff, 0x3d, 0xa2, 0x76, 0x25,
	0xec, 0xed, 0xae, 0xed, 0x33, 0xb5, 0x7a, 0xa7, 0xbc, 0x32, 0xbb, 0xbe, 0xd2, 0x8c, 0xf5, 0xfa,
	0xa6, 0x84, 0x63, 0xeb, 0xd4, 0xee, 0x5a, 0xa1, 0x9a, 0x95, 0x04, 0xed, 0xdb, 0x3e, 0x0b, 0x33,
	0x5e, 0xcb, 0x62, 0xbc, 0x9e, 0xc3, 0x38, 0x4c, 0x66, 0x7c, 0xb6, 0x10, 0xe3, 0x8d, 0x89, 0x8c,
	0xcf, 0xc5, 0x19, 0xff, 0x45, 0x81, 0x85, 0x8c, 0x14, 0xb3, 0x6f, 0x7b, 0xa1, 0x4e, 0x18, 0x34,
	0x99, 0x72, 0x4a, 0x93, 0x99, 0x1e, 0x37, 0x99, 0x7f, 0xcf, 0xa4, 0xfe, 0x67, 0x90, 0x84, 0x8c,
	0x1f, 0x3b, 0x56, 0x7e, 0x12, 0x93, 0x5a, 0x16, 0x47, 0x8e, 0x76, 0x3a, 0xd4, 0x64, 0x58, 0x7d,
	0x65, 0x81, 0x9c, 0x90, 0xf0, 0x02, 0x0c, 0xb6, 0x7b, 0xd6, 0x86, 0x2c, 0x4e, 0xb9, 0x7d, 0x60,
	0x6d, 0x70, 0xf3, 0x72, 0xdb, 0xa2, 0xbe, 0x29, 0x1b, 0xb2, 0x3c, 0xb1, 0x4d, 0x7d, 0x33, 0x9e,
	0x76, 0x65, 0x52, 0xda, 0xd5, 0x44, 0xda, 0x2f, 0x52, 0xb3, 0xc6, 0x92, 0xfc, 0x04, 0xa6, 0xb1,
	0xaa, 0x95, 0xfc, 0xaa, 0x8e, 0xa3, 0xd5, 0xc2, 0x53, 0xfa, 0xef, 0x0a, 0x68, 0x19, 0x1a, 0x07,
	0x86, 0x4b, 0x9e, 0x43, 0x5d, 0x20, 0xd7, 0x33, 0x5c, 0xe9, 0xe1, 0xa3, 0xa2, 0x1e, 0x0e, 0x0c,
	0xb7, 0x89, 0x8b, 0x03, 0xc3, 0xdd, 0xe9, 0x33, 0x6f, 0xd8, 0xaa, 0x1d, 0xcb, 0xa5, 0x46, 0x61,
	0x2e, 0xb2, 0xc5, 0x6b, 0xe5, 0x8c, 0x0e, 0x47, 0x4f, 0xcd, 0x33, 0x3a, 0x24, 0x9f, 0xc1, 0xcc,
	0xb9, 0xd1, 0x1d, 0x88, 0x9e, 0x52, 0x30, 0x31, 0x0e, 0x48, 0x4b, 0x1c, 0xfb, 0x78, 0xea, 0x43,
	0x45, 0x7f, 0x02, 0xb3, 0xb2, 0xbb, 0x60, 0x81, 0x84, 0x9e, 0xb7, 0x4a, 0xf4, 0x79, 0x1b, 0x63,
	0x68, 0x2a, 0xce, 0x90, 0xfe, 0xf9, 0xd8, 0x12, 0x82, 0xfe, 0x41, 0x04, 0xf4, 0x5b, 0x89, 0xd8,
	0x42, 0x5e, 0x25, 0xd0, 0x3f, 0x2b, 0x70, 0x73, 0xfc, 0x60, 0x1e, 0x47, 0xfd, 0x94, 0x19, 0x6c,
	0xe0, 0x73, 0xa4, 0x77, 0xa1, 0x1c, 0x60, 0xbc, 0x91, 0x30, 0x98, 0x73, 0xb4, 0x39, 0xc6, 0x97,
	0x5b, 0xd0, 0x8e, 0xa0, 0x96, 0x83, 0xea, 0xc3, 0x28, 0xaa, 0x4b, 0xd9, 0x8e, 0x76, 0x2e, 0xdc,
	0x30, 0x94, 0xbf, 0x26, 0x2e, 0x9e, 0x78, 0x6c, 0xf0, 0xd8, 0xb7, 0xc2, 0xb1, 0xaf, 0x4d, 0x20,
	0x6a, 0x7c, 0x2c, 0x16, 0xb7, 0x95, 0x1b, 0xf7, 0x66, 0x34, 0xee, 0x07, 0x99, 0x4e, 0x52, 0x9e,
	0x65, 0xe1, 0x34, 0x9c, 0xf4, 0xd9, 0x04, 0x49, 0xbd, 0x0e, 0x33, 0xcc, 0x61, 0xc6, 0x78, 0x54,
	0xc2, 0x05, 0xf9, 0x54, 0x52, 0x3d, 0x85, 0xd9, 0xbd, 0x5b, 0xc8, 0x71, 0x88, 0xf7, 0x17, 0xa9,
	0x4d, 0xf7, 0x32, 0x37, 0x37, 0xf1, 0x3c, 0x12, 0x86, 0xbf, 0x57, 0x80, 0x48, 0x0d, 0xce, 0x59,
	0xcb, 0xe8, 0x9f, 0xbd, 0xd1, 0xf7, 0x89, 0x60, 0x20, 0x9f, 0x4e, 0x1f, 0xc8, 0x67, 0x42, 0x03,
	0xb9, 0xfe, 0x15, 0xd4, 0x46, 0x51, 0xa4, 0x4c, 0xc0, 0x29, 0xaf, 0x11, 0xf1, 0x2b, 0x57, 0x4e,
	0x5c, 0xb9, 0xfd, 0x94, 0xfc, 0x7c, 0xf2, 0x08, 0xea, 0xfc, 0x78, 0x3b, 0x84, 0xdc, 0x62, 0x6a,
	0x11, 0xe3, 0x81, 0x1a, 0xd7, 0xe5, 0x60, 0xaf, 0xff, 0x74, 0x0d, 0xe6, 0xa3, 0x85, 0x48, 0x0c,
	0x58, 0x78, 0x6c, 0x59, 0x69, 0xfc, 0x91, 0xe2, 0x34, 0x6b, 0x49, 0xef, 0xe3, 0xf7, 0xc1, 0x12,
	0xb1, 0x40, 0x3b, 0xc2, 0x2e, 0xfe, 0x56, 0xbd, 0x3c, 0x05, 0x6d, 0x9b, 0x76, 0x69, 0x86, 0x17,
	0x35, 0x71, 0x54, 0xbe, 0xf8, 0xe4, 0x1b, 0x6d, 0xc3, 0x8d, 0x00, 0x9d, 0x70, 0x0d, 0x92, 0xc2,
	0x95, 0x9a, 0xef, 0xe0, 0x18, 0x16, 0x23, 0xd8, 0xbc, 0x0d, 0x1f, 0x2d, 0x58, 0x8c, 0x20, 0x13,
	0xf1, 0xf1, 0x0f, 0x81, 0x31, 0x41, 0xdb, 0xa5, 0xbc, 0xb7, 0xa4, 0xa2, 0x9d, 0x7d, 0x54, 0x5b,
	0x29, 0xda, 0x19, 0x91, 0xd2, 0x9b, 0xbb, 0x94, 0xa5, 0x79, 0xd8, 0x1c, 0xf2, 0x37, 0xc7, 0xcc,
	0xd0, 0xb3, 0x76, 0x7c, 0xbd, 0x44, 0x5e, 0xc2, 0x95, 0x80, 0x52, 0x7c, 0x60, 0x92, 0xc2, 0xf3,
	0x42, 0x3e, 0x26, 0xdf, 0x00, 0x89, 0x70, 0xf9, 0x46, 0x8d, 0x1f, 0x02, 0x89, 0x90, 0x28, 0x8c,
	0x2f, 0xa5, 0x26, 0x3a, 0x7a, 0x7f, 0x9d, 0x14, 0xee, 0xff, 0x02, 0x74, 0xf1, 0xc8, 0x04, 0x54,
	0x0b, 0x4f, 0x1f, 0x7a, 0x89, 0x7c, 0x0b, 0x24, 0x52, 0x1f, 0x22, 0xdc, 0x9c, 0xba, 0x78, 0xef,
	0x12, 0x13, 0x15, 0xb2, 0x78, 0x2b, 0x54, 0x1a, 0x42, 0x25, 0x3c, 0x9b, 0x64, 0x67, 0x91, 0x39,
	0xa7, 0xc8, 0xc8, 0x5f, 0x82, 0x1a, 0x6a, 0x88, 0x11, 0xcb, 0x64, 0x39, 0xd5, 0x6a, 0xf0, 0xdd,
	0x23, 0x1f, 0x70, 0x1b, 0x6e, 0xef, 0x52, 0xc6, 0xdb, 0x72, 0xe8, 0xde, 0x84, 0xe7, 0x18, 0xb2,
	0x90, 0x6c, 0xe2, 0xf8, 0xf5, 0x46, 0x7b, 0x70, 0x99, 0x59, 0x08, 0xfb, 0x16, 0x4f, 0x22, 0xa9,
	0xb3, 0x73, 0xe1, 0x12, 0x3d, 0xcd, 0x49, 0x2c, 0x8f, 0xfc, 0x91, 0x48, 0x2f, 0x91, 0x0e, 0x2c,
	0x64, 0x5c, 0x4d, 0x72, 0x3b, 0x71, 0x36, 0xf2, 0x91, 0x4b, 0x2b, 0xd6, 0xf0, 0x25, 0x1b, 0x06,
	0x2c, 0x06, 0x7e, 0xb0, 0x71, 0x79, 0xb4, 0x3f, 0xb9, 0x77, 0x15, 0xea, 0x9c, 0xd2, 0xc5, 0x49,
	0x66, 0x97, 0x11, 0xf3, 0x72, 0xa6, 0x93, 0x4b, 0xcd, 0x5f, 0xd8, 0xeb, 0xb5, 0x58, 0x2e, 0xc5,
	0xfc, 0x14, 0x7e, 0x0c, 0xe8, 0x25, 0x72, 0x00, 0x57, 0x43, 0xd5, 0x4b, 0x3d, 0xdf, 0xe9, 0x67,
	0x34, 0x89, 0xd1, 0xc7, 0xd7, 0x94, 0x66, 0x29, 0x3f, 0x86, 0xea, 0x25, 0xf2, 0x0a, 0xaf, 0x71,
	0x6c, 0x02, 0x21, 0xf7, 0xb2, 0x02, 0x0a, 0xcd, 0x60, 0x5a, 0x01, 0x25, 0x5f, 0x2f, 0x69, 0x77,
	0x7f, 0xfb, 0x6b, 0x79, 0x29, 0x5a, 0xa4, 0xf1, 0x9b, 0xff, 0xf0, 0xeb, 0xb5, 0x13, 0xa7, 0x6b,
	0xf4, 0x4f, 0x9a, 0x1b, 0xeb, 0x8c, 0x35, 0x4d, 0xa7, 0xb7, 0x8a, 0x9f, 0x9d, 0x4d, 0xa7, 0xbb,
	0xea, 0x53, 0xef, 0xdc, 0x36, 0xa9, 0x1f, 0xff, 0x94, 0x7d, 0x5c, 0x41, 0x95, 0x87, 0x7f, 0x07,
	0x00, 0x00, 0xff, 0xff, 0x31, 0x94, 0x51, 0x1d, 0xf4, 0x16, 0x00, 0x00,
}
