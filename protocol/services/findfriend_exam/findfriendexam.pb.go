// Code generated by protoc-gen-gogo.
// source: src/findfriend_exam/findfriendexam.proto
// DO NOT EDIT!

/*
	Package findfriendexam is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/findfriend_exam/findfriendexam.proto

	It has these top-level messages:
		UpdateUserFindFriendExamReq
		UpdateUserFindFriendExamResp
		StUserExamResult
		GetUserFindFriendExamReq
		GetUserFindFriendExamResp
		CheckUserFindFriendExamReq
		CheckUserFindFriendExamResp
		FindfriendExamSummary
		FindfriendExamIdReq
		GetFindfriendExamSummaryListReq
		GetFindfriendExamSummaryListResp
		FindfriendExamQuestion
		GetFindfriendExamQuestionListResp
		FindfriendExamResult
		GetFindfriendExamResultReq
		GetFindfriendExamResultListResp
*/
package findfriendexam

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 添加用户测试
type UpdateUserFindFriendExamReq struct {
	Uid    uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ExamId uint32 `protobuf:"varint,2,opt,name=exam_id,json=examId,proto3" json:"exam_id,omitempty"`
	Scores uint64 `protobuf:"varint,3,opt,name=scores,proto3" json:"scores,omitempty"`
}

func (m *UpdateUserFindFriendExamReq) Reset()         { *m = UpdateUserFindFriendExamReq{} }
func (m *UpdateUserFindFriendExamReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUserFindFriendExamReq) ProtoMessage()    {}
func (*UpdateUserFindFriendExamReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendexam, []int{0}
}

func (m *UpdateUserFindFriendExamReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateUserFindFriendExamReq) GetExamId() uint32 {
	if m != nil {
		return m.ExamId
	}
	return 0
}

func (m *UpdateUserFindFriendExamReq) GetScores() uint64 {
	if m != nil {
		return m.Scores
	}
	return 0
}

type UpdateUserFindFriendExamResp struct {
}

func (m *UpdateUserFindFriendExamResp) Reset()         { *m = UpdateUserFindFriendExamResp{} }
func (m *UpdateUserFindFriendExamResp) String() string { return proto.CompactTextString(m) }
func (*UpdateUserFindFriendExamResp) ProtoMessage()    {}
func (*UpdateUserFindFriendExamResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendexam, []int{1}
}

type StUserExamResult struct {
	ExamId    uint32   `protobuf:"varint,1,opt,name=exam_id,json=examId,proto3" json:"exam_id,omitempty"`
	GameId    uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	ExamName  string   `protobuf:"bytes,3,opt,name=exam_name,json=examName,proto3" json:"exam_name,omitempty"`
	Scores    uint64   `protobuf:"varint,4,opt,name=scores,proto3" json:"scores,omitempty"`
	TagName   string   `protobuf:"bytes,5,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	Img       string   `protobuf:"bytes,6,opt,name=img,proto3" json:"img,omitempty"`
	Desc      string   `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc,omitempty"`
	ScoreList []uint32 `protobuf:"varint,8,rep,packed,name=score_list,json=scoreList" json:"score_list,omitempty"`
}

func (m *StUserExamResult) Reset()                    { *m = StUserExamResult{} }
func (m *StUserExamResult) String() string            { return proto.CompactTextString(m) }
func (*StUserExamResult) ProtoMessage()               {}
func (*StUserExamResult) Descriptor() ([]byte, []int) { return fileDescriptorFindfriendexam, []int{2} }

func (m *StUserExamResult) GetExamId() uint32 {
	if m != nil {
		return m.ExamId
	}
	return 0
}

func (m *StUserExamResult) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *StUserExamResult) GetExamName() string {
	if m != nil {
		return m.ExamName
	}
	return ""
}

func (m *StUserExamResult) GetScores() uint64 {
	if m != nil {
		return m.Scores
	}
	return 0
}

func (m *StUserExamResult) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *StUserExamResult) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *StUserExamResult) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *StUserExamResult) GetScoreList() []uint32 {
	if m != nil {
		return m.ScoreList
	}
	return nil
}

// 获取用户测试结果
type GetUserFindFriendExamReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (m *GetUserFindFriendExamReq) Reset()         { *m = GetUserFindFriendExamReq{} }
func (m *GetUserFindFriendExamReq) String() string { return proto.CompactTextString(m) }
func (*GetUserFindFriendExamReq) ProtoMessage()    {}
func (*GetUserFindFriendExamReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendexam, []int{3}
}

func (m *GetUserFindFriendExamReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserFindFriendExamResp struct {
	ExamResult *StUserExamResult `protobuf:"bytes,1,opt,name=exam_result,json=examResult" json:"exam_result,omitempty"`
}

func (m *GetUserFindFriendExamResp) Reset()         { *m = GetUserFindFriendExamResp{} }
func (m *GetUserFindFriendExamResp) String() string { return proto.CompactTextString(m) }
func (*GetUserFindFriendExamResp) ProtoMessage()    {}
func (*GetUserFindFriendExamResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendexam, []int{4}
}

func (m *GetUserFindFriendExamResp) GetExamResult() *StUserExamResult {
	if m != nil {
		return m.ExamResult
	}
	return nil
}

// 检查用户是否测试
type CheckUserFindFriendExamReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (m *CheckUserFindFriendExamReq) Reset()         { *m = CheckUserFindFriendExamReq{} }
func (m *CheckUserFindFriendExamReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserFindFriendExamReq) ProtoMessage()    {}
func (*CheckUserFindFriendExamReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendexam, []int{5}
}

func (m *CheckUserFindFriendExamReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckUserFindFriendExamResp struct {
	ExamId uint32 `protobuf:"varint,1,opt,name=exam_id,json=examId,proto3" json:"exam_id,omitempty"`
	Scores uint64 `protobuf:"varint,2,opt,name=scores,proto3" json:"scores,omitempty"`
}

func (m *CheckUserFindFriendExamResp) Reset()         { *m = CheckUserFindFriendExamResp{} }
func (m *CheckUserFindFriendExamResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserFindFriendExamResp) ProtoMessage()    {}
func (*CheckUserFindFriendExamResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendexam, []int{6}
}

func (m *CheckUserFindFriendExamResp) GetExamId() uint32 {
	if m != nil {
		return m.ExamId
	}
	return 0
}

func (m *CheckUserFindFriendExamResp) GetScores() uint64 {
	if m != nil {
		return m.Scores
	}
	return 0
}

// ***************   题库相关api *****************
type FindfriendExamSummary struct {
	ExamId            uint32   `protobuf:"varint,1,opt,name=exam_id,json=examId,proto3" json:"exam_id,omitempty"`
	GameId            uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Name              string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Icon              string   `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	Status            uint32   `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	Rank              uint32   `protobuf:"varint,6,opt,name=rank,proto3" json:"rank,omitempty"`
	QuestionTypeNames []string `protobuf:"bytes,7,rep,name=question_type_names,json=questionTypeNames" json:"question_type_names,omitempty"`
}

func (m *FindfriendExamSummary) Reset()         { *m = FindfriendExamSummary{} }
func (m *FindfriendExamSummary) String() string { return proto.CompactTextString(m) }
func (*FindfriendExamSummary) ProtoMessage()    {}
func (*FindfriendExamSummary) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendexam, []int{7}
}

func (m *FindfriendExamSummary) GetExamId() uint32 {
	if m != nil {
		return m.ExamId
	}
	return 0
}

func (m *FindfriendExamSummary) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *FindfriendExamSummary) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *FindfriendExamSummary) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *FindfriendExamSummary) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *FindfriendExamSummary) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *FindfriendExamSummary) GetQuestionTypeNames() []string {
	if m != nil {
		return m.QuestionTypeNames
	}
	return nil
}

type FindfriendExamIdReq struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (m *FindfriendExamIdReq) Reset()         { *m = FindfriendExamIdReq{} }
func (m *FindfriendExamIdReq) String() string { return proto.CompactTextString(m) }
func (*FindfriendExamIdReq) ProtoMessage()    {}
func (*FindfriendExamIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendexam, []int{8}
}

func (m *FindfriendExamIdReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetFindfriendExamSummaryListReq struct {
	IncludeInvalid bool `protobuf:"varint,1,opt,name=include_invalid,json=includeInvalid,proto3" json:"include_invalid,omitempty"`
}

func (m *GetFindfriendExamSummaryListReq) Reset()         { *m = GetFindfriendExamSummaryListReq{} }
func (m *GetFindfriendExamSummaryListReq) String() string { return proto.CompactTextString(m) }
func (*GetFindfriendExamSummaryListReq) ProtoMessage()    {}
func (*GetFindfriendExamSummaryListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendexam, []int{9}
}

func (m *GetFindfriendExamSummaryListReq) GetIncludeInvalid() bool {
	if m != nil {
		return m.IncludeInvalid
	}
	return false
}

type GetFindfriendExamSummaryListResp struct {
	ExamList []*FindfriendExamSummary `protobuf:"bytes,1,rep,name=exam_list,json=examList" json:"exam_list,omitempty"`
}

func (m *GetFindfriendExamSummaryListResp) Reset()         { *m = GetFindfriendExamSummaryListResp{} }
func (m *GetFindfriendExamSummaryListResp) String() string { return proto.CompactTextString(m) }
func (*GetFindfriendExamSummaryListResp) ProtoMessage()    {}
func (*GetFindfriendExamSummaryListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendexam, []int{10}
}

func (m *GetFindfriendExamSummaryListResp) GetExamList() []*FindfriendExamSummary {
	if m != nil {
		return m.ExamList
	}
	return nil
}

type FindfriendExamQuestion struct {
	Id           uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ExamId       uint32   `protobuf:"varint,2,opt,name=exam_id,json=examId,proto3" json:"exam_id,omitempty"`
	QuestionType uint32   `protobuf:"varint,3,opt,name=question_type,json=questionType,proto3" json:"question_type,omitempty"`
	Question     string   `protobuf:"bytes,4,opt,name=question,proto3" json:"question,omitempty"`
	Options      []string `protobuf:"bytes,5,rep,name=options" json:"options,omitempty"`
}

func (m *FindfriendExamQuestion) Reset()         { *m = FindfriendExamQuestion{} }
func (m *FindfriendExamQuestion) String() string { return proto.CompactTextString(m) }
func (*FindfriendExamQuestion) ProtoMessage()    {}
func (*FindfriendExamQuestion) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendexam, []int{11}
}

func (m *FindfriendExamQuestion) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *FindfriendExamQuestion) GetExamId() uint32 {
	if m != nil {
		return m.ExamId
	}
	return 0
}

func (m *FindfriendExamQuestion) GetQuestionType() uint32 {
	if m != nil {
		return m.QuestionType
	}
	return 0
}

func (m *FindfriendExamQuestion) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

func (m *FindfriendExamQuestion) GetOptions() []string {
	if m != nil {
		return m.Options
	}
	return nil
}

type GetFindfriendExamQuestionListResp struct {
	QuestionList []*FindfriendExamQuestion `protobuf:"bytes,1,rep,name=question_list,json=questionList" json:"question_list,omitempty"`
}

func (m *GetFindfriendExamQuestionListResp) Reset()         { *m = GetFindfriendExamQuestionListResp{} }
func (m *GetFindfriendExamQuestionListResp) String() string { return proto.CompactTextString(m) }
func (*GetFindfriendExamQuestionListResp) ProtoMessage()    {}
func (*GetFindfriendExamQuestionListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendexam, []int{12}
}

func (m *GetFindfriendExamQuestionListResp) GetQuestionList() []*FindfriendExamQuestion {
	if m != nil {
		return m.QuestionList
	}
	return nil
}

type FindfriendExamResult struct {
	Id     uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ExamId uint32 `protobuf:"varint,2,opt,name=exam_id,json=examId,proto3" json:"exam_id,omitempty"`
	Result uint64 `protobuf:"varint,3,opt,name=result,proto3" json:"result,omitempty"`
	Tag    string `protobuf:"bytes,4,opt,name=tag,proto3" json:"tag,omitempty"`
	Desc   string `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc,omitempty"`
	Img    string `protobuf:"bytes,6,opt,name=img,proto3" json:"img,omitempty"`
}

func (m *FindfriendExamResult) Reset()         { *m = FindfriendExamResult{} }
func (m *FindfriendExamResult) String() string { return proto.CompactTextString(m) }
func (*FindfriendExamResult) ProtoMessage()    {}
func (*FindfriendExamResult) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendexam, []int{13}
}

func (m *FindfriendExamResult) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *FindfriendExamResult) GetExamId() uint32 {
	if m != nil {
		return m.ExamId
	}
	return 0
}

func (m *FindfriendExamResult) GetResult() uint64 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *FindfriendExamResult) GetTag() string {
	if m != nil {
		return m.Tag
	}
	return ""
}

func (m *FindfriendExamResult) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *FindfriendExamResult) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

type GetFindfriendExamResultReq struct {
	ExamId uint32 `protobuf:"varint,1,opt,name=exam_id,json=examId,proto3" json:"exam_id,omitempty"`
	Result uint64 `protobuf:"varint,2,opt,name=result,proto3" json:"result,omitempty"`
}

func (m *GetFindfriendExamResultReq) Reset()         { *m = GetFindfriendExamResultReq{} }
func (m *GetFindfriendExamResultReq) String() string { return proto.CompactTextString(m) }
func (*GetFindfriendExamResultReq) ProtoMessage()    {}
func (*GetFindfriendExamResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendexam, []int{14}
}

func (m *GetFindfriendExamResultReq) GetExamId() uint32 {
	if m != nil {
		return m.ExamId
	}
	return 0
}

func (m *GetFindfriendExamResultReq) GetResult() uint64 {
	if m != nil {
		return m.Result
	}
	return 0
}

type GetFindfriendExamResultListResp struct {
	ResultList []*FindfriendExamResult `protobuf:"bytes,1,rep,name=result_list,json=resultList" json:"result_list,omitempty"`
}

func (m *GetFindfriendExamResultListResp) Reset()         { *m = GetFindfriendExamResultListResp{} }
func (m *GetFindfriendExamResultListResp) String() string { return proto.CompactTextString(m) }
func (*GetFindfriendExamResultListResp) ProtoMessage()    {}
func (*GetFindfriendExamResultListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendexam, []int{15}
}

func (m *GetFindfriendExamResultListResp) GetResultList() []*FindfriendExamResult {
	if m != nil {
		return m.ResultList
	}
	return nil
}

func init() {
	proto.RegisterType((*UpdateUserFindFriendExamReq)(nil), "findfriendexam.UpdateUserFindFriendExamReq")
	proto.RegisterType((*UpdateUserFindFriendExamResp)(nil), "findfriendexam.UpdateUserFindFriendExamResp")
	proto.RegisterType((*StUserExamResult)(nil), "findfriendexam.StUserExamResult")
	proto.RegisterType((*GetUserFindFriendExamReq)(nil), "findfriendexam.GetUserFindFriendExamReq")
	proto.RegisterType((*GetUserFindFriendExamResp)(nil), "findfriendexam.GetUserFindFriendExamResp")
	proto.RegisterType((*CheckUserFindFriendExamReq)(nil), "findfriendexam.CheckUserFindFriendExamReq")
	proto.RegisterType((*CheckUserFindFriendExamResp)(nil), "findfriendexam.CheckUserFindFriendExamResp")
	proto.RegisterType((*FindfriendExamSummary)(nil), "findfriendexam.FindfriendExamSummary")
	proto.RegisterType((*FindfriendExamIdReq)(nil), "findfriendexam.FindfriendExamIdReq")
	proto.RegisterType((*GetFindfriendExamSummaryListReq)(nil), "findfriendexam.GetFindfriendExamSummaryListReq")
	proto.RegisterType((*GetFindfriendExamSummaryListResp)(nil), "findfriendexam.GetFindfriendExamSummaryListResp")
	proto.RegisterType((*FindfriendExamQuestion)(nil), "findfriendexam.FindfriendExamQuestion")
	proto.RegisterType((*GetFindfriendExamQuestionListResp)(nil), "findfriendexam.GetFindfriendExamQuestionListResp")
	proto.RegisterType((*FindfriendExamResult)(nil), "findfriendexam.FindfriendExamResult")
	proto.RegisterType((*GetFindfriendExamResultReq)(nil), "findfriendexam.GetFindfriendExamResultReq")
	proto.RegisterType((*GetFindfriendExamResultListResp)(nil), "findfriendexam.GetFindfriendExamResultListResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for FindFriendExam service

type FindFriendExamClient interface {
	UpdateUserFindFriendExam(ctx context.Context, in *UpdateUserFindFriendExamReq, opts ...grpc.CallOption) (*UpdateUserFindFriendExamResp, error)
	GetUserFindFriendExam(ctx context.Context, in *GetUserFindFriendExamReq, opts ...grpc.CallOption) (*GetUserFindFriendExamResp, error)
	CheckUserFindFriendExam(ctx context.Context, in *CheckUserFindFriendExamReq, opts ...grpc.CallOption) (*CheckUserFindFriendExamResp, error)
	SetFindfriendExamSummary(ctx context.Context, in *FindfriendExamSummary, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelFindfriendExamSummary(ctx context.Context, in *FindfriendExamIdReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetFindfriendExamSummary(ctx context.Context, in *FindfriendExamIdReq, opts ...grpc.CallOption) (*FindfriendExamSummary, error)
	GetFindfriendExamSummaryByGameId(ctx context.Context, in *FindfriendExamIdReq, opts ...grpc.CallOption) (*FindfriendExamSummary, error)
	GetFindfriendExamSummaryList(ctx context.Context, in *GetFindfriendExamSummaryListReq, opts ...grpc.CallOption) (*GetFindfriendExamSummaryListResp, error)
	SetFindfriendExamQuestion(ctx context.Context, in *FindfriendExamQuestion, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetFindfriendExamQuestionList(ctx context.Context, in *FindfriendExamIdReq, opts ...grpc.CallOption) (*GetFindfriendExamQuestionListResp, error)
	GetFindfriendExamQuestionListForUser(ctx context.Context, in *FindfriendExamIdReq, opts ...grpc.CallOption) (*GetFindfriendExamQuestionListResp, error)
	SetFindfriendExamResult(ctx context.Context, in *FindfriendExamResult, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetFindfriendExamResultList(ctx context.Context, in *FindfriendExamIdReq, opts ...grpc.CallOption) (*GetFindfriendExamResultListResp, error)
	DelFindfriendExamQuestion(ctx context.Context, in *FindfriendExamIdReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelFindfriendExamResult(ctx context.Context, in *FindfriendExamIdReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	BatSetFindfriendExamQuestion(ctx context.Context, in *GetFindfriendExamQuestionListResp, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	BatSetFindfriendExamResult(ctx context.Context, in *GetFindfriendExamResultListResp, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetFindfriendExamQuestion(ctx context.Context, in *FindfriendExamIdReq, opts ...grpc.CallOption) (*FindfriendExamQuestion, error)
}

type findFriendExamClient struct {
	cc *grpc.ClientConn
}

func NewFindFriendExamClient(cc *grpc.ClientConn) FindFriendExamClient {
	return &findFriendExamClient{cc}
}

func (c *findFriendExamClient) UpdateUserFindFriendExam(ctx context.Context, in *UpdateUserFindFriendExamReq, opts ...grpc.CallOption) (*UpdateUserFindFriendExamResp, error) {
	out := new(UpdateUserFindFriendExamResp)
	err := grpc.Invoke(ctx, "/findfriendexam.FindFriendExam/UpdateUserFindFriendExam", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendExamClient) GetUserFindFriendExam(ctx context.Context, in *GetUserFindFriendExamReq, opts ...grpc.CallOption) (*GetUserFindFriendExamResp, error) {
	out := new(GetUserFindFriendExamResp)
	err := grpc.Invoke(ctx, "/findfriendexam.FindFriendExam/GetUserFindFriendExam", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendExamClient) CheckUserFindFriendExam(ctx context.Context, in *CheckUserFindFriendExamReq, opts ...grpc.CallOption) (*CheckUserFindFriendExamResp, error) {
	out := new(CheckUserFindFriendExamResp)
	err := grpc.Invoke(ctx, "/findfriendexam.FindFriendExam/CheckUserFindFriendExam", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendExamClient) SetFindfriendExamSummary(ctx context.Context, in *FindfriendExamSummary, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/findfriendexam.FindFriendExam/SetFindfriendExamSummary", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendExamClient) DelFindfriendExamSummary(ctx context.Context, in *FindfriendExamIdReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/findfriendexam.FindFriendExam/DelFindfriendExamSummary", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendExamClient) GetFindfriendExamSummary(ctx context.Context, in *FindfriendExamIdReq, opts ...grpc.CallOption) (*FindfriendExamSummary, error) {
	out := new(FindfriendExamSummary)
	err := grpc.Invoke(ctx, "/findfriendexam.FindFriendExam/GetFindfriendExamSummary", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendExamClient) GetFindfriendExamSummaryByGameId(ctx context.Context, in *FindfriendExamIdReq, opts ...grpc.CallOption) (*FindfriendExamSummary, error) {
	out := new(FindfriendExamSummary)
	err := grpc.Invoke(ctx, "/findfriendexam.FindFriendExam/GetFindfriendExamSummaryByGameId", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendExamClient) GetFindfriendExamSummaryList(ctx context.Context, in *GetFindfriendExamSummaryListReq, opts ...grpc.CallOption) (*GetFindfriendExamSummaryListResp, error) {
	out := new(GetFindfriendExamSummaryListResp)
	err := grpc.Invoke(ctx, "/findfriendexam.FindFriendExam/GetFindfriendExamSummaryList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendExamClient) SetFindfriendExamQuestion(ctx context.Context, in *FindfriendExamQuestion, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/findfriendexam.FindFriendExam/SetFindfriendExamQuestion", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendExamClient) GetFindfriendExamQuestionList(ctx context.Context, in *FindfriendExamIdReq, opts ...grpc.CallOption) (*GetFindfriendExamQuestionListResp, error) {
	out := new(GetFindfriendExamQuestionListResp)
	err := grpc.Invoke(ctx, "/findfriendexam.FindFriendExam/GetFindfriendExamQuestionList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendExamClient) GetFindfriendExamQuestionListForUser(ctx context.Context, in *FindfriendExamIdReq, opts ...grpc.CallOption) (*GetFindfriendExamQuestionListResp, error) {
	out := new(GetFindfriendExamQuestionListResp)
	err := grpc.Invoke(ctx, "/findfriendexam.FindFriendExam/GetFindfriendExamQuestionListForUser", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendExamClient) SetFindfriendExamResult(ctx context.Context, in *FindfriendExamResult, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/findfriendexam.FindFriendExam/SetFindfriendExamResult", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendExamClient) GetFindfriendExamResultList(ctx context.Context, in *FindfriendExamIdReq, opts ...grpc.CallOption) (*GetFindfriendExamResultListResp, error) {
	out := new(GetFindfriendExamResultListResp)
	err := grpc.Invoke(ctx, "/findfriendexam.FindFriendExam/GetFindfriendExamResultList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendExamClient) DelFindfriendExamQuestion(ctx context.Context, in *FindfriendExamIdReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/findfriendexam.FindFriendExam/DelFindfriendExamQuestion", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendExamClient) DelFindfriendExamResult(ctx context.Context, in *FindfriendExamIdReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/findfriendexam.FindFriendExam/DelFindfriendExamResult", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendExamClient) BatSetFindfriendExamQuestion(ctx context.Context, in *GetFindfriendExamQuestionListResp, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/findfriendexam.FindFriendExam/BatSetFindfriendExamQuestion", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendExamClient) BatSetFindfriendExamResult(ctx context.Context, in *GetFindfriendExamResultListResp, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/findfriendexam.FindFriendExam/BatSetFindfriendExamResult", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *findFriendExamClient) GetFindfriendExamQuestion(ctx context.Context, in *FindfriendExamIdReq, opts ...grpc.CallOption) (*FindfriendExamQuestion, error) {
	out := new(FindfriendExamQuestion)
	err := grpc.Invoke(ctx, "/findfriendexam.FindFriendExam/GetFindfriendExamQuestion", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for FindFriendExam service

type FindFriendExamServer interface {
	UpdateUserFindFriendExam(context.Context, *UpdateUserFindFriendExamReq) (*UpdateUserFindFriendExamResp, error)
	GetUserFindFriendExam(context.Context, *GetUserFindFriendExamReq) (*GetUserFindFriendExamResp, error)
	CheckUserFindFriendExam(context.Context, *CheckUserFindFriendExamReq) (*CheckUserFindFriendExamResp, error)
	SetFindfriendExamSummary(context.Context, *FindfriendExamSummary) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelFindfriendExamSummary(context.Context, *FindfriendExamIdReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetFindfriendExamSummary(context.Context, *FindfriendExamIdReq) (*FindfriendExamSummary, error)
	GetFindfriendExamSummaryByGameId(context.Context, *FindfriendExamIdReq) (*FindfriendExamSummary, error)
	GetFindfriendExamSummaryList(context.Context, *GetFindfriendExamSummaryListReq) (*GetFindfriendExamSummaryListResp, error)
	SetFindfriendExamQuestion(context.Context, *FindfriendExamQuestion) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetFindfriendExamQuestionList(context.Context, *FindfriendExamIdReq) (*GetFindfriendExamQuestionListResp, error)
	GetFindfriendExamQuestionListForUser(context.Context, *FindfriendExamIdReq) (*GetFindfriendExamQuestionListResp, error)
	SetFindfriendExamResult(context.Context, *FindfriendExamResult) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetFindfriendExamResultList(context.Context, *FindfriendExamIdReq) (*GetFindfriendExamResultListResp, error)
	DelFindfriendExamQuestion(context.Context, *FindfriendExamIdReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelFindfriendExamResult(context.Context, *FindfriendExamIdReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	BatSetFindfriendExamQuestion(context.Context, *GetFindfriendExamQuestionListResp) (*tlvpickle.SKBuiltinEmpty_PB, error)
	BatSetFindfriendExamResult(context.Context, *GetFindfriendExamResultListResp) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetFindfriendExamQuestion(context.Context, *FindfriendExamIdReq) (*FindfriendExamQuestion, error)
}

func RegisterFindFriendExamServer(s *grpc.Server, srv FindFriendExamServer) {
	s.RegisterService(&_FindFriendExam_serviceDesc, srv)
}

func _FindFriendExam_UpdateUserFindFriendExam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserFindFriendExamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendExamServer).UpdateUserFindFriendExam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/findfriendexam.FindFriendExam/UpdateUserFindFriendExam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendExamServer).UpdateUserFindFriendExam(ctx, req.(*UpdateUserFindFriendExamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriendExam_GetUserFindFriendExam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserFindFriendExamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendExamServer).GetUserFindFriendExam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/findfriendexam.FindFriendExam/GetUserFindFriendExam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendExamServer).GetUserFindFriendExam(ctx, req.(*GetUserFindFriendExamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriendExam_CheckUserFindFriendExam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserFindFriendExamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendExamServer).CheckUserFindFriendExam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/findfriendexam.FindFriendExam/CheckUserFindFriendExam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendExamServer).CheckUserFindFriendExam(ctx, req.(*CheckUserFindFriendExamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriendExam_SetFindfriendExamSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindfriendExamSummary)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendExamServer).SetFindfriendExamSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/findfriendexam.FindFriendExam/SetFindfriendExamSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendExamServer).SetFindfriendExamSummary(ctx, req.(*FindfriendExamSummary))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriendExam_DelFindfriendExamSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindfriendExamIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendExamServer).DelFindfriendExamSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/findfriendexam.FindFriendExam/DelFindfriendExamSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendExamServer).DelFindfriendExamSummary(ctx, req.(*FindfriendExamIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriendExam_GetFindfriendExamSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindfriendExamIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendExamServer).GetFindfriendExamSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/findfriendexam.FindFriendExam/GetFindfriendExamSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendExamServer).GetFindfriendExamSummary(ctx, req.(*FindfriendExamIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriendExam_GetFindfriendExamSummaryByGameId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindfriendExamIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendExamServer).GetFindfriendExamSummaryByGameId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/findfriendexam.FindFriendExam/GetFindfriendExamSummaryByGameId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendExamServer).GetFindfriendExamSummaryByGameId(ctx, req.(*FindfriendExamIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriendExam_GetFindfriendExamSummaryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFindfriendExamSummaryListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendExamServer).GetFindfriendExamSummaryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/findfriendexam.FindFriendExam/GetFindfriendExamSummaryList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendExamServer).GetFindfriendExamSummaryList(ctx, req.(*GetFindfriendExamSummaryListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriendExam_SetFindfriendExamQuestion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindfriendExamQuestion)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendExamServer).SetFindfriendExamQuestion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/findfriendexam.FindFriendExam/SetFindfriendExamQuestion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendExamServer).SetFindfriendExamQuestion(ctx, req.(*FindfriendExamQuestion))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriendExam_GetFindfriendExamQuestionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindfriendExamIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendExamServer).GetFindfriendExamQuestionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/findfriendexam.FindFriendExam/GetFindfriendExamQuestionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendExamServer).GetFindfriendExamQuestionList(ctx, req.(*FindfriendExamIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriendExam_GetFindfriendExamQuestionListForUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindfriendExamIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendExamServer).GetFindfriendExamQuestionListForUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/findfriendexam.FindFriendExam/GetFindfriendExamQuestionListForUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendExamServer).GetFindfriendExamQuestionListForUser(ctx, req.(*FindfriendExamIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriendExam_SetFindfriendExamResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindfriendExamResult)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendExamServer).SetFindfriendExamResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/findfriendexam.FindFriendExam/SetFindfriendExamResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendExamServer).SetFindfriendExamResult(ctx, req.(*FindfriendExamResult))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriendExam_GetFindfriendExamResultList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindfriendExamIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendExamServer).GetFindfriendExamResultList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/findfriendexam.FindFriendExam/GetFindfriendExamResultList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendExamServer).GetFindfriendExamResultList(ctx, req.(*FindfriendExamIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriendExam_DelFindfriendExamQuestion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindfriendExamIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendExamServer).DelFindfriendExamQuestion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/findfriendexam.FindFriendExam/DelFindfriendExamQuestion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendExamServer).DelFindfriendExamQuestion(ctx, req.(*FindfriendExamIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriendExam_DelFindfriendExamResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindfriendExamIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendExamServer).DelFindfriendExamResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/findfriendexam.FindFriendExam/DelFindfriendExamResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendExamServer).DelFindfriendExamResult(ctx, req.(*FindfriendExamIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriendExam_BatSetFindfriendExamQuestion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFindfriendExamQuestionListResp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendExamServer).BatSetFindfriendExamQuestion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/findfriendexam.FindFriendExam/BatSetFindfriendExamQuestion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendExamServer).BatSetFindfriendExamQuestion(ctx, req.(*GetFindfriendExamQuestionListResp))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriendExam_BatSetFindfriendExamResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFindfriendExamResultListResp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendExamServer).BatSetFindfriendExamResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/findfriendexam.FindFriendExam/BatSetFindfriendExamResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendExamServer).BatSetFindfriendExamResult(ctx, req.(*GetFindfriendExamResultListResp))
	}
	return interceptor(ctx, in, info, handler)
}

func _FindFriendExam_GetFindfriendExamQuestion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindfriendExamIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FindFriendExamServer).GetFindfriendExamQuestion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/findfriendexam.FindFriendExam/GetFindfriendExamQuestion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FindFriendExamServer).GetFindfriendExamQuestion(ctx, req.(*FindfriendExamIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _FindFriendExam_serviceDesc = grpc.ServiceDesc{
	ServiceName: "findfriendexam.FindFriendExam",
	HandlerType: (*FindFriendExamServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateUserFindFriendExam",
			Handler:    _FindFriendExam_UpdateUserFindFriendExam_Handler,
		},
		{
			MethodName: "GetUserFindFriendExam",
			Handler:    _FindFriendExam_GetUserFindFriendExam_Handler,
		},
		{
			MethodName: "CheckUserFindFriendExam",
			Handler:    _FindFriendExam_CheckUserFindFriendExam_Handler,
		},
		{
			MethodName: "SetFindfriendExamSummary",
			Handler:    _FindFriendExam_SetFindfriendExamSummary_Handler,
		},
		{
			MethodName: "DelFindfriendExamSummary",
			Handler:    _FindFriendExam_DelFindfriendExamSummary_Handler,
		},
		{
			MethodName: "GetFindfriendExamSummary",
			Handler:    _FindFriendExam_GetFindfriendExamSummary_Handler,
		},
		{
			MethodName: "GetFindfriendExamSummaryByGameId",
			Handler:    _FindFriendExam_GetFindfriendExamSummaryByGameId_Handler,
		},
		{
			MethodName: "GetFindfriendExamSummaryList",
			Handler:    _FindFriendExam_GetFindfriendExamSummaryList_Handler,
		},
		{
			MethodName: "SetFindfriendExamQuestion",
			Handler:    _FindFriendExam_SetFindfriendExamQuestion_Handler,
		},
		{
			MethodName: "GetFindfriendExamQuestionList",
			Handler:    _FindFriendExam_GetFindfriendExamQuestionList_Handler,
		},
		{
			MethodName: "GetFindfriendExamQuestionListForUser",
			Handler:    _FindFriendExam_GetFindfriendExamQuestionListForUser_Handler,
		},
		{
			MethodName: "SetFindfriendExamResult",
			Handler:    _FindFriendExam_SetFindfriendExamResult_Handler,
		},
		{
			MethodName: "GetFindfriendExamResultList",
			Handler:    _FindFriendExam_GetFindfriendExamResultList_Handler,
		},
		{
			MethodName: "DelFindfriendExamQuestion",
			Handler:    _FindFriendExam_DelFindfriendExamQuestion_Handler,
		},
		{
			MethodName: "DelFindfriendExamResult",
			Handler:    _FindFriendExam_DelFindfriendExamResult_Handler,
		},
		{
			MethodName: "BatSetFindfriendExamQuestion",
			Handler:    _FindFriendExam_BatSetFindfriendExamQuestion_Handler,
		},
		{
			MethodName: "BatSetFindfriendExamResult",
			Handler:    _FindFriendExam_BatSetFindfriendExamResult_Handler,
		},
		{
			MethodName: "GetFindfriendExamQuestion",
			Handler:    _FindFriendExam_GetFindfriendExamQuestion_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/findfriend_exam/findfriendexam.proto",
}

func (m *UpdateUserFindFriendExamReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserFindFriendExamReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.Uid))
	}
	if m.ExamId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.ExamId))
	}
	if m.Scores != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.Scores))
	}
	return i, nil
}

func (m *UpdateUserFindFriendExamResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserFindFriendExamResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *StUserExamResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StUserExamResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ExamId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.ExamId))
	}
	if m.GameId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.GameId))
	}
	if len(m.ExamName) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(len(m.ExamName)))
		i += copy(dAtA[i:], m.ExamName)
	}
	if m.Scores != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.Scores))
	}
	if len(m.TagName) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(len(m.TagName)))
		i += copy(dAtA[i:], m.TagName)
	}
	if len(m.Img) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(len(m.Img)))
		i += copy(dAtA[i:], m.Img)
	}
	if len(m.Desc) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(len(m.Desc)))
		i += copy(dAtA[i:], m.Desc)
	}
	if len(m.ScoreList) > 0 {
		dAtA2 := make([]byte, len(m.ScoreList)*10)
		var j1 int
		for _, num := range m.ScoreList {
			for num >= 1<<7 {
				dAtA2[j1] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j1++
			}
			dAtA2[j1] = uint8(num)
			j1++
		}
		dAtA[i] = 0x42
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(j1))
		i += copy(dAtA[i:], dAtA2[:j1])
	}
	return i, nil
}

func (m *GetUserFindFriendExamReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserFindFriendExamReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.Uid))
	}
	return i, nil
}

func (m *GetUserFindFriendExamResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserFindFriendExamResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ExamResult != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.ExamResult.Size()))
		n3, err := m.ExamResult.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *CheckUserFindFriendExamReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserFindFriendExamReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.Uid))
	}
	return i, nil
}

func (m *CheckUserFindFriendExamResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserFindFriendExamResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ExamId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.ExamId))
	}
	if m.Scores != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.Scores))
	}
	return i, nil
}

func (m *FindfriendExamSummary) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindfriendExamSummary) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ExamId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.ExamId))
	}
	if m.GameId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.GameId))
	}
	if len(m.Name) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(len(m.Name)))
		i += copy(dAtA[i:], m.Name)
	}
	if len(m.Icon) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(len(m.Icon)))
		i += copy(dAtA[i:], m.Icon)
	}
	if m.Status != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.Status))
	}
	if m.Rank != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.Rank))
	}
	if len(m.QuestionTypeNames) > 0 {
		for _, s := range m.QuestionTypeNames {
			dAtA[i] = 0x3a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *FindfriendExamIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindfriendExamIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Id != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.Id))
	}
	return i, nil
}

func (m *GetFindfriendExamSummaryListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFindfriendExamSummaryListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.IncludeInvalid {
		dAtA[i] = 0x8
		i++
		if m.IncludeInvalid {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *GetFindfriendExamSummaryListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFindfriendExamSummaryListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ExamList) > 0 {
		for _, msg := range m.ExamList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintFindfriendexam(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *FindfriendExamQuestion) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindfriendExamQuestion) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Id != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.Id))
	}
	if m.ExamId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.ExamId))
	}
	if m.QuestionType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.QuestionType))
	}
	if len(m.Question) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(len(m.Question)))
		i += copy(dAtA[i:], m.Question)
	}
	if len(m.Options) > 0 {
		for _, s := range m.Options {
			dAtA[i] = 0x2a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *GetFindfriendExamQuestionListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFindfriendExamQuestionListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.QuestionList) > 0 {
		for _, msg := range m.QuestionList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintFindfriendexam(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *FindfriendExamResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindfriendExamResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Id != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.Id))
	}
	if m.ExamId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.ExamId))
	}
	if m.Result != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.Result))
	}
	if len(m.Tag) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(len(m.Tag)))
		i += copy(dAtA[i:], m.Tag)
	}
	if len(m.Desc) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(len(m.Desc)))
		i += copy(dAtA[i:], m.Desc)
	}
	if len(m.Img) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(len(m.Img)))
		i += copy(dAtA[i:], m.Img)
	}
	return i, nil
}

func (m *GetFindfriendExamResultReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFindfriendExamResultReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ExamId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.ExamId))
	}
	if m.Result != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintFindfriendexam(dAtA, i, uint64(m.Result))
	}
	return i, nil
}

func (m *GetFindfriendExamResultListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFindfriendExamResultListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ResultList) > 0 {
		for _, msg := range m.ResultList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintFindfriendexam(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func encodeFixed64Findfriendexam(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Findfriendexam(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintFindfriendexam(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *UpdateUserFindFriendExamReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovFindfriendexam(uint64(m.Uid))
	}
	if m.ExamId != 0 {
		n += 1 + sovFindfriendexam(uint64(m.ExamId))
	}
	if m.Scores != 0 {
		n += 1 + sovFindfriendexam(uint64(m.Scores))
	}
	return n
}

func (m *UpdateUserFindFriendExamResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *StUserExamResult) Size() (n int) {
	var l int
	_ = l
	if m.ExamId != 0 {
		n += 1 + sovFindfriendexam(uint64(m.ExamId))
	}
	if m.GameId != 0 {
		n += 1 + sovFindfriendexam(uint64(m.GameId))
	}
	l = len(m.ExamName)
	if l > 0 {
		n += 1 + l + sovFindfriendexam(uint64(l))
	}
	if m.Scores != 0 {
		n += 1 + sovFindfriendexam(uint64(m.Scores))
	}
	l = len(m.TagName)
	if l > 0 {
		n += 1 + l + sovFindfriendexam(uint64(l))
	}
	l = len(m.Img)
	if l > 0 {
		n += 1 + l + sovFindfriendexam(uint64(l))
	}
	l = len(m.Desc)
	if l > 0 {
		n += 1 + l + sovFindfriendexam(uint64(l))
	}
	if len(m.ScoreList) > 0 {
		l = 0
		for _, e := range m.ScoreList {
			l += sovFindfriendexam(uint64(e))
		}
		n += 1 + sovFindfriendexam(uint64(l)) + l
	}
	return n
}

func (m *GetUserFindFriendExamReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovFindfriendexam(uint64(m.Uid))
	}
	return n
}

func (m *GetUserFindFriendExamResp) Size() (n int) {
	var l int
	_ = l
	if m.ExamResult != nil {
		l = m.ExamResult.Size()
		n += 1 + l + sovFindfriendexam(uint64(l))
	}
	return n
}

func (m *CheckUserFindFriendExamReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovFindfriendexam(uint64(m.Uid))
	}
	return n
}

func (m *CheckUserFindFriendExamResp) Size() (n int) {
	var l int
	_ = l
	if m.ExamId != 0 {
		n += 1 + sovFindfriendexam(uint64(m.ExamId))
	}
	if m.Scores != 0 {
		n += 1 + sovFindfriendexam(uint64(m.Scores))
	}
	return n
}

func (m *FindfriendExamSummary) Size() (n int) {
	var l int
	_ = l
	if m.ExamId != 0 {
		n += 1 + sovFindfriendexam(uint64(m.ExamId))
	}
	if m.GameId != 0 {
		n += 1 + sovFindfriendexam(uint64(m.GameId))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovFindfriendexam(uint64(l))
	}
	l = len(m.Icon)
	if l > 0 {
		n += 1 + l + sovFindfriendexam(uint64(l))
	}
	if m.Status != 0 {
		n += 1 + sovFindfriendexam(uint64(m.Status))
	}
	if m.Rank != 0 {
		n += 1 + sovFindfriendexam(uint64(m.Rank))
	}
	if len(m.QuestionTypeNames) > 0 {
		for _, s := range m.QuestionTypeNames {
			l = len(s)
			n += 1 + l + sovFindfriendexam(uint64(l))
		}
	}
	return n
}

func (m *FindfriendExamIdReq) Size() (n int) {
	var l int
	_ = l
	if m.Id != 0 {
		n += 1 + sovFindfriendexam(uint64(m.Id))
	}
	return n
}

func (m *GetFindfriendExamSummaryListReq) Size() (n int) {
	var l int
	_ = l
	if m.IncludeInvalid {
		n += 2
	}
	return n
}

func (m *GetFindfriendExamSummaryListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ExamList) > 0 {
		for _, e := range m.ExamList {
			l = e.Size()
			n += 1 + l + sovFindfriendexam(uint64(l))
		}
	}
	return n
}

func (m *FindfriendExamQuestion) Size() (n int) {
	var l int
	_ = l
	if m.Id != 0 {
		n += 1 + sovFindfriendexam(uint64(m.Id))
	}
	if m.ExamId != 0 {
		n += 1 + sovFindfriendexam(uint64(m.ExamId))
	}
	if m.QuestionType != 0 {
		n += 1 + sovFindfriendexam(uint64(m.QuestionType))
	}
	l = len(m.Question)
	if l > 0 {
		n += 1 + l + sovFindfriendexam(uint64(l))
	}
	if len(m.Options) > 0 {
		for _, s := range m.Options {
			l = len(s)
			n += 1 + l + sovFindfriendexam(uint64(l))
		}
	}
	return n
}

func (m *GetFindfriendExamQuestionListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.QuestionList) > 0 {
		for _, e := range m.QuestionList {
			l = e.Size()
			n += 1 + l + sovFindfriendexam(uint64(l))
		}
	}
	return n
}

func (m *FindfriendExamResult) Size() (n int) {
	var l int
	_ = l
	if m.Id != 0 {
		n += 1 + sovFindfriendexam(uint64(m.Id))
	}
	if m.ExamId != 0 {
		n += 1 + sovFindfriendexam(uint64(m.ExamId))
	}
	if m.Result != 0 {
		n += 1 + sovFindfriendexam(uint64(m.Result))
	}
	l = len(m.Tag)
	if l > 0 {
		n += 1 + l + sovFindfriendexam(uint64(l))
	}
	l = len(m.Desc)
	if l > 0 {
		n += 1 + l + sovFindfriendexam(uint64(l))
	}
	l = len(m.Img)
	if l > 0 {
		n += 1 + l + sovFindfriendexam(uint64(l))
	}
	return n
}

func (m *GetFindfriendExamResultReq) Size() (n int) {
	var l int
	_ = l
	if m.ExamId != 0 {
		n += 1 + sovFindfriendexam(uint64(m.ExamId))
	}
	if m.Result != 0 {
		n += 1 + sovFindfriendexam(uint64(m.Result))
	}
	return n
}

func (m *GetFindfriendExamResultListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ResultList) > 0 {
		for _, e := range m.ResultList {
			l = e.Size()
			n += 1 + l + sovFindfriendexam(uint64(l))
		}
	}
	return n
}

func sovFindfriendexam(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozFindfriendexam(x uint64) (n int) {
	return sovFindfriendexam(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *UpdateUserFindFriendExamReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendexam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserFindFriendExamReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserFindFriendExamReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamId", wireType)
			}
			m.ExamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Scores", wireType)
			}
			m.Scores = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Scores |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendexam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserFindFriendExamResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendexam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserFindFriendExamResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserFindFriendExamResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendexam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StUserExamResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendexam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StUserExamResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StUserExamResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamId", wireType)
			}
			m.ExamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExamName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Scores", wireType)
			}
			m.Scores = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Scores |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Img", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Img = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Desc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFindfriendexam
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ScoreList = append(m.ScoreList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFindfriendexam
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFindfriendexam
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFindfriendexam
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ScoreList = append(m.ScoreList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ScoreList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendexam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserFindFriendExamReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendexam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserFindFriendExamReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserFindFriendExamReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendexam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserFindFriendExamResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendexam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserFindFriendExamResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserFindFriendExamResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamResult", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ExamResult == nil {
				m.ExamResult = &StUserExamResult{}
			}
			if err := m.ExamResult.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendexam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserFindFriendExamReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendexam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserFindFriendExamReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserFindFriendExamReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendexam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserFindFriendExamResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendexam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserFindFriendExamResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserFindFriendExamResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamId", wireType)
			}
			m.ExamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Scores", wireType)
			}
			m.Scores = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Scores |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendexam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindfriendExamSummary) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendexam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindfriendExamSummary: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindfriendExamSummary: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamId", wireType)
			}
			m.ExamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Icon", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Icon = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rank", wireType)
			}
			m.Rank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuestionTypeNames", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.QuestionTypeNames = append(m.QuestionTypeNames, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendexam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindfriendExamIdReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendexam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindfriendExamIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindfriendExamIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendexam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFindfriendExamSummaryListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendexam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFindfriendExamSummaryListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFindfriendExamSummaryListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IncludeInvalid", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IncludeInvalid = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendexam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFindfriendExamSummaryListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendexam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFindfriendExamSummaryListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFindfriendExamSummaryListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExamList = append(m.ExamList, &FindfriendExamSummary{})
			if err := m.ExamList[len(m.ExamList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendexam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindfriendExamQuestion) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendexam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindfriendExamQuestion: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindfriendExamQuestion: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamId", wireType)
			}
			m.ExamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuestionType", wireType)
			}
			m.QuestionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QuestionType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Question", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Question = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Options", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Options = append(m.Options, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendexam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFindfriendExamQuestionListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendexam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFindfriendExamQuestionListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFindfriendExamQuestionListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuestionList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.QuestionList = append(m.QuestionList, &FindfriendExamQuestion{})
			if err := m.QuestionList[len(m.QuestionList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendexam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindfriendExamResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendexam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindfriendExamResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindfriendExamResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamId", wireType)
			}
			m.ExamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Tag", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Tag = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Desc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Img", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Img = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendexam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFindfriendExamResultReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendexam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFindfriendExamResultReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFindfriendExamResultReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamId", wireType)
			}
			m.ExamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendexam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFindfriendExamResultListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendexam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFindfriendExamResultListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFindfriendExamResultListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultList = append(m.ResultList, &FindfriendExamResult{})
			if err := m.ResultList[len(m.ResultList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendexam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendexam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipFindfriendexam(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowFindfriendexam
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFindfriendexam
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthFindfriendexam
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowFindfriendexam
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipFindfriendexam(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthFindfriendexam = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowFindfriendexam   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/findfriend_exam/findfriendexam.proto", fileDescriptorFindfriendexam)
}

var fileDescriptorFindfriendexam = []byte{
	// 1294 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x58, 0xcf, 0x8f, 0xdb, 0xc4,
	0x17, 0xef, 0x24, 0xdb, 0xfc, 0x78, 0x69, 0xd2, 0xed, 0xf4, 0xdb, 0x8d, 0x37, 0xbb, 0xdd, 0xaf,
	0xeb, 0xb6, 0x10, 0x68, 0x93, 0x85, 0x22, 0xf5, 0x60, 0x59, 0x96, 0x08, 0x74, 0xab, 0xa5, 0x50,
	0xb5, 0x0e, 0xbd, 0x12, 0xdc, 0x78, 0x9a, 0x5a, 0x1b, 0xff, 0x58, 0x7b, 0x5c, 0xba, 0x5c, 0x80,
	0x53, 0x11, 0x02, 0x01, 0x95, 0x10, 0x97, 0x1e, 0xf7, 0x8a, 0xc4, 0x91, 0x13, 0x47, 0xc4, 0x05,
	0xc4, 0x95, 0x1b, 0x2a, 0x97, 0xfd, 0x33, 0xd0, 0x8c, 0x1d, 0xd7, 0x49, 0xec, 0xac, 0x4b, 0x97,
	0xdb, 0xcc, 0xcb, 0xcc, 0xbc, 0xcf, 0xfb, 0xbc, 0xf7, 0x3e, 0xcf, 0xbb, 0xd0, 0xf6, 0xbd, 0xe1,
	0xe6, 0x3d, 0xd3, 0x36, 0xee, 0x79, 0x26, 0xb1, 0x8d, 0x01, 0x79, 0xa8, 0x5b, 0x89, 0x3d, 0xdb,
	0x76, 0x5d, 0xcf, 0xa1, 0x0e, 0x6e, 0x4c, 0x5b, 0x5b, 0x17, 0x86, 0x8e, 0x65, 0x39, 0xf6, 0x26,
	0x1d, 0x3f, 0x70, 0xcd, 0xe1, 0xce, 0x98, 0x6c, 0xfa, 0x3b, 0x77, 0x03, 0x73, 0x4c, 0x4d, 0x9b,
	0xee, 0xb9, 0x24, 0xbc, 0x25, 0x7d, 0x08, 0x6b, 0x77, 0x5c, 0x43, 0xa7, 0xe4, 0x8e, 0x4f, 0xbc,
	0x2d, 0xd3, 0x36, 0xb6, 0xf8, 0x0b, 0xd7, 0x1e, 0xea, 0x96, 0x46, 0x76, 0xf1, 0x32, 0x14, 0x03,
	0xd3, 0x10, 0x90, 0x88, 0xda, 0x75, 0x8d, 0x2d, 0x71, 0x13, 0xca, 0xec, 0xf9, 0x81, 0x69, 0x08,
	0x05, 0x6e, 0x2d, 0xb1, 0xed, 0xb6, 0x81, 0x57, 0xa0, 0xe4, 0x0f, 0x1d, 0x8f, 0xf8, 0x42, 0x51,
	0x44, 0xed, 0x25, 0x2d, 0xda, 0x49, 0x1b, 0xb0, 0x9e, 0xed, 0xc1, 0x77, 0xa5, 0x3f, 0x11, 0x2c,
	0xf7, 0x29, 0xfb, 0x31, 0x32, 0x05, 0x63, 0x9a, 0xf4, 0x82, 0xa6, 0xbc, 0x34, 0xa1, 0x3c, 0xd2,
	0x2d, 0x92, 0x70, 0xcf, 0xb6, 0xdb, 0x06, 0x5e, 0x83, 0x2a, 0xbf, 0x61, 0xeb, 0x16, 0xe1, 0x08,
	0xaa, 0x5a, 0x85, 0x19, 0x6e, 0xea, 0x16, 0x49, 0x60, 0x5b, 0x4a, 0x62, 0xc3, 0xab, 0x50, 0xa1,
	0xfa, 0x28, 0xbc, 0x73, 0x9c, 0xdf, 0x29, 0x53, 0x7d, 0xc4, 0xaf, 0x2c, 0x43, 0xd1, 0xb4, 0x46,
	0x42, 0x89, 0x5b, 0xd9, 0x12, 0x63, 0x58, 0x32, 0x88, 0x3f, 0x14, 0xca, 0xdc, 0xc4, 0xd7, 0xf8,
	0x2c, 0x00, 0x7f, 0x6a, 0x30, 0x36, 0x7d, 0x2a, 0x54, 0xc4, 0x62, 0xbb, 0xae, 0x55, 0xb9, 0xe5,
	0x5d, 0xd3, 0xa7, 0xd2, 0x65, 0x10, 0xae, 0x13, 0x9a, 0x93, 0x5a, 0xe9, 0x03, 0x58, 0xcd, 0x38,
	0xed, 0xbb, 0xf8, 0x4d, 0xa8, 0xf1, 0xf8, 0x3c, 0x4e, 0x10, 0xbf, 0x56, 0xbb, 0x22, 0x76, 0x67,
	0x4a, 0x61, 0x96, 0x48, 0x0d, 0x48, 0xbc, 0x96, 0xba, 0xd0, 0x7a, 0xeb, 0x3e, 0x19, 0xee, 0xe4,
	0xc5, 0x73, 0x13, 0xd6, 0x32, 0xcf, 0xfb, 0x6e, 0x76, 0x8e, 0x9e, 0xb1, 0x5d, 0x98, 0xaa, 0x84,
	0xdf, 0x10, 0x9c, 0xd9, 0x8a, 0xf1, 0xb2, 0x77, 0xfa, 0x81, 0x65, 0xe9, 0xde, 0xde, 0xbf, 0x48,
	0x37, 0x86, 0xa5, 0x44, 0xa6, 0xf9, 0x9a, 0xd9, 0xcc, 0xa1, 0x63, 0xf3, 0x1c, 0x57, 0x35, 0xbe,
	0xe6, 0x58, 0xa8, 0x4e, 0x03, 0x9f, 0xe7, 0xb7, 0xae, 0x45, 0x3b, 0x76, 0xd6, 0xd3, 0xed, 0x1d,
	0x9e, 0xdf, 0xba, 0xc6, 0xd7, 0xb8, 0x0b, 0xa7, 0x77, 0x03, 0xe2, 0x53, 0xd3, 0xb1, 0x07, 0xac,
	0x45, 0x78, 0x5d, 0xf8, 0x42, 0x59, 0x2c, 0xb6, 0xab, 0xda, 0xa9, 0xc9, 0x4f, 0xef, 0xef, 0xb9,
	0x84, 0x55, 0x88, 0x2f, 0x5d, 0x84, 0xd3, 0xd3, 0xe1, 0x6c, 0x1b, 0x8c, 0xc8, 0x06, 0x14, 0xe2,
	0x38, 0x0a, 0xa6, 0x21, 0xbd, 0x03, 0xff, 0xbf, 0x4e, 0x68, 0x6a, 0xe0, 0xac, 0x48, 0xd8, 0x95,
	0x97, 0xe1, 0xa4, 0x69, 0x0f, 0xc7, 0x81, 0x41, 0x06, 0xa6, 0xfd, 0x40, 0x1f, 0x47, 0xf7, 0x2b,
	0x5a, 0x23, 0x32, 0x6f, 0x87, 0x56, 0xe9, 0x1e, 0x88, 0x8b, 0xdf, 0xf2, 0x5d, 0xdc, 0x8b, 0x3a,
	0x81, 0x97, 0x24, 0x12, 0x8b, 0xed, 0xda, 0x95, 0x8b, 0xb3, 0x75, 0x92, 0xfa, 0x42, 0xd8, 0x30,
	0xbc, 0x70, 0x9f, 0x20, 0x58, 0x99, 0x3e, 0x73, 0x3b, 0x0a, 0x7f, 0x36, 0xbc, 0x6c, 0x41, 0x38,
	0x0f, 0xf5, 0x29, 0x3a, 0x79, 0xae, 0xea, 0xda, 0x89, 0x24, 0x91, 0xb8, 0x05, 0x95, 0xc9, 0x3e,
	0xca, 0x5b, 0xbc, 0xc7, 0x02, 0x94, 0x1d, 0x97, 0xad, 0x58, 0xf2, 0x58, 0x0e, 0x26, 0x5b, 0xc9,
	0x85, 0x73, 0x73, 0x34, 0x4c, 0x00, 0xc6, 0x3c, 0xdc, 0x48, 0xf8, 0x4f, 0x70, 0xf1, 0xd2, 0x62,
	0x2e, 0x26, 0xcf, 0x3c, 0xc3, 0xc9, 0x09, 0xf9, 0x1a, 0xc1, 0xff, 0xa6, 0x0f, 0x46, 0x4a, 0x95,
	0x9b, 0x8e, 0x15, 0x28, 0x45, 0xbd, 0x1b, 0xe9, 0x63, 0xb8, 0x63, 0x7d, 0x47, 0xf5, 0x51, 0x14,
	0x3c, 0x5b, 0xc6, 0x42, 0x73, 0x3c, 0x21, 0x34, 0x73, 0x72, 0x24, 0xbd, 0x07, 0xad, 0x39, 0x0e,
	0xa2, 0xa6, 0x27, 0xbb, 0x0b, 0x9b, 0x33, 0x82, 0x51, 0x48, 0xc2, 0x90, 0xee, 0xa7, 0x54, 0x69,
	0xf8, 0x5c, 0x4c, 0xe8, 0x35, 0xa8, 0x85, 0x87, 0x93, 0x74, 0x5e, 0x58, 0x4c, 0xe7, 0x44, 0x86,
	0xbc, 0xf8, 0xa9, 0x2b, 0x8f, 0x56, 0xa0, 0x31, 0x2d, 0x27, 0xf8, 0x07, 0x04, 0x42, 0xd6, 0x90,
	0xc0, 0x97, 0x66, 0x3d, 0x2c, 0x18, 0x58, 0xad, 0xcb, 0xf9, 0x0f, 0xfb, 0xae, 0x74, 0xf5, 0xd3,
	0xfd, 0x83, 0x22, 0xfa, 0x62, 0xff, 0xa0, 0x58, 0x0a, 0x64, 0x22, 0xfb, 0xf2, 0xe3, 0xfd, 0x83,
	0xe2, 0xb9, 0x4e, 0x20, 0x2a, 0x81, 0x69, 0xa8, 0x62, 0x87, 0x88, 0x4a, 0xc4, 0xa2, 0x2a, 0x76,
	0x7c, 0x51, 0x09, 0x85, 0x4c, 0xc5, 0x8f, 0x10, 0x9c, 0x49, 0x95, 0x6a, 0xdc, 0x9e, 0xf5, 0x9f,
	0xa5, 0xff, 0xad, 0x57, 0x72, 0x9e, 0xf4, 0x5d, 0x69, 0x95, 0xc1, 0x2c, 0x30, 0x98, 0x85, 0x80,
	0x43, 0xac, 0x4c, 0x20, 0xe2, 0xaf, 0x10, 0x34, 0x33, 0x44, 0x1a, 0xbf, 0x3a, 0xeb, 0x21, 0x5b,
	0xfd, 0x5b, 0x97, 0x72, 0x9f, 0x9d, 0xe0, 0x29, 0xa6, 0xe2, 0xf9, 0x1d, 0x81, 0xd0, 0xcf, 0x50,
	0x28, 0x9c, 0x4f, 0x86, 0x5a, 0xeb, 0xdd, 0xf8, 0x9b, 0xa5, 0xdb, 0xbf, 0xd1, 0x0b, 0xbf, 0x59,
	0xae, 0x59, 0x2e, 0xdd, 0x1b, 0xdc, 0xea, 0x49, 0x16, 0x73, 0xbe, 0xc4, 0x9c, 0x37, 0x4c, 0x79,
	0x24, 0xdb, 0xb2, 0x23, 0xfb, 0xb2, 0x21, 0x7b, 0x1c, 0xc8, 0xad, 0x8e, 0xa9, 0xf0, 0x64, 0x8d,
	0x94, 0x68, 0x6e, 0xa8, 0x62, 0xc7, 0x56, 0x98, 0x90, 0xab, 0x62, 0xc7, 0x51, 0xd8, 0x4c, 0x60,
	0xa9, 0x54, 0xc2, 0x31, 0xa0, 0x8a, 0x1d, 0x43, 0x49, 0xd1, 0x7c, 0x55, 0xec, 0x78, 0x0a, 0x9b,
	0x09, 0x2a, 0x7e, 0x00, 0xc2, 0xdb, 0x64, 0x9c, 0x1e, 0xcf, 0xf9, 0xc5, 0xf1, 0xf0, 0x71, 0x70,
	0x48, 0x34, 0x4d, 0x16, 0xcd, 0x71, 0x4e, 0xa5, 0xc9, 0x23, 0x28, 0x85, 0x11, 0xe0, 0x4f, 0xf8,
	0xa7, 0xc3, 0x0b, 0xf8, 0xcd, 0x47, 0x76, 0x08, 0xa0, 0x94, 0x02, 0xe0, 0x4b, 0x94, 0x3d, 0x6b,
	0x7a, 0x7b, 0xd7, 0xc3, 0x31, 0x7c, 0x94, 0x48, 0xd6, 0x19, 0x92, 0x72, 0x02, 0x49, 0xad, 0x63,
	0xc6, 0x59, 0xc4, 0x8f, 0x11, 0xac, 0x2f, 0x1a, 0x7d, 0x78, 0x33, 0xa5, 0x9f, 0x16, 0x0d, 0xdd,
	0xd6, 0x6b, 0xcf, 0x77, 0xc1, 0x77, 0xa5, 0x93, 0x0c, 0x61, 0x85, 0x21, 0x3c, 0xc6, 0xf0, 0x1d,
	0xc3, 0x3f, 0x21, 0x58, 0xed, 0x67, 0x0d, 0x22, 0x9c, 0x73, 0xd2, 0x1c, 0x52, 0x21, 0x77, 0x98,
	0xd3, 0x2a, 0x73, 0x0a, 0xa6, 0x4c, 0x64, 0x2a, 0xef, 0xca, 0x0e, 0xa7, 0x47, 0x99, 0xd4, 0x3a,
	0x49, 0x88, 0x14, 0x9d, 0x2e, 0x67, 0x55, 0xec, 0xec, 0xc6, 0x16, 0xde, 0x01, 0x8e, 0x4b, 0x1d,
	0xdb, 0x57, 0xf1, 0x77, 0x08, 0xce, 0x2e, 0x9c, 0xa1, 0xf9, 0x72, 0xfb, 0xfa, 0xa1, 0x24, 0xce,
	0xce, 0xe5, 0x30, 0xcf, 0x30, 0x93, 0xe7, 0x49, 0x04, 0xf8, 0x09, 0x82, 0x0b, 0x0b, 0xdf, 0xd8,
	0x72, 0x3c, 0x26, 0x4b, 0xff, 0x2d, 0xbc, 0x5a, 0x16, 0xbc, 0x1f, 0x11, 0x34, 0xfb, 0xe9, 0x73,
	0x12, 0xe7, 0x1a, 0x85, 0x87, 0x64, 0xfb, 0x36, 0xf3, 0x7e, 0x82, 0x79, 0x3f, 0xc1, 0xb2, 0xed,
	0xc9, 0x54, 0x36, 0x64, 0x8b, 0xe3, 0xb8, 0x9a, 0x96, 0x6f, 0x4f, 0x09, 0x27, 0x2b, 0x4f, 0x3d,
	0xd5, 0x47, 0x5c, 0xd1, 0xd8, 0x17, 0x83, 0x2a, 0x76, 0x2c, 0xc5, 0xb4, 0x46, 0x2a, 0xfe, 0x16,
	0xc1, 0xda, 0x82, 0xd1, 0x9e, 0x8f, 0xc8, 0xc3, 0xbb, 0x6b, 0xfa, 0x63, 0x21, 0xa4, 0xb1, 0x9e,
	0x45, 0xe3, 0x47, 0xb0, 0x3a, 0xa7, 0xaa, 0x71, 0xdf, 0x1c, 0x95, 0xac, 0x36, 0x52, 0x54, 0x2d,
	0x80, 0xe6, 0x9c, 0xe3, 0x28, 0x7d, 0x47, 0xe5, 0xf6, 0x64, 0x8a, 0xdb, 0x5f, 0x10, 0xac, 0xf7,
	0x74, 0x9a, 0xad, 0x15, 0xcf, 0x5f, 0xa8, 0x79, 0x64, 0x63, 0xf9, 0xc8, 0x65, 0xe3, 0x67, 0x04,
	0xad, 0xb4, 0x40, 0x22, 0x0e, 0x9f, 0xb7, 0x4c, 0xf2, 0x74, 0xc3, 0xa9, 0x23, 0xed, 0x86, 0xcf,
	0x10, 0xff, 0x2b, 0xfb, 0x45, 0x4a, 0x2f, 0xa7, 0xae, 0x87, 0xd5, 0x80, 0xe7, 0xab, 0xa1, 0x55,
	0xfa, 0x7c, 0xff, 0xa0, 0xf8, 0xfd, 0xc7, 0xbd, 0xe5, 0x5f, 0x9f, 0x6e, 0xa0, 0x3f, 0x9e, 0x6e,
	0xa0, 0xbf, 0x9e, 0x6e, 0xa0, 0x6f, 0xfe, 0xde, 0x38, 0x76, 0xb7, 0xc4, 0xff, 0x2b, 0xf3, 0xc6,
	0x3f, 0x01, 0x00, 0x00, 0xff, 0xff, 0x1d, 0x01, 0xc1, 0xc0, 0xf7, 0x11, 0x00, 0x00,
}
